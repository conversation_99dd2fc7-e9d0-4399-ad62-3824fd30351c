[{"ArticleId": 105453465, "Title": "Adaptive event-triggered dynamic distributed control of switched positive systems with switching faults", "Abstract": "This paper designs the dynamic output-feedback controller of switched positive systems subject to switching faults using an improved adaptive event-triggering mechanism. An adaptive event-triggering condition is addressed in the form of 1-norm by virtue of the measurable outputs of distributed sensors and the corresponding error. An error-based closed-loop control system whose dynamic variable relies on a state observer is obtained. A multiple copositive <PERSON><PERSON><PERSON><PERSON> function is constructed to deal with the positivity and stability of the systems. The matrix decomposition and linear programming approaches are used to design and compute the controller and observer gains. An improved average dwell time scheme is proposed to handle the switching faults. The contributions of this paper lie in that: (i) An adaptive event-triggering mechanism is established for switched positive systems, (ii) A framework on the fault of switching signal is constructed, and (iii) A dynamic distributed controller is proposed for the considered systems. Finally, two illustrative examples are given to verify the effectiveness of the obtained results.", "Keywords": "Switched positive systems ; Adaptive event-triggering mechanism ; Dynamic distributed control ; Switching faults", "DOI": "10.1016/j.nahs.2022.101328", "PubYear": 2023, "Volume": "48", "Issue": "", "JournalId": 5866, "JournalTitle": "Nonlinear Analysis: Hybrid Systems", "ISSN": "1751-570X", "EISSN": "1878-7460", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, Hainan University, Haikou 570228, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Hangzhou Dianzi University, Hangzhou 310018, China"}, {"AuthorId": 3, "Name": "Xuanjin Deng", "Affiliation": "School of Automation, Hangzhou Dianzi University, Hangzhou 310018, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, Hainan University, Haikou 570228, China"}], "References": [{"Title": "Dynamic event-triggered mechanism for H∞ non-fragile state estimation of complex networks under randomly occurring sensor saturations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "304", "JournalTitle": "Information Sciences"}, {"Title": "Hybrid L∞×ℓ∞-performance analysis and control of linear time-varying impulsive and switched positive systems", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "", "Page": "100980", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}]}, {"ArticleId": 105453570, "Title": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> Gambar Mata Menggunakan Algoritma Convolutional Neural Network (CNN)", "Abstract": "<p>Mata merupakan organ penglihatan yang terletak di rongga orbital. Bentuknya bulat, sekitar 2,5cm. Ruang antara mata dan orbit diisi oleh jaringan gemuk, dinding tulang dan lemak orbit yang dapat melindungi mata dari terluka. Mata adalah salah satu panca indra yang dapat digunakan untuk membedakan jenis kelamin dari manusia. Untuk membantu mengklasifikasikan jenis kelamin manusia menggunakan data citra mata bisa dengan menggunakan pendekatan deep learning dengan menggunakan algoritma Convolutional Neural Network (CNN). Hasil yang diperoleh adalah berupa nilai precision, recall, F1-score dan Accuracy dengan nilai precision untuk femaleeyes 97% dan maleeyes 90%, Recall femaleeyes dengan nilai 91% dan maleeyes 96%, F1-score  dengan nilai femaleeyes 94% serta maleeyes 93% dan nilai accuracy yang di dapatkan pada jumlah data yang sudah di training sebesar 94%.</p>", "Keywords": "", "DOI": "10.37859/coscitech.v3i3.4360", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 83284, "JournalTitle": "Jurnal CoSciTech (Computer Science and Information Technology)", "ISSN": "2723-567X", "EISSN": "2723-5661", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}], "References": []}, {"ArticleId": 105453580, "Title": "A local search scheme in the natural element method for the analysis of elastic-plastic problems", "Abstract": "Natural element method (NEM) is a meshless method based on the <PERSON><PERSON><PERSON><PERSON> diagram and Delaunay triangulation. It has the advantages of the meshless method, and simplifies the imposition of essential boundary conditions. NEM has great potential to solve the problems with large deformation. However, the high computational cost for searching natural neighbors is one of the main problems in NEM. In this paper a local algorithm based on K-Nearest Neighbor is presented for searching natural neighbors. Compared with the global sweep algorithm and other local search algorithms, the proposed algorithm introduces K to reduce the search scope. The value of K can be adjusted adaptively with the distribution characteristics of local neighbors of the calculation point and so the search can reach the global optimal value. The search scope changes with the flow of nodes, avoiding the problem that the natural neighbors exceed the search scope and reducing the calculation error. The proposed approach realizes the meshless characteristic in the natural neighbor searching process. The approach was used to solve the elastic deformation of a cantilever beam and a porous structure and the large plastic deformation in metal forming process. The results show that the proposed approach has great significance to solve problems in the elastic-plastic large deformation of metals and it is efficient.", "Keywords": "", "DOI": "10.1016/j.advengsoft.2022.103403", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Fujian Key Laboratory of Special Energy Manufacturing, Huaqiao University, Xiamen 361021, China;Xiamen Key Laboratory of Digital Vision Measurement, Huaqiao University, Xiamen 361021, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Special Energy Manufacturing, Huaqiao University, Xiamen 361021, China;Xiamen Key Laboratory of Digital Vision Measurement, Huaqiao University, Xiamen 361021, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sichuan Jiuzhou Electric Group Co Ltd, Mianyang 621000, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Fujian Key Laboratory of Special Energy Manufacturing, Huaqiao University, Xiamen 361021, China;Xiamen Key Laboratory of Digital Vision Measurement, Huaqiao University, Xiamen 361021, China"}, {"AuthorId": 5, "Name": "<PERSON>eifeng Li", "Affiliation": "<PERSON> of Engineering, University of Glasgow, Glasgow, G12 8QQ, UK"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Fujian Key Laboratory of Special Energy Manufacturing, Huaqiao University, Xiamen 361021, China;Xiamen Key Laboratory of Digital Vision Measurement, Huaqiao University, Xiamen 361021, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Special Energy Manufacturing, Huaqiao University, Xiamen 361021, China;Xiamen Key Laboratory of Digital Vision Measurement, Huaqiao University, Xiamen 361021, China"}], "References": []}, {"ArticleId": 105453785, "Title": "Dataset on UAV RGB videos acquired over a vineyard including bunch labels for object detection and tracking", "Abstract": "Counting the number of grape bunches at an early stage of development offers relevant information to the winegrower about the potential yield to be harvested. However, manual counting on the fields is laborious and time-consuming. Remote sensing, and more precisely unmanned aerial vehicles mounted with RGB or multispectral cameras, facilitate this task rapidly and accurately. This dataset contains 40 RGB videos from a 1.06-ha vineyard located in northern Spain. Moreover, the dataset includes mask labels of visible grape bunches. The videos were acquired throughout four UAV flights with an RGB camera tilted at 60 degrees. Each flight recorded one side of a row of the vineyard. The grape berries were between pea-size (BBCH75) and bunch closure (BBCH79) stage, which is two months before harvesting. No operations other than those usual in a commercial vineyard, such as pruning, cane tying, fertilization, and pest treatment, have been carried out, hence, the dataset presents leaf occlusion. The dataset was gathered and labelled to train object detection and tracking algorithms for grape bunch counting. Furthermore, it eases the work of winegrowers to check the sanitary status of the vineyard.", "Keywords": "Viticulture ; Precision agriculture ; Object detection ; Object tracking ; Remote sensing ; UAV", "DOI": "10.1016/j.dib.2022.108848", "PubYear": 2023, "Volume": "46", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Mar Ariza-<PERSON><PERSON>ís", "Affiliation": "Information Technology Group, Wageningen University & Research, 6708 PB Wageningen, the Netherlands;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Information Technology Group, Wageningen University & Research, 6708 PB Wageningen, the Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Information Technology Group, Wageningen University & Research, 6708 PB Wageningen, the Netherlands"}], "References": []}, {"ArticleId": 105453792, "Title": "Ammonia-based green corridors for sustainable maritime transportation", "Abstract": "Decarbonizing maritime transportation will require huge investments in new technologies and infrastructure for the production, distribution, and utilization of alternative marine fuels. In this context, the concept of green shipping corridors has been proposed, where a green corridor refers to a major shipping route along which low- and zero-carbon maritime transportation solutions are provided. In this work, we conduct a global analysis of green shipping corridors by designing a network of alternative fuel production sites, transportation links, and bunkering ports that can support a large fraction of the global marine fuel demand. We choose green ammonia to be the alternative fuel as it has received significant attention as a potential carbon- and sulfur-free marine fuel that can be produced entirely from renewable resources. Our study identifies the most suitable locations for producing and bunkering green ammonia, examines the trade-off between ammonia production and transportation costs, and highlights the extent to which local reduction in production cost can lead to a competitive advantage in a future green ammonia market. We also demonstrate the value of our global network optimization, which considers many shipping routes simultaneously, in capturing potential synergies and trade-offs across different routes and regions. This work provides insights that can help inform decisions in establishing future green shipping corridors around the world.", "Keywords": "Green ammonia ; Alternative marine fuel ; Maritime transportation ; Green corridors ; Supply chain design", "DOI": "10.1016/j.dche.2022.100082", "PubYear": 2023, "Volume": "6", "Issue": "", "JournalId": 90723, "JournalTitle": "Digital Chemical Engineering", "ISSN": "2772-5081", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, University of Minnesota, Minneapolis, MN 55455, USA;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, University of Minnesota, Minneapolis, MN 55455, USA;Corresponding authors"}], "References": [{"Title": "Using hydrogen and ammonia for renewable energy storage: A geographically comprehensive techno-economic study", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "106785", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Small-scale LNG supply chain optimization for LNG bunkering in Turkey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "162", "Issue": "", "Page": "107789", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 105453799, "Title": "Assessing the qualiti indicators of automated control system facilities", "Abstract": "<p>The aim of the study is to show the necessity to solve the problem of assessing the quality indicators of the automated systems facilities; to demonstrate that the work quality problem of system facilities is closely connected with choosing rational technologies for processing information in the system. The article is devoted to calculating the main quality indicators of operating the automated system facilities, which include the time to complete the system tasks, the output information accuracy and the system feasibility. Research methods are statistical analysis of the operating quality of the automated system facilities using the methods of probability theory and parallel data processing. The novelty of the work is using methods of parallel data processing in calculating the main quality indicators of operating the automated control system facilities. The study results in developing a methodology for assessing the quality of the automated system facilities, namely the time to complete the system tasks, the output information accuracy and the system feasibility. The article concludes that the methods given in the article for calculating the main quality indicators of operating the automated system facilities make it possible to increase the efficiency of automated control systems by boosting the reliability and accuracy of information processing</p>", "Keywords": "", "DOI": "10.30987/2658-6436-2022-4-12-17", "PubYear": 2022, "Volume": "2022", "Issue": "4", "JournalId": 60984, "JournalTitle": "Automation and modeling in design and management of", "ISSN": "2658-3488", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Admiral <PERSON><PERSON><PERSON> State University of Maritime and Inland Shipping"}], "References": []}, {"ArticleId": 105453809, "Title": "<PERSON><PERSON><PERSON>anan Website Terhadap Pengguna Akhir Menggunakan Webqual 4.0", "Abstract": "<p>Peran Teknologi dalam membantu menyebarluaskan informasi mempunyai peranan penting, Salah satunya melalui website. Website Kantor Pertanahan Kota Pekanbaru merupakan website yang memberikan pelayanan seputar informasi tentang surat tanah. Layanan dan fitur yang tersedia meliputi halaman beranda, informasi tentang Kantor Pertanahan Kota Pekanbaru, publikasi, layanan, PPID, aplikasi loketku, antrian online, dan aplikasi laporan PPAT. Selama penerapannya terkadang website mengalami beberapa kendala seperti gagal diakses, informasi terbaru tidak muncul, dan terjadinya error saat mengakses menu yang ada. Penelitian ini bertujuan yaitu untuk mengukur sejauh mana kualitas website berpengaruh terhadap pengguna dengan metode Webqual 4.0. Pengambilan data dilakukan dengan kuesioner dan obesrvasi, selanjutnya akan dilakukan uji validitas dan reliabilitas data, serta uji Hipotesis menggunakan SmartPLS. Hasil penelitian menunjukan bahwa website yang digunakan sudah dikatakan sangat bagus dengan nilai keterkaitan R-Square variabel User Satisfaction sebesar 87%.\r  \r  </p>", "Keywords": "", "DOI": "10.37859/coscitech.v3i3.4403", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 83284, "JournalTitle": "Jurnal CoSciTech (Computer Science and Information Technology)", "ISSN": "2723-567X", "EISSN": "2723-5661", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Islam Negeri Sultan <PERSON><PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Islam Negeri Sultan <PERSON><PERSON><PERSON>"}, {"AuthorId": 3, "Name": "Mega Kumalasari", "Affiliation": "Universitas Islam Negeri Sultan <PERSON><PERSON><PERSON>"}], "References": []}, {"ArticleId": 105453922, "Title": "Capture and control content discrepancies via normalised flow transfer", "Abstract": "Unsupervised Style Transfer (UST) has recently been a hot topic in Computer Vision, and this type of work has been exemplified by CycleGAN. Although the existing UST methods have proven to be useful, in many circumstances, we want to be able to manage not just the transformation of a single piece of instance, but also the morphological features of the two data sets’ underlying distributions. To this end, we propose a novel framework called Normalised Flow Transfer (NFT), where a reversible probability transform using the normalised flow method is developed to transfer the data in the first domain to the second, so as to exhibit their probabilities under both domains in the mapping process. A particularly interesting application under this framework is that when the data sets in two domains contain numerous clusters based on their finite class labels, we can control the distribution pattern across the two domains and apply any constraints on the underlying distributions of the same class. The experimental results show that not only can we devise many new complex style transfer functions, but also our framework has better image generation capabilities in terms of evaluation metrics, including mean square error, inception score and Frechet inception distance.", "Keywords": "", "DOI": "10.1016/j.patrec.2022.12.017", "PubYear": 2023, "Volume": "165", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Technology Sydney, 15 Broadway, Ultimo NSW 2007, Australia;Shanghai University, No. 99 Shangda Road,Baoshan District, Shanghai, China;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Technology Sydney, 15 Broadway, Ultimo NSW 2007, Australia;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Shanghai University, No. 99 Shangda Road,Baoshan District, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Technology Sydney, 15 Broadway, Ultimo NSW 2007, Australia"}], "References": [{"Title": "Explaining digital humanities by aligning images and textual descriptions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "166", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "MISS GAN: A Multi-IlluStrator style generative adversarial network for image to illustration translation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "140", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Deep ladder reconstruction-classification network for unsupervised domain adaptation", "Authors": "Wan<PERSON> Deng; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "152", "Issue": "", "Page": "398", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Adaptive Modulation and Rectangular Convolutional Network for Stereo Image Super-Resolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "161", "Issue": "", "Page": "122", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 105453986, "Title": "GMR biosensing with magnetic nanowires as labels for the detection of osteosarcoma cells", "Abstract": "Magnetic nanowires (MNWs) were explored as potential magnetic tags for cell detection with giant magnetoresistance (GMR) biosensors based on a handheld system. Due to size, shape anisotropy and higher moment materials, the signal detected from a single MNW was 2500 times larger than that from a single magnetic iron oxide nanobead, which is important for ultra-low concentration cell detection. A model was used to determine how the MNW orientation with respect to the GMR sensor impacts detection performance, and the results aligned well with the experimental results. As a proof of concept OSCA-8 cells tagged with Ni MNWs were also detected using the same handheld system. The limit of detection (LOD) in aqueous solution appeared to be 133 cells, and single-cell detection can be realized if the cell is in direct contact with the sensor surface. Since MNWs are already employed in magnetic separation of cells, directly using MNWs as tags in cell detection eliminates the need of additional functionalization with other labels. This largely simplifies the detection process and reduces the risk of contamination during sample preparation.", "Keywords": "Giant magnetoresistance (GMR) ; Biosensors ; Magnetic nanowires ; Cell detection ; Angular dependence", "DOI": "10.1016/j.sna.2022.114115", "PubYear": 2023, "Volume": "350", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "Diqing Su", "Affiliation": "Department of Chemical Engineering and Materials Science, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Electrical Engineering Department, King Abdullah University of Science and Technology, Thuwal 23955, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, King Abdullah University of Science and Technology, Thuwal 23955, Saudi Arabia"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Animal Cancer Care and Research Program, University of Minnesota, St. Paul, MN 55108, USA;Department of Veterinary Clinical Sciences, College of Veterinary Medicine, University of Minnesota, St. Paul, MN 55108, USA;Masonic Cancer Center, University of Minnesota, Minneapolis, MN 55455, USA;Center for Immunology, University of Minnesota, Minneapolis, MN 55455, USA;Stem Cell Institute, University of Minnesota, Minneapolis, MN 55455, USA;Institute for Engineering in Medicine, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Minnesota, Minneapolis, MN 55455, USA;Institute for Engineering in Medicine, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, University of Minnesota, Minneapolis, MN 55455, USA;Department of Electrical and Computer Engineering, University of Minnesota, Minneapolis, MN 55455, USA;Masonic Cancer Center, University of Minnesota, Minneapolis, MN 55455, USA;Institute for Engineering in Medicine, University of Minnesota, Minneapolis, MN 55455, USA;Corresponding authors at: Department of Chemical Engineering and Materials Science, University of Minnesota, Minneapolis, MN 55455, USA"}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, University of Minnesota, Minneapolis, MN 55455, USA;Department of Electrical and Computer Engineering, University of Minnesota, Minneapolis, MN 55455, USA;Masonic Cancer Center, University of Minnesota, Minneapolis, MN 55455, USA;Institute for Engineering in Medicine, University of Minnesota, Minneapolis, MN 55455, USA;Corresponding authors at: Department of Chemical Engineering and Materials Science, University of Minnesota, Minneapolis, MN 55455, USA"}], "References": []}, {"ArticleId": 105454137, "Title": "Data analytics for crop management: a big data view", "Abstract": "Recent advances in Information and Communication Technologies have a significant impact on all sectors of the economy worldwide. Digital Agriculture appeared as a consequence of the democratisation of digital devices and advances in artificial intelligence and data science. Digital agriculture created new processes for making farming more productive and efficient while respecting the environment. Recent and sophisticated digital devices and data science allowed the collection and analysis of vast amounts of agricultural datasets to help farmers, agronomists, and professionals understand better farming tasks and make better decisions. In this paper, we present a systematic review of the application of data mining techniques to digital agriculture. We introduce the crop yield management process and its components while limiting this study to crop yield and monitoring. After identifying the main categories of data mining techniques for crop yield monitoring, we discuss a panoply of existing works on the use of data analytics. This is followed by a general analysis and discussion on the impact of big data on agriculture.", "Keywords": "Digital agriculture;Data analytics;Crop management;Big data;Data mining;Machine learning", "DOI": "10.1186/s40537-022-00668-2", "PubYear": 2022, "Volume": "9", "Issue": "1", "JournalId": 2151, "JournalTitle": "Journal of Big Data", "ISSN": "", "EISSN": "2196-1115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Technology, Ferhat Abbas University, Sétif, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Technology, Ferhat Abbas University, Sétif, Algeria; School of Computer Science, University College Dublin, Dublin, Ireland"}], "References": [{"Title": "Soybean yield prediction from UAV using multimodal data fusion and deep learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111599", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Automated disease classification in (Selected) agricultural crops using transfer learning", "Authors": "<PERSON><PERSON><PERSON> ; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "2", "Page": "260", "JournalTitle": "Automatika"}]}, {"ArticleId": 105454360, "Title": "Response of Singly Reinforced Square Concrete Slab with Varying Standard Concrete Strength and Slab Thickness under Concentric Impact Loading: A Numerical Study", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJASS.2023.10053043", "PubYear": 2023, "Volume": "1", "Issue": "1", "JournalId": 18638, "JournalTitle": "International Journal of Applied Systemic Studies", "ISSN": "1751-0589", "EISSN": "1751-0597", "Authors": [{"AuthorId": 1, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105454440, "Title": "Intelligent seismic stratigraphic modeling using temporal convolutional network", "Abstract": "Deep learning (DL) has stimulated the intelligent processing and interpretation of seismic big data for oil and gas exploration in the past decade. In the applications to seismic stratigraphic modeling, most DL algorithms utilize a combination of ‘convolution + pooling’ to extract spatial features from seismic volume or images, which, however, may lead to a loss of the fine-grained information endowed in seismic time-series records inevitably. Here, we proposed a novel scheme for intelligent seismic stratigraphic interpretation based on temporal convolution network (TCN). The purpose of using TCN is to implement a trace-by-trace segmentation of lithologies by extracting spatio-temporal correlation of seismic record from a small amount of well log labels or manually interpreted geological profiles, thereby building a 3-D subsurface stratigraphic model. The proposed TCN model for seismic stratigraphic interpretation is trained, validated, and tested using the Netherlands F3 seismic dataset. The results suggest that, with a limited number of either manual interpretation profiles or well logs information, the proposed TCN algorithm can produce a highly accurate and laterally continuous 3-D stratigraphic model. Specifically, the mean pixel accuracy of prediction results of the above two schemes (compared to the real model) has achieved 98.23% and 96.09%, respectively. In addition, comparing TCN to the widely used encoder-decoder convolutional neural networks for seismic strata interpretation shows the outperformance of TCN scheme in terms of training time, model size, and prediction accuracy, while also revealing that the proposed TCN model can well identify thin strata in complex seismic data.", "Keywords": "Seismic data interpretation ; Deep learning ; Temporal convolution network ; Stratigraphic identification", "DOI": "10.1016/j.cageo.2022.105294", "PubYear": 2023, "Volume": "171", "Issue": "", "JournalId": 4833, "JournalTitle": "Computers & Geosciences", "ISSN": "0098-3004", "EISSN": "1873-7803", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Geological Processes and Mineral Resource, China University of Geosciences, Wuhan, 430074, China;Faculty of Earth Resources, China University of Geosciences, Wuhan, 430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Geological Processes and Mineral Resource, China University of Geosciences, Wuhan, 430074, China;Corresponding author"}], "References": [{"Title": "Seismic fault detection using convolutional neural networks with focal loss", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "158", "Issue": "", "Page": "104968", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": *********, "Title": "Object detection based on cortex hierarchical activation in border sensitive mechanism and classification-GIou joint representation", "Abstract": "By imitating the brain neurons for object perception, the deep networks enable a comprehensive feature characterization in the task of object detection. Considering such a perceptual ability is usually bounded in a box area for feature extraction, the balance of dimension reduction and feature information retaining has been taken into account in more recent studies, especially for the information preservation in the border areas. Motivated by the mechanism of neuron cortex activation, in this work, a novel function based on cortex hierarchical activation is proposed to achieve more effective border sensitive mechanism by joint pooling in backbone networks. In order to avoid the parameter solidification, this strategy is also capable to benefit the feature extraction on the border without unnecessary model re-training. Furthermore, by replacing the square kernel with a designed band shape kernel, more adequate feature description can be obtained on the border via the combination of the strip hierarchical pooling and strip max pooling. With an extension of the proposed activation function on classification-GIoU joint representation, the overall detection accuracy has been further improved. Experimental evaluations on the COCO benchmark datasets have shown that the proposed work has a superior performance in comparison to other state-of-the-art detection approaches.", "Keywords": "", "DOI": "10.1016/j.patcog.2022.109278", "PubYear": 2023, "Volume": "137", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, Shaanxi, PR China;Ningbo Institute of Northwestern Polytechnical University, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, Shaanxi, PR China;Ningbo Institute of Northwestern Polytechnical University, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Computer Sciences, Nanchang University, Nanchang, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, Shaanxi, PR China;Ningbo Institute of Northwestern Polytechnical University, PR China"}, {"AuthorId": 5, "Name": "<PERSON> You", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, Shaanxi, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an, Shaanxi, PR China"}], "References": [{"Title": "Multi-model ensemble with rich spatial information for object detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "99", "Issue": "", "Page": "107098", "JournalTitle": "Pattern Recognition"}, {"Title": "Gated CNN: Integrating multi-scale feature layers for object detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107131", "JournalTitle": "Pattern Recognition"}, {"Title": "Scale-balanced loss for object detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "107997", "JournalTitle": "Pattern Recognition"}, {"Title": "Sparse attention block: Aggregating contextual information for object detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "108418", "JournalTitle": "Pattern Recognition"}, {"Title": "YOLO-Anti: YOLO-based counterattack model for unseen congested object detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108814", "JournalTitle": "Pattern Recognition"}, {"Title": "DSLA: Dynamic smooth label assignment for efficient anchor-free object detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108868", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 105454531, "Title": "Trust and online purchase intention: a systematic literature review through meta-analysis", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJEB.2022.10053045", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 8392, "JournalTitle": "International Journal of Electronic Business", "ISSN": "1470-6067", "EISSN": "1741-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105454546, "Title": "The art of augmented memory: Remembering with augmented reality application in the case of the Temple of Artemis", "Abstract": "<p>This article focuses on the effects of different representation modes of architectural heritage in augmented reality (AR) applications on remembering. Deterioration of the tangible evidence of architectural heritage compromises not only its visibility in the heritage site, but also its presence in memory. Converging survived features and digitally produced representations of the heritage, AR applications in mobile devices provide the memory of the site with endurance, however what is remembered immensely depends on how the heritage is digitally represented on screen. Conceived as a case study for the method of analysis derived from classical memorizing technique of the art of memory, the ‘[AR]temis’ project, reported in this article, aimed to get insights into the effects of the representational qualities of augmented architectural heritage on remembering and also into future AR projects developed for architectural heritage sites with its original method of analysis to inform design decisions. The research project involved the development of the method of art of augmented memory, the AR application, as well as questionnaires and interviews with the respondents’ on-site tests of the application. The results of this analysis show that the decisions regarding the digital representation of architectural heritage in AR applications entail not only the visual qualities of the heritage per se, but also how the actual site of memory is visualized on screen.</p>", "Keywords": "", "DOI": "10.1093/llc/fqac084", "PubYear": 2023, "Volume": "38", "Issue": "2", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "Ahenk Yılmaz", "Affiliation": "Department of Architecture, Yaşar University , Bornova, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Architecture, Yaşar University , Bornova, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management Information Systems, Yaşar University , Bornova, Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Architecture, Yaşar University , Bornova, Turkey"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Architecture, Yaşar University , Bornova, Turkey"}], "References": []}, {"ArticleId": 105454584, "Title": "Extensions of two constructions of Ahmad", "Abstract": "<p>In her 1990 thesis, <PERSON> showed that there is a so-called “Ahmad pair”, i.e., there are incomparable Σ 2 0 -enumeration degrees  a 0 and  a 1 such that every enumeration degree x < a 0 is ⩽ a 1 . At the same time, she also showed that there is no “symmetric Ahmad pair”, i.e., there are no incomparable Σ 2 0 -enumeration degrees  a 0 and  a 1 such that every enumeration degree x 0 < a 0 is ⩽ a 1 and such that every enumeration degree x 1 < a 1 is ⩽ a 0 . In this paper, we first present a direct proof of <PERSON>’s second result. We then show that her first result cannot be extended to an “Ahmad triple”, i.e., there are no Σ 2 0 -enumeration degrees  a 0 , a 1 and  a 2 such that both ( a 0 , a 1 ) and ( a 1 , a 2 ) are an Ahmad pair. On the other hand, there is a “weak <PERSON> triple”, i.e., there are pairwise incomparable Σ 2 0 -enumeration degrees  a 0 , a 1 and  a 2 such that every enumeration degree x < a 0 is also ⩽ a 1 or ⩽ a 2 ; however neither ( a 0 , a 1 ) nor ( a 0 , a 2 ) is an Ahmad pair.</p>", "Keywords": "", "DOI": "10.3233/COM-210380", "PubYear": 2022, "Volume": "11", "Issue": "3-4", "JournalId": 33294, "JournalTitle": "Computability", "ISSN": "2211-3568", "EISSN": "2211-3576", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, University of Wisconsin, Madison, WI, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Wisconsin, Madison, WI, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Mathematical Sciences, School of Physical and Mathematical Sciences, College of Science, Nanyang Technological University, Singapore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Wisconsin, Madison, WI, USA"}], "References": []}, {"ArticleId": 105454628, "Title": "Forming synthetic data for training a computer vision system", "Abstract": "<p>The article presents a method for generating synthetic data for training a neural network (hereinafter referred to as a neural network) to recognize existing objects. This method is designed to simplify the process of compiling the initial data set and modifying it for further application in the computer vision. An aircraft engine gearbox printed using additive technologies is used as a sample object for recognition. Three-dimensional models are loaded into <PERSON><PERSON><PERSON> three-dimensional editor, where a screenshot collection of the part on different backgrounds is saved using a sub-programme (hereinafter referred to as script) in Python. The received data set is applied to train three neural networks on the Roboflow website, and the results obtained are analysed for the possibility of using this method further. The article shows in detail the process of creating screenshots and the result of recognizing a printed part using three neural networks</p>", "Keywords": "", "DOI": "10.30987/2658-6436-2022-4-18-28", "PubYear": 2022, "Volume": "2022", "Issue": "4", "JournalId": 60984, "JournalTitle": "Automation and modeling in design and management of", "ISSN": "2658-3488", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Moscow Aviation  Institute"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Moscow Aviation Institute"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Moscow Aviation Institute"}], "References": []}, {"ArticleId": 105454643, "Title": "Representing and Expressing Measurement Data in Digital Systems", "Abstract": "<p>This article elaborates on a proposal for expressing measurement data in digital systems. Systems will deal with compact three-component expressions of data, comprising: an aspect, value and scale. The aspect and scale will be compactly encoded as unique digital identifiers, which can also serve as keys to access information held in central registers. A register-based way to provide legitimate conversion operations between different expressions of data is also envisaged.The proposal addresses known difficulties with digital representation of SI units and extends support to a range of measurement data that cannot be expressed in the SI. The notion of aspect is an extension of ‘quantity’ and ‘kind of quantity’, which are terms used in association with the SI. The idea of a scale combines a conventional measurement unit or reference with the notion of a particular mathematical structure for the expression of values, sometimes called a level of measurement.</p>", "Keywords": "Units of measurement; SI units; Levels of measurement; Quantity; Digital transformation; M-layer", "DOI": "10.1007/s42979-022-01534-x", "PubYear": 2023, "Volume": "4", "Issue": "2", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "Blair Hall", "Affiliation": "Measurement Standards Laboratory of New Zealand, Callaghan Innovation, Lower Hutt, New Zealand"}], "References": [{"Title": "Unit of measurement libraries, their popularity and suitability", "Authors": "<PERSON>; <PERSON>; <PERSON>‐<PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "4", "Page": "711", "JournalTitle": "Software: Practice and Experience"}]}, {"ArticleId": 105454668, "Title": "Implementasi Adasyn Untuk Imbalance Data Pada Dataset UNSW-NB15 Adasyn Implementation For Data Imbalance on UNSW-NB15 Dataset", "Abstract": "<p>Di masa Machine Learning pada saat ini, para peneliti bekerja keras untuk mengembangkan algoritma yang meningkatkan kemungkinan prediksi yang benar dengan akurasi yang lebih baik. Data tidak seimbang adalah ketika ukuran sampel dari satu kelas jauh lebih besar dari kelas lain, sampel minoritas dapat diperlakukan sebagai noise dalam proses klasifikasi, yang mengakibatkan hasil algoritma klasifikasi yang tidak memuaskan. Pada penelitian ini peneliti menggunakan dataset UNSW-NB15, setelah menggabungkan data train dan test, terdapat data tidak seimbangan pada kelas label, yaitu 164673 untuk label 1 dan 93000 untuk label 0. Tujuan penelitian ini untuk mengatasi masalah ketidakseimbangan data pada binary class dengan menggunakan teknik ADASYN dan mendeteksi serangan malware pada dataset UNSW-NB15 dengan menerapkan model algoritma Random Forest dan teknik ADASYN agar mendapatkan performa yang cukup baik. Berdasarkan hasil pengujian dengan teknik ADASYN untuk penanganan ketidakseimbangan data pada Binarry Class dan menggunakan model algoritma Random Forest, serta Hyperparameter Optuna untuk klasifikasi Anomali pada data UNSW-NB15 memperoleh akurasi yang cukup baik. Pada beberapa split data mendapatkan nilai  akurasi tertinggi pada split data 90/10 dengan hasil 99.86%. dari segi waktu tercepat didapat pada split data 60/40 yaitu 1,85 seconds.</p>", "Keywords": "", "DOI": "10.37859/coscitech.v3i3.4339", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 83284, "JournalTitle": "Jurnal CoSciTech (Computer Science and Information Technology)", "ISSN": "2723-567X", "EISSN": "2723-5661", "Authors": [{"AuthorId": 1, "Name": "Januar Al Amien", "Affiliation": ""}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}, {"AuthorId": 3, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}], "References": []}, {"ArticleId": 105454673, "Title": "Calculation optimal control system parameters for nonlinear dynamic processes of pulse voltage converters", "Abstract": "<p>A technique for choosing the optimal control system parameters for DC voltage converters is proposed, based on the joint application of the theory of linear automatic control systems and the theory of nonlinear dynamic systems. A small-signal structural dynamic model of an open loop of an automatic control system based on a direct step-up voltage converter with a control system for nonlinear dynamic processes based on a delayed feedback method is considered. Applying this model makes it possible to carry out a scientifically substantiated choice of the control system parameters for nonlinear dynamic processes using the methods of linear automatic control system theory. Bode diagrams of an open loop system are calculated without additional control of nonlinear dynamic processes and with additional control. The efficiency of using control systems for nonlinear dynamic processes is shown, which allows eliminating undesirable dynamic modes without additional parametric synthesis of the controller and, consequently, without reducing the system performance as a whole. In addition, applying these methods allows adjusting the controller parameters to increase the system performance without switching the system to undesirable dynamic modes. The results obtained can be applied at the stage of designing wide-class DC voltage pulse converters</p>", "Keywords": "", "DOI": "10.30987/2658-6436-2022-4-87-96", "PubYear": 2022, "Volume": "2022", "Issue": "4", "JournalId": 60984, "JournalTitle": "Automation and modeling in design and management of", "ISSN": "2658-3488", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bryanskiy gosudarstvennyy tehnicheskiy universitet"}], "References": []}, {"ArticleId": 105454785, "Title": "An Effective Approach Based on Temporal Centrality Measures for Improving Temporal Network Controllability", "Abstract": "", "Keywords": "", "DOI": "10.1080/01969722.2022.2159162", "PubYear": 2025, "Volume": "56", "Issue": "1", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Technical and Vocational University (TVU), Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Engineering, University of Isfahan, Isfahan, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Engineering, University of Isfahan, Isfahan, Iran"}], "References": [{"Title": "A workload clustering based resource provisioning mechanism using Biogeography based optimization technique in the cloud based systems", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "5", "Page": "3813", "JournalTitle": "Soft Computing"}, {"Title": "Resource provisioning in edge/fog computing: A Comprehensive and Systematic Review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "102362", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Impact of Centrality Measures on the Common Neighbors in Link Prediction for Multiplex Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "2", "Page": "138", "JournalTitle": "Big Data"}]}, {"ArticleId": 105454798, "Title": "State/Parameter Identification in Cancerous Models Using Unscented Kalman Filter", "Abstract": "", "Keywords": "", "DOI": "10.1080/01969722.2022.2157608", "PubYear": 2024, "Volume": "55", "Issue": "8", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shiraz University, Shiraz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shiraz University, Shiraz, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical and Computer Engineering, Shiraz University, Shiraz, Iran"}], "References": []}, {"ArticleId": 105454801, "Title": "Strategic argumentation dialogues for persuasion: Framework and experiments based on modelling the beliefs and concerns of the persuadee", "Abstract": "<p>Persuasion is an important and yet complex aspect of human intelligence. When undertaken through dialogue, the deployment of good arguments, and therefore counterarguments, clearly has a significant effect on the ability to be successful in persuasion. Two key dimensions for determining whether an argument is “good” in a particular dialogue are the degree to which the intended audience believes the argument and counterarguments, and the impact that the argument has on the concerns of the intended audience. In this paper, we present a framework for modelling persuadees in terms of their beliefs and concerns, and for harnessing these models in optimizing the choice of move in persuasion dialogues. Our approach is based on the Monte Carlo Tree Search which allows optimization in real-time. We provide empirical results of a study with human participants that compares an automated persuasion system based on this technology with a baseline system that does not take the beliefs and concerns into account in its strategy.</p>", "Keywords": "", "DOI": "10.3233/AAC-210005", "PubYear": 2023, "Volume": "14", "Issue": "2", "JournalId": 17289, "JournalTitle": "Argument & Computation", "ISSN": "1946-2166", "EISSN": "1946-2174", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University College London, UK;Scribe Labs, London, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University College London, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Informatics, Cardiff University, UK"}], "References": [{"Title": "Epistemic graphs for representing and reasoning with positive and negative influences of arguments", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "281", "Issue": "", "Page": "103236", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": *********, "Title": "High-throughput methotrexate sensing strategy based on a chemometrically assisted pH-switchable optical nanosensor", "Abstract": "In the present work, a methotrexate (MTX) optical nanosensor involving the generation of a double pH gradient in which silver nanoclusters (AgNCs) act as an optical probe is presented. The proposed strategy is based on the pH-dependent agglomeration and dispersion behavior of the AgNCs and the consequent reversible changes in the spectroscopic signal. MTX is an antineoplastic drug used in high doses (HD) for the treatment of different types of cancer. Interestingly, the presence of the AgNCs enhances the signal of MTX, a phenomenon that was exploited to generate a simple, fast, and sensitive method for MTX quantitation in the presence of uncalibrated components based on second-order data generation and chemometric data modeling. The successful calibration models were obtained by unfolded partial least squares coupled to residual bilinearization, which was able to deal with data with high spectral collinearity while achieving the second-order advantage. The proposed sensing strategy reached limits of detection and quantitation of 3.3 nmol L<sup>–1</sup> and 10.1 nmol L<sup>–1</sup>, respectively. These results support the applicability of the method for the detection and quantitation of MTX at clinically relevant concentrations, considering that plasma levels after the HD-MTX treatment should reach concentrations of 0.1 µmol L<sup>–1</sup> at 72 h to minimize side effects. Furthermore, the generation of second-order data and chemometric data modeling allowed the accurate prediction of MTX in the presence of uncalibrated compounds. The developed sensing platform is presented as an alternative of great interest, which allows the simple, fast, sensitive, and selective quantitation of MTX.", "Keywords": "Metal nanoclusters ; pH-switchable nanosensor ; Chemometrics ; Second-order advantage ; U-PLS/RBL ; Methotrexate", "DOI": "10.1016/j.snb.2022.133217", "PubYear": 2023, "Volume": "378", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratorio de Desarrollo Analítico y Quimiometría (LADAQ), Cátedra de Química Analítica I, Facultad de Bioquímica y Ciencias Biológicas, Universidad Nacional del Litoral, Ciudad Universitaria, 3000 Santa Fe, Argentina;Consejo Nacional de Investigaciones Científicas y Técnicas (CONICET), <PERSON><PERSON> 2290, 1425 Buenos Aires, Argentina;Corresponding authors at: Laboratorio de Desarrollo Analítico y Quimiometría (LADAQ), Cátedra de Química Analítica I, Facultad de Bioquímica y Ciencias Biológicas, Universidad Nacional del Litoral, Ciudad Universitaria, 3000 Santa Fe, Argentina"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratorio de Desarrollo Analítico y Quimiometría (LADAQ), Cátedra de Química Analítica I, Facultad de Bioquímica y Ciencias Biológicas, Universidad Nacional del Litoral, Ciudad Universitaria, 3000 Santa Fe, Argentina;Consejo Nacional de Investigaciones Científicas y Técnicas (CONICET), <PERSON><PERSON> 2290, 1425 Buenos Aires, Argentina"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratorio de Desarrollo Analítico y Quimiometría (LADAQ), Cátedra de Química Analítica I, Facultad de Bioquímica y Ciencias Biológicas, Universidad Nacional del Litoral, Ciudad Universitaria, 3000 Santa Fe, Argentina;Consejo Nacional de Investigaciones Científicas y Técnicas (CONICET), <PERSON><PERSON> 2290, 1425 Buenos Aires, Argentina"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Consejo Nacional de Investigaciones Científicas y Técnicas (CONICET), <PERSON><PERSON> 2290, 1425 Buenos Aires, Argentina;Instituto de Desarrollo Tecnológico para la Industria Química (INTEC), Universidad Nacional del Litoral, CONICET, Güemes 3450, (S3000GLN), Santa Fe, Argentina"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Laboratorio de Desarrollo Analítico y Quimiometría (LADAQ), Cátedra de Química Analítica I, Facultad de Bioquímica y Ciencias Biológicas, Universidad Nacional del Litoral, Ciudad Universitaria, 3000 Santa Fe, Argentina;Consejo Nacional de Investigaciones Científicas y Técnicas (CONICET), <PERSON><PERSON> 2290, 1425 Buenos Aires, Argentina;Corresponding authors at: Laboratorio de Desarrollo Analítico y Quimiometría (LADAQ), Cátedra de Química Analítica I, Facultad de Bioquímica y Ciencias Biológicas, Universidad Nacional del Litoral, Ciudad Universitaria, 3000 Santa Fe, Argentina"}], "References": []}, {"ArticleId": 105454929, "Title": "Extending properly n - REA sets1", "Abstract": "<p>In 1982, <PERSON><PERSON> and <PERSON><PERSON> proved that if A is an r.e. set which isn’t computable then there is a set of the form A ⊕ W e which isn’t of r.e. Turing degree. If we define a properly n + 1-REA set to be an n + 1-REA set which isn’t Turing equivalent to any n-REA set this result shows that every properly 1-REA set can be extended to a properly 2-REA set. This result was extended by <PERSON><PERSON> and <PERSON><PERSON> in 1994. They proved that every 2-REA set can be extended to a properly 3-REA set. This leads naturally to the hypothesis that every properly n-REA set can be extended to a properly n + 1-REA set. Here we show this hypothesis is false and that there is a properly 3-REA set which can’t be extended to a properly 4-REA set. Moreover we show this set A can be Δ 2 0 .</p>", "Keywords": "", "DOI": "10.3233/COM-210362", "PubYear": 2022, "Volume": "11", "Issue": "3-4", "JournalId": 33294, "JournalTitle": "Computability", "ISSN": "2211-3568", "EISSN": "2211-3576", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mathematics Department, University of Notre Dame du Lac, 255 Hurley Building, Notre Dame, IN 46556, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mathematics Department, Indiana University, Bloomington, Rawles Hall, 831 East 3rd St., Bloomington, IN 47405, USA"}], "References": []}, {"ArticleId": 105454976, "Title": "A scoping review of personalized user experiences on social media: The interplay between algorithms and human factors", "Abstract": "No social media user sees the same feed. These platforms are personalized to the individual with the aid of algorithms that filter and prioritize content based on users&#x27; demographic profiles and personal data. On the one hand, this personalization aids the user by making the service more relevant, for instance by curating information of interest. On the other hand, personalization introduces potential risks associated with privacy concerns, lack of autonomy and control, as well as limited diversity of information. This scoping review presents an overview of the current state of knowledge of social media personalization from different research domains, providing insight on social media users’ algorithmic awareness, their customization habits, their interactions with curated content, and the debate on how algorithms may create closed information outlets. It also provides a condensed overview of the different terminology used across domains, in the form of a glossary.", "Keywords": "Personalization ; Social media ; Algorithmic awareness ; Curation ; Filter bubbles ; Echo chambers", "DOI": "10.1016/j.chbr.2022.100253", "PubYear": 2023, "Volume": "9", "Issue": "", "JournalId": 75723, "JournalTitle": "Computers in Human Behavior Reports", "ISSN": "2451-9588", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, Department of Consumer and Sensory Sciences, Postal address: Postboks 210, 1431 Ås, Norway;Kristiania University College, School of Health Sciences, Postal address: PB 1190 Sentrum, 0107, Oslo, Norway;Corresponding author. <PERSON><PERSON><PERSON>, Department of Consumer and Sensory Sciences, Postboks 210, 1431 Ås, Norway"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Southampton, Winchester School of Art, Park Ave, Winchester SO23 8DL, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Tennfjord", "Affiliation": "Kristiania University College, School of Health Sciences, Postal address: PB 1190 Sentrum, 0107, Oslo, Norway"}], "References": [{"Title": "When does personalization work on social media? a posteriori segmentation of consumers", "Authors": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>-<PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "30", "Page": "36509", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Mapping HCI research methods for studying social media interaction: A systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "107131", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 105455005, "Title": "Cheap and secure metatransactions on the blockchain using hash-based authorisation and preferred batchers", "Abstract": "Smart contracts are self-executing programs running in the blockchain allowing for decentralised storage and execution without a middleman. On-chain execution is expensive, with miners charging fees for distributed execution according to a cost model defined in the protocol. In particular, transactions have a high fixed cost. We present MultiCall, a transaction-batching interpreter for Ethereum that reduces the cost of smart contract executions by gathering multiple users’ transactions into a batch. Our current implementation of MultiCall includes the following features: the ability to emulate Ethereum calls and create transactions, both from MultiCall itself and using an identity unique to the user; the ability to cheaply pay Ether to other MultiCall users; and the ability to authorise emulated transactions on behalf of multiple users in a single transaction using hash-based authorisation rather than more expensive signatures. This improves upon a previous version of MultiCall. Our experiments show that MultiCall provides a saving between 57% and 99% of the fixed transaction cost compared with the standard approach of sending Ethereum transactions directly. Besides, we also show how to prevent an economic attack exploiting the metatransaction feature, describe a generic protocol for hash-based authorisation of metatransactions, and analyse how to minimise its off-chain computational and storage cost.", "Keywords": "Ethereum ; Domain-specific language ; Interpreter ; Gas optimisation", "DOI": "10.1016/j.bcra.2022.100125", "PubYear": 2023, "Volume": "4", "Issue": "2", "JournalId": 83117, "JournalTitle": "Blockchain: Research and Applications", "ISSN": "2096-7209", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Science and Engineering, University of Gothenburg, 41296, Gothenburg, Sweden;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Science and Engineering, Chalmers University, 41296, Gothenburg, Sweden"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Science and Engineering, Chalmers University, 41296, Gothenburg, Sweden"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Computer Science and Engineering, University of Gothenburg, 41296, Gothenburg, Sweden"}], "References": []}, {"ArticleId": 105455032, "Title": "The gamblers of the future? Migration from loot boxes to gambling in a longitudinal study of young adults", "Abstract": "Cross-sectional studies have established a robust correlational link between loot box engagement and problem gambling, but the causal connections are unknown. This longitudinal study tested for ‘migration’ from loot box use to gambling initiation 6-months later. A sample of gamers (aged 18–26) was stratified into two subgroups at baseline: 415 non-gamblers and 221 gamblers. Self-reported engagement with video game microtransactions distinguished loot boxes and ‘direct purchase’ microtransactions (DPMs). Loot box expenditure and the Risky Loot Box Index (RLI) were tested as predictors of self-identified gambling initiation and spend at follow-up. At baseline, gamblers spent significantly more than non-gamblers on microtransactions. Among baseline non-gamblers, loot box expenditure and RLI predicted gambling initiation (logistic regressions) and later gambling spending (linear regressions). DPM expenditure did not predict gambling initiation or spend after correcting for multiple comparisons, underscoring the key role of randomized rewards. Exploratory analyses tested whether baseline gambling predicted loot box consumption (the ‘reverse pathway’): among loot box non-users, gambling-related cognitive distortions predicted subsequent loot box expenditure. These data provide empirical evidence for a migration from loot boxes to gambling. Preliminary evidence is also provided for a reverse pathway, of loot box initiation by gamblers. These findings support regulatory steps directed toward young gamers and those who gamble.", "Keywords": "Randomized reward ; Problem gambling ; Behavioural addiction ; Monetization ; Microtransaction ; Longitudinal", "DOI": "10.1016/j.chb.2022.107605", "PubYear": 2023, "Volume": "141", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for Gambling Research at UBC, Department of Psychology, University of British Columbia, Vancouver, B.C., Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Centre for Gambling Research at UBC, Department of Psychology, University of British Columbia, Vancouver, B.C., Canada;<PERSON><PERSON><PERSON><PERSON> Centre for Brain Health, University of British Columbia, Vancouver, B.C., Canada;Corresponding author. Department of Psychology, University of British Columbia, Vancouver, B.C., Canada"}], "References": []}, {"ArticleId": 105455041, "Title": "A dependable hybrid machine learning model for network intrusion detection", "Abstract": "Network intrusion detection systems (NIDSs) play an important role in computer network security. There are several detection mechanisms where anomaly-based automated detection outperforms others significantly. Amid the sophistication and growing number of attacks, dealing with large amounts of data is a recognized issue in the development of anomaly-based NIDS. However, do current models meet the needs of today’s networks in terms of required accuracy and dependability? In this research, we propose a new hybrid model that combines machine learning and deep learning to increase detection rates while securing dependability. Our proposed method ensures efficient pre-processing by combining SMOTE for data balancing and XGBoost for feature selection. We compared our developed method to various machine learning and deep learning algorithms in order to find a more efficient algorithm to implement in the pipeline. Furthermore, we chose the most effective model for network intrusion based on a set of benchmarked performance analysis criteria. Our method produces excellent results when tested on two datasets, KDDCUP’99 and CIC-MalMem-2022, with an accuracy of 99.99% and 100% for KDDCUP’99 and CIC-MalMem-2022, respectively, and no overfitting or Type-1 and Type-2 issues.", "Keywords": "Intrusion detection system ; Machine learning ; XGBoost ; Feature selection ; Feature importance ; Accuracy ; Dependability", "DOI": "10.1016/j.jisa.2022.103405", "PubYear": 2023, "Volume": "72", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jagannath University, Dhaka, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information Security Discipline, School of Computer Science, Queensland University of Technology (QUT), 2 George Street, Brisbane 4000, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jagannath University, Dhaka, Bangladesh;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jagannath University, Dhaka, Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jagannath University, Dhaka, Bangladesh"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Technology, Jahangirnagar University, Savar, Dhaka, Bangladesh"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Shaqra University, Shaqra 15526, Saudi Arabia"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Artificial Intelligence & Data Science, School of Health and Rehabilitation Sciences, Faculty of Health and Behavioural Sciences, The University of Queensland St Lucia, QLD 4072, Australia;Corresponding authors"}], "References": [{"Title": "Overview and comparative study of dimensionality reduction techniques for high dimensional data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "44", "JournalTitle": "Information Fusion"}, {"Title": "Detection of illicit accounts over the Ethereum blockchain", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113318", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Analysis of KDD-Cup’99, NSL-KDD and UNSW-NB15 Datasets using Deep Learning in IoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "1561", "JournalTitle": "Procedia Computer Science"}, {"Title": "Imbalance-XGBoost: leveraging weighted and focal losses for binary label-imbalanced classification with XGBoost", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "190", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Cybersecurity data science: an overview from machine learning perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "Challenge-based collaborative intrusion detection in software-defined networking: an evaluation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "2", "Page": "257", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Performance Analysis of Intrusion Detection Systems Using a Feature Selection Method on the UNSW-NB15 Dataset", "Authors": "Sydney M<PERSON>; Yanxia <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "An Ensemble Approach for Feature Selection and Classification in Intrusion Detection Using Extra-Tree Algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Information Security and Privacy"}, {"Title": "Network intrusion detection using oversampling technique and machine learning algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "e820", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Machine learning-based lung and colon cancer detection using deep feature extraction and ensemble learning", "Authors": "Md. <PERSON><PERSON><PERSON>; M<PERSON>. <PERSON>; M<PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "117695", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 105455076, "Title": "D-Score: An expert-based method for assessing the detectability of IoT-related cyber-attacks", "Abstract": "IoT devices are known to be vulnerable to various cyber-attacks, such as data exfiltration and the execution of flooding attacks as part of a DDoS attack. When it comes to detecting such attacks using network traffic analysis, it has been shown that some attack scenarios are not always equally easy to detect if they involve different IoT models. That is, when targeted at some IoT models, a given attack can be detected rather accurately, while when targeted at others the same attack may result in too many false alarms. In this research, we attempt to explain this variability of IoT attack detectability and devise a risk assessment method capable of addressing a key question: how easy is it for an anomaly-based network intrusion detection system to detect a given cyber-attack involving a specific IoT model? In the process of addressing this question we (a) investigate the predictability of IoT network traffic, (b) present a novel taxonomy for IoT attack detection which also encapsulates traffic predictability aspects, (c) propose an expert-based attack detectability estimation method which uses this taxonomy to derive a detectability score (termed ‘D-Score’ ) for a given combination of IoT model and attack scenario, and (d) empirically evaluate our method while comparing it with a data-driven method.", "Keywords": "Internet of things (IoT) security ; Attack detection ; Network traffic predictability ; Multi-Criteria decision making ; Analytical hierarchical process (AHP)", "DOI": "10.1016/j.cose.2022.103073", "PubYear": 2023, "Volume": "126", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software and Information Systems Engineering, Ben-Gurion University of the Negev, Beer-Sheva, Israel;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Software and Information Systems Engineering, Ben-Gurion University of the Negev, Beer-Sheva, Israel"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Software and Information Systems Engineering, Ben-Gurion University of the Negev, Beer-Sheva, Israel"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Software and Information Systems Engineering, Ben-Gurion University of the Negev, Beer-Sheva, Israel"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software and Information Systems Engineering, Ben-Gurion University of the Negev, Beer-Sheva, Israel"}], "References": [{"Title": "Enhancing collaborative intrusion detection via disagreement-based semi-supervised learning in IoT environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "102631", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "A novel approach for detecting vulnerable IoT devices connected behind a home NAT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "101968", "JournalTitle": "Computers & Security"}, {"Title": "IoT information theft prediction using ensemble feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "DeBot: A deep learning-based model for bot detection in industrial internet-of-things", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "108214", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 105455081, "Title": "WaterPurifier: A scalable system to prevent the DNS water torture attack in 5G-enabled SIoT network", "Abstract": "Social Internet of Things (SIoT) network contains a huge number of smart devices and is rich in social behavior relationships between these objects, especially in 5G-abled SIoT network. However, vulnerable devices and assailable applications or services in SIoT give attackers the opportunity to conduct DNS water torture (DNSWT) attacks for domain name system (DNS) infrastructures, which can cause the device to be unavailable, and moreover, previous approaches designed for normal Internet environment cannot reach the high level of demand on scalability and flexibility in SIoT network. In this work, we comprehensively analyze the characteristics of DNSWT attacks in 5G-enabled SIoT network and propose a collaborative and hierarchical defensive system called WaterPurifier to protect the SIoT network from DNSWT attacks. WaterPurifier consists of Gateway Layer, Server Layer and Cloud Layer. An asynchronously communication mechanism is implemented in the defensive system to ensure its flexibility. The components in Gateway Layer and Server Layer execute lightweight functions, like flow forwarding and attack monitoring, to guarantee a high level of scalability. Cloud Layer deploys an end-to-end domain encoding and a real-time training process, which can filter out DNSWT attack traffic effectively and efficiently. We implement a prototype of WaterPurifier in an in-lab environment and do an evaluation on the experiment result with performance indicators like packet loss rate. Both results and evaluations show high effectiveness and efficacy of the proposed system.", "Keywords": "", "DOI": "10.1016/j.comcom.2022.12.019", "PubYear": 2023, "Volume": "199", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "Lihua Yin", "Affiliation": "Cyberspace Institute of Advanced Technology, Guangzhou University, Guangzhou Higher Education Mega Center, 230 Wai Huan Xi Road, Guangzhou, 510006, Guangdong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cyberspace Institute of Advanced Technology, Guangzhou University, Guangzhou Higher Education Mega Center, 230 Wai Huan Xi Road, Guangzhou, 510006, Guangdong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Cyberspace Institute of Advanced Technology, Guangzhou University, Guangzhou Higher Education Mega Center, 230 Wai Huan Xi Road, Guangzhou, 510006, Guangdong, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Cyberspace Institute of Advanced Technology, Guangzhou University, Guangzhou Higher Education Mega Center, 230 Wai Huan Xi Road, Guangzhou, 510006, Guangdong, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON>ng<PERSON> Wang", "Affiliation": "China Industrial Control Systems Cyber Emergency Response Team, 35 Lugu Road, Shijingshan District, Beijing, 100040, Beijing, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Li", "Affiliation": "Cyberspace Institute of Advanced Technology, Guangzhou University, Guangzhou Higher Education Mega Center, 230 Wai Huan Xi Road, Guangzhou, 510006, Guangdong, China"}], "References": [{"Title": "IoT malicious traffic identification using wrapper-based feature selection mechanisms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "101863", "JournalTitle": "Computers & Security"}, {"Title": "IoT enabled HELMET to safeguard the health of mine workers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "1", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 105455117, "Title": "Mixed Reality Annotation of Robotic-Assisted Surgery videos with real- time tracking and stereo matching", "Abstract": "Robotic-Assisted Surgery (RAS) is beginning to unlock its potential. However, despite the latest advances in RAS, the steep learning curve of RAS devices remains a problem. A common teaching resource in surgery is the use of videos of previous procedures, which in RAS are almost always stereoscopic. It is important to be able to add virtual annotations onto these videos so that certain elements of the surgical process are tracked and highlighted during the teaching session. Including virtual annotations in stereoscopic videos turns them into Mixed Reality (MR) experiences, in which tissues, tools and procedures are better observed. However, an MR-based annotation of objects requires tracking and some kind of depth estimation. For this reason, this paper proposes a real-time hybrid tracking–matching method for performing virtual annotations on RAS videos. The proposed method is hybrid because it combines tracking and stereo matching, avoiding the need to calculate the real depth of the pixels. The method was tested with six different state-of-the-art trackers and assessed with videos of a sigmoidectomy of a sigma neoplasia, performed with a Da Vinci® X surgical system. Objective assessment metrics are proposed, presented and calculated for the different solutions. The results show that the method can successfully annotate RAS videos in real-time. Of all the trackers tested for the presented method, the CSRT (Channel and Spatial Reliability Tracking) tracker seems to be the most reliable and robust in terms of tracking capabilities. In addition, in the absence of an absolute ground truth, an assessment with a domain expert using a novel continuous-rating method with an Oculus Quest 2 Virtual Reality device was performed, showing that the depth perception of the virtual annotations is good, despite the fact that no absolute depth values are calculated.", "Keywords": "Mixed Reality ; Annotation ; Robotic-Assisted Surgery ; Tracking ; Stereo matching", "DOI": "10.1016/j.cag.2022.12.006", "PubYear": 2023, "Volume": "110", "Issue": "", "JournalId": 3909, "JournalTitle": "Computers & Graphics", "ISSN": "0097-8493", "EISSN": "1873-7684", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Robotics and Information Technology and Communication (IRTIC), University of Valencia, Valencia, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Robotics and Information Technology and Communication (IRTIC), University of Valencia, Valencia, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "General and Gastrointestinal Surgery. Fundación Investigación Consorcio Hospital General Universitario de Valencia (FIHGUV), Valencia, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "General and Gastrointestinal Surgery. Valencian Institute of Oncology (IVO), Valencia, Spain"}, {"AuthorId": 5, "Name": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Robotics and Information Technology and Communication (IRTIC), University of Valencia, Valencia, Spain;Corresponding author"}], "References": []}, {"ArticleId": 105455140, "Title": "Development of locomotive skid prevention devices based on object modeling of technical solutions", "Abstract": "<p>Techniques of prevention of locomotive wheelsets skidding were analyzed. The relevance of using object modeling techniques to automate the design of devices to prevent skidding is shown. The main task the solution of which is devoted to the article is the synthesis of the object model of technical solutions of such devices, the calculation and compilation of matrices of measures for inclusion, intersection and similarity. As a result of object modeling, the closest technical solution (prototype) was chosen, its characteristic features and disadvantages were identified. Novelty of results obtained in the present article consists in proposed device for prevention of skidding, application of which makes it possible to reduce power losses caused by eddy currents created by magnetic field in wheels and rail, to reduce energy consumption for creation of magnetic field, due to the fact that magnetic field is created only at possibility of wheels skidding and current values are maintained at minimum required level. The methodology and results of the scientific investigation can be used in the synthesis of new technical solutions aimed at improving the components of the crew of locomotives</p>", "Keywords": "", "DOI": "10.30987/2658-6436-2022-4-79-86", "PubYear": 2022, "Volume": "2022", "Issue": "4", "JournalId": 60984, "JournalTitle": "Automation and modeling in design and management of", "ISSN": "2658-3488", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Russian University of Transport"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Rossiyskiy  universitet   transporta"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Russian University of Transport"}], "References": [{"Title": "THE METHOD OF O<PERSON><PERSON>ECT MODELING IN THE DEVELOPMENT OF PATENTABLE DESIGNS OF TRACTION DRIVE MECHANISMS", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "3-4", "Page": "4", "JournalTitle": "Automation and modeling in design and management of"}]}, {"ArticleId": 105455142, "Title": "<PERSON><PERSON><PERSON> per<PERSON> Codeigniter dan <PERSON>i framework pada perancangan website rencana anggaran biaya", "Abstract": "<p>Per<PERSON>ungan biaya dalam membangun sebuah rumah menjadi hal yang perlu dipertimbangkan. Dalam perhitungan pembangunan rumah saat ini masih banyak yang melakukan dengan cara manual sehingga memungkinkan terjadi kesalahan perhitungan biaya dan material. Dengan adanya sistem komputerisasi memberikan dampak pada kehidupan masyarakat dalam melakukan perhitungan biaya pembangunan rumah ini. Sistem komputerisasi juga didukung oleh software dan hardware. Dalam membangun perangkat lunak menggunakan berbagai jenis bahasa pemrograman. Salah satunya adalah pemrograman PHP. Untuk mempercepat perancangan sebuah aplikasi web, diperlukan framework. Framework pada bahasa pemrograman PHP yang dikenal umum memiliki performa baik yaitu Codeigniter, Yii dan <PERSON>. Namun pada penelitian ini dilakukan untuk mengetahui perbandingan dua framework Codeigniter dan Yii. Penulis merancang aplikasi berbasis web untuk menyusun rencana anggaran biaya (RAB) dengan dua framework yaitu Codeigniter dan Yii Framework. Serta membandingkan kedua framework yang dirancang menggunakan diagram blok. Perbandingan yang tergolong dalam diagram blok adalah baris pada kode program, efisiensi kode, kecepatan web, struktural folder dan URL, arsitektur dan keamanan web. Perbandingan kedua framework memiliki perbedaan pada kecepatan web dan keamanan web.  Yii framework lebih unggul dalam kecepatan pada pengujian speed index sama hal dengan pengujian keamanan, Codeigniter framework memiliki keamanan yang lebih rendah dibandingkan Yii framework.</p>", "Keywords": "", "DOI": "10.37859/coscitech.v3i3.4338", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 83284, "JournalTitle": "Jurnal CoSciTech (Computer Science and Information Technology)", "ISSN": "2723-567X", "EISSN": "2723-5661", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Internasional Batam"}, {"AuthorId": 2, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Universitas Internasional Batam"}], "References": []}, {"ArticleId": 105455179, "Title": "A data-driven rule-based system for China’s traffic accident prediction by considering the improvement of safety efficiency", "Abstract": "Rapid traffic development brings convenience to social circulation, but the number of fatalities in traffic accidents has brought great pressure on traffic safety and social stability management. Therefore, traffic accidents prediction is being of great significance to alleviate the safety pressure of regional traffic management. Nevertheless, the existing studies has yet reached a consensus on the scientific and feasible modeling method for traffic safety management, the improvement of traffic safety efficiencies has also rarely discussed in traffic accidents prediction. This paper fills the gap by promoting a novel data-driven decision model for traffic accidents prediction, which is constructed by the extended belief rule-based system (EBRBS) with considering the improvement of traffic safety efficiencies. Hence, the new traffic accident prediction model consists of two components: 1) safety efficiencies evaluation modeling with considering meta-frontier and group-frontier to evaluate the current traffic safety management, which are also defined to improve safety efficiencies evaluation by the adjustment of inputs and outputs; 2) extended belief rule base (EBRB)-based modeling for traffic accidents prediction by considering the improvement of traffic safety efficiencies, where the effective efficiencies of traffic management inputs and outputs are utilized to predict the future number of traffic accidents. The effectiveness of the proposed model is verified by using traffic management data from 31 Chinese provinces during 2003–2020. Experimental results demonstrate that the model can offer powerful reference value in the traffic accidents prediction process, which help to achieve the relatively effective efficiencies of traffic safety.", "Keywords": "", "DOI": "10.1016/j.cie.2022.108924", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Cultural Tourism and Public Administration, Fujian Normal University, Fuzhou 350117, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Decision Sciences Institute, Fuzhou University, Fuzhou 350108, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Decision Sciences Institute, Fuzhou University, Fuzhou 350108, China"}, {"AuthorId": 4, "Name": "Haitian Lu", "Affiliation": "School of Accounting and Finance, The Hong Kong Polytechnic University, HKSAR, China"}], "References": [{"Title": "Deep spatio-temporal graph convolutional network for traffic accident prediction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "423", "Issue": "", "Page": "135", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": *********, "Title": "One-step preparation of flexible citric acid-doped polyaniline gas sensor for ppb-level ammonia detection at room temperature", "Abstract": "In this work, a citric acid-doped polyaniline/polyvinylidene fluoride (CPP) flexible ammonia sensor was prepared by in situ oxidative chemical polymerization on a flexible porous polyvinylidene fluoride membrane. The CPP sensor exhibited excellent performance at room temperature (217% response for 1 ppm NH<sub>3</sub>), low detection limit (LOD) reaches 3 ppb, and rapid response and recovery times (30 s /195 s for 1 ppm NH<sub>3</sub>, respectively). Furthermore, the sensor shows outstanding flexibility and excellent stability. After 10,000 bending cycles, the CPP sensor still produces over 99% stable response. The response reached 95% of its initial value after more than 60 days of storage. The CPP sensor exhibits excellent gas-sensing performance for ammonia gas, mainly attributed to the increase in active sites induced by citric acid doping and the synergistic effect of the porous substrate. The above results demonstrated that the CPP flexible film sensor is a high-performance candidate for enhanced ammonia gas sensing properties at room temperature.", "Keywords": "Flexible sensor ; Ammonia sensor ; Citric acid ; Polyvinylidene fluoride ; Polyaniline", "DOI": "10.1016/j.sna.2022.114120", "PubYear": 2023, "Volume": "350", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Lv", "Affiliation": "Zhejiang Provincial Engineering Research Center of Energy Optoelectronic Materials and Devices, Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, Zhejiang 315201, China;Center of Materials Science and Optoelectronics Engineering, University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Shen", "Affiliation": "Zhejiang Provincial Engineering Research Center of Energy Optoelectronic Materials and Devices, Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, Zhejiang 315201, China;Center of Materials Science and Optoelectronics Engineering, University of Chinese Academy of Sciences, Beijing 100049, China;Correspondence to: Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, Zhejiang 315201, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Healthcare Equipment, Zhejiang Pharmaceutical University, Ningbo, Zhejiang 315000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering and Computer Science, Ningbo University, Ningbo, Zhejiang 315211, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering and Computer Science, Ningbo University, Ningbo, Zhejiang 315211, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Key Laboratory of Micro and Nano Heat Fluid Flow Technology and Energy Application, Suzhou University of Science and Technology, Suzhou 215009, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Provincial Engineering Research Center of Energy Optoelectronic Materials and Devices, Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, Zhejiang 315201, China;Center of Materials Science and Optoelectronics Engineering, University of Chinese Academy of Sciences, Beijing 100049, China;Research Center for Sensing Materials and Devices Zhejiang Lab, Hangzhou, Zhejiang 311121, China;Correspondence to: Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, Zhejiang 315201, China"}], "References": [{"Title": "Gas sensing properties of amperometric NH3 sensors based on Sm2Zr2O7 solid electrolyte and SrM2O4 (M = Sm, La, Gd, Y) sensing electrodes", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "303", "Issue": "", "Page": "127220", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Room temperature ammonia sensing by CdS nanoparticle decorated polyaniline (PANI) nanorods", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "310", "Issue": "", "Page": "112071", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Room temperature ammonia sensing by CdS nanoparticle decorated polyaniline (PANI) nanorods", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "310", "Issue": "", "Page": "112071", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Room temperature ammonia gas sensor based on polyaniline/copper ferrite binary nanocomposites", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "322", "Issue": "", "Page": "128615", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "PSS-PANI/PVDF composite based flexible NH3 sensors with sub-ppm detection at room temperature", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "328", "Issue": "", "Page": "129085", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Development of water-based polyaniline sensor for hydrazine detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "317", "Issue": "", "Page": "112460", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "An enhanced flexible room temperature ammonia gas sensor based on GP-PANI/PVDF multi-hierarchical nanocomposite film", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>v", "PubYear": 2021, "Volume": "334", "Issue": "", "Page": "129630", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 105455277, "Title": "Optimizing the distributed generators integration in electrical distribution networks: efficient modified forensic-based investigation", "Abstract": "<p>As a result of various loads, including critical installations (industries, nuclear facilities, etc.), electrical distribution networks (EDNs) must operate safely and sustainably in order to overcome problems such as high power losses and voltage drops, which must be addressed with the most efficient location and capacity of distributed generators (DGs). In order to address this purpose, the proposed research introduces a robust modified forensic-based investigation (mFBI) optimization method that is demonstrated first time to produce the optimum allocation of DGs in EDNs for minimizing power losses and voltage deviations. Moreover, the analytical hierarchy process approach is employed to generate the most applicable weighting factors of the multi-objective function (MOF). Validation and demonstration of the newly developed mFBI technique is conducted by studying the impact of DG integration on 118 IEEE EDN nodes and real Delta-Egypt EDNs. Additionally, an in-depth comprehensive analysis has been carried out between the novel mFBI and 7 recent proposed optimizers, considering the <PERSON>on sign rank test that is used to verify the significant nature of the results. The numeric results best demonstrate the advantage and utility of incorporating the MOF approach and the superior mFBI technique in the EDN to derive an efficient optimum solution.</p>", "Keywords": "Distributed generators; CEC’2020; Modified forensic; Based investigation optimizer (mFBI); Analytical hierarchy process method (AHP); Voltage deviation index approach", "DOI": "10.1007/s00521-022-08103-6", "PubYear": 2023, "Volume": "35", "Issue": "11", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Reactors Department, Nuclear Research Center, Egyptian Atomic Energy Authority (EAEA), Cairo, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computers and Information, Minia University, Minia, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Center for Radiation Research and Technology (NCRRT), Egyptian Atomic Energy Authority (EAEA), Cairo, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering, Helwan University, Cairo, Egypt"}], "References": [{"Title": "A Quasi-Oppositional-Chaotic Symbiotic Organisms Search algorithm for optimal allocation of DG in radial distribution networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106067", "JournalTitle": "Applied Soft Computing"}, {"Title": "Heuristic optimization techniques for connecting renewable distributed generators on distribution grids", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "17", "Page": "14195", "JournalTitle": "Neural Computing and Applications"}, {"Title": "FBI inspired meta-optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106339", "JournalTitle": "Applied Soft Computing"}, {"Title": "Lévy flight distribution: A new metaheuristic algorithm for solving engineering optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "103731", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Archimedes optimization algorithm: a new metaheuristic algorithm for solving optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1531", "JournalTitle": "Applied Intelligence"}, {"Title": "Genetic Algorithms and Satin Bowerbird Optimization for optimal allocation of distributed generators in radial system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>At<PERSON> A<PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107727", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-objective optimization of hybrid renewable energy system by using novel autonomic soft computing techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "94", "Issue": "", "Page": "107350", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 105455304, "Title": "NNSplit-SØREN: Supporting the model implementation of large neural networks in a programmable data plane", "Abstract": "Embedding neural network (NN) models in the data plane is one of the very promising and attractive ways to leverage the computational power of computer network switches. This method became possible with the advent of the P4 language controlling the programmable data plane. However, most data planes today have some constraints, such as a limited set of operations and limited memory size. The computational cost of training large-scale NNs is high. In addition, while complex large-scale NN architectures are often used to improve prediction accuracy, they affect the functional performance of the data plane because of factors such as numerous input parameters and complex model design. Therefore, determining how to reduce the performance cost incurred by implementing large NN models in the data plane is a critical issue that needs to be addressed. This research proposes a technique called Neural Network Split (NNSplit) to solve the performance problems of embedding a large NN in a data plane by splitting the NN layers across multiple data planes. To support layer splitting, a new protocol called SuppORting ComplEx Computation in the Network (SØREN) is also proposed. The SØREN protocol header carries the activation value and bridges the NN layers in all switches. A multi-class classification use case of network traffic is used as the context for the experimental analysis. Experimental results show that compared to non-splitting NN architectures, NNSplit can reduce memory usage by nearly 50% and increase network traffic throughput with the cost of a 14% increase in round-trip time. In addition, when the SØREN protocol is encapsulated into data packets, the average processing time of the switch is 773   µs, which has very little impact on the processing time of the packets. Experimental results also show that the proposed NNSplit–SØREN can support large NN models on the data plane with a small performance cost.", "Keywords": "", "DOI": "10.1016/j.comnet.2022.109537", "PubYear": 2023, "Volume": "222", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Information Technology and Management Program, Ming Chuan University, Taoyuan City, 333321, Taiwan, ROC"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National Central University, Taoyuan City, 320317, Taiwan, ROC"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National Central University, Taoyuan City, 320317, Taiwan, ROC;Corresponding author"}], "References": [{"Title": "Deep packet: a novel approach for encrypted traffic classification using deep learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "1999", "JournalTitle": "Soft Computing"}, {"Title": "The rise of traffic classification in IoT networks: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "102538", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Network traffic classification using deep convolutional recurrent autoencoder neural networks for spatial–temporal features extraction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "102890", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Artificial intelligence for securing industrial-based cyber–physical systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "291", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Deep Learning for Network Traffic Monitoring and Analysis (NTMA): A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "170", "Issue": "", "Page": "19", "JournalTitle": "Computer Communications"}, {"Title": "Multi class SVM algorithm with active learning for network traffic classification", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "176", "Issue": "", "Page": "114885", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep data plane programming and AI for zero-trust self-driven networking in beyond 5G", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "108668", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 105455310, "Title": "Preface of the Special Issue for the Oberwolfach Workshop on Computability Theory 2021", "Abstract": "", "Keywords": "", "DOI": "10.3233/COM-220950", "PubYear": 2022, "Volume": "11", "Issue": "3-4", "JournalId": 33294, "JournalTitle": "Computability", "ISSN": "2211-3568", "EISSN": "2211-3576", "Authors": [{"AuthorId": 1, "Name": "Vasco Brattka", "Affiliation": "Faculty of Computer Science, Universität der Bundeswehr München, Neubiberg, Germany;Department of Mathematics and Applied Mathematics, University of Cape Town, Rondebosch, South Africa"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Computer Sciences, Victoria University, Wellington, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mathematics and Mechanics, Kazan Federal University, Kazan, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Wisconsin, Madison, WI, USA"}], "References": []}, {"ArticleId": 105455406, "Title": "An Empirical Survey on Long Document Summarization: Datasets, Models, and Metrics", "Abstract": "<p>Long documents such as academic articles and business reports have been the standard format to detail out important issues and complicated subjects that require extra attention. An automatic summarization system that can effectively condense long documents into short and concise texts to encapsulate the most important information would thus be significant in aiding the reader’s comprehension. Recently, with the advent of neural architectures, significant research efforts have been made to advance automatic text summarization systems, and numerous studies on the challenges of extending these systems to the long document domain have emerged. In this survey, we provide a comprehensive overview of the research on long document summarization and a systematic evaluation across the three principal components of its research setting: benchmark datasets, summarization models, and evaluation metrics. For each component, we organize the literature within the context of long document summarization and conduct an empirical analysis to broaden the perspective on current research progress. The empirical analysis includes a study on the intrinsic characteristics of benchmark datasets, a multi-dimensional analysis of summarization models, and a review of the summarization evaluation metrics. Based on the overall findings, we conclude by proposing possible directions for future exploration in this rapidly growing field.</p>", "Keywords": "Document summarization; datasets; neural networks; language models; transformer", "DOI": "10.1145/3545176", "PubYear": 2023, "Volume": "55", "Issue": "8", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Monash University, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Monash University, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Deakin University, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Monash University, Australia"}], "References": [{"Title": "Automatic text summarization: A comprehensive survey", "Authors": "Waf<PERSON> <PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113679", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Neural Abstractive Text Summarization with Sequence-to-Sequence Models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "1", "Page": "1", "JournalTitle": "ACM/IMS Transactions on Data Science"}]}, {"ArticleId": *********, "Title": "Decentralized learning control for large-scale systems with gain-adaptation mechanisms", "Abstract": "This study investigated the gain-adaptation mechanism of decentralized learning control for large-scale interconnected systems subject to measurement noise. The control objective is to minimize asymptotically averaged tracking errors in the iteration domain. The state-coupling matrix concept is employed to model the interactions among subsystems. Decentralized learning control schemes are proposed with three gain sequences: a predefined decreasing gain sequence, global performance-adaptive gain sequence, and decentralized adaptive gain sequence. The input sequences generated by the proposed schemes are shown to be convergent in the mean-square sense. Illustrative simulations are performed to verify the theoretical results.", "Keywords": "", "DOI": "10.1016/j.ins.2022.12.043", "PubYear": 2023, "Volume": "623", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Renmin University of China, Beijing 100872, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Renmin University of China, Beijing 100872, PR China"}, {"AuthorId": 3, "Name": "Qijiang Song", "Affiliation": "School of Mathematics, Renmin University of China, Beijing 100872, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mathematics, Renmin University of China, Beijing 100872, PR China;Corresponding author"}], "References": [{"Title": "A just-in-time-learning based two-dimensional control strategy for nonlinear batch processes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "220", "JournalTitle": "Information Sciences"}, {"Title": "Enhanced kalman-filtering iterative learning control with application to a wafer scanner", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "541", "Issue": "", "Page": "152", "JournalTitle": "Information Sciences"}, {"Title": "A periodic iterative learning scheme for finite-iteration tracking of discrete networks based on FlexRay communication protocol", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "548", "Issue": "", "Page": "344", "JournalTitle": "Information Sciences"}, {"Title": "Continuous sliding mode iterative learning control for output constrained MIMO nonlinear systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "556", "Issue": "", "Page": "288", "JournalTitle": "Information Sciences"}, {"Title": "Global iterative learning control based on fuzzy systems for nonlinear multi-agent systems with unknown dynamics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "587", "Issue": "", "Page": "556", "JournalTitle": "Information Sciences"}, {"Title": "All state constrained decentralized adaptive implicit inversion control for a class of large scale nonlinear hysteretic systems with time-delays", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "588", "Issue": "", "Page": "52", "JournalTitle": "Information Sciences"}, {"Title": "Point-to-point consensus tracking control for unknown nonlinear multi-agent systems using data-driven iterative learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "488", "Issue": "", "Page": "78", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": *********, "Title": "Research on energy performance contracting with shared savings under stochastic market demand", "Abstract": "In practice, the market risk caused by stochastic market demand is a main risk affecting the shared savings contract implementation in energy performance contracting. We develop a game-theoretic model to characterize the strategic interaction between the manufacturer’s ex-ante capacity decision and the energy service company (ESCO)’s investment decision on digitization and intellectualization to save energy, through which we unfold the impact of stochastic market demand upon all the stakeholders, the supply chain efficiency and the total energy consumption. The results show that the demand uncertainty can significantly affect the decisions of the ESCO and the manufacturer. Specifically, when there is no demand uncertainty, the manufacturer’s capacity decision is independent of the ESCO’s decision on energy-saving effort. By contrast, under stochastic demand there exists a complementary relationship between the manufacturer’s capacity investment and the ESCO’s energy-saving effort. Moreover, by comparing the optimal equilibria in shared savings with that under centralized supply chain, we find that decentralization causes inefficiencies in the form of lower energy-saving effort, underinvestment or overinvestment in capacity, and lower supply chain profit. In addition, it is more valuable for the supply chain to coordinate the ESCO and the manufacturer when the market demand is more stochastic. Furthermore, though always cutting the manufacturer’s total energy consumption without demand uncertainty, implementing the ESCO’s energy-saving service can increase the manufacturer’s total energy consumption and carbon emission in the society in the presence of demand uncertainty. Hence, governmental agencies should not ignore the impact of stochastic demand upon the total energy consumption with the interplay between the endogenous capacity setup decision and energy-saving service, and to reduce the carbon emission in the society governmental policies should be designed by taking into account such an increase of the total energy consumption.", "Keywords": "", "DOI": "10.1016/j.cie.2022.108877", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Zhengzhou University, Zhengzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business Administration, South China University of Technology, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Accounting Studies, Xiamen University, Xiamen, China;School of Management, Xiamen University, Xiamen, China;Corresponding author"}], "References": []}, {"ArticleId": *********, "Title": "subGE: Enhancing the subgraph representation of molecular compounds structure–activity relationship discovery", "Abstract": "Prediction of the molecular compound structure–activity relationship is one of the most critical tasks in computer-assisted drug design. To accurately predict the properties of molecular compounds and explain their structure–activity relationships, we proposed a subgraph embedding model, subGE, based on reinforcement learning and mutual information mechanisms. First, molecular compounds were abstracted into graphs, and the original graphs were sampled using a breadth-first search. These subgraphs were then encoded using graph neural networks and converted into graph embeddings. Reinforcement learning was introduced to reduce the dimensionality of the subgraph embeddings and filter out significant subgraphs. A mutual information mechanism was introduced to further enhance the ability of the filtered subgraphs to characterize a full graph. SubGE was evaluated based on three open-source datasets, BBBP, Bace, and Clintox from the DeepChem package developed by MoleculeNet. The experimental results showed that subGE achieved accuracies of 86.61%, 80.49%, 95.81%, and 96.34% for four classification tasks with three datasets. These values represent improvements of 16.87%, 19.29%, 3.91%, and 3.73%, respectively, compared to that of existing graph convolutional networks, and of 8.34%, 6.64%, 5.53%, and 5.50%, respectively, compared to that of the direct encoding of subgraphs without introducing reinforcement learning and mutual information mechanisms. The subgraphs extracted by sub<PERSON> could fully explain the conformational relationships of compounds through visualization.", "Keywords": "", "DOI": "10.1016/j.engappai.2022.105727", "PubYear": 2023, "Volume": "119", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Materials Genome Institute, Shanghai University, Shanghai, 200444, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, 200444, China;Materials Genome Institute, Shanghai University, Shanghai, 200444, China;Zhejiang Laboratory, Hangzhou, 311100, Zhejiang, China;Corresponding author at: School of Computer Engineering and Science, Shanghai University, Shanghai, 200444, China"}], "References": [{"Title": "Adaptive transfer learning-based multiscale feature fused deep convolutional neural network for EEG MI multiclassification in brain–computer interface", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105347", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A new approach to dominant motion pattern recognition at the macroscopic crowd level", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105387", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 105455614, "Title": "On feasibility of roll-stamp forming variable-sectional metal channels", "Abstract": "<p>Sheet metal channels with variable sections or local features have been widely used in automobile and construction industries, and novel forming techniques, such as flexible roll forming process, flexibly reconfigurable roll forming process, <PERSON><PERSON><PERSON>’s flexible forming facility, and chain-die forming recently have been developed to manufacture those channels. In this paper, the feasibility of chain-die forming technique to manufacture channels with variable sections is systematically investigated through experiment and finite element simulation by taking 6 types of channel products as demonstration, including three variable-width and three variable-depth profiles. The forming process of the channels shows a combination of roll forming and stamping, and this roll-stamp mode has great potential in manufacturing a wide variety of channels with variable cross-sections. The formability for roll-stamp forming variable-depth channels is evaluated through finite element simulation and forming limit diagram. The roll-stamp mode can be discomposed into roll forming longitudinally and stamping vertically, and can achieve a reduction in forming load by the maximum of 33.9% compared with the conventional stamping in forming the flange step product. The forming direction sensitivity of the variable-width feature is discussed from the aspect of web arch height development.</p>", "Keywords": "Roll-stamp forming; Variable-width channels; Variable-depth channels; Roll forming; Forming load reduction; Directional Sensitivity", "DOI": "10.1007/s00170-022-10633-1", "PubYear": 2023, "Volume": "125", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Wang", "Affiliation": "State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Mining Engineering, The University of Queensland, St Lucia, Brisbane, Australia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Development and Application Technology of Automotive Steels (BaoSteel), Shanghai, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Development and Application Technology of Automotive Steels (BaoSteel), Shanghai, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai, China"}], "References": [{"Title": "On the prediction of wrinkling in flexible roll forming", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "7-8", "Page": "2257", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Springback and longitudinal bow in chain-die forming U and hat channels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "11-12", "Page": "3571", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 105455654, "Title": "Distributed region following and perimeter surveillance tasks in star-shaped sets", "Abstract": "In this paper, a distributed control law for a kinematically modelled agent swarm is proposed, which is able to control the multi-agent system into a star-shaped set. The main idea behind the present work is to combine the traditional potential function based approach with the transformation of coordinate frames to drive a group of limited range agents into a moving region. The difficult problem of tracking a star-shaped set is transformed into a simpler problem where the star-shaped set is converted into a circular region. The control signal is designed with reference to this new circular region and then fed back to the original region of interest. The overall evolution of the agents is mainly determined by an attraction potential that directs the swarm to the region of interest and an interaction potential that controls the relationships between the agents and avoids collisions. As a further degree of freedom, the proposed control protocol can be set to move the multi-agent system to a certain perimeter around the region of interest to perform a perimeter monitoring task. In both cases, i.e. tracking the region or monitoring the perimeter, the evolution of the agents within the zone of interest can be chosen to achieve either a speed consensus or a rotational motion. The latter can be extremely useful in all those cases where a small number of agents have to explore a whole large area. Numerical simulations are carried out to illustrate the main features of the resulting control strategy.", "Keywords": "Region following ; Star-shaped sets ; Target capturing ; Multi-agent system ; Distributed control law", "DOI": "10.1016/j.sysconle.2022.105437", "PubYear": 2023, "Volume": "172", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Modeling, Electronics and Systems, Università della Calabria, <PERSON>, Cubo 42-C, Rende (CS), 87036, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Modeling, Electronics and Systems, Università della Calabria, <PERSON>, Cubo 42-C, <PERSON><PERSON> (CS), 87036, Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Modeling, Electronics and Systems, Università della Calabria, <PERSON>, Cubo 42-C, Rende (CS), 87036, Italy"}], "References": [{"Title": "Swarm Robotic Behaviors and Current Applications", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "36", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Unmanned aerial vehicle abstraction layer: An abstraction layer to operate unmanned aerial vehicles", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "4", "Page": "172988142092501", "JournalTitle": "International Journal of Advanced Robotic Systems"}, {"Title": "Physical Human-UAV Interaction with Commercial Drones using Admittance Control", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "20", "Page": "258", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105455767, "Title": "Long-term missing value imputation for time series data using deep neural networks", "Abstract": "We present an approach that uses a deep learning model, in particular, a MultiLayer Perceptron, for estimating the missing values of a variable in multivariate time series data. We focus on filling a long continuous gap (e.g., multiple months of missing daily observations) rather than on individual randomly missing observations. Our proposed gap filling algorithm uses an automated method for determining the optimal MLP model architecture, thus allowing for optimal prediction performance for the given time series. We tested our approach by filling gaps of various lengths (three months to three years) in three environmental datasets with different time series characteristics, namely daily groundwater levels, daily soil moisture, and hourly Net Ecosystem Exchange. We compared the accuracy of the gap-filled values obtained with our approach to the widely used R-based time series gap filling methods and . The results indicate that using an MLP for filling a large gap leads to better results, especially when the data behave nonlinearly. Thus, our approach enables the use of datasets that have a large gap in one variable, which is common in many long-term environmental monitoring observations.", "Keywords": "", "DOI": "10.1007/s00521-022-08165-6", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Jangho Park", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Missing value imputation in multivariate time series with end-to-end generative adversarial networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "551", "Issue": "", "Page": "67", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105455816, "Title": "A multi-source heterogeneous spatial big data fusion method based on multiple similarity and voting decision", "Abstract": "<p>Data fusion is an efficient way to achieve an improved accuracy and more specific inferences by fusing and aggregating data from different sensors. However, due to the increasing complexity of spatial data with massive and multi-source heterogeneous characteristics, the existing methods cannot satisfy quite well the requirement for the integrity of data and the accuracy of fusion results in some specific situations. By considering the geographical properties of spatial data, a multi-source heterogeneous spatial big data fusion method based on multiple similarity and voting decision (SDFSV) is proposed in this paper, which develops a three-step record linking algorithm to improve the quality of entity recognition for the incremental fusion of massive data. Then, a one-time voting algorithm is introduced into the proposed method, so that the data conflicts can be significantly reduced and thus the accuracy of the data fusion can be improved. And a relation deduction method based on rule and entity recognition is presented to enhance the data integrity. In addition, in order to promote traceability and interpretability of fusion results, it is necessary to construct a data traceability mechanism. Experimental results show that SDFSV has an improved performance by using the data of Beijing Medical Institutions collected from 10 data sources.</p>", "Keywords": "Data fusion; Spatial big data; Multi-source heterogeneity; Multiple similarity; Voting decision", "DOI": "10.1007/s00500-022-07734-0", "PubYear": 2023, "Volume": "27", "Issue": "5", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "JD Technology, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijing, China; Scientific Research Base for Integrated Technologies of Precision Agriculture (Animal Husbandry), The Ministry of Agriculture, Beijing, China"}], "References": [{"Title": "Improving malicious URLs detection via feature engineering: Linear and nonlinear space transformation methods", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "101494", "JournalTitle": "Information Systems"}, {"Title": "Multi-sensor data fusion for accurate surface modeling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "19", "Page": "14449", "JournalTitle": "Soft Computing"}, {"Title": "Online-review analysis based large-scale group decision-making for determining passenger demands and evaluating passenger satisfaction: Case study of high-speed rail system in China", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "", "Page": "22", "JournalTitle": "Information Fusion"}, {"Title": "An optimal evidential data fusion algorithm based on the new divergence measure of basic probability assignment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "17", "Page": "11449", "JournalTitle": "Soft Computing"}, {"Title": "RETRACTED ARTICLE: Smart transportation travel model based on multiple data sources fusion for defense systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "7", "Page": "3247", "JournalTitle": "Soft Computing"}, {"Title": "A survey: Optimization and applications of evidence fusion algorithm based on <PERSON><PERSON><PERSON><PERSON> theory", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "109075", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": *********, "Title": "A survey on complex factual question answering", "Abstract": "Answering complex factual questions has drawn a lot of attention. Researchers leverage various data sources to support complex QA, such as unstructured texts, structured knowledge graphs and relational databases, semi-structured web tables, or even hybrid data sources. However, although the ideas behind these approaches show similarity to some extent, there is not yet a consistent strategy to deal with various data sources. In this survey, we carefully examine how complex factual question answering has evolved across various data sources. We list the similarities among these approaches and group them into the analysis–extend–reason framework, despite the various question types and data sources that they focus on. We also address future directions for difficult factual question answering as well as the relevant benchmarks.", "Keywords": "Question answering ; Complex question ; Factual question ; Knowledge base question answering ; Text2SQL ; Document-based question answering ; Table question answering ; Multi-source question answering", "DOI": "10.1016/j.aiopen.2022.12.003", "PubYear": 2023, "Volume": "4", "Issue": "", "JournalId": 78702, "JournalTitle": "AI Open", "ISSN": "2666-6510", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information School, Renmin University of China, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Information School, Renmin University of China, Beijing, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information School, Renmin University of China, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "Information School, Renmin University of China, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information School, Renmin University of China, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information School, Renmin University of China, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Tsinghua University, Beijing, China"}, {"AuthorId": 8, "Name": "Xin Lv", "Affiliation": "Tsinghua University, Beijing, China"}], "References": [{"Title": "Text‐based question answering from information retrieval and deep neural network perspectives: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "6", "Page": "e1412", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "Neural, symbolic and neural-symbolic reasoning on knowledge graphs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "14", "JournalTitle": "AI Open"}]}, {"ArticleId": 105455877, "Title": "Federated learning for interpretable short-term residential load forecasting in edge computing network", "Abstract": "<p>Short-term residential load forecasting is of great significance to smart grid applications. Deep learning techniques, especially recurrent neural networks, can greatly improve the performance of prediction models. However, deep neural networks usually have low interpretability, which creates obstacles for customers to deeply understand the prediction results and make quick responses. In addition, the existing deep learning prediction methods rely heavily on the centralized training of massive data. However, the transmission of data from the client to the server poses a threat to the data security of customers. In this work, we propose an interpretable deep learning framework with federated learning for short-term residential load forecasting. Specifically, we propose a new automatic relevance determination network for feature interpretation, combined with the encoder–decoder architecture to achieve interpretable multi-step load prediction. In the edge computing network, the training scheme based on federated learning does not share the original data, which can effectively protect data privacy. The introduction of iterative federated clustering algorithm can alleviate the problem of non-independent and identical distribution of data in different households. We use two real-world datasets to verify the feasibility and performance of the proposed method. Finally, we discuss in detail the feature interpretation of these two datasets.</p>", "Keywords": "Short-term residential load forecasting; Interpretable deep learning; Federated learning", "DOI": "10.1007/s00521-022-08130-3", "PubYear": 2023, "Volume": "35", "Issue": "11", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Chongchong Xu", "Affiliation": "School of Automation, Central South University, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Automation, Central South University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha, China"}], "References": []}, {"ArticleId": 105455898, "Title": "Atlas of Optimal Low-Thrust Rephasing Solutions in Circular Orbit", "Abstract": "<p>In this paper, the time- and propellant-optimal low-thrust rephasing problems in circular orbit are studied to depict their solution spaces in an atlas. The rephasing problem is known as the same-orbit rendezvous from different angular positions. It is generally solved by some optimization methods, but its whole solution space is rarely investigated due to many key parameters. To reduce the number of parameters, a set of linearized equations of motion is developed based on the <PERSON><PERSON> transformation, and two reduced shooting functions are formulated using the minimum principle and symmetry properties. Only one key parameter is identified for the time-optimal problem, while two key parameters are obtained for the propellant-optimal one. Numerical investigation of the relationships between these parameters and shooting variables reveals that they can be depicted by some curve (or contour) maps and approximated by piecewise functions (or linear interpolations). For the relatively short- or long-term rephasing cases, some analytical time- and propellant-optimal solutions are proposed and consistent with the numerical solutions. Numerical results demonstrate that the proposed solutions can provide good initial guesses to solve the low-thrust rephasing problems with nonlinear dynamics. Moreover, the approximations of the performance indexes can be used in the preliminary mission design.</p>", "Keywords": "Orbit Rendezvous; Trajectory Optimization; Mathematical Optimization; Low-Thrust Solutions; Atlas Representation", "DOI": "10.2514/1.G007138", "PubYear": 2023, "Volume": "46", "Issue": "5", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Tsinghua University, 100084 Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tsinghua University, 100084 Beijing, People’s Republic of China"}, {"AuthorId": 3, "Name": "Fanghua Jiang", "Affiliation": "Tsinghua University, 100084 Beijing, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Tsinghua University, 100084 Beijing, People’s Republic of China"}], "References": [{"Title": "Warm Start for Low-Thrust Trajectory Optimization via Switched System", "Authors": "<PERSON>; <PERSON>, <PERSON>; Fanghua Jiang", "PubYear": 2021, "Volume": "44", "Issue": "9", "Page": "1700", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Analytical Costate Estimation by a Reference Trajectory-Based Least-Squares Method", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "45", "Issue": "8", "Page": "1529", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}]}, {"ArticleId": 105455927, "Title": "Low area and high throughput implementation of advanced encryption standard hardware accelerator on FPGA using Mux‐Demux pair", "Abstract": "<p>Now-a-days advanced cryptographic algorithms are needed in order to improve data security and confidentiality. One such algorithm used prominently is advanced encryption standard (AES) algorithm. AES is a complex algorithm with multiple rounds of processing data and occupies more space or area when implemented on hardware. Since each sub-step of computation has a similar structure, the proposed method employs the novel idea of using the same hardware to implement the AES functionality. Hence the number of logical units occupied are leveraged. The proposed scheme, Mux-Demux pair method (MDP), uses a mux-demux structure. It is implemented on Virtex-7 and ZynQ7000 FPGAs and the code is written in Verilog HDL language in the Vivado software. The proposed work when simulated on Virtex-7 occupies an area of 1932 slices, giving an optimized throughput of 10.167 Gbps while the work simulated on ZynQ7000 occupies an area of 3253 slices, resulting in a throughput of 23.858 Gbps.</p>", "Keywords": "AES;area;encryption;FPGA board;hardware accelerator;throughput;Virtex-7;ZynQ7000", "DOI": "10.1002/spy2.292", "PubYear": 2023, "Volume": "6", "Issue": "4", "JournalId": 7086, "JournalTitle": "Security and Privacy", "ISSN": "2475-6725", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Indian Institute of Information Technology  Tiruchirappalli Tamil Nadu India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SoC design, QIPL IPS, Loc Tech team Qualcomm  Hyderabad India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Analytics team Amagi Media  Bengaluru India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SoC design, QIPL IPS, Loc Tech team Qualcomm  Hyderabad India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SoC design, QIPL IPS, Loc Tech team Qualcomm  Hyderabad India"}], "References": []}, {"ArticleId": 105455957, "Title": "Assessing the Industry 4.0 European divide through the country/industry dichotomy", "Abstract": "Industry 4.0 refers to the application of new technologies to production and supply chain processes under the fourth industrial revolution (4<sup>th</sup> IR) paradigm and has been studied mainly in manufacturing. The present study looks to understand how the 4<sup>th</sup> IR manifests itself in the EU countries and across different industries through an Industry 4.0 perspective. Following a five-step research protocol that used a multivariate approach, Industry 4.0 Infrastructure, Big Data Maturity and Industry 4.0 Applications were identified as characterizing elements of Industry 4.0. Cluster analysis showed five homogeneous profiles of Industry 4.0 implementation across different industries and European countries. Our findings unveil a strong Industry 4.0 divide across (and within) European countries and industries. Industry 4.0 is much more determined by the industry than by the country. Knowledge of Industry 4.0 development conditions would greatly benefit from further research on the elements that drive the Industry 4.0 divide at the industry and country levels.", "Keywords": "Industry 4.0 ; 4<sup>th</sup> Industrial Revolution ; Industry 4.0 divide ; Asymmetries ; European Union", "DOI": "10.1016/j.cie.2022.108925", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Universidade Nova de Lisboa, Campus de Campolide, 1070-312 Lisboa, Portugal;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Universidade Nova de Lisboa, Campus de Campolide, 1070-312 Lisboa, Portugal"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Universidade Nova de Lisboa, Campus de Campolide, 1070-312 Lisboa, Portugal"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Universidade Nova de Lisboa, Campus de Campolide, 1070-312 Lisboa, Portugal"}], "References": [{"Title": "The digital revolution in the travel and tourism industry", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "3", "Page": "455", "JournalTitle": "Information Technology & Tourism"}]}, {"ArticleId": *********, "Title": "Data-driven optimal control of wind turbines using reinforcement learning with function approximation", "Abstract": "We propose a reinforcement learning approach with function approximation for maximizing the power output of wind turbines (WTs). The optimal control of wind turbines majorly uses the maximum power point tracking (MPPT) strategy for sequential decision-making that can be modeled as a Markov decision process (MDP). In the literature, the continuous control variables are typically discretized to cope with the curse of dimensionality in traditional dynamic programming methods. To provide a more accurate prediction, we formulate the problem into an MDP with continuous state and action spaces by utilizing the function approximation in reinforcement learning. The commonly used pitch angle is selected as a control variable we are concerned with, which is regarded as the system state along with some other controllable and uncontrollable variables proven to affect the power output. Computational studies of real data are conducted to demonstrate that the proposed method outperforms the existing methods in the literature in obtaining the optimal power output.", "Keywords": "", "DOI": "10.1016/j.cie.2022.108934", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, University of Houston, Houston, TX 77204, United States of America;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, University of Houston, Houston, TX 77204, United States of America"}], "References": [{"Title": "Reinforcement learning with Gaussian processes for condition-based maintenance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> (May) Feng", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107321", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 105456028, "Title": "SLF: A passive parallelization of subgraph isomorphism", "Abstract": "Subgraph isomorphism is one of the most important graph query operations. Existing subgraph isomorphic parallelization methods suffer from redundant sharing or fine-grained parallelism. SLF, a parallel approach for subgraph isomorphism algorithm based on tree-search, is proposed in this paper. In SLF, threads are prevented from sharing tasks blindly by an “Ask for Sharing” mechanism. The “Low-depth Priority Sharing” rule filters out unnecessary sharing, which reduces the number of sharing. The context of shared tasks is represented by embedding, which reduces the time and memory usage of sharing. We prove that the parallelization overhead of SLF is smaller than that of previous method. The experimental results show that SLF achieves higher speedup and efficiency than state-of-the-art parallelization methods.", "Keywords": "", "DOI": "10.1016/j.ins.2022.12.033", "PubYear": 2023, "Volume": "623", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, China"}, {"AuthorId": 2, "Name": "<PERSON>yong Dong", "Affiliation": "School of Computer Science, Wuhan University, China"}, {"AuthorId": 3, "Name": "Mengting Yuan", "Affiliation": "School of Computer Science, Wuhan University, China;Corresponding author"}], "References": [{"Title": "Efficient access methods for very large distributed graph databases", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "573", "Issue": "", "Page": "65", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105456066, "Title": "Supervised contrastive learning for robust text adversarial training", "Abstract": "<p>The lack of robustness is a serious problem for deep neural networks (DNNs) and makes DNNs vulnerable to adversarial examples. A promising solution is applying adversarial training to alleviate this problem, which allows the model to learn the features from adversarial examples. However, adversarial training usually produces overfitted models and may not work when facing a new attack. We believe this is because the previous adversarial training using cross-entropy loss ignores the similarity between the adversarial examples and the original examples, which will result in a low margin. Accordingly, we propose a supervised adversarial contrastive learning (SACL) approach for adversarial training. SACL uses supervised adversarial contrastive loss which contains both the cross-entropy term and adversarial contrastive term. The cross-entropy term is used for guiding DNN inductive bias learning, and the adversarial contrastive term can help models learn example representations by maximizing feature consistency under different original examples, which fits well with the goal of solving low margins. In addition, SACL only uses adversarial examples which can successfully fool the model and their corresponding original examples for training. This process is more advantageous to provide the model with more accurate information about the decision boundary and obtain a model that fits the example distribution. Experiments show that SACL can reduce the attack success rate of multiple adversarial attack algorithms against different models on text classification tasks. The defensive performance is significantly better than other adversarial training approaches without reducing the generalization ability of the model. In addition, the DNN model trained by our approach has high transferability and robustness.</p>", "Keywords": "Adversarial attack; Contrastive learning; Supervised learning; Neural network", "DOI": "10.1007/s00521-022-07871-5", "PubYear": 2023, "Volume": "35", "Issue": "10", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Weidong Li", "Affiliation": "School of Cyber Science and Engineering, Wuhan University, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Cyber Science and Engineering, Wuhan University, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Wuhan University, Wuhan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Wuhan University, Wuhan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Wuhan University, Wuhan, China"}], "References": [{"Title": "A span-based model for aspect terms extraction and aspect sentiment classification", "Authors": "<PERSON><PERSON> Lv; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "8", "Page": "3769", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Unsupervised domain adaptation with adversarial distribution adaptation network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "13", "Page": "7709", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Scalable multi-channel dilated CNN–BiLSTM model with attention mechanism for Chinese textual sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "297", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Bound estimation-based safe acceleration for maximum margin of twin spheres machine with pinball loss", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "107860", "JournalTitle": "Pattern Recognition"}, {"Title": "A Survey on Document-level Neural Machine Translation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "GAR: Graph adversarial representation for adverse drug event detection on Twitter", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107324", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 105456116, "Title": "Diagnosis automation methods in the subject area", "Abstract": "<p>This article gives an overview of some existing diagnostic automation methods applicable to a variety of subject areas. At present, many branches of industry, medicine, agriculture and agrotechnical economies are moving towards reducing the need for involving human resources in the processes of diagnosing equipment malfunctions, various diseases of both people and plants. The number of different methods for diagnostics and processing the received data increases over time, as do the data flow itself and the requirements for the processing accuracy and speed. An important task is to build adequate models for data analysis, taking into account random perturbations and the need for rapid research at the rate of incoming data. The only way to choose the most optimal method is to conduct a comparative analysis and correlate many factors to be considered when choosing a particular method</p>", "Keywords": "", "DOI": "10.30987/2658-6436-2022-4-37-45", "PubYear": 2022, "Volume": "2022", "Issue": "4", "JournalId": 60984, "JournalTitle": "Automation and modeling in design and management of", "ISSN": "2658-3488", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Nacional'nyy issledovatel'skiy universitet, Moskovskiy institut elektronnoy tehniki"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nacional'nyy issledovatel'skiy universitet, Moskovskiy institut elektronnoy tehniki"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Nacional'nyy issledovatel'skiy universitet, Moskovskiy institut elektronnoy tehniki"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Nacional'nyy issledovatel'skiy universitet, Moskovskiy institut elektronnoy tehniki"}], "References": []}, {"ArticleId": 105456124, "Title": "Peramalan Kedatangan W<PERSON>tawan ke Suatu Negara Menggunakan Metode Support Vector Machine (SVM)", "Abstract": "<p>Industri pariwisata adalah salah satu ekosistem yang paling menjanjikan untuk sektor ekonomi di seluruh dunia. Sektor pariwisata yang kuat secara langsung memberikan berkontribusi positif  pada pendapatan nasional negara, memerangi pengangguran dan meningkatkan keseimbangan pembayaran. Perkembangan pariwisata dapat dilihat dari adanya peningkatan kedatangan ke suatu negara, berdasarkan data yang diperoleh dari UNWTO dari tahun 1995-2019 mengalami peningkatan dan penurunan. Peningkatan dan penurunan jumlah wisatawan secara tiba-tiba akan memberikan dampak positif dan negatif. Maka penelitian ini akan meramalkan kedatangan wisatawan ke suatu negara menggunakan metode Support Vector Machine (SVM). Berdasarkan penelitian ini didapatkan bahwa bahwa di dapat SVM Confidence sebesar 86,3%, memiliki nilai Mean Absolute Percentage Error (MAPE) sebesar 56.00% dan nilai Root Mean Square Error (RMSE) sebesar 11126.36 dari keseluruhan data yaitu 53 negara. Dan dilakukan forecasting terhadap 5 negara dengan kunjungan jumlah wisatawan terbanyak. Di dapatkan hasil yang sangat baik yaitu SVM Confidence sebesar 99,13%, memiliki nilai MAPE sebesar 2,78% dan nilai RMSE sebesar 2783,57.</p>", "Keywords": "Traveler; Time Series; Forecasting; Support Vector Machine; Kernel", "DOI": "10.37859/coscitech.v3i3.4211", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 83284, "JournalTitle": "Jurnal CoSciTech (Computer Science and Information Technology)", "ISSN": "2723-567X", "EISSN": "2723-5661", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}, {"AuthorId": 4, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}, {"AuthorId": 5, "Name": " Wide Mulyana", "Affiliation": "Universitas Muhammadiyah Riau"}], "References": []}, {"ArticleId": *********, "Title": "PhD Abstracts", "Abstract": "", "Keywords": "", "DOI": "10.1017/S0956796822000144", "PubYear": 2022, "Volume": "32", "Issue": "", "JournalId": 14278, "JournalTitle": "Journal of Functional Programming", "ISSN": "0956-7968", "EISSN": "1469-7653", "Authors": [{"AuthorId": 1, "Name": "GRAHAM HUTTON", "Affiliation": "University of Nottingham, UK e-mail; Corresponding author."}], "References": []}, {"ArticleId": 105456150, "Title": "Sistem pakar kerusakan honda beat street 2021 menggunkan metode forward chaining dan certainty factor", "Abstract": "<p><PERSON><PERSON><PERSON><PERSON> masyarakat terhadap kendaraan bermotor sangatlah besar khususnya sepeda motor Honda Beat Street 2021, sebab sepeda motor dianggap sebagai sarana transportasi yang sangat memudahkan pengendara untuk menuju tempat dengan pertimbanganwaktu yang lebih cepat dibandingkan dengan menggunakan kendaraan yang beroda empat. Kurangnya pengetahuan masyarakat tentang kerusakan sepeda motor Honda Beat Street 2021 menimbulkan kerugian bagi pengguna dalam hal waktu dan biaya. Dalam masalah tersebut sepeda motor yang mengalami kerusakan dapat diatasi oleh seorang pakar dengan pengetahuan dan pengalamannya. Untuk itu perlu dibuatkan sebuah sistem pakar yang dapat mendiagnosa kerusakan yang terjadi sepeda motor Honda Beat Street 2021, dimana sistem pakar ini bertujuan untuk mentransfer pengetahuan yang dimiliki seorang pakar ke dalam komputer sehingga pengguna lebih menghemat waktu dan biaya. Sistem pakar kerusakan sepeda motor Honda Beat Street 2021 ini dibangun dengan bahasa pemrograman web PHP dan database MySQL. Proses inferensi sistem pakar ini menggunakan metode forward chaining dan proses perhitungan nilai kepastian menggunakan metode certainty factor. Para pengguna dapat mendiagnosis kerusakan yang terjadi pada sepeda motor Honda Beat Street 2021 mereka dengan mudah dan mengetahui cara penanganan kerusakan dengan memilih gejala yang ada pada sistem. Informasi pengetahuan dasar pada sistem dapat diupdate, ditambah, atau dihapus oleh admin (pakar). Presentase hasil diagnosa dengan menggunakan proses perhitungan Certainty Factor (CF) sangat dipengaruhi pada nilai CF yang diberikan oleh pakar. Uji coba sistem untuk 10 kasus menghasilkan tingkat akurasi sebesar 90%.</p>", "Keywords": "", "DOI": "10.37859/coscitech.v3i3.4377", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 83284, "JournalTitle": "Jurnal CoSciTech (Computer Science and Information Technology)", "ISSN": "2723-567X", "EISSN": "2723-5661", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}], "References": []}, {"ArticleId": 105456233, "Title": "Generative design of physical objects using modular framework", "Abstract": "In recent years generative design techniques have become firmly established in numerous applied fields, especially in engineering. These methods are crucial for automating the initial stages of the engineering design of various structures, which reduces the amount of routine work. However, existing approaches are limited by the specificity of the problem under consideration. In addition, they do not provide the desired flexibility in choosing a method for a particular problem. To avoid these issues, we proposed a general approach to an arbitrary generative design problem and implemented a novel open-source framework called GEFEST (Generative Evolution For Encoded STructure) on its basis. This approach is based on three general principles: sampling, estimation, and optimization. This ensures the freedom of method adjustment for the solution of the particular generative design problem and therefore enables the construction of the most suitable one. A series of experimental studies was conducted to confirm the effectiveness of the GEFEST framework. It involved synthetic and real-world cases (coastal engineering, microfluidics, thermodynamics, and oil field planning). The flexible structure of GEFEST makes it possible to obtain results that surpass baseline and state-of-the-art solutions: 12% improvement in the coastal engineering problem; 9% in microfluidics; 8% in thermodynamics and 7% in oil field planning.", "Keywords": "", "DOI": "10.1016/j.engappai.2022.105715", "PubYear": 2023, "Volume": "119", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ITMO University, Saint-Petersburg, Russia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ITMO University, Saint-Petersburg, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> the Great St. Petersburg Polytechnic University, Saint-Petersburg, Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ITMO University, Saint-Petersburg, Russia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "ITMO University, Saint-Petersburg, Russia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "ITMO University, Saint-Petersburg, Russia"}], "References": [{"Title": "A survey of the recent architectures of deep convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "5455", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Generative Design: An Explorative Study", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "1", "Page": "144", "JournalTitle": "Computer-Aided Design and Applications"}, {"Title": "Exponential stability of nonlinear state-dependent delayed impulsive systems with applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "", "Page": "101088", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}]}, {"ArticleId": 105456256, "Title": "A scoping review of supervised learning modelling and data-driven optimisation in monoclonal antibody process development", "Abstract": "<b  >Background</b> Supervised learning modelling and data-driven optimisation (SLDO) methods have only recently gathered interest in the monoclonal antibody (mAb) platform process development application, but have already demonstrated their advantages over traditional approaches in reducing development costs and accelerating research efforts. With potential usage in multiple unit operations, there is a need for mapping existing SLDO methodologies with the corresponding mAb applications. <b  >Methods</b> We performed a scoping review of mAb process development studies with at least one SLDO method published prior to April 26, 2022. A team of four independent reviewers conducted a search and synthesised characteristics of the eligible studies from four literature databases. <b  >Results</b> We identified 30 relevant studies from 1785 citations and 118 full-text papers. 70% were upstream studies (n = 21), and the majority of papers were published between 2010 and 2022 (n = 27, 90%). Multivariate data analysis (MVDA) techniques were identified as the most common SLDO methods (n = 11), and were typically used to model heterogeneous and high-dimensional bioprocess data. While the main usage of SLDO in process development was predictive modelling, a few studies also focused on data pre-processing, knowledge transfer, and optimisation. <b  >Conclusions</b> Despite the data challenges inherent to the mAb industry, SLDO has been demonstrated to be an efficient solution to some process development use cases such as knowledge transfer, process characterisation, optimisation, and predictive modelling. As biopharmaceutical companies are advancing their digital transformation, SLDO methods will need to be further developed and studied from a more integrative perspective to remain competitive against other platform development approaches.", "Keywords": "Monoclonal antibody ; Process development ; Supervised learning ; Data-driven optimisation ; Process modelling ; Scoping review", "DOI": "10.1016/j.dche.2022.100080", "PubYear": 2023, "Volume": "7", "Issue": "", "JournalId": 90723, "JournalTitle": "Digital Chemical Engineering", "ISSN": "2772-5081", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Parkville, Australia;CSL Innovation, Parkville, Australia;Corresponding author at: School of Computing and Information Systems, The University of Melbourne, Parkville, Australia"}, {"AuthorId": 2, "Name": "Chaitanya Manapragada", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Parkville, Australia"}, {"AuthorId": 3, "Name": "Yuan Sun", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Parkville, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CSL Innovation, Parkville, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Parkville, Australia"}], "References": [{"Title": "Model uncertainty-based evaluation of process strategies during scale-up of biopharmaceutical processes", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "106693", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "How to tell the difference between a model and a digital twin", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Advanced Modeling and Simulation in Engineering Sciences"}, {"Title": "Data intelligence for process performance prediction in biologics manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "146", "Issue": "", "Page": "107226", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Anticipated cell lines selection in bioprocess scale-up through machine learning on metabolomics dynamics", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "3", "Page": "85", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105456423, "Title": "Multi-view Contour-constrained Transformer Network for Thin-cap Fibroatheroma Identification", "Abstract": "Identification and detection of thin-cap fibroatheroma (TCFA) from intravascular optical coherence tomography (IVOCT) images is critical for treatment of coronary heart diseases. Recently, deep learning methods have shown promising successes in TCFA identification. However, most methods usually do not effectively utilize multi-view information or incorporate prior domain knowledge. In this paper, we propose a multi-view contour-constrained transformer network (MVCTN) for TCFA identification in IVOCT images. Inspired by the diagnosis process of cardiologists, we use contour constrained self-attention modules (CCSM) to emphasize features corresponding to salient regions (i.e., vessel walls) in an unsupervised manner and enhance the visual interpretability based on class activation mapping (CAM). Moreover, we exploit transformer modules (TM) to build global-range relations between two views (i.e., polar and Cartesian views) to effectively fuse features at multiple feature scales. Experimental results on a semi-public dataset and an in-house dataset demonstrate that the proposed MVCTN outperforms other single-view and multi-view methods. Lastly, the proposed MVCTN can also provide meaningful visualization for cardiologists via CAM.", "Keywords": "IVOCT ; TCFA ; Plaque identification ; Multi-view learning ; Transformer", "DOI": "10.1016/j.neucom.2022.12.041", "PubYear": 2023, "Volume": "523", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence and Robotics, Xi’an Jiaotong University, Xi’an 710049, China;Biomedical Imaging Group Rotterdam, Department of Radiology & Nuclear Medicine, Erasmus MC, Rotterdam, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence and Robotics, Xi’an Jiaotong University, Xi’an 710049, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence and Robotics, Xi’an Jiaotong University, Xi’an 710049, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Cardiovascular Department, First Affiliated Hospital of Medical College, Xi’an Jiaotong University, Xi’an 710049, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>ng Su", "Affiliation": "Biomedical Imaging Group Rotterdam, Department of Radiology & Nuclear Medicine, Erasmus MC, Rotterdam, The Netherlands"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Biomedical Imaging Group Rotterdam, Department of Radiology & Nuclear Medicine, Erasmus MC, Rotterdam, The Netherlands;Faculty of Applied Sciences, Delft University of Technology, The Netherlands"}, {"AuthorId": 7, "Name": "<PERSON>ning <PERSON>", "Affiliation": "Institute of Artificial Intelligence and Robotics, Xi’an Jiaotong University, Xi’an 710049, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Biomedical Imaging Group Rotterdam, Department of Radiology & Nuclear Medicine, Erasmus MC, Rotterdam, The Netherlands"}], "References": []}, {"ArticleId": 105456461, "Title": "Microscopic model on indoor propagation of respiratory droplets", "Abstract": "Indoor propagation of airborne diseases is yet poorly understood. Here, we theoretically study a microscopic model based on the motions of virus particles in a respiratory microdroplet, responsible for airborne transmission of diseases, to understand their indoor propagation. The virus particles are driven by a driving force that mimics force due to gushing of air by devices like indoor air conditioning along with the gravity. A viral particle within the droplet experiences viscous drag due to the droplet medium, force due to interfacial tension at the droplet boundary, the thermal forces and mutual interaction forces with the other viral particles. We use Brownian Dynamics (BD) simulations and scaling arguments to study the motion of the droplet, given by that of the center of mass of the viral assembly. The BD simulations show that in presence of the gravity force alone, the time the droplet takes to reach the ground level, defined by the gravitational potential energy being zero, from a vertical height H, t f ∼ γ − 0.1  dependence, where γ is the interfacial tension. In presence of the driving force of magnitude F <sub> 0 </sub> and duration τ 0 , the horizontal propagation length, Y <sub> max </sub> from the source increase linearly with  τ 0 , where the slope is steeper for larger F <sub> 0 </sub>. Our scaling analysis explains qualitatively well the simulation observations and show long-distance transmission of airborne respiratory droplets in the indoor conditions due to F <sub> 0 </sub> ∼ nano-dyne.", "Keywords": "Air conditioning;Airborne disease;Airborne transmission;Brownian dynamics;Driven motion;Respiratory droplet", "DOI": "10.1016/j.compbiolchem.2022.107806", "PubYear": 2023, "Volume": "102", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Systems and Physical Biology, Shenzhen Bay Laboratory, Shenzhen 518107, China. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Lady Brabourne College, P-1/2, Suhrawardy Avenue, Kolkata 700017, West Bengal, India. Electronic address:  ."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Systems and Physical Biology, Shenzhen Bay Laboratory, Shenzhen 518107, China; Beijing National Laboratory for Molecular Sciences, College of Chemistry and Molecular Engineering, Biomedical Pioneering Innovation Center, Beijing Advanced Innovation Center for Genomics, Peking University, Beijing 100871, China. Electronic address:  ."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computational Science Division, Saha Institute of Nuclear Physics, 1/AF Bidhannagar, Kolkata 700064, India. Electronic address:  ."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical, Biological and Macro-Molecular Sciences, Thematic unit of Excellence on Computational Materials Science and Technical Research Centre, S. N. Bose National Centre for Basic Sciences, Sector-III, Salt Lake, Kolkata 700098, India. Electronic address:  ."}], "References": []}, {"ArticleId": 105456591, "Title": "An enhanced triethylamine response by incorporating mesoporous CuO into nanosheet-assembled Co3O4 microtubes", "Abstract": "Realizing high gas response to triethylamine (TEA) are desirable for employment of sensors in applications of industrial safety and assessing the freshness of the seafood. Herein, a CuO/Co<sub>3</sub>O<sub>4</sub> heteojunction is proposed to improve the gas response performance via incorporating mesoporous CuO into nanosheet-assembled Co<sub>3</sub>O<sub>4</sub> microtubes. A novel approach to generate different proportion mesoporous CuO/Co<sub>3</sub>O<sub>4</sub> heterojunction is demonstrated, through which the morphologies can be fine-tuned. The resultant gas sensing performance shows that the Cu-Co-2 specimen exhibits the remarkably enhanced gas response value (7.6) to 200 ppm TEA, wide linear detection range (1–200 ppm), excellent repeatability and long-term stability as well as good gas selectivity to TEA gas at the working temperature of 320 ℃, which can be attributed to the surface active sites and the CuO/Co<sub>3</sub>O<sub>4</sub> heterointerface. This facile approach sheds lights on the design of sensing materials via construction of heterostructures for the high efficiency detection of target gas.", "Keywords": "Mesoporous CuO ; Co<sub>3</sub>O<sub>4</sub> microtubes ; Heterointerface ; Gas sensor ; Triethylamine", "DOI": "10.1016/j.snb.2022.133230", "PubYear": 2023, "Volume": "379", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Xi’an Technological University, School of Materials and Chemical Engineering, Xi’an, Shaanxi 710021, China"}, {"AuthorId": 2, "Name": "Chonghao Hu", "Affiliation": "Xi’an Technological University, School of Materials and Chemical Engineering, Xi’an, Shaanxi 710021, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Xi’an Technological University, School of Materials and Chemical Engineering, Xi’an, Shaanxi 710021, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Xi’an Technological University, School of Materials and Chemical Engineering, Xi’an, Shaanxi 710021, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Xi’an Technological University, School of Science, Xi’an, Shaanxi 710021, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xi’an Technological University, School of Materials and Chemical Engineering, Xi’an, Shaanxi 710021, China"}], "References": [{"Title": "Controllable synthesis of heterostructured CuO–NiO nanotubes and their synergistic effect for glycol gas sensing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127347", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Carbon monoxide gas sensing properties of metal-organic frameworks-derived tin dioxide nanoparticles/molybdenum diselenide nanoflowers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127369", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Plasma sprayed CuO coatings for gas sensing and catalytic conversion applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "331", "Issue": "", "Page": "129404", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Elastic loading enhanced NH3 sensing for surface acoustic wave sensor with highly porous nitrogen doped diamond like carbon film", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "344", "Issue": "", "Page": "130175", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Hierarchical flower-like TiO2 microspheres for high-selective NH3 detection: A density functional theory study", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "345", "Issue": "", "Page": "130303", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Sacrificial template triggered to synthesize hollow nanosheet-assembled Co3O4 microtubes for fast triethylamine detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "355", "Issue": "", "Page": "131246", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 105456734, "Title": "Optimal replacement in a proportional hazards model with cumulative and dependent risks", "Abstract": "This paper deals with a new maintenance problem with two competing dependent risks: minor failures and major failures. The cumulative number of minor failures is incorporated into the proportional hazards model as a covariant process of the hazard function of major failures. We introduce the “Time Discrete Markovian Approximation” (TDMA) technique to solve the “curse of dimensionality” in Markov Decision Processes (MDP) and simplify the high-dimensional integration when computing the average cost by renewal theory. We develop a new optimal control limit policy with a “mixed hazards function” as the threshold and reveal its agreement with the solution from MDP. Additionally, a corresponding iterative algorithm is developed to produce a sequence converging to the optimal solution faster than the policy iteration.", "Keywords": "", "DOI": "10.1016/j.cie.2022.108930", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electronic Engineering, Hubei Polytechnic University, Huangshi, 435003, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, University of Toronto, 5 King’s College Rd, Toronto, M5S 3G8, Ontario, Canada;Corresponding author"}], "References": []}, {"ArticleId": 105456742, "Title": "Analysing approaches to building the software complex architecture for managing repair in service companies", "Abstract": "<p>The paper presents an analysis of approaches to building an architecture system for service companies. A brief review of the subject area is made; the problems of such companies are identified. The article substantiates that the problem of choosing the software complex architecture is multi-criteria in nature. The main variants of the software package architecture are introduced, for which their advantages and disadvantages are identified and analysed, and the criteria for their evaluation are described. To solve the problem of multi-criteria optimisation, a weighted convolution method of the “distance to ideal” type is chosen. As a result, it is found that the most suitable software architecture for service companies is EAM-system with the developed Service Desk subsystem and flexible integration with external Service Desk systems to expand functionality in appropriate cases</p>", "Keywords": "", "DOI": "10.30987/2658-6436-2022-4-70-78", "PubYear": 2022, "Volume": "2022", "Issue": "4", "JournalId": 60984, "JournalTitle": "Automation and modeling in design and management of", "ISSN": "2658-3488", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bryansk State Technical University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Bryansk State Technical University"}], "References": []}, {"ArticleId": 105456745, "Title": "ntelligent analysis of the attractiveness of the Bashkortostan republic municipalities on the basis of scenario forecasting and geoinformation technologies", "Abstract": "<p>Socio-economic development of territories depends on many factors, one of which is the region population. Therefore, managing the migration flows is an important tool for solving various demographic, socio-economic and other problems. In recent years, the Republic of Bashkortostan has seen a continuous migration population outflow both to different regions of the Russian Federation and to other countries, which indicates that for the effective development of the territories it is necessary to regulate the region migration flows. To do this, it seems appropriate to increase the attractiveness level of the region municipalities. In this regard, this article is devoted to the intellectual analysis of the attractiveness of the Bashkortostan Republic municipalities based on scenario forecasting and geo information technologies, which will improve the decision-making efficiency in forming the Republic of Bashkortostan migration policy. In the study course, an analysis of existing approaches used in this subject area is carried out, a methodology for determining the attractiveness coefficient of municipalities is considered, and the results of predictive calculations of the region territory attractiveness are presented.</p>", "Keywords": "", "DOI": "10.30987/2658-6436-2022-4-54-62", "PubYear": 2022, "Volume": "2022", "Issue": "4", "JournalId": 60984, "JournalTitle": "Automation and modeling in design and management of", "ISSN": "2658-3488", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ufa State Aviation Technical University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institut social'no - ekonomicheskih issledovaniy UFIC RAN"}], "References": []}, {"ArticleId": 105456766, "Title": "Distortion identification of the cylindrical part form of technological units", "Abstract": "<p>The paper deals with the issues arising during the technological unit operation, in particular, the geometric characteristics control of large parts of the units. The main types of rolling the surface wear of cylindrical elements of various technological equipment are given (according to GOST 24642-81, GOST 18322-2016). The equipment wear patterns due to the operating conditions and the unit design, as well as differences in the part shape distortion with the roundness loss are highlighted. It is revealed that the uneven wear of the rolling surface of the technological cylindrical parts causes shape distortion. Considering this, the profile of a conditional round part in cross section relative to one centre is modelled. The appearance of ovality is accepted as the main defect of cylindrical parts. A calculation scheme is presented for constructing the distortion of the cylindrical body profile located on two round supports. An algorithm for computing the radii centre displacement is presented. Theoretical calculations and the algorithm are confirmed by a virtual kinematic model that displays rolling a non-circular body on two idlers. The method described in the paper for obtaining a visual kinematic model, based on an analytical description of rolling a non-circular body, is proposed to be used as a direct problem of searching for instantaneous centres of rotation. The kinematic model obtained by solving the direct problem can be applied for training a neural network that processes the measurement results of a real rolling body</p>", "Keywords": "", "DOI": "10.30987/2658-6436-2022-4-29-36", "PubYear": 2022, "Volume": "2022", "Issue": "4", "JournalId": 60984, "JournalTitle": "Automation and modeling in design and management of", "ISSN": "2658-3488", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Belgorod State Technological University named after <PERSON><PERSON><PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Politehnicheskiy kolledzh №8 im. I.F. <PERSON>, Moskva"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Belgorod State Technological University named after <PERSON><PERSON><PERSON><PERSON>"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Belgorodskiy Gosudarstvennyy Tehnologicheskiy Universitet im. V.G. <PERSON>"}], "References": []}, {"ArticleId": 105457002, "Title": "Optimization of Base Station Placement in 4G LTE Broadband Networks Using Adaptive Variable Length Genetic Algorithm", "Abstract": "<p>The growing subscriptions for cellular communication services have been remarkable globally. This development has placed immense pressure on the network providers to optimally position the radio transmitters to deliver quality services to mobile subscribers. The conventional cellular network planning techniques are prone to errors and incapable of guaranteeing the desired quality of service at user equipment terminals. To address this problem, meta-heuristic evolutionary algorithms have been employed to boost the radio transmitter placement for optimal performance. However, the meta-heuristic evolutionary algorithms require experimental measurements for testing and validation. This paper uses a field measurement-based genetic algorithms approach to optimize base station placement in cellular networks. The proposed method explores the combined impact of strong cellular networks influencing parameters, such as capacity, coverage, and transmit power in the base station placement process. Field signal strength measurement was conducted to examine the propagation loss predictive performance of the referenced COST 231 Hata model. This approach provided the means to determine the cell coverage radius and the minimum base station number needed for model optimization. Higher order sectorization (considering six and 12 sectors per site) on cellular network capacity was employed. Generally, results revealed that the system capacity increases compared to the standard approach with three sectors per site. The adaptive variable-length genetic algorithm approach was employed with a weighted fitness function that combines coverage, capacity, and transmit power parameters. The proposed approach outperforms the conventional analytical method based on the referenced COST 231 Hata model in convergence speed, computational efficiency, and algorithm reliability. Improved performance with the projected approach was demonstrated over the benchmarked particle swarm optimization approach.</p>", "Keywords": "Field measurements; Optimal base station placement; Genetic algorithm; Swarm particle optimization; COST 231 Hata model; Cellular networks", "DOI": "10.1007/s42979-022-01533-y", "PubYear": 2023, "Volume": "4", "Issue": "2", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Benin, Benin City, Nigeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Physics, Federal University Lokoja, Lokoja, Nigeria"}, {"AuthorId": 3, "Name": "Agbotiname Lucky Imoize", "Affiliation": "Department of Electrical and Electronics Engineering, Faculty of Engineering, University of Lagos, Lagos, Nigeria; Department of Electrical Engineering and Information Technology, Institute of Digital Communication, Ruhr University, Bochum, Germany"}], "References": [{"Title": "A review on genetic algorithm: past, present, and future", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "5", "Page": "8091", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Customer churn prediction system: a machine learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "104", "Issue": "2", "Page": "271", "JournalTitle": "Computing"}, {"Title": "Competitive swarm optimization based unequal clustering and routing algorithms (CSO-UCRA) for wireless sensor networks", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "17", "Page": "26093", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An optimized feature selection using bio-geography optimization technique for human walking activities recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "12", "Page": "2893", "JournalTitle": "Computing"}, {"Title": "An enhanced fast non-dominated solution sorting genetic algorithm for multi-objective problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "585", "Issue": "", "Page": "441", "JournalTitle": "Information Sciences"}]}, {"ArticleId": *********, "Title": "Akuisisi Bukti Digital Pada Aplikasi Michat di Smartphone Menggunakan Metode National Institute of Standards and Technology (NIST)", "Abstract": "<p>The development of technology in the world of mobile devices is growing. However, these developments have a detrimental impact, one of which is cybercrime. Cybercrime is a crime that uses information technology. One of the smart phone technologies which includes cases of cybercrime that are often found in Indonesia include online prostitution of minors. This is done by utilizing social media, namely <PERSON><PERSON><PERSON> as a medium of communication. The “chat with nearby users” feature by uploading a status that  can be connected to the surrounding area within a certain radius and after being connected, the actor and his potential customer will negotiate and transact with each other until they finally meet. To eliminate digital evidence in the form of conversations in messages, usually perpetrators will delete the history of messages that produce data that can be used as evidence and perpetrators who can avoid legal traps, which are ultimately online. prostitution will be rampant. For online prostitution activities, it is necessary to carry out mobile forensics to find evidence which is then useful to be given to the authorities. This research uses the National Institute of Standards and Technology (NIST) method.</p>", "Keywords": "", "DOI": "10.37859/coscitech.v3i3.4359", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 83284, "JournalTitle": "Jurnal CoSciTech (Computer Science and Information Technology)", "ISSN": "2723-567X", "EISSN": "2723-5661", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}, {"AuthorId": 3, "Name": " <PERSON>", "Affiliation": "Universitas Muhammadiyah Riau"}], "References": []}, {"ArticleId": 105457119, "Title": "A self-training automatic infant-cry detector", "Abstract": "Infant cry is one of the first distinctive and informative life signals observed after birth. Neonatologists and automatic assistive systems can analyse infant cry to early-detect pathologies. These analyses extensively use reference expert-curated databases containing annotated infant-cry audio samples. However, these databases are not publicly accessible because of their sensitive data. Moreover, the recorded data can under-represent specific phenomena or the operational conditions required by other medical teams. Additionally, building these databases requires significant investments that few hospitals can afford. This paper describes an open-source workflow for infant-cry detection, which identifies audio segments containing high-quality infant-cry samples with no other overlapping audio events (e.g. machine noise or adult speech). It requires minimal training because it trains an LSTM-with-self-attention model on infant-cry samples automatically detected from the recorded audio through cluster analysis and HMM classification. The audio signal processing uses energy and intonation acoustic features from 100-ms segments to improve spectral robustness to noise. The workflow annotates the input audio with intervals containing infant-cry samples suited for populating a database for neonatological and early diagnosis studies. On 16 min of hospital phone-audio recordings, it reached sufficient infant-cry detection accuracy in 3 neonatal care environments (nursery—69%, sub-intensive—82%, intensive—77%) involving 20 infants subject to heterogeneous cry stimuli, and had substantial agreement with an expert’s annotation. Our workflow is a cost-effective solution, particularly suited for a sub-intensive care environment, scalable to monitor from one to many infants. It allows a hospital to build and populate an extensive high-quality infant-cry database with a minimal investment.", "Keywords": "Artificial intelligence; Neonatology; Infant-cry detection; Audio processing; Machine learning; Early diagnosis", "DOI": "10.1007/s00521-022-08129-w", "PubYear": 2023, "Volume": "35", "Issue": "11", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Istituto di Scienza e Tecnologie dell’Informazione “A. Faedo”, Consiglio Nazionale delle Ricerche, Pisa, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Centro di Formazione e Simulazione Neonatale NINA, Dipartimento Materno-Infantile, AOUP, Pisa, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Centro di Formazione e Simulazione Neonatale NINA, Dipartimento Materno-Infantile, AOUP, Pisa, Italy; Unitá Operativa Neonatologia, Dipartimento Materno-Infantile, AOUP, Pisa, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Centro di Formazione e Simulazione Neonatale NINA, Dipartimento Materno-Infantile, AOUP, Pisa, Italy; Unitá Operativa Neonatologia, Dipartimento Materno-Infantile, AOUP, Pisa, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centro di Formazione e Simulazione Neonatale NINA, Dipartimento Materno-Infantile, AOUP, Pisa, Italy; Unitá Operativa Neonatologia, Dipartimento Materno-Infantile, AOUP, Pisa, Italy"}], "References": [{"Title": "Psycho-acoustics inspired automatic speech recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "93", "Issue": "", "Page": "107238", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 105457208, "Title": "Minimum number of scans for collagen fibre direction estimation using Magic Angle Directional Imaging (MADI) with a priori information", "Abstract": "Tissues such as tendons, ligaments, articular cartilage, and menisci contain significant amounts of organised collagen which gives rise to the Magic Angle effect during magnetic resonance imaging (MRI). The MR intensity response of these tissues is dependent on the angle between the main field, B 0 , and the direction of the collagen fibres. Our previous work showed that by acquiring scans at as few as 7–9 different field orientations, depending on signal to noise ratio (SNR), the tissue microstructure can be deduced from the intensity variations across the set of scans. Previously our Magic Angle Directional Imaging (MADI) technique used rigid registration and manual final alignment, and did not assume any knowledge of the target anatomy being scanned. In the present work, fully automatic soft registration is incorporated into the MADI workflow and a priori knowledge of the target anatomy is used to reduce the required number of scans. Simulation studies were performed to assess how many scans are theoretically necessary. These findings were then applied to MRI data from a caprine knee specimen. Simulations suggested that using 3 scans might be sufficient, but in practice 4 scans were necessary to achieve high accuracy. 5 scans only offered marginal gains over 4 scans. A 15 scan dataset was used as a gold standard for quantitative voxel-to-voxel comparison of computed fibre directions, qualitative comparison of collagen tractography plots are also presented. The results are also encouraging at low SNR values, showing robustness of the method and applicability at low field.", "Keywords": "Collagen ; Magic angle ; Magnetic resonance imaging ; Tractography ; Soft registration", "DOI": "10.1016/j.array.2022.100273", "PubYear": 2023, "Volume": "17", "Issue": "", "JournalId": 66362, "JournalTitle": "Array", "ISSN": "2590-0056", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering Department, Imperial College London, London, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, Imperial College London, London, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, Imperial College London, London, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering Department, Imperial College London, London, UK"}], "References": []}, {"ArticleId": 105457415, "Title": "Elliptic Curve Cryptography; Applications, challenges, recent advances, and future trends: A comprehensive survey", "Abstract": "Elliptic Curve (EC) is the most recent and advanced technique of Elliptic Curve Cryptography (ECC). EC is often used to improve the security of open communication networks and to let specific persons with confirmed identities into the Modern Digital Era (MDE). Users of MDE make use of many technologies, such as social media, the cloud, and the IoT industry, among others. No matter what tool the users are using, the whole environment has to be able to keep their security and privacy preserved. The study of cryptography is required because unsecure networks make data transmission and the transfer of information susceptible to data theft and attack via an open channel. This makes it necessary to learn cryptography. The art of encrypting documents and communications using keys in such a way that only the individuals who are intended to receive them are able to decode and process them is referred to as cryptography. A digital signature, cryptographic data integrity, and authentication method all rely on the address of the receiver and the sender in addition to mathematical operations to find the signature. During the process of signature and verification, the solution that was presented is compared with the technique that is currently being used by ECDSA in order to illustrate the differences that exist between the two processes. This comprehensive survey of EC seeks to thoroughly investigate many scientific concepts, state-of-the-art, and innovative methodologies and implementations. This work will be useful for academics, who are interested in further analysis. Use and development of EC based schemes for cloud computing, e-health, and e-voting, is more secure as compared to RSA, and <PERSON><PERSON><PERSON><PERSON> schemes. In this comprehensive study, we claim that the adoption of EC methods in distributed computing and asynchronous networking provides significant benefits in distributed computing and interdependent networking.", "Keywords": "Elliptic Curves ; Elliptic Curve Cryptography ; Elliptic curve digital signature ; <PERSON><PERSON><PERSON><PERSON> key exchange protocol ; Bi-linearity ; Identity based encryption ; Attribute-based encryption ; Discrete Logarithm Problem", "DOI": "10.1016/j.cosrev.2022.100530", "PubYear": 2023, "Volume": "47", "Issue": "", "JournalId": 21523, "JournalTitle": "Computer Science Review", "ISSN": "1574-0137", "EISSN": "1876-7745", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Northwestern Polytechnical University, Xi’an, Shaanxi, 710072, PR China;Knowledge Units of Systems and Technology (KUST), University of Management and Technology (UMT), Sialkot, 51040, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Northwestern Polytechnical University, Xi’an, Shaanxi, 710072, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Chitral, Chitral, 17251, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, University of Management and Technology (UMT), Lahore, 54000, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Northwestern Polytechnical University, Xi’an, Shaanxi, 710072, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, University of Science and Technology of China (USTC), Hefei, Anhui, 230000, PR China"}], "References": [{"Title": "IMPLEMENTATION OF PROVABLYSECURE DIGITAL SIGNATURE SCHEME BASED ON ELLIPTIC CURVE", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "405", "JournalTitle": "Indian Journal of Computer Science and Engineering"}, {"Title": "Key Sequences based on Cyclic Elliptic Curves over\n GF \n (2\n <sup>8</sup>\n ) with Logistic Map for Cryptographic Applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "11", "Page": "e6849", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 105457504, "Title": "Lessons for the Future: Supporting Policy Makers’ Choice of Stringency-Index Level While Facing Pandemic Viruses using Machine Learning Techniques", "Abstract": "", "Keywords": "", "DOI": "10.5120/ijca2022922505", "PubYear": 2022, "Volume": "184", "Issue": "39", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105457510, "Title": "Development of a Smart Wireless System for Safety of Bike Riders", "Abstract": "", "Keywords": "", "DOI": "10.5120/ijca2022922499", "PubYear": 2022, "Volume": "184", "Issue": "39", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105457513, "Title": "Predicting Students' Performance in Final Examination using Deep Neural Network", "Abstract": "<p>The academic result is the most important thing in a student's career. This result depends on their academic performance and many other factors. Educational data mining can help both students and institutions develop their academic performance. For analysis of their performance, we can use new techniques Deep Learning, Convolution Neural Networks, Data Clustering, Optimization Algorithms, etc. In machine learning. Using Deep Learning, we will predict the student’s performance yearly in the form of CGPA and compare that with the real CGPA. A real dataset can boost the prediction performance. We used a real dataset from the Institute of Science, Trade & Technology (ISTT). We used a total of 18 data factors to predict the performance and the data factors are: Class Performance, Test Marks, Class Attendance, Due Time Assignment Submission, Lab Performance, Previous Semester Result, Family Education, Freelancer, Relationship with Faculty, Study Hours, Living Area, Social Media Attraction, Extra-Curricular Activity, Drug Addiction, Financial Support from Family, Political Involvement, Affair & Year Final Result.</p>", "Keywords": "", "DOI": "10.9734/ajrcos/2022/v14i4306", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 63540, "JournalTitle": "Asian Journal of Research in Computer Science", "ISSN": "", "EISSN": "2581-8260", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105457579, "Title": "FPGA sharing in the cloud: a comprehensive analysis", "Abstract": "<p>Cloud vendors are actively adopting FPGAs into their infrastructures for enhancing performance and efficiency. As cloud services continue to evolve, FPGA (field programmable gate array) systems would play an even important role in the future. In this context, FPGA sharing in multi-tenancy scenarios is crucial for the wide adoption of FPGA in the cloud. Recently, many works have been done towards effective FPGA sharing at different layers of the cloud computing stack.</p><p>In this work, we provide a comprehensive survey of recent works on FPGA sharing. We examine prior art from different aspects and encapsulate relevant proposals on a few key topics. On the one hand, we discuss representative papers on FPGA resource sharing schemes; on the other hand, we also summarize important SW/HW techniques that support effective sharing. Importantly, we further analyze the system design cost behind FPGA sharing. Finally, based on our survey, we identify key opportunities and challenges of FPGA sharing in future cloud scenarios.</p>", "Keywords": "cloud FPGA; FPGA sharing; efficiency; design cost", "DOI": "10.1007/s11704-022-2127-0", "PubYear": 2023, "Volume": "17", "Issue": "5", "JournalId": 4733, "JournalTitle": "Frontiers of Computer Science", "ISSN": "2095-2228", "EISSN": "2095-2236", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Guo", "Affiliation": "Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Micro/Nano Electronics, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai, China"}], "References": [{"Title": "Cloud Pricing Models", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "CPAmap: On the Complexity of Secure FPGA Virtualization, Multi-Tenancy, and Physical Design", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "121", "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems"}, {"Title": "Trust in FPGA-accelerated Cloud Computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Accelerating DNNs from local to virtualized FPGA in the Cloud: A survey of trends", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "119", "Issue": "", "Page": "102257", "JournalTitle": "Journal of Systems Architecture"}]}, {"ArticleId": 105457682, "Title": "Generating an Optimal Tour Plan with Optimization", "Abstract": "", "Keywords": "", "DOI": "10.5120/ijca2022922473", "PubYear": 2022, "Volume": "184", "Issue": "38", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105457693, "Title": "State estimation-based robust optimal control of influenza epidemics in an interactive human society", "Abstract": "This paper presents a state estimation-based robust optimal control strategy for influenza epidemics in an interactive human society in the presence of modeling uncertainties. Interactive society is influenced by random entrance of individuals from other human societies whose effects can be modeled as a non-Gaussian noise. Since only the number of exposed and infected humans can be measured, the states of the influenza epidemics are first estimated by an extended maximum correntropy Kalman filter (EMCKF) to provide a robust state estimation in the presence of the non-Gaussian noise. An online quadratic program (QP) optimization is then synthesized subject to a robust control Lyapunov function (RCLF) to minimize susceptible and infected humans, while minimizing and bounding the rates of vaccination and antiviral treatment. The main contribution of this work is twofold. First, the joint QP-RCLF-EMCKF strategy meets multiple design specifications such as state estimation, tracking, pointwise control optimality , and robustness to parameter uncertainty and state estimation errors that have not been achieved simultaneously in previous studies. Second, the uniform ultimate boundedness (UUB)/convergence of all error trajectories is guaranteed by using a Lyapunov stability argument. Simulation results show that the proposed approach achieves appropriate tracking and state estimation performance with good robustness.", "Keywords": "", "DOI": "10.1016/j.ins.2022.01.049", "PubYear": 2022, "Volume": "592", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Auburn University, AL 36849, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, San Jose State University, San Jose, CA 95192-0087, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Cleveland State University, Cleveland, OH 44115, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Engineering, Texas A&M University-Corpus Christi, Corpus Christi, TX 78412, USA;Department of Automation and Control Engineering Thuyloi University, Hanoi, Vietnam;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Modeling Evolutionary Algorithms Simulation and Artificial Intelligence, Faculty of Electrical & Electronics Engineering, Ton Duc Thang University, Ho Chi Minh City, Viet Nam"}], "References": [{"Title": "Particle filtering for a class of cyber-physical systems under Round-Robin protocol subject to randomly occurring deception attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "298", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105457863, "Title": "Optimal Placement of Multiple DG Units With Energy Storage in Radial Distribution System by Hybrid Techniques", "Abstract": "<p>In recent years, distributed generations (DGs) are extremely fast in detecting their location, which helps to satisfy the ever-increasing power demands. The placement of energy storage systems (ESSs) could be a substantial opportunity to enhance the presentation of radial distribution system (RDS). The major part of DG units in RDS deals with the detection of ideal placement and size of the DGs, which efficiently balance the power loss and voltage stability. The ideal location and size of ESSs are examined in standard IEEE-33 and 69 bus systems, which is important to reduce power losses. Nowadays, several algorithms or techniques are modified for the development of hybrid algorithms to improve the quality of DG allocation. In this research, a hybrid shuffled frog leap algorithm (SFLA) with ant lion optimizer (SFLA-ALO) is proposed for the optimal placement and size of the DG and ESS in the RDS to reduce power losses and maintain the stability of voltage. The performance of the proposed SFLA-ALO technique is compared with the implemented BPSO-SFLA technique.</p>", "Keywords": "", "DOI": "10.4018/IJSI.315736", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 29711, "JournalTitle": "International Journal of Software Innovation", "ISSN": "2166-7160", "EISSN": "2166-7179", "Authors": [{"AuthorId": 1, "Name": "Munisekhar P.", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105458029, "Title": "Nonlinear model identification and statistical verification using experimental data with a case study of the UR5 manipulator joint parameters", "Abstract": "<p>The identification of nonlinear terms existing in the dynamic model of real-world mechanical systems such as robotic manipulators is a challenging modeling problem. The main aim of this research is not only to identify the unknown parameters of the nonlinear terms but also to verify their existence in the model. Generally, if the structure of the model is provided, the parameters of the nonlinear terms can be identified using different numerical approaches or evolutionary algorithms. However, finding a non-zero coefficient does not guarantee the existence of the nonlinear term or vice versa. Therefore, in this study, a meticulous investigation and statistical verification are carried out to ensure the reliability of the identification process. First, the simulation data are generated using the white-box model of a direct current motor that includes some of the nonlinear terms. Second, the particle swarm optimization (PSO) algorithm is applied to identify the unknown parameters of the model among many possible configurations. Then, to evaluate the results of the algorithm, statistical hypothesis and confidence interval tests are implemented. Finally, the reliability of the PSO algorithm is investigated using experimental data acquired from the UR5 manipulator. To compare the results of the PSO algorithm, the nonlinear least squares errors (NLSE) estimation algorithm is applied to identify the unknown parameters of the nonlinear models. The result shows that the PSO algorithm has higher identification accuracy than the NLSE estimation algorithm, and the model with identified parameters using the PSO algorithm accurately calculates the output torques of the joints of the manipulator.</p>", "Keywords": "nonlinear model identification; hypothesis test; confidence interval test; particle swarm optimization; UR5 manipulator; nonlinear least square errors estimation", "DOI": "10.1017/S0263574722001783", "PubYear": 2023, "Volume": "41", "Issue": "4", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechatronics Engineering, Istanbul Technical University, Sariyer, Istanbul 34469, Turkey; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechatronics Engineering, Izmir University of Economics, Balcova, Izmir 35330, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Izmir Institute of Technology, Urla, Izmir 35433, Turkey"}], "References": [{"Title": "Neural Network Design for Manipulator Collision Detection Based Only on the Joint Position Sensors", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "10", "Page": "1737", "JournalTitle": "Robotica"}]}, {"ArticleId": 105458156, "Title": "Smart Contract-Based Secure Decentralized Smart Healthcare System", "Abstract": "<p>Social distancing has been imposed to prevent substantial transmission of the COVID-19 outbreak, which is presently a global public health issue. Medical healthcare providers rely on telemedicine to monitor their patients, particularly those with chronic conditions. However, telemedicine faces many implementation-related risks, including data breaches, access restrictions within the medical community, inaccurate diagnosis, fraud, etc. The authors propose a transparent, tamper-proof, distributed, decentralized smart healthcare system (DSHS) that uses blockchain-based smart contracts. The authors use an immutable modified Merkel tree structure to hold the transaction for viewing contracts on a public blockchain, updating patient health records (PHR), and exchanging PHR to all entities. It is verified by a performance evaluation based on the Ethereum platform. The simulation results show that the proposed system outperforms existing approaches by enhancing transparency, boosting efficiency, and reducing average latency in the system. The proposed system improves the functionality of the SHS environment.</p>", "Keywords": "", "DOI": "10.4018/ijsi.315742", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 29711, "JournalTitle": "International Journal of Software Innovation", "ISSN": "2166-7160", "EISSN": "2166-7179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105458205, "Title": "Semisupervised Surveillance Video Character Extraction and Recognition With Attentional Learning Multiframe Fusion", "Abstract": "<p>Character extraction in the video is very helpful to the understanding of the video content, especially the artificially superimposed characters such as time and place in the surveillance video. However, the performance of the existing algorithms does not meet the needs of application. Therefore, the authors improve semisupervised surveillance video character extraction and recognition with attentional learning multiframe feature fusion. First, the multiframe fusion strategy based on an attention mechanism is adopted to solve the target missing problem, and the Dense ASPP network is introduced to solve the character multiscale problem. Second, a character image denoising algorithm based on semisupervised fuzzy C-means clustering is proposed to isolate and extract clean binary character images. Finally, for some video characters that may involve privacy, traditional and deep learning-based video restoration algorithms are used for characteristic elimination.</p>", "Keywords": "", "DOI": "10.4018/IJDCF.315745", "PubYear": 2022, "Volume": "14", "Issue": "3", "JournalId": 21478, "JournalTitle": "International Journal of Digital Crime and Forensics", "ISSN": "1941-6210", "EISSN": "1941-6229", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Yongdong Li", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105458223, "Title": "Towards Better Detection of Fraud in Health Insurance Claims in Kenya: Use of Naïve Bayes Classification Algorithm", "Abstract": "<p>The extent, possibility, and complexity of the healthcare industry have attracted widespread fraud that has contributed to rising healthcare costs hence affecting patients’ health and negatively impacting the economy of many countries. Despite putting up various technologies and strategies to fight fraud such as planned, targeted audits, random audits, whistle-blowing, and biometric systems, fraud in claims has continued to be a challenge in most of the health insurance providers in Kenya. This paper explored the application of data mining in detecting fraud in health insurance claims in Kenya. Classification algorithms (Naïve Bayes, Decision Tree and K-Nearest Neighbour) were used to build predictive models for the knowledge discovery process. After conducting several experiments, the resulting models showed that the Naïve Bayes works well in detecting fraud in claims with 91.790% classification accuracy and 74.12% testing hit rate. A prototype was developed based on the rules extracted from the Naïve Bayes model, which, if adopted, will save costs by detecting fraud as it is committed. Fraud detection in health insurance claims is much needed in many countries so as to help reduce loss of money and in return improve service delivery to patients.</p>", "Keywords": "", "DOI": "10.37284/eajit.5.1.1023", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 73559, "JournalTitle": "East African Journal of Information Technology", "ISSN": "2707-5346", "EISSN": "2707-5354", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105458298, "Title": "Modeling opponent learning in multiagent repeated games", "Abstract": "Multiagent reinforcement learning (MARL) has been used extensively in the game environment. One of the main challenges in MARL is that the environment of the agent system is dynamic, and the other agents are also updating their strategies. Therefore, modeling the opponents’ learning process and adopting specific strategies to shape learning is an effective way to obtain better training results. Previous studies such as DRON, LOLA and SOS approximated the opponent’s learning process and gave effective applications. However, these studies modeled only transient changes in opponent strategies and lacked stability in the improvement of equilibrium efficiency. In this article, we design the MOL (modeling opponent learning) method based on the Stackelberg game. We use best response theory to approximate the opponents’ preferences for different actions and explore stable equilibrium with higher rewards. We find that MOL achieves better results in several games with classical structures (the <PERSON><PERSON>’s Dilemma, Stackelberg Leader game and Stag Hunt with 3 players), and in randomly generated bimatrix games. MOL performs well in competitive games played against different opponents and converges to stable points that score above the Nash equilibrium in repeated game environments. The results may provide a reference for the definition of equilibrium in multiagent reinforcement learning systems, and contribute to the design of learning objectives in MARL to avoid local disadvantageous equilibrium and improve general efficiency.", "Keywords": "Multiagent reinforcement learning; Repeated game; Opponent modeling", "DOI": "10.1007/s10489-022-04249-x", "PubYear": 2023, "Volume": "53", "Issue": "13", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Yudong Hu", "Affiliation": "School of Mathematical Sciences, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "Congying Han", "Affiliation": "School of Mathematical Sciences, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, University of Chinese Academy of Sciences, Beijing, China"}], "References": [{"Title": "Infrared head pose estimation with multi-scales feature fusion on the IRHP database for human attention recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "411", "Issue": "", "Page": "510", "JournalTitle": "Neurocomputing"}, {"Title": "Anisotropic angle distribution learning for head pose estimation and attention understanding in human-computer interaction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "433", "Issue": "", "Page": "310", "JournalTitle": "Neurocomputing"}, {"Title": "A survey on multi-agent deep reinforcement learning: from the perspective of challenges and applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "5", "Page": "3215", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Bottom-up multi-agent reinforcement learning by reward shaping for cooperative-competitive tasks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "7", "Page": "4434", "JournalTitle": "Applied Intelligence"}, {"Title": "Enhancing cooperation by cognition differences and consistent representation in multi-agent reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "9701", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 105458361, "Title": "Unsupervised Word Embedding with Ensemble Deep Learning for Twitter Rumor Identification", "Abstract": "In social networks, rumor identification is a major problem. The structural data in a topic is applied to derive useful attributes for rumor identification. Most standard rumor identification methods concentrate on local structural attributes, ignoring the global structural attributes that exist between the source tweet and its responses. To tackle this issue, a Source-Replies relation Graph (SR-graph) has been built to develop an Ensemble Graph Convolutional neural Net (EGCN) with a Nodes Proportion Allocation Mechanism (NPAM) which identifies the rumor. But, the word vectors were trained by the standard word-embedding model which does not increase the accuracy for large Twitter databases. To solve this problem, an unsupervised word-embedding method is needed for large Twitter corpora. As a result, the Twitter word-embedded EGCN (T-EGCN) model is proposed in this article, which uses unsupervised learning-based word embedding to find rumors in huge Twitter databases. Initially, the latent contextual semantic correlation and co-occurrence statistical attributes among words in tweets are extracted. Then, to create a rumor attribute vector of tweets, these word embeddings are concatenated with the GloVe model's word attribute vectors, Twitter-specific attributes, and n-gram attributes. Further, the EGCN is trained by using this attribute vector to identify rumors in a huge Twitter database. Finally, the testing results exhibit that the T-EGCN achieves 87.56% accuracy, whereas the RNN, GCN, PGNN, EGCN, and BiLSTM-CNN attain 65.38%, 68.41%, 75.04%, 81.87%, and 86.12%, respectively for rumor identification.", "Keywords": "EGCN; n-gram attributes; rumor identification; unsupervised learning; word-embedding", "DOI": "10.18280/ria.360515", "PubYear": 2022, "Volume": "36", "Issue": "5", "JournalId": 21796, "JournalTitle": "Revue d'intelligence artificielle", "ISSN": "0992-499X", "EISSN": "1958-5748", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Government Arts College, Coimbatore, 641018, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Government Arts College, Coimbatore, 641018, India"}], "References": []}, {"ArticleId": 105458395, "Title": "Prediction of SO2 Concentration Based on AR-LSTM Neural Network", "Abstract": "<p>Sulphur dioxide is one of the most common air pollutants, forming acid rain and other harmful substances in the atmosphere, which can further damage our ecosystem and cause respiratory diseases in humans. Therefore, it is essential to monitor the concentration of sulphur dioxide produced in industrial processes in real-time to predict the concentration of sulphur dioxide emissions in the next few hours or days and to control them in advance. To address this problem, we propose an AR-LSTM analytical forecasting model based on ARIMA and LSTM. Based on the sensor's time series data set, we preprocess the data set and then carry out the modeling and analysis work. We analyze and predict the proposed analysis and prediction model in two data sets and conduct comparative experiments with other comparison models based on the three evaluation indicators of R<sup>2</sup>, RMSE and MAE. The results demonstrated the effectiveness of the AR-LSTM analytical prediction model; Finally, a forecasting exercise was carried out for emissions in the coming weeks using our proposed AR-LSTM analytical forecasting model.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2022, Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "Combined prediction model;LSTM;Sulfur dioxide concentration;Time series prediction", "DOI": "10.1007/s11063-022-11119-7", "PubYear": 2023, "Volume": "55", "Issue": "5", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, 250358 China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>n <PERSON>", "Affiliation": "Huawei Technologies Co., Ltd., Shenzhen, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, 250358 China."}], "References": [{"Title": "A deep learning model to effectively capture mutation information in multivariate time series prediction", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106139", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A new parameter reduction algorithm for interval-valued fuzzy soft sets based on Pearson’s product moment coefficient", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Hongwu Qin", "PubYear": 2020, "Volume": "50", "Issue": "11", "Page": "3718", "JournalTitle": "Applied Intelligence"}, {"Title": "Dose Regulation Model of Norepinephrine Based on LSTM Network and Clustering Analysis in Sepsis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "717", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "A new correlation coefficient between categorical, ordinal and interval variables with Pearson characteristics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "107043", "JournalTitle": "Computational Statistics & Data Analysis"}]}, {"ArticleId": 105458435, "Title": "Algorithm-Oriented SIMD Computer Mathematical Model and Its Application: ", "Abstract": "<p>This paper has designed a professional and practical SIMD computer mathematical model based on the SIMD physical machine model combined with the variable addition method. Furthermore, the model is applied in image collection, processing, and display operations, and a SIMD data parallel image processing system is finally established by absorbing the parallel computing advantages of the mathematical model. In addition, the data-parallel image processing algorithm is introduced and the convolutional neural network algorithm is optimized to promote the significant improvement of the main performance such as the accuracy of the application system. The final experimental results have shown that the highest accuracy of the data-parallel image processing algorithm reaches 93.3% and the lowest error rate reaches 0.11%, which proves the superiority of the SIMD computer mathematical model in image processing applications.</p>", "Keywords": "", "DOI": "10.4018/IJICTE.315743", "PubYear": 2022, "Volume": "18", "Issue": "3", "JournalId": 22087, "JournalTitle": "International Journal of Information and Communication Technology Education", "ISSN": "1550-1876", "EISSN": "1550-1337", "Authors": [{"AuthorId": 1, "Name": "Yong<PERSON> Jiang", "Affiliation": ""}, {"AuthorId": 2, "Name": "Yuan Li", "Affiliation": ""}], "References": [{"Title": "Monitoring area coverage optimization algorithm based on nodes perceptual mathematical model in wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "155", "Issue": "", "Page": "227", "JournalTitle": "Computer Communications"}, {"Title": "ALBUS: A method for efficiently processing SpMV using SIMD and Load balancing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "371", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 105458462, "Title": "強化学習によるバイオリン演奏ロボットの動作生成: —割引率と探索率の弓運動への影響—", "Abstract": "We are building a system that can automatically determine the bowing and fingering parameters for our anthropomorphic robot to perform the violin. We adopt the reinforcement leaning with ε-greedy policy, in which a neural network is embedded as a value function. This paper presents simulation results for determining the bow speed and the bowing-direction under the constant bow-force condition. Effects of the discount factor γ and the exploring rate ε on the bowing motion are investigated, while the previous study focused only on the discount factor. Simulation results suggests that by choosing appropriate values for the parameters, we can obtain the bowing parameters with higher reward values than the previous report, which means that the robot can produce sounds close to the target.", "Keywords": "Violin-performance Robot;Reinforcement Learning;ε-greedy Policy;Value Function", "DOI": "10.7210/jrsj.40.924", "PubYear": 2022, "Volume": "40", "Issue": "10", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ryukoku University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ryukoku University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Ryukoku University"}], "References": []}, {"ArticleId": 105458541, "Title": "風リスクを考慮した自律ドローンの軌道計画に関する研究", "Abstract": "In recent years, the use of drones has been advancing in various fields. In particular, attention is focused on autonomous drones that do not require a driver. However, autonomous drones need to use external information acquired by sensors to control flight so that the drone does not crash or become incapable of performing missions. In drones, it is important to consider the effects of wind-induced attitude changes, especially crashes, as well as preventing approach and collision with structures. However, as far as the author knows, path planning considering the influence of wind has not been announced so far. Therefore, we propose a method for path planning that avoids windy areas that have a large effect on drones. In this study, it is assumed that the drone can measure the current position and the wind direction and speed at that point. Based on that information, Model Predictive Control (MPC) performs path planning that predicts the wind condition around the drone and avoids windy areas. In this paper, we explain the wind prediction model and the setting of wind constraints for MPC. We also conducted an autonomous flight simulation in a windy environment to verify the effectiveness of the proposed method.", "Keywords": "Model Predictive Control (MPC);Micro Air Vehicle;Wind Environment", "DOI": "10.7210/jrsj.40.915", "PubYear": 2022, "Volume": "40", "Issue": "10", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chiba University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chiba University"}], "References": []}, {"ArticleId": 105458556, "Title": "High Altitude Airship: A Review of Thermal Analyses and Design Approaches", "Abstract": "<p>In recent years, there has been an increasing interest in the research and development of high-altitude airships. These systems provide a suspended platform using buoyancy at 17–25 km altitude. They have an enormous yet untapped potential for telecommunication, broadcasting relays, regional navigation, scientific exploration, and many other potential applications in an affordable fashion. Consequently, a large volume of literature has emerged covering the various aspects of design and development. This paper presents a state-of-the-art review of research and development in high-altitude airships. It covers the critical area, e.g., worldwide involvement, shape optimization, thermal analysis, design studies, etc. Realistic prediction of thermal behavior is essential to determine its effect on energy generation and altitude control. The paper discusses the various thermal investigations and numerical modeling approaches to study thermal performance. The envelope is the largest entity and directly contributes to the drag, hence energy demand. The energy generated also depends on the layout of the solar array. Therefore, aerodynamic shape optimization and solar array layout optimization are also studied. The efficiency of the solar array degrades as the temperature rises. The thermal model must be coupled to the design technique to compensate for the power loss. Therefore, various design optimization methods are critically analyzed with and without thermal consideration. Thermal behavior has been captured well in the existing literature through analytic, numerical, and machine-learning techniques. However, more experimental investigations are needed to validate these studies under generic conditions. The developed design techniques can design and optimize the airships conceptually only. Therefore, a comprehensive method must be developed based on a robust MDO framework. The paper highlights the challenges in designing and developing these systems and research gaps for future investigations.</p>", "Keywords": "", "DOI": "10.1007/s11831-022-09867-9", "PubYear": 2023, "Volume": "30", "Issue": "3", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Konkuk Aerospace Design-Airworthiness Research Institute (KADA), Konkuk University, Seoul, Korea; Department of Aerospace Engineering, SMEC, VIT Bhopal University, Sehore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Aerospace Engineering Department, King Abdulaziz University, Jeddah, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering, King Fahd University of Petroleum & Minerals, Dhahran, Saudi Arabia; Center for Refining & Advanced Chemicals, King Fahd University of Petroleum & Minerals, Dhahran, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, King Fahd University of Petroleum & Minerals, Dhahran, Saudi Arabia; Interdisciplinary Research Center for Hydrogen and Energy Storage, King Fahd University of Petroleum and Minerals, Dhahran, Saudi Arabia"}], "References": []}, {"ArticleId": 105458744, "Title": "An Evaluation of Machine Learning Approaches to Integrate Historical Farm Data", "Abstract": "Large datasets in agriculture are increasingly available through yearly surveys. However, very few longitudinal datasets providing insights for farmer’s decision are available. The main objective in this research is to match farm establishments. The purposes of this investigation is two fold: first, to match successive yearly surveys, producing longitudinal records into farm history; and second to use only categorical and numerical features to match records. We analyzed Ecuadorian national agricultural surveys from the years 2010 to 2012. In total, 125098 records were compared, using 16 different algorithms. Our results suggest that with this particular data setup, unsupervised methods using a stochastic matching approach outperform other algorithms in terms of F1 scores. Matching individuals over three consecutive years shows that ensemble techniques allowed the re-identification of 60% of individuals. In the context of Ecuador, no data are available to follow individual farms over time, longitudinal datasets could provide essential insights for local policies. © 2022 University of Bologna. All rights reserved.", "Keywords": "Data Integration; Data matching; Entity Resolution; Farm Matching; Machine Learning; Record Linkage", "DOI": "10.22364/bjmc.2022.10.4.03", "PubYear": 2022, "Volume": "10", "Issue": "4", "JournalId": 39705, "JournalTitle": "Baltic Journal of Modern Computing", "ISSN": "2255-8942", "EISSN": "2255-8950", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Facultad de Ingeniería de Sistemas, Escuela Politécnica Nacional, Ladrón de Guevara E11·253 Edif 20, Quito, Ecuador"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Facultad de Ingeniería de Sistemas, Escuela Politécnica Nacional, Ladrón de Guevara E11·253 Edif 20, Quito, Ecuador"}], "References": []}, {"ArticleId": 105458752, "Title": "Bidirectional Long Short-Term Memory Networks for Automatic Crop Classification at Regional Scale using Tabular Remote Sensing Time Series", "Abstract": "With the arrival of European Union’s new Common Agricultural Policy (CAP 2020), a paradigm shift in subsidy control is underway. Member states are required to gradually transition from a system of on-the-spot checks, where the presence or absence of a crop is detected manually on the field, to a system of agricultural monitoring based on remote sensing data; primarily – Sentinel-1 and Sentinel-2. This paper presents a classification of regional crop types based on the Bidirectional Long-Short-Term Memory (BiLSTM) network. The approach is based on tabular time series of Sentinel-1 and Sentinel-2 sensor data over the entire territory of Latvia. Two types of LSTM architectures are evaluated in this paper – regular and bidirectional. An exhaustive grid search of network hyperparameters with 15 distinct crop types led to the conclusion that the bidirectional variant of LSTM yields the highest overall weighted test accuracy of 89.1%. © 2022 University of Bologna. All rights reserved.", "Keywords": "classification; crop type; LSTM; neural networks; remote sensing; satellite data; sentinel data; Sentinel-1; Sentinel-2", "DOI": "10.22364/bjmc.2022.10.4.02", "PubYear": 2022, "Volume": "10", "Issue": "4", "JournalId": 39705, "JournalTitle": "Baltic Journal of Modern Computing", "ISSN": "2255-8942", "EISSN": "2255-8950", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Latvia, Faculty of Geography and Earth Sciences, Riga, Latvia"}, {"AuthorId": 2, "Name": "Ēvalds U<PERSON>āns", "Affiliation": "Riga Technical University, Department of Artificial Intelligence and Systems Engineering, Riga, Latvia"}], "References": []}, {"ArticleId": 105458766, "Title": "先天性上肢欠損のための歯車機構を用いた幼児用義手の手先具の開発", "Abstract": "Early application of myoelectric prosthetic hand to children with congenital upper limb deficiency have been shown to be effective in acquiring familiarity with prosthetic hand and promoting movements using both hands. However, a robotic hand for prosthetic hand for infant is required to have a weight and size smaller than that of the infant's hand, also requires skill in the hand. These are in a trade-off relationship with each other. In addition, in order to reduce the cost of prosthetic hand and realize smooth application of prosthetic hand, a robot hand that can be used with a wide range of defects is required. In this study, we proposed a mechanism for a robotic hand of a myoelectric prosthetic hand for infants using a bevel gear and a DC motor. By using this mechanism, we have developed a lightweight and practical robotic hand that can be used with a wide range of defects. In addition, the gripping performance improved by searching for the shape of the finger parts of the robotic hand. Furthermore, we confirmed the effectiveness of the robotic hand by applying the developed robotic hand to infants with congenital upper limb defects.", "Keywords": "Myoelectric Prosthetic Hand;Robotic Hand;Lightweight;Bevel Gear;Infant", "DOI": "10.7210/jrsj.40.903", "PubYear": 2022, "Volume": "40", "Issue": "10", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 2, "Name": "<PERSON>chi <PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 7, "Name": "Shunta <PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Yokohama National University"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "National Center for Child Health and Development"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Brace On R Nagoya"}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}], "References": []}, {"ArticleId": 105458923, "Title": "多指ロボットハンドの転がり接触を考慮したセンサレス把持剛性制御", "Abstract": "This paper proposes a grasping stiffness control method using a multi-fingered robot hand. This method does not need any information of a grasped object, such as geometry and attitude. The grasping stiffness is derived from geometrical constraints including rolling contact between a multi-fingered robot hand and a grasped object. To estimate the grasping stiffness without the use of external sensors, a contact point position of each fingertip is estimated by a relative relationship between the initial contact position and a virtual object frame. The proposed method is designed so that the desired grasping stiffness optimally transforms into a joint stiffness, and the desired joint stiffness is realized through each joint angle control. Finally, the usefulness of the proposed method is demonstrated through several numerical simulation results.", "Keywords": "Multi-fingered Robotic Hand;<PERSON>ras<PERSON>iff<PERSON>;Rolling Constraint", "DOI": "10.7210/jrsj.40.928", "PubYear": 2022, "Volume": "40", "Issue": "10", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kyushu Univercity"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Kyushu Univercity"}], "References": []}, {"ArticleId": 105458976, "Title": "Comparison of recent metaheuristic optimization algorithms to solve the SHE optimization problem in MLI", "Abstract": "<p>Multilevel inverters (MLIs) are one of the most popular topics of power electronics. Selective harmonic elimination (SHE) method is used to eliminate low-order harmonics in the MLI output voltage by determining the optimum switching angles. It includes the solution of nonlinear sets of transcendental equations. The optimization becomes more difficult as the number of levels in MLIs increases. Therefore, various metaheuristic algorithms have emerged toward obtaining optimal solutions to find the switching angles in the SHE problem in the last decade. In this study, a number of recent metaheuristics, such as ant lion optimization (ALO), hummingbird algorithm (AHA), dragonfly algorithm (DA), harris hawk optimization, moth flame optimizer (MFO), sine cosine algorithm (SCA), flow direction algorithm (FDA), equilibrium optimizer (EO), atom search optimization, artificial electric field algorithm and arithmetic optimization algorithm (AOA), are employed as an attempt to find the best optimization framework to identify switching moments in 11-level MLI. Marine predator algorithm (MPA), whale optimization algorithm (WOA), grey wolf optimizer (GWO), particle swarm optimization (PSO), multiverse optimizer (MVO), teaching–learning-based optimization (TLBO), and genetic algorithm (GA), which are widely used in solving this problem, are selected for performance analysis. AHA, ALO, AOA, DA, EO, FDA, GA, GWO, MFO, MPA, MVO, PSO, SCA, SSA, TLBO and WOA methods meet maximum 8% THD requirement specified in IEEE 519 standard in the range of 0.4–0.9 modulation index. Simulation results show that MFO is superior other algorithms in terms of THD minimization, convergence rate, a single iteration time and robustness.</p>", "Keywords": "Metaheuristic optimization algorithm; Multilevel inverter (MLI); Selective harmonic elimination (SHE); Total harmonic distortion (THD)", "DOI": "10.1007/s00521-022-07980-1", "PubYear": 2023, "Volume": "35", "Issue": "10", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Systems Engineering, Kocaeli University, Kocaeli, Turkey"}, {"AuthorId": 2, "Name": "Satılmış Ürgün", "Affiliation": "Faculty of Aeronautics and Astronautics, Kocaeli University, Kocaeli, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Artificial Intelligence Research and Optimization, Torrens University Australia, Brisbane, Australia; Yonsei Frontier Lab, Yonsei University, Seoul, Republic of Korea"}], "References": [{"Title": "An efficient and robust grey wolf optimizer algorithm for large-scale numerical optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "2", "Page": "997", "JournalTitle": "Soft Computing"}, {"Title": "Salp swarm algorithm: a comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "11195", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Multi-verse optimizer algorithm: a comprehensive survey of its results, variants, and applications", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12381", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Evolutionary algorithms and their applications to engineering problems", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12363", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Dragonfly algorithm: a comprehensive review and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "21", "Page": "16625", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Complex-valued encoding metaheuristic optimization algorithm: A comprehensive survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "407", "Issue": "", "Page": "313", "JournalTitle": "Neurocomputing"}, {"Title": "Multi-verse optimization algorithm- and salp swarm optimization algorithm-based optimization of multilevel inverters", "Authors": "Oğuz<PERSON> Ceylan", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "1935", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Advances in Sine Cosine Algorithm: A comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "4", "Page": "2567", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Flow Direction Algorithm (FDA): A Novel Optimization Approach for Solving Optimization Problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "156", "Issue": "", "Page": "107224", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Development and application of equilibrium optimizer for optimal power flow calculation of power system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "6", "Page": "7232", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 105458990, "Title": "A Study into Elevator Passenger In-Cabin Behaviour on a Smart-Elevator Platform", "Abstract": "With the advancement of technology, elevators have evolved into complex systems transporting people and goods between floors. The trend is towards smart elevators, adding not only value to services provided but enabling capabilities to study passenger behaviour more closely. Have you ever wondered why some people always choose the same spot to stand in an elevator cabin, or what is your favourite location to stand in an elevator? In this study, we use a smart elevator platform to explore elevator passengers’ in-cabin behaviour. We develop a location analysis model, passenger behaviour evaluation method, and analyse the data of real elevator passengers. We show that elevator passengers have their favourite spots in elevators to stand in, and common movement patterns they follow, which depend on the operational context. In general, the results we obtain are applicable to any elevator type, and beyond for several use cases of cyber-physical social systems. © 2022 University of Bologna. All rights reserved.", "Keywords": "human behaviour; moving patterns; Smart-elevator; Socio-Cyber-Physical Systems", "DOI": "10.22364/bjmc.2022.10.4.05", "PubYear": 2022, "Volume": "10", "Issue": "4", "JournalId": 39705, "JournalTitle": "Baltic Journal of Modern Computing", "ISSN": "2255-8942", "EISSN": "2255-8950", "Authors": [{"AuthorId": 1, "Name": "Tarm<PERSON>", "Affiliation": "Tallinn University of Technology, Tallinn, 12618, Estonia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tallinn University of Technology, Tallinn, 12618, Estonia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tallinn University of Technology, Tallinn, 12618, Estonia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Tallinn University of Technology, Tallinn, 12618, Estonia"}], "References": []}, {"ArticleId": 105458991, "Title": "DiagVol: Multi-block Bézier Volume Modeling from Prescribed Diagonal Surface Pairs", "Abstract": "In this paper, we present a novel method called DiagVol to construct C 1 continuous multi-block Bézier volumes from N pairs of prescribed diagonal surfaces. The explicit expression of the diagonal surface pair (DSP) is first formulated, and the necessary and sufficient conditions for the pair of diagonal surfaces which can be the DSP of a Bézier volume are derived. The conditions for the DSPs with C 1 continuity of the adjacent blocks are also presented. We can obtain new multi-block volume shapes by modifying the first block and the DSPs of other blocks, and volume morphing and deformation applications are given to show the modeling flexibility of DiagVol. Moreover, an approach to construct energy-minimizing single-block from the prescribed DSP is presented by introducing the Dirichlet energy and strain energy, which is further extended for the construction of multi-block Bézier volumes with C 1 continuity. Experimental results are given to illustrate the modeling flexibility and simulation effects of the presented approach.", "Keywords": "Volume modeling ; Diagonal surface pair ; Energy minimization ; Multi-block volumes ; Isogeometric analysis", "DOI": "10.1016/j.cad.2022.103464", "PubYear": 2023, "Volume": "156", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, 310018, China"}, {"AuthorId": 2, "Name": "Qinghua Hu", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, 310018, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, 310018, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, 310018, China;Institute of Computational Aerodynamics, China Aerodynamics Researching and Development Center, Mianyang, 621000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, 310018, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computational Aerodynamics, China Aerodynamics Researching and Development Center, Mianyang, 621000, China"}], "References": []}, {"ArticleId": 105458993, "Title": "Surveying 3D Data as Basis of a HBIM for the Management of Cultural Heritage Objects", "Abstract": "Like all areas of life, science is currently in a phase of upheaval in which we are learning to make use of the advantages of digitalisation. The most important aspects of this digital turn are the acquisition of information and the development of problem-related data collections that help us to document and analyse problems and developments, but also to predict them. In analogy to other research disciplines (e. g. geography), the creation of a database-supported information system is obvious. Based on these considerations, the present case will use Hanfelden Castle as an example to show how the integrative use of selected methods from the field of geospatial technologies can be used to generate integrated 3D geodata intended as a fundamental work for the future development of a multi-disciplinary Building Information System for the management of historical buildings (HBIM). © 2022 University of Bologna. All rights reserved.", "Keywords": "Archaeology; Hanfelden Castle (Austria); HBIM; Structure from Motion; Terrestrial Laser Scanning; UAV Mapping", "DOI": "10.22364/bjmc.2022.10.4.10", "PubYear": 2022, "Volume": "10", "Issue": "4", "JournalId": 39705, "JournalTitle": "Baltic Journal of Modern Computing", "ISSN": "2255-8942", "EISSN": "2255-8950", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Graz, Faculty of Environmental, Regional and Educational Sciences, Department of Geography and Regional Science, Graz, Austria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Graz, Faculty of Environmental, Regional and Educational Sciences, Department of Geography and Regional Science, Graz, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Graz, Faculty of Environmental, Regional and Educational Sciences, Department of Geography and Regional Science, Graz, Austria"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Graz, Faculty of Environmental, Regional and Educational Sciences, Department of Geography and Regional Science, Graz, Austria"}], "References": []}, {"ArticleId": 105459040, "Title": "Nonlinear analysis of shell structures using image processing and machine learning", "Abstract": "In this paper, we propose a novel approach to solve nonlinear stress analysis problems in shell structures using an image processing technique. In general, such problems in design optimisation or virtual reality applications must be solved repetitively in a short period using direct methods such as nonlinear finite element analysis. Hence, obtaining solutions in real-time using direct methods can quickly become computationally overwhelming. The proposed method in this paper is unique in that it converts the mechanical behaviour of shell structures into images that are then used to train a machine learning algorithm. This is achieved by mapping shell deformations and stresses to a set of images that are used to train a conditional generative adversarial network. The network can then predict the solution of the problem for a varying range of parameters. The proposed approach can be significantly more efficient than training a machine learning algorithm using the raw numerical data. To evaluate the proposed method, two different structures are assessed where the training data is created using nonlinear finite element analysis. Each structure is studied for a varying geometry and a set of material properties. We show that the results of the trained network agree well with the results of the nonlinear finite element analysis. The proposed approach can quickly and accurately predict the mechanical behaviour of the structure using a fraction of the computational cost. All created data and source codes are openly available.", "Keywords": "Convolutional neural networks ; Nonlinear finite element analysis ; Shell structures ; Stress prediction", "DOI": "10.1016/j.advengsoft.2022.103392", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Energy, Geoscience, Infrastructure and Society, Institute for Infrastructure & Environment, Heriot-Watt University, Edinburgh, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical & Industrial Engineering, College of Engineering, Qatar University, Doha, Qatar;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Energy, Geoscience, Infrastructure and Society, Institute for Infrastructure & Environment, Heriot-Watt University, Edinburgh, United Kingdom"}], "References": [{"Title": "Finite element simulation of physical systems in augmented reality", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102902", "JournalTitle": "Advances in Engineering Software"}, {"Title": "Estimating model inadequacy in ordinary differential equations with physics-informed neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "245", "Issue": "", "Page": "106458", "JournalTitle": "Computers & Structures"}, {"Title": "Finite element network analysis: A machine learning based computational framework for the simulation of physical systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "247", "Issue": "", "Page": "106484", "JournalTitle": "Computers & Structures"}, {"Title": "A physics-informed machine learning approach for solving heat transfer equation in advanced manufacturing and engineering applications", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104232", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Deep learning driven real time topology optimisation based on initial stress learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "", "Page": "101472", "JournalTitle": "Advanced Engineering Informatics"}]}]