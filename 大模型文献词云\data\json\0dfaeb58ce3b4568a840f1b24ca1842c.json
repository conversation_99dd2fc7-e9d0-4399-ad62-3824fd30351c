[{"ArticleId": 78321237, "Title": "Logic-based benders decomposition algorithm for contamination detection problem in water networks", "Abstract": "To prevent the serious hazards caused by the intrusion of contaminants into the water distribution network, equipping the network with monitoring sensors is necessary. In this regard, the identification criterion is an important and recently addressed issue indicating that the sensors should be located so that in the case of intrusion of contamination, not only at least one sensor responds, but also it is possible to identify the source of contamination intrusion, as well. This paper addresses the sensor location problem with the identification criterion assuming that a limited budget is available for the sensor placement, and the aim is to minimize the number of vulnerable nodes having the same alarm pattern. First, the problem is formulated as a bi-objective mixed-integer linear programming model, assuming that the objective functions are ordered based on a given prioritization. Then, by utilizing the underlying problem structure, an exact logic-based Benders decomposition algorithm is presented. Computational results over moderate and large-sized instances confirm the efficiency of the proposed algorithm.", "Keywords": "Sensor placement ; Water distribution network ; Observation and identification criteria ; Benders decomposition ; Logic-based cuts", "DOI": "10.1016/j.cor.2019.104840", "PubYear": 2020, "Volume": "115", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Amirkabir University of Technology (Tehran Polytechnic), Tehran, Iran;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Amirkabir University of Technology (Tehran Polytechnic), Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Amirkabir University of Technology (Tehran Polytechnic), Tehran, Iran"}], "References": []}, {"ArticleId": 78321241, "Title": "A non-cooperative rear-end collision avoidance scheme for non-connected and heterogeneous environment", "Abstract": "Rear end collisions are deadliest in nature and result in life losses and severe injuries. Many of the proposed rear-end collision avoidance schemes focus only on specific aspects without considering several non-direct factors, such as driver inattention or distraction, tailgating, panic stops, street surface circumstances, driver response time, person on foot stream and vehicle elements. Henceforth presenting greater challenges in proposing a precise numerical model of the vehicle control framework. Moreover, most collision avoidance schemes incorporate fuzzy logic for formulating human inspired collision avoidance controllers. But the problem with a fuzzy based controller is that their efficiency is greatly affected by the number of rules being deployed. Furthermore, no such agent-based modeling has been utilized, which can effectively model the human-inspired behavior. Literature is full of vehicle control frameworks that work in a connected and homogeneous environment, again critics have raised several questions for such frameworks working in a connected environment. This study aims not only to identify these key issues but also propose a human-inspired physics based Spiral Spring based Simple Reflex Agent for rear end collision avoidance. Our scheme will not only work in the non-connected and heterogeneous environment but will also consider the distraction causing road factors and effectively handle them. Extensive experimentations and presentation for the proof of concept have supported our claims and worthiness of proposed agent-based scheme.", "Keywords": "Autonomous vehicles ; Agent-based modeling ; Collision avoidance ; Driver distraction ; Spiral spring", "DOI": "10.1016/j.comcom.2019.11.002", "PubYear": 2020, "Volume": "150", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mirpur University of Science and Technology, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> R<PERSON>z", "Affiliation": "Iqra University, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Textile University, Faisalabad, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bahria University, Islamabad, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Al Ain University, United Arab Emirates;xAnalytics, Ottawa, ON, Canada;Corresponding author at: xAnalytics, Ottawa, ON, Canada"}], "References": []}, {"ArticleId": 78321254, "Title": "An Entropic Optimal Transport loss for learning deep neural networks under label noise in remote sensing images", "Abstract": "Deep neural networks have established as a powerful tool for large scale supervised classification tasks. The state-of-the-art performances of deep neural networks are conditioned to the availability of large number of accurately labeled samples. In practice, collecting large scale accurately labeled datasets is a challenging and tedious task in most scenarios of remote sensing image analysis, thus cheap surrogate procedures are employed to label the dataset. Training deep neural networks on such datasets with inaccurate labels easily overfits to the noisy training labels and degrades the performance of the classification tasks drastically. To mitigate this effect, we propose an original solution with entropic optimal transportation. It allows to learn in an end-to-end fashion deep neural networks that are, to some extent, robust to inaccurately labeled samples. We empirically demonstrate on several remote sensing datasets, where both scene and pixel-based hyperspectral images are considered for classification. Our method proves to be highly tolerant to significant amounts of label noise and achieves favorable results against state-of-the-art methods.", "Keywords": "Optimal transport ; Entropic Optimal Transport ; Robust deep learning ; Noisy labels ; Remote sensing", "DOI": "10.1016/j.cviu.2019.102863", "PubYear": 2020, "Volume": "191", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université de Bretagne Sud, IRISA, UMR 6074, CNRS, Vannes 56000, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université Côte d’Azur, Lagrange, UMR 7293, CNRS, OCA, Nice 06108, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kyoto University, Japan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Université de Bretagne Sud, IRISA, UMR 6074, CNRS, Vannes 56000, France"}], "References": []}, {"ArticleId": 78321278, "Title": "Fuzzy granularity neighborhood extreme clustering", "Abstract": "Clustering is an important method for data analysis. Up to now, how to develop an efficient clustering algorithm is still a critical issue. Unsupervised extreme learning machine is an effective neural network learning method which has a fast training speed. In this paper, a fuzzy granularity neighborhood extreme clustering algorithm which is based on extreme learning machine is proposed. We use fuzzy neighborhood rough set to develop a new feature selection method to eliminate redundant attributes and introduce the adaptive adjustment mechanism to solve the parameters of unsupervised extreme learning machine. Different from the existing clustering algorithms, the proposed algorithm can obtain a clustering result with minimum intra-cluster distance and maximum inter-cluster distance. The proposed algorithm and comparison algorithms are executed on the synthetic data sets and real data sets. The experimental results show that the proposed algorithm outperforms the comparison algorithms on the most data sets and the proposed algorithm is effective for clustering task.", "Keywords": "Extreme learning machine ; Neighborhood rough set ; Cluster analysis ; Granular computing ; Fuzzy set", "DOI": "10.1016/j.neucom.2019.10.108", "PubYear": 2020, "Volume": "379", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronic Information and Electrical Engineering, Dalian University of Technology, Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Innovation and Entrepreneurship, Dalian University of Technology, Dalian, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Innovation and Entrepreneurship, Dalian University of Technology, Dalian, China;Corresponding author"}], "References": []}, {"ArticleId": 78321349, "Title": "Effects of peer assessment within the context of spherical video-based virtual reality on EFL students’ English-Speaking performance and learning perceptions", "Abstract": "English has been recognized as a means of communication around the globe. However, owing to the lack of realistic English practicing contexts, EFL (English as Foreign Language) students generally have few opportunities to communicate with people in English, not to mention to get feedback from others for making reflections. In this study, a spherical video-based virtual reality (SVVR) environment was developed to situate students in authentic English-speaking contexts; moreover, the peer assessment (PA) strategy was employed for guiding students to provide comments on peers&#x27; speaking performance and to make reflections on their own performance. To evaluate the effectiveness of the proposed approach, an experiment was conducted in a high school English course. The experiment results reveal more positive effects of the peer-assessment-based SVVR approach compared with the non-peer-assessment-based SVVR approach in terms of the learners&#x27; English speaking, learning motivation, and critical thinking skills, as well as reducing their English learning anxiety. Moreover, it was found that the ratings of the students were statistically correlated with those of the teacher. This study further analyzed the types of peer comments by categorizing them into four types: Praise, Criticism, Opinion, and Irrelevant. It was found that Praise feedback was helpful for the students&#x27; English-speaking performance, while Criticism feedback might have been unfavorable in this case. Additionally, Irrelevant feedback was not significantly correlated with the students’ performance in the earlier PA stage, but had a significantly negative correlation in the later stage.", "Keywords": "Applications in subject areas ; Interactive learning environments ; Pedagogical issues ; Teaching/learning strategies", "DOI": "10.1016/j.compedu.2019.103751", "PubYear": 2020, "Volume": "146", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Applied Science and Technology, National Taiwan University of Science and Technology, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Digital Learning and Education, National Taiwan University of Science and Technology, Taiwan;Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Curriculum and Instruction and Centre for Learning Sciences and Technologies, The Chinese University of Hong Kong, China"}], "References": []}, {"ArticleId": 78321727, "Title": "A reliable and efficient micro-protocol for data transmission over an RTP-based covert channel", "Abstract": "<p>As the VoIP steganographic methods provide a low capacity covert channel for data transmission, an efficient and real-time data transmission protocol over this channel is required which provides reliability with minimum bandwidth usage. This paper proposes a micro-protocol for data embedding over covert storage channels or covert hybrid channels developed by steganographic methods where real-time transport protocol (RTP) is their underlying protocol. This micro-protocol applies an improved Go-Back-N mechanism which exploits some RTP header fields and error correction codes to retain maximum covert channel capacity while providing reliability. The bandwidth usage and the performance of the proposed micro-protocol are analyzed. The analyses indicate that the performance depends on the network conditions, the underlying steganographic method, the error correction code and the adjustable parameters of the micro-protocol. Therefore, a genetic algorithm is devised to obtain the optimal values of the adjustable micro-protocol parameters. The impact of network conditions, the underlying steganographic method and the error correction code on the performance are assessed through simulations. The performance of this micro-protocol is compared to an existing method named ReLACK where this micro-protocol outperforms its counterpart.</p>", "Keywords": "VoIP steganography; Reliability; Data embedding micro-protocol; Covert channel; Real-time protocol (RTP)", "DOI": "10.1007/s00530-019-00636-6", "PubYear": 2020, "Volume": "26", "Issue": "2", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology Engineering, University of Isfahan, Isfahan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology Engineering, University of Isfahan, Isfahan, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> Gha<PERSON>farokhi", "Affiliation": "Department of Information Technology Engineering, University of Isfahan, Isfahan, Iran"}], "References": []}, {"ArticleId": 78321972, "Title": "A data-driven approach to probabilistic impedance control for humanoid robots", "Abstract": "This paper presents a novel approach toward synthesizing whole-body motions from visual perception and reaction force for a humanoid robot that maintains a suitable physical interaction with an environment. A behavior containing a whole-body motion, reaction force, and visual perception is encoded into a probabilistic model referred to as a “motion symbol”. The humanoid robot selects a motion symbol appropriate to the current situation and computes references for joint angles and reaction forces according to the selected symbol. The robot subsequently modifies these references to satisfy a desired impedance relating the robot whole-body positions and forces. This computation builds visual and physical feedback loops with knowledge about the behaviors, making it possible for a humanoid robot to not only perform human-like motion behaviors similar to training behaviors, but to also physically adapt to the immediate environment. We applies this proposed framework only to controlling the upper-body motion for a humanoid robot. Experiments demonstrate that the proposed method allows a humanoid robot to control its upper-body motion in response to visual perception and reaction forces acting on its hands to achieve five tasks while controlling its lower-body motion for its balance.", "Keywords": "Impedance control ; Probabilistic model ; Humanoid robot", "DOI": "10.1016/j.robot.2019.103353", "PubYear": 2020, "Volume": "124", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Mathematical Modeling and Data Science, 1-3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Toyonaka, Osaka, 560-8531, Japan;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechano-Informatics, University of Tokyo, 7-3-1, Hongo, Bunkyoku, Tokyo, 113-8656, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Frontier Research Center, Toyota Motor Corporation, 1, Toyotacho, Toyota, Aichi, 471-8571, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Frontier Research Center, Toyota Motor Corporation, 1, Toyotacho, Toyota, Aichi, 471-8571, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechano-Informatics, University of Tokyo, 7-3-1, Hongo, Bunkyoku, Tokyo, 113-8656, Japan"}], "References": []}, {"ArticleId": 78322025, "Title": "A discretely adaptive connection logic network", "Abstract": "This paper proposes a new model for a theoretical neural network that can be used as a guide for the design of future (quantum or optical) computational technologies. The model utilizes a uniformly connected nodal structure, where the connections are discretely adaptive and the nodes act as simple gatekeepers. The model replicates all known logics used in current electronics, such as AND, OR, XNOR, XOR, NOR, XNOR, NAND and NOT. Additionally, by using recurrent negating connections the model easily creates XOR gates, and adds novel sided and favoured gates. This model also facilitates the creation of ternary to n-ary gates, and it simplifies the creation of a number of majority functions (especially for an odd number of inputs). Also, as a multi-layered neural network, the model allows learning back propagation through the use of its negating connections. Finally, because of its adaptive connections, parts of the network can be used as internal memory. Overall, the model provides backward compatibility to existing CMOS circuitry, while opening up a number of new logics and architectures for neural computing.", "Keywords": "Neural network ; Logic gate ; Adaptive network ; Discrete weights ; Threshold functions ; Multi-valued logic ; CMOS complementary metal–oxide–semiconductor ; CPU central processing unit ; BAM bidirectional associative memories ; CNN convolutional neural networks ; DACL discretely adaptive or configurable connection logic ; FPGA Field Programmable Gate Array ; LSTM Long Short Term Memory ; MAJ3 Majority function with three inputs ; MCP The 1943 <PERSON> and <PERSON> proposed neural network ; MLP Multi-layer perceptron ; RNN Recurrent Neural Networks ; SNOT Sided NOT ; SOR Sided OR ; SXNOR Sided XNOR gate ; SXOR Sided XOR", "DOI": "10.1016/j.neucom.2019.10.099", "PubYear": 2020, "Volume": "380", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing and Information Technology, Unitec Institute of Technology, New Zealand"}], "References": []}, {"ArticleId": 78322059, "Title": "TCP-based traceroute: An evaluation of different probing methods", "Abstract": "<p>Traceroute is the most commonly used tool, not only for network diagnostics, but also for discovering the topology of the Internet. We evaluated the discovery capability of three variations of TCP‐based traceroute: the first uses SYN segments as probes, the other two operate on an existing connection and use DATA and ACK segments as probes. Experimental results show that using different types of probes is useful to obtain a richer view of the Internet, both in terms of IP interfaces and autonomous systems.</p>", "Keywords": "internet measurements;internet topology;TCP;traceroute", "DOI": "10.1002/itl2.134", "PubYear": 2020, "Volume": "3", "Issue": "1", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Florence, Florence, Italy; Dipartimento di Ingegneria dell'Informazione, University of Pisa, Pisa, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Istituto di Informatica e Telematica, IIT, CNR, Pisa, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dipartimento di Ingegneria dell'Informazione, University of Pisa, Pisa, Italy"}], "References": []}, {"ArticleId": 78322129, "Title": "A reversible water marking algorithm for multimedia images using two-dimensional non-causal prediction and ESPVD", "Abstract": "<p>Reversible image watermarking algorithm is an important branch of information hiding, which can protect the integrity of data. Therefore, it is of great practical significance and practical value to study the reversible image water marking algorithm. A multimedia image watermarking algorithm based on two-dimensional non-causal prediction and edge based sorted pixel value difference (ESPVD) is proposed in this paper, which is used to protect the security of multimedia information. Firstly, the optimum prediction coefficients in the horizontal and vertical directions of image are calculated. Then, two-dimensional non-causal prediction of image is carried out according to raster scanning sequence, and prediction pixels and prediction errors are calculated. Finally, the morphological edge (ME) operator is used to identify edge pixel positions, and the ESPVD technology is used to embed the watermarking information. The experimental results show that the proposed algorithm has better performance than those of other image watermarking algorithms under the same embedding ability.</p>", "Keywords": "Multimedia images; Reversible water marking algorithm; Morphological edge (ME); Two-dimensional non-causal prediction; Edge based sorted pixel value difference (ESPVD)", "DOI": "10.1007/s11042-019-08219-3", "PubYear": 2020, "Volume": "79", "Issue": "3-4", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Zhoukou Normal University, Zhoukou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Zhoukou Normal University, Zhoukou, China"}], "References": []}, {"ArticleId": 78322241, "Title": "Influence of Cd on structure, surface morphology, optical and electrical properties of nano crystalline ZnS films", "Abstract": "Transparent nano crystalline Zn<sub>1-x</sub>Cd<sub>x</sub>S films were grown on glass substrates at 0.0125 M precursor concentration using chemical spray pyrolysis technique. The suitable optimized temperatures were adjusted from 673 K to 573 K for different compositions. The films with x< 0.40 have exhibited cubic phase with the crystalline plane of (1 1 1) orientation and for x> 0.30 have shown a hexagonal phase with (0 0 2) prominent plane. The lattice constants, crystallite size and dislocation density in the samples were estimated using X-ray diffraction data. The morphology of the films with higher Zn content exhibits fibrous structure and it changes to grain like structure on decreasing Cd concentration. XPS and EDAX analysis confirms the presence of elements in the deposits. The optical transmittance and energy band gap increased with increasing deposition temperature, whereas, decreased with increasing Cd dopant. The NLO behavior of the deposits indicates free carrier absorption induced two photon absorption processes. All deposits have shown n-type electrical conductivity. The film resistivity decreases with increase in deposition temperature and Cd content. The obtained Zn<sub>1-x</sub>Cd<sub>x</sub>S films are suitable for device application of gas sensor and optical limiter.", "Keywords": "Nano crystallites ; Cd-ZnS films ; X-ray photoelectron spectroscopy (XPS) ; Z-scan ; Resistivity", "DOI": "10.1016/j.sna.2019.111719", "PubYear": 2020, "Volume": "303", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal, 576104, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal, 576104, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal, 576104, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "UGC-DAE Consortium for Scientiﬁc Research, University Campus, Khandwa Road, Indore, 452017, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal, 576104, India;Corresponding author"}], "References": []}, {"ArticleId": 78322620, "Title": "The evolution of contact prediction: evidence that contact selection in statistical contact prediction is changing", "Abstract": "Abstract Motivation <p>Over the last few years, the field of protein structure prediction has been transformed by increasingly-accurate contact prediction software. These methods are based on the detection of coevolutionary relationships between residues from multiple sequence alignments. However, despite speculation, there is little evidence of a link between contact prediction and the physico-chemical interactions which drive amino-acid coevolution. Furthermore, existing protocols predict only a fraction of all protein contacts and it is not clear why some contacts are favoured over others. Using a dataset of 863 protein domains, we assessed the physico-chemical interactions of contacts predicted by CCMpred, MetaPSICOV, and DNCON2, as examples of direct coupling analysis, meta-prediction, and deep learning.</p> Results <p>We considered correctly-predicted contacts and compared their properties against the protein contacts that were not predicted. Predicted contacts tend to form more bonds than non-predicted contacts, which suggests these contacts may be more important than contacts that were not predicted. Comparing the contacts predicted by each method, we found that metaPSICOV and DNCON2 favour accuracy whereas CCMPred detects contacts with more bonds. This suggests that the push for higher accuracy may lead to a loss of physico-chemically important contacts. These results underscore the connection between protein physico-chemistry and the coevolutionary couplings that can be derived from multiple sequence alignments. This relationship is likely to be relevant to protein structure prediction and functional analysis of protein structure and may be key to understanding their utility for different problems in structural biology.</p> Availability <p>We use publicly-available databases. Our code is available for download at http://opig.stats.ox.ac.uk/.</p> Supplementary information <p>Supplementary information is available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btz816", "PubYear": 2020, "Volume": "36", "Issue": "6", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Statistics, University of Oxford, Oxford, OX1 3LB, UK"}, {"AuthorId": 2, "Name": "Saulo H <PERSON>", "Affiliation": "SLAC National Accelerator Laboratory, Stanford University, Menlo Park, California, USA;Department of Bioengineering, Stanford University, Menlo Park, California, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "NaturalAntibody, Hamburg, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Statistics, University of Oxford, Oxford, OX1 3LB, UK"}], "References": []}, {"ArticleId": 78322831, "Title": "Consensus-based aggregation for identification and ranking of top-k influential nodes", "Abstract": "<p>Technology has continuously been a crucially influenced and acutely tangled with the progress of society. Online Social Networks (OSN) are interesting and valuable datasets that can be leveraged to improve understanding about society and to know inter-personal choices. Identification and Ranking of Influential Nodes (IRIN) is non-trivial task for real time OSN like Twitter which accustom with ever-changing network, demographics and contents having heterogeneous features such as Tweets, Likes, Mentions and Retweets. Existing techniques such as Centrality Measures and Influence Maximization ignores vital information available on OSN, which are inappropriate for IRIN. Most of these approaches have high computational complexity i.e. (O(n^{3} )) . This research aims to put forward holistic approach using Heterogeneous Surface Learning Features (HSLF) for IRIN on specific topic and proposes two approaches: Average Consensus Ranking Aggregation and Weighted Average Consensus Ranking Aggregation using HSLF. The effectiveness and efficiency of the proposed approaches are tested and analysed using real world data fetched from Twitter for two topics, Politics and Economy and achieved superior results compared to existing approaches. The empirical analysis validate that the proposed approach is highly scalable with low computational complexity and applicable for large datasets.</p>", "Keywords": "Social network; Influence analysis; Centrality measures; Surface Learning", "DOI": "10.1007/s00521-019-04568-0", "PubYear": 2020, "Volume": "32", "Issue": "14", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, SVNIT, Surat, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, SVNIT, Surat, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, SVNIT, Surat, India"}], "References": []}, {"ArticleId": 78322877, "Title": "HealthFog: An ensemble deep learning based Smart Healthcare System for Automatic Diagnosis of Heart Diseases in integrated IoT and fog computing environments", "Abstract": "Cloud computing provides resources over the Internet and allows a plethora of applications to be deployed to provide services for different industries. The major bottleneck being faced currently in these cloud frameworks is their limited scalability and hence inability to cater to the requirements of centralized Internet of Things (IoT) based compute environments. The main reason for this is that latency-sensitive applications like health monitoring and surveillance systems now require computation over large amounts of data (Big Data) transferred to centralized database and from database to cloud data centers which leads to drop in performance of such systems. The new paradigms of fog and edge computing provide innovative solutions by bringing resources closer to the user and provide low latency and energy efficient solutions for data processing compared to cloud domains. Still, the current fog models have many limitations and focus from a limited perspective on either accuracy of results or reduced response time but not both. We proposed a novel framework called HealthFog for integrating ensemble deep learning in Edge computing devices and deployed it for a real-life application of automatic Heart Disease analysis. HealthFog delivers healthcare as a fog service using IoT devices and efficiently manages the data of heart patients, which comes as user requests. Fog-enabled cloud framework, FogBus is used to deploy and test the performance of the proposed model in terms of power consumption, network bandwidth, latency, jitter, accuracy and execution time. HealthFog is configurable to various operation modes which provide the best Quality of Service or prediction accuracy, as required, in diverse fog computation scenarios and for different user requirements.", "Keywords": "Fog computing ; Internet of things ; Healthcare ; Deep learning ; Ensemble learning ; Heart patient analysis", "DOI": "10.1016/j.future.2019.10.043", "PubYear": 2020, "Volume": "104", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cloud Computing and Distributed Systems (CLOUDS) Laboratory, School of Computing and Information Systems, The University of Melbourne, Australia;Department of Computer Science and Engineering, Indian Institute of Technology (IIT), Delhi, India;Corresponding author at: Cloud Computing and Distributed Systems (CLOUDS) Laboratory, School of Computing and Information Systems, The University of Melbourne, Australia."}, {"AuthorId": 2, "Name": "Nipam Basumatary", "Affiliation": "Cloud Computing and Distributed Systems (CLOUDS) Laboratory, School of Computing and Information Systems, The University of Melbourne, Australia;Department of Computer Science and Engineering, Indian Institute of Technology (IIT), Madras, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering and Computer Science (EECS), Queen Mary University of London, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cloud Computing and Distributed Systems (CLOUDS) Laboratory, School of Computing and Information Systems, The University of Melbourne, Australia;Web Technologies Laboratory, Ferdowsi University Of Mashhad, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Cardiology, Hero Heart Institute, Dayanand Medical College and Hospital, Ludhiana, Punjab, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Cardiology, Hero Heart Institute, Dayanand Medical College and Hospital, Ludhiana, Punjab, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Cloud Computing and Distributed Systems (CLOUDS) Laboratory, School of Computing and Information Systems, The University of Melbourne, Australia"}], "References": []}, {"ArticleId": 78322925, "Title": "Adaptive sliding windows for improved estimation of data center resource utilization", "Abstract": "Accurate prediction of data center resource utilization is required for capacity planning, job scheduling, energy saving, workload placement, and load balancing to utilize the resources efficiently. However, accurately predicting those resources is challenging due to dynamic workloads, heterogeneous infrastructures, and multi-tenant co-hosted applications. Existing prediction methods use fixed size observation windows which cannot produce accurate results because of not being adaptively adjusted to capture local trends in the most recent data. Therefore, those methods train on large fixed sliding windows using an irrelevant large number of observations yielding to inaccurate estimations or fall for inaccuracy due to degradation of estimations with short windows on quick changing trends. In this paper we propose a deep learning-based adaptive window size selection method, dynamically limiting the sliding window size to capture the trend for the latest resource utilization, then build an estimation model for each trend period. We evaluate the proposed method against multiple baseline and state-of-the-art methods, using real data-center workload data sets. The experimental evaluation shows that the proposed solution outperforms those state-of-the-art approaches and yields 16 to 54% improved prediction accuracy compared to the baseline methods.", "Keywords": "Sliding windows ; Adaptive observation window ; Time series ; Resource estimation ; Data center", "DOI": "10.1016/j.future.2019.10.026", "PubYear": 2020, "Volume": "104", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitat Politècnica de Catalunya (UPC), Spain;Barcelona Supercomputing Center (BSC), Spain;University of the Punjab (PU), Pakistan;Corresponding author at: Universitat Politècnica de Catalunya (UPC), Spain."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of the Punjab (PU), Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitat Politècnica de Catalunya (UPC), Spain;Barcelona Supercomputing Center (BSC), Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universitat Politècnica de Catalunya (UPC), Spain;Barcelona Supercomputing Center (BSC), Spain"}], "References": []}, {"ArticleId": 78322947, "Title": "Product Sustainable Design: A Review From the Environmental, Economic, and Social Aspects", "Abstract": "Abstract <p>As a global concern, the sustainability of a product is the responsibility for manufacturing. Product design has become one of the sources and core drivers for manufacturing competition, and the international competitiveness of products would mostly depend on product design capabilities. The product design has essential and profound impacts on the manufacturing, and thus, many researchers focus on product design and make varies of contributions in this area. Product sustainable design is a design process for a product with the consideration of environmental, economic, and social sustainability during the product entire life cycle. The result of product sustainable design is the creation of products with high sustainability of environmental, economic, and social aspects. This paper reviews the state of the art in the product sustainable design methodologies and tools from the perspective of environmental, economic, and social aspects. For the environmental perspective, design for environment methodologies and tools would enable products in a more environmentally friendly manner in the manufacturing. For the economic perspective, this paper introduces the design methodologies for the economic sustainability with cost, assembly, manufacture, and supply chain. For the social perspective, this paper introduces sustainable social design and social responsibility design for social sustainability and social sustainability through social intervention and social innovation. In addition, it encourages future works.</p>", "Keywords": "sustainable design ; product design ; environmental sustainability ; economic sustainability ; social sustainability ; computer-aided design ; multiscale modeling and simulation", "DOI": "10.1115/1.4045408", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 9341, "JournalTitle": "Journal of Computing and Information Science in Engineering", "ISSN": "1530-9827", "EISSN": "1944-7078", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Shanghai Key Laboratory of Intelligent Manufacturing and Robotics, School of Mechatronic Engineering and Automation, Shanghai University, 99 Shangda Road, Shanghai 200444, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Intelligent Manufacturing and Robotics, School of Mechatronic Engineering and Automation, Shanghai University, 99 Shangda Road, Shanghai 200444, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Cao", "Affiliation": "Shanghai Key Laboratory of Intelligent Manufacturing and Robotics, School of Mechatronic Engineering and Automation, Shanghai University, 99 Shangda Road, Shanghai 200444, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Intelligent Manufacturing and Robotics, School of Mechatronic Engineering and Automation, Shanghai University, 99 Shangda Road, Shanghai 200444, China"}], "References": []}, {"ArticleId": 78322982, "Title": "An Integrated Target Acquisition Approach and Graphical User Interface Tool for Parallel Manipulator Assembly", "Abstract": "Abstract \n In this paper, two integrated target identification and acquisition algorithms and a graphical user interface (GUI) simulation tool for automated assembly of parallel manipulators are proposed. They seek to identify the target machine part from the workspace, obtain its location and pose parameters, and accomplish its assembling task while avoiding the collision with other items (obstacles). Fourier descriptors (FDs) and support vector machine (SVM) are adopted in this approach. The image of task area of workspace is obtained through machine vision, and the target assembling parts are identified. To acquire the location and pose information of the target, a modulus-shift matching (MSM) algorithm is proposed and integrated into the FD and SVM approaches, which could efficiently obtain the pose parameters while eliminating the effect of choice of starting point. The simulation results of two integrated algorithms, FD-MSM and SVM-MSM, are then compared and analyzed. In addition, a GUI is designed to visualize and assist the assembly process. An application on delta parallel robot with an extra rotational degree of freedom (DOF) is presented.", "Keywords": "motion planning ; parallel robot ; machine vision ; FD ; SVM ; image matching ; computer aided manufacturing ; machine learning for engineering applications ; manufacturing automation ; manufacturing planning", "DOI": "10.1115/1.4045411", "PubYear": 2020, "Volume": "20", "Issue": "2", "JournalId": 9341, "JournalTitle": "Journal of Computing and Information Science in Engineering", "ISSN": "1530-9827", "EISSN": "1944-7078", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Design Engineering, School of Mechanical Engineering, Hefei University of Technology, Hefei 230009, China;;Mechanical Engineering Department, Missouri University of Science and Technology, Rolla, MO 65401"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Design Engineering, School of Mechanical Engineering, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The Institute of Robotics, School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Design Engineering, School of Mechanical Engineering, Hefei University of Technology, Hefei 230009, China"}], "References": []}, {"ArticleId": 78322993, "Title": "A lossless compression and encryption mechanism for remote monitoring of ECG data using <PERSON><PERSON>man coding and CBC-AES", "Abstract": "Biomedical signal processing provides a cross-disciplinary international forum through which research on signal and images measurement and analysis of clinical medicine and biological sciences are shared. The broadcast transmission in wireless body area networks raises serious security challenge when employed in biomedical applications. Electrocardiography (ECG) signal is frequently used for diagnosis of cardiovascular diseases. However, the ECG data need a large memory storage device due to continuous heart rate logs and vital parameter storage. Thus, compression schemes are applied to the ECG data before sending them to telemedicine centers for monitoring and analysis. Therefore, a proper compression mechanism cannot only improve the storage efficiency but also help in the rapid data transfer from one device to another due to its compact size. We propose a scheme using buffer blocks, peak detection, compression, and encryption mechanism to enable seamless and secure transmission of ECG signal from the sensor to the monitor. The study will prove that the quality of the reconstructed signals obtained using the proposed scheme, which uses discrete wavelet transform, <PERSON><PERSON>man coding, and Cipher Block Chaining-Advanced Encryption Standard algorithm approach, is superior to that obtained using unencrypted compression. Furthermore, the proposed system can provide quality-control compressed data, although the data have been encrypted. The experimental results show that the proposed system provides an effective means for assuring data security and compression performance for ECG data storage and transmission. The security of the scheme is applied against known attacks on privacy, such as eavesdropping and passive monitoring attack.", "Keywords": "Compression ; DWT ; Encryption ; Electrocardiogram ; Peak detection ; Signal blocks", "DOI": "10.1016/j.future.2019.10.010", "PubYear": 2020, "Volume": "111", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for Telecommunication Research and Innovation (CeTRI), Faculty of Electronic and Computer Engineering (FKeKK), Universiti Teknikal Malaysia Melaka (UTeM), Malaysia;Department of Computer Techniques Engineering, Bilad Al-rafidan University College, Diyala, Baqubah, Iraq;Correspondence to: Faculty of Electronic and Computer Engineering (FKeKK), Universiti Teknikal Malaysia Melaka (UTeM), Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Telecommunication Research and Innovation (CeTRI), Faculty of Electronic and Computer Engineering (FKeKK), Universiti Teknikal Malaysia Melaka (UTeM), Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Telecommunication Research and Innovation (CeTRI), Faculty of Electronic and Computer Engineering (FKeKK), Universiti Teknikal Malaysia Melaka (UTeM), Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Information and Communications Technology, UTeM, Melaka, Malaysia"}], "References": []}, {"ArticleId": 78323019, "Title": "Impact of temporal variations in vegetation optical depth and vegetation temperature on L-band passive soil moisture retrievals over a tropical forest using in-situ information", "Abstract": "The Soil Moisture and Ocean Salinity (SMOS) and Soil Moisture Active Passive (SMAP) missions provide estimates of soil moisture (SM) at similar spatial resolutions using L-band brightness temperatures ( <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0001.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0001.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> T B ). These missions meet the requirement of SM retrievals with an unbiased root-mean-square difference (ubRMSD) <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0002.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0002.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> < 0.04 (m <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0003.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0003.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> 3 m<sup>−3</sup>) compared to <i>in-situ</i> measurements over most of the ecosystems; however, their SM estimates over forests present an ubRMSD <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0004.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0004.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> > 0.10 (m <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0005.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0005.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> 3 m<sup>−3</sup>). In this paper, we compared the SM retrievals from the SMOS and SMAP SM products with <i>in-situ</i> SM over a tropical forest in Southern Mexico. The L-band passive SM retrievals were evaluated in terms of four statistical metrics: root-mean-square difference (RMSD), bias, ubRMSD, and correlation coefficient ( <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0006.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0006.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> r ). <i>In-situ</i> measurements of SM, soil and vegetation temperatures, precipitation, soil surface roughness, tree heights, diameters at the breast height of trunks, and forest cover fraction were collected during a field campaign from 6 January to 14 June 2015 in the Biosphere Reserve of Calakmul, Mexico, covering two areas of about 40 km <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0007.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0007.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> × 40 km each. The comparison between SM retrievals from SMOS and SMAP and <i>in-situ</i> SM showed an RMSD ranging from 0.107 to 0.322 (m <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0008.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0008.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> 3 m<sup>−3</sup>) and an ubRMSD from 0.049 to 0.128 (m <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0009.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0009.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> 3 m<sup>−3</sup>). Overall, the SMAP SM estimates showed higher values of <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0010.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0010.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> r and were closer to <i>in-situ</i> SM. Because the SMAP and SMOS radiometers performed very similar, these differences are due to the values assigned to the vegetation optical depth ( <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0011.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0011.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> τ ), the scattering albedo ( <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0012.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0012.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> ω ), and the representation of the dynamics in vegetation and soil temperatures in the SMOS and SMAP retrieval algorithm. Based on an optimization process, we estimated simultaneously optimal <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0013.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0013.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> ω and <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0014.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0014.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> τ values for the tropical forest by using <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0015.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0015.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> T B observations from SMAP and SMOS radiometers. The optimal value of <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0016.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0016.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> ω was 0.0655 for the tropical forest, and constant over the study period. In contrast, the optimal values of <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0017.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0017.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> τ showed to be variant on time and ranging between 1.0 and 1.7, with an averaged value of 1.4 and a standard deviation of 0.24. When applying the optimal values of <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0018.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0018.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> ω and <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0019.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0019.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> τ and <i>in-situ</i> soil and vegetation temperatures, the SM retrievals showed an ubRMSD of 0.035–0.070 (m <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0020.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0020.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> 3 m <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0021.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0021.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> − 3 ), improving the SM retrievals about 45%. A sensitivity analysis was conducted to evaluate the effect of the uncertainties in <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0022.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0022.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> τ , <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0023.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0023.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> ω , and soil and vegetation temperatures on the estimates of <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0024.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0024.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> T B . It was found that vegetation temperature ( <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0025.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0025.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> T v e g ) was the most sensitive parameter, with <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0026.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0026.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> r higher than 0.70 for both polarizations in <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0027.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0027.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> T B . When comparing <i>in-situ</i> <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0028.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/01431161.2019.1685715/20191208/images/tres_a_1685715_ilm0028.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> T v e g and surface temperature values used in the SMAP and SMOS SM retrieval algorithms, differences up to 10 K were observed, affecting the SM estimates. The results presented in this paper could be useful in the preparation of the SMAP Calibration/Validation Experiment 2019, aiming at improving SM retrievals over forests.", "Keywords": "", "DOI": "10.1080/01431161.2019.1685715", "PubYear": 2020, "Volume": "41", "Issue": "6", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Escuela Superior de Ingeniería Mecánica y Eléctrica Unidad Ticomán, Instituto Politécnico Nacional, Mexico City, Mexico"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": "Escuela Superior de Ingeniería Mecánica y Eléctrica Unidad Ticomán, Instituto Politécnico Nacional, Mexico City, Mexico"}, {"AuthorId": 3, "Name": "<PERSON>Escalona", "Affiliation": "Escuela Superior de Ingeniería Mecánica y Eléctrica Unidad Ticomán, Instituto Politécnico Nacional, Mexico City, Mexico"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CentroGeo, Centro de Investigación en Ciencias de la Información Geoespacial, Mexico City, Mexico;Instituto de Geografía, Universidad Nacional Autónoma de México, Mexico City, Mexico"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Escuela Superior de Ingeniería Mecánica y Eléctrica Unidad Ticomán, Instituto Politécnico Nacional, Mexico City, Mexico;División de Ingeniería Aeronáutica, Tecnológico de Estudios Superiores de Ecatepec, Mexico City, Mexico"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "CentroGeo, Centro de Investigación en Ciencias de la Información Geoespacial, Mexico City, Mexico"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Centre d’Applications et de Recherches en Télédétection, Université de Sherbrooke, Sherbrooke, Canada"}, {"AuthorId": 8, "Name": "Kalifa <PERSON>", "Affiliation": "Centre d’Applications et de Recherches en Télédétection, Université de Sherbrooke, Sherbrooke, Canada"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Instituto de Geografía, Universidad Nacional Autónoma de México, Mexico City, Mexico"}], "References": []}, {"ArticleId": 78323168, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0921-8890(19)30907-8", "PubYear": 2020, "Volume": "123", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [], "References": []}, {"ArticleId": 78323363, "Title": "Perceptual quality assessment of 3D videos with stereoscopic degradations", "Abstract": "<p>In the last decade, several aspects of the 3D video technology have been improved, including the 3D content production, distribution, and display. Still, the level of acceptability and popularity of 3D video applications are strongly correlated to the user Quality of Experience (QoE). Since research in this area depends heavily on data acquired in psychophysical experiments, public databases with typical stereoscopic degradations are considered important tools to researchers. Although currently there are number of available public 3D video quality databases, most of them contain only compression and transmission degradations. In this work, our first goal was to build a database (UnB-3D) that contains stereoscopic distortions. We created a set of Computer Graphics Imaging (CGI) scenes and rendered it using different parameters, generating 3D videos containing stereoscopic degradations at different strengths. Our second goal is to understand how these stereoscopic degradations are perceived by viewers. So, we performed a psychophysical experiment to analyze the perceived quality and comfort of these videos. Finally we conducted the statistical analysis and model generation. Results shows that users that have little familiarity with 3D content have difficulties identifying stereoscopic distortions. Also, the source content has a great influence on the user’s comfort. Similarly, the 3D quality is affected by the spatial and temporal information of the content, specially when the disparity is high.</p>", "Keywords": "3D video quality; Psychophysical experiments; Stereoscopic degradations; Quality databases", "DOI": "10.1007/s11042-019-08386-3", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto Federal de Goiás, Anápolis, Brasil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Brasilia, Brasilia, Brazil"}], "References": []}, {"ArticleId": 78323541, "Title": "Overlapping community detection in social networks with Voronoi and tolerance neighborhood-based method", "Abstract": "<p>Community detection is typically viewed as a graph clustering problem with early detection algorithms focused on detecting non-overlapping communities and formulating various measures and optimization methods to evaluate the quality of clustering. In recent years, overlapping community detection especially in real-world social networks, has become an important and challenging research area since it introduces the possibility of membership of a vertex in more that one community. Overlapping community detection by its definition implies soft clustering and leads to an ideal application of granular computing methods. In this paper, a hybrid computational geometry approach with Voronoi diagrams and tolerance-based neighborhoods (VTNM) is used to detect overlapping communities in social networks. Voronoi partitioning results in a crisp partition of an Euclidean space and a tolerance relation makes it possible to obtain soft partitions. A Voronoi diagram is a method to partition a plane into regions based on nearness to points in a specific set of sites (seeds). In the VTNM approach, these seeds are used as cores for determining tolerance neighborhoods via a non-transitive binary relation. The intersection of these neighborhoods are used to discover overlapping communities. Our proposed VTNM algorithm was tested with 7 small real-world networks and compared with 11 well-known algorithms. VTNM algorithm shows promising results in terms of the Extended Modularity measure, Average F1-score and Normalized Mutual Information (NMI) measure.</p>", "Keywords": "Community detection; Granular computing; Near sets; Social networks analysis; Tolerance neighborhoods; Voronoi diagrams", "DOI": "10.1007/s41066-019-00207-0", "PubYear": 2021, "Volume": "6", "Issue": "1", "JournalId": 4880, "JournalTitle": "Granular Computing", "ISSN": "2364-4966", "EISSN": "2364-4974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Computer Science, University of Winnipeg, Winnipeg, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Computer Science, University of Winnipeg, Winnipeg, Canada"}], "References": []}, {"ArticleId": 78323572, "Title": "Aircraft detection in remote sensing image based on corner clustering and deep learning", "Abstract": "Owing to the variations of aircraft type, pose, size and complex background, it remains difficult to detect aircraft effectively in remote sensing images, which plays a great significance in civilian and military. Classical aircraft detection algorithms still produce thousands of candidate regions and extract the features of candidate regions manually, which affects the detection performance. To address these difficulties encountered, an aircraft detection scheme based on corner clustering and Convolutional Neural Network (CNN) is proposed in this paper. The scheme is divided into two main steps: region proposal and classification. First, candidate regions are generated by utilizing mean-shift clustering algorithm to the corners detected on binary images. Then, the CNN is used for the feature extraction and classification of candidate regions that possibly contain the aircraft, and the location of the aircraft is finally determined after further screening. Compared with other classical methods, such as selective search (SS) + CNN, Edgeboxes + CNN and histogram of oriented gradient (HOG) + support vector machine (SVM), the proposed approach has a high accuracy and efficiency since it can automatically learn the essential features of the object from a large amount of data and produce fewer high quality candidate regions.", "Keywords": "High-resolution remote sensing image ; Aircraft detection ; Convolutional neural network (CNN) ; Corner detection ; Mean-shift clustering", "DOI": "10.1016/j.engappai.2019.103333", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Geography and Information Engineering, China University of Geosciences, Wuhan 430074, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Geography and Information Engineering, China University of Geosciences, Wuhan 430074, PR China;Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of California, Santa Barbara, 93106, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Geography and Information Engineering, China University of Geosciences, Wuhan 430074, PR China"}, {"AuthorId": 5, "Name": "<PERSON> Fang", "Affiliation": "School of Geography and Information Engineering, China University of Geosciences, Wuhan 430074, PR China"}], "References": []}, {"ArticleId": 78323698, "Title": "Transferable heterogeneous feature subspace learning for JPEG mismatched steganalysis", "Abstract": "Steganalysis is a technique that detects the presence of secret information in multimedia data. Many steganalysis algorithms have been proposed with high detection accuracy; however, the difference in statistical distribution between training and testing sets can cause mismatch problems, which will degrade the performance of traditional steganalysis algorithms. To solve this problem, we propose a transferable heterogeneous feature subspace learning (THFSL) algorithm for JPEG mismatched steganalysis. Our approach considers the feature space in each domain as a combination of the domain-independent features and the domain-related features. We use the transformation matrix to transfer both the domain-independent and domain-related features from the source and target domains to a common feature subspace, where each target sample can be better represented by a combination of source samples. By imposing low-rank constraints on the domain-independent features, the structures of data can be preserved, which can capture the intrinsic structures for discriminating cover and stego images. Our method can avoid a potentially negative transfer by using a sparse matrix to model the domain-related features and, thus, is more robust to different domain changes in mismatched steganalysis. Extensive experiments on various mismatched steganalysis tasks show the superiority of the proposed method over the state-of-the art methods.", "Keywords": "Mismatched steganalysis ; Heterogeneous subspace ; Domain-independent features ; Domain-related features ; Transfer learning", "DOI": "10.1016/j.patcog.2019.107105", "PubYear": 2020, "Volume": "100", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Aerospace Information Security and Trusted Computing, Ministry of Education, School of Cyber Science and Engineering, Wuhan University, Wuhan, Hubei 430072, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Aerospace Information Security and Trusted Computing, Ministry of Education, School of Cyber Science and Engineering, Wuhan University, Wuhan, Hubei 430072, China"}, {"AuthorId": 3, "Name": "Weixiang Ren", "Affiliation": "Key Laboratory of Aerospace Information Security and Trusted Computing, Ministry of Education, School of Cyber Science and Engineering, Wuhan University, Wuhan, Hubei 430072, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Aerospace Information Security and Trusted Computing, Ministry of Education, School of Cyber Science and Engineering, Wuhan University, Wuhan, Hubei 430072, China;Corresponding author"}, {"AuthorId": 5, "Name": "Yanzhen Ren", "Affiliation": "Key Laboratory of Aerospace Information Security and Trusted Computing, Ministry of Education, School of Cyber Science and Engineering, Wuhan University, Wuhan, Hubei 430072, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer, Wuhan University, Wuhan, Hubei 430072, China"}], "References": []}, {"ArticleId": 78323839, "Title": "Coupling remote sensing data with in-situ optical measurements to estimate suspended particulate matter under the Evros river influence (North-East Aegean sea, Greece)", "Abstract": "Monitoring the riverine output of Suspended Particulate Matter (SPM) distribution in marine embayment is a crucial factor for the water quality of neighbouring coastal regions. This study presents satellite-derived SPM calculations against <i>in-situ</i> measurements in the continental shelf of North-East Aegean surrounding the transboundary Evros river mouth. Surface SPM, Inherent Optical Properties (IOPs) and remote sensing reflectance (<i>R</i><sub>rs</sub>) data were collected in a field campaign during low river discharge period (June 2016). The relationship between the optical backscattering coefficient (<i>b</i><sub>bp</sub>) and the <i>in-situ</i> SPM concentrations was investigated. Subsequently, an empirical single band model was applied for estimating SPM concentrations by using the Landsat-8 Operational Land Imager (L8/OLI) red band and the model was then locally tuned within the study area. Furthermore, a multi-band SPM-retrieval algorithm was developed using the <i>in-situ</i> surface reflectance <i>R</i><sub>rs</sub> for calibration and it was validated using the Leave-One-Out Cross Validation technique (LOOCV). The relationship between <i>in-situ</i> SPM and backscattering coefficient values showed good proportionality, thus, nominating the predominance of terrestrial mineral particles. Validation against field measurements indicated that the SPM concentrations derived from the newly-developed multi-band algorithm had an improved significance correlation (96%), compared to both the single band model (not-tuned) (coefficient of determination, <i>R</i><sup>2</sup> = 0.82) and its locally tuned version (<i>R</i><sup>2</sup> = 0.83). Most importantly, the generated multi-band model apart from exhibiting the best performance (<i>R</i><sup>2</sup> = 0.93), it revealed high SPM spots which were not detected by the locally tuned single band model, indicating additional processes originating from river outflows, coastal erosion and subaqueous thermal springs in the area. In contrast, the locally tuned single band model overestimated SPM values in offshore waters, where low concentrations are encountered under the influence of the clear Black Sea Water (BSW).", "Keywords": "", "DOI": "10.1080/01431161.2019.1685713", "PubYear": 2020, "Volume": "41", "Issue": "6", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geology &amp; Geoenvironment, National &amp; Kapodistrian University of Athens, Athens, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Surveying &amp; Geoinformatics Engineering, University of West Attica, Aigaleo, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Optical Metrology, University of West Attica, Aigaleo, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geology &amp; Geoenvironment, National &amp; Kapodistrian University of Athens, Athens, Greece"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Environmental Engineering, Democritus University of Thrace, Xanthi, Greece"}], "References": []}, {"ArticleId": 78324436, "Title": "Equilibrium optimizer: A novel optimization algorithm", "Abstract": "This paper presents a novel, optimization algorithm called Equilibrium Optimizer (EO), inspired by control volume mass balance models used to estimate both dynamic and equilibrium states. In EO, each particle (solution) with its concentration (position) acts as a search agent. The search agents randomly update their concentration with respect to best-so-far solutions, namely equilibrium candidates, to finally reach to the equilibrium state (optimal result). A well-defined “generation rate” term is proved to invigorate EO’s ability in exploration, exploitation, and local minima avoidance. The proposed algorithm is benchmarked with 58 unimodal, multimodal, and composition functions and three engineering application problems. Results of EO are compared to three categories of existing optimization methods, including: (i) the most well-known meta-heuristics, including Genetic Algorithm (GA), Particle Swarm Optimization (PSO); (ii) recently developed algorithms, including Grey Wolf Optimizer (GWO), Gravitational Search Algorithm (GSA), and Salp Swarm Algorithm (SSA); and (iii) high performance optimizers, including CMA-ES, SHADE, and LSHADE-SPACMA. Using average rank of Friedman test, for all 58 mathematical functions EO is able to outperform PSO, GWO, GA, GSA, SSA, and CMA-ES by 60%, 69%, 94%, 96%, 77%, and 64%, respectively, while it is outperformed by SHADE and LSHADE-SPACMA by 24% and 27%, respectively. The Bonferroni–Dunnand Holm’s tests for all functions showed that EO is significantly a better algorithm than PSO, GWO, GA, GSA, SSA and CMA-ES while its performance is statistically similar to SHADE and LSHADE-SPACMA. The source code of EO is publicly availabe at https://github.com/afshinfaramarzi/Equilibrium-Optimizer , http://built-envi.com/portfolio/equilibrium-optimizer/ and http://www.alimirjalili.com/SourceCodes/EOcode.zip .", "Keywords": "Optimization ; Metaheuristic ; Genetic algorithm ; Particle Swarm Optimization ; Physics-based", "DOI": "10.1016/j.knosys.2019.105190", "PubYear": 2020, "Volume": "191", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil, Architectural, and Environmental Engineering, Illinois Institute of Technology, Chicago, IL, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Civil, Architectural, and Environmental Engineering, Illinois Institute of Technology, Chicago, IL, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Civil, Architectural, and Environmental Engineering, Illinois Institute of Technology, Chicago, IL, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Artificial Intelligence Research and Optimization, Torrens University Australia, Fortitude Valley, Brisbane, QLD 4006, Australia"}], "References": []}, {"ArticleId": 78324708, "Title": "OPENCoastS: An open-access service for the automatic generation of coastal forecast systems", "Abstract": "Coastal forecast systems are used for many purposes, including harbor management, search and rescue operations, and response to extreme events. However, the generation and operation of these systems is time-consuming, requires expertise in both information technologies and modeling of coastal processes, and needs dedicated computational power. The new service OPENCoastS overcomes these difficulties by generating on-demand coastal circulation forecast systems through a web platform with minimal user intervention. Using a web platform, the user is guided through seven simple steps to generate an operational forecast system for any coastal region. The only requirements are an unstructured grid of the study area and information on river flow, if applicable. The platform provides ocean and atmospheric forcings and data for model validation, and includes interfaces for results visualization and forecasts management. Forecasts are generated with the community model SCHISM, and computing resources are provided through the European Open Science Cloud.", "Keywords": "SCHISM ; Forecast systems ; Unstructured grids ; Web platform ; EOSC", "DOI": "10.1016/j.envsoft.2019.104585", "PubYear": 2020, "Volume": "124", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Laboratory for Civil Engineering, Av. do Brasil, 101, 1700-066, Lisbon, Portugal;Corresponding author."}, {"AuthorId": 2, "Name": "A.B. Fortunato", "Affiliation": "National Laboratory for Civil Engineering, Av. do Brasil, 101, 1700-066, Lisbon, Portugal"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Laboratory for Civil Engineering, Av. do Brasil, 101, 1700-066, Lisbon, Portugal"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Portuguese Institute for Sea and Atmosphere (IPMA), Rua C do Aeroporto, 1749-077, Lisboa, Portugal"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National Laboratory for Civil Engineering, Av. do Brasil, 101, 1700-066, Lisbon, Portugal"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "UMR LIENSs 7266, La Rochelle Université, CNRS, 2 Rue Olympe Gouges, F-17000, La Rochelle, France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "UMR LIENSs 7266, La Rochelle Université, CNRS, 2 Rue Olympe Gouges, F-17000, La Rochelle, France"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratório de Instrumentação & Física Experimental de Partículas, LIP, Lisbon, Portugal"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratório de Instrumentação & Física Experimental de Partículas, LIP, Lisbon, Portugal"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratório de Instrumentação & Física Experimental de Partículas, LIP, Lisbon, Portugal"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "National Laboratory for Civil Engineering, Av. do Brasil, 101, 1700-066, Lisbon, Portugal"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "National Laboratory for Civil Engineering, Av. do Brasil, 101, 1700-066, Lisbon, Portugal"}], "References": []}, {"ArticleId": 78324811, "Title": "An investigation into the hydrodynamics of a spinning cone column: CFD simulations by an Eulerian-Lagrangian approach", "Abstract": "Spinning Cone Column (SCC) is considered as an alternative to the existing stripping equipment in a residual monomer removal process. Rotation-induced dynamics of the fluid are key to the process&#x27;s enhanced efficiency, but the complex features of the SCC make its operation and design challenging. In this study, fluid dynamics inside a lab-scale SCC are investigated from a new angle. Full three-dimensional CFD transient simulations are conducted under various operation and design conditions. In particular, an Eulerian-Lagrangian approach is implemented to describe the motions of the liquid droplets as well as of the liquid film. Based on the results from the CFD simulations, key phenomena for the enhanced performance in monomer removal by the SCC are discussed. The study is intended to provide fundamental understandings of the flow patterns inside SCC, which can be translated into valuable insights for stable operation and scale-up.", "Keywords": "Spinning cone column ; Residual monomer stripping ; CFD ; Eulerian-Lagrangian approach ; Process intensification", "DOI": "10.1016/j.compchemeng.2019.106635", "PubYear": 2020, "Volume": "132", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "Se<PERSON>woong Bae", "Affiliation": "Department of Chemical and Biomolecular Engineering, KAIST, Daejeon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, KAIST, Daejeon, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, KAIST, Daejeon, Republic of Korea;Corresponding author."}], "References": []}, {"ArticleId": 78324820, "Title": "The time buffer approximated <PERSON><PERSON><PERSON> Allocation Problem: A row–column generation approach", "Abstract": "One of the main problems in production systems is the buffer sizing. Choosing the right buffer size, at each production stage, that allows to achieve some performance measure (usually throughput or waiting time) is known as Buffer Allocation Problem (BAP), and it has been widely studied in the literature. Due to its complexity, BAP is usually approached using decomposition methods, under very strict system assumptions, or using simulation-optimization techniques. In this paper, the approximated mathematical programming formulation of the BAP simulation-optimization based on the time buffer concept is used. Using this approximation, buffers are modeled as temporal lags ( time buffers ) and this allows to use Linear Programming (LP) instead of Mixed Integer Linear Programming (MILP) models. Although LP models are easier to solve than MILPs, the huge dimension and the complex solution space topology of the time buffer approximated BAP call for ad hoc solution algorithms. To this purpose, a row-column generation algorithm is proposed, which exploits the theoretical properties of the time buffer approximation to reduce the solution time. The proposed algorithm has been compared with a standard LP solver (ILOG CPLEX) and with a state-of-the-art MILP solver and it proved to be better than the LP solver in most of the cases, and more robust than the MILP solver with respect to computation time. Moreover, the LP model (for flow lines) is able to solve the BAP also for assembly/disassembly lines.", "Keywords": "Buffer allocation ; Simulation-optimization ; Assembly lines ; Math programming ; Row and column generation ; Exact method", "DOI": "10.1016/j.cor.2019.104835", "PubYear": 2020, "Volume": "115", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management and Production Engineering, Politecnico di Torino, Corso Duca degli Abruzzi 24, Torino, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Politecnico di Milano, via La Masa 1, Milano, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Management and Production Engineering, Politecnico di Torino, Corso Duca degli Abruzzi 24, Torino, Italy;Corresponding author."}], "References": []}, {"ArticleId": 78324894, "Title": "DeepIM: Deep Iterative Matching for 6D Pose Estimation", "Abstract": "<p>Estimating 6D poses of objects from images is an important problem in various applications such as robot manipulation and virtual reality. While direct regression of images to object poses has limited accuracy, matching rendered images of an object against the input image can produce accurate results. In this work, we propose a novel deep neural network for 6D pose matching named DeepIM. Given an initial pose estimation, our network is able to iteratively refine the pose by matching the rendered image against the observed image. The network is trained to predict a relative pose transformation using a disentangled representation of 3D location and 3D orientation and an iterative training process. Experiments on two commonly used benchmarks for 6D pose estimation demonstrate that DeepIM achieves large improvements over state-of-the-art methods. We furthermore show that DeepIM is able to match previously unseen objects.</p>", "Keywords": "3D object recognition; 6D object pose estimation; Object tracking", "DOI": "10.1007/s11263-019-01250-9", "PubYear": 2020, "Volume": "128", "Issue": "3", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, USA;Tsinghua University and BNRist, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tsinghua University and BNRist, Beijing, China"}, {"AuthorId": 3, "Name": "Xiangyang Ji", "Affiliation": "Tsinghua University and BNRist, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "NVIDIA, Seattle, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Washington, Seattle, USA;NVIDIA, Seattle, USA"}], "References": []}, {"ArticleId": 78325193, "Title": "Evolving Open-Source Technologies Offer Options for Remote Sensing and Monitoring in Agriculture", "Abstract": "A variety of sensing and monitoring systems have been developed based on the concept of open-source and on open-source hardware and software components. Availability and relatively low cost of hardware components and availability and ease of use of software components allow access to sensing and monitoring technologies that were previously unattainable to many potential users. Advances in electronic monitoring and evolving cellular communications technologies are increasingly offering more, simpler, and less expensive options for remote monitoring. Due to the near-future cessation of 2G and 3G cellular network services, however, many existing monitoring systems will need to be redesigned to operate on alternative cellular networks. A soil-moisture monitoring system was developed incorporating updated open-source Arduino microcontrollers and the recently introduced LTE Cat-M1 cellular network to transmit sensor measurements via the cellular network for access on an internet website. The monitoring system costs approximately US$130 to construct the electronic circuitry and less than US$1 per month for cellular network access and data transmission. Data were transmitted with a 95% success rate, and the monitoring system operated continuously throughout an entire crop growing season with no battery recharge or maintenance requirements. The design and operation of the monitoring system can serve as a basis for other remote monitoring systems.", "Keywords": "Arduino;Microcontrollers;Cellular;Soil Moisture;Sensors;Internet of Things", "DOI": "10.4236/ait.2020.101001", "PubYear": 2020, "Volume": "10", "Issue": "1", "JournalId": 34695, "JournalTitle": "Advances in Internet of Things", "ISSN": "2161-6817", "EISSN": "2161-6825", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "United States Department of Agriculture, Agricultural Research Service, Sustainable Water Management Research Unit, Stoneville, Mississippi, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "United States Department of Agriculture, Agricultural Research Service, Crop Production Systems Research Unit, Stoneville, Mississippi, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "United States Department of Agriculture, Agricultural Research Service, Sustainable Water Management Research Unit, Stoneville, Mississippi, USA"}], "References": []}, {"ArticleId": 78325622, "Title": "Joint mode selection and power control for D2D underlaid cellular networks", "Abstract": "Device-to-Device (D2D) communication is a new paradigm proposed in cellular networks, which promises several benefits to the network providers as well as end users. Firstly, it improves spectral efficiency (SE) of cellular networks by re-using the same frequency resources occupied by cellular users. In fact, mobile users cellular networks use high data rate services (e.g., video sharing, gaming, proximity aware social networking etc.), in which they could potentially be in range for direct communications (i.e., D2D). Secondly, D2D can enhance energy efficiency (EE), since the mobile terminals use less transmission power when communicating directly between each other. Thirdly, D2D communication can also reduce communication delay and increase network throughput, due to its short distance communication. This paper investigates the future challenges in order to improve the network throughput by proposing a joint mode selection (MS) and centralized power control (PC), which maximizes the sum rate of D2D links. To keep the interference under control, coverage probabilities are studied in this paper for both cellular and D2D users, while a signal-to-interference-plus-noise ratio (SINR) threshold is maintained for more than one CUE. Based on the latter assumption, this paper provides two sufficient conditions to ensure the existence of the Pareto optimal power, that should be verified by both Cellular User Equipment (CUE) and D2D User Equipment (DUE) during the MS process. The mathematical expressions of these two sufficient conditions are proved. Then, the PC approach is deduced based on two derived sufficient conditions. Two joint MS and PC algorithms are proposed in this paper and compared in terms of coverage probability and total power consumption with a conventional algorithm for both CUE and DUE.", "Keywords": "Cellular user equipment ; D2D user equipment ; Mode selection ; Power control", "DOI": "10.1016/j.phycom.2019.100917", "PubYear": 2020, "Volume": "38", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Carthage, Higher School of Communications of Tunis, COSIM Research Laboratory Tunis, Tunisia"}], "References": []}, {"ArticleId": 78325626, "Title": "Multi-robot Target Encirclement Control with Collision Avoidance via Deep Reinforcement Learning", "Abstract": "<p>The target encirclement control of multi-robot systems via deep reinforcement learning has been investigated in this paper. Inspired by the encirclement behavior of dolphins to entrap the fishes, the encirclement control is mainly to enforce the robots to achieve a capturing formation pattern around a target, and can be widely applied in many areas such as coverage, patrolling, escorting, etc. Different from traditional methods, we propose a deep reinforcement learning framework for multi-robot target encirclement formation control, combining the advantages of the deep neural network and deterministic policy gradient algorithm, which is free from the complicated work of building the control model and designing the control law. Our method provides a distributed control architecture for each robot in continuous action space, relying only on local teammate information. Besides, the behavioral output at each time step is determined by its own independent network. In addition, both the robots and the moving target can be trained simultaneously. In that way, both cooperation and competition can be contained, and the results validate the effectiveness of the proposed algorithm.</p>", "Keywords": "Multi-robot; Deep reinforcement learning; Encirclement control; Collision avoidance", "DOI": "10.1007/s10846-019-01106-x", "PubYear": 2020, "Volume": "99", "Issue": "2", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "Junchong Ma", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, People’s Republic of China"}], "References": []}, {"ArticleId": 78325790, "Title": "CLOSURE: A cloud scientific workflow scheduling algorithm based on attack–defense game model", "Abstract": "The multi-tenant coexistence service mode makes the cloud-based scientific workflow encounter the risks of being intruded. For this problem, we propose a CLoud scientific wOrkflow SchedUling algoRithm based on attack–defensE game model (CLOSURE). In the algorithm, attacks based on different operating system vulnerabilities are regarded as different “ attack ” strategies; and different operating system distributions in a virtual machine cluster executing the workflows are regarded as different “ defense ” strategies. The information of the attacker and defender is not balanced. In other words, the defender cannot obtain the information about the attacker’s strategies, while the attacker can acquire information about the defender’s strategies through a network scan. Therefore, we propose to dynamically switch the defense strategies during the workflow execution, which can weaken the network scan effects and transform the workflow security problem into an attack–defense game problem. Then, the probability distribution of the optimal mixed defense strategies can be achieved by calculating the Nash Equilibrium in the attack–defense game model. Based on this probability, diverse VMs are provisioned for workflow execution. Furthermore, a task-VM mapping algorithm based on dynamic Heterogeneous Earliest Finish Time (HEFT) is presented to accelerate the defense strategy switching and improve workflow efficiency. The experiments are conducted on both simulation and actual environment, experimental results demonstrate that compared with other algorithms, the proposed algorithm can reduce the attacker’s benefits by around 15.23%, and decrease the time costs of the algorithm by around 7.86%.", "Keywords": "Scientific workflow ; Workflow scheduling ; Attack–defense game ; Diverse operating systems ; Moving target defense", "DOI": "10.1016/j.future.2019.11.003", "PubYear": 2020, "Volume": "111", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Digital Switching System Engineering Technology Research Center, Zhengzhou 450002, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Digital Switching System Engineering Technology Research Center, Zhengzhou 450002, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing 100081, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Liverpool John Moores University, Liverpool L3 3AF, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National Digital Switching System Engineering Technology Research Center, Zhengzhou 450002, China"}], "References": []}, {"ArticleId": 78325831, "Title": "A dynamic games approach to proactive defense strategies against Advanced Persistent Threats in cyber-physical systems", "Abstract": "Advanced Persistent Threats (APTs) have recently emerged as a significant security challenge for a cyber-physical system due to their stealthy, dynamic and adaptive nature. Proactive dynamic defenses provide a strategic and holistic security mechanism to increase the costs of attacks and mitigate the risks. This work proposes a dynamic game framework to model a long-term interaction between a stealthy attacker and a proactive defender. The stealthy and deceptive behaviors are captured by the multi-stage game of incomplete information, where each player has his own private information unknown to the other. Both players act strategically according to their beliefs which are formed by the multi-stage observation and learning. The perfect Bayesian Nash equilibrium provides a useful prediction of both players’ policies because no players benefit from unilateral deviations from the equilibrium. We propose an iterative algorithm to compute the perfect Bayesian Nash equilibrium and use the Tennessee Eastman process as a benchmark case study. Our numerical experiment corroborates the analytical results and provides further insights into the design of proactive defense-in-depth strategies.", "Keywords": "Advanced persistent threats ; Defense in depth ; Proactive defense ; Industrial control system security ; Cyber deception ; Multi-stage Bayesian game ; Perfect Bayesian Nash equilibrium ; Tennessee Eastman process", "DOI": "10.1016/j.cose.2019.101660", "PubYear": 2020, "Volume": "89", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, New York University, Brooklyn, NY 11201, USA;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, New York University, Brooklyn, NY 11201, USA"}], "References": []}, {"ArticleId": 78325868, "Title": "Comprehensive development, uncertainty and sensitivity analysis of a model for the hydrolysis of rapeseed oil", "Abstract": "A model describing the batch hydrolysis of rapeseed oil including kinetics and mass transfer at subcritical conditions is presented in this paper. The primary purpose of this model is to interpret experimental data collected from typical batch tests and to estimate model parameters. The developed model was further investigated using Monte Carlo simulations to statistically quantify the variability in the model outputs due to uncertainties in the parameter estimates. To understand which parameters in the model are responsible for the output uncertainty, a sensitivity analysis method was used (polynomial chaos expansions-based Sobol sensitivity indices). The results from the sensitivity analysis helped to identify what parameters in the model are influential, giving insight into the robustness and predictive capabilities of the model which form the basis for any model-based decision making for detailed process characterization, design, optimization and operation of the hydrolysis of rapeseed oil.", "Keywords": "Uncertainty analysis ; Sensitivity analysis ; Vegetable oils ; Kinetic modeling", "DOI": "10.1016/j.compchemeng.2019.106631", "PubYear": 2020, "Volume": "133", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Edible Oil Systems, Alfa Laval Copenhagen A/S, Soborg, 2860, Denmark;Process and Systems Engineering Research Centre (PROSYS), Department of Chemical and Biochemical Engineering, Technical University of Denmark, 2800 Kongens Lyngby, Denmark"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Edible Oil Systems, Alfa Laval Copenhagen A/S, Soborg, 2860, Denmark;Process and Systems Engineering Research Centre (PROSYS), Department of Chemical and Biochemical Engineering, Technical University of Denmark, 2800 Kongens Lyngby, Denmark"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Edible Oil Systems, Alfa Laval Copenhagen A/S, Soborg, 2860, Denmark"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Combustion and Harmful Emission Control Research Centre (CHEC), Department of Chemical and Biochemical Engineering, Technical University of Denmark, Kongens Lyngby, 2800, Denmark"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Process and Systems Engineering Research Centre (PROSYS), Department of Chemical and Biochemical Engineering, Technical University of Denmark, 2800 Kongens Lyngby, Denmark"}, {"AuthorId": 6, "Name": "Gürkan Sin", "Affiliation": "Process and Systems Engineering Research Centre (PROSYS), Department of Chemical and Biochemical Engineering, Technical University of Denmark, 2800 <PERSON><PERSON>, Denmark;Corresponding author."}], "References": []}, {"ArticleId": 78325879, "Title": "Lightweight merging of compressed indices based on BWT variants", "Abstract": "In this paper we propose a flexible and lightweight technique for merging compressed indices based on variants of Burrows-Wheeler transform (BWT), thus addressing the need for algorithms that compute compressed indices over large collections using a limited amount of working memory. Merge procedures make it possible to use an incremental strategy for building large indices based on merging indices for progressively larger subcollections. Starting with a known lightweight algorithm for merging BWTs Holt and McMillan (2014) [22] , we show how to modify it in order to merge, or compute from scratch, also the Longest Common Prefix (LCP) array. We then expand our technique for merging compressed tries and circular/permuterm compressed indices, two compressed data structures for which there were hitherto no known merging algorithms.", "Keywords": "Multi-string BWT ; Longest Common Prefix array ; XBWT ; Trie compression ; Compressed permuterm index ; Circular patterns", "DOI": "10.1016/j.tcs.2019.11.001", "PubYear": 2020, "Volume": "812", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Eastern Piedmont, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Eastern Piedmont and IIT-CNR Pisa, Italy"}], "References": []}, {"ArticleId": 78325881, "Title": "Multi-agent systems with virtual stigmergy", "Abstract": "We introduce a simple language for multi-agent systems that lends itself to intuitive design of local specifications. Agents operate on (parts of) a decentralized data structure, the stigmergy, that contains their (partial) knowledge. Such knowledge is asynchronously propagated across local stigmergies. In this way, local changes may influence global behavior. The main novelty is that our interaction mechanism combines stigmergic interaction with attribute-based communication. Specific conditions for interaction can be expressed in the form of predicates over exposed features of the agents. Additionally, agents may access a global environment. After presenting the language, we show its expressiveness by considering some illustrative case studies. We also include preliminary results towards automated verification via a mechanizable symbolic encoding that enables us to exploit verification tools developed for mainstream languages.", "Keywords": "Multi-agent systems ; Stigmergic interaction ; Emergent behavior ; Attribute-based communication ; Agent-based modeling", "DOI": "10.1016/j.scico.2019.102345", "PubYear": 2020, "Volume": "187", "Issue": "", "JournalId": 12043, "JournalTitle": "Science of Computer Programming", "ISSN": "0167-6423", "EISSN": "1872-7964", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "IMT School for Advanced Studies, Lucca, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Gran Sasso Science Institute (GSSI), L'Aquila, Italy;Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Gran Sasso Science Institute (GSSI), L'Aquila, Italy"}], "References": []}, {"ArticleId": 78325918, "Title": "On dealing with strategic and tactical decision levels in forestry planning under uncertainty", "Abstract": "A new scheme for dealing with the uncertainty in the scenario trees is considered in the presence of strategic and tactical stochastic parameters for a dynamic mixed 0–1 optimization model in a forest harvesting network along a time horizon under uncertainty. The strategic level of the model presented in this work is included by a several years time horizon, where the uncertainty lies in the timber production. It is represented in a multistage stochastic scenario tree, such that each stage comprises one or several years. Each node in the strategic tree has associated a multi-period scenario graph, where each period in the stages is related to a summer /winter season. The nodes in the graph represent the tactical uncertainty, whose stochastic parameters are the timber price and demand. The strategic decisions aim to the optimal design of the logistic timber harvesting and distribution network at each first period in the stages. The tactical decisions aim to timber harvesting, stocking and distribution from the stands until the markets at the periods in the stages. The model has been validated by using data from a real-life problem.", "Keywords": "OR in natural resources ; Forestry planning ; Optimization under uncertainty ; Strategic and tactical scenario trees", "DOI": "10.1016/j.cor.2019.104836", "PubYear": 2020, "Volume": "115", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Area de Estadística e Investigación Operativa, Universidad Rey Juan Carlos, Móstoles, Madrid, Spain;Corresponding author."}, {"AuthorId": 2, "Name": "Laureano F. Escudero", "Affiliation": "Area de Estadística e Investigación Operativa, Universidad Rey Juan Carlos, Móstoles, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Operations, Information and Decisions Department, The Wharton School, University of Pennsylvania, Philadelphia, PA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Ingeniería Industrial, Universidad de Chile, Instituto Sistemas Complejos de Ingenieria, Santiago, Chile"}], "References": []}, {"ArticleId": 78325962, "Title": "Patterns of relation triples in inversion and ascent sequences", "Abstract": "Recently, <PERSON> and <PERSON> carried out the systematic study of inversion sequences avoiding triples of relations. They reported many nice connections with familiar combinatorial families and posed several enumeration conjectures. All of their conjectures have already been solved except those related to the OEIS sequence A098746. In this paper, we address those remaining conjectures, which completes a picture for all the suspected connections arising in their investigation. As one of the most important subsets of inversion sequences, ascent sequences were introduced by <PERSON><PERSON><PERSON> et al. in bijection with ( 2 + 2 ) -free posets and their pattern avoidance properties have been extensively studied. We investigate some classical Eulerian and Stirling statistics on ascent sequences avoiding triples of relations. This leads us to find two new interpretations of the Catalan numbers and its refinements, and to interpret combinatorially a natural refinement of the binomial transformation of Catalan numbers. The latter discovery answers a challenging open problem posed by <PERSON><PERSON><PERSON>.", "Keywords": "Inversion sequences ; Ascent sequences ; Pattern avoidance ; Catalan numbers ; Binomial-Catalan polynomials", "DOI": "10.1016/j.tcs.2019.11.007", "PubYear": 2020, "Volume": "804", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Center for Mathematics and Interdisciplinary Sciences, Shandong University, Qingdao 266237, PR China"}], "References": []}, {"ArticleId": 78325966, "Title": "The spatially conscious machine learning model", "Abstract": "<p>Successfully predicting gentrification could have many social and commercial applications; however, real estate sales are difficult to predict because they belong to a chaotic system comprised of intrinsic and extrinsic characteristics, perceived value, and market speculation. Using New York City real estate as our subject, we combine modern techniques of data science and machine learning with traditional spatial analysis to create robust real estate prediction models for both classification and regression tasks. We compare several cutting edge machine learning algorithms across spatial, semispatial, and nonspatial feature engineering techniques, and we empirically show that spatially conscious machine learning models outperform nonspatial models when married with advanced prediction techniques such as Random Forests, generalized linear models, gradient boosting machines, and artificial neural networks.</p>", "Keywords": "artificial neural networks;feature engineering;generalized linear models;gradient boosting;machine learning;Random Forests;real estate;spatial analysis", "DOI": "10.1002/sam.11440", "PubYear": 2020, "Volume": "13", "Issue": "1", "JournalId": 5556, "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal", "ISSN": "1932-1864", "EISSN": "1932-1872", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Data Science, Northwestern University, Chicago, Illinois"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Data Science, Northwestern University, Chicago, Illinois"}], "References": []}, {"ArticleId": 78326037, "Title": "Portraying the spatial dynamics of urban vibrancy using multisource urban big data", "Abstract": "Understanding urban vibrancy aids policy-making to foster urban space and therefore has long been a goal of urban studies. Recently, the emerging urban big data and urban analytic methods have enabled us to portray citywide vibrancy. From the social sensing perspective, this study presents a comprehensive and comparative framework to cross-validate urban vibrancy and uncover associated spatial effects. Spatial patterns of urban vibrancy indicated by multisource urban sensing data (points-of-interest, social media check-ins, and mobile phone records) were investigated. A comprehensive urban vibrancy metric was formed by adaptively weighting these metrics. The association between urban vibrancy and demographic, economic, and built environmental factors was revealed with global regression models and local regression models. An empirical experiment was conducted in Shenzhen. The results demonstrate that four urban vibrancy metrics are all higher in the special economic zone (SEZ) and lower in non-SEZs but with different degrees of spatial aggregation. The influences of employment and road density on all vibrancy metrics are significant and positive. However, the effects of metro stations, land use mix, building footprints, and distance to district center depend on the vibrancy indicator and location. These findings unravel the commonalities and differences in urban vibrancy metrics derived from multisource urban big data and the hidden spatial dynamics of the influences of associated factors. They further suggest that urban policies should be proposed to foster vibrancy in Shenzhen therefore benefit social wellbeing and urban development in the long term. They also provide valuable insights into the reliability of urban big data-driven urban studies.", "Keywords": "Urban vibrancy ; Geographically weighted regression ; mobile phone data ; Social media ; Points-of-interest ; Big data", "DOI": "10.1016/j.compenvurbsys.2019.101428", "PubYear": 2020, "Volume": "80", "Issue": "", "JournalId": 1688, "JournalTitle": "Computers, Environment and Urban Systems", "ISSN": "0198-9715", "EISSN": "1873-7587", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Guangdong Key Laboratory of Urban Informatics, Guangdong Laboratory of Artificial Intelligence and Digital Economy(SZ), Shenzhen Key Laboratory of Spatial Information Smart Sensing and Services, and Research Institute of Smart Cities, Shenzhen University, Shenzhen 518060, China;Department of Urban Informatics, School of Architecture and Urban Planning, Shenzhen University, Shenzhen 518060, China;Key Laboratory for Geo-Environmental Monitoring of Coastal Zone of the National Administration of Surveying, Mapping and GeoInformation, College of Civil Engineering, Shenzhen University, Shenzhen 518060, China;Co-corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Key Laboratory of Urban Informatics, Guangdong Laboratory of Artificial Intelligence and Digital Economy(SZ), Shenzhen Key Laboratory of Spatial Information Smart Sensing and Services, and Research Institute of Smart Cities, Shenzhen University, Shenzhen 518060, China;Department of Urban Informatics, School of Architecture and Urban Planning, Shenzhen University, Shenzhen 518060, China;Key Laboratory for Geo-Environmental Monitoring of Coastal Zone of the National Administration of Surveying, Mapping and GeoInformation, College of Civil Engineering, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 3, "Name": "<PERSON>z<PERSON> Xia", "Affiliation": "Guangdong Key Laboratory of Urban Informatics, Guangdong Laboratory of Artificial Intelligence and Digital Economy(SZ), Shenzhen Key Laboratory of Spatial Information Smart Sensing and Services, and Research Institute of Smart Cities, Shenzhen University, Shenzhen 518060, China;Department of Urban Informatics, School of Architecture and Urban Planning, Shenzhen University, Shenzhen 518060, China;Key Laboratory for Geo-Environmental Monitoring of Coastal Zone of the National Administration of Surveying, Mapping and GeoInformation, College of Civil Engineering, Shenzhen University, Shenzhen 518060, China;Corresponding author at: Guangdong Key Laboratory of Urban Informatics, Guangdong Laboratory of Artificial Intelligence and Digital Economy(SZ), Shenzhen Key Laboratory of Spatial Information Smart Sensing and Services, and Research Institute of Smart Cities, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography and Resource Management, The Chinese University of Hong Kong, Hong Kong"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Geo-Environmental Monitoring of Coastal Zone of the National Administration of Surveying, Mapping and GeoInformation, College of Civil Engineering, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 6, "Name": "Jincheng Jiang", "Affiliation": "Guangdong Key Laboratory of Urban Informatics, Guangdong Laboratory of Artificial Intelligence and Digital Economy(SZ), Shenzhen Key Laboratory of Spatial Information Smart Sensing and Services, and Research Institute of Smart Cities, Shenzhen University, Shenzhen 518060, China;Department of Urban Informatics, School of Architecture and Urban Planning, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Key Laboratory of Urban Informatics, Guangdong Laboratory of Artificial Intelligence and Digital Economy(SZ), Shenzhen Key Laboratory of Spatial Information Smart Sensing and Services, and Research Institute of Smart Cities, Shenzhen University, Shenzhen 518060, China;Department of Urban Informatics, School of Architecture and Urban Planning, Shenzhen University, Shenzhen 518060, China;Key Laboratory for Geo-Environmental Monitoring of Coastal Zone of the National Administration of Surveying, Mapping and GeoInformation, College of Civil Engineering, Shenzhen University, Shenzhen 518060, China"}], "References": []}, {"ArticleId": 78326189, "Title": "MDiNE : a model to estimate differential co-occurrence networks in microbiome studies", "Abstract": "Abstract Motivation <p>The human microbiota is the collection of microorganisms colonizing the human body, and plays an integral part in human health. A growing trend in microbiome analysis is to construct a network to estimate the co-occurrence patterns among taxa through precision matrices. Existing methods do not facilitate investigation into how these networks change with respect to covariates.</p> Results <p>We propose a new model called Microbiome Differential Network Estimation (MDiNE) to estimate network changes with respect to a binary covariate. The counts of individual taxa in the samples are modelled through a multinomial distribution whose probabilities depend on a latent Gaussian random variable. A sparse precision matrix over all the latent terms determines the co-occurrence network among taxa. The model fit is obtained and evaluated using Hamiltonian Monte Carlo methods. The performance of our model is evaluated through an extensive simulation study, and is shown to outperform existing methods in terms of estimation of network parameters. We also demonstrate an application of the model to estimate changes in the intestinal microbial network topology with respect to <PERSON><PERSON><PERSON>’s disease.</p> Availability <p>MDiNE is implemented in a freely available R package: https://github.com/kevinmcgregor/mdine.</p> Supplementary information <p>A file containing supplemental material has been submitted with this manuscript.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btz824", "PubYear": 2020, "Volume": "36", "Issue": "6", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Epidemiology, Biostatistics and Occupational Health, McGill University, Montréal, QC, Canada;Centre for Clinical Epidemiology, Lady Davis Institute, Jewish General Hospital, Montréal, QC, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Département de sciences de la décision, HEC Montréal, Montréal, QC, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Epidemiology, Biostatistics and Occupational Health, McGill University, Montréal, QC, Canada;Centre for Clinical Epidemiology, Lady Davis Institute, Jewish General Hospital, Montréal, QC, Canada;<PERSON> of Oncology, McGill Department of Human Genetics, McGill University, Montréal, QC, Canada"}], "References": []}, {"ArticleId": ********, "Title": "How to account for particle size effects in a landscape evolution model when there is a wide range of particle sizes", "Abstract": "Surface mining irreversibly disturbs the landscape. A first order priority is to establish an erosionally stable landscape. Soil and surface material particle size has a strong influence on soil erosion and a number of erosion models have been developed based on the relationship with particle size. Here we highlight the practicalities of assessing material particle size for a post-mining landscape. In particular, the CAESAR-Lisflood landscape evolution model (the focus here) requires a defined material particle size as input. A key feature which differentiates CAESAR-Lisflood is the ability to apply particle size data at the same resolution as the digital elevation model (DEM) representing the landform surface. Here we develop particle size distributions and demonstrate how they influence erosion for a potential post-mining landform. Field data from the site demonstrates that material particle size distribution changes little over a ten year period and yet a strong influence on erosion rates.", "Keywords": "Soil particle size ; Sediment transport ; Landscape evolution ; Mine rehabilitation ; Soil erosion modelling", "DOI": "10.1016/j.envsoft.2019.104582", "PubYear": 2020, "Volume": "124", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Environmental and Life Sciences, Earth Science Building, The University of Newcastle, Callaghan, New South Wales, 2308, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ecosystem Restoration and Landform Group, Environmental Research Institute of the Supervising Scientist, Department of the Environment and Energy, Darwin, Northern Territory, Australia"}, {"AuthorId": 3, "Name": "J.B.C<PERSON>", "Affiliation": "Ecosystem Restoration and Landform Group, Environmental Research Institute of the Supervising Scientist, Department of the Environment and Energy, Darwin, Northern Territory, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Environmental and Life Sciences, Earth Science Building, The University of Newcastle, Callaghan, New South Wales, 2308, Australia;Ecosystem Restoration and Landform Group, Environmental Research Institute of the Supervising Scientist, Department of the Environment and Energy, Darwin, Northern Territory, Australia"}], "References": []}, {"ArticleId": 78326281, "Title": "The role of superior image composition in children's analogical reasoning", "Abstract": "<p>Analogical reasoning, as a higher cognitive ability, can help children make inferences about a novel situation. It is vital to help children's analogical reasoning development. However, the traditional intervention methods are simple and the effects cannot maintain. Aiming at this problem, the present study was the first to use computer technology especially image composition technique to promote children's analogical reasoning from an interdisciplinary perspective.</p> <p>Specifically, one minimum region entropy based composition model was proposed. On the one hand, sparse coding model and spatial pyramid matching model were used for searching semantically matching images. On the other hand, minimum region entropy model could contribute to composite the candidate region into an ideal position. Furthermore, we set up a database using massive images and adequate experiments based on it to verify the model's effectiveness and robustness.</p> <p>What's more important, we applied the improved image composition to analogical reasoning task. The results showed that the performance of intervention group was obviously better than control group during intervention stages and posttest stage. In general, the present study not only demonstrated the advantages of the improved image composition but also revealed composition's remarkable contribution for getting analogical relationship by children.</p>", "Keywords": "analogical reasoning;children;image composition;minimum region entropy;sparse coding;spatial pyramid matching;watershed segmentation", "DOI": "10.1002/spe.2767", "PubYear": 2020, "Volume": "50", "Issue": "11", "JournalId": 1840, "JournalTitle": "Software: Practice and Experience", "ISSN": "0038-0644", "EISSN": "1097-024X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Developmental Psychology, Faculty of Psychology, Beijing Normal University, Beijing, China"}, {"AuthorId": 2, "Name": "Congcong Han", "Affiliation": "School of Psychology, Shandong Normal University, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing Normal University, Beijing, China; <PERSON><PERSON>, School of Artificial Intelligence, Beijing Normal University, Beijing 100875, China.; <PERSON><PERSON>, School of Developmental Psychology, Faculty of Psychology, Beijing Normal University, Beijing 100875, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Developmental Psychology, Faculty of Psychology, Beijing Normal University, Beijing, China; <PERSON><PERSON>, School of Artificial Intelligence, Beijing Normal University, Beijing 100875, China.; <PERSON><PERSON>, School of Developmental Psychology, Faculty of Psychology, Beijing Normal University, Beijing 100875, China."}], "References": []}, {"ArticleId": 78326326, "Title": "DiNGO: standalone application for Gene Ontology and Human Phenotype Ontology term enrichment analysis", "Abstract": "Abstract Summary <p>Although various tools for Gene Ontology (GO) term enrichment analysis are available, there is still room for improvement. Hence, we present DiNGO, a standalone application based on an open source code from BiNGO, a widely-used application to assess the overrepresentation of GO categories. Besides facilitating GO term enrichment analyses, DiNGO has been developed to allow for convenient Human Phenotype Ontology (HPO) term overrepresentation investigation. This is an important contribution considering the increasing interest in HPO in scientific research and its potential in clinical settings. DiNGO supports gene/protein identifier conversion and an automatic updating of GO and HPO annotation resources. Finally, DiNGO can rapidly process a large amount of data due to its multithread design.</p> Availability and Implementation <p>DiNGO is implemented in the JAVA language, and its source code, example datasets and instructions are available on GitHub: https://github.com/radoslav180/DiNGO. A pre-compiled jar file is available at: https://www.vin.bg.ac.rs/180/tools/DiNGO.php</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btz836", "PubYear": 2020, "Volume": "36", "Issue": "6", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory for Bioinformatics and Computational Chemistry, Institute of Nuclear Sciences Vinca, University of Belgrade, Mike <PERSON>-Alasa 12-14, Belgrade, Serbia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory for Bioinformatics and Computational Chemistry, Institute of Nuclear Sciences Vinca, University of Belgrade, Mike <PERSON>-Alasa 12-14, Belgrade, Serbia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory for Bioinformatics and Computational Chemistry, Institute of Nuclear Sciences Vinca, University of Belgrade, Mike <PERSON>-Alasa 12-14, Belgrade, Serbia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory for Bioinformatics and Computational Chemistry, Institute of Nuclear Sciences Vinca, University of Belgrade, Mike <PERSON>-Alasa 12-14, Belgrade, Serbia"}], "References": []}, {"ArticleId": 78326327, "Title": "Differential privacy under dependent tuples—the case of genomic privacy", "Abstract": "Motivation \n The rapid progress in genome sequencing has led to high availability of genomic data. Studying these data can greatly help answer the key questions about disease associations and our evolution. However, due to growing privacy concerns about the sensitive information of participants, accessing key results and data of genomic studies (such as genome-wide association studies) is restricted to only trusted individuals. On the other hand, paving the way to biomedical breakthroughs and discoveries requires granting open access to genomic datasets. Privacy-preserving mechanisms can be a solution for granting wider access to such data while protecting their owners. In particular, there has been growing interest in applying the concept of differential privacy (DP) while sharing summary statistics about genomic data. DP provides a mathematically rigorous approach to prevent the risk of membership inference while sharing statistical information about a dataset. However, DP does not consider the dependence between tuples in the dataset, which may degrade the privacy guarantees offered by the DP.\n \n \n Results \n In this work, focusing on genomic datasets, we show this drawback of the DP and we propose techniques to mitigate it. First, using a real-world genomic dataset, we demonstrate the feasibility of an inference attack on differentially private query results by utilizing the correlations between the entries in the dataset. The results show the scale of vulnerability when we have dependent tuples in the dataset. We show that the adversary can infer sensitive genomic data about a user from the differentially private results of a query by exploiting the correlations between the genomes of family members. Second, we propose a mechanism for privacy-preserving sharing of statistics from genomic datasets to attain privacy guarantees while taking into consideration the dependence between tuples. By evaluating our mechanism on different genomic datasets, we empirically demonstrate that our proposed mechanism can achieve up to 50% better privacy than traditional DP-based solutions.\n \n \n Availability and implementation \n https://github.com/nourmadhoun/Differential-privacy-genomic-inference-attack.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btz837", "PubYear": 2020, "Volume": "36", "Issue": "6", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Bilkent University, Bilkent, Ankara, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Bilkent University, Bilkent, Ankara, Turkey;Department of Electrical Engineering and Computer Science, Case Western Reserve University, Cleveland, OH, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Bilkent University, Bilkent, Ankara, Turkey"}], "References": []}, {"ArticleId": 78326402, "Title": "A Comprehensive Survey on Attacks, Security Issues and Blockchain Solutions for IoT and IIoT", "Abstract": "In recent years, the growing popularity of Internet of Things (IoT) is providing a promising opportunity not only for the development of various home automation systems but also for different industrial applications. By leveraging these benefits, automation is brought about in the industries giving rise to the Industrial Internet of Things (IIoT). IoT is prone to several cyberattacks and needs challenging approaches to achieve the desired security. Moreover, with the emergence of IIoT, the security vulnerabilities posed by it are even more devastating. Therefore, in order to provide a guideline to researchers, this survey primarily attempts to classify the attacks based on the objects of vulnerability. Subsequently, each of the individual attacks is mapped to one or more layers of the generalized IoT/IIoT architecture followed by a discussion on the countermeasures proposed in literature. Some relevant real-life attacks for each of these categories are also discussed. We further discuss the countermeasures proposed for the most relevant security threats in IIoT. A case study on two of the most important industrial IoT applications is also highlighted. Next, we explore the challenges brought by the centralized IoT/IIoT architecture and how blockchain can effectively be used towards addressing such challenges. In this context, we also discuss in detail one IoT specific Blockchain design known as Tangle, its merits and demerits. We further highlight the most relevant Blockchain-based solutions provided in recent times to counter the challenges posed by the traditional cloud-centered applications. The blockchain-related solutions provided in the context of two of the most relevant applications for each of IoT and IIoT is also discussed. Subsequently, we design a taxonomy of the security research areas in IoT/IIoT along with their corresponding solutions. Finally, several open research directions relevant to the focus of this survey are identified.", "Keywords": "IIoT ; Security ; Privacy ; Blockchain ; Smart Factory ; Smart Grid ; Supply Chain ; E-Healthcare ; VANET", "DOI": "10.1016/j.jnca.2019.102481", "PubYear": 2020, "Volume": "149", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Engineering Science and Technology, Howrah, India;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CSIRO, Data61, Australia and Indian Statistical Institute, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Engineering Science and Technology, Howrah, India"}], "References": [{"Title": "A new type of blockchain for secure message exchange in VANET", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "177", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Literature review of Industry 4.0 and related technologies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "1", "Page": "127", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 78326427, "Title": "Mobile application for the purpose of marketing, product distribution and location-based logistics for elderly farmers", "Abstract": "This research has developed a one-stop service supply chain mobile application for the purpose of marketing, product distribution and location-based logistics for elderly farmers and consumers in accordance with the Thailand 4.0 economic model. This is an investigation into the agricultural product distribution supply chain which focuses on marketing, distribution and logistics using the Dijkstra’s and Ant Colony Algorithms to respectively explore the major and minor product transport routes. The accuracy rate was determined to be 97%. The application is congruent with the product distribution, supply chain, in a value-based economy. The effectiveness of the mobile application was indicated to be at the highest level of results of learning outcomes, user comprehension and user experience of users. That is, the developed mobile application could be effectively used as a tool to support elderly farmers to distribute their agricultural products in the one-stop service supply chain which emphasizes marketing, distribution and location-based logistics for elderly farmers and consumers with respect to Thailand 4.0.", "Keywords": "Mobile application ; Elderly ; Supply chain ; Location-based logistics ; Distribution ; Dijkstra’s algorithm ; Ant Colony Algorithm", "DOI": "10.1016/j.aci.2019.11.001", "PubYear": 2023, "Volume": "19", "Issue": "1/2", "JournalId": 5816, "JournalTitle": "Applied Computing and Informatics", "ISSN": "2210-8327", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>, 1 U-Thong Nok Road, Dusit, Bangkok, Thailand;Corresponding author"}], "References": []}, {"ArticleId": 78326725, "Title": "Scalable parallel algorithms for maximum matching and Hamiltonian circuit in convex bipartite graphs", "Abstract": "Since bipartite convex graphs emerged from industrial applications, algorithms for this class of graphs have been devised in several research areas such as scheduling, DNA analysis, and constraint programming. A bipartite graph G = ( V , W , E ) is convex if there exists an ordering of the vertices of W such that, for each v ∈ V , the neighbors of v are consecutive in W . In this work we describe a coarse grained parallel algorithm for the maximum matching problem in a convex bipartite graph. For p processors, the algorithm runs in O ( ( | V | / p ) lg ⁡ ( | V | / p ) lg ⁡ p ) time and uses O ( lg ⁡ p ) communication rounds. We also address a well-known problem in the area of combinatorial optimization, the Hamiltonian circuit problem, presenting a sequential linear-time algorithm to determine if a convex bipartite graph has a Hamiltonian circuit. We further show how to efficiently implement both algorithms in PRAM and coarse grained parallel models. Experimental tests performed on commercial machines show the algorithms are robust and scalable.", "Keywords": "Convex bipartite graphs ; Graph matching ; Linear-time Hamiltonian circuit ; Coarse grained parallel computing", "DOI": "10.1016/j.tcs.2019.10.042", "PubYear": 2020, "Volume": "804", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Federal University of Mato Grosso do Sul, Faculdade de Computação, Cx postal 549, 79070-900, Campo Grande, MS, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal University of Mato Grosso do Sul, Faculdade de Computação, Cx postal 549, 79070-900, Campo Grande, MS, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of São Paulo, Departamento de Ciência da Computação, Rua do Matão, 1010, 05508-090, S. Paulo, SP, Brazil"}], "References": []}, {"ArticleId": 78326909, "Title": "Antecedents of social media activism: an analytical study of a controversial topic", "Abstract": "Using a data mining and deep machine learning technology and method, we conducted a multi-staged investigation of whether social media influenced people’s opinions or merely confirmed already held beliefs, and furthermore, whether those social media conversations lead to people taking increasingly affirmative actions. We found that social media did tend to reinforce beliefs, but there were exceptions depending on certain personality antecedents. Additionally, the results were moderated by the affect intensity of participant sentiments. Building from these findings, we further studied whether or not people would take increasingly aggressive actions. We found that personality characteristics as an interaction with how strongly they felt about the subject, positively or negatively, did cause some people to take increasingly active measures.", "Keywords": "Social media influences ; natural language processing ; social media activism", "DOI": "10.1080/0144929X.2019.1687755", "PubYear": 2021, "Volume": "40", "Issue": "3", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Technology Management, Texas A&M University, College Station, TX, USA"}], "References": []}, {"ArticleId": 78327131, "Title": "Comparative study of FFA architectures using different multiplier and adder topologies", "Abstract": "<p>Parallel FIR filter is the prime block of many modern communication application such as MIMO, multi-point transceivers etc. But hardware replication problem of parallel techniques make the system more bulky and costly. Fast FIR algorithm (FFA) gives the best alternative to traditional parallel techniques. In this paper, FFA based FIR structures with different topologies of multiplier and adder are implemented. To optimize design different multiplication technique like add and shift method, Vedic multiplier and booth multiplier are used for computation. Various adders such as carry select adder, carry save adder and Han-Carlson adder are analyzed for improved performance of the FFA structure. The basic objective is to investigate the performance of these designs for the tradeoffs between area, delay and power dissipation. Comparative study is carried out among conventional and different proposed designs. The advantage of presented work is that; based on the constraints, one can select the suitable design for specific application. It also fulfils the literature gap of critical analysis of FPGA implementation of FFA architecture using different multiplier and adder topologies. Xilinx Vivado HLS tool is used to implement the proposed designs in VHDL.</p>", "Keywords": "", "DOI": "10.1007/s00542-019-04678-8", "PubYear": 2020, "Volume": "26", "Issue": "5", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics Engineering, Rajasthan Technical University, Kota, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics Engineering, Rajasthan Technical University, Kota, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Birla Institute of Technology, Mesra, India"}], "References": []}, {"ArticleId": 78327375, "Title": "Parameter Estimation for the Exponentiated Kumar<PERSON>wamy-Power Function Distribution Based on Order Statistics with Application", "Abstract": "<p>Exponentiated Kumaraswamy-power function (EKPF) distribution has been proposed recently by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (Hacet J Math Stat 46:277–292, 2017) as a quite flexible in terms of probability density and hazard rate functions than power function distribution. In this paper, we obtain the explicit expressions for the single, double (product), triple and quadruple moments and moment generating function for single, double, triple and quadruple of order statistics of the EKPF distribution. By using these relations, we have tabulated the means and variances of order statistics from samples of sizes up to 10 for various values of the parameters. We use five frequentist estimation methods to estimate the unknown parameters and a simulation study is used to compare the performance of the different estimators. Finally, we analyse a real data set for illustrative purpose.</p>", "Keywords": "Exponentiated Kumar<PERSON>wamy-power function; Order statistics; Moments and moment generating function; Estimations; Monte Carlo simulation", "DOI": "10.1007/s40745-019-00233-4", "PubYear": 2021, "Volume": "8", "Issue": "4", "JournalId": 6800, "JournalTitle": "Annals of Data Science", "ISSN": "2198-5804", "EISSN": "2198-5812", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics, Central University of Haryana, Adalpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, University of Delhi, New Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics, Faculty of Science, King Abdulazi<PERSON> University, Jeddah, Kingdom of Saudi Arabia;Department of Statistics, Faculty of Commerce, Zagazig University, Zagazig, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, Faculty of Commerce, Zagazig University, Zagazig, Egypt"}], "References": []}, {"ArticleId": 78327392, "Title": "Community detection in signed networks by relaxing modularity optimization with orthogonal and nonnegative constraints", "Abstract": "<p>Community detection in networks including singed edges is a primary challenge that has already attracted substantial attention. In this paper, we show that this task could be reformulated as a combinatorial optimization concerning the trace of the signed modularity matrix. Keeping the orthogonal and nonnegative constraints in the relaxation, we propose a multiplicative update rule, named the SMON algorithm, which results in a solution that is a close approximation to the genuine community indication matrix. In addition, the rows of the solution can be referred to as the probabilities of corresponding vertex falling into each community, which can help us to discover the overlapping community structure of the network and identify vertices that reside on the watersheds between different communities. Experimental results on real-life social networks as well as synthetic signed networks verify that our method is effective and superior to the existing approaches.</p>", "Keywords": "Modularity optimization; Community detection; Relaxed algorithm; Orthogonal and nonnegative constraints; Signed networks", "DOI": "10.1007/s00521-019-04597-9", "PubYear": 2020, "Volume": "32", "Issue": "14", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Kunming University, Kunming, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Kunming University, Kunming, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Economics, Shenzhen University, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Kunming University, Kunming, China"}], "References": []}, {"ArticleId": 78327857, "Title": "QMEANDisCo—distance constraints applied on model quality estimation", "Abstract": "Abstract \n \n Motivation \n Methods that estimate the quality of a 3D protein structure model in absence of an experimental reference structure are crucial to determine a model’s utility and potential applications. Single model methods assess individual models whereas consensus methods require an ensemble of models as input. In this work, we extend the single model composite score QMEAN that employs statistical potentials of mean force and agreement terms by introducing a consensus-based distance constraint (DisCo) score.\n \n \n Results \n DisCo exploits distance distributions from experimentally determined protein structures that are homologous to the model being assessed. Feed-forward neural networks are trained to adaptively weigh contributions by the multi-template DisCo score and classical single model QMEAN parameters. The result is the composite score QMEANDisCo, which combines the accuracy of consensus methods with the broad applicability of single model approaches. We also demonstrate that, despite being the de-facto standard for structure prediction benchmarking, CASP models are not the ideal data source to train predictive methods for model quality estimation. For performance assessment, QMEANDisCo is continuously benchmarked within the CAMEO project and participated in CASP13. For both, it ranks among the top performers and excels with low response times.\n \n \n Availability and implementation \n QMEANDisCo is available as web-server at https://swissmodel.expasy.org/qmean. The source code can be downloaded from https://git.scicore.unibas.ch/schwede/QMEAN.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btz828", "PubYear": 2020, "Volume": "36", "Issue": "6", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Biozentrum, University of Basel, Basel 4056, Switzerland;SIB Swiss Institute of Bioinformatics, Basel 4056, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Biozentrum, University of Basel, Basel 4056, Switzerland;SIB Swiss Institute of Bioinformatics, Basel 4056, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Biozentrum, University of Basel, Basel 4056, Switzerland;SIB Swiss Institute of Bioinformatics, Basel 4056, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Biozentrum, University of Basel, Basel 4056, Switzerland;SIB Swiss Institute of Bioinformatics, Basel 4056, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Biozentrum, University of Basel, Basel 4056, Switzerland;SIB Swiss Institute of Bioinformatics, Basel 4056, Switzerland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Biozentrum, University of Basel, Basel 4056, Switzerland;SIB Swiss Institute of Bioinformatics, Basel 4056, Switzerland"}], "References": []}, {"ArticleId": 78327936, "Title": "Sparse matrix multiplication and triangle listing in the Congested Clique model", "Abstract": "We show how to multiply two n × n matrices S and T over semirings in the Congested Clique model, where n nodes communicate in a fully connected synchronous network using O ( log ⁡ n ) -bit messages, within O ( n z ( S ) 1 / 3 n z ( T ) 1 / 3 / n + 1 ) rounds of communication, where n z ( S ) and n z ( T ) denote the number of non-zero elements in S and T , respectively. By leveraging the sparsity of the input matrices, our algorithm greatly reduces communication costs compared with general multiplication algorithms [<PERSON><PERSON><PERSON><PERSON> et al. (2015) [9] ], and thus improves upon the state-of-the-art for matrices with o ( n 2 ) non-zero elements. Moreover, our algorithm exhibits the additional strength of surpassing previous solutions also in the case where only one of the two matrices is such. Particularly, this allows to efficiently raise a sparse matrix to a power greater than 2. As applications, we show how to speed up the computation on non-dense graphs of 4-cycle counting and all-pairs-shortest-paths. Our algorithmic contribution is a new deterministic method of restructuring the input matrices in a sparsity-aware manner, which assigns each node with element-wise multiplication tasks that are not necessarily consecutive but guarantee a balanced element distribution, providing for communication-efficient multiplication. Moreover, this new deterministic method for restructuring matrices may be used to restructure the adjacency matrix of input graphs, enabling faster deterministic solutions for graph related problems. As an example, we present a new sparsity aware, deterministic algorithm which solves the triangle listing problem in O ( m / n 5 / 3 + 1 ) rounds, a complexity that was previously obtained by a randomized algorithm [Pandurangan et al. (2018) [26] ], and that matches the known lower bound of Ω ˜ ( n 1 / 3 ) when m = n 2 of [Izumi and Le Gall (2017) [19] , Pandurangan et al. (2018) [26] ]. Naturally, our triangle listing algorithm also implies triangle counting within the same complexity of O ( m / n 5 / 3 + 1 ) rounds, which is (possibly more than) a cubic improvement over the previously known deterministic O ( m 2 / n 3 ) -round algorithm [Dolev et al. (2012) [12] ].", "Keywords": "Distributed algorithms ; Congested clique ; Matrix multiplication ; Triangle listing", "DOI": "10.1016/j.tcs.2019.11.006", "PubYear": 2020, "Volume": "809", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Technion, Israel;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Technion, Israel"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Technion, Israel"}], "References": []}, {"ArticleId": 78327958, "Title": "Dynamic Performance Optimization of a Novel 8-SPU Parallel Walking Mechanism", "Abstract": "Abstract <p>Dynamic performance as one of the important properties of the parallel mechanism cannot be ignored, which is usually illustrated through dynamic performance analysis with the aid of index. Therefore, to develop reasonable dynamic performance indices is of great theoretical and practical significance for the parallel mechanism. In this paper, the above issues are discussed by taking the parallel mechanism designed by our research group as a study object. First, on the basis of considering the driving motor, kinematics dexterity and dynamic dexterity based on the global kinematics condition index and the global dynamic condition index are analyzed, respectively, in the workspace. Second, a novel diagonally dominant index (DDI) is presented in terms of the equivalent inertia parameter in joint space, and the proposed approach gives full consideration to minimize the inter-chain coupling effects. Furthermore, the parallel mechanism is optimized by means of taking the volume of the workspace, the global kinematics condition index, the global dynamic condition index, and the diagonally dominant index as objective functions. The optimization result shows that the larger workspace, higher dexterity, and smaller DDI are obtained to further improve the work capability and dynamic property of the mechanism. Finally, a real and novel 8-SPU (spherical pair, prismatic pair, and universal pair) parallel walking mechanism is manufactured in terms of the optimized architecture parameters.</p>", "Keywords": "parallel walking mechanism ; dynamic performance ; dynamic dexterity ; diagonally dominant index ; multi-objective optimization ; computer-aided design ; manufacturing automation", "DOI": "10.1115/1.4045409", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 9341, "JournalTitle": "Journal of Computing and Information Science in Engineering", "ISSN": "1530-9827", "EISSN": "1944-7078", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automotive Engineering, Hubei Key Laboratory of Advanced Technology for Automotive Components;;Hubei Collaborative Innovation Center for Automotive Components Technology, Wuhan University of Technology, Wuhan 430070, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automotive Engineering, Hubei Key Laboratory of Advanced Technology for Automotive Components;;Hubei Collaborative Innovation Center for Automotive Components Technology, Wuhan University of Technology, Wuhan 430070, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Foreign Languages, Wuhan University of Technology, Wuhan 430070, China"}], "References": []}, {"ArticleId": 78328011, "Title": "Automated Classification of Manufacturing Process Capability Utilizing Part Shape, Material, and Quality Attributes", "Abstract": "Abstract \n The ability to classify the capabilities of different manufacturing processes based on computer-aided design (CAD) models of parts is a key missing link in cybermanufacturing. In this paper, we present a one-step approach for automatically classifying the capabilities of three discrete manufacturing processes—milling, turning, and casting—based on part shape, quality, and material property attributes. Specifically, our approach utilizes machine learning to classify manufacturing process capabilities of these processes in terms of part shape attributes such as curvature, rotational symmetry, and pairwise surface point distance (D2) histogram computed from CAD models, as well as part quality (surface finish and size tolerance) and material property attributes of parts. In this manner, historical data can be utilized to classify the capabilities of manufacturing processes. We show that it is possible to achieve high classification accuracies—88% and 83% for the training and test data sets, respectively—using this approach. In addition, a key insight gained from this work is that part shape attributes alone are inadequate for discriminating between the capabilities of the manufacturing processes considered. Specifically, the inclusion of material property and part quality attributes enables the classifier to predict viable manufacturing processes that would otherwise be ignored using shape attributes alone. Future extensions of this work will include enriching the classification process with additional attributes such as production cost, as well as alternative classification methods.", "Keywords": "cybermanufacturing ; data-driven engineering ; machine learning for engineering applications ; manufacturing planning", "DOI": "10.1115/1.4045410", "PubYear": 2020, "Volume": "20", "Issue": "2", "JournalId": 9341, "JournalTitle": "Journal of Computing and Information Science in Engineering", "ISSN": "1530-9827", "EISSN": "1944-7078", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> School of Mechanical Engineering, Georgia Institute of Technology, Atlanta, GA 30332"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> School of Mechanical Engineering, Georgia Institute of Technology, Atlanta, GA 30332"}, {"AuthorId": 3, "Name": "Shr<PERSON>s <PERSON>", "Affiliation": "<PERSON> School of Mechanical Engineering, Georgia Institute of Technology, Atlanta, GA 30332"}], "References": []}, {"ArticleId": 78328076, "Title": "A new correlation coefficient of the Pythagorean fuzzy sets and its applications", "Abstract": "<p>In this paper, we propose a new correlation coefficient between Pythagorean fuzzy sets. We then use this new result to compute some examples through which we find that it benefits from such an outcome with some well-known results in the literature. In probability and statistical theory, the correlation coefficient indicates the strength of the linear correlation between two random variables. The correlation coefficient is equal to one in the case of a linear correlation and − 1 in the case of a linear inverse correlation. Other values in the range (− 1, 1) indicate the degree of linear dependence between variables. The closer the coefficient is to − 1 and 1, the stronger the correlation between variables. As in statistics with real variables, we refer to variance and covariance between two intuitionistic fuzzy sets. Then, we determined the formula for calculating the correlation coefficient based on the variance and covariance of the intuitionistic fuzzy set, and the value of this correlation coefficient is in [− 1, 1]. We also commented on the linear relationship between fuzzy sets affecting their correlation coefficients through examples to show the usefulness in the proposed new measure. Then, we develop this direction to build correlation coefficients between the interval-valued intuitionistic fuzzy sets and apply it in the pattern recognition problem.</p>", "Keywords": "Pythagorean fuzzy set; Interval-valued Pythagorean fuzzy set; Variance; Covariance; Correlation coefficient", "DOI": "10.1007/s00500-019-04457-7", "PubYear": 2020, "Volume": "24", "Issue": "13", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Vietnam National University of Agriculture, Ha Noi, Viet Nam"}], "References": []}, {"ArticleId": 78328086, "Title": "A new fixed-time stabilization approach for neural networks with time-varying delays", "Abstract": "<p>In this article, we investigate the problem of fixed-time stabilization (FXTSB) of delayed neural networks (DNNs). Firstly, some new general conditions on the control law are established to guarantee the FXTSB of DNNs. Secondly, specific linear matrix inequalities FXTSB conditions are obtained by constructing different kinds of controller which include a delay-dependent and free ones. Furthermore, the FXTSB of DNNs with unbounded activation functions is investigated and the restriction of differentiability of the time-varying delay is removed. Finally, three numerical examples accompanied by graphical illustrations are given to illuminate our theoretical results and based on chaotic synchronization, our approach has been successfully applied to secure communication which can be realized with a time delay.</p>", "Keywords": "Neural networks; Fixed-time stabilization; Time delay systems; LMI; Settling time; Delay-free controller", "DOI": "10.1007/s00521-019-04586-y", "PubYear": 2020, "Volume": "32", "Issue": "8", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Units of Mathematics and Applications UR13ES47, Department of Mathematics, Faculty of Sciences of Bizerta, University of Carthage, Bizerta, Tunisia"}, {"AuthorId": 2, "Name": "Foued <PERSON>", "Affiliation": "Research Units of Mathematics and Applications UR13ES47, Department of Mathematics, Faculty of Sciences of Bizerta, University of Carthage, Bizerta, Tunisia"}], "References": []}, {"ArticleId": 78328235, "Title": "A multi-sensor-based evaluation of the morphometric characteristics of Opa river basin in Southwest Nigeria", "Abstract": "Studies have shown that many river basins in the sub-Saharan Africa are largely unmonitored, partly because they are poorly or totally ungauged. In this study, remote sensing products (Landsat, Advanced Spaceborne Thermal Emission and Reflection Radiometer; ASTER and Shuttle Radar Topography Mission; SRTM) that are freely available in the region were harnessed for the monitoring of Opa river basin in southwestern Nigeria. The remote sensing products were complementarily used with topographical sheets (1:50,000), ground based observation and global positioning systems to determine selected morphometric characteristics as well as changes in landuse/landcover and its impact on peak runoff in the Opa river basin. Results showed that the basin is a 5<sup>th</sup> order basin whose land area has been subjected to different natural and anthropogenic influences within the study period. Urbanisation is a major factor that threatens the basin with degradation and observed changes, and the threats are expected to become worse if restoration is not considered from some tributaries. The study concluded that commentary use of available remote sensing products in the region will provide an important level of decision support information for management and monitoring of river basins.", "Keywords": "River basins monitoring and management ; remote sensors ; morphological characteristics ; digital elevation models", "DOI": "10.1080/19479832.2019.1683622", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 26673, "JournalTitle": "International Journal of Image and Data Fusion", "ISSN": "1947-9832", "EISSN": "1947-9824", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography, Obafemi Awolowo University, Ile-Ife, Nigeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Geography and Planning, Abia State University, Uturu, Nigeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography, Obafemi Awolowo University, Ile-Ife, Nigeria"}], "References": []}, {"ArticleId": 78328394, "Title": "Optimal container resource allocation in cloud architecture: A new hybrid model", "Abstract": "A huge variety of fields and industries depend upon cloud computing based microservice due to its high-performance capability. Also, the merit of container usage is enormous; it enable larger portability, easier and faster deployment and restricted overheads. However, the rapid evolution causes issues in terms of container automation and management, Till now, a number of research works has concentrated on solving the open issues in container automation and management. In fact, container resource allocation is the major key hole for cloud providers since it directly influences the resource consumption and system performance. In this manner, this paper introduces a new optimized container resource allocation model by proposing a new optimization concept. To make the possibility of optimal container resource allocation, a new hybridized algorithm is implanted; namely, Whale Random update assisted Lion Algorithm (WR-LA), which is the hybrid form of Lion Algorithm (LA) and Whale Optimization Algorithm (WOA) is introduced. Moreover, the solution of optimized resource allocation is made by considering objectives like Threshold Distance, Balanced Cluster Use, System Failure, and Total Network Distance, respectively. Finally, the performance of the proposed model is compared over other conventional models and proves its superiority.", "Keywords": "Cloud computing ; Container resource allocation ; Microservices ; Optimization ; Lion algorithm ; Whale optimization algorithm", "DOI": "10.1016/j.jksuci.2019.10.009", "PubYear": 2022, "Volume": "34", "Issue": "5", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "Kapil N. <PERSON>", "Affiliation": "V.J.T.I., Mumbai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "V.J.T.I., Mumbai, India"}], "References": []}, {"ArticleId": 78328583, "Title": "Cognitive cryptography for data security in cloud computing", "Abstract": "<p>This paper presents new approach for creation of advanced multilevel user authentication protocols using hybrid CAPTCHA codes. Such codes will define a new class of cognitive CAPTCHAs, which, during verification, require from users special skills and knowledge, which is necessary for proper authentication. This way of authentication may be oriented for providing data access only for a special group of experts or trusted users who represent particular expertise areas and have special perception abilities and knowledge. For presented authentication protocols, possible examples of applications will be presented, and security features will be described. Presented innovative verification codes will considerably expand the existed security protocols toward the creation of cognitive‐based approaches oriented for user authentication. Such approaches define novel classes of cryptographic procedures, which can use personal features and perception skills.</p>", "Keywords": "authentication procedures;cognitive CAPTCHA;security protocols", "DOI": "10.1002/cpe.5557", "PubYear": 2020, "Volume": "32", "Issue": "18", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "Urs<PERSON>la <PERSON>la", "Affiliation": "Pedagogical University of Krakow, Kraków, Poland; Urszula Ogiela, Pedagogical University of Krakow, Podchorążych 2 Street, 30‐084 Kraków, Poland."}], "References": []}, {"ArticleId": 78328601, "Title": "Research on digital production technology for traditional manufacturing enterprises based on industrial Internet of Things in 5G era", "Abstract": "<p>Based on the analysis of 5G and Internet of Things technology, this paper proposes the reference architecture of smart factory and its application path for traditional manufacturing enterprises in China, in which the intelligent manufacturing workshop is the core component of smart factory. The Internet of Things technology combined the advanced technologies (Industrial Big Data, WSN, RFID, Cloud Computing Platform) and provides hardware network foundation and technical theory for designing the real-time tracking and monitoring system of intelligent workshop products. The developed system has the advantages of low cost, rapid deployment, and convenient expansion, which traditional manufacturing enterprises realize intelligent management based on IoT application platform.</p>", "Keywords": "5G; Industry 4.0; Internet of Things; Product tracking; Smart factory; Workshop monitoring", "DOI": "10.1007/s00170-019-04284-y", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Management School, Hangzhou Dianzi University, Hangzhou, China;The Research Center of Information Technology & Economic and Social Development, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Management School, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Management School, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Management School, Hangzhou Dianzi University, Hangzhou, China"}], "References": []}, {"ArticleId": 78328667, "Title": "Simulations, fabrication, and characterization of d31 mode piezoelectric vibration energy harvester", "Abstract": "<p>This paper presents the development work on d <sub>31</sub> mode piezoelectric vibration energy harvester. The device structure consists of a fixed-free type cantilever beam with a seismic mass attached at the free end of the beam. On top of the cantilever beam, a ZnO piezoelectric layer is sandwiched between two metal electrodes. The harvester is designed using an FEM tool CoventorWare. The simulations are carried out to estimate the resonance frequency, mises stress, optimal load resistance, and generated power. The optimized design is then implemented using a five mask SOI bulk micromachining process. The fabricated harvester is characterized for frequency response using Polytec MSA-500 Micro System Analyzer. The experimental resonance frequency is found to be 235.38 Hz. The harvester is also evaluated for generated open-circuit voltage when subjected to harmonic acceleration. The open-circuit peak-to-peak voltage for 0.1 g acceleration is found to be 306 mV.</p>", "Keywords": "", "DOI": "10.1007/s00542-019-04684-w", "PubYear": 2020, "Volume": "26", "Issue": "5", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CSIR-Central Electronics Engineering Research Institute (CEERI), Pilani, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "CSIR-Central Electronics Engineering Research Institute (CEERI), Pilani, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSIR-Central Electronics Engineering Research Institute (CEERI), Pilani, India"}], "References": []}, {"ArticleId": 78328727, "Title": "How successfully is open-source research software adopted? Results and implications of surveying the users of a sensitivity analysis toolbox", "Abstract": "Open-source research software is an important element of open science. While the number of software packages made available by researchers is increasing, there has been little analysis about their subsequent uptake. We collect basic information about prospective users when sharing our open-source sensitivity analysis toolbox. This enabled us to carry out a user survey to assess adoption success – beyond simply counting download numbers. Survey results confirm the key role of extensive documentation to ensure adoption, to enhance learning and to enable research implementation. We found that workflows are an effective tool to guide users to tailor methods to their problems. However, workflows also need to include guidance for interpretation of results, otherwise sophisticated functionalities are overlooked as their value is unclear. Developing effective documentation requires significant time investment but is essential if the ultimate aim of open research software is to promote the adoption of scientific methodologies and best practices.", "Keywords": "Research software ; Open-source software ; Reproducibility ; Workflows ; Software documentation", "DOI": "10.1016/j.envsoft.2019.104579", "PubYear": 2020, "Volume": "124", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, University of Bristol, UK;Cabot Institute, University of Bristol, UK;Corresponding author. Department of Civil Engineering, University of Bristol, UK."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, University of Bristol, UK;Department of Computational Hydrosystems, UFZ-Helmholtz-Zentrum für Umweltforschung, Leipzig, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, University of Bristol, UK;Cabot Institute, University of Bristol, UK"}], "References": []}, {"ArticleId": 78328856, "Title": "How relative mobility affects the aggregate throughput of VANET cluster?", "Abstract": "<p>Clustering is considered an important issue in Vehicular Ad‐hoc Networks (VANETs) to ensure the network robustness and throughput. Nevertheless, in the literature, there is still a lack of understanding of how relative mobility between cluster members and the cluster head affects the throughput of VANET clusters. In this letter, we present a model to characterize the throughput of VANET clusters, by taking into account the relative mobility effect, which implicitly accounts for the <PERSON><PERSON><PERSON> effect. The numerical analysis shows that there is a large gap between the throughput of the model with/without consideration of relative mobility, which indicates the necessity to develop clustering algorithms that minimize intra‐cluster mobility.</p>", "Keywords": "cluster;performance modeling,mobility;vehicular networks", "DOI": "10.1002/itl2.133", "PubYear": 2020, "Volume": "3", "Issue": "1", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "INFRES, Télécom Paris, Palaiseau, France and VEDECOM, Versailles, France; INFRES, Télécom Paris, Palaiseau, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "INFRES, Télécom Paris, Palaiseau, France"}, {"AuthorId": 3, "Name": "Houda Labiod", "Affiliation": "INFRES, Télécom Paris, Palaiseau, France"}], "References": []}, {"ArticleId": ********, "Title": "Smartphone‐based object recognition with embedded machine learning intelligence for unmanned aerial vehicles", "Abstract": "<p>Existing artificial intelligence solutions typically operate in powerful platforms with high computational resources availability. However, a growing number of emerging use cases such as those based on unmanned aerial systems (UAS) require new solutions with embedded artificial intelligence on a highly mobile platform. This paper proposes an innovative UAS that explores machine learning (ML) capabilities in a smartphone‐based mobile platform for object detection and recognition applications. A new system framework tailored to this challenging use case is designed with a customized workflow specified. Furthermore, the design of the embedded ML leverages TensorFlow, a cutting‐edge open‐source ML framework. The prototype of the system integrates all the architectural components in a fully functional system, and it is suitable for real‐world operational environments such as seek and rescue use cases. Experimental results validate the design and prototyping of the system and demonstrate an overall improved performance compared with the state of the art in terms of a wide range of metrics.</p>", "Keywords": "image processing;machine learning;object detection and recognition;smartphone;unmanned aerial vehicle;image processing;smartphone", "DOI": "10.1002/rob.21921", "PubYear": 2020, "Volume": "37", "Issue": "3", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Science and Technology, School of Engineering and Computing, University of the West of Scotland, Paisley, UK"}, {"AuthorId": 2, "Name": "Pablo Casaseca‐de‐la‐Higuera", "Affiliation": "Laboratorio de Procesado de Imagen, ETSI Telecomunicación, Universidad de Valladolid, Valladolid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Science and Technology, School of Engineering and Computing, University of the West of Scotland, Paisley, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, National College of Ireland, Dublin, Ireland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Science and Technology, School of Engineering and Computing, University of the West of Scotland, Paisley, UK"}], "References": []}, {"ArticleId": 78329005, "Title": "SigHotSpotter: scRNA-seq-based computational tool to control cell subpopulation phenotypes for cellular rejuvenation strategies", "Abstract": "Abstract Summary <p>Single-cell RNA-sequencing is increasingly employed to characterize disease or ageing cell subpopulation phenotypes. Despite exponential increase in data generation, systematic identification of key regulatory factors for controlling cellular phenotype to enable cell rejuvenation in disease or ageing remains a challenge. Here, we present SigHotSpotter, a computational tool to predict hotspots of signaling pathways responsible for the stable maintenance of cell subpopulation phenotypes, by integrating signaling and transcriptional networks. Targeted perturbation of these signaling hotspots can enable precise control of cell subpopulation phenotypes. SigHotSpotter correctly predicts the signaling hotspots with known experimental validations in different cellular systems. The tool is simple, user-friendly and is available as web-server or as stand-alone software. We believe SigHotSpotter will serve as a general purpose tool for the systematic prediction of signaling hotspots based on single-cell RNA-seq data, and potentiate novel cell rejuvenation strategies in the context of disease and ageing.</p> Availability and implementation <p>SigHotSpotter is at https://SigHotSpotter.lcsb.uni.lu as a web tool. Source code, example datasets and other information are available at https://gitlab.com/srikanth.ravichandran/sighotspotter.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btz827", "PubYear": 2020, "Volume": "36", "Issue": "6", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computational Biology Group, Luxembourg Centre for Systems Biomedicine (LCSB), University of Luxembourg, 7, avenue des Hauts-Fourneaux, L-4362 Esch-sur-Alzette Luxembourg"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computational Biology Group, Luxembourg Centre for Systems Biomedicine (LCSB), University of Luxembourg, 7, avenue des Hauts-Fourneaux, L-4362 Esch-sur-Alzette Luxembourg;Institute for Globally Distributed Open Research and Education (IGDORE)"}, {"AuthorId": 3, "Name": "Antonio del Sol", "Affiliation": "Computational Biology Group, Luxembourg Centre for Systems Biomedicine (LCSB), University of Luxembourg, 7, avenue des Hauts-Fourneaux, L-4362 Esch-sur-Alzette Luxembourg;Institute for Globally Distributed Open Research and Education (IGDORE);IKERBASQUE, Basque Foundation for Science, Bilbao, Spain;CIC bioGUNE, Derio, Spain"}], "References": []}, {"ArticleId": 78329006, "Title": "HiCBricks: building blocks for efficient handling of large Hi-C datasets", "Abstract": "Abstract Summary <p>Genome-wide chromosome conformation capture based on high-throughput sequencing (Hi-C) has been widely adopted to study chromatin architecture by generating datasets of ever-increasing complexity and size. HiCBricks offers user-friendly and efficient solutions for handling large high-resolution Hi-C datasets. The package provides an R/Bioconductor framework with the bricks to build more complex data analysis pipelines and algorithms. HiCBricks already incorporates functions for calling domain boundaries and functions for high-quality data visualization.</p> Availability and implementation <p>http://bioconductor.org/packages/devel/bioc/html/HiCBricks.html.</p> Contact <p><EMAIL></p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btz808", "PubYear": 2020, "Volume": "36", "Issue": "6", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IFOM, The FIRC Institute of Molecular Oncology, Milan, Italy"}, {"AuthorId": 2, "Name": "Ilario <PERSON>", "Affiliation": "IFOM, The FIRC Institute of Molecular Oncology, Milan, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IFOM, The FIRC Institute of Molecular Oncology, Milan, Italy"}, {"AuthorId": 4, "Name": "Francesco <PERSON>", "Affiliation": "IFOM, The FIRC Institute of Molecular Oncology, Milan, Italy;Institute of Molecular Genetics, National Research Council, Pavia, Italy"}], "References": []}, {"ArticleId": 78329111, "Title": "Coverage trajectory planning for a bush trimming robot arm", "Abstract": "<p>A novel motion planning algorithm for robotic bush trimming is presented. The algorithm is based on an optimal route search over a graph. Differently from other works in robotic surface coverage, it entails both accuracy in the surface sweeping task and smoothness in the motion of the robot arm. The proposed method requires the selection of a custom objective function in the joint space for optimal node traversal scheduling, as well as a kinematically constrained time interpolation. The algorithm was tested in simulation using a model of the Jaco arm and three target bush shapes. Analysis of the simulated motions showed how, differently from classical coverage techniques, the proposed algorithm is able to ensure high tool positioning accuracy while avoiding excessive arm motion jerkiness. It was reported that forbidding manipulation posture changes during the cutting phase of the motion is a key element for task accuracy, leading to a decrease of the tool positioning error up to 90%. Furthermore, the algorithm was validated in a real‐world trimming scenario with boxwood bushes. A target of 20 mm accuracy was proposed for a trimming result to be considered successful. Results showed that on average 82% of the bush surface was affected by trimming, and 51% of the trimmed surface was cut within the desired level of accuracy. Despite the fact that the trimming accuracy turned out to be lower than the stated requirements, it was found out this was mainly a consequence of the inaccurate, early stage vision system employed to compute the target trimming surface. By contrast, the trimming motion planning algorithm generated trajectories that smoothly followed their input target and allowed effective branch cutting.</p>", "Keywords": "agriculture;manipulators;planning", "DOI": "10.1002/rob.21917", "PubYear": 2020, "Volume": "37", "Issue": "2", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Farm Technology Group, Wageningen University & Research, Wageningen, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Livestock Robotics, Wageningen, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Farm Technology Group, Wageningen University & Research, Wageningen, The Netherlands"}], "References": []}, {"ArticleId": 78329637, "Title": "Dynamic auto-weighted multi-view co-clustering", "Abstract": "To exploit the complementary information of multi-view data, many weighted multi-view clustering methods have been proposed and have demonstrated impressive performance. However, most of these methods learn the view weights by introducing additional parameters, which can not be easily obtained in practice. Moreover, they all simply apply the learned weights on the original feature representation of each view, which may deteriorate the clustering performance in the case of high-dimensional data with redundancy and noise. In this paper, we extend information bottleneck co-clustering into a multi-view framework and propose a novel dynamic auto-weighted multi-view co-clustering algorithm to learn a group of weights for views with no need for extra weight parameters. By defining the new concept of the discrimination-compression rate, we quantify the importance of each view by evaluating the discriminativeness of the compact features (i.e., feature-wise clusters) of the views. Unlike existing weighted methods that impose weights on the original feature representations of multiple views, we apply the learned weights on the discriminative ones, which can reduce the negative impact of noisy features in high-dimensional data. To solve the optimization problem, a new two-step sequential method is designed. Experimental results on several datasets show the advantages of the proposed algorithm. To our knowledge, this is the first work incorporating weighting scheme into multi-view co-clustering framework.", "Keywords": "Multi-view co-clustering ; Information bottleneck ; Weighting", "DOI": "10.1016/j.patcog.2019.107101", "PubYear": 2020, "Volume": "99", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Zhengzhou University, Zhengzhou, 450001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Zhengzhou University, Zhengzhou, 450001, China"}, {"AuthorId": 3, "Name": "Yangdong Ye", "Affiliation": "School of Information Engineering, Zhengzhou University, Zhengzhou, 450001, China;Corresponding author."}], "References": []}, {"ArticleId": 78329646, "Title": "Entropy preserving low dissipative shock capturing with wave-characteristic based sensor for high-order methods", "Abstract": "Shock capturing procedures are required to stabilise numerical simulations of gas dynamics problems featuring non-isentropic discontinuities. In the present work, particular attention is focused on the expected non-monotonicity of the entropy profile across shock waves. A peculiar physical property which was not considered so far in the evaluation of shock capturing techniques. In the context of high-order spectral difference methods and using most recent discontinuity sensors based on the decay rate of the modes of the amplitude of characteristic waves, results show how the choice of a physical-based procedure (additional viscosity) returns a better description of shocks compared to approaches relying on the direct addition of a Laplacian term in the solved equations. Various canonical compressible flows are simulated, in one-, two-, and three-dimensional setups, to illustrate the performance and flexibility of the proposed approach. It is shown that the addition of a well-calibrated bulk viscosity is capable of smoothing out discontinuities without an excessive damping of vortical structures, preserving also specific compressible flow physics, as the non-monotonic entropy profiles through the shocks.", "Keywords": "High-order methods ; Shock capturing methods ; Shock detection", "DOI": "10.1016/j.compfluid.2019.104357", "PubYear": 2020, "Volume": "197", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CORIA - CNRS, Normandie Université, INSA de Rouen, Technopole du Madrillet, Saint-Etienne-du-Rouvray BP 8 76801, France;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CORIA - CNRS, Normandie Université, INSA de Rouen, Technopole du Madrillet, Saint-Etienne-du-Rouvray BP 8 76801, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CORIA - CNRS, Normandie Université, INSA de Rouen, Technopole du Madrillet, Saint-Etienne-du-Rouvray BP 8 76801, France"}], "References": []}, {"ArticleId": 78329876, "Title": "Applying Social Choice Theory to Solve Engineering Multi-objective Optimization Problems", "Abstract": "Abstract <p>Multi-objective optimization problems usually do not have a single unique optimal solution, for either discrete or continuous domains. Furthermore, there are usually many possible available algorithms for solving these problems, and one typically does not know in advance which of these will be the most effective for solving a particular problem instance. Hyper-heuristics (HHs) are often used as a means to make this choice. In particular, the underlying idea of HHs is to run several algorithms or heuristics and dynamically decide, based on different criteria, which problem or part of the problem should be solved by which algorithm or heuristic. On the other hand, the domain of social choice theory studies how to design collective decision processes by aggregating individual inputs into collective ones. In this paper, we explore the use of social choice theory in creating HHs. By using HHs based on different voting methods, like Borda, Copeland and Kemeny–Young, we show how we can solve both continuous and discrete engineering multi-objective optimization problems and discuss the results obtained by each of these methods. Our obtained results show that our strategy has found solutions that are at least equals to the ones generated by the best algorithm among the studied ones, and sometimes even overcomes these results.</p>", "Keywords": "Hyper-heuristics; Multi-objective evolutionary algorithms; Voting methods; Crashworthiness; Car side impact; Machining; Water resource planning; Multi-objective travel salesperson problem", "DOI": "10.1007/s40313-019-00526-2", "PubYear": 2020, "Volume": "31", "Issue": "1", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratório de Técnicas Inteligentes (LTI), Escola Politécnica (EP), Universidade de São Paulo (USP), São Paulo, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cheriton School of Computer Science, University of Waterloo, Waterloo, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratório de Técnicas Inteligentes (LTI), Escola Politécnica (EP), Universidade de São Paulo (USP), São Paulo, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Laboratório de Técnicas Inteligentes (LTI), Escola Politécnica (EP), Universidade de São Paulo (USP), São Paulo, Brazil"}], "References": []}, {"ArticleId": 78329924, "Title": "Extensional Paramodulation for Higher-Order Logic and Its Effective Implementation Leo-III", "Abstract": "<p>Automation of classical higher-order logic faces various theoretical and practical challenges. On a theoretical level, powerful calculi for effective equality reasoning from first-order theorem proving cannot be lifted to the higher-order domain in a simple manner. Practically, implementations of higher-order reasoning systems have to incorporate procedures that often have high time complexity or are not decidable in general. In my dissertation, both the theoretical and the practical challenges of designing an effective higher-order reasoning system are studied. The resulting system, the automated theorem prover <PERSON><PERSON><PERSON>, is one of the most effective and versatile systems, in terms of supported logical formalisms, to date.</p>", "Keywords": "Higher-order logic; Automated theorem proving; Henkin semantics; Quantified modal logics", "DOI": "10.1007/s13218-019-00628-8", "PubYear": 2020, "Volume": "34", "Issue": "1", "JournalId": 4294, "JournalTitle": "KI - Künstliche Intelligenz", "ISSN": "0933-1875", "EISSN": "1610-1987", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Science, Technology and Communication, University of Luxembourg, Esch-sur-Alzette, Luxembourg"}], "References": []}, {"ArticleId": 78329948, "Title": "Towards defect monitoring for metallic additive manufacturing components using phased array ultrasonic testing", "Abstract": "<p>Additive manufacturing (AM) is a rising technology bringing new opportunities for design and cost of manufacturing, compared to standard processes like casting and machining. Among the AM techniques, direct energy deposition (DED) processes are dedicated to manufacture functional metallic parts. Despite of their promising perspectives, the industrial implementation of DED processes is inhibited by the lack of structural health control. Consequently, non-destructive testing (NDT) techniques can be investigated to inspect DED-manufactured parts, in an online or offline manner. To date, most ultrasonic NDT applications to metallic AM concerned the selective laser melting process; existing studies tackling DED processes mainly compare various ultrasonic techniques and do not propose a comprehensive control method for such processes. Current researches in the GeM laboratory focus on a multi-sensor monitoring method dedicated to DED processes, with a structural health control loop included, in order to track defect formation during manufacturing. In this way, this paper aims to be a proof of concept and proposes a comprehensive control method that opens the way to in situ ultrasonic control for DED. In this paper, a control method using the phased array ultrasonic testing (PAUT) technique is particularly illustrated on wire-arc additive manufacturing (WAAM) components, and its applicability to laser metal deposition (LMD) is also demonstrated. A specific attention is given to the calibration method, towards a quantitative prediction of the size of the detected flaws. PAUT predictions are cross-checked thanks to X-ray radiography, which demonstrates that the PAUT method enables to detect and dimension defects from 0.6 to 1 mm for WAAM aluminum alloy parts. Then, an applicable scenario of inspection of a WAAM industrial and large-scale part is presented. Finally, perspectives for in situ and real-time application of the chosen method are given. This paper shows that real-time monitoring of the WAAM process is possible, as the PAUT method can be integrated in the manufacturing environment, provides relevant in situ data, and runs with computing times compatible with real-time applications. </p>", "Keywords": "Additive manufacturing; Direct energy deposition; WAAM; Process control; Phased array ultrasonic testing", "DOI": "10.1007/s10845-019-01505-9", "PubYear": 2020, "Volume": "31", "Issue": "5", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "UMR CNRS 6183, Centrale Nantes/GeM, Nantes, France;Joint Laboratory of Marine Technology (JLMT) Centrale Nantes – Naval Group, Nantes, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "UMR CNRS 6004, Centrale Nantes/LS2N, Nantes, France;The Phased Array Company (TPAC), Nantes, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The Phased Array Company (TPAC), Nantes, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "UMR CNRS 6183, Centrale Nantes/GeM, Nantes, France;Joint Laboratory of Marine Technology (JLMT) Centrale Nantes – Naval Group, Nantes, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "UMR CNRS 6183, Centrale Nantes/GeM, Nantes, France;Joint Laboratory of Marine Technology (JLMT) Centrale Nantes – Naval Group, Nantes, France"}], "References": []}, {"ArticleId": 78329954, "Title": "Restoring latent factors against negative transfer using partial-adaptation nonnegative matrix factorization", "Abstract": "<p>Collaborative filtering usually suffers from limited performance due to a data sparsity problem. Transfer learning presents an unprecedented opportunity to alleviate this issue through the transfer of useful knowledge from an auxiliary domain to a target domain. However, the situation becomes complicated when the source and target domain share partial knowledge with each other. Transferring the unshared part across domains will cause negative transfer and may degrade the prediction accuracy in the target domain. To address this issue, in this paper, we present a novel model that exploits the latent factors in the target domain against the negative transfer. First, we transfer rating patterns from the source domain to approximate and reconstruct the target rating matrix. Second, to be specific, we propose a partial-adaptation nonnegative matrix factorization method to correct the transfer learning result and restore latent factors in the target. The final experiments completed on real world datasets demonstrate that our proposed approach effectively addresses the negative transfer and significantly outperforms the state-of-art transfer-learning model.</p>", "Keywords": "Transfer learning; Cross-domain recommendation; Negative transfer", "DOI": "10.1007/s42486-019-00018-x", "PubYear": 2020, "Volume": "2", "Issue": "1", "JournalId": 60068, "JournalTitle": "CCF Transactions on Pervasive Computing and Interaction", "ISSN": "2524-521X", "EISSN": "2524-5228", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Grid YINGDA International Holdings CO., LTD, Beijing, China"}], "References": []}, {"ArticleId": 78329990, "Title": "Distributed Multi-robot Circumnavigation with Dynamic Spacing and Time Delay", "Abstract": "<p>Circumnavigation is the process whereby a single agent or multiple agents rotate around a target while preserving a circular formation, which has promising potential in real-world applications such as entrapping a malicious target or escorting an important member. For the multi-robot circumnavigation problem, spacing (i.e., the angle differences) among robots plays an important role in forming a desirable circular formation. The spacing is usually assumed to be a unified constant in most of the studies. However, when robots have different or even time-varying kinematic capabilities, a fixed and equal spacing is probably not effective for accomplishing such task as preventing an enclosed target from fleeing, and thus dynamic spacing is naturally proposed and preferred. The variations of spacing are caused by the “weights” (termed utilities ) of robots. This paper relaxes the condition of piecewise constant utilities and provides the ultimate bound and the input-to-state (ISS) stability conditions for the spacing error and its dynamics respectively. In addition, since time delay is ubiquitous in practical engineering systems while seldom considered in the current studies on circumnavigation, the maximum allowable time delay within which the circumnavigation remains stable is derived using both the frequency domain method and the Lambert-W function. Finally, the theoretical results are validated by a practical simulation system.</p>", "Keywords": "Circumnavigation; Dynamic spacing; Utility; Time-delay", "DOI": "10.1007/s10846-019-01111-0", "PubYear": 2020, "Volume": "99", "Issue": "1", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Robotics Research Center, College of Intelligence Science and Technology, National University of Defense Technology, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Robotics Research Center, College of Intelligence Science and Technology, National University of Defense Technology, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Control Science and Engineering, Harbin Institute of Technology, Harbin, People’s Republic of China"}], "References": []}, {"ArticleId": 78330019, "Title": "Can You Easily Perceive the Local Environment? A User Interface with One Stitched Live Video for Mobile Robotic Telepresence Systems", "Abstract": "Many existing mobile robotic telepresence systems have equipped with two cameras, one is a forward-facing camera for video communication, and the other is a downward-facing camera for robot navigation. However, the two live videos from these two cameras would cause some confusion which makes it difficult for a remote operator to perceive the local environment. In this paper, we propose to use a user interface with one stitched live video instead of two live videos for mobile robotic telepresence systems. We used a video stitching algorithm to stitch the two live videos into one live video through which a remote operator can well perceive the local environment. We conducted a user study to investigate the difference between one stitched live video and two separate live videos in the user interface. The results show that the user interface with one stitched live video improves task efficiency, the number of errors, and remote operators’ feelings of presence, and enables remote operators to concentrate on the work they are doing.", "Keywords": "", "DOI": "10.1080/10447318.2019.1685194", "PubYear": 2020, "Volume": "36", "Issue": "8", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Laboratory of Intelligent Information Technology, School of Computer Science, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Laboratory of Intelligent Information Technology, School of Computer Science, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Laboratory of Intelligent Information Technology, School of Computer Science, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Laboratory of Intelligent Information Technology, School of Computer Science, Beijing Institute of Technology, Beijing, China"}], "References": []}, {"ArticleId": 78330512, "Title": "Reinforcement learning approach for optimal control of multiple electric locomotives in a heavy-haul freight train:A Double-Switch-Q-network architecture", "Abstract": "Electric locomotives provide high tractive power for fast acceleration of heavy-haul freight trains, and significantly reduce the energy consumption with regenerative braking. This paper proposes a reinforcement learning (RL) approach for the optimal control of multiple electric locomotives in a heavy-haul freight train, without using the prior knowledge of train dynamics and the pre-designed velocity profile. The optimization takes the velocity, energy consumption and coupler force as objectives, considering the constraints on locomotive notches and their change rates, speed restrictions, traction and regenerative braking. Besides, since the problem in this paper has continuous state space and large action space, and the adjacent actions’ influences on states share similarities, we propose a Double-Switch Q-network (DSQ-network) architecture to achieve fast approximation of the action-value function, which enhances the parameter sharing of states and actions, and denoises the action-value function. In the numerical experiments, we test DSQ-network in 28 cases using the data of China Railways HXD3B electric locomotive. The results indicate that compared with table-lookup Q-learning, DSQ-network converges much faster and uses less storage space in the optimal control of electric locomotives. Besides, we analyze 1)the influences of ramps and speed restrictions on the optimal policy, and 2)the inter-dependent and inter-conditioned relationships between multiple optimization objectives. Finally, the factors that influence the convergence rate and solution accuracy of DSQ-network are discussed based on the visualization of the high-dimensional value functions.", "Keywords": "Reinforcement learning ; Double-Switch Q-network ; Optimal control ; Electric locomotive ; Heavy-haul freight train", "DOI": "10.1016/j.knosys.2019.105173", "PubYear": 2020, "Volume": "190", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Southwest Jiaotong University, Chengdu, China;Department of Civil and Environmental Engineering, Rutgers, The State University of New Jersey, NJ, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of System Design and Intelligent Manufacturing, Southern University of Science and Technology, Shenzhen, China;Department of Civil and Environmental Engineering, Rutgers, The State University of New Jersey, NJ, USA;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Rutgers, The State University of New Jersey, NJ, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Southwest Jiaotong University, Chengdu, China"}], "References": []}, {"ArticleId": 78330573, "Title": "Do Toxoplasma gondii apicoplast proteins have antigenic potential? An in silico study", "Abstract": "<p>Toxoplasma gondii, one of the extensively studied Apicomplexan parasites, is prevalent worldwide in animals and humans. Apart from its nuclear genome, T. gondii contains an apicoplast genome in 35 kb length which is originated from a secondary endosymbiotic event. In this study, we aimed to investigate the antigenic potential of apicoplast genome encoded proteins (n:28) of T. gondii using in silico analysis. For this purpose, proteins were primarily predicted to reveal antigenic probability and then, several bioinformatics analyses were applied for all predicted antigenic apicoplast proteins to analyze physico-chemical parameters, subcellular localization and transmembrane domain. Also, further prediction analyses including structural, B cell and MHC-I/II epitope sites as well as post-translational modifications were performed for antigenic proteins that have a signal peptide or a high antigenicity value. Of the 28 apicoplast proteins, 19 were predicted as probable antigen. Among antigenic proteins, ribosomal protein S5, L11 and S2 were predicted to have signal peptide whereas ribosomal protein L36 and S17 were predicted to have a significantly high antigenicity value (P &lt; 0.05). In addition, ribosomal protein S5, L11, S2, L36 and S17 were predicted to have a lot of epitopes which have low IC50 and percentile rank value indicating a strong binding among epitopes and MHC-I/II alleles, and post-translational modifications such as N-linked glycosylation, acetylation and phosphorylation. To the best of authors' knowledge this is the first study to show the antigenic potential and other properties of apicoplast-derived proteins of T. gondii.</p><p>Copyright © 2019 Elsevier Ltd. All rights reserved.</p>", "Keywords": "Antigen;Apicoplast;Epitope;Ribosomal protein;T. gondii;Vaccine", "DOI": "10.1016/j.compbiolchem.2019.107158", "PubYear": 2020, "Volume": "84", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ege University Faculty of Science Department of Biology Molecular Biology Section, İzmir, Turkey. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ege University Faculty of Science Department of Biology Molecular Biology Section, İzmir, Turkey."}, {"AuthorId": 3, "Name": "Ahmet Efe Köseoğlu", "Affiliation": "Ege University Faculty of Science Department of Biology Molecular Biology Section, İzmir, Turkey."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Ege University Faculty of Medicine Department of Parasitology, İzmir, Turkey."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Ege University Faculty of Science Department of Biology Molecular Biology Section, İzmir, Turkey."}], "References": []}, {"ArticleId": 78330579, "Title": "From finger friction to brain activation: Tactile perception of the roughness of gratings", "Abstract": "The formation of tactile perception is related to skin receptors and the cerebral cortex. In order to systematically study the tactile perception from finger friction to the brain response, a 32-channel Brain Products system and two tri-axial force sensors were used to obtain electroencephalograph (EEG) and friction signals during fingers exploring grating surfaces. A finite element finger model was established to analyze the stress changes of the skin receptors during tactile perception. Samples with different grating widths and spaces were chosen. The results indicated that different gratings induced different stress concentrations within skin that stimulated Meissner and Merkel receptors. Skin friction was affected by gratings during the tactile perception. It was also found that P300 evoked by gratings was related with the skin deformation, contact area, friction force, and stress around cutaneous mechanoreceptors. The wider grating width generated larger skin deformation, friction force, and stress, which induced stronger tactile stimulation. The smaller grating spacing generated higher vibration frequency, inducing stronger tactile stimulation. The latency of the P300 peak was related to the difference between the textured target stimulus and the smooth non-target stimulus. This study proofed that there was a relationship between the activation in brain regions, surface friction, and contact conditions of skin during the tactile perception. It contributes to understanding the formation process and cognitive mechanism of tactile perception of different surface textures.", "Keywords": "Brain activation;DOF, degree of freedom;EEG, electroencephalograph;ERP, event-related potential;Finite element analysis;Friction;Gratings;P300;SS-EP, steady-state evoked potentials;Tactile perception", "DOI": "10.1016/j.jare.2019.11.001", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechatronic Engineering, China University of Mining and Technology, Xuzhou, Jiangsu 221116, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronic Engineering, China University of Mining and Technology, Xuzhou, Jiangsu 221116, China"}, {"AuthorId": 3, "Name": "Yibing Shi", "Affiliation": "Xuzhou Centre Hospital, Xuzhou, Jiangsu 221116, China;Corresponding author at: Xuzhou Centre Hospital, Xuzhou, Jiangsu 221116, China."}, {"AuthorId": 4, "Name": "Chun<PERSON> Hu", "Affiliation": "Xuzhou Centre Hospital, Xuzhou, Jiangsu 221116, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Xuzhou Centre Hospital, Xuzhou, Jiangsu 221116, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronic Engineering, China University of Mining and Technology, Xuzhou, Jiangsu 221116, China"}], "References": []}, {"ArticleId": 78330621, "Title": "adVAE: A self-adversarial variational autoencoder with Gaussian anomaly prior knowledge for anomaly detection", "Abstract": "Recently, deep generative models have become increasingly popular in unsupervised anomaly detection. However, deep generative models aim at recovering the data distribution rather than detecting anomalies. Moreover, deep generative models have the risk of overfitting training samples, which has disastrous effects on anomaly detection performance. To solve the above two problems, we propose a self-adversarial variational autoencoder (adVAE) with a Gaussian anomaly prior assumption. We assume that both the anomalous and the normal prior distribution are Gaussian and have overlaps in the latent space. Therefore, a Gaussian transformer net T is trained to synthesize anomalous but near-normal latent variables. Keeping the original training objective of a variational autoencoder, a generator G tries to distinguish between the normal latent variables encoded by E and the anomalous latent variables synthesized by T, and the encoder E is trained to discriminate whether the output of G is real. These new objectives we added not only give both G and E the ability to discriminate, but also become an additional regularization mechanism to prevent overfitting. Compared with other competitive methods, the proposed model achieves significant improvements in extensive experiments. The employed datasets and our model are available in a Github repository.", "Keywords": "Anomaly detection ; Outlier detection ; Novelty detection ; Deep generative model ; Variational autoencoder", "DOI": "10.1016/j.knosys.2019.105187", "PubYear": 2020, "Volume": "190", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Jiao Tong University, Shanghai, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Wuhan University, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Davis, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Jiao Tong University, Shanghai, China"}], "References": []}, {"ArticleId": 78330658, "Title": "Measurement of temperature and relative humidity in exhaled breath", "Abstract": "Temperature and relative humidity are two of the most important variables in breath analysis. Several studies have investigated the temperature range of exhaled breath, mostly in the context of respiratory disease diagnostics, but none of them inspected the correlation with clinical parameters, nor the effect of different geographic locations. In this study, we have built a tailor-made device for measuring temperature and relative humidity in exhaled breath. With it, we have carried out 340 measurements (265 in Paris/France and 75 in Haifa/Israel) from 31 participants. The results show that the temperature range of exhaled breath is 31.4-35.4 °C for Haifa’s participants and 31.4-34.8 °C for Parisian participants and the range of exhaled breath relative humidity is 65.0-88.6% and 41.9-91.0% for Haifa and Paris participants, respectively. Clinical and environmental effects were also inspected to give more information on the confounding factors. The results presented in this study contribute to the definition of the ranges of temperature and relative humidity of exhaled breath in individuals, in addition to their correlation with clinical and environmental factors such as gender, BMI and age. These factors must be taken into consideration in order to increase the reproducibility and reliability of a wide variety of measurements in this particular field.", "Keywords": "Breath ; Sensor ; Temperature ; Humidity", "DOI": "10.1016/j.snb.2019.127371", "PubYear": 2020, "Volume": "304", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratory of Nanomaterial-Based Devices, Department of Chemical Engineering, Technion – Israeli Institute of Technology, Haifa, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Nanomaterial-Based Devices, Department of Chemical Engineering, Technion – Israeli Institute of Technology, Haifa, Israel"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Infectious Disease Division, Bio-Rad, Marnes la Coquette, Paris, France"}, {"AuthorId": 4, "Name": "Walaa <PERSON>", "Affiliation": "Laboratory of Nanomaterial-Based Devices, Department of Chemical Engineering, Technion – Israeli Institute of Technology, Haifa, Israel"}, {"AuthorId": 5, "Name": "Falk Fish", "Affiliation": "Laboratory of Nanomaterial-Based Devices, Department of Chemical Engineering, Technion – Israeli Institute of Technology, Haifa, Israel"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Infectious Disease Division, Bio-Rad, Marnes la Coquette, Paris, France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Nanomaterial-Based Devices, Department of Chemical Engineering, Technion – Israeli Institute of Technology, Haifa, Israel;Corresponding author"}], "References": []}, {"ArticleId": 78330659, "Title": "Ni-based tantalate sensing electrode for fast and low detection limit of acetone sensor combining stabilized zirconia", "Abstract": "A stabilized zirconia (YSZ) based acetone sensor based on mixed potential sensing mechanism was fabricated using NiTa<sub>2</sub>O<sub>6</sub> sensing electrode (SE) calcinated at 1000 °C. The NiTa<sub>2</sub>O<sub>6</sub> sensing material was characterized by TG/DSC, XRD, SEM, Raman and XPS measurement. The gas sensing performance of developed sensing device demonstrated that the sensor based on NiTa<sub>2</sub>O<sub>6</sub>-SE displayed low detection limit of 200 ppb acetone and rapid response and recovery times of 9 s and 18 s to 2 ppm acetone at 600 °C. Between the response value to wide concentration range of 0.2−200 ppm acetone and the acetone concentration logarithm for the developed sensor presented piecewise linear function. The fabricated acetone sensor also possessed good wet fastness, reproducibility and stability of 20 days, portending great potential in aspect of low and high acetone concentration detection. More importantly, the solid electrolyte type acetone sensor utilizing NiTa<sub>2</sub>O<sub>6</sub>-SE open up a new possibility for noninvasive diagnosis of diabetes through detecting exhaled breath of healthy people and diabetes patients.", "Keywords": "Acetone sensor ; NiTa<sub>2</sub>O<sub>6</sub> ; Stabilized zirconia ; Mixed potential", "DOI": "10.1016/j.snb.2019.127375", "PubYear": 2020, "Volume": "304", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Colorectal and Anal Surgery, The First Hospital of Jilin University, 71 Xinmin Street, Changchun, 130000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Peking University, Beijing, 100871, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 6, "Name": "Li <PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China;Corresponding authors"}], "References": []}, {"ArticleId": 78330683, "Title": "Nonintrusive proper generalised decomposition for parametrised incompressible flow problems in OpenFOAM", "Abstract": "The computational cost of parametric studies currently represents the major limitation to the application of simulation-based engineering techniques in a daily industrial environment. This work presents the first nonintrusive implementation of the proper generalised decomposition (PGD) in OpenFOAM, for the approximation of parametrised laminar incompressible Navier–Stokes equations. The key feature of this approach is the seamless integration of a reduced order model (ROM) in the framework of an industrially validated computational fluid dynamics software. This is of special importance in an industrial environment because in the online phase of the PGD ROM the description of the flow for a specific set of parameters is obtained simply via interpolation of the generalised solution, without the need of any extra solution step. On the one hand, the spatial problems arising from the PGD separation of the unknowns are treated using the classical solution strategies of OpenFOAM, namely the semi-implicit method for pressure linked equations (SIMPLE) algorithm. On the other hand, the parametric iteration is solved via a collocation approach. The resulting ROM is applied to several benchmark tests of laminar incompressible Navier–Stokes flows, in two and three dimensions, with different parameters affecting the flow features. Eventually, the capability of the proposed strategy to treat industrial problems is verified by applying the methodology to a parametrised flow control in a realistic geometry of interest for the automotive industry.", "Keywords": "Reduced order models ; Proper generalised decomposition ; Finite volume ; Incompressible laminar <PERSON><PERSON><PERSON> ; Pressure Poisson equation ; Parametrised flows ; OpenFOAM ; Nonintrusiveness", "DOI": "10.1016/j.cpc.2019.107013", "PubYear": 2020, "Volume": "249", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Volkswagen AG, Brieffach 011/1777, D-38436, Wolfsburg, Germany;Laboratori de Càlcul Numèric (LaCàN), ETS de Ingenieros de Caminos, Canales y Puertos, Universitat Politècnica de Catalunya, Barcelona, Spain;Zienkiewicz Centre for Computational Engineering, College of Engineering, Swansea University, Swansea, SA1 8EN, Wales, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratori de Càlcul <PERSON>umèric (LaCàN), ETS de Ingenieros de Caminos, Canales y Puertos, Universitat Politècnica de Catalunya, Barcelona, Spain;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Centre for Computational Engineering, College of Engineering, Swansea University, Swansea, SA1 8EN, Wales, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Volkswagen AG, Brieffach 011/1777, D-38436, Wolfsburg, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Laboratori de Càlcul Numèric (LaCàN), ETS de Ingenieros de Caminos, Canales y Puertos, Universitat Politècnica de Catalunya, Barcelona, Spain"}], "References": []}, {"ArticleId": 78330852, "Title": "Weak-restriction bi-objective optimization algorithm for scheduling with rejection on non-identical batch processing machines", "Abstract": "We investigate the problem of scheduling a set of jobs with arbitrary sizes and unequal weights on a set of parallel batch machines with non-identical capacities. The objective is to minimize the makespan of the accepted jobs and the total rejection penalty of the rejected jobs, simultaneously. To address the studied problem, a Pareto-based ant colony optimization algorithm with the first job selection probability (FPACO) is proposed. A weak-restriction selection strategy is proposed to obtain the desirability of candidate jobs. Two objective-oriented heuristic information and pheromone matrices are designed, respectively, to record the experience in different search dimensions. Moreover, a local optimization algorithm is incorporated to improve the solution quality. Finally, the proposed algorithm is compared with four existing algorithms through extensive simulation experiments. The experimental results indicate that the proposed algorithm outperforms all of the compared algorithms within a reasonable time.", "Keywords": "Parallel batch machines ; Non-identical capacities ; Selection restriction ; Rejection cost ; Ant colony optimization", "DOI": "10.1016/j.asoc.2019.105914", "PubYear": 2020, "Volume": "86", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "Zhao<PERSON><PERSON><PERSON>a", "Affiliation": "Key Lab of Intelligent Computing and Signal Processing of Ministry of Education, PR China;School of Computer Science and Technology, Anhui University, Hefei, Anhui, 230039, PR China;Corresponding author at: School of Computer Science and Technology, Anhui University, Hefei, Anhui, 230039, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei, Anhui, 230039, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Anhui, 230009, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Management, University of Science and Technology of China, Hefei, Anhui, 230026, PR China"}], "References": []}, {"ArticleId": 78330964, "Title": "Personal information and public health: Design tensions in sharing and monitoring wellbeing in pregnancy", "Abstract": "Mobile technologies are valuable tools for the self-report of mental health and wellbeing. These systems pose many unique design challenges which have received considerable attention within HCI, including the engagement of users. However, less attention has been paid to the use of personal devices in public health. Integrating self-reported data within the context of clinical care suggests the need to design interfaces to support data management, sense-making, risk-assessment, feedback and patient-provider relationships. This paper reports on a qualitative design study for the clinical interface of a mobile application for the self-report of psychological wellbeing and depression during pregnancy. We examine the design tensions which arise in managing the expectations and informational needs of pregnant women, midwives, clinical psychologists, GPs and other health professionals with respect to a broad spectrum of wellbeing. We discuss strategies for managing these tensions in the design of technologies required to balance personal information with public health.", "Keywords": "Data sharing;Disclosure;Engagement;Mental health;Midwifery;Perinatal depression;Pregnancy;Self report;Wellbeing", "DOI": "10.1016/j.ijhcs.2019.102373", "PubYear": 2020, "Volume": "135", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Copenhagen Center for Health Technology, Technical University of Denmark, Denmark;School of Computer Science and Statistics, Trinity College Dublin, Ireland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information and Communication Studies, University College Dublin, Ireland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Health Sciences, University of Surrey, England, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Research, Cambridge, England, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Primary Care and Public Health, School of Public Health, Imperial College London, United Kingdom;Centre for Population Health Sciences, Lee Kong Chian School of Medicine, Nanyang Technological University, Singapore"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Statistics, Trinity College Dublin, Ireland"}], "References": []}, {"ArticleId": 78331997, "Title": "A bottom-up packing approach for modeling the constrained two-dimensional guillotine placement problem", "Abstract": "In this paper, we address the Constrained Two-dimensional Guillotine Placement Problem (C2GPP). This problem considers a rectangular large object and a set of rectangular small item types of given sizes and values. The objective is to select and cut the most valuable subset of small items from the large object using orthogonal guillotine cuts and constrained patterns. To completely model the problem, we present pseudo-polynomial and compact integer non-linear formulations. Then, we obtain an equivalent Mixed Integer Linear Programming (MILP) formulation from each non-linear model. These novel formulations are related to a bottom-up packing approach of successive horizontal and vertical builds of the small items. Additionally, we develop a set of constraints for each model which allows us to strictly consider d -staged guillotine cutting patterns, for a given positive integer d . To evaluate the MILP models and compare their performance to the state-of-the-art formulation of the C2GPP, we run computational experiments using three sets of benchmark instances, two of them from the literature. The results show that the proposed models, based on a bottom-up packing approach, lead to optimal or near-optimal solutions in reasonable processing times, even for scenarios that are intractable for the benchmark model.", "Keywords": "Cutting &amp; packing problems ; Constrained two-dimensional cutting ; Non-staged and staged guillotine cuts ; Mixed integer linear programming ; Bottom-up packing", "DOI": "10.1016/j.cor.2019.104851", "PubYear": 2020, "Volume": "115", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Production Engineering, Federal University of São Carlos, Via Washington Luiz km. 235, São Carlos, 13565-905, SP, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Production Engineering, Federal University of São Carlos, Via Washington Luiz km. 235, São Carlos, 13565-905, SP, Brazil;Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Production Engineering, Federal University of São Carlos, Via Washington Luiz km. 235, São Carlos, 13565-905, SP, Brazil"}], "References": []}, {"ArticleId": 78332026, "Title": "A comparative evaluation of novelty detection algorithms for discrete sequences", "Abstract": "<p>The identification of anomalies in temporal data is a core component of numerous research areas such as intrusion detection, fault prevention, genomics and fraud detection. This article provides an experimental comparison of candidate methods for the novelty detection problem applied to discrete sequences. The objective of this study is to identify which state-of-the-art methods are efficient and appropriate candidates for a given use case. These recommendations rely on extensive novelty detection experiments based on a variety of public datasets in addition to novel industrial datasets. We also perform thorough scalability and memory usage tests resulting in new supplementary insights of the methods’ performance, key selection criteria to solve problems relying on large volumes of data and to meet the expectations of applications subject to strict response time constraints.</p>", "Keywords": "Novelty detection; Discrete sequences; Temporal data; Fraud detection; Outlier detection; Anomaly detection", "DOI": "10.1007/s10462-019-09779-4", "PubYear": 2020, "Volume": "53", "Issue": "5", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Data Science, EURECOM, Sophia Antipolis, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Data Science, EURECOM, Sophia Antipolis, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Amadeus, Sophia Antipolis, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Data Science, EURECOM, Sophia Antipolis, France"}], "References": []}, {"ArticleId": 78332207, "Title": "Epidermal antenna in palmar arch region for anaemia detection to avoid peripheral perfusion artifact in optical sensor during hemoglobin measurement", "Abstract": "<p>The anaemia disease diagnosed with various test such as complete blood count (CBC), ferritin measure, reticulocyte count, peripheral smear and blood gas analyser. The ferritin measure the storage and usage of iron in human body, reticulocyte count assess young red blood cell, and Peripheral smear analyse the cell colour, size, and shape of red blood cell through microscopic image. The blood gas analyser measures methemoglobin (MetHb) and carboxyhaemoglobin. Different test measure haemoglobin, since iron level show normal in blood for the patient with low total body iron. The iron deficiency never show symptom or sign in the human body. In this paper, we propose a new non-invasive method for anaemia measure through epidermal antenna over ulnar region of palm and acquire radiating signal from Haemoglobin protein. The acquired signal from epidermal antenna analysed with Transverse Dyadic Wavelet Transform for haemoglobin protein level in red blood cell. The epidermal antenna signal from ulnar region haemoglobin measurement avoids the peripheral perfusion artifact of optical or image sensor which arise during measurement. The experimental analysis show signal of epidermal antenna measure the haemoglobin more accurately, through regression modelling than existing optical and image based point of care device. Experimental result of haemoglobin measurement from ulnar region has validated with blood gas analyser.</p>", "Keywords": "", "DOI": "10.1007/s00542-019-04675-x", "PubYear": 2020, "Volume": "26", "Issue": "5", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, R.M.K. College of Engineering and Technology, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, SSN College of Engineering, Kalavakkam, India"}], "References": []}, {"ArticleId": 78332520, "Title": "Learning the Principles of Art History with convolutional neural networks", "Abstract": "Understanding the historical transformation of artistic styles implies the recognition of different stylistic properties. From a computer vision perspective, stylistic properties represent complex image features. In our work we explore the use of convolutional neural networks for learning features that are relevant for understanding properties of artistic styles. We focus on stylistic properties described by <PERSON> in his book Principles of Art History (1915). <PERSON><PERSON><PERSON><PERSON><PERSON> identified five key visual principles, each defined by two contrasting concepts. We refer to each principle as one high-level image feature that measures how much each of the contrasting concepts is present in an image. We introduce convolutional neural network regression models trained to predict values of the five <PERSON><PERSON><PERSON><PERSON><PERSON>’s features. We provide quantitative and qualitative evaluations of those predictions, as well as analyze how the predicted values relate to different styles and artists. The outcome of our analysis suggests that the models learn to discriminate meaningful features that correspond to the visual characteristics of concepts described by <PERSON><PERSON><PERSON><PERSON><PERSON>. This indicates that the presented approach can be used to enable new ways of exploring fine art collections based on image features relevant and well-known within art history.", "Keywords": "Convolutional neural networks ; Fine art ; High-level image features ; Wölfflin", "DOI": "10.1016/j.patrec.2019.11.008", "PubYear": 2020, "Volume": "129", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, Bijenicka cesta 54, Zagreb 10000, Croatia;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, Bijenicka cesta 54, Zagreb 10000, Croatia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Zagreb, Faculty of Electrical Engineering and Computing, Unska 3, Zagreb 10000, Croatia"}], "References": []}, {"ArticleId": 78332573, "Title": "Comparative Study of Fuzzy PID and PID Controller Optimized with Spider Monkey Optimization for a Robotic Manipulator System", "Abstract": "Background: <p>Robotic manipulator system has been useful in many areas like chemical industries, automobile, medical fields etc. Therefore, it is essential to implement a controller for controlling the end position of a robotic armeffectively. However, with the increasing non-linearity and the complexities of a robotic manipulator system, a conventional Proportional-Integral-Derivative controller has become ineffective. Nowadays, intelligent techniques like fuzzy logic, neural network and optimization algorithms has emerged as an efficient tool for controlling the highly complex non-linear functions with uncertain dynamics.</p> Objective: <p>To implement an efficient and robustcontroller using Fuzzy Logic to effectively control the end position of Single link Robotic Manipulator to follow the desired trajectory.</p> Methods: <p>In this paper, a Fuzzy Proportional-Integral-Derivativecontroller is implemented whose parameters are obtainedwith the Spider Monkey Optimization technique taking Integral of Absolute Error as an objective function.</p> Results: <p>Simulated results ofoutput of the plants controlled byFuzzy Proportional-Integral-Derivative controller have been shown in this paper and the superiority of the implemented controller has also been described by comparing itwith the conventional Proportional-Integral-Derivative controller and Genetic Algorithm optimization technique.</p> Conclusion: <p>From results, it is clear that the FuzzyProportional-Integral-Derivativeoptimized with the Spider monkey optimization technique is more accurate, fast and robust as compared to the Proportional-Integral-Derivativecontroller as well as the controllers optimized with the Genetic algorithm techniques.Also, by comparing the integral absolute error values of all the controllers, it has been found that the controller optimized with the Spider Monkey Optimization technique shows 99% better efficacy than the genetic algorithm technique.</p>", "Keywords": "", "DOI": "10.2174/2213275912666191107104635", "PubYear": 2021, "Volume": "14", "Issue": "4", "JournalId": 31517, "JournalTitle": "Recent Patents on Computer Sciencee", "ISSN": "2213-2759", "EISSN": "1874-4796", "Authors": [{"AuthorId": 1, "Name": "Alka Agrawal", "Affiliation": "Department of Electronics and Communication, GLA University, Mathura, Uttar Pradesh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, GLA University, Mathura, Uttar Pradesh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Birla Institute of Technology and Science, Pilani, Rajasthan, India"}], "References": []}, {"ArticleId": 78332586, "Title": "Application of gene expression programming and sensitivity analyses in analyzing effective parameters in gastric cancer tumor size and location", "Abstract": "<p>Gastric cancer (GC) is the third reason for cancer-related deaths in the world. The late referral of patients to medical centers in an advanced stage can make the treatment procedure more difficult. Accurate diagnosis of risk factors in GC tumor size and tumor location can lead to taking preventive measures or determining a suitable treatment strategy. This study aims to present a general model to identify the correlation of different parameters in a GC tumor place and tumor size. The medical documents of GC patients consist of the dataset of this study. The effect of seven main parameters, namely age, smoking, Helicobacter pylori ( <PERSON><PERSON>yl<PERSON> ) infection, job, surgical background, sex, and nodal stage is investigated in GC tumor location and tumor size. By considering all the medical documents, data modeling is conducted using gene expression programming because of the high precision of model output. In the following, three different sensitivity analysis methods (Morris, Distributed Evaluation of Local Sensitivity Analysis (DELSA), and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) are applied to determine the influential factors in the tumor size and location. Results show that in sequence, sex, age, and H. pylori records mostly affect tumor location; the nodal stage, smoking, and surgery record mostly affect tumor size. This method can help in identifying effective parameters and prevention of patients’ death in all types of diseases, even for terminal illnesses. </p>", "Keywords": "Gastric cancer; Sensitivity analysis; Gene expression programming; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; Distributed Evaluation of Local Sensitivity Analysis", "DOI": "10.1007/s00500-019-04507-0", "PubYear": 2020, "Volume": "24", "Issue": "13", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Urmia University of Technology (UUT), Urmia, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Urmia University of Technology (UUT), Urmia, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Internal Medicine, Imam <PERSON>, Urmia University of Medical Science, Urmia, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Urmia University of Technology (UUT), Urmia, Iran"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Urmia University of Technology (UUT), Urmia, Iran"}], "References": []}, {"ArticleId": 78332665, "Title": "Pulmonary nodule image super-resolution using multi-scale deep residual channel attention network with joint optimization", "Abstract": "<p>High-resolution medical images can help doctors to find early lesions and provide assistance and support for the diagnosis and treatment of diseases. Super-resolution can obtain a single high-resolution image from a given low-resolution image. It can be divided into three major means roughly: the interpolation-based methods, the reconstruction-based methods, and the learning-based methods. The interpolation-based methods rely on the smoothness assumptions and cannot restore fine textures. The reconstruction-based methods need to find the image degradation model and the optimal blur kernels. In fact, blur kernels are complicated and unknown. Kernel mismatch will fail to produce good results (e.g., over-sharpening or over-smoothing). The learning-based methods pay more attention to the understanding of the image content and structure, and they can establish a mapping function between the high-resolution images and the low-resolution images, which attract the attention of researchers. Deep learning has the strong ability of nonlinear mapping. Therefore, it has been widely used in super-resolution in recent years. In the paper, we propose a multi-scale deep residual channel attention network which consists of six components: joint input of low-resolution image and edge, shallow feature extraction, deep feature extraction, channel attention, high-resolution image reconstruction, and total loss. Edges are the first-order high-frequency details which are very important to super-resolution. The joint input of low-resolution images and edges enhances useful information. The multi-scale deep residual channel attention module can not only acquire structural features but also capture features of different scales and hierarchies. It can also obtain relationships among channel features. In addition, the joint guidance of perceptual loss, content loss, and edge loss is used to improve the visual quality and preserve the spatial structure and high-frequency details of low-resolution images. Experiments have been conducted on the pulmonary nodule image dataset, and the results demonstrate that the proposed method can yield better performance by comparing with the state-of-the-art methods.</p>", "Keywords": "Pulmonary nodule; Super-resolution; Multi-scale deep residual; Attention", "DOI": "10.1007/s11227-019-03066-3", "PubYear": 2020, "Volume": "76", "Issue": "2", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Reliability and Intelligence of Electrical Equipment, Hebei University of Technology, Tianjin, China;Laboratory of Electromagnetic Field and Electrical Apparatus Reliability, Hebei University of Technology, Tianjin, China;Information Technology Center, North China Institute of Aerospace Engineering, Langfang, China"}, {"AuthorId": 2, "Name": "Junhua Gu", "Affiliation": "State Key Laboratory of Reliability and Intelligence of Electrical Equipment, Hebei University of Technology, Tianjin, China;Laboratory of Electromagnetic Field and Electrical Apparatus Reliability, Hebei University of Technology, Tianjin, China;School of Artificial Intelligence, Hebei University of Technology, Tianjin, China;Hebei Province Key Laboratory of Big Data Calculation, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Hebei University of Technology, Tianjin, China;Hebei Province Key Laboratory of Big Data Calculation, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Information Technology Center, North China Institute of Aerospace Engineering, Langfang, China"}], "References": []}, {"ArticleId": ********, "Title": "Software fault localization using BP neural network based on function and branch coverage", "Abstract": "<p>Software failure is inevitable with the increase in scale and complexity of the software. Existing fault localization techniques based on neural networks take statement coverage information and test case execution results into account to train the network. In this paper, we propose an effective approach for fault localization based on back-propagation neural network which utilizes branch and function coverage information along with test case execution results to train the network. We investigated our approach using Siemens suite. Our experimental result shows that our proposed approach performs on average 23.50–44.27% better than existing fault localization techniques.</p>", "Keywords": "Back-propagation neural network (BPNN); Debugging; Fault localization; Successful test case; Failed test case; Predicate; Clause", "DOI": "10.1007/s12065-019-00318-2", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 5202, "JournalTitle": "Evolutionary Intelligence", "ISSN": "1864-5909", "EISSN": "1864-5917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "DOS Lab, Department of Computer Science and Engineering, National Institute of Technology Rourkela, Rourkela, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "GRL, Department of Computer Science and Engineering, Indian Institute of Technology Kharagpur, Kharagpur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "RS Lab, Department of Computer Science and Engineering, National Institute of Technology Rourkela, Rourkela, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "DOS Lab, Department of Computer Science and Engineering, National Institute of Technology Rourkela, Rourkela, India"}], "References": []}, {"ArticleId": 78332712, "Title": "An evaluation of ear biometric system based on enhanced Jaya algorithm and SURF descriptors", "Abstract": "<p>Recently, the ear biometric has received much attention for human recognition due to its unique shape and rich local features. However, extracting discriminative features from ear images is a crucial task in presence of illumination changes, low contrast, noise, and pose variations. With the aim of neutralizing the effect of these factors, this paper proposes an automatic enhancement technique using meta-heuristic optimization to enhance the ear images. Here, we modified a recent and simple yet meta-heuristic optimization technique known as Jaya algorithm by introducing a mutation operator to enhance the ear images in few iterations and the proposed approach is named as enhanced Jaya algorithm. Then, we employed a pose-invariant local feature extractor, SURF to extract local features. Finally, the k - NN classifier has used to evaluate the rate of correct identification. Extensive experiments are conducted on four standard datasets and the performance evaluation is carried out by qualitative and quantitative measures. Experimental results clearly indicate the proposed enhancement approach is competitive as compared to two classical methods HE, CLAHE, and two meta-heuristic algorithms PSO and DE-based image enhancement techniques.</p>", "Keywords": "Image enhancement; Ear biometrics; Ear identification; Jaya algorithm; Speeded-up robust features", "DOI": "10.1007/s12065-019-00311-9", "PubYear": 2020, "Volume": "13", "Issue": "3", "JournalId": 5202, "JournalTitle": "Evolutionary Intelligence", "ISSN": "1864-5909", "EISSN": "1864-5917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Seemanta Engineering College, Jharpokharia, Mayurbhanj, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering, KIIT University, Bhubaneswar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Technology, F<PERSON>r Mohan University, Balasore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Soft Computing Laboratory, Department of Computer Science, Yonsei University, Seoul, Korea"}], "References": [{"Title": "An optimal feature selection method for histopathology tissue image classification using adaptive jaya algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "3", "Page": "1279", "JournalTitle": "Evolutionary Intelligence"}]}, {"ArticleId": 78332714, "Title": "A Semi-fragile Video Watermarking Algorithm Based On Chromatic Residual DCT", "Abstract": "<p>In this paper, a semi fragile video watermarking scheme based on chromatic DCT is proposed, which can protect copyright and tamper detection simultaneously. Firstly, through the experiment, it is found that the chroma block has a more stable prediction mode relative to the luminance block, which can effectively reduce the asynchronous probability brought by the change of the prediction mode. At the same time, blocks contains more the number of nonzero (NNZ) coefficients, the less the prediction mode changes. Therefore, for each macroblock, the algorithm sort its 4 × 4 sub macroblock according to the NNZ of chroma DCT coefficients, then select the sub block which has most NNZ residual coefficients. According to the secret key K and medium frequency stability of Intra 4 × 4 coefficients, modulate the relationship of three DCT coefficients near medium frequency. Secondly, the sensitivity of the prediction mode to malicious attacks and recompression operations is different. Therefore, the algorithm classifies the prediction mode of the macroblock and generates the authentication code using the prediction mode. The experimental results show that the video quality is negligible affected after watermark embedding, and the change of video bit rate is basically constant. At the same time, the chromatic DCT algorithm can also use the authentication code of the prediction mode to detect and locate the tampering at the 4 × 4 sub block level with good robustness.</p>", "Keywords": "H.264; Chromatic DCT; Video watermark; Prediction mode; Tamper detection", "DOI": "10.1007/s11042-019-08256-y", "PubYear": 2020, "Volume": "79", "Issue": "3-4", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Lihua Tian", "Affiliation": "School of Software Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 2, "Name": "Hangtao Dai", "Affiliation": "School of Software Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Software Engineering, Xi’an Jiaotong University, Xi’an, China"}], "References": []}, {"ArticleId": ********, "Title": "Two-level distributed clustering routing algorithm based on unequal clusters for large-scale Internet of Things networks", "Abstract": "<p>According to the recent advancements in communication technologies and the widespread use of smart devices, our environment can be transforming into the Internet of Things (IoT) because it can connect the physical, cyber, and biological world via smart sensors for different purposes. Wireless sensor networks are considered as one of the main infrastructures in the IoT systems. Therefore, decreasing the total energy consumption of sensor nodes and prolonging the network longevity are two important challenges that should be considered. To increase energy efficiency and to improve the network longevity, a two-level distributed clustering routing algorithm based on unequal clusters has been proposed for large-scale IoT systems. The main idea is to decrease the data transmission distances between member nodes and cluster heads to mitigate the hot spot problem by distributing two cluster heads in each cluster, which in turn leads to energy conservation and load balancing. The clustering method is two level due to the benefits it offers for the sensor nodes. First, each node can transfer its data to the nearest cluster head because a primary cluster head and a secondary cluster head have been considered for each cluster. Therefore, the nodes far from the primary cluster head can be organized based on their distances to the closest cluster head to reduce their data transmission distances to the cluster heads. Second, two cluster heads can be replaced with each other in different circumstances. This reduces the overhead of the cluster head selection algorithm in the proposed scheme. Third, the sensor nodes can benefit from the primary and secondary cluster heads to transfer the data to the sink through different paths with the minimum energy consumption. Simulation results indicate that the proposed algorithm has better performance in terms of total energy consumption, total network energy, and network longevity compared to previous similar schemes.</p>", "Keywords": "Wireless sensor network; Distributed routing algorithm; Two-level clustering; Unequal clusters; Internet of Things", "DOI": "10.1007/s11227-019-03067-2", "PubYear": 2020, "Volume": "76", "Issue": "3", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>. <PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Waterloo, Waterloo, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer and Information Technology Engineering, Qazvin Branch, Islamic Azad University, Qazvin, Iran"}], "References": [{"Title": "Energy-efficient data dissemination algorithm based on virtual hexagonal cell-based infrastructure and multi-mobile sink for wireless sensor networks", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "1", "Page": "150", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 78333141, "Title": "Fault prognostics by an ensemble of Echo State Networks in presence of event based measurements", "Abstract": "Fault prognostics aims at predicting the degradation of equipment for estimating the Remaining Useful Life ( RUL ). Traditional data-driven fault prognostic approaches face the challenge of dealing with incomplete and noisy data collected at irregular time steps, e.g. in correspondence of the occurrence of triggering events in the system. Since the values of all the signals are missing at the same time and the number of missing data largely exceeds the number of triggering events, missing data reconstruction approaches are difficult to apply. In this context, the objective of the present work is to develop a one-step method, which directly receives in input the event-based measurement and produces in output the system RUL with the associated uncertainty. Two strategies based on the use of ensembles of Echo State Networks ( ESNs ), properly adapted to deal with data collected at irregular time steps, have been proposed to this aim. A synthetic and a real-world case study are used to show their effectiveness and their superior performance with respect to state-of-the-art prognostic methods.", "Keywords": "Prognostics ; Missing data ; Sliding bearing ; Echo State Network ; Ensemble ; Differential evolution optimization", "DOI": "10.1016/j.engappai.2019.103346", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Xu", "Affiliation": "Energy Department, Politecnico di Milano, Via La Masa 34, 20156 Milan, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Energy Department, Politecnico di Milano, Via La Masa 34, 20156 Milan, Italy;Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical and Maintenance Engineering Department, School of Applied Technical Sciences, German Jordanian University, Amman 11180, Jordan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Energy Department, Politecnico di Milano, Via La Masa 34, 20156 Milan, Italy;MINES ParisTech, PSL Research University, CRC, Sophia Antipolis, France;Eminant Scholar, Department of Nuclear Engineering, College of Engineering, Kyung Hee University, Republic of Korea;<PERSON><PERSON>, Via pergolesi 5, Milano, Italy"}], "References": []}]