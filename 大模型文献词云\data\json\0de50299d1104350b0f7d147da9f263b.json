[{"ArticleId": 87236311, "Title": "APPLICATION OF MULTI FACTORED BIOMETRIC MEASURE FOR\nDATA SECURITY IN ATM", "Abstract": "", "Keywords": "", "DOI": "10.26634/jip.7.4.17819", "PubYear": 2020, "Volume": "7", "Issue": "4", "JournalId": 40088, "JournalTitle": "i-manager’s Journal on Image Processing", "ISSN": "2349-4530", "EISSN": "2349-6827", "Authors": [{"AuthorId": 1, "Name": "YERRAMSETTI MEGHANA", "Affiliation": "Department of Artificial Intelligence, Vidya Jyothi Institute of Technology, Aziz Nagar, Hyderabad, Telangana, India."}, {"AuthorId": 2, "Name": "GHOSH SIDDHARTHA", "Affiliation": "Department of Artificial Intelligence, Vidya Jyothi Institute of Technology, Aziz Nagar, Hyderabad, Telangana, India."}, {"AuthorId": 3, "Name": "YADAV ARPIT", "Affiliation": "<PERSON><PERSON> Brew, Hyderabad, Telangana, India."}], "References": []}, {"ArticleId": 87236312, "Title": "FUZZY LOGIC IN TERMS OF BIOMETRICS", "Abstract": "", "Keywords": "", "DOI": "10.26634/jip.7.4.17775", "PubYear": 2020, "Volume": "7", "Issue": "4", "JournalId": 40088, "JournalTitle": "i-manager’s Journal on Image Processing", "ISSN": "2349-4530", "EISSN": "2349-6827", "Authors": [{"AuthorId": 1, "Name": "KUMAR SINGH KISHOR", "Affiliation": "MATS University, Raipur, Chhattisgarh, India."}, {"AuthorId": 2, "Name": "BARDE SNEHLATA", "Affiliation": "MATS University, Raipur, Chhattisgarh, India."}], "References": []}, {"ArticleId": 87236313, "Title": "R<PERSON><PERSON><PERSON><PERSON> ON SOURCE CAMERA IDENTIFICATION: DIGITAL IMAGE\nFORENSICS", "Abstract": "", "Keywords": "", "DOI": "10.26634/jip.7.4.17832", "PubYear": 2020, "Volume": "7", "Issue": "4", "JournalId": 40088, "JournalTitle": "i-manager’s Journal on Image Processing", "ISSN": "2349-4530", "EISSN": "2349-6827", "Authors": [{"AuthorId": 1, "Name": "JAIN PRAVEE", "Affiliation": ""}, {"AuthorId": 2, "Name": "AWASTHI MAYANK", "Affiliation": ""}, {"AuthorId": 3, "Name": "SHANDILYA MADHU", "Affiliation": ""}], "References": []}, {"ArticleId": 87236314, "Title": "A DISCUSSION ON IMAGE B<PERSON><PERSON><PERSON>ZATION METHODS", "Abstract": "", "Keywords": "", "DOI": "10.26634/jip.7.4.17331", "PubYear": 2020, "Volume": "7", "Issue": "4", "JournalId": 40088, "JournalTitle": "i-manager’s Journal on Image Processing", "ISSN": "2349-4530", "EISSN": "2349-6827", "Authors": [{"AuthorId": 1, "Name": "PRAKASH SAXENA LALIT", "Affiliation": ""}], "References": []}, {"ArticleId": 87236429, "Title": "Memory Utilization and Machine Learning Techniques for Compiler Optimization", "Abstract": "Compiler optimization techniques allow developers to achieve peak performance with low-cost hardware and are of prime importance in the field of efficient computing strategies. The realm of compiler suites that possess and apply efficient optimization methods provide a wide array of beneficial attributes that help programs execute efficiently with low execution time and minimal memory utilization. Different compilers provide a certain degree of optimization possibilities and applying the appropriate optimization strategies to complex programs can have a significant impact on the overall performance of the system. This paper discusses methods of compiler optimization and covers significant advances in compiler optimization techniques that have been established over the years. This article aims to provide an overall survey of the cache optimization methods, multi memory allocation features and explore the scope of machine learning in compiler optimization to attain a sustainable computing experience for the developer and user.", "Keywords": "", "DOI": "10.1051/itmconf/20213701021", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, 600127, Tamilnadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, 600127, Tamilnadu, India"}, {"AuthorId": 3, "Name": "<PERSON> Karmel", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, 600127, Tamilnadu, India"}], "References": []}, {"ArticleId": 87236436, "Title": "Utilizing the simple graph convolutional neural network as a model for simulating influence spread in networks", "Abstract": "Abstract The ability for people and organizations to connect in the digital age has allowed the growth of networks that cover an increasing proportion of human interactions. The research community investigating networks asks a range of questions such as which participants are most central, and which community label to apply to each member. This paper deals with the question on how to label nodes based on the features (attributes) they contain, and then how to model the changes in the label assignments based on the influence they produce and receive in their networked neighborhood. The methodological approach applies the simple graph convolutional neural network in a novel setting. Primarily that it can be used not only for label classification, but also for modeling the spread of the influence of nodes in the neighborhoods based on the length of the walks considered. This is done by noticing a common feature in the formulations in methods that describe information diffusion which rely upon adjacency matrix powers and that of graph neural networks. Examples are provided to demonstrate the ability for this model to aggregate feature information from nodes based on a parameter regulating the range of node influence which can simulate a process of exchanges in a manner which bypasses computationally intensive stochastic simulations.", "Keywords": "Graph convolutional neural networks;Social influence;Networks;Machine learning;Social networks;Information diffusion", "DOI": "10.1186/s40649-021-00095-y", "PubYear": 2021, "Volume": "8", "Issue": "1", "JournalId": 21567, "JournalTitle": "Computational Social Networks", "ISSN": "", "EISSN": "2197-4314", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Statistics and Data Science, University of Central Florida (UCF), Orlando, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Statistics and Data Science, University of Central Florida (UCF), Orlando, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Central Florida (UCF), Orlando, USA"}], "References": []}, {"ArticleId": 87236998, "Title": "E2LG: a multiscale ensemble of LSTM/GAN deep learning architecture for multistep-ahead cloud workload prediction", "Abstract": "<p>Efficient resource demand prediction and management are two main challenges for cloud service providers in order to control dynamic autoscaling and power consumption in recent years. The behavior of cloud workload time-series at subminute scale is highly chaotic and volatile; therefore, traditional machine learning-based time-series analysis approaches fail to obtain accurate predictions. In recent years, deep learning-based schemes are suggested to predict highly nonlinear cloud workloads, but sometimes they fail to obtain excellent prediction results. Hence, demands for more accurate prediction algorithm exist. In this paper, we address this issue by proposing a hybrid E2LG algorithm, which decomposes the cloud workload time-series into its constituent components in different frequency bands using empirical mode decomposition method which reduces the complexity and nonlinearity of prediction model in each frequency band. Also, a new state-of-the-art ensemble GAN/LSTM deep learning architecture is proposed to predict each sub band workload time-series individually, based on its degree of complexity and volatility. Our novel ensemble GAN/LSTM architecture, which employs stacked LSTM blocks as its generator and 1D ConvNets as discriminator, can exploit the long-term nonlinear dependencies of cloud workload time-series effectively specially in high-frequency, noise-like components. By validating our approach using extensive set of experiments with standard real cloud workload traces, we confirm that E2LG provides significant improvements in cloud workload prediction accuracy with respect to the mean absolute and standard deviation of the prediction error and outperforming traditional and state-of-the-art deep learning approaches. It improves the prediction accuracy at least 5% and 12% in average compared to the main contemporary approaches in recent papers such as hybrid methods which employs CNN, LSTM or SVR.</p>", "Keywords": "Cloud computing; Workload prediction; EMD; LSTM; ConvNet; GAN", "DOI": "10.1007/s11227-021-03723-6", "PubYear": 2021, "Volume": "77", "Issue": "10", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Amirkabir University of Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Amirkabir University of Technology, Tehran, Iran"}], "References": [{"Title": "A hybrid wavelet decomposer and GMDH-ELM ensemble model for Network function virtualization workload forecasting in cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "105940", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multivariate time series forecasting via attention-based encoder–decoder framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "388", "Issue": "", "Page": "269", "JournalTitle": "Neurocomputing"}, {"Title": "Ensemble learning based predictive framework for virtual machine resource request prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "397", "Issue": "", "Page": "20", "JournalTitle": "Neurocomputing"}, {"Title": "Robust IoT time series classification with data compression and deep learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "398", "Issue": "", "Page": "222", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 87237116, "Title": "Special issue on new generations of UI testing", "Abstract": "", "Keywords": "", "DOI": "10.1002/stvr.1770", "PubYear": 2021, "Volume": "31", "Issue": "3", "JournalId": 9990, "JournalTitle": "Software Testing, Verification and Reliability", "ISSN": "0960-0833", "EISSN": "1099-1689", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering, Faculty of Computing, Blekinge Institute of Technology, Karlskrona, Sweden"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Control and Computer Engineering, Politecnico di Torino, Corso Duca degli Abruzzi, 24, Turin, 10129 Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Control and Computer Engineering, Politecnico di Torino, Corso Duca degli Abruzzi, 24, Turin, 10129 Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Chalmers University of Technology, Sweden"}], "References": []}, {"ArticleId": 87237139, "Title": "Enhanced Non-parametric Sequence-based Learning Algorithm for Outlier Detection in the Internet of Things", "Abstract": "<p>Although research on outlier detection methods has been an investigation area for long, few of those studies relate to an Internet of Things (IoT) domain. Several critical decisions taken on daily business operations depend on various data collected over time. Therefore, it is mandatory to guarantee its correctness, integrity, and accuracy before any further processing can commence. Outliers are often assumed to be Error by most algorithms in the past, which is always attributed to faulty sensors. Hence, this assumption has been investigated and results show that outliers can be classified into Error and Event types with the support of a Non-parametric sequence-based learning algorithm. The event type outlier is majorly caused by abnormality from sensor readings, which are very important and should not be ignored. However, the non-parametric sequence approach and other existing techniques still find it elusive to detect outliers in the global search space of a large dataset. Therefore, this paper proposes an Enhanced Non-parametric sequence learning algorithm based on Ensemble Clustering Techniques to detect Event and Error outliers in large datasets. Experiments are conducted on six different datasets from the UCL repository, except one collected from a laboratory testbed, to demonstrate the robustness and effectiveness of the proposed approach over the existing techniques. The results show a remarkable performance rate of 96.653% accuracy, 94.284% precision, and 98.112% for Error outlier detection. It also performs better in Event outlier detection with 87.611% accuracy, 71.141% precision and 85.755% specificity with 1291 s execution time.</p>", "Keywords": "Internet of Things; Outliers; Agglomerative clustering; Gaussian mixture model; Resolution outlier factor", "DOI": "10.1007/s11063-021-10473-2", "PubYear": 2021, "Volume": "53", "Issue": "3", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, UniversitiTeknologi Malaysia (UTM), Skudai, Malaysia;Department of Computer Science, Delta State University, Abraka, Nigeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, UniversitiTeknologi Malaysia (UTM), Skudai, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Information Systems, UniversitiTeknologi Malaysia (UTM), Skudai, Malaysia"}], "References": [{"Title": "GARUDA: Gaussian dissimilarity measure for feature representation and anomaly detection in Internet of things", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "6", "Page": "4376", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Randomized nonlinear one-class support vector machines with bounded loss function to detect of outliers for large scale IoT data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "715", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 87237168, "Title": "Natural Deduction Bottom Up", "Abstract": "The paper introduces a new type of rules into Natural Deduction, elimination rules by composition. Elimination rules by composition replace usual elimination rules in the style of disjunction elimination and give a more direct treatment of additive disjunction, multiplicative conjunction, existence quantifier and possibility modality. Elimination rules by composition have an enormous impact on proof-structures of deductions: they do not produce segments, deduction trees remain binary branching, there is no vacuous discharge, there is only few need of permutations. This new type of rules fits especially to substructural issues, so it is shown for Lambek Calculus, i.e. intuitionistic non-commutative linear logic and to its extensions by structural rules like permutation, weakening and contraction. Natural deduction formulated with elimination rules by composition from a complexity perspective is superior to other calculi.", "Keywords": "Natural deduction; Intuitionistic logic; Lambek calculus; Linear logic; Normalisation; 03B47; 03F05; 03F07; 03F52; 68Q42", "DOI": "10.1007/s10849-021-09329-8", "PubYear": 2021, "Volume": "30", "Issue": "3", "JournalId": 5283, "JournalTitle": "Journal of Logic, Language and Information", "ISSN": "0925-8531", "EISSN": "1572-9583", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Tuebingen, Tuebingen, Germany"}], "References": []}, {"ArticleId": 87237537, "Title": "New advances in Instrument Detection and Control", "Abstract": "", "Keywords": "", "DOI": "10.1080/21642583.2021.1892280", "PubYear": 2021, "Volume": "9", "Issue": "sup1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering & Automation, Henan Polytechnic University, Jiaozuo, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Henan Institute of Science and Technology, Xinxiang, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering & Automation, Henan Polytechnic University, Jiaozuo, People's Republic of China"}], "References": []}, {"ArticleId": 87237598, "Title": "Broadband high gain coupled and cascaded circular ring antenna", "Abstract": "<p>In this article, we designed a broadband high gain coupled and cascaded circular ring antenna using circular rings. Two coupled rings consist of bandwidth enhancements, and two in-phase rings are cascaded with coupled rings, two enhanced the gain. The antenna is simulated, fabricated, and measured. The measurements have 10 dB bandwidth 25% (2.65-3.4 GHz) with a gain of 6.5 to 13 dBi over the bandwidth. The designed antenna occupied a total planner area of 2.54 λ<sup>2</sup> and height 0.16 λ. The designed antenna is suitable for mobile base stations, airborne, and radar communication.</p>", "Keywords": "broadband;cascaded rings;coupled rings;high gain and metallic antenna", "DOI": "10.1002/mmce.22629", "PubYear": 2021, "Volume": "31", "Issue": "6", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, NIT Patna, Patna, Bihar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, IIT Bombay, Mumbai, Maharashtra, India"}], "References": []}, {"ArticleId": 87237716, "Title": "Parallel FCM clustering algorithm of fuzzy number based on cut set", "Abstract": "<p>Aiming at the problem of complex method and low efficiency of fuzzy numbers in classification processing, a parallel Fuzzy CMeans (FCM) clustering method based on cut set is proposed. Firstly, according to the decomposition theorem, the fuzzy numbers are divided horizontally into the form of the union of interval numbers, and then the interval numbers are transformed into the determined “real” data, and the parallel FCM clustering algorithm is used to classify the fuzzy numbers. The theoretical analysis and application show that the method has good classification accuracy and efficiency for fuzzy data clustering.</p>", "Keywords": "", "DOI": "10.3233/JCM-204838", "PubYear": 2021, "Volume": "21", "Issue": "4", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87237730, "Title": "Evolution of e-participation in Greek local government", "Abstract": "<p>Local governments are increasingly developing electronic participation (e-participation) initiatives, expecting citizen involvement in local community affairs. Our objective was to assess e-participation and the extent of its change in local government in Greece. Using content analysis for 325 Greek municipal websites, we assessed e-participation status in 2017 and 2018 and examined the impact of change between these years. The assessment regards two consecutive years, since the adoption of digital technologies by municipalities has been rapid. The main findings show that Greek local governments have made significant small- to medium-scale changes, in order to engage citizens and local societies electronically. We conclude that the integration of advanced digital technologies in municipalities remains underdeveloped. We propose that Greek municipalities need to consider incorporating new technologies, such as mobile apps, social media and big data, as well as e-decision making processes, in order to eliminate those obstacles that hinder citizen engagement in local government. Moreover, the COVID-19 outbreak has highlighted the need for enhancing e-participation and policymakers’ coordination through advanced digital technologies.</p>", "Keywords": "", "DOI": "10.3233/IP-190174", "PubYear": 2021, "Volume": "26", "Issue": "3", "JournalId": 28496, "JournalTitle": "Information Polity", "ISSN": "1570-1255", "EISSN": "1875-8754", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural Economics and Rural Development, Agricultural University of Athens, Athens, Greece"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Agricultural Economics and Rural Development, Agricultural University of Athens, Athens, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, University of Piraeus, Piraeus, Greece"}], "References": []}, {"ArticleId": 87237740, "Title": "Design and implementation of electronic circuit virtual laboratory based on virtual reality technology", "Abstract": "<p>Virtual reality technology has been applied in virtual labs and remote experiment teaching. This paper designs an electronic circuit virtual laboratory based on virtual reality technology, and relevant experimental teaching content. The system uses Unity 3D and 3ds max tools to build a three-dimensional model of general instrumentation, electronic components, and laboratory scenes in electronic circuit experiments. The C# is used to develop general virtual instrumentation functions and electronic circuit simulation, the electronic circuit virtual reality laboratory based on VR equipment was realized. We have developed VR electronic circuit virtual laboratory on PC and web. Students can use VR electronic circuit virtual laboratory to learn basic electronic circuit knowledge and carry out electronic circuit virtual experiment immersively.</p>", "Keywords": "", "DOI": "10.3233/JCM-204742", "PubYear": 2021, "Volume": "21", "Issue": "5", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87237751, "Title": "A wireless real-time monitoring system on intelligent underwater pollution cleaning robot", "Abstract": "<p>Intelligent underwater pollution cleaning robot is used to release microbial solution which can dissolve into water slowly into polluted river, so that the solution can react fully with pollutants, so as to achieve the purpose of river pollution control. The research of robot wireless monitoring system is based on the comprehensive application of wireless communication technology and intelligent control technology, in order to achieve real-time monitoring and centralized remote control of underwater pollution removal. Through the three-dimensional structure modeling of the intelligent underwater pollution cleaning robot, the overall scheme design and debugging test of the wireless monitoring system, it is proved that the intelligent underwater pollution cleaning robot is feasible in the intelligent and efficient underwater cleaning operation, and it is a research method worthy of reference and promotion.</p>", "Keywords": "", "DOI": "10.3233/JCM-204780", "PubYear": 2021, "Volume": "21", "Issue": "4", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electronic Engineering, Hubei Polytechnic University, Huangshi, Hubei 435003, China;Postdoctoral Research Workstation, Huangshi Dongbei Electrical Appliance Co., Ltd., Huangshi, Hubei 435000, China;School of Energy and Power Engineering, Huazhong University of Science and Technology, Wuhan, Hubei 430074, China;Hubei Key Laboratory of Intelligent Conveying Technology and Device, Huangshi, Hubei 435003, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Electronic Engineering, Hubei Polytechnic University, Huangshi, Hubei 435003, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Postdoctoral Research Workstation, Huangshi Dongbei Electrical Appliance Co., Ltd., Huangshi, Hubei 435000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Energy and Power Engineering, Huazhong University of Science and Technology, Wuhan, Hubei 430074, China"}], "References": []}, {"ArticleId": 87237886, "Title": "Exponential discounting in security games of timing", "Abstract": "Abstract \n Strategic game models of defense against stealthy, targeted attacks that cannot be prevented but only mitigated are the subject of a significant body of recent research, often in the context of advanced persistent threats (APTs). In these game models, the timing of attack and defense moves plays a central role. A common assumption, in this literature, is that players are indifferent between costs and gains now and those in the distant future, which conflicts with the widely accepted treatment of intertemporal choice across economic contexts. This article investigates the significance of this assumption by studying changes in optimal player behavior when introducing time discounting. Specifically, we adapt a popular model in the games of timing literature, the FlipIt model, by allowing for exponential discounting of gains and costs over time. We investigate changes of best responses and the location of Nash equilibria through analysis of two well-known classes of player strategies: those where the time between players’ moves is constant, and a second class where the time between players’ moves is stochastic and exponentially distributed. By introducing time discounting in the framework of games of timing, we increase its level of realism as well as applicability to organizational security management, which is in dire need of sound theoretic work to respond to sophisticated, stealthy attack vectors.", "Keywords": "", "DOI": "10.1093/cybsec/tyaa008", "PubYear": 2021, "Volume": "7", "Issue": "1", "JournalId": 2358, "JournalTitle": "Journal of Cybersecurity", "ISSN": "2057-2085", "EISSN": "2057-2093", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "imec-DistriNet, KU Leuven, 3000 Leuven, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Informatics, Technical University of Munich, 85748r Garching, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, Technical University of Munich, 85748r Garching, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "imec-DistriNet, KU Leuven, 3000 Leuven, Belgium"}], "References": []}, {"ArticleId": 87237934, "Title": "Static Force Measurement Using Piezoelectric Sensors", "Abstract": "<p>In force measurement applications, a piezoelectric force sensor is one of the most popular sensors due to its advantages of low cost, linear response, and high sensitivity. Piezoelectric sensors effectively convert dynamic forces to electrical signals by the direct piezoelectric effect, but their use has been limited in measuring static forces due to the easily neutralized surface charge. To overcome this shortcoming, several static (either pure static or quasistatic) force sensing techniques using piezoelectric materials have been developed utilizing several unique parameters rather than just the surface charge produced by an applied force. The parameters for static force measurement include the resonance frequency, electrical impedance, decay time constant, and capacitance. In this review, we discuss the detailed mechanism of these piezoelectric-type, static force sensing methods that use more than the direct piezoelectric effect. We also highlight the challenges and potentials of each method for static force sensing applications.</p>", "Keywords": "", "DOI": "10.1155/2021/6664200", "PubYear": 2021, "Volume": "2021", "Issue": "1", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, North Carolina State University, Raleigh, NC 27695, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Joint Department of Biomedical Engineering, The University of North Carolina at Chapel Hill and North Carolina State University, Chapel Hill, NC 27599, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, North Carolina State University, Raleigh, NC 27695, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and System Engineering, Korea Military Academy, Seoul 01805, Republic of Korea"}], "References": []}, {"ArticleId": 87238031, "Title": "La fotografía en el aula de ciencias: propuestas didácticas", "Abstract": "<p>En este trabajo se proponen dos estrategias para la enseñanza de la química a nivel medio superior, en donde se utilizan como herramientas principales el teléfono inteligente (smartphone) y la fotografía. Las propuestas presentadas son integrales, porque además de lograr los aprendizajes estipulados en los programas de estudios, permiten que el estudiante se relacione con su entorno, desarrolle habilidades de expresión escrita e de intercambio ideas. Palabras clave: Fotografía, educación, química, teléfono inteligente.</p>", "Keywords": "", "DOI": "10.22201/cuaieed.16076079e.2021.22.2.8", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Facultad de Química"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CCH Sur"}], "References": []}, {"ArticleId": 87238358, "Title": "LocusZoom.js: interactive and embeddable visualization of genetic association study results", "Abstract": "Summary \n LocusZoom.js is a JavaScript library for creating interactive web-based visualizations of genetic association study results. It can display one or more traits in the context of relevant biological data (such as gene models and other genomic annotation), and allows interactive refinement of analysis models (by selecting linkage disequilibrium reference panels, identifying sets of likely causal variants, or comparisons to the GWAS catalog). It can be embedded in web pages to enable data sharing and exploration. Views can be customized and extended to display other data types such as phenome-wide association study (PheWAS) results, chromatin co-accessibility, or eQTL measurements. A new web upload service harmonizes datasets, adds annotations, and makes it easy to explore user-provided result sets.\n \n \n Availability and implementation \n LocusZoom.js is open-source software under a permissive MIT license. Code and documentation are available at: https://github.com/statgen/locuszoom/. Installable packages for all versions are also distributed via NPM. Additional features are provided as standalone libraries to promote reuse. Use with your own GWAS results at https://my.locuszoom.org/.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btab186", "PubYear": 2021, "Volume": "37", "Issue": "18", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Biostatistics and the Center for Statistical Genetics, University of Michigan, Ann Arbor, MI 48109, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Biostatistics and the Center for Statistical Genetics, University of Michigan, Ann Arbor, MI 48109, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Biostatistics and the Center for Statistical Genetics, University of Michigan, Ann Arbor, MI 48109, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Biostatistics and the Center for Statistical Genetics, University of Michigan, Ann Arbor, MI 48109, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Biostatistics and the Center for Statistical Genetics, University of Michigan, Ann Arbor, MI 48109, USA"}, {"AuthorId": 6, "Name": "Gonçalo <PERSON>", "Affiliation": "Department of Biostatistics and the Center for Statistical Genetics, University of Michigan, Ann Arbor, MI 48109, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Biostatistics and the Center for Statistical Genetics, University of Michigan, Ann Arbor, MI 48109, USA"}], "References": []}, {"ArticleId": 87238359, "Title": "Fast and sensitive taxonomic assignment to metagenomic contigs", "Abstract": "Summary \n MMseqs2 taxonomy is a new tool to assign taxonomic labels to metagenomic contigs. It extracts all possible protein fragments from each contig, quickly retains those that can contribute to taxonomic annotation, assigns them with robust labels and determines the contig’s taxonomic identity by weighted voting. Its fragment extraction step is suitable for the analysis of all domains of life. MMseqs2 taxonomy is 2–18× faster than state-of-the-art tools and also contains new modules for creating and manipulating taxonomic reference databases as well as reporting and visualizing taxonomic assignments.\n \n \n Availability and implementation \n MMseqs2 taxonomy is part of the MMseqs2 free open-source software package available for Linux, macOS and Windows at https://mmseqs.com.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btab184", "PubYear": 2021, "Volume": "37", "Issue": "18", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Quantitative and Computational Biology, Max Planck Institute for Biophysical Chemistry, Göttingen, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Biological Sciences, Seoul National University, Seoul, South Korea;Institute of Molecular Biology and Genetics, Seoul National University, Seoul, South Korea;Artificial Intelligence Institute, Seoul National University, Seoul, South Korea"}, {"AuthorId": 3, "Name": "F Breitwieser", "Affiliation": "Center for Computational Biology, McKusick-Nathans Institute of Genetic Medicine, Johns Hopkins School of Medicine, Baltimore, MD 21205, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Quantitative and Computational Biology, Max Planck Institute for Biophysical Chemistry, Göttingen, Germany;Campus-Institut Data Science (CIDAS), Göttingen, Germany"}, {"AuthorId": 5, "Name": "<PERSON> <PERSON>", "Affiliation": "Quantitative and Computational Biology, Max Planck Institute for Biophysical Chemistry, Göttingen, Germany"}], "References": []}, {"ArticleId": 87238520, "Title": "Application of Gradient Boosting Algorithms for Anti-money Laundering in Cryptocurrencies", "Abstract": "<p>The recent emergence of cryptocurrencies has added another layer of complexity in the fight towards financial crime. Cryptocurrencies require no central authority and offer pseudo-anonymity to its users, allowing criminals to disguise themselves among legitimate users. On the other hand, the openness of data fuels the investigator’s toolkit to conduct forensic examinations. This study focuses on the detection of illicit activities (e.g., scams, financing terrorism, and Ponzi schemes) on cryptocurrency infrastructures, both at an account and transaction level. Previous work has identified that class imbalance and the dynamic environment created by the evolving techniques deployed by criminals to avoid detection are widespread in this domain. In our study, we propose Adaptive Stacked eXtreme Gradient Boosting (ASXGB), an adaptation of eXtreme Gradient Boosting (XGBoost), to better handle dynamic environments and present a comparative analysis of various offline decision tree-based ensembles and heuristic-based data-sampling techniques. Our results show that: (i) offline decision tree-based gradient boosting algorithms outperform state-of-the-art Random Forest (RF) results at both an account and transaction level, (ii) the data-sampling approach NCL-SMOTE further improves recall at a transaction level, and (iii) our proposed ASXGB successfully reduced the impact of concept drift while further improving recall at a transaction level.</p>", "Keywords": "Anti-money laundering; Cryptocurrency; Supervised classification; Gradient boosting; Class imbalance; Concept drift", "DOI": "10.1007/s42979-021-00558-z", "PubYear": 2021, "Volume": "2", "Issue": "3", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Artificial Intelligence, University of Malta, Msida, Malta"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Artificial Intelligence, University of Malta, Msida, Malta"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centre for Distributed Ledger Technologies, University of Malta, Msida, Malta"}], "References": [{"Title": "Detection of illicit accounts over the Ethereum blockchain", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113318", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": ********, "Title": "Transfer of semi-supervised broad learning system in electroencephalography signal classification", "Abstract": "<p>Electroencephalography (EEG) signal classification is a crucial part in motor imagery brain–computer interface (BCI) system. Traditional supervised learning methods have performed well pleasing in EEG classification. Unfortunately, the unlabeled samples are easier to collect than labeled samples. In addition, recent studies have shown that it may degenerate performance of semi-supervised learning by exploiting unlabeled samples without selection. To address these issues, a novel semi-supervised broad learning system with transfer learning (TSS-BLS) is proposed in this paper. First, the pseudo-labels of unlabeled samples are obtained using the joint distribution adaptation algorithm. TSS-BLS is then constructed by an improved manifold regularization framework containing both labeled and pseudo-label information. Finally, the effectiveness of the proposed TSS-BLS is evaluated on three BCI competition datasets and four benchmark datasets from UCI repository and compared with seven state-of-the-art algorithms, including ELM, SS-ELM, HELM, SVM, LapSVM, BLS and GSS-BLS. Experimental results show that the performance of TSS-BLS is superior to BLS and GSS-BLS on average. It is thereby shown that TSS-BLS is safe and efficient for EEG classification.</p>", "Keywords": "Brain–computer interface; Electroencephalogram; Semi-supervised learning; Transfer learning; Broad learning system", "DOI": "10.1007/s00521-021-05793-2", "PubYear": 2021, "Volume": "33", "Issue": "16", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Hangzhou DianZi University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Hangzhou DianZi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Hangzhou DianZi University, Hangzhou, China"}, {"AuthorId": 4, "Name": "Wanzeng Kong", "Affiliation": "Key Laboratory of Brain Machine Collaborative Intelligence of Zhejiang Province, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, University of Houston, Houston, USA"}], "References": []}, {"ArticleId": ********, "Title": "Detection of malicious consumer interest packet with dynamic threshold values", "Abstract": "<p>As a promising next-generation network architecture, named data networking (NDN) supports name-based routing and in-network caching to retrieve content in an efficient, fast, and reliable manner. Most of the studies on NDN have proposed innovative and efficient caching mechanisms and retrieval of content via efficient routing. However, very few studies have targeted addressing the vulnerabilities in NDN architecture, which a malicious node can exploit to perform a content poisoning attack (CPA). This potentially results in polluting the in-network caches, the routing of content, and consequently isolates the legitimate content in the network. In the past, several efforts have been made to propose the mitigation strategies for the content poisoning attack, but to the best of our knowledge, no specific work has been done to address an emerging attack-surface in NDN, which we call an interest flooding attack. Handling this attack-surface can potentially make content poisoning attack mitigation schemes more effective, secure, and robust. Hence, in this article, we propose the addition of a security mechanism in the CPA mitigation scheme that is, Name-Key Based Forwarding and Multipath Forwarding Based Inband Probe, in which we block the malicious face of compromised consumers by monitoring the Cache-Miss Ratio values and the Queue Capacity at the Edge Routers. The malicious face is blocked when the cache-miss ratio hits the threshold value, which is adjusted dynamically through monitoring the cache-miss ratio and queue capacity values. The experimental results show that we are successful in mitigating the vulnerability of the CPA mitigation scheme by detecting and blocking the flooding interface, at the cost of very little verification overhead at the NDN Routers.</p>", "Keywords": "Content poisoning attacks;Dynamic threshold;Malicious consumer interest packet;Mitigation techniques;Named data networking", "DOI": "10.7717/peerj-cs.435", "PubYear": 2021, "Volume": "7", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science, Capital University of Science and Technology, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science, Capital University of Science and Technology, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON> Naveed <PERSON>", "Affiliation": "College of Engineering and IT, Ajman University, Ajman, United Arab Emirates"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> of Engineering, University of Glasgow, Glasgow, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Computer Science, Capital University of Science and Technology, Islamabad, Pakistan"}], "References": [{"Title": "MSIDN: Mitigation of Sophisticated Interest flooding-based DDoS attacks in Named Data Networking", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "293", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 87238668, "Title": "Hyperparameter tuning and comparison of k nearest neighbour and decision tree algorithms for cardiovascular disease prediction", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSI.2021.10036482", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 28760, "JournalTitle": "International Journal of Swarm Intelligence", "ISSN": "2049-4041", "EISSN": "2049-405X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87238783, "Title": "Folk Values for Beginners", "Abstract": "<p>The inherently diverse folkloric values are the applied aspects of folk philosophy. Amongst these values, the paper focusses on a few specific tenets that are transplanted in the beginners' hearts to counteract the current culture of thriving consumption that instigates competition and greed, profit-making use of science and technology, and moral degradation. These values are also linked to the globalisation of sustainability education to counteract the destruction of the natural and social environment. They need major attention right from the primary level of education. Using the case study of Bangladesh, the paper presents a synopsis of the core folkloric tradition of the country and critically analyses the substantial impact of folk values on the daily lives of its inhabitants as well as discusses how these values can contribute to sustainability.</p>", "Keywords": "", "DOI": "10.4018/IJISSC.**********", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 14494, "JournalTitle": "International Journal of Information Systems and Social Change", "ISSN": "1941-868X", "EISSN": "1941-8698", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Curtin University, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Rajshahi University, Bangladesh"}], "References": []}, {"ArticleId": 87238784, "Title": "Rural Face and Sustainable Urban Liveability", "Abstract": "<p>The present research investigates the significance of ‘rural face' from the lens of liveability and analyses what urban theorists can learn from this concept to overcome the unsustainability crisis of the present urban paradigm. Rural face attributes are discussed in terms of tangible (ecological and economical) and intangible (social) dimensions and linked to the sustainability principles they entail. The rustic environment, practices, livelihood, folklore, and culture are examined using the example of rural areas in Pakistan. This research concludes that for urban sustainability to become a reality it needs to assimilate attributes of rural face into the city realm and prompts rethinking the rural-urban relationship from a different perspective.</p>", "Keywords": "", "DOI": "10.4018/IJISSC.**********", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 14494, "JournalTitle": "International Journal of Information Systems and Social Change", "ISSN": "1941-868X", "EISSN": "1941-8698", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Curtin University, Australia"}], "References": []}, {"ArticleId": 87238785, "Title": "Turkish Cuisine", "Abstract": "<p>Turkish cuisine is one of the richest in the world with its strong historical and intercultural background. Contrary to the common perception, Turkish food culture is not all meat-centric; it has an incredibly rich diversity of vegan choices. Traditional Turkish cuisine also extends to ceremonial and traditional occasions, such as weddings, burial ceremonies, some religious events, circumcisions, migrations, journeys, hosting guests, and welcoming newborns. On these occasions, Turks prepare the best and most loved traditional foods to eat and share together. The prepared foods are mostly vegetarian-friendly, and meat is consumed in very restricted amounts. This paper explores the planet- and vegan-friendly features of the Turkish food culture and its links with folklore.</p>", "Keywords": "", "DOI": "10.4018/IJISSC.**********", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 14494, "JournalTitle": "International Journal of Information Systems and Social Change", "ISSN": "1941-868X", "EISSN": "1941-8698", "Authors": [{"AuthorId": 1, "Name": "İsmail Hakkı Tekiner", "Affiliation": "Istanbul Sabahattin Zaim University, Turkey"}], "References": []}, {"ArticleId": 87238786, "Title": "Interculturality, Identity, and Decoloniality", "Abstract": "<p>This paper focuses initially on the findings of research undertaken by colleagues-researchers from different countries. Then, the authors explore the postcolonial intercultural challenges from the Abya Yala point of view. The relationship with aboriginal and ancestral peoples is very relevant for understanding power and knowledge in historical processes. The contemporary globalizing world faces new challenges intensified by international connections, by sociocultural movements, and now, by pandemic context. These circumstances of greater interconnectivity and interdependence require each group to reflect and consider their own limits and thresholds in intercultural relationship with others and ecological priorities. The concept of “thought bordering” is discussed outlining its ability to interrogate the modern idea of culture as unique and universal. While greater interconnectivity offers the opportunity for multiple paradigms to emerge, it can also close off chances for mutual recognition and for solidarity if approached without thoughtful engagement. Thought bordering offers us the opportunity to facilitate different ways of being–feeling–thinking–acting, thus promoting an ontological shift that will enable respectful engagements with communities, societies, and ecologies. In this perspective, one is learning from the ancestral peoples about “well-living,” cultivating reciprocity, integrality, complementarity, and relationality in social and ecological relations.</p>", "Keywords": "", "DOI": "10.4018/IJISSC.2021010104", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 14494, "JournalTitle": "International Journal of Information Systems and Social Change", "ISSN": "1941-868X", "EISSN": "1941-8698", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Para State, Brazil"}], "References": []}, {"ArticleId": 87238839, "Title": "ProDy 2.0: increased scale and scope after 10 years of protein dynamics modelling with Python", "Abstract": "Summary \n ProDy, an integrated application programming interface developed for modelling and analysing protein dynamics, has significantly evolved in recent years in response to the growing data and needs of the computational biology community. We present major developments that led to ProDy 2.0: (i) improved interfacing with databases and parsing new file formats, (ii) SignDy for signature dynamics of protein families, (iii) CryoDy for collective dynamics of supramolecular systems using cryo-EM density maps and (iv) essential site scanning analysis for identifying sites essential to modulating global dynamics.\n \n \n Availability and implementation \n ProDy is open-source and freely available under MIT License from https://github.com/prody/ProDy.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btab187", "PubYear": 2021, "Volume": "37", "Issue": "20", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computational and Systems Biology, School of Medicine, University of Pittsburgh, Pittsburgh, PA 15213, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computational and Systems Biology, School of Medicine, University of Pittsburgh, Pittsburgh, PA 15213, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computational and Systems Biology, School of Medicine, University of Pittsburgh, Pittsburgh, PA 15213, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational and Systems Biology, School of Medicine, University of Pittsburgh, Pittsburgh, PA 15213, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational and Systems Biology, School of Medicine, University of Pittsburgh, Pittsburgh, PA 15213, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>Rumi<PERSON>", "Affiliation": "Department of Computational and Systems Biology, School of Medicine, University of Pittsburgh, Pittsburgh, PA 15213, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational and Systems Biology, School of Medicine, University of Pittsburgh, Pittsburgh, PA 15213, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational and Systems Biology, School of Medicine, University of Pittsburgh, Pittsburgh, PA 15213, USA"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computational and Systems Biology, School of Medicine, University of Pittsburgh, Pittsburgh, PA 15213, USA"}], "References": [{"Title": "QuartataWeb: Integrated Chemical–Protein-Pathway Mapping for Polypharmacology and Chemogenomics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON> <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "12", "Page": "3935", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 87239013, "Title": "DLGEA: a deep learning guided evolutionary algorithm for water contamination source identification", "Abstract": "<p>Water distribution network (WDN) is one of the most essential infrastructures all over the world and ensuring water quality is always a top priority. To this end, water quality sensors are often deployed at multiple points of WDNs for real-time contamination detection and fast contamination source identification (CSI). Specifically, CSI aims to identify the location of the contamination source, together with some other variables such as the starting time and the duration. Such information is important in making an efficient plan to mitigate the contamination event. In the literature, simulation-optimisation methods, which combine simulation tools with evolutionary algorithms (EAs), show great potential in solving CSI problems. However, the application of EAs for CSI is still facing big challenges due to their high computational cost. In this paper, we propose DLGEA, a deep learning guided evolutionary algorithm to improve the efficiency by optimising the search space of EAs. Firstly, based on a large number of simulated contamination events, DLGEA trains a deep neural network (DNN) model to capture the relationship between the time series of sensor data and the contamination source nodes. Secondly, given a contamination event, DLGEA guides the initialisation and optimise the search space of EAs based on the top K contamination nodes predicated by the DNN model. Empirically, based on two benchmark WDNs, we show that DLGEA outperforms the CSI method purely based on EAs in terms of both the average topological distance and the accumulated errors between the predicted and the real contamination events.</p>", "Keywords": "Contamination source identification; Deep neural networks; Evolutionary algorithms; Time series analysis; Water distribution networks; Water quality sensors", "DOI": "10.1007/s00521-021-05894-y", "PubYear": 2021, "Volume": "33", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "@mail.sustech.edu.cn;Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen, China"}], "References": [{"Title": "Pollution source intelligent location algorithm in water quality sensor networks", "Authors": "Xuesong Yan; Jingyu Gong; Qinghua Wu", "PubYear": 2021, "Volume": "33", "Issue": "1", "Page": "209", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 87239033, "Title": "A Deep Learning Model to predict the Industry Readiness of Engineering Students Community", "Abstract": "Industry readiness of Engineering students community is a big challenge in the recent campus recruitments. 21st century skills are completely mapped with the technical and non – technical knowledge background of the engineering graduates. In this paper the work narrated the process of identifying the parameters for skill assessment of the candidates and derived a learner model using deep learning framework. Further the model can be used to predict the employability readiness of candidates.", "Keywords": "", "DOI": "10.1051/itmconf/20213701017", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Assistant Professor/CSE,Rajalakshmi Institute of Technology, Chennai"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Professor/CSE, <PERSON><PERSON><PERSON><PERSON> Gandhi College of Technology, Salem"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Assistant Professor/MBA, SRMValliammai Engineering College, Chennai"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Professor/MBA, Velammal Engineering College, Chennai"}], "References": [{"Title": "Enhancing Learners’ Experience Through Extending Learning Systems", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "3", "Page": "540", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "Where is the Learning in Learning Analytics? A Systematic Literature Review on the Operationalization of Learning-Related Constructs in the Evaluation of Learning Analytics Interventions", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "3", "Page": "631", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "Characterizing Learners’ Engagement in MOOCs: An Observational Case Study Using the NoteMyProgress Tool for Supporting Self-Regulation", "Authors": "<PERSON>-<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "676", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "Automatic Method to Identify E-Learner Emotions Using Behavioral Cues", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "762", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "Context-Based Data Model for Effective Real-Time Learning Analytics", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "790", "JournalTitle": "IEEE Transactions on Learning Technologies"}]}, {"ArticleId": 87239034, "Title": "Internet of Things in Agriculture to Revolutionize Traditional Agricultural Industry", "Abstract": "Agriculture is familiarly called “Farming”. Agriculture is the basic art to cultivate food which is a necessary need to every living individual. Agriculture needs the practice of science for cultivating the soil factors and growing crops. In traditional farming, it includes more labor work and less yield quantity. This demerit can be overcome by the modern farming techniques which makes use of the advanced technology and focuses on maximizing the yield and maintaining the quality. Earlier the farmers used to figure out the type of the soil based on their suspicion and they would never think of the humidity, temperature, climatic condition and especially the level of water. IoT is trying to overcome all these factors by helping to assemble the information. This paper focuses on the soil moisture and soil type which lets the farmer know about the type of crops to be grown.", "Keywords": "", "DOI": "10.1051/itmconf/20213701018", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "VB Kirubanand", "Affiliation": "Department of Computer Science, CHRIST (Deemed to be University) Bangalore- 560029"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, CHRIST (Deemed to be University) Bangalore- 560029"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, CHRIST (Deemed to be University) Bangalore- 560029"}], "References": []}, {"ArticleId": 87239049, "Title": "Blockchain based mechanism to eliminate frauds and tampering of land records", "Abstract": "Data tampering and fraud in land records have increased drastically in the modern world. A data storage model using Blockchain and Interplanetary File System (IPFS) is proposed in this work. Land records and the farmer’s information are stored inside the Interplanetary file system. To avoid data faking, the hash address of the respective data generated by IPFS is stored in the blockchain. This proposed system when deployed on a large scale can outperform the existing methods of securing user data. One of the latest technological advancements in the software industry is the innovation of Blockchain Technology. This new technology has opened up a new business relationship platform that delivers feasibility, protection, and cheap rates. It provides a new foundation of trust for transactions that can facilitate a very streamlined workflow and a faster economy.", "Keywords": "", "DOI": "10.1051/itmconf/***********", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "D <PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sri Krishna College of Engineering and Technology, India"}, {"AuthorId": 2, "Name": "<PERSON> <PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sri Krishna College of Engineering and Technology, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sri Krishna College of Engineering and Technology, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sri Krishna College of Engineering and Technology, India"}], "References": []}, {"ArticleId": 87239086, "Title": "Swarm SLAM: Challenges and Perspectives", "Abstract": "<p>A robot swarm is a decentralized system characterized by locality of sensing and communication, self-organization, and redundancy. These characteristics allow robot swarms to achieve scalability, flexibility and fault tolerance, properties that are especially valuable in the context of simultaneous localization and mapping (SLAM), specifically in unknown environments that evolve over time. So far, research in SLAM has mainly focused on single- and centralized multi-robot systems—i.e., non-swarm systems. While these systems can produce accurate maps, they are typically not scalable, cannot easily adapt to unexpected changes in the environment, and are prone to failure in hostile environments. Swarm SLAM is a promising approach to SLAM as it could leverage the decentralized nature of a robot swarm and achieve scalable, flexible and fault-tolerant exploration and mapping. However, at the moment of writing, swarm SLAM is a rather novel idea and the field lacks definitions, frameworks, and results. In this work, we present the concept of swarm SLAM and its constraints, both from a technical and an economical point of view. In particular, we highlight the main challenges of swarm SLAM for gathering, sharing, and retrieving information. We also discuss the strengths and weaknesses of this approach against traditional multi-robot SLAM. We believe that swarm SLAM will be particularly useful to produce abstract maps such as topological or simple semantic maps and to operate under time or cost constraints.</p>", "Keywords": "swarm robotics; Mapping; distributed systems; SLAM; exploration schemes; localization", "DOI": "10.3389/frobt.2021.618268", "PubYear": 2021, "Volume": "8", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Belgium"}], "References": [{"Title": "Sparse Robot Swarms: Moving Swarms to Real-World Applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "83", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Disentangling automatic and semi-automatic approaches to the optimization-based design of control software for robot swarms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "9", "Page": "494", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Koord: a language for programming and verifying distributed robotics application", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 87239231, "Title": "Similarity Measures and Multi-person TOPSIS Method Using m-polar Single-Valued Neutrosophic Sets", "Abstract": "", "Keywords": "m-polar single-valued neutrosophic set, Distance measure, Ssimilarity measure, Pattern recognition, Multi-person TOPSIS technique", "DOI": "10.2991/ijcis.d.210203.003", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87239237, "Title": "Few-Shot Image Segmentation Based on Dual Comparison Module and Sequential k-Shot Integration", "Abstract": "", "Keywords": "Few-shot learning, Image segmentation, Dual comparison module, Convolutional-gated recurrent unit", "DOI": "10.2991/ijcis.d.210212.003", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "Chencong Xing", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Lyu", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87239239, "Title": "Psychological Health Status Evaluation of the Public in Different Areas Under the Outbreak of Novel Coronavirus Pneumonia", "Abstract": "", "Keywords": "COVID-19, LIWC, Psychological health status, Public perception", "DOI": "10.2991/ijcis.d.210225.001", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87239255, "Title": "Rumor Detection by Propagation Embedding Based on Graph Convolutional Network", "Abstract": "", "Keywords": "Rumor detection, Propagation embedding, Graph convolutional network, Feature aggregation", "DOI": "10.2991/ijcis.d.210304.002", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87239269, "Title": "Analyzing Online Shopping Behaviors via a new Data-Driven Hesitant Fuzzy Approach", "Abstract": "", "Keywords": "Online shopping, Customer behavior, Generation cohort, Hesitant fuzzy cognitive mapping, Partial least squares structural equation modeling", "DOI": "10.2991/ijcis.d.210205.003", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "M. <PERSON>ak", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87239298, "Title": "Mathematics and Computational Intelligence Synergies for Emerging Challenges", "Abstract": "", "Keywords": "", "DOI": "10.2991/ijcis.d.210121.001", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "László T. Kóczy", "Affiliation": ""}], "References": []}, {"ArticleId": 87239300, "Title": "Value-Based Reasoning in Autonomous Agents", "Abstract": "", "Keywords": "Reasoning, Model, Goals, Values, Expert systems, Autonomous agents", "DOI": "10.2991/ijcis.d.210203.001", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87239301, "Title": "Statistical and Machine Learning Approaches for Clinical Decision on Drug Usage in Diabetes with Reference to Competence and Safeness", "Abstract": "", "Keywords": "Diabetes, Clinical decision-making, Machine learning, Statistical approach, Drug usage, Drug recommendation system", "DOI": "10.2991/ijcis.d.210212.002", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Balamurugan", "Affiliation": ""}, {"AuthorId": 2, "Name": "K. <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "S. <PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87239380, "Title": "Three-Dimensional Force Prediction of a Flexible Tactile Sensor Based on Radial Basis Function Neural Networks", "Abstract": "A flexible tactile sensor array with \n \n 6 \n × \n 6 \n \n N-type sensitive elements made of conductive rubber is presented in this paper. The property and principle of the tactile sensor are analyzed in detail. Based on the piezoresistivity of conductive rubber, this paper takes full advantage of the nonlinear approximation ability of the radial basis function neural network (RBFNN) method to approach the high-dimensional mapping relation between the resistance values of the N-type sensitive element and the three-dimensional (3D) force and to accomplish the accurate prediction of the magnitude of 3D force loaded on the sensor. In the prediction process, the \n \n k \n \n -means algorithm and recursive least square (RLS) method are used to optimize the RBFNN, and the \n \n k \n \n -fold cross-validation method is conducted to build the training set and testing set to improve the prediction precision of the 3D force. The optimized RBFNN with different spreads is used to verify its influence on the performance of 3D force prediction, and the results indicate that the spread value plays a very important role in the prediction process. Then, sliding window technology is introduced to build the RBFNN model. Experimental results show that setting the size of the sliding window appropriately can effectively reduce the prediction error of the 3D force exerted on the sensor and improve the performance of the RBFNN predictor, which means that the sliding window technology is very feasible and valid in 3D force prediction for the flexible tactile sensor. All of the results indicate that the optimized RBFNN with high robustness can be well applied to the 3D force prediction research of the flexible tactile sensor.", "Keywords": "", "DOI": "10.1155/2021/8825019", "PubYear": 2021, "Volume": "2021", "Issue": "1", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Anhui Jianzhu University, Hefei 230601, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electronic and Information Engineering, Anhui Jianzhu University, Hefei 230601, China;Key Laboratory of Building Information Acquisition and Measurement Control Technology, Anhui Jianzhu University, Hefei 230601, China"}], "References": [{"Title": "A Machine-Learning-Based Approach to Solve Both Contact Location and Force in Soft Material Tactile Sensors", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "2", "Page": "409", "JournalTitle": "Soft Robotics"}]}, {"ArticleId": 87239441, "Title": "Stability and control of fuzzy time delay systems with unmatched preconditions", "Abstract": "<p>The stability and control of nonlinear time-delay systems of Takagi-Sugeno (T-S) fuzzy model are studied in this paper. The integral inequality of a free weight matrix is chosen to give a less conservative delay-dependent stability criterion in the form of linear matrix inequalities (LMIs). The premise mismatch strategy is applied, it is combined with <PERSON><PERSON> lemma, a more flexible design method of fuzzy state feedback controller is proposed. This method does not require the controller and system to share the common premise membership function and the number of rules. The controller design strategy proposed in this paper can effectively solve the control problem of fuzzy systems when the number of state variables is not equal to the number of input variables (r≠c), or mi⁢(x⁢(t))≠hi⁢(x⁢(t)),i=1,2,…,r. Finally, two simulation examples are given to prove the advancement and effectiveness of the proposed theory.</p>", "Keywords": "", "DOI": "10.3233/JCM-204722", "PubYear": 2021, "Volume": "21", "Issue": "5", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Jinding Gao", "Affiliation": ""}], "References": []}, {"ArticleId": 87239734, "Title": "VISA: a multimodal database of face and iris traits", "Abstract": "<p>In this paper, a new realistic and challenging Face-Iris multimodal biometric database called VISA database is described. One significant problem associated with the development and evaluation of multimodal biometric systems using face and iris biometric traits is the lack of publicly available multimodal databases that are acquired in an unconstrained environment. Currently, there exist no multimodal databases containing a sufficient number of common subjects involved in both face and iris data acquisition process under different conditions. The VISA database fulfills these requirements and it will be a useful tool for the design and development of new algorithms for developing multimodal biometric systems. The VISA iris images are acquired using the IriShield camera. Face images are captured using mobile device. The corpus of a new VISA database consists of face images that vary in expression, pose and illumination, and presence of occlusion whereas iris images vary in illumination, eye movement, and occlusion. A total of more than 5000 images of 100 subjects are collated and used to form the new database. The key features of the VISA dataset are the wide and diverse population of subjects (age and gender). The VISA database is able to support face and/or iris unimodal or multimodal biometric recognition. Hence, the VISA database is a useful addition for the purpose of research and development of biometric systems based on face and iris biometrics. This paper also describes the baseline results of state-of-the-art methods on the VISA dataset and other popular similar datasets. The VISA database will be made available to the public through https://vtu.ac.in/en/visa-multimodal-face-and-iris-biometrics-database/</p>", "Keywords": "Face; Iris; Database; VISA; Unimodal; Multimodal; Biometric; FFT; HoG; Gabor", "DOI": "10.1007/s11042-021-10650-4", "PubYear": 2021, "Volume": "80", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, Basaveshwar Engineering College, Bagalkot, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Centre for Post Graduate Studies, VTU, Belagavi, India"}], "References": []}, {"ArticleId": 87239803, "Title": "¿Hacia una nueva “tabla periódica”?", "Abstract": "<p>En 2019 se celebraron los 150 años de la creación de la tabla periódica por el químico ruso Mendeléiev. En ese siglo y medio, pocos son los cambios que ha sufrido la tabla periódica —el más importante es pasar del acomodo de los elementos de acuerdo con la masa atómica, al del número atómico, que propuso el físico <PERSON>, lo que demuestra la validez y el valor del trabajo del químico ruso. Pero ¿algún día existirá un reemplazo a esta tabla periódica? Y si lo tuviera, ¿cuáles serían las razones para ello? Palabras clave: nuevas tablas periódicas, universalidad química, nuevos elementos.</p>", "Keywords": "", "DOI": "10.22201/cuaieed.16076079e.2021.22.2.4", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "UNAM"}], "References": []}, {"ArticleId": 87239804, "Title": "¿Héroes o villanos? Azúcares en la salud y la enfermedad", "Abstract": "<p><PERSON><PERSON><PERSON> escuchamos hablar de azúcares, generalmente los asociamos con postres y golosinas, y en un segundo plano con enfermedades como caries, diabetes y obesidad. En redes sociales, blogs y revistas, se refuerza frecuentemente la idea de que los azúcares son nocivos para nuestra salud y por tanto hay que evitarlos. As<PERSON> pues, el carácter “virtuoso” de los azúcares es inseparable en el imaginario colectivo de su contraparte “malvada”. Además de los relacionados con la dieta, existen otros azúcares con gran diversidad funcional, cuyo papel es fundamental en varios eventos fisiológicos y patológicos. Aquí te explicamos algunos de los asombrosos procesos que son dirigidos, regulados o acompañados por estos azúcares, y analizamos sus efectos en la salud y la enfermedad. Palabras clave: az<PERSON>cares, glicoconjugados, infección, interacciones humano-patógeno.</p>", "Keywords": "", "DOI": "10.22201/cuaieed.16076079e.2021.22.2.5", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Juárez Autónoma de Tabasco"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instituto Tecnológico de Veracruz"}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON>", "Affiliation": "Universidad de Würzburg"}], "References": []}, {"ArticleId": 87239805, "Title": "Alumnos en pandemia: una mirada desde el aprendizaje autónomo", "Abstract": "<p>El aprendizaje autónomo es un concepto que refiere a la formación de los estudiantes como aprendices, con la capacidad de tomar el control sobre su propio proceso de aprendizaje, para el resto de la vida. El año 2020 ha sido determinante para mostrar la necesidad y urgencia de fomentar habilidades que permitan a los estudiantes ser autogestivos, y regular sus procesos de aprendizaje. En este contexto emergente actual, es necesario preguntarnos, como docentes, cómo apoyamos a los estudiantes en el fomento de estas habilidades, cuáles son las prioritarias y cuáles las que se tendrían que impulsar, ya sea porque existe una deficiencia en ellas o un mayor interés entre los jóvenes, y cómo podemos ayudar a los alumnos en la mejora de su proceso de aprendizaje. Con la intención de generar opciones viables en el marco de la pandemia de la COVID-19, se inició una investigación sobre el nivel de autonomía que tienen los estudiantes en México. Para ello, durante el mes de mayo de 2020 se aplicó el “Cuestionario aprendizaje autónomo en tiempos de COVID-19”. Este trabajo presenta los resultados de dicho sondeo. Palabras clave: aprendizaje autónomo, pandemia, evaluación del aprendizaje autónomo.</p>", "Keywords": "", "DOI": "10.22201/cuaieed.16076079e.2021.22.2.11", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CUAIEED"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CUAIEED"}], "References": []}, {"ArticleId": 87239847, "Title": "Conchas marinas: exquisitos termómetros", "Abstract": "<p>Casi todos hemos admirado la exquisita arquitectura de las conchas marinas, sus colores y disfrutado su delicioso contenido ¿Sabía usted que su química nos puede decir la temperatura del mar? Si tomamos la concha de una almeja o un ostión y analizamos las diferencias entre las masas de la multitud de átomos de oxígeno y carbono que la componen, podemos saber la temperatura a la que se formó, y con ello, la temperatura del agua. Esto es gracias a la novedosa técnica de isótopos estables agregados, una celebridad científica relativamente nueva y conocida internacionalmente por su nombre en inglés como “clumped isotopes”. El descubrimiento de la propiedad que tienen algunos átomos pesados de unirse entre ellos con mayor o menor frecuencia, dependiendo de la temperatura, y la posibilidad de medir esta propiedad, abren grandes perspectivas de avances en numerosos campos científicos. Palabras clave: isótopos estables, minerales biológicos, carbonatos de calcio, termómetro marino.</p>", "Keywords": "", "DOI": "10.22201/cuaieed.16076079e.2021.22.2.1", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad de Stirling"}], "References": []}, {"ArticleId": 87239893, "Title": "¿Cómo prevenir la caries dental?", "Abstract": "<p>Este podcast tiene como objetivo brindar información importante y puntual acerca de qué son las caries, su origen y cómo se pueden prevenir. Palabras clave: caries, salud dental, podcast, divulgación de la ciencia.</p>", "Keywords": "", "DOI": "10.22201/cuaieed.16076079e.2021.22.2.12", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ENES León"}], "References": []}, {"ArticleId": 87239900, "Title": "Cuevas volcánicas: entre la realidad y la ficción", "Abstract": "<p>El estudio de las cuevas volcánicas ha despertado el interés de los científicos de todo el mundo, debido a sus microorganismos únicos y a la posibilidad de su uso como análogos marcianos. Sin embargo, en México existen pocos estudios al respecto, pero una enorme cantidad de cuevas volcánicas por explorar y estudiar. El presente trabajo narra el proceso de investigación y los resultados obtenidos por un grupo de investigadores del Instituto de Geología de la Universidad Nacional Autónoma de México (UNAM) en el estudio del tubo de lava de Chimalacatepec, en México.</p>", "Keywords": "", "DOI": "10.22201/cuaieed.16076079e.2021.22.2.3", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto de Geología"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Geología"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Instituto de Geología"}], "References": []}, {"ArticleId": 87239914, "Title": "Mostrar y comunicar, misión de la Revista Digital Universitaria", "Abstract": "", "Keywords": "", "DOI": "10.22201/cuaieed.16076079e.2021.22.2.0", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON> del Pilar <PERSON>", "Affiliation": "Coordinación de Universidad Abierta, Innovación Educativa y Educación a Distancia (CUAIEED)"}], "References": []}, {"ArticleId": 87239974, "Title": "Disfunciones cognitivas en adultos mayores con depresión", "Abstract": "<p>La depresión es uno de los trastornos mentales con mayor prevalencia a nivel mundial. Quien la padece ve afectada su funcionalidad diaria debido a la sintomatología negativa, aislamiento social y disminución de la actividad psicomotora. Si se presenta durante el envejecimiento, puede incapacitar a la persona cuando, además de los síntomas depresivos, existen alteraciones cognitivas significativas. El tratamiento usual para los trastornos depresivos son los Inhibidores Selectivos de la Recaptación de Serotonina (ISRSs), no obstante, en los adultos mayores que presentan disfunción ejecutiva, dicho tratamiento puede tener menor efectividad, por lo que se deben analizar otros fármacos y métodos psicoterapéuticos que mejoren la calidad de vida de las personas. Por lo anterior, el objetivo de este artículo fue proporcionar una descripción de las disfunciones cognitivas presentes en adultos mayores con depresión, así como brindar algunas pautas para su tratamiento.</p>", "Keywords": "", "DOI": "10.22201/cuaieed.16076079e.2021.22.2.2", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Veracruzana"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Veracruzana"}], "References": []}, {"ArticleId": 87239975, "Title": "Transición de la educación media superior a la educación superior: estudio en la UNAM", "Abstract": "<p>La Universidad Nacional Autónoma de México (UNAM) posee una matrícula diversa y basta. En el contexto de una institución de Educación Media Superior (EMS) y Educación Superior (EMS), el seguimiento de las trayectorias escolares de sus estudiantes, al considerar la transición entre estos niveles educativos, es una valiosa herramienta que permite identificar de manera precisa y adecuada, las necesidades de acompañamiento que se requieren a fin de abatir el abandono y el rezago escolar. En este estudio se explora el desempeño escolar y la eficiencia terminal de los estudiantes de la generación 2009 del bachillerato de la UNAM (ENP y CCH), durante su paso por dicho nivel educativo, así como su interrelación con el desempeño escolar y la regularidad durante el primer año de la licenciatura. De un total de 34 070 estudiantes, el análisis de sus trayectorias escolares durante la EMS presentó abandono y rezago considerables, además de una baja eficiencia terminal (56.6%). Asimismo, se observa que uno de cada cuatro estudiantes que ingresa al bachillerato de la UNAM y lo concluye, es regular al término del primer año de la es. Derivado de estos resultados se hace evidente la importancia de seguir realizando este tipo de estudios, a fin de identificar de manera precisa las necesidades existentes dentro de estos niveles educativos en la UNAM, así como la inminente necesidad de desarrollar e impulsar estrategias institucionales que permitan amortiguar el impacto que genera la transición de la EMS a la es en los estudiantes. Palabras clave: trayectoria escolar, desempeño académico, rezago escolar, eficiencia terminal, abandono escolar, educación media superior, educación superior.</p>", "Keywords": "", "DOI": "10.22201/cuaieed.16076079e.2021.22.2.10", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CUAIEED"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CUAIEED"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CUAIEED"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CUAIEED"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CUAIEED"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CUAIEED"}], "References": []}, {"ArticleId": 87240137, "Title": "Mirror motion recognition method about upper limb rehabilitation robot based on sEMG", "Abstract": "<p>A novel method of mirror motion recognition by rehabilitation robot with multi-channels sEMG signals is proposed, aiming to help the stroked patients to complete rehabilitation training movement. Firstly the bilateral mirror training is used and the model of muscle synergy with basic sEMG signals is established. Secondly, the constrained L1/2-NMF is used to extracted the main sEMG signals information which can also reduce the limb movement characteristics. Finally the relationship between sEMG signal characteristics and upper limb movement is described by TSSVD-ELM and it is applied to improve the model stability. The validity and feasibility of the proposed strategy are verified by the experiments in this paper, and the rehabilitation robot can move with the mirror upper limb. By comparing the method proposed in this paper with PCA and full-action feature extraction, it is confirmed that convergence speed is faster; the feature extraction accuracy is higher which can be used in rehabilitation robot systems.</p>", "Keywords": "", "DOI": "10.3233/JCM-204812", "PubYear": 2021, "Volume": "21", "Issue": "4", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87240164, "Title": "The depth utilization method of condensate system’s energy-storage to improve large turbine generator units’ load response characteristic", "Abstract": "<p>Load response characteristics of high thermal power units are critical to a safe and efficient grid-connected utilization of large-scale renewable energy with strong randomness. A condensate throttling regulation was reported to improve the units variable load rate. However, it is not verified by tests in different types of units. In the paper, the influence of the storage capacity of the steam generator’s condensate system on the load response characteristics is studied for four subcritical 330 MW heating units by simulation and an experimental test. The result shows the feasibility of the condensate throttling regulation, which is of great value to the practical engineering application in the future. In addition, this paper obtains the condensate regulation potential of units under different power generation load conditions through simulation and actual tests. These real data will help other units of the same type to carry out similar modifications or tests.</p>", "Keywords": "", "DOI": "10.3233/JCM-204843", "PubYear": 2021, "Volume": "21", "Issue": "5", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Harbin, Heilongjiang 150001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Harbin, Heilongjiang 150001, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "North Unites the Electric Power Dalate Power Plant, Inner Mongolian Dalate 014300, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The State Key Laboratory of Alternate Electrical Power System with Renewable Energy Sources, North China Electric Power University, Beijing 102206, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, Harbin, Heilongjiang 150001, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Zheneng technology institute Co., Ltd, Hangzhou, Zhejiang 310052, China"}, {"AuthorId": 7, "Name": "Xiaozhong Tong", "Affiliation": "Zhejiang Zheneng technology institute Co., Ltd, Hangzhou, Zhejiang 310052, China"}, {"AuthorId": 8, "Name": "Ganghua Qin", "Affiliation": "Zhejiang Zheneng technology institute Co., Ltd, Hangzhou, Zhejiang 310052, China"}], "References": []}, {"ArticleId": 87240300, "Title": "Design and voice‐based control of a nasal endoscopic surgical robot", "Abstract": "In traditional nasal surgery, surgeons are prone to fatigue and jitter by holding the endoscope for a long‐time. Some complex operations require assistant surgeon to assist with holding the endoscope. To address the above problems, the authors design a remote centre of motion based nasal robot, and propose a voice‐based robot control method. First, through the operation space analysis of nasal surgery, the design scheme of the robot based on RCM mechanism is proposed. On this basis, the design parameters of the robot are analysed to complete the entire design of robot. Then, considering that the surgeon's hands are occupied by surgical instruments during complex surgical operations, a voice‐based robot control method is proposed. This method obtains direction instructions from surgeons by analysing the movement of the endoscopic image. Afterward, a commercial speech recognition interface is used to realise the offline grammar controlwords lib compatible with both Chinese and English, and the overall strategy of robot control is proposed. Finally, an experimental platform for virtual robot control is established, and the voice‐based robot control experiment is performed. The results show that the proposed voice‐based control method is feasible, and it provides guidance for the subsequent development and control of the actual robot system.", "Keywords": "", "DOI": "10.1049/cit2.12022", "PubYear": 2021, "Volume": "6", "Issue": "1", "JournalId": 26444, "JournalTitle": "CAAI Transactions on Intelligence Technology", "ISSN": "2468-6557", "EISSN": "2468-2322", "Authors": [{"AuthorId": 1, "Name": "Yucheng He", "Affiliation": "Department of Mechanical and Automation Engineering, Chinese University of Hong Kong, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, University of Hamburg, Hamburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, University of Hamburg, Hamburg, Germany"}], "References": []}, {"ArticleId": 87240523, "Title": "Naive Bayes and deep learning model for wireless intrusion detection systems", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJESMS.2021.10036481", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 33706, "JournalTitle": "International Journal of Engineering Systems Modelling and Simulation", "ISSN": "1755-9758", "EISSN": "1755-9766", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87240527, "Title": "Image enhancement based on skin-colour segmentation and smoothness", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCVR.2021.10036485", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 15056, "JournalTitle": "International Journal of Computational Vision and Robotics", "ISSN": "1752-9131", "EISSN": "1752-914X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Chen", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87240559, "Title": "Measurement and Evaluation of the Stiffness Characteristics of Precision Spring Tubes in Hydraulic Servovalves", "Abstract": "<p>The spring tube is the core component of the hydraulic servo valve, and its stiffness characteristics determine the sensitivity of the servo valve. For the difficulty in measuring and ensuring the stiffness of spring tube in complex structures, based on the principle of structural characteristics and stiffness measurement, an effective method for measuring and evaluating the stiffness of spring tube was proposed. Firstly, by the force analysis of the spring tube in the valve structure, using improved stiffness measurement theory, an equivalent measurement model of single-arm is established. Secondly, the stiffness measurement system of the spring tube is constructed based on this model. Furthermore, using the deformation and the spatial position recurrence method, the accuracy of the measurement system is further improved. Thirdly, using the orthogonal test method and linear optimization method of the Neural network model, the stiffness characteristics of the spring tube under the influence of different factors are studied further. Finally, the validity of the models is verified by using the software COMSOL and the experimental platform. The stability of the effective stiffness for the spring tube is further analyzed by the measurement data. The contribution and novelty of this paper are that based on the force analysis of the spring tube in the servo valve internal structure, an effective and systematic stiffness measurement and evaluation method are proposed. On this basis, experiments and stiffness characteristics analysis are carried out. Furthermore, several structural factors affecting the stiffness characteristics of spring tube are considered, and the stiffness characteristics of spring tube are systematically studied and analyzed. Based on this research and analysis, the systematic study of measurement and characteristics of precision components is very important for practical complex systems in this field. This makes it possible to further study the measurement of precision components which are difficult to measure in the actual structure. It is instructive to study the characteristics of precision components in complex structures.</p>", "Keywords": "", "DOI": "10.1115/1.4050573", "PubYear": 2021, "Volume": "143", "Issue": "9", "JournalId": 9493, "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control", "ISSN": "0022-0434", "EISSN": "1528-9028", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University School of automation, Xi'an City, 710072, Shanxi Province"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University School of automation, Xi'an City, 710072, Shanxi Province"}], "References": [{"Title": "A Review of Switched Inertance Hydraulic Converter Technology1", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "5", "Page": "", "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control"}]}, {"ArticleId": 87240671, "Title": "Convolutional neural network with joint stepwise character/word modeling based system for scene text recognition", "Abstract": "<p>Text recognition in the wild is a challenging task in the field of computer vision and machine learning. Existing optical character recognition engines cannot perform well in the natural scene. In this context, deep learning models have emerged as a powerful state-of-the-art technique in the classification and recognition process. This study proposes a new Convolutional Neural Network based system for scene text reading. We investigate how to combine the character recognition module followed by the word recognition module to achieve the overall system goal. The first module analyzes characters within multi-scale images by relaying on the power of the convolutional network and the fully connected network for character recognition. The second module relies on the Viterbi search to find the closest word to a given characters sequence. For the sake of more precision, a bigram based linguistic module is applied. The proposed system achieves the state-of-the-art performance on three standard scene text recognition benchmarks: chars74k, ICDAR 2003 and ICDAR 2013. In particular, this performance is proven on both of character and word recognition accuracy as well as speed aspects via a comparative study between different deep learning architectures.</p>", "Keywords": "Scene text; Text recognition; Deep learning; Linguistic verification", "DOI": "10.1007/s11042-021-10663-z", "PubYear": 2022, "Volume": "81", "Issue": "3", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "REGIM-Lab, ENIS, University of Sfax, Sfax, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "REGIM-Lab, ENIS, University of Sfax, Sfax, Tunisia"}, {"AuthorId": 3, "Name": "Fadoua <PERSON>", "Affiliation": "REGIM-Lab, ENIS, University of Sfax, Sfax, Tunisia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Team on Intelligent Machines, ENIG, University of Gabes, Gabes, Tunisia"}], "References": [{"Title": "MA-CRNN: a multi-scale attention CRNN for Chinese text line recognition in natural scenes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "2", "Page": "103", "JournalTitle": "International Journal on Document Analysis and Recognition"}, {"Title": "Adaptive embedding gate for attention-based scene text recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "381", "Issue": "", "Page": "261", "JournalTitle": "Neurocomputing"}, {"Title": "Arabic handwriting recognition system using convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; Isra Al-Turai<PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "2249", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 87240799, "Title": "MOSQUITO‐NET : A deep learning based CADx system for malaria diagnosis along with model interpretation using GradCam and class activation maps", "Abstract": "<p>Malaria is considered one of the deadliest diseases in today's world, which causes thousands of deaths per year. The parasites responsible for malaria are scientifically known as Plasmodium, which infects the red blood cells in human beings. Diagnosis of malaria requires identification and manual counting of parasitized cells in microscopic blood smears by medical practitioners. Its diagnostic accuracy is primarily affected by extensive scale screening due to the unavailability of resources. State of the art Computer-Aided Diagnostic techniques based on deep learning algorithms such as CNNs, which perform an end to end feature extraction and classification, have widely contributed to various image recognition tasks. In this paper, we evaluate the performance of Mosquito-Net, a custom made convnet to classify the infected and uninfected blood smears for malaria diagnosis. The CADx system can be deployed on IoT and mobile devices due to its fewer parameters and computation power, making it wildly preferable for diagnosis in remote and rural areas that lack medical facilities. Statistical analysis demonstrates that the proposed model achieves greater accuracy than the previous SOTA architectures for malaria diagnosis despite being 10 times lighter in parameters and inference time. Mosquito-Net achieves an AUC of 99.009% and an F-1 score of 96.7% on the validation set.</p>", "Keywords": "AUC;CNNs;computer-aided diagnostic techniques;F-1 score;malaria;Mosquito-Net", "DOI": "10.1111/exsy.12695", "PubYear": 2022, "Volume": "39", "Issue": "7", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering Kalinga Institute of Industrial Technology Deemed to be University  Bhubaneswar India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> B<PERSON>", "Affiliation": "School of Computer Engineering Kalinga Institute of Industrial Technology Deemed to be University  Bhubaneswar India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering Kalinga Institute of Industrial Technology Deemed to be University  Bhubaneswar India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering Kalinga Institute of Industrial Technology Deemed to be University  Bhubaneswar India"}], "References": []}, {"ArticleId": 87240815, "Title": "Attention‐based novel neural network for mixed frequency data", "Abstract": "<p>It is a common fact that data (features, characteristics or variables) are collected at different sampling frequencies in some fields such as economic and industry. The existing methods usually either ignore the difference from the different sampling frequencies or hardly take notice of the inherent temporal characteristics in mixed frequency data. The authors propose an innovative dual attention-based neural network for mixed frequency data (MID-DualAtt), in order to utilize the inherent temporal characteristics and select the input characteristics reasonably without losing information. According to the authors’ knowledge, this is the first study to use the attention mechanism to process mixed frequency data. The MID-DualAtt model uses the frequency alignment method to transform the high--frequency variables into observation vectors at low frequency, and more critical input characteristics are selected for the current prediction index by attention mechanism. The temporal characteristics are explored by the encoder-decoder with attention based on long- short-term memory networks (LSTM). The proposed MID-DualAtt has been tested in practical application, and the experimental results show that it has better prediction ability than the compared models.</p>", "Keywords": "", "DOI": "10.1049/cit2.12013", "PubYear": 2021, "Volume": "6", "Issue": "3", "JournalId": 26444, "JournalTitle": "CAAI Transactions on Intelligence Technology", "ISSN": "2468-6557", "EISSN": "2468-2322", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> Li", "Affiliation": "Chongqing key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 2, "Name": "Hong Yu", "Affiliation": "Chongqing key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Central South University, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Metallurgy and Environment, Central South University, Changsha, China"}], "References": []}, {"ArticleId": 87240897, "Title": "Stabilizing Radial Basis Function Methods for Conservation Laws Using Weakly Enforced Boundary Conditions", "Abstract": "<p>It is well understood that boundary conditions (BCs) may cause global radial basis function (RBF) methods to become unstable for hyperbolic conservation laws (CLs). Here we investigate this phenomenon and identify the strong enforcement of BCs as the mechanism triggering such stability issues. Based on this observation we propose a technique to weakly enforce BCs in RBF methods. In the case of hyperbolic CLs, this is achieved by carefully building RBF methods from the weak form of the CL, rather than the typically enforced strong form. Furthermore, we demonstrate that global RBF methods may violate conservation, yielding physically unreasonable solutions when the approximation does not take into account these considerations. Numerical experiments validate our theoretical results.</p>", "Keywords": "Hyperbolic conservation laws; Radial basis functions; Conservation; (Energy) stability; Spectral methods; Method of lines; 35L65; 41A05; 41A30; 65D05; 65M12", "DOI": "10.1007/s10915-021-01453-8", "PubYear": 2021, "Volume": "87", "Issue": "2", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Dartmouth College, Hanover, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Dartmouth College, Hanover, USA"}], "References": [{"Title": "Two-Dimensional RBF-ENO Method on Unstructured Grids", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "82", "Issue": "3", "Page": "76", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "Analysis of the SBP-SAT Stabilization for Finite Element Methods Part I: Linear Problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "85", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}]}, {"ArticleId": 87240966, "Title": "A novel feature selection approach with Pareto optimality for multi-label data", "Abstract": "<p>Multi-label learning has widely applied in machine learning and data mining. The purpose of feature selection is to select an approximately optimal feature subset to characterize the original feature space. Similar to single-label data, feature selection is an import preprocessing step to enhance the performance of multi-label classification model. In this paper, we propose a multi-label feature selection approach with Pareto optimality for continuous data, called MLFSPO. It maps multi-label features to high-dimensional space to evaluate the correlation between features and labels by utilizing the Hilbert-Schmidt Independence Criterion (HSIC). Then, the feature subset obtains by combining the Pareto optimization with feature ordering criteria and label weighting. Eventually, extensive experimental results on publicly available data sets show the effectiveness of the proposed algorithm in multi-label tasks.</p>", "Keywords": "Feature selection; Multi-label learning; Pareto optimality; Hilbert-Schmidt independence criterion", "DOI": "10.1007/s10489-021-02228-2", "PubYear": 2021, "Volume": "51", "Issue": "11", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Lab of Petroleum Data Mining, China University of Petroleum-Beijing, Beijing, China;College of Information Science and Engineering, China University of Petroleum-Beijing, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Beijing Key Lab of Petroleum Data Mining, China University of Petroleum-Beijing, Beijing, China;College of Information Science and Engineering, China University of Petroleum-Beijing, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Lab of Petroleum Data Mining, China University of Petroleum-Beijing, Beijing, China;College of Information Science and Engineering, China University of Petroleum-Beijing, Beijing, China;School of Computer Science, Minnan Normal University, ZhangZhou, China;Key Laboratory of Data Science and Intelligence Application, Fujian Province University, Zhangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Beijing Key Lab of Petroleum Data Mining, China University of Petroleum-Beijing, Beijing, China;College of Information Science and Engineering, China University of Petroleum-Beijing, Beijing, China"}, {"AuthorId": 5, "Name": "Yunfeng Hong", "Affiliation": "@qq.com;China Anti-Infringement and Anti-Counterfeit Innovation Strategic Alliance, Hangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "@qq.com;Xiamen Hanying Internet of things Application Research Institute, Xiamen, China"}], "References": [{"Title": "A comprehensive survey of the Grasshopper optimization algorithm: results, variants, and applications", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "19", "Page": "15533", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 87241204, "Title": "A Multi-Disciplinary Perspective for Conducting Artificial Intelligence-enabled Privacy Analytics", "Abstract": "<p>Events such as Facebook-Cambridge Analytica scandal and data aggregation efforts by technology providers have illustrated how fragile modern society is to privacy violations. Internationally recognized entities such as the National Science Foundation (NSF) have indicated that Artificial Intelligence (AI)-enabled models, artifacts, and systems can efficiently and effectively sift through large quantities of data from legal documents, social media, Dark Web sites, and other sources to curb privacy violations. Yet considerable efforts are still required for understanding prevailing data sources, systematically developing AI-enabled privacy analytics to tackle emerging challenges, and deploying systems to address critical privacy needs. To this end, we provide an overview of prevailing data sources that can support AI-enabled privacy analytics; a multi-disciplinary research framework that connects data, algorithms, and systems to tackle emerging AI-enabled privacy analytics challenges such as entity resolution, privacy assistance systems, privacy risk modeling, and more; a summary of selected funding sources to support high-impact privacy analytics research; and an overview of prevailing conference and journal venues that can be leveraged to share and archive privacy analytics research. We conclude this paper with an introduction of the papers included in this special issue.</p>", "Keywords": "", "DOI": "10.1145/3447507", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 24450, "JournalTitle": "ACM Transactions on Management Information Systems", "ISSN": "2158-656X", "EISSN": "2158-6578", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Operations and Decision Technologies, Indiana University, Bloomington, Indiana, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> School of Engineering and Computer Science, University of Texas at Dallas, Richardson, TX, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Management Information Systems, University of Arizona, Tucson, AZ, USA"}], "References": [{"Title": "Proactively Identifying Emerging Hacker Threats from the Dark Web", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Privacy and Security"}, {"Title": "Trailblazing the Artificial Intelligence for Cybersecurity Discipline", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Management Information Systems"}]}, {"ArticleId": 87241305, "Title": "Balancing between Right to Be Forgotten and Right to Freedom of Expression in Spent Criminal Convictions", "Abstract": "<p>Being two distinct fundamental rights, the coexisting state of the right to be forgotten and freedom of expression has already been confirmed by the competent authorities through balancing in situations when they collide. The paper focuses the balancing apprehensions concerning spent criminal conviction data while considering Google Spain ruling and the General Data Protection Regulation (GDPR) primarily for analysis. From the Google Spain ruling till the development of the GDPR, the balancing apprehension has already seen another generation resolving conflicting issues derived both from statutes and case laws. Though lawful authorities stepped into easing the tension between different elements of the two rights, it has been seen that the outcome of balancing intellection depends on the application of diverse norms and principles. The contemporary principles in balancing the rights of spent criminal conviction datum have been identified in this paper which needs to be enhanced carefully in the future towards a more privacy-friendly atmosphere to envisage the need of data-driven Europe and to upheld the right to be forgotten of spent criminal convicts.</p>", "Keywords": "balancing;Google Spain;principles;privacy;processing;solicitation;spent convictions", "DOI": "10.1002/spy2.157", "PubYear": 2021, "Volume": "4", "Issue": "4", "JournalId": 7086, "JournalTitle": "Security and Privacy", "ISSN": "2475-6725", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Law, University of Turku, Turku, Finland"}], "References": []}, {"ArticleId": 87241337, "Title": "Tensor product‐based model transformation approach to tower crane systems modeling", "Abstract": "<p>This paper presents the application of the tensor product (TP)-based model transformation approach to produce Tower CRrane (TCR) systems models. The modeling approach starts with a nonlinear model of TCR systems as representative multi-input–multi-output controlled processes. A linear parameter-varying model is next derived, and the modeling steps specific to TP–based model transformation are proceeded to obtain the TP model. The TP model is tested on TCR laboratory equipment in two open-loop scenarios considering chirp signals and pseudorandom binary step signals applied to the three model inputs (control inputs). The nonlinear and TP model outputs in the two scenarios are the payload position, the cart position, and the arm angular position. The nonlinear and TP model outputs are collected, measured, and compared. The simulation results prove that the derived TP model approximately mimics the behavior of the nonlinear model; both system responses and numerical approximation errors are illustrated.</p>", "Keywords": "arm angular position;cart position;payload position;tensor product-based model transformation;tower crane systems", "DOI": "10.1002/asjc.2494", "PubYear": 2021, "Volume": "23", "Issue": "3", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation and Applied Informatics, Politehnica University of Timisoara, Timisoara, Romania"}, {"AuthorId": 2, "Name": "Radu‐Emil <PERSON>", "Affiliation": "Department of Automation and Applied Informatics, Politehnica University of Timisoara, Timisoara, Romania; School of Engineering, Edith Cowan University, Joondalup, West Australia, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation and Applied Informatics, Politehnica University of Timisoara, Timisoara, Romania"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, University of Ottawa, Ottawa, Ontario, Canada"}], "References": [{"Title": "An Operator-based Nonlinear Vibration Control System Using a Flexible Arm with Shape Memory Alloy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "1", "Page": "139", "JournalTitle": "International Journal of Automation and Computing"}, {"Title": "New relaxed stabilization conditions for discrete‐time Takagi–Sugeno fuzzy control systems", "Authors": "Lei Kong; Jingqi Yuan", "PubYear": 2020, "Volume": "22", "Issue": "4", "Page": "1604", "JournalTitle": "Asian Journal of Control"}, {"Title": "Augmented TP model transformation‐based parallel distributed compensation control design", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "1", "Page": "315", "JournalTitle": "Asian Journal of Control"}, {"Title": "Community detection in networks using bio-inspired optimization: Latest developments, new results and perspectives with a selection of recent meta-heuristics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "106010", "JournalTitle": "Applied Soft Computing"}, {"Title": "Using a Self‐Clustering Algorithm and Type‐2 Fuzzy Controller for Multi‐robot Deployment and Navigation in Dynamic Environments", "Authors": "J<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "6", "Page": "2143", "JournalTitle": "Asian Journal of Control"}]}, {"ArticleId": 87241393, "Title": "A novel technique for detecting repacked android applications using constant key point selection-based hashing and limited binary pattern texture feature extraction", "Abstract": "", "Keywords": "", "DOI": "10.1007/s12652-021-03018-x", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87241453, "Title": "A band selection approach based on wavelet support vector machine ensemble model and membrane whale optimization algorithm for hyperspectral image", "Abstract": "<p>Hyperspectral Image (HSI) has become one of the important remote sensing sources for object interpretation by its abundant band information. Among them, band selection is considered as the main theme in HSI classification to reduce the data dimension, and it is a combinatorial optimization problem and difficult to be completely solved by previous techniques. Whale Optimization Algorithm (WOA) is a newly proposed swarm intelligence algorithm that imitates the predatory strategy of humpback whales, and membrane computing is able to decompose the band information into a series of elementary membranes that decreases the coding length. In addition, Support Vector Machine (SVM) combined with wavelet kernel is adapted to HSI datasets with high dimension and small samples, ensemble learning is an effective tool that synthesizes multiple sub-classifiers to solve the same problem and obtains accurate category label for each sample. In the paper, a band selection approach based on wavelet SVM (WSVM) ensemble model and membrane WOA (MWOA) is proposed, experimental results indicate that the proposed HSI classification technique is superior to other corresponding and newly proposed methods, achieves the optimal band subset with a fast convergence speed, and the overall classification accuracy has reached 93% for HSIs.</p>", "Keywords": "Hyperspectral image; Band selection; Whale optimization algorithm; Membrane computing; Classifier ensemble; Wavelet support vector machine", "DOI": "10.1007/s10489-021-02270-0", "PubYear": 2021, "Volume": "51", "Issue": "11", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Geological Survey, China University of Geosciences, Wuhan, People’s Republic of China;Key Laboratory for National Geographic Census and Monitoring, National Administration of Surveying, Mapping and Geoinformation, Wuhan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Geological Survey, China University of Geosciences, Wuhan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hubei Cancer Hospital, Tongji Medical College, Huazhong University of Science and Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Hubei University of Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON> He", "Affiliation": "College of Surveying and Geo-Informatics, North China University of Water Resources and Electric Power, Zhengzhou, People’s Republic of China"}], "References": [{"Title": "Class-imbalanced dynamic financial distress prediction based on Adaboost-SVM ensemble combined with SMOTE and time weighting", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "128", "JournalTitle": "Information Fusion"}, {"Title": "An overview on spectral and spatial information fusion for hyperspectral image classification: Current trends and challenges", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "59", "JournalTitle": "Information Fusion"}, {"Title": "Hyperspectral anomaly detection by local joint subspace process and support vector machine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "10", "Page": "3798", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Deep support vector machine for hyperspectral image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "107298", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 87241454, "Title": "Privacy and data balkanization: circumventing the barriers", "Abstract": "<p>The rapid growth in digital data forms the basis for a wide range of new services and research, e.g., large-scale medical studies. At the same time, increasingly restrictive privacy concerns and laws are leading to significant overhead in arranging for sharing or combining different data sets to obtain these benefits. For new applications, where the benefit of combined data is not yet clear, this overhead can inhibit organizations from even trying to determine whether they can mutually benefit from sharing their data. In this paper, we discuss techniques to overcome this difficulty by employing private information transfer to determine whether there is a benefit from sharing data and whether there is room to negotiate acceptable prices. These techniques involve cryptographic protocols. While currently considered secure, these protocols are potentially vulnerable to the development of quantum technology, particularly for ensuring privacy over significant periods of time into the future. To mitigate this concern, we describe how developments in practical quantum technology can improve the security of these protocols.</p>", "Keywords": "Data privacy; Negotiation; Quantum security", "DOI": "10.1007/s43681-021-00042-7", "PubYear": 2021, "Volume": "1", "Issue": "3", "JournalId": 79924, "JournalTitle": "AI and Ethics", "ISSN": "2730-5953", "EISSN": "2730-5961", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CableLabs, Louisville, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Molecular Manufacturing, Palo Alto, USA"}], "References": []}, {"ArticleId": 87241463, "Title": "Contrast enhancement of MRI images using morphological transforms and PSO", "Abstract": "<p>Medical imaging plays a crucial role in correct extraction of the significant information for monitoring the patient’s health and providing the quality treatment. A deluge of medical images requires initial interpretation for the presence of any abnormality, however, the correct diagnosis requires the images to be of good quality. To cope with the problem of poor contrast in medical images, this paper presents a method based on morphological transforms to improve the quality of the images. The proposed method incorporates Particle Swarm Optimization to find an optimum value of a parameter which controls the enhancement of the resulting image. The proposed algorithm is executed on a set of MRI images for testing its efficacy. The experimental results are compared in terms of both qualitative and quantitative parameters. The mean opinion score is obtained with the help of experts, which clearly shows the better performance of the proposed method. Furthermore, the parameters like Contrast Improvement Ratio, signal-to-noise ratio, peak signal-to-noise ratio, PL, and Structural Similarity Index are evident of better performance of proposed method when compared with the state-of-the-art methods and few recent methods. The comparison shows that the performance of the proposed method based on morphological transforms incorporating Particle Swarm Optimization is better not only visually but also in terms of other evaluation parameters.</p>", "Keywords": "Morphological transforms; Particle swarm optimization; CIR; PSNR; PL; SSIM", "DOI": "10.1007/s11042-021-10743-0", "PubYear": 2021, "Volume": "80", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jaypee Institute of Information Technology, Noida, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jaypee Institute of Information Technology, Noida, India"}], "References": [{"Title": "Enhancement of MRI images of brain tumor using Gr$\\ddot {u}$nwald Letnikov fractional differential mask", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "35-36", "Page": "25379", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 87241473, "Title": "Combined use of coral reefs optimization and reinforcement learning for improving resource utilization and load balancing in cloud environments", "Abstract": "<p>Resource management is the process of task scheduling and resource provisioning to provide requirements of cloud users. Since cloud resources are often heterogeneous, task scheduling and resource provisioning are major challenges in this area. Various methods have been introduced to improve resource utilization and thus increase the efficiency of cloud computing. Existing methods can be divided into several categories, including mathematical and statistical methods, heuristic- and meta-heuristic-based methods, and machine-learning-based methods. Since the resource management problem is NP-complete, several optimization methods have been also exploited in this area. Coral reefs algorithm is an evolutionary method that has showed appropriate convergence and response time for some problems, and thus is used in this paper to combine with reinforcement learning to improve efficiency of resource management in cloud environments. The proposed method of this paper consists of two phases. The initial allocation of resources to ready-to-perform tasks is done using the coral reefs algorithm in the first phase. The tasks are considered as corals and the resources are considered reefs in this method. The second phase utilizes reinforcement learning to avoid falling into the local optima and to make optimal use of resources using a long-term approach. The proposed model of this paper, called MO-CRAML, introduces a new hybrid algorithm for improving utilization and load balancing of cloud resources using the combination of coral reefs optimization algorithm and reinforcement learning. The results of the experiments show that the proposed algorithm has better performance in cloud resource utilization and load balancing in comparison with some other important methods of the literature.</p>", "Keywords": "Cloud computing; Resource utilization; Machine learning; Coral reefs algorithm; Load balancing; 60J20; 68T05; 68W50; 68Q85", "DOI": "10.1007/s00607-021-00920-2", "PubYear": 2021, "Volume": "103", "Issue": "7", "JournalId": 10374, "JournalTitle": "Computing", "ISSN": "0010-485X", "EISSN": "1436-5057", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Semnan Branch, Islamic Azad University, Semnan, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Semnan Branch, Islamic Azad University, Semnan, Iran"}], "References": [{"Title": "A Survey on the Combined Use of Optimization Methods and Game Theory", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "1", "Page": "59", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "QL-HEFT: a novel machine learning scheduling scheme base on cloud computing environment", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "10", "Page": "5553", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Hybridization of firefly and Improved Multi-Objective Particle Swarm Optimization algorithm for energy efficient load balancing in Cloud Computing environments", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "36", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Online scheduling of dependent tasks of cloud’s workflows to enhance resource utilization and reduce the makespan using multiple reinforcement learning-based agents", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "21", "Page": "16177", "JournalTitle": "Soft Computing"}, {"Title": "Trust based multi-agent cooperative load balancing system (TCLBS)", "Authors": "U.<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "185", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A cloud resource management framework for multiple online scientific workflows using cooperative reinforcement learning agents", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "179", "Issue": "", "Page": "107340", "JournalTitle": "Computer Networks"}, {"Title": "Task scheduling, resource provisioning, and load balancing on scientific workflows using parallel SARSA reinforcement learning agents and genetic algorithm", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "3", "Page": "2800", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 87241536, "Title": "NLE volume 27 issue 2 Cover and Back matter", "Abstract": "", "Keywords": "", "DOI": "10.1017/S1351324921000048", "PubYear": 2021, "Volume": "27", "Issue": "2", "JournalId": 23332, "JournalTitle": "Natural Language Engineering", "ISSN": "1351-3249", "EISSN": "1469-8110", "Authors": [], "References": []}, {"ArticleId": 87241606, "Title": "Mining Sports Articles using Cuckoo Search and Tabu Search with SMOTE Preprocessing Technique", "Abstract": "Sentiment analysis is one of the most popular domains for natural language text classification, crucial for improving information extraction. However, massive data availability is one of the biggest problems for opinion mining due to accuracy considerations. Selecting high discriminative features from an opinion mining database is still an ongoing research topic. This study presents a two-stage heuristic feature selection method to classify sports articles using Tabu search and Cuckoo search via <PERSON><PERSON><PERSON> flight. Lévy flight is used to prevent the solution from being trapped at local optima. Comparative results on a benchmark dataset prove that our method shows significant improvements in the overall accuracy from 82.6% up to 89.5%.", "Keywords": "Sentiment Analysis;Subjectivity Analysis;Feature Reduction;Tabu Search;Cuckoo Search;Random Forest Classifier", "DOI": "10.3844/jcssp.2021.231.241", "PubYear": 2021, "Volume": "17", "Issue": "3", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Public Authority for Applied Education and Training (PAAET)"}], "References": []}, {"ArticleId": 87241617, "Title": "One-step multi-view spectral clustering by learning common and specific nonnegative embeddings", "Abstract": "<p>Multi-view spectral clustering is a hot research area which has attracted increasing attention. Most existing multi-view spectral clustering methods utilize a two-step strategy. The first step obtains a common embedding by fusing spectral embeddings of different views, and the second step conducts hard clustering, such as K-means or spectral rotation, on the common embedding. Because the goal of the first step is not obtaining optimal clustering result, and the requirement to post-processing makes the final clustering result uncertain. In this paper, we propose a novel one-step multi-view spectral clustering method, in which the spectral embedding and nonnegative embedding are unified into one framework. Therefore, our method can avoid the uncertainty brought by post-processing and obtain optimal clustering result. Moreover, the nonnegative embedding is divided into two parts. The common nonnegative embedding indicates the shared cluster structure, and the specific nonnegative embedding indicates the exclusive cluster structure of each view. Hence, our method can well tackle with noises and outliers of different views. Furthermore, an alternating iterative algorithm is used to solve the joint optimization problem. Extensive experimental results on four real-world datasets have demonstrated the effectiveness of the proposed method.</p>", "Keywords": "Multi-view clustering; Spectral clustering; Common nonnegative embedding; Specific nonnegative embedding", "DOI": "10.1007/s13042-021-01297-6", "PubYear": 2021, "Volume": "12", "Issue": "7", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "Hong<PERSON> Yin", "Affiliation": "@zjhu.edu.cn;School of Information Engineering, Huzhou University, Huzhou, China;Zhejiang Province Key Laboratory of Smart Management and Application of Modern Agricultural Resources, Huzhou University, Huzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Huzhou University, Huzhou, China;Zhejiang Province Key Laboratory of Smart Management and Application of Modern Agricultural Resources, Huzhou University, Huzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "College of Computer Science and Technology, Soochow University, Suzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Huzhou University, Huzhou, China;Zhejiang Province Key Laboratory of Smart Management and Application of Modern Agricultural Resources, Huzhou University, Huzhou, China"}], "References": [{"Title": "Multi-view spectral clustering via integrating nonnegative embedding and spectral embedding", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "251", "JournalTitle": "Information Fusion"}, {"Title": "Auto-weighted multi-view co-clustering with bipartite graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "18", "JournalTitle": "Information Sciences"}, {"Title": "Multi-view spectral clustering via sparse graph learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "384", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "A novel dataset-specific feature extractor for zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "391", "Issue": "", "Page": "74", "JournalTitle": "Neurocomputing"}, {"Title": "An overview of recent multi-view clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Athana<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "402", "Issue": "", "Page": "148", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 87241621, "Title": "RETRACTED ARTICLE: Enhanced classification loss functions and regularization loss\n function (ECLFaRLF) algorithm for bowel cancer feature classification", "Abstract": "<p>Bowel cancer is one of the most common cancers as stated in the bowel cancer cases statistics. The proposed technique is to recognize the pattern of tissue affected by bowel cancer by using support vector machine (SVM) classification. This research aims to increase the accuracy of detecting and classifying bowel cancer with reduced processing time. The proposed method considered a feature extraction and image classification by Eenhanced Classification Loss Functions and Regularization Loss Function (ECLFaRLF) algorithm. This method allowed for more precise interpretations regarding the best associations for bowel cancer. The proposed was tested on colorectal images from different datasets commonly investigated in the proposed solution. The test was evaluated by applying 10-fold cross-validation method. All classification methods provide differentiation rate above processing time 0.413 s, and accuracy 95.67% for the state of the art solution, but by introducing SVM2 classification algorithm produce high accuracy rate with average accuracy is 97.02% over 95.67% and with processing time 0.359 s over 0.413 s. This reality shows the significance of the discriminating power of the SVM2 classifier. The proposed framework has presented an examination of feature extraction and classification techniques to help pathologists in the identifying of benign and malignant diagnosis of bowel cancer.</p>", "Keywords": "Deep learning; Image recognition; Feature extraction; Neural network; Bowel cancer; Feature classification", "DOI": "10.1007/s11042-021-10699-1", "PubYear": 2021, "Volume": "80", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Mathematics, Charles <PERSON> University (CSU), Wagga Wagga, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Mathematics, Charles <PERSON> University (CSU), Wagga Wagga, Australia;School of Computer Data and Mathematical Sciences, University of Western Sydney (UWS), Sydney, Australia;School of Information Technology, Southern Cross University (SCU), Sydney, Australia;Asia Pacific International College (APIC), Information Technology Department, Sydney, Australia;Kent Institute Australia, Information Technology Department, Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Computing and Mathematics, Charles <PERSON> University (CSU), Wagga Wagga, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Technology, Baghdad, Iraq"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Medicine, University of New South Wales, Sydney, Australia"}], "References": []}, {"ArticleId": 87241623, "Title": "Porosity-dependent vibration analysis of FG microplates embedded by polymeric nanocomposite patches considering hygrothermal effect via an innovative plate theory", "Abstract": "<p>The sandwich structures contain three or more layers attached to the core. In the current research, a three-layered sandwich microplate containing functionally graded (FG) porous materials as core and piezoelectric nanocomposite materials as face sheets subjected to electric field resting on Pasternak foundation is chosen as a model to investigate its vibrational behavior. To make the face sheets stiffer, they are reinforced by carbon nanotubes (CNTs) via different distribution patterns which result in changing their properties along the thickness direction. An innovative quasi-3D shear deformation theory with five unknowns, <PERSON>’s principle, and modified couple stress theory are hired to gain equations of motion related to the abovementioned microstructure. Eventually, the evaluation of materials’ properties, geometry specifications, foundation moduli, and hygrothermal environment on vibrational behavior of such structures became easier using the presented results of the current study in figure format. As an instance, it is revealed that CNTs’ volume fraction elevation causes mechanical properties improvement, and in the following, natural frequency increment. Besides, considering the hygrothermal environment causes significant effects on the results.</p>", "Keywords": "Sandwich structures; Piezoelectricity; Vibration analysis; Porous materials; Nanocomposite; Modified couple stress theory; Hygrothermal environment", "DOI": "10.1007/s00366-021-01382-y", "PubYear": 2022, "Volume": "38", "Issue": "S5", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Solid Mechanics, Faculty of Mechanical Engineering, University of Kashan, Kashan, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Basic and Applied Sciences for Engineering, Faculty of Civil and Industrial Engineering, Sapienza University, Rome, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Solid Mechanics, Faculty of Mechanical Engineering, University of Kashan, Kashan, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Solid Mechanics, Faculty of Mechanical Engineering, University of Kashan, Kashan, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "YFL (Yonsei Frontier Lab), Yonsei University, Seoul, Korea; Department of Civil and Environmental Engineering, King <PERSON>d University of Petroleum and Minerals, Dhahran, Saudi Arabia; Material and Hydrology Laboratory, Civil Engineering Department, Faculty of Technology, University of Sidi Bel Abbes, Sidi Bel Abbès, Algeria"}], "References": [{"Title": "Analytical modeling of bending and vibration of thick advanced composite plates using a four-variable quasi 3D HSDT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "807", "JournalTitle": "Engineering with Computers"}, {"Title": "Thermal vibration analysis of embedded graphene oxide powder-reinforced nanocomposite plates", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "879", "JournalTitle": "Engineering with Computers"}, {"Title": "Nonlocal strain gradient forced vibrations of FG-GPLRC nanocomposite microbeams", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1739", "JournalTitle": "Engineering with Computers"}, {"Title": "A new numerical approach for low velocity impact response of multiscale-reinforced nanocomposite plates", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "713", "JournalTitle": "Engineering with Computers"}, {"Title": "Vibration analysis of porous magneto-electro-elastically actuated carbon nanotube-reinforced composite sandwich plate based on a refined plate theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "921", "JournalTitle": "Engineering with Computers"}, {"Title": "Vibration control of rotating sandwich cylindrical shell-reinforced nanocomposite face sheet and porous core integrated with functionally graded magneto-electro-elastic layers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "87", "JournalTitle": "Engineering with Computers"}, {"Title": "Effects of elastic foundation on the large-amplitude vibration analysis of functionally graded GPL-RC annular sector plates", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "325", "JournalTitle": "Engineering with Computers"}, {"Title": "Vibration analysis of polymer composite plates reinforced with graphene platelets resting on two-parameter viscoelastic foundation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "419", "JournalTitle": "Engineering with Computers"}, {"Title": "Hygro-thermal buckling analysis of polymer–CNT–fiber-laminated nanocomposite disk under uniform lateral pressure with the aid of GDQM", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1793", "JournalTitle": "Engineering with Computers"}, {"Title": "A geometrically nonlinear size-dependent hypothesis for porous functionally graded micro-plate", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S1", "Page": "449", "JournalTitle": "Engineering with Computers"}, {"Title": "Dynamic buckling of functionally graded multilayer graphene nanocomposite annular plate under different boundary conditions in thermal environment", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S1", "Page": "583", "JournalTitle": "Engineering with Computers"}, {"Title": "Thermal vibration characteristics of pre/post-buckled bi-directional functionally graded tapered microbeams based on modified couple stress Reddy beam theory", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "3", "Page": "2079", "JournalTitle": "Engineering with Computers"}, {"Title": "Porosity, mass and geometric imperfection sensitivity in coupled vibration characteristics of CNT-strengthened beams with different boundary conditions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "3", "Page": "2313", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 87241627, "Title": "Automatic Piano Sheet Music Transcription with Machine Learning", "Abstract": "Automatic Music Transcription (AMT) is becoming more and more popular throughout the day, it has piqued the interest of many in addition to academic research. A successful AMT system would be able to bridge multiple ranges of interactions between people and music, including music education. The goal of this research is to transcribe an audio input to music notation. Research methods were conducted by training multiple neural networks architectures in different kinds of cases. The evaluation used two approaches, those were objective evaluation and subjective evaluation. The result of this research was an achievement of 74.80% F1 score and 73.3% out of 30 respondents claimed that Bidirectional Long Short-Term Memory (BiLSTM) has the best result. It could be concluded that BiLSTM is the best architecture suited for automatic music transcription.", "Keywords": "Automatic Music Transcription;Recurrent Neural Network;Convolutional Neural Network", "DOI": "10.3844/jcssp.2021.178.187", "PubYear": 2021, "Volume": "17", "Issue": "3", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bina Nusantara University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Bina Nusantara University"}, {"AuthorId": 3, "Name": " <PERSON>", "Affiliation": "Bina Nusantara University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Bina Nusantara University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Square <PERSON>"}], "References": []}, {"ArticleId": 87241628, "Title": "Geometrical Approach to a New Hybrid Grid-Based Gravitational Clustering Algorithm", "Abstract": "In the past years, several clustering algorithms have been developed, for example, K-means, K-medoid. Most of these algorithms have the common problem of selecting the appropriate number of clusters and these algorithms are sensitive to noisy data and would cause less accurate clustering of the data set. Therefore, this paper introduces a new Hybrid Grid-based Gravitational Clustering Algorithm (HGGCA) geometrically, which can automatically detect the number of clusters of the targeted data set and find the clusters with any arbitrary forms and filter the noisy data. This proposed clustering algorithm is used to move the cluster centers to the areas where the data density is high based on <PERSON>’s law of gravity and Newton’s laws of motion. Also, the proposed method has higher accuracy than the existing K-means and K-medoids methods which is shown in the experimental result. In this study, we used cluster-validity-indicators to verify the validity of the proposed and existing methods of clustering. Experimental results show that the proposed algorithm massively creates high-quality clusters.", "Keywords": "Clustering;<PERSON>’s Law of Gravity;Euclidean Distance;<PERSON>’s Law of Motion;Cluster Validity Index", "DOI": "10.3844/jcssp.2021.197.204", "PubYear": 2021, "Volume": "17", "Issue": "3", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>al Bin Al Abid", "Affiliation": "International Islamic University Chittagong"}, {"AuthorId": 2, "Name": "A.N<PERSON><PERSON><PERSON>", "Affiliation": "International Islamic University Chittagong"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "International Islamic University Chittagong"}], "References": []}, {"ArticleId": 87241629, "Title": "Is Single Scan based Restructuring Always a Suitable Approach to Handle Incremental Frequent Pattern Mining?", "Abstract": "Incremental mining of frequent patterns has attracted the attention of researchers in the last two decades. The researchers have explored the frequent pattern mining from incremental database problems by considering that the complete database to be processed can be accommodated in systems’ main memory even after the database gets updated very frequently. The FP-tree-based approaches were able to draw more interest because of their compact representation and requirement of a minimum number of database scans. The researchers have developed a few FP-tree based methods to handle the incremental scenario by adjusting or restructuring the tree prefix paths. Although the approaches have managed to solve the re-computation problem by constructing a complete pattern tree data structure using only one database scan, restructuring the prefix paths for each transaction is a computationally costly task, leading to the high tree construction time. If the FP-tree construction process can be supported with suitable data structures, reconstruction of the FP-tree from scratch may be less time consuming than the restructuring approaches in case of incremental scenario. In this study, we have proposed a tree data structure called Improved Frequent Pattern tree (Improved FP-tree). The proposed Improved FP-tree construction algorithm has immensely improved the performance of tree construction time by resourcefully using node links, maintained in header table to manage the same item node list in the FP-tree. The experimental results emphasize the significance of the proposed Improved FP-tree construction algorithm over a few conventional incremental FP-tree construction algorithms with prefix path restructuring.", "Keywords": "FP-tree;FP-Growth;Frequent Pattern;Pattern Mining;Data Mining;Frequent Itemset;Itemset Mining;Pattern Analysis", "DOI": "10.3844/jcssp.2021.205.220", "PubYear": 2021, "Volume": "17", "Issue": "3", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tezpur University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tezpur University"}], "References": []}, {"ArticleId": 87241680, "Title": "NLE volume 27 issue 2 Cover and Front matter", "Abstract": "", "Keywords": "", "DOI": "10.1017/S1351324921000036", "PubYear": 2021, "Volume": "27", "Issue": "2", "JournalId": 23332, "JournalTitle": "Natural Language Engineering", "ISSN": "1351-3249", "EISSN": "1469-8110", "Authors": [], "References": []}, {"ArticleId": 87241687, "Title": "Safe and secure cyber‐physical systems", "Abstract": "<p>Cyber-Physical Systems (CPSs) differ from traditional Information Technology (IT) systems in such a way that they interact with the physical environment, i.e., they can monitor and manipulate real objects and processes. For this special issue, the authors of the best papers of IWCFS 2019 were invited to submit extended versions of their workshop papers. Additionally, we received eight submissions from around the globe as a result of an open call. After thorough and stringent reviews, we selected six articles that provide relevant contributions to the field of safety and security for CPSs.</p>", "Keywords": "cyber-physical system;safety;security", "DOI": "10.1002/smr.2340", "PubYear": 2021, "Volume": "33", "Issue": "9", "JournalId": 2327, "JournalTitle": "Journal of Software: Evolution and Process", "ISSN": "2047-7473", "EISSN": "2047-7481", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Software Competence Center Hagenberg, Hagenberg, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Software Systems Engineering, Johannes <PERSON>pler University Linz, Linz, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for Software Systems Engineering, Johannes <PERSON>pler University Linz, Linz, Austria"}], "References": [{"Title": "Security‐ and safety‐critical cyber‐physical systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "2", "Page": "", "JournalTitle": "Journal of Software: Evolution and Process"}, {"Title": "How to curtail oversensing in the home", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "6", "Page": "20", "JournalTitle": "Communications of the ACM"}, {"Title": "Cyber-physical systems security: Limitations, issues and future trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "77", "Issue": "", "Page": "103201", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 87241693, "Title": "First-Degree Polynomial Gradient Approach to Reveal the Severity of COVID-19 Pandemic in Affected Countries", "Abstract": "COVID-19 is a new type of Coronavirus (2019-nCoV) which originated from Wuhan in China. Since 11 March 2020, WHO has declared COVID-19 as a pandemic. Currently, it has spread to 175 countries or regions around the world. From day to day, confirmed, recovered and death cases have been reported. This data rapidly changes that indicates an uncertain situation. This uncertain situation might affect many social-economic activities. However, until now, there is no approach to categorize these countries in conjunction with the latest situation. The typical measure, for example, the Case Fatality Rate (CFR) is used to measure the proportion of deaths compared to the total number of confirmed from a certain disease. It utilizes for diseases with discrete, limited-time courses, such as outbreaks of acute infections. The major drawback of CFR is it can only be considered as a final result when all the cases have been accomplished (either died or recovered). According to this gap, we proposed the first-degree polynomial or linear gradient approach to categorize the COVID-19 severity status of areas or countries based on the rate of confirmed, recovered and death cases. The status categorization is necessary information for all parties to be aware of the situation. It can be used for consideration to determine policies related to COVID-19 pandemic such as travel warning, self-isolation, work from home, lock-down, etc. © 2021 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>. This open access article is distributed under a Creative Commons Attribution (CC-BY) 4.0 license.", "Keywords": "Case Fatality Rate; COVID-19; First- Degree Polynomial; Gradient; Pandemic", "DOI": "10.3844/jcssp.2021.167.177", "PubYear": 2021, "Volume": "17", "Issue": "2", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Applied Science, Telkom University Bandung, Indonesia"}, {"AuthorId": 2, "Name": "Anak Agung Gde Agung", "Affiliation": "School of Applied Science, Telkom University Bandung, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Applied Science, Telkom University Bandung, Indonesia"}, {"AuthorId": 4, "Name": " <PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Applied Science, Telkom University Bandung, Indonesia"}], "References": []}, {"ArticleId": 87241698, "Title": "Fault detection and diagnosis of linear bearing in auto core adhesion mounting machines based on condition monitoring", "Abstract": "This study aims to increase machine reliability thereby preventing product defects from adhesive dispense and slider attachment by a fault detection and diagnostic technique. The experiment was set up to investigate the vibration signal and motor current. Six fault conditions of a linear bearing were set up. The approaches, including spectrum analysis, crest factor, and analysis of variance, are used for data analysis. It was found that the spectrum analysis was suitable for classifying the frequency domains and the statistics tool was successful in measuring the current. Fault detection and diagnosis results can forecast the status of the linear bearings.", "Keywords": "Fault detection and diagnosis ; linear bearing ; analysis of variance ; spectrum analysis ; crest factor ; motor current", "DOI": "10.1080/21642583.2021.1895901", "PubYear": 2021, "Volume": "9", "Issue": "1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Institute of Engineering, Suranaree University of Technology, Nakhon Ratchasima, Thailand;Western Digital Thailand Co. Ltd., Phra Nakhon Si Ayutthaya, Thailand"}, {"AuthorId": 2, "Name": "Thanasak <PERSON>", "Affiliation": "School of Mechanical Engineering, Institute of Engineering, Suranaree University of Technology, Nakhon Ratchasima, Thailand"}, {"AuthorId": 3, "Name": "Jiraphon Sri<PERSON>pol", "Affiliation": "School of Mechanical Engineering, Institute of Engineering, Suranaree University of Technology, Nakhon Ratchasima, Thailand"}], "References": []}, {"ArticleId": 87241720, "Title": "A RobustICA-based algorithmic system for blind separation of convolutive mixtures", "Abstract": "<p>In this paper, we propose a frequency-domain method employing robust independent component analysis (RICA) to address the multichannel Blind Source Separation (BSS) problem of convolutive speech mixtures in highly reverberant environments. We impose regularization processes to tackle the ill-conditioning problem of the covariance matrix and to mitigate the performance degradation. We evaluate the impact of several parameters on the performance of separation, e.g., windowing type and overlapping ratio of the frequency domain method. We then assess and compare different techniques to solve the frequency-domain permutation ambiguity. Furthermore, we develop an algorithm to separate the source signals in adverse conditions, i.e. high reverberation conditions when short observation signals are available. Finally, through extensive simulations and real-world experiments, we evaluate and demonstrate the superiority of the presented convolutive algorithmic system in comparison to other BSS algorithms, including recursive regularized ICA (RR-ICA) and independent vector analysis (IVA).</p>", "Keywords": "Blind source separation (BSS); Independent Component analysis (ICA); FastICA; Robust Independent component analysis (RobustICA); Highly reverberant environments; gradient descent algorithms; Recursive regularized ICA (RR-ICA); Independent vector analysis (IVA)", "DOI": "10.1007/s10772-021-09833-z", "PubYear": 2021, "Volume": "24", "Issue": "3", "JournalId": 4265, "JournalTitle": "International Journal of Speech Technology", "ISSN": "1381-2416", "EISSN": "1572-8110", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics Engineering Department, Hijjawi Faculty for Engineering Technology, Yarmouk University, Irbid, Jordan"}, {"AuthorId": 2, "Name": "Fathi M. <PERSON>", "Affiliation": "Circuits, Systems, And Neural Networks (CSANN) Laboratory Department of Electrical and Computer Engineering, Michigan State University, East Lansing, USA"}], "References": [{"Title": "Two pairwise iterative schemes for high dimensional blind source separation", "Authors": "<PERSON><PERSON>; Fathi Salem", "PubYear": 2021, "Volume": "24", "Issue": "4", "Page": "957", "JournalTitle": "International Journal of Speech Technology"}]}, {"ArticleId": 87242063, "Title": "Psychometric Evaluation of the F-SUS: Creation and Validation of the French Version of the System Usability Scale", "Abstract": "While the System Usability Scale (SUS) is probably one of the most widely used questionnaires to measure the perceived ease of use of interactive systems, there is currently no scientific valid translation in French. This article describes the translation and statistical validation of the French version of the SUS, called F-SUS. On the basis of two translations carried out by a committee of bilingual experts, the various psychometric analyses made it possible to select only one translation. Fidelity measurement, factor analysis and sensitivity measurement obtained results very close or similar to the original version of the SUS. Thus, the F-SUS can be used with confidence by French-speaking usability researchers and practitioners.", "Keywords": "", "DOI": "10.1080/10447318.2021.1898828", "PubYear": 2021, "Volume": "37", "Issue": "16", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Information Technology for Innovative Sciences (ITIS), Luxembourg Institute of Science and Technology (LIST), Esch-sur-Alzette, Luxembourg"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Information Technology for Innovative Sciences (ITIS), Luxembourg Institute of Science and Technology (LIST), Esch-sur-Alzette, Luxembourg"}], "References": [{"Title": "Is It Time to Go Positive? Assessing the Positively Worded System Usability Scale (SUS)", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "6", "Page": "987", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}]}, {"ArticleId": 87242064, "Title": "SNS Adoption for Consumer Active Information Search (AIS) - the Dyadic Role of Information Credibility", "Abstract": "Social network sites are used by consumers to search for information before making purchase decisions. While most research has investigated what motivates consumers to share their experiences with others and its effect on consumer behavior, the current study focuses on consumer active information search (AIS), that is, explicit consumer requests for information on SNS. This study integrates information credibility with a modified and customized UTAUT2 (Unified Theory of Acceptance and Use of technology2) to explain the consumer use of SNS for active information search. Data were collected using a representative sample of 729 Facebook users and was analyzed using Structural Equation Modeling (SEM). The results provide support for the extended UTAUT2 model and confirm its robustness in predicting consumer intention to adopt SNS as a vehicle for AIS. Moreover, the research emphasizes the centrality of information credibility and its dyadic role as an antecedent and a moderator in the adoption process. Acknowledgments We thank the support of the Institute for the study of new media, politics and society in the School of Communication in Ariel University. Additional information Notes on contributors <PERSON><PERSON> is a faculty member in the School of Communication at Ariel University. She has a field experience in marketing managemnt. Here research focuses on Consumer behavior in digital environments and in online shopping, and Marketing communication. Israel D. Nebenzahl Israel D<PERSON> is a proffessor emeritus and the former Dean of the Faculty of Social Sciences and Humanities at Ariel University. His research interest includes national and country image effects. He coauthored the two editions of National Image and Competitive advantage, Copenhagen Business School Press, 2001, 2006. <PERSON><PERSON> is a faculty membre in the School of Communication at Ariel University. His research focuses on the social and political uses and impact of the Internet and social media. Shalom Levy Shalom <PERSON> is a senior lecturer in marketing in the Department of Economics and Business Administration at Ariel University. Previously, he worked as a media manager and head of planning and research in advertising companies. His research interest includes eWOM, Social media, Social commerce and Marketing communications.", "Keywords": "", "DOI": "10.1080/10447318.2021.1898824", "PubYear": 2021, "Volume": "37", "Issue": "16", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Communication, Ariel University, Ariel, Israel"}, {"AuthorId": 2, "Name": "Israel <PERSON><PERSON>", "Affiliation": "Department of Economics and Business Administration, Ariel University, Ariel, Israel"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Communication, Ariel University, Ariel, Israel"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Economics and Business Administration, Ariel University, Ariel, Israel"}], "References": [{"Title": "Aiming the Mobile Targets in a Cross-Cultural Context: Effects of Trust, Privacy Concerns, and Attitude", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "227", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Determinants of Trust in Health Information Technology: An Empirical Investigation in the Context of an Online Clinic Appointment System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "12", "Page": "1095", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "The influence of subjective characteristics of social network sites on consumers' word-of-mouth sharing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "5", "Page": "977", "JournalTitle": "Online Information Review"}]}, {"ArticleId": 87242078, "Title": "A MCDM Method with Linguistic Variables and Intuitionistic Fuzzy Numbers to Evaluate Product Development Projects", "Abstract": "", "Keywords": "Product development projects (PDP), Linguistic variable, Intuitionistic fuzzy number, Multiple criteria decision-making (MCDM)", "DOI": "10.2991/ijcis.d.210222.003", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87242204, "Title": "Model-based test case generation from UML sequence diagrams using extended finite state machines", "Abstract": "<p>The effectiveness of model-based testing (MBT) is mainly due to its potential for automation. If the model is formal and machine-readable, test cases can be derived automatically. One of the most used formal modeling techniques is the interpretation of a system as an extended finite state machine (EFSM). However, formal models are not a common practice in the industry. The Unified Modeling Language (UML) has become the de facto standard for software modeling. Nevertheless, due to the lack of formal semantics, its diagrams can be given ambiguous interpretations and are not suitable for testing automation. This article introduces a systematic procedure for the generation of tests from UML models that uses concepts of model-driven engineering (MDE) for formalizing UML sequence diagrams into extended finite state machines and providing a precise semantics for them. It also applies ModelJUnit and JUnit libraries for an automatic generation of test cases. A case study was conducted in a real software towards the evaluation of its applicability.</p>", "Keywords": "Model-based testing; Model-driven engineering; Sequence diagram; Extended finite state machine; ModelJUnit; JUnit", "DOI": "10.1007/s11219-020-09531-0", "PubYear": 2021, "Volume": "29", "Issue": "3", "JournalId": 27487, "JournalTitle": "Software Quality Journal", "ISSN": "0963-9314", "EISSN": "1573-1367", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Instituto de Ciências Matemáticas e de Computação (ICMC), USP, São Carlos, SP, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Instituto de Ciências Matemáticas e de Computação (ICMC), USP, São Carlos, SP, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centro de Tecnologia e Urbanismo (CTU), UESPI, Teresina, Brazil"}], "References": []}, {"ArticleId": 87242290, "Title": "Remote Monitoring of Heart Patients Using Robotic Process Automation (RPA)", "Abstract": "After the age of 60 years or older many people are diagnosed with heart related issues to follow up older patients, bedridden, who needs maximum care at their homes implementation of phone monitoring becomes necessary. According to study many heart patients are supposed to have a regular check up to keep a track on their physiological data. Health care is a ground that is rapidly developing in services and technology. A recent development in this region is remote monitoring of patients which has many benefits in a fast-aging world population with increasing health complications. Sensors are used to monitor essentials or vital requirements such as heartbeat, blood pressure, temperature, blood glucose level and many more. Remote monitoring varies for every age group and every aspect. During pandemic times its difficult for many of them to move around, thus remote monitoring of patients helps to have a safer and efficient way to monitor them and also saves time. These new technologies can make a contact-less and monitor illness based on the sensor values. To make remote monitoring more efficient automation places a vital role. In health sector, to make any process automated RPA (Robotic process automation) is used. Using the established application software robots automate the process originally performed by human beings. This paper focuses on easy way to monitor patients who are in a distant place, have a regular check-up without visiting the doctor regularly and in case of emergency contact the doctor within no time.", "Keywords": "", "DOI": "10.1051/itmconf/20213701002", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science CHRIST (Deemed to be University), Bengaluru, India"}, {"AuthorId": 2, "Name": "V <PERSON>", "Affiliation": "Department of Computer Science CHRIST (Deemed to be University), Bengaluru, India"}], "References": []}, {"ArticleId": 87242291, "Title": "Hardiness sensing for susceptibility using American Fuzzy Lo<PERSON>", "Abstract": "American Fuzzy Lop is an automated software testing method to give unexceeded values, else random data’s as input to computer programs for testing the hardiness process. Existing fuzzing methods will come under carried based type. Pointing to test a certain input that covers a certain region of units to fit under retain part. Still, a susceptibility over a program space may not arrive paraded in all execution that occurs to see the certain program parts; just some program executions will go the location could disclose the susceptibility. In this paper, we introduced a unified fitness metric known as direct board, which can be used for American Fuzzy lop, and that is explicitly pointed toward exploring for test input that can disclose susceptibilities. To improvise the AFL, we have enhanced its methodology to produce a more effective quality fuzzing tool. This causes our method to notice buffer invade as well as the whole number invade susceptibilities. Enhanced AFL is tested with similar benchmark programs to compare it and adding an extension to it called AFLBLEND. By our method, many uncover susceptibility could be found with a given amount of time and faster than the AFL approach by 8%.", "Keywords": "AFL;American <PERSON><PERSON>;Fuzzing", "DOI": "10.1051/itmconf/20213701003", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON>", "Affiliation": "Kongunadu College of Engineering and Technology, Trichy, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "KSR College of Engineering, Tiruchengode, India"}, {"AuthorId": 3, "Name": "<PERSON> Indhuja", "Affiliation": "<PERSON><PERSON><PERSON> College of Engineering, Salem, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "KSR College of Engineering, Tiruchengode, India"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Mahendra Institute of Technology, Namakkal, India"}], "References": []}, {"ArticleId": 87242292, "Title": "Block Mining reward prediction with Polynomial Regression, Long short-term memory, and Prophet API for Ethereum blockchain miners", "Abstract": "The Ethereum blockchain is an open-source, decentralized blockchain with functions triggered by smart contract and has voluminous real-time data for analysis using machine learning and deep learning algorithms. Ether is the cryptocurrency of the Ethereum blockchain. Ethereum virtual machine is used to run Turing complete scripts. The data set concerning a block in the Ethereum blockchain with a block number, timestamp, crypto address of the miner, and the block rewards for the miner are explored for K means clustering for clustering miners with a unique crypto address and their rewards. Linear regression and polynomial regression are used for the prediction of the next block reward to the miner. The Long ShortTerm Memory (LSTM) algorithm is used to exploit the Ether market data set for predicting the next ether price in the market. Every kind of price and volume for every four hours is taken for prediction. The root mean square error of 34.9% is obtained for linear regression, the silhouette score is 71% for K-means clustering of miners with same rewards, with the optimal number of clusters obtained by Gap statistic method.", "Keywords": "", "DOI": "10.1051/itmconf/20213701004", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, SASTRA Deemed University, Tamil Nadu"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing, SASTRA Deemed University, Tamil Nadu"}], "References": [{"Title": "RETRACTED ARTICLE: A new cluster P2P file sharing system based on IPFS and blockchain technology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "S1", "Page": "63", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Bitcoin price prediction using ARIMA model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "4", "Page": "396", "JournalTitle": "International Journal of Internet Technology and Secured Transactions"}]}, {"ArticleId": 87242293, "Title": "Review of Medical Image Synthesis using GAN Techniques", "Abstract": "Generative Adversarial Networks (GANs) is one of the vital efficient methods for generating a massive, high-quality artificial picture. For diagnosing particular diseases in a medical image, a general problem is that it is expensive, usage of high radiation dosage, and time-consuming to collect data. Hence GAN is a deep learning method that has been developed for the image to image translation, i.e. from low-resolution to highresolution image, for example generating Magnetic resonance image (MRI) from computed tomography image (CT) and 7T from 3T MRI which can be used to obtain multimodal datasets from single modality. In this review paper, different GAN architectures were discussed for medical image analysis.", "Keywords": "Generative adversarial network;Computerized Tomography;Magnetic Resonance Imaging", "DOI": "10.1051/itmconf/20213701005", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> alias <PERSON><PERSON>", "Affiliation": "School of Electronics Engineering,Vellore Institute of Technology, Chennai"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics Engineering,Vellore Institute of Technology, Chennai"}], "References": []}, {"ArticleId": 87242294, "Title": "An intelligent detection and therapeutic device to support sleep apnea in infants", "Abstract": "Among the numerous sleep-disorders breathing patterns encountered by babies, such as intermittent respiration, premature apnea, obstructive sleep apnea wa sconsidered a major cause of concern. Upper airway structure, pulmonary system mechanics, etc. are only a few reasons why the babies are vulnerable to obstructive sleep disorder. An imbalance in the viscoelastic properties of the pharynx, dilators and pressure can lead to airway collapse. Low level of oxygen in blood or hypoxemia is considered a characteristic in infants with severe Obstructive Sleep Apnea (OSA). Invasive treatments like nasopharyngeal tubes, continuous positive airway pressure (CPAP), or tracheostomy are found to be helpful in most cases where infants experience sleep apnea. This paper suggests an appropriate method for long-term monitoring of obstructive sleep apnea in infants and, if any abnormalities are observed, the tool provides continuous airway pressure treatment until the abnormality is stabilized. Resilient propagation algorithm is utilised to train the datasets and produce a relevant output.", "Keywords": "", "DOI": "10.1051/itmconf/***********", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "Sindu Divakaran", "Affiliation": "Department of Biomedical Engineering, Sathyabama Institute of Science and Technology, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Sathyabama Institute of Science and Technology, Chennai, India"}, {"AuthorId": 3, "Name": "R Sindhiya", "Affiliation": "Department of Biomedical Engineering, Sathyabama Institute of Science and Technology, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Sathyabama Institute of Science and Technology, Chennai, India"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, Sathyabama Institute of Science and Technology, Chennai, India"}], "References": []}, {"ArticleId": 87242295, "Title": "Survey on Template Engines in Java", "Abstract": "In today’s fast paced world every minute is very important. In the corporate world and also otherwise Documentation is very important for various purposes like version control, proofs, consent, copyrights and expectations and outcomes/reports. So, because of these reasons template engines have become very important and extremely necessary for the world. Template engines are basically software that help us create result documents from data models and templates by combining them. This paper presents a survey on the newest development of research work on template engines for java, along with an in-depth outline of its research. To the best of what developments have been achieved till now, this survey paper has been written. Finally, the differences, advantages and disadvantages of the various template engines for java, have been tabulated as a part of the results.", "Keywords": "", "DOI": "10.1051/itmconf/20213701007", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai Campus, India 600 127"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai Campus, India 600 127"}], "References": []}, {"ArticleId": 87242325, "Title": "Securing Pharmaceutical Supply Chain using Blockchain Technology", "Abstract": "The production and distribution of counterfeit drugs is an urgent and increasingly critical worldwide issue, especially in pandemics. The imperfect supply chain system in the pharmaceutical industry is one of the reasons for drug counterfeiting. Drugs ownership changes from manufacturers to wholesaler, distributor, and then pharmacist before it reaches the customer thus making it difficult to keep track of it. In this paper, we have compared the existing proposed architectures of blockchain and IoT based supply chain management systems. The system implemented using hyper ledger fabric ensures sharing, storing, transparency, and traceability of data in each link of the supply chain. On the other hand, Ethereum architecture utilized the features of smart contracts to manage the interactions between sender and receiver. Finally, the study mainly focuses on increasing the safety of pharmaceutical products and reducing the manual operation of the supply chain with the most efficient architecture.", "Keywords": "", "DOI": "10.1051/itmconf/20213701013", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information Technology, SCTR’s Pune Institute of Computer Technology (PICT), Pune, Maharashtra, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Information Technology, SCTR’s Pune Institute of Computer Technology (PICT), Pune, Maharashtra, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Technology, SCTR’s Pune Institute of Computer Technology (PICT), Pune, Maharashtra, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Technology, SCTR’s Pune Institute of Computer Technology (PICT), Pune, Maharashtra, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Information Technology, SCTR’s Pune Institute of Computer Technology (PICT), Pune, Maharashtra, India"}], "References": [{"Title": "Supply chain transparency through blockchain-based traceability: An overview with demonstration", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "106895", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 87242327, "Title": "A Survey on Healthcare Systems using Internet of Things", "Abstract": "The Internet of Things allows things to become active users, facilitating interaction with things and the sharing of data between them. The most interesting issue in the science world, the public sector and industry in the IoT is unavoidable. It ensures a seamless relationship between doctors and patients that results in medical treatment with high quality results. This is accomplished by constant surveillance of patients through the use of sensors. The collected data is registered for potential uses and used for analytics. The analytical approach offers the opportunity for disease detection in healthcare results. This paper concerns the Internet of Things in healthcare and explores the different algorithms used in it. The system involved in analytics of healthcare and data sources involved in analytics are further clarified. Finally this paper demonstrates the Internet of Things and Big Data survey of healthcare systems with a reference table.", "Keywords": "", "DOI": "10.1051/itmconf/20213701015", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Immaculate College for Women, Cuddalore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, VISTAS, Chennai, India"}], "References": []}, {"ArticleId": 87242331, "Title": "Virtual AI Assistant for Person with Partial Vision Impairment", "Abstract": "Smartphones help us with almost every activity and task nowadays. The features and hardware of the phone can be leveraged to make apps for online payment, content consumption, and creation, accessibility, etc. These devices can also be used to help and assist visually challenged and guide them in their daily activities. As the visually challenged sometimes face difficulty in sensing the objects or humans in the surroundings, they require guidance or help in recognizing objects, human faces, reading text, and other activities. Hence, this Android application has been proposed to help and assist people with partial vision impairment. The application will make use of technologies like face detection, object and text recognition, barcode scanner, and a basic voice-based chatbot which can be used to execute basic commands implemented through Deep Learning, Artificial Intelligence, and Machine Learning. The application will be able to detect the number of faces, recognize the object in the camera frame of the application, read out the text from newspapers, documents, etc, and open the link detected from the barcode, all given as output to the user in the form of voice.", "Keywords": "", "DOI": "10.1051/itmconf/20213701019", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, SIES Graduate School of Technology, Navi Mumbai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, SIES Graduate School of Technology, Navi Mumbai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, SIES Graduate School of Technology, Navi Mumbai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, SIES Graduate School of Technology, Navi Mumbai, India"}], "References": []}]