[{"ArticleId": 105516538, "Title": "Security Analysis of RFID Technology", "Abstract": "", "Keywords": "", "DOI": "10.12677/SEA.2022.116137", "PubYear": 2022, "Volume": "11", "Issue": "6", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "红琦 包", "Affiliation": ""}], "References": []}, {"ArticleId": 105516590, "Title": "The Impact of Different Environmental Regulatory Tools on Technological Innovation in China’s Manufacturing Industry", "Abstract": "There are different types of environmental regulation tools, such as command and control, economic incentive and public participation, and the combination of environmental regulation tools can be divided into command and control and economic incentive, economic incentive and public participation, command and control and public participation and so on. Using the panel data of China’s listed manufacturing enterprises from 2010 to 2018, this paper empirically analyzes the impact of three combinations of environmental regulation tools, namely command and control type and economic incentive type, economic incentive type and public participation type, command and control type and public participation type, on the technological innovation of China’s manufacturing enterprises. The results show that the combination of command and control type and economic incentive type, economic incentive type and public participation type can promote the technological innovation of manufacturing enterprises, while the combination of command and control type and public participation type is not conducive to the technological innovation of China’s manufacturing enterprises. Regression is conducted with economic incentives of Environmental regulation as the threshold variable, found the command control type and the public participation in environmental regulation on manufacturing technology innovation is double threshold effect, and with the increase of the economic strength of the motivational tool, command control type and the public participating the effect of environmental regulation tools are also enhanced. The research in this paper shows that when environmental regulation of China’s manufacturing enterprises is carried out, the combination of command and control type and economic incentive type and economic incentive type and public participation type can play a role in promoting technological innovation in China’s manufacturing industry.", "Keywords": "China’s Manufacturing Industry;Environmental Regulation;Combination of Environmental Regulation;Technological Innovation;Threshold Effect", "DOI": "10.4236/ib.2022.144022", "PubYear": 2022, "Volume": "14", "Issue": "4", "JournalId": 21352, "JournalTitle": "iBusiness", "ISSN": "2150-4075", "EISSN": "2150-4083", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics, Wuhan University of Technology, Wuhan, China ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics, Wuhan University of Technology, Wuhan, China ."}], "References": []}, {"ArticleId": 105516633, "Title": "Research and Practice on High Availability Scheme of Unified Identity Authentication System Based on CAS in Colleges and Universities", "Abstract": "Unified identity authentication has become the basic information service provided by colleges and universities for teachers and students. Security, stability, high concurrency and easy maintenance are our requirements for a unified identity authentication system. Based on the practical work experience of China University of Geosciences (Beijing), this paper proposes a high availability scheme of unified identity authentication system based on CAS, which is composed of multiple CAS Servers, Nginx for load balancing, and Redis as a cache database. The scheme has been practiced in China University of Geosciences (Beijing), and the application effect is good, which has practical reference significance for other universities.", "Keywords": "Unified Identity Authentication;CAS;Redis;High Availability;Colleges and Universities", "DOI": "10.4236/jis.2023.141002", "PubYear": 2023, "Volume": "14", "Issue": "1", "JournalId": 26129, "JournalTitle": "Journal of Information Security", "ISSN": "2153-1234", "EISSN": "2153-1242", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Information Network Center, China University of Geosciences (Beijing), Beijing, China ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Information Network Center, China University of Geosciences (Beijing), Beijing, China .; Corresponding author"}], "References": []}, {"ArticleId": 105516685, "Title": "Automatic collaborative water surface coverage and cleaning strategy of UAV and USVs", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.dcan.2022.12.014", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 5621, "JournalTitle": "Digital Communications and Networks", "ISSN": "2352-8648", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "A modified particle swarm optimization for multimodal multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "103905", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Major Advances in Particle Swarm Optimization: Theory, Analysis, and Application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "", "Page": "100868", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 105516691, "Title": "ELF dynamic analysis tool for IoT systems with symbolic execution", "Abstract": "As a result of background work on analysis in embedded Linux OS, the authors created the ELF (embedded linux fuzzing) tool that provides functionality for use in conventional dynamic analysis tools working with IoT devices. The article discusses the use of full-system symbolic execution for the analysis of IoT systems based on Linux kernels, describes how to integrate S2E full-system symbolic execution frameworks into the ELF tool environment, as well as the possibility of applicability of the resulting toolchain to the implementation of distributed hybrid IoT fuzzing.", "Keywords": "fuzzing;symbolic execution;IoT-device;Linux;фаззинг;символьное выполнение;IoT-устройство", "DOI": "10.15514/ISPRAS-2022-34(4)-3", "PubYear": 2022, "Volume": "34", "Issue": "4", "JournalId": 9407, "JournalTitle": "Proceedings of the Institute for System Programming of the RAS", "ISSN": "2079-8156", "EISSN": "2220-6426", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}], "References": []}, {"ArticleId": 105516692, "Title": "FPGA-oriented lightweight multi-modal free-space detection network", "Abstract": "For autonomous vehicles, free-space detection is an essential part of visual perception. With the development of multi-modal convolutional neural networks (CNNs) in recent years, the performance of driving scene semantic segmentation algorithms has been dramatically improved. Therefore most free-space detection algorithms are developed based on multiple sensors. However, multi-modal CNNs have high data throughput and contain a large number of computationally intensive convolution calculations, limiting their feasibility for real-time applications. Field Programmable Gate Arrays (FPGAs) provide a unique combination of flexibility, performance, and low power for these problems to accommodate multi-modal data and the computational acceleration of different compression algorithms. Network lightweight methods offer great assurance for facilitating the deployment of CNNs on such resource-constrained devices. In this paper, we propose a network lightweight method for a multi-modal free-space detection algorithm. We first propose an FPGA-friendly multi-modal free-space detection lightweight network. It comprises operators that FPGA prefers and achieves a 95.54 % MaxF score on the test set of KITTI-Road free-space detection tasks and 81 ms runtime when running on 700 W GPU devices. Then we present a pruning approach for this network to reduce the number of parameters in case the complete model exceeds the FPGA chip memory. The pruning is in two parts. For the feature extractors, we propose a data-dependent filter pruner according to the principle that the low-rank feature map contains less information. To not compromise the integrity of the multi-modal information, the pruner is independent for each modality. For the segmentation decoder, we apply a channel pruning approach to remove redundant parameters. Finally, we implement our designs on an FPGA board using 8-bit quantisation, and the accelerator achieves outstanding performance. A real-time application of scene segmentation on KITTI-Road is used to evaluate our algorithm, and the model achieves a 94.39 % MaxF score and minimum 14 ms runtime on 20W FPGA devices.", "Keywords": "Pruning ; free-space detection ; lightweight network ; multi-modal learning ; FPGA", "DOI": "10.1080/09540091.2022.2159333", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 9414, "JournalTitle": "Connection Science", "ISSN": "0954-0091", "EISSN": "1360-0494", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Beijing RICH AI information Technology Co., Ltd, Beijing, People's Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Lu", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, People's Republic of China"}], "References": [{"Title": "Learning binary code for fast nearest subspace search", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107040", "JournalTitle": "Pattern Recognition"}, {"Title": "Explainable deep learning for efficient and robust pattern recognition: A survey of recent developments", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "108102", "JournalTitle": "Pattern Recognition"}, {"Title": "Uncertainty estimation for stereo matching based on evidential deep learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "108498", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 105516696, "Title": "Development of System for Collecting User-specified Training Data for Autonomous Driving Based on Virtual Road Environment", "Abstract": "Deep learning technologies that use road images to recognize autonomous driving environments have been actively developed. Such deep-learning-based autonomous driving technologies need a large amount of training data that can represent various road, traffic, and weather environments. However, there have been many difficulties in terms of time and cost in collecting training data that can represent various road environments. Therefore, in this study, we attempt to build a virtual road environment and develop a system for collecting training data based on the virtual environment. To build a virtual environment identical to the real world, we convert and use two kinds of existing geospatial data: high-definition 3D buildings and high-definition roads. We also develop a system for collecting training data running in the virtual environment. The implementation results of the proposed system show that it is possible to build a virtual environment identical to the real world and to collect specific training data quickly and at any time from the virtual environment with various user-specified settings. © 2022 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "autonomous driving; deep learning; high definition road; training data; virtual environment", "DOI": "10.18494/SAM3966", "PubYear": 2022, "Volume": "34", "Issue": "12", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Daejeon University, 62 Daehak-ro, Dong-gu, Daejeon, 34520, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Intelligent Convergence Research Laboratory, ETRI, 218 Gajeong-<PERSON>o, Yuseong-gu, Daejeon, 34129, South Korea"}], "References": []}, {"ArticleId": 105516746, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0950-7051(22)01321-1", "PubYear": 2023, "Volume": "260", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [], "References": []}, {"ArticleId": 105516759, "Title": "Hierarchically structured task-agnostic continual learning", "Abstract": "One notable weakness of current machine learning algorithms is the poor ability of models to solve new problems without forgetting previously acquired knowledge. The Continual Learning paradigm has emerged as a protocol to systematically investigate settings where the model sequentially observes samples generated by a series of tasks. In this work, we take a task-agnostic view of continual learning and develop a hierarchical information-theoretic optimality principle that facilitates a trade-off between learning and forgetting. We derive this principle from a Bayesian perspective and show its connections to previous approaches to continual learning. Based on this principle, we propose a neural network layer, called the Mixture-of-Variational-Experts layer, that alleviates forgetting by creating a set of information processing paths through the network which is governed by a gating policy. Equipped with a diverse and specialized set of parameters, each path can be regarded as a distinct sub-network that learns to solve tasks. To improve expert allocation, we introduce diversity objectives, which we evaluate in additional ablation studies. Importantly, our approach can operate in a task-agnostic way, i.e., it does not require task-specific knowledge, as is the case with many existing continual learning algorithms. Due to the general formulation based on generic utility functions, we can apply this optimality principle to a large variety of learning problems, including supervised learning, reinforcement learning, and generative modeling. We demonstrate the competitive performance of our method on continual reinforcement learning and variants of the MNIST, CIFAR-10, and CIFAR-100 datasets.", "Keywords": "Continual learning; Mixture-of-experts; Variational Bayes; Information theory", "DOI": "10.1007/s10994-022-06283-9", "PubYear": 2023, "Volume": "112", "Issue": "2", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Neural Information Processing, Ulm University, Ulm, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Neural Information Processing, Ulm University, Ulm, Germany"}], "References": [{"Title": "Towards Knowledgeable Supervised Lifelong Learning Systems", "Authors": "<PERSON>-<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "68", "Issue": "", "Page": "159", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Specialization in Hierarchical Learning Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "3", "Page": "2319", "JournalTitle": "Neural Processing Letters"}, {"Title": "Deep reinforcement learning: a survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "12", "Page": "1726", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}, {"Title": "A review on weight initialization strategies for neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "1", "Page": "291", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Target layer regularization for continual learning using Cramer-Wold distance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "609", "Issue": "", "Page": "1369", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105516802, "Title": "Wildfire Impact on Cerro Cora National Park Measured Using Landsat and Sentinel Images", "Abstract": "To prepare for and recover from damage due to wildfires, it is necessary to investigate and analyze the impacts of wildfires. To date, various wildfire research methodologies have been conducted and analyzed. The impact of fire tends to be difficult to generalize because it is closely related to many factors, such as the characteristics of the area and the seasonal distribution of vegetation. Therefore, it is necessary to conduct case studies considering the geographical, vegetational, and seasonal characteristics of the area for impact estimation. Quantitative and qualitative analyses of the wildfire that occurred in Cerro Cora National Park, Paraguay in August 2021 are needed to recover from the damage and to prepare for future wildfires. Therefore, in this study, we analyzed the impact of the wildfire in Cerro Cora National Park. To calculate the damaged area and severity, the normalized burn ratio and normalized difference vegetation index obtained from Landsat and Sentinel images were used. The tasseled cap transformation was also used to investigate the characteristic variation. To calibrate the seasonal factor, long-term analysis was performed, and short-term analysis was performed to determine the immediate impact of the wildfire. As a result, the damaged area, severity, and restoration for each area were estimated. In addition, characteristic variations of the area were identified. Furthermore, the reliability of the study was enhanced by comparing the results from each satellite image and index. © 2022 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "NBR; NDVI; tasseled cap transformation; wildfire damaged area; wildfire impact", "DOI": "10.18494/SAM3953", "PubYear": 2022, "Volume": "34", "Issue": "12", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Civil and Environmental System Engineering, Sungkyunkwan University, 2066 Seobu-ro, Jangan-gu, Suwon-si, Gyeonggi-do16419, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Land & Geo-spatial Consulting Group, Geomexsoft., Ltd., #103-306, 67 Seobinggo-ro, Yongsan-gu, Seoul, 04385, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Land & Geo-spatial Consulting Group, Geomexsoft., Ltd., #103-306, 67 Seobinggo-ro, Yongsan-gu, Seoul, 04385, South Korea"}], "References": []}, {"ArticleId": 105516847, "Title": "Discrete-time and continuous-time scheduling approaches for crane-assisted pipeless process plants", "Abstract": "In pipeless process plants that employ cranes to transfer material-containing-vessels between equipment, scheduling of cranes along with the equipment-operations on vessels is crucial from a productivity perspective. The workings of such crane-assisted pipeless process plants can differ from each other depending on the nature of their set-ups and product(s) produced. For the scheduling of such plants which currently lack substantive research in the literature, it is important to explore and develop general methods, and the current work is an endeavor in this direction. Mixed integer linear programming (MILP) based models are developed in this work using the discrete-time and precedence-based continuous-time approaches. While the modeling using the discrete-time approach is developed from scratch, that based on the continuous-time approach is a continuation from our prior work (<PERSON><PERSON><PERSON> et al., 2021). We compare the two approaches and draw generic model-agnostic insights about the aptness of the approaches to suit the practical requirements of implementation, through computational studies. Modeling based on the continuous-time approach is found to be favored from a computational perspective despite its inherent complexity in construction.", "Keywords": "", "DOI": "10.1016/j.compchemeng.2022.108130", "PubYear": 2023, "Volume": "170", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ABB Corporate Research, Bangalore, Karnataka 560048, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ABB Corporate Research, Bangalore, Karnataka 560048, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ABB Corporate Research, Bangalore, Karnataka 560048, India"}], "References": []}, {"ArticleId": 105516878, "Title": "Economical Inspection Methods Assisted by Unmanned Aerial Vehicle for Bridges in Korea", "Abstract": "Current infrastructure maintenance works face limitations for various reasons: insufficient budget, increasing number of infrastructure facilities requiring maintenance, shortage of labor, and rapidly increasing number of aged infrastructure facilities. To overcome these limitations, a new approach that is different from manual inspection methods under existing rules and regulations is required. In this context, we explored the efficiency of bridge inspection and maintenance using unmanned aerial vehicles (UAVs), which can observe inaccessible areas, be conveniently and easily controlled, and may offer high economic benefits. Various tests were performed on elevated bridges, and suitable UAV images were obtained. The obtained images were inspected using machine vision technology, thereby avoiding subjective evaluations by humans. We also discuss methods for enhancing the objectivity of inspections. Another aim of this study was to automate inspection work and improve work efficiency through computer vision technology. The UAV image analysis and classification technology in this study utilized existing computer vision technology, but the optimization process for each inspection item is described in detail so that it can be directly applied to the inspection task. This is to overcome limitations of current inspection tasks, which require the ability and experience of personnel. For this purpose, objectivity can be secured by optimizing the data acquisition and analysis process on a job-by-job basis. The test results showed that both the efficiency and objectivity of the proposed UAV-based method were superior to those of existing bridge maintenance and inspection methods. © 2022 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "bridge; crack detection; maintenance and inspection; UAV; water leak; white coating", "DOI": "10.18494/SAM3739", "PubYear": 2022, "Volume": "34", "Issue": "12", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Korea Institute of Civil Engineering and Building Technology, Goyang-si, 10223, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Korea Institute of Civil Engineering and Building Technology, Goyang-si, 10223, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Seoul Institute of Technology, Seoul-si, 03909, South Korea"}], "References": []}, {"ArticleId": 105516996, "Title": "Feedback precision and learners’ responses: A study into ETS Criterion automated corrective feedback in EFL writing classrooms", "Abstract": "This study examines the implementation of Criterion, an automated writing evaluation system developed by ETS, as a source of diagnostic feedback on learners’ linguistic performance in a Vietnamese EFL writing classroom. Thirty-eight second-year English majors had access to Criterion for a five-month period. Data include Criterion error tags on students’ essays from multiple practice sessions, recorded think-aloud protocols as students engaged with the feedback for revisions, and first and revised drafts students submitted to Criterion. The main findings indicate Criterion’s satisfactory precision and capacity to trigger various engagement strategies among learners, but reservations remain due to students’ modest response accuracy and lack of substantive revisions to their texts. Important implications for formative feedback practices in EFL writing classrooms and the adaptation of Criterion’s technical capacities are accordingly presented.", "Keywords": "", "DOI": "10.29140/jaltcall.v18n3.775", "PubYear": 2024, "Volume": "18", "Issue": "3", "JournalId": 67259, "JournalTitle": "The JALT CALL Journal", "ISSN": "", "EISSN": "1832-4215", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105517023, "Title": "An ADI orthogonal spline collocation method for a new two-dimensional distributed-order fractional integro-differential equation", "Abstract": "We consider an alternating direction implicit (ADI) scheme combined with an arbitrary-order orthogonal spline collocation (OSC) method in space for the numerical solution of the distributed-order fractional integro-differential equation. The weighted and shifted <PERSON><PERSON><PERSON><PERSON><PERSON> formula is employed to discretize the distributed-order time-fractional derivative combined with the second-order fractional quadrature convolution rule introduced by <PERSON><PERSON><PERSON> for the Riemann-Liouville fractional integral. We prove the stability of the proposed ADI OSC scheme and derive error estimates. Finally, some numerical tests are given.", "Keywords": "Distributed-order time-fractional derivative ; Riemann-<PERSON> fractional integral ; Alternating direction implicit method ; Orthogonal spline collocation method ; Stability and convergence", "DOI": "10.1016/j.camwa.2022.12.006", "PubYear": 2023, "Volume": "132", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hubei Key Laboratory of Power System Design and Test for Electrical Vehicle, Hubei University of Arts and Science, Xiangyang, Hubei 441053, PR China;School of Mathematics and Statistics, Hubei University of Arts and Science, Xiangyang, Hubei 441053, PR China;School of Mathematics and Computational Science, Xiangtan University, Xiangtan, Hunan 411105, PR China;Corresponding author at: Hubei Key Laboratory of Power System Design and Test for Electrical Vehicle, Hubei University of Arts and Science, Xiangyang, Hubei 441053, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Shanxi University, Taiyuan, Shanxi 030006, PR China;School of Mathematics and Statistics, Changsha University of Science and Technology, Changsha, Hunan 410114, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "MOE-LCSM, School of Mathematics and Statistics, Hunan Normal University, Hunan, Changsha 410081, PR China"}], "References": [{"Title": "Alternating direction implicit difference scheme for the multi-term time-fractional integro-differential equation with a weakly singular kernel", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "2", "Page": "244", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "A second-order ADI difference scheme based on non-uniform meshes for the three-dimensional nonlocal evolution problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "137", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 105517141, "Title": "An empirical investigation of the impact of influencer live-streaming ads in e-commerce platforms on consumers’ buying impulse", "Abstract": "Purpose E-commerce live streaming is a new influencer advertising method that allows influencers to interact directly with consumers on e-commerce platforms. Although evidence suggests that influencer live-streaming advertisements (ads) on social media can increase consumers’ buying impulses, little research examined how this similar but new advertising method on e-commerce platforms may influence consumers’ urge to buy impulsively. This study explores the role of influencer credibility, celebrity effect, perceived entertainment, trust and perceived usefulness on consumers’ attitudes toward influencer ads and their urge to buy impulsively. Design/methodology/approach A questionnaire containing seven constructs was developed and distributed to participants using a convenient sample and snowball sampling approach. The constructs were measured based on validated measurement items from the literature and adjusted according to this study’s focus. A total of 236 valid responses were obtained from the survey and used for data analysis. A partial least squares structural equation modeling approach was employed for parameter estimation and model testing. Findings The empirical results show that all constructs influenced consumers’ urge to buy impulsively via attitude toward influencer ads. The proposed research model explains 61.7% of the variance in attitude toward influencer ads and 19.4% of the urge to buy impulsively. Originality/value This is an early study investigating the relationship between influencer advertising and impulse buying. The results provide valuable insights into improving the design of influencer ads and marketing strategies. Highlights I-eIB model tests the mechanism of influencer ads on consumers’ buying impulse. Consumers’ attitude towards influencer ads affects their urge to buy impulsively. Influencer credibility affects consumer attitude via celebrity effect as a mediator. Trust affects consumer attitude via perceived usefulness as a mediator. Entertaining ads help develop favorable consumer attitude.", "Keywords": "Influencer Advertising;E-commerce live streaming;Impulse buying;Influencer credibility;Celebrity effect;Perceived entertainment;Trust;Perceived usefulness", "DOI": "10.1108/INTR-11-2020-0625", "PubYear": 2023, "Volume": "33", "Issue": "4", "JournalId": 20482, "JournalTitle": "Internet Research", "ISSN": "1066-2243", "EISSN": "2054-5657", "Authors": [{"AuthorId": 1, "Name": "Mia<PERSON>", "Affiliation": "School of Intelligent Systems Science and Engineering, Jinan University , Zhuhai, China GBA and B&R International Joint Research Center for Smart Logistics, Jinan University , Zhuhai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Applied Data Science, iFREE Group Innovation and Research Centre, Hong Kong Shue Yan University , Hong Kong, China School of Intelligent Systems Science and Engineering, Jinan University , Zhuhai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Advanced Design and Systems Engineering, City University of Hong Kong , Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Intelligent Systems Science and Engineering, Jinan University , Zhuhai, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Intelligent Systems Science and Engineering, Jinan University , Zhuhai, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Intelligent Systems Science and Engineering, Jinan University , Zhuhai, China"}], "References": [{"Title": "Enhancing consumer engagement in e-commerce live streaming via relational bonds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "1019", "JournalTitle": "Internet Research"}, {"Title": "The role of continuous trust in usage of online product recommendations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "4", "Page": "745", "JournalTitle": "Online Information Review"}, {"Title": "Do digital celebrities' relationships and social climate matter? Impulse buying in f-commerce", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "6", "Page": "1731", "JournalTitle": "Internet Research"}, {"Title": "Impulse Buying Behaviors in Live Streaming Commerce Based on the Stimulus-Organism-Response Framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "6", "Page": "241", "JournalTitle": "Information"}, {"Title": "Young adults’ motivations for following social influencers and their relationship to identification and buying behavior", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "124", "Issue": "", "Page": "106910", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Understanding the drivers of online trust and intention to buy on a website: An emerging market perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "1", "Page": "100065", "JournalTitle": "International Journal of Information Management Data Insights"}]}, {"ArticleId": 105517223, "Title": "A multi-agent system for FJSP with setup and transportation times", "Abstract": "Flexible job shops are quite common in manufacturing, where the machines can perform a variety of operations on a job. The setup and transportation times are quite significant in flexible job shops, which recently have attracted attention. This paper deals with the problem of scheduling of flexible job shops, termed FJSP, with setup and transportation times. Several metaheuristics approaches have been proposed for the problem assuming centralized decision-making. The centralized approaches, however, may not be suitable for large complex problems due to higher computational effort and slower convergence. The decentralized approaches are more suitable for such problems. To the best of authors’ knowledge, no work has been reported towards developing a decentralized approach for FJSP with setup and transportation times. To fill this gap a multi-agent system, a popular decentralized approach, is developed for FJSP with setup and transportation time. The performance of the proposed approach is compared with the three state-of-the-art (centralized) approaches by solving 20 problem instances. The proposed approach has been found to produce schedules with makespan lower (maximum of 35% and an average of 10.27%) than the comparison approaches. The algorithm has also been found to converge faster than the comparison algorithms.", "Keywords": "", "DOI": "10.1016/j.eswa.2022.119474", "PubYear": 2023, "Volume": "216", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, MNIT Jaipur, 302017, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, MNIT Jaipur, 302017, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, MNIT Jaipur, 302017, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering, MNIT Jaipur, 302017, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, MNIT Jaipur, 302017, India"}], "References": [{"Title": "An efficient evolutionary grey wolf optimizer for multi-objective flexible job shop scheduling problem with hierarchical job precedence constraints", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106280", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An improved genetic algorithm for the flexible job shop scheduling problem with multiple time constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100664", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Handling Sequence-dependent Setup Time Flexible Job Shop Problem with Learning and Deterioration Considerations using Evolutionary Bi-level Optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "6", "Page": "433", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "Improved particle swarm optimization algorithm based novel encoding and decoding schemes for flexible job shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "121", "Issue": "", "Page": "104951", "JournalTitle": "Computers & Operations Research"}, {"Title": "An improved Jaya algorithm for solving the flexible job shop scheduling problem with transportation and setup times", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "106032", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An improved artificial bee colony algorithm for solving multi-objective low-carbon flexible job shop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106544", "JournalTitle": "Applied Soft Computing"}, {"Title": "A hybrid many-objective evolutionary algorithm for flexible job-shop scheduling problem with transportation and setup times", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "132", "Issue": "", "Page": "105263", "JournalTitle": "Computers & Operations Research"}, {"Title": "Research on flexible job-shop scheduling problem in green sustainable manufacturing based on learning effect", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "6", "Page": "1725", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "An imperialist competitive algorithm with feedback for energy-efficient flexible job shop scheduling with transportation and sequence-dependent setup times", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "104307", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A hybrid Jaya algorithm for solving flexible job shop scheduling problem considering multiple critical paths", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "298", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 105517241, "Title": "Tube-based distributionally robust model predictive control for nonlinear process systems via linearization", "Abstract": "Model predictive control (MPC) is an effective approach to control multivariable dynamic systems with constraints. Most real dynamic models are however affected by plant-model mismatch and process uncertainties, which can lead to closed-loop performance deterioration and constraint violations. Methods such as stochastic MPC (SMPC) have been proposed to alleviate these problems; however, the resulting closed-loop state trajectory might still significantly violate the prescribed constraints if the real system deviates from the assumed disturbance distributions made during the controller design. In this work we propose a novel data-driven distributionally robust MPC scheme for nonlinear systems. Unlike SMPC, which requires the exact knowledge of the disturbance distribution, our scheme decides the control action with respect to the worst distribution from a distribution ambiguity set. This ambiguity set is defined as a Wasserstein ball centered at the empirical distribution. Due to the potential model errors that cause off-sets, the scheme is also extended by leveraging an offset-free method. The favorable results of this control scheme are demonstrated and empirically verified with a nonlinear mass spring system and a nonlinear CSTR case study.", "Keywords": "Model predictive control ; Stochastic optimal control ; Uncertain dynamic systems ; Distributionally robust optimization ; <PERSON><PERSON><PERSON> ambiguity set", "DOI": "10.1016/j.compchemeng.2022.108112", "PubYear": 2023, "Volume": "170", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Process Systems Engineering (CPSE), Department of Chemical Engineering, Imperial College London, UK"}, {"AuthorId": 2, "Name": "Ehecatl Antonio del Rio-Chanona", "Affiliation": "Centre for Process Systems Engineering (CPSE), Department of Chemical Engineering, Imperial College London, UK;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Process Systems Engineering (CPSE), Department of Chemical Engineering, Imperial College London, UK;Corresponding authors"}], "References": [{"Title": "Stochastic data-driven model predictive control using gaussian processes", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106844", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Industry engagement with control research: Perspective and messages", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "49", "Issue": "", "Page": "1", "JournalTitle": "Annual Reviews in Control"}, {"Title": "Stochastic MPC with Distributionally Robust Chance Constraints", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "7136", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Bayesian optimization with reference models: A case study in MPC for HVAC central plants", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "154", "Issue": "", "Page": "107491", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 105517299, "Title": "Optimasi Formula Tepung Komposit Tinggi Protein dan Seng dengan Response Surface Methodology", "Abstract": "<p>Covid-19 pandemic had limited people’s movement despite the demand to remain productive and maintain good health. Therefore, it was necessary to provide foods which are easy to distribute, durable, nutritious, and easily transformable to increase immunity. This research aims to develop a composite flour (TK) formula with optimal proportion of wheat flour (TT), snakehead fish flour (TIG), pumpkin flour (TBuL), and pumpkin seed flour (TBiL) which then enriched with zinc. The resulting TK is expected to be used as an ingredient for nutritious snacks containing high protein and zinc. The research was conducted in four stages, namely the making of each constituent flour, determining the best formulation with the Response Surface Methodology using Central Composite Design model, characterizing the physicochemical properties of TK and making meatballs, biscuits, and unting-unting from the TK. The three products were tested on experimental animals for their metabolic responses. The optimization of the formula resulted in three optimal formulations, namely formula A, B, and C with the proportion of TT:TIG:TBuL:TBiL respectively as follows 55:20:15:10; 56.65:20:13.35:10; 57.98:20:12.02:10. The most optimal formula of composite flour was formula A with the highest protein (26.12%) and zinc (18.06 mg/kg) content among other formulas. Then, zinc was added into Formula A using microencapsulation, and TK with protein content of 26.74% and zinc of 56.8 mg/kg were obtained. The histopatology observation on experimental animals showed that the three products made from TK did not cause necrosis of the liver or cell infiltration in the kidneys.</p>", "Keywords": "composite flour;covid 19;protein;zinc", "DOI": "10.6066/jtip.2022.33.2.119", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 36981, "JournalTitle": "Jurnal Teknologi dan Industri Pangan", "ISSN": "1979-7788", "EISSN": "2087-751X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Program Studi Biologi, Fakultas Teknobiologi, Universitas Surabaya, Surabaya, Indonesia"}, {"AuthorId": 2, "Name": "Ardhia Deasy <PERSON>", "Affiliation": "Program Studi Biologi, Fakultas Teknobiologi, Universitas Surabaya, Surabaya, Indonesia"}], "References": []}, {"ArticleId": 105517359, "Title": "Automatic dewarping of camera-captured comic document images", "Abstract": "<p>People often capture document images using the cameras attached to smart mobile phones. As a result, different types of distorted images gets generated. Warping is one of the major problems found on those distorted document images. Most existing techniques work based on the text lines present in the documents. On the other hand, comic documents contain fewer text lines. So, existing dewarping methods fail to perform with great accuracy in comic document images. Here, we propose a novel dewarping technique for warped comic document images. First, a simple mathematical model is proposed for warping generation in comic documents. Here, we show that warping depends on some factors. We estimate those factors from the boundaries of the panels present in a comic document image. Finally, based on those factors, we dewarp the document image. Unlike, the existing methods, the proposed approach can rectify warping in both horizontal and vertical direction. Nevertheless, the proposed approach can dewarp document images having multiple folds. We also evaluate the proposed approach, and the results are quite encouraging.</p>", "Keywords": "Comic document; Dewarping; Document image; Warped documents", "DOI": "10.1007/s11042-022-13234-y", "PubYear": 2023, "Volume": "82", "Issue": "1", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology, Delhi, New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Indian Institute of Engineering Science and Technology, Shibpur, Howrah, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Indian Institute of Engineering Science and Technology, Shibpur, Howrah, India"}], "References": [{"Title": "Geometric rectification of document images using adversarial gated unwarping network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107576", "JournalTitle": "Pattern Recognition"}, {"Title": "A theoretical justification of warping generation for dewarping using CNN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107621", "JournalTitle": "Pattern Recognition"}, {"Title": "A non-parametric binarization method based on ensemble of clustering algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Showmik Bhowmik", "PubYear": 2021, "Volume": "80", "Issue": "5", "Page": "7653", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "CNN-based segmentation of speech balloons and narrative text boxes from comic book page images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "1-2", "Page": "49", "JournalTitle": "International Journal on Document Analysis and Recognition"}, {"Title": "Dewarping of document images: A semi-CNN based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "28-29", "Page": "36009", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 105517366, "Title": "Cynicism as strength: Privacy cynicism, satisfaction and trust among social media users", "Abstract": "In this era of virtual communication, online user&#x27;s attitudes towards privacy have attracted the attention of social media researchers and practitioners as they continued to debate whether “online privacy” is an obsolete topic. While some scholars argued that individuals are deeply concerned about their online privacy, others have suggested that despite being cynical about the practises of social media companies, users were happy to disclose personal information in exchange for small rewards. There is consequently a gap between perceptions and practises when it comes to online privacy. Grounded on Expectation Confirmation Theory (ECM), we surveyed 475 social media users to help determine whether privacy cynicism is negatively related to satisfaction and trust in social media. Results indicated that privacy cynicism significantly affected users&#x27; satisfaction with social media. However, privacy cynicism did not significantly affect users&#x27; trust of social media. This research has important implications for post-technology adoption theories as well as for social media platforms, which would help users better understand public attitudes towards privacy and its impact on their satisfaction.", "Keywords": "", "DOI": "10.1016/j.chb.2022.107638", "PubYear": 2023, "Volume": "142", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Business, Government and Law, Canberra Business School, University of Canberra, Bruce Campus, Canberra, ACT, Australia"}, {"AuthorId": 2, "Name": "<PERSON> (M.I.) <PERSON><PERSON>", "Affiliation": "Faculty of Business, Government and Law, Canberra Business School, University of Canberra, Bruce Campus, Canberra, ACT, Australia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Marketing Jagannath University, Bangladesh;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Marketing Jagannath University, Bangladesh"}], "References": [{"Title": "Social comparison and continuance intention of smart fitness wearables: an extended expectation confirmation theory perspective", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "13", "Page": "1341", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "The Role of Privacy Cynicism in Consumer Habits with Voice Assistants: A Technology Acceptance Model Perspective", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "12", "Page": "1138", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Peer privacy protection motivation and action on social networking sites: Privacy self-efficacy and information security as moderators", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101176", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "“All apps do this”: Comparing Privacy Concerns Towards Privacy Tools and Non-Privacy Tools for Social Media Content", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "3", "Page": "57", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "Privacy concerns in consumer E-commerce activities and response to social media advertising: Empirical evidence from Europe", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "137", "Issue": "", "Page": "107412", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 105517376, "Title": "Migration to the metaverse and its predictors: Attachment to virtual places and metaverse-related threat", "Abstract": "The most ambitious visions of metaverse technology promise to create virtual places that offer the same possibilities as the real world. However, as any novel technology, the metaverse raises controversies and questions. Does one want to migrate to the metaverse? Does one&#x27;s willingness to move to virtual worlds depend on the bonds with existing virtual places and the sense of threat related to this technology? To address these questions, we drew on the theories of place attachment and intergroup threat. In two studies – (1) among users of open-world games ( N  = 366) and (2) using a sample representative of the Polish population in terms of age, gender and size of the residential place ( N  = 995) – we observed a low level of willingness to migrate to the metaverse. The participants displayed a high level of perceived metaverse-related threat, ranging from privacy concerns to the belief that metaverse can deprive one of access to essential human experiences. However, greater attachment to virtual, as opposed to real, places was associated with both an increased willingness to migrate to the metaverse and a low level of perceived threat. The results provide a better understanding of individuals&#x27; perception of the metaverse and of how the bonds with virtual and real places translate into attitudes towards metaverse technology.", "Keywords": "", "DOI": "10.1016/j.chb.2022.107642", "PubYear": 2023, "Volume": "141", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Psychology, University of Warsaw, <PERSON><PERSON>. <PERSON>aw<PERSON> 5/7, 00-183, Warsaw, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Psychology, University of Warsaw, Ul. Staw<PERSON> 5/7, 00-183, Warsaw, Poland"}, {"AuthorId": 3, "Name": "Małgorzata Piskorska", "Affiliation": "Faculty of Psychology, University of Warsaw, Ul. Staw<PERSON> 5/7, 00-183, Warsaw, Poland"}], "References": [{"Title": "Do women perceive sex robots as threatening? The role of political views and presenting the robot as a female-vs male-friendly product", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "106664", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 105517611, "Title": "Forward Hand Gesture Spotting and Prediction Using HMM-DNN Model", "Abstract": "<p>Automatic key gesture detection and recognition are difficult tasks in Human–Computer Interaction due to the need to spot the start and the end points of the gesture of interest. By integrating Hidden Markov Models (HMMs) and Deep Neural Networks (DNNs), the present research provides an autonomous technique that carries out hand gesture spotting and prediction simultaneously with no time delay. An HMM can be used to extract features, spot the meaning of gestures using a forward spotting mechanism with varying sliding window sizes, and then employ Deep Neural Networks to perform the recognition process. Therefore, a stochastic strategy for creating a non-gesture model using HMMs with no training data is suggested to accurately spot meaningful number gestures (0–9). The non-gesture model provides a confidence measure, which is utilized as an adaptive threshold to determine where meaningful gestures begin and stop in the input video stream. Furthermore, DNNs are extremely efficient and perform exceptionally well when it comes to real-time object detection. According to experimental results, the proposed method can successfully spot and predict significant motions with a reliability of 94.70%.</p>", "Keywords": "", "DOI": "10.3390/informatics10010001", "PubYear": 2023, "Volume": "10", "Issue": "1", "JournalId": 40001, "JournalTitle": "Informatics", "ISSN": "", "EISSN": "2227-9709", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, Faculty of Science, Tanta University, Tanta 31527, Egypt↑College of Computer Science and Engineering, Taibah University, Yanbu 966144, Saudi Arabia; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Taibah University, Yanbu 966144, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Faculty of Science, Tanta University, Tanta 31527, Egypt↑College of Computer Science and Engineering, Taibah University, Yanbu 966144, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Faculty of Science, Tanta University, Tanta 31527, Egypt↑College of Computer Science and Engineering, Taibah University, Yanbu 966144, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematics & Computer Science Department, Faculty of Science, Menoufiya University, Menoufia 32511, Egypt"}], "References": [{"Title": "Designing Mid-Air Haptic Gesture Controlled User Interfaces for Cars", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "EICS", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "A framework of human action recognition using length control features fusion and weighted entropy-variances based feature selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104090", "JournalTitle": "Image and Vision Computing"}, {"Title": "Multiple Kinect based system to monitor and analyze key performance indicators of physical training", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Human-centric Computing and Information Sciences"}, {"Title": "Detection of sitting posture using hierarchical image composition and deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Development and validation of a Brazilian sign language database for human gesture recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "16", "Page": "10449", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 105517643, "Title": "Deep Learning Test Optimization Method Using Multi-objective Optimization", "Abstract": "", "Keywords": "", "DOI": "10.21655/ijsi.1673-7288.00282", "PubYear": 2022, "Volume": "12", "Issue": "4", "JournalId": 87794, "JournalTitle": "International Journal of Software and Informatics", "ISSN": "1673-7288", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Yanzhou Mu", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin 300350, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105517691, "Title": "Leveraging feature-level fusion representations and attentional bidirectional RNN-CNN deep models for Arabic affect analysis on Twitter", "Abstract": "Arabic affect analysis on Twitter avidly helps to capture the emotional states of individuals being expressed regarding many targets, such as world-level events, products, and services. It is the key to monitoring and advancing human intelligence, which impacts human decision-making processes efficiently. However, state-of-the-art models have not witnessed serious developments yet since they have just achieved an accuracy of around 54%. This inaccuracy is mainly due to the agglutination, dialectal variation, and morphological richness of the Arabic language, as well as the unique features of tweets, such as shortness, noisiness, and informal language. This paper presents an approach that tackles these challenges and then improves the performance of Arabic affect analysis on Twitter. First, we propose a novel feature-based fusion representation for Arabic tweets to capture the polysemy, semantic/syntactic information, and conveyed emotional knowledge; and to deal with Out-Of-Vocabulary (OOV) words. Second, we propose an attentional deep learning model based on Bidirectional Gated Recurrent Unit (BiGRU), Bidirectional Long Short-Term Memory (BiLSTM), and Convolution Neural Network (CNN) to effectively learn local and global features and provide a multilabel emotional classification. The experimental results indicate that our proposal outperforms twelve state-of-the-art and baseline methods with a significant improvement of 6% in accuracy.", "Keywords": "Emotional analysis ; Feature-level fusion ; Deep learning ; Attention mechanism ; Multilabel classification ; Arabic language", "DOI": "10.1016/j.jksuci.2022.12.015", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, LISAC Laboratory, Faculty of Sciences Dhar EL <PERSON>, <PERSON><PERSON> University, Fez, Morocco;Corresponding author"}, {"AuthorId": 2, "Name": "El Habib Nfaoui", "Affiliation": "Computer Science Department, LISAC Laboratory, Faculty of Sciences Dhar EL Mahraz, <PERSON><PERSON>h University, Fez, Morocco"}], "References": [{"Title": "A review of sentiment analysis research in Arabic language", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "408", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Transformer based Deep Intelligent Contextual Embedding for Twitter sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "113", "Issue": "", "Page": "58", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Attention-based sentiment analysis using convolutional and recurrent neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "113", "Issue": "", "Page": "571", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "ABCDM: An Attention-based Bidirectional CNN-RNN Deep Model for sentiment analysis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "279", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Affect detection from arabic tweets using ensemble and deep learning techniques", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "2529", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "A new topic modeling based approach for aspect extraction in aspect based sentiment analysis: SS-LDA", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114231", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-label Arabic text classification in Online Social Networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "101785", "JournalTitle": "Information Systems"}, {"Title": "A comprehensive survey on sentiment analysis: Approaches, challenges and trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "107134", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Knowledge-enabled BERT for aspect-based sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107220", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A review on sentiment analysis and emotion detection from text", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "81", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "A survey on deep learning for textual emotion analysis in social networks", "Authors": "<PERSON><PERSON> Pen<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "5", "Page": "745", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Deep learning for emotion analysis in Arabic tweets", "Authors": "<PERSON><PERSON>; <PERSON>as M. <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "Aspect-based sentiment analysis via affective knowledge enhanced graph convolutional networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107643", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Integrating Character-level and Word-level Representation for Affect in Arabic Tweets", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "138", "Issue": "", "Page": "101973", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "A survey on sentiment analysis methods, applications, and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "7", "Page": "5731", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A systematic review on affective computing: emotion models, databases, and recent advances", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "83-84", "Issue": "", "Page": "19", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 105517693, "Title": "Local generalization and bucketization technique for personalized privacy preservation", "Abstract": "Anonymization technique has been extensively studied and widely applied for privacy-preserving data publishing. In most previous approaches, a microdata table consists of three categories of attributes, namely explicit-identifier, quasi-identifier (QI), and sensitive attribute. In general, individuals may have different views on the sensitivity of different attributes. Therefore, there is another type of attribute that contains both QI values and sensitive values, termed semi-sensitive attribute. In this paper, we propose a new anonymization technique, called Local Generalization and Bucketization, to prevent identity disclosure and protect the sensitive values on each semi-sensitive attribute and sensitive attribute. The rationale is to use local generalization and local bucketization to divide the tuples into local equivalence groups and partition the sensitive values into local buckets, respectively. The protections of local generalization and local bucketization are independent, so that they can be implemented by appropriate algorithms without weakening other protection. Besides, the protection of local bucketization for each semi-sensitive attribute and sensitive attribute is also independent. Consequently, local bucketization can comply with various principles in different attributes according to the actual requirements of anonymization. We conducted extensive experiments to illustrate the effectiveness of the proposed approach.", "Keywords": "Privacy preservation ; Data publication ; Local generalization ; Local bucketization", "DOI": "10.1016/j.jksuci.2022.12.008", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, China;Corresponding author"}], "References": []}, {"ArticleId": 105517697, "Title": "A robust detection and localization technique for copy-move forgery in digital images", "Abstract": "Image forensic analysis becomes a major role in the field of digital image security due to tampering and forgery. The image forgery violates the authenticity and ownership of digital images. Copy-move forgery considers a significant kind of image forensic analysis algorithm. In this kind, the forger copies a part of an original image and then pastes it into the selected position from the same image. The purpose of forgery is to hide or highlight a specific region of the original image. To detect copy-move forgery, there are two traditional techniques: block-based and keypoint-based. The main drawback of the keypoint-based technique is the insufficient features for the small and flat regions, which causes undetected forgery. In contrast, the block-based technique has intensive processing. Therefore, this paper proposes a robust scheme that overcomes the drawbacks of the above techniques and maintains their advantages. This scheme adopts three connected stages, the first detects the initial duplicated regions using the SURF-HOG detector and descriptor. Subsequently, the second stage localizes the primary matched regions by SLIC segmentation and then selects the suspicious neighbor regions to be combined with primary regions to obtain the active regions. In the third stage, the block-based technique adopts overlapping Zernike moments to extract sufficient key points from the produced active regions. In the final stage, the duplicated regions are classified into authentic or forged regions. The proposed scheme provides not only forgery detection but also localization and recognition for the duplicated regions. The experimental results show that the proposed scheme is fast and has high accuracy for forgery detection and localization, at least 93.75, and 7.25 in terms of True Positive and False Positive Rates. Moreover, the scheme has high robustness under various conditions and attacks such as geometric transformation attacks and compound photometric attacks. The proposed scheme can be used in sensitive applications such as cybercrime detection and adopted as evidence in the courts.", "Keywords": "Copy Move Forgery Detection (CMFD) ; Speed up Robust Feature (SURF) ; RGMS ; Gray Level Co-occurrence Matrix (GLCM) ; Zernike moments", "DOI": "10.1016/j.jksuci.2022.12.014", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Education for Human Sciences, University of Kerbala, Kerbala, Iraq"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering, Ferdowsi University of Mashhad, Mashhad, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering, University of Kerbala, Kerbala, Iraq"}], "References": [{"Title": "A passive forensic scheme for copy-move forgery based on superpixel segmentation and K-means clustering", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "Page": "477", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Tampering detection using hybrid local and global features in wavelet-transformed space with digital images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "7", "Page": "5427", "JournalTitle": "Soft Computing"}, {"Title": "Copy-move forgery detection using image blobs and BRISK feature", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "35-36", "Page": "26045", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Ciratefi based copy move forgery detection on digital images", "Authors": "<PERSON><PERSON> Tahaoglu; <PERSON><PERSON><PERSON>; Beste Ustubioglu", "PubYear": 2022, "Volume": "81", "Issue": "16", "Page": "22867", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A comprehensive survey of image and video forgery techniques: variants, challenges, and future directions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "3", "Page": "939", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": *********, "Title": "Construction of a Material Point Motion Basic Law and the Program Control Implementing With Phase Constraints", "Abstract": "The article presents a solved problem about a material point motion basic kinematic law in a homogeneousgravity field, taking into account the resistance of the medium, which proportional to the velocity in the second degree. A point bypasses the specified restricted zones and does not leave the specified vertical strip above the earth's surface during the flight according to proposed law. A program control has been developed that implements a point motion proposed law. Differential equations describing the dynamics of the basic motion perturbations are compiled in the article.", "Keywords": "кинематический закон движения;базовая траектория;фазовые ограничения;программное управление;задача Коши;динамика возмущений", "DOI": "10.17072/1993-0550-2022-3-25-37", "PubYear": 2022, "Volume": "", "Issue": "3(58)", "JournalId": 77295, "JournalTitle": "Вестник Пермского университета. Математика. Механика. Информатика", "ISSN": "1993-0550", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Perm State Univesity"}], "References": []}, {"ArticleId": 105517890, "Title": "Self-supervised semi-supervised nonnegative matrix factorization for data clustering", "Abstract": "Semi-supervised nonnegative matrix factorization exploits the strengths of matrix factorization in successfully learning part-based representation and is also able to achieve high learning performance when facing a scarcity of labeled data and a large amount of unlabeled data. Its major challenge lies in how to learn more discriminative representations from limited labeled data. Furthermore, self-supervised learning has been proven very effective at learning representations from unlabeled data in various learning tasks. Recent research works focus on utilizing the capacity of self-supervised learning to enhance semi-supervised learning. In this paper, we design an effective Self-Supervised Semi-Supervised Nonnegative Matrix Factorization (S<sup>4</sup>NMF) in a semi-supervised clustering setting. The S<sup>4</sup>NMF directly extracts a consensus result from ensembled NMFs with similarity and dissimilarity regularizations. In an iterative process, this self-supervisory information will be fed back to the proposed model to boost semi-supervised learning and form more distinct clusters. The proposed iterative algorithm is used to solve the given problem, which is defined as an optimization problem with a well-formulated objective function. In addition, the theoretical and empirical analyses investigate the convergence of the proposed optimization algorithm. To demonstrate the effectiveness of the proposed model in semi-supervised clustering, we conduct extensive experiments on standard benchmark datasets. The source code for reproducing our results can be found at https://github.com/ChavoshiNejad/S4NMF .", "Keywords": "", "DOI": "10.1016/j.patcog.2022.109282", "PubYear": 2023, "Volume": "137", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Kurdistan, Sanandaj, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Kurdistan, Sanandaj, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Kurdistan, Sanandaj, Iran;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Kurdistan, Sanandaj, Iran"}], "References": [{"Title": "Robust semi-supervised nonnegative matrix factorization for image clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107683", "JournalTitle": "Pattern Recognition"}, {"Title": "Discriminative semi-supervised non-negative matrix factorization for data clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "104289", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 105517912, "Title": "Integrasi Metode Kano dan <PERSON> dalam Evaluasi Sen<PERSON>i <PERSON>lat Instan Komersial", "Abstract": "<p>Sensory attribute is one of quality parameter that affects buying intention of consumer for commercial instant chocolate drink product. Evaluation of consumer liking using hedonic method or ideal profile method was not enough to fulfill consumer satisfaction. This research aims to identify sensory attributes which are able to give consumer satisfaction using survey method integrated with structural equation model (SEM), Kano method, and total unduplicated reach and frequency (TURF). Kano method is used to evaluate sensory attributes based on consumer satisfaction, while TURF is used to identify sensory component which is able to increase buying intention of consumers with regard to the range and frequency for chocolate instant commercial drink. Preliminary research conducted by modelling consumer preferences suggested that there was an influence of 0.76 sensory attributes to consumer satisfaction. Seven samples were purchased from market and evaluated by 30 untrained panelists using home use test with an approach of consumer habit to consume hot chocolate drink once a week. The panelists were chosen from people who consume commercial instant chocolate drink. Results of this research showed that sensory attributes creamy (texture) and dark chocolate (color) were attractive features which were able to improve consumer satisfaction. Atribute sweet (taste) was categorized as must-be features, while flavor, aroma, and color were classified as one-dimensional features. Combination of attributes in one category can improve the range to reach the targeted consumers and frequency of consumers towards the product, for example attribute creamy can reach 97%, but combination of attributes creamy and thickness can reach 100% of the targeted consumers.</p>", "Keywords": "chocolate drink;Kano;sensory evaluation;structural equation model (SEM);total unduplicated reach and frequency (TURF)", "DOI": "10.6066/jtip.2022.33.2.137", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 36981, "JournalTitle": "Jurnal Teknologi dan Industri Pangan", "ISSN": "1979-7788", "EISSN": "2087-751X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Master <PERSON>, <PERSON><PERSON><PERSON>, IPB University, Bogor, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}], "References": []}, {"ArticleId": 105517932, "Title": "A spatial superstructure approach to the optimal design of modular processes and supply chains", "Abstract": "Modularity is a design principle that aims to provide flexibility for spatio-temporal assembly/disassembly and reconfiguration of systems. This design principle can be applied to multiscale (hierarchical) manufacturing systems that connect units, processes, facilities, and entire supply chains. Designing modular systems is challenging because of the need to capture spatial interdependencies that arise between system components due to product exchange/transport between components and due to product transformation in such components. In this work, we propose an optimization framework to facilitate the design of modular manufacturing systems. Central to our approach is the concept of a spatial superstructure, which is a graph that captures all possible system configurations and interdependencies between components. The spatial superstructure is a generalization of the notion of a superstructure and of a p-graph used in process design, in that it encodes spatial (geographical) context of the system components. We show that this generalization facilitates the simultaneous design and analysis of processes, facilities, and of supply chains. Our framework aims to select the system topology from the spatial superstructure that minimizes design cost and that maximizes design modularity. We show that this design problem can be cast as a mixed-integer, multi-objective optimization formulation. We demonstrate these capabilities using a case study arising in the design of a plastic waste upcycling supply chain.", "Keywords": "", "DOI": "10.1016/j.compchemeng.2022.108102", "PubYear": 2023, "Volume": "170", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical and Biological Engineering, University of Wisconsin-Madison, 1415 Engineering Dr., Madison, WI 53706, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Ma", "Affiliation": "Department of Chemical and Biological Engineering, University of Wisconsin-Madison, 1415 Engineering Dr., Madison, WI 53706, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemical and Biological Engineering, University of Wisconsin-Madison, 1415 Engineering Dr., Madison, WI 53706, USA;Corresponding author"}], "References": [{"Title": "A framework for supply chain optimization for modular manufacturing with production feasibility analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "107175", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Production Scheduling of Supply Chains Comprised of Modular Production Units", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Efstratios <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "11452", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Biomass waste-to-energy supply chain optimization with mobile production modules", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "150", "Issue": "", "Page": "107326", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 105518026, "Title": "Global convergence towards statistical independence for noisy mixtures of stationary and non-stationary signals", "Abstract": "<p>This article deals with the problem of blind separation of statistically independent sources from the instantaneous linear model ( n × n ). When the observation signals are affected by the additive white gaussian noise (AWGN), the implementation of the proposed solution is performed by following three steps. The first step is a whitening process. The second step aims to convert the uncorrelated signals into statistically independent signals. The last step consists in reducing the noise existing in the noisy estimations. The main part of the proposed solution is to determine the adequate rotating angle ( θ ) that maximizes the kurtosis of the whitened signals. This rotating angle is obtained through the use of optimization techniques by applying a genetic algorithm. The proposed solution has the advantage of not converging to a local maximum, and also the separation method can be easily generalized to converge directly towards the global maximum for the case of several sources. The results obtained by applying many simulations, prove the effectiveness and the performance of the proposed method even in the noisy case and whatever the type of the signals (stationary or non-stationary).</p>", "Keywords": "PCA; ICA; Kurtosis maximization; Genetic optimization; Wavelet denoising", "DOI": "10.1007/s41870-022-01146-x", "PubYear": 2023, "Volume": "15", "Issue": "2", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "El Mouataz Billah S<PERSON>ti", "Affiliation": "Department of Electronics, University of Batna 2, Batna, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics, University of Batna 2, Batna, Algeria"}], "References": [{"Title": "A t-SNE based non linear dimension reduction for network intrusion detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "1", "Page": "125", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Efficient mixture control chart pattern recognition using adaptive RBF neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "1271", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Blind source separation using kurtosis, negentropy and maximum likelihood functions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "1", "Page": "13", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Complex environment perception and positioning based visual information retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "2", "Page": "409", "JournalTitle": "International Journal of Information Technology"}, {"Title": "A fuzzy based local minima avoidance path planning in autonomous robots", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "1", "Page": "33", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 105518065, "Title": "Modeling methods and the degree of parameter uncertainty in probabilistic analyses of economic evaluations", "Abstract": "<p>There is a framework providing some general guidance to interpret the levels of parameter uncertainty of the probabilistic analysis in economic evaluations. Given that this framework may not fully address underlying causes for the uncertainty, we sought to extend it for two specific scenarios. We provided the mathematical interpretations and conducted simulation studies for two scenarios. The first study examined a case where the intervention and control strategies were associated with different health states (e.g., active surveillance versus surgery for treatment of non-invasive cancer). The second study evaluated the quality-adjusted life-years (QALYs), estimated from reported summary statistics (i.e., mean and standard deviation) of longitudinal post-treatment utility data from a clinical trial. The first simulation study showed that the magnitude of uncertainty of cost-effectiveness results was much greater if a decision model considered different health states for the intervention and control strategies than if the model considered the same health states. The second study showed that variance in the estimates of QALYs and incremental QALYs using the summary statistics was substantially underestimated when the correlations of repeated measures which are generally not available in the literature were omitted. We further discussed the implications of our findings for the economic modeling. In addition to qualitative categorization of uncertainty proposed by the general framework to assist with decision-making, we also need to comprehend methods used to address uncertainty in economic evaluations to enable informed policy making.</p>", "Keywords": "Probabilistic analysis; Economic modeling; Parameter uncertainty; Health state utilities; Area under utility curve", "DOI": "10.1007/s13721-022-00404-z", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25861, "JournalTitle": "Network Modeling Analysis in Health Informatics and Bioinformatics", "ISSN": "2192-6662", "EISSN": "2192-6670", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Health Technology Assessment Program, Ontario Health, Toronto, Canada"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": "Health Technology Assessment Program, Ontario Health, Toronto, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Program of Child Health Evaluative Sciences, The Hospital for Sick Children Research Institute, Toronto, Canada; Institute of Health Policy, Management and Evaluation, University of Toronto, Toronto, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, University of Regina, Regina, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Health Technology Assessment Program, Ontario Health, Toronto, Canada"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Health Technology Assessment Program, Ontario Health, Toronto, Canada"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Statistics, University of Regina, Regina, Canada"}], "References": []}, {"ArticleId": 105518104, "Title": "Group decision making based on entropy measure of Pythagorean fuzzy sets and Pythagorean fuzzy weighted arithmetic mean aggregation operator of Pythagorean fuzzy numbers", "Abstract": "In this paper, we propose a new entropy measure of Pythagorean fuzzy sets (PFSs). The proposed entropy measure of PFSs can conquer the shortcomings of the existing entropy measure of PFSs. We also propose the Pythagorean fuzzy weighted arithmetic mean (PFWAM) aggregation operator (AO) of Pythagorean fuzzy numbers (PFNs). The proposed PFWAM AO of PFNs can conquer the shortcomings of the existing sine trignometry Pythagorean fuzzy weighted averaging (ST-PFWA) AO and the existing sine trignometry Pythagorean fuzzy weighted geometric (ST-PFWG) AO of PFNs. Based on the proposed entropy measure of PFSs and the proposed PFWAM AO of PFNs, we propose a new group decision making (GDM) approach in the environment of PFNs. The proposed GDM approach can conquer the shortcomings of existing GDM approaches, where they cannot distinguish the ranking orders (ROs) of alternatives in some conditions. It offers us a very useful approach to deal with GDM problems in the environment of PFNs.", "Keywords": "", "DOI": "10.1016/j.ins.2022.12.064", "PubYear": 2023, "Volume": "624", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Amity University Haryana, Gurugram, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National Taiwan University of Science and Technology, Taipei, Taiwan;Corresponding author"}], "References": [{"Title": "Arithmetic operations on normal semi elliptic intuitionistic fuzzy numbers and their application in decision-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "1", "Page": "163", "JournalTitle": "Granular Computing"}, {"Title": "Multi-criteria group decision making based on ELECTRE I method in Pythagorean fuzzy information", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "5", "Page": "3425", "JournalTitle": "Soft Computing"}, {"Title": "A series of generalized induced Einstein aggregation operators and their application to group decision-making process based on Pythagorean fuzzy numbers", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "2", "Page": "241", "JournalTitle": "Granular Computing"}, {"Title": "Group decision-making approach under multi (Q, N)-soft multi granulation rough model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "2", "Page": "339", "JournalTitle": "Granular Computing"}, {"Title": "Aggregation operators on cubic linguistic hesitant fuzzy numbers and their application in group decision-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "2", "Page": "303", "JournalTitle": "Granular Computing"}, {"Title": "Neutrality operations-based Pythagorean fuzzy aggregation operators and its applications to multiple attribute group decision-making process", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "7", "Page": "3021", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Multiple attribute group decision making based on weighted aggregation operators of triangular neutrosophic cubic fuzzy numbers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "2", "Page": "421", "JournalTitle": "Granular Computing"}, {"Title": "Group decision making with heterogeneous intuitionistic fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "523", "Issue": "", "Page": "197", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making based on acceptable multiplicative consistency of hesitant fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "524", "Issue": "", "Page": "77", "JournalTitle": "Information Sciences"}, {"Title": "Picture fuzzy Choquet integral-based VIKOR for multicriteria group decision-making problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "3", "Page": "587", "JournalTitle": "Granular Computing"}, {"Title": "Generalized intuitionistic fuzzy aggregation operators based on confidence levels for group decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "867", "JournalTitle": "Granular Computing"}, {"Title": "Induced generalized pythagorean fuzzy aggregation operators and their application based on t-norm and t-conorm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "887", "JournalTitle": "Granular Computing"}, {"Title": "Group decision making with incomplete q-rung orthopair fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "553", "Issue": "", "Page": "376", "JournalTitle": "Information Sciences"}, {"Title": "Multiattribute group decision making based on interval-valued neutrosophic N-soft sets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "1009", "JournalTitle": "Granular Computing"}, {"Title": "Optimization-based group decision making using interval-valued intuitionistic fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "561", "Issue": "", "Page": "352", "JournalTitle": "Information Sciences"}, {"Title": "Multi-attribute group decision making based on sine trigonometric spherical fuzzy aggregation operators", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "1", "Page": "141", "JournalTitle": "Granular Computing"}, {"Title": "Sine trigonometric operational laws and its based Pythagorean fuzzy aggregation operators for group decision-making process", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "4421", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Group decision making based on consistency and consensus analysis of dual multiplicative linguistic preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "572", "Issue": "", "Page": "590", "JournalTitle": "Information Sciences"}, {"Title": "Group decision based on trapezoidal neutrosophic Dombi fuzzy hybrid operator", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "2", "Page": "305", "JournalTitle": "Granular Computing"}, {"Title": "Group decision making based on multiplicative consistency-and-consensus preference analysis for incomplete q-rung orthopair fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "653", "JournalTitle": "Information Sciences"}, {"Title": "A framework for group decision making with multiplicative trapezoidal fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "577", "Issue": "", "Page": "722", "JournalTitle": "Information Sciences"}, {"Title": "Weighted average LINMAP group decision-making method based on q-rung orthopair triangular fuzzy numbers", "Authors": "<PERSON><PERSON> Wan; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "3", "Page": "489", "JournalTitle": "Granular Computing"}, {"Title": "Multiple attribute group decision-making based on generalized aggregation operators under linguistic interval-valued Pythagorean fuzzy environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "3", "Page": "591", "JournalTitle": "Granular Computing"}, {"Title": "Multiple attribute group decision making based on advanced linguistic intuitionistic fuzzy weighted averaging aggregation operator of linguistic intuitionistic fuzzy numbers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "587", "Issue": "", "Page": "813", "JournalTitle": "Information Sciences"}, {"Title": "Signed distance-based closeness coefficients approach for solving inverse non-linear programming models for multiple criteria group decision-making using interval Type-2 pythagorean fuzzy numbers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "4", "Page": "881", "JournalTitle": "Granular Computing"}, {"Title": "Group decision making based on q-rung orthopair fuzzy weighted averaging aggregation operator of q-rung orthopair fuzzy numbers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "598", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making based on multiplicative consistency and consensus of Pythagorean fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "601", "Issue": "", "Page": "340", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making based on advanced intuitionistic fuzzy weighted Heronian mean aggregation operator of intuitionistic fuzzy values", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "601", "Issue": "", "Page": "306", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making based on improved linguistic interval-valued Atanassov intuitionistic fuzzy weighted averaging aggregation operator of linguistic interval-valued Atanassov intuitionistic fuzzy numbers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "884", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making based on weighted distance measure of linguistic intuitionistic fuzzy sets and the TOPSIS method", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "611", "Issue": "", "Page": "660", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105518175, "Title": "On the Complexity of Binary Polynomial Optimization Over Acyclic Hypergraphs", "Abstract": "In this work, we advance the understanding of the fundamental limits of computation for binary polynomial optimization (BPO), which is the problem of maximizing a given polynomial function over all binary points. In our main result we provide a novel class of BPO that can be solved efficiently both from a theoretical and computational perspective. In fact, we give a strongly polynomial-time algorithm for instances whose corresponding hypergraph is $$\\beta $$ \n β \n -acyclic. We note that the $$\\beta $$ \n β \n -acyclicity assumption is natural in several applications including relational database schemes and the lifted multicut problem on trees. Due to the novelty of our proving technique, we obtain an algorithm which is interesting also from a practical viewpoint. This is because our algorithm is very simple to implement and the running time is a polynomial of very low degree in the number of nodes and edges of the hypergraph. Our result completely settles the computational complexity of BPO over acyclic hypergraphs, since the problem is NP-hard on $$\\alpha $$ \n α \n -acyclic instances. Our algorithm can also be applied to any general BPO problem that contains $$\\beta $$ \n β \n -cycles. For these problems, the algorithm returns a smaller instance together with a rule to extend any optimal solution of the smaller instance to an optimal solution of the original instance.", "Keywords": "Binary polynomial optimization; Strongly polynomial-time algorithm; Acyclic hypergraphs; Hardness of approximation", "DOI": "10.1007/s00453-022-01086-9", "PubYear": 2023, "Volume": "85", "Issue": "8", "JournalId": 2779, "JournalTitle": "Algorithmica", "ISSN": "0178-4617", "EISSN": "1432-0541", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, University of Wisconsin-Madison, Madison, USA; Wisconsin Institute for Discovery, University of Wisconsin-Madison, Madison, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science, TU Dresden, Dresden, Germany"}], "References": []}, {"ArticleId": 105518224, "Title": "A Comparative Analysis of Sentence Embedding Techniques for Document Ranking", "Abstract": "<p>Due to the exponential increase in the information on the web, extracting relevant documents for users in a reasonable time becomes a cumbersome task. Also, when user feedback is scarce or unavailable, content-based approaches to extract and rank relevant documents are critical as they suffer from the problem of determining semantic similarity between texts of user queries and documents. Various sentence embedding models exist today that acquire deep semantic representations through training on a large corpus, with the goal of providing transfer learning to a broad range of natural language processing tasks such as document similarity, text summarization, text classification, sentiment analysis, etc. So, in this paper, a comparative analysis of six pre-trained sentence embedding techniques has been done to identify the best model suited for document ranking in IR systems. These are SentenceBERT, Universal Sentence Encoder, InferSent, ELMo, XLNet, and Doc2Vec. Four standard datasets CACM, CISI, ADI, and Medline are used to perform all the experiments. It is found that Universal Sentence Encoder and SentenceBERT outperform other techniques on all four datasets in terms of MAP, recall, F-measure, and NDCG. This comparative analysis offers a synthesis of existing work as a single point of entry for practitioners who seek to use pre-trained sentence embedding models for document ranking and for scholars who wish to undertake work in a similar domain. The work can be expanded in many directions in the future as various researchers can combine these strategies to build a hybrid document ranking system or query reformulation system in IR.</p>", "Keywords": "BERT; cosine similarity; document ranking; information retrieval; sentence embedding", "DOI": "10.13052/jwe1540-9589.2177", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 58533, "JournalTitle": "Journal of Web Engineering", "ISSN": "1540-9589", "EISSN": "1544-5976", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "J<PERSON>C. Bose University of Science & Technology, YMCA, Haryana, Faridabad, India; MMEC, MM(DU), Haryana Mullana, Ambala, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "J.C. Bose University of Science & Technology, YMCA, Haryana, Faridabad, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "J.C. Bose University of Science & Technology, YMCA, Haryana, Faridabad, India"}], "References": []}, {"ArticleId": 105518276, "Title": "A Pontryagin Maximum Principle Analogue in the Optimal Control Problem of a Differential Equations System with a Fractional Caputo Derivative and a Multipoint Quality Criterion", "Abstract": "The processes optimal control problem described by a ordinary differential equations system with fractional order is considered. The quality criterion is a multipoint nonlinear functional. A quality functional increment formula is constructed by introducing a conjugate system in thefractional integral equation form such as Volterra. The necessary optimality condition is proved in the Pontryagin maximum principle analogue form by the constructed formula investigating with using the Mc<PERSON>hane needle variation.", "Keywords": "допустимое управление;производная дробного порядка;сопряженная система;принцип максимума;условие оптимальности;многоточечный функционал", "DOI": "10.17072/1993-0550-2022-3-5-10", "PubYear": 2022, "Volume": "", "Issue": "3(58)", "JournalId": 77295, "JournalTitle": "Вестник Пермского университета. Математика. Механика. Информатика", "ISSN": "1993-0550", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Baku State University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Control Systems, Azerbaijan National Academy of Sciences"}], "References": []}, {"ArticleId": 105518317, "Title": "A Defensive Framework for Reflected XSS in Client-Side Applications", "Abstract": "<p>Cross-site scripting attack (XSS) is a common vulnerability that is exploited in modern web applications by entering advanced HTML tags and Java Script functions. An attacker could potentially use this vulnerability to steal users’ sensitive information, hijack user sessions or rewrite whole website contents displaying fake login forms. This class of attacks affects the client-side of a web application and is a critical vulnerability that is difficult to both detect and remediate for websites, often leading to insufficient server-side protection, which is why the end-users need an extra layer of protection at the client-side. In this paper, we analyze the best-known client-side XSS filters, study their mechanisms, structures and mentioned the advantages and disadvantages of each filter. This paper presents a novel XSS filtering model based on filtering rules, XSSFilter, uses Regular Expression in Xpath to detect reflected content, which makes it more robust for web sites that employ custom input sanitizations. We provide a detailed experimental evaluation to compare the four filters with respect to their usability and protection.</p>", "Keywords": "Cross-site scripting; filtering rules; XSS; XSS filters; XSSFilter", "DOI": "10.13052/jwe1540-9589.2179", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 58533, "JournalTitle": "Journal of Web Engineering", "ISSN": "1540-9589", "EISSN": "1544-5976", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, University of Tabuk, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Tabuk, Saudi Arabia"}], "References": []}, {"ArticleId": 105518403, "Title": "Research on the Influence of the Deformable Trimaran Expansion and Contraction on the Speed and Stability of the Ship in Wave Waters Based on PID Algorithm", "Abstract": "<p>This article studied on whether transforming from a mono-hull vessel to a trimaran surface vessel increases the stability of the vessel. Based on testing results from different levels of waves, a system of algorithm was developed to maximize the speed and stability of the vessel. Automation of the width of the tamarin with respect to the strength of the wave was made possible with a program in Arduino. The data for this particular trimaran vessel have significant value to the optimization of speed and stability and can be generalized for other in trimaran vessels. The application of transformable trimaran can save energy and protect the environment. </p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.051308", "PubYear": 2022, "Volume": "5", "Issue": "13", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105518429, "Title": "Credit Card Fraud Detection Based on Random Forest Model", "Abstract": "<p>This paper uses a classifier named random forest to detect credit card fraud. Credit card fraud is one of the main issues in the economic industry. To construct a credit card fraud detection system, a certain amount of samples is required. In this paper, a dataset containing 284,807 credit card transactions is used. This dataset has gone through the PCA transformation and includes 492 frauds out of 284,807 transactions. Based on the huge amount of data and imbalanced samples, this paper compresses the dataset and uses the synthetic minority over-sampling technique (SMOTE) to address the problem of imbalanced samples. Also, in this paper, we use random forest as a classification model while constructing the fraud detection system/method.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.051309", "PubYear": 2022, "Volume": "5", "Issue": "13", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105518432, "Title": "Complexity Characteristics and Robustness Analysis of Chengdu Metro Network", "Abstract": "<p>The study of robustness of the metro rail network in Chengdu, a central city in western China, contributes to the stability as well as safe operation of the metro network. Based on the complex network theory, the Space-L topological structure model of the complex network constituted by the first 9 metro lines in Chengdu is constructed, and its complex network characteristics are analyzed by topological properties such as degree and degree distribution, average path length, average clustering coefficient and scale-free degree. Through its topological properties, a robustness evaluation index system applicable to the Chengdu metro network is constructed, and simulations of deliberate attacks and random attacks are performed. The data of both simulations are counted and the trends and reasons of each data are analyzed and compared. Among the many stations, three stations, namely, Chengdu University of TCM & Sichuan Provincial People's Hospital Station, Science and Technology University Station and the Culture Palace Station, have higher vulnerability. When they are attacked, the subway network connectivity reliability decreases by 1/3, indicating the need to strengthen the security check of these three stations with higher degree values. </p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.051302", "PubYear": 2022, "Volume": "5", "Issue": "13", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105518782, "Title": "Correction to: A phase‑field study of neck growth in electron beam powder bed fusion (EB‑PBF) process of Ti6Al4V powders under different processing conditions", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00170-022-10769-0", "PubYear": 2023, "Volume": "124", "Issue": "10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Management and Production Engineering, (DIGEP), Integrated Additive Manufacturing Center (IAM@PoliTo), Turin, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management and Production Engineering, (DIGEP), Integrated Additive Manufacturing Center (IAM@PoliTo), Turin, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Management and Production Engineering, (DIGEP), Integrated Additive Manufacturing Center (IAM@PoliTo), Turin, Italy"}], "References": []}, {"ArticleId": 105518811, "Title": "Improving vessel connectivity in retinal vessel segmentation via adversarial learning", "Abstract": "Despite having achieved human-level performance in retinal vessel segmentation, deep learning based methods still suffer from poor connectivity of vessels in the generated segmentation maps. Since most methods operate as pixelwise classifiers, the vessel structure is ignored during the optimization of the segmentation network. To address this problem, a novel framework is proposed to enhance the vessel connectivity by incorporating the vessel structure into the segmentation network. First, to obtain the structural priors, the vessel structural priors extraction module (VSPEM) is proposed; VSPEM employs the powerful feature extraction ability of the convolutional autoencoder. After being pretrained, the proposed VSPEM can be used to extract useful latent features from the ground truths, which perform as the structural priors in segmentation. Then, the segmentation network is enforced to generate results that follow the distribution of the learned priors via adversarial learning. We have validated our method on three publicly available datasets, i.e., the DRIVE, CHASE_DB1 and STARE, and the state-of-the-art experimental results achieved on the above datasets demonstrate the efficacy of the proposed framework. Moreover, we show that the proposed framework is independent of segmentation models and can further improve model performance on vessel connectivity without introducing extra memory or a computational burden.", "Keywords": "", "DOI": "10.1016/j.knosys.2022.110243", "PubYear": 2023, "Volume": "262", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Machine Intelligence Laboratory, College of Computer Science, Sichuan University, Chengdu, 610065, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Machine Intelligence Laboratory, College of Computer Science, Sichuan University, Chengdu, 610065, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Machine Intelligence Laboratory, College of Computer Science, Sichuan University, Chengdu, 610065, China"}], "References": []}, {"ArticleId": 105518868, "Title": "Chronicling Indonesian EFL students’ engagement in podcast-based speaking activities in online learning milieu: A selfdetermination theory perspective", "Abstract": "The abrupt shift of teaching and learning to the online mode due to the Covid19 outbreak has inevitably called for technology integration to better engage students in online instruction. This call includes the possibility to enact podcast in an English as a foreign language (EFL) class, particularly in speaking. However, despite the increasing number of studies on the benefits of podcasting for language teaching and learning, little attention has been paid to EFL students’ engagement in self-created podcast at the micro-level of instruction. Thus, this study aimed to fill this void by enquiring how students engage in podcast-based speaking activities and what drives their engagement. It involved 23 EFL students at a public university in West Nusa Tenggara, Indonesia. The data were gleaned from learning portfolios, oral reflections, and students’ written narratives in sixteen sessions. The framework of engagement and self-determination theory were employed to carry out thematic analysis. The findings evince that, in general, the students disclosed positive behavioral, cognitive, and emotional engagement in a series of podcast based-speaking activities. More autonomy 336Khotimah, Cahyono & Batunan: Student engagement in podcast-based speaking activities The JALT CALL Journal vol. 18 no.3 and competence-supportive learning environment and unbalanced relatednesssupportive learning environment were found to be the contributing factors of students’ engagement. In addition to teachers’ and students’ factors, parents, families, and students’ friends beyond the classroom appeared to be other social factors which might affect students’ engagement. This study suggests three recommendations for improving podcast-based speaking classrooms in EFL practices. Some possible research topics for extended investigation are also suggested.", "Keywords": "Online speaking instruction; Podcast in language learning; Podcast-based speaking activities; Self-determination theory; Students’ engagement", "DOI": "10.29140/jaltcall.v18n3.621", "PubYear": 2024, "Volume": "18", "Issue": "3", "JournalId": 67259, "JournalTitle": "The JALT CALL Journal", "ISSN": "", "EISSN": "1832-4215", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Mataram, Indonesia; Universitas Negeri Malang, Indonesia"}, {"AuthorId": 2, "Name": "Bambang <PERSON>", "Affiliation": "Universitas Negeri Malang, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Negeri Malang, Indonesia; Universitas Negeri Manado, Indonesia"}], "References": []}, {"ArticleId": 105519075, "Title": "Forward modelling of synthetic aperture radar backscatter from lake ice over Canadian Subarctic Lakes", "Abstract": "Lake ice provides important social and economic services to local communities, in addition to being a sensitive indicator of climate change. The reduction of ground observations of freshwater ice has led to an increased reliance on the use of satellite remote sensing data. There is currently interest in the retrieval of lake ice properties (e.g., ice thickness, bubble radius, roughness) using synthetic aperture radar (SAR). Roughness at the ice-water interface is particularly important as it has been identified as the dominant mechanism for increasing SAR backscatter throughout the ice season and must be considered in numerical radiative transfer models. Therefore, this study determines optimal ice-water interface roughness height for two subarctic lakes in northern Canada and models backscatter throughout two ice seasons using the snow microwave radiative transfer (SMRT) model. The two lakes for this study are Noell Lake and Malcolm Ramsay Lake. Field observations of ice thickness, snow depth, snow density, and the Canadian Lake Ice Model (CLIMo) are used to parameterize SMRT. Modelled L, C, and X-band backscatter at different incidence angles is assessed using SAR imagery from multiple satellite missions. Root mean square errors ranged from 0.38 to 1.45 dB for Noell Lake and 0.70 to 2.33 dB for Malcolm Ramsay Lake. Discrepancies between modelled and observed backscatter were found to be connected to the representation of roughness at different interfaces within the ice column and changes that occurred during freeze-melt events. These results provide insight into how changes in ice properties impact backscatter throughout the ice season. SMRT is valuable for modelling backscatter from lake ice during the cold season and could be used to develop retrieval algorithms for estimating ice-water interface roughness. This would allow for the development of other inversion models for retrieval of surface ice conditions and ice thickness.", "Keywords": "Lake ice ; Radiative transfer model ; Synthetic aperture radar (SAR) ; Cryosphere", "DOI": "10.1016/j.rse.2022.113424", "PubYear": 2023, "Volume": "286", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Geography and Environmental Management, University of Waterloo, 200 University Ave W, Waterloo N2L 3G1, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Geography and Environmental Management, University of Waterloo, 200 University Ave W, Waterloo N2L 3G1, Canada;H2O Geomatics Inc., Waterloo, ON N2L 1S7, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Environmental Geosciences, Université Grenoble Alpes, 38402 Grenoble, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Geography and Environmental Management, University of Waterloo, 200 University Ave W, Waterloo N2L 3G1, Canada"}], "References": [{"Title": "Arctic and subarctic snow microstructure analysis for microwave brightness temperature simulations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "242", "Issue": "", "Page": "111754", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Assessment of machine learning classifiers for global lake ice cover mapping from MODIS TOA reflectance data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "253", "Issue": "", "Page": "112206", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "50 years of lake ice research from active microwave remote sensing: Progress and prospects", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "264", "Issue": "", "Page": "112616", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Year-round sea ice and snow characterization from combined passive and active microwave observations and radiative transfer modeling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "278", "Issue": "", "Page": "113061", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 105519128, "Title": "Motivations to Adopt BPM in View of Digital Transformation", "Abstract": "", "Keywords": "", "DOI": "10.1080/10580530.2022.2163324", "PubYear": 2024, "Volume": "41", "Issue": "4", "JournalId": 21771, "JournalTitle": "Information Systems Management", "ISSN": "1058-0530", "EISSN": "1934-8703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Economic Sciences, University of Warsaw, Warsaw, Poland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Villanova School of Business, Villanova University, Villanova, Pennsylvania, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Bier<PERSON>owicz", "Affiliation": "Faculty of Management, University of Warsaw, Warsaw, Poland"}], "References": [{"Title": "Strategy archetypes for digital transformation: Defining meta objectives using business process management", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "5", "Page": "103262", "JournalTitle": "Information & Management"}, {"Title": "Digital transformation and the new logics of business process management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "238", "JournalTitle": "European Journal of Information Systems"}, {"Title": "An Exploration into Future Business Process Management Capabilities in View of Digitalization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "2", "Page": "83", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Building a complementary agenda for business process management and digital innovation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "208", "JournalTitle": "European Journal of Information Systems"}, {"Title": "BPM motivations framework for digital challenges: lessons learned from customer use cases", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "3133", "JournalTitle": "Procedia Computer Science"}, {"Title": "A quantitative and qualitative study of the link between business process management and digital innovation", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "2", "Page": "103413", "JournalTitle": "Information & Management"}, {"Title": "Unpacking the Difference Between Digital Transformation and IT-Enabled Organizational Transformation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "1", "Page": "102", "JournalTitle": "Journal of the Association for Information Systems"}, {"Title": "Business Process Management: The evolution of a discipline", "Authors": "<PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "126", "Issue": "", "Page": "103404", "JournalTitle": "Computers in Industry"}]}, {"ArticleId": 105519221, "Title": "Deep order-wavelet convolutional variational autoencoder for fault identification of rolling bearing under fluctuating speed conditions", "Abstract": "Because of the complex operating environment of high-end industrial machinery, rolling bearing is generally operated at fluctuating working conditions such as variable speeds or loads, thus enables fault feature information is not obvious. That said, bearing fault identification under fluctuating working conditions are recognized as a very challenging problem. Deep learning blazes a valid route to address this issue by right of strong self-learning performance. Nevertheless, the performance of traditional deep learning model will degrade in the face of the fluctuating data with a sharp rising and heavy external interference. Therefore, to overcome this limitation, this study proposes a novel method named deep order-wavelet convolutional variational autoencoder (DOWCVAE) to identify bearing faults under fluctuating speed conditions, which can improve feature learning ability of a plain convolutional variational autoencoder (CVAE). Within this approach, an improved energy-order analysis with frequency-weighted energy operator (FWEO) is firstly presented to convert the raw time-domain vibration signal into the resampled angle-domain signal to relieve the influence of speed fluctuating and acquire the enhanced order spectrum data. Afterwards, wavelet kernel convolutional block (WKCB) with anti-symmetric real Laplace wavelet (ARLW) is constructed to extract the latent feature information closely related to equipment states from the enhanced order spectrum data via the stacked way layer by layer, which is capable of further promoting learning performance of overall network model and improve its generalizability. In addition, a high-efficiency intelligent optimization algorithm termed as multi-objective gray wolf optimizer (MOGWO) is introduced for choosing automatically optimal wavelet parameters of DOWCVAE model and avoiding negative impact posed by artificially adjusting parameter. Ultimately, the learned latent features are loaded to the softmax classifier to achieve automatic identification of different bearing health states and provide comprehensive diagnosis result. The analysis results from two experiment cases testify the effectiveness of our approach. Quantitatively, average identification accuracy of the proposed approach can reach 99% above, which shows its competitive advantages and is more satisfying as compared to some representative deep learning methods.", "Keywords": "", "DOI": "10.1016/j.eswa.2022.119479", "PubYear": 2023, "Volume": "216", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Nanjing Forestry University, Nanjing 210037, China;Corresponding author"}, {"AuthorId": 2, "Name": "Daoming She", "Affiliation": "School of Mechanical Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing 211189, China"}], "References": [{"Title": "Deep transfer multi-wavelet auto-encoder for intelligent fault diagnosis of gearbox with few target training samples", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105313", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Video anomaly detection and localization via Gaussian Mixture Fully Convolutional Variational Autoencoder", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "102920", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Euclidean distance based feature ranking and subset selection for bearing fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "113400", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Intelligent fault diagnosis of rolling bearings based on normalized CNN considering data imbalance and variable working conditions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "199", "Issue": "", "Page": "105971", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Imbalanced sample fault diagnosis of rotating machinery using conditional variational auto-encoder generative adversarial network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "106333", "JournalTitle": "Applied Soft Computing"}, {"Title": "Ensemble transfer CNNs driven by multi-channel signals for fault diagnosis of rotating machinery cross working conditions", "Authors": "<PERSON><PERSON><PERSON>; Haidong Shao; <PERSON><PERSON>", "PubYear": 2020, "Volume": "207", "Issue": "", "Page": "106396", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Improved CNN for the diagnosis of engine defects of 2-wheeler vehicle using wavelet synchro-squeezed transform (WSST)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "208", "Issue": "", "Page": "106453", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Fault diagnosis with synchrosqueezing transform and optimized deep convolutional neural network: An application in modular multilevel converters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "430", "Issue": "", "Page": "24", "JournalTitle": "Neurocomputing"}, {"Title": "Rolling bearing fault diagnosis using optimal ensemble deep transfer network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "213", "Issue": "", "Page": "106695", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Intelligent fault diagnosis of rotating machinery based on continuous wavelet transform-local binary convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106796", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Convolutional variational autoencoder-based feature learning for automatic tea clone recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "3332", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Implicit supervision for fault detection and segmentation of emerging fault types with Deep Variational Autoencoders", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "454", "Issue": "", "Page": "324", "JournalTitle": "Neurocomputing"}, {"Title": "Deep regularized variational autoencoder for intelligent fault diagnosis of rotor–bearing system within entire life-cycle process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "107142", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An interpretable data augmentation scheme for machine fault diagnosis based on a sparsity-constrained generative adversarial network", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115234", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Self-learning transferable neural network for intelligent fault diagnosis of rotating machinery with unlabeled and imbalanced data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "230", "Issue": "", "Page": "107374", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Hierarchical diagnosis of bearing faults using branch convolutional neural network considering noise interference and variable working conditions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "230", "Issue": "", "Page": "107386", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Fault Diagnosis of Machines Using Deep Convolutional Beta-Variational Autoencoder", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "2", "Page": "287", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Unsupervised domain-share CNN for machine fault transfer diagnosis from steady speeds to time-varying speeds", "Authors": "Hongru Cao; Haidong Shao; Xiang <PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "186", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Dilated convolutional neural network based model for bearing faults and broken rotor bar detection in squirrel cage induction motors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116290", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Imbalanced bearing fault diagnosis under variant working conditions using cost-sensitive deep domain adaptation network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116459", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-mode data augmentation and fault diagnosis of rotating machinery using modified ACGAN designed with new framework", "Authors": "<PERSON>; <PERSON><PERSON>; Haidong Shao", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101552", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Application of domain-adaptive convolutional variational autoencoder for stress-state prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "248", "Issue": "", "Page": "108827", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Visual inspection of fault type and zone prediction in electrical grids using interpretable spectrogram-based CNN modeling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "210", "Issue": "", "Page": "118368", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 105519302, "Title": "Multiple attribute decision making based on probabilistic generalized orthopair fuzzy sets", "Abstract": "<p>The advent of <PERSON><PERSON>’s generalized orthopair fuzzy sets (also called q -rung orthopair fuzzy sets) has brought more possibilities to accomplish the challenging task of modelling uncertainties. Compared with Pythagorean fuzzy sets and intuitionist fuzzy sets, q -rung orthopair fuzzy sets are more general in theory and more powerful in practice. Nevertheless, these extensions of fuzzy sets all assume that the membership and non-membership grades are equally important for every element. However, membership and non-membership grades may have different degrees of importance in real decision-making applications. Thus it may cause information loss if intuitionistic fuzzy sets or their extensions are used to describe initial decision information in such cases. To fill the gap, we consider adding the probability information which reflects the importance of membership and non-membership grades to further enhance generalized orthopair fuzzy sets in this study. Firstly, we put forth probability generalized orthopair fuzzy numbers, which extend generalized orthopair fuzzy numbers with membership and non-membership probabilities. We define some basic operations of probability generalized orthopair fuzzy numbers and investigate their related properties. We also presented a partial order \\(\\le _{L_{q}^{*}\\times L^{*}}\\) and a lexicographic order \\(\\le _{(s,h)}\\) for comparing probability generalized orthopair fuzzy numbers. Also, we define the accuracy and (normalized) score functions of probability generalized orthopair fuzzy numbers, and discuss their basic properties. Secondly, three different probabilistic generalized orthopair fuzzy aggregation operators, namely the probabilistic generalized orthopair fuzzy simple weighted averaging operator, the probabilistic generalized orthopair fuzzy weighted averaging operator and the probabilistic generalized orthopair fuzzy weighted geometric operator are defined, and their fundamental properties are explored in detail. The Minkowski distance and closeness index of probabilistic generalized orthopair fuzzy numbers are proposed, which can be used to develop an attribute weight determination method. Thirdly, we introduce probabilistic generalized orthopair fuzzy sets, and develop two methods for solving multiple attribute decision making problems based on probabilistic generalized orthopair fuzzy sets. As an illustration, the proposed methods are used to solve a green supplier selection problem with unknown attribute weight information. Furthermore, we compare our methods with several existing decision-making approaches to validate the efficacy of the proposed methods.</p>", "Keywords": "Generalized orthopair fuzzy set; Probabilistic generalized orthopair fuzzy number; Probabilistic generalized orthopair fuzzy set; Aggregation operator; Multiple attribute decision making", "DOI": "10.1007/s41066-022-00358-7", "PubYear": 2023, "Volume": "8", "Issue": "4", "JournalId": 4880, "JournalTitle": "Granular Computing", "ISSN": "2364-4966", "EISSN": "2364-4974", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Science, Xi’an University of Posts and Telecommunications, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Modern Posts, Xi’an University of Posts and Telecommunications, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, University of the Punjab, Lahore, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Xi’an University of Posts and Telecommunications, Xi’an, China"}], "References": [{"Title": "Multiattribute group decision making based on neutrality aggregation operators of q-rung orthopair fuzzy sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "427", "JournalTitle": "Information Sciences"}, {"Title": "Multiple attribute decision making based on q-rung orthopair fuzzy generalized Maclaurin symmetic mean operators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "518", "Issue": "", "Page": "181", "JournalTitle": "Information Sciences"}, {"Title": "Pythagorean fuzzy MCDM method based on CoCoSo and CRITIC with score function for 5G industry evaluation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "5", "Page": "3813", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Solving green supplier selection problem using q-rung orthopair fuzzy-based decision framework with unknown weight information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106431", "JournalTitle": "Applied Soft Computing"}, {"Title": "A hybrid decision-making model under q-rung orthopair fuzzy Yager aggregation operators", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "763", "JournalTitle": "Granular Computing"}, {"Title": "Extension of Einstein geometric operators to multi-attribute decision making under q-rung orthopair fuzzy information", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "779", "JournalTitle": "Granular Computing"}, {"Title": "Pythagorean fuzzy <PERSON> and Sklar power aggregation operators for solving multi-attribute decision-making problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "991", "JournalTitle": "Granular Computing"}, {"Title": "Novel score functions of generalized orthopair fuzzy membership grades with application to multiple attribute decision making", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "1", "Page": "95", "JournalTitle": "Granular Computing"}, {"Title": "Group decision-making framework under linguistic q-rung orthopair fuzzy Einstein models", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "15", "Page": "10309", "JournalTitle": "Soft Computing"}, {"Title": "Multi-attribute decision-making with q-rung picture fuzzy information", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "1", "Page": "197", "JournalTitle": "Granular Computing"}, {"Title": "Decision-making with q-rung orthopair fuzzy graph structures", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "3", "Page": "505", "JournalTitle": "Granular Computing"}, {"Title": "Group decision-making analysis based on linguistic q-rung orthopair fuzzy generalized point weighted aggregation operators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "883", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Pythagorean fuzzy entropy measure-based complex proportional assessment technique for solving multi-criteria healthcare waste treatment problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "4", "Page": "917", "JournalTitle": "Granular Computing"}]}, {"ArticleId": 105519303, "Title": "Artificial intelligence in gastroenterology: A narrative review", "Abstract": "", "Keywords": "", "DOI": "10.35712/aig.v3.i5.117", "PubYear": 2022, "Volume": "3", "Issue": "5", "JournalId": 78350, "JournalTitle": "Artificial Intelligence in Gastroenterology", "ISSN": "2644-3236", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105519360, "Title": "Transcriptional expression and prognostic roles of MCM7 in human bladder, breast, and lung cancers: a multi-omics analysis", "Abstract": "<p>Minichromosome Maintenance Complex Component 7 (MCM7) protein is associated with cell proliferation in cancer and that might play an important role in human cancer. Here, we examined a systematic analysis to identify the expression and prognosis pattern of MCM7 gene in different cancer. Initially, we examined publicly available online platforms such as Oncomine, TIMER, cBioportal, PrognoScan, and NetworkAnalyst to identify the expression pattern of MCM7 in human cancers. Our analysis revealed the higher transcriptional expression of MCM7 gene in different cancers, but overall expression analysis showed that bladder, breast, and lung cancers are most significant and these cancers show positive correlation with immune infiltration. We also identified a group of notable protein interactions with MCM7 by PPI (protein–protein interaction) analysis. We observed that MCM7 showing the high mRNA expression is positively correlated with the low survival rate in human bladder, breast, and lung cancers. In addition, we analyzed mutations and copy number alterations in MCM7 genes in various types of cancers using cBioPortal. Most of the functional enrichment found in this study were related to cell division or cell cycle regulation. Subsequently, our results suggest MCM7 is highly expressed during the cancer development process and prospective therapeutic target for human bladder, breast, and lung cancers. Therefore, targeting the protein can be used as early diagnosis option for different human cancer.</p>", "Keywords": "MCM7; mRNA expression and mutations; Immune infiltrates; Survival analysis and functional enrichment", "DOI": "10.1007/s13721-022-00405-y", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25861, "JournalTitle": "Network Modeling Analysis in Health Informatics and Bioinformatics", "ISSN": "2192-6662", "EISSN": "2192-6670", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Genetic Engineering and Biotechnology, Jashore University of Science and Technology, Jashore, Bangladesh; Bioinformatics and Microbial Biotechnology Laboratory, Department of Genetic Engineering and Biotechnology, Jashore University of Science and Technology, Jashore, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Microbiology, Jahangirnagar University, Dhaka, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Food and Nutrition, College of Biotechnology and Natural Resource, Chung-Ang University, Anseong, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Genetic Engineering and Biotechnology, Jashore University of Science and Technology, Jashore, Bangladesh; Bioinformatics and Microbial Biotechnology Laboratory, Department of Genetic Engineering and Biotechnology, Jashore University of Science and Technology, Jashore, Bangladesh"}], "References": [{"Title": "3D mathematical modeling of calcium signaling in Alzheimer’s disease", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Network Modeling Analysis in Health Informatics and Bioinformatics"}]}, {"ArticleId": 105519371, "Title": "Generating Clear Vibrotactile Cues with a Magnet Embedded in a Soft Finger Sheath", "Abstract": "<p>Haptic displays act on the user's body to stimulate the sense of touch and enrich applications from gaming and computer-aided design to rehabilitation and remote surgery. However, when crafted from typical rigid robotic components, they tend to be heavy, bulky, and expensive, while sleeker designs often struggle to create clear haptic cues. This article introduces a lightweight wearable silicone finger sheath that can deliver salient and rich vibrotactile cues using electromagnetic actuation. We fabricate the sheath on a ferromagnetic mandrel with a process based on dip molding, a robust fabrication method that is rarely used in soft robotics but is suitable for commercial production. A miniature rare-earth magnet embedded within the silicone layers at the center of the finger pad is driven to vibrate by the application of alternating current to a nearby air-coil. Experiments are conducted to determine the amplitude of the magnetic force and the frequency response function for the displacement amplitude of the magnet perpendicular to the skin. In addition, high-fidelity finite element analyses of the finger wearing the device are performed to investigate the trends observed in the measurements. The experimental and simulated results show consistent dynamic behavior from 10 to 1000 Hz, with the displacement decreasing after about 300 Hz. These results match the detection threshold profile obtained in a psychophysical study performed by 17 users, where more current was needed only at the highest frequency. A cue identification experiment and a demonstration in virtual reality validate the feasibility of this approach to fingertip haptics.</p>", "Keywords": "dip molding;dynamic modeling;electromagnetic actuator;soft haptics;vibrotactile cues;wearable haptic device", "DOI": "10.1089/soro.2021.0184", "PubYear": 2023, "Volume": "10", "Issue": "3", "JournalId": 12246, "JournalTitle": "Soft Robotics", "ISSN": "2169-5172", "EISSN": "2169-5180", "Authors": [{"AuthorId": 1, "Name": "Ifat Gertler", "Affiliation": "Haptic Intelligence Department, Max Planck Institute for Intelligent Systems, Stuttgart, Germany."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Haptic Intelligence Department, Max Planck Institute for Intelligent Systems, Stuttgart, Germany."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Haptic Intelligence Department, Max Planck Institute for Intelligent Systems, Stuttgart, Germany."}], "References": [{"Title": "Closed-Loop Haptic Feedback Control Using a Self-Sensing Soft Pneumatic Actuator Skin", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "22", "JournalTitle": "Soft Robotics"}, {"Title": "A Wearable Soft Haptic Communicator Based on Dielectric Elastomer Actuators", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "2", "Page": "451", "JournalTitle": "Soft Robotics"}]}, {"ArticleId": 105519385, "Title": "The Performance Index of Convolutional Neural Network-Based Classifiers in Class Imbalance Problem", "Abstract": "Class imbalance is a common problem in many classification domains. This paper provides an evaluation index and one algorithm for this problem based on binary classification. The Model Performance Index ( MPI ) is proposed for assessing classifier performance as a new evaluation metric, considering class imbalance impacts. Based on MPI , we investigate algorithms to estimate ideal classifier performance with a fair distribution (1:1) , referred to as the Ideal Model Performance Algorithm. Experimentally, compared with traditional metrics, MPI is more sensitive. Specifically, it can detect all types of changes in classifier performances, while others might remain at the same levels. Moreover, for the estimation of classifier performances, the algorithm reaches small differences between predictions and the values observed. Generally, for ideal performances, it achieved error rates of 0.060% - 1.3% for rare class in four experiments, showing a practical value on estimation and representation on the classifier performances.", "Keywords": "", "DOI": "10.1016/j.patcog.2022.109284", "PubYear": 2023, "Volume": "137", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "All Authors are with the Department of Biomedical Engineering, Centre for Robotics and Automation, City University of Hong Kong, Hong Kong Special Administrative Region"}, {"AuthorId": 2, "Name": "King <PERSON><PERSON>", "Affiliation": "All Authors are with the Department of Biomedical Engineering, Centre for Robotics and Automation, City University of Hong Kong, Hong Kong Special Administrative Region;Corresponding author"}], "References": [{"Title": "Appropriateness of performance indices for imbalanced data classification: An analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107197", "JournalTitle": "Pattern Recognition"}, {"Title": "Internet financing credit risk evaluation using multiple structural interacting elastic net feature selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "107835", "JournalTitle": "Pattern Recognition"}, {"Title": "Discriminative feature generation for classification of imbalanced data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108302", "JournalTitle": "Pattern Recognition"}, {"Title": "Geometric imbalanced deep learning with feature scaling and boundary sample mining", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "108564", "JournalTitle": "Pattern Recognition"}, {"Title": "Siamese networks with an online reweighted example for imbalanced data learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "132", "Issue": "", "Page": "108947", "JournalTitle": "Pattern Recognition"}, {"Title": "Noise-robust oversampling for imbalanced data classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109008", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 105519412, "Title": "Approaches for improving the efficiency of protected OS components fuzzing", "Abstract": "Fuzzing as a part of the continuous integration is a necessary tool, aimed primarily at the providing confidence in the software being developed. At the same time, in the presence of significant amounts of the source code, fuzzing becomes a resource-intensive task. That’s why increasing the efficiency of fuzzing to reach needed code sections more quickly without reducing quality becomes an important line of research. The article deals with approaches to improve the efficiency of fuzzing both for kernel and for user-space software. On the other hand, on these amounts of program code, static code analysis produces a huge number of warnings about possible errors, and the main resources within this type of analysis are required not to obtain to result, but for analytical processing. In this regard, in the article considerable attention is paid to the approach of correlating the results of static and dynamic code analysis using the developed tool, which also allows to implement directed fuzzing in order to confirm the warnings of static analyzer, which significantly increases the efficiency of testing components of the protected OS Astra Linux.", "Keywords": "dynamic analysis;directed fuzzing;operating system;Astra Linux;динамический анализ;направленное фаззинг-тестирование;операционная система", "DOI": "10.15514/ISPRAS-2022-34(4)-2", "PubYear": 2022, "Volume": "34", "Issue": "4", "JournalId": 9407, "JournalTitle": "Proceedings of the Institute for System Programming of the RAS", "ISSN": "2079-8156", "EISSN": "2220-6426", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ООО «РусБИТех-Астра»"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ООО «РусБИТех-Астра»"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ООО «РусБИТех-Астра»"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "ООО «РусБИТех-Астра»"}], "References": []}, {"ArticleId": 105519415, "Title": "Speech Enhancement Method Based on Modified Encoder-Decoder Pyramid Transformer", "Abstract": "The development of new technologies for voice communication has led to the need of improvement of speech enhancement methods. Modern users of information systems place high demands on both the intelligibility of the voice signal and its perceptual quality. In this work we propose a new approach to solving the problem of speech enhancement. For this, a modified pyramidal transformer neural network with an encoder-decoder structure was developed. The encoder compressed the spectrum of the voice signal into a pyramidal series of internal embeddings. The decoder with self-attention transformations reconstructed the mask of the complex ratio of the cleaned and noisy signals based on the embeddings calculated by the encoder. Two possible loss functions were considered for training the proposed neural network model. It was shown that the use of frequency encoding mixed with the input data has improved the performance of the proposed approach. The neural network was trained and tested on the DNS Challenge 2021 dataset. It showed high performance compared to modern speech enhancement methods. We provide a qualitative analysis of the training process of the implemented neural network. It showed that the network gradually moved from simple noise masking in the early training epochs to restoring the missing formant components of the speaker's voice in later epochs. This led to high performance metrics and subjective quality of enhanced speech.", "Keywords": "speech enhancement;noise reduction;noise masking;deep neural network;deep learning;encoder-decoder architecture;pyramid transformer;self-attention;улучшение качества речи;очистка от шума;маскирование шума;глубокая нейронная сеть;глубокое обучение;архитектура кодер-декодер;пирамидальный трансформер;самовнимание", "DOI": "10.15514/ISPRAS-2022-34(4)-10", "PubYear": 2022, "Volume": "34", "Issue": "4", "JournalId": 9407, "JournalTitle": "Proceedings of the Institute for System Programming of the RAS", "ISSN": "2079-8156", "EISSN": "2220-6426", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Алтайский государственный университет"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Алтайский государственный университет"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Алтайский государственный университет"}], "References": []}, {"ArticleId": 105519475, "Title": "A method for the dynamic collaboration of the public and experts in large-scale group emergency decision-making: Using social media data to evaluate the decision-making quality", "Abstract": "Aiming at the complex and changeable environment and the low public participation in emergency decision-making, this article proposes a method for the dynamic collaboration of the public and experts in large-scale group emergency decision-making (LSGEDM) based on social media data. First, sentiment analysis is carried out on text data from social media platforms to evaluate the quality of LSGEDM at both the attribute and comprehensive levels. Then, according to the decision-making quality at the attribute level, a method for the dynamic updating of attribute weights is proposed. Next, in the social network environment, the trust relationship between experts is dynamically updated based on the comprehensive quality of decision-making and the distance between the expert and group preferences, and expert weights are calculated by the improved PageRank algorithm. Finally, the effectiveness and superiority of the proposed method are verified via its application to the COVID-19 epidemic in China and a comparative analysis.", "Keywords": "", "DOI": "10.1016/j.cie.2022.108943", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Yucheng Zhu", "Affiliation": "School of Business, Central South University, Changsha 410083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business, Central South University, Changsha 410083, China;School of Advanced Interdisciplinary Studies, Hunan University of Technology and Business, Changsha 410205, China;Corresponding author at: School of Business, Central South University, Changsha 410083, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Accounting, Hunan University of Finance and Economics, Changsha 410205, China"}], "References": [{"Title": "Consensus-based non-cooperative behaviors management in large-group emergency decision-making considering experts’ trust relations and preference risks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "190", "Issue": "", "Page": "105108", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "SACPC: A framework based on probabilistic linguistic terms for short text sentiment analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105572", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Water–Energy–Food nexus evaluation with a social network group decision making approach based on hesitant fuzzy preference relations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106363", "JournalTitle": "Applied Soft Computing"}, {"Title": "A framework based on (probabilistic) soft logic and neural network for NLP", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106232", "JournalTitle": "Applied Soft Computing"}, {"Title": "Sentiment Analysis based Multi-Person Multi-criteria Decision Making methodology using natural language processing and deep learning for smarter decision aid. Case study of restaurant choice using TripAdvisor reviews", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "22", "JournalTitle": "Information Fusion"}, {"Title": "An extended TODIM approach for group emergency decision making based on bidirectional projection with hesitant triangular fuzzy sets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "106959", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Trust and behavior analysis-based fusion method for heterogeneous multiple attribute group decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "152", "Issue": "", "Page": "106992", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Overlapping community detection by constrained personalized PageRank", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "114682", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dynamic assessment of Internet public opinions based on the probabilistic linguistic Bayesian network and Prospect theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107359", "JournalTitle": "Applied Soft Computing"}, {"Title": "Interactive multi-criteria group decision-making with probabilistic linguistic information for emergency assistance of COVID-19", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107383", "JournalTitle": "Applied Soft Computing"}, {"Title": "Building a fuzzy sentiment dimension for multidimensional analysis in social networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107390", "JournalTitle": "Applied Soft Computing"}, {"Title": "Sentiment Lossless Summarization", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107170", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Topic-level sentiment analysis of social media data using deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107440", "JournalTitle": "Applied Soft Computing"}, {"Title": "A sentiment analysis-based expert weight determination method for large-scale group decision-making driven by social media data", "Authors": "<PERSON><PERSON> Wan; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115629", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A public and large-scale expert information fusion method and its application: Mining public opinion via sentiment analysis and measuring public dynamic reliability", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "71", "JournalTitle": "Information Fusion"}, {"Title": "A clustering- and maximum consensus-based model for social network large-scale group decision making with linguistic distribution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "602", "Issue": "", "Page": "269", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105519515, "Title": "A Mobility-Aware Service Function Chain Migration Strategy Based on Deep Reinforcement Learning", "Abstract": "<p>With the development of network function virtualization (NFV) and mobile edge computing (MEC), service function chaining (SFC) can be deployed more flexibly on the network edge to provide users with higher quality services. However, the mobility of users might affect the quality of the perceived service and even cause service unavailability. It is thus a challenge to better migrate the service function chains to reduce prevent such degradation and improve the migration success rate. This paper investigates the SFC migration timing decision problem in user predicted movement scenarios with a known mobile path and predicted arrival time. First, we establish an prediction model of user arrival time and formulate the SFC migration process as a mathematical model. Then, we model the SFC migration process as a Markov decision process, and propose a deep Q-network based SFC migration timing decision (DQN-MTD) algorithm. DQN-MTD can be useful to perceive and predict the state changes of network resources, and select appropriate migration timing for virtual network functions (VNFs) based on SFC migration information. The experimental results show that compared with existing algorithms, DQN-MTD algorithm can reduce the average service downtime by about 14%, improve the migration success rate of SFC by about 20%, and reduce the average VNF migration time and memory when network load is low.</p>", "Keywords": "Network function virtualization; User mobility; Service function chain migration; Deep reinforcement learning", "DOI": "10.1007/s10922-022-09713-0", "PubYear": 2023, "Volume": "31", "Issue": "1", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communications Engineering, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information and Communications Engineering, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communications Engineering, Beijing University of Posts and Telecommunications, Beijing, China"}], "References": [{"Title": "Bus arrival time prediction and reliability analysis: An experimental comparison of functional data analysis and Bayesian support vector regression", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107663", "JournalTitle": "Applied Soft Computing"}, {"Title": "Priority-awareness VNF migration method based on deep reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "208", "Issue": "", "Page": "108866", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 105519533, "Title": "The Influence of <PERSON><PERSON><PERSON> and Lemon Juice on the Quality of Tomato Sorbet", "Abstract": "<p>Sorbet is one of common frozen desserts. It is prepared with low concentration of fat and protein; thus, the use of stabilizer in sorbet formulation extremely dictates the final properties. This current work investigated the quality (hardness, total solids, °Brix, viscosity, overrun, melting rate, vitamin C, lycopene content, and organoleptic test) of tomato-based sorbet added with different levels of xanthan gum as the stabilizer and lemon juice as the taste improver. The results showed that increase in xanthan gum level up to 0.5% was able to improve the overrun, melting rate, and lycopene content, i.e. 35%, 0.84 g/min, and 1.66 mg/100 g, respectively. Meanwhile, the addition of lemon juice into sorbet formulation could increase the content of vitamin C. Furthermore, addition of lemon juice was effective in removing the unpleasant tomato taste in the sorbet, but it did not affect the hardness, total solids, °Brix, lycopene content, viscosity, overrun, and melting rate.</p>", "Keywords": "lemon juice;lycopene;sorbet;tomato;xanthan gum", "DOI": "10.6066/jtip.2022.33.2.148", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 36981, "JournalTitle": "Jurnal Teknologi dan Industri Pangan", "ISSN": "1979-7788", "EISSN": "2087-751X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chemical Engineering Department, Universitas Katolik Parahyangan, Bandung, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Chemical Engineering Department, Universitas Katolik Parahyangan, Bandung, Indonesia"}], "References": []}, {"ArticleId": 105519731, "Title": "Attention Grabbing through Forward Reference: An ERP Study on Clickbait and Top News Stories", "Abstract": "Social media tends to use “catchy” headlines to intentionally get people to click news. However, clickbait headlines can erode readers’ trust and devalue news. To combat clickbait, it is necessary to understand how clickbait headlines influence readers’ clicking behavior and attention allocation. In this study, the researchers first collected headlines of news stories that had different amount and duration of media coverage, then identified whether these headlines include forward-reference strategies, and conducted an EEG experiment based on N2b and P3a. The results showed that the clickbait headlines with a forward-reference device attracted more attention resources and induced the involuntary attention allocation more quickly, compared with those without a forward-reference device. Unexpectedly, whether the news stories were top news stories or non-top news stories didn’t significantly influence the attention allocation.", "Keywords": "", "DOI": "10.1080/10447318.2022.2158262", "PubYear": 2024, "Volume": "40", "Issue": "11", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Chongqing University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing, China"}, {"AuthorId": 3, "Name": "Honglian <PERSON>", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Cao", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing, China"}], "References": [{"Title": "Effects of Semantic Congruence on Sign Identification: An ERP Study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "5", "Page": "800", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "An Exploratory Study Using Electroencephalography (EEG) to Measure the Smartphone User Experience in the Short Term", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "11", "Page": "1008", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 105519739, "Title": "Robust Rumor Detection based on Multi-Defense Model Ensemble", "Abstract": "The development of adversarial technology, represented by adversarial text, has brought new challenges to rumor detection based on deep learning. In order to improve the robustness of rumor detection models under adversarial conditions, we propose a robust detection method based on the ensemble of multi-defense model on the basis of several mainstream defense methods such as data enhancement, random smoothing, and adversarial training. First, multiple robust detection models are trained based on different defense principles; then, two different ensemble strategies are used to integrate the above models, and the detection effect under different ensemble strategies is studied. The test results on the open-source dataset Twitter15 show that the proposed method is able to compensate for the shortcomings of a single model by ensembling different decision boundaries to effectively defend against mainstream adversarial text attacks and improve the robustness of rumor detection models compared to existing defense methods.", "Keywords": "", "DOI": "10.1080/08839514.2022.2151174", "PubYear": 2023, "Volume": "37", "Issue": "1", "JournalId": 7360, "JournalTitle": "Applied Artificial Intelligence", "ISSN": "0883-9514", "EISSN": "1087-6545", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of information technology, Information Engineering University, Zhengzhou, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of information technology, Information Engineering University, Zhengzhou, People’s Republic of China"}], "References": [{"Title": "TRIESTE: translation based defense for text classifiers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "12", "Page": "16385", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 105519949, "Title": "User- Centered Methodologies for Requirements Engineering: A Comparative Analysis using ISO/IEC/IEEE 29148", "Abstract": "<p lang=\"es\"><p>La ingeniería de requisitos es un proceso fundamental del ciclo de vida del desarrollo de software que permite definir las funcionalidades, la calidad y el alcance de un software. La ingeniería de requisitos es un proceso de participación humana intensiva y que tiene, en ISO/IEC/IEEE 29148, un estándar que define un conjunto de procesos (actividades, tareas, elementos de información, etc.) que brindan soporte para su formalización. Sin embargo, en la industria del software, se han reportado muchos tipos de deficiencias. En este contexto, han surgido propuestas de Metodologías Centradas en el Usuario para Ingeniería de Requisitos que requieren ser estudiadas para determinar su alineación con la norma ISO 29148. (OBJETIVO) El objetivo de este estudio es comparar Metodologías Centradas en el Usuario para Ingeniería de Requisitos (MCUIR) tomando elementos de proceso de ISO 29148 como criterio. (MÉTODOS) Para esta investigación se realizó un estudio de mapeo sistemático y un análisis comparativo de las MCUIR obtenidas. (RESULTADOS) En el estudio de mapeo sistemático, se obtuvieron 4.463 estudios de tres bases de datos digitales relevantes en la primera etapa, y luego del proceso de selección, se identificaron cinco Metodologías Centradas en el Usuario para Ingeniería de Requisitos. Estas metodologías se caracterizaron y analizaron desde la perspectiva de los elementos del proceso para compararlas con la ISO 29148. (CONCLUSIONES) La metodología DoRCU es la más cercana a la ISO 29148, seguida de las metodologías Ammeth y Borja; y la metodología XRE es la que más se aleja de la ISO 29148.</p></p>", "Keywords": "", "DOI": "10.32870/recibe.v11i1.242", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 89361, "JournalTitle": "RECIBE, REVISTA ELECTRÓNICA DE COMPUTACIÓN, INFORMÁTICA, BIOMÉDICA Y ELECTRÓNICA", "ISSN": "", "EISSN": "2007-5448", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Escuela de Ingeniería de Sistemas e Informática - Universidad Nacional de Moquegua - UNAM"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Escuela de Ingeniería de Sistemas e Informática - Universidad Nacional de Moquegua - UNAM"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Pontificia Universidad Católica del Perú"}], "References": []}, {"ArticleId": 105519954, "Title": "Biomarcadores identificados mediante estudios de EEG para la descripción objetiva del padecimiento de acúfeno, revisión del estado del arte", "Abstract": "<p lang=\"es\"><p>El acúfeno es un padecimiento descrito como la percepción de un sonido, similar al emitido por un campana, zumbido o silbido, en ausencia de una fuente acústica que lo genere. Los principales métodos utilizados para evaluar esta condición son cuestionarios, escalas visuales análogas o pruebas de audiometría de tono puro, sin embargo, estas no son del todo confiables al obtener resultados que dependen de lo expresado por los pacientes con esta condición. El presente trabajo tiene como objetivo la revisión de diferentes fuentes de información en la búsqueda de trabajos que reporten la identificación de biomarcadores, es decir una medida objetiva a partir de datos de electroencefalografía (EEG) en el estudio del padecimiento de acúfeno. Con base en lo encontrado, se observó que existen una variedad de trabajos que reportan mediante diferentes metodologías una o varias características asociadas al padecimiento de acúfeno; sin embargo, un aspecto que se identificó fue que hoy en día no se han realizado las evaluaciones pertinentes para determinar si una característica pudiera ser considerada un biomarcador, es decir que ésta pueda describir de manera objetiva, precisa y reproducible el padecimiento de acúfeno, lo cual es importante considerar para la propuesta y desarrollo de futuros trabajos.</p></p>", "Keywords": "", "DOI": "10.32870/recibe.v11i1.240", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 89361, "JournalTitle": "RECIBE, REVISTA ELECTRÓNICA DE COMPUTACIÓN, INFORMÁTICA, BIOMÉDICA Y ELECTRÓNICA", "ISSN": "", "EISSN": "2007-5448", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "División de Tecnologías para la Integración Ciber-Humana, Universidad de Guadalajara, Guadalajara, Jalisco, México"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "División de Tecnologías para la Integración Ciber-Humana, Universidad de Guadalajara, Guadalajara, Jalisco, México"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Tecnológico de Monterrey, Escuela de Ingeniería y Ciencias, Monterrey, Nuevo León, México."}], "References": []}, {"ArticleId": 105519976, "Title": "Investigation on variation characteristics of bent tube axis and determination of bending die motion trajectory in free bending process", "Abstract": "<p>In free bending process, the axis shape of bent tube is determined by motion trajectory of bending die. The bent tubes with different bending radii and bending directions can be formed by continuously adjusting the position and posture of die. To accurately form the pre-designed bent tube, the formation mechanism of bending arc in free bending process was analyzed. The mathematical model between bending radius and deviation was established and the moving direction of bending die was also determined according to the plane space azimuth where the bending arc segment is located. The formation process of transition segments with variable curvature and variable bending direction is illustrated during the movement of bending die, and the influence of transition segment on the axis shape was also explored. By comparing the axis deviation of experimental and simulated bent tubes, the length of transition segment and the moving path of bending die can be optimized. Finally, the applicability of free bending forming technology in forming bent tubes with different axis features was verified by conducting free bending experiments on bent tubes with three typical shapes. Due to the high flexibility of free bending forming technology, the work in this paper could promote the engineering application of this technology in the precise control of axis shape of bent tube.</p>", "Keywords": "Free bending; Tube; Transition segment; Axis shape; Stable segment", "DOI": "10.1007/s00170-022-10727-w", "PubYear": 2023, "Volume": "124", "Issue": "10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Material Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, People’s Republic of China; State-Owned Machinery Factory in Wuhu, Wuhu, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Material Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Material Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Material Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, People’s Republic of China"}, {"AuthorId": 5, "Name": "Yuanji Shi", "Affiliation": "College of Material Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, People’s Republic of China; School of Mechanical Engineering, Nanjing Vocational University of Industry Technology, Nanjing, People’s Republic of China"}, {"AuthorId": 6, "Name": "Song Shu", "Affiliation": "State-Owned Machinery Factory in Wuhu, Wuhu, People’s Republic of China"}], "References": [{"Title": "Research on influencing factors and laws of free-bending forming limit of tube", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "3-4", "Page": "1421", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Investigation on the influence of weld position on the deformation behavior of welded tube during free bending process", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "3-4", "Page": "2201", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 105520005, "Title": "EFL teachers’ perceptions of online community projects in secondary school education", "Abstract": "English as a Foreign Language (EFL) is crucial in most secondary schools within the EU and many other schools worldwide. Some teachers are integrating Online Community Projects (OCPs) into their curricula to provide a means of communication that motivates learning and incites learner-centred methods. This collaborative Action Research study reports on the perspectives and experiences of EFL teachers teaching in secondary schools in three European countries: the north and south of Italy, the east of Norway and the west of Sweden. Data were collected from six EFL teachers: four were users of OCPs, while the other two had chosen not to use them. Methods used to gather data were individual face-to-face interviews and open-question questionnaires. The results showed that all teachers perceived that working on OCPs could benefit professional development and their students’ language skills. The choice not to use OCPs was related to the lack of time and difficulties in assessment. This research provides a lens through which to examine the advantages and disadvantages of integrating OCPs into the EFL curricula. It has implications for teachers wishing to include OCPs in their curriculum. © 2022 Lesley June Fearn", "Keywords": "Action research; Community; E-twinning; Iearn; Teacher development", "DOI": "10.29140/jaltcall.v18n3.600", "PubYear": 2024, "Volume": "18", "Issue": "3", "JournalId": 67259, "JournalTitle": "The JALT CALL Journal", "ISSN": "", "EISSN": "1832-4215", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105520053, "Title": "Developing a Neural Network Based Fault Prediction Tool for a Solar Power Plant in Uganda", "Abstract": "<p>Solar photovoltaic (PV) systems are one of the fastest growing renewable energy technologies and plenty of research has been and continues to be carried out in this domain. Maximization of solar PV power plant production, efficiency and return on investment can only be achieved by having adequate and effective maintenance systems in place. Of the various maintenance schemes, predictive maintenance is popular for its effectiveness and minimization of resource wastage. Maintenance activities are scheduled based on the real time condition of the system with priority being given to the system components with the highest likelihood of failure. A good predictive maintenance system is based on the premise of being able to anticipate faults before they occur. In this study therefore, a fault prediction tool for a solar plant in Uganda is proposed. The hybrid tool is developed using both feed forward and long short term memory neural networks for power prediction, in conjunction with a mean chart statistical process control tool for final fault prediction. Results from the study demonstrate that the feed forward and long short term memory neural network modules of the proposed tool attain mean absolute errors of 4.2% and 6.9% respectively for power production predictions. The fault prediction capability of the tool is tested under both normal and abnormal operating conditions. Results show that the tool satisfactorily discriminates against the fault and non-fault conditions thereby achieving successful solar PV system fault prediction.</p>", "Keywords": "Machine learning;Feedforward Neural Networks;Long Short Term Memory;Solar fault prediction", "DOI": "10.14738/tmlai.106.13645", "PubYear": 2022, "Volume": "10", "Issue": "6", "JournalId": 29184, "JournalTitle": "Transactions on Machine Learning and Artificial Intelligence", "ISSN": "", "EISSN": "2054-7390", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Institute of Technology, Perth, WA 6005, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Institute of Technology, Perth, WA 6005, Australia"}], "References": []}, {"ArticleId": *********, "Title": "Reinforcement Learning-Based Aggregated Spectrum Sharing for Multi-Channel Vehicular Networking", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2022.1212297", "PubYear": 2022, "Volume": "12", "Issue": "12", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "嘉程 唐", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Dataset on rheological measurements of xanthan gum aqueous dispersions containing sodium chloride and settling dynamics of spheres and disks in these dispersions", "Abstract": "This paper presents a dataset collected in laboratory experiments on the settling of solid spheres and disks in shear-thinning and viscoelastic aqueous solutions of xanthan gum with sodium chloride addition. Two types of spheres with density of 1.41 g/cm<sup>3</sup> varying in diameter (3.00 mm and 1.59 mm) and four types of disks with density of 1.43 g/cm<sup>3</sup> and thickness of 0.3 mm varying in diameter (1.5 mm, 2.0 mm, 2.5 mm, and 3.0 mm) were considered. A single particle was settling in a column filled with a test solution which varied in salt content (from 0 M to 0.9 M), while xanthan gum content was constant (1 g/L). The total of elven solutions were tested. For each experimental set, a sequence of images with a falling particle was captured using a camera with macro lenses. Dataset includes position of particle in time and enables the evaluation of settling velocity. Rheological measurements were carried out for each test solution to assess flow properties and viscoelasticity. The following measurements were performed: shear dependent viscosity, shear stress amplitude sweeps, frequency sweeps, the dependence of the first normal stresses difference on shear strain at constant frequency (1 Hz). Datasets may be useful in various areas on fluid mechanics and rheology, e.g., in research on the impact of salinity on rheological properties of exopolymer solutions, to develop numerical models on solid particles settling in non-Newtonian fluids, and in studies on the impact of exopolymers and electrolytes dissolved in water on settling dynamics of solid particles.", "Keywords": "Exopolymer;Non-Newtonian fluid;Particle settling;Rheology;Salinity;Sedimentation;Xanthan gum", "DOI": "10.1016/j.dib.2022.108865", "PubYear": 2023, "Volume": "46", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Geophysics, Polish Academy of Sciences, Ks. <PERSON> 64, Warsaw 01-452, Poland."}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Chemistry, Warsaw University of Technology, Noakowskiego St. 3, Warsaw 00-664, Poland."}], "References": []}, {"ArticleId": 105520275, "Title": "Optimal Entropy Genetic Fuzzy-C-Means SMOTE (OEGFCM-SMOTE)", "Abstract": "Classification problems of unbalanced data sets are commonplace in industrial production and medical research fields. Different approaches have been proposed to handle these problems by generating synthetic samples, but most of them implement hyperparameters and tend to generate noise, because they neglect the entropy of the initial data. Recently, oversampling methods based on clustering have been proposed to overcome this problem. Unfortunately, they inherit the sensitivity of hard clustering methods. Moreover, the hyperparameters are manually selected. This paper introduces Optimal Entropy Genetic Fuzzy-C-Means SMOTE (OEGFCM-SMOTE) that balances data with minimum noise based on the original mathematical model, soft clustering, and evolutionary optimization. First, to handle the Kmeans sensitivity, OEGFCM-SMOTE uses a SMOTE to generate samples in safe regions based on Fuzzy-C-Means, known to be consistent with the boundary problem. Fuzzy-C-Means SMOTE processes in three steps (grouping, filtering, and interpolation) and implements 4 parameters, namely the number of clusters, the number of neighboring points of the minority data, the threshold of the unbalanced ratio and the exponent of the distribution of the minority data in the promising clusters. Second, the optimal choice of these parameters is based on a mixed-variable optimization model which minimizes the amount of noise measured by the entropy; the feasible domain is estimated by considering the density of the data set and by studying the boundary cases. Finally, this model is solved using the genetic algorithm by adopting genetic operators with appropriate rates. OEGFCM-SMOTE is evaluated using 5 classifiers, 21 unbalanced datasets (15 ordinary size and 6 Big data), and it is compared to 14 oversampling methods using three performance measures. To overcome the problem of multiple comparisons, considering different data sets, Holm’s test is used. OEGFCM-SMOTE consistently outperforms other popular oversampling methods.", "Keywords": "", "DOI": "10.1016/j.knosys.2022.110235", "PubYear": 2023, "Volume": "262", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Science Laboratory (ESL), Department of Mathematics, Multidisciplinary Faculty of Taza, University Sidi Mohamed <PERSON>, Morocco;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Science Laboratory (ESL), Department of Mathematics, Multidisciplinary Faculty of Taza, University Sidi Mohamed <PERSON>, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Engineering Science Laboratory (ESL), Department of Mathematics, Multidisciplinary Faculty of Taza, University Sidi Mohamed <PERSON>, Morocco"}], "References": [{"Title": "Improving interpolation-based oversampling for imbalanced data learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104826", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel selective naïve Bayes algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "105361", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "ACO Resampling: Enhancing the performance of oversampling methods for class imbalance classification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "196", "Issue": "", "Page": "105818", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "LR-SMOTE — An improved unbalanced data set oversampling based on K-means and SVM", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "196", "Issue": "", "Page": "105845", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Local distribution-based adaptive minority oversampling for imbalanced data classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "422", "Issue": "", "Page": "200", "JournalTitle": "Neurocomputing"}, {"Title": "RCSMOTE: Range-Controlled synthetic minority over-sampling technique for handling the class imbalance problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "542", "Issue": "", "Page": "92", "JournalTitle": "Information Sciences"}, {"Title": "Multi-class imbalanced big data classification on Spark", "Authors": "<PERSON> IV; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106598", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A cluster-based oversampling algorithm combining SMOTE and k-means for imbalanced medical data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "572", "Issue": "", "Page": "574", "JournalTitle": "Information Sciences"}, {"Title": "An efficient parallel indexing structure for multi-dimensional big data using spark", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "10", "Page": "11187", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A new two-layer nearest neighbor selection method for kNN classifier", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107604", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Instance weighted SMOTE by indirectly exploring the data distribution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "249", "Issue": "", "Page": "108919", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel Random Forest integrated model for imbalanced data classification problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "250", "Issue": "", "Page": "109050", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Intelligent Local Search for an Optimal Control of Diabetic Population Dynamics", "Authors": "El Ouissari <PERSON>; El Mouta<PERSON>; Baïzri <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "6", "Page": "1051", "JournalTitle": "Mathematical Models and Computer Simulations"}]}, {"ArticleId": 105520302, "Title": "Retraction Note: Research on control strategy and policy optimal scheduling based on an improved genetic algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-022-08191-4", "PubYear": 2023, "Volume": "35", "Issue": "6", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics Management and Law, Hubei Normal University, Huangshi, China"}, {"AuthorId": 2, "Name": "Yongcai Yan", "Affiliation": "School of Economics Management and Law, Hubei Normal University, Huangshi, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Law, Hubei University of Economics, Wuhan, China"}], "References": []}, {"ArticleId": 105520321, "Title": "WristMenu with Tactons: An Eyes- and Ears-<PERSON> <PERSON>u with Ta<PERSON><PERSON> Describing Menu Items in the Wrist Rotation Space", "Abstract": "We propose a new interface concept; Proprioceptive Menu with Tacton<PERSON>, an eyes- and ears-free menu in a proprioceptive space that uses a set of Tactons to describe its menu items. While previous proprioceptive menus utilized vibrotactile feedback only to express the boundary between menu items, we spot an unexplored possibility that distinct vibrotactile stimuli, Tactons, can be used to describe the status of each menu item. As an instance of this concept, we present WristMenu with Tacton<PERSON>, an eyes- and ears-free menu using a one-dimensional proprioceptive space of the axial wrist rotation. Users can grasp an overview of each menu item’s status through Tactons delivered on their wrist skin and eventually decide to select the desired item. Our evaluation study showed that users could find a target item among five candidates and recognize its status in 5 seconds on average at the accuracies of 93% and 86% in seated and walking conditions, respectively. Through our evaluation, we could confirm the feasibility of the proposed concept.", "Keywords": "", "DOI": "10.1080/10447318.2022.2159780", "PubYear": 2024, "Volume": "40", "Issue": "9", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Korea Advanced Institute of Science and Technology, Daejeon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Korea Advanced Institute of Science and Technology, Daejeon, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Korea Advanced Institute of Science and Technology, Daejeon, Republic of Korea"}], "References": [{"Title": "WristDial: An Eyes-Free Integer-Value Input Method by Quantizing the Wrist Rotation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "17", "Page": "1607", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 105520360, "Title": "Unified neuroadaptive fault-tolerant control of fractional-order systems with or without state constraints", "Abstract": "The problem of controlling fractional-order nonlinear systems remains interesting and challenging due to the powerful and hereditary memory features of such systems. In this paper, we investigate the adaptive tracking control problem for a class of fractional-order nonlinear systems with or without state constraints. The tan-type barrier L<PERSON><PERSON>nov function (BLF) is adopted for the first time in fractional Lyapunov direct method. Such technique allows for the accommodation of the situations with and without state constraints in a unified framework. Moreover, neural network (NN) is utilized to reconstruct the sophisticated nonlinear functions arising from the fractional-order differentiation of the virtual controller, resulting in a neuroadaptive fault-tolerant control scheme. It is shown that, with the proposed unified control, all the closed-loop signals are semiglobally ultimately uniformly bounded whether state constraints are imposed or not. Both theoretical analysis and numerical simulation confirm the effectiveness of the developed approach.", "Keywords": "", "DOI": "10.1016/j.neucom.2022.12.035", "PubYear": 2023, "Volume": "524", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Automation, Chongqing University, Chongqing 400044, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Chongqing University, Chongqing 400044, PR China;Chongqing Key Laboratory of Intelligent Unmanned Systems, Chongqing 400044, PR China;Corresponding author at: School of Automation, Chongqing University, Chongqing 400044, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Chongqing University, Chongqing 400044, PR China"}], "References": [{"Title": "Bipartite consensus control for fractional-order nonlinear multi-agent systems: An output constraint approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "397", "Issue": "", "Page": "212", "JournalTitle": "Neurocomputing"}, {"Title": "Event-triggered adaptive neural control of fractional-order nonlinear systems with full-state constraints", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; Shaocheng Tong", "PubYear": 2020, "Volume": "412", "Issue": "", "Page": "320", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 105520403, "Title": "AUTOMATED DATABASE OF DESTRUCTIVE CONTENT: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AND MATHEMATICAL SUPPORT OF THE TEXT ANALYZER", "Abstract": "<p>В статье рассматривается создание автоматизированной базы данных деструктивного контента с возможностью тонального анализа текста в мессенджере «Telegram» с учетом структурно-функционального и методического обеспечения. Разработан алгоритм анализа контента с текстовым содержанием, в котором решение о деструктивности выносится непосредственно после глубокого анализа текста. На основе полученного алгоритма было разработано программное обеспечение, которое позволяет не просто добавлять контент, по ключевым словам, заранее предопределенным в базе данных, а именно с использованием тонального анализа текста. Разработанное методическое, алгоритмическое и программное обеспечение актуализации базы данных может быть применено для анализа различных мессенджеров, таких как «WhatsApp» и «Viber», а математическое обеспечение позволит произвести усовершенствование системы на основе полученных метрик.</p><p>The article discusses the creation of an automated database of destructive content with the possibility of tonal text analysis in the Telegram messenger, taking into account the structural, functional and methodological support. An algorithm for analyzing content with textual content has been developed, in which the decision on destructiveness is made immediately after a deep analysis of the text. Based on the developed algorithm, software was written that allows not just adding content based on keywords predefined in the database, but using tonal text analysis. The developed methodological, algorithmic and software for updating the database can be used to analyze various messengers, such as WhatsApp and Viber, and the mathematical software will allow improving the system based on the metrics obtained.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2022.25.3.010", "PubYear": 2022, "Volume": "", "Issue": "3(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Е<PERSON><PERSON><PERSON><PERSON><PERSON>евич", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, О<PERSON><PERSON><PERSON> Юр<PERSON>евич", "Affiliation": ""}], "References": []}, {"ArticleId": 105520411, "Title": "A physical method for downscaling land surface temperatures using surface energy balance theory", "Abstract": "Fine-resolution land surface temperature (LST) derived from thermal infrared remote sensing images is a good indicator of surface water status and plays an essential role in the exchange of energy and water between land and atmosphere. A physical surface energy balance (SEB)-based LST downscaling method (DTsEB) is developed to downscale coarse remotely sensed thermal infrared LST products with fine-resolution visible and near-infrared data. The DTsEB method is advantageous for its ability to mechanically interrelate surface variables contributing to the spatial variation of LST, to quantitatively weigh the contributions of each related variable within a physical framework, and to efficaciously avoid the subjective selection of scaling factors and the establishment of statistical regression relationships. The applicability of the DTsEB method was tested by downscaling 12 scenes of 990 m Moderate Resolution Imaging Spectroradiometer (MODIS) and aggregated Advanced Spaceborne Thermal Emission and Reflection Radiometer (ASTER) LST products to 90 m resolution at six overpass times between 2005 and 2015 over three 9.9 km by 9.9 km cropland (mixed by grass, tree, and built-up land) study areas. Three typical LST downscaling methods, namely the widely applied TsHARP, the later developed least median square regression downscaling (LMS) and the geographically weighted regression (GWR), were introduced for intercomparison. The results showed that the DTsEB method could more effectively reconstruct the subpixel spatial variations in LST within the coarse-resolution pixels and achieve a better downscaling accuracy than the TsHARP, LMS and GWR methods. The DTsEB method yielded, on average, root mean square errors (RMSEs) of 2.01 K and 1.42 K when applied to the MODIS datasets and aggregated ASTER datasets, respectively, which were lower than those obtained with the TsHARP method, with average RMSEs of 2.41 K and 1.71 K, the LMS method, with average RMSEs of 2.35 K and 1.63 K, and the GWR method, with average RMSEs of 2.38 K and 1.64 K, respectively. The contributions of the related surface variables to the subpixel spatial variation in the LST varied both spatially and temporally and were different from each other. In summary, the DTsEB method was demonstrated to outperform the TsHARP, LMS, and GWR methods and could be used as a good alternative for downscaling LST products from coarse to fine resolution with high robustness and accuracy.", "Keywords": "Land surface temperature ; Thermal infrared remote sensing ; Surface energy balance ; Downscaling ; DTsEB", "DOI": "10.1016/j.rse.2022.113421", "PubYear": 2023, "Volume": "286", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Resources and Environment, University of Chinese Academy of Sciences, Beijing, China;ICube Laboratory, (UMR 7357), CNRS, University of Strasbourg, 300 bd <PERSON><PERSON><PERSON><PERSON>, CS 10413, F-67412 Illkirch, France;State Key Laboratory of Resources and Environment Information System, Institute of Geographic Sciences and Natural Resources Research, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Resources and Environment Information System, Institute of Geographic Sciences and Natural Resources Research, Chinese Academy of Sciences, Beijing, China;College of Resources and Environment, University of Chinese Academy of Sciences, Beijing, China;Corresponding authors at: State Key Laboratory of Resources and Environment Information System, Institute of Geographic Sciences and Natural Resources Research, Chinese Academy of Sciences, Beijing, China; College of Resources and Environment, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Resources and Environment, University of Chinese Academy of Sciences, Beijing, China;Key Laboratory of Quantitative Remote Sensing Information Technology, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing, China;Corresponding authors at: State Key Laboratory of Resources and Environment Information System, Institute of Geographic Sciences and Natural Resources Research, Chinese Academy of Sciences, Beijing, China; College of Resources and Environment, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ICube Laboratory, (UMR 7357), CNRS, University of Strasbourg, 300 bd <PERSON><PERSON><PERSON><PERSON>, CS 10413, F-67412 <PERSON>lkirch, France;State Key Laboratory of Resources and Environment Information System, Institute of Geographic Sciences and Natural Resources Research, Chinese Academy of Sciences, Beijing, China;College of Resources and Environment, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Resources and Environment Information System, Institute of Geographic Sciences and Natural Resources Research, Chinese Academy of Sciences, Beijing, China;College of Resources and Environment, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Agricultural Remote Sensing, Ministry of Agriculture/Institute of Agricultural Resources and Regional Planning, Chinese Academy of Agricultural Sciences, Beijing, China"}, {"AuthorId": 7, "Name": "Caixia Gao", "Affiliation": "Key Laboratory of Quantitative Remote Sensing Information Technology, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Lanzhou University of Technology, Lanzhou, Gansu, China"}], "References": [{"Title": "The assessment of different vegetation indices for spatial disaggregating of thermal imagery over the humid agricultural region", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "5", "Page": "1907", "JournalTitle": "International Journal of Remote Sensing"}]}, {"ArticleId": 105520453, "Title": "A refined complexity analysis of fair districting over graphs", "Abstract": "We study the NP-hard Fair Connected Districting problem recently proposed by <PERSON><PERSON><PERSON> et al. [AAMAS 2020]: Partition a vertex-colored graph into k  connected components (subsequently referred to as districts) so that in every district the most frequent color occurs at most a given number of times more often than the second most frequent color. Fair Connected Districting is motivated by various real-world scenarios where agents of different types, which are one-to-one represented by nodes in a network, have to be partitioned into disjoint districts. Herein, one strives for “fair districts” without any type being in a dominating majority in any of the districts. This is to e.g. prevent segregation or political domination of some political party. We conduct a fine-grained analysis of the (parameterized) computational complexity of Fair Connected Districting . In particular, we prove that it is polynomial-time solvable on paths, cycles, stars, and caterpillars, but already becomes NP-hard on trees. Motivated by the latter negative result, we perform a parameterized complexity analysis with respect to various graph parameters including treewidth, and problem-specific parameters, including, the numbers of colors and districts. We obtain a rich and diverse, close to complete picture of the corresponding parameterized complexity landscape (that is, a classification along the complexity classes FPT, XP, W[1]-hard, and para-NP-hard).", "Keywords": "Parameterized Algorithmics; Electoral Districting; Fairness; Social Choice on Social Networks", "DOI": "10.1007/s10458-022-09594-2", "PubYear": 2023, "Volume": "37", "Issue": "1", "JournalId": 21187, "JournalTitle": "Autonomous Agents and Multi-Agent Systems", "ISSN": "1387-2532", "EISSN": "1573-7454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Algorithmics and Computational Complexity, Technische Universität Berlin, Berlin, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Algorithmics and Computational Complexity, Technische Universität Berlin, Berlin, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Algorithmics and Computational Complexity, Technische Universität Berlin, Berlin, Germany"}], "References": [{"Title": "Algorithms for gerrymandering over graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "868", "Issue": "", "Page": "30", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Schelling games on graphs", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "301", "Issue": "", "Page": "103576", "JournalTitle": "Artificial Intelligence"}, {"Title": "Margin of victory for tournament solutions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "302", "Issue": "", "Page": "103600", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": 105520525, "Title": "AUTOMATED INFORMATION SYSTEM FOR DISCRETE MODELING OF NETWORK EPIDEMIC PROCESSES. PART 2", "Abstract": "<p>Стремительное развитие информационных технологий и внедрение их в различные сферы деятельности обуславливает их применение при построении современных корпоративных сетей, что значительно расширяет их функционал и повышает эффективность. Вместе с тем, постоянно возрастает количество атак с применением вредоносного программного обеспечения (ВПО) различного типа, способных наносить значительный ущерб. Особую угрозу для корпоративных сетей представляют вирусы, способные порождать масштабные сетевые эпидемии, деструктивное воздействие которых за последние десятилетия нанесло значительный финансовый ущерб как организациям, так и частным лицам. В представленном исследовании описан процесс проведения специализированной алгоритмизации моделирования сетевых эпидемических процессов. Разработанные алгоритмы описывают основные функции, выполняемые при моделировании эпидемий, ключевыми особенностями которых стали: возможность загрузки топологии пользователем; поддержка нескольких моделей моделирования эпидемических процессов, в том числе многоэтапных.</p><p>The rapid development of information technologies and their introduction into various fields of activity determines their use in the construction of modern corporate networks, which significantly expands their functionality and increases efficiency. At the same time, the number of attacks using various types of malware that can cause significant damage is constantly increasing. A particular threat to corporate networks is viruses that can generate large-scale network epidemics, the destructive impact of which over the past decades has caused significant financial damage to both organizations and individuals. The present study describes the process of conducting a specialized algorithmization of modeling network epidemic processes. The developed algorithms describe the main functions performed in modeling epidemics, the key features of which are: the ability to load the topology by the user; support for several models of modeling epidemic processes, including multi-stage ones.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2022.25.3.008", "PubYear": 2022, "Volume": "", "Issue": "3(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Са<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Дмитрий Николаевич", "Affiliation": ""}, {"AuthorId": 2, "Name": "Шварцк<PERSON><PERSON><PERSON>, Евгения Андреевна", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, В<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Г<PERSON><PERSON><PERSON><PERSON><PERSON>евич", "Affiliation": ""}], "References": []}, {"ArticleId": 105520534, "Title": "AUTOMATED INFORMATION SYSTEM FOR MODELING EPIDEMIC PROCESSES GENERATED BY SELF-PROPAGATING AND MUTATING <PERSON><PERSON><PERSON>RE", "Abstract": "<p>В статье представлено исследование многообразия вредоносного программного обеспечения для разработки модулей автоматизированной информационной системы моделирования эпидемических процессов, порожденных саморазмножающимися и мутирующими вредоносами. Приведена классификация саморазмножающихся и мутирующих вредоносов, приводится информационное обеспечение, определяющее параметры саморазмножения и мутации вредоносов, а также антивирусных средств защиты информации. Предложено методическое и алгоритмическое обеспечения, на базе которых были разработаны модули автоматизированной информационной системы моделирования эпидемического процесса в сети с произвольной топологией, порожденного саморазмножающимися и мутирующими вредоносами. В отличие от аналогов разработанные модули автоматизированной информационной системы способствуют моделированию процесса развития эпидемии, а также учитывают снижение способности обнаружения вредоноса в компьютерной сети антивирусным средством защиты информации из-за модификаций, вызванных механизмом мутации вредоноса. Анализируемые методические процедуры и полученные результаты моделирования могут быть востребованы для реализации риск-анализа эпидемических процессов, порожденных мутирующими вредоносами, для последующего обеспечения безопасности компьютерных сетей различного назначения.</p><p>This article is aimed at investigating the variety of malicious software for the development of modules of an automated information system for modeling epidemic processes generated by self-propagating and mutating malware. The paper describes the classification of self-propagating and mutating malware, provides information support that determines the parameters of self-propagatingand mutation of malware, as well as anti-virus information protection tools. The article proposes methodological and algorithmic support, on the basis of which modules of an automated information system for modeling the epidemic process in a network with an any topology generated by self-propagating and mutating malware were developed. Unlike analogues, the developed modules of the automated information system contribute to modeling the epidemic development process, and also take into account the decrease in the ability to detect a malware in a computer network by an antivirus information protection tool, due to modifications caused by the mutation mechanism of this very malware. The analyzed methodological procedures and the obtained modeling results can be used to implement risk analysis of epidemic processes generated by mutating malware, for subsequent security of computer networks for various purposes.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2022.25.3.004", "PubYear": 2022, "Volume": "", "Issue": "3(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Ар<PERSON><PERSON><PERSON>ова, Ксения Владимировна", "Affiliation": ""}, {"AuthorId": 2, "Name": "Ос<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Алексан<PERSON>р Але<PERSON><PERSON><PERSON>евич", "Affiliation": ""}, {"AuthorId": 3, "Name": "Сафронова, Виктория Витальевна", "Affiliation": ""}, {"AuthorId": 4, "Name": "Ос<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, В<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105520540, "Title": "Tensor slicing and optimization for multicore NPUs", "Abstract": "Although code generation for Convolution Neural Network (CNN) models has been extensively studied, performing efficient data slicing and parallelization for highly-constrained Multicore Neural Processor Units (NPUs) is still a challenging problem. Given the size of convolutions&#x27; input/output tensors and the small footprint of NPU on-chip memories, minimizing memory transactions while maximizing parallelism and MAC utilization are central to any effective solution. This paper proposes a TensorFlow XLA/LLVM compiler optimization pass for Multicore NPUs, called Tensor Slicing Optimization (TSO), which: (a) maximizes convolution parallelism and memory usage across NPU cores; and (b) reduces data transfers between host and NPU on-chip memories by using DRAM memory burst time estimates to guide tensor slicing. To evaluate the proposed approach, a set of experiments was performed using the NeuroMorphic Processor (NMP), a multicore NPU containing 32 RISC-V cores extended with novel CNN instructions. Experimental results show that TSO is capable of identifying the best tensor slicing that minimizes execution time for a set of CNN models. Speed-ups of up to 21.7% result when comparing the TSO burst-based technique to a no-burst data slicing approach. To validate the generality of the TSO approach, the algorithm was also ported to the Glow Machine Learning framework. The performance of the models were measured on both Glow and TensorFlow XLA/LLVM compilers, revealing similar results.", "Keywords": "", "DOI": "10.1016/j.jpdc.2022.12.008", "PubYear": 2023, "Volume": "175", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Computing, University of Campinas (UNICAMP), Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing, University of Campinas (UNICAMP), Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Electronic and Telecommunications Research Institute (ETRI), Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electronic and Telecommunications Research Institute (ETRI), Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SilicoNeuro/AiM Future, Republic of Korea"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "SilicoNeuro/AiM Future, Republic of Korea"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "<PERSON>gi<PERSON>ore, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Institute of Computing, University of Campinas (UNICAMP), Brazil"}], "References": []}, {"ArticleId": 105520559, "Title": "Highly efficient, decoupled and unconditionally stable numerical schemes for a modified phase-field crystal model with a strong nonlinear vacancy potential", "Abstract": "In this paper, we consider efficient and energy-stable numerical schemes for solving a modified phase-field crystal model with a strong nonlinear vacancy potential. By combining the modified exponential SAV approach and the relaxed SAV approach, we introduce a new auxiliary variable to reformulate the model. We adopt the backward Euler formula and the second-order backward difference formula (BDF2) to develop the first- and second-order time-accurate schemes, respectively. The energy dissipation law can be proved for all proposed schemes. In each time step, the computation is totally decoupled. Non-local variables can be explicitly updated and the local variables can be computed by solving an elliptic type equation with constant coefficients. Numerous numerical experiments in 2D and 3D are carried out to show the accuracy and energy stability of the proposed schemes.", "Keywords": "Modified phase-field crystal ; Unconditional energy stability ; Exponential SAV ; Vacancy", "DOI": "10.1016/j.camwa.2022.12.011", "PubYear": 2023, "Volume": "132", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou 510275, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou 510275, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou 510275, China;Guangdong Province Key Laboratory of Computational Science, Sun Yat-sen University, Guangzhou 510275, China;Corresponding author at: School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou 510275, China"}], "References": [{"Title": "A variant of stabilized-scalar auxiliary variable (S-SAV) approach for a modified phase-field surfactant model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "261", "Issue": "", "Page": "107825", "JournalTitle": "Computer Physics Communications"}, {"Title": "A linearly second-order, unconditionally energy stable scheme and its error estimates for the modified phase field crystal equation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "104", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "New efficient time-stepping schemes for the anisotropic phase-field dendritic crystal growth model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "109", "Issue": "", "Page": "204", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "High-order time-accurate, efficient, and structure-preserving numerical methods for the conservative <PERSON>–<PERSON> model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "160", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 105520560, "Title": "Elastic and strength properties of statistical volume elements: Determination of isotropic and homogeneous size limits", "Abstract": "In this paper, a method is presented to describe the statistical behavior of mesoscale material property fields, which are useful in macroscopic fracture simulation. The use of representative volume element-based properties can result in unsatisfactory fracture patterns because this approach eliminates random variations. In contrast, statistical volume elements can approximate random and inhomogeneous material properties. We compare two material property convergence metrics based on 1) vanishing variations of a property and 2) the convergence of property mean value versus volume element size. We examine the trends with which the properties of a two-phase composite tend toward homogeneous and isotropic limits. Compared to fracture properties, elastic properties reach their homogeneous and isotropic limit at smaller sizes. Accordingly, a volume element size can be chosen such that only the apparent fracture strength remains random and inhomogeneous. This reduces the computational cost and has proven useful in capturing realistic fracture results. In addition, the geometry of the mesoscale partitioning scheme has an important effect on homogenized properties. The intersection of straight edges with inclusions results in nonphysical size effects, increases anisotropy of properties, and results in 30 to 60 times larger representative volume elements compared to a partitioning scheme based on Voronoi homogenization.", "Keywords": "Statistical volume element ; Homogenization ; Homogeneous ; Isotropic ; Size effect ; Voronoi", "DOI": "10.1016/j.compstruc.2022.106959", "PubYear": 2023, "Volume": "277-278", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tickle College of Engineering, University of Tennessee, 1506 Middle Drive, Knoxville, TN 37196, United States;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tickle College of Engineering, University of Tennessee, 1506 Middle Drive, Knoxville, TN 37196, United States;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Engineering, University of St. Thomas, 2115 Summit Avenue, OSS 100, St. Paul, MN 55105, United States"}], "References": []}, {"ArticleId": 105520564, "Title": "A polynomial-augmented RBF collocation method for fourth-order boundary value problems", "Abstract": "In this paper, we consider a polynomial-augmented radial basis function collocation method (RBFCM) for solving fourth-order multi-dimensional elliptic partial differential equations (PDEs). Instead of choosing the RBF centres inside the problem domain, we use fictitious centres that can be located outside it to deal with the double boundary conditions of the fourth-order PDEs, and to also improve the computational accuracy. Furthermore, additional polynomial constraints applied to the approximation matrix of the RBFCM using fictitious centres, significantly alleviate the sensitivity of the method to the variation of the shape parameter, whereby it makes the RBFCM more practical for solving partial differential equations. We also apply the proposed method to a fourth-order elliptic PDE with random forcing terms (input data of the model). The input data are assumed to depend on a finite number of random variables at the collocation points. Numerical experiments are presented to demonstrate the effectiveness of the proposed method in terms of the selection of the shape parameter. In addition, the numerical results show that the proposed method achieves a higher accuracy than the conventional RBFCM.", "Keywords": "Radial basis functions ; Shape parameter ; Fictitious centres ; Fourth-order partial differential equations", "DOI": "10.1016/j.camwa.2022.12.014", "PubYear": 2023, "Volume": "133", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Shanghai University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Shanghai University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhu", "Affiliation": "School of Mathematics and Natural Sciences, The University of Southern Mississippi, USA;Corresponding author"}], "References": [{"Title": "Two-step MPS-MFS ghost point method for solving partial differential equations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "94", "Issue": "", "Page": "38", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 105520584, "Title": "Plant pest invasions, as seen through news and social media", "Abstract": "Invasion by exotic pests into new geographic areas can cause major disturbances in forest and agricultural systems. Early response can greatly improve containment efforts, underscoring the importance of collecting up-to-date information about the locations where pest species are being observed. However, existing invasive species databases have limitations in both extent and rapidity. The spatial extent is limited by costs and there are delays between species establishment, official recording, and consolidation. Local online news outlets have the potential to provide supplemental spatial coverage worldwide and social media has the potential to provide direct observations and denser historical data for modeling. Gathering data from these online sources presents its own challenges and their potential contribution to historical tracking of pest invasions has not previously been tested. To this end, we examine the practical considerations for using three online aggregators, the Global Database of Events, Language and Tone (GDELT), Google News, and a commercial media listening platform, Brandwatch, to support pest biosurveillance. Using these tools, we investigate the presence and nature of cogent mentions of invasive species in these sources by conducting case studies of online news and Twitter excerpts regarding two invasive plant pests, Spotted Lanternfly and Tuta absoluta. Our results using past data demonstrate that online news and social media may provide valuable data streams to supplement official sources describing pest invasions.", "Keywords": "Text mining ; Invasive pests ; Twitter ; Online news ; GDELT ; Geospatial–temporal", "DOI": "10.1016/j.compenvurbsys.2022.101922", "PubYear": 2023, "Volume": "100", "Issue": "", "JournalId": 1688, "JournalTitle": "Computers, Environment and Urban Systems", "ISSN": "0198-9715", "EISSN": "1873-7587", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Natural Resources, Biltmore Hall 4008M — Campus Box 8004, North Carolina State University, 2800 Faucette Dr. <PERSON>, NC 27695 USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Natural Resources, Biltmore Hall 4008M — Campus Box 8004, North Carolina State University, 2800 Faucette Dr. <PERSON>, NC 27695 USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Natural Resources, Biltmore Hall 4008M — Campus Box 8004, North Carolina State University, 2800 Faucette Dr. <PERSON>, NC 27695 USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Natural Resources, Biltmore Hall 4008M — Campus Box 8004, North Carolina State University, 2800 Faucette Dr. <PERSON>, NC 27695 USA"}], "References": []}, {"ArticleId": 105520592, "Title": "Beyond digital shadows: A Digital Twin for monitoring earthwork operation in large infrastructure projects", "Abstract": "Current research on Digital Twin (DT) is largely focused on the performance of built assets in their operational phases as well as on urban environment. However, Digital Twin has not been given enough attention to construction phases, for which this paper proposes a Digital Twin framework for the construction phase, develops a DT prototype and tests it for the use case of measuring the productivity and monitoring of earthwork operation. The DT framework and its prototype are underpinned by the principles of versatility , scalability , usability and automation to enable the DT to fulfil the requirements of large-sized earthwork projects and the dynamic nature of their operation. Cloud computing and dashboard visualisation were deployed to enable automated and repeatable data pipelines and data analytics at scale and to provide insights in near-real time. The testing of the DT prototype in a motorway project in the Northeast of England successfully demonstrated its ability to produce key insights by using the following approaches: (i) To predict equipment utilisation ratios and productivities; (ii) To detect the percentage of time spent on different tasks (i.e., loading, hauling, dumping, returning or idling), the distance travelled by equipment over time and the speed distribution; and (iii) To visualise certain earthwork operations.", "Keywords": "Machine learning; Digital Twin; Earthwork; Data analytics; Data pipeline", "DOI": "10.1007/s43503-022-00009-5", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 101148, "JournalTitle": "AI in Civil Engineering", "ISSN": "", "EISSN": "2730-5392", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Northumbria University, Newcastle Upon Tyne, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Northumbria University, Newcastle Upon Tyne, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cambridge University, Cambridge, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Newcastle University, Newcastle Upon Tyne, UK"}], "References": [{"Title": "Peeking into the void: Digital twins for construction site logistics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "121", "Issue": "", "Page": "103264", "JournalTitle": "Computers in Industry"}, {"Title": "Application of digital twin and IoT concepts for solving the tasks of hydraulically actuated heavy equipment lifecycle management", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "194", "JournalTitle": "International Journal of Engineering Systems Modelling and Simulation"}]}, {"ArticleId": 105520778, "Title": "A NIR pH sensitive fluorescent strategy for ratiometric detection of reactive oxygen species and its application in the imaging of arthritis", "Abstract": "Inflammation, a common disease triggered by oxidative stress, could produce reactive oxygen species (ROSs) of high level in tissues with a wide pH range. However, non-invasive in vivo monitoring ROS in both acidic and alkaline tissues remains a great challenge. In this work, a novel pH sensitive fluorescent probe, Hcy-OH, for ratiometric detection and imaging of ROSs was developed, exhibiting specific response to different ROSs at varying pH values. Hcy-OH exhibited selectivity for ONOO<sup>-</sup> under acidic condition, ClO<sup>-</sup> under neutral condition and <sup>1</sup>O<sub>2</sub> under alkaline condition. Encouraged by the remarkable sensing performance, Hcy-OH was applied to visualize exogenous and endogenous ROSs cellularly, with responsive fluorescence and ratiometric profile for indication. This sensing scheme was proved to be workable for inflamed tissue imaging, confirming its application for accurate monitoring of arthritis at organism level. The elaborate receptor Hcy-OH could serve as a powerful tool to reveal the relationship between the physiology and pathology of inflammation.", "Keywords": "PH sensitive ; Reactive oxygen species (ROSs) ; NIR emission ; Ratiometric ; Arthritis imaging", "DOI": "10.1016/j.snb.2022.133262", "PubYear": 2023, "Volume": "379", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China;Key Laboratory of Analytical Chemistry for Life Science in Universities of Shandong, Qingdao 266042, PR China;Shandong Key Laboratory of Biochemical Analysis, Qingdao 266042, PR China;Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China;Key Laboratory of Analytical Chemistry for Life Science in Universities of Shandong, Qingdao 266042, PR China;Shandong Key Laboratory of Biochemical Analysis, Qingdao 266042, PR China;Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China;Key Laboratory of Analytical Chemistry for Life Science in Universities of Shandong, Qingdao 266042, PR China;Shandong Key Laboratory of Biochemical Analysis, Qingdao 266042, PR China;Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China;Key Laboratory of Analytical Chemistry for Life Science in Universities of Shandong, Qingdao 266042, PR China;Shandong Key Laboratory of Biochemical Analysis, Qingdao 266042, PR China;Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China;Key Laboratory of Analytical Chemistry for Life Science in Universities of Shandong, Qingdao 266042, PR China;Shandong Key Laboratory of Biochemical Analysis, Qingdao 266042, PR China;Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China;Key Laboratory of Analytical Chemistry for Life Science in Universities of Shandong, Qingdao 266042, PR China;Shandong Key Laboratory of Biochemical Analysis, Qingdao 266042, PR China;Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, Qingdao University of Science and Technology, Qingdao 266042, PR China;Corresponding authors at: College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China;Key Laboratory of Analytical Chemistry for Life Science in Universities of Shandong, Qingdao 266042, PR China;Shandong Key Laboratory of Biochemical Analysis, Qingdao 266042, PR China;Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, Qingdao University of Science and Technology, Qingdao 266042, PR China;Corresponding authors at: College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China"}], "References": []}, {"ArticleId": 105520811, "Title": "Reinforcement learning under temporal logic constraints as a sequence modeling problem", "Abstract": "Reinforcement learning (RL) under temporal logic typically suffers from slow propagation for credit assignment. Inspired by recent advancements called trajectory transformer in machine learning, the reinforcement learning under Temporal Logic (TL) is modeled as a sequence modeling problem in this paper, where an agent utilizes the transformer to fit the optimal policy satisfying the Finite Linear Temporal Logic ( LTL f ) tasks. To combat the sparse reward issue, dense reward functions for LTL f are designed. For the sake of reducing the computational complexity, a sparse transformer with local and global attention is constructed to automatically conduct credit assignment, which removes the time-consuming value iteration process. The optimal action is found by the beam search performed in transformers. The proposed method generates a series of policies fitted by sparse transformers, which has sustainably high accuracy in fitting the demonstrations. At last, the effectiveness of the proposed method is demonstrated by simulations in Mini-Grid environments.", "Keywords": "", "DOI": "10.1016/j.robot.2022.104351", "PubYear": 2023, "Volume": "161", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, Beijing Institute of Technology, No. 5, Zhong Guan Cun South Street, Haidian District, Beijing, 100081, China;Department of Biomedical Engineering, National University of Singapore, 4 Engineering Drive 3, Singapore, 117583, Singapore;Institute for Infocomm Research, A*STAR, 1 Fusionopolis Way, Singapore, 138632, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, Beijing Institute of Technology, No. 5, Zhong Guan Cun South Street, Haidian District, Beijing, 100081, China;Corresponding author"}, {"AuthorId": 3, "Name": "Qing<PERSON> Yang", "Affiliation": "Department of Automation, Beijing Institute of Technology, No. 5, Zhong Guan Cun South Street, Haidian District, Beijing, 100081, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Yu", "Affiliation": "Department of Biomedical Engineering, National University of Singapore, 4 Engineering Drive 3, Singapore, 117583, Singapore"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Infocomm Research, A*STAR, 1 Fusionopolis Way, Singapore, 138632, Singapore"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute for Infocomm Research, A*STAR, 1 Fusionopolis Way, Singapore, 138632, Singapore"}], "References": []}, {"ArticleId": 105520829, "Title": "An integrated model of prosocial crowdfunding decision: Three utility components and three informational cues", "Abstract": "Prosocial crowdfunding has contributed to tackling social problems by expanding financial access to social entrepreneurs. Despite the growing body of research, our understanding of prosocial crowdfunding decisions is still fragmented as few research has provided a comprehensive view of how various motives and informational cues interactively influence funding success. Drawing on a wide range of theories in economics and psychology, we proposed an integrated conceptual framework that delineates the dynamic relationships between potential supporters’ three utilities ( i.e. , financial, other-focused, and self-focused) and three informational cues ( i.e. , textual, pictorial, and numerical informational cues) embedded in the crowdfunding projects. Then we developed and empirically tested our hypotheses using massive data from Kiva ( n  = 142,578). The results of our study showed that funding success increases when borrowers smile (H1) or when the target loan amount is smaller (H2). Additionally, the negative relationship between funding success and the target loan amount is moderated by the intensity of smile only when more (vs fewer) social-tie words are mentioned (H3 &amp; H4). Our findings not only shed light on how diverse motives dynamically interact with subtle cues to affect supporters’ funding decisions but also provide practical implications for social entrepreneurs who are eager to improve funding success rates.", "Keywords": "Crowdfunding ; Prosocial behaviors ; Big data ; AI (artificial intelligence) ; Facial expression detection ; Kiva", "DOI": "10.1016/j.elerap.2022.101233", "PubYear": 2023, "Volume": "57", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hanyang School of Business, Hanyang University, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Business, University of Central Arkansas, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hanyang School of Business, Hanyang University, South Korea;Corresponding author at: Hanyang School of Business, Hanyang University, 222 Wangsimni-ro, Seongdong-gu, Seoul 04763, South Korea"}, {"AuthorId": 4, "Name": "Hyun S<PERSON> Shin", "Affiliation": "Hanyang School of Business, Hanyang University, South Korea"}], "References": [{"Title": "More than a feeling: Investigating the contagious effect of facial emotional expressions on investment decisions in reward-based crowdfunding", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "113326", "JournalTitle": "Decision Support Systems"}, {"Title": "The impact of soft information extracted from descriptive text on crowdfunding performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101002", "JournalTitle": "Electronic Commerce Research and Applications"}]}, {"ArticleId": 105520830, "Title": "Towards interpreting vulnerability of object detection models via adversarial distillation", "Abstract": "Recent works have shown that deep learning models are highly vulnerable to adversarial examples, limiting the application of deep learning in security-critical systems. This paper aims to interpret the vulnerability of deep learning models to adversarial examples. We propose adversarial distillation to illustrate that adversarial examples are generalizable data features. Deep learning models are vulnerable to adversarial examples because models do not learn this data distribution. More specifically, we obtain adversarial features by introducing a generation and extraction mechanism. The generation mechanism generates adversarial examples, which mislead the source model trained on the original clean samples. The extraction term removes the original features and selects valid and generalizable adversarial features. Valuable adversarial features guide the model to learn the data distribution of adversarial examples and realize the model’s generalization on the adversarial dataset. Extensive experimental evaluations have proved the excellent generalization performance of the adversarial distillation model. Compared with the normally trained model, the mAP has increased by 2.17% on their respective test sets, while the mAP on the opponent’s test set is very low. The experimental results further prove that adversarial examples are also generalizable data features, which obey a different data distribution from the clean data.", "Keywords": "Adversarial examples ; Interpretability ; Object detection ; Deep learning", "DOI": "10.1016/j.jisa.2022.103410", "PubYear": 2023, "Volume": "72", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyberspace Science and Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 3, "Name": "Mingfeng Lu", "Affiliation": "School of Information and Electronics, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Cyberspace Science and Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 7, "Name": "<PERSON>zhang Li", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China;Corresponding author"}], "References": [{"Title": "Boosting cross‐task adversarial attack with random blur", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>an <PERSON>; Mingfeng Lu", "PubYear": 2022, "Volume": "37", "Issue": "10", "Page": "8139", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 105520925, "Title": "Technology and the private sector: Language teachers’ perspectives toward technology and the role of CALL training in professional development", "Abstract": "English teachers in Vietnam have integrated technology in education for the past several years, particularly those who are working in the private sector. Regardless of the high number of Vietnamese teachers working in this sector, how they perceive technology integration and how important it is to their professional development have not yet been thoroughly studied. Therefore, this study sought to provide evidence to understand Vietnamese teachers’ perspectives toward technology in more detail and shed light on how to support them with technology training so that the training not only improves their teaching skills but also helps facilitate their professional development. The study employed a mixed-methods approach with classroom observations, semi-structured interviews, and a questionnaire survey. Data collected were analyzed by using thematic analysis and triangulated to ascertain the final findings. The results have shown that the language teachers in this study expressed enthusiasm toward technology regardless of the insufficient training and the challenges of technology self-education. Interview data also revealed the importance of a community of practice to the participants as it encouraged them to learn and overcome technology-related difficulties. The community in this study has also played a key role in the relationship between technology education and teachers’ professional development in their workplace. Finally, based on <PERSON> and Malloch’s (2011) concept of workplace learning and the results of data analysis, the study advocates for a comprehensive three-level CALL training scheme which are individual, peer, and institutional that can promote technologies and facilitate support teachers’ professional development © 2022 <PERSON><PERSON>", "Keywords": "Call training; In-service teachers; Professional development; Teacher development; The private sector", "DOI": "10.29140/jaltcall.v18n3.654", "PubYear": 2024, "Volume": "18", "Issue": "3", "JournalId": 67259, "JournalTitle": "The JALT CALL Journal", "ISSN": "", "EISSN": "1832-4215", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of International Culture and Communication Studies, Waseda University, Japan"}], "References": []}, {"ArticleId": 105520951, "Title": "Cancer Identification in Enteric Nervous System Preclinical Images Using Handcrafted and Automatic Learned Features", "Abstract": "<p>Chronic degenerative diseases affect Enteric Neuron Cells (ENC) and Enteric Glial Cells (EGC) in shape and quantity. Thus, searching for automatic methods to evaluate when these cells are affected is quite opportune. In addition, preclinical imaging analysis is outstanding because it is non-invasive and avoids exposing patients to the risk of death or permanent disability. We aim to identify a specific cancer experimental model (Walker-256 tumor) in the Enteric Nervous System (ENS) cells. The ENS image database used in our experimental evaluation comprises 1248 images taken from thirteen rats distributed in two classes: control/healthy or sick. The images were created with three distinct contrast settings targeting different ENS cells: ENC, EGC, or both. We extracted handcrafted and non-handcrafted features to provide a comprehensive classification approach using SVM as the core classifier. We also applied Late Fusion techniques to evaluate the complementarity between feature sets obtained in different scenarios. In the best case, we achieved an F1-score of 0.9903 by combining classifiers built from different image types (ENC and EGC), using Local Phase Quantization (LPQ) features.</p>", "Keywords": "Enteric Nervous system; Pattern recognition; Preclinical Images; Walker-256 Tumor; Image disease recognition; Machine learning", "DOI": "10.1007/s11063-022-11114-y", "PubYear": 2023, "Volume": "55", "Issue": "5", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Informática, Universidade Estadual de Maringá, Maringá, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Informática, Universidade Estadual de Maringá, Maringá, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Instituto Federal do Paraná, Pinhais, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Departamento de Ciências Morfológicas, Universidade Estadual de Maringá, Maringá, Brazil"}, {"AuthorId": 5, "Name": "Sara R. <PERSON>", "Affiliation": "Universidade Estadual do Oeste do Paraná, Francisco Beltrão, Brazil"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Ingegneria dell’Informazione, Università degli Studi di Padova, Padova, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Universidade Federal de Pernambuco, Recife, Brazil"}, {"AuthorId": 8, "Name": "<PERSON><PERSON> M. <PERSON>", "Affiliation": "Departamento de Informática, Universidade Estadual de Maringá, Maringá, Brazil"}], "References": [{"Title": "Automatic chronic degenerative diseases identification using enteric nervous system images", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "22", "Page": "15373", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Detecting skin lesions fusing handcrafted features in image network ensembles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> S. S.", "PubYear": 2023, "Volume": "82", "Issue": "2", "Page": "3155", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 105521135, "Title": "The class imbalance problem in deep learning", "Abstract": "<p>Deep learning has recently unleashed the ability for Machine learning (ML) to make unparalleled strides. It did so by confronting and successfully addressing, at least to a certain extent, the knowledge bottleneck that paralyzed ML and artificial intelligence for decades. The community is currently basking in deep learning’s success, but a question that comes to mind is: have all of the issues previously affecting machine learning systems been solved by deep learning or do some issues remain for which deep learning is not a bulletproof solution? This question in the context of the class imbalance becomes a motivation for this paper. Imbalance problem was first recognized almost three decades ago and has remained a critical challenge at least for traditional learning approaches. Our goal is to investigate whether the tight dependency between class imbalances, concept complexities, dataset size and classifier performance, known to exist in traditional learning systems, is alleviated in any way in deep learning approaches and to what extent, if any, network depth and regularization can help. To answer these questions we conduct a survey of the recent literature focused on deep learning and the class imbalance problem as well as a series of controlled experiments on both artificial and real-world domains. This allows us to formulate lessons learned about the impact of class imbalance on deep learning models, as well as pose open challenges that should be tackled by researchers in this field.</p>", "Keywords": "Deep learning; Class imbalance; Concept complexity", "DOI": "10.1007/s10994-022-06268-8", "PubYear": 2024, "Volume": "113", "Issue": "7", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "Kushan<PERSON><PERSON>", "Affiliation": "Department of Computing Science, University of Alberta, Edmonton, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Research Council of Canada, Ottawa, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, American University, Washington, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, University of Ottawa, Ottawa, Canada; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Virginia Commonwealth University, Richmond, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, American University, Washington, USA"}], "References": [{"Title": "A cost-sensitive convolution neural network learning for control chart pattern recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113275", "JournalTitle": "Expert Systems with Applications"}, {"Title": "2D geometric shapes dataset – for machine learning and pattern recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "", "Page": "106090", "JournalTitle": "Data in Brief"}, {"Title": "Optimization of Convolutional Neural Networks for Imbalanced Set Classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "660", "JournalTitle": "Procedia Computer Science"}, {"Title": "Multi-class imbalanced big data classification on Spark", "Authors": "<PERSON> IV; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106598", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Assessing the data complexity of imbalanced datasets", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>o C.P. <PERSON>", "PubYear": 2021, "Volume": "553", "Issue": "", "Page": "83", "JournalTitle": "Information Sciences"}, {"Title": "Addressing the multi-label imbalance for neural networks: An approach based on stratified mini-batches", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "435", "Issue": "", "Page": "91", "JournalTitle": "Neurocomputing"}, {"Title": "A survey on generative adversarial networks for imbalance problems in computer vision tasks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "27", "JournalTitle": "Journal of Big Data"}, {"Title": "Maximizing minority accuracy for imbalanced pattern classification problems using cost-sensitive Localized Generalization Error Model", "Authors": "<PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "107178", "JournalTitle": "Applied Soft Computing"}, {"Title": "An empirical study of data intrinsic characteristics that make learning from imbalanced data difficult", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115297", "JournalTitle": "Expert Systems with Applications"}, {"Title": "BBW: a batch balance wrapper for training deep neural networks on extremely imbalanced datasets with few minority samples", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "6", "Page": "6723", "JournalTitle": "Applied Intelligence"}, {"Title": "On the joint-effect of class imbalance and overlap: a critical review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "8", "Page": "6207", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 105521236, "Title": "Bio-Inspired Transparent Soft Jellyfish Robot", "Abstract": "<p>Jellyfish are among the widely distributed nature creatures that can effectively control the fluidic flow around their transparent soft body, thus achieving movements in the water and camouflage in the surrounding environments. Till now, it remains a challenge to replicate both transparent appearance and functionalities of nature jellyfish in synthetic systems due to the lack of transparent actuators. In this work, a fully transparent soft jellyfish robot is developed to possess both transparency and bio-inspired omni motions in water. This robot is driven by transparent dielectric elastomer actuators (DEAs) using hybrid silver nanowire networks and conductive polymer poly(3,4-ethylenedioxythiophene):poly(styrenesulfonate)/waterborne polyurethane as compliant electrodes. The electrode exhibits large stretchability, low stiffness, high transmittance, and excellent conductivity at large strains. Consequently, the highly transparent DEA based on this hybrid electrode, with Very-High-Bond membranes as dielectric layers and polydimethylsiloxane as top coating, can achieve a maximum area strain of 146% with only 3% hysteresis loss. Driven by this transparent DEA, the soft jellyfish robot can achieve vertical and horizontal movements in water, by mimicking the actual pulsating rhythm of an <i>Aurelia aurita</i>. The bio-inspired robot can serve multiple functions as an underwater soft robot. The hybrid electrodes and bio-inspired design approach are potentially useful in a variety of soft robots and flexible devices.</p>", "Keywords": "bio-inspiration;dielectric elastomers;soft robots;transparent electrodes", "DOI": "10.1089/soro.2022.0027", "PubYear": 2023, "Volume": "10", "Issue": "3", "JournalId": 12246, "JournalTitle": "Soft Robotics", "ISSN": "2169-5172", "EISSN": "2169-5180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Singapore Institute of Manufacturing Technology, Agency for Science, Technology and Research (A*STAR), Singapore.;Department of Mechanical Engineering, National University of Singapore, Singapore."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National University of Singapore, Singapore."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Singapore Institute of Manufacturing Technology, Agency for Science, Technology and Research (A*STAR), Singapore."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science and Engineering, Chinese University of Hong Kong at Shenzhen, Shenzhen, China.;Shenzhen Institute of Artificial Intelligence and Robotics for Society, Shenzhen, China."}], "References": []}, {"ArticleId": 105521306, "Title": "Unsupervised image-to-image translation via long-short cycle-consistent adversarial networks", "Abstract": "<p>Cycle consistency conducts generative adversarial networks from aligned image pairs to unpaired training sets and can be applied to various image-to-image translations. However, the accumulation of errors that may occur during image reconstruction can affect the realism and quality of the generated images. To address this, we exploit a novel long and short cycle-consistent loss. This new loss is simple and easy to implement. Our dual-cycle constrained cross-domain image-to-image translation method can handle error accumulation and enforce adversarial learning. When image information is migrated from one domain to another, the cycle consistency-based image reconstruction constraint should be constrained in both short and long cycles to eliminate error accumulation. We adopt the cascading manner with dual-cycle consistency, where the reconstructed image in the first cycle can be cast as the new input to the next cycle. We show a distinct improvement over baseline approaches in most translation scenarios. With extensive experiments on several datasets, the proposed method is superior to several tested approaches.</p>", "Keywords": "GAN; Image-to-image translation; Dual learning; Cycle consistency", "DOI": "10.1007/s10489-022-04389-0", "PubYear": 2023, "Volume": "53", "Issue": "14", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Data Science and Statistics, School of Statistics and Management, Shanghai University of Finance and Economics, Shanghai, China"}, {"AuthorId": 2, "Name": "Haibo Shi", "Affiliation": "Institute of Data Science and Statistics, School of Statistics and Management, Shanghai University of Finance and Economics, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CAD Research Center, College of Electronics and Information Engineering, Tongji University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shanghai University of Finance and Economics Zhejiang College, Jinhua, China"}], "References": [{"Title": "DRIT++: Diverse Image-to-Image Translation via Disentangled Representations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "10-11", "Page": "2402", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "IOSUDA: an unsupervised domain adaptation with input and output space alignment for joint optic disc and cup segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "6", "Page": "3880", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 105521411, "Title": "Review of methods for early melanoma computer vision detection", "Abstract": "Melanoma is one of the most aggressive forms of cancer, which can be treated only with early detection of the disease. The article discusses the existing algorithms and methods of visual diagnosis of melanoma. The systems of automatic diagnosis of dermatoscopic images and the methods used by them are also considered. The article considers the limitations hindering the development of automatic diagnosis systems: the lack of relevant domestic data sets that allow training artificial intelligence models, insufficient level of patient metadata accounting, low coverage of the population for the presence of melanoma during routine examinations. A variant of building a decision support system by general practitioners in the analysis of dermatoscopic images of the skin is proposed.", "Keywords": "early detection of melanoma;automatic diagnosis systems;artificial intelligence in medicine;раннее обнаружение меланомы;системы автоматической диагностики;искусственный интеллект в медицине", "DOI": "10.15514/ISPRAS-2022-34(4)-17", "PubYear": 2022, "Volume": "34", "Issue": "4", "JournalId": 9407, "JournalTitle": "Proceedings of the Institute for System Programming of the RAS", "ISSN": "2079-8156", "EISSN": "2220-6426", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Орловская областная клиническая больница"}], "References": []}, {"ArticleId": 105521419, "Title": "Classification of Rock Core Sensing Images Using Convolutional Neural Network Methods", "Abstract": "The development of underground spaces such as tunnels, subways, logistics warehouses, and complex facilities is continuing. However, owing to poor planning and reckless expansion, there has also been an increase in underground accidents. As such, it is important to obtain accurate geotechnical data on underground spaces for optimal construction outcomes and to ensure the safety of workers. Borehole cores contain essential geological information towards achieving these ends; however, rock classification using borehole cores takes a long time and the classification depends on the interpreter. To address these issues, we performed rock classification based on borehole sensing images using a convolutional neural network (CNN) combined with deep learning techniques. The data used for the training were collected from images of borehole cores in Hang-dong, Guro-gu, Seoul, and Hyeol-dong, Taebaek, Republic of Korea. We used the collected two datasets: a rod dataset labeled by the rock type of the borehole core rod unit and a grid dataset labeled by the rock type unit. The rock types were classified into basalt, gneiss, limestone, mudstone, and shale. In addition, mixed-rock and loss classes were added to the classifications. For the image classification process, we proposed three methods: general deep-learning-based image classification, multiregion image classification, and multiregion image classification using a scoring process. An experiment was conducted to validate these methods. A maximum accuracy of 99.02% was achieved in the validation process. The proposed methods introduced here are expected to reduce the time and costs associated with creating geotechnical databases. © 2022 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "borehole core; convolutional neural network; deep learning; rock classification", "DOI": "10.18494/SAM3970", "PubYear": 2022, "Volume": "34", "Issue": "12", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Korea Institute of Geoscience and Mineral Resources (KIGAM), 124 Gwahakro, Yuseong-gu, Daejeon, 34132, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Korea Institute of Geoscience and Mineral Resources (KIGAM), 124 Gwahakro, Yuseong-gu, Daejeon, 34132, South Korea"}], "References": []}, {"ArticleId": 105521461, "Title": "R-ProjNet: an optimal rotated-projection neural network for wood segmentation from point clouds", "Abstract": "This work aims to provide a deep learning framework to segment woods from tree point clouds. We develop a novel preprocessing layer before the classical sampling and convolution structure called the projection layer to organize 3D point clouds into 2D points. Input data are transformed into projection data along axis and planes for the subsequent convolution process, which helps decrease the complexity of networks. In order to obtain optimal and effective projection data for capturing local features, we formulate the 2D transformation in the learning process using two learnable angle parameters. The projection map is updated in the learning process for capturing geometric structure information, which plays an important role in wood point segmentation. Experiments show that we have achieved the loss and misclassification error of 0.41% and 8%, respectively, on wood points extraction from handheld laser scanning data. Besides, we also achieve the correctness, completeness and F -score of 90.4%, 91.5% and 0.91, respectively, in a public vehicle laser scanning dataset.", "Keywords": "Deep learning ; laser scanning ; segmentation ; rotation ; projection", "DOI": "10.1080/2150704X.2022.2163203", "PubYear": 2023, "Volume": "14", "Issue": "1", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Nanjing Forestry University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Nanjing Forestry University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Geomatics Engineering, University of Calgary, Calgary, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Xu", "Affiliation": "College of Information Science and Technology, Nanjing Forestry University, Nanjing, China"}], "References": [{"Title": "Street tree segmentation from mobile laser scanning data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "18", "Page": "7145", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Individual tree crown segmentation from airborne LiDAR data using a novel Gaussian filter and energy function minimization-based approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "256", "Issue": "", "Page": "112307", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "An improved space colonization algorithm with DBSCAN clustering for a single tree skeleton extraction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "10", "Page": "3692", "JournalTitle": "International Journal of Remote Sensing"}]}, {"ArticleId": 105521541, "Title": "Covid-19 and mobile payment in Belgium: Closing the digital divide or just for the young, social, and impulsive?", "Abstract": "Experts and industry reports agree that the COVID-19 crisis spurred the adoption of new retail technologies, like mobile payment. However, empirical academic evidence that compares their adoption and usage before, during, and after the crisis remains scarce. So far, academic mobile payment research has focussed almost entirely on the different building blocks of technological acceptance models, like perceived usefulness and ease of use, and their role in explaining intention to use. We need to learn more about the profile of the actual user. In this Belgian study, we investigate the evolution in mobile adoption based on survey data from 2019 to 2020 (2019: N = 897; 2020: N = 895). We examine differences in the profile of mobile payers in terms of their socio-demographics, retail, and social media behaviours. The pandemic triggered a clear uplift in mobile payment users between 2019 to 2020. Nonetheless, striking differences in socio-demographic profile and retail patronage remain. Our data shows that there is still inequality in adoption, related to age and social grade. We also observe a clear association between general impulse buying tendency and mobile payment. The link between internet/online shopping and mobile payment is firmly established. Finally, mobile adoption is related to the use of Instagram and Facebook. Consequences for retailers, researchers and public officers are further discussed.", "Keywords": "Mobile payment; Social network sites; Impulse buying tendency; Convenience stores; Digital divide", "DOI": "10.1007/s10660-022-09655-4", "PubYear": 2023, "Volume": "23", "Issue": "3", "JournalId": 2555, "JournalTitle": "Electronic Commerce Research", "ISSN": "1389-5753", "EISSN": "1572-9362", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Business, Marketing & Consumer Behavior Cluster, Imec-SMIT, Vrije Universiteit Brussel, Brussels, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Business, Marketing & Consumer Behavior Cluster, Imec-SMIT, Vrije Universiteit Brussel, Brussels, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business, Marketing & Consumer Behavior Cluster, Imec-SMIT, Vrije Universiteit Brussel, Brussels, Belgium"}], "References": [{"Title": "RETRACTED ARTICLE: Research on mobile impulse purchase intention in the perspective of system users during COVID-19", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "3", "Page": "665", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "An empirical study on facilitators and inhibitors of adoption of mobile banking in India", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "4", "Page": "2573", "JournalTitle": "Electronic Commerce Research"}, {"Title": "The effect of digital finance on Residents' happiness: the case of mobile payments in China", "Authors": "Chun<PERSON> Zhao; <PERSON><PERSON>; Jianfeng Yan", "PubYear": 2024, "Volume": "24", "Issue": "1", "Page": "69", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Mobile payment adoption in the time of the COVID-19 pandemic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "24", "Issue": "1", "Page": "427", "JournalTitle": "Electronic Commerce Research"}]}, {"ArticleId": *********, "Title": "Time Synchronization Algorithm of Power Wireless Sensor Network under Limited Energy Supply", "Abstract": "", "Keywords": "", "DOI": "10.12677/SEA.2022.116141", "PubYear": 2022, "Volume": "11", "Issue": "6", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "泽琳 韩", "Affiliation": ""}], "References": []}, {"ArticleId": 105521673, "Title": "Effect of augmented reality-based virtual educational robotics on programming students’ enjoyment of learning, computational thinking skills, and academic achievement", "Abstract": "The use of educational robotics for programming education has been shown to be effective in fostering students’ computational thinking (CT) skills. However, physical educational robots are expensive, which may limit their wide use in the classroom. This study used augmented reality technology to develop a virtual educational robotic system (AR Bot for short), which offers 3D visual learning feedback to strengthen spatial ability, as well as delayed feedback and auto-scoring feedback to promote students’ deeper CT processes. To examine the impact of AR Bot on programming learning, this study used a quasi-experimental design to compare an experimental group of 41 first-year university students who used AR Bot and a control group of 34 first-year university students who used Scratch. We assessed the impact of the two CT tools on students’ internal learning processes (enjoyment of learning), CT skills (problem decomposition, algorithm design, and algorithm efficiency skills), and academic achievement. The results showed that students who used AR Bot had higher enjoyment of learning, algorithm design skills, and algorithm efficiency skills but not higher problem decomposition skills and academic achievement than students who used Scratch. Enjoyment of learning led to higher problem decomposition, algorithm design, and algorithm efficiency skills but not academic achievement. Problem decomposition and algorithm design skills, but not algorithm efficiency skills, led to academic achievement. The theoretical and practical implications of the proposed tool and other CT tools in programming education are discussed.", "Keywords": "", "DOI": "10.1016/j.compedu.2022.104721", "PubYear": 2023, "Volume": "195", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Digital Multimedia Design, National Taipei University of Business, No.321, Sec. 1, Jinan Rd., Zhongzheng District, Taipei, 100, Taiwan, Taiwan, ROC"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, National Taichung University of Science and Technology, No. 129, Sec. 3, Sanmin Road, North Dist., Taichung, 404, Taiwan, ROC;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Management, Chien <PERSON> University, No.229, Jianxing Rd., Zhongli Dist., Taoyuan, 320, Taiwan, ROC"}], "References": [{"Title": "Assessing computational thinking: A systematic review of empirical studies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "103798", "JournalTitle": "Computers & Education"}, {"Title": "The Effects of Robotics Training on Children’s Spatial Ability and Attitude Toward STEM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "2", "Page": "379", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "The effectiveness of partial pair programming on elementary school students’ Computational Thinking skills and self-efficacy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "160", "Issue": "", "Page": "104023", "JournalTitle": "Computers & Education"}, {"Title": "Mapping computational thinking through programming in K-12 education: A conceptual model based on a systematic literature Review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "162", "Issue": "", "Page": "104083", "JournalTitle": "Computers & Education"}, {"Title": "Applying computational analysis of novice learners' computer programming patterns to reveal self-regulated learning, computational thinking, and learning performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "106746", "JournalTitle": "Computers in Human Behavior"}, {"Title": "HCDA", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "5", "Page": "66", "JournalTitle": "Communications of the ACM"}, {"Title": "The role of feedback and guidance as intervention methods to foster computational thinking in educational robotics learning activities for primary school", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "180", "Issue": "", "Page": "104431", "JournalTitle": "Computers & Education"}, {"Title": "Effect of different mind mapping approaches on primary school students’ computational thinking skills during visual programming learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "181", "Issue": "", "Page": "104445", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 105521781, "Title": "Impact of Conversion From the Use of Binary Artificial Neurons to the Use of Ternary Neurons With the Combined Applying of Five Classical Statistical Criteria for Normality or Uniformity Hypothesis Testing for Small Samples Distributions", "Abstract": "The work purpose is to show the advantages of moving from ordinary binary neurons to more complex neurons with ternary output quantization. As an example, a neural network combination of five classical statistical criteria is considered: <PERSON><PERSON> (1935), <PERSON><PERSON><PERSON> (1954), <PERSON><PERSON><PERSON> (1965), Maximum deviation from the center (1965), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1992). A forecast of combining these criteria with others is given, built with confidence probability is equal to 0.99. The binary artificial neurons using will require the use of 280 statistical criteria. The conversion to the artificial neurons using with ternary quantizers should reduce the neurons number to 9 for small samples of 16 experiments. An exponential decreasing of the necessary neurons number is seen.", "Keywords": "многокритериальный статистический анализ;проверка гипотезы нормальности или равномерности;малые выборки;искусственные нейроны с бинарными и троичными выходными квантователями", "DOI": "10.17072/1993-0550-2022-3-59-67", "PubYear": 2022, "Volume": "", "Issue": "3(58)", "JournalId": 77295, "JournalTitle": "Вестник Пермского университета. Математика. Механика. Информатика", "ISSN": "1993-0550", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Penza Research Electrotechnical Institute JSC"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Penza State University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Penza State University"}], "References": []}, {"ArticleId": 105521830, "Title": "COMPARISON OF BERT T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MODELS IN IDENTIFYING DESTRUCTIVE CONTENT IN SOCIAL MEDIA", "Abstract": "<p>Цель статьи состоит в определении наиболее эффективной модели из семейства BERT по выявлению деструктивного контента в социальных медиа. Произведено сравнение пяти наиболее известных моделей BERT по выявлению деструктивного контента. Для этого осуществлено создание текстового корпуса из материалов социальных медиа (СМ), дополненного запрещённым к распространению в Российской Федерации контентом нацистского характера из Федерального списка экстремистских материалов. Представлена структура классификатора текстовых данных, основанного на глубокой искусственной нейронной сети BERT, и описана его работа на каждом этапе. Проведен поиск наиболее эффективного метода предварительной обработки текстов. Оценена эффективность работы различных голов классификаторов, основанных на трансформере BERT. Оценено влияние дообучения модели BERT и доказана эффективность его применения с расчетом перплексии. Представлены сравнительные таблицы работы классификаторов на каждом этапе исследования. Найдена наиболее эффективная архитектура классификатора на основе трансформера BERT, выполняющего задачу выявления деструктивного контента с точностью 96,99%.</p><p>The purpose of the article is to determine the most effective model from the BERT family for identifying destructive content in social media. A comparison of the five most well-known BERT models for identifying destructive content was made. For this purpose, a text corpus was created from social media materials (CM), supplemented with Nazi content prohibited for distribution in the Russian Federation from the Federal List of Extremist Materials. The structure of the text data classifier based on the deep artificial neural network BERT is presented and its operation at each stage is described. The search for the most effective method of preprocessing texts was carried out. The efficiency of various heads of classifiers based on the BERT transformer is evaluated. The influence of BERT model retraining is estimated and the effectiveness of its application with the calculation of perplexy is proved. Comparative tables of classifiers' work at each stage of the study are presented. The most effective architecture of the classifier based on the BERT transformer has been found, which performs the task of identifying destructive content with an accuracy of 96.99%.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2022.25.3.003", "PubYear": 2022, "Volume": "", "Issue": "3(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Александ<PERSON> В<PERSON><PERSON><PERSON><PERSON><PERSON>евич", "Affiliation": ""}], "References": []}, {"ArticleId": 105521937, "Title": "An intelligent parameters optimization method of titanium alloy belt grinding considering machining efficiency and surface quality", "Abstract": "<p>Abrasive belt grinding is widely used in typical difficult materials such as titanium alloy, due to its lower grinding temperature and flexible machining. Processing efficiency and processing quality are the two most concerning problems. However, when enhancing processing efficiency, it is a key issue to guarantee the quality of the machining surface. This study provided a parameter optimization model based on the optimization objectives of surface roughness (Ra) and material removal rate (MRR), and the grinding parameters obtained by the solution were verified by experiments. It is found that the performance of the improved non-dominated sorting genetic algorithm (CNSGA-II) is generally good. The algorithm can converge faster and the diversity of Pareto solutions is improved. Besides, when the process parameters obtained by the multi-objective optimization model are used for machining, the surface roughness of the workpiece is reduced to 0.499 μm, and the material removal amount can reach 0.115 mg/min. This shows that the method can not only improve the grinding efficiency of titanium alloy workpiece, but also improve the surface quality. Furthermore, the surface morphology is better with the optimal combination of process parameters; there are no obvious tearing and wear debris on the surface of the CNSGA-II (Ra, MRR), which exhibited deeper wear scars than that of the DF (Ra) and improves surface fineness and flatness.</p>", "Keywords": "Parameter optimization; CNSGA-II; Abrasive belt grinding; Surface roughness; Material removal", "DOI": "10.1007/s00170-022-10723-0", "PubYear": 2023, "Volume": "125", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Chongqing University, Chongqing, China; State Key Laboratory of Mechanical Transmissions, Chongqing University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Chongqing University, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Chongqing University, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Chongqing University, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Vehicle Engineering, Chongqing University, Chongqing, China; State Key Laboratory of Mechanical Transmissions, Chongqing University, Chongqing, China"}], "References": [{"Title": "Novel approaches to one-directional two-dimensional principal component analysis in hybrid pattern framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>ul Negi", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "4897", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A modified particle swarm optimization for multimodal multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "103905", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Multi-objective titanium alloy belt grinding parameters optimization oriented to resources allocation and environment", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "1-2", "Page": "449", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "An intelligent process parameters determination method based on multi-algorithm fusion: a case study in five-axis milling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102244", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A robust methodology for optimizing the topology and the learning parameters of an ANN for accurate predictions of laser-cut edges surface roughness", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "102414", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Optimization of shot peening parameters for AA7B50-T7751 using response surface methodology", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "102426", "JournalTitle": "Simulation Modelling Practice and Theory"}]}, {"ArticleId": 105522154, "Title": "Grinding characteristics of ultra-high strength steel by ultrasonic vibration-assisted grinding with microcrystalline alumina wheel", "Abstract": "<p>Ultra-high strength steel has a wide application of gear transmission systems in the aerospace field, owing to its high hardness, good impact toughness, fracture toughness, etc. Grinding is the important method to guarantee the final quality of ultra-high strength gears. However, the service life of grinding wheels is seriously shortened due on the numerous reinforced particles inside the hardened layer on gear surface, deteriorating the ground quality eventually. In order to resolve this issue, ultrasonic vibration-assisted grinding (UVAG) technology is applied, and then comparative trials are conducted between UVAG and conventional grinding (CG) during ultra-high strength steel machining. Here, the grinding performance, such as the variation of grinding force and temperature under two grinding modes, and the variation of tool wear morphology with the increase of grinding step are analyzed. Subsequently, the grinding surface quality of UVAG process is discussed in view of ground surface roughness and surface morphology. Results show that the CG possesses a rapid increase in grinding forces and temperature, and eventually, surface quality deteriorates gradually, whereas they have been improved in UVAG. In the range of experimental parameters, the force F <sub>n</sub> and temperature of grinding maximum decreased by 35.5 and 39.2%, respectively, compared with CG. Moreover, due to the self-sharpening of abrasive grains, the UVAG process produces a finer ground surface quality. The research provides technical support for high quality and efficient machining of ultra-high strength aviation transmission gear.</p>", "Keywords": "Ultra-high strength steel; Ultrasonic vibration-assisted grinding; Wear properties; Microcrystalline alumina wheels", "DOI": "10.1007/s00170-022-10768-1", "PubYear": 2024, "Volume": "131", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory of Science and Technology On Helicopter Transmission, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory of Science and Technology On Helicopter Transmission, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 3, "Name": "Ming Han", "Affiliation": "National Key Laboratory of Science and Technology On Helicopter Transmission, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Laboratory of Science and Technology On Helicopter Transmission, Nanjing University of Aeronautics and Astronautics, Nanjing, China; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AECC Zhongchuan Transmission Machinery Co., Ltd., Changsha, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "AECC Zhongchuan Transmission Machinery Co., Ltd., Changsha, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AECC Zhongchuan Transmission Machinery Co., Ltd., Changsha, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "AECC Aviation Power Co., Ltd, Xi’an, China"}], "References": [{"Title": "Study on the surface topography of the vibration-assisted belt grinding of the pump gear", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "1-2", "Page": "719", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Effects of axial ultrasonic vibration on grinding quality in peripheral grinding and end grinding of ULE", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "7-8", "Page": "2285", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "An efficient generation grinding method for spur face gear along contact trace using disk CBN wheel", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Du", "PubYear": 2020, "Volume": "110", "Issue": "5-6", "Page": "1179", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Study on surface residual stress of hardened 12Cr2Ni4A alloy steel by ultrasonic vibration-assisted ELID grinding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "1-2", "Page": "641", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Prediction model for surface generation mechanism and roughness in face gear grinding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "7-8", "Page": "4423", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Simulation analysis and experiment of instantaneous temperature field for grinding face gear with a grinding worm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "7-8", "Page": "4989", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Experimental study on ultrasonic vibration-assisted grinding of hardened steel using white corundum wheel", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "3-4", "Page": "2243", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A nano-MQL grinding of single-crystal nickel-base superalloy using a textured grinding wheel", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "3-4", "Page": "2787", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 105522212, "Title": "Phosphorus doping of graphene for conductometric room temperature ammonia sensing", "Abstract": "The ammonia (NH<sub>3</sub>)-sensing performance of phosphorus-doped graphene sensors is systematically investigated in this paper. Using a chemical vapor deposition (CVD) system, phosphorus pentoxide was used as the phosphorus source to achieve stable phosphorus doping of graphene at 480 <sup>o</sup>C. The NH<sub>3</sub>-sensing test results showed that the NH<sub>3</sub> response of phosphorus-doped graphene increased by 2.4 times on average, the response (recovery) time shortened by 70.6% (73.4%) on average, and the theoretical limit of detection (LOD) was 68.76 ppb. In addition, the phosphorus-doped graphene sensor also exhibits excellent repeatability, stability, and ultra-high selectivity to NH<sub>3</sub>. XPS, EDS, and FTIR analysis show that the P-O groups introduced on graphene are the key to improving the NH<sub>3</sub> sensitivity of graphene. The P-O groups provide sufficient O atoms to assist graphene to adsorb NH<sub>3</sub> molecules, thereby improving the NH<sub>3</sub> sensitivity of graphene. A simple, large-scale implementable, and effective phosphorus doping by the CVD method was proposed to improve the NH<sub>3</sub> sensitivity of graphene in this work, which is of great significance for promoting the practical application of graphene gas sensors.", "Keywords": "Graphene gas sensor ; Ammonia sensor ; Phosphorus doping ; CVD", "DOI": "10.1016/j.snb.2022.133234", "PubYear": 2023, "Volume": "379", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronics Engineering, Guangxi Normal University, Guilin, Guangxi 541004, China;Key Laboratory of Integrated Circuits and Microsystems (Guangxi Normal University), Education Department of Guangxi Zhuang Autonomous Region, Guilin 541004, China;School of Electronic Engineering and Automation, Guilin University of Electronic Technology, Guilin 541004, China"}, {"AuthorId": 2, "Name": "Manli Sun", "Affiliation": "College of Electronics Engineering, Guangxi Normal University, Guilin, Guangxi 541004, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>ng Jiang", "Affiliation": "College of Electronics Engineering, Guangxi Normal University, Guilin, Guangxi 541004, China;Key Laboratory of Integrated Circuits and Microsystems (Guangxi Normal University), Education Department of Guangxi Zhuang Autonomous Region, Guilin 541004, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Song", "Affiliation": "College of Electronics Engineering, Guangxi Normal University, Guilin, Guangxi 541004, China;Key Laboratory of Integrated Circuits and Microsystems (Guangxi Normal University), Education Department of Guangxi Zhuang Autonomous Region, Guilin 541004, China;Corresponding authors at College of Electronics Engineering, Guangxi Normal University, Guilin, Guangxi 541004, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronics Engineering, Guangxi Normal University, Guilin, Guangxi 541004, China;Key Laboratory of Integrated Circuits and Microsystems (Guangxi Normal University), Education Department of Guangxi Zhuang Autonomous Region, Guilin 541004, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics Engineering, Guangxi Normal University, Guilin, Guangxi 541004, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronics Engineering, Guangxi Normal University, Guilin, Guangxi 541004, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics Engineering, Guangxi Normal University, Guilin, Guangxi 541004, China;Key Laboratory of Integrated Circuits and Microsystems (Guangxi Normal University), Education Department of Guangxi Zhuang Autonomous Region, Guilin 541004, China;Corresponding authors at College of Electronics Engineering, Guangxi Normal University, Guilin, Guangxi 541004, China"}], "References": [{"Title": "Ultrasensitive flexible NH3 gas sensor based on polyaniline/SrGe4O9 nanocomposite with ppt-level detection ability at room temperature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "319", "Issue": "", "Page": "128293", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Room temperature ammonia gas sensor based on ionic conductive biomass hydrogels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "320", "Issue": "", "Page": "128318", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Selective room temperature ammonia gas sensor using nanostructured ZnO/CuO@graphene on paper substrate", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "350", "Issue": "", "Page": "130833", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Highly sensitive and rapidly responding room-temperature NH3 gas sensor that is based on exfoliated black phosphorus", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "367", "Issue": "", "Page": "132038", "JournalTitle": "Sensors and Actuators B: Chemical"}]}]