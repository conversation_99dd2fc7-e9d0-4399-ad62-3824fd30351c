"""
2025年3月25日
python课程爬虫练习

https://wordpress-edu-3autumn.localprod.oc.forchange.cn/all-about-the-future_04/
1. 爬取上述URL所以页面的评论，打印并保存到文件cvs

https://wordpress-edu-3autumn.localprod.oc.forchange.cn/
2.获取以上链接4篇文章标题、发布时间、文章链接、并在终端print出来
"""

import requests
from bs4 import BeautifulSoup
import csv

# 1. 爬取上述URL所以页面的评论，打印并保存到文件cvs
url = "https://wordpress-edu-3autumn.localprod.oc.forchange.cn/all-about-the-future_04/"
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}
response = requests.get(url, headers=headers)
soup = BeautifulSoup(response.text, "html.parser")
comments = soup.find_all("div", class_="comment-content")
with open("comments.csv", "w", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    writer.writerow(["评论内容"])
    for comment in comments:
        writer.writerow([comment.text.strip()])

# 2.获取以上链接4篇文章标题、发布时间、文章链接、并在终端print出来
url = "https://wordpress-edu-3autumn.localprod.oc.forchange.cn/"
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}
response = requests.get(url, headers=headers)
soup = BeautifulSoup(response.text, "html.parser")
articles = soup.find_all("article")
for article in articles:
    title = article.find("h2").text
    date = article.find("time").text
    link = article.find("a")["href"]
    print("标题：", title)
    print("发布时间：", date)
    print("文章链接：", link)
    print("--------------------------------------------------")
