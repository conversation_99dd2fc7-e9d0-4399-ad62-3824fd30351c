[{"ArticleId": 114253421, "Title": "Semiparametric accelerated failure time models under unspecified random effect distributions", "Abstract": "Accelerated failure time (AFT) models with random effects, a useful alternative to frailty models, have been widely used for analyzing clustered (or correlated) time-to-event data. In the AFT model, the distribution of the unobserved random effect is conventionally assumed to be parametric, often modeled as a normal distribution. Although it has been known that a misspecfied random-effect distribution has little effect on regression parameter estimates, in some cases, the impact caused by such misspecification is not negligible. Particularly when our focus extends to quantities associated with random effects, the problem could become worse. In this paper, we propose a semi-parametric maximum likelihood approach in which the random-effect distribution under the AFT models is left unspecified. We provide a feasible algorithm to estimate the random-effect distribution as well as model parameters. Through comprehensive simulation studies, our results demonstrate the effectiveness of this proposed method across a range of random-effect distribution types (discrete or continuous) and under conditions of heavy censoring. The efficacy of the approach is further illustrated through simulation studies and real-world data examples.", "Keywords": "", "DOI": "10.1016/j.csda.2024.107958", "PubYear": 2024, "Volume": "195", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, Sungkyunkwan University, Seoul, South Korea"}, {"AuthorId": 2, "Name": "Il Do Ha", "Affiliation": "Department of Statistics & Data Science, Pukyong National University, Busan, South Korea;Corresponding author"}], "References": []}, {"ArticleId": 114253534, "Title": "On the Performance of Malleable APGAS Programs and Batch Job Schedulers", "Abstract": "<p>Malleability—the ability for applications to dynamically adjust their resource allocations at runtime—presents great potential to enhance the efficiency and resource utilization of modern supercomputers. However, applications are rarely capable of growing and shrinking their number of nodes at runtime, and batch job schedulers provide only rudimentary support for such features. While numerous approaches have been proposed to enable application malleability, these typically focus on iterative computations and require complex code modifications. This amplifies the challenges for programmers, who already wrestle with the complexity of traditional MPI inter-node programming. Asynchronous Many-Task ( AMT ) programming presents a promising alternative. In AMT, computations are split into many fine-grained task s, which are processed by worker s. This makes transparent task relocation via the AMT runtime system possible, thus offering great potential for enabling efficient malleability. In this work, we propose an extension to an existing AMT system, namely APGAS for Java . We provide easy-to-use malleability programming abstractions, requiring only minor application code additions from programmers. Runtime adjustments, such as process initialization and termination, are automatically managed by our malleability extension. We validate our malleability extension by adapting a load balancing library handling multiple benchmarks. We show that both shrinking and growing operations cost low execution time overhead. In addition, we demonstrate compatibility with potential batch job schedulers by developing a prototype batch job scheduler that supports malleable jobs. Through extensive real-world job batches execution on up to 32 nodes, involving rigid, moldable, and malleable programs, we evaluate the impact of deploying malleable APGAS applications on supercomputers. Exploiting scheduling algorithms, such as FCFS, Backfilling, Easy-Backfilling, and one exploiting malleable jobs, the experimental results highlight a significant improvement regarding several metrics for malleable jobs. We show a 13.09% makespan reduction (the time needed to schedule and execute all jobs), a 19.86% increase in node utilization, and a 3.61% decrease in job turnaround time (the time a job takes from its submission to completion) when using 100% malleable job in combination with our prototype batch job scheduler compared to the best-performing scheduling algorithm with 100% rigid jobs.</p>", "Keywords": "Malleable runtime system; Malleable job scheduling; APGAS", "DOI": "10.1007/s42979-024-02641-7", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Kobe University, Kobe, Japan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Kassel, Kassel, Germany; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Kassel, Kassel, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Kobe University, Kobe, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kobe University, Kobe, Japan"}], "References": [{"Title": "A self‐adjusting task granularity mechanism for the Java lifeline‐based global load balancer library on many‐core clusters", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "2", "Page": "e6224", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 114253580, "Title": "Implementation of Text Mining in Socio-Economic Research: ", "Abstract": "<p>This work aims to analyze insights from social networks for identification of population satisfaction with pay level in Russia using the text mining approach. For this, a sentiment analysis framework was developed, which integrates Twitter mining tools and a sentiment index. Sentiments were extracted using Twitter mining and then recoded and substituted into the sentiment formula. The results of sentiment analysis indicate low satisfaction with levels of pay among Russians. Twitter was chosen as the object of research, as one of the most active and independent networks in Russia. It is possible that some of the tweets belong to authors who are not living in Russia at the moment, but their number is not significant and their interest in this issue, in the authors' opinion, only enhances the relevance of the problem under study.</p>", "Keywords": "", "DOI": "10.4018/IJBDCN.341263", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 26537, "JournalTitle": "International Journal of Business Data Communications and Networking", "ISSN": "1548-0631", "EISSN": "1548-064X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Humanitarian and Pedagogical Academy, Crimean Federal University Named After <PERSON><PERSON>, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Humanitarian and Pedagogical Academy, Crimean Federal University Named After <PERSON><PERSON>, Russia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Humanitarian and Pedagogical Academy, Crimean Federal University Named After <PERSON><PERSON>, Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Crimean Federal University Named After <PERSON><PERSON><PERSON>, Russia"}], "References": [{"Title": "Products and services valuation through unsolicited information from social media", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; L<PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "1775", "JournalTitle": "Soft Computing"}, {"Title": "A new big data approach for topic classification and sentiment analysis of Twitter data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "2", "Page": "877", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "A big data approach to sentiment analysis using greedy feature selection with cat swarm optimization-based long short-term memory neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "6", "Page": "4414", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Sports-fanaticism formalism for sentiment analysis in Arabic text", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Over a decade of social opinion mining: a systematic review", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "7", "Page": "4873", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A New Cooperation Model for Dynamic Networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "2", "Page": "1", "JournalTitle": "International Journal of Business Data Communications and Networking"}]}, {"ArticleId": 114253608, "Title": "Ensemble classifiers using multi-objective Genetic Programming for unbalanced data", "Abstract": "Genetic Programming (GP) can be used to design effective classifiers due to its built-in feature selection and feature construction characteristics. Unbalanced data distributions affect the classification performance of GP classifiers. Some fitness functions have been proposed to solve the class imbalance problem of GP classifiers. However, with the evolution of GP, single-objective GP classifiers evaluated by a single fitness function have poor generalization ability. Moreover, using the best evolved GP classifier for decision-making can easily lead to the possibility of misclassification. In this paper, multi-objective GP is used to optimize multiple fitness functions including AUC approximation (Wmw), Distance (Dist), and Complexity to evolve ensemble classifiers, which jointly determines the class labels of unknown instances. Experiments on sixteen datasets show that our multi-objective GP can significantly improve classification performance compared with single-objective GP, and our proposed ensemble classifiers evolved by multi-objective GP can further improve the classification performance than the single best GP classifier. Comparisons with six GP-based and five traditional machine learning algorithms show that our proposed approaches can achieve significantly better classification performance on most cases.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111554", "PubYear": 2024, "Volume": "158", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Hebei Agricultural University, Baoding, 071001, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Economics and Management, Hebei Agricultural University, Baoding, 071001, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Computer Science, Victoria University of Wellington, Wellington, 6140, New Zealand"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Hebei Agricultural University, Baoding, 071001, China;Hebei Key Laboratory of Agricultural Big Data, Hebei Agricultural University, Baoding, 071001, China;Corresponding author at: College of Information Science and Technology, Hebei Agricultural University, Baoding, 071001, China"}], "References": [{"Title": "A filter-based feature construction and feature selection approach for classification using Genetic Programming", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "196", "Issue": "", "Page": "105806", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Designing genetic programming classifiers with feature selection and feature construction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106826", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-objective genetic programming for feature learning in face recognition", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107152", "JournalTitle": "Applied Soft Computing"}, {"Title": "High-Dimensional Unbalanced Binary Classification by Genetic\n Programming with Multi-Criterion Fitness Evaluation and\n Selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "30", "Issue": "1", "Page": "99", "JournalTitle": "Evolutionary Computation"}, {"Title": "Multi-generation multi-criteria feature construction using Genetic Programming", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "78", "Issue": "", "Page": "101285", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 114253864, "Title": "Tree barriers height information extraction under power line based on watershed segmentation and local maximum algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2024.2331975", "PubYear": 2024, "Volume": "45", "Issue": "7", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Geomatics and Geoinformation, Guilin University of Technology, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Geomatics and Geoinformation, Guilin University of Technology, Guilin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Geomatics and Geoinformation, Guilin University of Technology, Guilin, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Guangxi Guiyu Engineering Consulting Co, LTD, Nanning, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Hydraulic Engineering, Guangxi Vocational College of Water Resources and Electrick Power, Nanning, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Geodesy and Geomatics, Wuhan University, Wuhan, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "College of Geomatics and Geoinformation, Guilin University of Technology, Guilin, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "College of Geomatics and Geoinformation, Guilin University of Technology, Guilin, China"}], "References": [{"Title": "The NASA AfriSAR campaign: Airborne SAR and lidar measurements of tropical forest structure and biomass in support of current and future space missions", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "264", "Issue": "", "Page": "112533", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A survey of intelligent transmission line inspection based on unmanned aerial vehicle", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "1", "Page": "173", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 114253967, "Title": "An expert system for hybrid edge to cloud computational offloading in heterogeneous MEC-MCC environments", "Abstract": "Mobile Cloud Computing (MCC) integrates cloud computing into the mobile environment, effectively tackling performance, environmental, and security constraints. However, the proliferation of clouds and applications introduces complexities in offloading decisions. Mobile Edge Computing (MEC) has emerged as a solution to bolster MCC performance, capitalizing on the proximity to edge devices. However, optimizing computation offloading remains paramount for heterogeneous smart (mobile phones, wearables, and IoT) devices, necessitating efficient utilization of network resources and computational offloading. To address these challenges, this paper introduces NSANNOM (Network and Device Resources Utilization through Smart ANN-based Offloading Mechanism), an expert system designed for optimal computational offloading decision-making and efficient network resource allocation mechanisms. NSANNOM employs Artificial Neural Networks (ANN) for precise decision-making by validating real-world datasets, underscoring ANN’s superiority over existing algorithms, and showcasing enhanced energy savings, cost efficiency, and latency response. Experimental evaluations demonstrate that the proposed ANN model for the offloading decision-making algorithm achieves a training accuracy of 97 % and a validation accuracy of 99 % . The system consumes minimal energy (10 MJ) for task scheduling and exhibits remarkable accuracy in resource utilization across multiple tasks (10–50) ranging in size (from 1 to 16 GB). Additionally, it minimizes time delays (in milliseconds) during the offloading process.", "Keywords": "", "DOI": "10.1016/j.jnca.2024.103867", "PubYear": 2024, "Volume": "225", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Software, Northwestern Polytechnical University, Xi’an, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Software, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, The University of Haripur, Haripur, Pakistan"}], "References": [{"Title": "Processing capability and QoE driven optimized computation offloading scheme in vehicular fog based F-RAN", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "4", "Page": "2547", "JournalTitle": "World Wide Web"}, {"Title": "A review on the computation offloading approaches in mobile edge computing: A g\n ame‐theoretic\n perspective", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "9", "Page": "1719", "JournalTitle": "Software: Practice and Experience"}, {"Title": "A Survey on the Computation Offloading Approaches in Mobile Edge/Cloud Computing Environment: A Stochastic-based Perspective", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "4", "Page": "639", "JournalTitle": "Journal of Grid Computing"}, {"Title": "A survey on the computation offloading approaches in mobile edge computing: A machine learning-based perspective", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "182", "Issue": "", "Page": "107496", "JournalTitle": "Computer Networks"}, {"Title": "An autonomous computation offloading strategy in Mobile Edge Computing: A deep learning-based hybrid approach", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "102974", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Task offloading in edge computing for machine learning-based smart healthcare", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "191", "Issue": "", "Page": "108019", "JournalTitle": "Computer Networks"}, {"Title": "A novel Q-learning-based hybrid algorithm for the optimal offloading and scheduling in mobile edge computing environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "214", "Issue": "", "Page": "103617", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Dependent task offloading with deadline-aware scheduling in mobile edge networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "", "Page": "100868", "JournalTitle": "Internet of Things"}, {"Title": "Dynamic offloading technique for real-time edge-to-cloud computing in heterogeneous MEC–MCC and IoT devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "24", "Issue": "", "Page": "100996", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 114254003, "Title": "An efficient machine learning approach for extracting eSports players’ distinguishing features and classifying their skill levels using symbolic transfer entropy and consensus nested cross-validation", "Abstract": "", "Keywords": "", "DOI": "10.1007/s41060-024-00529-6", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 27236, "JournalTitle": "International Journal of Data Science and Analytics", "ISSN": "2364-415X", "EISSN": "2364-4168", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Consensus features nested cross-validation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "10", "Page": "3093", "JournalTitle": "Bioinformatics"}, {"Title": "Current practice and challenges in coaching Esports players: An interview study with league of legends professional team coaches", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "42", "Issue": "", "Page": "100481", "JournalTitle": "Entertainment Computing"}]}, {"ArticleId": 114254028, "Title": "Improvisation of artificial hummingbird algorithm through incorporation of chaos theory in intelligent optimization of fractional order PID controller tuning", "Abstract": "", "Keywords": "", "DOI": "10.1007/s41870-024-01791-4", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Chaotic driven gorilla troops optimizer based NMF approach for integrative analysis of multiple source data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "7", "Page": "3437", "JournalTitle": "International Journal of Information Technology"}, {"Title": "A novel chaotic archimedes optimization algorithm and its application for efficient selection of regression test cases", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "2", "Page": "1055", "JournalTitle": "International Journal of Information Technology"}, {"Title": "An enhanced artificial hummingbird algorithm and its application in truss topology engineering optimization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101761", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Improved Chaotic Sine Cosine Algorithm (ICSCA) for global optima", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "1", "Page": "245", "JournalTitle": "International Journal of Information Technology"}, {"Title": "An optimal heart disease prediction using chaos game optimization-based recurrent neural model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "5", "Page": "3359", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 114254042, "Title": "A new finite element formulation for the dynamic analysis of beams under moving loads", "Abstract": "Early studies highlighted the fact that moving loads on beams may induce significantly higher dynamic deflections and stresses than those observed in the quasi-static case. Since then, the dynamics of beams under moving loads has been an active research area, in particular for the past decades, with the rapidly increasing computer power allowing the development of highly robust and sophisticated computational and numerical methods in applied mechanics and engineering. This work introduces a novel finite element formulation for the dynamic analysis of Euler-<PERSON> beams subjected to moving loads. The formulation is consistent with a complementary form of the well known Hamilton&#x27;s variational principle, and will be used to address some numerical tests in both modal and time domains. The effectiveness and accuracy of the formulation will be assessed and discussed by comparison between the obtained results and those rendered by the standard displacement-based finite element formulation. As it will be demonstrated, the proposed formulation not only renders continuous bending-moment and shear-force distributions, a desired feature in the structural design field, but also has a superior accuracy than that provided by the displacement formulation that uses the same number of nodal degrees-of-freedom.", "Keywords": "Dynamics; Euler-<PERSON><PERSON><PERSON> beams; Moving loads; Complementary energy; Dual variational principle; Finite element analysis", "DOI": "10.1016/j.compstruc.2024.107347", "PubYear": 2024, "Volume": "298", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "H.A.F.A. Santos", "Affiliation": "CIMOSM, UnIRE, Instituto Superior de Engenharia de Lisboa, Instituto Politécnico de Lisboa, Rua Conselheiro Emídio Navarro 1, 1959-007 Lisboa, Portugal"}], "References": [{"Title": "A true PML approach for steady-state vibration analysis of an elastically supported beam under moving load by a DLSFEM formulation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Fernando M.F. <PERSON>", "PubYear": 2020, "Volume": "239", "Issue": "", "Page": "106295", "JournalTitle": "Computers & Structures"}, {"Title": "On the Numerical Modelization of Moving Load Beam Problems by a Dedicated Parallel Computing FEM Implementation", "Authors": "<PERSON>; <PERSON>; Rosa<PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "4", "Page": "2253", "JournalTitle": "Archives of Computational Methods in Engineering"}]}, {"ArticleId": 114254066, "Title": "The higher-order analysis of response for linear structure under non-Gaussian stochastic excitations with known statistical moments and power spectral density", "Abstract": "The frequency-domain analysis method is a fundamental component of the random vibration analysis, in which the corresponding moment spectrum of excitations are the prerequisite. Nevertheless, the determination of the higher-order moment spectra for non-Gaussian stochastic excitations continues to pose a significant challenge in the existing research works. This paper introduces an accurate and efficient computational approach for determining higher-order moment spectra of non-Gaussian stochastic excitations with known statistical moments and power spectral density (PSD). Firstly, following the idea of the simulation method of non-Gaussian stochastic processes, the transformation model between the stationary non-Gaussian stochastic process and underlying Gaussian stochastic process is determined based on the known statistical moments, the PSD of the underlying Gaussian stochastic process is determined using the transformation model. Secondly, the approximate higher-order moment spectra models of stationary non-Gaussian stochastic processes are presented by the PSD of the underlying Gaussian process. Subsequently, the higher-order moment spectra models of non-Gaussian excitations are utilized to compute the higher-order moment spectra of response for the linear structure via the auxiliary harmonic excitation generalized method. Finally, three numerical examples are examined to assess the efficiency and accuracy of the proposed approximate models for the higher-order moment spectra of non-Gaussian stochastic processes.", "Keywords": "", "DOI": "10.1016/j.compstruc.2024.107348", "PubYear": 2024, "Volume": "298", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Civil Engineering and Architecture, Henan University of Science and Technology, Luoyang 471023, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Chongqing University, Chongqing 400045, China;Key Laboratory of New Technology for Construction of Cities in Mountain Area (Chongqing University), Ministry of Education, Chongqing 400045, China;Corresponding author at: School of Civil Engineering, Chongqing University, Chongqing 400045, China"}], "References": []}, {"ArticleId": 114254124, "Title": "Integrated Spatial and Temporal Features Based Network Intrusion Detection System Using SMOTE Sampling", "Abstract": "With attackers discovering more inventive ways to take advantage of network weaknesses, the pace of attacks has drastically increased in recent years. As a result, network security has never been more important, and many network intrusion detection systems (NIDS) rely on old, out-of-date attack signatures. This necessitates the deployment of reliable and modern Network Intrusion Detection Systems that are educated on the most recent data and employ deep learning techniques to detect malicious activities. However, it has been found that the most recent datasets readily available contain a large quantity of benign data, enabling conventional deep learning systems to train on the imbalance data. A high false detection rate result from this. To overcome the aforementioned issues, we suggest a Synthetic Minority Over-Sampling Technique (SMOTE) integrated convolution neural network and bi-directional long short-term memory SCNN-BIDLSTM solution for creating intrusion detection systems. By employing the SMOTE, which integrates a convolution neural network to extract spatial features and a bi-directional long short-term memory to extract temporal information; difficulties are reduced by increasing the minority samples in our dataset. In order to train and evaluate our model, we used open benchmark datasets as CIC-IDS2017, NSL-KDD, and UNSW-NB15 and compared the results with other state of the art models. © 2024, Modern Education and Computer Science Press. All rights reserved.", "Keywords": "BIDLSTM; CICIDS2017; CNN; Deep Learning; Network Intrusion Detection; SMOTE", "DOI": "10.5815/ijcnis.2024.02.02", "PubYear": 2024, "Volume": "16", "Issue": "2", "JournalId": 20260, "JournalTitle": "International Journal of Computer Network and Information Security", "ISSN": "2074-9090", "EISSN": "2074-9104", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>i Technological Institute, Mumbai, 400019, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>i Technological Institute, Mumbai, 400019, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>i Technological Institute, Mumbai, 400019, India"}], "References": []}, {"ArticleId": 114254238, "Title": "Human presencing: an alternative perspective on human embodiment and its implications for technology", "Abstract": "Human presencing explores how people’s past encounters with others shape their present actions. In this paper, I present an alternative perspective on human embodiment in which the re-evoking of the absent can be traced to the intricate interplay of bodily dynamics. By situating the phenomenon within distributed, embodied, and dialogic approaches to language and cognition, I am overcoming the theoretical and methodological challenges involved in perceiving and acting upon what is not perceptually present. In a case study, I present strong and weak dimensions of human presencing. In the former, a person uses their body in distinct ways and shapes their immediate ecology to make others present to them. In contrast, in the latter, a person’s past encounter with others powerfully shapes the projections they make onto written digital inscriptions. These findings have implications for how people act in online learning environments and how human activity shapes the machines we use every day. In this way, the paper highlights the complexity of a person as a social being and allows for different approaches to human embodiment in technology.", "Keywords": "", "DOI": "10.1007/s00146-024-01874-7", "PubYear": 2025, "Volume": "40", "Issue": "2", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Between Subjectivity and Imposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Drones, robots and perceived autonomy: implications for living human beings", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "2", "Page": "591", "JournalTitle": "AI & SOCIETY"}, {"Title": "The Data-Production Dispositif", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "How systemic cognition enables epistemic engineering", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "", "Page": "300", "JournalTitle": "Frontiers in Artificial Intelligence"}]}, {"ArticleId": 114254239, "Title": "Landing and Use of Medical Assistance Algorithms for U-Shaped Symmetric Networks Incorporating Attention Mechanisms", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.143062", "PubYear": 2024, "Volume": "14", "Issue": "3", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [], "References": []}, {"ArticleId": 114254410, "Title": "Fingerprint image encryption based on chaos and nonlinear dynamic “X” model diffusion", "Abstract": "It is analyzed that the important part of the fingerprint image is concentrated in one part of the image, and the gray image is the part with useful information in the whole image. Therefore, this paper combines a new fingerprint image encryption algorithm proposed by combining two new one-dimensional chaotic systems, nonlinear functions, and rules with a dynamic &quot;X&quot; model. We propose two new one-dimensional chaotic systems, one is Sin-Cos chaotic system (SCCS) and the other is Cos-Cos chaotic system (CCCS). Firstly, the fingerprint image is binarized and the contour is extracted to get the area of the fingerprint in the image. Then, for the fingerprint area, a round of encryption is carried out by using nonlinear rules and 24 rules of the dynamic X model. Then the whole image is separated into several parts according to the fingerprint position, read into a sequence at one time, and the whole fingerprint image is scrambled and diffused synchronously by using nonlinear rules and functions. The entropy, NPCR, and UACI of the algorithm are very close to the ideal value, and the performance in time efficiency is passable. The algorithm is extremely secure in a large number of test performance analysis results.", "Keywords": "", "DOI": "10.1016/j.jisa.2024.103723", "PubYear": 2024, "Volume": "82", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science & Technology, Dalian Maritime University, Dalian 116026, PR China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Science & Technology, Dalian Maritime University, Dalian 116026, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science & Technology, Dalian Maritime University, Dalian 116026, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science & Technology, Dalian Maritime University, Dalian 116026, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science & Technology, Dalian Maritime University, Dalian 116026, PR China"}], "References": [{"Title": "Image encryption algorithm for synchronously updating Boolean networks based on matrix semi-tensor product theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "16", "JournalTitle": "Information Sciences"}, {"Title": "An efficient image encryption using non-dominated sorting genetic algorithm-III based 4-D chaotic maps", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "1309", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Enhanced digital chaotic maps based on bit reversal with applications in random bit generators", "Authors": "Moatsum Alawida; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "1155", "JournalTitle": "Information Sciences"}, {"Title": "Multi-task CNN for restoring corrupted fingerprint images", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "107203", "JournalTitle": "Pattern Recognition"}, {"Title": "Cryptanalysis of a DNA-based image encryption scheme", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "520", "Issue": "", "Page": "130", "JournalTitle": "Information Sciences"}, {"Title": "Fingerprint-related chaotic image encryption scheme based on blockchain framework", "Authors": "Ruiping <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "20", "Page": "30583", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Image encryption algorithm based on the matrix semi-tensor product with a compound secret key produced by a Boolean network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "539", "Issue": "", "Page": "195", "JournalTitle": "Information Sciences"}, {"Title": "Cross-plane colour image encryption using a two-dimensional logistic tent modular map", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "1063", "JournalTitle": "Information Sciences"}, {"Title": "A novel triple-image encryption and hiding algorithm based on chaos, compressive sensing and 3D DCT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Donghua Jiang", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "505", "JournalTitle": "Information Sciences"}, {"Title": "Image encryption algorithm based on lattice hash function and privacy protection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "13", "Page": "18251", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multiple-image encryption algorithm based on the bit plane and superpixel", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "13", "Page": "19969", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 114254480, "Title": "Simulation of hydraulic actuator dynamics in virtual environment systems", "Abstract": "", "Keywords": "", "DOI": "10.15827/0236-235X.144.582-589", "PubYear": 2023, "Volume": "19", "Issue": "", "JournalId": 19680, "JournalTitle": "International journal \"Programmnye produkty i sistemy\"", "ISSN": "0236-235X", "EISSN": "2311-2735", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>,", "Affiliation": ""}, {"AuthorId": 3, "Name": "И.Н. Мироненко", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>,", "Affiliation": ""}], "References": []}, {"ArticleId": 114254594, "Title": "Automatic landslide detection and visualization by using deep ensemble learning method", "Abstract": "Rapid detection of damages occurring as a result of natural disasters is vital for emergency response. In recent years, remote sensing techniques have been commonly used for the automatic categorization and localization of such events using satellite images. Trained based on natural disaster images, a convolutional neural network (CNN) has been applied as a highly successful method, with its ability to reveal outstanding features. Studies aiming to detect target points obtained as a result of extracting visual features from natural images within these networks have achieved their goals. In this study, ensemble learning methods have been suggested as a means to develop the detection of landslide areas from landslide satellite images. Landslide image dataset has been trained for their categorization in CNN models and then they have been used again to localize landslide regions. While model predictions develop overall performance and status, different ensemble strategies have been used and integrated to reduce the sensitivity to prediction variance and training data. Class-selective relevance mapping (CRM) has been used to visualize individual CNN models and ensemble learned behaviors. As a result of the comparisons made based on mean average precision metrics and the criteria of intersection over union, model ensembles have proved to show higher localization performance than any other individual model.", "Keywords": "Deep learning; Landslide; Ensemble learning; Class-selective relevance mapping", "DOI": "10.1007/s00521-024-09638-6", "PubYear": 2024, "Volume": "36", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>ğ<PERSON>", "Affiliation": "Department of Civil Engineering, Karadeniz Technical University, Ortahisar, Trabzon, Turkey; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Emergency and Disaster Management, Ankara University, Ankara, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Karadeniz Technical University, Ortahisar, Trabzon, Turkey"}, {"AuthorId": 4, "Name": "Ümit Bahadır", "Affiliation": "Department of Civil Engineering, Karadeniz Technical University, Ortahisar, Trabzon, Turkey"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, İzmir Democracy University, İzmir, Turkey"}], "References": [{"Title": "Machine learning for landslides prevention: a survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "17", "Page": "10881", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Classification of landslides on the southeastern Tibet Plateau based on transfer learning and limited labelled datasets", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "286", "JournalTitle": "Remote Sensing Letters"}, {"Title": "Landslide detection in real-time social media image streams", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "24", "Page": "17809", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 114254739, "Title": "Human-robot facial coexpression", "Abstract": "Large language models are enabling rapid progress in robotic verbal communication, but nonverbal communication is not keeping pace. Physical humanoid robots struggle to express and communicate using facial movement, relying primarily on voice. The challenge is twofold: First, the actuation of an expressively versatile robotic face is mechanically challenging. A second challenge is knowing what expression to generate so that the robot appears natural, timely, and genuine. Here, we propose that both barriers can be alleviated by training a robot to anticipate future facial expressions and execute them simultaneously with a human. Whereas delayed facial mimicry looks disingenuous, facial coexpression feels more genuine because it requires correct inference of the human’s emotional state for timely execution. We found that a robot can learn to predict a forthcoming smile about 839 milliseconds before the human smiles and, using a learned inverse kinematic facial self-model, coexpress the smile simultaneously with the human. We demonstrated this ability using a robot face comprising 26 degrees of freedom. We believe that the ability to coexpress simultaneous facial expressions could improve human-robot interaction.", "Keywords": "", "DOI": "10.1126/scirobotics.adi4724", "PubYear": 2024, "Volume": "9", "Issue": "88", "JournalId": 15892, "JournalTitle": "Science Robotics", "ISSN": "", "EISSN": "2470-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Creative Machines Laboratory, Department of Mechanical Engineering, Columbia University, New York, NY 10027, USA."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering and Materials Department, Duke University, Durham, NC 27708, USA.;Department of Electrical and Computer Engineering, Duke University, Durham, NC 27708, USA.;Department of Computer Science, Duke University, Durham, NC 27708, USA."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Creative Machines Laboratory, Department of Mechanical Engineering, Columbia University, New York, NY 10027, USA."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Columbia University, New York, NY 10027, USA."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Columbia University, New York, NY 10027, USA."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Creative Machines Laboratory, Department of Mechanical Engineering, Columbia University, New York, NY 10027, USA."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Creative Machines Laboratory, Department of Mechanical Engineering, Columbia University, New York, NY 10027, USA.;Data Science Institute, Columbia University, New York, NY, 10027, USA."}], "References": []}, {"ArticleId": 114254882, "Title": "WebRTC-QoE: A dataset of QoE assessment of subjective scores, network impairments, and facial & speech features", "Abstract": "In the realm of real-time communications, WebRTC-based multimedia applications are increasingly prevalent as these can be smoothly integrated within Web browsing sessions. The browsing experience is then significantly improved with respect to scenarios where browser add-ons and/or plug-ins are used; still, the end user&#x27;s Quality of Experience (QoE) in WebRTC sessions may be affected by network impairments, such as delays and losses. Due to the variability in user perceptions under different communications scenarios, comprehending and enhancing the resulting service quality is a complex endeavor. To address this, we present a dataset that provides a comprehensive perspective on the conversational quality of a two-party WebRTC-based audiovisual telemeeting service. This dataset was gathered through subjective evaluations involving 20 subjects across 15 different test conditions (TCs). A specialized system was developed to induce controlled network disruptions such as delay, jitter, and packet loss rate, which adversely affected the communication between the parties. This methodology offered an insight into user perceptions under various network impairments. The dataset encompasses a blend of objective and subjective data including ACR (Absolute Category Rating) subjective scores, WebRTC-internals parameters, facial expressions features, and speech features. Consequently, it serves as a substantial contribution to the improvement of WebRTC-based video call systems, offering practical and real-world data that can drive the development of more robust and efficient multimedia communication systems, thereby enhancing the user&#x27;s experience.", "Keywords": "", "DOI": "10.1016/j.comnet.2024.110356", "PubYear": 2024, "Volume": "244", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "Gülnaziye Bingöl", "Affiliation": "DIEE, University of Cagliari, 09123 Cagliari, Italy;CNIT, University of Cagliari, 09123 Cagliari, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "DIEE, University of Cagliari, 09123 Cagliari, Italy;CNIT, University of Cagliari, 09123 Cagliari, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "DIEE, University of Cagliari, 09123 Cagliari, Italy;CNIT, University of Cagliari, 09123 Cagliari, Italy;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "DIEE, University of Cagliari, 09123 Cagliari, Italy;CNIT, University of Cagliari, 09123 Cagliari, Italy"}], "References": []}, {"ArticleId": 114254901, "Title": "Deep learning-based recognition system for pashto handwritten text: benchmark on PHTI", "Abstract": "<p> This article introduces a recognition system for handwritten text in the Pashto language, representing the first attempt to establish a baseline system using the Pashto Handwritten Text Imagebase (PHTI) dataset. Initially, the PHTI dataset underwent pre-processed to eliminate unwanted characters, subsequently, the dataset was divided into training 70%, validation 15%, and test sets 15%. The proposed recognition system is based on multi-dimensional long short-term memory (MD-LSTM) networks. A comprehensive empirical analysis was conducted to determine the optimal parameters for the proposed MD-LSTM architecture; Counter experiments were used to evaluate the performance of the proposed system comparing with the state-of-the-art models on the PHTI dataset. The novelty of our proposed model, compared to other state of the art models, lies in its hidden layer size ( i.e ., 10, 20, 80) and its Tanh layer size ( i.e. , 20, 40). The system achieves a Character Error Rate (CER) of 20.77% as a baseline on the test set. The top 20 confusions are reported to check the performance and limitations of the proposed model. The results highlight complications and future perspective of the Pashto language towards the digital transition. </p>", "Keywords": "Deep learning;Natural language processing;Optical character recognition;Pashto handwritten text imagebase", "DOI": "10.7717/peerj-cs.1925", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, <PERSON><PERSON>, Sheringel, Dir, Pakistan;Department of Computer Science & IT, University of Malakand, Chakdara, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, <PERSON><PERSON> University, Sheringel, Dir, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Software Engineering, University of Malakand, Chakadara, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, <PERSON><PERSON> University, Sheringel, Dir, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, King Khalid University, Abha, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AI and Software, Gachon University, Seongnam-si, Republic of South Korea"}], "References": [{"Title": "Pashto Characters Recognition Using Multi-Class Enabled Support Vector Machine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "3", "Page": "2831", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Efficient skew detection and correction in scanned document images through clustering of probabilistic hough transforms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "152", "Issue": "", "Page": "93", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Baseline Isolated Printed Text Image Database for Pashto Script Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "1", "Page": "875", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 114254903, "Title": "Performance evaluation and dimensional optimization design of planar 6R redundant actuation parallel mechanism", "Abstract": "<p>Aiming at the problems of small good workspace, many singular configurations, and limited carrying capacity of non-redundant parallel mechanisms, a full-redundant drive parallel mechanism is designed and developed, and its performance evaluation, good workspace identification, and scale optimization design are studied. First, the kinematics analysis of the planar 6R parallel mechanism is completed. Then, the motion/force transmission performance evaluation index of the mechanism is established, and the singularity analysis of the mechanism is completed. Based on this, the fully redundant driving mode of the mechanism is determined, and the good transmission workspace of the mechanism in this mode is identified. Then, the mapping relationship between the performance and scale of the mechanism is established by using the space model theory, and the scale optimization of the mechanism is completed. Finally, the robot prototype is made according to the optimal scale, and the performance verification is carried out based on the research of dynamics and control strategy. The results show that the fully redundant actuation parallel mechanism obtained by design optimization has high precision and large bearing capacity. The position repeatability and position accuracy are 0.053 mm and 0.635 mm, respectively, and the load weight ratio can reach 15.83%. The research results of this paper complement and improve the performance evaluation and scale optimization system of redundantly actuated parallel mechanisms.</p>", "Keywords": "motion/force transmission performance; redundant drive; good transmission workspace; performance map; scale optimization", "DOI": "10.1017/S0263574724000456", "PubYear": 2024, "Volume": "42", "Issue": "5", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "Ming Han", "Affiliation": "School of Mechanical Engineering, Hebei University of Technology Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Hebei University of Technology Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Hebei University of Technology Tianjin, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Hebei University of Technology Tianjin, China; Corresponding author."}], "References": []}, {"ArticleId": 114255015, "Title": "Remote Sensing Enabled Sustainable Tomato Plant Health and Pest Surveillance Using Machine Learning Techniques", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSNET.2024.10063258", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114255056, "Title": "ATT&CK-based Advanced Persistent Threat attacks risk propagation assessment model for zero trust networks", "Abstract": "In recent years, the growing frequency and intensity of Advanced Persistent Threats (APTs) have significantly undermined the legitimacy and financial stability of government agencies, enterprises, and other entities. Moreover, these attacks have shown the inherent vulnerabilities in conventional border defense strategies. The emergence of the zero trust network architecture can be attributed to the increasing complexity of the cyber threat landscape. With the application of risk assessment, this paper effectively tackles the challenges posed by conventional network defense limitations and enhances the efficiency of the access control decision-making process. Nevertheless, the existing risk assessment approaches primarily focus on conventional security assessment objectives, which exhibits a deficiency in the ability to dynamically assess APT attacks. The Adversarial Tactics, Techniques, and Common Knowledge (ATT&amp;CK) Framework introduced in this paper is a novel approach to mitigating APT attacks. This paper aims to mine and analyze the frequent item set and correlation of cyber threat penetration attack techniques. The paper also intends to construct an attack technique relationship diagram and develop a tactical prediction model for cyber threat penetration attacks using the Markov chain model. Finally, our study aims to establish a risk propagation model for APT threats based on the aforementioned model. The approach presented in this paper significantly enhances the capacity of zero trust networks in addressing sophisticated cyber threats.", "Keywords": "", "DOI": "10.1016/j.comnet.2024.110376", "PubYear": 2024, "Volume": "245", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyberspace Science and Technology, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Cyberspace Science and Technology, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science & Technology, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyberspace Science and Technology, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyberspace Science and Technology, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science & Technology, Beijing Institute of Technology, Beijing, 100081, China;Corresponding author"}, {"AuthorId": 7, "Name": "<PERSON>zhang Li", "Affiliation": "School of Computer Science & Technology, Beijing Institute of Technology, Beijing, 100081, China"}], "References": [{"Title": "A Systematic Review of Hidden Markov Models and Their Applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "1429", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Bayesian networks for supply chain risk, resilience and ripple effect analysis: A literature review", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113649", "JournalTitle": "Expert Systems with Applications"}, {"Title": "CryptDICE: Distributed data protection system for secure cloud data storage and computation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "96", "Issue": "", "Page": "101671", "JournalTitle": "Information Systems"}, {"Title": "A Network Security Risk Assessment Method Based on a B_NAG Model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "1", "Page": "103", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Online social networks security and privacy: comprehensive review and analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "5", "Page": "2157", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Survey and Taxonomy of Adversarial Reconnaissance Techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Are Software Updates Useless against Advanced Persistent Threats?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON> di Tiz<PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "1", "Page": "31", "JournalTitle": "Communications of the ACM"}, {"Title": "A performance overview of machine learning-based defense strategies for advanced persistent threats in industrial control systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "103445", "JournalTitle": "Computers & Security"}]}, {"ArticleId": *********, "Title": "Machine Learning-Based Water Management Strategies for Sustainable Groundwater Resources", "Abstract": "<p>Groundwater resources are under increasing pressure, nevertheless, as a result of population growth, climate change, and overuse. Accurate estimates of groundwater levels are essential for the management of water resources to be sustainable. Deep learning algorithms have the potential to enhance groundwater level prediction by extracting complex patterns from the previous data. In recent years, groundwater level forecasting using deep learning has received increasing attention. Recurrent neural networks (RNNs) are a common deep learning technique for predicting groundwater levels. Since RNNs are capable of learning long-range dependencies in the data, they are well suited for time-series prediction problems. Utilizing convolutional neural networks (CNNs) is an additional strategy. CNNs are frequently employed for tasks such as segmenting and classifying images, but they may also be used to predict time series. CNNs are capable of effectively identifying spatial patterns in the data, which can be helpful for predicting groundwater levels. Numerous researches have shown that groundwater level prediction models based on deep learning produce promising outcomes. But there are still some issues that need to be resolved, such as the requirement for a substantial amount of training data and the complexity of deciphering the output of deep learning models. Overall, deep learning is a promising new strategy for predicting groundwater levels. Future groundwater level prediction algorithms should become progressively more precise and trustworthy as deep learning techniques in the future.</p>", "Keywords": "Accuracy; Convolutional neural networks; Deep learning; Groundwater level; Recurrent neural networks", "DOI": "10.1007/s42979-024-02686-8", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "Shubha G. <PERSON>u", "Affiliation": "Department of Information Science and Engineering, KLS Gogte Institute of Technology, Affiliated to Visvesvaraya Technological University, Belagavi, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, KLS Gogte Institute of Technology, Affiliated to Visvesvaraya Technological University, Belagavi, India"}], "References": [{"Title": "A comparative study of machine learning and Fuzzy-AHP technique to groundwater potential mapping in the data-scarce region", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "155", "Issue": "", "Page": "104855", "JournalTitle": "Computers & Geosciences"}, {"Title": "Groundwater level prediction using machine learning models: A comprehensive review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "489", "Issue": "", "Page": "271", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 114255290, "Title": "Structural health monitoring of aircraft through prediction of delamination using machine learning", "Abstract": "Background <p>Structural health monitoring (SHM) is a regular procedure of monitoring and recognizing changes in the material and geometric qualities of aircraft structures, bridges, buildings, and so on. The structural health of an airplane is more important in aerospace manufacturing and design. Inadequate structural health monitoring causes catastrophic breakdowns, and the resulting damage is costly. There is a need for an automated SHM technique that monitors and reports structural health effectively. The dataset utilized in our suggested study achieved a 0.95 R2 score earlier.</p> Methods <p>The suggested work employs support vector machine (SVM) + extra tree + gradient boost + AdaBoost + decision tree approaches in an effort to improve performance in the delamination prediction process in aircraft construction.</p> Results <p>The stacking ensemble method outperformed all the technique with 0.975 R2 and 0.023 RMSE for old coupon and 0.928 R2 and 0.053 RMSE for new coupon. It shown the increase in R2 and decrease in root mean square error (RMSE).</p>", "Keywords": "Delamination;Machine learning;Prediction;Stack ensemble;Structural health monitoring", "DOI": "10.7717/peerj-cs.1955", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Data Science and Business Systems, School of Computing, College of Engineering and Technology, SRM Institute of Science and Technology, Kattankulathur, Tamilnadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Solar, Al-Nahrain Research Center for Renewable Energy, Al-Nahrain University, Jadriya, Baghdad, Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing Technologies, School of Computing, College of Engineering and Technology, SRM Institute of Science and Technology, Kattankulathur, Tamil Nadu, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing Technologies, School of Computing, College of Engineering and Technology, SRM Institute of Science and Technology, Kattankulathur, Tamil Nadu, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université de Moncton, Moncton, Canada"}], "References": []}, {"ArticleId": 114255309, "Title": "Flatness-based control in successive loops for VSI-fed PM synchronous motors and induction motors", "Abstract": "", "Keywords": "", "DOI": "10.1080/23307706.2024.2330739", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 30013, "JournalTitle": "Journal of Control and Decision", "ISSN": "2330-7706", "EISSN": "2330-7714", "Authors": [{"AuthorId": 1, "Name": "Gerasi<PERSON>", "Affiliation": "Unit of Industrial Automation, Industrial Systems Institute, Rion Patras, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management and Innovation Systems, Universita degli Studi di Salerno, Fisciano, Italy"}, {"AuthorId": 3, "Name": "Mohammed <PERSON>-<PERSON>", "Affiliation": "Department of Electrical Engineering, King Saud University, Riyadh, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECS Engineering, Rensselaer Polytechnic Institute, NY, USA"}], "References": []}, {"ArticleId": 114255483, "Title": "MTL-TRANSFER: Leveraging Multi-task Learning and Transferred Knowledge for Improving Fault Localization and Program Repair", "Abstract": "<p>Fault localization (FL) and automated program repair (APR) are two main tasks of automatic software debugging. Compared with traditional methods, deep learning-based approaches have been demonstrated to achieve better performance in FL and APR tasks. However, the existing deep learning-based FL methods ignore the deep semantic features or only consider simple code representations. And for APR tasks, existing template-based APR methods are weak in selecting the correct fix templates for more effective program repair, which are also not able to synthesize patches via the embedded end-to-end code modification knowledge obtained by training models on large-scale bug-fix code pairs. Moreover, in most of FL and APR methods, the model designs and training phases are performed separately, leading to ineffective sharing of updated parameters and extracted knowledge during the training process. This limitation hinders the further improvement in the performance of FL and APR tasks. To solve the above problems, we propose a novel approach called MTL-TRANSFER, which leverages a multi-task learning strategy to extract deep semantic features and transferred knowledge from different perspectives. First, we construct a large-scale open-source bug datasets and implement 11 multi-task learning models for bug detection and patch generation sub-tasks on 11 commonly used bug types, as well as one multi-classifier to learn the relevant semantics for the subsequent fix template selection task. Second, an MLP-based ranking model is leveraged to fuse spectrum-based, mutation-based and semantic-based features to generate a sorted list of suspicious statements. Third, we combine the patches generated by the neural patch generation sub-task from the multi-task learning strategy with the optimized fix template selecting order gained from the multi-classifier mentioned above. Finally, the more accurate FL results, the optimized fix template selecting order, and the expanded patch candidates are combined together to further enhance the overall performance of APR tasks. Our extensive experiments on widely-used benchmark Defects4J show that MTL-TRANSFER outperforms all baselines in FL and APR tasks, proving the effectiveness of our approach. Compared with our previously proposed FL method TRANSFER-FL (which is also the state-of-the-art statement-level FL method), MTL-TRANSFER increases the faults hit by 8/11/12 on Top-1/3/5 metrics (92/159/183 in total). And on APR tasks, the number of successfully repaired bugs of MTL-TRANSFER under the perfect localization setting reaches 75, which is 8 more than our previous APR method TRANSFER-PR. Furthermore, another experiment to simulate the actual repair scenarios shows that MTL-TRANSFER can successfully repair 15 and 9 more bugs (56 in total) compared with TBar and TRANSFER, which demonstrates the effectiveness of the combination of our optimized FL and APR components.</p>", "Keywords": "", "DOI": "10.1145/3654441", "PubYear": 2024, "Volume": "33", "Issue": "6", "JournalId": 14907, "JournalTitle": "ACM Transactions on Software Engineering and Methodology", "ISSN": "1049-331X", "EISSN": "1557-7392", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Lab of CCSE, Beihang University, Beijing, China and Zhongguancun Laboratory, Beijing, China"}, {"AuthorId": 2, "Name": "Hongwei Yu", "Affiliation": "State Key Lab of CCSE, Beihang University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Lab of CCSE, Beihang University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beihang University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing University, Chongqing, China"}, {"AuthorId": 6, "Name": "Hailong Sun", "Affiliation": "State Key Lab of CCSE, Beihang University, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Lab of CCSE, Beihang University, Beijing, China and Zhongguancun Laboratory, Beijing China"}, {"AuthorId": 8, "Name": "Chunming Hu", "Affiliation": "State Key Lab of CCSE, Beihang University, Beijing, China and Zhongguancun Laboratory, Beijing China"}], "References": [{"Title": "Multi-task learning for natural language processing in the 2020s: Where are we going?", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "120", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A deep neural network based multi-task learning approach to hate speech detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "210", "Issue": "", "Page": "106458", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "End-to-end Japanese Multi-dialect Speech Recognition and Dialect Identification with Multi-task Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "", "JournalTitle": "APSIPA Transactions on Signal and Information Processing"}]}, {"ArticleId": 114255596, "Title": "Audio-visual speech synthesis using vision transformer–enhanced autoencoders with ensemble of loss functions", "Abstract": "<p>Audio-visual speech synthesis (AVSS) has garnered attention in recent years for its utility in the realm of audio-visual learning. AVSS transforms one speaker’s speech into another’s audio-visual stream while retaining linguistic content. This approach extends existing AVSS methods by first modifying vocal features from the source to the target speaker, akin to voice conversion (VC), and then synthesizing the audio-visual stream for the target speaker, termed audio-visual synthesis (AVS). In this work, a novel AVSS approach is proposed using vision transformer (ViT)-based Autoencoders (AEs), enriched with a combination of cycle consistency and reconstruction loss functions, with the aim of enhancing synthesis quality. Leveraging ViT’s attention mechanism, this method effectively captures spectral and temporal features from input speech. The combination of cycle consistency and reconstruction loss improves synthesis quality and aids in preserving essential information. The proposed framework is trained and tested on benchmark datasets, and compared extensively with state-of-the-art (SOTA) methods. The experimental results demonstrate the superiority of the proposed approach over existing SOTA models, in terms of quality and intelligibility for AVSS, indicating the potential for real-world applications.</p>", "Keywords": "Voice conversion (VC); Audio-visual synthesis (AVS); Autoencoder (AE); Vision transformer (ViT); Cycle consistency loss; Reconstruction loss; Combination of loss functions", "DOI": "10.1007/s10489-024-05380-7", "PubYear": 2024, "Volume": "54", "Issue": "6", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Durgapur, Durgapur, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Durgapur, Durgapur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology Durgapur, Durgapur, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Fraunhofer IIS, Erlangen, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology Durgapur, Durgapur, India"}], "References": [{"Title": "Combination of loss functions for deep text classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "751", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "A survey of autoencoder-based recommender systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "2", "Page": "430", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Voxceleb: Large-scale speaker verification in the wild", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "101027", "JournalTitle": "Computer Speech & Language"}, {"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "A-DBNF: adaptive deep belief network framework for regression and classification tasks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "7", "Page": "4199", "JournalTitle": "Applied Intelligence"}, {"Title": "Deep Audio-visual Learning: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "3", "Page": "351", "JournalTitle": "International Journal of Automation and Computing"}, {"Title": "Transformers in Vision: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "10s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A survey on multimodal-guided visual content synthesis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "497", "Issue": "", "Page": "110", "JournalTitle": "Neurocomputing"}, {"Title": "Ensemble of loss functions to improve generalizability of deep metric learning methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "7", "Page": "21525", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 114255633, "Title": "Does XBRL adoption increase financial information transparency in digital disclosure environment? Insights from emerging markets", "Abstract": "The eXtensible Business Reporting Language (XBRL) is one of the global-level business reporting standards that accommodates all firms to report financial affairs in a convenient manner. The main objective of this study is to explain the impact of XBRL adoption on the transparency of financial information disclosure in Jordanian financial companies. The current study used a sample of 124 respondents including accounting managers, auditors, and financial managers with a background in XBRL who are working in Jordanian financial firms. The findings confirmed that the adoption of XBRL in Jordanian financial firms increases financial information transparency in the digital disclosure environment which leads to more relevant, reliable, and transparent financial statements disclosure. One main implication the current study heightened is the importance of turning to the implementation of the XBRL will support the process of decision making which will support and enhance both the performance and decision-making process. Lastly, this research effort is the first of its kind that examined the impact of XBRL on the transparency of financial information from the financial firms&#x27; perspective in Jordan as a developing country.", "Keywords": "Digital disclosure ; XBRL adoption ; Financial information ; Financial reporting ; Information transparency ; Financial market", "DOI": "10.1016/j.jjimei.2024.100228", "PubYear": 2024, "Volume": "4", "Issue": "1", "JournalId": 83128, "JournalTitle": "International Journal of Information Management Data Insights", "ISSN": "2667-0968", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Manaf Al-Ok<PERSON>", "Affiliation": "School of Business, Jadara University, Irbid, Jordan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Administrative & Financial Sciences, University of Petra, Amman, Jordan"}, {"AuthorId": 3, "Name": "Aws Al<PERSON>", "Affiliation": "School of Business, Jadara University, Irbid, Jordan"}], "References": [{"Title": "Reporting Technologies and Textual Readability: Evidence from the XBRL Mandate", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "3", "Page": "1025", "JournalTitle": "Information Systems Research"}, {"Title": "Does financial awareness increase the acceptance rate for financial inclusion? An empirical examination in the era of digital transformation", "Authors": "Man<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "52", "Issue": "11", "Page": "4876", "JournalTitle": "Kybernetes"}, {"Title": "Factors Influencing the Decision to Utilize eTax Systems During the COVID-19 Pandemic", "Authors": "Man<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>z <PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Electronic Government Research"}, {"Title": "Job satisfaction, management sentiment, and financial performance: Text analysis with job reviews from indeed.com", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "1", "Page": "100155", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Unravelling the techno-functional building blocks of metaverse ecosystems – A review and research agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "100176", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Exploring artificial intelligence and big data scholarship in information systems: A citation, bibliographic coupling, and co-word analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "2", "Page": "100185", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Big data and business analytics enabled innovation and dynamic capabilities in organizations: Developing and validating scale", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "2", "Page": "100206", "JournalTitle": "International Journal of Information Management Data Insights"}]}, {"ArticleId": 114255711, "Title": "POEM: Performance Optimization and Endurance Management for Non-volatile Caches", "Abstract": "<p> Non-volatile memories (NVMs) with their high storage density and ultra-low leakage power offer promising potential for redesigning the memory hierarchy in next-generation Multi-Processor Systems-on-Chip (MPSoCs). However, the adoption of NVMs in cache designs introduces challenges such as NVM write overheads and limited NVM endurance. The shared NVM cache in an MPSoC experiences requests from different processor cores and responses from the off-chip memory when the requested data is not present in the cache. Besides, upon evictions of dirty data from higher-level caches, the shared NVM cache experiences another source of write operations, known as writebacks . These sources of write operations: writebacks and responses, further exacerbate the contention for the shared bandwidth of the NVM cache, and create significant performance bottlenecks. Uncontrolled write operations can also affect the endurance of the NVM cache, posing a threat to cache lifetime and system reliability. Existing strategies often address either performance or cache endurance individually, leaving a gap for a holistic solution. This study introduces the Performance Optimization and Endurance Management (POEM) methodology, a novel approach that aggressively bypasses cache writebacks and responses to alleviate the NVM cache contention. Contrary to the existing bypass policies which do not pay adequate attention to the shared NVM cache contention, and focus too much on cache data reuse, POEM’s aggressive bypass significantly improves the overall system performance, even at the expense of data reuse. POEM also employs effective wear leveling to enhance the NVM cache endurance by careful redistribution of write operations across different cache lines. Across diverse workloads, POEM yields an average speedup of \\(34\\% \\) over a naïve baseline and \\(28.8\\% \\) over a state-of-the-art NVM cache bypass technique, while enhancing the cache endurance by \\(15\\% \\) over the baseline. POEM also explores diverse design choices by exploiting a key policy parameter that assigns varying priorities to the two system-level objectives. </p>", "Keywords": "", "DOI": "10.1145/3653452", "PubYear": 2024, "Volume": "29", "Issue": "5", "JournalId": 8858, "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems", "ISSN": "1084-4309", "EISSN": "1557-7309", "Authors": [{"AuthorId": 1, "Name": "Aritra Bagchi", "Affiliation": "Indian Institute of Technology Delhi, New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Delhi, New Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Delhi, New Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Delhi, New Delhi, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Delhi, New Delhi, India"}], "References": [{"Title": "Improving the Performance of Hybrid Caches Using Partitioned Victim Caching", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "An Energy-Efficient DRAM Cache Architecture for Mobile Platforms With PCM-Based Main Memory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "CORIDOR: Using\n CO \n herence and Tempo\n R \n al Local\n I \n ty to Mitigate Read\n D \n isurbance Err\n OR \n in STT-RAM Caches", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "WiSE: W hen Learning Ass i sts Resolving S TT-MRAM E fficiency Challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "11", "Issue": "1", "Page": "43", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Self Adaptive Logical Split Cache Techniques for Delayed Aging of NVM LLC", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "28", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems"}, {"Title": "CABARRE: Request Response Arbitration for Shared Cache Management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "5s", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "COBRRA: COntention-aware cache Bypass with Request-Response Arbitration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "23", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}]}, {"ArticleId": 114255715, "Title": "Improving participation equity in dialogic collaborative problem solving: A participatory visual learning analytical approach", "Abstract": "Background The existing research on dialogue‐based learning and teaching predominantly highlights its capacity to yield productive educational outcomes, yet it often overlooks the pivotal factor of participation equity, which is fundamental to ensuring the efficacy of dialogic teaching and learning. Objectives In this study, participation equity refers to a condition in which participation itself and opportunities to participate are fairly distributed among participants and all participants are equally listened to and respected. We designed a technology‐enhanced participatory visual learning analytical approach to promote equitable participation in dialogic collaborative problem solving from four dimensions: participation (i.e., ensuring equal contributions from all participants), opportunity (i.e., promoting equal engagement with others), responsiveness (i.e., encouraging equal attentiveness and responsiveness to others' input), and respect (i.e., cultivating a respectful communication style). Methods The intervention class of fourth‐grade students ( n  = 59) interacted with a participatory visual learning analytical tool to reflect on their participation equity and learn productive peer talk moves to address equity issues concerning the four dimensions, while a comparison class ( n  = 59) only received simple feedback on participation and respect. Results The results indicated that equal participation rates among group members were insufficient to secure participation equity. The intervention was effective in helping students realize and address equity‐related issues. Intervention students were more equitable regarding responsiveness and participation opportunity than the comparison students. Conclusions The proposed multidimensional participation equity framework has the potential to deepen the understanding of equity and promote equitable learning interactions.", "Keywords": "", "DOI": "10.1111/jcal.12975", "PubYear": 2024, "Volume": "40", "Issue": "4", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Education The University of Texas at El Paso  El Paso Texas USA;Faculty of Education The University of Hong Kong  Pokfulam Hong Kong China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Education The University of Hong Kong  Pokfulam Hong Kong China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Education The University of Hong Kong  Pokfulam Hong Kong China"}], "References": [{"Title": "Dialogic intervisualizing in multimodal inquiry", "Authors": "<PERSON>; <PERSON>-<PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "3", "Page": "283", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}, {"Title": "Promoting regulation of equal participation in online collaboration by combining a group awareness tool and adaptive prompts. But does it even matter?", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "1", "Page": "67", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}, {"Title": "Improving learning and writing outcomes: Influence of cognitive and behavioral group awareness tools in wikis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "2", "Page": "225", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}, {"Title": "iTalk–iSee: A participatory visual learning analytical tool for productive peer talk", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "17", "Issue": "3", "Page": "397", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}]}, {"ArticleId": 114255808, "Title": "Fusion dynamical systems with machine learning in imitation learning: A comprehensive overview", "Abstract": "Imitation Learning (IL), also referred to as Learning from Demonstration (LfD), holds significant promise for capturing expert motor skills through efficient imitation, facilitating adept navigation of complex scenarios. A persistent challenge in IL lies in extending generalization from historical demonstrations, enabling the acquisition of new skills without re-teaching. Dynamical system-based IL (DSIL) emerges as a significant subset of IL methodologies, offering the ability to learn trajectories via movement primitives and policy learning based on experiential abstraction. This paper emphasizes the fusion of theoretical paradigms, integrating control theory principles inherent in dynamical systems into IL. This integration notably enhances robustness, adaptability, and convergence in the face of novel scenarios. This survey aims to present a comprehensive overview of DSIL methods, spanning from classical approaches to recent advanced approaches. We categorize DSIL into autonomous dynamical systems and non-autonomous dynamical systems, surveying traditional IL methods with low-dimensional input and advanced deep IL methods with high-dimensional input. Additionally, we present and analyze three main stability methods for IL: Lyapunov stability, contraction theory, and diffeomorphism mapping. Our exploration also extends to popular policy improvement methods for DSIL, encompassing reinforcement learning, deep reinforcement learning, and evolutionary strategies. The primary objective is to expedite readers’ comprehension of dynamical systems’ foundational aspects and capabilities, helping identify practical scenarios and charting potential future directions. By offering insights into the strengths and limitations of dynamical system methods, we aim to foster a deeper understanding among readers. Furthermore, we outline potential extensions and enhancements within the realm of dynamical systems, outlining avenues for further exploration.", "Keywords": "", "DOI": "10.1016/j.inffus.2024.102379", "PubYear": 2024, "Volume": "108", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Multi-Scale Medical Robotics Centre, Ltd., The Chinese University of Hong Kong, Hong Kong, China;School of Computation, Information and Technology, Technical University of Munich, Munich, 85748, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electronic and Computer Science Department, Faculty of Engineering, Mondragon Unibertsitatea, 20500 Arrasate, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Automation Engineering, The Chinese University of Hong Kong, Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Surgery, The Chinese University of Hong Kong, Hong Kong, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Multi-Scale Medical Robotics Centre, Ltd., The Chinese University of Hong Kong, Hong Kong, China;Department of Surgery, The Chinese University of Hong Kong, Hong Kong, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computation, Information and Technology, Technical University of Munich, Munich, 85748, Germany"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Nantong University, Nantong, 226019, China;Corresponding authors"}], "References": [{"Title": "Reinforcement learning based on movement primitives for contact tasks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "", "Page": "101863", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Recent Advances in Robot Learning from Demonstration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "1", "Page": "297", "JournalTitle": "Annual Review of Control, Robotics, and Autonomous Systems"}, {"Title": "Learning stabilizable nonlinear dynamics with contraction-based regularization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "10-11", "Page": "1123", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "Robot skill learning in latent space of a deep autoencoder neural network", "Authors": "<PERSON><PERSON>č; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "135", "Issue": "", "Page": "103690", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Variable Impedance Control and Learning—A Review", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "177", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Dynamic Movement Primitives: Volumetric Obstacle Avoidance Using Dynamic Potential Functions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "4", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "A Survey of Robot Learning Strategies for Human-Robot Collaboration in Industrial Settings", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102231", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Contraction theory for nonlinear stability analysis and learning-based control: A tutorial overview", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "", "Page": "135", "JournalTitle": "Annual Reviews in Control"}, {"Title": "Motion generation for walking exoskeleton robot using multiple dynamic movement primitives sequences combined with reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "8", "Page": "2732", "JournalTitle": "Robotica"}, {"Title": "An Adaptive Imitation Learning Framework for Robotic Complex Contact-Rich Insertion Tasks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "414", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Learning neural-shaped quadratic L<PERSON><PERSON>nov function for stable, accurate and generalizable human–robot skills transfer", "Authors": "<PERSON><PERSON><PERSON>; Dong<PERSON> Qin; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "", "Page": "102526", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Model predictive optimization for imitation learning from demonstrations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "163", "Issue": "", "Page": "104381", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Learning stable robotic skills on Riemannian manifolds", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>ki", "PubYear": 2023, "Volume": "169", "Issue": "", "Page": "104510", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "PI-ELM: Reinforcement learning-based adaptable policy improvement for dynamical system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "650", "Issue": "", "Page": "119700", "JournalTitle": "Information Sciences"}, {"Title": "Dynamic movement primitives in robotics: A tutorial survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "13", "Page": "1133", "JournalTitle": "The International Journal of Robotics Research"}]}, {"ArticleId": 114255848, "Title": "Design of an AI-Enhanced Medical Research-Informed Multi-Sensor-Based Online Physical Education Motion Capture System", "Abstract": "Online physical education has increasingly gained public attention due to China's growing interest in this kind of instruction. However, the growth of online physical education has its challenges. For instance, due to the limits of the objective site, teaching outcomes for online physical education courses are not satisfactory because students cannot watch all course components. Without expanding the current teaching volume, a new teaching strategy built on a multi-sensor motion capture system is suggested to overcome these issues. The outcomes of the experiments demonstrate that the proposed motion capture technology can significantly boost motion capture's effectiveness in online physical education. © 2024 U-turn Press LLC.", "Keywords": "AI-Enhanced Medical Research; motion capture; multi-sensor; online teaching; physical education", "DOI": "10.14733/cadaps.2024.S24.253-267", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dean's Office, Hebei Sport University, Hebei Province, Shijiazhuang, 050041, China"}, {"AuthorId": 2, "Name": "Xinyang Xing", "Affiliation": "Physical Education Department, Hebei Sport University, Hebei province, Shijiazhuang, 050041, China"}], "References": []}, {"ArticleId": 114255906, "Title": "Numerical prediction and experimental validation of forming limit curves of laminated half-hard aluminum sheets", "Abstract": "<p>Considering the recent and widespread use of half-hard rolled aluminum sheets in various industrial sectors, this study aims to characterize, determine and evaluate the formability of these sheets. The first phase is to experimentally determine the forming limit curve of laminated half-hard aluminum sheet AA1050-H24. The FLC is determined experimentally from the limiting strain values measured at the fracture location of the specimen using the Nakazima test. Different geometries of the laminated sheet were produced to obtain different deformation paths in the plane of the main deformations. However, experimentally determining a forming limit curve is very time intensive and requires dedicated and costly equipment. The second phase is to develop an alternative method to replace the experimental protocol. Indeed, we will propose a hybrid approach between the finite element method and necking criterion for determining the onset of localized necking in order to numerically predict this curve. In order to make a numerical prediction, Abaqus/Explicit was used to perform finite element modeling of the Nakazima test. The necking criterion based on the first component of the limit strain was used the time of appearance of necking and to plot the forming limit curve. A comparison of the experimental and numerical results is carried out to determine the effectiveness of the necking criterion in the numerical evaluation of the formability of aluminum sheet AA1050-H24. The necking criterion can numerically evaluate the formability of the AA1050-H24 sheet.</p>", "Keywords": "Sheet metal forming; Forming Limit Curves (FLC); Half-hard aluminum AA1050-H24; Necking criterion; Numerical-experimental approach", "DOI": "10.1007/s00170-024-13473-3", "PubYear": 2024, "Volume": "132", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering Laboratory, <PERSON><PERSON> University, Fez, Morocco; Corresponding author."}, {"AuthorId": 2, "Name": "Iliass EL Mrabti", "Affiliation": "Mechanical Engineering Laboratory, Sid<PERSON>h University, Fez, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Laboratory, Sid<PERSON>h University, Fez, Morocco"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Industrial Techniques Laboratory, Sid<PERSON>ellah University, Fez, Morocco"}, {"AuthorId": 5, "Name": "Abdelhadi EL Hakimi", "Affiliation": "Mechanical Engineering Laboratory, Sid<PERSON>h University, Fez, Morocco"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering Laboratory, Sid<PERSON>h University, Fez, Morocco"}], "References": [{"Title": "Experimental and numerical investigation on forming limit curves of AA6082 aluminum alloy at high strain rates", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "7-8", "Page": "1973", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Effect of process parameters on the deep drawing formability of aluminum and advanced high-strength steel square cups", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "5-6", "Page": "1827", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 114255972, "Title": "Enhancing Stock Price Prediction Using Stacked Long Short-Term Memory", "Abstract": "<p>This research explores the Stacked Long Short-Term Memory (LSTM) model for stock price prediction using a dataset obtained from Yahoo Finance. The main objective is to assess the effectiveness of the model in capturing stock price patterns and making accurate predictions. The dataset consists of stock prices for the top 10 companies listed in the Indonesia Stock Exchange from July 6, 2015, to October 14, 2021. The model is trained and evaluated using metrics such as RMSE, MAE, MAPE, and R2. The average values of these metrics for the predictions indicate promising results, with an average RMSE of 0.00885, average MAE of 0.00800, average MAPE of 0.02496, and an average R2 of 0.9597. These findings suggest that the Stacked LSTM model can effectively capture stock price patterns and make accurate predictions. The research contributes to the field of stock price prediction and highlights the potential of deep learning techniques in financial forecasting.</p>", "Keywords": "", "DOI": "10.25299/itjrd.2023.13486", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 58679, "JournalTitle": "IT JOURNAL RESEARCH AND DEVELOPMENT", "ISSN": "2528-4061", "EISSN": "2528-4053", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Study on the prediction of stock price based on the associated network model of LSTM", "Authors": "Guang<PERSON>; Liangxi Qin", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "1307", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Optimizing LSTM for time series prediction in Indian stock market", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2091", "JournalTitle": "Procedia Computer Science"}, {"Title": "A graph-based CNN-LSTM stock price prediction algorithm with leading indicators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "3", "Page": "1751", "JournalTitle": "Multimedia Systems"}, {"Title": "Application of LSTM and CONV1D LSTM Network in Stock Forecasting Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "1", "Page": "36", "JournalTitle": "Artificial Intelligence Advances"}]}, {"ArticleId": *********, "Title": "A Language Framework for Measuring Semantic and Syntactic Similarity for Arabic Texts", "Abstract": "<p>A language framework for determining the similarity of two snipped texts is proposed. The edit distance concept is employed as a frame algorithm to capture syntactic and semantic similarities. In the proposed work, syntax level distances between lemma-form words are calculated, while partial edit costs are allowed to embed semantic similarity measurements. Many knowledge resources have been used, such as words’ synonyms, negation rules, and word semantic spaces. A researchable Arabic thesaurus dictionary is built in two forms, surface form and lemma form. Semantic word spaces are generated from one of the word embedding models, which represents the words in vector spaces. The algorithm is enhanced to overcome problems with different word orders between sentences by a word permutation technique that elects the best alignment of the snipped text words to yield the best matching score. The algorithm also studied the effect of negation words on textual similarity. The proposed approach was implemented to find the similarity between Arabic language texts. Results are compared with other state-of-the-art algorithms using two benchmark datasets. The experimental results show that the proposed approach achieves a higher Pearson correlation coefficient compared to other works.</p>", "Keywords": "Arabic text similarity; Semantic similarity; Lexical similarity; Word embedding; Permutation feature; Negation effect", "DOI": "10.1007/s42979-024-02691-x", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Systems Department, Faculty of Computers and Artificial Intelligence, Benha University, Benha, Egypt; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Systems Department, Faculty of Engineering Shoubra, Benha University, Benha, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Information Systems Department, Faculty of Computers and Artificial Intelligence, Benha University, Benha, Egypt"}], "References": [{"Title": "Automatic text summarization: A comprehensive survey", "Authors": "Waf<PERSON> <PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113679", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Text data augmentations: Permutation, antonyms and negation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "114769", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Effect of stemming on text similarity for Arabic language at sentence level", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Building Arabic Paraphrasing Benchmark based on Transformation Rules", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Arabic sentence similarity based on similarity features and machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "15", "Page": "10089", "JournalTitle": "Soft Computing"}, {"Title": "A New Alignment Word-Space Approach for Measuring Semantic Similarity for Arabic Text", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal on Semantic Web and Information Systems"}]}, {"ArticleId": 114256052, "Title": "An Argonaute-mediated bio-barcode bioassay for one-tube and on-site detection of Staphylococcus aureus", "Abstract": "Isothermal amplification method has enabled major advancement toward rapid and deployable detection for nucleic acid. While exciting, there are still a series of deficiencies facing their practical implementation, such as carryover cross-contamination, which are major bottlenecks that must be addressed. In addition, there is a lack of high-throughput detection for foodborne pathogens in foods. Here, we reported an Argonaute-mediated bio-barcode bioassay for one-step cleavage, on-site and high-throughput detection of S. aureus ( Staphylococcus aureus ) through introducing Tag sequence and recombinase aided amplification (RAA). Furthermore, we designed a single vessel via tube-in-tube manner for one-tube contamination-free and portable detection without opening the lid. With this strategy, the same detection limit as fluorescence detection could be obtained, 1 CFU/mL with high specificity for S. aureus . We further evaluated the performance of bio-barcode biosensing strategy using S. aureus -contaminated clinical and food samples. Argonaute-mediated bio-barcode bioassay had the advantages of modular, universal plug-and-play and high-throughput scanning. This work not only satisfied the requirement for convenient detection but also fulfilled the need for portable on-site nucleic acid detection with portability, adaptability, transferability and practicality.", "Keywords": "", "DOI": "10.1016/j.snb.2024.135713", "PubYear": 2024, "Volume": "410", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Food Nutrition and Safety, Key Laboratory of Industrial Microbiology, Ministry of Education, Tianjin Key Laboratory of Industry Microbiology, National and Local United Engineering Lab of Metabolic Control Fermentation Technology, China International Science and Technology Cooperation Base of Food Nutrition/Safety and Medicinal Chemistry, College of Biotechnology, Tianjin University of Science & Technology, Tianjin 300457, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Food Nutrition and Safety, Key Laboratory of Industrial Microbiology, Ministry of Education, Tianjin Key Laboratory of Industry Microbiology, National and Local United Engineering Lab of Metabolic Control Fermentation Technology, China International Science and Technology Cooperation Base of Food Nutrition/Safety and Medicinal Chemistry, College of Biotechnology, Tianjin University of Science & Technology, Tianjin 300457, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Food Nutrition and Safety, Key Laboratory of Industrial Microbiology, Ministry of Education, Tianjin Key Laboratory of Industry Microbiology, National and Local United Engineering Lab of Metabolic Control Fermentation Technology, China International Science and Technology Cooperation Base of Food Nutrition/Safety and Medicinal Chemistry, College of Biotechnology, Tianjin University of Science & Technology, Tianjin 300457, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Food Nutrition and Safety, Key Laboratory of Industrial Microbiology, Ministry of Education, Tianjin Key Laboratory of Industry Microbiology, National and Local United Engineering Lab of Metabolic Control Fermentation Technology, China International Science and Technology Cooperation Base of Food Nutrition/Safety and Medicinal Chemistry, College of Biotechnology, Tianjin University of Science & Technology, Tianjin 300457, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Food Nutrition and Safety, Key Laboratory of Industrial Microbiology, Ministry of Education, Tianjin Key Laboratory of Industry Microbiology, National and Local United Engineering Lab of Metabolic Control Fermentation Technology, China International Science and Technology Cooperation Base of Food Nutrition/Safety and Medicinal Chemistry, College of Biotechnology, Tianjin University of Science & Technology, Tianjin 300457, China;Corresponding authors"}, {"AuthorId": 6, "Name": "Shengying Ye", "Affiliation": "Pharmacy Department, The 983th Hospital of the Joint Logistics Support Force of the Chinese People’s Liberation Army, Tianjin 300142, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Food Nutrition and Safety, Key Laboratory of Industrial Microbiology, Ministry of Education, Tianjin Key Laboratory of Industry Microbiology, National and Local United Engineering Lab of Metabolic Control Fermentation Technology, China International Science and Technology Cooperation Base of Food Nutrition/Safety and Medicinal Chemistry, College of Biotechnology, Tianjin University of Science & Technology, Tianjin 300457, China;Corresponding authors"}], "References": []}, {"ArticleId": 114256175, "Title": "Nucleus segmentation from the histopathological images of liver cancer through an efficient deep learning framework", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-18705-y", "PubYear": 2025, "Volume": "84", "Issue": "8", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "Hesham S. Almoallim", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "S. S<PERSON>", "Affiliation": ""}], "References": [{"Title": "DAN-NucNet: A dual attention based framework for nuclei segmentation in cancer histology images under wild clinical conditions", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "118945", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep learning based computer-aided automatic prediction and grading system for diabetic retinopathy", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "25", "Page": "39255", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "PlaNet: a robust deep convolutional neural network model for plant leaves disease recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "2", "Page": "4465", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Deep-learning based system for effective and automatic blood vessel segmentation from Retinal fundus images", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "2", "Page": "6005", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 114256195, "Title": "A conversational agent framework for mental health screening: design, implementation, and usability", "Abstract": "", "Keywords": "", "DOI": "10.1080/0144929X.2024.2332934", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Babeş-Bolyai University, Cluj-Napoca, Romania"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Interdisciplinary School of Doctoral Studies, University of Bucharest, Bucharest, Romania"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Interdisciplinary School of Doctoral Studies, University of Bucharest, Bucharest, Romania"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Interdisciplinary School of Doctoral Studies, University of Bucharest, Bucharest, Romania"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Polytechnic University of Bucharest, Bucharest, Romania"}, {"AuthorId": 6, "Name": "Ioana <PERSON>", "Affiliation": "Laboratory of Cognitive Clinical Sciences, University of Bucharest, Bucharest, Romania"}], "References": [{"Title": "Tell Me About Yourself", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "Screening for common mental health disorders: a psychometric evaluation of a chatbot system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "Behaviour & Information Technology"}]}, {"ArticleId": 114256245, "Title": "Investigation on ultrasonic-assisted grinding of characteristics of C/SiC composites by brazed and electroplated diamond wheels", "Abstract": "<p>Ultrasonic-assisted grinding (UAG) can effectively reduce the grinding force and improve the machining quality of carbon fiber-reinforced ceramic matrix composites (C/SiC) due to its excellent machining performance. Many researches have shown that the machining performance of UAG is closely related to the kinematic trajectory characteristics of the abrasive grains, so the surface morphology of diamond grinding wheels with various process preparations has an important influence on the grinding quality. In this paper, to investigate the performance of UAG of C/SiC composites with diamond grinding wheels prepared by different processes, UAG experiments were carried out on C/SiC composites using diamond grinding wheels prepared by brazing and electroplating processes. The trajectories of the abrasive grains and the maximum undeformed chip thicknesses with different abrasive grain protrusion heights were analyzed and the impact of the two diamond grinding wheels and the grain mesh size on the grinding force, surface roughness, and surface morphology were comparatively investigated. The results indicated that the grinding force and surface roughness obtained by brazed grinding wheels were reduced by 13 ~ 36.05% and 5.95 ~ 19.3% compared with electroplated diamond wheels at 240#, respectively. The brazed grinding wheels could improve the grinding surface morphology to a certain extent. However, the surface roughness value fluctuated with the increase of abrasive grain size. Additionally, the protrusion height of the abrasive grain of the brazed diamond wheels is more favorable for the UAG effect and the grinding wheels are less prone to clogging.</p>", "Keywords": "Brazed grinding wheel; Grain protrusion heights; C/SiC composites; Ultrasonic-assisted grinding; Grinding force", "DOI": "10.1007/s00170-024-13465-3", "PubYear": 2024, "Volume": "132", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electronic Engineering, Wuhan University of Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Electronic Engineering, Wuhan University of Technology, Wuhan, People’s Republic of China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electronic Engineering, Wuhan University of Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electronic Engineering, Wuhan University of Technology, Wuhan, People’s Republic of China"}], "References": [{"Title": "Investigation on machined surface quality in ultrasonic-assisted grinding of Cf/SiC composites based on fracture mechanism of carbon fibers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "5-6", "Page": "1583", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 114256251, "Title": "Improved multiple-Toeplitz matrices reconstruction method using quadratic spatial smoothing for coherent signals DOA estimation", "Abstract": "Purpose The purpose of this paper is to exploit the multiple-Toeplitz matrices reconstruction method combined with quadratic spatial smoothing processing to improve the direction-of-arrival (DOA) estimation performance of coherent signals at low signal-to-noise ratio (SNRs). Design/methodology/approach An improved multiple-Toeplitz matrices reconstruction method is proposed via quadratic spatial smoothing processing. Our proposed method takes advantage of the available information contained in the auto-covariance matrices of individual Toeplitz matrices and the cross-covariance matrices of different Toeplitz matrices, which results in a higher noise suppression ability. Findings Theoretical analysis and simulation results show that, compared with the existing Toeplitz matrix processing methods, the proposed method improves the DOA estimation performance in cases with a low SNR. Especially for the cases with a low SNR and small snapshot number as well as with closely spaced sources, the proposed method can achieve much better performance on estimation accuracy and resolution probability. Research limitations/implications The study investigates the possibility of reusing pre-existing designs for the DOA estimation of the coherent signals. The proposed technique enables achieve good estimation performance at low SNRs. Practical implications The paper includes implications for the DOA problem at low SNRs in communication systems. Originality/value The proposed method proved to be useful for the DOA estimation at low SNR.", "Keywords": "DOA;Spatial smoothing;Coherent signals;Cross correlation", "DOI": "10.1108/EC-08-2023-0416", "PubYear": 2024, "Volume": "41", "Issue": "2", "JournalId": 30507, "JournalTitle": "Engineering Computations", "ISSN": "0264-4401", "EISSN": "1758-7077", "Authors": [{"AuthorId": 1, "Name": "Bingbing Qi", "Affiliation": "School of Information and Electronics , Beijing Institute of Technology , Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Electronics , Beijing Institute of Technology , Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Electronics , Beijing Institute of Technology , Beijing, China"}], "References": [{"Title": "An enhanced spatial smoothing algorithm for coherent signals DOA estimation", "Authors": "<PERSON><PERSON> Qi; <PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "2", "Page": "574", "JournalTitle": "Engineering Computations"}]}, {"ArticleId": 114256351, "Title": "Deconstruction of the influence of entrepreneurial orientation on innovation performance based on logistic regression model", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2024.10063249", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "An Efficient and Secure Blockchain Consensus Algorithm Using Game Theory", "Abstract": "Blockchain technology is a decentralized ledger system that finds applications in various domains such as banking, e-governance, and supply chain management. The consensus algorithm plays a crucial role in any blockchain network as it directly impacts the network's performance and security. There have been several proposed consensus mechanisms in the literature, including Proof of Work (PoW), Proof of Stake (PoS), Robust Proof of Stake (RPoS), and Delegated Proof of Stake (DPoS). Both Ethereum and Bitcoin utilize the PoW consensus mechanism, where nodes compete to solve puzzles in order to generate blocks, consuming significant processing power. On the other hand, the PoS consensus mechanism selects miners based on the stakes they hold, making it more energy efficient. However, PoS has drawbacks such as vulnerability to coin age accumulation attacks and the potential for partial centralization. In this work, we present a consensus mechanism known as Delegated Proof of Stake with Downgrading Mechanism using Game Theory (DDPoS (GT)). This mechanism employs a two-step game strategy to divide nodes into strong and weak nodes, as well as attack and non-attack nodes. Later, the results of the two games are combined to enhance protocol efficiency and security. Experimental results using a private Ethereum-based network demonstrate that DDPoS (GT) performs better than PoS and DPoS in terms of transaction latency, average block waiting time, and fairness. © 2024, Modern Education and Computer Science Press. All rights reserved.", "Keywords": "Blockchain; Consensus; DPOS; Ethereum; Game Theory; POS; POW", "DOI": "10.5815/ijcnis.2024.02.08", "PubYear": 2024, "Volume": "16", "Issue": "2", "JournalId": 20260, "JournalTitle": "International Journal of Computer Network and Information Security", "ISSN": "2074-9090", "EISSN": "2074-9104", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "KLE Technological University, School of Computer Science and Engineering, Hubli, 580031, India"}, {"AuthorId": 2, "Name": "Narayan D. G.", "Affiliation": "KLE Technological University, School of Computer Science and Engineering, Hubli, 580031, India"}, {"AuthorId": 3, "Name": "Altaf Husain M.", "Affiliation": "KLE Technological University, School of Computer Science and Engineering, Hubli, 580031, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "KLE Technological University, School of Computer Science and Engineering, Hubli, 580031, India"}], "References": []}, {"ArticleId": 114256503, "Title": "Study on the factors affecting the performance of oil-filled pressure sensitive core", "Abstract": "Purpose \nThe performance of oil-filled pressure cores is very much affected by the corrugated diaphragm and the oil filling volume. The purpose of this paper is to show the effects of different corrugated diaphragms, different oil filling volumes and different treatments of the corrugated diaphragms on the performance of pressure sensors.\n \n \n Design/methodology/approach \nPressure-sensitive cores with different diaphragm diameters, different diaphragm ripple numbers and different oil filling volumes are produced, and thermal cycling is introduced to improve the diaphragm performance, and finally the performance of each pressure-sensitive core is tested and the test data are analyzed and compared.\n \n \n Findings \nThe experimental results show that the larger the diameter of the corrugated diaphragm used for encapsulation, the better the performance. For pressure-sensitive cores using smaller diameter corrugated diaphragms, the performance of one corrugation is better than that of two corrugations. When the number of corrugations and the diameter are the same size, the performance of the outer ring of the diaphragm with concave corrugations is better than that with convex corrugations. At the same time, the diaphragm after thermal cycling treatment and appropriate reduction of encapsulated oil filling can improve the performance of the pressure-sensitive core.\n \n \n Originality/value \nBy exploring the effects of corrugated diaphragm and oil filling volume on the performance of oil-filled pressure cores, the design of oil-filled pressure sensors can be guided to improve sensor performance.", "Keywords": "Pressure sensor;Corrugated diaphragm;Oil-filling", "DOI": "10.1108/SR-01-2024-0042", "PubYear": 2024, "Volume": "44", "Issue": "2", "JournalId": 5762, "JournalTitle": "Sensor Review", "ISSN": "0260-2288", "EISSN": "1758-6828", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The 48th Research Institute of China Electronics Technology Group Corporation , Changsha, China and The State Key Laboratory of Precision Manufacturing for Extreme Service Performance and School of Mechanical and Electronical Engineering, Central South University , Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The 48th Research Institute of China Electronics Technology Group Corporation , Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The 48th Research Institute of China Electronics Technology Group Corporation , Changsha, China and The State Key Laboratory of Precision Manufacturing for Extreme Service Performance and School of Mechanical and Electronical Engineering, Central South University , Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Liu", "Affiliation": "The State Key Laboratory of Precision Manufacturing for Extreme Service Performance and School of Mechanical and Electronical Engineering, Central South University , Changsha, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The State Key Laboratory of Precision Manufacturing for Extreme Service Performance and School of Mechanical and Electronical Engineering, Central South University , Changsha, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "The State Key Laboratory of Precision Manufacturing for Extreme Service Performance and School of Mechanical and Electronical Engineering, Central South University , Changsha, China"}], "References": [{"Title": "A packaging technique of pressure sensor for in vivo measurement system", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "340", "Issue": "", "Page": "113514", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 114256642, "Title": "A receiver-driven transport protocol using differentiated algorithms for differential congestion in datacenters", "Abstract": "With the continuous development of applications, data centers run application traffic from various service providers, and the requirements for data center networks are also increasing. Congestion control has always been a hot topic of discussion. Prevalent sender-driven transport protocols require adjusting their sending rate after network congestion occurs, and the long feedback delay makes them susceptible to a buffer overflow. Therefore, receiver-driven transport protocols are proposed, and the receiver adopts credits to allocate downlink bandwidth to overcome network congestion. This type of scheme solves last-hop congestion and reduces congestion feedback latency. However, the existing receiver-driven transport protocol is insensitive to congestion location and thus reacts inaccurately to congestion. To address this problem, this paper proposes a congestion control algorithm with congestion location awareness. In this scheme, 1) we first design an ECN-based congestion detection method to achieve lightweight congestion types awareness, i.e., instantaneous congestion and continuous congestion. 2) Based on this, we further design a differentiated transmission control strategy. For instantaneous congestion, we adopt adaptive backoff and delay control algorithms. For continuous congestion, we employ a rate control algorithm to reduce in-network congestion. This does not require complex modifications to the switch. In our evaluation, the overall average flow completion time (FCT) of DCC is up to 21%, 89%, 1.5 × , and 3.4 × better than Homa, ExpressPass, Timely, and pHost, respectively.", "Keywords": "", "DOI": "10.1016/j.comnet.2024.110357", "PubYear": 2024, "Volume": "245", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Peng Cheng Laboratory, Shenzhen 518066, Guangdong Province, China;Tsinghua Shenzhen International Graduate School, Shenzhen 518055, Guangdong Province, China"}, {"AuthorId": 2, "Name": "Qing Li", "Affiliation": "Peng Cheng Laboratory, Shenzhen 518066, Guangdong Province, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Peng Cheng Laboratory, Shenzhen 518066, Guangdong Province, China;Tsinghua Shenzhen International Graduate School, Shenzhen 518055, Guangdong Province, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Peng Cheng Laboratory, Shenzhen 518066, Guangdong Province, China;Tsinghua Shenzhen International Graduate School, Shenzhen 518055, Guangdong Province, China;Corresponding author"}], "References": []}, {"ArticleId": 114256811, "Title": "Hunter: Tracing anycast communications to uncover cross-border personal data transfers", "Abstract": "Cross-border personal data transfers are heavily regulated worldwide, with data protection authorities imposing huge fines on organizations that fail to meet their strict compliance requirements. However, network-level optimizations such as anycast addresses were not designed with personal data in mind, and their use may unwittingly divert personal data out of a legal boundary. This paper describes Hunter, an automated method to trace anycast communications and identify those threatening data protection compliance. We have applied Hunter in the wild to a set of Android apps to discover that all apps observed sending personal data to anycast addresses eventually carry out international transfers but fail to disclose them in their privacy policies. Our findings suggest that using anycast addresses to transmit personal data generally results in data protection compliance issues.", "Keywords": "Personal data; Data protection; International transfers; Traceability; IP geolocation; Anycast; Transparency; Privacy; GDPR; Compliance", "DOI": "10.1016/j.cose.2024.103823", "PubYear": 2024, "Volume": "141", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ETSI Telecomunicación, Universidad Politécnica de Madrid, Avenida Complutense 30, Madrid, 28040, Spain"}, {"AuthorId": 2, "Name": "Jose <PERSON> del Alamo", "Affiliation": "ETSI Telecomunicación, Universidad Politécnica de Madrid, Avenida Complutense 30, Madrid, 28040, Spain;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ETSI Telecomunicación, Universidad Politécnica de Madrid, Avenida Complutense 30, Madrid, 28040, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ETSI Telecomunicación, Universidad Politécnica de Madrid, Avenida Complutense 30, Madrid, 28040, Spain"}], "References": [{"Title": "Privacy protection of China’s top websites: A Multi-layer privacy measurement via network behaviours and privacy policies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "102606", "JournalTitle": "Computers & Security"}, {"Title": "A systematic mapping study on automated analysis of privacy policies", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "104", "Issue": "9", "Page": "2053", "JournalTitle": "Computing"}, {"Title": "Automated GDPR compliance assessment for cross-border personal data transfers in android applications", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "130", "Issue": "", "Page": "103262", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 114256812, "Title": "Paired 2-disjoint path covers of k-ary n-cubes under the partitioned edge fault model", "Abstract": "The k -ary n -cube Q n k serves as an indispensable interconnection network in the design of data center networks, network-on-chips, and parallel computing systems since it possesses numerous attractive properties. In these parallel architectures, the paired (or unpaired) many-to-many m -disjoint path cover ( m -DPC) plays a significant role in message transmission. Nevertheless, the construction of m -DPC is severely obstructed by large-scale edge faults due to the rapid growth of the system scale. In this paper, we investigate the existence of paired 2-DPC in Q n k under the partitioned edge fault (PEF) model, which is a novel fault model for enhancing the networks&#x27; fault-tolerance related to path embedding problem. We exploit this model to evaluate the edge fault-tolerance of Q n k when a paired 2-DPC is embedded into Q n k . Compared to the other known works, our results can help Q n k to achieve large-scale edge fault-tolerance.", "Keywords": "", "DOI": "10.1016/j.jpdc.2024.104887", "PubYear": 2024, "Volume": "190", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou 350108, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou 350108, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information and Decision Sciences, National Taipei University of Business, Taipei 10051, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Data Science, Fuzhou University, Fuzhou 350108, China"}], "References": [{"Title": "Unpaired Many-to-Many Disjoint Path Covers on Bipartite k-Ary n-Cube Networks with Faulty Elements", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "3", "Page": "371", "JournalTitle": "International Journal of Foundations of Computer Science"}, {"Title": "Novel schemes for embedding Hamiltonian paths and cycles in balanced hypercubes with exponential faulty edges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "177", "Issue": "", "Page": "182", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": 114256862, "Title": "Data on electroconvulsive seizure in mice, effects of anesthesia on immediate early gene expression", "Abstract": "Although electroconvulsive therapy (ECT) is one of the most effective treatments for severe mood and psychotic disorders, the mechanisms underlying its therapeutic effects remain unknown. Electroconvulsive stimulation (ECS), the animal model for ECT, can be used to investigate the potential therapeutic mechanisms of ECT in rodents. ECS produces numerous effects in the brain, such as increasing levels of growth factors, inducing dendritic sprouting, and stimulating neurogenesis. It also induces high-level expression of immediate early genes (IEGs) that have been implicated in the pathogenesis of schizophrenia, such as early growth response 3 ( Egr3 ) and activity-regulated cytoskeleton-associated protein ( Arc ), a validated downstream target of Egr3 [1–3]. However, the effect of isoflurane anesthesia preceding ECS on IEG response in mice has not been well characterized. This article provides immunofluorescent data of the activity responsive IEG ARC in the dorsal and ventral dentate gyrus of wildtype (WT) mice following ECS with or without anesthesia, as well as following sham ECS. The data in this article relate to a published article that employed serial ECS in mice to investigate the requirement of Egr3 in the neurobiological effects of this model of ECT [4]. The ability to study the effects of serial ECS has been limited in mice due to high rates of mortality during seizure. Administration of isoflurane anesthesia prior to ECS significantly reduces rodent mortality, irrespective of the number of times ECS is applied [5]. Since general anesthesia is administered to patients prior to ECT, use of isoflurane prior to ECS also more closely models the clinical use of ECT [6].", "Keywords": "ECS;ECT;Early growth response 3;Egr3;Electroconvulsive therapy;IEG;Immediate early gene", "DOI": "10.1016/j.dib.2024.110365", "PubYear": 2024, "Volume": "54", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Basic Medical Sciences, University of Arizona, College of Medicine, 425 N. 5th Street, Phoenix, AZ 85004, USA."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Basic Medical Sciences, University of Arizona, College of Medicine, 425 N. 5th Street, Phoenix, AZ 85004, USA. ;School of Life Sciences, Arizona State University, 427 E Tyler Mall, Tempe, AZ 85281, USA."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Psychology, <PERSON><PERSON><PERSON><PERSON>, Waterloo, ON N2L 3C5, Canada."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Basic Medical Sciences, University of Arizona, College of Medicine, 425 N. 5th Street, Phoenix, AZ 85004, USA."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Basic Medical Sciences, University of Arizona, College of Medicine, 425 N. 5th Street, Phoenix, AZ 85004, USA."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Basic Medical Sciences, University of Arizona, College of Medicine, 425 N. 5th Street, Phoenix, AZ 85004, USA."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Epidemiology and Biostatistics, University of Arizona Mel and Enid <PERSON> College of Public Health-Phoenix, 714 E <PERSON> St #119, Phoenix, AZ 85006, USA."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Psychology, <PERSON><PERSON><PERSON><PERSON>, Waterloo, ON N2L 3C5, Canada."}, {"AuthorId": 9, "Name": "A.L. Gallitano", "Affiliation": "Basic Medical Sciences, University of Arizona, College of Medicine, 425 N. 5th Street, Phoenix, AZ 85004, USA."}], "References": []}, {"ArticleId": 114256873, "Title": "Passive UHF-RFID Tag With Printed Security Features for Authentication and Tamper Resistance", "Abstract": "The development of innovative, miniaturized, and low-cost Radio Frequency Identification (RFID) tags for application in asset monitoring, counterfeit prevention, or personnel tracking requires advancements in materials, fabrication processes, and packaging. Typical RFID tags can be circumvented by tampering, cloning, or spoofing; however, by adding security features to the tag, nefarious actions such as these can be mitigated. Toward this objective, this paper presents the design and fabrication of an Ultra High Frequency (UHF) RFID tag through flexible hybrid electronics (FHE) materials and processes for authentication and anti-tamper /anti-counterfeit applications. The presented UHF RFID tag consists of a passive RF chip and dipole antenna with embedded hardware and software security features. The tag was fabricated using a hybrid of manufacturing techniques including, conventional photolithography and additive aerosol jet printing. The design, materials selection, processing, and tailored FHE fabrication processes, led to achieving a system-level functional RFID tag with a read distance of up to 15 in (0.381 m). The dependency of the read distance on the host surface was studied by attaching tags to different materials including surfaces with various dielectric constants and thicknesses. The performance of the tags was evaluated under realistic use conditions by performing thermal cycling, bending, and wearability tests. The RFID tag’s resistance to different tamper attack vectors (vulnerability assessment) is demonstrated. Overall, the demonstrated UHF RFID tag opens new opportunities for the development of flexible, lightweight, and low-cost RFID tags that leverage FHE fabrication techniques and materials for authentication and anti-tamper applications.", "Keywords": "", "DOI": "10.1109/JRFID.2024.3382505", "PubYear": 2024, "Volume": "8", "Issue": "", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "System Science and Industrial Engineering, State University of New York at Binghamton, Binghamton, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "System Science and Industrial Engineering, State University of New York at Binghamton, Binghamton, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "System Science and Industrial Engineering, State University of New York at Binghamton, Binghamton, NY, USA"}, {"AuthorId": 4, "Name": "Riadh Al-Haidari", "Affiliation": "System Science and Industrial Engineering, State University of New York at Binghamton, Binghamton, NY, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "System Science and Industrial Engineering, State University of New York at Binghamton, Binghamton, NY, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "System Science and Industrial Engineering, State University of New York at Binghamton, Binghamton, NY, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "System Science and Industrial Engineering, State University of New York at Binghamton, Binghamton, NY, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "System Science and Industrial Engineering, State University of New York at Binghamton, Binghamton, NY, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Advanced Manufacturing Technologies, Lockheed Martin RMS, Owego, NY, USA"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Advanced Technology Center, Lockheed Martin Space, Billerica, MA, USA"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Advanced Technology Center, Lockheed Martin Space, Billerica, MA, USA"}], "References": []}, {"ArticleId": 114256914, "Title": "Retraction Note: How Have the COVID-19 Pandemic and Market Sentiment Affected the FX Market? Evidence from Statistical Models and Deep Learning Algorithms", "Abstract": "", "Keywords": "", "DOI": "10.1007/s44196-024-00480-1", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "DVC Office, Wuhan College, Wuhan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics, Xihua University, Chengdu, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics, Xihua University, Chengdu, People’s Republic of China"}], "References": []}, {"ArticleId": 114257031, "Title": "Joint facial action unit recognition and self-supervised optical flow estimation", "Abstract": "Facial action unit (AU) recognition and optical flow estimation are two highly correlated tasks, since optical flow can provide motion information of facial muscles to facilitate AU recognition. However, most existing AU recognition methods handle the two tasks independently by offline extracting optical flow as auxiliary information or directly ignoring the use of optical flow. In this paper, we propose a novel end-to-end joint framework of AU recognition and optical flow estimation, in which the two tasks contribute to each other. Moreover, due to the lack of optical flow annotations in AU datasets, we propose to estimate optical flow in a self-supervised manner. To regularize the self-supervised estimation of optical flow, we propose an identical mapping constraint for the optical flow guided image warping process, in which the estimated optical flow between two same images is required to not change the image during warping. Experiments demonstrate that our framework (i) outperforms most of the state-of-the-art AU recognition methods on the challenging BP4D and GFT benchmarks, and (ii) also achieves competitive self-supervised optical flow estimation performance.", "Keywords": "", "DOI": "10.1016/j.patrec.2024.03.022", "PubYear": 2024, "Volume": "181", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai 200240, China;School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221116, China;Engineering Research Center of Mine Digitization, Ministry of Education of the People’s Republic of China, Xuzhou 221116, China;Corresponding author at: Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221116, China;Engineering Research Center of Mine Digitization, Ministry of Education of the People’s Republic of China, Xuzhou 221116, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221116, China;Engineering Research Center of Mine Digitization, Ministry of Education of the People’s Republic of China, Xuzhou 221116, China"}, {"AuthorId": 4, "Name": "Hancheng Zhu", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221116, China;Engineering Research Center of Mine Digitization, Ministry of Education of the People’s Republic of China, Xuzhou 221116, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221116, China;Engineering Research Center of Mine Digitization, Ministry of Education of the People’s Republic of China, Xuzhou 221116, China"}], "References": [{"Title": "JÂA-Net: Joint Facial Action Unit Detection and Face Alignment Via Adaptive Attention", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "2", "Page": "321", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 114257065, "Title": "Medical Research Infused Analysis of Marketing Strategy for Community Agricultural Products Based on Consumers' Psychological Demands for Nutrition and Health", "Abstract": "To explore the marketing strategy of community agricultural products based on the psychological needs of consumers' nutrition and health, this paper combines the psychological needs of consumers to carry out community agricultural product marketing innovation, improve the marketing effect of community agricultural products, and meet the needs of consumers' psychological health. Moreover, this paper proposes a probabilistic propagation model based on network embedding as an influence propagation model in social media. In addition, this paper analyzes the nature of information dissemination networks on social media and studies some of the characteristics of high-influence users. The data simulation study shows that the algorithm and model proposed in this paper can play a specific role in analyzing the marketing strategy of community agricultural products based on the psychological needs of consumers' nutrition and health. © 2024 U-turn Press LLC.", "Keywords": "community agricultural products; consumers; Medical Research Infused; nutritional health; psychological needs", "DOI": "10.14733/cadaps.2024.S24.145-156", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Finance & Business, Zhenjiang College, Jiangsu, Zhenjiang, 212028, China"}], "References": []}, {"ArticleId": 114257242, "Title": "A Comprehensive Survey of Multi-Level Thresholding Segmentation Methods for Image Processing", "Abstract": "<p>In image processing, multi-level thresholding is a sophisticated technique used to delineate regions of interest in images by identifying intensity levels that differentiate different structures or objects. Multi-range intensity partitioning captures the complexity and variability of an image. The aim of metaheuristic algorithms is to find threshold values that maximize intra-class differences and minimize inter-class differences. Various approaches and algorithms are reviewed and their advantages, limitations, and challenges are discussed in this paper. In addition, the review identifies future research areas such as handling complex images and inhomogeneous data, determining thresholding levels automatically, and addressing algorithm interpretation. The comprehensive review provides insights for future advancements in multilevel thresholding techniques that can be used by researchers in the field of image processing.</p>", "Keywords": "", "DOI": "10.1007/s11831-024-10093-8", "PubYear": 2024, "Volume": "31", "Issue": "6", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Shahid <PERSON> University of Kerman, Kerman, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Shahid <PERSON> University of Kerman, Kerman, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, <PERSON><PERSON> of Kerman, Kerman, Iran; Corresponding author."}], "References": [{"Title": "Human mental search-based multilevel thresholding for image segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "105427", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multilevel thresholding for image segmentation using Krill Herd Optimization algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "5", "Page": "528", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Understanding Deep Learning Techniques for Image Segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deep Learning for Generic Object Detection: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "261", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "An efficient krill herd algorithm for color image multilevel thresholding segmentation problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106063", "JournalTitle": "Applied Soft Computing"}, {"Title": "Hyper-heuristic method for multilevel thresholding image segmentation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "113201", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An improved emperor penguin optimization based multilevel thresholding for color image segmentation", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105570", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A better balance in metaheuristic algorithms: Does it exist?", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100671", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A novel equilibrium optimization algorithm for multi-thresholding image segmentation problems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "17", "Page": "10685", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Slime mould algorithm: A new method for stochastic optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "300", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "An Efficient Automated Road Region Extraction from High Resolution Satellite Images using Improved Cuckoo Search with Multi-Level Thresholding Schema", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "1161", "JournalTitle": "Procedia Computer Science"}, {"Title": "A differential evolutionary adaptive Harris hawks optimization for two dimensional practical Masi entropy-based multilevel image thresholding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "3011", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "An ameliorated teaching–learning-based optimization algorithm based study of image segmentation for multilevel thresholding using <PERSON><PERSON>’s entropy and <PERSON><PERSON>’s between class variance", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "533", "Issue": "", "Page": "72", "JournalTitle": "Information Sciences"}, {"Title": "An Adaptive Thresholding Algorithm-Based Optical Character Recognition System for Information Extraction in Complex Images", "Authors": "<PERSON>; Adewale Op<PERSON>; Mba <PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "6", "Page": "784", "JournalTitle": "Journal of Computer Science"}, {"Title": "A survey of recent interactive image segmentation methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "4", "Page": "355", "JournalTitle": "Computational Visual Media"}, {"Title": "Application of evolutionary and swarm optimization in computer vision: a literature survey", "Authors": "<PERSON><PERSON><PERSON>; Naranchimeg Bold; Haitian Sun", "PubYear": 2020, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "IPSJ Transactions on Computer Vision and Applications"}, {"Title": "A Review of Supervised Classification based on Contrast Patterns: Applications, Trends, and Challenges", "Authors": "<PERSON><PERSON><PERSON>-<PERSON>; <PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "4", "Page": "797", "JournalTitle": "Journal of Grid Computing"}, {"Title": "A novel Black Widow Optimization algorithm for multilevel thresholding image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114159", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel evolutionary row class entropy based optimal multi-level thresholding technique for brain MR images", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114426", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multilevel thresholding image segmentation based on improved volleyball premier league algorithm using whale optimization algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "8", "Page": "12435", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An efficient multilevel thresholding based satellite image segmentation approach using a new adaptive cuckoo search algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114633", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Color image segmentation using Kapur, Otsu and Minimum Cross Entropy functions based on Exchange Market Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "172", "Issue": "", "Page": "114636", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A comprehensive survey of image segmentation: clustering methods, performance parameters, and benchmark datasets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "24", "Page": "35001", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Opposition-based Laplacian Equilibrium Optimizer with application in Image Segmentation using Multilevel Thresholding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114766", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An Improved Jellyfish Algorithm for Multilevel Thresholding of Magnetic Resonance Brain Image Segmentations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "3", "Page": "2961", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Investigation of butterfly optimization and gases Brownian motion optimization algorithms for optimal multilevel image thresholding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115286", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Effect of image binarization thresholds on breast cancer identification in mammography images using OTSU, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Thepade's SBTC", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "10-11", "Issue": "", "Page": "200046", "JournalTitle": "Intelligent Systems with Applications"}, {"Title": "An efficient multilevel thresholding segmentation method for thermography breast cancer imaging based on improved chimp optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115651", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An improved opposition-based marine predators algorithm for global optimization and multilevel thresholding image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "229", "Issue": "", "Page": "107348", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Many-objective multilevel thresholding image segmentation for infrared images of power equipment with boost marine predators algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107905", "JournalTitle": "Applied Soft Computing"}, {"Title": "A comprehensive review of image analysis methods for microorganism counting: from classical image processing to deep learning approaches", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "4", "Page": "2875", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "HWOA: A hybrid whale optimization algorithm with a novel local minima avoidance method for multi-level thresholding color image segmentation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "190", "Issue": "", "Page": "116145", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Segmentation and classification on chest radiography: a systematic survey", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "3", "Page": "875", "JournalTitle": "The Visual Computer"}, {"Title": "Modality specific U-Net variants for biomedical image segmentation: a survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "7", "Page": "5845", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "ST-AL: a hybridized search based metaheuristic computational algorithm towards optimization of high dimensional industrial datasets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "18", "Page": "13553", "JournalTitle": "Soft Computing"}, {"Title": "An improved whale optimization algorithm based on multilevel threshold image segmentation using the Otsu method", "Authors": "<PERSON><PERSON> Ma; <PERSON><PERSON>", "PubYear": 2022, "Volume": "113", "Issue": "", "Page": "104960", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Artificial rabbits optimization: A new bio-inspired meta-heuristic algorithm for solving engineering optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "105082", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Multilevel thresholding image segmentation using meta-heuristic optimization algorithms: comparative analysis, open challenges and new trends", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "10", "Page": "11654", "JournalTitle": "Applied Intelligence"}, {"Title": "Facilitating learning in immersive virtual reality: Segmentation, summarizing, both or none?", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "1", "Page": "218", "JournalTitle": "Journal of Computer Assisted Learning"}, {"Title": "Maximum entropy scaled super pixels segmentation for multi-object detection and scene recognition via deep belief network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "9", "Page": "13401", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multilevel thresholding satellite image segmentation using chaotic coronavirus optimization algorithm with hybrid fitness function", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "1", "Page": "855", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A hybrid transient search naked mole-rat optimizer for image segmentation using multilevel thresholding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119021", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A whale optimization algorithm with combined mutation and removing similarity for global optimization and multilevel thresholding image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "137", "Issue": "", "Page": "110130", "JournalTitle": "Applied Soft Computing"}, {"Title": "Image thresholding approaches for medical image segmentation - short literature review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "219", "Issue": "", "Page": "1485", "JournalTitle": "Procedia Computer Science"}, {"Title": "A survey on the utilization of Superpixel image for clustering based image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "23", "Page": "35493", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Insulator fault diagnosis based on multi-objectives multilevel thresholding method and boost particle swarm optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Iris Recognition Based on Multilevel Thresholding Technique and Modified Fuzzy c-Means Algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "4", "Page": "201", "JournalTitle": "Journal on Artificial Intelligence"}, {"Title": "A Comprehensive Review on Segmentation Techniques for Satellite Images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "7", "Page": "4325", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "An improved African vultures optimization algorithm using different fitness functions for multi-level thresholding image segmentation", "Authors": "Farhad <PERSON> Gharehchopogh; Turgay <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "6", "Page": "16929", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A comprehensive survey of feature selection techniques based on whale optimization algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "16", "Page": "47775", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A multi-level thresholding image segmentation method using hybrid Arithmetic Optimization and Harris Hawks Optimizer algorithms", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "241", "Issue": "", "Page": "122316", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114257327, "Title": "An offline coupling approach for efficient SPH simulations of long-distance tsunami events using wave source boundary condition", "Abstract": "The entire procedure of tsunami events consists of wave generation and propagation toward the coastal structures encompassing the long-range system domain. Therefore, instead of an integrated continuous simulation, two or several consecutive simulations are preferred for numerical efficiency, where the hydrodynamic information within a particular domain from the preceding simulation can be utilized for the wave sources in the subsequent simulation. In this study, as an offline coupling approach, the wave source boundary condition is proposed based on the particle-based Lagrangian framework. This boundary condition is integrated with the solution algorithm of the smoothed particle hydrodynamics (SPH) in-house parallel code. In the dambreak simulation, it is validated that the wave source boundary condition can encompass both the forward and reverse flows, where the numerical results of the full and partial simulations show good agreement in terms of the wave profile, velocity distributions, and measured pressure histories. Thereafter, the full and partial simulations of a tsunami experiment are carried out. The numerical results in the partial simulation agree well with those obtained in the full simulation in terms of the particle distributions, physical quantities, and loading histories (e.g., pressure, acceleration) on the shoreline structure. It is demonstrated that the proposed offline coupling approach using the wave source boundary condition is compatibly utilized with the developed in-house SPH parallel solver, facilitating the detailed FSI simulations within a reduced domain of interest for long-distance tsunami-like phenomena.", "Keywords": "", "DOI": "10.1016/j.advengsoft.2024.103632", "PubYear": 2024, "Volume": "192", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Intelligent Simulation Center, Division of National Supercomputing, Korea Institute of Science and Technology Information, 245 Daehak-ro, Yuseong-gu, Daejeon 34141, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>ong Jin", "Affiliation": "Department of Civil and Environmental Engineering, Korea Advanced Institute of Science and Technology, 291 Daehak-ro, Yuseong-gu, Daejeon 34141, Republic of Korea;Division of Engineering and Applied Science, California Institute of Technology, Pasadena, CA 91125, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Korea Advanced Institute of Science and Technology, 291 Daehak-ro, Yuseong-gu, Daejeon 34141, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Intelligent Simulation Center, Division of National Supercomputing, Korea Institute of Science and Technology Information, 245 <PERSON><PERSON><PERSON><PERSON><PERSON>, Yuseong-gu, Daejeon 34141, Republic of Korea;Corresponding author"}], "References": [{"Title": "Strong-form meshfree collocation method for multibody thermomechanical contact", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "1", "Page": "89", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 114257336, "Title": "An investigation on implementation of generating adversarial network-based surrogate models for prediction of turbine endwall film cooling effectiveness", "Abstract": "Film cooling is an essential cooling technique for turbine blade, which guides coolant to cover the turbine blades surface by arranging discrete film holes on it. For the newly designed cooling units, the process of predicting film cooling effectiveness is really complex and time-consuming. For the purpose of accelerating the design procedure, the U-Net which is a very popular algorithm in the field of deep learning was improved with the basic layer structure, then it was adopted as the proxy function for the film cooling effectiveness prediction. The basic samples were generated by Latin hypercube sampling method and calculated by commercial software. The results were verified by experiment in a linear cascade. The training and testing samples were calculated at experimental conditions (inlet Mach number values 0.12). The results showed that comparing with the basic U-Net network, the novel U-Net network has a great better accuracy on the prediction of contour results. Both of the improved U-Net networks had higher prediction accuracy than original models. The correlation index reached more than 99.5% and the first order error was lower than 0.3%. The number of basic neural layers is not positively correlated with the network prediction accuracy which exists an optimal ratio. The experimental verification shows that the generating adversarial network prediction results are consistent with the experimental results. The final prediction results showed that the U-Net model can be used as a surrogate model and meets the requirements for accuracy of film cooling effectiveness prediction.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.108268", "PubYear": 2024, "Volume": "133", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shaanxi Engineering Laboratory of Turbomachinery and Power Equipment, Institute of Turbomachinery, School of Energy & Power Engineering, Xi'an Jiaotong University, Xi'an, 710049, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Shaanxi Engineering Laboratory of Turbomachinery and Power Equipment, Institute of Turbomachinery, School of Energy & Power Engineering, Xi'an Jiaotong University, Xi'an, 710049, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Shaanxi Engineering Laboratory of Turbomachinery and Power Equipment, Institute of Turbomachinery, School of Energy & Power Engineering, Xi'an Jiaotong University, Xi'an, 710049, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shaanxi Engineering Laboratory of Turbomachinery and Power Equipment, Institute of Turbomachinery, School of Energy & Power Engineering, Xi'an Jiaotong University, Xi'an, 710049, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shaanxi Engineering Laboratory of Turbomachinery and Power Equipment, Institute of Turbomachinery, School of Energy & Power Engineering, Xi'an Jiaotong University, Xi'an, 710049, China"}], "References": [{"Title": "Pix2Pix Hyperparameter Optimisation Prediction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "1009", "JournalTitle": "Procedia Computer Science"}, {"Title": "Automated door placement in architectural plans through combined deep-learning networks of ResNet-50 and Pix2Pix-GAN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "244", "Issue": "", "Page": "122932", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114257363, "Title": "Cooperative working performance of a dual-arm robot system optimized by a neural network adaptive preset control", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSNET.2024.10063267", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114257364, "Title": "Enhanced GRU-BiLSTM Technique for Crop Yield Prediction", "Abstract": "<p>Agriculture is the major source of food and significantly contributes to Indian employment, and the economy is intricately tied to the outcomes of crop management, where the final yield and market prices play crucial roles. The final yield and the market price completely determined the outcome of crop management or agriculture in India. Real-time observation emerges as a critical determinant of overall crop production success. Recognizing the significance of insightful analysis and precise crop yield predictions for effective farming practices, this study proposes an enhanced model to address the imperative of accurate yield forecasting. The pre-processing steps of the proposed model include Min-Max normalization, deletion of irrelevant data, and addition of missing values. The pre-processed data is then subjected to feature extraction using an Improved Shearlet transform (IST). After feature extraction, feature selection is done using an Enhanced multi-objective Grey Wolf optimization (EMGWO) technique. Finally, the prediction is made using an enhanced Gate Recurrent Unit with a Bidirectional LSTM (GRU-BiLSTM) model. This enhanced the accuracy (97%), precision (93%), recall (97.25%) and F-measure (95.14%) of agricultural yield predictions. Various measures related to errors, such as RMSE, MSE, MAE, MedAE, R<sup>2</sup> and MSLE, are compared for the proposed model and other existing techniques.</p>", "Keywords": "Crop yield prediction; Normalization; Improved shearlet transform; Soil attributes; Optimized feature selection; Deep learning; Grey Wolf optimization", "DOI": "10.1007/s11042-024-18898-2", "PubYear": 2024, "Volume": "83", "Issue": "41", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science & Engineering, Amity University Uttar Pradesh, Noida, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Astana IT University, Astana, Kazakhstan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science & Engineering, NIT Agartala, Agartala, India"}], "References": [{"Title": "Fuzzy deep learning-based crop yield prediction model for sustainable agronomical frameworks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "20", "Page": "13205", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Hybrid Deep Learning-based Models for Crop Yield Prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "1", "JournalTitle": "Applied Artificial Intelligence"}]}, {"ArticleId": 114257365, "Title": "Loose to compact feature alignment for domain adaptive object detection", "Abstract": "Recently, great achievements have been made for deep learning based object detection methods. But their performance drops significantly when domain shifts occur. To address this problem, in this work we propose a loose to compact feature alignment method under an unsupervised domain adaptation framework. The entire feature alignment is performed in a divide and conquer manner, so as to distribute the alignment difficulties at two steps. At the first step, we loosen the goal at both image and instance levels. At the image level, a new Mask Guided Foreground Alignment (MGFA) module is proposed to make the alignment focus more on easier foreground regions, leaving the more diverse and more difficult background regions to the second step; at the instance level, we propose a Class-Wise Instance Alignment (CWIA) module with separated domain classifiers for different categories so as to ease the alignment. At the second step, the alignment is performed per pixel and per instance, achieving a more compact and better aligned feature space. We conduct experiments on three different adaptation scenarios, where we achieve comparable results, demonstrating the effectiveness of our proposed method.", "Keywords": "", "DOI": "10.1016/j.patrec.2024.03.021", "PubYear": 2024, "Volume": "181", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, China"}], "References": [{"Title": "LCU-Net: A novel low-cost U-Net for environmental microorganism image segmentation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "107885", "JournalTitle": "Pattern Recognition"}, {"Title": "Discriminative distribution alignment for domain adaptive object detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "474", "Issue": "", "Page": "48", "JournalTitle": "Neurocomputing"}, {"Title": "Camera domain adaptation based on cross-patch transformers for person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "159", "Issue": "", "Page": "84", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Learning invariant representation for unsupervised domain adaptive thorax disease classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "160", "Issue": "", "Page": "155", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 114257446, "Title": "The Influence of Governmental Support on Cyber-Security Adoption and Performance: The Mediation of Cyber Security and Technological Readiness", "Abstract": "<p>The accelerated cyberattacks presents a severe challenge to the companies, as they seem unprepared to confront the threat of cyberattacks, they will suffer enormous losses and have their performance suffer as a result. To better serve its population and communities, Kuwait will have improved and updated its national infrastructure by 2035. This study examines how the governmental top management support, cyber security readiness, and technology readiness affect employee's organizational security adoption intentions in Kuwait governmental organizations and realization of its benefits. The quantitative method was employed in this work. The study found that top management support influencing organizational security performance mediating by cyber security readiness and technology, which affects the tangible and intangible benefits. This study can help policy makers in governmental organizations to improve cyber security adoption. The findings of this study may be utilized for enhancing the sustainability of cyber security in governmental organizations in Kuwait.</p>", "Keywords": "", "DOI": "10.4018/IJBDCN.341264", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 26537, "JournalTitle": "International Journal of Business Data Communications and Networking", "ISSN": "1548-0631", "EISSN": "1548-064X", "Authors": [{"AuthorId": 1, "Name": "Aleyah Al-Sharhan", "Affiliation": "College of Technological Studies, PAAET, Kuwait City, Kuwait"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "American University of Kuwait, Kuwait"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "American University of Kuwait, Kuwait"}, {"AuthorId": 4, "Name": "Anwaar <PERSON> Kandari", "Affiliation": "Kuwait Technical College, Kuwait"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Technology, Bahrain"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Pant University of Agriculture and Technology, Pantnagar, India"}], "References": [{"Title": "Technology readiness levels: Shortcomings and improvement opportunities", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "4", "Page": "395", "JournalTitle": "Systems Engineering"}, {"Title": "Evaluating the cyber security readiness of organizations and its influence on performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "102726", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Machine Learning Cybersecurity Adoption in Small and Medium Enterprises in Developed Countries", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "11", "Page": "150", "JournalTitle": "Computers"}]}, {"ArticleId": 114257453, "Title": "Finding Information Diffusion’s Seed Nodes in Online Social Networks Using a Special Degree Centrality", "Abstract": "<p>Information dissemination in online social networks determines the broad contours of how users interact within a platform and is often seen in the form of percolation of text, audio, and video messages, in addition to likes, dislikes, and mentions. As this information dissemination forms the bedrock of all major events happening in a given social network like news diffusion, virality of products, ad campaigns, friendship patterns, educational enlightenment, researchers have become active in finding the answer to various questions related to information diffusion. One of the important aspects of information diffusion is detection of seed nodes; nodes that flood a given social network and are the actual sources/creators of information emanating from a social network. These seed nodes are detected using a variety of centrality measures such as degree centrality, betweenness centrality, closeness centrality, eigenvector centrality, PageRank centrality; but all of them suffer from explicit specification of seed node limit k or implicit threshold calculation for seed limit at high complexities and thus have no self-classifying power to detect seed and non-seeds. In this paper, we identify these seed nodes using a newly proposed degree centrality ‘netdegree’ by utilizing the breadth first search propagation model. This novel approach uses the principle of outdegree of a given node in a network to find the netdegree and if it has a positive value, the node is treated as a seed node; otherwise, it is a non-seed node. We tested our netdegree centrality on five large online social network datasets in addition to three small benchmark datasets, and the findings for detection of seed nodes were promising. We compared our results with the existing centrality measures, and the comparison makes it clear that our approach is self-classifying and does not require any implicit or explicit seed limit parameter. Further, it is quite clear that that the proposed study is scalable to majority of online social networks in addition to being computationally time efficient when compared with other state of the art centrality-measure-based algorithms.</p>", "Keywords": "Information diffusion; Online social networks; Degree centrality; Social graphs", "DOI": "10.1007/s42979-024-02683-x", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Kashmir, Srinagar, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Kashmir, Srinagar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Kashmir, Srinagar, India"}], "References": [{"Title": "Product information diffusion in a social network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "1", "Page": "3", "JournalTitle": "Electronic Commerce Research"}, {"Title": "State-of-art review of information diffusion models and their impact on social network vulnerabilities", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "1275", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Modeling Information Diffusion In Online Social Networks Using SEI Epidemic Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "672", "JournalTitle": "Procedia Computer Science"}, {"Title": "Influential nodes detection in dynamic social networks: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "113642", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Influence propagation in social networks: Interest-based community ranking model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "2231", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "A deep learning approach for semi-supervised community detection in Online Social Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "229", "Issue": "", "Page": "107345", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A Survey on the Role of Centrality as Seed Nodes for Information Propagation in Large Scale Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "1", "JournalTitle": "ACM/IMS Transactions on Data Science"}, {"Title": "Impact of second-order network motif on online social networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "4", "Page": "5450", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Influence maximization in social networks using graph embedding and graph neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "1617", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 114257525, "Title": "A Hybrid Expert System for Estimation of the Manufacturability of a Notional Design", "Abstract": "<p>The more “manufacturable” a product is, the “easier” it is to manufacture. For two different product designs targeting the same role, one may be more manufacturable than the other. Evaluating manufacturability requires experts in the processes of manufacturing, “manufacturing process engineers” (MPEs). Human experts are expensive to train and employ, while a well-designed expert system (ES) could be quicker, more reliable, and provide higher performance and superior accuracy. In this work, a group of MPEs (“Team A”) externalized a portion of their expertise into a rule-based expert system in cooperation with a group of ES knowledge engineers and developers. We produced a large ES with 113 total rules and 94 variables. The ES comprises a crisp ES which constructs a Fuzzy ES, thus producing a two-stage ES. Team A then used the ES and a derivation of it (the “MAKE A”) to conduct assessments of the manufacturability of several “notional” designs, providing a sanity check of the rule-base. A provisional assessment used a first draft of the rule-base, and MAKE A, and was of notional wing designs. The primary assessment, using an updated rule-base and MAKE A, was of notional rotor blade designs. We describe the process by which this ES was made and the assessments that were conducted and conclude with insights gained from constructing the ES. These insights can be summarized as follows: build a bridge between expert and user, move from general features to specific features, do not make the user do a lot of work, and only ask the user for objective observations. We add the product of our work to the growing library of tools and methodologies at the disposal of the U.S. Army Engineer Research and Development Center (ERDC). The primary findings of the present work are (1) an ES that satisfied the experts, according to their expressed performance expectations, and (2) the insights gained on how such a system might best be constructed.</p>", "Keywords": "", "DOI": "10.1155/2024/4985090", "PubYear": 2024, "Volume": "2024", "Issue": "1", "JournalId": 10191, "JournalTitle": "Applied Computational Intelligence and Soft Computing", "ISSN": "1687-9724", "EISSN": "1687-9732", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Mississippi State University, Starkville, MS, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Mississippi State University, Starkville, MS, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Advanced Vehicular Systems Extension, Mississippi State University, Starkville, MS, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Center for Advanced Vehicular Systems Extension, Mississippi State University, Starkville, MS, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Research and Development Center, Vicksburg, MS, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute for Systems Engineering Research, Mississippi State University, Vicksburg, MS, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Center for Advanced Vehicular Systems Extension, Mississippi State University, Starkville, MS, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Center for Advanced Vehicular Systems Extension, Mississippi State University, Starkville, MS, USA"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Vehicular Systems, Mississippi State University, Starkville, MS, USA"}, {"AuthorId": 10, "Name": "Milan D. Parmar", "Affiliation": "Department of Computer Science and Engineering, Mississippi State University, Starkville, MS, USA"}], "References": [{"Title": "Knowledge-based expert system to support the semantic interoperability in smart manufacturing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "115", "Issue": "", "Page": "103161", "JournalTitle": "Computers in Industry"}, {"Title": "Simpful: A User-Friendly Python Library for Fuzzy Logic", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "1687", "JournalTitle": "International Journal of Computational Intelligence Systems"}]}, {"ArticleId": 114257536, "Title": "Data Dissemination Framework for Optimizing Overhead in IoT-Enabled Systems Using Tabu-RPL", "Abstract": "<p>The IoT devices vary in terms of processing power, storage capacity, memory, and energy capabilities. These device heterogeneities pose unique challenges for efficient data dissemination. The authors propose a data dissemination framework designed to address IoT system challenges. The framework focuses on reducing network congestion and traffic, minimising data transmission, improving overall system performance, and alleviating the burden on IoT devices with limited resources. The RPL protocol dealt with the constraints of low-power, lossy networks and objective function influences the behaviour of routing protocols. The RPL objective functions are not based on different criteria and affect the network quality of the services. Furthermore, the framework incorporates Tabu Search Routing (TSR), an adaptive routing algorithm that dynamically adjusts data dissemination paths based on network conditions and device capabilities. The algorithm adapts to network topology and traffic load changes to optimise the routing of data packets. The TSR algorithm aims to find efficient paths for data dissemination by exploring search space and evaluating various routing options. Tabu-RPL objective function is simulated cooja-3.0 simulator for a large-scale IoT network to achieve a 30% reduction of network overhead, 20% less energy consumption, 25% more packet delivery ratio, and 20% reduction of end-to-end delay compared to EPC-RPL, Enhancement of RPL and EEOPS-RPL and finally achieve the quality of services of the network.</p>", "Keywords": "Internet of Things; Objective function; Tabu search routing algorithm; Data aggregation; Data dissemination", "DOI": "10.1007/s42979-024-02694-8", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Nationtal Institute of Technology, Hamirpur, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Nationtal Institute of Technology, Hamirpur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Nationtal Institute of Technology, Hamirpur, India"}], "References": [{"Title": "Efficient parent selection for RPL using ACO and coverage based dynamic trickle techniques", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "4377", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 114257573, "Title": "A novel influence quantification model on Instagram using data science approach for targeted business advertising and better digital marketing outcomes", "Abstract": "<p>Instagram is one of the most popular and widely used social network platforms. It is used as a digital tool to connect with other users and also to share information and influence them for marketing and advertising purposes. The influence of popular users is broadly determined by post’s engagement rate in terms of likes, comments, and shares, and the number of followers as well. An objective and comprehensive measure of popularity is necessary to understand the factors that will help make an influencer marketing campaign more successful and beneficial for business activities. This research work attempts to take various features of an influencer account and Instagram posts dataset and develop a novel model that accurately quantifies and determines the influence of a user on Instagram. The research is based on datasets of top regional Instagram influencers and their posts based on categories signified through hashtags and captions. Our research attempts to develop a model using principal component analysis to quantify influence and using it to rank influencers. In our experiment, the proposed model after experimentation, gave the Instagram username “iqbaal.e” influence score as 874,712.9526, username “1nctdream” as 753,830.5847 and username “weareone. exo” as 668,054.4360. The proposed model ranks were compared with other ranks for Instagram users based on other measures such as follower rank etc. User names “huyitian”, “bintangemon” and “bimopd” are top social media influencers based on the proposed model for better business advertising and digital marketing outcomes with collected data and experiment context. This proposed approach gives an exploration for the stakeholders to quantify the impact of influencer in social media and demonstrate an innovative approach.</p>", "Keywords": "Influencer score; Social media; Quantification; Business advertising; Digital marketing; Data science; PCA", "DOI": "10.1007/s13278-024-01230-z", "PubYear": 2024, "Volume": "14", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Cluster Innovation Centre, University of Delhi, New Delhi, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cluster Innovation Centre, University of Delhi, New Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Cluster Innovation Centre, University of Delhi, New Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cluster Innovation Centre, University of Delhi, New Delhi, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Cluster Innovation Centre, University of Delhi, New Delhi, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Indraprastha Institute of Information Technology, Delhi, New Delhi, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engg and Natural Sciences, Western Norway University of Applied Sciences, Haugesund, Norway"}], "References": [{"Title": "An innovative neural network approach for stock market prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "2098", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "The effects of visual congruence on increasing consumers’ brand engagement: An empirical investigation of influencer marketing on instagram using deep-learning algorithms for automatic image classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "106443", "JournalTitle": "Computers in Human Behavior"}, {"Title": "A machine learning-based approach to enhancing social media marketing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106723", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Popularity Prediction of Instagram Posts", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "9", "Page": "453", "JournalTitle": "Information"}, {"Title": "Consumer engagement with social media platforms: A study of the influence of attitudinal components on cutting edge technology adaptation behaviour", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "121", "Issue": "", "Page": "106802", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Computer vision detection of foreign objects in coal processing using attention CNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>v", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "104242", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 114257600, "Title": "Actionable code smell identification with fusion learning of metrics and semantics", "Abstract": "Code smell detection is one of the essential tasks in the field of software engineering. Identifying whether a code snippet has a code smell is subjective and varies by programming language, developer, and development method. Moreover, developers tend to focus on code smells that have a real impact on development and ignore insignificant ones. However, existing static code analysis tools and code smell detection approaches exhibit a high false positive rate in detecting code smells, which makes insignificant smells drown out those smells that developers value. Therefore, accurately reporting those actionable code smells that developers tend to spend energy on refactoring can prevent developers from getting lost in the sea of smells and improve refactoring efficiency. In this paper, we aim to detect actionable code smells that developers tend to refactor. Specifically, we first collect actionable and non-actionable code smells from projects with numerous historical versions to construct our datasets. Then, we propose a dual-stream model for fusion learning of code metrics and code semantics to detect actionable code smells. On the one hand, code metrics quantify the code&#x27;s structure and even some rules or patterns, providing fundamental information for detecting code smells. On the other hand, code semantics encompass information about developers&#x27; refactoring tendencies, which prove valuable in detecting actionable code smells. Extensive experiments show that our approach can detect actionable code smells more accurately compared to existing approaches.", "Keywords": "", "DOI": "10.1016/j.scico.2024.103110", "PubYear": 2024, "Volume": "236", "Issue": "", "JournalId": 12043, "JournalTitle": "Science of Computer Programming", "ISSN": "0167-6423", "EISSN": "1872-7964", "Authors": [{"AuthorId": 1, "Name": "Dongjin Yu", "Affiliation": "College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, Zhejiang, 310018, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, Zhejiang, 310018, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, Zhejiang, 310018, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, Zhejiang, 310018, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, Zhejiang, 310018, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, Zhejiang, 310018, China"}], "References": [{"Title": "Code smell detection and identification in imbalanced environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "114076", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Technical debt resulting from architectural degradation and code smells: a systematic mapping study", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "4", "Page": "20", "JournalTitle": "ACM SIGAPP Applied Computing Review"}, {"Title": "Uncertainty-wise software anti-patterns detection: A possibilistic evolutionary machine learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "109620", "JournalTitle": "Applied Soft Computing"}, {"Title": "Graph-based code semantics learning for efficient semantic code clone detection", "Authors": "<PERSON><PERSON> Yu; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "156", "Issue": "", "Page": "107130", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 114257619, "Title": "Energy-efficient DAG scheduling with DVFS for cloud data centers", "Abstract": "<p>With the growth of the cloud computing market, the number and scale of cloud data centers are expanding rapidly. While cloud data centers provide a large amount of computing power, generating tremendous energy consumption has become a fundamental issue in the financial and environmental fields. Improving quality of service and reducing energy costs are fundamental challenges for next-generation cloud data centers. Task scheduling in cloud data centers grows increasingly complex due to the heterogeneity of computing resources, intricate dependencies of jobs and rising expenses resulting from high energy consumption. Efficiently utilizing computing resources is crucial, so it is necessary to develop optimal strategies for job scheduling. This paper proposes a reinforcement learning-based task scheduler (E2DSched) for online scheduling of randomly arriving directed acyclic graph jobs in cloud data centers. E2DSched divides the scheduling process into three layers: task selection layer, server selection layer and frequency control layer. It achieves joint optimization of energy consumption and quality of service through three-layer cooperation. Finally, we compare E2DSched with various other algorithms, and the results show that E2DSched can provide excellent service with less energy consumption.</p>", "Keywords": "Task schedule; Cloud data center; DAG job; Reinforcement learning", "DOI": "10.1007/s11227-024-06035-7", "PubYear": 2024, "Volume": "80", "Issue": "10", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 3, "Name": "Jingbo Li", "Affiliation": "School of Computer Science and Technology, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xi’an Jiaotong University, Xi’an, China; Corresponding author."}], "References": [{"Title": "A new energy-aware tasks scheduling approach in fog computing using hybrid meta-heuristic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "88", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Balancing Power And Performance In HPC Clouds", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "6", "Page": "880", "JournalTitle": "The Computer Journal"}, {"Title": "A hybrid list-based task scheduling scheme for heterogeneous computing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "9", "Page": "10252", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Task Scheduling in Cloud Using Deep Reinforcement Learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "42", "JournalTitle": "Procedia Computer Science"}, {"Title": "Using a task dependency job-scheduling method to make energy savings in a cloud computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "3", "Page": "4550", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Energy-aware task scheduling optimization with deep reinforcement learning for large-scale heterogeneous systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "4", "Page": "383", "JournalTitle": "CCF Transactions on High Performance Computing"}, {"Title": "Cluster resource scheduling in cloud computing: literature review and research challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "5", "Page": "6898", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Online energy-efficient scheduling of DAG tasks on heterogeneous embedded platforms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "102894", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Efficient, economical and energy-saving multi-workflow scheduling in hybrid cloud", "Authors": "Zaixing Sun; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "228", "Issue": "", "Page": "120401", "JournalTitle": "Expert Systems with Applications"}, {"Title": "TMDS: Temperature-aware Makespan Minimizing DAG Scheduler for Heterogeneous Distributed Systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "28", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems"}, {"Title": "DAG-based workflows scheduling using Actor–Critic Deep Reinforcement Learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "150", "Issue": "", "Page": "354", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": *********, "Title": "Sweeter than honey: Are Gmail accounts associated with greater rewards at a higher risk of hijacking?", "Abstract": "<b  >Objectives</b> This study investigates the effect of advertised rewards in credential leaks on the likelihood and speed of account hijacking. <b  >Methods</b> In an online field experiment, we created 176 honey Gmail accounts and randomly assigned them to eight different posts containing account credential leaks. We used a 2 × 2 experimental design, manipulating two key variables within the post titles: the number of accounts (5 K or 1.5 M) and the promise of access to additional platforms (absent or present). We then monitored the accounts for any subsequent activity. <b  >Results</b> Our findings indicate that the promise of access to additional platforms increased the likelihood and speed of an attempted access. Only 12 accounts were fully accessed, however, because most hijackers did not complete the second-factor authentication (2FA) process required for gaining full access. It seems that the 2FA acted as a deterrent to complete Gmail account hijacking. <b  >Conclusions</b> The study aligns with the rational choice perspective of crime, showing that the prospect of greater rewards leads to more attempted account accesses. <b  >Pre-registration</b> https://osf.io/9y26z .", "Keywords": "Account hijacking ; Cybercriminal decision-making ; Hacker forums ; Honey accounts ; Personal data theft ; Rational choice perspective ; Target selection", "DOI": "10.1016/j.chbr.2024.100410", "PubYear": 2024, "Volume": "14", "Issue": "", "JournalId": 75723, "JournalTitle": "Computers in Human Behavior Reports", "ISSN": "2451-9588", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Netherlands Institute for the Study of Crime and Law Enforcement (NSCR), Netherlands;Universiteit Utrecht, Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Netherlands Institute for the Study of Crime and Law Enforcement (NSCR), Netherlands;Universiteit Utrecht, Netherlands;Corresponding author. Postbus 71304, 1008 BH, Amsterdam, Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Netherlands Institute for the Study of Crime and Law Enforcement (NSCR), Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Netherlands Institute for the Study of Crime and Law Enforcement (NSCR), Netherlands;Center of Expertise Cyber Security, The Hague University of Applied Sciences, Netherlands"}], "References": [{"Title": "Situational Crime Prevention (SCP) techniques to prevent and control cybercrimes: A focused systematic review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "102611", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 114257748, "Title": "An EEG Dataset of Subject Pairs during Collaboration and Competition Tasks in Face-to-Face and Online Modalities", "Abstract": "<p>This dataset was acquired during collaboration and competition tasks performed by sixteen subject pairs (N = 32) of one female and one male under different (face-to-face and online) modalities. The collaborative task corresponds to cooperating to put together a 100-piece puzzle, while the competition task refers to playing against each other in a one-on-one classic 28-piece dominoes game. In the face-to-face modality, all interactions between the pair occurred in person. On the other hand, in the online modality, participants were physically separated, and interaction was only allowed through Zoom software with an active microphone and camera. Electroencephalography data of the two subjects were acquired simultaneously while performing the tasks. This article describes the experimental setup, the process of the data streams acquired during the tasks, and the assessment of data quality.</p>", "Keywords": "", "DOI": "10.3390/data9040047", "PubYear": 2024, "Volume": "9", "Issue": "4", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mechatronics Department, School of Engineering and Sciences, Tecnologico de Monterrey, Monterrey 64700, NL, Mexico"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>Car<PERSON>", "Affiliation": "Mechatronics Department, School of Engineering and Sciences, Tecnologico de Monterrey, Monterrey 64700, NL, Mexico"}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON><PERSON><PERSON>", "Affiliation": "Mechatronics Department, School of Engineering and Sciences, Tecnologico de Monterrey, Monterrey 64700, NL, Mexico"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechatronics Department, School of Engineering and Sciences, Tecnologico de Monterrey, Monterrey 64700, NL, Mexico"}, {"AuthorId": 5, "Name": "Diego A. Garza-Vélez", "Affiliation": "Mechatronics Department, School of Engineering and Sciences, Tecnologico de Monterrey, Monterrey 64700, NL, Mexico"}, {"AuthorId": 6, "Name": "Aranza Carrillo-Márquez", "Affiliation": "Mechatronics Department, School of Engineering and Sciences, Tecnologico de Monterrey, Monterrey 64700, NL, Mexico"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechatronics Department, School of Engineering and Sciences, Tecnologico de Monterrey, Monterrey 64700, NL, Mexico"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Mechatronics Department, School of Engineering and Sciences, Tecnologico de Monterrey, Monterrey 64700, NL, Mexico"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": "Mechatronics Department, School of Engineering and Sciences, Tecnologico de Monterrey, Monterrey 64700, NL, Mexico"}], "References": []}, {"ArticleId": 114257771, "Title": "Improving model performance of shortest‐path‐based centrality measures in network models through scale space", "Abstract": "The quality of the solution in resolving a complex network depends on either the speed or accuracy of the results. While some health studies prioritize high performance, fast algorithms are favored in scenarios requiring rapid decision‐making. A comprehensive understanding of the problem necessitates a detailed analysis of the network and its individual components. Betweenness Centrality (BC) and Closeness Centrality (CC) are commonly employed measures in network studies. This study introduces a new strategy to compute BC and CC that assesses their sensitivity in the scale space while measuring the shortest path. The scale space is generated by incorporating a scale parameter that is shown to achieve up to 60% performance improvements for various datasets. The study provides in‐depth insights into the importance of the scale space analysis. Finally, a flexible measurement tool is provided that is suitable for various types of problems. To demonstrate the flexibility and applicability, we experimented with two methods for 10 different graphs using the proposed approach.", "Keywords": "betweenness centrality;closeness centrality;feature engineering;scale space;sensitivity;shortest-path;transportation network", "DOI": "10.1002/cpe.8082", "PubYear": 2024, "Volume": "36", "Issue": "14", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Industrial Engineering Istanbul Technical University  Istanbul Turkey"}, {"AuthorId": 2, "Name": "Alper <PERSON>", "Affiliation": "Department of Civil, Environmental and Geodetic Engineering The Ohio State University  Columbus Ohio USA"}], "References": [{"Title": "Temporal betweenness centrality in dynamic graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "3", "Page": "257", "JournalTitle": "International Journal of Data Science and Analytics"}, {"Title": "MR-IBC: MapReduce-based incremental betweenness centrality in large-scale complex networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Supervised link prediction using structured‐based feature extraction in social network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "e5839", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Path-based estimation for link prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "9", "Page": "2443", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Fast cluster-based computation of exact betweenness centrality in large graphs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "Dissection of hubs and bottlenecks in a protein-protein interaction network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "102", "Issue": "", "Page": "107802", "JournalTitle": "Computational Biology and Chemistry"}, {"Title": "Analyzing and Comparing Omicron Lineage Variants Protein–Protein Interaction Network Using Centrality Measure", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; P<PERSON> <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "3", "Page": "299", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 114257781, "Title": "Three-dimensional reconstruction of industrial parts from a single image", "Abstract": "<p>This study proposes an image-based three-dimensional (3D) vector reconstruction of industrial parts that can generate non-uniform rational B-splines (NURBS) surfaces with high fidelity and flexibility. The contributions of this study include three parts: first, a dataset of two-dimensional images is constructed for typical industrial parts, including hexagonal head bolts, cylindrical gears, shoulder rings, hexagonal nuts, and cylindrical roller bearings; second, a deep learning algorithm is developed for parameter extraction of 3D industrial parts, which can determine the final 3D parameters and pose information of the reconstructed model using two new nets, CAD-ClassNet and CAD-ReconNet; and finally, a 3D vector shape reconstruction of mechanical parts is presented to generate NURBS from the obtained shape parameters. The final reconstructed models show that the proposed approach is highly accurate, efficient, and practical.</p><p>© 2024. The Author(s).</p>", "Keywords": "Deep learning;Industrial parts;Non-uniform rational B-splines;Three-dimensional reconstruction", "DOI": "10.1186/s42492-024-00158-7", "PubYear": 2024, "Volume": "7", "Issue": "1", "JournalId": 4922, "JournalTitle": "Visual Computing for Industry, Biomedicine, and Art", "ISSN": "", "EISSN": "2524-4442", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Beihang University, Beijing, 100191, China. ;Key Laboratory of Aeronautics Smart Manufacturing, Beihang University, Beijing, 100191, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Beihang University, Beijing, 100191, China.  . ;Key Laboratory of Aeronautics Smart Manufacturing, Beihang University, Beijing, 100191, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, 100190, China."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Beihang University, Beijing, 100191, China. ;Key Laboratory of Aeronautics Smart Manufacturing, Beihang University, Beijing, 100191, China."}], "References": [{"Title": "DV-Net: Dual-view network for 3D reconstruction by fusing multiple sets of gated control point clouds", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "376", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "3D-RVP: A method for 3D object reconstruction from a single depth view using voxel and point", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "430", "Issue": "", "Page": "94", "JournalTitle": "Neurocomputing"}, {"Title": "Learning pose-invariant 3D object reconstruction from single-view images", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "423", "Issue": "", "Page": "407", "JournalTitle": "Neurocomputing"}, {"Title": "Single-View 3D reconstruction: A Survey of deep learning methods", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "94", "Issue": "", "Page": "164", "JournalTitle": "Computers & Graphics"}, {"Title": "Survey on 3D face reconstruction from uncalibrated images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100400", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": 114257846, "Title": "AI-Powered Genetic Algorithm for Optimal Scheduling in Medical Research and Educational Management", "Abstract": "The course scheduling problem is a typical combinatorial optimization problem. It involves many factors, such as the school management department, teachers, students, and classrooms. It is a mixed integer nonlinear programming problem with multiple constraints. The traditional course scheduling algorithm has many defects. For example, the conventional genetic algorithm is only suitable for solving continuous function course scheduling problems and cannot be used for discrete and constant course scheduling problems. The improved genetic algorithm has unique advantages in solving discrete and continuous combinatorial optimization problems. This paper combines the traditional and improved genetic algorithms to solve the course scheduling problem. It applies the combination of the two to optimize intelligent course scheduling. The experimental results show that the improved genetic algorithm can improve course scheduling to a certain extent, including efficiency and scheduling quality. © 2024 U-turn Press LLC.", "Keywords": "AI-powered Genetics; educational management; Improved genetic algorithms; intelligent course Arrangement", "DOI": "10.14733/cadaps.2024.S24.17-34", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Education Science, Baise University, Guangxi, Baise, 533000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Life Science, Youjiang Medical University For Nationalities, Guangxi, Baise, 533000, China"}, {"AuthorId": 3, "Name": "Jingjing Guan", "Affiliation": "School of Education Science, Hanshan Normal University, Guangdong, Chaozhou, 521041, China"}], "References": []}, {"ArticleId": 114257911, "Title": "Performance Evaluation of Biometric Authentication Using Fragment Jaya Optimizer-Based Deep CNN with Multi-kernel SVM", "Abstract": "<p>The earlier research clearly indicated that the bimodal authentication system has more efficiency than unimodal and multimodal. This is due to the reason for the best intact biometric traits of fingerprint and retina. There is a chance to additionally improve further performance of the proposed biometric trait combination by additional or alternate algorithms or methodologies. Therefore, in this research work, the multi-kernel support vector machine (MK-SVM), a machine learning classification approach, is proposed and is used for the implementation. In addition, a hybrid algorithm of fragment Jaya optimizer-based deep convolutional neural network (FJO-DCNN) approach is also used to improve the performance value for bimodal biometric authentication and classification. The recognition systems analyze both biometrics independently, and their conclusions are combined to determine whether to give or refuse access to the user in the end. According to the findings of the implementation, this work demonstrates more dependability than the cascaded biometric system.</p>", "Keywords": "Multi-kernel support vector machine; Fragment Jaya optimizer convolutional neural network; Deep convolutional neural network; Bimodal biometric authentication", "DOI": "10.1007/s42979-024-02666-y", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sathyabama Institute of Science and Technology, Chennai, India; School of Computing, Department of CSE, Sathyabama Institute of Science and Technology, Chennai, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Rajalakshmi Engineering College, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SRM Institute of Science and Technology, Ramapuram, Chennai, India"}], "References": []}, {"ArticleId": 114258001, "Title": "Augmenting Sentiment Analysis Prediction in Binary Text Classification through Advanced Natural Language Processing Models and Classifiers", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijitcs.2024.02.02", "PubYear": 2024, "Volume": "16", "Issue": "2", "JournalId": 8584, "JournalTitle": "International Journal of Information Technology and Computer Science", "ISSN": "2074-9007", "EISSN": "2074-9015", "Authors": [{"AuthorId": 1, "Name": "Zhengbing Hu", "Affiliation": "School of Computer Science, Hubei University of Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114258012, "Title": "Area in circle: A novel evaluation metric for object detection", "Abstract": "As computer vision technologies undergo extensive advancement in recent years, the significance of object detection has been valued to an unprecedented level in a variety of industries, such as manufacturing, healthcare, agriculture, and smart transportation. Even though Average Precision (AP) is widely employed as the evaluation metric for object detection, it typically suffers from some issues, e.g., the difficulties in explanation for non-professionals, the lack of clear guidance in determining thresholds, and the incomplete consideration in localization and classification accuracy. In this paper, we propose a novel evaluation metric for object detection, called Area in Circle (AIC). To be specific, we introduce the novel concepts of Weighted True Positive (WTP) and Weighted False Positive (WFP), which project the original Precision and Recall results onto a circular coordinate. After that, we calculate the corresponding area in the circular coordinate in terms of perfect and weak detectors, without determining any Intersection over Union (IoU) threshold. We conducted the experiments on the COCO 2017 dataset with 22 state-of-the-art object detection models. The results showed that our proposed metric can clearly and accurately interpret a model’s overall detection capacity. Furthermore, it can provide extra knowledge about the detection quality, and can be served as an effective alternative for the existing metrics.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.111684", "PubYear": 2024, "Volume": "293", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Sobey School of Business, Saint Mary’s University, Canada;Faculty of Computer Science, University of New Brunswick, Canadian Institute for Cybersecurity, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "Roozbeh Razavi-Far", "Affiliation": "Faculty of Computer Science, University of New Brunswick, Canadian Institute for Cybersecurity, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science, University of New Brunswick, Canadian Institute for Cybersecurity, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science, University of New Brunswick, Canadian Institute for Cybersecurity, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science, University of New Brunswick, Canadian Institute for Cybersecurity, Canada"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Lu", "Affiliation": "Faculty of Computer Science, University of New Brunswick, Canadian Institute for Cybersecurity, Canada"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science, University of New Brunswick, Canadian Institute for Cybersecurity, Canada"}], "References": [{"Title": "Recent advances in deep learning for object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "39", "JournalTitle": "Neurocomputing"}, {"Title": "Deep learning-based method for SEM image segmentation in mineral characterization, an example from Duvernay Shale samples in Western Canada Sedimentary Basin", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "104450", "JournalTitle": "Computers & Geosciences"}, {"Title": "Deep learning methods for object detection in smart manufacturing: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "", "Page": "181", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 114258016, "Title": "Constructing a parametric 3D model of an extruder screw working area", "Abstract": "", "Keywords": "", "DOI": "10.15827/0236-235X.144.678-685", "PubYear": 2023, "Volume": "19", "Issue": "", "JournalId": 19680, "JournalTitle": "International journal \"Programmnye produkty i sistemy\"", "ISSN": "0236-235X", "EISSN": "2311-2735", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>,", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>,", "Affiliation": ""}], "References": []}, {"ArticleId": 114258047, "Title": "Information security of a supercomputer center", "Abstract": "", "Keywords": "", "DOI": "10.15827/0236-235X.144.615-631", "PubYear": 2023, "Volume": "19", "Issue": "", "JournalId": 19680, "JournalTitle": "International journal \"Programmnye produkty i sistemy\"", "ISSN": "0236-235X", "EISSN": "2311-2735", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>,", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>,", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>,", "Affiliation": ""}], "References": []}, {"ArticleId": 114258220, "Title": "Retraction Note to: A Hierarchical Algorithm Model for the Scheduling Problem of Cold Chain Logistics Distribution Vehicles Based on Machine Vision", "Abstract": "", "Keywords": "", "DOI": "10.1007/s44196-024-00474-z", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Modern Logistics College, Guangzhou Panyu Polytechnic, Guangzhou, China; Corresponding author."}], "References": []}, {"ArticleId": 114258255, "Title": "Global scale solar energy harnessing: An advanced intra-hourly diffuse solar irradiance predicting framework for solar energy projects", "Abstract": "<p>Diffuse horizontal irradiance (DHI) forecasts are critical for adopting solar photovoltaic technology. Yet, they can lack reliability given the limited and uncertain meteorological data available for desert areas. This research develops a new sine cosine dipper throated optimization (SCDTO) technique, targeting DHI prediction even given such data constraints. SCDTO uniquely hybridizes sine cosine metaheuristics, adept at exploration, with dipper throat optimization, providing focused exploitation. This hybridization aims to create a robust ensemble model capable of delivering reliable DHI predictions despite climatic uncertainty. The ensemble model, employing various input combinations, was rigorously evaluated across multiple meteorological stations. The SCDTO algorithm exhibited remarkable performance improvements, yielding substantial reductions exceeding 93% in root mean squared error and 98% in mean absolute error at key stations in Morocco, including Tan-Tan, Zagora, Erfoud, and Oujda. Comparative analyses against established optimization algorithms consistently underscored the superior predictive capabilities of SCDTO. Visualizations, including box plots and histograms, demonstrated SCDTO’s efficacy in minimizing prediction errors, particularly for Tan-Tan, Zagora, and Erfoud stations. Also, statistical validation through one-way analysis of variance (ANOVA) further affirmed the significance of the proposed SCDTO method. Therefore, this hybrid metaheuristic optimization enables more accurate DHI predictions from limited meteorological data.</p>", "Keywords": "Solar energy; Prediction; Hourly diffuse irradiance; Hybrid ensemble learning algorithm; SCDTO algorithm; Optimization", "DOI": "10.1007/s00521-024-09608-y", "PubYear": 2024, "Volume": "36", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Communications and Electronics, Delta Higher Institute of Engineering and Technology, Mansoura, Egypt; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Mathematics Modeling and Applications, Department of Mathematics and Computer Science, Faculty of Sciences and Technology, Ahmed <PERSON> University of Adrar, Adrar, Algeria; Energies and Materials Research Laboratory, Faculty of Sciences and Technology, University of Tamanghasset, Tamanrasset, Algeria; MEU Research Unit, Middle East University, Amman, Jordan; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Unité de Recherche en Energies Renouvelables en Milieu Saharien (URERMS), Centre de Développement Des Energies Renouvelables (CDER), Adrar, Algeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vegetal Chemistry-Water-Energy Laboratory, Faculty of Civil Engineering and Architecture, Department of Hydraulic, Hassiba <PERSON>i, University of Chlef, Chlef, Algeria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Power Engineering Department, Faculty of Engineering, Cairo University, Giza, Egypt; School of Architecture, University of Waterloo, Cambridge, Canada"}, {"AuthorId": 6, "Name": "Alban <PERSON>", "Affiliation": "Civil Engineering Department, University for Business and Technology, Pristina, Kosovo; CERIS, Instituto Superior Tecnico, Universidade de Lisboa, Lisbon, Portugal"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "High Temperature Processes Unit, IMDEA Energy Institute, Móstoles, Madrid, Spain"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering and Architectures, Nisantasi University, Istanbul, Turkey"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Power Engineering Department, Faculty of Engineering, Cairo University, Giza, Egypt"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering and Control Systems Department, Faculty of Engineering, Mansoura University, Mansoura, Egypt"}], "References": [{"Title": "Optimized Weighted Ensemble Using Dipper Throated Optimization Algorithm in Metamaterial Antenna", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "3", "Page": "5771", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 114258270, "Title": "Progress, challenges and trends on vision sensing technologies in automatic/intelligent robotic welding: State-of-the-art review", "Abstract": "Welding is a method of realizing material connections, and the development of modern sensing technology is pushing this traditional process towards automation and intelligence. Among many sensing methods, visual sensing stands out with its advantages of non-contact, fast response and economic benefits, etc. This paper provides a comprehensive review of visualization methods in the context of specific welding processes in the following five aspects. The problem of IWP location is summarized from two directions of active and passive vision. Weld seam identification and tracking methods are discussed in detail based on the morphological characteristics of the weld seam. The feasibility of different weld path planning methods is analyzed based on the point cloud information and the composite vision information. Two types of monitoring means based on infrared sensing and visible light sensing are summarized taking into account the thermal and morphological characteristics of the weld pool, and welding defect detection technology is summarized by comparing the intelligent detection algorithms and the traditional detection algorithms. Finally, by combining the existing developments in computer technology, composite sensing technology, machine learning technology, and multi-robot control technology, the article concludes with a summary and trends in the development of automated welding technologies.", "Keywords": "", "DOI": "10.1016/j.rcim.2024.102767", "PubYear": 2024, "Volume": "89", "Issue": "", "JournalId": 5910, "JournalTitle": "Robotics and Computer-Integrated Manufacturing", "ISSN": "0736-5845", "EISSN": "1879-2537", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, 454150, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, 454150, China"}, {"AuthorId": 3, "Name": "Jinting Xu", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian 116024, China;Corresponding authors"}, {"AuthorId": 4, "Name": "Yan <PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, 454150, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, 454150, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, 454150, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, 454150, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian 116024, China"}], "References": [{"Title": "A robust weld seam recognition method under heavy noise based on structured-light vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101821", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A novel system for off-line 3D seam extraction and path planning based on point cloud segmentation for arc welding robot", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "101929", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A virtual-physical collision detection interface for AR-based interactive teaching of robot", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "101948", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "An analytical C3 continuous tool path corner smoothing algorithm for 6R robot manipulator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "101947", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Research on the virtual reality technology of a pipeline welding robot", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "1", "Page": "84", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "Intelligent welding system technologies: State-of-the-art review and perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "373", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Application of sensing techniques and artificial intelligence-based methods to laser welding real-time monitoring: A critical review of recent literature", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "1", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A review of vision-aided robotic welding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "103326", "JournalTitle": "Computers in Industry"}, {"Title": "Autonomous seam recognition and feature extraction for multi-pass welding based on laser stripe edge guidance network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "9-10", "Page": "2719", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Visual sensing technologies in robotic welding: Recent research developments and future interests", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "320", "Issue": "", "Page": "112551", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Precise pose and assembly detection of generic tubular joints based on partial scan data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "7", "Page": "5201", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Multi-Robot Multi-Station Cooperative Spot Welding Task Allocation Based on Stepwise Optimization: An Industrial Case Study", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102197", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A method of welding path planning of steel mesh based on point cloud for welding robot", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "9-10", "Page": "2943", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Hybrid offline programming method for robotic welding systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102238", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A high-dynamic-range visual sensing method for feature extraction of welding pool based on adaptive image fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "5-6", "Page": "1675", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Universal fillet weld joint recognition and positioning for robot welding using structured light", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "102279", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Autonomous mobile welding robot for discontinuous weld seam recognition and tracking", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "7-8", "Page": "5497", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Research on the deviation sensing of V-groove weld seam based on a novel two channel acoustic sensor", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "9-10", "Page": "5821", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A novel welding path planning method based on point cloud for robotic welding of impeller blades", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "11-12", "Page": "8025", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "The 3D narrow butt weld seam detection system based on the binocular consistency correction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "5", "Page": "2321", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A novel visual guidance framework for robotic welding based on binocular cooperation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "102393", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Online obstacle avoidance path planning and application for arc welding robot", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "102413", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A novel seam extraction and path planning method for robotic welding of medium-thickness plate structural parts based on 3D vision", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "", "Page": "102433", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Seam tracking system based on laser vision and CGAN for robotic multi-layer and multi-pass MAG welding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105377", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A prediction and compensation method of robot tracking error considering pose-dependent load decomposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "80", "Issue": "", "Page": "102476", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Real-time segmentation network for accurate weld detection in large weldments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105008", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A robot welding path planning and automatic programming method for open impeller", "Authors": "Wei<PERSON> Fang; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "5-6", "Page": "1639", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A novel offline programming approach of robot welding for multi-pipe intersection structures based on NSGA-Ⅱ and measured 3D point-clouds", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "83", "Issue": "", "Page": "102549", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Online visual monitoring method for liquid rocket engine nozzle welding based on a multi-task deep learning model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "68", "Issue": "", "Page": "1", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "One-shot, integrated positioning for welding initial points via co-mapping of cross and parallel stripes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "84", "Issue": "", "Page": "102602", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Distributed spot welding task allocation and sequential planning for multi-station multi-robot coordinate assembly processes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "127", "Issue": "11-12", "Page": "5233", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 114258274, "Title": "A Flexible Non-Monotonic Discretization Method for Pre-processing in Supervised Learning", "Abstract": "Discretization is one of the important pre-processing steps for supervised learning. Discretizing attributes helps to simplify the data and make it easier to understand and analyze by reducing the number of values. It can provide a better representation of knowledge and thus help improve the accuracy of a classifier. However, to minimize the information loss, it is important to consider the characteristics of the data. Most approaches assume that the values of a continuous attribute are monotone with respect to the probability of belonging to a particular class. In other words, it is assumed that increasing or decreasing the value of the attribute leads to a proportional increase or decrease in the classification score. This assumption may not always be valid for all attributes of data. In this study, we present entropy-based, flexible discretization strategies capable of capturing the non-monotonicity of the attribute values. The algorithm can adjust the number of cut points and values depending on the characteristics of the data. It does not require setting of any hyper-parameter or threshold. Extensive experiments on different datasets have shown that the proposed discretizers significantly improve the performance of classifiers, especially on complex and high-dimensional data sets.", "Keywords": "", "DOI": "10.1016/j.patrec.2024.03.024", "PubYear": 2024, "Volume": "181", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "Hatice Şenozan", "Affiliation": "Graduate School of Natural and Applied Sciences, Erciyes University, Kayseri 38039, Türkiye"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Erciyes University, Department of Industrial Engineering, Kayseri 38039, Türki<PERSON>;Corresponding author"}], "References": [{"Title": "Non-parametric discretization for probabilistic labeled data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "161", "Issue": "", "Page": "52", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Feature selection using Information Gain and decision information in neighborhood decision system", "Authors": "<PERSON><PERSON> Qu; <PERSON><PERSON><PERSON>; Qincheng Hou", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "110100", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 114258307, "Title": "Vulnerability Detection in Intelligent Environments Authenticated by the OAuth 2.0 Protocol over HTTP/HTTPS", "Abstract": "OAuth 2.0 provides an open secure protocol for authorizing users across the web. However, many modalities of this standard allow these protections to be implemented optionally. Thus, its use does not guarantee security by itself and some of the deployment options in the OAuth 2.0 specification can lead to incorrect settings. FIWARE is an open platform for developing Internet applications of the future. It is the result of the international entity Future Internet Public-Private Partnership. [1,2] FIWARE was designed to provide a broad set of API to stimulate the development of new businesses in the context of the European Union. This platform can be understood as a modular structure to reach a broad spectrum of applications such as IoT, big data, smart device management, security, open data, and virtualization, among others. Regarding security, the exchange of messages between its components is done through the OAuth 2.0 protocol. The objective of the present work is to create a system that allows the detection and analysis of vulnerabilities of OAuth 2.0, executed on HTTP/HTTPS in an on-premise development environment focused on the management of IoT devices and to help developers to implement them ensuring security for these environments. Through the system proposed by this paper, it was possible to find vulnerabilities in FIWARE components in HTTP/HTTPS environments. With this evidence, mitigations were proposed based on the mandatory recommendations by the IETF. © 2024, Modern Education and Computer Science Press. All rights reserved.", "Keywords": "FIWARE; HTTP; HTTPS; Internet of Things (IoT); OAuth 2.0; Smart Environments", "DOI": "10.5815/ijcnis.2024.02.01", "PubYear": 2024, "Volume": "16", "Issue": "2", "JournalId": 20260, "JournalTitle": "International Journal of Computer Network and Information Security", "ISSN": "2074-9090", "EISSN": "2074-9104", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "IPT, Instituto de Pesquisas Tecnológicas, SP, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IPTUSP SENAC UNIP, SP, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Azevedo", "Affiliation": "Universidade de Sao Paulo, SP, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Instituto de Pesquisas Tecnológicas, SP, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidade do Oeste Paulista, SP, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Universidade de Sao Paulo, SP, Brazil"}], "References": []}, {"ArticleId": 114258360, "Title": "A Gentle Introduction to Controlled Query Evaluation in DL-Lite Ontologies", "Abstract": "Controlled query evaluation (CQE) is an approach for confidentiality-preserving query answering where a function called censor alters query answers so that users can never infer data that are protected by a policy given in terms of logic formulae. In this paper, we review some foundational results we have recently found in the context of CQE over Description Logic ontologies. In more detail, we discuss the main characteristics of two notions of censor, CQ censor and GA censor, focusing on the computational complexity of query answering and on the notion of indistinguishability. The latter is a desirable property imposing that a censor always makes a user believe that the underlying data instance might not contain confidential data. As for computational aspects, we characterize the data complexity of answering conjunctive queries for the relevant and practical case of $$\\text {DL-Lite} _{{\\mathcal {R}}}$$ DL-Lite R ontologies. Since neither CQ censors nor GA censors enjoy both indistinguishability and tractability of query answering in the analyzed setting, we finally recall the notion of IGA censors, a sound approximation of GA censors which instead enjoys both properties, thus paving the way for robust and practical CQE for $$\\text {DL-Lite} _{{\\mathcal {R}}}$$ DL-Lite R ontologies.", "Keywords": "Description Logics; Information disclosure; Computational complexity", "DOI": "10.1007/s42979-024-02652-4", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sapienza University of Rome, Roma, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Sapienza University of Rome, Roma, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sapienza University of Rome, Roma, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sapienza University of Rome, Roma, Italy; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Bergamo, Bergamo, Italy"}], "References": [{"Title": "A false sense of security", "Authors": "Pier<PERSON>", "PubYear": 2022, "Volume": "310", "Issue": "", "Page": "103741", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": 114258462, "Title": "Hybrid semantics-based vulnerability detection incorporating a Temporal Convolutional Network and Self-attention Mechanism", "Abstract": "<b  >Context:</b> Desirable characteristics in vulnerability-detection (VD) systems (VDSs) include both good detection capability (high accuracy, low false positive rate, low false negative rate, etc.) and low time overheads. The widely used VDSs based on models such as Recurrent Neural Networks (RNNs) have some problems, such as low time efficiency, failing to learn the vulnerability features better, and insufficient amounts of vulnerability features. Therefore, it is very important to construct an automatic detection model with high detection accuracy. <b  >Objective:</b> This paper reports on training based on the source code to analyze and learn from the code’s patterns and structures by deep-learning techniques to generate an efficient VD model that does not require manual feature design. <b  >Method:</b> We propose a software VD model based on multi-feature fusion and deep neural networks called AIdetectorX-SP. It first uses a Temporal Convolutional Network (TCN) and adds a Self-attention Mechanism (SaM) to the TCN to build a model for extracting vulnerability logic features, then transforms the source code into an image input to a Convolutional Neural Network (CNN) to extract structural and semantic information. Finally, we use feature-fusion technology to design and implement an improved deep-learning-based VDS, called AIdetectorX Sequence with Picturization (AIdetectorX-SP). <b  >Results:</b> We report on experiments conducted using publicly-available and widely-used datasets to evaluate the effectiveness of AIdetectorX-SP, with results indicating that AIdetectorX-SP is an effective VDS; that the combination of TCN and SaM can effectively extract vulnerability logic features; and that the pictorial code can extract code structure features, which can further improve the VD capability. <b  >Conclusion:</b> In this paper, we propose a novel detection model for software vulnerability based on TCNs, SaM, and software picturization. The proposed model solves some shortcomings and limitations of existing VDSs, and obtains a high software-VD accuracy with a high degree of stability.", "Keywords": "", "DOI": "10.1016/j.infsof.2024.107453", "PubYear": 2024, "Volume": "171", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China;Jiangsu Key Laboratory of Security Technology for Industrial Cyberspace, Jiangsu University, Zhenjiang 212013, China;Corresponding author at: School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China;Jiangsu Key Laboratory of Security Technology for Industrial Cyberspace, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China;Jiangsu Key Laboratory of Security Technology for Industrial Cyberspace, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 4, "Name": "Saihua Cai", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China;Jiangsu Key Laboratory of Security Technology for Industrial Cyberspace, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of Nottingham Ningbo China, Zhejiang 315100, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China;Jiangsu Key Laboratory of Security Technology for Industrial Cyberspace, Jiangsu University, Zhenjiang 212013, China"}], "References": [{"Title": "Ransomware classification using patch-based CNN and self-attention network on embedded N-grams of opcodes", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "708", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Temporal convolutional neural (TCN) network for an effective weather forecasting using time-series data from the local weather station", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "21", "Page": "16453", "JournalTitle": "Soft Computing"}, {"Title": "VUDENC: Vulnerability Detection with Deep Learning on a Natural Codebase for Python", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "144", "Issue": "", "Page": "106809", "JournalTitle": "Information and Software Technology"}, {"Title": "Predicting vulnerability inducing function versions using node embeddings and graph neural networks", "Authors": "<PERSON><PERSON>; Ecem Mine Özyedierler; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "145", "Issue": "", "Page": "106822", "JournalTitle": "Information and Software Technology"}, {"Title": "BiTCN_DRSN: An effective software vulnerability detection model based on an improved temporal convolutional network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "204", "Issue": "", "Page": "111772", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 114258490, "Title": "Design and implementation of hardware-software architecture based on hashes for SPHINCS+", "Abstract": "<p>Advances in quantum computing have posed a future threat to today’s cryptography. With the advent of these quantum computers, security could be compromised. Therefore, the National Institute of Standards and Technology (NIST) has issued a request for proposals to standardize algorithms for post-quantum cryptography (PQC), which is considered difficult to solve for both classical and quantum computers. Among the proposed technologies, the most popular choices are lattice-based (shortest vector problem) and hash-based approaches. Other important categories are public key cryptography (PKE) and digital signatures.</p><p>Within the realm of digital signatures lies SPHINCS+. However, there are few implementations of this scheme in hardware architectures. In this article, we present a hardware-software architecture for the SPHINCS+ scheme. We utilized a free RISC-V (Reduced Instruction Set Computer) processor synthesized on a Field Programmable Gate Array (FPGA), primarily integrating two accelerator modules for Keccak-1600 and the Haraka hash function. Additionally, modifications were made to the processor to accommodate the execution of these added modules. Our implementation yielded a 15-fold increase in performance with the SHAKE-256 function and nearly 90-fold improvement when using Haraka, compared to the reference software. Moreover, it is more compact compared to related works. This implementation was realized on a Xilinx FPGA Arty S7: Spartan-7.</p>", "Keywords": "", "DOI": "10.1145/3653459", "PubYear": 2024, "Volume": "17", "Issue": "4", "JournalId": 10055, "JournalTitle": "ACM Transactions on Reconfigurable Technology and Systems", "ISSN": "1936-7406", "EISSN": "1936-7414", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "INAOE, Puebla, Mexico"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "INAOE, Puebla, Mexico"}], "References": [{"Title": "Compact Dilithium Implementations on Cortex-M3 and Cortex-M4", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems"}, {"Title": "A Software/Hardware Co-Design of Crystals-Dilithium Signature Scheme", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Reconfigurable Technology and Systems"}, {"Title": "Post-quantum cryptography Algorithm's standardization and performance analysis", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "", "Page": "100242", "JournalTitle": "Array"}, {"Title": "Post-Quantum Signatures on RISC-V with Hardware Acceleration", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "23", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}]}, {"ArticleId": 114258503, "Title": "An accurate hypertension detection model based on a new odd-even pattern using ballistocardiograph signals", "Abstract": "<b  >Background</b> Ballistocardiography (BCG) signals play an important role in identifying hypertension. These signals coupled with machine learning methods can be used for detecting hypertension. In this study, we presented a novel feature engineering architecture to detect hypertension using BCG signals. <b  >Methods and architecture</b> In this work, we used a publicly available BCG signal dataset to develop the novel model. Our model consists of a unique feature extraction process, named the Odd-Even Pattern (OEP). This technique generates three specific feature vectors based on alternating odd and even indices when fed with BCG signals. We employed singular pooling to address the shortcomings of OEP in extracting advanced-level features. This technique combines singular value decomposition with statistical feature extraction to capture the intricate details from the BCG signals. We obtained 14 features by merging OEP with statistical features. We then employed two advanced feature selectors yielding 28 selected feature vectors, hence the model is named as OddEven28. The classification is done using k-nearest neighbors (kNN) algorithm, along with iterative majority voting. <b  >Results</b> Our proposed OddEven28 model achieved a classification accuracy of 97.78% using six different feature vectors on the dataset. <b  >Conclusions</b> Our developed OddEven28 architecture performed better than the deep learning-based models developed for the automated detection of hypertension using BCG signals. The robustness of the model can be improved by training with huge diverse data obtained from various centers.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.108306", "PubYear": 2024, "Volume": "133", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Digital Forensics Engineering, Technology Faculty, Firat University, Elazig, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business (Information System), University of Southern, Queensland, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Digital Forensics Engineering, Technology Faculty, Firat University, Elazig, Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Physics and Computing, University of Southern Queensland, Springfield, Australia"}], "References": [{"Title": "A novel automated tower graph based ECG signal classification method with hexadecimal local adaptive binary pattern and deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "2", "Page": "711", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A support system for automatic classification of hypertension using BCG signals", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "214", "Issue": "", "Page": "119058", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A new one-dimensional testosterone pattern-based EEG sentence classification method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "119", "Issue": "", "Page": "105722", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 114258513, "Title": "Detecting cyberbullying using deep learning techniques: a pre-trained glove and focal loss technique", "Abstract": "<p>This study investigates the effectiveness of various deep learning and classical machine learning techniques in identifying instances of cyberbullying. The study compares the performance of five classical machine learning algorithms and three deep learning models. The data undergoes pre-processing, including text cleaning, tokenization, stemming, and stop word removal. The experiment uses accuracy, precision, recall, and F1 score metrics to evaluate the performance of the algorithms on the dataset. The results show that the proposed technique achieves high accuracy, precision, and F1 score values, with the Focal Loss algorithm achieving the highest accuracy of 99% and the highest precision of 86.72%. However, the recall values were relatively low for most algorithms, indicating that they struggled to identify all relevant data. Additionally, the study proposes a technique using a convolutional neural network with a bidirectional long short-term memory layer, trained on a pre-processed dataset of tweets using GloVe word embeddings and the focal loss function. The model achieved high accuracy, precision, and F1 score values, with the GRU algorithm achieving the highest accuracy of 97.0% and the NB algorithm achieving the highest precision of 96.6%.</p>", "Keywords": "Cyberbullying;Deep learning;GRU;LSTM;Machine learning", "DOI": "10.7717/peerj-cs.1961", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Curricula and Teaching Methods, College of Education, King Faisal University, Al-Ahsa,  Saudi Arabia;Faculty of Specific Education, Minia University, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, College of Science, King Faisal University, Al-Ahsa, Saudi Arabia;Department of Computer Science, Faculty of Science, Minia University, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Science, Minia University, Egypt;Computer Science Unit, Deraya University, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computers and Information, Minia University, EL-Minia, Egypt"}], "References": [{"Title": "Detecting and visualizing hate speech in social media: A cyber Watchdog for surveillance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113725", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Time Series Prediction Method Based on Variant LSTM Recurrent Neural Network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "2", "Page": "1485", "JournalTitle": "Neural Processing Letters"}, {"Title": "Comparing general and specialized word embeddings for biomedical named entity recognition", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON>-<PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Cyberbullying detection: Utilizing social media features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "115001", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-label Arabic text classification in Online Social Networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "101785", "JournalTitle": "Information Systems"}, {"Title": "Automatic Detection of Cyberbullying and Abusive Language in Arabic Content on Social Networks: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "189", "Issue": "", "Page": "156", "JournalTitle": "Procedia Computer Science"}, {"Title": "Detecting Arabic Cyberbullying Tweets Using Machine Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "1", "Page": "29", "JournalTitle": "Machine Learning and Knowledge Extraction"}, {"Title": "Improving Semantic Information Retrieval Using Multinomial Naive Bayes Classifier and Bayesian Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "272", "JournalTitle": "Information"}, {"Title": "Contents-Based Spam Detection on Social Networks Using RoBERTa Embedding and Stacked BLSTM", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "A Review on Deep-Learning-Based Cyberbullying Detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; M<PERSON><PERSON> <PERSON>; Md. <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "5", "Page": "179", "JournalTitle": "Future Internet"}, {"Title": "A non-negative feedback self-distillation method for salient object detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "e1435", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Comparative performance of ensemble machine learning for Arabic cyberbullying and offensive language detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "58", "Issue": "2", "Page": "695", "JournalTitle": "Language Resources and Evaluation"}, {"Title": "Cyberbullying in text content detection: an analytical review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "45", "Issue": "9", "Page": "579", "JournalTitle": "International Journal of Computers and Applications"}, {"Title": "Pashto offensive language detection: a benchmark dataset and monolingual Pashto BERT", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Arabic Toxic Tweet Classification: Leveraging the AraBERT Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "4", "Page": "170", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "The effect of rebalancing techniques on the classification performance in cyberbullying datasets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "3", "Page": "1049", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 114258629, "Title": "A fictional history of robotics features forgotten real-world robots", "Abstract": "<p> The science-fiction movie The Creator uses six real-world robots from the 1950s and 1960s to show progress in AI. </p>", "Keywords": "", "DOI": "10.1126/scirobotics.ado7982", "PubYear": 2024, "Volume": "9", "Issue": "88", "JournalId": 15892, "JournalTitle": "Science Robotics", "ISSN": "", "EISSN": "2470-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science and Engineering, Texas A&M University, College Station, TX 77843, USA."}], "References": []}, {"ArticleId": 114258783, "Title": "A Model for Predicting Chronic Kidney Diseases Based on Medical Data Using Reinforcement Learning", "Abstract": "<p>Kidney diseases (KD) are a global public health concern affecting millions. Early detection and prediction are crucial for effective treatment. Artificial intelligence (AI) techniques have been used in KDP to analyze past medical records, applying patients’ Electronic Medical Record (EHR) data. However, conventional statistical analysis methods conflict with fully comprehending the complexity of EHR data. AI algorithms have helped early KDP learn and identify complex data patterns. However, challenges include training heterogeneous historical data, protecting privacy and security, and developing monitoring system regulations. This study addresses the primary challenge of training heterogeneous datasets for real-world evaluation. Early detection and diagnosis of chronic kidney disease (CKD) is crucial for improved outcomes, reduced healthcare costs, and reliable treatment. Early treatments are crucial for CKD, as it often develops without apparent symptoms. Predictive models, particularly those using reinforcement learning (RL), can identify significant trends in complex healthcare information, which standard techniques may struggle with. The study makes KDP more accurate and reliable using RL methods on clinical data. This lets doctors find diseases earlier and treat them better by looking at static and changing health measurements. Machine learning (ML) algorithms can enhance the accuracy of AI systems over time, enhancing their effectiveness in detecting and diagnosing diseases. In the current investigation, the RL-ANN model is implemented for performing enforceable CKD by assessing the outcomes of multiple neural networks, which include FNN, RNN, and CNN, according to parameters such as accuracy, sensitivity, specificity, prediction error, prediction rate, and kidney failure rate (KFR). The recommended RL-ANN method has a lower failure rate of 70% based on the KFR data. Further, the proposed approach earned 95% in PR and 70% in analysis of errors. However, the RL-ANN approach obtained superior results of 97% accuracy, 95% sensitivity, and 90% specificity.</p>", "Keywords": "Chronic kidney disease; Medical history; Machine learning; RL-ANN; Alerting systems and diagnostic assistance", "DOI": "10.1007/s42979-024-02665-z", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, School of Computing, Vel Tech Ra<PERSON>rajan Dr. <PERSON><PERSON><PERSON> R&D Institute of Science and Technology (Deemed to be University), Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Artificial Intelligence and Data Science, Panimalar Engineering College, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Business Systems Engineering, JSPM’S <PERSON><PERSON> College of Engineering, Savitribai Phule Pune University, Pune, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON>’s Institute of Technology, Chennai, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Paavai Engineering College, Namakkal, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department Information Technology, KCG College of Technology, Chennai, India; Corresponding author."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Master of Computer Application, T. J. Institute of Technology, Chennai, India"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, PSN College of Engineering and Technology, Tirunelveli, India; Corresponding author."}], "References": [{"Title": "Design an attribute based health record protection algorithm for healthcare services in cloud environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "5-6", "Page": "3943", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An image classification framework exploring the capabilities of extreme learning machines and artificial bee colony", "Authors": "Annaparedd<PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "8", "Page": "3079", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Visual topic models for healthcare data clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "2", "Page": "545", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "3D sign language recognition with joint distance and angular coded color topographical descriptor on a 2 – stream CNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "372", "Issue": "", "Page": "40", "JournalTitle": "Neurocomputing"}, {"Title": "Integrating Cuckoo search-Grey wolf optimization and Correlative Naive Bayes classifier with Map Reduce model for big data classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "127", "Issue": "", "Page": "101788", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "Design and sensitivity analysis of capacitive MEMS pressure sensor for blood pressure measurement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "8", "Page": "2371", "JournalTitle": "Microsystem Technologies"}, {"Title": "Modified Social Group Optimization—a meta-heuristic algorithm to solve short-term hydrothermal scheduling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106524", "JournalTitle": "Applied Soft Computing"}, {"Title": "Low dimensional DCT and DWT feature based model for detection of image splicing and copy-move forgery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "39-40", "Page": "29977", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An automatic approach based on CNN architecture to detect Covid-19 disease from chest X-ray images", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "5", "Page": "2864", "JournalTitle": "Applied Intelligence"}, {"Title": "A smart grid incorporated with ML and IoT for a secure management system", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "83", "Issue": "", "Page": "103954", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Prediction of Brain Stroke Severity Using Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "6", "Page": "753", "JournalTitle": "Revue d'intelligence artificielle"}, {"Title": "Quantum inspired meta-heuristic approach for optimization of genetic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "94", "Issue": "", "Page": "107356", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "RETRACTED ARTICLE: Wearable sensor based acoustic gait analysis using phase transition-based optimization algorithm on IoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "27", "Issue": "2", "Page": "519", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Blockchain‐based\n IoT \n architecture to secure healthcare system using identity‐based encryption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "10", "Page": "e12915", "JournalTitle": "Expert Systems"}]}, {"ArticleId": 114258853, "Title": "Autism spectrum disorder based on squeezenet with fractional tasmanian rat swarm optimization", "Abstract": "<p>A complicated neuro-developmental disorder called Autism Spectrum Disorder (ASD) is abnormal activities related to brain development. ASD generally affects the physical impression of the face as well as the growth of the brain in children. An early and proper medical diagnosis is essential for ASD affected children to enhance their quality of life. However, the clinical detection of ASD is a difficult task and time-consuming, hence it is essential to design an ASD detection approach for precise diagnosis of ASD children. In this research, an algorithmic approach called Fractional Tasmanian Rat Swarm Optimization driven SqueezeNet (FTRSO-SqueezeNet) is designed for the detection of ASD. The median filter and Region of Interest (RoI) extraction are used to de-noise the input image initially and extract a particular region from the filtered image. Later, the nub regions are extracted by choosing the optimal grid from the pre-processed image and using different feature extractors the features from the input images are determined. Finally, the detection of ASD is done by using SqueezeNet, which is trained using the FTRSO approach. The performance of the method is estimated, where the designed model achieved higher performance with an accuracy of 94.55%, sensitivity of 92.53%, and specificity of 95.22% than other prevailing approaches.</p>", "Keywords": "Rat Swarm Optimization; Tasmanian Devil Optimization; Fractional Calculus; SqueezeNet; RoI extraction", "DOI": "10.1007/s11042-024-18800-0", "PubYear": 2024, "Volume": "83", "Issue": "41", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, GMR Institute of Technology, Rajam, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Velagapu<PERSON><PERSON><PERSON><PERSON>a Engineering College, Kanuru, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Panimalar Engineering College, Bangalore Trunk Road, Pooñamallee, India"}], "References": [{"Title": "Automated glaucoma detection using GIST and pyramid histogram of oriented gradients (PHOG) descriptors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "3", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A novel algorithm for global optimization: Rat Swarm Optimizer", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Atulya Nagar", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "8457", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Development of Optimal Feature Selection and Deep Learning Toward Hungry Stomach Detection Using Audio Signals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "4", "Page": "853", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}, {"Title": "Detection of Autism Spectrum Disorder in Children Using Machine Learning Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "5", "Page": "386", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 114258906, "Title": "Learning shared features from specific and ambiguous descriptions for text-based person search", "Abstract": "<p>Text-based person search endeavors to utilize natural language descriptions for retrieving pedestrian images. Previous studies have primarily focused on leveraging information among pedestrians with distinct identities, overlooking the exploration of data variations within the same identity. Although some have attempted to extract multiple samples for each identity, an appropriate loss function was not employed. In response to this research gap, we present LFSA, a concise cross-model framework that Learns shared Features from Specific and Ambiguous descriptions. Firstly, building upon a distinctive sampling strategy, we formulate the Boundary Constraints Loss (BCL) and the Hard Sample Mining Loss (HSML) with the aim of extracting unique features from specific descriptions while simultaneously capturing shared features from ambiguous descriptions. Then, we introduce a textual augmentation module denoted as Mask-Delete-Replace (MDR). This module employs three operations to direct the model’s attention toward more comprehensive details within the textual descriptors. LFSA utilizes CLIP as the backbone of the network, only leveraging its global features from the [CLS] token. Extensive experiments on two benchmark datasets, CUHK-PEDES and ICFG-PEDES, demonstrate the effectiveness of our approach. Codes are available at https://github.com/CottonCandyZ/LFSA .</p>", "Keywords": "Text-based person search; Transformer; Joint embedding learning", "DOI": "10.1007/s00530-024-01286-z", "PubYear": 2024, "Volume": "30", "Issue": "2", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer, Jiangsu University of Science and Technology, Zhenjiang, China"}, {"AuthorId": 2, "Name": "Qikai Geng", "Affiliation": "School of Computer, Jiangsu University of Science and Technology, Zhenjiang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Huang", "Affiliation": "School of Computer, Jiangsu University of Science and Technology, Zhenjiang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer, Jiangsu University of Science and Technology, Zhenjiang, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang, China; Corresponding author."}], "References": [{"Title": "Encoder-decoder assisted image generation for person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "7", "Page": "10373", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "PVT v2: Improved baselines with Pyramid Vision Transformer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "415", "JournalTitle": "Computational Visual Media"}, {"Title": "TIPCB: A simple but effective part-based convolutional baseline for text-based person search", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Yu<PERSON> Lu", "PubYear": 2022, "Volume": "494", "Issue": "", "Page": "171", "JournalTitle": "Neurocomputing"}, {"Title": "A Similarity Matrix Low-Rank Approximation and Inconsistency Separation Fusion Approach for Multiview Clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "2", "Page": "868", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Soft-orthogonal constrained dual-stream encoder with self-supervised clustering network for brain functional connectivity data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "244", "Issue": "", "Page": "122898", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114259006, "Title": "ScrumSpiral: An Improved Hybrid Software Development Model", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijitcs.2024.02.05", "PubYear": 2024, "Volume": "16", "Issue": "2", "JournalId": 8584, "JournalTitle": "International Journal of Information Technology and Computer Science", "ISSN": "2074-9007", "EISSN": "2074-9015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, American International University-Bangladesh, Dhaka, 1229, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Zinniya Taffannum Pritee", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114259013, "Title": "A GAN-based method for diagnosing bodywork spot welding defects in response to small sample condition", "Abstract": "Due to the hidden nature and complexity of resistance spot welding weld nugget formation, how to avoid the time-consuming and money-consuming problem of traditional defect diagnosis methods and accurately grasp the weld nugget status is still an urgent problem. In this paper, an improved GAN model is proposed to solve the corresponding problem by combining the weld nugget defects with the dynamic resistance curve. Aiming at the problem that traditional GAN algorithms are prone to pattern collapse, this paper utilizes a variational autoencoder integrated with a channel attention mechanism as the generator part of the generative adversarial network, which helps the model pay better attention to the high-weight part of the defective sample data and combines the encoding and decoding processes to highlight defective features, thus reconstructing the defective samples with higher quality. Convolutional neural networks are then utilized to identify the features of the generated samples and diagnose the type of weldment defects. The test results show that the proposed scheme is highly reliable and the model outperforms other schemes in diagnosing welded nugget defects under the same conditions, avoiding undesirable effects such as underfitting. The validation of the actual dataset shows that, compared with other diagnostic methods that generally have an accuracy rate of less than 75%, the accuracy of the weld nugget defects diagnosis of this paper&#x27;s method reaches more than 94%, which is a positive impetus to the development of auto body welding diagnosis.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111544", "PubYear": 2024, "Volume": "157", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan 430070, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan 430070, PR China;School of Mechanical Engineering, Hubei University of Technology, Wuhan 430100, PR China;Corresponding author at: School of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan 430070, PR China"}, {"AuthorId": 3, "Name": "Fu <PERSON>i", "Affiliation": "School of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan 430070, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Wuhan University of Technology, Wuhan 430070, PR China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Dongfeng Motor Corporation, Wuhan 430056, PR China"}], "References": [{"Title": "Intelligent rotating machinery fault diagnosis based on deep learning using data augmentation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "433", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "How Generative Adversarial Networks and Their Variants Work", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A new graph-based semi-supervised method for surface defect classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "102083", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Dual VAEGAN: A generative model for generalized zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107352", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep regularized variational autoencoder for intelligent fault diagnosis of rotor–bearing system within entire life-cycle process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "107142", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Online quality inspection of resistance spot welding for automotive production lines", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "63", "Issue": "", "Page": "354", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Monitoring of wastewater treatment process based on multi-stage variational autoencoder", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "117919", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114259080, "Title": "YOLOv8s-CGF: a lightweight model for wheat ear Fusarium head blight detection", "Abstract": "<p> Fusarium head blight (FHB) is a destructive disease that affects wheat production. Detecting FHB accurately and rapidly is crucial for improving wheat yield. Traditional models are difficult to apply to mobile devices due to large parameters, high computation, and resource requirements. Therefore, this article proposes a lightweight detection method based on an improved YOLOv8s to facilitate the rapid deployment of the model on mobile terminals and improve the detection efficiency of wheat FHB. The proposed method introduced a C-FasterNet module, which replaced the C2f module in the backbone network. It helps reduce the number of parameters and the computational volume of the model. Additionally, the Conv in the backbone network is replaced with GhostConv, further reducing parameters and computation without significantly affecting detection accuracy. Thirdly, the introduction of the Focal CIoU loss function reduces the impact of sample imbalance on the detection results and accelerates the model convergence. Lastly, the large target detection head was removed from the model for lightweight. The experimental results show that the size of the improved model (YOLOv8s-CGF) is only 11.7 M, which accounts for 52.0% of the original model (YOLOv8s). The number of parameters is only 5.7 × 10 <sup>6</sup> M, equivalent to 51.4% of the original model. The computational volume is only 21.1 GFLOPs, representing 74.3% of the original model. Moreover, the mean average precision (mAP@0.5) of the model is 99.492%, which is 0.003% higher than the original model, and the mAP@0.5:0.95 is 0.269% higher than the original model. Compared to other YOLO models, the improved lightweight model not only achieved the highest detection precision but also significantly reduced the number of parameters and model size. This provides a valuable reference for FHB detection in wheat ears and deployment on mobile terminals in field environments. </p>", "Keywords": "Fusarium head blight;Image recognition;Lightweight model;Loss function;YOLOv8s", "DOI": "10.7717/peerj-cs.1948", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Yang", "Affiliation": "College of Information and Management Science, Henan Agricultural University, Zhengzhou, Henan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Management Science, Henan Agricultural University, Zhengzhou, Henan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Management Science, Henan Agricultural University, Zhengzhou, Henan, China"}, {"AuthorId": 4, "Name": "Haiyan Lv", "Affiliation": "College of Information and Management Science, Henan Agricultural University, Zhengzhou, Henan, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Information and Management Science, Henan Agricultural University, Zhengzhou, Henan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Management Science, Henan Agricultural University, Zhengzhou, Henan, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Management Science, Henan Agricultural University, Zhengzhou, Henan, China;Henan Grain Crop Collaborative Innovation Center, Henan Agricultural University, Zhengzhou, Henan, China"}], "References": [{"Title": "Focal and efficient IOU loss for accurate bounding box regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "506", "Issue": "", "Page": "146", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 114259241, "Title": "Multiclassification of DDoS Attacks using Machine and Deep Learning Techniques", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSN.2023.10063264", "PubYear": 2023, "Volume": "1", "Issue": "1", "JournalId": 15172, "JournalTitle": "International Journal of Security and Networks", "ISSN": "1747-8405", "EISSN": "1747-8413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114259322, "Title": "Control practice for robotic applications in challenging environments", "Abstract": "The use of robots has exceeded the standard focus of manufacturing and production. Over the last decades, special robotic systems have been developed in various extreme environments, such as in the maintenance, repair or even decommissioning of large-scale, strategic facilities, important to any nation’s infrastructure, including power, space, mining, etc. The deployment areas for these robots, like nuclear fuel handling systems, are generally hazardous or unreachable for human beings. The control techniques therein will play an indispensable role in the overall performance of a robotic system as they need to answer enhanced requirements for performance, robustness, and long-term reliability, driven by the fundamental demand for safe operation in complex and hazardous environments. This also needs an understanding of the enhanced industrial standards and requirements for the research, development, design and use of control systems in such environments. The control systems need to be designed specifically capable of tackling different practical control challenges caused by extreme environmental factors. This special section is designed and motivated to bridge the gap between the research community and application engineers, and to help connect control theory, control applications and industrial requirements/regulations.", "Keywords": "", "DOI": "10.1016/j.arcontrol.2024.100956", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 3642, "JournalTitle": "Annual Reviews in Control", "ISSN": "1367-5788", "EISSN": "1872-9088", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "United Kingdom Atomic Energy Authority, Abingdon, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The University of Manchester, Manchester, UK"}], "References": [{"Title": "Industry engagement with control research: Perspective and messages", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "49", "Issue": "", "Page": "1", "JournalTitle": "Annual Reviews in Control"}]}, {"ArticleId": *********, "Title": "Arrival and service time dependencies in the single- and multi-visit selective traveling salesman problem", "Abstract": "We analyze several time dependency issues for the selective traveling salesman problem with time-dependent profits. Specifically, we consider the case in which the profit collected at a vertex depends on the service time, understood as the time spent at this vertex, and when the service time at each vertex depends on the arrival time at the vertex. For each of these two cases, we formulate two continuous-time problems: (i) a vertex can be visited at most once, and (ii) vertices may be visited more than once. In each case, we consider general profit functions at the vertices, i.e., the profit functions are not limited to monotonic functions of time. We also formulate the problems as discrete-time problems using appropriate variants of an auxiliary time-extended graph, and we solve them with <PERSON><PERSON>bi. We apply our methodology to two sets of instances. First, we use a set of artificial instances to illustrate the main differences amongst the different versions of the problem. We then solve several instances adapted from TSPLIB to evaluate the computational capabilities of the methodology.", "Keywords": "Selective traveling salesman problem; Time-dependent service time; Service time-dependent profit; Single- and multi-visit", "DOI": "10.1016/j.cor.2024.106632", "PubYear": 2024, "Volume": "166", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering and Management Science, University of Seville, Seville, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Economics, Quantitative Methods and Economic History, Pablo de Olavide University, Ctra. de Utrera, 1, 41013, Seville, Spain;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "HEC Montréal, Canada;University of Bath, UK"}], "References": [{"Title": "An enhanced lower bound for the Time-Dependent Travelling Salesman Problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "113", "Issue": "", "Page": "104795", "JournalTitle": "Computers & Operations Research"}, {"Title": "Team Orienteering with Time-<PERSON><PERSON><PERSON> Profit", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "262", "JournalTitle": "INFORMS Journal on Computing"}, {"Title": "Iterated Maximum Large Neighborhood Search for the Traveling Salesman Problem with Time Windows and its Time-dependent Version", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "150", "Issue": "", "Page": "106078", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": *********, "Title": "Motion‐Compensation Control of Supernumerary Robotic Arms Subject to Human‐Induced Disturbances", "Abstract": "Supernumerary robotic limbs (SRLs) are a novel type of wearable robot used as the third limb to assist the wearer to perform operations that are difficult or impossible for human hands. Although SRLs can compensate for and enhance human physiological abilities, the unpredictable disturbances caused by human movements significantly affect the coordinating control between the robot and wearer. In this study, a general modeling of supernumerary robotic arms (SRAs) on an omnidirectional floating base is presented. Using position and orientation feedback from sensors at the base and tip, three control methods based on different sensor feedback are proposed to improve tracking accuracy. Experiments on point and trajectory tracking are conducted on the SRAs. In the results (point and circular trajectory‐tracking errors with the manipulator as floating base: 1.18 ± 0.56 mm [mean ± standard deviation(SD) error] and 1.42 ± 0.43 mm, point trajectory‐tracking errors with the human shoulder as floating base: 1.37 ± 0.58 mm, and the performance in perforation positioning operation experiment), it is demonstrated that the proposed controllers enable the SRAs to achieve high‐precision tracking and good adaptability to different user movements and frequencies. Also, in the results, future studies on dynamic high‐precision manipulation of SRLs are motivated.", "Keywords": "human disturbances;motion compensations;sensor feedbacks;supernumerary robotic limbs", "DOI": "10.1002/aisy.202300448", "PubYear": 2024, "Volume": "6", "Issue": "5", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "Qing<PERSON> Zhang", "Affiliation": "The School of Mechatronics Engineering Harbin Institute of Technology  Harbin Heilongjiang 150001 China"}, {"AuthorId": 2, "Name": "Dongbao Sui", "Affiliation": "Ji Hua Laboratory Jihua Institute of Biomedical Engineering and Technology  Foshan 528200 China"}, {"AuthorId": 3, "Name": "Hongwei Jing", "Affiliation": "The School of Mechatronics Engineering Harbin Institute of Technology  Harbin Heilongjiang 150001 China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The School of Mechatronics Engineering Harbin Institute of Technology  Harbin Heilongjiang 150001 China"}, {"AuthorId": 5, "Name": "Yuancheng Li", "Affiliation": "The School of Mechatronics Engineering Harbin Institute of Technology  Harbin Heilongjiang 150001 China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The School of Mechatronics Engineering Harbin Institute of Technology  Harbin Heilongjiang 150001 China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The School of Mechatronics Engineering Harbin Institute of Technology  Harbin Heilongjiang 150001 China"}, {"AuthorId": 8, "Name": "Yuan Tang", "Affiliation": "The Department of Mechanical, Aerospace and Civil Engineering University of Manchester  150080 Manchester UK"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics Hainan Normal University  Haikou 571158 China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "The School of Mechatronics Engineering Harbin Institute of Technology  Harbin Heilongjiang 150001 China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "The School of Mechatronics Engineering Harbin Institute of Technology  Harbin Heilongjiang 150001 China"}], "References": []}, {"ArticleId": 114259470, "Title": "Implementasi Algoritma Neighborhood-Based Collaborative Filtering Pada Sistem Rekomendasi Layanan Laundry", "Abstract": "<p>Teknologi informasi saat ini telah membawa kehidupan manusia ke arah yang lebih maju terutama dalam menyajikan informasi yang penting. Banyaknya informasi yang dapat disediakan oleh teknologi informasi menimbulkan kesulitan bagi masyarakat untuk menentukan pilihan informasi yang sesuai dengan kebutuhannya. Sistem rekomendasi menjadi solusi untuk membantu masyarakat dalam menyaring informasi yang dibutuhkan. Penggunaan sistem rekomendasi dapat dilakukan dalam bidang bisnis seperti layanan laundry dengan menerapkan algoritma collaborative filtering. Dengan menggunakan desain penelitian hubungan kausal (eksperimental) sebagai rancangan penelitian. Teknik pengumpulan data yang digunakan penulis adalah studi literatur yang meliputi buku-buku ilmiah, laporan penelitian, jurnal ilmiah, skripsi, serta sumber-sumber tertulis baik cetak ataupun elektronik. Teknik pemodelan sistem yang digunakan penulis adalah menggunakan Unified Modeling Language (UML), yang berperan untuk membantu mengambarkan prosedur dalam cara kerja sistem rekomendasi dengan menerapkan collaborative filtering dalam merekomendasikan layanan laundry. Algoritma collaborative filtering menyaring data layanan laundry berdasarkan karakteristik yang diinginkan pengguna untuk memberikan informasi yang baru berdasarkan pola suatu kelompok pengguna yang memiliki karakteristik yang serupa. Algoritma diimplementasikan dalam suatu aplikasi berbasis web dengan rancangan antarmuka pengguna yang interaktif dalam mennyajikan hasil penyaringan. Sistem rekomendasi layanan laundry mampu memberikan hasil rekomendasi yang sesuai dengan preferensi dan kebutuhan pengguna.</p>", "Keywords": "", "DOI": "10.46229/jifotech.v4i1.870", "PubYear": 2024, "Volume": "4", "Issue": "1", "JournalId": 85255, "JournalTitle": "Journal of Information Technology", "ISSN": "2774-4884", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114259480, "Title": "Retraction Note: Financial Shared Service, Digital Transformation and Corporate Value Creation", "Abstract": "", "Keywords": "", "DOI": "10.1007/s44196-024-00481-0", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, Harbin Institute of Technology, Weihai, China; Corresponding author."}], "References": []}, {"ArticleId": *********, "Title": "A Hybrid Intrusion Detection System to Mitigate Biomedical Malicious Nodes", "Abstract": "This paper proposes an intrusion detection system to prevent malicious node attacks that may result in failure links in wireless body area networks. The system utilizes a combination of Optimized Convolutional Neural Networks and Support Vector Machine techniques to classify nodes as malicious or not, and links as failure or not. In case of detection, the system employs a trust-based routing strategy to isolate malicious nodes or failure links and ensure a secure path. Furthermore, sensitive data is encrypted using a modified RSA encryption algorithm. The experimental results demonstrate the improved network performance in terms of data rate, delay, packet delivery ratio, energy consumption, and network security, by providing effective protection against malicious node attacks and failure links. The proposed system achieves the highest classification rate and sensitivity, surpassing similar methods in all evaluation metrics. © 2024, Modern Education and Computer Science Press. All rights reserved.", "Keywords": "Failure Links; Malicious Nodes; Modified RSA Cipher; Optimized Convolutional Neural Network-support Vector Machine; Security; Trust Value; Wireless Body Area Network", "DOI": "10.5815/ijcnis.2024.02.10", "PubYear": 2024, "Volume": "16", "Issue": "2", "JournalId": 20260, "JournalTitle": "International Journal of Computer Network and Information Security", "ISSN": "2074-9090", "EISSN": "2074-9104", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratory of Coding and Security of Information (LACOSI), Department of Electronic, Faculty of Electrical  Engineering, University of Sciences and Technology of ORAN<PERSON> <PERSON> (USTO-MB), Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Coding and Security of Information (LACOSI), Department of Electronic, Faculty of Electrical Engineering, University of Sciences and Technology of ORAN<PERSON><PERSON> (USTO-MB), Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Coding and Security of Information (LACOSI), Department of Electronic, Faculty of Electrical Engineering, University of Sciences and Technology of ORAN<PERSON><PERSON> (USTO-MB), Algeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Bel<PERSON>", "Affiliation": "Laboratory of Coding and Security of Information (LACOSI), Department of Electronic, Faculty of Electrical Engineering, University of Sciences and Technology of ORAN<PERSON><PERSON> (USTO-MB), Algeria"}], "References": []}, {"ArticleId": 114259573, "Title": "Semantic-specific multimodal relation learning for sentiment analysis", "Abstract": "<p>Multimodal sentiment analysis (MSA) seeks to understand human affection by leveraging signals from multiple modalities. A core challenge in MSA is the effective extraction of sentimental relations between these signals, as this can enhance a model’s consistency and accuracy. Existing studies typically use multimodal matching tasks to learn all semantic relations between modalities and then use downstream task to obtain the specific semantics from the multimodal representation. However, there are multiple semantics between modalities, such as action semantics, scene semantics and sentiment semantics. Relying solely on specific tasks to filter these semantics often results in a surplus of redundant information in the multimodal representation, potentially degrading MSA accuracy. In addition, the unimodal semantic expression is also important. In this paper, we propose a semantic-specific multimodal relation learning method to correlate modalities with specific semantics. Specifically, with smaller computational resources, we enhance unimodal sentimental semantic expression while diminishing non-sentimental semantic information in the multimodal representation. We conducted experiments on multimodal sentiment analysis datasets, CMU-MOSI, CMU-MOSEI and CH-SIMS. The results show that our method outperforms the current state-of-the-art. Notably, on the Acc2 evaluation metric, our approach exhibits an average accuracy improvement of 0.75 compared to the best baseline.</p>", "Keywords": "Multimodal sentiment analysis; Semantic-specific relation learning; Multimodal matching learning; Multitask learning", "DOI": "10.1007/s00521-024-09644-8", "PubYear": 2024, "Volume": "36", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, China"}], "References": [{"Title": "Image-text interaction graph neural network for image-text sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "10", "Page": "11184", "JournalTitle": "Applied Intelligence"}, {"Title": "Aspect-level sentiment classification based on location and hybrid multi attention mechanism", "Authors": "<PERSON><PERSON>; Weijiang Li", "PubYear": 2022, "Volume": "52", "Issue": "10", "Page": "11539", "JournalTitle": "Applied Intelligence"}, {"Title": "Attention mechanisms in computer vision: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "331", "JournalTitle": "Computational Visual Media"}, {"Title": "A soft voting ensemble learning-based approach for multimodal sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "21", "Page": "18391", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Sentiment-aware multimodal pre-training for multimodal sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "258", "Issue": "", "Page": "110021", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "AOBERT: All-modalities-in-One BERT for multimodal sentiment analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON> Kim; Sanghyun Park", "PubYear": 2023, "Volume": "92", "Issue": "", "Page": "37", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 114259593, "Title": "A preliminary step toward intelligent forming of fabric composites: Artificial intelligence-based fiber distortions monitoring", "Abstract": "This work presents a prototype of an intelligent quality inspection tool for application to fibers distortion monitoring in the fabric composites forming processes. To this end, a series of hemisphere draping tests on a typical commingled fiberglass/polypropylene twill weave were conducted at the dry form, and the defects (in-plane and out-of-plane) for each formed part were inspected via camera vision. By gathering data from around 30 samples and over 1200 images from different forming regions, different machine-learning algorithms were trained and validated. As an application scenario in a smart factory, the developed simple AI tool would be used by an operator, or a robot, to scan different areas of the formed parts and identify ‘defected’ (fail) versus ‘non-defected’ (pass) scenarios. It was found that the K-nearest neighbors and Support Vector Machine models detect the defects with an error rate of less than 5% in the present case study, regardless of the background noise in the images such as external objects, marks on samples, or blurriness.", "Keywords": "Fabric-forming; Quality monitoring; Fiber distortions; Intelligent composite manufacturing", "DOI": "10.1016/j.engappai.2024.108262", "PubYear": 2024, "Volume": "133", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Composites Research Network-Okanagan Laboratory, The University of British Columbia, BC, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Composites Research Network-Okanagan Laboratory, The University of British Columbia, BC, Canada;Corresponding author"}], "References": [{"Title": "Literature review of Industry 4.0 and related technologies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "1", "Page": "127", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Continuous mapping of large surfaces with a quality inspection robot", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "156", "Issue": "", "Page": "104195", "JournalTitle": "Robotics and Autonomous Systems"}]}]