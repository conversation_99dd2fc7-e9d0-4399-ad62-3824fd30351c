[{"ArticleId": 95638683, "Title": "Determination of Density and Viscosity of Crude Oil Samples from FTIR Data using Multivariate Regression, Variable Selection and Classification", "Abstract": "The use of Fourier Transform Infrared (FTIR) spectroscopy for quantification of crude oil properties was investigated using chemometric methods. Sample sets consisting of crude oil from seven different Canadian fields were analyzed. Different methods such as PLS, PCA, iPLS, and PLS-GA were used for model building and the results were compared. Evaluation of the models was conducted by determination of the coefficient of determination (R<sup>2</sup>) and cross validation error. The best results for quantification of density and viscosity were obtained by partial least squares (PLS) regression on FTIR data. Data analysis on the total sample set of 82 samples yielded a prediction error (root mean square error of cross validation) of 4.5 × 10<sup>−5</sup> and 0.33 respectively for density and viscosity. Improvement in prediction accuracy of viscosity was obtained by using Decision tree classification on samples before applying PLS regression.", "Keywords": "Chemometric tools ; Partial least squares ; FTIR ; Crude oil ; Viscosity ; Density ; Classification ; Multivariate regression ; Spectroscopy", "DOI": "10.1016/j.ifacol.2022.07.550", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical and Biochemical Engineering, Western University, Canada N6A 5B9"}, {"AuthorId": 2, "Name": "Souvik Ta", "Affiliation": "Department of Chemical and Biochemical Engineering, Western University, Canada N6A 5B9"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical and Biochemical Engineering, Western University, Canada N6A 5B9"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical and Biochemical Engineering, Western University, Canada N6A 5B9;Department of Chemical and Biomolecular Engineering, National University of Singapore, Singapore"}], "References": []}, {"ArticleId": 95638684, "Title": "Predictive Control of Batch Crystallization Process Using Machine Learning", "Abstract": "This work develops a framework for building machine learning models and machine learning-based predictive control schemes for batch crystallization processes. We consider a seeded fesoterodine fumarate cooling crystallization and dissolution in a batch reactor and present the methodology and implementation of simulation, modeling, and controller design. Specifically, to address the experimental data scarcity problem, we first develop a population balance model (PBM) based on published kinetic parameters to describe the formation of crystals via nucleation, growth, and agglomeration. Then, recurrent neural network (RNN) models are developed using data from extensive simulations of the semi-empirical PBM under various operating conditions to capture the process dynamic behavior. The model predictive control (MPC) scheme using RNN models is developed to optimize the crystallization process in terms of product yield, crystal size, and energy consumption, while accounting for the constraints on the manipulated inputs. Through open- and closed-loop simulations, it is demonstrated that the RNN models well capture the process dynamics, and the RNN-based MPC achieves desired product yield and crystal size with significantly improved computational efficiency.", "Keywords": "Batch crystallization processes ; Model predictive control ; Machine learning ; Recurrent neural networks", "DOI": "10.1016/j.ifacol.2022.07.542", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, National University of Singapore, 117585, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, National University of Singapore, 117585, Singapore"}], "References": []}, {"ArticleId": ********, "Title": "A modular approach to constraint satisfaction under uncertainty - with application to bioproduction systems", "Abstract": "The paper proposes a modular-based approach to constraint handling in process optimization and control. This is partly motivated by the recent interest in learning-based methods, e.g., within bioproduction, for which constraint handling under uncertainty is a challenge. The proposed constraint handler, called predictive filter, is combined with an adaptive constraint margin to minimize the cost of violating soft constraints due to uncertainty and disturbances. The module can be combined with any controller and is based on modifying the controller output, in a least squares sense, such that constraints are satisfied within the considered horizon. The proposed method is computationally efficient and suitable for real-time applications. The effectiveness of the method is illustrated by a simple heater example and a nonlinear and time-varying example in penicillin fed-batch production optimization.", "Keywords": "Constraint handling ; predictive filter ; adaptive constraint margin ; bioproduction", "DOI": "10.1016/j.ifacol.2022.07.508", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Division of Decision and Control Systems, EECS, KTH Royal Institute of Technology, Stockholm 10044, Sweden;Competence Centre for Advanced Bioproduction by Continuous Processing, AdBIOPRO, Sweden"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Division of Decision and Control Systems, EECS, KTH Royal Institute of Technology, Stockholm 10044, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Decision and Control Systems, EECS, KTH Royal Institute of Technology, Stockholm 10044, Sweden;Competence Centre for Advanced Bioproduction by Continuous Processing, AdBIOPRO, Sweden"}], "References": [{"Title": "Model-based reinforcement learning and predictive control for two-stage optimal control of fed-batch bioreactor", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "154", "Issue": "", "Page": "107465", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 95638695, "Title": "Simulation platform of an industrial propylene-propane splitter integrated to Advanced Process Control for Real Time Optimization experiments", "Abstract": "The present paper shows the development of a dynamic rigorous model of a propylene-propane splitter connected to an industrial MPC controller via OPC-UA. The model includes the material and energy balances, thermodynamic equilibrium, and constitutive equations. Some of the PI controllers presented in the real plant have also been modeled. A dynamic model requires further information as the sizing of the equipments, heat transfer coefficients, thermodynamics data and a good initial value for the state and algebraic variables. The validation of the model was performed in open loop and in closed loop simulations. The simulation results were compared to the historical data of the process. As future work, the simulation platform created will be used to study new algorithms of RTO with Modifier Adaptation methodology.", "Keywords": "Real-time Optimization ; superfractionator ; Aspen DMC ; MPC", "DOI": "10.1016/j.ifacol.2022.07.521", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Systems Engineering and Automatic Control, School of Industrial Engineering, University of Valladolid, Dr. <PERSON><PERSON><PERSON> s/n, 47011, Vallad<PERSON>d, Spain Institute of Sustainable Processes, Dr. <PERSON><PERSON> s/n, 47011, Valladolid, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Systems Engineering and Automatic Control, School of Industrial Engineering, University of Valladolid, Dr. <PERSON><PERSON><PERSON> s/n, 47011, Vallad<PERSON>d, Spain Institute of Sustainable Processes, Dr. <PERSON><PERSON> s/n, 47011, Valladolid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dpto. Ingeniería Química y Ambiental, Universidad Técnica Federico Santa María, Avd. Vicuña <PERSON>enna, Campus San Joaquín, Santiago, Chile"}], "References": []}, {"ArticleId": 95638696, "Title": "Data-Efficient Reinforcement Learning from Controller Guidance with Integrated Self-Supervision for Process Control", "Abstract": "Model-free reinforcement learning methods have achieved significant success in a variety of decision-making problems. In fact, they traditionally rely on large amounts of data generated by sample-efficient simulators. However, many process control industries involve complex and costly computations, which limits the applicability of model-free reinforcement learning. In addition, extrinsic rewards are naturally sparse in the real world, further increasing the amount of necessary interactions with the environment. This paper presents a sample-efficient model-free algorithm for process control, which massively accelerates the learning process even when rewards are extremely sparse. To achieve this, we leverage existing controllers to guide the agent&#x27;s learning — controller guidance is used to drive exploration towards key regions of the state space. To further mitigate the above-mentioned challenges, we propose a strategy for self-supervision learning that lets us improve the agent&#x27;s policy via its own successful experience. Notably, the method we develop is able to leverage guidance that does not include the actions and remains effective when the existing controllers are suboptimal. We present an empirical evaluation on a vinyl acetate monomer (VAM) chemical plant under disturbances. The proposed method exhibits better performance than baselines approaches and higher sample efficiency. Besides, empirical results show that our method outperforms the existing controllers for controlling the plant and canceling disturbances, mitigating the drop in the production load.", "Keywords": "Reinforcement learning control ; Process control ; Chemical plant control ; Co-Learning ; self-learning ; Artificial intelligence", "DOI": "10.1016/j.ifacol.2022.07.553", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "NEC-AIST AI Cooperative Research Laboratory, National Institute of Advanced Industrial Science and Technology, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NEC-AIST AI Cooperative Research Laboratory, National Institute of Advanced Industrial Science and Technology, Tokyo, Japan;Data Science Research Laboratories, NEC Corporation, Kanagawa, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NEC-AIST AI Cooperative Research Laboratory, National Institute of Advanced Industrial Science and Technology, Tokyo, Japan;Department of Information and Communication Engineering, The University of Tokyo, Tokyo, Japan"}], "References": []}, {"ArticleId": 95638698, "Title": "Hierarchical nonlinear model predictive control of offshore hybrid power systems", "Abstract": "This paper presents an approach for controlling ofshore hybrid power systems consisting of gas turbines, offshore wind, and batteries for satisfying an exogenous power demand. A hierarchical controller is developed comprising a high-level economic nonlinear model predictive controller that distributes the power demand according to some economic objective, a low-level nonlinear tracking model predictive controller that actuates on the hybrid power system, and a nonlinear moving horizon estimator to estimate the system state. Simulation results and concluding remarks reveal the advantage of such a hierarchical approach for a simple simulation study.", "Keywords": "Hierarchical control ; Nonlinear predictive control ; Industrial applications of optimal control ; Power systems ; Control of renewable energy resources", "DOI": "10.1016/j.ifacol.2022.07.488", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Engineering Cybernetics, Norwegian University of Science and Technology, Trondheim, Norway"}, {"AuthorId": 2, "Name": "Brage Rugstad <PERSON>en", "Affiliation": "SINTEF Energy Research, Trondheim, Norway"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Engineering Cybernetics, Norwegian University of Science and Technology, Trondheim, Norway"}], "References": []}, {"ArticleId": 95638700, "Title": "Data-enhanced learning compensation for linear predictive control of nonlinear chemical processes", "Abstract": "In chemical processes, nonlinearities, uncertainties, and constraints have resulted in much more complex optimization problems, since optimization algorithms depend on model characteristics. Optimization success for chemical processes requires a proper combination of the optimization technique and the model that is appropriate. In this paper, we propose a data-enhanced learning compensation method for linear predictive control of nonlinear chemical processes. By using more reliable data to increase the accuracy of the model, optimizing performance can be greatly enhanced. Our method can be used in situations where engineering constraints must be met by a system with dynamics not well understood or nonlinearities that make previous control methods ineffective. Finally, a practical example of the CSTR is provided to demonstrate the efficacy of the proposed methods.", "Keywords": "Model predictive control ; constrained systems ; nonlinear control systems ; data-driven control ; process control ; optimization", "DOI": "10.1016/j.ifacol.2022.07.538", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Biological Engineering, Hong Kong University of Science and Technology, Kowloon, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical and Biological Engineering, Hong Kong University of Science and Technology, Kowloon, Hong Kong and Guangzhou HKUST Fok Ying Tung Research Institute, Guangzhou 511458, China"}], "References": [{"Title": "Learning-Based Model Predictive Control: Toward Safe Learning in Control", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "1", "Page": "269", "JournalTitle": "Annual Review of Control, Robotics, and Autonomous Systems"}, {"Title": "Synthesis of model predictive control based on data-driven learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "8", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "On Data Science for Process Systems Modeling, Control and Operations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "11325", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 95638703, "Title": "Optimization-based framework for technical, economic, and environmental performance assessment of CO2 utilization strategies", "Abstract": "Carbon capture and utilization (CCU) for chemicals and fuel production is one of the effective measures addressing global warming and energy security. Since CCU utilizes harmful CO<sub>2</sub> as a raw material to produce high-value chemicals and fuels such as methanol, Fischer-Tropsch fuel, dimethyl ether, and gasoline, it mitigates CO<sub>2</sub> emission and creates more fuel availability or shares the burden on fossil fuels. This study aims to develop an optimization-based framework of CO<sub>2</sub> utilization strategies and to analyze CO<sub>2</sub>-to-fuel strategies regarding technical, economic, and environmental performance. To achieve this goal, we generated a superstructure consisting of many CO<sub>2</sub> utilization pathways, including a series of technologies (e.g., reaction/conversion, and separation and purification) for different fuel production. We then developed process simulation and estimated the key techno-economic parameters such as mass and energy flow, and sizing and costing data. The optimization models were developed to identify the optimal CO<sub>2</sub> utilization strategy and assess its feasibility with four different criteria: energy efficiency (EEF), production quantity, production cost (UPC), and net CO<sub>2</sub> emission (NCE). As a result, the proposed optimization-based framework is able to i) identify the best CO<sub>2</sub> utilization strategy over various technological pathways for targeted fuels, ii) provide a decision-making guide to policymakers and stakeholders for planning an economically viable and sustainable CO<sub>2</sub> utilization strategy.", "Keywords": "CO<sub>2</sub> utilization superstructure ; CO<sub>2</sub>-based fuels ; Process design ; Techno-economic analysis ; Optimization", "DOI": "10.1016/j.ifacol.2022.07.478", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Thai Ngan Do", "Affiliation": "School of Chemical Engineering, Sungkyungkwan University, 16419, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Sungkyungkwan University, 16419, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Sungkyungkwan University, 16419, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Sungkyungkwan University, 16419, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Sungkyungkwan University, 16419, Republic of Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Sungkyungkwan University, 16419, Republic of Korea"}], "References": []}, {"ArticleId": 95638707, "Title": "LSTM and Statistical Learning for Dynamic Inferential Modeling with Applications to a 660MW Boiler", "Abstract": "Statistical learning methods have been widely studied and practiced in the past for inferential modeling. In recent years, deep learning methods have been implemented for inferential sensor modeling. As a popular deep learning model, the long short-term memory (LSTM) network is capable of handling data nonlinearity and dynamics and is therefore applied for dynamic inferential modeling. In this paper, we analyze and compare LSTM with other statistical learning methods for the dynamic NOx emission prediction of a 660 MW industrial boiler. Support vector regression (SVR), partial least squares (PLS), and Least absolute shrinkage and selection operator (Lasso) with embedded dynamics are compared with LSTM for dynamic inferential modeling. The experimental results indicate that SVR, PLS, and Lasso outperform LSTM. By disabling the LSTM gates to realize a simple memory structure, the LSTM performance is signifcantly improved. The main goal of the paper is to demonstrate that a deep neural network that is effective in other domains requires close scrutiny and detailed study to show its superiority in process applications.", "Keywords": "Dynamic inferential modeling ; LSTM ; Statistical learning ; Dynamic modeling", "DOI": "10.1016/j.ifacol.2022.07.509", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Jicheng Li", "Affiliation": "School of Data Science, City University of Hong Kong, Kowloon, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Coal Combustion, School of Energy and Power Engineering, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science and Hong Kong Institute for Data Science, City University of Hong Kong, Kowloon, Hong Kong"}], "References": [{"Title": "Fast multi-language LSTM-based online handwriting recognition", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "2", "Page": "89", "JournalTitle": "International Journal on Document Analysis and Recognition"}, {"Title": "Deep Learning and System Identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "1175", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 95638715, "Title": "Optimal control and dynamic modulation of the ATPase gene expression for enforced ATP wasting in batch fermentations", "Abstract": "Competitive biotechnological processes need to operate over various conditions and adapt to changing economic contexts. Dynamic ATP turnover allows trading off declines in biomass formation and volumetric productivity for enhancements of product yields in fermentations where the product pathway is linked to ATP synthesis. To facilitate its practical implementation, we propose to dynamically manipulate the cellular ATP turnover by putting the ATPase enzyme, which hydrolyzes ATP into ADP, under the control of an optogenetic gene expression system. This allows achieving dynamic control of the ATP wasting online via a tunable external input. While light as control input is promising because it is easily tunable, generally non-invasive/non-toxic, and more affordable than classical chemical inducers, it makes the overall control task challenging. Thus, we derive an expanded version of dynamic enzyme-cost flux balance analysis that takes into account the dynamics of the optogenetic actuator. We then formulate a suitable optimal control problem to find optimal inputs for achieving the desired process performance. We test our approach in simulations using the batch anaerobic lactate fermentation of glucose by Escherichia coli as a case study.", "Keywords": "ATP wasting ; ATPase ; optimal control ; gene expression ; optogenetics ; batch fermentation", "DOI": "10.1016/j.ifacol.2022.07.440", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>s", "Affiliation": "Control and Cyber-Physical Systems Laboratory, TU Darmstadt, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory for System Theory and Automatic Control, <PERSON> University Magdeburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratory for System Theory and Automatic Control, <PERSON> University Magdeburg, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Analysis and Redesign of Biological Networks, Max Planck Institute for Dynamics of Complex Technical Systems, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Analysis and Redesign of Biological Networks, Max Planck Institute for Dynamics of Complex Technical Systems, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Control and Cyber-Physical Systems Laboratory, TU Darmstadt, Germany"}], "References": [{"Title": "Adaptive predictive control of bioprocesses with constraint-based modeling and estimation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "106744", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 95638718, "Title": "A color channel multiplexing approach for robust discrete wavelet transform based image watermarking", "Abstract": "<p>Copyright protection of digital images has become a consequential issue in recent years. To prevent a breach of copyright for a color image, this article presents a novel discrete wavelet transform (DWT) based robust image watermarking method with a color channel multiplexing approach. In this proposed method, the original color image is separated into red, green, and blue color channels. One of these color channels is copied and a set of four channels is obtained. Then these channels are combined to obtain a new gray-level cover image. 2-level DWT is performed on the cover image. Arnold scrambled binary watermark image i embedded into the low-frequency sub-band of the cover image. 2-level inverse DWT is performed to produce a gray-level watermarked image. Then a demultiplexing process is performed by separating the gray-level watermarked image into four color channels. The average values of the two copied color channels are calculated and then converted into a single-color channel. Finally, the watermarked color image is get by merging three different color channels. To approve the performance of the presented method, various experimental tests have been performed and a benchmarking analysis has been conducted. The results show that the proposed method is robust against non-linear and linear attacks while protecting watermarking imperceptibility.</p>", "Keywords": "color image watermarking;copyright protection;discrete wavelet transform (DWT);multiplexing", "DOI": "10.1002/cpe.7255", "PubYear": 2022, "Volume": "34", "Issue": "25", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Technology Department Kayseri University  Kayseri Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department Erciyes University  Kayseri Turkey"}], "References": [{"Title": "An improved SVD-based blind color image watermarking algorithm with mixed modulation incorporated", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "519", "Issue": "", "Page": "161", "JournalTitle": "Information Sciences"}, {"Title": "An improved robust image watermarking by using different embedding strengths", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "Page": "12041", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Digital Image Watermarking Techniques: A Review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "110", "JournalTitle": "Information"}, {"Title": "A blind robust color image watermarking method based on discrete wavelet transform and discrete cosine transform using grayscale watermark image", "Authors": "Ertugrul Gul", "PubYear": 2022, "Volume": "34", "Issue": "22", "Page": "e6884", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "A novel Frei‐Chen based fragile watermarking method for authentication of an image", "Authors": "<PERSON><PERSON><PERSON>; Serkan <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "22", "Page": "e6897", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 95638723, "Title": "Lexicographic Optimization Control Method Based Phase Recognition for Multiphase Batch Polymerization Reaction", "Abstract": "To solve the problem that the correlation of variables varies with phase in multiphase batch polymerization reaction, this paper proposes an optimization method for lexicographic order switching based on phase recognition. This method uses kernel principal component analysis to analyze the correlation of each sampling time in a sliding time window, and analyze the phase of each batch of historical data. According to the analysis results, each batch is divided into individual phases, and the sub-phase data is used to identify the phase of real-time production. This method sets the corresponding lexicographic control target according to the characteristics of each phase of the process. According to the lexicographical order of real-time phase location, the optimal operation variable trajectory is obtained, so as to achieve the best optimization effect in phase, and ensure the safe production process and continuous and stable product quality.", "Keywords": "Multiphase batch process ; Kernel Principle Component Analysis(KPCA) ; Uneven-length ; Phase Partition ; Lexicographic Optimization", "DOI": "10.1016/j.ifacol.2022.07.516", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electrical Engineering and Control Science, Nanjing Tech University, Nanjing Jiangsu, 211816, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Control Science, Nanjing Tech University, Nanjing Jiangsu, 211816, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Ningbo Artificial Intelligence Institute, Shanghai Jiao Tong University, Ningbo Zhejiang, 315012, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Intelligent Manufacturing, Nanjing Tech University, Nanjing Jiangsu,211816, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Electrical Engineering and Control Science, Nanjing Tech University, Nanjing Jiangsu, 211816, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Control Science and Engineering, Zhejiang University, Hangzhou Zhejiang, 310027, China"}], "References": []}, {"ArticleId": 95638740, "Title": "Extent-based reconstruction of the inlet composition matrix in process systems with feed variability and unknown reaction dynamics", "Abstract": "Extent-based representation is a powerful tool for reaction systems since it decouples the reaction related dynamics and inlet flow dynamics. However it assumes that the inlet flow composition is exactly known. In this paper, we present the design of an estimation procedure to partially reconstruct the approximate inlet composition matrix based on the extent representation of the process. This is especially necessary when process systems have recycles streams or the inlet composition matrix contains traces of other species. The estimation is done by transforming the measured moles to the extent basis based on a nominal but incorrect inlet composition. The use of this incorrect composition will reveal the difference between the true process and the nominal model. The error between these two can be exploited to be cast a constrained linear optimization problem to estimate the uncertainty in the inlet composition. The correction of the inlet composition matrix will be crucial in obtaining an appropriate extent-based representation that can be used for control, and state and parameter estimation approaches. The use of extents is motivated because it allows to represent the system with a set of decoupled dynamics, avoiding the necessity of the reaction dynamics in the estimation procedure. The techniques developed in this paper are ultimately tested in a simple case study of a CSTR with a recycle stream from a flash evaporator.", "Keywords": "extent representation ; parameter estimation ; model update ; uncertainty", "DOI": "10.1016/j.ifacol.2022.07.457", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON>", "Affiliation": "Eindhoven University of Technology, Department of Electrical Engineering"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Eindhoven University of Technology, Department of Electrical Engineering"}], "References": [{"Title": "Revisiting the concept of extents for chemical reaction systems using an enthalpy balance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "106652", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 95638750, "Title": "Mathematical Modelling of Membrane CO2 Capture for Blue Hydrogen Production", "Abstract": "Membrane gas separation is a promising technology for carbon capture due to its high energy efficiency, easy scale-up and simple operation. In particular, carbon capture followed by syngas production becomes important as hydrogen is deemed to be a prominent candidate to substitute fossil fuels. Therefore there is the need of understanding the effects of process operation and design variables on hydrogen production and CO<sub>2</sub> separation efficiency in membrane gas capture for blue hydrogen production. This work presents a predictive model for a hollow fibre membrane module that can be applied to multicomponent gas separation. The developed model accounts for concentration polarisation in a multicomponent system, caused by limited mass transfer near selective membrane barriers, as well as axial variations in gas flowrates and pressures. A number of simulations are undertaken by varying membrane module properties and feed flowrates in order to investigate their effects on product quantity and quality. Finally the model is validated against experimental data available in the literature.", "Keywords": "CO<sub>2</sub> capture ; blue hydrogen production ; membrane gas separation ; hollow fibre modules ; mathematical modelling", "DOI": "10.1016/j.ifacol.2022.07.461", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Chonnam National University, Gwangju, Republic of Korea"}], "References": []}, {"ArticleId": ********, "Title": "Direct Discretized Kernel Identification for Continuous Agglomeration Processes", "Abstract": "Continuous particle agglomeration processes are important size-enlargement unit operations applied in the food, pharmaceutical and agricultural industry. For the improvement of these processes predictive mathematical models are of utmost importance. A widely applied modeling framework is the population balance equation, where the agglomeration kinetics are described by the so-called agglomeration kernel. The identification of functions describing these kinetics has turned out to be a challenging task. Therefore, this article deals with identifying such a kernel function by minimizing the L2-residual between experimentally obtained particle size distributions and simulations. The application of a stochastic gradient descent algorithm with automatic differentiation for minimization allows for the direct identification of the high-dimensional matrix representing the discretized kernel function. The comparison between the simulated and measured size distribution shows that the identified kernel is able to accurately describe the evolution of the particle size distribution. The algorithm presented in this contribution can be applied to a variety of similar processes and the identified kernels can be used in process optimization and automation applications.", "Keywords": "Process modeling ; identification ; Fluidized bed processes ; Agglomeration processes ; Agglomeration kernel ; Optimization based identification", "DOI": "10.1016/j.ifacol.2022.07.454", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Automation/Modelling, <PERSON> University Magdeburg, Universitdtsplatz 2, 39106 Magdeburg, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Automation/Modelling, <PERSON> University Magdeburg, Universitdtsplatz 2, 39106 Magdeburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Engineering Mathematics, Magdeburg-Stendal University of Applied Sciences, Breitscheidstrafie 2, 39114 Magdeburg, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National Research University Moscow Power Engineering Institute, Krasnokazarmennaya Ulitsa, 14, 111250 Moscow, Russia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Automation/Modelling, <PERSON> University Magdeburg, Universitdtsplatz 2, 39106 Magdeburg, Germany;Process Synthesis and Dynamics, Max Planck Institute for Dynamics of Complex Technical Systems, Sandtorstrasse 1, 39106 Magdeburg, Germany"}], "References": [{"Title": "Hybrid machine learning assisted modelling framework for particle processes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106916", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 95638759, "Title": "Systematic Estimation of Noise Statistics for Nonlinear Kalman Filters", "Abstract": "We propose two new systematic and easy-to-implement online tuning strategies for nonlinear Kalman filters with low computational cost. The tuning strategies assume the process and measurement noise are due to parametric uncertainty. We assume n<sub>θ</sub> uncertain parameters which are translated into noise statistics by either i) generalized unscented transformation with 2 n<sub>θ</sub> extra online model evaluations at every time step or ii) latin hypercube sampling, where the user sets the number of samples. Both approaches are distribution free, hence, the tuning strategies work for all kind of distributions. In the case study, it was found that the two proposed tuning strategies outperform the standard approach of fixed, diagonal noise matrices. In the case study, we further found that tuning based on the generalized unscented transformation seems to be more consistent than the method based on latin hypercube sampling for the same online computational cost. In addition, a Monte Carlo based tuning with modal noise adjustment is tested with promising performance. The modal noise adjustment is interesting as we can estimate the most likely point value of the noise (the mode of the noise distribution) and add this term to the state- and measurement equations at every time step.", "Keywords": "Estimation ; Filtering ; Generalized Unscented Transformation", "DOI": "10.1016/j.ifacol.2022.07.416", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Norwegian University of Science and Technology (NTNU), Sem Sælandsvei 4, Kjemiblokk 5, 7491 Trondheim, Norway"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering, Norwegian University of Science and Technology (NTNU), Sem Sælandsvei 4, Kjemiblokk 5, 7491 Trondheim, Norway"}], "References": []}, {"ArticleId": 95638761, "Title": "Model Identification and Model Predictive Control of Biopharmaceutical and Biomedical Systems", "Abstract": "This paper demonstrates the use of model predictive control (MPC) formulations for uncertain time-varying biopharmaceutical and biomedical systems implemented using measured data without prior knowledge of an accurate model. Furthermore, we demonstrate how prior knowledge can be incorporated in the identification of the model either through constraints or as regularization of the system identification procedure. We demonstrate the use of system identification to develop a model of the fed-batch Chinese hamster ovary mammalian cell bioreactor process and the implementation of model-based control to maximize therapeutic product yields. We also use a time-varying nonlinear biomedical system to demonstrate improvements due to incorporating prior information in the learning of the models and reidentification of the models when prediction accuracy deteriorates. We propose a new partial least squares algorithm that incorporates regularization from prior knowledge and can handle missing data in the independent covariates. Simulation case studies involving a biopharmaceutical production process and automated drug delivery demonstrate the capabilities of the proposed techniques.", "Keywords": "model identification ; model predictive control ; regularized latent variables models ; biopharmaceutical process ; biomedical systems", "DOI": "10.1016/j.ifacol.2022.07.421", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Biological Engineering, Illinois Institute of Technology, Chicago, IL 60616 USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Illinois Institute of Technology, Chicago, IL 60616 USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemical and Biological Engineering, Illinois Institute of Technology, Chicago, IL 60616 USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemical and Biological Engineering, Illinois Institute of Technology, Chicago, IL 60616 USA;Department of Biomedical Engineering, Illinois Institute of Technology, Chicago, IL 60616 USA"}], "References": [{"Title": "Dynamic latent variable regression for inferential sensor modeling and monitoring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "106809", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Adaptive-learning model predictive control for complex physiological systems: Automated insulin delivery in diabetes", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "", "Page": "1", "JournalTitle": "Annual Reviews in Control"}]}, {"ArticleId": 95638763, "Title": "Incremental Model Identification of Bio-processes from Data: Application to Microbial Production of Hyaluronic Acid", "Abstract": "The development of reliable kinetic models of bioprocesses from data is a challenging task. In this work, a systematic approach to develop a kinetic model of bioprocess involving single biomass from concentration data is proposed without imposing any kinetic model a priori. The proposed incremental model identification approach decomposes the model-building task into a set of sub-tasks such as determining the yield coefficients and maintenance coefficient, specific growth rate structure identification, and parameter estimation. It is shown that the proposed approach allows identifying the mechanism of product formation. The proposed approach is applied to the microbial production of Hyaluronic acid (HA), an important biopolymer, using a recombinant Lactococcus lactis MKG6. An unstructured kinetic model is developed for the HA production from data. It is shown that HA production is a growth-associated process. Further, the specific growth rate of HA production is identified from a set of rate candidates. It is revealed that the specific growth rate in the HA production follows the non-competitive HA inhibition model. The parameters obtained by the incremental identification are further refined to obtain statistically optimal estimates using the simultaneous model identification. Validation of the identified kinetic model of HA production on new experimental data shows that the proposed approach leads to a reliable kinetic model with the optimal parameter estimates.", "Keywords": "Kinetic models ; Incremental identification ; Hyaluronic acid ; Bioprocesses ; Unstructured kinetics", "DOI": "10.1016/j.ifacol.2022.07.511", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Kamakshi C", "Affiliation": "Department of Biotechnology, Bhupat and <PERSON><PERSON><PERSON> school of Biosciencies;Prospective CoE for Network Systems Learning, Control, and Evolution, Indian Institute of Technology Madras, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biotechnology, Bhupat and Jyoti <PERSON> school of Biosciencies"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biotechnology, Bhupat and <PERSON><PERSON><PERSON> school of Biosciencies;Prospective CoE for Network Systems Learning, Control, and Evolution, Indian Institute of Technology Madras, Chennai, India"}], "References": []}, {"ArticleId": 95638765, "Title": "Data-driven Linear Predictor based on Maximum Likelihood Nonnegative Matrix Decomposition for Batch Cultures of Hybridoma Cells", "Abstract": "This paper presents an original design of low-rank linear predictors of nonlinear process state variables based on nonnegative matrix decomposition (NMD). Therefore, this predictor is data-driven and does not require an accurate model description of the process. In addition, measurement errors are considered, conferring maximum likelihood (ML) properties to the estimator and resulting in a maximum likelihood nonnegative matrix decomposition (MLNMD) formulation. The latter is validated in simulation with a model developed by the authors, describing monoclonal antibody (MAb) production from sequential batch hybridoma cell cultures that are further validated with real-life experimental data. To this end, two available experimental data sets are used for direct and cross-validation, highlighting the good predictive properties of the method.", "Keywords": "nonnegative matrix decomposition ; maximum likelihood ; measurements errors ; hybridoma cells cultures", "DOI": "10.1016/j.ifacol.2022.07.559", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Systems, Estimation, Control and Optimization Group (SECO), University of Mons, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Systems, Estimation, Control and Optimization Group (SECO), University of Mons, Belgium"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Systems, Estimation, Control and Optimization Group (SECO), University of Mons, Belgium"}], "References": []}, {"ArticleId": 95638768, "Title": "Optimal control of renewable biohydrogen production: A switched system approach*", "Abstract": "Biohydrogen produced from microorganisms such as cyanobacteria is a promising low cost, sustainable and environmentally friendly energy source. Recent studies have shown that high biohydrogen yield can be obtained from Cyanothece sp. ATCC 51142 in a fed-batch reactor. This system has been accurately described with a modified Droop model that can be used for optimization studies. Searching for the optimal operating conditions and the switching time from batch to fed-batch operation, such that the biohydrogen production is maximized, leads to a challenging singular optimal control problem. In this study, a novel reformulation based on the theory of switched systems and time-scaling transformation is proposed to address the switching of the operating modes and the optimal control structure. Solutions are found by solving an embedded optimal control problem that can be solved efficiently as a nonlinear programming problem. No mesh refinement is required to capture the switching times. Smooth optimal control profiles and clear switching structures that maximize the biohydrogen yield were found for two types of control parametrization.", "Keywords": "Singular optimal control ; switched systems ; biohydrogen production ; fed-batch reactors", "DOI": "10.1016/j.ifacol.2022.07.459", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, University of Waterloo, N2L 3G1, Waterloo, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>l", "Affiliation": "Department of Chemical Engineering, University of Waterloo, N2L 3G1, Waterloo, Canada"}], "References": [{"Title": "An indirect approach for singular optimal control problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106923", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 95638774, "Title": "Nonlinear Model Predictive Control of Post-combustion CO2 Capture Process for Flexible Operation", "Abstract": "A large amount of energy requirement for solvent regeneration is a major barrier to the widespread adoption of amine-based post-combustion CO<sub>2</sub> capture (PCC). Flexible operation is one of the ways to lower the energy penalty by responding to changes in economic factors like the energy price. However, for effective implementation of flexible operation strategies, it is necessary to identify the most economic operating condition under various potential scenarios and to establish an appropriate control strategy to operate the process. As flexible operation will inherently involve a large operating envelope, we investigate the use of nonlinear model predictive control (NMPC) technology. To circumvent the problem of solving a large-scale nonlinear programming problem online, a simpler NARX model is identified and used. With the NARX model, an offset-free NMPC is designed and simulated under various dynamic scenarios. The developed NARX-based NMPC shows satisfactory control performance, stabilizing the CO<sub>2</sub> capture rate faster than LMPC by 60-100 min.", "Keywords": "Post-combustion CO<sub>2</sub> capture ; Dynamic simulation ; System identification ; Nonlinear model predictive control", "DOI": "10.1016/j.ifacol.2022.07.482", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, Korea Advanced Institute of Science and Technology, Daejeon, 305-701, Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, Korea Advanced Institute of Science and Technology, Daejeon, 305-701, Korea"}], "References": [{"Title": "Dynamic analysis and linear model predictive control for operational flexibility of post-combustion CO2 capture processes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106968", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 95638778, "Title": "Bilevel programming as a means of infinite weighting in regression problems", "Abstract": "Linear regression is concerned about fitting a model to a set of data. The weighted least squares method is a standard tool for performing linear regression. In this paper, we focus on the case when some of the samples are given priority over others. The residuals for these samples should be given an infinite weighting compared to other samples. However, due to numerical limitations, a weight which is finite but sufficiently large must be chosen instead. We suggest an alternative approach that in practice allows infinite weighting. This is achieved by reformulating the regression optimization problem as a bilevel program. The method is illustrated in a numerical example study. The example shows that, without needing to determine a weighting factor, the proposed method yields the same solution, up to numerical precision, as to the one obtained by using a large weight.", "Keywords": "Bilevel programming ; linear regression ; infinite weighting ; sample prioritization ; daily production optimization", "DOI": "10.1016/j.ifacol.2022.07.551", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering Cybernetics, Norwegian University of Science and Technology, Trondheim, Norway"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Engineering Cybernetics, Norwegian University of Science and Technology, Trondheim, Norway"}], "References": []}, {"ArticleId": 95638779, "Title": "Teaching Sustainable Food Systems Engineering in the times of a Pandemic", "Abstract": "Sustainable food systems embrace a range of aspects such as security of the supply of food, health, safety, affordability, quality, a strong food industry in terms of jobs and growth, and environmental sustainability in terms of issues such as climate change, biodiversity, water and soil quality. In recent years, quantitative modelling and engineering tools are being developed to better cope with these challenges at the level of all stakeholders involved, including industry, government and regulatory agencies. For example, Life Cycle Assessment (LCA) and related concepts (such as carbon or water footprints) are being exploited within a multi-objective food chain optimization framework. A well-balanced pan-European MSc programme “Sustainable Food Systems Engineering, Technology and Business” (FOOD4S ‘ food force ’) 2020-2026 (2029), with a specific integrated and international outlook, fills an increasing need in the transfer of knowledge, experience and standards to developing countries in particular, while contributing to the necessary transformation towards social, environmental, and economic sustainability in food systems. The purpose of this paper is to address the nature of the challenges facing agriculture and food systems, to provide knowledge about the threats and to indicate possibilities of knowledge transfer by education and research.", "Keywords": "sustainable food systems ; food engineering ; food processing ; modelling ; control in agriculture ; biosystems ; life cycle assessment ; risk assessment ; teaching curricula development ; higher education", "DOI": "10.1016/j.ifacol.2022.07.527", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Monika E. Polańska", "Affiliation": "BioTeC+ Chemical & Biochemical Process Technology & Control, KU Leuven/Campus Gent, Belgium"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "BioTeC+ Chemical & Biochemical Process Technology & Control, KU Leuven/Campus Gent, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "BioTeC+ Chemical & Biochemical Process Technology & Control, KU Leuven/Campus Gent, Belgium"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Food Science and Environmental Health, University College Dublin, Dublin 6, Ireland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Food Sciences and Nutrition, Faculty of Health Sciences, University of Malta, 2080 Msida, Malta"}, {"AuthorId": 6, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "BioTeC+ Chemical & Biochemical Process Technology & Control, KU Leuven/Campus Gent, Belgium"}], "References": []}, {"ArticleId": 95638782, "Title": "Modeling the Carbon Cycle Dynamics and the Greenhouse Effect", "Abstract": "A carbon cycle model is proposed that predicts changes in the atmospheric CO<sub>2</sub> concentration, and provides a global temperature estimate from an empirical correlation of the two variables. The model is validated by simulating the anthropogenic carbon emissions and deforestation since the industrial revolution, and comparing the predicted and measured atmospheric CO<sub>2</sub> concentration and global temperature data. The temperature data are also compared with those predicted by a greenhouse effect model based on the effective emission temperature hypothesis. The result suggests that radiative forcing by CO<sub>2</sub> alone can account for only about half of the measured global warming. The model requires further elaboration for the other half, in order to be applicable to simulation of potential climate control.", "Keywords": "carbon cycle dynamics ; greenhouse effect ; global warming ; carbon neutrality ; climate control", "DOI": "10.1016/j.ifacol.2022.07.480", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Division of Chemical Engineering, Jeonbuk National University, Jeonju, 54896, S. Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Biomolecular Engineering, University of California, Los Angeles, CA 90095, USA"}], "References": [{"Title": "On the carbon cycle impact of combustion of harvested plant biomass vs. fossil carbon resources", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106942", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 95638783, "Title": "Quantum Computing and Resilient Design Perspectives for Cybersecurity of Feedback Systems", "Abstract": "Cybersecurity of control systems is an important issue in next-generation manufacturing that can impact both operational objectives (safety and performance) as well as process designs (via hazard analysis). Cyberattacks differ from faults in that they can be coordinated efforts to exploit system vulnerabilities to create otherwise unlikely hazard scenarios. Because coordination and targeted process manipulation can be characteristics of attacks, some of the tactics previously analyzed in our group from a control system cybersecurity perspective have incorporated randomness to attempt to thwart attacks. The underlying assumption for the generation of this randomness has been that it can be achieved on a classical computer; however, quantum computers can also create random behavior in the results of computations. This work explores how errors in quantum hardware that can create non-deterministic outputs from quantum computers interact with control system cybersecurity. These studies serve as a reminder of the need to incorporate cybersecurity considerations at the process design stage.", "Keywords": "quantum computing ; control ; resilience ; cybersecurity", "DOI": "10.1016/j.ifacol.2022.07.526", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}], "References": [{"Title": "Quantum computing based hybrid solution strategies for large-scale discrete-continuous optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "106630", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 95638788, "Title": "Challenges and Opportunities for Next-Generation Manufacturing in Space", "Abstract": "With commercial space travel now a reality, the idea that people might spend time on other planets in the future seems to have greater potential. To make this possible, however, there needs to be flexible means for manufacturing in space to enable tooling or resources to be created when needed to handle unexpected situations. Next-generation manufacturing paradigms offer significant potential for the kind of flexibility that might be needed; however, they can result in increases in computation time compared to traditional control methods that could make many of the computing resources already available on earth attractive for use. Furthermore, resilience is a significant focus of next-generation manufacturing strategies, and one way to enable resilience for space manufacturing would be to have backup controllers available on earth. These types of considerations raise questions about remote control and monitoring, as well as privacy of the data involved in such practices, that must be considered. This work provides a perspective on several topics tied to remote control and monitoring for manufacturing in space.", "Keywords": "space manufacturing ; next-generation manufacturing ; model predictive control ; interpretability ; cryptography", "DOI": "10.1016/j.ifacol.2022.07.569", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Detroit, MI 48202 USA"}], "References": []}, {"ArticleId": 95638794, "Title": "Stochastic MPC For Optimal Operation of Hydropower Station Under Uncertainty", "Abstract": "The operational condition at the Dalsfoss power station is complicated due to many requirements related to environmental regulations and safety constraints such as the seasonally varying water level requirement at the reservoir. However, the operation becomes more difficult due to uncertainties in the system. In this paper, at first a certainty equivalent MPC is applied to the uncertain hydro power system and it has been shown that its robustness property is poor. Secondly, to prevent the constraint violations due to the uncertainties in the system, two measures are taken. One measure is to introduce a safety margin for the constraints and further design a certainty equivalent MPC. The other measure is to implement a multi-stage MPC for robust constraint satisfaction. Two types of multi-stage MPC are considered in this paper. The first employs all of the possible 50 scenarios of the uncertainty of an input disturbance variable, and the latter generates and uses three synthetic scenarios to approximate all of the possible 50 scenarios. All of the simulation results are compared for their robust performances and computational time.", "Keywords": "Model predictive control ; Multi stage model predictive control ; Stochastic analysis ; uncertainty ; food management", "DOI": "10.1016/j.ifacol.2022.07.437", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical engineering, Information Technology and Cybernetics, University of South-Eastern Norway, Porsgrunn, Norway"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical engineering, Information Technology and Cybernetics, University of South-Eastern Norway, Porsgrunn, Norway"}], "References": []}, {"ArticleId": ********, "Title": "Artificial neural networks integrated mixed integer mathematical model for multi-fleet heterogeneous time-dependent cash in transit problem with time windows", "Abstract": "<p>The cash in transit (CIT) problem is a version of the vehicle routing problem (VRP), which deals with the planning of money distribution from the depot(s) to the automated teller machines (ATMs) safely and quickly. This study investigates a novel CIT problem, which is a variant of time-dependent VRP with time windows. To establish a more realistic approach to the time-dependent CIT problem, vehicle speed varying according to traffic density is considered. The problem is formulated as a mixed-integer mathematical model. Artificial neural networks (ANNs) are used to forecast the money demand for each ATM. For this purpose, key factors are defined, and a formulation is proposed to determine the money deposited to and withdrawn into ATMs. The mathematical model is run for different scenarios, and optimum routes are obtained.</p>", "Keywords": "Artificial neural networks; Cash in transit; Mixed integer linear programming; Time-dependent vehicle routing problem", "DOI": "10.1007/s00521-022-07659-7", "PubYear": 2022, "Volume": "34", "Issue": "24", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Ertuğrul Ayyıldız", "Affiliation": "Department of Industrial Engineering, Karadeniz Technical University, Trabzon, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Yıldız Technical University, Istanbul, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Yıldız Technical University, Istanbul, Turkey"}, {"AuthorId": 4, "Name": "Coşkun Özkan", "Affiliation": "Department of Industrial Engineering, Yıldız Technical University, Istanbul, Turkey"}], "References": [{"Title": "Time-dependent vehicle routing problem with time windows of city logistics with a congestion avoidance approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "188", "Issue": "", "Page": "104813", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A new game-theoretical multi-objective evolutionary approach for cash-in-transit vehicle routing problem with time windows (A Real life Case)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106378", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-objective periodic cash transportation problem with path dissimilarity and arrival time variation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "114015", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An adapted multi-objective genetic algorithm for solving the cash in transit vehicle routing problem with vulnerability estimation for risk quantification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "103964", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": ********, "Title": "OneModel: an open-source SBML modeling tool focused on accessibility, simplicity and modularity", "Abstract": "With the advent of the Systems Biology Markup Language (SBML), a large community of SBML-compliant tools has been created. However, these tools can only be used to their full potential by expert users with advanced programming knowledge. OneModel is an open-source text-based tool for defining SBML models in a modular and incremental way that minimizes the user&#x27;s programming knowledge requirements. It is focused on accessibility, simplicity, and modularity. OneModel syntax allows the user to define models based on chemical (and pseudo-chemical) reactions, differential equations, and algebraic equations. OneModel is written in Python, and it provides two interfaces: a command-line interface for expert-users, and a graphical user interface for non-expert users. Here, we show two OneModel syntax use case scenarios for modeling an antithetic controller and then integrating it into a host-aware model, which is freely distributed with OneModel.", "Keywords": "systems biology ; synthetic biology ; mathematical models ; SBML ; Python", "DOI": "10.1016/j.ifacol.2022.07.432", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "F.N. Santos-Navarro", "Affiliation": "Synthetic Biology and Biosystems Control Lab, I.U. de Automática e Informática Industrial (ai2), Universitat Politècnica de Valencia, 46022, Camino de Vera S/N, Valencia, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Synthetic Biology and Biosystems Control Lab, I.U. de Automática e Informática Industrial (ai2), Universitat Politècnica de Valencia, 46022, Camino de Vera S/N, Valencia, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Synthetic Biology and Biosystems Control Lab, I.U. de Automática e Informática Industrial (ai2), Universitat Politècnica de Valencia, 46022, Camino de Vera S/N, Valencia, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Synthetic Biology and Biosystems Control Lab, I.U. de Automática e Informática Industrial (ai2), Universitat Politècnica de Valencia, 46022, Camino de Vera S/N, Valencia, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Synthetic Biology and Biosystems Control Lab, I.U. de Automática e Informática Industrial (ai2), Universitat Politècnica de Valencia, 46022, Camino de Vera S/N, Valencia, Spain"}], "References": [{"Title": "SBML2Modelica: integrating biochemical models within open-standard simulation ecosystems", "Authors": "F Maggioli; <PERSON>; E Tronci", "PubYear": 2020, "Volume": "36", "Issue": "7", "Page": "2165", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 95638825, "Title": "Molecular Reconstruction of Naphtha based on Physical Information Neural Network", "Abstract": "A molecular reconstruction method based on physical information neural network is proposed for predicting the molecular composition of naphtha. By embedding physical information utilized in typical molecular reconstruction methods, such as mixing rules, into the loss function of the neural network, the model tends to converge to the state conforming to physical rules in training stage. The neural network model obtained by the method contains certain physical information, which can improve the generalization ability of the model. The results show that the prediction performance and application range of the proposed method are better than those of the typical ANN-based molecular reconstruction method.", "Keywords": "Artificial Neural Network ; Mixing Rules ; Generalization Ability", "DOI": "10.1016/j.ifacol.2022.07.442", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Fang<PERSON> Ma", "Affiliation": "College of Chemical Engineering, Beijing University of Chemical Technology, 100029 Beijing, China;Center of process monitoring and data analysis, Wuxi Research Institute of Applied Technologies, Tsinghua University, 214072 Wuxi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemical Engineering, Beijing University of Chemical Technology, 100029 Beijing, China"}, {"AuthorId": 3, "Name": "Chengyu Han", "Affiliation": "College of Chemical Engineering, Beijing University of Chemical Technology, 100029 Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemical Engineering, Beijing University of Chemical Technology, 100029 Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Chemical Engineering, Beijing University of Chemical Technology, 100029 Beijing, China"}], "References": []}, {"ArticleId": 95638826, "Title": "Development of Wiener-Hammerstein Models Parameterized using Orthonormal Basis Filters and Deep Neural Network", "Abstract": "Many chemical or biochemical processes exhibit strongly nonlinear dynamic behavior in the desired region of operations. To develop effective monitoring and control schemes for such systems, it is necessary to develop a reliable model that captures dynamics as well as the steady-state behavior over a wide range of operations. In this work, it is proposed to develop a block-oriented Wiener-<PERSON><PERSON> model parameterized using generalized orthonormal basis filters and deep neural network (GOBF DNN). A two-step procedure is developed to select the generalized orthonormal basis filters (GOBF) pole locations and estimate the deep neural network (DNN) parameters. The e¢cacy of the proposed modeling strategy is demonstrated using the simulation study on a benchmark continuously operated fomenter system. The proposed GOBF DNN model is able to capture the dynamic and steady-state behavior of the plant over a wide range of operations. Comparison of performances based on the dynamic as well as the steady-state indices clearly underscores the advantages of using a DNN over a shallow neural net and a NARX model developed using DNN.", "Keywords": "Block Oriented Model ; Weiner-<PERSON>stein Model ; Deep Neural Network ; Generalized Orthonormal Basis Filters", "DOI": "10.1016/j.ifacol.2022.07.427", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Indian Institute of Technology Bombay, Mumbai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Systems and Control Engineering, Indian Institute of Technology Bombay, Mumbai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Chemical Engineering, Indian Institute of Technology Bombay, Mumbai, India"}], "References": [{"Title": "An Introductory Review of Deep Learning for Prediction Models With Big Data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "", "Page": "4", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Learning physics based models of Lithium-ion Batteries", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "3", "Page": "97", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 95638835, "Title": "Neural network model for atmospheric residue desulfurization conversion prediction using real plant data", "Abstract": "Atmospheric residue desulfurization (RDS) is used to treat the invaluable atmospheric residue from crude distillation column before it is sent to the fluid catalytic cracking (FCC) process. RDS is operated by increasing the operation temperature of catalysts to keep the desirable conversion as the catalyst aging progresses. To use the catalyst as long as possible while satisfying the criterion on the impurities in the RDS product, we need to predict the conversion based on the operation histories. In this study, we propose neural network models for predicting removal amount of the impurities in RDS. As the temperature, hydrogen flow rate, and impurities feed flow rates are selected as the features for neural network models. In addition, an approximated aging factor for the catalyst is considered with the cumulative amount of treated impurities and trainable parameters. The neural networks are trained in a moving horizon manner and tested using the real plant data during 123 days. If we train the neural networks in a general way, the trained model shows unrealistic results that are physically and chemically unexpected. When we increase the temperature with the other factors fixed, the catalyst conversion predicted by the trained neural network decreases. This absurd prediction is because the model is trained using the data where the temperature increases when the conversion is low with aging catalyst. To address this issue, we use a clipping method that the weights of neural networks are restricted to positive values. This strategy always ensures the increasing (decreasing) tendencies of the removal amount with respective to the increase (decrease) of the temperature and hydrogen flow rate.", "Keywords": "Neural network ; Desulfurization ; Conversion Prediction ; Atmospheric residue", "DOI": "10.1016/j.ifacol.2022.07.443", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Kwangwoon University, 20 Kwangwoon-ro, Nowon-gu, Seoul, 01897, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Kwangwoon University, 20 Kwangwoon-ro, Nowon-gu, Seoul, 01897, Republic of Korea"}, {"AuthorId": 3, "Name": "Gyeonggwan Jeon", "Affiliation": "Department of Chemical Engineering, Kwangwoon University, 20 Kwangwoon-ro, Nowon-gu, Seoul, 01897, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Kwangwoon University, 20 Kwangwoon-ro, Nowon-gu, Seoul, 01897, Republic of Korea;Corresponding author"}], "References": []}, {"ArticleId": 95638837, "Title": "Image hashing retrieval based on generative adversarial networks", "Abstract": "<p>To solve the current problem of low retrieval accuracy in deep hashing image retrieval, an improved generative adversarial network (GAN)-based image hashing retrieval method using supervised contrast learning (SconGAN) is proposed. The method augments training samples by introducing dense residual blocks and content fidelity items into the generative network to synthesize more diverse images with higher quality; moreover, it enhances the feature discrimination ability of the discriminative network by introducing pyramidal convolution and supervised contrast learning. Meanwhile, it improves the hashing code generation quality by introducing new pairwise similarity loss, semantic retention loss and quantization loss into the hashing network. In addition, discriminative networks and hashing networks share the core network structure to reduce resource consumption and improve training efficiency. Comprehensive experiments on the CIFAR-10 and NUS-WIDE benchmark datasets show that the proposed method greatly outperforms the comparison methods and obtains the best mean average precision (MAP).</p>", "Keywords": "Deep hashing; Generative adversarial network (GAN); Supervised contrast learning; Image retrieval; Pyramidal convolution", "DOI": "10.1007/s10489-022-03970-x", "PubYear": 2023, "Volume": "53", "Issue": "8", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Software, Nanyang Institute of Technology, Nanyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Software, Nanyang Institute of Technology, Nanyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Software, Nanyang Institute of Technology, Nanyang, China"}, {"AuthorId": 4, "Name": "Zechen Wu", "Affiliation": "College of Computer and Software, Nanyang Institute of Technology, Nanyang, China"}], "References": [{"Title": "Product quantization with dual codebooks for approximate nearest neighbor search", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "401", "Issue": "", "Page": "59", "JournalTitle": "Neurocomputing"}, {"Title": "Supervised deep hashing with a joint deep network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107368", "JournalTitle": "Pattern Recognition"}, {"Title": "Remote sensing image super-resolution using cascade generative adversarial nets", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "443", "Issue": "", "Page": "117", "JournalTitle": "Neurocomputing"}, {"Title": "Clustering-driven Deep Adversarial Hashing for scalable unsupervised cross-modal retrieval", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "459", "Issue": "", "Page": "152", "JournalTitle": "Neurocomputing"}, {"Title": "Atrous spatial pyramid convolution for object detection with encoder-decoder", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "464", "Issue": "", "Page": "107", "JournalTitle": "Neurocomputing"}, {"Title": "Supervised contrastive learning over prototype-label embeddings for network intrusion detection", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "79", "Issue": "", "Page": "200", "JournalTitle": "Information Fusion"}, {"Title": "Supervised discrete hashing for hamming space retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "154", "Issue": "", "Page": "16", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Deep Multi-Similarity Hashing with semantic-aware preservation for multi-label image retrieval", "Authors": "<PERSON><PERSON> Qin; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "117674", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 95638881, "Title": "Getting Along with Relational Databases", "Abstract": "", "Keywords": "", "DOI": "10.4000/jtei.3874", "PubYear": 2021, "Volume": "", "Issue": "14", "JournalId": 25101, "JournalTitle": "Journal of the Text Encoding Initiative", "ISSN": "", "EISSN": "2162-5603", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 95638894, "Title": "SSPCatcher: Learning to catch security patches", "Abstract": "<p>Timely patching (i.e., the act of applying code changes to a program source code) is paramount to safeguard users and maintainers against dire consequences of malicious attacks. In practice, patching is prioritized following the nature of the code change that is committed in the code repository. When such a change is labeled as being security-relevant, i.e., as fixing a vulnerability, maintainers rapidly spread the change, and users are notified about the need to update to a new version of the library or of the application. Unfortunately, oftentimes, some security-relevant changes go unnoticed as they represent silent fixes of vulnerabilities. In this paper, we propose SSPCatcher , a Co-Training-based approach to catch security patches (i.e., patches that address vulnerable code) as part of an automatic monitoring service of code repositories. Leveraging different classes of features, we empirically show that such automation is feasible and can yield a precision of over 80% in identifying security patches, with an unprecedented recall of over 80%. Beyond such a benchmarking with ground truth data which demonstrates an improvement over the state-of-the-art, we confirmed that SSPCatcher can help catch security patches that were not reported as such.</p>", "Keywords": "", "DOI": "10.1007/s10664-022-10168-9", "PubYear": 2022, "Volume": "27", "Issue": "6", "JournalId": 3327, "JournalTitle": "Empirical Software Engineering", "ISSN": "1382-3256", "EISSN": "1573-7616", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Université du Québec à Montréal, Montréal, Canada"}, {"AuthorId": 2, "Name": "Tegawendé F. Bissyandé", "Affiliation": "SnT, University of Luxembourg, Esch-sur-Alzette, Luxembourg"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université du Québec à Montréal, Montréal, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "SnT, University of Luxembourg, Esch-sur-Alzette, Luxembourg"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "SnT, University of Luxembourg, Esch-sur-Alzette, Luxembourg"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Monash University, Melbourne, Australia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "SnT, University of Luxembourg, Esch-sur-Alzette, Luxembourg"}], "References": []}, {"ArticleId": 95638981, "Title": "The concept of improving the security of IT systems supporting the storage of knowledge in organizations", "Abstract": "This article presents the concept of improving the security of information systems intended for storing knowledge in organizations. It is designed to protect knowledge from leakage, theft or destruction, but without denying access to its resources to employees. It is possible thanks to the introduction of the so-called security and sharing groups (S&S groups) that have two attributes at the same time – the level of knowledge security and the level of knowledge sharing. The proposed concept also assumes assigning knowledge categories to data and enabling the organization to manage the allocation of operations on data from individual knowledge categories to S&S groups. As a result, the system enables the protection of the most valuable knowledge resources, without restricting access to its other categories. The developed concept makes it easier to find a compromise between the strength of security and the free flow of knowledge at the time of system implementation. In addition, the dynamic ability to choose the level of security and the level of access to knowledge in the system allows adaptation to changes taking place in the environment of the organization and increases its resilience.", "Keywords": "Knowledge management ; knowledge sharing ; organization resilience ; security", "DOI": "10.1080/21642583.2022.2102550", "PubYear": 2022, "Volume": "10", "Issue": "1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Cybernetics, Military University of Technology, Warsaw, Poland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TakeCtrl.eu, Warsaw, Poland;Institute of Security and Defence, Faculty of Security, Logistics and Management, Military University of Technology, Warsaw, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>-Wróbel", "Affiliation": "Institute of Security and Defence, Faculty of Security, Logistics and Management, Military University of Technology, Warsaw, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Cybernetics, Military University of Technology, Warsaw, Poland;TakeCtrl.eu, Warsaw, Poland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Cyber Command, Warsaw, Poland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Applied Physics, Faculty of New Technologies and Chemistry, Military University of Technology, Warsaw, Poland"}], "References": []}, {"ArticleId": 95639056, "Title": "Summarizing the special issue on the human factor in cybercrime", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.chb.2022.107411", "PubYear": 2023, "Volume": "138", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Criminal Justice, University of Montreal, Canada;School of Criminal Justice, Michigan State University, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Criminal Justice, University of Montreal, Canada"}], "References": []}, {"ArticleId": 95639058, "Title": "Light-Emitting Diode-Based Optical Localization of a Robot in Continuous Motion Using Dynamic Prediction", "Abstract": "<p>For mobile robots, localization is essential for navigation and spatial correlation of its collected data. However, localization in Global Positioning System-denied environments such as underwater has been challenging. Light-emitting diode (LED)-based optical localization has been proposed in the literature, where the bearing angles extracted from the line-of-sight of the robot viewed from a pair of base nodes (also known as beacon nodes) are used to triangulate the position of the robot. The state-of-the-art in this approach uses a stop-and-go motion for the robot in order to ensure an accurate position measurement, which severely limits the mobility of the robot. This work presents an LED-based optical localization scheme for a mobile robot undergoing continuous motion, despite the two angles in each measurement cycle being captured at different locations of the robot. In particular, the bearing angle measurements are captured by the robot one at a time and are properly correlated with respect to the base nodes by utilizing the velocity prediction from Kalman filtering. The proposed system is evaluated in simulation and experiments, with its performance compared to the traditional state-of-the-art approach where the two angle measurements in each cycle are used directly to compute the position of the robot. In particular, the experimental results show that the average position and velocity estimation errors are reduced by 55% and 38%, respectively, when comparing the proposed method to the state-of-the-art.</p>", "Keywords": "", "DOI": "10.1115/1.4055176", "PubYear": 2022, "Volume": "144", "Issue": "11", "JournalId": 9493, "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control", "ISSN": "0022-0434", "EISSN": "1528-9028", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 95639085, "Title": "Performance evaluation of transparent self-powered n-ZnO/p-NiO heterojunction ultraviolet photosensors", "Abstract": "We have constructed NiO/ZnO thin film heterojunction diodes by dc magnetron sputtering technique and evaluated their performance for ultraviolet sensor applications. The constructed device configuration, ITO/ZnO/NiO/Ag exhibited excellent current-voltage rectifying characteristics in the order of 10<sup>5</sup> at room temperature. The effect of rapid thermal annealing treatment on the fabricated ITO/ZnO/NiO thin film stack was evaluated for their photodetector characteristics. The structural, optical, and spectroscopic properties were also investigated. Further, the ITO/ZnO/NiO/Ag diodes were tested under 365 nm UV light illumination having a power density of 0.06 mW/cm<sup>2</sup>. Remarkably, a speed of response with rise/fall time of 197.29/537.10 ms has been recorded at self-powered mode. Interestingly, the photodiode device has exhibited a spectral responsivity of 13.01 mA/W and stable photo detectivity of 5.66 × 10<sup>11</sup> Jones at room temperature. The n-ZnO/p-NiO heterojunction photodetector has shown its ability to detect a faint UV light in a self-powered mode.", "Keywords": "Self-powered ; UV photodetector ; N-ZnO/p-NiO heterojunction ; Performance evaluation", "DOI": "10.1016/j.sna.2022.113799", "PubYear": 2022, "Volume": "345", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal 576104, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal 576104, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal 576104, India;Corresponding author"}], "References": [{"Title": "Enhancement of performance of Ga incorporated ZnO UV photodetectors prepared by simplified two step chemical solution process", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "333", "Issue": "", "Page": "113217", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Self-powered transparent ultraviolet photo-sensors based on bilayer p-NiO/n-Zn(1−x) Sn(x)O heterojunction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "338", "Issue": "", "Page": "113479", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "SnO2-NiO heterojunction based self-powered UV photodetectors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "340", "Issue": "", "Page": "113540", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Current-voltage characteristics of nano whisker ZnO/Si heterojunction under UV exposition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "342", "Issue": "", "Page": "113618", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 95639126, "Title": "The acceptability of social robots: A scoping review of the recent literature", "Abstract": "<b  >Background</b> Social robots seem to be integrating into our society quite rapidly (e.g., <PERSON><PERSON> et al. 2019). Yet, the fact that they are integrated in different domains does not ensure potential users accept them (e.g., <PERSON>, <PERSON>, &amp; <PERSON>, 2018). This is especially true as technology assessments evolve over time (e.g., <PERSON> &<PERSON>; <PERSON>, 2005). In this scoping review, the aim is to provide an overview of the acceptability of social robots over the last ten years. <b  >Objectives</b> The research question is to understand whether potential users now finally accept these social robots, by focusing on the last few years. <b  >Eligibility criteria</b> Only studies that explicitly state that they measure the acceptability of social robots were included whether qualitative, quantitative, or mixed. <b  >Source of evidence</b> The electronic databases searched were PsycINFO, PsycARTICLES and the Psychology database (ProQuest), as well as Google Scholar. Data was collected in August 2020 from the databases and in November 2020 from Google scholar. Three hundred seventy-six articles were identified in the databases as relevant. Finally, after screening by inclusion and exclusion criteria, 43 articles were eligible. <b  >Charting methods</b> In addition to the metadata, the following data were extracted: type of robot exposition (e.g., real interaction, video, picture), the name of the robot used, the acceptability measure used, secondary measure (e.g., satisfaction), type of article (e.g., qualitative) and authors’ conclusions. Sample nationality, sample size, sample type and funding/conflict of interest were extracted in a second step. <b  >Results</b> The scoping review identified 43 articles published from August 2010 to November 2020. From the selected articles we observe that in the last ten years it is the field of health (60.5%) that has been the focus, in the accompaniment of the elderly (32.6%). These results are also reflected in the tools used to measure acceptability. The Almere model (i.e., adapted to older adults, Heerink, Krose, Evers, &amp; Wielinga, 2009, pp. 528–533) was the most used (9.3%). Overall, most people have a rather positive attitude towards social robots (67.4%), while others often are ambivalent regarding their acceptability (11.6%). <b  >Conclusion</b> In recent years, researchers have focused on improving the acceptability of social robots, often with the objective of supporting the autonomy of the elderly. However, the high rate of acceptability found in this scoping review should be taken with precaution in view of the few articles selected and the conditions of the studies selected, often in the laboratory and rarely in long-term ecological conditions.", "Keywords": "Acceptability ; Acceptance ; Adoption ; Social robots ; Scoping review", "DOI": "10.1016/j.chb.2022.107419", "PubYear": 2022, "Volume": "137", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratoire d’Anthropologie et de Psychologie Cognitives, Cliniques et Sociale (LAPCOS, UPR78), Université Côte d’Azur, Nice, France;Corresponding author. Laboratoire d'Anthropologie et de Psychologie Cliniques, Cognitives et Sociales, Campus Saint Jean d'Angely / SJA3 / MSHS Sud-Est, 24 avenue des diables bleus, 06357, Nice Cedex 4, Université Côte d’Azur, Nice, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratoire d’Anthropologie et de Psychologie Cognitives, Cliniques et Sociale (LAPCOS, UPR78), Université Côte d’Azur, Nice, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratoire d’Anthropologie et de Psychologie Cognitives, Cliniques et Sociale (LAPCOS, UPR78), Université Côte d’Azur, Nice, France"}], "References": [{"Title": "Mutual Shaping in the Design of Socially Assistive Robots: A Case Study on Social Robots for Therapy", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "847", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Sharing Experiences to Help a Robot Present Its Mind and Sociability", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "2", "Page": "341", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "How Physical Presence Overrides Emotional (Coping) Effects in HRI: Testing the Transfer of Emotions and Emotional Coping in Interaction with a Humanoid Social Robot", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "2", "Page": "407", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "A Systematic Review of Attitudes, Anxiety, Acceptance, and Trust Towards Social Robots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "6", "Page": "1179", "JournalTitle": "International Journal of Social Robotics"}]}, {"ArticleId": 95639538, "Title": "From skill growth expectancy to online game commitment", "Abstract": "Players&#x27; game commitment is relevant and important to game makers. Hence, we need to know how to strengthen players’ game commitment. The literature pointed out that two theories (flow theory and goal gradient theory) may be effective perspectives. However, we do not know how to trigger both theoretical pathways, indicating a gap. Hence, the aim of this study is to address this gap by proposing a new construct: expectancy for skill growth . We used a survey method to collect data from 1320 gamers. The structural equation modeling method was used to test the hypotheses. We found that the new construct enhances in-game flow, gaming goal proximity, and motivation to attain gaming goals, strengthening game commitment. The key interpretation is that the new construct can trigger both theoretical pathways, strengthening game commitment.", "Keywords": "Online game ; Expectancy ; Flow ; Game commitment ; Goal gradient ; Structural equation modeling", "DOI": "10.1016/j.chb.2022.107422", "PubYear": 2022, "Volume": "137", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "PhD Candidate, Graduate Institute of Management, Chang Gung University, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Business Management, Chang Gung University, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Business Management, Chang Gung University, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "Associate Professor & Chair, Department of Information Management, Chang Gung University, Taiwan;Associate Research Fellow (joint Appointment), Department of Nursing, Chang Gung Memorial Hospital, Taoyuan Branch, Taiwan;Corresponding author.259, Wen<PERSON> 1st Rd, Gueishan, Taoyuan, 333, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Professor, Graduate Institute of Management, Chang Gung University, Taiwan;Research Fellow (joint Appointment), Department of Rehabilitation, Chang Gung Memorial Hospital, Linkou, Taiwan;Adjunct Professor, Department of Business and Management, Ming Chi University of Technology, Taiwan;Corresponding author.259, Wenhua 1st Rd, Gueishan, Taoyuan, 333, Taiwan"}], "References": [{"Title": "Exploring the relationship between smartphone activities, flow experience, and boredom in free time", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "130", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The role of wishful identification, emotional engagement, and parasocial relationships in repeated viewing of live-streaming games: A social cognitive theory perspective", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "106327", "JournalTitle": "Computers in Human Behavior"}, {"Title": "South African millennials’ acceptance and use of retail mobile banking apps: An integrated perspective", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "106405", "JournalTitle": "Computers in Human Behavior"}, {"Title": "What drives problematic online gaming? The role of IT identity, maladaptive cognitions, and maladaptive emotions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "106386", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Understanding massively multiplayer online role‐playing game addiction: A hedonic management perspective", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "1", "Page": "33", "JournalTitle": "Information Systems Journal"}, {"Title": "Why future friends matter: impact of expectancy of relational growth on online gamer loyalty", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "5", "Page": "1479", "JournalTitle": "Internet Research"}, {"Title": "Running on a social exercise platform: Applying self-determination theory to increase motivation to participate in a sporting event", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "106523", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Impact of online gamers' conscientiousness on team function engagement and loyalty", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "142", "Issue": "", "Page": "113468", "JournalTitle": "Decision Support Systems"}, {"Title": "Understanding trust in ms-commerce: The roles of reported experience, linguistic style, profile photo, emotional, and cognitive trust", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "2", "Page": "103416", "JournalTitle": "Information & Management"}, {"Title": "Exploring the effects of psychological ownership, gaming motivations, and primary/secondary control on online game addiction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "144", "Issue": "", "Page": "113512", "JournalTitle": "Decision Support Systems"}, {"Title": "The engagement–addiction dilemma: an empirical evaluation of mobile user interface and mobile game affordance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "5", "Page": "1745", "JournalTitle": "Internet Research"}, {"Title": "Why do people play games on mobile commerce platforms? An empirical study on the influence of gamification on purchase intention", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "106991", "JournalTitle": "Computers in Human Behavior"}, {"Title": "That's not my fault: Excuses given by players exhibiting in-game intra-team aggressive behavior in online games", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "107045", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Catch me if you can: effects of AR-enhanced presence on the mobile game experience", "Authors": "Jihye Park; Dongwoo Ko", "PubYear": 2022, "Volume": "32", "Issue": "4", "Page": "1235", "JournalTitle": "Internet Research"}, {"Title": "Power structure builds gamer loyalty", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "154", "Issue": "", "Page": "113696", "JournalTitle": "Decision Support Systems"}, {"Title": "Does the social platform established by MMORPGs build social and psychological capital?", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "107139", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Gamer Girl vs. Girl Gamer: Stereotypical Gamer Traits Increase Men's Play Intention", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "107217", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Need satisfaction, passion and wellbeing effects of videogame play prior to and during the COVID-19 pandemic", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "107232", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Virtual reality technology and game enjoyment: The contributions of natural mapping and need satisfaction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "132", "Issue": "", "Page": "107242", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The role of family intimacy in playing collaborative e-sports with a Switch device to predict the experience of flow and anxiety during COVID-19 lockdown", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "132", "Issue": "", "Page": "107244", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Personalities, sequences of strategies and actions, and game attacks: A statistical discourse analysis of strategic board game play", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "133", "Issue": "", "Page": "107271", "JournalTitle": "Computers in Human Behavior"}, {"Title": "A Dual-Identity Perspective of Obsessive Online Social Gaming", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "5", "Page": "1245", "JournalTitle": "Journal of the Association for Information Systems"}, {"Title": "The actualization of meta affordances: Conceptualizing affordance actualization in the metaverse games", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "133", "Issue": "", "Page": "107292", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 95639607, "Title": "Flatness based Control of Single Phase Grid Connected Photovoltaic System Associated with a Shunt Active Power Filter", "Abstract": "The present paper proposes a new controller design method based on the flatness approach of single-phase full bridge shunt active power filter (SAPF) connected to photovoltaic system. The main control objective is threefold: (i) extracting the maximum active power in the output photovoltaic panel using an appropriate maximum power point tracking (MPPT) algorithm; (ii) regulation of the voltage of the DC link capacitor with the MPPT requirement to ensure the power exchanged between PV source and AC power grid; (iii) compensation for undesired harmonic and reactive currents caused by the non-linearity of electronic power loads, i.e. power factor correction (PFC). In this paper, the problem is dealt with designing a new two-loop cascaded controller in which the inner loop is constructed using a flatness approach to ensure the unity power factor. The outer loop consists mainly of filtered proportional-integral (PI) regulator, that assures a tight regulation of the voltage across the photovoltaic panel with an incremental conductance (IC) algorithm to meet MPPT requirement. The theoretical model and its controller are validated by numerical simulation within the MATLAB/SimPowerSystems environment.", "Keywords": "Shunt active power filter ; PV system ; Flatness control ; MPPT ; PI regulator ; PFC", "DOI": "10.1016/j.ifacol.2022.07.286", "PubYear": 2022, "Volume": "55", "Issue": "12", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ESE lab, ENSEM of Casablanca, Hassan II University, BP 8118 Casablanca, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ESE lab, ENSEM of Casablanca, Hassan II University, BP 8118 Casablanca, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "ESE lab, ENSEM of Casablanca, Hassan II University, BP 8118 Casablanca, Morocco"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "ESE lab, ENSEM of Casablanca, Hassan II University, BP 8118 Casablanca, Morocco"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "LAC Department d'Enginyeria Electrònica, Elèctrica i Automàtica, Universitat Rovira i Virgili, Tarragona , Spain"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "LAC LAB, EA 7478, University of Caen Basse-Normandie, Caen, France"}], "References": [{"Title": "Hybrid Automaton Control of Three Phase Reduced Switch Shunt Active Power Filter Connected Photovoltaic System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "12847", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Hybrid Controller with Fuzzy Logic Technique for Three Phase Half Bridge Interleaved Buck Shunt Active Power Filter", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "13418", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 95639608, "Title": "Control of a PV-fed Cascaded Half-Bridge Multilevel Inverter with Shunt Active Power Filter Capabilities", "Abstract": "Due to increasingly use of nonlinear loads in power grids, an Active Power Filter (APF) is unavoidable for guaranteeing the stable and efficient operation of power systems. APFs based on Cascaded Half-Bridge (CHB) inverters with low switching frequency can compensate high-order harmonics in medium and high voltage systems. Since grid-connected Photovoltaic (PV) systems interface to the grid with inverters, they can be multi-functionally used both as APFs and power feeders. In this paper, the control of a grid-connected 5-level CHB inverter acting as a shunt APF is addressed in presence of nonlinear loading conditions. The CHB converter is fed by PV arrays interfacing DC-DC boost converters whose main objective is to track maximum available power from the PV sources. The CHB is controlled to keep the grid current sinusoidal and in phase with the grid voltage hence achieving a unitary power factor as well as to regulate DC-link voltages. The active component of the inverter output current is determined by low pass filtering the sum of the PV sources powers. In addition, the compensating current signal is calculated based on synchronous reference frame (SRF) theory, and then it is added to the current control loop. To achieve a high-quality steady-state current, a proportional resonant controller is used for the current loop. PSIM© numerical simulations are performed to validate the CHB performances as a shunt APF under irradiance and load change conditions.", "Keywords": "Active power filter ; Cascaded half-bridge inverter ; Grid-connected ; Synchronous reference frame ; Photovoltaic", "DOI": "10.1016/j.ifacol.2022.07.285", "PubYear": 2022, "Volume": "55", "Issue": "12", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitat Rovira i Virgili, Tarragona, Cataluña, 43007 Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitat Rovira i Virgili, Tarragona, Cataluña, 43007 Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universitat Rovira i Virgili, Tarragona, Cataluña, 43007 Spain"}], "References": []}, {"ArticleId": 95639617, "Title": "<PERSON><PERSON><PERSON><PERSON>'s method of recursive objective inequalities in machine learning", "Abstract": "“Stripe” algorithm was designed by <PERSON><PERSON><PERSON><PERSON> as an algorithm for linear classification model training. The basic approach is to reduce loss minimization problem to solving a system of infinite number of inequalities. The present paper considers various forms of this reduction as well as its practical applicability. Obtained algorithms are experimentally compared with traditional linear models such as logistic regression and linear regression trained using stochastic gradient descent. Simulation results show that the “Stripe” algorithm possesses fast convergence and in particular suitable well in the paradigm of online machine learning.", "Keywords": "“Stripe” algorithm ; machine learning ; linear models ; online machine learning", "DOI": "10.1016/j.ifacol.2022.07.301", "PubYear": 2022, "Volume": "55", "Issue": "12", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Problems in Mechanical Engineering, Saint Petersburg, Russia;Saint Petersburg State University, Saint Petersburg, Russia"}], "References": []}, {"ArticleId": 95639621, "Title": "Adaptive parameter estimation of linear time invariant generators in finite time with relaxed excitation conditions", "Abstract": "The problem of identifying the constant parameters of LTI generators in finite time are considered. The signal is represented as the output of a linear generator, the parameters of the state matrix and initial conditions are unknown. The main idea is to parameterize the signal using delays and obtain a linear or nonlinear regression model. The performance of the algorithms considered in the article is illustrated by computer modeling.", "Keywords": "identification ; Jordan form matrix ; linear regression model ; generator", "DOI": "10.1016/j.ifacol.2022.07.294", "PubYear": 2022, "Volume": "55", "Issue": "12", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Control Systems and Robotics, ITMO University, Kronversky Pr. 49, St. Petersburg, 197101, Russia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Control Systems and Robotics, ITMO University, Kronversky Pr. 49, St. Petersburg, 197101, Russia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Control Systems and Robotics, ITMO University, Kronversky Pr. 49, St. Petersburg, 197101, Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Control Systems and Robotics, ITMO University, Kronversky Pr. 49, St. Petersburg, 197101, Russia"}], "References": [{"Title": "Finite-time Frequency Estimator for Harmonic Signal", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "584", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 95639625, "Title": "Learning Speed-Gradient Synchronization Control of the Two-Rotor Vibration Setup", "Abstract": "In this paper the implementation of the speed-gradient single or multiple synchronization control algorithm with learning for a two-rotor vibration setup is considered. The main contribution of the paper is demonstration of possibility of neural network control of vibration setups under uncertainty. We propose the structure of the learning control system which is developed to expand the range of the vibration machine efficiency and increase the performance of its operation with uncertainty system parameters. The neural network was trained on simulation results for various loads and desired total system energy values. The presented results of computer simulation demonstrate the possibility of using a learning vibration setup control system based on an artificial neural network and speed gradient algorithm to compensate the platform mass change and keep the desired vibration level due to various bulk media feeding modes.", "Keywords": "Vibration setup ; speed gradient algorithm ; neural network ; learning control system ; multiple synchronization", "DOI": "10.1016/j.ifacol.2022.07.302", "PubYear": 2022, "Volume": "55", "Issue": "12", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "<PERSON> the Great Polytechnic University and Institute of Problems in Mechanical Engineering, Saint-Petersburg, Russia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Saint Petersburg State University of Architecture and Civil Engineering"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Problems in Mechanical Engineering and Saint-Petersburg University Saint-Petersburg, Russia"}], "References": [{"Title": "Speed Gradient Method and Its Applications", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "9", "Page": "1463", "JournalTitle": "Automation and Remote Control"}]}, {"ArticleId": 95639879, "Title": "Sistem Prediksi Penyakit Jantung Koroner Menggunakan Metode Naive Bayes", "Abstract": "Penyebab dari penyakit jantung koroner yaitu penyumbatan pembuluh darah koroner, penyakit ini sangat diperhatikan oleh seluruh kalangan masyarakat dikarenakan pengaruh yang disebabkant oleh penyakit tersebut. Pen<PERSON>tian ini memiliki tujuan untuk membuat prediksi yang akan membantu para dokter untuk melakukan diagnose dengan tepat dan akurat sehingga penyakit jantung koroner dapat ditangani lebih awal. Salah satu algoritma klasifikasi data mining yang digunakan pada penelitian ini adalah algoritma Naïve Bayes Classifier. Algoritma ini diterapkan dengan tujuan untuk menghitung probabilitas kemungkinan seseorang pasien berdasarkan data rekam medis pasien. Rekam medis pasien diperoleh dari kaggle untuk dilakukan percobaan pada sistem yang akan dibuat. Dataset awal memuat 303 record setelah dilakukan preprocessing memuat 296 record. Percobaan pada penelitian ini dilakukan sebanyak 3 percobaan dengan membagi data latih dan data uji. <PERSON><PERSON> yang diperoleh dalam percobaan pertama memiliki akurasi paling tinggi yaitu sebesar 83.1%. Diharapkan dengan adanya sistem ini dapat membantu dokter untuk mendiagnosis penyakit jantung koroner.", "Keywords": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; Naïve Bayes Classifier.", "DOI": "10.29100/jipi.v7i2.2842", "PubYear": 2022, "Volume": "7", "Issue": "2", "JournalId": 59609, "JournalTitle": "JIPI (<PERSON><PERSON><PERSON> da<PERSON>ajaran Informatika)", "ISSN": "", "EISSN": "2540-8984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "UPN Veteran Jakarta"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "UPN Veteran Jakarta"}], "References": []}, {"ArticleId": 95639930, "Title": "Integration of IMEI, RSA, and Signature to Secure Communication in Mobile Applications", "Abstract": "<p>The mobile instant messaging applications have become a famous wireless service in the world allowing a user to communicate with anyone in anywhere. Consequently, the manipulation of the user data creates a pressing need for securing the information and the associated technologies. The protection of these communications has become a paramount objective. The concept of cryptography has become a basic concept in communication and the exchange of messages within companies. This paper presents a hybrid security system allowing messages to be exchanged within a company in a secure manner. Our goal is to provide end-to-end encryption through the combination of asymmetric cryptography, signature, and integration of IMEI parameter. The confidentiality of the messages is ensured by the RSA algorithm, while the integrity of these messages and the sender authentication are ensured by the digital signature, that is carried out with the SHA-2 hash function.</p>", "Keywords": "", "DOI": "10.4018/IJOCI.306979", "PubYear": 2022, "Volume": "12", "Issue": "1", "JournalId": 29137, "JournalTitle": "International Journal of Organizational and Collective Intelligence", "ISSN": "1947-9344", "EISSN": "1947-9352", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Security-oriented view of app behaviour using textual descriptions and user-granted permission requests", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "101685", "JournalTitle": "Computers & Security"}, {"Title": "Identifying security issues for mobile applications based on user review summarization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "122", "Issue": "", "Page": "106290", "JournalTitle": "Information and Software Technology"}, {"Title": "Security and detection mechanism in IoT-based cloud computing using hybrid approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "5/6", "Page": "436", "JournalTitle": "International Journal of Internet Technology and Secured Transactions"}]}, {"ArticleId": 95639940, "Title": "A hybrid differential evolution algorithm for a stochastic location-inventory-delivery problem with joint replenishment", "Abstract": "A practical stochastic location-inventory-delivery problem with multi-item joint replenishment is studied. Unlike the conventional location-inventory model with a continuous-review ( r , Q ) inventory policy, the periodic-review inventory policy is adopted with multi-item joint replenishment under stochastic demand, and the coordinated delivery cost is considered. The proposed model considers the integrated optimization of strategic, tactical, and operational decisions by simultaneously determining (a) the number and location of DCs to be opened, (b) the assignment of retailers to DCs, (c) the frequency and cycle interval of replenishment and delivery, and (d) the safety stock level for each item. An intelligent algorithm based on particle swarm optimization (PSO) and adaptive differential evolution (ADE) is proposed to address this complex problem. Numerical experiments verified the effectiveness of the proposed two-stage PSO-ADE algorithm. A sensitivity analysis is presented to reveal interesting insights that can guide managers in making reasonable decisions.", "Keywords": "Location-inventory problem ; Joint replenishment ; Stochastic demand ; Particle swarm optimization ; Differential evolution", "DOI": "10.1016/j.dsm.2022.07.003", "PubYear": 2022, "Volume": "5", "Issue": "3", "JournalId": 84310, "JournalTitle": "Journal of Information Technology and Data Management", "ISSN": "2666-7649", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Huazhong University of Science and Technology, Wuhan, 430074, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Management, Huazhong University of Science and Technology, Wuhan, 430074, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Huazhong University of Science and Technology, Wuhan, 430074, China;Corresponding author"}], "References": []}, {"ArticleId": 95639984, "Title": "A deep learning based dislocation detection method for cylindrical silicon growth process", "Abstract": "<p>In the process of preparing heavily doped silicon crystals for power electronics and solar power by the Czochralski(CZ) method, dislocations occur due to the presence of impurities and other factors. The presence of dislocations can affect the quality of single-crystal silicon crystalline columns. Therefore, we proposed the improved Yolov4-tiny model (Yolo-SPI) for detecting the occurrence of dislocations. For resolving the problem of low detection accuracy of the original model, we improve the neck part of the model by referring to the structure of the Path Aggregation Network (PAnet). Furthermore, we propose a feature enhancement module to improve the feature extraction ability of the model and introduce depthwise separable convolution to reduce the parameters. We produced the single-crystal silicon habit line dataset by using the industrial camera. The experimental results on our dataset show that the Yolo-SPI model outperforms Ghostnet-Yolov4, Mobilenetv3-Yolov4, EfficientDet-v0, and Nanodet. The Yolo-SPI model improves the Precision from 73.28% to 98.01% compared to the original Yolov4-tiny model and the Recall is also improved to 97.51%. At the same time, the number of parameters in the Yolo-SPI model is decreased from 5.87M to 2.05M compared to the Yolov4-tiny model. Our model also beats other models in the speed of detection, reaching 133FPS. In practical applications, our model achieves higher accuracy and faster detection speed.</p>", "Keywords": "Deep learning; Silicon; Yolov4-tiny; Depthwise separable convolution; Crystal furnace; Habit line", "DOI": "10.1007/s10489-022-03800-0", "PubYear": 2023, "Volume": "53", "Issue": "8", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science & Engineering, Lanzhou University, Lanzhou, China"}, {"AuthorId": 2, "Name": "Li Hongxing", "Affiliation": "School of Information Science & Engineering, Lanzhou University, Lanzhou, China"}], "References": []}, {"ArticleId": 95639989, "Title": "Sistem Informasi Pariwisata Berbasis Web Pada Kabupaten Nunukan", "Abstract": "Penggunaan teknologi informasi masa kini berkembang semakin maju dan cepat, dengan adanya teknologi pertukaran informasi menjadi lebih mudah. Instansi pemerintahan merupakan salah satu pihak yang harus mulai mengikuti perkembangan dengan pemanfaatkan teknologi informasi untuk meningkatkan mutu layanan kepada masyarakat. Kabupaten Nunukan merupakan provinsi baru yaitu Kalimantan Utara dan memiliki berbagai potensi sumber daya alam antara lain yaitu, kepariwisataan yang sangat menarik.  Untuk penyebaran informasi mengenai objek pariwisata yang ada masih sangat terbatas yaitu menggunakan brosur dan pamphlet. Promosi perlu dilakukan untuk meningkatkan daya kunjung wisatawan terhadap tempat wisata yang ada di Kabupaten Nunukan. Penelitian ini menggunakan metode waterfall serta menggunakan analisis sistem PIECES. Sistem ini dibuat dengan menggunakan bahasa PHP, HTML, CSS dan framework Laravel  serta menggunakan database MySQL yang terdiri atas fitur pemesanan paket wisata, fitur informasi yang terdiri atas informasi hotel, rumah makan, kebudayaan, informasi objek wisata dan fitur laporan. Hasil yang diperoleh adalah sebuah sistem informasi pariwisata berbasis website diharapkan dengan penggunaan sistem ini dapat mempermudah masyarakat untuk memperoleh informasi terkait kepariwisataan di Kabupaten Nunukan.", "Keywords": "", "DOI": "10.29100/jipi.v7i2.2822", "PubYear": 2022, "Volume": "7", "Issue": "2", "JournalId": 59609, "JournalTitle": "JIPI (<PERSON><PERSON><PERSON> da<PERSON>ajaran Informatika)", "ISSN": "", "EISSN": "2540-8984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Pembangunan Nasional Veteran Jakarta"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Pembangunan Nasional Veteran Jakarta"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Pembangunan Nasional Veteran Jakarta"}], "References": []}, {"ArticleId": 95640144, "Title": "Universal Streaming of Subset Norms", "Abstract": "Most known algorithms in the streaming model of computation aim to approximate a single function such as an ℓp norm. In 2009, <PERSON> [https://sublinear.info, Open Problem 30] asked if it is possible to design universal algorithms, that simultaneously approximate multiple functions of the stream. In this paper we answer the question of <PERSON> for the class of subset-io norms in the insertion-only frequency-vector model. Given a family of subsets, S ⊂ 2[n], we provide a single streaming algorithm that can (1 ± ε)-approximate the subset-ℓp norm for every S ∈ S. Here, the subset-ℓp norm of v є ℝn with respect to the set S ⊆ [n] is the ℓp norm of v |S (the vector v restricted to S by zeroing all other coordinates). Our main result is a nearly tight characterization of the space complexity of the subset-io norm for every family S ⊂ 2[n] in insertion-only streams, expressed in terms of the \"heavy-hitter dimension\" of S, a new combinatorial quantity related to the VC-dimension of S. We also show that the more general turnstile and sliding-window models require a much larger space usage. All these results easily extend to the i1 norm. In addition, we design algorithms for two other subset-ℓp, variants. These can be compared to the famous Priority Sampling algorithm of Duff<PERSON>, Lund and <PERSON> [JACM 2007], which achieves additive approximation ε ‖ v ‖x for all possible subsets (S = 2[n]) in the entrywise update model. One of our algorithms extends their algorithm to handle turnstile updates, and another one achieves multiplicative approximation, given a family S. © 2022 <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> <PERSON>. <PERSON>.", "Keywords": "universal streaming; subset norms", "DOI": "10.4086/toc.2022.v018a020", "PubYear": 2022, "Volume": "18", "Issue": "1", "JournalId": 34450, "JournalTitle": "Theory of Computing", "ISSN": "", "EISSN": "1557-2862", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Johns Hopkins University, Baltimore, MD, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Applied Mathematics, TheWeizmann Institute of Science, Rehovot, Israel"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of California, Los Angeles, CA, United States"}], "References": []}, {"ArticleId": 95640243, "Title": "A critical review of Australia's China-dominant education supply chain", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJASM.2021.10049706", "PubYear": 2021, "Volume": "15", "Issue": "2", "JournalId": 10974, "JournalTitle": "International Journal of Agile Systems and Management", "ISSN": "1741-9174", "EISSN": "1741-9182", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 95640244, "Title": "A time interval-based approach for business process fragmentation over cloud and edge resources", "Abstract": "<p>This paper presents an approach for fragmenting business processes over 2 types of complementary platforms referred to as cloud resources and edge resources. Fragmentation caters to the separate needs and requirements of business processes’ owners. Indeed, some owners prioritize the security of their fragmented processes over availability while others prioritize the reliability of their fragmented processes over performance. Despite its benefits, fragmentation raises many concerns like how to reduce communication delays between disparate fragments and how to maintain acceptable loads over all the distributed resources. To identify the necessary cloud and edge resources that would accommodate fragmented business processes, the approach resorts to <PERSON>’s time algebra allowing to simultaneously reason over both resources’ availability-time intervals and processes’ use-time intervals. This reasoning covers a good range of time relations like overlaps, during, and meets, is aware of resources’ properties like limited-but-extensible, and satisfies business processes’ requirements like data freshness. The fragmentation approach, in this paper, is illustrated with a banking case-study, validated through a system developed on top of Google Colaboratory, and evaluated through a set of real experiments.</p>", "Keywords": "<PERSON>’s time algebra; Business process; Cloud resource; Edge resource; Fragmentation", "DOI": "10.1007/s11761-022-00345-5", "PubYear": 2022, "Volume": "16", "Issue": "4", "JournalId": 4313, "JournalTitle": "Service Oriented Computing and Applications", "ISSN": "1863-2386", "EISSN": "1863-2394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ReDCAD, University of Sfax, Sfax, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zayed University, Dubai, UAE"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "ReDCAD, University of Sfax, Sfax, Tunisia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ReDCAD, University of Sfax, Sfax, Tunisia"}], "References": [{"Title": "Restriction‐based fragmentation of business processes over the cloud", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "1", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Fog computing architecture for personalized recommendation of banking products", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "112900", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Maude-Based rewriting approach to model and verify Cloud/Fog self-adaptation and orchestration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "101821", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Towards privacy preserving AI based composition framework in edge networks using fully homomorphic encryption", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "103737", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": ********, "Title": "Applications of cognitive internet of medical things in modern healthcare", "Abstract": "The sudden outbreak of the novel coronavirus disease in 2019, known as COVID-19 has impacted the entire globe and has forced governments of various countries to a partial or full lockdown in the fear of the rapid spread of this disease. The major lesson learned from this pandemic is that there is a need to implement a robust system by using non-pharmaceutical interventions for the prevention and control of new contagious viruses. This goal can be achieved using the platform of the Internet of Things (IoT) because of its seamless connectivity and ubiquitous sensing ability. This technology-enabled healthcare sector is helpful to monitor COVID-19 patients properly by adopting an interconnected network. IoT is useful for improving patient satisfaction by reducing the rate of readmission in the hospital. The presented work discusses the applications and technologies of IoT like smart and wearable devices, drones, and robots which are used in healthcare systems to tackle the Coronavirus pandemic This paper focuses on applications of cognitive radio-based IoT for medical applications, which is referred to as &quot;Cognitive Internet of Medical Things&quot; (CIoMT). CIoMT is a disruptive and promising technology for dynamic monitoring, tracking, rapid diagnosis, and control of pandemics and to stop the spread of the virus. This paper explores the role of the CIoMT in the health domain, especially during pandemics, and also discusses the associated challenges and research directions.", "Keywords": "COVID-19;Cognitive Internet of Medical Things (CIoMT);Cognitive radio (CR);Health care informatics;Internet of Medical Things (IoMT);Internet of Things (IoT);Real-time tracking", "DOI": "10.1016/j.compeleceng.2022.108276", "PubYear": 2022, "Volume": "102", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Vardhaman College of Engineering, Hyderabad, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing Science and Engineering, VIT Bhopal University, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Soongsil University, South Korea."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics, Sagar Institute of Research and Technology, India."}], "References": [{"Title": "Building Resilience against COVID-19 Pandemic Using Artificial Intelligence, Machine Learning, and IoT: A Survey of Recent Progress", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "2", "Page": "506", "JournalTitle": "IoT"}]}, {"ArticleId": 95640378, "Title": "Incident Detection Techniques for the Thai language on Twitter", "Abstract": "<p>Nowadays, the rate of road incidents is continuously increasing as a result of elevated capability of vehicle acceleration that increases the risk of driver’s mistakes. Such road incidents directly impact the flow of traffic in such area and affect directly and indirectly to the economy, society, and environment. Incident monitoring and detection in Thailand is currently done by the responsible authority through CCTV and the traffic flow data from traffic flow measurement, both means of monitoring and detection have high operation costs. Online communication, on the other hand, has seen significant growth in the present days resulting in a fast growth of online social media use for various characteristic of communication replacing telephone calls. This article will present forms of incident detection from social media posts that have been data-mined from Twitter with autonomous API designed to screen for messages related to incident detection consisting of 4 steps. The experiment demonstrated the ability of the proposed method to detect incidents in Thai language with the accuracy of 85.80%, the detection rate (DR) of 78.83%, and false alarm rate (FAR) of 21.17%, based on the top 5 ranked keywords, out of 20 first keywords.</p>", "Keywords": "Incident Detection; Twitter; Word Cloud", "DOI": "10.37936/ecti-cit.2022163.247328", "PubYear": 2022, "Volume": "16", "Issue": "3", "JournalId": 75000, "JournalTitle": "ECTI Transactions on Computer and Information Technology (ECTI-CIT)", "ISSN": "2286-9131", "EISSN": "2286-9131", "Authors": [{"AuthorId": 1, "Name": "KORN PUANGNAK", "Affiliation": "Faculty of Engineering, Rajamangala University of Technology, Phra Nakhon, Thailand"}, {"AuthorId": 2, "Name": "Natworapol Rachsiriwatcharabul", "Affiliation": "Faculty of Engineering, Rajamangala University of Technology, Phra Nakhon, Thailand"}], "References": []}, {"ArticleId": 95640591, "Title": "Liver segmentation based on complementary features U-Net", "Abstract": "<p>Automatic segmentation of the liver in abdominal CT images is critical for guiding liver cancer biopsies and treatment planning. Yet, automatic segmentation of CT liver images remains challenging due to the poor contrast between the liver and surrounding organs in abdominal CT images. In this paper, we propose a novel network for liver segmentation, and the network is essentially a U-shaped network with an encoder–decoder structure. Firstly, the complementary feature enhancement unit is designed in the network to mitigate the semantic gap between encoder and decoder. The complementary feature enhancement unit is based on subtraction, which enhances the complementary features between encoder and decoder. Secondly, this paper proposes a new cross attention model that no longer generates value by convolution, which reduces redundant information and enhances the contextual information of single sparse attention by encoding contextual information by (3\times 3) convolution. The dice score, accuracy, and precision of our network on the LiTS dataset were 95.85 (%) , 97.19 (%) , and 97.11 (%) , and the dice score, accuracy, and precision on the dataset consisted of 3Dircadb and CHAOS were 93.65 (%) , 94.38 (%) , and 97.53 (%) .</p>", "Keywords": "U-Net; Liver segmentation; Attention; Complementary features", "DOI": "10.1007/s00371-022-02617-9", "PubYear": 2023, "Volume": "39", "Issue": "10", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "Junding Sun", "Affiliation": "College of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> Hui", "Affiliation": "College of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, China"}], "References": [{"Title": "Automatic liver segmentation from abdominal CT volumes using improved convolution neural networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "1", "Page": "111", "JournalTitle": "Multimedia Systems"}, {"Title": "An improved segmentation algorithm of CT image based on U-Net network and attention mechanism", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "25", "Page": "35983", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 95640644, "Title": "Does sample source matter for theory? Testing model invariance with the influence of presumed influence model across Amazon Mechanical Turk and Qualtrics Panels", "Abstract": "Online data collection services are increasingly common for testing mass communication theory. However, how consistent are the theoretical tenets of theory when tested across different online data services? A pre-registered online survey ( N  = 1546) examined the influence of the presumed influence model across subjects simultaneously recruited from Amazon Mechanical Tur k and Qualtrics Panels . Results revealed that model parameters were mostly consistent with the IPI theory regardless of data source. Methodological implications are discussed.", "Keywords": "Amazon mechanical turk ; Model invariance ; Presumed influence ; Qualtrics ; Sampling", "DOI": "10.1016/j.chb.2022.107416", "PubYear": 2022, "Volume": "137", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Journalism and Communications, University of Florida, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Communications, The Pennsylvania State University, USA"}, {"AuthorId": 3, "Name": " <PERSON>", "Affiliation": "School of Journalism and Mass Communications, University of South Carolina, USA"}], "References": [{"Title": "Identifying psychological features of robots that encourage and discourage trust", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "134", "Issue": "", "Page": "107301", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The effects of emotions, individual attitudes towards vaccination, and social endorsements on perceived fake news credibility and sharing motivations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "134", "Issue": "", "Page": "107307", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Service robots or human staff? The role of performance goal orientation in service robot adoption", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "134", "Issue": "", "Page": "107339", "JournalTitle": "Computers in Human Behavior"}, {"Title": "How online searches fuel health anxiety: Investigating the link between health-related searches, health anxiety, and future intention", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "136", "Issue": "", "Page": "107384", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Predicting the moral consideration of artificial intelligences", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "136", "Issue": "", "Page": "107372", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 95640660, "Title": "Dynamic proximal unrolling network for compressive imaging", "Abstract": "Compressive imaging aims to recover a latent image from under-sampled measurements, suffering from a serious ill-posed inverse problem. Recently, deep neural networks have been applied to this problem with superior results, owing to the learned advanced image priors. These approaches, however, require training separate models for different imaging modalities and sampling ratios, leading to overfitting to specific settings. In this paper, a dynamic proximal unrolling network (dubbed DPUNet) was proposed, which can handle a variety of measurement matrices via one single model without retraining. Specifically, DPUNet can exploit both the embedded observation model via gradient descent and imposed image priors by learned dynamic proximal operators, achieving joint reconstruction. A key component of DPUNet is a dynamic proximal mapping module, whose parameters can be dynamically adjusted at the inference stage and make it adapt to different imaging settings. Moreover, in order to eliminate the image blocking artifacts, an enhanced version DPUNet<sup>+</sup> is developed, which integrates a dynamic deblocking module and reconstructs jointly with DPUNet to further improve the performance. Experimental results demonstrate that the proposed method can effectively handle multiple compressive imaging modalities under varying sampling ratios and noise levels via only one trained model, and outperform the state-of-the-art approaches. Our code is available at https://github.com/Yixiao-Yang/DPUNet-PyTorch.", "Keywords": "Dynamic neural networks ; Deep proximal unrolling ; Compressive imaging ; Image reconstruction", "DOI": "10.1016/j.neucom.2022.08.034", "PubYear": 2022, "Volume": "510", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Electronics, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Electronics, Beijing Institute of Technology, Beijing, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing, China"}], "References": [{"Title": "A dual-domain deep lattice network for rapid MRI reconstruction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "397", "Issue": "", "Page": "94", "JournalTitle": "Neurocomputing"}, {"Title": "Image compressive sensing recovery via group residual based nonlocal low-rank regularization", "Authors": "<PERSON>; <PERSON><PERSON>z<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "449", "Issue": "", "Page": "315", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 95640678, "Title": "Enterprise Architecture Management Education in Academia: An International Comparative Analysis", "Abstract": "Enterprise architecture (EA), although matured in more than 30 years of ongoing research, receives more importance with the increasing dependency of business in IT and the growing complexity of IT systems. The integrated management of a companies’ goals, structures, and processes with respect to the business and IT elements, as well as the representation of impacts triggered by planned changes is educated in different ways at many universities all over the world. There are several techniques, methods, tools, and approaches to transfer the knowledge from the educators to the students, giving them the qualification to support their future employers in handling the EA challenges modern companies are facing. This work gives a detailed comparative analysis of more than twenty international educational offers regarding Enterprise Architecture Management, carves out the commonalities and finds two prototypical courses as a best-practice combining the strongest matches for Business Informatics and Computer Science studies alike.", "Keywords": "Enterprise Architecture Management;Education;Survey;Course Design", "DOI": "10.7250/csimq.2022-31.03", "PubYear": 2022, "Volume": "", "Issue": "31", "JournalId": 19473, "JournalTitle": "Complex Systems Informatics and Modeling Quarterly", "ISSN": "", "EISSN": "2255-9922", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Dominik Bork", "Affiliation": ""}], "References": []}, {"ArticleId": 95640706, "Title": "Solving constrained portfolio optimization model using stochastic fractal search approach", "Abstract": "Purpose Optimum utilization of investments has always been considered one of the most crucial aspects of capital markets. Investment into various securities is the subject of portfolio optimization intent to maximize return at minimum risk. In this series, a population-based evolutionary approach, stochastic fractal search (SFS), is derived from the natural growth phenomenon. This study aims to develop portfolio selection model using SFS approach to construct an efficient portfolio by optimizing the Sharpe ratio with risk budgeting constraints. Design/methodology/approach This paper proposes a constrained portfolio optimization model using the SFS approach with risk-budgeting constraints. SFS is an evolutionary method inspired by the natural growth process which has been modeled using the fractal theory. Experimental analysis has been conducted to determine the effectiveness of the proposed model by making comparisons with state-of-the-art from domain such as genetic algorithm, particle swarm optimization, simulated annealing and differential evolution. The real datasets of the Indian stock exchanges and datasets of global stock exchanges such as Nikkei 225, DAX 100, FTSE 100, Hang Seng31 and S&P 100 have been taken in the study. Findings The study confirms the better performance of the SFS model among its peers. Also, statistical analysis has been done using SPSS 20 to confirm the hypothesis developed in the experimental analysis. Originality/value In the recent past, researchers have already proposed a significant number of models to solve portfolio selection problems using the meta-heuristic approach. However, this is the first attempt to apply the SFS optimization approach to the problem.", "Keywords": "Portfolio optimization;Risk-budgeting constraint;Sharpe ratio;Evolutionary algorithm;Stochastic fractal search", "DOI": "10.1108/IJICC-03-2022-0086", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 26058, "JournalTitle": "International Journal of Intelligent Computing and Cybernetics", "ISSN": "1756-378X", "EISSN": "1756-3798", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Commerce, Aligarh Muslim University , Aligarh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Aligarh Muslim University , Aligarh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Commerce, Aligarh Muslim University , Aligarh, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Commerce, Aligarh Muslim University , Aligarh, India"}], "References": []}, {"ArticleId": 95640730, "Title": "Selected Topics on Business Informatics: Editorial Introduction to Issue 31 of CSIMQ", "Abstract": "This thematic issue introduces two structured literature review articles as well as a couple of empirical ones. The authors of the literature reviews move into the broad field of assuring the quality of IT artifacts, focusing on different dimensions of the software engineering process. With the ever-increasing scale of computerization in more and more areas of life, insufficient emphasis on quality is not only associated with significant costs of bug-fixing. After all, considerable risks arise from the possibility of exploiting the vulnerabilities of the target product. In extreme cases, poor quality can lead to loss of health and life. Not surprisingly, academics and practitioners alike have been looking at this challenge for many years, and from numerous perspectives. The quality of IT artefacts also depends on the education of professionals and good understanding of application domains. The empirical papers concern educational issues regarding Enterprise Architecture and deepen our understanding of decentralized autonomous systems.", "Keywords": "Model-Based Engineering;Model Comprehension;Model Quality;Test-Driven Development;Literature Review;Enterprise Architecture;Education;Course Design;Decentralization;Blockchain;Compliance", "DOI": "10.7250/csimq.2022-31.00", "PubYear": 2022, "Volume": "", "Issue": "31", "JournalId": 19473, "JournalTitle": "Complex Systems Informatics and Modeling Quarterly", "ISSN": "", "EISSN": "2255-9922", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 95640797, "Title": "An Adaptive Intelligent System Based on Energy‐Efficient Synaptic Resistor Circuits with Fast Real‐Time Learning", "Abstract": "<p>Unlike the human brain, which concurrently executes inference and learning algorithms in neural networks in real time, artificial intelligence (AI) systems usually execute inference algorithms and learning algorithms in series, which lack fast real-time learning functionality, high computing energy efficiency, and adaptability in the complex, erratic real world. Herein, an intelligent system integrating a drone and a synaptic resistor (synstor) circuit that concurrently executes inference and reinforcement learning algorithms in real-time is reported. Without any prior learning or programming, the conductance matrix of the synstor circuit is dynamically optimized in its real-time learning processes, thus enabling the drone to adapt and fly toward its target positions in erratic aerodynamic environments. In learning experiments involving a drone driven by synstor circuits, humans, or computers, the real-time learning by the synstor circuit is superior to the real-time learning by humans and the cloud learning by computers, in terms of key benchmarks including adaptability, learning time, precision, power consumption, and energy efficiency. By circumventing the fundamental limitations in computers, synstor circuits open up new directions to establish AI systems with brain-like fast real-time learning functionality, high computing energy efficiency, and adaptability in complex, erratic real-world environments for versatile applications.</p>", "Keywords": "adaptive intelligent systems;high energy efficiency;real-time learning;synaptic resistor circuits", "DOI": "10.1002/aisy.202200105", "PubYear": 2022, "Volume": "4", "Issue": "10", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Electrical and Computer Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Materials Science and Engineering University of California, Los Angeles  Los Angeles CA 90095 USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Electrical and Computer Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Materials Science and Engineering University of California, Los Angeles  Los Angeles CA 90095 USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Electrical and Computer Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Materials Science and Engineering University of California, Los Angeles  Los Angeles CA 90095 USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Electrical and Computer Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Materials Science and Engineering University of California, Los Angeles  Los Angeles CA 90095 USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Electrical and Computer Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Materials Science and Engineering University of California, Los Angeles  Los Angeles CA 90095 USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Electrical and Computer Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Materials Science and Engineering University of California, Los Angeles  Los Angeles CA 90095 USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Electrical and Computer Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Materials Science and Engineering University of California, Los Angeles  Los Angeles CA 90095 USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Electrical and Computer Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Materials Science and Engineering University of California, Los Angeles  Los Angeles CA 90095 USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Electrical and Computer Engineering University of California, Los Angeles  Los Angeles CA 90095 USA;Department of Materials Science and Engineering University of California, Los Angeles  Los Angeles CA 90095 USA"}], "References": [{"Title": "Self-driving cars: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113816", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Self‐Programming Synaptic Resistor Circuit for Intelligent Systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "8", "Page": "2100016", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Machine Learning at the Network Edge: A Survey", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "8", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 95640802, "Title": "Effective phase transformation behavior of NiTi triply periodic minimal surface architectures", "Abstract": "<p>The effective behavior of shape memory alloy triply periodic minimal surface (TPMS) structures is investigated using finite element analysis and numerical homogenization. The TPMS unit cells considered are primitive, IW-P, gyroid, and diamond subjected to different loading conditions. Under uniaxial displacement-driven loading, the results show a dramatic increase in effective stress and martensite volume fraction with increased relative density of the TPMS unit cell. Comparison among the four types of TPMS unit cells shows that diamond has superior mechanical performance for the loading cases considered. Based on numerical homogenization results, the onset and subsequent thresholds of phase transformation are determined for all four unit cells considering multiaxial loading. At lower relative density, the loading surfaces corresponding to the onset of phase transformation were reasonably well represented by either <PERSON> or <PERSON>’s criteria. The observed fit with the von <PERSON> model degenerated with increased effective martensite volume fraction, while a proper fit was maintained with <PERSON>’s criterion. Subsequent loading surfaces, corresponding to monotonically increasing martensite volume fraction, show a nonlinear hardening behavior, which seems to follow a similar trend regardless of geometry. The loading surfaces were found to reach asymptotic states with distinctly different features compared to their initial shapes.</p>", "Keywords": "", "DOI": "10.1177/1045389X221115704", "PubYear": 2023, "Volume": "34", "Issue": "6", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Advanced Digital & Additive Manufacturing Center, Khalifa University of Science and Technology, Abu Dhabi, United Arab Emirates;Department of Mechanical Engineering, Khalifa University of Science and Technology, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Digital & Additive Manufacturing Center, Khalifa University of Science and Technology, Abu Dhabi, United Arab Emirates;Department of Mechanical Engineering, Khalifa University of Science and Technology, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Digital & Additive Manufacturing Center, Khalifa University of Science and Technology, Abu Dhabi, United Arab Emirates;Department of Mechanical Engineering, Khalifa University of Science and Technology, Abu Dhabi, United Arab Emirates"}], "References": []}, {"ArticleId": 95640904, "Title": "Exploring views on affective haptic devices in times of COVID-19", "Abstract": "<p> Affective haptic devices (AHDs) are communication technologies utilizing the sense of touch, and include mediated social touch (MST), symbolic haptic messaging, and awareness systems that, for example, let one feel another person's heartbeat. The COVID-19 pandemic and consequent social distancing measures have led to a reemphasis of the importance of social touch, and many people have experienced firsthand what it is like to miss touching loved ones. This offers an excellent opportunity to study people's intention to use AHDs. For this purpose, a survey study ( n = 277) was conducted combining qualitative and quantitative data analysis methods. Touch deprivation, resulting from not being able to touch a loved one, was associated with intention to use AHDs: the more deprived an individual, the higher his or her intention to use AHDs. Technology readiness and touch aversion did not affect intention to use AHDs. AHDs for symbolic messaging gained higher interest than MST and awareness devices, and long-distance relationships were seen as the most likely scenario for using AHDs. Bi-directionality, synchronicity, and symmetry were regarded as important features for providing shared meaning and a sense of connectedness. Reviewability, multimodality, and actuation type were also deemed important. Limitations of the study and implications for the design of AHDs are discussed. </p>", "Keywords": "Mediated social touch; Social Touch Technology; Haptic technology; Communication characteristics; Technology interest; Touch deprivation; COVID-19", "DOI": "10.3389/fcomp.2022.795927", "PubYear": 2022, "Volume": "4", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "Si<PERSON>kchian Askari", "Affiliation": "Department of Industrial Engineering & Innovation Sciences, Eindhoven University of Technology, Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Industrial Design, Delft University of Technology, Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering & Innovation Sciences, Eindhoven University of Technology, Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering & Innovation Sciences, Eindhoven University of Technology, Netherlands"}], "References": [{"Title": "Generative Adversarial Networks for face generation: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 95640958, "Title": "The influence of trust and commitment on free-to-play gamers co-creation intentions", "Abstract": "", "Keywords": "", "DOI": "10.1080/0144929X.2022.2105745", "PubYear": 2023, "Volume": "42", "Issue": "12", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Marketing, College of Business, Zayed University, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Communication and Media Studies, Zayed University, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Marketing, International Business and Strategy Department, Goodman School of Business, Brock University, St. Catharines, Canada"}], "References": [{"Title": "Building Brand Community Relationships on Facebook Fan Pages: The Role of Perceived Interactivity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "2", "Page": "211", "JournalTitle": "International Journal of Electronic Commerce"}, {"Title": "Customer experiences in the age of artificial intelligence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "106548", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Impact of online gamers' conscientiousness on team function engagement and loyalty", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "142", "Issue": "", "Page": "113468", "JournalTitle": "Decision Support Systems"}, {"Title": "Effects of customer trust on engagement in live streaming commerce: mediating role of swift guanxi", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "5", "Page": "1718", "JournalTitle": "Internet Research"}, {"Title": "Analyzing the changes in the psychological profile of professional League of Legends players during competition", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>-<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "107030", "JournalTitle": "Computers in Human Behavior"}, {"Title": "That's not my fault: Excuses given by players exhibiting in-game intra-team aggressive behavior in online games", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "107045", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Power structure builds gamer loyalty", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "154", "Issue": "", "Page": "113696", "JournalTitle": "Decision Support Systems"}, {"Title": "Real-world demotivation as a predictor of continued video game playing: A study on escapism, anxiety and lack of intrinsic motivation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "101147", "JournalTitle": "Electronic Commerce Research and Applications"}]}, {"ArticleId": 95641062, "Title": "User Preferences of Privacy-Enhancing Attributes of a Smart Speaker", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2022.2101685", "PubYear": 2023, "Volume": "39", "Issue": "18", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Institute for Information and Culture, Korea University, Seoul, Republic of Korea"}, {"AuthorId": 2, "Name": "Jonghwa Park", "Affiliation": "Department of Business Information Education, Kongju National University, Kongju, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Graduate School of Technology and Innovation Management, Ulsan National Institute of Science and Technology, Ulsan, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Media and Communication, Korea University, Seoul, Republic of Korea"}], "References": [{"Title": "Aiming the Mobile Targets in a Cross-Cultural Context: Effects of Trust, Privacy Concerns, and Attitude", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "227", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Privacy and the Internet of Things−An experiment in discrete choice", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "2", "Page": "103292", "JournalTitle": "Information & Management"}, {"Title": "User Requirement Analysis for Smart Voice Technology for Older Adults with Visual Impairments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "16", "Page": "1551", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Listen Only When Spoken To: Interpersonal Communication Cues as Smart Speaker Privacy Controls", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "2", "Page": "251", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "My Smart Speaker is Cool! Perceived Coolness, Perceived Values, and Users’ Attitude toward Smart Speakers", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "6", "Page": "560", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Users’ Cognitive and Affective Response to the Risk to Privacy from a Smart Speaker", "Authors": "Jonghwa Park; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "8", "Page": "759", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Exploring older adults’ perception and use of smart speaker-based voice assistants: A longitudinal study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "124", "Issue": "", "Page": "106914", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 95641170, "Title": "Intelligent Single-Board Computer for Industry 4.0: Efficient Real-Time Monitoring System for Anomaly Detection in CNC Machines", "Abstract": "The Industry 4.0 technology relies on Single-board computers and the Internet of Things (IOT) and Machine Learning (ML). In addition, sensory detectors, controllers, and a communication interface are employed to address the demands of distant supervision and operational management. In today&#x27;s industrial environment, understanding machines and giving effective interpretation and prognostics is a challenging issue. This paper presents an effective on-process identification tool for monitoring and advising the operator based on sensor parameters. The data is analysed with the Fast Fourier Transform (FFT) inference and machine learning strategies to detect the production calibre of an industrial Computer Numerical Control (CNC) machine, including vibration, temperature, humidity, and operating temperatures. The vibration parameter is provided to the FFT algorithm to produce frequency, and the diameter dataset is also provided manually from the hole diameter in the job piece to correctly monitor and inspect the product quality to prognostics to the fault in machines. Improper machine settings cause varied vibrations and changes in parameters, whereas our Industry 4.0 module detects and warns about faulty parameters. The device is put through its trials using three distinct machine learning approaches , and the results are collected. ML combines the outcomes of many baseline estimators to provide better results. This research work utilizes the Linear regression model since it has a high-power detection ability and minimizes variance as well as bias. The Single board computer using FFT and linear regression monitoring gives greater data accuracy of 97.6 percent on comparing the outcomes of 5.4%improved efficiency than K-Nearest Neighbourhood (KNN), 95.5% Random Forest Network (RN), and 95% Support Vector Machine (SVM) algorithms. The proposed system was validated via the deployment of suitable test scenarios, illustrating the technique&#x27;s effectiveness in manufacturing environments.", "Keywords": "", "DOI": "10.1016/j.micpro.2022.104629", "PubYear": 2022, "Volume": "93", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vel Tech Rangarajan Dr <PERSON> R&D Institute of Science and Technology, Chennai. India;Corresponding author"}, {"AuthorId": 2, "Name": "Kanagachidambaresan G R", "Affiliation": "Vel Tech Rangarajan Dr <PERSON> R&D Institute of Science and Technology, Chennai. India"}], "References": [{"Title": "Realization and performance evaluation of a machine tool vibration monitoring module by multiple MEMS accelerometer integrations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "1-2", "Page": "465", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Classification and regression models of audio and vibration signals for machine state monitoring in precision machining systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "45", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 95641177, "Title": "On initials and the fundamental theorem of tropical partial differential algebraic geometry", "Abstract": "Tropical Differential Algebraic Geometry considers difficult or even intractable problems in Differential Equations and tries to extract information on their solutions from a restricted structure of the input. The fundamental theorem of Tropical Differential Algebraic Geometry and its extensions state that the support of power series solutions of systems of ordinary differential equations (with formal power series coefficients over an uncountable algebraically closed field of characteristic zero) can be obtained either, by solving a so-called tropicalized differential system, or by testing monomial-freeness of the associated initial ideals. Tropicalized differential equations work on a completely different algebraic structure which may help in theoretical and computational questions, particularly on the existence of solutions. We show here that both of these methods can be generalized to the case of systems of partial differential equations, this is, one can go either with the solution of tropicalized systems, or test monomial-freeness of the ideal generated by the initials when looking for supports of power series solutions of systems of differential equations, regardless the (finite) number of derivatives. The key are the vertex sets of Newton polytopes, upon which relies the definition of both tropical vanishing condition and the initial of a differential polynomial.", "Keywords": "Differential algebra ; Tropical differential algebraic geometry ; Power series solutions ; Newton polyhedra ; Tropical differential equations ; Initial of differential polynomials", "DOI": "10.1016/j.jsc.2022.08.005", "PubYear": 2023, "Volume": "115", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Research Institute for Symbolic Computation (RISC), Johannes <PERSON>pler University Linz, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": "Centro de Investigación en Matemáticas, A.C. (CIMAT), Jalisco S/N, Col. Valenciana CP. 36023 Guanajuato, Gto, Mexico"}, {"AuthorId": 3, "Name": "Mercedes Haiech", "Affiliation": "Université de Limoges, XLIM UMR CNRS 7252, Université de Limoges, 87060 Limoges, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Bernoulli Institute, University of Groningen, the Netherlands"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Univ. Lille, CNRS, Centrale Lille, UMR 9189 CRIStAL, F-59000 Lille, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Queen Mary University of London, United Kingdom of Great Britain and Northern Ireland"}], "References": [{"Title": "Tropical Differential Gröbner Bases", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "2", "Page": "255", "JournalTitle": "Mathematics in Computer Science"}]}, {"ArticleId": 95641226, "Title": "Flexible dibutyl phthalate aptasensor based on self-powered CNTs-rGO enzymatic biofuel cells", "Abstract": "To meet the emerging demand of portable and on-site assay, a self-powered and flexible split-type aptasensor was constructed based on an enzymatic biofuel cell (EBFC) by using a flexible glucose oxidase (GOD)/Bi<sub>3</sub>Ti<sub>2</sub>O<sub>8</sub>F/carbon nanotubes-reduced graphene oxide (Bi<sub>3</sub>Ti<sub>2</sub>O<sub>8</sub>F/CNTs-rGO) film as the anode and the aptamers fixed AuNPs/CNTs-rGO film as the selective cathode. Bi<sub>3</sub>Ti<sub>2</sub>O<sub>8</sub>F with H<sub>2</sub>O<sub>2</sub> enzyme-mimicking property and three-dimensional structure was used for the immobilization of GOD to improve the performance of the EBFC. The recognition of dibutyl phthalate (DBP) was performed in DBP containing solutions using the aptamer/AuNPs/CNTs-rGO cathode, and the detection was carried out in the other glucose containing solution. The output power density of the EBFC decreased after recognizing DBP because of the hindered electron transfer. The aptasensor responded linearly to dibutyl phthalate (DBP) (1.0 × 10<sup>−14</sup> to 1.0 × 10<sup>−9</sup> mol/L) with a detection limit of 1.99 × 10<sup>−15</sup> mol/L. The bending and stretching tests didn’t influence the output power density of the EBFC, indicating the stability and flexibility of the aptasensor. This sensor had been successfully applied for water sample analysis. It has the potential to be a useful platform for developing implantable, portable, and miniaturized sensors.", "Keywords": "Aptasensor ; Biofuel cells ; Self-powered ; Flexible ; Phthalates", "DOI": "10.1016/j.snb.2022.132468", "PubYear": 2022, "Volume": "371", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Honglan Qin", "Affiliation": "School of Chemistry and Chemical Engineering, Yangzhou University, Yangzhou 225002, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Yangzhou University, Yangzhou 225002, China"}, {"AuthorId": 3, "Name": "Qinghua Yu", "Affiliation": "School of Chemistry and Chemical Engineering, Yangzhou University, Yangzhou 225002, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Yangzhou University, Yangzhou 225002, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, Yangzhou University, Yangzhou 225002, China"}], "References": [{"Title": "Significantly improving the performance of self-powered biosensor by effectively combining with high-energy enzyme biofuel cells, N-doped graphene, and ultrathin hollow carbon shell", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "327", "Issue": "", "Page": "128933", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A self-powered rotating paper-based analytical device for sensing of thrombin", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "351", "Issue": "", "Page": "130917", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 95641387, "Title": "On the kinematic-geometry of one-parameter Lorentzian spatial movement", "Abstract": "<p>In this paper, the relationships among the instantaneous invariants of a one-parameter Lorentzian spatial movement and the local invariants of the axodes are studied. New proofs for <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> formulae are given which demonstrate the elegance and efficiency of the E. Study map in Lorentzian spatial kinematics. Consequently, two spacelike line congruences are introduced and their spatial equivalent are examined in detail.</p>", "Keywords": "Timelike axodes; Line congruence; <PERSON><PERSON><PERSON>’s formula; 53A04; 53A05; 53A17", "DOI": "10.1007/s00170-022-09812-x", "PubYear": 2022, "Volume": "121", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Science and Arts College, King Abdulaziz University, Rabigh, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, University of Assiut, Assiut, Egypt"}], "References": []}, {"ArticleId": 95641427, "Title": "Surface Reconstruction from Point Clouds without Normals by Parametrizing the Gauss Formula", "Abstract": "<p>We propose Parametric Gauss Reconstruction (PGR) for surface reconstruction from point clouds without normals. Our insight builds on the Gauss formula in potential theory, which represents the indicator function of a region as an integral over its boundary. By viewing surface normals and surface element areas as unknown parameters, the Gauss formula interprets the indicator as a member of some parametric function spaces. We can solve for the unknown parameters using the <PERSON><PERSON><PERSON> formula and simultaneously obtain the indicator function. Our method bypasses the need for accurate input normals as required by most existing non-data-driven methods, while also exhibiting superiority over data-driven methods since no training is needed. Moreover, by modifying the Gauss formula and employing regularization, PGR also adapts to difficult cases such as noisy inputs, thin structures, sparse or nonuniform points, for which accurate normal estimation becomes quite difficult. Our code is publicly available at https://github.com/jsnln/ParametricGaussRecon.</p>", "Keywords": "", "DOI": "10.1145/3554730", "PubYear": 2023, "Volume": "42", "Issue": "2", "JournalId": 15014, "JournalTitle": "ACM Transactions on Graphics", "ISSN": "0730-0301", "EISSN": "1557-7368", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> Shi", "Affiliation": "Yau Mathematical Sciences Center, Tsinghua University, China and Yanqi Lake Beijing Institute of Mathematical Sciences and Applications (BIMSA), China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Tsinghua University, China and Beijing National Research Center for Information Science and Technology (BNRIST), China"}], "References": [{"Title": "Poisson Surface Reconstruction with Envelope Constraints", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "5", "Page": "173", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Orienting point clouds with dipole propagation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 95641469, "Title": "Evaluation of a joint replenishment policy with sales-based threshold and stochastic demand", "Abstract": "In this study, we consider a stochastic joint replenishment problem with Poisson demand, where multiple items are replenished jointly through a common supplier. We study a novel type of joint replenishment policy controlled by a sales threshold, under which new orders are placed to restore every item’s inventory position to a base-stock level when the system’s accumulated sales generated since the last order reach a certain threshold. The sales threshold enables our model to capture multiple items’ value heterogeneity that has been ignored by the quantity-based threshold in traditional joint replenishment control policy. We first characterize the inventory dynamics of the joint replenishment model by the semi-Markov chain method and then provide an approach to evaluate the exact long-run average system inventory-related costs. We further provide an approach to optimize the joint replenishment policy. Numerical experiments are conducted to compare the sales-based threshold policy with the traditional quantity-based threshold policy. Our results show that the sales-based threshold policy can significantly outperform the quantity-based threshold policy, especially for a system with a high holding cost rate, short lead time, low fixed ordering cost, and large heterogeneity among products. This is because the sales-based threshold adds an extra dimension to differentiate the products in addition to the use of different stock levels for multiple items. Our results help practitioners understand the underlying factors that drive the effectiveness of the sales-based threshold policy.", "Keywords": "Inventory control ; Cost evaluation ; Joint replenishment problem ; Sales-based threshold ; Semi-Markov chain", "DOI": "10.1016/j.cie.2022.108525", "PubYear": 2022, "Volume": "172", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Guangyu Wan", "Affiliation": "School of Economics & Trade, Hunan University, 410082, China;Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Business School, Central South University, 410083, China;Corresponding author at: Business School, Central South University, 410083, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100190, China;Center for Forecasting Science, Chinese Academy of Sciences, Beijing 100190, China"}], "References": []}, {"ArticleId": 95641470, "Title": "Group decision making based on weighted distance measure of linguistic intuitionistic fuzzy sets and the TOPSIS method", "Abstract": "Linguistic intuitionistic fuzzy numbers (LIFNs) are useful to express the uncertainty of qualitative aspects of information, which have received many attentions in recent years. In this paper, we propose the distance measure of linguistic intuitionistic fuzzy sets (LIFSs), where the membership grade and the non-membership grade of each element in the universe of discourse belonging to a LIFS are represented by LIFNs. We also provide the proofs of the validity and some desirable properties of the proposed distance measure of LIFSs. Moreover, we also propose the weighted distance measure of LIFSs. Based on the proposed weighted distance measure of LIFSs and the “Technique for Order Preference by Similarity to Ideal Solution” (TOPSIS) method, we propose a new group decision making (GDM) approach in the environments of LIFNs. We also use some examples to illustrate the practicability and the feasibility of the proposed GDM approach. The proposed GDM approach can overcome the drawbacks of the existing GDM approaches, where they have the drawbacks that they cannot distinguish the ranking orders of the alternatives in some situations. It provides us a very useful method for dealing with GDM problems in the environments of LIFNs.", "Keywords": "", "DOI": "10.1016/j.ins.2022.07.184", "PubYear": 2022, "Volume": "611", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Amity University Haryana, Gurugram, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National Taiwan University of Science and Technology, Taipei, Taiwan;Corresponding author"}], "References": [{"Title": "Multiattribute group decision making based on interval-valued Pythagorean fuzzy Einstein geometric aggregation operators", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "3", "Page": "361", "JournalTitle": "Granular Computing"}, {"Title": "New approach to multiple attribute group decision-making based on Pythagorean fuzzy Einstein hybrid geometric operator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "3", "Page": "349", "JournalTitle": "Granular Computing"}, {"Title": "Arithmetic operations on normal semi elliptic intuitionistic fuzzy numbers and their application in decision-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "1", "Page": "163", "JournalTitle": "Granular Computing"}, {"Title": "Supplier selection using a flexible interval-valued fuzzy VIKOR", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "4", "Page": "485", "JournalTitle": "Granular Computing"}, {"Title": "A series of generalized induced Einstein aggregation operators and their application to group decision-making process based on Pythagorean fuzzy numbers", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "2", "Page": "241", "JournalTitle": "Granular Computing"}, {"Title": "Group decision-making approach under multi (Q, N)-soft multi granulation rough model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "2", "Page": "339", "JournalTitle": "Granular Computing"}, {"Title": "Aggregation operators on cubic linguistic hesitant fuzzy numbers and their application in group decision-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "2", "Page": "303", "JournalTitle": "Granular Computing"}, {"Title": "Group decision making based on multiplicative consistency and consensus of fuzzy linguistic preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "71", "JournalTitle": "Information Sciences"}, {"Title": "Multiattribute group decision making based on intuitionistic fuzzy partitioned Maclaurin symmetric mean operators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "830", "JournalTitle": "Information Sciences"}, {"Title": "Multiple attribute group decision making based on weighted aggregation operators of triangular neutrosophic cubic fuzzy numbers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "2", "Page": "421", "JournalTitle": "Granular Computing"}, {"Title": "Group decision making with hesitant fuzzy linguistic preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "514", "Issue": "", "Page": "354", "JournalTitle": "Information Sciences"}, {"Title": "Multiattribute group decision making based on neutrality aggregation operators of q-rung orthopair fuzzy sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "427", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making with incomplete intuitionistic multiplicative preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "516", "Issue": "", "Page": "560", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making with heterogeneous intuitionistic fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "523", "Issue": "", "Page": "197", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making based on acceptable multiplicative consistency of hesitant fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "524", "Issue": "", "Page": "77", "JournalTitle": "Information Sciences"}, {"Title": "Picture fuzzy Choquet integral-based VIKOR for multicriteria group decision-making problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "3", "Page": "587", "JournalTitle": "Granular Computing"}, {"Title": "Group decision making under social influences based on information entropy", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "3", "Page": "303", "JournalTitle": "Granular Computing"}, {"Title": "Group decision making based on acceptable consistency analysis of interval linguistic hesitant fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "530", "Issue": "", "Page": "66", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making based on acceptable multiplicative consistency and consensus of hesitant fuzzy linguistic preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "541", "Issue": "", "Page": "531", "JournalTitle": "Information Sciences"}, {"Title": "Generalized intuitionistic fuzzy aggregation operators based on confidence levels for group decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "867", "JournalTitle": "Granular Computing"}, {"Title": "Induced generalized pythagorean fuzzy aggregation operators and their application based on t-norm and t-conorm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "887", "JournalTitle": "Granular Computing"}, {"Title": "Multiattribute group decision making based on interval-valued neutrosophic N-soft sets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "1009", "JournalTitle": "Granular Computing"}, {"Title": "Optimization-based group decision making using interval-valued intuitionistic fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "561", "Issue": "", "Page": "352", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making based on consistency and consensus analysis of dual multiplicative linguistic preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "572", "Issue": "", "Page": "590", "JournalTitle": "Information Sciences"}, {"Title": "Group decision making based on multiplicative consistency-and-consensus preference analysis for incomplete q-rung orthopair fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "653", "JournalTitle": "Information Sciences"}, {"Title": "A framework for group decision making with multiplicative trapezoidal fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "577", "Issue": "", "Page": "722", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 95641481, "Title": "Codebook-softened product quantization for high accuracy approximate nearest neighbor search", "Abstract": "Product quantization (PQ) is a fundamental technique for approximate nearest neighbor (ANN) search in many applications such as information retrieval, computer vision and pattern recognition. In the existing PQ-based methods for approximate nearest neighbor search, the reachable best search accuracy is achieved by using fixed codebooks. The search performance is limited by the quality of the hard codebooks. Unlike the existing methods, in this paper, we present a novel codebook-softened product quantization (CSPQ) method to achieve more quantization levels by softening codebooks. We firstly analyze how well the database vectors match the trained codebooks by examining quantization error for each database vector, and select the bad-matching database vectors. Then, we give the trained codebooks b -bit freedom to soften codebooks. Finally, by minimizing quantization errors, the bad-matching vectors are encoded by softened codebooks and the labels of best-matching codebooks are recorded. Experimental results on SIFT1M, GIST1M and SIFT10M show that, despite its simplicity, our proposed method achieves higher accuracy compared with PQ and it can be combined with other non-exhaustive frameworks to achieve fast search.", "Keywords": "Approximate nearest neighbor search ; Product quantization ; Vector quantization ; High-dimensional space ; Soften codebooks", "DOI": "10.1016/j.neucom.2022.08.002", "PubYear": 2022, "Volume": "507", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, Xi’an Jiaotong University, Xi’an, Shaanxi 710049, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, Xi’an Jiaotong University, Xi’an, Shaanxi 710049, PR China;Zhengzhou Xinda Institute of Advanced Technology, Zhengzhou 450002, PR China;Research Institute of Xi’an Jiaotong University, Zhejiang 311215, PR China;Corresponding author at: Faculty of Electronic and Information Engineering, Xi’an Jiaotong University, Xi’an, Shaanxi 710049, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, Xi’an Jiaotong University, Xi’an, Shaanxi 710049, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Information Science, Xi’an University of Technology, Xi’an, Shaanxi 710048, PR China"}], "References": [{"Title": "A new fast search algorithm for exact k-nearest neighbors based on optimal triangle-inequality-based check strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "189", "Issue": "", "Page": "105088", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Product quantization with dual codebooks for approximate nearest neighbor search", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "401", "Issue": "", "Page": "59", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 95641483, "Title": "Optimal decisions in a dual-channel competitive green supply chain management under promotional effort", "Abstract": "The awareness of the environment has been extensively studied in the past decade. In this study, the production of eco-friendly and comparatively less harmful green innovative products is considered under an uncertain environment to reduce their detrimental effect on the environment under green supply chain management. Owing to the complexity of green innovation, the various pricing decisions of the players, green innovation level, and promotional effort under the centralized, manufacturer Stackelberg, and vertical Nash policies are studied. The relations among the parameters are analytically investigated such that the profits are optimized for various cases. The optimal level of green innovation, promotional effort, prices, and profits are achieved by varying the market potential and price parameters. It is also observed that the cost coefficients of green innovation and promotional effort have the highest effect on the optimal level. Green innovation is very effective in improving the players’ profit margin, and the manufacturer must decide the extent of green innovation to optimize the profits. The optimal level of the promotional effort for the various cases is determined such that the players can gain the most. It is found from the study that a dual-channel supply chain is more efficient than a single-channel supply chain for green products.", "Keywords": "Green innovation ; Promotional effort ; Interval-valued parameter ; Supply chain ; Dual-channel", "DOI": "10.1016/j.eswa.2022.118315", "PubYear": 2023, "Volume": "211", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, The University of Burdwan, Burdwan 713104, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, The University of Burdwan, Burdwan 713104, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Yonsei University, 50 Yonsei-ro, Sinchon-dong, Seodaemun-gu, Seoul 03722, South Korea;Center for Transdisciplinary Research (CFTR), Saveetha Dental College, Saveetha Institute of Medical and Technical Sciences, Saveetha University, 162, Poonamallee High Road, Velappanchavadi, Chennai, Tamil Nadu 600077, India;Corresponding author"}], "References": [{"Title": "A sustainable flexible manufacturing–remanufacturing model with improved service and green investment under variable demand", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "117154", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 95641486, "Title": "Machine-learning-aided identification of steroid hormones based on the anisotropic galvanic replacement generated sensor array", "Abstract": "The conveniently simultaneous identification of steroid hormones is challenging due to their similar chemical structures. Herein, we have established a machine-learning-aided sensor array for accurate discrimination and determination of steroid hormones based on [email&#160;protected] <sub>2</sub>O (CC), [email&#160;protected] <sub>2</sub> [email&#160;protected] (CCP) and [email&#160;protected] <sub>2</sub> [email&#160;protected] (CCPA). The Michaelis-Menten constant of CC, CCP and CCPA toward H<sub>2</sub>O<sub>2</sub> were calculated as 2.61, 1.11 and 2.03 mM, respectively, indicating their different catalytic activities that were obtained from the anisotropic galvanic replacement of [email&#160;protected] <sub>2</sub>O with Pd (II) and Au (III). Five kinds of steroid hormones were selected as model targets and reacted with the sensor array before chromogenic reaction. The absorption of chromogenic substrate was used as learning data to train the k -nearest neighbors algorithm, the discrimination confidence was from 88.9% to 100% for different mixtures, and 100% for betamethasone of 1–50 μM in real sample. This work provides a quick analysis of steroid hormones in 1.5 h and low-cost strategy, its further application in the field of cosmetic safety is highly expected.", "Keywords": "Sensor array ; Machine learning ; Anisotropic galvanic replacement ; Steroid hormones", "DOI": "10.1016/j.snb.2022.132470", "PubYear": 2022, "Volume": "370", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NMPA Key Laboratory for Research and Evaluation of Drug Metabolism, Guangdong Provincial Key Laboratory of New Drug Screening, School of Pharmaceutical Sciences, Southern Medical University, Guangzhou 510515, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "NMPA Key Laboratory for Research and Evaluation of Drug Metabolism, Guangdong Provincial Key Laboratory of New Drug Screening, School of Pharmaceutical Sciences, Southern Medical University, Guangzhou 510515, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Chen", "Affiliation": "School of Chemistry and Chemical Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "NMPA Key Laboratory for Research and Evaluation of Drug Metabolism, Guangdong Provincial Key Laboratory of New Drug Screening, School of Pharmaceutical Sciences, Southern Medical University, Guangzhou 510515, China;Guangdong Provincial Key Laboratory of Cardiac Function and Microcirculation, Southern Medical University, Guangzhou 510515, China;Corresponding author at: NMPA Key Laboratory for Research and Evaluation of Drug Metabolism, Guangdong Provincial Key Laboratory of New Drug Screening, School of Pharmaceutical Sciences, Southern Medical University, Guangzhou 510515, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, South China University of Technology, Guangzhou 510641, China;Corresponding author"}], "References": [{"Title": "Lanthanide ions as sensor elements based sensor array for colorimetric identification of antioxidants", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "305", "Issue": "", "Page": "127532", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Nanomaterial-based sensors and biosensors for enhanced inorganic arsenic detection: A functional perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "315", "Issue": "", "Page": "128100", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Two-dimensional MoS2-based impedimetric electronic tongue for the discrimination of endocrine disrupting chemicals using machine learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Murilo H.M. F<PERSON>", "PubYear": 2021, "Volume": "336", "Issue": "", "Page": "129696", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 95641531, "Title": "Big data analytics in Cloud computing: an overview", "Abstract": "<p>Big Data and Cloud Computing as two mainstream technologies, are at the center of concern in the IT field. Every day a huge amount of data is produced from different sources. This data is so big in size that traditional processing tools are unable to deal with them. Besides being big, this data moves fast and has a lot of variety. Big Data is a concept that deals with storing, processing and analyzing large amounts of data. Cloud computing on the other hand is about offering the infrastructure to enable such processes in a cost-effective and efficient manner. Many sectors, including among others businesses (small or large), healthcare, education, etc. are trying to leverage the power of Big Data. In healthcare, for example, Big Data is being used to reduce costs of treatment, predict outbreaks of pandemics, prevent diseases etc. This paper, presents an overview of Big Data Analytics as a crucial process in many fields and sectors. We start by a brief introduction to the concept of Big Data, the amount of data that is generated on a daily bases, features and characteristics of Big Data. We then delve into Big Data Analytics were we discuss issues such as analytics cycle, analytics benefits and the movement from ETL to ELT paradigm as a result of Big Data analytics in Cloud. As a case study we analyze Google's BigQuery which is a fully-managed, serverless data warehouse that enables scalable analysis over petabytes of data. As a Platform as a Service (PaaS) supports querying using ANSI SQL. We use the tool to perform different experiments such as average read, average compute, average write, on different sizes of datasets.</p><p>© The Author(s) 2022.</p>", "Keywords": "Analytics;Big data;BigQuery;Cloud computing", "DOI": "10.1186/s13677-022-00301-w", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, Department of Computer Engineering, University of Prishtina, 10000 Prishtina, Kosovo."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, Department of Computer Engineering, University of Prishtina, 10000 Prishtina, Kosovo."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, Department of Computer Engineering, University of Prishtina, 10000 Prishtina, Kosovo."}], "References": []}, {"ArticleId": 95641686, "Title": "First-principles LCPAO approach for insulators under finite electric fields with forces", "Abstract": "We propose a linear-combination-of-pseudo-atomic-orbitals scheme for a finite electric field method based on the modern theory of polarization. We derive the matrix elements of the effective potential for the static and homogeneous field and the corresponding terms of the forces on atoms. In addition, we successfully evaluated the static and electronic dielectric constants and Born effective charges of typical semiconducting and insulating materials. Our formalism will aid in the study of materials under electric fields.", "Keywords": "", "DOI": "10.1016/j.cpc.2022.108487", "PubYear": 2022, "Volume": "280", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Nanomaterials Research Institute (NanoMaRi), Kanazawa University, Kakuma-machi, Kanazawa, 920-1192, Japan;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanomaterials Research Institute (NanoMaRi), Kanazawa University, Kakuma-machi, Kanazawa, 920-1192, Japan;Corresponding authors"}], "References": []}, {"ArticleId": 95641713, "Title": "A novel solution of deep learning for enhanced support vector machine for predicting the onset of type 2 diabetes", "Abstract": "<p>Type 2 Diabetes is one of the most major and fatal diseases known to human beings, where thousands of people are subjected to the onset of Type 2 Diabetes every year. However, the diagnosis and prevention of Type 2 Diabetes are relatively costly in today’s scenario; hence, the use of machine learning and deep learning techniques is gaining momentum for predicting the onset of Type 2 Diabetes. This research aims to increase the accuracy and Area Under the Curve (AUC) metric while improving the processing time for predicting the onset of Type 2 Diabetes. The proposed system consists of a deep learning technique that uses the Support Vector Machine (SVM) algorithm along with the Radial Base Function (RBF) along with the Long Short-term Memory Layer (LSTM) for prediction of onset of Type 2 Diabetes. The proposed solution provides an average accuracy of 86.31% and an average AUC value of 0.8270 or 82.70%, with an improvement of 3.8 milliseconds in the processing. Radial Base Function (RBF) kernel and the LSTM layer enhance the prediction accuracy and AUC metric from the current industry standard, making it more feasible for practical use without compromising the processing time.</p>", "Keywords": "Deep learning; Type 2 diabetes; Support vector machine; Radial base function; Long short-term memory; Electronic health record", "DOI": "10.1007/s11042-022-13582-9", "PubYear": 2023, "Volume": "82", "Issue": "4", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing Mathematics and Engineering, Charles <PERSON> University (CSU), Sydney, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Islamic Sciences, Al Iraqia University, Baghdad, Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing Mathematics and Engineering, Charles <PERSON> University (CSU), Sydney, Australia; School of Computer Data and Mathematical Sciences, Western Sydney University (WSU), Sydney, Australia; Kent Institute Australia, Sydney, Australia; Asia Pacific International College (APIC), Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>in", "Affiliation": "School of Computing Mathematics and Engineering, Charles <PERSON> University (CSU), Sydney, Australia; School of Computer Data and Mathematical Sciences, Western Sydney University (WSU), Sydney, Australia; Kent Institute Australia, Sydney, Australia; Asia Pacific International College (APIC), Sydney, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, University of Kurdistan Hewler, Erbil, Iraq"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Computing Mathematics and Engineering, Charles <PERSON> University (CSU), Sydney, Australia; School of Computer Data and Mathematical Sciences, Western Sydney University (WSU), Sydney, Australia; Kent Institute Australia, Sydney, Australia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Faculty of Medicine, University of New South Wales, Sydney, Australia"}], "References": [{"Title": "Machine Learning in Python: Main Developments and Technology Trends in Data Science, Machine Learning, and Artificial Intelligence", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "193", "JournalTitle": "Information"}, {"Title": "Prediction of Type 2 Diabetes using Machine Learning Classification Methods", "Authors": "Neha Prerna Tigga; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "706", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 95641724, "Title": "Modal complementary fusion network for RGB-T salient object detection", "Abstract": "<p>RGB-T salient object detection (SOD) combines thermal infrared and RGB images to overcome the light sensitivity of RGB images in low-light conditions. However, the quality of RGB-T images could be unreliable under complex imaging scenarios, and direct fusion of these low-quality images will lead to sub-optimal detection results. In this paper, we propose a novel Modal Complementary Fusion Network (MCFNet) to alleviate the contamination effect of low-quality images from both global and local perspectives. Specifically, we design a modal reweight module (MRM) to evaluate the global quality of images and adaptively reweight RGB-T features by explicitly modelling interdependencies between RGB and thermal images. Furthermore, we propose a spatial complementary fusion module (SCFM) to explore the complementary local regions between RGB-T images and selectively fuse multi-modal features. Finally, multi-scale features are fused to obtain the salient detection result. Experiments on three RGB-T benchmark datasets demonstrate that our MCFNet achieved outstanding performance compared with the latest state-of-the-art methods. We have also achieved competitive results in RGB-D SOD tasks, which proves the generalization of our method. The source code is released at https://github.com/dotaball/MCFNet .</p>", "Keywords": "RGB-T salient object detection; Image quality; Modality reweight; Spatial complementary fusion", "DOI": "10.1007/s10489-022-03950-1", "PubYear": 2023, "Volume": "53", "Issue": "8", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering & Automation, Northeastern University, Shenyang, China; Key Laboratory of Vibration and Control of Aero-Propulsion Systems Ministry of Education of China, Northeastern University, Shenyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering & Automation, Northeastern University, Shenyang, China; Key Laboratory of Vibration and Control of Aero-Propulsion Systems Ministry of Education of China, Northeastern University, Shenyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering & Automation, Northeastern University, Shenyang, China; Key Laboratory of Vibration and Control of Aero-Propulsion Systems Ministry of Education of China, Northeastern University, Shenyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering & Automation, Northeastern University, Shenyang, China; Key Laboratory of Vibration and Control of Aero-Propulsion Systems Ministry of Education of China, Northeastern University, Shenyang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering & Automation, Northeastern University, Shenyang, China; Key Laboratory of Vibration and Control of Aero-Propulsion Systems Ministry of Education of China, Northeastern University, Shenyang, China"}], "References": [{"Title": "STA-Net: spatial-temporal attention network for video salient object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "6", "Page": "3450", "JournalTitle": "Applied Intelligence"}, {"Title": "Depth quality-aware selective saliency fusion for RGB-D image salient object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "432", "Issue": "", "Page": "44", "JournalTitle": "Neurocomputing"}, {"Title": "Non-local duplicate pooling network for salient object detection", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "10", "Page": "6881", "JournalTitle": "Applied Intelligence"}, {"Title": "Unsupervised RGB-T saliency detection by node classification distance and sparse constrained graph learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "1", "Page": "1030", "JournalTitle": "Applied Intelligence"}, {"Title": "Global contextual guided residual attention network for salient object detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "6", "Page": "6208", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 95641740, "Title": "A novel enhanced convolution neural network with extreme learning machine: facial emotional recognition in psychology practices", "Abstract": "<p>Facial emotional recognition is one of the essential tools used by recognition psychology to diagnose patients. Face and facial emotional recognition are areas where machine learning is excelling. Facial Emotion Recognition in an unconstrained environment is an open challenge for digital image processing due to different environments, such as lighting conditions, pose variation, yaw motion, and occlusions. Deep learning approaches have shown significant improvements in image recognition. However, accuracy and time still need improvements. This research aims to improve facial emotion recognition accuracy during the training session and reduce processing time using a modified Convolution Neural Network Enhanced with Extreme Learning Machine (CNNEELM). The proposed system consists of an optical flow estimation technique that detects the motion of change in facial expression and extracts peak images from video frames for image pre-processing. The system entails (CNNEELM) improving the accuracy in image registration during the training session. Furthermore, the system recognizes six facial emotions – happy, sad, disgust, fear, surprise, and neutral with the proposed CNNEELM model. The study shows that the overall facial emotion recognition accuracy is improved by 2% than the state of art solutions with a modified Stochastic Gradient Descent (SGD) technique. With the Extreme Learning Machine (ELM) classifier, the processing time is brought down to 65 ms from 113 ms, which can smoothly classify each frame from a video clip at 20fps. With the pre-trained InceptionV3 model, the proposed CNNEELM model is trained with JAFFE, CK+, and FER2013 expression datasets. The simulation results show significant improvements in accuracy and processing time, making the model suitable for the video analysis process. Besides, the study solves the issue of the large processing time required to process the facial images.</p>", "Keywords": "Convolution neural network; Stochastic gradient descent; Log-likelihood estimator; Optical flow estimation; Extreme learning machine; Cross-entropy loss", "DOI": "10.1007/s11042-022-13567-8", "PubYear": 2023, "Volume": "82", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing Mathematics and Engineering, Charles <PERSON> University (CSU), Bathurst, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing Mathematics and Engineering, Charles <PERSON> University (CSU), Bathurst, Australia; School of Computer Data and Mathematical Sciences, Western Sydney University (WSU), Sydney, Australia; Kent Institute Australia, Sydney, Australia; Asia Pacific International College (APIC), Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Computing Mathematics and Engineering, Charles <PERSON> University (CSU), Bathurst, Australia; School of Computer Data and Mathematical Sciences, Western Sydney University (WSU), Sydney, Australia; Kent Institute Australia, Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computing Mathematics and Engineering, Charles <PERSON> University (CSU), Bathurst, Australia; School of Computer Data and Mathematical Sciences, Western Sydney University (WSU), Sydney, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, University of Kurdistan Hewler, Erbil, Iraq"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Islamic Sciences, Al Iraqia University, Baghdad, Iraq"}], "References": []}, {"ArticleId": 95641757, "Title": "Event-triggered output feedback dissipative control of nonlinear systems under DoS attacks and actuator saturation", "Abstract": "This paper presents a novel event-triggered dynamic output feedback dissipative control of nonlinear systems under intermittent denial-of-service (DoS) attacks and actuator saturation. Firstly, based on attack information, a secure event-triggered mechanism (ETM) is introduced, which not only saves systems resources but also is Zeno-free and resilient to DoS attacks. Secondly, a switched T–S fuzzy closed-loop system model is built, which unifies the parameters of nonlinear plant, noises, ETM, DoS attacks, switched output feedback fuzzy controller, and actuator saturation all in one framework. Thirdly, low conservative exponential stability criteria are derived while guaranteeing strict ( G , H , I ) -dissipativity, and hence the relationships between system performance and factors such as DoS attacks, secure ETM, noises and actuator saturation are established. Further, sufficient conditions are given for the co-design of the switched output-based fuzzy controller and the secure ETM. Finally, the effectiveness of the proposed method is confirmed by numerical examples, achieving over 92% system resources.", "Keywords": "Networked control systems ; T–S fuzzy systems ; secure event-triggered mechanism ; intermittent denial-of-service attacks ; dynamic output feedback control ; dissipative control", "DOI": "10.1080/00207721.2022.2083260", "PubYear": 2022, "Volume": "53", "Issue": "16", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Sciences, Henan Agricultural University, Zhengzhou, People's Republic of China;School of Electronic and Electrical Engineering, University of Leeds, Leeds, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electronic and Electrical Engineering, University of Leeds, Leeds, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, People's Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, People's Republic of China"}], "References": [{"Title": "Output-based event-triggered resilient control of uncertain NCSs under DoS attacks and quantisation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "14", "Page": "2582", "JournalTitle": "International Journal of Systems Science"}, {"Title": "The construction of augmented Lyapunov-Krasovskii functionals and the estimation of their derivatives in stability analysis of time-delay systems: a survey", "Authors": "Xi<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "12", "Page": "2480", "JournalTitle": "International Journal of Systems Science"}]}, {"ArticleId": 95641868, "Title": "User-guided motion planning with reinforcement learning for human-robot collaboration in smart manufacturing", "Abstract": "In today’s manufacturing system, robots are expected to perform increasingly complex manipulation tasks in collaboration with humans. However, current industrial robots are still largely preprogrammed with very little autonomy and still required to be reprogramed by robotics experts for even slightly changed tasks. Therefore, it is highly desirable that robots can adapt to certain task changes with motion planning strategies to easily work with non-robotic experts in manufacturing environments. In this paper, we propose a user-guided motion planning algorithm in combination with reinforcement learning (RL) method to enable robots automatically generate their motion plans for new tasks by learning from a few kinesthetic human demonstrations. Features of common human demonstrated tasks in a specific application environment, e.g., desk assembly or warehouse loading/unloading are abstracted and saved in a library. The definition of semantical similarity between features in the library and features of a new task is proposed and further used to construct the reward function in RL. To achieve an adaptive motion plan facing task changes or new task requirements, features embedded in the library are mapped to appropriate task segments based on the trained motion planning policy using Q-learning. A new task can be either learned as a combination of a few features in the library or a requirement for further human demonstration if the current library is insufficient for the new task. We evaluate our approach on a 6 DOF UR5e robot on multiple tasks and scenarios and show the effectiveness of our method with respect to different scenarios.", "Keywords": "Human-robot collaboration ; Learning from demonstration ; Motion planning ; Reinforcement learning", "DOI": "10.1016/j.eswa.2022.118291", "PubYear": 2022, "Volume": "209", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, University of Virginia, Charlottesville, VA 22904, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, University of Virginia, Charlottesville, VA 22904, United States;Corresponding author"}], "References": [{"Title": "Trajectory planning for multi-robot systems: Methods and applications", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "114660", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Semantic segmentation based stereo visual servoing of nonholonomic mobile robot in intelligent manufacturing environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "190", "Issue": "", "Page": "116203", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 95641887, "Title": "An ultra-compact and high-speed FFT-based large-integer multiplier for fully homomorphic encryption using a dual spike-based arithmetic circuit over GF(p)", "Abstract": "During last years, fully homomorphic encryption (FHE) has attracted great interest since enables computation on encrypted data and thus can be considered as a potential solution in advanced applications, such as privacy-preserving cloud computing, genomics, electronic voting and bio-metric authentication. Nowadays, software simulations of the FHE schemes on general purpose computers are extremely slow. Therefore, the development of specific hardware architectures open up new horizons in efficient simulation of FHE schemes. Until date, the implementation of FHE schemes in embedded devices remain impractical due to very high processing time and area consumption required to process very large-integer numbers. Specifically, the development of efficient very large-integer finite-field adder and multiplier potentially allows the throughput and area consumption to be improved since these circuits are the most used components in the computation of FHE schemes. This work presents, for the first time, the development of a compact and highly dual finite-field circuit based on spiking neural P systems (SN P), dendritic growth, dendritic pruning, communication on request and structural plasticity. Specifically, this circuit performs either the finite-field addition or multiplication of two variable integers by only reconnecting their synapses dynamically. Hence, the proposed circuit performs both operations employing the same neural network. In this way, we achieve a significant improvement in terms of area consumption and throughput. Since the computation of FHE schemes requires the multiplication of very large-integer numbers, we use the proposed dual finite-field circuit as the basic processing unit to create a very large-integer multiplier based on fast Fourier transform (FFT). Specifically, the creation of the proposed very large-integer multiplier has allowed us to accelerate the key generation and encryption processes involved in the computation of FHE algorithm. This multiplier was implemented in scalable compact neuromorphic architecture, which mimic the dynamic dendritic phenomena, such as dendritic growth and dendritic pruning. To achieve this, we propose a dynamic multiplexing mechanism based on simple multiplexers and an optimized control unit. This have allowed us to significantly improve the area consumption compared with previously reported solution.", "Keywords": "Finite-field adder ; Finite-field adder multiplier ; Spiking neural P system ; Dendritic delay ; Dendritic growth ; Dendritic pruning ; Dendritic delays ; Dendritic feedback ; Astrocyte-like control ; FPGA ; Fast fourier transform", "DOI": "10.1016/j.neucom.2022.08.020", "PubYear": 2022, "Volume": "507", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto Politecnico Nacional ESIME Culhuacan, Av. Santana N 1000, Coyoacan, 04260 Ciudad de Mexico, Mexico"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instituto Politecnico Nacional ESIME Culhuacan, Av. Santana N 1000, Coyoacan, 04260 Ciudad de Mexico, Mexico"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto Politecnico Nacional ESIME Culhuacan, Av. Santana N 1000, Coyoacan, 04260 Ciudad de Mexico, Mexico"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Instituto Politecnico Nacional ESIME Culhuacan, Av. <PERSON> N 1000, Coyoacan, 04260 Ciudad de Mexico, Mexico;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Instituto Politecnico Nacional ESIME Culhuacan, Av. <PERSON> N 1000, Coyoacan, 04260 Ciudad de Mexico, Mexico;Corresponding authors"}], "References": [{"Title": "Spiking neural P systems with inhibitory rules", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "188", "Issue": "", "Page": "105064", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Spiking neural P systems with target indications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "862", "Issue": "", "Page": "250", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Homogeneous spiking neural P systems with structural plasticity", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "1", "Page": "10", "JournalTitle": "Journal of Membrane Computing"}, {"Title": "Spiking neural P systems with autapses", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "570", "Issue": "", "Page": "383", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 95641988, "Title": "Moire compensation in 2D colorimeter using adaptive spatial-frequency filter", "Abstract": "The paper describes effective compensation of moire in a two-dimensional (2D) colorimeter using CMOS sensor without an Optical Low Pass Filter (OLPF) for display measurements. First, frequency characteristics of moire were modeled using a conventional system theory, and based on this moire model, a measured moire was characterized experimentally using the discrete Fourier transform (DFT). A detected moire in the frequency domain will be then compensated using spatial and frequency filters with minimizing loss of image details of displays, such as pixel-, line-defects and local variations in tristimulus values. Result images of the proposed method were compared with moire-free reference images. Experimental results have shown that the proposed method has sufficient performance to be used in high-precision imaging colorimeters.", "Keywords": "Moire compensation ; 2D Colorimeter ; Imaging Colorimeter ; Aliasing", "DOI": "10.1016/j.displa.2022.102280", "PubYear": 2022, "Volume": "74", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, TECH University of Korea, Republic of Korea;Corresponding author at: Department of Electronic Engineering, TECH University of Korea, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, TECH University of Korea, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, TECH University of Korea, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "R&D Center, ANI Co., Ltd, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "R&D Center, ANI Co., Ltd, Republic of Korea"}], "References": [{"Title": "Analysis and prediction of Moiré pattern on metal mesh touch screen based on two dimensional Fourier transformation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "", "Page": "102060", "JournalTitle": "Displays"}]}, {"ArticleId": 95641990, "Title": "Design of data feature-driven 1D/2D convolutional neural networks classifier for recycling black plastic wastes through laser spectroscopy", "Abstract": "In this study, two types of convolutional neural network (CNN) classifiers are designed to handle the problem of classifying black plastic wastes. In particular, the black plastic wastes have the property of absorbing laser light coming from spectrometer. Therefore, the classification of black plastic wastes remains still a challenging problem compared to classifying other colored plastic wastes using existing spectroscopy (i.e., NIR). When it comes the classification problem of black plastic wastes, effective classification techniques by the laser spectroscopy of Fourier Transform-Infrared Radiation (FT-IR) with Attenuated Total Reflectance (ATR) and Raman to analyze the classification problem of black plastic wastes are introduced. Due to the strong ability of extracting spatial features and remarkable performance in image classification, 1D and 2D CNN through data features are designed as classifiers. The technique of chemical peak points selection is considered to reduce data redundancy. Furthermore, through the selection of data features based on the extracted 1D data with peak points is introduced. Experimental results demonstrate that 2DCNN classifier designed with the help of 2D data feature selection as well as 1DCNN classifier shows the best performance compared with other reported methods for classifying black plastic wastes.", "Keywords": "Classification of Black Plastic Wastes ; ATR FT-IR ; Raman ; Laser Spectroscopy ; 1D/2D CNN ; 2D Data Feature Selection ; CNN Convolutional neural networks ; NIR Near-infrared ; ATR Attenuated total reflectance ; FT-IR Fourier transform-infrared radiation ; 1/2D One/two dimensional ; PVC Polyvinyl chloride ; PET polyethylene terephthalate ; PP Polypropylene ; PE Polyethylene ; PS Polystyrene ; LIBS Laser-induced breakdown spectroscopy ; RBF Radial basis function ; FRBFNN Fuzzy radial basis function neural network ; MIR Mid-infrared ; AFS ATR FT-IR spectrometer ; RS Raman spectrometer ; SVM Support vector machines ; KNN K-nearest neighbor ; PCA Principal component analysis ; MLP Multilayer perception ; DNA Deoxyribo nucleic acid ; ReLU Rectified linear unit ; RGBA Red green blue alpha ; LSTM Long short-term memory ; RNN Recurrent neural networks", "DOI": "10.1016/j.aei.2022.101695", "PubYear": 2022, "Volume": "53", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical & Electronic Engineering, The University of Suwon, San 2-2 Wau-r<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Gyeonggi-do 445-743, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical & Electronic Engineering, The University of Suwon, San 2-2 <PERSON><PERSON>-<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Gyeonggi-do 445-743, South Korea;Research Center for Big Data and Artificial Intelligence, Linyi University, Linyi 276005, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical & Computer Engineering, University of Alberta, Edmonton T6R 2V4 AB, Canada;Department of Electrical and Computer Engineering, Faculty of Engineering, King Abdulaziz University, Jeddah 21589, Saudi Arabia;Systems Research Institute, Polish Academy of Sciences, Warsaw, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation and Electrical Engineering, Linyi University, Linyi 276000, Shandong, China;Key Laboratory of Complex Systems and Intelligent Computing in University of Shandong, Linyi University, Linyi 276000, Shandong, China;Corresponding authors"}, {"AuthorId": 5, "Name": "Zunwei Fu", "Affiliation": "Research Center for Big Data and Artificial Intelligence, Linyi University, Linyi 276005, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "TOP'S EnT Co., Ltd., South Korea"}], "References": [{"Title": "A combination model based on transfer learning for waste classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "19", "Page": "e5751", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "A neurophysiological approach to assess training outcome under stress: A virtual reality experiment of industrial shutdown maintenance using Functional Near-Infrared Spectroscopy (fNIRS)", "Authors": "<PERSON><PERSON> Shi; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "", "Page": "101153", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "The use of decision tree based predictive models for improving the culvert inspection process", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101203", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Bottom-up image detection of water channel slope damages based on superpixel segmentation and support vector machine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101205", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A human-centred approach based on functional near-infrared spectroscopy for adaptive decision-making in the air traffic control environment: A case study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101325", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Intelligent fault recognition framework by using deep reinforcement learning with one dimension convolution and improved actor-critic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101315", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A new Feature-Fusion method based on training dataset prototype for surface defect recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "", "Page": "101392", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "An improved convolutional neural network with an adaptable learning rate towards multi-signal fault diagnosis of hydraulic piston pump", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "", "Page": "101406", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 95642019, "Title": "Efficient high-utility occupancy itemset mining algorithm on massive data", "Abstract": "Mining interesting itemsets on massive data is a necessary topic in data mining. Nowadays, most studies use frequency or utility as primary measure. However, using these two measures individually has its own limitations. For example, itemsets with high frequencies may have low profits while itemsets with high utilities perhaps appear occasionally, so they might be misleading. In addition, the existing algorithms can only deal with small-medium scale database, and their performances degrade significantly when data is expanded. To address these drawbacks, this paper proposes a novel high utility occupancy itemset mining algorithm SHO ( Suffix-based High-utility Occupancy itemset mining ), it considers both quantities and profits of itemsets. SHO designs the algorithm from suffix-based partitioning, generation pruning and itemsets linking, it can mine high utility occupancy itemsets on large-scale database effectively. At the beginning, the database are divided into some non-overlapping suffix-based partitions and stored in vertical format, then the support and utility occupancy of itemset can be calculated in a certain partition instead of traversing total database. Besides, two optimization strategies and four pruning strategies are proposed to make SHO faster. The extensive experiments show that SHO is much better than the current state-of-the-art algorithm, the efficiency can be improved up to 3 orders of magnitude.", "Keywords": "Massive data ; High utility occupancy pattern mining ; Suffix-based partitioning ; LI strategy ; RTI optimization strategy", "DOI": "10.1016/j.eswa.2022.118329", "PubYear": 2022, "Volume": "210", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, China;Corresponding author"}, {"AuthorId": 2, "Name": "Xixian Han", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, China"}], "References": [{"Title": "Efficient top-k high utility itemset mining on massive data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "557", "Issue": "", "Page": "382", "JournalTitle": "Information Sciences"}, {"Title": "Efficient algorithms for discovering high-utility patterns with strong frequency affinities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Tin T<PERSON>ong", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114464", "JournalTitle": "Expert Systems with Applications"}, {"Title": "High utility itemset mining using binary differential evolution: An application to customer segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "115122", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Review on Frequent Itemsets Generation Techniques and Their Comparative Analysis Using FIMAK", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "1", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Mining high average-utility sequential rules to identify high-utility gene expression sequences in longitudinal human studies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>-<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116411", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 95642047, "Title": "An optimization algorithm for conformer generation based on the bond contribution ranking", "Abstract": "Many works in computational drug discovery require the conformer generation of small molecules. Most existing tools aim to generate diverse conformers and deal with all of the rotatable bonds without distinction. There are some problems in existing approaches, such as the combinatorial explosion of conformers along with the number of rotatable bonds increasing and the incomplete sampling of the conformational space. Here, we present an optimized conformer generation algorithm based on the bond contribution ranking (ABCR) to find the optimal conformer under any specified scoring function. Compared with existing methods, our method can improve molecular conformational searching and protein-ligand docking performance. Meanwhile, our method has the same or broader coverage of conformational space in the global conformer sampling. Our research shows it can achieve the optima with small numbers of generated conformers and small numbers of iterations.", "Keywords": "Bond contribution rank;Conformer generation;Systematic sampling;Torsion angle", "DOI": "10.1016/j.compbiolchem.2022.107751", "PubYear": 2022, "Volume": "100", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Laboratory Medicine, Chongqing Medical University, Chongqing, China."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center for Novel Target and Therapeutic Intervention, Institute of Life Sciences, Chongqing Medical University, Chongqing, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The First Clinical College, Chongqing Medical University, Chongqing, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The First Clinical College, Chongqing Medical University, Chongqing, China."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "The First Clinical College, Chongqing Medical University, Chongqing, China; Department of Orthopaedics, Jinshan Hospital, Fudan University, Shanghai, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Novel Target and Therapeutic Intervention, Institute of Life Sciences, Chongqing Medical University, Chongqing, China. Electronic address:  ."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Novel Target and Therapeutic Intervention, Institute of Life Sciences, Chongqing Medical University, Chongqing, China; The Center for Chemical Biology, Drug Discovery and Design Center, State Key Laboratory of Drug Research, Shanghai Institute of Materia Medica, Chinese Academy of Sciences, Shanghai, China. Electronic address:  ."}], "References": []}, {"ArticleId": 95642157, "Title": "CPRiL: compound–protein relationships in literature", "Abstract": "Summary \n Newly discovered functional relationships of (bio-)molecules are a key component in molecular biology and life science research. Especially in the drug discovery field, knowledge of how small molecules associated with proteins plays a fundamental role in understanding how drugs or metabolites can affect cells, tissues and human metabolism. Finding relevant information about these relationships among the huge number of published articles is becoming increasingly challenging and time-consuming. On average, more than 25 000 new (bio-)medical articles are added to the literature database PubMed weekly. In this article, we present a new web server [compound–protein relationships in literature (CPRiL)] that provides information on functional relationships between small molecules and proteins in literature. Currently, CPRiL contains ∼465 000 unique names and synonyms of small molecules, ∼100 000 unique proteins and more than 9 million described functional relationships between these entities. The applied BioBERT machine learning model for the determination of functional relationships between small molecules and proteins in texts was extensively trained and tested. On a related benchmark, CPRiL yielded a high performance, with an F1 score of 84.3%, precision of 82.9% and recall of 85.7%.\n \n \n Availability and implementation \n CPRiL is freely available at https://www.pharmbioinf.uni-freiburg.de/cpril.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btac539", "PubYear": 2022, "Volume": "38", "Issue": "18", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Group Pharmaceutical Bioinformatics, Institute of Pharmaceutical Sciences, Albert-Ludwigs-Universität Freiburg, Freiburg 79104, Germany."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Research Group Pharmaceutical Bioinformatics, Institute of Pharmaceutical Sciences, Albert-Ludwigs-Universität Freiburg, Freiburg 79104, Germany."}], "References": [{"Title": "BioBERT: a pre-trained biomedical language representation model for biomedical text mining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1234", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 95642228, "Title": "The study of the improvement and mechanisms on ASS surface topography in pre-stress grinding considering SCC", "Abstract": "<p>The surface topography has an important influence on the SCC behavior of stainless steel. Pre-stress grinding has the potential to improve the SCC resistance of the surface. Therefore, theoretical and experimental studies ought to be carried out to explore the improvement effect of pre-stress grinding on topography. Aiming to consider the influence of deformation effect on surface topography, the grinding force, and heat model considering the properties of stainless steel material and grinding wheel was established at the beginning. Then, to make the simulation more accurate, the grains’ position with random distribution, the bulge height obeying non-Gaussian distribution, and possessing autocorrelation length were expressed, respectively; finally, the surface topography model considering deformation and plow effect in pre-stress grinding was established, and experimental tests were conducted for the model validation. The simulation results showed good agreement with the experimental results. The results suggest that appropriate pre-stress could flatter the ground surface and decrease the amount of grinding surface defects, such as grinding areas, indentations, and deep grooves. With the increase of pre-stress, Ra, Rz, and aspect ratio of grooves tend to fall, which are conducive to improving the SCC resistance of the surface. The mechanism of the improvement introduced by pre-stress may be that pre-stress improves the rigidity of the grinding surface and reduces the thermal deformation during the grinding process, which makes the material easy to be cut and reduces the probability of surface defects; the spring-back effect after pre-stress release would further close the minor defects on the surface, and further improve the surface corrosion resistance.</p>", "Keywords": "Pre-stress grinding; Topography; Simulation and experiment; SCC resistance", "DOI": "10.1007/s00170-022-09778-w", "PubYear": 2022, "Volume": "121", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "Zhuangzhuang Hou", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical and Electrical Engineering Institute, Shenyang Aerospace University, Shenyang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Northeastern University, Shenyang, China"}], "References": []}, {"ArticleId": 95642235, "Title": "Codensity Games for Bisimilarity", "Abstract": "<p>Bisimilarity as an equivalence notion of systems has been central to process theory. Due to the recent rise of interest in quantitative systems (probabilistic, weighted, hybrid, etc.), bisimilarity has been extended in various ways, such as bisimulation metric between probabilistic systems. An important feature of bisimilarity is its game-theoretic characterization, where <PERSON>po<PERSON> and Du<PERSON>licator play against each other; extension of bisimilarity games to quantitative settings has been actively pursued too. In this paper, we present a general framework that uniformly describes game characterizations of bisimilarity-like notions. Our framework is formalized categorically using fibrations and coalgebras. In particular, our characterization of bisimilarity in terms of fibrational predicate transformers allows us to derive what we call codensity bisimilarity games: a general categorical game characterization of bisimilarity. Our framework covers known bisimilarity-like notions (such as bisimulation metric and bisimulation seminorm) as well as new ones (including what we call bisimulation topology).</p>", "Keywords": "Coalgebra; Bisimulation; Safety game; Bisimulation metric; Fibration", "DOI": "10.1007/s00354-022-00186-y", "PubYear": 2022, "Volume": "40", "Issue": "2", "JournalId": 6245, "JournalTitle": "New Generation Computing", "ISSN": "0288-3635", "EISSN": "1882-7055", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Informatics, Tokyo, Japan; The Graduate University for Advanced Studies, SOKENDAI, Hayama, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Informatics, Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Oxford, Oxford, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Oxford, Oxford, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "École Normale Supérieure de Lyon, Lyon, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Informatics, Tokyo, Japan; Japanese-French Laboratory for Informatics, Tokyo, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Informatics, Tokyo, Japan; The Graduate University for Advanced Studies, SOKENDAI, Hayama, Japan"}], "References": []}, {"ArticleId": 95642245, "Title": "A deep learning-based framework for accurate identification and crop estimation of olive trees", "Abstract": "<p>Over the last several years, olive cultivation has grown throughout the Mediterranean countries. Among them, Spain is the world’s leading producer of olives. Due to its high economic significance, it is in the best interest of these countries to maintain the crop spread and its yield. Manual enumeration of trees over such extensive fields is impractical and humanly infeasible. There are several methods presented in the existing literature; nonetheless, the optimal method is of greater significance. In this paper, we propose an automated method of olive tree detection as well as crop estimation. The proposed approach is a two-step procedure that includes a deep learning-based classification model followed by regression-based crop estimation. During the classification phase, the foreground tree information is extracted using an enhanced segmentation approach, specifically the K -Mean clustering technique, followed by the synthesis of a super-feature vector comprised of statistical and geometric features. Subsequently, these extracted features are utilized to estimate the expected crop yield. Furthermore, the suggested method is validated using satellite images of olive fields obtained from Google Maps. In comparison with existing methods, the proposed method contributed in terms of novelty and accuracy, outperforming the rest by an overall classification accuracy of 98.1% as well as yield estimate with a root mean squared error of 0.185 respectively.</p>", "Keywords": "Deep learning; Olive tree detection; Crop yield estimation; Google maps; Satellite Images; K-Mean clustering", "DOI": "10.1007/s11227-022-04738-3", "PubYear": 2023, "Volume": "79", "Issue": "2", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Bahria University, Lahore, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Islamabad, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Media Design and Technology, Faculty of Engineering & Informatics, University of Bradford, Bradford, UK"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chung-Ang University, Seoul, South Korea"}], "References": [{"Title": "A survey on deep learning and its applications", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100379", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": 95642492, "Title": "Learning Attribute-guided Fashion Similarity with Spatial and Channel Attention", "Abstract": "Fashion image retrieval is one of the important services of e-commerce platforms, and it is also the basis of various fashion-related AI applications. Studies have shown that in a multi-modal environment (images + attribute labels), embedding items into specific attribute spaces can support more fine-grained similarity measures, which is especially suitable for fashion retrieval tasks. In this paper, we propose an attention-based attribute-guided similarity learning network (AttnFashion) for fashion image retrieval. The core of this network is an attribute-guided spatial attention module and an attribute-guided channel attention module, which correspond to the mapping between attributes and image regions, and the mapping between attributes and high-level image semantics, respectively. To make these two modules interact deeply, we design a parallel structure that allows them to share attribute embeddings and guide each other to extract specific features, which also helps to reduce the network parameters of the attention modules. An adaptive feature fusion strategy is proposed to synthesise the features extracted by the two modules. Extensive experiments show that the proposed AttnFashion performs better than current competitive networks in the field of fine-grained attribute-based fashion retrieval.", "Keywords": "multi-modal ; fashion image retrieval ; attention mechanism ; similarity learning", "DOI": "10.1080/0952813X.2022.2104386", "PubYear": 2024, "Volume": "36", "Issue": "5", "JournalId": 23221, "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence", "ISSN": "0952-813X", "EISSN": "1362-3079", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China;Department of Computer Science and Technology, Shanghai Jian Qian University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Donghua University, Shanghai, China"}, {"AuthorId": 3, "Name": "Cairong Yan", "Affiliation": "School of Computer Science and Technology, Donghua University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Shanghai Polytechnic University, Shanghai, China"}], "References": [{"Title": "Clothes image caption generation with attribute detection and visual attention model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "141", "Issue": "", "Page": "68", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Fashion Meets Computer Vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "JointCTR: a joint CTR prediction framework combining feature interaction and sequential behavior learning", "Authors": "Cairong Yan; <PERSON><PERSON>; Yizhou Chen", "PubYear": 2022, "Volume": "52", "Issue": "4", "Page": "4701", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 95642750, "Title": "A Novel Method Designing Optimal Observer Gain for Robust Fault Detection", "Abstract": "This paper considers the design of optimal gain of set-value observer for robust fault detection (FD) from a new perspective. Set-based robust FD can be implemented by real-timely check whether the measured output is contained in the corresponding output estimation set or not. Our proposed method focuses on designing observer gains at each step to maximize the exclusion tendency of the measured output from the output estimation zonotope to achieve the goal of robust FD. The key deign logic is formulated as a two-level min-max optimization problem, which is further equivalently transformed into a non-convex single-level programming based on duality theory. Finally, this non-convex single-level programming can be efficiently solved via linear programming and matrix decomposition. At the end, a circuit model is used to illustrate the effectiveness of the proposed method.", "Keywords": "Fault detection ; set-value observer ; zonotopes", "DOI": "10.1016/j.ifacol.2022.07.529", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center of Intelligent Control and Telescience, Tsinghua Shenzhen International Graduate School, Tsinghua University, 518055 Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center of Intelligent Control and Telescience, Tsinghua Shenzhen International Graduate School, Tsinghua University, 518055 Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Shanghai Academy of Spaceflight Technology, 201109, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Key Laboratory of Information Science and Technology, Tsinghua Shenzhen International Graduate School, Tsinghua University, 518055, Shenzhen, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Navigation and Control Research Center, Department of Automation, Tsinghua University, 100084 Beijing, China"}], "References": []}, {"ArticleId": 95642773, "Title": "An In-depth Study of Java Deserialization Remote-Code Execution Exploits and Vulnerabilities", "Abstract": "<p> Nowadays, an increasing number of applications uses deserialization. This technique, based on rebuilding the instance of objects from serialized byte streams, can be dangerous since it can open the application to attacks such as remote code execution (RCE) if the data to deserialize is originating from an untrusted source. Deserialization vulnerabilities are so critical that they are in OWASP’s list of top 10 security risks for web applications. This is mainly caused by faults in the development process of applications and by flaws in their dependencies, i.e., flaws in the libraries used by these applications. No previous work has studied deserialization attacks in-depth: How are they performed? How are weaknesses introduced and patched? And for how long are vulnerabilities present in the codebase? To yield a deeper understanding of this important kind of vulnerability, we perform two main analyses: one on attack gadgets, i.e., exploitable pieces of code, present in Java libraries, and one on vulnerabilities present in Java applications. For the first analysis, we conduct an exploratory large-scale study by running 256 515 experiments in which we vary the versions of libraries for each of the 19 publicly available exploits. Such attacks rely on a combination of gadgets present in one or multiple Java libraries. A gadget is a method which is using objects or fields that can be attacker-controlled. Our goal is to precisely identify library versions containing gadgets and to understand how gadgets have been introduced and how they have been patched. We observe that the modification of one innocent-looking detail in a class – such as making it public – can already introduce a gadget. Furthermore, we noticed that among the studied libraries, 37.5% are not patched, leaving gadgets available for future attacks. </p><p>For the second analysis, we manually analyze 104 deserialization vulnerabilities CVEs to understand how vulnerabilities are introduced and patched in real-life Java applications. Results indicate that the vulnerabilities are not always completely patched or that a workaround solution is proposed. With a workaround solution, applications are still vulnerable since the code itself is unchanged.</p>", "Keywords": "Serialization; deserialization; vulnerabilities; gadget; remote code execution RCE", "DOI": "10.1145/3554732", "PubYear": 2023, "Volume": "32", "Issue": "1", "JournalId": 14907, "JournalTitle": "ACM Transactions on Software Engineering and Methodology", "ISSN": "1049-331X", "EISSN": "1557-7392", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Toulouse, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Umeå University, Sweden"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Paderborn University, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Luxembourg, Luxembourg"}], "References": []}, {"ArticleId": 95642881, "Title": "Modified minimum spanning tree based vertical fragmentation, allocation and replication approach in distributed multimedia databases", "Abstract": "<p>Distributed Multimedia Database Systems have become an indispensable part of modern world organizations that increased demand for reliable, scalable, and expeditiously accessible information processing systems, data has evolved in multiple media forms having found many application areas across industries that calls for optimal storage, processing and retrieval methodologies in a distributed fashion. The solution mainly relies on the optimization of database design structure in which data fragmentation, allocation and replication play eminent roles. The presented scheme employs a method of vertical fragmentation using enhanced CRUD matrix and Fibonacci heap to efficiently fragment the database into clusters. The fragments are then allocated and replicated at different network nodes depending on the manipulates and reads operation at respective sites, taking into consideration the cost factor. With the use of Fibonacci heap, the amortized complexity of the proposed algorithm has come down to O(E + V log V ) in contrast to the previous works of enhanced Prims algorithm in vertical fragmentation which offered a complexity of O(E log V ) where E denotes the number of edges and V, the number of vertices. This approach generates all the fragments at once and without the use of any predetermined parameters and does not involve the use of a query log. The proposed approach also considers communication and site storage costs for optimal allocation and replication thus minimizing the overall system costs.</p>", "Keywords": "Allocation; CRUD; Distributed multimedia database management system (DMDBMS); Distributed multimedia database system (DMDBS); Fragmentation; Minimum spanning tree; Replication; Vertical fragmentation", "DOI": "10.1007/s11042-022-13541-4", "PubYear": 2022, "Volume": "81", "Issue": "26", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Indira Gandhi Delhi Technical University for Women, Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Netaji Subhas University of Technology, New Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Netaji Subhas University of Technology, New Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Systems Sciences, Jawaharlal Nehru University, New Delhi, India"}], "References": [{"Title": "GridTables: A One-Size-Fits-Most H2TAP Data Store", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "1", "Page": "43", "JournalTitle": "Datenbank-Spektrum"}]}, {"ArticleId": ********, "Title": "Recursive recurrent neural network: A novel model for manipulator control with different levels of physical constraints", "Abstract": "Manipulators actuate joints to let end effectors to perform precise path tracking tasks. Recurrent neural network which is described by dynamic models with parallel processing capability, is a powerful tool for kinematic control of manipulators. Due to physical limitations and actuation saturation of manipulator joints, the involvement of joint constraints for kinematic control of manipulators is essential and critical. However, current existing manipulator control methods based on recurrent neural networks mainly handle with limited levels of joint angular constraints, and to the best of our knowledge, methods for kinematic control of manipulators with higher order joint constraints based on recurrent neural networks are not yet reported. In this study, for the first time, a novel recursive recurrent network model is proposed to solve the kinematic control issue for manipulators with different levels of physical constraints, and the proposed recursive recurrent neural network can be formulated as a new manifold system to ensure control solution within all of the joint constraints in different orders. The theoretical analysis shows the stability and the purposed recursive recurrent neural network and its convergence to solution. Simulation results further demonstrate the effectiveness of the proposed method in end‐effector path tracking control under different levels of joint constraints based on the Kuka manipulator system. Comparisons with other methods such as the pseudoinverse‐based method and conventional recurrent neural network method substantiate the superiority of the proposed method.", "Keywords": "", "DOI": "10.1049/cit2.12125", "PubYear": 2023, "Volume": "8", "Issue": "3", "JournalId": 26444, "JournalTitle": "CAAI Transactions on Intelligence Technology", "ISSN": "2468-6557", "EISSN": "2468-2322", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Swansea University  Swansea UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Swansea University  Swansea UK"}], "References": []}, {"ArticleId": 95642971, "Title": "Correlation-based feature selection using bio-inspired algorithms and optimized KELM classifier for glaucoma diagnosis", "Abstract": "Reduced computational time and cost, reduced skilled professional resources, and diagnostic accuracy have made medical diagnosis using computer aided systems (CAD) increasingly popular and can be comfortably employed in the diagnosis of many acute and chronic diseases in ophthalmology, cardiology, cancer detection, etc. There seems to be a growing necessity for the computational algorithms to be robust enough, to identify the abnormality in each of the cases, aiding in early diagnosis. In this paper, a glaucoma diagnostic approach based on the wrapper method employing bio-inspired algorithms, and a Kernel-Extreme Learning Machine (KELM) classifier is proposed. The bio-inspired algorithms are deployed to select feature sub-sets, generating three feature sub-sets from the pre-processed fundus images by adopting a correlation-based feature selection (CFS) approach. The selected features are utilized to train the salp-swarm optimization based KELM, which finds the optimal parameters of the KELM classifier network. The proposed methodology is evaluated on the public and private retinal fundus datasets containing 7280 images. The experimental outcome revealed that the system is able to attain a maximum overall accuracy of 99.61% with 99.89% sensitivity and 100% specificity. A 5-fold cross validation showed 98.78% accuracy ensuring a bias-free classification. Further, by experimenting on degraded images (Gaussian, salt-pepper noise images) of the original dataset, the model achieved extreme robustness with 99.3% accuracy. The proposed method is compared with other similar methods, which showed the efficiency of our method. The framework proposed can aid in making clinical decisions for various pathologies like lung infection, diabetic retinopathy, etc.", "Keywords": "Glaucoma ; Fundus image ; Feature selection ; Bio-Inspired Optimization algorithm ; KELM", "DOI": "10.1016/j.asoc.2022.109432", "PubYear": 2022, "Volume": "128", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dr. <PERSON><PERSON><PERSON> College of Engineering and Technology, Pollachi, India;Corresponding author"}, {"AuthorId": 2, "Name": "Ananthamoorthy N.P.", "Affiliation": "Hindusthan College of Engineering and Technology, Coimbatore, India"}], "References": [{"Title": "Feature selection strategy based on hybrid crow search optimization algorithm integrated with chaos theory and fuzzy c-means algorithm for medical diagnosis problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "1565", "JournalTitle": "Soft Computing"}, {"Title": "Recognition of Glaucoma by means of Gray Wolf Optimized Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "15-16", "Page": "10341", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Automated detection of Glaucoma using deep learning convolution network (G-net)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "15531", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "RETRACTED ARTICLE: Early diagnosis of glaucoma using multi-feature analysis and DBN based classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "4027", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 95642990, "Title": "Online Optimal Tuning of Relay Feedback Controllers for a class of Second-Order Systems with Limited Measurements", "Abstract": "We propose a novel, self-optimizing relay feedback control for a class of second-order systems. The proposed controller achieves a control performance that is close to that of continuous (e.g., PID) control, but it requires only very limited process information, acquired from a two-valued signal of a simple binary sensor. The smooth output of the proposed controller results in reduced energy consumption and wear of the actuator.", "Keywords": "Adaptive control ; Second-order systems ; Relay control ; Process control ; Discontinuous control", "DOI": "10.1016/j.ifacol.2022.07.572", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ruhr-Universität Bochum, Bochum, 44801 Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ruhr-Universität Bochum, Bochum, 44801 Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Ruhr-Universität Bochum, Bochum, 44801 Germany"}], "References": []}]