[{"ArticleId": 96352124, "Title": "New Obfuscation Scheme for Conjunctions", "Abstract": "<p>Recently, there has been renewed interest in conjunction obfuscations. A conjunction, which is called pattern matching with wildcards sometimes, is associated with a pattern $\\mathsf{pat}\\in \\{0,1,*\\}^n$ where * is a wildcard. It accepts if and only if the input bits are the same as the pattern at all non-wildcard positions. The conjunction obfuscation starts to get noticed because it provides the ability to protect these sensitive patterns while preserving its functionality. It is meaningful when the conjunction obfuscation is applied in the pattern matching, biological recognition, resisting SQL injection attacks and so on. In this work, we propose a new candidate of conjunction obfuscation. It not only retains the simplicity of the intuitive scheme in BKM18, but also adds wildcards to the pattern. Besides, we also propose a conjunction obfuscation with multi-bit output. The second obfuscation has the same size of the obfuscated program as the first obfuscation. Both obfuscations provide the distributional virtual black-box security.</p>", "Keywords": "", "DOI": "10.1093/comjnl/bxac120", "PubYear": 2023, "Volume": "66", "Issue": "11", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University , Guangzhou 510006, China;Guangdong Province Key Laboratory of Information Security Technology , Guangzhou 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University , Guangzhou 510006, China;Guangdong Province Key Laboratory of Information Security Technology , Guangzhou 510006, China"}], "References": []}, {"ArticleId": 96352130, "Title": "Traffic flow simulation of modified cellular automata model based on producer-consumer algorithm", "Abstract": "<p>With the rise of new technologies such as the Internet of Vehicles and the Internet of Things, research on the intelligent connected vehicle has become a hot topic in contemporary times. The modeling and simulation of traffic flow are mainly used to analyze the characteristics of traffic flow and study the formation and dissipation mechanism of traffic congestion to better guide the real traffic. Cellular automata are suitable for the simulation of complex giant systems. Because of the randomness and discreteness of vehicle driving, cellular automata are often used to model and analyze traffic flow. This article mainly studies the traffic flow formed by intelligent connected vehicles. Based on the traditional NaSch model, the producer-consumer algorithm is introduced to form a multi-buffer vehicle information access mode, and an improved cellular automata model with random updates is constructed. The simulation results show that the improved cellular automata model improves the traffic congestion significantly compared with the original NaSch model in the intelligent network environment, which is consistent with the actual traffic situation. Therefore, the algorithm proposed in this article can effectively simulate the traffic flow characteristics of intelligent connected vehicles, and provide a theoretical basis for solving traffic problems.</p>", "Keywords": "Cellular automata;NaSch model;Producer-consumer;Simulation;Traffic flow", "DOI": "10.7717/peerj-cs.1102", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Shanxi Agricultural University, Taigu, Shanxi, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Shanxi Agricultural University, Taigu, Shanxi, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Shanxi Agricultural University, Taigu, Shanxi, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Shanxi Agricultural University, Taigu, Shanxi, China"}], "References": []}, {"ArticleId": 96352219, "Title": "Feature selection based on ACO and knowledge graph for Arabic text classification", "Abstract": "", "Keywords": "", "DOI": "10.1080/0952813X.2022.2125588", "PubYear": 2024, "Volume": "36", "Issue": "7", "JournalId": 23221, "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence", "ISSN": "0952-813X", "EISSN": "1362-3079", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Digital transformation programs and information department, Institute of Public Administration, Riyadh, Saudi Arabia"}], "References": [{"Title": "Text categorisation in Quran and Hadith: Overcoming the interrelation challenges using machine learning and term weighting", "Authors": "<PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "658", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "A novel hybrid particle swarm optimization and gravitational search algorithm for multi-objective optimization of text mining", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106189", "JournalTitle": "Applied Soft Computing"}, {"Title": "A multi-strategy integrated multi-objective artificial bee colony for unsupervised band selection of hyperspectral images", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100806", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Multi-objective PSO based online feature selection for multi-label classification", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "222", "Issue": "", "Page": "106966", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Predicting Semantic Categories in Text Based on Knowledge Graph Combined with Machine Learning Techniques", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "12", "Page": "933", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "A wrapper based binary bat algorithm with greedy crossover for attribute selection", "Authors": "<PERSON>la; <PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115828", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Improved evolutionary-based feature selection technique using extension of knowledge based on the rough approximations", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "594", "Issue": "", "Page": "76", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96352271, "Title": "Sℒ\n <sub>1</sub>\n -Simplex: Safe Velocity Regulation of Self-Driving Vehicles in Dynamic and Unforeseen Environments", "Abstract": "This paper proposes a novel extension of the Simplex architecture with model switching and model learning to achieve safe velocity regulation of self-driving vehicles in dynamic and unforeseen environments. To guarantee the reliability of autonomous vehicles, an\n \n \\(\\mathcal {L}_{1} \\) \n \n adaptive controller that compensates for uncertainties and disturbances is employed by the Simplex architecture as a verified high-assurance controller (HAC) to tolerate concurrent software and physical failures. Meanwhile, the safe switching controller is incorporated into the HAC for safe velocity regulation in the dynamic (prepared) environments, through the integration of the traction control system and anti-lock braking system. Due to the high dependence of vehicle dynamics on the driving environments, the HAC leverages the finite-time model learning to timely learn and update the vehicle model for\n \n \\(\\mathcal {L}_{1} \\) \n \n adaptive controller, when any deviation from the safety envelope or the uncertainty measurement threshold occurs in the unforeseen driving environments. With the integration of\n \n \\(\\mathcal {L}_{1} \\) \n \n adaptive controller, safe switching controller and finite-time model learning, the vehicle’s angular and longitudinal velocities can asymptotically track the provided references in the dynamic and unforeseen driving environments, while the wheel slips are restricted to safety envelopes to prevent slipping and sliding. Finally, the effectiveness of the proposed Simplex architecture for safe velocity regulation is validated by the AutoRally platform.", "Keywords": "Simplex; model learning; model switching; ℒ1 adaptive controller; safe velocity regulation; traction control system; anti-lock braking system", "DOI": "10.1145/3564273", "PubYear": 2023, "Volume": "7", "Issue": "1", "JournalId": 41094, "JournalTitle": "ACM Transactions on Cyber-Physical Systems", "ISSN": "2378-962X", "EISSN": "2378-9638", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Mao", "Affiliation": "Wayne State University, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana–Champaign, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana–Champaign, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana–Champaign, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Nevada, USA"}], "References": []}, {"ArticleId": 96352323, "Title": "Synthesis of Cost-Optimal Multi-Agent Systems for Resource Allocation", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.369.5", "PubYear": 2022, "Volume": "369", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Pretoria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Pretoria"}], "References": [{"Title": "Model checking safety and liveness via k-induction and witness refinement with constraint generation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "102532", "JournalTitle": "Science of Computer Programming"}, {"Title": "Combining quantitative and qualitative reasoning in concurrent multi-player games", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "1", "JournalTitle": "Autonomous Agents and Multi-Agent Systems"}]}, {"ArticleId": 96352337, "Title": "Correctness of Broadcast via Multicast: Graphically and Formally", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.369.3", "PubYear": 2022, "Volume": "369", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96352353, "Title": "Two-phased Participant Selection Method Based on Partial Transfer Learning in Mobile Crowdsensing", "Abstract": "<p>To solve the problem of sensing data redundancy and missing caused by the uneven distribution of resources in mobile crowd sensing, a two-phased participant selection method based on partial transfer learning is proposed. Firstly, the data is preprocessed. On the one hand, the sensing task features are extracted to analyze the correlation between the source task and the target task feature space. On the other hand, users are divided into active users and passive users according to the historical movement law of sensing users. Secondly, the participant selection in the first stage is carried out. According to the similarity of feature space between the source task and the target task, part of the user resources of the source task are migrated to the target subtask with a similar distribution of its feature space. As a result, the target task can select participants efficiently and accurately. Finally, the participant selection in the second stage is carried out. For the target subtask not covered, the passive users in the subtask area are taken as the assignment object. Simulation results based on real data sets show that this method can effectively improve the task coverage and reduce the perceived excitation cost.</p>", "Keywords": "Mobile crowd sensing; two-phased participant selection; partial transfer learning; task recommendation", "DOI": "10.1145/3563776", "PubYear": 2023, "Volume": "19", "Issue": "2", "JournalId": 12743, "JournalTitle": "ACM Transactions on Sensor Networks", "ISSN": "1550-4859", "EISSN": "1550-4867", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin University of Science and Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin University of Science and Technology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Information Engineering, Harbin Normal University"}], "References": []}, {"ArticleId": 96352362, "Title": "Intelligent Awareness of Delay-Sensitive Internet Traffic in Digital Twin Network", "Abstract": "The increasingly complex network architecture and numerous emerging network services make it difficult for traditional “best effort” consumer Internet to support current Production Internet/5G requirements for differentiated SLA commitments, low latency, and deterministic bandwidth. To solve various difficulties and challenges faced by the development of ultra-large-scale networks, the Digital Twin Network has become a research hotspot. In this paper, based on the traffic modeling problem involved in the digital twin layer of digital twin network architecture, we proposed an intelligent awareness method for delay-sensitive network traffic. Through the real-time awareness of delay-sensitive traffic, intelligent routing of network services is realized and meets the requirements of scenarios, such as high-value traffic assurance and intelligence traffic scheduling and grooming for network congestion. We experimentally confirmed the scheme by traffic data collected in realistic network environments, and it has an awareness accuracy of more than 90%.", "Keywords": "Intelligent awareness;delay-sensitive traffic;machine learning;digital twin network", "DOI": "10.1109/JRFID.2022.3207489", "PubYear": 2022, "Volume": "6", "Issue": "", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Huawei Technologies Co., Ltd. Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Chinamobile Research Institute Beijing, China"}, {"AuthorId": 3, "Name": "Tao Sun", "Affiliation": "Chinamobile Research Institute Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Huawei Technologies Co., Ltd. Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Huawei Technologies Co., Ltd. Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Huawei Technologies Co., Ltd. Beijing, China"}], "References": []}, {"ArticleId": 96352368, "Title": "Investigation of a Neural Network Decomposition by Proteus Design Suite", "Abstract": "The division of a monolithic neural network into blocks with their implementation on programmable logic within the framework of the Fog computing concept is considered. It is assumed that considering possible reconfiguration the implementation of blocks is performed on programmable logic: field-programmable gate array, FPGA (complex programmable logic device, CPLD), System-on-a-Chip, SoC or System-in-Package, SiP. The article explores such an implementation in the Proteus Design Suite based on ATMega32 microcontrollers. Modeling confirms the efficiency of the developed decomposition method. The research was carried out under the RFBR grant 20-37-90036 (Method of synthesizing neural network recognition devices for implementing the Fog computing mode).", "Keywords": "Схемотехническое моделирование;Микроконтроллер;Нейронная сеть;Proteus", "DOI": "10.17072/1993-0550-2022-2-73-80", "PubYear": 2022, "Volume": "", "Issue": "2(57)", "JournalId": 77295, "JournalTitle": "Вестник Пермского университета. Математика. Механика. Информатика", "ISSN": "1993-0550", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Perm National Research Polytechnic University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Perm National Research Polytechnic University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Perm National Research Polytechnic University"}], "References": []}, {"ArticleId": 96352392, "Title": "Dynamic Modeling of Professional Mobility in the Context of Labor Market Precarization", "Abstract": "The pandemic has become a catalyst for the inevitable process of digitalization of communications, significantly changing the organization and technology of professional activity around the world. There has been a radical change in labor market trends due to the digital transformation of the economy, changes in the unemployment rate, the transition of professional groups to the remote work format due to the external need for isolation to minimize the spread of Covid-19. The uncertainty of the labor market development conditions, as well as changes in the unemployment rate among the young subset of employable population, cause the emergence of various forms of unstable employment. The presence of high social risks, the need to develop mechanisms to increase the level of protection of the population, ensuring the growth of youth welfare and the formation of an energy saving policy aimed, inter alia, at the innovative development of the labor economy, determine the relevance of developing a set of models for predicting precarization of the labor market. To describe the dynamics of the labor market development, we constructed a model for assessing the risks for young professionals from key sectors of the economy who enter the precariat during the pandemic. Based on the provisions of the theory of positional games and behavioral economics, we have developed a multifactorial dynamic model for predicting professional mobility and precarization of the labor market, considering the fulfillment of Nash equilibrium conditions. The model allows you to track and predict the professional mobility of qualified youth depending on the intensity of the labor market, wage levels and the degree of digital maturity of companies in the region.", "Keywords": "optimal control ; dynamic games ; labor market ; professional trajectories ; behavioral economics ; dynamic model ; optimization ; professional mobility ; labor precarization", "DOI": "10.1016/j.ifacol.2022.09.055", "PubYear": 2022, "Volume": "55", "Issue": "16", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ural Federal University named after the first President of Russia <PERSON><PERSON><PERSON><PERSON>, Mira 19, Yekaterinburg, Russia, 620002"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ural Federal University named after the first President of Russia <PERSON><PERSON><PERSON><PERSON>, Mira 19, Yekaterinburg, Russia, 620002"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ural Federal University named after the first President of Russia <PERSON><PERSON><PERSON><PERSON>, Mira 19, Yekaterinburg, Russia, 620002"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Krasovskii Institute of Mathematics and Mechanics of Ural Branch of RAS, S. Kovalevskaya Street, 16, Yekaterinburg, Russia, 620108"}], "References": []}, {"ArticleId": 96352608, "Title": "Four MPC implementations compared on the Quadruple Tank Process Benchmark: pros and cons of neural MPC*", "Abstract": "This study aims to aid understanding of Model Predictive Control (MPC) alternatives through comparing most interesting MPC implementations. This comparison will be performed intrinsically and illustrated using the four-tank benchmark, widely studied by academics taking care of industrial perspectives. Although MPC provides advanced control solutions for a wide class of dynamical systems, challenges arise in managing the compromise between accuracy, computational cost and resilience, depending on the type of model used. In this study, linear, linear time-varying and non-linear MPCs are compared to MPC that uses a neural network based predictive model identified from data. The tuning and implementation methods considered are discussed, and accurate simulation results provided and analyzed. Precisely, the performance of each method (linear, linear time-varying, non-linear MPC) are compared to the neural MPC. Pros and cons of neural MPC are highlighted.", "Keywords": "Model predictive control ; artificial neural network ; quadruple tank process", "DOI": "10.1016/j.ifacol.2022.09.048", "PubYear": 2022, "Volume": "55", "Issue": "16", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IMT Atlantique, LS2N, UMR CNRS 6004, F-44307 Nantes, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IMT Atlantique, LS2N, UMR CNRS 6004, F-44307 Nantes, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IMT Atlantique, LS2N, UMR CNRS 6004, F-44307 Nantes, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "IMT Atlantique, GEPEA, UMR CNRS 6144, F-44307 Nantes, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "CEA, CEA Tech Pays de la Loire, F-44340 Bouguenais, France"}], "References": []}, {"ArticleId": 96352659, "Title": "BER Aided Energy and Spectral Efficiency Estimation in a Heterogeneous Network", "Abstract": "<p>In this work, we adopt the analysis of a heterogeneous cellular network by means of stochastic geometry, to estimate energy and spectral network efficiency. More specifically, it has been the widely spread experience that practical field assessment of the Signal-to-Noise and Interference Ratio (SINR), being the key physical-layer performance indicator, involves quite sophisticated test instrumentation that is not always available outside the lab environment. So, in this regard, we present here a simpler test model coming out of the much easier-to-measure Bit Error Rate (BER), as the latter can deteriorate due to various impairments regarded here as equivalent with additive white Gaussian noise (AWGN) abstracting (in terms of equal BER degradation) any actual non-AWGN impairment. We validated the derived analytical model for heterogeneous two-tier networks by means of an ns3 simulator, as it provided the test results that fit well to the analytically estimated corresponding ones, both indicating that small cells enable better energy and spectral efficiencies than the larger-cell networks.</p>", "Keywords": "heterogeneous network; BER; energy efficiency; spectral efficiency heterogeneous network ; BER ; energy efficiency ; spectral efficiency", "DOI": "10.3390/computation10090162", "PubYear": 2022, "Volume": "10", "Issue": "9", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Communications Regulatory Agency, 71000 Sarajevo, Bosnia and Herzegovina"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computing, University of Dubrovnik, 20000 Dubrovnik, Croatia; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computing, University of Dubrovnik, 20000 Dubrovnik, Croatia"}], "References": []}, {"ArticleId": 96352670, "Title": "Procedure Used to Estimate the Power Production of a Photovoltaic Module Operating under Partial Shading Conditions", "Abstract": "<p>This paper presents a methodology used to estimate the energy generated during one year by a photovoltaic module (PVM) operating under partial shading conditions. The methodology starts by calculating the solar paths and contours of nearby objects that produce shadows. Then, a method was proposed to estimate the shading factors of each submodule. Afterwards, the solar resource data and the calculated shading factors were used to feed a detailed PVM model to calculate the power–voltage curves for each hour, which were used to obtain a power profile and estimate the energy generated by the PVM in one year. The procedure was validated through simulation and experimental results. The simulation results consider a case study available in the literature, which was simulated to evaluate the effect on the PVM energy estimation considering and disregarding the partial shading conditions. The experimental results illustrate the capacity of the proposed methodology to predict the shaded and unshaded submodules and the module power–voltage curve. The results show that the proposed method avoids the energy overestimation introduced by classical estimation methods, which affects the sizing of a photovoltaic generator.</p>", "Keywords": "partial shading; photovoltaic module (PVM); power estimation; shading factors; solar paths partial shading ; photovoltaic module (PVM) ; power estimation ; shading factors ; solar paths", "DOI": "10.3390/computation10090167", "PubYear": 2022, "Volume": "10", "Issue": "9", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-Florez", "Affiliation": "Ingeniería Eléctrica, Unidades Tecnológicas de Santander, Bucaramanga 680003, Colombia; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": "Departamento de Ingeniería Eléctrica, Electrónica y Computación, Universidad Nacional de Colombia, Manizales 170003, Colombia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Facultad de Minas, Universidad Nacional de Colombia, Medellín 050041, Colombia"}], "References": []}, {"ArticleId": 96352696, "Title": "A data-driven Machine Learning approach to creativity and innovation techniques selection in solution development", "Abstract": "The creation and refinement of new ideas is a strategic competence for teams and organization to innovate and prosper. This paper addresses the challenge of finding adequate creativity and innovation techniques (CITs) for improving individual or team creativity through the use of Machine Learning (ML). The process of choosing which CIT to use is complex and demanding, especially when taking into consideration the existence of hundreds of techniques and the plurality of different design contexts. This empiric knowledge, usually retained in an expert’s repertoire, can be extracted and implemented in a computational system, making it more available and permanent. This research focused on developing a Decision Support System embedded in an online application with a two-stage ML inference process able to evaluate users’ design scenario through an online form, and infer the most appropriate CITs from the database that would fit their needs. This paper presents two iterative development cycles of the prototype, first focused on core knowledge acquisition, representation, ML implementation, and verification; while second focused on system expansion, addition of web interface, and initial validation. After essaying 12 algorithms, the two-stage model achieved uses a Gradient Boosted Regression Trees algorithm using user provided information about the context to infer the required CITs characteristics; followed by a Logistic Regression classification-ranking algorithm that uses outputs from first model to define which CITs to present to users. To the best of our efforts, no other system was found to use ML approaches to address the problem of CIT selection.", "Keywords": "Decision support system ; Creativity ; Artificial intelligence ; Design", "DOI": "10.1016/j.knosys.2022.109893", "PubYear": 2022, "Volume": "257", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "UFSC - Universidade Federal de Santa Catarina, Campus Reitor João <PERSON>, Florianópolis, Santa Catarina, ZIP Code: 88040-900, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "UFSC - Universidade Federal de Santa Catarina, Campus Reitor João David <PERSON>, Florianópolis, Santa Catarina, ZIP Code: 88040-900, Brazil"}], "References": [{"Title": "Artificial intelligence versus <PERSON>: Experimental evidence that people cannot differentiate AI-generated from human-written poetry", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "106553", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 96352711, "Title": "About the Solutions Stability of Linear Differential Equations System", "Abstract": "The article proposes the continuation and development of the apparatus of derived numbers, which allows us to study the behavior of several variables functions and the properties of systems solutions of differential equations without their differentiability. The conditions and criteria for using the apparatus of partial and external derivatives numbers are proposed, the obtained earlier results can be applied in the study of the solutions stability of ordinary differential equations systems.", "Keywords": "дифференциальные уравнения;периодические решения;функции Ляпунова;устойчивость решений;асимптотическая устойчивость", "DOI": "10.17072/1993-0550-2022-2-31-39", "PubYear": 2022, "Volume": "", "Issue": "2(57)", "JournalId": 77295, "JournalTitle": "Вестник Пермского университета. Математика. Механика. Информатика", "ISSN": "1993-0550", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "St. Petersburg State University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "St. Petersburg State University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "St. Petersburg State University"}], "References": []}, {"ArticleId": 96352737, "Title": "A Prediction Framework for Turning Period Structures in COVID-19 Epidemic and Its Application to Practical Emergency Risk Management", "Abstract": "<p id=\"C1\">The aim of this paper is first to establish a general prediction framework for turning (period) term structures in COVID-19 epidemic related to the implementation of emergency risk management in the practice, which allows us to conduct the reliable estimation for the peak period based on the new concept of \" ${\\mathbf{Turning~~ Period}}\"$ (instead of the traditional one with the focus on \"Turning Point\") for infectious disease spreading such as the COVID-19 epidemic appeared early in year 2020. By a fact that emergency risk management is necessarily to implement emergency plans quickly, the identification of the Turning Period is a key element to emergency planning as it needs to provide a time line for effective actions and solutions to combat a pandemic by reducing as much unexpected risk as soon as possible. As applications, the paper also discusses how this \"Turning Term (Period) Structure\" is used to predict the peak phase for COVID-19 epidemic in Wuhan from January/2020 to early March/2020. Our study shows that the predication framework established in this paper is capable to provide the trajectory of COVID-19 cases dynamics for a few weeks starting from Feb.10/2020 to early March/2020, from which we successfully predicted that the turning period of COVID-19 epidemic in Wuhan would arrive within one week after Feb.14/2020, as verified by the true observation in the practice. The method established in this paper for the prediction of \" ${\\mathbf{ Turning~~ Term ~~(Period) ~~Structures}}\"$ by applying COVID-19 epidemic in China happened early 2020 seems timely and accurate, providing adequate time for the government, hospitals, essential industry sectors and services to meet peak demands and to prepare aftermath planning, and associated criteria for the Turning Term Structure of COVID-19 epidemic is expected to be a useful and powerful tool to implement the so-called \"dynamic zero-COVID-19 policy\" ongoing basis in the practice.</p>", "Keywords": "prediction framework;turning period structure;turing phase;COVID-19 epidemic;emergency risk management;emergency plan;Delta and Gamma;iSEIR;spatio-temporal model;supersaturation phenomenon;multiplex network;dynamic zero-COVID-19 policy", "DOI": "10.21078/JSSI-2022-309-29", "PubYear": 2022, "Volume": "10", "Issue": "4", "JournalId": 42617, "JournalTitle": "Journal of Systems Science and Information", "ISSN": "1478-9906", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Lan DI", "Affiliation": "School of Artificial Intelligence and Computer Science, Jiangnan University, Wuxi, 214122, China"}, {"AuthorId": 2, "Name": "Yudi GU", "Affiliation": "Center of Information Construct and Management, Jiangnan University, Wuxi, 214122, China"}, {"AuthorId": 3, "Name": "Guoqi QIAN", "Affiliation": "School of Mathematics & Statistics, University of Melbourne, Melbourne, VIC  3010, Australia"}, {"AuthorId": 4, "Name": "<PERSON> YUAN", "Affiliation": "Business School, East China University of Technology, Shanghai, 200237, China; Business School, Chengdu University, Chengdu, 610106, China; Business School, Sun Yat-sen University, Guangzhou, 510275, China"}], "References": []}, {"ArticleId": 96352744, "Title": "Retraction Note: Research on the construction of teaching case library of the computer simulation technology", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-022-13984-9", "PubYear": 2023, "Volume": "82", "Issue": "6", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Su<PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao, China"}], "References": []}, {"ArticleId": 96352879, "Title": "Artificial pancreas under periodic MPC for trajectory tracking: handling circadian variability of insulin sensitivity", "Abstract": "Closed-loop glycemic control algorithms have demonstrated the ability to improve glucose regulation in patients with type 1 diabetes mellitus (T1D), both in silico and clinical trials. Many of the proposed control strategies have been developed, based on time-invariant linear models, without considering the parametric variations of T1DM subjects. In this work, a pulsatile Zone Model Predictive Control (pZMPC) is proposed, which explicitly considers patterns of intra-day insulin sensitivity ( S<sub>I</sub> ), according to the latest updates of the FDA-approved UVA/Padova simulator. Results show a significant improvement in the performance, which a-priori justifies the increment in the controller complexity.", "Keywords": "Insulin sensitivity variations ; trajectory tracking ; MPC ; Artificial Pancreas", "DOI": "10.1016/j.ifacol.2022.09.023", "PubYear": 2022, "Volume": "55", "Issue": "16", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Technological Development for the Chemical Industry (INTEC), CONICET-Universidad Nacional del Litoral (UNL), Santa Fe, Argentina"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Management, Information and Production Engineering, University of Bergamo, Bergamo Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical, Computer and Biomedical Engineering, University of Pavia, Pavia, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Architecture Engineering, University of Pavia, Pavia, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Technological Development for the Chemical Industry (INTEC), CONICET-Universidad Nacional del Litoral (UNL), Santa Fe, Argentina"}], "References": []}, {"ArticleId": 96352881, "Title": "Considerations on Two-Phase Averaging of Time-Optimal Control Systems", "Abstract": "Averaging is a valuable technique to gain understanding of the long-term evolution of dynamical systems characterized by slow dynamics and fast periodic or quasi-periodic dynamics. Averaging the extremal flow of optimal control problems with one fast variable is possible if a special treatment of the adjoint to this fast variable is carried out. The present work extends these results by tackling averaging of time optimal systems with two fast variables, that is considerably more complex because of resonances. No general theory is presented, but rather a thorough treatement of an example, based on numerical experiments. After providing a justification of the possibility to use averaging techniques for this problem &quot;away from resonances&quot; and discussing compatibility conditions between adjoint variables of the original and averaged systems, we analyze numerically the impact of resonance crossings on the dynamics of adjoint variables. Resonant averaged forms are used to model the effect of resonances and cross them without loosing the accuracy of the averaging prediction.", "Keywords": "fast-oscillating systems ; averaging ; optimal control ; minimum time", "DOI": "10.1016/j.ifacol.2022.08.073", "PubYear": 2022, "Volume": "55", "Issue": "16", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Université Côte d'Azur, Inria, CNRS, LJAD"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université Côte d'Azur, Inria, CNRS, LJAD"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université Côte d'Azur, Inria, CNRS, LJAD"}], "References": []}, {"ArticleId": 96352896, "Title": "An optimization-based algorithm for simultaneous shaping of poles and zeros for non-collocated vibration suppression", "Abstract": "This article presents a control design method for simultaneous shaping of the poles and zeros of linear time-invariant systems, motivated by the application of non-collocated vibration suppression to flexible multi-body systems. An entire suppression of vibrations at a target mass for a given excitation frequencies can be recast into the problem of assigning zeros of the transfer function from the excitation force to the target mass&#x27; position. The design requirement of achieving sufficient damping in the closed loop system combined with suppressing vibrations at the target, leads to the minimization of the spectral abscissa function of the closed loop system as a function of the controller parameters, subject to zero location constraints. These constraints exhibit polynomial dependence on the controller parameters. We present two approaches to solve the optimization problem which are both based on constraint elimination followed by application of an algorithm for non-smooth unconstrained optimization. The design approach is applicable to delay-free models as well as time-delay models of retarded and neutral type. Simulations results illustrate its applicability to a spring-mass-damper system.", "Keywords": "vibration suppression ; zero placement ; time-delay systems ; spectral optimization", "DOI": "10.1016/j.ifacol.2022.09.056", "PubYear": 2022, "Volume": "55", "Issue": "16", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, KU Leuven, Celestijnenlaan 200A, 3001 Leuven, Belgium;Dept. Instrumentation and Control Engineering, Faculty of Mechanical Engineering and Czech Institute of Informatics, Robotics and Cybernetics, Czech Technical University in Prague, Prague 6, Czechia"}, {"AuthorId": 2, "Name": "Hai<PERSON>", "Affiliation": "Department of Computer Science, KU Leuven, Celestijnenlaan 200A, 3001 Leuven, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, KU Leuven, Celestijnenlaan 200A, 3001 Leuven, Belgium"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. Instrumentation and Control Engineering, Faculty of Mechanical Engineering and Czech Institute of Informatics, Robotics and Cybernetics, Czech Technical University in Prague, Prague 6, Czechia"}], "References": [{"Title": "Stabilization with Zero Location Constraints for Delay-Based Non-collocated Vibration Suppression", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "18", "Page": "121", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 96352901, "Title": "Permanence regions for switched linear systems under waiting-time constraints", "Abstract": "When switched systems are restricted by waiting-time constraints, some basic concepts such as equilibria, stability regions and invariant regions need to be updated. In this work, we propose some novel concepts concerning generalized permanence regions for these types of dynamics and we present three different algorithms that compute permanence regions out of the origin. The theoretical results are tested via simulation examples, one of them concerning a population ecological system.", "Keywords": "Constraint Switched Systems ; Waiting Times ; Permanence Regions", "DOI": "10.1016/j.ifacol.2022.09.021", "PubYear": 2022, "Volume": "55", "Issue": "16", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto de Desarrollo Tecnológico para la Industria Química (INTEC), Consejo Nacional de Investigaciones científicas y tecnicas (CONICET) and Universidad Nacional del Litoral (UNL), Santa Fe, Argentina"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instituto de Desarrollo Tecnológico para la Industria Química (INTEC), Consejo Nacional de Investigaciones científicas y tecnicas (CONICET) and Universidad Nacional del Litoral (UNL), Santa Fe, Argentina;IMT Nord Europe, Univ. Lille, F-59000 Lille, France"}, {"AuthorId": 3, "Name": "Esteban <PERSON><PERSON>", "Affiliation": "Institute of Mathematics, UNAM, Juriquilla, Mexico"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Instituto de Desarrollo Tecnológico para la Industria Química (INTEC), Consejo Nacional de Investigaciones científicas y tecnicas (CONICET) and Universidad Nacional del Litoral (UNL), Santa Fe, Argentina"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Facultad de Ingeniera Química (FIQ), Universidad Nacional del Litoral (UNL) and Consejo Nacional de Investigaciones cientficas y tecnicas (CONICET), Santa Fe, Argentina"}], "References": []}, {"ArticleId": 96352970, "Title": "Ineffectiveness of flood cooling in reducing cutting temperatures during continuous machining", "Abstract": "<p>Water-based metalworking fluids are applied in the form of a liquid jet to flood the entire cutting zone and increase the tool life. The objective of this study is to investigate the effectiveness of flood cooling in reducing the tool chip interface temperatures during continuous cutting. An instrumented smart cutting tool with a thin film temperature sensor was fabricated to accurately measure the real-time cutting temperatures from 1.3 µm below the tool chip interface in orthogonal turning of AISI 4140 steel under dry and flood cooling conditions. The cutting process was simulated in Deform 2D with the Johnson–Cook material model to present the transient temperature distributions on the coated cutting insert. The heat flux into the cutting tool was also estimated analytically and then three-dimensional finite element heat transfer simulations were performed to determine the maximum convective heat transfer of the cutting fluid in steady state. The measurements with the embedded thermocouple showed that flood cooling with a water-based cutting fluid slightly lowers the tool chip interface temperature. Moreover, the chip color may not be a good characteristic indicator to evaluate the cutting temperature in machining of metals. It was also found that flood cooling becomes more effective at a distance of approximately 150 µm from the cutting edge where the chip does not contact the rake face of the cutting tool.</p>", "Keywords": "Turning; Flood cooling; Cutting temperature; Modeling; Simulation", "DOI": "10.1007/s00170-022-10093-7", "PubYear": 2022, "Volume": "122", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Faculty of Engineering, Abdullah Gul University, Kayseri, Turkey"}], "References": [{"Title": "On cutting temperatures in high and ultrahigh-speed machining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "1-2", "Page": "73", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Comparative performance studies of turning 4140 steel with TiC/TiCN/TiN-coated carbide inserts using MQL, flooding with vegetable cutting fluids, and dry machining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "1-2", "Page": "381", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Study of using cutting chip color to the tool wear prediction", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "3-4", "Page": "823", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Investigation of the cutting fluid’s flow and its thermomechanical effect on the cutting zone based on fluid–structure interaction (FSI) simulation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "1-2", "Page": "267", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 96353008, "Title": "AIoT support and applications with multimedia", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-022-13896-8", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [], "References": []}, {"ArticleId": 96353126, "Title": "A High-Precision Power Line Recognition and Location Method Based on Structured-Light Binocular Vision", "Abstract": "<p>To complete the wiring operation of the main transmission line stripped of its insulating skin in a live power distribution system, a structured-light binocular vision method is utilized to identify and locate the line. First, aiming at the interference of the background information, a depth threshold segmentation method is used to filter the background area. Second, a mean filtering method is proposed to filter out the mismatch noise of a binocular vision camera in an outdoor environment. The Canny algorithm is then utilized to extract the contour, the central axis of the main transmission line is fitted, and the difference in the neighborhood pixel value is used to recognize the stripping area. Finally, the spatial equation and attitude of the central axis of the fitting transmission line are obtained along with the central coordinates of the stripping area, guiding the robot to carry out the wiring.</p>", "Keywords": "live working of power distribution;binocular vision;transmission line recognition;precise positioning", "DOI": "10.20965/jaciii.2022.p0691", "PubYear": 2022, "Volume": "26", "Issue": "5", "JournalId": 13600, "JournalTitle": "Journal of Advanced Computational Intelligence and Intelligent Informatics", "ISSN": "1343-0130", "EISSN": "1883-8014", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Electric Power Research Institute, State Grid Hubei Electric Power Co., Ltd. No.227 Xudong Street, Hongshan District, Wuhan 430077, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, China University of Geosciences No.388 Lumo Road, Hongshan District, Wuhan 430074, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Advanced Control and Intelligent Automation for Complex Systems No.388 Lumo Road, Hongshan District, Wuhan 430074, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, China University of Geosciences; Hubei Key Laboratory of Advanced Control and Intelligent Automation for Complex Systems"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Electric Power Research Institute, State Grid Hubei Electric Power Co., Ltd"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electric Power Research Institute, State Grid Hubei Electric Power Co., Ltd"}], "References": [{"Title": "Graph-Based Knowledge Acquisition With Convolutional Networks for Distribution Network Patrol Robots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "5", "Page": "384", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}]}, {"ArticleId": 96353154, "Title": "Generation of Voice Signal <PERSON><PERSON> and <PERSON> Based on Convolutional Neural Network", "Abstract": "<p>There is a need to prevent the generation of criminal activities in the voice signals due to changing voices by intruders to cover up their personal identities. The voice signal change detection based on convolutional neural network is proposed in this work that uses three commonly used voice processing software to change the tone of the voice library: Audacity, CoolEdit and RTISI. The research further raises 5 semitones for each voice, which are recorded at different levels, as +4, +5, +6, +7 and +8 respectively. Simultaneously, every speech is lowered by 5 halftones and which are further represented as -4, -5, -6, -7 and -8 respectively. The convolution neural network corresponding to network b-3 is determined as the final classifier in this article through experiments. The average accuracy A1 of its three categories has reached more than 97%, the detection accuracy A2 of electronic tone sandhi speech has reached more than 97%, and the false alarm rate FAR of the original speech is less than 1.9%. The outcomes obtained shows that the detection algorithm in this paper is effective, and it has good generalization ability.</p>", "Keywords": "Detection algorithm", "DOI": "10.1145/3545569", "PubYear": 2023, "Volume": "22", "Issue": "5", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Music, Shandong University of Science and Technology, Qingdao Shandong, 266590, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Music, Shandong University of Science and Technology, Qingdao Shandong, 266590, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Model Institute of Engineering and Technology, Jammu, J&K, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Petroleum and Energy Studies, Dehradun, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computer Science and Information Science, Majmaah University, Saudi Arabia"}], "References": [{"Title": "Classification of Vowels from Imagined Speech with Convolutional Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "2", "Page": "46", "JournalTitle": "Computers"}, {"Title": "Diagnosing <PERSON>'s disease with speech signal based on convolutional neural network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "4", "Page": "348", "JournalTitle": "International Journal of Computer Applications in Technology"}, {"Title": "Interactive Gated Decoder for Machine Reading Comprehension", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Linguistically Driven Multi-Task Pre-Training for Low-Resource Neural Machine Translation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Arabic Word Sense Disambiguation for Information Retrieval", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Investigating the Effect of Preprocessing Arabic Text on Offensive Language and Hate Speech Detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Dual Discriminator GAN: Restoring Ancient Yi Characters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}]}, {"ArticleId": 96353277, "Title": "Unsupervised feature selection based on incremental forward iterative Laplacian score", "Abstract": "<p>Feature selection facilitates intelligent information processing, and the unsupervised learning of feature selection has become important. In terms of unsupervised feature selection, the Laplacian score (LS) provides a powerful measurement and optimization method, and good performance has been achieved using the recent forward iterative Laplacian score (FILS) algorithm. However, there is still room for advancement. The aim of this paper is to improve the FILS algorithm, and thus, feature significance (SIG) is mainly introduced to develop a high-quality selection method, i.e., the incremental forward iterative Laplacian score (IFILS) algorithm. Based on the modified LS, the metric difference in the incremental feature process motivates SIG. Therefore, SIG offers a dynamic characterization by considering initial and terminal states, and it promotes the current FILS measurement on only the terminal state. Then, both the modified LS and integrated SIG acquire granulation nonmonotonicity and uncertainty, especially on incremental feature chains, and the corresponding verification is achieved by completing examples and experiments. Furthermore, a SIG-based incremental criterion of minimum selection is designed to choose optimization features, and thus, the IFILS algorithm is naturally formulated to implement unsupervised feature selection. Finally, an in-depth comparison of the IFILS algorithm with the FILS algorithm is achieved using data experiments on multiple datasets, including a nominal dataset of COVID-19 surveillance. As validated by the experimental results, the IFILS algorithm outperforms the FILS algorithm and achieves better classification performance.</p><p>© The Author(s), under exclusive licence to Springer Nature B.V. 2022, Springer Nature or its licensor holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "Feature selection;Feature significance;Forward iterative Laplacian score;Granulation nonmonotonicity and uncertainty;Incremental forward iterative Laplacian score;Unsupervised learning", "DOI": "10.1007/s10462-022-10274-6", "PubYear": 2023, "Volume": "56", "Issue": "5", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Sichuan Normal University, Chengdu, 610066 China. ;Institute of Intelligent Information and Quantum Information, Sichuan Normal University, Chengdu, 610066 China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Sichuan Normal University, Chengdu, 610066 China. ;Institute of Intelligent Information and Quantum Information, Sichuan Normal University, Chengdu, 610066 China. ;Research Center of Sichuan Normal University, National-Local Joint Engineering Laboratory of System Credibility Automatic Verification, Chengdu, 610066 China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Intelligent Information and Quantum Information, Sichuan Normal University, Chengdu, 610066 China. ;College of Computer Science, Sichuan Normal University, Chengdu, 610101 China."}], "References": [{"Title": "Evaluation of feature selection methods for text classification with small datasets using multiple criteria decision-making methods", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105836", "JournalTitle": "Applied Soft Computing"}, {"Title": "Unsupervised feature selection based extreme learning machine for clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "386", "Issue": "", "Page": "198", "JournalTitle": "Neurocomputing"}, {"Title": "Unsupervised feature selection for balanced clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "193", "Issue": "", "Page": "105417", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A survey on swarm intelligence approaches to feature selection in data mining", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100663", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Semi-supervised neighborhood discrimination index for feature selection", "Authors": "Qing-<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "204", "Issue": "", "Page": "106224", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Dynamic Salp swarm algorithm for feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113873", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Feature selection based on fuzzy-neighborhood relative decision entropy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "146", "Issue": "", "Page": "100", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Unsupervised attribute reduction for mixed data based on fuzzy rough sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "572", "Issue": "", "Page": "67", "JournalTitle": "Information Sciences"}, {"Title": "Tri-level attribute reduction in rough set theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "190", "Issue": "", "Page": "116187", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Statistical-mean double-quantitative K-nearest neighbor classification learning based on neighborhood distance measurement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "250", "Issue": "", "Page": "109018", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Measurement, modeling, reduction of decision-theoretic multigranulation fuzzy rough sets based on three-way decisions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "1550", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96353287, "Title": "Cover-based multiple book genre recognition using an improved multimodal network", "Abstract": "<p>Despite the idiom not to prejudge something by its outward appearance, we consider deep learning to learn whether we can judge a book by its cover or, more precisely, by its text and design. The classification was accomplished using three strategies, i.e., text only, image only, and both text and image. State-of-the-art CNNs (convolutional neural networks) models were used to classify books through cover images. The Gram and SE layers (squeeze and excitation) were used as an attention unit in them to learn the optimal features and identify characteristics from the cover image. The Gram layer enabled more accurate multi-genre classification than the SE layer. The text-based classification was done using word-based, character-based, and feature engineering-based models. We designed EXplicit interActive Network (EXAN) composed of context-relevant layers and multi-level attention layers to learn features from books title. We designed an improved multimodal fusion architecture for multimodal classification that uses an attention mechanism between modalities. The disparity in modalities convergence speed is addressed by pre-training each sub-network independently prior to end-to-end training of the model. Two book cover datasets were used in this study. Results demonstrated that text-based classifiers are superior to image-based classifiers. The proposed multimodal network outperformed all models for this task with the highest accuracy of 69.09 % and 38.12% for Latin and Arabic book cover datasets. Similarly, the proposed EXAN surpassed the extant text classification models by scoring the highest prediction rates of 65.20% and 33.8% for Latin and Arabic book cover datasets.</p>", "Keywords": "Book covers classification; CNN; Image classifiers; Multimodal learning; Text classifiers", "DOI": "10.1007/s10032-022-00413-8", "PubYear": 2023, "Volume": "26", "Issue": "1", "JournalId": 13682, "JournalTitle": "International Journal on Document Analysis and Recognition", "ISSN": "1433-2833", "EISSN": "1433-2825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Hazara University, Mansehra, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Hazara University, Mansehra, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, Hazara University, Mansehra, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Hazara University, Mansehra, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, Hazara University, Mansehra, Pakistan"}], "References": [{"Title": "Benchmarking Deep Learning Models for Classification of Book Covers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Visual-semantic graph neural network with pose-position attentive learning for group activity recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "491", "Issue": "", "Page": "217", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 96353338, "Title": "Excessive use of personal social media at work: antecedents and outcomes from dual-system and person-environment fit perspectives", "Abstract": "Purpose This study aims to investigate the antecedents and outcomes of excessive use of personal social media at work. The prevalence of personal social media in the work environment can easily lead to excessive use and negative consequences. Understanding the predictive factors and negative consequences of employees' excessive use of personal social media at work is important to develop their appropriate use of social media and improve their job performance. Design/methodology/approach Based on dual-system theory and the person-environment fit model, this study develops a research model to examine the effect of habit and self-regulation on excessive use of personal social media at work and that of the outcomes of excessive use on employee job performance through strain. This study conducts a questionnaire survey on 408 employees to test the research model and hypotheses empirically. Findings Results suggest that the imbalance between habit and self-regulation drives excessive personal social media use of employees at work. Furthermore, excessive use of personal social media has a strong impact on employee strain, which can significantly decrease job performance. Originality/value First, this study considers excessive use of personal social media at work as a result of two different cognitive systems, that is, an automatic system and a controlled system, thereby extending the dual-system theory to explain excessive use of personal social media in the work context. Second, unlike previous studies that focused on the outcomes or explored the antecedents of excessive social media use at work respectively, the study employs the person-environment fit model and examines the systematic influence of excessive social media use at work from a broad perspective by linking its antecedents and outcomes.", "Keywords": "Excessive use of personal social media;Dual-system theory;Person-environment fit model;Habit;Self-regulation;Strain;Job performance", "DOI": "10.1108/INTR-05-2021-0287", "PubYear": 2023, "Volume": "33", "Issue": "3", "JournalId": 20482, "JournalTitle": "Internet Research", "ISSN": "1066-2243", "EISSN": "2054-5657", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Yu", "Affiliation": "School of Management , Shanghai University , Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Management , Shanghai University , Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management , Zhejiang University of Technology , Hangzhou, China"}, {"AuthorId": 4, "Name": "Bao Dai", "Affiliation": "School of Management , Hefei University of Technology , Hefei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management , Zhejiang University of Technology , Hangzhou, China"}], "References": [{"Title": "Why We Cannot Resist Our Smartphones: Investigating Compulsive Use of Mobile SNS from a Stimulus-Response-Reinforcement Perspective", "Authors": "<PERSON><PERSON>; \" <PERSON>\"", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "175", "JournalTitle": "Journal of the Association for Information Systems"}, {"Title": "Enterprise social media: combating turnover in businesses", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "2", "Page": "591", "JournalTitle": "Internet Research"}, {"Title": "Appealing to Sense and Sensibility: System 1 and System 2 Interventions for Fake News on Social Media", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "3", "Page": "987", "JournalTitle": "Information Systems Research"}, {"Title": "Proximal and distal antecedents of problematic information technology use in organizations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "7", "Page": "139", "JournalTitle": "Internet Research"}, {"Title": "A Dual-Identity Perspective of Obsessive Online Social Gaming", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "5", "Page": "1245", "JournalTitle": "Journal of the Association for Information Systems"}]}, {"ArticleId": 96353457, "Title": "Review on Modelling and Verification of Secure Exams", "Abstract": "", "Keywords": "", "DOI": "10.1145/3545182", "PubYear": 2022, "Volume": "34", "Issue": "2", "JournalId": 592, "JournalTitle": "Formal Aspects of Computing", "ISSN": "0934-5043", "EISSN": "1433-299X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Exeter, Exeter, England, UK"}], "References": []}, {"ArticleId": 96353479, "Title": "Spatial justice in relation to the urban amenities distribution in Austin, Texas", "Abstract": "<p>In addition to enhancing our theoretical grasp of justice, thinking spatially about it can also reveal important new insights that broaden our practical understanding in order to advance justice and democracy. On the other hand, these opportunities won’t be as obvious if the spatial equities aren't made apparent and strong. Austin city has experienced a fast-urban growing in the past decades. As urban areas grow, the public facilities should increase. The purpose of this paper investigates Facilities in terms of public facilities. Even though we said that the concept of justice is very complex, it is possible to get an understanding of it by using a quantitative method. This paper explores the condition of urban justice and opportunities for accessibility to public facilities for all residents in Austin by using GIS data and the Fuzzy logic model. The facilities and services maps were made in GIS and after the Euclidean Distance and Reclassify function in Arc Map, the Fuzzy Logic model was used to analyze spatial justice. The result shows the facilities are distributed properly. Spatial justice is in the context of Austin and residents enjoy spatial justice.</p>", "Keywords": "Spatial justice; Urban amenities; Spatial analysis; Distribution; Austin", "DOI": "10.1007/s41324-022-00484-z", "PubYear": 2023, "Volume": "31", "Issue": "1", "JournalId": 2564, "JournalTitle": "Spatial Information Research", "ISSN": "2366-3286", "EISSN": "2366-3294", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Earth and Environment Science, Murray State University, Murray, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography, University of Tehran, Tehran, Iran; Department of Architecture, Bologna University, Bologna, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Geography, University of Tehran, Tehran, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Architecture, Bologna University, Bologna, Italy"}], "References": []}, {"ArticleId": 96353490, "Title": "A pollution sensitive fuzzy EPQ model with endogenous reliability and product deterioration based on lock fuzzy game theoretic approach", "Abstract": "<p>This article deals with a cost minimization objective function of an economic production quantity (EPQ) inventory model with production breakdown and deterioration. The process reliability and the environmental pollution due to over production have also been considered. The model has been split into two different scenarios according to the breaking time before and after the production period. In scenario 1, no machinery failure occurs during production run time and that of scenario 2 the failure occurs during production run time. We develop a deterministic cost minimization problem first then we fuzzify the model by considering the production rate, the demand rate and all the cost components as lock fuzzy numbers. We convert the fuzzy model into equivalent game problem by considering Gaussian normal strategic probabilities. The model has been solved with the help of different key vectors employed by the decision maker. We have shown that the value of the game might be changed with the change of different key vectors. A comparative study has been made with the numerical results of the general fuzzy and crisp models. Finally, graphical illustrations and sensitivity analysis have been done followed by a conclusion.</p>", "Keywords": "EPQ; Failure rate; Reliability; Deterioration; Game theory; Lock fuzzy set; Optimization", "DOI": "10.1007/s00500-022-07485-y", "PubYear": 2023, "Volume": "27", "Issue": "6", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Midnapore College (Autonomous), Midnapore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Midnapore College (Autonomous), Midnapore, India"}], "References": [{"Title": "A production inventory supply chain model with partial backordering and disruption under triangular linguistic dense fuzzy lock set approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "7", "Page": "5053", "JournalTitle": "Soft Computing"}, {"Title": "Solution of an EPQ model for imperfect production process under game and neutrosophic fuzzy approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106397", "JournalTitle": "Applied Soft Computing"}, {"Title": "An inventory model of a deteriorating product considering carbon emissions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "106694", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A robust two layer green supply chain modelling under performance based fuzzy game theoretic approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "152", "Issue": "", "Page": "107005", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 96353512, "Title": "A training sample selection method for predicting software defects", "Abstract": "<p>Software Defect Prediction (SDP) is an important method to analyze software quality and reduce development cost. Data from software life cycle has been widely used to predict the defect prone of software modules, and although many machine learning-based SDP models have been proposed, their predictive performance is not always satisfactory. Traditional machine learning-based classifiers usually assume that all samples have the same contribution to the training of SDP, which is not true. In fact, different training samples have different effects on the performance of the SDP model, the performance of machine learning-based SDP models is heavily dependent on the quality of training samples. For the above shortcoming of traditional machine learning-based classifiers, the contributions of this paper are as follows: (1) Inspired by the clustering algorithm, a method to calculate the contribution of each training sample to the SDP model is proposed, which not only considers the relationship between the contributions of the training samples to the SDP model, and also analyzes the influence of the distance between the sample and the category boundary on the performance of the SDP model, so it is different from the existing calculation method of sample contribution. (2) A Sample Selection (SS) method is proposed to improve the performance of the SDP model. It first calculates the contribution of each training sample based on several nearest neighbors of the sample and the label information of these neighbors, and then implements SS according to Hoeffding probability inequality and the contribution of each sample. To confirm the validity of the proposed SDP model, some experimental results are given. Both direct observations and statistical tests of the experimental results show that the SS method is very effective for improving the predictive performance of the SDP model.</p>", "Keywords": "Software defect prediction; Sample contribution; Sample selection; Predictive performance", "DOI": "10.1007/s10489-022-04044-8", "PubYear": 2023, "Volume": "53", "Issue": "10", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, Central China Normal University, Wuhan, People’s Republic of China"}], "References": [{"Title": "Comparison of Machine Learning Techniques for Software Quality Prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "20", "JournalTitle": "International Journal of Knowledge and Systems Science"}, {"Title": "Software defect prediction model based on distance metric learning", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "447", "JournalTitle": "Soft Computing"}, {"Title": "Cross-project software defect prediction based on domain adaptation learning and optimization", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "114637", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Improved prediction of software defects using ensemble machine learning techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "16", "Page": "10551", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Software defect prediction based on enhanced metaheuristic feature selection optimization and a hybrid deep neural network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "180", "Issue": "", "Page": "111026", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Heterogeneous stacked ensemble classifier for software defect prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "26", "Page": "37033", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 96353583, "Title": "Simplified artificial neural network based online adaptive control scheme for nonlinear systems", "Abstract": "<p>Complex neural network structures may not be appropriate for adaptive control applications, as large number of variable adaptive parameters have to be updated in each time step, thus increasing the computational burden, which makes the real-time implementation of the controller difficult. In this study, the ability of the neural networks to approximate non-linear dynamic systems is used to derive a neuro-based adaptive control with minimalistic architecture. A linear neural identifier is devised, which emulates a local linear model of the system by online adjustment of its parameters. Stability and optimal rate of convergence is ensured through an adaptive learning rate, determined using Lya<PERSON>nov stability theorems. The novelty of the control scheme lies in its minimalistic neural structure comprising of a single-linear neuron and, therefore, does not impose excessive computational burden on the system, making it feasible for real-time application. To assert our claims, benchmark examples from different domains are used to illustrate the effectiveness of the proposed controller. The neuro-controller is used in a water-lift plant to control the height of the water in a storage tank. Another example of a moving cart holding an inverted pendulum, an inherently unstable system that forms the basis of the robot-control mechanism, is also used. The controller is also tested on a complex non-linear higher-order power system to enhance stability by effectively damping the electromechanical oscillations. The superior performance of the controller is demonstrated by comparing with other recently reported controllers. Additional advantages of the proposed scheme include model-free control and requirement of only local measurements. The proposed method has potential applications in control problems that require adaptability, computational simplicity, and quick response.</p>", "Keywords": "Adaptive control; Identification algorithms; Neural network-based Online Control; Inverted Pendulum; Power system oscillations", "DOI": "10.1007/s00521-022-07760-x", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Institute of Technology Srinagar, Srinagar, India"}, {"AuthorId": 2, "Name": "Sheikh <PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Institute of Technology Srinagar, Srinagar, India"}], "References": [{"Title": "Learning elastic memory online for fast time series forecasting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "390", "Issue": "", "Page": "315", "JournalTitle": "Neurocomputing"}, {"Title": "Robust control based on adaptive neural network for Rotary inverted pendulum with oscillation compensation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "18", "Page": "14667", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Memory level neural network: A time-varying neural network for memory input processing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "425", "Issue": "", "Page": "256", "JournalTitle": "Neurocomputing"}, {"Title": "Neural network control system of cooperative robot based on genetic algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "14", "Page": "8217", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Adaptive control of manipulator based on neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "9", "Page": "4077", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Improved Design of Interval Type-2 Fuzzy based Wide Area Power System Stabilizer for Inter-area Oscillation Damping", "Authors": "<PERSON> <PERSON>; <PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "83", "Issue": "", "Page": "103957", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Full-state neural network observer-based hybrid quantum diagonal recurrent neural network adaptive tracking control", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "15", "Page": "9221", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Efficient and effective training of sparse recurrent neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "15", "Page": "9625", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A novel adaptive control design method for stochastic nonlinear systems using neural network", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "15", "Page": "9259", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Neural adaptive appointed-time control for flexible air-breathing hypersonic vehicles: an event-triggered case", "Authors": "Yi Shi; Xingling Shao", "PubYear": 2021, "Volume": "33", "Issue": "15", "Page": "9545", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An improved neural network tracking control strategy for linear motor-driven inverted pendulum on a cart and experimental study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "7", "Page": "5161", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 96353587, "Title": "Equitable scheduling on a single machine", "Abstract": "<p>We introduce a natural but seemingly yet unstudied variant of the problem of scheduling jobs on a single machine so as to minimize the number of tardy jobs. The novelty of our new variant lies in simultaneously considering several instances of the problem at once. In particular, we have n clients over a period of m days, where each client has a single job with its own processing time and deadline per day. Our goal is to provide a schedule for each of the m days, so that each client is guaranteed to have their job meet its deadline in at least \\(k \\le m\\) days. This corresponds to an equitable schedule where each client is guaranteed a minimal level of service throughout the period of m days. We provide a thorough analysis of the computational complexity of three main variants of this problem, identifying both efficient algorithms and worst-case intractability results.</p>", "Keywords": "Resource allocation; Fairness; Equity; Fixed-parameter tractability; Approximation", "DOI": "10.1007/s10951-022-00754-6", "PubYear": 2023, "Volume": "26", "Issue": "2", "JournalId": 9977, "JournalTitle": "Journal of Scheduling", "ISSN": "1094-6136", "EISSN": "1099-1425", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Algorithmics and Computational Complexity, Faculty IV, TU Berlin, Berlin, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering and Management, Ben-Gurion University of the Negev, Beer-Sheva, Israel"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Durham University, Durham, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, Ben-Gurion University of the Negev, Beer-Sheva, Israel"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Algorithmics and Computational Complexity, Faculty IV, TU Berlin, Berlin, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, Ben-Gurion University of the Negev, Beer-Sheva, Israel"}], "References": [{"Title": "Parameterized Multi-Scenario Single-Machine Scheduling Problems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "82", "Issue": "9", "Page": "2644", "JournalTitle": "Algorithmica"}]}, {"ArticleId": 96353612, "Title": "Contrastive hashing with vision transformer for image retrieval", "Abstract": "<p>Hashing techniques have attracted considerable attention owing to their advantages of efficient computation and economical storage. However, it is still a challenging problem to generate more compact binary codes for promising performance. In this paper, we propose a novel contrastive vision transformer hashing method, which seamlessly integrates contrastive learning and vision transformers (ViTs) with hash technology into a well-designed model to learn informative features and compact binary codes simultaneously. First, we modify the basic contrastive learning framework by designing several hash layers to meet the specific requirement of hash learning. In our hash network, ViTs are applied as backbones for feature learning, which is rarely performed in existing hash learning methods. Then, we design a multiobjective loss function, in which contrastive loss explores discriminative features by maximizing agreement between different augmented views from the same image, similarity preservation loss performs pairwise semantic preservation to enhance the representative capabilities of hash codes, and quantization loss controls the quantitative error. Hence, we can facilitate end-to-end joint training to improve the retrieval performance. The encouraging experimental results on three widely used benchmark databases demonstrate the superiority of our algorithm compared with several state-of-the-art hashing algorithms.</p>", "Keywords": "contrastive learning;deep hashing;image retrieval;vision transformer", "DOI": "10.1002/int.23082", "PubYear": 2022, "Volume": "37", "Issue": "12", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering Shandong Normal University  Jinan China;Shandong Provincial Key Laboratory for Distributed Computer Software Novel Technology Shandong Normal University  Jinan China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering Shandong Normal University  Jinan China;Shandong Provincial Key Laboratory for Distributed Computer Software Novel Technology Shandong Normal University  Jinan China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Mathematical Sciences University of Leicester  Leicester United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering Shandong University of Finance and Economics  Jinan China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence Sun Yat‐sen University  Zhuhai China"}], "References": [{"Title": "Secure video retrieval using image query on an untrusted cloud", "Authors": "<PERSON><PERSON> Yan; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106782", "JournalTitle": "Applied Soft Computing"}, {"Title": "Querying little is enough: Model inversion attack via latent information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "2", "Page": "681", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "ImpSuic: A quality updating rule in mixing coins with maximum utilities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "3", "Page": "1182", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "MHAT: An efficient model-heterogenous aggregation training scheme for federated learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "493", "JournalTitle": "Information Sciences"}, {"Title": "Deep center-based dual-constrained hashing for discriminative face image retrieval", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "107976", "JournalTitle": "Pattern Recognition"}, {"Title": "ELAA: An efficient local adversarial attack using model interpreters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "12", "Page": "10598", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A precision‐preferred comprehensive information extraction system for clinical articles in traditional Chinese Medicine", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "8", "Page": "4994", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Intelligent algorithm for dynamic functional brain network complexity from CN to AD", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "8", "Page": "4715", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Motif‐based embedding label propagation algorithm for community detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "3", "Page": "1880", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Cross‐modal retrieval based on deep regularized hashing constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "9", "Page": "6508", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 96353665, "Title": "SerialTrack: ScalE and rotation invariant augmented Lagrangian particle tracking", "Abstract": "We present a new particle tracking algorithm for accurately resolving large deformation and rotational motion fields, which takes advantage of both local and global particle tracking algorithms. We call this method ScalE and Rotation Invariant Augmented Lagrangian Particle Tracking (SerialTrack). This method builds an iterative scale and rotation invariant topology-based feature vector for each particle within a multi-scale tracking algorithm. The global kinematic compatibility condition is applied as a global augmented Lagrangian constraint to enhance tracking accuracy. An open source software package implementing this numerical approach to track both 2D and 3D, incremental and cumulative deformation fields is provided.", "Keywords": "Particle tracking ; Topology-based feature vector ; Augmented Lagrangian ; Finite deformation", "DOI": "10.1016/j.softx.2022.101204", "PubYear": 2022, "Volume": "19", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Wisconsin-Madison, Mechanical Engineering, Madison, WI, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Wisconsin-Madison, Mechanical Engineering, Madison, WI, USA;Department of Mechanical Engineering, Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Institute of Standards and Technology, Gaithersburg, MD, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Wisconsin-Madison, Mechanical Engineering, Madison, WI, USA;School of Engineering, Brown University, Providence, RI, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Wisconsin-Madison, Mechanical Engineering, Madison, WI, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Wisconsin-Madison, Mechanical Engineering, Madison, WI, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of Wisconsin-Madison, Mechanical Engineering, Madison, WI, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Graduate Aerospace Laboratories, California Institute of Technology, Pasadena, CA, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Graduate Aerospace Laboratories, California Institute of Technology, Pasadena, CA, USA"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "University of Wisconsin-Madison, Mechanical Engineering, Madison, WI, USA;Corresponding author"}], "References": [{"Title": "FM-Track: A fiducial marker tracking software for studying cell mechanics in a three-dimensional environment", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100417", "JournalTitle": "SoftwareX"}, {"Title": "Part2Track: A MATLAB package for double frame and time resolved Particle Tracking Velocimetry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100413", "JournalTitle": "SoftwareX"}]}, {"ArticleId": 96353713, "Title": "Compact broadband bi‐directional circularly polarized fan‐shaped patch with parasitic annular groove for\n \n S \n \n ‐band satellite applications", "Abstract": "<p>A novel bi-directional circularly polarized (CP) antenna with low profile, real planar configuration and broad axial ratio (AR) bandwidth is presented for S-band satellite relay communication application. The designed antenna consists of four fan-shaped patches, a sequentially rotating feed network and ground plane with annular gaps. Four fan-shaped patches are directly integrated with the feed network on the same FR4 substrate for good impedance matching and compact size. To realize the two-way radiation characteristics and excellent AR bandwidth, one parasitic annular groove on the ground plane is designed. The sequential rotation feeding network can provide good port characteristics within the available bandwidth and good AR. The properties of designed antenna was tested to validate its simulation. The final prototype is 83 × 83 × 0.8 mm<sup>3</sup> in size and it can generate a broad impedance bandwidth for voltage standing wave ratio (VSWR) <2 of 36.6% (2.32–3.36 GHz), 3-dB AR bandwidth of 21.3% (2.6–3.22 GHz) and gain over 5.4 dB in the bi-directional direction. The miniaturization, low-cost and good CP radiation for this proposed antenna make it a better candidate for future applications in the field of spacecraft relay communication.</p>", "Keywords": "broad axial ratio bandwidth;circular polarization;fan-shaped patch;sequential rotating", "DOI": "10.1002/mmce.23395", "PubYear": 2022, "Volume": "32", "Issue": "11", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "Xue<PERSON> Li", "Affiliation": "College of Electronic and Electrical Engineering Henan Normal University  Xinxiang China;Henan Engineering Laboratory of additive intelligent manufacturing Henan Normal University  Xinxiang China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic and Electrical Engineering Henan Normal University  Xinxiang China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Electronic and Electrical Engineering Henan Normal University  Xinxiang China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>g Li", "Affiliation": "College of Electronic and Electrical Engineering Henan Normal University  Xinxiang China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Electronic and Electrical Engineering Henan Normal University  Xinxiang China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Electronic and Electrical Engineering Henan Normal University  Xinxiang China;Henan Engineering Laboratory of additive intelligent manufacturing Henan Normal University  Xinxiang China"}], "References": [{"Title": "A compact wideband circularly polarized antenna with two pairs of driven patch arrays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "11", "Page": "", "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering"}, {"Title": "A wideband circularly polarized\n S \n ‐shaped slot antenna", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "5", "Page": "e22612", "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering"}]}, {"ArticleId": 96354127, "Title": "Covid-19 classification using sigmoid based hyper-parameter modified DNN for CT scans and chest X-rays", "Abstract": "<p>Coronavirus disease (COVID-19) is an infectious disease caused by the SARS-CoV-2 virus. Diagnosis of Computed Tomography (CT), and Chest X-rays (CXR) contains the problem of overfitting, earlier diagnosis, and mode collapse. In this work, we predict the classification of the Corona in CT and CXR images. Initially, the images of the dataset are pre-processed using the function of an adaptive Gaussian filter for de-nosing the image. Once the image is pre-processed it goes to Sigmoid Based Hyper-Parameter Modified DNN(SHMDNN). The hyperparameter modification makes use of the optimization algorithm of adaptive grey wolf optimization (AGWO). Finally, classification takes place and classifies the CT and CXR images into 3 categories namely normal, Pneumonia, and COVID-19 images. Better accuracy of 99.9% is reached when compared to different DNN networks.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2022, Springer Nature or its licensor holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "AGWO;Covid-19;DNN;Gaussian filter;Pre-processing;Sigmoid value", "DOI": "10.1007/s11042-022-13783-2", "PubYear": 2023, "Volume": "82", "Issue": "8", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "Department of ECE, GMR Institute of Technology, Rajam, India."}, {"AuthorId": 2, "Name": "K Srividya", "Affiliation": "Department of CSE, GMR Institute of Technology, Rajam, India."}, {"AuthorId": 3, "Name": "<PERSON> <PERSON>", "Affiliation": "Department of CS&amp;SE, Andhra University College of Engineering, Visakhapatnam, India."}], "References": [{"Title": "Deep Neural Network-Based Screening Model for COVID-19-Infected Patients Using Chest X-Ray Images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "3", "Page": "2151004", "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence"}, {"Title": "OptCoNet: an optimized convolutional neural network for an automatic diagnosis of COVID-19", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>, <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1351", "JournalTitle": "Applied Intelligence"}, {"Title": "Deep neural network to detect COVID-19: one architecture for both CT Scans and Chest X-rays", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "5", "Page": "2777", "JournalTitle": "Applied Intelligence"}, {"Title": "COVID-DeepNet: Hybrid Multimodal Deep Learning System for Improving COVID-19 Pneumonia Detection in Chest X-ray Images", "Authors": "<PERSON><PERSON> S<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "2", "Page": "2409", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Automatic COVID-19 detection from X-ray images using ensemble learning with convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "3", "Page": "1111", "JournalTitle": "Pattern Analysis and Applications"}]}, {"ArticleId": 96354142, "Title": "CLQLMRS: improving cache locality in MapReduce job scheduling using Q-learning", "Abstract": "Scheduling of MapReduce jobs is an integral part of Hadoop and effective job scheduling has a direct impact on Hadoop performance. Data locality is one of the most important factors to be considered in order to improve efficiency, as it affects data transmission through the system. A number of researchers have suggested approaches for improving data locality, but few have considered cache locality. In this paper, we present a state-of-the-art job scheduler, CLQLMRS (Cache Locality with Q-Learning in MapReduce Scheduler) for improving both data locality and cache locality using reinforcement learning. The proposed algorithm is evaluated by various experiments in a heterogeneous environment. Experimental results show significantly decreased execution time compared with FIFO, Delay, and the Adaptive Cache Local scheduler.", "Keywords": "Computer Communication Networks;Special Purpose and Application-Based Systems;Information Systems Applications (incl.Internet);Computer Systems Organization and Communication Networks;Computer System Implementation;Software Engineering/Programming and Operating Systems", "DOI": "10.1186/s13677-022-00322-5", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, North Tehran Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, North Tehran Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Science and Research Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 4, "Name": "Douglas G. Down", "Affiliation": "Department of Computing and Software, McMaster University, Hamilton, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Sharif University of Technology, Tehran, Iran"}], "References": [{"Title": "Q-learning based dynamic task scheduling for energy-efficient cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "361", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Task scheduling, resource provisioning, and load balancing on scientific workflows using parallel SARSA reinforcement learning agents and genetic algorithm", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "3", "Page": "2800", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A classification framework for straggler mitigation and management in a heterogeneous Hadoop cluster: A state-of-art survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "7621", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Job scheduling for big data analytical applications in clouds: A taxonomy study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "135", "Issue": "", "Page": "129", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 96354163, "Title": "5G Smart Sensor Network-Based e-Commerce Investment Risk Management System for SMEs", "Abstract": "<p>The rapid development of Internet technology in the new era has led to the prosperous development of the e-commerce industry, and more and more traditional retail enterprises are transforming into e-commerce enterprises in line with the development of the times to survive. Based on the relevant financial risk management theories, this paper designs an e-commerce investment risk management system for small and medium-sized enterprises based on the 5G intelligent sensor network, identify the financial risks arising from the e-commerce model, and takes appropriate countermeasures to manage the identified risks. For the optimal coverage problem of homogeneous terminals in the subnetwork of a smart sensing network, this paper introduces the concept of area coverage contribution, transforms the problem of selecting the optimal coverage subset in a large area into the problem of selecting the substrate particles, and organically combines the particle swarm algorithm in the bionic algorithm to consider the network survival period, area sensing coverage, and iteration cost for the hierarchical distributed large-scale IoT architecture. A minimum coverage subset dormancy scheduling algorithm based on homogeneous terminals is proposed. The simulation results are compared with the greedy algorithm and the multioptimized target task assignment algorithm, and it is demonstrated that this algorithm can consider multiple optimization objectives such as energy balance, scheduling cycle, timeout rate, network survival cycle, and system reliability and perform well in the case of large-scale deployment. Finally, an appropriate financial risk evaluation model is constructed to objectively evaluate the identified financial risks, and targeted financial risk control measures and suggestions are proposed based on four aspects: financing, investment, operation, and others.</p>", "Keywords": "", "DOI": "10.1155/2022/4287851", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Shanghai for Science and Technology, Shanghai 200093, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "University of Shanghai for Science and Technology, Shanghai 200093, China"}], "References": [{"Title": "Study on early warning of E-commerce enterprise financial risk based on deep learning algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "1", "Page": "21", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Addressing disasters in smart cities through UAVs path planning and 5G communications: A systematic review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114", "JournalTitle": "Computer Communications"}, {"Title": "Secure Device-to-Device communications for 5G enabled Internet of Things applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114", "JournalTitle": "Computer Communications"}, {"Title": "5G-Wireless Sensor Networks for Smart Grid-Accelerating technology’s progress and innovation in the Kingdom of Saudi Arabia", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "46", "JournalTitle": "Procedia Computer Science"}, {"Title": "A robust and distributed architecture for 5G-enabled networks in the smart blockchain era", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Fadi AL-Turjman", "PubYear": 2022, "Volume": "181", "Issue": "", "Page": "293", "JournalTitle": "Computer Communications"}, {"Title": "Secure routing with multi-watchdog construction using deep particle convolutional model for IoT based 5G wireless sensor networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "71", "JournalTitle": "Computer Communications"}, {"Title": "5G/IoT-enabled UAVs for multimedia delivery in industry-oriented applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "13-14", "Page": "8627", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Research on digital production technology for traditional manufacturing enterprises based on industrial Internet of Things in 5G era", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "Page": "1101", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Research and application of wireless sensor network technology in power transmission and distribution system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "2", "Page": "199", "JournalTitle": "Intelligent and Converged Networks"}]}, {"ArticleId": 96354179, "Title": "Retraction Note: Multimedia based intelligent network big data optimization model", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-022-13975-w", "PubYear": 2023, "Volume": "82", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Lingjuan Tai", "Affiliation": "Faculty of Management and Economics, Kunming University of Science and Technology, Kunming, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Management and Economics, Kunming University of Science and Technology, Kunming, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Urban and Rural Construction & Engineering Management, Kunming University, Kunming, China"}], "References": []}, {"ArticleId": 96354220, "Title": "Regulating social media and influencers within Vietnam", "Abstract": "en <p>Ms. <PERSON><PERSON><PERSON> is the CEO of Dai <PERSON>, a tourism complex in Binh Duong province in southern Vietnam, and in 2021 she started livestreaming on social media. <PERSON>'s livestreams would humiliate celebrities, include personal attacks, criticize media and charity organizations, and use harassing language, resulting in the Vietnamese government regulating social media influencers. Vietnam is a centralized government that promotes the regulation of social media influencers to control the population by censoring content and banning certain discussion topics. This is exemplary of Vietnam's media regulatory environment: Vietnam wants to take advantage of the opportunities that new media and technology bring to promote economic integration, yet the Vietnamese government prefers to accept them by enforcing their adherence to local regulations via regulatory and economic measures. As a result, the Vietnamese government has increased control over livestreaming influencers: any social media account with more than 10,000 followers must provide account holders' contact information to government authorities. Further, social media platforms will be asked to remove content that has been flagged as problematic by government officials, shoring up Vietnam's view on regulation toward foreign social media platforms. These regulations allow the government to control the narrative around social issues and prevent dissenting voices from being heard. Departing from the analysis of <PERSON>'s case study, this article examines the current regulatory status of social media users with a specific focus on Vietnamese influencers. The paper also extrapolates the tension that the country faces as it invests and develops its digital and creative industries.</p> <h3 >摘要</h3> zh <p>2021年，越南南部平阳省大型旅游综合体Dai Nam <PERSON>的首席执行官Hang Nguyen Phuong（著名女商人）的脸书直播持续了三个多小时，并点名羞辱了名人的公共行为。这不仅对于特定类型的越南社交媒体网红而言是一个具有里程碑意义的时刻，而且也标志着越南社交媒体及网红的新时代。作为一个已实施严格监管的国家，越南的社交媒体历史是存在压力的。越南对数字媒体未来进行投资，同时试图保持一种社会健全的传播方式。作为近期YouTube事件的结果，越南政府正寻求通过加强对直播的管制来向网红施压:任何拥有超过10,000名追随者的社交媒体账户都必须向当局提供联系信息。社交媒体平台将被要求删除被政府官员标记为有问题的内容，这加强了越南对外国社交媒体平台监管的看法，而不仅仅是YouTube或脸书。本研究从分析越南网红Hang的案例出发，研究了对社交媒体用户（聚焦于网红）的监管现状。基于几个近期案例研究，我们强调了越南在该领域的监管现状，同时推断了该国在投资和发展其数字和创意产业时面临的紧张局势。</p> <h3 >Resumen</h3> es <p>En 2021, la transmisión en vivo de Facebook de Hang Nguyen Phuong, una empresaria muy famosa y directora ejecutiva de Dai Nam Van Hien, un enorme complejo turístico en la provincia de Binh Duong en el sur de Vietnam, duró más de tres horas y nombró y avergonzó a celebridades por su público. comportamientos Este no solo fue un momento monumental para un tipo particular de personas influyentes en las redes sociales vietnamitas, sino que también significa una nueva era para las redes sociales y sus personas influyentes dentro de Vietnam. Como país que ya tiene regulaciones estrictas, Vietnam tiene una historia tensa en las redes sociales. Invierte en su futuro de medios digitales mientras intenta mantener un enfoque socialmente sólido de la comunicación. Como resultado de este caso reciente de YouTube, el gobierno vietnamita busca presionar a los influencers en línea aumentando el control sobre la transmisión en vivo: cualquier cuenta de redes sociales con más de 10,000 seguidores debe proporcionar información de contacto a las autoridades. Se pedirá a las plataformas de redes sociales que eliminen el contenido que los funcionarios del gobierno hayan señalado como problemático, lo que reforzará la opinión de Vietnam sobre la regulación hacia las plataformas de redes sociales extranjeras, y no solo YouTube o Facebook específicamente. Partiendo del análisis del caso de Hang, un influencer en Vietnam, esta investigación examina el estado actual de la regulación sobre los usuarios de redes sociales con un enfoque específico en los influencers. Sobre la base de varios estudios de casos recientes, destacamos el estado actual de la regulación en este espacio para Vietnam al mismo tiempo que extrapolamos la tensión que enfrenta el país a medida que invierte y desarrolla sus industrias digitales y creativas.</p>", "Keywords": "digital platforms;influencers;regulation;social media;Vietnamese media;网红;数字平台;社交媒体;越南媒体;监管;Influencers;plataformas digitales;redes sociales;medios vietnamitas;regulación", "DOI": "10.1002/poi3.325", "PubYear": 2022, "Volume": "14", "Issue": "3", "JournalId": 4443, "JournalTitle": "Policy & Internet", "ISSN": "1944-2866", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Viet Tho Le", "Affiliation": "<PERSON> University Perth Western Australia Australia;Journalism Division, Faculty of Public Relations and Communication Van Lang University Ho Chi Minh City Vietnam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Media & Communication, Faculty of Arts and Social Sciences The University of Sydney Sydney New South Wales Australia"}], "References": [{"Title": "When the machine hails you, do you turn? Media orientations and the constitution of digital space", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "First Monday"}]}, {"ArticleId": 96354406, "Title": "Existence and stability of strong solutions to the <PERSON><PERSON><PERSON> model in three dimensions", "Abstract": "This work is devoted to the analysis of the strong solutions to the <PERSON><PERSON> (AGG) model in three dimensions. First, we prove the existence of local-in-time strong solutions originating from an initial datum \n \n (u_0, \\phi_0)\\in \\mathbf{H}^{1}_{\\sigma} \\times H^2(\\Omega) \n \n such that \n \n \\mu_0 \\in H^1(\\Omega) \n \n and \n \n |\\overline{\\phi_0}|\\leq 1 \n \n . For the subclass of initial data that are strictly separated from the pure phases, the corresponding strong solutions are locally unique. Finally, we show a stability estimate between the solutions to the AGG model and the model H. These results extend the analysis achieved by the author in 2021 from two-dimensional bounded domains to three-dimensional ones.", "Keywords": "AGG model; Navier–<PERSON>-<PERSON>–<PERSON> system; strong solutions; unmatched densities", "DOI": "10.4171/IFB/482", "PubYear": 2022, "Volume": "24", "Issue": "4", "JournalId": 27622, "JournalTitle": "Interfaces and Free Boundaries", "ISSN": "1463-9963", "EISSN": "1463-9971", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Imperial College London, UK"}], "References": []}, {"ArticleId": 96354463, "Title": "IoT-Enabled Pest Identification and Classification with New Meta-Heuristic-Based Deep Learning Framework", "Abstract": "", "Keywords": "", "DOI": "10.1080/01969722.2022.2122001", "PubYear": 2024, "Volume": "55", "Issue": "2", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "Atul <PERSON>", "Affiliation": "IT Department, Pimpri Chinchwad College of Engineering, Pune, India"}, {"AuthorId": 2, "Name": "Kapil N. <PERSON>", "Affiliation": "IT Department, Pimpri Chinchwad College of Engineering, Pune, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> D<PERSON>", "Affiliation": "IT Department, Pimpri Chinchwad College of Engineering, Pune, India"}], "References": [{"Title": "Multi-model LSTM-based convolutional neural networks for detection of apple diseases and pests", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "7", "Page": "3335", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An Internet of Things assisted Unmanned Aerial Vehicle based artificial intelligence model for rice pest detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "", "Page": "103607", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Machine learning ensemble with image processing for pest identification and classification in field crops", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "13", "Page": "7491", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 96354540, "Title": "A Functional Abstraction of Typed Invocation Contexts", "Abstract": "<p>In their paper \"A Functional Abstraction of Typed Contexts\", <PERSON><PERSON> and <PERSON><PERSON> show how to derive a monomorphic type system of the shift and reset operators from a CPS semantics. In this paper, we show how this method scales to <PERSON><PERSON><PERSON>'s control and prompt operators. Compared to shift and reset, control and prompt exhibit a more dynamic behavior, in that they can manipulate a trail of contexts surrounding the invocation of previously captured continuations. Our key observation is that, by adopting a functional representation of trails in the CPS semantics, we can derive a type system that encodes all and only constraints imposed by the CPS semantics.</p>", "Keywords": "Computer Science - Programming Languages", "DOI": "10.46298/lmcs-18(3:34)2022", "PubYear": 2022, "Volume": "18, Issue 3", "Issue": "", "JournalId": 10075, "JournalTitle": "Logical Methods in Computer Science", "ISSN": "", "EISSN": "1860-5974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tokyo Institute of Technology, 2-12-1, Ookayama, Meguro-ku, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ochanomizu University, 2-1-1, <PERSON><PERSON><PERSON>, Bunkyo-ku, Tokyo, Japan"}, {"AuthorId": 3, "Name": "Kaho Honda", "Affiliation": "Ochanomizu University, 2-1-1, <PERSON><PERSON><PERSON>, Bunkyo-ku, Tokyo, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Ochanomizu University, 2-1-1, <PERSON><PERSON><PERSON>, Bunkyo-ku, Tokyo, Japan"}], "References": []}, {"ArticleId": 96354602, "Title": "Schema-Based Automata Determinization", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.370.4", "PubYear": 2022, "Volume": "370", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Inria, Université de Lille, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Inria, Université de Lille, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Inria, Université de Lille, France"}], "References": []}, {"ArticleId": 96354712, "Title": "Systems engineering applied to urban planning and development: A review and research agenda", "Abstract": "<p>Systems engineering tools and methodologies are increasingly being used in urban planning and sustainable development applications. Such tools were previously extensively used for urban planning during the 1960s and 1970s in the United States, only to result in high profile failures and pushback from urban planners, politicians, and the public. In order to better understand why this occurred, what has changed, and how we can avoid such failures moving forward, this study conducts a systematic review and an integrative review of the systems engineering and critical literature. These reviews are used to identify eight common pitfalls and organize them into key themes. Technological and methodological developments that may address each of these pitfalls are considered and recommendations are made for future applications of systems engineering to planning contexts. Finally, examples are provided of systems engineering being used productively in a way consistent with these recommendations for sustainable development applications.</p>", "Keywords": "lessons learned;review;stakeholders;sustainabilitys;urban development;urban planning", "DOI": "10.1002/sys.21642", "PubYear": 2023, "Volume": "26", "Issue": "1", "JournalId": 2331, "JournalTitle": "Systems Engineering", "ISSN": "1098-1241", "EISSN": "1520-6858", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Media Arts and Sciences Program Massachusetts Institute of Technology Cambridge Massachusetts USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Aeronautics and Astronautics Massachusetts Institute of Technology Cambridge Massachusetts USA"}], "References": [{"Title": "Handling the COVID‐19 crisis: Toward an agile model‐based systems approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "5", "Page": "656", "JournalTitle": "Systems Engineering"}, {"Title": "A Transdisciplinary Design and Implementation of Sustainable Agricultural Principles in the Waikato Region of New Zealand", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "1", "Page": "637", "JournalTitle": "INCOSE International Symposium"}, {"Title": "Addressing the Sustainable Development Goals with a System‐of‐Systems for Monitoring Arctic Coastal Regions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "1", "Page": "604", "JournalTitle": "INCOSE International Symposium"}]}, {"ArticleId": 96354717, "Title": "Radiation pattern synthesis in conformal antenna arrays using modified convex optimization technique", "Abstract": "<p>In this paper, a modified convex optimization technique is used for radiation pattern correction in a cylindrical-shaped conformal microstrip array antenna. The technique uses numerical simulations to optimize the amplitude and phase excitations, with the goal to decrease the Euclidean distance between the desired field pattern and the obtained (simulated/measured) field pattern while maintaining the main beam direction, null's location, and side lobe levels under control. Two prototypes of 1 × 4 and 2 × 4 conformal microstrip antenna array deformed from linear/planar structure to the prescribed cylindrical shape, with different radii of curvature, are studied to demonstrate the performance of the proposed technique. The proposed convex optimization model when applied to conformal antenna array possesses fast computing speed and high convergence accuracy for radiation pattern synthesis, which can be a valuable tool for engineering applications.</p>", "Keywords": "conformal array antenna;convex optimization;least square optimization;mutual coupling compensation;radiation pattern correction", "DOI": "10.1002/mmce.23393", "PubYear": 2022, "Volume": "32", "Issue": "11", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering University of Engineering and Technology Peshawar  Peshawar Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering University of Engineering and Technology Peshawar  Peshawar Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering University of Science & Technology, Bannu  Bannu Khyber Pakhtunkhwa Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering University of Engineering and Technology Peshawar  Peshawar Pakistan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Physics Islamia College Peshawar  Peshawar Pakistan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Aviation Sciences Amman Arab University  Amman Jordan"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Signal Theory and Communications Universidad Carlos III di Madrid  Leganés Madrid Spain"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science KTH Royal Institute of Technology  Stockholm Sweden"}], "References": []}, {"ArticleId": 96354925, "Title": "Quantum-Inspired Evolutionary Algorithm for Optimal Service-Matching Task Assignment", "Abstract": "<p>This paper proposes a quantum-inspired evolutionary algorithm (QiEA) to solve an optimal service-matching task-assignment problem. Our proposed algorithm comes with the advantage of generating always feasible population individuals and, thus, eliminating the necessity for a repair step. That is, with respect to other quantum-inspired evolutionary algorithms, our proposed QiEA algorithm presents a new way of collapsing the quantum state that integrates the problem constraints in order to avoid later adjusting operations of the system to make it feasible. This results in lower computations and also faster convergence. We compare our proposed QiEA algorithm with three commonly used benchmark methods: the greedy algorithm, Hungarian method and Simplex, in five different case studies. The results show that the quantum approach presents better scalability and interesting properties that can be used in a wider class of assignment problems where the matching is not perfect.</p>", "Keywords": "quantum-inspired evolutionary algorithm; service-matching task assignment; greedy algorithm; Hungarian method; Simplex quantum-inspired evolutionary algorithm ; service-matching task assignment ; greedy algorithm ; Hungarian method ; Simplex", "DOI": "10.3390/info13090438", "PubYear": 2022, "Volume": "13", "Issue": "9", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, University of California Irvine, Irvine, CA 92697, USA; Corresponding author"}, {"AuthorId": 2, "Name": "Sol<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, University of California Irvine, Irvine, CA 92697, USA"}], "References": []}, {"ArticleId": 96354979, "Title": "Nonlinearity handling in MPC for Power Congestion management in sub-transmission areas", "Abstract": "This paper proposes congestion management solutions based on Model Predictive Control (MPC) principles for transmission network zones. The contribution resides in the use of logical variables for describing the nonlinearities related to the modelling of the physical aspects in power curtailment, or some desired control goals. This allows for the representation of the nonlinear model of a sub-transmission zone with a storage device in a linear Mixed Logical Dynamical (MLD) formulation, then paving the way to the utilisation of linear mixed integer programming for the optimization problem. Moreover, part of the contribution considers supplementary temporal specifications for the energy storage device utilisation. This is modelled using additional temporal and logical variables as part of the system state and control signal. Consequently, the extended model of the power network zone is formulated as a linear MLD system and integrated to the MPC design. The proposed controllers are validated through simulations on an industrial case-study.", "Keywords": "", "DOI": "10.1016/j.ifacol.2022.09.003", "PubYear": 2022, "Volume": "55", "Issue": "16", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Signals and Systems (L2S), CNRS, CentraleSupélec, Paris-Saclay University, Gif-sur-Yvette , France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Signals and Systems (L2S), CNRS, CentraleSupélec, Paris-Saclay University, Gif-sur-Yvette , France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Signals and Systems (L2S), CNRS, CentraleSupélec, Paris-Saclay University, Gif-sur-Yvette , France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Réseau de Transport d'Electricite (RTE), Paris, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Réseau de Transport d'Electricite (RTE), Paris, France"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Réseau de Transport d'Electricite (RTE), Paris, France"}], "References": []}, {"ArticleId": 96355002, "Title": "Nonlinear optimal control for Maglev platform roll motion", "Abstract": "The stable manifold method is applied to construct a nonlinear real-time feedback optimal control system for the roll motion and vertical position of a certain maglev platform. The chosen platform uses combined electromagnetic suspensions consisting of permanent magnets and upper and lower electromagnets. Within the given technical gaps between the platform and guideway, the magnetic forces provide highly nonlinear effects. This makes this object a multi-input multi-output (MIMO) nonlinear control system. The stable manifold method is applied to construct an optimal nonlinear stabilizing controller. The benefit of the nonlinear control in comparison with a linear regulator is illustrated on an ensemble of perturbed motions caused by a set of initial deviations covering the necessary engineering stabilization range.", "Keywords": "Stability of nonlinear systems ; nonlinear ; optimal control ; HJE ; stable manifold ; maglev system ; real-time feedback ; ensemble of trajectories", "DOI": "10.1016/j.ifacol.2022.09.060", "PubYear": 2022, "Volume": "55", "Issue": "16", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Saint Petersburg State University, 7/9 Universitetskaya nab., St. Petersburg, 199034 Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science and Engineering, Nanzan University, Showa-ku 464-8673, Japan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Saint Petersburg State University, 7/9 Universitetskaya nab., St. Petersburg, 199034 Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Saint Petersburg State University, 7/9 Universitetskaya nab., St. Petersburg, 199034 Russia"}], "References": [{"Title": "Offset-free Nonlinear Model Predictive Control by the Example of Maglev Vehicles", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "83", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 96355018, "Title": "Online Feature Selection Using Sparse Gradient", "Abstract": "Feature Selection (FS) is an important preprocessing step in data analytics. It is used to select a subset of the original feature set such that the selected subset does not affect the classification performance significantly. Its objective is to remove irrelevant and redundant features from the original dataset. FS can be done either in offline mode or in online mode. The basic assumption in the former mode is that the entire dataset has been available for the FS algorithm; and the FS algorithm takes multiple epochs to select optimal feature subset that gives good accuracy. In contrast, the FS algorithms in online mode take input data one instance at a time and accumulate knowledge by learning each one of them. In online mode each instance of the original dataset is considered as training and testing sample as well. The offline FS algorithms require long time periods, if the data to be processed is large such as Big data. Whereas online FS algorithms will take only one epoch to learn the entire data and can produce the results swiftly which is highly desirable in the case of Big data. This paper deals with the online FS problem and provides a novel Feature Selection algorithm which uses the Sparse Gradient method to build a sparse classifier. In this proposed method, an online classifier is built and maintained throughout the learning process and feature weights, which are limited to a particular boundary limit, are reduced in a step by step decrement process. This method creates sparsity in the classifier. Effectively, the built classifier is used to select optimal feature subset from the incoming data. As this method reduces the weights in the classifier in step by step manner, only those important features which have value higher than the boundary survive from this repeated decrement process. The resultant optimal feature subset is formed using these non-zero weighted features. Most significantly, this particular method can be used with any learning algorithm. To show its applicability with different learning algorithms, various online feature selection models have been built using Learning Vector Quantization, Radial Basis Function Networks and Adaptive Resonance Theory MAP. In all these models, the proposed Sparse Gradient method is used. The encouraging results shows the effectiveness of the proposed method with different learning algorithms in medium and large sized benchmark datasets.", "Keywords": "Data analysis; data preprocessing; big data analytics; feature selection; online learning", "DOI": "10.1142/S0218213022500385", "PubYear": 2022, "Volume": "31", "Issue": "8", "JournalId": 98756, "JournalTitle": "International Journal on Artificial Intelligence Tools", "ISSN": "0218-2130", "EISSN": "1793-6349", "Authors": [{"AuthorId": 1, "Name": "N Nasrin Banu", "Affiliation": "Department of Information Technology, Madras Institute of Technology, Chennai, Tamil Nadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Madras Institute of Technology, Chennai, Tamil Nadu, India"}], "References": []}, {"ArticleId": 96355183, "Title": "Block-based abstractions and expansive services to make advanced computing concepts accessible to novices", "Abstract": "Many block-based programming environments have proven to be effective at engaging novices in learning programming. However, most offer only restricted access to the outside world, limiting learners to commands and computing resources built in to the environment. Some allow learners to drag and drop files, connect to sensors and robots locally or issue HTTP requests. But in a world where most of the applications in our daily lives are distributed (i.e., their functionality depends on communicating with other computers or accessing resources and data on the internet), the limited support for beginners to envision and create such distributed programs is a lost opportunity. We argue that it is feasible to create environments with simple yet powerful abstractions that open up distributed computing and other widely-used but advanced computing concepts including networking, the Internet of Things, and cybersecurity to novices. The paper presents the architecture of and design decisions behind NetsBlox, a programming environment that supports these ideas. We show how NetsBlox expands opportunities for learning considerably: NetsBlox projects can access a wealth of online data and web services, and they can communicate with other projects. Moreover, the tool infrastructure enables young learners to collaborate with each other during program construction, whether they share their physical location or study remotely. Importantly, providing access to the wider world will also help counter widespread student perceptions that block-based environments are mere toys, and show that they are capable of creating compelling applications. In this way, NetsBlox offers an illuminating example of how tools can be designed to democratize access to powerful ideas in computing.", "Keywords": "Block based programming ; NetsBlox ; Distributed computing ; Message passing ; Remote procedure calls", "DOI": "10.1016/j.cola.2022.101156", "PubYear": 2022, "Volume": "73", "Issue": "", "JournalId": 59906, "JournalTitle": "Journal of Computer Languages", "ISSN": "2665-9182", "EISSN": "2590-1184", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Vanderbilt University, Nashville, TN, United States of America"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Vanderbilt University, Nashville, TN, United States of America"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Vanderbilt University, Nashville, TN, United States of America"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Vanderbilt University, Nashville, TN, United States of America"}, {"AuthorId": 5, "Name": "Shuchi Grover", "Affiliation": "Looking Glass Ventures, Palo Alto, CA, United States of America"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "North Carolina State University, Raleigh, NC, United States of America"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "North Carolina State University, Raleigh, NC, United States of America"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Vanderbilt University, Nashville, TN, United States of America;Corresponding author"}], "References": [{"Title": "History of Logo", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "HOPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "How secondary school girls perceive Computational Thinking practices through collaborative programming with the micro:bit", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "183", "Issue": "", "Page": "111107", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 96355199, "Title": "Applying hybrid machine learning algorithms to assess customer risk-adjusted revenue in the financial industry", "Abstract": "A Peer-to-Peer (P2P) service is a decentralized platform that directly connects individuals, buyers (lenders) and sellers (investors) without the intermediation of a third party. In the P2P lending market, customer cash flows are undeniably linked to their financial risk of default. Thus, forecasting customers’ Risk-Adjusted Revenue (RAR) value is one of the most critical issues in financial decision-making. With the emergence of big data, traditional forecasting methods cannot provide the high predictive power needed for such metrics. We propose a hybrid method by integrating the use of supervised and unsupervised Machine Learning (ML) algorithms to enhance the accuracy of predicting customer-adjusted risk metrics. Using a real P2P dataset from the Lending Club, containing over two million cases, we forecast customers’ risk-adjusted revenue by applying ML algorithms for the first time. These include individual methods such as gradient boosting and decision trees, and hybrid frameworks that group customers using a clustering algorithm (k-Means or Density-Based Spatial Clustering of Applications with Noise (DBSCAN)) prior to implementing the individual methods. We compare the efficiency (processing time and accuracy) of this hybrid approach with the performance of individual regressor-based models to predict RAR. Our results indicate high predictive power for many individual ML algorithms ( R 2 score over 90%). Further, in most cases, hybrid models outperform the individual ones in both predictive performance and processing time. Finally, the feature importance analysis in the best predictive frameworks helps identify the most influential factors in predicting customers’ RAR in the P2P lending market.", "Keywords": "P2P ; Customer value prediction ; Machine learning ; Hybrid frameworks ; Risk-adjusted revenue", "DOI": "10.1016/j.elerap.2022.101202", "PubYear": 2022, "Volume": "56", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering and Business Information Systems, University of Twente, 7500 AE Enschede, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Business and IT, Ontario Tech University, 2000 Simcoe St N., L1G 0C5, Oshawa, ON, Canada;Corresponding author"}], "References": [{"Title": "Customer switching behavior analysis in the telecommunication industry via push-pull-mooring framework: A machine learning approach", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "106476", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Hybrid machine learning for predicting strength of sustainable concrete", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "19", "Page": "14965", "JournalTitle": "Soft Computing"}, {"Title": "Credit risk evaluation model with textual features from loan descriptions for P2P lending", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "42", "Issue": "", "Page": "100989", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Assessing credit risk of commercial customers using hybrid machine learning algorithms", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "200", "Issue": "", "Page": "116889", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 96355270, "Title": "A fuzzy approach to support decision-making in the triage process for suspected COVID-19 patients in Brazil", "Abstract": "Triage is a fundamental process in hospitals and emergency care units, as it allows for the classification and prioritization of patient care based on the severity of their clinical conditions. In Brazil, the triage of suspected COVID-19 cases is performed using a specific protocol, which involves manual steps, requiring the completion of four different forms, by four health care professionals. Aiming to investigate the possibility of improving the triage processes in Brazil, this article proposes the use of computational techniques for decision-making based on fuzzy inference systems. We argue that fuzzy set theory is appropriate to the problem because it allows the use of natural language to express the patient’s symptoms, making it easier for health care professionals. After modelling the problem in a fuzzy system we applied a pilot test. The model includes symptoms that health professionals currently use to analyse COVID-19 cases. The results suggest that the model presents convergence with the sample data, highlighting its potential application in supporting triage for the classification of the severity of COVID-19 cases. Among the benefits of the proposed model, we emphasize contributions as the reduction of the time and number of professionals required for triage as well as the reduction of exposure of health care professionals and other patients suspected of carrying the virus. In this context, this research provides an opportunity to obtain social contributions regarding the services in public hospitals improvement.", "Keywords": "COVID-19;Classification of the severity;Fuzzy inference systems;Screening;Triage", "DOI": "10.1016/j.asoc.2022.109626", "PubYear": 2022, "Volume": "129", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Goiás, Faculty of Sciences and Technology, Mucuri Street s/n, Setor Conde dos Arcos, Aparecida de Goiânia, Goiás, Brazil."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal University of Goiás, Faculty of Sciences and Technology, Mucuri Street s/n, Setor Conde dos Arcos, Aparecida de Goiânia, Goiás, Brazil."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Federal University of Goiás, Faculty of Sciences and Technology, Mucuri Street s/n, Setor Conde dos Arcos, Aparecida de Goiânia, Goiás, Brazil."}], "References": [{"Title": "Emergency decision support modeling for COVID‐19 based on spherical fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "11", "Page": "1601", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Detecting COVID-19 patients based on fuzzy inference engine and Deep Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "106906", "JournalTitle": "Applied Soft Computing"}, {"Title": "Hospital admission and care of COVID‐19 patients problem based on spherical hesitant fuzzy decision support system", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "8", "Page": "4167", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 96355324, "Title": "A bi-objective model for territorial design", "Abstract": "<p>The clustering of spatial-geographic units, zones or areas has been used to solve problems related to Territorial Design. Clustering adapts to the definition of territorial design for a specific problem, which demands spatial data processing under clustering schemes with topological requirements for the zones. For small sized instances, once the geographical compactness is attended to as an objective function, this problem has been solved by exact methods with an acceptable response time. However, for larger instances and due to the combinatory nature of this problem, the computational complexity increases and the use of approximated methods becomes a necessity, in such a way that when geographical compactness was the only cost function a couple of approximated methods were incorporated giving satisfactory results. A particular case of this kind of problems that has had our attention in recent years is the classification by partitioning of AGEBs (Basic Geographical Units by its initials in Spanish). Some work has been made related to the formation of compact groups of AGEBs, but additional re-strictions were often not considered. A very interesting and highly demanded application problem is to extend the compact clustering to form groups with for some of its descriptive variables. This problem translates to a multi-objective approach that has to pursue two objectives to attain a balance between them. At this point, to reach a set of non-dominated and non-comparable solutions, a method has been included that allows obtaining the Pareto front through the <PERSON><PERSON> diagram, which implies proposing a mathematical programming model and the synthetic resulting between compactness and homogeneity.</p>", "Keywords": "", "DOI": "10.3233/HIS-220011", "PubYear": 2023, "Volume": "18", "Issue": "3-4", "JournalId": 36703, "JournalTitle": "International Journal of Hybrid Intelligent Systems", "ISSN": "1448-5869", "EISSN": "1875-8819", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Loran<PERSON>", "Affiliation": "Facultad de Ciencias de la Computación, Benemérita Universidad Autónoma de Puebla, Puebla, México"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Facultad de Ciencias Físico-Matemáticas, Benemérita Universidad Autónoma de Puebla, Puebla, México"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Facultad de Ciencias de la Computación, Benemérita Universidad Autónoma de Puebla, Puebla, México"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Facultad de Ciencias de la Computación, Benemérita Universidad Autónoma de Puebla, Puebla, México"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Facultad de Filosofia y Letras, Benemérita Universidad Autónoma de Puebla, Puebla, México"}], "References": [{"Title": "Handling multiple objectives using k‐means clustering guided multiobjective evolutionary algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "4", "Page": "e12890", "JournalTitle": "Expert Systems"}]}, {"ArticleId": 96355351, "Title": "Sparsing and Smoothing for the seq2seq Models", "Abstract": "Current neural language models are trained to minimize cross-entropy and use softmax to compute the locally normalized probabilities over the target. While this setup provides solid results in several natural language processing (NLP) tasks, one unsatisfying aspect is its dense output. This density is wasteful, making models hard to interpret and assigning probability mass to many implausible outputs. To overcome this problem, we propose T-softmax, a simple but effective method to draw considerably sparse probability out of neural language models than softmax. Our method avoids dense output by truncating the unreliable tail of the probability distribution to improve the model's performance. In addition, we generalize logits with temperature, a critical regularization technique, from the softmax to T-softmax. To show our approach as a drop-in replacement for softmax, we evaluate them on three NLP tasks: summary generation, question answer, and math word problem. Experimental results show that our proposed model significantly improves performance without sacrificing speed; notably, in all experiments, our method outperforms the softmax.", "Keywords": "Natural language processing (NLP);softmax;T-softmax;temperature smoothing", "DOI": "10.1109/TAI.2022.3207982", "PubYear": 2023, "Volume": "4", "Issue": "3", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Jinan University, Guangzhou, Guangdong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Jinan University, Guangzhou, Guangdong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Wen", "Affiliation": "College of Information Science and Technology, Jinan University, Guangzhou, Guangdong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Trustworthy Computing, School of Software Engineering, East China Normal University, China"}], "References": []}, {"ArticleId": 96355565, "Title": "Toward Decreasing the Driving Risk: Speech-Based Driver’s Anger Regulation in Smart Cockpit", "Abstract": "With the advent of intelligent connected vehicles, vehicles have gradually changed from traditional means of transportation to emotional third-party mobile space. However, driving anger has also become a severe problem in our daily life. As a medium of expression of information and emotion, speech plays a crucial role in the human-computer interaction of intelligent cockpits. Research shows that speech can regulate drivers’ anger and improve driving performance, which provides ways to control driving anger. This paper investigated the regulation quality of different speech types on driving anger. Thirty participants completed the driving simulator task as required to collect data. We analyzed the regulation effects of personalized speech and synthesized speech from the drivers’ subjective experience and driving behavior. This study found that personalized speech exhibited a more significant regulation effect than synthesized speech. Personalized speech can better control the expression of angry behavior and reduce the driving risk. The findings provide practical guidance for designing in-vehicle speech interactions for driving anger intervention.", "Keywords": "Human-machine interaction;intelligent cockpit;driving anger;emotion regulation;driving risk", "DOI": "10.1109/JRFID.2022.3208199", "PubYear": 2022, "Volume": "6", "Issue": "", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of MVE, Chongqing University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of MVE, Chongqing University, Chongqing, China"}, {"AuthorId": 3, "Name": "Wenbo Li", "Affiliation": "School of VM, Tsinghua University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "CMVR, KLST, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "CMVR, KLST, Chongqing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of MVE, Chongqing University, Chongqing, China"}], "References": [{"Title": "Drivers’ attitudes, preference, and acceptance of in-vehicle anger intervention systems and their relationships to demographic and personality characteristics", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "75", "Issue": "", "Page": "102899", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Effects of speech-based intervention with positive comments on reduction of driver's anger state and perceived workload, and improvement of driving performance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "103098", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 96355580, "Title": "Decision Boundary Visualization for Counterfactual Reasoning", "Abstract": "<p>Machine learning algorithms are widely applied to create powerful prediction models. With increasingly complex models, humans' ability to understand the decision function (that maps from a high-dimensional input space) is quickly exceeded. To explain a model's decisions, black-box methods have been proposed that provide either non-linear maps of the global topology of the decision boundary, or samples that allow approximating it locally. The former loses information about distances in input space, while the latter only provides statements about given samples, but lacks a focus on the underlying model for precise ‘What-If'-reasoning. In this paper, we integrate both approaches and propose an interactive exploration method using local linear maps of the decision space. We create the maps on high-dimensional hyperplanes—2D-slices of the high-dimensional parameter space—based on statistical and personal feature mutability and guided by feature importance. We complement the proposed workflow with established model inspection techniques to provide orientation and guidance. We demonstrate our approach on real-world datasets and illustrate that it allows identification of instance-based decision boundary structures and can answer multi-dimensional ‘What-If'-questions, thereby identifying counterfactual scenarios visually.</p>", "Keywords": "visual model evaluation;machine learning explanation;inverse multi-dimensional projection", "DOI": "10.1111/cgf.14650", "PubYear": 2023, "Volume": "42", "Issue": "1", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Technische Universität Kaiserslautern Kaiserslautern Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technische Universität Kaiserslautern Kaiserslautern Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Technische Universität Kaiserslautern Kaiserslautern Germany"}], "References": [{"Title": "Making deep neural networks right for the right scientific reasons by interacting with their explanations", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "8", "Page": "476", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 96355690, "Title": "Alternative multi-label imitation learning framework monitoring tool wear and bearing fault under different working conditions", "Abstract": "Bearings and tools are the important parts of the machine tool. And monitoring automatically the fault of bearings and the wear of tools under different working conditions is the necessary performance of the intelligent manufacturing system. In this paper, a multi-label imitation learning (MLIL) framework is proposed to monitor the tool wear and bearing fault under different working conditions. Specially, the multi-label samples with multiple sublabels are transformed into the imitation objects, and the MLIL develops a discriminator and a deep reinforcement learning (DRL) to imitate the feature from imitation objects. In detail, the DRL is implemented without setting the reward function to enhance the feature extraction ability of deep neural networks, and meanwhile the discriminator is used to discriminate the generations of DRL and imitation objects. As a result, the MLIL framework can not only deal with the correlation between multiple working conditions including different speeds and loads, but also distinguish the compound fault composed of coinstantaneous bearing fault and tool wear. Two cases demonstrate jointly the imitation ability of the MLIL framework on monitoring tool wear and bearing fault under different working conditions.", "Keywords": "Tool wear monitoring ; Bearing fault monitoring ; Multi-label learning ; Imitation learning ; Deep reinforcement learning", "DOI": "10.1016/j.aei.2022.101749", "PubYear": 2022, "Volume": "54", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan 430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan 430074, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan 430074, China"}], "References": [{"Title": "CREDO: Efficient and privacy-preserving multi-level medical pre-diagnosis based on ML-kNN", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "514", "Issue": "", "Page": "244", "JournalTitle": "Information Sciences"}, {"Title": "Stacked pruning sparse denoising autoencoder based intelligent fault diagnosis of rolling bearings", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106060", "JournalTitle": "Applied Soft Computing"}, {"Title": "Wasserstein distance based deep adversarial transfer learning for intelligent fault diagnosis with unlabeled or insufficient labeled data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "409", "Issue": "", "Page": "35", "JournalTitle": "Neurocomputing"}, {"Title": "Multi-label fault diagnosis of rolling bearing based on meta-learning", "Authors": "<PERSON><PERSON><PERSON><PERSON> Yu; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "10", "Page": "5393", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Intelligent fault recognition framework by using deep reinforcement learning with one dimension convolution and improved actor-critic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101315", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A reinforcement ensemble deep transfer learning network for rolling bearing fault diagnosis with Multi-source domains", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "", "Page": "101480", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 96355702, "Title": "AR4CAD", "Abstract": "<p>Infrastructure-mounted sensors that monitor roads can provide essential information for manual drivers and automated vehicles, e.g., positions of other vehicles occluded by buildings. However, human drivers and passengers have to trust and accept their use. This raises the question of how trust can be increased in such a scenario. One important factor for this is understanding the available information, including its quality and, for passengers of automated vehicles, the actions planned based on it. For this, augmented reality is a promising visualization technology because it can present the relevant information integrated into the physical world. Thus, this work develops a taxonomy of augmented reality visualizations for connected automated and manual driving. It is intended to classify and compare existing visualizations, identify novel visualizations, and provide a common language for discussions. The use case infrastructure-supported automated driving is explored by suggesting augmented reality visualizations to inform passengers of automated vehicles and are intended to increase trust. They present information available from infrastructure and onboard sensors as well as the driving decisions based on it. Finally, we evaluated the visualizations' influence on trust in an automated vehicle by conducting a driving simulator study (N=18). Results indicate a high dependency of trust on presenting driving decisions and information on road users but less on location-specific information.</p>", "Keywords": "augmented reality; automated vehicles; connected driving; interface design.", "DOI": "10.1145/3546712", "PubYear": 2022, "Volume": "6", "Issue": "MHCI", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Robert <PERSON> GmbH, Stuttgart, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ulm University, Ulm, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Dogru", "Affiliation": "Ulm University, Ulm, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Ulm, Ulm, Germany"}], "References": [{"Title": "Effects of Scene Detection, Scene Prediction, and Maneuver Planning Visualizations on Trust, Situation Awareness, and Cognitive Load in Highly Automated Vehicles", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 96355703, "Title": "Formative Evaluation of a Tablet Application to Support Goal-Oriented Care in Community-Dwelling Older Adults", "Abstract": "<p>Tools that can help older adults self-manage multiple health goals in collaboration with their care managers are rare to find. Informed by the Self-Determination Theory, Goal-Oriented Care paradigm and our prior findings, we used an iterative, user-centered process to design a tablet application to facilitate Goal-Oriented care in community-dwelling low income older adults with chronic (multi)morbidity. A formative in-situ evaluation was conducted in which 20 participants used the app to set and track health and wellness goals for 24 weeks, while participants' interactions with the app were logged. At the end of the study, semi-structured interviews were administered to understand how the app was used. Thirteen participants used the app throughout the study, while the remaining abandoned after short usage. Thematic analysis of the qualitative feedback shows that participants who used the app increased their commitment towards their goals and adopted healthy behaviors. Health issues, time constraints, lack of technical know-how and doubts about goal-setting paradigm were identified as primary reasons for low app usage and abandonment. Tools for Goal-Oriented care should support personalized goal exploration, build trust in the care paradigm, support collaboration, design for motivation, lower barriers to tracking and support re-engagement after abandonment. Carefully designed mobile apps have the potential to support Goal-Oriented care for older adults.</p>", "Keywords": "care managers; chronic (multi)morbidity; community-dwelling; formative evaluation; goal-oriented care; goal-setting; health; low income; mhealth; older adults; self-determination; tablet", "DOI": "10.1145/3546743", "PubYear": 2022, "Volume": "6", "Issue": "MHCI", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Louisiana at Lafayette, LAFAYETTE, LA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Notre Dame, Notre Dame, IN, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Notre Dame, Notre Dame, IN, USA"}], "References": []}, {"ArticleId": 96355937, "Title": "Editorial to theme section on modeling in low-code development platforms", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10270-022-01045-6", "PubYear": 2022, "Volume": "21", "Issue": "5", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of L’Aquila, L’Aquila, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma de Madrid, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "LS2N (UMR CNRS 6004), IMT Atlantique, Nantes, France"}], "References": []}, {"ArticleId": 96355939, "Title": "Learning from what we know: How to perform vulnerability prediction using noisy historical data", "Abstract": "Vulnerability prediction refers to the problem of identifying system components that are most likely to be vulnerable. Typically, this problem is tackled by training binary classifiers on historical data. Unfortunately, recent research has shown that such approaches underperform due to the following two reasons: a) the imbalanced nature of the problem, and b) the inherently noisy historical data, i.e., most vulnerabilities are discovered much later than they are introduced. This misleads classifiers as they learn to recognize actual vulnerable components as non-vulnerable. To tackle these issues, we propose TROVON , a technique that learns from known vulnerable components rather than from vulnerable and non-vulnerable components, as typically performed. We perform this by contrasting the known vulnerable, and their respective fixed components. This way, TROVON manages to learn from the things we know, i.e., vulnerabilities, hence reducing the effects of noisy and unbalanced data. We evaluate TROVON by comparing it with existing techniques on three security-critical open source systems, i.e., Linux Kernel, OpenSSL, and Wireshark, with historical vulnerabilities that have been reported in the National Vulnerability Database (NVD). Our evaluation demonstrates that the prediction capability of TROVON significantly outperforms existing vulnerability prediction techniques such as Software Metrics , Imports , Function Calls , Text Mining , Devign , LSTM , and LSTM-RF with an improvement of 40.84% in Matthews Correlation Coefficient (MCC) score under Clean Training Data Settings , and an improvement of 35.52% under Realistic Training Data Settings .", "Keywords": "Vulnerability prediction; Trovon; Training on vulnerabilities only; Encoder-decoder; Machine translation; tf-seq2seq", "DOI": "10.1007/s10664-022-10197-4", "PubYear": 2022, "Volume": "27", "Issue": "7", "JournalId": 3327, "JournalTitle": "Empirical Software Engineering", "ISSN": "1382-3256", "EISSN": "1573-7616", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Luxembourg, Esch-sur-Alzette, Luxembourg"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Luxembourg, Esch-sur-Alzette, Luxembourg"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Luxembourg, Esch-sur-Alzette, Luxembourg"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Luxembourg, Esch-sur-Alzette, Luxembourg"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Luxembourg, Esch-sur-Alzette, Luxembourg"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Luxembourg, Esch-sur-Alzette, Luxembourg"}], "References": [{"Title": "Better together: Comparing vulnerability prediction models", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "119", "Issue": "", "Page": "106204", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 96355958, "Title": "A Novel Shape Retrieval Method for 3D Mechanical Components Based on Object Projection, Pre-Trained Deep Learning Models and Autoencoder", "Abstract": "The reuse of existing design models offers great potential in saving resources and generating an efficient workflow. In order to fully benefit from these advantages, it is necessary to develop methods that are able to retrieve mechanical engineering geometry from a query input. This paper aims to address this problem by presenting a method that focuses on the needs of product development to retrieve similar components by comparing the geometrical similarity of existing parts. Therefore, a method is described, which first converts surface meshes into point clouds, rotates them, and then transforms the results into matrices. These are subsequently passed to a pre-trained Deep Learning network to extract the feature vector. A similarity between different geometries is calculated and evaluated based on this vector. The procedure employs a new type of part alignment, especially developed for mechanical engineering geometries. The method is presented in detail and several parameters affecting the accuracy of the retrieval are discussed. This is followed by a critical comparison with other shape retrieval approaches through a mechanical engineering benchmark data set.", "Keywords": "3D object retrieval ; Shape retrieval ; Projection method ; Part alignment ; Deep Learning ; Autoencoder", "DOI": "10.1016/j.cad.2022.103417", "PubYear": 2023, "Volume": "154", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Design, Friedrich-Alexander-Universität Erlangen-Nürnberg, Martensstraße 9, Erlangen, 91058, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Design, Friedrich-Alexander-Universität Erlangen-Nürnberg, Martensstraße 9, Erlangen, 91058, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Design, Friedrich-Alexander-Universität Erlangen-Nürnberg, Martensstraße 9, Erlangen, 91058, Germany"}], "References": [{"Title": "A survey on deep geometry learning: From a representation perspective", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "113", "JournalTitle": "Computational Visual Media"}, {"Title": "Deep-learning-based retrieval of piping component catalogs for plant 3D CAD model reconstruction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "103320", "JournalTitle": "Computers in Industry"}, {"Title": "A method of generating depth images for view-based shape retrieval of 3D CAD models from partial point clouds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "7", "Page": "10859", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 96355959, "Title": "Exploring vision transformer: classifying electron-microscopy pollen images with transformer", "Abstract": "<p>Pollen identification is a sub-discipline of Palynology, which has broad applications in several fields such as allergy control, paleoclimate reconstruction, criminal investigation, and petroleum exploration. Among these, pollen allergy is a common and frequent disease worldwide. Accurate and rapid identification of pollen species under the electron microscope help medical staff in pollen forecast and interrupt the natural course of pollen allergy. The current pollen species identification needs to rely on professional researchers to identify pollen particles in pictures manually, and this time-consuming and laborious way cannot meet the requirements of pollen forecasting. Recently, the self-attention based Transformer has attracted considerable attention in vision tasks, such as image classification. However, pure self-attention lacks local operations on pixels and requires large-scale dataset pretraining to achieve comparable performance to convolutional neural networks (CNN). In this study, we propose a new Vision Transformer pipeline for image classification. First, we design a FeatureMap-to-Token (F2T) module to perform token embedding on the input image. A global self-attention operation is performed on the basis of tokens with local information, and the hierarchical design of CNN is applied to the Vision Transformer, combining local and global strengths in multiscale spaces. Second, we use a distillation strategy to learn the feature representation in the output space of the teacher network to further learn the inductive bias in the CNN to improve the recognition accuracy. Experiments demonstrate that the proposed model achieves CNN-equivalent performance under the same conditions after being trained from scratch on the electron-microscopic pollen dataset. It also requires less model parameters and training time. Code for the model is available at https://github.com/dkbshuai/PyTorch-Our-S .</p>", "Keywords": "Image classification; Vision transformer; Self-attention; Knowledge distillation", "DOI": "10.1007/s00521-022-07789-y", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Inner Mongolia University of Technology, Huhhot, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Inner Mongolia University of Technology, Huhhot, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Inner Mongolia University of Technology, Huhhot, P. R. China"}, {"AuthorId": 4, "Name": "Shaodong Cui", "Affiliation": "School of Information Engineering, Inner Mongolia University of Technology, Huhhot, P. R. China"}], "References": [{"Title": "PointDet++: an object detection framework based on human local features with transformer encoder", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "14", "Page": "10097", "JournalTitle": "Neural Computing and Applications"}, {"Title": "STMG: <PERSON><PERSON> transformer for multi-label image recognition with graph convolution network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "12", "Page": "10051", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Knowledge distillation in plant disease recognition", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "17", "Page": "14287", "JournalTitle": "Neural Computing and Applications"}, {"Title": "MLTDNet: an efficient multi-level transformer network for single image deraining", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "16", "Page": "14013", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 96355962, "Title": "Investigating the effects of low-cost head-mounted display based virtual reality environments on learning and presence", "Abstract": "<p>In this study, it is aimed to reduce the cost of using virtual reality (VR) in education by using low-cost wireless VR devices. In this direction, the effect of low-cost VR environments developed for head-mounted displays (HMD) on learning and to what extent the presence is created in virtual environments within the scope of the “Fire and Emergency Situations” course was examined. In addition, student experiences in VR environments were investigated. Multimedia design principles were used while developing the VR environments. Adopted embedded experimental research design was used in the study. The study was carried out with 2 experimental groups and a comparison group, a total of 96 students, 32 in each. Experimental group 1 participated in both of teacher-centered direct instruction and VR implementations, experimental group 2 participated only in VR implementations, and comparison group only participated in teacher-centered direct instruction. A fire knowledge test was applied to all students before and after the implementations. The “Presence Questionnaire in Virtual Environments” and “Three-Dimensional Virtual Learning Environments Evaluation Scale” were applied to the experimental groups after the implementations. Moreover, students’ opinions about VR implementations were obtained through semi-structured interviews. After the implementations, the achievement of the experimental groups and comparison group increased statistically. VR implementations have created a high level of presence for all students in the experimental groups. The participants expressed positive opinions about implementations. VR implementations reduce the risk factors that can be encountered in authentic life and can be useful in the acquisition of kinesthetic skills.</p>", "Keywords": "Virtual reality; Head-mounted display; Learning in virtual reality environments; Presence; User experience", "DOI": "10.1007/s11042-022-13794-z", "PubYear": 2023, "Volume": "82", "Issue": "9", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Directorate of Flight Operations, Turkish Airlines, İstanbul, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Education and Instructional Technology, Hacettepe University, Ankara, Turkey"}], "References": [{"Title": "A systematic review of immersive virtual reality applications for higher education: Design elements, lessons learned, and research agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "103778", "JournalTitle": "Computers & Education"}, {"Title": "Measuring Visual Fatigue and Cognitive Load via Eye Tracking while Learning with Virtual Reality Head-Mounted Displays: A Review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "9", "Page": "801", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 96356081, "Title": "The implementation of a digital group intervention for individuals with subthreshold borderline personality disorder", "Abstract": "Context: Systems Training for Emotional Predictability and Problem Solving for Emotional Intensity (STEPPS-EI), a 13-week skills-based group intervention for individuals with subthreshold borderline personality disorder (BPD) has been deemed feasible and clinically effective in primary care [1] [2]. To modernize the service, STEPPS-EI lesson content has recently built onto an eHealth platform (Minddistrict). Due to Covid-19 restrictions, group sessions were additionally delivered remotely via Zoom. This project evaluates the implementation of this digitally blended version of STEPPS-EI within two Sussex Partnership NHS Foundation Trust (SPFT) primary care services. Methods: Service users and pracitioners who participated in the first two groups from March to July 2021 were invited to take part in a feasibility evaluation investigating recruitment, retention, and attendance rates, in addition to self-reported symptoms (BSL-23, QuEST), quality of life (ReQoL), system usability, and qualitative and quantitative measures designed to shed light on the experience and opinions of service users and practitioners during the intervention. Service users participating in following groups (from July to December 2021) were invited to share their symptom and quality of life outcome data only. Results: 14 service users and 5 practitioners agreed to take part in the primary evaluation. Results suggested that 86% of these service users attended at least 75% of the group sessions, and that service users completed on average 70% of the online material. Usability ratings revealed good gradings for Zoom from all participants, yet lower gradings for Minddistrict. Further analyses revealed a generally positive attitude towards digital STEPPS-EI from all parties and practical suggestions on how to improve the intervention. 11 service users from following groups agreed to share their data. Bayesian analyses were conducted for the data of service users who provided ratings at both timepoints. Evidence was found for a decrease in BSL-23 scores and an increase in ReQoL ratings from baseline to post-intervention. Incomplete self-report data-sets limits conclusions. Conclusions: It was found that the implementation of STEPPS-EI delivered in a blended digital format was feasible. The online delivery might increase service users&#x27; engagement with the material and group sessions. Yet more training and support on the use of Minddistrict may be required to increase usability. Implications: It may be possible to effectively implement digital interventions for individuals with subthreshold BPD. However, more research on the effect of these on symptom outcomes should follow.", "Keywords": "digital intervention ; eHealth ; tellehealth ; emotional instabillity ; Borderline Personality", "DOI": "10.1016/j.procs.2022.09.082", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-<PERSON><PERSON>", "Affiliation": "Sussex Partnership NHS Foundation Trust (SPFT), Worthing BN13 3EP, England;Univeristy of Sussex, Brighton BN1 9RH, England"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Sussex Partnership NHS Foundation Trust (SPFT), Worthing BN13 3EP, England"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sussex Partnership NHS Foundation Trust (SPFT), Worthing BN13 3EP, England;Univeristy of Sussex, Brighton BN1 9RH, England"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Univeristy of Sussex, Brighton BN1 9RH, England"}], "References": []}, {"ArticleId": 96356097, "Title": "An application of tournament differential evolution algorithm in production inventory model with green level and expiry time dependent demand", "Abstract": "<p>In the present situation, environment is rapidly polluted by the manufacturing of non-eco-friendly products and carbon emission from production industries. So, to control this pollution as well as carbon emission, everyone should be aware to use eco-friendly products and reduce carbon emission. Motivating on this topic, a model on green manufacturing system has been formulated where the produced items are eco-friendly and have a fixed lifetime. Also, in this model, the green level of the produced items enhances the demand of the customers. The objective of this work is to determine the green level of the product and business period by maximizing the average profit of the system. To solve the corresponding maximization problems, a hybrid tournament differential evolution (TDE) algorithm is applied to obtain the best-found solutions along with the average profit of the system. To check the validity of the proposed model, a numerical example is considered and solved by the different variants of the said algorithm. Also, the simulated results obtained from the different variants of TDE algorithm are compared with the simulated results obtained from some of the existing algorithms reported in the literature. Then to test the performance and efficiency of the said hybrid algorithm, statistical comparisons, statistical tests are performed. Finally, sensitivity analyses are carried out in order to examine the effects of changes of parameters involved in the model on the average profit, green level of the product, business and production periods.</p>", "Keywords": "Tournamenting; Differential evolution; Non-parametric test; Variable demand; Carbon emission", "DOI": "10.1007/s10462-022-10268-4", "PubYear": 2023, "Volume": "56", "Issue": "5", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, The University of Burdwan, Burdwan, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, <PERSON><PERSON><PERSON>zad University of Technology, WB, Nadia, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, The University of Burdwan, Burdwan, India; Department of Mathematics, C. V. <PERSON>n Global University, Bhubaneswar, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, The University of Burdwan, Burdwan, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, The University of Burdwan, Burdwan, India"}], "References": [{"Title": "An EOQ model of non-instantaneous deteriorating items with price, time-dependent demand and backlogging", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "2", "Page": "135", "JournalTitle": "Journal of Control and Decision"}, {"Title": "Optimizing an integrated inventory-routing system for multi-item joint replenishment and coordinated outbound delivery using differential evolution algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105863", "JournalTitle": "Applied Soft Computing"}, {"Title": "Advanced backtracking search optimization algorithm for a new joint replenishment problem under trade credit with grouping constraint", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105953", "JournalTitle": "Applied Soft Computing"}, {"Title": "A new hybrid algorithm to solve bound-constrained nonlinear optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12427", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An application of interval differential equation on a production inventory model with interval‐valued demand via center‐radius optimization technique and particle swarm optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "8", "Page": "1280", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Modified Social Group Optimization—a meta-heuristic algorithm to solve short-term hydrothermal scheduling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106524", "JournalTitle": "Applied Soft Computing"}, {"Title": "Backtracking search algorithm with specular reflection learning for global optimization", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106546", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An inventory model for non-instantaneous deteriorating items with preservation technology and multiple credit periods-based trade credit financing via particle swarm optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "7", "Page": "5365", "JournalTitle": "Soft Computing"}, {"Title": "Optimization of a deteriorated two-warehouse inventory problem with all-unit discount and shortages via tournament differential evolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107388", "JournalTitle": "Applied Soft Computing"}, {"Title": "An efficient differential evolution algorithm based on orthogonal learning and elites local search mechanisms for numerical optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107636", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Investigation of green production inventory problem with selling price and green level sensitive interval-valued demand via different metaheuristic algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "19", "Page": "10409", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 96356099, "Title": "SSGNet: semi-supervised multi-path grid network for diagnosing melanoma", "Abstract": "<p>Early diagnosis of melanoma can help patients receive timely treatment, thereby increasing the cure rate of the patient. Dermoscopy is an important way for checking melanoma, and in order to precisely diagnose melanoma in dermoscopy images, based on the advantages of multi-scale method and multi-task collaboration in deep learning, a novel semi-supervised multi-path grid network (SSGNet) was constructed. The SSGNet includes a colorization network and a classification network, and these two networks share the encoder part. The classification network can use the colorized network to regularize its own encoder, thereby improving its ability to extract features. In the construction of the SSGNet, firstly, a layer correction block was designed to extract sufficient image features, which used spatial self-attention mechanism and belongs to a residual structure. Secondly, in order to make full use of the multi-scale information of the image, the features of different inputs under the same scale were densely connected to form a novel grid structure. Thirdly, the classification network and the colorization network share the encoder to perform their own tasks. The form of multi-scale input greatly reduces the effect of the lesion’s size in the image on the result, and the grid structure can enable the SSGNet model to make maximum use of features at different scales. We evaluated our proposed SSGNet on the ISIC 2020 dataset, and experiments showed that the SSGNet achieved better results than existing deep learning models.</p>", "Keywords": "Diagnosis of melanoma; Semi-supervised; Multi-path grid network; Deep learning; Layer correction block", "DOI": "10.1007/s10044-022-01100-4", "PubYear": 2023, "Volume": "26", "Issue": "1", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "Baoping Dong", "Affiliation": "Shouguang People’s Hospital, Weifang, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical, Electrical and Information Engineering, Shandong University, Weihai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shouguang People’s Hospital, Weifang, China"}], "References": [{"Title": "Semi-supervised local feature selection for data classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "9", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 96356319, "Title": "A bibliometric analysis of COVID-19's impact on the sharing economy", "Abstract": "A rapidly growing body of scholarly research discusses the impact of the COVID-19 pandemic on the sharing economy (SE). This study aims to employ bibliometric analysis to examine the research themes, scholarly communities, evolution paths and research hotspots of the developing domain of COVID-19’s SE research. Adopting an integrated bibliometric approach, this study analyses articles published on Web of Science from 2008 to 2019 and from 2020 to 2021. The outcomes of this study reveal that four new research themes including the COVID-19’s impact were updated for the post-pandemic SE research. The scholarly communities’ analysis identifies research changes of documents and published journals communities of COVID-19. The results also dig into the evolution path of the topic and instigate further research hotspots toward SE activities. Thus, we contribute to point out the research directions for the opportunities and challenges of SE after the COVID-19 pandemic. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "bibliometric analysis; COVID-19 pandemic; evolution path; intellectual structures; sharing economy", "DOI": "10.1504/IJWET.2022.125653", "PubYear": 2022, "Volume": "17", "Issue": "2", "JournalId": 26853, "JournalTitle": "International Journal of Web Engineering and Technology", "ISSN": "1476-1289", "EISSN": "1741-9212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "China Institute of FTZ Supply Chain, Shanghai Maritime University, Shanghai, 201306, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, Shanghai Maritime University, Shanghai, 201306, China"}], "References": []}, {"ArticleId": 96356323, "Title": "The Influence of Transparency and Control on the Willingness of Data Sharing in Adaptive Mobile Apps", "Abstract": "<p>Today, adaptive mobile applications use mobile sensing and user tracking, allowing for adaptation to the users' context and needs. This raises several privacy concerns. Privacy dashboards provide transparency and sharing control; however, their impact on the users' behavior is unclear. To shed light on the effects of (a) transparency and (b) control features, we developed a mobile sensing privacy dashboard and evaluated it in the wild (N=227). We found that the pure presentation of raw logging data is rather deterring, and users tend to use the app less, but offering the user control over the data collection can compensate for that. Users used the control features rarely and, as such, did not affect the data collocation. Our work informs the design of future privacy-enhancing interfaces in applications relying on passively collected mobile sensing data. Moreover, our results encourage the adoption of privacy dashboards in the applications and relieve developers from concerns about the negative influences of transparency information on the quality of collected data.</p>", "Keywords": "data collection; mobile sensing; privacy; reflection", "DOI": "10.1145/3546724", "PubYear": 2022, "Volume": "6", "Issue": "MHCI", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LMU Munich, Munich, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "LMU Munich, Munich, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "LMU Munich, Munich, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "LMU Munich, Munich, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "LMU Munich, Munich, Germany"}], "References": [{"Title": "The impact of transparency on mobile privacy decision making", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "607", "JournalTitle": "Electronic Markets"}]}, {"ArticleId": 96356514, "Title": "Building a Lucy hybrid model for grocery sales forecasting based on time series", "Abstract": "<p>Nowadays, time series data are applied in many fields, such as economics, medicine, biology, science, society, nature, environment, or typically in weather forecasting. Time series is a tool that includes methodological formulas and models to help us analyze time series data, extract potentially valuable information, capture historical fluctuations, present and support forecasts of the value of the research object in future. There are many models and methods of time series analysis that have been researched and improved these days for trend analysis and forecasts. Techniques related to time series data processing include linear regression with time series with two features unique to time series lags and time steps, the trend for model long-term changes with moving averages and time dummy, seasonality to create indicators, Fourier features to capture periodic change, and time series as features to predict the future from the pass with a lag embedding. In this article, we build a new hybrid model called Lucy Hybrid that provides full steps in the machine learning process including data pre-processing, training model, evaluation model with Mean Square Error (MSE), Root-Mean-Square Error (RMSE) and Mean Absolute Error (MAE) to compare and get the best model quality. The model also provides functions like storage and loading model to support researchers to reuse and save time on training model. In the Lucy hybrid, we also support the trend and forecast function for time series data. We experiment with a large dataset of more than 3,000,000 records from a large Ecuadorian-based grocery retailer, and we used Linear Regression, Elastic Net, Lasso, Ridge and Extra Trees Regressor, Random Forest Regressor, K -Neighbors Regressor, MLP Regressor, XGB Regressor to experiment and create 20 Lucy hybrid sample models and publish a full source code for researchers to use to expand the model.</p>", "Keywords": "Lucy hybrid; Hybrid model; Forecast; Grocery sales; Machine learning; Time series; Trend", "DOI": "10.1007/s11227-022-04824-6", "PubYear": 2023, "Volume": "79", "Issue": "4", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Systems, University of Economics and Law, Vietnam National University Ho Chi Minh City, Ho Chi Minh, Vietnam; Department of Data Informatics, (National) Korea Maritime and Ocean University, Busan, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Data Science, (National) Korea Maritime and Ocean University, Busan, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Data Science, (National) Korea Maritime and Ocean University, Busan, Republic of Korea"}], "References": [{"Title": "A Comparative Study and Analysis of Time Series Forecasting Techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Strategies for time series forecasting with generalized regression neural networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "491", "Issue": "", "Page": "509", "JournalTitle": "Neurocomputing"}, {"Title": "Time-series forecasting of seasonal items sales using machine learning – A comparative analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "1", "Page": "100058", "JournalTitle": "International Journal of Information Management Data Insights"}]}, {"ArticleId": 96356581, "Title": "A Brief History and Foundations for Modern Artificial Intelligence", "Abstract": "<p>In this paper, we present a brief history of artificial intelligence (AI) research for more than 70 years since its inception. We begin with an analysis of the mathematical, engineering, psychological and philosophical foundations enabling modern AI. We then outline and give examples of the three primary research thrusts the discipline has taken over its existence. We conclude offering both important criticisms as well as describing the future promise for current AI research and practice.</p>", "Keywords": "", "DOI": "10.1142/S1793351X22500076", "PubYear": 2023, "Volume": "17", "Issue": "1", "JournalId": 16083, "JournalTitle": "International Journal of Semantic Computing", "ISSN": "1793-351X", "EISSN": "1793-7108", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science Department, University of New Mexico, Albuquerque, NM 83835, USA"}], "References": []}, {"ArticleId": 96356602, "Title": "A culturally adapted internet-delivered mindfulness intervention for Indonesian university students’ distress: Overview of development and preliminary study", "Abstract": "Psychological distress is a common mental health problem among university students, including students from Low and Middle-income countries (LMICs), such as Indonesia. Mindfulness interventions that can reduce psychological distress have been growing in popularity and are being increasingly delivered through the Internet. The present study examined the development process and preliminary outcomes of an internet-delivered mindfulness program for distressed Indonesian university students. To develop a more culturally relevant program for Indonesian students, we conducted qualitative interviews and focus group discussions among students. We then conducted an open pilot trial with a sample of 40 university students with elevated psychological distress. Participants took part in the 4-lesson counsellor-guided online mindfulness intervention over 4 weeks and completed measures of distress (DASS-21) at baseline and post-treatment. During the pilot trial, the rate of program completion was 70%, and we found large, significant reductions in distress from baseline to post-treatment (<PERSON><PERSON>’ g&#x27;s = 1.05-1.68). These findings show that it is feasible to deliver an online mindfulness intervention for distressed Indonesian students. A randomized controlled trial is needed to explore the efficacy of this program.", "Keywords": "cultural ; mindfulness ; distress ; students ; Indonesia", "DOI": "10.1016/j.procs.2022.09.099", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Psychology, University of New South Wales, Sydney, 2052, Australia;Faculty of Psychology, YARSI University, Jakarta, 10510, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Psychology, Universitas Jambi, Indonesia"}, {"AuthorId": 3, "Name": "Melok Roro <PERSON>", "Affiliation": "Faculty of Psychology, YARSI University, Jakarta, 10510, Indonesia"}, {"AuthorId": 4, "Name": "Fairuz Callista", "Affiliation": "Faculty of Psychology, YARSI University, Jakarta, 10510, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Psychology, YARSI University, Jakarta, 10510, Indonesia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Psychology, Universitas Islam Indonesia, Yogyakarta, Indonesia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Psychology, Universitas Indonesia, Depok, Indonesia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Tiga Generasi Psychological Clinic, Jakarta, Indonesia"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "School of Psychology, University of New South Wales, Sydney, 2052, Australia"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Clinical Research Unit for Anxiety and Depression, St Vincent's Hospital, Sydney, 2010, Australia;School of Psychiatry, Faculty of Medicine, University of New South Wales, Sydney 2052, Australia"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "School of Psychology, University of New South Wales, Sydney, 2052, Australia;Black Dog Institute, Sydney, 2052, Australia"}], "References": [{"Title": "Transdiagnostic internet-delivered CBT and mindfulness-based treatment for depression and anxiety: A randomised controlled trial", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "", "Page": "100310", "JournalTitle": "Internet Interventions"}]}, {"ArticleId": 96356604, "Title": "Use of a custom testing center locator tool to improve STI and HIV testing rates in adolescent men who have sex with men as part of an online sexual health program", "Abstract": "Adolescent men who have sex with men (AMSM) experience high rates of HIV diagnoses and low utilization of HIV testing and prevention services. Meta-analyses and literature reviews have reported significant effects of online programs at reducing HIV and STI risk and improving use of protective behaviors. SMART is a stepped care package of eHealth interventions that comprehensively address the sexual health and HIV prevention needs of diverse 13-18 year old AMSM nationwide. As part of the online curriculum, educational tools were created to promote specific learning objectives, including the importance of STI and HIV testing. This study describes the use of an HIV and STI testing center locator custom designed for the SMART intervention to promote STI and HIV testing in AMSM. Data were collected between April 2018 and July 2020 as part of the SMART trial assessing the impact of the first intervention of the SMART program, titled SMART Sex Ed, on 13-18 year old AMSM. Measures included AMSM interaction data with the locator tool, history of HIV and STI testing, and confidence to get tested at baseline and 3 months post intervention. Upon entering the SMART program, most participants (69.3%) had never received an HIV or STI test in their lifetime. From those who were enrolled in SMART Sex Ed (n=1075), 82.6% used the custom developed HIV and STI testing center locator tool and those who used the tool were significantly more confident to receive an HIV test and STI test (p &lt; 0.01). Qualitative feedback from SMART participants described the tool as interactive, useful, and easy to use. Preliminary data analysis suggests that our custom developed HIV and STI testing center locator is an acceptable and useful tool with potential for implementation outside the SMART Program. Future analysis should examine if the testing center locator is an acceptable and effective tool outside of the SMART intervention package. Given these results, providing AMSMs easy to use and acceptable online tools with comprehensive, culturally relevant didactic content can significantly improve AMSM&#x27;s utilization of HIV testing and prevention services.", "Keywords": "eHealth tools ; HIV prevention ; HIV ; STI testing ; sexual health programs ; smartphone app", "DOI": "10.1016/j.procs.2022.09.088", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Sexual and Gender Minority Health and Wellbeing, Northwestern University, Chicago, IL, USA;Department of Medical Social Sciences, Feinberg School of Medicine, Northwestern University, Chicago, IL, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Sexual and Gender Minority Health and Wellbeing, Northwestern University, Chicago, IL, USA;Department of Medical Social Sciences, Feinberg School of Medicine, Northwestern University, Chicago, IL, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for Sexual and Gender Minority Health and Wellbeing, Northwestern University, Chicago, IL, USA;Department of Medical Social Sciences, Feinberg School of Medicine, Northwestern University, Chicago, IL, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute for Sexual and Gender Minority Health and Wellbeing, Northwestern University, Chicago, IL, USA;Department of Medical Social Sciences, Feinberg School of Medicine, Northwestern University, Chicago, IL, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute for Sexual and Gender Minority Health and Wellbeing, Northwestern University, Chicago, IL, USA;Department of Medical Social Sciences, Feinberg School of Medicine, Northwestern University, Chicago, IL, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute for Sexual and Gender Minority Health and Wellbeing, Northwestern University, Chicago, IL, USA;Department of Medical Social Sciences, Feinberg School of Medicine, Northwestern University, Chicago, IL, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Institute for Sexual and Gender Minority Health and Wellbeing, Northwestern University, Chicago, IL, USA;Department of Medical Social Sciences, Feinberg School of Medicine, Northwestern University, Chicago, IL, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Institute for Sexual and Gender Minority Health and Wellbeing, Northwestern University, Chicago, IL, USA;Department of Medical Social Sciences, Feinberg School of Medicine, Northwestern University, Chicago, IL, USA"}], "References": []}, {"ArticleId": 96356624, "Title": "Ensemble Learning of Lightweight Deep Convolutional Neural Networks for Crop Disease Image Detection", "Abstract": "<p>The application of convolutional neural networks (CNNs) to plant disease recognition is widely considered to enhance the effectiveness of such networks significantly. However, these models are nonlinear and have a high bias. To address the high bias of the single CNN model, the authors proposed an ensemble method of three lightweight CNNs models (MobileNetv2, NasNetMobile and a simple CNN model from scratch) based on a stacking generalization approach. This method has two-stage training, first, we fine-tuned and trained the base models (level-0) to make predictions, then we passed these predictions to XGBoost (level-1 or meta-learner) for training and making the final prediction. Furthermore, a search grid algorithm was used for the hyperparameter tuning of the XGBoost. The proposed method is compared to the majority voting approach and all base learner models (MobileNetv2, NasNetMobile and simple CNN model from scratch). The proposed ensemble method significantly improved the performance of plant disease classification. Experiments show that the ensemble approach achieves higher prediction accuracy (98% for majority voting and 99% for staking method) than a single CNN learner. Furthermore, the proposed ensemble method has a lightweight size (e.g., 10[Formula: see text] smaller than VGG16), allowing farmers to deploy it on devices with limited resources such as cell phones, internet of things (IoT) devices, unmanned aerial vehicles (UAVs) and so on.</p>", "Keywords": "Deep learning; plant disease; ensemble learning; convolutional neural network; transfer learning", "DOI": "10.1142/S021812662350086X", "PubYear": 2023, "Volume": "32", "Issue": "5", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "Mehdhar S. A. <PERSON>", "Affiliation": "College of Computer Science and Technology, Chongqing University of Posts and Telecommunications, 2 Chongwen Road, Chongqing 400065, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Chongqing University of Posts and Telecommunications, 2 Chongwen Road, Chongqing 400065, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "EIAS Data Science Lab, College of Computer and Information Sciences, Prince Sultan University, Riyadh 11586, Saudi Arabia;Mathematics and Computer Science Department, Faculty of Science, Menoufia University, University, Shebin El-Koom 32511, Egypt"}], "References": []}, {"ArticleId": 96356628, "Title": "The Construction of Conceptual Framework of Enterprise Internal Control Evaluation Report", "Abstract": "<p>In practice, although there is no doubt about the role of enterprise internal control, how to evaluate the effect of enterprise internal control and the auditability of internal control has always been a difficult problem that plagues the theoretical and practical circles. The problems existing in the construction and development of enterprise internal control, such as the information content problem in the enterprise internal control evaluation report, are largely due to the lack of the conceptual framework of enterprise internal control to guide the internal control theory of enterprises. Therefore, the establishment of a set of conceptual framework for the evaluation report of internal control of enterprises and a full understanding of its importance play an important role in solving the practical problems faced in the practice of internal control of enterprises and promoting the construction and development of internal control of enterprises. The construction of the conceptual framework of the internal control evaluation report of an enterprise should adopt the viewpoint of the essential starting point theory, learn from the experience in the construction of relevant conceptual frameworks, start from the concept of the conceptual framework, clarify the main users of the report and their common needs, and combine the functions of the internal control evaluation report. Then, determine the objectives of the internal control evaluation report. This paper proposes that the introduction of information quality characteristics, elements of internal control evaluation reports, and cost-benefit measurement concepts can achieve the preset goals of enterprise internal control evaluation reports.</p>", "Keywords": "", "DOI": "10.1155/2022/2753001", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Accounting, Guangdong University of Finance and Economics, Guangzhou 510320, China"}, {"AuthorId": 2, "Name": "Zongkeng Li", "Affiliation": "Department of Management, Sumy National Agrarian University, Ukraine;Hezhou University, Hezhou 542899, China"}, {"AuthorId": 3, "Name": "Changliang Pan", "Affiliation": "Hezhou University, Hezhou 542899, China"}], "References": []}, {"ArticleId": ********, "Title": "Application Effect Analysis of the Mie Scattering Theory Based on Big Data Analysis Technology in the Optical Scattering Direction", "Abstract": "<p>In order to study the characteristics of wake, an application method of the Mie scattering theory based on big data analysis technology in the optical scattering direction is proposed in this paper. Firstly, based on the scattering theory, the optical scattering model of a single bubble is simulated on the computer. It is concluded that the light scattering properties of a single bubble are closely related to the bubble diameter and relative refractive index. Based on the single bubble scattering model, the properties of bubble group scattering are further discussed. Under the condition of irrelevant scattering, the scattering of the bubble group satisfies the linear superposition of single bubble scattering. Under the assumed mathematical model of bubble group scattering, the scattered light intensity of wake can be measured experimentally, and then, the velocity, diameter, and density distribution of the bubble group can be calculated by using the mathematical inversion algorithm. The experimental results show that the diameter of the main bubbles in the bubble group is about 240, accounting for about 50% of the whole bubble group in number, the bubbles with a diameter of about 140 account for 24% of the whole bubble group, and the bubbles with a diameter of about 350 account for 24% of the whole bubble group. Conclusion. It is feasible to detect the wake by the backscattered light of the wake.</p>", "Keywords": "", "DOI": "10.1155/2022/6158067", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 8496, "JournalTitle": "Advances in Multimedia", "ISSN": "1687-5680", "EISSN": "1687-5699", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Jian Qiao University, College of Education, Shanghai 201306, China"}], "References": [{"Title": "Image processing operations identification via convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "3", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "A new algorithm to determine the creation or depletion term of parabolic equations from boundary measurements", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "80", "Issue": "10", "Page": "2135", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 96356656, "Title": "An improved extreme learning machine with self-recurrent hidden layer", "Abstract": "Extreme learning machine (ELM) is widely used in complex industrial problems, especially the online-sequential extreme learning machine (OS-ELM) plays a good role in industrial online modeling. However, OS-ELM requires batch samples to be pre-trained to obtain initial weights, which may reduce the timeliness of samples. This paper proposes a novel model for the online process regression prediction, which is called the Recurrent Extreme Learning Machine (Recurrent-ELM). The nodes between the hidden layers are connected in Recurrent-ELM, thus the input of the hidden layer receives both the information from the current input layer and the previously hidden layer. Moreover, the weights and biases of the proposed model are generated by analysis rather than random. Six regression applications are used to verify the designed Recurrent-ELM, compared with extreme learning machine (ELM), fast learning network (FLN), online sequential extreme learning machine (OS-ELM), and an ensemble of online sequential extreme learning machine (EOS-ELM), the experimental results show that the Recurrent-ELM has better generalization and stability in several samples. In addition, to further test the performance of Recurrent-ELM, we employ it in the combustion modeling of a 330 MW coal-fired boiler compared with FLN, SVR and OS-ELM. The results show that Recurrent-ELM has better accuracy and generalization ability, and the theoretical model has some potential application value in practical application.", "Keywords": "Online Sequential Extreme Learning Machine (OS-ELM) ; Time-series ; Coal-fired boiler ; Combustion regression", "DOI": "10.1016/j.aei.2022.101736", "PubYear": 2022, "Volume": "54", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao 066004, Hebei Province, China;Key Lab of Industrial Computer Control Engineering of Hebei Province,Yanshan University, Qinhuangdao 066004, Hebei Province, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao 066004, Hebei Province, China;Key Lab of Industrial Computer Control Engineering of Hebei Province,Yanshan University, Qinhuangdao 066004, Hebei Province, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao 066004, Hebei Province, China;Key Lab of Industrial Computer Control Engineering of Hebei Province,Yanshan University, Qinhuangdao 066004, Hebei Province, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao 066004, Hebei Province, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao 066004, Hebei Province, China;Key Lab of Industrial Computer Control Engineering of Hebei Province,Yanshan University, Qinhuangdao 066004, Hebei Province, China"}], "References": [{"Title": "Benchmark value determination of energy efficiency indexes for coal-fired power units based on data mining methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101029", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Adaptive LSSVM based iterative prediction method for NOx concentration prediction in coal-fired power plant considering system delay", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106070", "JournalTitle": "Applied Soft Computing"}, {"Title": "Online sequential extreme learning machine based adaptive control for wastewater treatment plant", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "408", "Issue": "", "Page": "169", "JournalTitle": "Neurocomputing"}, {"Title": "A newly-designed fault diagnostic method for transformers via improved empirical wavelet transform and kernel extreme learning machine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101320", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "INTERVALES: INTERactive Virtual and Augmented framework for industriaL Environment and Scenarios", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "", "Page": "101425", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Recurrent neural network model for high-speed train vibration prediction from time series", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "16", "Page": "13305", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A robust double-parallel extreme learning machine based on an improved M-estimation algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101606", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 96356793, "Title": "PreMSat: Preventing Magnetic Saturation Attack on Hall Sensors", "Abstract": "<p>Spoofing a passive Hall sensor with fake magnetic fields can inject false data into the downstream of connected systems. Several works have tried to provide a defense against the intentional spoofing to different sensors over the last six years. However, they either only work on active sensors or against externally injected unwanted weak signals (e.g., EMIs, acoustics, ultrasound, etc.), which can only spoof sensor output in its linear region. However, they do not work against a strong magnetic spoofing attack that can drive the passive Hall sensor output in its saturation region. We name this as the saturation attack. In the saturation region, the output gets flattened, and no information can be retrieved, resulting in a denial-of-service attack on the sensor.Our work begins to fill this gap by providing a defense named PreMSat against the saturation attack on passive Hall sensors. The core idea behind PreMSat is that it cangenerate an internal magnetic field having the same strength but in opposite polarity to external magnetic fields injected by an attacker. Therefore, the generated internal magnetic field by PreMSat can nullify the injected external field while preventing: (i) intentional spoofing in the sensor’s linear region, and (ii) saturation attack in the saturation region. PreMSat integrates a low-resistance magnetic path to collect the injected external magnetic fields and utilizes a finely tuned PID controller to nullify the external fields in real-time. PreMSat can prevent the magnetic saturation attack having a strength up to ∼4200 A-t within a frequency range of 0 Hz–30 kHz with low cost (∼$14), whereas the existing works cannot prevent saturation attacks with any strength. Moreover, it works against saturation attacks originating from any type, such as constant, sinusoidal, and pulsating magnetic fields. We did over 300 experiments on ten different industry-used Hall sensors from four different manufacturers to prove the efficacy of PreMSat and found that the correlation coefficient between the signals before the attack and after the attack is greater than 0.94 in every test case. Moreover, we create a prototype of PreMSat and evaluate its performance in a practical system — a grid-tied solar inverter. We find that PreMSat can satisfactorily prevent the saturation attack on passive Hall sensors in real-time.</p>", "Keywords": "Hall sensors;PID controller;Saturation region;Real-time defense", "DOI": "10.46586/tches.v2022.i4.438-462", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of California, Irvine, Irvine, U.S.A"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of California, Irvine, Irvine, U.S.A"}], "References": []}, {"ArticleId": 96356865, "Title": "SEO centrado en el usuario: un cambio de paradigma orientado a satisfacer mejor las necesidades de las personas en el buscador de Google", "Abstract": "<p>Los buscadores son el principal punto de acceso a los contenidos de los sitios web. El SEO es la práctica encaminada al aumento de la cantidad y calidad de tráfico hacia un sitio web a través de los resultados de búsqueda orgánicos procedentes de los buscadores. El trabajo SEO busca satisfacer ciertos factores de posicionamiento que tienen en cuenta los algoritmos de los buscadores en la ordenación de los resultados de búsqueda. En los últimos años hemos visto como estos algoritmos han ido virando hacia factores y señales orientados a priorizar aquellos resultados que mejor satisfacen la intención de búsqueda que se esconde tras la palabra clave utilizada, ofreciendo también la mejor experiencia de usuario posible en la página de destino. Tras un análisis bibliográfico de los factores relacionados con el análisis de la intención de búsqueda y los factores relacionados con la mejora de la experiencia de usuario desde un punto de vista SEO en el buscador de Google, se recogen un conjunto de acciones y estrategias que pueden implementarse con el objetivo de mejorar el posicionamiento de las páginas de un sitio web.</p>", "Keywords": "SEO;Posicionamiento web;Buscadores;Análisis de palabras clave;Intención de búsqueda;Experiencia de usuario", "DOI": "10.5209/cdmu.81156", "PubYear": 2022, "Volume": "32", "Issue": "", "JournalId": 45675, "JournalTitle": "Cuadernos de Documentación Multimedia", "ISSN": "", "EISSN": "1575-9733", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departament de Biblioteconomia, Documentació i Comunicació Audiovisual. Universitat de Barcelona"}], "References": []}, {"ArticleId": 96356912, "Title": "System design of a text messaging program to support the mental health needs of non-treatment seeking young adults", "Abstract": "Young adults (ages 18-25) experience the highest levels of mental health problems of any adult age group, but have the lowest mental health treatment rates. Text messages are the most used feature on the mobile phone and provide an opportunity to reach non-treatment engaged users throughout the day in a conversational manner. We present the design of an automated text message-based intervention for symptom self-management. The intervention comprises: (1) psychological strategies (i.e., types of evidence-based techniques leveraged to achieve symptom reduction) and (2) interaction types or the form that intervention content takes as it is delivered to and elicited from users.", "Keywords": "mental health ; young adults ; automated messaging ; behavioral intervention technology", "DOI": "10.1016/j.procs.2022.09.086", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Northwestern University, Center for Behavioral Intervention Technologies, 750 N. Lake Shore Drive, 10th Floor, Chicago, IL 60611, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mental Health America, 500 Montgomery Street, Suite 820, Alexandria, VA 22314, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Audacious Software, 3900 N. Fremont Street, Unit B, Chicago, IL 60613"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Irvine, <PERSON> School of Information and Computer Sciences, Department of informatics, 6210 <PERSON>, Irvine, CA 92697, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Toronto, Department of Computer Science, 40 St. George Street, Toronto, ON M5S 2E4, Canada"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "University of Toronto, Department of Computer Science, 40 St. George Street, Toronto, ON M5S 2E4, Canada"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Northwestern University, Center for Behavioral Intervention Technologies, 750 N. Lake Shore Drive, 10th Floor, Chicago, IL 60611, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Northwestern University, Center for Behavioral Intervention Technologies, 750 N. Lake Shore Drive, 10th Floor, Chicago, IL 60611, USA"}], "References": []}, {"ArticleId": 96356923, "Title": "How can machine learning identify suicidal ideation from user's texts? Towards the explanation of the Boamente system", "Abstract": "Suicidal Ideation (SI) is characterized by a desire to die and, in many cases, even planning a suicide with writing notes or farewell letters. As a digital phenotyping-based application, the Boamente tool remotely detects patterns that indicate SI by passively collecting texts typed by patients under supervision of a mental health professional. The system uses Artificial Intelligence (AI) techniques in the process of classifying texts as positive or negative for suicidal ideation. However, there is no explanation yet on how texts influence the outputs of Machine/Deep Learning (ML/DL) models. This study aims to start the process of explaining how words and sentences of the collected texts influence the outputs of the classification models. The following classical ML algorithms were trained and models explained in this study: C-Support Vector Classification (SVC), Extra Trees, and Random Forest machine learning. The ELI5 method was used for the local and global interpretability of the algorithms. Features of each class were analyzed with their respective weight values on the output of each algorithm in the global interpretability. Classifications of three samples from the dataset were explained during the local interpretability step. After analyzing the features of each class, we were able to identify that the Extra Trees and Random Forest classifiers were equally influenced by features composed of one term or two terms. SVC was more influenced by features composed of two terms than just one term. In general, features including the features “suicide”, “desire to kill oneself” and “sadness” had a higher importance value to indicate positive for SI.", "Keywords": "Digital Phenotyping ; Explainable AI ; ELI5 ; Artificial Intelligence ; Mobile Application ; Natural Language Processing ; Suicide", "DOI": "10.1016/j.procs.2022.09.093", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technological Neuro Innovation Laboratory, Federal University of Parnáıba Delta, Parnaíba, 64202-020, Brazil;Federal Institute of Ceará, Tianguá, Ceará, 62320-000, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> J.<PERSON>", "Affiliation": "Federal Institute of Maranhão, Araioses, Maranhão, 65570-000, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technological Neuro Innovation Laboratory, Federal University of Parnáıba Delta, Parnaíba, 64202-020, Brazil"}, {"AuthorId": 4, "Name": "Ariel S. Teles", "Affiliation": "Technological Neuro Innovation Laboratory, Federal University of Parnáıba Delta, Parnaíba, 64202-020, Brazil;Federal Institute of Maranhão, Araioses, Maranhão, 65570-000, Brazil"}], "References": [{"Title": "Joint evaluation of preprocessing tasks with classifiers for sentiment analysis in Brazilian Portuguese language", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "10", "Page": "15391", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A comprehensive survey on sentiment analysis: Approaches, challenges and trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "107134", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Predicting acute suicidal ideation on Instagram using ensemble machine learning models", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "", "Page": "100424", "JournalTitle": "Internet Interventions"}]}, {"ArticleId": 96356924, "Title": "A Narrative Serious Game to Teach Self-Advocacy Skills in Advanced Cancer", "Abstract": "Patients with advanced cancer face an onslaught of challenges. Self-advocacy refers to the way patients with cancer ensure their needs and priorities are met in the face of a challenge. The three key dimensions of self-advocacy include: (1) informed decision-making, (2) connected strength, and (3) effective communication. Our research team developed Strong Together, a narrative serious game to teach self-advocacy skills to female cancer survivors. Evidence-based evaluation to optimize serious game features is lacking. We aim to present a novel research protocol to assess the impacts of various game mechanisms of Strong Together (including its aesthetic feedback systems, soft fail states, and third-person perspective as opposed to explicit feedback, hard fail states, and a first-person perspective) embedded within a randomized clinical trial.", "Keywords": "serious games ; trial design ; cancer ; self-advocacy", "DOI": "10.1016/j.procs.2022.09.095", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Pittsburgh School of Computing and Information, 135 N Bellefield Ave, Pittsburgh, PA 15213, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Pittsburgh School of Nursing, 3500 Victoria St, Pittsburgh, PA 15261, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Pittsburgh School of Computing and Information, 135 N Bellefield Ave, Pittsburgh, PA 15213, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Pittsburgh School of Nursing, 3500 Victoria St, Pittsburgh, PA 15261, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Pittsburgh School of Computing and Information, 135 N Bellefield Ave, Pittsburgh, PA 15213, USA"}], "References": [{"Title": "Revealing the theoretical basis of gamification: A systematic review and analysis of theory in research on gamification, serious games and game-based learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "125", "Issue": "", "Page": "106963", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 96357074, "Title": "Detecting and Managing Adolescent Depression in Primary Care: A Pilot Randomized Controlled Trial", "Abstract": "We conducted a pilot randomized controlled trial of 100 youth to receive “Enhanced Screening as Usual” which was a basic report listing mental health symptom scores (n=23) or Screening Wizard 2.0, a web-based enhanced screener for adolescents and parents producing an extensive report for providers (n=77). All participants completed self-report assessments and electronic health-record review. Patients receiving Screening Wizard 2.0 were more likely to receive a referral for outpatient psychotherapy (25.7% vs. 18.2%) compared to Enhanced Screening as Usual, odds ratio = 1.56. Satisfaction with Screening Wizard 2.0 was high, with mean scores of 6.5 on a 1-7 scale.", "Keywords": "depression ; suicidality ; screening ; primary care", "DOI": "10.1016/j.procs.2022.09.087", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Pittsburgh School of Medicine, 3550 Terrace St, Pittsburgh, PA 15213, USA;Adolescent and Young Adult Medicine, UPMC Children's Hospital of Pittsburgh, 4401 Penn Ave, Pittsburgh, PA 15224, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Pittsburgh School of Medicine, 3550 Terrace St, Pittsburgh, PA 15213, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Pittsburgh School of Medicine, 3550 Terrace St, Pittsburgh, PA 15213, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Midwestern University- Chicago College of Osteopathic Medicine 555 31st Street, Downers Grove, IL 60515, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "UPMC Western Psychiatric Hospital, 3811 O'Hara St, Pittsburgh, PA 15213, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "UPMC Western Psychiatric Hospital, 3811 O'Hara St, Pittsburgh, PA 15213, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of Pittsburgh School of Medicine, 3550 Terrace St, Pittsburgh, PA 15213, USA;UPMC Western Psychiatric Hospital, 3811 O'Hara St, Pittsburgh, PA 15213, USA"}], "References": []}, {"ArticleId": 96357075, "Title": "Dyadic digital health interventions: Their rationale and implementation", "Abstract": "While most psychosocial and behavioral digital health interventions have been designed to be consumed by an individual, intervening at the level of a dyad – two interdependent individuals – can more comprehensively address the needs of both individuals and their relationship. The clinical utility of the dyadic digital health intervention approach, as well as the practical implementation of this design, will be demonstrated via three examples: eSCCIP, FAMS, and OurRelationship.", "Keywords": "dyads ; couples therapy ; family therapy ; digital health ; eHealth ; mHealth", "DOI": "10.1016/j.procs.2022.09.097", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for Behavioral Health and Technology, University of Virginia, Charlottesville, VA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Medicine, Vanderbilt University, Nashville, TN, USA;Center for Health Behavior and Health Education, Vanderbilt University Medical Center, Nashville, TN, USA"}, {"AuthorId": 3, "Name": "<PERSON> Salivar", "Affiliation": "Department of Clinical and School Psychology, Nova Southeastern University, Ft. Lauderdale, FL, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Psychology, University of Miami, Coral Gables, FL, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Nemours Center for Healthcare Delivery Science, Nemours Children's Health, Wilmington, DE, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Pediatrics, Sidney Kimmel Medical College, Philadelphia, PA, USA;Nemours Center for Healthcare Delivery Science, Nemours Children's Health, Wilmington, DE, USA"}], "References": []}, {"ArticleId": 96357084, "Title": "Other PHQ-9 item pairings are better than the PHQ-2: A Machine Learning analysis", "Abstract": "The Patient Health Questionnaire-2 (PHQ-2) is the most commonly used ultra-brief depressive symptomatology pre-screening instrument. However, this analysis demonstrated the PHQ-2 is not superior to other PHQ-9 item pairings in this sample. 12 out of the 36 pairings had an equal or higher cross-validation area under the receiver operating characteristic curve than the PHQ-2. The two pairings of phq2&amp;4 and phq2&amp;8 performed best and suggest low energy or psychomotor retardation or agitation may be more discriminatory symptoms than anhedonia. Pairings were combined with machine learning models which are not restricted to the summation and greater-than-or-equal-to logic of the PHQ-2 thresholds.", "Keywords": "Depression ; Depressive symptomatology ; PHQ-9 items ; PHQ-2 ; Ultra-brief questionnaires ; Machine Learning", "DOI": "10.1016/j.procs.2022.09.089", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University of Limerick, Limerick V94 T9PX, Ireland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University of Limerick, Limerick V94 T9PX, Ireland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University of Limerick, Limerick V94 T9PX, Ireland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Psiquiatria, Faculdade de Medicina FMUSP, Universidade de Sao Paulo, Sao Paulo, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Departamento de Psiquiatria, Faculdade de Medicina FMUSP, Universidade de Sao Paulo, Sao Paulo, Brazil;Instituto de Psiquiatria, Hospital das Clinicas HCFMUSP, Faculdade de Medicina, Universidade de Sao Paulo, Sao Paulo, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Centre for Global Mental Health, Institute of Psychiatry, King's College London, London WC2R 2LS, United Kingdom"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Bristol Dental School, University of Bristol, Bristol BS1 2LY, United Kingdom"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Bristol Medical School, University of Bristol, Bristol BS8 1NU, United Kingdom"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Health Research Institute, University of Limerick, V94 T9PX, Ireland;Health Research Institute, University of Limerick, V94 T9PX, Ireland"}], "References": []}, {"ArticleId": 96357086, "Title": "Efficacy and acceptability of digital stress management micro-interventions", "Abstract": "Tremendous interest exists in the use of brief digital “micro-interventions” for online and mobile intervention. Most digital micro-interventions, however, lack a strong theoretical or empirical basis and have not demonstrated efficacy and acceptability. We developed a suite of brief digital stress management micro-interventions based on theory and empirical evidence and tested the efficacy and acceptability of these micro-interventions in managing stress, mood, and perseverative cognition. Participants were 1,050 US adults (ages 35-65) in good health who received digital micro-intervention content (or comparison content) and then completed post-intervention measures and acceptability ratings. We created 16 brief (&lt;2 min) micro-interventions across four therapeutic domains (relaxation, response modulation, positive experiences, and resource buffers) and a brief active comparison activity. We also created one multi-component micro-intervention (∼20 min) that contained elements of all four domains, and a time-matched active comparison activity. A subset (n=850) of participants received one of the 16 brief micro-interventions or a brief comparator; the remainder (n=200) received either the longer multi-component micro-intervention or the time-matched active comparator task. All micro-intervention stress management content reduced acute stress, negative mood, and perseverative cognitions. For all outcomes, the multi-component intervention showed the strongest effects. Active comparator tasks were more weakly associated with outcomes (except that brief distraction was highly effective at reducing perseverative cognitions). Micro-intervention acceptability was generally high across multiple dimensions. These data demonstrate that a diverse set of 16 brief digital micro-interventions comparison activity were efficacious, and an integrative multi-component micro-intervention was more efficacious. Such micro-interventions hold great potential for scalable digital implementation, including “just-in-time” intervention in response to acute risk states.", "Keywords": "mHealth ; intervention ; ecological momentary assessment ; micro-intervention ; digital health", "DOI": "10.1016/j.procs.2022.09.084", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biobehavioral Health, Pennsylvania State University, University Park, PA, 16802, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Psychological Sciences, University of California Merced, Merced, CA, 95343, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Biobehavioral Health, Pennsylvania State University, University Park, PA, 16802, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Biobehavioral Health, Pennsylvania State University, University Park, PA, 16802, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Biobehavioral Health, Pennsylvania State University, University Park, PA, 16802, USA"}], "References": []}, {"ArticleId": 96357171, "Title": "Contents", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1877-0509(22)00977-2", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96357204, "Title": "AR Sightseeing: Comparing Information Placements at Outdoor Historical Heritage Sites using Augmented Reality", "Abstract": "<p>Augmented Reality (AR) has influenced the presentation of historical information to tourists and museum visitors by making the information more immersive and engaging. Since smartphones and AR glasses are the primary devices to present AR information to users, it is essential to understand how the information about a historical site can be presented effectively and what type of device is best suited for information placements. In this paper, we investigate the placement of two types of content, historical images and informational text, for smartphones and AR glasses in the context of outdoor historical sites. For this, we explore three types of placements: (1) on-body, (2) world, and (3) overlay. To evaluate all nine combinations of text and image placements for smartphone and AR glasses, we conducted a controlled experiment (N = 18) at outdoor historical landmarks. We discovered that on-body image and text placements were the most convenient compared to overlay and world for both devices. Furthermore, participants found themselves more successful in exploring historical sites using a smartphone than AR glasses. Although interaction with a smartphone was more convenient, participants found exploring AR content using AR glasses more fun.</p>", "Keywords": "augmented reality; historical heritage; information placement; sightseeing", "DOI": "10.1145/3546729", "PubYear": 2022, "Volume": "6", "Issue": "MHCI", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technical University of Darmstadt, Darmstadt, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technical University of Darmstadt, Darmstadt, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Technical University of Darmstadt, Darmstadt, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Technical University of Darmstadt, Darmstadt, Germany"}], "References": []}, {"ArticleId": 96357300, "Title": "Generation and Splitting of the Compound Words in Nepali Text", "Abstract": "<p>In Nepali language, compound word formation is mostly associated with inflection, derivation, and postposition attachment. Inflection occurs due to suffixation, whereas derivation is driven by both prefixation and suffixation. The compound word generated by the rules may produce lots of out-of-vocabulary words due to limited lexical resources and numerous exceptions. Hence, the machine learning approach can help to generate valid compounds and split them into valid morphemes that can be further used as a resource for spelling suggestions, information retrieval, and machine translation. In this research, a method to generate valid compounds from the corresponding compound splits (head word and prefix/suffix/ postpositions) is suggested. A BiLSTM based deep learning approach was used to generate and split the valid compound words. Publicly available Nepali <PERSON><PERSON><PERSON> data from Nepal Academy and scraped news data were used for the experimentation. The obtained results were found to be outstanding compared to the rule-based approach applied to a similar job.</p>", "Keywords": "", "DOI": "10.36548/jitdw.2022.3.007", "PubYear": 2022, "Volume": "4", "Issue": "3", "JournalId": 71072, "JournalTitle": "Journal of Information Technology and Digital World", "ISSN": "", "EISSN": "2582-418X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96357395, "Title": "Analyzing perceptions of a global event using CNN-LSTM deep learning approach: the case of Hajj 1442 (2021)", "Abstract": "<p>Hajj (pilgrimage) is a unique social and religious event in which many Muslims worldwide come to perform Hajj. More than two million people travel to Makkah, Saudi Arabia annually to perform various Hajj rituals for four to five days. However, given the recent outbreak of the coronavirus (COVID-19) and its variants, Hajj in the last 2 years 2020–2021 has been different because pilgrims were limited down to a few thousand to control and prevent the spread of COVID-19. This study employs a deep learning approach to investigate the impressions of pilgrims and others from within and outside the Makkah community during the 1442 AH Hajj season. Approximately 4,300 Hajj-related posts and interactions were collected from social media channels, such as Twitter and YouTube, during the Hajj season Dhul-Hijjah 1–13, 1442 (July 11–23, 2021). Convolutional neural networks (CNNs) and long short-term memory (LSTM) deep learning methods were utilized to investigate people’s impressions from the collected data. The CNN-LSTM approach showed superior performance results compared with other widely used classification models in terms of F-score and accuracy. Findings revealed significantly positive sentiment rates for tweets collected from Mina and Arafa holy sites, with ratios exceeding 4 out of 5. Furthermore, the sentiment analysis (SA) rates for tweets about Hajj and pilgrims varied during the days of Hajj. Some were classified as positive tweets, such as describing joy at receiving the days of Hajj, and some were negative tweets, such as expressing the impression about the hot weather and the level of satisfaction for some services. Moreover, the SA of comments on several YouTube videos revealed positive classified comments, including praise and supplications, and negative classified comments, such as expressing regret that the Hajj was limited to a small number of pilgrims.</p>", "Keywords": "Convolutional Neural Networks (CNN);Deep learning;Hajj rituals;Long short term memory;Sentiment analysis", "DOI": "10.7717/peerj-cs.1087", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Custodian of the Two Holy Mosques Institute for Hajj and Umrah Research, Umm Al-Qura University, Makkah, Saudi Arabia."}], "References": [{"Title": "Deep learning CNN–LSTM framework for Arabic sentiment analysis using textual information shared in social networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "A Hybrid CNN-LSTM Model for SMS Spam Detection in Arabic and English Messages", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "9", "Page": "156", "JournalTitle": "Future Internet"}, {"Title": "Sentiment analysis on the impact of coronavirus in social life using the BERT model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "33", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Social Media Sentiment Analysis: The Hajj Tweets Case Study", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "3", "Page": "265", "JournalTitle": "Journal of Computer Science"}, {"Title": "Multi-level aspect based sentiment classification of Twitter data: using hybrid approach in deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Detecting Epidemic Diseases Using Sentiment Analysis of Arabic Tweets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "1", "Page": "50", "JournalTitle": "JUCS - Journal of Universal Computer Science"}, {"Title": "Identification of affective valence of Twitter generated sentiments during the COVID-19 outbreak", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "108", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Air-pollution prediction in smart city, deep learning approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "161", "JournalTitle": "Journal of Big Data"}, {"Title": "Fruit Image Classification Using Deep Learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "3", "Page": "5135", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A Late Acceptance Hyper-Heuristic Approach for the Optimization Problem of Distributing Pilgrims over <PERSON>", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "4", "Page": "396", "JournalTitle": "JUCS - Journal of Universal Computer Science"}]}, {"ArticleId": 96357528, "Title": "A geographic information model for 3-D environmental suitability analysis in railway alignment optimization", "Abstract": "<p>Railway alignment design is a complicated problem affected by intricate environmental factors. Although numerous alignment optimization methods have been proposed, a general limitation among them is the lack of a spatial environmental suitability analysis to guide the subsequent alignment search. Consequently, many unfavorable regions in the study area are still searched, which significantly degrades optimization efficiency. To solve this problem, a geographic information model is proposed for evaluating the environmental suitability of railways. Initially, the study area is abstracted as a spatial voxel set and the 3-D reachable ranges of railways are determined. Then, a geographic information model is devised which considers topographic influencing factors (including those affecting structural cost and stability) as well as geologic influencing factors (including landslides and seismic impacts) for different railway structures. Afterward, a 3-D environmental suitability map can be generated using a multi-criteria decision-making approach to combine the considered factors. The map is further integrated into the alignment optimization process based on a 3-D distance transform algorithm. The proposed model and method are applied to two complex realistic railway cases. The results demonstrate that they can considerably improve the search efficiency and also find better alignments compared to the best alternatives obtained manually by experienced human designers and produced by a previous distance transform algorithm as well as a genetic algorithm.</p>", "Keywords": "", "DOI": "10.3233/ICA-220692", "PubYear": 2022, "Volume": "30", "Issue": "1", "JournalId": 24898, "JournalTitle": "Integrated Computer-Aided Engineering", "ISSN": "1069-2509", "EISSN": "1875-8835", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Pu", "Affiliation": "School of Civil Engineering, Central South University, Changsha, Hunan, China;National Engineering Research Center of High-speed Railway Construction Technology, Changsha, Hunan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Central South University, Changsha, Hunan, China;National Engineering Research Center of High-speed Railway Construction Technology, Changsha, Hunan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Central South University, Changsha, Hunan, China;National Engineering Research Center of High-speed Railway Construction Technology, Changsha, Hunan, China;Department of Civil Engineering, University of British Columbia, Vancouver, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Maryland, College Park, MD, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Civil Engineering, Central South University, Changsha, Hunan, China;National Engineering Research Center of High-speed Railway Construction Technology, Changsha, Hunan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Railway Eryuan Engineering Group Co. Ltd, Chengdu, Sichuan, China"}], "References": [{"Title": "Multi-objective railway alignment optimization considering costs and environmental impacts", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106105", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 96357564, "Title": "An Algorithm for Maximizing a Convex Function Based on Its Minimum", "Abstract": "In this paper, an algorithm for maximizing a convex function over a convex feasible set is proposed. The algorithm, called CoMax, consists of two phases: in phase 1, a feasible starting point is obtained that is used in a gradient ascent algorithm in phase 2. The main contribution of the paper is connected to phase 1; five different methods are used to approximate the original NP-hard problem of maximizing a convex function (MCF) by a tractable convex optimization problem. All the methods use the minimizer of the convex objective function in their construction. In phase 2, the gradient ascent algorithm yields stationary points to the MCF problem. The performance of CoMax is tested on a wide variety of MCF problems, demonstrating its efficiency. History: Accepted by <PERSON>, Area Editor for Design & Analysis of Algorithms–Continuous. Funding: This work was supported by the Nederlandse Organisatie voor Wetenschappelijk Onderzoek [Grant 406.17.511]. Supplemental Material: The software that supports the findings of this study is available within the paper and its Supplementary Information [ https://pubsonline.informs.org/doi/suppl/10.1287/ijoc.2022.1238 ] or is available from the IJOC GitHub software repository ( https://github.com/INFORMSJoC ) at [ http://dx.doi.org/10.5281/zenodo.6884872 ].", "Keywords": "global optimization; convex maximization; gradient ascent; hidden convexity", "DOI": "10.1287/ijoc.2022.1238", "PubYear": 2022, "Volume": "34", "Issue": "6", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Industrial Engineering and Management, Technion – Israel Institute of Technology, Haifa 32000, Israel;;Shenkar College, Ramat Gan 50200, Israel"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ORTEC, 2719 EA Zoetermeer, Netherlands"}], "References": [{"Title": "Convex Maximization via Adjustable Robust Optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "4", "Page": "2091", "JournalTitle": "INFORMS Journal on Computing"}]}, {"ArticleId": 96357582, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0921-8890(22)00164-6", "PubYear": 2022, "Volume": "157", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [], "References": []}, {"ArticleId": 96357721, "Title": "Walk a mile in their shoes", "Abstract": "<p>The COVID-19 pandemic through the lens of four tech workers.</p>", "Keywords": "", "DOI": "10.1145/3561989", "PubYear": 2022, "Volume": "65", "Issue": "10", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Microsoft Research, Redmond, WA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Williams College, Williamson, MA"}], "References": [{"Title": "Belonging There", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 96357829, "Title": "Retraction Note: Automatic test case generation for validating the dynamic behaviors of the complete solar power monitoring system", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-022-13970-1", "PubYear": 2023, "Volume": "82", "Issue": "2", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "SE Laboratory, Sejong Campus, Hongik University, Sejong, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software, Gangneung-Wonju National University, Wonju, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Son", "Affiliation": "SE Laboratory, Sejong Campus, Hongik University, Sejong, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "SE Laboratory, Sejong Campus, Hongik University, Sejong, South Korea"}], "References": []}, {"ArticleId": 96357836, "Title": "Testing software’s changing features with environment-driven abstraction identification", "Abstract": "<p>ions are significant domain terms that have assisted in requirements elicitation and modeling. To extend the assistance toward requirements validation, we present in this paper an automated approach to identifying the abstractions for supporting requirements-based testing. We select relevant Wikipedia pages to serve as a domain corpus that is independent from any specific software system. We further define five novel patterns based on part-of-speech tagging and dependency parsing, and frame our candidate abstractions in the form of &lt;key, value&gt; pairs for better testability, where the \"key\" helps locate \"what to test\", and the \"value\" helps guide \"how to test it\" by feeding in concrete data. We evaluate our approach with six software systems in two application domains: Electronic health records and Web conferencing. The results show that our abstractions are more accurate than those generated by a state-of-the-art technique. While the initial findings indicate our abstractions' capabilities of revealing bugs and matching the environmental assumptions created manually, we articulate a new way to perform requirements-based testing by focusing on a software system's changing features. Specifically, we hypothesize that the same feature would behave differently under a pair of opposing environmental conditions and assess our abstractions' applicability to this new form of feature testing.</p><p>© The Author(s), under exclusive licence to Springer-Verlag London Ltd., part of Springer Nature 2022, Springer Nature or its licensor holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "Abstractions;Environmental assumptions and conditions;Natural language;Requirements-based testing", "DOI": "10.1007/s00766-022-00390-8", "PubYear": 2022, "Volume": "27", "Issue": "4", "JournalId": 15613, "JournalTitle": "Requirements Engineering", "ISSN": "0947-3602", "EISSN": "1432-010X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Cincinnati, Cincinnati, OH USA."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Cincinnati, Cincinnati, OH USA."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Cincinnati, Cincinnati, OH USA."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mississippi State University, Mississippi State, Starkville, MS USA."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing, China."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute of Software Chinese Academy of Sciences, University of Chinese Academy of Sciences, Beijing, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Peking University Beijing, Beijing, China."}], "References": [{"Title": "NLP-assisted software testing: A systematic mapping of the literature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "126", "Issue": "", "Page": "106321", "JournalTitle": "Information and Software Technology"}, {"Title": "Feature requests-based recommendation of software refactorings", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "5", "Page": "4315", "JournalTitle": "Empirical Software Engineering"}, {"Title": "Controlled experimentation in continuous experimentation: Knowledge and challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "134", "Issue": "", "Page": "106551", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 96357843, "Title": "Galois field transformation effect on space-time-volume velocimetry method for water surface velocity video analysis", "Abstract": "<p>This paper presents a novel method of image processing that combines Galois Field (GF) image representation and Space Time Volume Velocimetry (STVV) algorithms namely GF-STVV. STVV is an emerging method to analyze river water flows from video sequences. This study aimed to improve the performance of STVV when applied to close-range measurements (1 to 1.5 m) by reducing the variability (repeatibility and reproducibility) of computational results. Variability is related to the precision of the water flow measurement results. Small variability values indicate high measurement precision. Experiments were carried out using two main methods. The first method was by rotating videos data virtually, and the second one was by rotating the camera directly during video captures. The Experiment result shows that GF reduced the variability of computation results if applied to a particular Region Of Interest (ROI) in video frames of water flow. The particular ROI was obtained by dividing the video frame into several small regions. The GF-STVV generate 0.0 % for both repeatibility and reproducibility, while STVV generate 2.3 % for repeatibility and 3.3 % for reproducibility.</p>", "Keywords": "Galois Field; Space-Time-Volume Velocimetry; Water Surface Velocity; Video Processing; Open Channel Flow Measurement; Region of Interest", "DOI": "10.1007/s11042-022-13627-z", "PubYear": 2023, "Volume": "82", "Issue": "8", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Bernadus <PERSON>", "Affiliation": "Faculty of Computer Science, Universitas Indonesia, Depok, Indonesia; Research Centre of Electronics, National Research and Innovation Agency of Indonesia, Tangerang Selatan, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Science, Universitas Indonesia, Depok, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Research Centre of Photonics, National Research and Innovation Agency of Indonesia, Tangerang Selatan, Indonesia"}], "References": [{"Title": "OpenOpticalFlow_PIV: An Open Source Program Integrating Optical Flow\n Method with Cross- Correlation Method for Particle Image Velocimetry", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Open Research Software"}]}]