[{"ArticleId": 97085029, "Title": "Self-Transfer Learning Network for Multicolor Fabric Defect Detection", "Abstract": "<p>This paper presented a self-transfer learning network (STLN) for multicolor fabric defect detection. Deep neural networks were adopted to detect defects in objects with complex backgrounds such as multicolored fabrics. It is noteworthy that the more disturbances there are on the object surface, the more difficult it is to optimize the network and the more training samples will be required. At the same time, the distinct difference in different types of multi-colored fabrics makes model difficult to apply data information. To this end, the STLN in this paper, consisting of a dataset expansion module, a dataset filtering module, a feature extraction module, a defect detection module, and a category discrimination module, used only limited raw data without the help of external data, and expanded the training set by mining the features of the raw data to better optimize the network. It is experimentally demonstrated that the STLN can achieve higher accuracy compared to deep neural networks for detecting defects of multicolor fabrics with insufficient target data.</p>", "Keywords": "Convolutional neural network (CNN); Transfer learning; Intelligent manufacturing; Defect detection·deep learning", "DOI": "10.1007/s11063-022-11063-6", "PubYear": 2023, "Volume": "55", "Issue": "4", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechatronics Engineering, Harbin Institute of Technology, Harbin, China; School of Mechanical and Electrical Engineering, Soochow University, Suzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Harbin Institute of Technology, Harbin, China; School of Mechanical and Electrical Engineering, Soochow University, Suzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>ing <PERSON>", "Affiliation": "School of Mechatronics Engineering, Harbin Institute of Technology, Harbin, China; School of Mechanical and Electrical Engineering, Soochow University, Suzhou, China"}], "References": [{"Title": "Collective transfer learning for defect prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "416", "Issue": "", "Page": "103", "JournalTitle": "Neurocomputing"}, {"Title": "A level set method based on additive bias correction for image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115633", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 97085076, "Title": "On the Beneficial Effects of Reinjections for Continual Learning", "Abstract": "<p>Deep learning delivers remarkable results in a wide range of applications, but artificial neural networks still suffer from catastrophic forgetting of old knowledge as new knowledge is learned. Rehearsal methods overcome catastrophic forgetting by replaying an amount of previously learned data stored in dedicated memory buffers. Alternatively, pseudo-rehearsal methods generate pseudo-samples to emulate previously learned data, alleviating the need for dedicated buffers. First, we show that it is possible to alleviate catastrophic forgetting with a pseudo-rehearsal method without employing memory buffers or generative models. We propose a hybrid architecture similar to that of an autoencoder with additional neurons to classify the input. This architecture preserves specific properties of autoencoders by allowing the generation of pseudo-samples through reinjections (i.e. iterative sampling) from random noise. The generated pseudo-samples are then interwoven with the new examples to acquire new knowledge without forgetting the previous ones. Second, we combine the two methods (rehearsal and pseudo-rehearsal) in the hybrid architecture. Examples stored in small memory buffers are employed as seeds instead of noise to improve the process of generating pseudo-samples and retrieving previously learned knowledge. We demonstrate that reinjections are suitable for rehearsal and pseudo-rehearsal approaches and show state-of-the-art results on rehearsal methods for small buffer sizes. We evaluate our method extensively on MNIST, CIFAR-10 and CIFAR-100 image classification datasets.</p>", "Keywords": "Incremental learning; Lifelong learning; Continual learning; Sequential learning; Pseudo-rehearsal; Rehearsal", "DOI": "10.1007/s42979-022-01392-7", "PubYear": 2023, "Volume": "4", "Issue": "1", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Univ. Grenoble Alpes, CEA, LIST, Grenoble, France"}, {"AuthorId": 2, "Name": "Marina <PERSON>", "Affiliation": "Univ. Grenoble Alpes, CEA, LIST, Grenoble, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Univ. Grenoble Alpes, CEA, LIST, Grenoble, France; LPNC, Univ Grenoble Alpes, Univ Savoie Mont Blanc, Grenoble, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Univ. Grenoble Alpes, CEA, LIST, Grenoble, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Univ. Grenoble Alpes, CEA, LIST, Grenoble, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Univ. Grenoble Alpes, CEA, LIST, Grenoble, France; LPNC, Univ Grenoble Alpes, Univ Savoie Mont Blanc, Grenoble, France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Univ. Grenoble Alpes, CEA, LIST, Grenoble, France"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Univ. Grenoble Alpes, CEA, LIST, Grenoble, France; LPNC, Univ Grenoble Alpes, Univ Savoie Mont Blanc, Grenoble, France"}], "References": [{"Title": "Pseudo-rehearsal: Achieving deep reinforcement learning without catastrophic forgetting", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "428", "Issue": "", "Page": "291", "JournalTitle": "Neurocomputing"}, {"Title": "Online continual learning in image classification: An empirical survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "469", "Issue": "", "Page": "28", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 97085164, "Title": "A Sparse Distributed Gigascale Resolution Material Point Method", "Abstract": "<p> In this article, we present a four-layer distributed simulation system and its adaptation to the Material Point Method (MPM). The system is built upon a performance portable C++ programming model targeting major High-Performance-Computing (HPC) platforms. A key ingredient of our system is a hierarchical block-tile-cell sparse grid data structure that is distributable to an arbitrary number of Message Passing Interface (MPI) ranks. We additionally propose strategies for efficient dynamic load balance optimization to maximize the efficiency of MPI tasks. Our simulation pipeline can easily switch among backend programming models, including OpenMP and CUDA, and can be effortlessly dispatched onto supercomputers and the cloud. Finally, we construct benchmark experiments and ablation studies on supercomputers and consumer workstations in a local network to evaluate the scalability and load balancing criteria. We demonstrate massively parallel, highly scalable, and gigascale resolution MPM simulations of up to 1.01 billion particles for less than 323.25 seconds per frame with 8 OpenSSH-connected workstations. </p>", "Keywords": "", "DOI": "10.1145/3570160", "PubYear": 2023, "Volume": "42", "Issue": "2", "JournalId": 15014, "JournalTitle": "ACM Transactions on Graphics", "ISSN": "0730-0301", "EISSN": "1557-7368", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Los Angeles, CA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Oak Ridge National Laboratory, Oak Ridge, TN"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Los Angeles & Timestep Technologies"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Utah & Timestep Technologies, Salt Lake City, UT"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Oak Ridge National Laboratory, Oak Ridge, TN"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of California, Los Angeles & Timestep Technologies"}], "References": [{"Title": "A massively parallel and scalable multi-GPU material point method", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Enabling particle applications for exascale computing platforms", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "6", "Page": "572", "JournalTitle": "The International Journal of High Performance Computing Applications"}, {"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "<PERSON>", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 97085341, "Title": "Design and Implementation of Soil Moisture Monitoring and Irrigation System based on ARM and IoT", "Abstract": "This paper proposed a novel framework for soil humidity control and watering management based on the Internet of Things (IoT) and ARM. The framework is designed to minimize manual field process and transfer information to a cloud server that is accessible through a web application. The proposed scheme has advantages in reducing costs, restricting water waste as well as minimizing the physical interference. It also advocates low maintenance and environmentally sustainable irrigation. The sound, humidity and temperature sensors are combined with ARM single board computer (called the Raspberry Pi). A relay module is placed to regulate the stream of water. The sensor obtained values are stored on the cloud server and the necessary values and recommendation are provided via the web application. When the ground temperature is high and soil humidity is low, the automatic irrigation system is triggered and user notification can be performed with email. The model showed expected impacts at the various levels of humidity.", "Keywords": "IoT Systems ; ARM ; raspberry Pi ; smart irrigation ; soil moisture sensor", "DOI": "10.1016/j.procs.2022.10.067", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Tongda College of Nanjing University of Posts and Telecommunications, Yangzhou 225127, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>ebremaria<PERSON>", "Affiliation": "Nanjing University of Posts and Telecommunications, Nanjing 210023, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Posts and Telecommunications, Nanjing 210023, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Nanjing University of Posts and Telecommunications, Nanjing 210023, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Nanjing University of Posts and Telecommunications, Nanjing 210023, China"}], "References": []}, {"ArticleId": 97085491, "Title": "Electrostatic Adhesion Clutch with Superhigh Force Density Achieved by MXene-Poly(Vinylidene Fluoride–Trifluoroethylene–Chlorotrifluoroethylene) Composites", "Abstract": "<p>Electrostatic adhesion (EA) clutches are widely applied in robots, wearable devices, and virtual reality, due to their compliance, lightweight, ultrathin profile, and low power consumption. Higher force density has been constantly perpetuated in the past decades since EA was initially proposed. In this study, by composing terpolymer poly(vinylidene fluoride-trifluoroethylene-chlorotrifluoroethylene) [P(VDF-TrFE-CTFE)] and two-dimensional Ti<sub>3</sub>C<sub>2</sub>T<sub><i>x</i></sub> nanosheets (MXene), nanocomposite films with high dielectric constant (δr' &gt; 2300) and low loss tangent are achieved. The force representative index δr'Ebd2 (the relative dielectric constant times the square of breakdown electric field) is enhanced by 5.91 times due to the charge accumulation at matrix-filler interfaces. Superhigh shear stress (85.61 N cm<sup>-2</sup>) is generated, 408% higher than the previous maximum value. One of the EA clutches fabricated in this study is only 160 μm thin and 0.4 g heavy. Owing to the low current (&lt;1 μA), the power consumption is &lt;60 mW/cm<sup>2</sup>. It can hold a 2.5 kg weight by only 0.32 cm<sup>2</sup> area and support an adult (45 kg) (Clinical Trial Registration number: 20210090). With this technology, a dexterous robotic hand is displayed to grasp and release a ball, showing extensive applications of this technique.</p>", "Keywords": "MXene;P(VDF-TrFE-CTFE);composites;electrostatic adhesion;high-force density", "DOI": "10.1089/soro.2022.0013", "PubYear": 2023, "Volume": "10", "Issue": "3", "JournalId": 12246, "JournalTitle": "Soft Robotics", "ISSN": "2169-5172", "EISSN": "2169-5180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Southern Marine Science and Engineering Guangdong Laboratory (Guangzhou), Guangzhou, China.;Shenzhen Key Laboratory of Biomimetic Robotics and Intelligent Systems, Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Southern Marine Science and Engineering Guangdong Laboratory (Guangzhou), Guangzhou, China.;Shenzhen Key Laboratory of Biomimetic Robotics and Intelligent Systems, Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Engineering Research Center for Novel Electronic Information Materials and Devices, Department of Materials Science and Engineering, Southern University of Science and Technology, Shenzhen, China.;State Key Laboratory for Mechanical Behavior of Materials, School of Electronic and Information Engineering, Xi'an Jiaotong University, Xi'an, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Southern Marine Science and Engineering Guangdong Laboratory (Guangzhou), Guangzhou, China.;Shenzhen Key Laboratory of Biomimetic Robotics and Intelligent Systems, Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Southern Marine Science and Engineering Guangdong Laboratory (Guangzhou), Guangzhou, China.;Shenzhen Key Laboratory of Biomimetic Robotics and Intelligent Systems, Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Southern Marine Science and Engineering Guangdong Laboratory (Guangzhou), Guangzhou, China.;Shenzhen Key Laboratory of Biomimetic Robotics and Intelligent Systems, Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Engineering Research Center for Novel Electronic Information Materials and Devices, Department of Materials Science and Engineering, Southern University of Science and Technology, Shenzhen, China."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Southern Marine Science and Engineering Guangdong Laboratory (Guangzhou), Guangzhou, China.;Shenzhen Key Laboratory of Biomimetic Robotics and Intelligent Systems, Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, China.;Guangdong Provincial Key Laboratory of Human-Augmentation and Rehabilitation Robotics in Universities, Southern University of Science and Technology, Shenzhen, China."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Shenzhen Engineering Research Center for Novel Electronic Information Materials and Devices, Department of Materials Science and Engineering, Southern University of Science and Technology, Shenzhen, China."}], "References": [{"Title": "Electrostatic bellow muscle actuators and energy harvesters that stack up", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "51", "Page": "eaaz5796", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 97085560, "Title": "Classification of the wind turbine components based on importance degrees: A three-way decision perspective", "Abstract": "The bad working environment and various complex influencing factors lead to the high failure rate of a wind turbine. It is necessary to decide on a reasonable maintenance strategy for wind turbines. The classification of wind turbine components based on their importance degrees can identify the components which have an important impact on the reliability and maintenance of wind turbines. However, the traditional multi-criteria decision-making method can only provide the importance ranking of components, rather than the importance classification of components. The emergence of the three-way decision (TWD) method makes up for this deficiency. Then we classify the importance degrees of components by the TWD method. Firstly, the decision-theoretic rough sets are introduced into the uncertain linguistic setting to construct uncertain linguistic decision-theoretic rough sets. Secondly, the weights of experts are attained based on the consistency degree of loss functions. Thirdly, conditional probability is attained by the evaluation based on distance from the average solution method. And the classification of wind turbine components is derived based on the minimum-loss principle. Finally, a case study about the classification of the wind turbine components based on importance degrees is employed to certify the practicability of our designed method. The proposed model extends both the theory and practice of TWD and offers a classification helpful to make maintenance strategies for reducing the risk of component failure.", "Keywords": "", "DOI": "10.1016/j.asoc.2022.109754", "PubYear": 2022, "Volume": "131", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Southeast University, Nanjing, Jiangsu 210007, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, Southeast University, Nanjing, Jiangsu 210007, China;Business School, Sichuan University, Chengdu 610064, China;Corresponding author at: School of Economics and Management, Southeast University, Nanjing, Jiangsu 210007, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Nanjing Audit University, Nanjing, Jiangsu 211815, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Andalusian Research Institute in Data Science and Computational Intelligence, University of Granada, E-18071 Granada, Spain;Department of Electrical and Computer Engineering, Faculty of Engineering, King Abdulaziz University, Jeddah 21589, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Faculty of Engineering, King Abdulaziz University, P.O. Box 80204, Jeddah 21589, Saudi Arabia"}], "References": [{"Title": "Sequential three-way decisions via multi-granularity", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "606", "JournalTitle": "Information Sciences"}, {"Title": "A Novel Three-Way Investment Decisions Based on Decision-Theoretic Rough Sets with <PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "8", "Page": "2708", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "A novel decision-making approach based on three-way decisions in fuzzy information systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "541", "Issue": "", "Page": "362", "JournalTitle": "Information Sciences"}, {"Title": "Three‐way decisions based on some Hamacher aggregation operators under double hierarchy linguistic environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "12", "Page": "7731", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Failure mode and effect analysis: A three-way decision approach", "Authors": "Jiang<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104505", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 97085590, "Title": "Design and implementation of high speed, low complexity FFT/IFFT processor using modified mixed radix-24–22-23 algorithm for high data rate applications", "Abstract": "<p>A modified mixed radix algorithm with low complexity based Fast Fourier Transform (FFT) processor for large data rate applications is presented in this paper. In order to reduce the complexity of twiddle factor multiplication an improved FFT/IFFT architecture has been derived. A novel Modified Mixed Radix-2<sup>4</sup>-2<sup>2</sup>-2<sup>3</sup> (MMR-2<sup>4</sup>-2<sup>2</sup>-2<sup>3</sup>) algorithm is derived and implemented in this work with a 90 nm CMOS processing technology operating at 1.2 V. From the simulation results, the proposed algorithm reduces the power consumption and area when compared with the existing models. The power consumption of the proposed design is approximately 90.4 mW, which reduces the power consumption by 22.7%. Also, the normalized area of the complex constant multiplier is 14.88, which reduces the area by 26.19% when compared with existing architectures. A canonical signed digit constant multiplier with a common sub-expression sharing method is incorporated to further reduce the complexity of the multiplier by a factor of over 33%.</p>", "Keywords": "Mixed radix algorithm; Canonical signed digit constant multiplier; Common sub-expression sharing; Orthogonal frequency division multiplexing", "DOI": "10.1007/s41870-022-01128-z", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Vel Tech Rangarajan Dr. <PERSON> R & D Institute of Science and Technology, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Pavai College of Technology, Namakkal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "R & D Embedded Engineer, Connected Digital Systems Private Limited, Chennai, India"}], "References": [{"Title": "Implementation of convolution coded OFDM through different channel models on SDR platform", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "6", "Page": "2561", "JournalTitle": "International Journal of Information Technology"}, {"Title": "ASIC design of 4K-point RADIX-2 FFT in 0.18 µm CMOS technology", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "1427", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 97085592, "Title": "Double discriminative face super-resolution network with facial landmark heatmaps", "Abstract": "<p>At present, most of face super-resolution (SR) networks cannot balance the visual quality and the pixel accuracy. The networks with high objective index values often reconstruct too smooth images, while the networks which can restore texture information often introduce too much high-frequency noise and artifacts. Besides, some face super-resolution networks do not consider the mutual promotion between the extracting face prior knowledge part and the super-resolution reconstruction part. To solve these problems, we propose the double discriminative face super-resolution network (DDFSRNet). We propose a collaborative generator and two discriminators. Specifically, the collaborative generator, including the face super-resolution module (FSRM) and the face alignment module (FAM), can strengthen the reconstruction of facial key components, under the restriction of the perceptual similarity loss, the facial heatmap loss and double generative adversarial loss. We design the feature fusion unit (FFU) in FSRM, which integrates the facial heatmap features and SR features. FFU can use the facial landmarks to correct the face edge shape. Moreover, the double discriminators, including the facial SR discriminator (FSRD) and the facial landmark heatmap discriminator (FLHD), are used to judge whether face SR images and face heatmaps are from real data or generated data, respectively. Experiments show that the perceptual effect of our method is superior to other advanced methods on 4x reconstruction and fit the face high-resolution (HR) images as much as possible.</p>", "Keywords": "Face super-resolution; Generative adversarial network; Facial landmark Heatmaps; Deep learning", "DOI": "10.1007/s00371-022-02701-0", "PubYear": 2023, "Volume": "39", "Issue": "11", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Integrated Circuits and Electronics, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Integrated Circuits and Electronics, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Integrated Circuits and Electronics, Beijing Institute of Technology, Beijing, China"}], "References": [{"Title": "Back-projection-based progressive growing generative adversarial network for single image super-resolution", "Authors": "<PERSON><PERSON>ong Ma; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "5", "Page": "925", "JournalTitle": "The Visual Computer"}, {"Title": "(SARN)spatial-wise attention residual network for image super-resolution", "Authors": "<PERSON><PERSON> Shi; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "6", "Page": "1569", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 97085645, "Title": "Learning successful implementation of Chatbots in businesses from B2B customer experience perspective", "Abstract": "<p>Artificial intelligence empowered Chatbots are altering the nature of service interfaces which has further resulted in raised expectations from Chatbots to understand customer's social interactions and respond them within the turnaround time. To close this research gap, we conduct an exploratory study in two phases-industry's perspective and B2B customer's perspective and analyze results with the help of NVIVO 12 plus and Leximancer. The findings reveal perceived risk with respect to Chatbots is high, complex pricing structure along with nonavailability of testing options makes the pre purchase more complex. Moreover, interactive speed, customization especially with respect to language issues, integration with other platforms is some of the major themes which influence customer experience. Advancements in AI, natural language processing and more testing at all phases will bring efficiency, automation first strategies. Further, our findings suggest Chatbots must provide more personalization, scalability and omni channel engagement and focus on delivering more enhanced customer experience. Chatbots must offer a grievance management dashboard where the customer can see live queries, resolved queries, present queries status and so on to get transparency. Chatbots streamline the lead qualification process, greatly improve, and speed up the data collection therefore, enhancing customer experience.</p>", "Keywords": "Chatbots;content analysis;customer experience;customization;integration", "DOI": "10.1002/cpe.7450", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Delhi School of Management Delhi Technological University  New Delhi India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Delhi School of Management Delhi Technological University  New Delhi India"}], "References": [{"Title": "Customer experiences in the age of artificial intelligence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "106548", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Artificial intelligence in marketing: Systematic review and future research direction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100002", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Modeling the use of voice based assistant devices (VBADs): A machine learning base an exploratory study using cluster analysis and correspondence analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "1", "Page": "100069", "JournalTitle": "International Journal of Information Management Data Insights"}]}, {"ArticleId": 97085689, "Title": "DeepPlayer: An open‐source SignalPlant plugin for deep learning inference", "Abstract": "<h3 > Background and Objective</h3> <p>Machine learning has become a powerful tool in several computation domains. The most progressive way of machine learning, deep learning, has already surpassed several algorithms designed by human experts. It also applies to the field of biomedical signal processing. However, while many experts produce deep learning models, there is no software platform for signal processing, allowing the convenient use of pre-trained deep learning models and evaluating them using any inspected signal. This may also hinder understanding, interpretation, and explanation of results. For these reasons, we designed DeepPlayer. It is a plugin for the free signal processing software SignalPlant. The plugin allows loading deep learning models saved in the Open Neural Network Exchange (ONNX) file format and evaluating them on any given signal.</p> <h3 > Methods</h3> <p>The DeepPlayer plugin and its graphical user interface were designed in C# programming language and the .NET framework. We used the inference library OnnxRuntime, which supports graphics card acceleration. The inference is executed in asynchronous tasks for a live preview and evaluation of the signals. Model outputs can be exported back to SignalPlant for further processing, such as peak detection or thresholding.</p> <h3 > Results</h3> <p>We developed the DeepPlayer plugin to evaluate deep learning models in SignalPlant. The plugin keeps with SignalPlant's interactive work with signals, such as live preview or easy selection of associated signals. The plugin can load classification or regression models and allows standard pre-processing and post-processing methods. We prepared several deep learning models to test the plugin. Additionally, we provide a tutorial training script that outputs an ONNX format model with correctly set metadata information. These, and the source code of the DeepPlayer plugin, are publicly accessible via GitHub and Google Colab service.</p> <h3 > Conclusion</h3> <p>The DeepPlayer plugin allows running deep learning models easily and interactively. Therefore, experts and non-AI experts alike can explore and apply deep learning models for (biomedical) signal processing. Its ease of use and interactivity might also contribute to a better understanding and acceptance of AI methods in biomedicine.</p>", "Keywords": "artificial intelligence;deep learning;signal processing;SignalPlant;software", "DOI": "10.1002/spe.3159", "PubYear": 2023, "Volume": "53", "Issue": "2", "JournalId": 1840, "JournalTitle": "Software: Practice and Experience", "ISSN": "0038-0644", "EISSN": "1097-024X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Scientific Instruments of the Czech Academy of Sciences  Brno Czechia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Scientific Instruments of the Czech Academy of Sciences  Brno Czechia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Scientific Instruments of the Czech Academy of Sciences  Brno Czechia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "KIS*MED (AI Systems in Medicine) Technische Universität Darmstadt  Darmstadt Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Scientific Instruments of the Czech Academy of Sciences  Brno Czechia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Scientific Instruments of the Czech Academy of Sciences  Brno Czechia;Faculty of Electrical Engineering and Communication, Department of Biomedical Engineering Brno University of Technology  Brno Czechia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Institute of Scientific Instruments of the Czech Academy of Sciences  Brno Czechia"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "International Clinical Research Center St. Anne's University Hospital  Brno Czechia"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Card<PERSON>center, Third Faculty of Medicine Charles University and University Hospital Kralovske Vinohrady  Prague Czechia"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "KIS*MED (AI Systems in Medicine) Technische Universität Darmstadt  Darmstadt Germany"}], "References": [{"Title": "RIFLING: A reinforcement learning‐based GPU scheduler for deep learning research and development platforms", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "6", "Page": "1319", "JournalTitle": "Software: Practice and Experience"}]}, {"ArticleId": 97085695, "Title": "Valorphins alter physicochemical characteristics of phosphatidylcholine membranes: Datasets on lipid packing, bending rigidity, specific electrical capacitance, dipole potential, vesicle size", "Abstract": "Endogenous hemorphins are being intensively investigated as therapeutic agents in neuropharmacology, and also as biomarkers in mood regulation, inflammation and oncology. The datasets collected herein report physicochemical parameters of 1-palmitoyl-2-oleoyl-sn-glycero-3-phosphocholine membranes in the presence of VV-hemorphin-5 (Val-Val-Tyr-Pro-Trp-Thr-Gln) and analogues, modified at position 1 and 7 by the natural amino acid isoleucine or the non-proteinogenic 2-aminoisobutyric, 2,3-diaminopropanoic or 2,4-diaminobutanoic amino acids. These peptides have been previously screened for nociceptive activity and were chosen accordingly. The present article contains fluorescence spectroscopy data of Laurdan- and di-8-ANEPPS- labelled large unilamellar vesicles (LUV) providing the degree of hydration and dipole potential of lipid bilayers in the presence of VV-hemorphin-5 analogues. Lipid packing is accessible from Laurdan intensity profiles and generalized polarization datasets reported herein. The data presented on fluorescence intensity ratios of di-8-ANEPPS dye provide dipole potential values of phosphatidylcholine-valorphin membranes. Vesicle size and electrophoretic mobility datasets included refer to the effect of valorphins on the size distribution and ζ -potential of POPC LUVs. Investigation of physicochemical properties of peptides such as diffusion coefficients and heterogeneous rate constant relates to elucidation of transport mechanisms in living cells. Voltammetric data of valorphins are presented together with square-wave voltammograms of investigated peptides for calculation of their heterogeneous electron transfer rate constants. Datasets from the thermal shape fluctuation analysis of quasispherical ‘giant’ unilamellar vesicles (GUV) are provided to quantify the influence of hemorphin incorporation on the membrane bending elasticity. Isothermal titration calorimetric data on the thermodynamics of peptide-lipid interactions and the binding affinity of valorphin analogues to phosphatidylcholine membranes are reported. Data of frequency-dependent deformation of GUVs in alternating electric field are included together with the values of the specific electrical capacitance of POPC-valorphin membranes. The datasets reported in this article can underlie the formulation and implementation of peptide-based strategies in pharmacology and biomedicine.", "Keywords": "Lipid vesicles ; Fluctuation analysis ; Isothermal titration calorimetry ; Voltammetry ; Laurdan ; Di-8-ANEPPS ; Aib 2-aminoisobutyric acid ; АTSF analysis of thermal shape fluctuations ; CV cyclic voltammetry ; Dab 2;4-diaminobutanoic acid ; Dap 2;3-diaminopropanoic acid ; Di-8-ANEPPS 4-(2-[6-(Dioctylamino)-2-naphthalenyl]ethenyl)-1-(3-sulfopropyl)pyridinium inner salt ; DPV differential pulse voltammetry ; Gln glutamine (Q) ; GP generalized polarization ; GUV giant unilamellar vesicle ; Ile isoleucine (I) ; ITC isothermal titration calorimetry ; ITO indium tin oxide ; LUV large unilamellar vesicle ; PC phosphatidylcholine ; PDMS polydimethylsiloxane ; POPC 1-palmitoyl-2-oleoyl- sn -glycero-3-phosphocholine ; Pro proline (P) ; Thr threonine (T) ; Trp tryptophan (W) ; Tyr tyrosine (Y) ; Val valine (V) ; VV-hemorphin-5 Val-Val-Tyr-Pro-Trp-Thr-Gln-NH<sub>2</sub>", "DOI": "10.1016/j.dib.2022.108716", "PubYear": 2022, "Volume": "45", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Solid State Physics, Bulgarian Academy of Sciences, 72 Tzarigradsko Chaussee Blvd., 1784 Sofia, Bulgaria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Biophysics and Biomedical Engineering, Bulgarian Academy of Sciences, Acad. G<PERSON>r., bl. 21, Sofia 1113, Bulgaria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Biophysics and Biomedical Engineering, Bulgarian Academy of Sciences, Acad. G<PERSON>r., bl. 21, Sofia 1113, Bulgaria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Analytical Chemistry, University of Chemical Technology and Metallurgy, 8 Kliment Ohridski Blvd., 1756 Sofia, Bulgaria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, Faculty of Pharmacy, Medical University, 2 Dunav Str. 1000 Sofia, Bulgaria"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Solid State Physics, Bulgarian Academy of Sciences, 72 Tzarigradsko Chaussee Blvd., 1784 Sofia, Bulgaria"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Organic Chemistry, University of Chemical Technology and Metallurgy, 8 Kliment Ohridski Blvd., 1756 Sofia, Bulgaria"}], "References": []}, {"ArticleId": 97085720, "Title": "Exact approaches for the Minimum Subgraph Diameter Problem", "Abstract": "This work addresses the Minimum Subgraph Diameter Problem (MSDP), an NP-hard problem with applications to network design. Given an undirected graph with lengths and costs on the edges, the MSDP’s goal is to find a spanning subgraph with total cost limited by a given budget, such that the subgraph’s diameter is minimum. We propose a Mixed-Integer Linear Programming (MILP) model for the MSDP, along with two families of valid inequalities, which form the basis of the first exact approaches for the MSDP. We present an approach to apply lazy constraints on the MILP and a heuristic to generate upper bounds from fractional solutions. We propose a comprehensive benchmark for the MSDP and conduct a thorough computational study. The results show the effectiveness of each contribution towards reducing optimality gaps and computational times.", "Keywords": "Combinatorial optimization ; Network design ; Integer linear programming ; Branch-and-cut ; Benchmark instances", "DOI": "10.1016/j.cor.2022.106050", "PubYear": 2023, "Volume": "150", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Computing - University of Campinas (Unicamp), Av. <PERSON>, 1251. Cidade Universitária. 13083-852, Campinas, SP, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computing - University of Campinas (Unicamp), Av. <PERSON>, 1251. Cidade Universitária. 13083-852, <PERSON><PERSON>, SP, Brazil;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departament of Computing - Federal University of São Carlos (UFSCar), Rod<PERSON>, Km 235. 13565-905, <PERSON>, SP, Brazil"}], "References": []}, {"ArticleId": 97085731, "Title": "Construction of Winter Wheat Optimal Irrigation Yield Prediction Model Based on DM Algorithm", "Abstract": "The practice shows that there is a close and nonlinear correlation between the greenness and yield of winter wheat. As we all know, the agricultural production system is a complex system with a high degree of uncertainty, including soil fertility grade, climate, domain management and many other factors, which brings difficulties to the prediction of winter wheat yield. This paper studies the construction of winter wheat optimal irrigation yield prediction model based on DM algorithm. In this article, ANN technique is used to fit and describe the relationship between green degree and yield of winter wheat, and a yield model is established. The algorithm used in this paper is BP neural network, which uses physical devices to simulate some organizations and functions of biological neural network, that is, many neurons with simple functions are interconnected to form a network system that can simulate human learning, decision-making, recognition and other functions. The steepest descent means is used to continuously adjust the weight and threshold of the network through back propagation to minimize the sum of squares of errors of the network. Through the research of this paper, the algorithm in this paper is effective and suitable for use.", "Keywords": "Data mining algorithm ; optimize irrigation yield of winter wheat ; prediction model ; BP neural network", "DOI": "10.1016/j.procs.2022.10.037", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Biological and Food Engineering, Huaihua University, Huaihua, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Li", "Affiliation": "Biomedicine and health industry research center , Huaihua University, Huaihua, China"}], "References": []}, {"ArticleId": 97085733, "Title": "Quantum-inspired metaheuristic algorithms: comprehensive survey and classification", "Abstract": "<p>Metaheuristic algorithms are widely known as efficient solutions for solving problems of optimization. These algorithms supply powerful instruments with significant engineering, industry, and science applications. The Quantum-inspired metaheuristic algorithms were developed by integrating Quantum Computing (QC) concepts into metaheuristic algorithms. The QC-inspired metaheuristic algorithms solve combinational and numerical optimization problems to achieve higher-performing results than conventional metaheuristic algorithms. The QC is used more than any other strategy for accelerating convergence, enhancing exploration, and exploitation, significantly influencing metaheuristic algorithms’ performance. The QC is a new field of research that includes elements from mathematics, physics, and computing. QC has attracted increasing attention among scientists, technologists, and industrialists. During the current decade, it has provided a research platform for the scientific, technical, and industrial areas. In QC, metaheuristic algorithms can be improved by the parallel processing feature. This feature helps to find the best solutions for optimization problems. The Quantum-inspired metaheuristic algorithms have been used in the optimization fields. In this paper, a review of different usages of QC in metaheuristics has been presented. This review also shows a classification of the Quantum-inspired metaheuristic algorithms in optimization problems and discusses their applications in science and engineering. This review paper’s main aims are to give an overview and review the Quantum-inspired metaheuristic algorithms applications.</p>", "Keywords": "Metaheuristics; Optimization; Quantum Computing; Quantum-Inspired; Combinatorial", "DOI": "10.1007/s10462-022-10280-8", "PubYear": 2023, "Volume": "56", "Issue": "6", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Gharehchopogh", "Affiliation": "Department of Computer Engineering, Urmia Branch, Islamic Azad University, Urmia, Iran"}], "References": [{"Title": "A sonar image segmentation algorithm based on quantum-inspired particle swarm optimization and fuzzy clustering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "22", "Page": "16775", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A comprehensive survey on symbiotic organisms search algorithms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "2265", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Support vector machines on the D-Wave quantum annealer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "248", "Issue": "", "Page": "107006", "JournalTitle": "Computer Physics Communications"}, {"Title": "Quantum computing based hybrid solution strategies for large-scale discrete-continuous optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "106630", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Diversity-preserving quantum particle swarm optimization for the multidimensional knapsack problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113310", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Quantum based Whale Optimization Algorithm for wrapper feature selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106092", "JournalTitle": "Applied Soft Computing"}, {"Title": "Trade-off between exploration and exploitation with genetic algorithm using a novel selection operator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Quantum-enhanced multiobjective large-scale optimization via parallelism", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "100697", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "An improved opposition based learning firefly algorithm with dragonfly algorithm for solving continuous optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "2", "Page": "309", "JournalTitle": "Intelligent Data Analysis"}, {"Title": "A novel hybrid whale optimization algorithm with flower pollination algorithm for feature selection: Case study Email spam detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "176", "JournalTitle": "Computational Intelligence"}, {"Title": "Quantum particles-enhanced multiple Harris Hawks swarms for dynamic optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114202", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A KNN quantum cuckoo search algorithm applied to the multidimensional knapsack problem", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107077", "JournalTitle": "Applied Soft Computing"}, {"Title": "Quantum inspired Particle Swarm Optimization with guided exploration for function optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107122", "JournalTitle": "Applied Soft Computing"}, {"Title": "Quantum-inspired ensemble approach to multi-attributed and multi-agent decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107283", "JournalTitle": "Applied Soft Computing"}, {"Title": "QANA: Quantum-based avian navigation optimizer algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "104314", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A new quantum particle swarm optimization algorithm for controller placement problem in software-defined networking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "95", "Issue": "", "Page": "107456", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Multiple birth support vector machine based on dynamic quantum particle swarm optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "480", "Issue": "", "Page": "146", "JournalTitle": "Neurocomputing"}, {"Title": "GGWO: Gaze cues learning-based grey wolf optimizer and its applications for solving engineering problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "61", "Issue": "", "Page": "101636", "JournalTitle": "Journal of Computational Science"}, {"Title": "DMDE: Diversity-maintained multi-trial vector differential evolution algorithm for non-decomposition large-scale global optimization", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116895", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-objective stochastic project scheduling with alternative execution methods: An improved quantum-behaved particle swarm optimization approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117029", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Quantum entanglement inspired hard constraint handling for operations engineering optimization with an application to airport shift planning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "117684", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Effective route generation framework using quantum mechanism-based multi-directional and parallel ant colony optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "169", "Issue": "", "Page": "108308", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 97085737, "Title": "Construction of Smart Campus Cloud Service Platform Based on Big Data Computer System", "Abstract": "At present, universities are facing a critical period of transition from digital campus to smart campus. Because cloud service data has the characteristics of diversity, in addition to structured data, there are also unstructured data, which play an important role in the study and life of the majority of students. Therefore, it is very important to improve the construction level of smart campus cloud service platform. At this stage, there are great differences between the service platform under the traditional model and the smart campus platform under the big data computer system. Under the traditional mode, the construction method of smart campus cloud service platform in the city has some shortcomings. Therefore, it is urgent to build a smart campus cloud service platform. Based on this, this paper compares the smart campus cloud service platform of the traditional service platform and the big data computer system. The results show that the latter has a timely response speed and can bring high-quality and efficient data services to users. At the same time, it also analyzes the ideas and goals of building an intelligent cloud service platform, and puts forward corresponding construction methods.", "Keywords": "Big data ; computer system ; smart campus ; cloud service platform", "DOI": "10.1016/j.procs.2022.10.081", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong University of Science & Technology, Dongguan 523083, Guangdong, China"}], "References": []}, {"ArticleId": 97085742, "Title": "Design of Virtual Bass Enhancement Algorithm Based on SVM Algorithm", "Abstract": "The purpose of target detection is to detect and locate a set kind of target object from still pictures or videos. Most of the existing studies have simplified the target detection problem into a binary classification problem. Considering the superiority of support vector machine in pattern recognition, how to apply it to target detection has become the focus of computer vision. The traditional support vector machine is based on two kinds of problems, and how to effectively extend it to multiple kinds is still a problem to be studied. There are many unique advantages in solving small and nonlinear high-dimensional model recognition problems, and these problems can also be applied to other automatic learning problems, such as the attribute algorithm currently used to define models, bec feedback. The processing process of traditional virtual bass enhancement algorithm is analyzed, and it is found that the processing effect of different frequency components in the sound source is not uniform, which has a great influence on the final bass enhancement effect. Therefore, a new virtual bass enhancement algorithm is proposed based on the idea of sound source separation, which can reduce the distortion and improve the bass enhancement effect by separating the transient and steady components of the sound source before bass enhancement.", "Keywords": "SVM algorithm ; virtual bass ; target detection ; sound source separation", "DOI": "10.1016/j.procs.2022.10.034", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Music and Dance , Huaihua University , Huaihua , China"}], "References": []}, {"ArticleId": 97085746, "Title": "Comprehensive Evaluation on Agricultural Modernization Development of Heilongjiang Province Based on Entropy Method and Generalized Least Squares", "Abstract": "Although Heilongjiang Province is the largest commodity grain producing area and modern agricultural reform pilot area, its agricultural modernization still has a long way to go. According to the actual situation of 34 regions in Heilongjiang Province, this paper evaluates the agricultural development of Heilongjiang Province from four main aspects: input level, output level, social development ability and sustainable ability. An index system of four first-level indicators and ten second-level indicators has been established. Analytic hierarchy process method and entropy weight method were used to calculate the weight of each index layer. Secondly, the least square method was used to fit the three-dimensional nonlinear value, and the extreme value standardization method was used to evaluate each index. Finally, based on the longitude and latitude data of 34 regions in Heilongjiang Province, the Moran index is calculated and the comprehensive evaluation results of the development level index of agricultural modernization in each region were given.", "Keywords": "Analytic hierarchy process ; entropy method ; generalized least squares ; moran index", "DOI": "10.1016/j.procs.2022.10.055", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Heilongjiang Bayi Agricultural University , College of Science , Daqing , 163319 , China"}, {"AuthorId": 2, "Name": "<PERSON> yang", "Affiliation": "Heilongjiang Bayi Agricultural University , College of Science , Daqing , 163319 , China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Heilongjiang Bayi Agricultural University , College of Science , Daqing , 163319 , China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "HeilongjiangBayi Agricultural University , College of Economics and Management , Daqing , 163319 , China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Heilongjiang Bayi Agricultural University , College of Science , Daqing , 163319 , China"}], "References": []}, {"ArticleId": 97085748, "Title": "Research on the Design of Rural Tourism E-commerce System Based on Big Data Technology", "Abstract": "The traditional rural tourism industry has some problems, such as incomplete tourism information, unable to share all data, serious data loss, and the decline of tourism service quality. In recent years, with the support of big data and e-commerce technology, many villages have vigorously developed tourism e-commerce, which has brought huge economic benefits to rural residents and improved their living environment to a certain extent. This paper briefly expounds the concept and characteristics of big data, analyzes the needs of rural tourism e-commerce on the basis of being familiar with big data related technologies, develops a rural tourism e-commerce system (hereinafter referred to as RTES), and explains the design of the system in detail from the system architecture design, database design and system function structure design. The application of the system shows that the tourism e-commerce designed and developed in this paper is guaranteed in practicality and security, and reflects professionalism and comprehensiveness in service. It can not only recommend and book the best tourist routes for customers, but also provide support for managers and facilitate management.", "Keywords": "Big data ; rural tourism ; e-commerce system ; system design ; system requirements analysis", "DOI": "10.1016/j.procs.2022.10.058", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Tourism Management , Wuhan Business University , Wuhan 430056 , China"}], "References": []}, {"ArticleId": 97085940, "Title": "A relaxed binary quadratic function negative-determination lemma and its application to neutral systems with interval time-varying delays and nonlinear disturbances", "Abstract": "This paper considers the stability problem of neutral systems with interval time-varying delays and nonlinear disturbances. Firstly, an augmented vector containing two double integral terms is introduced into the Lyapunov-<PERSON> functional (LKF). In this case, a binary quadratic function with discrete and neutral delay arises in the time derivative. To gain the negativity condition of such function, by taking full advantage of the idea of partial differential of the binary quadratic function and <PERSON>'s formula, a relaxed binary quadratic function negative-determination lemma with two adjustable parameters is proposed, which contains the existing lemmas as its special cases and shows the great potential of reducing conservatism for the case where the tangent slope at the endpoint is far from zero. Then, based on the improved lemma, more relaxing stability criteria have been obtained via an augmented LKF. Finally, two classic numerical examples are given to attest the effectiveness and strengths of the obtained stability criteria.", "Keywords": "Stability ; neutral systems ; generalised reciprocally convex combination lemma ; a relaxed binary quadratic function negative-determination lemma", "DOI": "10.1080/00207721.2022.2065705", "PubYear": 2022, "Volume": "53", "Issue": "14", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, China University of Geosciences, Wuhan, People's Republic of China;Hubei Key Laboratory of Advanced Control and Intelligent Automation for Complex Systems, Wuhan, People's Republic of China;Engineering Research Center of Intelligent Technology for Geo-Exploration, Ministry of Education, Wuhan, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Automation, China University of Geosciences, Wuhan, People's Republic of China;Hubei Key Laboratory of Advanced Control and Intelligent Automation for Complex Systems, Wuhan, People's Republic of China;Engineering Research Center of Intelligent Technology for Geo-Exploration, Ministry of Education, Wuhan, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering  Electronics, University of Liverpool, Liverpool, UK"}], "References": []}, {"ArticleId": 97085941, "Title": "Solving multiobjective optimisation problems using genetic algorithms and solutions concepts of cooperative games", "Abstract": "A new methodology is proposed in this paper to solve multiobjective optimisation problems (MOPs) by invoking genetic algorithms and solutions concepts of cooperative games. The weighting problems that are formulated by assigning some suitable weights to the multiple objective functions are usually solved to obtain the Pareto optimal solutions of MOPs. In this paper, the suitable weights will be taken from the core values of a cooperative game that is formulated from the original MOP by regarding the objective functions as players. Under these settings, we shall obtain a large set of all so-called Core-Pareto optimal solutions. In order to choose the best Core-Pareto optimal solution from this set, we shall invoke the genetic algorithms by setting a reasonable fitness function.", "Keywords": "Multiobjective optimisation ; game theory ; genetic algorithms ; Pareto optimal solutions ; core values ; weighting problems", "DOI": "10.1080/00207721.2022.2070793", "PubYear": 2022, "Volume": "53", "Issue": "14", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, National Kaohsiung Normal University, Kaohsiung, Taiwan"}], "References": []}, {"ArticleId": 97085959, "Title": "Elementary Clothoid-Based Three-Dimensional Curve for Unmanned Aerial Vehicles", "Abstract": "", "Keywords": "Unmanned Aerial Vehicle", "DOI": "10.2514/1.G006935", "PubYear": 2022, "Volume": "45", "Issue": "12", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitat Politècnica de València, 46022 València, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitat Politècnica de València, 46022 València, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitat de València, 46100 Burjassot, Spain"}], "References": [{"Title": "Cooperative Docking Guidance and Control with Application to Civil Autonomous Aerial Refueling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>, <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "44", "Issue": "9", "Page": "1638", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Cascaded Incremental Nonlinear Dynamic Inversion for Three-Dimensional Spline-Tracking with Wind Compensation", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "44", "Issue": "8", "Page": "1559", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Computationally Efficient Trajectory Generation for Smooth Aircraft Flight Level Changes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "44", "Issue": "8", "Page": "1532", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}]}, {"ArticleId": 97086111, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0045-7930(22)00309-7", "PubYear": 2022, "Volume": "249", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [], "References": []}, {"ArticleId": 97086183, "Title": "What you don't know can hurt you", "Abstract": "", "Keywords": "", "DOI": "10.12968/S1353-4858(22)70059-1", "PubYear": 2022, "Volume": "2022", "Issue": "10", "JournalId": 12821, "JournalTitle": "Network Security", "ISSN": "1353-4858", "EISSN": "1872-9371", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>"}], "References": []}, {"ArticleId": 97086203, "Title": "The Internet of Autonomous Things applications: A taxonomy, technologies, and future directions", "Abstract": "The Internet of Autonomous Things (IoAT), also known as Autonomous Things (AuT), is a relatively new concept for technological advancements that enable autonomous devices to connect and share information without human intervention. IoAT includes robotics, autonomous vehicles, drones, and smart home devices. Recent progress in deep learning and artificial intelligence (AI) is the foundation for all IoAT applications. Using the systematic literature review (SLR) method, we examined five application domains for IoAT: healthcare, military and security, transportation, aerospace, and home robotics. Analysis shows that 30% of the IoAT application domains are devoted to transportation applications, 21% to military and security applications, 19% to aerospace applications, 16% to healthcare applications, and 14% to home robotics applications. Finally, we discuss the current issues and potential research challenges in the context of IoAT.", "Keywords": "", "DOI": "10.1016/j.iot.2022.100635", "PubYear": 2022, "Volume": "20", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer engineering, Science and Research Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Future Technology Research Center, National Yunlin University of Science and Technology, 123 University Road, Section 3, 64002, Douliou, Yunlin, Taiwan;Corresponding author"}], "References": [{"Title": "PIE: a Tool for Data-Driven Autonomous UAV Flight Testing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "2", "Page": "421", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Neural collision avoidance system for biomimetic autonomous underwater vehicle", "Authors": "<PERSON><PERSON> Praczy<PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "2", "Page": "1315", "JournalTitle": "Soft Computing"}, {"Title": "Self-driving cars: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113816", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Health Monitoring System for Autonomous Vehicles using Dynamic Bayesian Networks for Diagnosis and Prognosis", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Coordination of Autonomous Vehicles", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "The MRS UAV System: Pushing the Frontiers of Reproducible Research, Real-world Deployment, and Education with Autonomous Unmanned Aerial Vehicles", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Secure fusion approach for the Internet of Things in smart autonomous multi-robot systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "579", "Issue": "", "Page": "468", "JournalTitle": "Information Sciences"}, {"Title": "Artificial intelligence in industrial design: A semi-automated literature survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "112", "Issue": "", "Page": "104884", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 97086227, "Title": "Upconversion luminescent sensor for endogenous H2O2 detection in cells based on the inner filter effect of coated silver layer", "Abstract": "We design and construct an upconversion luminescence sensor for hydrogen peroxide detection based on lanthanide-doped nanoparticles (LDN-OA). After modification with an electron-donating ligand, the silver was coated on the surface of LDN-OA in-situ by photo-induced free-electron reduction under the excitation of ultraviolet light. Then, the core-shell structure of upconversion nanoparticles growing with the silver metal surface is formed and modified with polyethylene-glycol, leading to the final hydrogen peroxide biosensor (denoted as [email&#160;protected] ). The sensor is constructed based on the luminescence inner filter effect, using the LDN-OA as an energy donor and the coated silver layer as an energy acceptor. The confocal images were analyzed in vitro as well, presenting that the [email&#160;protected] sensor could achieve the sensing of endogenous hydrogen peroxide under 980 nm excitation. Thus the upconversion luminescence sensor provides a powerful idea for the development of intracellular biomolecule sensors upon near-infrared light irradiation as well.", "Keywords": "Upconversion luminescence sensor ; Lanthanide-doped upconversion nanoparticles ; Hydrogen peroxide detection ; Luminescence inner filter effect", "DOI": "10.1016/j.snb.2022.132936", "PubYear": 2023, "Volume": "376", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Marine Resource Utilization in South China Sea & Special Glass Key Lab of Hainan Province, School of Information and Communication Engineering, Hainan University, Haikou 570228, China;Department of Chemistry, College of Sciences, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 2, "Name": "Yale Hong", "Affiliation": "Department of Chemistry, College of Sciences, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Environmental and Chemical Engineering, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Environmental and Chemical Engineering, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 5, "Name": "Hong Jiang", "Affiliation": "State Key Laboratory of Marine Resource Utilization in South China Sea & Special Glass Key Lab of Hainan Province, School of Information and Communication Engineering, Hainan University, Haikou 570228, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON>ing <PERSON>", "Affiliation": "Department of Chemistry, College of Sciences, Shanghai University, Shanghai 200444, China;Corresponding authors"}], "References": [{"Title": "A facile ratiometric sensing platform based on inner filter effect for hypochlorous acid detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "325", "Issue": "", "Page": "128766", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 97086262, "Title": "A novel automated CNN arrhythmia classifier with memory-enhanced artificial hummingbird algorithm", "Abstract": "Cardiac arrhythmias indicate cardiovascular disease which is the leading cause of mortality worldwide, and can be detected by an electrocardiogram (ECG). Automated deep learning methods have been developed to overcome the disadvantages of manual interpretation by medical experts. The performance of the networks strongly depends on hyperparameter optimization (HPO), and this NP-hard problem is suitable for metaheuristic (MH) methods. In this study, a novel method is proposed for the HPO of a convolutional neural network (CNN) arrhythmia classifier using an MH algorithm. The approach utilizes our variant of an MH method, named the memory-enhanced artificial hummingbird algorithm, which has an additional memory unit that stores the evaluations of the solutions and reduces the computation time significantly. The study also proposes a novel fitness function that considers both the accuracy rate and the total number of parameters of each candidate network. Experiments were conducted on raw ECG samples from the MIT-BIH arrhythmia database. The proposed method was compared with five other MH methods and achieved equal or outperforming results, with classification accuracy reaching 98.87%. The proposed method yielded promising results in finding a high-performing solution with relatively lower complexity.", "Keywords": "", "DOI": "10.1016/j.eswa.2022.119162", "PubYear": 2023, "Volume": "213", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Adana Alparslan Türkeş Science and Technology University, Adana, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Adana Alparslan Türkeş Science and Technology University, Adana, Turkey;Corresponding author"}], "References": [{"Title": "Heart sounds classification using a novel 1-D convolutional neural network with extremely low parameter consumption", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "392", "Issue": "", "Page": "153", "JournalTitle": "Neurocomputing"}, {"Title": "A metaheuristic-driven approach to fine-tune Deep Boltzmann Machines", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "105717", "JournalTitle": "Applied Soft Computing"}, {"Title": "Evolving deep learning architectures for network intrusion detection using a double PSO metaheuristic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "168", "Issue": "", "Page": "107042", "JournalTitle": "Computer Networks"}, {"Title": "Optimizing hyperparameters of deep learning in predicting bus passengers based on simulated annealing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106068", "JournalTitle": "Applied Soft Computing"}, {"Title": "Optimising Convolutional Neural Networks using a Hybrid Statistically-driven Coral Reef Optimisation algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106144", "JournalTitle": "Applied Soft Computing"}, {"Title": "Marine Predators Algorithm: A nature-inspired metaheuristic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "113377", "JournalTitle": "Expert Systems with Applications"}, {"Title": "On hyperparameter optimization of machine learning algorithms: Theory and practice", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}, {"Title": "A novel technique for the detection of myocardial dysfunction using ECG signals based on hybrid signal processing and neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "6", "Page": "4571", "JournalTitle": "Soft Computing"}, {"Title": "A novel multi population based particle swarm optimization for feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "219", "Issue": "", "Page": "106894", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An efficient ECG arrhythmia classification method based on Manta ray foraging optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "115131", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An automatic arrhythmia classification model based on improved Marine Predators Algorithm and Convolutions Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115936", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Hyperparameter optimization of deep CNN classifier for plant species identification using artificial bee colony algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "7", "Page": "8827", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Automatic classification of atrial fibrillation from short single-lead ECG recordings using a Hybrid Approach of Dual Support Vector Machine", "Authors": "Gamal G<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116848", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 97086278, "Title": "Model Research of Visual Report Components", "Abstract": "The existing mainstream report system in the market can meet the daily report generation. However, it is difficult to maintain the system later due to the poor satisfaction of its report personalization and the high front-end and backend dependencies. In order to solve the above problems, this paper studies the development idea of Vue components. The technical characteristics of Vue and the source code of the Axios framework are analyzed, and a visual component model is proposed in combination with the application scenarios and development process. The data transmission method of the components is clarified in the actual application layer, which enhances the control of the page in the front-end component development process. The ability to convert complex reports into the construction of visual components and the dynamic data components to obtain data effectively improves the reporting system&#x27;s development speed and meets the needs of personalized reports.", "Keywords": "Visualization ; reporting system ; Vue ; components ; Axios", "DOI": "10.1016/j.procs.2022.10.066", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Software Engineering Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Software Engineering Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Communication Engineering Chongqing University of Posts and Telecommunications, Chongqing, China"}], "References": [{"Title": "JSON: Data model and query languages", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "101478", "JournalTitle": "Information Systems"}, {"Title": "The comparative analysis of web applications frameworks in the Node.js ecosystem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "", "Page": "42", "JournalTitle": "Journal of Computer Sciences Institute"}]}, {"ArticleId": 97086283, "Title": "Design of Vocal Music Education System Based on VR Technology", "Abstract": "With the popularization of conceptual teaching methods such as quality education and comprehensive education in schools, China has invested more energy in art education. Especially the vocal music education of art education, under this background, its music education management has entered a new stage. Through a series of means to make teaching more scientific and standardized. Music teachers are of high importance in the traditional meaning teaching mode, and most of them teach in person. The traditional teaching mode has a single way and the content is lack of change. In a very short time, teaching will lead to poor information transmission. As a product of the information age, network teaching platform has gradually become a new way for people to obtain knowledge. With the support of modern network technology, the development and Reform in the field of education have reached a qualitative leap. In view of the singleness of traditional vocal music education methods and learning situations and the limitations of learning resources, this paper deeply discusses the influence of constructivism theory on music education based on Moodle platform. Combining music education with the teaching platform based on network environment, taking constructivism theory as the main theoretical basis, using network technology, a music education system based on Moodle platform is designed and developed. It realizes the collection, playback and real-time display of audio segments, and also has the function of vocal music scoring. This design makes vocal education lessons intuitive, interactive, and interesting.", "Keywords": "VR technology ; vocal music ; teaching system ; design", "DOI": "10.1016/j.procs.2022.10.002", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Art, Hubei Polytechnic University, Huangshi, Hubei, 435003, China"}], "References": []}, {"ArticleId": 97086438, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0957-4174(22)02195-9", "PubYear": 2023, "Volume": "212", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [], "References": []}, {"ArticleId": 97086444, "Title": "Internet of Things World: A New Security Perspective", "Abstract": "<p>The Internet of Things (IoT) is changing and transforming how we interact with the physical world. IoT devices have had widespread applications in many fields of production and social living, such as healthcare, energy and industrial automation, and military application, to name a few. While we can’t deny the benefits of IoT, like convenience, accessibility, and efficiency, it is a double-edged sword. The security aspect remains a significant concern in the IoT realm, especially physical attacks since there are abundant channels due to physical effects. Much research focuses on software, network, and cloud security; however, hardware security in these devices has been overlooked. Considering this motivation, in this survey, first, we provide the recent advancement in the physical attack defense techniques and extend the literature to summarize the unified countermeasures that benefit IoT devices to address the footprint and power constraints. We also discuss some open problems that need attention. Further, to defeat the IoT system from advanced hardware attacks, we proposed to use 3D integration as a key enabling IoT platform. 3D technology provides various advantages, such as heterogeneous integration, split manufacturing, and disparate technologies like MEMS sensors, making 3D integration the best choice for IoT platforms.</p>", "Keywords": "Hardware security; Internet of things security; Physical attacks; Side-channel attacks; 3D integrated circuits", "DOI": "10.1007/s42979-022-01443-z", "PubYear": 2023, "Volume": "4", "Issue": "1", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Electrical and Computer Engineering, California State University, Fullerton, Fullerton, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering, California State University, Fullerton, Fullerton, USA"}], "References": [{"Title": "Leveraging Side-Channel Information for Disassembly and Security", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Apostol Vassilev", "PubYear": 2020, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "ACM Journal on Emerging Technologies in Computing Systems"}, {"Title": "RASCv2: Enabling Remote Access to Side-Channels for Mission Critical and IoT Systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems"}]}, {"ArticleId": 97086451, "Title": "Modelling and optimization of distributed heterogeneous hybrid flow shop lot-streaming scheduling problem", "Abstract": "More enterprises are facing rapid and changing market demand and small but frequent orders, and they extend the production capability of manufacturing systems to respond to these changes. This paper studies a distributed heterogeneous hybrid flow shop lot-streaming scheduling problem (DHHFLSP) with the minimization of makespan. In the DHHFLSP, the processing capacity of each factory is different and each job can be split into several sub-lots. These sub-lots are assigned to several non-identical factories. The mixed-integer linear programming model (MILP) of DHHFLSP is established. To solve the DHHFLSP, eighteen constructive heuristics and an iterated local search algorithm (ILS) are designed. In constructive heuristics, the jobs are sorted according to several time-based heuristic rules or they are divided into several groups according to bottleneck stages, and then these jobs are assigned into factories through two NEH-based job assignment rules. In the ILS, the NEH-based heuristic (NEH <sub>after</sub> ) plus Largest medium rule is used to generate an initial solution. Two greedy insertion operators with or without critical factories are adopted to generate the perturbation solutions. Four greedy local search operators are designed to make a deep search. The influence of the parameters and main components are investigated by a comprehensive analysis. The comparisons with several related algorithms on extensive testing instances demonstrate the effectiveness and efficiency of the ILS algorithm.", "Keywords": "Distributed heterogeneous hybrid flow shop ; Lot-streaming scheduling ; Constructive heuristics ; Iterated local search ; Makespan", "DOI": "10.1016/j.eswa.2022.119151", "PubYear": 2023, "Volume": "214", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Electronic Information/School of Artificial Intelligence, Nanjing Normal University, Nanjing, China;College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China;Jiangsu Engineering Research Center on Information Security and Privacy Protection Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Shaanxi Normal University, Xi'an, China;Corresponding author"}, {"AuthorId": 3, "Name": "Dechang Pi", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}], "References": [{"Title": "Efficient multi-objective algorithm for the lot-streaming hybrid flowshop with variable sub-lots", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "", "Page": "100600", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Dynamic shuffled frog-leaping algorithm for distributed hybrid flow shop scheduling with multiprocessor tasks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103540", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Modeling and multi-neighborhood iterated greedy algorithm for distributed hybrid flow shop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105527", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Effective Constructive Heuristic and Metaheuristic for the Distributed Assembly Blocking Flow-shop Scheduling Problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "12", "Page": "4647", "JournalTitle": "Applied Intelligence"}, {"Title": "An improved artificial bee colony algorithm for distributed heterogeneous hybrid flowshop scheduling problem with sequence-dependent setup times", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "106638", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A distributed heterogeneous permutation flowshop scheduling problem with lot-streaming and carryover sequence-dependent setup time", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100804", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Local search-based metaheuristics for the robust distributed permutation flowshop problem", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "107247", "JournalTitle": "Applied Soft Computing"}, {"Title": "Solving Type-2 Fuzzy Distributed Hybrid Flowshop Scheduling Using an Improved Brain Storm Optimization Algorithm", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "4", "Page": "1194", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Multi-objective evolutionary algorithm based on multiple neighborhoods local search for multi-objective distributed hybrid flow shop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115453", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Effective constructive heuristics for distributed no-wait flexible flow shop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "136", "Issue": "", "Page": "105482", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 97086471, "Title": "Smart health analysis system using regression analysis with iterative hashing for IoT communication networks", "Abstract": "Wireless communication systems offer a dynamic infrastructure with efficient data sensing and forwarding services using digital networks and the Internet of Things (IoT). Many schemes have been proposed to cope with a smart communication system by integrating medical devices. However, lowering the processing overheads with efficient utilization of network services are challenging tasks. Thus, this paper presents a smart health analysis system using machine learning techniques for IoT network, which aims to handle big data with balancing the communication load for green technologies. Firstly, using regression prediction, the proposed system offers quality-aware services, secondly, by exploring intelligent methods it provides a delay-tolerant scheme to give the least overhead communication paradigm using mobile agents. Finally, the big data is secured using cryptographic techniques and collaborative devices to maintain its trustworthiness with the cloud. The proposed system has revealed a noteworthy performance in terms of network parameters against existing studies.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108456", "PubYear": 2022, "Volume": "104", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence and Data Analytics Lab (AIDA) CCIS Prince Sultan University, Riyadh 11586, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence and Data Analytics Lab (AIDA) CCIS Prince Sultan University, Riyadh 11586, Saudi Arabia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Islamia College Peshawar, Peshawar 25120, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science (IOT), <PERSON> Institute of Technology, 5KM Stone Delhi, Meerut Rd, near Raj Nagar Extension Road, Ghaziabad, Uttar Pradesh, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence and Data Analytics Lab (AIDA) CCIS Prince Sultan University, Riyadh 11586, Saudi Arabia;Department of Embedded Systems Engineering, College of Information Technology, Incheon National University, Incheon 22012, South Korea"}], "References": [{"Title": "An accurate and dynamic predictive model for a smart M-Health system using machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "538", "Issue": "", "Page": "486", "JournalTitle": "Information Sciences"}, {"Title": "Trust aggregation authentication protocol using machine learning for IoT wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "91", "Issue": "", "Page": "107130", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 97086560, "Title": "Semi-supervised transformable architecture search for feature distillation", "Abstract": "<p>The designed method aims to perform image classification tasks efficiently and accurately. Different from the traditional CNN-based image classification methods, which are greatly affected by the number of labels and the depth of the network. Although the deep network can improve the accuracy of the model, the training process is usually time-consuming and laborious. We explained how to use only a few of labels, design a more flexible network architecture and combine feature distillation method to improve model efficiency while ensuring high accuracy. Specifically, we integrate different network structures into independent individuals to make the use of network structures more flexible. Based on knowledge distillation, we extract the channel features and establish a feature distillation connection from the teacher network to the student network. By comparing the experimental results with other related popular methods on commonly used data sets, the effectiveness of the method is proved. The code can be found at https://github.com/ZhangXinba/Semi_FD.</p>", "Keywords": "Semi-supervised; Feature distillation; Transformable architecture search; Joint loss", "DOI": "10.1007/s10044-022-01122-y", "PubYear": 2023, "Volume": "26", "Issue": "2", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 7, "Name": "Hancheng Zhu", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Guanglian Technology Co.,Ltd, Xuzhou, China"}], "References": [{"Title": "Multi-teacher knowledge distillation for compressed video action recognition based on deep learning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "101695", "JournalTitle": "Journal of Systems Architecture"}]}, {"ArticleId": 97086870, "Title": "Analytical Gradient-Guided Nonlinear Programming Approach for Multitarget Rendezvous Mission", "Abstract": "", "Keywords": "", "DOI": "10.2514/1.G006967", "PubYear": 2023, "Volume": "46", "Issue": "3", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xi’an Satellite Control Center, 710043 Xi’an, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National University of Defense Technology, 410073 Changsha, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Xi’an Satellite Control Center, 710043 Xi’an, People’s Republic of China"}], "References": [{"Title": "Fast Estimation of Perturbed Impulsive Rendezvous via Semi-Analytical Equality-Constrained Optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "12", "Page": "2383", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Optimization for Multitarget, Multispacecraft Impulsive Rendezvous Considering J2 Perturbation", "Authors": "<PERSON><PERSON>; Chen, <PERSON>; Fanghua Jiang", "PubYear": 2021, "Volume": "44", "Issue": "10", "Page": "1811", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Global Optimization of Multiple-Spacecraft Rendezvous Mission via Decomposition and Dynamics-Guide Evolution Approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "45", "Issue": "1", "Page": "171", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}]}, {"ArticleId": 97086884, "Title": "Possibility Extent and Possible Alternatives Preorder Type-2 Fuzzy Analytical Hierarchy Process (PE&PAP-AHP) to improve pharmaceutical R&D productivity", "Abstract": "The major challenges of the pharmaceutical industry are optimizing global expenditure on medicines and maximizing the effectiveness of pharmaceutical products while minimizing the Research and Development (R&amp;D) costs. The commercial success rate mainly causes the decline in R&amp;D productivity across the pharmaceutical industry, the discovery of new drugs, compliance with regulations, and the R&amp;D cycle time. The lack of comprehensive Multi-Criteria Decision Making (MCDM) methodologies leads to ineffective decisions for setting priorities to improve the productivity of pharmaceutical R&amp;D. To increase the precision and accuracy required to improve productivity in Pharmaceutical R&amp;D, in this study, Possibility Extent and Possible Alternatives Preorder Type-2 Fuzzy Analytical Hierarchy Process (PE&amp;PAP-AHP) type-2 fuzzy logic MCDM methodology is developed. Specifically, the type-2 fuzzy geometric mean of PE&amp;PAP-AHP methodology addresses the weakness of traditional methods and correctly reflects the subtle differences in evaluating decision-makers’ opinions. It also constructs fuzzy positive reciprocal matrices, acknowledging the indifference existing among alternatives. PE&amp;PAP-AHP also makes it more accurate during the defuzzification stage of solving the multi-criteria decision-making problem than the type-1 fuzzy approach. It is achieved using two principles: (I) ranking the alternatives by a type-2 fuzzy partial preorder and (II) ranking the alternatives by a type-2 fuzzy total preorder. To compare the results with a type-1 fuzzy, based on the results of the PE&amp;PAP-AHP methodology, the score of the best alternative is 5.2% more than the second-best alternative, whereas, per the type-1 fuzzy, the score of the best alternative is only 3.6% more than the second-best alternative.", "Keywords": "", "DOI": "10.1016/j.asoc.2022.109770", "PubYear": 2022, "Volume": "131", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Systems Science and Industrial Engineering, State University of New York at Binghamton, Binghamton, NY 13902, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Systems Science and Industrial Engineering, State University of New York at Binghamton, Binghamton, NY 13902, USA"}], "References": [{"Title": "AHP integrated TOPSIS and VIKOR methods with Pythagorean fuzzy sets to prioritize risks in self-driving vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "106948", "JournalTitle": "Applied Soft Computing"}, {"Title": "Interval type-2 fuzzy programming method for risky multicriteria decision-making with heterogeneous relationship", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "584", "Issue": "", "Page": "184", "JournalTitle": "Information Sciences"}, {"Title": "An efficiency-based interval type-2 fuzzy multi-criteria group decision making for makeshift hospital selection", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "108243", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 97086885, "Title": "A Survey on ensemble learning under the era of deep learning", "Abstract": "<p>Due to the dominant position of deep learning (mostly deep neural networks) in various artificial intelligence applications, recently, ensemble learning based on deep neural networks (ensemble deep learning) has shown significant performances in improving the generalization of learning system. However, since modern deep neural networks usually have millions to billions of parameters, the time and space overheads for training multiple base deep learners and testing with the ensemble deep learner are far greater than that of traditional ensemble learning. Though several algorithms of fast ensemble deep learning have been proposed to promote the deployment of ensemble deep learning in some applications, further advances still need to be made for many applications in specific fields, where the developing time and computing resources are usually restricted or the data to be processed is of large dimensionality. An urgent problem needs to be solved is how to take the significant advantages of ensemble deep learning while reduce the required expenses so that many more applications in specific fields can benefit from it. For the alleviation of this problem, it is essential to know about how ensemble learning has developed under the era of deep learning. Thus, in this article, we present discussions focusing on data analyses of published works, methodologies, recent advances and unattainability of traditional ensemble learning and ensemble deep learning. We hope this article will be helpful to realize the intrinsic problems and technical challenges faced by future developments of ensemble learning under the era of deep learning.</p>", "Keywords": "", "DOI": "10.1007/s10462-022-10283-5", "PubYear": 2023, "Volume": "56", "Issue": "6", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institution of Clinical Pathology, West China Hospital, Sichuan University, Chengdu, China"}, {"AuthorId": 2, "Name": "Haijun Lv", "Affiliation": "AIDP, Baidu Co., Ltd, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Information, Xi’an Polytechnic University, Xi’an, China"}], "References": [{"Title": "RETRACTED ARTICLE: A novel deep learning-based multi-model ensemble method for the prediction of neuromuscular disorders", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "11083", "JournalTitle": "Neural Computing and Applications"}, {"Title": "RETRACTED ARTICLE: Automatic detection of lung cancer from biomedical data set using discrete AdaBoost optimized ensemble learning generalized neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "3", "Page": "777", "JournalTitle": "Neural Computing and Applications"}, {"Title": "FTBME: feature transferring based multi-model ensemble", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "18767", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A survey of the recent architectures of deep convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "5455", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Detecting helicobacter pylori in whole slide images via weakly supervised multi-task learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "35-36", "Page": "26787", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Survey on Deep Neural Networks in Speech and Vision Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "417", "Issue": "", "Page": "302", "JournalTitle": "Neurocomputing"}, {"Title": "Local minima found in the subparameter space can be effective for ensembles of deep convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107582", "JournalTitle": "Pattern Recognition"}, {"Title": "Ensemble deep learning in bioinformatics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "9", "Page": "500", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "An effective ensemble deep learning framework for text classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "10", "Page": "8825", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Design of deep ensemble classifier with fuzzy decision method for biomedical image classification", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "108178", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 97086936, "Title": "Judicial knowledge-enhanced magnitude-aware reasoning for numerical legal judgment prediction", "Abstract": "<p>Legal Judgment Prediction (LJP) is an essential component of legal assistant systems, which aims to automatically predict judgment results from a given criminal fact description. As a vital subtask of LJP, researchers have paid little attention to the numerical LJP, i.e., the prediction of imprisonment and penalty. Existing methods ignore numerical information in the criminal facts, making their performances far from satisfactory. For instance, the amount of theft varies, as do the prison terms and penalties. The major challenge is how the model can obtain the ability of numerical comparison and magnitude perception, e.g., 400 < 500 < 800, 500 is closer to 400 than to 800. To this end, we propose a judicial knowledge-enhanced magnitude-aware reasoning architecture, called NumLJP, for the numerical LJP task. Specifically, we first implement a contrastive learning-based judicial knowledge selector to distinguish confusing criminal cases efficiently. Unlike previous approaches that employ the law article as external knowledge, judicial knowledge is a quantitative guideline in real scenarios. It contains many numerals (called anchors) that can construct a reference frame. Then we design a masked numeral prediction task to help the model remember these anchors to acquire legal numerical commonsense from the selected judicial knowledge. We construct a scale-based numerical graph using the anchors and numerals in facts to perform magnitude-aware numerical reasoning. Finally, the representations of fact description, judicial knowledge, and numerals are fused to make decisions. We conduct extensive experiments on three real-world datasets and select several competitive baselines. The results demonstrate that the macro-F1 of NumLJP improves by at least 9.53% and 11.57% on the prediction of penalty and imprisonment, respectively.</p>", "Keywords": "Numerical legal judgment prediction; Masked numeral prediction; Judicial knowledge; Magnitude-ware; Numerical reasoning", "DOI": "10.1007/s10506-022-09337-4", "PubYear": 2023, "Volume": "31", "Issue": "4", "JournalId": 437, "JournalTitle": "Artificial Intelligence and Law", "ISSN": "0924-8463", "EISSN": "1572-8382", "Authors": [{"AuthorId": 1, "Name": "<PERSON>g Bi", "Affiliation": "School of Computer Science and Engineering, Southeast University, Nanjing, China; Judicial Big Data Research Centre, School of Law, Southeast University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 3, "Name": "Lu Pan", "Affiliation": "Tencent Technology (Shenzhen) Co., Ltd., Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Southeast University, Nanjing, China"}], "References": [{"Title": "Rank consistent ordinal regression for neural networks with application to age estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "325", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Text Data Augmentation for Deep Learning", "Authors": "<PERSON>; <PERSON><PERSON>; Bork<PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "101", "JournalTitle": "Journal of Big Data"}]}, {"ArticleId": 97086994, "Title": "Lead–lag detection and network clustering for multivariate time series with an application to the US equity market", "Abstract": "<p>In multivariate time series systems, it has been observed that certain groups of variables partially lead the evolution of the system, while other variables follow this evolution with a time delay; the result is a lead–lag structure amongst the time series variables. In this paper, we propose a method for the detection of lead–lag clusters of time series in multivariate systems. We demonstrate that the web of pairwise lead–lag relationships between time series can be helpfully construed as a directed network, for which there exist suitable algorithms for the detection of pairs of lead–lag clusters with high pairwise imbalance. Within our framework, we consider a number of choices for the pairwise lead–lag metric and directed network clustering model components. Our framework is validated on both a synthetic generative model for multivariate lead–lag time series systems and daily real-world US equity prices data. We showcase that our method is able to detect statistically significant lead–lag clusters in the US equity market. We study the nature of these clusters in the context of the empirical finance literature on lead–lag relations, and demonstrate how these can be used for the construction of predictive financial signals.</p>", "Keywords": "High-dimensional time series; Unsupervised learning; Lead–lag; Clustering; Financial markets; Directed networks; Flow imbalance", "DOI": "10.1007/s10994-022-06250-4", "PubYear": 2022, "Volume": "111", "Issue": "12", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics, University of Oxford, Oxford, UK; The Alan Turing Institute, London, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics, University of Oxford, Oxford, UK; The Alan Turing Institute, London, UK; Mathematical Institute, University of Oxford, Oxford, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, University of Oxford, Oxford, UK; The Alan Turing Institute, London, UK"}], "References": []}, {"ArticleId": 97087072, "Title": "Multiview subspace clustering via low‐rank correlation analysis", "Abstract": "In order to explore multi‐view data, existing low‐rank‐based multi‐view subspace clustering methods seek a common low‐rank structure from different views. However, in real‐world scenarios, each view will often hold complex structures resulting from noise or outliers, causing unreliable and imprecise graphs, which the previous methods cannot effectively ameliorate. This study proposes a new method based on low‐rank correlation analysis to overcome these limitations. Firstly, the canonical correlation analysis strategy is introduced to jointly find the low‐rank structures in different views. In order to facilitate a robust solution, a dual regularisation term is further introduced to find such low‐rank structures that maximise the correlation in respective views much better. Thus, a unifying clustering structure is then integrated into the model to characterise the connections between different views adaptively. In this way, noise suppression is achieved more effectively. Furthermore, we avoid the uncertainty of spectral post‐processing of the unifying clustering structure by imposing a rank constraint on its Laplacian matrix to obtain the clustering results explicitly, further enhancing computation efficiency. Experimental results obtained from several clustering and classification experiments performed using 3Sources, Caltech101‐20, 100leaves, WebKB, and Hdigit datasets reveal the proposed method's superiority over compared state‐of‐the‐art methods in Accuracy, Normalised Mutual Information, and F‐score evaluation metrics.", "Keywords": "", "DOI": "10.1049/cvi2.12155", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 11350, "JournalTitle": "IET Computer Vision", "ISSN": "1751-9632", "EISSN": "1751-9640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Jingjiang College Jiangsu University  Zhenjiang Jiangsu China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Communication Engineering JiangSu University  Zhenjiang JiangSu China;Department of Computer Science University of Nigeria  Nsukka Nigeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Communication Engineering JiangSu University  Zhenjiang JiangSu China"}], "References": [{"Title": "Robust multi-view clustering via inter-and-intra-view low rank fusion", "Authors": "<PERSON><PERSON>; Yan <PERSON>; Hanjiang Lai", "PubYear": 2020, "Volume": "385", "Issue": "", "Page": "220", "JournalTitle": "Neurocomputing"}, {"Title": "Multiview Common Subspace Clustering via Coupled Low Rank Representation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Robust low-rank representation via residual projection for image classification", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "241", "Issue": "", "Page": "108230", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-view unsupervised feature selection with tensor low-rank minimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "487", "Issue": "", "Page": "75", "JournalTitle": "Neurocomputing"}, {"Title": "Robust Spectral Clustering via Low-Rank Sample Representation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Applied Computational Intelligence and Soft Computing"}]}, {"ArticleId": 97087156, "Title": "Research on IPMSM Control Based on MTPA", "Abstract": "In view of the lack of the role of reluctance torque in the electromagnetic torque output of the motor under the previous control logic with id=0, only the excitation torque component is working. Therefore, on the premise of inputting unit current, the output electromagnetic torque cannot reach the maximum value, resulting in low energy utilization, reduced load capacity and low operation efficiency. The maximum torque control of unit current output is proposed to control id to pursue the maximum torque. This control method studies the mathematical model of the Interior Permanent Magnet Synchronous Motor (IPMSM) in the rotating d-q-O coordinate system, and establishes the relationship between the current and torque of IPMSM through mathematical formula derivation, so as to realize the Maximum Torque Per Ampere (MTPA) control of the IPMSM. Through Simulink simulation, compared with the previous control logic with id=0, when MTPA control logic outputs the same electromagnetic torque value, the stator current value of IPMSM is smaller and the energy utilization rate is the highest, thus improving the operation efficiency.", "Keywords": "Interior Permanent Magnet Synchronous Motor (IPMSM) ; Maximum Torque Per Ampere (MTPA) ; vector control ; current limit circle", "DOI": "10.1016/j.procs.2022.10.087", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Shanghai DianJi University, School of Electrical Engineering, Shanghai, 201306, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Shanghai DianJi University, School of Electrical Engineering, Shanghai, 201306, China;<PERSON><PERSON><PERSON> new motor electric control research institute, Foshan, 528500, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Shanghai DianJi University, School of Electrical Engineering, Shanghai, 201306, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shanghai DianJi University, School of Electrical Engineering, Shanghai, 201306, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Shanghai DianJi University, School of Electrical Engineering, Shanghai, 201306, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Shanghai DianJi University, School of Electrical Engineering, Shanghai, 201306, China"}], "References": []}, {"ArticleId": 97087239, "Title": "NFVLearn: A multi‐resource, long short‐term memory‐based virtual network function resource usage prediction architecture", "Abstract": "<p>Virtual resource load prediction in network function virtualization (NFV) is the subject of intense research due to its crucial role in enabling proactive resource adaptation in dynamic NFV environments whose resource demand constantly changes. Several long short-term memory (LSTM)-based approaches have been proposed to forecast the resource load of multiple resource attributes of a virtual network function (VNF) in a service function chain (SFC). In this article, we present NFVLearn, a flexible multivariate, many-to-many LSTM-based model which uses different types of resource load history (CPU, memory, I/O bandwidth) from various VNFs of an SFC to predict future loads of multiple resources of a VNF. We then compare four novel automated input selection frameworks for NFVLearn. Simulations on those frameworks based on graph neural networks, Pearson correlation coefficient, <PERSON><PERSON><PERSON> rank correlation coefficient, and <PERSON> rank correlation coefficient demonstrate that models using lesser, highly correlated input features retain high prediction root mean squared error accuracy and coefficients of determination scores by leveraging resource attribute inter-dependencies from the SFC. Those results show that resource attribute interdependency-based input feature selection frameworks can reduce overhead in the control plane while keeping high accuracy and high fidelity resource load prediction of multiple resource attributes.</p>", "Keywords": "correlation coefficients;GNN;input feature selection;LSTM;network function virtualization;resource usage prediction", "DOI": "10.1002/spe.3160", "PubYear": 2023, "Volume": "53", "Issue": "3", "JournalId": 1840, "JournalTitle": "Software: Practice and Experience", "ISSN": "0038-0644", "EISSN": "1097-024X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>Onge", "Affiliation": "Department of Software and Information Technology Engineering, École de technologie supérieure Université du Québec  Montréal Quebec Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Software and Information Technology Engineering, École de technologie supérieure Université du Québec  Montréal Quebec Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>sson Canada  Montréal Quebec Canada"}], "References": [{"Title": "A new optimization algorithm for non-stationary time series prediction based on recurrent neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "738", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Proposal and Investigation of an Artificial Intelligence (AI)-Based Cloud Resource Allocation Algorithm in Network Function Virtualization Architectures", "Authors": "<PERSON>; <PERSON>; Tiziana <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "11", "Page": "196", "JournalTitle": "Future Internet"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "Application of a Long Short Term Memory neural predictor with asymmetric loss function for the resource allocation in NFV network architectures", "Authors": "<PERSON>; <PERSON>; Tiziana <PERSON>", "PubYear": 2021, "Volume": "193", "Issue": "", "Page": "108104", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 97087269, "Title": "Rethinking endpoint management for the modern age", "Abstract": "<p>Four decades after the first PC rolled off the production line, today's systems are an order of magnitude faster and more functional than the originals. But how vulnerable are they to attack?</p>", "Keywords": "", "DOI": "10.12968/S1353-4858(22)70060-8", "PubYear": 2022, "Volume": "2022", "Issue": "10", "JournalId": 12821, "JournalTitle": "Network Security", "ISSN": "1353-4858", "EISSN": "1872-9371", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Adaptiva"}], "References": []}, {"ArticleId": 97087279, "Title": "The threat from Russia continues to loom large", "Abstract": "<p>As the threat from cyber attacks originating from Russia continues to grow, every nation must remain vigilant and learn to adapt and respond quickly to the evolving strategies of Russian cyber attackers.</p>", "Keywords": "", "DOI": "10.12968/S1353-4858(22)70061-X", "PubYear": 2022, "Volume": "2022", "Issue": "10", "JournalId": 12821, "JournalTitle": "Network Security", "ISSN": "1353-4858", "EISSN": "1872-9371", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cylera"}], "References": []}, {"ArticleId": 97087296, "Title": "An Approach For Unity Field Theory With New Quantumvacuum Energy Aether Concept", "Abstract": "Theories of relativity and quantum physics are basing on some complex abstract mathematical formalism which can be quite confusing and not always satisfactory concerning the interpretation of observed corresponding physical effects. For a revised approach of them, we present a new quantum vacuum energy Aether concept we will discuss qualitatively avoiding some abstract mathematical formalism often far from intuitive physical common sense. This is essentially concerning: the relative velocity of light and the Aether disprove, the particle/wave duality, the single particle interference quantum phenomena, the generalized confusion between absolute quantum determinism and undetermined probabilistic measurements of quantum states and the paradoxical possibility for a same quantum system to have at same time different states. Notwithstanding different suggested flaws about the principle of energy and impulse conservation and concerning the optical doppler effect and the postulate of an absolute light speed limit which is contradicted with some observation of superluminal particle transportation velocity. Paradoxical aspects of both theories of the quantum mechanics and theory of relativity are suggested to be sorted out with the rehabilitation of an absolute space reference and a newly defined Aether concept, basing on the equivalence between density of mass and density of quantum vacuum energy. Different aspects of the Field Theory and wave /mass /electric particles interactions are suggested to be interpreted in terms of Aether hydrodynamical energy displacement, fluctuation and waves. Electric charge and mass of particles and their kinetic energy are described in terms of energy of an associated Aether 3D transversal and/or longitudinal wave-packet moving with its group velocity in form of Aether vortices. This model is expected to sort out contradiction about different quantum phenomena and to open the way for new description and interpretation of corresponding new experimental results.", "Keywords": "", "DOI": "10.47363/JMCA/2022(1)106", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 98742, "JournalTitle": "Journal of Mathematical & Computer Applications", "ISSN": "2754-6705", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Independent Consultant, F-60330 Le Plessis Belleville, France"}], "References": []}, {"ArticleId": 97087392, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0167-6911(22)00183-9", "PubYear": 2022, "Volume": "169", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [], "References": []}, {"ArticleId": 97087463, "Title": "Safely catching aerial micro-robots in mid-air using an open-source aerial robot with soft gripper", "Abstract": "<p>This work focuses on catching safely an aerial micro-robot in mid-air using another aerial robot that is equipped with a universal soft gripper. To avoid aerodynamic disturbances such as downwash, that would push the target robot away, we follow a horizontal grasping approach. To this end, the article introduces a gripper design based on soft actuators that can stay horizontally straight with a single fixture and maintain sufficiently compliance in order to bend when air pressure is applied. Further, we develop the Soft Aerial Gripper (SoAG), an open-source aerial robot equipped with the developed soft end-effector and that features an onboard pneumatic regulation system. Experimental results show that the developed low-cost soft gripper has fast opening and closing responses despite being powered by lightweight air pumps, responses that are comparable to those of a commercially available end-effector tested we test against. Static grasping tests study the soft gripper’s robustness in capturing aerial micro-robots under aerodynamic disturbances. We experimentally demonstrated the feasibility of using the SoAG robot to catch a hovering micro-robot with or without propeller guards. The feasibility of dynamic catching is also shown by capturing a moving aerial micro-robot with a velocity of 0.2 m/s. The free flight performance of the SoAG robot is studied against a conventional quadrotor and in different gripper and payload status.</p>", "Keywords": "aerial systems; Biologically-Inspired Robots; soft robots; grasping; Manipulation planning; Field robots", "DOI": "10.3389/frobt.2022.1030515", "PubYear": 2022, "Volume": "9", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, United States"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, United States"}], "References": [{"Title": "Design and integration of a drone based passive manipulator for capturing flying targets", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "7", "Page": "2349", "JournalTitle": "Robotica"}, {"Title": "On Aerial Robots with Grasping and Perching Capabilities: A Comprehensive Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "405", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 97087564, "Title": "Special Section on Analysis and control design for neurodynamics", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.arcontrol.2022.09.007", "PubYear": 2022, "Volume": "54", "Issue": "", "JournalId": 3642, "JournalTitle": "Annual Reviews in Control", "ISSN": "1367-5788", "EISSN": "1872-9088", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of California, Riverside, CA 92507, United States;Department of Information Technology, Uppsala University, PO Box 337, 75105, Uppsala, Sweden;Corresponding editor"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of California, Riverside, CA 92507, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of California, Riverside, CA 92507, United States"}], "References": []}, {"ArticleId": 97087723, "Title": "Research on Escalator Data Acquisition and Transmission Based on Big Data Platform", "Abstract": "In order to collect the operation data of escalator for fault diagnosis, the fault tree analysis method is firstly applied to obtain the occurrence probability of the intermediate event and the bottom events of escalator fault. Then the importance degree of each event is calculated and the four most important components are selected based on the importance degree. For collecting the operation data of these important components, vibration sensor is selected to obtain their acceleration data and store the data locally through Ethernet. In order to meet the characteristics of large data volume and fast data growth and make the transmission platform have the characteristics of large throughput and distributed expansion, Flume, Kafka and Flink components are used to build the big data processing platform for the operation data transmission and pre-processing. The result shows that the platform can complete the data acquisition of the main components of the escalator.", "Keywords": "Escalator ; fault tree ; data acquisition ; flume ; kafka ; flink", "DOI": "10.1016/j.procs.2022.10.073", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Science & Technology, No.200 Xiaolingwei Street, Xuanwu District, Nanjing 210094, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Science & Technology, No.200 Xiaolingwei Street, Xuanwu District, Nanjing 210094, China"}, {"AuthorId": 3, "Name": "Xiyang Jiang", "Affiliation": "Special equipment safety supervision inspection institute of Jiangsu province, Gulou District, Nanjing 210036, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Science & Technology, No.200 Xiaolingwei Street, Xuanwu District, Nanjing 210094, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Science & Technology, No.200 Xiaolingwei Street, Xuanwu District, Nanjing 210094, China"}], "References": [{"Title": "Real-Time Weather Analytics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "4", "Page": "15", "JournalTitle": "International Journal of Web Services Research"}]}, {"ArticleId": 97088123, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1367-5788(22)00127-4", "PubYear": 2022, "Volume": "54", "Issue": "", "JournalId": 3642, "JournalTitle": "Annual Reviews in Control", "ISSN": "1367-5788", "EISSN": "1872-9088", "Authors": [], "References": []}, {"ArticleId": 97088287, "Title": "FeaSel-Net: A Recursive Feature Selection Callback in Neural Networks", "Abstract": "<p>Selecting only the relevant subsets from all gathered data has never been as challenging as it is in these times of big data and sensor fusion. Multiple complementary methods have emerged for the observation of similar phenomena; oftentimes, many of these techniques are superimposed in order to make the best possible decisions. A pathologist, for example, uses microscopic and spectroscopic techniques to discriminate between healthy and cancerous tissue. Especially in the field of spectroscopy in medicine, an immense number of frequencies are recorded and appropriately sized datasets are rarely acquired due to the time-intensive measurements and the lack of patients. In order to cope with the curse of dimensionality in machine learning, it is necessary to reduce the overhead from irrelevant or redundant features. In this article, we propose a feature selection callback algorithm (FeaSel-Net) that can be embedded in deep neural networks. It recursively prunes the input nodes after the optimizer in the neural network achieves satisfying results. We demonstrate the performance of the feature selection algorithm on different publicly available datasets and compare it to existing feature selection methods. Our algorithm combines the advantages of neural networks’ nonlinear learning ability and the embedding of the feature selection algorithm into the actual classifier optimization.</p>", "Keywords": "classification algorithms; dimensionality reduction; feature selection; linear discriminant analysis; machine learning; neural networks; principal component analysis classification algorithms ; dimensionality reduction ; feature selection ; linear discriminant analysis ; machine learning ; neural networks ; principal component analysis", "DOI": "10.3390/make4040049", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 51955, "JournalTitle": "Machine Learning and Knowledge Extraction", "ISSN": "", "EISSN": "2504-4990", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Applied Optics, University of Stuttgart, 70569 Stuttgart, Germany; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Applied Optics, University of Stuttgart, 70569 Stuttgart, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for System Dynamics, University of Stuttgart, 70563 Stuttgart, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Applied Optics, University of Stuttgart, 70569 Stuttgart, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute for System Dynamics, University of Stuttgart, 70563 Stuttgart, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Applied Optics, University of Stuttgart, 70569 Stuttgart, Germany"}], "References": []}, {"ArticleId": 97088334, "Title": "The Role of Machine Learning in Tribology: A Systematic Review", "Abstract": "<p>The machine learning (ML) approach, motivated by artificial intelligence (AI), is an inspiring mathematical algorithm that accurately simulates many engineering processes. Machine learning algorithms solve nonlinear and complex relationships through data training; additionally, they can infer previously unknown relationships, allowing for a simplified model and estimation of hidden data. Unlike other statistical tools, machine learning does not impose process parameter restrictions and yields an accurate association between input and output parameters. Tribology is a branch of surface science concerned with studying and managing friction, lubrication, and wear on relatively interacting surfaces. While AI-based machine learning approaches have been adopted in tribology applications, modern tribo-contact simulation requires a deliberate decomposition of complex design challenges into simpler sub-threads, thereby identifying the relationships between the numerous interconnected features and processes. Numerous studies have established that artificial intelligence techniques can accurately model tribological processes and their properties based on various process parameters. The primary objective of this review is to conduct a thorough examination of the role of machine learning in tribological research and pave the way for future researchers by providing a specific research direction. In terms of future research directions and developments, the expanded application of artificial intelligence and various machine learning methods in tribology has been emphasized, including the characterization and design of complex tribological systems. Additionally, by combining machine learning methods with tribological experimental data, interdisciplinary research can be conducted to understand efficient resource utilization and resource conservation better. At the conclusion of this article, a detailed discussion of the limitations and future research opportunities associated with implementing various machine learning algorithms in tribology and its interdisciplinary fields is presented.</p>", "Keywords": "Machine learning; Tribology; Friction; Wear; Lubrication; Review", "DOI": "10.1007/s11831-022-09841-5", "PubYear": 2023, "Volume": "30", "Issue": "2", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, CVR College of Engineering, Hyderabad, 501510, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, CVR College of Engineering, Hyderabad, 501510, India"}, {"AuthorId": 3, "Name": "N<PERSON> S<PERSON>", "Affiliation": "School of Materials Science and Engineering, Gyeongsang National University, 501 Jinju‑daero, Jinju, 52828, South Korea"}], "References": [{"Title": "Neural network-based adaptive funnel sliding mode control for servo mechanisms with friction compensation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "377", "Issue": "", "Page": "16", "JournalTitle": "Neurocomputing"}, {"Title": "Machine learning-based wear fault diagnosis for marine diesel engine by fusing multiple data-driven models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "190", "Issue": "", "Page": "105324", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Analysing wear behaviour of Al–CaCO3 composites using ANN and Sugeno-type fuzzy inference systems", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "17", "Page": "13453", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Hierarchical convolutional neural network via hierarchical cluster validity based visual tree learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "409", "Issue": "", "Page": "408", "JournalTitle": "Neurocomputing"}, {"Title": "Applications of Artificial Intelligence in Oil and Gas Development", "Authors": "Hong Li; <PERSON><PERSON> Yu; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "937", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Machine learning based multi-index prediction of aviation turbulence over the Asia-Pacific", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "", "Page": "100008", "JournalTitle": "Machine Learning with Applications"}, {"Title": "Trends of Multimodal Neural Engineering Study: A Bibliometric Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "7", "Page": "4487", "JournalTitle": "Archives of Computational Methods in Engineering"}]}, {"ArticleId": 97088342, "Title": "Computational-based biomarkers for mental and emotional health", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-022-07920-z", "PubYear": 2023, "Volume": "35", "Issue": "8", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Oviedo, Oviedo, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of the Basque Country, Leioa, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Control Engineering and Intelligent Systems (eXiT), University of Girona, Gerona, Spain"}], "References": [{"Title": "Virtual reality and machine learning in the automatic photoparoxysmal response detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "8", "Page": "5643", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Impact of sampling rate and interpolation on photoplethysmography and electrodermal activity signals’ waveform morphology and feature extraction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "8", "Page": "5661", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Diagnostic classification of Parkinson’s disease based on non-motor manifestations and machine learning strategies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "8", "Page": "5603", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Black hole algorithm with convolutional neural networks for the creation of brain-computer interface based in visual perception and visual imagery", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "8", "Page": "5631", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Ranking the effect of chronodisruption-based biomarkers in reproductive health", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "8", "Page": "5697", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Prediction of schizophrenia from activity data using hidden Markov model parameters", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "8", "Page": "5619", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 97088409, "Title": "Image Captioning with Synergy-Gated Attention and Recurrent Fusion LSTM", "Abstract": "Long Short-Term Memory (LSTM) combined with attention mechanism is extensively used to generate semantic sentences of images in image captioning models. However, features of salient regions and spatial information are not utilized sufficiently in most related works. Meanwhile, the LSTM also suffers from the problem of underutilized information in a single time step. In the paper, two innovative approaches are proposed to solve these problems. First, the Synergy-Gated Attention (SGA) method is proposed, which can process the spatial features and the salient region features of given images simultaneously. SGA establishes a gated mechanism through the global features to guide the interaction of information between these two features. Then, the Recurrent Fusion LSTM (RF-LSTM) mechanism is proposed, which can predict the next hidden vectors in one time step and improve linguistic coherence by fusing future information. Experimental results on the benchmark dataset of MSCOCO show that compared with the state-of-the-art methods, the proposed method can improve the performance of image captioning model, and achieve competitive performance on multiple evaluation indicators. Copyright © 2022 KSII.", "Keywords": "Deep learning; Image captioning; Recurrent Fusion LSTM; Synergy-Gated Attention", "DOI": "10.3837/tiis.2022.10.010", "PubYear": 2022, "Volume": "16", "Issue": "10", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 97088413, "Title": "Modulation Recognition of MIMO Systems Based on Dimensional Interactive Lightweight Network", "Abstract": "Automatic modulation recognition is the core algorithm in the field of modulation classification in communication systems. Our investigations show that deep learning (DL) based modulation recognition techniques have achieved effective progress for multiple-input multiple-output (MIMO) systems. However, network complexity is always an additional burden for high-accuracy classifications, which makes it impractical. Therefore, in this paper, we propose a low-complexity dimensional interactive lightweight network (DilNet) for MIMO systems. Specifically, the signals received by different antennas are cooperatively input into the network, and the network calculation amount is reduced through the depth-wise separable convolution. A two-dimensional interactive attention (TDIA) module is designed to extract interactive information of different dimensions, and improve the effectiveness of the cooperation features. In addition, the TDIA module ensures low complexity through compressing the convolution dimension, and the computational burden after inserting TDIA is also acceptable. Finally, the network is trained with a penalized statistical entropy loss function. Simulation results show that compared to existing modulation recognition methods, the proposed DilNet dramatically reduces the model complexity. The dimensional interactive lightweight network trained by penalized statistical entropy also performs better for recognition accuracy in MIMO systems. Copyright © 2022 KSII.", "Keywords": "automatic modulation recognition; lightweight network; multiple-input multiple-output(MIMO); penalized statistical entropy; two-dimensional interactive attention", "DOI": "10.3837/tiis.2022.10.014", "PubYear": 2022, "Volume": "16", "Issue": "10", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 97088416, "Title": "Weibo Disaster Rumor Recognition Method Based on Adversarial Training and Stacked Structure", "Abstract": "To solve the problems existing in the process of Weibo disaster rumor recognition, such as lack of corpus, poor text standardization, difficult to learn semantic information, and simple semantic features of disaster rumor text, this paper takes <PERSON><PERSON> as the data source, constructs a dataset for Weibo disaster rumor recognition, and proposes a deep learning model BERT_AT_Stacked LSTM for Weibo disaster rumor recognition. First, add adversarial disturbance to the embedding vector of each word to generate adversarial samples to enhance the features of rumor text, and carry out adversarial training to solve the problem that the text features of disaster rumors are relatively single. Second, the BERT part obtains the word-level semantic information of each Weibo text and generates a hidden vector containing sentence-level feature information. Finally, the hidden complex semantic information of poorly-regulated Weibo texts is learned using a Stacked Long Short-Term Memory (Stacked LSTM) structure. The experimental results show that, compared with other comparative models, the model in this paper has more advantages in recognizing disaster rumors on Weibo, with an F1_Socre of 97.48%, and has been tested on an open general domain dataset, with an F1_Score of 94.59%, indicating that the model has better generalization. Copyright © 2022 KSII.", "Keywords": "Adversarial training; BERT; Rumor recognition; Stacked LSTM; Weibo disaster", "DOI": "10.3837/tiis.2022.10.001", "PubYear": 2022, "Volume": "16", "Issue": "10", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 97088522, "Title": "Design of Intelligent Classification Trash Can System", "Abstract": "Intelligent garbage can can identify the name of garbage by voice to identify the type of garbage and accurately classify the type of garbage. This design uses a control system for multi-functional intelligent garbage bins based on STM32F103C8T6. The system mainly consists of speech recognition module, motor drive module, buzzer module, voice broadcast module, power module, infrared obstacle avoidance module and display module, then steering module and embedded system as the core, by reading the garbage type of voice, using language recognition module, through the language broadcast module broadcast garbage type, and then automatically open the corresponding trash can lid, when the trash can reaches a certain height, will make the corresponding prompt. This system can be simple operation, low cost, simple structure, and has the advantages of intelligence, in order to prevent the waste of resources, to prevent waste pollution to the environment, the intelligent voice recognition and classification can be used to solve this problem.", "Keywords": "STM32F103C8T6 ; language recognition module ; steering gear module ; dustbin barrel lid ; infrared obstacle avoidance module", "DOI": "10.1016/j.procs.2022.10.016", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong University of Science & Technology, Dongguan, 523000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong University of Science & Technology, Dongguan, 523000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong University of Science & Technology, Dongguan, 523000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangzhou Yuejian Communication Technology Co., LTD"}], "References": []}, {"ArticleId": 97088525, "Title": "Preliminarily Explore the Steps of Financial Big Data Analytics", "Abstract": "Big data analytics is difficult for people since there are so many factors that can influence the ways stock market change. Big data analytics is different from weather forecasting, which can be made by applying the laws of atmospheric change and mass experience and other comprehensive research. However, stock markets do not have a fixed law that allows people to do analysis. They are influenced by governments decision, information on the website, citizens’ behaviors, etc. Hence, this paper helps people learn how to do big data analytics and stock market prediction by three steps, such as sentiment analysis, information extraction and cleaning, and three models for stock market prediction. The implementation process of big data analytics generally includes several stages, such as data acquisition and recording, information extraction and cleaning, data integration and presentation, selection of modeling and analysis methods, interpretation of results, effectiveness of evaluation results and monitoring. [1] , [2] The result of the prediction is that LSTM model is the best model in prediction of the trend of stock market.", "Keywords": "Big data analytics ; sentiment analysis ; prophet model ; automatic ARIMA model ; LSTM model", "DOI": "10.1016/j.procs.2022.10.065", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Haidian Foreign Language Academy, Beijing, 100195 P.R.China"}], "References": []}, {"ArticleId": 97088529, "Title": "The Influence of Rotating Axis Position on Wind Load Coefficient of Polyhedral Phased Array Antenna Model", "Abstract": "For a radar antenna working in an open air environment, wind load is a typical load that cannot be ignored, and it has a greater impact on the rigidity, strength and servo drive capability of the antenna.And it will indirectly affect the use accuracy of the radar antenna, resulting in the pointing deviation and defocusing of the radar antenna. Therefore, the wind load research of radar is one of the important links in radar antenna design. This paper designs an antenna wind load coefficient simulation model by using the standard k-ɛ turbulence numerical solution method, the author studies the wind load coefficient of the polyhedral phased array antenna relative to different shaft positions, compares the trend of the wind load coefficient under the offset of the axis δ= 70, 100, 120,140,170. And explores the influence of shaft offset on the wind load coefficient. The feasibility of the turbulence model was verified through industrial wind tunnel tests, and the wind load coefficients of similar-shaped polyhedral antennas were given for reference in engineering design.", "Keywords": "Rotating Axis ; polyhedral phased array antenna model ; wind load coefficient ; the standard k-ɛ ; turbulence", "DOI": "10.1016/j.procs.2022.10.092", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Eighth Research Institute, China State Shipbuilding Corporation, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The Eighth Research Institute, China State Shipbuilding Corporation, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The Eighth Research Institute, China State Shipbuilding Corporation, Nanjing, China"}, {"AuthorId": 4, "Name": "Ke<PERSON>ng Li", "Affiliation": "The Eighth Research Institute, China State Shipbuilding Corporation, Nanjing, China"}], "References": []}, {"ArticleId": 97088607, "Title": "PENERAPAN TEKN<PERSON>O<PERSON> QUICK RESPO<PERSON>E CODE DAN APPLICATION PROGRAMMING INTERFACE PADA PERANCANGAN APLIKASI PERPUSTAKAAN (STUDI KASUS : SMP NEGERI 25 SURAKARTA)", "Abstract": "SMP Negeri 25 Surakarta memiliki perpustakaan dengan banyak koleksi bahan pustaka tetapi untuk mengelola data masih menggunakan cara manual. Cara manual memiliki beberapa kekurangan antara lain lambat dalam proses pengolahan data, menimbulkan kesulitan ketika mencari data atau informasi, dan membutuhkan banyak ruang untuk menyimpan data. <PERSON><PERSON> karena itu, penulis merancang aplikasi perpustakaan dengan menerapkan teknologi QR Code dan API di SMP Negeri 25 Surakarta. Aplikasi ini dikembangkan dengan metode Software Development Life Cycle (SDLC) model waterfall. Aplikasi perpustakaan ini dirancang berbasis website. Aplikasi ini dibuat menggunakan bahasa pemrograman PHP, framework codeigniter dan bootstrap, serta MySQL sebagai Database Management System (DBMS). Kemudian menambahkan ReST API server pada aplikasi tersebut agar data yang tersimpan dalam database dapat diakses untuk dikembangkan menjadi aplikasi android. Selanjutnya dibuat ReST API client dalam bentuk aplikasi android. ReST API client ini mengakses data milik ReST API server dan dikembangkan menjadi aplikasi baru. Hasil dari penelitian ini adalah aplikasi perpustakaan dengan menerapkan teknologi QR Code dan API di SMP Negeri 25 Surakarta.", "Keywords": "API; codeigniter; perpustakaan; QR Code; waterfall", "DOI": "10.29100/jipi.v7i3.3096", "PubYear": 2022, "Volume": "7", "Issue": "3", "JournalId": 59609, "JournalTitle": "JIPI (<PERSON><PERSON><PERSON> da<PERSON>ajaran Informatika)", "ISSN": "", "EISSN": "2540-8984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Surakarta"}], "References": []}, {"ArticleId": 97088693, "Title": "Special Issue on Security and Privacy in Blockchains and the IoT", "Abstract": "<p>The increasing digitalization in all areas of life is leading step-by-step to a data-driven society [...]</p>", "Keywords": "", "DOI": "10.3390/fi14110317", "PubYear": 2022, "Volume": "14", "Issue": "11", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Parallel and Distributed Systems, University of Stuttgart, Universitätsstraße 38, 70569 Stuttgart, Germany"}], "References": [{"Title": "Securing SDN-Based IoT Group Communication", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "8", "Page": "207", "JournalTitle": "Future Internet"}, {"Title": "Securing Environmental IoT Data Using Masked Authentication Messaging Protocol in a DAG-Based Blockchain: IOTA Tangle", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "12", "Page": "312", "JournalTitle": "Future Internet"}, {"Title": "Query Processing in Blockchain Systems: Current State and Future Challenges", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "Future Internet"}, {"Title": "CNN for User Activity Detection Using Encrypted In-App Mobile Data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "2", "Page": "67", "JournalTitle": "Future Internet"}, {"Title": "Utilizing Blockchain for IoT Privacy through Enhanced ECIES with Secure Hash Function", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "3", "Page": "77", "JournalTitle": "Future Internet"}, {"Title": "A Bidirectional Trust Model for Service Delegation in Social Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "5", "Page": "135", "JournalTitle": "Future Internet"}, {"Title": "A Review of Blockchain Technology Applications in Ambient Assisted Living", "Authors": "Alexand<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "5", "Page": "150", "JournalTitle": "Future Internet"}]}, {"ArticleId": 97088722, "Title": "A New Rank Metric Codes based Identification Scheme", "Abstract": "", "Keywords": "", "DOI": "10.5120/ijca2022922409", "PubYear": 2022, "Volume": "184", "Issue": "34", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Regis F. <PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 97088826, "Title": "Infection dynamics of rabbit and red fox with alternative prey", "Abstract": "In many countries, the decline in red foxes due to Rabbit hemorrhagic disease (RHD) in primary prey European rabbits is a significant concern. We proposed a four-compartment spatiotemporal rabbits-alternative prey-red fox eco-epidemiological model with mange disease and hunting in red foxes. The essential theoretical properties, such as existence, boundedness, stability, and bifurcation analysis, are executed. We have also conducted Turing instability and Higher-order stability analysis for the spatiotemporal model. Hopf bifurcation is shown at a critical value of hunting rate h = hc using central manifold theory. Numerical simulation reveals that the present dynamic is chaotic for a mange disease transmission rate’s threshold value β = β+, the most significant factor in the present dynamics. We can control the red fox population by controlling the mange contact rate despite RHD disease in European rabbits. Also, the model does not have diffusion-driven instability due to alternative prey, and if the system is linearly stable, it remains stable for higher-order. © 2023 All rights reserved.", "Keywords": "bifurcation; chaos; mange; Stability", "DOI": "10.22436/jmcs.029.03.02", "PubYear": 2022, "Volume": "29", "Issue": "3", "JournalId": 34363, "JournalTitle": "Journal of Mathematics and Computer Science", "ISSN": "", "EISSN": "2008-949X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "S.M.S. Govt. Model Science College, Jiwaji University, M.P, Gwalior, 474002, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ABV-Indian Institute of Information Technology and Management, M.P., Gwalior, 474015, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "S.M.S. Govt. Model Science College, Jiwaji University, M.P, Gwalior, 474002, India"}], "References": []}, {"ArticleId": 97088850, "Title": "Theoretical Analysis of Fuel-Optimal Powered Descent Problem with State Constraints", "Abstract": "", "Keywords": "", "DOI": "10.2514/1.G006815", "PubYear": 2022, "Volume": "45", "Issue": "12", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "Sixiong You", "Affiliation": "Purdue University, West Lafayette, Indiana 47907"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Purdue University, West Lafayette, Indiana 47907"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "NASA Johnson Space Center, Houston, Texas 77058"}], "References": [{"Title": "Learning-Based Onboard Guidance for Fuel-Optimal Powered Descent", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "44", "Issue": "3", "Page": "601", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Mars Entry Trajectory Planning with Range Discretization and Successive Convexification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "45", "Issue": "4", "Page": "755", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}]}, {"ArticleId": 97088952, "Title": "A methodology for refined evaluation of neural code completion approaches", "Abstract": "<p>Code completion has become an indispensable feature of modern Integrated Development Environments. In recent years, many approaches have been proposed to tackle this task. However, it is hard to compare between the models without explicitly re-evaluating them due to the differences of used benchmarks (e.g. datasets and evaluation metrics). Besides, almost all of these works report the accuracy of the code completion models as aggregated metrics averaged over all types of code tokens. Such evaluations make it difficult to assess the potential improvements for particularly relevant types of tokens (i.e. method or variable names), and blur the differences between the performance of the methods. In this paper, we propose a methodology called Code Token Type Taxonomy ( CT3 ) to address the issue of using aggregated metrics. We identify multiple dimensions relevant for code prediction (e.g. syntax type, context, length), partition the tokens into meaningful types along each dimension, and compute individual accuracies by type. We illustrate the utility of this methodology by comparing the code completion accuracy of a Transformer-based model in two variants: with closed, and with open vocabulary. Our results show that the refined evaluation provides a more detailed view of the differences and indicates where further work is needed. We also survey the state-of-the-art of Machine Learning-based code completion models to illustrate that there is a demand for a set of standardized benchmarks for code completion approaches. Furthermore, we find that the open vocabulary model is significantly more accurate for relevant code token types such as usage of (defined) variables and literals.</p>", "Keywords": "Code completion; Accuracy evaluation; Code token types; Open/closed vocabulary; Transformers; Python", "DOI": "10.1007/s10618-022-00866-9", "PubYear": 2023, "Volume": "37", "Issue": "1", "JournalId": 3928, "JournalTitle": "Data Mining and Knowledge Discovery", "ISSN": "1384-5810", "EISSN": "1573-756X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Heidelberg University, Heidelberg, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Heidelberg University, Heidelberg, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Heidelberg University, Heidelberg, Germany"}], "References": [{"Title": "CodeGRU: Context-aware deep learning with gated recurrent unit for source code modeling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "125", "Issue": "", "Page": "106309", "JournalTitle": "Information and Software Technology"}, {"Title": "Deep Learning for Source Code Modeling and Generation", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 97088956, "Title": "Two-stage semi-supervised clustering ensemble framework based on constraint weight", "Abstract": "<p>Semi-supervised clustering ensemble introduces partial supervised information, usually pairwise constraints, to achieve better performance than clustering ensemble. Although it has been successful in many aspects, there are still several limitations that need to be further improved. Firstly, supervised information is only utilized in ensemble generation, but not in the consensus process. Secondly, all clustering solutions participate in getting a final partition without considering redundancy among clustering solutions. Thirdly, each cluster in the same clustering solution is treated equally, which neglects the influence of different clusters to the final clustering result. To address these issues, we propose a two-stage semi-supervised clustering ensemble framework which considers both ensemble member selection and the weighting of clusters. Especially, we define the weight of each pairwise constraint to assist ensemble members selection and the weighting of clusters. In the first stage, a subset of clustering solutions is obtained based on the quality and diversity of clustering solutions in consideration of supervised information. In the second stage, the quality of each cluster is determined by the consistency of unsupervised and supervised information. For the unsupervised information consistency of a cluster, we consider evaluating it by the consistency of a cluster relative to all clustering solutions. For the supervised information consistency of a cluster, it depends on how satisfied a cluster is with the supervised information. In the end, the final partition is achieved by a weighted co-association matrix as consensus function. Experimental results on various datasets show that the proposed framework outperforms most of state-of-the-art clustering algorithms.</p>", "Keywords": "Clustering ensemble; Pairwise constraint; Semi-supervised clustering; Weighting of cluster", "DOI": "10.1007/s13042-022-01651-2", "PubYear": 2023, "Volume": "14", "Issue": "2", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xidian University, Xi’an, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xidian University, Xi’an, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xidian University, Xi’an, People’s Republic of China"}], "References": [{"Title": "Multiple clustering and selecting algorithms with combining strategy for selective clustering ensemble", "Authors": "Tinghuai Ma; <PERSON>; Xiuge Wu", "PubYear": 2020, "Volume": "24", "Issue": "20", "Page": "15129", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 97089153, "Title": "Enhancing the safety and reliability for loading and unloading bulk storage bags and a new bulk feeder support design structure", "Abstract": "Super bags are used to pack large and heavy bulk materials. They are durable; however they can be damaged by improper handling or misuse. Ensuring the safety of the operators who handle heavy bags is a main concern. The first objective is designing a backup safety frame to support the suspended bags by the hoist. The second objective is designing a spider bag support system compatible with the new frame and able to lift the super bag as required by Occupational Safety and Health Administration 1926.554 standard. Minimising weight and reducing cost were taken into consideration. The new frame and spider combined design mechanical properties were simulated using ANSYS™ software. The results showed the maximum deflection found at the spider was 1.78 × 10–3 m, while the maximum strain was 0.0006 for 250 MPa material yield stress. The maximum stress was 100 Mpa located at the hopper’s base. The combined design safety factor was 2.503 and fatigue safety factor was 1.45. This study concluded the new frame and spider structure will achieve the desired objectives and meet the OSHA regulations. Copyright © The Author(s) 2022.", "Keywords": "bulk bags safety; fatigue; hoist safety; spider support; stress", "DOI": "10.1504/IJCAET.2022.126661", "PubYear": 2022, "Volume": "17", "Issue": "4", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Management Systems Engineering, Kuwait University, P.O. Box 5969, Safat13060, Kuwait"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, West Virginia University, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Engineering Management, University of Sharjah, United Arab Emirates"}], "References": []}, {"ArticleId": ********, "Title": "Temporal dependence and bank efficiency drivers in OECD: A stochastic DEA-ratio approach based on generalized auto-regressive moving averages", "Abstract": "There are two gaps in the literature: (1) there has been no effort yet considering the black-box productive process, through which financial ratio-inputs are turned into financial ratio-outputs; (2) the previous studies have not yet considered the underlying temporal dependence embedded in the input/output set. Filling in the first gap would contribute to the literature from the methodological perspective, while addressing the second issue is supposed to provide more robust efficiency results. In correspondence to these two gaps, we propose an innovative DEA-Ratio model for undesirable financial output ratios considering weak disposability. In addition, we employ the generalized Auto-Regressive Moving Average model to analyze temporal dependence in the input/output set, which allows us to project the efficiency levels five year ahead. We apply our proposed method to a sample of 124 OECD banks over a twelve-year time window.", "Keywords": "", "DOI": "10.1016/j.eswa.2022.119120", "PubYear": 2023, "Volume": "214", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "COPPEAD, Universidade Federal de Rio de Janeiro, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Facultad de Farmacia, Escuela de Nutrición y Dietética, Universidad de Valparaíso, Chile;Facultad de Farmacia, Centro de Micro-Bio Innovación, Universidad de Valparaíso, Chile;Corresponding author at: Facultad de Farmacia, Escuela de Nutrición y Dietética, Universidad de Valparaíso, Chile"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Management, University of Bradford, Bradford, West Yorkshire, BD7 1DP, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "COPPEAD, Universidade Federal de Rio de Janeiro, Brazil"}], "References": [{"Title": "Two-stage DEA in banks: Terminological controversies and future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113632", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Evaluation of retail ındustry performance ability through ıntegrated ıntuitionistic fuzzy TOPSIS and data envelopment analysis approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "16", "Page": "12255", "JournalTitle": "Soft Computing"}, {"Title": "A robust credibility DEA model with fuzzy perturbation degree: An application to hospitals performance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "189", "Issue": "", "Page": "116021", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Supplier selection in sustainable supply chains: Using the integrated BWM, fuzzy Shannon entropy, and fuzzy MULTIMOORA methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "195", "Issue": "", "Page": "116567", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Comparing aggregation methods in large-scale group AHP: Time for the shift to distance-based aggregation", "Authors": "Szabolcs Duleba; Zsombor Szádoczki", "PubYear": 2022, "Volume": "196", "Issue": "", "Page": "116667", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 97089337, "Title": "Real-time segmentation network for accurate weld detection in large weldments", "Abstract": "Aiming at the defects of inaccurate weld extraction and high matching error rate in automatic welding system of large weldments currently. We propose a multi task detection model based on CNN architecture, which integrates the semantic segmentation technology required for weldment merging as well as the edge detection technology needed for weld matching. In particular, for the purpose of predicting smoother edges and welds, we carefully construct a new segment head, which adopts the sub-pixel convolution technology for up-sampling. Furthermore, a joint optimization loss function is explored to alleviate the imbalance of category distribution in large-scale weldment datasets. To verify the effectiveness of the model, abundant groups of data are collected for training and testing. The experimental results indicate that the proposed method has achieved the optimal trade-off between detection accuracy (83.35% mIoU, 95.15% F-score of welds and edges) as well as speed (74FPS) on a 2080Ti GPU compared with other state-of-the-arts, which greatly improves the robustness of the automatic welding system for large weldments.", "Keywords": "", "DOI": "10.1016/j.engappai.2022.105008", "PubYear": 2023, "Volume": "117", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Key Laboratory of Spectral Imaging and Intelligent Sense, Nanjing University of Science and Technology, Nanjing 210094, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Key Laboratory of Spectral Imaging and Intelligent Sense, Nanjing University of Science and Technology, Nanjing 210094, China"}, {"AuthorId": 3, "Name": "Jing <PERSON>", "Affiliation": "Jiangsu Key Laboratory of Spectral Imaging and Intelligent Sense, Nanjing University of Science and Technology, Nanjing 210094, China"}, {"AuthorId": 4, "Name": "Lianfa Bai", "Affiliation": "Jiangsu Key Laboratory of Spectral Imaging and Intelligent Sense, Nanjing University of Science and Technology, Nanjing 210094, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Jiangsu Key Laboratory of Spectral Imaging and Intelligent Sense, Nanjing University of Science and Technology, Nanjing 210094, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Key Laboratory of Spectral Imaging and Intelligent Sense, Nanjing University of Science and Technology, Nanjing 210094, China;Corresponding authors"}], "References": [{"Title": "A robust weld seam recognition method under heavy noise based on structured-light vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101821", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Multiple weld seam extraction from RGB-depth images for automatic robotic welding via point cloud registration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "6", "Page": "9703", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "BiSeNet V2: Bilateral Network with Guided Aggregation for Real-Time Semantic Segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "11", "Page": "3051", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 97089359, "Title": "An energy graph eigendecomposition approach to fault detection and isolation applied to a gas-to-liquids process", "Abstract": "Fault detection and isolation (FDI), which make up a large part of a process monitoring protocol, is a refined scheme which aims to detect and isolate anomalies that occur within an industrial plant. For the past 50+ years, much work has been done on developing FDI schemes for a vast array of different applications. In recent years, novel energy-based FDI techniques were proposed, as energy is seen as a unifying parameter of different domains. Additionally, the proposed energy-based approaches attempt to capture causal (or structural) information of the considered physical system. Keeping with this theme, this study will determine, after some alterations, the applicability and performance of some of the previously proposed energy-based approaches, especially compared to one another, when applied to a single, larger-scale gas-to-liquid (GTL) process. The approaches covered in this study include one qualitative eigendecomposition approach, one quantitative eigendecomposition approach, and a graph matching approach utilising a distance parameter ( D C -value). Even though the quantitative eigendecomposition approach revealed improved detection sensitivity over the D C -value approach for faulty conditions, both techniques revealed a detection accuracy of 89%. The D C -value approach could correctly isolate 71% of the fault cases while the quantitative and qualitative eigendecomposition approaches could respectively only match 37.69% and 11% correctly. Even though the eigendecomposition approaches could not outperform the D C -value approach, the resolution benefit of 6 parameters that it offers warrants further research.", "Keywords": "Gas-to-liquids ; Exergy ; Fault detection and isolation ; Energy graph-based ; Eigendecomposition", "DOI": "10.1016/j.compchemeng.2022.108040", "PubYear": 2022, "Volume": "168", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical, Electronic, and Computer Engineering, Faculty of Engineering, North-West University, Potchefstroom, 2531, South Africa"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Unit for Energy and Technology Systems, Faculty of Engineering, North-West University, Potchefstroom, 2531, South Africa"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical, Electronic, and Computer Engineering, Faculty of Engineering, North-West University, Potchefstroom, 2531, South Africa;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical, Electronic, and Computer Engineering, Faculty of Engineering, North-West University, Potchefstroom, 2531, South Africa"}], "References": [{"Title": "Exergy graph-based fault detection and isolation of a gas-to-liquids process", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "13674", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Exergy-based fault detection on the Tennessee Eastman process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "13713", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 97089381, "Title": "A robust watermarking scheme for hybrid attacks on heritage images", "Abstract": "<p>Heritage incorporates images, customs, rituals, beliefs, cultures, knowledge, arts, crafts, music, and artifacts of a region. Apart from conferring the details about the understanding of previous generations, the heritage multimedia also provides details regarding innovative attitudes, ways of living, and heterogeneity of historical and archaeological approaches of a society. Since the availability of the internet in the present scenario makes an illicit user access the data easily, therefore, it is mandatory to protect the Cultural Heritage (CH) media. This paper offers a secure and resilient Discrete Cosine Transform (DCT) based blind watermarking algorithm for copyright security of digital images. We have developed and tested two different approaches of embedding namely Fixed Threshold Reference-based Relative Modulation (FTRRM) and Adaptive Threshold Reference-based Relative Modulation (ATRRM). In either approach, the watermark is embedded in the ‘Y’ component of the YCbCr color model. In FTRRM, the ‘Y’ component of the host image is first divided into 8 × 8 non-overlapping blocks and DCT is applied on each block. The watermark bits are embedded into the transformed coefficients by modulating the relative difference of coefficients depending on the bit to be embedded. A similar embedding strategy is followed in ATRRM, with a change that relative difference is modulated adaptively. Both schemes have been tested on a set of heritage images, besides general test images. We make use of chaotic and Deoxyribo Nucleic acid (DNA) encryption to ensure double-layer security of embedded watermark. The Peak Signal-to-Noise Ratio (PSNR) of the proposed scheme in the case of FTRRM is 40.4984 dB and for ATRRM it is 39.9549 dB, the Structural Similarity Index Matrix (SSIM) values are close to unity, in both cases for various test images that guarantee the imperceptibility of the proposed scheme. The robustness of each scheme is revealed by comparing them with the various state-of-the-art techniques separately.</p>", "Keywords": "Copyright protection; Cultural media; Digital watermarking; Ownership verification", "DOI": "10.1007/s12652-022-04445-0", "PubYear": 2023, "Volume": "14", "Issue": "6", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation Technology, University of Kashmir, Srinagar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation Technology, University of Kashmir, Srinagar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Electronics and Instrumentation Technology, University of Kashmir, Srinagar, India"}, {"AuthorId": 4, "Name": "Kaiser J. Giri", "Affiliation": "Department of Computer Science, Islamic University of Science and Technology, Pulwama, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation Technology, University of Kashmir, Srinagar, India"}], "References": [{"Title": "Robust and blind image watermarking in DCT domain using inter-block coefficient correlation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "128", "JournalTitle": "Information Sciences"}, {"Title": "Robust and blind image watermarking in DCT domain using inter-block coefficient correlation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "128", "JournalTitle": "Information Sciences"}, {"Title": "Blind robust image watermarking based on adaptive embedding strength and distribution of quantified coefficients", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115906", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep Discrete Cross-<PERSON><PERSON> with Multiple Supervision", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "486", "Issue": "", "Page": "215", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 97089474, "Title": "Algorithm 1031: MQ<PERSON>—Monotone Quintic Spline Interpolation", "Abstract": "<p> MQSI is a Fortran 2003 subroutine for constructing monotone quintic spline interpolants to univariate monotone data. Using sharp theoretical monotonicity constraints, first and second derivative estimates at data provided by a quadratic facet model are refined to produce a univariate \\(\\scriptstyle C^2 \\) monotone interpolant. Algorithm and implementation details, complexity and sensitivity analyses, usage information, a brief performance study, and comparisons with other spline approaches are included. </p>", "Keywords": "Software; quintic spline; interpolation; B-spline; univariate; shape-preserving", "DOI": "10.1145/3570157", "PubYear": 2023, "Volume": "49", "Issue": "1", "JournalId": 14400, "JournalTitle": "ACM Transactions on Mathematical Software", "ISSN": "0098-3500", "EISSN": "1557-7295", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Virginia Polytechnic Institute and State University, Blacksburg, Virginia, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Virginia Polytechnic Institute and State University, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Argonne National Laboratory, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Winthrop University, USA"}], "References": []}, {"ArticleId": 97089477, "Title": "Study on athlete's facial emotion recognition based on <PERSON><PERSON> filter", "Abstract": "Aiming at the problems of high loss rate of expression details, poor comprehensiveness of recognition results and low recognition rate in traditional methods, an athlete’s facial emotion recognition based on <PERSON><PERSON> filter is proposed. Firstly, the uncertainty of athlete’s facial emotion image is described according to the active learning algorithm. Then, with full consideration of the uncertainty factors, the feature block method is used to mosaic the image. Finally, according to the splicing results, a first-order motion model is established by <PERSON><PERSON> filter to track and calibrate the image target points to complete the facial emotion recognition. The results show that the expression detail loss rate of this method is low, the comprehensiveness coefficient of recognition results is high, and the emotion recognition rate is always higher than 90%, indicating that the recognition effect of this method is better. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "active learning algorithm; emotion recognition; feature block; first-order motion model; image mosaic; <PERSON><PERSON> filter; uncertainty description", "DOI": "10.1504/IJRIS.2022.126651", "PubYear": 2022, "Volume": "14", "Issue": "4", "JournalId": 10882, "JournalTitle": "International Journal of Reasoning-based Intelligent Systems", "ISSN": "1755-0556", "EISSN": "1755-0564", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Physical Education and Health, Linyi University, Linyi, 276000, China"}, {"AuthorId": 2, "Name": "Qingxue Li", "Affiliation": "College of Physical Education and Health, Linyi University, Linyi, 276000, China"}, {"AuthorId": 3, "Name": "Yuanhua Li", "Affiliation": "College of Physical Education, Central South University of Forestry and Technology, Changsha, 410004, China"}], "References": []}, {"ArticleId": 97089555, "Title": "Waveform Relaxation with Asynchronous Time-integration", "Abstract": "<p>We consider Waveform Relaxation (WR) methods for parallel and partitioned time-integration of surface-coupled multiphysics problems. WR allows independent time-discretizations on independent and adaptive time-grids, while maintaining high time-integration orders. Classical WR methods such as Jacobi or Gauss-Seidel WR are typically either parallel or converge quickly.</p><p>We present a novel parallel WR method utilizing asynchronous communication techniques to get both properties. Classical WR methods exchange discrete functions after time-integration of a subproblem. We instead asynchronously exchange time-point solutions during time-integration and directly incorporate all new information in the interpolants. We show both continuous and time-discrete convergence in a framework that generalizes existing linear WR convergence theory. An algorithm for choosing optimal relaxation in our new WR method is presented.</p><p> Convergence is demonstrated in two conjugate heat transfer examples. Our new method shows an improved performance over classical WR methods. In one example we show a partitioned coupling of the compressible Euler equations with a nonlinear heat equation, with subproblems implemented using the open source libraries DUNE and FEniCS . </p>", "Keywords": "Asynchronous iteration; waveform relaxation; dynamic iteration; coupled problems; thermal fluid-structure interaction", "DOI": "10.1145/3569578", "PubYear": 2022, "Volume": "48", "Issue": "4", "JournalId": 14400, "JournalTitle": "ACM Transactions on Mathematical Software", "ISSN": "0098-3500", "EISSN": "1557-7295", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for the mathematical sciences, Lund University, Sweden"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Centre for the mathematical sciences, Lund University, Sweden"}], "References": [{"Title": "The Dune framework: Basic concepts and recent developments", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "81", "Issue": "", "Page": "75", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 97089585, "Title": "Data Analytics Approach for Short-term Sales Forecasts Using Limited Information in E-commerce Marketplace", "Abstract": "E-commerce has become very important in our daily lives. Many business transactions are made easier on this platform. Sellers and consumers are the two main parties that gain a lot of benefits from it. Although many sellers are attracted to set up their businesses on this online platform, it also causes challenges such as a highly competitive business environment and unpredictable sales. Thus, we propose a data analytics approach for short-term sales forecasts using limited information in the e-commerce marketplace. Product details are scraped from the e-commerce marketplace using a content scraping tool. Since the information in the e-commerce marketplace is limited and essential, scraped product details are pre-processed and constructed into meaningful data. These data are used in the computation of the forecasting methods. Three types of quantitative forecasting methods are computed and compared. These are simple moving average, dynamic linear regression and exponential smoothing. Three different evaluation metrics, namely mean absolute deviation, mean absolute percentage error and mean squared error, are used for the performance evaluation in order to determine the most suitable forecasting method. In our experiment, we found that the simple moving average has the best forecasting accuracy among other forecasting methods. Therefore, the application of the simple moving average forecasting method is suitable and can be used in the e-commerce marketplace for sales forecasting. © 2022 by the author(s).", "Keywords": "Data analytics; Electronic commerce; Regression; Sales forecast; Simple moving average", "DOI": "10.18267/j.aip.196", "PubYear": 2022, "Volume": "11", "Issue": "3", "JournalId": 32235, "JournalTitle": "Acta Informatica Pragensia", "ISSN": "1805-4951", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science and Information Technology, Universiti Malaysia Sarawak, Kota Samarahan, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science and Information Technology, Universiti Malaysia Sarawak, Kota Samarahan, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> bt Sarbini", "Affiliation": "Faculty of Computer Science and Information Technology, Universiti Malaysia Sarawak, Kota Samarahan, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> (Sarawak) Enterprise Sdn. Bhd., Malaysia"}], "References": []}, {"ArticleId": 97089625, "Title": "Accurate gait recognition with inertial sensors using a new FCN-BiLSTM architecture", "Abstract": "Gait recognition is one of the most successful biometric recognition technologies. Earlier studies have employed inertial sensors to capture gait dynamics for individual identification. Still, the overall performance of gait recognition is improvable. In this work, we have suggested a new deep neural network architecture named FCN-BiLSTM. The architecture concatenates the extracted features of a Bidirectional LSTM Network with the extracted features provided by a Fully Convolutional Network that uses Squeeze-and-Excitation blocks to provide a better feature map. That map is then input to a softmax classifier. We assessed our model on multiple benchmark datasets, particularly the OU-ISIR and whuGAIT datasets. The suggested architecture surpasses the existing state-of-the-art methods on the OU-ISIR dataset, Dataset #1, and #3 of the whuGAIT datasets. The performance was equivalent on Dataset #2 and Dataset #4 of the whuGAIT Datasets. Therefore, we believe the proposed architecture can be employed for biometric systems benefitting humans.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108428", "PubYear": 2022, "Volume": "104", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Rajshahi University of Engineering and Technology, Rajshahi 6204, Bangladesh;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Rajshahi University of Engineering and Technology, Rajshahi 6204, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Rajshahi University of Engineering and Technology, Rajshahi 6204, Bangladesh"}], "References": []}, {"ArticleId": 97089726, "Title": "GNLStools.py: A generalized nonlinear Schrödinger Python module implementing different models of input pulse quantum noise", "Abstract": "We provide Python tools enabling numerical simulation and analysis of the propagation dynamics of ultrashort laser pulses in nonlinear waveguides. The modeling approach is based on the widely used generalized nonlinear Schrödinger equation for the pulse envelope. The presented software implements the effects of linear dispersion, pulse self-steepening, and the Raman effect. The focus lies on the implementation of input pulse shot noise, i.e. classical background fields that mimic quantum noise, which are often not thoroughly presented in the scientific literature. We discuss and implement commonly adopted quantum noise models based on pure spectral phase noise, as well as Gaussian noise. Coherence properties of the resulting spectra can be calculated. We demonstrate the functionality of the software by reproducing results for a supercontinuum generation process in a photonic crystal fiber, documented in the scientific literature. The presented Python tools are open-source and released under the MIT license in a publicly available software repository.", "Keywords": "Generalized nonlinear <PERSON><PERSON><PERSON><PERSON><PERSON> equation ; Quantum noise ; Spectral coherence ; Python", "DOI": "10.1016/j.softx.2022.101232", "PubYear": 2022, "Volume": "20", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cluster of Excellence PhoenixD (Photonics, Optics, and Engineering – Innovation Across Disciplines), Hannover, Germany;Leibniz Universität Hannover, Institute of Quantum Optics (IQO), 30167 Hannover, Germany;Corresponding author at: Leibniz Universität Hannover, Institute of Quantum Optics (IQO), 30167 Hannover, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cluster of Excellence PhoenixD (Photonics, Optics, and Engineering – Innovation Across Disciplines), Hannover, Germany;Leibniz Universität Hannover, Institute of Quantum Optics (IQO), 30167 Hannover, Germany"}], "References": [{"Title": "py-fmas: A python package for ultrashort optical pulse propagation in terms of forward models for the analytic signal", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "273", "Issue": "", "Page": "108257", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 97089730, "Title": "Uncanny valley for interactive social agents: an experimental study", "Abstract": "The uncanny valley hypothesis states that users might experience eerie when interacting with almost but not fully human-like artificial characters. The advancements in artificial intelligence, robotics, and computer graphics have led to life-like virtual humans and humanoid robots. It is necessary to revisit the hypothesis to check if they positively or negatively affect the current population, who are much more accustomed to the latest technologies. In this paper, we study and present a unique evaluation of the uncanny valley hypothesis by allowing participants to interact live with four different humanoid robots (of varying levels of humanlikeness). To evaluate the affinity of each robot, each participant needs to fill a survey questionnaire. Apart from this, we also use deep learning methods to quantify the participants’ emotional states using multi-modalcues, including visual, audio, and text, by recording the participant-robot interaction. The multi-modal analysis and surveys provide interesting results and insights into the uncanny valley hypothesis.", "Keywords": "Uncanny Valley hypothesis ; Human Robot Interaction ; Interactive robots ; Humanoid robots and Virtual humans", "DOI": "10.1016/j.vrih.2022.08.003", "PubYear": 2022, "Volume": "4", "Issue": "5", "JournalId": 61022, "JournalTitle": "Virtual Reality & Intelligent Hardware", "ISSN": "2096-5796", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Media Innovation, Nanyang Technological University, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Rehabilitation Research Institute of Singapore, Nanyang Technological University, Singapore;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Media Innovation, Nanyang Technological University, Singapore"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute for Media Innovation, Nanyang Technological University, Singapore and MIRALab, University of Geneva, Switzerland"}], "References": [{"Title": "Supervised machine learning for audio emotion recognition", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "4", "Page": "637", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "On attaining user-friendly hand gesture interfaces to control existing GUIs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "2", "Page": "153", "JournalTitle": "Virtual Reality & Intelligent Hardware"}, {"Title": "Moral Uncanny Valley: A Robot’s Appearance Moderates How its Decisions are Judged", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "7", "Page": "1679", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Can a humanoid social robot stimulate the interactivity of cognitively impaired elderly? A thorough study based on computer vision methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "12", "Page": "3019", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 97089802, "Title": "Research on Neural Network-based Cognitive Behavioral Therapy for Chronic Fatigue Syndrome", "Abstract": "Cognitive behavioral therapy (CBT) A structured, short-range, cognitive-oriented psychotherapy method developed by <PERSON><PERSON><PERSON><PERSON> in the 1960s, which mainly targets mental illnesses such as depression and anxiety disorders and psychological problems caused by irrational cognition.It mainly focuses on the unreasonable cognitive problems of patients, and changes the psychological problems by changing the views and attitudes of patients towards themselves, people or things.Cognitive-behavioral therapy based on neural network is a way to achieve cognitive-behavioral therapy by obtaining computer programs through the Internet. The collected data were screened and meta-analyzed through meta-analytic studies and randomized controlled experiments of web-based cognitive behavioral therapy interventions to form applied random-effects models, or fixed-effects models for combined effect sizes. Through the use of online cognitive behavioral therapy to compare the differences between the treatment group and the control group in terms of overall state, fatigue level, physical function, etc., it is found that cognitive behavioral therapy can improve the overall state of chronic fatigue syndrome, reduce fatigue symptoms, and improve physical function. have significant effects.", "Keywords": "Chronic fatigue syndrome ; cognitive behavioral therapy ; effectiveness ; neural network", "DOI": "10.1016/j.procs.2022.10.011", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Suzhou Vocational University , Suzhou , 215104"}], "References": []}, {"ArticleId": 97089832, "Title": "Intellectual capital and team resilience capability of information system development project teams", "Abstract": "Adversities are inherent in the information system development (ISD) process and often put projects to a halt. However, it is unclear what capabilities the team needs to resist and bounce back from adverse events. The purpose of this study is to propose that team resilience capability (TRC), containing affective, cognitive, and behavioral factors is vital for effective project performance. Further, by adopting the conservation of resource perspective, we theorize that intellectual capital, including human capital, technology capital, and political capital fosters TRC. Survey data collected from 149 ISD project teams confirmed our ideas that TRC is strongly tied with project performance and is more affected by human capital, followed by political capital and technology capital.", "Keywords": "Team resilience capability ; Conservation of resource theory ; ISD project team ; Intellectual capital", "DOI": "10.1016/j.im.2022.103722", "PubYear": 2023, "Volume": "60", "Issue": "1", "JournalId": 2493, "JournalTitle": "Information & Management", "ISSN": "0378-7206", "EISSN": "1872-7530", "Authors": [{"AuthorId": 1, "Name": "Kuang<PERSON><PERSON><PERSON>", "Affiliation": "National United University, No. 1, <PERSON><PERSON>, Kung-<PERSON>, MiaoLi City, MiaoLi County, 36003, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Sun Yat-sen University, No. 70, Lienhai Rd, Kaohsiung City, 80424, Taiwan;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Massachusetts Dartmouth, 285 Old Westport Road, North Dartmouth, MA 02747, United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National Sun Yat-sen University, No. 70, Lienhai Rd, Kaohsiung City, 80424, Taiwan"}], "References": [{"Title": "Enabling collaboration and innovation in Denver’s smart city through a living lab: a social capital perspective", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "4", "Page": "369", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Enacting formal controls in information system development: Process guidance and goal importance", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "2", "Page": "103430", "JournalTitle": "Information & Management"}, {"Title": "Control choices and enactments in IS development projects: Implications for legitimacy perceptions and compliance intentions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "7", "Page": "103522", "JournalTitle": "Information & Management"}]}, {"ArticleId": 97089866, "Title": "Integration of blockchain and edge computing in internet of things: A survey", "Abstract": "As an important technology to ensure data security, consistency, traceability, etc. , blockchain has been increasingly used in Internet of Things (IoT) applications. The integration of blockchain and edge computing (IBEC) can further improve the resource utilization in terms of network, computing, storage, and security. This paper aims to present a survey on the IBEC. In particular, we first give an overview of blockchain and edge computing respectively. We then present a general architecture of an IBEC system. We next study the various applications of the IBEC in IoT. We also discuss the optimizations of the IBEC system and solutions from perspectives of resource management and performance improvement. Finally, we analyze and summarize the existing challenges posed by the IBEC system and the potential solutions in the future. Introduction Traditional cloud computing (CC) has been used to achieve on-demand resource sharing because of its high flexibility and scalability. With the rapid development of Internet of Things (IoT) in recent years, however, the explosive growth of data has higher real-time requirements for data storage, data processing, and data exchange, which are far beyond the carrying capacity of traditional CC. Specifically, in many IoT application scenarios, e.g. , Smart Grid and Internet of Vehicles (IoV), due to large scale data transmission between billions of devices and the data centers, high latency and bandwidth pressure limit the development of traditional CC in IoT. According to the Telecommunications Industry Association (TIA), the number of connected devices on global network by 2022 will be 29 billion, and roughly 18 billion of them will be related to the IoT [1]. As an important complement to CC, a new computing paradigm, named edge computing (EC), has been proposed to expand both computing and storage capabilities of the edge of IoT. Specifically, in EC, IoT devices can acquire resources by offloading tasks to nearby edge servers. Compared with cloud servers, edge servers are closer to IoT devices, that is, closer to the data sources. To some extent, this avoids the risk of privacy leakage brought by long-distance communication [2], [3], [4]. In addition, the resources of edge devices can also be effectively integrated and used to alleviate computing, storage, and bandwidth burdens of traditional CC. Therefore, EC can essentially offer distributed and low-latency computing services to smart cities, smart grid, smart healthcare, and other IoT scenarios [5]. However, due to the limited resources of IoT devices, heterogeneity of networks, and highly dynamic environment, many existing data-security techniques cannot be fully utilized in the EC architecture [6]. As an alternative solution to improve the security level and efficiency of EC, blockchain has attracted enough attentions recently [7], [8]. Blockchain can be regarded as a decentralized ledger, which utilizes the technologies of peer-to-peer (P2P), cryptography, distributed storage, etc. to achieve the following properties: decentralization, transparency, traceability, security and immutability [9]. In essence, blockchain can enhance the data integrity of edge nodes including edge servers and devices in EC by storing critical data at blockchain [10]. Moreover, blockchain can enable EC to implement security mechanisms, such as access control, authentication and privacy preservation [11], by using well-designed smart contracts [12], [13], [14], [15], [16]. Furthermore, blockchain can enable EC to orchestrate various edge resources through smart contract-based algorithms of resource allocation, task offloading and resource pricing [17], [18]. EC, in turn, can support blockchain by providing enough computing resources for the mining tasks. For instance, when edge devices can provide idle resources as edge servers do, the resources will be allocated in the way of bidding and trading for blockchain. The integration of blockchain and EC (IBEC) is a promising paradigm since both the two technologies can be complementary to construct frameworks to solve problems in several fields. For instance, in IoV and smart transportation, there exist challenges of insufficient on-board resources of most vehicles to support task processing and data storage, and difficulties in resources allocation brought by the mobility of vehicles [19]. To this end, the IBEC can serve IoV with collaborative management of computing and communication resources [20], [21], [22], [23], data sharing and data management for automatic driving [24], [25], [26], collaborative identity authentication during consensus mechanism [27], [28], [29], [30], efficient communications among vehicles [31], [32], [33], etc. In Smart Grid, the IBEC is mainly applied to aspects of pricing and framework designing of energy trading [34], [35], [36], [37], and trading security ensuring [38], [39], [40]. The IBEC can also benefit the other IoT scenarios, such as, Industrial Internet of Things (IIoT) [41], [42], [43], [44], [45], [46], [47], [48], smart healthcare [49], [50], [51], [52], [53], [54], [55], [56], edge intelligence and artificial intelligence (AI) [57], [58], [59], [60], [61], supply chain [62], [63], smart city [64], [65], [66], etc. Moreover, other technologies ( e.g. , CC [67], fog computing (FC) [68], Software Defined Network (SDN) [69], [70], [71], Network Function Virtualization (NFV) [72], [73], [74], AI [75], [76], [77]), are usually utilized in the architecture of IBEC, either to accomplish specific tasks like video stream processing and model training, or to design adaptable general frameworks for more practical and complicated networks and topologies. These studies aim to improve the processing performance of various tasks, to increase the resources utilization, and to enhance both network and data security in a more general and realistic environment. It is worth noting that FC is different from EC. FC implies distribution of the communication, computation, storage resources, and services on or close to devices and systems in the control of end-users in general [78], [79]. It is considered to be a medium weight and intermediate level of computing power [80]. While EC is typically referred to the location where services are instantiated. Some studies in this paper do not especially distinguish FC and EC while others do elaborate when stating their frameworks. Both cases are specified when mentioning the works. This paper first discusses existing studies including the applications and optimizations of IBEC. Then the challenges and solutions including performance scalability, resource management, security and privacy computing, are summarized and discussed. Different from other similar studies, which focus on the components of computing, network and storage [81] or pay attention to the solutions for edge intelligence and blockchain [82], this paper regards IBEC as an infrastructure and studies its advantages from the angle of its abroad applications in IoT, and discusses its disadvantages from the angle of optimizations. Other corresponding technologies are also discussed only if they contribute to the architecture design and problem solving of IBEC. The remainder of this paper is structured as follows. In Section 2, preliminaries of blockchain and EC are first reviewed, followed by the IBEC and its applications. Then, the optimizations of IBEC are discussed in Section 3 including resource management and performance optimizations. The challenges and solutions for IBEC are presented in Section 4, and the paper is concluded in Section 5. Section snippets Blockchain Blockchain is a kind of chained data structure where data blocks are connected cryptographically and chronologically in sequence for immutability and unforgeability [83]. As shown in Fig. 1, there are three adjacent blocks and each block has block header and block body. The hash value of the previous block, timestamp of the block generating, version, hash of the transactions list, etc. are recorded in the block header and the transaction list is stored in the block body. For instance, the value Optimizations of IBEC IBEC has its own limitations being optimized which will be discussed in this section. We will specify the resource management in Section 3.1 and the performance optimizations in Section 3.2. Challenges and solutions This section presents the challenges and the potential solutions of the IBEC. Conclusion As two crucial technologies for IoT, both blockchain and EC have been used to realize secure and efficient resource management, computation offloading and data sharing. This survey starts with a brief introduction of blockchain and EC and then presents the architecture of an IBEC system in IoT application scenarios. We next classify and discuss the corresponding research issues and existing solutions, in perspectives of resource management, data management, computation offloading, security and CRediT authorship contribution statement He Xue: Conceptualization, Methodology, Writing – original draft, Writing – review & editing. Dajiang Chen: Conceptualization, Methodology, Writing – original draft, Writing – review & editing. Ning Zhang: Investigation, Writing – original draft, Writing – review & editing. Hong-Ning Dai: Investigation, Writing – original draft, Writing – review & editing. Keping Yu: Investigation, Writing – original draft, Writing – review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. He Xue is currently an Assistant Engineer in Sichuan Fire Science and Technology Research Institute of MEM. She received the B.S. and M.S. degrees in Computer Science and Technology and Software Engineering from University of Electronic Science and Technology of China (UESTC) in 2015 and 2018, respectively. Her current research interests include Blockchain, Security and Privacy Protection in the application scenarios of Wireless Networks (e.g., IoT, Edge Computing, and Internet of Vehicles). References (202) Chen Y. et al. Joint task scheduling and energy management for heterogeneous mobile edge computing with hybrid energy supply IEEE Internet Things J. (2020) Ma X. et al. Cost-efficient resource provisioning for dynamic requests in cloud assisted mobile edge computing IEEE Trans. Cloud Comput. (2019) Khan W.Z. et al. Edge computing: A survey Future Gener. Comput. Syst. (2019) Miglani A. et al. Blockchain for internet of energy management: Review, solutions, and challenges Comput. Commun. (2020) Lu Y. The blockchain: State-of-the-art and research challenges J. Ind. Inform. Integr. (2019) Guan Z. et al. Towards secure and efficient energy trading in IIoT-enabled energy internet: A blockchain approach Future Gener. Comput. Syst. (2020) Zhang Y. et al. Chronos: Secure and accurate time-stamping scheme for digital files via blockchain Hu S. et al. Blockchain and edge computing technology enabling organic agricultural supply chain: a framework solution to trust crisis Comput. Ind. Eng. (2021) Wang W. et al. Blockchain-assisted handover authentication for intelligent telehealth in multi-server edge computing environment J. Syst. Archit. (2021) Jindal A. et al. SURVIVOR: A blockchain based edge-as-a-service framework for secure energy trading in SDN-enabled vehicle-to-grid environment Comput. Netw. (2019) Wang X. et al. Survey on blockchain for internet of things Comput. Commun. (2019) Sittón-Candanedo I. et al. A review of edge computing reference architectures and a new global edge proposal Future Gener. Comput. Syst. (2019) https://tiaonline.org/industry-priorities/transforming-infrastructure/edge-data-centers-cloud/. Accessed February... Chen D. et al. S2M: A lightweight acoustic fingerprints-based wireless device authentication protocol IEEE Internet Things J. (2016) Chen D. et al. Channel precoding based message authentication in wireless networks: Challenges and solutions IEEE Netw. (2018) Chen D. et al. Physical layer based message authentication with secure channel codes IEEE Trans. Dependable Secure Comput. (2018) Sittón-Candanedo I. Edge computing: A review of application scenarios Xiao Y. et al. Edge computing security: State of the art and challenges Proc. IEEE (2019) Wu Y. et al. Convergence of blockchain and edge computing for secure and scalable iIoT critical infrastructures in industry 4.0 IEEE Internet Things J. (2020) Luo G. et al. Software defined cooperative data sharing in edge computing assisted 5G-VANET IEEE Trans. Mob. Comput. (2019) Yin J. et al. SmartDID: a novel privacy-preserving identity based on blockchain for IoT IEEE Internet Things J. (2022) Xiong Z. et al. When mobile blockchain meets edge computing IEEE Commun. Mag. (2018) Tan H. et al. Secure authentication and key management with blockchain in VANETs IEEE Access (2019) Guo S. et al. Trust access authentication in vehicular network based on blockchain China Commun. (2019) Nkenyereye L. et al. Secure and blockchain-based emergency driven message protocol for 5G enabled vehicular edge computing Sensors (2020) Xiao Y. et al. Edge computing and blockchain for quick fake news detection in IoV Sensors (2020) Liu J. et al. Blockchain-empowered content cache system for vehicle edge computing networks Wang S. et al. Permissioned blockchain for efficient and secure resource sharing in vehicular edge computing (2019) Xiao K. et al. DAER: A resource pre-allocation algorithm of edge computing server by using blockchain in intelligent driving IEEE Internet Things J. (2020) Lin X. et al. Blockchain-based on-demand computing resource trading in IoV-Assisted smart city IEEE Trans. Emerg. Top. Comput. (2020) Wang S. et al. Consortium blockchain for secure resource sharing in vehicular edge computing: A contract-based approach IEEE Trans. Netw. Sci. Eng. (2020) Hammoud A. et al. AI, blockchain, and vehicular edge computing for smart and secure IoV: Challenges and directions IEEE Internet Things Mag. (2020) Dai Y. et al. Deep reinforcement learning and permissioned blockchain for content caching in vehicular edge computing and networks IEEE Trans. Veh. Technol. (2020) Li C. et al. Vehicle position correction: A vehicular blockchain networks-based GPS error sharing framework IEEE Trans. Intell. Transp. Syst. (2020) Singh G. et al. BloCkEd: Blockchain-based secure data processing framework in edge envisioned V2X environment IEEE Trans. Veh. Technol. (2020) Cui L. et al. A blockchain-based containerized edge computing platform for the internet of vehicles IEEE Internet Things J. (2020) Ayaz F. et al. A proof-of-quality-factor (PoQF) based blockchain and edge computing for vehicular message dissemination IEEE Internet Things J. (2020) Lai C. et al. Security and privacy challenges in 5G-enabled vehicular networks IEEE Netw. (2020) Yang A. et al. Delegating authentication to edge: A decentralized authentication architecture for vehicular networks IEEE Trans. Intell. Transp. Syst. (2020) Liu H. et al. Blockchain empowered cooperative authentication with data traceability in vehicular edge computing IEEE Trans. Veh. Technol. (2020) Wu C. et al. Collaborative learning of communication routes in edge-enabled multi-access vehicular environment IEEE Trans. Cogn. Commun. Netw. (2020) Gao L. et al. Multi-channel blockchain scheme for internet of vehicles IEEE Open J. Comput. Soc. (2021) Buda S. et al. Empowering blockchain in vehicular environments with decentralized edges IEEE Access (2020) Zhou Z. et al. Secure and efficient vehicle-to-grid energy trading in cyber physical systems: Integration of blockchain and edge computing IEEE Trans. Syst. Man Cybern. Syst. (2019) Stübs M. et al. Blockchain-based multi-tier double auctions for smart energy distribution grids Chen S. et al. A framework of decentralized electricity market based on the collaborative mechanism of blockchain and edge computing Ren Y. et al. A novel authentication scheme based on edge computing for blockchain-based distributed energy trading system EURASIP J. Wireless Commun. Networking (2020) Wang J. et al. Blockchain-based anonymous authentication with key management for smart grid edge computing infrastructure IEEE Trans. Ind. Inform. (2019) Gai K. et al. Permissioned blockchain and edge computing empowered privacy-preserving smart grid networks IEEE Internet Things J. (2019) Zhang K. et al. Edge intelligence and blockchain empowered 5G beyond for the industrial internet of things IEEE Netw. (2019) View more references Cited by (0) Recommended articles (6) Research article A two-dimensional sharding model for access control and data privilege management of blockchain Simulation Modelling Practice and Theory, Volume 122, 2023, Article 102678 Show abstract This paper presents a method to manage private data stored on a blockchain. With our method, the blockchain’s features for log transparency and tamper-resistance are maintained, even though the data is only available to authorized users. The most relevant work so far randomly selects nodes to store the decryption key shares of a threshold cryptosystem for some data which are not maintained in the system. They provide the decryption keys to the data requester via on-chain methods. This is for guaranteeing the availability and distributing the incentives. If the system maintains the data and wants to achieve the same guarantees, it has to post the data to the blockchain. This will make the blockchain oversized and the work impractical. This paper shows that nodes in our method may provide data to the requester directly without posing to the blockchain while guaranteeing availability and that the incentives be fairly distributed. Furthermore, each data request incurs a tiny size of transactions. We achieve so by implementing a two-dimensional sharding model, where nodes are randomly assigned to shards. Data is arithmetically compressed and then split into pieces. Each data piece is stored by a node in a first dimension shard. Without getting all the pieces, the data cannot be successfully decompressed. Each node in the first dimension shard is monitored by a second dimension shard. We propose designs that empower the corresponding second dimension shard for evaluating whether the first dimension node has provided the correct data piece to the data requester. This waives the need for placing the data into transactions and being witnessed by all. In case when a first dimension node fails, its data will be recovered by the corresponding second dimension shard. Research article Designing secure business processes for blockchains with SecBPMN2BC Future Generation Computer Systems, Volume 141, 2023, pp. 382-398 Show abstract Collaborative business processes can be seen as smart contracts, as they are oftentimes adopted to express agreements among different organizations. Indeed, they provide mechanisms to formalize the obligations of each involved party. For instance, collaborative business processes can specify when a certain task should be executed, under which conditions a service should be offered to the other participants, and how physical objects and information should be manipulated. In this setting, to prevent misuse of smart contracts and services and information provided, it is paramount to guarantee by design that security requirements are fulfilled. With the rise in popularity of blockchains, several approaches exploiting the trusted smart contract execution environment offered by this technology to enforce collaborative business processes have been proposed. Yet, the complexity of business processes, security requirements, and blockchain applications calls for an engineering approach that guides the design of secure business processes. Such an approach should both take advantage of the possibilities offered by blockchain technology to enforce some security requirements (e.g., non-repudiation), and take into account the limitations blockchain poses for other security requirements (e.g., confidentiality). However, we are not aware of any existing work that aims at addressing such issues following a similar approach. In this article, we propose SecBPMN2BC: a model-driven approach to designing business processes with security requirements that are meant to be deployed on blockchains. SecBPMN2BC consists of: (i) an extension of BPMN 2.0 that allows designing secure smart contracts; (ii) a set of algorithms and their implementation that check incompatible security requirements and help the design of smart contracts; (iii) a workflow that guides the application of the method. The method has been validated with a survey conducted on security and BPMN experts. Research article Elliptic curve threshold signature scheme for blockchain Journal of Information Security and Applications, Volume 70, 2022, Article 103345 Show abstract Smart contract execution requires the triggering condition. Triggering condition of smart contract is the information outside the blockchain, and thus an oracle is required to provide the data services. For the security of user funds in on-chain aggregation model, each oracle must pay the gas fees to transmit off-chain data to the blockchain and trigger the smart contract, but this way easily causes the network congestion. In view of this, we propose an elliptic curve threshold signature scheme for blockchain (BC-ECTSS) to overcome the above issue. In the random oracle (RO) model, we detailedly show BC-ECTSS satisfies the existential unforgeability against the adaptive chosen-message attacks; in addition, it has the anonymity and its computation cost is relatively low. Research article GP-NFSP: Decentralized task offloading for mobile edge computing with independent reinforcement learning Future Generation Computer Systems, Volume 141, 2023, pp. 205-217 Show abstract In Mobile Edge Computing (MEC), offloading tasks from mobile devices to edge servers may accelerate the processing speed and save the energy of the devices, hence improving device users’ quality of experience. Recently, reinforcement learning (RL) is increasingly used for offload decision making. RL seeks long-term cumulative benefits and is proved useful for a sequence of decisions, thus is well suited for the work. Due to privacy and security concerns, mobile devices may be unwilling to expose their local information, leading to a fully decentralized MEC environment. Independent RL (IRL) emerges as a promising solution for this scenario. However, IRL solutions are faced with the non-stationarity issue, which arises when the components are changing their policies. In this paper, we proposing adopting the Neural Fictitious Self-Play (NFSP) architecture for offload decision making. NFSP explicitly tackles the non-stationarity issue with the built-in self-play mechanism, and uses a mixed strategy consisting of deep RL and the past average strategy, which is approximated by supervised deep learning. Furthermore, we use the Proximal Policy Optimization (PPO) algorithm as the RL component and exploit the Gated Recurrent Unit (GRU) to deal with the partial-observability issue in fully decentralized MEC. We conduct extensive simulation experiment, the result of which shows that our method outperforms the raw IRL approaches, validating the effectiveness of our proposed method. Research article SI4IoT: A methodology based on models and services for the integration of IoT systems Future Generation Computer Systems, Volume 143, 2023, pp. 132-151 Show abstract The Internet of Things (IoT) is a technology that is growing faster every day due to the large number of platforms and end-devices that are becoming connected to each other. As part of this wide and diverse scenario, developers are now facing various challenges, such as heterogeneity, diversity of communication protocols, discovery of things, and coordination of services, among others. A paradigm that can help to tackle these issues is the model engineering since it allows different elements to be reused which can simplify the work of developers. In this paper, we propose SI4IoT (Service Integration for IoT), a methodology based on MDE (Model-Driven Engineering) for the development of IoT systems. This methodology enables automatic code generation, making it easier for developers to design sophisticated new IoT applications. We focus on a DSL (Domain-Specific Language), a graphic editor, and a set of M2T (Model-to-Text) transformations that generate code for software artifacts on Arduino, Node-Red, Ballerina, and NCL-Lua for deployment on hardware nodes, web services, and DTV (Digital TV). Our proposal consists of a model for the integration of services made up of three layers: physical, logical, and application. To validate our proposal, a Smart Home scenario has been considered, with sensors and actuators which, when combined, allow control of lights and heating. In addition, it allows the user to receive information about their home on television based on the REST services that have been created for the IoT nodes. Research article Secure D2D caching framework inspired on trust management and blockchain for Mobile Edge Caching Pervasive and Mobile Computing, Volume 77, 2021, Article 101481 Show abstract Device-to-Device communication (D2D), combined with edge caching and mobile edge computing, is a promising approach to allow offloading data from the wireless mobile network in light of the growth of the number of connected devices and the increase in traffic. However, user security is still an open issue in D2D communication. Security vulnerabilities remain possible owing to easy, direct and spontaneous interactions between untrusted users as well as mobility. In this article, we establish a Secure D2D Caching framework inspired on trUst management and Blockchain (SeCDUB) to improve the security of D2D communication in video caching, through an approach that combines direct and indirect observations. In addition, blockchain concepts were adapted to the dynamic and restricted scenario of the D2D networks to prevent data interception and the alteration of indirect observations. Two uncertainty mathematical models were used to infer direct and indirect trust values: Bayesian inference and the Dempster Shafer Theory (DST) respectively. In addition, we have designed a clustering scheme that enables a faster and more lightweight blockchain for D2D networks. This ensures overhead will not increase with the number of nodes, and thus offers a scalable security solution. Although the average throughput and packet loss increase when SeCDUB is employed, it is not significant in dense scenarios. Nonetheless, there is an improvement in the goodput for all the accessed network scenarios. He Xue is currently an Assistant Engineer in Sichuan Fire Science and Technology Research Institute of MEM. She received the B.S. and M.S. degrees in Computer Science and Technology and Software Engineering from University of Electronic Science and Technology of China (UESTC) in 2015 and 2018, respectively. Her current research interests include Blockchain, Security and Privacy Protection in the application scenarios of Wireless Networks (e.g., IoT, Edge Computing, and Internet of Vehicles). Dajiang Chen (M’15) is currently an Associate Professor in the School of Information and Software Engineering at University of Electronic Science and Technology of China (UESTC). He was a Post Doctoral Fellow with the Broadband Communications Research (BBCR) group, Department of Electrical and Computer Engineering, University of Waterloo, Canada, from 2015 to 2017. He received the Ph.D. degree in information and communication engineering from UESTC in 2014. Dr. Chen served as the workshop chair for BDEC-SmartCity’19 (in conjunction with IEEE WiMob 2019). He also serves/served as a Technical Program Committee Member for IEEE Globecom, IEEE ICC, IEEE VTC, IEEE WPMC, and IEEE WF-5G. His current research interests include Physical Layer Security, Secure Channel Coding, and Machine Learning and its applications in Wireless Network Security and Wireless Communications. Ning Zhang (M’15-SM’18) is an Associate Professor in the Department of Electrical and Computer Engineering at University of Windsor, Canada. He received the Ph.D. degree in Electrical and Computer Engineering from University of Waterloo, Canada, in 2015. After that, he was a postdoc research fellow at University of Waterloo and University of Toronto, Canada, respectively. His research interests include connected vehicles, mobile EC, wireless networking, and machine learning. He is a Highly Cited Researcher (Web of Science). He received an NSERC PDF award in 2015 and 6 Best Paper Awards from IEEE Globecom in 2014, IEEE WCSP in 2015, IEEE ICC in 2019, IEEE ICCC in 2019, IEEE Technical Committee on Transmission Access and Optical Systems in 2019, and Journal of Communications and Information Networks in 2018, respectively. He serves as an Associate Editor of IEEE Internet of Things Journal, IEEE Transactions on Cognitive Communications and Networking, and IEEE Systems Journal; and a Guest Editor of several international journals, such as IEEE Wireless Communications, IEEE Transactions on Industrial Informatics, %IEEE Transactions on Intelligent Transportation Systems, and IEEE Transactions on Cognitive Communications and Networking. Hong-Ning Dai [SM’16] is currently with the Department of Computer Science at Hong Kong Baptist University, Hong Kong as an associate professor. He obtained the Ph.D. degree in Computer Science and Engineering from Department of Computer Science and Engineering at the Chinese University of Hong Kong. His current research interests include the Internet of Things, big data, and blockchain technology. He has served as associate editors/editors for IEEE Transactions on Intelligent Transportation Systems, IEEE Transactions on Industrial Informatics, Ad Hoc Networks, and Connection Science. He is also a senior member of Association for Computing Machinery (ACM). Keping Yu (Member, IEEE) received the M.E. and Ph.D. degrees from the Graduate School of Global Information and Telecommunication Studies, Waseda University, Tokyo, Japan, in 2012 and 2016, respectively. He was a Research Associate and a Junior Researcher with the Global Information and Telecommunication Institute, Waseda University, from 2015 to 2019 and 2019 to 2020, respectively, where he is currently an Assistant Professor. <sup>☆</sup> This work is jointly supported by NSFC (No. 61872059 and 61502085 ) and Fire and Rescue Department of MEM Science and Technology Program (No. 2021XFCX26). View full text © 2022 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.future.2022.10.029", "PubYear": 2023, "Volume": "144", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Sichuan Fire Science and Technology Research Institute of MEM, Chengdu, China"}, {"AuthorId": 2, "Name": "Da<PERSON> Chen", "Affiliation": "Network and Data Security Key Laboratory, University of Electronic Science and Technology of China, Chengdu, 611731, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Windsor, ON, Canada;Corresponding authors"}, {"AuthorId": 4, "Name": "Hong-Ning Dai", "Affiliation": "Department of Computer Science, Hong Kong Baptist University, Hong Kong"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Global Information and Telecommunication Studies, Waseda University, Japan"}], "References": [{"Title": "Blockchain and edge computing technology enabling organic agricultural supply chain: A framework solution to trust crisis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "153", "Issue": "", "Page": "107079", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A systematic literature review of blockchain and smart contract development: Techniques, tools, and open challenges", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "110891", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Blockchain-assisted handover authentication for intelligent telehealth in multi-server edge computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "102024", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Resource allocation and trust computing for blockchain-enabled edge computing system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "102249", "JournalTitle": "Computers & Security"}, {"Title": "Multi-Channel Blockchain Scheme for Internet of Vehicles", "Authors": "<PERSON>ing <PERSON>; <PERSON><PERSON><PERSON> Wu; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "192", "JournalTitle": "IEEE Open Journal of the Computer Society"}, {"Title": "A Survey of Smart Contract Formal Specification and Verification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "7", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A systematic review of blockchain scalability: Issues, solutions, analysis and future research", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Ray C.C. Cheung", "PubYear": 2021, "Volume": "195", "Issue": "", "Page": "103232", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Towards secure and efficient energy trading in IIoT-enabled energy internet: A blockchain approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "686", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Blockchain for Internet of Energy management: Review, solutions, and challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "395", "JournalTitle": "Computer Communications"}, {"Title": "Unmanned aerial vehicle for internet of everything: Opportunities and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "155", "Issue": "", "Page": "66", "JournalTitle": "Computer Communications"}, {"Title": "DecChain: A decentralized security approach in Edge Computing based on Blockchain", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "113", "Issue": "", "Page": "363", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 97089894, "Title": "Robust principal component analysis based on tensor train rank and Schatten p-norm", "Abstract": "<p>For a given data, robust principal component analysis (RPCA) aims to exactly recover the low-rank and sparse components from it. To date, as the convex relaxations of tensor rank, a number of tensor nuclear norms have been defined and applied to approximate the tensor rank because of their convexity. However, those relaxations may make the solution seriously deviate from the original solution for real-world data recovery. In this paper, we define the tensor Schatten p -norm based on tensor train rank and propose a new model for tensor robust principal component analysis (named \\(\\hbox {TT}S_{p}\\) ). We solve the proposed model iteratively by using the ADMM algorithm. In addition, a tensor augmentation tool called ket augmentation is introduced to convert lower-order tensors to higher-order tensors to exploit the low-TT-rank structure. We report higher PSNR and SSIM values in numerical experiments to image recovery problems which demonstrate the superiority of our method. Further experiments on real data also illustrate the effectiveness of the proposed method. </p>", "Keywords": "Tensor robust principal component analysis; Tensor train rank; Schatten p-norm; High-dimensional data", "DOI": "10.1007/s00371-022-02699-5", "PubYear": 2023, "Volume": "39", "Issue": "11", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "Peng<PERSON> Zhang", "Affiliation": "School of mathematics and statistics, Hebei University of Economics and Business, Shijiazhuang, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of mathematics and statistics, Hebei University of Economics and Business, Shijiazhuang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of mathematics and statistics, Hebei University of Economics and Business, Shijiazhuang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of mathematics and statistics, Hebei University of Economics and Business, Shijiazhuang, China"}], "References": [{"Title": "Robust principal component analysis: A factorization-based approach with linear complexity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "513", "Issue": "", "Page": "581", "JournalTitle": "Information Sciences"}, {"Title": "<PERSON><PERSON>-p Norm Based Approach for Tensor Completion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "82", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "Low-rank decomposition on transformed feature maps domain for image denoising", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "7", "Page": "1899", "JournalTitle": "The Visual Computer"}, {"Title": "Background segmentation in multicolored illumination environments", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "8", "Page": "2221", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 97090003, "Title": "Elliptical UHF sensor for partial discharge detection", "Abstract": "In this article, simplified geometries for microstrip antennas were evaluated and optimized, to select one appropriate geometry, aiming for its application of partial discharge (PD) detection, as a UHF sensor. To improve the performance of the proposed antenna, cascaded frequency selective surfaces (FSS), using double square loops as a unit cell element, were designed and evaluated. Antenna reflection coefficient measurements without and with the integrated FSS were performed and compared with simulated results. The results show that the insertion of the FSSs did not impact the operational bandwidth of the antenna, which comprises 100% of the characteristic frequency range, of the PD activity (300–1500 MHz). The insertion of the FSSs resulted in an average gain of 4 dBi, concerning the isolated antenna, increasing the sensitivity for PD detection, within the proposed frequency range. We evaluated the PD detection of the structure through measurements in three devices, a bar of a hydro generator, an oil tank with electrodes in the flat-tip configuration, and a potential transformer (PT). The proposed UHF sensor for PD detection could detect PD activity in all three scenarios and for levels lower than 10 mV.", "Keywords": "Microstrip antenna ; Frequency selective surfaces ; Partial discharge ; UHF sensor", "DOI": "10.1016/j.sna.2022.113981", "PubYear": 2022, "Volume": "348", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Rio Grande do Norte (UFRN), Natal, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal University of Rio Grande do Norte (UFRN), Natal, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Federal Institute of Paraiba (IFPB) , João P<PERSON>oa, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Federal University of Rio Grande do Norte (UFRN), Natal, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Federal University of Campina Grande (UFCG), Campina Grande, Brazil"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Federal University of Campina Grande (UFCG), Campina Grande, Brazil"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Federal University of Campina Grande (UFCG), Campina Grande, Brazil"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Campina Grande (UFCG), Campina Grande, Brazil"}], "References": []}, {"ArticleId": 97090006, "Title": "Simulation based model for component replenishment in multi-product ATO systems with shared resources", "Abstract": "With increasing product complexity, sophistication and customization, Assemble-to-Order (ATO) systems have gained a lot of popularity in recent years. ATO systems have the advantage of delivering customer orders at shorter leadtimes by manufacturing components to stock. However, for an on-time delivery of the final assembled product, the corresponding components must be replenished and be available when needed for assembly in a timely yet cost-effective manner. This research investigates the production and subcontracting decisions in the multi-product ATO systems. We also provide insights on resource allocation decisions among various components and how does randomness in the service times impact these decisions. Using, Monte Carlo simulation approach, we identified that when the manufacturer is cheaper the direction towards optimality is by having the threshold values kept close to the base stock level for all components.", "Keywords": "Assemble-to-Order ; simulation ; subcontractor ; Threshold-based policy", "DOI": "10.1080/02286203.2022.2129345", "PubYear": 2023, "Volume": "43", "Issue": "6", "JournalId": 4075, "JournalTitle": "International Journal of Modelling and Simulation", "ISSN": "0228-6203", "EISSN": "1925-7082", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Industrial and Manufacturing Systems Engineering, Kansas State University, Manhattan, KS, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Industrial and Manufacturing Systems Engineering, Kansas State University, Manhattan, KS, USA"}], "References": []}, {"ArticleId": 97090011, "Title": "Multifocal hyperspectral Raman imaging setup for multi-well plates", "Abstract": "High-throughput screening has become widespread in drug discovery. This increases the need for efficient, parallelized spectral imaging techniques. However, current designs focus on microplate readers without the capability of measuring hyperspectral images or microscopy setups. In this work, a novel setup for parallelized hyperspectral Raman spectroscopy is presented. Utilizing a unique combination of a diffractive element and a micro-lens array in a tailored optical setup, it was possible to acquire 64 high-resolution Raman spectra from a multi-well plate simultaneously. This allows an enormous reduction in measurement time in comparison to sequential readers. Thus, the presented parallelized hyperspectral Raman spectroscopic setup provides a time saving way for modern high-throughput screening applications and the advantage of chemically selective real-time comparison between the different micro-well spots.", "Keywords": "Hyperspectral Raman imaging ; Microplate reader ; Parallelized Raman spectroscopy ; High throughput screening ; Optical design", "DOI": "10.1016/j.snb.2022.132949", "PubYear": 2023, "Volume": "375", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Leibniz Institute of Photonic Technology, Jena, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Leibniz Institute of Photonic Technology, Jena, Germany;Abbe Center of Photonics, Friedrich Schiller University, Jena, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Leibniz Institute of Photonic Technology, Jena, Germany;Abbe Center of Photonics, Friedrich Schiller University, Jena, Germany;Biophotonics and Biomedical Engineering Group, Technical University Darmstadt, Merckstraße 25, 64283 Darmstadt, Germany;Corresponding author at: Leibniz Institute of Photonic Technology, Jena, Germany"}], "References": []}, {"ArticleId": 97090013, "Title": "Design and performance evaluation of a magnetorheological valve with mosquito-coil-plate fluid flow channels", "Abstract": "This work proposed and fabricated a magnetorheological (MR) valve with mosquito-coil-plate fluid flow channels. By setting non-magnetic mosquito-coil-plate arc baffles in the radial fluid flow channels of the conventional radial MR valve, MR fluid was forced to flow circumferentially in the mosquito-coil-plate fluid flow channels surrounded by arc baffles. Thus, the effective fluid flow channels of the proposed MR valve were significantly lengthened in the case of fixed volume, and the radial resistance gaps could be easily adjusted by replacing valve cores with different thicknesses. In order to investigate the distributions of the magnetic flux lines and the changes of the magnetic flux density of the MR valve accurately, the 2D and 3D finite element models of the designed MR valve were established by using ANSYS software. Moreover, pressure drop and response time of the MR valve were experimentally evaluated, and the experimental results show that the designed MR valve can not only output larger pressure drop, but also achieve rapid response time. In addition, the proposed MR valve was used as control unit in a valve controlled cylinder system, and its damping performances were tested separately under different applied currents and harmonic excitations with different amplitudes and frequencies. The test results indicate that the proposed MR valve controlled cylinder system can output reliable damping forces in a variety of operating modes.", "Keywords": "MR valve ; Mosquito-coil-plate fluid flow channels ; Pressure drop ; Response time ; Damping performance", "DOI": "10.1016/j.sna.2022.113983", "PubYear": 2022, "Volume": "347", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Conveyance and Equipment, Ministry of Education, East China Jiaotong University, Nanchang, Jiangxi 330013, People’s Republic of China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Conveyance and Equipment, Ministry of Education, East China Jiaotong University, Nanchang, Jiangxi 330013, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Conveyance and Equipment, Ministry of Education, East China Jiaotong University, Nanchang, Jiangxi 330013, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Conveyance and Equipment, Ministry of Education, East China Jiaotong University, Nanchang, Jiangxi 330013, People’s Republic of China"}], "References": [{"Title": "A Concentric Design of a Bypass Magnetorheological Fluid Damper with a Serpentine Flux Valve", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "16", "JournalTitle": "Actuators"}, {"Title": "Accurate and fast estimation for field-dependent nonlinear damping force of meandering valve-based magnetorheological damper using extreme learning machine method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "318", "Issue": "", "Page": "112479", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 97090040, "Title": "Adaptive scheduled partitioning technique for reliable emergency message broadcasting in VANET for intelligent transportation systems", "Abstract": "This paper aims to enable accurate and reliable emergency message broadcast in Vehicular Ad hoc Network (VANET). The VANET is the most common topology used in Intelligent Transportation Systems (ITS), where changes in standard topology due to the mobility of nodes create challenges in broadcasting the emergency message and efficient data delivery in both highway and urban scenarios. The main problems in urban scenarios are channel contention, message redundancy and road structure. To obtain information, broadcast protocols for VANET typically use beacon messages, which are distributed among the vehicles. When multiple vehicles transmit messages at the same time, a broadcast storm occurs and vehicles experience message delivery failure. To address this problem, Adaptive Scheduled Partitioning and Broadcasting Technique (ASPBT) for emergency message broadcast and beacon retransmissions for message reliability were proposed. This protocol dynamically modifies several partitions and beacon periodicity to reduce the number of retransmissions. Later, the partition size is determined by estimating the network transmission density of each partition schedule via the Black Widow Optimization (BWO) algorithm is proposed. The simulation is carried out with different network densities at the vehicle speed of 110 km/h, a direct path length of 12 km under a four-way direction path and performance analysis was performed.", "Keywords": "Vehicular ad hoc network (VANET) ; broadcasting messages ; beacon ; black widow optimization ; adaptive scheduled partitioning and broadcasting technique", "DOI": "10.1080/00051144.2022.2140392", "PubYear": 2023, "Volume": "64", "Issue": "2", "JournalId": 7651, "JournalTitle": "Automatika", "ISSN": "0005-1144", "EISSN": "1848-3380", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Hindusthan College of Engineering and Technology, Coimbatore, TN, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Hindusthan College of Engineering and Technology, Coimbatore, TN, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, PSG Institute of Technology and Applied Research, Coimbatore, TN, India"}], "References": [{"Title": "Black Widow Optimization Algorithm: A novel meta-heuristic approach for solving engineering optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103249", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Lion optimization algorithm (LOA)-based reliable emergency message broadcasting system in VANET", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "14", "Page": "10415", "JournalTitle": "Soft Computing"}, {"Title": "A Proposal of Fault Tree Analysis for Embedded Control Software", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "9", "Page": "402", "JournalTitle": "Information"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 97090056, "Title": "Disposable, pressure-driven, and self-contained cartridge with pre-stored reagents for automated nucleic acid extraction", "Abstract": "Nucleic acid extraction is vital in many applications such as molecular diagnostics, genetic engineering, and deoxyribonucleic acid (DNA) sequencing. Although recent advances in nucleic acid extraction have been made to improve the process using microfluidics, it is still necessary to make it easy for end-users by having pre-loaded reagents, eliminating pipetting between steps, and automating the process for point-of-care (POC) testing. Herein, we present a pressure-driven and self-contained cartridge with pre-stored reagents for automating nucleic acid purification. To reduce operational complexity, reagents were transferred through a microfluidic chip using pressurized air stored inside the cartridge instead of an external pump or valving system. After performing cell lysis, the cartridge was inserted into the device, and the nucleic acid was purified automatically within 3 min Escherichia coli ( E. coli ) O157:H7 DNA extracted by our device showed similar concentration, purity, and real-time polymerase chain reaction (qPCR) results as the conventional column-based nucleic acid extraction method. Our device achieved a detection limit of 10<sup>3</sup> CFU for E. coli DNA, which is the same as that obtained using the conventional solid-phase extraction method. This study introduces a novel approach for automating the sample preparation process, which can help in facilitating POC molecular diagnostics.", "Keywords": "Nucleic acid ; Extraction ; Purification ; Bacteria ; Cartridge ; Pathogen detection", "DOI": "10.1016/j.snb.2022.132948", "PubYear": 2023, "Volume": "375", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Le Tran Hu<PERSON>", "Affiliation": "Industry 4.0 Convergence Bionics Engineering, Pukyong National University, Busan 48513, South Korea"}, {"AuthorId": 2, "Name": "Won Han", "Affiliation": "Industry 4.0 Convergence Bionics Engineering, Pukyong National University, Busan 48513, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biology and Biological Engineering, Division of Industrial Biotechnology, Chalmers University of Technology, Gothenburg, Sweden"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Industry 4.0 Convergence Bionics Engineering, Pukyong National University, Busan 48513, South Korea;Department of Biomedical Engineering, Pukyong National University, Busan 48513, South Korea;Corresponding author at: Industry 4.0 Convergence Bionics Engineering, Pukyong National University, Busan 48513, South Korea"}], "References": [{"Title": "Pop-up paper-based and fully integrated microdevice for point-of-care testing of vancomycin-resistant Enterococcus", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "345", "Issue": "", "Page": "130362", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 97090087, "Title": "PSO-SVM-based novel haptic interface controller design", "Abstract": "The key performance issues in the haptic system are stability and transparency. A system’s stability, is defined as minimum oscillations and vibration in the output response as well as in the device itself, whereas transparency is defined as minimum error between the applied force or velocity and executed in the virtual environment (VE) of haptic system. Here, when we control the stability of the system, transparency gets hampered and vice-versa. To overcome this problem, a novel optimal haptic interface controller (HIC) has been designed using modern techniques (neural network, support vector machine, and PSO optimised SVM). This optimal HIC employs the input force provided by the user and the feedback force from the haptic device. The error between the applied force and the feedback force should be minimised to maximise transparency. Moreover, there are always some differences between the theoretical model and the physical model. To ensure the designed controller works in the physical device, an effort also has been made to accommodate the uncertainty and delay, which makes the major difference between simulated and physical models. The results obtained using optimal HIC have been compared with the conventional method, which shows improvement in transparency. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "haptic interface controller; haptic system; HIC; neural network; particle swarm optimisation; PSO; stability; support vector machine; SVM; transparency", "DOI": "10.1504/IJCAET.2022.126599", "PubYear": 2022, "Volume": "17", "Issue": "4", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, National Institute of Technology, Kurukshetra, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, National Institute of Technology, Kurukshetra, India"}], "References": []}, {"ArticleId": 97090100, "Title": "ANALYSIS AND <PERSON>SIG<PERSON> ANDROID-BASED RESPIRATION RATE MONITORING FOR CLASSIFICATION OF RESPIRATION DISORDERS", "Abstract": "<p>Sistem pengawasan pasien rumah sakit yang dilakukan selama ini kebanyakan masih dilakukan secara konvensional yakni dengan sistem mengunjungi pasien berjadwal. Alat pengawasan kondisi pasien tersimpan di dalam ruangan dan bisa dicek hanya saat berada dalam ruangan tersebut. Remote Patient Monitoring (RPM) adalah solusi pemanfaatan teknologi dalam bidang kesehatan yang memungkinkan pasien termonitor secara realtime dan dapat diakses kapan saja. Dalam memonitoring kondisi pasien, salah satu yang perlu terus dipantau adalah respiration rate. Respiration rate ini merupakah salah satu parameter yang paling penting dalam memonitoring pasien karena menjadi penanda kondisi patologis pasien. Dalam pengawasan pasien yang disebut sebagai ABCD Sekunder salah satunya parameter yang menjadi perhatian adalah pernafasan. Melalui tulisan ini telah dibuat suatu perangkat respiration rate monitoring yang dapat diakses secara real time untuk mengimplementasikan konsep RPM. Terdapat juga tambahan fitur yakni perangkat dapat melaporkan hasil monitoring secara detail kondisi normal atau tidaknya respirasi pasien. Perangkat monitoring respirasi yang telah dibuat ini dapat di akses secara real time dengan memanfaatkan jaringan wifi kemudian diterima pada perangkat smartphone sehingga tetap bisa diketahui kondisinya meski tidak berada dalam ruang pasien sekalipun. Data monitoring dapat dilihat lewat visualiasi grafik di smartphone selanjutnya klasifikasi kondisi pasien berdasarkan dari nilai respiration rate yang dihitung. Sistem yang telah dirancang memiliki keakuratan 95,16%. Threshold yang digunakan adalah 27 yang merupakan representasi dari nilai analog sinyal dari sensor. Sistem monitoring respiration rate ini diharapkan dapat digunakan dan dikembangkan untuk membantu dalam memberikan pelayanan yang optimal terutama dalam hal monitoring kondisi pasien.</p>", "Keywords": "RPM;Respiration rate;Android;E-Health", "DOI": "10.29100/jipi.v7i3.3069", "PubYear": 2022, "Volume": "7", "Issue": "3", "JournalId": 59609, "JournalTitle": "JIPI (<PERSON><PERSON><PERSON> da<PERSON>ajaran Informatika)", "ISSN": "", "EISSN": "2540-8984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Telkom University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Telkom University"}], "References": []}, {"ArticleId": 97090113, "Title": "PROTOTYPE PENERAPAN HASIL KOMBINASI KRIPTOGRAFI DIFFIE-HELLMAN, MESSAGE-DIGEST 5 DAN RIVEST CHIPER 4 PADA LAYANAN PESAN SINGKAT SMARTPHONE ANDROID", "Abstract": "Kebutuhan masyarakat dalam saling bertukar informasi sudah tidak dapat dipungkiri lagi. Seiring dengan perkembangan teknologi, masyarakat semakin mudah untuk bertukar informasi. Salah satu contohnya menggunakan layanan pesan singkat (Short Message Service) atau biasa disebut SMS. Layanan ini biasa tersedia pada handphone biasa hingga smartphone, namun sekarang seiring berkembangnya jaman terutama perkembangan teknologi smartphone yang semakin canggih, layanan pesan singkat memiliki beberapa kekurangan yang salah satunya ada pada bagian keamanannya yang masih rendah. Untuk mengatasi masalah tersebut peneliti berencana untuk meningkatkan keamanan pada layanan pesan singkat ini dengan menambahkan algoritma algoritma kriptografi diffie-hellman, message-digest 5, dan rivest chiper 4 pada aplikasi sms berbasis Android. Sehingga, pengguna layanan pesan singkat ini tidak khawatir pesannya akan diketahui oleh oranglain yang tidak berkepentingan. <PERSON><PERSON><PERSON> algoritma kroptografi diffie-hellman, message-digest 5, dan rivest chiper 4 berhasil diimplementasikan pada aplikasi sms android, pesan yang dikirim dan diterima berbentuk chipertext, sehingga orang yang tidak berkepentingan tidak bisa membacanya langsung.", "Keywords": "Android;Implementasi;Kombinasi;Kriptografi;Sms.", "DOI": "10.29100/jipi.v7i3.2881", "PubYear": 2022, "Volume": "7", "Issue": "3", "JournalId": 59609, "JournalTitle": "JIPI (<PERSON><PERSON><PERSON> da<PERSON>ajaran Informatika)", "ISSN": "", "EISSN": "2540-8984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "STMIK LIKMI"}, {"AuthorId": 2, "Name": "Sugiarti Sugiarti", "Affiliation": "STMIK LIKMI"}], "References": []}, {"ArticleId": 97090229, "Title": "Design of Intelligent Stereo Garage System Based on PLC Control", "Abstract": "In today&#x27;s society, the economy is developing rapidly, and people&#x27;s living standards and quality are constantly improving. Now, the car has become a common means of transportation for People&#x27;s Daily travel. More and more families own private cars, but the corresponding urban parking space is increasingly tight. The types and functions of all kinds of cars are growing rapidly, and the demand for parking facilities and sites is also increasing. Parking problem is a static traffic (parking state) problem in the process of urban development, and parking facilities are the main content of urban static traffic. If the balance between the two is confused, then there will be a series of difficult problems such as parking difficulties in the city. As a result, changing plane parking into three-dimensional parking, from a simple mechanical garage into a highly automated computer management of modern intelligent three-dimensional parking, can improve the appreciation, but also improve the utilization rate. In this paper, based on Mitsubishi FX2N series PLC, the design of intelligent stereo garage control system, the system uses the lifting and horizontal parking garage, the use of tray shift to produce vertical channel, the realization of high-rise parking space lifting and accessing vehicles.", "Keywords": "PLC ; intelligent ; stereo garage ; system ; design", "DOI": "10.1016/j.procs.2022.10.014", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hunan Railway Professional Technology College, ZhuZhou, China"}, {"AuthorId": 2, "Name": "Qing <PERSON>", "Affiliation": "Hunan Railway Professional Technology College, ZhuZhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Railway Professional Technology College, ZhuZhou, China"}], "References": []}, {"ArticleId": ********, "Title": "Daily Water Demand Prediction Driven by Multi-source Data", "Abstract": "The best scheduling of water distribution systems may be supported by accurate and trustworthy water demand forecasts, which is a positive assurance for the development of smart cities and smart water services. This paper studies a new multivariate time-series prediction model that is based on the Convolution Neural Network (CNN) and Gate Recurrent Unit(GRU), taking into account the limitations of the prediction of multivariate time series using a single model. According to the regularity of water use during the time period, the characteristics of water users are clustered and classified to form two types of users: tidal type and irregular type. The factors that affect the daily water consumption such as user type, week, water consumption of the day of the previous three weeks, major events, etc. are used as input vectors, and the 2018-2021 resident daily water consumption time series data of a domestic water company is used as the training sample, respectively establish CNN-GRU models.CNN-GRU approach is checked using the root mean square error(RMSE),mean absolute error(MAE),and mean percentage absolute error(MAPE).The results are compared with Long-Short Term Memory (LSTM),CNN and GRU.Results show that CNN-GRU improves water demand prediction. The CNN-GRU model&#x27;s RMSE dropped by around 0.648, 0.82, 0.82 when compared to the LSTM, CNN, and GRU models.the MAE decreased by about 0.418,0.47,0.462;and the MAPE decreased by about 0.722,0.649,0.712.", "Keywords": "Water consumption ; CNN ; GRU ; machine learning ; prediction", "DOI": "10.1016/j.procs.2022.10.020", "PubYear": 2022, "Volume": "208", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Yanbian University, Yanji 133002, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Yanbian University, Yanji 133002, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Yanbian University, Yanji 133002, China"}], "References": []}, {"ArticleId": 97090292, "Title": "An integrated 3D cell-based electrochemical biosensor for neurotoxicity assessment of organophosphorus pesticides", "Abstract": "This work presents an integrated three-dimensional (3D) cell-based electrochemical biosensor to assess the neurotoxicity of organophosphorus pesticides (OPs). In this system, OPs inhibit the activity of intracellular acetylcholinesterase (AChE), and thus the enzymatic hydrolysate 1-naphthol (1-N) presents a declining trend. The fluctuation of the 1-N electrochemical response is proved to be a reliable indicator to evaluate the OPs neurotoxicity. Gelatin methacrylate hydrogel is introduced as a scaffold for the 3D culture of PC12 cells, providing an in vivo-like environment for toxicological evaluation. Meanwhile, zeolite imidazolate [email&#160;protected] layered double hydroxides/multi-walled carbon nanotube composites devolve the screen-printed carbon electrode with significant sensitivity. The cell culture and electrochemical detection processes are integrated into a 3D-printed platform, facilitating the portability and timeliness of testing. Under optimal experimental conditions, the detection limit and linear range of 1-N are found as 0.148 μM and 0.5 – 150 μM, respectively. The proposed sensor is applied to assess the neurotoxicity of three typical OPs (chlorpyrifos, dimethoate, and isocarbophos), and the results are further confirmed with the conventional Ellman&#x27;s method. With the comparatively high sensitivity, reliability, and simplicity provided, the fabricated sensor exhibits broad prospects in the field of neurotoxicity evaluation.", "Keywords": "Three-dimensional cell culture ; PC12 cells ; 1-Naphthyl acetate ; Organophosphorus pesticides ; Neurotoxicity", "DOI": "10.1016/j.snb.2022.132941", "PubYear": 2023, "Volume": "376", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China;International Joint Research Laboratory of Intelligent Agriculture and Agri-products Processing, Jiangsu University, Zhenjiang 12013, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China;International Joint Research Laboratory of Intelligent Agriculture and Agri-products Processing, Jiangsu University, Zhenjiang 12013, PR China"}, {"AuthorId": 3, "Name": "Xinai Zhang", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China;International Joint Research Laboratory of Intelligent Agriculture and Agri-products Processing, Jiangsu University, Zhenjiang 12013, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China;International Joint Research Laboratory of Intelligent Agriculture and Agri-products Processing, Jiangsu University, Zhenjiang 12013, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China;International Joint Research Laboratory of Intelligent Agriculture and Agri-products Processing, Jiangsu University, Zhenjiang 12013, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Food Science and Technology, Henan University of Technology, Zhengzhou 450001, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON> Shi", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China;International Joint Research Laboratory of Intelligent Agriculture and Agri-products Processing, Jiangsu University, Zhenjiang 12013, PR China"}, {"AuthorId": 8, "Name": "Quancai Sun", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China;International Joint Research Laboratory of Intelligent Agriculture and Agri-products Processing, Jiangsu University, Zhenjiang 12013, PR China;School of Medicine, University of Leeds, United Kingdom"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China;International Joint Research Laboratory of Intelligent Agriculture and Agri-products Processing, Jiangsu University, Zhenjiang 12013, PR China;Corresponding authors at: School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China;International Joint Research Laboratory of Intelligent Agriculture and Agri-products Processing, Jiangsu University, Zhenjiang 12013, PR China;Corresponding authors at: School of Food and Biological Engineering, Jiangsu University, Zhenjiang 212013, PR China"}], "References": [{"Title": "A nitrile-mediated SERS aptasensor coupled with magnetic separation for optical interference-free detection of atrazine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129075", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 97090311, "Title": "Digital Avatars: A programming framework for personalized human interactions through virtual profiles", "Abstract": "Technology is evolving in the direction of making the users a key part in the model, developing services that adapt to their preferences and needs in a seamless way. So, information and knowledge about people has become one of the main actives for the IT industry. However, only a few major companies are presently able to take advantage of the digital footprint everyone leaves while performing their daily activities. Moreover, people cannot even decide when, how, and by whom the contents and information they are producing are used. In this work, we present the Digital Avatars framework for mobile-based collaborative social computing applications as a realization of People as a Service, a mobile computing model that empowers smartphones with the capability of learning virtual profiles or digital avatars of their owners and sharing them as a service in a controlled and seamless way. Digital Avatars offers services for third party applications, including an inference engine and a module for run-time scripts execution for interacting with the digital avatar in the smartphone assuring the privacy and full control of the users over their data. In this paper, we describe the architecture of this framework and the structure of the digital avatars focusing on an independent living scenario for Mild Cognitive Impairment patients.", "Keywords": "Social computing ; Mild cognitive impairment ; People as a service ; Digital Avatars ; Independent living ; Virtual profiles", "DOI": "10.1016/j.pmcj.2022.101718", "PubYear": 2022, "Volume": "87", "Issue": "", "JournalId": 4135, "JournalTitle": "Pervasive and Mobile Computing", "ISSN": "1574-1192", "EISSN": "1873-1589", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad de Castilla - La Mancha, Ciudad Real, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad de Castilla - La Mancha, Ciudad Real, Spain"}, {"AuthorId": 3, "Name": "Carlos Canal", "Affiliation": "ITIS Software, Universidad de Malaga, Malaga, Spain"}], "References": [{"Title": "Modelling digital avatars: A tuple space approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "203", "Issue": "", "Page": "102583", "JournalTitle": "Science of Computer Programming"}]}, {"ArticleId": ********, "Title": "Systematic and Unsystematic Determinants of Sectoral Risk Default Interconnectedness", "Abstract": "<p>Assessing the financial stability of the banking industry, particularly in credit risk management, has become extremely crucial in times of uncertainty. Given that, this paper aims to investigate the determinants of the interconnectedness of sectoral credit risk default for developing countries. To that purpose, we employ a dynamic credit risk model that considers a variety of macroeconomic indicators, bank-specific variables, and household characteristics. Moreover, the SURE model is used to analyze empirical data. We find the connection between macroeconomic, bank-specific, and household characteristics, and sectoral default risk. The outcomes of macroeconomic factors demonstrate that few macroeconomic determinants significantly influence the sector's default risk. The empirical results of household components reveal that educated households play a substantial role in decreasing sectoral loan defaults interconnectedness and vice versa. While for bank-specific characteristic, we find that greater bank profitability and specialization have substantially reduced loan defaults.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2022, Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "Bank loans;Credit risk;Default risk;Regulation", "DOI": "10.1007/s10614-022-10336-5", "PubYear": 2023, "Volume": "62", "Issue": "2", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Inseec Grande École, Omnes Education Group, Paris, France."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "OCRE, EDC Paris Business School, Paris, France."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Rabat Business School, International University of Rabat, Rabat, Morocco."}], "References": []}, {"ArticleId": ********, "Title": "Multi-objective reinforcement learning framework for dynamic flexible job shop scheduling problem with uncertain events", "Abstract": "The economic benefits for manufacturing companies will be influenced by how it handles potential dynamic events and performs multi-objective real-time scheduling for existing dynamic events. Based on these, we propose a new dynamic multi-objective flexible job shop scheduling problem (DMFJSP) to simulate realistic production environment. Six dynamic events are involved in the problem including job insertion, job cancellation, job operation modification, machine addition, machine tool replacement and machine breakdown. As well as three objectives of longest job processing time (makespan), average machine utilization and average job processing delay rate with a set of constraints are also raised in the study. Then this research designs a novel dynamic multi-objective scheduling algorithm based on deep reinforcement learning. The algorithm uses two deep Q-learning networks and a real-time processing framework to process each dynamic event and generate complete scheduling scheme. In addition, an improved local search algorithm is adopted to further optimize the scheduling results and the idea of combination is used to make the scheduling rules more comprehensive. Experiments on 27 instances show the superiority and stability of our approach compared to each proposed combined rule, well-known scheduling rules and standard deep Q-learning based algorithms. Compared to the current optimal deep Q-learning method, the maximum performance improvement for our three objectives are approximately 57%, 164% and 28%.", "Keywords": "", "DOI": "10.1016/j.asoc.2022.109717", "PubYear": 2022, "Volume": "131", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Sichuan University, Chengdu 610065, China;Second Research Institute of Civil Aviation Administration, Chengdu 610041, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON> Hu", "Affiliation": "School of Computer Science, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Sichuan University, Chengdu 610065, China;Institute for Industrial Internet Research, Sichuan University, Chengdu 610065, China;Corresponding author at: School of Computer Science, Sichuan University, Chengdu 610065, China"}], "References": [{"Title": "Dynamic scheduling for flexible job shop with new job insertions by deep reinforcement learning", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106208", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Pareto based discrete Jaya algorithm for multi-objective flexible job shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "170", "Issue": "", "Page": "114567", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Deep Reinforcement Learning Based Solution for Flexible Job Shop Scheduling Problem", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "2", "Page": "375", "JournalTitle": "International Journal of Simulation Modelling"}, {"Title": "Dynamic multi-objective scheduling for flexible job shop by deep reinforcement learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "159", "Issue": "", "Page": "107489", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Semiconductor final testing scheduling using Q-learning based hyper-heuristic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115978", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A self‐learning artificial bee colony algorithm based on reinforcement learning for a flexible job‐shop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "4", "Page": "e6658", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 97090388, "Title": "Prediction of bolt missing fault for multistage rotor by experimental test and analysis", "Abstract": "<p>The high-pressure rotor of aero-engine is assembled by numerous bolts under high manufacture precision. The connected structure is subjected to both axial force and transverse vibration during service, which may result in individual bolt loosen. In this study, the influence of bolt missing on the dynamic characteristics is analyzed by numerical simulation. A test rig capable of impact and frequency sweeping experiment under axial tension was constructed. The vibration response features in the simulation were then extracted. The loss function of the mean absolute error and the decision method of extreme gradient boosting were used to predict the bolt missing position. The results show that the proposed model can reach a prediction precision of more than 90%. Moreover, the coefficient of determination evaluation index of the prediction effect reaches 0.9, which is significantly higher than those of other conventional models such as multivariate linear regression and multivariate adaptive regression spliness.</p>", "Keywords": "Manufacturing precision; Vibration feature; Intelligent prediction; Bolt fastening", "DOI": "10.1007/s00170-022-10356-3", "PubYear": 2023, "Volume": "124", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Logistics Engineering College, Shanghai Maritime University, Shanghai, China; AECC Shanghai Commercial Aircraft Engine Manufacturing CO., LTD, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Logistics Engineering College, Shanghai Maritime University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Logistics Engineering College, Shanghai Maritime University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Logistics Engineering College, Shanghai Maritime University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Logistics Engineering College, Shanghai Maritime University, Shanghai, China"}], "References": [{"Title": "A New assembly precision prediction method of aeroengine high-pressure rotor system considering manufacturing error and deformation of parts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "112", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 97090512, "Title": "A Comparative Study of Particle Swarm Optimized Control Techniques for Active Suspension System", "Abstract": "This work presents a comparative study for performance evaluation of quarter vehicle Active Suspension System (ASS) using different control techniques. Linear and non-linear mathematical models of the systems are presented and controlled using Proportional Integral Derivative (PID), Feedback Linearization, and Fuzzy Logic Control (FLC). Particle Swarm Optimization (PSO) technique is applied to optimize the gains of the implemented controllers. A simulation process is done using MATLAB/SIMULINK and the results show that the proposed PSO-optimized PID controller has enhanced the root mean square (r.m.s) values of ride comfort, load carrying, and road holding of the linear suspension system by 26.82%, 20.95%, and 22.72% respectively compared to the linear passive suspension system. Whereas, for the nonlinear suspension system, the PSO-optimized FLC has provided better compromise of the performance criteria. The controller has improved the r.m.s values of ride comfort, load carrying, and road handling by 40.61%, 44.21%, and 27.46% respectively compared to the non-linear passive suspension system.", "Keywords": "Active Suspension;Feedback Linearization;Fuzzy Logic Control;Particle Swarm Optimization;PID;Quarter-Vehicle Model", "DOI": "10.15866/ireaco.v15i4.22430", "PubYear": 2022, "Volume": "15", "Issue": "4", "JournalId": 27633, "JournalTitle": "International Review of Automatic Control (IREACO)", "ISSN": "1974-6059", "EISSN": "1974-6067", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering and Technology, Future University in Egypt (FUE)"}], "References": []}, {"ArticleId": 97090530, "Title": "Theoretical modelling and experimental investigation on a frequency up-converted nonlinear piezoelectric energy harvester", "Abstract": "Ambient vibration energy harvesting has attracted the great attention of numerous researchers to replace the battery power supply strategy of electronic devices. However, the high natural frequency and narrow broadband operation are still significant shortcomings of the conventional vibration energy harvesters to be widely deployed. This paper presents a novel piezoelectric energy harvester (PEH) based on the traditional impact frequency up-converted PEH to achieve lower and broader operating frequency. Nonlinear magnetic force is adopted to regulate the working frequency of PEH. Distributed-parameter modelling and magnetic force simplified modelling are applied in analyzing the piecewise linear dynamic characteristics of the low-frequency driving beam, and the piezoelectric effect is employed for calculating the output voltage of the high-frequency generating beams. Theoretical results comparison between the traditional impact PEH and the proposed novel PEH shows that the introduced nonlinear magnetic force offers a lower initial frequency and broader operating bandwidth. The comparison experimental study achieves a close match to the theoretical results. Under excitation conditions of 3 m/s<sup>2</sup> excitation acceleration, 3 mm impact distance, and 19 mm magnet distance, the initial frequency decreases by about 9.5 Hz, and the operating bandwidth increases from 9 Hz to 16.5 Hz. The maximum power output value of the proposed PEH is 0.491 mW. The novel proposed PEH in this paper provides promising guidance and understanding for energy harvesting in low and broadband environmental vibration.", "Keywords": "Piecewise-linear ; Nonlinear magnetic force ; Bandwidth ; Vibration ; Piezoelectric energy harvester", "DOI": "10.1016/j.sna.2022.113979", "PubYear": 2022, "Volume": "347", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Huanggang Normal University, Huanggang 438000, China;Corresponding author"}, {"AuthorId": 2, "Name": "Zean Lv", "Affiliation": "School of Mechanical and Electrical Engineering, Huanggang Normal University, Huanggang 438000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Huanggang Normal University, Huanggang 438000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Huanggang Normal University, Huanggang 438000, China"}], "References": [{"Title": "A review on vibration-based piezoelectric energy harvesting from the aspect of compliant mechanisms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "331", "Issue": "", "Page": "112743", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "A review on piezoelectric energy harvesting", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "8", "Page": "1797", "JournalTitle": "Microsystem Technologies"}]}, {"ArticleId": 97090678, "Title": "A collaborative method for code clone detection using a deep learning model", "Abstract": "Code cloning (CC) is the process of copying and reconfiguring a code fragment and using it in another part of a software project. This clones increases the running overhead of the software. As a result, Code Clone Detection (CCD) has become an active research area in software development research. The detection of Large-Variance Code Clones (LV-CCs) is very difficult when the lines of codes (LOCs) in the source code are very large. The distance metrics have been used in LV-CC detection by calculating the distance between training feature sets of source codes and testing feature sets of source codes. However, threshold selection for detecting clones is a challenging issue in distance-based LV-CC detection. To solve this, a Collaborative CCD using Deep Learning (CCCD-DL) is developed in this paper by utilising lexical, syntactic, semantic and structural features for identifying all types of clones together. A lexical feature is extracted from Clone Pairs (CPs) identified by LV-Mapper. Syntactic and semantic features are identified by the Abstract Syntax Tree (AST) and Control Flow Graph (CFG). The structural features are extracted by code size metrics (CZMs) and object-oriented metrics (OOMs). All features are coordinated and fed into the input layer of DNN. The hidden layer then transforms the inputs into the neural vertices in the multi-classification stage using linear transformation preceded by suppressing non-linearity. This process can generate a complicated and non-hypothetical prototype with a weight matrix for fitting the training sequence. Thus, the feed-forward step has been successfully completed. This model then uses back-propagation in the following element to modify the weight matrix based on the training set. Finally, a softmax layer converts the clone detection task into a classification process. The results of the experiments show that the proposed method solves distance-based problems more quickly and effectively than the traditional methods for the CCD.", "Keywords": "Code cloning ; LV-Mapper ; Deep learning ; Feed-forward step ; Softmax layer", "DOI": "10.1016/j.advengsoft.2022.103327", "PubYear": 2022, "Volume": "174", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, PSG College of Arts & Science, Coimbatore, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, PSG College of Arts & Science, Coimbatore, India"}], "References": []}]