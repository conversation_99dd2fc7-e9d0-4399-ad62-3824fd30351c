[{"ArticleId": 94518684, "Title": "Objective Nontechnical Skills Measurement Using Sensor-based Behavior Metrics in Surgical Teams", "Abstract": "Objective <p>The purpose of this study was to identify objective measures that predict surgeon nontechnical skills (NTS) during surgery.</p> Background <p>NTS are cognitive and social skills that impact operative performance and patient outcomes. Current methods for NTS assessment in surgery rely on observation-based tools to rate intraoperative behavior. These tools are resource intensive (e.g., time for observation or manual labeling) to perform; therefore, more efficient approaches are needed.</p> Method <p>Thirty-four robotic-assisted surgeries were observed. Proximity sensors were placed on the surgical team and voice recorders were placed on the surgeon. Surgeon NTS was assessed by trained observers using the NonTechnical Skills for Surgeons (NOTSS) tool. NTS behavior metrics from the sensors included communication, speech, and proximity features. The metrics were used to develop mixed effect models to predict NOTSS score and in machine learning classifiers to distinguish between exemplar NTS scores (highest NOTSS score) and non-exemplar scores.</p> Results <p>NTS metrics were collected from 16 nurses, 12 assistants, 11 anesthesiologists, and four surgeons. Nineteen behavior features and overall NOTSS score were significantly correlated (12 communication features, two speech features, five proximity features). The random forest classifier achieved the highest accuracy of 70% (80% F1 score) to predict exemplar NTS score.</p> Conclusion <p>Sensor-based measures of communication, speech, and proximity can potentially predict NOTSS scores of surgeons during robotic-assisted surgery. These sensing-based approaches can be utilized for further reducing resource costs of NTS and team performance assessment in surgical environments.</p> Application <p>Sensor-based assessment of operative teams’ behaviors can lead to objective, real-time NTS measurement.</p>", "Keywords": "communication;machine learning;prosody;proximity;robotic-assisted surgery;speech", "DOI": "10.1177/00187208221101292", "PubYear": 2024, "Volume": "66", "Issue": "3", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Clemson University, SC, USA;School of Industrial Engineering, Purdue University, West Lafayette, IN, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Surgery, Indiana University School of Medicine, Indianapolis, IN, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Industrial Engineering, Purdue University, West Lafayette, IN, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Industrial Engineering, Purdue University, West Lafayette, IN, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Surgery, Indiana University School of Medicine, Indianapolis, IN, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Surgery, Indiana University School of Medicine, Indianapolis, IN, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Industrial Engineering, Purdue University, West Lafayette, In, USA"}], "References": [{"Title": "Objective Measures of Surgeon Non-Technical Skills in Surgery: A Scoping Review", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "1", "Page": "42", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Using flow disruptions to understand healthcare system safety: A systematic review of observational studies", "Authors": "<PERSON>; <PERSON>; F<PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "", "Page": "103559", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 94518853, "Title": "A Low-Overhead and High-Security Cryptographic Circuit Design Utilizing the TIGFET-Based Three-Phase Single-Rail Pulse Register against Side-Channel Attacks", "Abstract": "<p>Side-channel attack (SCA) reveals confidential information by statistically analyzing physical manifestations, which is the serious threat to cryptographic circuits. Various SCA circuit-level countermeasures have been proposed as fundamental solutions to reduce the side-channel vulnerabilities of cryptographic implementations; however, such approaches introduce non-negligible power and area overheads. Among all of the circuit components, flip-flops are the main source of information leakage. This article proposes a three-phase single-rail pulse register (TSPR) based on the three-independent-gate field effect transistor (TIGFET) to achieve all desired properties with improved metrics of area and security. TIGFET-based TSPR consumes a constant power (MCV is 0.25%), has a low delay (12 ps), and employs only 10 TIGFET devices, which is applicable for the low-overhead and high-security cryptographic circuit design compared to the existing flip-flops. In addition, a set of TIGFET-based combinational basic gates are designed to reduce the area occupation and power consumption as much as possible. As a proof of concept, a simplified advanced encryption algorithm (AES), SM4 block cipher algorithm (SM4), and light-weight cryptographic algorithm (PRESENT) are built with the TIGFET-based library. SCA is implemented on the cryptographic implementations to prove its SCA resilience, and the SCA results show that the correct key of cryptographic circuits with TIGFET-based TSPRs is not guessed within 2,000 power traces.</p>", "Keywords": "", "DOI": "10.1145/3498339", "PubYear": 2022, "Volume": "27", "Issue": "4", "JournalId": 8858, "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems", "ISSN": "1084-4309", "EISSN": "1557-7309", "Authors": [{"AuthorId": 1, "Name": "Yanjiang Liu", "Affiliation": "Information Engineering University"}, {"AuthorId": 2, "Name": "Tongzhou Qu", "Affiliation": "Information Engineering University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Engineering University"}], "References": [{"Title": "Security Threat Analyses and Attack Models for Approximate Computing Systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems"}]}, {"ArticleId": 94518998, "Title": "H-SegMed: A Hybrid Method for Prostate Segmentation in TRUS Images via Improved Closed Principal Curve and Improved Enhanced Machine Learning", "Abstract": "<p>Prostate segmentation is an important step in prostate volume estimation, multi-modal image registration, and patient-specific anatomical modeling for surgical planning and image-guided biopsy. Manual delineation of the prostate contour is time-consuming and prone to inter- and intra-observer variability. Accurate prostate segmentation in transrectal ultrasound images is particularly challenging due to the ambiguous boundary between the prostate and neighboring organs, the presence of shadow artifacts, heterogeneous intra-prostate image intensity, and inconsistent anatomical shapes. Therefore, in this study, we propose a novel hybrid segmentation method (H-SegMed) for accurate prostate segmentation in TRUS images. The method consists of two main steps: (1) an improved closed principal curve-based method was used to obtain the data sequence, in which only few radiologist-defined seed points were used as an approximate initialization; and (2) an enhanced machine learning method was used to achieve an accurate and smooth contour of the prostate. Our results show that the proposed model achieved superior segmentation performance compared with several other state-of-the-art models, achieving an average Dice similarity coefficient, Jaccard similarity coefficient (Ω), and accuracy of 96.5, 95.1, and 96.3%, respectively.</p>", "Keywords": "Accurate prostate segmentation; Transrectal ultrasound; Principal curve; Greedy closed principal curve method; Memory-based adaptive differential evolution; Machine learning", "DOI": "10.1007/s11263-022-01619-3", "PubYear": 2022, "Volume": "130", "Issue": "8", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Health Technology and Informatics, Hong Kong Polytechnic University, Hong Kong SAR, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Medical Imaging, Taizhou People’s Hospital, Taizhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medical Technology, Jiangsu Province Hospital, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Health Technology and Informatics, Hong Kong Polytechnic University, Hong Kong SAR, China"}], "References": [{"Title": "An adaptive fractional-order BP neural network based on extremal optimization for handwritten digits recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "391", "Issue": "", "Page": "260", "JournalTitle": "Neurocomputing"}, {"Title": "Deep Belief Network and Closed Polygonal Line for Lung Segmentation in Chest Radiographs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "5", "Page": "1107", "JournalTitle": "The Computer Journal"}]}, {"ArticleId": 94519067, "Title": "Cold region data accessibility portal for Québec (CRDAP-QC): An integrated, multi-variable and multi-scale data repository for studying cold-region hydrological processes in Québec", "Abstract": "We present an integrated data portal and retrieval system for various variable related to hydrology and cryosphere of Québec, named Cold Region Data Accessibility Portal for Québec (CRDAP-QC). The raw data with which this integrated platform is built are pulled from various publicly available data sources. The platform integrates data variables related to climate (maximum, minimum and mean temperature along with total precipitation), snow accumulation (snow cover and depth) as well as Freeze-Thaw characteristics. The platform enables downloading, visualizing, and comparing these data across various temporal (monthly, seasonal and annual) and spatial scales (from 25 × 25 km<sup>2</sup> grids, to sub-basin and basin, to the whole province). The platform also provides a Printable Document File with summary of the data, an additional set of information on elevation, land-use, and land-cover as well as the location of climate and hydrometric stations within the chosen area. The portal is available in both English and French.", "Keywords": "Earth System variables ; Cold regions ; Climate ; Hydrology ; Freeze-thaw characteristics ; Gridded data ; Snow cover and depth ; Land cover information ; Québec", "DOI": "10.1016/j.dib.2022.108298", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Building, Civil and Environmental Engineering, Concordia University, Montreal, Québec, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Building, Civil and Environmental Engineering, Concordia University, Montreal, Québec, Canada;Department of Earth System Sciences, University of California at Irvine, Irvine, California, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Building, Civil and Environmental Engineering, Concordia University, Montreal, Québec, Canada;Department of Bioresources Engineering, McGill University, Montreal, Québec, Canada"}], "References": []}, {"ArticleId": 94519103, "Title": "GP-based fair power allocation for 6G multi-user power domain NOMA network", "Abstract": "<p>In this paper, a novel geometric progression (GP)-based approach for dynamic power allocation (PA) in a single-carrier multi-user power-domain non-orthogonal multiple access (PD-NOMA) network is proposed. The mathematical analysis of the proposed model furnishes the PA coefficients that satisfy the basic PA as well as successive interference cancellation (SIC) constraints. The model requires a minimum signal-to-noise power ratio (SNR) that depends only on the channel conditions of the strongest-user. With this SNR, the model ensures each of the cluster users experiences at least a constant, desired minimum target throughput. Further, the PA coefficients obtained with the proposed GP-based fair power allocation (FPA) algorithm are analyzed for a multi-user scenario of four, five, and six users, respectively. A comparative analysis of performance metrics, such as the obtained sum-rate, user fairness index, complexity, coefficient’s nature, and the throughput is carried out, that verifies the efficacy of the proposed model. The simulations reveal a fairness index up to unity along with error-free SIC. For a six-user case with a GP ratio of 10, the minimum target throughput unity, and operational SNR 3927.63 (35.94 dB), the proposed algorithm provides a network sum-rate of 16.76 bps and a high fairness index of 91.64%. Furthermore, the geometric progression-based fair power allocation (GPFPA) algorithm provides a trade-off between the sum-rate and fairness index and can be tuned for higher network throughput or higher fairness index. Hence, the proposed model justifies its appropriateness for the sixth-generation (6G) massive connection density and ultra-reliability scenarios.</p>", "Keywords": "Throughput; Capacity; Geometric progression; Jain’s fairness index; Sum-rate", "DOI": "10.1007/s12652-022-03905-x", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ECE Department, Birla Institute of Technology, Ranchi, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ECE Department, Birla Institute of Technology, Ranchi, India"}], "References": [{"Title": "RETRACTED ARTICLE: A novel approach for non-orthogonal multiple access for delay sensitive industrial IoT communications for smart autonomous factories", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "6", "Page": "6865", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Efficient power allocation for NOMA-enabled IoT networks in 6G era", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "", "Page": "101043", "JournalTitle": "Physical Communication"}, {"Title": "Dynamic ambient HetNet for hybrid data communication and transmission in IoT networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "9", "Page": "8899", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A novel approach to efficient resource allocation in load-balanced cellular networks using hierarchical DRL", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "5", "Page": "2887", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Coefficient-Scaling-Based Fair Power Allocation for Multi-User Power-Domain Nonorthogonal Multiple Access Network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "16", "Page": "2150303", "JournalTitle": "Journal of Circuits, Systems and Computers"}]}, {"ArticleId": 94519123, "Title": "Criss-Cross Attention Based Auto Encoder for Video Anomaly Event Detection", "Abstract": "The surveillance applications generate enormous video data and present challenges to video analysis for huge human labor cost. Reconstruction-based convolutional autoencoders have achieved great success in video anomaly detection for their ability of automatically detecting abnormal event. The approaches learn normal patterns only with the normal data in an unsupervised way due to the difficulty of collecting anomaly samples and obtaining anomaly annotations. But convolutional autoencoders have limitations in global feature extraction for the local receptive field of convolutional kernels. What is more, 2-dimensional convolution lacks the capability of capturing temporal information while videos change over time. In this paper, we propose a method established on Criss-Cross attention based AutoEncoder (CCAE) for capturing global visual features of sequential video frames. The method utilizes Criss-Cross attention based encoder to extract global appearance features. Another Criss-Cross attention module is embedded into bi-directional convolutional long short-term memory in hidden layer to explore global temporal features between frames. A decoder is executed to fuse global appearance and temporal features and reconstruct the frames. We perform extensive experiments on two public datasets UCSD Ped2 and CUHK Avenue. The experimental results demonstrate that CCAE achieves superior detection accuracy compared with other video anomaly detection approaches.", "Keywords": "bi-directional long short-term memory; convolutional autoencoder; Criss-Cross attention module; Video anomaly detection", "DOI": "10.32604/iasc.2022.029535", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Nanjing Normal University, Nanjing, 210023, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Electronic Information, Nanjing Normal University, Nanjing, 210023, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Electronic Information, Nanjing Normal University, Nanjing, 210023, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Massachusetts Boston, Boston, 02125, United States"}], "References": [{"Title": "Video anomaly detection and localization via Gaussian Mixture Fully Convolutional Variational Autoencoder", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "102920", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "CNN features with bi-directional LSTM for real-time anomaly detection in surveillance networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "11", "Page": "16979", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A Generative Adversarial Networks for Log Anomaly Detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "135", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "A Multi-Feature Learning Model with Enhanced Local Attention for Vehicle Re-Identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "3549", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Improved Anomaly Detection in Surveillance Videos with Multiple Probabilistic Models Inference", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "3", "Page": "1703", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 94519126, "Title": "Efficient Medical Image Encryption Framework against Occlusion Attack", "Abstract": "Image encryption has attracted a lot of interest as an important security application for protecting confidential image data against unauthorized access. An adversary with the power to manipulate cipher image data can crop part of the image out to prevent decryption or render the decrypted image useless. This is known as the occlusion attack. In this paper, we address a vulnerability to the occlusion attack identified in the medical image encryption framework recently proposed in []. We propose adding a pixel scrambling phase to the framework and show through simulation that the extended framework effectively mitigates the occlusion attack while maintaining the other attractive security features. The scrambling is performed using a separate chaotic map which is securely initialized using a secret key and a random nonce to deter chosen-plaintext attacks. Moreover, we show through simulation that the choice of chaotic map used for scrambling is irrelevant to the effectiveness of the scrambling algorithm against the occlusion attack.", "Keywords": "Medical image encryption; occlusion attack; scrambling", "DOI": "10.32604/iasc.2022.026161", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "May <PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, College of Computers and Information Technology, Taif University, P.O. Box 11099, Taif, 21944, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, College of Computers and Information Technology, Taif University, P.O. Box 11099, Taif, 21944, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, College of Engineering, Taif University, P.O. Box 11099, Taif, 21944, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, College of Engineering, Taif University, P.O. Box 11099, Taif, 21944, Saudi Arabia"}], "References": []}, {"ArticleId": 94519160, "Title": "ICT Solutions in the D-Sys-Com Research: Analysis of the Needs and Attitudes of the Frail Elderly Person", "Abstract": "<p>The introduction and use of innovative technological devices to support the aging of frail elderly people does not necessarily correspond to an improvement in people’s quality of life. The strong technical curvature resulting from the use of telemedicine models often highlights limits in the usability of technologies in responding to the real needs of users. The theoretical framework of special pedagogy allows the assumption of the bio-psycho-social perspective and the constructs of quality of life and participation and opens up to inclusive logics that implement a profound and questioning reflection on all contexts of life, with the goal of exposing the set of disabling processes and indicating a valid support in the use of technological resources. The study, retracing the research phases of the Data System Platform for Smart Communities project (project admitted for funding in the Innolabs 2018–2019 call), completed in 2020, investigates the needs of strategic stakeholders and explores the factors that influence the adoption and diffusion of telemedicine devices by frail elderly people.</p>", "Keywords": "Frail elderly people; Telemedicine; Quality of Life; inclusion; universal design", "DOI": "10.3389/frobt.2022.851473", "PubYear": 2022, "Volume": "9", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for New Technologies for Inclusion and Disabilities, Italy"}], "References": []}, {"ArticleId": 94519172, "Title": "Smart Greenhouse Control via NB-IoT", "Abstract": "The Internet of Things (IoT) has flourished in recent years, which brings convenience to people’s lives, improves the quality of life, allows more effectively managing and maximizing benefits in industry, and improves weather predictions as the impact of global warming has complicated traditional methods to infer the weather. To this end, agriculture has also given more attention to greenhouse cultivation. In the early days of industrial research, Wi-Fi and ZigBee were used as short-or medium-distance communication technologies for transmissions in the network layer of the IoT architecture. Instead of long-distance communication technologies, such as LoRa and NB-IoT, the features of low power consumption and low cost are also more favored by the industry. This article uses the NB-IoT communication module with various sensors to monitor and control a small smart greenhouse, and the data sensed on the network platform is visually presented and recorded. On one hand, the control matches the NB-IoT communication module with the relay module. The fan, LED light, and motor can be controlled by the conditions set on the network platform. Therefore, a smart greenhouse with bidirectional control is realized.", "Keywords": "greenhouse; Internet of thing; NB-IoT; WI-FI; ZigBee", "DOI": "10.32604/iasc.2022.026927", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Chin-Yi University of Technology, Taichung, 411030, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Chin-Yi University of Technology, Taichung, 411030, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Takming University of Science and Technology, Taipei City, 11451, Taiwan"}], "References": []}, {"ArticleId": ********, "Title": "Novel DoS Attack Detection Based on Trust Mode Authentication for IoT", "Abstract": "Wireless sensor networks are extensively utilized as a communication mechanism in the field of the Internet of Things (IoT). Along with these services, numerous IoT based applications need stabilized transmission or delivery over unbalanced wireless connections. To ensure the stability of data packets delivery, prevailing works exploit diverse geographical routing with multi-hop forwarders in WSNs. Furthermore, critical Denial of Service (DoS) attacks frequently has an impact on these techniques, where an enormous amount of invalid data starts replicating and transmitted to receivers to prevent Wireless Sensor Networks (WSN) communication. In this investigation, a novel adaptive endorsement method is designed by combining dimensionality reduction based Hilbert-Huang Transformation (DR-HHT) and authentication trust mode (ATM). DR-HHT and ATM defend against the severity of DoS attacks, by fulfilling trust, reliability, stability requirements. ATM also examines the state information (SI) of nodes in wireless links; this SI leverages the performance of ATM to enhance data delivery effectually. Dissimilar to existing routing protocols, DR-HHT and ATM guarantee data integrity by building Kolmogorov_Smirnov based authentication algorithms. Concerning the correlation coefficient, this model isolates DoS attacks and diminishes computational cost. This strategy also eliminates duplicate data transmission and redundant information, offering an effectual trust-based evaluation model from adaptive authentication. Extensive simulation demonstrates that the anticipated model shows a better trade-off than the prevailing techniques and the simulation is carried out in a MATLAB environment.", "Keywords": "authentication trust model; dimensionality reduction based hilbert-huang transformation; k<PERSON><PERSON><PERSON>_smirnov; reliability; stability; Wireless sensor networks", "DOI": "10.32604/iasc.2022.022151", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Cihan University, Kurdistan Region, Duhok, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, M.I.E.T Engineering College, Tamilnadu, Trichy, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department Electronics and Communication Engineering, SACS MAVMM Engineering College, Tamilnadu, Madurai, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, India"}, {"AuthorId": 6, "Name": "Abolfazl Mehbodniya", "Affiliation": "Department of Electronics and Communications Engineering, Kuwait College of Science and Technology (KCST), Kuwait"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Qala University College, Kurdistan Region, Erbil, Iraq"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Research Center, Labanese French University, Erbil, Iraq"}], "References": []}, {"ArticleId": 94519222, "Title": "Triangle-Based Heuristics for Area Optimal Polygonizations", "Abstract": "In this article, we describe an empirical study conducted on problems of Polygonizations with Optimal Area: given a set\n \n \\( S \\) \n \n of points in the plane, find a simple polygon with minimum (\n Min-Area \n ) or maximum (\n Max-Area \n ) area whose vertices are all the points of\n \n \\( S \\) \n \n . Both problems arise in the context of pattern recognition, image reconstruction, and clustering. Higher dimensional variants play a role in object modeling, as well as optimal surface design. Both\n Min-Area \n and\n Max-Area \n are in NP-hard, and heuristics as well as exact methods have already been proposed. Our main contributions include the design and implementation of novel constructive heuristics and local search procedures to improve the solutions obtained by the former methods for the two problems.", "Keywords": "", "DOI": "10.1145/3504001", "PubYear": 2022, "Volume": "27", "Issue": "", "JournalId": 17156, "JournalTitle": "Journal of Experimental Algorithmics", "ISSN": "1084-6654", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Campinas, Campinas, SP, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Campinas, Campinas, SP, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Campinas, Campinas, SP, Brazil"}, {"AuthorId": 4, "Name": "Cid <PERSON><PERSON>", "Affiliation": "University of Campinas, Campinas, SP, Brazil"}, {"AuthorId": 5, "Name": "Fbio L. <PERSON>", "Affiliation": "University of Campinas, Campinas, SP, Brazil"}], "References": []}, {"ArticleId": 94519261, "Title": "Nonholonomic Reorientation of Free-Flying Space Robots Using Parallelogram Actuation in Joint Space", "Abstract": "A robot with large-degree-of-freedom joints is a promising future space robot capable of adapting to various environments and can perform dexterous tasks with multiple manipulators. The attitude dynamics of a free-flying robot shows nonholonomy, which enables the robot to reorient its attitude by moving its body. However, previous studies were not generally able to handle the nonholonomy, especially for robots with large degrees of freedom of joints. In the present study, we analytically investigate a maneuver in which joints are actuated along a parallelogram trajectory in joint angle space and propose an analytical path modification method in joint angle space in order to achieve the target attitude. Parallelogram actuation guarantees that the body configuration is returned to the initial state after one set of maneuvers, and thus the attitude of the robot is independently reoriented to the target maintaining the body configuration. The analytical solution is provided by applying Magnus expansion to the kinematics equation of rotational matrices, and its Lie group structure contributes to concise mathematical expressions. In addition, the analytical solution does not cause numerical difficulties, such as combinatorial explosion, and so can fully make use of the reorientation ability of a robot with large degrees of freedom. © 2022 by The Authors. Published by the American Institute of Aeronautics and Astronautics, Inc., with permission.", "Keywords": "Free Flying Space Robot; Free Flying Robots; Robotic Manipulators; Attitude Dynamics; Kinematics Equation; Euler Angles; Inverse Problems; Newton <PERSON>on Method; Numerical Simulation; Space Manipulator", "DOI": "10.2514/1.G006511", "PubYear": 2022, "Volume": "45", "Issue": "7", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Japan Aerospace Exploration Agency, Sagamihara 252-5210, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Australian National University, Canberra ACT 2600, Australia"}], "References": [{"Title": "Normal forms and singularities of non-holonomic robotic systems: A study of free-floating space robots", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "104661", "JournalTitle": "Systems & Control Letters"}]}, {"ArticleId": 94519299, "Title": "Data De-Duplication Process and Authentication Using ERCE with Poisson Filter in Cloud Data Storage", "Abstract": "The cloud storage is essential environment for users to access high confidential data. Every single data is most valued by users. If we count, day by day information as well as, memory storage are increasing gradually. Cost of memory will increase when data increases with demand for storage. At present data duplication or redundant data storing in the cloud became hard for storage provider. Also, it makes security issue if repeated data from various users stored in the server. It makes data duplication, which is very efficient for intruders. Also, when same data stored in cloud, there will be a waste of storage. Our research article is focused on security of original data by generating the key from owner and identifying the repeated data, while storing in cloud platform. This process is called as data de-duplication, which is also known as intelligent based computing. Storing the data in single instance is very challenging among cloud computing researchers. In this article we propose a content level de-duplication with re-encryption using enhanced Randomized convergent encryption (ERCE) based on Poisson filter (PF). First the data is encrypted and re-encrypted using the cipher methodology. Generated key only stored and processed by authenticated user. Owner of the data give permission to access key. Then the Poisson filter is used in de-duplication process. if key is authenticated, then the authenticated user can access data from cloud server. Data is stored only once and accessing key decides who can access the data. The result is evaluated with various existing algorithm. our proposed algorithm proves less time in downloading file and less computation cost when comparing with existing system.", "Keywords": "Cloud computing; data redundancy; de-duplication; enhanced randomized convergent encryption; Poisson filter", "DOI": "10.32604/iasc.2022.026049", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Paavai Engineering College, Namakkal, 637018, India"}, {"AuthorId": 2, "Name": "S. Chi<PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON><PERSON> College of Engineering, Hosur, 635117, India"}], "References": [{"Title": "An enhanced secure content de-duplication identification and prevention (ESCDIP) algorithm in cloud environment", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "2", "Page": "485", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 94519306, "Title": "Blockchain-Enabled Digital Rights Management for Museum-Digital Property Rights", "Abstract": "With the rapid development of digitization technology, digital copyright of museum has become more and more valuable. Its collections can be opened to and shared with the people through the Internet. However, centralized authorization, untransparent transaction information and risk of tampering data in traditional digital rights management have a strong impact on system normal operation. In this paper, we proposed a blockchain-based digital rights management scheme (BMDRM) that realizes a distributed digital rights management and authorization system by introducing non-fungible tokens (NFTs) and smart contracts. To ensure the security and efficiency of transactions and authorization, we store all processing data in a high-security distributed ledger based on cryptographic signatures. We test our scheme on Ethereum private network and the experimental results show that BMDRM is feasible and secure for digital rights management in museums.", "Keywords": "blockchain; digital rights management (DRM); Museum; non-fungible tokens (NFTS); smart contract", "DOI": "10.32604/iasc.2022.029693", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, 300350, China; Beijing Computing Center Co., Ltd, Beijing, 100094, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, 300350, China; Tianjin Cultural Heritage Conservation and Inheritance Engineering Technology Center and Key Research Center for Surface Monitoring and Analysis of Relics, State Administration of Cultural Heritage, China; Tianjin, 300350, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Planetarium, Beijing, 100044, China"}], "References": [{"Title": "A Food Traceability Framework Based on Permissioned Blockchain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "2", "Page": "107", "JournalTitle": "Journal of Cyber Security"}, {"Title": "Energy-efficient and Blockchain-enabled Model for Internet of Things (IoT) in Smart Cities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "3", "Page": "2509", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Hyperledger Fabric Blockchain: Secure and Efficient Solution for Electronic Health Records", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "2", "Page": "2377", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A Lightweight Blockchain for IoT in Smart City (IoT-SmartChain)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "2", "Page": "2687", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 94519327, "Title": "Multi-Objective Immune Algorithm for Internet of Vehicles for Data Offloading", "Abstract": "On the Internet of Vehicle (IoV) devices, offloading data is the major problem because massive amounts of data generate energy consumption, and the execution cost is high. At present, accidents traffic management is highly prominent due to increased vehicles among the population. IoV is the only technology to help the transport system effectively. This data outreach the memory also has high energy consumption, and the storage cost is high. To overcome these issues, a Mobility aware Offloading scheme with Multi-Objective Immune Optimization algorithm (MOS-MOIO) is used in the cloud storage. The data is generated from the online sensor system. The generated information must alert the IoV users with a useful data offloading scheme. The result of the data offloading is evaluated with various performance metrics.", "Keywords": "Internet of vehicles; mobility aware offloading scheme; multi-objective immune optimization; offloading; offloading scheme", "DOI": "10.32604/iasc.2022.026779", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Hindusthan College of Engineering and Technology, Coimbatore, 641050, India"}, {"AuthorId": 2, "Name": "<PERSON>. <PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Lebanese French University, Erbil, 44001, Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Information Technology, Sri Ramakrishna Engineering College, Coimbatore, 641022, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Computers, Kharkiv National University of Radio Electronics, Kharkiv Oblast61000, Ukraine"}], "References": [{"Title": "QASEC: A secured data communication scheme for mobile Ad-hoc networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "604", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Transient fault aware application partitioning computational offloading algorithm in microservices based mobile cloudlet networks", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "1", "Page": "105", "JournalTitle": "Computing"}, {"Title": "Mobility-aware task scheduling in cloud-Fog IoT-based healthcare architectures", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "179", "Issue": "", "Page": "107348", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 94519359, "Title": "A Comparative Study of non-deep Learning, Deep Learning, and Ensemble Learning Methods for Sunspot Number Prediction", "Abstract": "Solar activity has significant impacts on human activities and health. One most commonly used measure of solar activity is the sunspot number. This paper compares three important non-deep learning models, four popular deep learning models, and their five ensemble models in forecasting sunspot numbers. In particular, we propose an ensemble model called XGBoost-DL, which uses XGBoost as a two-level nonlinear ensemble method to combine the deep learning models. Our XGBoost-DL achieves the best forecasting performance (RMSE = 25.70 and MAE = 19.82 ) in the comparison, outperforming the best non-deep learning model SARIMA (RMSE = 54.11 and MAE = 45.51 ), the best deep learning model Informer (RMSE = 29.90 and MAE = 22.35 ) and the NASA’s forecast (RMSE = 48.38 and MAE = 38.45 ). Our XGBoost-DL forecasts a peak sunspot number of 133.47 in May 2025 for Solar Cycle 25 and 164.62 in November 2035 for Solar Cycle 26, similar to but later than the NASA’s at 137.7 in October 2024 and 161.2 in December 2034. An open-source Python package of our XGBoost-DL for the sunspot number prediction is available at https://github.com/yd1008/ts_ensemble_sunspot .", "Keywords": "", "DOI": "10.1080/08839514.2022.2074129", "PubYear": 2022, "Volume": "36", "Issue": "1", "JournalId": 7360, "JournalTitle": "Applied Artificial Intelligence", "ISSN": "0883-9514", "EISSN": "1087-6545", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Data Science, New York University, New York, New York, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Theory and Application in Statistics and Data Science-MOE, School of Statistics, East China Normal University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen, Guangdong Province, China"}, {"AuthorId": 4, "Name": "Hai Shu", "Affiliation": "Department of Biostatistics, School of Global Public Health, New York University, New York, USA"}], "References": [{"Title": "A survey on ensemble learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "2", "Page": "241", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Deep Learning for Time Series Forecasting: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "3", "JournalTitle": "Big Data"}, {"Title": "Sunspot Number Prediction Using Gated Recurrent Unit (GRU) Algorithm", "Authors": "Unix I<PERSON>ah <PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "2", "Page": "141", "JournalTitle": "IJCCS (Indonesian Journal of Computing and Cybernetics Systems)"}]}, {"ArticleId": 94519460, "Title": "Improving aspect term extraction via span-level tag data augmentation", "Abstract": "<p>Aspect term extraction (ATE), a fundamental subtask in aspect-based sentiment analysis, aims to extract explicit aspect term from reviewers’ expressed opinions. However, the distribution of samples containing different numbers of aspect terms is long-tailed. Due to the scarcity of long-tailed samples and the existence of multiple variable-length aspect terms inside each sample, most ATE models converge to an inferior state because they have difficulty capturing features. Popular data augmentation techniques used for addressing this problem, such as synonym replacement and back translation, cannot produce substantial improvements when using pretrained language models. In this paper, we present a novel span-level aspect term extraction (SATE) framework, which includes three main components: a simple and effective tag data augmentation (TaDA) module, an original pretrained language model, and an optimized heuristic decoding algorithm module. TaDA is based on a span-level tagging scheme and generates new pseudo training samples for long-tailed multiaspect samples. The pretrained model, deemed a general feature extractor, yields contextual token representations. Then, the decoding algorithm adopts an adjustment factor to extract the variable-length aspect terms simultaneously. All the techniques are seamlessly integrated into the stacked SATE framework to pinpoint the aspect terms. Empirical experiments on SemEval benchmark datasets of multiple domains achieve F <sub>1</sub>-scores of 86.92% and 86.28% for laptops and restaurants, respectively, demonstrating the superiority of our model compared with the well-known baseline models.</p>", "Keywords": "Sentiment analysis; Aspect term extraction; Data augmentation; Pretrained language model", "DOI": "10.1007/s10489-022-03558-5", "PubYear": 2023, "Volume": "53", "Issue": "3", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Computer Science, Sichuan University, Chengdu, China; Technology Innovation Center, Mianyang, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Science, Sichuan University, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer Science, Sichuan University, Chengdu, China"}], "References": [{"Title": "Multi-task learning for aspect term extraction and aspect sentiment classification", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "398", "Issue": "", "Page": "247", "JournalTitle": "Neurocomputing"}, {"Title": "Multi-level knowledge-based approach for implicit aspect identification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "12", "Page": "4616", "JournalTitle": "Applied Intelligence"}, {"Title": "Recommender systems based on generative adversarial networks: A problem-driven perspective", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "1166", "JournalTitle": "Information Sciences"}, {"Title": "Joint aspect terms extraction and aspect categories detection via multi-task learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114688", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Aspect term extraction for opinion mining using a Hierarchical Self-Attention Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "465", "Issue": "", "Page": "195", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 94519477, "Title": "A Proposed Biometric Authentication Model to Improve Cloud Systems Security", "Abstract": "Most user authentication mechanisms of cloud systems depend on the credentials approach in which a user submits his/her identity through a username and password. Unfortunately, this approach has many security problems because personal data can be stolen or recognized by hackers. This paper aims to present a cloud-based biometric authentication model (CBioAM) for improving and securing cloud services. The research study presents the verification and identification processes of the proposed cloud-based biometric authentication system (CBioAS), where the biometric samples of users are saved in database servers and the authentication process is implemented without loss of the users' information. The paper presents the performance evaluation of the proposed model in terms of three main characteristics including accuracy, sensitivity, and specificity. The research study introduces a novel algorithm called \"Bio-Authen-as-a-Service\" for implementing and evaluating the proposed model. The proposed system performs the biometric authentication process securely and preserves the privacy of user information. The experimental result was highly promising for securing cloud services using the proposed model. The experiments showed encouraging results with a performance average of 93.94%, an accuracy average of 96.15%, a sensitivity average of 87.69%, and a specificity average of 97.99%. © 2022 CRL Publishing. All rights reserved.", "Keywords": "biometric authentication; biometrics technologies; Cloud computing; cloud security", "DOI": "10.32604/csse.2022.024302", "PubYear": 2022, "Volume": "43", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Hosam El- El-Sofany", "Affiliation": ""}], "References": []}, {"ArticleId": 94519478, "Title": "Binary Representation of Polar Bear Algorithm for Feature Selection", "Abstract": "In most of the scientific research feature selection is a challenge for researcher. Selecting all available features is not an option as it usually complicates the research and leads to performance drop when dealing with large datasets. On the other hand, ignoring some features can compromise the data accuracy. Here the rough set theory presents a good technique to identify the redundant features which can be dismissed without losing any valuable information, however, exploring all possible combinations of features will end with NP-hard problem. In this research we propose adopting a heuristic algorithm to solve this problem, Polar Bear Optimization PBO is a metaheuristic algorithm provides an effective technique for solving such kind of optimization problems. Among other heuristic algorithms it proposes a dynamic mechanism for birth and death which allows keep investing in promising solutions and keep dismissing hopeless ones. To evaluate its efficiency, we applied our proposed model on several datasets and measured the quality of the obtained minimal feature set to prove that redundant data was removed without data loss. © 2022 CRL Publishing. All rights reserved.", "Keywords": "feature selection; heuristic algorithms; Optimization; rough set", "DOI": "10.32604/csse.2022.023249", "PubYear": 2022, "Volume": "43", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Sakarya University, Computer Engineering Department, Sakarya, Turkey"}, {"AuthorId": 2, "Name": "Numan 莈lebi", "Affiliation": ""}], "References": []}, {"ArticleId": 94519479, "Title": "An Efficient Video Inpainting Approach Using Deep Belief Network", "Abstract": "The video inpainting process helps in several video editing and restoration processes like unwanted object removal, scratch or damage rebuilding, and retargeting. It intends to fill spatio-temporal holes with reasonable content in the video. Inspite of the recent advancements of deep learning for image inpainting, it is challenging to outspread the techniques into the videos owing to the extra time dimensions. In this view, this paper presents an efficient video inpainting approach using beetle antenna search with deep belief network (VIA-BASDBN). The proposed VIA-BASDBN technique initially converts the videos into a set of frames and they are again split into a region of 5*5 blocks. In addition, the VIABASDBN technique involves the design of optimal DBN model, which receives input features from Local Binary Patterns (LBP) to categorize the blocks into smooth or structured regions. Furthermore, the weight vectors of the DBN model are optimally chosen by the use of BAS technique. Finally, the inpainting of the smooth and structured regions takes place using the mean and patch matching approaches respectively. The patch matching process depends upon the minimal Euclidean distance among the extracted SIFT features of the actual and references patches. In order to examine the effective outcome of the VIA-BASDBN technique, a series of simulations take place and the results denoted the promising performance. © 2022 CRL Publishing. All rights reserved.", "Keywords": "beetle antenna search; deep belief network; deep learning; feature extraction; patch matching; Video inpainting; video restoration", "DOI": "10.32604/csse.2022.023109", "PubYear": 2022, "Volume": "43", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, E.G.S. Pillay Engineering College, Tamilnadu, Nagapattinam, 611002, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, E.G.S. Pillay Engineering College, Tamilnadu, Nagapattinam, 611002, India"}], "References": [{"Title": "Dual‐tree complex wavelet transform and super‐resolution based video inpainting application to object removal and error concealment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "4", "Page": "314", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": 94519480, "Title": "Intrusion Detection System for Big Data Analytics in IoT Environment", "Abstract": "In the digital area, Internet of Things (IoT) and connected objects generate a huge quantity of data traffic which feeds big data analytic models to discover hidden patterns and detect abnormal traffic. Though IoT networks are popular and widely employed in real world applications, security in IoT networks remains a challenging problem. Conventional intrusion detection systems (IDS) cannot be employed in IoT networks owing to the limitations in resources and complexity. Therefore, this paper concentrates on the design of intelligent metaheuristic optimization based feature selection with deep learning (IMFSDL) based classification model, called IMFSDL-IDS for IoT networks. The proposed IMFSDL-IDS model involves data collection as the primary process utilizing the IoT devices and is preprocessed in two stages: data transformation and data normalization. To manage big data, Hadoop ecosystem is employed. Besides, the IMFSDL-IDS model includes a hill climbing with moth flame optimization (HCMFO) for feature subset selection to reduce the complexity and increase the overall detection efficiency. Moreover, the beetle antenna search (BAS) with variational autoencoder (VAE), called BAS-VAE technique is applied for the detection of intrusions in the feature reduced data. The BAS algorithm is integrated into the VAE to properly tune the parameters involved in it and thereby raises the classification performance. To validate the intrusion detection performance of the IMFSDL-IDS system, a set of experimentations were carried out on the standard IDS dataset and the results are investigated under distinct aspects. The resultant experimental values pointed out the betterment of the IMFSDL-IDS model over the compared models with the maximum accuracy 95.25% and 97.39% on the applied NSL-KDD and UNSW-NB15 dataset correspondingly. © 2022 CRL Publishing. All rights reserved.", "Keywords": "Big data; Cybersecurity; Deep learning; Intelligent systems; Intrusion detection; IoT networks; Metaheuristics", "DOI": "10.32604/csse.2022.023321", "PubYear": 2022, "Volume": "43", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, St<PERSON>’s College of Engineering, Chennai, 600119, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University College of Engineering, Thatchur, Arni, 632326, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, Kings College of Engineering, Pudukkottai, 613303, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, KPR Institute of Engineering and Technology, Coimbatore, 641407, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, SRM Institute of Science and Technology, Chennai, 603203, India"}], "References": [{"Title": "RETRACTED ARTICLE: Intelligent hybrid model for financial crisis prediction using machine learning techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "4", "Page": "617", "JournalTitle": "Information Systems and e-Business Management"}, {"Title": "The rise of traffic classification in IoT networks: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "102538", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Modelling the map reduce based optimal gradient boosted tree classification algorithm for diabetes mellitus diagnosis system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "1717", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Beetle Swarm Optimization Algorithm-Based Load Control with Electricity Storage", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "", "Page": "1", "JournalTitle": "Journal of Control Science and Engineering"}]}, {"ArticleId": 94519481, "Title": "Assessing Asian Economies Renewable Energy Consumption Efficiency Using DEA with Undesirable Output", "Abstract": "Along with vast non-fossil potential and significant expertise, there is a question of whether Asian nations are attaining efficient consumption and exploitation of renewable resources. From this perspective, the paper aims to evaluate the efficiency of 14 potential Asia countries in renewable energy consumption during the six-year periods (2014–2019). In analyzing the performance of the renewable energy sector, the data envelopment analysis (DEA) with an undesirable output model approach has been widely utilized to measure the efficiency of peer units compared with the best practice frontier. We consider four inputs and two outputs to a DEA-based efficiency model. Labor force, total energy consumption, share of renewable energy, and total renewable energy capacity are inputs. The outputs consist of CO2 emissions as an undesirable output and gross domestic product as a desirable output. The results show that United Arab Emirates, Saudi Arabia, Japan, and South Korea consistently outperform in the evaluation, achieving perfect efficiency scores during the research period. Uzbekistan is found to have the lowest average efficiency of renewable energy utilization. © 2022 CRL Publishing. All rights reserved.", "Keywords": "Asia; Bad output; Data envelopment analysis; Efficiency; Frontier; Renewable energy", "DOI": "10.32604/csse.2022.022941", "PubYear": 2022, "Volume": "43", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, National Kaohsiung University of Science and Technology, Kaohsiung, 80778, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, National Kaohsiung University of Science and Technology, Kaohsiung, 80778, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, National Kaohsiung University of Science and Technology, Kaohsiung, 80778, Taiwan, Department of Logistics and Supply Chain Management, Hong Bang International University, Ho Chi Minh, 723000, Viet Nam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Photonics Engineering, National Kaohsiung University of Science and Technology, Kaohsiung, 80778, Taiwan"}], "References": []}, {"ArticleId": 94519482, "Title": "Energy-Efficient Cluster in Wireless Sensor Network Using Life Time Delay Clustering Algorithms", "Abstract": "Through Wireless Sensor Networks (WSN) formation, industrial and academic communities have seen remarkable development in recent decades. One of the most common techniques to derive the best out of wireless sensor networks is to upgrade the operating group. The most important problem is the arrangement of optimal number of sensor nodes as clusters to discuss clustering method. In this method, new client nodes and dynamic methods are used to determine the optimal number of clusters and cluster heads which are to be better organized and proposed to classify each round. Parameters of effective energy use and the ability to decide the best method of attachments are included. The Problem coverage find change ability network route due to which traffic and delays keep the performance to be very high. A newer version of Gravity Analysis Algorithm (GAA) is used to solve this problem. This proposed new approach GAA is introduced to improve network lifetime, increase system energy efficiency and end delay performance. Simulation results show that modified GAA performance is better than other networks and it has more advanced Life Time Delay Clustering Algorithms-LTDCA protocols. The proposed method provides a set of data collection and increased throughput in wireless sensor networks. © 2022 CRL Publishing. All rights reserved.", "Keywords": "End to end delay; Energy efficient; Gravity analysis algorithm–GAA; Life time delay clustering algorithms-LTDCA; WSNs", "DOI": "10.32604/csse.2022.023481", "PubYear": 2022, "Volume": "43", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Knowledge Institute of Technology, Salem, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Christ University, Bangalore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Kongu Engineering College, Perundurai, India"}], "References": [{"Title": "Energy Aware Clustering with Multihop Routing Algorithm for Wireless Sensor Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "1", "Page": "233", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": ********, "Title": "QKD in Cloud-Fog Computing for Personal Health Record", "Abstract": "Cloud computing is a rapid growing technology which delivers computing services such as servers, storage, database, networking, software and analytics. It has brought a new way to securely store and share information and data with multiple users. When authorized person access these clouds, the released data should not compromise any individual’s privacy and identity should not be revealed. Fog Computing is the extension of cloud with decentralized structure which stores the data in locations somewhere between the data source and cloud. The goal of fog computing is to provide high security, improve performance and network efficiency. We use quantum key distribution to produce and distribute key which change its quantum state and key, when key is known by mediator and it has ability to detect presence of mediator trying to gain lore of the key. In this paper, we introduced sugar-salt encryption which overcomes brute-force attack in effect delivers phony data in return to every incorrect guess of the password or key. © 2022 CRL Publishing. All rights reserved.", "Keywords": "Cloud computing; Fog computing; Quantum key distribution; Security; Sugar-salt encryption", "DOI": "10.32604/csse.2022.022024", "PubYear": 2022, "Volume": "43", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Science and Technology, Anna University, Chennai, 600025, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Science and Technology, Anna University, Chennai, 600025, India"}], "References": []}, {"ArticleId": 94519484, "Title": "QoS Constrained Network Coding Technique to Data Transmission Using IoT", "Abstract": "The research work presents, constrained network coding technique to ensure the successful data transmission based composite channel cmos technology using dielectric properties. The charge fragmentation and charge splitting are two components of the filtered switch domino (FSD) technique. Further behavior of selected switching is achieved using generator called conditional pulse generator which is employed in Multi Dynamic Node Domino (MDND) technique. Both FSD and MDND technique need wide area compared to existing single nodekeeper domino technique. The aim of this research is to minimize dissipation of power and to achieve less consumption of power. The proposed research, works by introducing the method namely Interference and throughput aware Optimized Multicast Routing Protocol (IT-OMRP). The main goal of this proposed research method is to introduce the system which can forward the data packets towards the destination securely and successfully. To achieve the bandwidth and throughput in optimized data transmission, proposed multicast tree is selected by Particle Swarm Optimization which will select the most optimal host node as the branches of multi cast tree. Here node selection is done by considering the objectives residual energy, residual bandwidth and throughput. After node selection multi cast routing is done with the concern of interference to ensure the reliable and successful data transmission. In case of transmission range size is higher than the coverage sense range, successful routing is ensured by selecting secondary host forwarders as a backup which will act as intermediate relay forwarders. The NS2 simulator is used to evaluate research outcome from which it is proved that the proposed technique tends to have increased packet delivery ratio than the existing work. © 2022 CRL Publishing. All rights reserved.", "Keywords": "bandwidth; Multicast routing; optimal node selection; probability of interference; residual energy; secondary relay nodes; throughput", "DOI": "10.32604/csse.2022.021694", "PubYear": 2022, "Volume": "43", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, Kavery Engineering College, Tamilnadu, Mecheri, 636453, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of EEE, Karpagam College of Engineering, Coimbatore, 641032, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of EEE, <PERSON><PERSON> of Engineering, Karur, 639113, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of EEE, <PERSON><PERSON> of Engineering, Karur, 639113, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, CMR Technical Campus, Hyderabad, 501401, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of CST, Manav <PERSON>na University, Faridabad, 121004, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of EEE, Kongu Engineering College, Perundurai, 638060, India"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of EEE, Nandha Engineering College, Tamilnadu, Perundurai, 638052, India"}], "References": [{"Title": "HFuzz: Towards automatic fuzzing testing of NB-IoT core network protocols implementations", "Authors": "Xi<PERSON><PERSON>; Baojiang <PERSON>; Junsong Fu", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "390", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Deep reinforcement learning based mobile edge computing for intelligent Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101184", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 94519485, "Title": "Efficient Energy-Aware Resource Management Model (EEARMM) Based Dynamic VM Migration", "Abstract": "In cloud environment, an efficient resource management establishes the allocation of computational resources of cloud service providers to the requests of users for meeting the user's demands. The proficient resource management and work allocation determines the accomplishment of the cloud infrastructure. However, it is very difficult to persuade the objectives of the Cloud Service Providers (CSPs) and end users in an impulsive cloud domain with random changes of workloads, huge resource availability and complicated service policies to handle them, With that note, this paper attempts to present an Efficient Energy-Aware Resource Management Model (EEARMM) that works in a decentralized manner. Moreover, the model involves in reducing the number of migrations by definite workload management for efficient resource utilization. That is, it makes an effort to reduce the amount of physical devices utilized for load balancing with certain resource and energy consumption management of every machine. The Estimation Model Algorithm (EMA) is given for determining the virtual machine migration. Further, VM-Selection Algorithm (SA) is also provided for choosing the appropriate VM to migrate for resource management. By the incorporation of these algorithms, overloading of VM instances can be avoided and energy efficiency can be improved considerably. The performance evaluation and comparative analysis, based on the dynamic workloads in different factors provides evidence to the efficiency, feasibility and scalability of the proposed model in cloud domain with high rate of resources and workload management. © 2022 CRL Publishing. All rights reserved.", "Keywords": "cloud computing; energy efficient; Resource management model; VM migration; workload management", "DOI": "10.32604/csse.2022.022173", "PubYear": 2022, "Volume": "43", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Sri Krishna College of Technology, Coimbatore, 641042, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical & Electronics Engineering, Kumaraguru College of Technology, Coimbatore, 641049, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, SNS College of Technology, Tamilnadu, Coimbatore, 641035, India"}], "References": []}, {"ArticleId": 94519486, "Title": "Soil Nutrient Detection and Recommendation Using IoT and Fuzzy Logic", "Abstract": "Precision agriculture is a modern farming practice that involves the usage of Internet of Things (IoT) to provide an intelligent farm management system. One of the important aspects in agriculture is the analysis of soil nutrients and balancing these inputs are essential for proper crop growth. The crop productivity and the soil fertility can be improved with effective nutrient management and precise application of fertilizers. This can be done by identifying the deficient nutrients with the help of an IoT system. As traditional approach is time consuming, an IoT-enabled system is developed using the colorimetry principle which analyzes the amount of nutrients present in the soil and a fuzzy expert system is designed to recommend the quantity of fertilizers to be added in the soil. A set of 27 IF-THEN rules are framed using the Mamdani inference system by relating the input and output membership functions based on the linguistic variable for fertilizer recommendation. The experiments are conducted using MATLAB for different ranges of Nitrogen (N), Phosphorous (P) and Potassium (K). The NPK data retrieved by the system is sent to the ThingSpeak cloud and displayed on a mobile application that assists the farmers to know the nutrient information of their field. This system delivers the required nutrient information to farmers which results in efficient green farming. © 2022 CRL Publishing. All rights reserved.", "Keywords": "fertilizer recommendation; fuzzy logic; internet of things; nutrient detection; Precision agriculture", "DOI": "10.32604/csse.2022.023792", "PubYear": 2022, "Volume": "43", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sri Ramak<PERSON>hna Engineering College, Coimbatore, 641022, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ICAR-Sugarcane Breeding Institute, Coimbatore, 641007, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sri Ramak<PERSON>hna Engineering College, Coimbatore, 641022, India"}], "References": []}, {"ArticleId": 94519487, "Title": "Optimized Gated Recurrent Unit for Mid-Term Electricity Price Forecasting", "Abstract": "Electricity price forecasting (EPF) is important for energy system operations and management which include strategic bidding, generation scheduling, optimum storage reserves scheduling and systems analysis. Moreover, accurate EPF is crucial for the purpose of bidding strategies and minimizing the risk for market participants in the competitive electricity market. Nevertheless, accurate time-series prediction of electricity price is very challenging due to complex nonlinearity in the trend of electricity price. This work proposes a mid-term forecasting model based on the demand and price data, renewable and non-renewable energy supplies, the seasonality and peak and off-peak hours of working and nonworking days. An optimized Gated Recurrent Unit (GRU) which incorporates Bagged Regression Tree (BTE) is developed in the Recurrent Neural Network (RNN) architecture for the mid-term EPF. Tanh layer is employed to optimize the hyperparameters of the heterogeneous GRU with the aim to improve the model's performance, error reduction and predict the spikes. In this work, the proposed framework is assessed using electricity market data of five major economical states in Australia by using electricity market data from August 2020 to May 2021. The results showed significant improvement when adopting the proposed prediction framework compared to previous works in forecasting the electricity price. © 2022 CRL Publishing. All rights reserved.", "Keywords": "Deep learning; energy management; machine learning; prediction", "DOI": "10.32604/csse.2022.023617", "PubYear": 2022, "Volume": "43", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Faculty of Engineering, Universiti Malaya50603, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Faculty of Engineering, Universiti Malaya50603, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Faculty of Engineering, Universiti Malaya50603, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Stock Price Forecasting: An Echo State Network Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "3", "Page": "509", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Short-term Wind Speed Prediction with a Two-layer Attention-based LSTM", "Authors": "<PERSON><PERSON> Qian; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "2", "Page": "197", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Stock-Price Forecasting Based on XGBoost and LSTM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "1", "Page": "237", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 94519488, "Title": "Data Warehouse Design for Big Data in Academia", "Abstract": "This paper describes the process of design and construction of a datawarehouse (\"DW\") for an online learning platformusing three prominent technologies, Microsoft SQL Server, MongoDB and Apache Hive. The three systems are evaluated for corpus construction and descriptive analytics. The case also demonstrates the value of evidence-centered design principles for data warehouse design that is sustainable enough to adapt to the demands of handling big data in a variety of contexts. Additionally, the paper addresses maintainability-performance tradeoff, storage considerations and accessibility of big data corpora. In this NSF-sponsored work, the data were processed, transformed, and stored in the three versions of a data warehouse in search for a better performing and more suitable platform. The data warehouse engines-a relational database, a No-SQL database, and a big data technology for parallel computations-were subjected to principled analysis. Design, construction and evaluation of a data warehouse were scrutinized to find improved ways of storing, organizing and extracting information. The work also examines building corpora, performing ad-hoc extractions, and ensuring confidentiality. It was found that Apache Hive demonstrated the best processing time followed by SQL Server and MongoDB. In the aspect of analytical queries, the SQL Server was a top performer followed by MongoDB and Hive. This paper also discusses a novel process for render students anonymity complying with Family Educational Rights and Privacy Act regulations. Five phases for DW design are recommended: 1) Establishing goals at the outset based on Evidence-Centered Design principles; 2) Recognizing the unique demands of student data and use; 3) Adopting a model that integrates cost with technical considerations; 4) Designing a comparative database and 5) Planning for a DW design that is sustainable. Recommendations for future research include attempting DW design in contexts involving larger data sets, more refined operations, and ensuring attention is paid to sustainability of operations. © 2022 Tech Science Press. All rights reserved.", "Keywords": "Apache hive; Big data; Data warehouse; MongoDB; SQL server", "DOI": "10.32604/cmc.2022.016676", "PubYear": 2022, "Volume": "71", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94519489, "Title": "Deep Learning-Based Approach for Arabic Visual Speech Recognition", "Abstract": "Lip-reading technologies are rapidly progressing following the breakthrough of deep learning. It plays a vital role in its many applications, such as: Human-machine communication practices or security applications. In this paper, we propose to develop an effective lip-reading recognitionmodel for Arabic visual speech recognition by implementing deep learning algorithms. The Arabic visual datasets that have been collected contains 2400 records of Arabic digits and 960 records of Arabic phrases from 24 native speakers. The primary purpose is to provide a high-performance model in terms of enhancing the preprocessing phase. Firstly, we extract keyframes from our dataset. Secondly, we produce a Concatenated Frame Images (CFIs) that represent the utterance sequence in one single image. Finally, the VGG-19 is employed for visual features extraction in our proposed model.We have examined different keyframes: 10, 15, and 20 for comparing two types of approaches in the proposed model: (1) the VGG-19 base model and (2) VGG-19 base model with batch normalization. The results show that the second approach achieves greater accuracy: 94% for digit recognition, 97% for phrase recognition, and 93% for digits and phrases recognition in the test dataset. Therefore, our proposed model is superior to models based on CFIs input. © 2022 Tech Science Press. All rights reserved.", "Keywords": "Convolutional neural network; Deep learning; Lip reading; Transfer learning; Visual speech recognition", "DOI": "10.32604/cmc.2022.019450", "PubYear": 2022, "Volume": "71", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94519490, "Title": "Malicious Traffic Detection in IoT and Local Networks Using Stacked Ensemble Classifier", "Abstract": "Malicious traffic detection over the internet is one of the challenging areas for researchers to protect network infrastructures from any malicious activity. Several shortcomings of a network system can be leveraged by an attacker to get unauthorized access through malicious traffic. Safeguard from such attacks requires an efficient automatic system that can detect malicious traffic timely and avoid system damage. Currently, many automated systems can detect malicious activity, however, the efficacy and accuracy need further improvement to detect malicious traffic from multi-domain systems. The present study focuses on the detection of malicious traffic with high accuracy using machine learning techniques. The proposed approach used two datasets UNSW-NB15 and IoTID20 which contain the data for IoT-based traffic and local network traffic, respectively. Both datasets were combined to increase the capability of the proposed approach in detecting malicious traffic from local and IoT networks, with high accuracy. Horizontally merging both datasets requires an equal number of features which was achieved by reducing feature count to 30 for each dataset by leveraging principal component analysis (PCA). The proposed model incorporates stacked ensemble model extra boosting forest (EBF) which is a combination of tree-based models such as extra tree classifier, gradient boosting classifier, and random forest using a stacked ensemble approach. Empirical results show that EBF performed significantly better and achieved the highest accuracy score of 0.985 and 0.984 on the multi-domain dataset for two and four classes, respectively. © 2022 Tech Science Press. All rights reserved.", "Keywords": "Classification; Machine learning; Malicious traffic detection; PCA; Stacked ensemble", "DOI": "10.32604/cmc.2022.019636", "PubYear": 2022, "Volume": "71", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>lut<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Mathematics, Charles Sturt University, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Broward College, Broward County, Florida, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> Li", "Affiliation": "School of Computing and Information Sciences, Florida International University, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Khwaja <PERSON>eed University of Engineering and Information Technology, <PERSON><PERSON>, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan-si, 38541, Korea"}], "References": [{"Title": "An ensemble machine learning approach through effective feature extraction to classify fake news", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "47", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Featured Hybrid Recommendation System Using Stochastic Gradient Descent", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "25", "JournalTitle": "International Journal of Networked and Distributed Computing"}, {"Title": "A decision tree classifier for credit assessment problems in big data environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "19", "Issue": "1", "Page": "363", "JournalTitle": "Information Systems and e-Business Management"}]}, {"ArticleId": 94519491, "Title": "Energy Aware Data Collection with Route Planning for 6G Enabled UAV Communication", "Abstract": "With technological advancements in 6G and Internet of Things (IoT), the incorporation of Unmanned Aerial Vehicles (UAVs) and cellular networks has become a hot research topic. At present, the proficient evolution of 6G networks allows the UAVs to offer cost-effective and timely solutions for real-time applications such as medicine, tracking, surveillance, etc. Energy efficiency, data collection, and route planning are crucial processes to improve the network communication. These processes are highly difficult owing to high mobility, presence of non-stationary links, dynamic topology, and energy-restricted UAVs. With this motivation, the current research paper presents a novel Energy Aware Data Collection with Routing Planning for 6G-enabled UAV communication (EADCRP-6G) technique. The goal of the proposed EADCRP-6G technique is to conduct energy-efficient cluster-based data collection and optimal route planning for 6G-enabled UAV networks. EADCRP-6G technique deploys Improved Red Deer Algorithm-based Clustering (IRDAC) technique to elect an optimal set of Cluster Heads (CH) and organize these clusters. Besides, Artificial Fish Swarm-based Route Planning (AFSRP) technique is applied to choose an optimum set of routes for UAV communication in 6G networks. In order to validated whether the proposed EADCRP-6G technique enhances the performance, a series of simulations was performed and the outcomeswere investigated under different dimensions. The experimental results showcase that the proposed model outperformed all other existing models under different evaluation parameters. © 2022 Tech Science Press. All rights reserved.", "Keywords": "6G networks; Artificial intelligence; Clustering; Data collection; Energy efficiency; Metaheuristics; Route planning; Unmanned aerial vehicle", "DOI": "10.32604/cmc.2022.021490", "PubYear": 2022, "Volume": "71", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Privacy Preserving Blockchain Technique to Achieve Secure and Reliable Sharing of IoT Data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "1", "Page": "87", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Secure communication between UAVs using a method based on smart agents in unmanned aerial vehicles", "Authors": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "5", "Page": "5076", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Battery-constrained federated edge learning in UAV-enabled IoT for B5G/6G networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101381", "JournalTitle": "Physical Communication"}, {"Title": "Energy Efficient Neuro-Fuzzy Cluster based Topology Construction with Metaheuristic Route Planning Algorithm for Unmanned Aerial Vehicles", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "196", "Issue": "", "Page": "108214", "JournalTitle": "Computer Networks"}]}, {"ArticleId": ********, "Title": "Decoding of Factorial Experimental Design Models Implemented in Production Process", "Abstract": "The paper dealswith factorial experimental design models decoding. For the ease of calculation of the experimental mathematical models, it is convenient first to code the independent variables. When selecting independent variables, it is necessary to take into account the range covered by each. A wide range of choices of different variables is presented in this paper. After calculating the regression model, its variables must be returned to their original values for the model to be easy recognized and represented. In the paper, the procedures of simple first order models, with interactions and with second order models, are presented, which could be a very complicated process. Models without and with themutual influence of independent variables differ. The encoding and decoding procedure on a model with two independent first-order parameters is presented in details. Also, the procedure of model decoding is presented in the experimental surface roughness parameters models' determination, in the face milling machining process, using the first and second order model central compositional experimental design. The simple calculation procedure is recommended in the case study. Also, a large number of examples usingmathematicalmodels obtained on the basis of the presented methodology are presented throughout the paper. © 2022 Tech Science Press. All rights reserved.", "Keywords": "Coding; Decoding; Design of experiments; Functions; Mathematical modeling; Regression coefficients; Variable", "DOI": "10.32604/cmc.2022.021642", "PubYear": 2022, "Volume": "71", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Novi Sad, Faculty of Technical Sciences, Novi Sad 21000, Serbia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Novi Sad, Faculty of Technical Sciences, Novi Sad 21000, Serbia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Comenius University in Bratislava, Faculty of Management, Bratislava, 820054 Slovakia↑University Business Academy, Faculty of Economics and Engineering Management, Novi Sad 21000, Serbia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Comenius University in Bratislava, Faculty of Management, Bratislava, 820054 Slovakia"}], "References": [{"Title": "W-shaped surfaces to the nematic liquid crystals with three nonlinearity laws", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "6", "Page": "4513", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 94519493, "Title": "MELex: The Construction of Malay-English Sentiment Lexicon", "Abstract": "Currently, the sentiment analysis research in the Malaysian context lacks in terms of the availability of the sentiment lexicon. Thus, this issue is addressed in this paper in order to enhance the accuracy of sentiment analysis. In this study, a new lexicon for sentiment analysis is constructed. A detailed review of existing approaches has been conducted, and a new bilingual sentiment lexicon known as MELex (Malay-English Lexicon) has been generated. Constructing MELex involves three activities: Seed words selection, polarity assignment, and synonym expansions.Our approach differs from previous works in thatMELex can analyze text for the two most widely used languages in Malaysia, Malay, and English, with the accuracy achieved, is 90%. It is evaluated based on the experimentation and case study approaches where the affordable housing projects inMalaysia are selected as case projects. This finding has given an implication on the ability of MELex to analyze public sentiments in the Malaysian context. The novel aspects of this paper are two-fold. Firstly, it introduces the new technique in assigning the polarity score, and second, it improves the performance over the classification of mixed language content. © 2022 Tech Science Press. All rights reserved.", "Keywords": "Artificial intelligence; Bilingual lexicon; Data sciences; Lexicon-based; Machine learning; Opinion mining; Sentiment analysis; Sentiment lexicon", "DOI": "10.32604/cmc.2022.021131", "PubYear": 2022, "Volume": "71", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer and Mathematical Sciences, Universiti Teknologi MARA (UiTM) Kedah, 08400, Merbok, Kedah, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Decision Sciences, School of Quantitative Sciences, Universiti Utara Malaysia, 06010, Kedah, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Disaster Management of Institute, School of Technology Management and Logistic, Universiti Utara Malaysia, 06010, Kedah, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Management Information System, College of Business Administration, Prince <PERSON> Abdulaziz University, 165, Al-Kharj, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Entrepreneurship and Business, Universiti Malaysia Kelantan, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Lahore, Pakistan"}], "References": [{"Title": "Automatic Indonesian Sentiment Lexicon Curation with Sentiment Valence Tuning for Social Media Sentiment Analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}]}, {"ArticleId": 94519494, "Title": "Diabetes Prediction Algorithm Using Recursive Ridge Regression L2", "Abstract": "At present, the prevalence of diabetes is increasing because the human body cannot metabolize the glucose level. Accurate prediction of diabetes patients is an important research area.Many researchers have proposed techniques to predict this disease through data mining and machine learning methods. In prediction, feature selection is a key concept in preprocessing. Thus, the features that are relevant to the disease are used for prediction. This condition improves the prediction accuracy. Selecting the right features in the whole feature set is a complicated process, and many researchers are concentrating on it to produce a predictive model with high accuracy. In this work, a wrapper-based feature selection method called recursive feature elimination is combined with ridge regression (L2) to form a hybrid L2 regulated feature selection algorithm for overcoming the overfitting problem of data set. Overfitting is a major problem in feature selection, where the new data are unfit to the model because the training data are small. Ridge regression is mainly used to overcome the overfitting problem. The features are selected by using the proposed feature selection method, and random forest classifier is used to classify the data on the basis of the selected features. This work uses the Pima Indians Diabetes data set, and the evaluated results are compared with the existing algorithms to prove the accuracy of the proposed algorithm. The accuracy of the proposed algorithm in predicting diabetes is 100%, and its area under the curve is 97%. The proposed algorithm outperforms existing algorithms. © 2022 Tech Science Press. All rights reserved.", "Keywords": "Feature selection; Machine learning; Random forest; Recursive feature elimination; Ridge regression", "DOI": "10.32604/cmc.2022.020687", "PubYear": 2022, "Volume": "71", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94519495, "Title": "An Automated Deep Learning Based Muscular Dystrophy Detection and Classification Model", "Abstract": "Muscular Dystrophy (MD) is a group of inherited muscular diseases that are commonly diagnosed with the help of techniques such as muscle biopsy, clinical presentation, andMuscleMagneticResonance Imaging (MRI). Among these techniques, Muscle MRI recommends the diagnosis of muscular dystrophy through identification of the patterns that exist in muscle fatty replacement. But the patterns overlap among various diseases whereas there is a lack of knowledge prevalent with regards to disease-specific patterns. Therefore, artificial intelligence techniques can be used in the diagnosis of muscular dystrophies, which enables us to analyze, learn, and predict for the future. In this scenario, the current research article presents an automated muscular dystrophy detection and classification model using Synergic Deep Learning (SDL) method with extreme Gradient Boosting (XGBoost), called SDL-XGBoost. SDL-XGBoost model has been proposed to act as an automated deep learning (DL) model that examines the muscle MRI data and diagnose muscular dystrophies. SDL-XGBoost model employs <PERSON><PERSON>'s entropy based Region of Interest (RoI) for detection purposes. Besides, SDLbased feature extraction process is applied to derive a useful set of feature vectors. Finally, XGBoost model is employed as a classification approach to determine proper class labels for muscleMRI data. The researcher conducted extensive set of simulations to showcase the superior performance of SDLXGBoostmodel. The obtained experimental values highlighted the supremacy of SDL-XGBoost model over other methods in terms of high accuracy being 96.18% and 94.25% classification performance upon DMD and BMD respectively. Therefore, SDL-XGBoostmodel can help physicians in the diagnosis of muscular dystrophies by identifying the patterns of muscle fatty replacement in muscleMRI. © 2022 Tech Science Press. All rights reserved.", "Keywords": "<PERSON><PERSON>'s entropy; Muscle magnetic resonance imaging; Muscular dystrophies; RoI detection; Synergic deep learning; XGBoost", "DOI": "10.32604/cmc.2022.020914", "PubYear": 2022, "Volume": "71", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Automated detection and classification of fundus diabetic retinopathy images using synergic deep learning model", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "210", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A novel Black Widow Optimization algorithm for multilevel thresholding image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114159", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94519496, "Title": "Energy-Efficient Resource Optimization for Massive MIMO Networks Considering Network Load", "Abstract": "This paper investigates the resource optimization problem for a multi-cell massive multiple-input multiple-output (MIMO) network in which each base station (BS) is equipped with a large number of antennas and each base station (BS) adapts the number of antennas to the daily load profile (DLP). This paper takes into consideration user location distribution (ULD) variation and evaluates its impact on the energy efficiency of load adaptive massive MIMO system. ULD variation is modeled by dividing the cell into two coverage areas with different user densities: Boundary focused (BF) and center focused (CF) ULD. All cells are assumed identical in terms of BS configurations, cell loading, and ULD variation and each BS is modeled as an M/G/m/m state dependent queue that can serve a maximum number of users at the peak load. Together with energy efficiency (EE)we analyzed deployment and spectrum efficiency in our adaptive massive MIMO system by evaluating the impact of cell size, available bandwidth, output power level of the BS, and maximum output power of the power amplifier (PA) at different cell loading. We also analyzed average energy consumption on an hourly basis per BS for themodel proposed for data traffic in Europe and also the model proposed for business, residential, street, and highway areas. © 2022 Tech Science Press. All rights reserved.", "Keywords": "Energy efficiency; Massive MIMO; Optimization; Traffic load; User location distribution", "DOI": "10.32604/cmc.2022.021441", "PubYear": 2022, "Volume": "71", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Ubaid M<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Weighted Gauss-<PERSON><PERSON><PERSON> Precoder for Downlink Massive MIMO Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "2", "Page": "1729", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Energy-Efficient Low-Complexity Algorithm in 5G Massive MIMO Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "3", "Page": "3189", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 94519601, "Title": "A lightweight block cipher technique for IoT based E-healthcare system security", "Abstract": "<p>All the real and virtual IoT devices are connected to provide intelligent and decision-making services. Each IoT based application is designed for some specific purpose and function. For instance, the IoT based e-healthcare system is intended for providing healthcare services more smartly. All the healthcare data are stored/accessed remotely in an open environment with the help of the Internet and wireless media. Several cryptographic approaches were developed, protecting the system from misuse, modification, and node tempering of data. Such cryptographic approaches are inadequate due to the device’s small size, low processing capacity, insufficient memory, and power resources. A Lightweight Cryptographic Algorithm (LCA) is needed to secure such a system. In this paper, a lightweight cryptographic algorithm for the security of the e-healthcare system is proposed. The proposed lightweight scheme is based on the Addition substitution and XOR (LWARX). Also, a secure authentication scheme based on the LWARX technique is proposed for secure communication in the healthcare system. The security analysis of the authentication scheme shows it will resist all types of network attacks. The performance analysis of the LWARX shows the enhanced results.</p>", "Keywords": "Internet of Things (IoT); Block cipher; Lightweight cryptography; LWARX; E-healthcare system; Encryption and decryption", "DOI": "10.1007/s11042-022-13106-5", "PubYear": 2022, "Volume": "81", "Issue": "30", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, National Institute of Technology Patna, Patna, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science & Engineering, National Institute of Technology Patna, Patna, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering, KIIT Deemed to be University, Bhubaneswar, India"}], "References": [{"Title": "A secure and lightweight authentication scheme for next generation IoT infrastructure", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "85", "JournalTitle": "Computer Communications"}, {"Title": "Design, analysis, and implementation of a new lightweight block cipher for protecting IoT smart devices", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "6077", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An enhanced scheme for mutual authentication for healthcare services", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "2", "Page": "150", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Retraction Note to: Preserving mobile commerce IoT data using light weight SIMON block cipher cryptographic paradigm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "S1", "Page": "137", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 94519666, "Title": "Computational Design of Kinesthetic Garments", "Abstract": "", "Keywords": "", "DOI": "10.1111/cgf.14492", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science ETH Zürich  Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science ETH Zürich  Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science ETH Zürich  Switzerland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science ETH Zürich  Switzerland"}], "References": []}, {"ArticleId": 94519671, "Title": "Real‐time Virtual‐Try‐On from a Single Example Image through Deep Inverse Graphics and Learned Differentiable Renderers", "Abstract": "<p>Augmented reality applications have rapidly spread across online retail platforms and social media, allowing consumers to virtually try-on a large variety of products, such as makeup, hair dying, or shoes. However, parametrizing a renderer to synthesize realistic images of a given product remains a challenging task that requires expert knowledge. While recent work has introduced neural rendering methods for virtual try-on from example images, current approaches are based on large generative models that cannot be used in real-time on mobile devices. This calls for a hybrid method that combines the advantages of computer graphics and neural rendering approaches. In this paper, we propose a novel framework based on deep learning to build a real-time inverse graphics encoder that learns to map a single example image into the parameter space of a given augmented reality rendering engine. Our method leverages self-supervised learning and does not require labeled training data, which makes it extendable to many virtual try-on applications. Furthermore, most augmented reality renderers are not differentiable in practice due to algorithmic choices or implementation constraints to reach real-time on portable devices. To relax the need for a graphics-based differentiable renderer in inverse graphics problems, we introduce a trainable imitator module. Our imitator is a generative network that learns to accurately reproduce the behavior of a given non-differentiable renderer. We propose a novel rendering sensitivity loss to train the imitator, which ensures that the network learns an accurate and continuous representation for each rendering parameter. Automatically learning a differentiable renderer, as proposed here, could be beneficial for various inverse graphics tasks. Our framework enables novel applications where consumers can virtually try-on a novel unknown product from an inspirational reference image on social media. It can also be used by computer graphics artists to automatically create realistic rendering from a reference product image.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Computer vision;Machine learning;Computer graphics", "DOI": "10.1111/cgf.14456", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "L'Oréal Research and Innovation, France; LTCI, Télécom Paris, Institut Polytechnique de Paris, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Modiface, Canada"}, {"AuthorId": 3, "Name": "S. Ba", "Affiliation": "L'Oréal Research and Innovation, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Modiface, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "L'Oréal Research and Innovation, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "LTCI, Télécom Paris, Institut Polytechnique de Paris, France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "LTCI, Télécom Paris, Institut Polytechnique de Paris, France; Sorbonne Université, CNRS, LIP6, France"}], "References": [{"Title": "State of the Art on Neural Rendering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "2", "Page": "701", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Learning temporal coherence via self-supervision for GAN-based video generation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "MichiGAN", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "Modular primitives for high-performance differentiable rendering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "RGB2Hands", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Total relighting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 94519673, "Title": "Closed space‐filling curves with controlled orientation for 3D printing", "Abstract": "<p>We explore the optimization of closed space-filling curves under orientation objectives. By solidifying material along the closed curve, solid layers of 3D prints can be manufactured in a single continuous extrusion motion. The control over orientation enables the deposition to align with specific directions in different areas, or to produce a locally uniform distribution of orientations, patterning the solidified volume in a precisely controlled manner.</p> <p>Our optimization framework proceeds in two steps. First, we cast a combinatorial problem, optimizing Hamiltonian cycles within a specially constructed graph. We rely on a stochastic optimization process based on local operators that modify a cycle while preserving its Hamiltonian property. Second, we use the result to initialize a geometric optimizer that improves the smoothness and uniform coverage of the cycle while further optimizing for alignment and orientation objectives.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Shape modeling;• Applied computing → Computer-aided design", "DOI": "10.1111/cgf.14488", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Université de Lorraine, CNRS, Inria, LORIA; Joint first authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Université de Lorraine, CNRS, Inria, LORIA; Joint first authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Université de Lorraine, CNRS, Inria, LORIA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Ryerson University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Victoria"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Université de Lorraine, CNRS, Inria, LORIA"}], "References": [{"Title": "Variable-width contouring for additive manufacturing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Reinforced FDM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 94519691, "Title": "Vectorizing Line Drawings of Arbitrary Thickness via Boundary‐based Topology Reconstruction", "Abstract": "<p>Vectorization is a commonly used technique for converting raster images to vector format and has long been a research focus in computer graphics and vision. While a number of attempts have been made to extract the topology of line drawings and further convert them to vector representations, the existing methods commonly focused on resolving junctions composed of thin lines. They usually fail for line drawings composed of thick lines, especially at junctions. In this paper, we propose an automatic line drawing vectorization method that can reconstruct the topology of line drawings of arbitrary thickness. Our key observation is that no matter the lines are thin or thick, the boundaries of the lines always provide reliable hints for reconstructing the topology. For example, the boundaries of two continuous line segments at a junction are usually smoothly connected. By analyzing the continuity of boundaries, we can better analyze the topology at junctions. In particular, we first extract the skeleton of the input line drawing via thinning. Then we analyze the reliability of the skeleton points based on boundaries. Reliable skeleton points are preserved while unreliable skeleton points are reconstructed based on boundaries again. Finally, the skeleton after reconstruction is vectorized as the output. We apply our method on line drawings of various contents and styles. Satisfying results are obtained. Our method significantly outperforms existing methods for line drawings composed of thick lines.</p>", "Keywords": "CCS Concepts;• Applied computing → Fine arts", "DOI": "10.1111/cgf.14485", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Caritas Institute of Higher Education, Hong Kong SAR, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Caritas Institute of Higher Education, Hong Kong SAR, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen University, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen University, China"}], "References": [{"Title": "Integer‐Grid Sketch Simplification and Vectorization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "5", "Page": "149", "JournalTitle": "Computer Graphics Forum"}, {"Title": "A benchmark for rough sketch cleanup", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "General virtual sketching framework for vector line art", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 94519693, "Title": "Compact Facial Landmark Layouts for Performance Capture", "Abstract": "<p>An abundance of older, as well as recent work exists at the intersection of computer vision and computer graphics on accurate estimation of dynamic facial landmarks with applications in facial animation, emotion recognition, and beyond. However, only a few publications exist that optimize the actual layout of facial landmarks to ensure an optimal trade-off between compact layouts and detailed capturing. At the same time, we observe that applications like social games prefer simplicity and performance over detail to reduce the computational budget especially on mobile devices. Other common attributes of such applications are predefined low-dimensional models to animate and a large, diverse user-base. In contrast to existing methods that focus on creating person-specific facial landmarks, we suggest to derive application-specific facial landmarks. We formulate our optimization method on the widely adopted blendshape model. First, a score is defined suitable to compute a characteristic landmark for each blendshape. In a following step, we optimize a global function, which mimics merging of similar landmarks to one. The optimization is solved in less than a second using integer linear programming and guarantees a globally optimal solution to an NP-hard problem. Our application-specific approach is faster and fundamentally different to previous, actor-specific methods. Resulting layouts are more similar to empirical layouts. Compared to empirical landmarks, our layouts require only a fraction of landmarks to achieve the same numerical error when reconstructing the animation from landmarks. The method is compared against previous work and tested on various blendshape models, representing a wide spectrum of applications.</p>", "Keywords": "", "DOI": "10.1111/cgf.14463", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Trinity College Dublin; University of Bonn"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Trinity College Dublin"}], "References": [{"Title": "Optimizing UI layouts for deformable face-rig manipulation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 94519910, "Title": "Texture classification-based feature processing for violence-based anomaly detection in crowded environments", "Abstract": "Anomaly detection from video surveillance inputs helps to improve security in crowded places and outdoors. The captured image is analyzed to identify human faces, objects, and abnormal events through computer-aided analytics. This article proposes a Texture-Classification-based Feature Processing (TCFP) technique for distinguishing anomalies in captured video inputs. The anomalies are identified as events from the sequence frames wherein the dynamic inputs are distinguished using their features. Deep learning is employed for temporal training features based on frame characteristics in this distinguishing process. The input frame is segregated using textural boundaries separated using non-dimensional features. The learning process trains dimensional and non-dimensional features for identifying anomalies and maximizing detection accuracy. The textural boundaries are defined using the non-dimensional vectors present in the frame series in the different face classifications. Therefore, the errors are confined within selective boundaries without impacting the preceding feature. This improves the F1score with less processing time.", "Keywords": "Anomaly detection ; Deep learning ; Feature processing ; Textural analysis ; Video surveillance", "DOI": "10.1016/j.imavis.2022.104488", "PubYear": 2022, "Volume": "124", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mathematics and Computer Science Department, Faculty of Science, Menoufia University, Shebin Elkom 32511, Egypt;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Software Engineering Department, College of Computer and Information Sciences, King Saud University, Riyadh 12372, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bioengineering Department, Speed School of Engineering, University of Louisville, Louisville, KY 40292, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Community College, King Saud University, Riyadh 11437, Saudi Arabia"}], "References": [{"Title": "Crowd anomaly detection using Aggregation of Ensembles of fine-tuned ConvNets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "371", "Issue": "", "Page": "188", "JournalTitle": "Neurocomputing"}, {"Title": "A hybrid analysis of LBSN data to early detect anomalies in crowd dynamics", "Authors": "Rebeca P<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "83", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Abnormal event detection in surveillance videos based on low-rank and compact coefficient dictionary learning", "Authors": "<PERSON>; Zhenjiang Miao; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107355", "JournalTitle": "Pattern Recognition"}, {"Title": "Aggregated context network for crowd counting", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "11", "Page": "1626", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}, {"Title": "Effective crowd counting using multi-resolution context and image quality assessment-guided training", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "201", "Issue": "", "Page": "103065", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Deep learning and handcrafted features for one-class anomaly detection in UAV video", "Authors": "<PERSON><PERSON>; <PERSON>; Hiche<PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "2", "Page": "2599", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Crowd density classification method based on pixels and texture features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "2", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Two-stream deep spatial-temporal auto-encoder for surveillance video abnormal event detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "439", "Issue": "", "Page": "256", "JournalTitle": "Neurocomputing"}, {"Title": "Abnormal crowd density estimation in aerial images based on the deep and handcrafted features fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "114656", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Video anomaly detection using deep residual-spatiotemporal translation network", "Authors": "Thitta<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "155", "Issue": "", "Page": "143", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Deep-learning-based anomaly detection for lace defect inspection employing videos in production line", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "", "Page": "101471", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 94519918, "Title": "Optimal deployment of virtual network functions for securing telecommunication networks against distributed denial of service attacks: A robust optimization approach", "Abstract": "Distributed Denial of Service (DDoS) cyberattacks represent a major security risk for network operators and internet service providers. They thus need to invest in security solutions to protect their network against DDoS attacks. The present work focuses on deploying a network function virtualization based architecture to secure a network against an on-going DDoS attack. We assume that the target, sources and volume of the attack have been identified. However, due to 5G network slicing, the exact routing of the illegitimate flow in the network is not known by the internet service provider. We seek to determine the optimal number and locations of virtual network functions in order to remove all the illegitimate traffic while minimizing the total cost of the activated virtual network functions. We propose a robust optimization framework to solve this problem. The uncertain input parameters correspond to the amount of illegitimate flow on each path connecting an attack source to the target and can take values within a predefined uncertainty set. In order to solve this robust optimization problem, we develop an adversarial approach in which the adversarial sub-problem is solved by a Branch &amp; Price algorithm. The results of our computational experiments, carried out on medium-size randomly generated instances, show that the proposed solution approach is able to provide optimal solutions within short computation times.", "Keywords": "Telecommunication networks ; Cybersecurity ; Distributed denial of service ; Network function virtualization ; Robust optimization ; Adversarial approach ; Mixed-integer linear programming ; Branch & price ; Column generation", "DOI": "10.1016/j.cor.2022.105890", "PubYear": 2022, "Volume": "146", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratoire Interdisciplinaire des Sciences du Numérique, Université Paris Saclay, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratoire d’Informatique de l’Ecole Polytechnique, Université Paris 1, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Orange Labs Products & Services, France"}], "References": []}, {"ArticleId": 94519933, "Title": "A lead-free K2CuBr3 microwires-based humidity sensor realized via QCM for real-time breath monitoring", "Abstract": "Halide perovskites have recently emerged as potential humidity-detection materials due to their excellent chemical and physical properties. However, the toxicity of Pb and environmental instability of the lead halide perovskites restrict the application in humidity detection. Herein, a lead-free K<sub>2</sub>CuBr<sub>3</sub> microwires-based quartz crystal microbalance (QCM) humidity sensor was investigated, and the application in human respiratory monitoring was developed. Coupling the high humidity sensitivity of K<sub>2</sub>CuBr<sub>3</sub> with the high mass change sensitivity of QCM, the lead-free K<sub>2</sub>CuBr<sub>3</sub> coated QCM humidity sensor exhibited excellent logarithmic linearity response (R<sup>2</sup> = 0.98626, 11–95% RH). In addition, the sensor demonstrated excellent repeatability, fast response/recover time (10.63 s/4.31 s), and considerable stability. Moreover, the Fourier transform infrared spectra (FTIR) and Langmuir adsorption model were applied to reveal the corresponding humidity sensing mechanism. The results indicate the lead-free K<sub>2</sub>CuBr<sub>3</sub>-based QCM sensor reported in this work could be a promising candidate for real-time breath monitoring.", "Keywords": "Metal halide ; Lead-free ; Humidity sensor ; QCM ; Respiratory monitoring", "DOI": "10.1016/j.snb.2022.132112", "PubYear": 2022, "Volume": "367", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electromechanical and Automotive Engineering, Yantai University, Yantai 264005 China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 5, "Name": "Libo Liu", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "College of Optoelectronic Engineering, Chongqing University of Posts and Telecommunications, Chongqing 400065, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China;Corresponding authors"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China;College of Optoelectronic Engineering, Chongqing University of Posts and Telecommunications, Chongqing 400065, China;Corresponding author at: Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}], "References": [{"Title": "Surface acoustic wave humidity sensor based on three-dimensional architecture graphene/PVA/SiO2 and its application for respiration monitoring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "308", "Issue": "", "Page": "127693", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "High-performance FBAR humidity sensor based on the PI film as the multifunctional layer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "308", "Issue": "", "Page": "127694", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Sensitive and renewable quartz crystal microbalance humidity sensor based on nitrocellulose nanocrystals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "327", "Issue": "", "Page": "128944", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Ultrasensitive humidity sensing using one-dimensional π-d conjugated coordination polymers for breath monitoring", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "330", "Issue": "", "Page": "129353", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "An excellent impedance-type humidity sensor based on halide perovskite CsPbBr3 nanoparticles for human respiration monitoring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "337", "Issue": "", "Page": "129772", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 94519997, "Title": "VPFL: A verifiable privacy-preserving federated learning scheme for edge computing systems", "Abstract": "Federated learning for edge computing is a promising solution in the data booming era, which leverages the computation ability of each edge device to train local models and only shares the model gradients to the central server. However, the frequently transmitted local gradients could also leak the participants’ private data. To protect the privacy of local training data, lots of cryptographic-based Privacy-Preserving Federated Learning (PPFL) schemes have been proposed. However, due to the constrained resource nature of mobile devices and complex cryptographic operations, traditional PPFL schemes fail to provide efficient data confidentiality and lightweight integrity verification simultaneously. To tackle this problem, we propose a Verifiable Privacy-preserving Federated Learning scheme (VPFL) for edge computing systems to prevent local gradients from leaking over the transmission stage. Firstly, we combine the Distributed Selective Stochastic Gradient Descent (DSSGD) method with Paillier homomorphic cryptosystem to achieve the distributed encryption functionality, so as to reduce the computation cost of the complex cryptosystem. Secondly, we further present an online/offline signature method to realize the lightweight gradients integrity verification, where the offline part can be securely outsourced to the edge server. Comprehensive security analysis demonstrates the proposed VPFL can achieve data confidentiality, authentication, and integrity. At last, we evaluate both communication overhead and computation cost of the proposed VPFL scheme, the experimental results have shown VPFL has low computation costs and communication overheads while maintaining high training accuracy.", "Keywords": "Federated learning ; Edge computing ; Privacy-preserving ; Verifiable aggregation ; Homomorphic cryptosystem", "DOI": "10.1016/j.dcan.2022.05.010", "PubYear": 2023, "Volume": "9", "Issue": "4", "JournalId": 5621, "JournalTitle": "Digital Communications and Networks", "ISSN": "2352-8648", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou, 225009, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Water Conservancy and Civil Engineering College, Inner Mongolia Agricultural University, Hohhot, 010018, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Deakin Blockchain Innovation Lab, School of Information Technology, Deakin University, Melbourne, VIC, 3125, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Water Conservancy and Civil Engineering College, Inner Mongolia Agricultural University, Hohhot, 010018, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, 211106, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Technology Sydney, Sydney, 2007, Australia"}], "References": [{"Title": "A training-integrity privacy-preserving federated learning scheme with trusted execution environment", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "522", "Issue": "", "Page": "69", "JournalTitle": "Information Sciences"}, {"Title": "PFLM: Privacy-preserving federated learning with membership proof", "Authors": "<PERSON><PERSON><PERSON> Jiang; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "576", "Issue": "", "Page": "288", "JournalTitle": "Information Sciences"}, {"Title": "A Comprehensive Survey of Privacy-preserving Federated Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 94520015, "Title": "Stochastic boundary condition effects on supersonic leading edge blowing", "Abstract": "The present manuscript studies the robustness of leading edge blowing for active control of the upstream bow shock and overall aerothermal characteristics of a supersonic airfoil subject to variability in the injection conditions. The injection boundary conditions were considered stochastic and their randomness was propagated to the flow topology and aerothermal quantities of interest by means of non-intrusive Polynomial Chaos Expansions. The evaluations needed to build these expansions were carried out via steady two-dimensional Reynolds-Averaged Navier Stokes simulations. Two different injection port sizes were considered, with the smallest promoting a Coanda effect that gives rise to non-symmetric flow topologies. The uncertain boundary conditions lead to significant variations in the flow topology, particularly at the injection exit and the bow shock position, with variations of up to 75% of the nominal value. This study identifies the key operating conditions to ensure the optimal application of leading-edge injection for active control of supersonic airfoils.", "Keywords": "Supersonic flow ; Bbow shock ; Fflow control ; Coanda ; Polynomial Chaos ; Bow shock ; Flow control", "DOI": "10.1016/j.compfluid.2022.105513", "PubYear": 2022, "Volume": "243", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Purdue University, West Lafayette IN 47907, USA;Corresponding author at: Mechanical Engineering, Purdue University, Zucrow Laboratories, 500 Allison Rd West Lafayette, Indiana 47907, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Purdue University, West Lafayette IN 47907, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Purdue University, West Lafayette IN 47907, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Purdue University, West Lafayette IN 47907, USA"}], "References": []}, {"ArticleId": 94520068, "Title": "Ant colony optimization equipped with an ensemble of heuristics through multi-criteria decision making: A case study in ensemble feature selection", "Abstract": "Ant Colony Optimization (ACO) is a probabilistic and approximation metaheuristic algorithm to solve complex combinatorial optimization problems. ACO algorithm is inspired by the behavior of a colony of real ants and uses their pheromone trials to find optimal solutions. Since the beginning of the ACO algorithm, many researchers have tried to improve the performance and stability of the algorithm by using various methodologies. Resolving the exploitation/exploration dilemma by an efficient procedure is critical in improving the ACO. One of the critical parameters in ACO is selecting the heuristic that can affect the movements of ants. So far, the use of several heuristics in ACO has not been studied. We believe that using multiple heuristics instead of a single heuristic can improve the ACO algorithm. For this matter, we have proposed an ACO algorithm based on the ensemble of heuristics using a Multi-Criteria Decision-Making (MCDM) procedure. It means that the movement of the ants is defined based on the judgment of multiple experts (criteria). The idea is based on the hypothesis that different heuristics give us more information about the subsequent nodes, and the variety of these methods examines the different aspects to achieve better and optimal solutions in ACO. In this paper, we have applied our proposed method to the ensemble feature selection task to evaluate the performance of the proposed method. Blending several feature selection methods is regular to tackle the feature selection problem, and also efficiently combining feature selection methods is still challenging. Some well-known ensemble feature selection and primary feature selection methods have been compared with Ant-MCDM on twelve datasets to evaluate the performance of the proposed method in the feature selection task.", "Keywords": "Ant Colony Optimization ; Multi-Criteria Decision-Making ; Feature selection ; Ensemble feature selection ; Ensemble of heuristics", "DOI": "10.1016/j.asoc.2022.109046", "PubYear": 2022, "Volume": "124", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Yazd University, Yazd, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Isfahan University of Technology, Isfahan, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Lorestan University, Khorramabad, Iran"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Lorestan University, Khorramabad, Iran;Corresponding author"}], "References": [{"Title": "A review of unsupervised feature selection methods", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON><PERSON>; <PERSON>-Trinidad", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "907", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "MGFS: A multi-label graph-based feature selection algorithm via PageRank centrality", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "113024", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A survey on swarm intelligence approaches to feature selection in data mining", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100663", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Robust human activity recognition using single accelerometer via wavelet energy spectrum features and ensemble feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "1", "Page": "83", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "A new ensemble feature selection approach based on genetic algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "20", "Page": "15811", "JournalTitle": "Soft Computing"}, {"Title": "MFS-MCDM: Multi-label feature selection using multi-criteria decision making", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "206", "Issue": "", "Page": "106365", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A bipartite matching-based feature selection for multi-label learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "459", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "A pareto-based ensemble of feature selection algorithms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "180", "Issue": "", "Page": "115130", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Ant-TD: Ant colony optimization plus temporal difference reinforcement learning for multi-label feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "", "Page": "100892", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "VMFS: A VIKOR-based multi-target feature selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115224", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An efficient Pareto-based feature selection algorithm for multi-label classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "581", "Issue": "", "Page": "428", "JournalTitle": "Information Sciences"}, {"Title": "An ensemble feature selection algorithm based on PageRank centrality and fuzzy logic", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "233", "Issue": "", "Page": "107538", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 94520076, "Title": "PASM: Parallelism Aware Space Management strategy for hybrid SSD towards in-storage DNN training acceleration", "Abstract": "With the explosive growth of data volume and great improvement in flash technologies, SSD-based In-Storage Computing (ISC) is becoming one of the most important means to accelerate data-intensive applications, which also provides a possibility for in-storage acceleration of DNN training. However, the continuous write requests involved in DNN training become a big challenge for the reliability and efficiency of the flash-centric computing system. Recently trend in high-density and low-cost NAND flash further aggravates this challenge. To address this problem, we propose a P arallelism A ware S pace M anagement (PASM) strategy to enable the utilization of SLC-TLC hybrid SSD for DNN training acceleration. And two key technologies are included in PASM. Firstly, we proposed the conflict-free parallelized data layout. It distributes data of different lifetimes into different kinds of physical blocks to avoid data migration as far as possible. Meanwhile, it enables parallel read/program and erase operations by exploiting the inherent parallelism in the flash array. As a result, it eliminates the conflicts between continuous I/O requests and garbage collections so as to provide a stable I/O performance during training. Secondly, a novel garbage collection strategy, called lifetime-aware deterministic garbage collection, is proposed. This scheme performs erase operations without disturbing continuous I/O requests by sensing the data lifetime and tightly coordinating the neural network training process, which is able to reclaim invalid blocks in a timely and efficient way to guarantee that the high endurance of SLC can be utilized continuously. Finally, to verify the performance of PASM, we compared PASM with the related state-of-the-art hybrid SSD and TLC-only SSD under Resnet50 training workload. Experimental results show that the PASM improves I/O performance by 20% and increases lifetime by 6.6 times.", "Keywords": "In-storage computing ; DNN training ; Hybrid SSD ; SLC-TLC", "DOI": "10.1016/j.sysarc.2022.102565", "PubYear": 2022, "Volume": "128", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer science, Chongqing University, No. 174 Shazhengjie, Shapingba, Chongqing, 400044, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer science, Chongqing University, No. 174 <PERSON>, Shapingba, Chongqing, 400044, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer science, Chongqing University, No. 174 Shazhengjie, Shapingba, Chongqing, 400044, China"}], "References": [{"Title": "Cost-effective, Energy-efficient, and Scalable Storage Computing for Large-scale AI Applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Storage"}]}, {"ArticleId": 94520095, "Title": "The reference effect on a retailer’s selling strategy with limited initial inventory level and strategic consumer behavior", "Abstract": "Reference price is generally formed in consumers’ minds and used to judge the current selling price. In this paper, we consider a retailer owns a limited initial inventory level (IIL) selling to strategic consumers over a selling horizon with a regular period and a potential markdown period. A unique feature but also a significant challenge of this paper is that either reference price or IIL affects the consumers’ strategic behaviors and the retailer’s selling strategy, especially when the consumers are aware of stockout risk. To explore the single and joint impacts of both two factors and the value of consumers’ awareness of the risk, we first present a pricing model in which the consumers are unaware of the risk, we subsequently extend to another model where the consumers are aware of the risk, and we finally conduct numerical experiments to further examine the impacts and the value. The results show that if the consumers are unaware of the risk, a stronger reference effect (RE) will generate more revenue for the retailer, especially when IIL is insufficient. If the consumers realize the risk, the above results heavily depend on their attitude to risk. At this time, a higher IIL will induce more consumers to wait until the markdown period. Besides, the consumers are optimistic towards the risk will enhance the strength of RE and help the retailer to improve revenue, while the opposite is true when the consumers are pessimistic towards the risk. In addition, a sufficient inventory level does not necessarily bring more benefits than the limited inventory level if the consumers are aware of the risk.", "Keywords": "Reference effect ; Strategic consumers ; Inventory constraints ; Selling strategy ; Stockout risk", "DOI": "10.1016/j.cie.2022.108260", "PubYear": 2022, "Volume": "169", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei 230009, PR China;Key Laboratory of Process Optimization and Intelligent Decision-Making, Ministry of Education, Hefei 230009, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "International Institute of Finance, School of Management, University of Science and Technology of China, Hefei 230026, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "International Institute of Finance, School of Management, University of Science and Technology of China, Hefei 230026, PR China"}], "References": [{"Title": "Joint production and pricing inventory system under stochastic reference price effect", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "106411", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Two-period discount pricing strategies for an e-commerce platform with strategic consumers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "106640", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 94520127, "Title": "Design and Analysis of Novel Antenna for Millimeter-Wave Communication", "Abstract": "At present, the microwave frequency band bandwidth used for mobile communication is only 600 MHz. In 2020, the 5G mobile Communication required about 1 GHz of bandwidth, so people need to tap new spectrum resources to meet the development needs of mobile Internet traffic that will increase by 1,000 times in the next 10 years. Utilize the potentially large bandwidth (30∼300 GHz) of the millimeter wave frequency band to provide higher data rates is regarded as the potential development trend of the future wireless communication technology. A microstrip patch implementation approach based on electromagnetic coupling feeding is presented to increase the bandwidth of a dual-polarized millimeter-wave antenna. To extend the antenna unit's impedance bandwidth, coplanar parasitic patches and spatial parallel parasitic patches are used, and a 22 sub-array antenna is developed using paired inverse feed technology. The standing wave at the centre frequency of 37.5 GHz is less than 2 GHz. The antenna array's relative bandwidth is 6.13 percent, the isolation is >30 dB, the cross-polarization is -23.6 dB, and the gain is 11.5 dBi, according to the norm. The proposed dual-polarized microstrip antenna has the characteristics of wide frequency bandwidth, large port isolation, low cross-polarization, and high gain. The antenna performance meets the general engineering requirements of millimeter-wave dual-polarized antennas. © 2022 CRL Publishing. All rights reserved.", "Keywords": "Electromagnetics; Metamaterial; Millimeter-wave antenna; Wireless communication", "DOI": "10.32604/csse.2022.024202", "PubYear": 2022, "Volume": "43", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "A Novel Broadband Antenna Design for 5G Applications", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "1", "Page": "1121", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 94520130, "Title": "Attention mechanism in neural networks: where it comes and where it goes", "Abstract": "<p>A long time ago in the machine learning literature, the idea of incorporating a mechanism inspired by the human visual system into neural networks was introduced. This idea is named the attention mechanism , and it has gone through a long development period. Today, many works have been devoted to this idea in a variety of tasks. Remarkable performance has recently been demonstrated. The goal of this paper is to provide an overview from the early work on searching for ways to implement attention idea with neural networks until the recent trends. This review emphasizes the important milestones during this progress regarding different tasks. By this way, this study aims to provide a road map for researchers to explore the current development and get inspired for novel approaches beyond the attention.</p>", "Keywords": "Attention mechanism; Neural networks; Deep learning; Survey", "DOI": "10.1007/s00521-022-07366-3", "PubYear": 2022, "Volume": "34", "Issue": "16", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Brain and Cognition, University of Leuven (KU Leuven), Leuven, Belgium"}], "References": [{"Title": "BioBERT: a pre-trained biomedical language representation model for biomedical text mining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1234", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 94520227, "Title": "How to manage a task-oriented virtual assistant software project: an experience report", "Abstract": "<p>Task-oriented virtual assistants are software systems that provide users with a natural language interface to complete domain-specific tasks. With the recent technological advances in natural language processing and machine learning, an increasing number of task-oriented virtual assistants have been developed. However, due to the well-known complexity and difficulties of the natural language understanding problem, it is challenging to manage a task-oriented virtual assistant software project. Meanwhile, the management and experience related to the development of virtual assistants are hardly studied or shared in the research community or industry, to the best of our knowledge. To bridge this knowledge gap, in this paper, we share our experience and the lessons that we have learned at managing a task-oriented virtual assistant software project at Microsoft. We believe that our practices and the lessons learned can provide a useful reference for other researchers and practitioners who aim to develop a virtual assistant system. Finally, we have developed a requirement management tool, named SpecSpace, which can facilitate the management of virtual assistant projects.</p>", "Keywords": "Experience report; Software project management; Virtual assistant; Machine learning; 经验报告; 软件项目管理; 虚拟助手; 机器学习; TP311.5", "DOI": "10.1631/FITEE.2100467", "PubYear": 2022, "Volume": "23", "Issue": "5", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Microsoft Research Asia, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Research Asia, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Microsoft Research Asia, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Microsoft Research Asia, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, Xi’an Jiaotong University, Xi’an, China"}], "References": []}, {"ArticleId": 94520309, "Title": "Mobile internet big data technology-based echo loss measurement method of optical communication system", "Abstract": "The paper proposes a method to analyze the process of echo loss in an optical communication system in combination with mobile internet big data technology . The optical echo loss tester shall include the output connector, the standard reflector and the automatic unite to process and display the device to be measured. Using mode-coupling theory, we can calculate the loss when a light in an optical fiber flows through a fiber optic connector under various effect variables; then analytical formulas are developed, to calculate insertion and echo loss in a real-life situation. In this research, we propose a method for measuring echo loss in optical communication systems using mobile internet big data technology. The effect of this method is evaluated combined with the simulation test, based on the research carried out by research. The optical communication echo loss measurement based on mobile Internet of Things big data technology has some influence.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108097", "PubYear": 2022, "Volume": "101", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Xidian University, Shanxi Xian, 710071 China;Institute of systems engineering, AMS, Beijing, 100141 China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Xidian University, Shanxi Xian, 710071 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Huaneng coal industry co., LTD, Beijing, 100070 China;Corresponding author"}], "References": []}, {"ArticleId": 94520315, "Title": "Energy-Efficient Secure Adaptive Neuro Fuzzy Based Clustering Technique for Mobile Adhoc Networks", "Abstract": "In recent times, Mobile Ad Hoc Network (MANET) becomes a familiar research field owing to its applicability in distinct scenarios. MANET comprises a set of autonomous mobile nodes which independently move and send data through wireless channels. Energy efficiency is considered a critical design issue in MANET and can be addressed by the use of the clustering process. Clustering is treated as a proficient approach, which partitions the mobile nodes into groups called clusters and elects a node as cluster head (CH). On the other hand, the nature of wireless links poses security as a major design issue. Therefore, this paper proposes a non-probabilistic and energy-efficient secure adaptive neuro fuzzy-based clustering scheme (NPEE-SANFC) for MANET. The proposed NPEE-SANFC techniques elects CHs in two levels such as tentative CH election and final CH election. Besides, a non-probabilistic way of Tentative CH (TCH) selection takes place by the use of a back-off timer. In addition, ANFC technique is applied for the election of Final CH (FCH)s. The presented model involves a set of input parameters such as residual energy, intra-cluster distance, inter-cluster distance, and trust degree. The incorporation of the trust degree of the node enables to elect secure CHs. Furthermore, the application of two processes for optimal CH selection will result in enhanced network lifetime and energy efficiency. To validate the results regarding the effectiveness of the presented NPEE-SANFC technique, a set of experiments was performed; and the results were determined using distinct measures such as the energy consumption, network lifetime, throughput, and end-to-end delay.", "Keywords": "Clustering; fuzzy logic; non-probabilistic; routing; wireless sensor networks", "DOI": "10.32604/iasc.2022.026355", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Instrument Technology, A. U. College of Engineering, Andhra University, Andhra Pradesh, Visakhapatnam, 530003, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Instrument Technology, A. U. College of Engineering, Andhra University, Andhra Pradesh, Visakhapatnam, 530003, India"}], "References": []}, {"ArticleId": ********, "Title": "Integrated identity and access management metamodel and pattern system for secure enterprise architecture", "Abstract": "Identity and access management (IAM) is one of the key components of the secure enterprise architecture for protecting the digital assets of the information systems. The challenge is: How to model an integrated IAM for a secure enterprise architecture to protect digital assets? This research aims to address this question and develops an ontology based integrated IAM metamodel for the secure digital enterprise architecture (EA). Business domain and technology agnostic characteristics of the developed IAM metamodel will allow it to develop IAM models for different types of information systems. Well-known design science research (DSR) methodology was adopted to conduct this research. The developed IAM metamodel is evaluated by using the demonstration method. Furthermore, as a part of the evaluation, a pattern system has been developed, consisting of eight IAM patterns. Each pattern offers a solution to a specific IAM related problem. The outcome of this research indicates that enterprise, IAM and information systems architects and academic researchers can use the proposed IAM metamodel and the pattern system to design and implement situation-specific IAM models within the overall context of a secure EA for information systems.", "Keywords": "Identity management ; Access control management ; Metamodel ; Ontology ; Enterprise architecture ; Design science research", "DOI": "10.1016/j.datak.2022.102038", "PubYear": 2022, "Volume": "140", "Issue": "", "JournalId": 5635, "JournalTitle": "Data & Knowledge Engineering", "ISSN": "0169-023X", "EISSN": "1872-6933", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Technology Sydney, Ultimo NSW 2007, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Technology Sydney, Ultimo NSW 2007, Australia"}], "References": [{"Title": "COPri v.2 — A core ontology for privacy requirements", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "133", "Issue": "", "Page": "101888", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "Ontology-driven evolution of software security", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "134", "Issue": "", "Page": "101907", "JournalTitle": "Data & Knowledge Engineering"}]}, {"ArticleId": 94520402, "Title": "A Hybrid K-Means Hierarchical Algorithm for Natural Disaster Mitigation Clustering", "Abstract": "<p>Cluster methods such as k-means have been widely used to group areas with a relatively equal number of disasters to determine areas prone to natural disasters. Nevertheless, it is dificult to obtain a homogeneous clustering result of the k-means method because this method is sensitive to a random selection of the centers of the cluster. This paper presents the result of a study that aimed to apply a proposed hybrid approach of the combined k-means algorithm and hierarchy to the clustering process of anticipation level datasets of natural disaster mitigation in Indonesia. This study also added keyword and disaster-type ields to provide additional information for a better clustering process. The clustering process produced three clusters for the anticipation level of natural disaster mitigation. Based on the validation from experts, 67 districts/cities (82.7%) fell into Cluster 1 (low anticipation), nine districts/cities (11.1%) were classiied into Cluster 2 (medium), and the remaining ive districts/cities (6.2%) were categorized in Cluster 3 (high anticipation). From the analysis of the calculation of the silhouette coeficient, the hybrid algorithm provided relatively homogeneous clustering results. Furthermore, applying the hybrid algorithm to the keyword segment and the type of disaster produced a homogeneous clustering as indicated by the calculated purity coeficient and the total purity values. Therefore, the proposed hybrid algorithm can provide relatively homogeneous clustering results in natural disaster mitigation.</p>", "Keywords": "Clustering; Hybrid; K-means; Mitigation; Natural disaster", "DOI": "10.32890/jict2022.21.2.2", "PubYear": 2022, "Volume": "21", "Issue": "", "JournalId": 66251, "JournalTitle": "Journal of Information and Communication Technology", "ISSN": "1675-414X", "EISSN": "2180-3862", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Research Center for Informatics, National Research and Innovation Agency, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Research Center for Informatics, National Research and Innovation Agency, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Research Center for Geotechnology, National Research and Innovation Agency, Indonesia"}], "References": [{"Title": "Perbandingan Aplikasi Algoritma Kernel K-Means pada Graf <PERSON>it dan K-Means pada <PERSON>- <PERSON><PERSON><PERSON> dalam Dataset Penelitian Covid-19 RISTEKBRIN", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "2", "Page": "411", "JournalTitle": "Jurnal Teknologi Informasi dan <PERSON>"}]}, {"ArticleId": 94520561, "Title": "Non-traditional machining techniques for silicon wafers", "Abstract": "<p>Silicon (Si) micromachining techniques have recently witnessed significant advancement, attributable to the high surge in demand for microelectromechanical and microelectronic devices. Micromachining techniques are widely used to cut or pattern Si, in order to obtain high-quality surface finishes for the fabrication of devices. Micromachining techniques are used for the fabrication of three-dimensional (3D) microstructures for microelectromechanical devices. In this work, the capabilities and competencies of non-traditional Si micromachining techniques, including ultrasonic, ion beam milling, laser machining, and electrical discharge machining, are discussed and compared accordingly. The working principles, advantages, limitations, and Si microstructures that have been fabricated before are discussed in detail. Additionally, this work covers the performance reported by multiple researchers on these micromachining methods, spanning the temporal range of 1990 to 2020. The key outcomes of this study are explored and summarized.</p>", "Keywords": "Micromachining; Silicon; Ultrasonic; Ion beam; Laser; Electrical discharge; Machining performances", "DOI": "10.1007/s00170-022-09365-z", "PubYear": 2022, "Volume": "121", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Faculty of Engineering, Universiti Teknologi Malaysia, Johor Bahru, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Faculty of Engineering, Universiti Teknologi Malaysia, Johor Bahru, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechatronics Engineering, Kulliyyah of Engineering, International Islamic University Malaysia, Kuala Lumpur, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Faculty of Engineering, Universiti Teknologi Malaysia, Johor Bahru, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering, Faculty of Engineering, Universiti Teknologi Malaysia, Johor Bahru, Malaysia"}], "References": []}, {"ArticleId": 94520589, "Title": "Classification of physiological disorders in apples fruit using a hybrid model based on convolutional neural network and machine learning methods", "Abstract": "<p>Physiological disorders in apples are due to post-harvest conditions. For this reason, automatic identification of physiological disorders is important in obtaining agricultural information. Image processing is one of the techniques that can help achieve the features of physiological disorders. Physiological disorders during image acquisition can be affected by the changes in brightness values created by different lighting conditions. This changes the results of the classification. In recent years, the convolutional neural network (CNN) has been a successful approach in automatically obtaining deep features from raw images in image classification problems. The study aims to classify physiological disorders using machine learning (ML) methods according to extracted deep features of the images under different lighting conditions. The data sets were created by acquired images (1080 images) and augmentation images (4320 images). Deep features were extracted using five popular pre-trained CNN models in these data sets, and these features were classified using five ML methods. The highest average accuracy was obtained with the VGG19(fc6) + SVM method in the data set-1 and data set-2 and were 96.11 and 96.09%, respectively. With this study, physiological disorders can be determined early, and needed precautions can be taken before and after harvest, not too late.</p>", "Keywords": "Convolutional neural network; Machine learning; Deep features; Physiological disorders in apple; Lighting", "DOI": "10.1007/s00521-022-07350-x", "PubYear": 2022, "Volume": "34", "Issue": "19", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Technologies, Sarayönü Vocational High School, Selçuk University, Konya, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering and Natural Sciences, Konya Technical University, Konya, Turkey"}], "References": [{"Title": "Multi-model LSTM-based convolutional neural networks for detection of apple diseases and pests", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "7", "Page": "3335", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Multiclass classification of nutrients deficiency of apple using deep neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "11", "Page": "8411", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Image-based failure detection for material extrusion process using a convolutional neural network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "5-6", "Page": "1291", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 94520603, "Title": "An UWB-based indoor coplanar localization and anchor placement optimization method", "Abstract": "<p>Ultrawideband (UWB)-based localization systems are appropriated for human and vehicle tracking because the time-of-flight between UWB devices can be accurately estimated. A moving UWB embedded target can be located by the anchors whose positions are set in the initialization step. Considering that some areas are unreachable or dangerous for humankind or some urgent tasks make it impossible to set the position for anchors manually. Hence, we propose a coplanar localization method for one-side-open areas, which is easy for deploying. In this method, the coordinates of the inside node can be estimated by the coplanar outside anchors that are attached to an outside window and are easy for calibrating. However, optimizing anchor placement is still a critical challenge that affects localization performance and is essentially NP-complete. To improve the localization accuracy, we propose an anchor placement optimization method that is based on utilizing the genetic algorithm and minimizing the <PERSON><PERSON><PERSON><PERSON><PERSON> lower bound. Finally, field experiments are conducted, and a thorough comparison confirms the efficiency and effectiveness of the proposed method.</p>", "Keywords": "Indoor localization systems; Ultrawideband; Genetic algorithm; Placement optimization", "DOI": "10.1007/s00521-022-07329-8", "PubYear": 2022, "Volume": "34", "Issue": "19", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Pan", "Affiliation": "School of Mathematics and Statistics, Xidian University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xidian University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xidian University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Sciences, Xidian University, Xi’an, China"}], "References": [{"Title": "Optimizing AP and Beacon Placement in WiFi and BLE hybrid localization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "164", "Issue": "", "Page": "102673", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Dynamic mobile charger scheduling with partial charging strategy for WSNs using deep-Q-networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "22", "Page": "15267", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 94520613, "Title": "Effect of color properties in multiple time series graph comprehension", "Abstract": "Multiple time series graphs are used prevalently in representing business and research data, but the use of color properties to visualize them to enhance comprehension is limited. This study explored the effect of hue and lightness in representing 4-time series data in relation to response time (RT) and accuracy. Two types of palettes were developed for each experiment: monochrome and multi-hue. The three sets of monochrome palettes created were red, green, and blue, while four equidistant hues in the color wheel were used in the multi-hue palette: red, blue, green, and purple. A total of forty people participated in the two experiments. Participants performed two tasks for both experiments: maximum and discrimination tasks. The monochrome experiment showed the primacy of green in terms of RT and accuracy in the discrimination task. RT and accuracy were significantly affected by lightness in the multi-hue experiment. For both tasks, RT was longer for 20% lightness and lowest at 60% lightness. Accuracy results were also consistent with RT. In the discrimination task, participants made more errors in 20% lightness and the highest accuracy for 60% and 80%.", "Keywords": "Color;Data and knowledge visualization;Graphics recognition and interpretation;Time series analysis", "DOI": "10.1016/j.apergo.2022.103808", "PubYear": 2022, "Volume": "103", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Industrial and Systems Engineering Department, De La Salle University, Philippines. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Industrial and Systems Engineering Department, De La Salle University, Philippines."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Industrial and Systems Engineering Department, De La Salle University, Philippines."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Industrial and Systems Engineering Department, De La Salle University, Philippines."}], "References": []}, {"ArticleId": 94520715, "Title": "Opinion Triplet Extraction for Aspect-Based Sentiment Analysis Using Co-Extraction Approach", "Abstract": "<p>In aspect-based sentiment analysis, tasks are diverse and consist of aspect term extraction, aspect categorization, opinion term extraction, sentiment polarity classification, and relation extractions of aspect and opinion terms. These tasks are generally carried out sequentially using more than one model. However, this approach is inefficient and likely to reduce the model’s performance due to cumulative errors in previous processes. The co-extraction approach with Dual crOss-sharEd RNN (DOER) and span-based multitask acquired better performance than the pipelined approaches in English review data. Therefore, this research focuses on adapting the co-extraction approach where the extraction of aspect terms, opinion terms, and sentiment polarity are conducted simultaneously from review texts. The co-extraction approach was adapted by modifying the original frameworks to perform unhandled subtask to get the opinion triplet. Furthermore, the output layer on these frameworks was modified and trained using a collection of Indonesian-language hotel reviews. The adaptation was conducted by testing the output layer topology for aspect and opinion term extraction as well as variations in the type of recurrent neural network cells and model hyperparameters used, and then analysing the results to obtain a conclusion. The two proposed frameworks were able to carry out opinion triplet extraction and achieve decent performance. The DOER framework achieves better performance than the baselines on aspect and opinion term extraction tasks.</p>", "Keywords": "Aspect-based sentiment analysis; Co-extraction; Dual cross-shared rnn; Opinion triplet; Span-based multitask", "DOI": "10.32890/jict2022.21.2.5", "PubYear": 2022, "Volume": "21", "Issue": "", "JournalId": 66251, "JournalTitle": "Journal of Information and Communication Technology", "ISSN": "1675-414X", "EISSN": "2180-3862", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Informatics, Institut Teknologi Bandung, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Informatics, Institut Teknologi Bandung, Indonesia"}], "References": [{"Title": "A multi-task learning framework for end-to-end aspect sentiment triplet extraction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "479", "Issue": "", "Page": "12", "JournalTitle": "Neurocomputing"}, {"Title": "A span-sharing joint extraction framework for harvesting aspect sentiment triplets", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "242", "Issue": "", "Page": "108366", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 94520716, "Title": "Critical factors affecting the decision to adopt cloud computing in Saudi health care organizations", "Abstract": "<p>This research aims to identify factors that affect cloud computing (CC) adoption decisions among Saudi health care organizations. More precisely, a research model has been developed and tested to present factors that can facilitate or inhibit the decision to adopt CC in such organizations. In addition to its theoretical contributions, this model would help to formulate some recommendations that can aid IT/IS managers in Saudi health care organizations in making the right decisions when adopting cloud solutions. These managerial contributions are very interesting since the health care sector is very advanced and the number of health care organizations searching for new and effective IT solutions is growing quickly in Saudi Arabia. A quantitative type of research was conducted using a self-administered survey methodology. Data collected from 123 Saudi health care organizations are used to test the research hypotheses. They are analyzed by using XLStat by structural equation modeling. The findings revealed that top managers' support and adequate resources, competitive pressure, and security and compatibility, representing organizational, environmental and technology factors, respectively, are perceived as significant factors for adopting a CC system. Considering the direct and indirect effects of the main variables on CC decisions regarding adoption, we propose some recommendations that would benefit decision-makers and scholars in this area.</p>", "Keywords": "adoption decision;cloud computing;health IT/IS;IT/IS managers;Saudi health care organizations;structural equation modeling", "DOI": "10.1002/isd2.12231", "PubYear": 2022, "Volume": "88", "Issue": "6", "JournalId": 6046, "JournalTitle": "The Electronic Journal of Information Systems in Developing Countries", "ISSN": "1681-4835", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Business Administration Department Imam <PERSON> University  Riyadh Saudi Arabia"}], "References": [{"Title": "Technology organization environment framework in cloud computing", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "2", "Page": "716", "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)"}, {"Title": "Understanding the Determinants and Future Challenges of Cloud Computing Adoption for High Performance Computing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "8", "Page": "135", "JournalTitle": "Future Internet"}]}, {"ArticleId": 94520746, "Title": "Graph-powered learning for social networks", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.neucom.2022.05.029", "PubYear": 2022, "Volume": "501", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Georgia State University, Atlanta, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science Department, University of Salerno, Italy"}, {"AuthorId": 3, "Name": "Tooska Dargahi", "Affiliation": "School of Science, Engineering and Environment, University of Salford, Manchester, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Tsinghua University, China"}], "References": []}, {"ArticleId": 94520937, "Title": "Towards the sustainability of small and medium software enterprises through the implementation of software process improvement: Empirical investigation", "Abstract": "<p>To improve and sustain the quality of software products, software process improvement (SPI) is needed. Currently, small and medium software enterprises (SMSEs) represent a high proportion of companies around the world and become a cornerstone in the worldwide industry economy. These companies have realized that improving their process is crucial for success, but they are facing difficulties to implement it due to limited resources, limited knowledge, and time constraints. This study aimed to identify the sustainability success factors (SSFs) that have a positive impact on implementing SPI efforts in SMSEs. Data were collected through a systematic literature review (SLR) approach and quantitatively through a survey questionnaire. A list of 44 SSFs was identified during SLR and empirical study. Results illustrate that there is a positive correlation between the ranks obtained from both dataset ( rs (44) = .548, ρ = .001). Therefore, there would be significant differences between the SSFs identified in both datasets. In conclusion, the top-ranked factors can then be used to guide the SPI coordinators on where they should focus their attention to reach the desired SPI goals, which is crucial to deliver the software products and also facilitates in development of model for SPIs in the future.</p>", "Keywords": "small and medium software enterprises;software process improvement;sustainability success factors;systematic literature review", "DOI": "10.1002/smr.2466", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 2327, "JournalTitle": "Journal of Software: Evolution and Process", "ISSN": "2047-7473", "EISSN": "2047-7481", "Authors": [{"AuthorId": 1, "Name": "Abdul<PERSON><PERSON> Balog<PERSON>", "Affiliation": "Department of Computer Science University of Ilorin  Ilorin Nigeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Engineering and Information Systems The World Islamic Sciences and Education University  Amman Jordan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Information Sciences Universiti Teknologi PETRONAS  Seri Iskandar Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Network and Information Systems The World Islamic Sciences and Education University  Amman Jordan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Western University  London Ontario Canada"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "M3S Empirical Software Engineering Research Unit University of Oulu  Oulu Finland"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Computer Science Sukkur IBA University  Sukkur Pakistan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computing and Information Universiti Malaysia Sabah (UMS)  Labuan Malaysia"}], "References": [{"Title": "Measuring the maturity of Indian small and medium enterprises for unofficial readiness for capability maturity model integration‐based software process improvement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "e2261", "JournalTitle": "Journal of Software: Evolution and Process"}]}, {"ArticleId": 94521003, "Title": "Intelligent analysis for software data: research and applications", "Abstract": "", "Keywords": "", "DOI": "10.1631/FITEE.2230000", "PubYear": 2022, "Volume": "23", "Issue": "5", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Macau University of Science and Technology, Macao, China"}, {"AuthorId": 2, "Name": "<PERSON>bing Sun", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, Sun Yat-sen University, Zhuhai, China"}, {"AuthorId": 4, "Name": "Ge Li", "Affiliation": "School of Computer Science, Peking University, Beijing, China"}], "References": []}, {"ArticleId": 94521025, "Title": "The effect of the pre-rolling high-rolling curve of the ring rolling on the forming quality of large ring", "Abstract": "<p>The rolling curve determines the real-time distribution of feed rate of radial-axial during ring rolling, which is a key factor affecting the forming quality of ring. In order to improve rolling quality, the pre-rolling stage in ring rolling is studied, and the pre-rolling high stage (PRHS) was proposed. The PRHS is that the ring is rolled high during the pre-rolling stage. The rolling curve with the PRHS was designed and verified by experiments. Ten rolling curve models with and without PRHS were established respectively. Based on ABAQUS, the influence of different rolling curves on ring quality is obtained through simulation of hot ring rolling process. The results indicate that during the ring rolling process, a certain amount of PRHS is beneficial to reduce roundness error, wall thickness difference and spread. However, the aforementioned parameters expand with the increase of height and rolling ratio of PRHS. When the rolling ratio and height of PRHS are 1.02 and 2 mm, the roundness error and wall thickness difference of the rolling curve are the smallest; the minimum value of spread is reached when the rolling ratio and height of PRHS are 1.05 and 10 mm, respectively.</p>", "Keywords": "The forming quality; Pre-rolling high stage; Rolling curve; Finite element simulation", "DOI": "10.1007/s00170-022-09334-6", "PubYear": 2022, "Volume": "121", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechanical Engineering and Mechanics, Ningbo University, Ningbo, China; Zhejiang Provincial Key Lab of Part Rolling Technology, Ningbo University, Ningbo, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechanical Engineering and Mechanics, Ningbo University, Ningbo, China; Zhejiang Provincial Key Lab of Part Rolling Technology, Ningbo University, Ningbo, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Chen", "Affiliation": "Faculty of Mechanical Engineering and Mechanics, Ningbo University, Ningbo, China; Zhejiang Provincial Key Lab of Part Rolling Technology, Ningbo University, Ningbo, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechanical Engineering and Mechanics, Ningbo University, Ningbo, China; Zhejiang Provincial Key Lab of Part Rolling Technology, Ningbo University, Ningbo, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Department of Mechanics of Material Plasticity and Resource-Saving Process, National Technical University of Ukraine, Kiev, Ukraine"}], "References": []}, {"ArticleId": 94521130, "Title": "A Virtual and On-site Hackathon to Recruit High School Students within Cybersecurity Major", "Abstract": "", "Keywords": "", "DOI": "10.6025/isej/2022/8/2/31-38", "PubYear": 2021, "Volume": "8", "Issue": "2", "JournalId": 56527, "JournalTitle": "Information Security Education Journal (ISEJ)", "ISSN": "2349-8161", "EISSN": "2349-817X", "Authors": [{"AuthorId": 1, "Name": "Yunkai Liu", "Affiliation": ""}], "References": []}, {"ArticleId": 94521219, "Title": "Recent advances on effective and efficient deep learning-based solutions", "Abstract": "<p>This editorial briefly analyses, describes, and provides a short summary of a set of selected papers published in a special issue focused on deep learning methods and architectures and their application to several domains and research areas. The set of selected and published articles covers several aspects related to two basic aspects in deep learning (DL) methods, <i>efficiency</i> of the models and <i>effectiveness</i> of the architectures These papers revolve around different interesting application domains such as health (e.g. cancer, polyps, melanoma, mental health), wearable technologies solar irradiance, social networks, cloud computing, wind turbines, object detection, music, and electricity, among others. This editorial provides a short description of each published article and a brief analysis of their main contributions.</p><p>© The Author(s), under exclusive licence to Springer-Verlag London Ltd., part of Springer Nature 2022.</p>", "Keywords": "", "DOI": "10.1007/s00521-022-07344-9", "PubYear": 2022, "Volume": "34", "Issue": "13", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Sistemas Informáticos, Universidad, Politécnica de Madrid, Madrid, Spain."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Sistemas Informáticos, Universidad, Politécnica de Madrid, Madrid, Spain."}], "References": [{"Title": "Toward hardware-aware deep-learning-based dialogue systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10397", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Deep learning-based ambient assisted living for self-management of cardiovascular conditions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10449", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Using a Multi-view Convolutional Neural Network to monitor solar irradiance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10295", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Suicidal ideation and mental disorder detection with attentive relation networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10309", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Deep learning and fuzzy logic to implement a hybrid wind turbine pitch control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10503", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A deep embedded refined clustering approach for breast cancer distinction based on DNA methylation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10243", "JournalTitle": "Neural Computing and Applications"}, {"Title": "On the post-hoc explainability of deep echo state networks for time series forecasting, image and video classification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10257", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Citation recommendation employing heterogeneous bibliographic network embedding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10229", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Real-time polyp detection model using convolutional neural networks", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10375", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An ensemble of deep transfer learning models for handwritten music symbol recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10409", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A deep learning-based resource usage prediction model for resource provisioning in an autonomic cloud computing environment", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10211", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An ensemble-based convolutional neural network model powered by a genetic algorithm for melanoma diagnosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10429", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Optimized convolutional neural network architectures for efficient on-device vision-based object detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Florentino <PERSON>-Riverola", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10469", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Novel convolutional neural networks for efficient classification of rotated and scaled images", "Authors": "<PERSON><PERSON><PERSON>; Piotr S. Szczepaniak", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10519", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Federated meta-learning for spatial-temporal prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10355", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Iterative neural networks for adaptive inference on resource-constrained devices", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10321", "JournalTitle": "Neural Computing and Applications"}, {"Title": "FCSF-TABS: two-stage abstractive summarization with fact-aware reinforced content selection and fusion", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10547", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A deep LSTM network for the Spanish electricity consumption forecasting", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; A. Troncoso", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10533", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Combined angular margin and cosine margin softmax loss for music classification based on spectrograms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10337", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 94521246, "Title": "A systematic literature review on recent trends of machine learning applications in additive manufacturing", "Abstract": "<p>Additive manufacturing (AM) offers the advantage of producing complex parts more efficiently and in a lesser production cycle time as compared to conventional subtractive manufacturing processes. It also provides higher flexibility for diverse applications by facilitating the use of a variety of materials and different processing technologies. With the exceptional growth of computing capability, researchers are extensively using machine learning (ML) techniques to control the performance of every phase of AM processes, such as design, process parameters modeling, process monitoring and control, quality inspection, and validation. Also, ML methods have made it possible to develop cybermanufacturing for AM systems and thus revolutionized Industry 4.0. This paper presents the state-of-the-art applications of ML in solving numerous problems related to AM processes. We give an overview of the research trends in this domain through a systematic literature review of relevant journal articles and conference papers. We summarize recent development and existing challenges to point out the direction of future research scope. This paper can provide AM researchers and practitioners with the latest information consequential for further development. </p>", "Keywords": "Review; Additive manufacturing; 3D printing; Machine learning; Deep learning; Industry 4.0; Cybermanufacturing", "DOI": "10.1007/s10845-022-01957-6", "PubYear": 2023, "Volume": "34", "Issue": "6", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Military Institute of Science and Technology, Dhaka, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Houston, Houston, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bangladesh University of Engineering and Technology, Dhaka, Bangladesh"}], "References": [{"Title": "Modeling of EHD inkjet printing performance using soft computing-based approaches", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "1", "Page": "571", "JournalTitle": "Soft Computing"}, {"Title": "A deep neural network for classification of melt-pool images in metal additive manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "375", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A convolutional approach to quality monitoring for laser manufacturing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "3", "Page": "789", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Data-Driven Predictive Modeling of Tensile Behavior of Parts Fabricated by Cooperative 3D Printing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "2", "Page": "", "JournalTitle": "Journal of Computing and Information Science in Engineering"}, {"Title": "Automated Real‐Time Detection and Prediction of Interlayer Imperfections in Additive Manufacturing Processes Using Artificial Intelligence", "Authors": "<PERSON><PERSON><PERSON> Jin; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "1900130", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "ARIMA-GMDH: a low-order integrated approach for predicting and optimizing the additive manufacturing process parameters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "1-2", "Page": "701", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Optimization of parameters of micro-plasma transferred arc additive manufacturing process using real coded genetic algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "3-4", "Page": "1239", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Image Data-Based Surface Texture Characterization and Prediction Using Machine Learning Approaches for Additive Manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "2", "Page": "", "JournalTitle": "Journal of Computing and Information Science in Engineering"}, {"Title": "A convolutional neural network approach on bead geometry estimation for a laser cladding system", "Authors": "<PERSON>ça<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "5-6", "Page": "1811", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Real-time penetration state monitoring using convolutional neural network for laser welding of tailor rolled blanks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "348", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Improving surface finish quality in extrusion-based 3D concrete printing using machine learning-based extrudate geometry control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "2", "Page": "178", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "Towards an automated decision support system for the identification of additive manufacturing part candidates", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "8", "Page": "1917", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Quality analysis in metal additive manufacturing with deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "8", "Page": "2003", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A data-driven approach for predicting printability in metal additive manufacturing processes", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "7", "Page": "1769", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Detection of powder bed defects in selective laser sintering using convolutional neural network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "5-6", "Page": "2485", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Neural networks for trajectory evaluation in direct laser writing", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "5-6", "Page": "2563", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Prediction of geometry deviations in additive manufactured parts: comparison of linear regression with machine learning algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "179", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Study of the hinge thickness deviation for a 316L parallelogram flexure mechanism fabricated via selective laser melting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "5", "Page": "1411", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A novel strategy for multi-part production in additive manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "5-6", "Page": "1237", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Machine learning applications in production lines: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106773", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Control of deposition height in WAAM using visual inspection of previous and current layers", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "8", "Page": "2209", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Automated inspection in robotic additive manufacturing using deep learning for layer deformation detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "3", "Page": "771", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Detecting voids in 3D printing using melt pool time series data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "3", "Page": "845", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A hybrid deep learning model of process-build interactions in additive manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "460", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 94521520, "Title": "Data Assimilation by Neural Network for Ocean Circulation: Parallel Implementation", "Abstract": "Data assimilation (DA) is an essential issue for operational prediction centers, where a computer code is applied to simulate physical phenomena by solving differential equations. The procedure to determine the best initial condition combining data from observation and previous forecasting (background) is carried out by a data assimilation method. The Kalman filter (KF) is a technique for data assimilation, but it is computationally expensive. An approach to reduce the computational effort for DA is to emulate the KF by a neural network. The multi-layer perceptron neural network (MLP-NN) is employed to emulate the Kalman in a 2D ocean circulation model, and algorithmic complexity to KF and NN is presented. A shallow-water system models the ocean dynamics. Synthetic measurements are used for evaluating the MLP-NN for the data assimilation process. Here, a parallel version for the DA procedure by the neural network is described and tested, showing the performance improvement for a parallel version of the NN-DA.", "Keywords": "data assimilation;artificial neural network;shallow water equations;parallel processing", "DOI": "10.14529/jsfi220105", "PubYear": 2022, "Volume": "9", "Issue": "1", "JournalId": 38172, "JournalTitle": "Supercomputing Frontiers and Innovations", "ISSN": "2409-6008", "EISSN": "2313-8734", "Authors": [{"AuthorId": 1, "Name": "Haroldo F. Campos Velho", "Affiliation": "National Institute for Space Research, São José dos Campos, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Western Pará, Santarém, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Independent researcher, <PERSON>, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National Laboratory for Scientific Computing, Petrópolis, Brazil"}, {"AuthorId": 5, "Name": "<PERSON> E. <PERSON>", "Affiliation": "National Laboratory for Scientific Computing, Petrópolis, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "National Laboratory for Scientific Computing, Petrópolis, Brazil"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Federal Center for Technological Education <PERSON><PERSON>o Su<PERSON>ow da Fonseca, Rio de Janeiro, Brazil"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Federal Center for Technological Education Ce<PERSON>o Suckow da Fonseca, Petrópolis, Brazil; Polytechnic Institute of Tomar, Tomar, Portugal"}], "References": []}, {"ArticleId": 94521540, "Title": "A principle-based approach to AI: the case for European Union and Italy", "Abstract": "As Artificial Intelligence (AI) becomes more and more pervasive in our everyday life, new questions arise about its ethical and social impacts. Such issues concern all stakeholders involved in or committed to the design, implementation, deployment, and use of the technology. The present document addresses these preoccupations by introducing and discussing a set of practical obligations and recommendations for the development of applications and systems based on AI techniques. With this work we hope to contribute to spreading awareness on the many social challenges posed by AI and encouraging the establishment of good practices throughout the relevant social areas. As points of novelty, the paper elaborates on an integrated view that combines both human rights and ethical concepts to reap the benefits of the two approaches. Moreover, it proposes innovative recommendations, such as those on redress and governance, which add further insight to the debate. Finally, it incorporates a specific focus on the Italian Constitution, thus offering an example of how core legislations of Member States might contribute to further specify and enrich the EU normative framework on AI.", "Keywords": "AI Ethics; Principles; Values; Recommendations; AI Governance", "DOI": "10.1007/s00146-022-01453-8", "PubYear": 2023, "Volume": "38", "Issue": "2", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Independent, London, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politecnico Di Milano, Milan, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Brescia, Brescia, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Copernicani, Milan, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Bologna, Bologna, Italy"}], "References": [{"Title": "AI K–12 Education Service", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "2", "Page": "125", "JournalTitle": "KI - Künstliche Intelligenz"}]}, {"ArticleId": 94521591, "Title": "SentiCode: A new paradigm for one-time training and global prediction in multilingual sentiment analysis", "Abstract": "<p>The main objective of multilingual sentiment analysis is to analyze reviews regardless of the original language in which they are written. Switching from one language to another is very common on social media platforms. Analyzing these multilingual reviews is a challenge since each language is different in terms of syntax, grammar, etc. This paper presents a new language-independent representation approach for sentiment analysis, SentiCode. Unlike previous work in multilingual sentiment analysis, the proposed approach does not rely on machine translation to bridge the gap between different languages. Instead, it exploits common features of languages, such as part-of-speech tags used in Universal Dependencies. Equally important, SentiCode enables sentiment analysis in multi-language and multi-domain environments simultaneously. Several experiments were conducted using machine/deep learning techniques to evaluate the performance of SentiCode in multilingual (English, French, German, Arabic, and Russian) and multi-domain environments. In addition, the vocabulary proposed by SentiCode and the effect of each token were evaluated by the ablation method. The results highlight the 70% accuracy of SentiCode, with the best trade-off between efficiency and computing time (training and testing) in a total of about 0.67 seconds, which is very convenient for real-time applications.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2022.</p>", "Keywords": "Machine learning;Multilingual sentiment analysis;Natural language processing;Part-of-speech tags;SentiCode", "DOI": "10.1007/s10844-022-00714-8", "PubYear": 2022, "Volume": "59", "Issue": "2", "JournalId": 7081, "JournalTitle": "Journal of Intelligent Information Systems", "ISSN": "0925-9902", "EISSN": "1573-7675", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "MISC Laboratory, Constantine 2 University Abdelhamid <PERSON>, Constantine, 25000 Algeria."}, {"AuthorId": 2, "Name": "Abdelkrim Bo<PERSON>ul", "Affiliation": "MISC Laboratory, Constantine 2 University Abdelhamid <PERSON>, Constantine, 25000 Algeria."}], "References": [{"Title": "Sentiment analysis using rule-based and case-based reasoning", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "1", "Page": "51", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Empirical study of sentiment analysis tools and techniques on societal topics", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "56", "Issue": "2", "Page": "379", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Financial markets sentiment analysis: developing a specialized Lexicon", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "1", "Page": "127", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Universal Dependencies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "255", "JournalTitle": "Computational Linguistics"}, {"Title": "Deep learning and multilingual sentiment analysis on social media data: An overview", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107373", "JournalTitle": "Applied Soft Computing"}, {"Title": "BiERU: Bidirectional emotional recurrent unit for conversational sentiment analysis", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "467", "Issue": "", "Page": "73", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 94521600, "Title": "An Integrated Study of User Acceptance and Resistance on Voice Commerce", "Abstract": "<p>Virtual assistants are becoming significant in the area of voice commerce. Voice commerce creates an environment where orders and payments can be done through voice recognition, increasing its accessibility in comparison to other existing commercial transaction methods. Thus, the field is expected to grow into a promising industry. However, the full bloom of voice commerce depends on user acceptance, as well as technological improvement. This study develops a conceptual framework to describe factors influencing user acceptance to voice commerce, which are analyzed with the structural equation model by combining the technology acceptance model (TAM) and the model of innovation resistance (MIR). With accuracy, social presence, and interactivity of a virtual assistant, along with user attributes such as user innovativeness and experience, as variables, a survey of 151 Koreans in their 20s and 30s is conducted. Moreover, the impact of three factors, relative advantage, perceived ease of use, and perceived risk, on innovation acceptance and resistance is analyzed to find key variables that affect the resistance to and acceptance of voice commerce. These findings provide notable implications to companies currently using voice commerce platforms and guidance to an emerging commercial trading system.</p>", "Keywords": "Voice commerce; virtual assistant; innovation resistance; behavioral intention; interactivity", "DOI": "10.1142/S0219877022500250", "PubYear": 2022, "Volume": "19", "Issue": "7", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Handong Global University, School of Entrepreneurship and ICT, Pohang, 37554, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Handong Global University, School of Entrepreneurship and ICT, Pohang, 37554, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "KAIST, School of Business and Technology Management, Daejeon, 34141, South Korea"}], "References": []}, {"ArticleId": 94521687, "Title": "Worst‐Case Rigidity Analysis and Optimization for Assemblies with Mechanical Joints", "Abstract": "<p>We study structural rigidity for assemblies with mechanical joints. Existing methods identify whether an assembly is structurally rigid by assuming parts are perfectly rigid. Yet, an assembly identified as rigid may not be that “rigid” in practice, and existing methods cannot quantify how rigid an assembly is. We address this limitation by developing a new measure, worst-case rigidity , to quantify the rigidity of an assembly as the largest possible deformation that the assembly undergoes for arbitrary external loads of fixed magnitude. Computing worst-case rigidity is non-trivial due to non-rigid parts and different joint types. We thus formulate a new computational approach by encoding parts and their connections into a stiffness matrix, in which parts are modeled as deformable objects and joints as soft constraints. Based on this, we formulate worst-case rigidity analysis as an optimization that seeks the worst-case deformation of an assembly for arbitrary external loads, and solve the optimization problem via an eigenanalysis. Furthermore, we present methods to optimize the geometry and topology of various assemblies to enhance their rigidity, as guided by our rigidity measure. In the end, we validate our method on a variety of assembly structures with physical experiments and demonstrate its effectiveness by designing and fabricating several structurally rigid assemblies.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Modeling and simulation", "DOI": "10.1111/cgf.14490", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Chinese University of Hong Kong; IST Austria"}, {"AuthorId": 2, "Name": "Jingyu Hu", "Affiliation": "The Chinese University of Hong Kong"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The Chinese University of Hong Kong"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Singapore University of Technology and Design"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Hasso <PERSON>lattner Institute"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "IST Austria"}, {"AuthorId": 7, "Name": "Chi‐Wing Fu", "Affiliation": "The Chinese University of Hong Kong"}], "References": [{"Title": "State of the Art on Computational Design of Assemblies with Rigid Parts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2", "Page": "633", "JournalTitle": "Computer Graphics Forum"}, {"Title": "MOCCA", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 94521688, "Title": "Recursive analytic spherical harmonics gradient for spherical lights", "Abstract": "<p>When rendering images using Spherical Harmonics (SH), the projection of a spherical function on the SH basis remains a computational challenge both for high-frequency functions and for emission functions from complex light sources. Recent works investigate efficient SH projection of the light field coming from polygonal and spherical lights. To further reduce the rendering time, instead of computing the SH coefficients at each vertex of a mesh or at each fragment on an image, it has been shown, for polygonal area light, that computing both the SH coefficients and their spatial gradients on a grid covering the scene allows the efficient and accurate interpolation of these coefficients at each shaded point. In this paper, we develop analytical recursive formulae to compute the spatial gradients of SH coefficients for spherical light. This requires the efficient computation of the spatial gradients of the SH basis function that we also derive. Compared to existing method for polygonal light, our method is faster, requires less memory and scales better with respect to the SH band limit. We also show how to approximate polygonal lights using spherical lights to benefit from our derivations. To demonstrate the effectiveness of our proposal, we integrate our algorithm in a shading system able to render fully dynamic scenes with several hundreds of spherical lights in real time.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Rendering; Rasterization;Real-time simulation", "DOI": "10.1111/cgf.14482", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "IRIT, Université de Toulouse, CNRS, INP, UT3, Toulouse, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "IRIT, Université de Toulouse, CNRS, INP, UT3, Toulouse, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "IRIT, Université de Toulouse, CNRS, INP, UT3, Toulouse, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "IRIT, Université de Toulouse, CNRS, INP, UT3, Toulouse, France"}], "References": [{"Title": "Analytic spherical harmonic gradients for real-time rendering with many polygonal area lights", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Path-space differentiable rendering", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "BRDF importance sampling for polygonal lights", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Moving Basis Decomposition for Precomputed Light Transport", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "127", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Unified Shape and SVBRDF Recovery using Differentiable Monte Carlo Rendering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "101", "JournalTitle": "Computer Graphics Forum"}]}, {"ArticleId": 94521692, "Title": "<PERSON><PERSON><PERSON>: A Hardware‐Agnostic Differentiable Renderer with Reactive Shader Packing and Soft Rasterization", "Abstract": "<p>Differentiable rendering (DR) enables various computer graphics and computer vision applications through gradient-based optimization with derivatives of the rendering equation. Most rasterization-based approaches are built on general-purpose automatic differentiation (AD) libraries and DR-specific modules handcrafted using CUDA. Such a system design mixes DR algorithm implementation and algorithm building blocks, resulting in hardware dependency and limited performance. In this paper, we present a practical hardware-agnostic differentiable renderer called <PERSON><PERSON><PERSON>, which is based on a new full AD design. The DR algorithms of Dressi are fully written in our Vulkan-based AD for DR, Dressi-AD, which supports all primitive operations for DR. Dressi-AD and our inverse UV technique inside it bring hardware independence and acceleration by graphics hardware. Stage packing, our runtime optimization technique, can adapt hardware constraints and efficiently execute complex computational graphs of DR with reactive cache considering the render pass hierarchy of Vulkan. HardSoftRas, our novel rendering process, is designed for inverse rendering with a graphics pipeline. Under the limited functionalities of the graphics pipeline, HardSoftRas can propagate the gradients of pixels from the screen space to far-range triangle attributes. Our experiments and applications demonstrate that <PERSON><PERSON><PERSON> establishes hardware independence, high-quality and robust optimization with fast speed, and photorealistic rendering.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Rasterization;Shape inference", "DOI": "10.1111/cgf.14455", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Huawei Technologies Japan K.K.; These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Huawei Technologies Japan K.K.; These authors contributed equally to this work"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Huawei Technologies Japan K.K.; These authors contributed equally to this work"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Huawei Technologies Japan K.K."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Huawei Technologies Japan K.K."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Huawei Technologies Japan K.K."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Huawei Technologies Japan K.K."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Huawei Technologies Japan K.K."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Huawei Technologies Japan K.K."}], "References": [{"Title": "Large steps in inverse rendering of geometry", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 94521695, "Title": "A \n ‐ULMPM: An Adaptively Updated Lagrangian Material Point Method for Efficient Physics Simulation without Numerical Fracture", "Abstract": "<p>We present an adaptively updated Lagrangian Material Point Method (A-ULMPM) to alleviate non-physical artifacts, such as the cell-crossing instability and numerical fracture, that plague state-of-the-art Eulerian formulations of MPM, while still allowing for large deformations that arise in fluid simulations. A-ULMPM spans MPM discretizations from total Lagrangian formulations to Eulerian formulations. We design an easy-to-implement physics-based criterion that allows A-ULMPM to update the reference configuration adaptively for measuring physical states, including stress, strain, interpolation kernels and their derivatives. For better efficiency and conservation of angular momentum, we further integrate the APIC [JSS<sup>*</sup>15] and MLS-MPM [HFG<sup>*</sup>18] formulations in A-ULMPM by augmenting the accuracy of velocity rasterization using both the local velocity and its first-order derivatives. Our theoretical derivations use a nodal discretized Lagrangian, instead of the weak form discretization in MLS-MPM [HFG<sup>*</sup>!!18], and naturally lead to a “modified” MLS-MPM in A-ULMPM, which can recover MLS-MPM using a completely Eulerian formulation. A-ULMPM does not require significant changes to traditional Eulerian formulations of MPM, and is computationally more efficient since it only updates interpolation kernels and their derivatives during large topology changes. We present end-to-end 3D simulations of stretching and twisting hyperelastic solids, viscous flows, splashing liquids, and multi-material interactions with large deformations to demonstrate the efficacy of our new method.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Computer graphics;Physical simulation", "DOI": "10.1111/cgf.14477", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Rutgers University; Joint first authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Rutgers University; Joint first authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Rutgers University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Rutgers University"}], "References": [{"Title": "AnisoMPM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "IQ-MPM", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "A novel discretization and numerical solver for non-fourier diffusion", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "A unified second-order accurate in time MPM formulation for simulating viscoelastic liquids with phase change", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Bijective and coarse high-order tetrahedral meshes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 94521706, "Title": "Meshlets and How to Shade Them: A Study on Texture‐Space Shading", "Abstract": "<p>Commonly used image-space layouts of shading points, such as used in deferred shading, are strictly view-dependent, which restricts efficient caching and temporal amortization. In contrast, texture-space layouts can represent shading on all surface points and can be tailored to the needs of a particular application. However, the best grouping of shading points—which we call a shading unit—in texture space remains unclear. Choices of shading unit granularity (how many primitives or pixels per unit) and in shading unit parametrization (how to assign texture coordinates to shading points) lead to different outcomes in terms of final image quality, overshading cost, and memory consumption. Among the possible choices, shading units consisting of larger groups of scene primitives, so-called meshlets, remain unexplored as of yet. In this paper, we introduce a taxonomy for analyzing existing texture-space shading methods based on the group size and parametrization of shading units. Furthermore, we introduce a novel texture-space layout strategy that operates on large shading units: the meshlet shading atlas. We experimentally demonstrate that the meshlet shading atlas outperforms previous approaches in terms of image quality, run-time performance and temporal upsampling for a given number of fragment shader invocations. The meshlet shading atlas lends itself to work together with popular cluster-based rendering of meshes with high geometric detail.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Rendering;Texturing", "DOI": "10.1111/cgf.14474", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graz University of Technology, Institute of Computer Graphics and Vision, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Graz University of Technology, Institute of Computer Graphics and Vision, Austria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graz University of Technology, Institute of Computer Graphics and Vision, Austria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Graz University of Technology, Institute of Computer Graphics and Vision, Austria"}], "References": [{"Title": "A Survey of Temporal Antialiasing Techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "2", "Page": "607", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Temporally Adaptive Shading Reuse for Real-Time Rendering and Virtual Reality", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "SnakeBinning: Efficient Temporally Coherent Triangle Packing for Shading Streaming", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2", "Page": "475", "JournalTitle": "Computer Graphics Forum"}]}, {"ArticleId": 94521709, "Title": "MaterIA: Single Image High‐Resolution Material Capture in the Wild", "Abstract": "<p>We propose a hybrid method to reconstruct a physically-based spatially varying BRDF from a single high resolution picture of an outdoor surface captured under natural lighting conditions with any kind of camera device. Relying on both deep learning and explicit processing, our PBR material acquisition handles the removal of shades, projected shadows and specular highlights present when capturing a highly irregular surface and enables to properly retrieve the underlying geometry. To achieve this, we train two cascaded U-Nets on physically-based materials, rendered under various lighting conditions, to infer the spatially-varying albedo and normal maps. Our network processes relatively small image tiles ( 512 × 512 pixels) and we propose a solution to handle larger image resolutions by solving a Poisson system across these tiles. We complete this pipeline with analytical solutions to reconstruct height, roughness and ambient occlusion.</p>", "Keywords": "Material Capture;SVBRDF;Shadow Removal;Deep Learning;Dataset Synthesis;Delighting", "DOI": "10.1111/cgf.14466", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Adobe Research"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Adobe Research"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Adobe Research"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Adobe Research"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Adobe Research"}], "References": [{"Title": "Guided Fine‐Tuning for Large‐Scale Material Transfer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "91", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Adversarial Single‐Image SVBRDF Estimation with Hybrid Training", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2", "Page": "315", "JournalTitle": "Computer Graphics Forum"}]}, {"ArticleId": 94521710, "Title": "Deep Reconstruction of 3D Smoke Densities from Artist Sketches", "Abstract": "<p>Creative processes of artists often start with hand-drawn sketches illustrating an object. Pre-visualizing these keyframes is especially challenging when applied to volumetric materials such as smoke. The authored 3D density volumes must capture realistic flow details and turbulent structures, which is highly non-trivial and remains a manual and time-consuming process. We therefore present a method to compute a 3D smoke density field directly from 2D artist sketches, bridging the gap between early-stage prototyping of smoke keyframes and pre-visualization. From the sketch inputs, we compute an initial volume estimate and optimize the density iteratively with an updater CNN. Our differentiable sketcher is embedded into the end-to-end training, which results in robust reconstructions. Our training data set and sketch augmentation strategy are designed such that it enables general applicability. We evaluate the method on synthetic inputs and sketches from artists depicting both realistic smoke volumes and highly non-physical smoke shapes. The high computational performance and robustness of our method at test time allows interactive authoring sessions of volumetric density fields for rapid prototyping of ideas by novice users.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Shape modeling;Neural networks", "DOI": "10.1111/cgf.14461", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ETH Zurich"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ETH Zurich; Max Planck Institute for Informatics"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ETH Zurich"}, {"AuthorId": 4, "Name": "Jingwei Tang", "Affiliation": "ETH Zurich"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "ETH Zurich; Inria, Université Côte d'Azur, France"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "ETH Zurich"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "ETH Zurich"}], "References": [{"Title": "Lagrangian neural style transfer for fluids", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "A Rapid, End‐to‐end, Generative Model for Gaseous Phenomena from Limited Views", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "6", "Page": "242", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Honey, I Shrunk the Domain: Frequency‐aware Force Field Reduction for Efficient Fluids Optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2", "Page": "339", "JournalTitle": "Computer Graphics Forum"}]}, {"ArticleId": 94521714, "Title": "Procedural Roof Generation From a Single Satellite Image", "Abstract": "<p>Urban procedural modeling has benefited from recent advances in deep learning and computer graphics. However, few, if any, approaches have automatically produced procedural building roof models from a single overhead satellite image. Large-scale roof modeling is important for a variety of applications in urban content creation and in urban planning (e.g., solar panel planning, heating/cooling/rainfall modeling). While the allure of modeling only from satellite images is clear, unfortunately structures obtained from the satellite images are often in low-resolution, noisy and heavily occluded, thus getting a clean and complete view of urban structures is difficult. In this paper, we present a framework that exploits the inherent structure present in man-made buildings and roofs by explicitly identifying the compact space of potential building shapes and roof structures. Then, we utilize this relatively compact space with a two-component solution combining procedural modeling and deep learning. Specifically, we use a <b>building decomposition component</b> to separate the building into roof parts and predict regularized building footprints in a procedural format, and use a roof <b>ridge detection component</b> to refine the individual roof parts by estimating the procedural roof ridge parameters. Our qualitative and quantitative assessments over multiple satellite datasets show that our method outperforms various state-of-the-art methods.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Shape analysis;Image-based rendering", "DOI": "10.1111/cgf.14472", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Purdue University, West Lafayette, IN, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Purdue University, West Lafayette, IN, USA"}], "References": [{"Title": "Deep Learning for Understanding Satellite Imagery: An Experimental Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "", "Page": "85", "JournalTitle": "Frontiers in Artificial Intelligence"}]}, {"ArticleId": 94521717, "Title": "Interaction Fields: Intuitive Sketch‐based Steering Behaviors for Crowd Simulation", "Abstract": "<p>The real-time simulation of human crowds has many applications. In a typical crowd simulation, each person ('agent') in the crowd moves towards a goal while adhering to local constraints. Many algorithms exist for specific local ‘steering’ tasks such as collision avoidance or group behavior. However, these do not easily extend to completely new types of behavior, such as circling around another agent or hiding behind an obstacle. They also tend to focus purely on an agent's velocity without explicitly controlling its orientation. This paper presents a novel sketch-based method for modelling and simulating many steering behaviors for agents in a crowd. Central to this is the concept of an interaction field (IF): a vector field that describes the velocities or orientations that agents should use around a given ‘source’ agent or obstacle. An IF can also change dynamically according to parameters, such as the walking speed of the source agent. IFs can be easily combined with other aspects of crowd simulation, such as collision avoidance. Using an implementation of IFs in a real-time crowd simulation framework, we demonstrate the capabilities of IFs in various scenarios. This includes game-like scenarios where the crowd responds to a user-controlled avatar. We also present an interactive tool that computes an IF based on input sketches. This IF editor lets users intuitively and quickly design new types of behavior, without the need for programming extra behavioral rules. We thoroughly evaluate the efficacy of the IF editor through a user study, which demonstrates that our method enables non-expert users to easily enrich any agent-based crowd simulation with new agent interactions.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Motion path planning;Intelligent agents;Real-time simulation;• Human-centered computing → Graphical user interfaces", "DOI": "10.1111/cgf.14491", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "A. <PERSON>s", "Affiliation": "Univ Rennes, Inria, CNRS, IRISA, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Univ Rennes, Inria, CNRS, IRISA, France; Breda University of Applied Sciences, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Univ Rennes, Inria, CNRS, IRISA, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Univ Rennes, Inria, CNRS, IRISA, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Univ Rennes, Inria, CNRS, IRISA, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Univ Rennes, Inria, CNRS, IRISA, France"}], "References": [{"Title": "A Survey on Sketch Based Content Creation: from the Desktop to Virtual and Augmented Reality", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "2", "Page": "757", "JournalTitle": "Computer Graphics Forum"}]}, {"ArticleId": 94521773, "Title": "Augmented assistive technology: the importance of tailoring technology solutions for people living with dementia at home", "Abstract": "This research investigates the benefits of personalised augmented assistive technology to support people living with dementia in their daily activities in the home. With the aim to achieve a tailored and person-centred technology solution for each participant, we discuss the challenges and potential benefits of Augmented Reality (AR) for people living with dementia. We highlight the need to take a human-centred design approach for this user group and demonstrate how AR technology should not drive the research focus in this challenging field but should instead be responsive to the needs and goals of stakeholders. The qualitative approach demonstrates the benefits of using modular technology for the shifting needs of individuals with dementia. The breadth of tailored solutions is described via two detailed participant stories. We discuss the multi-sensory dimensions of AR and how this is important to maintain and increase independence for the user group. We investigate the challenges and benefits of using technologies including Microsoft HoloLens, digital projectors, smart mirrors, Amazon Alexa, and aroma diffusers for assistive purposes.", "Keywords": "Augmented reality ; Dementia ; Assistive technology ; Tailored solutions ; Human-centered design ; Speech interactions", "DOI": "10.1016/j.ijhcs.2022.102852", "PubYear": 2022, "Volume": "165", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Swinburne University of Technology, John <PERSON> Hawthorn VIC 3122 Australia;Corresponding author at: Swinburne University of Technology - Hawthorn Campus: Swinburne University of Technology, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Swinburne University of Technology, John <PERSON> Hawthorn VIC 3122 Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Swinburne University of Technology, John <PERSON> Hawthorn VIC 3122 Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON>ia <PERSON>, 98-104 Riversdale Road Hawthorn VIC 3122 Australia"}], "References": []}, {"ArticleId": 94521832, "Title": "Reducing Ransomware Crime: Analysis of Victims’ Payment Decisions", "Abstract": "In this paper, the decision-making processes of victims during ransomware attacks were analysed. Forty-one ransomware attacks using qualitative data collected from organisations and police officers from cybercrime units in the UK were examined. The hypothesis tested in this paper is that victims carefully analyse the situation before deciding whether to pay a ransom. This research confirms that victims often weigh the costs and benefits of interventions before making final decisions, and that their decisions are based on a range of reasons. As ransomware attacks become more prevalent globally, the findings should be highly relevant to those developing guidance and policies to prevent or minimise ransom payments.", "Keywords": "Ransomware attacks ; Organisations ; Decision processes ; Ransom payments ; Cybercrime", "DOI": "10.1016/j.cose.2022.102760", "PubYear": 2022, "Volume": "119", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Zayed University, Liberty Building, Leeds, United Arab Emirates;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zayed University, Liberty Building, Leeds, United Arab Emirates"}], "References": []}, {"ArticleId": 94521904, "Title": "Fast multiplicative algorithms for symmetric nonnegative tensor factorization", "Abstract": "Symmetric nonnegative tensor factorization (SNTF) is an important tool for clustering analysis. To date, most of algorithms for SNTF are based on multiplicative update rules, which have many attractive properties, e.g., they are often simple to implement and can enforce nonnegativity without extra projection steps. However, the existing multiplicative algorithms often converge slowly due to the conservative multiplicative learning steps or the use of low-level BLAS (basic linear algebra subprograms) in their implementation. In this paper, three new multiplicative algorithms are proposed for SNTF to overcome the drawback of slow convergence. First, a parallel multiplicative algorithm, which can be implemented with high-level BLAS, is derived by auxiliary optimization. To further accelerate the convergence, two new parallel multiplicative algorithms, which enable larger learning steps for improving efficiency, are developed based on weighted geometric mean and weighted arithmetic mean, respectively. Finally, we apply the proposed algorithms to multiway probabilistic clustering, where a new hyper-stochastic normalization scheme based on Euclidean distance is developed for better data preprocessing. The experiment results on both synthetic and real-world data show that the proposed SNTF algorithms converge faster than the state-of-the-art algorithms.", "Keywords": "Symmetric nonnegative tensor factorization ; Multiplicative updates ; Auxiliary optimization ; Multiway probabilistic clustering", "DOI": "10.1016/j.neucom.2022.05.046", "PubYear": 2022, "Volume": "500", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou 510006, China;Key Laboratory for IoT Intelligent Information Processing and System Integration of Ministry of Education, Guangzhou 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou 510006, China;Key Laboratory for IoT Intelligent Information Processing and System Integration of Ministry of Education, Guangzhou 510006, China;Corresponding author at: School of Automation, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou 510006, China;Guangdong Key Laboratory of IoT Information Technology, Guangzhou 510006, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou 510006, China;Guangdong Key Laboratory of IoT Information Technology, Guangzhou 510006, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou 510006, China;Guangdong-HongKong-Macao Joint Laboratory for Smart Discrete Manufacturing, Guangzhou 510006, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou 510006, China;Guangdong Key Laboratory of IoT Information Technology, Guangzhou 510006, China"}], "References": [{"Title": "Multiplicative Algorithms for Symmetric Nonnegative Tensor Factorizations and Its Applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "83", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}]}, {"ArticleId": 94521954, "Title": "Fusing Earth observation and socioeconomic data to increase the transferability of large-scale urban land use classification", "Abstract": "Monitoring and understanding urban development requires up-to-date information on multiple urban land-use classes. Manual classification and deep learning approaches based on very-high resolution imagery have been applied successfully, but the required resources limits their capacity to map urban land use at larger scales. Here, we use a combination of open-source satellite imagery, constituting of data from Sentinel-1 and Sentinel-2, and socioeconomic data, constituting of points-of-interest and spatial metrics from road networks to classify urban land-use at a national scale, using a deep learning approach. A related challenge for large-scale mapping is the availability of ground truth data. Therefore, we focus our analysis on the transferability of our classification approach, using ground truth labels from a nationwide land-use dataset for the Netherlands. By dividing the country into four regions, we tested whether a combination of satellite data and socioeconomic data increases the transferability of the classification approach, compared to using satellite data only. The results indicate that socioeconomic data increases the overall accuracy of the classification for the Netherlands by 3 percentage points. In a transfer learning approach we find that adding socioeconomic data increases the accuracy between 3 and 5 percentage points when trained on three regions and tested on the independent fourth one. In the case of training and testing on one region and testing on another, the increase in overall accuracy increased up to 9 percentage points. In addition, we find that our deep learning approach consistently outperforms a random forest model, used here as benchmark, in all of the abovementioned experiments. Overall, we find that socioeconomic data increases the accuracy of urban land use classification, but variations between experiments are large.", "Keywords": "Deep learning ; Model transferability ; Sentinel ; SAR ; Point-of-interest ; Road networks", "DOI": "10.1016/j.rse.2022.113076", "PubYear": 2022, "Volume": "278", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON>", "Affiliation": "Institute for Environmental Studies, Vrije Universiteit Amsterdam, De Boelelaan 1105, Amsterdam, the Netherlands;Corresponding author at: De Boelelaan 1087, 1081 HV Amsterdam, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "German Aerospace Center (DLR), German Remote Sensing Data Center (DFD), Oberpfaffenhofen 82234, Wessling, Germany;Institute for Geography and Geology, Julius-Maximilians-Universität Würzburg, Würzburg 97074, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for Environmental Studies, Vrije Universiteit Amsterdam, De Boelelaan 1105, Amsterdam, the Netherlands"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute for Environmental Studies, Vrije Universiteit Amsterdam, De Boelelaan 1105, Amsterdam, the Netherlands"}], "References": []}, {"ArticleId": 94522012, "Title": "Reversing Kia Motors Head Unit to discover and exploit software vulnerabilities", "Abstract": "<p> Modern vehicles resemble four-wheels computers connected to the Internet via their In-Vehicle Infotainment (IVI) systems. As with PCs in the past, cars, being connected to the Internet can be potentially vulnerable. The IVI system of a car is part of the intra-vehicle network and can be the entry-point of offensive cybersecurity attacks. The intra-vehicle network, based on the CAN protocol, is vulnerable by design : messages are exchanged in clear. Thus, the uncontrolled access to the CAN bus may have serious impact on the vehicle itself and its passengers. In this paper, we present a vulnerability assessment, through a reverse engineering process, of Kia vehicles IVI system. In particular, we focused on reverse engineer the Kia IVI system to discover vulnerabilities that may allow an attacker to compromise the IVI functionalities and inject CAN frames into the CAN bus to alter the behaviour of (part of) the vehicle. By reverse engineering the IVI, we identified four important vulnerabilities that affect all Kia vehicles that embed the studied IVI. Finally, we show how an attacker can easily control the IVI and inject CAN bus frames by means of a Metasploit module that we wrote. </p>", "Keywords": "Automotive; Vulnerability Assessment; Reverse Engineering; IVI Exploit", "DOI": "10.1007/s11416-022-00430-5", "PubYear": 2023, "Volume": "19", "Issue": "1", "JournalId": 3946, "JournalTitle": "Journal of Computer Virology and Hacking Techniques", "ISSN": "", "EISSN": "2263-8733", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Istituto di Informatica e Telematica, CNR Pisa, Pisa, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Istituto di Informatica e Telematica, CNR Pisa, Pisa, Italy"}], "References": []}, {"ArticleId": 94522059, "Title": "Complete coverage path planning and performance factor analysis for autonomous bulldozer", "Abstract": "<p>With the development of intelligent machinery, path planning of autonomous bulldozers plays an important role to solve the problems of efficiency in future construction sites. How to plan the path that meets the construction requirements is the key problem. This study investigates the standardized construction workmanship and moving rules of the bulldozer to add rules and path selection strategies to the algorithm. This study establishes the path planning model of bulldozer's leveling, considering the limitations of the traditional bioinspired neural network algorithm, an improved complete coverage path planning method is proposed. An integrated framework is proposed for an automatic bulldozer with a sensor system for a field experiment to verify the feasibility of the method. According to the comparative statistical simulation results, the effectiveness of the method is verified, and the effects of different factors on the path planning results are analyzed.</p>", "Keywords": "autonomous bulldozer;BINN hybrid A* algorithm;complete coverage path planning;earthwork construction;performance factors analysis;sensor system;site layout", "DOI": "10.1002/rob.22085", "PubYear": 2022, "Volume": "39", "Issue": "7", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Center of Technology Innovation for Digital Construction Huazhong University of Science & Technology Wuhan Hubei China;Department of Construction Management, School of Civil and Hydraulic Engineering Huazhong University of Science & Technology Wuhan Hubei China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Center of Technology Innovation for Digital Construction Huazhong University of Science & Technology Wuhan Hubei China;Department of Construction Management, School of Civil and Hydraulic Engineering Huazhong University of Science & Technology Wuhan Hubei China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Weichai Power Co., Ltd Weifang Shandong China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shantui Construction Machinery Co.,Ltd Jining Shandong China"}], "References": [{"Title": "Toward energy-efficient online Complete Coverage Path Planning of a ship hull maintenance robot based on Glasius Bio-inspired Neural Network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115940", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94522061, "Title": "RETRACTED ARTICLE: Query-oriented topical influential users detection for top-k trending topics in twitter", "Abstract": "<p>Online Social Networks (OSNs) have become inevitable for any new methodology both for viral promoting applications and instructing the creation of inciting information and data. As a result, finding influential users in OSNs is one of the most studied research problems. Existing research works paid less attention to the temporal factors associated with the activities performed by the social users. Our motivation is to find influential users who show their most powerful interests towards a given query on various subjects (topics) at different time intervals by featuring more on users’ most recent activities as well as their associations with different users. To address this problem, we propose a temporal activity-biased weight model that gives higher weight to users’ recent activities and develops an algorithm to list the most effective influential users. In addition, our proposed model also considers the impacts of topical similarities both from direct and indirect neighbors of the users. Experimental results on two real datasets demonstrate that our proposed framework yields better outcomes than the baseline method.</p>", "Keywords": "Online social network; Trending topic; Common neighbors; Influential User", "DOI": "10.1007/s10489-022-03582-5", "PubYear": 2022, "Volume": "52", "Issue": "12", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jahangirnagar University, Dhaka, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jahangirnagar University, Dhaka, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jahangirnagar University, Dhaka, Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Chittagong University of Engineering and Technology, Chittagong, Bangladesh"}], "References": [{"Title": "Efficient algorithms based on centrality measures for identification of top-K influential users in social networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "527", "Issue": "", "Page": "88", "JournalTitle": "Information Sciences"}, {"Title": "Social Network Influencer Rank Recommender Using Diverse Features from Topical Graph", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "1861", "JournalTitle": "Procedia Computer Science"}, {"Title": "Time-aware domain-based social influence prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "A survey of Twitter research: Data model, graph structure, sentiment analysis and attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "114006", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Social Media and Stock Market Prediction: A Big Data Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "2", "Page": "2569", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "IM-ELPR: Influence maximization in social networks using label propagation based community structure", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "11", "Page": "7647", "JournalTitle": "Applied Intelligence"}, {"Title": "Identification of top-k influential nodes based on discrete crow search algorithm optimization for influence maximization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "11", "Page": "7749", "JournalTitle": "Applied Intelligence"}, {"Title": "Deep collaborative filtering with social promoter score-based user-item interaction: a new perspective in recommendation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "11", "Page": "7855", "JournalTitle": "Applied Intelligence"}, {"Title": "A community detection algorithm based on Quasi-Laplacian centrality peaks clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "11", "Page": "7917", "JournalTitle": "Applied Intelligence"}, {"Title": "Multi-topical authority sensitive influence maximization with authority based graph pruning and three-stage heuristic optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "11", "Page": "8432", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 94522074, "Title": "Short-Term Electricity Price Forecasting Using Random Forest Model with Parameters Tuned by Grey Wolf Algorithm Optimization", "Abstract": "Accurately forecasting short-term electricity prices is of great significance to electricity market participants. Compared with the time series forecasting methods, machine learning forecasting methods can consider more external factors. The forecasting accuracy of machine learning models is greatly affected by the parameters, meanwhile, the manual selection of parameters usually cannot guarantee the accuracy and stability of the forecasting. Therefore, this paper proposes a random forest (RF) electricity price forecasting model based on the grey wolf optimizer (GWO) to improve the accuracy of forecasting. Among them, RF has a good ability to deal with the problem of non-linear and unstable electricity prices. The optimization of model parameters by GWO can overcome the instability of the forecasting accuracy of manually tune parameters. On this basis, the short-term electricity prices of the PJM power market in four seasons are separately predicted. Experimental results show that the RF algorithm can better predict the short-term electricity price, and the optimization of the RF forecasting model by GWO can effectively improve the accuracy of the RF forecasting model.", "Keywords": "short-term electricity price forecasting;random forest;grey wolf optimizer;electricity market", "DOI": "10.21078/JSSI-2022-167-14", "PubYear": 2022, "Volume": "10", "Issue": "2", "JournalId": 42617, "JournalTitle": "Journal of Systems Science and Information", "ISSN": "1478-9906", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Junshuang ZHANG", "Affiliation": "State Key Laboratory of Electrical Insulation and Power Equipment, Xi’an Jiaotong University, Xi’an, 710049, China; State Grid East Inner Mongolia Electric Power Co., Ltd., Hohhot, 010010, China"}, {"AuthorId": 2, "Name": "Ziqiang LEI", "Affiliation": "Economics and Management School, North China Electric Power University, Beijing, 102206, China"}, {"AuthorId": 3, "Name": "Runkun CHENG", "Affiliation": "Economics and Management School, North China Electric Power University, Beijing, 102206, China"}, {"AuthorId": 4, "Name": "Huiping ZHANG", "Affiliation": ""}], "References": []}, {"ArticleId": 94522136, "Title": "Grad<PERSON> Terrain Authoring", "Abstract": "Digital terrains are a foundational element in the computer‐generated depiction of natural scenes. Given the variety and complexity of real‐world landforms, there is a need for authoring solutions that achieve perceptually realistic outcomes without sacrificing artistic control. In this paper, we propose setting aside the elevation domain in favour of modelling in the gradient domain. Such a slope‐based representation is height independent and allows a seamless blending of disparate landforms from procedural, simulation, and real‐world sources. For output, an elevation model can always be recovered using Poisson reconstruction, which can include Dirichlet conditions to constrain the elevation of points and curves.In terms of authoring our approach has numerous benefits. It provides artists with a complete toolbox, including: cut‐and‐paste operations that support warping as needed to fit the destination terrain, brushes to modify region characteristics, and sketching to provide point and curve constraints on both elevation and gradient. It is also a unifying representation that enables the inclusion of tools from the spectrum of existing procedural and simulation methods, such as painting localised high‐frequency noise or hydraulic erosion, without breaking the formalism. Finally, our constrained reconstruction is GPU optimized and executes in real‐time, which promotes productive cycles of iterative authoring.", "Keywords": "", "DOI": "10.1111/cgf.14460", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Univ Lyon, INSA Lyon, CNRS, UCBL, LIRIS, UMR5205  F‐69621 Villeurbanne France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Univ Lyon, INSA Lyon, CNRS, UCBL, LIRIS, UMR5205  F‐69621 Villeurbanne France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Univ Lyon, UCBL, CNRS UMR 5208, Institut Camille Jordan  F‐69622 Villeurbanne France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Univ Lyon, INSA Lyon, CNRS, UCBL, LIRIS, UMR5205  F‐69621 Villeurbanne France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Université de Strasbourg, CNRS, ICube UMR 7357  France"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Cape Town"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Univ Lyon, INSA Lyon, CNRS, UCBL, LIRIS, UMR5205  F‐69621 Villeurbanne France"}], "References": []}, {"ArticleId": 94522296, "Title": "OT Security Requirements for Ethernet-APL field devices", "Abstract": "<p>Network convergence is an increasing trend in the automation domain. More and more plant owners strive for a unification ofnetworks in their plants. This yields a seamless network structure, simplified supervision, and reduced training effort for the personnel, as only one unified network technology needs to be handled. Ethernet-APL is one piece of the puzzle for such a converged network, supporting various real time protocols like PROFINET, EtherNet, HART-IP as well as the middleware protocolOPC UA. This paper gives an overview on the impact of Ethernet-APL field devices to OT security and proposes how to ensure OTsecurity for them.</p>", "Keywords": "Ethernet-APL;IT Security;Field devices", "DOI": "10.17560/atp.v63i5.2594", "PubYear": 2022, "Volume": "63", "Issue": "5", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Endress+Hauser Digital Solutions"}], "References": []}, {"ArticleId": 94522312, "Title": "Integration einer Power-to-Gas-Anlage", "Abstract": "<p>Im nachstehenden Beitrag wird untersucht, inwiefern der Einsatz zusätzlicher Speicherelemente die Einbindung einer Power-to-Gas-Anlage in ein bestehendes Energiesystem effizienter gestalten kann. Hierzu wurde im Rahmen einer Simulation die Power-to-Gas-Anlage um drei verschiedene Speicherelemente zunächst einzeln und später kombiniert ergänzt. Die Kapazität der einzelnen Speicherelemente wurde variiert und der Einfluss dieser auf die Parameter Ressourcennutzung, Auslastung und Wirtschaftlichkeit betrachtet.</p>", "Keywords": "Power-to-Gas;Dekarbonisierung;Energiespeicher", "DOI": "10.17560/atp.v63i5.2572", "PubYear": 2022, "Volume": "63", "Issue": "5", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hochschule für Technik und Wirtschaft des Saarlandes"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hochschule für Technik und Wirtschaft des Saarlandes"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hochschule für Technik und Wirtschaft des Saarlandes"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universität des Saarlandes"}], "References": []}, {"ArticleId": 94522314, "Title": "Imputation of missing time-activity data with long-term gaps: A multi-scale residual CNN-LSTM network model", "Abstract": "Despite the increasing availability and spatial granularity of individuals&#x27; time-activity (TA) data, the missing data problem, particularly long-term gaps, remains as a major limitation of TA data as a primary source of human mobility studies. In the present study, we propose a two-step imputation method to address the missing TA data with long-term gaps, based on both efficient representation of TA patterns and high regularity in TA data. The method consists of two steps: (1) the continuous bag-of-words word2vec model to convert daily TA sequences into a low-dimensional numerical representation to reduce complexity; (2) a multi-scale residual Convolutional Neural Network (CNN)-stacked Long Short-Term Memory (LSTM) model to capture multi-scale temporal dependencies across historical observations and to predict the missing TAs. We evaluated the performance of the proposed imputation method using the mobile phone-based TA data collected from 180 individuals in western New York, USA, from October 2016 to May 2017, with a 10-fold out-of-sample cross-validation method. We found that the proposed imputation method achieved excellent performance with 84% prediction accuracy, which led us to conclude that the proposed imputation method was successful at reconstructing the sequence, duration, and spatial extent of activities from incomplete TA data. We believe that the proposed imputation method can be applied to impute incomplete TA data with relatively long-term gaps with high accuracy.", "Keywords": "Missing data ; Long-term gaps in time-activity data ; Imputation ; Embedding ; Multi-scale residual CNN-stacked LSTM", "DOI": "10.1016/j.compenvurbsys.2022.101823", "PubYear": 2022, "Volume": "95", "Issue": "", "JournalId": 1688, "JournalTitle": "Computers, Environment and Urban Systems", "ISSN": "0198-9715", "EISSN": "1873-7587", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography, University at Buffalo, State University of New York, Buffalo, NY, USA;Corresponding author at: 408 <PERSON><PERSON>, Department of Geography, University at Buffalo, State University of New York, Buffalo, NY 14261, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography, University at Buffalo, State University of New York, Buffalo, NY, USA"}], "References": []}, {"ArticleId": 94522334, "Title": "The 3D Motorcycle Complex for Structured Volume Decomposition", "Abstract": "<p>The so-called motorcycle graph has been employed in recent years for various purposes in the context of structured and aligned block decomposition of 2D shapes and 2-manifold surfaces. Applications are in the fields of surface parametrization, spline space construction, semi-structured quad mesh generation, or geometry data compression. We describe a generalization of this motorcycle graph concept to the three-dimensional volumetric setting. Through careful extensions aware of topological intricacies of this higher-dimensional setting, we are able to guarantee important block decomposition properties also in this case. We describe algorithms for the construction of this 3D motorcycle complex on the basis of either hexahedral meshes or seamless volumetric parametrizations. Its utility is illustrated on examples in hexahedral mesh generation and volumetric T-spline construction.</p>", "Keywords": "block-structured;multi-block;T-mesh;hexahedral mesh;volume mesh;block decomposition;base complex;CCS Concepts;• Computing methodologies → Computer graphics;Mesh models;Mesh geometry models;Shape modeling", "DOI": "10.1111/cgf.14470", "PubYear": 2022, "Volume": "41", "Issue": "2", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Osnabrück University, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Osnabrück University, Germany"}, {"AuthorId": 3, "Name": "Man<PERSON>", "Affiliation": "Osnabrück University, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Osnabrück University, Germany"}], "References": [{"Title": "Seamless Parametrization with Arbitrary Cones for Arbitrary Genus", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Algebraic Representations for Volumetric Frame Fields", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Efficient piecewise higher-order parametrization of discrete surfaces with local and global injectivity", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "127", "Issue": "", "Page": "102862", "JournalTitle": "Computer-Aided Design"}, {"Title": "Octahedral Frames for Feature-Aligned Cross Fields", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Combinatorial Construction of Seamless Parameter Domains", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "2", "Page": "179", "JournalTitle": "Computer Graphics Forum"}, {"Title": "LoopyCuts", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Practical Fabrication of Discrete Chebyshev Nets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "7", "Page": "13", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Quad Layouts via Constrained T‐Mesh Quantization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2", "Page": "305", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Simpler Quad Layouts using Relaxed Singularities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "5", "Page": "169", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Efficient and robust discrete conformal equivalence with boundary", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 94522367, "Title": "Shot classification and replay detection for sports video summarization", "Abstract": "<p>Automated analysis of sports video summarization is challenging due to variations in cameras, replay speed, illumination conditions, editing effects, game structure, genre, etc. To address these challenges, we propose an effective video summarization framework based on shot classification and replay detection for field sports videos. Accurate shot classification is mandatory to better structure the input video for further processing, i.e., key events or replay detection. Therefore, we present a lightweight convolutional neural network based method for shot classification. Then we analyze each shot for replay detection and specifically detect the successive batch of logo transition frames that identify the replay segments from the sports videos. For this purpose, we propose local octa-pattern features to represent video frames and train the extreme learning machine for classification as replay or non-replay frames. The proposed framework is robust to variations in cameras, replay speed, shot speed, illumination conditions, game structure, sports genre, broadcasters, logo designs and placement, frame transitions, and editing effects. The performance of our framework is evaluated on a dataset containing diverse YouTube sports videos of soccer, baseball, and cricket. Experimental results demonstrate that the proposed framework can reliably be used for shot classification and replay detection to summarize field sports videos.</p>", "Keywords": "Extreme learning machine; Lightweight convolutional neural network; Local octa-patterns; Shot classification; Replay detection; Video summarization; 极限学习机; 轻量级卷积神经网络; 局部八元模式; 镜头分类; 回放检测; 视频摘要; TP391", "DOI": "10.1631/FITEE.2000414", "PubYear": 2022, "Volume": "23", "Issue": "5", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering, University of Engineering and Technology, Taxila, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Engineering, University of Engineering and Technology, Taxila, Pakistan"}], "References": [{"Title": "A decision tree framework for shot classification of field sports videos", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "9", "Page": "7242", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 94522368, "Title": "基于成本收益分析的集成测试序列生成优化方法", "Abstract": "<p>Integration testing is an integral part of software testing. Prior studies have focused on reducing test cost in integration test order generation. However, there are no studies concerning the testing priorities of critical classes when generating integration test orders. Such priorities greatly affect testing efficiency. In this study, we propose an effective strategy that considers both test cost and efficiency when generating test orders. According to a series of dynamic execution scenarios, the software is mapped into a multi-layer dynamic execution network (MDEN) model. By analyzing the dynamic structural complexity, an evaluation scheme is proposed to quantify the class testing priority with the defined class risk index. Cost—benefit analysis is used to perform cycle-breaking operations, satisfying two principles: assigning higher priorities to higher-risk classes and minimizing the total complexity of test stubs. We also present a strategy to evaluate the effectiveness of integration test order algorithms by calculating the reduction of software risk during their testing process. Experiment results show that our approach performs better across software of different scales, in comparison with the existing algorithms that aim only to minimize test cost. Finally, we implement a tool, ITOsolution, to help practitioners automatically generate test orders.</p>", "Keywords": "Integration test order; Cost—benefit analysis; Probabilistic risk analysis; Complex network; 集成测试序列; 成本收益分析; 概率风险分析; 复杂网络; TP311", "DOI": "10.1631/FITEE.2100466", "PubYear": 2022, "Volume": "23", "Issue": "5", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Software College, Northeastern University, Shenyang, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Software College, Northeastern University, Shenyang, China; State Key Lab for Novel Software Technology, Nanjing University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Software College, Northeastern University, Shenyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Software College, Northeastern University, Shenyang, China"}], "References": []}, {"ArticleId": 94522537, "Title": "Online Support for Education, Medication, Agriculture and Relief Work at COVID-19 Pandemic Time", "Abstract": "<p>The future of Bangladesh belongs to the younger generation. All of us have to do our bit to get them involved in improving the lives of the rural poor. If we do not do so, there will be serious social conflicts. Unless we can provide basic amenities so that the rural poor can live a meaningful life, we will never become a great nation. This is a great challenge for all youngsters and it is our work’s dream that they will take it up so as to make Bangladesh a better place to live and work. This work describes an organization named “Neuron Group” which contains four fields: Edu-Care (Neuron Educational Support Center), Medi-Care (Neuron Medical Support Center), Agri-Care (Neuron Agricultural Support Center) and Relief-Care (Neuron Relief Support Center). Students will have the opportunity to study with the most talented doctors and engineers of the country. The contribution of this work is to keep the students in their studies at pandemic time (covid-19 period) with the help of our website and social media such as Facebook, twitter and Instagram. Other contributions of this work are social awareness, to develop rural areas, create employment, help increase the income of poor people, raise awareness among poor people, bringing poor people into the mainstream of society, assistance in agriculture and financial assistance of poor and marginal farmers. In the district where the \"Neuron Group will have a branch\", a list of rational poor and destitute based on the wards of each Upazila of the district will be prepared and they will be given food aid from time to time.</p>", "Keywords": "COVID-19;Pandemic;Neuron group;edu-care;medi-care;agri-care;relief-care development;poverty;refugee", "DOI": "10.9734/ajrcos/2022/v13i430322", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 63540, "JournalTitle": "Asian Journal of Research in Computer Science", "ISSN": "", "EISSN": "2581-8260", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Noakhali Science and Technology University, Noakhali, Chattogram, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Noakhali Science and Technology University, Noakhali, Chattogram, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Prime University, Dhaka, Bangladesh"}], "References": []}]