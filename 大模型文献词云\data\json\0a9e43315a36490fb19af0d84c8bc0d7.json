[{"ArticleId": 105139637, "Title": "The influence of online retail/service brand equity and effect of country of origin on e-marketplace patronage intention", "Abstract": "E-marketplaces are estimated as the primary online shopping channel in Thailand; however, the local e-marketplaces cannot compete with foreign competitors. Besides, Thai online shoppers prefer to shop from cross-border websites. Brand equity can strengthen traditional retailers' patronage intention. However, the brand equity concept, which is particular for online retailers, may be more suitable for strengthening the e-marketplaces of the country. Also, studies on the effect of country of origin (COO) on online retailers in terms of foreignness are limited. Therefore, the study aims to investigate the e-marketplace patronage intention of Thai online shoppers by utilising the extended theory of reasoned action (TRA), with online retail/ service (ORS) brand equity as an independent variable and the effect of COO as a moderator variable. The findings reveal that all of the independent variables are positively significant with e-marketplace patronage intention except the effect of COO. The relationship between attitude towards behaviour and the e-marketplace patronage intention is negatively moderated by the effect of COO. Copyright © 2023 Inderscience Enterprises Ltd.", "Keywords": "attitude towards behaviour; brand equity; COO; country of origin; e-marketplace; electronic marketplace; online retail/service; ORS; patronage intention; subjective norm; theory of reasoned action; TRA", "DOI": "10.1504/IJEB.2023.127524", "PubYear": 2023, "Volume": "18", "Issue": "1", "JournalId": 8392, "JournalTitle": "International Journal of Electronic Business", "ISSN": "1470-6067", "EISSN": "1741-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Business Management, College of Business, University Utara, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Business Management, College of Business, University Utara, Malaysia"}, {"AuthorId": 3, "Name": "Normalisa <PERSON>", "Affiliation": "School of Business Management, College of Business, University Utara, Malaysia"}], "References": []}, {"ArticleId": 105139744, "Title": "Graph representation learning based on deep generative gaussian mixture models", "Abstract": "Graph representation learning is an effective tool for facilitating graph analysis with machine learning methods. Most GNNs, including Graph Convolutional Networks (GCN), Graph Recurrent Neural Networks (GRNN), and Graph Auto-Encoders (GAE), employ vectors to represent nodes in a deterministic way without exploiting the uncertainty in hidden variables. Deep generative models are combined with GAE in the Variational Graph Auto-Encoder (VGAE) framework to address this issue. While traditional VGAE-based methods can capture hidden and hierarchical dependencies in latent spaces, they are limited by the data’s multimodality. Here, we propose the Gaussian Mixture Model (GMM) to model the prior distribution in VGAE. Furthermore, an adversarial regularization is incorporated into the proposed approach to ensure the fruitful impact of the latent representations on the results. We demonstrate the performance of the proposed method on clustering and link prediction tasks. Our experimental results on real datasets show remarkable performance compared to state-of-the-art methods.", "Keywords": "", "DOI": "10.1016/j.neucom.2022.11.087", "PubYear": 2023, "Volume": "523", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of New Sciences and Technologies, University of Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering Science, University of Oxford, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of New Sciences and Technologies, University of Tehran, Iran;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Engineering Science, University of Oxford, UK;Oxford-Suzhou Centre for Advanced Research (OSCAR), Suzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication Technology, Griffith University, Australia"}], "References": [{"Title": "Deep learning approach on information diffusion in heterogeneous networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "189", "Issue": "", "Page": "105153", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An analysis on the use of autoencoders for representation learning: Fundamentals, learning task case studies, explainability and challenges", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "404", "Issue": "", "Page": "93", "JournalTitle": "Neurocomputing"}, {"Title": "GraphAIR: Graph representation learning with neighborhood aggregation and interaction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107745", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep node clustering based on mutual information maximization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "455", "Issue": "", "Page": "274", "JournalTitle": "Neurocomputing"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}]}, {"ArticleId": 105139752, "Title": "Reduction of data-value-aware process models: A relevance-based approach", "Abstract": "In many settings, verification of process models hinges on data values . A data value is a value in the domain of a data object, e.   g., $ 1000 as the price of a product. Naive exploration of process models with data values, i.   e., data-value-aware process models , tends to lead to state-space explosion. To tackle this problem, various reduction techniques have been proposed. These current techniques have a fundamental shortcoming: They do not take the modification of data values by process elements into consideration, in spite of its effects on execution behavior of processes. This may affect the verification result. In this paper, we propose a novel property-specific reduction technique to reduce data-value-aware process models supporting modification of data values. We detect the control-flow nodes and data elements that are necessary to verify a property in question, i.   e., elements that are relevant for verification. Then we reduce the data-value-aware process model accordingly. We evaluate our approach by verifying a real-world application: the German 4G spectrum auction model.", "Keywords": "Workflow management ; Data-value-aware process ; Data-value modification ; Property verification ; State-space reduction", "DOI": "10.1016/j.is.2022.102157", "PubYear": 2023, "Volume": "114", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, KIT Institute for Program Structures and Data Organization, 76131 Karlsruhe, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, KIT Institute for Program Structures and Data Organization, 76131 Karlsruhe, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, KIT Institute for Program Structures and Data Organization, 76131 Karlsruhe, Germany"}], "References": []}, {"ArticleId": 105139755, "Title": "Multi-objective evolutionary algorithms are generally good: Maximizing monotone submodular functions over sequences", "Abstract": "Evolutionary algorithms (EAs) are general-purpose optimization algorithms, inspired by natural evolution. Recent theoretical studies have shown that EAs can achieve good approximation guarantees for solving the problem classes of submodular optimization, which have a wide range of applications, such as maximum coverage, sparse regression, influence maximization, document summarization and sensor placement, just to name a few. Though they have provided some theoretical explanation for the general-purpose nature of EAs, the considered submodular objective functions are defined only over sets or multisets. To complement this line of research, this paper studies the problem class of maximizing monotone submodular functions over sequences, where the objective function depends on the order of items. We prove that for each kind of previously studied monotone submodular objective functions over sequences, i.e., prefix monotone submodular functions, weakly monotone and strongly submodular functions, and DAG monotone submodular functions, a simple multi-objective EA, i.e., GSEMO, can always reach or improve the best known approximation guarantee after running polynomial time in expectation. Note that these best-known approximation guarantees can be obtained only by different greedy-style algorithms before. Empirical studies on various applications, e.g., accomplishing tasks, maximizing information gain, search-and-tracking and recommender systems, show the excellent performance of the GSEMO.", "Keywords": "Evolutionary algorithms ; Multi-objective evolutionary algorithms ; Submodular optimization ; Sequences ; Computational complexity ; Approximation ratio ; Experimental studies", "DOI": "10.1016/j.tcs.2022.12.011", "PubYear": 2023, "Volume": "943", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, Nanjing 210023, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, Nanjing 210023, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, University of Science and Technology of China, Hefei 230027, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shenzhen Key Laboratory of Computational Intelligence, Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China;Corresponding author"}], "References": [{"Title": "Multiobjective Evolutionary Algorithms Are Still Good: Maximizing Monotone Approximately Submodular Minus Modular Functions", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "4", "Page": "463", "JournalTitle": "Evolutionary Computation"}, {"Title": "Result diversification by multi-objective evolutionary algorithms with theoretical guarantees", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "309", "Issue": "", "Page": "103737", "JournalTitle": "Artificial Intelligence"}, {"Title": "A primal-dual approximation algorithm for the k-prize-collecting minimum vertex cover problem with submodular penalties", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "3", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}]}, {"ArticleId": 105139772, "Title": "A difunctional electrochemiluminescence sensor based on Ru-MOFs and strand-displacement-amplification reaction for ultrasensitive detection of Hg2+ and Ag+", "Abstract": "A label free difunctional electrochemiluminescence (ECL) sensor for the ultrasensitive detection of mercury ions (Hg<sup>2+</sup>) and silver ions (Ag<sup>+</sup>) was constructed based on Ru(bpy)<sub>3</sub><sup>2+</sup>-functionalized metal-organic frameworks (Ru-MOFs) and the strand-displacement-amplification (SDA) reaction. Electrodeposited Ru-MOFs not only overcome the disadvantage of poor stability of traditional Ru<sup>2+</sup> in aqueous solution, but also serve as an excellent carrier for hairpin DNA H1 during sensor construction. The SDA technology can transform extremely low concentrations of Hg<sup>2+</sup> and Ag<sup>+</sup> into a large number of the same alternative target DNA (tDNA). By combining SDA with Ru-MOFs, the detection sensitivity of the sensor is further improved. Meanwhile, the generated same alternative target tDNA can realize the detection of Hg<sup>2+</sup> and Ag<sup>+</sup> ions on this same sensing platform, which is beneficial to solve the common problems of high cost, long time and complicated operation in the detection of metal ion. The detection limits for Hg<sup>2+</sup> and Ag<sup>+</sup> are as low as 0.00032 pM and 0.00298 pM, respectively. And the corresponding linear detection range (LDR) is 0.001–1000 pM and 0.01–10000 pM, respectively. This ECL sensor has also been successfully used in the determination of Hg<sup>2+</sup> and Ag<sup>+</sup> in seawater, indicating an ideal platform for the simultaneous detection of trace heavy metal ions.", "Keywords": "Electrochemiluminescence ; Ru-MOFs ; Strand displacement amplification (SDA) ; Mercury ions ; Silver ions", "DOI": "10.1016/j.snb.2022.133141", "PubYear": 2023, "Volume": "378", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-Electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-Electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-Electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-Electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-Electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-Electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao 266042, PR China;Corresponding author"}], "References": []}, {"ArticleId": 105139776, "Title": "Modeling and predicting user preferences with multiple item attributes for sequential recommendations", "Abstract": "Sequential recommendations have become a focus of attention across the deep learning community owing to their fitness to the actual application scenario. Although recently we have witnessed a surge of work on sequential recommender systems, they are still insufficient in exploring and exploiting item-attribute relations to enhance prediction accuracy. In this work, we propose a novel technological framework, MIA-SR, for sequential recommendation (SR) by modeling and predicting user preferences with multiple item attributes (MIA). When modeling the dynamic behavior of a user, not only the item sequence but also the attribute sequence is used to generate the fused representation of users. Further, we propose using a graph convolution network on the item-attribute bipartite graph to enhance the representations of items and attribute entities. Moreover, MIA-SR is naturally empowered with a multi-tasking strategy to exploit inductive bias among different preference signals and enhance item recommendation. Extensive experiments on public benchmark datasets have verified the merits of MIA-SR. The source code and data are available at: https://github.com/619496775/MIA-SR .", "Keywords": "", "DOI": "10.1016/j.knosys.2022.110174", "PubYear": 2023, "Volume": "260", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Yunnan University, Kunming 650091, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Yunnan University, Kunming 650091, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Yunnan University, Kunming 650091, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Yunnan University, Kunming 650091, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Yunnan University, Kunming 650091, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Yunnan University, Kunming 650091, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Automation Engineering, Nanjing Normal University, Nanjing 210024, China;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan 430072, China"}], "References": [{"Title": "GFE: General Knowledge Enhanced Framework for Explainable Sequential Recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "230", "Issue": "", "Page": "107375", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "FairSR: Fairness-aware Sequential Recommendation through Multi-Task Learning with Preference Graph Embeddings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Gating augmented capsule network for sequential recommendation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "247", "Issue": "", "Page": "108817", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "CGSNet: Contrastive Graph Self-Attention Network for Session-based Recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "251", "Issue": "", "Page": "109282", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 105139801, "Title": "Cover Image, Volume 30, Issue 12", "Abstract": "<p>The cover image is based on the Research Article In-line mura detection using machine learning and subspace method in display manufacturing by <PERSON><PERSON> et al., https://doi.org/10.1002/jsid.1180 </p> <picture> </picture> <p ></p> <p></p>", "Keywords": "", "DOI": "10.1002/jsid.1189", "PubYear": 2022, "Volume": "30", "Issue": "12", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105139839, "Title": "An evaluation of scintillation index in atmospheric turbulent for new super Lorentz vortex Gaussian beam", "Abstract": "Super Lorentz vortex Gaussian beam (SLVGB) is propagated via the turbulent atmosphere parameters. The benefit key of the SLVGB wave model is that the unlimited bandwidth wave and a spherical wave are involved. Additionally, <PERSON><PERSON><PERSON> integral was used for schoolwork to study the propagation of SLVGB in a slant direction via a moderate turbulent medium. On the other hand, applying the crude international telecommunication union (ITU-R) model possible. Moreover, the Kolmogorov turbulent power spectrum model is applied, and the source field is dispersed by the zenith angle to the receiver plane. Additionally, examine the contour of the source field and the SLVGB intensity. To investigate various parameters such as source size, mode, scintillation index, topological charge, and others that are associated with the beam of super Lorentz vortex Gaussian are entirely understood, the outcomes were examined, and obtained other references to build the beam of slant path propagation in turbulent; the form constants are especially in comparison and matching. Our graphical findings indicate that the parameters happened randomly in the scintillation index and intensity of the SLVGB, resulting in a novel beam technical configuration. To summarize, this article is advantageous for remote sensing and uses an optical communications system and laser applications.", "Keywords": "contour;ITU-R model;kolmogorov;scintillation;SLVGB; topological charge;turbulent", "DOI": "10.12928/telkomnika.v21i1.22221", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Engineering, Al-Nahrain University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Information Technology and Communications"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Information Technology and Communications"}], "References": []}, {"ArticleId": 105139843, "Title": "Performance evaluation of software-defined networking controllers in wired and wireless networks", "Abstract": "Traditional networking solutions are unable to meet modern computing needs due to the expanding popularity of the internet, which requires increased agility and flexibility. To meet these objectives, software-defined networking (SDN) arises. A controller is a major element that will determine if SDN succeeds or fails. Various current SDN controllers in many sectors must be evaluated and compared. The performance of two well-known SDN controllers, POX and Ryu, is evaluated in this research. We used the Mininet-WiFi emulator to implement our work and the distributed internet traffic generator (D-ITG) to assess the aforementioned controllers using delay, jitter, packet loss, and throughput metrics. What is new in our research is the study of network performance in two different types of transmission media: wired and wireless. The speed of the wired medium was chosen to be fast ethernet, which was not previously studied. In addition, the size of the packet was varied among 128, 256, 512, and 1,024 bytes. The comparison was performed on three topologies (single, linear, and tree). The experimental results showed that Ryu offers significantly lower latency, jitter, and packet loss than POX in most scenarios. Also, the Ryu controller has higher throughput than POX, especially on wireless networks.", "Keywords": "bitrate;delay;jitter;mininet-WiFi;POX;Ryu;SDN", "DOI": "10.12928/telkomnika.v21i1.23468", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Northern Technical University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mosul University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Northern Technical University"}], "References": []}, {"ArticleId": 105139848, "Title": "Big data classification based on improved parallel k-nearest neighbor", "Abstract": "In response to the rapid growth of many sorts of information, highway data has continued to evolve in the direction of big data in terms of scale, type, and structure, exhibiting characteristics of multi-source heterogeneous data. The k-nearest neighbor (KNN) join has received a lot of interest in recent years due to its wide range of applications. Processing KNN joins is time-consuming and inefficient due to the quadratic structure of the join method . As the number of applications dealing with vast amounts of data develops, KNN joins get more sophisticated. The authors seek to save money on computer resources by leveraging a large number of threads and multiprocessors. Six popular datasets are used to apply the method and evaluate the sequential and parallel performance of the KNN technique. These datasets are used to compare the sequential and parallel performance of the KNN method. When compared to a matching multi-core solution, the final implementation saves computing resources. It has been optimized to utilize as little RAM as possible, allowing it to manage high-resolution photo data without sacrificing efficiency. The authors will use the technique they presented using Spark Radoop. Our performance research validates the supplied method’s efficacy and scalability.", "Keywords": "big data;k-nearest neighbor;machine learning;parallel processing;Radoop;Spark", "DOI": "10.12928/telkomnika.v21i1.24290", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "AL-Iraqia University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> University College"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Al-Hweija Technical Institute/Noerthern Technical University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> University College"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Tikrit University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Ahmad <PERSON>"}], "References": []}, {"ArticleId": 105139904, "Title": "A full-potential and multiscale computational scheme for interactions between ultrafast intense laser pulses and condensed medium", "Abstract": "We have presented a multiscale computational scheme for the simulation of interactions between ultrafast intense laser pulses and condensed medium. This approach solves the coupled time-dependent Kohn-<PERSON>ham (TDKS) and Maxwell equations in real time and provides a unified description of microscopic electron dynamics and macroscopic light propagation from first principles and applicable to wide ultrafast phenomena. Especially, as the TDKS equation was solved within the framework of all-electron full-potential and the augmented plane wave with local orbitals basis sets were adopted, this approach overcomes the drawbacks of real space grids and pseudopotential approximations and works well for some extreme conditions that the information near the nucleus is important. We demonstrated the approach by calculating the microscopic electron dynamics of crystalline silicon (Si) excited with an intense laser pulse and the macroscopic propagation of a laser pulse in a ∼3.5 μm free standing Si film with the feedback of microscopic electron dynamics coupled together. We also verified the availability of this approach under extreme condition with hundreds TPa (1 TPa = 10<sup>12</sup> Pa) pressure in a metallic solid neon, and reasonable results were obtained. Our approach provides a new paradigm for multiscale simulations of ultrafast phenomena that induced by intense laser pulses in condensed medium.", "Keywords": "", "DOI": "10.1016/j.cpc.2022.108633", "PubYear": 2023, "Volume": "284", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Science and Technology on Surface Physics and Chemistry Laboratory, Mianyang 621908, China"}], "References": [{"Title": "A comparison of numerical approaches to the solution of the time-dependent <PERSON><PERSON><PERSON><PERSON><PERSON> equation in one dimension", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "252", "Issue": "", "Page": "106808", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 105140032, "Title": "GEOTHERMALCLOUD: MACHINE LEARNING FOR GEOTHERMAL RESOURCE EXPLORATION", "Abstract": "<p>Geothermal is a renewable energy source that can provide reliable and flexible electricity generation for the world. In the past decade, play fairway analysis (PFA) studies identified that geothermal resources without surface expression (e.g., blind/hidden hydrothermal systems) have vast potential. However, a comprehensive search for these blind systems can be time-consuming, expensive, and resource-intensive, with a low probability of success. Accelerated discovery of these blind resources is needed with growing energy needs and higher chances of exploration success. Recent advances in machine learning (ML) have shown promise in shortening the timeline for this discovery. This paper presents a novel ML-based methodology for geothermal exploration towards PFA applications. Our methodology is provided through our open-source ML framework, GeoThermalCloud https://github.com/SmartTensors/GeoThermalCloud.jl. The GeoThermalCloud uses a series of un-supervised, supervised, and physics-informed ML methods available in SmartTensors AI platform https://github.com/SmartTensors. Through GeoThermalCloud, we can identify hidden patterns in the geothermal field data needed to discover blind systems efficiently. Crucial geothermal signatures often overlooked in traditional PFA are extracted using the GeoThermalCloud and analyzed by the subject matter experts to provide ML-enhanced PFA (ePFA), which is informative for efficient exploration. We applied our ML methodology to various open-source geothermal datasets within the U.S. (some of these are collected by past PFA work). The results provide valuable insights into resource types within those regions. This ML-enhanced workflow makes the GeoThermalCloud attractive for the geothermal community to improve existing datasets and extract valuable information often unnoticed during geothermal exploration.</p>", "Keywords": "geothermal energy exploration; unsupervised machine learning; play fairway analysis; SmartTensors artificial intelligence platform; hidden signatures", "DOI": "10.1615/JMachLearnModelComput.2022046445", "PubYear": 2022, "Volume": "3", "Issue": "4", "JournalId": 78716, "JournalTitle": "Journal of Machine Learning for Modeling and Computing", "ISSN": "2689-3967", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Maruti K. Mudunuru", "Affiliation": "Earth System Measurement & Data Group, Atmospheric Sciences & Global Change Division, Pacific Northwest National Laboratory, Richland, Washington 99352, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "EnviTrace LLC. Santa Fe, New Mexico 87501, USA"}, {"AuthorId": 3, "Name": "Bulbul Ahmmed", "Affiliation": "Earth and Environmental Sciences Division, Los Alamos National Laboratory, Los Alamos, New Mexico 87545, USA"}], "References": [{"Title": "Scientific Machine Learning Through Physics–Informed Neural Networks: Where we are and What’s Next", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "92", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}]}, {"ArticleId": 105140045, "Title": "Heterogeneous dual network with feature consistency for domain adaptation person re-identification", "Abstract": "<p>To reduce the noisy pseudo-labels generated by clustering for unsupervised domain adaptation (UDA) person re-identification (re-ID), the method of collaborative training between dual networks has been proposed and proved to be effective. However, most of these methods ignore the coupling problem between dual networks with the same architecture, which makes them inevitably share a high similarity and lack heterogeneity and complementarity. In this paper, we propose a heterogeneous dual network (HDNet) framework with two asymmetric networks, one of which applies convolution with limited receptive fields to obtain local information and the other uses Transformer to capture long-range dependency. Additionally, we propose feature consistency loss (FCL) that does not rely on pseudo-labels. FCL focuses more on the consistency of the sample in the feature space rather than the class prediction space, driving the feature learning of UDA re-ID from the task level to the feature level. Furthermore, we propose an adaptive channel mutual-aware (ACMA) module which contains two branches to focus on the global and local information between channels. We evaluate our proposed method on three popular datasets: DukeMTMC-reID, Market-1501 and MSMT17. Extensive experimental results demonstrate that our method achieves a competitive performance.</p>", "Keywords": "Person re-identification; Unsupervised domain adaptation; Heterogeneous dual network; Feature consistency; Attention", "DOI": "10.1007/s13042-022-01739-9", "PubYear": 2023, "Volume": "14", "Issue": "5", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Provincial Engineering Laboratory of Pattern Recognition and Computational Intelligence, Jiangnan University, Wuxi, China"}, {"AuthorId": 2, "Name": "Jun Kong", "Affiliation": "Key Laboratory of Advanced Process Control for Light Industry (Ministry of Education), Jiangnan University, Wuxi, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Jiangsu Provincial Engineering Laboratory of Pattern Recognition and Computational Intelligence, Jiangnan University, Wuxi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Information Engineering, The Hong Kong Polytechnic University, Hong Kong, China"}], "References": [{"Title": "Unsupervised domain adaptive re-identification: Theory and practice", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107173", "JournalTitle": "Pattern Recognition"}, {"Title": "Chinese medical relation extraction based on multi-hop self-attention mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "355", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Attention-based context aggregation network for monocular depth estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "6", "Page": "1583", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Artificial neural networks training algorithm integrating invasive weed optimization with differential evolutionary model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "6017", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Imitating targets from all sides: an unsupervised transfer learning method for person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "2281", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "OBPP: An ontology-based framework for privacy-preserving in IoT-based smart city", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "123", "Issue": "", "Page": "1", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Unsupervised person re-identification via K-reciprocal encoding and style transfer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "10", "Page": "2899", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Domain adaptive attention-based dropout for one-shot person re-identification", "Authors": "<PERSON><PERSON> Song; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "255", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}]}, {"ArticleId": 105140050, "Title": "A multi-factor two-stage deep integration model for stock price prediction based on intelligent optimization and feature clustering", "Abstract": "<p>Stock market fluctuations have a great impact on various economic and financial activities worldwide. Accurate prediction of stock prices plays a decisive role in constructing the investment decision or risk hedging. However, accurate prediction of the stock price is a thorny task, because stock price fluctuations are non-linear and chaotic. In order to promote the accuracy of stock price prediction, a multi-factor two-stage deep learning integrated prediction system based on intelligent optimization and feature clustering is proposed to predict stock price in this paper. Firstly, a multi-factor analysis is carried out to select a variety of factors that have an impact on the stock price, and adopt the extreme gradient boosting (XGBoost) algorithm to eliminate factors with low correlation. The second step is to apply the idea of classification prediction to cluster the filtered feature set. Further, multiple parameters of long short-term memory (LSTM) are optimized by genetic algorithm (GA), and multiple GA-LSTM models are obtained by training each clustering result. Finally, the results of each class predicted by the GA-LSTM model are nonlinearly integrated to acquire the final prediction model, which is applied to the prediction of the test set. The experimental results indicate that the performance of the proposed model outperforms other baseline models in China's two stock markets and the New York stock exchange. At the same time, these results fully prove that the prediction model proposed by us possesses more reliable and better predictive ability.</p>", "Keywords": "Stock price prediction; Deep learning; Feature extraction; Intelligent optimization; Nonlinear integration", "DOI": "10.1007/s10462-022-10352-9", "PubYear": 2023, "Volume": "56", "Issue": "7", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Nanjing University of Information Science and Technology, Nanjing, China; Collaborative Innovation Center on Forecast and Evaluation of Meteorological Disasters, Nanjing University of Information Science and Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "Shuzhou Zhu", "Affiliation": "School of Management Science and Engineering, Nanjing University of Information Science and Technology, Nanjing, China"}], "References": [{"Title": "A hybrid stock price index forecasting model based on variational mode decomposition and LSTM network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "12", "Page": "4296", "JournalTitle": "Applied Intelligence"}, {"Title": "A Novel Multi-Factor Stock Index Prediction Approach Using Principal Component Analysis, Feature Classification and Two-Stage Long Short-Term Memory Network with Residual Correction", "Authors": "WANG JUJIE; FENG CHUNCHEN; HE JUNJIE", "PubYear": 2020, "Volume": "54", "Issue": "3/2020", "Page": "95", "JournalTitle": "ECONOMIC COMPUTATION AND ECONOMIC CYBERNETICS STUDIES AND RESEARCH"}, {"Title": "Hybrid models for intraday stock price forecasting based on artificial neural networks and metaheuristic algorithms", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "147", "Issue": "", "Page": "124", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Research on a hybrid prediction model for stock price based on long short-term memory and variational mode decomposition", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "21", "Page": "13513", "JournalTitle": "Soft Computing"}, {"Title": "Stock index prediction and uncertainty analysis using multi-scale nonlinear ensemble paradigm of optimal feature extraction, two-stage deep learning and Gaussian process regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107898", "JournalTitle": "Applied Soft Computing"}, {"Title": "Application of online multitask learning based on least squares support vector regression in the financial market", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108754", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 105140234, "Title": "Channel structure and evolutionary stability analysis between traditional and green service supply chains", "Abstract": "<p>This paper aims to explore the optimal pricing and green service decisions and discuss the evolutionary stability strategy (ESS) of vertical channel structure strategic interaction between the traditional and green service supply chains (TSSC and GSSC). Considering these two supply chains could choose between the centralized (C) and decentralized (D) channel structures, current research establishes four channel models, namely, Models DD, DC, CD and CC, wherein Model DD(CC) means that both supply chains adopt channel D(C) and Model DC(CD) refers to GSSC adopting channel D(C) while TSSC using channel C(D). Furthermore, an evolutionary game is developed to explore the ESSs of the dynamic competitive system. The research results show that the stronger the integration between upstream and downstream firms of GSSC is, the higher green service the supply chain would provide when TSSC adopts channel D. Besides, when the market competition is sufficiently low, only point (0,0) is the ESS; when it is moderate, there exist two ESSs, i.e., ESS (0,0) and ESS (1,1); when it is extremely high, only point (1,1) is the ESS. The numerical examples show that the green service level increases in market competition while some retailing and wholesale prices under specific models would not be affected by it or show an inverted U shape, and the initial states of two supply chains’ channel strategies significantly impact the system’s ESSs.</p>", "Keywords": "Channel structure; Chain-to-chain competition; Green service supply chain; Evolutionary stability analysis", "DOI": "10.1007/s00500-022-07689-2", "PubYear": 2023, "Volume": "27", "Issue": "5", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Collaborative Innovation Center for Chongqing’s Modern Trade Logistics and Supply Chain, Chongqing Technology and Business University, Chongqing, China; School of Management Science and Engineering, Chongqing Technology and Business University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business, Henan University, Kaifeng, China"}], "References": [{"Title": "Green supply chain analysis under cost sharing contract with uncertain information based on confidence level", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "4", "Page": "2617", "JournalTitle": "Soft Computing"}, {"Title": "Green supply chain network design considering chain-to-chain competition on price and carbon emission", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "106503", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Optimizing the competitive service and pricing decisions of dual retailing channels: A combined coordination model", "Authors": "<PERSON>-<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "107789", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Pricing strategies of dual-channel green supply chain considering Big Data information inputs", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "6", "Page": "2981", "JournalTitle": "Soft Computing"}, {"Title": "Manufacturer’s selling mode choice in a platform-oriented dual channel supply chain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116842", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Comparison of subsidy strategies on the green supply chain under a behaviour-based pricing model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "14", "Page": "6789", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 105140359, "Title": "Altruistic production and distribution planning in the multilayer dual-channel supply chain: Using an improved NSGA-II with lion pride algorithm", "Abstract": "To achieve long-term stable development of the supply chain with online and retail channels, manufacturers may employ altruistic pricing strategies to increase retailers’ interests while considering their own interests. Therefore, this work creates a mixed-integer linear programming (MILP) model for planning altruistic production and distribution for a manufacturer in a multilayer dual-channel supply chain, while maximizing the total profit of both the manufacturer and retailers and the order fulfillment rate from the manufacturer to retailers. Different from previous works using fixed altruistic pricing, this model allows flexible altruistic pricing. Since MILP is NP-hard, this work further solves this problem by an improved nondominated sorting genetic algorithm II (NSGA-II) with lion pride algorithm (LPA), in which the NSGA-II is commonly used for solving bi-objective optimization problems; the LPA adopts the slope index measure to resolve the difficulty in distinguishing the solutions with similar performance at the later stage of the NSGA-II; and multiple crossover and mutation operators are integrated to increase diversity of solutions. Simulation results show that as compared with the selfish pricing strategy, the proposed altruistic pricing strategy significantly increases the proportion of the profit of retailers, so that the channel conflict between the manufacturer and retailers can be alleviated.", "Keywords": "", "DOI": "10.1016/j.cie.2022.108884", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, National Yang Ming Chiao Tung University, Hsinchu 300, Taiwan;Department of Business Administration, Asia University, Taichung 413, Taiwan;Department of Medical Research, China Medical University Hospital, China Medical University, Taichung 404, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Forestry, National Chung Hsing University, Taichung 402, Taiwan;Innovation and Development Center of Sustainable Agriculture, National Chung Hsing University, Taichung 402, Taiwan;Corresponding author at: Department of Forestry, National Chung Hsing University, Taichung 402, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, National Yang Ming Chiao Tung University, Hsinchu 300, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, National Yang Ming Chiao Tung University, Hsinchu 300, Taiwan"}], "References": [{"Title": "Two-stage pricing strategies of a dual-channel supply chain considering public green preference", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "106988", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Big data empowering low-carbon smart tourism study on low-carbon tourism O2O supply chain considering consumer behaviors and corporate altruistic preferences", "Authors": "Deqing Ma; <PERSON><PERSON><PERSON> Hu; <PERSON><PERSON>", "PubYear": 2021, "Volume": "153", "Issue": "", "Page": "107061", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Product pricing problem in green and non-green multi-channel supply chains under government intervention and in the presence of third-party logistics companies", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "159", "Issue": "", "Page": "107490", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 105140428, "Title": "Adaptive fuzzy command filtered control for incommensurate fractional-order MIMO nonlinear systems with input saturation", "Abstract": "<p>In this paper, an adaptive fuzzy control approach for incommensurate fractional-order multi-input multi-output (MIMO) systems with unknown nonlinearities and input saturation is presented. First, the nonlinear terms of MIMO systems are identified by introducing the fuzzy logic systems, and an adaptive compensating control term is provided to estimate the approximation errors. Then, the drawback of “explosion of complexity” in the typical backstepping is effectively figured out via an improved command filter, and the influence of filtered error is avoided by constructing the error compensation laws. Meanwhile, the input saturation issue is addressed by utilizing the fractional-order auxiliary equations. Derived from the fractional-order <PERSON><PERSON><PERSON>nov stability theory, it is proved that all signals of the closed-loop system are guaranteed to be bounded. Finally, the availability of the investigated control scheme is verified by simulation examples.</p>", "Keywords": "Fractional-order; Backstepping; Command filtering; Adaptive fuzzy control; Multi-input multi-output (MIMO); Input saturation", "DOI": "10.1007/s00521-022-08091-7", "PubYear": 2023, "Volume": "35", "Issue": "11", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Senkui Lu", "Affiliation": "Science and Technology on Rotorcraft Aeromechanics Laboratory, China Helicopter Research and Development Institute, Jingdezhen, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Science and Technology on Rotorcraft Aeromechanics Laboratory, China Helicopter Research and Development Institute, Jingdezhen, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Science and Technology on Rotorcraft Aeromechanics Laboratory, China Helicopter Research and Development Institute, Jingdezhen, People’s Republic of China; National Key Laboratory of Science and Technology on Rotorcraft Aeromechanics, Nanjing University of Aeronautics and Astronautics, Nanjing, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Science and Technology on Rotorcraft Aeromechanics Laboratory, China Helicopter Research and Development Institute, Jingdezhen, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Science and Technology on Rotorcraft Aeromechanics Laboratory, China Helicopter Research and Development Institute, Jingdezhen, People’s Republic of China"}], "References": [{"Title": "New insight into meshless radial point Hermite interpolation through direct and inverse 2-D reaction–diffusion equation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "3605", "JournalTitle": "Engineering with Computers"}, {"Title": "Adaptive neural network output feedback control of incommensurate fractional-order PMSMs with input saturation via command filtering and state observer", "Authors": "<PERSON><PERSON><PERSON>; Xingcheng Wang", "PubYear": 2021, "Volume": "33", "Issue": "11", "Page": "5631", "JournalTitle": "Neural Computing and Applications"}, {"Title": "The numerical solution of high dimensional variable-order time fractional diffusion equation via the singular boundary method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "", "Page": "73", "JournalTitle": "Journal of Advanced Research"}, {"Title": "Adaptive Fuzzy Variable Structure Control of Fractional-Order Nonlinear Systems with Input Nonlinearities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "7", "Page": "2309", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Fuzzy synchronization of fractional-order chaotic systems using finite-time command filter", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Fawaz <PERSON><PERSON>", "PubYear": 2021, "Volume": "579", "Issue": "", "Page": "325", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105140463, "Title": "Audio self-supervised learning: A survey", "Abstract": "Similar to humans’ cognitive ability to generalize knowledge and skills, self-supervised learning (SSL) targets discovering general representations from large-scale data. This, through the use of pre-trained SSL models for downstream tasks, alleviates the need for human annotation, which is an expensive and time-consuming task. Its success in the fields of computer vision and natural language processing have prompted its recent adoption into the field of audio and speech processing. Comprehensive reviews summarizing the knowledge in audio SSL are currently missing. To fill this gap, we provide an overview of the SSL methods used for audio and speech processing applications. Herein, we also summarize the empirical works that exploit audio modality in multi-modal SSL frameworks and the existing suitable benchmarks to evaluate the power of SSL in the computer audition domain. Finally, we discuss some open problems and point out the future directions in the development of audio SSL.", "Keywords": "self-supervised learning ; audio and speech processing ; multi-modal SSL ; representation learning ; unsupervised learning ; DSML 2: Proof-of-concept: Data science output has been formulated; implemented; and tested for one domain/problem", "DOI": "10.1016/j.patter.2022.100616", "PubYear": 2022, "Volume": "3", "Issue": "12", "JournalId": 74143, "JournalTitle": "Patterns", "ISSN": "2666-3899", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chair of Embedded Intelligence for Health Care & Wellbeing, University of Augsburg, 86159 Augsburg, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "Adria Mallol-Ragolta", "Affiliation": "Chair of Embedded Intelligence for Health Care & Wellbeing, University of Augsburg, 86159 Augsburg, Germany"}, {"AuthorId": 3, "Name": "Emilia <PERSON>da-Cabaleiro", "Affiliation": "Institute of Computational Perception, Johannes <PERSON>pler University Linz, 4040 Linz, Austria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medical Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Chair of Embedded Intelligence for Health Care & Wellbeing, University of Augsburg, 86159 Augsburg, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Chair of Embedded Intelligence for Health Care & Wellbeing, University of Augsburg, 86159 Augsburg, Germany"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Medical Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Chair of Embedded Intelligence for Health Care & Wellbeing, University of Augsburg, 86159 Augsburg, Germany;GLAM – the Group on Language, Audio, & Music, Imperial College London, London SW7 2AZ, UK"}], "References": [{"Title": "A tutorial on distance metric learning: Mathematical foundations, algorithms, experimental analysis, prospects and challenges", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "425", "Issue": "", "Page": "300", "JournalTitle": "Neurocomputing"}, {"Title": "N-HANS: A neural network-based toolkit for in-the-wild audio enhancement", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "18", "Page": "28365", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Fitbeat: COVID-19 estimation based on wristband heart rate using a contrastive convolutional auto-encoder", "Authors": "<PERSON><PERSON>; <PERSON>; Estela Laporta <PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108403", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 105140791, "Title": "Fixed time synchronization of octonion valued neural networks with time varying delays", "Abstract": "Octonion valued neural networks (OVNNs) does not fall into the category of Clifford valued neural networks because of the non-associativity of OVNNs. The present article contains the study of fixed time synchronization of OVNNs with time varying delays. Fixed time synchronization is an extension of the finite time synchronization. In fixed time synchronization, the trajectories of the error system reaches the origin in finite time independent of the initial conditions. In this article, OVNNs are decomposed into eight real valued systems of equations. Using some lemmas and <PERSON><PERSON><PERSON><PERSON> function, several sufficient criteria have been derived for the fixed time synchronization. Moreover, a suitable novel non-linear controller is created to keep the drive–response system in synchronization state. Finally, a numerical example is performed to demonstrate and validate the theoretical findings.", "Keywords": "", "DOI": "10.1016/j.engappai.2022.105684", "PubYear": 2023, "Volume": "118", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Mathematical Sciences, Indian Institute of Technology (Banaras Hindu University), Varanasi 221005, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematical Sciences, Indian Institute of Technology (Banaras Hindu University), Varanasi 221005, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematical Sciences, Indian Institute of Technology (Banaras Hindu University), Varanasi 221005, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Southeast University, Nanjing 210096, China;Yonsei Frontier Lab, Yonsei University, Seoul 03722, South Korea;Corresponding author at: School of Mathematics, Southeast University, Nanjing 210096, China"}], "References": [{"Title": "Stability criteria of quaternion-valued neutral-type delayed neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Zhenjiang Zhao", "PubYear": 2020, "Volume": "412", "Issue": "", "Page": "287", "JournalTitle": "Neurocomputing"}, {"Title": "General type-2 fuzzy multi-switching synchronization of fractional-order chaotic systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "104163", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Secured communication using efficient artificial neural synchronization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104478", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Synchronization for stochastic Lévy noise systems on a time-varying multi-weights network via delay intermittent control", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "108", "Issue": "", "Page": "104594", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Multistability analysis of octonion-valued neural networks with time-varying delays", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "609", "Issue": "", "Page": "1412", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105140813, "Title": "Innovative ANN hysteresis to predict hysteretic performance of composite reinforced concrete beam", "Abstract": "This article assess the precise estimation of the hysteresis loop of reinforced concrete (RC) beams in distinct failure cases to verify inelastic seismic beam function. Any test failure in RC frame columns is able to produce hysteresis curves in low cyclic repeat load that follows the analysis of the hysteretic behavior of the frame columns. In this case, the application of fibers as a mass enhancement to improve the post-cracking of RC beams, strength, and delay cracking has been investigated. In this research, the hysteretic response of deep and slender SFRC beams enhanced with SF using ten beams under the reversal cyclic load was studied through innovative ANN hysteresis. Shear and flexural strength of SFRC beams were analyzed using a diverse number of fibers with content from 0.1 to 5% per volume, closed stirrups (from 0 to 0.5%), and steel reinforcing bars (0.50% and 1.50%). The innovative artificial neural network hysteresis model has been utilized to define the accuracy prediction of the parameters and determine the hysteresis loop of RC columns failing in different modes. Comparing the experimental findings properly indicated the accuracy of the model to capture the main features of the response, such as the load versus deformation cyclic envelope, SFRC tension softening effect, and the impact of the fibers on the hysteretic energy. The results revealed that SFRC beams represented developed cyclic efficiency in case of deformation, load-bearing capacity, residual stiffness, cracking and energy dissipation ability while generating their integrity within the imposed reversal cyclic experiments.", "Keywords": "", "DOI": "10.1016/j.advengsoft.2022.103373", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Yan", "Affiliation": "School of Architectural Engineering, Chongqing Creation Vocational College, Chongqing 402160, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Architectural Engineering, Chongqing Creation Vocational College, Chongqing 402160, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Building and Construction Techniques Engineering Department, Al-Mustaqbal University College, 51001 Hillah, Babylon, Iraq"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer, College of Science and Arts in Ar Rass, Qassim University, Ar Rass, Qassim, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer, College of Science and Arts in Ar Rass, Qassim University, Ar Rass, Qassim, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Faculty of Science, King Khalid University, P.O. Box 9004, Abha, Saudi Arabia;Physics Department, Faculty of Science, Zagazig University, 44519 Zagazig, Egypt;Research Center for Advanced Materials Science (RCAMS), King Khalid University, Abha 61413, P.O. Box 9004, Saudi Arabia"}], "References": [{"Title": "CALRECOD — A software for Computed Aided Learning of REinforced COncrete structural Design", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "172", "Issue": "", "Page": "103189", "JournalTitle": "Advances in Engineering Software"}, {"Title": "Structural damage identification in thin-shell structures using a new technique combining finite element model updating and improved Cuckoo search algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "173", "Issue": "", "Page": "103206", "JournalTitle": "Advances in Engineering Software"}, {"Title": "Numerical performance evaluation of debonding strength in fiber reinforced polymer composites using three hybrid intelligent models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "173", "Issue": "", "Page": "103193", "JournalTitle": "Advances in Engineering Software"}]}, {"ArticleId": 105140816, "Title": "Hierarchical attention neural network for information cascade prediction", "Abstract": "Online social networking platforms have drastically facilitated the phenomenon of information cascades, making cascade prediction an important task for both researchers and practitioners. In cascade propagation paths, influential users can often attract more attention. Moreover, the community structure formed by users with similar interests creates information redundancy, thereby restricting the propagation process. These two features greatly affect the growth of the cascades. This paper investigates the incremental prediction of cascades during a future time period based on the evolution of cascades in early stages and proposes a neural network framework with hierarchical attention mechanisms, named Hierarchical Attention Cascade Neural Network (CasHAN). This network has a node-level attention mechanism based on user influence and a sequence-level attention mechanism based on community redundancy. User influence considers the user’s own attributes and the influence feedback provided by neighbors, while community redundancy measures information redundancy characteristics that limit cascade propagation. Through hierarchical attention mechanisms, the effective combination of these two features improves the accuracy of cascade increment prediction. Extensive experiments on real-world datasets demonstrate that our approach outperforms other state-of-the-art prediction methods.", "Keywords": "", "DOI": "10.1016/j.ins.2022.11.163", "PubYear": 2023, "Volume": "622", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University, Beijing 100044, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University, Beijing 100044, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication Technology, Griffith University, Queensland 4215, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Northwestern Polytechnical University, Xi’an 10072, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Cybersecurity, Chengdu University of Information Technology, Chengdu 610225, China"}], "References": [{"Title": "Time sensitivity-based popularity prediction for online promotion on Twitter", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "525", "Issue": "", "Page": "82", "JournalTitle": "Information Sciences"}, {"Title": "An efficient approach to identify social disseminators for timely information diffusion", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "78", "JournalTitle": "Information Sciences"}, {"Title": "Users’ mobility enhances information diffusion in online social networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "329", "JournalTitle": "Information Sciences"}, {"Title": "Popularity Prediction of Instagram Posts", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "9", "Page": "453", "JournalTitle": "Information"}, {"Title": "RNe2Vec: information diffusion popularity prediction based on repost network embedding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "2", "Page": "271", "JournalTitle": "Computing"}, {"Title": "Instagram Post Popularity Trend Analysis and Prediction using Hashtag, Image Assessment, and User History Features", "Authors": "", "PubYear": 2020, "Volume": "18", "Issue": "1", "Page": "85", "JournalTitle": "The International Arab Journal of Information Technology"}, {"Title": "Prediction of information cascades via content and structure proximity preserved graph level embedding", "Authors": "Xiao<PERSON> Feng; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "424", "JournalTitle": "Information Sciences"}, {"Title": "A Survey of Information Cascade Analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Recurrent Neural Network (RNN) to Analyse Mental Behaviour in Social Media: ", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "3", "Page": "1", "JournalTitle": "International Journal of Software Science and Computational Intelligence"}, {"Title": "Modeling information diffusion in social networks with ordinary linear differential equations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2022, "Volume": "593", "Issue": "", "Page": "614", "JournalTitle": "Information Sciences"}, {"Title": "Handling Data Scarcity Through Data Augmentation in Training of Deep Neural Networks for 3D Data Processing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "Attention-based explainable friend link prediction with heterogeneous context information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "597", "Issue": "", "Page": "211", "JournalTitle": "Information Sciences"}, {"Title": "A group behavior prediction model based on sparse representation and complex message interactions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "601", "Issue": "", "Page": "224", "JournalTitle": "Information Sciences"}, {"Title": "BMP: A blockchain assisted meme prediction method through exploring contextual factors from social networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "603", "Issue": "", "Page": "262", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105140823, "Title": "An alternative system to improve accessibility for wheelchair users: The stepped ramp", "Abstract": "Ramps are one of the main solutions for people with motor disabilities to overcome small disparities in height, both across cities and inside buildings. To permit the autonomous use of ramps, they must satisfy specific requisites. In particular, the slopes must not be excessively steep but adhere to the values identified in regulations and validated by scientific research. In historic cities, however, the placement of ramps is often complicated by a lack of space required for their length. In Venice, in particular, its urban morphology often makes it impossible to conform to the required slopes. For this reason, a specific ramp, known as “stepped ramp”, has been designed by technicians of the City of Venice with a steeper slope than allowed by regulations. It offers many possibilities but even some key problems. This paper presents a scientific analysis of ten different ramps to evaluate the structures that directly influence the feeling of comfort or discomfort of a wheelchair user with assistance, as well as the coefficients of friction of the different flooring surfaces. This study aims to understand objectively if this solution is efficient to improve accessibility in some specific circumstances, where it is not possible to follow the regulations using flat ramps.", "Keywords": "Stepped ramp;Urban accessibility;Wheelchair user", "DOI": "10.1016/j.apergo.2022.103938", "PubYear": 2023, "Volume": "108", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Architecture and Art, Università Iuav di Venezia, Italy. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Architecture and Art, Università Iuav di Venezia, Italy. Electronic address:  ."}], "References": []}, {"ArticleId": 105140966, "Title": "Design a remote sensing of multi-BOTDR fiber optic sensors for fuel pipeline monitoring", "Abstract": "The necessity for environmental protection has led to the development of diﬀerent sensors for monitoring fuel pipelines to avoid explosion, leakage, and corrosion. Distributed optical ﬁber sensors-based Brillouin scattering have become progressively common because of their novel feature of simultaneously determining temperature, vibration, and strain. The stimulated Brillouin scattering (SBS) threshold power, sensing distance, single-mode fiber cable length, and sensing accuracy are serious parameters to the implementation of Brillouin optical time-domain reﬂectometry (BOTDR) sensor. In this study, remote sensing of multi-BOTDR fiber optic sensors for fuel pipeline monitoring is designed and presented. The results of simulations and theoretical explainations on suppressing ﬁber optic nonlinearity are analyzed. Results show that an SBS suppression via 16 dB is required to attain the maximum multi-remote sensing distance of up to 20 km for each network and a perfect frequency shift of 11 GHz. An open eye pattern corresponds to minimal signal distortion with a high Q-factor of 22 for the developed sensor. The highest sensing accuracy of 0.3318 %/K indicating power change (%) is observed from 290–300 K at 193.1 THz operating frequency.", "Keywords": "bidirectional optical fiber;brillouin scattering;remote sensing;scattering threshold power", "DOI": "10.12928/telkomnika.v21i1.24255", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Palestine Technical University–Kadoorie (PTUK)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Palestine Technical University–Kadoorie (PTUK)"}], "References": []}, {"ArticleId": 105141015, "Title": "Fusion of vertical and oblique images using Intra-Cluster-Classification for building damage assessment", "Abstract": "Using High Resolution (HR) and Very High Resolution (VHR) Remote Sensing (RS) images for post-disaster building damage assessment provides more information than low-resolution images. Consequently, a damage map is expected to be at building level, which requires both rooftop and facade information. Oblique imagery is therefore becoming increasingly popular for post-disaster analysis. However, oblique images are rarely available in the pre-disaster acquisition, and thus without any prior information it is a challenging task to assess the facade damage just from a post-disaster scene. As a solution, we aggregated information at the cluster level using pre-disaster neighborhood buildings’ feature analysis, and thus the post-disaster building-level damage assessment is supported by the cluster-level information. Therefore, the proposed method, Intra-Cluster-Classification (ICC), uses hierarchical steps of unsupervised and supervised methods to detect damaged and undamaged areas within each cluster of buildings. The procedure is implemented on Google Earth Engine platform, and the results are evaluated using Hurricane Michael (2018) images. At the building-level, damage information is shown as a fractional number between 0 and 1, with the higher number indicating more destruction. R-squared (R<sup>2</sup>) value is 0.9688 between actual and predicted damage scores. In addition, the Overall Accuracy (OA) and the Kappa coefficient (K) in the 4-class RS-scale are 83.2% and 0.7438, respectively. Furthermore, in 3-class RS-scale, the OA and K of our results are 91.08%, and 0.8582, respectively.", "Keywords": "Building damage ; Oblique image ; High resolution ; Fusion ; Google Earth Engine", "DOI": "10.1016/j.compeleceng.2022.108536", "PubYear": 2023, "Volume": "105", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Electrical & Computer Engineering Department, Babol Noshirvani University of Technology, Babol, Iran, 4714871167"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical & Computer Engineering Department, Babol Noshirvani University of Technology, Babol, Iran, 4714871167;Corresponding author"}], "References": [{"Title": "A novel approach for unsupervised image segmentation fusion of plant leaves based on G-mutual information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Building damage assessment for rapid disaster response with a deep object-based semantic change detection framework: From natural disasters to man-made disasters", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "265", "Issue": "", "Page": "112636", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 105141017, "Title": "As-Continuous-As-Possible Extrusion-Based Fabrication of Surface Models", "Abstract": "<p>In this study, we propose a computational framework for optimizing the continuity of the toolpath in fabricating surface models on an extrusion-based 3D printer. Toolpath continuity is a critical issue that influences both the quality and the efficiency of extrusion-based fabrication. Transfer moves lead to rough and bumpy surfaces, where this phenomenon worsens for materials with large viscosity, like clay. The effects of continuity on the surface models are even more severe in terms of the quality of the surface and the stability of the model. We introduce a criterion called the “one-path patch” (OPP) to represent a patch on the surface of the shell that can be traversed along one path by considering the constraints on fabrication. We study the properties of the OPPs and their merging operations to propose a bottom-up OPP merging procedure to decompose the given shell surface into a minimal number of OPPs, and to generate the ”as-continuous-as-possible” (ACAP) toolpath. Furthermore, we augment the path planning algorithm with a curved-layer printing scheme that reduces staircase defects and improves the continuity of the toolpath by connecting multiple segments. We evaluated the ACAP algorithm on ceramic and thermoplastic materials, and the results showed that it improves the fabrication of surface models in terms of both efficiency and surface quality.</p>", "Keywords": "Toolpath planning; shell models; extrusion-based printing", "DOI": "10.1145/3575859", "PubYear": 2023, "Volume": "42", "Issue": "3", "JournalId": 15014, "JournalTitle": "ACM Transactions on Graphics", "ISSN": "0730-0301", "EISSN": "1557-7368", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shandong University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong University, IST Austria and University of Washington"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shandong University"}], "References": [{"Title": "Ceramic 3D printed sweeping surfaces", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "108", "JournalTitle": "Computers & Graphics"}, {"Title": "A Framework for Adaptive Width Control of Dense Contour-Parallel Toolpaths in Fused Deposition Modeling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "", "Page": "102907", "JournalTitle": "Computer-Aided Design"}, {"Title": "Reinforcement of General Shell Structures", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Variable-width contouring for additive manufacturing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "VDAC", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Reinforced FDM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Multi-Axis Support-Free Printing of Freeform Parts with Lattice Infill Structures", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "133", "Issue": "", "Page": "102986", "JournalTitle": "Computer-Aided Design"}, {"Title": "Shell thickening for extrusion-based ceramics printing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "97", "Issue": "", "Page": "160", "JournalTitle": "Computers & Graphics"}]}, {"ArticleId": 105141047, "Title": "ANALYSIS OF MARСOVIAN SYSTEMS WITH A GIVEN SET OF SELECTED STATES", "Abstract": "<p>Analysis of stationary Marcovian systems is traditionally performed using systems of linear Kolmogorov differential equations. Such systems make it possible to determine the probability of the analyzed system being in each of its possible states at an arbitrary time. This standard task becomes more complicated if the set of possible states of systems is heterogeneous and some special subset can be distinguished from it, in accordance with the specifics of the system functioning. Subject of the study is technology development for such systems analysis. In accordance with this, the purpose of the work is to find the distribution law of the random duration of such a system's stay on a set of possible states until it falls into a selected subset of these states. Method for solving the problem is proposed based on splitting the entire set of possible states of the system into two subsets. The first of them contains a selected subset of states, and the second contains all the other states of the system. Now a subset of states is allocated from the second subset, from which a direct transition to the states of the first subset is possible. Next, a system of differential equations describing the transitions between the formed subsets is formed. The solution of this system of equations gives the desired result – distribution of the random duration of the system's stay until the moment of the first hit in the selected subset of states. The method allows solving a large number of practical problems, for example, in the theory of complex systems reliability with many different failure states. In particular, finding the law of the uptime duration distribution, calculating the average duration of uptime.</p>", "Keywords": "Marcovian systems;subset of special states;analysis of inhomogeneous systems states probabilities dynamics", "DOI": "10.20998/2522-9052.2022.4.08", "PubYear": 2022, "Volume": "6", "Issue": "4", "JournalId": 52678, "JournalTitle": "Advanced Information Systems", "ISSN": "2522-9052", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Technical University \"Kharkiv Polytechnic Institute\", Kharkiv"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Mikhail <PERSON>y National University, Kremenchuk"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Technical University \"Kharkiv Polytechnic Institute\", Kharkiv"}], "References": []}, {"ArticleId": 105141058, "Title": "METHODS OF DETECTION OF TEMPERATURE FACTORS AFFECTING TRAFFIC SAFETY OF RAILWAY TRANSPORTATION AND RISK ANALYSIS", "Abstract": "<p>The article is dedicated to the investigation of internal and external factors affecting traffic safety of railway transport, including researching and solving ever-present issue of investigating, reducing and eliminating human factors, emergency situations and other impacts through complex methods and means. The technical state of wagons, one of the important components for traffic safety in railway transport, methods and means of detecting faults during their diagnostics, and making right decisions according to the situation are investigated. Diagnostics of the state of wagons is carried out through the method of remote measurement at measuring points installed at certain distances with a certain rule. At measuring points, the temperatures of tire boxes are investigated according to the normal limits, and risk status is assessed according to the comparison results. Accuracy and stability of diagnostics are very important for safe operation. In order to make right decisions, assessment of measurement errors of temperature factors, performing self-monitoring and correction, execution of the algorithm based on repeated measurements and points, carrying out comparison with norm limits, and making decisions provided that they are confirmed are presented. Based on Fuzzy Logic in Matlab environment, assessment of processing risks and suitable combinations are presented.</p>", "Keywords": "hot box detectors;railway vehicle gearboxes;fault diagnosis;railcar condition monitoring;calibration;bolometr;traffıc safety;computer simulation", "DOI": "10.20998/2522-9052.2022.4.06", "PubYear": 2022, "Volume": "6", "Issue": "4", "JournalId": 52678, "JournalTitle": "Advanced Information Systems", "ISSN": "2522-9052", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Azerbaijan State Oil and Industry University, Baku"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Azerbaijan Railways CJSC, Baku"}], "References": []}, {"ArticleId": 105141079, "Title": "Synthesis and application of dual-channel fluorescent probes for selective recognition of SO2/H2O2", "Abstract": "The balance between active oxidizing species and active reducing species plays an important role in cell metabolism. A multitude of excellent probes can be used for the discriminative detection of reactive REDOX pairs have been reported. However, probes that can be used to detect both sulfur dioxide (SO<sub>2</sub>) and hydrogen peroxide (H<sub>2</sub>O<sub>2</sub>) have not been reported. In this work, we synthesized benzoindolium probes BI and BBI containing boronic acid pinacol ester group, which can distinguish sulfur dioxide (SO<sub>2</sub>) and hydrogen oxide (H<sub>2</sub>O<sub>2</sub>) by generating different fluorescence signals. They also have the advantages of fast response time, high sensitivity, and high selectivity. In addition, the probes successfully achieved fluorescence imaging of SO<sub>2</sub> and H<sub>2</sub>O<sub>2</sub> in HeLa cells and zebrafish.", "Keywords": "Hydrogen peroxide ; Sulfur dioxide ; Benzoindolium ; Discriminative detection ; Fluorescence imaging", "DOI": "10.1016/j.snb.2022.133146", "PubYear": 2023, "Volume": "378", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Pharmacy, Xinxiang Medical University, Xinxiang, Henan 453003, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Life Science and Technology, Xinxiang Medical University, Xinxiang, Henan 453003, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Medical Engineering, Xinxiang Medical University, Xinxiang, Henan 453003, PR China;Corresponding authors"}, {"AuthorId": 4, "Name": "Ying<PERSON>Ying <PERSON>", "Affiliation": "School of Basic Medical Sciences, Xinxiang Medical University, Xinxiang, Henan 453003, PR China;Corresponding authors"}], "References": [{"Title": "A benzo[b]xanthene-derived fluorescent probe capable of two-photon ratiometric imaging of lysosomal cysteine with high specificity", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "322", "Issue": "", "Page": "128588", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A reversible NIR fluorescent probe for monitoring of SO2 and formaldehyde in live cells and zebrafish", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "366", "Issue": "", "Page": "131962", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 105141094, "Title": "Rapid on-site PEDV detection using homogeneous fluorescence resonance energy transfer-based ELISA", "Abstract": "The porcine epidemic diarrhea virus (PEDV) is a major pathogen of swine enteric diseases. Various etiology and serological methods have been employed for PEDV detection, but most of their applications are limited to laboratories. To extend PEDV detection to on-site applications, we design a homogeneous fluorescence resonance energy transfer (FRET)-based enzyme-linked immunosorbent assay (ELISA). Both donor and acceptor fluorescence microspheres modified PEDV antibodies can be linked only to the occurrence of PEDV antigen, thus generating FRET signals, which can be collected by our designed portable FRET immunoassay station (FRETIS). Verified by standard samples, FRET immunoassay reached high sensitivity with a detection limit as TCID<sub>50</sub> (median tissue culture infective dose) of 10/mL, which is 10 times more sensitive than colloidal gold test strips; and verified by clinical samples, it was also proved with high accuracy, good selectivity, and repeatability. More importantly, FRET immunoassay could detect PEDV in a 96-well plate in 35 min with only one step of incubation without any further washing steps using field-portable devices and field-operable procedures, well supporting on-site applications. Considering these advantages, this reported FRET immunoassay provides a promising way for multi-sample on-site PEDV detection and can be potentially used in the swine industry.", "Keywords": "PEDV detection ; FRET immunoassay ; Rapid on-site detection ; FRET immunoassay station (FRETIS)", "DOI": "10.1016/j.snb.2022.133138", "PubYear": 2023, "Volume": "378", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Joint International Research Laboratory of Animal Health and Food Safety of Ministry of Education & Single Molecule Nanobiology Laboratory (Sinmolab), Nanjing Agricultural University, Nanjing, Jiangsu 210095, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Joint International Research Laboratory of Animal Health and Food Safety of Ministry of Education & Single Molecule Nanobiology Laboratory (Sinmolab), Nanjing Agricultural University, Nanjing, Jiangsu 210095, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Joint International Research Laboratory of Animal Health and Food Safety of Ministry of Education & Single Molecule Nanobiology Laboratory (Sinmolab), Nanjing Agricultural University, Nanjing, Jiangsu 210095, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Joint International Research Laboratory of Animal Health and Food Safety of Ministry of Education & Single Molecule Nanobiology Laboratory (Sinmolab), Nanjing Agricultural University, Nanjing, Jiangsu 210095, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Veterinary Medicine, Hunan Agricultural University, Changsha, Hunan 410128, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Manufacture Science and Engineering, Key Laboratory of Testing Technology for Manufacturing Process, Ministry of Education, Southwest University of Science and Technology, Mianyang, Sichuan 621010, China"}, {"AuthorId": 7, "Name": "<PERSON>ding <PERSON>", "Affiliation": "Advanced Institute of Micro-Nano Intelligent Sensing (AIMNIS), School of Electronic Information Engineering, Xi’an Technological University, Xi’an, Shaanxi 710032, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Joint International Research Laboratory of Animal Health and Food Safety of Ministry of Education & Single Molecule Nanobiology Laboratory (Sinmolab), Nanjing Agricultural University, Nanjing, Jiangsu 210095, China;OptiX+ Laboratory, School of Electronics and Information Engineering, Wuxi University, Wuxi, Jiangsu 214105, China;Corresponding author at: Joint International Research Laboratory of Animal Health and Food Safety of Ministry of Education & Single Molecule Nanobiology Laboratory (Sinmolab), Nanjing Agricultural University, Nanjing, Jiangsu, 210095, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Joint International Research Laboratory of Animal Health and Food Safety of Ministry of Education & Single Molecule Nanobiology Laboratory (Sinmolab), Nanjing Agricultural University, Nanjing, Jiangsu 210095, China;Corresponding author"}], "References": []}, {"ArticleId": 105141352, "Title": "CFIO: A conflict-free I/O mechanism to fully exploit internal parallelism for Open-Channel SSDs", "Abstract": "I/O access conflicts make utilization within NVMe SSDs seriously low, which introduces unpredictable performance loss of NVMe SSDs. Although existing works adopt I/O isolation or conflict-aware I/O scheduling to avoid access conflicts, they can result in an unbalanced utilization and reduce the lifetime of NVMe SSDs. In this paper, we design and implement CFIO, a low-overhead conflict-aware I/O mechanism that achieves conflict-free I/Os to exploit the internal parallelism in NVMe SSDs. CFIO improves PU utilization and reduces I/O latency with two novel mechanisms. First, a conflict-free (CF) lane is proposed to eliminate conflicts by dividing I/O requests into conflict-free PU queues based on physical addresses. The PU queues correspond to the PU resources within the NVMe SSDs. Second, a k -RR scheduler is designed to dispatch reading and writing requests to NVMe SSDs in batches and separately. K -RR scheduler can fully exploit the internal parallelism of NVMe SSDs and form an I/O pipeline based on the dual registers of PU. Finally, we integrate CFIO into the LightNVM with Open-Channel NVMe SSD (OCSSD) and compare it with several existing solutions. Our evaluations show that CFIO improves the throughput of OCSSD by 19.32% and reduces its tail latency by 23.71%, compared to state-of-the-art methods.", "Keywords": "NVMe SSDs ; Open-Channel SSD ; Access conflict ; Conflict-free I/O management", "DOI": "10.1016/j.sysarc.2022.102803", "PubYear": 2023, "Volume": "135", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Software Development Environment, Beihang University, Beijing, 100191, China;School of Computer Science and Engineering, Beihang University, Beijing, 100191, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Software Development Environment, Beihang University, Beijing, 100191, China;School of Computer Science and Engineering, Beihang University, Beijing, 100191, China;Corresponding authors at: State Key Laboratory of Software Development Environment, Beihang University, Beijing, 100191, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Software Development Environment, Beihang University, Beijing, 100191, China;School of Computer Science and Engineering, Beihang University, Beijing, 100191, China;Corresponding authors at: State Key Laboratory of Software Development Environment, Beihang University, Beijing, 100191, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Software Development Environment, Beihang University, Beijing, 100191, China;School of Computer Science and Engineering, Beihang University, Beijing, 100191, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Smart City College, Beijing Union University, Beijing, 100101, China"}], "References": [{"Title": "SSW: A strictly sequential writing method for open-channel SSD", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "101828", "JournalTitle": "Journal of Systems Architecture"}]}, {"ArticleId": 105141383, "Title": "Investigation of transverse vibration suppression of hoisting catenaries in mine hoists by virtual prototype and uniform design", "Abstract": "In the hoisting process of an ultra-deep mine shaft, the winding movement of ropes on the Lebus drum will cause transverse vibrations of the catenaries, leading to intense swing of the rope and even winding confusion. In this paper, the transverse vibration virtual model of the catenary was firstly established using bushing sleeve force method in ADAMS, then the displacement response of the catenary under the drum excitation was obtained. Secondly, the correctness of the established model was verified by comparing the numerical simulation results to the ADAMS model results. Finally, a spring-damper device was proposed to further suppress the transverse vibration of the catenary based on the boundary control. The reasonable values of the stiffness and damping parameters of the damper were obtained by applying the uniform experimental design. This study provides a theoretical support for the reduction of the transverse vibration of hoisting catenary. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "hoisting catenary; spring-damper device; transverse vibration; uniform design; vibration suppression; virtual prototype", "DOI": "10.1504/IJMIC.2022.127514", "PubYear": 2022, "Volume": "41", "Issue": "3", "JournalId": 12335, "JournalTitle": "International Journal of Modelling, Identification and Control", "ISSN": "1746-6172", "EISSN": "1746-6180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Nantong University, Jiangsu, Nantong, 226019, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Nantong University, Jiangsu, Nantong, 226019, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Nantong University, Jiangsu, Nantong, 226019, China"}], "References": []}, {"ArticleId": 105141530, "Title": "Machine learning approach for corona virus disease extrapolation: A case study", "Abstract": "<p>Supervised/unsupervised machine learning processes are a prevalent method in the field of Data Mining and Big Data. Corona Virus disease assessment using COVID-19 health data has recently exposed the potential application area for these methods. This study classifies significant propensities in a variety of monitored unsupervised machine learning of K-Means Cluster procedures and their function and use for disease performance assessment. In this, we proposed structural risk minimization means that a number of issues affect the classification efficiency that including changing training data as the characteristics of the input space, the natural environment, and the structure of the classification and the learning process. The three problems mentioned above improve the broad perspective of the trajectory cluster data prediction experimental coronavirus to control linear classification capability and to issue clues to each individual. K-Means Clustering is an effective way to calculate the built-in of coronavirus data. It is to separate unknown variables in the database for the disease detection process using a hyperplane. This virus can reduce the proposed programming model for K-means, map data with the help of hyperplane using a distance-based nearest neighbor classification by classifying subgroups of patient records into inputs. The linear regression and logistic regression for coronavirus data can provide valuation, and tracing the disease credentials is trial.</p>", "Keywords": "", "DOI": "10.3233/KES-220015", "PubYear": 2022, "Volume": "26", "Issue": "3", "JournalId": 18404, "JournalTitle": "International Journal of Knowledge-based and Intelligent Engineering Systems", "ISSN": "1327-2314", "EISSN": "1875-8827", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of IT, Shri Vishnu Engineering College for Women, Bhimavaram, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, SRKR Engineering College, Bhimavaram, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, GIET University, Gunupur, Odisha, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering, KIIT Deemed to be University, Bhubaneswar, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Science and Engineering, PDA College of Engineering, Kalaburagi, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Application and Information Technology Dr <PERSON><PERSON><PERSON> University, Ranchi, India"}], "References": []}, {"ArticleId": 105141565, "Title": "A Reliable Client Detection System during Load Balancing for Multi-tenant Cloud Environment", "Abstract": "<p>Cloud computing is a broad-scale distributed computing system and is a widely accepted archetype that has many luxurious features, including traffic scalability, resource allocation, accessibility, inter-communication cost, and many more. Security is considered one of the primary concerns in the cloud. This paper chooses to formulate this problem by improving the Virtual Machine (VM) allocation policy. The authors have enhanced this policy from a different perspective by maintaining the difficulty in co-tenancy between attackers and targets. A secure resource allocation mechanism has been proposed to prevent multi-tenancy attacks, where attackers and the target are co-tenants on the same server. The multi-objective approach is implemented for a secure load balancing model called reliable client detection system (RCDS). This model inquires the safe or unsafe states all along VM distribution which accomplishes and estimates the reliability of the clients as per the historical performances. When cloud data centers have received the demands to deploy the upcoming jobs, the introduced model helps to find a secure physical machine for balancing the load with avoiding threats. It is evident from the results that RCDS can effectively diminish the risks and security score when increased from 100 to 1000 numbers of cloudlets under the safe states. Performance evaluation demonstrates that RCDS achieves high throughput, avoids traffic overflow, and reduces traffic up to 33.37% in the network.</p>", "Keywords": "Cloud computing; Multi-tenancy; Allocation policy; Security; Resource utilization", "DOI": "10.1007/s42979-022-01504-3", "PubYear": 2023, "Volume": "4", "Issue": "1", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, National Institute of Technology, Kurukshetra, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, Panipat Institute of Engineering and Technology, Samalkha, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, National Institute of Technology, Kurukshetra, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Applications, National Institute of Technology, Kurukshetra, India"}], "References": []}, {"ArticleId": 105141576, "Title": "List of contributing reviewers 2022*", "Abstract": "", "Keywords": "", "DOI": "10.3233/IP-229018", "PubYear": 2022, "Volume": "27", "Issue": "4", "JournalId": 28496, "JournalTitle": "Information Polity", "ISSN": "1570-1255", "EISSN": "1875-8754", "Authors": [], "References": []}, {"ArticleId": 105141700, "Title": "Hybrid Algorithm for Node Deployment with the Guarantee of Connectivity in Wireless Sensor Networks", "Abstract": "In this paper, the problem of deployment in wireless sensor networks is investigated. The authors propose a Hybrid Modified Crow Search Bee Algorithm (HMCSBA) for coverage maximization with the guarantee of connectivity between the deployed sensors. Firstly, a Modified Crow Search Algorithm (MCSA) is proposed based on the basic CSA algorithm to form a connected network after initial random deployment. The position equation of the original CSA was updated by introducing a linear flight length that increases throughout iterations to force the sensors to join the network. Then, the Bees Algorithm (BA) is applied to optimize the network coverage without losing connectivity between the deployed sensors. Simulations and comparative studies were carried out to prove the relevance of the proposed algorithm. Results demonstrate that the proposed algorithm can optimize the coverage and guarantee network connectivity.", "Keywords": "Bees Algorithm; Connectivity; Coverage; CSA; Deployment; Metaheuristics; Optimization; Simulation", "DOI": "10.31449/inf.v46i8.3370", "PubYear": 2022, "Volume": "46", "Issue": "8", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University <PERSON><PERSON><PERSON> of Mascara"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University <PERSON><PERSON><PERSON> of Mascara"}], "References": []}, {"ArticleId": 105141849, "Title": "Optimal Placement and Sizing of DGs in Distribution Networks Using Dandelion Optimization Algorithm: Case Study of an Algerian Distribution Network", "Abstract": "The optimal placement of distributed generation (DG) in power distribution systems involves identifying the best locations for the generators to be installed and sizing them appropriately, to optimize the performance of the system. In this paper, the recently proposed nature-inspired optimization algorithm namely: Dandelion Optimizer (DO) has been used for the optimal placement and sizing of DG in the radial distribution network. The objectives are to minimize active power loss and voltage deviation and to enhance the voltage stability of the distribution network. The efficiency of the proposed method has been verified over the IEEE 33-bus and Algerian 112-bus distribution systems. The result comparisons indicated that the proposed method can obtain higher quality solutions than many other methods for the considered scenarios from the test systems. Therefore, the DO algorithm can be a very effective method for solving the optimal allocation of the DG problem.", "Keywords": "", "DOI": "10.30534/ijatcse/2022/021162022", "PubYear": 2022, "Volume": "11", "Issue": "6", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 105141999, "Title": "Does working memory capacity influence learning from video and attentional processing of the instructor’s visuals?", "Abstract": "", "Keywords": "", "DOI": "10.1080/0144929X.2022.2155574", "PubYear": 2024, "Volume": "43", "Issue": "1", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Teaching, Learning and Curriculum Studies, College of Education, Health, and Human Services, Kent State University, Kent, OH, USA"}], "References": [{"Title": "Does visual attention to the instructor in online video affect learning and learner perceptions? An eye-tracking analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "103779", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 105142011, "Title": "Software assistants in software engineering: A systematic mapping study", "Abstract": "<p>The increasing essential complexity of software systems makes current software engineering methods and practices fall short in many occasions. Software assistants have the ability to help humans achieve a variety of tasks, including the development of software. Such assistants, which show human-like competences such as autonomy and intelligence, help software engineers do their job by empowering them with new knowledge. This article investigates the research efforts that have been conducted on the creation of assistants for software design, construction and maintenance paying special attention to the user-assistant interactions. To this end, we followed the standard systematic mapping study method to identify and classify relevant works in the state of the art. Out of the 7580 articles resulting from the automatic search, we identified 112 primary studies that present works which qualify as software assistants. We provide all the resources needed to reproduce our study. We report on the trends and goals of the assistants, the tasks they perform, how they interact with users, the technologies and mechanisms they exploit to embed intelligence and provide knowledge, and their level of automation. We propose a classification of software assistants based on interactions and present an analysis of the different automation patterns. As outcomes of our study, we provide a classification of software assistants dealing with the design, construction and maintenance phases of software development, we discuss the results, identify open lines of work and challenges and call for new innovative and rigorous research efforts in this field.</p>", "Keywords": "software assistants;software construction;software design;software maintenance;systematic mapping study", "DOI": "10.1002/spe.3170", "PubYear": 2023, "Volume": "53", "Issue": "3", "JournalId": 1840, "JournalTitle": "Software: Practice and Experience", "ISSN": "0038-0644", "EISSN": "1097-024X", "Authors": [{"AuthorId": 1, "Name": "Maxime Savary‐Leblanc", "Affiliation": "UMR 9189 CRIStAL, Univ. Lille, CNRS, Inria, Centrale Lille  Lille France"}, {"AuthorId": 2, "Name": "<PERSON>ue<PERSON>", "Affiliation": "SOM Research Lab Open University of Catalonia  Barcelona Spain;Department of Computer Science and Programming Languages University of Malaga  Malaga Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "SOM Research Lab Open University of Catalonia  Barcelona Spain;Internet Interdisciplinary Institute ICREA  Barcelona Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "UMR 9189 CRIStAL, Univ. Lille, CNRS, Inria, Centrale Lille  Lille France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Université Paris‐Saclay, CEA List  Palaiseau France"}], "References": [{"Title": "Code generation using model driven architecture: A systematic mapping study", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "100935", "JournalTitle": "Journal of Computer Languages"}]}, {"ArticleId": 105142087, "Title": "Issue Information", "Abstract": "<p>No abstract is available for this article.</p>", "Keywords": "", "DOI": "10.1002/jsid.1055", "PubYear": 2022, "Volume": "30", "Issue": "12", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [], "References": []}, {"ArticleId": 105142091, "Title": "Auto-generated Relative Importance for Multi-agent Inducing Variable in Uncertain and Preference Involved Evaluation", "Abstract": "Inducing information and bi-polar preference-based weights allocation and relevant decision-making are one important branch of <PERSON><PERSON>’s decision theory. In the context of basic uncertain information environment, there exist more than one inducing factor and the relative importance between them should be determined. Some subjective methods require decision makers to indicate the bi-polar preference extents for each inducing factor as well as the relative importance between all the involved inducing factors. However, although the bi-polar preference extents for inducing factors can often be elicited, sometimes decision makers cannot provide the required relative importance. This work presents some approaches to address such problem in basic uncertain information environment. From the mere bi-polar preference extents offered by decision makers, we propose three methods, statistic method, distance method and linguistic variable method, to derive relative importance between different inducing factors, respectively. Each of them has advantages and disadvantages, and the third method serves as a trade-off between the first two methods. The rationale of preference and uncertainty involved evaluation is analyzed, detailed evaluation procedure is presented, and numerical example is given to illustrate the proposals.", "Keywords": "Aggregation operators; Basic uncertain information; Bi-polar preferences; Induced ordered weighted averaging operators; Ordered weighted averaging; Weights allocation", "DOI": "10.1007/s44196-022-00167-5", "PubYear": 2022, "Volume": "15", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering Management, School of Civil Engineering, Wuhan University, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering Management, School of Civil Engineering, Wuhan University, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Arts and Science, University of Toronto, Toronto, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Nanjing Audit University, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Jaén, Jaén, Spain"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Jaén, Jaén, Spain"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Arts and Sciences, Shanghai Maritime University, Shanghai, China"}, {"AuthorId": 8, "Name": "Le<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Nanjing Normal University, Nanjing, China"}], "References": [{"Title": "Basic uncertain information soft set and its application to multi-criteria group decision making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "103871", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Online-review analysis based large-scale group decision-making for determining passenger demands and evaluating passenger satisfaction: Case study of high-speed rail system in China", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "", "Page": "22", "JournalTitle": "Information Fusion"}, {"Title": "Interval-valued seminormed fuzzy operators based on admissible orders", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "96", "JournalTitle": "Information Sciences"}, {"Title": "Sustainable building material selection: An integrated multi-criteria large group decision making framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107903", "JournalTitle": "Applied Soft Computing"}, {"Title": "Symmetric weights for OWA operators prioritizing intermediate values. The EVR-OWA operator", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "584", "Issue": "", "Page": "583", "JournalTitle": "Information Sciences"}, {"Title": "Large-scale group decision-making for prioritizing engineering characteristics in quality function deployment under comparative linguistic environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "109359", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 105142122, "Title": "FL-Defender: Combating targeted attacks in federated learning", "Abstract": "Federated learning (FL) enables learning a global machine learning model from data distributed among a set of participating workers. This makes it possible (i) to train more accurate models due to learning from rich, joint training data and (ii) to improve privacy by not sharing the workers’ local private data with others. However, the distributed nature of FL makes it vulnerable to targeted poisoning attacks that negatively impact on the integrity of the learned model while, unfortunately, being difficult to detect. Existing defenses against those attacks are limited by assumptions on the workers’ data distribution and/or are ill-suited to high-dimensional models. In this paper, we analyze targeted attacks against FL, specifically label-flipping and backdoor attacks, and find that the neurons in the last layer of a deep learning (DL) model that are related to these attacks exhibit a different behavior from the unrelated neurons. This makes the last-layer gradients valuable features for attack detection. Accordingly, we propose FL-Defender to combat FL targeted attacks. It consists of (i) engineering robust discriminative features by calculating the worker-wise angle similarity for the workers’ last-layer gradients, (ii) compressing the resulting similarity vectors using PCA to reduce redundant information, and (iii) re-weighting the workers’ updates based on their deviation from the centroid of the compressed similarity vectors. Experiments on three data sets show the effectiveness of our method in defending against label-flipping and backdoor attacks. Compared to several state-of-the-art defenses, FL-Defender achieves the lowest attack success rates while maintaining the main task accuracy.", "Keywords": "", "DOI": "10.1016/j.knosys.2022.110178", "PubYear": 2023, "Volume": "260", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitat Rovira i Virgili, Department of Computer Engineering and Mathematics, CYBERCAT Center for Cybersecurity Research of Catalonia, UNESCO Chair in Data Privacy, Av. <PERSON>s Catalans 26, E-43007, Tarragona, Catalonia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitat Rovira i Virgili, Department of Computer Engineering and Mathematics, CYBERCAT Center for Cybersecurity Research of Catalonia, UNESCO Chair in Data Privacy, Av. <PERSON><PERSON>sos Catalans 26, E-43007, Tarragona, Catalonia"}], "References": [{"Title": "Achieving security and privacy in federated learning systems: Survey, research challenges and future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104468", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 105142152, "Title": "In memory of <PERSON>", "Abstract": "", "Keywords": "", "DOI": "10.1002/aaai.12072", "PubYear": 2022, "Volume": "43", "Issue": "4", "JournalId": 18861, "JournalTitle": "AI Magazine", "ISSN": "0738-4602", "EISSN": "0738-4602", "Authors": [], "References": []}, {"ArticleId": 105142198, "Title": "A reliability and truth-aware based online digital data auction mechanism for cybersecurity in MCS", "Abstract": "Mobile Crowd Sensing (MCS) platform recruits participants to sense and report data, thus to build and provide services to consumers, which is a promising computing paradigm. However, malicious attacks based on false data may damage network security, and some low-trust participants may not complete tasks or submit false data since it costs resources to sense data. Both these behaviors do great harm to cybersecurity and reduce the benefit of the platform. Even if many incentive mechanisms have been proposed, there are three critical properties that are not well considered, i.e., truth-reduction, reliability-zero and time-discounting properties, which correspond to the effects of malicious attacks and the uncertain behaviors of participants. In this paper, we take these properties into account and propose a Time, Reliability and Truth-aware Online Auction (TRT-OA) mechanism to ensure cybersecurity and maximize the benefit of the platform, introducing a function T ̃ and a RT coefficient to select more secure and profitable participants. We prove that TRT-OA mechanism achieves computational efficiency, budget feasibility, truthfulness, individual rationality and strategy-proofness. By comparing it with OMG and TDMC, we show that the benefit of TRT-OA mechanism increases by 44.06%.", "Keywords": "", "DOI": "10.1016/j.future.2022.11.028", "PubYear": 2023, "Volume": "141", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, 410083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, 410083, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Mathematics, Sul Ross State University, Alpine, TX 79830, USA;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering of the Hunan University of Science and Technology, Xiangtan, 411201, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, 410083, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Computer Science, Fuzhou University, Fuzhou 350116, China;Center for AI Research (CAIR), University of Agder(UiA), Grimstad, Norway"}], "References": [{"Title": "Preserving adjustable path privacy for task acquisition in Mobile Crowdsensing Systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "527", "Issue": "", "Page": "602", "JournalTitle": "Information Sciences"}, {"Title": "Task allocation algorithm and optimization model on edge collaboration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "101778", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Adversarial attacks on deep-learning-based radar range profile target recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "531", "Issue": "", "Page": "159", "JournalTitle": "Information Sciences"}, {"Title": "Worker recruitment with cost and time constraints in Mobile Crowd Sensing", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "819", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Analyzing host security using D‐S evidence theory and multisource information fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "2", "Page": "1053", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "ImpSuic: A quality updating rule in mixing coins with maximum utilities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "3", "Page": "1182", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Price Learning-based Incentive Mechanism for Mobile Crowd Sensing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}, {"Title": "A privacy-protected intelligent crowdsourcing application of IoT based on the reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "56", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": *********, "Title": "Editorial: Deep neural networks with cloud computing", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.neucom.2022.12.001", "PubYear": 2023, "Volume": "521", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering, Computing and Mathematical Sciences, Curtin University, Australia"}, {"AuthorId": 2, "Name": "Bilal Abu-Salih", "Affiliation": "<PERSON> School of Information Technology, The University of Jordan, Amman, Jordan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "VIS2KNOW Lab, Department of Applied Artificial Intelligence, School of Convergence, College of Computing and Informatics, Sungkyunkwan University, Seoul, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Computational Science and Mathematical Modelling, Coventry University, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software and Electrical Engineering, Swinburne University of Technology, Australia"}], "References": [{"Title": "Special issue on soft computing for edge-driven applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "23", "Page": "12867", "JournalTitle": "Soft Computing"}]}, {"ArticleId": *********, "Title": "PyfastSPM: A Python package to convert 1D FastSPM data streams into publication quality movies", "Abstract": "Since the invention of scanning probe microscopy, researchers have desired to use this technique to monitor sub-second surface dynamics with atomic spatial resolution. A recently presented add-on electronics module enables the speed-up of existing, conventional scanning probe microscopes without any modification of the actual instrument. The resulting one-dimensional (1D) data stream, recorded while the tip oscillates in a sinusoidal motion, has to be reconstructed into a layered rectangular matrix in order to visualize the movie. The Python-based pyfastspm package performs this conversion, while also correcting for sample tilt, noise frequencies, piezo creep, and thermal drift. Quick automatic conversion even of considerable batches of data is achieved by efficient algorithms that bundle time-expensive steps, such as interpolation based on Delaunay triangulation.", "Keywords": "Movie-rate scanning probe microscopy ; Creep correction ; Drift correction", "DOI": "10.1016/j.softx.2022.101269", "PubYear": 2023, "Volume": "21", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Chair of Physical Chemistry, Department of Chemistry & Catalysis Research Center, School of Natural Sciences, Technical University of Munich, D-85748 Garching, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Chair of Physical Chemistry, Department of Chemistry & Catalysis Research Center, School of Natural Sciences, Technical University of Munich, D-85748 Garching, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Chair of Physical Chemistry, Department of Chemistry & Catalysis Research Center, School of Natural Sciences, Technical University of Munich, D-85748 Garching, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Chair of Physical Chemistry, Department of Chemistry & Catalysis Research Center, School of Natural Sciences, Technical University of Munich, D-85748 Garching, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Chair of Physical Chemistry, Department of Chemistry & Catalysis Research Center, School of Natural Sciences, Technical University of Munich, D-85748 Garching, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "CNR-IOM Laboratorio TASC, S.S. 14 km 163.5, Basovizza, I-34149 Trieste, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "CNR-IOM Laboratorio TASC, S.S. 14 km 163.5, Basovizza, I-34149 Trieste, Italy;Elettra-Sincrotrone Trieste, S.S. 14 km 163.5, Basovizza, I-34149 Trieste, Italy"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Functional Nanomaterials Group, Department of Chemistry & Catalysis Research Center, School of Natural Sciences, Technical University of Munich, D-85748 Garching, Germany;Corresponding author"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Chair of Physical Chemistry, Department of Chemistry & Catalysis Research Center, School of Natural Sciences, Technical University of Munich, D-85748 Garching, Germany"}], "References": []}, {"ArticleId": *********, "Title": "Integrated consensus genetic map and genomic scaffold re-ordering of oil palm (Elaeis guineensis) genome", "Abstract": "A high-quality reference genome is an important resource that can help decipher the genetic basis of traits in combination with linkage or association analyses. The publicly available oil palm draft genome sequence of AVROS pisifera (EG5) accounts for 1.535 Gb of the 1.8 Gb oil palm genome. However, the assemblies are fragmented, and the earlier assembly only had 43% of the sequences placed on pseudo-chromosomes. By integrating a number of SNP and SSR-based genetic maps, a consensus map (AM_EG5.1), comprising of 828.243 Mb genomic scaffolds anchored to 16 pseudo-chromosomes, was generated. This accounted for 54% of the genome assembly, which is a significant improvement to the original assembly. The total length of N50 scaffolds anchored to the pseudo-chromosomes increased by ∼18% compared to the previous assembly. A total of 139 quantitative trait loci for agronomically important quantitative traits, sourced from literature, were successfully mapped on the new pseudo-chromosomes. The improved assembly could also be used as a reference to identify potential errors in placement of specific markers in the linkage groups of the genetic maps used to assemble the consensus map. The 3422 unique markers from five genetic maps, anchored to the pseudo-chromosomes of AM_EG5.1, are an important resource that can be used preferentially to either construct new maps or fill gaps in existing genetic maps. Synteny analysis further revealed that the AM_EG5.1 had high collinearity with the date palm genome cultivar &#x27;Barhee BC4&#x27; and shared most of its segmental duplications. This improved chromosomal-level genome is a valuable resource for genetic research in oil palm.", "Keywords": "African oil palm ; Chromosomal-level assembly ; Genome ; Genetic map ; Physical map ; Quantitative trait loci (QTL)", "DOI": "10.1016/j.compbiolchem.2022.107801", "PubYear": 2023, "Volume": "102", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Malaysian Palm Oil Board, 6 Persiaran Institusi, Bandar <PERSON>u <PERSON>, 43000 Kajang, Selangor, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Malaysian Palm Oil Board, 6 Persiaran Institusi, Bandar <PERSON>u <PERSON>, 43000 Kajang, Selangor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Malaysian Palm Oil Board, 6 Persiaran Institusi, Bandar <PERSON>u <PERSON>, 43000 Kajang, Selangor, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Malaysian Palm Oil Board, 6 Persiaran Institusi, Bandar <PERSON>u <PERSON>, 43000 Kajang, Selangor, Malaysia"}, {"AuthorId": 5, "Name": "Ngoot-<PERSON>", "Affiliation": "Malaysian Palm Oil Board, 6 Persiaran Institusi, Bandar <PERSON>u <PERSON>, 43000 Kajang, Selangor, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Malaysian Palm Oil Board, 6 Persiaran Institusi, Bandar <PERSON>u <PERSON>, 43000 Kajang, Selangor, Malaysia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Malaysian Palm Oil Board, 6 Persiaran Institusi, <PERSON><PERSON>, 43000 Kajang, Selangor, Malaysia;Corresponding author"}], "References": []}, {"ArticleId": 105142456, "Title": "A Soft Growing Robot Using Hyperelastic Material", "Abstract": "<p>Soft growing robots have received considerable attention because of their unique locomotion. However, the use of a single material for their manufacturing causes numerous problems, because each part of the robot requires different characteristics. This study attempts to solve this fundamental problem from a material perspective, rather than integrating independent solutions. A hyperelastic material is proposed for building a soft growing robot. Several problems associated with conventional soft growing robots are addressed using a hyperelastic material that offers the required properties for each part of the soft growing robot, while maintaining its intrinsic advantages. Two unique features of hyperelastic materials, bulging and shape-locking, are introduced. The advantages that these two features offer to soft growing robots are investigated and analyzed. A soft growing robot, utilizing these features, can easily achieve shape locking, small tail tension, easy retraction, uniform circular inner channel, and wrinkle-free curve formation, without additional hardware. The initial concept of the growing and steering mechanisms of a hyperelastic soft growing robot is proposed and demonstrated using a prototype.</p>", "Keywords": "bulging;hyperelastic material;shape locking;soft growing robots", "DOI": "10.1002/aisy.202200264", "PubYear": 2023, "Volume": "5", "Issue": "2", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Robotics Program Korea Advanced Institute of Science and Technology (KAIST)  34141 Daejeon Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering Korea Advanced Institute of Science and Technology (KAIST)  34141 Daejeon Republic of Korea"}], "References": [{"Title": "Robust navigation of a soft growing robot by exploiting contact with the environment", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "14", "Page": "1724", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "Design and Development of a Growing Pneumatic Soft Robot", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "2", "Page": "521", "JournalTitle": "Soft Robotics"}, {"Title": "Controlling subterranean forces enables a fast, steerable, burrowing soft robot", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "55", "Page": "eabe2922", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 105142487, "Title": "Barriers of digital transaction in rural areas: an interpretive structural modelling and MICMAC analysis", "Abstract": "Digital transaction is the indispensable practice for all the household as well as commercial units in this era of digitalisation that is affected by pandemic too. Digital transaction stills a difficult task in the several rural areas. This study aims to identify the key barriers of digital transaction in rural areas and focus to explain the contextual interrelationship among these barriers. This study primarily focusing over the identification of key barriers of digital transactions in the rural areas from the extensive literature review. Secondly, it emphasises over the establishment of a hierarchical model using interpretive structural modelling (ISM). MICMAC analysis is further used to segregate these key barriers. Result revealed twelve key barriers of digital transactions in rural areas that further established a six-level hierarchical interpretive structural model. This paper provides the insights for the researchers, academicians, industry practitioners and policymakers to fill the theoretical and implication gaps. Copyright © 2023 Inderscience Enterprises Ltd.", "Keywords": "barriers; digital transaction; interpretive structural modelling; ISM; MICMAC analysis; rural area", "DOI": "10.1504/IJEB.2023.127541", "PubYear": 2023, "Volume": "18", "Issue": "1", "JournalId": 8392, "JournalTitle": "International Journal of Electronic Business", "ISSN": "1470-6067", "EISSN": "1741-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management Studies, Graphic Era (Deemed to be University), 566/6, Bell Road, Society Area, Clement Town, Uttarakhand, Dehradun, 248002, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IMS Unison University, Dehradun, 248009, India"}], "References": []}, {"ArticleId": 105142515, "Title": "Automatic 1p/19q co-deletion identification of gliomas by MRI using deep learning U-net network", "Abstract": "The chromosome 1p/19q co-deletion which is a hallmark of oligodendroglioma plays more crucial role in glioma classification especially in “The 2021 WHO Classification of Tumors of the Central Nervous System”. A more effective non-invasive method to distinguish 1p/19q co-deletion tumor from all gliomas can facilitate the strategy selection of pathologists, physicians, and surgeons. Preoperative MRI, including T1, T2, enhanced T1 and T2-FLAIR, from 61 glioma patients of our facility were reviewed. Data from 89 gliomas subjects from The Cancer Imaging Archive were recruited. Following the preprocessing, we improved the U-net and ResNet152 based on the MRI data of different modalities to determine the 1p/19q codeletion from overall gliomas. The different models were compared. The UMAP result implies that two different data share some similar traits. All the sensitivity, specificity and accuracy of U-net are higher than that of the ResNet152. The test accuracy with four modalities outperforms others significantly, reaching 92.156%. We introduce an efficient pipeline with U-net network for the identification of 1p/19q genotype status. The study implements one step judgement with multi-modal sequence MRI images. It takes a further step to suggest that machine learning can render more possibilities to conventional MRI.", "Keywords": "Deep learning ; U-net ; Gliomas ; 1p/19q co-deletion ; Molecular imaging", "DOI": "10.1016/j.compeleceng.2022.108482", "PubYear": 2023, "Volume": "105", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Neurosurgery, The First Medical Center, Chinese PLA General Hospital, Beijing, China;Department of Neurosurgery, Pingjin Hospital, Characteristic Medical Center of Chinese People's Armed Police Force, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Henan University, Henan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Surgery, First Teaching Hospital of Tianjin University of Traditional Chinese Medicine, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, The First Medical Center, Chinese PLA General Hospital, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer and Information Engineering, Henan University, Henan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, The First Medical Center, Chinese PLA General Hospital, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, The First Medical Center, Chinese PLA General Hospital, Beijing, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, Beijing Tsinghua Changgung Hospital, Tsinghua University, Beijing, China;Corresponding authors"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON> Chen", "Affiliation": "School of Computer and Information Engineering, Henan University, Henan, China;Corresponding authors"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, The First Medical Center, Chinese PLA General Hospital, Beijing, China;Corresponding authors"}], "References": []}, {"ArticleId": 105142537, "Title": "Issue Information", "Abstract": "", "Keywords": "", "DOI": "10.1111/isj.12390", "PubYear": 2023, "Volume": "33", "Issue": "1", "JournalId": 7047, "JournalTitle": "Information Systems Journal", "ISSN": "1350-1917", "EISSN": "1365-2575", "Authors": [], "References": []}, {"ArticleId": 105142616, "Title": "GWO-based Modeling of an Unstable Transport System", "Abstract": "The goal of this paper is to obtain optimal models of an unstable transport system, which is a nonlinear process represented by the two-wheeled unstable transport system. An optimization problem is defined in order to ideally minimize and practically reduce the differences of the outputs of the real-time laboratory equipment with respect to the outputs of the nonlinear model. The parameters of the nonlinear models are optimally tuned using a recent metaheuristic optimization algorithm, namely the Grey Wolf Optimizer, which solves three optimization problems. A comparison of the responses of the real-time laboratory equipment and the derived optimal nonlinear models is carried out in various simulation scenarios, which are discussed and analyzed.", "Keywords": "Grey Wolf Optimizer ; modeling ; optimization ; UnTrans system", "DOI": "10.1016/j.procs.2022.11.166", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Cristiana-Bogdana Gale-Cazan", "Affiliation": "Politehnica University of Timişoara , Dept. Automation and Applied Informatics , Bd. V. Parvan 2 , 300223 Timişoara , Romania"}, {"AuthorId": 2, "Name": "Claudia-<PERSON><PERSON>", "Affiliation": "Politehnica University of Timişoara , Dept. Automation and Applied Informatics , Bd. V. Parvan 2 , 300223 Timişoara , Romania"}, {"AuthorId": 3, "Name": "Radu<PERSON><PERSON>", "Affiliation": "Politehnica University of Timişoara , Dept. Automation and Applied Informatics , Bd. V. Parvan 2 , 300223 Timişoara , Romania;Center for Fundamental and Advanced Technical Research , Romanian Academy – Timisoara Branch , Bd. <PERSON><PERSON> 24 , 300223 Timişoara , Romania"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "Affiliation": "Politehnica University of Timişoara , Dept. Automation and Applied Informatics , Bd. V. Parvan 2 , 300223 Timişoara , Romania"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Ottawa , School of Electrical Engineering and Computer Science , 800 King Eduard , Ottawa , Ontario , K1N 6N5 Canada"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Politehnica University of Timişoara , Dept. Automation and Applied Informatics , Bd. V. Parvan 2 , 300223 Timişoara , Romania"}], "References": [{"Title": "Grey Wolf Optimizer-Based Approaches to Path Planning and Fuzzy Logic-based Tracking Control for Mobile Robots", "Authors": "Ra<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "3", "Page": "", "JournalTitle": "International Journal of Computers Communications & Control"}, {"Title": "Slime Mould Algorithm-Based Tuning of Cost-Effective Fuzzy Controllers for Servo Systems", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>-<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1042", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "GWO-Based Optimal Tuning of Type-1 and Type-2 Fuzzy Controllers for Electromagnetic Actuated Clutch Systems", "Authors": "Claudia-<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "4", "Page": "189", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 105142628, "Title": "Living with offspring surely brings happiness to the elderly?—the heterogeneity in the effect of living arrangements on the life satisfaction of the Chinese elderly", "Abstract": "This paper studies the effect of living arrangements on the life satisfaction of the Chinese elderly. It first points out that the effect is significant, and living with offspring can improve the life satisfaction of the elderly on average. Then, by employing a machine learning algorithm—causal forests, it spotlights an exploration of the heterogeneity in this effect. The effect heterogeneity is expressed by the difference of individual treatment effect (ITE) where an individual is indicated by the characteristics of covariates. Especially, the elderly with the following characteristics, significantly benefit from living with their offspring: female, rural residential identity, relatively older, low education level, bad physical and mobility condition, seldom participating in social activities, no spouse accompanying, poor financial conditions, a small number of surviving children, much emotional interaction with their children, small financial support to children, much daily care from children. These results demonstrate empirical relationships between living arrangements, individual and family characteristics, and life satisfaction of the Chinese elderly, and provide a valuable reference for families and society to develop appropriate eldercare plans for the happy lives of the elderly.", "Keywords": "living arrangements ; effect ; heterogeneity ; individual treatment effect (ITE) ; causal forests", "DOI": "10.1016/j.procs.2022.11.186", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Capital University of Economics and Business , 121 Zhangjialukou, Fengtai District, Beijing, 100070, China"}], "References": []}, {"ArticleId": 105142629, "Title": "Detecting Financial Fraud using Two Types of Benford Factors: Evidence from China", "Abstract": "Financial fraud of listed companies can lead to anomalies in the distribution of financial data, which can be detected by Benford&#x27;s Law. This study takes financial data of Chinese listed companies to construct two types of Benford factors for detecting financial fraud. The empirical results show that as the deviation of financial data distribution from Benford&#x27;s law increases, the probability of financial fraud increases significantly. Furthermore, compared with rustically using traditional financial indicators, the addition of the Benford factors can effectively reduce the Type I or Type II error using the logistic regression model. Finally, we show that the identification indicators selected in this study contributes to the detection of financial fraud with the help of digital distribution laws.", "Keywords": "Financial fraud ; <PERSON><PERSON>'s law ; Logistic regression ; China Market", "DOI": "10.1016/j.procs.2022.11.225", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Central University of Finance and Economics, Haidian District, Beijing, 100081, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Shandong College of Economics and Business, 2799 QingNian Road , WeiCheng District, WeiFang, ShanDong , 261011 , China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Central University of Finance and Economics, Haidian District, Beijing, 100081, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Central University of Finance and Economics, Haidian District, Beijing, 100081, China"}], "References": []}, {"ArticleId": 105142630, "Title": "Dynamic Correlation Analysis on the Financial Institutions in Shanghai, Shenzhen, and Hong Kong Stock Markets Based on Complex Network", "Abstract": "With the launch of the Shanghai-Hong Kong and the Shenzhen-Hong Kong Stock Connect programs, the correlations among the three stock markets have been more complex. Therefore, this paper applies the Minimum Spanning Tree and Sliding Window methods to analyze the complex correlations of the 38 financial institutions in the three markets. Based on closing prices ranging from January 1, 2011 to December 31, 2021, three conclusions are obtained. The top 20 institutions in the network were mainly banking institutions from 2011 to 2013, while the node strength of non-banking financial institutions tended to increase from 2014 to 2021. The network shows different structures over time, even the changes in the three correlation coefficients generally show the same trend. The degree correlation between any two layers is positive, but the similarity shows different characteristics. These results would help to enrich multi-layer network theory and reveal the evolutionary characteristics of the correlation between financial institutions.", "Keywords": "Complex network ; Stock market ; Financial industry ; Correlation ; Dynamic evolution", "DOI": "10.1016/j.procs.2022.11.236", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Engineering, Capital University of Economics and Business, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Engineering, Capital University of Economics and Business, Beijing, China"}], "References": []}, {"ArticleId": 105142674, "Title": "Benchmarking large-scale subset selection in evolutionary multi-objective optimization", "Abstract": "In the field of evolutionary multi-objective optimization (EMO), the standard practice is to present the final population of an EMO algorithm as the output. However, it has been shown that the final population often includes solutions which are dominated by other solutions generated and discarded in previous generations. Recently, a novel EMO framework has been developed to solve this issue by storing all the non-dominated solutions generated during the evolution in an archive, and selecting a subset of solutions from the archive as the output. The key component of this framework is the subset selection from the archive, which typically stores a large number of candidate solutions. However, most relevant studies have focused on small candidate solution sets for environmental selection. There is no benchmark test suite for large-scale subset selection. This study aims to fill this research gap by proposing a benchmark test suite for large-scale subset selection, and providing a comparison between several representative subset selection algorithms using the proposed test suite. The proposed test suite together with the benchmarking studies provides a baseline for researchers to understand, use, compare, and develop large-scale subset selection algorithms in the EMO field.", "Keywords": "", "DOI": "10.1016/j.ins.2022.11.155", "PubYear": 2023, "Volume": "622", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Brain-inspired Intelligent Computation, Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Shu", "Affiliation": "Guangdong Provincial Key Laboratory of Brain-inspired Intelligent Computation, Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Brain-inspired Intelligent Computation, Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Brain-inspired Intelligent Computation, Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Brain-inspired Intelligent Computation, Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China"}], "References": [{"Title": "Quality Evaluation of Solution Sets in Multiobjective Optimisation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Graph Structured Sparse Subset Selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "518", "Issue": "", "Page": "71", "JournalTitle": "Information Sciences"}, {"Title": "Opinion subset selection via submodular maximization", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "283", "JournalTitle": "Information Sciences"}, {"Title": "Exact hypervolume subset selection through incremental computations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "136", "Issue": "", "Page": "105471", "JournalTitle": "Computers & Operations Research"}, {"Title": "A dual-population algorithm based on alternative evolution and degeneration for solving constrained multi-objective optimization problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "579", "Issue": "", "Page": "89", "JournalTitle": "Information Sciences"}, {"Title": "A novel adaptive weight algorithm based on decomposition and two-part update strategy for many-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "615", "Issue": "", "Page": "323", "JournalTitle": "Information Sciences"}, {"Title": "An effective and efficient evolutionary algorithm for many-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "617", "Issue": "", "Page": "211", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105142684, "Title": "An exact solution method and a genetic algorithm-based approach for the unit commitment problem in conventional power generation systems", "Abstract": "The unit commitment problem (UCP) is one of the fundamental problems in power systems planning and operations that comprises two decisions: commitment and dispatching of conventional generating units. The objective is to minimize total operating costs -fuel and start-up costs- while satisfying several operational and technical constraints. The UCP is characterized as a highly constrained mixed-integer nonlinear NP-hard problem, which makes it difficult to develop a rigorous optimization method for real-size systems. Hence, we devise an efficient mixed-integer quadratic programming formulation as an exact method with brand-new linear representations for each of the three crucial constraint sets, namely minimum uptime/downtime, start-up and ramp-up/down constraints. Furthermore, to be able to solve a large-scale UCP and to deal with its complexities, we propose a Genetic Algorithm-based matheuristic approach that can provide optimal/near-optimal solutions quickly, thanks to its unique binary-integer coding scheme and several problem-specific operators. During the genetic evolution, commitment and dispatching schedules are determined by combining genetic operations and the Improved Lambda Iteration Method reinforced by the incorporation of average fuel cost optimization and ramp rate limits. The final dispatching schedule is then determined via a start-up adjustment procedure and an efficient quadratic programming model. The computational experiments show that both proposed exact approach and GA-based matheuristic can provide satisfactorily good schedules even for large-scale conventional power systems in quite a reasonable computation time.", "Keywords": "", "DOI": "10.1016/j.cie.2022.108876", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Middle East Technical University, 06800 Çankaya, Ankara, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Middle East Technical University, 06800 Çankaya, Ankara, Turkey"}], "References": []}, {"ArticleId": 105142724, "Title": "Design of 15 level reduced switches inverter topology using multicarrier sinusoidal pulse width modulation", "Abstract": "In this proposed paper, multicarrier sinusoidal pulse width modulation (M-SPWM) method is implemented for design of 15 level reduced switches inverter topology. This inverter topology generates 15 level output-voltage with suitablelswitching pulse production using M-SPWM and altered level of voltages are attained with distinction of modulationlindex. The split inductor is used to diminish the harmoniclcontent and flatted output current. This type of system which contains different range of different range of voltage supplies. As a result, this inverter reduces the difficulty in gating time calculation and there is no neutral point fluctuation issue. This paper illuminates the modes of switching and minimization of stress in voltage and harmonic diminution are examined. The grades of the projected multilevel inverter (MLI) system are verified using Matlab/Simulink and dsPIC controller respectively.", "Keywords": "coupled inductor;DC-AC converter;M-SPWM;multilevel inverter;switch level ratio;total harmonic distortion", "DOI": "10.12928/telkomnika.v21i1.24263", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "SRM Institute of Science and Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "SRM Institute of Science and Technology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "SRM Institute of Science and Technology"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SRM Institute of Science and Technology"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "SRM Institute of Science and Technology"}], "References": []}, {"ArticleId": 105142725, "Title": "Review on cyber-security for optimized and smart irrigation systems", "Abstract": "It is well known that the resources in agriculture are considered the most important factors for success. Therefore, numerous researchers are involved in the field of managing these resources, particularly water and consumed power. Moreover, the security side of these resources is considered, particularly the cyber-attacks. In this project, an optimized resource management method is proposed for allocating the available resources in a smart on-demand way. The proposed method is applied for dripped and sprinkler irrigation systems for managing the available water and generated power. In addition, an optimization method is utilized to obtain reliable solutions for managing the adopted resources. This method adopts a cyber security algorithm for preventing any possible attack. Wireless sensor network (WSN) is used as a reading source, in which the underlying area is covered well, since using sensors in irrigation systems is cost-effective that ensures on-demand irrigation process to save water and power resources. This network is supported by the fault tolerance method to increase availability.", "Keywords": "irrigation systems;smart techniques;optimization;WSN", "DOI": "10.12928/telkomnika.v21i1.24234", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "University of Technology-Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Technology-Iraq"}], "References": []}, {"ArticleId": 105142754, "Title": "An Integrated Dual Attention with Convolutional LSTM for Short-Term Temperature Forecasting", "Abstract": "", "Keywords": "", "DOI": "10.1080/01969722.2022.2122010", "PubYear": 2024, "Volume": "55", "Issue": "2", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>o", "Affiliation": "<PERSON><PERSON> Dau Mot University, Binh Duong, Vietnam"}], "References": [{"Title": "Multivariate Time Series Clustering and its Application in Industrial Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "3", "Page": "315", "JournalTitle": "Cybernetics and Systems"}, {"Title": "Short-term temperature forecasts using a convolutional neural network — An application to different weather stations in Germany", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "", "Page": "100007", "JournalTitle": "Machine Learning with Applications"}, {"Title": "Deep Learning for Time Series Forecasting: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "3", "JournalTitle": "Big Data"}, {"Title": "K-Means Clustering Based High Order Weighted Probabilistic Fuzzy Time Series Forecasting Method", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "54", "Issue": "2", "Page": "197", "JournalTitle": "Cybernetics and Systems"}, {"Title": "A Novel Semantic-Enhanced Text Graph Representation Learning Approach through Transformer Paradigm", "Authors": "<PERSON><PERSON>o", "PubYear": 2023, "Volume": "54", "Issue": "4", "Page": "499", "JournalTitle": "Cybernetics and Systems"}]}, {"ArticleId": 105142792, "Title": "Railcar reallocation optimization on water-rail network under uncertain busyness", "Abstract": "Water-rail intermodal transportation can reduce cargo losses and transportation transferring costs. However, the imbalance between the capacity of the scheduled railway network and the large container freight demand greatly reduces operational efficiency. To minimize the total transportation cost and relocation cost, a railcar reallocation stochastic optimization model is formulated to deal with uncertain congestion in the railway network. To capture the uncertain busyness and queuing pattern, a hypercube spatial queue model is embedded in the optimization model by estimating the expected queue length and waiting time. To solve the proposed nonlinear nonconcave stochastic model, an approximate hypercube based iterative algorithm is proposed. A real-world case study is presented to show the effectiveness and efficiency of the proposed method. The proposed model outperforms the comparable deterministic model in the objective value. Sensitivity analyses on the ratio of the unit waiting cost and the unit travel cost for empty cars, and the total number of freight cars show the robustness of the proposed method.", "Keywords": "Intermodal transport ; Train reallocation ; Hypercube spatial queuing ; Busyness estimation", "DOI": "10.1016/j.aei.2022.101828", "PubYear": 2023, "Volume": "55", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "Yun <PERSON>", "Affiliation": "College of Transportation Engineering, Dalian Maritime University, 1 Linghai Rd, Dalian 116026, China"}, {"AuthorId": 2, "Name": "Yu <PERSON>", "Affiliation": "College of Transportation Engineering, Dalian Maritime University, 1 Linghai Rd, Dalian 116026, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Transportation Engineering, Dalian Maritime University, 1 Linghai Rd, Dalian 116026, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Transportation Engineering, Dalian Maritime University, 1 Linghai Rd, Dalian 116026, China;Corresponding author"}], "References": [{"Title": "A facility location and equipment emplacement technique model with expected coverage for the location of fire stations in the Concepción province, Chile", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "106522", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Bi-level programming enabled design of an intelligent maritime search and rescue system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "", "Page": "101194", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Container storage space assignment problem in two terminals with the consideration of yard sharing", "Authors": "<PERSON><PERSON> Hu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101224", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Knowledge-based integrated product design framework towards sustainable low-carbon manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "101258", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 105142895, "Title": "China's Crude oil futures forecasting with search engine data", "Abstract": "In this paper, we used the Baidu feed index to quantify the valuable information from the search engine, and proposed a China&#x27;s crude oil futures price forecasting model with feed index. The empirical analysis confirms that the feed index, which characterizes the investor attention of participants in China&#x27;s crude oil futures market, provides valuable information for internet concerns and investor sentiment, and has significant impact on China&#x27;s crude oil futures price forecasting. We found that the time lag between the search query results and the crude oil futures price changes, which mostly lies within the shorter time range. The forecasting model with the search engine data would produce forecasts with superior performance.", "Keywords": "ARIMAX model ; Baidu feed Index ; Crude Oil Futures Price Forecasting", "DOI": "10.1016/j.procs.2022.11.266", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Tourism, Hunan Normal University, Changsha, 410081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Tourism, Hunan Normal University, Changsha, 410081, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Tourism Management, Macao Institute for Tourism Studies, Macao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Tourism, Hunan Normal University, Changsha, 410081, China"}], "References": [{"Title": "Forecasting crude oil price with a new hybrid approach and multi-source data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104217", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 105142896, "Title": "The reaction of energy markets to regional conflict: evidence from event study approach", "Abstract": "This article explores the impact of the Russia-Ukraine conflict on energy markets by using the event study approach. The analysis of the average abnormal returns (AARs) and cumulative average abnormal returns (CAARs) before and after the launch of the ‘special military operation’ by Russian military forces on 24 February 2022 reveals a strong positive impact of this military action on clean energy and conventional energy markets, especially on the clean energy market. All clean energy indices are significant on the event day, while only half of the conventional energy indices respond significantly. Moreover, some conventional energy indices, such as DJ Oil &amp; Gas, have no significant response to the event.", "Keywords": "Russia-Ukraine conflict ; energy markets reaction ; event study", "DOI": "10.1016/j.procs.2022.11.262", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Finance, Jiangxi University of Finance and Economics, Nanchang, 330013, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Finance, Jiangxi University of Finance and Economics, Nanchang, 330013, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Jiangxi Financial Development Group Co., Ltd, Nanchang, 330013, China"}], "References": []}, {"ArticleId": 105142897, "Title": "A new application for multi criteria decision making processes in e-government scenarios", "Abstract": "Most of the group decision making methods used in e-government scenarios deal not only with a high number of alternatives, but also a large number of criteria. Sometimes, criteria are even the most important think to manage. In such a way, we propose the use of fuzzy ontologies in order to tackle this problem, dealing with a high number of alternatives and criteria. Our application combines the different criteria using a clustering method and filter the alternatives by using the appropriate criteria in each case making the decision process easier for the experts. Finally, the application uses a consensus reaching mechanism that collect the knowledge stored in the fuzzy ontology.", "Keywords": "Multi criteria decision making ; Fuzzy Ontologies ; E-government Decision making", "DOI": "10.1016/j.procs.2022.11.282", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Sciences and Artificial Intelligence, University of Granada, 18071 Granada, Spain"}, {"AuthorId": 2, "Name": "F.J. Cabrerizo", "Affiliation": "Department of Computer Sciences and Artificial Intelligence, University of Granada, 18071 Granada, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": "Department of Computer Sciences and Artificial Intelligence, University of Granada, 18071 Granada, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": "Department of Quantitative Methods in Economic and Business, University of Granada, 18071 Granada, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics and Operational Research, University of Granada, 18071 Granada, Spain"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences and Artificial Intelligence, University of Granada, 18071 Granada, Spain"}], "References": [{"Title": "A novel multi-criteria group decision-making method for heterogeneous and dynamic contexts using multi-granular fuzzy linguistic modelling and consensus measures", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "240", "JournalTitle": "Information Fusion"}, {"Title": "Clustering-based method for large group decision making with hesitant fuzzy linguistic information: Integrating correlation and consensus", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "105973", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multiple criteria group decision making based on group satisfaction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "518", "Issue": "", "Page": "309", "JournalTitle": "Information Sciences"}, {"Title": "A dynamic group decision making process for high number of alternatives using hesitant Fuzzy Ontologies and sentiment analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; F.<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105657", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Social network community analysis based large-scale group decision making approach with incomplete fuzzy preference relations", "Authors": "<PERSON><PERSON> Chu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "98", "JournalTitle": "Information Fusion"}, {"Title": "Multi-expert multi-criteria decision making based on the likelihoods of interval type-2 trapezoidal fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "12", "Page": "2719", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Bid Evaluation for Major Construction Projects Under Large-Scale Group Decision-Making Environment and Characterized Expertise Levels", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "1227", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Minimum cost consensus modelling under various linear uncertain-constrained scenarios", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "1", "JournalTitle": "Information Fusion"}, {"Title": "A cyclic dynamic trust-based consensus model for large-scale group decision making with probabilistic linguistic information", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106937", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 105142907, "Title": "A Brief Survey for Fake News Detection via Deep Learning Models", "Abstract": "Social networks have become indispensable in people&#x27;s lives. Despite the conveniences brought by social networks, the fake news on those online platforms also induces negative impacts and losses for users. With the development of deep learning technologies, detecting fake news in a data-driven manner has attracted great attention. In this paper, we give a brief survey that discusses the recent development of deep learning methods in fake news detection. Compared with previous surveys, we focus on the different data structures instead of the models they used to process those data. We give a new taxonomy that categorizes current models into the following three parts: models that formulate fake news detection as text classification, models that formulate fake news detection as graph classification, and models that formulate fake news detection as hybrid classification. The advantages and drawbacks of those methods are also discussed.", "Keywords": "Fake news detection ; Deep learning ; Text classification ; Graph classification ; Multi-modal data", "DOI": "10.1016/j.procs.2022.11.314", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China;Beijing Institute of Artificial Intelligence, Beijing University of Technology, Beijing, 100124, China"}], "References": [{"Title": "Automating fake news detection system using multi-level voting model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "12", "Page": "9049", "JournalTitle": "Soft Computing"}, {"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "FakeBERT: Fake news detection in social media with a BERT-based deep learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "8", "Page": "11765", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Fake news detection: A hybrid CNN-RNN based deep learning approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100007", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "A transformer-based architecture for fake news classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Knowledge augmented transformer for adversarial multidomain multiclassification multimodal fake news detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "462", "Issue": "", "Page": "88", "JournalTitle": "Neurocomputing"}, {"Title": "Knowledge graph informed fake news classification via heterogeneous representation ensembles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>-Šiko<PERSON>", "PubYear": 2022, "Volume": "496", "Issue": "", "Page": "208", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 105142908, "Title": "Research on multi factor stock selection model based on LightGBM and Bayesian Optimization", "Abstract": "With the development of the stock market, the number of individual investors has become more and more. Because of emotional and irrational factors, the risk and instability of stock market in China have been greatly increased. Therefore, it is necessary to introduce the idea of quantitative investment into the financial field. In this paper, firstly we use random forest, XGBoost and LightGBM to conduct rolling tests on multiple factors. After parameter adjustment based on Bayesian Optimization, we find that LightGBM-Bayes has the best effect. Finally, this paper uses the multi factor stock selection model based on LightGBM-Bayes to conduct a rolling back test on CSI300 Constituent Stocks, and the back test results are better than the benchmark CSI300 index.", "Keywords": "LightGBM ; Bayesian Optimization ; Multi Factor Model ; Quantitative Investment", "DOI": "10.1016/j.procs.2022.11.301", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Central University of Finance and Economics, School of management science and Engineering, Beijing 102206, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Central University of Finance and Economics, School of management science and Engineering, Beijing 102206, China"}, {"AuthorId": 3, "Name": "Aihua Li", "Affiliation": "Central University of Finance and Economics, School of management science and Engineering, Beijing 102206, China"}], "References": [{"Title": "An integrated framework of deep learning and knowledge graph for prediction of stock price trend: An application in Chinese stock exchange market", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106205", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": *********, "Title": "Arbitrage as a new normal in contemporary financial markets?", "Abstract": "The global financial system is constantly changing, rapidly reacting on the external changes. These changes include evolving macroeconomic policy tools, creation of new regulations in financial markets (affecting both banking institutions, financial markets’ benchmarks, etc.), imposition of legal restrictions of dealing with certain types of financial institutors on the basis of decisions of particular governments, and other developments. All these create grounds for appearance of fragmentation between financial markets and jurisdictions, which, in turn, creates a phenomenon of arbitrage. In the paper certain sources of such price disparities is analysed and illustrated. It becomes evident that the arbitrage might become a new normal for the contemporary financial markets, and that this phenomenon seems to be overlooked and undervalued, while its consequences might be large and long-lasting.", "Keywords": "interest rates parity ; banks ; financial markets ; arbitrage", "DOI": "10.1016/j.procs.2022.11.279", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Higher School of Economics, Myasnitskaya 20, Moscow , Russia"}], "References": []}, {"ArticleId": *********, "Title": "A Rule-Based Coordination Method of Conductive Contradictions for Product Function Innovation", "Abstract": "To coordinate the conductive contradiction for product function innovation, a conduction rule-based coordination method is proposed. The multi-layer network of function-effect-structure basic-element model of design elements is established to make the conduction features clear on the intra- and inter-layer(s), and then, the rules of the conduction between these elements on intra-layer and among the inter layers are established. Finally, the coordination strategy for conductive contradictions is proposed accordingly. In the end of the paper, a cutting table is taken as a case to verify the practicability and effectiveness of the method.", "Keywords": "Function innovation ; multi-layer network ; conduction rules ; coordination strategy", "DOI": "10.1016/j.procs.2022.11.216", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Fangzhi Gui", "Affiliation": "School of Mechenical Engineering, Nanchang Institute of Technology, Nanchang 330029, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechenical Engineering, Nanchang Institute of Technology, Nanchang 330029, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechenical Engineering, Nanchang Institute of Technology, Nanchang 330029, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University of Technology, Hangzhou 310023, PR China"}], "References": []}, {"ArticleId": 105142916, "Title": "Review of graph construction and graph learning in stock price prediction", "Abstract": "Precise prediction of stock prices leads to more profits and more effective risk prevention, which is of great significance to both investors and regulators. Recent years, various kinds of information not directly-relevant with stock prices have received more attention, such as texts, images or connections. These external information has the potential of reflecting or influencing fluctuations, and thus, given the utilization of advanced analyzing techniques, the forecasting performance of stock prices could be promoted substantially. For instance, graph neural network models have expanded into many other disciplines including stock price prediction, and exhibited impressive representation learning ability. However, in stock markets, well-defined graphs are rarely seen and how to formulate the graph structures needed remains a challenging problem. Towards this end, this article presents a comprehensive overview of graph construction and graph learning in stock price prediction, by reviewing the existing studies, summarizing its general paradigm, special cases and proposing possible prospects. Our research not only systematically reveals the feasible ways of constructing graphs in financial markets, but also provides insights for further implementations of graph learning models into stock prediction tasks.", "Keywords": "Stock price prediction ; Graph construction ; Graph learning ; Data structure", "DOI": "10.1016/j.procs.2022.11.240", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Center on Fictitious Economy and Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Research Center on Fictitious Economy and Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Management and Engineering, Capital University of Economics and Business, Beijing 100070, China"}], "References": [{"Title": "An integrated framework of deep learning and knowledge graph for prediction of stock price trend: An application in Chinese stock exchange market", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106205", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel graph convolutional feature based convolutional neural network for stock trend prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "556", "Issue": "", "Page": "67", "JournalTitle": "Information Sciences"}, {"Title": "Relation-aware dynamic attributed graph attention network for stocks recommendation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108119", "JournalTitle": "Pattern Recognition"}, {"Title": "Financial time series forecasting with multi-modality graph neural network", "Authors": "Dawei Cheng; Fangzhou Yang; Sheng <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108218", "JournalTitle": "Pattern Recognition"}, {"Title": "Graph-based stock correlation and prediction for high-frequency trading systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108209", "JournalTitle": "Pattern Recognition"}, {"Title": "Price graphs: Utilizing the structural information of financial time series for stock prediction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "588", "Issue": "", "Page": "405", "JournalTitle": "Information Sciences"}, {"Title": "Chart GCN: Learning chart information with a graph convolutional network for stock movement prediction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "248", "Issue": "", "Page": "108842", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Leveraging enterprise knowledge graph to infer web events’ influences via self-supervised learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "100722", "JournalTitle": "Journal of Web Semantics"}, {"Title": "HGNN: Hierarchical graph neural network for predicting the classification of price-limit-hitting stocks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "783", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105142917, "Title": "The Further Development of Intelligent Knowledge for Wisdom", "Abstract": "This paper introduces some modes from D, I, K to W, M. From different angles we investigate the various process. Especially we utilize the intelligent knowledge to develop the whole process. Finally we design a new medical hall for workshop of meta-synthetic engineering for helping patient to get the appropriate treatment and useful knowledge for health.", "Keywords": "Wisdom Intelligent Knowledge ; DIKWM;Medical Hall for Workshop of Meta-synthetic Engineering", "DOI": "10.1016/j.procs.2022.11.320", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Jifa Gu", "Affiliation": "Academy of Mathematics and Systems Science of Chinese Academy of Sciences, Beijing, 100190, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing, 100190, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing, 100190, China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences, Beijing, 100190, China;MOE Social Science Laboratory of Digital Economic Forecasts and Policy Simulation at UCAS, Beijing, 100190, China"}], "References": []}, {"ArticleId": *********, "Title": "Multi-classification assessment of personal credit risk based on stacking integration", "Abstract": "In the multi-category assessment of personal credit risk, the prediction accuracy of a single classifier is often low, and each category cannot be well distinguished. In this paper, the stacking ensemble algorithm is used to integrate the base classifiers to build a multi-class evaluation model. Through the empirical analysis of the five-class data of an anonymous commercial bank in China, six types of base classifiers are selected to train the data, and the evaluation results of each single classifier is obtained. The four classifiers with the best classification effect are used as the first-layer base classifier in the stacking ensemble, and <PERSON> Forest is selected as the second-layer meta-learner. Compared with the evaluation results of the single-classifier model, the stacking ensemble model has improved in the four indicators of accuracy, precision, recall and F1-score, which verifies the efficiency and effectiveness of the model proposed in this paper.", "Keywords": "personal credit risk ; multi-category assessment ; stacking integration", "DOI": "10.1016/j.procs.2022.11.218", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhu", "Affiliation": "School of Business Administration , Northeastern University , Shenyang 110819 , China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Business Administration , Northeastern University , Shenyang 110819 , China"}, {"AuthorId": 3, "Name": "Gang Li", "Affiliation": "School of Business Administration , Northeastern University , Shenyang 110819 , China"}], "References": [{"Title": "A new hybrid ensemble model with voting-based outlier detection and balanced sampling for credit scoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114744", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Novel hybrid ensemble credit scoring model with stacking-based noise detection and weight assignment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116913", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel XGBoost extension for credit scoring class-imbalanced data combining a generalized extreme value link and a modified focal loss function", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "117233", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 105142923, "Title": "Forecasting hourly electricity demand with nonparametric functional data analysis", "Abstract": "The ultra-short-term electricity demand forecast is very crucial to the operation of power grid. This paper analyses the hourly electricity demand in a functional data framework and uses the functional nonparametric prediction method to forecast the hourly electricity demand of the next day. The results indicate functional nonparametric prediction method performs better than traditional time series model (seasonal-ARMA). This paper expands the application field of functional data analysis. This paper also introduces some future research directions under this topic.", "Keywords": "Functional data ; electricity demand ; nonparametric", "DOI": "10.1016/j.procs.2022.11.195", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Yanhui CHEN", "Affiliation": "Shanghai Maritime University , Shanghai P.R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Maritime University , Shanghai P.R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Maritime University , Shanghai P.R. China"}], "References": []}, {"ArticleId": 105142926, "Title": "AI shapes the future of web conferencing platforms", "Abstract": "In the last couple of years, videoconferencing platforms have become very popular and have captured the attention of researchers and the public, particularly due to the Covid-19 pandemic. Immediately after the pandemic started in the spring of 2020, demand for video conferencing apps has grown exponentially. This demand, and the emergence of new needs, have forced manufacturers to adapt to the new context by improving the services offered and adding new features to existing applications. Many new video conferencing applications have also emerged with this demand. This paper presents a series of statistics on the evolution during the pandemic and the current status of the main video conferencing systems. The different ways in which these systems have integrated artificial intelligence technologies to address different identified problems and user needs are also presented.", "Keywords": "video conferencing ; web conferencing ; AI ; Covid-19", "DOI": "10.1016/j.procs.2022.11.177", "PubYear": 2022, "Volume": "214", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitatea Valahia din Targoviste, 13 Aleea Sinaia Street , Targoviste , 130004 , Romania"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitatea Valahia din Targoviste, 13 Aleea Sinaia Street , Targoviste , 130004 , Romania"}], "References": [{"Title": "Image quality evaluation of video conferencing solutions with realistic laboratory scenes", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "318", "JournalTitle": "Electronic Imaging"}]}, {"ArticleId": *********, "Title": "Introduction to Special Section on FPGA 2021", "Abstract": "", "Keywords": "", "DOI": "10.1145/3536335", "PubYear": 2022, "Volume": "15", "Issue": "4", "JournalId": 10055, "JournalTitle": "ACM Transactions on Reconfigurable Technology and Systems", "ISSN": "1936-7406", "EISSN": "1936-7414", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, The University of Sydney, Australia 2006"}], "References": []}, {"ArticleId": *********, "Title": "Pharmaceutical process optimisation: Decision support under high uncertainty", "Abstract": "Optimisation of cell culture processes is an extremely challenging problem for the biomanufacturing industry. Limited data availability, coupled with biological complexity in modelling highly variable living cells, necessitates a decision support methodology that is performant under high levels of uncertainty. Raw materials, experimental &amp; manufacturing facilities, and human expertise are all steeply expensive and availability is tightly constrained — planning their allocation is subject to the core uncertainties underlying the behaviour of living cells. This paper presents a novel decision support methodology for optimisation under high uncertainty. Optimisation techniques require an “objective function” that maps decision variables to the quantity being optimised, so that the decision space can be explored to find an optimum. By learning multiple types of objective function candidates with different levels of fidelity to real-world processes, our method mitigates the risk of picking a poor approximation of the objective function due to sampling effects and algorithmic randomness. Wet lab experimentation on a biomanufacturing feed optimisation problem verified that inferred candidates can successfully support domain experts in designing a new optimised feed strategy with significantly higher product yield than the current industrial control strategy. Our results indicate potential for extending our methodology to the optimisation of other complex industrial processes.", "Keywords": "", "DOI": "10.1016/j.compchemeng.2022.108100", "PubYear": 2023, "Volume": "170", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "Chaitanya Manapragada", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Melbourne, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Melbourne, Australia;CSL Innovation, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Melbourne, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Melbourne, Australia"}], "References": [{"Title": "Incorporating expert prior in Bayesian optimisation via space warping", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105663", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A survey of inverse reinforcement learning: Challenges, methods and progress", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "297", "Issue": "", "Page": "103500", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": *********, "Title": "A Non-Cooperative Distributed Model Predictive Control Using Laguerre Functions for Large-Scale Interconnected Systems", "Abstract": "This paper deals with a new non-cooperative distributed controller for linear large-scale systems based on designing multiple local Model Predictive Control (MPC) algorithms using Laguerre functions to enhance the global performance of the overall closed-loop system. In this distributed control scheme, that does not require a coordinator, local MPC algorithms might transmit and receive information from other sub-controllers by means of the communication network to perform their control decisions independently on each other. Thanks to the exchanged information, the sub-controllers have in this way the ability to work together in a collaborative manner towards achieving a good overall system performance. To decrease drastically the computational load in the small-size optimization problem with a short prediction horizon, discrete-time Laguerre functions are used to tightly approximate the optimal control sequence. For evaluating the proposed distribution control framework, a simulation example is proposed to show the effectiveness of the proposed scheme and its applicability for large-scale interconnected systems. The obtained simulation results are provided to demonstrate clearly that the proposed Non-Cooperative Distributed MPC (NC-DMPC) outperforms Decentralized MPC (De-MPC) and achieves performance comparable to centralized MPC with a reduced computing time. The system performance of the proposed distributed model predictive control is given.", "Keywords": "discrete-time Laguerre functions; distributed model predictive control; large-scale interconnected systems; non-cooperative strategy; optimization problem", "DOI": "10.18280/jesa.550501", "PubYear": 2022, "Volume": "55", "Issue": "5", "JournalId": 14230, "JournalTitle": "Journal Européen des Systèmes Automatisés", "ISSN": "1269-6935", "EISSN": "2116-7087", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Petrochemicals, University of 20 Aout 1955, Skikda, 21000, Algeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitede Lorraine, CNRS, CRAN, Nancy, F-54000, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DAC HR Laboratoiy, Ferhat ABBAS University, Setif, 119000, Algeria"}], "References": []}, {"ArticleId": 105143210, "Title": "Using the Model Reduction Techniques to Find the Low-Order Controller of the Aircraft's Angle of Attack Control System", "Abstract": "The problem of controlling the angle of attack of the aircraft is one of the difficult and complex problems due to the problems of nonlinear kinematics, variable parameters and uncertainty model. The design of the angle of attack control according to the robustness control algorithm often leads to a higher order robustness controller. Using a higher-order controller has many disadvantages, so it is necessary to have solutions to reduce the order of the controller. This paper presents the idea of designing a low-order controller for the aircraft's angle of attack control system using the order reduction algorithm. In order to meet the requirements of performance and stability when parameters change, the optimal controller of the aircraft's angle of attack is usually of high order. The paper has used order reduction algorithms to reduce the order of high-order angle of attack controller, the results show that: 4th-order controller or 1st-order controller can be used instead of high order controller. Using a low-order controller to control the aircraft's angle of attack shows that the quality of the control system is comparable to that of a high-order controller.", "Keywords": "aircraft; angle of attack; optimal controller; order reduction algorithm", "DOI": "10.18280/jesa.550510", "PubYear": 2022, "Volume": "55", "Issue": "5", "JournalId": 14230, "JournalTitle": "Journal Européen des Systèmes Automatisés", "ISSN": "1269-6935", "EISSN": "2116-7087", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mechanical and Electrical, Hanoi Industrial Textile Garment University, Hanoi, 100000, Viet Nam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mechanical and Electrical, Hanoi Industrial Textile Garment University, Hanoi, 100000, Viet Nam"}], "References": []}, {"ArticleId": 105143217, "Title": "Data literacy for improving governmental performance: A competence-based approach and multidimensional operationalization", "Abstract": "Big data analytics received much attention in the last decade and is viewed as one of the next most important strategic resources for organizations. Yet, the role of employees&#x27; data literacy seems to be neglected in current literature. The aim of this study is twofold: (1) it develops data literacy as an organization competency by identifying its dimensions and measurement, and (2) it examines the relationship between data literacy and governmental performance (internal and external). Using data from a survey of 120 Dutch governmental agencies, the proposed model was tested using PLS-SEM. The results empirically support the suggested theoretical framework and corresponding measurement instrument. The results partially support the relationship of data literacy with performance as a significant effect of data literacy on internal performance. However, counter-intuitively, this significant effect is not found in relation to external performance.", "Keywords": "Data literacy ; Organizational performance ; Competence-based theory", "DOI": "10.1016/j.digbus.2022.100050", "PubYear": 2023, "Volume": "3", "Issue": "1", "JournalId": 82670, "JournalTitle": "Digital Business", "ISSN": "2666-9544", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "HU University of Applied Sciences Utrecht, The Netherlands, P.O. Box 85029, 3508 AA Utrecht, the Netherlands"}], "References": []}, {"ArticleId": 105143218, "Title": "TiO2 QDs/MoSe2 heterojunctions for enhanced photo-excited charge separation and gas sensing performance", "Abstract": "2D transition metal selenides are facing challenges in the field of gas sensing due to their short carrier lifetime and low utilization rate. Along these lines, in this work, MoSe<sub>2</sub> nanosheets were successfully synthesized by solvothermal method, and modified with TiO<sub>2</sub> QDs. P-N heterostructures with the advantage of quantum dot constraint effect are constructed. The developed heterojunction can effectively promote the separation and transfer of the generated charges, under the excitation of ultraviolet light, while the room temperature sensitivity of the material for the detection of NO<sub>2</sub> is enhanced.", "Keywords": "TiO<sub>2</sub> QDs/MoSe<sub>2</sub> ; Heterojunctions ; Charge separation ; NO<sub>2</sub> gas sensor", "DOI": "10.1016/j.snb.2022.133124", "PubYear": 2023, "Volume": "379", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The Key Laboratory for Photonic and Electronic Bandgap Materials, Ministry of Education, School of Physics and Electronic Engineering, Harbin Normal University, Harbin 150025, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The Key Laboratory for Photonic and Electronic Bandgap Materials, Ministry of Education, School of Physics and Electronic Engineering, Harbin Normal University, Harbin 150025, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The Key Laboratory for Photonic and Electronic Bandgap Materials, Ministry of Education, School of Physics and Electronic Engineering, Harbin Normal University, Harbin 150025, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Key Laboratory for Photonic and Electronic Bandgap Materials, Ministry of Education, School of Physics and Electronic Engineering, Harbin Normal University, Harbin 150025, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Sun", "Affiliation": "The Key Laboratory for Photonic and Electronic Bandgap Materials, Ministry of Education, School of Physics and Electronic Engineering, Harbin Normal University, Harbin 150025, China;Corresponding author"}], "References": [{"Title": "Stretchable energy storage E-skin supercapacitors and body movement sensors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "305", "Issue": "", "Page": "127529", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Indium selenide nanosheets for photoelectrical NO2 sensor with ultra sensitivity and full recovery at room temperature", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129127", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Boosting of NO2 gas sensing performances using GO-PEDOT:PSS nanocomposite chemical interface coated on langasite-based surface acoustic wave sensor", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "344", "Issue": "", "Page": "130267", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Visible light-induced, highly responsive, below lower explosive limit (LEL) LPG sensor based on hydrothermally synthesized barium hexaferrite nanorods", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "348", "Issue": "", "Page": "130714", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "PrGO decorated TiO2 nanoplates hybrid nanocomposite for augmented NO2 gas detection with faster gas kinetics under UV light irradiation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "358", "Issue": "", "Page": "131503", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Enhanced sensitivity of langasite-based surface acoustic wave CO gas sensor using highly porous Ppy@PEDOT:PSS hybrid nanocomposite", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "363", "Issue": "", "Page": "131786", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 105143222, "Title": "Delay-dependent and order-dependent conditions for stability and stabilization of fractional-order memristive neural networks with time-varying delays", "Abstract": "The stability and stabilization problems of fractional-order memristive neural networks with time-varying delays are addressed. Based on Jensen-based integral inequalities, novel delay-dependent and order-dependent stability conditions for fractional-order memristive neural networks with time-varying delays are established. The proposed stability criterion is in terms of linear matrix inequalities and is easy to be verified and applied. Then, a linear feedback control law that stabilizes fractional-order memristive neural networks with time-varying delays is designed by utilizing the obtained stability conditions. Two numerical examples are used to illustrate the effectiveness and less conservativeness of the proposed results.", "Keywords": "", "DOI": "10.1016/j.neucom.2022.12.006", "PubYear": 2023, "Volume": "522", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Shanghai Jiao Tong University, Shanghai 200240, PR China;Key Laboratory of System Control and Information Processing, Ministry of Education of China, Shanghai 200240, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Shanghai Jiao Tong University, Shanghai 200240, PR China;Key Laboratory of System Control and Information Processing, Ministry of Education of China, Shanghai 200240, PR China;Corresponding author at: Department of Automation, Shanghai Jiao Tong University, Shanghai 200240, PR China and Key Laboratory of System Control and Information Processing, Ministry of Education of China, Shanghai 200240, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Shanghai Jiao Tong University, Shanghai 200240, PR China;Key Laboratory of System Control and Information Processing, Ministry of Education of China, Shanghai 200240, PR China"}], "References": [{"Title": "Event-triggered distributed control for synchronization of multiple memristive neural networks under cyber-physical attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "518", "Issue": "", "Page": "361", "JournalTitle": "Information Sciences"}, {"Title": "An overview of stability analysis and state estimation for memristive neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "391", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Mean-square input-to-state stability for stochastic complex-valued neural networks with neutral delay", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>hen<PERSON> Zhao; <PERSON><PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "269", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 105143254, "Title": "To what extent a modern teaching style benefits students? Why do teachers act the way they do?", "Abstract": "<h3 >Background</h3> <p>The growth of teaching practices conducted to promote cognitive activation and active participation in the classroom in recent years has become the epicentre of an active debate regarding their effectiveness.</p> <h3 >Objectives</h3> <p>This study is aimed to shed some light on this topic by empirically analysing the causes and consequences of more modern strategies in Spanish secondary schools.</p> <h3 >Methods</h3> <p>The database used in the study was PISA 2018. In order to analyse the causes behind the teaching style followed by teachers, a logit model was used. Likewise, to analyze the consequences that a modern teaching style has on students at the age of 15, we used propensity score analysis techniques.</p> <h3 >Results and Conclusions</h3> <p>Regarding the results obtained through a logit model, it is shown that the likelihood of a modern teaching style decreases if teachers work for a state school or if they usually apply traditional strategies. Similarly, this series of practices are shown to be more common in schools where teachers and students make greater use of digital devices. The second section of results, focused on analysing the effect this type of teaching strategies has on cognitive, non-cognitive and wellbeing among students, show that, on the one hand, modern teaching style negatively affects students' performance in mathematics, science or language. On the other hand, modern teaching style has been shown to contribute to improving the students' well-being, motivation and cooperation, only being significant the latter. Moreover, this teaching style contributes to reducing bullying among students.</p> Lay Description <h3 >What is already known about this topic</h3> Modern teaching style is gaining increased popularity. Teaching style is determined by students characteristics, performance and characteristics of the teacher and the class in general. There is not an agreed-upon hypothesis on the impact of a modern teaching style on the academic results obtained by students. <h3 >What this paper adds</h3> This study analyses empirically the factors that explain modern teaching practices at secondary schools. Modern teaching practices are negative associated with more traditional practices, being more common this style between teachers that work at private school or use more digital services, among others. Reports the first analysis, taking into account teacher and students responses about teacher style, on the effects of modern teaching practice on cognitive, non-cognitive and wellbeing among students. <h3 >Implications for practice and/or policy</h3> Knowing the factors behind the teaching style followed by teachers can be useful in trying to adapt teaching strategies to the needs of students. Modern teaching style can be good ally to promote collaborative practices among students. Modern teaching style should be complemented with traditional practices, like summaries previous class or advice students to achieve goals, which the literature has demonstrated to be effective to improve mathematics or reading competences. Teaching style adopted by the teachers can be decisive in improving the educational system as a whole.", "Keywords": "modern teaching style;non-cognitive skills;PISA 2018;propensity score matching;secondary education;Spain", "DOI": "10.1111/jcal.12765", "PubYear": 2023, "Volume": "39", "Issue": "2", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>Aldonza", "Affiliation": "Applied Economics Universidad de La Rioja  La Rioja Spain"}], "References": []}, {"ArticleId": 105143264, "Title": "Extracting Radiomic features from pre-operative and segmented MRI scans improved survival prognosis of glioblastoma Multiforme patients through machine learning: a retrospective study", "Abstract": "<p>The combination of radiomics and artificial intelligence has emerged as a strong technique for building predictive models in radiology. This study aims to address the clinically important issue of whether a radiomic profile can predict the overall survival (OS) time of glioblastoma multiforme (GBM) patients having gross tumor resection (GTR) through pre-operative structural magnetic resonance imaging (MRI) scans. A retrospective analysis was made using data of glioma patients made publicly available by the University of Pennsylvania. The radiomic characteristics were extracted from pre-operative structural multiparametric MRI (mpMRI) sequences after pre-processing and 3D segmentation using deep learning (DL). After removing irrelevant features, regression models based on machine learning (ML) were developed by considering selected features to predict the OS time of GBM patients within a period of days only. The patients were divided into three survivor groups depending on their projected survival time. To validate the significance of the selected feature set, statistical analysis was performed. As many as 494 patients were considered to improve survival prediction (SP) by using more effective feature extraction and selection techniques. The ridge regressor acquired the highest SpearmanR Rank correlation of 0.635 with an accuracy of 69%, the greatest of all the previous works for categorical predictions of such patients. The researchers in the past who used radiomic characteristics for the OS prognosis of GBM patients could yield limited results only. However, the current research work recorded an enhanced accuracy and SpearmanR rank for the three survivor classes of GBM patients using ML, feature selection, and radiomics. The significance of this work lies in the selection of patients with GTR and the extraction of radiomic characteristics through the use of radiomics and artificial intelligence.</p>", "Keywords": "3D magnetic resonance imaging; Convolutional neural network; Deep supervised UNet; Tumor subregion segmentation; Radiomics; Survival prediction; BraTS", "DOI": "10.1007/s11042-022-14223-x", "PubYear": 2023, "Volume": "82", "Issue": "19", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science & Engineering Department, Thapar Institute of Engineering and Technology, Patiala, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science & Engineering Department, Thapar Institute of Engineering and Technology, Patiala, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science & Engineering Department, Thapar Institute of Engineering and Technology, Patiala, India"}], "References": [{"Title": "COVID \n ‐19 diagnosis system by deep learning approaches", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "3", "Page": "e12776", "JournalTitle": "Expert Systems"}]}, {"ArticleId": 105143320, "Title": "Prediction of software engineering careers from Instagram personality traits", "Abstract": "A social networking platform named Instagram is one of the most popular platforms that we spend time on. The data from user-generated Instagram posts may be helpful to gauge the personalities of people. Previously, experiments that utilised Instagram data for personality appraisal and visualisation for professional reasons were not accessible for public usage. This research helps to recognise personality styles that are common of the Instagram culture. A computational score is generated and a model is built by using these scores in job suggestions that complement the personality of the individual. In the creation of a test score, Instagram's personality score is benchmarked against a five-factor personality model (FFPM) test score to assess how reliable it is. The statistical grade scale on Instagram personality and the FFPM test is very strong and reported 92.4%. This research will help employers identify candidates that match their company's needs by gauging their personalities. Copyright © 2023 Inderscience Enterprises Ltd.", "Keywords": "FFPM; five-factor personality model; Instagram; personality; software engineering careers", "DOI": "10.1504/IJEB.2023.127529", "PubYear": 2023, "Volume": "18", "Issue": "1", "JournalId": 8392, "JournalTitle": "International Journal of Electronic Business", "ISSN": "1470-6067", "EISSN": "1741-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, MLR Institute of Technology, Dundigal, Telangana, Hyderabad, India; Jawaharlal Technological University, Hyderabad, India"}], "References": []}, {"ArticleId": *********, "Title": "Spectrum analysis of OFDM versus FBMC in 5G mobile communications", "Abstract": "With the demand for mobile data traffic, multi-carrier transmission techniques are highly attractive for all-new wireless communication systems, which divides data into many components and sends each of these components via a different carrier signal. So far, orthogonal frequency division multiplexing (OFDM) and filter bank multi-carrier (FBMC) techniques are the dominant waveform contenders. A number of studies have been made and failed to give a complete comparison where they did not consider various conditions altogether. Therefore, this paper addresses a complete comparative analysis of OFDM and FBMC, performed based on spectral efficiency, modulation, demodulation, power spectral densities, and peak to average power ratio comparison, all simulated using Matlab and GNU’s not unix radio (GNU-radio) software.", "Keywords": "FBMC;GNU-radio;OFDM;polyphase filters", "DOI": "10.12928/telkomnika.v21i1.24252", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Abdelmalek Essaadi University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Abdelmalek Essaadi University"}], "References": []}, {"ArticleId": 105143509, "Title": "E-waste management using hybrid optimization-enabled deep learning in IoT-cloud platform", "Abstract": "Electronic waste (E-waste) is generated at a quick pace due to technological expansion and thereby reducing the obsolescence age of electrical and electronic equipment (EEE). This ever-increasing e-waste is not only dangerous for the environment but also to the health of human beings. A robust and effective E-waste classification approach is devised in this paper, named Fractional Horse Herd Gas Optimization-based Shepherd Convolutional Neural Network (FrHHGO-based ShCNN) for classifying E-wastes in the Internet of Things (IoT)-cloud platform. Here, the E-waste images are collected by the IoT nodes and then stored in the cloud data storage which then performs the process of routing using the proposed FrHHGO algorithm. Moreover, effective features are extracted and then augmented to perform the process of E-waste classification. The E-waste classification is performed using FrHHGO, which is the combination of Fractional Henry Gas Optimization (FHGO), and Horse Herd Optimization Algorithm (HOA). The developed method outperformed various existing approaches with minimum energy and delay of 0.301 J, and 0.666 s, maximum accuracy, sensitivity, and specificity of 0.950, 0.934, and 0.967, respectively. Thus, the developed E-waste classification system enhances the social, environmental, and economic sustainability using FrHHGO-based ShCNN in emerging economies.", "Keywords": "", "DOI": "10.1016/j.advengsoft.2022.103353", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Faculty of Engineering and Technology, Annamalai University, Tamil Nadu, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> V", "Affiliation": "Department of CSE, Faculty of Engineering and Technology, Annamalai University, Tamil Nadu, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of CSE, Gudlavalleru Engineering College, Gudlavalleru, India"}], "References": [{"Title": "Outpatient Text Classification Using Attention-Based Bidirectional LSTM for Robot-Assisted Servicing in Hospital", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "106", "JournalTitle": "Information"}, {"Title": "Horse herd optimization algorithm: A nature-inspired algorithm for high-dimensional optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "213", "Issue": "", "Page": "106711", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 105143522, "Title": "Dynamic Bin Packing with Predictions", "Abstract": "<p>The MinUsageTime Dynamic Bin Packing (DBP) problem aims to minimize the accumulated bin usage time for packing a sequence of items into bins. It is often used to model job dispatching for optimizing the busy time of servers, where the items and bins match the jobs and servers respectively. It is known that the competitiveness of MinUsageTime DBP has tight bounds of Θ(√łog μ ) and Θ(μ) in the clairvoyant and non-clairvoyant settings respectively, where μ is the max/min duration ratio of all items. In practice, the information about the items' durations (i.e., job lengths) obtained via predictions is usually prone to errors. In this paper, we study the MinUsageTime DBP problem with predictions of the items' durations. We find that an existing O(√łog μ )-competitive clairvoyant algorithm, if using predicted durations rather than real durations for packing, does not provide any bounded performance guarantee when the predictions are adversarially bad. We develop a new online algorithm with a competitive ratio of minØ(ε^2 √łog(ε^2 μ) ), O(μ) (where ε is the maximum multiplicative error of prediction among all items), achieving O(√łog μ) consistency (competitiveness under perfect predictions where ε = 1) and O(μ) robustness (competitiveness under terrible predictions), both of which are asymptotically optimal.</p>", "Keywords": "algorithms with predictions; busy time; competitive ratio; consistency; dynamic bin packing; robustness; scheduling", "DOI": "10.1145/3570605", "PubYear": 2022, "Volume": "6", "Issue": "3", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nanyang Technological University, Singapore, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanyang Technological University, Singapore, Singapore"}], "References": []}, {"ArticleId": 105143590, "Title": "Study Comparison Between Enhanced Firefly and Differential Evolution to Solve the Maximum Power Point Tracking Problem", "Abstract": "The penetration of photovoltaic (PV) in electric power generation is continually increasing. On the other side, the load will receive the actual power, which is a part of power supplied by the photovoltaic. Therefore, it is necessary to extract maximum power from PV. One of a defy problem, is the tracking maximum power point (MPPT) in photovoltaic frameworks and it is a significant task. It can be a reproducer of maximum power from a photovoltaic system, which it depends on the adjusting of duty cycle of DC-DC converter. In order to produce a maximum power transfer, the impedance between the source and the load should be coincide by using of a buck boost converter. In this work, the proposed methods; Firefly algorithm (FA), Enhanced Firefly (EFA), Differential Evolution Scheme1, Differential Evolution Scheme2, and Differential Evolution Scheme 3 were tested for their performances in different conditions. Finally, the simulation results confirm that the second scheme of DE outperforms the others. Visual Basic. Net has been used to simulate the results and proceed with algorithms.", "Keywords": "differential evolution; duty cycle; firefly; MPPT; optimization", "DOI": "10.18280/jesa.550509", "PubYear": 2022, "Volume": "55", "Issue": "5", "JournalId": 14230, "JournalTitle": "Journal Européen des Systèmes Automatisés", "ISSN": "1269-6935", "EISSN": "2116-7087", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Basrah Engineering Technical College, Southern Technical University, Basrah, 61001, Iraq"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Basrah Engineering Technical College, Southern Technical University, Basrah, 61001, Iraq"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Engineering, University of Basrah, Basrah, 61001, Iraq"}], "References": []}, {"ArticleId": 105143612, "Title": "Commentary: A review on the role of affective stimuli in event-related frontal alpha asymmetry", "Abstract": "", "Keywords": "Frontal Alpha Asymmetry (FAA); Emotion Feelings-as-information ; Motivation; EEG; Human Media Interaction", "DOI": "10.3389/fcomp.2022.994071", "PubYear": 2022, "Volume": "4", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Experimental Psychology I, Institute of Psychology, Osnabrück University, Germany"}], "References": [{"Title": "Sustained inattentional blindness in virtual reality and under conventional laboratory conditions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "209", "JournalTitle": "Virtual Reality"}, {"Title": "Authentic Fear Responses in Virtual Reality: A Mobile EEG Study on Affective, Behavioral and Electrophysiological Correlates of Fear", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "106", "JournalTitle": "Frontiers in Virtual Reality"}, {"Title": "A Review on the Role of Affective Stimuli in Event-Related Frontal Alpha Asymmetry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "", "Page": "74", "JournalTitle": "Frontiers in Computer Science"}]}, {"ArticleId": 105143663, "Title": "A model fusion method based on multi-source heterogeneous data for stock trading signal prediction", "Abstract": "<p>In the prediction of turning points (TPs) of time series, the improved model of integrating piecewise linear representation and weighted support vector machine (IPLR-WSVM) has achieved good performance. However, due to the single data source and the limitation of algorithm, IPLR-WSVM has encountered challenges in profitability. In this paper, a model fusion method based on multi-source heterogeneous data and different learning algorithms is proposed for the prediction of TPs (MF-MSHD). Multi-source heterogeneous data include weighted unstructured and structured information with different granularities. RF, WSVM, BPNN, GBDT, and LSTM are selected to be the learning algorithms. The differences among meta-models are constructed by different inputs and algorithms as much as possible, and a model fusion rule is designed to determine the final TPs. Moreover, the TPs are generated based on the characteristics of individual stock. For sentiment analysis, a more accurate sentiment dictionary of stock market comments is established. Specifically, the fine-grained data is introduced to jointly determine the accurate trading moment. The prediction level of the proposal improves the accuracy and profitability, and also outperforms the composite indexes. Experimental results show that the profit rate of randomly selected stocks in MF-MSHD reaches 0.5172, while the highest value is 0.2841 in single meta-model and 0.0992 in buy and hold strategy, respectively. The other indicators including the accuracy are also modified. Compared with the increases of 0.1648, 0.4051, and 0.3397 in Shanghai Composite Index, Shenzhen Composite Index, and CSI 300 Index, MF-MSHD shows higher profitability in stock trading signal prediction.</p>", "Keywords": "Stock trading signal prediction; Model fusion; Multi-source heterogeneous data; Sentiment analysis", "DOI": "10.1007/s00500-022-07714-4", "PubYear": 2023, "Volume": "27", "Issue": "10", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, China; College of Physics and Energy, Fujian Normal University, Fuzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, China"}], "References": [{"Title": "Exploration of stock index change prediction model based on the combination of principal component analysis and artificial neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "11", "Page": "7851", "JournalTitle": "Soft Computing"}, {"Title": "Stock price prediction based on deep neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>g Yan", "PubYear": 2020, "Volume": "32", "Issue": "6", "Page": "1609", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A hybrid two-stage financial stock forecasting algorithm based on clustering and ensemble learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "11", "Page": "3852", "JournalTitle": "Applied Intelligence"}, {"Title": "A graph‐based convolutional neural network stock price prediction with leading indicators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "628", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Sentiment analysis of stock markets using a novel dimensional valence–arousal approach", "Authors": "<PERSON><PERSON>g<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "6", "Page": "4433", "JournalTitle": "Soft Computing"}, {"Title": "A graph-based CNN-LSTM stock price prediction algorithm with leading indicators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "3", "Page": "1751", "JournalTitle": "Multimedia Systems"}, {"Title": "Stock values predictions using deep learning based hybrid models", "Authors": "<PERSON><PERSON><PERSON>dav; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "1", "Page": "107", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": 105143787, "Title": "PASIFTNet: Scale-and-Directional-Aware Semantic Segmentation of Point Clouds", "Abstract": "Point clouds obey the sparsity, disorderliness and irregularity properties, leading to noisy or unrobust features during the 3D semantic segmentation task. Existing approaches cannot fully mine local geometry and context information of point clouds, due to their irrational feature learning or neighborhood selection schemes. In this paper, we propose a Point-Atrous SIFT Network (PASIFTNet) for learning multi-scale multi-directional features of point clouds. PASIFTNet is a hierarchical encoder–decoder network, which combines the Point-Atrous SIFT (PASIFT) modules and edge-preserved pooling/unpooling modules alternatively during the encoder/decoder stage. The key component of PASIFTNet is the Point-Atrous Orientation Encoding unit of the PASIFT module, which can arbitrarily expand its receptive fields to incorporate larger context information and extract scale-and-directional-aware feature point information, benefiting from the quadrant-wise SIFT-like point-atrous convolution. Moreover, the edge-preserved pooling/unpooling modules complement PASIFTNet by preserving the edge features and recovering the high-dimensional features of point clouds. We conduct experiments on two public 3D point cloud datasets: ScanNet , S3DIS and a real-world unlabeled dataset FARO-3 collected by the FARO laser scanner. The quantitative results show that, PASIFTNet achieves 86.8% overall accuracy on ScanNet and achieves 86.5% overall accuracy and 68.3% mean intersection-over-union on S3DIS . Moreover, PASIFTNet exhibits a satisfactory robustness and generalization ability towards unknown scenes on FARO-3 .", "Keywords": "Semantic segmentation ; Point-atrous convolution ; Scale-and-directional-aware", "DOI": "10.1016/j.cad.2022.103462", "PubYear": 2023, "Volume": "156", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Artificial Intelligence, Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Artificial Intelligence, Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Artificial Intelligence, Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China"}, {"AuthorId": 4, "Name": "Yanfeng Sun", "Affiliation": "Beijing Institute of Artificial Intelligence, Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China"}, {"AuthorId": 5, "Name": "Baocai Yin", "Affiliation": "Beijing Institute of Artificial Intelligence, Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China;Corresponding author"}], "References": [{"Title": "Point attention network for semantic segmentation of 3D point clouds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107446", "JournalTitle": "Pattern Recognition"}, {"Title": "RGAM: A novel network architecture for 3D point cloud semantic segmentation in indoor scenes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "571", "Issue": "", "Page": "87", "JournalTitle": "Information Sciences"}, {"Title": "Geometry Guided Deep Surface Normal Estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "142", "Issue": "", "Page": "103119", "JournalTitle": "Computer-Aided Design"}, {"Title": "Efficient Representation and Optimization of TPMS-Based Porous Structures for 3D Heat Dissipation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> Hu", "PubYear": 2022, "Volume": "142", "Issue": "", "Page": "103123", "JournalTitle": "Computer-Aided Design"}, {"Title": "3D Shape Segmentation Using Soft Density Peak Clustering and Semi-Supervised Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "145", "Issue": "", "Page": "103181", "JournalTitle": "Computer-Aided Design"}]}, {"ArticleId": 105143788, "Title": "Deep Learning-based Integrated Framework for stock price movement prediction", "Abstract": "Stock market prediction is a very important problem in the economics field. With the development of machine learning, more and more algorithms are applied in the stock market to predict the stock price movement. However, stock market prediction is regarded as a challenging task for the noise and volatility of stock market data. Therefore, in this paper, a novel hybrid model SA-DLSTM is proposed to predict stock market and simulation trading by combine a emotion enhanced convolutional neural network (ECNN), the denoising autoencoder (DAE) models, and long short-term memory model (LSTM). Firstly, user-generated comments on Internet were used as a complement to stock market data, and ECNN was applied to extract the sentiment representation. Secondly, we extract the key features of stock market data by DAE, which can improve the prediction accuracy. Thirdly, we take the timeliness of emotion on stock market into consideration and construct more reliable and realistic sentiment indexes. Finally, the key features of stock data and sentiment indexes are fed into LSTM to make stock market prediction. Experiment results show that the prediction accuracy of SA-DLSTM are superior to other compared models. Meanwhile, SA-DLSTM has a good performance both in return and risk. It can help investors make wise decisions.", "Keywords": "", "DOI": "10.1016/j.asoc.2022.109921", "PubYear": 2023, "Volume": "133", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business Administration, Wuhan Business University, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Safety Engineering, Zhongnan University of Economics and Law, Wuhan, China;Corresponding author"}], "References": [{"Title": "Stock Market Prediction Using LSTM Recurrent Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "170", "Issue": "", "Page": "1168", "JournalTitle": "Procedia Computer Science"}, {"Title": "Stock market forecasting with super-high dimensional time-series data using ConvLSTM, trend sampling, and specialized data augmentation", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113704", "JournalTitle": "Expert Systems with Applications"}, {"Title": "The application research of neural network and BP algorithm in stock price pattern classification and prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "872", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A novel study on deep learning framework to predict and analyze the financial time series information", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "125", "Issue": "", "Page": "812", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A hybrid approach for forecasting ship motion using CNN–GRU–AM and GCWOA", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "108084", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 105143899, "Title": "Parallel Power System Restoration", "Abstract": "<p>After a blackout event, power system restoration is an essential activity for grid resilience; operators restart generators, re-establish transmission paths, and restore loads. With a goal of restoring electric service in the shortest time, the core decisions in restoration planning are to partition the grid into subnetworks, each of which has an initial power source for black-start (called sectionalization problem), and then restart all generators in each network (called generator startup sequencing (GSS) problem) as soon as possible. Due to their complexity, the sectionalization and GSS problems are usually solved separately, often resulting in a suboptimal solution. Our paper develops models and computational methods to solve the two problems simultaneously. We first study the computational complexity of the GSS problem and develop an efficient integer linear programming formulation. We then integrate the GSS problem with the sectionalization problem and develop an integer linear programming formulation for the parallel power system restoration (PPSR) problem to find exact optimal solutions. To solve larger systems, we then develop bounding approaches that find good upper and lower bounds efficiently. Finally, to address computational challenges for very large power grids, we develop a randomized approach to find a high-quality feasible solution quickly. Our computational experiments demonstrate that the proposed approaches are able to find good solutions for PPSR in up to 2,000-bus systems.</p><p>History: Accepted by <PERSON>, Area Editor for Design & Analysis of Algorithms – Discrete.</p><p>Funding: This research was supported by the Visiting Faculty Program of Argonne National Laboratory and the U.S. Department of Energy Advanced Grid Modeling Program [Grant DE-OE0000875].</p>", "Keywords": "integer programming; randomized rounding; power system restoration; generator startup sequencing; centered network partition problem", "DOI": "10.1287/ijoc.2022.1258", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kellogg School of Management, Northwestern University, Evanston, Illinois 60208"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Energy Systems Division, Argonne National Laboratory, Lemont, Illinois 60439"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Mathematics and Science, Robert Morris University, Moon Township, Pennsylvania 15108"}], "References": []}, {"ArticleId": 105143902, "Title": "<PERSON><PERSON><PERSON>ov polynomial-based interval reduced order modelling of Cuk converter", "Abstract": "This paper proposes a method of reduced order modelling for Cuk converter using state space averaging (SSA) technique in which combined state space description is obtained and output to control for Cuk converter transfer function is determined. However, there may be some variation in parameters of system due to uncertainties and imperfect modelling that are addressed using interval modelling. Thus, the obtained interval model is reduced further using <PERSON><PERSON><PERSON><PERSON> polynomials. Routh-Padé approximation is employed for model order reduction. This procedure of model order reduction is explained for Cuk converter model. Fourth-order system is reduced to its respective first, second and third-order reduced models. To signify efficacy of technique proposed, frequency and step responses of models and system are plotted. Comparative study is performed using time domain specifications. Further, difference between responses of model and system are presented in terms of performance indices. The results prove effectiveness and simplicity of proposed technique. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "Cuk converter; interval modelling; interval systems; <PERSON><PERSON><PERSON><PERSON> polynomials; order reduction; parametric uncertainty; SSA; state space averaging", "DOI": "10.1504/IJMIC.2022.127520", "PubYear": 2022, "Volume": "41", "Issue": "3", "JournalId": 12335, "JournalTitle": "International Journal of Modelling, Identification and Control", "ISSN": "1746-6172", "EISSN": "1746-6180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Malaviya National Institute of Technology, Rajasthan, Jaipur, 302017, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Malaviya National Institute of Technology, Rajasthan, Jaipur, 302017, India"}], "References": []}, {"ArticleId": 105143949, "Title": "fastball: a fast algorithm to randomly sample bipartite graphs with fixed degree sequences", "Abstract": "<p>Many applications require randomly sampling bipartite graphs with fixed degrees or randomly sampling incidence matrices with fixed row and column sums. Although several sampling algorithms exist, the ‘curveball’ algorithm is the most efficient with an asymptotic time complexity of $O(n~log~n)$ and has been proven to sample uniformly at random. In this article, we introduce the ‘fastball’ algorithm, which adopts a similar approach but has an asymptotic time complexity of $O(n)$. We show that a C$\\texttt{++}$ implementation of fastball randomly samples large bipartite graphs with fixed degrees faster than curveball, and illustrate the value of this faster algorithm in the context of the fixed degree sequence model for backbone extraction.</p>", "Keywords": "", "DOI": "10.1093/comnet/cnac049", "PubYear": 2022, "Volume": "10", "Issue": "6", "JournalId": 6946, "JournalTitle": "Journal of Complex Networks", "ISSN": "2051-1310", "EISSN": "2051-1329", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Michigan Electrical Engineering and Computer Science, , Ann Arbor, Michigan, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Michigan State University Psychology Department, , East Lansing, Michigan, USA"}], "References": []}, {"ArticleId": 105144002, "Title": "Design and verification of daisy chain serial peripheral interface using system Verilog and universal verification methodology", "Abstract": "Serial peripheral interface (SPI) transfers the data between electronic devices like micro controllers and other peripherals. SPI consists of two control lines: select signal and clock signal, and two data lines: input and output. In single master-single slave, the communication is in between master and slave only which will make the design complex and costly, area will increase. In regular SPI mode, the number of chip-select lines is increased if the number of slaves increases. Due to this, the input data received by the master from the slaves are corrupted at master input slave output (MISO). The proposed daisy chain method is used to overcome this problem. The daisy chain method requires only one chip select line at master compared to the regular SPI mode. When the chip-select line is active low, all the slaves are active, and the clock is initiated to all the slaves to transfer the data from the master to the first slave through the master output slave input (MOSI). In this paper, the daisy-chain SPI is designed and developed using Verilog. The proposed design is verified using system Verilog (SV) and universal verification methodology (UVM) in QuestaSim.", "Keywords": "daisy-chain;I2C;SV;UVM", "DOI": "10.12928/telkomnika.v21i1.24093", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Anurag University, Hyderabad"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Anurag University, Hyderabad"}], "References": []}, {"ArticleId": 105144004, "Title": "Stereo matching algorithm using census transform and segment tree for depth estimation", "Abstract": "This article proposes an algorithm for stereo matching corresponding process that will be used in many applications such as augmented reality, autonomous vehicle navigation and surface reconstruction. Basically, the proposed framework in this article is developed through a series of functions. The final result from this framework is disparity map which this map has the information of depth estimation. Fundamentally, the framework input is the stereo image which represents left and right images respectively. The proposed algorithm in this article has four steps in total, which starts with the matching cost computation using census transform, cost aggregation utilizes segment-tree, optimization using winner-takes-all (WTA) strategy, and post-processing stage uses weighted median filter. Based on the experimental results from the standard benchmarking evaluation system from the Middlebury, the disparity map results produce an average low noise error at 9.68% for nonocc error and 18.9% for all error attributes. On average, it performs far better and very competitive with other available methods from the benchmark system.", "Keywords": "census transform;cyber-physical system;segment-tree cost aggregation;stereo matching algorithm;stereo vision;weighted median filtering", "DOI": "10.12928/telkomnika.v21i1.21881", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 2, "Name": "Rostam <PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Terra Drone Technology Malaysia Sdn Bhd"}, {"AuthorId": 5, "Name": "Tg <PERSON><PERSON>d <PERSON>ook", "Affiliation": "Universiti Teknikal Malaysia Melaka"}], "References": []}, {"ArticleId": 105144012, "Title": "TempMesh – A Flexible Wireless Sensor Network for Monitoring River Temperatures", "Abstract": "<p>For a Chinook salmon restoration project in the lower Yuba River in California, we designed and deployed a wireless sensor network to monitor river temperatures at micro-habitat scales. The study required that temperatures be measured along a 3 km study reach, across the channel, and into off-channel areas. To capture diel and seasonal fluctuations, sensors were sampled quarter-hourly for the full duration of the six-month juvenile salmon winter residency. This sampling duration required that nodes minimize power-use. We adopted event-based software on MSP430 micro-controllers with 433 MHz radio and minimized the networking duty-cycle. To address link failures, we included network storage. As the network lacked real-time clocks, data were timestamped at the destination. This, coupled with the storage, yielded timestamp inaccuracies, which we re-aligned using a novel algorithm. We collected over six months of temperature data from 35 sensors across seven nodes. Of the packets collected, we identified 21% as being incorrectly timestamped and were able to re-align 41% of these incorrectly timestamped packets. We collected temperature data through major floods, and the network uploaded data until the flood destroyed the sensors. The network met an important need in ecological sampling with ultra-low power (multi-year battery life) and low-throughput.</p>", "Keywords": "", "DOI": "10.1145/3542697", "PubYear": 2023, "Volume": "19", "Issue": "1", "JournalId": 12743, "JournalTitle": "ACM Transactions on Sensor Networks", "ISSN": "1550-4859", "EISSN": "1550-4867", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of California, <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of California, <PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, <PERSON>"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "U.S. Fish and Wildlife Service, Lodi, CA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "U.S. Army Corps of Engineers"}, {"AuthorId": 7, "Name": "Dipak G<PERSON>", "Affiliation": "University of California, <PERSON>"}], "References": []}, {"ArticleId": 105144129, "Title": "Advance genetic algorithm-based PID controller for air levitation system", "Abstract": "In industrial control systems, PID controllers are being widely used owing to their simple working principles. Many control and instruments engineers and operators use PID controllers in daily life. PID controllers allows for many variations which can cope with a wide range of systems and conditions. For increasing performances of PID controller, fine tuning of its parameters are required. Many authors have used different optimisation algorithms to tune parameters of PID controllers. These optimisation algorithms offer less performance. In this paper, the fine-tuned PID controller have proposed for the air levitation system. Advanced genetic algorithm is used for tuning parameters of PID controllers. For demonstration of efficiency and applicability of the proposed PID controller, simulation-based experimentations have been conducted. The proposed PID design method has been linked with other three optimisation techniques. Ant colony optimisation, particle swarm optimisation and fuzzy logic have been used for performances comparison of advanced genetic algorithm-based PID controllers. In experimental results, we have got very smallest value of IAE, ISE and ITAE using proposed method. It indicates that the proposed PID design method offers better performances than other three optimisation-based PID design methods and other existing methods. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "integrating; PID; process model; tuning, stability", "DOI": "10.1504/IJMIC.2022.127519", "PubYear": 2022, "Volume": "41", "Issue": "3", "JournalId": 12335, "JournalTitle": "International Journal of Modelling, Identification and Control", "ISSN": "1746-6172", "EISSN": "1746-6180", "Authors": [{"AuthorId": 1, "Name": "Dwarkoba P. <PERSON>", "Affiliation": "AISSMS College of Engineering, Pune, 411001, India"}, {"AuthorId": 2, "Name": "Bharat S. Patil", "Affiliation": "AISSMS Polytechnic, Pune, 411001, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "PVPIT, Sangli, 416304, India"}], "References": []}, {"ArticleId": 105144295, "Title": "A hybrid SUGWO optimization for partial face recognition with new similarity index", "Abstract": "<p>This paper introduces a Partial Face Recognition (PFR) method with the benefits of optimization logic using an optimized feature matching aspect. Besides, for better recognition, the Sparse Representation Classification (SRC) and Fully Convolutional Network (FCN) have been combined. As a novelty, this work aims to tune the sparse coefficient of Dynamic Feature Matching (DFM) optimally, in which the reconstruction error should be minimal. Also, this work presents the structural similarity index measure to calculate the similarity scores between the gallery sub-feature map and probe feature map. For optimization purposes, this work deploys a proposed Sealion Updated Grey Wolf Optimization (SUGWO) algorithm. Finally, the proposed method is executed over the traditional methods concerning certain measures.</p>", "Keywords": "Partial face recognition; Fully convolutional network; Sparse representation classification; Dynamic feature matching; Optimization", "DOI": "10.1007/s11042-022-14205-z", "PubYear": 2023, "Volume": "82", "Issue": "12", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Rajasthan Technical University, Kota, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Rajasthan Technical University, Kota, India"}], "References": [{"Title": "Grey Wolf Optimization-Based Second Order Sliding Mode Control for Inchworm Robot", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "9", "Page": "1539", "JournalTitle": "Robotica"}, {"Title": "Deformable face net for pose invariant face recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "", "Page": "107113", "JournalTitle": "Pattern Recognition"}, {"Title": "Coupled generative adversarial network for heterogeneous face recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "103861", "JournalTitle": "Image and Vision Computing"}, {"Title": "Learning local representations for scalable RGB-D face recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113319", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 105144329, "Title": "Correction to: A study of plithogenic graphs: applications in spreading coronavirus disease (COVID-19) globally", "Abstract": "<p>[This corrects the article DOI: 10.1007/s12652-022-03772-6.].</p><p>© Springer-Verlag GmbH Germany, part of Springer Nature 2022.</p>", "Keywords": "", "DOI": "10.1007/s12652-022-04483-8", "PubYear": 2023, "Volume": "14", "Issue": "10", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "Fazeelat Sultana", "Affiliation": "Department of Mathematics and Statistics, Hazara University Mansehra, 21120 Mansehra, Khyber Pakhtunkhwa Pakistan."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Statistics, Hazara University Mansehra, 21120 Mansehra, Khyber Pakhtunkhwa Pakistan."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>akin-SWU Joint Research Centre on Big Data, School of Information Technology, Deakin University, Burwood, VIC 3125 Australia."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, Riphah International University, I-14 Islamabad, Pakistan."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Genetics, Centre for Human Genetics, Hazara University Mansehra, 21120 Mansehra, Khyber Pakhtunkhwa Pakistan."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Management and Technology, Lahore, Pakistan."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Epidemiology and Health Statistic, School of Public Health Southeast University, Nanjing, 210009 China."}], "References": []}, {"ArticleId": *********, "Title": "Applications of Double ARA Integral Transform", "Abstract": "<p>This paper describes our construction of a new double transform, which we call the double ARA transform (DARAT). Our novel double-integral transform can be used to solve partial differential equations and other problems. We discuss some fundamental characteristics of our approach, including existence, linearity, and several findings relating to partial derivatives and the double convolution theorem. DARAT can be used to precisely solve a variety of partial differential equations, including the heat equation, wave equation, telegraph equation, Klein–Gordon equation, and others, all of which are crucial for physical applications. Herein, we use DARAT to solve model integral equations to obtain exact solutions. We conclude that our novel method is easier to use than comparable transforms.</p>", "Keywords": "", "DOI": "10.3390/computation10120216", "PubYear": 2022, "Volume": "10", "Issue": "12", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Zarqa University, Zarqa 13132, Jordan"}], "References": []}]