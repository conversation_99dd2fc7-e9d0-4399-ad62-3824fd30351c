[{"ArticleId": 118198484, "Title": "Assertion-Based Validation using Clustering and Dynamic Refinement of Hardware Checkers", "Abstract": "<p>Post-silicon validation is a vital step in System-on-Chip (SoC) design cycle. A major challenge in post-silicon validation is the limited observability of internal signal states using trace buffers. Hardware assertions are promising to improve observability during post-silicon debug. Unfortunately, we cannot synthesize thousands (or millions) of pre-silicon assertions as hardware checkers (coverage monitors) due to hardware overhead constraints. Therefore, we need to select the most profitable assertions based on design constraints. However, the design constraints can also change dynamically during the device lifetime due to changes in use-case scenarios as well as input variations. Therefore, assertion selection needs to dynamically adapt based on changing circumstances. In this article, we propose an assertion-based post-silicon validation framework to address the above challenges. Specifically, this article makes two important contributions. We propose a clustering-based assertion selection technique that can select the most profitable pre-silicon assertions to maximize the fault coverage. We also present a cost-aware dynamic refinement technique that can select beneficial hardware checkers during runtime based on changing design constraints. Experimental evaluation demonstrates that our proposed pre-silicon assertion selection can outperform state-of-the-art assertion ranking methods (Goldmine and HARM). The results also highlight that our proposed post-silicon dynamic refinement can accurately predict area (less than 5% error) and power consumption (less than 3% error) of hardware checkers at runtime. This accurate prediction enables the identification of the most profitable hardware checkers based on design constraints.</p>", "Keywords": "", "DOI": "10.1145/3696108", "PubYear": 2024, "Volume": "29", "Issue": "6", "JournalId": 8858, "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems", "ISSN": "1084-4309", "EISSN": "1557-7309", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer & Information Science & Engineering, University of Florida, Gainesville, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer & Information Science & Engineering, University of Florida, Gainesville, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer & Information Science & Engineering, University of Florida, Gainesville, United States"}], "References": [{"Title": "A Survey on Assertion-based Hardware Verification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "11s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Automated Generation of Security Assertions for RTL Models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "19", "Issue": "1", "Page": "1", "JournalTitle": "ACM Journal on Emerging Technologies in Computing Systems"}, {"Title": "LTLf Synthesis under Partial Observability: From Theory to Practice", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "326", "Issue": "", "Page": "1", "JournalTitle": "Electronic Proceedings in Theoretical Computer Science"}, {"Title": "Directed Test Generation for Activation of Security Assertions in RTL Models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems"}]}, {"ArticleId": 118198501, "Title": "Code and Data Repository for An Algorithm for Maximizing a Convex Function Based on its Minimum", "Abstract": "", "Keywords": "global optimization; convex maximization; gradient ascent; hidden convexity", "DOI": "10.1287/ijoc.2022.1238.cd", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118198503, "Title": "Sustainable optimization of balancing valve settings in urban heating systems with an enhanced Jaya algorithm", "Abstract": "With the continuous growth of global energy demand, energy conservation in heating has become a research hotspot for urban sustainable development. However, the research on energy conservation by adjusting the balance valve in the secondary network of heating system is relatively few. To address this gap, this paper solves the balancing valve opening adjustment (BVOA) problem with the goal of minimizing heat consumption and the energy usage of circulation pumps. A mixed-integer linear programming model is designed for this problem, and an effective EJaya algorithm is proposed. Initially, a random initialization method with three scrambling strategies is employed to generate high-quality solutions. Moreover, a segmented operator is proposed to enhance the algorithm’s search efficiency. Subsequently, Q-learning is utilized to select from three neighborhood operators, enhancing the algorithm’s local search capabilities. Additionally, an adaptive search balance strategy is designed to balance the methods of updating solutions, aiding in finding better solutions. Finally, extensive experiments have validated the effectiveness and efficiency of the proposed algorithm in solving the BVOA problem, contributing to the promotion of urban energy sustainability and social welfare.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125466", "PubYear": 2025, "Volume": "261", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Liaocheng University, Liaocheng 252000, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Liaocheng University, Liaocheng 252000, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Liaocheng University, Liaocheng 252000, PR China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Liaocheng University, Liaocheng 252000, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Liaocheng University, Liaocheng 252000, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Liaocheng University, Liaocheng 252000, PR China;Corresponding authors"}], "References": [{"Title": "Comprehensive learning Jaya algorithm for engineering design optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "5", "Page": "1229", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "An efficient population-based simulated annealing algorithm for 0–1 knapsack problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "3", "Page": "2771", "JournalTitle": "Engineering with Computers"}, {"Title": "Nature inspired meta heuristic algorithms for optimization problems", "Authors": "<PERSON><PERSON>; Anand H. S.", "PubYear": 2022, "Volume": "104", "Issue": "2", "Page": "251", "JournalTitle": "Computing"}, {"Title": "An Intensive and Comprehensive Overview of JAYA Algorithm, its Versions and Applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "2", "Page": "763", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Efficient multiobjective optimization for an AGV energy-efficient scheduling problem with release time", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "242", "Issue": "", "Page": "108334", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A Hybrid Optimization Algorithm for Water Volume Adjustment Problem in District Heating Systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>xin Lv", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "A Novel Binary Artificial Jellyfish Search Algorithm for Solving 0–1 Knapsack Problems", "Authors": "Gülnur Yildizdan; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "7", "Page": "8605", "JournalTitle": "Neural Processing Letters"}, {"Title": "Intelligent control of district heating system based on RDPG", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "129", "Issue": "", "Page": "107672", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An effective collaboration evolutionary algorithm for multi-robot task allocation and scheduling in a smart farm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; JC Ji", "PubYear": 2024, "Volume": "289", "Issue": "", "Page": "111474", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An effective multi-objective evolutionary algorithm for multiple spraying robots task assignment problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "87", "Issue": "", "Page": "101558", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 118198782, "Title": "Decentralized Integrity: Blockchain-Based Frameworks for Enhancing Software Update Security", "Abstract": "This article examines the application of blockchain technology as a solution for secure and transparent software update distribution. As cyber threats evolve, traditional centralized update mechanisms face increasing vulnerabilities to tampering and unauthorized modifications. We propose a blockchain-based framework that leverages distributed ledger technology to create an immutable, decentralized environment for software updates. Our article demonstrates how this approach can eliminate single points of failure, ensure update integrity through cryptographic signing and network-wide verification, and enhance transparency in the update process. We discuss the system architecture, including update creation, distribution, and verification mechanisms, and evaluate its integration with existing software ecosystems. While acknowledging challenges such as scalability and implementation costs, we argue that blockchain-based solutions significantly advance securing software distribution. Our findings suggest that this approach has the potential to become a standard practice in the industry, substantially improving trust and reliability in software update systems. This article contributes to the growing body of research on blockchain applications in cybersecurity and provides insights for future developments in secure software distribution methods.", "Keywords": "Blockchain;Software Updates;Cybersecurity;Decentralized Distribution;Update Integrity", "DOI": "10.32628/CSEIT241051035", "PubYear": 2024, "Volume": "10", "Issue": "5", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " Venkata Naga <PERSON>", "Affiliation": "Oracle, USA"}], "References": []}, {"ArticleId": 118198879, "Title": "Modelling the effects of perceived system quality and personal innovativeness on the intention to use metaverse: a structural equation modelling approach", "Abstract": "<p>The metaverse, an interactive and immersive 3D virtual environment, has recently become popular and is widely used in several fields, including education. However, the successful use of metaverse relies on the extent to which users intend to adopt and use it. Close examination of this critical issue reveals a lack of research that examines the effects of certain factors on users’ intentions toward using metaverses. Thus, this study extends the technology acceptance model by integrating two constructs—perceived system quality and students’ personal innovativeness. Using a survey to collect data, 164 responses were received from students at the University of Ha’il in Saudi Arabia. Two steps in structural equation modelling (SEM) using the AMOS software were applied to analyse the data and test the research hypotheses. The results revealed that perceived system quality had a significant effect on students’ intentions to use metaverses through perceived ease of use. Furthermore, personal innovativeness had a significant effect on students’ intentions through the perceived usefulness of the metaverse. In addition, perceived usefulness affected students’ intentions to use a metaverse. Surprisingly, perceived ease of use had an insignificant effect on students’ intentions to use the metaverse. Although the proposed model and its findings contribute to the technology acceptance model (TAM) literature, the study’s practical value is significant because it can help educational policymakers and authorities to understand the effect of each factor and plan future strategies. Additionally, the findings of this study can assist practitioners, designers, and developers in designing and promoting the utilisation of metaverses.</p>", "Keywords": "Higher education;Metaverse;Personal innovativeness;System quality;TAM", "DOI": "10.7717/peerj-cs.2331", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "Sultan <PERSON><PERSON>", "Affiliation": "Department of Educational Technology, College of Education, University of Ha'il, Ha'il, Saudi Arabia."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Educational Technology, College of Education, University of Ha'il, Ha'il, Saudi Arabia."}], "References": [{"Title": "Investigating the Acceptance of Mobile Library Applications with an Extended Technology Acceptance Model (TAM)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "103732", "JournalTitle": "Computers & Education"}, {"Title": "A conceptual framework for determining metaverse adoption in higher institutions of gulf area: An empirical study using hybrid SEM-ANN approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100052", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Metaverse: Perspectives from graphics, interactions and visualization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "56", "JournalTitle": "Visual Informatics"}, {"Title": "A Technology Acceptance Model Survey of the Metaverse Prospects", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "2", "Page": "285", "JournalTitle": "AI"}, {"Title": "Definition, roles, and potential research issues of the metaverse in education: An artificial intelligence perspective", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100082", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "The Influence of Social Isolation, Technostress, and Personality on the Acceptance of Online Meeting Platforms during the COVID-19 Pandemic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "17", "Page": "3388", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Social benefits of living in the metaverse: The relationships among social presence, supportive interaction, social self-efficacy, and feelings of loneliness", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> J.<PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "107498", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Development of metaverse for intelligent healthcare", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "11", "Page": "922", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Understanding learners' intention toward Metaverse in higher education institutions from a developing country perspective: UTAUT and ISS integrated model", "Authors": "Abeer F. <PERSON>", "PubYear": 2024, "Volume": "53", "Issue": "12", "Page": "6008", "JournalTitle": "Kybernetes"}]}, {"ArticleId": 118199108, "Title": "Biometric CNN Model for Verification Based on Blockchain and Hyperparameter Optimization", "Abstract": "<p>Nowadays, fingerprints as biometrics are among the most popular means of identity verification for various applications. However, they are susceptible to theft, tampering, or alteration by attackers after storage. Hence, it is critical to guarantee the privacy of these fingerprint templates because standard privacy techniques are not secure enough. Additionally, fingerprint templates are verified using a deep learning model to distinguish between authentic and fake fingerprints, making them more protected and secure by storing them inside a blockchain, which has become the most common secure technique in recent years. This paper implements the proposed efficient and secure biometric system for verification based on blockchain technology and hyperparameter optimization. First, for the storing phase, each user’s authentic fingerprint template, private, and public keys are saved in the block and linked to the previous block in the chain by a hash function. If a hacker attempts to assault a fingerprint, all prior blocks must be changed. Second, for the authentication phase, the user logs in with his fingerprint and looks up the required template in the chain. If the required fingerprint exists, it creates a new block with the login details, and the number 1 is returned, which means that the authentication is valid; if it does not exist, it is verified to see if it is authentic or fake using the proposed biometric convolutional neural network (CNN). The proposed CNN uses the Grid Search (GS) algorithm to tune hyperparameters to distinguish between authentic and fake fingerprints. The SOCOFing dataset is used for evaluating our experiment. According to the experimental results, the proposed CNN model achieved the highest accuracy of 99.52%. As a result of the blockchain, our system can return authentication information after looking up the chain in 300 ms.</p>", "Keywords": "Biometrics; Fingerprint; Authentication; Blockchain; Deep learning; Grid search optimization; Hyperparameter; Verification", "DOI": "10.1007/s44196-024-00653-y", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computers and Information, Mansoura University, Mansoura, Egypt; Department of Computer Science, MET High Institute, Mansoura, Egypt; Corresponding author."}, {"AuthorId": 2, "Name": "Lobna <PERSON>", "Affiliation": "Department of Computer Science, MET High Institute, Mansoura, Egypt"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The Higher Institute of Engineering and Automotive Technology and Energy, New Heliopolis, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computers and Information, Mansoura University, Mansoura, Egypt"}], "References": [{"Title": "Non-Invertible cancellable fingerprint template for fingerprint biometric", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "101690", "JournalTitle": "Computers & Security"}, {"Title": "On hyperparameter optimization of machine learning algorithms: Theory and practice", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}, {"Title": "A survey of consensus algorithms in public blockchain systems for crypto-currencies", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "103035", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Optimal and secure peer-to-peer carbon emission trading: A game theory informed framework on blockchain", "Authors": "Monzure<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "180", "Issue": "", "Page": "108478", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Consortium blockchain private key protection scheme based on rational secret sharing and blockchain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "242", "Issue": "", "Page": "110260", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 118199153, "Title": "Edge Cloud Assisted Quantum LSTM-based Framework for Road Traffic Monitoring", "Abstract": "<p>Effective traffic management is critical in the modern age of smart cities to guarantee seamless transit, lessen traffic, and protect the environment. Therefore, to enhance road traffic monitoring, this research presents a novel Quantum Optimized LSTM (QO-LSTM) framework that leverages Quantum Machine Learning techniques with Long Short-Term Memory in an edge cloud environment. The quantum circuit is used to improve predictions via quantum-enhanced optimization, while the LSTM network is utilized to extract temporal relationships from traffic data. The use of this hybrid approach in edge cloud infrastructure offers low latency and great scalability, making it ideal for real-time applications in smart city contexts. The QO-LSTM model's performance was assessed using several measures, yielding results such as a 99.32% Coefficient of Determination (R2), a 1.96% Root Mean Squared Error (RMSE), and a 0.97% Mean Absolute Error (MAE) which are far better when compared with other models like GRU, LSTM and SAE. Additionally, the model showed great prediction accuracy and reliability with an Explained Variance Score (EVS) of 99.33% and a Mean Absolute Percentage Error (MAPE) of 1.07%. Traffic peaks were also identified followed by the peak durations to gain an understanding of traffic congestion patterns. Moreover, by integrating these innovations, the findings reveal that the model considerably improves the accuracy and responsiveness of traffic predictions, allowing for more effective traffic management approaches and real-time decision-making.</p>", "Keywords": "Deep learning; Edge computing; Traffic monitoring; Quantum machine learning; Cloud computing", "DOI": "10.1007/s13177-024-00424-1", "PubYear": 2024, "Volume": "22", "Issue": "3", "JournalId": 6726, "JournalTitle": "International Journal of Intelligent Transportation Systems Research", "ISSN": "1348-8503", "EISSN": "1868-8659", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering and Technology, Guru Nanak Dev University, Amritsar, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering and Technology, Guru Nanak Dev University, Amritsar, India"}], "References": [{"Title": "Intelligent resource allocation in mobile blockchain for privacy and security transactions: a deep reinforcement learning based approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "6", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Comparative performance analysis of quantum machine learning with deep learning for diabetes prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "4", "Page": "3073", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Classification Model Evaluation Metrics", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "6", "Page": "599", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Contrastive learning based self-supervised time-series analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "117", "Issue": "", "Page": "108397", "JournalTitle": "Applied Soft Computing"}, {"Title": "Evaluating hybrid quantum-classical deep learning for cybersecurity botnet DGA detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "197", "Issue": "", "Page": "223", "JournalTitle": "Procedia Computer Science"}, {"Title": "Machine learning in the quantum realm: The state-of-the-art, challenges, and future vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "194", "Issue": "", "Page": "116512", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Autonomic edge cloud assisted framework for heart disease prediction using RF-LRG algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>;  <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "2", "Page": "5929", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "QCLR: Quantum-LSTM contrastive learning framework for continuous mental health monitoring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121921", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 118199175, "Title": "Explainable AI for Transparent and Trustworthy Tuberculosis Diagnosis: From <PERSON><PERSON> to Actionable Insights", "Abstract": "<p>Building transparent and trustworthy AI-powered systems for disease diagnosis has become more paramount than ever due to a lack of understanding of black box models. A lack of transparency and explainability in AI-driven models can propagate biases and erode patients' and medical practitioners' trust. To answer this challenge, Explainable AI (XAI) is drastically emerging as a practical solution and approach to tackling ethical concerns in the health sector. The overarching purpose of this paper is to highlight the advancement in XAI for tuberculosis diagnosis (TB) and identify the benefits and challenges associated with improved trust in AI-powered TB diagnosis. We explore the potential of XAI in improving TB diagnosis. We attempt to provide a complete plan to promote XAI. We examine the significant problems associated with using XAI in TB diagnosis. We argue that XAI is critical for reliable TB diagnosis by improving the interpretability of AI decision-making processes and recognising possible biases and mistakes. We evaluate techniques and methods for XAI in TB diagnosis and examine the ethical and societal ramifications. By leveraging explainable AI, we can create a more reliable and trustworthy TB diagnostic framework, ultimately improving patient outcomes and global health. Finally, we provide thorough recommendations for developing and implementing XAI in TB diagnosis using X-ray imaging</p>", "Keywords": "", "DOI": "10.37284/eajit.7.1.2276", "PubYear": 2024, "Volume": "7", "Issue": "1", "JournalId": 73559, "JournalTitle": "East African Journal of Information Technology", "ISSN": "2707-5346", "EISSN": "2707-5354", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Male <PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118199255, "Title": "AUTOMAP: Inferring Rank-Polymorphic Function Applications with Integer Linear Programming", "Abstract": "<p>Dynamically typed array languages such as Python, APL, and Matlab lift scalar operations to arrays and replicate scalars to fit applications. We present a mechanism for automatically inferring map and replicate operations in a statically-typed language in a way that resembles the programming experience of a dynamically-typed language while preserving the static typing guarantees. Our type system---which supports parametric polymorphism, higher-order functions, and top-level let-generalization---makes use of integer linear programming in order to find the minimum number of operations needed to elaborate to a well-typed program. We argue that the inference system provides useful and unsurprising guarantees to the programmer. We demonstrate important theoretical properties of the mechanism and report on the implementation of the mechanism in the statically-typed array programming language Futhark.</p>", "Keywords": "", "DOI": "10.1145/3689774", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Copenhagen, Copenhagen, Denmark"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Copenhagen, Copenhagen, Denmark"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Copenhagen, Copenhagen, Denmark"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Copenhagen, Copenhagen, Denmark"}], "References": [{"Title": "APL since 1978", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "HOPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": *********, "Title": "The Ultimate Conditional Syntax", "Abstract": "<p>Functional programming languages typically support expressive pattern-matching syntax allowing programmers to write concise and type-safe code, especially appropriate for manipulating algebraic data types. Many features have been proposed to enhance the expressiveness of stock pattern-matching syntax, such as pattern bindings, pattern alternatives (a.k.a. disjunction), pattern conjunction, view patterns, pattern guards, pattern synonyms, active patterns, ‘if-let’ patterns, multi-way if-expressions, etc. In this paper, we propose a new pattern-matching syntax that is both more expressive and (we argue) simpler and more readable than previous alternatives. Our syntax supports parallel and nested matches interleaved with computations and intermediate bindings. This is achieved through a form of nested multi-way if-expressions with a condition-splitting mechanism to factor common conditional prefixes as well as a binding technique we call conditional pattern flowing. We motivate this new syntax with many examples in the setting of MLscript, a new ML-family programming language. We describe a straightforward desugaring pass from our rich source syntax into a minimal core syntax that only supports flat patterns and has an intuitive small-step semantics. We then provide a translation from the core syntax into a normalized syntax without backtracking, which is more amenable to coverage checking and compilation, and formally prove that our translation is semantics-preserving. We view this work as a step towards rethinking pattern matching to make it more powerful and natural to use. Our syntax can easily be integrated, in part or in whole, into existing as well as future programming language designs.</p>", "Keywords": "", "DOI": "10.1145/3689746", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Hong Kong, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Hong Kong, Hong Kong"}], "References": [{"Title": "The history of Standard ML", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "HOPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "The simple essence of algebraic subtyping: principal type inference with subtyping made easy (functional pearl)", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "ICFP", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Effects as capabilities: effect handlers and lightweight effect polymorphism", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Revisiting occurrence typing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "217", "Issue": "", "Page": "102781", "JournalTitle": "Science of Computer Programming"}, {"Title": "Effects, capabilities, and boxes: from scope-based reasoning to type-based reasoning and back", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "MLstruct: principal type inference in a Boolean algebra of structural types", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA2", "Page": "449", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118199264, "Title": "Making Formulog Fast: An Argument for Unconventional Datalog Evaluation", "Abstract": "<p> With its combination of Datalog, SMT solving, and functional programming, the language Formulog provides an appealing mix of features for implementing SMT-based static analyses (e.g., refinement type checking, symbolic execution) in a natural, declarative way. At the same time, the performance of its custom Datalog solver can be an impediment to using Formulog beyond prototyping—a common problem for Datalog variants that aspire to solve large problem instances. In this work we speed up Formulog evaluation, with some surprising results: while 2.2× speedups can be obtained by using the conventional techniques for high-performance Datalog (e.g., compilation, specialized data structures), the big wins come by abandoning the central assumption in modern performant Datalog engines, semi-naive Datalog evaluation. In the place of semi-naive evaluation, we develop eager evaluation, a concurrent Datalog evaluation algorithm that explores the logical inference space via a depth-first traversal order. In practice, eager evaluation leads to an advantageous distribution of Formulog’s SMT workload to external SMT solvers and improved SMT solving times: our eager evaluation extensions to the Formulog interpreter and <PERSON><PERSON><PERSON>’s code generator achieve mean 5.2× and 7.6× speedups, respectively, over the optimized code generated by off-the-shelf <PERSON><PERSON><PERSON> on SMT-heavy Formulog benchmarks. All in all, using compilation and eager evaluation (as appropriate), Formulog implementations of refinement type checking, bottom-up pointer analysis, and symbolic execution achieve speedups on 20 out of 23 benchmarks over previously published, hand-tuned analyses written in F <sup>♯</sup> , Java, and C++, providing strong evidence that Formulog can be the basis of a realistic platform for SMT-based static analysis. Moreover, our experience adds nuance to the conventional wisdom that traditional semi-naive evaluation is the one-size-fits-all best Datalog evaluation algorithm for static analysis workloads. </p>", "Keywords": "", "DOI": "10.1145/3689754", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Melbourne, Parkville, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Stevens Institute of Technology, Hoboken, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Harvard University, Cambridge, USA"}], "References": [{"Title": "Seminaïve evaluation for a higher-order functional language", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Specializing parallel data structures for Datalog", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "2", "Page": "e5643", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Formulog: Datalog for SMT-based static analysis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Symbolic value-flow static analysis: deep, precise, complete modeling of Ethereum smart contracts", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Scallop: A Language for Neurosymbolic Programming", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "1463", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Bring Your Own Data Structures to Datalog", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "1198", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Flan: An Expressive and Efficient Datalog Compiler for Program Analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "Page": "2577", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Making Formulog Fast: An Argument for Unconventional Datalog Evaluation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "Page": "1219", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Modern Datalog Engines", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "Foundations and Trends® in Databases"}]}, {"ArticleId": 118199445, "Title": "Code and Data Repository for Bi-fidelity Surrogate Modelling: Showcasing the Need for New Test Instances", "Abstract": "", "Keywords": "surrogate modelling; expensive black-box; Co-Kriging; Kriging; multifidelity", "DOI": "10.1287/ijoc.2022.1217.cd", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118199488, "Title": "Minotaur: A SIMD-Oriented Synthesizing Superoptimizer", "Abstract": "<p>A superoptimizing compiler—-one that performs a meaningful search of the program space as part of the optimization process—-can find optimization opportunities that are missed by even the best existing optimizing compilers. We created Minotaur: a superoptimizer for LLVM that uses program synthesis to improve its code generation, focusing on integer and floating-point SIMD code. On an Intel Cascade Lake processor, Minotaur achieves an average speedup of 7.3% on the GNU Multiple Precision library (GMP)’s benchmark suite, with a maximum speedup of 13%. On SPEC CPU 2017, our superoptimizer produces an average speedup of 1.5%, with a maximum speedup of 4.5% for 638.imagick. Every optimization produced by Minotaur has been formally verified, and several optimizations that it has discovered have been implemented in LLVM as a result of our work.</p>", "Keywords": "", "DOI": "10.1145/3689766", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Utah, Salt Lake City, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Utah, Salt Lake City, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Utah, Salt Lake City, USA"}], "References": []}, {"ArticleId": 118199489, "Title": "Weighted Context-Free-Language Ordered Binary Decision Diagrams", "Abstract": "<p> This paper presents a new data structure, called Weighted Context-Free-Language Ordered BDDs (WCFLOBDDs), which are a hierarchically structured decision diagram, akin to Weighted BDDs (WBDDs) enhanced with a procedure-call mechanism. For some functions, WCFLOBDDs are exponentially more succinct than WBDDs. They are potentially beneficial for representing functions of type B <sup> n </sup> → D , when a function’s image V ⊆ D has many different values. We apply WCFLOBDDs in quantum-circuit simulation, and find that they perform better than WBDDs on certain benchmarks. With a 15-minute timeout, the number of qubits that can be handled by WCFLOBDDs is 1-64× that of WBDDs(and 1-128× that of CFLOBDDs, which are an unweighted version of WCFLOBDDs). These results support the conclusion that for this application—from the standpoint of problem size, measured as the number of qubits—WCFLOBDDs provide the best of both worlds: performance roughly matches whichever of WBDDs and CFLOBDDs is better.(From the standpoint of running time, the results are more nuanced.) </p>", "Keywords": "", "DOI": "10.1145/3689760", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Texas at Austin, Austin, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Texas at Austin, Austin, USA"}, {"AuthorId": 3, "Name": "Thomas Reps", "Affiliation": "University of Wisconsin, Madison, USA"}], "References": [{"Title": "Introducing Design Automation for Quantum Computing, <PERSON><PERSON> and <PERSON>. ISBN 978-3-030-41753-6, 2020, Springer International Publishing. 222 Pages, 51 b/w illustrations, 14 illustrations in colour", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "3", "Page": "387", "JournalTitle": "Genetic Programming and Evolvable Machines"}, {"Title": "A Tensor Network based Decision Diagram for Representation of Quantum Circuits", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Sanjiang Li", "PubYear": 2022, "Volume": "27", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems"}, {"Title": "CFLOBDDs: Context-Free-Language Ordered Binary Decision Diagrams", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Thomas <PERSON>", "PubYear": 2024, "Volume": "46", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Programming Languages and Systems"}]}, {"ArticleId": 118199597, "Title": "MPEG Column: 140th MPEG Meeting in Mainz, Germany", "Abstract": "<p>After several years of online meetings, the 140th MPEG meeting was held as a face-to-face meeting in Mainz, Germany.</p>", "Keywords": "", "DOI": "10.1145/3699843.3699851", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 26893, "JournalTitle": "ACM SIGMultimedia Records", "ISSN": "", "EISSN": "1947-4598", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Alpen-Adria-Universität (AAU) Klagenfurt, Austria & Bitmovin Inc."}], "References": []}, {"ArticleId": 118199607, "Title": "Member Spotlight: <PERSON>", "Abstract": "<p>Dr<PERSON> is the founder, president, and CEO of the Institute for Advancing Computing Education.</p>", "Keywords": "", "DOI": "10.1145/3699853.3699859", "PubYear": 2024, "Volume": "56", "Issue": "3", "JournalId": 19941, "JournalTitle": "ACM SIGCSE Bulletin", "ISSN": "0097-8418", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118199682, "Title": "Between the Booms: AI in Winter", "Abstract": "Dek: <p>After people stopped caring, artificial intelligence got more interesting.</p>", "Keywords": "", "DOI": "10.1145/3688379", "PubYear": 2024, "Volume": "67", "Issue": "11", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Wisconsin, Milwaukee, USA"}], "References": []}, {"ArticleId": 118200037, "Title": "Generalizing Random Butterfly Transforms to Arbitrary Matrix Sizes", "Abstract": "<p><PERSON> and <PERSON><PERSON> introduced random butterfly transforms (RBTs) as a preprocessing technique to replace pivoting in dense LU factorization. Unfortunately, their FFT-like recursive structure restricts the dimensions of the matrix. Furthermore, on multi-node systems, efficient management of the communication overheads restricts the matrix’s distribution even more. To remove these limitations, we have generalized the RBT to arbitrary matrix sizes by truncating the dimensions of each layer in the transform. We expanded <PERSON>’s theoretical analysis to generalized RBT, specifically that in exact arithmetic, Gaussian elimination with no pivoting will succeed with probability 1 after transforming a matrix with full-depth RBTs. Furthermore, we experimentally show that these generalized transforms improve performance over <PERSON>’s formulation by up to 62 % while retaining the ability to replace pivoting. This generalized RBT is available in the SLATE numerical software library.</p>", "Keywords": "", "DOI": "10.1145/3699714", "PubYear": 2024, "Volume": "50", "Issue": "4", "JournalId": 14400, "JournalTitle": "ACM Transactions on Mathematical Software", "ISSN": "0098-3500", "EISSN": "1557-7295", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The University of Tennessee, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MIT Lincoln Laboratory & The University of Tennessee, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The University of Tennessee, USA"}], "References": [{"Title": "High performance sparse multifrontal solvers on modern GPUs", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "110", "Issue": "", "Page": "102897", "JournalTitle": "Parallel Computing"}, {"Title": "Task-parallel tiled direct solver for dense symmetric indefinite systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "111", "Issue": "", "Page": "102900", "JournalTitle": "Parallel Computing"}]}, {"ArticleId": 118200176, "Title": "How Differential Privacy Impacts Data Elicitation", "Abstract": "<p> Many studies and online platforms rely on data from individuals to learn properties of a population or train data-driven services and recommendation systems. However, this data is often sensitive or private, and individuals may be unwilling to share personal information. At the same time, the introduction of Differential Privacy has given us a formal and principled way to protect the privacy of individuals. Differential Privacy adds noise to the data or the learner's computation to \"hide\" the data of any specific individual, while still permitting to learn properties at the level of a population. </p><p>In this letter, we discuss our recent work on how one may use differential privacy to increase individuals' willingness to share their personal information. We are particularly interested in understanding the following trade-off: on the one hand, providing more privacy requires adding more noise, which leads to less accurate models; on the other hand, providing more privacy allows more individuals to share their personal data, leading to better models. The main challenges are two-fold: i) we may need to provide different levels of privacy protections to individuals with differing privacy preferences and ii) we aim to incentivize individuals to share their data without payments, but instead through the utility they obtain from the data-driven service offered by the learner.</p>", "Keywords": "", "DOI": "10.1145/3699804.3699811", "PubYear": 2022, "Volume": "20", "Issue": "2", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Ju<PERSON>", "Affiliation": "Georgia Institute of Technology"}], "References": [{"Title": "The Privacy Paradox and Optimal Bias-Variance Trade-offs in Data Acquisition", "Authors": "Guo<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "49", "Issue": "2", "Page": "6", "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review"}]}, {"ArticleId": 118200299, "Title": "Rustlantis: Randomized Differential Testing of the Rust Compiler", "Abstract": "<p> Compilers are at the core of all computer architecture. Their middle-end and back-end are full of subtle code that is easy to get wrong. At the same time, the consequences of compiler bugs can be severe. Therefore, it is important that we develop techniques to increase our confidence in compiler correctness, and to help find the bugs that inevitably happen. One promising such technique that has successfully found many compiler bugs in the past is randomized differential testing , a fuzzing approach whereby the same program is executed with different compilers or different compiler settings to detect any unexpected differences in behavior. We present <PERSON><PERSON><PERSON><PERSON> , the first fuzzer for the Rust programming language that is able to find new correctness bugs in the official Rust compiler. To avoid having to deal with Rust’s strict type and borrow checker, <PERSON><PERSON><PERSON><PERSON> directly generates MIR, the central IR of the Rust compiler for optimizations. The program generation strategy of Rustlantis is a combination of statically tracking the state of the program, obscuring the program state for the compiler, and decoy blocks to lead optimizations astray. This has allowed us to identify 22 previously unknown bugs in the Rust compiler, most of which have been fixed. </p>", "Keywords": "", "DOI": "10.1145/3689780", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ETH Zurich, Zürich, Switzerland / Imperial College London, London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ETH Zurich, Zürich, Switzerland"}], "References": [{"Title": "Stacked borrows: an aliasing model for Rust", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A Survey of Compiler Testing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Random testing for C and C++ compilers with YARPGen", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A Lightweight Formalism for Reference Lifetimes and Borrowing in Rust", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "43", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Programming Languages and Systems"}, {"Title": "CsmithEdge: more effective compiler testing by handling undefined behaviour less conservatively", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "6", "Page": "1", "JournalTitle": "Empirical Software Engineering"}]}, {"ArticleId": 118200303, "Title": "Object-Oriented Fixpoint Programming with Datalog", "Abstract": "<p>Modern usages of Datalog exceed its original design purpose in scale and complexity. In particular, Datalog lacks abstractions for code organization and reuse, making programs hard to maintain. Is it possible to exploit abstractions and design patterns from object-oriented programming (OOP) while retaining a Datalog-like fixpoint semantics? To answer this question, we design a new OOP language called OODL with common OOP features: dynamic object allocation, object identity, dynamic dispatch, and mutation. However, OODL has a Datalog-like fixpoint semantics, such that recursive computations iterate until their result becomes stable. We develop two semantics for OODL: a fixpoint interpreter and a compiler that translates OODL to Datalog. Although the side effects found in OOP (object allocation and mutation) conflict with Datalog's fixpoint semantics, we can mostly resolve these incompatibilities through extensions of OODL. Within fixpoint computations, we employ immutable algebraic data structures (e.g. case classes in Scala), rather than relying on object allocation, and we introduce monotonically mutable data types (mono types) to enable a relaxed form of mutation. Our performance evaluation shows that the interpreter fails to solve fixpoint problems efficiently, whereas the compiled code exploits Datalog's semi-naïve evaluation.</p>", "Keywords": "", "DOI": "10.1145/3689713", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "JGU Mainz, Mainz, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "JGU Mainz, Mainz, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "JGU Mainz, Mainz, Germany"}], "References": [{"Title": "Seminaïve evaluation for a higher-order functional language", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Formulog: Datalog for SMT-based static analysis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Fixpoints for the masses: programming with first-class Datalog constraints", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118200322, "Title": "Stokes flow with Tresca boundary condition: a posteriori error analysis", "Abstract": "<p>In this article, a reliable a posteriori error estimate of residual type is derived for a variational inequality of second kind modeling Stokes equations with <PERSON><PERSON><PERSON>’s boundary condition. Two sources of the error are considered here; the discretization error and the linearization error. Balancing these two errors is crucial to design an adaptive strategy for mesh refinement. Numerical results are reported to illustrate the good performance of the estimator constructed.</p>", "Keywords": "Stokes equations; Nonlinear slip boundary conditions; Variational inequality; Iterative solution; Estimator; 65N30; 76D07; 35J85", "DOI": "10.1007/s10092-024-00617-w", "PubYear": 2024, "Volume": "61", "Issue": "4", "JournalId": 12677, "JournalTitle": "<PERSON><PERSON><PERSON>", "ISSN": "0008-0624", "EISSN": "1126-5434", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>um", "Affiliation": "EPF, Ecole d’Ingénieurs, Rosières-près-Troyes, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, North West University, Potchefstroom, South Africa; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "LIMOS, Clermont-Ferrand, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratoire de Mathematiques et Applications, Unite de recherche “Mathematiques et Modelisation”, Faculté des Sciences, Université Saint-Joseph, Beirut, Lebanon"}], "References": [{"Title": "Adaptive Inexact Semismooth Newton Methods for the Contact Problem Between Two Membranes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "84", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "Adaptive inexact smoothing Newton method for a nonconforming discretization of a variational inequality", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "12", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "Adaptive inexact smoothing Newton method for a nonconforming discretization of a variational inequality", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "12", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 118200342, "Title": "Video game structural layers for narrative design and articulation", "Abstract": "", "Keywords": "", "DOI": "10.1080/14626268.2024.2411222", "PubYear": 2024, "Volume": "35", "Issue": "4", "JournalId": 2725, "JournalTitle": "Digital Creativity", "ISSN": "1462-6268", "EISSN": "1744-3806", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Belfast School of Art, Ulster University, Belfast, UK"}], "References": []}, {"ArticleId": 118200412, "Title": "Tailoring stiffness distribution of tendon-driven continuum finger from manipulation force vector", "Abstract": "", "Keywords": "", "DOI": "10.1080/01691864.2024.2411691", "PubYear": 2024, "Volume": "38", "Issue": "21", "JournalId": 5595, "JournalTitle": "Advanced Robotics", "ISSN": "0169-1864", "EISSN": "1568-5535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Textile Science and Technology, Graduate School of Science and Technology, Shinshu University, Nagano, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Textile Science and Technology, Graduate School of Science and Technology, Shinshu University, Nagano, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Textile Science and Technology, Shinshu University, Nagano, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Textile Science and Technology, Shinshu University, Nagano, Japan"}], "References": []}, {"ArticleId": 118200533, "Title": "Desarrollo de un sistema y de un proceso como un medio para lograr la repetibilidad de resultados en la fermentación de café de baja altura", "Abstract": "", "Keywords": "", "DOI": "10.36825/RITI.12.27.009", "PubYear": 2024, "Volume": "12", "Issue": "27", "JournalId": 70037, "JournalTitle": "Revista de Investigación en Tecnologías de la Investigaión", "ISSN": "", "EISSN": "2387-0893", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118200643, "Title": "libLISA: Instruction Discovery and Analysis on x86-64", "Abstract": "<p>Even though heavily researched, a full formal model of the x86-64 instruction set is still not available. We present libLISA, a tool for automated discovery and analysis of the ISA of a CPU. This produces the most extensive formal x86-64 model to date, with over 118000 different instruction groups. The process requires as little human specification as possible: specifically, we do not rely on a human-written (dis)assembler to dictate which instructions are executable on a given CPU, or what their in- and outputs are. The generated model is CPU-specific: behavior that is \"undefined\" is synthesized for the current machine. Producing models for five different x86-64 machines, we mutually compare them, discover undocumented instructions, and generate instruction sequences that are CPU-specific. Experimental evaluation shows that we enumerate virtually all instructions within scope, that the instructions' semantics are correct w.r.t. existing work, and that we improve existing work by exposing bugs in their handwritten models.</p>", "Keywords": "", "DOI": "10.1145/3689723", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Open Universiteit, Heerlen, Netherlands"}, {"AuthorId": 2, "Name": "Freek Verbeek", "Affiliation": "Open Universiteit, Heerlen, Netherlands / Virginia Tech, Blacksburg, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Virginia Tech, Blacksburg, USA"}], "References": []}, {"ArticleId": 118200719, "Title": "Persuasive explanations for path reasoning recommendations", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10844-024-00896-3", "PubYear": 2025, "Volume": "63", "Issue": "2", "JournalId": 7081, "JournalTitle": "Journal of Intelligent Information Systems", "ISSN": "0925-9902", "EISSN": "1573-7675", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Explainable Recommendation: A Survey and New Perspectives", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "Foundations and Trends® in Information Retrieval"}, {"Title": "CAESAR: context-aware explanation based on supervised attention for service recommendations", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "1", "Page": "147", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Users’ Responsiveness to Persuasive Techniques in Recommender Systems", "Authors": "Alaa <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "679459", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "KR-GCN: Knowledge-Aware Reasoning with Graph Convolution Network for Explainable Recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Approaches and algorithms to mitigate cold start problems in recommender systems: a systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "59", "Issue": "2", "Page": "341", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Time-aware Path Reasoning on Knowledge Graph for Recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "A Systematic Review of Deep Knowledge Graph-Based Recommender Systems, with Focus on Explainable Embeddings", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "7", "Page": "94", "JournalTitle": "Data"}, {"Title": "Reinforcement recommendation reasoning through knowledge graphs for explanation path quality", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "260", "Issue": "", "Page": "110098", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A qualitative analysis of knowledge graphs in recommendation scenarios through semantics-aware autoencoders", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "62", "Issue": "3", "Page": "787", "JournalTitle": "Journal of Intelligent Information Systems"}]}, {"ArticleId": 118200732, "Title": "Preface to the special issue on model-driven engineering and system analysis and modelling", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11334-024-00586-x", "PubYear": 2024, "Volume": "20", "Issue": "4", "JournalId": 18069, "JournalTitle": "Innovations in Systems and Software Engineering", "ISSN": "1614-5046", "EISSN": "1614-5054", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut Supérieur de l’Aéronautique et de l’Espace, Toulouse Cedex 4, France; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institut Supérieur de l’Aéronautique et de l’Espace, Toulouse Cedex 4, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Norwegian University of Science and Technology (NTNU), Trondheim, Norway"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Retired, ABB Corporate Research Norway, Billingstad, Norway"}], "References": []}, {"ArticleId": 118200794, "Title": "Invitations to play: designing for family engagement through STEAM exhibits in community settings", "Abstract": "<p>Intergenerational play between children and their family members provides a rich context that can inform family-centered interaction design. Despite research on the roles and perspectives in adult-child interactions, less is known about the design elements and features of voluntary interaction settings that provide opportunities for family members to collaborate and play in joint, balanced ways. To this end, we report the qualitative results of our design and implementation of three interactive STEAM exhibits installed in an early childhood education center over a six-month period. Our findings surface insights and tensions related to the design features’ impact on supporting family engagement in STEAM exhibits, emergent aspects of family-centered play, and supports needed to implement family-centered STEAM exhibits. Other designers, such as those who design with new technologies or explore human–computer interactions, can learn from our process of iteration and adjustment based on family interactions.</p>", "Keywords": "Constructionism in learning; Exhibit design; Family centered approaches; STEAM (enriched with arts); Playful learning environment", "DOI": "10.3389/fcomp.2024.1387202", "PubYear": 2024, "Volume": "6", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "AIMS Center for Math and Science Education, United States; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Atlas Institute, University of Colorado, Boulder, United States"}], "References": []}, {"ArticleId": 118200952, "Title": "Migración de serverbox hacia un sistema de microservicios aplicando arquitectura vertical", "Abstract": "", "Keywords": "", "DOI": "10.36825/RITI.12.27.006", "PubYear": 2024, "Volume": "12", "Issue": "27", "JournalId": 70037, "JournalTitle": "Revista de Investigación en Tecnologías de la Investigaión", "ISSN": "", "EISSN": "2387-0893", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118201104, "Title": "MusicTalk: A Microservice Approach for Musical Instrument Recognition", "Abstract": "", "Keywords": "", "DOI": "10.1109/OJCS.2024.3476416", "PubYear": 2024, "Volume": "5", "Issue": "", "JournalId": 68233, "JournalTitle": "IEEE Open Journal of the Computer Society", "ISSN": "", "EISSN": "2644-1268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Medical University, Miin <PERSON> School of Computing, National Cheng Kung University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information Technology Service CenterNYCU"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer ScienceNYCU"}], "References": [{"Title": "Grad-CAM: Visual Explanations from Deep Networks via Gradient-Based Localization", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "336", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 118201221, "Title": "Supporting Data Discovery: Comparing Perspectives of Support Specialists and Researchers", "Abstract": "", "Keywords": "", "DOI": "10.5334/dsj-2024-048", "PubYear": 2024, "Volume": "23", "Issue": "", "JournalId": 11331, "JournalTitle": "Data Science Journal", "ISSN": "", "EISSN": "1683-1470", "Authors": [{"AuthorId": 1, "Name": "Guangyuan Sun", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Data-Seeking Behaviour in the Social Sciences", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "2", "Page": "175", "JournalTitle": "International Journal on Digital Libraries"}]}, {"ArticleId": 118201263, "Title": "<i>H</i><sub>∞</sub> bumpless transfer control for switched affine systems with its application to turbofan engine model", "Abstract": "", "Keywords": "", "DOI": "10.1080/00207721.2024.2410456", "PubYear": 2025, "Volume": "56", "Issue": "4", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Marine Electrical Engineering, Dalian Maritime University, Da Lian, Liao Ning, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Marine Electrical Engineering, Dalian Maritime University, Da Lian, Liao Ning, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "AECC Shenyang Engine Research Institute, Shenyang, Liao Ning, People's Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "AECC Shenyang Engine Research Institute, Shenyang, Liao Ning, People's Republic of China"}], "References": [{"Title": "Bumpless transfer fault detection for switched systems: a state-dependent switching approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "On ℓ2–ℓ∞ output-feedback control scheduled by stochastic communication protocol for two-dimensional switched systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "14", "Page": "2961", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Bumpless stabilisation of uncertain switched positive linear systems under synchronous and asynchronous switching", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "2", "Page": "363", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Input-to-state practical stability of switched affine systems with time-varying delay: an event-triggered mechanism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "9", "Page": "1983", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Dynamic output feedback L ∞ control of switched affine systems: An event-triggered mechanism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "", "Page": "101278", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}, {"Title": "Sampled-point-based partition control of linear switched systems with a novel asynchronous switching rule", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "54", "Issue": "12", "Page": "2465", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Trajectory tracking with integral action of switched periodic affine systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "55", "Issue": "6", "Page": "1156", "JournalTitle": "International Journal of Systems Science"}]}, {"ArticleId": 118201322, "Title": "Autonomous positioning, capturing, and grasping mechanism for robot end effectors based on the attraction domain relationship", "Abstract": "Purpose \nThis paper aims to solve the problem of uncertain position and attitude between unstructured terrain robot and grasped target and insufficient control accuracy in extreme environment, a grasping mechanism based on attraction domain relationship is proposed, which can realize autonomous positioning, capturing and grasping of robot under low control accuracy.\n \n \n Design/methodology/approach \nThe grasping mechanism was designed, taking inspiration from fishing behavior this mechanism introduces attraction domains and flexible-elastic structures through the active and passive ends to achieve automatic positioning and capture. After the capture is completed, the grasping mechanism connects the active end and the passive end, simultaneously relying on the gravity of the target object to achieve locking and release between the robot and the target object. This paper adopts theoretical, simulation and experimental verification methods to conduct theoretical and simulation analysis on the autonomous positioning and grasping process of the mechanism, and produces grasping experimental prototypes with different positions and postures.\n \n \n Findings \nThe experiment shows that the gripping mechanism designed in this paper can achieve automatic positioning capture and gripping of large deviation situations under low control accuracy, with a displacement deviation of up to 10 mm (about 1/6 diameter of the end of the mechanism) and an angle deviation of up to 3°. The scientific research task in the extremely high altitude environment has finally been successfully accomplished.\n \n \n Originality/value \nInspired by fishing behavior, this paper proposes a positioning, capturing and grasping mechanism. The attraction area built with permanent magnets, coupled with the flexible connection, enables precise capture under low control, while the grasping mechanism can also rely on gravity to self-lock and release.", "Keywords": "", "DOI": "10.1108/IR-05-2024-0234", "PubYear": 2025, "Volume": "52", "Issue": "2", "JournalId": 4481, "JournalTitle": "Industrial Robot: An International Journal", "ISSN": "0143-991X", "EISSN": "1758-5791", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Guo<PERSON> Zhao", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Ligen Qi", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Multi-segment soft robotic fingers enable robust precision grasping", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "14", "Page": "1647", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "Cone–hole docking mechanism for a modular reconfigurable mobile robot and its characteristic analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "5", "Page": "781", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "Reconfigurable wheel-crawler-integrated walking mechanism design and kinetic analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "4", "Page": "633", "JournalTitle": "Industrial Robot: An International Journal"}]}, {"ArticleId": 118201415, "Title": "Heterogeneous graph contrastive learning with adaptive data augmentation for semi‐supervised short text classification", "Abstract": "Short text classification has been widely used in many fields. Due to the scarcity of labelled data, implementing short text classification under semi‐supervised learning setting has become increasingly popular. Semi‐supervised short text classification methods based on graph neural networks can achieve state‐of‐the‐art classification performance by utilizing the expressive power of graph neural networks. However, these methods usually fail to mine the hidden patterns of a large amount of short text node data in the graph to optimize the short text node embeddings, which limits the semantic representation power of the short texts, thus leading to suboptimal classification performance. To overcome the limitation, this paper proposes a novel semi‐supervised short text classification method called the Heterogeneous Graph Contrastive Learning with Adaptive Data Augmentation (HGCLADA). In the knowledge bases guided soft prompt‐based data augmentation component, the related words of the tag words are used to optimize the soft prompts for generating diverse augmented samples. In the heterogeneous graph contrastive learning framework component, a heterogeneous graph that is constructed using short texts and keywords and an effective edge augmentation scheme based on a short text clustering algorithm are proposed. The optimized short text embeddings can be obtained to achieve the effective semi‐supervised short text classification. Extensive experiments on six benchmark datasets show that our HGCLADA method outperforms four classes of state‐of‐the‐art methods in terms of classification accuracy, especially with significant performance improvements of 8.74% on the TagMyNews dataset when each class only contains 20 labelled data.", "Keywords": "", "DOI": "10.1111/exsy.13744", "PubYear": 2025, "Volume": "42", "Issue": "2", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering Hohai University  Nanjing China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering Hohai University  Nanjing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering Hohai University  Nanjing China"}], "References": [{"Title": "A survey on semi-supervised learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "2", "Page": "373", "JournalTitle": "Machine Learning"}, {"Title": "HGAT: Heterogeneous Graph Attention Networks for Semi-supervised Short Text Classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Predicting video engagement using heterogeneous DeepWalk", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "465", "Issue": "", "Page": "228", "JournalTitle": "Neurocomputing"}, {"Title": "A Survey on Data Augmentation for Text Classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "7", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Self-training method based on GCN for semi-supervised short text classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "611", "Issue": "", "Page": "18", "JournalTitle": "Information Sciences"}, {"Title": "Self‐supervised short text classification with heterogeneous graph neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "6", "Page": "e13249", "JournalTitle": "Expert Systems"}, {"Title": "Self-supervised contrastive learning on heterogeneous graphs with mutual constraints of structure and feature", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "640", "Issue": "", "Page": "119026", "JournalTitle": "Information Sciences"}, {"Title": "Commonsense knowledge powered heterogeneous graph attention networks for semi-supervised short text classification", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "232", "Issue": "", "Page": "120800", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 118201563, "Title": "Statistical Testing of Quantum Programs via Fixed-Point Amplitude Amplification", "Abstract": "<p>We present a new technique for accelerating quantum program testing. Given a quantum circuit with an input/output specification, our goal is to check whether executing the program on the input state produces the expected output. In quantum computing, however, it is impossible to directly check the equivalence of the two quantum states. Instead, we rely on statistical testing, which involves repeated program executions, state measurements, and subsequent comparisons with the specified output. To guarantee a high level of assurance, however, this method requires an extensive number of measurements. In this paper, we propose a solution to alleviate this challenge by adapting Fixed-Point Amplitude Amplification (FPAA) for quantum program testing. We formally present our technique, demonstrate its ability to reduce the required number of measurements as well as runtime cost without sacrificing the original statistical guarantee, and showcase its runtime effectiveness through case studies.</p>", "Keywords": "", "DOI": "10.1145/3689716", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Korea University, Seoul, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Korea University, Seoul, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Korea University, Seoul, South Korea"}], "References": [{"Title": "Projection-based runtime assertions for testing and debugging Quantum programs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Tower: data structures in Quantum superposition", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA2", "Page": "259", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "CoqQ: Foundational Verification of Quantum Programs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "833", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Modular Component-Based Quantum Circuit Synthesis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA1", "Page": "348", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Synthesizing Quantum-Circuit Optimizers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "835", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A Case for Synthesis of Recursive Quantum Unitary Programs", "Authors": "Haowei Deng; Runzhou Tao; Yuxiang Peng", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "Page": "1759", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118201567, "Title": "Full Iso-Recursive Types", "Abstract": "<p> There are two well-known formulations of recursive types: iso-recursive and equi-recursive types. <PERSON><PERSON><PERSON> and <PERSON><PERSON> [<PERSON><PERSON><PERSON> 1996] have shown that iso- and equi-recursive types have the same expressive power. However, their encoding of equi-recursive types in terms of iso-recursive types requires explicit coercions. These coercions come with significant additional computational overhead, and complicate reasoning about the equivalence of the two formulations of recursive types. This paper proposes a generalization of iso-recursive types called full iso-recursive types. Full iso-recursive types allow encoding all programs with equi-recursive types without computational overhead. Instead of explicit term coercions, all type transformations are captured by computationally irrelevant casts, which can be erased at runtime without affecting the semantics of the program. Consequently, reasoning about the equivalence between the two approaches can be greatly simplified. We present a calculus called λ <sub> Fi </sub><sup>µ</sup> , which extends the simply typed lambda calculus (STLC) with full iso-recursive types. The λ <sub> Fi </sub><sup>µ</sup> calculus is proved to be type sound, and shown to have the same expressive power as a calculus with equi-recursive types. We also extend our results to subtyping, and show that equi-recursive subtyping can be expressed in terms of iso-recursive subtyping with cast operators. </p>", "Keywords": "", "DOI": "10.1145/3689718", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Hong Kong, Hong Kong, China"}, {"AuthorId": 2, "Name": "Qianyong Wan", "Affiliation": "University of Hong Kong, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON> C<PERSON>", "Affiliation": "University of Hong Kong, Hong Kong, China"}], "References": [{"Title": "Revisiting iso-recursive subtyping", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "On the semantic expressiveness of recursive types", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Revisiting Iso-Recursive Subtyping", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "44", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Programming Languages and Systems"}, {"Title": "Recursive Subtyping for All", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "1396", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Mutually Iso-Recursive Subtyping", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "347", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118201687, "Title": "Hybrid HAN ‐ CNN with aspect term extraction for sentiment analysis using product review", "Abstract": "In this article, an intensive sentiment analysis approach termed Hierarchical attention‐convolutional neural network (HAN‐CNN) has been proposed using product reviews. Firstly, the input product review is subjected to Bidirectional Encoder Representation from Transformers (BERT) tokenization, where the input data of each sentence are partitioned into little bits of words. Thereafter, Aspect Term Extraction (ATE) is carried out and feature extraction is completed utilizing some features. Finally, sentiment analysis is accomplished by the developed HAN‐CNN, which is formed by combining a Hierarchical Attention Network (HAN) and a Convolutional Neural Network (CNN). Moreover, the proposed HAN‐CNN achieved a greater performance with maximum accuracy, recall and F1‐Score of 91.70%, 90.60% and 91.20%, respectively.", "Keywords": "aspect term extraction;convolutional neural network;hierarchical-attention network;sentiment analysis", "DOI": "10.1111/coin.12698", "PubYear": 2024, "Volume": "40", "Issue": "5", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "P. <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Kongu Engineering College  Perundurai India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering Vellore Institute of Technology  Chennai India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science Engineering and Information Systems Vellore Institute of Technology, Vellore Campus  Vellore India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Kongu Engineering College  Perundurai India"}], "References": [{"Title": "Machine learning based aspect level sentiment analysis for Amazon products", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "5", "Page": "601", "JournalTitle": "Spatial Information Research"}, {"Title": "A multi-task learning model for Chinese-oriented aspect polarity classification and aspect term extraction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "419", "Issue": "", "Page": "344", "JournalTitle": "Neurocomputing"}, {"Title": "A comprehensive survey on sentiment analysis: Approaches, challenges and trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "107134", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Topic-level sentiment analysis of social media data using deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107440", "JournalTitle": "Applied Soft Computing"}, {"Title": "ACNN-TL: attention-based convolutional neural network coupling with transfer learning and contextualized word representation for enhancing the performance of sentiment classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "7", "Page": "10149", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Contextualized Multidimensional Personality Recognition using Combination of Deep Neural Network and Ensemble Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "5", "Page": "3811", "JournalTitle": "Neural Processing Letters"}, {"Title": "A deep learning-based model using hybrid feature extraction approach for consumer sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "1", "Page": "5", "JournalTitle": "Journal of Big Data"}, {"Title": "Consumer sentiment analysis with aspect fusion and GAN‐BERT aided adversarial learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "4", "Page": "e13247", "JournalTitle": "Expert Systems"}, {"Title": "An energy aware resource allocation based on combination of CNN and GRU for virtual machine selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "9", "Page": "25769", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 118201739, "Title": "Correction: Multi-source-free Domain Adaptive Object Detection", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11263-024-02257-7", "PubYear": 2025, "Volume": "133", "Issue": "4", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "Sicheng Zhao", "Affiliation": ""}, {"AuthorId": 2, "Name": "Hui<PERSON> Yao", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118201789, "Title": "Editorial para el número especial de la 12a Conferencia Internacional sobre Investigación e Innovación en Ingeniería de Software", "Abstract": "", "Keywords": "", "DOI": "10.36825/RITI.12.27.001", "PubYear": 2024, "Volume": "12", "Issue": "27", "JournalId": 70037, "JournalTitle": "Revista de Investigación en Tecnologías de la Investigaión", "ISSN": "", "EISSN": "2387-0893", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Carolina Tripp-Barba", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Numerical simulation of the movement of the water surface with macroscopic particles during a partial dam break for various complex reliefs", "Abstract": "<p>In this paper, it was considered a numerical simulation of the water surface movement during the partial collapse of the destruction of a dam with complex terrain. The numerical simulations took into account debris after a partial collapse of the dam, which imitates debris and moves downstream with the water flow. Carrying out these calculations brings the results closer to the real scenario. The mathematical model was based on the Navier–Stokes equations and uses the turbulent large eddy method (LES) model describing the flow of an incompressible viscous fluid. To describe the movement of a two-phase fluid, the volume of fluid (VOF) methods for the phase were used, and the Discrete Phase Model (DPM) and Macroscopic Particle Model (MPM) were used to describe the movement of particles. For the numerical solution of this system of equations, the Pressure Implicit Split Operator (PISO) numerical algorithm was chosen. The results show that the model is accurate and capable of handling very complex interactions such as particle transport or hydrodynamic actions on structures if the appropriate scales are reproduced. Also in this work, a three-dimensional (3D) model of the flow of a dam break on uneven terrain was considered and combined problems were performed that are closer to real conditions. For combined problems, flood zones and flood times were determined, knowledge of which will help evacuate people from dangerous areas. The accuracy of the 3D model and the selected numerical algorithm were verified using two natural measurements.</p>", "Keywords": "", "DOI": "10.1177/00375497231156455", "PubYear": 2025, "Volume": "101", "Issue": "1", "JournalId": 4563, "JournalTitle": "SIMULATION", "ISSN": "0037-5497", "EISSN": "1741-3133", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kazakh-British Technical University, Republic of Kazakhstan;Al-Farabi Kazakh National University, Republic of Kazakhstan;International Information Technology University, Republic of Kazakhstan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kazakh-British Technical University, Republic of Kazakhstan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Kazakh-British Technical University, Republic of Kazakhstan"}], "References": [{"Title": "Numerical study of dam-break fluid flow using volume of fluid (VOF) methods for different angles of inclined planes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "97", "Issue": "11", "Page": "717", "JournalTitle": "SIMULATION"}, {"Title": "Hybrid simulation model for navigation performance evaluation of the Three Gorges–Gezhouba Dams under novel regulations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "8", "Page": "677", "JournalTitle": "SIMULATION"}, {"Title": "Dynamic risk assessment of slope stability of homogeneous earth-rock dam under action of multiple hazards", "Authors": "<PERSON><PERSON> Su; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "8", "Page": "699", "JournalTitle": "SIMULATION"}]}, {"ArticleId": 118201911, "Title": "Automated Audible Truck-Mounted Attenuator Alerts: Vision System Development and Evaluation", "Abstract": "<p>Background: The rise in work zone crashes due to distracted and aggressive driving calls for improved safety measures. While Truck-Mounted Attenuators (TMAs) have helped reduce crash severity, the increasing number of crashes involving TMAs shows the need for improved warning systems. Methods: This study proposes an AI-enabled vision system to automatically alert drivers on collision courses with TMAs, addressing the limitations of manual alert systems. The system uses multi-task learning (MTL) to detect and classify vehicles, estimate distance zones (danger, warning, and safe), and perform lane and road segmentation. MTL improves efficiency and accuracy, making it ideal for devices with limited resources. Using a Generalized Efficient Layer Aggregation Network (GELAN) backbone, the system enhances stability and performance. Additionally, an alert module triggers alarms based on speed, acceleration, and time to collision. Results: The model achieves a recall of 90.5%, an mAP of 0.792 for vehicle detection, an mIOU of 0.948 for road segmentation, an accuracy of 81.5% for lane segmentation, and 83.8% accuracy for distance classification. Conclusions: The results show the system accurately detects vehicles, classifies distances, and provides real-time alerts, reducing TMA collision risks and enhancing work zone safety.</p>", "Keywords": "", "DOI": "10.3390/ai5040090", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 69255, "JournalTitle": "AI", "ISSN": "", "EISSN": "2673-2688", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Missouri, Columbia, MO 65211, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Missouri, Columbia, MO 65211, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Missouri, Columbia, MO 65211, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Missouri, Columbia, MO 65211, USA"}], "References": [{"Title": "Ehsinet: Efficient High-Order Spatial Interaction Multi-task Network for Adaptive Autonomous Driving Perception", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "8", "Page": "11353", "JournalTitle": "Neural Processing Letters"}, {"Title": "YOLOPX: Anchor-free multi-task learning network for panoptic driving perception", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "148", "Issue": "", "Page": "110152", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 118201995, "Title": "Beyond the stereotypes: Artificial Intelligence image generation and diversity in anesthesiology", "Abstract": "Introduction <p>Artificial Intelligence (AI) is increasingly being integrated into anesthesiology to enhance patient safety, improve efficiency, and streamline various aspects of practice.</p> Objective <p>This study aims to evaluate whether AI-generated images accurately depict the demographic racial and ethnic diversity observed in the Anesthesia workforce and to identify inherent social biases in these images.</p> Methods <p>This cross-sectional analysis was conducted from January to February 2024. Demographic data were collected from the American Society of Anesthesiologists (ASA) and the European Society of Anesthesiology and Intensive Care (ESAIC). Two AI text-to-image models, ChatGPT DALL-E 2 and Midjourney, generated images of anesthesiologists across various subspecialties. Three independent reviewers assessed and categorized each image based on sex, race/ethnicity, age, and emotional traits.</p> Results <p>A total of 1,200 images were analyzed. We found significant discrepancies between AI-generated images and actual demographic data. The models predominantly portrayed anesthesiologists as White, with ChatGPT DALL-E2 at 64.2% and Midjourney at 83.0%. Moreover, male gender was highly associated with White ethnicity by ChatGPT DALL-E2 (79.1%) and with non-White ethnicity by Midjourney (87%). Age distribution also varied significantly, with younger anesthesiologists underrepresented. The analysis also revealed predominant traits such as “masculine, ““attractive, “and “trustworthy” across various subspecialties.</p> Conclusion <p>AI models exhibited notable biases in gender, race/ethnicity, and age representation, failing to reflect the actual diversity within the anesthesiologist workforce. These biases highlight the need for more diverse training datasets and strategies to mitigate bias in AI-generated images to ensure accurate and inclusive representations in the medical field.</p>", "Keywords": "Artificial Intelligence;anesthesiology;biases;gender equity;race/ethnicity;stereotypes", "DOI": "10.3389/frai.2024.1462819", "PubYear": 2024, "Volume": "7", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Division of Anesthesiology, Department of Anesthesiology, Clinical Pharmacology, Intensive Care and Emergency Medicine, Faculty of Medicine, Geneva University Hospitals, Geneva, Switzerland."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Anesthesia, Antwerp University Hospital, Edegem, Belgium."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Anesthesiology and Intensive Care Unit, University of Ankara School of Medicine, Ankara, Türkiye."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Division of Anesthesiology, Department of Anesthesiology, Clinical Pharmacology, Intensive Care and Emergency Medicine, Faculty of Medicine, Geneva University Hospitals, Geneva, Switzerland."}, {"AuthorId": 5, "Name": "Basak Ceyda Meco", "Affiliation": "Department of Anesthesiology and Intensive Care Unit, University of Ankara School of Medicine, Ankara, Türkiye. ;Ankara University Brain Research Center (AÜBAUM), Ankara, Türkiye."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "B-STAT, Biostatistics and Research Method Center of ULiège and CHU of Liège, Liege, Belgium."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "B-STAT, Biostatistics and Research Method Center of ULiège and CHU of Liège, Liege, Belgium."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Anesthesia and Perioperative Care, University of California San Francisco, San Francisco, CA, United States."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Anesthesia and Reanimation, AZ Sint-Jan Brugge Oostende AV, Brugge, Belgium."}, {"AuthorId": 10, "Name": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute for Medical Education, University of Bern, Bern, Switzerland. ;CINTESIS@RISE, Centre for Health Technology and Services Research, Faculty of Medicine, University of Porto, Porto, Portugal. ;Institute for Anesthesiology and Intensive Care, Salemspital, Hirslanden Medical Group, Bern, Switzerland."}], "References": [{"Title": "Exposing implicit biases and stereotypes in human and artificial intelligence: state of the art and challenges with a focus on gender", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "38", "Issue": "2", "Page": "747", "JournalTitle": "AI & SOCIETY"}, {"Title": "Algorithmic fairness and bias mitigation for clinical machine learning with deep reinforcement learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "8", "Page": "884", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 118202142, "Title": "Presenting a hybrid method for fault tolerance based on neural fuzzy logic in distribution networks using phasor measurement units", "Abstract": "<p>In this paper, a hybrid approach to increase the performance reliability of power distribution networks using phasor measurement units (PMU) is presented. Electricity distribution networks are very important as a vital part of electricity transmission systems in providing energy to users. Considering the ever-increasing complexity and the huge amount of existing demand, maintaining the optimal performance and reliability of these networks is vital. One of the main challenges in this industry is identifying and finding problems. In this regard, improvements in phasor measurement technology using phasor measurement units (PMU) allow engineers in this industry to more accurately evaluate data, diagnose and locate network faults. In this research, one of the non-linear methods for finding ground faults in power distribution networks using voltage phasor measurement in several network stations using phasor measurement units (D-PMU) has been demonstrated. In the first approach, genetic optimization algorithms and bullet optimization algorithm (PSO) have been used for nonlinear modeling of fault position estimation based on different types of 1-phase, 2-phase and 3-phase faults. The second method uses fuzzy network training to provide details about phasor voltages and fault types. By simulating a 9-station system using MATLAB software, the usefulness of the proposed methods has been shown. In modeling, 1-phase, 2-phase and 3-phase faults along with different line lengths and line characteristics at different stations have been investigated. Also, the findings are presented and the location of the defect is identified immediately.</p>", "Keywords": "PMUs; Fault location; Power distribution network; Neural fuzzy; Genetic optimization algorithms; Particle swarm optimization", "DOI": "10.1007/s12652-024-04876-x", "PubYear": 2024, "Volume": "15", "Issue": "12", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangxi Vocational and Technical College of Communication, Nanchang, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangxi Vocational and Technical College of Communication, Nanchang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangxi Vocational and Technical College of Communication, Nanchang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangxi Vocational and Technical College of Communication, Nanchang, China"}], "References": [{"Title": "PBPHS: A Profile-Based Predictive Handover Strategy for 5G Networks", "Authors": "<PERSON><PERSON><PERSON> Sun; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "55", "Issue": "5", "Page": "1041", "JournalTitle": "Cybernetics and Systems"}, {"Title": "Membership Functions, Set-Theoretic Operations, Distance Measurement Methods Based on Ambiguous Set Theory: A Solution to a Decision-Making Problem in Selecting the Appropriate Colleges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "4", "Page": "1311", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Increasing efficiency for routing in internet of things using Binary Gray Wolf Optimization and fuzzy logic", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "9", "Page": "101732", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "A Four-Valued Ambiguous Logic: Application in Designing Ambiguous Inference System for Control Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "8", "Page": "2921", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Recognizing sports activities from video frames using deformable convolution and adaptive multiscale features", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "Multi-objective optimization of a laterally perforated-finned heat sink with computational fluid dynamics method and statistical modeling using response surface methodology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "130", "Issue": "", "Page": "107674", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An efficient approach for multi-label classification based on Advanced Kernel-Based Learning System", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "21", "Issue": "", "Page": "200332", "JournalTitle": "Intelligent Systems with Applications"}, {"Title": "Edge intelligence-assisted animation design with large models: a survey", "Authors": "Jing Zhu; Chuanjiang Hu; <PERSON><PERSON>", "PubYear": 2024, "Volume": "13", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "Link prediction in social networks using hyper-motif representation on hypergraph", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "30", "Issue": "3", "Page": "1", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 118202146, "Title": "Audio Deep Fake Detection with Sonic Sleuth Model", "Abstract": "<p>Information dissemination and preservation are crucial for societal progress, especially in the technological age. While technology fosters knowledge sharing, it also risks spreading misinformation. Audio deepfakes—convincingly fabricated audio created using artificial intelligence (AI)—exacerbate this issue. We present Sonic Sleuth, a novel AI model designed specifically for detecting audio deepfakes. Our approach utilizes advanced deep learning (DL) techniques, including a custom CNN model, to enhance detection accuracy in audio misinformation, with practical applications in journalism and social media. Through meticulous data preprocessing and rigorous experimentation, we achieved a remarkable 98.27% accuracy and a 0.016 equal error rate (EER) on a substantial dataset of real and synthetic audio. Additionally, Sonic Sleuth demonstrated 84.92% accuracy and a 0.085 EER on an external dataset. The novelty of this research lies in its integration of datasets that closely simulate real-world conditions, including noise and linguistic diversity, enabling the model to generalize across a wide array of audio inputs. These results underscore Sonic Sleuth’s potential as a powerful tool for combating misinformation and enhancing integrity in digital communications.</p>", "Keywords": "", "DOI": "10.3390/computers13100256", "PubYear": 2024, "Volume": "13", "Issue": "10", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah P.O. Box 80221, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah P.O. Box 80221, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah P.O. Box 80221, Saudi Arabia; Center of Research Excellence in Artificial Intelligence and Data Science, King Abdulaziz University, Jeddah, P.O. Box 80221, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah P.O. Box 80221, Saudi Arabia; Center of Research Excellence in Artificial Intelligence and Data Science, King Abdulaziz University, Jeddah, P.O. Box 80221, Saudi Arabia"}], "References": [{"Title": "ASVspoof 2019: A large-scale public database of synthesized, converted and replayed speech", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "101114", "JournalTitle": "Computer Speech & Language"}]}, {"ArticleId": 118202163, "Title": "A Bag-of-Words Approach for Information Extraction from Electricity Invoices", "Abstract": "<p>In the context of digitization and automation, extracting relevant information from business documents remains a significant challenge. It is typical to rely on machine-learning techniques to automate the process, reduce manual labor, and minimize errors. This work introduces a new model for extracting key values from electricity invoices, including customer data, bill breakdown, electricity consumption, or marketer data. We evaluate several machine learning techniques, such as Naive Bayes, Logistic Regression, Random Forests, or Support Vector Machines. Our approach relies on a bag-of-words strategy and custom-designed features tailored for electricity data. We validate our method on the IDSEM dataset, which includes 75,000 electricity invoices with eighty-six fields. The model converts PDF invoices into text and processes each word separately using a context of eleven words. The results of our experiments indicate that Support Vector Machines and Random Forests perform exceptionally well in capturing numerous values with high precision. The study also explores the advantages of our custom features and evaluates the performance of unseen documents. The precision obtained with Support Vector Machines is 91.86% on average, peaking at 98.47% for one document template. These results demonstrate the effectiveness of our method in accurately extracting key values from invoices.</p>", "Keywords": "", "DOI": "10.3390/ai5040091", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 69255, "JournalTitle": "AI", "ISSN": "", "EISSN": "2673-2688", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centro de Tecnologías de la Imagen (CTIM), Instituto Universitario de Cibernética, Empresas y Sociedad (IUCES), 3507 Las Palmas de Gran Canaria, Spain"}, {"AuthorId": 2, "Name": "Giovanny A<PERSON>-Londoño", "Affiliation": "Centro de Tecnologías de la Imagen (CTIM), Instituto Universitario de Cibernética, Empresas y Sociedad (IUCES), 3507 Las Palmas de Gran Canaria, Spain"}], "References": []}, {"ArticleId": 118202251, "Title": "Development of an algorithm for the functioning of a system for remote monitoring of the psycho-physiological state of a person", "Abstract": "", "Keywords": "", "DOI": "10.15276/imms.v14.no1-2.5", "PubYear": 2024, "Volume": "14", "Issue": "1-2", "JournalId": 57994, "JournalTitle": "INFORMATICS AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> METHODS IN SIMULATION", "ISSN": "2223-5744", "EISSN": "2226-1923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dniprovsky State Technical University 2, Dniprobudivska str., Kamianske, 51918, Ukraine"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118202301, "Title": "Forecasting inpatient admissions in district hospitals: a hybrid model approach", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10844-024-00895-4", "PubYear": 2025, "Volume": "63", "Issue": "2", "JournalId": 7081, "JournalTitle": "Journal of Intelligent Information Systems", "ISSN": "0925-9902", "EISSN": "1573-7675", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Forecasting emergency department admissions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "56", "Issue": "3", "Page": "509", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Forecasting and explaining emergency department visits in a public hospital", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "59", "Issue": "2", "Page": "479", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Machine learning methods for predicting the admissions and hospitalisations in the emergency department of a civil and military hospital", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON> R<PERSON>", "PubYear": 2023, "Volume": "61", "Issue": "3", "Page": "881", "JournalTitle": "Journal of Intelligent Information Systems"}]}, {"ArticleId": 118202367, "Title": "Knowledge Transfer from High-Resource to Low-Resource Programming Languages for Code LLMs", "Abstract": "<p>Over the past few years, Large Language Models of Code (Code LLMs) have started to have a significant impact on programming practice. Code LLMs are also emerging as building blocks for research in programming languages and software engineering. However, the quality of code produced by a Code LLM varies significantly by programming language. Code LLMs produce impressive results on high-resource programming languages that are well represented in their training data (e.g., Java, Python, or JavaScript), but struggle with low-resource languages that have limited training data available (e.g., OCaml, Racket, and several others). This paper presents an effective approach for boosting the performance of Code LLMs on low-resource languages using semi-synthetic data. Our approach, called MultiPL-T, generates high-quality datasets for low-resource languages, which can then be used to fine-tune any pretrained Code LLM. MultiPL-T translates training data from high-resource languages into training data for low-resource languages in the following way. 1) We use a Code LLM to synthesize unit tests for commented code from a high-resource source language, filtering out faulty tests and code with low test coverage. 2) We use a Code LLM to translate the code from the high-resource source language to a target low-resource language. This gives us a corpus of candidate training data in the target language, but many of these translations are wrong. 3) We use a lightweight compiler to compile the test cases generated in (1) from the source language to the target language, which allows us to filter our obviously wrong translations. The result is a training corpus in the target low-resource language where all items have been validated with test cases. We apply this approach to generate tens of thousands of new, validated training items for five low-resource languages: Julia, Lua, OCaml, R, and Racket, using Python as the source high-resource language. Furthermore, we use an open Code LLM (StarCoderBase) with open training data (The Stack), which allows us to decontaminate benchmarks, train models without violating licenses, and run experiments that could not otherwise be done. Using datasets generated with MultiPL-T, we present fine-tuned versions of StarCoderBase and Code Llama for Julia, Lua, OCaml, R, and Racket that outperform other fine-tunes of these base models on the natural language to code task. We also present Racket fine-tunes for two very recent models, DeepSeek Coder and StarCoder2, to show that MultiPL-T continues to outperform other fine-tuning approaches for low-resource languages. The MultiPL-T approach is easy to apply to new languages, and is significantly more efficient and effective than alternatives such as training longer.</p>", "Keywords": "", "DOI": "10.1145/3689735", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Northeastern University, Boston, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Northeastern University, Boston, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Northeastern University, Boston, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Northeastern University, Boston, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Wellesley College, Wellesley, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Wellesley College, Wellesley, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Oberlin College, Oberlin, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Stevens Institute of Technology, Hoboken, USA"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Microsoft Research, Redmond, USA"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northeastern University, Northeastern, USA / Roblox, San Mateo, USA"}], "References": []}, {"ArticleId": 118202371, "Title": "Automating Pruning in Top-Down Enumeration for Program Synthesis Problems with Monotonic Semantics", "Abstract": "<p>In top-down enumeration for program synthesis, abstraction-based pruning uses an abstract domain to approximate the set of possible values that a partial program, when completed, can output on a given input. If the set does not contain the desired output, the partial program and all its possible completions can be pruned. In its general form, abstraction-based pruning requires manually designed, domain-specific abstract domains and semantics, and thus has only been used in domain-specific synthesizers. This paper provides sufficient conditions under which a form of abstraction-based pruning can be automated for arbitrary synthesis problems in the general-purpose Semantics-Guided Synthesis (SemGuS) framework without requiring manually-defined abstract domains. We show that if the semantics of the language for which we are synthesizing programs exhibits some monotonicity properties, one can obtain an abstract interval-based semantics for free from the concrete semantics of the programming language, and use such semantics to effectively prune the search space. We also identify a condition that ensures such abstract semantics can be used to compute a precise abstraction of the set of values that a program derivable from a given hole in a partial program can produce. These precise abstractions make abstraction-based pruning more effective. We implement our approach in a tool, <PERSON><PERSON>, which can tackle synthesis problems defined in the SemGuS framework. <PERSON><PERSON> can automate interval-based pruning without any a-priori knowledge of the problem domain, and solve synthesis problems that previously required domain-specific, abstraction-based synthesizers— e.g., synthesis of regular expressions, CSV file schema, and imperative programs from examples.</p>", "Keywords": "", "DOI": "10.1145/3689744", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Wisconsin-Madison, Madison, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Wisconsin-Madison, Madison, USA"}, {"AuthorId": 3, "Name": "Thomas Reps", "Affiliation": "University of Wisconsin-Madison, Madison, USA"}, {"AuthorId": 4, "Name": "<PERSON>s <PERSON>", "Affiliation": "University of California at San Diego, San Diego, USA"}], "References": [{"Title": "Combining the top-down propagation and bottom-up enumeration for inductive program synthesis", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Absynthe: Abstract Interpretation-Guided Synthesis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "1584", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Inductive Program Synthesis via Iterative Forward-Backward Abstract Interpretation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "1657", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Optimal Program Synthesis via Abstract Interpretation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "Page": "457", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118202375, "Title": "Intensional Functions", "Abstract": "<p> Functions in functional languages have a single elimination form — application — and cannot be compared, hashed, or subjected to other non-application operations. These operations can be approximated via defunctionalization: functions are replaced with first-order data and calls are replaced with invocations of a dispatch function. Operations such as comparison may then be implemented for these first-order data to approximate e.g. deduplication of continuations in algorithms such as unbounded searches. Unfortunately, this encoding is tedious, imposes a maintenance burden, and obfuscates the affected code. We introduce an alternative in intensional functions , a language feature which supports the definition of non-application operations in terms of a function’s definition site and closure-captured values. First-order data operations may be defined on intensional functions without burdensome code transformation. We give an operational semantics and type system and prove their formal properties. We further define intensional monads , whose Kleisli arrows are intensional functions, enabling monadic values to be similarly subjected to additional operations. </p>", "Keywords": "", "DOI": "10.1145/3689714", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Swarthmore College, Swarthmore, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Microsoft, Cambridge, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Johns Hopkins University, Baltimore, USA"}], "References": [{"Title": "A quick look at impredicativity", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "ICFP", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Intensional Functions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "Page": "87", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Intensional Functions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "Page": "87", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Intensional Functions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "Page": "87", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Intensional Functions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "Page": "87", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118202377, "Title": "Compositionality and Observational Refinement for Linearizability with Crashes", "Abstract": "<p> Crash-safety is an important property of real systems, as the main functionality of some systems is resilience to crashes. Toward a compositional verification approach for crash-safety under full-system crashes, one observes that crashes propagate instantaneously to all components across all levels of abstraction, even to unspecified components, hindering compositionality. Furthermore, in the presence of concurrency, a correctness criterion that addresses both crashes and concurrency proves necessary. For this, several adaptations of linearizability have been suggested, each featuring different trade-offs between complexity and expressiveness. The recently proposed compositional linearizability framework shows that to achieve compositionality with linearizability, both a locality and observational refinement property are necessary. Despite that, no linearizability criterion with crashes has been proven to support an observational refinement property. In this paper, we define a compositional model of concurrent computation with full-system crashes. We use this model to develop a compositional theory of linearizability with crashes, which reveals a criterion, crash-aware linearizability , as its inherent notion of linearizability and supports both locality and observational refinement. We then show that strict linearizability and durable linearizability factor through crash-aware linearizability as two different ways of translating between concurrent computation with and without crashes, enabling simple proofs of locality and observational refinement for a generalization of these two criteria. Then, we show how the theory can be connected with a program logic for durable and crash-aware linearizability, which gives the first program logic that verifies a form of linearizability with crashes. We showcase the advantages of compositionality by verifying a library facilitating programming persistent data structures and a fragment of a transactional interface for a file system. </p>", "Keywords": "", "DOI": "10.1145/3689792", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Yale University, New Haven, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Yale University, New Haven, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Yale University, New Haven, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Yale University, New Haven, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Yale University, New Haven, USA"}], "References": [{"Title": "Persistency semantics of the Intel-x86 architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Layered and object-based game semantics", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "The Path to Durable Linearizability", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "748", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A Compositional Theory of Linearizability", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "1089", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A Compositional Theory of Linearizability", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "71", "Issue": "2", "Page": "1", "JournalTitle": "Journal of the ACM"}, {"Title": "Compositionality and Observational Refinement for Linearizability with Crashes", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "Page": "2296", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118202388, "Title": "Modular Synthesis of Efficient Quantum Uncomputation", "Abstract": "<p>A key challenge of quantum programming is uncomputation: the reversible deallocation of qubits. And while there has been much recent progress on automating uncomputation, state-of-the-art methods are insufficient for handling today’s expressive quantum programming languages. A core reason is that they operate on primitive quantum circuits, while quantum programs express computations beyond circuits, for instance, they can capture families of circuits defined recursively in terms of uncomputation and adjoints. In this paper, we introduce the first modular automatic approach to synthesize correct and efficient uncomputation for expressive quantum programs. Our method is based on two core technical contributions: (i) an intermediate representation (IR) that can capture expressive quantum programs and comes with support for uncomputation, and (ii) modular algorithms over that IR for synthesizing uncomputation and adjoints. We have built a complete end-to-end implementation of our method, including an implementation of the IR and the synthesis algorithms, as well as a translation from an expressive fragment of the Silq programming language to our IR and circuit generation from the IR. Our experimental evaluation demonstrates that we can handle programs beyond the capabilities of existing uncomputation approaches, while being competitive on the benchmarks they can handle. More broadly, we show that it is possible to benefit from the greater expressivity and safety offered by high-level quantum languages without sacrificing efficiency.</p>", "Keywords": "", "DOI": "10.1145/3689785", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Sofia University St. Klim<PERSON>, Sofia, Bulgaria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ETH Zurich, Zürich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sofia University St. Klim<PERSON>, Sofia, Bulgaria"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ETH Zurich, Zürich, Switzerland / Sofia University St. Kliment Ohridski, Sofia, Bulgaria"}], "References": [{"Title": "Quantum information effects", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "QIRO: A Static Single Assignment-based Quantum Program Representation for Optimization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Quantum Computing"}, {"Title": "Tower: data structures in Quantum superposition", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA2", "Page": "259", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Qunity: A Unified Language for Quantum and Classical Computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "921", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118202443, "Title": "Siam2C: Siamese visual segmentation and tracking with classification-rank loss and classification-aware", "Abstract": "<p>Siamese visual trackers based on segmentation have garnered considerable attention due to their high accuracy. However, these trackers rely solely on simple classification confidence to distinguish between positive and negative samples (foreground or background), lacking more precise discrimination capabilities for objects. Moreover, the backbone network excels at focusing on local information during feature extraction, failing to capture the long-distance contextual semantics crucial for classification. Consequently, these trackers are highly susceptible to interference during actual tracking, leading to erroneous object segmentation and subsequent tracking failures, thereby compromising robustness. For this purpose, we propose a Siamese visual segmentation and tracking network with classification-rank loss and classification-aware (Siam2C). We design a classification-rank loss (CRL) algorithm to enlarge the margin between positive and negative samples, ensuring that positive samples are ranked higher than negative ones. This optimization enhances the network’s ability to learn from positive and negative samples, allowing the tracker to accurately select the object for segmentation and tracking rather than being misled by interfering targets. Additionally, we design a classification-aware attention module (CAM), which employs spatial and channel self-attention mechanisms to capture long-distance dependencies between different positions in the feature map. The module enhances the feature representation capability of the backbone network, providing richer global contextual semantic information for the tracking network’s classification decisions. Extensive experiments on the VOT2016, VOT2018, VOT2019, OTB100, UAV123, GOT-10k, DAVIS2016, and DAVIS2017 datasets demonstrate the outstanding performance of Siam2C.</p>", "Keywords": "Siamese network; Visual object tracking; Video object segmentation; Self-attention; Classification-Aware", "DOI": "10.1007/s10489-024-05840-0", "PubYear": 2024, "Volume": "54", "Issue": "24", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Digital Finance Innovation, Hubei University of Economics, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Intelligent Vision Based Monitoring for Hydroelectric Engineering, China Three Gorges University, Yichang, China; College of Computer and Information Technology, China Three Gorges University, Yichang, China; Yichang Key Laboratory of Intelligent Vision Based Monitoring for Hydroelectric Engineering, China Three Gorges University, Yichang, China; Corresponding author."}, {"AuthorId": 3, "Name": "Weisheng Li", "Affiliation": "Chongqing Key Laborotory of Image Rocognition, Chongqing, China; Chongqing University of Posts and Telecommications, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON>o T<PERSON>", "Affiliation": "Hubei Key Laboratory of Digital Finance Innovation, Hubei University of Economics, Wuhan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Key Laborotory of Image Rocognition, Chongqing, China; Chongqing University of Posts and Telecommications, Chongqing, China"}], "References": [{"Title": "Siamese block attention network for online update object tracking", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "3", "Page": "3459", "JournalTitle": "Applied Intelligence"}, {"Title": "Multi‐template temporal information fusion for Siamese object tracking", "Authors": "<PERSON><PERSON> Lu; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "1", "Page": "51", "JournalTitle": "IET Computer Vision"}, {"Title": "Self-aware circular response-guided attention for robust siamese tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "12", "Page": "16293", "JournalTitle": "Applied Intelligence"}, {"Title": "Siamese residual network for efficient visual tracking", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "606", "JournalTitle": "Information Sciences"}, {"Title": "Multi-template global re-detection based on Gumbel-Softmax in long-term visual tracking", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Jingyuan Ma; Wangsheng Yu", "PubYear": 2023, "Volume": "53", "Issue": "18", "Page": "20874", "JournalTitle": "Applied Intelligence"}, {"Title": "Hierarchical Siamese network for real-time visual tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121651", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Learning rich feature representation and aggregation for accurate visual tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "23", "Page": "28114", "JournalTitle": "Applied Intelligence"}, {"Title": "Adaptive distractor-aware for siamese tracking via enhancement confidence evaluator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "23", "Page": "29223", "JournalTitle": "Applied Intelligence"}, {"Title": "Efficient object tracking algorithm based on lightweight Siamese networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Hong<PERSON> Wang", "PubYear": 2024, "Volume": "133", "Issue": "", "Page": "107976", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "SCATT: Transformer tracking with symmetric cross-attention", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "54", "Issue": "8", "Page": "6069", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 118202466, "Title": "Disentangling Contact Location for Stretchable Tactile Sensors from Soft Waveguide Ultrasonic Scatter Signals", "Abstract": "Flexible tactile sensors have the ability to provide unparalleled levels of tactile sensation, including information regarding roughness, contact force, and contact location. However, it remains a challenge to achieve precise contact location sensing that is decoupled from sensor strain and touching forces. This paper proposes a novel data‐driven approach for force contact location sensing (FCLS) with the influence of sensor strain and forces based on scatter signals (SS) of the ultrasonic waveguide. First, the envelope of the force contact scatter signal (FCSS) is extracted via the Hilbert transform, which retrieves the global features of SS. The time‐frequency spectrogram is obtained via continuous wavelet transform, which extracts the local features of SS. Second, a deep convolutional neural network (CNN) is utilized to extract these features separately and concentrate them together. Third, based on the outputs of the CNN, a multilayer perception regression model is applied to acquire the force contact location. The experimental results indicate that the accuracy of the proposed FCLS method has a mean absolute error of 0.627 mm and a mean relative error of 3.19%. This research provides a foundation for further multimodal sensing using ultrasonic waveguides and its application in robotic sensing.", "Keywords": "", "DOI": "10.1002/aisy.202400561", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering The Chinese University of Hong Kong  Hong Kong 999077 Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering The Chinese University of Hong Kong  Hong Kong 999077 Hong Kong;School of Mechanical Engineering Shanghai Jiao Tong University  Shanghai 201100 China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering Shanghai Jiao Tong University  Shanghai 201100 China"}, {"AuthorId": 4, "Name": "<PERSON>lian<PERSON> Ren", "Affiliation": "Department of Electronic Engineering The Chinese University of Hong Kong  Hong Kong 999077 Hong Kong"}], "References": [{"Title": "Soft magnetic skin for super-resolution tactile sensing with force self-decoupling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "51", "Page": "eabc8801", "JournalTitle": "Science Robotics"}, {"Title": "Soft Tactile Sensing Skins for Robotics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "343", "JournalTitle": "Current Robotics Reports"}, {"Title": "Real-time motion control of robotic manipulators for safe human–robot coexistence", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102223", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Minsight: A Fingertip‐Sized Vision‐Based Tactile Sensor for Robotic Manipulation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "8", "Page": "2300042", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "A Learning‐Based Sensor Array for Untethered Soft Prosthetic Hand Aiming at Restoring Tactile Sensation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "6", "Issue": "10", "Page": "", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Fingerprint-inspired biomimetic tactile sensors for the surface texture recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "371", "Issue": "", "Page": "115275", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 118202660, "Title": "Study on Non-iterative Algorithms for Center-of-Sets Type-Reduction of Interval Type-2 Takagi–Sugeno–Kang Fuzzy Logic Systems", "Abstract": "<p>In the application of interval type-2 (IT2) <PERSON><PERSON><PERSON>–<PERSON><PERSON> (TSK) fuzzy logic systems (FLSs), the center-of-sets (COS) type-reduction (TR) is more advantageous than the centroid TR. This paper proposes three types of discrete non-iterative algorithms to solve the problem of COS TR in IT2 TSK FLSs. Multiple simulation experiments are carried out for the IT2 TSK FLSs with different fuzzy rule numbers. Experimental results show that the computational efficiencies of the three discrete non-iterative algorithms are better than that of <PERSON><PERSON><PERSON> (KM) algorithms, which provides latent value for the application of type-2 FLSs.</p>", "Keywords": "Interval type-2 fuzzy logic systems; Center-of-sets type-reduction; KM algorithms; Non-iterative algorithms; Computational efficiency", "DOI": "10.1007/s40815-024-01873-2", "PubYear": 2024, "Volume": "26", "Issue": "8", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Science, Liaoning University of Technology, Jinzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Science, Liaoning University of Technology, Jinzhou, China; Corresponding author."}], "References": [{"Title": "Comparative study of interval Type-2 and general Type-2 fuzzy systems in medical diagnosis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "525", "Issue": "", "Page": "37", "JournalTitle": "Information Sciences"}, {"Title": "Study on sampling-based discrete noniterative algorithms for centroid type-reduction of interval type-2 fuzzy logic systems", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "15", "Page": "11819", "JournalTitle": "Soft Computing"}, {"Title": "An Efficient High-Order α-Plane Aggregation in General Type-2 Fuzzy Systems Using Newton–Cotes Rules", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "4", "Page": "1102", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Optimal Setting of Membership Functions for Interval Type-2 Fuzzy Tracking Controllers Using a Shark Smell Metaheuristic Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; Prometeo Co<PERSON>s", "PubYear": 2022, "Volume": "24", "Issue": "2", "Page": "799", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Design and application of Nagar-Bardini structure-based interval type-2 fuzzy logic systems optimized with the combination of backpropagation algorithms and recursive least square algorithms", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118596", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Design of Takagi Sugeno Kang Type Interval Type-2 Fuzzy Logic Systems Optimized with Hybrid Algorithms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "2", "Page": "868", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Interval type-2 fuzzy neural networks with asymmetric MFs based on the twice optimization algorithm for nonlinear system identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "629", "Issue": "", "Page": "123", "JournalTitle": "Information Sciences"}, {"Title": "Design of Discrete Noniterative Algorithms for Center-of-Sets Type Reduction of General Type-2 Fuzzy Logic Systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "4", "Page": "2024", "JournalTitle": "International Journal of Fuzzy Systems"}]}, {"ArticleId": 118202968, "Title": "Multi-objective scheduling for an energy-efficient flexible job shop problem with peak power constraint", "Abstract": "Controlling electricity costs, which are closely related to the peak power reached, while simultaneously reducing completion time and energy consumption, is crucial for achieving sustainability in the manufacturing industry. However, most existing research on energy-efficient flexible job shop scheduling problems primarily focuses on optimizing completion time and energy consumption, often neglecting the critical aspect of controlling electricity costs. With decreasing resources and increasing energy prices, it is necessary to control electricity costs by limiting the peak power reached during production. To address this gap, this paper investigates an energy-efficient flexible job shop scheduling problem with peak power constraint considering setup and transportation time (EEFJSSP-PPST). We establish a mathematical model for EEFJSSP-PPST and propose an improved non-dominated sorting genetic algorithm II (INSGA-II) incorporating several key improvements. We first introduce three scheduling rules in decoding to optimize objectives and employ heuristic rules to generate a high-quality initial population. Then, we propose an innovative cluster crossover method to accelerate convergence and design an adaptive-based local optimization strategy to further enhance performance. Finally, our experiments explore the impacts of different degrees of the peak power constraint on objectives and evaluate the effectiveness of the three scheduling rules. Additionally, the performance of INSGA-II is tested using 135 benchmark instances. The results indicate that INSGA-II outperformed its variations without improvements, achieving the best hypervolume (HV) indicator in 80% of the instances and the best inverted generational distance (IGD) indicator in 68.89% of the instances. Compared to other state-of-the-art algorithms, INSGA-II achieves the best HV and IGD indicators in 82.2% and 81.5% of the instances, respectively, and demonstrates better convergence and a superior Pareto front. Therefore, we conclude that the improvements in INSGA-II are effective and enhance the algorithm’s performance, making it outstanding in solving EEFJSSP-PPST.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.112330", "PubYear": 2024, "Volume": "167", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Jiangsu University, Zhenjiang, Jiangsu, 212013, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Jiangsu University, Zhenjiang, Jiangsu, 212013, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Jiangsu University, Zhenjiang, Jiangsu, 212013, PR China"}], "References": [{"Title": "A review of energy-efficient scheduling in intelligent production systems", "Authors": "Kaizhou Gao; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "237", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "An improved genetic algorithm for the flexible job shop scheduling problem with multiple time constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100664", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Particle filter and Levy flight-based decomposed multi-objective evolution hybridized particle swarm for flexible job shop greening scheduling with crane transportation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106217", "JournalTitle": "Applied Soft Computing"}, {"Title": "An improved Jaya algorithm for solving the flexible job shop scheduling problem with transportation and setup times", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "106032", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An improved artificial bee colony algorithm for solving multi-objective low-carbon flexible job shop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106544", "JournalTitle": "Applied Soft Computing"}, {"Title": "An improved non-dominated sorting biogeography-based optimization algorithm for the (hybrid) multi-objective flexible job-shop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "106869", "JournalTitle": "Applied Soft Computing"}, {"Title": "An imperialist competitive algorithm with feedback for energy-efficient flexible job shop scheduling with transportation and sequence-dependent setup times", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "104307", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Energy-efficient scheduling for a flexible job shop with machine breakdowns considering machine idle time arrangement and machine speed level selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "161", "Issue": "", "Page": "107677", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Evolutionary algorithms for multi-objective flexible job shop cell scheduling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107890", "JournalTitle": "Applied Soft Computing"}, {"Title": "Hybrid energy-efficient scheduling measures for flexible job-shop problem with variable machining speeds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "197", "Issue": "", "Page": "116785", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A two-stage memetic algorithm for energy-efficient flexible job shop scheduling by means of decreasing the total number of machine restarts", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101131", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Memetic algorithm based on learning and decomposition for multiobjective flexible job shop scheduling considering human factors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101204", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A multi-agent system for FJSP with setup and transportation times", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "216", "Issue": "", "Page": "119474", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Pareto-based two-stage evolutionary algorithm for flexible job shop scheduling problem with worker cooperation flexibility", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "", "Page": "102534", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A Pareto Front grid guided multi-objective evolutionary algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "110095", "JournalTitle": "Applied Soft Computing"}, {"Title": "Knowledge-driven two-stage memetic algorithm for energy-efficient flexible job shop scheduling with machine breakdowns", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "235", "Issue": "", "Page": "121149", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Energy-efficient flexible job shop scheduling problem considering discrete operation sequence flexibility", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "84", "Issue": "", "Page": "101421", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "An improved MOEA/D for low-carbon many-objective flexible job shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "188", "Issue": "", "Page": "109926", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Deep reinforcement learning-based memetic algorithm for energy-aware flexible job shop scheduling with multi-AGV", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "189", "Issue": "", "Page": "109917", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Hybrid quantum particle swarm optimization and variable neighborhood search for flexible job-shop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "73", "Issue": "", "Page": "334", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A multidimensional probabilistic model based evolutionary algorithm for the energy-efficient distributed flexible job-shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "135", "Issue": "", "Page": "108841", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Knowledge-based multi-objective evolutionary algorithm for energy-efficient flexible job shop scheduling with mobile robot transportation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "62", "Issue": "", "Page": "102647", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A Pareto-optimality based black widow spider algorithm for energy efficient flexible job shop scheduling problem considering new job insertion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "164", "Issue": "", "Page": "111937", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 118203010, "Title": "Dynamic frequent subgraph mining algorithms over evolving graphs: a survey", "Abstract": "<p>Frequent subgraph mining (FSM) is an essential and challenging graph mining task used in several applications of the modern data science. Some of the FSM algorithms have the objective of finding all frequent subgraphs whereas some of the algorithms focus on discovering frequent subgraphs approximately. On the other hand, modern applications employ evolving graphs where the increments are small graphs or stream of nodes and edges. In such cases, FSM task becomes more challenging due to growing data size and complexity of the base algorithms. Recently we see frequent subgraph mining algorithms designed for dynamic graph data. However, there is no comparative review of the dynamic subgraph mining algorithms focusing on the discovery of frequent subgraphs over evolving graph data. This article focuses on the characteristics of dynamic frequent subgraph mining algorithms over evolving graphs. We first introduce and compare dynamic frequent subgraph mining algorithms; trying to highlight their attributes as increment type, graph type, graph representation, internal data structure, algorithmic approach, programming approach, base algorithm and output type. Secondly, we introduce and compare the approximate frequent subgraph mining algorithms for dynamic graphs with additional attributes as their sampling strategy, data in the sample, statistical guarantees on the sample and their main objective. Finally, we highlight research opportunities in this specific domain from our perspective. Overall, we aim to introduce the research area of frequent subgraph mining over evolving graphs with the hope that this can serve as a reference and inspiration for the researchers of the field.</p>", "Keywords": "Approximate frequent subgraph mining;Dynamic graph;Evolving graph;Exact frequent subgraph mining;Frequent subgraph mining;Incremental subgraph mining", "DOI": "10.7717/peerj-cs.2361", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "Belgin Ergenç Bostanoğlu", "Affiliation": "Computer Engineering, Izmir Institute of Technology, Izmir, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering, Izmir Institute of Technology, Izmir, Turkey"}], "References": [{"Title": "A survey of pattern mining in dynamic graphs", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "6", "Page": "", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "MaNIACS \n : Approximate Mining of Frequent Subgraph Patterns through Sampling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 118203030, "Title": "Integrating ytopt and libEnsemble to autotune OpenMC", "Abstract": "<p>Ytopt is a Python machine-learning-based autotuning software package developed within the ECP PROTEAS-TUNE project. The ytopt software adopts an asynchronous search framework that consists of sampling a small number of input parameter configurations and progressively fitting a surrogate model over the input-output space until exhausting the user-defined maximum number of evaluations or the wall-clock time. libEnsemble is a Python toolkit for coordinating workflows of asynchronous and dynamic ensembles of calculations across massively parallel resources developed within the ECP PETSc/TAO project. libEnsemble helps users take advantage of massively parallel resources to solve design, decision, and inference problems and expands the class of problems that can benefit from increased parallelism. In this paper we present our methodology and framework to integrate ytopt and libEnsemble to take advantage of massively parallel resources to accelerate the autotuning process. Specifically, we focus on using the proposed framework to autotune the ECP ExaSMR application OpenMC, an open source Monte Carlo particle transport code. OpenMC has seven tunable parameters some of which have large ranges such as the number of particles in-flight, which is in the range of 100,000 to 8 million, with its default setting of 1 million. Setting the proper combination of these parameter values to achieve the best performance is extremely time-consuming. Therefore, we apply the proposed framework to autotune the MPI/OpenMP offload version of OpenMC based on a user-defined metric such as the figure of merit (FoM) (particles/s) or energy efficiency energy-delay product (EDP) on Crusher at Oak Ridge Leadership Computing Facility. The experimental results show that we achieve the improvement up to 29.49% in FoM and up to 30.44% in EDP.</p>", "Keywords": "", "DOI": "10.1177/10943420241286476", "PubYear": 2025, "Volume": "39", "Issue": "1", "JournalId": 3326, "JournalTitle": "The International Journal of High Performance Computing Applications", "ISSN": "1094-3420", "EISSN": "1741-2846", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mathematics & Computer Science Division,Argonne National Laboratory, Lemont, IL, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mathematics & Computer Science Division,Argonne National Laboratory, Lemont, IL, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Mathematics & Computer Science Division,Argonne National Laboratory, Lemont, IL, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mathematics & Computer Science Division,Argonne National Laboratory, Lemont, IL, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence Programs, Oak Ridge National Laboratory, Oak Ridge, TN, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematics & Computer Science Division,Argonne National Laboratory, Lemont, IL, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Mathematics & Computer Science Division,Argonne National Laboratory, Lemont, IL, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Mathematics & Computer Science Division,Argonne National Laboratory, Lemont, IL, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Mathematics & Computer Science Division,Argonne National Laboratory, Lemont, IL, USA"}, {"AuthorId": 10, "Name": "Mary <PERSON>", "Affiliation": "School of Computing, University of Utah, Salt Lake City, UT, USA"}], "References": [{"Title": "Autotuning PolyBench benchmarks with LLVM Clang/Polly loop optimization pragmas using Bayesian optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "20", "Page": "e6683", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Autotuning PolyBench benchmarks with LLVM Clang/Polly loop optimization pragmas using Bayesian optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "20", "Page": "e6683", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 118203265, "Title": "SMT2Test: From SMT Formulas to Effective Test Cases", "Abstract": "<p>One of the primary challenges in software testing is generating high-quality test inputs and obtaining corresponding test oracles. This paper introduces a novel methodology to mitigate this challenge in testing program verifiers by employing SMT (Satisfiability Modulo Theories) formulas as a universal test case generator. The key idea is to transform SMT formulas into programs and link the satisfiability of the formulas with the safety property of the programs, allowing the satisfiability of the formulas to act as a test oracle for program verifiers. This method was implemented as a framework named SMT2Test, which enables the transformation of SMT formulas into Dafny and C programs. An intermediate representation was designed to augment the flexibility of this framework, streamlining the transformation for other programming languages and fostering modular transformation strategies. We evaluated the effectiveness of SMT2Test by finding defects in two program verifiers: the Dafny verifier and CPAchecker. Utilizing the SMT2Test framework with the SMT formulas from the SMT competition and SMT solver fuzzers, we discovered and reported a total of 14 previously unknown defects in these program verifiers that were not found by previous methods. After reporting, all of them have been confirmed, and 6 defects have been fixed. These findings show the effectiveness of our method and imply its potential application in testing other programming language infrastructures.</p>", "Keywords": "", "DOI": "10.1145/3689719", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}], "References": [{"Title": "Random testing for C and C++ compilers with YARPGen", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "On the unusual effectiveness of type-aware operator mutations for testing SMT solvers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Generative type-aware mutation for testing SMT solvers", "Authors": "<PERSON>won <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118203266, "Title": "Practical Verification of Smart Contracts using Memory Splitting", "Abstract": "<p>SMT-based verification of low-level code requires modeling and reasoning about memory operations. Prior work has shown that optimizing memory representations is beneficial for scaling verification—pointer analysis, for example can be used to split memory into disjoint regions leading to faster SMT solving. However, these techniques are mostly designed for C and C++ programs with explicit operations for memory allocation which are not present in all languages. For instance, on the Ethereum virtual machine, memory is simply a monolithic array of bytes which can be freely accessed by Ethereum bytecode, and there is no allocation primitive. In this paper, we present a memory splitting transformation guided by a conservative memory analysis for Ethereum bytecode generated by the Solidity compiler. The analysis consists of two phases: recovering memory allocation and memory regions, followed by a pointer analysis. The goal of the analysis is to enable memory splitting which in turn speeds up verification. We implemented both the analysis and the memory splitting transformation as part of a verification tool, CertoraProver, and show that the transformation speeds up SMT solving by up to 120x and additionally mitigates 16 timeouts when used on 229 real-world smart contract verification tasks.</p>", "Keywords": "", "DOI": "10.1145/3689796", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tel Aviv University, Tel Aviv, Israel"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Certora, Seattle, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Certora, San Diego, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Certora, Seattle, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Certora, Tel Aviv, Israel"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Certora, Seattle, USA"}], "References": [{"Title": "MadMax", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "10", "Page": "87", "JournalTitle": "Communications of the ACM"}, {"Title": "Precise static modeling of Ethereum “memory”", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Symbolic value-flow static analysis: deep, precise, complete modeling of Ethereum smart contracts", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Rich specifications for Ethereum smart contract verification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "SolType: refinement types for arithmetic overflow in solidity", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Elipmoc: advanced decompilation of Ethereum smart contracts", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Security Threat Mitigation for Smart Contracts: A Comprehensive Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "14s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 118203304, "Title": "SparseAuto: An Auto-scheduler for Sparse Tensor Computations using Recursive Loop Nest Restructuring", "Abstract": "<p>Automated code generation and performance enhancements for sparse tensor algebra have become essential in many real-world applications, such as quantum computing, physical simulations, computational chemistry, and machine learning. General sparse tensor algebra compilers are not always versatile enough to generate asymptotically optimal code for sparse tensor contractions. This paper shows how to generate asymptotically better schedules for complex sparse tensor expressions using kernel fission and fusion. We present generalized loop restructuring transformations to reduce asymptotic time complexity and memory footprint. Furthermore, we present an auto-scheduler that uses a partially ordered set (poset)-based cost model that uses both time and auxiliary memory complexities to prune the search space of schedules. In addition, we highlight the use of Satisfiability Module Theory (SMT) solvers in sparse auto-schedulers to approximate the Pareto frontier of better schedules to the smallest number of possible schedules, with user-defined constraints available at compile-time. Finally, we show that our auto-scheduler can select better-performing schedules and generate code for them. Our results show that the auto-scheduler provided schedules achieve orders-of-magnitude speedup compared to the code generated by the Tensor Algebra Compiler (TACO) for several computations on different real-world tensors.</p>", "Keywords": "", "DOI": "10.1145/3689730", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Purdue University, West Lafayette, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Purdue University, West Lafayette, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Virginia Tech, Blacksburg, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Purdue University, West Lafayette, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Purdue University, West Lafayette, USA"}], "References": [{"Title": "A sparse iteration space transformation framework for sparse tensor algebra", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Compiler Support for Sparse Tensor Computations in MLIR", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Architecture and Code Optimization"}]}, {"ArticleId": 118203308, "Title": "Study of plastic deformation and quality of flexible medium forming of bionic egg-shaped shell", "Abstract": "<p>Aiming at the problems of large deviation of plastic machining accuracy and different buckling instability loads of bionic egg-shaped shells under different flexible medium forming technologies, it is proposed to adopt unidirectional loading rubber bulging and granular medium bulging technologies of bionic egg-shaped shells. By comparing the plastic machining accuracy, geometric accuracy, and strength of bionic egg-shaped shells with different flexible mediums, the machining accuracy and overall quality of bionic egg-shaped shells can be improved. In order to explore the stress and strain conditions at different stages and in different regions of the egg-shaped shell flexible medium forming technologies under the ideal case considering the thick directional stress conditions, a mechanical analysis of the egg-shaped shell flexible medium forming technologies is carried out. The combination of numerical analysis and experiment is used to analyze the forming process of egg-shaped shell under the action of different flexible mediums, and the accuracy of the numerical analysis model is verified. And then, through the study of the influence of different flexible medium forming technologies on the plastic processing accuracy, geometric accuracy, and strength of the egg-shaped shells, the plastic deformation and quality of the egg-shaped shell is mastered by the influence law of the flexible medium forming technology. The study shows that the results of the numerical analysis of the egg-shaped shell flexible medium forming technology have an average error of no more than 11% with the test, and the numerical analysis model can be considered accurate. Moreover, the plastic machining accuracy of egg-shaped shell process parts and egg-shaped parts with granular medium is better than that of rubber medium. The geometric accuracy deviation of the egg-shaped part of the egg-shaped shell with the granular medium is 10% smaller than that of the rubber medium, and the process parts are also superior to the rubber medium. The buckling load of the egg-shaped shell process parts under granular medium are higher than that of the rubber medium, while the egg-shaped parts are slightly smaller. Considering the experiment error and the overall buckling load distribution, the overall strength of the egg-shaped shell is higher under the action of granular medium.</p>", "Keywords": "Flexible medium; Egg-shaped shell; Mechanical analysis; Buckling load", "DOI": "10.1007/s00170-024-14575-8", "PubYear": 2024, "Volume": "135", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Jiangsu University of Science and Technology, Zhenjiang, China; Corresponding author."}, {"AuthorId": 2, "Name": "Zheng<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Jiangsu University of Science and Technology, Zhenjiang, China"}, {"AuthorId": 3, "Name": "Mingting Shen", "Affiliation": "School of Mechanical Engineering, Jiangsu University of Science and Technology, Zhenjiang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Jiangsu University of Science and Technology, Zhenjiang, China"}], "References": [{"Title": "Numerical simulation and experimental research on rubber flexible-die forming limitation with new position-limited backpressure mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "7-8", "Page": "2183", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Investigation on granular medium forming formability of TA1 titanium alloy cylinder-shaped parts", "Authors": "<PERSON><PERSON>o Hu; <PERSON><PERSON><PERSON>; Jubo Fu", "PubYear": 2022, "Volume": "118", "Issue": "5-6", "Page": "1933", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Research on the deformation mechanism of egg-shaped shells in granular medium bulging", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "1-2", "Page": "497", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Study on the mechanism of unidirectional loading rubber bulging of bionic egg-shaped shell", "Authors": "Zhengxian Shi; <PERSON><PERSON><PERSON>; <PERSON><PERSON> Shen", "PubYear": 2023, "Volume": "125", "Issue": "7-8", "Page": "3633", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 118203311, "Title": "A Case for First-Class Environments", "Abstract": "<p>Formalizations of programming languages typically adopt the substitution model from the lambda calculus. However, substitution creates notorious complications for reasoning and implementation. Furthermore, it is disconnected from practical implementations, which normally adopt environments and closures.</p><p> In this paper we advocate for formalizing programming languages using a novel style of small-step environment-based semantics , which avoids substitution and is closer to implementations. We present a call-by-value statically typed calculus, called λ <sub>E</sub> , using our small-step environment semantics. With our alternative environment semantics programming language constructs for first-class environments arise naturally, without creating significant additional complexity. Therefore, λ <sub>E</sub> also adopts first-class environments, adding expressive power that is not available in conventional lambda calculi. λ <sub>E</sub> is a conservative extension of the call-by-value Simply Typed Lambda Calculus (STLC), and employs de <PERSON><PERSON> indices for its formalization, which fit naturally with the environment-based semantics. Reasoning about λ <sub>E</sub> is simple, and in many cases simpler than reasoning about the traditional STLC. We show an abstract machine that implements the semantics of λ <sub>E</sub> , and has an easy correctness proof. We also extend λ <sub>E</sub> with references. We show that λ <sub>E</sub> can model a simple form of first-class modules, and suggest using first-class environments as an alternative to objects for modelling capabilities. All technical results are formalized in the <PERSON>q proof assistant. In summary, our work shows that the small-step environment semantics that we adopt has three main and orthogonal benefits: 1) it simplifies the notorious binding problem in formalizations and proof assistants; 2) it is closer to implementations; and 3) additional expressive power is obtained from first-class environments almost for free. </p>", "Keywords": "", "DOI": "10.1145/3689800", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Hong Kong, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON> C<PERSON>", "Affiliation": "University of Hong Kong, Hong Kong, China"}], "References": [{"Title": "The weak call-by-value λ-calculus is reasonable for both time and space", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118203312, "Title": "Effects and Coeffects in Call-by-Push-Value", "Abstract": "<p>Effect and coeffect tracking integrate many types of compile-time analysis, such as cost, liveness, or dataflow, directly into a language's type system. In this paper, we investigate the addition of effect and coeffect tracking to the type system of call-by-push-value (CBPV), a computational model useful in compilation for its isolation of effects and for its ability to cleanly express both call-by-name and call-by-value computations. Our main result is effect-and-coeffect soundness, which asserts that the type system accurately bounds the effects that the program may trigger during execution and accurately tracks the demands that the program may make on its environment. This result holds for two different dynamic semantics: a generic one that can be adapted for different coeffects and one that is adapted for reasoning about resource usage. In particular, the second semantics discards the evaluation of unused values and pure computations while ensuring that effectful computations are always evaluated, even if their results are not required. Our results have been mechanized using the Coq proof assistant.</p>", "Keywords": "", "DOI": "10.1145/3689750", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Pennsylvania, Philadelphia, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Pennsylvania, Philadelphia, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Michigan, Ann Arbor, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Pennsylvania, Philadelphia, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Pennsylvania, Philadelphia, USA"}], "References": [{"Title": "The fire triangle: how to mix substitution, dependent elimination, and effects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Doo bee doo bee doo", "Authors": "LUKAS CONVENT; SAM LINDLEY; CONOR MCBRIDE", "PubYear": 2020, "Volume": "30", "Issue": "", "Page": "e9", "JournalTitle": "Journal of Functional Programming"}, {"Title": "Computation focusing", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "ICFP", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A unified view of modalities in type systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "ICFP", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A unifying type-theory for higher-order (amortized) cost analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A graded dependent type system with a usage-aware semantics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Bidirectional Typing", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A relational theory of effects and coeffects", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Effects, capabilities, and boxes: from scope-based reasoning to type-based reasoning and back", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A Graded Modal Dependent Type Theory with a Universe and Erasure, Formalized", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "ICFP", "Page": "920", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Resource-Aware Soundness for Big-Step Semantics", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "1281", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118203314, "Title": "The ART of Sharing Points-to Analysis: Reusing Points-to Analysis Results Safely and Efficiently", "Abstract": "<p>Data-flow analyses like points-to analysis can vastly improve the precision of other analyses, and enable powerful code optimizations. However, whole-program points-to analysis of large Java programs tends to be expensive – both in terms of time and memory. Consequently, many compilers (both static and JIT) and program-analysis tools tend to employ faster – but more conservative – points-to analyses to improve usability. As an alternative to such trading of precision for performance, various techniques have been proposed to perform precise yet expensive fixed-point points-to analyses ahead of time in a static analyzer, store the results, and then transmit them to independent compilation/program-analysis stages that may need them. However, an underlying concern of safety affects all such techniques – can a compiler (or program analysis tool) trust the points-to analysis results generated by another compiler/tool? In this work, we address this issue of trust in the context of Java, while accounting for the issue of performance. We propose ART: Analysis-results Representation Template – a novel scheme to efficiently and concisely encode results of flow-sensitive, context-insensitive points-to analysis computed by a static analyzer for use in any independent system that may benefit from such a precise points-to analysis. ART also allows for fast regeneration of the encoded sound analysis results in such systems. Our scheme has two components: (i) a producer that can statically perform expensive points-to analysis and encode the same concisely, (ii) a consumer that, on receiving such encoded results (called artwork), can regenerate the points-to analysis results encoded by the artwork if it is deemed “safe”. The regeneration scheme completely avoids fixed-point computations and thus can help consumers like static analyzers and JIT compilers to obtain precise points-to information without paying a prohibitively high cost. We demonstrate the usage of ART by implementing a producer (in Soot) and two consumers (in Soot and the Eclipse OpenJ9 JIT compiler). We have evaluated our implementation over various benchmarks from the DaCapo and SPECjvm2008 suites. Our results demonstrate that using ART, a consumer can obtain precise flow-sensitive, context-insensitive points-to analysis results in less than (average) 1% of the time taken by a static analyzer to perform the same analysis, with the storage overhead of ART representing a small fraction of the program size (average around 4%).</p>", "Keywords": "", "DOI": "10.1145/3689803", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IIT Madras, Chennai, India / University of Texas at Austin, Austin, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IBM Canada Lab, Markham, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IBM Canada Lab, Markham, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "IIT Madras, Chennai, India"}], "References": [{"Title": "LiveDroid: identifying and preserving mobile app state in volatile runtime environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Eagle", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "Synthesizing fine-grained synchronization protocols for implicit monitors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Verification Witnesses", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "A Container-Usage-Pattern-Based Context Debloating Approach for Object-Sensitive Pointer Analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "971", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Optimistic Stack Allocation and Dynamic Heapification for Managed Runtimes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "PLDI", "Page": "296", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118203315, "Title": "Computing Precise Control Interface Specifications", "Abstract": "<p> Verifying network programs is challenging because of how they divide labor: the control plane computes high level routes through the network and compiles them to device configurations, while the data plane uses these configurations to realize the desired forwarding behavior. In practice, the correctness of the data plane often assumes that the configurations generated by the control plane will satisfy complex specifications. Consequently, validation tools such as program verifiers, runtime monitors, fuzzers, and test-case generators must be aware of these control interface specifications (ci-specs) to avoid raising false alarms. In this paper, we propose the first algorithm for computing precise ci-specs for network data planes. Our specifications are designed to be efficiently monitorable —concretely, checking that a fixed configuration satisfies a ci-spec can be done in polynomial time. Our algorithm, based on modular program instrumentation, quantifier elimination, and a path-based analysis, is more expressive than prior work, and is applicable to practical network programs. We describe an implementation and show that ci-specs computed by our tool are useful for finding real bugs in real-world data plane programs. </p>", "Keywords": "", "DOI": "10.1145/3689743", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tehran Institute for Advanced Studies, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, USA"}], "References": [{"Title": "Using deep programmability to put network owners in control", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "4", "Page": "82", "JournalTitle": "ACM SIGCOMM Computer Communication Review"}, {"Title": "Dependently-typed data plane programming", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "P4R-Type: A Verified API for P4 Control Plane Programs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "1935", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Synthesizing Specifications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Thomas Reps", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "1787", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118203316, "Title": "When Your Infrastructure Is a Buggy Program: Understanding Faults in Infrastructure as Code Ecosystems", "Abstract": "<p>Modern applications have become increasingly complex and their manual installation and configuration is no longer practical. Instead, IT organizations heavily rely on Infrastructure as Code (IaC) technologies, to automate the provisioning, configuration, and maintenance of computing infrastructures and systems. IaC systems typically offer declarative, domain-specific languages (DSLs) that allow system administrators and developers to write high-level programs that specify the desired state of their infrastructure in a reliable, predictable, and documented fashion. Just like traditional programs, IaC software is not immune to faults, with issues ranging from deployment failures to critical misconfigurations that often impact production systems used by millions of end users. Surprisingly, despite its crucial role in global infrastructure management, the tooling and techniques for ensuring IaC reliability still have room for improvement. In this work, we conduct a comprehensive analysis of 360 bugs identified in IaC software within prominent IaC ecosystems including Ansible, Puppet, and Chef. Our work is the first in-depth exploration of bug characteristics in these widely-used IaC environments. Through our analysis we aim to understand: (1) how these bugs manifest, (2) their underlying root causes, (3) their reproduction requirements in terms of system state (e.g., operating system versions) or input characteristics, and (4) how these bugs are fixed. Based on our findings, we evaluate the state-of-the-art techniques for IaC reliability, identify their limitations, and provide a set of recommendations for future research. We believe that our study helps researchers to (1) better understand the complexity and peculiarities of IaC software, and (2) develop advanced tooling for more reliable and robust system configurations.</p>", "Keywords": "", "DOI": "10.1145/3689799", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "Georgios-<PERSON><PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Athens, Athens, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Athens, Athens, Greece"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}], "References": [{"Title": "Toward a catalog of software quality metrics for infrastructure code", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "170", "Issue": "", "Page": "110726", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Actor concurrency bugs: a comprehensive study on symptoms, root causes, API usages, and differences", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Security Smells in Ansible and Chef <PERSON>ts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "Well-typed programs can go wrong: a study of typing-related bugs in JVM compilers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Security Misconfigurations in Open Source Kubernetes Manifests: An Empirical Study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}]}, {"ArticleId": 118203328, "Title": "Catálogo de directrices de sostenibilidad para soportar el desarrollo y operación de software sostenible desde la ingeniería de requisitos", "Abstract": "", "Keywords": "", "DOI": "10.36825/RITI.12.27.005", "PubYear": 2024, "Volume": "12", "Issue": "27", "JournalId": 70037, "JournalTitle": "Revista de Investigación en Tecnologías de la Investigaión", "ISSN": "", "EISSN": "2387-0893", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Begoña Moros Valle", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118203520, "Title": "A Stability Study by Routh-<PERSON><PERSON>witz Criterion and Gershgorin Circles for Covid-19", "Abstract": "", "Keywords": "", "DOI": "10.4173/mic.2024.3.2", "PubYear": 2024, "Volume": "45", "Issue": "3", "JournalId": 22211, "JournalTitle": "Modeling, Identification and Control: A Norwegian Research Bulletin", "ISSN": "0332-7353", "EISSN": "1890-1328", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118203578, "Title": "Optimal containment control for multi‐agent systems using fast adaptive dynamic programming", "Abstract": "", "Keywords": "", "DOI": "10.1002/asjc.3516", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence Nankai University  Tianjin China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence Nankai University  Tianjin China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence Nankai University  Tianjin China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence Nankai University  Tianjin China"}], "References": [{"Title": "Internal reinforcement adaptive dynamic programming for optimal containment control of unknown continuous-time multi-agent systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "413", "Issue": "", "Page": "85", "JournalTitle": "Neurocomputing"}, {"Title": "Optimal distributed cooperative control for multi-agent systems with constrains on convergence speed and control input", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "426", "Issue": "", "Page": "14", "JournalTitle": "Neurocomputing"}, {"Title": "Fast distributed consensus seeking in large-scale and high-density multi-agent systems with connectivity maintenance", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "1010", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 118203820, "Title": "A Dependent Nominal Physical Type System for Static Analysis of Memory in Low Level Code", "Abstract": "<p> We tackle the problem of checking non-proof-carrying code , i.e. automatically proving type-safety (implying in our type system spatial memory safety) of low-level C code or of machine code resulting from its compilation without modification. This requires a precise static analysis that we obtain by having a type system which (i) is expressive enough to encode common low-level idioms, like pointer arithmetic, discriminating variants by bit-stealing on aligned pointers, storing the size and the base address of a buffer in distinct parts of the memory, or records with flexible array members, among others; and (ii) can be embedded in an abstract interpreter. We propose a new type system that meets these criteria. The distinguishing feature of this type system is a nominal organization of contiguous memory regions, which (i) allows nesting, concatenation, union, and sharing parameters between regions; (ii) induces a lattice over sets of addresses from the type definitions; and (iii) permits updates to memory cells that change their type without requiring one to control aliasing. We provide a semantic model for our type system, which enables us to derive sound type checking rules by abstract interpretation, then to integrate these rules as an abstract domain in a standard flow-sensitive static analysis. Our experiments on various challenging benchmarks show that semantic type-checking using this expressive type system generally succeeds in proving type safety and spatial memory safety of C and machine code programs without modification, using only user-provided function prototypes. </p>", "Keywords": "", "DOI": "10.1145/3689712", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University Paris-Saclay, CEA, List, Palaiseau, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University Paris-Saclay, CEA, List, Palaiseau, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University Paris-Saclay, ENS Paris-Saclay, CNRS, LMF, Gif-sur-Yvette, France"}], "References": [{"Title": "VIP: verifying real-world C idioms with integer-pointer casts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Bit-Stealing Made Legal: Compilation for Custom Memory Representations of Algebraic Data Types", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "ICFP", "Page": "813", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A Dependent Nominal Physical Type System for Static Analysis of Memory in Low Level Code", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "Page": "30", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118203925, "Title": "Can digital transformation promote the green development of manufacturing enterprises? Evidence from China", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10660-024-09909-3", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 2555, "JournalTitle": "Electronic Commerce Research", "ISSN": "1389-5753", "EISSN": "1572-9362", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Digital transformation and the new logics of business process management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "238", "JournalTitle": "European Journal of Information Systems"}]}, {"ArticleId": 118204378, "Title": "A Proof of the Nisan-Ronen Conjecture --- An Overview", "Abstract": "<p> This note presents an overview of our recent publication, which validates a conjecture proposed by <PERSON><PERSON> and <PERSON><PERSON> in their seminal paper [<PERSON><PERSON> and <PERSON><PERSON> 2001]. We show that the optimal approximation ratio for deterministic truthful mechanisms for makespan-minimization by a set of n unrelated machines is n. </p>", "Keywords": "", "DOI": "10.1145/3699814.3699819", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Aristotle University of Thessaloniki and Archimedes/RC Athena"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Oxford"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Goethe University, Frankfurt M."}], "References": [{"Title": "A New Lower Bound for Deterministic Truthful Scheduling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "83", "Issue": "9", "Page": "2895", "JournalTitle": "Algorithmica"}]}, {"ArticleId": 118204504, "Title": "Pathological voice detection using optimized deep residual neural network and explainable artificial intelligence", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-20348-y", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Nature-inspired optimization algorithms for different computing systems: novel perspective and systematic review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "19", "Page": "26779", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Snake Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "242", "Issue": "", "Page": "108320", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Indian language identification using time-frequency texture features and kernel ELM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "10", "Page": "13237", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Unified wavelet-based framework for evaluation of voice impairment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "2", "Page": "527", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "A comprehensive survey on recent metaheuristics for feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Hakan Ezgi Kiziloz", "PubYear": 2022, "Volume": "494", "Issue": "", "Page": "269", "JournalTitle": "Neurocomputing"}, {"Title": "A review on voice pathology: Taxonomy, diagnosis, medical procedures and detection techniques, open challenges, limitations, and recommendations for future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "1", "Page": "855", "JournalTitle": "Journal of Intelligent Systems"}, {"Title": "Plant leaf disease classification using deep attention residual network optimized by opposition-based symbiotic organisms search algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "23", "Page": "21049", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A survey on binary metaheuristic algorithms and their engineering applications", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "7", "Page": "6101", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A Systematic Review on Metaheuristic Optimization Techniques for Feature Selections in Disease Diagnosis: Open Issues and Challenges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "3", "Page": "1863", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Classification of apple images using support vector machines and deep residual networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "16", "Page": "12073", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Speech assessment tool methods for speech impaired children: a systematic literature review on the state-of-the-art in Speech impairment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "22", "Page": "35021", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "MMHFNet: Multi-modal and multi-layer hybrid fusion network for voice pathology detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "223", "Issue": "", "Page": "119790", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Time-frequency visual representation and texture features for audio applications: a comprehensive review, recent trends, and challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "23", "Page": "36143", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Explainable Artificial Intelligence (XAI): What we know and what is left to attain Trustworthy Artificial Intelligence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101805", "JournalTitle": "Information Fusion"}, {"Title": "Advances in nature-inspired metaheuristic optimization for feature selection problem: A comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "49", "Issue": "", "Page": "100559", "JournalTitle": "Computer Science Review"}, {"Title": "Hybrid models for classifying histological images: An association of deep features by transfer learning with ensemble classifier", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>ci<PERSON>; Guilherme F. Roberto", "PubYear": 2024, "Volume": "83", "Issue": "8", "Page": "21929", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Improved versions of snake optimizer for feature selection in medical diagnosis: a real case COVID-19", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "23", "Page": "17833", "JournalTitle": "Soft Computing"}, {"Title": "Voice disorder detection using machine learning algorithms: An application in speech and language pathology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "133", "Issue": "", "Page": "108047", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 118204513, "Title": "Image-Based Fitness Yoga Pose Recognition: Using Ensemble Learning and Multi-head Attention", "Abstract": "<p>With the increasing awareness of fitness, more and more people are choosing to participate in fitness activities. Yoga, as a form of exercise that improves both physical and mental health, is becoming increasingly popular worldwide. In order to assist yoga practitioners in more effective training through automated or semi automated systems, improve training effectiveness, assist professional athletes in training through intelligent recognition systems, correct movements, and improve athletic performance. This paper proposes a method that addresses the low accuracy issue of current yoga pose recognition algorithms by integrating multi-head attention mechanism and ensemble learning. Firstly, the Mixup algorithm is used to enhance yoga movement images. Subsequently, convolutional features are extracted from the images using the ResNet101 and VGGNet19 transfer learning models. Finally, the extracted convolutional features are combined and stacked using a multi-head attention mechanism. Model training, validation, and testing are performed using the Soft target cross-entropy loss function. Experimental results demonstrate that the proposed method achieves a training accuracy of 100%, a validation accuracy of 89.94%, a testing accuracy of 93.79%, and a detection speed of 297 frames per second. Overall, this method demonstrates high stability and robustness, providing a technological foundation for intelligent recognition of yoga poses.</p>", "Keywords": "Fitness yoga movements; Residual network; VGG network; Multi-head attention; Ensemble learning", "DOI": "10.1007/s44196-024-00662-x", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Aviation Security, Civil Aviation Flight University of China, Deyang, China"}, {"AuthorId": 2, "Name": "Hai <PERSON>", "Affiliation": "College of Civil Aviation Safety Engineering, Civil Aviation Flight University of China, Deyang, China; Civil Aircraft Fire Science and Safety Engineering Key Laboratory of Sichuan Province, Civil Aviation Flight University of China, Deyang, China; Corresponding author."}], "References": [{"Title": "Text summarization based on multi-head self-attention mechanism and pointer network", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "1", "Page": "555", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Attention mechanisms in computer vision: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "331", "JournalTitle": "Computational Visual Media"}, {"Title": "YogNet: A two-stream network for realtime multiperson yoga action recognition and posture correction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "250", "Issue": "", "Page": "109097", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Yoga pose classification: a CNN and MediaPipe inspired deep learning approach for real-world application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "12", "Page": "16551", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A transfer-based few-shot classification approach via masked manifold mixup and fuzzy memory contrastive learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "14", "Page": "10069", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Linear normalization attention neural Hawkes process", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "1", "Page": "1025", "JournalTitle": "Neural Computing and Applications"}, {"Title": "YoNet: A Neural Network for Yoga Pose Classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "2", "Page": "198", "JournalTitle": "SN Computer Science"}, {"Title": "YOGA: Deep object detection in the wild with lightweight feature learning and multiscale attention", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "109451", "JournalTitle": "Pattern Recognition"}, {"Title": "Real-time yoga pose classification with 3-D pose estimation model with LSTM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "11", "Page": "33019", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 118204705, "Title": "BGFL: a blockchain-enabled group federated learning at wireless industrial edges", "Abstract": "<p>In the rapidly evolving landscape of Industry 4.0, the complex computational tasks and the associated massive data volumes present substantial opportunities for advancements in machine learning at industry edges. Federated learning (FL), which is a variant of distributed machine learning for edge-cloud computing, presents itself as a persuasive resolution for these industrial edges, with its main objectives being the mitigation of privacy breaches and the resolution of data privacy concerns. However, traditional FL methodologies encounter difficulties in effectively overseeing extensive undertakings in Industry 4.0 as a result of challenges including wireless communications with high latency, substantial heterogeneity, and insufficient security protocols. As a consequence of these obstacles, blockchain technology has garnered acclaim for its secure, decentralized, and transparent data storage functionalities. A novel blockchain-enabled group federated learning (BGFL) framework designed specifically for wireless industrial edges is presented in this paper. By strategically dividing industrial devices into multiple groups, the BGFL framework simultaneously reduces the wireless traffic loads required for convergence and improves the accuracy of collaborative learning. Moreover, to optimize aggregation procedures and reduce communication resource utilization, the BGFL employs a hierarchical aggregation strategy that consists of both local and global aggregation off-chain and on-chain, respectively. The integration of a smart contract mechanism serves to fortify the security framework. The results of comparative experimental analyses demonstrate that the BGFL framework enhances the resilience of the learning framework and effectively reduces wireless communication latency. Thus, it offers a scalable and efficient solution for offloading tasks in edge-cloud computing environments.</p>", "Keywords": "Federated learning;Blockchain;Edge-cloud cooperation;Wireless traffic", "DOI": "10.1186/s13677-024-00700-1", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Electric Power Research Institute, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "China Electric Power Research Institute, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Nanjing Power Supply Company, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Electric Power Research Institute, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "State Grid Nanjing Power Supply Company, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Intelligence Technology Co., Ltd., Jinan, China"}], "References": [{"Title": "Privacy-preserving blockchain-enabled federated learning for B5G-Driven edge computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Longxiang Gao", "PubYear": 2022, "Volume": "204", "Issue": "", "Page": "108671", "JournalTitle": "Computer Networks"}, {"Title": "A Blockchain-Based Data-Driven Fault-Tolerant Control System for Smart Factories in Industry 4.0", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "204", "Issue": "", "Page": "158", "JournalTitle": "Computer Communications"}, {"Title": "Federated Learning for Computationally Constrained Heterogeneous Devices: A Survey", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "14s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Survey on Federated Learning enabling indoor navigation for industry 4.0 in B5G", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "250", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "PrivMaskFL: A private masking approach for heterogeneous federated learning in IoT", "Authors": "Jing <PERSON>; Hong Zhu", "PubYear": 2024, "Volume": "214", "Issue": "", "Page": "100", "JournalTitle": "Computer Communications"}, {"Title": "Decentralized Cooperative Caching and Offloading for Virtual Reality Task Based on GAN-Powered Multi-Agent Reinforcement Learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "1", "Page": "291", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Dependent Task Scheduling Using Parallel Deep Neural Networks in Mobile Edge Computing", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "22", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Grid Computing"}, {"Title": "Blockchain-enabled heterogeneous 6G supported secure vehicular management system over cloud edge computing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "25", "Issue": "", "Page": "101115", "JournalTitle": "Internet of Things"}, {"Title": "AI-empowered mobile edge computing: inducing balanced federated learning strategy over edge for balanced data and optimized computation cost", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "13", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "IoT workload offloading efficient intelligent transport system in federated ACNN integrated cooperated edge-cloud networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "13", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}]}, {"ArticleId": *********, "Title": "Innovative predictive maintenance for mining grinding mills: from LSTM-based vibration forecasting to pixel-based MFCC image and CNN", "Abstract": "<p>This article presents an innovative predictive maintenance for grinding mills, aiming to enhance operational efficiency and minimize downtime. Leveraging advancements in data analytics and IoT sensor technologies, the approach integrates vibration signal forecasting, LSTM-based fast Fourier transform (FFT) analysis, and convolutional neural networks (CNNs) to detect faults early on. The method involves creating LSTM models to forecast vibration signals based on historical data and using FFT analysis to identify fault frequencies associated with the grinding process. Additionally, techniques such as Mel-frequency cepstral coefficients (MFCCs), short-time Fourier transform (STFT), and continuous wavelet transform (CWT) are employed for spectrogram extraction, providing valuable insights into machinery conditions. Validation on real-world datasets with 99.95% of accuracy with 1 in AUC-ROC, showcases the robust predictive performance of the model and has reached 99.96% of accuracy for 16 classes with an AUC-ROC of 1 using CWRU dataset, surpassing existing approaches and demonstrating its potential for proactive maintenance across various industries beyond mining.</p>", "Keywords": "Predictive maintenance; Industry 4.0/5.0; Advanced monitoring; Grinding mills; LSTM-MFCC-CNN; Vibration analysis", "DOI": "10.1007/s00170-024-14588-3", "PubYear": 2024, "Volume": "135", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Alqualsadi Research Team, Rabat IT Center, Mohammed V University in Rabat, ENSIAS, Rabat, Morocco; Micro Embedded System and Artificial Intelligence Laboratory, Mascir, Morocco; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Alqualsadi Research Team, Rabat IT Center, Mohammed V University in Rabat, ENSIAS, Rabat, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Alqualsadi Research Team, Rabat IT Center, Mohammed V University in Rabat, ENSIAS, Rabat, Morocco"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Computer Systems Engineering (LISI), FSSM, Cadi Ayyad University, FSSM, Marrakesh, Morocco"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Micro Embedded System and Artificial Intelligence Laboratory, Mascir, Morocco"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Reminex R&D Center and Engineering Center, MANAGEM Group, REMINEX, Marrakech, Morocco"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Reminex R&D Center and Engineering Center, MANAGEM Group, REMINEX, Marrakech, Morocco"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Alqualsadi Research Team, Rabat IT Center, Mohammed V University in Rabat, ENSIAS, Rabat, Morocco"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Laboratory of Computer Systems Engineering (LISI), FSSM, Cadi Ayyad University, FSSM, Marrakesh, Morocco"}], "References": [{"Title": "Layer-wise relevance propagation for interpreting LSTM-RNN decisions in predictive maintenance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "3-4", "Page": "963", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Basic tools for vibration analysis with applications to predictive maintenance of rotating machines: an overview", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "9-10", "Page": "2883", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Selecting an appropriate supervised machine learning algorithm for predictive maintenance", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "7-8", "Page": "4277", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A 2 -LSTM for predictive maintenance of industrial equipment based on machine learning", "Authors": "<PERSON><PERSON> Jiang; <PERSON><PERSON><PERSON>; Pengcheng Fang", "PubYear": 2022, "Volume": "172", "Issue": "", "Page": "108560", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Predictive maintenance in mining industry: grinding mill case study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "2483", "JournalTitle": "Procedia Computer Science"}, {"Title": "Manufacturing industry-based optimal scheduling method of information system operation and maintenance resources", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Quantized neural adaptive finite-time preassigned performance control for interconnected nonlinear systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "21", "Page": "15429", "JournalTitle": "Neural Computing and Applications"}, {"Title": "The benefits of predictive maintenance in manufacturing excellence: a case study to establish reliable methods for predicting failures", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "128", "Issue": "7-8", "Page": "3685", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Artificial intelligence for enhanced flotation monitoring in the mining industry: A ConvLSTM-based approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "180", "Issue": "", "Page": "108476", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Saturated-threshold event-triggered adaptive global prescribed performance control for nonlinear Markov jumping systems and application to a chemical reactor model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "249", "Issue": "", "Page": "123490", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Advanced ML for predictive maintenance: a case study on remaining useful life prediction and reliability enhancement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "132", "Issue": "1-2", "Page": "323", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 118204835, "Title": "Process-integrated computerized numerical control: an analysis on process-machine coupling and feed scheduling", "Abstract": "Computerized numerical controls (CNCs) have been invented for the automation of industrial processes. They are used, when the process to be automated is required to be exact and fast with repeatable quality. Originally, the use of CNCs was primarily focused on milling and drilling processes. Today, CNCs are utilized in a wide range of industrial processes due to the growing importance of automation. However, the integration of process information or adaptation to the needs of these processes to achieve advanced manufacturing with CNCs is difficult: Industrial CNCs are rather closed real-time machining systems. Today, process integration is possible, when the interaction between the process and the machine is decoupled in view of the bandwidth of machine dynamics and process dynamics. There are interfaces that allow for process-motivated control loops that are realized on top of the machine control loop (e.g. chatter control). Then, machine-integrated real-time control is not the focus. Besides, it is often possible to change desired values inside the control loop (position, velocity, acceleration) on an axes basis. In this case, adaptation to the process can be realized in each computation cycle. However, process dynamics and machine axes dynamics are generally treated separately. The same holds for extra actuators (e.g., in the spindle) for position control. This paper has two goals. First, it wants to create an understanding for different levels of process-machine coupling. Second, the problem of direct coupling of process dynamics and machine dynamics is focused. Machining systems design propositions as well as some examples for the coupling of process and machine dynamics in the CNC are given.", "Keywords": "CNC; Feed scheduling; Advanced manufacturing; Process integration", "DOI": "10.1007/s00170-024-14437-3", "PubYear": 2024, "Volume": "135", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ISW, University of Stuttgart, Stuttgart, Germany; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ISW, University of Stuttgart, Stuttgart, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ISW, University of Stuttgart, Stuttgart, Germany"}], "References": [{"Title": "From classic CNC systems to cloud-based technology and back", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "101927", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 118204965, "Title": "Preservation of emotional context in tweet embeddings on social networking sites", "Abstract": "In communication, emotional information is crucial, yet its preservation in tweet embeddings remains a challenge. This study aims to address this gap by exploring three distinct methods for generating embedding vectors of tweets: word2vec models, pre-trained BERT models, and fine-tuned BERT models. We conducted an analysis to assess the degree to which emotional information is conserved in the resulting embedding vectors. Our findings indicate that the fine-tuned BERT model exhibits a higher level of preservation of emotional information compared to other methods. These results underscore the importance of utilizing advanced natural language processing techniques for preserving emotional context in text data, with potential implications for enhancing sentiment analysis and understanding human communication in social media contexts.", "Keywords": "Emotion; Intensity; Tweet; Embedding vector; BERT; Word2vec", "DOI": "10.1007/s10015-024-00974-3", "PubYear": 2024, "Volume": "29", "Issue": "4", "JournalId": 4137, "JournalTitle": "Artificial Life and Robotics", "ISSN": "1433-5298", "EISSN": "1614-7456", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Design, Kyushu University, Fukuoka, Japan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Design, Kyushu University, Fukuoka, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Design, Kyushu University, Fukuoka, Japan"}], "References": []}, {"ArticleId": 118205056, "Title": "Too-Short-Arc Association and Clustering Method for Space-Based Optical Observations", "Abstract": "<p>Space-based optical observation is assuming an increasingly significant role in space surveillance and tracking. However, it often yields tracks that are too short, which are known as too short arcs (TSAs). A single TSA lacks sufficient information for reliable initial orbit determination of a resident space object. Therefore, it is typically necessary to perform tracklet association during catalog building and maintenance. This study presents a novel tracklet association method that improves upon existing techniques by combining admissible regions and nonlinear orbital uncertainty propagation. Proceeding by constructing TSA association matrices and TSA clustering matrices, the bond energy algorithm, traditionally employed in the design of distributed database systems, is applied to cluster TSAs based on the association results between two arbitrary TSAs. Furthermore, the proposed splitting algorithm reduces the computational load associated with clustering multiple tracklets and effectively handles erroneous association results. To assess the effectiveness of this approach, it was tested in a space-based optical-survey scenario, accounting for practical observation uncertainties. The simulation results demonstrate that the method achieves a high level of accuracy in both TSA association and clustering.</p>", "Keywords": "Geosynchronous Earth Orbit; Orbit Determination; Distributed Database; Space Science and Technology; Planets; Satellites; Specific Angular Momentum; Orbital Property; RADAR; Sun Synchronous Orbit", "DOI": "10.2514/1.G008069", "PubYear": 2025, "Volume": "48", "Issue": "1", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National University of Defense Technology, 410073 Changsha, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Defense Technology, 410073 Changsha, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National University of Defense Technology, 410073 Changsha, People’s Republic of China"}], "References": []}, {"ArticleId": 118205062, "Title": "SIGecom Winter Meeting 2024 Highlights", "Abstract": "<p>The fourth annual ACM SIGecom Winter Meeting took place on February 15, 2024. Organized by <PERSON><PERSON> and <PERSON><PERSON>, this year's meeting brought together researchers from economics, computer science, and adjacent fields to focus behavioral models. The virtual meeting took the form of a workshop including talks and presentations from leading experts on various directions, as well as a fireside chat on getting into the research space and exciting opportunities that lie ahead. We share some highlights from the 2024 Winter Meeting, and additional insights from follow-up interviews with the speakers.</p>", "Keywords": "", "DOI": "10.1145/3699824.3699827", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Penn State University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cornell University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Stanford University"}], "References": []}, {"ArticleId": 118205216, "Title": "Exploring Large Language Models to generate Easy to Read content", "Abstract": "<p>Ensuring text accessibility and understandability are essential goals, particularly for individuals with cognitive impairments and intellectual disabilities, who encounter problems accessing information across various mediums such as web pages, newspapers, online administrative tasks, or health documents. Initiatives like Easy to Read and Plain Language guidelines aim to simplify complex texts; however, standardizing these guidelines remains challenging and often involves manual processes. This work presents an exploratory investigation into leveraging Artificial Intelligence (AI) and Natural Language Processing (NLP) approaches to simplify Spanish texts into Easy to Read formats, with a focus on utilizing Large Language Models (LLMs) for creating accessible texts, especially in generating Easy to Read content. The study contributes a parallel corpus of Spanish adapted for Easy To Read format, which serves as a valuable resource for training and testing text simplification systems. Additionally, several text simplification experiments using LLMs and the collected corpus are conducted, involving fine-tuning and testing a Llama2 model to generate Easy to Read content. A qualitative evaluation, guided by an expert in text adaptation for Easy to Read content, is carried out to assess the automatically simplified texts. This research contributes to advancing text accessibility for individuals with cognitive impairments, highlighting promising strategies for leveraging LLMs while responsibly managing energy usage.</p>", "Keywords": "Large Language Model; Text simplification; Plain language; Easy to read; digital accessibility; Natural Language Processing", "DOI": "10.3389/fcomp.2024.1394705", "PubYear": 2024, "Volume": "6", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Universidad Carlos III de Madrid, Spain; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science and Engineering Department, Universidad Carlos III de Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Universidad Carlos III de Madrid, Spain"}], "References": [{"Title": "Lexical simplification benchmarks for English, Portuguese, and Spanish", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "5", "Issue": "", "Page": "201", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "EASIER System. Evaluating a Spanish Lexical Simplification Proposal with People with Cognitive Impairments", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "5", "Page": "1195", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Designing user interfaces for content simplification aimed at people with cognitive impairments", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "23", "Issue": "1", "Page": "99", "JournalTitle": "Universal Access in the Information Society"}]}, {"ArticleId": 118205466, "Title": "Optimal Selective Maintenance for Complex Systems Under Stochastic Maintenance and Break Durations", "Abstract": "The selective maintenance problem (SMP) arises in many settings where systems perform sequences of missions separated by maintenance breaks. The SMP aims to identify the optimal subset of maintenance actions that will maximize the system reliability for the next mission. This paper addresses the SMP when the durations of the break and maintenance actions are stochastic. An exact solution method is proposed which uses the saddlepoint approximation in the computation of the system reliability. Several experiments are used to demonstrate the effectiveness of the proposed solution method for large and complex systems.", "Keywords": "Maintenance; Reliability; Optimization; Stochastic duration", "DOI": "10.1016/j.ifacol.2024.09.072", "PubYear": 2024, "Volume": "58", "Issue": "19", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dalhousie University, Industrial Engineering, Halifax, Nova Scotia, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dalhousie University, Industrial Engineering, Halifax, Nova Scotia, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Industrial Engineering, Production and Maintenance (LGIPM), Lorraine University, Metz, France"}], "References": [{"Title": "Optimization of the integrated fleet-level imperfect selective maintenance and repairpersons assignment problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "3", "Page": "703", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 118205469, "Title": "Cold Storage Order Picking Performance: Effects of Load Unit Utilization and Product Volume", "Abstract": "Increasing labor costs in retail have intensified the focus on order-fulfillment performance, particularly in warehouse operations. In this context, manual picker-to-parts systems, especially in cold storage environments processing deep-frozen products below -20°C, are of significant interest. Our study explores order picking in such cold storage environments, where products are packed into insulated, roll containers serving as load units. While existing research has explored the effects of product characteristics such as weight, volume, and different load units like pallets from a theoretical perspective, the specific interplay between load unit utilization levels and product volume remains underexplored in practical settings. To address this gap, we conducted a field study focusing on a cold storage of a prominent German brick-and-mortar grocery retailer and analyzed a comprehensive longitudinal dataset comprising 227,274 storage location visits by 29 order pickers in January 2023. Employing a mixed-effects model, we examined the impact of these factors on order picking time (OPT). Our findings revealed a subtle relationship: a 1% increase in load unit utilization level correlates with a 1% increase in OPT, signifying that higher utilization levels negatively impact pickers’ performance. Notably, our analysis also indicated that product volume moderates this effect. Specifically, lower product volumes can mitigate the adverse effects of high load unit utilization, whereas higher product volumes amplify these effects. This research offers valuable insights for warehouse managers, particularly in cold storage settings on finding the balance between OPT and load unit utilization.", "Keywords": "Cold storage; Order picking time; Load unit utilization; Product volume; Deep-frozen goods", "DOI": "10.1016/j.ifacol.2024.09.131", "PubYear": 2024, "Volume": "58", "Issue": "19", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chair of Digital Transformation in Operations Management, Saarland University Saarbrücken, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Production and Supply Chain Management, Technical University of Darmstadt Darmstadt, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Chair of Digital Transformation in Operations Management, Saarland University Saarbrücken, Germany"}], "References": []}, {"ArticleId": 118205484, "Title": "Energy-based design optimization of Adept test trajectory for SCARA robots using clothoid curve", "Abstract": "", "Keywords": "", "DOI": "10.1007/s41315-024-00391-6", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 6109, "JournalTitle": "International Journal of Intelligent Robotics and Applications", "ISSN": "2366-5971", "EISSN": "2366-598X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Pythagorean-Hodograph curves-based trajectory planning for pick-and-place operation of Delta robot with prescribed pick and place heights", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "6", "Page": "1651", "JournalTitle": "Robotica"}]}, {"ArticleId": 118205547, "Title": "A flexible and efficient algorithm for high dimensional support vector regression", "Abstract": "In high dimensional statistical learning, variable selection and handling highly correlated phenomena are two crucial topics. Elastic-net regularization can automatically perform variable selection and tends to either simultaneously select or remove highly correlated variables. Consequently, it has been widely applied in machine learning. In this paper, we incorporate elastic-net regularization into the support vector regression model, introducing the Elastic-net Support Vector Regression (En-SVR) model. Due to the inclusion of elastic-net regularization, the En-SVR model possesses the capability of variable selection, addressing high dimensional and highly correlated statistical learning problems. However, the optimization problem for the En-SVR model is rather complex, and common methods for solving the En-SVR model are challenging. Nevertheless, we observe that the optimization problem for the En-SVR model can be reformulated as a convex optimization problem where the objective function is separable into multiple blocks and connected by an inequality constraint. Therefore, we employ a novel and efficient Alternating Direction Method of Multipliers (ADMM) algorithm to solve the En-SVR model, and provide a complexity analysis as well as convergence analysis for the algorithm. Furthermore, extensive numerical experiments validate the outstanding performance of the En-SVR model in high dimensional statistical learning and the efficiency of this novel ADMM algorithm.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.128671", "PubYear": 2025, "Volume": "611", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Statistics, Chongqing University, Chongqing, 401331, China;<PERSON> and <PERSON> contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Microbiology, School of Public Health, Cheeloo College of Medicine, Shandong University, Jinan, 250012, China;<PERSON> and <PERSON> contributed equally to this work"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Statistics, Chongqing University, Chongqing, 401331, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Statistics, Chongqing University, Chongqing, 401331, China;Corresponding author"}], "References": [{"Title": "ν-projection twin support vector machine for pattern classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "376", "Issue": "", "Page": "10", "JournalTitle": "Neurocomputing"}, {"Title": "Remaining useful life prediction of lithium-ion batteries with adaptive unscented kalman filter and optimized support vector regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "376", "Issue": "", "Page": "95", "JournalTitle": "Neurocomputing"}, {"Title": "Output based transfer learning with least squares support vector machine and its application in bladder cancer prognosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "387", "Issue": "", "Page": "279", "JournalTitle": "Neurocomputing"}, {"Title": "Deep support vector machine for hyperspectral image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "107298", "JournalTitle": "Pattern Recognition"}, {"Title": "Support vector machine classifier with huberized pinball loss", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "103635", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Nonparallel support vector machine with large margin distribution for pattern classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "107374", "JournalTitle": "Pattern Recognition"}, {"Title": "A support vector regression model hybridized with chaotic krill herd algorithm and empirical mode decomposition for regression task", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "410", "Issue": "", "Page": "185", "JournalTitle": "Neurocomputing"}, {"Title": "Uncertainty analysis of wind power probability density forecasting based on cubic spline interpolation and support vector quantile regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "430", "Issue": "", "Page": "121", "JournalTitle": "Neurocomputing"}, {"Title": "A denoising carbon price forecasting method based on the integration of kernel independent component analysis and least squares support vector regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "434", "Issue": "", "Page": "67", "JournalTitle": "Neurocomputing"}, {"Title": "Multi-class financial distress prediction based on support vector machines integrated with the decomposition and fusion methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "559", "Issue": "", "Page": "153", "JournalTitle": "Information Sciences"}, {"Title": "Sparse elastic net multi-label rank support vector machine with pinball loss and its applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "107232", "JournalTitle": "Applied Soft Computing"}, {"Title": "A graph based preordonnances theoretic supervised feature selection in high dimensional data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "257", "Issue": "", "Page": "109899", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-block alternating direction method of multipliers for ultrahigh dimensional quantile fused regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "192", "Issue": "", "Page": "107901", "JournalTitle": "Computational Statistics & Data Analysis"}, {"Title": "Privileged multi-view one-class support vector machine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "572", "Issue": "", "Page": "127186", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 118205569, "Title": "Retraction Note: Dispatching and rebalancing for ride-sharing autonomous mobility-on-demand systems based on a fuzzy multi-criteria approach", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-024-10168-5", "PubYear": 2024, "Volume": "28", "Issue": "S1", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universite de Pau et des Pays de l’Adour, E2S UPPA, LIUPPA, EA3000, Anglet, France; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universite de Pau et des Pays de l’Adour, E2S UPPA, LIUPPA, EA3000, Anglet, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universite de Pau et des Pays de l’Adour, E2S UPPA, LIUPPA, EA3000, Anglet, France"}], "References": []}, {"ArticleId": 118205656, "Title": "On the atout ticket learning problem for neural networks and its application in securing federated learning exchanges", "Abstract": "Artificial Neural Networks (ANNs) have become the backbone of many real-world applications, including distributed applications relying on Federated Learning (FL). However, several vulnerabilities/attacks have emerged in recent years, affecting the benefits of using ANNs in FL, such as reconstruction attacks and membership inference attacks. These attacks can have severe impacts on both the societal and professional levels. For instance, inferring the presence of a patient’s private health record in a medical study or a clinic database violates the patient’s privacy and can have legal or ethical consequences. Therefore, protecting the data and model from malicious attacks in FL systems is important. This paper introduces the Atout Ticket Learning (ATL) problem. This new problem consists of identifying sensitive parameters (atout tickets) of a neural network model, which, if modified, will increase the model’s loss by at least a given threshold ϵ . First, we formulate ATL as an ℓ 0 -norm minimization problem, and we derive a lower bound on the number of atout tickets needed to achieve a model degradation of ϵ . Second, we design the Atout Ticket Protocol (ATP) as an effective solution for privacy-preserving in FL systems using atout tickets, along with the benefit of noise perturbations and secure aggregation techniques. Finally, we experiment ATP against FL reconstruction attacks using new selection strategies, namely Inverting Gradients, Deep Leakage, and Improved Deep Leakage. The results show that ATP is highly robust against these attacks.", "Keywords": "", "DOI": "10.1016/j.jisa.2024.103891", "PubYear": 2024, "Volume": "86", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computing, Mohammed VI Polytechnic University, Benguerir, Morocco;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computing, Mohammed VI Polytechnic University, Benguerir, Morocco"}, {"AuthorId": 3, "Name": "Abdelkader <PERSON>", "Affiliation": "College of Computing, Mohammed VI Polytechnic University, Benguerir, Morocco"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Sciences Dhar EL <PERSON>, <PERSON><PERSON>h University, Morocco"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Computing, Mohammed VI Polytechnic University, Benguerir, Morocco"}], "References": [{"Title": "Privacy‐preserving federated learning based on multi‐key homomorphic encryption", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "9", "Page": "5880", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "LEGO: A hybrid toolkit for efficient 2PC-based privacy-preserving machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "102782", "JournalTitle": "Computers & Security"}, {"Title": "Privacy-Preserving federated learning: An application for big data load forecast in buildings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "", "Page": "103211", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 118205665, "Title": "Optimizing integrated energy systems at park level: A cooperative game approach with exergy economics", "Abstract": "This study presents a comprehensive framework for optimizing energy systems by integrating exergy analysis, energy economics, and game theory. The concept of exergy, which quantifies the usable energy within a system, is employed to evaluate energy efficiency and losses across various energy sources, including thermal, cooling, chemical, and electrical systems. Stoichiometric coefficients, denoted by the factor λ, are utilized to simplify exergy calculations for different energy types and processes. The economic evaluation of energy flows is conducted through energy economics principles, incorporating cost allocation and balance equations. The integration of game theory into the optimization model ensures strategic interactions among energy components, leading to a Nash equilibrium that balances economic performance, efficiency, and environmental sustainability. The model also accounts for emissions and the required proportion of renewable energy. To solve the complex optimization problem, a modified Particle Swarm Optimization (PSO) algorithm is employed, featuring adaptive mechanisms for velocity and inertia updates, enhancing the search process for the optimal solution. The proposed framework is designed to optimize the Integrated Energy System (IES) efficiently, ensuring sustainable and economically viable energy management. The analysis of optimization strategies highlights a trade-off between cost and efficiency. Strategy 1, focused on minimizing cost, achieves the lowest cost at 4069.37 CNY, 25.79 % less than Strategy 2, but with a reduced exergy efficiency of 59.82 %, which is 10.14 % lower than Strategy 2′s 68.47 %. Strategy 3 offers a balanced approach, with a cost of 4970.89 CNY, 9.43 % higher than Strategy 1 but 9.43 % lower than Strategy 2. It achieves an exergy efficiency of 67.87 %, only 0.60 % lower than Strategy 2, thus providing a practical compromise between economic performance and efficiency.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2024.109762", "PubYear": 2024, "Volume": "120", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, Qujing Normal University, Qujing 655011, Yunnan, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong Changzhi Construction Engineering Co., LTD, Jinan 25000, Shandong, PR China;Corresponding author"}], "References": [{"Title": "Study on Master-Slave Game Optimization Operation of Integrated Energy Microgrid Considering PV Output Uncertainty and Shared Energy Storage", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "28", "Issue": "3", "Page": "528", "JournalTitle": "Journal of Advanced Computational Intelligence and Intelligent Informatics"}, {"Title": "Optimal design of PV-SMES systems for power quality enhancement using pelicon optimized multi-level inverter model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "118", "Issue": "", "Page": "109404", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 118205667, "Title": "The predictive value of contrast-enhanced ultrasonography combined with aspartate aminotransferase-to-lymphocyte ratio in predicting therapeutic efficacy and prognosis of post-transarterial chemoembolization in primary liver cancer", "Abstract": "<b >Objective</b> To investigate the predictive value of contrast-enhanced ultrasonography (CEUS) combined with the aspartate aminotransferase-to-lymphocyte ratio (ALRI) in predicting the therapeutic efficacy and prognosis of primary liver cancer after transarterial chemoembolization (TACE). <b >Methods</b> We included 75 patients with hepatocellular carcinoma (HCC) who underwent TACE in our oncology department between January 2019 and December 2021. The patients' clinical data, preoperative ALRI, and postoperative CEUS results were analyzed. <b >Results</b> The optimal cutoff value of ALRI for predicting residual tumor lesions using digital subtraction angiography (DSA) was 42.50. CEUS had a specificity of 0.92, sensitivity of 0.96, and area under the curve (AUC) of 0.940 for determining residual tumor lesions, while ALRI had a specificity of 0.88, sensitivity of 0.70, and AUC of 0.825. The combination of CEUS and ALRI provided a specificity of 0.92, sensitivity of 0.98, and AUC of 0.985 for determining residual tumor lesions. All patients were followed up continuously after the operation. The optimal cutoff value of ALRI for predicting residual tumor lesions in relation to 3-year survival was 41.50. In terms of 3-year survival prediction, DSA showed a specificity of 0.559, sensitivity of 0.889, and AUC of 0.724, CEUS exhibited a specificity of 0.500, sensitivity of 0.852, and AUC of 0.676, and ALRI had a specificity of 0.618, sensitivity of 0.704, and AUC of 0.611. Combined detection of CEUS and ALRI resulted in a specificity of 0.500, sensitivity of 0.889, and AUC of 0.662 for predicting 3-year survival. Patients with negative DSA results, CEUS-negative patients, and those with ALRI ≤41.5 exhibited significantly better survival outcomes compared to their positive counterparts. <b >Conclusion</b> CEUS combined with ALRI yields a higher predictive value in predicting residual tumor lesions and death in patients with HCC after TACE.", "Keywords": "Hepatocellular carcinoma; Transarterial chemoembolization; Contrast-enhanced ultrasonography; Aspartate aminotransferase-to-lymphocyte ratio; Prognosis", "DOI": "10.1016/j.jrras.2024.101111", "PubYear": 2024, "Volume": "17", "Issue": "4", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ultrasound Department of Taizhou People's Hospital, Taizhou, Jiangsu Province, 225300, China;Corresponding author. Taizhou People's Hospital, 366 Taihu Road, Taizhou City, Jiangsu Province, 225300, China"}], "References": []}, {"ArticleId": 118205917, "Title": "Malware communication in smart factories: A network traffic data set", "Abstract": "Machine learning-based intrusion detection requires suitable and realistic data sets for training and testing. However, data sets that originate from real networks are rare. Network data is considered privacy sensitive and the purposeful introduction of malicious traffic is usually not possible. In this paper we introduce a labeled data set captured at a smart factory located in Vienna, Austria during normal operation and during penetration tests with different attack types. The data set consists of 173 GB of Packet Capture (PCAP) files, which represent 16 days (395 h) of factory operation. It includes Message Queuing Telemetry Transport (MQTT), OPC Unified Architecture (OPC UA), and Modbus/TCP traffic. The captured malicious traffic was originated by a professional penetration tester who performed two types of attacks: (a) aggressive attacks that are easier to detect and (b) stealthy attacks that are harder to detect. Our data set includes the raw PCAP files and extracted flow data. Labels for packets and flows indicate whether packets (or flows) originated from a specific attack or from benign communication. We describe the methodology for creating the data set, conduct an analysis of the data and provide detailed information about the recorded traffic itself. The data set is freely available to support reproducible research and the comparability of results in the area of intrusion detection in industrial networks.", "Keywords": "Operational technology; OT; Network traffic; Data set; Industrial control systems; ICS; Intrusion detection system; IDS; Network security; Internet of Things; IoT; IIOT", "DOI": "10.1016/j.comnet.2024.110804", "PubYear": 2024, "Volume": "255", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TU Wien, Gusshausstrasse 25/E389, Vienna, 1040, Austria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "TU Wien, Gusshausstrasse 25/E389, Vienna, 1040, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "TÜV AUSTRIA, Tüv-Austria-Platz 1, Brunn am Gebirge, 2345, Lower Austria, Austria"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "TÜV AUSTRIA, Tüv-Austria-Platz 1, Brunn am Gebirge, 2345, Lower Austria, Austria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "TU Wien, Gusshausstrasse 25/E389, Vienna, 1040, Austria"}], "References": [{"Title": "DoS/DDoS-MQTT-IoT: A dataset for evaluating intrusions in IoT networks using the MQTT protocol", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "109809", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 118205956, "Title": "BoPo Online, BoPo Offline? Engagement with Body Positivity Posts, Positive Appearance Comments on Social Media, and Adolescents' Appearance-Related Prosocial Tendencies", "Abstract": "Encouraging prosocial tendencies toward others' physical appearance is crucial for promoting a positive body image during adolescence. Social media content that highlights positive appearance messages can significantly influence these tendencies. Hence, this three-wave panel study explored the impact of exposure to and the posting of Body Positivity (BoPo) posts and positive appearance comments on social media upon adolescents' offline prosocial behavioral tendencies towards others’ appearances, as mediated by appearance-related prosocial reasoning. Using a sample of adolescents aged 12 to 18 ( N = 496, M <sub>age</sub> = 15.05, SD = 1.49; 67.9% girls), the hypotheses received partial support. At the between-person level, adolescents who more often viewed BoPo and posted positive appearance comments also reported higher appearance-related prosocial tendencies. Nonetheless, on the within-person level, only more often posting positive appearance comments predicted a change to more appearance-related prosocial reasoning from T2 to T3. Notably, the mediation pathways from exposure to and the posting of BoPo and positive appearance comments to appearance-related prosocial tendencies through prosocial reasoning were not significant. Such findings suggest the presence of more significant sources for appearance-related prosocial tendencies than social media during adolescence. Still, the between-person findings can be valuable for peer-led campaigns that aim to foster a positive body image. Such campaigns could identify adolescents with heightened appearance-related prosocial tendencies who tend to engage with body positivity posts and positive appearance comments on social media.", "Keywords": "Body positivity; Social media; Body image; Adolescents; Prosocial tendencies for physical appearance", "DOI": "10.1016/j.chb.2024.108471", "PubYear": 2025, "Volume": "162", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Interdisciplinary Research Team on Internet and Society, Faculty of Social Studies, Masaryk University, Jostova 10, Brno, Czech Republic;Corresponding author. Jostova 10, Brno, 60200, Czech Republic.;Shared first authorship"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Media Psychology Lab, KU Leuven, Leuven, Belgium;Shared first authorship"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Media Psychology Lab, KU Leuven, Leuven, Belgium"}], "References": [{"Title": "Different interactions with appearance-focused social media content and adolescents’ body dissatisfaction: A within-person perspective", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "135", "Issue": "", "Page": "107364", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 118206043, "Title": "Hybrid modeling with data enhanced driven learning algorithm for smart generation control in multi-area integrated energy systems with high proportion renewable energy", "Abstract": "Multi-area integrated energy systems (MA-IES) with a high proportion of renewable energy have become a trend in social development. The access to a large number of distributed energy sources makes the safe, stable, and economic operation of MA-IES suffer serious challenges. To reduce the frequency deviation and area control error (ACE) of MA-IES and to improve the economy of operation, this study proposed a hybrid modeling with data enhanced driven learning algorithm, which combines model-driven and data-driven algorithms, named combined proportion-integral-derivative and deep reinforcement learning (CPIDDRL). Firstly, the frequency deviation signals are collected and judged, and the smaller frequency deviation signals, which are sent to the model-driven part, are regulated by proportion-integral-derivative (PID) controllers. Then, the ACE and large frequency deviation signals are collected and transmitted to the data-driven part, and the Transformer deep learning network is applied to predict the multi-feature timeseries, which are applied to strategy selection for state-action-reward-state-action reinforcement learning. Finally, the model-driven part and data-driven part work together to generate adjustment commands every 4 s. The control effects of CPIDDRL, PID, Q-learning, and sliding model control algorithms are compared in two and four areas integrated energy systems. The results show that, compared to the comparison algorithms, the CPIDDRL reduces the mean values of frequency deviation and ACEs by at least 46.78% and 6.83%, respectively, and the total generation cost by at least 8.20%. CPIDDRL has practical significance in improving system stability, enhancing renewable energy integration capabilities, and promoting the development of smart grids.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125530", "PubYear": 2025, "Volume": "261", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Laboratory of Power System Optimization and Energy Technology, Guangxi University, Nanning, Guangxi 530004, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Guangxi Key Laboratory of Power System Optimization and Energy Technology, Guangxi University, Nanning, Guangxi 530004, China"}], "References": [{"Title": "Fuzzy vector reinforcement learning algorithm for generation control of power systems considering flywheel energy storage", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "125", "Issue": "", "Page": "109149", "JournalTitle": "Applied Soft Computing"}, {"Title": "Index similarity assisted particle filter for early failure time prediction with applications to turbofan engines and compressors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "118008", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Inspired lightweight robust quantum Q-learning for smart generation control of power systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "109804", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel interval forecasting system based on multi-objective optimization and hybrid data reconstruct strategy", "Authors": "<PERSON><PERSON><PERSON> Wang; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "217", "Issue": "", "Page": "119539", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A game-theoretical constructive approach for the multi-objective frequency assignment problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "110444", "JournalTitle": "Applied Soft Computing"}, {"Title": "Reinforcement learning algorithms: A brief survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "120495", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A contrastive learning-based framework for wind power forecast", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "230", "Issue": "", "Page": "120619", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deterministic and probabilistic multi-time-scale forecasting of wind speed based on secondary decomposition, DFIGR and a hybrid deep learning method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "234", "Issue": "", "Page": "121051", "JournalTitle": "Expert Systems with Applications"}, {"Title": "LiConvFormer: A lightweight fault diagnosis framework using separable multiscale convolution and broadcast self-attention", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121338", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Lazy deep Q networks for unified rotor angle stability framework with unified time-scale of power systems with mass distributed energy storage", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107129", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Adaptive event-triggered PID load frequency control for multi-area interconnected wind power systems under aperiodic DoS attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "241", "Issue": "", "Page": "122420", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-objective optimization and decision making for integrated energy system using STA and fuzzy TOPSIS", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "240", "Issue": "", "Page": "122539", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multivariate and hybrid data-driven models to predict thermoelectric power plants fuel consumption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "252", "Issue": "", "Page": "124219", "JournalTitle": "Expert Systems with Applications"}, {"Title": "On compositional generalization of transformer-based neural machine translation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "111", "Issue": "", "Page": "102491", "JournalTitle": "Information Fusion"}, {"Title": "A multi-stage LSTM federated forecasting method for multi-loads under multi-time scales", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "253", "Issue": "", "Page": "124303", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 118206089, "Title": "V2I Physical Layer Security Beamforming with Antenna Hardware Impairments under RIS Assistance", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.056983", "PubYear": 2024, "Volume": "81", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Graph-based deep learning for communication networks: A survey", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "185", "Issue": "", "Page": "40", "JournalTitle": "Computer Communications"}, {"Title": "Cellular traffic prediction with machine learning: A survey", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "201", "Issue": "", "Page": "117163", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-Antenna Array-Based Massive MIMO for B5G/6G: State of the Art, Challenges, and Future Research Directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "8", "Page": "442", "JournalTitle": "Information"}]}, {"ArticleId": 118206115, "Title": "Multiscale Feature Fusion for Gesture Recognition Using Commodity Millimeter-Wave Radar", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.056073", "PubYear": 2024, "Volume": "81", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "Lingsheng Li", "Affiliation": ""}, {"AuthorId": 2, "Name": "Weiqing Bai", "Affiliation": ""}, {"AuthorId": 3, "Name": "Chong Han", "Affiliation": ""}], "References": [{"Title": "XGest: Enabling Cross-Label Gesture Recognition with RF Signals", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}, {"Title": "A Survey on Wireless Device-free Human Sensing: Application Scenarios, Current Solutions, and Open Issues", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "UQRCom", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "A Review of Hand Gesture Recognition Systems Based on Noninvasive Wearable Sensors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "10", "Page": "2300207", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Supporting Mixed-Presence Awareness across Wall-Sized Displays Using a Tracking Pipeline based on Depth Cameras", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "EICS", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 118206135, "Title": "An Efficient Long Short-Term Memory and Gated Recurrent Unit Based Smart Vessel Trajectory Prediction Using Automatic Identification System Data", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.056222", "PubYear": 2024, "Volume": "81", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Adaptively constrained dynamic time warping for time series classification and clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "534", "Issue": "", "Page": "97", "JournalTitle": "Information Sciences"}, {"Title": "TrajVAE: A Variational AutoEncoder model for trajectory generation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "428", "Issue": "", "Page": "332", "JournalTitle": "Neurocomputing"}, {"Title": "A higher prediction accuracy–based alpha–beta filter algorithm using the feedforward artificial neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "4", "Page": "1124", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": *********, "Title": "Towards Optimization Methods for Order Data Management: A Case Study of a Medium-Sized Special Purpose Machinery Manufacturer", "Abstract": "Digital transformations (DT) as part of currently implemented Industry 4.0 projects represent a key role in the competitiveness of manufacturing companies. However, the boundary conditions of a large company must be distinguished from those of a small and medium sized enterprise (SME). This means that an SME has a smaller specialist staff, tighter budgets and less market power available to implement DT projects internally and considering the prevailing conditions. This paper presents a methodology for handling a digitalization project in an SME. Due to its generality, this can be adapted to further use cases. The methodology was developed together with ATA Anlagentechnik (ATA), an SME in special purpose machinery sector. Focused on the internal process of technical sales, the method was used to optimize order data management. This was done through analysis and initial implementations in the areas of object recognition and the creation of efficient user interfaces in order to subject sales sub-processes such as the collection of customer input data to the creation of offers to a digital transformation. Once put into operation and supplemented by further internal or external processes, ATA moves towards complete digital data continuity, which represents an uninterrupted and consistent chain of business processes using digital data and tools. The paper outlines future steps to further refine what has been achieved using the presented DT method.", "Keywords": "Digital Transformation (DT); SME; Digitalization; Order Data Management; Technical Sales; Offer Phase", "DOI": "10.1016/j.ifacol.2024.09.111", "PubYear": 2024, "Volume": "58", "Issue": "19", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Ergonomics, Manufacturing Systems and Automation, Otto-von-Guericke U., Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Ergonomics, Manufacturing Systems and Automation, Otto-von-Guericke U., Germany;Austrian Center for Digital Production"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Ergonomics, Manufacturing Systems and Automation, Otto-von-Guericke U., Germany"}], "References": []}, {"ArticleId": 118206231, "Title": "OCAE and OUNET: Standard automatic optimization for medical image segmentation", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-20287-8", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Meryem <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Mebarka BELAHCENE", "Affiliation": ""}, {"AuthorId": 3, "Name": "Salah BOURENNANE", "Affiliation": ""}], "References": [{"Title": "Application of deep learning for retinal image analysis: A review", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "", "Page": "100203", "JournalTitle": "Computer Science Review"}, {"Title": "Deep learning techniques for skin lesion analysis and melanoma cancer detection: a survey of state-of-the-art", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "2", "Page": "811", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Synergistic learning of lung lobe segmentation and hierarchical multi-instance classification for automated severity assessment of COVID-19 in CT images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107828", "JournalTitle": "Pattern Recognition"}, {"Title": "GFNet: Automatic segmentation of COVID-19 lung infection regions using CT images based on boundary features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "132", "Issue": "", "Page": "108963", "JournalTitle": "Pattern Recognition"}, {"Title": "Optimization driven model and segmentation network for skin cancer detection", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "103", "Issue": "", "Page": "108359", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Medical image segmentation using deep semantic-based methods: A review of techniques, applications and emerging trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "90", "Issue": "", "Page": "316", "JournalTitle": "Information Fusion"}, {"Title": "A Survey on Medical Image Segmentation Based on Deep Learning Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "117", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "Unsupervised medical image feature learning by using de-melting reduction auto-encoder", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "523", "Issue": "", "Page": "145", "JournalTitle": "Neurocomputing"}, {"Title": "Development of a compressed FCN architecture for semantic segmentation using Particle Swarm Optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "16", "Page": "11833", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Improved Tuna Swarm-based U-EfficientNet: Skin Lesion Image Segmentation by Improved Tuna Swarm Optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "903", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}]}, {"ArticleId": 118206243, "Title": "Retraction Note: Utility function for intelligent access web selection using the normalized fuzzy fractional entropy", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-024-10167-6", "PubYear": 2024, "Volume": "28", "Issue": "S1", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Informetrics Research Group, Ton Duc Thang University, Ho Chi Minh City, Vietnam; Faculty of Mathematics and Statistics, Ton Duc Thang University, Ho Chi Minh City, Vietnam; Corresponding author."}], "References": []}]