balance = 0.0  # 初始余额

def add_income():
    global balance
    try:
        amount = float(input("（魏海东 202331060927）请输入收入金额："))
        if amount <= 0:
            print("错误：金额必须大于0！（魏海东 202331060927）")
            return
        balance += amount
        print(f"已记录收入：+{amount:.2f}元（魏海东 202331060927）")
    except ValueError:
        print("错误：请输入有效数字！（魏海东 202331060927）")

def add_expense():
    global balance
    try:
        amount = float(input("（魏海东 202331060927）请输入支出金额："))
        if amount <= 0:
            print("错误：金额必须大于0！（魏海东 202331060927）")
            return
        if balance < amount:
            print("错误：余额不足！（魏海东 202331060927）")
            return
        balance -= amount
        print(f"已记录支出：-{amount:.2f}元（魏海东 202331060927）")
    except ValueError:
        print("错误：请输入有效数字！（魏海东 202331060927）")

def show_balance():
    print(f"当前余额：{balance:.2f}元（魏海东 202331060927）")

def main():
    while True:
        print("\n===== 个人财务助手 =====")
        print("1. 记录收入")
        print("2. 记录支出")
        print("3. 查看余额")
        print("4. 退出系统")
        print("（魏海东 202331060927）")
        choice = input("请选择操作（1-4）：")
        
        if choice == '1':
            add_income()
        elif choice == '2':
            add_expense()
        elif choice == '3':
            show_balance()
        elif choice == '4':
            print("感谢使用，再见！（魏海东 202331060927）")
            break
        else:
            print("（魏海东 202331060927）错误：请输入1-4的选项！")

if __name__ == "__main__":
    main()