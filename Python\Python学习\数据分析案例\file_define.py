"""
和文件相关的定义
"""

from data_define import Record
import json

# 定义抽象类，确定功能
class FileReader:
    def read_data(self) -> list[Record]:
        pass

class TextFileReader(FileReader):
    def __init__(self, file_path: str):
        self.file_path = file_path

    def read_data(self) -> list[Record]:
        f = open(self.file_path, 'r', encoding='utf-8')
        record_list: list[Record] = []
        for line in f:
            line = line.strip()
            data_list = line.split(',')
            record = Record(data_list[0], data_list[1], int(data_list[2]),data_list[3])
            record_list.append(record)
        f.close()

        return record_list

class JsonFileReader(FileReader):
    def __init__(self, file_path: str):
        self.file_path = file_path

    def read_data(self) -> list[Record]:    
        f = open(self.file_path, 'r', encoding='utf-8')
        record_list: list[Record] = []
        for line in f:
            data_dict = json.loads(line)
            record = Record(data_dict['date'], data_dict['order_id'], int(data_dict['money']), data_dict['province'])
            record_list.append(record)

        f.close()
        return record_list



if __name__ == '__main__':
    text_reader = TextFileReader('./data.txt')
    json_reader = JsonFileReader('./data.json')
    list1 = text_reader.read_data()
    list2 = json_reader.read_data()

    for line in list1:
        print(line)
    for line in list2:
        print(line)