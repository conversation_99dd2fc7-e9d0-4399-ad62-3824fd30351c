[{"ArticleId": 144036816, "Title": "Deep Learning in Heart Murmur Detection: Analyzing the Potential of FCNN vs. Traditional Machine Learning Models", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.01602128", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144036817, "Title": "DyGAN: Generative Adversarial Network for Reproducing Handwriting Affected by Dyspraxia", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160222", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144036820, "Title": "Effectiveness of Immersive Contextual English Teaching Based on Fuzzy Evaluation", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160239", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144036821, "Title": "Mobile Application Based on Geolocation for the Recruitment of General Services in Trujillo, La Libertad", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160209", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144036822, "Title": "Comparative Analysis of Undersampling, Oversampling, and SMOTE Techniques for Addressing Class Imbalance in Phishing Website Detection", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160276", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144036837, "Title": "Atom Search Optimization: a comprehensive review of its variants, applications, and future directions", "Abstract": "<p>The Atom Search Optimization (ASO) algorithm is a recent advancement in metaheuristic optimization inspired by principles of molecular dynamics. It mathematically models and simulates the natural behavior of atoms, with interactions governed by forces derived from the Lennard-Jones potential and constraint forces based on bond-length potentials. Since its inception in 2019, it has been successfully applied to various challenges across diverse fields in technology and science. Despite its notable achievements and the rapidly growing body of literature on ASO in the metaheuristic optimization domain, a comprehensive study evaluating the success of its various implementations is still lacking. To address this gap, this article provides a thorough review of half a decade of advancements in ASO research, synthesizing a wide range of studies to highlight key ASO variants, their foundational principles, and significant achievements. It examines diverse applications, including single- and multi-objective optimization problems, and introduces a well-structured taxonomy to guide future exploration in ASO-related research. The reviewed literature reveals that several variants of the ASO algorithm, including modifications, hybridizations, and multi-objective implementations, have been developed to tackle complex optimization problems. Moreover, ASO has been effectively applied across various domains, such as engineering, healthcare and medical applications, Internet of Things and communication, clustering and data mining, environmental modeling, and security, with engineering emerging as the most prevalent application area. By addressing the common challenges researchers face in selecting appropriate algorithms for real-world problems, this study provides valuable insights into the practical applications of ASO and offers guidance for designing ASO variants tailored to specific optimization problems.</p>", "Keywords": "Applications;Atom search optimization;Engineering problems;Global optimization;Hybridization;Metaheuristics", "DOI": "10.7717/peerj-cs.2722", "PubYear": 2025, "Volume": "11", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, College of Science and Humanities in Al-Kharj, Prince <PERSON><PERSON><PERSON> bin Abdul<PERSON> University, Al-Kharj, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Science and Technology, Hassan II University of Casablanca, Mohammedia, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Technology, Sunway University Malaysia, Petaling Jaya, Malaysia;Centre for Research Impact & Outcome, Chitkara University Institute of Engineering and Technology, Chitkara University, Punjab, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering, Helwan University, Egypt, Cairo, Egypt;Applied Science Research Center, Applied Science Private University, Amman, Jordan"}], "References": [{"Title": "Black Widow Optimization Algorithm: A novel meta-heuristic approach for solving engineering optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103249", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A new hybrid chaotic atom search optimization based on tree-seed algorithm and Levy flight for solving optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "3079", "JournalTitle": "Engineering with Computers"}, {"Title": "A better balance in metaheuristic algorithms: Does it exist?", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100671", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Political Optimizer: A novel socio-inspired meta-heuristic for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105709", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An enhanced sitting–sizing scheme for shunt capacitors in radial distribution systems using improved atom search optimization", "Authors": "Rizk M<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "17", "Page": "13971", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Binary atom search optimisation approaches for feature selection", "Authors": "<PERSON><PERSON> Too; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "4", "Page": "406", "JournalTitle": "Connection Science"}, {"Title": "Marine Predators Algorithm: A nature-inspired metaheuristic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "113377", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Inbound tourism demand forecasting framework based on fuzzy time series and advanced optimization algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "106320", "JournalTitle": "Applied Soft Computing"}, {"Title": "Gradient-based optimizer: A new metaheuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "540", "Issue": "", "Page": "131", "JournalTitle": "Information Sciences"}, {"Title": "Plasma generation optimization: a new physically-based metaheuristic algorithm for solving constrained optimization problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "4", "Page": "1554", "JournalTitle": "Engineering Computations"}, {"Title": "A hybrid classical techniques and optimal decision model for iris recognition under variable image quality conditions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "9", "Page": "8913", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An improved atom search optimization with dynamic opposite learning and heterogeneous comprehensive learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107140", "JournalTitle": "Applied Soft Computing"}, {"Title": "At<PERSON> Bird Swarm algorithm-based deep belief network for incremental classification using medical data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "359", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Fractional-atom search algorithm-based deep recurrent neural network for cancer classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "RUN beyond the metaphor: An efficient optimization algorithm based on Runge <PERSON> method", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "115079", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel improved atom search optimization algorithm for designing power system stabilizer", "Authors": "<PERSON>vut <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "2089", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Comparison of metaheuristic optimization algorithms for solving constrained mechanical design optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115351", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Student Performance Prediction Using Atom Search Optimization Based Deep Belief Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "2", "Page": "157", "JournalTitle": "Optical Memory and Neural Networks"}, {"Title": "A feature selection approach for spam detection in social networks using gravitational force-based heuristic algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "1633", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A novel meta-heuristic algorithm for solving numerical optimization problems: <PERSON> and the forty thieves", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "409", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An optimized deep learning model using Mutation-based Atom Search Optimization algorithm for cervical cancer detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "24", "Page": "15363", "JournalTitle": "Soft Computing"}, {"Title": "A Novel Smell Agent Optimization (SAO): An extensive CEC study and engineering application", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "232", "Issue": "", "Page": "107486", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Optimum cycle length models using atom search optimization and grasshopper optimization algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "e6732", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "A new optimization algorithm inspired by the quest for the evolution of human society: Human felicity algorithm", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116468", "JournalTitle": "Expert Systems with Applications"}, {"Title": "INFO: An efficient optimization algorithm based on weighted mean of vectors", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "195", "Issue": "", "Page": "116516", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Renewable sources-based automatic load frequency control of interconnected systems using chaotic atom search optimization", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "", "Page": "108574", "JournalTitle": "Applied Soft Computing"}, {"Title": "A feature selection model for speech emotion recognition using clustering-based population generation with hybrid of equilibrium optimizer and atom search optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "7", "Page": "9693", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "City councils evolution: a socio-inspired metaheuristic optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "9", "Page": "12207", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A secure multi-hop relay node selection scheme based data transmission in wireless ad-hoc network via block chain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "13", "Page": "18343", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Hybrid Meta-Heuristic Algorithms for Optimal Sizing of Hybrid Renewable Energy System: A Review of the State-of-the-Art", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "6", "Page": "4049", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Golden jackal optimization: A novel nature-inspired optimizer for engineering applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116924", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Water atom search algorithm-based deep recurrent neural network for the big data classification based on spark architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "8", "Page": "2297", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Hybrid deep CNN-SVR algorithm for solar radiation prediction problems in Queensland, Australia", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2022, "Volume": "112", "Issue": "", "Page": "104860", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Age estimation from facial images based on Gabor feature fusion and the CIASO‐SA algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "2", "Page": "518", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "Chaotic whale-atom search optimization-based deep stacked auto encoder for crowd behaviour recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "2", "Page": "187", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "Artificial rabbits optimization: A new bio-inspired meta-heuristic algorithm for solving engineering optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "105082", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An accurate generation of image captions for blind people using extended convolutional atom neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "3", "Page": "3801", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An improved atom search optimization for optimization tasks", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "5", "Page": "6375", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Special Relativity Search: A novel metaheuristic method based on special relativity physics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "257", "Issue": "", "Page": "109484", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Intelligent Optimization-Based Clustering with Encryption Technique for Internet of Drones Environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "3", "Page": "6617", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A new approach for crop type mapping in satellite images using hybrid deep capsule auto encoder", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "256", "Issue": "", "Page": "109881", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A qualitative systematic review of metaheuristics applied to tension/compression spring design problem: Current situation, recommendations, and research direction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "118", "Issue": "", "Page": "105521", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A hybrid machine learning technique for early prediction of lung nodules from medical images using a learning‐based neural network classifier", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "3", "Page": "e7488", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Growth Optimizer: A powerful metaheuristic algorithm for solving continuous and discrete global optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "261", "Issue": "", "Page": "110206", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Nutcracker optimizer: A novel nature-inspired metaheuristic algorithm for global optimization and engineering design problems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "262", "Issue": "", "Page": "110248", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "MDP-HML: an efficient detection method for multiple human disease using retinal fundus images based on hybrid learning techniques", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "3", "Page": "961", "JournalTitle": "Multimedia Systems"}, {"Title": "Exponential distribution optimizer (EDO): a novel math-inspired algorithm for global optimization and engineering problems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "9", "Page": "9329", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "<PERSON><PERSON> optimization algorithm: A new metaheuristic algorithm inspired by <PERSON><PERSON>’s laws of planetary motion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "268", "Issue": "", "Page": "110454", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Forecasting NFT Prices on Web3 Blockchain Using Machine Learning to Provide SAAS NFT Collectors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "2", "Page": "55", "JournalTitle": "Fusion: Practice and Applications"}, {"Title": "CEO election optimization algorithm and its application in constrained optimization problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "11", "Page": "7363", "JournalTitle": "Soft Computing"}, {"Title": "Snow ablation optimizer: A novel metaheuristic technique for numerical optimization and engineering design", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "120069", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Analysis of the high-gain BOCUK DC-DC converter-based PFC using an LQR controller for SMPS applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "64", "Issue": "3", "Page": "565", "JournalTitle": "Automatika"}, {"Title": "GMO: geometric mean optimizer for solving engineering problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "15", "Page": "10571", "JournalTitle": "Soft Computing"}, {"Title": "An Efficient Model for Forecasting Renewable Energy Using Ensemble LSTM Based Hybrid Chaotic Atom Search Optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "2", "Page": "1625", "JournalTitle": "Neural Processing Letters"}, {"Title": "Meerkat optimization algorithm: A new meta-heuristic optimization algorithm for solving constrained engineering problems", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "120482", "JournalTitle": "Expert Systems with Applications"}, {"Title": "PID-based search algorithm: A novel metaheuristic algorithm based on PID algorithm", "Authors": "Yuansheng Gao", "PubYear": 2023, "Volume": "232", "Issue": "", "Page": "120886", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Great Wall Construction Algorithm: A novel meta-heuristic algorithm for engineer problems", "Authors": "<PERSON><PERSON><PERSON>; Chang<PERSON> Ren; <PERSON><PERSON>", "PubYear": 2023, "Volume": "233", "Issue": "", "Page": "120905", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Literature Review and Critical Analysis of Metaheuristics Recently Developed", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "31", "Issue": "1", "Page": "125", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Adaptive Dolphin Atom Search Optimization-Based DRNN for Network Intrusion Detection System", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "5", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Crayfish optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "S2", "Page": "1919", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "<PERSON><PERSON><PERSON> shark optimizer: A novel nature-inspired algorithm for engineering optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "58", "Issue": "", "Page": "102210", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A review of metaheuristic algorithms for solving TSP-based scheduling optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "110908", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Sinh Cosh optimizer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "282", "Issue": "", "Page": "111081", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Efficient retinal detachment classification using hybrid machine learning with levy flight-based optimization", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "239", "Issue": "", "Page": "122311", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An atom search optimization approach for IIR system identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "International Journal of Modelling and Simulation"}, {"Title": "Guided learning strategy: A novel update mechanism for metaheuristic algorithms design and improvement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "286", "Issue": "", "Page": "111402", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "FASO-C: A rapid visualization technique based on optimized fusion with crossover-based atom search for multi-band imagery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "249", "Issue": "", "Page": "123609", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Pied kingfisher optimizer: a new bio-inspired algorithm for solving numerical optimization and industrial engineering problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Visualization and Classification of Mushroom Species with Multi-feature Fusion of Metaheuristics-based Convolutional Neural Network Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> Gharehchopogh", "PubYear": 2024, "Volume": "164", "Issue": "", "Page": "111936", "JournalTitle": "Applied Soft Computing"}, {"Title": "Bald eagle search algorithm: a comprehensive review with its variants and applications", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "12", "Issue": "1", "Page": "", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "Multi-population biogeography-based optimization algorithm and its application to image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "109005", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 144036856, "Title": "Adaptive machine learning approaches utilizing soft decision-making <i>via</i> intuitionistic fuzzy parameterized intuitionistic fuzzy soft matrices", "Abstract": "<p> The exponential data growth generated by technological advancements presents significant challenges in analysis and decision-making, necessitating innovative and robust methodologies. Machine learning has emerged as a transformative tool to address these challenges, especially in scenarios requiring precision and adaptability. This study introduces two novel adaptive machine learning approaches, i.e ., AIFPIFSC1 and AIFPIFSC2. These methods leverage the modeling ability of intuitionistic fuzzy parameterized intuitionistic fuzzy soft matrices ( ifpifs -matrices). This state-of-the-art framework enhances the classification task in machine learning by employing soft decision-making through ifpifs -matrices. The proposed approaches are rigorously evaluated against leading fuzzy/soft-based classifiers using 15 widely recognized University of California, Irvine datasets, including accuracy and robustness, across six performance metrics. Statistical analyses conducted using Friedman and Nemenyi tests further substantiate the reliability and superiority of the proposed approaches. The results consistently demonstrate that these approaches outperform their counterparts, highlighting their potential for solving complex classification problems. This study contributes to the field by offering adaptable and effective solutions for modern data analysis challenges, paving the way for future advancements in machine learning and decision-making systems. </p>", "Keywords": "Intuitionistic fuzzy sets;Machine learning;Soft decision-making;Soft sets;ifpifs-matrices", "DOI": "10.7717/peerj-cs.2703", "PubYear": 2025, "Volume": "11", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Marine Engineering, Faculty of Maritime, Bandırma Onyedi Eylül University, Balıkesir, Türkiye"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Gazi University, Ankara, Türkiye"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Graduate School of Natural and Applied Sciences, Gazi University, Ankara, Türkiye"}], "References": [{"Title": "A precise and stable machine learning algorithm: eigenvalue classification (EigenClass)", "Authors": "Uğur Erkan", "PubYear": 2021, "Volume": "33", "Issue": "10", "Page": "5381", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A classification method in machine learning based on soft decision-making via fuzzy parameterized fuzzy soft matrices", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "3", "Page": "1165", "JournalTitle": "Soft Computing"}, {"Title": "Fuzzy parameterized fuzzy soft k-nearest neighbor classifier", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; U. Erkan", "PubYear": 2022, "Volume": "500", "Issue": "", "Page": "351", "JournalTitle": "Neurocomputing"}, {"Title": "Assessment of Solar Panel Using Multiattribute Decision-Making Approach Based on Intuitionistic Fuzzy Aczel Alsina Heronian Mean Operator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "2023", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Picture Fuzzy Parameterized Picture Fuzzy Soft Sets and Their Application in a Performance-Based Value Assignment Problem to Salt-and-Pepper Noise Removal Filters", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "7", "Page": "2860", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "A pre-averaged pseudo nearest neighbor classifier", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "", "Page": "e2247", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 144037101, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0952-1976(25)00422-1", "PubYear": 2025, "Volume": "145", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [], "References": []}, {"ArticleId": 144037118, "Title": "Leveraging LLaMA2 for improved document classification in English", "Abstract": "<p>Document classification is an important component of natural language processing, with applications that include sentiment analysis, content recommendation, and information retrieval. This article investigates the potential of Large Language Model Meta AI (LLaMA2), a cutting-edge language model, to enhance document classification in English. Our experiments show that LLaMA2 outperforms traditional classification methods, achieving higher precision and recall values on the WOS-5736 dataset. Additionally, we analyze the interpretability of LLaMA2’s classification process to reveal the most pertinent features for categorization and the model’s decision-making. These results emphasize the potential of advanced language models to enhance classification outcomes and provide a more profound comprehension of document structures, thereby contributing to the advancement of natural language processing methodologies.</p>", "Keywords": "Deep learning;Document classification;LLaMA2;NLP;Neural networks", "DOI": "10.7717/peerj-cs.2740", "PubYear": 2025, "Volume": "11", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Foreign Languages, Taizhou University, Taizhou City, Jiangsu Province, China."}], "References": [{"Title": "Deep Learning--based Text Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Robo-advisor using genetic algorithm and BERT sentiments from tweets for hybrid portfolio optimisation", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "115060", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Automatic classification of document resources based on Naive Bayesian classification algorithm", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "46", "Issue": "3", "Page": "373", "JournalTitle": "Informatica"}, {"Title": "MFIR: Multimodal fusion and inconsistency reasoning for explainable fake news detection", "Authors": "Lianwei Wu; Yuzhou Long; <PERSON>", "PubYear": 2023, "Volume": "100", "Issue": "", "Page": "101944", "JournalTitle": "Information Fusion"}, {"Title": "Graph neural networks for text classification: a survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "8", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Rethinking of BERT sentence embedding for text classification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "32", "Page": "20245", "JournalTitle": "Neural Computing and Applications"}, {"Title": "LLMFormer: Large Language Model for Open-Vocabulary Semantic Segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "133", "Issue": "2", "Page": "742", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Enhancing Chinese comprehension and reasoning for large language models: an efficient LoRA fine-tuning and tree of thoughts framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "81", "Issue": "1", "Page": "1", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 144037141, "Title": "ANJeL: 日本語を扱う深層ニューラルネットワー クを対象としたブラックボックス敵対的攻撃", "Abstract": "", "Keywords": "", "DOI": "10.1527/tjsai.40-2_C-O52", "PubYear": 2025, "Volume": "40", "Issue": "2", "JournalId": 8299, "JournalTitle": "Transactions of the Japanese Society for Artificial Intelligence", "ISSN": "1346-0714", "EISSN": "1346-8030", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kagoshima University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Kagoshima University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Kagoshima University"}], "References": [{"Title": "Efficiently generating sentence-level textual adversarial examples with Seq2seq Stacked Auto-Encoder", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119170", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 144037239, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0010-4655(25)00060-8", "PubYear": 2025, "Volume": "310", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [], "References": []}, {"ArticleId": 144037290, "Title": "Emotion classification in internet memes utilizing enhanced ConvNeXt and tensor fusion", "Abstract": "", "Keywords": "", "DOI": "10.1007/s41870-025-02431-1", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Sentimental study of CAA by location-based tweets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "4", "Page": "1555", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Sentiment analysis based on aspect and context fusion using attention encoder with LSTM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "7", "Page": "3611", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Mobilenetv3: a deep learning technique for human face expressions identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "6", "Page": "3229", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Leveraging attention layer in improving deep learning models performance for sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Sentiment analysis in product reviews in Thai language", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Differently processed modality and appropriate model selection lead to richer representation of the multimodal input", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Omprak<PERSON> <PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "7", "Page": "4505", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 144037304, "Title": "Usefulness of Deep Learning Techniques Using Magnetic Resonance Imaging for the Diagnosis of Meningioma and Atypical Meningioma", "Abstract": "<p>This study aimed to implement an artificial intelligence (AI) model capable of diagnosing meningioma and atypical meningioma during deep learning using magnetic resonance imaging (MRI). The experimental method was to acquire MRI scans of meningiomas and atypical meningiomas using the T2 weighted imaging (T2WI), T1 weighted imaging (T1WI), contrast enhanced T1WI (CE-T1WI), and contrast enhanced fluid attenuated inversion recovery (CE-FLAIR) methods. The MRI results, according to each method, were categorized into two classes for diagnosing either meningioma or atypical meningioma. The CE-FLAIR images tended to have lower learning performance compared to other methods, but all methods showed excellent diagnostic performance. We confirmed that deep learning is a useful method for diagnosing meningioma and atypical meningioma. When using MRI, if the accuracy and loss rate are improved by applying deep learning optimized for medical images, it will be possible to implement a brain tumor diagnosis model with better learning performance.</p>", "Keywords": "", "DOI": "10.3390/info16030188", "PubYear": 2025, "Volume": "16", "Issue": "3", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, Kyung Hee University Medical Center, Seoul 02447, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, K<PERSON> He<PERSON> University Medical Center, Seoul 02447, Republic of Korea; Department of Neurosurgery, Kyung Hee University College of Medicine, Seoul 02447, Republic of Korea; Department of Neurosurgery, Kyung Hee University Hospital at Gangdong, Seoul 05278, Republic of Korea"}, {"AuthorId": 3, "Name": "Chang Kyu Park", "Affiliation": "Department of Neurosurgery, K<PERSON> Hee University Medical Center, Seoul 02447, Republic of Korea; Department of Neurosurgery, Kyung Hee University College of Medicine, Seoul 02447, Republic of Korea"}], "References": [{"Title": "Artificial Neural Variability for Deep Learning: On Overfitting, Noise Memorization, and Catastrophic Forgetting", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "8", "Page": "2163", "JournalTitle": "Neural Computation"}, {"Title": "Artificial intelligence in disease diagnosis: a systematic literature review, synthesizing framework and future research agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "7", "Page": "8459", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A Systematic Review of Artificial Intelligence (AI) Based Approaches for the Diagnosis of Parkinson’s Disease", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "6", "Page": "3639", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "A Brain Tumor Identification and Classification Using Deep Learning based on CNN-LSTM Method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "107960", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A Comprehensive Survey on Brain Tumor Diagnosis Using Deep Learning and Emerging Hybrid Techniques with Multi-modal MR Image", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "7", "Page": "4871", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "A deep learning approach for brain tumor classification using MRI images", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "108105", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 144037333, "Title": "A deep reinforcement learning control framework for a partially observable system: experimental validation on a rotary flexible link system", "Abstract": "", "Keywords": "", "DOI": "10.1080/00207721.2025.2468870", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Vellore Institute of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vellore Institute of Technology"}], "References": [{"Title": "Deep reinforcement learning approach for MPPT control of partially shaded PV systems in Smart Grids", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106711", "JournalTitle": "Applied Soft Computing"}, {"Title": "Adaptive output-feedback optimal control for continuous-time linear systems based on adaptive dynamic programming approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "438", "Issue": "", "Page": "334", "JournalTitle": "Neurocomputing"}, {"Title": "Fast Real-Time Reinforcement Learning for Partially-Observable Large-Scale Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "3", "Page": "206", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "A CNN-based policy for optimizing continuous action control by learning state sequences", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "468", "Issue": "", "Page": "286", "JournalTitle": "Neurocomputing"}, {"Title": "Deep reinforcement learning based active disturbance rejection control for ship course control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "484", "Issue": "", "Page": "99", "JournalTitle": "Neurocomputing"}, {"Title": "Logistics-involved service composition in a dynamic cloud manufacturing environment: A DDPG-based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "76", "Issue": "", "Page": "102323", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Exploration in deep reinforcement learning: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "85", "Issue": "", "Page": "1", "JournalTitle": "Information Fusion"}, {"Title": "Transfer reinforcement learning method with multi-label learning for compound fault recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101818", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A deep reinforcement learning approach to energy management control with connected information for hybrid electric vehicles", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106239", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Intelligent proximal-policy-optimization-based decision-making system for humanoid robots", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "102009", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Deep reinforcement learning with reward shaping for tracking control and vibration suppression of flexible link manipulator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "152", "Issue": "", "Page": "110756", "JournalTitle": "Applied Soft Computing"}, {"Title": "Voltage control of DC–DC converters through direct control of power switches using reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; Javad <PERSON>", "PubYear": 2023, "Volume": "120", "Issue": "", "Page": "105833", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 144037366, "Title": "Dynamic prototype-guided structural information maintaining for unsupervised domain adaptation", "Abstract": "<p>Unsupervised Domain Adaptation (UDA) intends to transfer the knowledge learned from labeled source domain to unlabeled target domain. Most existing methods employ domain adversarial training to align the feature space distributions of two domains. However, these methods may destroy the discriminative structural information. In this paper, we propose a Dynamic Prototype-guided Structural Information Maintaining (DPSIM) approach to preserve the structural information of the target domain based on pairwise semantic similarity. Specifically, we propose a dynamic prototype learning module to learn the categorical intrinsic representation of the source domain and then to predict the similarity of pairwise samples of the target domain. Finally, a structural information maintaining module is proposed to restrict the target domain by discriminating structural information. Extensive experiments on both image classification and object detection tasks demonstrate the effectiveness of our method.</p>", "Keywords": "Unsupervised domain adaptation; Prototype learning; Structural information; Pairwise similarity", "DOI": "10.1007/s10044-025-01435-8", "PubYear": 2025, "Volume": "28", "Issue": "2", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Songshan Laboratory, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China; Corresponding author."}], "References": [{"Title": "Unsupervised visual domain adaptation via discriminative dictionary evolution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "4", "Page": "1665", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "Compact class-conditional domain invariant learning for multi-class domain adaptation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107763", "JournalTitle": "Pattern Recognition"}, {"Title": "Domain adaption based on source dictionary regularized RKHS subspace learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "4", "Page": "1513", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "Unsupervised Domain Adaptation via Deep Conditional Adaptation Network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109088", "JournalTitle": "Pattern Recognition"}, {"Title": "Prototype-Guided Feature Learning for Unsupervised Domain Adaptation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109154", "JournalTitle": "Pattern Recognition"}, {"Title": "Improving pseudo labels with intra-class similarity for unsupervised domain adaptation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "109379", "JournalTitle": "Pattern Recognition"}, {"Title": "Subdomain adaptation via correlation alignment with entropy minimization for unsupervised domain adaptation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "27", "Issue": "1", "Page": "1", "JournalTitle": "Pattern Analysis and Applications"}]}, {"ArticleId": 144037383, "Title": "A generalised sigmoid population growth model with energy dependence: Application to quantify the tipping point for Antarctic shallow seabed algae", "Abstract": "Sigmoid growth models are often used to study population dynamics. The size of a population at equilibrium commonly depends explicitly on the availability of resources, such as an energy or nutrient source, which is not explicit in standard sigmoid growth models. A simple generalised extension of sigmoid growth models is introduced that can explicitly account for this resource-dependence, demonstrated by three examples of this family of models of increasing mathematical complexity. Each model is calibrated and compared to observed data for algae under sea-ice in Antarctic coastal waters. It was found that through careful construction, models satisfying the proposed framework can estimate key properties of a sea-ice break-out controlled tipping point for the algae, which cannot be estimated using standard sigmoid growth models. The proposed broader family of energy-dependent sigmoid growth models likely has usage in many population growth contexts where resources limit population size.", "Keywords": "Bayesian inference; Logistic growth; Model-data calibration; Regime shift; Sequential Monte Carlo; Tipping point", "DOI": "10.1016/j.envsoft.2025.106397", "PubYear": 2025, "Volume": "188", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Securing Antarctica’s Environmental Future, Queensland University of Technology, Brisbane, 4001, QLD, Australia;School of Mathematical Sciences, Queensland University of Technology, Brisbane, 4000, QLD, Australia;Centre for Data Science, Queensland University of Technology, Brisbane, 4000, QLD, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Evolution & Ecology Research Centre, University of New South Wales, Sydney, 2052, NSW, Australia;Centre of Marine Science and Innovation, University of New South Wales, Sydney, 2052, NSW, Australia;School of Life and Environmental Sciences, University of Sydney, Camperdown, 2006, NSW, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mathematical Sciences, Queensland University of Technology, Brisbane, 4000, QLD, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CSIRO Environment, Hobart, 7001, TAS, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Securing Antarctica’s Environmental Future, Queensland University of Technology, Brisbane, 4001, QLD, Australia;School of Mathematical Sciences, Queensland University of Technology, Brisbane, 4000, QLD, Australia;Centre for Data Science, Queensland University of Technology, Brisbane, 4000, QLD, Australia;School of Chemical Engineering, The University of Queensland, St Lucia, 4072, QLD, Australia"}], "References": [{"Title": "Predicting seagrass decline due to cumulative stressors", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "104717", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": *********, "Title": "Unlocking the potential of quantum computing in prefabricated construction supply chains: Current trends, challenges, and future directions", "Abstract": "The intricate nature of prefabricated construction supply chain management (PCSCM) presents ongoing challenges in production scheduling, inventory control, and logistics coordination. Recent advances in quantum computing (QC) offer compelling approaches to address these multifaceted issues by enabling significantly faster and more precise optimization. This paper systematically reviews and synthesizes existing QC research in the supply chain context, particularly focusing on quantum algorithms that target the PCSCM lifecycle. Our analysis identifies three key domains: production, inventory, and transportation, in which QC can outperform classical methods, as evidenced by enhanced scheduling flexibility and cost minimization. However, our findings also highlight crucial bottlenecks, including quantum hardware limitations, organizational readiness gaps, and a lack of specialized interdisciplinary talent. We propose a framework of strategies to guide QC adoption, such as specialized algorithm development, collaborative research partnerships, and standardized data protocols. These insights offer promising future directions for leveraging QC to streamline operations and boost sustainability in the prefabricated construction sector.", "Keywords": "", "DOI": "10.1016/j.inffus.2025.103043", "PubYear": 2025, "Volume": "120", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Wuhan University, Wuhan 430072, China;Faculty of Business, City University of Macau, 999078, Macao Special Administrative Region of China;Corresponding author at: School of Civil Engineering, Wuhan University, Wuhan 430072, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Wuhan University, Wuhan 430072, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Civil Engineering, Wuhan University, Wuhan 430072, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Automotive Engineers, Hubei University of Automotive Technology, Shiyan 442002, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environment Engineering, University of Maryland, College Park, MD 20742-3021, USA;Chaoyang University of Technology, Taichung 413310, Taiwan;Polish Academy of Science Institute of Theoretical and Applied Informatics, Gliwice 44-100, Poland"}], "References": [{"Title": "Quantum computing based hybrid solution strategies for large-scale discrete-continuous optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "106630", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "An application of interval differential equation on a production inventory model with interval‐valued demand via center‐radius optimization technique and particle swarm optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "8", "Page": "1280", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "An inventory model for non-instantaneous deteriorating items with preservation technology and multiple credit periods-based trade credit financing via particle swarm optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "7", "Page": "5365", "JournalTitle": "Soft Computing"}, {"Title": "A production inventory model with interval-valued carbon emission parameters under price-sensitive demand", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "154", "Issue": "", "Page": "107154", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Sustainable building material selection: An integrated multi-criteria large group decision making framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107903", "JournalTitle": "Applied Soft Computing"}, {"Title": "Interval valued demand related inventory model under all units discount facility and deterioration via parametric approach", "Authors": "<PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "3", "Page": "2455", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Contemporary Quantum Computing Use Cases: Taxonomy, Review and Challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "1", "Page": "615", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "An interval-valued green production inventory model under controllable carbon emissions and green subsidy via particle swarm optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "14", "Page": "9709", "JournalTitle": "Soft Computing"}, {"Title": "Genetic algorithms as classical optimizer for the Quantum Approximate Optimization Algorithm", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110296", "JournalTitle": "Applied Soft Computing"}, {"Title": "QNMF: A quantum neural network based multimodal fusion system for intelligent diagnosis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "100", "Issue": "", "Page": "101913", "JournalTitle": "Information Fusion"}, {"Title": "Hybrid deep learning and quantum-inspired neural network for day-ahead spatiotemporal wind speed forecasting", "Authors": "<PERSON><PERSON><PERSON>; Christian <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "241", "Issue": "", "Page": "122645", "JournalTitle": "Expert Systems with Applications"}, {"Title": "QMFND: A quantum multimodal fusion-based fake news detection model for social media", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "104", "Issue": "", "Page": "102172", "JournalTitle": "Information Fusion"}, {"Title": "Multi-objective Quantum Annealing approach for solving flexible job shop scheduling in manufacturing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "72", "Issue": "", "Page": "142", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Strategic contexts, strategic orientations and organisational technology adoption: A configurational approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "34", "Issue": "4", "Page": "1355", "JournalTitle": "Information Systems Journal"}, {"Title": "Rice Yield Forecasting Using Hybrid Quantum Deep Learning Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "13", "Issue": "8", "Page": "191", "JournalTitle": "Computers"}]}, {"ArticleId": 144037452, "Title": "Multipoint dynamic displacement monitoring of long-span beam bridges and their time-space evolution using a camera-chain system", "Abstract": "Deflection and lateral displacement are critical factors in bridge structural health monitoring. Vision-based displacement monitoring techniques have advantages, such as full-field coverage, high precision, real-time feedback, and automation. However, existing methods still face two key problems that limit their field application: a) an inherent trade-off between measurement range and accuracy, and b) the effect of environmental disturbance on observation platform stability. This paper proposes a camera-chain-based multipoint displacement measurement system for long-span beam bridges. The system primarily consists of double-head camera stations linked by artificial markers. By connecting the optical paths of the camera stations in the physical setup and compensating for motion-induced errors in the measurement model, the system can be deployed along a deformed beam to enable synchronized dynamic measurements at multiple points. Comparative field tests demonstrate that the displacement measurements obtained from this system are consistent with those from traditional methods, such as manual leveling and automated connecting pipe systems, while providing advantages in terms of dynamic monitoring and the breadth of measurement parameters. Benefiting from real-time multipoint measurement, the system captures detailed spatial deformation curves. In addition, the observed vehicle-induced evolution of bridge deflection aligns well with the recorded vehicle speed and can be used to evaluate the modal parameters of beam bridges. The proposed method and system can provide new options and better data for bridge displacement monitoring to support the accuracy and timeliness of safety warnings.", "Keywords": "Beam bridges; Displacement; Camera chain; Motion compensation; Time-space evolution", "DOI": "10.1016/j.compind.2025.104271", "PubYear": 2025, "Volume": "168", "Issue": "", "JournalId": 5958, "JournalTitle": "Computers in Industry", "ISSN": "0166-3615", "EISSN": "1872-6194", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Key Laboratory of Intelligent Optical Measurement and Detection, Shenzhen University, Shenzhen 518000, China;College of Physics and Optoelectronic Engineering, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Key Laboratory of Intelligent Optical Measurement and Detection, Shenzhen University, Shenzhen 518000, China;College of Physics and Optoelectronic Engineering, Shenzhen University, Shenzhen 518060, China;National Key Laboratory of Green and Long-Life Road Engineering in Extreme Environment (Shenzhen), Shenzhen University, Shenzhen 518000, China"}, {"AuthorId": 3, "Name": "Biao Hu", "Affiliation": "Shenzhen Key Laboratory of Intelligent Optical Measurement and Detection, Shenzhen University, Shenzhen 518000, China;College of Physics and Optoelectronic Engineering, Shenzhen University, Shenzhen 518060, China;National Key Laboratory of Green and Long-Life Road Engineering in Extreme Environment (Shenzhen), Shenzhen University, Shenzhen 518000, China;Corresponding author at: Shenzhen Key Laboratory of Intelligent Optical Measurement and Detection, Shenzhen University, Shenzhen 518000, China"}, {"AuthorId": 4, "Name": "Qi<PERSON> Yu", "Affiliation": "Shenzhen Key Laboratory of Intelligent Optical Measurement and Detection, Shenzhen University, Shenzhen 518000, China;College of Physics and Optoelectronic Engineering, Shenzhen University, Shenzhen 518060, China;State Key Laboratory of Radio Frequency Heterogeneous Integration (Shenzhen University), Shenzhen 518000, China;College of Aerospace Science and Engineering, National University of Defense Technology, Changsha 410073, China;Hunan Provincial Key Laboratory of Image Measurement and Vision Navigation, Changsha 410073, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Key Laboratory of Intelligent Optical Measurement and Detection, Shenzhen University, Shenzhen 518000, China;College of Physics and Optoelectronic Engineering, Shenzhen University, Shenzhen 518060, China;National Key Laboratory of Green and Long-Life Road Engineering in Extreme Environment (Shenzhen), Shenzhen University, Shenzhen 518000, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Key Laboratory of Intelligent Optical Measurement and Detection, Shenzhen University, Shenzhen 518000, China;College of Physics and Optoelectronic Engineering, Shenzhen University, Shenzhen 518060, China;National Key Laboratory of Green and Long-Life Road Engineering in Extreme Environment (Shenzhen), Shenzhen University, Shenzhen 518000, China"}, {"AuthorId": 7, "Name": "Zhendong Ge", "Affiliation": "College of Aeronautics, Northwestern Polytechnical University, Xi’an 710072, China;International Research Laboratory of Impact Dynamics and its Engineering Application, Xi’an 710072, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Eagle Eye Online Electronic Technology Co., Ltd, Shenzhen 518115, China"}], "References": [{"Title": "Development of immersive bridge digital twin platform to facilitate bridge damage assessment and asset model updates", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2025, "Volume": "164", "Issue": "", "Page": "104189", "JournalTitle": "Computers in Industry"}]}, {"ArticleId": 144037521, "Title": "Enterprise Digital Transformation: Leveraging AI/ML and Automation for Operational Excellence", "Abstract": "", "Keywords": "", "DOI": "10.32628/CSEIT251112373", "PubYear": 2025, "Volume": "11", "Issue": "1", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Integration of AI Supported Risk Management in ERP Implementation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "37", "JournalTitle": "Computer and Information Science"}]}, {"ArticleId": 144037532, "Title": "Novel ensemble bagging-logistic regression algorithm for NoSQL database security", "Abstract": "Abstract </h3> <p>In the present era, the use of the Internet has drastically increased in the sharing of digital information. In this case, the digital information is stored using cloud technology or NoSQL databases. However, there is a significant challenge in protecting and managing the cloud and NoSQL-based data and extracting required information from these sources while maintaining the actual information. The network traffic has also increased significantly, which requires more memory and sufficient systems to manage and monitor the influx of Big Data. Traditional relational databases face issues in managing and securing the cloud-based dynamic data generated from various sources. NoSQL databases have recently been used to store and manage dynamic data effectively. However, there are security and privacy issues with the NoSQL databases, which remain challenging to provide. Consequently, in the present study, we propose a novel algorithm that enhances the security of the NoSQL databases and predicts its success rate. Initially, we implemented the <PERSON>rnet data masking algorithm to secure the NoSQL database. Then, the secured data is classified and predicted using an innovative proposed method called the Ensemble Bagging Classifier-Logistic Regression (EBC-LR) to validate the accuracy of the secured NoSQL database. The experimental outcomes depict that our proposed algorithm achieves 85 percent accuracy, better than traditional methods in enhancing the security of NoSQL databases. Our proposed algorithm can effectively predict secure standard databases with the highest success rate.</p>", "Keywords": "Ensemble Bagging Classifier-Logistic Regression (EBC-LR); NoSQL database; Data masking; Security; Linear discriminant analysis", "DOI": "10.1007/s10489-025-06358-9", "PubYear": 2025, "Volume": "55", "Issue": "6", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Applications, Dr. <PERSON><PERSON><PERSON> World Peace University, Pune, India"}, {"AuthorId": 2, "Name": "Amol <PERSON>", "Affiliation": "Symbiosis Institute of Computer Studies and Research (SICSR), Symbiosis International (Deemed University), Pune, India; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Symbiosis School for Online and Digital Learning, Symbiosis International (Deemed University), Pune, India"}], "References": [{"Title": "Secure search for encrypted personal health records from big data NoSQL databases in cloud", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "6", "Page": "1521", "JournalTitle": "Computing"}, {"Title": "Evaluating the effects of access control policies within NoSQL systems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "491", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Security policies by design in NoSQL document databases", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "", "Page": "103120", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Security&privacy issues and challenges in NoSQL databases", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "206", "Issue": "", "Page": "108828", "JournalTitle": "Computer Networks"}, {"Title": "Enabling Attribute-Based Access Control in NoSQL Databases", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "11", "Issue": "1", "Page": "208", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Analysis of the Unexplored Security Issues Common to All Types of NoSQL Databases", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "Asian Journal of Research in Computer Science"}, {"Title": "Detection and prevention of SQLI attacks and developing compressive framework using machine learning and hybrid techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> G<PERSON>w Deriba", "PubYear": 2022, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "Applying Detection Leakage on Hybrid Cryptography to Secure Transaction Information in E-Commerce Apps", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "8", "Page": "262", "JournalTitle": "Future Internet"}, {"Title": "An LSTM ‐based novel near‐real‐time multiclass network intrusion detection system for complex cloud environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "11", "Page": "e8024", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Deep learning-based network anomaly detection and classification in an imbalanced cloud environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "232", "Issue": "", "Page": "1636", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 144037577, "Title": "Diffusive network connected chemical reaction networks", "Abstract": "", "Keywords": "", "DOI": "10.1080/21642583.2025.2469615", "PubYear": 2025, "Volume": "13", "Issue": "1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Pannonia; HUN-REN Research Institute for Computer Science and Control"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Brawijaya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sapientia Hungarian University of Transylvania"}], "References": [{"Title": "Control synthesis of reaction–diffusion systems with varying parameters and varying delays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "16", "Page": "3451", "JournalTitle": "International Journal of Systems Science"}]}, {"ArticleId": 144037591, "Title": "On the Reliability Index of the Holo-Hilbert Spectral Analysis", "Abstract": "", "Keywords": "", "DOI": "10.1142/S2424922X25500044", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 17622, "JournalTitle": "Advances in Data Science and Adaptive Analysis", "ISSN": "2424-922X", "EISSN": "2424-9238", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Chi-Hong <PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "A reliable multi-path cluster routing method for ground-air cooperative flying ad-hoc networks", "Abstract": "<p>Most routing methods in ground-air cooperative systems adopt the clustering technique to improve network scalability and topology management. However, their performance often degrades in highly dynamic environments due to inadequate addressing of routing problems, particularly in establishing stable and reliable flying ad-hoc networks for unmanned aerial vehicles (UAVs). In response to this challenge, this study proposes a reliable multi-path cluster routing method for flying ad-hoc networks, exemplified by a border patrol scenario. The proposed method comprises two mechanisms aimed at enhancing communication reliability: one is to use a multi-path routing strategy to select a more stable and load-balanced route to minimize the likelihood of link failure caused by node movement, and the other is to employ a greedy forwarding strategy based on geographical location to repair the broken links in cases of routing link failure. Based on the above mechanisms, network communication performance can be significantly improved, even in highly dynamic environments characterized by high mobility and drastic topology changes. We thoroughly evaluate the performance of the proposed algorithm against state-of-the-art methods using the ns-3 network simulator. The results demonstrate that the proposed method notably enhances data delivery rate and throughput while reducing task completion time.</p>", "Keywords": "Ground-air cooperative; Flying ad-hoc network; Routing; Reliability; Multi-path", "DOI": "10.1007/s00607-025-01442-x", "PubYear": 2025, "Volume": "107", "Issue": "3", "JournalId": 10374, "JournalTitle": "Computing", "ISSN": "0010-485X", "EISSN": "1436-5057", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and software, Nanyang Institute of Technology, Nanyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Artificial Intelligence, Zhengzhou University, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and software, Nanyang Institute of Technology, Nanyang, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON>yong Dong", "Affiliation": "School of Computer Scienc, Wuhan University, Wuhan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Artificial Intelligence, Zhengzhou University, Zhengzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang Bangsun Technology Co. Ltd, Hangzhou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and software, Nanyang Institute of Technology, Nanyang, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and software, Nanyang Institute of Technology, Nanyang, China"}], "References": [{"Title": "Improved routing in MANET with optimized multi path routing fine tuned with hybrid modeling", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "2443", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "An energy-aware clustering and two-level routing method in wireless sensor networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "7", "Page": "1653", "JournalTitle": "Computing"}, {"Title": "A Review of Recent Advances in Coordination Between Unmanned Aerial and Ground Vehicles", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "2", "Page": "97", "JournalTitle": "Unmanned Systems"}, {"Title": "Intelligent cluster routing scheme for flying ad hoc networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "8", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "A Comprehensive Survey of the Key Technologies and Challenges Surrounding Vehicular Ad Hoc Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Improved Sequencing Heuristic DSDV Protocol Using Nomadic Mobility Model for FANETS", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "2", "Page": "3653", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A new DTN routing strategies ensuring high message delivery ratio while keeping low power consumption", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "17", "Issue": "", "Page": "100463", "JournalTitle": "Internet of Things"}, {"Title": "Swarm intelligence algorithms for multiple unmanned aerial vehicles collaboration: a comprehensive review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "5", "Page": "4295", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A greedy perimeter stateless routing method based on a position prediction mechanism for flying ad hoc networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "8", "Page": "101712", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}]}, {"ArticleId": *********, "Title": "Optimizing AutoTVM By Parallel Genetic Algorithms", "Abstract": "", "Keywords": "", "DOI": "10.1142/S0218001425510061", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Xiaohua Shi", "Affiliation": ""}], "References": []}, {"ArticleId": 144037667, "Title": "Transforming Formal Knowledge to Language and Graphs to Promote Mathematics Learning: A Repeated-Measures Mixed Design Quasi-Experiment", "Abstract": "The transition from school to university mathematics presents a significant challenge for students, as both the demands on mathematical reasoning and the level of abstraction increase. This often makes it difficult for learners to construct the mental models necessary for understanding mathematical content and meeting academic requirements. Research has shown that incorporating a second level of content representation—particularly graphical representations—can help students develop more viable mental models. This longitudinal quasi-experimental study aims to enhance mathematical learning in higher education by supporting students' mental modeling. We use a new approach called natural-language conceptual Graph (NaGra), which translates mathematical formalism into natural language. Using computer-linguistic software, we then generate knowledge maps from these texts, providing two distinct types of additional representations to complement traditional instruction. In a 6-point repeated-measures control-group design, 139 math undergraduates received either (a) a natural language text, (b) a knowledge map, (c) both the natural language text and the knowledge map, or (d) the traditional instruction based solely on mathematical formalism. Results from non-parametric longitudinal analyses indicate that students in the experimental conditions consistently outperformed those in the control group over time in mathematical performance. However, students did not perceive the added value of these representations. These findings suggest that the NaGra method can contribute to students’ understanding of STEM subjects (science, technology, engineering, and mathematics), where first-year students often struggle to adapt to abstract formal content.", "Keywords": "Mathematics education; Higher education; Knowledge maps; Natural language processing; Repeated-measures design experiment", "DOI": "10.1016/j.chbr.2025.100640", "PubYear": 2025, "Volume": "18", "Issue": "", "JournalId": 75723, "JournalTitle": "Computers in Human Behavior Reports", "ISSN": "2451-9588", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Educational Psychology, Institute of Pedagogy, Faculty of Philosophy III, <PERSON><PERSON><PERSON>-University Halle-Wittenberg, Germany;Corresponding author. <PERSON><PERSON><PERSON>-University Halle-Wittenberg, Faculty of Philosophy III, Institute of Pedagogy, Educational Psychology, Franckeplatz 1, 06110, Halle (Saale), Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Educational Psychology, Institute of Pedagogy, Faculty of Philosophy III, Martin-Luther-University Halle-Wittenberg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Mathematics, Faculty of Natural Sciences II, Martin-Luther-University Halle-Wittenberg, Germany;<PERSON> is now at Harmonic Analysis, Faculty of Mathematics, University of Technology Chemnitz"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mathematics, Faculty of Natural Sciences II, Martin-Luther-University Halle-Wittenberg, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Educational Psychology, Institute of Pedagogy, Faculty of Philosophy III, Martin-Luther-University Halle-Wittenberg, Germany"}], "References": []}, {"ArticleId": 144037687, "Title": "ARGO: Overcoming hardware dependence in distributed learning", "Abstract": "Mobile devices offer a valuable resource for distributed learning alongside traditional computers, encouraging energy efficiency and privacy through local computations. However, the hardware limitations of these devices makes it impossible to use classical SGD for industry-grade machine learning models (with a very large number of parameters). Moreover, they are intermittently available and susceptible to failures. To address these challenges, we introduce ARGO , an algorithm that combines adaptive workload schemes with Byzantine resilience mechanisms, as well as dynamic device participation. Our theoretical analysis demonstrates linear convergence for strongly convex losses and sub-linear convergence for non-convex losses, without assuming specific dataset partitioning (for potential data heterogeneity). Our formal analysis highlights the interplay between convergence properties, hardware capabilities, Byzantine impact, and standard factors such as mini-batch size and learning rate. Through extensive evaluations, we show that ARGO outperforms standard SGD in terms of convergence speed and accuracy, and most importantly, thrives when classical SGD is not possible due to hardware limitations.", "Keywords": "", "DOI": "10.1016/j.future.2025.107778", "PubYear": 2025, "Volume": "168", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computing, UM6P, Benguerir, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computing, UM6P, Benguerir, Morocco;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Distributed Computing Laboratory, EPFL, Lausanne, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Computing, UM6P, Benguerir, Morocco"}], "References": []}, {"ArticleId": 144037695, "Title": "From Misinformation to Insight: Machine Learning Strategies for Fake News Detection", "Abstract": "<p>In the digital age, the rapid proliferation of misinformation and disinformation poses a critical challenge to societal trust and the integrity of public discourse. This study presents a comprehensive machine learning framework for fake news detection, integrating advanced natural language processing techniques and deep learning architectures. We rigorously evaluate a diverse set of detection models across multiple content types, including social media posts, news articles, and user-generated comments. Our approach systematically compares traditional machine learning classifiers (Naïve <PERSON>es, SVMs, Random Forest) with state-of-the-art deep learning models, such as CNNs, LSTMs, and BERT, while incorporating optimized vectorization techniques, including TF-IDF, Word2Vec, and contextual embeddings. Through extensive experimentation across multiple datasets, our results demonstrate that BERT-based models consistently achieve superior performance, significantly improving detection accuracy in complex misinformation scenarios. Furthermore, we extend the evaluation beyond conventional accuracy metrics by incorporating the Matthews Correlation Coefficient (MCC) and Receiver Operating Characteristic–Area Under the Curve (ROC–AUC), ensuring a robust and interpretable assessment of model efficacy. Beyond technical advancements, we explore the ethical implications of automated misinformation detection, addressing concerns related to censorship, algorithmic bias, and the trade-off between content moderation and freedom of expression. This research not only advances the methodological landscape of fake news detection but also contributes to the broader discourse on safeguarding democratic values, media integrity, and responsible AI deployment in digital environments.</p>", "Keywords": "", "DOI": "10.3390/info16030189", "PubYear": 2025, "Volume": "16", "Issue": "3", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Ionian University, 49100 Corfu, Greece"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Informatics, Ionian University, 49100 Corfu, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, Ionian University, 49100 Corfu, Greece"}], "References": [{"Title": "FakeBERT: Fake news detection in social media with a BERT-based deep learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "8", "Page": "11765", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Deep Learning for Fake News Detection in a Pairwise Textual Input Schema", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "2", "Page": "20", "JournalTitle": "Computation"}, {"Title": "Implementation of the BERT-derived architectures to tackle disinformation challenges", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "23", "Page": "20449", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Knowledge graph informed fake news classification via heterogeneous representation ensembles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>-Šiko<PERSON>", "PubYear": 2022, "Volume": "496", "Issue": "", "Page": "208", "JournalTitle": "Neurocomputing"}, {"Title": "Multimodal Fake News Detection", "Authors": "<PERSON>-<PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "6", "Page": "284", "JournalTitle": "Information"}, {"Title": "A Systematic Literature Review and Meta-Analysis of Studies on Online Fake News Detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "11", "Page": "527", "JournalTitle": "Information"}, {"Title": "A Comparative Study of Machine Learning and Deep Learning Techniques for Fake News Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "12", "Page": "576", "JournalTitle": "Information"}, {"Title": "Fake news detection: Taxonomy and comparative study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; George <PERSON>.<PERSON>", "PubYear": 2024, "Volume": "103", "Issue": "", "Page": "102140", "JournalTitle": "Information Fusion"}, {"Title": "Mapping the Landscape of Misinformation Detection: A Bibliometric Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "1", "Page": "60", "JournalTitle": "Information"}, {"Title": "Fake User Detection Based on Multi-Model Joint Representation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "5", "Page": "266", "JournalTitle": "Information"}, {"Title": "Navigating the Disinformation Maze: A Bibliometric Analysis of Scholarly Efforts", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "12", "Page": "742", "JournalTitle": "Information"}]}, {"ArticleId": 144037710, "Title": "Multi-scale Frequency Feature Fusion Transformer for Pediatric Echocardiography Analysis", "Abstract": "Congenital heart disease (CHD) is the most common birth defect. Echocardiography analysis is essential for identifying pediatric CHD, yet existing methods often focus on single functions and lack comprehensive analysis. In ultrasound data analysis, low-frequency features reflect the overall structure and morphology of the heart, while high-frequency features emphasize the detailed characteristics of cardiac structures. However, existing intelligent analysis models apply a uniform feature processing approach to all echocardiography features, lacking adaptive learning for features at different frequencies. This limitation reduces the model’s capacity to effectively capture both the global shape and local details of echocardiography target structures. Therefore, this paper proposes a multi-scale frequency feature fusion Transformer (MFT-Former) model for the multi-functional analysis of pediatric echocardiography. Specifically, this paper first effectively implements the extraction and aggregation of coarse and fine-grained features of echocardiography by setting a multi-scale patch embedding unit. Secondly, we design a grouped frequency feature fusion Transformer block (GFFT_Block), which contains two parallel branches: an adaptive frequency feature fusion branch (AFF_Branch) and a cross-learning wavelet Transformer branch (CWT_Branch). The former is used for adaptive learning of different frequency features in a local range. The latter achieves cross-learning of different frequency features on a global range and avoids the loss of information caused by dimensionality reduction operations. Based on the parasternal short-axis view, this model identifies ventricular septal defects via echocardiography classification and realizes quantitative analysis through structural segmentation and measurement. Experimental results demonstrate that the MFT-Former outperforms the state-of-the-art algorithms and offers new perspectives for multi-functional echocardiography analysis.", "Keywords": "", "DOI": "10.1016/j.asoc.2025.112950", "PubYear": 2025, "Volume": "173", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National-Regional Key Technology Engineering Laboratory for Medical Ultrasound, Guangdong Key Laboratory for Biomedical Measurements and Ultrasound Imaging, School of Biomedical Engineering, Shenzhen University Medical School, Shenzhen University, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National-Regional Key Technology Engineering Laboratory for Medical Ultrasound, Guangdong Key Laboratory for Biomedical Measurements and Ultrasound Imaging, School of Biomedical Engineering, Shenzhen University Medical School, Shenzhen University, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ultrasound Department, Shenzhen Children Hospital, Hospital of Shantou University, Shantou 518050, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National-Regional Key Technology Engineering Laboratory for Medical Ultrasound, Guangdong Key Laboratory for Biomedical Measurements and Ultrasound Imaging, School of Biomedical Engineering, Shenzhen University Medical School, Shenzhen University, Shenzhen, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National-Regional Key Technology Engineering Laboratory for Medical Ultrasound, Guangdong Key Laboratory for Biomedical Measurements and Ultrasound Imaging, School of Biomedical Engineering, Shenzhen University Medical School, Shenzhen University, Shenzhen, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ultrasound Department, Shenzhen Children Hospital, Hospital of Shantou University, Shantou 518050, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Centre for Smart Health, School of Nursing, The Hong Kong Polytechnic University, Hong Kong"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National-Regional Key Technology Engineering Laboratory for Medical Ultrasound, Guangdong Key Laboratory for Biomedical Measurements and Ultrasound Imaging, School of Biomedical Engineering, Shenzhen University Medical School, Shenzhen University, Shenzhen, China;Corresponding authors"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "National-Regional Key Technology Engineering Laboratory for Medical Ultrasound, Guangdong Key Laboratory for Biomedical Measurements and Ultrasound Imaging, School of Biomedical Engineering, Shenzhen University Medical School, Shenzhen University, Shenzhen, China;Corresponding authors"}], "References": [{"Title": "Multi-scale wavelet network algorithm for pediatric echocardiographic segmentation via hierarchical feature guided fusion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107386", "JournalTitle": "Applied Soft Computing"}, {"Title": "PVT v2: Improved baselines with Pyramid Vision Transformer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "415", "JournalTitle": "Computational Visual Media"}, {"Title": "TransUNet＋: Redesigning the skip connection to enhance features in medical image segmentation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "256", "Issue": "", "Page": "109859", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-level multi-type self-generated knowledge fusion for cardiac ultrasound segmentation", "Authors": "<PERSON><PERSON> Yu; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "92", "Issue": "", "Page": "1", "JournalTitle": "Information Fusion"}, {"Title": "An effective CNN and Transformer complementary network for medical image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "109228", "JournalTitle": "Pattern Recognition"}, {"Title": "Slide deep reinforcement learning networks: Application for left ventricle segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "109667", "JournalTitle": "Pattern Recognition"}, {"Title": "FTransCNN: Fusing Transformer and a CNN based on fuzzy logic for uncertain medical image segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101880", "JournalTitle": "Information Fusion"}, {"Title": "MAXFormer: Enhanced transformer for medical image segmentation with multi-attention and multi-scale features fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "280", "Issue": "", "Page": "110987", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Semi-supervised domain adaptation incorporating three-way decision for multi-view echocardiographic sequence segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "155", "Issue": "", "Page": "111449", "JournalTitle": "Applied Soft Computing"}, {"Title": "<PERSON> co-teaching for semi-supervised medical image segmentation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "152", "Issue": "", "Page": "110426", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 144037735, "Title": "Transforming Enterprise Systems through Event-Driven Architecture: Implementation, Benefits, and Future Trends", "Abstract": "", "Keywords": "", "DOI": "10.32628/CSEIT251112347", "PubYear": 2025, "Volume": "11", "Issue": "1", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "On the impact of event-driven architecture on performance: An exploratory study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "153", "Issue": "", "Page": "52", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 144037776, "Title": "Domain generalization for zero-calibration brain–computer interfaces with knowledge distillation-based phase invariant feature extraction", "Abstract": "The distribution shift of electroencephalography (EEG) data causes poor generalization of brain–computer interfaces (BCIs) in unseen domains. Some methods try to tackle this challenge by collecting a portion of user data for calibration. However, it is time-consuming, mentally fatiguing, and user-unfriendly. To achieve zero-calibration BCIs, most studies employ domain generalization techniques to learn invariant features across different domains in the training set. However, they fail to fully explore invariant features within the same domain, leading to limited performance. In this paper, we present an novel method to learn domain-invariant features from both inter-domain and intra-domain perspectives. For intra-domain invariant features, we propose a knowledge distillation framework to extract EEG phase-invariant features within one domain. As for inter-domain invariant features, correlation alignment is used to bridge distribution gaps across multiple domains. Experimental results on three public datasets validate the effectiveness of our method, showcasing state-of-the-art performance. To the best of our knowledge, this is the first domain generalization study that exploit Fourier phase information as an intra-domain invariant feature to facilitate EEG generalization. More importantly, the zero-calibration BCI based on inter- and intra-domain invariant features has significant potential to advance the practical applications of BCIs in real world. (The code is available on https://github.com/ZilinL/KnIFE ).", "Keywords": "", "DOI": "10.1016/j.engappai.2025.110340", "PubYear": 2025, "Volume": "148", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hangzhou Innovation Institute, Beihang University, Hangzhou 310052, China;School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191, China;Hangzhou Innovation Institute, Beihang University, Hangzhou 310052, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hangzhou Innovation Institute, Beihang University, Hangzhou 310052, China;School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191, China;Corresponding author at: Hangzhou Innovation Institute, Beihang University, Hangzhou 310052, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Ma", "Affiliation": "Hangzhou Innovation Institute, Beihang University, Hangzhou 310052, China;School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191, China"}, {"AuthorId": 5, "Name": "Zhongcai Pei", "Affiliation": "Hangzhou Innovation Institute, Beihang University, Hangzhou 310052, China;School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191, China"}, {"AuthorId": 6, "Name": "Xiantao Sun", "Affiliation": "School of Electrical Engineering and Automation, Anhui University, Anhui 230039, China;Corresponding author"}], "References": [{"Title": "A review on transfer learning in EEG signal analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "421", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Spatial filtering based on Riemannian distance to improve the generalization of ErrP classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON> J<PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "236", "JournalTitle": "Neurocomputing"}, {"Title": "Adaptive transfer learning-based multiscale feature fused deep convolutional neural network for EEG MI multiclassification in brain–computer interface", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105347", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An adaptive teacher–student learning algorithm with decomposed knowledge distillation for on-edge intelligence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105560", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Differentiated knowledge distillation: Patient-specific single-sample personalization for electrocardiogram diagnostic models", "Authors": "<PERSON><PERSON> Wei; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "136", "Issue": "", "Page": "108880", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Entropy-guided robust feature domain adaptation for electroencephalogram-based cross-dataset drowsiness recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "137", "Issue": "", "Page": "109153", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 144037844, "Title": "Cultivating Cluster Enactment in Wireless Sensor Networks Through Hybrid Metaheuristics Intended for Energy Optimization", "Abstract": "<p>Though traditional search methods shine when faced with local goals and a plethora of decision factors, they could struggle when faced with engineering design limitations. On the other hand, metaheuristic approaches promise to find global optima in real-world optimization problems, even in nonlinear and multimodal settings. In order to improve cluster head selection in Wireless Sensor Networks (WSNs), this research presents a new metaheuristic method that combines the principles of Cuckoo Search method (CSA) and Selection by Optimization (SBO). When choosing a Chief Cluster Head (CCH), the CSA algorithm takes hop count into account, whereas SBO finds the best cluster heads by considering communication range. By comparing the suggested approach to both stand-alone SBO and CSA methods, we can see how well it performs in terms of throughput, latency, energy usage, delivery ratio, and packet overhead. The combined SBO-CSA method improves cluster performance, simplifies cluster head selection in WSNs, and increases network efficiency overall, according to the results. To prove its efficacy in enhancing network performance, the suggested solution outperforms standalone SBO and CSA approaches across a range of performance parameters, such as throughput, latency, energy consumption, and packet overhead.</p>", "Keywords": "Metaheuristic methods; SBO; CSA; CH; CCH", "DOI": "10.1007/s42979-025-03702-1", "PubYear": 2025, "Volume": "6", "Issue": "3", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Rungta College of Engineering and Technology, R1, Bhilai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, College of Science and Theoretical Studies, Saudi Electronic University, Riyadh, Saudi Arabia; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, GITAM (Deemed to be University), Visakhapatnam, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Manipal University Jaipur, Jaipur, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Chitkara University Institute of Engineering and Technology, Chitkara University, Rajpura, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, Vignan’s Foundation for Science, Technology and Research, Guntur, India"}], "References": [{"Title": "MaReSPS for energy efficient spectral precoding technique in large scale MIMO-OFDM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "58", "Issue": "", "Page": "102057", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 144037856, "Title": "Application of Adaptive Hierarchical Grids to Simulation of Laser-Generated Plasma Fluxes", "Abstract": "<p>A computational technique for high-temperature plasma flows produced by intensive laser action on solid targets is presented with application of hierarchic adaptively refined meshes for adequate resolution of the flow structure. The model is based on gasdynamics equations including effects of heat conductivity, laser beam propagation and energy transport by thermal radiation. Governing system of equations, expressed in the form of conservation laws, is approximated by a difference scheme of high accuracy order. The modeling technique is implemented using AMReX—a software environment for applications of massively parallel, block-structured locally adaptive-refined grids. A test example, which consists of modeling the plasma flow during heating of Sn-target by nanosecond laser radiation, demonstrates а good quality of the numerical solution.</p>", "Keywords": "difference schemes; adaptive mesh refinement; laser plasma; parallel computing", "DOI": "10.1134/S2070048224700893", "PubYear": 2024, "Volume": "16", "Issue": "S2", "JournalId": 19732, "JournalTitle": "Mathematical Models and Computer Simulations", "ISSN": "2070-0482", "EISSN": "2070-0490", "Authors": [{"AuthorId": 1, "Name": "A. A. Bay", "Affiliation": "Keldysh Institute of Applied Mathematics, Russian Academy of Sciences, Moscow, Russia; Corresponding author."}], "References": []}, {"ArticleId": 144037892, "Title": "Parkinson’s disease detection from speech using combination of empirical wavelet transform and Hilbert transform", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10772-025-10172-6", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 4265, "JournalTitle": "International Journal of Speech Technology", "ISSN": "1381-2416", "EISSN": "1572-8110", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Non-negative matrix factorization-based time-frequency feature extraction of voice signal for Parkinson's disease prediction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>-Arroyave", "PubYear": 2021, "Volume": "69", "Issue": "", "Page": "101216", "JournalTitle": "Computer Speech & Language"}, {"Title": "A Hybrid Approach for Parkinson’s Disease diagnosis with Resonance and Time-Frequency based features from Speech signals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115283", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Identification of Parkinson’s disease from speech signal using machine learning approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "4", "Page": "981", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "A two-stage ensemble approach for the analysis of Parkinson’s Disease using speech signals", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 144037896, "Title": "THE BASICS OF DATA PRIVACY IN HEALTHCARE AI: MAKING SENSE OF HIPAA-COMPLIANT MACHINE LEARNING", "Abstract": "", "Keywords": "", "DOI": "10.34218/IJITMIS_16_01_057", "PubYear": 2025, "Volume": "16", "Issue": "1", "JournalId": 66348, "JournalTitle": "INTERNATIONAL JOURNAL OF INFORMATION TECH<PERSON><PERSON>OGY AND MANAGEMENT INFORMATION SYSTEMS", "ISSN": "0976-6405", "EISSN": "0976-6413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144037903, "Title": "INTELLIGENT FINANCIAL ANALYTICS: AI-POWERED BUSINESS INTELLIGENCE IN THE DIGITAL AGE", "Abstract": "", "Keywords": "", "DOI": "10.34218/IJITMIS_16_01_055", "PubYear": 2025, "Volume": "16", "Issue": "1", "JournalId": 66348, "JournalTitle": "INTERNATIONAL JOURNAL OF INFORMATION TECH<PERSON><PERSON>OGY AND MANAGEMENT INFORMATION SYSTEMS", "ISSN": "0976-6405", "EISSN": "0976-6413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144037915, "Title": "ENTERPRISE DIGITAL WALLET INTEGRATION: MOD<PERSON>NIZING ERP FINANCIAL OPERATIONS", "Abstract": "", "Keywords": "", "DOI": "10.34218/IJITMIS_16_01_073", "PubYear": 2025, "Volume": "16", "Issue": "1", "JournalId": 66348, "JournalTitle": "INTERNATIONAL JOURNAL OF INFORMATION TECH<PERSON><PERSON>OGY AND MANAGEMENT INFORMATION SYSTEMS", "ISSN": "0976-6405", "EISSN": "0976-6413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144037925, "Title": "Numerical Study of Wave Processes during Oxidative Regeneration of a Stationary Catalyst Layer", "Abstract": "<p>An The article is devoted to the study of the heat waves formation and the combustion front propagation in a stationary catalyst layer during the burning of coke sedimentation based on a mathematical model. Two modes of the process management are investigated: stationary (boundary conditions for temperature and concentration of reagents in the reaction mixture are constant) and dynamic (temperature and composition of the reaction mixture are non-stationary). The mathematical model is described by equations of mathematical physics and ilcludes diffusion, convection and chemical reactions in the pores of the catalyst grain, heat and mass transfer in the gas flow, heat propagation through the catalyst skeleton and heat and mass transfer between the catalyst and the gas. The explicit-implicit computational algorithm is based on the integro-interpolation method using the principle of splitting by physical processes: chemistry problems are solved by the three-stage <PERSON><PERSON><PERSON> method of the fifth accuracy order, hyperbolization is used to calculate diffusion fluxes, convection is calculated conservatively. The transfer equations and third-kind boundary conditions are approximated implicitly. The article presents the results of serial calculations for various initial and boundary conditions to study the heat waves formation and the combustion front propagation along the catalyst.</p>", "Keywords": "mathematical modeling; numerical methods; wave processes; combustion front; dynamic mode; chemical kinetics; oxidative regeneration", "DOI": "10.1134/S2070048224700972", "PubYear": 2024, "Volume": "16", "Issue": "S2", "JournalId": 19732, "JournalTitle": "Mathematical Models and Computer Simulations", "ISSN": "2070-0482", "EISSN": "2070-0490", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Steklov Mathematical Institute, Russian Academy of Sciences, Moscow, Russia; Corresponding author."}], "References": [{"Title": "An Explicit Difference Scheme for a Nonlinear Heat Conduction Equation", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "3", "Page": "529", "JournalTitle": "Mathematical Models and Computer Simulations"}, {"Title": "Numerical Simulation of Oxidative Regeneration of a Spherical Catalyst Grain", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "3", "Page": "485", "JournalTitle": "Mathematical Models and Computer Simulations"}]}, {"ArticleId": 144037944, "Title": "A statistical categorization-based curriculum learning approach for multi-task classification of images", "Abstract": "<p>Image classification and the detection of features within images remain significant challenges in computer vision. Several approaches, including serial task models and multi-output models, have been explored to address these challenges. This study focuses on multitasking attention mechanisms, which enable simultaneous categorization of data and tasks. By applying a statistical framework, the proposed method enhances the efficiency and accuracy of image classification and feature detection, with a focus on handling multiple tasks concurrently. To enhance the robustness of the model, a data-driven approach based on curriculum learning was proposed. The experiments were conducted using two distinct datasets. The first dataset involves forensic examinations, specifically identifying firearms and their calibers from firing pin marks. The proposed model achieved an accuracy of 95% in brand detection and 98% in caliber detection on this dataset. In the second part of the experiments, the animals with attributes 2 (AwA2) dataset, where state-of-the-art models have previously been applied, was used. The proposed model reduced classification errors by 1 to 10% compared to traditional convolutional neural network (CNN) architectures. The experimental results from both the forensic and public datasets demonstrate that the proposed model effectively handles multitask classification tasks, validating its applicability across diverse domains.</p>", "Keywords": "Deep neural networks; Progressive learning; Multi-task learning; Firearm detection; Curriculum learning", "DOI": "10.1007/s10489-025-06270-2", "PubYear": 2025, "Volume": "55", "Issue": "6", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Bahcesehir University, Istanbul, Turkey; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Bahcesehir University, Istanbul, Turkey"}], "References": [{"Title": "The multi-mode operation decision of cleaning robot based on curriculum learning strategy and feedback network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "12", "Page": "9955", "JournalTitle": "Neural Computing and Applications"}, {"Title": "TC3KD: Knowledge distillation via teacher-student cooperative curriculum customization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "508", "Issue": "", "Page": "284", "JournalTitle": "Neurocomputing"}, {"Title": "Temporal action detection with dynamic weights based on curriculum learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "524", "Issue": "", "Page": "106", "JournalTitle": "Neurocomputing"}, {"Title": "Cascaded structure tensor for robust baggage threat detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "15", "Page": "11269", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 144038019, "Title": "Generic Spectral Library Framework for Urban Land Cover Mapping with Optical Remote Sensing Imagery", "Abstract": "Spectral libraries link surface reflectance characteristics to thematic cover type interpretation. Despite their potential, spectral libraries are rarely used beyond their original application or analysis. In this paper we introduce the concept of a Generic Urban Spectral Library (GUSL). A GUSL is a thoroughly labelled collection of multi-site, -sensor and -temporal spectral libraries that supports urban mapping. The GUSL is envisioned as an open data source equipped with tools that facilitate deployment for different mapping purposes and image types. We introduce the GUSL concept, present a preliminary implementation containing eight spectral libraries and discuss two use case experiments performed with the spectral libraries and the GSL-tools currently included in the GUSL. The first use case focuses on GUSL expansion with image-derived spectral endmembers. From a 200 by 200-pixel hyperspectral urban image, we extract 229 endmember spectra. By comparing the extracted spectra to the spectra already included in the experimental GUSL, we demonstrate how the GUSL can reduce the burden of endmember labelling and identify spectra that are novel to the GUSL. In the second use case the experimental GUSL is used for mapping urban land cover from airborne hyperspectral data in two cities (Munich, Brussels) for which local spectra are included in the GUSL, and in a third city (Pavia) for which no local spectra are present in the library. Generalized material group and more detailed artificial material type mapping yield average user and producer accuracies ranging between 0.72 and 0.96. When mapping is performed on the Pavia image, the obtained material group accuracies remain reasonable, i.e., around 0.80. Our experimental results confirm the potential of the GUSL for urban library building and mapping. Future research avenues are proposed to move towards an operational GUSL.", "Keywords": "", "DOI": "10.1016/j.envsoft.2025.106405", "PubYear": 2025, "Volume": "188", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cartography and GIS Research Group, Vrije Universiteit Brussel, Pleinlaan 2, 1050, Brussel, Belgium;Division Forest, Nature and Landscape, KU Leuven, Celestijnenlaan 200E, 3001, Leuven, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "North Rhine-Westphalia Office of Nature, Environment and Consumer Protection (LANUV), Leibnizstraße 10 45659 Recklinghausen, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Earth Observation Center, German Aerospace Center, Oberpfaffenhofen, 82234, Weßling, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Division Forest, Nature and Landscape, KU Leuven, Celestijnenlaan 200E, 3001, Leuven, Belgium;KU Leuven Urban Studies Institute (LUSI), KU Leuven, Belgium"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Cartography and GIS Research Group, Vrije Universiteit Brussel, Pleinlaan 2, 1050, Brussel, Belgium;Corresponding author. Cartography and GIS Research Group, Vrije Universiteit Brussel, Pleinlaan 2, 1050 Brussel, Belgium"}], "References": [{"Title": "Visualizing and labeling dense multi-sensor earth observation time series: The EO Time Series Viewer", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "125", "Issue": "", "Page": "104631", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Mapping urban-rural gradients of settlements and vegetation at national scale using Sentinel-2 spectral-temporal metrics and regression-based unmixing with synthetic training data", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "246", "Issue": "", "Page": "111810", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Predicting developed land expansion using deep convolutional neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "104751", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Laboratory-based spectral data acquisition of roof materials", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "23", "Page": "9180", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "A spatial-spectral clustering-based algorithm for endmember extraction and hyperspectral unmixing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "5", "Page": "1948", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "QLSU (QGIS Linear Spectral Unmixing) Plugin: An open source linear spectral unmixing tool for hyperspectral & multispectral remote sensing imagery", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "168", "Issue": "", "Page": "105782", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Appraisal of EnMAP hyperspectral imagery use in LULC mapping when combined with machine learning pixel-based classifiers", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "173", "Issue": "", "Page": "105956", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 144038087, "Title": "Dual function of MrgprB2 receptor-dependent neural immune axis in chronic pain", "Abstract": "<p><b>INTRODUCTION</b>:Neuro-immune interactions have been recognized to be involved in the development of neuropathic pain induced by chemotherapeutic drugs (CINP). However, its role in pain resolution remains largely unknown, particularly concerning mast cells.</p><p><b>OBJECTIVES</b>:To investigate the bidirectional modulation of mast cell Mas-related G protein-coupled receptor B2 (MrgprB2)-mediated neuro-immune interactions in CINP.</p><p><b>METHODS</b>:CINP model was established in wild-type mice, Mas-related G protein-coupled receptor D knockout (MrgprD<sup>-/-</sup>) mice, mast cell-deficient mice, MrgprB2 knockout (MrgprB2<sup>-/-</sup>) mice, and MrgprB2-Cre tdTomato mice. The role of MrgprB2 receptor in CINP was investigated by calcium imaging, cytokine antibody arrays, mining of single-cell sequencing databases, immunofluorescence, western blotting, co-immunoprecipitation (Co-IP), among other methodologies.</p><p><b>RESULTS</b>:We observed that cisplatin-induced allodynia was significantly inhibited in MrgprB2<sup>-/-</sup> mice, which was attributed to the blockade of tryptase release and the suppression of upregulation of protease-activated receptor 2 (PAR2) expression in dorsal root ganglion (DRG). Thus, the activation of MrgprB2/Tryptase/PAR2 axis contributed to the development of cisplatin-induced pain. In addition, we also found that there was co-expression of PAR2 and MrgprD in DRG neurons. And activation of PAR2 can negatively regulate the expression of MrgprD, whether in a physiological state or in a chronic pain condition. Consequently, MrgprD expression was down-regulated by the activation of the MrgprB2/Tryptase/PAR2 axis during the later stages of CINP, which was associated with pain relief. Therefore, the activation of MrgprB2/Tryptase/PAR2 axis also contributed to the alleviation of cisplatin-induced pain. This finding was in line with the phenomenon that persistent stimulation by cisplatin did not cause a continuous increase in pain.</p><p><b>CONCLUSIONS</b>:Our research elucidated the bidirectional modulation of MrgprB2-dependent neural immune axis in CINP. This study emphasized that MrgprB2 is a critical target for early intervention in CINP, and highlighted the necessity of considering the mechanism differences at different stages in pain management.</p><p>Copyright © 2025. Published by Elsevier B.V.</p>", "Keywords": "CINP;MrgprB2 receptor;MrgprD receptor;PAR2 receptor", "DOI": "10.1016/j.jare.2025.02.037", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chinese Medicine, Nanjing University of Chinese Medicine, 138 Xianlin Road, Nanjing 210023, China."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Pharmacy, Jishou University, Jishou 416000, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medicine, Nanjing University of Chinese Medicine, 138 Xianlin Road, Nanjing 210023, China."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Medicine, Nanjing University of Chinese Medicine, 138 Xianlin Road, Nanjing 210023, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medicine, Nanjing University of Chinese Medicine, 138 Xianlin Road, Nanjing 210023, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chinese Medicine, Nanjing University of Chinese Medicine, 138 Xianlin Road, Nanjing 210023, China."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Medicine, Nanjing University of Chinese Medicine, 138 Xianlin Road, Nanjing 210023, China."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Medicine, Nanjing University of Chinese Medicine, 138 Xianlin Road, Nanjing 210023, China."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Organic Electronics and Information Displays & Institute of Advanced Materials (IAM), Nanjing University of Posts & Telecommunications, 9 Wenyuan Road, Nanjing 210023, China."}, {"AuthorId": 10, "Name": "Guang Yu", "Affiliation": "School of Medicine, Nanjing University of Chinese Medicine, 138 Xianlin Road, Nanjing 210023, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Medicine, Nanjing University of Chinese Medicine, 138 Xianlin Road, Nanjing 210023, China"}], "References": []}, {"ArticleId": 144038146, "Title": "Novel Statistical Modelling and Optimization Techniques of Fading Channel Coefficients for 5G Network Performance", "Abstract": "<p>The channel estimation process (CE) is a pivotal element in wireless communication employing orthogonal frequency division multiplexing (OFDM) modulation. The derived coefficient can represent the channel characterization. A newly proposed model derived for fifth-generation (5G) network and future communication systems two encounter the fading effects of the channel. The proposed model improves the channel estimation accuracy by incorporating precise measurements of channel coefficients, enhancing the overall performance. The model uses least square (LS) and minimum mean squared error (MMSE) optimization algorithms for the channel coefficient extraction process. The effectiveness of the proposed model has been experimentally confirmed using different fading channel scenarios in the literature. The mean square error (MSE) and bit error rate (BER) test criteria are used for performance measures. The test was generalized using a 64, 256 and 1024 quadrature amplitude modulation (QAM) scheme, demonstrating the model’s applicability to various communication scenarios. The proposed model was evaluated alongside several state-of-the-art statistical channel estimation methods to demonstrate its performance across various tests. When compared to pilot-based estimation, the model consistently exhibits superior accuracy and adaptability, ensuring dependable performance in a wide range of communication scenarios. Additionally, the proposed model is better suited to handle the complexity of non-line-of-sight (NLOS) and multipath environments, offering a more nuanced approach compared to pilot signals, which often rely on idealized assumptions of channel linearity.</p>", "Keywords": "Channel estimation; Faded channel; 5G; Bit error rate; Channel coefficients", "DOI": "10.1007/s10922-025-09905-4", "PubYear": 2025, "Volume": "33", "Issue": "2", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Electronic Engineering, Near East University, Mersin 10, Turkey; Near East University Science, Technology, Engineering Application and Research Center (BİLTEM), Mersin 10, Turkey; Corresponding author."}, {"AuthorId": 2, "Name": "Bülent Bilgehan", "Affiliation": "Electrical and Electronic Engineering, Near East University, Mersin 10, Turkey; Near East University Science, Technology, Engineering Application and Research Center (BİLTEM), Mersin 10, Turkey"}], "References": [{"Title": "Blind channel estimation for DCO-OFDM based vehicular visible light communication", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "101942", "JournalTitle": "Physical Communication"}, {"Title": "Deep learning for joint channel estimation and feedback in massive MIMO systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "1", "Page": "83", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Pilot based channel estimation improvement in orthogonal frequency-division multiplexing systems using linear predictive coding", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "14", "Issue": "1", "Page": "418", "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)"}, {"Title": "Cluster-Based Hybrid Approach for PCI Configuration and Optimization in 5G EN-DC Heterogeneous Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "32", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "Empowering UAV Communications with AI-Assisted Software-Defined Networks: A Review on Performance, Security, and Efficiency", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "32", "Issue": "4", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "Implementation and analysis of 5G network identification operations at low signal-to-noise ratio", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "21", "Issue": "3", "Page": "496", "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)"}]}, {"ArticleId": 144038180, "Title": "Bayesian Uncertainty Weighted Optimization for Offline Reinforcement Learning", "Abstract": "", "Keywords": "", "DOI": "10.1142/S0218001425510012", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144038181, "Title": "DGNet: A Double-Graph Framework Combined with Occluded Person Re-Identification for the Prediction of Pedestrian Flow in Scenic Spots", "Abstract": "", "Keywords": "", "DOI": "10.1142/S0218001425550031", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144038186, "Title": "Avoiding the formation of pores during laser welding of copper hairpins by dynamic beam shaping", "Abstract": "Any pores formed during laser welding of copper hairpins affect the structural integrity and the mechanical/electrical functionality of the joint. We report on investigations using synchrotron X-ray imaging techniques to observe the formation of the pores in the processing zone while welding. It was found that all pores are formed at the joint gap and that the small pores are distributed throughout the complete joint volume as a result of the melt flow induced by the movement of the laser beam. This insight has led to the development of a welding strategy that minimizes pore formation by avoiding the movement of the laser beam across the joint gap. This was achieved by rapid beam shaping based on coherent beam combining (CBC) technology.", "Keywords": "", "DOI": "10.1007/s00170-025-15236-0", "PubYear": 2025, "Volume": "137", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Tailored laser beam shapes for welding of copper using green laser radiation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2025, "Volume": "136", "Issue": "9", "Page": "3843", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 144038190, "Title": "Dialogue-Based Disease Diagnosis Using Hierarchical Reinforcement Learning with Multi-Expert Feedback", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160232", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144038191, "Title": "Enhancing Urban Mapping in Indonesia with YOLOv11", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160261", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Alesandra Zhegita Helga <PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> -", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144038213, "Title": "AI-POWERED SUPPLY CHAIN INNOVATION: FROM PREDICTIVE ANALYTICS TO WAREHOUSE AUTOMATION", "Abstract": "", "Keywords": "", "DOI": "10.34218/IJITMIS_16_01_063", "PubYear": 2025, "Volume": "16", "Issue": "1", "JournalId": 66348, "JournalTitle": "INTERNATIONAL JOURNAL OF INFORMATION TECH<PERSON><PERSON>OGY AND MANAGEMENT INFORMATION SYSTEMS", "ISSN": "0976-6405", "EISSN": "0976-6413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144038281, "Title": "Joint image-instance spatial-temporal attention for few-shot action recognition", "Abstract": "Few-shot Action Recognition (FSAR) constitutes a crucial challenge in computer vision, entailing the recognition of actions from a limited set of examples. Recent approaches mainly focus on employing image-level features to construct temporal dependencies and generate prototypes for each action category. However, a considerable number of these methods utilize mainly image-level features that incorporate background noise and focus insufficiently on real foreground (action-related instances), thereby compromising the recognition capability, particularly in the few-shot scenario. To tackle this issue, we propose a novel joint Image-Instance level Spatial–temporal attention approach (I 2 ST) for Few-shot Action Recognition. The core concept of I 2 ST is to perceive the action-related instances and integrate them with image features via spatial–temporal attention. Specifically, I 2 ST consists of two key components: Action-related Instance Perception and Joint Image-Instance Spatial–temporal Attention. Given the basic representations from the feature extractor, the Action-related Instance Perception is introduced to perceive action-related instances under the guidance of a text-guided segmentation model. Subsequently, the Joint Image-Instance Spatial–temporal Attention is used to construct the feature dependency between instances and images. To enhance the prototype representations of different categories of videos, a pair of spatial–temporal attention sub-modules is introduced to combine image features and instance embeddings across both temporal and spatial dimensions, and a global fusion sub-module is utilized to aggregate global contextual information, then robust action video prototypes can be formed. Finally, based on the video prototype, a Global–Local Prototype Matching is performed for reliable few-shot video matching. In this manner, our proposed I 2 ST can effectively exploit the foreground instance-level cues and model more accurate spatial–temporal relationships for the complex few-shot video recognition scenarios. Extensive experiments across standard few-shot benchmarks demonstrate that the proposed framework outperforms existing methods and achieves state-of-the-art performance under various few-shot settings.", "Keywords": "", "DOI": "10.1016/j.cviu.2025.104322", "PubYear": 2025, "Volume": "254", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "Chongyang Zhang", "Affiliation": "Shanghai Jiao Tong University, Shanghai, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Tokyo, Tokyo, Japan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "E-surfing Vision Technology Co., Ltd., Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "E-surfing Vision Technology Co., Ltd., Hangzhou, China"}], "References": [{"Title": "Ventral & Dorsal Stream Theory based Zero-Shot Action Recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "107953", "JournalTitle": "Pattern Recognition"}, {"Title": "Few-shot action recognition with implicit temporal alignment and pair similarity optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Qinyi Lv", "PubYear": 2021, "Volume": "210", "Issue": "", "Page": "103250", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Modeling long-term video semantic distribution for temporal action proposal generation", "Authors": "Tingting Han; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "490", "Issue": "", "Page": "217", "JournalTitle": "Neurocomputing"}, {"Title": "Superclass-aware network for few-shot learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "216", "Issue": "", "Page": "103349", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "HyRSM++: Hybrid relation guided temporal set matching for few-shot action recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "147", "Issue": "", "Page": "110110", "JournalTitle": "Pattern Recognition"}, {"Title": "Hierarchical compositional representations for few-shot action recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "240", "Issue": "", "Page": "103911", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Mask guided two-stream network for end-to-end few-shot action recognition", "Authors": "<PERSON><PERSON><PERSON>; Yanxiang Gong; Jiangfei Ji", "PubYear": 2024, "Volume": "583", "Issue": "", "Page": "127582", "JournalTitle": "Neurocomputing"}, {"Title": "VPE-WSVAD: Visual prompt exemplars for weakly-supervised video anomaly detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "299", "Issue": "", "Page": "111978", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Task-specific alignment and multiple-level transformer for few-shot action recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "598", "Issue": "", "Page": "128044", "JournalTitle": "Neurocomputing"}, {"Title": "Semantic-driven dual consistency learning for weakly supervised video anomaly detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2025, "Volume": "157", "Issue": "", "Page": "110898", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 144038303, "Title": "A dual-lock toehold-exchange-based aptamer switch for detecting tetracyclines in foods using non-G-quadruplex/hemin DNAzyme", "Abstract": "G-quadruplex (G4)/hemin DNAzymes, popularly used in aptasensors, have limitations regarding specificity and stability. To address these deficiencies, we investigated the potential of the non-G4/hemin DNAzyme as an alternative. Computational simulations revealed that hemin was embedded within the conserved sequence (5‘-GATTCC-3’) of the non-G4 aptamer (Hem1–2T) in a more stable binding configuration than its stacking interaction with the G4 structure. Subsequently, we developed a dual-aptamer switch with non-G4/hemin DNAzyme as the signal transduction element, utilizing the toehold-exchange strategy. Oxytetracycline (OTC), a member of the tetracyclines family, was selected as a representative target due to its inhibition with G4/hemin DNAzyme activity. In the conventional toehold-exchange-based aptamer (TEA) switch, OTC regulates the unpairing of the Hem1–2T fragment from the toehold linked to the 3′ end of the OTC aptamer, enabling colorimetric \"turn-on\" detection with a limit of detection (LOD) of 58.49 nM. We developed a novel double-locked TEA (DL-TEA) switch by extending the toehold. The toehold was paired with the OTC aptamer to form a hairpin structure. OTC and the Hem1–2T fragment acted as keys to unlock the DL-TEA switch cooperatively, facilitating colorimetric \"turn-off\" detection with a LOD of 3.52 nM. The DL-TEA switch decreased the LOD by 16.62 times. Furthermore, we obtained the LOD of 4.66 nM, 6.15 nM, and 5.87 nM for tetracycline, doxycycline, and chlortetracycline, respectively. The DL-TEA switch enabled label-free, rapid (15 min), and highly specific detection of tetracyclines in animal-derived foods, while a smartphone-assisted platform facilitated on-site testing.", "Keywords": "", "DOI": "10.1016/j.snb.2025.137528", "PubYear": 2025, "Volume": "433", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Food Science, Southwest University, Chongqing 400715, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Food Science, Southwest University, Chongqing 400715, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Food Science, Southwest University, Chongqing 400715, PR China"}, {"AuthorId": 4, "Name": "Wenjin Ma", "Affiliation": "Joint International Research Laboratory of Animal Health and Animal Food Safety, College of Veterinary Medicine, Southwest University, Chongqing 400715, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Animal Disease Prevention and Control Center, Chongqing 401120, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Food Science, Southwest University, Chongqing 400715, PR China;Joint International Research Laboratory of Animal Health and Animal Food Safety, College of Veterinary Medicine, Southwest University, Chongqing 400715, PR China;Yibin Academy of Southwest University, Southwest University, Yinbin, Sichuan 644000, PR China;Corresponding author at: College of Food Science, Southwest University, Chongqing 400715, PR China"}], "References": [{"Title": "Dual-catalytic colorimetric biosensor based on double-active Fe@Co-N stellate porous carbon and DNAzyme for simultaneous detection of tetracycline antibiotics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "376", "Issue": "", "Page": "133024", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Binding affinity estimation from restrained umbrella sampling simulations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "1", "Page": "59", "JournalTitle": "Nature Computational Science"}, {"Title": "Construction of a fluorescent and colorimetric multi-mode aptasensor for label-free and sensitive detection of ochratoxin A in agricultural products", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "410", "Issue": "", "Page": "135677", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 144038384, "Title": "Policy-based optimization for drag reduction via spanwise wall oscillations", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-025-11067-y", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Evolutionary algorithms and their applications to engineering problems", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12363", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A review On reinforcement learning: Introduction and applications in industrial process control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106886", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Xcompact3D: An open-source framework for solving turbulence problems on a Cartesian mesh", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "", "Page": "100550", "JournalTitle": "SoftwareX"}, {"Title": "A review on deep reinforcement learning for fluid mechanics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "225", "Issue": "", "Page": "104973", "JournalTitle": "Computers & Fluids"}, {"Title": "Policy-based optimization: single-step policy gradient method seen as an evolution strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "1", "Page": "449", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Gym-preCICE: Reinforcement learning environments for active flow control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "", "Page": "101446", "JournalTitle": "SoftwareX"}]}, {"ArticleId": 144038404, "Title": "Progressive semantic aggregation and structured cognitive enhancement for image–text matching", "Abstract": "Image–text matching tasks aim to bridge the heterogeneous gap between image and text, establishing a connective bridge between visual and linguistic modalities. The critical challenge lies in accurately and effectively learning semantic alignment between these two heterogeneous modalities. Visual-semantic embedding has proven highly effective in capturing cross-modal relationships, while the incorporation of unimodal structured knowledge has further enhanced the semantic understanding of these embeddings. However, when textual descriptions corresponding to images become overly verbose or lengthy, important structured semantics within the text may be obscured. This introduces additional noise, impacting the ability to effectively align image and text through co-occurring core semantic relationships, which can assist in connecting image and text in higher-level semantic spaces. Moreover, existing visual-semantic embedding pooling functions primarily focus on intra-modal local feature interactions, often overlooking the influence of global semantics on local features during the feature fusion process. To address these limitations, we propose a multi-modal structured knowledge-enhanced visual-semantic adaptive embedding method. Our approach: (1) constructs a structured relationship graph to explicitly represent core semantic relationships within text and injects them into textual embeddings, minimizing the semantic discrepancy across different modalities to improve cross-modal consistency; (2) designs a generalized pooling operator to aggregate embeddings, capturing macro-level semantics while selectively enhancing representative regions or words; (3) adopts a multi-granularity matching method to integrate global and local alignment, thereby improving image–text matching accuracy. Extensive experiments and analyses demonstrate the superior performance of our proposed Progressive semantic Aggregation and Structured cognitive Enhancement (PASE) method on the Flickr30K and MS-COCO datasets. The code is available at https://github.com/gyhhe/PASE .", "Keywords": "", "DOI": "10.1016/j.eswa.2025.126943", "PubYear": 2025, "Volume": "274", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>yong Li", "Affiliation": "College of Computer and Information Science, Chongqing Normal University, Chongqing 401331, China;<PERSON><PERSON><PERSON> and <PERSON><PERSON> contributed equally to this work"}, {"AuthorId": 2, "Name": "Yihua Gao", "Affiliation": "College of Computer and Information Science, Chongqing Normal University, Chongqing 401331, China;<PERSON><PERSON><PERSON> and <PERSON><PERSON> contributed equally to this work"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xi’an Jiaotong University, Xi’an 710049, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Digital Finance Innovation, Hubei University of Economics, Wuhan 430205, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Chongqing Normal University, Chongqing 401331, China;Corresponding authors"}], "References": [{"Title": "Complementarity is the king: Multi-modal and multi-grained hierarchical semantic enhancement network for cross-modal retrieval", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "216", "Issue": "", "Page": "119415", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Transformer-based local-global guidance for image captioning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "223", "Issue": "", "Page": "119774", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Adversarial pre-optimized graph representation learning with double-order sampling for cross-modal retrieval", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "120731", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dual-adaptive interactive transformer with textual and visual context for image captioning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "243", "Issue": "", "Page": "122955", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multiview adaptive attention pooling for image-text retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Qingxuan Lv", "PubYear": 2024, "Volume": "291", "Issue": "", "Page": "111550", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Heterogeneous Graph Fusion Network for cross-modal image-text retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "249", "Issue": "", "Page": "123842", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Language-vision matching for text-to-image synthesis with context-aware GAN", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "255", "Issue": "", "Page": "124615", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 144038452, "Title": "DCMA-Net: A dual channel multi-scale feature attention network for crack image segmentation", "Abstract": "Cracks are a common structural damage to pavement that significantly threatens traffic safety, so regular inspections of road conditions are essential for maintaining traffic safety. Artificial intelligence (AI) technology has demonstrated significant potential in various computer vision tasks and has been widely applied to the field of crack detection in recent years. However, the crack detection remains a challenging task due to the intricacy of crack types, the presence of intensity inhomogeneities, and the difficulty in detecting edge areas within a complex background. To address these challenges, this paper proposes a novel dual channel multi-scale feature attention segmentation network (DCMA-Net) model for crack detection. Specifically, this two feature extraction channels are exploited to obtain global and local information. A multi-scale feature extraction module (MFE) is integrated into the residual structure to capture depth multi-scale information. Additionally, a hybrid attention mechanism is employed to connect the coder and decoder, allowing the network to focus on the target area from different scales and dimensions to enrich crack feature representation. To verify the superiority of the proposed method, we evaluate it on three crack datasets and compare it with state-of-the-art crack detection techniques. The experimental results demonstrate the superior segmentation performance of our proposed network compared to existing advanced methods.", "Keywords": "", "DOI": "10.1016/j.engappai.2025.110411", "PubYear": 2025, "Volume": "148", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, 454003, China"}, {"AuthorId": 2, "Name": "Junding Sun", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, 454003, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, 454003, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, 454003, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, 454003, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biological Sciences, Xi'an Jiaotong-Liverpool University, Suzhou, Jiangsu, 215123, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, 454003, China;Department of Information Technology, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah, 21589, Saudi Arabia;Corresponding author. School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, 454003, China"}], "References": [{"Title": "Computer vision framework for crack detection of civil infrastructure—A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105478", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A hybrid deep learning pavement crack semantic segmentation", "Authors": "<PERSON><PERSON>; <PERSON>; Riyadh Nazar <PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106142", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "DeepCrackAT: An effective crack segmentation framework based on learning multi-scale crack features", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "106876", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Deep learning algorithm for real-time automatic crack detection, segmentation, qualification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107085", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Ventilation diagnosis of minigrinders using thermal images", "Authors": "<PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121435", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 144038469, "Title": "State-of-Charge Estimation of an Experimentally Identified Lithium-ion Cell Model using Advanced Nonlinear Filters", "Abstract": "<p>This paper considers the state-of-charge (SoC) estimation problem for Lithium-ion (Li-ion) cells. An accurate SoC estimation is crucial in many aspects. Firstly, it prolongs battery lifespan by preventing overcharging and overdischarging. Additionally, preventing overcharging, and hence thermal runaway, averts any potential risk of fire or explosion in the battery systems. Understanding the SoC enables the efficient utilization of a battery’s capacity, enhancing the performance of the device or vehicle it powers. This is especially crucial for electric vehicles, where concerns about driving range are prevalent. SoC estimation is frequently paired with state-of-health (SoH) estimation to assess the battery’s overall condition. This combination aids in forecasting the battery’s remaining lifespan and scheduling maintenance or replacement. In grid storage and renewable energy systems, precise SoC estimation aids in balancing energy supply and demand, ensuring dependable and efficient energy management. The above-mentioned discussion highlights the importance of the study which is carried out in this work. The existing works in the literature mainly use the extended Kalman filter (EKF) for the SoC estimation problem. It should be noted that the performance of the EKF degrades as the system’s non-linearity increases. Moreover, the existing battery management systems are complex and inherently involve high nonlinearities which further extend as these systems are expanded. For these reasons, the EKF may lose its applicability for applications demanding highly accurate SoC estimates. In this paper, therefore, we apply a more accurate cubature-quadrature Kalman filter (CQKF) to estimate the SoC of the Li-ion cell. The SoC estimates provided by the CQKF are more accurate than those provided by the EKF. In this regard, we first develop the completely observable equivalent circuit model (ECM) of a Li-ion cell by experimentally identifying the parameters of the cell model. The experimental study is carried out in a commercially available 2.5 Ah lithium iron phosphate (LFP) cell (A123 ANR26650M1-B). Subsequently, we apply the cubature quadrature Kalman filter (CQKF) for estimating the SoC of the considered Li-ion cell. We also perform a comparative analysis of the CQKF and the extended Kalman filter (EKF) based SoC estimation for the considered model. We further extended the analysis for the missing measurement case, where the actual measurement is intermittently lost or does not contain sufficient information for the state measurement. The efficacy of the estimation schemes is validated both experimentally and in simulation by computing several performance indexes. The simulation results show that, compared with the EKF, the implementation of the CQKF improves the SoC estimation accuracy significantly.</p>", "Keywords": "", "DOI": "10.15837/ijccc.2025.2.6896", "PubYear": 2025, "Volume": "20", "Issue": "2", "JournalId": 8306, "JournalTitle": "International Journal of Computers Communications & Control", "ISSN": "1841-9836", "EISSN": "1841-9844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144038526, "Title": "An Unconstrained Primal Based Twin Parametric Insensitive Support Vector Regression", "Abstract": "<p>In this paper, we propose an efficient regression algorithm based on primal formulation of twin support vector machine. This is an efficient approach to solve the optimization problem leading to reduced computation time. The proposed method is termed as twin parametric insensitive support vector regression (UPTPISVR). The optimization problems of the proposed (UPTPISVR) are a pair of unconstrained convex minimization problems. Moreover, the objective functions of UPTPISVR are strongly convex, differentiable and piecewise quadratic. Therefore, an approximate solution is obtained in primal variables instead of solving the dual formulation. Further, an absolute value equation problem is solved by using a functional iterative algorithm for UPTPISVR, termed as FUPTPISVR. The objective function of the proposed formulation involves the plus function which is non-smooth and therefore, smooth approximation functions are used to replace the plus function, termed as SUPTPISVR. The Newton-<PERSON>ijo algorithm is then used to iteratively obtain the solutions, thus eliminates the requirement of any optimization toolbox. Various numerical experiments on synthetic and benchmark real-world datasets are presented for justifying the applicability and effectiveness of the proposed UPTPISVR. The results clearly indicate that the proposed algorithms outperform the existing algorithms in terms of root mean square error (RMSE) on most datasets.</p>", "Keywords": "", "DOI": "10.1142/S0218488525500072", "PubYear": 2025, "Volume": "33", "Issue": "2", "JournalId": 15072, "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems", "ISSN": "0218-4885", "EISSN": "1793-6411", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Motilal Nehru National Institute of Technology Allahabad, Uttar Pradesh-211004, INDIA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Systems, Birla Institute of Technology and Science (BITS) Pilani (Pilani Campus)-333031, INDIA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Information Technology, Guwahati-781015, INDIA"}], "References": [{"Title": "Wavelet transform-based weighted $$\\nu$$-twin support vector regression", "Authors": "<PERSON><PERSON> Wang; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "95", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Unconstrained convex minimization based implicit Lagrangian twin extreme learning machine for classification (ULTELMC)", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "4", "Page": "1327", "JournalTitle": "Applied Intelligence"}, {"Title": "Modelling and forecasting of COVID-19 spread using wavelet-coupled random vector functional link networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106626", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 144038527, "Title": "Interval Methods in Knowledge Representation", "Abstract": "", "Keywords": "", "DOI": "10.1142/S0218488525970037", "PubYear": 2025, "Volume": "33", "Issue": "2", "JournalId": 15072, "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems", "ISSN": "0218-4885", "EISSN": "1793-6411", "Authors": [], "References": []}, {"ArticleId": 144038559, "Title": "A reliable and secure video steganography method based on 4d-logistic mapping", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-025-20686-5", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Motion vector based video steganography using homogeneous block selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "9-10", "Page": "5881", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Robust video encryption for H.264 compressed bitstream based on cross-coupled chaotic cipher", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "4", "Page": "363", "JournalTitle": "Multimedia Systems"}, {"Title": "The large capacity embedding algorithm for H.264/AVC intra-prediction mode video steganography based on linear block code over Z4", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "Page": "12659", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Video encryption based on hyperchaotic system", "Authors": "Xiaodong Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "33-34", "Page": "23995", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "PSNR vs SSIM: imperceptibility quality assessment for image steganography", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "6", "Page": "8423", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A hybrid DST-SBPNRM approach for compressed video steganography", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "3", "Page": "417", "JournalTitle": "Multimedia Systems"}, {"Title": "A secure and robust video steganography scheme for covert communication in H.264/AVC", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "9", "Page": "14383", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Cybersecurity framework of hybrid watermarking and selective encryption for secure HEVC communication", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "2", "Page": "1215", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Robust video steganography for social media sharing based on principal component analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "1", "Page": "1", "JournalTitle": "EURASIP Journal on Information Security"}, {"Title": "Framework for Video Steganography Using Integer Wavelet Transform and JPEG Compression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "9", "Page": "254", "JournalTitle": "Future Internet"}, {"Title": "Optimizing Region of Interest Selection for Effective Embedding in Video Steganography Based on Genetic Algorithms", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "2", "Page": "1451", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "A new approach to video steganography models with 3D deep CNN autoencoders", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "17", "Page": "51423", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Real-time motion estimation based video steganography with preserved consistency and local optimality", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2025, "Volume": "84", "Issue": "8", "Page": "5001", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 144038582, "Title": "DeepLeaf: an optimized deep learning approach for automated recognition of grapevine leaf diseases", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-025-11038-3", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Identification of grape diseases using image analysis and BP neural networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "14539", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Olive Spot Disease Detection and Classification using Analysis of Leaf Image Textures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2328", "JournalTitle": "Procedia Computer Science"}, {"Title": "Classification and Grading of Okra-ladies finger using Deep Learning", "Authors": "Me<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "2380", "JournalTitle": "Procedia Computer Science"}, {"Title": "A novel PCA–whale optimization-based deep neural network model for classification of tomato plant diseases using GPU", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "4", "Page": "1383", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "Deep learning for grape variety recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "1211", "JournalTitle": "Procedia Computer Science"}, {"Title": "Detecting cassava mosaic disease using a deep residual convolutional neural network with distinct block processing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Cassava disease recognition from low‐quality images using enhanced data augmentation model and deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "7", "Page": "e12746", "JournalTitle": "Expert Systems"}, {"Title": "RL based hyper-parameters optimization algorithm (ROA) for convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "10", "Page": "13349", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Downsampling for Binary Classification with a Highly Imbalanced Dataset Using Active Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "", "Page": "100314", "JournalTitle": "Big Data Research"}, {"Title": "A New Reliable System For Managing Virtual Cloud Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "3", "Page": "5863", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A lightweight convolutional neural network for disease detection of fruit leaves", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "20", "Page": "14855", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Crop yield prediction algorithm (CYPA) in precision agriculture based on IoT techniques and climate changes", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "23", "Page": "17281", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Medical images classification using deep learning: a survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "7", "Page": "19683", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Enhancing the Early Detection of Chronic Kidney Disease: A Robust Machine Learning Model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "3", "Page": "144", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "A novel framework for semi-automated system for grape leaf disease detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "17", "Page": "50733", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A deep neural network with modified random forest incremental interpretation approach for diagnosing diabetes in smart healthcare", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "152", "Issue": "", "Page": "111183", "JournalTitle": "Applied Soft Computing"}, {"Title": "Enhancing crop recommendation systems with explainable artificial intelligence: a study on agricultural decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "11", "Page": "5695", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Next generation of computer vision for plant disease monitoring in precision agriculture: A contemporary survey, taxonomy, experiments, and future direction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "665", "Issue": "", "Page": "120338", "JournalTitle": "Information Sciences"}, {"Title": "Designing of Lightweight Deep Learning Framework for Plant Disease Detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "6", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": *********, "Title": "Effects of user movement and target placement on target selection during walking", "Abstract": "", "Keywords": "", "DOI": "10.1080/0144929X.2025.2471900", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Fuzhou University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fuzhou University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Chen", "Affiliation": "Fuzhou University"}], "References": [{"Title": "The effects of target location on musculoskeletal load, task performance, and subjective discomfort during virtual reality interactions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "84", "Issue": "", "Page": "103010", "JournalTitle": "Applied Ergonomics"}, {"Title": "User capabilities in eyes-free spatial target acquisition in immersive virtual reality environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "94", "Issue": "", "Page": "103400", "JournalTitle": "Applied Ergonomics"}, {"Title": "Walking-in-place for omnidirectional VR locomotion using a single RGB camera", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "173", "JournalTitle": "Virtual Reality"}, {"Title": "Understanding user performance of acquiring targets with motion-in-depth in virtual reality", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "102817", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Immersive virtual reality in the age of the Metaverse: A hybrid-narrative review based on the technology affordance perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "2", "Page": "101717", "JournalTitle": "The Journal of Strategic Information Systems"}, {"Title": "Effects of physical walking on eyes-engaged target selection with ray-casting pointing in virtual reality", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "2", "Page": "603", "JournalTitle": "Virtual Reality"}, {"Title": "Effects of Reading Text on an Optical See-Through Head-Mounted Display During Treadmill Walking in a Virtual Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "9", "Page": "2225", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Effect of distance cue and walking speed on matching and discrimination of visually simulated walking speed in treadmill-based locomotor VR", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "80", "Issue": "", "Page": "102511", "JournalTitle": "Displays"}]}, {"ArticleId": 144038685, "Title": "Token-aware and step-aware acceleration for Stable Diffusion", "Abstract": "Stable Diffusion has shown strong ability to generate high-quality and diverse images. However, Stable Diffusion suffers from high computational cost, due to the heavy model and step-by-step denoising process. To address these issues, we propose a token-aware and step-aware acceleration approach for Stable Diffusion, named TSA-SD. We first build a simple and efficient baseline by combining exiting intra-step and cross-step acceleration strategies, including token merging and feature caching, into Stable Diffusion. To improve image generation quality of the baseline, we introduce token-aware merging–unmerging and step-aware acceleration. The token-aware merging–unmerging aims to select informative tokens when merging and recover merged tokens using token ratio information. Therefore, the token-aware merging–unmerging can fully utilize token-specific information, thereby reducing token information loss. In addition, we observe that different steps have different functional linearity, and propose step-aware acceleration to perform different merging operations according to functional linearity at different steps. With these two modules, our proposed TSA-SD is able to generate high-quality images at a high speed. We perform the experiments on two widely-used datasets, including ImageNet and MS-COCO. The experimental results demonstrate the effectiveness and efficiency of our proposed method. For instance, on ImageNet validation set, compared to Stable Diffusion, ToMe-SD has a lower FID of 33.68 at 1.96 × speedup, while our method achieves a lower FID of 32.49 at 4.68 × speedup.", "Keywords": "", "DOI": "10.1016/j.patcog.2025.111479", "PubYear": 2025, "Volume": "164", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin 300072, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin 300072, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin 300072, China"}, {"AuthorId": 4, "Name": "Jing Pan", "Affiliation": "School of Electronic Engineering, Tianjin University of Technology and Education, Tianjin 300222, China"}, {"AuthorId": 5, "Name": "<PERSON>hong <PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin 300072, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin 300072, China;Shanghai Artificial Intelligence Laboratory, Shanghai 200232, China"}], "References": [{"Title": "SRDiff: Single image super-resolution with diffusion probabilistic models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "479", "Issue": "", "Page": "47", "JournalTitle": "Neurocomputing"}, {"Title": "Depth-aware guidance with self-estimated depth representations of diffusion models", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "153", "Issue": "", "Page": "110474", "JournalTitle": "Pattern Recognition"}, {"Title": "AdaptBIR: Adaptive Blind Image Restoration with latent diffusion prior for higher fidelity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "155", "Issue": "", "Page": "110659", "JournalTitle": "Pattern Recognition"}, {"Title": "Denoising diffusion post-processing for low-light image enhancement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "156", "Issue": "", "Page": "110799", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 144038770, "Title": "AI 医療機器の「unwanted bias」に対するガバナンスの国際比較研究", "Abstract": "", "Keywords": "", "DOI": "10.1527/tjsai.40-2_D-O96", "PubYear": 2025, "Volume": "40", "Issue": "2", "JournalId": 8299, "JournalTitle": "Transactions of the Japanese Society for Artificial Intelligence", "ISSN": "1346-0714", "EISSN": "1346-8030", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Frontier Sciences, The University of Tokyo"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Frontier Sciences, The University of Tokyo"}], "References": [{"Title": "Bias in data‐driven artificial intelligence systems—An introductory survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "3", "Page": "", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}]}, {"ArticleId": 144038796, "Title": "Bayesian optimization of hybrid quantum LSTM in a mixed model for precipitation forecasting", "Abstract": "Precipitation forecasting has important applications in meteorological research. Accurate forecasting is of great significance for reducing the impact of floods, optimizing crop planting plans, rationally allocating water resources, and ensuring traffic safety. However, the factors affecting precipitation are complex and nonlinear, and have spatiotemporal variability, making rainfall forecasting extremely challenging. In response to these challenges, this paper proposes a hybrid model based on temporal convolutional network, quantum long short-term memory network (QLSTM), and random forest regression (RFR) to achieve more accurate rainfall forecasting. The hyperparameters of the model are optimized using the Bayesian optimization algorithm to obtain the best performance. Experiments are conducted on meteorological datasets from Seattle and Ukraine, and the results are verified using mean absolute error (MAE), root mean square error (RMSE), and bias evaluation indicators. The results show that the proposed hybrid model outperforms traditional models such as RFR, support vector machine, K-nearest neighbor, LSTM, and QLSTM in terms of MAE, RMSE, and bias. The proposed model achieves improvements of 1.89 \n \n \n \n % \n \n \n MAE, 2.65 \n \n \n \n % \n \n \n RMSE, and 31 \n \n \n \n % \n \n \n Bias, respectively. These results highlight the improved forecast accuracy and robustness of the proposed hybrid model. This research provides a new approach to weather forecasting and demonstrates the potential of combining quantum computing with traditional machine learning techniques.", "Keywords": "", "DOI": "10.1088/2632-2153/adbbad", "PubYear": 2025, "Volume": "6", "Issue": "1", "JournalId": 72803, "JournalTitle": "Machine Learning: Science and Technology", "ISSN": "", "EISSN": "2632-2153", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "DSTP-RNN: A dual-stage two-phase attention-based recurrent neural network for long-term and multivariate time series prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "113082", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Parallel spatio-temporal attention-based TCN for multivariate time series prediction", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "18", "Page": "13109", "JournalTitle": "Neural Computing and Applications"}, {"Title": "RSDF-AM-LSTM: Regional Scale Division Rainfall Forecasting Using Attention and LSTM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "4", "Page": "1", "JournalTitle": "ACM/IMS Transactions on Data Science"}]}, {"ArticleId": 144038930, "Title": "Brain tumor image segmentation based on shuffle transformer-dynamic convolution and inception dilated convolution", "Abstract": "Accurate segmentation of brain tumors is essential for accurate clinical diagnosis and effective treatment. Convolutional neural networks (CNNs) have improved brain tumor segmentation with their excellent performance in local feature modeling. However, they still face the challenge of unpredictable changes in tumor size and location, because it cannot be effectively matched by CNN-based methods with local and regular receptive fields. To overcome these obstacles, we propose brain tumor image segmentation based on shuffle transformer-dynamic convolution and inception dilated convolution that captures and adapts different features of tumors through multi-scale feature extraction. Our model combines Shuffle Transformer-Dynamic Convolution (STDC) to capture both fine-grained and contextual image details so that it helps improve localization accuracy. In addition, the Inception Dilated Convolution(IDConv) module solves the problem of significant changes in the size of brain tumors, and then captures the information of different size of object. The multi-scale feature aggregation(MSFA) module integrates features from different encoder levels, which contributes to enriching the scale diversity of input patches and enhancing the robustness of segmentation. The experimental results conducted on the BraTS 2019, BraTS 2020, BraTS 2021, and MSD BTS datasets indicate that our model outperforms other state-of-the-art methods in terms of accuracy.", "Keywords": "", "DOI": "10.1016/j.cviu.2025.104324", "PubYear": 2025, "Volume": "254", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Image Cognition, Chongqing University of Posts and Telecommunications, Chongqing, 400065, Chongqing, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, 400065, Chongqing, China"}], "References": [{"Title": "Brain tumor segmentation based on the fusion of deep semantics and edge information in multimodal MRI", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "376", "JournalTitle": "Information Fusion"}, {"Title": "SCAU-net: 3D self-calibrated attention U-Net for brain tumor segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "33", "Page": "23973", "JournalTitle": "Neural Computing and Applications"}, {"Title": "DAUnet: A U-shaped network combining deep supervision and attention for brain tumor segmentation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "285", "Issue": "", "Page": "111348", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Yaru3DFPN: a lightweight modified 3D UNet with feature pyramid network and combine thresholding for brain tumor segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "13", "Page": "7529", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Brain tumor segmentation in MRI with multi-modality spatial information enhancement and boundary shape correction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "153", "Issue": "", "Page": "110553", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 144038935, "Title": "Trustworthy TAVR navigator system, I: A generative adversarial network-driven medical twin approach for Post-TAVR pacemaker implantation prediction", "Abstract": "This paper explores the advanced integration of predictive models in transcatheter aortic valve replacement (TAVR) to improve post-procedural outcomes and address the risk of conduction abnormalities (CA), which frequently require permanent pacemaker implantation (PPI). We investigate critical considerations, from enhancing model reliability and interactivity for physicians to ensuring data confidentiality, advancing the development of intelligent healthcare solutions, and bridging the gap between technological innovation and practical medical application. To overcome data scarcity, we use a penalized gradient policy over the conditional generative adversarial networks (CGAN) as a solution for data augmentation. Furthermore, we present a unique tool to doctors for simulating and assessing probable surgical consequences: a digital twin framework for visualizing the requirement of pacemaker implantation post-TAVR. We aim to simplify complex predictions for healthcare practitioners by combining Explainable AI (XAI) with medical twins. Additionally, we have set up a roadmap for the TAVR navigator system that adheres to stringent ethical and legal criteria while respecting reliable AI features like validity, confidentiality, responsiveness, and privacy. It will guarantee that the development and implementation of our system focuses on ethics and transparency.", "Keywords": "", "DOI": "10.1016/j.eswa.2025.126973", "PubYear": 2025, "Volume": "275", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Gachon University, 1342 Seongnam-daero, Sujeong-gu, Seongnam-si, 13120, Gyeonggi-do, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Gachon University, 1342 Seongnam-daero, Sujeong-gu, Seongnam-si 13120, Gyeonggi-do, Republic of Korea"}, {"AuthorId": 3, "Name": "InSeo Song", "Affiliation": "Department of Computer Engineering, Gachon University, 1342 Seongnam-daero, Sujeong-gu, Seongnam-si 13120, Gyeonggi-do, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Gachon University, 1342 Seongnam-daero, Sujeong-gu, Seongnam-si 13120, Gyeonggi-do, Republic of Korea;Correspondence to: Department of Computer Engineering, Gachon University, Seongnam-si 13120, Republic of Korea.; Corresponding author"}], "References": [{"Title": "Synthetic data generation for tabular health records: A systematic review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "493", "Issue": "", "Page": "28", "JournalTitle": "Neurocomputing"}, {"Title": "High-order conditional mutual information maximization for dealing with high-order dependencies in feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108895", "JournalTitle": "Pattern Recognition"}, {"Title": "Enhancing Small Medical Dataset Classification Performance Using GAN", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "1", "Page": "28", "JournalTitle": "Informatics"}, {"Title": "A new method for GAN-based data augmentation for classes with distinct clusters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "235", "Issue": "", "Page": "121199", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 144039210, "Title": "Auxiliary action unit model for facial expression adversarial training", "Abstract": "The adversarial training of neural networks against adversarial attacks is increasingly gaining attention due to the demands for artificial intelligence security. However, there have been few studies on adversarial training for facial expression recognition (FER) models. In this work, we propose a novel adversarial training method for FER models. Specifically, we employ an action unit (AU) model to enhance the adversarial robustness of the FER model during the training process. Experimental results demonstrate that our method (i) exhibits greater generalization and robustness than other existing methods for FER models; (ii) incurs feasible computational training costs; and (iii) can converge under extreme circumstances, such as random labels. Our research makes sense as it paves the way for future studies in adversarial training for FER models.", "Keywords": "", "DOI": "10.1016/j.patcog.2025.111493", "PubYear": 2025, "Volume": "164", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "Yudao Sun", "Affiliation": "Department of New Networks, Peng Cheng Laboratory, Shenzhen, PR China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of New Networks, Peng Cheng Laboratory, Shenzhen, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyberspace Security, Beijing University of Posts and Telecommunications, Beijing, PR China"}], "References": [{"Title": "FER-net: facial expression recognition using deep neural net", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "15", "Page": "9125", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Understanding deep learning (still) requires rethinking generalization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "3", "Page": "107", "JournalTitle": "Communications of the ACM"}, {"Title": "Adv-Emotion: The Facial Expression Adversarial Attack", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> Zheng", "PubYear": 2021, "Volume": "35", "Issue": "11", "Page": "2152016", "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence"}, {"Title": "Generating facial expression adversarial examples based on saliency map", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "104318", "JournalTitle": "Image and Vision Computing"}, {"Title": "Unauthorized AI cannot recognize me: Reversible adversarial example", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109048", "JournalTitle": "Pattern Recognition"}, {"Title": "Cross-city matters: A multimodal remote sensing benchmark dataset for cross-city semantic segmentation using high-resolution domain adaptation networks", "Authors": "<PERSON><PERSON> Hong; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "299", "Issue": "", "Page": "113856", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Towards robust neural networks via orthogonal diversity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "149", "Issue": "", "Page": "110281", "JournalTitle": "Pattern Recognition"}, {"Title": "Explainable AI in human motion: A comprehensive approach to analysis, modeling, and generation", "Authors": "<PERSON>-<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "151", "Issue": "", "Page": "110418", "JournalTitle": "Pattern Recognition"}, {"Title": "Towards the adversarial robustness of facial expression recognition: Facial attention-aware adversarial training", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "584", "Issue": "", "Page": "127588", "JournalTitle": "Neurocomputing"}, {"Title": "Regional Adversarial Training for Better Robust Generalization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "132", "Issue": "10", "Page": "4510", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 144039216, "Title": "Energy Efficient Smart City", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2025.14209", "PubYear": 2025, "Volume": "14", "Issue": "2", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> khan", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> bute", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144039331, "Title": "Effective Numerical Simulation of Fault Transient System", "Abstract": "Power systems, including synchronous generator systems, are typical systems that strive for stable operation. In this paper, we numerically study the fault transient process of a synchronous generator system based on the first benchmark model. That is, we make it clear whether an originally stable generator system can restore its stability after a short time of unstable transient process. To achieve this, we construct a structure-preserving method and compare it with the existing and frequently-used predictor–corrector method. We newly establish a reductive form of the circuit system and accelerate the reduction process. Also a switching method between two stages in the fault transient process is given. Numerical results show the effectiveness and reliability of our method.", "Keywords": "", "DOI": "10.1142/S1793962325500291", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 12484, "JournalTitle": "International Journal of Modeling, Simulation, and Scientific Computing", "ISSN": "1793-9623", "EISSN": "1793-9615", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Power Transmission Technology, Beijing 102209, P. R. <PERSON>;LSEC, ICMSEC, Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing 100190, P. R. China;School of Mathematical Sciences, University of Chinese Academy of Sciences, Beijing 100049, P. R. China"}, {"AuthorId": 2, "Name": "Feng Ji", "Affiliation": "State Key Laboratory of Advanced Power Transmission Technology, Beijing 102209, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Advanced Power Transmission Technology, Beijing 102209, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Beijing Jiaotong University, Beijing 100044, P. R. China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Urban Power Supply Branch, State Grid Beijing Electric Power Company, Bejing 100031, P. R. China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "LSEC, ICMSEC, Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing 100190, P. R. China;School of Mathematical Sciences, University of Chinese Academy of Sciences, Beijing 100049, P. R. China"}], "References": []}, {"ArticleId": *********, "Title": "On protecting the data privacy of Large Language Models (LLMs) and LLM agents: A literature review", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.hcc.2025.100300", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 85789, "JournalTitle": "High-Confidence Computing", "ISSN": "2667-2952", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>wei Yan", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Creating the First Confidential GPUs", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "21", "Issue": "4", "Page": "68", "JournalTitle": "Queue"}, {"Title": "A survey on Large Language Model (LLM) security and privacy: The Good, The Bad, and The Ugly", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "4", "Issue": "2", "Page": "100211", "JournalTitle": "High-Confidence Computing"}, {"Title": "A survey on large language model based autonomous agents", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "18", "Issue": "6", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Prompting Large Language Models with Knowledge-Injection for Knowledge-Based Visual Question Answering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "7", "Issue": "3", "Page": "843", "JournalTitle": "Big Data Mining and Analytics"}, {"Title": "Privacy issues in Large Language Models: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "120", "Issue": "", "Page": "109698", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Large Language Models in Psychiatry: Current Applications, Limitations, and Future Scope", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "7", "Issue": "4", "Page": "1148", "JournalTitle": "Big Data Mining and Analytics"}]}, {"ArticleId": 144039378, "Title": "Image deblurring algorithm based on unsupervised network and alternating optimization iterations", "Abstract": "<p>Image deblurring aims at recovering clear images from degraded blurred images. From the existing research results, most of learning-based image deblurring networks have a common problem: long training time, large amount of training data, etc. Traditional image deblurring methods can still achieve good deblurring effect without the support of large amount of data, but the computational complexity is relatively high. Aiming at the above problems, this paper proposes a new image deblurring method based on unsupervised network with optimization alternating iterations. Firstly, constructing an unsupervised network based on the encoder-decoder framework, the goal is generating a potentially clear image by inputting random noise. Then, a closed-form solution of the blurring kernel is constructed by combining the cost function of the optimization model. Finally, the loss function is used to optimize the image and the blurring kernel in alternating iterations to generate the final deblurring result. Compared with the state-of-the-art deblurring methods, both objective metrics and visualization experimental results show that the proposed method achieves superior results. The proposed method achieves an average PSNR improvement of 0.32 dB compared to the second-best method. Additionally, the average HNMSE of the blur kernel is reduced by 0.018 compared to the second-best method. Further, the ablation experiments show that the proposed method can effectively combine the advantages of traditional methods and deep learning.</p>", "Keywords": "Image deblurring; Unsupervised networks; Optimization; Alternating iteration", "DOI": "10.1007/s00530-025-01698-5", "PubYear": 2025, "Volume": "31", "Issue": "2", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Cyber Security, Fujian Normal University, Fuzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Huang", "Affiliation": "College of Computer and Cyber Security, Fujian Normal University, Fuzhou, China; Corresponding author."}], "References": []}, {"ArticleId": 144039427, "Title": "Proactive Data Categorization for Privacy in DevPrivOps", "Abstract": "<p>Assessing privacy within data-driven software is challenging due to its subjective nature and the diverse array of privacy-enhancing technologies. A simplistic personal/non-personal data classification fails to capture the nuances of data specifications and potential privacy vulnerabilities. Robust, privacy-focused data categorization is vital for a deeper understanding of data characteristics and the evaluation of potential privacy risks. We introduce a framework for Privacy-sensitive Data Categorization (PsDC), which accounts for data inference from multiple sources and behavioral analysis. Our approach uses a hierarchical, multi-tiered tree structure, encompassing direct data categorization, dynamic tags, and structural attributes. PsDC is a data-categorization model designed for integration with the DevPrivOps methodology and for use in privacy-quantification models. Our analysis demonstrates its applicability in network-management infrastructure, service and application deployment, and user-centered design interfaces. We illustrate how PsDC can be implemented in these scenarios to mitigate privacy risks. We also highlight the importance of proactively reducing privacy risks by ensuring that developers and users understand the privacy “value” of data.</p>", "Keywords": "", "DOI": "10.3390/info16030185", "PubYear": 2025, "Volume": "16", "Issue": "3", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Electrónica, Telecomunicações e Informática, Universidade de Aveiro, 3810-193 Aveiro, Portugal; Instituto de Telecomunicações, 3810-164 Aveiro, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Electrónica, Telecomunicações e Informática, Universidade de Aveiro, 3810-193 Aveiro, Portugal; Instituto de Telecomunicações, 3810-164 Aveiro, Portugal"}, {"AuthorId": 3, "Name": "Paulo Salvador", "Affiliation": "Departamento de Electrónica, Telecomunicações e Informática, Universidade de Aveiro, 3810-193 Aveiro, Portugal; Institute of Electronics and Informatics Engineering of Aveiro, Universidade de Aveiro, 3810-193 Aveiro, Portugal"}], "References": [{"Title": "An Ontology for Privacy Requirements via a Systematic Literature Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "4", "Page": "123", "JournalTitle": "Journal on Data Semantics"}, {"Title": "k -Anonymity in practice: How generalisation and suppression affect machine learning classifiers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "102488", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 144039654, "Title": "A review of fruit ripeness recognition methods based on deep learning", "Abstract": "", "Keywords": "", "DOI": "10.1080/23335777.2025.2467639", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 2756, "JournalTitle": "Cyber-Physical Systems", "ISSN": "2333-5777", "EISSN": "2333-5785", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Shanghai for Science & Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Shanghai for Science & Technology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Yunnan Agricultural University"}], "References": [{"Title": "A Survey on Object Detection for the Internet of Multimedia Things (IoMT) using Deep Learning and Event-based Middleware: Approaches, Challenges, and Future Directions", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104095", "JournalTitle": "Image and Vision Computing"}, {"Title": "Banana ripeness stage identification: a deep learning approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "8", "Page": "4033", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A review of deep learning used in the hyperspectral image analysis for agriculture", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "7", "Page": "5205", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Pre-trained deep learning-based classification of jujube fruits according to their maturity level", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "16", "Page": "13925", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Comparison of Deep Learning-Based Object Classification Methods for Detecting Tomato Ripeness", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "3", "Page": "223", "JournalTitle": "International Journal of Fuzzy Logic and Intelligent Systems"}]}, {"ArticleId": 144039665, "Title": "FROM DATA TO DECISION: SIMPLIFYING FINTECH ML MODELS WITH AWS SAGEMAKER", "Abstract": "", "Keywords": "", "DOI": "10.34218/IJITMIS_16_01_040", "PubYear": 2025, "Volume": "16", "Issue": "1", "JournalId": 66348, "JournalTitle": "INTERNATIONAL JOURNAL OF INFORMATION TECH<PERSON><PERSON>OGY AND MANAGEMENT INFORMATION SYSTEMS", "ISSN": "0976-6405", "EISSN": "0976-6413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144039733, "Title": "On the Trustworthiness Landscape of State-of-the-art Generative Models: A Survey and Outlook", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11263-025-02375-w", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Fan", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "ReDMark: Framework for residual diffusion watermarking based on deep networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "113157", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A review of applications in federated learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106854", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Falcon: Honest-Majority Maliciously Secure Framework for Private Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "1", "Page": "188", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "Wavelet-packets for deepfake image analysis and detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "111", "Issue": "11", "Page": "4295", "JournalTitle": "Machine Learning"}, {"Title": "Pre-train, Prompt, and Predict: A Systematic Survey of Prompting Methods in Natural Language Processing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Defending ChatGPT against jailbreak attack via self-reminders", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "12", "Page": "1486", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Auditing and instructing text-to-image generation models on fairness", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "AI and Ethics"}]}, {"ArticleId": *********, "Title": "Exchange Rates, Supply Chain Activity/Disruption Effects, and Exports", "Abstract": "<p>In the past, South African monetary policy aimed to protect the external value of the domestic currency (Rand); however, these efforts failed. Later, its monetary policy approach changed to allow the foreign exchange rate market to determine the exchange rates. In such a change, the South African Reserve Bank (SARB) aimed to stabilize the demand for the Rand in the foreign exchange market by providing information to stabilize market expectations and create favorable market conditions. However, South African policymakers have struggled with currency depreciation since the early 60s, increasing the uncertainty of South African exports. This study aims to examine the effect of currency depreciation on exports using the Threshold Autoregressive (TAR) model. Additionally, this study created and validated the supply chain activity/disruption index to capture the sea trade activity. The sample period for the analysis is 2009 to 2023. The study finds that currency depreciation does not improve trade between South Africa and its trading partners over time. Furthermore, the currency depreciation was found to be asymmetric to the effect of international trade across the different regimes. The supply chain activity index shows that the effect of supply chain activity/disruption on exports is regime-dependent. This implies that the effect on exports is dependent on the economic environment.</p>", "Keywords": "", "DOI": "10.3390/forecast7010010", "PubYear": 2025, "Volume": "7", "Issue": "1", "JournalId": 54589, "JournalTitle": "Forecasting", "ISSN": "", "EISSN": "2571-9394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Accounting, Economics & Finance, Collage of Law & Management Studies, University of KwaZulu-Natal, Durban 3629, South Africa"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Accounting, Economics & Finance, Collage of Law & Management Studies, University of KwaZulu-Natal, Durban 3629, South Africa"}], "References": []}, {"ArticleId": *********, "Title": "Design of a Provable Secure Lightweight Privacy-preserving Authentication Protocol for autonomous vehicles in IoT systems", "Abstract": "The rapid advancement of the Internet of Things (IoT) has enabled the adoption of autonomous vehicles (AVs) and drones in intelligent transportation systems (ITS), improving traffic efficiency and safety. However, security and privacy in interconnected ITS environments is a major concern. It is imperative to safeguard sensitive information from various known attacks while enabling secure communication. Keeping in view the security and privacy of autonomous vehicles in IoT systems, this paper puts forward Provable Secure Lightweight Privacy-Preserving Authentication Protocol (PSLAP). The proposed PSLAP protocol achieves high-level security by utilizing cryptographic primitives such as exclusive-OR, secure one-way hash, elliptic curve cryptography (ECC), and concatenation operators. The proposed PSLAP protocol is demonstrated to be resistant to numerous known security assaults through an informal security and privacy assessment. This study presents a formal security analysis using a widely accepted real-or-random (ROR) model which demonstrates the security hardness of the proposed scheme. Additionally, the performance analysis shows that the proposed protocol has minimal computation and communication costs compared to other existing protocols.", "Keywords": "", "DOI": "10.1016/j.comnet.2025.111155", "PubYear": 2025, "Volume": "261", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems and Security, College of IT, UAE University, Al Ain, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Sciences and Information Technology, University of Anbar, Anbar, Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computers and Information Technology, Taif University, P.O. Box 11099, Taif 21944, Saudi Arabia"}, {"AuthorId": 4, "Name": "Aymen Dia <PERSON>", "Affiliation": "Department of Information Systems and Security, College of IT, UAE University, Al Ain, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems and Security, College of IT, UAE University, Al Ain, Abu Dhabi, United Arab Emirates;Corresponding author"}], "References": [{"Title": "A secure authentication scheme framework for mobile-sinks used in the Internet of Drones applications", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "155", "Issue": "", "Page": "143", "JournalTitle": "Computer Communications"}, {"Title": "Blockchain-based security attack resilience schemes for autonomous vehicles in industry 4.0: A systematic review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106717", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "An efficient and reliable ultralightweight RFID authentication scheme for healthcare systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "205", "Issue": "", "Page": "147", "JournalTitle": "Computer Communications"}, {"Title": "HCALA: Hyperelliptic curve-based anonymous lightweight authentication scheme for Internet of Drones", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "92", "Issue": "", "Page": "101798", "JournalTitle": "Pervasive and Mobile Computing"}, {"Title": "Design of provably secure and lightweight authentication protocol for unmanned aerial vehicle systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "228", "Issue": "", "Page": "107971", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 144039955, "Title": "Unlocking Seamless Gesture-Based Interfaces: Hand Gesture Recognition with 3DCNN and LSTM", "Abstract": "", "Keywords": "", "DOI": "10.1142/S0218213025500071", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 98756, "JournalTitle": "International Journal on Artificial Intelligence Tools", "ISSN": "0218-2130", "EISSN": "1793-6349", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Y. S. Gan", "Affiliation": ""}], "References": []}, {"ArticleId": 144039998, "Title": "Prediction Models for Early Detection of Alzheimer: Recent Trends and Future Prospects", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11831-025-10246-3", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Prediction of Alzheimer's disease based on deep neural network by integrating gene expression and DNA methylation dataset", "Authors": "Chihyun Park; Jihwan Ha; Sanghyun Park", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "112873", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Hybrid chaotic firefly decision making model for <PERSON>’s disease diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "1", "Page": "155014771989521", "JournalTitle": "International Journal of Distributed Sensor Networks"}, {"Title": "Detecting Alzheimer's disease Based on 4D fMRI: An exploration under deep learning framework", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "388", "Issue": "", "Page": "280", "JournalTitle": "Neurocomputing"}, {"Title": "Deep and joint learning of longitudinal data for Alzheimer's disease prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107247", "JournalTitle": "Pattern Recognition"}, {"Title": "Multimodal multitask deep learning model for Alzheimer’s disease progression detection based on time series data", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "412", "Issue": "", "Page": "197", "JournalTitle": "Neurocomputing"}, {"Title": "Adaptive Multi-Task Dual-Structured Learning with Its Application on Alzheimer’s Disease Study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "DeTrAs: deep learning-based healthcare framework for IoT-based assistance of Alzheimer patients", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Deep learning for Alzheimer prediction using brain biomarkers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "7", "Page": "4827", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Alzheimer Disease Detection Empowered with Transfer Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "3", "Page": "5005", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Study on the effect of extreme learning machine and its variants in differentiating Alzheimer conditions from selective regions of brain MR images", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "209", "Issue": "", "Page": "118250", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Data classification using rough set and bioinspired computing in healthcare applications - an extensive review", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "9", "Page": "13479", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Improving Alzheimer’s Disease and Brain Tumor Detection Using Deep Learning with Particle Swarm Optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "3", "Page": "551", "JournalTitle": "AI"}, {"Title": "Comparative analysis of different brain regions using machine learning for prediction of EMCI and LMCI stages of Alzheimer’s disease", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "7", "Page": "21455", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "EDCNNS: Federated learning enabled evolutionary deep convolutional neural network for Alzheimer disease detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "147", "Issue": "", "Page": "110804", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 144040040, "Title": "A majority voting framework for reliable sentiment analysis of product reviews", "Abstract": "<p>This article presents a tailored majority voting approach for enhancing the consistency and reliability of sentiment analysis in online product reviews. The methodology addresses discrepancies in sentiment classification by leveraging sentiment labels from multiple automated tools and implementing a robust majority decision rule. This consensus-based approach significantly enhances the trustworthiness and consistency of sentiment analysis outcomes, serving as a dependable foundation for training more precise sentiment analysis models. The data labeled with our method was utilized to train deep learning models, achieving competitive accuracy with significantly less data. The findings demonstrate the effectiveness of the method in producing results comparable to commercial tools while ensuring data consistency for model training.</p>", "Keywords": "Automated labeling;Deep learning;Majority voting;Online product reviews;Sentiment analysis", "DOI": "10.7717/peerj-cs.2738", "PubYear": 2025, "Volume": "11", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business Information Systems, Babes-Bolyai University of Cluj-Napoca, Cluj-Napoca, Romania;Rutgers, The State University of New Jersey-Newark, Newark, NJ, United States"}], "References": [{"Title": "How textual quality of online reviews affect classification performance: a case of deep learning sentiment analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "4387", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Aspect-based sentiment analysis with gated alternate neural network", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "188", "Issue": "", "Page": "105010", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Toward multi-label sentiment analysis: a transfer learning based approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "Monitoring online reviews for reputation fraud campaigns", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105685", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Using VADER sentiment and SVM for predicting customer response sentiment", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "113746", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Good and bad events: combining network-based event detection with sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "SSentiA: A Self-supervised Sentiment Analyzer for classification from unlabeled data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "100026", "JournalTitle": "Machine Learning with Applications"}, {"Title": "Strategic manipulation of online information in duopolies: Inducing fight-back?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101052", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Discrepancy detection between actual user reviews and numeric ratings of Google App store using deep learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "115111", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Textual analysis of traitor-based dataset through semi supervised machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "125", "Issue": "", "Page": "652", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A systematic literature review on machine learning applications for consumer sentiment analysis using online reviews", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "41", "Issue": "", "Page": "100413", "JournalTitle": "Computer Science Review"}, {"Title": "A survey on sentiment analysis methods, applications, and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "7", "Page": "5731", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Deceptive reviews and sentiment polarity: Effective link by exploiting BERT", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "209", "Issue": "", "Page": "118290", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Benefits or harms? The effect of online review manipulation on sales", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "57", "Issue": "", "Page": "101224", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "A deep learning-based model using hybrid feature extraction approach for consumer sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "1", "Page": "5", "JournalTitle": "Journal of Big Data"}, {"Title": "Sentiment analysis classification system using hybrid BERT models", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "Analysis of customer reviews with an improved VADER lexicon classifier", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "Sentiment analysis methods, applications, and challenges: A systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "4", "Page": "102048", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Price promotion does not always work: online reviews, price-quality heuristics, and risk aversion", "Authors": "<PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Electronic Commerce Research"}]}, {"ArticleId": 144040268, "Title": "Federated Workload-Aware Quantized Framework for Secure Learning in Data-Sensitive Applications", "Abstract": "Federated Learning (FL) emerged as a leading secure, distributed learning technology based on sharing insights instead of data. The privacy-ensuring capability of FL has enabled its extensive use in Data-Sensitive Applications like healthcare and finance. However, the transmitted insights are at risk of leakage as the security of the medium cannot be guaranteed and can lead to the inference of the user data. Quantization is sometimes used to change these transmitted values to provide security but at the cost of accuracy loss in global models. Coupled with client dropouts, this increases performance loss. In this paper, we propose a Federated Workload-Aware Framework with Linear Quantization (Fed-WALQ), which layers the quantization process with an active client-selection technique based on the sustainable workload of the clients. The framework minimizes the dropout rates and compensates for the loss due to quantization. Through numerical experiments compared against traditional FL and Quantization-enabled FL over multiple datasets, the Fed-WALQ shows improvements in security over the former and accuracy over the latter. The accuracy improvement varies with the complexities of the involved datasets, while a substantial drop in straggler node percentages is seen in all cases (up to 91.8% drop).", "Keywords": "", "DOI": "10.1016/j.future.2025.107772", "PubYear": 2025, "Volume": "168", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Delhi Technological University, Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jawaharlal Nehru University, Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Delhi Technological University, Delhi, India;Corresponding author"}], "References": [{"Title": "Fairness and accuracy in horizontal federated learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "170", "JournalTitle": "Information Sciences"}, {"Title": "A dynamic incentive and reputation mechanism for energy-efficient federated learning in 6G", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "4", "Page": "817", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Blockchain Empowered Federated Learning for Data Sharing Incentive Mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "348", "JournalTitle": "Procedia Computer Science"}, {"Title": "A comprehensive review on Federated Learning for Data-Sensitive Application: Open issues & challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "133", "Issue": "", "Page": "108128", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Reliable incentive mechanism in hierarchical federated learning based on two-way reputation and contract theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "159", "Issue": "", "Page": "533", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 144040280, "Title": "Multi-attribute decision-making using q-rung orthopair fuzzy Zagreb index", "Abstract": "<p>The q -rung orthopair fuzzy set ( q - ROFS ), an extension of intuitionistic and Pythagorean fuzzy sets, offers greater flexibility in representing vague information with two possible outcomes, yes or no. The fuzzy Zagreb index is an important graph parameter, widely used in fields such as network theory, spectral graph theory, mathematics, and molecular chemistry. In this paper, the first and second Zagreb indices for q-rung orthopair fuzzy graphs (q-ROFGs) are introduced, and bounds for these indices are established, including their behavior in regular q-ROFGs. Additionally, it is explored, how various graph operations such as union, Cartesian product, direct product, and lexicographical product affect the first Zagreb index. Furthermore, a new approach is presented that combines Multiple-Attribute Decision-Making (MADM) with graph-based models to improve decision-making, particularly in vaccine selection. The methodology constructs a bipartite graph for each attribute, where virologists assign membership and non-membership values to vaccines. The Zagreb index is used to measure the importance of each vaccine, and a weighted aggregation technique normalizes the scores. The final ranking is derived from a computed score function. The results demonstrate the effectiveness of the approach in providing a systematic and mathematically rigorous framework for multi-attribute decision-making, with rank correlation analysis confirming its robustness compared to existing methods such as q -ROF PROMETHEE, q -ROF VICOR, q -ROF TOPSIS, q -ROFWG, and q -ROFWA.</p>", "Keywords": "Lexicographic product; Cartesian product; Direct product; Zagreb indices; Topological indices", "DOI": "10.1007/s10462-025-11149-2", "PubYear": 2025, "Volume": "58", "Issue": "5", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computing Science and Technology, Guangzhou University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Science and Technology, Guangzhou University, Guangzhou, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of the Punjab, Lahore, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of the Punjab, Lahore, Pakistan"}], "References": [{"Title": "Solving green supplier selection problem using q-rung orthopair fuzzy-based decision framework with unknown weight information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106431", "JournalTitle": "Applied Soft Computing"}, {"Title": "Determination of journeys order based on graph’s Wiener absolute index with bipolar fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "545", "Issue": "", "Page": "608", "JournalTitle": "Information Sciences"}, {"Title": "Certain Properties of Single-Valued Neutrosophic Graph With Application in Food and Agriculture Organization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1516", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Decision-making with q-rung orthopair fuzzy graph structures", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "3", "Page": "505", "JournalTitle": "Granular Computing"}, {"Title": "Multicriteria decision-making based on the degree and distance-based indices of fuzzy graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "4", "Page": "793", "JournalTitle": "Granular Computing"}, {"Title": "Connectivity indices of m-polar fuzzy network model, with an application to a product manufacturing problem", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "8", "Page": "7795", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Fuzzy topological indices with application to cybercrime problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "5", "Page": "967", "JournalTitle": "Granular Computing"}, {"Title": "Explication of crossroads order based on Randic index of graph with fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "28", "Issue": "3", "Page": "1851", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 144040327, "Title": "PyFR v2.0.3: Towards Industrial Adoption of Scale-Resolving Simulations", "Abstract": "PyFR is an open-source cross-platform computational fluid dynamics framework based on the high-order Flux Reconstruction approach, specifically designed for undertaking high-accuracy scale-resolving simulations in the vicinity of complex engineering geometries. Since the initial release of PyFR v0.1.0 in 2013, a range of new capabilities have been added to the framework, with a view to enabling industrial adoption. In this work, we provide details of these enhancements as released in PyFR v2.0.3, including improvements to cross-platform performance (new backends, extensions of the DSL, new matrix multiplication providers, improvements to the data layout, use of task graphs) and improvements to numerical stability (modal filtering, anti-aliasing, artificial viscosity, entropy filtering), as well as the addition of prismatic, tetrahedral and pyramid shaped elements, improved domain decomposition support for mixed element grids, improved handling of curved element meshes, the addition of an adaptive time-stepping capability, the addition of incompressible Euler and Navier-Stokes solvers, improvements to file formats and the development of a plugin architecture. We also explain efforts to grow an engaged developer and user community and provided a range of examples that show how our user base is applying PyFR to solve a wide range of fundamental, applied and industrial flow problems. Finally, we demonstrate the accuracy of PyFR v2.0.3 for a supersonic Taylor-Green vortex case, with shocks and turbulence, and provided latest performance and scaling results on up to 1024 AMD Instinct MI250X accelerators of Frontier at ORNL (each with two GCDs) and up to 2048 Nvidia GH200 GPUs of Alps at CSCS. We note that absolute performance of PyFR accounting for the totality of both hardware and software improvements has, conservatively, increased by almost 50× over the last decade. <b >Program summary</b> Program Title: PyFR CPC Library link to program files: https://doi.org/10.17632/vmgh4kfjk6.1 Developer's repository link: https://github.com/PyFR/PyFR Licensing provisions: BSD 3-clause Programming language: Python (generating C/OpenMP, CUDA, OpenCL, HIP, Metal) Nature of problem: Accurate and efficient scale-resolving simulation of industrial flows. Solution method: Massively parallel cross-platform implementation of high-order accurate Flux Reconstruction schemes.", "Keywords": "High-order accuracy; Flux reconstruction; Computational fluid dynamics", "DOI": "10.1016/j.cpc.2025.109567", "PubYear": 2025, "Volume": "311", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Ocean Engineering, Texas A&M University, 3145 TAMU, College Station, 77843, TX, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Aeronautics, Imperial College London, South Kensington, London, SW7 2AZ, UK;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IBM Research UK, Hartree Centre, Warrington, WA4 4AD, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Fluid Science, Tohoku University, Sendai, Japan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Ocean Engineering, Texas A&M University, 3145 TAMU, College Station, 77843, TX, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Aeronautics, Imperial College London, South Kensington, London, SW7 2AZ, UK"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Ocean Engineering, Texas A&M University, 3145 TAMU, College Station, 77843, TX, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Aeronautics, Imperial College London, South Kensington, London, SW7 2AZ, UK"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Lawrence Livermore National Laboratory, Livermore, CA, USA"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Siemens Digital Industries Software, Shepherds Bush Road, London, W6 7NL, UK"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Astrome Technologies, Bangalore, India"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Department of Ocean Engineering, Texas A&M University, 3145 TAMU, College Station, 77843, TX, USA"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "NVIDIA Corporation, Fasanenstr 81, Berlin, 10623, Germany"}, {"AuthorId": 14, "Name": "<PERSON><PERSON>", "Affiliation": "NVIDIA Corporation, Porkkalankatu 1, Helsinki, 00180, Finland"}, {"AuthorId": 15, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ocean Engineering, Texas A&M University, 3145 TAMU, College Station, 77843, TX, USA"}, {"AuthorId": 16, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Ocean Engineering, Texas A&M University, 3145 TAMU, College Station, 77843, TX, USA"}, {"AuthorId": 17, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Luminary, 101 S Ellsworth Ave Suite 600, San Mateo, CA, USA;ISAE SUPAERO, University of Toulouse, 31400 Toulouse, France"}, {"AuthorId": 18, "Name": "<PERSON>", "Affiliation": "Department of Aerospace Engineering and Program in Aerospace Systems Convergence, Inha University, Incheon, Korea"}, {"AuthorId": 19, "Name": "<PERSON>", "Affiliation": "Department of Mechanical, Industrial, and Aerospace Engineering, Concordia University, Montreal, QC, Canada"}, {"AuthorId": 20, "Name": "<PERSON>", "Affiliation": "Gaithersburg, MD, USA"}], "References": [{"Title": "Nektar++: Enhancing the capability and application of high-fidelity spectral/hp element methods", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "249", "Issue": "", "Page": "107110", "JournalTitle": "Computer Physics Communications"}, {"Title": "FLEXI: A high order discontinuous Galerkin framework for hyperbolic–parabolic conservation laws", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "81", "Issue": "", "Page": "186", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "High-order accurate direct numerical simulation of flow over a MTU-T161 low pressure turbine blade", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; B.C. Vermeire", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "104989", "JournalTitle": "Computers & Fluids"}, {"Title": "OpenSBLI: Automated code-generation for heterogeneous computing architectures applied to compressible fluid dynamics on structured grids", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "267", "Issue": "", "Page": "108063", "JournalTitle": "Computer Physics Communications"}, {"Title": "Cache blocking strategies applied to flux reconstruction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "271", "Issue": "", "Page": "108193", "JournalTitle": "Computer Physics Communications"}, {"Title": "Positivity-preserving entropy filtering for the ideal magnetohydrodynamics equations", "Authors": "<PERSON><PERSON>; F<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "266", "Issue": "", "Page": "106056", "JournalTitle": "Computers & Fluids"}, {"Title": "Positivity-preserving discontinuous spectral element methods for compressible multi-species flows", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "280", "Issue": "", "Page": "106343", "JournalTitle": "Computers & Fluids"}]}, {"ArticleId": 144040329, "Title": "Assessing organizational performance post-GST adoption in India: an application of the UTAUT model with PLS-SEM and IPMA analysis", "Abstract": "", "Keywords": "", "DOI": "10.1007/s41870-024-02351-6", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> .", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Designing conceptual model and statistical validation for Government-citizen participation model in Indian context", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "2", "Page": "637", "JournalTitle": "International Journal of Information Technology"}, {"Title": "A hybrid SEM and ANN approach to predict the individual cloud computing adoption based on the UTAUT2", "Authors": "<PERSON><PERSON><PERSON><PERSON> Song", "PubYear": 2022, "Volume": "14", "Issue": "7", "Page": "3539", "JournalTitle": "International Journal of Information Technology"}, {"Title": "The moderating role of trust in government adoption e-service during Covid-19 pandemic: health belief model perspective", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "3", "Page": "1545", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Adoption and sustainability of bitcoin and the blockchain technology in Nigeria", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Martins <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "5", "Page": "2793", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Improving performance of recommendation systems using sentiment patterns of user", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "7", "Page": "3779", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Digital transformation: acceptance and use of technology among microfinance institutions in developing country: an application of UTAUT2 mode", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "8", "Page": "4459", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Assessing and prioritizing crucial drivers for CloudIoT-based healthcare adoption: an analytic hierarchy process approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Using the Unified Theory of Acceptance and Use of Technology to Investigate the Adoption of Open Educational Resources by Faculty Members", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "6", "Page": "3201", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 144040348, "Title": "Large Language Model-Aware In-Context Learning for Code Generation", "Abstract": "<p>Large Language Models (LLMs) have shown impressive In-Context Learning (ICL) ability in code generation. LLMs take a prompt context consisting of a few demonstration examples and a new requirement as input, and output new programs without any parameter update. Existing studies have found that the performance of ICL-based code generation heavily depends on the quality of demonstration examples and thus arises research on selecting demonstration examples: given a new requirement, a few demonstration examples are selected from a candidate pool, where LLMs are expected to learn the pattern hidden in these selected demonstration examples. Existing approaches are mostly based on heuristics or randomly selecting examples. However, the distribution of randomly selected examples usually varies greatly, making the performance of LLMs less robust. The heuristics retrieve examples by only considering textual similarities of requirements, leading to sub-optimal performance.</p><p> To fill this gap, we propose a L arge language model- A ware selection approach for I n-context- L earning-based code generation named LAIL. LAIL uses LLMs themselves to select examples. It requires LLMs themselves to label a candidate example as a positive example or a negative example for a requirement. Positive examples are helpful for LLMs to generate correct programs, while negative examples are trivial and should be ignored. Based on the labeled positive and negative data, LAIL trains a model-aware retriever to learn the preference of LLMs and select demonstration examples that LLMs need. During the inference, given a new requirement, LAIL uses the trained retriever to select a few examples and feed them into LLMs to generate desired programs. We apply LAIL to four widely used LLMs and evaluate it on five code generation datasets. Extensive experiments demonstrate that LAIL outperforms the state-of-the-art (SOTA) baselines by 11.58%, 3.33%, and 5.07% on CodeGen-Multi-16B, 1.32%, 2.29%, and 1.20% on CodeLlama-34B, and achieves 4.38%, 2.85%, and 2.74% improvements on Text-davinci-003 in terms of Pass@1 at MBJP, MBPP, and MBCPP, respectively. In addition to function-level code generation, LAIL improves the performance of LLMs on DevEval, a repository-level code generation dataset, which achieves 10.04%, 8.12%, and 4.63% improvements compared to the SOTA baselines at Pass@1, 3, and 5 on CodeLlama-7B. Human evaluation further verifies that the generated programs of LAIL are superior in correctness, code quality, and maintainability. Besides, LAIL has satisfactory transferability across different LLMs and datasets, where the retriever learned on one LLM (dataset) can be transferred to other LLMs (datasets). </p>", "Keywords": "", "DOI": "10.1145/3715908", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 14907, "JournalTitle": "ACM Transactions on Software Engineering and Methodology", "ISSN": "1049-331X", "EISSN": "1557-7392", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Lab of High Confidence Software Technology (Peking University), MoE, China"}, {"AuthorId": 2, "Name": "Chongyang Tao", "Affiliation": "Beihang University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Lab of High Confidence Software Technology (Peking University), MoE, China"}, {"AuthorId": 4, "Name": "Ge Li", "Affiliation": "Key Lab of High Confidence Software Technology (Peking University), MoE, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Lab of High Confidence Software Technology (Peking University), MoE, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of High Confidence Software Technology (Peking University), MoE, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Key Lab of High Confidence Software Technology (Peking University), MoE, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "The State Key Laboratory of Software Development Environment (SKLSDE), SEI, School of Computer Science & Engineering, Beihang University, China"}], "References": [{"Title": "GitHub Copilot AI pair programmer: Asset or Liability?", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "203", "Issue": "", "Page": "111734", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 144040497, "Title": "Establish a Novel Neural Network-Based Art Design Principles Recognition Model", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.eswa.2025.127073", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Fine-art painting classification via two-channel dual path networks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "137", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Artist-based painting classification using Markov random fields with convolution neural network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "Page": "12635", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Hierarchical classification of fine-art paintings using deep neural networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "1", "Page": "59", "JournalTitle": "Iran Journal of Computer Science"}, {"Title": "Novel features for art movement classification of portrait paintings", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "104121", "JournalTitle": "Image and Vision Computing"}, {"Title": "Artificial intelligence in the creative industries: a review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "1", "Page": "589", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Artificial intelligence-based creative thinking skill analysis model using human–computer interaction in art design teaching", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "100", "Issue": "", "Page": "107957", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Assessing the best art design based on artificial intelligence and machine learning using GTMA", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "1", "Page": "149", "JournalTitle": "Soft Computing"}, {"Title": "Artistic and Structural Connections Between Architecture and Music – a Comparative Case Study of the Forbidden City and Palace Memories", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "26", "Issue": "2", "Page": "353", "JournalTitle": "Nexus Network Journal"}]}, {"ArticleId": 144040519, "Title": "Evanescent Field-based Sensor Design Using Uniform Strips on SOI-based Slot Type of Waveguide for Brain Cancer Application", "Abstract": "Introduction: <p>The slot type of photonic waveguide is commonly used in designing sensors based on the evanescent field because of its high evanescent field characteristics.</p> Method: <p>In this study, we introduced multiple uniform strips to improve the performance of the waveguide, aiming for a higher evanescent field ratio and reduced propagation loss to optimize the effectiveness of the sensor. The inclusion of uniform strips enhances the overall capabilities of the waveguide for evanescent field-based sensing applications.</p> objective: <p>This study aims to improve the evanescent field and sensitivity of slot waveguide structures while keeping propagation loss minimal. The research explores the potential applications of different slot waveguide configurations, particularly for sensors like the brain cancer sensor.</p> Result: <p>The investigation showed that the one-strip and three-strip design structures achieved maximum evanescent field values of 0.43 and 0.38, respectively, indicating significant levels. However, the three-strip design structure exhibited the minimum propagation loss, recorded at 9.1 dB/cm. Considering the variations in Evanescent Field Ratio (EFR) and propagation loss, the slot waveguide with three strips emerges as a potentially optimal design structure.</p> Conclusion: <p>This conclusion is particularly relevant in the context of brain cancer applications, where heightened sensitivity is crucial. Therefore, the three-strip configuration shows promise for superior performance in detecting and addressing the complexities of brain cancer.</p>", "Keywords": "", "DOI": "10.2174/0118764029351169250211043329", "PubYear": 2025, "Volume": "17", "Issue": "", "JournalId": 12053, "JournalTitle": "Micro and Nanosystems", "ISSN": "1876-4029", "EISSN": "1876-4037", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, SRM Institute of Science and Technology Kattankulathur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, SRM Institute of Science and Technology Kattankulathur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, SRM Institute of Science and Technology Kattankulathur, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, SRM Institute of Science and Technology Kattankulathur, India"}], "References": []}, {"ArticleId": 144040536, "Title": "CAT-UNet: Integrating CNN Attention Mechanism and TransUNet for Lung Mass Segmentation", "Abstract": "", "Keywords": "", "DOI": "10.1142/S0218001425520020", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144040537, "Title": "Mining Emerging Patterns of Data Stream for Improving Classification Performance", "Abstract": "", "Keywords": "", "DOI": "10.1142/S0218001425500053", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144040589, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1566-2535(25)00127-7", "PubYear": 2025, "Volume": "118", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [], "References": []}, {"ArticleId": 144040614, "Title": "Neural Network Models for Prostate Zones Segmentation in Magnetic Resonance Imaging", "Abstract": "<p>Prostate cancer (PCa) is one of the most common tumors diagnosed in men worldwide, with approximately 1.7 million new cases expected by 2030. Most cancerous lesions in PCa are located in the peripheral zone (PZ); therefore, accurate identification of the location of the lesion is essential for effective diagnosis and treatment. Zonal segmentation in magnetic resonance imaging (MRI) scans is critical and plays a key role in pinpointing cancerous regions and treatment strategies. In this work, we report on the development of three advanced neural network-based models: one based on ensemble learning, one on Meta-Net, and one on YOLO-V8. They were tailored for the segmentation of the central gland (CG) and PZ using a small dataset of 90 MRI scans for training, 25 MRIs for validation, and 24 scans for testing. The ensemble learning method, combining U-Net-based models (Attention-Res-U-Net, Vanilla-Net, and V-Net), achieved an IoU of 79.3% and DSC of 88.4% for CG and an IoU of 54.5% and DSC of 70.5% for PZ on the test set. Meta-Net, used for the first time in segmentation, demonstrated an IoU of 78% and DSC of 88% for CG, while YOLO-V8 outperformed both models with an IoU of 80% and DSC of 89% for CG and an IoU of 58% and DSC of 73% for PZ.</p>", "Keywords": "", "DOI": "10.3390/info16030186", "PubYear": 2025, "Volume": "16", "Issue": "3", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Università degli Studi di Milano, 20133 Milano, Italy"}, {"AuthorId": 2, "Name": "Luca <PERSON>", "Affiliation": "Department of Diagnostic Imaging and Stereotactic Radiosurgery, Centro Diagnostico Italiano (CDI), 20147 Milano, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Diagnostic Imaging and Stereotactic Radiosurgery, Centro Diagnostico Italiano (CDI), 20147 Milano, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Diagnostic Imaging and Stereotactic Radiosurgery, Centro Diagnostico Italiano (CDI), 20147 Milano, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "R&D, Bracco Imaging S.p.A., 20134 Milano, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Diagnostic Imaging and Stereotactic Radiosurgery, Centro Diagnostico Italiano (CDI), 20147 Milano, Italy"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Informatica, Sistemistica e Comunicazione (DISCo), Università degli Studi di Milano-Bicocca, 20126 Milano, Italy"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Diagnostic Imaging and Stereotactic Radiosurgery, Centro Diagnostico Italiano (CDI), 20147 Milano, Italy; R&D, Bracco Imaging S.p.A., 20134 Milano, Italy"}], "References": [{"Title": "Unet based Xception Model for Prostate Cancer Segmentation from MRI Images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "26", "Page": "37333", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 144040731, "Title": "Design and Implementation Evaluation of Personalized and Differentiated Teaching Strategies for Preschool Children Based on Fuzzy Decision Support Systems", "Abstract": "<p>School operations have changed due to information technology. Personalized teaching for preschool children requires innovative and adaptable strategies for providing the best-afford experience and learning environment. Conventional teaching approaches may not always accommodate students’ learning styles. This can lower performance and involvement. The expanding variety of pre-schoolers necessitates creative evaluation and optimization methods that promote inclusion and success for all children. The article introduces a preschool teaching evaluation model to improve the students’ learning experience. In particular, the evaluation model is designed for various assessment strategies irrespective of personalization. The proposed model inputs the students’ learning feasibility and the teaching mode to evaluate the personalization adaptability. The fuzzy decision support system improves the evaluation based on the above factors. It evaluates student-specific indicators, including attentiveness, body language, and engagement, to determine instructional flexibility. The model uses real-time classroom observations and student assessments to find realistic approaches with low-to-high fuzzy derivatives. The proposed system is designed to compute the adaptability from both factors under low-to-high fuzzy derivatives. By defining the maximum feasibility range, the teaching strategy is optimized to meet the adaptability. Thus, the low-level strategies are discarded using adaptability measures to reduce personalization failures. The proposed model is verified using adaptability, feasibility, and mode improvements. Research shows that the fuzzy decision support system makes courses more adaptive and feasible in many contexts, particularly those that involve games, audiovisual approaches, and crafts. Preschoolers, parents, and teachers indicated increased enjoyment, fewer customization failures, and greater involvement. Fuzzy-based evaluation increased feasibility ratings and approach versatility. The research provides a valuable foundation for solving classroom customization difficulties in preschool settings, emphasizing data-driven, adaptive teaching techniques. This method enables scalable applications in early childhood education through fuzzy decision-making and real-time evaluations.</p>", "Keywords": "Fuzzy decision; Preschool teaching; Strategy evaluation; Teaching personalization", "DOI": "10.1007/s44196-025-00748-0", "PubYear": 2025, "Volume": "18", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Education College, Shaanxi Fashion Engineering University, Xi’an, China; Corresponding author."}], "References": [{"Title": "Multi-Objective Heuristic Decision Making and Benchmarking for Mobile Applications in English Language Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "A Knowledge Diffusion Model in Autonomous Learning Under Multiple Networks for Personalized Educational Resource Allocation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "4", "Page": "430", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "Preschool children’s social and playful interactions with a play-facilitating cardboard robot", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON> Præst <PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "", "Page": "100435", "JournalTitle": "International Journal of Child-Computer Interaction"}, {"Title": "The inﬂuence of digital educational games on preschool Children's creative thinking", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "189", "Issue": "", "Page": "104578", "JournalTitle": "Computers & Education"}, {"Title": "The key artificial intelligence technologies in early childhood education: a review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "1", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "An interactive teaching evaluation system for preschool education in universities based on machine learning algorithm", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "157", "Issue": "", "Page": "108211", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Intelligent e-learning system in the development of preschool music education based on digital audio technology", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "50", "Issue": "", "Page": "100682", "JournalTitle": "Entertainment Computing"}]}, {"ArticleId": *********, "Title": "Generative AI and LLMs in Banking: A Technical Roadmap", "Abstract": "", "Keywords": "", "DOI": "10.32628/CSEIT251112367", "PubYear": 2025, "Volume": "11", "Issue": "1", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Modernizing Banking Compliance: An Analysis of AI-Powered Data Governance in a Hybrid Cloud Environment", "Authors": "<PERSON><PERSON><PERSON> Boggarapu", "PubYear": 2024, "Volume": "10", "Issue": "6", "Page": "2373", "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology"}]}, {"ArticleId": *********, "Title": "$$\\text {H}^2\\text {CAN}$$: heterogeneous hypergraph attention network with counterfactual learning for multimodal sentiment analysis", "Abstract": "<p>Multimodal sentiment analysis (MSA) has garnered significant attention for its immense potential in human-computer interaction. While cross-modality attention mechanisms are widely used in MSA to capture inter-modality interactions, existing methods are limited to pairwise interactions between two modalities. Additionally, these methods can not utilize the causal relationship to guide attention learning, making them susceptible to bias information. To address these limitations, we introduce a novel method called Heterogeneous Hypergraph Attention Network with Counterfactual Learning ((\text {H}^2\text {CAN}).) The method constructs a heterogeneous hypergraph based on sentiment expression characteristics and employs Heterogeneous Hypergraph Attention Networks (HHGAT) to capture interactions beyond pairwise constraints. Furthermore, it mitigates the effects of bias through a Counterfactual Intervention Task (CIT). Our model comprises two main branches: hypergraph fusion and counterfactual fusion. The former uses HHGAT to capture inter-modality interactions, while the latter constructs a counterfactual world using Gaussian distribution and additional weighting for the biased modality. The CIT leverages causal inference to maximize the prediction discrepancy between the two branches, guiding attention learning in the hypergraph fusion branch. We utilize unimodal labels to help the model adaptively identify the biased modality, thereby enhancing the handling of bias information. Experiments on three mainstream datasets demonstrate that (\text {H}^2\text {CAN}) sets a new benchmark.</p>", "Keywords": "Multimodal sentiment analysis; Modality interaction; Heterogeneous hypergraph; Counterfactual learning", "DOI": "10.1007/s40747-025-01806-y", "PubYear": 2025, "Volume": "11", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Key Laboratory of Intelligent Education Technology and Application, Zhejiang Normal University, Jinhua, China; School of Education, Zhejiang Normal University, Jinhua, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Key Laboratory of Intelligent Education Technology and Application, Zhejiang Normal University, Jinhua, China; School of Computer Science and Technology, Zhejiang Normal University, Jinhua, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Key Laboratory of Intelligent Education Technology and Application, Zhejiang Normal University, Jinhua, China; School of Education, Zhejiang Normal University, Jinhua, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Mathematics and Engineering, Charles Sturt University, Albury, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Zhejiang Key Laboratory of Intelligent Education Technology and Application, Zhejiang Normal University, Jinhua, China; School of Computer Science and Technology, Zhejiang Normal University, Jinhua, China; Corresponding author."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang Key Laboratory of Intelligent Education Technology and Application, Zhejiang Normal University, Jinhua, China; School of Education, Zhejiang Normal University, Jinhua, China"}], "References": [{"Title": "Deep Emotional Arousal Network for Multimodal Sentiment Analysis and Emotion Recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "88", "Issue": "", "Page": "296", "JournalTitle": "Information Fusion"}, {"Title": "Multimodal sentiment analysis: A systematic review of history, datasets, multimodal fusion methods, applications, challenges and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "424", "JournalTitle": "Information Fusion"}, {"Title": "Multimodal Sentiment Analysis: A Survey of Methods, Trends, and Challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "13s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "TeFNA: Text-centered fusion network with crossmodal attention for multimodal sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "269", "Issue": "", "Page": "110502", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Learning discriminative multi-relation representations for multimodal sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "641", "Issue": "", "Page": "119125", "JournalTitle": "Information Sciences"}, {"Title": "Dynamic hypergraph convolutional network for multimodal sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "565", "Issue": "", "Page": "126992", "JournalTitle": "Neurocomputing"}, {"Title": "Visual sentiment analysis with semantic correlation enhancement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "2", "Page": "2869", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "TMBL: Transformer-based multimodal binding learning model for multimodal sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "285", "Issue": "", "Page": "111346", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Video multimodal sentiment analysis using cross-modal feature translation and dynamical propagation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "299", "Issue": "", "Page": "111982", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "AtCAF: Attention-based causality-aware fusion network for multimodal sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "114", "Issue": "", "Page": "102725", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 144040944, "Title": "A Systematic Review of the Benefits and Challenges of Data Analytics in Organizational Decision Making", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160221", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144040945, "Title": "Data Analytics for Product Segmentation and Demand Forecasting of a Local Retail Store Using Python", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160224", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144040946, "Title": "Enhancing Recurrent Neural Network Efficacy in Online Sales Predictions with Exploratory Data Analysis", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160230", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144040947, "Title": "Lightweight CA-YOLOv7-Based Badminton Stroke Recognition: A Real-Time and Accurate Behavior Analysis Method", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160235", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144040949, "Title": "BlockMed: AI Driven HL7-FHIR Translation with Blockchain-Based Security", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160233", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144040951, "Title": "Enhancing Emotion Prediction in Multimedia Content Through Multi-Task Learning", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.01602118", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144040952, "Title": "Planning and Design of Elderly Care Space Combining PER and Dueling DQN", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160250", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144040953, "Title": "Machine Learning-Based Denoising Techniques for Monte Carlo Rendering: A Literature Review", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.0160259", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 144040954, "Title": "Investigating Retrieval-Augmented Generation in Quranic Studies: A Study of 13 Open-Source Large Language Models", "Abstract": "", "Keywords": "", "DOI": "10.14569/IJACSA.2025.01602134", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}]