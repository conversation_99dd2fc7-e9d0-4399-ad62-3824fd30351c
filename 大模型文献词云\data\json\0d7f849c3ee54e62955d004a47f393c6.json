[{"ArticleId": 92378158, "Title": "Deep asymmetric hashing with dual semantic regression and class structure quantization", "Abstract": "Recently, deep hashing methods have been widely used in image retrieval task. Most existing deep hashing approaches adopt one-to-one quantization to reduce information loss. However, such class-unrelated quantization cannot give discriminative feedback for network training. In addition, these methods only utilize single label to integrate supervision information of data for hashing function learning, which may result in inferior network generalization performance and relatively low-quality hash codes since the inter-class information of data is totally ignored. In this paper, we propose a dual semantic asymmetric hashing (DSAH) method, which generates discriminative hash codes under threefold constraints. Firstly, DSAH utilizes class prior to conduct class structure quantization so as to transmit class information during the quantization process. Secondly, a simple yet effective label mechanism is designed to characterize both the intra-class compactness and inter-class separability of data, thereby achieving semantic-sensitive binary code learning. Finally, a meaningful pairwise similarity preserving loss is devised to minimize the distances between class-related network outputs based on an affinity graph. With these three main components, high-quality hash codes can be generated through network. Extensive experiments conducted on various data sets demonstrate the superiority of DSAH in comparison with state-of-the-art deep hashing methods. Introduction Hashing has received extensive attention over the past few years in the field of image search. It aims to transform high-dimensional data into compact binary representations, while maintaining original similarity structure of data [37]. Owing to its low storage consumption and high computation efficiency, hashing has been successfully applied in many fields, such as deep neural network quantization [3], sketch retrieval [41], domain adaptation [36], dimensionality reduction [14], and multi-label image search [34]. Traditional hashing methods can be roughly divided into two categories, including data-independent and data-dependent methods. The typical data-independent hashing method, locality sensitive hashing (LSH) [6], generates binary codes of data via random projections. In [23], a global low-density locality sensitive hashing (GLDH) is proposed to produce a low error hyperplane partition and find the global low-density hyperplanes. Although these methods achieve good retrieval efficiency, they require relatively long hash codes to obtain high precision and recall rates [33]. In order to learn compact binary codes, a family of data-dependent hashing methods have been proposed, which can be further classified into unsupervised and supervised methods. Most of unsupervised hashing methods aim to learn compact binary codes embedded on their intrinsic manifolds [24], [33], [39], [38]. These methods typically construct a graph incorporating affinity relationship of data for effective binary code learning. For instance, spectral hashing (SH) [39] aims to preserve the intrinsic manifold structure of data in the Hamming space. Due to the difficulty in optimization, SH adopts relaxation strategy to optimize its objective function by removing the binary constraint directly. However, such relaxation strategy makes a great information loss, resulting in performance degradation. To this end, discrete graph hashing (DGH) [26] explicitly handles the binary constraint and directly learns hash codes, which better characterizes the manifold structure of data and produces higher-quality binary codes. Being different from DGH, iterative quantization (ITQ) [8] attempts to find an orthogonal rotation matrix to minimize the information loss between hash codes and data representations. The binary reconstructive embedding (BRE) [17] explicitly minimizes the reconstruction error between the original distances and the Hamming distances of the corresponding hash codes, which avoids the restrictions of data distribution assumptions. Another potential problem in SH is that the construction of similarity matrix is prohibitively time-consuming, limiting its application in large-scale data sets. To overcome this drawback, some efficient graph construction hashing methods [12], [27] have been proposed, which construct an anchor graph with asymmetric similarity metric. Nevertheless, unsupervised hashing methods fail to achieve satisfactory performance due to the lack of semantic information, hindering their applications in practice. As indicated in [43], high-level semantic description of an object often differs from the extracted low-level feature descriptors. To make full use of label information of data, various supervised hashing methods have been proposed [10], [11], [32], which achieve superior retrieval performance than unsupervised methods. These methods attempt to learn semantic-sensitivity hash codes guided by manually-annotated labels. For example, supervised discrete hashing (SDH) [32] aims to generate the optimal binary codes for linear classification, which keeps the discrete constraint in optimization and utilizes full semantic information for discriminative binary code learning. To enlarge the regression boundary and exploit more precise regression target, supervised discrete hashing with relaxation (SDHR) [11] learns the discriminative hash codes and regression targets simultaneously in a joint optimization objective. Being different from these methods which regress binary codes into data labels, fast supervised discrete hashing (FSDH) [10] regresses the data labels into binary codes, achieving faster training speed and better retrieval performance. More recently, the supervised robust discrete multimodal hashing [19] and supervised discrete multi-view hashing [28] have been proposed, which extend SDH into the cross-media scenario and FSDH into the multi-view application, respectively. The above-mentioned supervised hashing methods can obtain optimal hash codes that are penalized by a discrete constraint. Nevertheless, these methods usually encode images by hand-crafted features. As a result, they lack the feature learning ability to generate high-quality binary codes. Inspired by great advances in deep learning, many deep hashing methods [4], [15], [20], [21], [22], [31] have been proposed to perform feature learning and hashing function learning simultaneously in an end-to-end framework. The representative methods include deep pairwise supervised hashing (DPSH) [22], deep asymmetric pairwise hashing (DAPH) [31], deep supervised discrete hashing (DSDH) [21], deep joint semantic-embedding hashing (DSEH) [20], and deep anchor graph hashing (DAGH) [4]. Generally speaking, these methods utilize a negative log-likelihood-based similarity preserving loss to learn deep hashing functions. However, such loss faces the problem of imbalance data since there are far fewer similar pairs than dissimilar pairs [1]. In addition to this limitation, there are two more potential drawbacks in existing deep hashing methods. Firstly, due to the non-differentiable sign function, deep hashing methods directly remove the discrete constraint during network training, and obtain real-valued network outputs as the approximate binary codes. In oder to reduce information loss, they typically penalize the distance between each real-valued embedding and its corresponding binary code (which is generally referred to as quantization). Nevertheless, such one-to-one quantization strategy only consider the quantization error for each individual sample rather than class-related samples, leading to a class-unrelated quantization problem. Secondly, in the process of binary code learning, these methods put undue emphasis on intra-class compactness while ignoring inter-class separability of data. As a result, the semantic knowledge of data is not fully utilized to learn semantic-sensitive binary codes. Motivated by these issues, we attempt to develop a novel quantization strategy to minimize the distances between each binary code and all its class-related real-valued embeddings (as illustrated in Fig. 1). Additionally, we aim to design an effective label mechanism to characterize both the intra-class and inter-class information of data. We hope that the proposed method can make the binary codes of the same class to be similar and the binary codes of different classes far away from each other. To achieve these goals, this paper proposes a novel deep supervised hashing framework called dual semantic asymmetric hashing (DSAH), which jointly conducts dual semantic regression to integrate both the intra-class and inter-class information of data, and class structure quantization to minimize the gap between each hash code and all its class-related embeddings. Being different from previous methods which use likelihood-based similarity preserving loss, DSAH adopts a novel similarity preserving loss to utilize semantic knowledge of data. Specifically, this loss minimizes the distances between class-related real-valued embeddings via an affinity graph that is pre-constructed from label information of data. The main contributions of this paper can be summarized as follows: • We propose a novel deep supervised hashing framework called DSAH, which adopts an interesting similarity preserving strategy based on distance-similarity product function. Such strategy minimizes the product of data distance calculated from Hamming space and data similarity derived from input space. • DSAH considers the quantization errors between each hash code and all its class-related real-valued embeddings. It subtly incorporates class prior of data into the quantization stage, thereby providing discriminative feedback for network training. • To learn semantic-sensitive binary codes, a dual semantic regression loss supported by a simple yet effective label mechanism is well designed to characterize the intra-class compactness and inter-class separability of data. • Extensive experiments show that the proposed method provides much superior generalization of network than existing state-of-the-art deep hashing methods. The rest of this paper is organized as follows. The related works are briefly introduced in Section 2. Section 3 shows the framework of the proposed DSAH and provides the corresponding optimization algorithm. Extensive experiments are presented in Section 4. Section 5 gives the conclusions and our future work. Section snippets Related Works In this section, we briefly review some related hashing approaches, including supervised hashing, deep hashing, and asymmetric hashing. The notions used in this paper and their corresponding descriptions are listed in Table 1 for ease of reading. Dual Semantic Asymmetric Hashing This sections gives the overall framework of the proposed DSAH and designs an iterative algorithm to optimize its objective function. Then, we show how to solve the out-of-sample extension problem and provide an accelerated version for the proposed DSAH. Experiments For comparison, seven traditional hashing methods including LSH [6], SH [39], BRE [17], ITQ [8], SP [40], SDH [32], FSDH [10], and six deep hashing methods including DPSH [22], DAPH [31], DDSH [15], ADSH [16], DAGH [4], and DSDH [21] are selected in our experiments. Conclusion This paper proposes a novel deep hashing framework called DSAH, which consists of three components including dual semantic regression, pairwise similarity preserving, and class structure quantization. The proposed DSAH adopts a distance-similarity product function for data similarity preserving and incorporates class structure prior into the quantization process, thereby providing discriminative feedback for network training. The dual semantic labels are designed to characterize the intra-class CRediT authorship contribution statement Jianglin Lu: Conceptualization, Software, Formal analysis, Investigation, Writing - original draft, Writing - review & editing. Hailing Wang: Methodology, Validation, Investigation, Writing - review & editing. Jie Zhou: Writing - review & editing, Supervision, Funding acquisition. Mengfan Yan: Conceptualization, Writing - original draft. Jiajun Wen: Supervision, Funding acquisition. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments This work was supported in part by the National Natural Science Foundation of China (No. 62076164), in part by the Guangdong Basic and Applied Basic Research Foundation (No. 2021A1515011318, 2021A1515011861), and in part by Shenzhen Science and Technology Program (No. JCYJ20210324094601005). References (42) W. Wang et al. Sparse graph based self-supervised hashing for scalable image retrieval Inf. Sci. (2021) Y. Shen et al. DSRPH: deep semantic-aware ranking preserving hashing for efficient multi-label image retrieval Inf. Sci. (2020) Y. Li et al. GLDH: toward more efficient global low-density locality-sensitive hashing for high dimensions Inf. Sci. (2020) Z. Cao et al. HashNet: deep learning to hash by continuation K. Chatfield, K. Simonyan, A. Vedaldi, A. Zisserman, Return of the devil in the details: delving deep into... W. Chen et al. Compressing neural networks with the hashing trick Y. Chen et al. Deep supervised hashing with anchor graph C. Da et al. Nonlinear asymmetric multi-valued hashing IEEE Trans. Pattern Anal. Mach. Intell. (2019) M. Datar et al. Locality-sensitive hashing scheme based on p-stable distributions W. Dong et al. Asymmetric distance estimation with sketches for similarity search in high-dimensional spaces Y. Gong et al. Iterative quantization: a procrustean approach to learning binary codes for large-scale image retrieval IEEE Trans. Pattern Anal. Mach. Intell. (2013) A. Gordo et al. Asymmetric distances for binary embeddings IEEE Trans. Pattern Anal. Mach. Intell. (2014) J. Gui et al. Fast supervised discrete hashing IEEE Trans. Pattern Anal. Mach. Intell. (2018) J. Gui et al. Supervised discrete hashing with relaxation IEEE Trans. Neural Networks Learn. Syst. (2018) D. Hu et al. Discrete spectral hashing for efficient similarity retrieval IEEE Trans. Image Process. (2019) P. Hu, X. Wang, L. Zhen, D. Peng, Separated variational hashing networks for cross-modal retrieval, in: Proc. 27th ACM... M. Jagadeesan Understanding sparse JL for feature hashing Q.-Y. Jiang et al. Deep discrete supervised hashing IEEE Trans. Image Process. (2018) Q.-Y. Jiang et al. Asymmetric deep supervised hashing B. Kulis et al. Learning to hash with binary reconstructive embeddings V.D.M. Laurens et al. Visualizing data using t-SNE J. Mach. Learn. Res. (2008) View more references Cited by (0) Recommended articles (6) <sup> 1 </sup> Equal contribution. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2022 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.12.112", "PubYear": 2022, "Volume": "589", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, China;SZU Branch, Shenzhen Institute of Artificial Intelligence and Robotics for Society, Shenzhen 518060, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Software, Tianjin University, Tianjin 300350, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, China;SZU Branch, Shenzhen Institute of Artificial Intelligence and Robotics for Society, Shenzhen 518060, China;Corresponding author College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering and Computer Science, Australian National University, Canberra, ACT 0200, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, China"}], "References": [{"Title": "A General Framework for Deep Supervised Discrete Hashing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "8-9", "Page": "2204", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "GLDH: Toward more efficient global low-density locality-sensitive hashing for high dimensions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "533", "Issue": "", "Page": "43", "JournalTitle": "Information Sciences"}, {"Title": "DSRPH: Deep semantic-aware ranking preserving hashing for efficient multi-label image retrieval", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "539", "Issue": "", "Page": "145", "JournalTitle": "Information Sciences"}, {"Title": "Sparse graph based self-supervised hashing for scalable image retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "622", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 92378189, "Title": "Dynamic cyber risk estimation with competitive quantile autoregression", "Abstract": "The increasing value of data held in enterprises makes it an attractive target to attackers. The increasing likelihood and impact of a cyber attack have highlighted the importance of effective cyber risk estimation. We propose two methods for modelling Value-at-Risk (VaR) which can be used for any time-series data. The first approach is based on Quantile Autoregression (QAR), which can estimate VaR for different quantiles, i. e. confidence levels. The second method, we term Competitive Quantile Autoregression (CQAR), dynamically re-estimates cyber risk as soon as new data becomes available. This method provides a theoretical guarantee that it asymptotically performs as well as any QAR at any time point in the future. We show that these methods can predict the size and inter-arrival time of cyber hacking breaches by running coverage tests. The proposed approaches allow to model a separate stochastic process for each significance level and therefore provide more flexibility compared to previously proposed techniques. We provide a fully reproducible code used for conducting the experiments.", "Keywords": "Cyber risk; Dynamic risk estimation; Time-series; Quantile Autoregression; Competitive prediction; Cyber breach modelling", "DOI": "10.1007/s10618-021-00814-z", "PubYear": 2022, "Volume": "36", "Issue": "2", "JournalId": 3928, "JournalTitle": "Data Mining and Knowledge Discovery", "ISSN": "1384-5810", "EISSN": "1573-756X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The Alan Turing Institute, London, United Kingdom;Department of Computer Science, Royal Holloway, University of London, London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The Alan Turing Institute, London, United Kingdom;WMG Cyber Security Centre, University of Warwick, London, United Kingdom"}], "References": []}, {"ArticleId": 92378281, "Title": "A Multigrid Multilevel Monte Carlo Method for Stokes–Darcy Model with Random Hydraulic Conductivity and <PERSON><PERSON>–<PERSON>", "Abstract": "<p>A multigrid multilevel Monte Carlo (MGMLMC) method is developed for the stochastic Stokes–Darcy interface model with random hydraulic conductivity both in the porous media domain and on the interface. Three interface conditions with randomness are considered on the interface between <PERSON> and <PERSON> equations, especially the <PERSON><PERSON>–<PERSON><PERSON><PERSON> interface condition with random hydraulic conductivity. Because the randomness through the interface affects the flow in the Stokes domain, we investigate the coupled stochastic Stokes–Darcy model to improve the fidelity. Under suitable assumptions on the random coefficient, we prove the existence and uniqueness of the weak solution of the variational form. To construct the numerical method, we first adopt the Monte Carlo (MC) method and finite element method, for the discretization in the probability space and physical space, respectively. In order to improve the efficiency of the classical single-level Monte Carlo (SLMC) method, we adopt the multilevel Monte Carlo (MLMC) method to dramatically reduce the computational cost in the probability space. A strategy is developed to calculate the number of samples needed in MLMC method for the stochastic Stokes–Darcy model. In order to accomplish the strategy for MLMC method, we also present a practical method to determine the variance convergence rate for the stochastic Stokes–Darcy model with <PERSON><PERSON> interface condition. Furthermore, MLMC method naturally provides the hierarchical grids and sufficient information on these grids for multigrid (MG) method, which can in turn improve the efficiency of MLMC method. In order to fully make use of the dynamical interaction between this two methods, we propose a multigrid multilevel Monte Carlo (MGMLMC) method with finite element discretization for more efficiently solving the stochastic model, while additional attention is paid to the interface and the random <PERSON>s–<PERSON>sph interface condition. The computational cost of the proposed MGMLMC method is rigorously analyzed and compared with the SLMC method. Numerical examples are provided to verify and illustrate the proposed method and the theoretical conclusions.</p>", "Keywords": "Stochastic Stokes–<PERSON> interface model; <PERSON><PERSON><PERSON> interface condition; Multilevel Monte Carlo; Multigrid method; Finite elements; 35R60; 65C05; 65M60; 76S05", "DOI": "10.1007/s10915-021-01742-2", "PubYear": 2022, "Volume": "90", "Issue": "2", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Southern University of Science and Technology, Shenzhen, People’s Republic of China;Division of Applied and Computational Mathematics, Beijing Computational Science Research Center, Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Statistics, Huazhong University of Science and Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Ningbo University, Ningbo, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, University of Electronic Science and Technology of China, Chengdu, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, Missouri University of Science and Technology, Rolla, USA"}], "References": [{"Title": "Adaptive Kriging Method for Uncertainty Quantification of the Photoelectron Sheath and Dust Levitation on the Lunar Surface", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "1", "Page": "", "JournalTitle": "Journal of Verification, Validation and Uncertainty Quantification"}]}, {"ArticleId": 92378309, "Title": "A Novel Lyapunov based Dynamic Resource Allocation for UAVs-assisted Edge Computing", "Abstract": "Mobile edge computing (MEC), as a key component in the development of IoT and 5G technologies, can provide extra computation resources in edge servers for mobile devices to complete their computation tasks with low latency and high reliability. Considerable efforts on computation offloading and resource allocation have been developed to reduce the energy consumption and computation latency in edge computing. Nonetheless, the system utility of heterogeneous edge computing system (e.g., UAVs-assisted edge computing), in which multiple unmanned aerial vehicles (UAVs) are involved in an edge computing system to serve as edge servers still needs to be further investigated. To this end, in this paper, we propose a novel Lyapunov based Dynamic Resource Allocation (LDRA) for UAVs-assisted Mobile Edge Computing, which can effectively choose suitable edge servers for mobile devices to offload and complete their computation tasks with low system cost and great system utility of UAVs-assisted edge computing system, as well as acceptable computation latency and great reliability for computation tasks of mobile devices. Particularly, a random queue model for edge servers is conducted in our LDRA scheme to support the dynamic of offloaded computation tasks of mobile devices. Additionally, a system cost model of UAVs-assisted edge computing is developed considering the combination of multiple constraints, such as both the mobility of UAVs and mobile devices, energy consumption, communication cost, etc. With the objective of minimizing the system cost and maximizing the system utility in providing edge resources to complete the offloaded computation tasks of mobile devices, by introducing <PERSON><PERSON><PERSON><PERSON> optimization, a dynamic resource allocation scheme is proposed to effectively determine edge servers to offload tasks of mobile devices with considering both the real-time execution state of offloaded tasks in edge servers and states of the communication link. Through analysis and performance evaluations, our results show that our proposed LDRA scheme can achieve a great balance between system cost and system stability. Additionally, our results also demonstrate that our LDRA scheme also can achieve better system utility in comparison with existing schemes.", "Keywords": "UAVs-assisted edge computing ; Dynamic resource allocation ; Lyapunov optimization ; System cost and utility", "DOI": "10.1016/j.comnet.2021.108710", "PubYear": 2022, "Volume": "205", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Qingdao University, Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xi’an Jiaotong University, Xi’an, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xi’an Jiaotong University, Xi’an, China"}], "References": [{"Title": "Joint optimization of service chain caching and task offloading in mobile edge computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107142", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 92378310, "Title": "Online multimedia traffic classification from the QoS perspective using deep learning", "Abstract": "In the next generation of communication systems, data traffic is expected to increase dramatically and continuously. Particularly for multimedia traffic, it has a dominant share in the increasing traffic. Therefore, there is an urgent need to develop an effective and accurate scheme to achieve online and automatic traffic management. To this end, this paper proposes an online multimedia traffic classification framework based on a Convolutional Neural Network (CNN), capable of conducting fast and early classification as well as class incremental learning. First, the sliding window technique is applied to capture the flow slices for further feature extraction. Then, the 3-dimensional flow representation is extracted based on the probability distribution function. After that, according to the specific structure of features, a deeply adapted structure of CNN is devised to better learn the knowledge from the representation. Besides, to better support the addition of new services, a class incremental learning model is developed with the techniques of knowledge distillation and bias correction to achieve continuous learning without retraining from scratch. Our experimental results reveal that the proposed method achieves faster and more accurate traffic classification compared with the state-of-the-art. Additionally, the deployed scheme using incremental learning achieves drops by about 50% in both time and memory consumptions compared with existing methods, while guaranteeing the accurate classification after adding new classes.", "Keywords": "Multimedia traffic classification ; Convolutional Neural Network ; Class incremental learning ; Sliding window", "DOI": "10.1016/j.comnet.2021.108716", "PubYear": 2022, "Volume": "204", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Telecommunications & Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing 210003, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Telecommunications & Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing 210003, China;Corresponding author;@njupt.edu.cn"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Telecommunications & Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing 210003, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Computing and Engineering Technologies, Swinburne University of Technology, Melbourne, Victoria 3122, Australia"}], "References": [{"Title": "Encrypted traffic classification based on Gaussian mixture models and Hidden Markov Models", "Authors": "Zhongjiang Yao; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "166", "Issue": "", "Page": "102711", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "TSCRNN: A novel classification scheme of encrypted traffic based on flow spatiotemporal features for efficient management of IIoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "190", "Issue": "", "Page": "107974", "JournalTitle": "Computer Networks"}, {"Title": "Self-attentive deep learning method for online traffic classification and its interpretability", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "196", "Issue": "", "Page": "108267", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 92378320, "Title": "The Zoomorphic Miro Robot’s Affective Expression Design and Perceived Appearance", "Abstract": "<p>This article proposes design guidelines for 11 affective expressions for the Miro robot, and evaluates the expressions through an online video study with 116 participants. All expressions were recognized significantly above the chance level. For six of the expressions, the correct response was selected significantly more than the others, while more than one emotion was associated to some other expressions. Design decisions and the robot's limitations that led to selecting other expressions, along with the correct expression, are discussed. We also investigated how participants' abilities to recognize human and animal emotions, their tendency to anthropomorphize, and their familiarity with and attitudes towards animals and pets might have influenced the recognition of the robot's affective expressions. Results show significant impact of human emotion recognition, difficulty in understanding animal emotions, and anthropomorphism tendency on recognition of Robot's expressions. We did not find such effects regarding familiarity with/attitudes towards animals/pets in terms of how they influenced participants' recognition of the designed affective expressions. We further studied how the robot is perceived in general and showed that it is mostly perceived to be gender neutral, and, while it is often associated with a dog or a rabbit, it can also be perceived as a variety of other animals.</p><p>© The Author(s), under exclusive licence to Springer Nature B.V. 2021.</p>", "Keywords": "Affective expression;Emotion;Emotionally intelligent robot;Miro;Social robot;Zoomorphic robot", "DOI": "10.1007/s12369-021-00832-3", "PubYear": 2022, "Volume": "14", "Issue": "4", "JournalId": 5388, "JournalTitle": "International Journal of Social Robotics", "ISSN": "1875-4791", "EISSN": "1875-4805", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Waterloo, Waterloo, ON Canada."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Adaptive Systems Research Group, University of Hertfordshire, Hatfield, UK."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departments of Electrical and Computer Engineering, and Systems Design Engineering, University of Waterloo, Waterloo, ON Canada."}], "References": []}, {"ArticleId": 92378337, "Title": "Querying graph databases using context-free grammars", "Abstract": "Path queries are used to specify paths inside a data graph to match a given pattern. Query languages such as SPARQL usually include support for regular path patterns defined by means of regular expressions. Context-free path queries define a path whose language can be defined by a context-free grammar. This kind of query is interesting in practice in domains such as genetics, data science, and source code analysis. In this paper, we present an algorithm for context-free path query processing. Our algorithm works by looking for localized paths, allowing us to process subgraphs, in contrast to other approaches that have to process the whole graph. It also takes any context-free grammar as input, avoiding the use of normal forms that can be problematic. The grammar normalization process may introduce a large number of non-terminal symbols and production rules, what, in general, reflects on more runtime and memory consumption by evaluation algorithms. We prove the correctness of our approach and show its runtime and memory complexity. We show the viability of our approach by means of prototypes implemented in Go and Python. We run experiments proposed in recent works, which include both synthetic and real RDF databases, and introduce a more realistic scenario inspired in Biology. Our algorithm shows performance gains when compared to other algorithms implemented using single-thread programs.", "Keywords": "Context-free grammars ; Graph path queries ; RDF", "DOI": "10.1016/j.cola.2021.101089", "PubYear": 2022, "Volume": "68", "Issue": "", "JournalId": 59906, "JournalTitle": "Journal of Computer Languages", "ISSN": "2665-9182", "EISSN": "2590-1184", "Authors": [{"AuthorId": 1, "Name": "Ciro M. <PERSON>", "Affiliation": "Federal University of Rio Grande do Norte, Computer Science Department–DIMAp, Natal, RN, Brazil;University of Orléans, Laboratoire d’Informatique Fondamentale d’Orléans - LIFO, Orléans, France;Corresponding author at: Federal University of Rio Grande do Norte, Computer Science Department–DIMAp, Natal, RN, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal University of Rio Grande do Norte, Computer Science Department–DIMAp, Natal, RN, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Costa", "Affiliation": "Federal University of Rio Grande do Norte, Computer Science Department–DIMAp, Natal, RN, Brazil"}], "References": [{"Title": "Learning graph-based heuristics for pointer analysis without handcrafting application-specific features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 92378353, "Title": "Informative and diverse emotional conversation generation with variational recurrent pointer-generator", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11704-021-0517-3", "PubYear": 2022, "Volume": "16", "Issue": "5", "JournalId": 4733, "JournalTitle": "Frontiers of Computer Science", "ISSN": "2095-2228", "EISSN": "2095-2236", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University, Shenyang, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University, Shenyang, China"}, {"AuthorId": 3, "Name": "Kaisong Song", "Affiliation": "Alibaba Group, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University, Shenyang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Li", "Affiliation": "School of Computer Science and Engineering, Northeastern University, Shenyang, China"}], "References": []}, {"ArticleId": 92378376, "Title": "DMH-FSL: Dual-Modal Hypergraph for Few-Shot Learning", "Abstract": "<p>Large scale labeled samples are expensive and difficult to obtain, hence few-shot learning (FSL), only needing a small number of labeled samples, is a dedicated technology. Recently, the graph-based FSL approaches have attracted lots of attention. It is helpful to model pair-wise relations among samples according to the similarity of features. However, the data in the reality usually have high-order relations, which can not be modeled by the traditional graph-based methods. To address this challenge, we introduce hypergraph structure and propose the Dual-Modal Hypergraph Few-Shot Learning (DMH-FSL) method to model the relations from different perspectives to model the high-order relations between samples. Specifically, we construct a dual-modal (e.g., feature-modal and label-modal) hypergraph, the feature-modal hypergraph construct incidence matrix with samples’ features and the label-modal hypergraph construct incidence matrix with samples’ labels. In addition, we employ two hypergraph convolution methods to perform flexible aggregation of samples from different modals. The proposed DMH-FSL method is easy to extend to other graph-based methods. We demonstrate the efficiency of our DMH-FSL method on three benchmark datasets. Our algorithm has at least an increase of 2.62% in Stanford40(from 72.20 to 74.82%), 0.85% in mini-ImageNet(from 50.33 to 51.18%) and 1.61% in USE-PPMI(from 78.77 to 80.38%) in few-shot learning experiments. What’s more, the cross-domain experimental results evaluate our method’s adaptability in real-world applications to some extent.</p>", "Keywords": "Few-shot learning; Hypergraph; Dual-modal hypergraph; Incidence matrix; Cross-domain", "DOI": "10.1007/s11063-021-10684-7", "PubYear": 2022, "Volume": "54", "Issue": "2", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Control Science and Engineering, China University of Petroleum (East China), Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Control Science and Engineering, China University of Petroleum (East China), Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Haier Industrial Intelligence Institute Co., Ltd., Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Petroleum Engineering, China University of Petroleum (East China), Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Control Science and Engineering, China University of Petroleum (East China), Qingdao, China"}], "References": [{"Title": "Specialization in Hierarchical Learning Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "3", "Page": "2319", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 92378379, "Title": "Improving knowledge graph completion via increasing embedding interactions", "Abstract": "<p>Knowledge graphs usually consist of billions of triplet facts describing the real world. Although most of the existing knowledge graphs are huge in scale, they are still far from completion. As a result, varieties of knowledge graph embedding approaches have emerged, which have been proven to be an effective and efficient solution for knowledge graph completion. In this paper, we devise a novel knowledge graph embedding model named InterERP , which aims to improve model performance by increasing Inter actions between the embeddings of E ntities, R elations and relation P aths. Specifically, we first introduce the interaction matrix to obtain the interaction embeddings of entities and relations. Then, we employ the Inception network to learn the query embedding, which can further increase the interactions between entities and relations. Furthermore, we resort to logical rules to construct semantic relation paths and are committed to modeling the interactions between different relations in a relation path. The experimental results on four commonly used datasets, demonstrate that the proposed InterERP matches or outperforms the state-of-the-art approaches.</p>", "Keywords": "Knowledge graph completion; Interaction embeddings; Knowledge graph embedding; Inception network", "DOI": "10.1007/s10489-021-02947-6", "PubYear": 2022, "Volume": "52", "Issue": "8", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Weidong Li", "Affiliation": "School of Computer Science, Wuhan University, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Information Technology, Guangxi Normal University, Guangxi, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan, China"}], "References": [{"Title": "Knowledge graph based natural language generation with adapted pointer-generator networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "382", "Issue": "", "Page": "174", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": ********, "Title": "Giraffe kicking optimization algorithm provides efficient routing mechanism in the field of vehicular ad hoc networks", "Abstract": "<p>Deployment of vehicular ad hoc network (VANET) has drawn considerable attention in current times. Energy efficient designs and sensing coverage in networks are the critical issues. Scheming an optimal wake-up system that avoids awakening extra nodes is a very challenging problem. This nature-inspired algorithm known as Giraffe kicking optimization (GKO) helps to balance between exploitation and exploration then helps to awake minimum number of sensor nodes by using the kicking style of a mother giraffe and also help to improve the throughput and prolong the lifetime of the network. Energy efficient routing is a vital phenomenon in the field of VANET which is a part of Wireless Sensor Network (WSNs) are developed to collect the information and to impel them towards the cluster head and the base station. The hybrid C-means based GKO algorithm is useful for VANET to avoid a large amount of energy consumption triggered by the redundant sensor nodes. To provide quality of service, it is essential to awake the minimum number of sensor nodes to consume less energy in the network by using optimized clustering techniques. For this issue, here we have planned a hybrid C-means Giraffe optimization technique with a multi-fitness function used to reach efficient routing enactment in VANET. Then, the GKO is contrasted with some other popular nature-inspired algorithms which are widely used in the field of VANET. We have confirmed our projected procedure against performance parameters and two non-parametric tests, such as <PERSON> and <PERSON>’s test has been used to analyze the results.</p>", "Keywords": "Nature inspired algorithm; Routing; Giraffe kicking algorithm; Optimization; Clustering", "DOI": "10.1007/s12652-021-03519-9", "PubYear": 2022, "Volume": "13", "Issue": "8", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> University of Technology, Odisha, Burla, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NIT Warangal, Warangal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> University of Technology, Odisha, Burla, India"}], "References": [{"Title": "Hybrid opportunistic and position-based routing protocol in vehicular ad hoc networks", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "1593", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "HybTGR: a hybrid routing protocol based on topological and geographical information in vehicular ad hoc networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "1683", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An enhanced hybrid ant colony optimization routing protocol for vehicular ad-hoc networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "8", "Page": "3837", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Optimized data transmission scheme based on proper channel coordination used in vehicular ad hoc networks", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "2", "Page": "1107", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": ********, "Title": "A parameter-free approach to lossless summarization of fully dynamic graphs", "Abstract": "In large dynamic graphs, it is often impractical to store and process the entire graph. To contend with such graphs, lossless graph summarization is a compression technique that can provide a succinct graph representation without losing information. However, given a fully dynamic graph stream, the problem of lossless summarization is computationally and technically challenging. Although the state-of-the-art method performs well with respect to efficiency, its compression quality is usually unstable. This outcome occurs because it is a randomized algorithm and depends heavily on the pretuned parameters. In this paper, we propose a parameter-free approach to lossless summarization of a fully dynamic graph stream. In response to edge insertions and deletions, we first develop an incremental algorithm to maintain the summarization result by carefully exploring the influenced subgraph. Furthermore, we present a similarity-based algorithm to control the movement of vertices on the subgraph and thus guarantee the optimal result at each summarization update. To enhance the performance of our approach, we also propose two optimization techniques regarding candidate supernode refinement and destination supernode selection. The experimental results demonstrate that the proposed methods outperform the state-of-the-art methods by a large margin in terms of compression quality with comparable running time on the majority of datasets.", "Keywords": "", "DOI": "10.1016/j.ins.2021.12.116", "PubYear": 2022, "Volume": "589", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, China;Academy of Military Sciences PLA China, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hunan Province Key Laboratory of Industrial Internet Technology and Security, Changsha University, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, China"}], "References": [{"Title": "Reachability preserving compression for dynamic graph", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "520", "Issue": "", "Page": "232", "JournalTitle": "Information Sciences"}, {"Title": "Exploring cohesive subgraphs with vertex engagement and tie strength in bipartite graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "572", "Issue": "", "Page": "277", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 92378455, "Title": "Two-dimensional semi-nonnegative matrix factorization for clustering", "Abstract": "In this paper, we propose a new Semi-Nonnegative Matrix Factorization method for 2-dimensional (2D) data, named TS-NMF. It overcomes the drawback of existing methods that seriously damage the spatial information of the data by converting 2D data to vectors in a preprocessing step. In particular, projection matrices are sought under the guidance of building new data representations, such that the spatial information is retained and projections are enhanced by the goal of clustering, which helps construct optimal projection directions. Moreover, to exploit nonlinear structures of the data, manifold is constructed in the projected subspace, which is adaptively updated according to the projections and less afflicted with noise and outliers of the data and thus more representative in the projected space. Hence, seeking projections, building new data representations, and learning manifold are seamlessly integrated in a single model, which mutually enhance other and lead to a powerful data representation. Comprehensive experimental results verify the effectiveness of TS-NMF in comparison with several state-of-the-art algorithms, which suggests high potential of the proposed method for real world applications.", "Keywords": "Semi-nonnegative matrix factorization;clustering;two-dimensional", "DOI": "10.1016/j.ins.2021.12.098", "PubYear": 2022, "Volume": "590", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Kentucky, United States;Institute of Biomedical Informatics, University of Kentucky, United States"}], "References": [{"Title": "Robust principal component analysis: A factorization-based approach with linear complexity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "513", "Issue": "", "Page": "581", "JournalTitle": "Information Sciences"}, {"Title": "Kernel two-dimensional ridge regression for subspace clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107749", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 92378470, "Title": "A spatiotemporal multi-feature extraction framework for opinion mining", "Abstract": "With the rapid development of Internet technology and the explosive growth of digital text, opinion mining has become one of the important research hotspots in the field of natural language processing (NLP). In recent years, neural network based deep learning algorithms have been applied in the field of opinion mining. Considering the relation between temporal and spatial dimensions of text data and the characteristics of natural language itself, traditional deep learning algorithms cannot be comprehensive in the processing of fully feature extraction. In this paper, we propose a new deep learning framework for opinion mining, which includes a temporal feature extraction layer that consists of two layers of bidirectional simple recurrent unit (Bi-SRU) networks extracting features at the word and grammar levels; a semantic feature extraction layer that mainly contains a multi-head attention module; a spatial feature extraction layer with dilated convolution that is used to extract opinion preference features. The Internet movie database (IMDb) is used to verify the performance of the proposed framework. The experiment results show that the proposed framework can effectively improve the classification accuracy, whose performance is better than that of the compared algorithms.", "Keywords": "Opinion mining ; Deep learning ; Spatiotemporal feature extraction ; Machine learning", "DOI": "10.1016/j.neucom.2021.11.098", "PubYear": 2022, "Volume": "490", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "Tiankuo Li", "Affiliation": "School of Information Science and Engineering, Shandong University, Qingdao, China"}, {"AuthorId": 2, "Name": "Hongji Xu", "Affiliation": "School of Information Science and Engineering, Shandong University, Qingdao, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong University, Qingdao, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong University, Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong University, Qingdao, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong University, Qingdao, China;@163.com"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong University, Qingdao, China;@qq.com"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong University, Qingdao, China"}], "References": []}, {"ArticleId": 92378494, "Title": "Bridging Composite and Real: Towards End-to-End Deep Image Matting", "Abstract": "<p>Extracting accurate foregrounds from natural images benefits many downstream applications such as film production and augmented reality. However, the furry characteristics and various appearance of the foregrounds, e.g., animal and portrait, challenge existing matting methods, which usually require extra user inputs such as trimap or scribbles. To resolve these problems, we study the distinct roles of semantics and details for image matting and decompose the task into two parallel sub-tasks: high-level semantic segmentation and low-level details matting. Specifically, we propose a novel Glance and Focus Matting network (GFM), which employs a shared encoder and two separate decoders to learn both tasks in a collaborative manner for end-to-end natural image matting. Besides, due to the limitation of available natural images in the matting task, previous methods typically adopt composite images for training and evaluation, which result in limited generalization ability on real-world images. In this paper, we investigate the domain gap issue between composite images and real-world images systematically by conducting comprehensive analyses of various discrepancies between the foreground and background images. We find that a carefully designed composition route RSSN that aims to reduce the discrepancies can lead to a better model with remarkable generalization ability. Furthermore, we provide a benchmark containing 2,000 high-resolution real-world animal images and 10,000 portrait images along with their manually labeled alpha mattes to serve as a test bed for evaluating matting model’s generalization ability on real-world images. Comprehensive empirical studies have demonstrated that GFM outperforms state-of-the-art methods and effectively reduces the generalization error. The code and the datasets will be released at https://github.com/JizhiziLi/GFM .</p>", "Keywords": "Image matting; Deep learning; Alpha matte; Image composition; Domain gap", "DOI": "10.1007/s11263-021-01541-0", "PubYear": 2022, "Volume": "130", "Issue": "2", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Faculty of Engineering, The University of Sydney, Darlington, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Faculty of Engineering, The University of Sydney, Darlington, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information System, Birkbeck College, University of London, London, UK"}, {"AuthorId": 4, "Name": "Dacheng Tao", "Affiliation": "School of Computer Science, Faculty of Engineering, The University of Sydney, Darlington, Australia"}], "References": [{"Title": "Recursive Context Routing for Object Detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "1", "Page": "142", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Towards High Performance Human Keypoint Detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "9", "Page": "2639", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": ********, "Title": "Action2video: Generating Videos of Human 3D Actions", "Abstract": "<p>We aim to tackle the interesting yet challenging problem of generating videos of diverse and natural human motions from prescribed action categories. The key issue lies in the ability to synthesize multiple distinct motion sequences that are realistic in their visual appearances. It is achieved in this paper by a two-step process that maintains internal 3D pose and shape representations, action2motion and motion2video . Action2motion stochastically generates plausible 3D pose sequences of a prescribed action category, which are processed and rendered by motion2video to form 2D videos. Specifically, the Lie algebraic theory is engaged in representing natural human motions following the physical law of human kinematics; a temporal variational auto-encoder is developed that encourages diversity of output motions. Moreover, given an additional input image of a clothed human character, an entire pipeline is proposed to extract his/her 3D detailed shape, and to render in videos the plausible motions from different views. This is realized by improving existing methods to extract 3D human shapes and textures from single 2D images, rigging, animating, and rendering to form 2D videos of human motions. It also necessitates the curation and reannotation of 3D human motion datasets for training purpose. Thorough empirical experiments including ablation study, qualitative and quantitative evaluations manifest the applicability of our approach, and demonstrate its competitiveness in addressing related tasks, where components of our approach are compared favorably to the state-of-the-arts.</p>", "Keywords": "3D human motion generation; Video motion synthesis; Lie algebraic human representation; Temporal variational autoencoders", "DOI": "10.1007/s11263-021-01550-z", "PubYear": 2022, "Volume": "130", "Issue": "2", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Alberta, Edmonton, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Alberta, Edmonton, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Alberta, Edmonton, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Tsinghua University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Alberta, Edmonton, Canada"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Guelph, Guelph, Canada"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Alberta, Edmonton, Canada"}], "References": [{"Title": "Modeling Human Motion with Quaternion-Based Neural Networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "4", "Page": "855", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Generating Human Action Videos by Coupling 3D Game Engines and Probabilistic Graphical Models", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "5", "Page": "1505", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Text2Sign: Towards Sign Language Production Using Neural Machine Translation and Generative Adversarial Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "4", "Page": "891", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 92378525, "Title": "Evaluating user susceptibility to phishing attacks", "Abstract": "Purpose \nPhishing is a well-known cybersecurity attack that has rapidly increased in recent years. It poses risks to businesses, government agencies and all users due to sensitive data breaches and subsequent financial losses. To study the user side, this paper aims to conduct a literature review and user study.\n \n \n Design/methodology/approach \nTo investigate phishing attacks, the authors provide a detailed overview of previous research on phishing techniques by conducting a systematic literature review of n = 367 peer-reviewed academic papers published in ACM Digital Library. Also, the authors report on an evaluation of a high school community. The authors engaged 57 high school students and faculty members (12 high school students, 45 staff members) as participants in research using signal detection theory (SDT).\n \n \n Findings \nThrough the literature review which goes back to as early as 2004, the authors found that only 13.9% of papers focused on user studies. In the user study, through scenario-based analysis, participants were tasked with distinguishing phishing e-mails from authentic e-mails. The results revealed an overconfidence bias in self-detection from the participants, regardless of their technical background.\n \n \n Originality/value \nThe authors conducted a literature review with a focus on user study which is a first in this field as far the authors know. Additionally, the authors conducted a detailed user study with high school students and faculty using SDT which is also an understudied area and population.", "Keywords": "Phishing,<PERSON><PERSON> phishing,High school,User study,Usable security,Authentication,Literature review,Information security,Computer security,Security,Computer users,Data security,Internet security", "DOI": "10.1108/ICS-12-2020-0204", "PubYear": 2022, "Volume": "30", "Issue": "1", "JournalId": 31004, "JournalTitle": "Information and Computer Security", "ISSN": "2056-4961", "EISSN": "2056-497X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Denver , Denver, Colorado, USA and Department of Informatics, Indiana University Bloomington , Bloomington, Indiana, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, Indiana University Bloomington , Bloomington, Indiana, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Informatics, Indiana University Bloomington , Bloomington, Indiana, USA"}], "References": []}, {"ArticleId": 92378539, "Title": "Simultaneous retrieval of selected optical water quality indicators from Landsat-8, Sentinel-2, and Sentinel-3", "Abstract": "Constructing multi-source satellite-derived water quality (WQ) products in inland and nearshore coastal waters from the past, present, and future missions is a long-standing challenge. Despite inherent differences in sensors’ spectral capability, spatial sampling, and radiometric performance, research efforts focused on formulating, implementing, and validating universal WQ algorithms continue to evolve. This research extends a recently developed machine-learning (ML) model, i.e., Mixture Density Networks (MDNs) ( <PERSON><PERSON><PERSON><PERSON> et al., 2020 ; <PERSON> et al., 2021 ), to the inverse problem of simultaneously retrieving WQ indicators, including chlorophyll- a (Chl a ), Total Suspended Solids (TSS), and the absorption by Colored Dissolved Organic Matter at 440 nm ( a <sub>cdom</sub>(440)), across a wide array of aquatic ecosystems. We use a database of in situ measurements to train and optimize MDN models developed for the relevant spectral measurements (400–800 nm) of the Operational Land Imager (OLI), MultiSpectral Instrument (MSI), and Ocean and Land Color Instrument (OLCI) aboard the Landsat-8, Sentinel-2, and Sentinel-3 missions, respectively. Our two performance assessment approaches, namely hold-out and leave-one-out, suggest significant, albeit varying degrees of improvements with respect to second-best algorithms, depending on the sensor and WQ indicator (e.g., 68%, 75%, 117% improvements based on the hold-out method for Chl a , TSS, and a <sub>cdom</sub>(440), respectively from MSI-like spectra). Using these two assessment methods, we provide theoretical upper and lower bounds on model performance when evaluating similar and/or out-of-sample datasets. To evaluate multi-mission product consistency across broad spatial scales, map products are demonstrated for three near-concurrent OLI, MSI, and OLCI acquisitions. Overall, estimated TSS and a <sub>cdom</sub>(440) from these three missions are consistent within the uncertainty of the model, but Chl a maps from MSI and OLCI achieve greater accuracy than those from OLI. By applying two different atmospheric correction processors to OLI and MSI images, we also conduct matchup analyses to quantify the sensitivity of the MDN model and best-practice algorithms to uncertainties in reflectance products. Our model is less or equally sensitive to these uncertainties compared to other algorithms. Recognizing their uncertainties, MDN models can be applied as a global algorithm to enable harmonized retrievals of Chl a , TSS, and a <sub>cdom</sub>(440) in various aquatic ecosystems from multi-source satellite imagery. Local and/or regional ML models tuned with an apt data distribution (e.g., a subset of our dataset) should nevertheless be expected to outperform our global model.", "Keywords": "Machine learning ; Water quality ; Inland and coastal waters ; OLI ; MSI ; OLCI", "DOI": "10.1016/j.rse.2021.112860", "PubYear": 2022, "Volume": "270", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "NASA Goddard Space Flight Center, Greenbelt, MD, USA;Science Systems and Applications, Inc. (SSAI), Lanham, MD, USA;Corresponding author at: NASA Goddard Space Flight Center, Greenbelt, MD, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "NASA Goddard Space Flight Center, Greenbelt, MD, USA;Science Systems and Applications, Inc. (SSAI), Lanham, MD, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tartu, Tartu, Estonia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Oceans and Atmosphere, Commonwealth Scientific and Industrial Research Organization (CSIRO), Canberra, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Instrumentation Lab for Aquatic Systems (LabISA), National Institute for Space Research (INPE), Sao Jose dos Campos, Brazil"}, {"AuthorId": 6, "Name": "Caren Binding", "Affiliation": "Environment and Climate Change Canada, Burlington, ON, Canada"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "National Research Council of Italy, Institute for Electromagnetic Sensing of the Environment, CNR-IREA, Italy"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "University of Sherbrooke, Department of Applied Geomatics, Sherbrooke, Québec, Canada"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "National Research Council of Italy, Institute for Electromagnetic Sensing of the Environment, CNR-IREA, Italy"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Wisconsin Department of Natural Resources, Madison, WI, USA"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "University of the Republic, Department of Geography, Montevideo 11400, Uruguay"}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Univ. Littoral Côte d’Opale, CNRS, Univ. Lille, IRD, UMR 8187 - LOG - Laboratoire d’Océanologie et de Géosciences, F-62930 Wimereux, France"}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Tartu, Tartu, Estonia"}, {"AuthorId": 14, "Name": "<PERSON><PERSON>", "Affiliation": "Xerra Earth Observation Institute and the University of Waikato, Hamilton, New Zealand"}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": "Univ. Littoral Côte d’Opale, CNRS, Univ. Lille, IRD, UMR 8187 - LOG - Laboratoire d’Océanologie et de Géosciences, F-62930 Wimereux, France"}, {"AuthorId": 16, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Tsukuba, Ibaraki, Japan"}, {"AuthorId": 17, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "VNU University of Science, Vietnam National University, Hanoi, Viet Nam"}, {"AuthorId": 18, "Name": "<PERSON><PERSON>", "Affiliation": "University of Minnesota, Department of Forest Resources, St. Paul, MN, USA"}, {"AuthorId": 19, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Sherbrooke, Department of Applied Geomatics, Sherbrooke, Québec, Canada"}, {"AuthorId": 20, "Name": "Stefan G.<PERSON>", "Affiliation": "Plymouth Marine Laboratory, Plymouth PL1 3DH, UK"}, {"AuthorId": 21, "Name": "<PERSON>", "Affiliation": "Great Lakes Environmental Research Laboratory, National Oceanic and Atmospheric Administration, Ann Arbor, MI, USA"}, {"AuthorId": 22, "Name": "<PERSON>", "Affiliation": "Univ. Littoral Côte d’Opale, CNRS, Univ. Lille, IRD, UMR 8187 - LOG - Laboratoire d’Océanologie et de Géosciences, F-62930 Wimereux, France"}, {"AuthorId": 23, "Name": "<PERSON>", "Affiliation": "University of Valencia, Laboratory for Earth Observation, Valencia, Spain"}], "References": [{"Title": "Remotely estimating total suspended solids concentration in clear to extremely turbid waters using a novel semi-analytical method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "258", "Issue": "", "Page": "112386", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Integration of in-situ and multi-sensor satellite observations for long-term water quality monitoring in coastal areas", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "239", "Issue": "", "Page": "111632", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Seamless retrievals of chlorophyll-a from Sentinel-2 (MSI) and Sentinel-3 (OLCI) in inland and coastal waters: A machine-learning approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111604", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Determining the drivers of suspended sediment dynamics in tidal marsh-influenced estuaries using high-resolution ocean color remote sensing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111682", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Robust algorithm for estimating total suspended solids (TSS) in inland and nearshore coastal waters", "Authors": "Sundarabalan <PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "246", "Issue": "", "Page": "111768", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A machine learning approach to estimate chlorophyll-a from Landsat-8 measurements in inland lakes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "248", "Issue": "", "Page": "111974", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Hyperspectral retrievals of phytoplankton absorption and chlorophyll-a in inland and nearshore coastal waters", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "253", "Issue": "", "Page": "112200", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Atmospheric correction of Sentinel-3/OLCI data for mapping of suspended particulate matter and chlorophyll-a concentration in Belgian turbid coastal waters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "256", "Issue": "", "Page": "112284", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "ACIX-Aqua: A global assessment of atmospheric correction methods for Landsat-8 and Sentinel-2 over lakes, rivers, and coastal waters", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>lan <PERSON><PERSON>", "PubYear": 2021, "Volume": "258", "Issue": "", "Page": "112366", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": ********, "Title": "Stochastic Entry Guidance", "Abstract": "In this paper, closed-loop entry guidance in a randomly perturbed atmosphere, using bank angle control, is posed as a stochastic optimal control problem. The entry trajectory and the closed-loop controls are both modeled as random processes with statistics determined by the entry dynamics, the entry guidance, and the probabilistic structure of altitude-dependent atmospheric density variations. The entry guidance, which is parameterized as a sequence of linear feedback gains, is designed to steer the probability distribution of the entry trajectories while satisfying bounds on the allowable control inputs and on the maximum allowable state errors. Numerical simulations of a Mars entry scenario demonstrate improved range targeting performance with approximately 50% lower 1st and 99th percentile final range errors when using the developed stochastic guidance scheme as compared to the existing Apollo final phase algorithm.", "Keywords": "Aerodynamic Coefficients; Stochastic Optimal Control; Atmospheric Density; Probability Distribution; Numerical Simulation; Earth; Flight Path Angle; Angle of Attack; Linear Quadratic Gaussian; Guidance Algorithms", "DOI": "10.2514/1.G005964", "PubYear": 2022, "Volume": "45", "Issue": "2", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, Georgia 30332"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, Georgia 30332"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NASA Johnson Space Center, Houston, Texas 77058"}], "References": []}, {"ArticleId": 92378647, "Title": "Swarm Intelligence inspired Intrusion Detection Systems — A systematic literature review", "Abstract": "An Intrusion Detection System (IDS) is one of the fundamental building blocks in securing a network. A huge number of techniques have been proposed and implemented to improve the performance and accuracy of intrusion detection models. In recent years, efforts have been made to keep the attack surface as small as possible. However, the attack vectors have evolved in terms of complexity and diversity, and various intelligent techniques have been employed by the adversaries to exploit the ecosystems. This exploitation can either make the whole system dysfunctional or may lead to important information leakage. More specifically, with the evolution of Internet of Things (IoT), more heterogeneous and resource-constrained devices are interconnecting with each other. These devices have limited processing power and resources, particularly for detecting intrusions. Consequently, Swarm Intelligence (SI) based techniques have received considerable attention, especially in the past decade, as the SI approaches have achieved a decent success rate by optimizing various aspects of an IDS. This paper presents a systematic review with a thorough coverage of articles published between 2010 and 2020 of the state-of-the-art swarm intelligence approaches deployed in various attack surfaces for intrusion detection in various domains. The paper also presents a categorization in accordance with applicability of these SI approaches in improving various aspects of an intrusion detection process. Moreover, the paper also discusses the capabilities and features of various datasets used in experimentation. This aims to help researchers in assessing the capabilities and limitations of SI algorithms to identify security threats and challenges to design and implement an IDS for the detection of cyber attacks in various domains. Moreover, this will also help security individuals in differentiating SI based IDS with traditional ones. As such, the survey would be equally beneficial for the researchers working in the domain of swarm intelligence as well as cybersecurity. The survey highlights certain existing challenges and provides directions to address them effectively. In addition, new research directions are also identified.", "Keywords": "Swarm intelligence ; Intrusion Detection ; Network security ; Data security", "DOI": "10.1016/j.comnet.2021.108708", "PubYear": 2022, "Volume": "205", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science & IT, NED University of Engineering & Technology Karachi, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computing & Information Sciences, Karachi Institute of Economics & Technology, Pakistan;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science & IT, NED University of Engineering & Technology Karachi, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Software Engineering, Bahria University Karachi Campus, Pakistan"}], "References": [{"Title": "Intrusion detection system based on a modified binary grey wolf optimisation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Zakaria N. M<PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "10", "Page": "6125", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Training a Neural Network for Cyberattack Classification Applications Using Hybridization of an Artificial Bee Colony and Monarch Butterfly Optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "1", "Page": "905", "JournalTitle": "Neural Processing Letters"}, {"Title": "Role of swarm and evolutionary algorithms for intrusion detection system: A survey", "Authors": "<PERSON><PERSON><PERSON>; R<PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "100631", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "The rise of machine learning for detection and classification of malware: Research developments, trends and challenges", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "102526", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "An effect of chaos grasshopper optimization algorithm for protection of network infrastructure", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "107251", "JournalTitle": "Computer Networks"}, {"Title": "Towards trustworthy Internet of Things: A survey on Trust Management applications and schemes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "475", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 92378656, "Title": "Configuring ADAS Platforms for Automotive Applications Using Metaheuristics", "Abstract": "<p>Modern Advanced Driver-Assistance Systems (ADAS) combine critical real-time and non-critical best-effort tasks and messages onto an integrated multi-core multi-SoC hardware platform. The real-time safety-critical software tasks have complex interdependencies in the form of end-to-end latency chains featuring, e.g., sensing, processing/sensor fusion, and actuating. The underlying real-time operating systems running on top of the multi-core platform use static cyclic scheduling for the software tasks, while the communication backbone is either realized through PCIe or Time-Sensitive Networking (TSN). In this paper, we address the problem of configuring ADAS platforms for automotive applications, which means deciding the mapping of tasks to processing cores and the scheduling of tasks and messages. Time-critical messages are transmitted in a scheduled manner via the timed-gate mechanism described in IEEE 802.1Qbv according to the pre-computed Gate Control List (GCL) schedule. We study the computation of the assignment of tasks to the available platform CPUs/cores, the static schedule tables for the real-time tasks, as well as the GCLs, such that task and message deadlines, as well as end-to-end task chain latencies, are satisfied. This is an intractable combinatorial optimization problem. As the ADAS platforms and applications become increasingly complex, such problems cannot be optimally solved and require problem-specific heuristics or metaheuristics to determine good quality feasible solutions in a reasonable time. We propose two metaheuristic solutions, a Genetic Algorithm (GA) and one based on Simulated Annealing (SA), both creating static schedule tables for tasks by simulating Earliest Deadline First (EDF) dispatching with different task deadlines and offsets. Furthermore, we use a List Scheduling-based heuristic to create the GCLs in platforms featuring a TSN backbone. We evaluate the proposed solution with real-world and synthetic test cases scaled to fit the future requirements of ADAS systems. The results show that our heuristic strategy can find correct solutions that meet the complex timing and dependency constraints at a higher rate than the related work approaches, i.e., the jitter constraints are satisfied in over 6 times more cases, and the task chain constraints are satisfied in 41% more cases on average. Our method scales well with the growing trend of ADAS platforms.</p>", "Keywords": "automotive applications; task scheduling; Task preemption; Time-sensitive networking; TSN; IEEE 802.1Qbv", "DOI": "10.3389/frobt.2021.762227", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark Kongens Lyngby, Denmark"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark Kongens Lyngby, Denmark"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark Kongens Lyngby, Denmark"}, {"AuthorId": 4, "Name": "Silviu S. Craciunas", "Affiliation": "TTTech Computertechnik AG, Austria"}], "References": []}, {"ArticleId": 92378667, "Title": "Catalytic filters for metal oxide gas sensors", "Abstract": "Chemical sensors based on metal oxides (MOx) are most promising for emerging applications including medical breath analysis, distributed environmental monitoring and rapid food quality assessment. Yet, such sensors are not established in daily practice, mainly due to their limited selectivity, sensitivity and stability. Catalytic filters offer an effective solution to improve these by converting interferants to inactive species and/or target analytes to more responsive ones. This has been exploited successfully for alkane sensors, enabling their commercial utilization. Here, catalytic filters are discussed as promising tool to optimize the performance of chemoresistive MOx sensors. First, we provide an overview of chemical and physical parameters that govern the catalytic reactivity of such filters and we compare their implementation as overlayers and packed beds. Thereby, recent advances in the nanoscale design of suitable materials to finely tune their catalytic properties are elaborated. Next, filter solutions for analytes of various chemical families (including alkanes, alkenes, inorganics, alcohols, ketones and aromatics) are discussed and quantitatively compared also to other state-of-the-art detectors. Emphasis is placed on present challenging scenarios, for instance, the distinction of analytes from significantly higher concentrated interferants (e.g., breath markers in the presence of background ethanol in hospitals) or chemically similar compounds (e.g., benzene from xylene and toluene in air quality assessment). This is followed by examples demonstrating the integration of such filter-sensor concepts into devices and their evaluation under real conditions. Finally, opportunities and research frontiers are highlighted to inspire future research.", "Keywords": "Gas detection ; Separation ; Chemoresistors ; Nanotechnology ; Heterogeneous catalysis ; Devices", "DOI": "10.1016/j.snb.2021.131346", "PubYear": 2022, "Volume": "356", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Particle Technology Laboratory, Department of Mechanical and Process Engineering, ETH Zurich, CH-8092 Zurich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Endocrinology, Diabetes, and Clinical Nutrition, University Hospital Zurich and University of Zurich, CH-8091 Zurich, Switzerland;Corresponding author"}], "References": [{"Title": "A room-temperature methane sensor based on Pd-decorated ZnO/rGO hybrids enhanced by visible light photocatalysis", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127334", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Selective detection of methane by HZSM-5 zeolite/Pd-SnO2 gas sensors", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "321", "Issue": "", "Page": "128567", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A portable sensor system for the detection of human volatile compounds against transnational crime", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "328", "Issue": "", "Page": "129036", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Selective detection of methane by Pd-In2O3 sensors with a catalyst filter film", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "328", "Issue": "", "Page": "129030", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Glucose-assisted synthesis of hierarchical NiO-ZnO heterostructure with enhanced glycol gas sensing performance", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129167", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Highly selective, sensitive, and rapidly responding acetone sensor using ferroelectric ε-WO3 spheres doped with Nb for monitoring ketogenic diet efficiency", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "338", "Issue": "", "Page": "129823", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A selective methane gas sensor with printed catalytic films as active filters", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "347", "Issue": "", "Page": "130603", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 92378673, "Title": "Lightweight hierarchical residual feature fusion network for single-image super-resolution", "Abstract": "In recent years, numerous lightweight convolution neural networks (CNNs) have made remarkable progress for single image super-resolution (SISR) and showed great power for image reconstruction under constrained resources. However, existing lightweight networks can not fully utilize the informative hierarchical features, which will lead to the degradation of network reconstruction. To alleviate this issue, we propose a hierarchical residual feature network named HRFFN. Specifically, we design an enhanced residual block (ERB) containing multiple mixed attention blocks (MABs) to boost the representative ability of the network. Compared with ordinary residual blocks, ERB can achieve better performance while reducing network parameters and computational complexity. To utilize more features from intermediate convolution layers, we introduce a hierarchical feature fusion strategy (HFFS) to efficiently fuse the detailed information from each ERB step by step. By fully utilizing the hierarchical details with this strategy, we can refine the hierarchical features more efficiently. Besides, we cooperate the global dense connection strategy (GDCS) and residual learning connection (RLC, at low, meditate, and high levels) to construct our HRFFN. By employing these strategies, we can maximize the utilization of hierarchical features with a slight increase in parameters. Comprehensive experiments show the superiority of our method on five benchmark datasets against other state-of-the-art methods, which achieves a comparable trade-off between visual quality and quantitative metrics.", "Keywords": "Single image super-resolution ; Convolution neural network ; Lightweight network ; Hierarchical residual feature fusion", "DOI": "10.1016/j.neucom.2021.12.090", "PubYear": 2022, "Volume": "478", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, Sichuan 610065, China;School of Aeronautics & Astronautics, Sichuan University, Chengdu, Sichuan 610065, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, Sichuan 610065, China;School of Aeronautics & Astronautics, Sichuan University, Chengdu, Sichuan 610065, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Electrical Engineering, Sichuan University, Chengdu, Sichuan 610065, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Embedded Systems Engineering, Incheon National University, Incheon 22012, Republic of Korea;School of Electronic Engineering, Xidian University, Xi’an 710071, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, Sichuan 610065, China;School of Aeronautics & Astronautics, Sichuan University, Chengdu, Sichuan 610065, China;Corresponding author at: College of Electronics and Information Engineering, Sichuan University, Chengdu, Sichuan 610065, China"}], "References": [{"Title": "Lightweight multi-scale residual networks with attention for image super-resolution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106103", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A lightweight multi-scale channel attention network for image super-resolution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "456", "Issue": "", "Page": "327", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 92378777, "Title": "Performance analysis of multi-level high voltage direct current converter", "Abstract": "<p><p class=\"Abstract\">The conventional three-phase alternating current (AC) to direct current (DC) converter can be modified using two isolated-gate bipolar transistor (IGBT) as by-pass switches connected to tapping points on the secondary side of the transformer. This scheme yields a reduction in both harmonic contents and reactive volt-ampere absorption. This modified converter possibly eliminates the need for an on-load tap-changer on the converter transformer. The modified AC/DC converter is fully analyzed and implemented under balanced conditions using MATLAB-Simulink. The expressions of the output DC voltage are derived for different cases. The supply current harmonic contents, the reactive power absorption and the power factor have been compared for three schemes; the conventional bridge, the modified bridge using one by-pass IGBT valve and the modified bridge with two by-pass IGBT valves. </p></p>", "Keywords": "by-pass;characteristics harmonics;multi-level;reactive power", "DOI": "10.11591/ijece.v12i2.pp1368-1376", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Al Hussein Technical University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mu’tah University"}], "References": []}, {"ArticleId": 92378782, "Title": "Energy sink-holes avoidance method based on fuzzy system in wireless sensor networks", "Abstract": "<p>The existence of a mobile sink for gathering data significantly extends wireless sensor networks (WSNs) lifetime. In recent years, a variety of efficient rendezvous points-based sink mobility approaches has been proposed for avoiding the energy sink-holes problem nearby the sink, diminishing buffer overflow of sensors, and reducing the data latency. Nevertheless, lots of research has been carried out to sort out the energy holes problem using controllable-based sink mobility methods. However, further developments can be demonstrated and achieved on such type of mobility management system. In this paper, a well-rounded strategy involving an energy-efficient routing protocol along with a controllable-based sink mobility method is proposed to extirpate the energy sink-holes problem. This paper fused the fuzzy A-star as a routing protocol for mitigating the energy consumption during data forwarding along with a novel sink mobility method which adopted a grid partitioning system and fuzzy system that takes account of the average residual energy, sensors density, average traffic load, and sources angles to detect the optimal next location of the mobile sink. By utilizing diverse performance metrics, the empirical analysis of our proposed work showed an outstanding result as compared with fuzzy A-star protocol in the case of a static sink.</p>", "Keywords": "fuzzy a-star approach;mobile sink;network lifetime;routing protocol;wireless sensor networks", "DOI": "10.11591/ijece.v12i2.pp1776-1785", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Basrah"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Basrah"}], "References": []}, {"ArticleId": ********, "Title": "New artificial neural network design for Chua chaotic system prediction using FPGA hardware co-simulation", "Abstract": "<p><p>This study aims to design a new architecture of the artificial neural networks (ANNs) using the Xilinx system generator (XSG) and its hardware co-simulation equivalent model using field programmable gate array (FPGA) to predict the behavior of Chu<PERSON>’s chaotic system and use it in hiding information. The work proposed consists of two main sections. In the first section, MATLAB R2016a was used to build a 3×4×3 feed forward neural network (FFNN). The training results demonstrate that FFNN training in the Bayesian regulation algorithm is sufficiently accurate to directly implement. The second section demonstrates the hardware implementation of the network with the XSG on the Xilinx artix7 xc7a100t-1csg324 chip. Finally, the message was first encrypted using a dynamic Chua system and then decrypted using ANN’s chaotic dynamics. ANN models were developed to implement hardware in the FPGA system using the IEEE 754 Single precision floating-point format. The ANN design method illustrated can be extended to other chaotic systems in general.</p></p>", "Keywords": "Artificial neural network;<PERSON><PERSON>’s circuit;Floating point format;Hardware co-simulation encryption and decryption;Xilinx system generator", "DOI": "10.11591/ijece.v12i2.pp1955-1964", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "W<PERSON><PERSON> Adnan <PERSON>", "Affiliation": "Basrah University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Basrah University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Basrah University"}], "References": []}, {"ArticleId": 92378789, "Title": "Inductanceless high order low frequency filters for medical applications", "Abstract": "<p><p>In this paper, a designed circuit used for low-frequency filters is implemented and realized the filter is based on frequency-dependent negative resistance (FDNR) as an inductor simulator to substitute the traditional inductance, which is heavy and high cost due to the coil material manufacturing and size area. The simulator is based on an active operation amplifier or operation transconductance amplifier (OTA) that is easy to build in an integrated circuit with a minimum number of components. The third and higher-order Butterworth filter is simulated at low frequency for low pass filter to use in medical instruments and low-frequency applications. The designed circuit is compared with the traditional proportional integral controller enhanced (PIE) and T section ordinary filter. The results with magnitude and phase response were compared and an acceptable result is obtained. The filter can be used for general applications such as medical and other low-frequency filters needed.</p></p>", "Keywords": "embeded IC filter;frequency dependent negative resistance;inductancess filter;lowpass filter", "DOI": "10.11591/ijece.v12i2.pp1299-1307", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Mosul"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Mosul"}], "References": []}, {"ArticleId": 92378792, "Title": "A hybrid method for traumatic brain injury lesion segmentation", "Abstract": "<span>Traumatic brain injuries are significant effects of disability and loss of life. Physicians employ computed tomography (CT) images to observe the trauma and measure its severity for diagnosis and treatment. Due to the overlap of hemorrhage and normal brain tissues, segmentation methods sometimes lead to false results. The study is more challenging to unitize the AI field to collect brain hemorrhage by involving patient datasets employing CT scans images. We propose a novel technique free-form object model for brain injury CT image segmentation based on superpixel image processing that uses CT to analyzing brain injuries, quite challenging to create a high outstanding simple linear iterative clustering (SLIC) method. The maintains a strategic distance of the segmentation image to reduced intensity boundaries. The segmentation image contains marked red hemorrhage to modify the free-form object model. The contour labelled by the red mark is the output from our free-form object model. We proposed a hybrid image segmentation approach based on the combined edge detection and dilation technique features. The approach diminishes computational costs, and the show accomplished 96.68% accuracy. The segmenting brain hemorrhage images are achieved in the clustered region to construct a free-form object model. The study also presents further directions on future research in this domain.</span>", "Keywords": "edge detection;free-form object model;hybrid method;image segmentation;simple linear iterative clustering algorithm", "DOI": "10.11591/ijece.v12i2.pp1437-1448", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Chiang Mai University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chiang Mai University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Chiang Mai University"}], "References": []}, {"ArticleId": 92378793, "Title": "Bandwidth enhancement of dual-band bi-directional microstrip antenna using complementary split ring resonator with defected structure for 3/5 GHz applications", "Abstract": "<span>This paper presents a bandwidth enhancement of a dual-band bi-directional rectangular microstrip patch antenna. The novelty of this work lies in the modification of conventional rectangular microstip patch antenna by using the combination of two techniques: a complementary split ring resonator (CSRR) and a defected patch structure (DPS). The structure of antenna was studied and investigated via computer </span><span>simulation technology (CST). The dimension and position of CSRR on the ground plane was optimized to achieve dual bandwidth and bi-directional radiation pattern characteristics. In addition, the bandwidths were enhanced by defecting suitable shape incorporated in the microstrip patch. A prototype with overall dimension of 70.45×63.73 mm<sup>2</sup> has been fabricated on FR-4 substrate. To verify the proposed design, the impedance bandwidth, gain, and radiation patterns were carried out in measurements. The measured impedance bandwidths were respectively 560 MHz (3.08-3.64 GHz) and 950 GHz (4.64-5.59 GHz) while the measured gains of each bandwidth were respectively 4.28 dBi and 4.63 dBi. The measured radiation patterns were in good agreement with simulated ones. The proposed antenna achieves wide dual bandwidth and bi-directional radiation patterns performances. Consequently, it is a promising candidate for Wi-Fi or 5G communications in specific areas such as tunnel, corridor, or transit and rail.</span>", "Keywords": "bi-directional radiation pattern complementary split ring resonator;defected structure;dual-band antenna;microstrip antenna", "DOI": "10.11591/ijece.v12i2.pp1683-1694", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "Charernkiat Pochaiya", "Affiliation": "Walailak University"}, {"AuthorId": 2, "Name": "Srawouth Chandhaket", "Affiliation": "Walailak University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>t University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Silpakorn University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Walailak University"}], "References": []}, {"ArticleId": 92378808, "Title": "Fast reinforcement learning algorithms for joint adaptive source coding and transmission control in IoT devices with renewable energy storage", "Abstract": "<p>Energy harvesting and source coding are two key techniques that can be exploited to mitigate the device battery limitation in Internet of things (IoT). However, these mitigating techniques come with the expense of adding to the complexity of control and optimization in the digital communication chain. In this paper, to strike a balance between the opposing goals of energy-efficient communication, high-fidelity reconstruction at the IoT gateway, low packet drop ratio, and timeliness of data, we address the delay-constrained joint lossy compression and transmission control problem for an IoT device with rechargeable energy storage. Given the stochastic dynamics emanated from the random nature of the energy harvesting process as well as the fading channel, we formulate a stochastic optimization problem using the formalism of constrained Markov decision process (CMDP) and utilize the standard Lagrangian technique to recast the problem in the unconstrained form. To compute the optimal control policy, we propose a two-timescale stochastic approximation algorithm consisting of some reinforcement learning (RL) algorithms for estimating the CMDP’s value function and stochastic gradient descent for estimating the Lagrange multiplier. Specifically, we propose three RL procedures: one based on standard Q-learning, and two accelerated learning procedures, namely post-decision state (PDS) and virtual experience (VE) learning. These algorithms exploit the known system dynamics and batch updates to overcome the slowness caused by the asynchronous updating pattern in Q-learning. Simulation results demonstrate that the proposed PDS and VE learning algorithms speed up the convergence to the optimal control policy by one and two orders of magnitude, respectively.</p>", "Keywords": "Source coding; Data transmission; Internet of things; Reinforcement learning", "DOI": "10.1007/s00521-021-06656-6", "PubYear": 2022, "Volume": "34", "Issue": "5", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Farnoosh <PERSON>", "Affiliation": "School of Computer Engineering, Iran University of Science and Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering, Iran University of Science and Technology, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center of Excellence in Future Networks, School of Computer Engineering, Iran University of Science and Technology, Tehran, Iran"}], "References": [{"Title": "RETRACTED ARTICLE: CMDP-based intelligent transmission for wireless body area network in remote health monitoring", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "3", "Page": "829", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Energy efficient data transmission in WSN thru compressive slender penetrative etiquette", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "4681", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "AI for dynamic packet size optimization of batteryless IoT nodes: a case study for wireless body area sensor networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "20", "Page": "16167", "JournalTitle": "Neural Computing and Applications"}, {"Title": "RETRACTED ARTICLE: Soft computing approach based energy and correlation aware cooperative data collection for wireless sensor network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "5", "Page": "5297", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An optimal policy for joint compression and transmission control in delay-constrained energy harvesting IoT devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "554", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 92378946, "Title": "Telecom Fraud Detection with Big Data Analytics", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJDS.2021.10043956", "PubYear": 2021, "Volume": "6", "Issue": "3", "JournalId": 44182, "JournalTitle": "International Journal of Data Science", "ISSN": "2053-0811", "EISSN": "2053-082X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Duygu Sinanc Terzi", "Affiliation": ""}], "References": []}, {"ArticleId": 92379025, "Title": "Proteome dataset of peripheral blood mononuclear cells in postpartum dairy cows supplemented with different sources of omega-3 fatty acids", "Abstract": "This article contains raw and processed data related to research published by <PERSON><PERSON> et al. [1] . There is a scarce knowledge on the proteome of peripheral blood mononuclear cells (PBMC) during the transition period in dairy cows. In human research, proteomics PBMC is used in order to gain insight into inflammatory diseases and syndromes. Dietary fats, and specifically omega-3 (n-3) FA, can moderate the immune fluctuation caused by parturition through improvements of the immune function [2] . Therefore, this study aim was to characterize the changes that may occur in proteome of PBMC during transition, as influenced by different n-3 FA supplementation. Proteomics data of PBMC was obtained from postpartum dairy cows supplemented peripartum with either encapsulated saturated fat (CTL), encapsulated flaxseed oil that is enriched with ALA (α-linolenic acid; FLX) or encapsulated fish oil that is enriched with EPA (eicosapentaenoic acid) and DHA (docosahexaenoic acid; FO).The analysis was done by liquid chromatography-mass spectrometry from PBMCs protein extraction. The cells were collected from six cows per treatment during the 1<sup>st</sup> week postpartum. Quantification of differential abundance between groups was done using MS1 intensity based label-free. Label-free quantitative shotgun proteomics was used for characterization. This novel dataset of proteomics data from PBMC contains 3807 proteins; 44, 42 and 65 were differently abundant ( P ≤ 0.05 and FC ± 1.5), in FLX vs. CTL, FO vs. CTL and FLX vs. FO, respectively; these findings are discussed in our recent research article (Kra et al., 2021). The present dataset of PBMC proteome adds new information regarding the effects of n-3 FA on the immune system, while providing reference for PBMC proteome in postpartum dairy cows.", "Keywords": "Dairy cows ; Peripheral blood mononuclear cells ; Proteomic analysis ; Omega-3 fatty acids", "DOI": "10.1016/j.dib.2021.107785", "PubYear": 2022, "Volume": "40", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> K<PERSON>", "Affiliation": "Department of Ruminant Science, Institute of Animal Sciences, Agricultural Research Organization, Volcani Center, Israel;Department of Animal Science, The Robert <PERSON> Faculty of Agriculture, Food and Environment, The Hebrew University of Jerusalem, Rehovot 76100, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ruminant Science, Institute of Animal Sciences, Agricultural Research Organization, Volcani Center, Israel;Department of Animal Science, The Robert <PERSON> Faculty of Agriculture, Food and Environment, The Hebrew University of Jerusalem, Rehovot 76100, Israel"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Ruminant Science, Institute of Animal Sciences, Agricultural Research Organization, Volcani Center, Israel"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ruminant Science, Institute of Animal Sciences, Agricultural Research Organization, Volcani Center, Israel"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Ruminant Science, Institute of Animal Sciences, Agricultural Research Organization, Volcani Center, Israel"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> <PERSON> and <PERSON> Israel National Center for Personalized Medicine, Weizmann Institute of Science, Rehovot 7610001, Israel"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Ruminant Science, Institute of Animal Sciences, Agricultural Research Organization, Volcani Center, Israel"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ruminant Science, Institute of Animal Sciences, Agricultural Research Organization, Volcani Center, Israel;Corresponding author"}], "References": []}, {"ArticleId": 92379062, "Title": "A hybrid lattice Boltzmann model for simulating viscoelastic instabilities", "Abstract": "Low-Reynolds-number viscoelastic fluids exhibit a chaotic behaviour linked to the growth of elastic instabilities when the polymer molecules are stretched excessively. Despite offering various exciting benefits, the numerical difficulties associated with simulating this complex regime have left a lot still unexplored regarding the transition and onset of these low-Reynolds number viscoelastic instabilities. Recently, methods based on the lattice Boltzmann method (LBM) have emerged as a viable numerical tool for studying the behaviour of viscoelastic fluids largely due to the simplicity, adaptability, and intrinsic parallel features of LBM. However, extensive memory requirements and the inability to preserve numerical stability have restricted previous attempts from simulating viscoelastic instabilities. In this work, a hybrid lattice Boltzmann model is applied to simulate low-Reynolds number viscoelastic instabilities. In this approach, the hydrodynamic field is solved using a lattice Boltzmann model, whereas the polymer field is solved separately using a high-resolution finite difference scheme with the logarithmic Cholesky decomposition. The model is first validated for steady viscoelastic flows using the four-roll mill case, where the results are compared against the analytical solution and previous numerical studies. The model is then applied to simulate a chaotic low-Reynolds number viscoelastic instability by initialising a small perturbation to the isotropic polymer stress. The results at different <PERSON>enberg numbers experience complex flow dynamics at sufficiently long time durations, which transition from quasi-periodic to periodic to aperiodic states with increasing elastic effects. The quasi-periodic regime was found to experience the fastest transition into the transient regime, with the transitioning time being delayed as the <PERSON>enberg number increased. This work proves the capability of applying a coupled lattice Boltzmann method for exploring the complex flow behaviour of elastic instabilities, allowing the potential to explore more challenging and practical viscoelastic instability cases.", "Keywords": "Viscoelastic instability ; Low Reynolds number ; <PERSON><PERSON><PERSON> method ; Polymers", "DOI": "10.1016/j.compfluid.2021.105280", "PubYear": 2022, "Volume": "235", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical, Medical, and Process Engineering, Faculty of Engineering, Queensland University of Technology, QLD 4001, Australia"}, {"AuthorId": 2, "Name": "C.S. From", "Affiliation": "Department of Chemical Engineering, University of Manchester, Manchester M13 9PL, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical, Medical, and Process Engineering, Faculty of Engineering, Queensland University of Technology, QLD 4001, Australia;Corresponding author"}], "References": []}, {"ArticleId": 92379067, "Title": "Preface to JAISE 14(1)", "Abstract": "", "Keywords": "", "DOI": "10.3233/AIS-210618", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 20863, "JournalTitle": "Journal of Ambient Intelligence and Smart Environments", "ISSN": "1876-1364", "EISSN": "1876-1372", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Cadiz, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Research Group on Development of Intelligent Environments, Middlesex University, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Faculty of Engineering, The University of Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "imec, IPI, Department of Telecommunications and Information Processing, Gent University, Belgium"}], "References": []}, {"ArticleId": 92379072, "Title": "Efficient edit rule implication for nominal and ordinal data", "Abstract": "Edit rule implication is an essential subtask when repairing data inconsistencies against a set of edit rules. In this paper, novel techniques to enhance the performance of this subtask are studied. Our work includes several contributions. First, we draw attention to the case of nominal edit rules in particular. We point out that in many cases, starting with a set of edit rules that is as small as possible is important to improve the performance. This could be achieved by folding edit rules together. Besides that, an enhanced nominal edit rule implication algorithm is proposed, exploiting the properties of nominal edit rules. Second, we introduce ordinal edit rules as a generalization of nominal edit rules, used to capture data inconsistencies for data measured on an ordinal scale and we propose an ordinal edit rule implication algorithm. Evaluation of our methods shows promising results for both implication algorithms, with the ordinal algorithm as best performing in general. On average, our techniques improve the state-of-the-art algorithm for edit rule implication with more than 50 % .", "Keywords": "", "DOI": "10.1016/j.ins.2021.12.114", "PubYear": 2022, "Volume": "590", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ghent University, Department of Telecommunications and Information Processing, St.-Pietersnieuwstraat 41, B-9000 Ghent, Belgium;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ghent University, Department of Telecommunications and Information Processing, St.-Pietersnieuwstraat 41, B-9000 Ghent, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Ghent University, Department of Telecommunications and Information Processing, St.-Pietersnieuwstraat 41, B-9000 Ghent, Belgium"}], "References": []}, {"ArticleId": 92379100, "Title": "Local stability of concrete arch bridge based on Ritz method", "Abstract": "<p>In order to minimize the self-weight and prevent local buckling failure of thin-walled box concrete arch bridges at the same time, the limit values of width-thickness ratios are deduced based on Ritz method and equivalent strut theory of arch bridge. A new method of determining sectional forms based on the limit values of width-thickness ratios is put forward. Based on Mupeng bridge, the theoretical results are verified by finite element software ANSYS. Results show that the limits of width-thickness ratios are related to concrete grade, equivalent calculation length and radius of gyration, the allowable minimum thickness of Mupeng bridge is 15 cm to avoid local buckling. The limit values of width-thickness ratios deduced in this paper are reasonable and this new method of determining sectional forms is simple and rational to apply in engineering. A scientific engineering calculation method on arch ring design is put forward and it can provide a theoretical basis for the design of thin-walled box concrete arch bridges constructed by cantilever pouring.</p>", "Keywords": "", "DOI": "10.3233/JCM-215644", "PubYear": 2022, "Volume": "22", "Issue": "1", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "China Highway Engineering Consulting Group Co., Ltd., Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "China Highway Engineering Consulting Group Co., Ltd., Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "China Communications Second Highway Survey, Design and Research Institute Co., Ltd., Wuhan, Hubei, China"}], "References": []}, {"ArticleId": 92379189, "Title": "View VULMA: Data Set for Training a Machine-Learning Tool for a Fast Vulnerability Analysis of Existing Buildings", "Abstract": "<p>The paper presents View VULMA, a data set specifically designed for training machine-learning tools for elaborating fast vulnerability analysis of existing buildings. Such tools require supervised training via an extensive set of building imagery, for which several typological parameters should be defined, with a proper label assigned to each sample on a per-parameter basis. Thus, it is clear how defining an adequate training data set plays a key role, and several aspects should be considered, such as data availability, preprocessing, augmentation and balancing according to the selected labels. In this paper, we highlight all these issues, describing the pursued strategies to elaborate a reliable data set. In particular, a detailed description of both requirements (e.g., scale and resolution of images, evaluation parameters and data heterogeneity) and the steps followed to define View VULMA are provided, starting from the data assessment (which allowed to reduce the initial sample of about 20.000 images to a subset of about 3.000 pictures), to achieve the goal of training a transfer-learning-based automated tool for fast estimation of the vulnerability of existing buildings from single pictures.</p>", "Keywords": "data set; seismic vulnerability; deep learning data set ; seismic vulnerability ; deep learning", "DOI": "10.3390/data7010004", "PubYear": 2022, "Volume": "7", "Issue": "1", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Intelligent Industrial Technologies and Systems for Advanced Manufacturing (STIIMA), National Research Council of Italy, Via Amendola, 122 D/O, 70126 Bari, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "DICATECH Department, Polytechnic University of Bari, Via Orabona, 4, 70126 Bari, Italy↑Author to whom correspondence should be addressed"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "DICATECH Department, Polytechnic University of Bari, Via Orabona, 4, 70126 Bari, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "DICATECH Department, Polytechnic University of Bari, Via Orabona, 4, 70126 Bari, Italy"}], "References": []}, {"ArticleId": ********, "Title": "Deep-BiGRU based channel estimation scheme for MIMO–FBMC systems", "Abstract": "Deep Learning (DL)–based wireless communication systems have the potential to improve the conventional functions and current architecture of communication systems. In this paper, we propose a novel DL-based channel estimation scheme for multiple-input multiple-output filter bank multicarrier with offset quadrature amplitude modulation (MIMO-FBMC/OQAM) systems called deep bidirectional gated-recurrent unit (BiGRU) scheme. This scheme can easily be applied to a single-input single-output (SISO) system. The proposed scheme is divided into two stages: offline and online. The network is first trained in the offline stage. The prediction of channel information and estimation of the channel matrix using the trained network is then performed in the online stage. The simulation results in terms of the normalized mean square error (NMSE) and bit error rate (BER) demonstrate that, under different time-varying channel models, the proposed DL scheme significantly improves the channel estimation performance of FBMC for single and multiple antennas compared to conventional interference approximation method (IAM) channel estimation methods.", "Keywords": "Channel estimation ; Deep learning (DL) ; Filter bank multicarrier (FBMC) ; Gated-recurrent unit (GRU) ; Interference approximation method (IAM) ; Multiple-input multiple-output (MIMO)", "DOI": "10.1016/j.phycom.2021.101592", "PubYear": 2022, "Volume": "51", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Electronics and Communication Engineering Department, Faculty of Engineering, Delta University for Science and Technology, Gamasa, Egypt;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Electronics and Communication Engineering Department, Faculty of Engineering, Mansoura University, Mansoura 35516, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, Faculty of Engineering, Port Said University, Port Said 42524, Egypt"}], "References": []}, {"ArticleId": ********, "Title": "Automated construction of security integrity wrappers for Industry 4.0 applications", "Abstract": "Industry 4.0 (I4.0) refers to the trend towards automation and data exchange in manufacturing technologies and processes which include cyber-physical systems, where the internet of things connect with each other and the environment via networking. This new connectivity opens systems to attacks, by, e.g. , injecting or tampering with messages. The solution supported by communication protocols such as OPC-UA is to sign and/or encrypt messages. However, given the limited resources of devices and the high performance requirements of I4.0 applications, instead of applying crypto algorithms to all messages in the network, it is better to focus on the messages, that if tampered with or injected, could lead to undesired configurations. This paper describes a framework for developing and analyzing formal executable specifications of I4.0 applications in Maude. The framework supports the engineering design workflow using theory transformations that include algorithms to enumerate network attacks leading to undesired states, and to determine wrappers preventing these attacks. In particular, given a deployment map from application components to devices we define a theory transformation that models execution of applications on the given set of (network) devices. Given an enumeration of attacks (message flows) we define a further theory transformation that wraps each device with policies for signing/signature checking for just those messages needed to prevent the attacks. In addition, we report on a series of experiments checking for attacks by a bounded intruder against variations on a Pick-n-Place application, investigating the effect of increasing bounds or increasing application size and further minimizing the number of messages that must be signed.", "Keywords": "Bounded intruder ; Theory transformation ; Security ; Safety ; Verification ; Rewriting logic", "DOI": "10.1016/j.jlamp.2021.100745", "PubYear": 2022, "Volume": "126", "Issue": "", "JournalId": 781, "JournalTitle": "Journal of Logical and Algebraic Methods in Programming", "ISSN": "2352-2208", "EISSN": "2352-2216", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>, GmbH, Munich, Germany;Federal University of Paraíba, João Pessoa, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "SRI International, CA, USA"}], "References": [{"Title": "A Formal Approach to Physics-based Attacks in Cyber-physical Systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Privacy and Security"}]}, {"ArticleId": 92379369, "Title": "Identification of COM Controller of a Human in Stance Based on Motion Measurement and Phase-Space Analysis", "Abstract": "<p> This article proposes a process to identify the standing stabilizer, namely, the controller in humans to keep upright posture stable against perturbations. We model the controller as a piecewise-linear feedback system, where the state of the center of mass (COM) is regulated by coordinating the whole body so as to locate the zero-moment point (ZMP) at the desired position. This was developed for humanoid robots and is possibly able to elaborate the fundamental control scheme used by humans to stabilize themselves. Difficulties lie on how to collect motion trajectories in a wide area of the state space for reliable identification and how to identify the piecewise-affine dynamical system. For the former problem, a motion measurement protocol is devised based on the theoretical phase portrait of the system. Regarding the latter problem, some clustering techniques including K -means method and EM (Expectation-and-Maximization) algorithm were examined. We found that a modified K -means method produced the most accurate result in this study. The method was applied to the identification of a lateral standing controller of a human subject. The result of the identification quantitatively supported a hypothesis that the COM-ZMP regulator reasonably models the human’s controller when deviations of the angular momentum about the COM are limited. </p>", "Keywords": "Standing stabilization; human motor control; COM-ZMP regulator; system identification; Piecewise-affine dynamical system", "DOI": "10.3389/frobt.2021.729575", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "OMRON Corporation, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Kawada Robotics Corporation, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kawada Robotics Corporation, Japan"}], "References": [{"Title": "A survey: dynamics of humanoid robots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "21-22", "Page": "1338", "JournalTitle": "Advanced Robotics"}]}, {"ArticleId": 92379540, "Title": "Pengaruh Sistem Informasi Akuntansi Keuangan Terhadap Siklus Hidup <PERSON>an Manufaktur Di Era Pandemi", "Abstract": "<p><PERSON>k<PERSON> hidup kinerja keuangan perusahaan merupakan gambaran keuangan perusahaan pada suatu periode tertentu baik menyangkut aspek penghimpunan dana maupun penyaluran dana yang biasanya diukur dengan indikator kecukupan modal, likuiditas dan profitabilitas. Kinerja keuangan adalah gambaran kondisi keuangan perusahaan pada periode tertentu. Kinerja keuangan adalah gambaran kondisi keuangan pada suatu periode tertentu dalam pencapaian hasil ataupun tujuan perusahaan. Tujuan kinerja keuangan adalah untuk menentukan siklus hidup keberhasilan pengelolaan keuangan perusahaan terutama kondisi likuiditas, kecukupan modal dan profitabilitas yang dicapai dalam tahun berjalan maupun tahun sebelumnya serta untuk mengetahui kemampuan dalam mendayagunakan semua asset yang dimiliki dalam menghasilkan profit secara efisien. Tujuan kinerja keuangan perusahaan adalah untuk mengetahui kondisi keuangan perusahaan, sehingga keputusan yang rasional dapat dibuat dalam mengambil keputusan dalam perusahaan. Kinerja keuangan dapat mengetahui posisi keuangan dan keberhasilan dalam pengelolaan keuangan yang mampu dapat digunakan dalam pengambilan keputusan yang rasional dalam suatu perusahaan. Populasi dalam penelitian ini adalah 193 (seratus Sembilan puluh tiga) sektor manufaktur yang yang terdaftar di BEI dan 579 (lima ratus tujuh puluh sembilan sampel. Metode pengambilan sampel dilakukan dengan teknik purposive sampling berdasarkan kriteria tertentu. Analisis data menggunakan analisis regresia linier berganda yang diperoleh dalam penelitian ini dengan bantuan teknologi komputer yaitu program aplikasi SPSS For Windows 26. Hasil penelitian ini menunjukan bahwa sistem informasi akuntansi keuangan, berpengaruh terhadap siklus hidup kinerja keuangan perusahaan era Pandemi.</p>", "Keywords": "Financial Accounting Information System;Financial Performance Life Cycle;Manufacturing Companies;Pandemi Era", "DOI": "10.34010/aisthebest.v6i2.4935", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 66136, "JournalTitle": "@is The Best [Accounting Information System & Information Technology Business Enterprise]", "ISSN": "2252-9853", "EISSN": "2656-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Cenderawasih"}], "References": []}, {"ArticleId": ********, "Title": "INK: knowledge graph embeddings for node classification", "Abstract": "<p>Deep learning techniques are increasingly being applied to solve various machine learning tasks that use Knowledge Graphs as input data. However, these techniques typically learn a latent representation for the entities of interest internally, which is then used to make decisions. This latent representation is often not comprehensible to humans, which is why deep learning techniques are often considered to be black boxes. In this paper, we present INK: Instance Neighbouring by using Knowledge, a novel technique to learn binary feature-based representations, which are comprehensible to humans, for nodes of interest in a knowledge graph. We demonstrate the predictive power of the node representations obtained through INK by feeding them to classical machine learning techniques and comparing their predictive performances for the node classification task to the current state of the art: Graph Convolutional Networks (R-GCN) and RDF2Vec. We perform this comparison both on benchmark datasets and using a real-world use case. </p>", "Keywords": "Knowledge graph representation; Knowledge graph embedding; Node classification; Semantic data mining", "DOI": "10.1007/s10618-021-00806-z", "PubYear": 2022, "Volume": "36", "Issue": "2", "JournalId": 3928, "JournalTitle": "Data Mining and Knowledge Discovery", "ISSN": "1384-5810", "EISSN": "1573-756X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IDLab, Ghent, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IDLab, Ghent, Belgium"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IDLab, Ghent, Belgium"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IDLab, Ghent, Belgium"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "IDLab, Ghent, Belgium"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IDLab, Ghent, Belgium"}], "References": [{"Title": "On the role of knowledge graphs in explainable AI", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "41", "JournalTitle": "Semantic Web"}, {"Title": "Large-scale relation extraction from web documents and knowledge graphs with human-in-the-loop", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "100546", "JournalTitle": "Journal of Web Semantics"}]}, {"ArticleId": 92379730, "Title": "Raman-based spectrophenotyping of the most important cells of the immune system", "Abstract": "<p><b>INTRODUCTION</b>:Human peripheral blood mononuclear cells (PBMCs) are a heterogeneous population of cells that includes T and B lymphocytes. The total number of lymphocytes and their percentage in the blood can be a marker for the diagnosis of several human diseases. Currently, cytometric methods are widely used to distinguish subtypes of leukocytes and quantify their number. These techniques use cell immunophenotyping, which is limited by the number of fluorochrome-labeled antibodies that can be applied simultaneously.</p><p><b>OBJECTIVE</b>:B and T lymphocytes were isolated from peripheral blood obtained from healthy human donors.</p><p><b>METHODS</b>:The immunomagnetic negative selection was used for the enrichment of B and T cells fractions, and their purity was assessed by flow cytometry. Isolated cells were fixed with 0.5% glutaraldehyde and measured using confocal Raman imaging. K-means cluster analysis, principal component analysis and partial least squares discriminant methods were applied for the identification of spectroscopic markers to distinguish B and T cells. HPLC was the reference method for identifying carotene in T cells.</p><p><b>RESULTS</b>:Reliable discrimination between T and B lymphocytes based on their spectral profile has been demonstrated using label-free Raman imaging and chemometric analysis. The presence of carotene in T lymphocytes (in addition to the previously reported in plasma) was confirmed and for the first time unequivocally identified as β-carotene. In addition, the molecular features of the lymphocytes nuclei were found to support the discriminant analysis. It has been shown that although the presence of carotenoids in T cells depends on individual donor variability, the reliable differentiation between lymphocytes is possible based on Raman spectra collected from individual cells.</p><p><b>CONCLUSIONS</b>:This proves the potential of Raman spectroscopy in clinical diagnostics to automatically differentiate between cells that are an important component of our immune system.</p><p>Copyright © 2022. Production and hosting by Elsevier B.V.</p>", "Keywords": "B cells;Carotenoids;Confocal Raman imaging;Spectroscopic markers;T cells", "DOI": "10.1016/j.jare.2021.12.013", "PubYear": 2022, "Volume": "41", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jagiellonian University, Faculty of Chemistry, Krakow, Poland; Jagiellonian University, Jagiellonian Centre for Experimental Therapeutics (JCET), Krakow, Poland."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Jagiellonian University, Faculty of Chemistry, Krakow, Poland."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jagiellonian University, Faculty of Chemistry, Krakow, Poland."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Jagiellonian University, Faculty of Chemistry, Krakow, Poland."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Jagiellonian University, Jagiellonian Centre for Experimental Therapeutics (JCET), Krakow, Poland."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Medical University of Lodz, Department of Pediatrics, Oncology and Hematology, Lodz, Poland."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Medical University of Lodz, Department of Pediatrics, Oncology and Hematology, Lodz, Poland."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Medical University of Lodz, Department of Pediatrics, Oncology and Hematology, Lodz, Poland."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Medical University of Lodz, Department of Pediatrics, Oncology and Hematology, Lodz, Poland."}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>-Sklodowska University, Department of Biophysics, Institute of Physics, Lublin, Poland."}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>-Sklodowska University, Department of Biophysics, Institute of Physics, Lublin, Poland."}, {"AuthorId": 12, "Name": "Malgorzata Baranska", "Affiliation": "Jagiellonian University, Faculty of Chemistry, Krakow, Poland; Jagiellonian University, Jagiellonian Centre for Experimental Therapeutics (JCET), Krakow, Poland."}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Lukasiewicz Research Network - Krakow Institute of Technology, Krakow, Poland. Electronic address:  ."}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jagiellonian University, Faculty of Chemistry, Krakow, Poland; Jagiellonian University, Jagiellonian Centre for Experimental Therapeutics (JCET), Krakow, Poland. Electronic address:  ."}], "References": []}, {"ArticleId": 92379757, "Title": "Issue Information", "Abstract": "", "Keywords": "", "DOI": "10.1002/spy2.166", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 7086, "JournalTitle": "Security and Privacy", "ISSN": "2475-6725", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 92379761, "Title": "A Survey on Big IoT Data Indexing: Potential Solutions, Recent Advancements, and Open Issues", "Abstract": "<p>The past decade has been characterized by the growing volumes of data due to the widespread use of the Internet of Things (IoT) applications, which introduced many challenges for efficient data storage and management. Thus, the efficient indexing and searching of large data collections is a very topical and urgent issue. Such solutions can provide users with valuable information about IoT data. However, efficient retrieval and management of such information in terms of index size and search time require optimization of indexing schemes which is rather difficult to implement. The purpose of this paper is to examine and review existing indexing techniques for large-scale data. A taxonomy of indexing techniques is proposed to enable researchers to understand and select the techniques that will serve as a basis for designing a new indexing scheme. The real-world applications of the existing indexing techniques in different areas, such as health, business, scientific experiments, and social networks, are presented. Open problems and research challenges, e.g., privacy and large-scale data mining, are also discussed.</p>", "Keywords": "big data; Internet of Things; indexing; information retrieval; query big data ; Internet of Things ; indexing ; information retrieval ; query", "DOI": "10.3390/fi14010019", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Labstic Laboratory, Department of Computer Science, Guelma University, Guelma 24000, Algeria"}, {"AuthorId": 2, "Name": "Ala-Ed<PERSON>", "Affiliation": "Labstic Laboratory, Department of Computer Science, Guelma University, Guelma 24000, Algeria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Labstic Laboratory, Department of Computer Science, Guelma University, Guelma 24000, Algeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Labstic Laboratory, Department of Computer Science, Guelma University, Guelma 24000, Algeria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Labstic Laboratory, Department of Computer Science, Guelma University, Guelma 24000, Algeria"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics Engineering, University of Yildiz Technical, Istanbul 34349, Turkey"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China"}], "References": [{"Title": "A queries-based structure for similarity searching in static and dynamic metric spaces", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "2", "Page": "188", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Deep Learning for Generic Object Detection: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "261", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "IoT Inspector", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Interval-based Queries over Lossy IoT Event Streams", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "4", "Page": "1", "JournalTitle": "ACM/IMS Transactions on Data Science"}, {"Title": "An approach to compute the scope of a social object in a Multi-IoT scenario", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "67", "Issue": "", "Page": "101223", "JournalTitle": "Pervasive and Mobile Computing"}, {"Title": "Process Automation in an IoT–Fog–Cloud Ecosystem: A Survey and Taxonomy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "1", "Page": "92", "JournalTitle": "IoT"}]}, {"ArticleId": 92379767, "Title": "Tidal influence on the relationship between solar-induced chlorophyll fluorescence and canopy photosynthesis in a coastal salt marsh", "Abstract": "Accurately estimating the dynamics of ecosystem photosynthesis in coastal wetlands is of paramount importance to the quantification of blue carbon and climate change. In this study, we investigated the relationship between the solar-induced chlorophyll fluorescence at 760 nm (SIF<sub>760</sub>) and the gross primary productivity (GPP), as well as the underlying mechanisms in coastal salt marshes that are regularly flooded by tides, based on continuous observations of the SIF<sub>760</sub> and GPP throughout the growing season of Phragmites australis during 2019. The results show that the SIF<sub>760</sub> was significantly correlated with the GPP on the half-hourly, daily, and weekly scales in different phenological stages, and the linearity of the SIF<sub>760</sub>–GPP relationship generally improved as the time scale increased. Moreover, we found that the canopy structure (i.e., leaf area index and spatial distribution of leaf angles) plays a major role in explaining the relationship between the SIF<sub>760</sub> and photosynthesis on half-hourly and daily scales at the study site. Tidal flooding significantly suppressed the strength of the correlation between SIF<sub>760</sub> and GPP in the early and rapid growth vegetation stage. Furthermore, the tides generally lowered the degree of the correlation between the light use efficiency for photosynthesis (LUE<sub>p</sub>) and the SIF yield, and between the LUE<sub>p</sub> and the canopy SIF<sub>760</sub> escape probability. This indicates that tidal inundation diminished the roles of the plant physiology and canopy structure in explaining the relationship between the SIF<sub>760</sub> and photosynthesis, and thus, it had a negative effect on the SIF<sub>760</sub>-based GPP estimation. The results of this study demonstrate that tidal inundation exerts a significant influence on the relationships between the SIF<sub>760</sub> and GPP and their responses to the absorbed photosynthetically active radiation, which leads to a better understanding of the mechanism linking the SIF<sub>760</sub> and photosynthesis in tidal wetlands and provides new insights into reliable blue carbon quantification in a large scale domain.", "Keywords": "Coastal salt marsh ; Solar-induced chlorophyll fluorescence (SIF) ; Gross primary productivity (GPP) ; Photosynthesis ; Tidal influence", "DOI": "10.1016/j.rse.2021.112865", "PubYear": 2022, "Volume": "270", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Estuarine and Coastal Research, Center for Blue Carbon Science and Technology, East China Normal University, Shanghai, China;Yangtze Delta Estuarine Wetland Ecosystem Observation and Research Station, Ministry of Education & Shanghai Science and Technology Committee, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Estuarine and Coastal Research, Center for Blue Carbon Science and Technology, East China Normal University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Estuarine and Coastal Research, Center for Blue Carbon Science and Technology, East China Normal University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Jiuduansha Wetland National Nature Reserve Administration Center, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Estuarine and Coastal Research, Center for Blue Carbon Science and Technology, East China Normal University, Shanghai, China;Yangtze Delta Estuarine Wetland Ecosystem Observation and Research Station, Ministry of Education & Shanghai Science and Technology Committee, Shanghai, China;Institute of Eco-Chongming, East China Normal University, Shanghai, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Estuarine and Coastal Research, Center for Blue Carbon Science and Technology, East China Normal University, Shanghai, China;Yangtze Delta Estuarine Wetland Ecosystem Observation and Research Station, Ministry of Education & Shanghai Science and Technology Committee, Shanghai, China;Institute of Eco-Chongming, East China Normal University, Shanghai, China;Corresponding author at: State Key Laboratory of Estuarine and Coastal Research, Center for Blue Carbon Science and Technology, East China Normal University, Shanghai, China"}], "References": [{"Title": "Canopy structure explains the relationship between photosynthesis and sun-induced chlorophyll fluorescence in crops", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "241", "Issue": "", "Page": "111733", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 92379779, "Title": "Surface quality adjustment and controlling mechanism of machined surface layer in two-step milling of titanium alloy", "Abstract": "<p>In the two-step cutting process, due to the combined effects of mechanical and thermal deformation, the microstructure and residual stress of the workpiece are changed with the change of roughing parameters, which affects the machined surface quality. In this paper, the two-step milling (roughing and then finishing) experiments of Ti-6Al-4V titanium alloy were designed to analyze the effect of different roughing parameters on the cutting force of roughing and finishing, the residual stress of finishing surface, and the microstructure. The microstructural characteristics in terms of residual stress, XRD patterns, phase composition and content, and nano-scale crystallite size of machined surface layer were characterized to reveal the machined surface layer quality adjustment and controlling mechanism from the prospective of the microscopic scale. The experimental results showed that the cutting force and compressive residual stress were larger at low roughing cutting speed than that at high roughing cutting speed because of the combined effects of mechanical and thermal deformations. In the two-step machining process, with the increase of roughing cutting speed, the β phase on the finishing surface firstly increased and then decreased. Meanwhile, the high roughing cutting speed weakened the microcrystal refinement of the finishing surface because of the work hardening effect. Therefore, the appropriate roughing machining parameters will contribute to the improvement of finishing machined surface quality in the terms of lower cutting force, higher compressive residual stress, and better grain refinement, phase content, and preferred crystallite orientation, thus increasing the microscopic mechanical properties of machined surface layer. This provides a reference for optimizing the cutting parameters to adjust and control the quality and integrity of the machined surface layer in two-step milling of Ti-6Al-4V alloy.</p>", "Keywords": "Two-step milling; Titanium alloy; Machined surface quality; Microscopic mechanism", "DOI": "10.1007/s00170-021-08359-7", "PubYear": 2022, "Volume": "119", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of High Efficiency and Clean Mechanical Manufacture of MOE, School of Mechanical Engineering, Shandong University, Jinan, People’s Republic of China;National Demonstration Center for Experimental Mechanical Engineering Education (Shandong University), Jinan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of High Efficiency and Clean Mechanical Manufacture of MOE, School of Mechanical Engineering, Shandong University, Jinan, People’s Republic of China;National Demonstration Center for Experimental Mechanical Engineering Education (Shandong University), Jinan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of High Efficiency and Clean Mechanical Manufacture of MOE, School of Mechanical Engineering, Shandong University, Jinan, People’s Republic of China;National Demonstration Center for Experimental Mechanical Engineering Education (Shandong University), Jinan, People’s Republic of China"}], "References": [{"Title": "Surface integrity enhancement of ZL109 aluminum-silicon piston alloy employing the forward and reverse finish cutting method", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "1-2", "Page": "617", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 92379784, "Title": "<PERSON><PERSON><PERSON> method for solving uncertain heat equation", "Abstract": "<p>For usual uncertain heat equations, it is challenging to acquire their analytic solutions. A forward difference Euler method has been used to compute the uncertain heat equations' numerical solutions. Nevertheless, the Euler scheme is instability in some cases. This paper proposes an implicit task to overcome this disadvantage, namely the Crank-<PERSON><PERSON> method, which is unconditional stability. An example shows that the Crank-<PERSON><PERSON> scheme is more stable than the previous scheme (Euler scheme). Moreover, the Crank-Nicolson method is also applied to compute two characteristics of uncertain heat equation's solution-expected value and extreme value. Some examples of uncertain heat equations are designed to show the availability of the Crank-<PERSON>lson method.</p><p>© The Author(s), under exclusive licence to Springer-Verlag GmbH Germany, part of Springer Nature 2021.</p>", "Keywords": "<PERSON><PERSON><PERSON><PERSON> method;Heat equation;<PERSON> process;Numerical solution", "DOI": "10.1007/s00500-021-06565-9", "PubYear": 2022, "Volume": "26", "Issue": "3", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of System Engineering, National University of Defense Technology, Changsha, 410073 China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business Administration, Chongqing Technology and Business University, Chongqing, 400067 China."}], "References": []}, {"ArticleId": 92379794, "Title": "Z-Voter: a novel high-impedance voter for efficient realization of tristate logic in quantum-dot cellular automata technology", "Abstract": "<p>Regardless of the technology, the tristate logic is a crucial concept which facilitate bidirectional shared media access as an essential requirement for development of large-scale systems. Moreover, the tristate circuits significantly contribute to some advanced design topics such as wired logic and construction of basic components. Lack of a straightforward realization for the tristate logic is still a challenge in efficient development of some advanced topics such as shared media, and memory and I/O access mechanism in Quantum-dot Cellular Automata, as a tentative replacement candidate of the digital technology. A two-input tristate voter named as <PERSON><PERSON><PERSON><PERSON> is introduced in this paper whose output is equal to both inputs in case of identical inputs. In case of different inputs, however, it does not enforce a strict ‘0’ or ‘1’ value on its output which resembles a high-impedance state. To demonstrate the Z-Voter correct functionality and applicability, it is utilized to propose a novel stand-alone efficient tristate buffer. Its main advantage with respect to other previous rivals is that it operates just similar to other conventional Quantum-dot Cellular Automata gates and does not rely on a special clocking mechanism. This simplifies its utilization for implementing larger systems. To demonstrate its correct operation as well as simple utilization, the introduced tristate buffer is exploited to implement a simple yet scalable shared media access control scheme. The simulation results of the proposed scheme with two and three simultaneously connected drivers are presented in the paper to demonstrate its scalability. The proposed Z-Voter and tristate buffer are very compact and composed of 4 and 29 cells, respectively. Convenient kink energy proofs, as well as QCADesigner-E tool simulation results, prove the correct functionality and applicability of the proposed gates and circuits. The power analysis results of the circuits are also extracted using QCAPro.</p>", "Keywords": "QCA; Z-Voter; Tristate buffer; High-Impedance; Input/Output; External memory; Peripheral access; Shared media access control", "DOI": "10.1007/s11227-021-04167-8", "PubYear": 2022, "Volume": "78", "Issue": "6", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Shahid Beheshti University, Tehran, Iran"}], "References": [{"Title": "Design and implementation of multiplication algorithm in quantum-dot cellular automata with energy dissipation analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "6", "Page": "5779", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "QCA-based Hamming code circuit for nano communication network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "84", "Issue": "", "Page": "104237", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 92379949, "Title": "An efficient and lightweight identity-based scheme for secure communication in clustered wireless sensor networks", "Abstract": "Clustered Wireless Sensor Networks (CWSNs) are typically deployed in unsecured or even hostile areas, making them vulnerable to many cyber-attacks and security threats that adversely affect their performance. Furthermore, the design of an efficient cryptographic scheme for CWSN is challenging due to the dynamic nature of the network and resource-constrained sensor devices. The paper presents a new identity-based authentication and key agreement scheme for CWSNs called IBAKAS, which combines Elliptic Curve Cryptography (ECC) and Identity-Based Cryptography (IBC) to provide mutual authentication and establish secret session keys over insecure channels. IBAKAS achieves all desirable security properties of key agreement and prevents specific cyber-attacks on CWSN. Moreover, the formal security of the proposed scheme is verified using the AVISPA tool. Comparison with existing relevant schemes shows that the proposed scheme decreases computational and communication overheads, saves keys storage space and prolongs the network lifetime by reducing the energy consumption of the sensor node.", "Keywords": "Cluster-based WSN ; Identity-based cryptography ; Elliptic curve ; Mutual authentication ; Key agreement ; AVISPA", "DOI": "10.1016/j.jnca.2021.103282", "PubYear": 2022, "Volume": "200", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of M’Sila, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Biskra, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Paris-Est Creteil, LISSI/TincNET, F-94400, Vitry sur Seine, France;Corresponding author"}], "References": [{"Title": "Securing Wireless Sensors in Military Applications through Resilient Authentication Mechanism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "719", "JournalTitle": "Procedia Computer Science"}, {"Title": "Secure data aggregation methods and countermeasures against various attacks in wireless sensor networks: A comprehensive review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "190", "Issue": "", "Page": "103118", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "A Lightweight Leakage-Resilient Identity-Based Mutual Authentication and Key Exchange Protocol for Resource-limited Devices", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "196", "Issue": "", "Page": "108246", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 92379958, "Title": "Accurate indoor positioning system based on modify nearest point technique", "Abstract": "<p>Wireless fidelity (Wi-Fi) is common technology for indoor environments that use to estimate required distances, to be used for indoor localization. Due to multiple source of noise and interference with other signal, the receive signal strength (RSS) measurements unstable. The impression about targets environments should be available to estimate accurate targets location. The Wi-Fi fingerprint technique is widely implemented to build database matching with real data, but the challenges are the way of collect accurate data to be the reference and the impact of different environments on signals measurements. In this paper, optimum system proposed based on modify nearest point (MNP). To implement the proposal, 78 points measured to be the reference points recorded in each environment around the targets. Also, the case study building is separated to 7 areas, where the segmentation of environments leads to ability of dynamic parameters assignments. Moreover, database based on optimum data collected at each time using 63 samples in each point and the average will be final measurements. Then, the nearest point into specific environment has been determined by compared with at least four points. The results show that the errors of indoor localization were less than (0.102 m).</p>", "Keywords": "modify nearest point;positioning system;RSS;Wi-Fi;wireless InSite", "DOI": "10.11591/ijece.v12i2.pp1593-1601", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Middle Technical University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Al-Esraa University College"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Hadba'a University College"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Fallujah"}], "References": []}, {"ArticleId": 92379963, "Title": "Automatic BIRCH thresholding with features transformation for hierarchical breast cancer clustering", "Abstract": "<p><p>Breast cancer is one of the most common diseases diagnosed in women over the world. The balanced iterative reducing and clustering using hierarchies (BIRCH) has been widely used in many applications. However, clustering the patient records and selecting an optimal threshold for the hierarchical clusters still a challenging task. In addition, the existing BIRCH is sensitive to the order of data records and influenced by many numerical and functional parameters. Therefore, this paper proposes a unique BIRCH-based algorithm for breast cancer clustering. We aim at transforming the medical records using the breast screening features into sub-clusters to group the subject cases into malignant or benign clusters. The basic BIRCH clustering is firstly fed by a set of normalized features then we automate the threshold initialization to enhance the tree-based sub-clustering procedure. Additionally, we present a thorough analysis on the performance impact of tuning BIRCH with various relevant linkage functions and similarity measures. Two datasets of the standard breast cancer wisconsin (BCW) benchmarking collection are used to evaluate our algorithm. The experimental results show a clustering accuracy of 97.7% in 0.0004 seconds only, thereby confirming the efficiency of the proposed method in clustering the patient records and making timely decisions.</p></p>", "Keywords": "automatic thresholding;balanced iterative reducing and clustering using hierarchies;breast cancer;computer-aided diagnosis;hierarchical clustering", "DOI": "10.11591/ijece.v12i2.pp1498-1507", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Jordan University of Science and Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Middle East University"}], "References": []}, {"ArticleId": 92379966, "Title": "Microstrip band-stop filter based on double negative metamaterial", "Abstract": "<p>In this work, we present a novel miniature band stop filter based on double negative metamaterial, this circuit is designed on a low-cost substrate FR-4 of relative permittivity 4.4 and low tangential losses 0.002. The proposed filter has a compact and miniature size of 15 mm in length and 12mm in width without the 50 Ω feed lines. The resonator was studied and analyzed with a view to achieving a band-stop behavior around its resonant frequency. The band-stop characteristics are obtained by implementing the metamaterial resonator on the final structure. The obtained results show that this microstrip filter achieves fractional bandwidth of 40% at 2 GHz. Furthermore, excellent transmission quality and good attenuation are achieved. This filter is an adequate solution for global system for mobile communications (GSM).</p>", "Keywords": "band-stop;double negative;filter;metamaterial;microstrip", "DOI": "10.11591/ijece.v12i2.pp1579-1584", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hassan First University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hassan First University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hassan First University"}], "References": []}, {"ArticleId": 92379976, "Title": "Study and analysis of motion artifacts for ambulatory electroencephalography", "Abstract": "<p>Motion artifacts contribute complexity in acquiring clean electroencephalography (EEG) data. It is one of the major challenges for ambulatory EEG. The performance of mobile health monitoring, neurological disorders diagnosis and surgeries can be significantly improved by reducing the motion artifacts. Although different papers have proposed various novel approaches for removing motion artifacts, the datasets used to validate those algorithms are questionable. In this paper, a unique EEG dataset was presented where ten different activities were performed. No such previous EEG recordings using EMOTIV EEG headset are available in research history that explicitly mentioned and considered a number of daily activities that induced motion artifacts in EEG recordings. Quantitative study shows that in comparison to correlation coefficient, the coherence analysis depicted a better similarity measure between motion artifacts and motion sensor data. Motion artifacts were characterized with very low frequency which overlapped with the Delta rhythm of the EEG. Also, a general wavelet transform based approach was presented to remove motion artifacts. Further experiment and analysis with more similarity metrics and longer recording duration for each activity is required to finalize the characteristics of motion artifacts and henceforth reliably identify and subsequently remove the motion artifacts in the contaminated EEG recordings.</p>", "Keywords": "electroencephalogram;electroencephalography;motion artifact;movement artifact;neural signal;scalp EEG", "DOI": "10.11591/ijece.v12i2.pp1520-1529", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Independent University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Independent University"}, {"AuthorId": 3, "Name": "Sheikh <PERSON><PERSON>", "Affiliation": "Islamic University of Technology"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Independent University"}], "References": []}, {"ArticleId": 92379978, "Title": "Auto tuning of frequency on wireless power transfer for an electric vehicle", "Abstract": "<p><p>In these days, electric vehicles are enthusiastically researched as a countermeasure to air pollution, although these do not have practicality compared to gasoline-powered vehicles. The aim of this study is to transport energy wirelessly and efficiently to an electric vehicle. To accomplish this, we focused on frequency of an alternating current (AC) power supply, and suggested a method which determined the value of it constantly. In particular, a wireless power transfer circuit and a lithium-ion battery in an electric vehicle were expressed with an equivalent circuit, and efficiency of energy transfer was calculated. Furthermore, the optimal frequency which maximizes efficiency was found, and the behavior of voltage was demonstrated on a secondary circuit. Finally, we could obtain the larger electromotive force at the secondary inductor than an input voltage.</p></p>", "Keywords": "adjustment of frequency;control engineering;electric vehicle;mutual inductance;wireless power transfer", "DOI": "10.11591/ijece.v12i2.pp1147-1152", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology"}], "References": []}, {"ArticleId": 92379980, "Title": "Health-related fake news during the COVID-19 pandemic: perceived trust and information search", "Abstract": "Purpose \n Health-related online fake news (HOFN) has become a major social problem. HOFN can lead to the spread of ineffective and even harmful remedies. The study aims to understand Internet users' responses to HOFN during the coronavirus (COVID-19) pandemic using the protective action decision model (PADM).\n \n \n Design/methodology/approach \n The authors collected pandemic severity data (regional number of confirmed cases) from government websites of the USA and China (Studies 1 and 2), search behavior from Google and Baidu search engines (Studies 1 and 2) and data regarding trust in two online fake news stories from two national surveys (Studies 2 and 3). All data were analyzed using a multi-level linear model.\n \n \n Findings \n The research detected negative time-lagged relationships between pandemic severity and regional HOFN search behavior by three actual fake news stories from the USA and China (Study 1). Importantly, trust in HOFN served as a mediator in the time-lagged relationship between pandemic severity and search behavior (Study 2). Additionally, the relationship between pandemic severity and trust in HOFN varied according to individuals' perceived control (Study 3).\n \n \n Originality/value \n The authors' results underscore the important role of PADM in understanding Internet users' trust in and search for HOFN. When people trust HOFN, they may seek more information to implement further protective actions. Importantly, it appears that trust in HOFN varies with environmental cues (regional pandemic severity) and with individuals' perceived control, providing insight into developing coping strategies during a pandemic.", "Keywords": "Online fake news,Health-related fake news,Trust in fake news,Search behavior,Perceived control", "DOI": "10.1108/INTR-11-2020-0624", "PubYear": 2022, "Volume": "32", "Issue": "3", "JournalId": 20482, "JournalTitle": "Internet Research", "ISSN": "1066-2243", "EISSN": "2054-5657", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management , Fuzhou University , Fuzhou, China Center for China Social Trust Research , Fuzhou University , Fuzhou, China Institute of Psychological and Cognitive Sciences , Fuzhou University , Fuzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Psychology, The University of Toledo , Toledo, Ohio, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medical Psychology, School of Health Humanities , Peking University , Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Business Administration , Dongbei University of Finance and Economics , Dalian, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management , Fuzhou University , Fuzhou, China Center for China Social Trust Research , Fuzhou University , Fuzhou, China Institute of Psychological and Cognitive Sciences , Fuzhou University , Fuzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Psychological and Cognitive Sciences , Beijing Key Laboratory of Behavior and Mental Health , Peking University , Beijing, China"}], "References": []}, {"ArticleId": 92379981, "Title": "Development and testing of braking and acceleration features for vehicle advanced driver assistance system", "Abstract": "<span lang=\"EN-US\">Traffic congestion is a constant problem for cities worldwide. The human driving inefficiency and poor urban planning and development contribute to traffic buildup and travel discomfort. An example of human inefficiency is the phantom traffic jam, which is caused by unnecessary braking, causing traffic to slow down, and eventually coming to a stop. In this study, a brake and acceleration feature (BAF) for the advanced driver assistance system (ADAS) is proposed to mitigate the effects of the phantom traffic phenomenon. In its initial stage, the BAF provides a heads-up display that gives information on how much braking and acceleration input is needed to maintain smooth driving conditions, i.e., without sudden acceleration or deceleration, while observing a safe distance from the vehicle in front. BAF employs a fuzzy logic controller that takes distance information from a light detection and ranging (LIDAR) sensor and the vehicle’s instantaneous speed from the engine control unit (ECU). It then calculates the corresponding percentage value of needed acceleration and braking in order to maintain travel objectives of smooth and safe-distance travel. Empirical results show that the system suggests acceleration and braking values slightly higher than the driver’s actual inputs and can achieve 90% accuracy overall.</span>", "Keywords": "Advanced driver assistance;Brake and acceleration features;Fuzzy logic;Intelligent transportation systems;Phantom traffic jam;System", "DOI": "10.11591/ijece.v12i2.pp2047-2057", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ateneo de Manila University Quezon City"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Ateneo de Manila University Quezon City"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "De La Salle University"}], "References": []}, {"ArticleId": 92379982, "Title": "Sliding-mode controller for a step up-down battery charger with a single current sensor", "Abstract": "<p>This paper proposes a battery charger solution based on the Zeta DC/DC converter to provide a general interface between batteries and microgrid direct current (DC) buses. This solution enables to interface batteries and DC buses with voltage conversion ratios lower, equal, and higher than one using the same components and without redesigning the control system, thus ensuring global stability. The converter controller is designed to require only the measurement of a single inductor current, instead of both inductors currents, without reducing the system flexibility and stability. The controller stability is demonstrated using the sliding-mode theory, and a design procedure for the parameters is developed to ensure a desired bus performance. Finally, simulations and experiments validate the performance of the proposed solution under realistic operation conditions.</p>", "Keywords": "battery charger;bus voltage regulation;DC/DC converter;sliding-mode control", "DOI": "10.11591/ijece.v12i2.pp1251-1264", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto Tecnológico Metropolitano"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Nacional de Colombia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Instituto Tecnológico Metropolitano"}], "References": []}, {"ArticleId": 92379989, "Title": "Cosine similarity-based algorithm for social networking recommendation", "Abstract": "<p>Social media have become a discussion platform for individuals and groups. Hence, users belonging to different groups can communicate together. Positive and negative messages as well as media are circulated between those users. Users can form special groups with people who they already know in real life or meet through social networking after being suggested by the system. In this article, we propose a framework for recommending communities to users based on their preferences; for example, a community for people who are interested in certain sports, art, hobbies, diseases, age, case, and so on. The framework is based on a feature extraction algorithm that utilizes user profiling and combines the cosine similarity measure with term frequency to recommend groups or communities. Once the data is received from the user, the system tracks their behavior, the relationships are identified, and then the system recommends one or more communities based on their preferences. Finally, experimental studies are conducted using a prototype developed to test the proposed framework, and results show the importance of our framework in recommending people to communities.</p>", "Keywords": "cosine similarity;feature extraction;social media;TF-IDF;user profiling;virtual community", "DOI": "10.11591/ijece.v12i2.pp1881-1892", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Princess <PERSON><PERSON><PERSON> University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Princess <PERSON><PERSON><PERSON> University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Princess <PERSON><PERSON><PERSON> University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Princess <PERSON><PERSON><PERSON> University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Princess <PERSON><PERSON><PERSON> University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Princess <PERSON><PERSON><PERSON> University"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Princess <PERSON><PERSON><PERSON> University"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Princess <PERSON><PERSON><PERSON> University"}], "References": []}, {"ArticleId": 92379998, "Title": "A new proactive feature selection model based on the enhanced optimization algorithms to detect DRDoS attacks", "Abstract": "Cyberattacks have grown steadily over the last few years. The distributed reflection denial of service (DRDoS) attack has been rising, a new variant of distributed denial of service (DDoS) attack. DRDoS attacks are more difficult to mitigate due to the dynamics and the attack strategy of this type of attack. The number of features influences the performance of the intrusion detection system by investigating the behavior of traffic. Therefore, the feature selection model improves the accuracy of the detection mechanism also reduces the time of detection by reducing the number of features. The proposed model aims to detect DRDoS attacks based on the feature selection model, and this model is called a proactive feature selection model proactive feature selection (PFS). This model uses a nature-inspired optimization algorithm for the feature subset selection. Three machine learning algorithms, i.e., k-nearest neighbor (KNN), random forest (RF), and support vector machine (SVM), were evaluated as the potential classifier for evaluating the selected features. We have used the CICDDoS2019 dataset for evaluation purposes. The performance of each classifier is compared to previous models. The results indicate that the suggested model works better than the current approaches providing a higher detection rate (DR), a low false-positive rate (FPR), <span>and increased accuracy detection (DA).</span> The PFS model shows better accuracy to detect DRDoS attacks with 89.59%.", "Keywords": "adaptive threshold;cybersecurity;DRDoS attacks;DRDoS detection mechanism;feature selection", "DOI": "10.11591/ijece.v12i2.pp1869-1880", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "Riyadh <PERSON>", "Affiliation": "Universiti Sains Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Sains Malaysia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universitas of Al-Qadisiyah"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Wasit University"}], "References": []}, {"ArticleId": 92380004, "Title": "Design and development of learning model for compression and processing of deoxyribonucleic acid genome sequence", "Abstract": "<p>Owing to the substantial volume of human genome sequence data files (from 30-200 GB exposed) Genomic data compression has received considerable traction and storage costs are one of the major problems faced by genomics laboratories. This involves a modern technology of data compression that reduces not only the storage but also the reliability of the operation. There were few attempts to solve this problem independently of both hardware and software. A systematic analysis of associations between genes provides techniques for the recognition of operative connections among genes and their respective yields, as well as understandings into essential biological events that are most important for knowing health and disease phenotypes. This research proposes a reliable and efficient deep learning system for learning embedded projections to combine gene interactions and gene expression in prediction comparison of deep embeddings to strong baselines. In this paper we preform data processing operations and predict gene function, along with gene ontology reconstruction and predict the gene interaction. The three major steps of genomic data compression are extraction of data, storage of data, and retrieval of the data. Hence, we propose a deep learning based on computational optimization techniques which will be efficient in all the three stages of data compression.</p>", "Keywords": "data compression;gene quality score;gene-interaction-networks;heterogeneous-data-processing;network-representation-learning;quality score distribution", "DOI": "10.11591/ijece.v12i2.pp1786-1794", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "REVA University"}, {"AuthorId": 2, "Name": "Rayapur Venkata Siva Reddy", "Affiliation": "REVA University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "REVA university"}], "References": []}, {"ArticleId": 92380009, "Title": "Super-capacitor energy storage system to recuperate regenerative braking energy in elevator operation of high buildings", "Abstract": "<span>In operating phases of elevators, accelerating, braking modes occur frequently, so braking energy recuperation of elevators has contributed considerably to decrease the total electric energy consumption for operating elevators in multi-floor buildings. In this paper, the supercapacitor energy storage system is used to recover regenerative braking energy of elevators when they operate down full-load and up no-load, reducing fluctuation of voltage on DC bus as well. Therefore, super-capacitor energy storage system (SCESS) will be parallel with line utility to recuperate regenerative braking energy in braking phase and support energy for acceleration phase. The surplus energy will be stored in the supercapacitors thanks to a DC-DC converter capable of exchanging energy bidirectionally in buck/boost modes, and designing control strategy including two control loops. Inner loop-current loop: controlling charge/discharge process of supercapacitors by current iL complying with operation characteristic of elevator; Outer loop-voltage loop: managing UDC-link at a fixed value. Simulation results with elevator system of the ten-floor building, Hanoi, Vietnam installed SCESS have been verified on MATLAB Simulink, SimPowerSystem with saving energy level about 30%.</span>", "Keywords": "bidirectional DC-DC converter;energy saving;mechanical elevator;regenerative braking;super-capacitor energy storage system", "DOI": "10.11591/ijece.v12i2.pp1358-1367", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of transport and communications"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of transport and communications"}], "References": []}, {"ArticleId": 92380011, "Title": "COVID-19 impact on Facebook-based social commerce in Bangladesh", "Abstract": "<p>Popular social media Facebook-oriented social commerce (S-commerce), commonly known as Facebook commerce (F-commerce) has progressed towards a bevy business in Bangladesh. Many young people, especially at the age of 20-28, are now in this industry. The pandemic situation due to coronavirus disease 2019 (COVID-19) forces people to buy more from the online market because of the safety issue. People are getting more interested in the new trend of buying from an online store. The current study aims to explore the impact of COVID-19 on F-commerce, particularly in Bangladesh. It uses the non-probability purposive sampling method and collects 181 usable responses through an online questionnaire. A research model is developed following the social commerce acceptance model (SCAM), and structural equation model partial least square (SEM-PLS) using SmartPLS 3.0 is applied to find out and justify the result. Likert five-point scale for determining the independent variables, including COVID-19 awareness (CA), consumer behavior (CB), and purchase intention (PI), is used. The study result confirms that these three variables have a positive impact on F-commerce. The survey covers other measurable items that indicate some assumptions, which reflect F-commerce consumers’ behavior. The researchers recommend that F-commerce businesspeople must emphasize on mitigating trust issues and provide enhanced home delivery service.</p>", "Keywords": "Covid-19 impact;E-commerce;F-commerce;S-commerce", "DOI": "10.11591/ijece.v12i2.pp1636-1649", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Daffodil International University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Daffodil International University"}], "References": []}, {"ArticleId": 92380013, "Title": "<PERSON>in<PERSON>é plots to analyze photoplethysmography signal between non-smokers and smokers", "Abstract": "<span>An analysis of blood circulation was used to identify variations of heart rate and to create an early warning system of autonomic dysfunction. The Poincaré plot analyzed blood circulation using photoplethysmography (PPG) signals between non-smokers and smokers in three different indices: SD1, SD2, and SD1 SD2 ratio (SSR). There were twenty subjects separated into non-smoker and smoker groups with sample sizes of 10, respectively. An independent sample t-test to compare the continuous variables. Whereas, the comparison between two groups employed <PERSON>’s exact test for categorical variables. The result showed that SD1 was found to be considerably lower in the group of smokers (0.03±0.01) than that of the non-smokers (0.06±0.03). Similarly, SSR was recorded at 0.0012±0.0005 and 0.0023±0.0012 for smoking and non-smoking subjects, respectively. As a comparison, SD2 for non-smokers (25.7±0.5) was lower than smokers (27.3±0.4). In conclusion, we revealed that the parameters of Poincaré plots (SD1, SD2, and SSR) exert good performances to significantly differentiate the PPG signals of the group of non-smokers from those of smokers. We also supposed that the method promises to be a suitable method to distinguish the cardiovascular disease group. Therefore, this method can be applied as a part of early detection system of cardiovascular diseases.</span>", "Keywords": "blood circulation;photoplethysmography signal;poincaré plots;smokers", "DOI": "10.11591/ijece.v12i2.pp1565-1570", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Ahmad <PERSON> and National Dong Hwa University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Dong Hwa University"}, {"AuthorId": 3, "Name": "Akrom Akrom", "Affiliation": "Universitas Ahmad <PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Ahmad <PERSON>"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Ahmad <PERSON>"}], "References": []}, {"ArticleId": 92380014, "Title": "Operational transconductance amplifier-based comparator for high frequency applications using 22 nm FinFET technology", "Abstract": "<p><span>Fin field-effect transistor (FinFET) based analog circuits are gaining importance over metal oxide semiconductor field effect transistor (MOSFET) based circuits with stability and high frequency operations. Comparator that forms the sub block of most of the analog circuits is designed using operational transconductance amplifier (OTA). The OTA is designed using new design procedures and the comparator circuit is designed integrating the sub circuits with OTA. The building blocks of the comparator design such as input level shifter, differential pair with cascode stage and class AB amplifier for output swing are designed and integrated. Folded cascode circuit is used in the feedback path to maintain the common mode input value to a constant, so that the differential pair amplifies the differential signal. The gain of the comparator is achieved to be greater than 100 dB, with phase margin of 65°, common mode rejection ratio (CMRR) of above 70 dB and output swing from rail to rail. The circuit provides unity gain bandwidth of 5 GHz and is suitable for high sampling rate data converter circuits.</span></p>", "Keywords": "Comparator;Fin field-effect transistor;High frequency;Layout design;Operational transconductance amplifier;Rail-to-rail", "DOI": "10.11591/ijece.v12i2.pp2158-2168", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "RV College of Engineering, Bangalore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Visvesvaraya Technological University"}], "References": []}, {"ArticleId": ********, "Title": "Determining customer limits by data mining methods in credit allocation process", "Abstract": "<p>The demand for credit is increasing constantly. Banks are looking for various methods of credit evaluation that provide the most accurate results in a shorter period in order to minimize their rising risks. This study focuses on various methods that enable the banks to increase their asset quality without market loss regarding the credit allocation process. These methods enable the automatic evaluation of loan applications in line with the sector practices, and enable determination of credit policies/strategies based on actual needs. Within the scope of this study, the relationship between the predetermined attributes and the credit limit outputs are analyzed by using a sample data set of consumer loans. Random forest (RF), sequential minimal optimization (SMO), PART, decision table (DT), J48, multilayer perceptron(MP), JRip, naïve Bayes (NB), one rule (OneR) and zero rule (ZeroR) algorithms were used in this process. As a result of this analysis, SMO, PART and random forest algorithms are the top three approaches for determining customer credit limits.</p>", "Keywords": "Banking;Credit allocation process;Data mining;Machine learning algorithms", "DOI": "10.11591/ijece.v12i2.pp1910-1915", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "Tuğçe Ayhan", "Affiliation": "Bahcesehir University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Bahcesehir University"}], "References": []}, {"ArticleId": ********, "Title": "Gender recognition from unconstrained selfie images: a convolutional neural network approach", "Abstract": "<p><p>Human gender recognition is an essential demographic tool. This is reflected in forensic science, surveillance systems and targeted marketing applications. This research was always driven using standard face images and hand-crafted features. Such way has achieved good results, however, the reliability of the facial images had a great effect on the robustness of extracted features, where any small change in the query facial image could change the results. Nevertheless, the performance of current techniques in unconstrained environments is still inefficient, especially when contrasted against recent breakthroughs in different computer vision research. This paper introduces a novel technique for human gender recognition from non-standard selfie images using deep learning approaches. Selfie photos are uncontrolled partial or full-frontal body images that are usually taken by people themselves in real-life environment. As far as we know this is the first paper of its kind to identify gender from selfie photos, using deep learning approach. The experimental results on the selfie dataset emphasizes the proposed technique effectiveness in recognizing gender from such images with 89% accuracy. The performance is further consolidated by testing on numerous benchmark datasets that are widely used in the field, namely: Adience, LFW, FERET, NIVE, Caltech WebFaces and CAS-PEAL-R1.</p></p>", "Keywords": "Deep learning;CNN;Gender recognition selfie images;Soft biometrics;Transferred learning", "DOI": "10.11591/ijece.v12i2.pp2066-2078", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "South Valley University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Imam <PERSON><PERSON><PERSON> University"}, {"AuthorId": 3, "Name": "Islam <PERSON>", "Affiliation": "Assiut University"}], "References": []}, {"ArticleId": 92380030, "Title": "Preliminary analysis of eddy current and iron loss in magnetic gear in electric vehicle", "Abstract": "<p>The inclusion of a high energy density permanent magnet into magnetic gear improves the machine's torque density. However, it also contributes to eddy current loss, especially in a high-speed application such in electric vehicle. In this paper, the losses from eddy current and iron loss are investigated on concentric magnetic gear (CMG). Torque multiplier CMG is designed with 8/3 gear ratio for this study. Iron loss and eddy current loss are compared and discussed. Based on this study, eddy current loss contributes to almost 96% of the total loss. This finding is hoped to direct the researcher to focus more on reducing loss associated with eddy current loss.</p>", "Keywords": "eddy current loss;efficiency;iron loss;magnetic gear;torque", "DOI": "10.11591/ijece.v12i2.pp1161-1167", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}, {"AuthorId": 4, "Name": "Raja <PERSON>", "Affiliation": "Universiti Teknikal Malaysia Melaka"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}], "References": []}, {"ArticleId": 92380033, "Title": "Investigating the residential electricity consumption-income nexus in Morocco: a stochastic impacts by regression on population, affluence, and technology analysis", "Abstract": "<p>In a comprehensive LMDI-STIRPAT-ARDL framework, this research investigates the residential electricity consumption (REC)-income nexus in Morocco for the period 1990 to 2018. The logarithmic mean Divisia index (LMDI) results show that economic activity and electricity intensity are the leading drivers of Morocco’s REC, followed by population and residential structure. And then, the LMDI analysis was combined with stochastic impacts by regression on population, affluence, and technology (STIRPAT) analysis and the bounds testing approach to search for a long-run equilibrium relationship. The empirical results show that REC, economic growth, urbanization, and electricity intensity are cointegrated. The results further show that there exists a U-shaped relationship between per capita gross domestic product (GDP) and REC: an increase in per capita GDP reduces REC initially; but, after reaching a turning point (the GDPPC level of 17,145.22 Dh), further increases in per capita GDP increase REC. Regarding urbanization, the results reveal that it has no significant impact on Morocco’s REC. The stability parameters of the short and long-term coefficients of residential electricity demand function are tested. The results of these tests showed a stable pattern. Finally, based on the findings mentioned above, policy implications for guiding the country's development and electricity planning under energy and environmental constraints are given.</p>", "Keywords": "electricity consumption;logarithmic mean Divisia index model;Morocco;residential sector;STIRPAT model", "DOI": "10.11591/ijece.v12i2.pp1089-1101", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> 1st University Settat"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "High School of Thechnology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "High School of Thechnology"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "High School of Thechnology"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> 1st University Settat"}], "References": []}, {"ArticleId": 92380037, "Title": "A novel algorithm for software defined networks model to enhance the quality of services and scalability in wireless network", "Abstract": "<p>Software defined networks (SDN) have replaced the traditional network architecture by separating the control from forwarding planes. SDN technology utilizes computer resources to provide worldwide effective service than the aggregation of single internet resources usage. Breakdown while resource allocation is a major concern in cloud computing due to the diverse and highly complex architecture of resources. These resources breakdowns cause delays in job completion and have a negative influence on attaining quality of service (QoS). In order to promote error-free task scheduling, this study represents a promising fault-tolerance scheduling technique. For optimum QoS, the suggested restricted Boltzmann machine (RBM) approach takes into account the most important characteristics like current consumption of the resources and rate of failure. The proposed approach's efficiency is verified using the MATLAB toolbox by employing widely used measures such as resource consumption, average processing time, throughput and rate of success.</p>", "Keywords": "controller;quality of service;restricted boltzmann machine;software-defined network", "DOI": "10.11591/ijece.v12i2.pp1585-1592", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Al-Balqa Applied University"}], "References": []}, {"ArticleId": ********, "Title": "Modeling <PERSON><PERSON><PERSON> Coils for Monitoring Surge Arrester Discharge Currents", "Abstract": "Rogowski coils (RCs) are widely used to measure power or high frequency currents based on their design. In this paper, two types of RCs that are circular (traditional) and cylindrical shapes wound using wire covered by varnish are constructed. This construction is carried out to be suitable for monitoring the discharge current of the surge arrester installed in the distribution system. Concerning high frequency RC modeling for both types considering transfer function is introduced. Self-integrating for both types is attained. Therefore, the experimental tests using function generator for both coils are carried out to identify the parameters of the transfer function representing the introduced model. The measured signals for current and induced voltages are denoised for the parameter identification process. The denoised process is achieved using the MATLAB code ‘wdenoise’ while the parameters are estimated using the system identification toolbox. Verification of the proposed model is achieved using experimental results for the two coils. The sensitivity of the two coils is investigated based on the induced output voltage. The application concerning the two coils for monitoring the discharge current of the surge arrester is done. The results confirm the accuracy of the introduced RC model, as well as the performance of the cylindrical shape, is better than the traditional one. The simulation is carried out using MATLAB and ATPDraw programs. © 2021 CRL Publishing. All rights reserved.", "Keywords": "Denoised signal; Frequency response; Parameters dentification; <PERSON><PERSON><PERSON> coil; Surge arrester discharge current", "DOI": "10.32604/csse.2022.022506", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Nehmdoh A<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92380053, "Title": "User Centric Block-Level Attribute Based Encryption in Cloud Using Blockchains", "Abstract": "Cloud computing is a collection of distributed storage Network which can provide various services and store the data in the efficient manner. The advantages of cloud computing is its remote access where data can accessed in real time using Remote Method Innovation (RMI). The problem of data security in cloud environment is a major concern since the data can be accessed by any time by any user. Due to the lack of providing the efficient security the cloud computing they fail to achieve higher performance in providing the efficient service. To improve the performance in data security, the block chains are used for securing the data in the cloud environment. However, the traditional block chain technique are not suitable to provide efficient security to the cloud data stored in the cloud. In this paper, an efficient user centric block level Attribute Based Encryption (UCBL-ABE) scheme is presented to provide the efficient security of cloud data in cloud environment. The proposed approach performs data transaction by employing the block chain. The proposed system provides efficient privacy with access control to the user access according to the behavior of cloud user using Data Level Access Trust (DLAT). Based on DLAT, the user access has been restricted in the cloud environment. The proposed protocol is implemented in real time using Java programming language and uses IBM cloud. The implementation results justifies that the proposed system can able to provide efficient security to the data present in and cloud and also enhances the cloud performance. © 2021 CRL Publishing. All rights reserved.", "Keywords": "ABE; Block-level; Blockchain; Cloud; Data security", "DOI": "10.32604/csse.2022.022467", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, School of Computing, College of Engineering and Technology, SRM Institute of Science and Technology, Chengalpattu, 603203, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Sri Sai Ram Engineering College, Chennai, 600044, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Saveetha Engineering College, Chennai, 602105, India"}], "References": [{"Title": "A Pursuit of Sustainable Privacy Protection in Big Data Environment by an Optimized Clustered-Purpose Based Algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "4", "Page": "1217", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Efficient sensitivity orient blockchain encryption for improved data security in cloud", "Authors": "<PERSON><PERSON>; <PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "249", "JournalTitle": "Concurrent Engineering"}, {"Title": "Towards Machine Learning Based Intrusion Detection in IoT Networks", "Authors": "<PERSON><PERSON> Islam; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "2", "Page": "1801", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A Novel PoW Scheme Implemented by Probabilistic Signature for Blockchain", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "2", "Page": "265", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 92380100, "Title": "Finite-time fault tolerant neural control of nonlinear multi-agent systems under switching topologies", "Abstract": "The neural tracking control problem for nonlinear strict-feedback multi-agent systems (MASs) subject to actuator faults in finite-time is considered, where each agent communicates with its neighbours on a time-varying directed topology. Radial basis function neural networks (RBFNNs) are used to approximate the unknown internal dynamics of system. By utilising backstepping method and dynamic surface control (DSC) technique, a novel adaptive fault tolerant controller is put forward. It turns out that the designed controller can compensate the effect of uncertainties and actuator faults. Meanwhile, by constructing piecewise Lyapunov functions, sufficient conditions for dwell time of topologies are given such that system achieves finite-time tracking consensus under the designed controller. Finally, an example is carried out to verify feasibility of the theoretical result.", "Keywords": "Nonlinear multi-agent systems ; finite-time tracking ; RBFNNs ; directed switching topologies ; fault tolerant control", "DOI": "10.1080/00207721.2021.2019347", "PubYear": 2022, "Volume": "53", "Issue": "8", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Zhengzhou University, Zhengzhou, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhongyuan University of Technology, Zhengzhou, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Zhengzhou University, Zhengzhou, People's Republic of China"}], "References": [{"Title": "Neural-network-based adaptive quasi-consensus of nonlinear multi-agent systems with communication constrains and switching topologies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "", "Page": "100833", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}, {"Title": "Event-triggered adaptive consensus tracking control for nonlinear switching multi-agent systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "157", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": ********, "Title": "A multi-objective discrete particle swarm optimization method for particle routing in distributed particle filters", "Abstract": "Distributed particle filters (PFs) have received extensive attention because of its excellent performance. Generally, it needs to transmit particles to multiple processing units (PUs) in order to improve the performance. However, how to balance communication costs and computation costs in particle routing is still an open problem. This paper presents a multi-objective discrete particle swarm optimization (PSO) algorithm to solve the problem. In the algorithm, the particle routing problem in distributed PFs is modeled as a multi-objective constrained optimization model for the first time. Following that, an improved hybrid discrete multi-objective PSO is proposed. Two new operators designed based on the problem’s characteristics, that is, a local search strategy based on molecular force and a constraint processing mechanism with greedy search, are developed to improve the performance of the proposed algorithm. By comparing with three commonly used methods on classical particle filters problems, experimental results show that the proposed algorithm is a highly competitive approach, and it can provide multiple high-quality Pareto optimal solutions for decision-makers to meet their different needs.", "Keywords": "Particle swarm optimization ; Particle filters ; Particle routing ; Local search ; Constraint", "DOI": "10.1016/j.knosys.2021.108068", "PubYear": 2022, "Volume": "240", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information and Control Engineering, China University of Mining and Technology, Xuzhou, 221116, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Jiangsu Normal University, Xuzhou, 221116, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information and Control Engineering, China University of Mining and Technology, Xuzhou, 221116, China;Key Laboratory of Symbolic Computation and Knowledge Engineering of Ministry of Education, Jilin University, Changchun, 130012, China;Corresponding author at: School of Information and Control Engineering, China University of Mining and Technology, Xuzhou, 221116, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, College of Staten Island, The City University of New York, Staten Island, NY 10314, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Xu", "Affiliation": "School of Computer Science and Technology, Jiangsu Normal University, Xuzhou, 221116, China"}], "References": [{"Title": "A multi-objective differential evolution algorithm and a constraint handling mechanism based on variables proportion for dynamic economic emission dispatch problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107419", "JournalTitle": "Applied Soft Computing"}, {"Title": "Improving decomposition-based multiobjective evolutionary algorithm with local reference point aided search", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "576", "Issue": "", "Page": "557", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 92380271, "Title": "XAI for myo-controlled prosthesis: Explaining EMG data for hand gesture classification", "Abstract": "Machine Learning has recently found a fertile ground in EMG signal decoding for prosthesis control. However, its understanding and acceptance are strongly limited by the notion of AI models as black-boxes. In critical fields, such as medicine and neuroscience, understanding the neurophysiological phenomena underlying models’ outcomes is as relevant as the classification performances. In this work, we adapt state-of-the-art XAI algorithms to EMG hand gesture classification to understand the outcome of machine learning models with respect to physiological processes, evaluating the contribution of each input feature to the prediction and showing that AI models recognize the hand gestures by mapping and fusing efficiently high amplitude activity of synergic muscles. This allows us to (i) drastically reduce the number of required electrodes without a significant loss in classification performances, ensuring the suitability of the system for a larger population of amputees and simplifying the realization of near real-time applications and (ii) perform an efficient selection of features based on their classification relevance, apprehended by the XAI algorithms. This feature selection leads to classification improvements in term of robustness and computational time, outperforming correlation based methods. Finally, (iii) comparing the physiological explanations produced by the XAI algorithms with the experimental setting highlights inconsistencies in the electrodes positioning over different rounds or users, then improving the overall quality of the process.", "Keywords": "EMG signal decoding ; eXplainable AI ; Myo-controlled prosthesis", "DOI": "10.1016/j.knosys.2021.108053", "PubYear": 2022, "Volume": "240", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics, Information and Bioengineering, Politecnico di Milano, Milan, Italy;Laboratory for Neuroengineering, Department of Health Science and Technology, ETH Zürich, Zürich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Statistics and Quantitative Methods, University of Milano-Bicocca, Milan, Italy;CRISP Research Centre, University of Milano-Bicocca, Milan, Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics and Quantitative Methods, University of Milano-Bicocca, Milan, Italy;CRISP Research Centre, University of Milano-Bicocca, Milan, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics, Information and Bioengineering, Politecnico di Milano, Milan, Italy"}], "References": [{"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "From local explanations to global understanding with explainable AI for trees", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "56", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "EEG-based intention recognition with deep recurrent-convolution neural network: Performance and channel selection by Grad-CAM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "225", "JournalTitle": "Neurocomputing"}, {"Title": "A Survey on the Explainability of Supervised Machine Learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "70", "Issue": "", "Page": "245", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "ContrXT: Generating contrastive explanations from any text classifier", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "", "Page": "103", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 92380288, "Title": "Adversarially Robust Learning via Entropic Regularization", "Abstract": "<p>In this paper we propose a new family of algorithms, ATENT, for training adversarially robust deep neural networks. We formulate a new loss function that is equipped with an additional entropic regularization. Our loss function considers the contribution of adversarial samples that are drawn from a specially designed distribution in the data space that assigns high probability to points with high loss and in the immediate neighborhood of training samples. Our proposed algorithms optimize this loss to seek adversarially robust valleys of the loss landscape. Our approach achieves competitive (or better) performance in terms of robust classification accuracy as compared to several state-of-the-art robust learning approaches on benchmark datasets such as MNIST and CIFAR-10.</p>", "Keywords": "adversarial learning; robustness; Adversarial attack; regularization; Neural network training", "DOI": "10.3389/frai.2021.780843", "PubYear": 2022, "Volume": "4", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering, New York University, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering, New York University, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering, New York University, United States"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering, New York University, United States"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering, New York University, United States"}], "References": []}, {"ArticleId": 92380330, "Title": "Decision Tree Based Key Management for Secure Group Communication", "Abstract": "Group communication is widely used by most of the emerging network applications like telecommunication, video conferencing, simulation applications, distributed and other interactive systems. Secured group communication plays a vital role in case of providing the integrity, authenticity, confidentiality, and availability of the message delivered among the group members with respect to communicate securely between the inter group or else within the group. In secure group communications, the time cost associated with the key updating in the proceedings of the member join and departure is an important aspect of the quality of service, particularly in the large groups with highly active membership. Hence, the paper is aimed to achieve better cost and time efficiency through an improved DC multicast routing protocol which is used to expose the path between the nodes participating in the group communication. During this process, each node constructs an adaptive Ptolemy decision tree for the purpose of generating the contributory key. Each of the node is comprised of three keys which will be exchanged between the nodes for considering the group key for the purpose of secure and cost-efficient group communication. The rekeying process is performed when a member leaves or adds into the group. The performance metrics of novel approach is measured depending on the important factors such as computational and communicational cost, rekeying process and formation of the group. It is concluded from the study that the technique has reduced the computational and communicational cost of the secure group communication when compared to the other existing methods. © 2021 CRL Publishing. All rights reserved.", "Keywords": "Adaptive Ptolemy decision tree; Cost reduction; Key generation; Secure group communication", "DOI": "10.32604/csse.2022.019561", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Bannari Amman Institute of Technology, Sathyamangalam638401, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Hindusthan College of Engineering and Technology, Coimbatore, 641032, India"}], "References": []}, {"ArticleId": 92380334, "Title": "Improved Key Agreement Based Kerberos Protocol for M-Health Security", "Abstract": "The development of wireless sensor network with Internet of Things (IoT) predicts various applications in the field of healthcare and cloud computing. This can give promising results on mobile health care (M-health) and Telecare medicine information systems. M-health system on cloud Internet of Things (IoT) through wireless sensor network (WSN) becomes the rising research for the need of modern society. Sensor devices attached to the patients’ body which is connected to the mobile device can ease the medical services. Security is the key connect for optimal performance of the m-health system that share the data of patients in wireless networks in order to maintain the anonymity of the patients. This paper proposed a secure transmission of M-health data in wireless networks using proposed key agreement based Kerberos protocol. The patients processed data are stored in cloud server and accessed by doctors and caregivers. The data transfer between the patients, server and the doctors are accessed with proposed protocol in order to maintain the confidentiality and integrity of authentication. The efficiency of the proposed algorithm is compared with the existing protocols. For computing 100 devices it consumes only 91milllisecond for computation. © 2021 CRL Publishing. All rights reserved.", "Keywords": "Authentication; Health monitoring; Kerberos; Key agreement; Preparation protocol", "DOI": "10.32604/csse.2022.021717", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineeirng, Nandha Engineering College, Erode, 638052, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical, Electronics and Communication Engineering, GITAM University, Bengaluru Campus561203, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical, Electronics and Communication Engineering, GITAM University, Bengaluru Campus561203, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Amrita School of Engineering, Amrita Vishwa Vidyapeetham, Coimbatore, 641112, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Kongu Engineering College, Perundurai638060, India"}], "References": []}, {"ArticleId": 92380354, "Title": "An Improved Method for Extractive Based Opinion Summarization Using Opinion Mining", "Abstract": "Opinion summarization recapitulates the opinions about a common topic automatically. The primary motive of summarization is to preserve the properties of the text and is shortened in a way with no loss in the semantics of the text. The need of automatic summarization efficiently resulted in increased interest among communities of Natural Language Processing and Text Mining. This paper emphasis on building an extractive summarization system combining the features of principal component analysis for dimensionality reduction and bidirectional Recurrent Neural Networks and Long Short-Term Memory (RNN-LSTM) deep learning model for short and exact synopsis using seq2seq model. It presents a paradigm shift with regard to the way extractive summaries are generated. Novel algorithms for word extraction using assertions are proposed. The semantic framework is well-grounded in this research facilitating the correct decision making process after reviewing huge amount of online reviews, considering all its important features into account. The advantages of the proposed solution provides greater computational efficiency, better inferences from social media, data understanding, robustness and handling sparse data. Experiments on the different datasets also outperforms the previous researches and the accuracy is claimed to achieve more than the baselines, showing the efficiency and the novelty in the research paper. The comparisons are done by calculating accuracy with different baselines using Rouge tool. © 2021 CRL Publishing. All rights reserved.", "Keywords": "Data mining; Opinion mining; Principal component analysis; Sentiment analysis; Text summarization", "DOI": "10.32604/csse.2022.022579", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems, College of Computer Sciences and Information Technology, King Faisal University, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information Systems, College of Computer Sciences and Information Technology, King Faisal University, Saudi Arabia"}], "References": [{"Title": "Using data mining techniques to improve replica management in cloud environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "10", "Page": "7335", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 92380362, "Title": "Covid-19’s Pandemic Relationship to Saudi Arabia’s Weather Using Statistical Analysis and GIS", "Abstract": "The eruption of the novel Covid-19 has changed the socio-economic conditions of the world. The escalating number of infections and deaths seriously threatened human health when it became a pandemic from an epidemic. It developed into an alarming situation when the World Health Organization (WHO) declared a health emergency in MARCH 2020. The geographic settings and weather conditions are systematically linked to the spread of the epidemic. The concentration of population and weather attributes remains vital to study a pandemic such as Covid-19. The current work aims to explore the relationship of the population, weather conditions (humidity and temperature) with the reported novel Covid-19 cases in the Kingdom of Saudi Arabia (KSA). For the study, the data for the reported Covid-19 cases was secured from 11 March 2020, to 21 July 2020 (132 days) from the 13 provinces of KSA. The Governorate level data was used to estimate the population data. A Geographic information system (GIS) analysis was utilised to visualise the relationship. The results suggested that a significant correlation existed between the population and Covid-19 cases. For the weather conditions, the data for the 13 provinces of KSA for the same period was utilised to estimate the relationship between the weather conditions and Covid-19 cases. <PERSON><PERSON><PERSON>’s rank correlation results confirmed that the humidity was significantly linked with the reported cases of Covid-19 in Makkah, Aseer, Najran, and Al Baha provinces. The temperature had a significant relation with the reported Covid-19 cases in Al-Riyad, Makkah, Al-Madinah, Aseer, Najran, and Al-Baha. The inconsistency of the results highlighted the variant behavior of Covid-19 in different regions of the KSA. More exploration is required beyond the weather-related variables. Suggestions for future research and policy direction are offered at the end of the study. © 2021 CRL Publishing. All rights reserved.", "Keywords": "Covid-19; GIS; Humidity; Saudi Arabia; Spearman correlation; Temperature", "DOI": "10.32604/csse.2022.021645", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92380363, "Title": "CNN Based Automated Weed Detection System Using UAV Imagery", "Abstract": "The problem of weeds in crops is a natural problem for farmers. Machine Learning (ML), Deep Learning (DL), and Unmanned Aerial Vehicles (UAV) are among the advanced technologies that should be used in order to reduce the use of pesticides while also protecting the environment and ensuring the safety of crops. Deep Learning-based crop and weed identification systems have the potential to save money while also reducing environmental stress. The accuracy of ML/DL models has been proven to be restricted in the past due to a variety of factors, including the selection of an efficient wavelength, spatial resolution, and the selection and tuning of hyperparameters. The purpose of the current research is to develop a new automated weed detecting system that uses Convolution Neural Network (CNN) classification for a real dataset of 4400 UAV pictures with 15336 segments. Snapshots were used to choose the optimal parameters for the proposed CNN LVQ model. The soil class achieved the user accuracy of 100% with the proposed CNN LVQ model, followed by soybean (99.79%), grass (98.58%), and broadleaf (98.32%). The developed CNN LVQ model showed an overall accuracy of 99.44% after rigorous hyperparameter tuning for weed detection, significantly higher than previously reported studies. © 2021 CRL Publishing. All rights reserved.", "Keywords": "Classification; CNN; Detection; Uav; Weed", "DOI": "10.32604/csse.2022.023016", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Multi Criteria Decision Making System for Parking System", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "1", "Page": "101", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Flood Forecasting of Malaysia Kelantan River using Support Vector Regression Technique", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "3", "Page": "297", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 92380378, "Title": "PAPR Reduction Using Advanced Partial Transmission Scheme for 5G Waveforms", "Abstract": "The implementation of Peak Average to Power Ratio (PAPR) reduction technologies will play an important role in the regularization of Fifth Generation (5G) radio communication. PAPR reduction in the advanced waveform will be the key part of designing a 5G network for different applications. This work introduces the simulation of an Advanced Partial Transmission Sequence (A-PTS) reduction techniques for Orthogonal Frequency Division Multiplexing (OFDM) and Filter Bank Multi-Carrier (FBMC) transmission schemes. In the projected A-PTS, the FBMC signals are mapped into the number of sub-blocks and Inverse Fast Fourier transform (IFFT) is performed to estimate the high peak power in the time domain. The FBMC sub-blocks are multiplied with the phase elements to achieve an optimal PAPR value. A MATLAB 2014v simulation is used to estimate the PAPR, Bit Error Rate (BER), Error Vector Magnitude (EVM), and Modulation Error Rate (MER) performance of the proposed reduction schemes. The simulated result reveals that the performance of the projected algorithm is better than the conventional algorithms. © 2021 CRL Publishing. All rights reserved.", "Keywords": "5G; FBMC; OFDM; PAPR; PTS", "DOI": "10.32604/csse.2022.022899", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, JECRC University, Jaipur, 303905, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Kennesaw State UniversityGA, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics and Communication Engineering, CMR Institute of Technology, Bangalore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computers and Information Technology, Taif University11099, Saudi Arabia"}, {"AuthorId": 5, "Name": "Sultan <PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computers and Information Technology, Taif University11099, Saudi Arabia"}], "References": [{"Title": "A comprehensive study of PAPR reduction techniques: design of DSLM-CT joint reduction technique for advanced waveform", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "16", "Page": "11893", "JournalTitle": "Soft Computing"}, {"Title": "PTS-PAPR Reduction Technique for 5G Advanced Waveforms Using BFO Algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "3", "Page": "713", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "An Efficient Genetic Hybrid PAPR Technique for 5G Waveforms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "3", "Page": "3283", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "An Efficient Hybrid PAPR Reduction for 5G NOMA-FBMC Waveforms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "2967", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 92380381, "Title": "Diabetic Retinopathy Diagnosis Using ResNet with Fuzzy Rough C-Means Clustering", "Abstract": "Diabetic Retinopathy (DR) is a vision disease due to the long-term prevalence of Diabetes Mellitus. It affects the retina of the eye and causes severe damage to the vision. If not treated on time it may lead to permanent vision loss in diabetic patients. Today’s development in science has no medication to cure Diabetic Retinopathy. However, if diagnosed at an early stage it can be controlled and permanent vision loss can be avoided. Compared to the diabetic population, experts to diagnose Diabetic Retinopathy are very less in particular to local areas. Hence an automatic computer-aided diagnosis for DR detection is necessary. In this paper, we propose an unsupervised clustering technique to automatically cluster the DR into one of its five development stages. The deep learning based unsupervised clustering is made to improve itself with the help of fuzzy rough c-means clustering where cluster centers are updated by fuzzy rough c-means clustering algorithm during the forward pass and the deep learning model representations are updated by Stochastic Gradient Descent during the backward pass of training. The proposed method was implemented using python and the results were taken on DGX server with Tesla V100 GPU cards. An experimental result on the publically available Kaggle dataset shows an overall accuracy of 88.7%. The proposed model improves the accuracy of DR diagnosis compared to the existing unsupervised algorithms like k-means, FCM, auto-encoder, and FRCM with alexnet. © 2021 CRL Publishing. All rights reserved.", "Keywords": "Clustering; Diabetic retinopathy detection; Diabetic retinopathy diagnosis; Fuzzy rough c-means clustering; Unsupervised CNN", "DOI": "10.32604/csse.2022.021909", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>a Engineering College, Coimbatore, 641022, India"}], "References": []}, {"ArticleId": 92380404, "Title": "How does information overload about COVID-19 vaccines influence individuals’ vaccination intentions? The roles of cyberchondria, perceived risk, and vaccine skepticism", "Abstract": "<p>This research proposes and tests an integrated model to explain how information overload influence vaccine skepticism and vaccination intention. In addition, this research investigates the effectiveness of using a celebrity endorsement strategy in promoting vaccination and compares its effectiveness with other endorsement types. A survey study (Study 1) was conducted to examine the mechanism underlying the impact of the COVID-19 vaccine information overload on vaccine skepticism that, subsequently, affects vaccination intention. It also examined the moderating role of celebrity endorsement trustworthiness. The results indicate that information overload positively influenced vaccine skepticism through cyberchondria and perceived risk of the vaccine, which subsequently reduces vaccination intention. The negative effect of vaccine skepticism on vaccination intention was weakened by the celebrity endorsement that was considered trustworthy. A follow-up experimental study (Study 2) was performed to compare the effectiveness of celebrity endorsement with other endorsement types (i.e., government official and medical expert endorsements). The results showed that the celebrity endorsement was more effective in mitigating the negative effect of vaccine skepticism on vaccination intention compared to government official and medical expert. The findings provide practical insights into how governments can minimize people's vaccine skeptical views and increase their vaccination intentions.</p><p>© 2021 Published by Elsevier Ltd.</p>", "Keywords": "Cyberchondria;Endorsement;Information overload;Perceived risk;Vaccination intention;Vaccine skepticism", "DOI": "10.1016/j.chb.2021.107176", "PubYear": 2022, "Volume": "130", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Business Administration, National Dong Hwa University, No. 1, Sec. 2, Da <PERSON> Rd, Shoufeng, Hualien, 97401, Taiwan, ROC."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Marketing, International Business and Strategy, Goodman School of Business, Brock University, 1812 Sir <PERSON>, St. Catharines, ON L2S3A1, Canada."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, National Dong Hwa University, No. 1, Sec. 2, Da <PERSON> Rd, Shoufeng, Hualien, 97401, Taiwan, ROC."}], "References": [{"Title": "Cyberpsychology research and COVID-19", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "106357", "JournalTitle": "Computers in Human Behavior"}, {"Title": "COVID-19 and digital inequalities: Reciprocal impacts and mitigation strategies", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "106424", "JournalTitle": "Computers in Human Behavior"}, {"Title": "What drives unverified information sharing and cyberchondria during the COVID-19 pandemic?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "288", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Exploring information avoidance intention of social media users: a cognition–affect–conation perspective", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "5", "Page": "1455", "JournalTitle": "Internet Research"}, {"Title": "Cyberchondria: a systematic review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "677", "JournalTitle": "Internet Research"}]}, {"ArticleId": 92380574, "Title": "Multiband decomposition and spectral discriminative analysis for motor imagery BCI via deep neural network", "Abstract": "<p>Human limb movement imagery, which can be used in limb neural disorders rehabilitation and brain-controlled external devices, has become a significant control paradigm in the domain of brain-computer interface (BCI). Although numerous pioneering studies have been devoted to motor imagery classification based on electroencephalography (EEG) signal, their performance is somewhat limited due to insufficient analysis of key effective frequency bands of EEG signals. In this paper, we propose a model of multiband decomposition and spectral discriminative analysis for motor imagery classification, which is called variational sample-long short term memory (VS-LSTM) network. Specifically, we first use a channel fusion operator to reduce the signal channels of the raw EEG signal. Then, we use the variational mode decomposition (VMD) model to decompose the EEG signal into six band-limited intrinsic mode functions (BIMFs) for further signal noise reduction. In order to select discriminative frequency bands, we calculate the sample entropy (SampEn) value of each frequency band and select the maximum value. Finally, to predict the classification of motor imagery, a LSTM model is used to predict the class of frequency band with the largest SampEn value. An open-access public data is used to evaluated the effectiveness of the proposed model. In the data, 15 subjects performed motor imagery tasks with elbow flexion/extension, forearm supination/pronation and hand open/close of right upper limb. The experiment results show that the average classification result of seven kinds of motor imagery was 76.2%, the average accuracy of motor imagery binary classification is 96.6% (imagery vs. rest), respectively, which outperforms the state-of-the-art deep learning-based models. This framework significantly improves the accuracy of motor imagery by selecting effective frequency bands. This research is very meaningful for BCIs, and it is inspiring for end-to-end learning research.</p>", "Keywords": "brain computer interface; EEG; long short-term emory; VMD; sample entropy; motor imagery", "DOI": "10.1007/s11704-021-0587-2", "PubYear": 2022, "Volume": "16", "Issue": "5", "JournalId": 4733, "JournalTitle": "Frontiers of Computer Science", "ISSN": "2095-2228", "EISSN": "2095-2236", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, MIIT Key Laboratory of Pattern Analysis and Machine Intelligence, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, MIIT Key Laboratory of Pattern Analysis and Machine Intelligence, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, MIIT Key Laboratory of Pattern Analysis and Machine Intelligence, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, MIIT Key Laboratory of Pattern Analysis and Machine Intelligence, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, MIIT Key Laboratory of Pattern Analysis and Machine Intelligence, Nanjing, China"}], "References": [{"Title": "Identification of epileptic seizures in EEG signals using time-scale decomposition (ITD), discrete wavelet transform (DWT), phase space reconstruction (PSR) and neural networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "4", "Page": "3059", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 92380654, "Title": "Visual Collaboration Leader-Follower UAV-Formation for Indoor Exploration", "Abstract": "<p>UAVs operating in a leader-follower formation demand the knowledge of the relative pose between the collaborating members. This necessitates the RF-communication of this information which increases the communication latency and can easily result in lost data packets. In this work, rather than relying on this autopilot data exchange, a visual scheme using passive markers is presented. Each formation-member carries passive markers in a RhOct configuration. These markers are visually detected and the relative pose of the members is on-board determined, thus eliminating the need for RF-communication. A reference path is then evaluated for each follower that tracks the leader and maintains a constant distance between the formation-members. Experimental studies show a mean position detection error (5 × 5 × 10cm) or less than 0.0031% of the available workspace [0.5 up to 5m, 50.43° × 38.75° Field of View (FoV)]. The efficiency of the suggested scheme against varying delays are examined in these studies, where it is shown that a delay up to 1.25s can be tolerated for the follower to track the leader as long as the latter one remains within its FoV.</p>", "Keywords": "UAV; Drone; Passive marker; swarm; arUco; Leader follower; Relative localization; indoor localization; Indoor Exploration Tasks; Rhombicuboctahedron", "DOI": "10.3389/frobt.2021.777535", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Robotics and Intelligent Systems Control (RISC) Lab, Electrical and Computer Engineering Department, New York University Abu Dhabi, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering Department, New York University, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Robotics and Intelligent Systems Control (RISC) Lab, Electrical and Computer Engineering Department, New York University Abu Dhabi, United Arab Emirates"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Robotics and Intelligent Systems Control (RISC) Lab, Electrical and Computer Engineering Department, New York University Abu Dhabi, United Arab Emirates"}], "References": [{"Title": "UWB-Based Localization for Multi-UAV Systems and Collaborative Heterogeneous Multi-Robot Systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "175", "Issue": "", "Page": "357", "JournalTitle": "Procedia Computer Science"}, {"Title": "Visual Odometry Using Pixel Processor Arrays for Unmanned Aerial Systems in GPS Denied Environments", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "126", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 92380707, "Title": "A robust occlusion-adaptive attention-based deep network for facial landmark detection", "Abstract": "<p>The Internet of Things (IoT) has extensively transformed the industry. The innovation of 5G technology and its rapid growth have enabled fast communication between IoT devices and the cyber domain. Technological advancement and the desire for ease of life have resulted in the development of the concept of smart cities. Security is one of the prime objectives of smart cities. The surveillance video management system is rapidly expanding its scope and applications. The use of 5G technology in smart cities enables the integration of real-time video observations with access to specific locations. This allows facial recognition to detect known criminals or a person of interest in a crowd. Facial landmark detection (FLD) is an essential step in facial attribute analysis, the face recognition pipeline, and face verification. Currently, researchers are focusing on convolutional neural network (CNN) based facial landmark detection approaches, and they have attained substantial advancement. However, occlusion is still the leading cause of difficulty impeding the ability of convolutional neural networks to achieve accurate results. Because attention plays a vital role in the human visual system, the significance regarding rich feature representation in computer vision problems has been recently proved by researchers. In this paper, an occlusion-adaptive attentive deep network (OADN) is proposed for facial landmark detection. In short, we extend our already well-established occlusion-adaptive deep network (ODN) by modifying the geometry-aware module (GM) and distillation module (DM). The results of our experiments show that our proposed model outperforms the current state-of-the-art methods on the available benchmark datasets. It reduces the error from 4.17 to 3.82 for the 300W Full-set dataset. After training on the Menpo dataset, the error decreases to 3.63, this is a 13% decrease in error compared to that of the ODN. In addition, we perform a statistical analysis with a 95% confidence interval to validate the effectiveness of our proposed methodology. Our method reduces the total number of network parameters from 6.6 million to 5.46 million, an approximately 16% decrease in network parameters, effectively reducing the training time and cost. Hence, it is more suitable for scalable data processing. Furthermore, taking advantage of our proposed model’s inherently low weight, we also propose a distributive facial recognition model for 5G camera-based surveillance systems.</p>", "Keywords": "Facial landmark detection; Cyber-physical system; Attention; Spatial attention; Channel-wise attention; Facial recognition", "DOI": "10.1007/s10489-021-02848-8", "PubYear": 2022, "Volume": "52", "Issue": "8", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Guangdong Province, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Guangdong Province, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Nanyang Technological University, Singapore, Singapore"}], "References": [{"Title": "Siamese attentional keypoint network for high performance visual tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "193", "Issue": "", "Page": "105448", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Learning reinforced attentional representation for end-to-end visual tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "52", "JournalTitle": "Information Sciences"}, {"Title": "Heterogenous output regression network for direct face alignment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107311", "JournalTitle": "Pattern Recognition"}, {"Title": "A data integrity verification method for surveillance video system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "41-42", "Page": "30163", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multi-modal 3D object detection by 2D-guided precision anchor proposal and multi-layer fusion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107405", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 92380709, "Title": "Effect of copper donor material-assisted friction stir welding of AA6061-T6 alloy on downward force, microstructure, and mechanical properties", "Abstract": "<p>In this research, copper (Cu) donor material-assisted friction stir welding (FSW) of AA6061-T6 alloy was studied. Cu-assisted FSW joints of AA6061-T6 alloy were prepared at a constant tool rotational rate of 1400 rpm and various welding speeds at 1 mm/s and 3 mm/s. The Cu donor material of different thickness (i.e., 20%, 40%, and 60%) with respect to the workpiece thickness was selected to assist the FSW joining at the plunge stage. It is observed that the downward force generated in the FSW process was gradually decreased after introducing Cu donor material with incremental thicknesses with respect to workpiece at the plunge stage. Post-weld analysis was characterized in terms of microstructure and mechanical properties. The results of microstructure analysis at the stir zone (SZ) show the formation of finer grains due to dynamic recrystallization and plastic deformation. Micro-hardness tests reveal that the hardness decreased from the base metal (BM) to the SZ across the heat-affected zone (HAZ) and thermo-mechanically affected zone (TMAZ). The lowest value of hardness appeared in the TMAZ and HAZ where tensile failure occurs. With increasing welding speed, the average hardness in the SZ decreased due to lower heat input and faster cooling rate. Tensile test plots show no significant change in ultimate tensile strength with or without Cu donor material. Fractography of tensile tested samples shows both ductile and brittle like structure for given welding parameters. This proposed work of FSW with Cu donor material is promising to increase tool life due to the decrement of the downforce during plunge and throughout the welding stage. Meanwhile, the inclusion of donor material did not compromise the weld quality in terms of the mechanical properties and micro-hardness.</p>", "Keywords": "Al6061-T6 alloy; Cu donor material; Friction stir welding; Microstructure; Micro-hardness; Tensile properties; Fractography", "DOI": "10.1007/s00170-021-08390-8", "PubYear": 2022, "Volume": "119", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering, Virginia State University, Petersburg, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Wu", "Affiliation": "Department of Engineering, Virginia State University, Petersburg, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, Old Dominion University, Norfolk, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, Old Dominion University, Norfolk, USA"}], "References": []}, {"ArticleId": 92380715, "Title": "Reconstruction of aesthetically smooth curves", "Abstract": "<p>We present a new method for reconstructing aesthetically smooth curves with a focus on engineering applications in the automotive industry. The condition of curvature monotonicity is an important design principle. However, its flexibility is insufficient to create curves in automotive industry applications. Therefore, in this study, we reconstruct a curve based on the curvature convexity such that the curve has a curvature graph that is close to a quadratic function. Consequently, the resulting curve can exhibit curvature monotonicity or convexity and can be flexibly represented based on the situation. The experimental results demonstrate the validity of the proposed method by comparing the obtained results with those created by CAD experts.</p>", "Keywords": "Curvature convexity; Parabolic curvature graph; Feature line; Underlying curve; Reverse engineering", "DOI": "10.1007/s00371-021-02334-9", "PubYear": 2023, "Volume": "39", "Issue": "1", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nihon Unisys, Ltd., Koto-ku, Japan"}], "References": [{"Title": "Reconstruction of intersecting surface models from scanned data for styling design", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "211", "JournalTitle": "Engineering with Computers"}, {"Title": "Reconstruction of adaptive swept surfaces from scanned data for styling design", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "493", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 92380718, "Title": "Autonomous measurement and semantic segmentation of non-cooperative targets with deep convolutional neural networks", "Abstract": "<p>In spatial missions, it is important to estimate kinematic state and to identify the shape of non-cooperative targets. In order to improve the accuracy and generality of the existing algorithm, a real-time recognition and detection method for tumbling non-cooperative target is proposed. First, we design a key point detection network to identify non-cooperative targets and their feature points in the process of long-distance non-cooperative target recognition. At the same time, the detected image is combined with the PNP algorithm to obtain the target’s 6D attitude. Then, a BiSeNet based model is trained for real-time semantic segmentation of satellite components as the base satellite is pursued close to its target. The segmented image is selected with depth information and the relative position of the capturing point is transmitted to the manipulator. Finally, we complete physical experiments under different lighting conditions with a spinning non-cooperative target on a 6-DOF air-bearing table. The experimental results show that the satellite recognition accuracy and object segmentation accuracy are 99.48% and 98.11%, respectively. The position measurement error is less than 1 mm, which achieves more than 50% improvement over the conventional methods.</p>", "Keywords": "Feature extraction; Non-cooperative target; Pose measurement; Semantic segmentation; Convolutional neural networks", "DOI": "10.1007/s12652-021-03553-7", "PubYear": 2023, "Volume": "14", "Issue": "6", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Du", "Affiliation": "Beijing Institute of Control Engineering, National Key Laboratory of Science and Technology On Space Intelligent Control, Beijing, China"}, {"AuthorId": 2, "Name": "Haidong Hu", "Affiliation": "Beijing Institute of Control Engineering, National Key Laboratory of Science and Technology On Space Intelligent Control, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Spacecraft System Engineering, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Control Engineering, National Key Laboratory of Science and Technology On Space Intelligent Control, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Control Engineering, National Key Laboratory of Science and Technology On Space Intelligent Control, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Wei", "Affiliation": "Beijing Institute of Control Engineering, National Key Laboratory of Science and Technology On Space Intelligent Control, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Control Engineering, National Key Laboratory of Science and Technology On Space Intelligent Control, Beijing, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "College of Automation, College of Artificial Intelligence, Nanjing University of Posts and Communications, Nanjing, China"}], "References": []}, {"ArticleId": 92380929, "Title": "ElasT: A toolkit for thermoelastic calculations", "Abstract": "We have automated the stress-strain method to derive elastic constants from ab initio calculations. We found the stress-strain method implemented under our constant pressure dynamics <PERSON>lkit can provide almost identical results to the implementation under constant volume dynamics. The computational load is greatly reduced under constant pressure dynamics while remaining sufficiently accurate. This <PERSON>lk<PERSON> will prove invaluable to those carrying out materials and mineral physics research, where Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgement Y. Li thanks the support from CAS Hundred Talents Program. We acknowledge the support from NERC grant NE/M000125/1 . Computation was performed at the ARCHER supercomputer facility. The authors declare no conflicts of interest. This research was also supported by the advanced computing resources provided by the Supercomputing Center of the USTC . References (31) G<PERSON><PERSON><PERSON> J. Phys. Chem. Solids (1974) T<PERSON>R<PERSON> et al. Acta Metall. (1957) <PERSON><PERSON> et al. Comput. Phys. Commun. (2011) Y. Li et al. Earth Planet. Sci. Lett. (2018) Z.-L<PERSON> et al. Comput. Phys. Commun. (2017) <PERSON><PERSON> et al. Ceram. Int. (2021) <PERSON><PERSON> et al. Comput. Phys. Commun. (2021) <PERSON><PERSON> et al. Phys. Rev. (1965) <PERSON><PERSON> et al. Dynamical Theory of Crystal Lattices (1988) T<PERSON> et al. J. Appl. Phys. (2012) J. Bouchet et al. Phys. Rev. B (2015) G. Simmons et al. Single Crystal Elastic Constants and Calculated Aggregate Properties: A Handbook (1971) J.F. Nye Physical Properties of Crystals: Their Representation by Tensors and Matrices (1985) M. Parrinello et al. J. Chem. Phys. (1982) A.A. Gusev et al. Phys. Rev. B (1996) View more references Cited by (0) Recommended articles (6) <sup> ☆ </sup> The review of this paper was arranged by Prof. Hazel Andrew. <sup> ☆☆ </sup> This paper and its associated computer program are available via the Computer Physics Communications homepage on ScienceDirect ( http://www.sciencedirect.com/science/journal/00104655 ). View full text © 2022 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2022 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.cpc.2021.108280", "PubYear": 2022, "Volume": "273", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Earth Sciences, UCL, Gower Street, London, WC1E 6BT, UK;CAS Key Laboratory of Crust–Mantle Materials and Environments, School of Earth and Space Sciences, University of Science and Technology of China, Hefei, Anhui 230026, China;CAS Center for Excellence in Comparative Planetology, USTC, Hefei, Anhui 230026, China;Corresponding author at: CAS Key Laboratory of Crust–Mantle Materials and Environments, School of Earth and Space Sciences, University of Science and Technology of China, Hefei, Anhui 230026, China"}, {"AuthorId": 2, "Name": "Lidunka Vočadlo", "Affiliation": "Department of Earth Sciences, UCL, Gower Street, London, WC1E 6BT, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Earth Sciences, UCL, Gower Street, London, WC1E 6BT, UK;Centre for Earth Evolution and Dynamics, University of Oslo, Oslo, Norway"}], "References": [{"Title": "cij: A Python code for quasiharmonic thermoelasticity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "267", "Issue": "", "Page": "108067", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 92380942, "Title": "Optimization algorithms in wireless monitoring networks: A survey", "Abstract": "Wireless network has emancipated people from the bondage of wired network and enhanced the quality of human life. However, there remain a few areas pertaining to wireless network that require attention, such as network congestion, low communication reliability and security. With the increasing scale and extended applications of Internet of Things (IoT), there is a growing demand for reliability, stability and security within the network. Wireless network monitoring is an effective system put in place which involves distributed sniffers that capture the transmitted data of wireless users, facilitating status analysis, fault diagnosis and resource management of the network system. Due to the limited number of sniffers, optimization of hardware configuration and channel assignment of sniffers is paramount, which can significantly improve the amount of captured data and the monitoring quality of the wireless network. Primarily, the concept, classification and characteristics of wireless network monitoring are introduced. Secondarily, the application of optimization algorithms in wireless network monitoring, particularly the channel selection algorithms during the data collection process and the channels and time-slot scheduling algorithms during the data aggregation process are summarized. Finally, the challenges faced when building a wireless network monitoring are discussed and to conclude prospects aimed towards the development of this research field are put forward.", "Keywords": "Channel assignment ; Optimization Algorithm ; Wireless sniffer ; Wireless network monitoring", "DOI": "10.1016/j.neucom.2021.12.072", "PubYear": 2022, "Volume": "489", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei 230009, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 5, "Name": "Yuqing Chen", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 7, "Name": "Huazheng Du", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Jikan Research Institute of Engineering Investigations and Design Company Ltd., Xi’an 710043, China"}], "References": [{"Title": "Channel allocation optimization algorithm for hybrid wireless mesh networks for information physical fusion system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "212", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 92380979, "Title": "A relativistic UGKS for stimulated Raman scattering in two dimension", "Abstract": "To study the kinetic behaviour of electrons in laser-plasma interaction with the consideration of Coulomb collisions, a modified UGKS(unified gas-kinetic scheme) is proposed to solve the collisional Vlasov Maxwell equations for stimulated Raman scattering in two dimension. Based on Strang splitting scheme, the equation is divided into two parts, the transport-collision one and acceleration one. The transport-collision part is solved by a modified UGKS with relativistic effect, the acceleration part is solved by semi-Lagrangian scheme, and the Maxwell equations is discretized by leap-frog scheme. Quadratic B-spline reconstruction is used in the calculation of numerical flux to achieve high order accuracy in space. Validity is tested by a series of typical problems, including the stimulated Raman scattering.", "Keywords": "<PERSON><PERSON><PERSON>–<PERSON> system ; Coulomb collisions ; Relativistic UGKS ; Stimulated Raman scattering", "DOI": "10.1016/j.compfluid.2021.105261", "PubYear": 2022, "Volume": "235", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Nuclear Energy Safety Technology, Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei 230031, China;University of Science and Technology of China, Hefei 230026, China;Graduate school of China Academy of Engineering Physics, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Applied Physics and Computational Mathematics, Beijing, 100088, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Applied Physics and Computational Mathematics, Beijing, 100088, China"}], "References": []}, {"ArticleId": 92380988, "Title": "Defending against adversarial attacks using spherical sampling-based variational auto-encoder", "Abstract": "Although deep neural networks achieve outstanding performance in many tasks, adding very imperceptible perturbations to clean images can easily fool the deep neural network. In this paper, we propose a new defence model: Adversarial Memory Variational AutoEncoder(AdMVAE), that can be used to transform adversarial images into clean images. At inference time, it finds an output that is similar to a given image in a high probability region of the manifold space. And the memory module uses normal features to reconstruct the image in the process of reconstruction. It can effectively prevent the reconstruction of malicious perturbations and avoid defense failure. Our approach is a pre-processing module that does not change the results of the classifier. Therefore, it can be combined with other defence models to jointly improve the performance robustness of the classifier. The experimental results on three benchmark datasets including Fashion-MNIST, CIFAR10 and Imagenet show that the proposed method outperforms the state-of-the-art defense methods.", "Keywords": "Adversarial defense ; Adversarial attack ; Deep Learning ; Memory module ; Security", "DOI": "10.1016/j.neucom.2021.12.080", "PubYear": 2022, "Volume": "478", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China;Beijing Key Laboratory of Trusted Computing, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China;Beijing Key Laboratory of Trusted Computing, Beijing, China;Corresponding author at: Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China;Beijing Key Laboratory of Trusted Computing, Beijing, China"}], "References": []}, {"ArticleId": 92380990, "Title": "Maximum margin Riemannian manifold-based hyperdisk for fault diagnosis of roller bearing with multi-channel fusion covariance matrix", "Abstract": "For rotating machinery, the sudden failure of roller bearing would lead to the downtime of the whole system and even catastrophic accidents. Therefore, multiple accelerometers are usually arranged to comprehensively evaluate the health of roller bearing, enhancing the stability and reliability of monitoring results. This paper proposes a novel fault diagnosis framework by utilizing a multi-channel fusion covariance matrix (MFCM) and Riemannian manifold-based hyperdisk. First, 22 statistical features are acquired from each channel data. Then, MFCM is calculated as the fault feature representation of roller bearing to achieve multi-channel feature fusion, where the element of MFCM represents the correlation information between different channels. Finally, since MFCM is a symmetric positive definite (SPD) matrix, lying on a Riemannian manifold, we design a maximum margin Riemannian manifold-based hyperdisk (MMRMHD) classifier to conduct fault classification, where Log-Euclidean metric (LEM) is introduced to calibrate the distribution of MFCMs. Moreover, to further improve the classification ability of nonlinear SPD data, we map MFCMs into a high-dimensional Hilbert space with the LEM-based kernel function and construct a novel kernelized MMRMHD model. The experimental results on two bearing datasets with multi-channel vibration signals demonstrate the effectiveness and superiority of the proposed fault diagnosis framework.", "Keywords": "Roller bearing ; Fault diagnosis ; Maximum margin Riemannian manifold-based hyperdisk ; Multi-channel fusion covariance matrix", "DOI": "10.1016/j.aei.2021.101513", "PubYear": 2022, "Volume": "51", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Design and Manufacturing for Vehicle Body, Hunan University, Changsha 410082, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Advanced Design and Manufacturing for Vehicle Body, Hunan University, Changsha 410082, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "Niaoqing Hu", "Affiliation": "Laboratory of Science and Technology on Integrated Logistics Support, National University of Defense Technology, Changsha 410082, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Science and Technology on Integrated Logistics Support, National University of Defense Technology, Changsha 410082, PR China"}, {"AuthorId": 5, "Name": "Haidong Shao", "Affiliation": "State Key Laboratory of Advanced Design and Manufacturing for Vehicle Body, Hunan University, Changsha 410082, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Design and Manufacturing for Vehicle Body, Hunan University, Changsha 410082, PR China"}], "References": [{"Title": "Symplectic interactive support matrix machine and its application in roller bearing condition monitoring", "Authors": "<PERSON><PERSON> Pan; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "398", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Fault diagnosis using novel AdaBoost based discriminant locality preserving projection with resamples", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "103631", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Extensible and displaceable hyperdisk based classifier for gear fault intelligent diagnosis", "Authors": "<PERSON><PERSON><PERSON> Hu; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "204", "Issue": "", "Page": "106250", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A spiking neural network-based approach to bearing fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "714", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "An intelligent fault diagnosis method for rotor-bearing system using small labeled infrared thermal images and enhanced CNN transferred from CAE", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "", "Page": "101150", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Intelligent fault diagnosis for rolling bearings based on graph shift regularization with directed graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101253", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A novel approach of multisensory fusion to collaborative fault diagnosis in maintenance", "Authors": "<PERSON><PERSON> Shao; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "74", "Issue": "", "Page": "65", "JournalTitle": "Information Fusion"}, {"Title": "Automatic representation and detection of fault bearings in in-wheel motors under variable load conditions", "Authors": "Xi<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101321", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "An improved convolutional neural network with an adaptable learning rate towards multi-signal fault diagnosis of hydraulic piston pump", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "", "Page": "101406", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 92381036, "Title": "Towards data sharing economy on Internet of Things: a semantic for telemetry data", "Abstract": "Internet of Things (IoT) provides data processing and machine learning techniques with access to physical world data through sensors, namely telemetry data. Acquiring sensor data through IoT faces challenges such as connectivity and proper measurement requiring domain-specific knowledge, that results in data quality problems. Data sharing is one solution to this. In this work, we propose IoT Telemetry Data Hub (IoT TeleHub), a general framework and semantic for telemetry data collection and sharing. The framework is principled on abstraction, layering of elements, and extensibility and openness. We showed that while the framework is defined specifically for telemetry data, it is general enough to be mapped to existing IoT platforms with various use cases. Our framework also considers the machine-readable and machine-understandable notion in regard to resource-constrained IoT devices. We also present IoThingsHub, an IoT platform for real-time data sharing based on the proposed framework. The platform demonstrated that the framework could be implemented with existing technologies such as HTTP, MQTT, SQL, NoSQL.", "Keywords": "Internet of Things;Data sharing;Data semantic;Interoperability;Platform;Framework", "DOI": "10.1186/s40537-021-00549-0", "PubYear": 2022, "Volume": "9", "Issue": "1", "JournalId": 2151, "JournalTitle": "Journal of Big Data", "ISSN": "", "EISSN": "2196-1115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Computer Engineering, Universitas Multimedia Nusantara, Tangerang, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Engineering, Universitas Multimedia Nusantara, Tangerang, Indonesia"}], "References": [{"Title": "Context information sharing for the Internet of Things: A survey", "Authors": "Everton de Matos; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "166", "Issue": "", "Page": "106988", "JournalTitle": "Computer Networks"}, {"Title": "Industrial internet of things: Recent advances, enabling technologies and open challenges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "81", "Issue": "", "Page": "106522", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "SICE: an improved missing data imputation technique", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "37", "JournalTitle": "Journal of Big Data"}, {"Title": "IoT reliability: a review leading to 5 key research directions", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "3", "Page": "147", "JournalTitle": "CCF Transactions on Pervasive Computing and Interaction"}]}, {"ArticleId": 92381052, "Title": "Optimal Algorithms for Load Balancing in Optical Burst Switching Networks", "Abstract": "Data packet drop can happen in Optical Burst-Switched (OBS) when two data bursts are competing on the same wavelength. Recently, many techniques have been developed to solve this problem but they do not consider the congestion. Also, it is necessary to balance the load system in the OBS network. The Ant Colony Optimization (ACO) technique can be applied to determine the straight and the safest route. However, the ACO technique raises both power utilization as well as the execution time. In this study, Cuckoo Search (CS) and ACO methods based approach is proposed to avoid the congestion and load balancing in the OBS network. This strategy evaluates the intensity of hotspot data and then launches the congestion rate optimization process that depends on their load situations. The congestion rate optimization represents the available bandwidth, data distribution rate, queue size, and access rate, and also these factors are optimized through the CS technique. The fitness utility in the CS technique adjusts the distribution rate in the OBS network, and the proposed ACO technique solves the energy utilization problem. The simulation results proved that the presented strategy evades both the end-to-end delay as well as the possibility of packet drop. © 2021 CRL Publishing. All rights reserved.", "Keywords": "Ant colony optimization; Congestion control; Cuckoo search; Load balancing; Optical burst-switched", "DOI": "10.32604/csse.2022.017577", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, Thiagarajar College of Engineering, Madurai, 625 015, India"}], "References": []}, {"ArticleId": 92381053, "Title": "Robust subset selection", "Abstract": "The best subset selection (or “best subsets”) estimator is a classic tool for sparse regression, and developments in mathematical optimization over the past decade have made it more computationally tractable than ever. Notwithstanding its desirable statistical properties, the best subsets estimator is susceptible to outliers and can break down in the presence of a single contaminated data point. To address this issue, a robust adaption of best subsets is proposed that is highly resistant to contamination in both the response and the predictors. The adapted estimator generalizes the notion of subset selection to both predictors and observations, thereby achieving robustness in addition to sparsity. This procedure, referred to as “robust subset selection” (or “robust subsets”), is defined by a combinatorial optimization problem for which modern discrete optimization methods are applied. The robustness of the estimator in terms of the finite-sample breakdown point of its objective value is formally established. In support of this result, experiments on synthetic and real data are reported that demonstrate the superiority of robust subsets over best subsets in the presence of contamination. Importantly, robust subsets fares competitively across several metrics compared with popular robust adaptions of continuous shrinkage estimators.", "Keywords": "Best subset selection ; Least trimmed squares ; Sparse regression ; Robust regression ; Discrete optimization ; Mixed-integer optimization", "DOI": "10.1016/j.csda.2021.107415", "PubYear": 2022, "Volume": "169", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Econometrics and Business Statistics, Monash University, VIC 3800, Australia"}], "References": []}, {"ArticleId": 92381133, "Title": "BIM-enabled learning for building systems and technology", "Abstract": "<p>This paper presents a series of educational case studies for the BIM-enabled pedagogical approaches for learning building systems and technology in the early stages of architectural education and provides evidence-based arguments about the influence of BIM on the students’ learning processes. Using a dual-channel pedagogical framework the study employed an object-oriented ontological approach tightly integrated with the parameterization of building components and their behaviors. Students experienced a fully BIM-enhanced course for learning fundamental concepts of building systems and technology where the creation of parametric BIM models was the main vessel for comprehensive understanding. The results show significant conceptual and practical advantages of BIM-enabled learning as well as the observed challenges in an educational context. The study also suggests positive educational transformations due to carefully devised BIM-based pedagogical frameworks for the understanding of building systems through parametric thinking and modeling. Based on a grounded theory approach, the findings are synthesized in a theoretical learning model including the systemic relationships between building technology content and parametric BIM methodology.</p>", "Keywords": "Building Information Modeling", "DOI": "10.36680/j.itcon.2022.001", "PubYear": 2022, "Volume": "27", "Issue": "", "JournalId": 70075, "JournalTitle": "Journal of Information Technology in Construction", "ISSN": "", "EISSN": "1874-4753", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Istanbul Technical University, Department of Architecture, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Istanbul Technical University, Department of Architecture, Turkey"}], "References": []}, {"ArticleId": 92381142, "Title": "Issue Information", "Abstract": "", "Keywords": "", "DOI": "10.1002/sys.21582", "PubYear": 2022, "Volume": "25", "Issue": "1", "JournalId": 2331, "JournalTitle": "Systems Engineering", "ISSN": "1098-1241", "EISSN": "1520-6858", "Authors": [], "References": []}]