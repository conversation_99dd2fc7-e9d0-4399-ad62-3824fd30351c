[{"ArticleId": 83252027, "Title": "Combining Clustering and Factor Analysis as Complementary Techniques", "Abstract": "<p>The study of driver behavior and associated accidents has been of interest to researchers and insurance companies. From the perspective of insurance companies, identifying factors that contribute to traffic violations plays a significant role in providing insurance quotes as it establishes the basis for charging appropriate insurance rates to customers. This study assesses the traffic violations intensity for 64 counties in the state of Florida, USA by using the publicly available traffic violations data set. This data set consists of 3,669,796 records with 11 attributes, which include race, gender, driver's age, type of driving violation, etc. The 187 types of traffic violations are categorized into 11 broad traffic violations categories. Two machine learning algorithms, factor analysis and k-means clustering, were applied in this study. After applying factor analysis, a new comprehensive traffic violation index (TVI) was developed, which quantified the traffic violation intensity of each county. All the counties in the data set were ranked with the TVI scores, and the counties with high TVI scores were identified. K-means clustering algorithm was then applied to the same data, and four clusters of counties were derived. The counties that were grouped in each cluster were compared with the TVI scores to check if the counties in each cluster had similar TVI scores. The counties with the highest TVI scores are found to be grouped in one cluster, followed by counties with the next high TVI scores in the second cluster, and so on. Thus, it is observed that there is a perfect match in the results of both models. They serve as two techniques complementary to each other, in that the k-means clustering method groups counties with comparable traffic violation intensities and factor analysis is able to also rank individual counties according to the TVI. These techniques have identified the counties with high traffic violation intensities, which helps the policymakers to take adequate measures for traffic management.</p>", "Keywords": "", "DOI": "10.4018/IJDA.2020070104", "PubYear": 2020, "Volume": "1", "Issue": "2", "JournalId": 71163, "JournalTitle": "International Journal of Data Analytics", "ISSN": "2644-1705", "EISSN": "2644-1713", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, University of West Florida, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Indian Institute of Chemical Technology, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Physics Department, University of West Florida, USA"}], "References": []}, {"ArticleId": 83252033, "Title": "A Machine Learning-Based Exploration of Relationship Between Security Vulnerabilities of IoT Devices and Manufacturers", "Abstract": "<p>The internet of things has brought in innovations in the daily lives of users. The enthusiasm and openness of consumers have fuelled the manufacturers to dish out new devices with more features and better aesthetics. In an attempt to keep up with the competition, the manufacturers are not paying enough attention to cyber security of these smart devices. The gravity of security vulnerabilities is further aggravated due to their connected nature. As a result, a compromised device would not only stop providing the intended service but could also act as a host for malware introduced by an attacker. This study has focused on 10 manufacturers, namely Fitbit, D-Link, Edimax, Ednet, Homematic, Smarter, Osram, Belkin Wemo, Philips Hue, and Withings. The authors studied the security issues which have been raised in the past and the communication protocols used by devices made by these brands. It was found that while security vulnerabilities could be introduced due to lack of attention to details while designing an IoT device, they could also get introduced by the protocol stack and inadequate system configuration. Researchers have iterated that protocols like TCP, UDP, and mDNS have inherent security shortcomings and manufacturers need to be mindful of the fact. Furthermore, if protocols like EAPOL or Zigbee have been used, then the device developers need to be aware of safeguarding the keys and other authentication mechanisms. The authors also analysed the packets captured during setup of 23 devices by the above-mentioned manufacturers. The analysis gave insight into the underlying protocol stack preferred by the manufacturers. In addition, they also used count vectorizer to tokenize the protocols used during device setup and use them to model a multinomial classifier to identify the manufacturers. The intent of this experiment was to determine if a manufacturer could be identified based on the tokenized protocols. The modelled classifier could then be used to drive an algorithm to checklist against possible security vulnerabilities, which are characteristic of the protocols and the manufacturer history. Such an automated system will be instrumental in regular diagnostics of a smart system. The authors then wrapped up this report by suggesting some measures a user can take to protect their local networks and connected devices.</p>", "Keywords": "", "DOI": "10.4018/IJDA.2020070101", "PubYear": 2020, "Volume": "1", "Issue": "2", "JournalId": 71163, "JournalTitle": "International Journal of Data Analytics", "ISSN": "2644-1705", "EISSN": "2644-1713", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Amity University, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Amity Institute of Information Technology, Amity University, India"}], "References": []}, {"ArticleId": 83252035, "Title": "A Review of Data Governance Definitions and Emerging Perspectives", "Abstract": "<p>The field of data governance is emerging globally. Although there is yet no universal definition of the field, the issue of data governance is important to governments, industries, institutions, and professionals across disciplines. This paper summarizes findings from a review of 50 data governance definitions from a cross-section of industries, institutions, and professional associations. Text analysis reveals varying definitions and levels of specificity, focus, and attitudes towards basic principles of control or prescription. Organizational culture is an additional factor likely reflected in semantics shaping the definitions. The paper makes recommendations for developing and designing definitions that are meaningfully aligned to the organization's mission and diverse stakeholders. The main contributions of the paper are 1) comprehensive summary of data governance definitions across industries; 2) role of specificity, focus, and attitudes underlying control; 3) framework to customize data governance definition to align with current data maturity and organizational mission and culture.</p>", "Keywords": "", "DOI": "10.4018/IJDA.2020070103", "PubYear": 2020, "Volume": "1", "Issue": "2", "JournalId": 71163, "JournalTitle": "International Journal of Data Analytics", "ISSN": "2644-1705", "EISSN": "2644-1713", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of South Carolina Upstate, USA"}, {"AuthorId": 2, "Name": "San Cannon", "Affiliation": "University of Rochester, USA"}], "References": []}, {"ArticleId": 83252038, "Title": "Bibliometric Analysis of Social Media as a Platform for Knowledge Management", "Abstract": "<p>The purpose of this study is to conduct a bibliometric analysis to examine the most influential journals, institutions, and countries in social media (SM) publications related to knowledge management (KM). Moreover, various research themes in SM KM publications are also explored. VOSviewer was employed to process 234 SM KM publications retrieved from Web of Science (WoS) in the time period 2009-2019. Different methodologies were used according to the nature of bibliometric analysis and explained in each section. Journal of Knowledge Management was the most influential journal in SM KM publications. USA and England ranked first and second respectively, while the Tampere University of Technology was the most productive institute in SM KM research. Four emerged themes indicated an explicit contribution of SM users in KM through big data, knowledge sharing, innovation, Enterprise 2.0, and social capital. This is the first bibliometric study that explores the overall contribution of SM publications in the KM field.</p>", "Keywords": "", "DOI": "10.4018/IJKM.2020070103", "PubYear": 2020, "Volume": "16", "Issue": "3", "JournalId": 31939, "JournalTitle": "International Journal of Knowledge Management", "ISSN": "1548-0666", "EISSN": "1548-0658", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, East China University of Science and Technology, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, East China University of Science and Technology, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Glorious Sun School of Business and Management, Donghua University, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Humanities and Social Sciences, Harbin Institute of Technology, Shenzhen, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management, School of Business, American University of Ras Al Khaimah, UAE"}], "References": []}, {"ArticleId": 83252366, "Title": "Character-level neural network model based on Nadam optimization and its application in clinical concept extraction", "Abstract": "Clinical concept extraction aims to quickly and effectively extract available data from complex and diverse clinical information, which is a crucial task for medical diagnosis using electronic medical records. Named entity recognition (NER) accurately marks essential information in clinical records based on the characteristics of the target entity, providing a way to extract clinical concepts. In the clinical concept extraction task, the existing methods are not satisfactory to obtain accurate labelling results in the face of large-scale and complex clinical information. To solve this problem, we improve and optimize a named entity recognition method based on the LSTM-CRF model. First, the improved deep neural network model uses two optional configurations of Convolutional Neural Network (CNN) and Bidirectional Long Short-Term Memory (BLSTM) to achieve character-level representation. Then the BLSTM layer obtains the context information of the target word, and the Conditional Random Field (CRF) gives constraints to ensure the standardization of the label. On this basis, <PERSON><PERSON> is used to optimize the training process of the network. The experimental results show that our new method has an F1 score of 84.61 on the public dataset of 2010 i2b2/VA concept extraction task, which exceeds the LSTM-CRF model. And the recall of 85.41 is ahead of all the methods evaluated on this data set.", "Keywords": "Deep neural networks ; Natural language processing ; Clinical concept extraction ; Named entity recognition", "DOI": "10.1016/j.neucom.2020.07.027", "PubYear": 2020, "Volume": "414", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan 250358, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Xu", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan 250358, China;State Key Laboratory of High-end Server & Storage Technology, Jinan 250101, China;Corresponding authors at: State Key Laboratory of High-end Server & Storage Technology, Jinan 250101, China. Shandong Normal University, Jinan 250358, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Business School, Shandong Normal University, Jinan 250358, China;State Key Laboratory of High-end Server & Storage Technology, Jinan 250101, China;Corresponding authors at: State Key Laboratory of High-end Server & Storage Technology, Jinan 250101, China. Shandong Normal University, Jinan 250358, China"}], "References": [{"Title": "Deep-reinforcement-learning-based images segmentation for quantitative analysis of gold immunochromatographic strip", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "425", "Issue": "", "Page": "173", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 83252400, "Title": "Discriminative comparison classifier for generalized zero-shot learning", "Abstract": "Comparison classifier based generalized zero-shot learning (GZSL) helps to achieve knowledge transfer from seen to unseen classes. However, it only utilizes the original semantic features, which are highly related and indistinguishable, to learn an embedding with no consideration of the relationship between them. Moreover, it cannot well encode discriminative information embedded in semantic features. To handle these problems, we present a discriminative comparison classifier for GZSL, which consists of semantic embedding network and relation network. Semantic embedding network takes as input original semantic features and relationship features which can be obtained by clustering, it ensures that the embedding network from semantic space to visual space can learn more discriminative features. Relation network is used to learn relationship between the embedded features and visual features, the validation information will guide embedding network to learn more discriminate features. Moreover, we adopt a novel semantic pivot regularization to keep inter-class discrimination in the visual space. Extensive experiments on several real-world datasets demonstrate the effectiveness of our method over the other state-of-the-arts.", "Keywords": "Generalized zero-shot learning ; Weakly-supervised learning ; Image classification", "DOI": "10.1016/j.neucom.2020.07.030", "PubYear": 2020, "Volume": "414", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "Mingzhen Hou", "Affiliation": "State Key Laboratory of Integrated Services Networks, Xidian University, Shaanxi 710071, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Integrated Services Networks, Xidian University, Shaanxi 710071, China;Corresponding authors"}, {"AuthorId": 3, "Name": "Xiangdong Zhang", "Affiliation": "State Key Laboratory of Integrated Services Networks, Xidian University, Shaanxi 710071, China;Corresponding authors;@qq.com"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Services Networks, Xidian University, Shaanxi 710071, China"}], "References": [{"Title": "Similarity preserving feature generating networks for zero-shot learning", "Authors": "Yuanbo Ma; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "406", "Issue": "", "Page": "333", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 83252563, "Title": "Engineering Intelligent Nanosystems for Enhanced Medical Imaging", "Abstract": "Medical imaging serves to obtain anatomical and physiological data, supporting medical diagnostics as well as providing therapeutic evaluation and guidance. A variety of contrast agents have been developed to enhance the recorded signals and to provide molecular imaging. However, fast clearance from the body or nonspecific biodistribution often limit their efficiency, constituting challenges that need to be overcome. Nanoparticle‐based systems are currently emerging as versatile and highly integrated platforms providing improved circulating times, tissue specificity, high loading capacity for signaling moieties, and multimodal imaging features. Furthermore, nanoengineered devices can be tuned for specific applications and the development of responsive behaviors. Responses include in situ modulation of nanoparticle size, increased intratissue mobility through active propulsion of motorized particles, and active modulation of the particle surroundings such as the extracellular matrix for an improved penetration and retention at the desired locations. Once accumulated in the targeted tissue, smart nanoparticle‐based contrast agents can provide molecular sensing of biomarkers or characteristics of the tissue microenvironment. In this case, the signal or contrast provided by the nanosystem is responsive to the presence or concentration of an analyte. Herein, recent developments of intelligent nanosystems to improve medical imaging are presented.", "Keywords": "", "DOI": "10.1002/aisy.202000087", "PubYear": 2020, "Volume": "2", "Issue": "10", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The Barcelona Institute of Science and Technology, Institute for Bioengineering of Catalonia (IBEC), Baldiri <PERSON> 10-12, Barcelona, 08028 Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The Barcelona Institute of Science and Technology, Institute for Bioengineering of Catalonia (IBEC), Baldiri <PERSON> 10-12, Barcelona, 08028 Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "CIC biomaGUNE, Basque Research and Technology Alliance, Paseo Miramon 182, San Sebastian, 20014 Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The Barcelona Institute of Science and Technology, Institute for Bioengineering of Catalonia (IBEC), Baldiri <PERSON> 10-12, Barcelona, 08028 Spain"}], "References": []}, {"ArticleId": 83252601, "Title": "A2Cloud‐RF\n : A random forest based statistical framework to guide resource selection for\n high‐performance\n scientific computing on the cloud", "Abstract": "<p>This article proposes a random‐forest based A2Cloud framework to match scientific applications with Cloud providers and their instances for high performance. The framework leverages four engines for this task: PERF engine, Cloud trace engine, A2Cloud‐ext engine, and the random forest classifier (RFC) engine. The PERF engine profiles the application to obtain performance characteristics, including the number of single‐precision (SP) floating‐point operations (FLOPs), double‐precision (DP) FLOPs, x87 operations, memory accesses, and disk accesses. The Cloud trace engine obtains the corresponding performance characteristics of the selected Cloud instances including: SP floating point operations per second (FLOPS), DP FLOPS, x87 operations per second, memory bandwidth, and disk bandwidth. The A2Cloud‐ext engine uses the application and Cloud instance characteristics to generate objective scores that represent the application‐to‐Cloud match. The RFC engine uses these objective scores to generate two types of random forests to assist users with rapid analysis: application‐specific random forests (ARF) and application‐class based random forests. The ARF consider only the input application's characteristics to generate a random forest and provide numerical ratings to the selected Cloud instances. To generate the application‐class based random forests, the RFC engine downloads the application profiles and scores of previously tested applications that perform similar to the input application. Using these data, the RFC engine creates a random forest for instance recommendation. We exhaustively test this framework using eight real‐world applications across 12 instances from different Cloud providers. Our tests show significant statistical agreement between the instance ratings given by the framework and the ratings obtained via actual Cloud executions.</p>", "Keywords": "capacity planning;cloud computing;cloud framework;scientific computing", "DOI": "10.1002/cpe.5942", "PubYear": 2020, "Volume": "32", "Issue": "24", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Engineering and Computer Science, University of the Pacific, Stockton, California, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Computer Science, University of the Pacific, Stockton, California, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Lawrence Livermore National Laboratory, Stockton, California, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Computer Science, University of the Pacific, Stockton, California, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, College of the Pacific, University of the Pacific, Stockton, California, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Computer Science, University of the Pacific, Stockton, California, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Engineering and Computer Science, University of the Pacific, Stockton, California, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>k K. Pallipuram", "Affiliation": "School of Engineering and Computer Science, University of the Pacific, Stockton, California, USA"}], "References": []}, {"ArticleId": 83252652, "Title": "Digital literacy and e-learning experiences among the pre-service teachers data", "Abstract": "The data show different issues connected with the digital literacy of pre-service teachers. The data were collected in 2019 among 450 teachers of pedagogical studies in Poland. The research was conducted in the biggest Polish university that trains educational staff, the Pedagogical University of Cracow. The data describe issues related to the self-evaluation of digital literacy in using text editors, spreadsheets, and presentation and graphic software. They also describe experiences with e-learning: participation in obligatory online classes, searching for information on the Internet, participation in paid and free e-learning courses, and participation in informal study groups.", "Keywords": "Digital literacy;E-learning;Information society;Poland;Pre-service teachers;Self-evaluation;Students;University", "DOI": "10.1016/j.dib.2020.106052", "PubYear": 2020, "Volume": "32", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Uniwersytet Pedagogiczny im. Komisji Edukacji Narodowej, Poland"}], "References": []}, {"ArticleId": 83252666, "Title": "Infinite horizon LQ Nash Games for SDEs with infinite jumps", "Abstract": "<p>In this paper, we consider infinite horizon linear-quadratic (LQ) Nash games for stochastic differential equations (SDEs) with infinite Markovian jumps and ( x , u , v )-dependent noise. An indefinite stochastic LQ result is first derived for the considered system. Then, under the condition of strong detectability, a necessary and sufficient condition for the existence of a Nash equilibrium is put forward in terms of the solvability of a countably infinite set of coupled generalized algebraic Riccati equations (ICGAREs). Moreover, the mixed H <sub>2</sub>/ H <sub> ∞ </sub> control is investigated by Nash game approach as an important application. At last, we present an iterative algorithm to solve the ICGAREs, and a numerical simulation is given to illustrate its efficiency.</p>", "Keywords": "Coupled generalized algebraic Riccati equations;Infinite Markovian jumps;Nash games;Stochastic differential equations;Strong detectability", "DOI": "10.1002/asjc.2371", "PubYear": 2021, "Volume": "23", "Issue": "5", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Systems Science, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Shandong Normal University, Jinan, China, Mathematics and Systems Science, Shandong University of Science and Technology, Qingdao, China"}], "References": []}, {"ArticleId": 83252690, "Title": "A brightness-preserving two-dimensional histogram equalization method based on two-level segmentation", "Abstract": "<p>Histogram equalization (HE) is a classical enhancement method for image processing. However, conventional HE techniques have poor performance in terms of preserving the brightness and natural appearance of images, meaning they typically fail to produce satisfactory results. A novel two-dimensional HE method with two-level segmentation for refining image brightness is proposed in this paper. Additionally, a modified two-dimensional histogram is generated to determine the locations of main segmentation points based on neighborhood matrices. The weights of the absolute brightness differences between low and high local contrast regions in this two-dimensional histogram are adjustable. After separating images into two main areas based on main segmentation points, multiple sub-segmentation points are selected based on a novel criterion derived from the maximum value distribution of the double histograms. Experimental results for various test images demonstrate that the proposed method achieves excellent performance in terms of brightness preservation and image contrast enhancement.</p>", "Keywords": "Image enhancement; Two-dimensional histogram; Histogram equalization; Brightness-preserving; Two-level segmentation", "DOI": "10.1007/s11042-020-09265-y", "PubYear": 2020, "Volume": "79", "Issue": "37-38", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Microelectronics, Tianjin University, Tianjin, China;School of Mathematical Sciences, Tianjin Normal University, Tianjin, China"}, {"AuthorId": 2, "Name": "Zaifeng Shi", "Affiliation": "School of Microelectronics, Tianjin University, Tianjin, China;Tianjin Key Laboratory of Imaging and Sensing Microelectronic Technology, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Microelectronics, Tianjin University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Microelectronics, Tianjin University, Tianjin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Microelectronics, Tianjin University, Tianjin, China"}], "References": []}, {"ArticleId": 83252735, "Title": "A moving vehicle tracking algorithm based on deep learning", "Abstract": "", "Keywords": "", "DOI": "10.1007/s12652-020-02352-w", "PubYear": 2020, "Volume": "", "Issue": "", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Multiple vehicle tracking and classification system with a convolutional neural network", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "3", "Page": "1603", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Combining a context aware neural network with a denoising autoencoder for measuring string similarities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "101028", "JournalTitle": "Computer Speech & Language"}, {"Title": "A fog based ball tracking (FB2T) system using intelligent ball bees", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "5735", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 83252812, "Title": "Efficiency improving of NEMS solar cell using piezoelectric nanowires", "Abstract": "<p>This paper presents a new structure of solar cells based on NEMS (Nano Electro Mechanical System) technology to eliminate dependence of cell to band gap energy and improve efficiency. In this paper, the process of converting solar energy to electrical energy using NEMS solar cell, which is able to convert the thermal to mechanical energy and then to electrical energy by changing the temperature and using direct piezoelectric effect, is investigated. Thus, unlike photovoltaic cells, these cells have ability to absorb the full spectrum of the sun’s radiation. Energy conversion is such that at first a two-layer cantilever with different thermal expansion coefficient (Al aluminum and SiO2 Silicon Oxide) is diverted by solar energy absorption, thus the solar energy is converted to mechanical energy. This structure is capable to receive heat energy from all spectrum of solar radiations. The mechanical oscillation of PZT (Lead Zirconate Titanate) nanowires, which are perpendicular to cantilever beams, is converted to electrical energy. The electrical voltage produced by the proposed structure is an alternating voltage with amplitude of 0.9 volts. Also, the efficiency of this proposed cell, which was introduced at first time and does not have a similar sample, is 40% which shows improved performance and much smaller size compared to previous works.</p>", "Keywords": "", "DOI": "10.1007/s00542-020-04967-7", "PubYear": 2021, "Volume": "27", "Issue": "3", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Babol Noshirvani University of Technology, Babol, Iran"}, {"AuthorId": 2, "Name": "Rohollah Teymurnejad", "Affiliation": "Electrical and Computer Engineering Department, Babol Noshirvani University of Technology, Babol, Iran"}], "References": []}, {"ArticleId": 83252959, "Title": "Neural network-based damage identification in composite laminated plates using frequency shifts", "Abstract": "<p>Delamination is the principal mode of failure of laminated composites. It is caused by the rupture of the fiber–matrix interface and results in the separation of the layers. This failure is induced by interlaminate tension and shear that is developed due to a variety of factors such as fatigue. Composites are known for excellent structural performance, which can be significantly affected by delamination. Such damages are not always visible on the surface and can lead to sudden catastrophic failures. To ensure a structural performance and integrity, accurate methods to monitor damages are required. This work presents a methodology for damage detection and identification on laminated composite plates using artificial neural networks fed with modal data obtained by finite element analysis. The proposed neural network to quantify damage severity achieved up to 95% success rate. A comparative study was done to evaluate the effect of boundary conditions on damage location. With the comparative study results, another neural network was proposed to locate damage position, achieving excellent results by successfully locating or by significantly reducing the search area. Both proposed ANNs use only frequency variation values as inputs, an easily obtainable quantity that requires few equipment to be acquired. The obtained results from these numerical examples indicate that the proposed approach can detect true damage locations and estimate damage magnitudes with satisfactory accuracy for this particular geometry, even under high measurement noise.</p>", "Keywords": "Damage identification; Artificial neural network; Composite material; Vibrations", "DOI": "10.1007/s00521-020-05180-3", "PubYear": 2021, "Volume": "33", "Issue": "8", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Institute, Federal University of Itajubá, Itajubá, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering Institute, Federal University of Itajubá, Itajubá, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Institute, Federal University of Itajubá, Itajubá, Brazil"}], "References": [{"Title": "Sensor placement optimization and damage identification in a fuselage structure using inverse modal problem and firefly algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "571", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Fault classification in three-phase motors based on vibration signal analysis and artificial neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "18", "Page": "15171", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Lichtenberg optimization algorithm applied to crack tip identification in thin plate-like structures", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "1", "Page": "151", "JournalTitle": "Engineering Computations"}]}, {"ArticleId": 83253095, "Title": "Improving Competence for Reliable Autonomy", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.319.4", "PubYear": 2020, "Volume": "319", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Massachusetts Amherst"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Massachusetts Amherst"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Alliance Innovation Lab Silicon Valley"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Alliance Innovation Lab Silicon Valley"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Massachuetts Amherst"}], "References": []}, {"ArticleId": 83253120, "Title": "Statistical Model Checking of Human-Robot Interaction Scenarios", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.319.2", "PubYear": 2020, "Volume": "319", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Politecnico di Milano"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "McMaster University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Politecnico di Milano"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Politecnico di Milano"}], "References": []}, {"ArticleId": 83253122, "Title": "Testing the Robustness of AutoML Systems", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.319.8", "PubYear": 2020, "Volume": "319", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 83253145, "Title": "Uncertainty measures for probabilistic hesitant fuzzy sets in multiple criteria decision making", "Abstract": "<p>This contribution reviews critically the existing entropy measures for probabilistic hesitant fuzzy sets (PHFSs), and demonstrates that these entropy measures fail to effectively distinguish a variety of different PHFSs in some cases. In the sequel, we develop a new axiomatic framework of entropy measures for probabilistic hesitant fuzzy elements (PHFEs) by considering two facets of uncertainty associated with PHFEs which are known as fuzziness and nonspecificity. Respect to each kind of uncertainty, a number of formulae are derived to permit flexible selection of PHFE entropy measures. Moreover, based on the proposed PHFE entropy measures, we introduce some entropy‐based distance measures which are used in the portion of comparative analysis. Eventually, the proposed PHFE entropy measures and PHFE entropy‐based distance measures are applied to decision making in the strategy initiatives where their reliability and effectiveness are verified.</p>", "Keywords": "entropy measure;multiple criteria decision making;probabilistic hesitant fuzzy set", "DOI": "10.1002/int.22266", "PubYear": 2020, "Volume": "35", "Issue": "11", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "Bahram Farhadinia", "Affiliation": "Department of Mathematics, Quchan University of Technology, Quchan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Systems, University of Melbourne, Melbourne, Victoria, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Systems, University of Melbourne, Melbourne, Victoria, Australia"}], "References": []}, {"ArticleId": 83253154, "Title": "Toward Campus Mail Delivery Using BDI", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.319.10", "PubYear": 2020, "Volume": "319", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Ottawa"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Carleton University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Carleton University"}], "References": []}, {"ArticleId": 83253155, "Title": "Engineering Reliable Interactions in the Reality-Artificiality Continuum", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.319.6", "PubYear": 2020, "Volume": "319", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Genova, DIBRIS"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Genova, DIBRIS"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Genova, DIBRIS"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Genova, DIBRIS"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Genova, DIBRIS"}], "References": []}, {"ArticleId": 83253176, "Title": "Datasets of solid and liquid discharges of an urban Mediterranean river and its karst springs (Las River, SE France)", "Abstract": "This data paper presents: (1) the liquid and solid discharge characteristics of the Las River, an urban Mediterranean stream flowing to the Bay of Toulon (south of France), and (2) the water height of the main karst springs supplying the Las River. We assessed the river's discharge with hydrological observations and we explored floods characteristics influencing its solid discharge [1] . The location of the monitoring station near the river's mouth was selected accordingly to accessibility and technical constraints, as far downstream as possible. The vast majority of tributaries (such as possible underground springs, stormwater outlets, urban runoff) were taken into account. A multi-parameter probe (temperature, pressure, turbidity and electric conductivity) and a sediment trap were deployed continuously for 17 months, from October 2012 to March 2014. At the river's sources, probes (temperature, water height) were deployed to characterized karst springs. Times series were averaged at a daily time step, and water height converted in discharge when the rating curve was available. Sediment samples were analyzed for grain-size distribution. Datasets may help to estimate karsts' contributions to the Mediterranean Sea and to assess their influence on rivers discharge and solid yield. Stakeholders may also use the maximum water height to evaluate the flooding risk. Our data also contribute to linking the catchment freshwater to the coastal sea, a connection yet to be fully explored.", "Keywords": "Dardennes springs;Discharge;Grain-size;Karst spring;Mediterranean river;Solid yield;Urban stream;flood", "DOI": "10.1016/j.dib.2020.106022", "PubYear": 2020, "Volume": "31", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institut des sciences de la mer de Rimouski, Université du Québec à Rimouski, 310 allée des Ursulines, Rimouski QC, G5L 3A1, Canada;Institut de Radioprotection et de Sureté Nucléaire (IRSN), PSE-ENV, SRTE/LRTA, BP 3, 13115, Saint-Paul-lez-Durance, France;Université de Toulon, CNRS/INSU, IRD, Mediterranean Institute of Oceanography (MIO), UM 110, 83041 Toulon Cedex 09, France;Aix Marseille Université, CNRS/INSU, IRD, Mediterranean Institute of Oceanography (MIO), OSU PYTHEAS, UM 110, 13288 Marseille, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Aix Marseille Univ, CNRS, IRD, INRAE, Coll France, CEREGE, Aix-en-Provence, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut de Radioprotection et de Sureté Nucléaire (IRSN), PSE-ENV, SRTE/LRTA, BP 3, 13115, Saint-Paul-lez-Durance, France;Université de Nîmes, EA7352 CHROME, Laboratoire GIS, Parc scientifique et technique G<PERSON>, 150 rue Georges Besse, 30000 Nîmes, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut de Radioprotection et de Sureté Nucléaire (IRSN), PSE-ENV, SRTE/LRTA, BP 3, 13115, Saint-Paul-lez-Durance, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institut de Radioprotection et de Sureté Nucléaire (IRSN), PSE-ENV, SRTE/LRTA, BP 3, 13115, Saint-Paul-lez-Durance, France"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Université de Toulon, CNRS/INSU, IRD, Mediterranean Institute of Oceanography (MIO), UM 110, 83041 Toulon Cedex 09, France;Aix Marseille Université, CNRS/INSU, IRD, Mediterranean Institute of Oceanography (MIO), OSU PYTHEAS, UM 110, 13288 Marseille, France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Association Spélé-H2<PERSON>, av. <PERSON><PERSON>, Six-Fours, France"}], "References": []}, {"ArticleId": 83253363, "Title": "SVD++ Recommendation Algorithm Based on Backtracking", "Abstract": "<p>Collaborative filtering (CF) has successfully achieved application in personalized recommendation systems. The singular value decomposition (SVD)++ algorithm is employed as an optimized SVD algorithm to enhance the accuracy of prediction by generating implicit feedback. However, the SVD++ algorithm is limited primarily by its low efficiency of calculation in the recommendation. To address this limitation of the algorithm, this study proposes a novel method to accelerate the computation of the SVD++ algorithm, which can help achieve more accurate recommendation results. The core of the proposed method is to conduct a backtracking line search in the SVD++ algorithm, optimize the recommendation algorithm, and find the optimal solution via the backtracking line search on the local gradient of the objective function. The algorithm is compared with the conventional CF algorithm in the FilmTrust, MovieLens 1 M and 10 M public datasets. The effectiveness of the proposed method is demonstrated by comparing the root mean square error, absolute mean error and recall rate simulation results.</p>", "Keywords": "collaborative filtering; SVD++; backtracking line search; recommendation system collaborative filtering ; SVD++ ; backtracking line search ; recommendation system", "DOI": "10.3390/info11070369", "PubYear": 2020, "Volume": "11", "Issue": "7", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronic Information and Optical Engineering, Nankai University, Tianjin 300350, China"}, {"AuthorId": 2, "Name": "Guiling Sun", "Affiliation": "College of Electronic Information and Optical Engineering, Nankai University, Tianjin 300350, China ↑ Author to whom correspondence should be addressed"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Li", "Affiliation": "College of Electronic Information and Optical Engineering, Nankai University, Tianjin 300350, China"}], "References": []}, {"ArticleId": 83253401, "Title": "Building a complementary agenda for business process management and digital innovation", "Abstract": "The world is blazing with change and digital innovation is fuelling the fire. Process management can help channel the heat into useful work. Unfortunately, research on digital innovation and process management has been conducted by separate communities operating under orthogonal assumptions. We argue that a synthesis of assumptions is required to bring these streams of research together. We offer suggestions for how these assumptions can be updated to facilitate a convergent conversation between the two research streams. We also suggest ways that methodologies from each stream could benefit the other. Together with the three exemplar empirical studies included in the special issue on business process management and digital innovation, we develop a broader foundation for reinventing research on business process management in a world ablaze with digital innovation.", "Keywords": "Business process management ; digital innovation ; organisational routines ; process-aware information systems ; theory", "DOI": "10.1080/0960085X.2020.1755207", "PubYear": 2020, "Volume": "29", "Issue": "3", "JournalId": 3498, "JournalTitle": "European Journal of Information Systems", "ISSN": "0960-085X", "EISSN": "1476-9344", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Wirtschaftsuniversität Wien,  Information Systems and Operations , Vienna, Austria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Accounting and Information Systems,  Michigan State University , East Lansing United Kingdom of Great Britain and Northern Ireland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Information Systems and Systems Development,  University of Cologne , Cologne, Germany"}], "References": [{"Title": "Architectural alignment of process innovation and digital infrastructure in a high-tech hospital", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "220", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Digital transformation and the new logics of business process management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "238", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Examining the interplay between big data analytics and contextual factors in driving process innovation capabilities", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "260", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Bringing Context Inside Process Research with Digital Trace Data", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "5", "Page": "1214", "JournalTitle": "Journal of the Association for Information Systems"}]}, {"ArticleId": 83253475, "Title": "Study on surface quality improvement of the plane magnetic abrasive finishing process", "Abstract": "<p>Magnetic abrasive finishing (MAF) is an effective surface finishing method. At present, most of the research on plane MAF focuses on finishing characteristics. However, due to the “edge effect” of the magnetic field and the rotational movement of the magnetic brush, the uniformity and flatness of the finished surface are poor. In order to further improve the accuracy of the finished surface, the surface uniformity and flatness are improved by changing the shape of the magnetic pole and the trajectory of the magnetic brush. At the same time, the surface flatness is evaluated not only by the maximum height difference of the cross section but also by the standard deviation to evaluate the surface uniformity and flatness. Through magnetic field simulation and experiments, it is proved that the bottom groove of the magnetic pole helps to make the magnetic particle distribution more uniform in the processing area, which can effectively improve the surface quality. In addition, through experiments, changing the trajectory of the magnetic brush can also effectively improve the surface flatness of the finished surface.</p>", "Keywords": "Magnetic abrasive finishing; Magnetic brush trajectory; Flatness evaluation; Surface uniformity; Magnetic pole shape", "DOI": "10.1007/s00170-020-05759-z", "PubYear": 2020, "Volume": "109", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Utsunomiya University, Utsunomiya, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Utsunomiya University, Utsunomiya, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Utsunomiya University, Utsunomiya, Japan"}], "References": []}, {"ArticleId": 83254168, "Title": "Learning distributed communication and computation in the IoT", "Abstract": "In distributed, cooperative Internet of Things (IoT) settings, sensing devices must communicate in a resource-aware fashion to achieve a diverse set of tasks, (i.e., event detection, image classification). In such settings, we continue to see a shift from reliance on cloud-centric to edge-centric architectures for data processing, inference and actuation. Distributed edge inference techniques address real-time, connectivity, network bandwidth and latency challenges in spatially distributed IoT applications. Achieving efficient, resource-aware communication in such systems is a longstanding challenge. Many current approaches require complex, hand-engineered communication protocols. In this paper, we present a novel scalable, data-driven and communication-efficient Convolutional Recurrent Neural Network (C-RNN) framework for distributed tasks. We provide empirical and systematic analyses of model convergence, node scalability, computation-cost and communication-cost based on dynamic network graphs. Further to this, we show that our framework is able to solve distributed image classification tasks via automatically learned communication.", "Keywords": "Machine learning ; Internet of Things ; Distributed communication ; Distributed inference", "DOI": "10.1016/j.comcom.2020.07.001", "PubYear": 2020, "Volume": "161", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "Prince <PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Oxford, 15 Parks Road, Oxford, OX13QD, United Kingdom;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Oxford, 15 Parks Road, Oxford, OX13QD, United Kingdom"}], "References": []}, {"ArticleId": 83254178, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0950-7051(20)30478-0", "PubYear": 2020, "Volume": "203", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [], "References": []}, {"ArticleId": 83254267, "Title": "Simplified non-locally dense network for single-image dehazing", "Abstract": "<p>Single-image dehazing is an ill-posed problem. Most previous methods focused on estimating intermediate parameters for input hazy images. In this paper, we propose a novel end-to-end Simplified Non-locally Dense Network (SNDN) which does not rely on intermediate parameters. To capture long-range dependencies, we propose a Simplified Non-local Dense Block (SNDB) which is lightweight and outperforms traditional non-local method. Our SNDB will be embedded into a densely connected encoder–decoder network. To avoid gradients vanishing problem, we propose a simple branch network which only have five convolution layers. The effectiveness of our proposed network is proved through ablation experiment. In addition, we enhanced our training set by synthesizing colored hazy images, which helps restore the original color of the hazy image. The experimental results demonstrate that our network have better performance than most of the pervious state-of-the-art methods.</p>", "Keywords": "Single-image dehazing; Dense; Non-local", "DOI": "10.1007/s00371-020-01929-y", "PubYear": 2020, "Volume": "36", "Issue": "10-12", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East China University of Science and Technology, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "@qq.com;Department of Computer Science and Engineering, East China University of Science and Technology, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computing, The Hong Kong Polytechnic University, Kowloon, Hong Kong"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Biomedical and Multimedia Information Technology Research Group, School of Information Technologies, The University of Sydney, Sydney, Australia"}, {"AuthorId": 6, "Name": "Enhua Wu", "Affiliation": "State Key Laboratory of Computer Science, Institute of Software, Chinese Academy of Sciences, Beijing, China;Faculty of Science and Technology, University of Macau, Macau, China"}], "References": []}, {"ArticleId": 83254273, "Title": "Violation of digital and analog academic integrity through the eyes of faculty members and students: Do institutional role and technology change ethical perspectives?", "Abstract": "<p>This study aimed to address the gap in the literature through a comprehensive comparison of different types of violations of academic integrity (VAI), cheating, plagiarism, fabrication and facilitation (<PERSON>a in J College Univ Law 24(1):1-22, 1997), conducted in analog versus digital settings, as well as students' and faculty members' perceptions regarding their severity. The study explored differences in perceptions regarding students' VAI and penalties for VAI among 1482 students and 42 faculty members. Furthermore, we explored the impact of socio-demographic characteristics (ethnic majority vs. minority students), gender, and academic degree on the perceived severity of VAI. Presented with a battery of scenarios, participants assessed the severity of penalties imposed by a university disciplinary committee. Furthermore, participants selected the penalties they deemed appropriate for violations engaged in by students, including: reprimanding, financial, academic, and accessibility penalties. All participants tended to suggest more severe penalties for VAI conducted in traditional analog environments than for the same offenses in digital settings. Students perceived all four types of penalties imposed by the disciplinary committee to be significantly more severe than faculty members. Moreover, findings demonstrated a significant difference between faculty and students in both perceptions of the severity of VAI and in relation to suggested punishments. Consistent with the Self-Concept Maintenance Model (<PERSON><PERSON> et al. in J Mark <PERSON> 45(6):633-644, 2008) and Neutralizing Effect (Brimble, in: Bretag (ed) Handbook of academic integrity, SpringerNature, Singapore, pp 365-382, 2016), ethnic minority students estimated cheating, plagiarism, and facilitation violations as more severe than majority students. The theoretical and practical implications of the findings are discussed.</p><p>© Springer Science+Business Media, LLC, part of Springer Nature 2020.</p>", "Keywords": "Cheating, plagiarism, fabrication and facilitation;Differences between faculty members and students;Digital and analog academic dishonesty;Gender, ethnicity and academic degree in academic offenses;Violation of academic integrity", "DOI": "10.1007/s12528-020-09260-0", "PubYear": 2021, "Volume": "33", "Issue": "1", "JournalId": 3560, "JournalTitle": "Journal of Computing in Higher Education", "ISSN": "1042-1726", "EISSN": "1867-1233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Education and Psychology, The Open University of Israel, 1 University Road, P.O.B. 808, 43107 Ra'anana, Israel."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Education and Psychology, The Open University of Israel, 1 University Road, P.O.B. 808, 43107 Ra'anana, Israel."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Education and Psychology, The Open University of Israel, 1 University Road, P.O.B. 808, 43107 Ra'anana, Israel."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Education and Psychology, The Open University of Israel, 1 University Road, P.O.B. 808, 43107 Ra'anana, Israel."}], "References": []}, {"ArticleId": 83254513, "Title": "Construction of Confidence Interval for a Univariate Stock Price Signal Predicted Through Long Short Term Memory Network", "Abstract": "<p>In this paper, we show an innovative way to construct bootstrap confidence interval of a signal estimated based on a univariate LSTM model. We take three different types of bootstrap methods for dependent set up. We prescribe some useful suggestions to select the optimal block length while performing the bootstrapping of the sample. We also propose a benchmark to compare the confidence interval measured through different bootstrap strategies. We illustrate the experimental results through some stock price data set.</p>", "Keywords": "Confidence interval; Bootstrap; LSTM; Forecasting", "DOI": "10.1007/s40745-020-00307-8", "PubYear": 2022, "Volume": "9", "Issue": "2", "JournalId": 6800, "JournalTitle": "Annals of Data Science", "ISSN": "2198-5804", "EISSN": "2198-5812", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, IIT Guwahati, Guwahati, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, IIT Guwahati, Guwahati, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, IIT Guwahati, Guwahati, India"}], "References": [{"Title": "Predicting the Unpredictable: An Application of Machine Learning Algorithms in Indian Stock Market", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "4", "Page": "791", "JournalTitle": "Annals of Data Science"}]}, {"ArticleId": 83254587, "Title": "Four-Dimensional Anisotropic Mesh Adaptation", "Abstract": "Anisotropic mesh adaptation is important for accurately simulating physical phenomena at reasonable computational costs. Previous work in anisotropic mesh adaptation has been restricted to studies in two- or three-dimensional computational domains. However, in order to accurately simulate time-dependent physical phenomena in three dimensions, a four-dimensional mesh adaptation tool is needed. This work develops a four-dimensional anisotropic mesh adaptation tool to support time-dependent three-dimensional numerical simulations. Anisotropy is achieved through the use of a background metric field and the mesh is adapted using a dimension-independent cavity framework. Metric-conformity – in the sense of edge lengths, element quality and element counts – is effectively demonstrated on four-dimensional benchmark cases within a unit tesseract in which the background metric is prescribed analytically. Next, the metric field is optimized to minimize the approximation error of a scalar function with discontinuous Galerkin discretizations on four-dimensional domains. We demonstrate that this four-dimensional mesh adaptation algorithm achieves optimal element sizes and orientations. To our knowledge, this is the first presentation of anisotropic four-dimensional meshes.", "Keywords": "Mesh adaptation ; Metric-conforming ; Four-dimensional ; Function approximation ; High-order finite elements", "DOI": "10.1016/j.cad.2020.102915", "PubYear": 2020, "Volume": "129", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Middlebury College, Department of Computer Science, United States of America;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, Department of Aeronautics & Astronautics, United States of America"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, Department of Aeronautics & Astronautics, United States of America"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, Department of Aeronautics & Astronautics, United States of America"}], "References": []}, {"ArticleId": 83254606, "Title": "LSMD: A fast and robust local community detection starting from low degree nodes in social networks", "Abstract": "Community detection is an appropriate approach for discovering and understanding the structure and hidden information in complex networks One of the most critical issues in community detection problem is the low-time complexity of the algorithm, while preserving the accuracy of the algorithm, which is important in large-scale networks such as social networks. Local community detection algorithms try to use local information and provide acceptable results in a reasonable time. This paper proposes a fast and accurate community detection based on local information for community’s label assigning. In the proposed algorithm, local community detection is started from low degree nodes by label assigning in a multi-level diffusion way, called LSMD algorithm, with significant low time complexity. In the first phase of the LSMD algorithm, the first community’s label is assigned to the node with degree 1 and its direct neighbor and second level neighbors. In fact, we used this fact that people with less number of neighbors are not likely to be connected to diverse communities. Next, a community label is respectively assigned for the nodes with degrees 2, 3, and so forth. In the second phase, initial communities are merged using a new simple and fast strategy as far as possible to form the final communities. Besides, there is no random nature in the algorithm, as well as the adjustable parameter. Therefore, the obtained results show meaningful stability for the LSMD. Experiments are performed on real-world and synthetic networks to evaluate the performance and accuracy of the proposed algorithm. The results show that the proposed algorithm significantly is more accurate than the other state-of-the-art algorithms. In addition, it substantially is faster than other local algorithms such as LPA, LCCD, NIBLPA, DA, RTLCD, Louvain, G-CN, Infomap, and ECES on large-scale networks.", "Keywords": "Community detection ; Local similarity ; Label diffusion ; Low degree nodes ; Social networks", "DOI": "10.1016/j.future.2020.07.011", "PubYear": 2020, "Volume": "113", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Azarbaijan Shahid Madani University, Tabriz, Iran;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Azarbaijan Shahid Madani University, Tabriz, Iran"}], "References": []}, {"ArticleId": 83254610, "Title": "A multi-objective evolutionary algorithm based on adaptive clustering for energy-aware batch scheduling problem", "Abstract": "For batch scheduling problems, more and more attentions have been paid to reducing energy consumption. In this paper, a complex batch scheduling problem on parallel batch processing machines considering time-of-use electricity price is investigated to minimize makespan and total electricity cost, simultaneously. Due to NP-hardness of the studied problem, a multi-objective evolutionary algorithm based on adaptive clustering is proposed, where an improved adaptive clustering method is incorporated to mine the distribution structure of solutions, which can be used to guide the search. Moreover, a new recombination strategy based on both distribution characteristics and mating probability is designed to select individuals for mating. In addition, to better balance exploration and exploitation, the mating probability is adaptively adjusted according to historical information. The experimental results demonstrate the competitiveness of the proposed algorithm in terms of solution quality.", "Keywords": "Multi-objective optimization ; Evolutionary algorithm ; Adaptive clustering ; Energy-aware scheduling ; Batch processing machines", "DOI": "10.1016/j.future.2020.06.010", "PubYear": 2020, "Volume": "113", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of Intelligent Computing and Signal Processing of Ministry of Education, Anhui University, Hefei, Anhui, 230601, PR China;School of Computer Science and Technology, Anhui University, Hefei, Anhui, 230601, PR China"}, {"AuthorId": 2, "Name": "Zhao<PERSON><PERSON><PERSON>a", "Affiliation": "Key Lab of Intelligent Computing and Signal Processing of Ministry of Education, Anhui University, Hefei, Anhui, 230601, PR China;School of Computer Science and Technology, Anhui University, Hefei, Anhui, 230601, PR China;School of Internet, Anhui University, Hefei, Anhui, 230601, PR China;Corresponding author at: School of Computer Science and Technology, Anhui University, Hefei, Anhui, 230601, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Anhui, 230009, PR China"}], "References": [{"Title": "Neural network based multi-objective evolutionary algorithm for dynamic workflow scheduling in cloud computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "307", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Concurrent container scheduling on heterogeneous clusters with multi-resource constraints", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "562", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 83254811, "Title": "Leveraging energy‐efficient load balancing algorithms in fog computing", "Abstract": "<p>Cloud computing and smart gadgets are the need of smart world these days. This often leads to latency and irregular connectivity issues in many situations. In order to overcome this issue, an emerging technique of fog computing is used for cloud and smart devices. A decentralized computing infrastructure in which all the elements, that is, storage, compute, data and the applications in use, are passed in an efficient and logical place between cloud and the data source, is called Fog computing. The cloud computing and services are generally extended by fog computing, which brings the power and advantages of data creation and data analysis at the network edge. Real-time location based services and applications with mobility support are enabled due to the physical proximity of users and high speed internet connection to the cloud. Fog computing is promoted with leveraging load balancing techniques so as to balance the load which is done in two ways, that is, static load balancing and dynamic load balancing. In this paper, different load balancing algorithms are discussed and their comparative analysis has been carried out. Round Robin load balancing is the simplest and easiest load balancing technique to be implemented in fog computing environments. The major problem of Source IP Hash load balancing algorithm is that each change can redirect to anyone with a different server, and thus, is least preferred in fog networks. The mechanisms to make energy efficient load balancing are also considered as the part of this paper.</p>", "Keywords": "edge computing;energy efficient;fog computing;load balancing techniques;types of load balancing", "DOI": "10.1002/cpe.5913", "PubYear": 2022, "Volume": "34", "Issue": "13", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institute of Engineering and Technology, Patiala, Punjab, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institute of Engineering and Technology, Patiala, Punjab, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computational Sciences, <PERSON> <PERSON><PERSON><PERSON> Punjab Technical University, Bathinda, Punjab, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Graduate School, Faculty of Information Technology, Duy Tan University, Da Nang, Viet Nam"}], "References": [{"Title": "HealthFog: An ensemble deep learning based Smart Healthcare System for Automatic Diagnosis of Heart Diseases in integrated IoT and fog computing environments", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "187", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "MobFogSim: Simulation of mobility and migration for fog computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>io <PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "102062", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Quantumized approach of load scheduling in fog computing environment for IoT applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "5", "Page": "1097", "JournalTitle": "Computing"}]}, {"ArticleId": 83254815, "Title": "A machine learning‐based memory forensics methodology for TOR browser artifacts", "Abstract": "<p>At present, 96 % of the resources available into the World-Wide-Web belongs to the Deep Web , which is composed of contents that are not indexed by search engines. The Dark Web is a subset of the Deep Web , which is currently the favorite place for hiding illegal markets and contents. The most important tool that can be used to access the Dark Web is the Tor Browser . In this article, we propose a bottom-up formal investigation methodology for the Tor Browser's memory forensics. Based on a bottom-up logical approach, our methodology enables us to obtain information according to a level of abstraction that is gradually higher, to characterize semantically relevant actions carried out by the Tor browser. Again, we show how the proposed three-layer methodology can be realized through open-source tools. Also, we show how the extracted information can be used as input to a novel Artificial Intelligence-based architecture for mining effective signatures capable of representing malicious activities in the Tor network. Finally, to assess the effectiveness of the proposed methodology, we defined three test cases that simulate widespread real-life scenarios and discuss the obtained results. To the best of our knowledge, this is the first work that deals with the forensic analysis of the Tor Browser in a live system, in a formal and structured way.</p>", "Keywords": "anonymity;private browsing;the onion router;TOR;Tor browser;Web browser forensics", "DOI": "10.1002/cpe.5935", "PubYear": 2021, "Volume": "33", "Issue": "23", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Fisciano, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Fisciano, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Fisciano, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Salerno, Fisciano, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Fisciano, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Fisciano, Italy"}], "References": [{"Title": "Compression‐based steganography", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "8", "Page": "e5322", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Malware detection in mobile environments based on Autoencoders and API-images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "26", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": 83254950, "Title": "Unsupervised Assignment Flow: Label Learning on Feature Manifolds by Spatially Regularized Geometric Assignment", "Abstract": "<p>This paper introduces the unsupervised assignment flow that couples the assignment flow for supervised image labeling (<PERSON><PERSON><PERSON> et al. in J Math Imaging Vis 58(2):211–238, 2017 ) with Riemannian gradient flows for label evolution on feature manifolds. The latter component of the approach encompasses extensions of state-of-the-art clustering approaches to manifold-valued data. Coupling label evolution with the spatially regularized assignment flow induces a sparsifying effect that enables to learn compact label dictionaries in an unsupervised manner. Our approach alleviates the requirement for supervised labeling to have proper labels at hand, because an initial set of labels can evolve and adapt to better values while being assigned to given data. The separation between feature and assignment manifolds enables the flexible application which is demonstrated for three scenarios with manifold-valued features. Experiments demonstrate a beneficial effect in both directions: adaptivity of labels improves image labeling, and steering label evolution by spatially regularized assignments leads to proper labels, because the assignment flow for supervised labeling is exactly used without any approximation for label learning. </p>", "Keywords": "Assignment flow; Divergence function; Feature manifolds; Unsupervised learning; Information geometry", "DOI": "10.1007/s10851-019-00935-7", "PubYear": 2020, "Volume": "62", "Issue": "6-7", "JournalId": 6119, "JournalTitle": "Journal of Mathematical Imaging and Vision", "ISSN": "0924-9907", "EISSN": "1573-7683", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Image and Pattern Analysis Group, Heidelberg University, Heidelberg, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Image and Pattern Analysis Group, Heidelberg University, Heidelberg, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematical Imaging Group, Heidelberg University, Heidelberg, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Image and Pattern Analysis Group, Heidelberg University, Heidelberg, Germany"}], "References": [{"Title": "Learning Adaptive Regularization for Image Labeling Using Geometric Assignment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "2", "Page": "186", "JournalTitle": "Journal of Mathematical Imaging and Vision"}]}, {"ArticleId": 83255122, "Title": "Activity recognition in a smart home using local feature weighting and variants of nearest-neighbors classifiers", "Abstract": "<p>Recognition of activities, such as preparing meal or watching TV, performed by a smart home resident, can promote the independent living of elderly in a safe and comfortable environment of their own homes, for an extended period of time. Different activities performed at the same location have commonalities resulting in less inter-class variations; while the same activity performed multiple times, or by multiple residents, varies in its execution resulting in high intra-class variations. We propose a Local Feature Weighting approach (LFW) that assigns weights based on both inter-class and intra-class importance of a feature in an activity. Multiple sensors are deployed at different locations in a smart home to gather information. We exploit the obtained information, such as frequency and duration of activation of sensors, and the total sensors in an activity for feature weighting. The weights for the same features vary among activities, since a feature may have more importance for one activity but less for the other. For the classification, we exploit the two variants of K-Nearest Neighbors (KNN): Evidence Theoretic KNN (ETKNN) and Fuzzy KNN (FKNN). The evaluation of the proposed approach on three datasets, from CASAS smart home project, demonstrates its ability in the correct recognition of activities compared to the existing approaches.</p><p>© Springer-Verlag GmbH Germany, part of Springer Nature 2020.</p>", "Keywords": "Activity recognition;Ambient assisted living;Feature weighting;Machine learning;Nearest neighbors;Smart home", "DOI": "10.1007/s12652-020-02348-6", "PubYear": 2021, "Volume": "12", "Issue": "2", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Intelligent Knowledge Mining and Analytics Lab, Department of Computer Science, National University of Computer and Emerging Sciences, Islamabad, Pakistan."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computing &amp; Artificial Intelligence, Air University, Islamabad, Pakistan."}], "References": [{"Title": "A sequential deep learning application for recognising human activities in smart homes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "501", "JournalTitle": "Neurocomputing"}, {"Title": "Key feature identification for recognition of activities performed by a smart-home resident", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "2105", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 83255158, "Title": "Design and modeling of an innovative magnetorheological fluid-elastomeric damper with compact structure", "Abstract": "<p>The aim of the present work is to propose an innovative magnetorheological fluid-elastomeric damper which is applied to small amplitude vibration reduction occasions, especially for helicopter rotor. The damper is compact in structure and possesses the controllable dynamic characteristics. The magnetic circuit structure of the damper is designed and optimized emphatically in order to ensure its rationality. After the completion of the prototype, the damper is tested by a hydraulically actuated dynamic testing system under different excitation amplitudes, excitation frequencies, and input currents. Subsequently, a new phenomenological model is established to describe the nonlinear behavior of the proposed magnetorheological fluid-elastomeric damper. A series of parameter identification is conducted to fit both this model and Bouc–<PERSON> model to experimental data for the purpose of verifying the accuracy of the established model.</p>", "Keywords": "", "DOI": "10.1177/1045389X20942898", "PubYear": 2020, "Volume": "31", "Issue": "18", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Launch Dynamics, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Launch Dynamics, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Launch Dynamics, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Launch Dynamics, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Launch Dynamics, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute of Launch Dynamics, Nanjing University of Science and Technology, Nanjing, China"}], "References": [{"Title": "Dynamic simulation of a full vehicle system featuring magnetorheological dampers with bypass holes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "253", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}]}, {"ArticleId": 83255205, "Title": "Multi-split optimized bagging ensemble model selection for multi-class educational data mining", "Abstract": "<p>Predicting students’ academic performance has been a research area of interest in recent years, with many institutions focusing on improving the students’ performance and the education quality. The analysis and prediction of students’ performance can be achieved using various data mining techniques. Moreover, such techniques allow instructors to determine possible factors that may affect the students’ final marks. To that end, this work analyzes two different undergraduate datasets at two different universities. Furthermore, this work aims to predict the students’ performance at two stages of course delivery (20% and 50% respectively). This analysis allows for properly choosing the appropriate machine learning algorithms to use as well as optimize the algorithms’ parameters. Furthermore, this work adopts a systematic multi-split approach based on Gini index and p-value. This is done by optimizing a suitable bagging ensemble learner that is built from any combination of six potential base machine learning algorithms. It is shown through experimental results that the posited bagging ensemble models achieve high accuracy for the target group for both datasets.</p>", "Keywords": "e-Learning; Student Performance Prediction; Optimized Bagging Ensemble Learning Model Selection; Gini Index", "DOI": "10.1007/s10489-020-01776-3", "PubYear": 2020, "Volume": "50", "Issue": "12", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical & Computer Engineering Department, University of Western Ontario, London, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical & Computer Engineering Department, University of Western Ontario, London, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computer Engineering Department, University of Sharjah, Sharjah, UAE;Electrical & Computer Engineering Department, University of Western Ontario, London, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical & Computer Engineering Department, University of Western Ontario, London, Canada"}], "References": [{"Title": "Systematic ensemble model selection approach for educational data mining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "105992", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 83255597, "Title": "Performance of mobile users with text-only and text-and-icon menus in seated and walking situations", "Abstract": "We investigated mobile users’ performance on text-only and text-and-icon menu interfaces of short and medium lengths. We conducted two experiments, with 18 participants each, on four mobile menus in two common usage situations, seated and walking. In Experiment 1, seated participants conducted known-item searches of word menus on short and medium text-only menus and short and medium text-and-icon menus. In Experiment 2, we replicated the first experiment in a walking context of use. We used selection time and error as performance measures in both experiments, plus walking speed in the second. Results showed that, when seated, participants recorded faster times in selecting targets with text-and-icon medium menu than text-only medium menu. Participants, when walking, had significantly faster selection times with text-and-icon short menu than text-only short menu, and with text-and-icon medium menu than text-only medium menu. Text-and-icon medium menu also resulted in fewer incorrect selections by users than text-only medium menu in the walking situation. Further, when comparing menus of the same length in the walking situation, text-and-icon menu had a more efficient learning curve than text-only menu. Walking speed was better with text-and-icon medium menu than text-only medium menu. We discuss results and highlight theoretical and practical implications.", "Keywords": "Mobile menu ; user performance ; learnability ; texts and icons ; seated ; walking", "DOI": "10.1080/0144929X.2020.1795257", "PubYear": 2022, "Volume": "41", "Issue": "1", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, Institute of Public Administration, Riyadh, Saudi Arabia"}], "References": []}, {"ArticleId": 83255618, "Title": "Deep learning-based approach for segmentation of glioma sub-regions in MRI", "Abstract": "<h3>Purpose</h3> <p>Brain tumor is one of the most dangerous and life-threatening disease. In order to decide the type of tumor, devising a treatment plan and estimating the overall survival time of the patient, accurate segmentation of tumor region from images is extremely important. The process of manual segmentation is very time-consuming and prone to errors; therefore, this paper aims to provide a deep learning based method, that automatically segment the tumor region from MR images.</p> <h3>Design/methodology/approach</h3> <p>In this paper, the authors propose a deep neural network for automatic brain tumor (Glioma) segmentation. Intensity normalization and data augmentation have been incorporated as pre-processing steps for the images. The proposed model is trained on multichannel magnetic resonance imaging (MRI) images. The model outputs high-resolution segmentations of brain tumor regions in the input images.</p> <h3>Findings</h3> <p>The proposed model is evaluated on benchmark BRATS 2013 dataset. To evaluate the performance, the authors have used Dice score, sensitivity and positive predictive value (PPV). The superior performance of the proposed model is validated by training very popular UNet model in the similar conditions. The results indicate that proposed model has obtained promising results and is effective for segmentation of Glioma regions in MRI at a clinical level.</p> <h3>Practical implications</h3> <p>The model can be used by doctors to identify the exact location of the tumorous region.</p> <h3>Originality/value</h3> <p>The proposed model is an improvement to the UNet model. The model has fewer layers and a smaller number of parameters in comparison to the UNet model. This helps the network to train over databases with fewer images and gives superior results. Moreover, the information of bottleneck feature learned by the network has been fused with skip connection path to enrich the feature map.</p>", "Keywords": "Deep learning;CNN;MRI;Image processing;Brain tumor segmentation;Glioma", "DOI": "10.1108/IJICC-02-2020-0013", "PubYear": 2020, "Volume": "13", "Issue": "4", "JournalId": 26058, "JournalTitle": "International Journal of Intelligent Computing and Cybernetics", "ISSN": "1756-378X", "EISSN": "1756-3798", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dr <PERSON>kar National Institute of Technology , Jalandhar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dr <PERSON>kar National Institute of Technology , Jalandhar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Dr <PERSON>kar National Institute of Technology , Jalandhar, India"}], "References": []}, {"ArticleId": 83255639, "Title": "Problem-based collaborative learning groupware to improve computer programming skills", "Abstract": "In this paper, a new computer-supported collaborative learning-based groupware is proposed for supporting problem-based collaborative learning in computer programming education setting. The geo-distributed learners can collaboratively with their tutor resolve the common programming problem within a shared workspace. The proposed system embeds some synchronous and asynchronous collaborative tools that allow learners and tutor to interact with each other to develop a shared source code of the program. This research work has two main objectives. First, it attempts to present the proposed groupware functionalities. Second, it discusses the experimental study adopted to assess the acceptability of the Groupware. Such study is an empirical method based on the model of unified theory of the acceptance and use of technology to determine learners’ Behavioural Intention ( BI ) to accept to use such Groupware in Algerian higher education setting.", "Keywords": "Collaborative programming ; computer-supported collaborative learning ; problem-based learning ; groupware ; CSCW ; UTAUT", "DOI": "10.1080/0144929X.2020.1795263", "PubYear": 2022, "Volume": "41", "Issue": "1", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, University of Batna2, Batna, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LaSTIC Laboratory, Computer Science Department, University of Batna2, Batna, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, University of Batna2, Batna, Algeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "LaSTIC Laboratory, Computer Science Department, University of Batna2, Batna, Algeria"}], "References": []}, {"ArticleId": 83255874, "Title": "Convergence and Error Estimates for a Finite Difference Scheme for the Multi-dimensional Compressible Navier–Stokes System", "Abstract": "<p>We prove convergence of a finite difference approximation of the compressible Navier–Stokes system towards the strong solution in \\(R^d,\\) \\(d=2,3,\\) for the adiabatic coefficient \\(\\gamma >1\\) . Employing the relative energy functional, we find a convergence rate which is uniform in terms of the discretization parameters for \\(\\gamma > d/2\\) . All results are unconditional in the sense that we have no assumptions on the regularity nor boundedness of the numerical solution. We also provide numerical experiments to validate the theoretical convergence rate. To the best of our knowledge this work contains the first unconditional result on the convergence of a finite difference scheme for the unsteady compressible Navier–Stokes system in multiple dimensions.</p>", "Keywords": "Compressible Navier–Stokes system; Finite difference method; Convergence; Weak–strong uniqueness; Error estimates; Relative energy functional", "DOI": "10.1007/s10915-020-01278-x", "PubYear": 2020, "Volume": "84", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mathematics of the Czech Academy of Sciences, Praha 1, Czech Republic;Department of Mathematical Analysis and Numerical Mathematics, Faculty of Mathematics, Physics and Informatics of the Comenius University, Mlynská Dolina, Slovakia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mathematics of the Czech Academy of Sciences, Praha 1, Czech Republic;Faculty of Mathematics and Physics of the Charles University, Praha 8, Czech Republic"}], "References": []}, {"ArticleId": 83255904, "Title": "Testing that a Local Optimum of the Likelihood is Globally Optimum Using Reparameterized Embeddings", "Abstract": "<p>Many mathematical imaging problems are posed as non-convex optimization problems. When numerically tractable global optimization procedures are not available, one is often interested in testing ex post facto whether or not a locally convergent algorithm has found the globally optimal solution. When the problem is formulated in terms of maximizing the likelihood function under a statistical model for the measurements, one can construct a statistical test that a local maximum is in fact the global maximum. A one-sided test is proposed for the case that the statistical model is a member of the generalized location family of probability distributions, a condition often satisfied in imaging and other inverse problems. We propose a general method for improving the accuracy of the test by reparameterizing the likelihood function to embed its domain into a higher-dimensional parameter space. We show that the proposed global maximum testing method results in improved accuracy and reduced computation for a physically motivated joint-inverse problem arising in camera-blur estimation.</p>", "Keywords": "Inverse problems; Parameter estimation; Maximum likelihood; Global optimization; Local maxima", "DOI": "10.1007/s10851-020-00979-0", "PubYear": 2020, "Volume": "62", "Issue": "6-7", "JournalId": 6119, "JournalTitle": "Journal of Mathematical Imaging and Vision", "ISSN": "0924-9907", "EISSN": "1573-7683", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, USA;Michigan Tech Research Institute, Ann Arbor, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, USA;Michigan Tech Research Institute, Ann Arbor, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, USA"}], "References": []}, {"ArticleId": 83255942, "Title": "Deniably authenticated searchable encryption scheme based on Blockchain for medical image data sharing", "Abstract": "<p>In the cloud applications of medical data based on blockchain, doctors and managers usually want to obtain image data shared by other healthcare institutions. To ensure the privacy and workability of the image data, it is necessary to encrypt plain image data, retrieve cypher data and verify the authenticity of the data. Public key authenticated searchable encryption (PAEKS) is an effective mechanism to realize the privacy and workability properties of data. However, the existing PAEKS schemes are unable to realize the identity privacy protection of the data owner, and the traditional blockchain system (such as the Bitcoin) cannot achieve these goals directly. To overcome the above drawback, we first present a deniably authenticated searchable encryption scheme for medical image data sharing (DASES) that is based on blockchain and deniably authenticated encryption technology. The DASES takes advantage of blockchain technology to ensure the non-tampered, unforgettable and traceability of the image data, and it also avoids the limitation of the blockchain’s own storage and computing power. The DASES can not only withstand inside keyword guessing attack (IKGA) but also provide effective privacy protection and verify the authenticity of medical image data. Hence, it can better protect the privacy of data senders and provide stronger security. Next, we prove that the DASES satisfies the indistinguishability of the ciphertext and trapdoor. It is regrettable that the DASES is less efficient than related schemes in the literature, but its greatest strength is its ability to provide better identity privacy protection and stronger security.</p>", "Keywords": "Blockchain; Deniably authenticated encryption; Identity privacy; Searchable encryption; Medical image", "DOI": "10.1007/s11042-020-09213-w", "PubYear": 2020, "Volume": "79", "Issue": "37-38", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Northwest Normal University, Lanzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "@qq.com;College of Computer Science and Engineering, Northwest Normal University, Lanzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gansu Health Vocational College, Lanzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Big Data And Internet, Shenzhen Technology University, Shenzhen, China"}], "References": []}, {"ArticleId": 83255984, "Title": "Analytical fuzzy triangular solutions of the wave equation", "Abstract": "<p>The analytical fuzzy triangular solutions for both one-dimensional homogeneous and non-homogeneous wave equations with emphasis on the type of [gH-p]-differentiability of solutions are obtained by using the fuzzy D<PERSON><PERSON><PERSON>s formulas. In the current article, the existence and uniqueness of the solutions of the homogeneous and non-homogeneous fuzzy wave equation by considering the type of [gH-p]-differentiability of solutions are provided. In a special case, the fuzzy mathematical model of a vibrating string with a fixed end is investigated. Eventually, given to the various examples represented, the efficacy and accuracy of the method are examined.</p>", "Keywords": "Generalized Hukuhara differentiability; The <PERSON><PERSON>s formula; Homogeneous and non-homogeneous fuzzy wave equation; Le<PERSON><PERSON><PERSON> rule", "DOI": "10.1007/s00500-020-05146-6", "PubYear": 2021, "Volume": "25", "Issue": "1", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Islamic Azad University, Tehran, Iran"}], "References": [{"Title": "A fuzzy generalized power series method under generalized <PERSON><PERSON><PERSON> differentiability for solving fuzzy Legendre differential equation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "12", "Page": "8763", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 83255987, "Title": "Discrete uniform and binomial distributions with infinite support", "Abstract": "Abstract We study properties of two probability distributions defined on the infinite set $$\\{0,1,2, \\ldots \\}$$ \n \n { \n 0 \n , \n 1 \n , \n 2 \n , \n … \n } \n \n and generalizing the ordinary discrete uniform and binomial distributions. Both extensions use the grossone-model of infinity. The first of the two distributions we study is uniform and assigns masses $$1/\\textcircled {1}$$ \n \n 1 \n / \n \n 1 \n \n \n to all points in the set $$ \\{0,1,\\ldots ,\\textcircled {1}-1\\}$$ \n \n { \n 0 \n , \n 1 \n , \n … \n , \n \n 1 \n \n - \n 1 \n } \n \n , where $$\\textcircled {1}$$ \n \n 1 \n \n denotes the grossone. For this distribution, we study the problem of decomposing a random variable $$\\xi $$ \n ξ \n with this distribution as a sum $$\\xi {\\mathop {=}\\limits ^\\mathrm{d}} \\xi _1 + \\cdots + \\xi _m$$ \n \n ξ \n \n = \n d \n \n \n ξ \n 1 \n \n + \n ⋯ \n + \n \n ξ \n m \n \n \n , where $$\\xi _1 , \\ldots , \\xi _m$$ \n \n \n ξ \n 1 \n \n , \n … \n , \n \n ξ \n m \n \n \n are independent non-degenerate random variables. Then, we develop an approximation for the probability mass function of the binomial distribution Bin $$(\\textcircled {1},p)$$ \n \n ( \n \n 1 \n \n , \n p \n ) \n \n with $$p=c/\\textcircled {1}^{\\alpha }$$ \n \n p \n = \n c \n / \n \n \n 1 \n \n α \n \n \n with $$1/2<\\alpha \\le 1$$ \n \n 1 \n / \n 2 \n < \n α \n ≤ \n 1 \n \n . The accuracy of this approximation is assessed using a numerical study.", "Keywords": "Binomial distribution; <PERSON><PERSON><PERSON> approximation; <PERSON> polynomials", "DOI": "10.1007/s00500-020-05190-2", "PubYear": 2020, "Volume": "24", "Issue": "23", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Cardiff University, Cardiff, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cardiff University, Cardiff, UK"}], "References": [{"Title": "Infinitesimal Probabilities Based on Grossone", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "1", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 83255989, "Title": "Interview with <PERSON><PERSON>", "Abstract": "", "Keywords": "", "DOI": "10.1007/s13218-020-00683-6", "PubYear": 2020, "Volume": "34", "Issue": "3", "JournalId": 4294, "JournalTitle": "KI - Künstliche Intelligenz", "ISSN": "0933-1875", "EISSN": "1610-1987", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Manchester, Manchester, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Bremen, Bremen, Germany"}], "References": []}, {"ArticleId": 83256014, "Title": "InfGCN: Identifying influential nodes in complex networks with graph convolutional networks", "Abstract": "Identifying influential nodes in a complex network is very critical as complex networks are ubiquitous. Traditional methods, such as centrality based methods and machine learning based methods, only consider either network structures or node features to evaluate the significance of nodes. However, the influential importance of nodes should be determined by both network structures and node features. To solve this problem, this paper proposes a deep learning model, named InfGCN, to identify the most influential nodes in a complex network based on Graph Convolutional Networks. InfGCN takes neighbor graphs and four classic structural features as the input into a graph convolutional network for learning nodes’ representations, and then feeds the representations into the task-learning layers, comparing the ground truth derived from Susceptible Infected Recovered (SIR) simulation experiments with quantitative infection rate. Extensive experiments on five real-world networks of different types and sizes demonstrate that the proposed model significantly outperforms traditional methods, and can accurately identify influential nodes.", "Keywords": "Influential nodes identification ; Deep learning ; Graph convolution networks ; Complex networks", "DOI": "10.1016/j.neucom.2020.07.028", "PubYear": 2020, "Volume": "414", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Cybersecurity, Sichuan University, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Cybersecurity, Sichuan University, Chengdu, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Cybersecurity, Sichuan University, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing, China"}], "References": [{"Title": "Identifying critical nodes in complex networks via graph convolutional networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "198", "Issue": "", "Page": "105893", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 83256270, "Title": "An end-to-end graph convolutional kernel support vector machine", "Abstract": "A novel kernel-based support vector machine (SVM) for graph classification is proposed. The SVM feature space mapping consists of a sequence of graph convolutional layers, which generates a vector space representation for each vertex, followed by a pooling layer which generates a reproducing kernel Hilbert space (RKHS) representation for the graph. The use of a RKHS offers the ability to implicitly operate in this space using a kernel function without the computational complexity of explicitly mapping into it. The proposed model is trained in a supervised end-to-end manner whereby the convolutional layers, the kernel function and SVM parameters are jointly optimized with respect to a regularized classification loss. This approach is distinct from existing kernel-based graph classification models which instead either use feature engineering or unsupervised learning to define the kernel function. Experimental results demonstrate that the proposed model outperforms existing deep learning baseline models on a number of datasets.", "Keywords": "Graph neural network;Kernel method;Support vector machine", "DOI": "10.1007/s41109-020-00282-2", "PubYear": 2020, "Volume": "5", "Issue": "1", "JournalId": 5330, "JournalTitle": "Applied Network Science", "ISSN": "", "EISSN": "2364-8228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science & Informatics, Cardiff University, Cardiff, UK"}], "References": []}, {"ArticleId": 83256287, "Title": "Secure mobile health system supporting search function and decryption verification", "Abstract": "<p>In order to protect the privacy of the data, the data owners encrypt their sensitive data before transferring it to cloud servers. This method leads to the inability to search over these data. To tackle these challenges, we propose a Secure Mobile Health System (SMHS) supporting search function and decryption verification by using online/offline attribute-based encryption. The new scheme provides a secure Electronic Health Records (EHRs) and efficient searching over the health cloud. In the security analysis, the proposed scheme is proved in the standard model. In addition, we explain via experimental results with a comparison to similar existing protocols that the SMHS can significantly reduce the computation cost in the mobile health system.</p>", "Keywords": "Mobile devices; Electronic health records; Attribute-based encryption; Online/offline; Health cloud", "DOI": "10.1007/s12652-020-02321-3", "PubYear": 2021, "Volume": "12", "Issue": "2", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Cyber Security, School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Cyber Security, School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Guangzhou University, Guangzhou, China"}, {"AuthorId": 4, "Name": "Fagen Li", "Affiliation": "Center for Cyber Security, School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu, China"}], "References": [{"Title": "An efficient certificateless public key cryptography with authorized equality test in IIoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "1065", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 83256301, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0747-5632(20)30245-4", "PubYear": 2020, "Volume": "111", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [], "References": []}, {"ArticleId": 83256460, "Title": "Effect of parametric uncertainties on vibration mitigation with periodically distributed and interconnected piezoelectric patches", "Abstract": "<p>This work presents an analysis of the effect of parametric uncertainties on the vibration control performance of a rod with periodically distributed piezoelectric patches that can be either independently connected to electrical shunt circuits or interconnected through an electrical line of inductors. In both cases, the capacitance of the piezoelectric patches is considered as stochastic parameters following a known probability density function distribution. Then, Monte Carlo simulations are performed to evaluate mean values and confidence intervals of the frequency response functions to assess the robustness of each solution and to compare different solutions in terms of nominal and robust performances. Results have shown that vibration amplitude reduction worsen significantly due to the mistuning between structural natural frequency and circuit resonance frequency. Yet, interconnected circuits are more robust to these uncertainties than independent shunts because they ensure a mean response that is closer to the nominal one. It was then proposed to assess the effect of modifying the circuits’ resistance. Results have shown that increased resistance decreases variability when considering both environmental and manufacturing variabilities. This also favors the use of interconnected circuits that require increased resistance for robust vibration mitigation.</p>", "Keywords": "", "DOI": "10.1177/1045389X20942847", "PubYear": 2021, "Volume": "32", "Issue": "9", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "Marcelo A Trindade", "Affiliation": "Department of Mechanical Engineering, São Carlos School of Engineering, University of São Paulo, São Carlos, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratoire de Mécanique des Structures et des Systèmes Couplés (LMSSC), Conservatoire national des arts et métiers (Cnam), Paris, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratoire de Mécanique des Structures et des Systèmes Couplés (LMSSC), Conservatoire national des arts et métiers (Cnam), Paris, France"}], "References": []}, {"ArticleId": 83256474, "Title": "Analytical and experimental study of shape memory alloy reinforcement on the performance of butt-fusion welded joints in high-density polyethylene pipe", "Abstract": "<p>In this article, a shape memory alloy wire is employed as confinement to enhance the flexural rigidity of the butt-fusion welded joints in the high-density polyethylene pipes. To this end, analytical and experimental studies are considered, when the shape memory alloy wire is wrapped around a butt-fusion joint and an internal pressure is applied to the high-density polyethylene pipe. To investigate the efficiency of shape memory alloy wire jacket on the performance of butt-fusion welded joint in high-density polyethylene pipe, two cases of perfect butt-fusion joint and imperfect butt-fusion joint are tested. The influence of the number of shape memory alloy wire jacket on the high-density polyethylene pipe joints as well as the effect of different flexural rigidities of the wire and high-density polyethylene pipe, and different diameters are evaluated. The deformation is measured using linear voltage displacement transducer and digital image correlation analysis at different location of the pipe length. In addition, radial strain of the pipes due to bulging is measured by electronic resistance strain gauges. Results show that the shape memory alloy wire jacket has a significant effect (33%) on enhancing imperfect butt-fusion welded joint of high-density polyethylene pipes.</p>", "Keywords": "", "DOI": "10.1177/1045389X20942583", "PubYear": 2020, "Volume": "31", "Issue": "17", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Hongik University, Seoul, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Hongik University, Seoul, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Hongik University, Seoul, South Korea"}], "References": []}, {"ArticleId": 83256508, "Title": "Limit state behavior and response sensitivity analysis of endplate steel connections with shape memory alloy bolts", "Abstract": "<p>Shape memory alloys have been used in developing self-centering steel moment connections. This article presents a numerical study aiming at evaluating the cyclic response sensitivity and limit states of extended endplate steel connections with shape memory alloy bolts. Three-dimensional finite element models are developed and validated against a recent experimental study. Using a statistical design-of-experiment method, the effects of 21 design factors and their interactions on the cyclic response of shape memory alloy connections are assessed. The sensitivity of six response parameters is studied. In addition, four limit states for shape memory alloy connections are discussed, including beam local buckling, bolt excessive axial strain, endplate yielding, and column flange yielding. Results show that endplate thickness, shape memory alloy bolt diameter, beam web slenderness ratio, and shape memory alloy maximum transformation strain are the most influential factors. Furthermore, endplate yielding is found to be the governing limit state in almost 80% of the analyzed connections, whereas shape memory alloy bolt excessive strain and column flange yielding are observed in less than 20% and 5% of the connections, respectively. Beam local buckling is not governing in the analyzed shape memory alloy connections designed as per the AISC 358-16 and AISC 341-16 seismic design requirements for extended endplate connections and highly ductile members.</p>", "Keywords": "", "DOI": "10.1177/1045389X20942584", "PubYear": 2020, "Volume": "31", "Issue": "18", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Ryerson University, Toronto, ON, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Ryerson University, Toronto, ON, Canada"}], "References": []}, {"ArticleId": 83256534, "Title": "Affective analysis of patients in homecare video-assisted telemedicine using computational intelligence", "Abstract": "<p>The affective/emotional status of patients is strongly connected to the healing process and their health. Therefore, being aware of the psychological peaks and troughs of a patient provides the advantage of timely intervention by specialists or closely related kinsfolk. In this context, this paper presents the design and implementation of an emotion analysis module integrated in an existing telemedicine platform. Two different methodologies are utilized and discussed. The first scheme exploits the fast and consistent properties of the speeded-up robust features algorithm in order to identify the existence of seven different sentiments in human faces. The second is based on convolutional neural networks. The whole functionality is provided as a Web service for the healthcare platform during regular video teleconference sessions between authorized medical personnel and patients. The paper discusses the technical details of the implementation and the incorporation of the proposed scheme and provides the initial results of its accuracy and operation in practice.</p>", "Keywords": "Affective computing; Convolutional neural networks; Speeded-up robust features (SURF); Emotion analysis; Telemedicine", "DOI": "10.1007/s00521-020-05203-z", "PubYear": 2020, "Volume": "32", "Issue": "23", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Biomedical Informatics, University of Thessaly, Volos, Greece;Department of Digital Systems, University of Piraeus, Piraeus, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Biomedical Informatics, University of Thessaly, Volos, Greece;Department of Digital Systems, University of Piraeus, Piraeus, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Digital Systems, University of Piraeus, Piraeus, Greece;BioAssist S.A, Rion, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Digital Systems, University of Piraeus, Piraeus, Greece"}], "References": []}, {"ArticleId": 83256539, "Title": "A comprehensive survey of the Grasshopper optimization algorithm: results, variants, and applications", "Abstract": "<p>The grasshopper optimization algorithm is one of the dominant modern meta-heuristic optimization algorithms. It has been successfully applied to various optimization problems in several fields, including engineering design, wireless networking, machine learning, image processing, control of power systems, and others. We survey the available literature on the grasshopper optimization algorithm, including its modifications, hybridizations, and generalization to the binary, chaotic, and multi-objective cases. We review its applications, evaluate the algorithms, and provide conclusions.</p>", "Keywords": "Grasshopper optimization algorithm; Meta-heuristic optimization algorithms; Optimization problems; Bio-inspired algorithms", "DOI": "10.1007/s00521-020-04789-8", "PubYear": 2020, "Volume": "32", "Issue": "19", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Sciences and Informatics, Amman Arab University, Amman, Jordan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Division of Engineering, New York University Abu Dhabi, Abu Dhabi, UAE;Department of Civil and Urban Engineering, Tandon School of Engineering, New York University, Brooklyn, USA"}], "References": [{"Title": "Grass<PERSON><PERSON> inspired artificial bee colony algorithm for numerical optimisation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "3", "Page": "363", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "Salp swarm algorithm: a comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "11195", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 83256541, "Title": "Automatic Face Enhancement Technique using Sigmoid Normalization based on Single Scale Retinex (SSR) Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2021.10030669", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 83256596, "Title": "Structural equation modelling for influencing virtual community networks", "Abstract": "With the development of internet, the exchange of knowledge and information has been more convenient, and virtual communities with various themes have been formed. User stickiness has always been the focus of enterprises providing virtual community services. User stickiness refers to the tendency of users continuously using community services after their activity in virtual community; for enterprises providing virtual community services, enterprises are easier to gain profits if the users are more active in the community and have higher tendency of continuously using the community. This paper briefly introduced the stickiness and structural equation model of users in network virtual community, collected data using questionnaire survey, and made example analysis on the influencing factors of stickiness of users in network virtual community using structural equation model. The results showed that knowledge difficulty and environment had no significant impact on user usage, but had a significant positive impact on user satisfaction; competence matching, interpersonal relationship and knowledge quality had significant positive effects on user's usage and satisfaction; user usage and satisfaction had significant positive effects on user loyalty and user dependence reflecting user stickiness.", "Keywords": "virtual community ; influencing factors ; user stickiness ; structural equation model", "DOI": "10.1504/IJWBC.2020.108631", "PubYear": 2020, "Volume": "16", "Issue": "3", "JournalId": 9397, "JournalTitle": "International Journal of Web Based Communities", "ISSN": "1477-8394", "EISSN": "1741-8216", "Authors": [{"AuthorId": 1, "Name": "Di <PERSON>", "Affiliation": "Henan University of Animal Husbandry and Economy, <PERSON><PERSON>, No. 299, East Street, Zhengzhou, Henan 450000, China"}], "References": []}, {"ArticleId": 83256836, "Title": "Creo-Based Parametric Design of a Torque Converter", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2020.107138", "PubYear": 2020, "Volume": "10", "Issue": "7", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "", "Affiliation": ""}], "References": []}, {"ArticleId": 83256899, "Title": "A Single Gene Expression Set Derived from Artificial Intelligence Predicted the Prognosis of Several Lymphoma Subtypes; and High Immunohistochemical Expression of TNFAIP8 Associated with Poor Prognosis in Diffuse Large B-Cell Lymphoma", "Abstract": "<p>Objective: We have recently identified using multilayer perceptron analysis (artificial intelligence) a set of 25 genes with prognostic relevance in diffuse large B-cell lymphoma (DLBCL), but the importance of this set in other hematological neoplasia remains unknown. Methods and Results: We tested this set of genes (i.e., ALDOB, ARHGAP19, ARMH3, ATF6B, CACNA1B, DIP2A, EMC9, ENO3, GGA3, K<PERSON>23, LPXN, MESD, METTL21A, POLR3H, RAB7A, RPS23, SERPINB8, SFTPC, SNN, SPACA9, SWSAP1, SZRD1, TNFAIP8, WDCP and ZSCAN12) in a large series of gene expression comprised of 2029 cases, selected from available databases, that included chronic lymphocytic leukemia (CLL, n = 308), mantle cell lymphoma (MCL, n = 92), follicular lymphoma (FL, n = 180), DLBCL (n = 741), multiple myeloma (MM, n = 559) and acute myeloid leukemia (AML, n = 149). Using a risk-score formula we could predict the overall survival of the patients: the hazard-ratio of high-risk versus low-risk groups for all the cases was 3.2 and per disease subtype were as follows: <PERSON><PERSON> (4.3), <PERSON><PERSON> (5.2), FL (3.0), DLBCL not otherwise specified (NOS) (4.5), multiple myeloma (MM) (5.3) and AML (3.7) (all p values < 0.000001). All 25 genes contributed to the risk-score, but their weight and direction of the correlation was variable. Among them, the most relevant were ENO3, TNFAIP8, ATF6B, METTL21A, KIF23 and ARHGAP19. Next, we validated TNFAIP8 (a negative mediator of apoptosis) in an independent series of 97 cases of DLBCL NOS from Tokai University Hospital. The protein expression by immunohistochemistry of TNFAIP8 was quantified using an artificial intelligence-based segmentation method and confirmed with a conventional RGB-based digital quantification. We confirmed that high protein expression of TNFAIP8 by the neoplastic B-lymphocytes associated with a poor overall survival of the patients (hazard-risk 3.5; p = 0.018) as well as with other relevant clinicopathological variables including age >60 years, high serum levels of soluble IL2RA, a non-GCB phenotype (cell-of-origin Hans classifier), moderately higher MYC and Ki67 (proliferation index), and high infiltration of the immune microenvironment by CD163-positive tumor associated macrophages (CD163+TAMs). Conclusion: It is possible to predict the prognosis of several hematological neoplasia using a single gene-set derived from neural network analysis. High expression of TNFAIP8 is associated with poor prognosis of the patients in DLBCL.</p>", "Keywords": "Non-Hodgkin lymphoma; acute myeloid leukemia (AML); prognosis; artificial intelligence; gene expression; TNFAIP8; diffuse large b-cell lymphoma; deep learning (machine learning) Non-Hodgkin lymphoma ; acute myeloid leukemia (AML) ; prognosis ; artificial intelligence ; gene expression ; TNFAIP8 ; diffuse large b-cell lymphoma ; deep learning (machine learning)", "DOI": "10.3390/ai1030023", "PubYear": 2020, "Volume": "1", "Issue": "3", "JournalId": 69255, "JournalTitle": "AI", "ISSN": "", "EISSN": "2673-2688", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Pathology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan↑Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pathology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pathology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pathology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Pathology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pathology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pathology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pathology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Hematology and Oncology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Clinical Sciences, College of Medicine, University of Sharjah, Sharjah 27272, UAE↑Division of Surgery and Interventional Science, UCL, London WC1E 6BT, UK"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Hematology and Oncology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pathology, Tokai University, School of Medicine, Isehara, Kanagawa 259-1193, Japan"}], "References": []}, {"ArticleId": 83257006, "Title": "Application of IoT Technologies for Cyber-physical Systems of a Sensible Enterprise", "Abstract": "", "Keywords": "", "DOI": "10.15407/csc.2020.02.030", "PubYear": 2020, "Volume": "", "Issue": "2 (286)", "JournalId": 69315, "JournalTitle": "Control Systems and Computers", "ISSN": "2706-8145", "EISSN": "2706-8153", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "International Research and Training Centre of Information Technologies and Systems of the NAS and MES of Ukraine"}], "References": []}, {"ArticleId": 83257039, "Title": "Construction of a Mathematical Model of Multiobjective Optimization on Permutations", "Abstract": "", "Keywords": "", "DOI": "10.15407/csc.2020.02.023", "PubYear": 2020, "Volume": "", "Issue": "2 (286)", "JournalId": 69315, "JournalTitle": "Control Systems and Computers", "ISSN": "2706-8145", "EISSN": "2706-8153", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Lodz"}, {"AuthorId": 2, "Name": "Olena A<PERSON>", "Affiliation": "Poltava University of Economics and Trade"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National University of “Kyiv-Mohyla Academy”"}], "References": [{"Title": "A Method to Solve Conditional Optimization Problems with Quadratic Objective Functions on the Set of Permutations", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "2", "Page": "278", "JournalTitle": "Cybernetics and Systems Analysis"}]}, {"ArticleId": 83257155, "Title": "RETRACTED ARTICLE: An advanced artificial intelligence technique for resource allocation by investigating and scheduling parallel-distributed request/response handling", "Abstract": "<p>Cloud computing is an emerging technology undergoing various challenges that integrate parallel and distributed computing together. In the multi-tenant environment cloud applications can be utilized as a service. User request are enormous and therefore the attributes to be concerned about are scalability, reliability, and resource availability and server response. Utilization of software, platform and infrastructure increases in this environment paving way for resource consumption. This scenario arises various types of issues through collision, traffic jam, data loss, request dropout and delay in response. The past research provides solutions for aspects like scalability, resource allocation, scheduling, load balancing and optimized request and response handling, resource management through virtualization. The process of virtualization and migration of environment is difficult. The cost for allocating VM for a single user is less. The paper proposed a novel scheduling approach for handling unlimited incoming request with quality of service through energy and throughput. The allocated resource focus on maintaining incoming job request, request for dispatch to the server and an acknowledgement for the receipt of response. The paper provides resource allocation methodology through scheduling approaches called integrating of AI techniques namely Genetic Algorithms (GA) and Artificial Neural Networks (ANN). The property of the request is analyzed and priorityis applied for scheduling the request using resource allocation.</p>", "Keywords": "Genetic Algorithms (GA); Artificial Neural Networks (ANN); Cloud Computing; Dynamic Voltage Frequency Scaling Technique (DVFS)", "DOI": "10.1007/s12652-020-02334-y", "PubYear": 2021, "Volume": "12", "Issue": "7", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University College of Engineering, Villupuram, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Vel Tech Multi Tech Dr. <PERSON><PERSON><PERSON><PERSON> Dr. Sakunthala Engineering College, Chennai, India"}], "References": []}, {"ArticleId": 83257273, "Title": "Topic Jerk Detector: Detection of Tweet Bursts Related to the Fukushima Daiichi Nuclear Disaster", "Abstract": "<p>In recent disaster situations, social media platforms, such as Twitter, played a major role in information sharing and widespread communication. These situations require efficient information sharing; therefore, it is important to understand the trends in popular topics and the underlying dynamics of information flow on social media better. Developing new methods to help us in these situations, and testing their effectiveness so that they can be used in future disasters is an important research problem. In this study, we proposed a new model, “topic jerk detector.” This model is ideal for identifying topic bursts. The main advantage of this method is that it is better fitted to sudden bursts, and accurately detects the timing of the bursts of topics compared to the existing method, topic dynamics. Our model helps capture important topics that have rapidly risen to the top of the agenda in respect of time in the study of specific social issues. It is also useful to track the transition of topics more effectively and to monitor tweets related to specific events, such as disasters. We attempted three experiments that verified its effectiveness. First, we presented a case study applied to the tweet dataset related to the Fukushima disaster to show the outcomes of the proposed method. Next, we performed a comparison experiment with the existing method. We showed that the proposed method is better fitted to sudden burst accurately detects the timing of the bursts of the topic. Finally, we received expert feedback on the validity of the results and the practicality of the methodology.</p>", "Keywords": "social media; text mining; burst detection; crisis situation social media ; text mining ; burst detection ; crisis situation", "DOI": "10.3390/info11070368", "PubYear": 2020, "Volume": "11", "Issue": "7", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, The University of Tokyo, Tokyo 113-8656, Japan↑Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, The University of Tokyo, Tokyo 113-8656, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, The University of Tokyo, Tokyo 113-0033, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, The University of Tokyo, Tokyo 113-8656, Japan"}], "References": []}, {"ArticleId": 83257366, "Title": "Low-light-level image enhancement algorithm based on integrated networks", "Abstract": "<p>In dark or poorly lit environments, it is often difficult for the naked eye to distinguish low-light-level images because of low brightness, low contrast and noise, and it is difficult to perform subsequent image processing (such as video surveillance and target detection). To solve these problems, this paper proposes a low-light-level image enhancement algorithm based on deep learning. First, the low-light-level image is segmented into several super-pixels, and the noise level of each super-pixel is estimated by the ratio of the local standard deviation to the local gradient. Then, the image is inverted and smoothed by a BM3D filter, and the structural filter adaptive method is used to obtain complete images without noise but with the correct texture. Finally, the noise-free image and texture-complete images are applied to the integrated network, which can not only enhance the contrast but also effectively prevent the over-enhancement of the contrast. The experimental results show that this method is superior to traditional methods in terms of both subjective and objective evaluation, and the peak signal–noise ratio and improved structural similarity are 31.64 dB and 91.2%, respectively.</p>", "Keywords": "Image processing; Light level image; De-noising; Contrast enhancement; Low-light-level integrated network (LLAON); BM3D filter", "DOI": "10.1007/s00530-020-00671-8", "PubYear": 2022, "Volume": "28", "Issue": "6", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Marine Science and Technology, Northwestern Polytechnical University, Xi’an, China; Key Laboratory of Ocean Acoustics and Sensing (Northwestern Polytechnical University), Ministry of Industry and Information Technology, Xi’an, China; School of Electronics and Information Engineering, Xi’an Technological University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Information Engineering, Xi’an Technological University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Ocean Acoustics and Sensing (Northwestern Polytechnical University), Ministry of Industry and Information Technology, Xi’an, China; School of Electronic Information and Artificial Intelligence, Shaanxi University of Science and Technology, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Information Engineering, Xi’an Technological University, Xi’an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Information Engineering, Xi’an Technological University, Xi’an, China"}], "References": []}, {"ArticleId": 83257393, "Title": "Single-channel EEG measurement of engagement in virtual rehabilitation: a validation study", "Abstract": "<p>Stroke rehabilitation suffers from low levels of patient engagement, impeding recovery. Virtual rehabilitation (VR) approaches can improve patient outcomes; however, there is limited understanding of the participant’s user experience and the field lacks a validated, objective measure of VR engagement. A neurophysiological measure of engagement in healthy adults was therefore examined, to inform future clinical studies. Twenty-four participants ( M <sub>age</sub> 26.7 years, range 18–47) interacted with a tabletop VR system ( Elements DNA, or EDNA), after which they rated their experience on the presence questionnaire (PQ). Separately, participants completed tasks eliciting low ( resting eyes-open and -closed) and high (EDNA VR and roller coaster simulation ) levels of engagement while continuous electroencephalogram (EEG) was recorded from a single, left pre-frontal electrode. EEG differences between the resting and simulation conditions included an increase in theta power ( p < 0.01) and a decrease in alpha power ( p < 0.01). Importantly, theta power in simulation conditions correlated with PQ scores expressing the hands-on EDNA VR experience ( r <sub> s </sub> = 0.38–0.48). In conclusion, the current results provide proof of concept that increased frontal theta power in healthy adults provides a valid measure of user engagement in VR simulation and participation. As the practical potential of VR is increasingly realised in stroke rehabilitation, objective EEG-based measures of engagement may provide a convenient and sensitive technique to assist in evaluating these interventions.</p>", "Keywords": "Engagement; Presence; Electroencephalogram; Rehabilitation; Virtual reality", "DOI": "10.1007/s10055-020-00460-8", "PubYear": 2021, "Volume": "25", "Issue": "2", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Medicine and Health, The University of Sydney, Sydney, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Behavioural and Health Sciences, Australian Catholic University, Melbourne, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Acoustic Laboratories, Sydney, Australia;Department of Linguistics, Macquarie University, Sydney, Australia;The HEARing CRC, Melbourne, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Psychology and Brain and Behaviour Research Institute, University of Wollongong, Wollongong, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Behavioural and Health Sciences and Centre for Disability and Development Research, Australian Catholic University, Melbourne, Australia"}], "References": []}, {"ArticleId": 83257428, "Title": "Design and simulation of a novel RF-MEMS tunable narrow band LC filter in the UHF band", "Abstract": "<p>The design and simulation of a continuously tunable LC filter by integration of microelectromechanical systems (MEMS) is presented in this work. The proposed tunable LC filter consists of a tunable capacitor and four inductors. The novelties of this RF-MEMS circuit include the integration of both mechanical suspensions with electrical inductors and the electrostatic actuator with the electrical capacitor. This filter is designed based on standard MetalMUMPs fabrication process trademark of MEMSCAP. With this filter, the frequency range from 2.5 to 3 GHz is achieved in the UHF band. High-quality performance is determined based on the 3 dB-bandwidth with about 83 MHz. The insertion loss of − 2.8 dB and the return loss of − 17.58 dB up to − 21.43 dB are other bold characteristic points of this design. The power consumption is near-zero thanks to the use of the electrostatic actuator. Therefore this filter can be used in low power and high-performance RF tuning applications. The group delay of this filter is less than 2.5 ns and the quality factor is from 45 to 85.</p>", "Keywords": "", "DOI": "10.1007/s00542-020-04959-7", "PubYear": 2021, "Volume": "27", "Issue": "1", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Sahand University of Technology, Tabriz, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Sahand University of Technology, Tabriz, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Sahand University of Technology, Tabriz, Iran"}], "References": [{"Title": "Fabrication and characterization of zinc oxide piezoelectric MEMS resonator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "2", "Page": "415", "JournalTitle": "Microsystem Technologies"}]}, {"ArticleId": 83257465, "Title": "Bag of feature and support vector machine based early diagnosis of skin cancer", "Abstract": "<p>Skin cancer is one of the diseases which lead to death if not detected at an early stage. Computer-aided detection and diagnosis systems are designed for its early diagnosis which may prevent biopsy and use of dermoscopic tools. Numerous researches have considered this problem and achieved good results. In automatic diagnosis of skin cancer through computer-aided system, feature extraction and reduction plays an important role. The purpose of this research is to develop computer-aided detection and diagnosis systems for classifying a lesion into cancer or non-cancer owing to the usage of precise feature extraction technique. This paper proposed the fusion of bag-of-feature method with speeded up robust features for feature extraction and quadratic support vector machine for classification. The proposed method shows the accuracy of 85.7%, sensitivity of 100%, specificity of 60% and training time of 0.8507 s in classifying the lesion. The result and analysis of experiments are done on the PH<sup>2</sup> dataset of skin cancer. Our method improves performance accuracy with an increase of 3% than other state-of-the-art methods.</p>", "Keywords": "Skin cancer; Computer-aided detection and diagnosis; Bag of feature; Support vector machine; Classification; SURF", "DOI": "10.1007/s00521-020-05212-y", "PubYear": 2022, "Volume": "34", "Issue": "11", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Amity Institute of Information Technology, Amity University Uttar Pradesh, Noida, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Amity School of Engineering and Technology, Amity University Uttar Pradesh, Noida, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Jamia Millia Islamia, New Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "ISEG, University of Lisbon, Lisbon, Portugal"}], "References": [{"Title": "An improved bag of dense features for skin lesion recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "3", "Page": "520", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}]}, {"ArticleId": 83257595, "Title": "The dilemma between arc and bounds consistency", "Abstract": "<p>Consistency enforcement is used to prune the search tree of a constraint satisfaction problem (CSP). Arc consistency (AC) is a well‐studied consistency level, with many implementations. Bounds consistency (BC), a looser consistency level, is known to have equal time complexity to AC. To solve a CSP, we have to implement an algorithm of our own or employ an existing solver. In any case, at some point, we have to decide between enforcing either AC or BC. As the choice between AC or BC is more or less predefined and currently made without considering the individualities of each CSP, this study attempts to make this decision deterministic and efficient, without the need of trial and error. We find that BC fits better while solving a CSP with its maximum domains' size being greater than its constrained variables number. We study the behavior of maintaining arc or bounds consistency during search, and we show how the overall search methods complexity is affected by the employed consistency level.</p>", "Keywords": "bounds consistency;constraint satisfaction;MAC;propagation;search", "DOI": "10.1002/int.22259", "PubYear": 2020, "Volume": "35", "Issue": "10", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics and Telecommunications, National and Kapodistrian University of Athens, Athens, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics and Telecommunications, National and Kapodistrian University of Athens, Athens, Greece"}], "References": []}, {"ArticleId": 83257615, "Title": "Adaptable and Verifiable BDI Reasoning", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.319.9", "PubYear": 2020, "Volume": "319", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Liverpool"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Liverpool"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Liverpool"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Liverpool"}], "References": []}, {"ArticleId": 83257616, "Title": "Establishing Reliable Robot Behavior using Capability Analysis Tables", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.319.3", "PubYear": 2020, "Volume": "319", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Naval Research Laboratory"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Naval Research Laboratory"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Naval Research Laboratory"}], "References": [{"Title": "Formal Specification and Verification of Autonomous Robotic Systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 83257645, "Title": "Proceedings of the First Workshop on Agents and Robots for reliable Engineered Autonomy", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.319.0", "PubYear": 2020, "Volume": "319", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Liverpool"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Liverpool"}, {"AuthorId": 3, "Name": "Daniela Briola", "Affiliation": "University of Milano-Bicocca"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Luxembourg"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Clausthal University of Technology"}], "References": []}, {"ArticleId": 83257719, "Title": "Extended visual cryptography-based copyright protection scheme for multiple images and owners using LBP–SURF descriptors", "Abstract": "<p>Existing visual cryptography ( VC )-based copyright protection schemes (<PERSON><PERSON> and <PERSON><PERSON><PERSON> in Multimed Tools Appl 75(14):8527–8543, 2016; <PERSON> and <PERSON> in IET Inf Secur 5(2):121–128, 2011) for multiple images provide meaningless shares to the owners. These shares create a suspicion that some secret information is shared. Also, these schemes require the share of every owner to prove the copyright. If any of the ownership share is not available, the copyright of these owners cannot be verified. This makes the usage of schemes restricted. To address these issues, an extended visual cryptography-based copyright protection scheme is proposed for multiple images with multiple owners. This scheme provides meaningful ownership shares to the owners, and their copyright can be verified by using a qualified set of owner shares. In this scheme, three types of shares are used, i.e., master share, ownership share and key share. The proposed scheme ensures robustness against different geometrical attacks, especially the rotation attack, as LBP and SURF together represent the host image efficiently. There is no restriction on watermark size, as SURF gives a flexibility to select any number of feature points. Usage of LBP ensures no false positive cases. Each of the ownership shares is created using the master share and the watermark. The ownership share is used to create a key share which is stored with the Trusting Authority ( TA ). To prove the copyright of multiple images, the ownership images and key share are superimposed to retrieve the watermark. The experimental results show that the scheme clearly verifies the copyright of digital images and is robust against several image processing attacks while having high imperceptibility. Comparisons with the existing copyright protection schemes show better performance of the proposed scheme.</p>", "Keywords": "Copyright protection; SURF; LBP; Extended visual cryptography; Normalized correlation; Multiple cover images; Multiple owners", "DOI": "10.1007/s00371-020-01883-9", "PubYear": 2021, "Volume": "37", "Issue": "6", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institue of Engineering and Technology, Patiala, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institue of Engineering and Technology, Patiala, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institue of Engineering and Technology, Patiala, India"}], "References": [{"Title": "A block-based RDWT-SVD image watermarking method using human visual system characteristics", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "19", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 83257740, "Title": "An equity-based incentive mechanism for persistent virtual world content service", "Abstract": "<p>Virtual world has the potential to become a future global electronic marketplace, integrating many isolated markets in many areas. To achieve this goal, future virtual world is required to be persistent, implying that a virtual world together with its accumulated content shall exist forever regardless of dynamic changes of its users and owners. Unfortunately, existing virtual worlds, owning by some entities, are not immune from death due to business entity failure. To provide a persistent virtual world, a decentralized architecture is explored, which is constructed on user contributed devices. However, there are many challenges to realize a decentralized virtual world. One important issue is user cooperation in reliable content storage. The devices contributed by users may not be reliable for maintaining all user contents, but users do not have the incentive to provide reliable devices for others. This paper addresses the issue by two steps. First, an indicator, called replica group reliability, is provided to users, which is based on the proposed replicability index. Based on the indicator, users can learn the reliability of their content storage. Then, a new user incentive mechanism, called equity-based node allocation strategy, is proposed to promote user cooperation to collectively maintain reliable content storage. A decentralized algorithm implementing the strategy is designed and the evaluation results show its effectiveness and efficiency.</p>", "Keywords": "Virtual world; Content service; Persistency; Replication; Incentive; Cooperation; Reliability", "DOI": "10.1007/s11761-020-00297-8", "PubYear": 2020, "Volume": "14", "Issue": "4", "JournalId": 4313, "JournalTitle": "Service Oriented Computing and Applications", "ISSN": "1863-2386", "EISSN": "1863-2394", "Authors": [{"AuthorId": 1, "Name": "Bingqing Shen", "Affiliation": "Faculty of Science and Technology, University of Macau, Macau, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Science and Technology, University of Macau, Macau, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Guo", "Affiliation": "Faculty of Science and Technology, University of Macau, Macau, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Science and Technology, University of Macau, Macau, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Science and Technology, University of Macau, Macau, China"}], "References": []}, {"ArticleId": 83257908, "Title": "Correction", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2020.1765948", "PubYear": 2020, "Volume": "36", "Issue": "15", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [], "References": []}, {"ArticleId": 83258058, "Title": "Evaluating higher education educators’ computer technology competencies in Libya", "Abstract": "<p>The purpose of this study was to evaluate issues related to the integration of technology into Libyan higher education from Libyan educators’ perspectives. A total of 161 Libyan educators who worked at the two main universities in Eastern Libya participated in the study. The study focused on four critical computer technology skill areas: (1) basic computer operation, (2) advanced computer operation, (3) use of the Internet, and (4) use of peripheral technologies. The three research objectives of the study were to (1) evaluate educators’ skill levels in four areas of computer competency, (2) investigate educators’ learning needs based on their specialty, and (3) explore educators’ teaching needs regarding types of support that allow them to improve classroom teaching. The results showed levels of perceived competency in each skill area differed significantly from perceived competence in each of the other areas. In addition, the results showed a statistically significant difference between educators who are from technical disciplines and non-technical disciplines in overall competence in using computer technologies. Furthermore, it showed that educators in technical disciplines expressed more competence in the basic and advanced computer operations. This comparison indicated a need to tailor training and implementation efforts to the needs of educators in various disciplines rather than using a standardized approach. The supplemental data using an open-ended question presented that type of support Libyan educators need to improve their teaching using computer technology. Finally, several recommendations were provided to the stakeholders in Libyan higher education.</p>", "Keywords": "Higher education; Technology competency; Technology integration", "DOI": "10.1007/s12528-020-09261-z", "PubYear": 2021, "Volume": "33", "Issue": "1", "JournalId": 3560, "JournalTitle": "Journal of Computing in Higher Education", "ISSN": "1042-1726", "EISSN": "1867-1233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Education and Behavioral Sciences, The University of Northern Colorado, Greeley, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Education and Behavioral Sciences, The University of Northern Colorado, Greeley, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Business, The Colorado State University, Fort Collins, USA"}], "References": []}, {"ArticleId": 83258104, "Title": "Generalized Shape Expansion-Based Motion Planning in Three-Dimensional Obstacle-Cluttered Environment", "Abstract": "A study expands the SE-SCP algorithm and presents the generalized shape expansion (GSE) algorithm for a three-dimensional (3-D) environment. Contrary to the shape expansion performed by the SE-SCP, which is restricted to a spherical one with a small radius, the GSE algorithm helps in expansion over a generalized shape, which is the best representative of the free space in the overall workspace that helps in exploring the free space in a much more efficient way. That is why the word ‘generalized’ is used here. To this end, a sampling-based motion-planning algorithm has been presented, which explored a two-dimensional (2-D) workspace leveraging the novel GSE algorithm. It was found to explore the free space in a very efficient way, which was reflected in its computational advantage over several other existing seminal algorithms.", "Keywords": "Motion Planning; Unmanned System; Six Degree of Freedom; Trajectory Optimization; Shortest Path Algorithms; Search Algorithm; Histograms; Mathematical Optimization; Unmanned Aerial Systems; Proportional Navigation", "DOI": "10.2514/1.G004756", "PubYear": 2020, "Volume": "43", "Issue": "9", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Chennai 600 036, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Chennai 600 036, India"}], "References": []}, {"ArticleId": 83258174, "Title": "ARCFIRE: Experimentation with the Recursive InterNetwork Architecture", "Abstract": "<p>European funded research into the Recursive Inter-Network Architecture (RINA) started with IRATI, which developed an initial prototype implementation for OS/Linux. IRATI was quickly succeeded by the PRISTINE project, which developed different policies, each tailored to specific use cases. Both projects were development-driven, where most experimentation was limited to unit testing and smaller scale integration testing. In order to assess the viability of RINA as an alternative to current network technologies, larger scale experimental deployments are needed. The opportunity arose for a project that shifted focus from development towards experimentation, leveraging Europe’s investment in Future Internet Research and Experimentation (FIRE+) infrastructures. The ARCFIRE project took this next step, developing a user-friendly framework for automating RINA experiments. This paper reports and discusses the implications of the experimental results achieved by the ARCFIRE project, using open source RINA implementations deployed on FIRE+ Testbeds. Experiments analyze the properties of RINA relevant to fast network recovery, network renumbering, Quality of Service, distributed mobility management, and network management. Results highlight RINA properties that can greatly simplify the deployment and management of real-world networks; hence, the next steps should be focused on addressing very specific use cases with complete network RINA-based networking solutions that can be transferred to the market.</p>", "Keywords": "RINA; experimentation; Quality of Service; resiliency; renumbering; mobility; network management RINA ; experimentation ; Quality of Service ; resiliency ; renumbering ; mobility ; network management", "DOI": "10.3390/computers9030059", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "imec IDLAB, Ghent University, B-9000 Ghent, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "imec IDLAB, Ghent University, B-9000 Ghent, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "imec IDLAB, Ghent University, B-9000 Ghent, Belgium"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Fundació i2CAT, Software Networks Research Area, 08034 Barcelona, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Fundació i2CAT, Software Networks Research Area, 08034 Barcelona, Spain"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Ericsson Network Management Lab, N37 Athlone, Ireland"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Nextworks s.r.l, Knowledge Services, 56122 Pisa, Italy"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Nextworks s.r.l, Knowledge Services, 56122 Pisa, Italy"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Telefónica Investigación y Desarrollo S.A, Technology Exploration, 28050 Madrid, Spain"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Metropolitan College, Computer Science, Boston University, Boston, MA 02215, USA"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Metropolitan College, Computer Science, Boston University, Boston, MA 02215, USA"}], "References": [{"Title": "Towards a RINA-Based Architecture for Performance Management of Large-Scale Distributed Systems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "2", "Page": "53", "JournalTitle": "Computers"}]}, {"ArticleId": 83258265, "Title": "Differentially private 1R classification algorithm using artificial bee colony and differential evolution", "Abstract": "Classification is an important topic in data mining field. Privacy preserving classification is a substantial subtopic that aims to perform classification of private data with satisfactory accuracy, while allowing sensitive information leakage at minimal level. Differential privacy is a strong privacy guarantee that determines privacy leakage ratio by using ϵ parameter; and enables privacy of individuals whose sensitive data are stored in a database. There exist some differentially private implementations of well-known classification algorithms such as ID3, random tree, random forests, Naïve Bayes, SVM, logistic regression, k-NN etc. Although One Rule (1R) is a simple but powerful classification algorithm, any implementation of differentially private 1R classification algorithm has not been proposed in the literature to our best knowledge. Motivated by this gap, first we propose a differentially private 1R classification algorithm (DP1R), then improve its performance by using metaheuristics that are differential evolution (DE) and artificial bee colony (ABC) in this study. Additionally, we also apply DE and ABC to improve performance of differentially private Naïve Bayes classifier and compare with DP1R. Moreover, DP1R is compared with the state-of-the-art differentially private algorithms such as differentially private SVM, differentially private logistic regression, differentially private ID3, and differentially private random tree on nine publicly available UCI datasets. The experimental results demonstrate that DP1R is an efficient classifier that has very similar accuracy to differentially private SVM which has the best accuracy results, however with respect to running time comparison of the methods, DP1R has the best performance among the all methods.", "Keywords": "Artificial bee colony ; Differential evolution ; Privacy preserving data mining ; One Rule ; Differential privacy ; Feature selection", "DOI": "10.1016/j.engappai.2020.103813", "PubYear": 2020, "Volume": "94", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Iskenderun Technical University, Hatay, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Cukurova University, Adana, Turkey"}], "References": [{"Title": "Decision Tree Classification with Differential Privacy", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 83258286, "Title": "Parallel mining method for symbol application features of complex network images", "Abstract": "Aiming at the problems of poor denoising effect, low recognition rate and long feature mining time in current methods, a parallel feature mining method of image symbol application features in complex network based on neural network learning control is proposed. First, the standard deviation of image symbol noise is calculated and denoising is carried out by filtering parameters. Then the image symbol is segmented by using the two-dimensional maximum inter-group variance method. Finally, the momentum coefficient and learning efficiency are introduced to calculate the parameters of the neural network, and the improved simulated annealing algorithm is used to adjust the learning efficiency of the neural network, so as to realise the parallel mining of image symbol application features of the complex network. Experimental results show that this method has good image denoising effect, high image recognition rate and short feature mining time, which verifies the comprehensive effectiveness of this method.", "Keywords": "complex networks ; image symbols ; application features ; parallel mining ; filtering denoising ; annealing algorithm", "DOI": "10.1504/IJICT.2020.108605", "PubYear": 2020, "Volume": "17", "Issue": "1", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "Chiyu Pan", "Affiliation": "Department of Art and Design, Mudanjiang Normal University, Mudanjiang157012, China"}], "References": []}, {"ArticleId": 83258287, "Title": "Research on security monitoring system for wind-solar complementary power generation based on internet of things", "Abstract": "When traditional system is used to monitor wind-solar complementary power generation, there are problems such as large errors in temperature and wind speed acquired and high power consumption of nodes when the sensor transmits signals. Therefore, a security monitoring system for wind-solar complementary power generation based on internet of things (IoT) is designed. The system hardware is composed of I/O control panel, sensor, GPRS and data processing board. The system software mainly acquires data and processes the acquisition interrupt program. On this basis, the security monitoring of wind-solar complementary power generation is finally realised. The analysis of simulation experiments shows that compared with the traditional system, the data acquisition error of the proposed system is small, which is in good agreement with the actual value. Moreover, the power consumption of nodes is only 15.8 mw when the sensor transmits the signal, indicating that the system has strong reliability and practical application value.", "Keywords": "internet of things ; IoTs ; wind-solar complementary ; sensor ; data acquisition ; power generation security ; monitoring system", "DOI": "10.1504/IJICT.2020.108609", "PubYear": 2020, "Volume": "17", "Issue": "1", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mathematics and Information Technology School, Yuncheng University, Yuncheng, 044000, Shanxi, China"}], "References": []}, {"ArticleId": 83258446, "Title": "Multiphase flow model and experimental study of pressure swing distillation for low pressure process of hydrochloric/water separation in hydrogen production", "Abstract": "Extraction of water from a concentrated HCl(aq) azeotropic solution is one of the challenging processes in recycling of anolyte within the copper-chlorine (Cu-Cl) cycle of hydrogen production. To pass the azeotropic point, a distillation process needs to take place in high pressure and low pressure columns. In this paper, a new lab-scale pressure swing distillation unit (PSDU) is developed and presented. The low pressure side of the PSDU is operated as a batch process to separate HCl(aq). A dynamic model for this process is formulated. Currently there is a lack of experimental data for this process so this study presents new measured data for the separation of HCl(aq). During the experiment, the temperature and concentration profiles are recorded as a bench-mark for reference in further next studies. The predictive model is verified with experimental results. The results of the experiments and simulations are provided, compared and discussed. The simulation results show reasonable agreement with the experimental data.", "Keywords": "Hydrogen production ; Thermochemical cycle ; Cu-Cl cycle ; Pressure swing distillation ; Water-HCl azeotropic mixture", "DOI": "10.1016/j.compchemeng.2020.107020", "PubYear": 2020, "Volume": "141", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Clean Energy Research Laboratory, Faculty of Engineering and Applied Science, University of Ontario Institute of Technology, 2000 Simcoe Street North, Oshawa, ON L1H 7K4, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Gebze Technical University, Gebze 41400 Kocaeli, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Clean Energy Research Laboratory, Faculty of Engineering and Applied Science, University of Ontario Institute of Technology, 2000 Simcoe Street North, Oshawa, ON L1H 7K4, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Clean Energy Research Laboratory, Faculty of Engineering and Applied Science, University of Ontario Institute of Technology, 2000 Simcoe Street North, Oshawa, ON L1H 7K4, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering and Applied Science, Memorial University of Newfoundland, 240 Prince Phillip Drive St. John's, NL A1B 3 × 5, Canada"}], "References": []}, {"ArticleId": 83258499, "Title": "Learning semantic information from Internet Domain Names using word embeddings", "Abstract": "Word embeddings is a well-known set of techniques widely used in Natural Language Processing (NLP). These techniques are able to learn words’ semantic based on the distributional hypothesis which states that words that are used and occur in the same contexts tend to purport similar meanings. This paper explores the usage of word embeddings in a new scenario to create a Vector Space Model (VSM) for Internet Domain Names (DNS). Our goal is to find semantically similar domains only using information of DNS queries without any knowledge about the content of those domains. The results presented here have practical applications in many engineering activities including websites recommendations, identification of fraudulent or risky sites, parental-control systems and anomaly detection in network traffic analysis (among others). We use the distributional hypothesis to learn the semantic of domain names from users’ web navigation patterns, validating empirically that domain names that occur in the same web sessions tend to have similar semantic. We also test different word embeddings techniques: word2vec , app2vec (considering time intervals between DNS queries), and fastText (which includes sub-word information). Due to the characteristics of domain names, we found fastText as the best option for building a VSM for DNS, being 10.5% superior than word2vec with Skip-Gram which was the next best technique considering the Mean Average Precision at k ( [email protected] ) metric, which compares the most similar domains in our VSM with the most similar domains provided by a third party source, namely, similar sites service offered by Alexa Internet, Inc.", "Keywords": "DNS ; Word embeddings ; Word2vec ; FastText app2vec ; Semantic similarity ; Natural language processing", "DOI": "10.1016/j.engappai.2020.103823", "PubYear": 2020, "Volume": "94", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Instituto de Computación, Facultad de Ingeniería, Universidad de la República, Montevideo, Uruguay;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instituto de Computación, Facultad de Ingeniería, Universidad de la República, Montevideo, Uruguay"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto de Computación, Facultad de Ingeniería, Universidad de la República, Montevideo, Uruguay"}], "References": []}, {"ArticleId": 83258549, "Title": "Influencing the influencers: the case of retailers' social shopping platforms", "Abstract": "Companies discovered throughout the years that in order to acquire, retain or grow clients, they should allow customers to take a more active role in the product or service creation. Customers have become so valued by companies that they are now considered as partners in co-creation and co-selling. This is reflected in social shopping communities where consumers connect to each other and share, discover, rate, recommend and purchase products. This act of peer-to-peer influencing is becoming crucial to brands especially when it is conducted on retailers' own social networking sites, such as on Amazon's Spark platform. Accordingly, the purpose of this paper is to examine influencers' effects on social shopping within retailers' social platforms. Based on a qualitative approach, 15 experts were interviewed to shed light on the underlying factors surrounding social shopping that is affected by influencers within a retailer's networking platform. The paper consequently suggests the needed strategies to be adopted by brands and retailers alike.", "Keywords": "social shopping ; influencers ; Amazon ; online communities", "DOI": "10.1504/IJWBC.2020.108626", "PubYear": 2020, "Volume": "16", "Issue": "3", "JournalId": 9397, "JournalTitle": "International Journal of Web Based Communities", "ISSN": "1477-8394", "EISSN": "1741-8216", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Lebanese American University, PO Box: 13-5053, <PERSON><PERSON><PERSON>, Beirut 1102 2801, Lebanon"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Lebanese American University, PO Box: 13-5053, <PERSON><PERSON><PERSON>, Beirut 1102 2801, Lebanon"}], "References": []}, {"ArticleId": 83258603, "Title": "Research on highway vehicle detection algorithm based on video image", "Abstract": "In order to solve the problem that vehicle detection rate can be affected in complex scenarios, authors put forward the adaptive method based on GMM which sets up and updates the background and use the average neighbourhood based on HSV fast shadow elimination algorithm which improves the speed of shadow elimination. For occluded vehicles, authors use the recognition algorithm based on <PERSON><PERSON> Filter which blocks vehicle identification, then authors adopts pyramid hierarchical search algorithm based on the template matching which segments the occluded vehicles. The experimental results show that the algorithm is simple and effective and the detection rate of vehicle is 97%, which meets the requirements of vehicle detection completely.", "Keywords": "vehicle occlusion ; GMM ; shadow removal ; Kalman Filter ; vehicle detection", "DOI": "10.1504/IJICT.2020.108604", "PubYear": 2020, "Volume": "17", "Issue": "1", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Energy and Power, Changsha University of Science and Technology, Changsha Hunan Province, 410076, China; Key Lab of Intelligent road and Vehicles Networking of Hunan Province, Changsha, Hunan, 410076, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Puyang Vocational and Technical College, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automobile and Mechanical Engineering, Changsha University of Science and Technology, Changsha, Hunan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of Intelligent road and Vehicles Networking of Hunan Province, Changsha, Hunan, 410076, China"}], "References": []}, {"ArticleId": 83258682, "Title": "Aligning IT and Business: Fostering Organizational Performance, Employees’ Commitment and Quality of Management Methods", "Abstract": "No Abstract", "Keywords": "", "DOI": "10.1080/1097198X.2020.1794134", "PubYear": 2020, "Volume": "23", "Issue": "3", "JournalId": 39199, "JournalTitle": "Journal of Global Information Technology Management", "ISSN": "1097-198X", "EISSN": "2333-6846", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Texas A&M University , San Antonio, Texas, USA,"}], "References": []}, {"ArticleId": 83258749, "Title": "A robust similarity based deep siamese convolutional neural network for gait recognition across views", "Abstract": "<p>Gait recognition has been considered as the emerging biometric technology for identifying the walking behaviors of humans. The major challenges addressed in this article is significant variation caused by covariate factors such as clothing, carrying conditions and view angle variations will undesirably affect the recognition performance of gait. In recent years, deep learning technique has produced a phenomenal performance accuracy on various challenging problems based on classification. Due to an enormous amount of data in the real world, convolutional neural network will approximate complex nonlinear functions in models to develop a generalized deep convolutional neural network (DCNN) architecture for gait recognition. DCNN can handle relatively large multiview datasets with or without using any data augmentation and fine‐tuning techniques. This article proposes a color‐mapped contour gait image as gait feature for addressing the variations caused by the cofactors and gait recognition across views. We have also compared the various edge detection algorithms for gait template generation and chosen the best from among them. The databases considered for our work includes the most widely used CASIA‐B dataset and OULP database. Our experiments show significant improvement in the gait recognition for fixed‐view, crossview, and multiview compared with the recent methodologies.</p>", "Keywords": "contour;convolutional neural network;covariate factors;deep learning;gait recognition;siamese;view change", "DOI": "10.1111/coin.12361", "PubYear": 2020, "Volume": "36", "Issue": "3", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Saveetha Engineering College, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, AMET University, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Saveetha Engineering College, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Tagore Engineering College, Chennai, India"}], "References": []}, {"ArticleId": 83258877, "Title": "Detection of global positioning system spoofing attack on unmanned aerial vehicle system", "Abstract": "<p>Most of the existing global positioning system (GPS) spoofing detection schemes are vulnerable to the generative GPS spoofing attack, or require additional auxiliary equipment and extensive signal processing capabilities, leading to defects such as low real-time performance and large communication overhead which are not available for the unmanned aerial vehicle (UAV, also known as drone) system. Therefore, we propose a novel solution which employs information fusion based on the GPS receiver and inertial measurement unit. We use a real-time model of tracking and calculating to derive the current position of the drones which are then contrasted with the position information received by the receiver to verify whether the presence or absence of spoofing attack. Subsequent experimental work shows that, the proposed method can accurately detect the spoof within 8 seconds, with a detection rate (DR) of 98.6%. Compared with the existing schemes, the performance of real-time detecting is improved while the DR is ensured. Even in our worst-case, we detect the spoof within 28 seconds after the UAV system starts its mission.</p>", "Keywords": "attack detection;drone;GPS spoofing;UAV", "DOI": "10.1002/cpe.5925", "PubYear": 2022, "Volume": "34", "Issue": "7", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Information and Navigation, Air Force Engineering University, Xi'an, China; School of Cyber Engineering, Xidian University, Xi'an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Wireless Security, Xi'an University of Posts and Telecommunications, Xi'an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi'an, China"}, {"AuthorId": 4, "Name": "Hongyang Yan", "Affiliation": "School of Computer Science, Guangzhou University, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information and Navigation, Air Force Engineering University, Xi'an, China"}, {"AuthorId": 6, "Name": "Xinghua Li", "Affiliation": "School of Cyber Engineering, Xidian University, Xi'an, China"}], "References": []}, {"ArticleId": 83258909, "Title": "Analytical Approach of a Pure Flow Mode Serpentine Path Rotary Magnetorheological Damper", "Abstract": "<p>This paper has two main goals in the development of a novel flow-mode magnetorheological brake (MRB): (1) produce a mathematical model of a flow-mode MRB and (2) predict the torque density of the proposed MRB compared to the other type of MRB. In this design, the flow mode MRB is made by screw pump to make the Magnetorheological Fluid (MRF) flow through the radial and annular channel. The serpentine path flux is developed in the proposed MRB to make the annular channel an active region as well. With the proposed design concept, the work of a pure flow-mode serpentine path MRB can be accomplished. In this study, Finite Element Method Magnetics (FEMM) is used to calculate the magnetic field applied to the active regions and analytical approach used to obtain the output damping torque. The simulation results show that the magnetic fluxes flow through the radial channel and annular channel as well. The radial and annular channel is activated, which led to higher output damping torque. The mathematical modelling shows that the helical angle of the screw pump significantly affects the damping torque. The results show that the output damping torque density can be adjusted from 42.18 N/mm2 in the off-state with 0 rpm to around 40,518.96 N/mm2 at 20 rpm. The torque density of the proposed MRB is higher than the shear mode MRB.</p>", "Keywords": "rotary damper; magnetorheological; flow mode; MRF; MR brake rotary damper ; magnetorheological ; flow mode ; MRF ; MR brake", "DOI": "10.3390/act9030056", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, Universitas Sebelas Maret, Jalan Ir. Sutami 36A, Kentingan, Surakarta 57126, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, Universitas Sebelas Maret, Jalan Ir. Sutami 36A, Kentingan, Surakarta 57126, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, Universitas Sebelas Maret, Jalan Ir. Sutami 36A, Kentingan, Surakarta 57126, Indonesia"}], "References": [{"Title": "A Concentric Design of a Bypass Magnetorheological Fluid Damper with a Serpentine Flux Valve", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "16", "JournalTitle": "Actuators"}]}, {"ArticleId": 83258938, "Title": "Intelligent Authentication Model in a Hierarchical Wireless Sensor Network With Multiple Sinks", "Abstract": "<p>A wireless sensor network consists of a number of sensors laid out in a field with mobile sinks dynamically aggregating data from the nodes. Sensitive applications such as military environment require the sink to identify if a sensor that it visits is legitimate, and in turn, the sensor has to ensure that the sink is authenticated to access its sensitive data. For the system to intelligently learn the credentials of non-malicious sink and non-malicious sensors based on the dynamically observed data, four approaches using access control lists, authenticator tokens, message digests, and elliptic curve variant of RSA algorithm are proposed along with the formal logic for correctness. The experimented data is analysed using false acceptance rate, false rejection rate, precision, and curve analysis parameters. The approaches are further compared based on the attacks they are vulnerable to and execution time, ultimately concluding that exchange of message digests and elliptic curve RSA algorithm are more widely applicable.</p>", "Keywords": "", "DOI": "10.4018/IJNCR.2020070103", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 24918, "JournalTitle": "International Journal of Natural Computing Research", "ISSN": "1947-928X", "EISSN": "1947-9298", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chaitanya Bharathi Institute of Technology, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Roorkee, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SRM Institute of Science and Technology, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nazarbayev University, Kazakhstan"}], "References": []}, {"ArticleId": 83258993, "Title": "The parents' tale: Why parents resist the educational use of smartphones at schools?", "Abstract": "This study examined the level of parental resistance to the use of smartphones in schools, as well as the predictors and the factors underlying parental resistance. Data was collected from a sample of 220 parents of elementary and secondary school students who completed an online questionnaire. The participants ranked four different factors for resisting and rejecting the use of smartphones in schools: social, environmental, economic and pedagogical. Parents’ actual resistance level was also measured, from “no resistance”, through “passive resistance”, to “active resistance”. Furthermore, the study examined the association between parental resistance and four parenting styles: authoritarian, authoritative, permissive, and uninvolved, as well as associations with demographic and socioeconomic variables. About two-thirds of the parents expressed resistance toward the use of smartphones in school, and more than half of them expressed active resistance to such use. Social and economic factors were reported to underlie resistance to the use of smartphones in school to a great extent, whereas pedagogical resistance factor was reported to a low extent in all parental resistance levels Nevertheless, pedagogical and social resistance factors predicted a high level of parental resistance. Authoritative parenting style was found to be a negative predictor of parental resistance. Implications of the findings are discussed in relation to educational theory and the challenges of policy-makers who cope with parental resistance towards the integration of smartphones in school learning.", "Keywords": "Learning with smartphones ; Parental resistance ; Resistance factors ; Parenting style", "DOI": "10.1016/j.compedu.2020.103984", "PubYear": 2020, "Volume": "157", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "Shl<PERSON>t Hadad", "Affiliation": "Department of Education and Psychology, The Open University of Israel, Holon Institute of Technology, 1 University Road, P.O.B. 808, <PERSON><PERSON><PERSON><PERSON>, 43107, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Mei<PERSON>r-<PERSON>", "Affiliation": "Department of Education and Psychology, The Open University of Israel, Holon Institute of Technology, 1 University Road, P.O.B. 808, <PERSON><PERSON><PERSON><PERSON>, 43107, Israel"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Education and Psychology, The Open University of Israel, Holon Institute of Technology, 1 University Road, P.O.B. 808, <PERSON><PERSON><PERSON><PERSON>, 43107, Israel;Corresponding author;https://www.openu.ac.il/en/personalsites/ProfInaBlau.aspx"}], "References": []}, {"ArticleId": 83259017, "Title": "A Machine Learning Approach to Tracking and Characterizing Planar or Near Planar Fluid Flow", "Abstract": "<p>This paper presents a framework to segment planar or near-planar fluid flow and uses artificial neural networks to characterize fluid flow by determining the rate of flow and source of the fluid, which can be applied in various areas (e.g., characterizing fluid flow in surface irrigation from aerial pictures, in leakage detection, and in surgical robotics for characterizing blood flow over an operative site). For the latter, the outcome enables to assess bleeding severity and find the source of the bleeding. Based on its importance in assessing injuries and from a medical perspective in directing the course of surgery, fluid flow assessment is deemed to be a desirable addition to a surgical robot's capabilities. The results from tests on fluid flows generated from a test rig show that the proposed methods can contribute to an automated characterization of fluid flow, which in the presence of several fluid flow sources can be achieved by tracking the flows, determining the locations of the sources and their relative severities, with execution times suitable for real-time operation.</p>", "Keywords": "", "DOI": "10.4018/IJNCR.**********", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 24918, "JournalTitle": "International Journal of Natural Computing Research", "ISSN": "1947-928X", "EISSN": "1947-9298", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Mauritius, Mauritius"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Loughborough University, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Loughborough University, UK"}], "References": []}, {"ArticleId": 83259018, "Title": "Recognition of Historical Handwritten Kannada Characters Using Local Binary Pattern Features", "Abstract": "<p>Archaeological departments throughout the world have undertaken massive digitization projects to digitize their historical document corpus. In order to provide worldwide visibility to these historical documents residing in the digital libraries, a character recognition system is an inevitable tool. Automatic character recognition is a challenging problem as it needs a cautious blend of enhancement, segmentation, feature extraction, and classification techniques. This work presents a novel holistic character recognition system for the digitized Estampages of Historical Handwritten Kannada Stone Inscriptions (EHHKSI) belonging to 11th century. First, the EHHKSI images are enhanced using Retinex and Morphological operations to remove the degradations. Second, the images are segmented into characters by connected component labeling. Third, LBP features are extracted from these characters. Finally, decision tree is used to learn these features and classify the characters into appropriate classes. The LBP features improved the performance of the system significantly.</p>", "Keywords": "", "DOI": "10.4018/IJNCR.2020070101", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 24918, "JournalTitle": "International Journal of Natural Computing Research", "ISSN": "1947-928X", "EISSN": "1947-9298", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON><PERSON> G.", "Affiliation": "BMS Institute of Technology and Management, Visveswaraya Technological University, Bangalore, India"}, {"AuthorId": 2, "Name": " Chandrakala H. T.", "Affiliation": "Government First Grade College Madhugiri, Tumkur University, Karnataka, India"}], "References": []}, {"ArticleId": 83259022, "Title": "Extracellular matrix permeability/efficacy assay tip (E-PAT) to realize three-dimensional cell-based screening", "Abstract": "In this work, an extracellular matrix permeability/efficacy assay tip (E-PAT), which can be used in a conventional pipette to simultaneously test drug permeability and efficacy, was developed by growing cells in the extracellular matrix (ECM) layer in a hole at the outlet (bottom) of E-PAT. In the existing permeability/efficacy assay technique conducted using the transwell assays, the Cell/ECM layers were formed on permeable polymer membranes; however, the production of small membranes to realize transwell miniaturization is expensive and difficult. To resolve this issue, in the proposed approach using E-PAT, small Cell/ECM layers were formed in the hole at the outlet (bottom) of the tip without membranes. A pipette was connected to the E-PAT to easily aspirate the Cell/ECM mixture into the hole of the tip, allowing the formation of miniature Cell/ECM layers at the bottom of the tip. Subsequently, the E-PAT was detached from the pipette and dipped into a well filled with media. The cells in the ECM grew three-dimensionally and made Cell/ECM layer in the E-PAT. The permeability/efficacy assays were performed using the Cell/ECM layer in E-PAT. The compounds or nanoparticles supplied through the inlet (hole) at the top of the E-PAT penetrated the Cell/ECM layers and were diluted by the media in the well. By measuring the concentration of the compounds or nanoparticles in the well and the cell viability in the Cell/ECM layer, the permeability and efficacy/cytotoxicity of the compounds in the proposed E-PAT could be concurrently estimated with a high throughput. In particular, E-PAT measured the IC<sub>50</sub> (half maximal inhibitory concentration) for 3.383 μM doxorubicin (DOX) and indicated that 73 % 4 μM DOX penetrated the Cell/ECM layers, inducing the death of 57.2 % HepG2 cells. Moreover, the device demonstrated that FITC-SiO<sub>2</sub> nanoparticles with a diameter of 25 nm permeated higher into the ECM and killed more HepG2 cells under the serum (fetal bovine serum, FBS) free media conditions than under the serum containing media conditions. The E-PAT could thus measure the compound efficacy as well as the compound permeability. The proposed tip can be a valuable tool to determine whether the efficacy/cytotoxicity of a compound is because of its low permeability.", "Keywords": "Nanoparticle permeability ; 3D cell culture ; Efficiacy assay", "DOI": "10.1016/j.snb.2020.128624", "PubYear": 2020, "Volume": "321", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Konyang University, Daejeon 35365, Republic of Korea;Center for Nano-Bio Measurement, Industrial Metrology, Korea Research Institute of Standards and Science (KRISS), Yuseong-Gu, Daejeon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Graduate School of Analytical Science and Technology (GRAST), Chungnam National University, 99 Daehak-ro(St), Yuseong-gu, Daejeon 34134, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> K<PERSON>k", "Affiliation": "Center for Nano-Bio Measurement, Industrial Metrology, Korea Research Institute of Standards and Science (KRISS), Yuseong-Gu, Daejeon, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Nano-Bio Measurement, Industrial Metrology, Korea Research Institute of Standards and Science (KRISS), Yuseong-Gu, Daejeon, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Center for Nano-Bio Measurement, Industrial Metrology, Korea Research Institute of Standards and Science (KRISS), Yuseong-Gu, Daejeon, Republic of Korea;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, Konyang University, Daejeon 35365, Republic of Korea;Corresponding authors"}], "References": []}, {"ArticleId": 83259032, "Title": "Energy-efficient activity recognition framework using wearable accelerometers", "Abstract": "Acceleration data for activity recognition typically are collected on battery-powered devices, leading to a trade-off between high-accuracy recognition and energy-efficient operation. We investigate this trade-off from a feature selection perspective, and propose an energy-efficient activity recognition framework with two key components: a detailed energy consumption model and a number of feature selection algorithms. We evaluate the model and the algorithms using Random Forest classifiers to quantify the recognition accuracy, and find that the multi-objective Particle Swarm Optimization algorithm achieves the best results for the task. The results show that by selecting appropriate groups of features, energy consumption for computation and data transmission is reduced by an order of magnitude compared with the raw-data approach, and that the framework presents a flexible selection of feature groups that allow the designer to choose an appropriate accuracy-energy trade-off for a specific target application.", "Keywords": "Feature selection ; Activity recognition ; Wearables", "DOI": "10.1016/j.jnca.2020.102770", "PubYear": 2020, "Volume": "168", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Electronics and Computer Science, 14 Dzerbenes St., LV-1006, Riga, Latvia;Department of Electrical and Electronic Engineering, University of Bristol, 1 Cathedral Square, Bristol, BS15DD, UK;Corresponding author. Institute of Electronics and Computer Science, 14 Dzerbenes St., LV-1006, Riga, Latvia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, University of Bristol, 1 Cathedral Square, Bristol, BS15DD, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, University of Bristol, 1 Cathedral Square, Bristol, BS15DD, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, University of Bristol, 1 Cathedral Square, Bristol, BS15DD, UK"}], "References": [{"Title": "A Comprehensive Survey on Attacks, Security Issues and Blockchain Solutions for IoT and IIoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102481", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "A–Z survey of Internet of Things: Architectures, protocols, applications, recent advances, future directions and recommendations", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "163", "Issue": "", "Page": "102663", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 83259062, "Title": "Detection of Anomalous Transactions in Mobile Payment Systems", "Abstract": "<p>Mobile payment systems are providing an opportunity for smartphone users for transferring money to each other with ease. This simple way of transferring through mobile payment systems has great potential for economic activity. However, fraudulent transactions may occur and can have a substantial impact on the economy of a country. Financial fraud and anomalous transactions can cause a loss of billions of dollars annually. Therefore, there is a need to detect anomalous transactions through mobile payment systems to prevent financial fraud. For this research study, a synthetic dataset is generated by using a PAYSIM simulator due to the lack of availability of a realistic dataset. This research study performed experiments on a financial transactional dataset using eight data mining classification algorithms. The performance of classification models was measured by using evaluation metrics: accuracy, precision, F-score, recall, and specificity. A comparative analysis of classification models was also performed based on their performance.</p>", "Keywords": "", "DOI": "10.4018/IJDA.**********", "PubYear": 2020, "Volume": "1", "Issue": "2", "JournalId": 71163, "JournalTitle": "International Journal of Data Analytics", "ISSN": "2644-1705", "EISSN": "2644-1713", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Lahore, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Lahore, Pakistan"}], "References": []}, {"ArticleId": 83259063, "Title": "Enriching SME Learning and Innovation Through Inter-Organizational Knowledge Transfer", "Abstract": "<p>Small and medium enterprises (SMEs) are today considered a driving force in several nations' economies. Inter-organizational knowledge transfer (IOKT) enables organizations including SMEs to improve operational and strategic performance. To enhance SMEs' competitive advantages, they need external knowledge to enrich learning capabilities. This study uses qualitative methodology to examine the values that can be generated from the IOKT process in SMEs. Participants were 10 Omani SMEs from the ICT sector, a knowledge-intensive sector. Based on face-to-face, semi-structured interviews, the results confirmed that inter-organizational knowledge transfer is considered important to SMEs. Results indicated that learning benefits are gained from informal IOKT activities at those SMEs, but they do not yet show a strong impact on the innovation performance of organizations. Thus, this study provides valuable insights for researchers and practitioners interested in the SME domain to use as a basis for their investigations.</p>", "Keywords": "", "DOI": "10.4018/IJKM.2020070102", "PubYear": 2020, "Volume": "16", "Issue": "3", "JournalId": 31939, "JournalTitle": "International Journal of Knowledge Management", "ISSN": "1548-0666", "EISSN": "1548-0658", "Authors": [{"AuthorId": 1, "Name": "Himyar Al-Jabri", "Affiliation": "Sultan Qaboos University, Oman"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sultan Qaboos University, Oman"}], "References": []}, {"ArticleId": 83259076, "Title": "Toward fault-tolerant and secure frequent itemset mining outsourcing in hybrid cloud environment", "Abstract": "Due to the rising costs of maintaining IT infrastructures for large-scale data mining, it is becoming a trend for data owners to outsource data mining tasks together with storage to cloud service providers, however, which also arouses security concerns on unauthorized breaches of data confidentiality and result integrity. Existing solutions yet seldom protect data privacy whilst guaranteeing result integrity. To address these issues, this paper proposes a series of privacy-preserving building blocks by employing Shamir’s secret sharing scheme. Based on those subprotocols, an efficient frequent itemset mining protocol is designed under hybrid cloud setting, in which the public unreliable cloud and semi-trusted cloud cooperate to mine frequent patterns over the encrypted database. Our scheme not only protects the privacy of datasets from frequency analysis attack, but also verifies the integrity of mining results. Theoretical analysis demonstrates that the scheme ensures security as well as fault tolerance under our threat model. Experimental evaluations show that our proposed protocol outperforms the similar solution regarding efficiency while it can detect and correct cloud servers’ errors effectively.", "Keywords": "Frequent itemset mining ; Privacy preservation ; Integrity verification ; Fault tolerance ; Hybrid cloud environment", "DOI": "10.1016/j.cose.2020.101969", "PubYear": 2020, "Volume": "98", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electronic Science and Technology, National University of Defense Technology, Changsha, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Science and Technology, National University of Defense Technology, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Electronic Science and Technology, National University of Defense Technology, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronic Science and Technology, National University of Defense Technology, Changsha, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Science and Technology, National University of Defense Technology, Changsha, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Electronic Science and Technology, National University of Defense Technology, Changsha, China"}], "References": []}, {"ArticleId": 83259352, "Title": "A novel method for extracting skeleton of fruit tree from 3D point clouds", "Abstract": "Tree skeleton could be useful to agronomy researchers because the skeleton describes the shape and topological structure of a tree. The phenomenon of organs’ mutual occlusion in fruit tree canopy is usually very serious, this should result in a large amount of data missing in directed laser scanning 3D point clouds from a fruit tree. However, traditional approaches can be ineffective and problematic in extracting the tree skeleton correctly when the tree point clouds contain occlusions and missing points. To overcome this limitation, we present a method for accurate and fast extracting the skeleton of fruit tree from laser scanner measured 3D point clouds. The proposed method selects the start point and endpoint of a branch from the point clouds by user’s manual interaction, then a backward searching is used to find a path from the 3D point cloud with a radius parameter as a restriction. The experimental results in several kinds of fruit trees demonstrate that our method can extract the skeleton of a leafy fruit tree with highly accuracy.", "Keywords": "", "DOI": "10.1142/S1793962320500518", "PubYear": 2020, "Volume": "11", "Issue": "6", "JournalId": 12484, "JournalTitle": "International Journal of Modeling, Simulation, and Scientific Computing", "ISSN": "1793-9623", "EISSN": "1793-9615", "Authors": [{"AuthorId": 1, "Name": "Shenglian Lu", "Affiliation": "College of Computer Science and Information Technology, Guangxi Normal University, Guilin 541004, P. R. China;Guangxi Key Lab of Multi-Source Information Mining and Security, No. 15 Yucai Road, Guilin 541004, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Information Technology, Guangxi Normal University, Guilin 541004, P. R. China;Guangxi Key Lab of Multi-Source Information Mining and Security, No. 15 Yucai Road, Guilin 541004, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Center of Information Technology, Beijing Municipal Bureau of Agriculture and Rural Affairs, No. 6 Yumin Middle Rood, Beijing 100029, P. R. China"}], "References": []}, {"ArticleId": 83259360, "Title": "Model-free control of electrically driven robot manipulators using an extended state observer", "Abstract": "This paper deals with the robust control of robot manipulators by presenting a novel voltage-based control approach. For this purpose, an Extended State Observer (ESO) is utilized to estimate the dynamical behaver of the system without any need to use the equations of motion including robot dynamics, actuator dynamics and their parameters in the proposed method. Convergence analysis proves that the estimated parameters converge to actual parameters asymptotically. The boundedness of tracking error is guaranteed through the <PERSON><PERSON><PERSON><PERSON> theorem. Simulation results on an articulated robot driven by permanent magnet DC motors, and experimental implementation on a SCARA robot show that the proposed model-free controller has a satisfactory performance as compared to an adaptive uncertainty estimation-based controller.", "Keywords": "Model-free control ; Voltage-based control approach ; Extended state observer ; Robot manipulator", "DOI": "10.1016/j.compeleceng.2020.106768", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Robotic Engineering, Shahrood University, Shahrood, Iran;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Robotic Engineering, Shahrood University, Shahrood, Iran"}], "References": []}]