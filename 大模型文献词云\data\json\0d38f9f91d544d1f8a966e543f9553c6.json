[{"ArticleId": 112094451, "Title": "TS2ACT", "Abstract": "<p>Human Activity Recognition (HAR) based on embedded sensor data has become a popular research topic in ubiquitous computing, which has a wide range of practical applications in various fields such as human-computer interaction, healthcare, and motion tracking. Due to the difficulties of annotating sensing data, unsupervised and semi-supervised HAR methods are extensively studied, but their performance gap to the fully-supervised methods is notable. In this paper, we proposed a novel cross-modal co-learning approach called TS2ACT to achieve few-shot HAR. It introduces a cross-modal dataset augmentation method that uses the semantic-rich label text to search for human activity images to form an augmented dataset consisting of partially-labeled time series and fully-labeled images. Then it adopts a pre-trained CLIP image encoder to jointly train with a time series encoder using contrastive learning, where the time series and images are brought closer in feature space if they belong to the same activity class. For inference, the feature extracted from the input time series is compared with the embedding of a pre-trained CLIP text encoder using prompt learning, and the best match is output as the HAR classification results. We conducted extensive experiments on four public datasets to evaluate the performance of the proposed method. The numerical results show that TS2ACT significantly outperforms the state-of-the-art HAR methods, and it achieves performance close to or better than the fully supervised methods even using as few as 1% labeled data for model training. The source codes of TS2ACT are publicly available on GitHub1.</p>", "Keywords": "", "DOI": "10.1145/3631445", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, Nanjing, Jiangsu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, Nanjing, Jiangsu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, Nanjing, Jiangsu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, Nanjing, Jiangsu, China"}], "References": [{"Title": "Weakly Supervised Multi-Task Representation Learning for Human Activity Analysis Using Wearables", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Cross-subject transfer learning in human activity recognition systems using generative adversarial networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "426", "Issue": "", "Page": "26", "JournalTitle": "Neurocomputing"}, {"Title": "SelfHAR", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Unsupervised Human Activity Representation Learning with Multi-task Deep Clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Determinants of Longitudinal Adherence in Smartphone-Based Self-Tracking for Chronic Health Conditions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Leveraging Activity Recognition to Enable Protective Behavior Detection in Continuous Data", "Authors": "<PERSON>ng<PERSON> Wang; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Multimodal Co-learning: Challenges, applications with datasets, recent advances and future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "", "Page": "203", "JournalTitle": "Information Fusion"}, {"Title": "Zero-Shot Learning for IMU-Based Activity Recognition Using Video Embeddings", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "IoT-Based Smart Health Monitoring System for COVID-19", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "2", "Page": "137", "JournalTitle": "SN Computer Science"}, {"Title": "Learning Disentangled Beha<PERSON> Patterns for Wearable-based Human Activity Recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Bootstrapping Human Activity Recognition Systems for Smart Homes from Scratch", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Bootstrapping Human Activity Recognition Systems for Smart Homes from Scratch", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Predicting Performance Improvement of Human Activity Recognition Model by Additional Data Collection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Assessing the State of Self-Supervised Human Activity Recognition Using Wearables", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Augmented Adversarial Learning for Human Activity Recognition with Partial Sensor Sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Cross-Modality Graph-based Language and Sensor Data Co-Learning of Human-Mobility Interaction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 112094531, "Title": "Noise-robust voice conversion using adversarial training with multi-feature decoupling", "Abstract": "Most existing voice conversion methods focus primarily on separating speech content from speaker information while overlooking the decoupling of pitch information. Additionally, the quality of converted speech significantly degrades when the speech of the target speaker is contaminated by noises. To address these issues, this paper proposes a noise-robust voice conversion model with multi-feature decoupling based on adversarial training . The proposed framework utilizes three distinct encoders to encode speech content, speaker identity, and pitch information independently, which aims to enhance the performance of decoupling by minimizing their mutual information and reduce the correlations between feature vectors. Moreover, a gradient reversal layer and a noise decoupling discriminator are incorporated into the framework, which extracts noise-resistant speaker representations and content representations through adversarial training to facilitate the synthesis of high-quality speech. In order to optimize the learning process, a training strategy is developed which involves alternating between clean and noisy data during the training of the encoder. This strategy effectively guides and expedites the convergence of the model. Experimental results demonstrate that compared to the state-of-the-art baselines of noise-robust voice conversion, the proposed model achieves improvements around 0.31 and 0.39 in terms of speech naturalness and speaker similarity evaluation metrics , respectively.", "Keywords": "", "DOI": "10.1016/j.engappai.2023.107807", "PubYear": 2024, "Volume": "131", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Intelligent Information Processing, Army Engineering University, Nanjing, 210007, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Intelligent Information Processing, Army Engineering University, Nanjing, 210007, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Intelligent Information Processing, Army Engineering University, Nanjing, 210007, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Intelligent Information Processing, Army Engineering University, Nanjing, 210007, China;Corresponding author"}], "References": [{"Title": "Parallel voice conversion with limited training data using stochastic variational deep kernel learning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "105279", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Waveform level adversarial example generation for joint attacks against both automatic speaker verification and spoofing countermeasures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105469", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "T-distributed stochastic neighbor embedding echo state network with state matrix dimensionality reduction for time series prediction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106055", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 112094625, "Title": "Incorporating CNN-LSTM and SVM with wavelet transform methods for tourist passenger flow prediction", "Abstract": "<p>Effective management of urban rail transit systems increasingly centers on short-term passenger flow prediction which is a key factor in operational scheduling decisions. The critical need to use existing infrastructure efficiently and avoid potential emergencies due to large crowd gatherings. The advancements in short-term passenger flow forecasting have become essential within the intelligent transportation system domain. Despite its importance, there is a prominent gap in research specifically addressing the prediction of various types of passenger flows in subway systems. To bridge the gap, the paper proposed an innovative integrated method combining CNN-LSTM and SVM with Wavelet Transform harnessing their collective strengths. The approach involves a multi-stage process. Initially, passenger flow data is dissected into high and low-frequency series using wavelet transform. In the prediction phase, CNN-LSTM and SVM techniques are employed to, respectively learn and forecast these high and low-frequency sequence. The final phase involves the reintegration of these various predicted sequences through wavelet transform. The integrated approach aims to provide a more accurate prediction of short-term passenger flows in urban rail transit catering to the specific demands of the complex and dynamic field. The analysis shows that the SVM based Wavelet Transform method outperforms the CNN-LSTM based Wavelet Transform method in terms of efficiency and accuracy. Both approaches achieve better results than classical prediction techniques for passenger flow prediction on evaluation metrics, i.e., MAPE (9.80% and 7.28%), VAPE (0.78% and 3.85%), RMSE (2.48% and 0.76%), MSE (14.86% and 4.65%), R <sup>2</sup> (1.57% and 12.57%) and accuracy (85.00% and 90.00%) respectively.</p>", "Keywords": "Urban rail transit; Passenger flow prediction; Forecasting; Support vector machine (SVM); Wavelet transform method; CNN; LSTM", "DOI": "10.1007/s00500-023-09592-w", "PubYear": 2024, "Volume": "28", "Issue": "3", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Tourism, Henan University of Animal Husbandry and Economy, Zhengzhou, China; Corresponding author."}], "References": [{"Title": "Short-term traffic flow prediction based on improved wavelet neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "14", "Page": "8181", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A novel prediction model for the inbound passenger flow of urban rail transit", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "566", "Issue": "", "Page": "347", "JournalTitle": "Information Sciences"}, {"Title": "Deep learning models for forecasting aviation demand time series", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "23", "Page": "16329", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Performance improvement for metro passenger flow forecast using spatio-temporal deep neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "2", "Page": "983", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Passenger flow prediction in bus transportation system using deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "9", "Page": "12519", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Metro passenger flow forecasting though multi-source time-series fusion: An ensemble deep learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "108644", "JournalTitle": "Applied Soft Computing"}, {"Title": "An effective spatiotemporal deep learning framework model for short-term passenger flow prediction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "12", "Page": "5523", "JournalTitle": "Soft Computing"}, {"Title": "A new traffic flow prediction model based on cosine similarity variational mode decomposition, extreme learning machine and iterative error compensation strategy", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "105234", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A CNN-Bi_LSTM parallel network approach for train travel time prediction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "256", "Issue": "", "Page": "109796", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Data preprocessing strategy in constructing convolutional neural network classifier based on constrained particle swarm optimization with fuzzy penalty function", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105580", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Short-term urban rail transit passenger flow forecasting based on fusion model methods using univariate time series", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "147", "Issue": "", "Page": "110740", "JournalTitle": "Applied Soft Computing"}, {"Title": "Improving deep learning-based image super-resolution with residual learning and perceptual loss using SRGAN model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "21", "Page": "16041", "JournalTitle": "Soft Computing"}, {"Title": "Multivariate long-time series traffic passenger flow prediction using causal convolutional sparse self-attention MTS-Informer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "34", "Page": "24207", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An origin–destination passenger flow prediction system based on convolutional neural network and passenger source-based attention mechanism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121989", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 112094886, "Title": "Selective and Adaptive Incremental Transfer Learning with Multiple Datasets for Machine Fault Diagnosis", "Abstract": "The visions of Industry 4.0 and 5.0 have reinforced the industrial environment. They have also made artificial intelligence incorporated as a major facilitator. Diagnosing machine faults has become a solid foundation for automatically recognizing machine failure, and thus timely maintenance can ensure safe operations. Transfer learning is a promising solution that can enhance the machine fault diagnosis model by borrowing pre-trained knowledge from the source model and applying it to the target model, which typically involves two datasets. In response to the availability of multiple datasets, this paper proposes using selective and adaptive incremental transfer learning (SA-ITL), which fuses three algorithms, namely, the hybrid selective algorithm, the transferability enhancement algorithm, and the incremental transfer learning algorithm. It is a selective algorithm that enables selecting and ordering appropriate datasets for transfer learning and selecting useful knowledge to avoid negative transfer. The algorithmalso adaptively adjusts the portion of training data to balance the learning rate and training time. The proposed algorithm is evaluated and analyzed using ten benchmark datasets. Compared with other algorithms fromexisting works, SA-ITL improves the accuracy of all datasets. Ablation studies present the accuracy enhancements of the SA-ITL, including the hybrid selective algorithm (1.22%–3.82%), transferability enhancement algorithm (1.91%–4.15%), and incremental transfer learning algorithm (0.605%–2.68%). These also show the benefits of enhancing the target model with heterogeneous image datasets that widen the range of domain selection between source and target domains. © 2024 Tech Science Press. All rights reserved.", "Keywords": "Deep learning; incremental learning; machine fault diagnosis; negative transfer; transfer learning", "DOI": "10.32604/cmc.2023.046762", "PubYear": 2024, "Volume": "78", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "Kwok Tai Chui", "Affiliation": "School of Science and Technology, Hong Kong Metropolitan University, Hong Kong; 999077, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, Asia University, Taichung, 41354, Taiwan; Center for Advanced Information Technology, Kyung Hee University, Seoul, 02447, South Korea; Symbiosis Centre for Information Technology, Symbiosis International University, Maharashtra, Pune, 411042, India; Department of Electrical and Computer Engineering, Lebanese American University, Beirut, 1102, Lebanon; School of Computing, Skyline University College, Sharjah, 1797, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, Asia University, Taichung, 41354, Taiwan; Center for Interdisciplinary Research, University of Petroleum and Energy Studies, Dehradun, 248007, India; Department of Computer Science & Engineering, Chandigarh University, Chandigarh, 160036, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Centro de Investigación en Computación, Instituto Politécnico Nacional, UPALM-Zacatenco, Mexico City, 07320, Mexico"}], "References": [{"Title": "A systematic review of deep transfer learning for machinery fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "407", "Issue": "", "Page": "121", "JournalTitle": "Neurocomputing"}, {"Title": "A review on fault detection and diagnosis techniques: basics and beyond", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "5", "Page": "3639", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A new ensemble convolutional neural network with diversity regularization for fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "964", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "AFTTM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Cloud Applications and Computing"}, {"Title": "A multi-source information transfer learning method with subdomain adaptation for cross-domain fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "243", "Issue": "", "Page": "108466", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Reactive Hybrid Model for Fault Mitigation in Real-Time Cloud Computing", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Cloud Applications and Computing"}, {"Title": "Measuring lane-changing trajectories by employing context-based modified dynamic time warping", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "216", "Issue": "", "Page": "119489", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 112094902, "Title": "shinyDeepDR: A user-friendly R Shiny app for predicting anti-cancer drug response using deep learning", "Abstract": "Advancing precision oncology requires accurate prediction of treatment response and accessible prediction models. To this end, we present shinyDeepDR, a user-friendly implementation of our innovative deep learning model, DeepDR, for predicting anti-cancer drug sensitivity. The web tool makes DeepDR more accessible to researchers without extensive programming experience. Using shinyDeepDR, users can upload mutation and/or gene expression data from a cancer sample (cell line or tumor) and perform two main functions: &quot;Find Drug,&quot; which predicts the sample’s response to 265 approved and investigational anti-cancer compounds, and &quot;Find Sample,&quot; which searches for cell lines in the Cancer Cell Line Encyclopedia (CCLE) and tumors in The Cancer Genome Atlas (TCGA) with genomics profiles similar to those of the query sample to study potential effective treatments. shinyDeepDR provides an interactive interface to interpret prediction results and to investigate individual compounds. In conclusion, shinyDeepDR is an intuitive and free-to-use web tool for in silico anti-cancer drug screening.", "Keywords": "R Shiny app ; web tool ; deep learning ; drug response ; prediction ; cancer ; DSML3: Development/Pre-production: Data science output has been rolled out/validated across multiple domains/problems", "DOI": "10.1016/j.patter.2023.100894", "PubYear": 2024, "Volume": "5", "Issue": "2", "JournalId": 74143, "JournalTitle": "Patterns", "ISSN": "2666-3899", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cancer Therapeutics Program, University of Pittsburgh Medical Center Hillman Cancer Center, Pittsburgh, PA 15232, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cancer Therapeutics Program, University of Pittsburgh Medical Center Hillman Cancer Center, Pittsburgh, PA 15232, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Greehey Children’s Cancer Research Institute, University of Texas Health San Antonio, San Antonio, TX 78229, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Cancer Therapeutics Program, University of Pittsburgh Medical Center Hillman Cancer Center, Pittsburgh, PA 15232, USA"}, {"AuthorId": 5, "Name": "Satdarshan P. <PERSON>", "Affiliation": "Department of Pathology, University of Pittsburgh School of Medicine, Pittsburgh, PA 15261, USA;Pittsburgh Liver Research Center, University of Pittsburgh Medical Center and University of Pittsburgh School of Medicine, Pittsburgh, PA 15261, USA;Department of Medicine, University of Pittsburgh School of Medicine, Pittsburgh, PA 15261, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Medicine, University of Pittsburgh School of Medicine, Pittsburgh, PA 15261, USA;Cancer Virology Program, University of Pittsburgh Medical Center Hillman Cancer Center, Pittsburgh, PA 15232, USA;Department of Electrical and Computer Engineering, Swanson School of Engineering, University of Pittsburgh, Pittsburgh, PA 15261, USA;Department of Pharmaceutical Sciences, University of Pittsburgh, Pittsburgh, PA 15261, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Greehey Children’s Cancer Research Institute, University of Texas Health San Antonio, San Antonio, TX 78229, USA;Department of Population Health Sciences, University of Texas Health San Antonio, San Antonio, TX 78229, USA;Corresponding author"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cancer Therapeutics Program, University of Pittsburgh Medical Center Hillman Cancer Center, Pittsburgh, PA 15232, USA;Pittsburgh Liver Research Center, University of Pittsburgh Medical Center and University of Pittsburgh School of Medicine, Pittsburgh, PA 15261, USA;Department of Medicine, University of Pittsburgh School of Medicine, Pittsburgh, PA 15261, USA;Corresponding author"}], "References": [{"Title": "Investigation of REFINED CNN ensemble learning for anti-cancer drug sensitivity prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Ranadip <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "Supplement_1", "Page": "i42", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 112094915, "Title": "Thermal parameter inversion of low-heat cement concrete for Baihetan arch dam", "Abstract": "For the first time, low-heat cement was used in the entire dam section of Baihetan Dam, but the thermal properties of low-heat cement under construction conditions have yet to be fully studied. The thermal parameter values of low-heat cement may differ significantly from the indoor test values or specification values due to factors such as ambient temperature, cooling through water, and surface insulation under actual site conditions. Therefore, in order to obtain more accurate values of the thermal parameters, the hybrid swarm intelligence algorithm and field temperature monitoring data are used to identify the concrete thermal parameters of Baihetan arch dam . To overcome the shortcomings of Particle Swarm Optimization (PSO) that is easy to fall into local optimum and Artificial Bee Colony (ABC) that has insufficient development ability, an Integrated Algorithm Based on ABC and PSO (IABAP) is established. Through eight different test functions and comparing with other different algorithms, it is verified that the IABAP algorithm has certain advantages in terms of convergence speed and accuracy. Considering the influence of ambient air temperature and multi-shift water cooling during construction, IABAP is applied to the inversion of concrete thermal parameters with the same strength, different strength and different gradation of Baihetan arch dam. The computational results show the good performance of the IABAP algorithm in engineering applications on the one hand, and the applicability and reliability of the parameter inversion on the other hand, which can meet the accuracy requirements of practical engineering. At the same time, the conjecture that the thermal parameters are consistent in adjacent dam sections was verified by bringing the thermal parameters into the adjacent dam sections for simulation calculations. Finally, the experimental values of thermal parameters of low-heat cement concrete of Baihetan Dam were compared with the inverse values to analyze the change law of thermal parameters, and it was found that the final adiabatic temperature rise of low-heat concrete was smaller than the indoor experimental values during the actual construction.", "Keywords": "", "DOI": "10.1016/j.engappai.2023.107823", "PubYear": 2024, "Volume": "131", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hubei Key Laboratory of Construction and Management in Hydropower Engineering, China Three Gorges University, Yichang 443002, China;College of Hydraulic & Environmental Engineering, China Three Gorges University, Yichang 443002, China;Corresponding author. Hubei Key Laboratory of Construction and Management in Hydropower Engineering, China Three Gorges University, Yichang 443002, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Construction and Management in Hydropower Engineering, China Three Gorges University, Yichang 443002, China;College of Hydraulic & Environmental Engineering, China Three Gorges University, Yichang 443002, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hubei Key Laboratory of Construction and Management in Hydropower Engineering, China Three Gorges University, Yichang 443002, China;College of Hydraulic & Environmental Engineering, China Three Gorges University, Yichang 443002, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Construction and Management in Hydropower Engineering, China Three Gorges University, Yichang 443002, China;College of Hydraulic & Environmental Engineering, China Three Gorges University, Yichang 443002, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Construction and Management in Hydropower Engineering, China Three Gorges University, Yichang 443002, China;College of Hydraulic & Environmental Engineering, China Three Gorges University, Yichang 443002, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Materials and Structure, Changjiang River Scientific Research Institute, Wuhan 430010, China"}], "References": [{"Title": "Recent trends in the use of statistical tests for comparing swarm and evolutionary computing algorithms: Practical guidelines and a critical review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100665", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Exploratory differential ant lion-based optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "113548", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Joint set-up of parameters in genetic algorithms and the artificial bee colony algorithm: an approach for cultivation process modelling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "3", "Page": "2015", "JournalTitle": "Soft Computing"}, {"Title": "Chaotic coyote algorithm applied to truss optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "242", "Issue": "", "Page": "106353", "JournalTitle": "Computers & Structures"}, {"Title": "An improved LSHADE-RSP algorithm with the Cauchy perturbation: iLSHADE-RSP", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "215", "Issue": "", "Page": "106628", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Optimal design of truss structures with frequency constraints: a comparative study of DE, IDE, LSHADE, and CMAES algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "2", "Page": "1499", "JournalTitle": "Engineering with Computers"}, {"Title": "ABFIA: A hybrid algorithm based on artificial bee colony and <PERSON><PERSON><PERSON><PERSON> indicator algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "61", "Issue": "", "Page": "101651", "JournalTitle": "Journal of Computational Science"}, {"Title": "Dung beetle optimizer: a new meta-heuristic algorithm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "7", "Page": "7305", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 112094922, "Title": "Audience selection for maximizing social influence", "Abstract": "<p>Viral marketing campaigns target primarily those individuals who are central in social networks and hence have social influence. Marketing events, however, may attract diverse audience. Despite the importance of event marketing, the influence of heterogeneous target groups is not well understood yet. In this paper, we define the Audience Selection (AS) problem in which different sets of agents need to be evaluated and compared based on their social influence. A typical application of Audience selection is choosing locations for a series of marketing events. The Audience selection problem is different from the well-known Influence Maximization (IM) problem in two aspects. Firstly, it deals with sets rather than nodes. Secondly, the sets are diverse, composed by a mixture of influential and ordinary agents. Thus, Audience selection needs to assess the contribution of ordinary agents too, while IM only aims to find top spreaders. We provide a systemic test for ranking influence measures in the Audience Selection problem based on node sampling and on a novel statistical method, the Sum of Ranking Differences. Using a Linear Threshold diffusion model on two online social networks, we evaluate eight network measures of social influence. We demonstrate that the statistical assessment of these influence measures is remarkably different in the Audience Selection problem, when low-ranked individuals are present, from the IM problem, when we focus on the algorithm’s top choices exclusively.</p>", "Keywords": "Influence maximization; sum of ranking differences; innovation spreading; audience selection; linear threshold diffusion model", "DOI": "10.1017/nws.2023.23", "PubYear": 2024, "Volume": "12", "Issue": "1", "JournalId": 25796, "JournalTitle": "Network Science", "ISSN": "2050-1242", "EISSN": "2050-1250", "Authors": [{"AuthorId": 1, "Name": "Balázs R<PERSON>", "Affiliation": "Institute of Economics, HUN-REN Centre for Economic and Regional Studies, Budapest, Hungary Department of Operations Research and Actuarial Sciences, Corvinus University of Budapest, Budapest, Hungary; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Economics, HUN-REN Centre for Economic and Regional Studies, Budapest, Hungary Corvinus Institute for Advanced Studies, Corvinus University of Budapest, Budapest, Hungary Institute of Data Analytics and Information Systems, Corvinus University of Budapest, Budapest, Hungary"}], "References": [{"Title": "Discovering the Hidden Community Structure of Public Transportation Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "1", "Page": "209", "JournalTitle": "Networks and Spatial Economics"}, {"Title": "Finding early adopters of innovation in social networks", "Authors": "Balázs <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "13", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Axiomatic characterization of PageRank", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "318", "Issue": "", "Page": "103900", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": 112094952, "Title": "Towards robust neural networks via orthogonal diversity", "Abstract": "Deep Neural Networks (DNNs) are vulnerable to invisible perturbations on the images generated by adversarial attacks , which raises researches on the adversarial robustness of DNNs. A series of methods represented by the adversarial training and its variants have proven as one of the most effective techniques in enhancing the DNN robustness. Generally, adversarial training focuses on enriching the training data by involving perturbed data. Such data augmentation effect of the involved perturbed data in adversarial training does not contribute to the robustness of DNN itself and usually suffers from clean accuracy drop. Towards the robustness of DNN itself, we in this paper propose a novel defense that aims at augmenting the model in order to learn features that are adaptive to diverse inputs, including adversarial examples . More specifically, to augment the model, multiple paths are embedded into the network, and an orthogonality constraint is imposed on these paths to guarantee the diversity among them. A margin-maximization loss is then designed to further boost such DIversity via Orthogonality (DIO). In this way, the proposed DIO augments the model and enhances the robustness of DNN itself as the learned features can be corrected by these mutually-orthogonal paths. Extensive empirical results on various data sets, structures and attacks verify the stronger adversarial robustness of the proposed DIO utilizing model augmentation. Besides, DIO can also be flexibly combined with different data augmentation techniques ( e.g. , TRADES and DDPM), further promoting robustness gains.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.110281", "PubYear": 2024, "Volume": "149", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Image Processing and Pattern Recognition, Department of Automation, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "Qinghua Tao", "Affiliation": "ESAT-STADIUS, KU Leuven, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Image Processing and Pattern Recognition, Department of Automation, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Image Processing and Pattern Recognition, Department of Automation, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Central Media Technology Institute, Huawei Technologies Ltd., China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Central Media Technology Institute, Huawei Technologies Ltd., China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Image Processing and Pattern Recognition, Department of Automation, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Image Processing and Pattern Recognition, Department of Automation, Shanghai Jiao Tong University, Shanghai, China;Corresponding author"}], "References": [{"Title": "A survey of robust adversarial training in pattern recognition: Fundamental, theory, and methodologies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108889", "JournalTitle": "Pattern Recognition"}, {"Title": "Query efficient black-box adversarial attack on deep neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109037", "JournalTitle": "Pattern Recognition"}, {"Title": "Improving adversarial robustness by learning shared information", "Authors": "<PERSON>; <PERSON><PERSON>-Margulies; Shuchin Aeron", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109054", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 112094978, "Title": "AI Pilot in the Cockpit: An Investigation of Public Acceptance", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2301856", "PubYear": 2025, "Volume": "41", "Issue": "1", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Safety Science and Engineering, Civil Aviation University of China, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Purdue University, West Lafayette, IN, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Safety Science and Engineering, Civil Aviation University of China, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Purdue University, West Lafayette, IN, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Safety Science and Engineering, Civil Aviation University of China, Tianjin, China"}], "References": [{"Title": "Trust and Distrust of Automated Parking in a Tesla Model X", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "2", "Page": "194", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "The importance of the assurance that “humans are still in the decision loop” for public trust in artificial intelligence: Evidence from an online experiment", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "106572", "JournalTitle": "Computers in Human Behavior"}, {"Title": "How People Perceive and Expect Safety in Autonomous Vehicles: An Empirical Study for Risk Sensitivity and Risk-related Feelings", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "340", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Effect of relative status on responsibility attributions in human–robot collaboration: Mediating role of sense of responsibility and moderating role of power distance orientation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "122", "Issue": "", "Page": "106820", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Single Pilot Operations IN Commercial Cockpits: Background, Challenges, and Options", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "What makes an AI device human-like? The role of interaction quality, empathy and perceived psychological anthropomorphic characteristics in the acceptance of artificial intelligence in the service industry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "122", "Issue": "", "Page": "106855", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Exploring older adults’ perception and use of smart speaker-based voice assistants: A longitudinal study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "124", "Issue": "", "Page": "106914", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The roles of trust, personalization, loss of privacy, and anthropomorphism in public acceptance of smart healthcare services", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "107026", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Exploring the influence of anxiety, pleasure and subjective knowledge on public acceptance of fully autonomous vehicles", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "107187", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Review of the Factors Affecting Acceptance of AI-Infused Systems", "Authors": "<PERSON><PERSON><PERSON> Vahob<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "66", "Issue": "1", "Page": "126", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "AI-enabled investment advice: Will users buy it?", "Authors": "<PERSON>n <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "107481", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Explainable AI: The Effect of Contradictory Decisions and Explanations on Users’ Acceptance of AI Systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "9", "Page": "1807", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "“Baby, I Can’t Drive My Car”: How Controllability Mediates the Relationship between Personality and the Acceptance of Autonomous Vehicles?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "17", "Page": "4698", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Exploring the User Acceptance of Urban Air Mobility: Extending the Technology Acceptance Model with Trust and Service Quality Factors", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "14", "Page": "2893", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 112095009, "Title": "Transductive classification via patch alignment", "Abstract": "<p>In this paper, a novel approach for transductive classification is proposed. Unlike existing methods that heavily rely on constructing the Laplacian matrix to capture data distribution, the proposed approach takes a unique path. It employs a linear transformation model to create local patches for each data point and then unifies them in an objective function to build the Laplacian matrix. Incorporating this Laplacian matrix into the transductive classification framework allows us to assign optimal class labels globally. The experimental results from toy data and real world databases demonstrate that the proposed approach achieves more efficient and stable performance, while this approach is insensitive to the parameters. Notably, our method exhibits robustness to parameter variations, making it highly adaptable to practical applications.</p>", "Keywords": "", "DOI": "10.3233/AIC-220179", "PubYear": 2024, "Volume": "37", "Issue": "1", "JournalId": 20835, "JournalTitle": "AI Communications", "ISSN": "0921-7126", "EISSN": "1875-8452", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Information Engineering, Jiangsu Second Normal University, Nanjing, China;Jiangsu Province Engineering Research Center of Basic Education Big Data Application, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of computer science and technology, China University of mining and technology, Xuzhou, China;Jiangsu Vocational Institute of Architectural Technology, Jiangsu Collaborative Innovation Center for Building Energy Saving and Construct Technology, Xuzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Information Engineering, Jiangsu Second Normal University, Nanjing, China;Jiangsu Province Engineering Research Center of Basic Education Big Data Application, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Information Engineering, Jiangsu Second Normal University, Nanjing, China;Jiangsu Province Engineering Research Center of Basic Education Big Data Application, Nanjing, China"}], "References": [{"Title": "A survey on semi-supervised learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "2", "Page": "373", "JournalTitle": "Machine Learning"}, {"Title": "Graph-based semi-supervised learning: A review", "Authors": "<PERSON><PERSON>; <PERSON>; Qing Yan", "PubYear": 2020, "Volume": "408", "Issue": "", "Page": "216", "JournalTitle": "Neurocomputing"}, {"Title": "Comparative analysis of image classification algorithms based on traditional machine learning and deep learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "141", "Issue": "", "Page": "61", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A new transductive learning method with universum data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "8", "Page": "5571", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 112095014, "Title": "Human-Computer Interaction in Times of Grief: Unveiling Support Processes Among COVID-19 Bereaved Users in a Facebook Group Through Netnography", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2023.2301259", "PubYear": 2025, "Volume": "41", "Issue": "1", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "General Psychology,Dipartimento di Psicologia Generale, University of Padova, Padova, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Australian Institute for Suicide Research and Prevention, School of Applied Psychology, Griffith University, Brisbane, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Australian Institute for Suicide Research and Prevention, School of Applied Psychology, Griffith University, Brisbane, Australia;Slovene Centre for Suicide Research, University of Primorska, Koper, Slovenia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "General Psychology,Dipartimento di Psicologia Generale, University of Padova, Padova, Italy"}], "References": []}, {"ArticleId": *********, "Title": "Looking forward to the new year", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.patter.2023.100916", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 74143, "JournalTitle": "Patterns", "ISSN": "2666-3899", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Editor-in-Chief, <PERSON><PERSON><PERSON>"}], "References": []}, {"ArticleId": *********, "Title": "Position and Attitude Tracking Controllers Using Lyapunov Transformations for Quadrotors", "Abstract": "<p>In this paper, a novel feedback control strategy for quadrotor trajectory tracking is designed and experimentally tested with proof of exponential stability, using the <PERSON><PERSON><PERSON><PERSON> transformations theory. The controller is derived from an inner-outer loop control structure, namely by considering the position system coupled through an interconnection term with the attitude system. For the design of the position controller, the considered dynamics are worked on the body frame, which is uncommon in the literature, and its synthesis derives from theories such as <PERSON><PERSON><PERSON>’s maximum principle, Lyapunov theory, and Linear Quadratic Regulator (LQR), which ensure Input-to-state stability, steady-state optimality, and global exponential stability. The attitude system is based on an error quaternion parameterization via a nonlinear coordinate transformation matrix followed by a state input feedback, rendering the system linear and time-invariant. Under a correct transformation, LQR theory ensures almost exponential stability and steady-state optimality for the overall interconnected closed-loop systems. Experimental and simulation results illustrate the performance of the tracking system onboard a quadrotor.</p>", "Keywords": "Nonlinear control; Interconnected systems; Stability; Quadrotor", "DOI": "10.1007/s10846-023-02016-9", "PubYear": 2024, "Volume": "110", "Issue": "1", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IDMEC - Institute of Mechanical Engineering, Instituto Superior Técnico, Lisboa, Portugal; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IDMEC - Institute of Mechanical Engineering, Instituto Superior Técnico, Lisboa, Portugal; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IDMEC - Institute of Mechanical Engineering, Instituto Superior Técnico, Lisboa, Portugal; ISR - Institute for Systems and Robotics, Instituto Superior Técnico, Lisboa, Portugal; Corresponding author."}], "References": [{"Title": "Feedback Linearization with Zero Dynamics Stabilization for Quadrotor Control", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Exponentially Stable Motion Control for Multirotor UAVs with Rotor Drag and Disturbance Compensation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "A Review of Quadrotor Unmanned Aerial Vehicles: Applications, Architectural Design and Control Algorithms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "104", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}]}, {"ArticleId": 112095129, "Title": "ChatGPT Related Principles and Future Development", "Abstract": "", "Keywords": "", "DOI": "10.20431/2349-4859.1001001", "PubYear": 2024, "Volume": "10", "Issue": "1", "JournalId": 42295, "JournalTitle": "International Journal of Research Studies in Computer Science and Engineering", "ISSN": "", "EISSN": "2349-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 112095143, "Title": "BigDEC: A multi-algorithm Big Data tool based on the k -mer spectrum method for scalable short-read error correction", "Abstract": "Despite the significant improvements in both throughput and cost provided by modern Next-Generation Sequencing (NGS) platforms, sequencing errors in NGS datasets can still degrade the quality of downstream analysis. Although state-of-the-art correction tools can provide high accuracy to improve such analysis, they are limited to apply a single correction algorithm while also requiring long runtimes when processing large NGS datasets. Furthermore, current parallel correctors generally only provide efficient support for shared-memory systems lacking the ability to scale out across a cluster of multicore nodes, or they require the availability of specific hardware devices or features. In this paper we present a Big Data Error Correction (BigDEC) tool that overcomes all those limitations by: (1) implementing three different error correction algorithms based on the widely extended k -mer spectrum method; (2) providing scalable performance for large datasets by efficiently exploiting the capabilities of Big Data technologies on multicore clusters based on commodity hardware; (3) supporting two different Big Data processing frameworks (Spark and Flink) to provide greater flexibility to end users; (4) including an efficient, stream-based merge operation to ease downstream processing of the corrected datasets; and (5) significantly outperforming existing parallel tools, being up to 79% faster on a 16-node multicore cluster when using the same underlying correction algorithm. BigDEC is publicly available to download at https://github.com/UDC-GAC/BigDEC .", "Keywords": "Big Data processing ; Next-Generation Sequencing (NGS) ; Error correction ; Apache Spark ; Apache Flink", "DOI": "10.1016/j.future.2024.01.011", "PubYear": 2024, "Volume": "154", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidade da Coruña, CITIC, Computer Architecture Group, Elviña, 15071 A Coruña, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade da Coruña, CITIC, Computer Architecture Group, Elviña, 15071 A Coruña, Spain"}], "References": [{"Title": "SMusket: Spark-based DNA error correction on distributed-memory systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "698", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "CARE: context-aware sequencing read error correction", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "7", "Page": "889", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 112095164, "Title": "HapticPilot", "Abstract": "<p>The emergence of vibrotactile feedback in hand wearables enables immersive virtual reality (VR) experience with whole-hand haptic rendering. However, existing haptic rendering neglects inconsistent sensations caused by hand postures. In our study, we observed that changing hand postures alters the distribution of vibrotactile signals which might degrade one's haptic perception. To address the issues, we present HapticPilot which allows an in-situ haptic experience design for hand wearables in VR. We developed an in-situ authoring system supporting instant haptic design. In the authoring tool, we applied our posture-adaptive haptic rendering algorithm with a novel haptic design abstraction called phantom grid. The algorithm adapts phantom grid to the target posture and incorporates 1D & 2D phantom sensation with a unique actuator arrangement to provide a whole-hand experience. With this method, HapticPilot provides a consistent haptic experience across various hand postures is available. Through measuring perceptual haptic performance and collecting qualitative feedback, we validated the usability of the system. In the end, we demonstrated our system with prospective VR scenarios showing how it enables an intuitive, empowering, and responsive haptic authoring framework.</p>", "Keywords": "", "DOI": "10.1145/3631453", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "KAIST, Daehak-ro, Yuseong-gu, Daejeon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "KAIST, Daejeon, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "KAIST, Daehak-ro, Yuseong-gu, Daejeon, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "HITSZ, Shenzhen, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "KAIST, Daehak-ro, Yuseong-gu, Daejeon, Republic of Korea"}], "References": [{"Title": "Smart Tactile Gloves for Haptic Interaction, Communication, and Rehabilitation", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "2", "Page": "2100091", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "ColabAR: A Toolkit for Remote Collaboration in Tangible Augmented Reality Laboratories", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 112095166, "Title": "Orientation-Aware 3D SLAM in Alternating Magnetic Field from Powerlines", "Abstract": "<p>Identifying new sensing modalities for indoor localization is an interest of research. This paper studies powerline-induced alternating magnetic field (AMF) that fills the indoor space for the orientation-aware three-dimensional (3D) simultaneous localization and mapping (SLAM). While an existing study has adopted a uniaxial AMF sensor for SLAM in a plane surface, the design falls short of addressing the vector field nature of AMF and is therefore susceptible to sensor orientation variations. Moreover, although the higher spatial variability of AMF in comparison with indoor geomagnetism promotes location sensing resolution, extra SLAM algorithm designs are needed to achieve robustness to trajectory deviations from the constructed map. To address the above issues, we design a new triaxial AMF sensor and a new SLAM algorithm that constructs a 3D AMF intensity map regularized and augmented by a Gaussian process. The triaxial sensor's orientation estimation is free of the error accumulation problem faced by inertial sensing. From extensive evaluation in eight indoor environments, our AMF-based 3D SLAM achieves sub-1m to 3m median localization errors in spaces of up to 500 m2, sub-2° mean error in orientation sensing, and outperforms the SLAM systems based on Wi-Fi, geomagnetism, and uniaxial AMF by more than 30%.</p>", "Keywords": "", "DOI": "10.1145/3631446", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Singtel Cognitive and AI Lab for Enterprises, Nanyang Technological University, Singapore and School of Computer Science and Engineering, Nanyang Technological University, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Singtel Cognitive and AI Lab for Enterprises, Nanyang Technological University, Singapore and School of Computer Science and Engineering, Nanyang Technological University, Singapore"}, {"AuthorId": 3, "Name": "Zhenyu Yan", "Affiliation": "Department of Information Engineering, The Chinese University of Hong Kong, China and Singtel Cognitive and Artificial Intelligence Lab for Enterprises, Nanyang Technological University, Singapore"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Informatics, University of Edinburgh, UK"}], "References": []}, {"ArticleId": 112095224, "Title": "A boundary value problem for a non-linear difference equation", "Abstract": "A boundary value problem for a non-linear difference equation of order three is considered. We show that this equation can be interpreted as the equation satisfied by the value function in a stochastic optimal control problem. We thus obtain an expression for the solution of the non-linear difference equation that can be used to find an explicit solution to this equation. An example is presented. Copyright © 2023. The Author(s).", "Keywords": "dynamic programming; first-passage time; higher-order difference equations; homing problem; optimal control", "DOI": "10.24425/acs.2023.148883", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 30654, "JournalTitle": "Archives of Control Sciences", "ISSN": "1230-2384", "EISSN": "2300-2611", "Authors": [], "References": []}, {"ArticleId": 112095238, "Title": "Measuring Subjective Differences Objectively", "Abstract": "<p>Wide color gamut displays reproduce colors encoded in large color spaces. However, they may encounter challenges stemming from observer metameric mismatch.</p>", "Keywords": "", "DOI": "10.1002/msid.1453", "PubYear": 2024, "Volume": "40", "Issue": "1", "JournalId": 59974, "JournalTitle": "Information Display", "ISSN": "0362-0972", "EISSN": "2637-496X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "; Corresponding author."}], "References": [{"Title": "Metameric failure assessment and reduction between RGB and laser phosphor projectors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "30", "Issue": "4", "Page": "292", "JournalTitle": "Journal of the Society for Information Display"}, {"Title": "Vision Science Calls for Perception‐Friendly Displays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "1", "Page": "7", "JournalTitle": "Information Display"}]}, {"ArticleId": 112095323, "Title": "Investigating Generalizability of Speech-based Suicidal Ideation Detection Using Mobile Phones", "Abstract": "<p>Speech-based diaries from mobile phones can capture paralinguistic patterns that help detect mental illness symptoms such as suicidal ideation. However, previous studies have primarily evaluated machine learning models on a single dataset, making their performance unknown under distribution shifts. In this paper, we investigate the generalizability of speech-based suicidal ideation detection using mobile phones through cross-dataset experiments using four datasets with N=786 individuals experiencing major depressive disorder, auditory verbal hallucinations, persecutory thoughts, and students with suicidal thoughts. Our results show that machine and deep learning methods generalize poorly in many cases. Thus, we evaluate unsupervised domain adaptation (UDA) and semi-supervised domain adaptation (SSDA) to mitigate performance decreases owing to distribution shifts. While SSDA approaches showed superior performance, they are often ineffective, requiring large target datasets with limited labels for adversarial and contrastive training. Therefore, we propose sinusoidal similarity sub-sampling (S3), a method that selects optimal source subsets for the target domain by computing pair-wise scores using sinusoids. Compared to prior approaches, S3 does not use labeled target data or transform features. Fine-tuning using S3 improves the cross-dataset performance of deep models across the datasets, thus having implications in ubiquitous technology, mental health, and machine learning.</p>", "Keywords": "", "DOI": "10.1145/3631452", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dartmouth College, Hanover, New Hampshire, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, USA"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, USA"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}, {"AuthorId": 14, "Name": "<PERSON><PERSON>", "Affiliation": "University of Washington, Seattle, USA"}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": "Dartmouth College, Hanover, USA"}], "References": [{"Title": "A hierarchical depression detection model based on vocal and emotional cues", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "441", "Issue": "", "Page": "279", "JournalTitle": "Neurocomputing"}, {"Title": "StudentSADD", "Authors": "ML Tlachac; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "GLOBEM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 112095360, "Title": "Collaborative Learning in the Edu-Metaverse Era: An Empirical Study on the Enabling Technologies", "Abstract": "Computer-supported collaborative learning aims to use information technologies to support collaborative knowledge construction by practicing the relevant pedagogical approaches, especially in the distance learning setting. The enabling technologies are fast advancing, and the need for solutions during the COVID-19 global pandemic led to the emergence of the Edu-Metaverse, which is conceptualized as a collection of networked virtual worlds (i.e., the Metaverse) for learning. There is a great necessity to investigate how these more recent enabling technologies can support collaborative learning. This empirical study aims to collect both quantitative and qualitative results to fill the knowledge gaps. Specifically, 20 undergraduate students (three females and 17 males) taking the Game Design and Development course voluntarily participated in this study. The participants used three representative collaboration platforms (i.e., AltSpace, Gather, and ZOOM) in our laboratory for discussing three course-specific topics, simulating the undertaking of collaborative learning tasks in the distance learning setting. The results suggest that the participants were more engaged in the learning activities using the Metaverse platforms that offer avatar-mediated communications and collaborations (i.e., AltSpace and Gather). These platforms also gave the participants a stronger sense of copresence and belonging to the learning community. Potential improvements to the usability and the participants' feedback are also discussed in the article. We hope the results can contribute to the fast-growing use of the Metaverse-enabling technologies for educational purposes.", "Keywords": "", "DOI": "10.1109/TLT.2024.3352743", "PubYear": 2024, "Volume": "17", "Issue": "", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hong Kong Polytechnic University, Kowloon, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hong Kong Polytechnic University, Kowloon, Hong Kong"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hong Kong Polytechnic University, Kowloon, Hong Kong"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Dai", "Affiliation": "Hong Kong Polytechnic University, Kowloon, Hong Kong"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Hong Kong Polytechnic University, Kowloon, Hong Kong"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Hong Kong Polytechnic University, Kowloon, Hong Kong"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Hong Kong Polytechnic University, Kowloon, Hong Kong"}], "References": [{"Title": "The effect of using Kahoot! for learning – A literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "103818", "JournalTitle": "Computers & Education"}, {"Title": "Social benefits of living in the metaverse: The relationships among social presence, supportive interaction, social self-efficacy, and feelings of loneliness", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> J.<PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "107498", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Constructing an Edu-Metaverse Ecosystem: A New and Innovative Framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "6", "Page": "685", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "Social Networking Applications: A Comparative Analysis for a Collaborative Learning through Google Classroom and Zoom", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "210", "Issue": "", "Page": "61", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 112095425, "Title": "A Latency-Aware End-Edge Cooperative Insulator Detection Method with High Energy Efficiency and Accuracy", "Abstract": "<p>A prerequisite to ensure the stability of the power supply system is suitable functioning of transmission line equipment. However, the increasing deployment of transmission lines in modern power systems has introduced significant challenges to line inspection. While deep learning-based image detection techniques have shown promise in improving the efficiency and accuracy of insulator detection, they often require substantial computational resources and energy. This limitation hinders the consistent guarantee of accuracy and real-time performance on resource-constrained drones. To address this issue, this paper investigates the co-optimization problem of energy consumption and analytic accuracy in insulator image detection on unmanned aerial vehicles (UAVs). We propose a latency-aware end-edge cooperative insulator detection task offloading scheme with high energy efficiency and accuracy that aims to achieve optimal performance. Initially, we conducted an experimental analysis to examine the influence of input image resolution on the accuracy and latency of the CNN-based insulation detection model. Subsequently, we develop a model that takes into account the latency, analytic accuracy and energy consumption for image detection task offloading. Finally, we formalized a nonlinear integer optimization problem and designed a particle swarm optimization (PSO)-based task offloading scheme to optimize task accuracy and energy consumption while adhering to latency constraints. Extensive experiments validated the effectiveness of the proposed end-edge cooperative insulator detection method in optimizing accuracy and energy consumption.</p>", "Keywords": "", "DOI": "10.1142/S0218126624501901", "PubYear": 2024, "Volume": "33", "Issue": "11", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Grid Fujian Electric Power Co., Ltd., Fuzhou, P. R. China"}], "References": [{"Title": "Energy-aware allocation for delay-sensitive multitask in mobile edge computing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "15", "Page": "16621", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 112095507, "Title": "<PERSON><PERSON><PERSON><PERSON>", "Abstract": "<p>Smartphones and smartwatches have contributed significantly to fitness monitoring by providing real-time statistics, thanks to accurate tracking of physiological indices such as heart rate. However, the estimation of calories burned during exercise is inaccurate and cannot be used for medical diagnosis. In this work, we present JoulesEye, a smartphone thermal camera-based system that can accurately estimate calorie burn by monitoring respiration rate. We evaluated JoulesEye on 54 participants who performed high intensity cycling and running. The mean absolute percentage error (MAPE) of JoulesEye was 5.8%, which is significantly better than the MAPE of 37.6% observed with commercial smartwatch-based methods that only use heart rate. Finally, we show that an ultra-low-resolution thermal camera that is small enough to fit inside a watch or other wearables is sufficient for accurate calorie burn estimation. These results suggest that JoulesEye is a promising new method for accurate and reliable calorie burn estimation.</p>", "Keywords": "", "DOI": "10.1145/3631422", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IIT Gandhinagar, Gandhinagar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Cornell University, NY, United States"}, {"AuthorId": 3, "Name": "Nipun Batra", "Affiliation": "IIT Gandhinagar, Gandhinagar, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, PA, United States"}], "References": [{"Title": "A Survey on Energy Expenditure Estimation Using Wearable Devices", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "BreathTrack", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Deep learning for time series forecasting: The electric load case", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": 112095530, "Title": "CatBoost-based Intrusion Detection Method for the Physical Layer of Smart Agriculture", "Abstract": "Agriculture holds a pivotal role in the progress of human society. The challenges stemming from a burgeoning population, land degradation, water scarcity, and urbanization have intensified the need for more efficient agricultural production. While smart farming brings significant benefits to farmers and agricultural output, it also introduces complex cybersecurity risks to agricultural production. The security of the physical layer in smart agriculture is intricately tied to crop growth and yield, with indirect implications for the security of the network and application layers. This paper introduces a novel intrusion detection scheme based on CatBoost for the physical layer and evaluates its effectiveness using the publicly available ToN_IOT dataset. In binary classification results, the scheme achieves a remarkable recognition accuracy of 99.94%, along with a precision and recall of 99.88%. In multi-classification results, the scheme outperforms other existing solutions across all metrics. The experimental findings clearly illustrate the exceptional recognition accuracy of this implemented method against physical layer attacks within the domain of smart agriculture. Furthermore, the system’s implementation ensures the security of input data for the smart agriculture network layer, cloud, and blockchain applications.", "Keywords": "", "DOI": "10.1051/itmconf/20246000009", "PubYear": 2024, "Volume": "60", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Agricultural Blockchain Application, Ministry of Agriculture and Rural Affairs, Beijing, China;Inspur Academy of Science and Technology, Jinan, Shandong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Inspur Software Co. Ltd, Jinan, Shandong, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Inspur Academy of Science and Technology, Jinan, Shandong, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Inspur Academy of Science and Technology, Jinan, Shandong, China"}, {"AuthorId": 5, "Name": "Zixiang Bi", "Affiliation": "Inspur Academy of Science and Technology, Jinan, Shandong, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Inspur Academy of Science and Technology, Jinan, Shandong, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Inspur Academy of Science and Technology, Jinan, Shandong, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Inspur Academy of Science and Technology, Jinan, Shandong, China"}], "References": [{"Title": "GARUDA: Gaussian dissimilarity measure for feature representation and anomaly detection in Internet of things", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "6", "Page": "4376", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Deep recurrent neural network for IoT intrusion detection system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "102031", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Security challenges to smart agriculture: Current state, key issues, and future directions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "", "Page": "100048", "JournalTitle": "Array"}]}, {"ArticleId": 112095542, "Title": "Intensity variability in stationary solutions of the Fractional Nonlinear Schrödinger Equation", "Abstract": "Solitons that propagate in optical fiber with indexes of refraction, dispersion, and diffraction are balanced, making pulses or electromagnetic waves propagate without any distortion. This is closely related to use of nonlinear refractive index in fiber optics. If an optical fiber only uses a nonlinear refractive index, then the partial signal can be lost over time. This study aims to analyze the variability of stationary solutions in multi-solitons formed using Fractional Nonlinear Schrödinger (FNLS). The parameter p indicates energy level of the solution to FNLS equation which has a positive integer value. This study focuses on 3 variations of p values, namely p = 0 which indicates the ground state, p = 1 which indicates the first excited state, and p = 2 which indicates the second excited state. During the first to second excited state, multi soliton peaks are formed with the same amplitude symmetrically. The amplitude experienced by the middle soliton in second excited state is lower which indicates the input signal obtained from the FNLS solution in the ground state in the form of triple-soliton. The polarization mode cause the soliton pulse width to shrink and the consequent amplitude in the first excited state to increase.", "Keywords": "", "DOI": "10.1051/itmconf/20245802002", "PubYear": 2024, "Volume": "58", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Universitas Syiah Kuala, Banda Aceh, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Universitas Syiah Kuala, Banda Aceh, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Universitas Syiah Kuala, Banda Aceh, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematics Graduate Program, Universitas Syiah Kuala, Banda Aceh, Indonesia"}], "References": []}, {"ArticleId": 112095545, "Title": "LoCal", "Abstract": "<p>Millimeter wave (mmWave) radar excels in accurately estimating the distance, speed, and angle of the signal reflectors relative to the radar. However, for diverse sensing applications reliant on radar's tracking capability, these estimates must be transformed from radar to room coordinates. This transformation hinges on the mmWave radar's location attribute, encompassing its position and orientation in room coordinates. Traditional outdoor calibration solutions for autonomous driving utilize corner reflectors as static reference points to derive the location attribute. When deployed in the indoor environment, it is challenging, even for the mmWave radar with GHz bandwidth and a large antenna array, to separate the static reference points from other multipath reflectors. To tackle the static multipath, we propose to deploy a moving reference point (a moving robot) to fully harness the velocity resolution of mmWave radar. Specifically, we select a SLAM-capable robot to accurately obtain its locations under room coordinates during motion, without requiring human intervention. Accurately pairing the locations of the robot under two coordinate systems requires tight synchronization between the mmWave radar and the robot. We therefore propose a novel trajectory correspondence based calibration algorithm that takes the estimated trajectories of two systems as input, decoupling the operations of two systems to the maximum. Extensive experimental results demonstrate that the proposed calibration solution exhibits very high accuracy (1.74 cm and 0.43° accuracy for location and orientation respectively) and could ensure outstanding performance in three representative applications: fall detection, point cloud fusion, and long-distance human tracking.</p>", "Keywords": "", "DOI": "10.1145/3631436", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Ministry of Education), school of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Ministry of Education), school of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University at Buffalo, Buffalo, New York, United States"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Software, Chinese Academy of Sciences and University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Ministry of Education), school of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Ministry of Education), school of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Ministry of Education), school of Computer Science, Peking University, Beijing, China, Telecom SudParis and Institut Polytechnique de Paris, Paris, France"}], "References": [{"Title": "Real-time Arm Gesture Recognition in Smart Home Scenarios via Millimeter Wave Sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "TouchID", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Exploring Multiple Antennas for Long-range WiFi Sensing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Thru-the-wall Eavesdropping on Loudspeakers via RFID by Capturing Sub-mm Level Vibration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "CornerRadar", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "IndexPen", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Sensor-free Soil Moisture Sensing Using LoRa Signals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Contactless Monitoring of PPG Using Radar", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "<PERSON><PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Sanjib Sur", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "RF-Chain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "MilliPCD", "Authors": "<PERSON><PERSON> Cai; Sanjib Sur", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "SleepMore", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "MI-Mesh: 3D Human Mesh Construction by Fusing Image and Millimeter Wave", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Midas: Generating mmWave Radar Data from Videos for Training Pervasive and Privacy-preserving Human Sensing Tasks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "LT-Fall: The Design and Implementation of a Life-threatening Fall Detection and Alarming System", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Hierarchical Wi-Fi Trajectory Embedding for Indoor User Mobility Pattern Analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Headar: Sensing Head Gestures for Confirmation Dialogs on Smartwatches with Wearable Millimeter-Wave Radar", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "LiqDetector", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "PyroSense", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 112095578, "Title": "Development of English for Mathematics E-Module based on Flipbook Maker", "Abstract": "This research discusses the development of an English for mathematics e-module based on flipbook maker at Palopo State Islamic Institute. The purpose of this research is to produce an English for mathematics e-module product based on flipbook maker on basic mathematics material. This type of research is research and development (R&D) and applying 4D models (Define, Design, Develop, and Dessiminate). The results show that the final prototype of this development is an English for Mathematics e-module based on a flipbook maker in the form of an android application containing 14 materials accompanied by evaluation questions that can be used in the learning process at the Palopo State Islamic Institute. The feasibility of developing this E-Module is reviewed from the results of expert validation with a percentage of 87% of the category being very valid while in terms of practicality with a percentage of 89% of the category being very practical.", "Keywords": "", "DOI": "10.1051/itmconf/20245802001", "PubYear": 2024, "Volume": "58", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mathematics Education Program, Institut Agama Islam Negeri Palopo, South Sulawesi, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "English Language Education Program, Instutut Agama Islam Negeri Palopo, South Sulawesi Indonesia"}], "References": []}, {"ArticleId": 112095595, "Title": "EarSE", "Abstract": "<p>Speech enhancement is regarded as the key to the quality of digital communication and is gaining increasing attention in the research field of audio processing. In this paper, we present EarSE, the first robust, hands-free, multi-modal speech enhancement solution using commercial off-the-shelf headphones. The key idea of EarSE is a novel hardware setting---leveraging the form factor of headphones equipped with a boom microphone to establish a stable acoustic sensing field across the user's face. Furthermore, we designed a sensing methodology based on Frequency-Modulated Continuous-Wave, which is an ultrasonic modality sensitive to capture subtle facial articulatory gestures of users when speaking. Moreover, we design a fully attention-based deep neural network to self-adaptively solve the user diversity problem by introducing the Vision Transformer network. We enhance the collaboration between the speech and ultrasonic modalities using a multi-head attention mechanism and a Factorized Bilinear Pooling gate. Extensive experiments demonstrate that EarSE achieves remarkable performance as increasing SiSDR by 14.61 dB and reducing the word error rate of user speech recognition by 22.45--66.41% in real-world application. EarSE not only outperforms seven baselines by 38.0% in SiSNR, 12.4% in STOI, and 20.5% in PESQ on average but also maintains practicality.</p>", "Keywords": "", "DOI": "10.1145/3631447", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "City University of Hong Kong, Kowloon, Hong Kong SAR, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "City University of Hong Kong, Kowloon, Hong Kong SAR, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "City University of Hong Kong, Kowloon, Hong Kong SAR, Hong Kong, China"}, {"AuthorId": 4, "Name": "T<PERSON>xing Li", "Affiliation": "Michigan State University, East Lansing, Michigan, USA"}], "References": [{"Title": "EarDynamic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "T<PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Cyber-Physical Systems"}, {"Title": "FaceSense", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Quantifying the Causal Effect of Individual Mobility on Health Status in Urban Space", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "UltraSpeech", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "MuteIt", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 112095686, "Title": "Color Capability of RGB Laser Displays", "Abstract": "<p>The high color saturation of laser light sources suggests that RGB laser displays should have superior color reproduction, although standardized gamut rings and ISO test images reveal this is not always true, and conventional chromaticity gamut area metrics must be abandoned.</p>", "Keywords": "", "DOI": "10.1002/msid.1455", "PubYear": 2024, "Volume": "40", "Issue": "1", "JournalId": 59974, "JournalTitle": "Information Display", "ISSN": "0362-0972", "EISSN": "2637-496X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "; Corresponding author."}], "References": [{"Title": "Seventeen‐inch laser backlight in‐plane switching liquid crystal display with 8K, 120‐Hz driving, and BT.2020 color gamut", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "1", "Page": "17", "JournalTitle": "Journal of the Society for Information Display"}, {"Title": "Evaluating Display Color Capability", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "5", "Page": "9", "JournalTitle": "Information Display"}, {"Title": "Gamut Rings Color Scope", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "1", "Page": "30", "JournalTitle": "Information Display"}]}, {"ArticleId": 112095723, "Title": "DIPA2", "Abstract": "<p>The world today is increasingly visual. Many of the most popular online social networking services are largely powered by images, making image privacy protection a critical research topic in the fields of ubiquitous computing, usable security, and human-computer interaction (HCI). One topical issue is understanding privacy-threatening content in images that are shared online. This dataset article introduces DIPA2, an open-sourced image dataset that offers object-level annotations with high-level reasoning properties to show perceptions of privacy among different cultures. DIPA2 provides 5,897 annotations describing perceived privacy risks of 3,347 objects in 1,304 images. The annotations contain the type of the object and four additional privacy metrics: 1) information type indicating what kind of information may leak if the image containing the object is shared, 2) a 7-point Likert item estimating the perceived severity of privacy leakages, and 3) intended recipient scopes when annotators assume they are either image owners or allowing others to repost the image. Our dataset contains unique data from two cultures: We recruited annotators from both Japan and the U.K. to demonstrate the impact of culture on object-level privacy perceptions. In this paper, we first illustrate how we designed and performed the construction of DIPA2, along with data analysis of the collected annotations. Second, we provide two machine-learning baselines to demonstrate how DIPA2 challenges the current image privacy recognition task. DIPA2 facilitates various types of research on image privacy, including machine learning methods inferring privacy threats in complex scenarios, quantitative analysis of cultural influences on privacy preferences, understanding of image sharing behaviors, and promotion of cyber hygiene for general user populations.</p>", "Keywords": "", "DOI": "10.1145/3631439", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Interactive Intelligent Systems Lab., The University of Tokyo, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Interactive Intelligent Systems Lab., The University of Tokyo, Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Interactive Intelligent Systems Lab., The University of Tokyo, Tokyo, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Interactive Intelligent Systems Lab., The University of Tokyo, Tokyo, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Ubiquitous Computing, University of Oulu, Oulu, Finland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Interactive Intelligent Systems Lab., The University of Tokyo, Tokyo, Japan"}], "References": [{"Title": "The Open Images Dataset V4", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "7", "Page": "1956", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Privacy Norms and Preferences for Photos Posted Online", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "<PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Examining Co-Owners’ Privacy Consideration in Collaborative Photo Sharing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "1", "Page": "79", "JournalTitle": "Computer Supported Cooperative Work (CSCW)"}, {"Title": "Shared Privacy Concerns of the Visually Impaired and Sighted Bystanders with Camera-Based Assistive Technologies", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Accessible Computing"}, {"Title": "Privacy Concerns for Visual Assistance Technologies", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Accessible Computing"}, {"Title": "Privacy-Enhancing Technology and Everyday Augmented Reality", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 112095729, "Title": "Exploring the potential of federated learning in mental health research: a systematic literature review", "Abstract": "The rapid advancement of technology has created new opportunities to improve the accuracy and efficiency of medical diagnoses, treatments, and overall patient care in several medical domains, including mental health. One promising novel approach is federated learning, a machine learning approach that allows multiple devices to train a shared model without exchanging raw data. Instead of centralizing the data in one location, each device or machine holds a portion of the data and collaborates with other devices to update the shared model. In this way, federated learning enables training on more extensive and diverse datasets than would be possible with centralized training while preserving the privacy and security of individual data. In the mental health domain, federated learning has the potential to improve mental disorders’ detection, diagnosis, and treatment. By pooling data from multiple sources while maintaining patient privacy by keeping data secure and ensuring that they are not used for unauthorized purposes. This literature survey reviews recent studies that have exploited federated learning in the psychiatric domain, covering multiple data resources and different machine-learning techniques. Furthermore, we formulate the gap in the current methodologies and propose new research directions.", "Keywords": "Federated learning; Mental illness; Psychiatry; Machine learning", "DOI": "10.1007/s10489-023-05095-1", "PubYear": 2024, "Volume": "54", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Arab Academy for Science, Technology and Maritime Transport, 1029 Alexandria, Egypt; Leiden Institute of Advanced Computer Science, Leiden University, Leiden, The Netherlands; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Computer Engineering Department, Arab Academy for Science, Technology and Maritime Transport, 1029 Alexandria, Egypt"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Leiden Institute of Advanced Computer Science, Leiden University, Leiden, The Netherlands; Public Health & Primary Care, Leiden University Medical Center, Leiden, The Netherlands"}], "References": [{"Title": "An open source machine learning framework for efficient and transparent systematic reviews", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "2", "Page": "125", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Federated Learning in a Medical Context: A Systematic Literature Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "Federated learning for violence incident prediction in a simulated cross-institutional psychiatric setting", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "116720", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Federated Learning for Healthcare: Systematic Review and Architecture Proposal", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Intelligent depression detection with asynchronous federated optimization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "1", "Page": "115", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": *********, "Title": "Multi-task metaphor detection based on linguistic theory", "Abstract": "<p>Metaphorical expressions are widely present in natural language, posing significant challenges to a variety of natural language processing tasks such as machine translation. How to obtain richer contextual representations is an urgent problem to be solved. To address this issue, this paper proposes a model that combines syntax-aware local attention (SLA), a simple contrastive sentence embedding framework (SimCSE), and linguistic theories, called a combination of syntax-aware and semantic methods (CSS). Specifically, we apply linguistic theory in metaphor detection. Additionally, we simultaneously conduct metaphor identification and contrastive learning tasks. The SimCSE contrastive learning framework effectively captured more information, and the concurrent execution of these two tasks helped increase the sensitivity of the semantic space to metaphors. The integration of SLA with the pre-trained language model BERT enhanced the attention weights between grammatically relevant words, assisting the encoder in focusing more on grammar-related words. Overall, CSS prioritizes the sentence itself, avoiding the introduction of excessive additional information. Experimental results on the VU Amsterdam-verb (VUA), TroFi, and MOH-X metaphorical corpora show that our method is superior to state-of-the-art models.</p>", "Keywords": "Metaphor detection; Syntax-aware local attention; Contrastive learning; Linguistic theory; BERT", "DOI": "10.1007/s11042-023-18063-1", "PubYear": 2024, "Volume": "83", "Issue": "24", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China; Key Laboratory of Software Engineering Technology, Xinjiang University, Urumqi, China"}, {"AuthorId": 2, "Name": "<PERSON>g<PERSON> T<PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China; Key Laboratory of Software Engineering Technology, Xinjiang University, Urumqi, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Network Center, Xinjiang University, Urumqi, China; Signal and Signal Processing Laboratory, College of Information Science and Engineering, Xinjiang University, Urumqi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China; Key Laboratory of Software Engineering Technology, Xinjiang University, Urumqi, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China; Key Laboratory of Software Engineering Technology, Xinjiang University, Urumqi, China"}], "References": []}, {"ArticleId": 112095844, "Title": "Gamut Rings Color Scope", "Abstract": "<p>The real‐time gamut rings color scope simultaneously visualizes distribution of lightness, chroma, and hue attributes in a single 2D diagram.</p>", "Keywords": "", "DOI": "10.1002/msid.1456", "PubYear": 2024, "Volume": "40", "Issue": "1", "JournalId": 59974, "JournalTitle": "Information Display", "ISSN": "0362-0972", "EISSN": "2637-496X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "; Corresponding author."}], "References": [{"Title": "Analysis of color volume of multi‐chromatic displays using gamut rings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "3", "Page": "273", "JournalTitle": "Journal of the Society for Information Display"}, {"Title": "New Color Metrology Content in IDMS v1.1", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "6", "Page": "33", "JournalTitle": "Information Display"}]}, {"ArticleId": 112095853, "Title": "A method for the competitiveness estimation of the incremental new product through user-generated content", "Abstract": "The current dynamic market environment challenges successful incremental new product (INP) launches, compelling enterprise managers to promptly recognize and respond to competitive situations. Estimating INP competitiveness before sale helps enterprise managers adjust their strategies effectively and in a timely manner to ensure successful INP launches. However, a lack of historical evaluation information challenges the estimation. Given that INPs are updates of existing products, massive user-generated content (UGC) regarding existing products and the new product launch provides an appropriate data source for estimating INPs&#x27; competitiveness. Therefore, this study proposes a method for estimating the competitiveness of INP using UGC. First, the INP product attributes are classified as non-updated or updated. For the former, existing products containing the same attribute values are used as references, and their reference values are measured based on the time and price of the first sale. UGC regarding the launch of INPs serves as a data source for the latter. Sentiment analysis is performed on the UGC concerning product attributes to obtain the sentiment tendency and sentiment intensity for constructing an intuitive fuzzy number, which represents INP&#x27;s attribute performance. INP&#x27;s product performance is estimated by considering attribute weights determined by customer attention. INP&#x27;s competitiveness in the market environment is then estimated by comparing its product performance with that of competing products. Finally, the proposed method is applied to a mobile phone, and its effectiveness and applicability are verified.", "Keywords": "", "DOI": "10.1016/j.dss.2024.114175", "PubYear": 2024, "Volume": "179", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Economics Faculty, Liaoning University, Shenyang 110136, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Economics Faculty, Liaoning University, Shenyang 110136, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Basic Teaching and Research, Criminal Investigation Police University of China, Shenyang 110854, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Economics Faculty, Liaoning University, Shenyang 110136, China;Corresponding author"}], "References": [{"Title": "Sentiment strength detection with a context-dependent lexicon-based convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "520", "Issue": "", "Page": "389", "JournalTitle": "Information Sciences"}, {"Title": "Forecasting demand profiles of new products", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "113401", "JournalTitle": "Decision Support Systems"}, {"Title": "Mining product competitiveness by fusing multisource online information", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "143", "Issue": "", "Page": "113477", "JournalTitle": "Decision Support Systems"}, {"Title": "A multi-criteria procedure in new product development using different qualitative scales", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107279", "JournalTitle": "Applied Soft Computing"}, {"Title": "Ranking Tourist Attractions through Online Reviews: A Novel Method with Intuitionistic and Hesitant <PERSON>zzy Information Based on Sentiment Analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "2", "Page": "755", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "A fuzzy aspect‐based approach for recommending hospitals", "Authors": "<PERSON>‐<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "4", "Page": "2885", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A risky large group emergency decision-making method based on topic sentiment analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "195", "Issue": "", "Page": "116527", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A project prioritization approach considering uncertainty, reliability, criteria prioritization, and robustness", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "156", "Issue": "", "Page": "113731", "JournalTitle": "Decision Support Systems"}, {"Title": "Sourcing product innovation intelligence from online reviews", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "113751", "JournalTitle": "Decision Support Systems"}, {"Title": "The interval grey QFD method for new product development: Integrate with LDA topic model to analyze online reviews", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "105213", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Online reviews and high-involvement product sales: Evidence from offline sales in the Chinese automobile industry", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "57", "Issue": "", "Page": "101231", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Why some products compete and others don't: A competitive attribution model from customer perspective", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "169", "Issue": "", "Page": "113956", "JournalTitle": "Decision Support Systems"}, {"Title": "A small sample data-driven method: User needs elicitation from online reviews in new product iteration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "101953", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Identification and evaluation of competitive products based on online user-generated content", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "120168", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Bayesian non-parametric method for decision support: Forecasting online product sales", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "174", "Issue": "", "Page": "114019", "JournalTitle": "Decision Support Systems"}, {"Title": "Idea crowdsourcing platforms for new product development: A study of idea quality and the number of submitted ideas", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "175", "Issue": "", "Page": "114041", "JournalTitle": "Decision Support Systems"}, {"Title": "Which product description phrases affect sales forecasting? An explainable AI framework by integrating WaveNet neural network models with multiple regression", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "176", "Issue": "", "Page": "114065", "JournalTitle": "Decision Support Systems"}]}, {"ArticleId": 112095887, "Title": "Shifting your research from X to Mastodon? Here’s what you need to know", "Abstract": "Since Elon Musk’s purchase of Twitter/X and subsequent changes to that platform, computational social science researchers may be considering shifting their research programs to Mastodon and the fediverse. This article sounds several notes of caution about such a shift. We explain key differences between the fediverse and X, ultimately arguing that research must be with the fediverse, not on it.", "Keywords": "", "DOI": "10.1016/j.patter.2023.100914", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 74143, "JournalTitle": "Patterns", "ISSN": "2666-3899", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Abbing", "Affiliation": "Malmö University, Malmö, Sweden"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "York University, Toronto, ON, Canada;Corresponding author"}], "References": []}, {"ArticleId": 112095897, "Title": "Multi-strategy synthetized equilibrium optimizer and application", "Abstract": "Background <p>Improvement on the updating equation of an algorithm is among the most improving techniques. Due to the lack of search ability, high computational complexity and poor operability of equilibrium optimizer (EO) in solving complex optimization problems, an improved EO is proposed in this article, namely the multi-strategy on updating synthetized EO (MS-EO).</p> Method <p>Firstly, a simplified updating strategy is adopted in EO to improve operability and reduce computational complexity. Secondly, an information sharing strategy updates the concentrations in the early iterative stage using a dynamic tuning strategy in the simplified EO to form a simplified sharing EO (SS-EO) and enhance the exploration ability. Thirdly, a migration strategy and a golden section strategy are used for a golden particle updating to construct a Golden SS-EO (GS-EO) and improve the search ability. Finally, an elite learning strategy is implemented for the worst particle updating in the late stage to form MS-EO and strengthen the exploitation ability. The strategies are embedded into EO to balance between exploration and exploitation by giving full play to their respective advantages.</p> Result and Finding <p>Experimental results on the complex functions from CEC2013 and CEC2017 test sets demonstrate that MS-EO outperforms EO and quite a few state-of-the-art algorithms in search ability, running speed and operability. The experimental results of feature selection on several datasets show that MS-EO also provides more advantages.</p>", "Keywords": "Equilibrium optimizer;Exploitation;Exploration;Feature selection;Meta-heuristic algorithm;Multi-strategy", "DOI": "10.7717/peerj-cs.1760", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "Quandang Sun", "Affiliation": "Engineering Lab of Intelligence Business & Internet of Things, Xinxiang, Henan, China;Henan Normal University, Software College of Software, Henan Normal University, Xinxiang, Henan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Henan Normal University, College of Computer and Information Engineering, Xinxiang, Henan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sanquan College of Xinxiang Medical University, Xinxiang, Henan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Lab of Intelligence Business & Internet of Things, Xinxiang, Henan, China;Henan Normal University, College of Computer and Information Engineering, Xinxiang, Henan, China"}, {"AuthorId": 5, "Name": "Yuanyuan Ma", "Affiliation": "Henan Normal University, College of Computer and Information Engineering, Xinxiang, Henan, China"}], "References": [{"Title": "Equilibrium optimizer: A novel optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105190", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Yin-Yang firefly algorithm based on dimensionally Cauchy mutation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Kwok-<PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113216", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An evolutionary decomposition-based multi-objective feature selection for multi-label classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "A novel interdependence based multilevel thresholding technique using adaptive equilibrium optimizer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "103836", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An efficient equilibrium optimizer with mutation strategy for numerical optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106542", "JournalTitle": "Applied Soft Computing"}, {"Title": "Fusion with distance-aware selection strategy for dandelion algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "205", "Issue": "", "Page": "106282", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A problem-specific non-dominated sorting genetic algorithm for supervised feature selection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "841", "JournalTitle": "Information Sciences"}, {"Title": "Dynamic Collaborative Fireworks Algorithm and its applications in robust pole assignment optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106999", "JournalTitle": "Applied Soft Computing"}, {"Title": "Orthogonal learning covariance matrix for defects of grey wolf optimizer: Insights, balance, diversity, and feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "213", "Issue": "", "Page": "106684", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "X-architecture Steiner minimal tree algorithm based on multi-strategy optimization discrete differential evolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Information guiding and sharing enhanced simultaneous heat transfer search and its application to k-means optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107476", "JournalTitle": "Applied Soft Computing"}, {"Title": "An efficient Equilibrium Optimizer for parameters identification of photovoltaic modules", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "e708", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Enhanced meta-heuristic optimization of resource efficiency in multi-relay underground wireless sensor networks", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "e1357", "JournalTitle": "PeerJ Computer Science"}, {"Title": "A hybridizing-enhanced differential evolution for optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "e1420", "JournalTitle": "PeerJ Computer Science"}, {"Title": "A new metaphor-less simple algorithm based on Rao algorithms: a Fully Informed Search Algorithm (FISA)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "e1431", "JournalTitle": "PeerJ Computer Science"}, {"Title": "A novel chaotic transient search optimization algorithm for global optimization, real-world engineering problems and feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "e1526", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 112095898, "Title": "A preliminary study on linear perturbation for a non-minimal derivative coupling scalar-tensor theory", "Abstract": "The Cisterna-Delsate-Rinaldi (CDR) model is a variant of scalar-tensor theory that modify gravity by including a term of non-minimal derivative coupling. This model gives interesting aspects in the properties of compact objects, specifically neutron stars. By adjusting one of its parameters, the maximum possible mass of neutron stars can be increased. The authors of the model had also did a perturbation analysis using odd-parity perturbation and following that they also did analysis on the slowly-rotating neutron stars. In this paper, we report our ongoing research on the linear perturbation for the Cisterna model to see its dynamical properties. More precisely, we work on the polar perturbation that affected both the metric and the scalar field, which is different from the axial perturbation used in the slow rotation case. We use higher-dimensional spacetimes to see if the obtained equations will be dimensionally dependent. To simplify calculations for this metric form, we use tetrad method. Currently, we have not succeeded in obtaining the equations of motions in the form of Reg<PERSON>-<PERSON> wave equation. The reason is the metric functions cannot be easily decoupled and we find no second derivatives with respect to both time <i>t<i/> and radius <i>r<i/> in the equations of motion. Only the scalar field can give a wave equation. Further investigation is undergoing.", "Keywords": "Linear perturbation;non-minimal derivative coupling", "DOI": "10.1051/itmconf/20246101015", "PubYear": 2024, "Volume": "61", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of General Education, Faculty of Art and Sciences, Sampoerna University, Jakarta 12780, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Theoretical Physics Laboratory, Theoretical High Energy Physics and Instrumentation Research Group, Faculty of Mathematics and Natural Sciences, Institut Teknologi Bandung, Jl. Ganesha no. 10, Bandung 40132, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Theoretical Physics Laboratory, Theoretical High Energy Physics and Instrumentation Research Group, Faculty of Mathematics and Natural Sciences, Institut Teknologi Bandung, Jl. Ganesha no. 10, Bandung 40132, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Faculty of Mathematics and Natural Sciences (FMIPA), Universitas Indonesia, Depok 16424, Indonesia"}], "References": []}, {"ArticleId": 112095901, "Title": "A Study of Strength Prediction of Multifiber Concrete Based on Improved Stacking", "Abstract": "In order to solve the expected problem of concrete strength in actual engineering, a mixed concrete database was constructed. Machine learning method was used to use multiple single learners and stacking integrated model. The results showed that the effect of stacking integrated model was far better than that of single learner, and the stacking model was improved. Obtain excellent prediction model of mixed concrete. The performance indexes of MAE, RMES and R2 of this model were significantly better than that of any single learner, and were significantly improved compared with the ordinary stacking model. It provides a reference for the prediction model of civil engineering industry in the future.", "Keywords": "", "DOI": "10.23977/autml.2023.040312", "PubYear": 2023, "Volume": "4", "Issue": "3", "JournalId": 75931, "JournalTitle": "Automation and Machine Learning", "ISSN": "2516-5003", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 112095949, "Title": "Stability Analysis of Covid-19 Model Based on Compliance and Carrier Transmission", "Abstract": "The Covid-19 pandemic has officially ended with the lifting of the Public Health Emergency of International Concern (PHEIC) status by the World Health Organization (WHO). The world has begun the transition from a pandemic to an endemic period through policy updates such as healthy living habits, wearing masks if sick, vaccination, self-quarantine, contact tracing or testing, increasing understanding or awareness of diseases and treatment. This research aims to analyze the role of individuals in the pandemic transition period and the addition of the Carrier subpopulation to the COVID-19 model. This model produces two equilibrium points: a disease-free equilibrium points and an endemic equilibrium point. Furthermore, stability analysis was carried out around the equilibrium point and obtained three basic reproduction numbers that became the threshold for the spread of disease around the equilibrium point, namely R0 less than one (R0 < 1) and greater than one (R0 > 1). This shows that increasing policies such as disease awareness or understanding, healthy living habits, and vaccination can prevent the spread of COVID-19 so that the pandemic period does not occur and the disease will disappear over time.", "Keywords": "", "DOI": "10.1051/itmconf/20245801003", "PubYear": 2024, "Volume": "58", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mathematics Graduate Program, Universitas Syiah Kuala, Banda Aceh, Nangroe Aceh Darussalam, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mathematics Graduate Program, Universitas Syiah Kuala, Banda Aceh, Nangroe Aceh Darussalam, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematics Graduate Program, Universitas Syiah Kuala, Banda Aceh, Nangroe Aceh Darussalam, Indonesia;Department of Mathematics, Universitas Syiah Kuala, Banda Aceh, Nangroe Aceh Darussalam, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Universitas Syiah Kuala, Banda Aceh, Nangroe Aceh Darussalam, Indonesia"}], "References": [{"Title": "Asymptomatic and Pre-Symptoms Transmission of COVID-19 in Heterogeneous Epidemic Network", "Authors": "", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "149", "JournalTitle": "Information Sciences Letters"}]}, {"ArticleId": 112095972, "Title": "Optimal Suppression of Oscillations in the Problem of a Spin-Up of a Two-Mass System", "Abstract": "<p>We consider a controlled mechanical system of many bodies, consisting of a load-bearing disk that rotates around its axis fixed in space, and a carried disk attached to it using weightless elastic elements. The presented bodies are in the same plane. The problem of minimizing the amplitude of radial oscillations is studied. To solve this problem over a sufficiently large interval, two numerical methods are used: the method of successive approximations in the control space and <PERSON>’s method. The properties of the phase trajectories of the system are studied depending on the initial states of the disks. Various disk spin-up modes are detected. Using the smoothing procedure for optimal control, a continuous control is constructed that reduces the amplitude of radial oscillations.</p>", "Keywords": "", "DOI": "10.1134/S1064230723060114", "PubYear": 2023, "Volume": "62", "Issue": "6", "JournalId": 14082, "JournalTitle": "Journal of Computer and Systems Sciences International", "ISSN": "1064-2307", "EISSN": "1555-6530", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Ishlinsky Institute for Problems in Mechanics, Russian Academy of Sciences (IPMech RAS), Moscow, Russia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Ishlinsky Institute for Problems in Mechanics, Russian Academy of Sciences (IPMech RAS), Moscow, Russia"}], "References": [{"Title": "On Quick Action in the Problem of Controlling the Vertical\nPosition of a Pendulum by the Movement of its Base", "Authors": "<PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "1", "Page": "39", "JournalTitle": "Journal of Computer and Systems Sciences International"}, {"Title": "Time-Optimal Movement of a <PERSON>rolley with a Pendulum", "Authors": "<PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "1", "Page": "28", "JournalTitle": "Journal of Computer and Systems Sciences International"}, {"Title": "Controlled Motion of a Linear Chain of Oscillators", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "5", "Page": "686", "JournalTitle": "Journal of Computer and Systems Sciences International"}, {"Title": "Time-Optimal Turning of a Spring Pendulum", "Authors": "<PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "62", "Issue": "4", "Page": "652", "JournalTitle": "Journal of Computer and Systems Sciences International"}]}, {"ArticleId": 112096032, "Title": "Assessing the validity of VR as a training tool for medical students", "Abstract": "<p>The advances in Virtual Reality technologies, increased availability and reducing hardware costs have diminished many of the early challenges in the adoption of VR. However, a commonly identified gap in immersive Virtual Reality-Head Mounded Display (VR-HMD) training for medical education is the confidence in the long-term validity of the applications, in particular, the acceleration of the learning curve efficacy of learning outcomes over time and actual skills translation into real environments. Research shows a wide range of ad hoc applications, with superficial evaluations often conducted by technology vendors, based on assumed environments and tasks, envisaged (as opposed to actual) users and effectiveness of learning outcomes underpinned with little or no research focusing on a requirements-driven validation approach. This presents decision-making challenges for those seeking to adopt, implement and embed such systems in teaching practice. The current paper aims to (i) determine whether medical VR training improves the skill acquisition of training candidates, (ii) determine the factors affecting the acquisition of skills and (iii) validate the VR-based training using requirement-driven approach. In this paper, we used within- and between-subject design approaches to assess the validity of VR-based surgical training platform developed by Vantari VR against requirements which have been identified to have impact on learning processes and outcomes in VR-based training. First, study and control groups were compared based on their level of skill acquisitions. Then, by tailoring a requirements framework, the system was validated against the appropriate requirements. In total, 74 out of 109 requirements were investigated and evaluated against survey, observer and stakeholder workshop data. The training scenario covered the topic of Arterial Blood Gas (ABG) collection for second-year university medical students. In total 44 students volunteered to participate in this study, having been randomly assigned to either the study or control group. Students exposed to VR training (the study group) outperformed the control group in practical clinical skills training tasks and also adhered to better safety and hygiene practices. The study group also had a greater procedural completion rate over the control group. Students showed increased self-efficacy and knowledge scores immediately post-VR training. Prior ABG training did not impact on VR training outcomes. Low levels of simulation sickness, physical strain and stress, coupled with high levels of enjoyability, engagement, presence and fidelity were identified as factors affecting the overall training experience. In terms of learning, high scores were recorded for active learning, cognitive benefit and reflective thinking. Lastly, by validating the system against 74 system requirements, the study found a user acceptance level of 75%. This enabled the identification of weaknesses of the current system and possible future directions.</p>", "Keywords": "HMD-VR; Training; Education; Surgical; Immersive; Virtual reality; Validation", "DOI": "10.1007/s10055-023-00912-x", "PubYear": 2024, "Volume": "28", "Issue": "1", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "SMART Infrastructure Facility, Faculty of Engineering and Information Sciences, University of Wollongong, Wollongong, Australia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "SMART Infrastructure Facility, Faculty of Engineering and Information Sciences, University of Wollongong, Wollongong, Australia"}, {"AuthorId": 3, "Name": "Sal Sanzone", "Affiliation": "Faculty of Science, Medicine and Health, School of Medicine, University of Wollongong, Wollongong, Australia"}], "References": [{"Title": "Investigating the process of mine rescuers' safety training with immersive virtual reality: A structural equation modelling approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "103891", "JournalTitle": "Computers & Education"}, {"Title": "User experiences of virtual reality technologies for healthcare in learning: an integrative review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "1", "Page": "1", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "A collaborative virtual reality environment for liver surgery planning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "234", "JournalTitle": "Computers & Graphics"}, {"Title": "A study of how immersion and interactivity drive VR learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "179", "Issue": "", "Page": "104429", "JournalTitle": "Computers & Education"}, {"Title": "Toward the validation of VR-HMDs for medical education: a systematic literature review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "3", "Page": "2255", "JournalTitle": "Virtual Reality"}]}, {"ArticleId": 112096072, "Title": "TouchEditor", "Abstract": "<p>A text editing solution that adapts to speech-unfriendly (inconvenient to speak or difficult to recognize speech) environments is essential for head-mounted displays (HMDs) to work universally. For existing schemes, e.g., touch bar, virtual keyboard and physical keyboard, there are shortcomings such as insufficient speed, uncomfortable experience or restrictions on user location and posture. To mitigate these restrictions, we propose TouchEditor, a novel text editing system for HMDs based on a flexible piezoresistive film sensor, supporting cursor positioning, text selection, text retyping and editing commands (i.e., Copy, Paste, Delete, etc.). Through literature overview and heuristic study, we design a pressure-controlled menu and a shortcut gesture set for entering editing commands, and propose an area-and-pressure-based method for cursor positioning and text selection that skillfully maps gestures in different areas and with different strengths to cursor movements with different directions and granularities. The evaluation results show that TouchEditor i) adapts to various contents and scenes well with a stable correction speed of 0.075 corrections per second; ii) achieves 95.4% gesture recognition accuracy; iii) reaches a considerable level with a mobile phone in text selection tasks. The comparison results with the speech-dependent EYEditor and the built-in touch bar further prove the flexibility and robustness of TouchEditor in speech-unfriendly environments.</p>", "Keywords": "", "DOI": "10.1145/3631454", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xiamen University, Xiamen, China"}, {"AuthorId": 2, "Name": "Tianyang <PERSON>", "Affiliation": "Xiamen University, Xiamen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Xiamen University, Xiamen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Xiamen University, Xiamen, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Xiamen University, Xiamen, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for AI Industry Research (AIR), Tsinghua University, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xiamen University, Xiamen, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cardiff University, Cardiff, United Kingdom"}], "References": [{"Title": "Quantifying the Causal Effect of Individual Mobility on Health Status in Urban Space", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "The Eye in Extended Reality: A Survey on Gaze Interaction and Eye Tracking in Head-worn Extended Reality", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Handwriting Velcro", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Text Pin: Improving text selection with mode-augmented handles on touchscreen mobile devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "175", "Issue": "", "Page": "103028", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Touch-and-Heal: Data-driven Affective Computing in Tactile Interaction with Robotic Dog", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 112096140, "Title": "A Comparison of Spatial Interpolation Methods for Regionalizing Maximum Daily Rainfall Data in South Sulawesi, Indonesia", "Abstract": "The aim of this research is to compare between the Inverse Distance Weighted (IDW) and Ordinary Kriging (OK) interpolation methods for regionalization of areas within the South Sulawesi province based on maximum daily rainfall. The data utilized consists of maximum daily rainfall data from 56 rain stations within the South Sulawesi from 1986 to 2021. The spatial interpolation methods applied include the power 2 IDW, and OK. Various semivariogram models, namely Spherical, Gaussian, and Exponential, are employed within the OK method. The selection of the best method is based on the smallest Root Mean Square Error (RMSE) and Mean Absolute Error (MAE) values. The findings of this research reveal that the optimal method for regionalization of maximum daily rainfall is the OK method with a Gaussian semivariogram model. The RMSE values for this method are 57.45, and the MAE values are 46.49. The results of the spatial interpolation demonstrate that the South Sulawesi is divided into four zones characterized by maximum daily rainfall (in mm) as follows: Zone I: less than 230 mm (Eastern and Southeastern regions), Zone II: 230-260 mm (Northern region), Zone III: 260-280 mm (Western region), and Zone IV: more than 280 mm (Southwestern region).", "Keywords": "", "DOI": "10.1051/itmconf/20245804003", "PubYear": 2024, "Volume": "58", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Universitas Negeri Makassar, Makassar, South Sulawesi, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Universitas Negeri Makassar, Makassar, South Sulawesi, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, Universitas Negeri Makassar, Makassar, South Sulawesi, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, Universitas Negeri Makassar, Makassar, South Sulawesi, Indonesia"}], "References": []}, {"ArticleId": 112096242, "Title": "Effective GA Operator for Product Assembly Sequence Planning", "Abstract": "Assembly operations of products can be determined by assembly sequence planning to meet assembly criteria. Although the genetic algorithm (GA) is one of the common algorithms used to find the optimal sequence of components in the product assembly, the optimal sequence is selected after the elimination of sequences that do not meet constraints. There is a lack of research on the effect of different types of crossover operators on GA performance. This paper introduces applications of different GA operators in the search for the optimal product assembly sequence. Four versions of GA are evaluated for their performances by employing different crossover operators in the algorithm. The roulette wheel is used as the selection mechanism. The solutions are examined by the statistical analysis in three case studies. This investigation obtains the pros and cons of each method to select the most suitable GA crossover operator for solving this specific optimization problem. © 2024, CAD Solutions, LLC. All rights reserved.", "Keywords": "Assembly sequence planning (ASP); Crossover operators; Genetic algorithm (GA)", "DOI": "10.14733/cadaps.2024.713-728", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Manitoba, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "South China University of Technology, China"}, {"AuthorId": 3, "Name": "Qingjin Peng", "Affiliation": "University of Manitoba, Canada"}, {"AuthorId": 4, "Name": "Shiping <PERSON>", "Affiliation": "South China University of Technology, China"}], "References": []}, {"ArticleId": 112096256, "Title": "Reliability-Adaptive Consistency Regularization for Weakly-Supervised Point Cloud Segmentation", "Abstract": "<p>Weakly-supervised point cloud segmentation with extremely limited labels is highly desirable to alleviate the expensive costs of collecting densely annotated 3D points. This paper explores applying the consistency regularization that is commonly used in weakly-supervised learning, for its point cloud counterpart with multiple data-specific augmentations, which has not been well studied. We observe that the straightforward way of applying consistency constraints to weakly-supervised point cloud segmentation has two major limitations: noisy pseudo labels due to the conventional confidence-based selection and insufficient consistency constraints due to discarding unreliable pseudo labels. Therefore, we propose a novel Reliability-Adaptive Consistency Network (RAC-Net) to use both prediction confidence and model uncertainty to measure the reliability of pseudo labels and apply consistency training on all unlabeled points while with different consistency constraints for different points based on the reliability of corresponding pseudo labels. Experimental results on the S3DIS and ScanNet-v2 benchmark datasets show that our model achieves superior performance in weakly-supervised point cloud segmentation. The code will be released publicly at https://github.com/wu-zhonghua/RAC-Net .</p>", "Keywords": "Weakly supervision; Point cloud; Point cloud segmentation; Uncertainty", "DOI": "10.1007/s11263-023-01975-8", "PubYear": 2024, "Volume": "132", "Issue": "6", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Wu", "Affiliation": "School of Computer Science and Engineering, Nanyang Technological University, Singapore, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Wu", "Affiliation": "Department of Data Science and AI, Monash University, Melbourne, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanyang Technological University, Singapore, Singapore; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Data Science and AI, Monash University, Melbourne, Australia"}], "References": [{"Title": "SensatUrban: Learning Semantics from Urban-Scale Photogrammetric Point Clouds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "2", "Page": "316", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Zero-Shot Learning on 3D Point Cloud Objects and Beyond", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "10", "Page": "2364", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "DESC: Domain Adaptation for Depth Estimation via Semantic Consistency", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "3", "Page": "752", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Semi-Supervised and Long-Tailed Object Detection with CascadeMatch", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "4", "Page": "987", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Revisiting Consistency Regularization for Semi-Supervised Learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "3", "Page": "626", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "PointMatch: A consistency training framework for weakly supervised semantic segmentation of 3D point clouds", "Authors": "Yu<PERSON><PERSON>; Zizheng Yan; Shengcai Cai", "PubYear": 2023, "Volume": "116", "Issue": "", "Page": "427", "JournalTitle": "Computers & Graphics"}, {"Title": "LCReg: Long-tailed image classification with Latent Categories based Recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "145", "Issue": "", "Page": "109971", "JournalTitle": "Pattern Recognition"}, {"Title": "Harmonizing Base and Novel Classes: A Class-Contrastive Approach for Generalized Few-Shot Segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "132", "Issue": "4", "Page": "1277", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 112096387, "Title": "Occluded pedestrian re-identification via Res-ViT double-branch hybrid network", "Abstract": "<p>Existing occluded pedestrian re-identification methods mainly utilize convolutional neural networks to realize the feature matching under different camera perspectives. Due to the complex occlusion situation, the accuracy of occluded pedestrian re-identification is not so satisfied where convolutional neural networks are utilized to extract local features. Convolutional neural network is unique in its ability to capture local features, but its global modeling ability is weak. In contrast, Vision Transformer (ViT) can efficiently extract global features from shallow layers with more spatial information and obtain intermediate features with high quality. To deal with the above issues, ViT is here introduced into the residual network to construct a dual-branch hybrid network of residual network and visual converter (DB-ResHViT), where the ViT branch is utilized to reduce training errors, while the residual-ViT branch is utilized to construct the global correlation of feature sequences extracted by the residual network. The proposed network proposes a novel data augmentation module, called partial image patch pre-convolution module (PPPC), which is utilized to input the extracted partial image patches into the pre-convolution network to replace the original image patches to achieve the goal of introducing local features into the ViT branch. In addition, the proposed network designs a novel module integrating residual and mobile vision transformer, called RMV Module, which is utilized to establish the global correlation of local features extracted by the residual network to achieve the goal of reducing the computational cost and improve the re-identification accuracy. Experimental results of a large number of occluded pedestrian re-identification datasets demonstrate that the performance of the proposed method is superior to other advanced methods.</p>", "Keywords": "Occluded pedestrian Re-identification; Double-branch hybrid structure; Image block partial pre-convolution enhancement; Residual-vision transformer module", "DOI": "10.1007/s00530-023-01235-2", "PubYear": 2024, "Volume": "30", "Issue": "1", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Automation and Artificial Intelligence, Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Automation and Artificial Intelligence, Nanjing University of Posts and Telecommunications, Nanjing, China; Corresponding author."}], "References": [{"Title": "PAFM: pose-drive attention fusion mechanism for occluded person re-identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "10", "Page": "8241", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An improved interaction-and-aggregation network for person re-identification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "28", "Page": "44053", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Accurate Fine-Grained Object Recognition with Structure-Driven Relation Graph Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "132", "Issue": "1", "Page": "137", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 112096450, "Title": "Cloud Computing Principles", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2024.13103", "PubYear": 2023, "Volume": "13", "Issue": "1", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 112096464, "Title": "Learning by Asking Questions for Knowledge-Based Novel Object Recognition", "Abstract": "In real-world object recognition, there are numerous object classes to be recognized. Traditional image recognition methods based on supervised learning can only recognize object classes present in the training data, and have limited applicability in the real world. In contrast, humans can recognize novel objects by questioning and acquiring knowledge about them. Inspired by this, we propose a framework for acquiring external knowledge by generating questions that enable the model to instantly recognize novel objects. Our framework comprises three components: the object classifier (OC), which performs knowledge-based object recognition, the question generator (QG), which generates knowledge-aware questions to acquire novel knowledge, and the policy decision (PD) Model, which determines the “policy” of questions to be asked. The PD model utilizes two strategies, namely “confirmation” and “exploration”—the former confirms candidate knowledge while the latter explores completely new knowledge. Our experiments demonstrate that the proposed pipeline effectively acquires knowledge about novel objects compared to several baselines, and realizes novel object recognition utilizing the obtained knowledge. We also performed a real-world evaluation in which humans responded to the generated questions, and the model used the acquired knowledge to retrain the OC, which is a fundamental step toward a real-world human-in-the-loop learning-by-asking framework. We plan to release the dataset immediately upon acceptance of our work.", "Keywords": "Visual question generation; Novel object recognition; Human-in-the-loop learning; Knowledge acquisition", "DOI": "10.1007/s11263-023-01976-7", "PubYear": 2024, "Volume": "132", "Issue": "6", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Tokyo, Tokyo, Japan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Tokyo, Tokyo, Japan; RIKEN, Tokyo, Japan"}], "References": []}, {"ArticleId": 112096500, "Title": "Navigation and mapping of closed spaces with a mobile robot and RFID grid", "Abstract": "This article concerns the use of an integrated RFID system with a mobile robot for the navigation and mapping of closed spaces. The architecture of a prototype mobile robot equipped with a set of RFID readers that performs the mapping functions is described. Laboratory tests of the robot have been carried out using a test stand equipped with a grid of appropriately programmed RFID transponders. A simulation model of the effectiveness of transponder reading by the robot has been prepared. The conclusions from measurements and tests are discussed, and methods for improving the solution are proposed. © 2023. The Author(s).", "Keywords": "mobile robots;space mapping;RFID", "DOI": "10.24425/acs.2023.148879", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 30654, "JournalTitle": "Archives of Control Sciences", "ISSN": "1230-2384", "EISSN": "2300-2611", "Authors": [], "References": []}, {"ArticleId": 112096507, "Title": "Area-Efficient and Wideband Tunable Quadrature Colpitts Oscillator with Active Inductors", "Abstract": "<p>This study presents a new wide-tunable, area-efficient and low-phase noise quadrature voltage-controlled oscillator(QVCO). Initially, a modified Colpitts voltage-controlled oscillator (VCO) is proposed in which all transistors are cross-connected in a self-biased scheme to increase negative resistance and facilitate the startup condition. Besides, instead of a passive inductor, a modified tunable active inducer (AI) is used in the proposed Colpitts VCO. Then a new AI-based QVCO is presented by the in-phase anti-phase coupling of two identical Colpitts VCOs. The core VCOs are coupled by the current source transistors of the AI and in this way, no extra coupling devices are used. The proposed AI-based QVCO is designed and simulated in TSMC [Formula: see text] CMOS technology by Cadence. Post-layout simulation results show that the proposed QVCO occupies only [Formula: see text] of chip area, and has a phase noise of −106.6[Formula: see text]dBc/Hz at 1[Formula: see text]MHz offset from the 0.7[Formula: see text]GHz oscillation frequency. A wide frequency tuning range (FTR) of 68% (i.e., 0.68–1.4[Formula: see text]GHz) is achieved by adjusting the tuning voltage of the active inductor (AI), and the total power consumption is 7.2[Formula: see text]mW. Coarse and fine frequency tuning of the QVCO is available by changing the emulated inductors and varactors, respectively, that gives [Formula: see text] of 1100 and 34[Formula: see text]MHz/V, respectively.</p>", "Keywords": "", "DOI": "10.1142/S0218126624501949", "PubYear": 2024, "Volume": "33", "Issue": "11", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Shahrood University of Technology, Shahrood, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technische Hochschule Nürnberg, Nuremberg, Bavaria, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "IC Design Research Laboratory, Department of Electrical Engineering, Shahrood University of Technology, Shahrood, Iran"}], "References": [{"Title": "An Ultra-Low Power Area Efficient Voltage Controlled Oscillator Based on Tunable Active Inductor for Wireless Applications.", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "85", "Issue": "", "Page": "104291", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 112096516, "Title": "Natural language processing in the era of large language models", "Abstract": "", "Keywords": "Natural Language Processing; large language models (LLM); Language models (LMs); Specialty Grand Challenge; Generative AI", "DOI": "10.3389/frai.2023.1350306", "PubYear": 2024, "Volume": "6", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering and Computer Science, Queen Mary University of London, United Kingdom"}], "References": [{"Title": "Towards generalisable hate speech detection: a review on obstacles and solutions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Pre-train, Prompt, and Predict: A Systematic Survey of Prompting Methods in Natural Language Processing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Biases in Large Language Models: Origins, Inventory, and Discussion", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Data and Information Quality"}, {"Title": "Recent Advances in Natural Language Processing via Large Pre-trained Language Models: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A Survey of Privacy Attacks in Machine Learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Rationalization for explainable NLP: a survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "", "Page": "1225093", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Explainability for Large Language Models: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 112096560, "Title": "Truthful mechanisms to maximize the social welfare in real-time ride-sharing", "Abstract": "<p>Ride-sharing contributes significantly to lowering trip expenses, easing traffic congestion and decreasing air pollution. However, current order pairing approaches in ride-sharing usually focus on minimizing total trip distances or maximizing platform profits, overlooking the drivers’ desire for increased earnings. As a result, drivers might provide dishonest information to gain higher profits, leading to inefficient order pairing for the ride-sharing platform and potential losses for both the platform and drivers. In this paper, we address this challenging issue by developing efficient order pairing mechanisms that maximize the social welfare of the platform and drivers. Specifically, we introduce two truthful auction-based order pairing mechanisms, SWMOM-VCG and SWMOM-GM, where drivers bid on platform-published orders to complete them and earn profits. We provide theoretical proof that both mechanisms fulfill the criteria of individual rationality, profitability, truthfulness and so on. Using real taxi order data from New York City, we assess the performance of both mechanisms and show that they achieve greater social welfare compared to existing methods. Additionally, we find that SWMOM-GM requires less computation time than SWMOM-VCG for order pairing, with only a minor reduction in social welfare.</p>", "Keywords": "", "DOI": "10.3233/WEB-230401", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 20625, "JournalTitle": "Web Intelligence", "ISSN": "2405-6456", "EISSN": "2405-6464", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Wuhan University of Technology, Whuan, 430070, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Wuhan University of Technology, Whuan, 430070, China"}, {"AuthorId": 3, "Name": "Yikai Luo", "Affiliation": "School of Computer Science and Technology, Wuhan University of Technology, Whuan, 430070, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Wuhan University of Technology, Whuan, 430070, China"}], "References": [{"Title": "Fast optimised ridesharing: Objectives, reformulations and driver flexibility", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112914", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 112096592, "Title": "Structural Models for Failure Detection of Moore Finite-State Machines", "Abstract": "<p>Fault detection is an important task in the design of fault-tolerant state machines. Structural models of Moore finite-state machines are proposed to detect multiple faults in various elements of the finite-state machine and prevent their negative impact on the controlled object. The structural models under consideration make it possible to detect invalid input and output vectors both in each state and for the entire automaton, invalid code of the current and next state of the automaton, and invalid transitions between states. The costs of implementing the proposed structures by area range on average from 3 to 26%, and the speed of the machine either does not change or even increases by an average of 24–30%. Estimates of the area and performance of the proposed structural models of finite-state machines are given, and recommendations for their practical use are given. It is shown that the choice of a suitable structure allows us not to increase the area, and in some cases even leads to an increase in the performance of the finite-state machine.</p>", "Keywords": "", "DOI": "10.1134/S1064230723060102", "PubYear": 2023, "Volume": "62", "Issue": "6", "JournalId": 14082, "JournalTitle": "Journal of Computer and Systems Sciences International", "ISSN": "1064-2307", "EISSN": "1555-6530", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>’ev", "Affiliation": "Bialystok University of Technology, Bialystok, Poland; Corresponding author."}], "References": [{"Title": "Synthesis of Fast Finite State Machines on Programmable Logic Integrated Circuits by Splitting Internal States", "Authors": "<PERSON><PERSON> <PERSON><PERSON>’ev", "PubYear": 2022, "Volume": "61", "Issue": "3", "Page": "360", "JournalTitle": "Journal of Computer and Systems Sciences International"}]}, {"ArticleId": 112096600, "Title": "Multimodal Sentiment Analysis Method Based on Hierarchical Adaptive Feature Fusion Network", "Abstract": "<p>The traditional multi-modal sentiment analysis (MSA) method usually considers the multi-modal characteristics to be equally important and ignores the contribution of different modes to the final MSA result. Therefore, an MSA method based on hierarchical adaptive feature fusion network is proposed. Firstly, RoBERTa, ResViT, and LibROSA are used to extract different modal features and construct a layered adaptive multi-modal fusion network. Then, the multi-modal feature extraction module and cross-modal feature interaction module are combined to realize the interactive fusion of information between modes. Finally, an adaptive gating mechanism is introduced to design a global multi-modal feature interaction module to learn the unique features of different modes. The experimental results on three public data sets show that the proposed method can make full use of multi-modal information, outperform other advanced comparison methods, improve the accuracy and robustness of sentiment analysis, and is expected to achieve better results in the field of sentiment analysis.</p>", "Keywords": "", "DOI": "10.4018/IJSWIS.335918", "PubYear": 2024, "Volume": "20", "Issue": "1", "JournalId": 17494, "JournalTitle": "International Journal on Semantic Web and Information Systems", "ISSN": "1552-6283", "EISSN": "1552-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Agricultural Business College, Shaoxing, China"}], "References": [{"Title": "Multiple features based approach for automatic fake news detection on social networks using deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106983", "JournalTitle": "Applied Soft Computing"}, {"Title": "Using E-Reputation for Sentiment Analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "2", "Page": "32", "JournalTitle": "International Journal of Cloud Applications and Computing"}, {"Title": "A review on the attention mechanism of deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "48", "JournalTitle": "Neurocomputing"}, {"Title": "Classification of Code-Mixed Bilingual Phonetic Text Using Sentiment Analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "2", "Page": "59", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "Multimodal sentiment analysis with unidirectional modality translation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "467", "Issue": "", "Page": "130", "JournalTitle": "Neurocomputing"}, {"Title": "Image sentiment classification via multi-level sentiment region correlation analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "469", "Issue": "", "Page": "221", "JournalTitle": "Neurocomputing"}, {"Title": "Image-text interaction graph neural network for image-text sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "10", "Page": "11184", "JournalTitle": "Applied Intelligence"}, {"Title": "Flesch-<PERSON> as Proxy of Socio-Economic Status on Twitter", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "Phishing Website Detection With Semantic Features Based on Machine Learning Classifiers", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "Attention mechanisms in computer vision: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "331", "JournalTitle": "Computational Visual Media"}, {"Title": "Sentiment Analysis of COVID-19 Tweets Using Adaptive Neuro-Fuzzy Inference System Models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Software Science and Computational Intelligence"}, {"Title": "Building a three-level multimodal emotion recognition framework", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "1", "Page": "239", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Deep Emotional Arousal Network for Multimodal Sentiment Analysis and Emotion Recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "88", "Issue": "", "Page": "296", "JournalTitle": "Information Fusion"}, {"Title": "A Multi-Level Circulant Cross-Modal Transformer for Multimodal Speech Emotion Recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "74", "Issue": "2", "Page": "4203", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 112096635, "Title": "Simulation-based variable neighborhood search for optimizing skill assignments in multi-server facilities with inventories", "Abstract": "This paper addresses the joint optimization problem of skill assignments and inventory in a multi-skill, multi-server repair facility. Failures of different part types occur according to Poisson processes, and each part type requires a certain repair skill. The repair facility supplies ready-to-install spare parts when available in the inventory, according to the ( S − 1 , S ) inventory policy. The repair times follow exponential distributions, with rates dependent on the part type. After repair, the parts are returned to the inventory as ready-to-install spare parts. If the inventory is empty when a failed part arrives, the replacement part is backordered, and a penalty cost is incurred. The objective of the problem is to find an assignment of repair skills to servers and inventory levels that minimize the expected total cost of the system. That is, the costs for servers, the costs to upgrade the skills of servers, and the expected holding and backorder costs. We propose to solve this problem by a simulation-based Variable Neighborhood Search (VNS) approach, in which a Discrete Event Simulation is applied to evaluate the expected backorder and holding costs given the skill assignments. The proposed method is capable of significantly improving the results of a recently published Genetic Algorithm, achieving an average cost reduction of 5.1% in the same running time. Moreover, it is able to find comparable solutions in one fifth of the GA running time.", "Keywords": "Variable neighborhood search ; Simulation-based optimization ; Maintenance facility ; Skill assignments ; Multi-server queues ; Inventories", "DOI": "10.1016/j.cor.2024.106546", "PubYear": 2024, "Volume": "164", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Management Science and Engineering, Khalifa University, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 2, "Name": "Adriana F. Gabor", "Affiliation": "Department of Mathematics, Khalifa University, Abu Dhabi, United Arab Emirates;Research Center on Digital Supply Chain and Operations Management, Khalifa University, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management Science and Engineering, Khalifa University, Abu Dhabi, United Arab Emirates;Research Center on Digital Supply Chain and Operations Management, Khalifa University, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Management Science and Engineering, Khalifa University, Abu Dhabi, United Arab Emirates;Research Center on Digital Supply Chain and Operations Management, Khalifa University, Abu Dhabi, United Arab Emirates;Corresponding author at: Department of Management Science and Engineering, Khalifa University, Abu Dhabi, United Arab Emirates"}], "References": [{"Title": "A sorting based efficient heuristic for pooled repair shop designs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "117", "Issue": "", "Page": "104887", "JournalTitle": "Computers & Operations Research"}, {"Title": "Variable Neighborhood Search: The power of change and simplicity", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "155", "Issue": "", "Page": "106221", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 112096636, "Title": "Complete Issue", "Abstract": "", "Keywords": "", "DOI": "10.1002/msid.1462", "PubYear": 2024, "Volume": "40", "Issue": "1", "JournalId": 59974, "JournalTitle": "Information Display", "ISSN": "0362-0972", "EISSN": "2637-496X", "Authors": [], "References": []}, {"ArticleId": 112096656, "Title": "Semi-meta-supervised hate speech detection", "Abstract": "On social media, hate speech is a daily occurrence but has physical and psychological implications. Utilizing a deep learning strategy to combat hate speech is one method for preventing it. Deep learning techniques may require massive datasets to generate accurate models, but hate speech samples (such as misogyny and cyber samples) are frequently insufficient and diverse. We offer methods for leveraging these diverse datasets and enhancing deep learning models through knowledge sharing. We analyzed the existing Bidirectional Encoder Representations from Transformers (BERT) technique and built a BERT-3CNN method to generate a single-task classifier that optimally absorbs the target dataset&#x27;s features. Second, we proposed a shared BERT layer to gain a general understanding of hate speech. Third, we proposed a method for adapting another dataset to the desired dataset. We conducted several quantitative experimental investigations on five datasets, including Hatebase, Supremacist, Cybertroll, TRAC, and TRAC 2020, and assessed the achieved performance using the accuracy and F1 metrics. The first experiment demonstrated that our BERT-3CNN model improved the average accuracy by 5% and the F1 score by 18%. The second experiment demonstrated that BERT-SP improved the average accuracy by 0.2% and the F1 score by 2%. TRAC, Supremacist, Hatebase, and Cybertroll all showed improvements in accuracy, with Semi BERT-SP enhancing accuracy by 6% and F1 score by 5%, while TRAC2020 showed 10% and 9% improvements.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.111386", "PubYear": 2024, "Volume": "287", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information Management, National Cheng Kung University, Tainan 701, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Management, National Cheng Kung University, Tainan 701, Taiwan;Center for Innovative FinTech Business Models, National Cheng Kung University, Tainan 701, Taiwan;Corresponding author"}], "References": [{"Title": "Detecting and visualizing hate speech in social media: A cyber Watchdog for surveillance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113725", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Aggressive and Offensive Language Identification in Hindi, Bangla, and English: A Comparative Study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "1", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "A probabilistic clustering model for hate speech classification in twitter", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Friday <PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "114762", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Detecting abusive Instagram comments in Turkish using convolutional Neural network and machine learning methods", "Authors": "Habibe Karayiğit; Çiğdem İnan Acı; Ali Akdağlı", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114802", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": *********, "Title": "Classifying Risks On Motor Insurance Policies For IFRS 17 Implementation In General Insurance Companies", "Abstract": "IFRS 17 is a financial accounting standard issued by the International Financial Reporting System that regulates internationally agreed accounting treatment for insurance contracts. In an effort to increase the accuracy of risk assessment for IFRS 17 adaptation, a good way is needed to classify risks from the insured. Therefore, it is necessary to determine the risk group. Because data from an insurance company is large, the CLARA method is suitable for dealing with the problem. CLARA has a more robust nature of outliers and can be used to handle large amounts of data. After grouping, it is important to know what factors cause a person to enter a certain group. For this, classification analysis is needed. Some classification analysis methods are XGBoost, SVM, and AdaBoost. Extreme Gradient Boosting and Adaptive Boosting is a technique in machine learning for binary or multiclass regression and classification problems that results in predictive models in the form of weak predictive models. Support Vector Machine (SVM) is a technique for making predictions, both in the case of regression and binary or multiclass classification. SVM has the basic principle of linear classifier.", "Keywords": "", "DOI": "10.1051/itmconf/20245804006", "PubYear": 2024, "Volume": "58", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "Andiansyah Prima Wardana", "Affiliation": "Actuarial Unit, PT Asuransi Jasaraharja Putera, Jakarta, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Departement of Mathematics, Universitas Gadjah Mada, Yogyakarta, DI Yogyakarta, Indonesia"}], "References": []}, {"ArticleId": 112096723, "Title": "Intelligent communication of two humanoid robots based on computer vison", "Abstract": "<p>The focus of this paper is on investigating the use of visual communication between two humanoid robots, in order to enhance the coordination of tasks between them. The problem continues to be an interesting and fruitful area of research from the days of using multiple robot manipulator arms for manufacturing as well as space robotics to current research in medical robotics. The approach here is to employ several off-the-shelf algorithms, software and hardware such as the NAO robot and support software, including Choregraphe, OpenCV to capture and process images, the SVM to classify objects in images, and the Python programming environment. Five robotic actions were studied and three modes. The experiments used one robot as the “viewer” and the second robot as the “subject” being analyzed. Results show that the visual communication system has an accuracy of 90% in correctly identifying the five movements. This research has shown an original solution, as a model that can enable robots to run in the complex service tasks consisting of multiple connected actions in a dynamic environment. This methodology can also let the intelligent operation of the robots serve in different scenes according to their actual requirements. This research focuses on enhancing the prototype robot vision function and development of additional value for consolidation manageable platform that increases service robots in the home environment of intelligent control capability.</p>", "Keywords": "Anthropomorphic communication; Dual-robots; Visual communication; Body posture; Visual identification", "DOI": "10.1007/s11042-023-17989-w", "PubYear": 2024, "Volume": "83", "Issue": "23", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence/School of Future Technology, Nanjing University of Information Science and Technology, Nanjing, China; Corresponding author."}], "References": [{"Title": "A Novel Attack on Monochrome and Greyscale Devanagari CAPTCHAs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "An efficient technique for breaking of coloured Hindi CAPTCHA", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "16", "Page": "11661", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 112096779, "Title": "Dual-attention-based semantic-aware self-supervised monocular depth estimation", "Abstract": "<p>Based on the assumption of photometric consistency, self-supervised monocular depth estimation has been widely studied due to the advantage of avoiding costly annotations. However, it is sensitive to noise, occlusion issues and photometric changes. To overcome these problems, we propose a multi-task model with a dual-attention-based cross-task feature fusion module (DCFFM). We simultaneously predict depth and semantic with a shared encoder and two separate decoders, aiming to improve depth estimation with the enhancement of semantic supervision information. In DCFFM, we fuse the cross-task features with both pixel-wise and channel-wise attention, which fully excavate and make good use of the helpful information from the other task mutually. We compute both of two attentions in a one-to-all manner to capture global information while limiting the rapid growth of computation. Furthermore, we propose a novel data augmentation method called data exchange & recovery (DE &R), which performs inter-batch data exchange in both vertical and horizontal direction so as to increase the diversity of input data. It encourages the network to explore more diversified cues for depth estimation and avoid overfitting. And essentially, the corresponding outputs are further recovered in order to keep the geometry relationship and ensure the correct calculation of photometric loss. Extensive experiments on the KITTI dataset and the NYU-Depth-v2 dataset demonstrate that our method is very effective and achieves better performance compared with other state-of-the-art works.</p>", "Keywords": "Attention mechanism; Data augmentation; Monocular depth estimation; Self-supervised learning", "DOI": "10.1007/s11042-023-17976-1", "PubYear": 2024, "Volume": "83", "Issue": "24", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, South China University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "Feng Ye", "Affiliation": "School of Mechanical and Automotive Engineering, South China University of Technology, Guangzhou, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, South China University of Technology, Guangzhou, China"}], "References": [{"Title": "Learning depth via leveraging semantics: Self-supervised monocular depth estimation with both implicit and explicit semantic guidance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "137", "Issue": "", "Page": "109297", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 112096787, "Title": "Investigating the mechanism of Banxia Xiexin Decoction in treating Helicobacter pylori-associated chronic atrophic gastritis through network pharmacology and molecular docking", "Abstract": "<b  >Objective</b> The aim of this study was to investigate the mechanism of Banxia Xiexin Decoction in treating Helicobacter pylori (HP)-associated chronic atrophic gastritis (CAG) using network pharmacology methods. <b  >Methods</b> Differential genes between HP-associated CAG and healthy individuals were screened from the Gene Expression Omnibus (GEO) database using high-throughput gene expression data. The compounds and targets of Banxia Xiexin Decoction were obtained from the Traditional Chinese Medicine Systems Pharmacology (TCMSP) database. <b  >Results</b> Through screening, 217 differentially expressed genes were obtained. Banxia Xiexin Decoction contained a total of 211 active components corresponding to 258 target genes. Nine key targets, including DPP4, SLC6A4, CYP3A4, HMOX1, MGAM, MTTP, APOB, UGT1A1, and CASP1, were identified. The enrichment analysis showed that Banxia Xiexin Decoction exerted its therapeutic effects on HP-associated CAG by participating in biological processes such as energy metabolism, oxidative reactions, cell apoptosis, anti-inflammatory response, and immune response, as well as regulating drug metabolism processes, heme and chlorophyll metabolism, steroid hormone biosynthesis, and retinol metabolism pathways. <b  >Conclusion</b> The main active components of Banxia Xiexin Decoction are quercetin, ellagic acid, and genistein, and it exerts its therapeutic effects on HP-associated chronic atrophic gastritis through a mechanism involving multiple components, targets, and pathways.", "Keywords": "Banxia Xiexin Decoction ; Chronic atrophic gastritis ; Network pharmacology ; Molecular docking", "DOI": "10.1016/j.jrras.2024.100824", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Pharmacy, The Second Affiliated Hospital of Baotou Medical College, Inner Mongolia University of Science and Technology, Baotou, 014030, China;Department of Pharmacy, Baotou Medical College, Baotou, 014040, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pharmacy, The Second Affiliated Hospital of Baotou Medical College, Inner Mongolia University of Science and Technology, Baotou, 014030, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Pharmacy, The Second Affiliated Hospital of Baotou Medical College, Inner Mongolia University of Science and Technology, Baotou, 014030, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Pharmacy, Baotou Medical College, Baotou, 014040, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmacy, Baotou Medical College, Baotou, 014040, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmacy, The Second Affiliated Hospital of Baotou Medical College, Inner Mongolia University of Science and Technology, Baotou, 014030, China;Corresponding author. The Second Affiliated Hospital of Baotou Medical College, 30 Hude Mulin Street, Baotou, 014040, China"}], "References": []}, {"ArticleId": 112096825, "Title": "Optimization-Free Test-Time Adaptation for Cross-Person Activity Recognition", "Abstract": "<p>Human Activity Recognition (HAR) models often suffer from performance degradation in real-world applications due to distribution shifts in activity patterns across individuals. Test-Time Adaptation (TTA) is an emerging learning paradigm that aims to utilize the test stream to adjust predictions in real-time inference, which has not been explored in HAR before. However, the high computational cost of optimization-based TTA algorithms makes it intractable to run on resource-constrained edge devices. In this paper, we propose an Optimization-Free Test-Time Adaptation (OFTTA) framework for sensor-based HAR. OFTTA adjusts the feature extractor and linear classifier simultaneously in an optimization-free manner. For the feature extractor, we propose Exponential Decay Test-time Normalization (EDTN) to replace the conventional batch normalization (CBN) layers. EDTN combines CBN and Test-time batch Normalization (TBN) to extract reliable features against domain shifts with TBN's influence decreasing exponentially in deeper layers. For the classifier, we adjust the prediction by computing the distance between the feature and the prototype, which is calculated by a maintained support set. In addition, the update of the support set is based on the pseudo label, which can benefit from reliable features extracted by EDTN. Extensive experiments on three public cross-person HAR datasets and two different TTA settings demonstrate that OFTTA outperforms the state-of-the-art TTA approaches in both classification performance and computational efficiency. Finally, we verify the superiority of our proposed OFTTA on edge devices, indicating possible deployment in real applications. Our code is available at https://github.com/Claydon-Wang/OFTTA.</p>", "Keywords": "", "DOI": "10.1145/3631450", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "PAMI Research Group, Department of Computer and Information Science, University of Macau, Taipa, Macau"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Research Asia, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>un Xi", "Affiliation": "Southern University of Science and Technology, Shenzhen, Guang Dong, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "PAMI Research Group, Department of Computer and Information Science, University of Macau, Taipa, Macau, Centre for Artificial Intelligence and Robotics, Institute of Collaborative Innovation, University of Macau, Taipa, Macau"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing Normal University, Naning, Jiang Su, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics and Data Science, Southern University of Science and Technology, Shenzhen, Guang Dong, China"}], "References": [{"Title": "A Systematic Study of Unsupervised Domain Adaptation for Robust Human-Activity Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "A survey on video-based Human Action Recognition: recent updates, datasets, challenges, and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "3", "Page": "2259", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Learning Disentangled Beha<PERSON> Patterns for Wearable-based Human Activity Recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Deep Ensemble Learning for Human Activity Recognition Using Wearable Sensors via Filter Activation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "Domain Generalization for Activity Recognition via Adaptive Feature Fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": *********, "Title": "How should robots exercise with people? Robot-mediated exergames win with music, social analogues, and gameplay clarity", "Abstract": "<p> Introduction: The modern worldwide trend toward sedentary behavior comes with significant health risks. An accompanying wave of health technologies has tried to encourage physical activity, but these approaches often yield limited use and retention. Due to their unique ability to serve as both a health-promoting technology and a social peer, we propose robots as a game-changing solution for encouraging physical activity. </p><p> Methods: This article analyzes the eight exergames we previously created for the Rethink Baxter Research Robot in terms of four key components that are grounded in the video-game literature: repetition, pattern matching, music, and social design. We use these four game facets to assess gameplay data from 40 adult users who each experienced the games in balanced random order. </p><p> Results: In agreement with prior research, our results show that relevant musical cultural references, recognizable social analogues, and gameplay clarity are good strategies for taking an otherwise highly repetitive physical activity and making it engaging and popular among users. </p><p> Discussion: Others who study socially assistive robots and rehabilitation robotics can benefit from this work by considering the presented design attributes to generate future hypotheses and by using our eight open-source games to pursue follow-up work on social-physical exercise with robots. </p>", "Keywords": "Human-robot interaction (HRI); Socially Assistive Robotics (SAR); Physical HRI; Exercise games; personal robots; rehabilitation robotics", "DOI": "10.3389/frobt.2023.1155837", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Collaborative Robotics and Intelligent Systems (CoRIS) Institute, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Haptic Intelligence Department, Germany"}, {"AuthorId": 3, "Name": "Rhian C. Preston", "Affiliation": "Collaborative Robotics and Intelligent Systems (CoRIS) Institute, United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Rehabilitation Robotics Lab, United States"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Haptic Intelligence Department, Germany"}], "References": [{"Title": "Development and validation of the player experience inventory: A scale to measure player experiences at the level of functional and psychosocial consequences", "Authors": "<PERSON><PERSON>den <PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "102370", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Challenge types in gaming validation of video game challenge inventory (CHA)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "102473", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Robotic System for Physical Training of Older Adults", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "5", "Page": "1109", "JournalTitle": "International Journal of Social Robotics"}]}, {"ArticleId": 112096921, "Title": "An Ensemble-Based Multi-Classification Machine Learning Classifiers Approach to Detect Multiple Classes of Cyberbullying", "Abstract": "<p>The impact of communication through social media is currently considered a significant social issue. This issue can lead to inappropriate behavior using social media, which is referred to as cyberbullying. Automated systems are capable of efficiently identifying cyberbullying and performing sentiment analysis on social media platforms. This study focuses on enhancing a system to detect six types of cyberbullying tweets. Employing multi-classification algorithms on a cyberbullying dataset, our approach achieved high accuracy, particularly with the TF-IDF (bigram) feature extraction. Our experiment achieved high performance compared with that stated for previous experiments on the same dataset. Two ensemble machine learning methods, employing the N-gram with TF-IDF feature-extraction technique, demonstrated superior performance in classification. Three popular multi-classification algorithms: Decision Trees, Random Forest, and XGBoost, were combined into two varied ensemble methods separately. These ensemble classifiers demonstrated superior performance compared to traditional machine learning classifier models. The stacking classifier reached 90.71% accuracy and the voting classifier 90.44%. The results of the experiments showed that the framework can detect six different types of cyberbullying more efficiently, with an accuracy rate of 0.9071.</p>", "Keywords": "", "DOI": "10.3390/make6010009", "PubYear": 2024, "Volume": "6", "Issue": "1", "JournalId": 51955, "JournalTitle": "Machine Learning and Knowledge Extraction", "ISSN": "", "EISSN": "2504-4990", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering and Computer Science, Florida Atlantic University, 777 Glades Road, Boca Raton, FL 33431, USA; Ministry of National Guard, King Khalid Military Academy, Riyadh 14625, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Electrical Engineering and Computer Science, Florida Atlantic University, 777 Glades Road, Boca Raton, FL 33431, USA"}], "References": [{"Title": "A Comparative Analysis of Machine Learning Techniques for Cyberbullying Detection on Twitter", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "11", "Page": "187", "JournalTitle": "Future Internet"}, {"Title": "Machine Learning: Algorithms, Real-World Applications and Research Directions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Classification of movie reviews using term frequency-inverse document frequency and optimized machine learning algorithms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "e914", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Performance analysis of transformer-based architectures and their ensembles to detect trait-based cyberbullying", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Detecting Arabic Cyberbullying Tweets Using Machine Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "1", "Page": "29", "JournalTitle": "Machine Learning and Knowledge Extraction"}, {"Title": "A Review on Deep-Learning-Based Cyberbullying Detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; M<PERSON><PERSON> <PERSON>; Md. <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "5", "Page": "179", "JournalTitle": "Future Internet"}]}, {"ArticleId": 112097071, "Title": "Factors Affecting Workers’ Mental Stress in Handover Activities During Human–Robot Collaboration", "Abstract": "Objective <p>This study investigated the effects of different approach directions, movement speeds, and trajectories of a co-robot’s end-effector on workers’ mental stress during handover tasks.</p> Background <p>Human–robot collaboration (HRC) is gaining attention in industry and academia. Understanding robot-related factors causing mental stress is crucial for designing collaborative tasks that minimize workers’ stress.</p> Methods <p>Mental stress in HRC tasks was measured subjectively through self-reports and objectively through galvanic skin response (GSR) and electromyography (EMG). Robot-related factors including approach direction, movement speed, and trajectory were analyzed.</p> Results <p>Movement speed and approach direction had significant effects on subjective ratings, EMG, and GSR. High-speed and approaching from one side consistently resulted in higher fear, lower comfort, and predictability, as well as increased EMG and GSR signals, indicating higher mental stress. Movement trajectory affected GSR, with the sudden stop condition eliciting a stronger response compared to the constrained trajectory. Interaction effects between speed and approach direction were observed for “surprise” and “predictability” subjective ratings. At high speed, approach direction did not significantly differ, but at low speeds, approaching from the side was found to be more surprising and unpredictable compared to approaching from the front.</p> Conclusion <p>The mental stress of workers during HRC is lower when the robot’s end effector (1) approaches a worker within the worker’s field of view, (2) approaches at a lower speed, or (3) follows a constrained trajectory.</p> Application <p>The outcome of this study can serve as a guide to design HRC tasks with a low level of workers’ mental stress.</p>", "Keywords": "collaborative robot;mental health;unpredictable motion;workplace safety", "DOI": "10.1177/00187208241226823", "PubYear": 2024, "Volume": "66", "Issue": "12", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "North Carolina State University, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "North Carolina State University, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "North Carolina State University, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "North Carolina State University, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "North Carolina State University, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "North Carolina State University, USA"}], "References": [{"Title": "Robot Errors in Proximate HRI", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Human-Robot Interaction"}, {"Title": "Do you feel safe with your robot? Factors influencing perceived safety in human-robot interaction based on subjective and objective measures", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "158", "Issue": "", "Page": "102744", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 112097122, "Title": "A deep learning-based illumination transform for devignetting photographs of dermatological lesions", "Abstract": "Photographs of skin lesions taken with standard digital cameras (macroscopic images) have gained wide acceptance in dermatology. However, uneven background lighting caused by nonstandard image acquisition negatively impacts lesion segmentation and diagnosis. To address this, we propose an automated illumination equalization method based on a counter exponential transform (IECET). A modified residual network (ResNet) regressor is used to automate the selection of the operational parameter of the IECET. The regressor is designed by modifying the final fully-connected layer of the baseline ResNet-50 model. The modified fully-connected layer is coupled to a regression layer in the modified ResNet regressor. A prior knowledge base is created to train the modified ResNet regressor. For this, a set of corrupted images are generated by simulating uneven background illumination on pristine images. The knowledge base is created by including pairs of value components obtained from the HSV color space version of the corrupted macroscopic images and ideal operational parameter values that maximize the peak signal-to-noise ratio (PSNR) between the pristine images and the IECET outputs. We evaluated segmentation accuracies of the deep threshold prediction network (DTP-Net), DeepLabV3   +, fully convolutional network (FCN), and U-Net on the corrupted macroscopic images and output images of the IECET. The DTP-Net, DeepLabV3   +, FCN, and U-Net exhibited Dice similarity coefficient (DSC) of 0.71 ± 0.26, 0.85 ± 0.15, 0.75 ± 0.22, and 0.66 ± 0.28 on corrupted images and 0.81 ± 0.17, 0.87 ± 0.12, 0.79 ± 0.18, and 0.79 ± 0.15, on the outputs of the IECET. Increase in DSC proves the ability of the IECET to improve the performance of deep learning models used to segment skin lesions on macroscopic images.", "Keywords": "", "DOI": "10.1016/j.imavis.2024.104909", "PubYear": 2024, "Volume": "142", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Puducherry, Karaikal, Puducherry 609609, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Puducherry, Karaikal, Puducherry 609609, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Brain Research, Indian Institute of Science, Bangalore, Karnataka 560012, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Dermatology, Kerala Health Services, Trivandrum, Kerala 695035, India"}], "References": [{"Title": "Transfer learning in computer vision tasks: Remember where you come from", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "103853", "JournalTitle": "Image and Vision Computing"}, {"Title": "Skin lesion segmentation using fully convolutional networks: A comparative experimental study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Aysegul Ucar", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113742", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Bayesian retinex underwater image enhancement", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104171", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Deep Learning Based Modeling of Groundwater Storage Change", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "3", "Page": "4599", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Learning an augmentation strategy for sparse datasets", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "117", "Issue": "", "Page": "104338", "JournalTitle": "Image and Vision Computing"}, {"Title": "CDLSTM: A Novel Model for Climate Change Forecasting", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "2", "Page": "2363", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Shading and texture constrained retinex for correcting vignetting on dermatological macro images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "2", "Page": "693", "JournalTitle": "The Visual Computer"}, {"Title": "DNNBoT: Deep Neural Network-Based Botnet Detection and Classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "1", "Page": "1729", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Development of PCCNN-Based Network Intrusion Detection System for EDGE Computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "1", "Page": "1769", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Brain Tumor Identification Using Data Augmentation and Transfer Learning Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "46", "Issue": "2", "Page": "1845", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Single image dehazing using extended local dark channel prior", "Authors": "P<PERSON><PERSON><PERSON>; Soumendu Chakraborty", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "104747", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 112097190, "Title": "Relationship Between Self-Efficacy and Technology-Based Teaching Styles in Mathematics Education Among Pre-Service Teachers in UPSI", "Abstract": "Self-efficacy is defined as a task-specific belief in a person’s potential and ability to master a skill or a task. As the technology is getting sophisticated, teachers are encouraged to apply the technology as teaching aid tool in the class. This research is done to investigate the relationship between the self-efficacy and the technology-based teaching styles of Mathematics pre-service teachers among Sultan Idris University of Education (UPSI) undergraduates. The research design used in this study is correlational study at where 148 AT14 Bachelor of Education (Mathematics) course and AT48 Bachelor of Science (Mathematics) with Education students are the sample. The statistical method used in this study is descriptive statistic which is mean and inferential statistics through Pearson’s correlation were used to study the relationship. The findings show that the level of self-efficacy and practice of technology-based teaching of pre-service teachers is significantly high. The results showed that there was a strong positive significant correlation between the self-efficacy and the technology-based teaching styles of those pre-service teachers (r=0.656, p=0.00, p<0.05). In conclusion, this study unequivocally demonstrates that UPSI’s pre-service teachers are well prepared to train themselves with higher self-esteem and hence applying technology-based teaching style while teaching Mathematics.", "Keywords": "", "DOI": "10.1051/itmconf/20245803008", "PubYear": 2024, "Volume": "58", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departement of Mathematics, Universiti Pendidikan Sultan <PERSON>, Perak, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departement of Mathematics, Universiti Pendidikan Sultan <PERSON>, Perak, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Departement of Mathematics, Universiti Pendidikan Sultan <PERSON>, Perak, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departement of Human Development, Universiti Pendidikan Sultan <PERSON>, Perak, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Departement of Mathematics, Universiti Pendidikan Sultan <PERSON>, Perak, Malaysia"}], "References": []}, {"ArticleId": 112097196, "Title": "Handling noisy labels via one-step abductive multi-target learning and its application to helicobacter pylori segmentation", "Abstract": "<p>Learning from noisy labels is an important concern in plenty of real-world scenarios. Various approaches for this concern first make corrections corresponding to potentially noisy-labeled instances, and then update predictive model with information of the made corrections. However, in specific areas, such as medical histopathology whole slide image analysis (MHWSIA), it is often difficult or impossible for experts to manually achieve the noisy-free ground-truth labels which leads to labels with complex noise. This situation raises two more difficult problems: 1) the methodology of approaches making corrections corresponding to potentially noisy-labeled instances has limitations due to the complex noise existing in labels; and 2) the appropriate evaluation strategy for validation/testing is unclear because of the great difficulty in collecting the noisy-free ground-truth labels. For the problem 1), we present one-step abductive multi-target learning (OSAMTL) that imposes a one-step logical reasoning upon machine learning via a multi-target learning procedure to constrain the predictions of the learning model to be subject to our prior knowledge about the true target. For the problem 2), we propose a logical assessment formula (LAF) that evaluates the logical rationality of the outputs of an approach by estimating the consistencies between the predictions of the learning model and the logical facts narrated from the results of the one-step logical reasoning of OSAMTL. Based on the Helicobacter pylori (<PERSON><PERSON>) segmentation task in MHWSIA, we show that OSAMTL enables the machine learning model achieving logically more rational predictions, which is beyond various state-of-the-art approaches in handling complex noisy labels.</p>", "Keywords": "Helicobacter pylori segmentation; Learning from noisy labels; Abductive learning", "DOI": "10.1007/s11042-023-17743-2", "PubYear": 2024, "Volume": "83", "Issue": "24", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Clinical Pathology, West China Hospital, Sichuan University, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Clinical Pathology, West China Hospital, Sichuan University, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Clinical Pathology, West China Hospital, Sichuan University, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Pre-Medical School, University of San Francisco, San Francisco, USA"}, {"AuthorId": 5, "Name": "<PERSON>hong<PERSON> Zheng", "Affiliation": "Institute of Clinical Pathology, West China Hospital, Sichuan University, Chengdu, China; Corresponding author."}], "References": [{"Title": "Detecting helicobacter pylori in whole slide images via weakly supervised multi-task learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "35-36", "Page": "26787", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 112097207, "Title": "Enhancing the analog to digital converter using proteretic hopfield neural network", "Abstract": "<p>An artificial neural network (ANN) in information technology is a system of hardware or software modeled after the operation of neurons in the human brain. ANNs, often known as \"neural networks,\" are a form of deep learning technology that falls under the umbrella of Artificial Intelligence (AI). Commercial applications of these technologies typically focus on optimization and solving complex signal processing and pattern recognition problems. Multiple types of optimization techniques are utilized to determine the optimal neural network for a model. These procedures help determine and define the model’s accuracy, dependability, functionality, and capacity. The convergence of the neural network helps determine the number of training iterations required to generate the fewest errors. In this paper, we investigate an activation function to help reduce the training time of the analog-to-digital converter (ADC). A new Hopfield ADC model is proposed by using the proteretic activation function property. We supported our research by simulating the new ADC converter and comparing the traditional Hopfield ADC, the hysteretic ADC, and the proteretic ADC. Experiment and simulation demonstrate that the proteretic function provides a faster rate of convergence than other functions, thereby enhancing the performance of the ADC application.</p>", "Keywords": "Hopfield neural network; Hysteresis; Porteresis; Analog to digital converter; Convergence", "DOI": "10.1007/s00521-023-09373-4", "PubYear": 2024, "Volume": "36", "Issue": "11", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Informatics, Sulaimani Polytechnic University, Sulaimani, Iraq; School of Electrical, Computer, and Biomedical Engineering, Southern Illinois University, Carbondale, USA; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical, Computer, and Biomedical Engineering, Southern Illinois University, Carbondale, USA; Information Technology, James Cook University, Brisbane, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Informatics, Sulaimani Polytechnic University, Sulaimani, Iraq; School of Electrical, Computer, and Biomedical Engineering, Southern Illinois University, Carbondale, USA"}], "References": []}, {"ArticleId": *********, "Title": "A Novel Newton–Raphson Modulo Generator and Its VLSI Architecture for Pseudorandom Bit Generation", "Abstract": "<p>The existing methods for generating random numbers may get decoded, making the produced random sequences insecure, which may compromise the security of encryptions used in various communication fields. New and robust random generator methods are essential for securing communication systems. Therefore, a novel method is proposed in this paper to generate random numbers and their bits. The modified <PERSON>–<PERSON> method becomes the foundation for the proposed random number generator. As a result, the Newton–Rap<PERSON>on modulo generator (NRMG) is the name given to the random number generator. The generated sequence of the NRMG is exponential in nature and varying, which makes them very hard to predict. Various statistical tests are performed on the generated sequence, and results reaffirm that the sequence has high non-predictability. The VLSI architecture of the proposed method is also implemented using Hardware description languages to assess their complexity during real-time implementation. This is performed through the digital signal processing (DSP) tool of the Xilinx system generator in conjunction with MATLAB. The proposed method architecture is also implemented at different bit sizes on a SOC prototype PYNQ-Z1 board for its validation.</p>", "Keywords": "Newton–Raphson modulo generator (NRMG); VLSI architecture for the proposed method; NIST test suite; Xilinx system generator; FPGA SoC prototype", "DOI": "10.1007/s42979-023-02496-4", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, National Institute of Technology Jamshedpur, Adityapur, India; Corresponding author."}, {"AuthorId": 2, "Name": "Tarni Man<PERSON>", "Affiliation": "Department of Mathematics, National Institute of Technology Jamshedpur, Adityapur, India"}], "References": [{"Title": "Security of Internet of Things edge devices", "Authors": "<PERSON><PERSON>han Lv", "PubYear": 2021, "Volume": "51", "Issue": "12", "Page": "2446", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Secure data dissemination techniques for IoT applications: Research challenges and opportunities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "12", "Page": "2469", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Lightweight image encryption algorithm using NLFSR and CBC mode", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "17", "Page": "19452", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 112097241, "Title": "Picker Blinder: a framework for automatic injection of malicious inter-app communication", "Abstract": "<p>Malware writers, with the aim to elude the current detection mechanism implemented by commercial and free anti-malware, are finding new ways to develop new aggressive attack paradigms. Current anti-malware basically suffer about the following limitations: the first one is that they are not able to detect zero-day malware: as a matter of fact, to mark an application as malware they need to know the malicious payload signature. With regard to the second limitation, they are able to scan only one application at a time: this is the reason why a type of malware characterized by the colluding attack, where the malicious behaviour is divided between several applications, can never be detected. To demonstrate the ineffectiveness of current anti-malware in detecting colluding attacks, in this paper we design a method aimed to automatically inject a malicious payload in two or more different Android applications. We implemented the proposed method into a framework that we called <PERSON><PERSON> Blinder . In a nutshell, <PERSON><PERSON> Blinder is able to inject a collusive malicious payload exploiting two different channels (i.e., SharedPreferences and Sockets), allowing the attacker to catch sensitive and private information stored into the infected device. We perform an experimental analysis by submitting 398 colluding applications to different 79 anti-malware, by showing that current detection mechanisms are not able to detect this kind of threat.</p>", "Keywords": "Collusion; Malware; Security; Android; Mobile", "DOI": "10.1007/s11416-023-00510-0", "PubYear": 2024, "Volume": "20", "Issue": "2", "JournalId": 3946, "JournalTitle": "Journal of Computer Virology and Hacking Techniques", "ISSN": "", "EISSN": "2263-8733", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biosciences and Territory, University of Molise, Pesche, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Istituto di Informatica e Telematica, Consiglio Nazionale delle Ricerche, Pisa, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Istituto di Informatica e Telematica, Consiglio Nazionale delle Ricerche, Pisa, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Istituto di Informatica e Telematica, Consiglio Nazionale delle Ricerche, Pisa, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Istituto di Informatica e Telematica, Consiglio Nazionale delle Ricerche, Pisa, Italy; Corresponding author."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medicine and Health Sciences “Vincenzo <PERSON>, University of Molise, Campobasso, Italy"}], "References": [{"Title": "2Faces: a new model of malware based on dynamic compiling and reflection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "3", "Page": "215", "JournalTitle": "Journal of Computer Virology and Hacking Techniques"}]}, {"ArticleId": 112097272, "Title": "Implementation of an Augmented Reality Guide: Auto-Presenting Suitable Content to Adults and Young Children about <PERSON>’s Oil Paintings", "Abstract": "A single painting can be embedded with rich layers of information. However, the information on a painting's museum label remains limited. The growth of mobile computing has facilitated the creation of innovative self-guiding apps. Augmented reality (AR) technology allows for dynamic digital content to be displayed on real-world objects and scenes, thus expanding the explanation of abstract and underlying stories beyond the limitations of static text. An augmented reality art guide application was developed to allow adults and children to access suitable content about paintings with the assistance of a gyroscope, following a case study of <PERSON>’s oil paintings. In this study, four oil paintings of different orientations and sizes were selected for the design implementation to provide threshold suggestions for the gyroscope. This natural user interface AR guide application that can precisely deliver the content to adults and young children offers the potential to create in-depth content for adults interested in art and child-friendly inspiring content for future artists. © 2024, CAD Solutions, LLC. All rights reserved.", "Keywords": "Art Guide Application; Augmented Reality; Mobile Learning; Museum Education; Natural User Interface", "DOI": "10.14733/cadaps.2024.869-877", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Taipei University of Technology, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Taipei University of Technology, Taiwan"}], "References": []}, {"ArticleId": 112097298, "Title": "ADA-SHARK", "Abstract": "<p>Due to global warming, sharks are moving closer to the beaches, affecting the risk to humans and their own lives. Within the past decade, several technologies were developed to reduce the risks for swimmers and surfers. This study proposes a robust method based on computer vision to detect sharks using an underwater camera monitoring system to secure coastlines. The system is autonomous, environment-friendly, and requires low maintenance. 43,679 images extracted from 175 hours of videos of marine life were used to train our algorithms. Our approach allows the collection and analysis of videos in real-time using an autonomous underwater camera connected to a smart buoy charged with solar panels. The videos are processed by a Domain Adversarial Convolutional Neural Network to discern sharks regardless of the background environment with an F2-score of 83.2% and a recall of 90.9%, while human experts have an F2-score of 94% and a recall of 95.7%.</p>", "Keywords": "", "DOI": "10.1145/3631416", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Boston University, Metropolitan College, Boston, Massachusetts, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Inria Rennes, Serpico, Rennes, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Boston University, Metropolitan College, Boston, Massachusetts, United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Centre Sécurité <PERSON>, Saint-Leu, La Réunion, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Fetch Engineering, Le Port, La Réunion, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON>, Chatillon, France"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Boston University, Metropolitan College, Boston, Massachusetts, United States"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Boston University, Metropolitan College, Boston, Massachusetts, United States"}], "References": [{"Title": "UQRCom", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 112097370, "Title": "Distribution characteristics of inhalable particulate pollutants and their effects on cardiopulmonary respiratory system of outdoor football players in a smart healthcare system", "Abstract": "<p>The Internet of Things (IoT) is a new revolution that integrates multiple technologies to improve our lifestyle and healthcare. With the use of this technology, football players may be safeguarded from health issues such as a cardiopulmonary respiratory system that could arise during a game or practice. This paper investigates the effect of inhalable particle pollution on the number of cardiovascular and respiratory diseases during outdoor football games using an advanced IoT-enabled approach. First, this research work investigates the negative impacts of inhalable air particles on the health of IoT-enabled outdoor football players. Second, it designs a sophisticated IoT-based monitoring system for real-time health evaluation, with a particular emphasis on the impact of inhalable particle pollution on the cardiovascular and respiratory systems of the participants. This proposed system is made up of three interconnected layers: the Sensory Layer, which is outfitted with lightweight IoT sensors and meticulously stationed for real-time monitoring of the players' physiological parameters and stadium environmental conditions; the Network Layer, which is in charge of facilitating seamless data flow between the sensors and the Cloud Layer; and the Cloud Layer, which is in charge of secure data storage, intricate analysis, and the extraction of invaluable information. Third, it employs Kriging estimation as a multi-step method that includes variogram modeling to analyze spatial data dependency, providing insights into how data points relate to distance and direction. <PERSON><PERSON><PERSON> calculates the difference between data points and generates spatial concentration maps that illustrate the geographical distributions of particle pollutants. Finally, it employs Linear Regression (LR) analysis to investigate the relationship between inhalable particle pollution and cardiopulmonary respiratory disease in outdoor football players. The data are dimensioned before comparison to produce the correlation coefficient, which reveals the relationship between these variables. The experimental result shows that in the single pollution model of inhalable particulate pollutant PM2.5, the relative risk values of cardiorespiratory diseases in different populations of outdoor football are 1.0022, 1.0019, 1.0024, 1.0025, and 1.0028, respectively. In CO, SO2, NO2, O3, and other multi-pollutant models, the concentration level of inhalable particulate pollutant PM2.5 increased by 10 μg/m<sup>3</sup>, and the number of visits for cardiorespiratory diseases in the whole population increased by 0.339% (95% CI 0.220–0.458) after the 10th day of outdoor football. In addition, the number of visits for cardiorespiratory diseases in men and women who lagged on the 10th day of outdoor football increased by 0.322% (95% CI 0.198–0.446) and 0.345% (95% CI 0.223–0.467), respectively. The number of visits for cardiorespiratory diseases of the elderly and children who lagged on the 10th day of outdoor football increased by 0.373% (95% CI 0.234–0.512) and 0.364% (95% CI 0.229–0.500), respectively.</p>", "Keywords": "Inhalable particles; Air pollution; Outdoor sports; Football exercise; Internet of Things; Cardiopulmonary respiratory system", "DOI": "10.1007/s00500-023-09589-5", "PubYear": 2024, "Volume": "28", "Issue": "3", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Outdoor Sports, Guilin Tourism University, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Outdoor Sports, Guilin Tourism University, Guilin, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Outdoor Sports, Guilin Tourism University, Guilin, China"}], "References": [{"Title": "Adaptive event‐triggered robust\n H \n <sub>\n ∞ \n </sub>\n control for Takagi–Sugeno fuzzy networked Markov jump systems with time‐varying delay", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "1", "Page": "213", "JournalTitle": "Asian Journal of Control"}, {"Title": "An Effective WSSENet-Based Similarity Retrieval Method of Large Lung CT Image Databases", "Authors": "", "PubYear": 2022, "Volume": "16", "Issue": "7", "Page": "2359", "JournalTitle": "KSII Transactions on Internet and Information Systems"}, {"Title": "A data-driven approach for intrusion and anomaly detection using automated machine learning for the Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "19", "Page": "14469", "JournalTitle": "Soft Computing"}, {"Title": "A hybrid CEEMD-GMM scheme for enhancing the detection of traffic flow on highways", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "21", "Page": "16373", "JournalTitle": "Soft Computing"}, {"Title": "Advanced efficient strategy for detection of dark objects based on spiking network with multi-box detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "12", "Page": "36307", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Regional feature fusion for on-road detection of objects using camera and 3D-LiDAR in high-speed autonomous vehicles", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "23", "Page": "18195", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 112097386, "Title": "Distribution amplitude and decay constant of 1S and 2S state light mesons in the light-front quark model", "Abstract": "Studying meson structures is essential for gaining insights into the nonperturbative nature of Quantum Chromodynamics (QCD). This study will focus on calculating the decay constant and distribution amplitudes (DAs) of unflavored light mesons (<i>π<i/> and <i>ρ<i/>) in the 1<i>S<i/> and 2<i>S<i/> states using the Light Front Quark Model. This study utilizes the QCD-motivated Hamiltonian, taking into account both contact and smeared spin-spin interactions. The two lowest harmonic oscillator bases are employed in this work to achieve improved results for the 2<i>S<i/> states. The study found the optimal mixing parameter in basis expansion coefficients to be <i>θ<i/> = 10°. Light meson properties, including the mass spectrum, decay constant, and twist-2 DAs, are then predicted using model parameters fixed through the variational principle. While the contact spin-spin interaction yields poor accuracy, the results from the smeared interaction generally agree well with experimental data and other theoretical models with MeV and MeV in the mixed state. Unlike the case for the 1<i>S<i/> state, it should be noted that the properties of the 2<i>S<i/> state are sensitive to the mixing parameter <i>θ<i/>. In addition, we observe that the decay constant for <i>ρ<i/>(2S) is MeV. While for <i>π<i/>(2S) the decay constant is extremely small with the value of MeV, which is mainly due to the dynamical chiral symmetry breaking.", "Keywords": "LFQM;decay constant;DAs", "DOI": "10.1051/itmconf/20246101014", "PubYear": 2024, "Volume": "61", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Physics, Faculty of Mathematics and Natural Sciences (FMIPA), Universitas Indonesia, Depok 16424, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Few-Body System in Physics Laboratory, RIKEN Nishina Center, Wako 351-0198, Japan;Research Center for Nuclear Physics, Osaka University, Ibaraki, Osaka 567-0047, Japan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Physics, Faculty of Mathematics and Natural Sciences (FMIPA), Universitas Indonesia, Depok 16424, Indonesia"}], "References": []}, {"ArticleId": 112097444, "Title": "Utilizing Aerial Imagery and Deep Learning Techniques for Identifying Banana Plants Diseases", "Abstract": "The primary agricultural pursuit in Malaysia centres around banana cultivation; however, this vital crop faces the daunting challenge of multiple diseases that hinder its growth. The adverse consequences of these diseases extend beyond the farms to impact the nation’s economy. To empower farmers with the tools to promptly identify and categorize these diseases, image processing techniques offer a valuable solution. This research leverages deep learning Convolutional Neural Networks (CNN) implemented through MATLAB in conjunction with a DJI drone. By harnessing this technology, the system can automatically detect and classify major banana diseases. The study meticulously fine-tuned several hyperparameters to achieve impressive training and testing accuracy levels. The results revealed that the model attained its highest training accuracy of 81.27% at epoch 8 and its lowest accuracy of 78.40% at epoch 4, demonstrating its potential to aid in early disease detection and classification in banana crops.", "Keywords": "", "DOI": "10.1051/itmconf/20246000013", "PubYear": 2024, "Volume": "60", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Quality Engineering Research Cluster, Malaysian Institute of Industrial Technology, Universiti Kuala Lumpur, 81700, Johor, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Quality Engineering Research Cluster, Malaysian Institute of Industrial Technology, Universiti Kuala Lumpur, 81700, Johor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Quality Engineering Research Cluster, Malaysian Institute of Industrial Technology, Universiti Kuala Lumpur, 81700, Johor, Malaysia"}, {"AuthorId": 4, "Name": "Nor Samsiah Sani", "Affiliation": "Center for Artificial Intelligence Technology, Universiti Kebangsaan Malaysia, Selangor, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Quality Engineering Research Cluster, Malaysian Institute of Industrial Technology, Universiti Kuala Lumpur, 81700, Johor, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Plantation and Agrotechnology, Universiti Teknologi MARA (UiTM), Cawangan Melaka Kampus Jasin, 77300 Merlimau, Melaka, Malaysia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "UTM-MPRC Institute for Oil & Gas (IFOG), Universiti Teknologi Malaysia, Malaysia"}], "References": [{"Title": "Banana leaf diseased image classification using novel HEAP auto encoder (HAE) deep learning", "Authors": "<PERSON><PERSON>;  <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "41-42", "Page": "30601", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Ensemble Learning for Rainfall Prediction", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "153", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Development of Pipe Inspection Robot using Soft Actuators, Microcontroller and LabVIEW", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "3", "Page": "349", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Development of Underwater Pipe Crack Detection System for Low-Cost Underwater Vehicle using Raspberry Pi and Canny Edge Detection Method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "11", "Page": "456", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}]}, {"ArticleId": 112097450, "Title": "Preface", "Abstract": "", "Keywords": "", "DOI": "10.1051/itmconf/20246000001", "PubYear": 2024, "Volume": "60", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 112097533, "Title": "Reenvisioning Patient Education with Smart Hospital Patient Rooms", "Abstract": "<p>Smart hospital patient rooms incorporate various smart devices to allow digital control of the entertainment --- such as TV and soundbar --- and the environment --- including lights, blinds, and thermostat. This technology can benefit patients by providing a more accessible, engaging, and personalized approach to their care. Many patients arrive at a rehabilitation hospital because they suffered a life-changing event such as a spinal cord injury or stroke. It can be challenging for patients to learn to cope with the changed abilities that are the new norm in their lives. This study explores ways smart patient rooms can support rehabilitation education to prepare patients for life outside the hospital's care. We conducted 20 contextual inquiries and four interviews with rehabilitation educators as they performed education sessions with patients and informal caregivers. Using thematic analysis, our findings offer insights into how smart patient rooms could revolutionize patient education by fostering better engagement with educational content, reducing interruptions during sessions, providing more agile education content management, and customizing therapy elements for each patient's unique needs. Lastly, we discuss design opportunities for future smart patient room implementations for a better educational experience in any healthcare context.</p>", "Keywords": "", "DOI": "10.1145/3631419", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Utah, Salt Lake City, UT, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Utah, Salt Lake City, UT, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Utah, Salt Lake City, UT, USA"}], "References": [{"Title": "Assumptions Checked", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "FORTNIoT", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Bootstrapping Human Activity Recognition Systems for Smart Homes from Scratch", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 112097546, "Title": "Preface of the Conference Proceeding of the 9 <sup>th</sup> ISCPMS 2023 in conjunction with AUA Academic Conference 2023", "Abstract": "", "Keywords": "", "DOI": "10.1051/itmconf/20246100001", "PubYear": 2024, "Volume": "61", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Indonesia Department of Mathematics Faculty of Mathematics and Natural Sciences (FMIPA) Kampus UI Depok Depok, Indonesia, 16424"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Malaya Department of Physics Faculty of Science (FoS) Kuala Lumpur, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "United Arab Emirates University Department of Mathematical Sciences Al Ain, United Arab Emirates, 15551"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Indonesia Department of Physics Faculty of Mathematics and Natural Sciences (FMIPA) Kampus UI Depok Depok, Indonesia, 16424"}], "References": []}, {"ArticleId": 112097576, "Title": "Versatile correlation learning for size-robust generalized counting: A new perspective", "Abstract": "Generalized counting has recently emerged to count novel-class objects within a query image, leveraging limited exemplars. Although methods based on exemplar-query pairs matching have made impressive progress, they typically rely on a single correlation representation, regardless of the varying sizes of objects, which limits more accurate counting. In this paper, we introduce a novel and conceptually straightforward perspective to guide the design of our correlation mechanism that enhances the effectiveness of counting size-diversity objects. Our new perspective encompasses three key aspects: (1) Small objects typically exhibit features concentrated within limited spatial regions, underscoring the importance of an effective channel-wise correlation mechanism for small object counting. (2) Large objects tend to possess rich spatial semantics, making an effective spatial-wise correlation mechanism crucial for large object counting. (3) Integrating both channel-wise and spatial-wise correlation mechanisms holds the potential to enhance counting accuracy across different object sizes. Building upon the above perspective, firstly, we propose a simple yet effective Dual-level Channel-wise Correlation (DCC) module that utilizes kernel-wise correlation and distinct correlation to encode global-to-local channel-wise relationships, enhancing small objects counting accuracy. Secondly, we develop a 4D-convolution-based Spatial-aware Correlation (4DSC) module to extract local-to-local spatial correlation in 4D space, promoting large objects counting accuracy. Finally, we combine the proposed DCC and 4DSC to realize our Versatile Correlation Module (VCM) to simultaneously process both small and large objects, providing adaptability to object size diversity. Extensive experiments on the FSC-147 dataset and CARPK dataset demonstrate the effectiveness of the proposed methods and the superior performance of our counting model.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.111394", "PubYear": 2024, "Volume": "286", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "Han<PERSON> Yang", "Affiliation": "State Key Laboratory of Industrial Control Technology, College of Control Science and Engineering, Zhejiang University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Alibaba Cloud, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Alibaba Cloud, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Industrial Control Technology, College of Control Science and Engineering, Zhejiang University, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Industrial Control Technology, College of Control Science and Engineering, Zhejiang University, China;Corresponding author"}], "References": [{"Title": "A multi-context representation approach with multi-task learning for object counting", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "197", "Issue": "", "Page": "105927", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deeply scale aggregation network for object counting", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "210", "Issue": "", "Page": "106485", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "ELMGAN: A GAN-based efficient lightweight multi-scale-feature-fusion multi-task model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "252", "Issue": "", "Page": "109434", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Learning the cross-modal discriminative feature representation for RGB-T crowd counting", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "257", "Issue": "", "Page": "109944", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 112097650, "Title": "Analysis of external corrosion protection performance on buried gas pipeline using CIPS and DCVG methods", "Abstract": "The present work investigates the effectiveness of external corrosion protection using Close Interval Potential Survey (CIPS) and Direct Current Voltage Gradient (DCVG) on a buried gas pipeline. These surveys were performed on 12” gas pipeline in East Kalimantan. Coating and cathodic protection were the primary corrosion protection on these pipelines. Soil resistivity measurement was conducted every km along the pipeline to obtain the resistivity profile combined with the pH profile. CIPS showed that the Cathodic Protection system protects 79.21 % of the pipeline area. There is no sign of telluric or stray current effect from the result of CIPS. DCVG showed 159 defects that can be divided into three different clusters. There is 6-point coating defects that should be repaired immediately due to the characteristics and category of the defect. The integrated data analysis of CIPS and DCVG showed a correlation which dips pattern on CIPS graph indicating coating defect on DCVG surveyed. Soil resistivity measurement showed that the soil resistivity profile is negligible from KP 0 – 5, while KP 5 – 14 is mildly corrosive. pH measurement showed the acidity level or the soil along the pipeline were neutral. Cathodic protection level could be improved with the increase of cathodic protection current and coating repair along the area with low-level protection.", "Keywords": "Cathodic protection;coating defect;CIPS;DCVG", "DOI": "10.1051/itmconf/20246101022", "PubYear": 2024, "Volume": "61", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Metallurgy and Material, Faculty of Engineering, Universitas Indonesia, Depok 16424, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Metallurgy and Material, Faculty of Engineering, Universitas Indonesia, Depok 16424, Indonesia"}], "References": []}, {"ArticleId": 112097664, "Title": "Improved small foreign object debris detection network based on YOLOv5", "Abstract": "<p>In response to the challenges of detecting foreign object debris (FOD) on airport runways, where the objects are small in size and have indistinct features leading to false detections and missed detections, significant improvements were made to the YOLOv5 algorithm. First, the original YOLOv5-n model was optimized by incorporating multi-scale fusion and detection enhancements. To improve detection speed and reduce parameters, the detection head for large objects was removed. Second, the C3 module in the backbone network was replaced with the C2f module, resulting in enhanced gradient flow and improved feature representation capabilities. Additionally, the spatial pyramid pooling-fast (SPPF) module in the backbone network was refined to expand the receptive field and enhance the model’s perception of dependencies between targets and backgrounds. Furthermore, the coordinate attention (CA) mechanism was introduced in the neck layer to further enhance the model's perception of small FOD items. Lastly, the SCYLLA-IoU (SIoU) loss function was introduced to further improve the speed and accuracy of bounding box regression. Moreover, the nearest neighbor interpolation upsampling method was substituted with the lightweight Content-Aware ReAssembly of FEatures (CARAFE) upsampling operator to better exploit global information. Experimental results on the Fod_Tiny dataset, which consists of small FOD items in airports, demonstrated a significant 5.4% improvement over the baseline algorithm. To validate the generalizability of the algorithm, experiments were conducted on the Mirco_COCO dataset, resulting in a notable 1.9% improvement compared to the baseline algorithm.</p>", "Keywords": "FOD; Object detection; YOLO algorithm; Deep learning", "DOI": "10.1007/s11554-023-01399-0", "PubYear": 2024, "Volume": "21", "Issue": "1", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Chinese ASEAN Arts, Chengdu University, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu, China"}], "References": [{"Title": "Focal and efficient IOU loss for accurate bounding box regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "506", "Issue": "", "Page": "146", "JournalTitle": "Neurocomputing"}, {"Title": "Towards optimal foreign object debris detection in an airport environment", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "118829", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": *********, "Title": "The effects of coronavirus news on travel and leisure stocks: Evidence from the NARDL model", "Abstract": "This study employs the NARDL model to investigate the asymmetric effects of COVID-19 news on the return and volatility of travel and leisure stocks in both the United States and the global context. Our findings reveal distinct longand short-term impacts of COVID-19 news on these stocks, exhibiting asymmetry and lag effects. Various types of news, including panic-inducing reports, entity-related coronavirus news, and misleading information, contribute to adjustments in travel and leisure stock returns over the long term. Notably, panic-inducing news has a more pronounced long-term impact on stock volatility compared to a higher volume of news sources reporting coronavirus-related information in similar situations. We argue that regulatory bodies can influence the stock market by ensuring the accuracy and reliability of news content.", "Keywords": "tourism;NARDL;coronavirus news", "DOI": "10.1051/itmconf/20246000015", "PubYear": 2024, "Volume": "60", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Science and Technology Information, Beijing Academy of Science and Technology, Beijing 100044, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Normal University, Beijing 100091, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Statistics, Remin University of China, Beijing 100872, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics, Hebei GEO University, Shijiazhuang 050031, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Economics, Hebei GEO University, Shijiazhuang 050031, China"}], "References": []}, {"ArticleId": 112097712, "Title": "S$$^{2}$$P$$^{3}$$: Self-Supervised Polarimetric Pose Prediction", "Abstract": "This paper proposes the first self-supervised 6D object pose prediction from multimodal RGB + polarimetric images. The novel training paradigm comprises (1) a physical model to extract geometric information of polarized light, (2) a teacher–student knowledge distillation scheme and (3) a self-supervised loss formulation through differentiable rendering and an invertible physical constraint. Both networks leverage the physical properties of polarized light to learn robust geometric representations by encoding shape priors and polarization characteristics derived from our physical model. Geometric pseudo-labels from the teacher support the student network without the need for annotated real data. Dense appearance and geometric information of objects are obtained through a differentiable renderer with the predicted pose for self-supervised direct coupling. The student network additionally features our proposed invertible formulation of the physical shape priors that enables end-to-end self-supervised training through physical constraints of derived polarization characteristics compared against polarimetric input images. We specifically focus on photometrically challenging objects with texture-less or reflective surfaces and transparent materials for which the most prominent performance gain is reported.", "Keywords": "Self-supervision; Multi-modalities; Pose estimation; Differentiable rendering", "DOI": "10.1007/s11263-023-01965-w", "PubYear": 2024, "Volume": "132", "Issue": "6", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TUM School of Computation, Information and Technology, Technical University of Munich, Munich, Germany; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TUM School of Computation, Information and Technology, Technical University of Munich, Munich, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TUM School of Computation, Information and Technology, Technical University of Munich, Munich, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "TUM School of Computation, Information and Technology, Technical University of Munich, Munich, Germany; Corresponding author."}], "References": []}, {"ArticleId": 112097762, "Title": "Metric Learning with Sequence-to-sequence Autoencoder for Content-based Music Identification", "Abstract": "Content-based music identification is an active research field that involves recognizing the identity of a musical performance embedded within an audio query. This process holds significant relevance in practical applications, such as radio broadcast monitoring for detecting copyright infringement. Various approaches for content-based music identification have been explored in the existing literature, yielding diverse levels of performance. However, despite the considerable attention dedicated to this area, no attempts have been made to leverage the dynamical nature of musical works coupled with the modern advances in machine learning such as metric learning for content-based music identification. In this paper, we propose a novel approach that encodes the dynamic nature of musical performances into the latent space of a sequence-to-sequence auto-encoder network. The learning objective is further enforced with the metric learning for music similarity measurement. The proposed model is extensively evaluated by testing it with 14 distortions of the same musical performance. The experimental results demonstrate a substantial increase of 31.71% in hit-rate over the baseline established using related work found in the literature. These findings highlight the potential of our approach to significantly improve content-based music identification, thereby offering promising applications in various practical scenarios.", "Keywords": "", "DOI": "10.1051/itmconf/20246000007", "PubYear": 2024, "Volume": "60", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Colombo School of Computing, 35, Reid Avenue, Colombo, Sri Lanka"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Colombo School of Computing, 35, Reid Avenue, Colombo, Sri Lanka"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Colombo School of Computing, 35, Reid Avenue, Colombo, Sri Lanka"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Colombo School of Computing, 35, Reid Avenue, Colombo, Sri Lanka"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Colombo School of Computing, 35, Reid Avenue, Colombo, Sri Lanka"}], "References": []}, {"ArticleId": 112097784, "Title": "Is There Another System?", "Abstract": "<p>One of the easiest tests to determine if you are at risk is to look hard at what you do every day and see if you, yourself, could code yourself out of a job. Programming involves a lot of rote work: templating, boilerplate, and the like. If you can see a way to write a system to replace yourself, either do it, don't tell your bosses, and collect your salary while reading novels in your cubicle, or look for something more challenging to work on.</p>", "Keywords": "", "DOI": "10.1145/3639446", "PubYear": 2023, "Volume": "21", "Issue": "6", "JournalId": 22497, "JournalTitle": "Queue", "ISSN": "1542-7730", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 112097850, "Title": "Modeling the Flood Disaster in South Kalimantan Using Geographically Weighted Regression and Mixed Geographically Weighted Regression", "Abstract": "The flood disaster in South Kalimantan is a crucial problem that needs to be addressed because the impact is relatively severe. So, this study aims to model flood disasters in South Kalimantan based on factors suspected to be the cause, including population density, rainfall, residential area, and forest area. This study uses two methods of spatial statistics, namely the Geographically Weighted Regression (GWR) and Mixed Geographically Weighted Regression (MGWR) methods. The weighting used is Adaptive Gaussian. The modeling results show that the GWR model is superior in explaining the causes of flood events in South Kalimantan, which is indicated by the highest coefficient of determination value of 95.62% compared to the regression and MGWR models. Nonetheless, the MGWR model can explain the causes of flooding in Kalimantan. The GWR and MGWR models show that the area that is vulnerable to flooding is Balangan District. The results of this study contribute to providing alternative information for disaster mitigation to minimize losses.", "Keywords": "", "DOI": "10.1051/itmconf/20245804004", "PubYear": 2024, "Volume": "58", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science and Technology, UIN Sunan Ampel Surabaya, East Java, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science and Technology, UIN Sunan Ampel Surabaya, East Java, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science and Technology, UIN Sunan Ampel Surabaya, East Java, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science and Technology, UIN Sunan Ampel Surabaya, East Java, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science and Technology, UIN Sunan Ampel Surabaya, East Java, Indonesia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science and Technology, UIN Sunan Ampel Surabaya, East Java, Indonesia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science and Technology, UIN Sunan Ampel Surabaya, East Java, Indonesia"}], "References": [{"Title": "Estimation of Extreme Rainfall Patterns Using Generalized Linear Mixed Model for Spatio-temporal data in West Java, Indonesia", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "330", "JournalTitle": "Procedia Computer Science"}, {"Title": "The coefficient of determination R-squared is more informative than SMAPE, MAE, MAPE, MSE and RMSE in regression analysis evaluation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "e623", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 112097862, "Title": "RimSense", "Abstract": "<p>Smart eyewear's interaction mode has attracted significant research attention. While most commercial devices have adopted touch panels situated on the temple front of eyeglasses for interaction, this paper identifies a drawback stemming from the unparalleled plane between the touch panel and the display, which disrupts the direct mapping between gestures and the manipulated objects on display. Therefore, this paper proposes RimSense, a proof-of-concept design for smart eyewear, to introduce an alternative realm for interaction - touch gestures on eyewear rim. RimSense leverages piezoelectric (PZT) transducers to convert the eyeglass rim into a touch-sensitive surface. When users touch the rim, the alteration in the eyeglass's structural signal manifests its effect into a channel frequency response (CFR). This allows RimSense to recognize the executed touch gestures based on the collected CFR patterns. Technically, we employ a buffered chirp as the probe signal to fulfil the sensing granularity and noise resistance requirements. Additionally, we present a deep learning-based gesture recognition framework tailored for fine-grained time sequence prediction and further integrated with a Finite-State Machine (FSM) algorithm for event-level prediction to suit the interaction experience for gestures of varying durations. We implement a functional eyewear prototype with two commercial PZT transducers. RimSense can recognize eight touch gestures on the eyeglass rim and estimate gesture durations simultaneously, allowing gestures of varying lengths to serve as distinct inputs. We evaluate the performance of RimSense on 30 subjects and show that it can sense eight gestures and an additional negative class with an F1-score of 0.95 and a relative duration estimation error of 11%. We further make the system work in real-time and conduct a user study on 14 subjects to assess the practicability of RimSense through interactions with two demo applications. The user study demonstrates RimSense's good performance, high usability, learnability and enjoyability. Additionally, we conduct interviews with the subjects, and their comments provide valuable insight for future eyewear design.</p>", "Keywords": "", "DOI": "10.1145/3631456", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSE, Hong Kong Univ. of Science and Technology, Hong Kong and Southern Univ. of Science and Technology, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IoT Thrust, Information Hub, Hong Kong University of Science and Technology (Guangzhou), Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CIS, University of Melbourne, Melbourne, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shenzhen Key Laboratory of Safety and Security for Next Generation of Industrial Internet, CSE, Southern University of Science and Technology, Shenzhen, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "CSE, Hong Kong University of Science and Technology, Hong Kong"}], "References": [{"Title": "Acoustic-based Upper Facial Action Recognition for Smart Eyewear", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "StretchAR", "Authors": "<PERSON>; <PERSON><PERSON>; Juan <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": *********, "Title": "Stability of convex linear combinations of continuous-time and discrete-time linear systems", "Abstract": "The asymptotic stability of the convex linear combination of continuous-time and discretetime linear systems is considered. Using the <PERSON><PERSON><PERSON><PERSON><PERSON> theorem it is shown that the convex linear combination of the linear asymptotically stable continuous-time and discretetime linear systems is also asymptotically stable. It is shown that the above thesis is also valid (even simpler) for positive linear systems. Copyright © 2023. The Author(s).", "Keywords": "convex linear combination;linear system;continuous-time;discrete-time;positive;stability", "DOI": "10.24425/acs.2023.148881", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 30654, "JournalTitle": "Archives of Control Sciences", "ISSN": "1230-2384", "EISSN": "2300-2611", "Authors": [], "References": []}, {"ArticleId": 112097962, "Title": "Visual analytics of soccer player performance using objective ratings", "Abstract": "<p>The performance of soccer players is commonly rated by soccer experts for each match as well as over a tournament or during a season/year. However, these ratings are mostly subjective. We instead propose a visual analytics approach for a more objective, data-driven analysis of soccer players’ performances. We introduce data-driven ratings for various aspects, which can be combined by interactively assigning weights to compute an overall score as well as individual scores for passes, duels, and shots. Our tool supports comparative visualizations at a global level that can be adapted to different analysis tasks as well as in-detail analyses of individual events of the game. We apply our approach to data gathered during the 2020 UEFA European Football Championship and perform in-detail analyses of individual players in selected matches.</p>", "Keywords": "", "DOI": "10.1177/14738716231220539", "PubYear": 2024, "Volume": "23", "Issue": "2", "JournalId": 7733, "JournalTitle": "Information Visualization", "ISSN": "1473-8716", "EISSN": "1473-8724", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Evers", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Westfälische Wilhelms-Universität Münster, Münster, Germany"}], "References": []}, {"ArticleId": 112098047, "Title": "Cluster and trajectory analysis of motivation in an emergency remote programming course", "Abstract": "<p>Emergency remote teaching is a temporary change in the way education occurs, whereby an educational system unexpectedly becomes entirely remote. This article analyzes the motivation of students undertaking a university course over one semester of emergency remote teaching in the context of the COVID-19 pandemic. University students undertaking a programming course were surveyed three times during one semester, about motivation and COVID concern. This work explores which student motivation profiles existed, how motivation evolved, and whether concern about the pandemic was a factor affecting motivation throughout the course. The most adaptive profile was highly motivated, more prepared and less frustrated by the conditions of the course. However, this cluster experienced the highest levels of COVID-19 concern. The least adaptive cluster behaved as a mirror image of the most adaptive cluster. Clear differences were found between the clusters that showed the most and least concern about COVID-19.</p>", "Keywords": "Computer education;Emergency remote course;Motivation;Programming", "DOI": "10.7717/peerj-cs.1787", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Pontificia Universidad Católica de Chile, Santiago, Chile"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Pontificia Universidad Católica de Chile, Santiago, Chile"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Pontificia Universidad Católica de Chile, Santiago, Chile"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Escuela de Gobierno, Universidad Adolfo Ibáñez, Santiago, Chile"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Pontificia Universidad Católica de Chile, Santiago, Chile"}], "References": [{"Title": "Computer Science Students’ Perceptions of Emergency Remote Teaching: An Experience Report", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "5", "Page": "378", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 112098188, "Title": "Research on Improving Customer Satisfaction of Hotel Robot Service Based on Web Text Analysis", "Abstract": "With the gradual development of artificial intelligence, the hotel has fully applied robot services in the daily operation of the hotel. Robot services can help guests quickly self-check-in, room delivery and contactless delivery of takeout, greatly enhancing the customer’s hotel experience. This paper specifically consulted literature to comprehensively understand the relevant knowledge of hotel robot service and customer satisfaction, and then used network text analysis to capture a large number of hotel customer reviews of JI Hotel, and through further data processing and analysis, understood the initial impression of customers on service robots. According to the problems existing in the robot service of the JI Hotel, the reasonable and easy to use customer satisfaction improvement strategies and suggestions are summarized. It is expected to provide reference for future research on hotel robot service.", "Keywords": "", "DOI": "10.1051/itmconf/***********", "PubYear": 2024, "Volume": "60", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Shandong Technology and Business University, Yantai 264005, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Shandong Technology and Business University, Yantai 264005, China"}], "References": []}, {"ArticleId": 112098215, "Title": "MIRROR", "Abstract": "<p>We present MIRROR, an on-device video virtual try-on (VTO) system that provides realistic, private, and rapid experiences in mobile clothes shopping. Despite recent advancements in generative adversarial networks (GANs) for VTO, designing MIRROR involves two challenges: (1) data discrepancy due to restricted training data that miss various poses, body sizes, and backgrounds and (2) local computation overhead that uses up 24% of battery for converting only a single video. To alleviate the problems, we propose a generalizable VTO GAN that not only discerns intricate human body semantics but also captures domain-invariant features without requiring additional training data. In addition, we craft lightweight, reliable clothes/pose-tracking that generates refined pixel-wise warping flow without neural-net computation. As a holistic system, MIRROR integrates the new VTO GAN and tracking method with meticulous pre/post-processing, operating in two distinct phases (on/offline). Our results on Android smartphones and real-world user videos show that compared to a cutting-edge VTO GAN, MIRROR achieves 6.5× better accuracy with 20.1× faster video conversion and 16.9× less energy consumption.</p>", "Keywords": "", "DOI": "10.1145/3631420", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Seoul National University, Seoul, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Seoul National University, Seoul, Republic of Korea"}, {"AuthorId": 3, "Name": "Sungwook Son", "Affiliation": "Seoul National University, Seoul, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Seoul National University, Seoul, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nokia Bell Labs, Cambridge, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Seoul National University, Seoul, Republic of Korea"}], "References": []}, {"ArticleId": 112098223, "Title": "ClearSpeech", "Abstract": "<p>Wireless earbuds have been gaining increasing popularity and using them to make phone calls or issue voice commands requires the earbud microphones to pick up human speech. When the speaker is in a noisy environment, speech quality degrades significantly and requires speech enhancement (SE). In this paper, we present ClearSpeech, a novel deep-learning-based SE system designed for wireless earbuds. Specifically, by jointly using the earbud's in-ear and out-ear microphones, we devised a suite of techniques to effectively fuse the two signals and enhance the magnitude and phase of the speech spectrogram. We built an earbud prototype to evaluate ClearSpeech under various settings with data collected from 20 subjects. Our results suggest that ClearSpeech can improve the SE performance significantly compared to conventional approaches using the out-ear microphone only. We also show that ClearSpeech can process user speech in real-time on smartphones.</p>", "Keywords": "", "DOI": "10.1145/3631409", "PubYear": 2023, "Volume": "7", "Issue": "4", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Singapore Management University, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Nokia Bell Labs, Cambridge, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Data61, CSIRO, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Singapore Management University, Singapore"}], "References": [{"Title": "Quantifying the Causal Effect of Individual Mobility on Health Status in Urban Space", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": *********, "Title": "An intelligent healthcare monitoring system-based novel deep learning approach for detecting covid-19 from x-rays images", "Abstract": "<p>This paper aims to address the detection of COVID-19 by developing an accurate and efficient diagnostic system using chest X-ray images. The research utilizes open-source Kaggle data comprising four categories: COVID-19, Lung-Opacity, Normal, and Viral Pneumonia. The proposed system employs convolutional neural networks (CNNs), including VGG19, RNN-LSTM, and inceptionv3. Results vary among the methodologies, with VGG19 achieving 26% accuracy, RNN-LSTM attaining 25% accuracy (28% with preprocessing), and inceptionv3 with histogram equalization achieving 83% accuracy. A CNN designed from scratch demonstrates the highest performance, with an accuracy of 93% (96% with histogram equalization). The findings emphasize the potential of AI techniques in enhancing disease diagnosis, particularly in distinguishing COVID-19 from other conditions, thereby facilitating timely and effective interventions.</p>", "Keywords": "Classification; Deep learning; Intelligent diagnosing; COVID-19; Lung disease diagnosis; Computer added diagnosis; Smart medicine", "DOI": "10.1007/s11042-023-18056-0", "PubYear": 2024, "Volume": "83", "Issue": "23", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science and IT, Al-Zaytoonah University of Jordan, Amman, Jordan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science and IT, Al-Zaytoonah University of Jordan, Amman, Jordan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science and IT, Al-Zaytoonah University of Jordan, Amman, Jordan; Ministry of Youth and Sports, Nineveh Youth and Sports Directorate, Mosul, Iraq"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Faculty of Prince <PERSON><PERSON><PERSON><PERSON><PERSON> for IT, The Hashemite University, Zarqa, Jordan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Artificial Intelligence and Sensing Technologies (AIST) Research Center, University of Tabuk, Tabuk, Saudi Arabia; Hourani Center for Applied Scientific Research, Al-Ahliyya Amman University, Amman, Jordan; MEU Research Unit, Middle East University, Amman, Jordan; Department of Electrical and Computer Engineering, Lebanese American University, Byblos, Lebanon; Corresponding author."}], "References": [{"Title": "Parallel implementation for 3D medical volume fuzzy segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "312", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Efficient 3D medical image segmentation algorithm over a secured multimedia network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "11", "Page": "16887", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Probabilistic Deep Q Network for real-time path planning in censorious robotic procedures using force sensors", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "5", "Page": "1773", "JournalTitle": "Journal of Real-Time Image Processing"}]}, {"ArticleId": 112098384, "Title": "A review of object detection: Datasets, performance evaluation, architecture, applications and current trends", "Abstract": "<p>Object detection is one of the most important and challenging branches of computer vision, whose main task is to classify and localize objects in images or videos. The development of object detection technology has been more than 20 years, from the early traditional detection methods to the current deep learning methods, the improvement of object detection accuracy and speed stems from the rapid development of deep learning technology. Traditional object detection techniques have many limitations, and using convolutional neural networks as the main framework for object detection can efficiently extract features and reduce the complexity of manual feature extraction. To comprehensively and deeply understand the development status of object detection, based on the research of domestic and foreign related literature, this paper reviews the research background of object detection, introduces the problems and dilemmas faced by traditional object detection algorithms, and analyzes the current mainstream object detection algorithms. This paper mainly carries out the relevant algorithms from three perspectives: Anchor-based, Anchor-free, and Transformer-based, and summarizes their structure, performance, advantages, and disadvantages in detail. This paper also introduces the commonly used datasets and related performance evaluation indexes for object detection, as well as the applications of object detection in industrial, transportation, medical, and other fields. According to the current research hotspots and the development trend of related technologies, the future research direction of object detection is prospected.</p>", "Keywords": "Object detection; Deep learning; Convolutional neural network; Feature extraction; Transformer", "DOI": "10.1007/s11042-023-17949-4", "PubYear": 2024, "Volume": "83", "Issue": "24", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, China University of Mining and Technology (Beijing), Beijing, China; Key Laboratory of Intelligent Mining and Robotics, Ministry of Emergency Management, Beijing, China; School of Computer Science & Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 2, "Name": "Jinjin Luo", "Affiliation": "School of Artificial Intelligence, China University of Mining and Technology (Beijing), Beijing, China; Key Laboratory of Intelligent Mining and Robotics, Ministry of Emergency Management, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, China University of Mining and Technology (Beijing), Beijing, China; Key Laboratory of Intelligent Mining and Robotics, Ministry of Emergency Management, Beijing, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, China University of Mining and Technology (Beijing), Beijing, China; Key Laboratory of Intelligent Mining and Robotics, Ministry of Emergency Management, Beijing, China"}], "References": [{"Title": "CornerNet: Detecting Objects as Paired Keypoints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "3", "Page": "642", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Feature agglomeration networks for single stage face detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "380", "Issue": "", "Page": "180", "JournalTitle": "Neurocomputing"}, {"Title": "A comparative study of features selection for skin lesion detection from dermoscopic images", "Authors": "<PERSON><PERSON> Javed; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Network Modeling Analysis in Health Informatics and Bioinformatics"}, {"Title": "UA-DETRAC: A new benchmark and protocol for multi-object detection and tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "193", "Issue": "", "Page": "102907", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "The Open Images Dataset V4", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "7", "Page": "1956", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "PVT v2: Improved baselines with Pyramid Vision Transformer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "415", "JournalTitle": "Computational Visual Media"}, {"Title": "Center and Scale Prediction: Anchor-free Approach for Pedestrian and Face Detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109071", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 112098408, "Title": "Navigating Human-Chatbot Interactions: An Investigation into Factors Influencing User Satisfaction and Engagement", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2023.2301252", "PubYear": 2025, "Volume": "41", "Issue": "1", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Newcastle, Newcastle Upon Tyne, UK"}], "References": [{"Title": "Understanding automated conversational agent as a decision aid: matching agent's conversation with customer's shopping task", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "4", "Page": "1376", "JournalTitle": "Internet Research"}, {"Title": "Exploring consumers' response to text-based chatbots in e-commerce: the moderating role of task complexity and chatbot disclosure", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "2", "Page": "496", "JournalTitle": "Internet Research"}, {"Title": "Artificial intelligent chatbots as brand promoters: a two-stage structural equation modeling-artificial neural network approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "4", "Page": "1329", "JournalTitle": "Internet Research"}, {"Title": "Calming the customers by AI: Investigating the role of chatbot acting-cute strategies in soothing negative customer emotions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "4", "Page": "2277", "JournalTitle": "Electronic Markets"}, {"Title": "Can AI chatbots help retain customers? Impact of AI service quality on customer loyalty", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "33", "Issue": "6", "Page": "2205", "JournalTitle": "Internet Research"}, {"Title": "Emotion-regulatory chatbots for enhancing consumer servicing: An interpersonal emotion management approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "60", "Issue": "5", "Page": "103794", "JournalTitle": "Information & Management"}]}, {"ArticleId": *********, "Title": "Robust fuzzy logic schemes for cooperative spectrum sharing in 5G networks with uncertain channel conditions", "Abstract": "The rising use of wireless interfaces necessitates effective spectral reuse, addressed here through a novel Fuzzy Logic-inspired Enhanced Reinforced Learning (FL-eRL) technique. This paper explores robust spectrum access within wireless sharing networks, leveraging fuzzy-game strategies against routing uncertainties. Decision-making spaces are modelled as fuzzy-logical areas, using fuzzy values to represent ambiguous data, enhancing resilience to incentive variability, and maintaining network speed amidst uncertainties. This approach enables controllers to make informed, collective decisions through fuzzy analysis. Validated through rigorous testing in variable conditions, this technique promises significant advancements for next-generation communication systems in volatile settings, underscoring its pivotal role in future network management.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2023.109060", "PubYear": 2024, "Volume": "114", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "NITTE (Deemed to be University), NMAM Institute of Technology (NMAMIT), Department of Electronics and Communication Engineering, NITTE, Karnataka, 574110, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Design, Karpagam college of Engineering, Coimbatore, Tamil Nadu, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Koneru Lakshm<PERSON>h Education Foundation, Green Fields, Vaddeswaram, Guntur Dist 522502, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of C. S. E, Aditya College of Engineering &Technology, Surampalem, Peddapuram, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ISE, FET CAMPUS, <PERSON> to be University, Ramnagara District, Bangalore 562112, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Mangalm College of Engineering, Kottayam, Kerala, India"}], "References": [{"Title": "A novel framework paradigm for EMR management cloud system authentication using blockchain security network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 112098423, "Title": "Phase transitions in the mini-batch size for sparse and dense two-layer neural networks", "Abstract": "The use of mini-batches of data in training artificial neural networks is nowadays very common. Despite its broad usage, theories explaining quantitatively how large or small the optimal mini-batch size should be are missing. This work presents a systematic attempt at understanding the role of the mini-batch size in training two-layer neural networks. Working in the teacher-student scenario, with a sparse teacher, and focusing on tasks of different complexity, we quantify the effects of changing the mini-batch size m . We find that often the generalization performances of the student strongly depend on m and may undergo sharp phase transitions at a critical value \n \n \n \n m \n \n c \n \n \n \n \n , such that for \n \n \n m \n < \n \n m \n \n c \n \n \n \n \n the training process fails, while for \n \n \n m \n > \n \n m \n \n c \n \n \n \n \n the student learns perfectly or generalizes very well the teacher. Phase transitions are induced by collective phenomena firstly discovered in statistical mechanics and later observed in many fields of science. Observing a phase transition by varying the mini-batch size across different architectures raises several questions about the role of this hyperparameter in the neural network learning process.", "Keywords": "", "DOI": "10.1088/2632-2153/ad1de6", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 72803, "JournalTitle": "Machine Learning: Science and Technology", "ISSN": "", "EISSN": "2632-2153", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dipartimento di Fisica, Sapienza Università di Roma, Piazzale Aldo Moro 5, 00185 Roma, Italy; Dipartimento di Fisica e Astronomia, Università degli studi di Firenze, Via <PERSON> Sanson<PERSON> 1, <PERSON><PERSON>, 50019 Firenze, Italy; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dipartimento di Fisica, Sapienza Università di Roma, Piazzale Aldo Moro 5, 00185 Roma, Italy; CNR-Nanotec and INFN, Sezione di Roma1, Piazzale Aldo Moro 5, 00185 Roma, Italy"}], "References": [{"Title": "Learning from survey propagation: a neural network for MAX-E-3-SAT", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "035032", "JournalTitle": "Machine Learning: Science and Technology"}, {"Title": "A new iterative technique for solving fractal-fractional differential equations based on artificial neural network in the new generalized Caputo sense", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "1", "Page": "505", "JournalTitle": "Engineering with Computers"}, {"Title": "Deep learning via message passing algorithms based on belief propagation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "3", "Page": "035005", "JournalTitle": "Machine Learning: Science and Technology"}, {"Title": "Solving Non-linear Kolmogorov Equations in Large Dimensions by Using Deep Learning: A Numerical Comparison of Discretization Schemes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "94", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "Learning curves for the multi-class teacher–student perceptron", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "1", "Page": "015019", "JournalTitle": "Machine Learning: Science and Technology"}]}, {"ArticleId": 112098457, "Title": "GAL: combining global and local contexts for interpersonal relation extraction toward document-level Chinese text", "Abstract": "<p>Current interpersonal relation extraction toward Chinese text remains at the sentence-level, which narrows practical applications since most relational facts are implied through multiple sentences in the document. Moreover, considering huge differences between the Chinese and English languages (e.g., different ways of word segmentation and expression preferences), it is impractical to directly adopt models designed for English corpus to model Chinese implicit semantics. To address these challenges, we propose a novel model called GAL (global and local) to comprehend the Chinese context for interpersonal relation extraction at the document-level. Specifically, we devise the global and the local representation modules to encode the entity pair’s relation, perceiving related content from multi-perspectives. For the global representation module, we first design a heterogeneous graph comprised of the document node, entity nodes, and sentence nodes; then, we perform reasoning on it to obtain the global feature via R-GCN. For the local representation module, the robust hierarchical attention is conceived to capture the local semantic features from entity pair’s co-occurrence sentences. Finally, we concatenate the global and local representations for classification. Moreover, due to the lack of an open-source dataset, we employ the distant supervision method to construct the Chinese interpersonal relation extraction dataset at the document-level, DocIPRE. Various experimental results validate the better modeling ability of GAL as it achieves 54.55% on the F1 score, outperforming the state-of-the-art method by a significant margin (1.34% on F1). The source code and dataset will be available soon.</p>", "Keywords": "Chinese interpersonal relation extraction; Distant supervision; Robust hierarchical attention; Heterogeneous graph; Graph convolutional network", "DOI": "10.1007/s00521-023-09336-9", "PubYear": 2024, "Volume": "36", "Issue": "11", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Jiawei Ge", "Affiliation": "School of Cyber Science and Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Southeast University, Nanjing, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Southeast University, Nanjing, China"}], "References": []}]