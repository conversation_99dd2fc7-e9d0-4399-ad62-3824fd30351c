[{"ArticleId": 96000544, "Title": "A method for protecting neural networks from computer backdoor attacks based on the trigger identification", "Abstract": "Modern technologies for the development and operation of neural networks are vulnerable to computer attacks with the introduction of software backdoors. Program backdoors can remain hidden indefinitely until activated by input of modified data containing triggers. These backdoors pose a direct threat to the security of information for all components of the artificial intelligence system. Such influences of intruders lead to a deterioration in the quality or complete cessation of the functioning of artificial intelligence systems. This paper proposes an original method for protecting neural networks, the essence of which is to create a database of ranked synthesized backdoor’s triggers of the target class of backdoor attacks. The proposed method for protecting neural networks is implemented through a sequence of protective actions: detecting a backdoor, identifying a trigger, and neutralizing a backdoor. Based on the proposed method, software and algorithmic support for testing neural networks has been developed that allows you to identify and neutralize computer backdoor attacks. Experimental studies have been carried out on various dataset-trained convolutional neural network architectures for objects such as aerial photographs (DOTA), handwritten digits (MNIST), and photographs of human faces (LFW). The decrease in the effectiveness of backdoor attacks (no more than 3 %) and small losses in the quality of the functioning of neural networks (by 8–10 % of the quality of the functioning of a neural network without a backfill) showed the success of the developed method. The use of the developed method for protecting neural networks allows information security specialists to purposefully counteract computer backdoor attacks on artificial intelligence systems and develop automated information protection tools. © Menisov A.B., Lomako A.G., Dudkin A.S., 2022.", "Keywords": "artificial intelligence; artificial neural network; information security; computer attacks; backdoor; backdoors in neural\r\nnetworks; synthesized triggers", "DOI": "10.17586/**************-22-4-742-750", "PubYear": 2022, "Volume": "22", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mozhaisky Military Aerospace Academy, Saint Petersburg, 197198, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mozhaisky Military Aerospace Academy, Saint Petersburg, 197198, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mozhaisky Military Aerospace Academy, Saint Petersburg, 197198, Russian Federation"}], "References": []}, {"ArticleId": 96000561, "Title": "Comprehensive review on time series motif discovery using evolutionary techniques", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2022.10050297", "PubYear": 2022, "Volume": "23", "Issue": "1/2", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96000569, "Title": "ReFRS: Resource-efficient Federated Recommender System for Dynamic and Diversified User Preferences", "Abstract": "<p> Owing to its nature of scalability and privacy by design, federated learning (FL) has received increasing interest in decentralized deep learning. FL has also facilitated recent research on upscaling and privatizing personalized recommendation services, using on-device data to learn recommender models locally. These models are then aggregated globally to obtain a more performant model while maintaining data privacy. Typically, federated recommender systems (FRSs) do not take into account the lack of resources and data availability at the end-devices. In addition, they assume that the interaction data between users and items is i.i.d. and stationary across end-devices (i.e., users), and that all local recommender models can be directly averaged without considering the user’s behavioral diversity. However, in real scenarios, recommendations have to be made on end-devices with sparse interaction data and limited resources. Furthermore, users’ preferences are heterogeneous and they frequently visit new items. This makes their personal preferences highly skewed, and the straightforwardly aggregated model is thus ill-posed for such non-i.i.d. data. In this article, we propose Resource Efficient Federated Recommender System (ReFRS) to enable decentralized recommendation with dynamic and diversified user preferences. On the device side, ReFRS consists of a lightweight self-supervised local model built upon the variational autoencoder for learning a user’s temporal preference from a sequence of interacted items. On the server side, ReFRS utilizes a scalable semantic sampler to adaptively perform model aggregation within each identified cluster of similar users. The clustering module operates in an asynchronous and dynamic manner to support efficient global model update and cope with shifting user interests. As a result, ReFRS achieves superior performance in terms of both accuracy and scalability, as demonstrated by comparative experiments on real datasets. </p>", "Keywords": "", "DOI": "10.1145/3560486", "PubYear": 2023, "Volume": "41", "Issue": "3", "JournalId": 12861, "JournalTitle": "ACM Transactions on Information Systems", "ISSN": "1046-8188", "EISSN": "1558-2868", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Queensland, Brisbane, QLD, Australia"}, {"AuthorId": 2, "Name": "Hong<PERSON> Yin", "Affiliation": "The University of Queensland, Brisbane, QLD, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The University of Queensland, Brisbane, QLD, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Griffith University, Gold Coast, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Hong Kong, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Electronic Science and Technology of China, Chengdu, Sichuan, China"}], "References": [{"Title": "Exploiting Cross-session Information for Session-based Recommendation with Graph Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "A survey on federated learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106775", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 96000628, "Title": "Development of Classification Model for the Prediction of Churn among Customers using Decision tree Algorithm", "Abstract": "<p>No Abstract.</p>", "Keywords": "", "DOI": "10.4314/jcsia.v28i2.5", "PubYear": 2022, "Volume": "28", "Issue": "2", "JournalId": 29552, "JournalTitle": "Journal of Computer Science and Its Application", "ISSN": "2006-5523", "EISSN": "2006-5523", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Funmilayo A. <PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Ibidapo <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96000640, "Title": "Extension of GRA method for multiattribute group decision making problem under linguistic Pythagorean fuzzy setting with incomplete weight information", "Abstract": "<p>Linguistic Pythagorean fuzzy numbers (LPFNs) are better tools for dealing with imprecision and vagueness. This article develops a new multiattribute group decision-making approach with LPFNs. The attribute values are LPFNs, and the information about the attribute weight is incomplete. Extended the notion of the traditional grey relational analysis (GRA) method, a new extension of the GRA method based on LPFN details is introduced. We develop a new distance measure and entropy measure for LPFNs. Moreover, a decision-making approach is proposed based on the traditional. GRA method and steps for solving LPFMAGDM problems with incompletely known weight information are given. The degree of grey relation between positive (PIS) and negative-ideal solutions (NIS) are determined. A relative relational degree is considered by calculating the degree of grey relation to the LPF-PIS and the LPF-NIS, respectively. Finally, an illustrative example is also given to show the application and effectiveness and compare the developed approach with existing methods.</p>", "Keywords": "distance measure;entropy measure;GRA method;linguistic Pythagorean fuzzy set;MAGDM problem", "DOI": "10.1002/int.23003", "PubYear": 2022, "Volume": "37", "Issue": "11", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics K<PERSON>hal Khan Khattak University Karak KPK Pakistan"}, {"AuthorId": 2, "Name": "Chiranjibe Jana", "Affiliation": "Department of Applied Mathematics with Oceanology and Computer Programming Vidyasagar University Midnapore India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics Quaid‐i‐Azam University Islamabad Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics Quaid‐i‐Azam University Islamabad Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics with Oceanology and Computer Programming Vidyasagar University Midnapore India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Numerical Sciences Kohat University of Science & Technology Kohat Pakistan"}], "References": [{"Title": "A dynamical hybrid method to design decision making process based on GRA approach for multiple attributes problem", "Authors": "Chiranjibe Jana; Madhumangal Pal", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "104203", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 96000649, "Title": "iKS2 An improved Kernel and Set Similarity Measure for Fuzzy Clustering of Seqential Data", "Abstract": "<p>No Abstract.</p>", "Keywords": "", "DOI": "10.4314/jcsia.v28i2.15", "PubYear": 2022, "Volume": "28", "Issue": "2", "JournalId": 29552, "JournalTitle": "Journal of Computer Science and Its Application", "ISSN": "2006-5523", "EISSN": "2006-5523", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96000655, "Title": "A Pilot Implementation of a Remote IoT Sensors for Aquaponics System Datasets Acquisition", "Abstract": "<p>No Abstract.</p>", "Keywords": "", "DOI": "10.4314/jcsia.v28i2.1", "PubYear": 2022, "Volume": "28", "Issue": "2", "JournalId": 29552, "JournalTitle": "Journal of Computer Science and Its Application", "ISSN": "2006-5523", "EISSN": "2006-5523", "Authors": [{"AuthorId": 1, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "B.<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "P.<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "U.K. Ome", "Affiliation": ""}], "References": []}, {"ArticleId": 96000696, "Title": "Predictive Analysis of Customer Retention Using the Random Forest Algorithm", "Abstract": "<p>Retaining customers is becoming a measurement focus in an industry with increasing competition. The concept of customer retention has become a research study in the sales industry, because it is difficult to retain customers and easily switch to other brands. Customer repurchase decisions in the business world of sales are very competitive. Customer satisfaction is directly proportional to the retention rate, if the customer is not satisfied then the automatic retention rate will be low. If the company is not able to meet customer expectations, it will have a serious impact on the company, namely moving customers to other services. Service factors, price, profit value, satisfaction and trust affect customer retention. One of the factors that influence consumers to become customer retention is service quality. A predictive customer retention plan is needed with data mining using the random forest algorithm. The random forest algorithm is a method that generates a number of trees from sample data, where the creation of one tree during training does not depend on the previous tree, the decision is based on the most voting. The voting results from several decision trees that are formed are the boundaries that are used as class determination in the classification process and the most votes are the winners and determine the classification class. This study aims to determine and analyze customer loyalty, customer trust and customer satisfaction. So that it can make it easier to monitor customers at the company. The results can be seen with the percentage of about 81.12% customer retention and about 18.87% customer churn. The result of feature evaluation shows that customer_activity has the highest influence on customer retention, followed by subtotal and qty.</p>", "Keywords": "<PERSON><PERSON><PERSON>; Customer; <PERSON><PERSON>; Retensi p<PERSON>gan; Random forest; Service quality.", "DOI": "10.38043/tiers.v3i1.3616", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 94001, "JournalTitle": "TIERS Information Technology Journal", "ISSN": "2723-4533", "EISSN": "2723-4541", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut Teknologi Dan Bisnis Swdharma"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institut Teknologi dan <PERSON><PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institut Teknologi Dan Bisnis Swdharma"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institut Teknologi dan <PERSON><PERSON>"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institut Teknologi Dan Bisnis Swdharma"}], "References": []}, {"ArticleId": 96000772, "Title": "Internet use and cyberbullying: Impacts on psychosocial and psychosomatic wellbeing among Chinese adolescents", "Abstract": "The use of the internet for entertainment has increased hugely over the last decade among Chinese adolescents, but the psychosocial impacts remain unclear. The aims of this study are to explore the associations between internet use, cyberbullying and psychosocial wellbeing among Chinese adolescents. Questionnaires were completed in the classroom setting by 3378 middle school students aged 11–16 years old (M = 13.58, SD = 0.87) in three provinces representing eastern, central and western China. Key findings included: 1) Internet use of over 3 h per day was associated with higher prevalence of anxiety [OR = 1.6, 95% CI (1.1, 2.2), p  = 0.006], depression [OR = 2.1, 95% CI (1.7, 2.6), p  &lt; 0.001] and psychosomatic health problems, such as abdominal pain [OR = 2.4, 95% CI (1.8, 3.3), p  &lt; 0.001]. 2) Boys were much more likely to play online games. 3) Moderate time of gaming was overall beneficial to well-being. 4) Cyberbullying was common, with 37.5% admitting involvement. 5) Bully-victims were most vulnerable to mental and psychosomatic health problems, and only-bullies were the least vulnerable group. Our findings suggest moderate internet use for entertainment is not detrimental to mental health, but excessive use is. Schools should promote adolescents’ responsible use of the internet and incorporate anti -cyberbullying programs into the curriculum.", "Keywords": "Internet use for entertainment ; Cyberbullying ; Psychosocial well-being ; Psychosomatic illness", "DOI": "10.1016/j.chb.2022.107461", "PubYear": 2023, "Volume": "138", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "Jiameng Li", "Affiliation": "Centre for Global Health, Zhejiang University School of Medicine 866 Yuhangtang Road, Xihu District, Hangzhou, Zhejiang Province, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Global Health, Zhejiang University School of Medicine 866 Yuhangtang Road, Xihu District, Hangzhou, Zhejiang Province, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Global Health, Zhejiang University School of Medicine 866 Yuhangtang Road, Xihu District, Hangzhou, Zhejiang Province, PR China;The Institute for Global Health, UCL 30 Guilford St, London WC1NEH, UK;Corresponding author. The Institute for Global Health, UCL 30 Guilford St, London, WC1NEH, UK"}], "References": []}, {"ArticleId": 96000858, "Title": "Modelling the perception of music in brain network dynamics", "Abstract": "<p>We analyze the influence of music in a network of FitzHugh-Nagumo oscillators with empirical structural connectivity measured in healthy human subjects. We report an increase of coherence between the global dynamics in our network and the input signal induced by a specific music song. We show that the level of coherence depends crucially on the frequency band. We compare our results with experimental data, which also describe global neural synchronization between different brain regions in the gamma-band range in a time-dependent manner correlated with musical large-scale form, showing increased synchronization just before transitions between different parts in a musical piece (musical high-level events). The results also suggest a separation in musical form-related brain synchronization between high brain frequencies, associated with neocortical activity, and low frequencies in the range of dance movements, associated with interactivity between cortical and subcortical regions.</p>", "Keywords": "synchronization; Coupled oscillators; neuronal network dynamics; pattern formation: activity and anatomic; external driven", "DOI": "10.3389/fnetp.2022.910920", "PubYear": 2022, "Volume": "2", "Issue": "", "JournalId": 89151, "JournalTitle": "Frontiers in Network Physiology", "ISSN": "", "EISSN": "2674-0109", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Potsdam Institute for Climate Impact Research, Germany; Institut für Musikpädagogik, Germany; Fachhochschule Nordwestschweiz FHNW, Switzerland; Institut für Theoretische Physik, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Systematic Musicology, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Systematic Musicology, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> ", "Affiliation": "Potsdam Institute for Climate Impact Research, Germany; Institut für Theoretische Physik, Germany; Bernstein Center for Computational Neuroscience Berlin, Germany"}], "References": [{"Title": "Criticality in the Healthy Brain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "1", "Issue": "", "Page": "21", "JournalTitle": "Frontiers in Network Physiology"}]}, {"ArticleId": 96000910, "Title": "A hybrid soft computing technique for intrusion detection in web and cloud environment", "Abstract": "<p>Cloud computing environment contains important, essential, or confidential information; therefore, a security solution is needed to prevent this environment from potential attacks. In short, cloud computing has become one of the most sought after technologies in the field of information technology, and among the most dangerous threats. In this article, we propose a hybrid soft computing technique for intrusion detection in web and cloud environment (ST-IDS). In ST-IDS, we illustrate whale integrated slap swarm optimization algorithm for pre-processing which remove the unwanted/repeated data's in dataset. We introduce new clustering technique based on modified tug-of-war optimization algorithm which groups the data in different segments. Then, we develop hybrid machine learning technique that is, capsule learning based neural network which categorize the attack in cloud environment. Finally, the proposed ST-IDS technique can evaluate through standard open source datasets are KDD cup'99 and NSL-KDD. The performance comparison of the proposed ST-IDS technique using existing innovative technologies in terms of accuracy, precession, recall, specificity, F measure, false positive rate, and false negative rate.</p>", "Keywords": "CLNN;clustering technique;hybrid machine learning;IDS;intrusion detection;preprocessing", "DOI": "10.1002/cpe.7046", "PubYear": 2022, "Volume": "34", "Issue": "22", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Information Technology Government College of Engineering  Erode India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology Nandha Engineering College  Erode India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology Saveetha Engineering College  Chennai India"}], "References": [{"Title": "Improving Intrusion Detection System using Artificial Neural Network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "578", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Multi-chain and data-chains partitioning algorithm in intelligent manufacturing CPS", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}]}, {"ArticleId": 96000937, "Title": "High-speed Firewall Rule Verification Technique Improves Throughput Performance for IP Version 6", "Abstract": "<p>Throughput performance of firewalls depend on the execution speed to verify rules. Internet Protocol Version 6 (IPv6) and IPv4 ruleset memory requirements differ and affect rule access and execution time in a wide range of common firewalls. This paper contributes a high-speed firewall to execute rules for IPv6 with constant O(1) access time, and consumes optimal O(nbit) memory for 64-bit architectures, named FW6 firewall. Results are based on actual performance evaluations in conjunction with other high-speed firewalls (IPSets, IPack, and F3), such as processing time, memory consumption and throughput. Throughput measurements in IPv6 TCP/UDP packet trials (across ruleset and window sizes) show FW6 significantly outperforms IPSets. The trials have shown that FW6 improves throughput performance over IPSets by 0.24% (mean) and 0.21% (median) across all test variables. Nevertheless, the results suggest similarity and a minor performance increase by FW6 over IPSets. In addition, FW6 and IPSets throughputs are similar to IPack and F3 in IPv4 ruleset execution comparisons. As a result, FW6 can be used to replace previous high-speed firewalls.</p>", "Keywords": "", "DOI": "10.37936/ecti-cit.2022163.248690", "PubYear": 2022, "Volume": "16", "Issue": "3", "JournalId": 75000, "JournalTitle": "ECTI Transactions on Computer and Information Technology (ECTI-CIT)", "ISSN": "2286-9131", "EISSN": "2286-9131", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Informatics, Mahasarakham University, Maha sarakham, Thailand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Informatics, Mahasarakham University, Maha sarakham, Thailand"}, {"AuthorId": 3, "Name": "Kritsanapong Somsuk", "Affiliation": "Department of Computer and Communication Engineering, Faculty of Technology, Udon Thani Rajabhat University, UDRU, Udon Thani, Thailand"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Education, Udon Thani Rajabhat University, Udon Thani, Thailand"}], "References": []}, {"ArticleId": 96001001, "Title": "Wasserstein generative adversarial networks for modeling marked events", "Abstract": "<p>Marked temporal events are ubiquitous in several areas, where the events’ times and marks (types) are usually interrelated. Point processes and their non-functional variations using recurrent neural networks (RNN) model temporal events using intensity functions. However, since they usually utilize the likelihood maximization approach, they might fail. Moreover, their high simulation complexity makes them inappropriate. Since calculating the intensity function is not always necessary, generative models are utilized for modeling. Generative adversarial networks (GANs) have been successful in modeling point processes, but they still lack in modeling interdependent types and times of events. In this research, a double Wasserstein GAN (WGAN), using a conditional GAN, is proposed which generates types of events that are categorical data, dependent on their times. Experiments on synthetic and real-world data represent that WGAN methods are efficient or competitive with the compared intensity-based models. Furthermore, these methods have a faster simulation than intensity-based methods.</p>", "Keywords": "Marked point process; Wasserstein GAN; Generative adversarial networks; Conditional GAN; Generative models", "DOI": "10.1007/s11227-022-04781-0", "PubYear": 2023, "Volume": "79", "Issue": "3", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, University of Tabriz, Tabriz, Iran"}], "References": [{"Title": "Conditional Wasserstein GAN-based oversampling of tabular data for imbalanced learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114582", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}, {"Title": "The effect of loss function on conditional generative adversarial networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "6977", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}]}, {"ArticleId": 96001118, "Title": "Numerical study on the straight, helical and spiral capillary tube for the CO2 refrigerant", "Abstract": "A numerical study has been carried out for straight, spiral and helical capillary tubes and their performance has been compared with CO2 refrigerant. The numerical models are developed based on the fundamental conservation principles of mass, momentum, and energy. Within this, outer loop, the ordinary differential equations are solved from the inlet to the exit of the capillary tube. The study has been carried out to calculate the mass flow rate by bisection method where the mass is iteratively calculated at the specified capillary length or vice versa. In-house coding programming employs the finite difference approach for numerical solutions. The characterization of the capillary tube has been done by calculating the length for the given mass or by calculating mass for the given length. The comparison of the straight capillary with helical capillary tube (50 mm coil diameter) and spiral capillary tube (50 mm pitch) has been reported. For a change in tube diameter, surface roughness, and length, the percentage reduction in mass flow rate in capillary tubes (straight, helical, and spiral) is calculated. The percentage reduction in mass in a helical capillary tube compared to the straight capillary tube is about 7–9 %. The percentage reduction in mass in a spiral tube compared to the straight capillary tube is nearly 23–26 %. Additionally, the percentage reduction in mass in a spiral tube compared to the helical capillary tube is almost 17–19 %. Additionally, the percentage reduction in length in a spiral tube compared to straight capillary tube ranges from 37 % to 43 %. Similarly, the percentage reduction in length in a spiral tube compared to helical capillary tube is ranging from 25 % to 32 %. © <PERSON>, <PERSON><PERSON>, S. Ballal 2022.", "Keywords": "straight tube; helical tube; spiral tube; capillary tube; adiabatic; mass flow rate", "DOI": "10.17586/**************-22-4-804-811", "PubYear": 2022, "Volume": "22", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> College of Engineering, Satara, 415001, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Majhighariani Institute of Technology and Science, Odisha, Rayagada, 765017, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> College of Engineering, Satara, 415001, India"}], "References": []}, {"ArticleId": 96001208, "Title": "Automated software effort estimation for agile development system by heuristically improved hybrid learning", "Abstract": "<p>In the process of modern software development, an important role is played by software effort estimation. The failure or success of the projects is mostly based on the schedule outcomes and effort estimation accuracy. The effort estimation problem still remains as a challenging issue because of the limitations of various standard measures for forecasting the efforts in the plan-driven software development. This article emphasizes on novel software effort estimation framework using heuristically improved hybrid learning model. This article proposes heuristically improved hybrid learning (HI-HL) with deep belief network and artificial neural network for software effort estimation. The weight optimization strategy of DBN and ANN to attain highly accurate estimation. Here, the weight optimization is performed by integrating two standard optimization algorithms such as forest optimization algorithm and moth-flame optimization, so called as solution index-based forest moth flame optimization with the purpose of solving the fitness function concerning the “Magnitude of Relative Error and Mean Absolute Error (MAE).” From the table results, for dataset 1, the SMAPE of SI-FMFO-HI-HL is correspondingly secured 60.75%, 34.26%, 56.77%, and 61.44% improved than ELM, LSTM, DNN, and fuzzy. The simulation findings indicated that the recommended estimation model outperformed the baseline approaches on the three publically available datasets.</p>", "Keywords": "agile development system;artificial neural network;deep belief network;heuristically improved hybrid learning;software effort estimation;solution index-based forest-moth flame optimization", "DOI": "10.1002/cpe.7267", "PubYear": 2022, "Volume": "34", "Issue": "25", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, SRM IST Delhi NCR‐Campus  Ghaziabad India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, SRM IST Delhi NCR‐Campus  Ghaziabad India"}], "References": [{"Title": "Remote tracking of Parkinson's Disease progression using ensembles of Deep Belief Network and Self-Organizing Map", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "113562", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A new auto-tuning model for predicting the rock fragmentation: a cat swarm optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "3", "Page": "2209", "JournalTitle": "Engineering with Computers"}, {"Title": "RETRACTED ARTICLE: An effective agile development process by a hybrid intelligent effort estimation protocol", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "S1", "Page": "7", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 96001234, "Title": "The limits of explainability & human oversight in the EU Commission’s proposal for the Regulation on AI- a critical approach focusing on medical diagnostic systems", "Abstract": "The EU Commission’s proposal for the Regulation on Artificial Intelligence, whilst providing important specifications on the importance of transparency of high-risk systems, falls short in providing a nuanced picture of how technical safeguards in Articles 13 and 14 in the proposal should be translated to AI systems operating on the ground. This paper focusing on medical diagnostic systems offers a perspective on how transparency safeguards should be applied in practice, considering the role of post hoc explainability and Uncertainty Estimates in medical imaging. Medical diagnostic systems offer probabilistic judgements regarding disease classification tasks, having an impact on the interactive experience between the doctor and the patient. Accordingly, we need additional guidance regarding Articles 13 and 14 in the proposal, considering the role of shared decision-making, and patient autonomy in healthcare and to ensure that technical safeguards secure medical diagnostic systems that are a safe, reliable, and trustworthy.", "Keywords": "AI Act ; transparency ; post hoc explainability ; medical diagnostic systems", "DOI": "10.1080/13600834.2022.2116354", "PubYear": 2023, "Volume": "32", "Issue": "2", "JournalId": 4941, "JournalTitle": "Information & Communications Technology Law", "ISSN": "1360-0834", "EISSN": "1469-8404", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "UKRI Governance & Regulation Node at Trustworthy Autonomous Systems Programme, Edinburgh Law School, Old College, South Bridge, Edinburgh EH8, UK"}], "References": []}, {"ArticleId": 96001235, "Title": "Ultrasonic and electrical discharge–assisted milling of the Ti-6Al-4 V alloy", "Abstract": "<p>The ultrasonic and electrical discharge–assisted milling (US-EDAM) is a new machining method designed for machining difficult-to-cut materials. It combines two machining technologies: electrical discharge machining (EDM) and conventional milling with ultrasonic vibration. The EDM in the proposed method is used to soften the surface of the material to be machined and thereby reduce the cutting force. In the meanwhile, the ultrasonic vibration of the proposed method is used to improve the discharge efficiency of the EDM and reduce the cutting force. Likewise, the milling in the present method is used to remove the workpiece’s EDM-softened surface accurately and quickly. The effects of different machining methods (including the conventional milling (CM), the ultrasonic-assisted machining (USM), the electrical discharge–assisted machining (EDAM), and the US-EDAM) on the machined material’s surface topography, plastic deformation, microscopic appearance, surface microhardness, and residual stress on the surface were compared for different machining parameters. The results confirmed that the EDM in the US-EDAM softened the surface of the material to be removed and reduced the cutting force. Furthermore, the ultrasonic vibration assistance in the US-EDAM reduced the cutting force with intermittent cutting. Notably, the surface integrity of the machined workpiece under the US-EDAM was better than the ones under the other machining methods. Hence, the US-EDAM demonstrated its capability as a new hybrid machining combining EDM, ultrasonic vibration assistance, and milling.</p>", "Keywords": "Ultrasonic and electrical discharge–assisted milling; Titanium alloy; Conventional milling; Discharge signal; Surface roughness", "DOI": "10.1007/s00170-022-10010-y", "PubYear": 2022, "Volume": "122", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Precision Machining Laboratory Room 214, School of Mechanical Engineering, Yeungnam University, Gyeongsan, South Korea; Hunan Provincial Key Laboratory of High Efficiency and Precision Machining of Difficult-to-Cut Materials, School of Mechanical Engineering, Hunan University of Science and Technology, Xiangtan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Provincial Key Laboratory of High Efficiency and Precision Machining of Difficult-to-Cut Materials, School of Mechanical Engineering, Hunan University of Science and Technology, Xiangtan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Precision Machining Laboratory Room 214, School of Mechanical Engineering, Yeungnam University, Gyeongsan, South Korea"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Precision Machining Laboratory Room 214, School of Mechanical Engineering, Yeungnam University, Gyeongsan, South Korea"}, {"AuthorId": 5, "Name": "Ye In Kwak", "Affiliation": "Precision Machining Laboratory Room 214, School of Mechanical Engineering, Yeungnam University, Gyeongsan, South Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Precision Machining Laboratory Room 214, School of Mechanical Engineering, Yeungnam University, Gyeongsan, South Korea"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Precision Machining Laboratory Room 214, School of Mechanical Engineering, Yeungnam University, Gyeongsan, South Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Precision Machining Laboratory Room 214, School of Mechanical Engineering, Yeungnam University, Gyeongsan, South Korea"}], "References": [{"Title": "Experimental study on tool wear in ultrasonic vibration–assisted milling of C/SiC composites", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "1-2", "Page": "425", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Direct observation of discharging phenomena in vibration-assisted micro-electrical discharge machining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "4", "Page": "1125", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Analytical, FEA, and experimental research of 2D-Vibration Assisted Cutting (2D-VAC) in titanium alloy Ti6Al4V", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "5-6", "Page": "1739", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 96001361, "Title": "Grouped spatial autoregressive model", "Abstract": "With the development of the internet, network data with replications can be collected at different time points. The spatial autoregressive panel (SARP) model is a useful tool for analyzing such network data. However, in the traditional SARP model, all individuals are assumed to be homogeneous in their network autocorrelation coefficients, while in practice, correlations could differ for the nodes in different groups. Here, a grouped spatial autoregressive (GSAR) model based on the SARP model is proposed to permit network autocorrelation heterogeneity among individuals, while analyzing network data with independent replications across different time points and strong spatial effects. Each individual in the network belongs to a latent specific group, which is characterized by a set of parameters. Two estimation methods are studied: two-step naive least-squares estimator, and two-step conditional least-squares estimator. Furthermore, their corresponding asymptotic properties and technical conditions are investigated. To demonstrate the performance of the proposed GSAR model and its corresponding estimation methods, numerical analysis was performed on simulated and real data.", "Keywords": "Conditional least-squares estimation ; Network autocorrelation heterogeneity ; Large-scale network ; Naive least-squares estimation ; Spatial autoregressive panel model", "DOI": "10.1016/j.csda.2022.107601", "PubYear": 2023, "Volume": "178", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Huang", "Affiliation": "Center for Applied Statistics, Renmin University of China, Beijing, China;School of Statistics, Renmin University of China, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center for Applied Statistics, Renmin University of China, Beijing, China;School of Statistics, Renmin University of China, Beijing, China;Corresponding author at: Center for Applied Statistics, Renmin University of China, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics & Data Science, Southern University of Science and Technology, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Center for Applied Statistics, Renmin University of China, Beijing, China;School of Statistics, Renmin University of China, Beijing, China;Corresponding author at: Renmin University of China, Beijing, 100872, China"}], "References": [{"Title": "Approximate least squares estimation for spatial autoregressive models with covariates", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "106833", "JournalTitle": "Computational Statistics & Data Analysis"}]}, {"ArticleId": 96001373, "Title": "Performance Evaluation of Multiband Embroidered Fractal Antenna on Human Body", "Abstract": "<p>Several recent technologies are emerging that integrate the wearable system with antenna technology. The wearable antennas embedded into textiles are the most promising and fully integrative ones in the field of Wireless Body Area Networks (WBAN). In this paper, a fully textile wearable antenna operating at 2.4 GHz with Minkowski fractal design is presented. The antenna is fabricated using a pure silver conductive thread on the polyester substrate using the embroidery technique. The design is simulated using a 3D full-wave electromagnetic simulation tool. The antenna is placed onto the human body to obtain a Specific Absorption Rate (SAR) results. Simulation results are demonstrated using different antenna parameters like S11, return loss, VSWR, gain, and directivity. Simulation and measured results depict the multiband antenna performance for various applications with four resonant frequencies 2.68, 4.06, 4.32, and 4.46 GHz. The SAR value for the simulated embroidered textile fractal antenna is 1.32 W/Kg when placed along with the realistic human male torso model in HFSS.</p>", "Keywords": "Body area network; Antenna design; <PERSON><PERSON> fractal design", "DOI": "10.1007/s42979-022-01354-z", "PubYear": 2022, "Volume": "3", "Issue": "6", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "Shruti Gite", "Affiliation": "Department of Electronics and Telecommunication Engineering, Pillai HOC College of Engineering and Technology, Dist. Raigad, India"}, {"AuthorId": 2, "Name": "<PERSON>i Subhedar", "Affiliation": "Department of Electronics and Telecommunication Engineering, Pillai HOC College of Engineering and Technology, Dist. Raigad, India"}], "References": [{"Title": "A Circularly Polarized, Flexible and Compact Quad-Band Wearable Antenna for Off-Body Communication Applications", "Authors": "<PERSON><PERSON>; B T <PERSON> Madhav; B <PERSON><PERSON>hvi Nadh", "PubYear": 2022, "Volume": "31", "Issue": "3", "Page": "", "JournalTitle": "Journal of Circuits, Systems and Computers"}]}, {"ArticleId": 96001390, "Title": "A Critical Scrutiny of ConvNets (CNNs) and Its Applications: Review", "Abstract": "<p>This manuscript offers the existing methods used in identifying the objects and its applications. Deep Learning ConvNets (CNN) which help to judge the face of humans face using CNNs are considered in this manuscript which are reviewed and works relating to their applications are examined. Existing research nurtured from national and international articles relating to facial object identification is collected and surveyed to evaluate which method is the most common than the available existing methods in facial object identification in consideration. The publications from the year 1993 onwards are considered, the manuscript recommends that presently, deep CNNs are extensively used in facial object identification. In the process of facial object identification raises privacy issues. It also acts as a commercial and business tool applied in airports, payment methods, health care, retail and education, etc. In airports this method is used in security check-ins, in banks this method is utilized in contactless indispensable payments, in health care this technique can be used in the patient check-in process, emotion detection of the patient, diagnosing the condition of the patient, identification of the staff, security etc., In retail markets this technique helps to identify the experience in shopping, improves the service and satisfaction of the customer as well as the security, in the field of education this technique helps to identify a person formally engaged in learning. ConvNets is applied in different arena such as Sentiment analysis, Social Networks, Traffic Network Analysis, Analysis of user nature behavior, and dealing with social networks. This work demonstrates the Introduction, CNN approaches and its utilization, types available, typical approaches and its applications, Observations, Conclusions and References.</p>", "Keywords": "Multilayer Perceptron; Back Propagation Neural Network (BPNN); Face recognition; Artificial Neural Networks (ANN); Convolutional Neural Network (CNN)", "DOI": "10.1007/s42979-022-01359-8", "PubYear": 2022, "Volume": "3", "Issue": "6", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of C.S.E, Panimalar Engineering College, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of C.S.E, Dr. M.G.R. Educational and Research Institute, Chennai, India"}], "References": []}, {"ArticleId": 96001423, "Title": "Design of Digital Teaching Platform for Spoken English Based on Virtual Reality", "Abstract": "<p>In order to improve the effect of digital teaching of spoken English, this paper combines virtual reality technology to design a digital teaching platform for spoken English and combines virtual reality technology to digitally process spoken English. The signal processed by the front-end amplifier circuit is converted into a digital signal through analog-to-digital conversion, and the digital receiving system is realized by means of software radio, which reduces the dependence on the hardware circuit and enhances the portability of the system. In addition, this paper selects filters according to the needs of digital teaching of spoken English and constructs a digital teaching platform for spoken English based on virtual reality. The experiment verifies that the digital teaching platform for spoken English based on virtual reality has a good effect of digital spoken English teaching.</p>", "Keywords": "", "DOI": "10.1155/2022/3274852", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 8496, "JournalTitle": "Advances in Multimedia", "ISSN": "1687-5680", "EISSN": "1687-5699", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Anyang Vocational and Technical College, Anyanng, Henan 455000, China"}], "References": []}, {"ArticleId": 96001440, "Title": "A Forward-Looking Study on the In-Depth Development Planning of Information Technology into Teacher Education", "Abstract": "<p>Teacher professional education development is actually another type of development that crosses over into a category of teacher training development that encompasses two other types of teacher training—one of which is formal and the other is informal teacher training. The other approach to teacher professional development is quite different from the professional development of professional education. The focus of the professional development of teachers is a dynamic process of development in which teachers take the initiative, actively, and consciously pursue the direction of professional development of teachers. Therefore, how to observe and study the direction of teachers’ professional development from the perspective of teachers is an urgent problem that needs to be studied and solved. The professional development of teachers should be combined with their own social and teaching lives. In addition, information technology should be integrated into teachers’ daily teaching life, so that it can be used as a tool to support teachers’ professional development. Information technology has gradually become a tool and a means that teachers cannot do without in their teaching lives, so it is important to study and think about it from various aspects, such as the teaching process and teachers’ growth experiences, and to continuously study teachers’ professional development. Therefore, it is necessary to study how information technology can be used well by teachers and used by teachers to become a useful tool in teachers’ educational and teaching work from various aspects. This paper first argues that the main goal of teachers’ professional development is to gradually grow into expert teachers, while the main content of teachers’ professional development is to understand practical knowledge, and the specific tool used by teachers in teaching is the application of social software support that can support teachers’ professional development (<PERSON><PERSON> and <PERSON>, 2022). This paper envisages that information technology can be integrated into the entire teaching environment and the teaching process of teachers, as a tool and a means to make the content of teachers’ teaching more concise and clear. Through the use of information technology and other tools, teachers can gradually identify what they need to improve and add to their teaching process, lay the foundation for their future development, and pave the way to become better teachers with a clearer plan for their future development.</p>", "Keywords": "", "DOI": "10.1155/2022/4885771", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Marxism, Zhengzhou Railway Vocational and Technical College, Zhengzhou, Henan 450052, China"}], "References": []}, {"ArticleId": 96001461, "Title": "PRIVAFRAME: A Frame-Based Knowledge Graph for Sensitive Personal Data", "Abstract": "<p>The pervasiveness of dialogue systems and virtual conversation applications raises an important theme: the potential of sharing sensitive information, and the consequent need for protection. To guarantee the subject’s right to privacy, and avoid the leakage of private content, it is important to treat sensitive information. However, any treatment requires firstly to identify sensitive text, and appropriate techniques to do it automatically. The Sensitive Information Detection (SID) task has been explored in the literature in different domains and languages, but there is no common benchmark. Current approaches are mostly based on artificial neural networks (ANN) or transformers based on them. Our research focuses on identifying categories of personal data in informal English sentences, by adopting a new logical-symbolic approach, and eventually hybridising it with ANN models. We present a frame-based knowledge graph built for personal data categories defined in the Data Privacy Vocabulary (DPV). The knowledge graph is designed through the logical composition of already existing frames, and has been evaluated as background knowledge for a SID system against a labeled sensitive information dataset. The accuracy of PRIVAFRAME reached 78%. By comparison, a transformer-based model achieved 12% lower performance on the same dataset. The top-down logical-symbolic frame-based model allows a granular analysis, and does not require a training dataset. These advantages lead us to use it as a layer in a hybrid model, where the logical SID is combined with an ANNs SID tested in a previous study by the authors.</p>", "Keywords": "sensitive personal data; semantic models; privacy protection; privacy knowledge graph; graph-based AI sensitive personal data ; semantic models ; privacy protection ; privacy knowledge graph ; graph-based AI", "DOI": "10.3390/bdcc6030090", "PubYear": 2022, "Volume": "6", "Issue": "3", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Filologia Classica e Italianistica (FICLIT), University of Bologna, 40126 Bologna, Italy↑Ellysse s.r.l., 42124 Reggio Emilia, Italy↑These authors contributed equally to this work.; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Filologia Classica e Italianistica (FICLIT), University of Bologna, 40126 Bologna, Italy↑Institute of Cognitive Sciences and Technologies, National Research Council (ISTC-CNR), 00185 Roma, Italy↑These authors contributed equally to this work.; Corresponding author"}], "References": [{"Title": "Closing the Loop between knowledge patterns in cognition and the Semantic Web", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "139", "JournalTitle": "Semantic Web"}, {"Title": "Exsense: Extract sensitive information from unstructured data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "102156", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 96001547, "Title": "Bald eagle search optimization with deep transfer learning enabled age-invariant face recognition model", "Abstract": "Facial aging variation is a challenging process in the design of face recognition system because of high intra-personal differences instigated by age progression. Age-invariant face recognition (AIFR) models find applicability in several real time applications such as criminal identification, missing person detection, and so on. The main issue is the high intra-personal disparities because of complicated and non-linear age progression process. An essential component of face recognition model is the extraction of important features from the facial images for reducing intrapersonal differences produced by illumination, expression, pose, age, etc. The recent advances of machine learning (ML) and deep learning (DL) models pave a way for effective design of AIFR models. In this view, this study presents a new Bald Eagle Search Optimization with Deep Transfer Learning Enabled AFIR (BESDTL-AIFR) model. The presented BESDTL-AIFR model primarily pre-processes the facial images to enhance the quality. Besides, the BESDTL-AIFR model utilizes Inception v3 model for learning high level deep features. Next, these features are passed into the optimal deep belief network (DBN) model for face recognition. Finally, the hyperparameters of the DBN model are optimally chosen by the use of BES algorithm. Experimentation analysis on challenging benchmark datasets pointed out the promising outcomes of the BESDTL-AIFR model compared to recent approaches.", "Keywords": "Age invariant face recognition ; Facial image analysis ; Age progression ; Deep transfer learning ; Hyperparameter tuning", "DOI": "10.1016/j.imavis.2022.104545", "PubYear": 2022, "Volume": "126", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "Shtwai Alsubai", "Affiliation": "Department of Computer Science, College of Computer Engineering and Sciences, Prince <PERSON> Abdul<PERSON>z University, P.O. Box 151, Al-Kharj 11942, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, College of Computer and Information Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O.Box 84428, Riyadh 11671, Saudi Arabia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, College of Science, Taif University, P.O. Box 11099, Taif 21944, Saudi Arabia;Department of Mathematics, Faculty of Science, Sohag University, Sohag, Egypt"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Computer Engineering and Sciences, Prince <PERSON> University, Al-Kharj, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Engineering and Sciences, Prince <PERSON> University, Al Kharj, Saudi Arabia"}, {"AuthorId": 6, "Name": "Romany F. <PERSON>our", "Affiliation": "Department of Mathematics, Faculty of Science, New Valley University, El-Kharga, 72511, Egypt"}], "References": [{"Title": "Novel meta-heuristic bald eagle search optimisation algorithm", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; B. <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "2237", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A robust and efficient convolutional deep learning framework for age‐invariant face recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "3", "Page": "", "JournalTitle": "Expert Systems"}, {"Title": "Novel local feature extraction for age invariant face recognition", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "175", "Issue": "", "Page": "114786", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Prediction of face age progression with generative adversarial networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "25", "Page": "33911", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 96001601, "Title": "Graph‐based Bayesian network conditional normalizing flows for multiple time series anomaly detection", "Abstract": "<p>Various devices and sensors of cyber-physical systems interact with each other in time and space, and the generated multiple time series have implicit correlations and highly nonlinear relationships. Determining how to model the multiple time series and capture dependencies through extracting features is the key to anomaly detection. In this paper, we propose a graph-based Bayesian network conditional normalizing flows model for multiple time series anomaly detection, Bayesian network conditional normalizing flows (BNCNF). It applies a Bayesian network to model the causal relationships of multiple time series and introduces a spectral temporal dependency encoder to obtain the representations of interdependency between multiple time series. The representations are introduced as conditional information into the normalizing flows for density estimation, and the data corresponding to low density is judged as anomalies. The experiment are conducted on SWaT, WADI, and SMD datasets, the F1 score reaches 0.95, 0.92, and 0.97 on the three datasets, respectively. The results show that BNCNF has better performance in anomaly detection compared with the current mainstream methods.</p>", "Keywords": "anomaly detection;Bayesian network;conditional normalizing flows;density estimation;multiple time series", "DOI": "10.1002/int.23027", "PubYear": 2022, "Volume": "37", "Issue": "12", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering East China Jiaotong University  Nanchang China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering East China Jiaotong University  Nanchang China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering East China Jiaotong University  Nanchang China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Engineering East China Jiaotong University  Nanchang China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Information Engineering East China Jiaotong University  Nanchang China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Jiangxi Electric Power Co Ltd, Electric Power Research Institute  Nanchang China"}], "References": [{"Title": "Optimal mixed block withholding attacks based on reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "12", "Page": "2032", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Camdar‐adv: Generating adversarial patches on 3D object", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "3", "Page": "1441", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "CSRT rumor spreading model based on complex network", "Authors": "Shan Ai; Sheng Hong; <PERSON><PERSON><PERSON> Zheng", "PubYear": 2021, "Volume": "36", "Issue": "5", "Page": "1903", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Enhancing intrusion detection with feature selection and neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "7", "Page": "3087", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Motif‐based embedding label propagation algorithm for community detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "3", "Page": "1880", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 96001622, "Title": "Prediction Method of User Behavior Label Based on the BP Neural Network", "Abstract": "<p>With the development of big data, precision marketing helps businesses to be more efficient in selling products and stand out from the fierce competition. In this paper, the backpropagation neural network is introduced as an approach to analyze the data of user online behaviors and create labels for each user. In this way, the business will be able to realize accurate user classification and forecast users’ future behaviors and thus achieve precision marketing. The study tested the backpropagation neural network with a real user behavior dataset from Taobao for a recommendation. According to users’ behavior data, the network successfully classified users into 5 clusters with distinct labels and this information can give the business valuable insights into their customers for precision marketing and selling products.</p>", "Keywords": "", "DOI": "10.1155/2022/7241956", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northwestern University, Evanston, USA"}], "References": [{"Title": "Improving search ranking of geospatial data based on deep learning using user behavior data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "104520", "JournalTitle": "Computers & Geosciences"}, {"Title": "Regular Expressions and Transducers Over Alphabet-Invariant and User-Defined Labels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "8", "Page": "983", "JournalTitle": "International Journal of Foundations of Computer Science"}]}, {"ArticleId": 96001843, "Title": "A C2 continuous trajectory planning method for 6-DOF rotational robot manipulators", "Abstract": "Purpose \nThe purpose of this paper is to propose a smooth double-spline interpolation method for six-degree-of-freedom rotational robot manipulators, achieving the global C2 continuity of the robot trajectory.\n \n \n Design/methodology/approach \nThis paper presents a smooth double-spline interpolation method, achieving the global C2 continuity of the robot trajectory. The tool center positions and quaternion orientations are first fitted by a cubic B-spline curve and a quartic-polynomial-based quaternion spline curve, respectively. Then, a parameter synchronization model is proposed to realize the synchronous and smooth movement of the robot along the double spline curves. Finally, an extra u-s function is used to record the relationship between the B-spline parameter and its arc length parameter, which may reduce the feed rate fluctuation in interpolation. The seven segments jerk-limited feed rate profile is used to generate motion commands for algorithm validation.\n \n \n Findings \nThe simulation and experimental results demonstrate that the proposed method is effective and can generate the global C2-continuity robot trajectory.\n \n \n Originality/value \nThe main contributions of this paper are as follows: guarantee the C2 continuity of the position path and quaternion orientation path simultaneously; provide a parameter synchronization model to realize the synchronous and smooth movement of the robot along the double spline curves; and add an extra u-s function to realize arc length parameterization of the B-spline path, which may reduce the feed rate fluctuation in interpolation.", "Keywords": "C2 continuity;B-spline fitting;Arc length parameterization;Unit quaternion interpolation spline curve", "DOI": "10.1108/AA-07-2021-0091", "PubYear": 2022, "Volume": "42", "Issue": "5", "JournalId": 4373, "JournalTitle": "Assembly Automation", "ISSN": "0144-5154", "EISSN": "1758-4078", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Robotics and System, Harbin Institute of Technology , Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Robotics and System, Harbin Institute of Technology , Harbin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Robotics and System, Harbin Institute of Technology , Harbin, China"}, {"AuthorId": 4, "Name": "Xin <PERSON>", "Affiliation": "State Key Laboratory of Robotics and System, Harbin Institute of Technology , Harbin, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Robotics and System, Harbin Institute of Technology , Harbin, China"}], "References": [{"Title": "A tolerance constrained G2 continuous path smoothing and interpolation method for industrial SCARA robots", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "101907", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "An analytical C3 continuous tool path corner smoothing algorithm for 6R robot manipulator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "101947", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 96001846, "Title": "A local meshless method for transient nonlinear problems: Preliminary investigation and application to phase-field models", "Abstract": "Transient nonlinear problems play an important role in many engineering problems. Phase-field equations, including the well-known Allen-<PERSON><PERSON> and <PERSON><PERSON><PERSON> equations, fall in this category, and have applications in cutting-edge technologies such as modeling the diffusion of lithium (Li) ions in two-phase electrode particles of Li-ion batteries. In this paper, a local meshless method for solving this category of partial differential equations (PDEs) is proposed. The Newton-<PERSON><PERSON> scheme is employed to transform the nonlinear PDEs to an iterative series of linear ones which can be solved with the proposed method. The accuracy and performance of the method are examined in various linear and nonlinear problems, such as Laplace equation, three dimensional elasticity as well as some abstract mathematical equations with linear or nonlinear boundary conditions. The main focus of the work is on applying the proposed method in solution of the phase-field equations, including the <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> equations. In addition to homogeneous Neumann boundary condition which has been widely examined in the literature, we also employ a practical nonlinear, inhomogeneous Neumann boundary condition formulation specialized for modeling the diffusion of lithium ions in electrode particles of Li-ion batteries. The generalized- α method is used for time integration of diffusion-type equations to overcome the intrinsic stiffness of the phase-field equations. It is shown that the method is capable of capturing the main features of the phase-field models i.e. phase separation, coarsening and energy decay in closed systems.", "Keywords": "Meshless methods ; Transient problems ; Nonlinear problems ; Nonlinear boundary conditions ; Phase-field models ; Generalized- α method", "DOI": "10.1016/j.camwa.2022.08.027", "PubYear": 2022, "Volume": "124", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, University of Isfahan, 81744-73441 Isfahan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, University of Isfahan, 81744-73441 Isfahan, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, University of Isfahan, 81744-73441 Isfahan, Iran"}], "References": [{"Title": "A fully discrete virtual element scheme for the <PERSON><PERSON>–<PERSON> equation in mixed form", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "246", "Issue": "", "Page": "106870", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 96001930, "Title": "Microfluidics chip inspired by fish gills for blood cells and serum separation", "Abstract": "In this paper, a microfluidic chip inspired by fish gills has been designed and fabricated in order to separate blood cells from serum. This microchip is modeled after fish gills which filter particles from fluids. In this way, the blood passes through a reservoir that contains several parallel rows of filter columns, which perform the filtering action. COMSOL Multiphysics software was used to conduct several simulations to optimize the pressure and velocity parameters of blood entering the microchip, and these parameters were used to determine the appropriate dimensions of the microchip. The simulation results indicate that the microchip is able to completely separate the cells from the serum, assuming a blood flow rate of 134 μl/s. With the help of the proposed filter, blood cells are separated from the plasma after 20 s and the aggregation occurs in the outlet reservoirs. In order to fabricate the microchip, the filter component made of polydimethylsiloxane (PDMS) was bonded to the copper substrate. In this paper a new copper to PDMS bonding process is proposed. Copper was chosen as the substrate because it allows the application of voltage to separate particles through the Dielectrophoresis method. The chemical etching method reinforced with noble metals was used to create micro holes on the silicon wafer, which are used to mold PDMS filters with a diameter of several micrometers. The main reservoir with a depth of 20 µm was created by the wet etching method. The reservoir was coated with silane coating and the oxygen plasma was utilized to bond the PDMS filters and the main reservoir. The copper substrate allows the microchip to be used with other electronic devices and circuits on printed circuit boards. Scanning Electron Microscopy (SEM) was used to observe the silane-coated copper surface, the micro holes on the silicon wafer, and the PDMS pillars. The topography and roughness of the copper surface before and after silane coating was evaluated through Atomic Force Microscopy (AFM). Optical Microscopy (OM) was utilized to observe the separation of the cells from the plasma. The results showed acceptable adhesion between copper and PDMS and good results for serum separation from blood cells.", "Keywords": "Fish gills filters ; Blood separation ; Microfluidics chip ; Copper-PDMS bonding", "DOI": "10.1016/j.sna.2022.113839", "PubYear": 2022, "Volume": "346", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Sensors and Actuators Lab., Faculty of New Sciences and Technologies, University of Tehran, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Sensors and Actuators Lab., Faculty of New Sciences and Technologies, University of Tehran, Tehran, Iran;Corresponding author at: Sensors and Actuators Lab., Mechatronics & MEMS Dep., Faculty of New Sciences and Technologies, University of Tehran, North Kargar St., Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Metallurgy and Materials Engineering, College of Engineering, University of Tehran, Tehran, Iran"}], "References": []}, {"ArticleId": 96001977, "Title": "Event-based security tracking control for networked control systems against stochastic cyber-attacks", "Abstract": "This paper investigates the security tracking controller design for discrete-time networked control systems (NCSs) with stochastic cyber-attacks via dynamic event-triggered communication approach (DETCA). The DETCA is introduced to adjust the amount of data transmission in the network based on variation of the tracking error while keeping the tracking performance of the system. Stochastic cyber-attacks such as the denial-of-service and deception attacks are assumed to be encountered during the signal transmitted in the network. A sufficient condition to guarantee the asymptotical stability of the tracking error system is achieved with the Lya<PERSON>nov stability theory . The tracking controller gain and the event-triggering parameter are co-designed by solving a linear matrix inequality . Finally, two simulation examples are given to verify the availability of theoretical results.", "Keywords": "", "DOI": "10.1016/j.ins.2022.08.085", "PubYear": 2022, "Volume": "612", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, Nanjing University of Finance and Economics, Nanjing, Jiangsu 210023, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Nanjing University of Finance and Economics, Nanjing, Jiangsu 210023, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, Nanjing University of Finance and Economics, Nanjing, Jiangsu 210023, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Optical-Electrical and Computer Engineering, University of Shanghai for Science and Technology, Shanghai 200093, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Advanced Technology, Nanjing University of Posts and Telecommunications, Nanjing, Jiangsu 210023, China"}], "References": [{"Title": "Dynamic event-triggered mechanism for H∞ non-fragile state estimation of complex networks under randomly occurring sensor saturations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "304", "JournalTitle": "Information Sciences"}, {"Title": "Distributed guaranteed two-target tracking over heterogeneous sensor networks under bounded noises and adversarial attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "535", "Issue": "", "Page": "187", "JournalTitle": "Information Sciences"}, {"Title": "Observer-based event triggeringH∞LFC for multi-area power systems under DoS attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "543", "Issue": "", "Page": "437", "JournalTitle": "Information Sciences"}, {"Title": "Particle filtering for a class of cyber-physical systems under Round-Robin protocol subject to randomly occurring deception attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "298", "JournalTitle": "Information Sciences"}, {"Title": "Resilient adaptive control of switched nonlinear cyber-physical systems under uncertain deception attacks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "543", "Issue": "", "Page": "398", "JournalTitle": "Information Sciences"}, {"Title": "Hybrid-triggered-based security controller design for networked control system under multiple cyber attacks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "548", "Issue": "", "Page": "69", "JournalTitle": "Information Sciences"}, {"Title": "Attack Detection Method based on Bayesian Hypothesis Testing Principle in CPS", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "187", "Issue": "", "Page": "474", "JournalTitle": "Procedia Computer Science"}, {"Title": "Event-triggered resilient control for cyber-physical systems under periodic DoS jamming attacks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "577", "Issue": "", "Page": "541", "JournalTitle": "Information Sciences"}, {"Title": "Resilient leader tracking for networked Lagrangian systems under DoS attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "577", "Issue": "", "Page": "622", "JournalTitle": "Information Sciences"}, {"Title": "Dynamic event-triggered model-free adaptive control for nonlinear CPSs under aperiodic DoS attacks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "790", "JournalTitle": "Information Sciences"}, {"Title": "Event-triggered control of Markov jump systems against general transition probabilities and multiple disturbances via adaptive-disturbance-observer approach", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "1113", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96002341, "Title": "Embedded predictive controller based on fuzzy linear parameter-varying model: A hardware-in-the-loop application to an ESP-lifted oil well system", "Abstract": "This work addresses the development of a fuzzy infinite horizon model predictive control (FIHMPC) strategy and its application to an ESP-lifted oil well system in an embedded hardware. The proposed scheme is based on a fuzzy model to approximate the nonlinear system to a linear parameter-varying model and incorporate it into the proposed MPC law. Slacked terminal constraints and zone control scheme are incorporated to the FIHMPC controller assuring optimization feasibility to handle ESP time-varying operation envelope. The implementable zone FIHMPC controller is applied through an ESP32 micro-controller to ESP-lifted oil well system equations, executed in MATLAB, composing a hardware-in-the-loop (HIL) simulation scheme. Nonlinear mismatch scenarios with unmeasured disturbances show the effectiveness of the proposed controller over multiple operating conditions. Computational memory and processing analysis over simulations also validates the controller application.", "Keywords": "Artificial Oil Lift Methods ; Electrical Submersible Pump ; Zone control ; Fuzzy Model Predictive Control ; Data-based Model ; Fuzzy Ta<PERSON>gi-Sugeno-Kang model", "DOI": "10.1016/j.dche.2022.100054", "PubYear": 2022, "Volume": "5", "Issue": "", "JournalId": 90723, "JournalTitle": "Digital Chemical Engineering", "ISSN": "2772-5081", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Graduate Program in Mechatronics (PPGM), Polytechnic School, Federal University of Bahia (UFBA), Prof. <PERSON><PERSON>, 2 Federação, Salvador, 40210-630, Bahia, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Graduate Program in Mechatronics (PPGM), Polytechnic School, Federal University of Bahia (UFBA), Prof. <PERSON><PERSON>, 2 Federação, Salvador, 40210-630, Bahia, Brazil"}, {"AuthorId": 3, "Name": "Thiago P<PERSON>", "Affiliation": "Mechatronics Laboratory, Department of Exact and Technological Sciences, State University of Santa Cruz (UESC), Road Jorge Amado, Km 16 - Salobrinho, Ilhus, 45662-900, Bahia, Brazil"}, {"AuthorId": 4, "Name": "Márcio A.F. Martins", "Affiliation": "Graduate Program in Mechatronics (PPGM), Polytechnic School, Federal University of Bahia (UFBA), Prof. <PERSON><PERSON><PERSON>, 2 Federação, Salvador, 40210-630, Bahia, Brazil;Department of Chemical Engineering, Polytechnic School, Federal University of Bahia (UFBA), Rua Prof. Aristides Novis, 2 Federação, Salvador, 40210-630, Bahia, Brazil;Corresponding author"}], "References": [{"Title": "An Adaptive Infinite Horizon Model Predictive Control Strategy Applied to an ESP-lifted Oil Well System", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "3", "Page": "176", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Nonlinear Model Predictive Control of Electrical Submersible Pumps based on Echo State Networks", "Authors": "<PERSON>; <PERSON><PERSON>; Sondre B<PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101553", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 96002359, "Title": "Intricacies in image steganography and innovative directions", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2022.10050293", "PubYear": 2022, "Volume": "23", "Issue": "1/2", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96002434, "Title": "Precise Values for the Strong Subgraph 3-Arc-Connectivity of Cartesian Products of Some Digraph Classes", "Abstract": "<p>Let [Formula: see text] be a digraph of order [Formula: see text], [Formula: see text] a subset of [Formula: see text] of size [Formula: see text] and [Formula: see text]. A strong subgraph [Formula: see text] of [Formula: see text] is called an [Formula: see text]-strong subgraph if [Formula: see text]. A pair of [Formula: see text]-strong subgraphs [Formula: see text] and [Formula: see text] is said to be arc-disjoint if [Formula: see text]. Let [Formula: see text] be the maximum number of arc-disjoint [Formula: see text]-strong subgraphs in [Formula: see text]. <PERSON> and <PERSON><PERSON><PERSON> defined the strong subgraph [Formula: see text]-arc-connectivity as [Formula: see text] The new parameter [Formula: see text] could be seen as a generalization of classical edge-connectivity of undirected graphs. In this paper, we get precise values for the strong subgraph 3-arc-connectivity of Cartesian products of some digraph classes. Also, we prove that there is no upper bound on [Formula: see text] depending on [Formula: see text] and [Formula: see text].</p>", "Keywords": "", "DOI": "10.1142/S0219265921500365", "PubYear": 2023, "Volume": "23", "Issue": "1", "JournalId": 20646, "JournalTitle": "Journal of Interconnection Networks", "ISSN": "0219-2659", "EISSN": "1793-6713", "Authors": [{"AuthorId": 1, "Name": "Yiling Dong", "Affiliation": "School of Mathematics and Statistics, Ningbo University, Zhejiang 315211, P. R. China"}], "References": []}, {"ArticleId": 96002662, "Title": "Extending SC-PDSI-PM with neural network regression using GLDAS data and Permutation Feature Importance", "Abstract": "The Palmer Drought Severity Index (PDSI) ranges from −10 to 10 and is used for monitoring drought extent and severity. PDSI is a monthly global gridded data set with partial global coverage from 1850 through 1947 and full global coverage from 1948 through 2018. PDSI updates are infrequent. We present a method to extend PDSI using Global Land Data Assimilation System (GLDAS) data. We provide an updated dataset and code for the method. We discuss the accuracy and bias of the method for various regions. We have high accuracy, with 99.5% of the globe exhibiting RMSE values less than 1. Globally our method is unbiased with an average ME of approximately 0. Some regions have slight biases with dryer and wetter regions showing a slight negative and positive biases, respectively. Prediction errors exhibits spatial trends with the highest errors in areas with extreme climate.", "Keywords": "PDSI ; Drought ; Machine learning ; GLDAS ; Time series regression ; Data extension", "DOI": "10.1016/j.envsoft.2022.105475", "PubYear": 2022, "Volume": "157", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96002756, "Title": "A method for improving the efficiency of integrated processing of Earth remote sensing data in solving problems of spatial objects monitoring", "Abstract": "A method is proposed for improving the efficiency of the system for complex processing of data obtained by remote sensing of the Earth in conditions of limited resources in solving problems of spatial objects monitoring. The efficiency of the system functioning is increased through the rational allocating its resources according to the tasks to be solved, taking into account the priority solution of those that have a higher value of relative importance coefficient. It is also proposed to improve the efficiency of solving each of the tasks on the basis of exclusion from the work plans for solving those resources that are overloaded with other tasks, but at the same time make an insignificant contribution to the formation of an integral result. The simulation results show that the use of the proposed method for improving the efficiency of the system functioning for complex processing of Earth remote sensing data in conditions of limited resources, when solving problems of monitoring spatial objects, makes it possible to ensure the required quality of managerial decisions, especially in conditions of high dynamism of changing the monitoring objects characteristics. © Sergey A. K., Alexandr I. K. 2022.", "Keywords": "complex processing; earth remote sensing data; complex processing resources; single technological cycle of remote sensing data processing", "DOI": "10.17586/**************-22-4-691-698", "PubYear": 2022, "Volume": "22", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "S.A<PERSON> Karin", "Affiliation": "Mozhaisky Military Aerospace Academy, Saint Petersburg, 197198, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mozhaisky Military Aerospace Academy, Saint Petersburg, 197198, Russian Federation"}], "References": []}, {"ArticleId": 96002760, "Title": "A multivariate binary decision tree classifier based on shallow neural network", "Abstract": "In this paper, a novel decision tree classifier based on shallow neural networks with piecewise and nonlinear transformation activation functions are presented. A shallow neural network is recursively employed into linear and non-linear multivariate binary decision tree methods which generates splitting nodes and classifier nodes. Firstly, a linear multivariate binary decision tree with a shallow neural network is proposed which employs a rectified linear unit function. Secondly, there is presented a new activation function with non-linear property which has good generalization ability in learning process of neural networks. The presented method shows high generalization ability for linear and non-linear multivariate binary decision tree models which are called a Neural Network Decision Tree (NNDT). The proposed models with high generalization ability ensure the classification accuracy and performance. A novel split criterion of generating the nodes which focuses more on majority objects of classes on the current node is presented and employed in the new NNDT models. Furthermore, a shallow neural network based NNDT models are converted into a hyperplane based linear and non-linear multivariate decision trees which has high speed in the processing classification decisions. Numerical experiments on publicly available datasets have showed that the presented NNDT methods outperform the existing decision tree algorithms and other classifier methods. © Marakhimov A.R., Kudaybergenov J.K., Khudaybergenov K.K., Ohundadaev U.R., 2022.", "Keywords": "hierarchical classifier; neural networks; binary tree; multivariate decision tree; activation function", "DOI": "10.17586/**************-22-4-725-733", "PubYear": 2022, "Volume": "22", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Termez State University, Termez, 190111, Uzbekistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tashkent University of Information Technologies Nukus branch named after <PERSON>, Nukus, 230113, Uzbekistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National University of Uzbekistan, Tashkent, 100174, Uzbekistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National University of Uzbekistan, Tashkent, 100174, Uzbekistan"}], "References": []}, {"ArticleId": 96002766, "Title": "Fitness inheritance in multi-objective genetic algorithms: a case study on fuzzy classification rule mining", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2022.10050295", "PubYear": 2022, "Volume": "23", "Issue": "1/2", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96002771, "Title": "Privacy-preserving edge computing offloading scheme based on whale optimization algorithm", "Abstract": "Aiming at the problem of user’s task offloading in mobile edge computing and the potential leakage of location privacy during the offloading process, a privacy-preserving computing offloading scheme based on whale optimization algorithm is proposed. Using differential privacy technology to obfuscate the user's location information, the user can make task offloading decisions according to the obfuscated distance. Considering the delay, energy consumption, and their weighted sum, the offloading problem is modeled as a convex optimization problem. Then, the whale optimization algorithm is adopted to solve this optimization problem to achieve a balance between privacy protection and resource consumption. Experiments are conducted to verify the relationship between the degree of privacy leakage, the computation-offloading cost and real distance, privacy-preserving impact factor, the respective weights of time delay and energy consumption The experimental results show that the offloading scheme proposed in this paper has good performance in terms of cost and privacy protection.", "Keywords": "Edge computing; Computing offloading; Differential privacy; Whale optimization algorithm", "DOI": "10.1007/s11227-022-04756-1", "PubYear": 2023, "Volume": "79", "Issue": "3", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information Engineering, Hebei University, Baoding, China; Information Technology Center, Hebei University, Baoding, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Information Engineering, Hebei University, Baoding, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information Engineering, Hebei University, Baoding, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Technology Center, Hebei University, Baoding, China; Network and Experiment Management Center, Xinjiang University of Science and Technology, Korla, China"}], "References": [{"Title": "User allocation‐aware edge cloud placement in mobile edge computing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "5", "Page": "489", "JournalTitle": "Software: Practice and Experience"}, {"Title": "A Survey on Task Offloading in Multi-access Edge Computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "102225", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "An identity privacy scheme for blockchain‐based on edge computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "e6545", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Computation offloading and resource allocation based on distributed deep learning and software defined mobile edge computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "108732", "JournalTitle": "Computer Networks"}, {"Title": "Survey on computation offloading in UAV-Enabled mobile edge computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "201", "Issue": "", "Page": "103341", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Task offloading mechanism based on federated reinforcement learning in mobile edge computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "2", "Page": "492", "JournalTitle": "Digital Communications and Networks"}]}, {"ArticleId": 96002787, "Title": "Compensation of external disturbances for MIMO systems with control delay", "Abstract": "The problem of compensation of external disturbing influences for MIMO system with input delay is important and relevant. A solution to this problem is proposed in the problems of dynamic objects control and in a number of others. The proposed method is based on the principle of an internal model and requires the identification of perturbation parameters. At the first stage, a scheme for extracting a disturbance is presented which is represented as a sinusoidal signal with an unknown frequency, amplitude, and phase. At the second stage, the problem of identifying the frequencies of a sinusoidal and multisinusoidal signal is solved. In the last stage, an algorithm for stabilizing the state of the object to zero is developed using feedback. A new scheme for compensating external disturbances for a MIMO system with an input delay is proposed. A new algorithm for identifying the frequencies of a multisinusoidal signal is proposed. The analysis of the possibilities of the proposed estimation method using computer simulation in the MATLAB Simulink environment is carried out. The developed method can be effectively applied to a wide class of applied tasks related to the control of robots and robotic manipulators for various purposes. © The Authors.", "Keywords": "adaptive control; MIMO systems; identification; internal model; delay", "DOI": "10.17586/**************-22-4-666-673", "PubYear": 2022, "Volume": "22", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 96002802, "Title": "Optimization of Intelligent Display Mode of Museum Cultural Relics Based on Intelligent Wireless Sensor Network", "Abstract": "<p>The traditional way of museum exhibition is physical exhibition, which is essentially restricted by the venue, time, space, and display conditions and is no longer applicable to the display form of modern museum exhibits. Therefore, with the development of a large number of modern technologies such as information technology, wireless sensor technology and image processing technology, the way of museum cultural relics display also tends to be more intelligent and intelligent. Based on this, this paper will set up corresponding gateway nodes, routing nodes, and corresponding terminal nodes for each node of the corresponding museum based on the intelligent wireless sensor network technology; transmit the audio impact information of the corresponding museum exhibits through the terminal nodes; and automatically send the node short address information of the corresponding information to the corresponding analysis end in real time for analysis and processing, and then based on the information characteristics of the corresponding exhibits, at the same time, combined with virtual fusion and human-computer interaction technology, a set of augmented reality application system for tourist mobile terminal is developed. Abandoning the disadvantages of traditional museum display methods, this paper creatively designs the corresponding museum heritage intelligent display workflow based on intelligent wireless sensor network through C/S architecture and finally realizes the enhancement effect of virtual reality on tourism terminals. In order to verify the superiority of the intelligent display mode of museum cultural relics based on intelligent wireless sensor network proposed in this paper, this paper compares it with the traditional display mode. The experimental results show that this paper has obvious advantages in the display comprehensiveness, visitor satisfaction, and display effectiveness, which further improves the effect of museum cultural relics display.</p>", "Keywords": "", "DOI": "10.1155/2022/1961700", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Fine Arts, Suzhou Vocational University, Suzhou, Jiangsu 215000, China"}], "References": [{"Title": "Extending Fitts’ law in three-dimensional virtual environments with current low-cost virtual reality technology", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "102413", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Impact of immersing university and high school students in educational linear narratives using virtual reality technology", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "104005", "JournalTitle": "Computers & Education"}, {"Title": "A cluster-tree-based energy-efficient routing protocol for wireless sensor networks with a mobile sink", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "6", "Page": "6078", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "AIDM: artificial intelligent for digital museum autonomous system with mixed reality and software-driven data collection and analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "1", "Page": "1", "JournalTitle": "Automated Software Engineering"}]}, {"ArticleId": ********, "Title": "Partial impulse observability of linear descriptor systems", "Abstract": "A research paper in this journal vol. 61, no. 3, pp. 427–434, 2012, by <PERSON><PERSON>, provides a functional observer design for linear descriptor systems under the partial impulse observability condition. The observer design is correct, but there was a flaw in the algebraic criterion characterizing partial impulse observability. In the present paper, we derive a novel characterization of partial impulse observability in terms of a simple rank condition involving the system coefficient matrices and an alternative characterization in terms of the Wong sequences.", "Keywords": "Linear descriptor systems ; Differential–algebraic equations ; Partial impulse observability ; Wong sequences", "DOI": "10.1016/j.sysconle.2022.105352", "PubYear": 2022, "Volume": "168", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology Patna, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universität Paderborn, Institut für Mathematik, Warburger Str. 100, 33098 Paderborn, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology Patna, India;Corresponding author"}], "References": []}, {"ArticleId": 96003138, "Title": "An Efficient Deep Learning Model with Interrelated Tagging Prototype with Segmentation for Telugu Optical Character Recognition", "Abstract": "<p>More than 66 million people in India speak Telugu, a language that dates back thousands of years and is widely spoken in South India. There has not been much progress reported on the advancement of Telugu text Optical Character Recognition (OCR) systems. Telugu characters can be composed of many symbols joined together. OCR is the process of turning a document image into a text-editable one that may be used in other applications. It saves a great deal of time and effort by not having to start from scratch each time. There are hundreds of thousands of different combinations of modifiers and consonants when writing compound letters. Symbols joined to one another form a compound character. Since there are so many output classes in Telugu, there’s a lot of interclass variation. Additionally, there are not any Telugu OCR systems that take use of recent breakthroughs in deep learning, which prompted us to create our own. When used in conjunction with a word processor, an OCR system has a significant impact on real-world applications. In a Telugu OCR system, we offer two ways to improve symbol or glyph segmentation. When it comes to Telugu OCR, the ability to recognise that Telugu text is crucial. In a picture, connected components are collections of identical pixels that are connected to one another by either 4- or 8-pixel connectivity. These connected components are known as glyphs in Telugu. In the proposed research, an efficient deep learning model with Interrelated Tagging Prototype with Segmentation for Telugu Text Recognition (ITP-STTR) is introduced. The proposed model is compared with the existing model and the results exhibit that the proposed model’s performance in text recognition is high.</p>", "Keywords": "", "DOI": "10.1155/2022/1059004", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering, GITAM Deemed-to-be-University, Hyderabad, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering, GITAM Deemed-to-be-University, Hyderabad, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Institute of Aeronautical Engineering, Hyderabad, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering, Symbiosis Institute of Technology, Pune, Symbiosis International (Deemed University), Pune, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Information Technology, RVR & JC College of Engineering, Guntur, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Vishwakarma Institute of Information Technology, Pune, Maharashtra, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, IOT-HH Campus Ambo University, Ethiopia"}], "References": [{"Title": "Deep Learning with Backtracking Search Optimization Based Skin Lesion Diagnosis Model", "Authors": "<PERSON>. <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "1", "Page": "1297", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96003209, "Title": "Digitized Visual Transformation of Grotto Art Using Deep Learning and Virtual Reality Technology", "Abstract": "<p>The purpose is to convert the contents of the grotto murals into a digital vision based on deep learning (DL) and virtual reality (VR) technology to protect the precious grotto murals. The grotto murals are analyzed and modeled by introducing the related concepts of the digital library, using VR technology, and taking the cultural relics data acquisition technology as the basic framework. Then, the cultural relics information collection technology under DL is combined with VR technology, the color and materialization concepts of grotto murals are integrated, and a digital recognition model of grotto mural restoration and simulation is constructed. The color analysis scheme of grotto murals is established. Through the analysis of color and material, the comprehensive recognition rate of this color model can reach 70%, and the material recognition of color painting can also ensure one-to-one restoration. Besides, the model can be applied to the simulation and restoration of grotto murals and provide some help for the protection of grotto murals. This paper has practical application value for the protection and restoration of mural art.</p>", "Keywords": "", "DOI": "10.1155/2022/5106036", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Fine Arts, Sichuan University of Science and Engineering, Zigong 643000, China;The Catholic University of Korea, Bucheon, Republic of Korea"}], "References": [{"Title": "Attention Guided Low-Light Image Enhancement with a Large Scale Low-Light Simulation Dataset", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "7", "Page": "2175", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 96003371, "Title": "Analysis of ownership network of European companies using gravity models", "Abstract": "<p>Social network analysis is increasingly applied to modeling regional relationships. However, in this scenario, we cannot ignore the geographical economic and technological nature of the relationships. In this study, the tools of social network analysis and the gravity model are combined. Our study is based on the Amadeus database of European organizations, which includes 24 million companies. The ownership of parent subsidiaries was modeled using economic, technological, and geographic factors. Ownership was aggregated to the NUTS 3 regional level, to which average corporate profitability indicators, the GDP per capita characterizing the economic environment, and the number of patents, which is a proxy of the technological environment, were assigned to NUTS 3 regions. The formation of the ownership network between 2010 and 2018 was characterized using this dataset. As the proposed model accurately describes the formation of ownership relationships marked with edges, it is possible to estimate network properties, such as modularity and centrality.</p>", "Keywords": "Gravity models;Regional analysis;Temporal and spatial networks;Distance-based modules", "DOI": "10.1007/s41109-022-00501-y", "PubYear": 2022, "Volume": "7", "Issue": "1", "JournalId": 5330, "JournalTitle": "Applied Network Science", "ISSN": "", "EISSN": "2364-8228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Tibor <PERSON>", "Affiliation": "Department of Quantitative Methods, University of Pannonia, Veszprém, Hungary"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Quantitative Methods, University of Pannonia, Veszprém, Hungary"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computational Sciences, Wigner Research Centre for Physics, Budapest, Hungary; Institute of Data Analytics and Information Systems, Corvinus University of Budapest, Budapest, Hungary"}], "References": [{"Title": "Structure and influence in a global capital–ownership network", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Applied Network Science"}]}, {"ArticleId": 96003513, "Title": "Making Bipedal Robot Experiments Reproducible and Comparable: The Eurobench Software Approach", "Abstract": "<p>This study describes the software methodology designed for systematic benchmarking of bipedal systems through the computation of performance indicators from data collected during an experimentation stage. Under the umbrella of the European project Eurobench, we collected approximately 30 protocols with related testbeds and scoring algorithms, aiming at characterizing the performances of humanoids, exoskeletons, and/or prosthesis under different conditions. The main challenge addressed in this study concerns the standardization of the scoring process to permit a systematic benchmark of the experiments. The complexity of this process is mainly due to the lack of consistency in how to store and organize experimental data, how to define the input and output of benchmarking algorithms, and how to implement these algorithms. We propose a simple but efficient methodology for preparing scoring algorithms, to ensure reproducibility and replicability of results. This methodology mainly constrains the interface of the software and enables the engineer to develop his/her metric in his/her favorite language. Continuous integration and deployment tools are then used to verify the replicability of the software and to generate an executable instance independent of the language through dockerization. This article presents this methodology and points at all the metrics and documentation repositories designed with this policy in Eurobench. Applying this approach to other protocols and metrics would ease the reproduction, replication, and comparison of experiments.</p>", "Keywords": "Software; Benchmarking; replicability; exoskeleton; humanoid; algorithm; Performance indicator", "DOI": "10.3389/frobt.2022.951663", "PubYear": 2022, "Volume": "9", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TECNALIA, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "TECNALIA, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "TECNALIA, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Spanish National Research Council (CSIC), Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Spanish National Research Council (CSIC), Spain; Universidad Politécnica de Madrid, Spain"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Optimization, Robotics and Biomechanics, Germany"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Systems Design Engineering, Canada"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "IUVO S. r.l., Italy"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "IUVO S. r.l., Italy"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Össur Iceland ehf, Iceland"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Department of Biomechanical Engineering, Netherlands"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Center for Clinical Neuroscience, Spain"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "Center for Clinical Neuroscience, Spain"}, {"AuthorId": 14, "Name": "Clara B. Sanz-Morère", "Affiliation": "Center for Clinical Neuroscience, Spain"}, {"AuthorId": 15, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Clinical Neuroscience, Spain"}, {"AuthorId": 16, "Name": "<PERSON>", "Affiliation": "Spanish National Research Council (CSIC), Spain"}], "References": [{"Title": "Benchmarking Wearable Robots: Challenges and Recommendations From Functional, User Experience, and Methodological Perspectives", "Authors": "<PERSON>; <PERSON>-<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "168", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 96003572, "Title": "Assessing rheoencephalography dynamics through analysis of the interactions among brain and cardiac networks during general anesthesia", "Abstract": "<p>Cerebral blood flow (CBF) reflects the rate of delivery of arterial blood to the brain. Since no nutrients, oxygen or water can be stored in the cranial cavity due to space and pressure restrictions, a continuous perfusion of the brain is critical for survival. Anesthetic procedures are known to affect cerebral hemodynamics, but CBF is only monitored in critical patients due, among others, to the lack of a continuous and affordable bedside monitor for this purpose. A potential solution through bioelectrical impedance technology, also known as rheoencephalography (REG), is proposed, that could fill the existing gap for a low-cost and effective CBF monitoring tool. The underlying hypothesis is that REG signals carry information on CBF that might be recovered by means of the application of advanced signal processing techniques, allowing to track CBF alterations during anesthetic procedures. The analysis of REG signals was based on geometric features extracted from the time domain in the first place, since this is the standard processing strategy for this type of physiological data. Geometric features were tested to distinguish between different anesthetic depths, and they proved to be capable of tracking cerebral hemodynamic changes during anesthesia. Furthermore, an approach based on Poincaré plot features was proposed, where the reconstructed attractors form REG signals showed significant differences between different anesthetic states. This was a key finding, providing an alternative to standard processing of REG signals and supporting the hypothesis that REG signals do carry CBF information. Furthermore, the analysis of cerebral hemodynamics during anesthetic procedures was performed by means of studying causal relationships between global hemodynamics, cerebral hemodynamics and electroencephalogram (EEG) based-parameters. Interactions were detected during anesthetic drug infusion and patient positioning (Trendelenburg positioning and passive leg raise), providing evidence of the causal coupling between hemodynamics and brain activity. The provided alternative of REG signal processing confirmed the hypothesis that REG signals carry information on CBF. The simplicity of the technology, together with its low cost and easily interpretable outcomes, should provide a new opportunity for REG to reach standard clinical practice. Moreover, causal relationships among the hemodynamic physiological signals and brain activity were assessed, suggesting that the inclusion of REG information in depth of anesthesia monitors could be of valuable use to prevent unwanted CBF alterations during anesthetic procedures.</p>", "Keywords": "general anesthesia; cerebral blood flow; Electroencephalography; Rheoencephalography; Poincaré plot descriptors; Granger causality", "DOI": "10.3389/fnetp.2022.912733", "PubYear": 2022, "Volume": "2", "Issue": "", "JournalId": 89151, "JournalTitle": "Frontiers in Network Physiology", "ISSN": "", "EISSN": "2674-0109", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Biomedical Engineering Research Centre, Spain; Research and Development Department, Quantium Medical, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Biomedical Engineering Research Centre, Spain; Research and Development Department, Quantium Medical, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Research and Development Department, Quantium Medical, Spain"}, {"AuthorId": 4, "Name": "Montserrat Val<PERSON>rdú-Ferrer", "Affiliation": "Biomedical Engineering Research Centre, Spain"}], "References": []}, {"ArticleId": 96003691, "Title": "Robust privacy‐preserving federated learning framework for IoT devices", "Abstract": "<p>Federated Learning (FL) is a framework where multiple parties can train a model jointly without sharing private data. Private information protection is a critical problem in FL. However, the communication overheads of existing solutions are too heavy for IoT devices in resource-constrained environments. Additionally, they cannot ensure robustness when IoT devices become offline. In this paper, Democratic Federated Learning (DemoFL) is proposed, which is a privacy-preserving FL framework that has sufficiently low communication overheads. DemoFL involves a consensus module to ensure the system is robust. It also utilizes a tree structure to reduce the time communication overheads and realizes high robustness without reducing accuracy. The proposed algorithm reduces the communication complexity of aggregation at training by M $M$ times, M $M$ being a controllable parameter. Sufficient experiments have been conducted to evaluate the efficiency of the proposed method. The experimental results also demonstrate the practicality of the proposed framework for IoT devices in unstable environments.</p>", "Keywords": "federated learning;machine learning;multi-party computation;resource-constrained devices;secure aggregation", "DOI": "10.1002/int.22993", "PubYear": 2022, "Volume": "37", "Issue": "11", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Han", "Affiliation": "College of Computer Science and Technology Nanjing University of Aeronautics and Astronautics Nanjing China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology Nanjing University of Aeronautics and Astronautics Nanjing China"}, {"AuthorId": 3, "Name": "Chunpeng Ge", "Affiliation": "College of Computer Science and Technology Nanjing University of Aeronautics and Astronautics Nanjing China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology Nanjing University of Aeronautics and Astronautics Nanjing China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology Nanjing University of Aeronautics and Astronautics Nanjing China"}], "References": [{"Title": "A federated learning system with enhanced feature extraction for human activity recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "229", "Issue": "", "Page": "107338", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 96003712, "Title": "A sustainable supply chain for a wellness tourism center considering discount and quality of service", "Abstract": "Nowadays, the most successful and prominent wellness tourism centers are those that, consider the aspects of sustainable development, while providing memorable services with the best quality and reasonable prices. A wellness tourism center is seeking to meet the purposeful journey of wellness tourists by improving their mental, physical, and spiritual happiness. This paper presents a three-objective mathematical model to formulate the supply chain of services provided by a wellness tourism center. The purpose of this model is to establish a balance between sustainability aspects in this supply chain. The first objective seeks to maximize profits from the supply chain and the second and third objectives are to maximize customer satisfaction concerning environmental and social impacts. The proposed model includes quantitative parameters of operating costs, capacity increase cost, service capacity, and service demand. Due to the multi-objective nature of the model, the improved multi-choice goal programming method is used. Real data are collected from the Ardabil-Sarein wellness tourism center in Iran and used as a case study to validate and analyze the model. In collecting the dataset for the parameters, the three aspects of big data (3 V’s) are considered. Findings show that the model offers different types of discounts for various periods following the tourist demand. Additionally, the wellness tourism center can use the original cost to expand its range of services by offering different tours. Finally, the limitations and managerial suggestions are discussed.", "Keywords": "Wellness tourism ; Sustainable tourism ; Discount ; Quality of service ; Mathematical model ; Big data", "DOI": "10.1016/j.eswa.2022.118682", "PubYear": 2023, "Volume": "211", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Babol Noshirvani University of Technology, Babol, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Dean of Materials and Industrials Engineering Faculty, Babol Noshirvani University of Technology, Babol, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Head of Industrial Engineering Department, Babol Noshirvani University of Technology, Babol, Iran"}], "References": [{"Title": "A capacity planning approach for sustainable-resilient supply chain network design under uncertainty: A case study of vaccine supply chain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "159", "Issue": "", "Page": "107406", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 96003846, "Title": "Machine learning-based proactive social-sensor service for mental health monitoring using twitter data", "Abstract": "The social media platforms are considered an ecosystem of social sensors where each social media platform user is treated as a social sensor cloud. To overcome the limitations of large-scale mental health surveillance using traditional health administration systems. Although the existing approaches in the literature provide an online detection of mental illness, these are challenging to apply in early detection. Focusing on the Twitter platform, a generic framework is designed in this paper to support proactive mental health monitoring. Detailed data cleaning and pre-processing of the tweets are offered using regular expressions based on observed patterns to ensure accurate results in sentiment analysis. A machine learning mechanism, especially LSTM, is applied for the early detection of at-risk social sensors based on custom event definitions to overcome the limitations of traditional classifiers. The performance of the proposed mechanism has been experimented with, and it outperforms the existing approaches in terms of accurate prediction.", "Keywords": "Twitter ; Social sensor ; Sentiment analysis ; Machine learning ; Mental health", "DOI": "10.1016/j.jjimei.2022.100113", "PubYear": 2022, "Volume": "2", "Issue": "2", "JournalId": 83128, "JournalTitle": "International Journal of Information Management Data Insights", "ISSN": "2667-0968", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, The University of Sydney, Sydney, NSW 2000, Australia"}, {"AuthorId": 2, "Name": "Mahbuba Afrin", "Affiliation": "School of Electrical Engineering, Computing and Mathematical Sciences, Curtin University, Australia"}, {"AuthorId": 3, "Name": "Sajib Mistry", "Affiliation": "School of Electrical Engineering, Computing and Mathematical Sciences, Curtin University, Australia;Corresponding Author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Computing and Mathematical Sciences, Curtin University, Australia"}], "References": [{"Title": "Mobile applications aiming to facilitate immigrants’ societal integration and overall level of integration, health and mental health. Does artificial intelligence enhance outcomes?", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "106661", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Leveraging Twitter data to understand public sentiment for the COVID‐19 outbreak in Singapore", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100021", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Sentiment analysis and classification of Indian farmers’ protest using twitter data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100019", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "The impact of Instagram on young Adult's social comparison, colourism and mental health: Indian perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "1", "Page": "100057", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Negative Information Measurement at AI Edge: A New Perspective for Mental Health Monitoring", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}]}, {"ArticleId": 96004077, "Title": "Design of Underwater Thruster Fault Detection Model Based on Vibration Sensor Data: Generative Adversarial Network-based Fault Data Expansion Approach for Data Imbalance", "Abstract": "The underwater thruster is an essential driving element for underwater platforms. Since underwater thrusters may fail because of external factors, a fault detection system is necessary for reliability and safety. Among the underwater thruster fault detection and diagnosis methods, a data-driven learning method, which does not require expertise or a physical model of the platform, is applied because a rule-based method lacks flexibility and a model-based method relies heavily on expertise. Although high-quality, large-capacity datasets are essential to implementing data-driven fault detection systems, the amount of fault sensor data is relatively scarce because most underwater thrusters operate in a normal state. However, if the platform is operated in a fault state for a long time to acquire fault sensor data, performance degradation of the thruster or accidents may result. In this study, we investigated a fault detection system wherein a small number of vibration sensor datasets were used as inputs for a generative adversarial network (GAN), and new vibration sensor datasets were generated, extended, and applied to a long short-term memory neural network for fault detection in an underwater thruster. For the defects detected by the machine learning algorithm, the rotor imbalance due to a thruster blade fault or the entanglement of floating objects was analyzed. To collect the vibration sensor dataset of the thruster, a structure for an underwater experiment was designed, and a system with a stable power supply, thruster control, and the capability to acquire vibration data was developed. Vibration sensor data obtained from the experiment and those generated by the GAN were comparatively analyzed in terms of their vibration characteristics using the fast Fourier transform. After training the neural network with GAN-generated data, the fault detection system was validated using real data as prediction data. © 2022 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "confusion matrix; fault detection; generative adversarial network; long short-term memory; underwater thruster; vibration data", "DOI": "10.18494/SAM3991", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Maritime R&D Center, LIG Nex1 Co., Ltd, Gyeonggi-do, 13488, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Korea Maritime and Ocean University, Busan, 49112, South Korea, Interdisciplinary Major of Ocean Renewable Energy Engineering, Korea Maritime and Ocean University, Busan, 49112, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Korea Maritime and Ocean University, Busan, 49112, South Korea, Interdisciplinary Major of Ocean Renewable Energy Engineering, Korea Maritime and Ocean University, Busan, 49112, South Korea"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Korea Maritime and Ocean University, Busan, 49112, South Korea, Interdisciplinary Major of Ocean Renewable Energy Engineering, Korea Maritime and Ocean University, Busan, 49112, South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Underwater Vehicle Research Center, Korea Maritime and Ocean University, Busan, 49112, South Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Park", "Affiliation": "Department of Mechanical Engineering, Korea Maritime and Ocean University, Busan, 49112, South Korea"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Maritime ICT R&D Center, Korea Institute of Ocean Science and Technology, Busan, 49111, South Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Maritime ICT R&D Center, Korea Institute of Ocean Science and Technology, Busan, 49111, South Korea"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Marine Security and Safety Research Center, Korea Institute of Ocean Science and Technology, Busan, 49111, South Korea"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Choi", "Affiliation": "Department of Mechanical Engineering, Korea Maritime and Ocean University, Busan, 49112, South Korea"}], "References": []}, {"ArticleId": 96004175, "Title": "Adaptive output feedback controller of resisting large measurement noises for MIMO nonlinear systems", "Abstract": "In this paper, we propose an adaptive output feedback control law for MIMO nonlinear systems with distinct unknown measurement errors in each output. The nonlinear terms have incremental rates which include nonlinear functions of output and polynomials of output multiplying unknown constants. By introducing an observer, and two classes of particular dual adaptive high-gains and a class of exclusive constant high-gains, an output feedback controller is developed to stabilise the MIMO nonlinear system. The designed controller is able to tolerate uncertain measurement errors with a relatively wide range. Finally, some simulations are provided to corroborate the theoretical analysis.", "Keywords": "Stabilisation ; uncertainty measurement noises ; adaptive high-gain ; output feedback ; MIMO nonlinear system", "DOI": "10.1080/00207721.2022.2113175", "PubYear": 2023, "Volume": "54", "Issue": "2", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical, Engineering and New Energy, China Three Gorges University, Yichang, People's Republic of China;State Grid Hubei Direct Current Operation Research Institute, Yichang, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical, Engineering and New Energy, China Three Gorges University, Yichang, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical, Engineering and New Energy, China Three Gorges University, Yichang, People's Republic of China"}], "References": []}, {"ArticleId": 96004288, "Title": "On Petrie cycle and Petrie tour partitions of 3- and 4-regular plane graphs", "Abstract": "Given a plane graph \n\t \n\t\t \n\t\t \n$G=(V,E)$\n \n\t \n\t , a Petrie tour of G is a tour P of G that alternately turns left and right at each step. A Petrie tour partition of G is a collection \n\t \n\t\t \n\t\t \n${\\mathscr P}=\\{P_1,\\ldots,P_q\\}$\n \n\t \n\t of Petrie tours so that each edge of G is in exactly one tour \n\t \n\t\t \n\t\t \n$P_i \\in {\\mathscr P}$\n \n\t \n\t . A Petrie tour P is called a Petrie cycle if all its vertices are distinct. A Petrie cycle partition of G is a collection \n\t \n\t\t \n\t\t \n${\\mathscr C}=\\{C_1,\\ldots,C_p\\}$\n \n\t \n\t of Petrie cycles so that each vertex of G is in exactly one cycle \n\t \n\t\t \n\t\t \n$C_i \\in {\\mathscr C}$\n \n\t \n\t . In this paper, we study the properties of 3-regular plane graphs that have Petrie cycle partitions and 4-regular plane multi-graphs that have Petrie tour partitions. Given a 4-regular plane multi-graph \n\t \n\t\t \n\t\t \n$G=(V,E)$\n \n\t \n\t , a 3-regularization of G is a 3-regular plane graph \n\t \n\t\t \n\t\t \n$G_3$\n \n\t \n\t obtained from G by splitting every vertex \n\t \n\t\t \n\t\t \n$v\\in V$\n \n\t \n\t into two degree-3 vertices. G is called Petrie partitionable if it has a 3-regularization that has a Petrie cycle partition. The general version of this problem is motivated by a data compression method, tristrip , used in computer graphics. In this paper, we present a simple characterization of Petrie partitionable graphs and show that the problem of determining if G is Petrie partitionable is NP-complete.", "Keywords": "Plane graph; Petrie cycles; Petrie tours; left-right paths", "DOI": "10.1017/S0960129522000238", "PubYear": 2022, "Volume": "32", "Issue": "2", "JournalId": 10211, "JournalTitle": "Mathematical Structures in Computer Science", "ISSN": "0960-1295", "EISSN": "1469-8072", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, State University of New York at Buffalo, Buffalo, NY 14260, USA; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, The University of Alabama in Huntsville, Huntsville, AL 35899, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Engineering, University of Missouri-Kansas City, Kansas City, MO 64110, USA"}], "References": []}, {"ArticleId": 96004432, "Title": "Research on the Controllability of Software Networks Based on the Source-Driven Model", "Abstract": "The complexity of software system has been increasing with software evolution, which affects the stability of software structure. Most of the existing measurement methods focus on the analysis of the macro-characteristics of the network topology, but lacked a certain depth and expansion to explore the nature of the complexity of the software structure, for this purpose, the complex network control theory was applied to the study of software network controllability. Firstly, the Source-Driver (SD) model was established based on the system control theory, the driver node sets were obtained by the minimum input theorem in the control process of software network topology; Then the relationship between the degree and center degree, the relationship between the in-degree and the out-degree of the software network topology were further analyzed owing to the non-uniqueness of the driver node sets; Finally, the values of the four indicators in the software system were compared. Experimental results show that the driver node sets in the software networks are mainly composed of nodes with low degree values, but it does not mean that the nodes whose in-degree values and out-degree values are also low; The action on control nodes and driver nodes are not random, the controllability of the driver nodes is closely related to the in-degree, when selecting the driver node sets, the network topology characteristics should be considered comprehensively, and the nodes with high degree and center degree are the first choice. The results have important guiding significance for the control, maintenance and redesign of software architecture.", "Keywords": "Complex Networks;Software Networks;Driver Node Sets;Network Control", "DOI": "10.4236/jcc.2022.108003", "PubYear": 2022, "Volume": "10", "Issue": "8", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Sugon Big Data, Liaoning Institute of Science and Technology, Benxi, China .↑College of Information, Liaoning University, Shenyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Sugon Big Data, Liaoning Institute of Science and Technology, Benxi, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Sugon Big Data, Liaoning Institute of Science and Technology, Benxi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Sugon Big Data, Liaoning Institute of Science and Technology, Benxi, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Sugon Big Data, Liaoning Institute of Science and Technology, Benxi, China"}], "References": []}, {"ArticleId": 96004493, "Title": "Robust Table Detection and Structure Recognition from Heterogeneous Document Images", "Abstract": "We introduce a new table detection and structure recognition approach named RobusTabNet to detect the boundaries of tables and reconstruct the cellular structure of each table from heterogeneous document images. For table detection, we propose to use CornerNet as a new region proposal network to generate higher quality table proposals for Faster R-CNN, which has significantly improved the localization accuracy of Faster R-CNN for table detection. Consequently, our table detection approach achieves state-of-the-art performance on three public table detection benchmarks, namely cTDaR TrackA, PubLayNet and IIIT-AR-13K, by only using a lightweight ResNet-18 backbone network. Furthermore, we propose a new split-and-merge based table structure recognition approach, in which a novel spatial CNN based separation line prediction module is proposed to split each detected table into a grid of cells, and a Grid CNN based cell merging module is applied to recover the spanning cells. As the spatial CNN module can effectively propagate contextual information across the whole table image, our table structure recognizer can robustly recognize tables with large blank spaces and geometrically distorted (even curved) tables. Thanks to these two techniques, our table structure recognition approach achieves state-of-the-art performance on three public benchmarks, including SciTSR, PubTabNet and cTDaR TrackB2-Modern. Moreover, we have further demonstrated the advantages of our approach in recognizing tables with complex structures, large blank spaces, as well as geometrically distorted or even curved shapes on a more challenging in-house dataset.", "Keywords": "Table detection ; Table structure recognition ; Corner detection ; Spatial CNN ; Grid CNN ; Split-and-merge", "DOI": "10.1016/j.patcog.2022.109006", "PubYear": 2023, "Volume": "133", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "Chixiang Ma", "Affiliation": "Department of EEIS, University of Science and Technology of China, Hefei, 230026, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Research Asia, Beijing, 100080, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Research Asia, Beijing, 100080, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Research Asia, Beijing, 100080, China"}], "References": [{"Title": "ReLaText: Exploiting visual relationships for arbitrary-shaped scene text detection with graph convolutional networks", "Authors": "<PERSON><PERSON><PERSON> Ma; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107684", "JournalTitle": "Pattern Recognition"}, {"Title": "Split, Embed and Merge: An accurate table structure recognizer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "108565", "JournalTitle": "Pattern Recognition"}, {"Title": "Table detection in business document images by message passing networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "108641", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 96004567, "Title": "Automatic shape adaptation scheme planning for CAD models in direct modeling", "Abstract": "Designing a new CAD model by adapting a retrieved candidate model offers several advantages; however, shape adaptation is often a tough job. To facilitate shape adaptation, direct modeling (technology) provides a very flexible and intuitive approach. However, based on new design requirements, the research on automatic and effective methods to determine the geometries on the candidate model that need adapting and how to adapt these geometries has rarely been reported till date. The shape adaptation based on direct modeling is often dependent on manual processing and thus it is usually a tedious process. To account for this, a novel automatic shape adaptation scheme planning approach is proposed in this study. The candidate model is usually retrieved using a more shape-simple query model that indicates the overall shape meeting the main functional requirements of a final product (i.e., new primary shape requirements); therefore, this study focuses on the automatic adaptation of the shape of the candidate model to that of the query model to accelerate its entire adaptation progress. First, to describe the shape requirements indicated by a CAD model, an attributed face-layout graph (AFLG) is presented. Further, by comparing the candidate model’s AFLG to that of the query model, the geometries (i.e., faces) on the candidate model requiring adaptation can be determined. Next, integrating with a geometric detail reuse strategy and a novel incremental adapting method on the presented graph, an improved particle swarm optimization method is developed to determine an optimized shape adaptation scheme (i.e., an optimized direct modeling operation sequence) based on the determined geometries. Finally, by applying the above-mentioned scheme, the face layout and shape parameters are smoothly transferred from the query model to the candidate model as shape requirements, and the shape of the candidate model gets automatically adapted to the query model while its geometric details are reused adaptively. Besides, a prototype system is also implemented, and the experimental results are analyzed to verify the effectiveness and characteristics of the proposed approach.", "Keywords": "Attributed face-layout graph ; Topology-consistent shape adaptation ; Graph-incremental shape adaptation ; Direct modeling planning ; CAD", "DOI": "10.1016/j.cad.2022.103405", "PubYear": 2022, "Volume": "153", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Pan", "Affiliation": "School of Media and Design, Hangzhou Dianzi University, Hangzhou, 310018, PR China;State Key Laboratory of CAD&CG, Zhejiang University, Hangzhou, 310058, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Media and Design, Hangzhou Dianzi University, Hangzhou, 310018, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Media and Design, Hangzhou Dianzi University, Hangzhou, 310018, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of CAD&CG, Zhejiang University, Hangzhou, 310058, PR China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Media and Design, Hangzhou Dianzi University, Hangzhou, 310018, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Media and Design, Hangzhou Dianzi University, Hangzhou, 310018, PR China"}], "References": [{"Title": "A constraint model for assembly planning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "196", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "ASPPR: A New Assembly Sequence and Path Planner/Replanner for Monotone and Nonmonotone Assembly Planning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "102828", "JournalTitle": "Computer-Aided Design"}, {"Title": "A decision-support method for information inconsistency resolution in direct modeling of CAD models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "", "Page": "101087", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Qualitative case-based reasoning and learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "283", "Issue": "", "Page": "103258", "JournalTitle": "Artificial Intelligence"}, {"Title": "Automatic Update of Feature Model After Direct Modeling Operation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "1", "Page": "170", "JournalTitle": "Computer-Aided Design and Applications"}, {"Title": "An effective retrieval method for 3D models in plastic injection molding for process reuse", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107034", "JournalTitle": "Applied Soft Computing"}, {"Title": "Cyber-physical assembly system-based optimization for robotic assembly sequence planning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "452", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Common design structures and substitutable feature discovery in CAD databases", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "101261", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 96004669, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1874-5482(22)00045-2", "PubYear": 2022, "Volume": "38", "Issue": "", "JournalId": 6406, "JournalTitle": "International Journal of Critical Infrastructure Protection", "ISSN": "1874-5482", "EISSN": "2212-2087", "Authors": [], "References": []}, {"ArticleId": 96004671, "Title": "Predictive Modeling Classification of Post-Flood and Abrasion Effects With Deep Learning Approach", "Abstract": "<p>Floods and abrasion are the most common disasters in Indonesia. A lot of data is collected from post-flood and abrasion disasters. From the data released by BNPB, disaster data is directly based on the occurrence of disasters. From these data, we will test predictive modeling classification with a deep learning approach.  Big data can be made through classification and predictive modeling. Our proposed model is a classification of predictive modeling of post-flood and abrasion data using the H2O deep learning approach. Deep learning H2O models can also be evaluated with specific model metrics, termination metrics, and performance charts. This approach is used to optimize the performance and accuracy of predictions during the modeling process using our dataset pool training. The big data to be processed will generate new knowledge for policies in decision making. Big data performance modeled with Deep Learning H2O is used to predict the Survival attributes of post-flood and abrasion sample datasets. The best metric performance is obtained from the maxout activation function with a 200-200 unit layer that gets an accuracy of 93.49% with AUC: 0.808 +/- 0.022 (micro average: 0.808).</p>", "Keywords": "Deep Learning;Flood;Clasification;Predictive", "DOI": "10.38043/tiers.v3i1.3604", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 94001, "JournalTitle": "TIERS Information Technology Journal", "ISSN": "2723-4533", "EISSN": "2723-4541", "Authors": [{"AuthorId": 1, "Name": "Finki Dona Marleny", "Affiliation": "Universitas Muhammadiyah <PERSON>"}, {"AuthorId": 2, "Name": "Mambang Mambang", "Affiliation": "Universitas Sari Mulia"}], "References": []}, {"ArticleId": 96004727, "Title": "Building cryptographic schemes based on elliptic curves over rational numbers", "Abstract": "The possibility of using elliptic curves over the rational field of non-zero ranks in cryptographic schemes is studied. For the first time, the construction of cryptosystems is proposed the security of which is based on the complexity of solving the knapsack problem on elliptic curves over rational numbers of non-zero ranks. A new approach to the use of elliptic curves for cryptographic schemes is proposed. A few experiments have been carried out to estimate the heights characteristic of points on elliptic curves of infinite order. A model of a cryptosystem resistant to computations on a quantum computer and based on rational points of an infinite order curve is proposed. A study of the security and effectiveness of the proposed scheme has been carried out. An attack on the secret search in such a cryptosystem is implemented and it is shown that the complexity of the attack is exponential. The proposed solution can be applied in the construction of real cryptographic schemes as well as cryptographic protocols. © Davydov V.V., Dakuo J.-MN., Johanson I.D., Khutsaeva A.F., 2022.", "Keywords": "elliptic curves; rational numbers; curve rank; asymmetric encryption; knapsack problem", "DOI": "10.17586/**************-22-4-674-680", "PubYear": 2022, "Volume": "22", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "J.-<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 96004731, "Title": "Optimal control for a class of impulsive switched systems", "Abstract": "", "Keywords": "", "DOI": "10.1080/23307706.2022.2113829", "PubYear": 2023, "Volume": "10", "Issue": "4", "JournalId": 30013, "JournalTitle": "Journal of Control and Decision", "ISSN": "2330-7706", "EISSN": "2330-7714", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Shandong Normal University, Jinan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, People’s Republic of China"}], "References": [{"Title": "Exponential stability of impulsive stochastic differential equations with Markovian switching", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "162", "Issue": "", "Page": "105178", "JournalTitle": "Systems & Control Letters"}, {"Title": "A penalty function-based random search algorithm for optimal control of switched systems with stochastic constraints and its application in automobile test-driving with gear shifts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "45", "Issue": "", "Page": "101218", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}]}, {"ArticleId": 96004735, "Title": "An algorithm for generating design solutions for data and design-production procedures management at the stages of the lifecycle of an electronic product", "Abstract": "The integration of automated systems at enterprises provides information support for the stages of the product life cycle and electronic interaction between employees in the process of performing work. This means that performing design and production procedures employees of enterprises solve various design problems. The tasks are related to the analysis of a large amount of information about the product presented in the form of ontology. This requires the development of an algorithm to extract information from the ontology based on given requirements. The developed algorithm consists of several stages. At the first stage, a search space for design solutions is formed. At the second stage, for each variant of the design solution the values of the objective function are calculated, and the best design solution is selected. The best solution is the one for which the condition of minimizing the value of the objective function is satisfied. The third stage is associated with the choice of design solutions that are close to the found best solution. The best solution is determined by the computed Hamming distance. The fourth and fifth stages are characterized by the analysis of the elements of the set of options for design solutions and the formation of the desired design solution Sequences of actions performed at the stages of the algorithm for generating design solutions are proposed. The proposed algorithm can be implemented at enterprises to provide a procedure for solving design problems. The presented algorithm allows the development of signatures and semantics of unified services for the use of a digital passport. © Julia V. Donetskaya 2022.", "Keywords": "algorithm; digital passport; design solution; ontology", "DOI": "10.17586/**************-22-4-681-690", "PubYear": 2022, "Volume": "22", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "Ju.V<PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 96004744, "Title": "Evaluation of large shareholder's monitoring or tunnelling behaviour in companies accepted in Tehran Stock Exchange", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2022.10050296", "PubYear": 2022, "Volume": "23", "Issue": "1/2", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96004789, "Title": "Transferring artificial intelligence practices between collaborative robotics and autonomous driving", "Abstract": "Purpose Collaborative robotics and autonomous driving are fairly new disciplines, still with a long way to go to achieve goals, set by the research community, manufacturers and users. For technologies like collaborative robotics and autonomous driving, which focus on closing the gap between humans and machines, the physical, psychological and emotional needs of human individuals becoming increasingly important in order to ensure effective and safe human–machine interaction. The authors' goal was to conceptualize ways to combine experience from both fields and transfer artificial intelligence knowledge from one to another. By identifying transferable meta-knowledge, the authors will increase quality of artificial intelligence applications and raise safety and contextual awareness for users and environment in both fields. Design/methodology/approach First, the authors presented autonomous driving and collaborative robotics and autonomous driving and collaborative robotics' connection to artificial intelligence. The authors continued with advantages and challenges of both fields and identified potential topics for transferrable practices. Topics were divided into three time slots according to expected research timeline. Findings The identified research opportunities seem manageable in the presented timeline. The authors' expectation was that autonomous driving and collaborative robotics will start moving closer in the following years and even merging in some areas like driverless and humanless transport and logistics. Originality/value The authors' findings confirm the latest trends in autonomous driving and collaborative robotics and expand them into new research and collaboration opportunities for the next few years. The authors' research proposal focuses on those that should have the most positive impact to safety, complement, optimize and evolve human capabilities and increase productivity in line with social expectations. Transferring meta-knowledge between fields will increase progress and, in some cases, cut some shortcuts in achieving the aforementioned goals.", "Keywords": "Robotics;Artificial intelligence;Automation;Decision-making;Intelligent agents", "DOI": "10.1108/K-05-2022-0679", "PubYear": 2023, "Volume": "52", "Issue": "9", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON>an", "Affiliation": "Institute of Computer Science , Faculty of Electrical Engineering and Computer Sciences , University of Maribor , Maribor, Slovenia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computer Science , Faculty of Electrical Engineering and Computer Sciences , University of Maribor , Maribor, Slovenia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Robotics , Faculty of Electrical Engineering and Computer Science , University of Maribor , Maribor, Slovenia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Robotics , Faculty of Electrical Engineering and Computer Science , University of Maribor , Maribor, Slovenia"}], "References": [{"Title": "Human-robot coexistence and interaction in open industrial cells", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101846", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Hybrid reality development - can social responsibility concepts provide guidance?", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "3", "Page": "676", "JournalTitle": "Kybernetes"}, {"Title": "An adaptive human sensor framework for human–robot collaboration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "1-2", "Page": "1233", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Identification and classification of risk factors for human-robot collaboration from a system-wide perspective", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "107827", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Risk-aware Decision-making in Human-multi-robot Collaborative Search: A Regret Theory Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "105", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Deliberative safety for industrial intelligent human–robot collaboration: Regulatory challenges and solutions for taking the next step towards industry 4.0", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "102386", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 96004854, "Title": "Information reconstruction from noisy channel using ghost imaging method with spectral multiplexing in visible range", "Abstract": "The ghost imaging technique allows us to recover information about an object in conditions of noisy transmission channels, commensurate with the intensity of the speckle structures involved in the reconstruction. One of the main disadvantages of this technique is relatively slow reconstruction speed. This limits its applicability for study of dynamic processes or fast-moving objects. In this paper, we propose a modification of the computational ghost imaging technique that allows us to overcome this limitation. It is shown that the spectral multiplexing of the speckle patterns speeds up the image reconstruction. Increase in the number of spectral channels from 4 to 10 leads to the increase of the signal-tonoise ratio by the factor of 6. Simultaneously, under the same conditions and with the same number of measurements classical monochrome ghost imaging does not reconstruct the picture at all. This makes the proposed technique attractive for high-speed demanding applications such as communications and remote sensing. © 2022, ITMO University. All rights reserved.", "Keywords": "ghost imaging; supercontinuum; spatial light modulator; data transmission; remote sensing", "DOI": "10.17586/**************-22-4-812-816", "PubYear": 2022, "Volume": "22", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "V.S<PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 96004862, "Title": "A Generic Type System for Higher-Order Ψ-calculi", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.368.3", "PubYear": 2022, "Volume": "368", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96004889, "Title": "Energy-saving Service Offloading for the Internet of Medical Things Using Deep Reinforcement Learning", "Abstract": "<p>As a critical branch of the Internet of Things (IoT) in the medicine industry, the Internet of Medical Things (IoMT) significantly improves the quality of healthcare due to its real-time monitoring and low medical cost. Benefiting from edge and cloud computing, IoMT is provided with more computing and storage resources near the terminal to meet the low-delay requirements of computation-intensive services. However, the service offloading from health monitoring units (HMUs) to edge servers generates additional energy consumption. Fortunately, artificial intelligence (AI), which has developed rapidly in recent years, has proved effective in some resource allocation applications. Taking both energy consumption and delay into account, we propose an energy-aware service offloading algorithm under an end-edge-cloud collaborative IoMT system with Asynchronous Advantage Actor-Critic (A3C), named ECAC. Technically, ECAC uses the structural similarity between the natural distributed IoMT system and A3C, whose parameters are asynchronously updated. Besides, due to the typical delay-sensitivity mechanism and time-energy correction, ECAC can adjust dynamically to the diverse service types and system requirements. Finally, the effectiveness of ECAC for IoMT is proved on real data.</p>", "Keywords": "Service offloading; asynchronous advantage actor-critic; internet of medical things; deep reinforcement learning", "DOI": "10.1145/3560265", "PubYear": 2023, "Volume": "19", "Issue": "3", "JournalId": 12743, "JournalTitle": "ACM Transactions on Sensor Networks", "ISSN": "1550-4859", "EISSN": "1550-4867", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Software, Jiangsu Collaborative Innovation Center of Atmospheric Environment and Equipment Technology (CICAEET), Nanjing University of Information Science and Technology, China and The State Key Lab. for Novel Software Technology, Nanjing University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Software, Nanjing University of Information Science and Technology, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Software Competence Center Hagenberg GmbH, Softwarepark, Austria and SPCAI, Pak-Austria Fachhochschule-Institute of Applied Sciences and Technology, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Mathematics and Information Science, Nanjing Normal University of Special Education, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of VR and Intelligent System, Alibaba Business School, Hangzhou Normal University, China"}], "References": [{"Title": "Efficient computation offloading for Internet of Vehicles in edge computing-assisted 5G networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "4", "Page": "2518", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Intelligence in the Internet of Medical Things era: A systematic review of current and future trends", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "644", "JournalTitle": "Computer Communications"}, {"Title": "Secure healthcare monitoring framework integrating NDN-based IoT with edge cloud", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "320", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "An effective feature engineering for DNN using hybrid PCA-GWO for intrusion detection in IoMT architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>.", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "139", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 96004946, "Title": "Application of job shop scheduling approach in green patient flow optimization using a hybrid swarm intelligence", "Abstract": "With the increasing demand for hospital services amidst the COVID-19 pandemic, allocation of limited public resources and management of healthcare services are of paramount importance. In the field of patient flow scheduling, previous research primarily focused on classical-based objective functions, while ignoring environmental-based objective functions. This study presents a flexible job shop scheduling problem to optimize patient flow and, thereby, minimize the total carbon footprint, as the sustainability-based objective function. Since flexible job shop scheduling is an NP-hard problem, a metaheuristic optimization algorithm, called Chaotic Salp Swarm Algorithm Enhanced with Opposition-Based Learning and Sine Cosine (CSSAOS), was developed. The proposed algorithm integrates the Salp Swarm Algorithm (SSA) with chaotic maps to update the position of followers, the sine cosine algorithm to update the leader position, and opposition-based learning for a better exploration of the search space. generating more accurate solutions. The proposed method was successfully applied in a real-world case study and demonstrated better performance than other well-known metaheuristic algorithms, including differential evolution, genetic algorithm, grasshopper optimization algorithm, SSA based on opposition-based learning, quantum evolutionary SSA, and whale optimization algorithm. In addition, it was found that the proposed method is scalable to different sizes and complexities.", "Keywords": "chaotic map;job shop scheduling;patient flow;salp swarm algorithm;swarm intelligence", "DOI": "10.1016/j.cie.2022.108603", "PubYear": 2022, "Volume": "172", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computational Intelligence &amp; Intelligent Research Group, Business &amp; Economics School, Persian Gulf University, Bushehr 75168, Iran."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computational Intelligence &amp; Intelligent Research Group, Business &amp; Economics School, Persian Gulf University, Bushehr 75168, Iran."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering &amp; IT, University of Technology Sydney, Australia."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Health and Social Care Modelling Group, School of Computer Science and Engineering, University of Westminster, London, UK."}], "References": []}, {"ArticleId": 96005070, "Title": "The Application of Artificial Intelligence Technologies in Digital Humanities: Applying to Dunhuang Culture Inheritance, Development, and Innovation", "Abstract": "<p>This article will focus on the relationship between Digital Humanities and Artificial Intelligence and will discuss the methodology of applying Artificial Intelligence in Digital Humanities; specifically, the article will target the applications in Dunhuang culture studying. The development of digital humanities facilitated by computers and the future research direction will be studied. The article aims to propose specific methods of applying Artificial Intelligence to Digital Humanities to facilitate the inheritance, development, and innovation of traditional culture. By sorting out the development process of digital humanities and combining it with the newest development direction of Artificial Intelligence forecasted by specialists in the area, the study will discuss the possible applications of the technologies in intangible cultural heritage studies. The methods include utilizing Smart Data to obtain structured and visual cultural heritage, implying Cross-media Intelligence to help reconstruct and propagate culture, and using Human-machine Association for the renovation and recreation of cultural heritage. Additionally, the specific methods of applying these methodologies to Dunhuang culture studying will be discussed, whose object is to realize natural language analysis, image style transfer using Smart Data, build digital museums using VR and AR, and restore murals in Mogao caves using Human-machine Association. The effects of these techniques on cultural heritage and its future development methodology will also be discussed.</p>", "Keywords": "Digital Humanities; Artificial Intelligence; Dunhuang Culture", "DOI": "10.32996/jcsts.2022.4.2.5", "PubYear": 2022, "Volume": "4", "Issue": "2", "JournalId": 88928, "JournalTitle": "Journal of Computer Science and Technology Studies", "ISSN": "", "EISSN": "2709-104X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Glasgow Collage, University of Electronic Science and Technology of China, China"}], "References": []}, {"ArticleId": 96005115, "Title": "Surface feature and material removal in ultrasonic vibration-assisted slot-milling of Ti–6Al–4 V titanium alloy", "Abstract": "<p>The continuous optimization of processing technology and evaluation methods is an important way to achieve high processing quality and processing efficiency for difficult-to-process materials. To solve the problem of frequent defects in the slot, the ultrasonic vibration-assisted slot-milling (UVASM) technology was developed for processing titanium alloy. Simultaneously, comparative experiments were carried out between UVASM and conventional slot milling (CSM), in terms of surface features of slot bottom and slot sidewall, cutting force, tool trajectory, chip morphology, and micro-hardness. The results show that uniform vibration micro-texture could significantly improve surface topography of slot bottom in UVASM, while numerous tool feed trajectories and observable machining defects detract from the surface quality in CSM. The UVASM can greatly reduce material spalling and edge breakage, thereby maintaining a smooth and regular edge profile of the slot sidewall. The tool tip trajectories of the two machining methods are highly corresponding to the machining textures of the slot sidewall surface. There is a high-frequency and small-amplitude force fluctuation signal on the axial force waveform in UVASM, which can reduce the instantaneous maximum milling force and milling force in the stable stage by 8.7 and 12.2%, respectively. The UVASM has a better chip breaking effect and surface anti-scratch effect, and the UVASM can obtain higher surface micro-hardness and deeper plastic deformation layer than those CSM. In summary, the multi-dimensional evaluation of slot processing status has been completed, and the processing quality of the slot has been improved.</p>", "Keywords": "Ultrasonic vibration-assisted slot milling; Slot sidewall; Chip morphological; Micro-texture; Subsurface micro-hardness", "DOI": "10.1007/s00170-022-09970-y", "PubYear": 2022, "Volume": "122", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of High Performance and Complex Manufacturing, Central South University, Changsha, China; College of Mechanical and Electrical Engineering, Central South University, Changsha, China"}, {"AuthorId": 2, "Name": "Zhao<PERSON> Yi", "Affiliation": "State Key Laboratory of High Performance and Complex Manufacturing, Central South University, Changsha, China; College of Mechanical and Electrical Engineering, Central South University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of High Performance and Complex Manufacturing, Central South University, Changsha, China; College of Mechanical and Electrical Engineering, Central South University, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of High Performance and Complex Manufacturing, Central South University, Changsha, China; College of Mechanical and Electrical Engineering, Central South University, Changsha, China"}], "References": [{"Title": "Influence of force load on the stability of ultrasonic longitudinal–torsional composite drilling system", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "3-4", "Page": "891", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Theoretical investigation of machining-induced residual stresses in longitudinal torsional ultrasonic–assisted milling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "11-12", "Page": "3689", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Experimental investigations on longitudinal-torsional vibration-assisted milling of Ti-6Al-4V", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "11-12", "Page": "3607", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "An analytical prediction model for residual stress distribution and plastic deformation depth in ultrasonic-assisted single ball burnishing process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "1-2", "Page": "127", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Research on breakage characteristics in side milling of titanium alloy with cemented carbide end mill", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "11-12", "Page": "3661", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Characterization of Thrust Force, Temperature, Chip Morphology and Power in Ultrasonic-assisted Drilling of Aluminium 6061", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "3-4", "Page": "979", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Mechanistic modeling of cutting forces in high-speed microturning of titanium alloy with consideration of nose radius", "Authors": "Kubilay Aslantas; Şükr<PERSON>; Ömer <PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "3-4", "Page": "2393", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 96005191, "Title": "Coarse-to-fine spatial-channel-boundary attention network for image copy-move forgery detection", "Abstract": "<p>Forensics still faces a serious challenge with image copy-move forgery, in which the copied source and pasted target regions exist in the same image, also known as a homogeneous forgery. In the past decade, numerous copy-move forgery detection (CMFD) methods have attempted to resolve this issue. However, the traditional keypoint-based and block-based methods have certain insurmountable deficiencies, such as the inability to smooth out regions and the lack of scaling invariance. Since the introduction of deep neural networks (DNNs) in the CMFD scheme, researchers have been able to overcome the defects of the traditional hand-crafted methods and obtain promising results. Using DNNs as a reference, this paper proposes a coarse-to-fine spatial-channel-boundary attention network (SCBAN) which is more suited to CMFD. SCBAN consists of three sub-networks, namely, feature extraction, coarse forgery identification, and fine forgery identification modules. First, CondenseNet will serve as SCBAN's backbone for feature extraction. Next, we present a dual-correlation-attention module for parallel fusion, as well as a nearest-correlation matching module for coarse forgery identification. In addition, we propose a boundary refinement attention module for fine forgery identification. We have conducted numerous experiments on IMD, CoMoFoD, and CMHD benchmarks to show that our SCBAN can achieve the best performance and robustness, compared to the existing DNN CMFD. In addition, unlike the well-designed hand-crafted methods which achieve good performance in a specific dataset, our SCBAN can maintain its scalability to achieve good performance on multiple benchmark datasets.</p>", "Keywords": "Copy-move forgery detection; Coarse-to-fine; Spatial-channel-boundary attention network", "DOI": "10.1007/s00500-022-07432-x", "PubYear": 2022, "Volume": "26", "Issue": "21", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Guangzhou Maritime University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Macau University of Science and Technology, Macau, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, South China Business College, Guangdong University of Foreign Studies, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Information Science, University of Macau, Macau, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Guangzhou Maritime University, Guangzhou, China"}], "References": [{"Title": "Two-pass hashing feature representation and searching method for copy-move forgery detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "675", "JournalTitle": "Information Sciences"}, {"Title": "Dense moment feature index and best match algorithms for video copy-move forgery detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "537", "Issue": "", "Page": "184", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96005237, "Title": "Analysis of Insider Threats in the Healthcare Industry: A Text Mining Approach", "Abstract": "<p>To address rapidly growing data breach incidents effectively, healthcare providers need to identify various insider and outsider threats, analyze the vulnerabilities of their internal security systems, and develop more appropriate data security measures against the threats. While there have been studies on trends of data breach incidents, there is a lack of research on the analysis of descriptive contents posted on the data breach reporting website of the U.S. Department of Health and Human Services (HHS) Office for Civil Rights (OCR). Hence, this study develops a novel approach to the analysis of descriptive data breach information with the use of text mining and visualization. Insider threats, vulnerabilities, breach incidents, impacts, and responses to the breaches are analyzed for three data breach types.</p>", "Keywords": "insider threats; PHI; protected health information; data breach; healthcare provider; text mining; visualization; vulnerabilities; cybersecurity insider threats ; PHI ; protected health information ; data breach ; healthcare provider ; text mining ; visualization ; vulnerabilities ; cybersecurity", "DOI": "10.3390/info13090404", "PubYear": 2022, "Volume": "13", "Issue": "9", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "In Lee", "Affiliation": "School of Computer Sciences, Western Illinois University, Macomb, IL 61455, USA"}], "References": [{"Title": "Validation of a socio-technical management process for optimising cybersecurity practices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "101846", "JournalTitle": "Computers & Security"}, {"Title": "Information security governance challenges and critical success factors: Systematic review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "99", "Issue": "", "Page": "102030", "JournalTitle": "Computers & Security"}, {"Title": "An Assessment of Data Location Vulnerability for Human Factors Using Linear Regression and Collaborative Filtering", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "9", "Page": "449", "JournalTitle": "Information"}, {"Title": "Sentiment Analysis of COVID-19 tweets by Deep Learning Classifiers—A study to show how popularity is affecting accuracy in social media", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106754", "JournalTitle": "Applied Soft Computing"}, {"Title": "Cybersecurity knowledge and skills taught in capture the flag challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "102154", "JournalTitle": "Computers & Security"}, {"Title": "Verizon: 2021 Data Breach Investigations Report", "Authors": "", "PubYear": 2021, "Volume": "2021", "Issue": "6", "Page": "4", "JournalTitle": "Computer Fraud & Security"}, {"Title": "Proposal for an Implementation Guide for a Computer Security Incident Response Team on a University Campus", "Authors": "<PERSON>-<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "8", "Page": "102", "JournalTitle": "Computers"}, {"Title": "IBM: Cost of a Data Breach Report", "Authors": "", "PubYear": 2021, "Volume": "2021", "Issue": "8", "Page": "4", "JournalTitle": "Computer Fraud & Security"}]}, {"ArticleId": 96005273, "Title": "Barriers Affecting Higher Education Institutions’ Adoption of Blockchain Technology: A Qualitative Study", "Abstract": "<p>Despite the many benefits of blockchain technology in higher education, this technology is not widely adopted by Higher Education Institutions (HEIs). Therefore, instead of providing additional motives for adopting blockchain technology, this research tries to understand what factors discourage HEIs from merging blockchain with their procedures. The methodology used for this research is based upon qualitative research using 14 interviews with administrative and academic staff from the European Union (EU) and Canada. Our findings based on our empirical data revealed 15 key challenges to blockchain adoption by HEIs that are classified based on the technology, organization, and environment (TOE) framework. Theoretically, this study contributes to the body of knowledge relating to blockchain technology adoption. Practically, this research is expected to aid HEIs to assess the applicability of blockchain technology and pave the way for the widespread adoption of this technology in the educational field.</p>", "Keywords": "blockchain; distributed ledgers; blockchain challenges; blockchain in education; decentralized education; Higher Education Institutions (HEIs) blockchain ; distributed ledgers ; blockchain challenges ; blockchain in education ; decentralized education ; Higher Education Institutions (HEIs)", "DOI": "10.3390/informatics9030064", "PubYear": 2022, "Volume": "9", "Issue": "3", "JournalId": 40001, "JournalTitle": "Informatics", "ISSN": "", "EISSN": "2227-9709", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Business, Economics and IT, University West, SE-46186 Trollhattan, Sweden; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Business, Economics and IT, University West, SE-46186 Trollhattan, Sweden"}], "References": [{"Title": "Blockchain-enabled supply chain: analysis, challenges, and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "4", "Page": "787", "JournalTitle": "Multimedia Systems"}, {"Title": "A survey on blockchain technology and its security", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "2", "Page": "100067", "JournalTitle": "Blockchain: Research and Applications"}, {"Title": "Using Blockchain for Data Collection in the Automotive Industry Sector: A Literature Review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "2", "Page": "257", "JournalTitle": "Cybersecurity"}]}, {"ArticleId": 96005300, "Title": "A Frequency Adaptive Data Collection Method for Internet of Things Nodes", "Abstract": "<p>The aim of this paper is to solve the problem of a large amount of redundant data in the process of large-scale acquisition of Internet of Things sensors. This paper proposes a frequency adaptive data sensing method based on revolving gate algorithm for STM32 power safety data acquisition system. The method realizes the intelligent control of current protection terminal through three-stage current protection algorithm and frequency adaptive data acquisition algorithm. First, the three-stage current protection algorithm is used to protect the circuit, which can trip quickly in case of overload, and reduce the peak current caused by some equipment when starting up, so as to avoid damage to the equipment. Second, the arithmetic mean of each statistic is compared with the last value reported to the server. If the absolute value of the difference between the two exceeds the specified threshold range, the reported value is updated. Otherwise, it is filtered out, and the data smoothness is calculated according to the rules. The data collection interval is dynamically adjusted according to the data smoothness, which can greatly reduce the collection of redundant data and the traffic consumption of Internet of Things devices. On the server side, the inverse algorithm is used for interpolation and reconstruction to recover the collected data. This method is applied to the developed electricity safety equipment, and the electricity consumption is monitored and collected continuously for 24 h. After filtering by the frequency adaptive algorithm, only 108 pieces of data records are reported for 28,713 pieces of original data, and the compression ratio reaches 99.49[Formula: see text]. Compared with other data collection strategies, this method significantly reduces the amount of redundant data collection and the energy consumption of terminal nodes. Furthermore, this method realizes the real-time perception of line data and environmental data through current, residual current, temperature sensor and electric energy metering chip, and controls the opening and closing of wire controlled micro circuit breaker through PC817 optocoupler to protect the safety of the circuit.</p>", "Keywords": "Frequency adaptation; state monitoring; edge computing; interpolation algorithm; electricity safety", "DOI": "10.1142/S0218126623500664", "PubYear": 2023, "Volume": "32", "Issue": "4", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information and Intelligent Science and Technology, Hunan Agricultural University, Hunan Changsha, 410128, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Intelligent Science and Technology, Hunan Agricultural University, Hunan Changsha, 410128, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Intelligent Science and Technology, Hunan Agricultural University, Hunan Changsha, 410128, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Intelligent Science and Technology, Hunan Agricultural University, Hunan Changsha, 410128, P. R. China"}], "References": [{"Title": "Exploring reliable edge‐cloud computing for service latency optimization in sustainable cyber‐physical systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "11", "Page": "2225", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Wearable devices and IoT applications for symptom detection, infection tracking, and diffusion containment of the COVID-19 pandemic: a survey", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "11", "Page": "1413", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}]}, {"ArticleId": 96005301, "Title": "Assessment of Multi-Layer Perceptron Neural Network for Pulmonary Function Test’s Diagnosis Using ATS and ERS Respiratory Standard Parameters", "Abstract": "<p>The aim of the research work is to investigate the operability of the entire 23 pulmonary function parameters, which are stipulated by the American Thoracic Society (ATS) and the European Respiratory Society (ERS), to design a medical decision support system capable of classifying the pulmonary function tests into normal, obstructive, restrictive, or mixed cases. The 23 respiratory parameters specified by the ATS and the ERS guidelines, obtained from the Pulmonary Function Test (PFT) device, were employed as input features to a Multi-Layer Perceptron (MLP) neural network. Thirteen possible MLP Back Propagation (BP) algorithms were assessed. Three different categories of respiratory diseases were evaluated, namely obstructive, restrictive, and mixed conditions. The framework was applied on 201 PFT examinations: 103 normal and 98 abnormal cases. The PFT decision support system’s outcomes were compared with both the clinical truth (physician decision) and the PFT built-in diagnostic software. It yielded 92–99% and 87–92% accuracies on the training and the test sets, respectively. An 88–94% area under the receiver operating characteristic curve (ROC) was recorded on the test set. The system exceeded the performance of the PFT machine by 9%. All 23 ATS\\ERS standard PFT parameters can be used as inputs to design a PFT decision support system, yielding a favorable performance compared with the literature and the PFT machine’s diagnosis program.</p>", "Keywords": "American Thoracic Society; European Respiratory Society; medical decision support system; multi-layer perceptron neural network; pulmonary function test; respiratory disease American Thoracic Society ; European Respiratory Society ; medical decision support system ; multi-layer perceptron neural network ; pulmonary function test ; respiratory disease", "DOI": "10.3390/computers11090130", "PubYear": 2022, "Volume": "11", "Issue": "9", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Biomedical Engineering, School of Electrical and Computer Engineering, University of Campinas (UNICAMP), Av. <PERSON> 400, Cidade Universitária Zeferino <PERSON>, Campinas 13083-852, Brazil; Corresponding author"}, {"AuthorId": 2, "Name": "Abdel-Ra<PERSON>k <PERSON>", "Affiliation": "Faculty of Allied Medical Sciences, Isra University, Amman 11622, Jordan↑Faculty of Science, Isra University, Amman 11622, Jordan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Innovation Engineering, University of Salento, 73100 Lecce, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Innovation Engineering, University of Salento, 73100 Lecce, Italy; Corresponding author"}], "References": [{"Title": "Development of a decision support system tool to predict the pulmonary function using artificial neural network approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "16", "Page": "e6258", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Automated Detection of Left Bundle Branch Block from ECG Signal Utilizing the Maximal Overlap Discrete Wavelet Transform with ANFIS", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "6", "Page": "93", "JournalTitle": "Computers"}]}, {"ArticleId": 96005349, "Title": "Teaching-learning optimizer-based FO-PID for load frequency control of interlinked power systems", "Abstract": "Designing an effective, robust controller is vital for regulating system frequency corresponding to the load demand variations in a traditional interlinked power system. Hence, load frequency control plays a critical role in countering load disturbances and minimizing unscheduled power flow between interlinked areas. This paper proposes a novel teaching-learning optimization (TLO) based dual-loop load frequency control approach aided by fractional-order proportional-integral-derivative controller (FO-PID). The inner control loop of the presented method employs internal model control and model approximation. On the other hand, the external loop uses a TLO-tuned FO-PID controller to improve the outcomes of the proposed approach. The proposed method is validated using a 1-area multi-stage reheat hydrothermal power system (RHTPS), 2-area reheat thermal power system (RTPS), and 2-area multi-stage RHTPS. The robustness of the proposed method is demonstrated by executing sensitivity analysis. It is established from the simulation results that the proposed approach enhances the dynamic response of the test systems substantially.", "Keywords": "Load frequency control ; internal model control ; teaching-learning optimization ; interlinked power systems ; model reduction", "DOI": "10.1080/02286203.2022.2112009", "PubYear": 2023, "Volume": "43", "Issue": "5", "JournalId": 4075, "JournalTitle": "International Journal of Modelling and Simulation", "ISSN": "0228-6203", "EISSN": "1925-7082", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, Motilal Nehru National Institute of Technology Allahabad, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, Motilal Nehru National Institute of Technology Allahabad, India"}], "References": [{"Title": "Grasshopper optimization algorithm scaled fractional order PI-D controller applied to reduced order model of load frequency control system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "40", "Issue": "3", "Page": "217", "JournalTitle": "International Journal of Modelling and Simulation"}, {"Title": "Optimized fuzzy self-tuning PID controller design based on Tribe-DE optimization algorithm and rule weight adjustment method for load frequency control of interconnected multi-area power systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106424", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 96005593, "Title": "BH2I-GAN: Bidirectional Hash_code-to-Image Translation using Multi-Generative Multi-Adversarial Nets", "Abstract": "Given the benefits of high retrieval efficiency and low storage cost, hashing method has received an increasing attention. In particular, deep learning-based hashing has been widely used in data mining and information retrieval. However, almost all the existing methods only achieve the goal of high retrieval precision, and limit the evaluation of hashing methods to objective aspect. In this paper, we propose a novel bidirectional hash_code-to-image translation model by using multi-generative multi-adversarial nets to reduce storage cost truly and obtain satisfactory user acceptance on the basis of high retrieval precision. Firstly, we propose supervised manifold metric to reduce Hamming distance between similar instances while increasing the Hamming distance between dissimilar instances, which have been proved to be helpful for high retrieval precision and good user acceptance. Then, we utilize multi-generative and multi-adversarial networks to construct hash mapping and inverse hash generation. During inverse generation, theoretical analysis is conducted to show that inverse hash network can avoid unstable training and mode collapse. Besides, we prove that Poisson distribution induced by hash codes can be initialized as generative distribution to fit real distribution. Experimental results show that our method outperforms several state-of-the-art approaches on three popular datasets. Specifically, ours yields average about 9.3% increment in Mean Average Precision(MAP) on three datasets, and achieves over 90% user satisfaction. Besides, it successfully reduces storage cost by 1,634 times in COCO 2017 large-scale dataset.", "Keywords": "Deep hashing ; Generative adversarial nets ; Low storage cost ; Hash_code-to-image ; Supervised manifold similarity", "DOI": "10.1016/j.patcog.2022.109010", "PubYear": 2023, "Volume": "133", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, China West Normal University, Nanchong, 637009, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Image Cognition, College of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, 400065, China"}, {"AuthorId": 3, "Name": "Weisheng Li", "Affiliation": "Chongqing Key Laboratory of Image Cognition, College of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, 400065, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Image Cognition, College of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, 400065, China"}], "References": [{"Title": "Weakly-supervised Semantic Guided Hashing for Social Image Retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "8-9", "Page": "2265", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Deep video code for efficient face video retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107754", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": ********, "Title": "Green Logistics of Vehicle Dispatch under Smart IoT", "Abstract": "As environmental problems increase, green development has become an indispensable part of the modern logistics system. Therefore, to help the logistics industry achieve the green development goals of energy saving and emission reduction, we studied the green logistics vehicle routing problem. Considering that the Internet of Things (IoT) plays an essential role in logistics scheduling, we constructed a Smart-IoT-based multi-objective green logistics vehicle scheduling model. This model guarantees the accuracy of vehicle-related information in all aspects of dispatch through IoT sensor technologies such as Global Positioning System (GPS) sensors, load sensors, and radio frequency identification (RFID) sensors and considers practical dispatch constraints such as dynamic vehicle carbon emission and physical constraints on heterogeneous vehicles. Furthermore, to address problems such as the poor convergence accuracy of the traditional path planning algorithm, we designed an improved path optimization algorithm by introducing the Levy flight strategy and simulated annealing mechanism in the bald eagle search algorithm to improve the search space and convergence speed of the algorithm. Then we conducted simulation experiments based on the scheduling task of a real logistics company. We also compared the designed algorithm horizontally with cutting-edge algorithms such as the sparrow search algorithm and crow search algorithm to verify the feasibility of the designed model and algorithm. Our results provide a practical reference for helping the logistics industry achieve green development. © 2022 MYU K.K.", "Keywords": "bald eagle search; green logistics vehicle routing; Industrial Internet of Things; logistics dispatch; RFID", "DOI": "10.18494/SAM3934", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Civil Aviation and Aeronautics, Kunming University of Science and Technology, Kunming, 650500, China"}, {"AuthorId": 2, "Name": "Jingcheng Zhang", "Affiliation": "Faculty of Science, Kunming University of Science and Technology, Kunming, 650500, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lijiang Power Supply Bureau, Yunnan Power Grid Co., Ltd., Kunming, 674100, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Civil Aviation and Aeronautics, Kunming University of Science and Technology, Kunming, 650500, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> An", "Affiliation": "Logistics Center, Hongyun Honghe Tobacco (Group) Co., Ltd., Lincang, 650231, China"}], "References": []}, {"ArticleId": 96005730, "Title": "“First Kilometer” Scheduling Task of Multiple Unmanned Aerial Vehicles Based on Multisource Heterogeneous Sensors", "Abstract": "To solve the problem of \"first-kilometer\" distribution difficulties in rural areas, we propose a transportation method using unmanned aerial vehicles (UAVs) for delivery. The mountainous environment of Fengshan County in Guizhou is first simulated as the UAV delivery environment. A differential evolution strategy based on the improved whale optimization algorithm (DEIWOA) combined with multisource heterogeneous sensors is then proposed to solve the UAV obstacle avoidance path. After the UAV's delivery path is planned using the DEIWOA algorithm, the multisource heterogeneous sensor is used to perform obstacle avoidance among multiple UAVs and path correction of UAVs in actual situations. Afterwards, to minimize the delivery cost of UAVs, a multi-UAV cargo delivery model is built with the optimization goal of minimizing the transportation cost and time window violation cost. This UAV scheduling model is solved using the proposed DEIWOA algorithm. Finally, simulations are performed to compare the proposed method with the cutting-edge algorithms. The obtained results show that the proposed DEIWOA algorithm can provide a better plan of the UAV path and reduce the cost of logistics scheduling. It can also provide support for UAV logistics and distribution in mountainous areas in actual situations. © 2022 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "logistics scheduling; multisource heterogeneous sensors; path planning; UAVs; whale algorithm", "DOI": "10.18494/SAM3935", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Measurement Center, Yunnan Power Grid Co. Ltd., Kunming, 650051, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Civil Aviation and Aeronautics, Kunming University of Science and Technology, Kunming, 650500, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Civil Aviation and Aeronautics, Kunming University of Science and Technology, Kunming, 650500, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Civil Aviation and Aeronautics, Kunming University of Science and Technology, Kunming, 650500, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Civil Aviation and Aeronautics, Kunming University of Science and Technology, Kunming, 650500, China"}], "References": []}, {"ArticleId": 96005757, "Title": "Logic Realization of Galois Field for AES SBOX using Quantum Dot Cellular Automata", "Abstract": "<p>With the growing technological trends in VLSI domain, quantum dot cellular automata (QCA) technology is slowly replacing CMOS technology due to its smaller feature size, high operating frequency and reduced power consumption. In the initial research phase, QCA has been used to implement various combinatorial and sequential circuits models, which are the fundamental blocks in various applications. Nowadays, researchers focus on the implementation of application-based designs using QCA. This motivated to implement the Galois field (GF) functions for SBox module in the most secure cryptography encryption standard AES with QCA. In AES, SBOX is the predominant power consumption modules. Hence, a research has been carried out to implement a compact QCA-based AES-SBOX with GF. This paper describes the implementation of our proposed QCA-based AES-SBOX with Galois field and analysis of various GF functional modules in terms of area, performance, energy and QCA cells used. The functional verification is performed using the simulated waveforms.</p>", "Keywords": "QCA; Galois Field (GF); AES SBOX; Cryptography Algorithm; QCA Designer", "DOI": "10.1007/s11227-022-04779-8", "PubYear": 2023, "Volume": "79", "Issue": "3", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ECE, Narayana Engineering College, SPSR Nellore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ECE, Sri <PERSON> Engineering College, Coimbatore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "EEE, PSG Institute of Technology and Applied Research, Coimbatore, India"}], "References": [{"Title": "Optimal demultiplexer unit design and energy estimation using quantum dot cellular automata", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "2", "Page": "1714", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Design and energy dissipation analysis of simple QCA multiplexer for nanocomputing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "6", "Page": "8430", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 96005787, "Title": "Neural ordinary differential gray algorithm to forecasting nonlinear systems", "Abstract": "Due to the feasibility of the gray model for predicting time series with small samples, the gray theory is well investigated since it is presented and is currently evolved in an important manner for forecasting small samples. This study proposes a new gray prediction criterion based on the Neural Ordinary Differential Equation (NODE), which is named the NODGM (Neural Based Ordinary Differential gray Mode). This mode permits the forecasting approximation to be learned by a training process which contains a new whitening equation. It is needed to prepare the structure and time series, compared with other models, according to the regularity of actual specimens in advance, therefore this model of NODGM can provide comprehensive applications as well as learning the properties of distinct data specimens. In order to acquire a better model which has highly predictive efficiency, afterwards, this study trains the model by NODGM meanwhile using the Runge-Kutta method to obtain the prediction sequence and solve the model. The controller establishes an advantageous theoretical foundation in adapting to novel wheels and comprehensive spreads the utilize extent of Mechanical Elastic Vehicle Wheel (MEVW).", "Keywords": "Artificial intelligence ; Evolved control ; Gray DGM (2;1) model ; Mechanical Elastic Vehicle Wheel (MEVW) ; Nonlinear control", "DOI": "10.1016/j.advengsoft.2022.103199", "PubYear": 2022, "Volume": "173", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "ZY Chen", "Affiliation": "School of Science, Guangdong University of Petrochemical Technology, Maoming 525000, Guangdong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Guangdong University of Petrochemical Technology, Maoming 525000, Guangdong, China;Correspondence authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Guangdong University of Petrochemical Technology, Maoming 525000, Guangdong, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "California Institute of Technology, Pasadena, CA 91125, USA;Correspondence authors"}], "References": [{"Title": "Deep Neural Networks Motivated by Partial Differential Equations", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "3", "Page": "352", "JournalTitle": "Journal of Mathematical Imaging and Vision"}, {"Title": "Neural ordinary differential grey model and its applications", "Authors": "Dajiang Lei; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "114923", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A new comprehensive model of damage for flexural subassemblies prone to fatigue", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "256", "Issue": "", "Page": "106639", "JournalTitle": "Computers & Structures"}, {"Title": "Grey Signal Predictor and Fuzzy Controls for Active Vehicle Suspension Systems via Lyapunov Theory", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "3", "Page": "1", "JournalTitle": "International Journal of Computers Communications & Control"}, {"Title": "Interpretability application of the Just-in-Time software defect prediction model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "188", "Issue": "", "Page": "111245", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 96005838, "Title": "Cluster‐based improvement rates for trust establishment models in single or distributed multi‐agent systems", "Abstract": "<p>Intelligent agents within open and dynamic multi-agent systems are becoming increasingly capable in their decision-making abilities and rely upon the notion of trustworthiness to determine which agents to interact with. To improve the overall performance of trust establishment models which trustees individually select and equip to improve their trustworthiness with trustors, while balancing the resources being spent, a cluster-based trust establishment model update mechanism is proposed. This cluster-based approach is applicable to robust trust establishment models which utilize dynamic improvement and disimprovement rate variables to adjust a trustee's behaviors toward trustors to improve or maintain trust with the trustor. By storing a single trust establishment model's dynamic improvement and disimprovement rate variables independently for each trustor and by clustering similar trustors together based on observed experiences, a model can more accurately update a trustee's behaviors toward trustors. Through simulated experiments comparing the performance of the existing integrated trust establishment (ITE) model with and without the cluster-based approach, with varying trustor to trustee ratios to diversify the agent behaviors, the cluster-based approach consistently improves a trustee's ability to fully meet a trustor's needs, for less resources than ITE, while minimizing the corresponding impact to the trustee's overall trust.</p>", "Keywords": "intelligent agents;machine learning;multi-agent systems;trust establishment", "DOI": "10.1111/coin.12546", "PubYear": 2022, "Volume": "38", "Issue": "5", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science University of Ottawa  Ottawa Ontario Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science University of Ottawa  Ottawa Ontario Canada"}], "References": [{"Title": "Agent-based Internet of Things: State-of-the-art and research challenges", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "1038", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 96005856, "Title": "A decentralized and reliable trust measurement for edge computing enabled Internet of Things", "Abstract": "The combination of edge computing (EC) and the Internet of Things is a hot research topic. And security is one of the most important problems to be solved. The trust measurement of devices is an effective way to solve the security problem, and the lack of a unified trust measurement model makes the untrusted devices destroy the quality of service. Establishing a reliable trust relationship between devices can effectively improve the security of the system. A decentralized trust measurement model for devices is proposed. First, a decentralized trust measurement framework is proposed, which combines EC with blockchain technology to establish a decentralized hierarchical structure; second, the credibility of devices is measured from multilevel and multi‐attribute; finally, a feedback trust filtering mechanism is designed to filter reliable feedback information, and then the trust between devices and the comprehensive trust of devices are calculated. Experiments and analysis show that the proposed decentralized trust measurement model can effectively measure the trust degree of devices and resist various malicious feedback attacks.", "Keywords": "blockchain;edge computing;Internet of Things;trust measurement", "DOI": "10.1002/cpe.7238", "PubYear": 2022, "Volume": "34", "Issue": "24", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology Beijing University of Technology  Beijing China"}, {"AuthorId": 2, "Name": "Dongzhi Cao", "Affiliation": "Faculty of Information Technology Beijing University of Technology  Beijing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology Beijing University of Technology  Beijing China"}], "References": [{"Title": "An experimental framework for future smart cities using data fusion and software defined systems: The case of environmental monitoring for smart healthcare", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "883", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Image encryption based on a single‐round dictionary and chaotic sequences in cloud computing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "1", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "A Gaussian error correction multi‐objective positioning model with NSGA‐II", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "An under‐sampled software defect prediction method based on hybrid multi‐objective cuckoo search", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "A fog-enabled smart home solution for decision-making using smart objects", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "18", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "An Edge Computing-enhanced Internet of Things Framework for Privacy-preserving in Smart City", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "81", "Issue": "", "Page": "106504", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Personalized Recommendation System Based on Collaborative Filtering for IoT Scenarios", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "685", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Blockchain-empowered decentralised trust management for the Internet of Vehicles security", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106722", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A Survey on Differential Privacy for Unstructured Data Content", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "10s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 96005867, "Title": "The strategic usage of Facebook by local governments: A structural topic modelling analysis", "Abstract": "We examine the content of the official Facebook pages of 502 Italian municipalities over the period 2016–2018 to empirically infer the motivations of municipalities to use Facebook. Our inference is mostly based on the relationship between the main topics discussed by the municipalities on Facebook and the most related municipal expenses per capita, which are reported by function in municipal financial statements. Using a structural topic modelling analysis consisting of an unsupervised machine learning procedure, we find that Italian municipalities mostly post on five semantically different topics on their Facebook pages. More importantly, each topic&#x27;s prevalence is positively associated with the most related municipal expenses per capita by function. Furthermore, additional covariates significantly influence each topic&#x27;s prevalence. Within the framework of legitimacy theory, these results indicate the strategic usage of Facebook by Italian municipalities for self-promotion and political marketing aimed at enhancing their political legitimacy and consensus among citizens. Our findings contribute to the literature on voluntary e-disclosures of local governments and on interactions between local governments and their citizens through social media. Therefore, these findings may have practical implications for public regulators, managers, and other users of social media.", "Keywords": "Political legitimacy ; Social media ; Topic modelling ; Voluntary e-disclosures", "DOI": "10.1016/j.im.2022.103704", "PubYear": 2022, "Volume": "59", "Issue": "8", "JournalId": 2493, "JournalTitle": "Information & Management", "ISSN": "0378-7206", "EISSN": "1872-7530", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TBS Business School, Campus Barcelona, C/ Trafalgar, 10. 08010 Barcelona, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> M<PERSON>-<PERSON>", "Affiliation": "EAE Business School, Campus Barcelona, C/Tarragona, 110. 08015 Barcelona, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Barcelona, Facultat d'Economia i Empresa. Av. <PERSON>, 690. 08034 Barcelona, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Ramón Llull, IQS School of Management, Via Augusta, 390. 08017 Barcelona, Spain"}], "References": [{"Title": "Business analytics for strategic management: Identifying and assessing corporate challenges via topic modeling", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "1", "Page": "103070", "JournalTitle": "Information & Management"}, {"Title": "Evolution of information systems research: Insights from topic modeling", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "4", "Page": "103207", "JournalTitle": "Information & Management"}, {"Title": "Why do citizens participate on government social media accounts during crises? A civic voluntarism perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "1", "Page": "103286", "JournalTitle": "Information & Management"}, {"Title": "The four dimensions of social network analysis: An overview of research methods, applications, and software tools", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "88", "JournalTitle": "Information Fusion"}, {"Title": "Multilingual topic modeling for tracking COVID-19 trends based on Facebook data analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>, <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "5", "Page": "3052", "JournalTitle": "Applied Intelligence"}, {"Title": "Deep Learning--based Text Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 96005889, "Title": "Intelligent Reflecting Surface-Aided Device-to-Device Communication: A Deep Reinforcement Learning Approach", "Abstract": "<p>Recently, the growing demand of various emerging applications in the realms of sixth-generation (6G) wireless networks has made the term internet of Things (IoT) very popular. Device-to-device (D2D) communication has emerged as one of the significant enablers for the 6G-based IoT network. Recently, the intelligent reflecting surface (IRS) has been considered as a hardware-efficient innovative scheme for future wireless networks due to its ability to mitigate propagation-induced impairments and to realize a smart radio environment. Such an IRS-assisted D2D underlay cellular network is investigated in this paper. Our aim is to maximize the network’s spectrum efficiency (SE) by jointly optimizing the transmit power of both the cellular users (CUs) and the D2D pairs, the resource reuse indicators, and the IRS reflection coefficients. Instead of using traditional optimization solution schemes to solve this mixed integer nonlinear optimization problem, a reinforcement learning (RL) approach is used in this paper. The IRS-assisted D2D communication network is structured by the Markov Decision Process (MDP) in the RL framework. First, a Q-learning-based solution is studied. Then, to make a scalable solution with large dimension state and action spaces, a deep Q-learning-based solution scheme using experience replay is proposed. Lastly, an actor-critic framework based on the deep deterministic policy gradient (DDPG) scheme is proposed to learn the optimal policy of the constructed optimization problem considering continuous-valued state and action spaces. Simulation outcomes reveal that the proposed RL-based solution schemes can provide significant SE enhancements compared to the existing optimization schemes.</p>", "Keywords": "device-to-device communication; overlay communication; intelligent reflecting surface (IRS); reinforcement learning (RL); spectrum efficiency (SE) device-to-device communication ; overlay communication ; intelligent reflecting surface (IRS) ; reinforcement learning (RL) ; spectrum efficiency (SE)", "DOI": "10.3390/fi14090256", "PubYear": 2022, "Volume": "14", "Issue": "9", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Current address: Department of Computer Science, Algoma University, Brampton, ON L6V 1A3, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical, Computer and Biomedical Engineering, Ryerson University, Toronto, ON M5B 2K3, Canada; Corresponding author"}], "References": []}, {"ArticleId": 96005950, "Title": "A new geometric trajectory tracking controller for the unicycle mobile robot", "Abstract": "This work proposes a geometric controller to solve the trajectory tracking problem for a differential drive mobile robot. The proposed control design exploits the properties of the mobile robot attitude configuration space and the cascade structure of the translational and rotational kinematic models. It is formally proven, through <PERSON><PERSON><PERSON><PERSON> arguments, that the closed-loop error dynamics is almost globally asymptotically stable. Numerical simulation results illustrate the performance of the proposed control algorithm.", "Keywords": "Non-holonomic systems ; Trajectory tracking ; Geometric control ; Unicycle robot ; <PERSON><PERSON><PERSON><PERSON> theory", "DOI": "10.1016/j.sysconle.2022.105360", "PubYear": 2022, "Volume": "168", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-Cortés", "Affiliation": "Sección de Mecatrónica, Departamento de Ingeniería Eléctrica, Centro de Investigación y de Estudios Avanzados del Instituto Politécnico Nacional, Avenida Instituto Politécnico Nacional 2508, Col. San Pedro Z<PERSON>co, CP 07360, Ciudad de México, México;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Sección de Mecatrónica, Departamento de Ingeniería Eléctrica, Centro de Investigación y de Estudios Avanzados del Instituto Politécnico Nacional, Avenida Instituto Politécnico Nacional 2508, Col. San Pedro Zacatenco, CP 07360, Ciudad de México, México"}], "References": []}, {"ArticleId": 96006003, "Title": "Large‐scale knowledge distillation with elastic heterogeneous computing resources", "Abstract": "Although more layers and more parameters generally improve the accuracy of the models, such big models generally have high computational complexity and require big memory, which exceed the capacity of small devices for inference and incurs long training time. In addition, it is difficult to afford long training time and inference time of big models even in high performance servers, as well. As an efficient approach to compress a large deep model (a teacher model) to a compact model (a student model), knowledge distillation emerges as a promising approach to deal with the big models. Existing knowledge distillation methods cannot exploit the elastic available computing resources and correspond to low efficiency. In this paper, we propose an Elastic Deep Learning framework for knowledge Distillation, that is, EDL‐Dist. The advantages of EDL‐Dist are threefold. First, the inference and the training process is separated. Second, elastic available computing resources can be utilized to improve the efficiency. Third, fault‐tolerance of the training and inference processes is supported. We take extensive experimentation to show that the throughput of EDL‐Dist is up to 3.125 times faster than the baseline method (online knowledge distillation) while the accuracy is similar or higher.", "Keywords": "deep neural network;distributed computing;knowledge distillation", "DOI": "10.1002/cpe.7272", "PubYear": 2023, "Volume": "35", "Issue": "26", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Baidu Inc.  Beijing China"}, {"AuthorId": 2, "Name": "Daxiang Dong", "Affiliation": "Baidu Inc.  Beijing China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Baidu Inc.  Beijing China"}, {"AuthorId": 4, "Name": "An Qin", "Affiliation": "Baidu Inc.  Beijing China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Baidu Inc.  Beijing China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "LIRMM Inria, University of Montpellier, CNRS  Montpellier France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Baidu Inc.  Beijing China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Baidu Inc.  Beijing China"}], "References": [{"Title": "Job scheduling for distributed machine learning in optical WAN", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Gang Sun", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "549", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 96006005, "Title": "A Novel Radially Closable Tubular Origami Structure (RC-ori) for Valves", "Abstract": "<p>Cylindrical Kresling origami structures are often used in engineering fields due to their axial stretchability, tunable stiffness, and bistability, while their radial closability is rarely mentioned to date. This feature enables a valvelike function, which inspired this study to develop a new origami-based valve. With the unique one-piece structure of origami, the valve requires fewer parts, which can improve its tightness and reduce the cleaning process. These advantages meet the requirements of sanitary valves used in industries such as the pharmaceutical industry. This paper summarizes the geometric definition of the Kresling pattern as developed in previous studies and reveals the similarity of its twisting motion to the widely utilized iris valves. Through this analogy, the Kresling structure’s closability and geometric conditions are characterized. To facilitate the operation of the valve, we optimize the existing structure and create a new crease pattern, RC-ori. This novel design enables an entirely closed state without twisting. In addition, a simplified modeling method is proposed in this paper for the non-rigid foldable cylindrical origami. The relationship between the open area and the unfolded length of the RC-ori structure is explored based on the modeling method with a comparison with nonlinear FEA simulations. Not only limited to valves, the new crease pattern could also be applied to microreactors, drug carriers, samplers, and foldable furniture.</p>", "Keywords": "cylindrical origami; Kresling; radially closable; valve cylindrical origami ; Kresling ; radially closable ; valve", "DOI": "10.3390/act11090243", "PubYear": 2022, "Volume": "11", "Issue": "9", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Ye", "Affiliation": "School of Engineering and Architecture, University College Cork, T12 K8AF Cork, Ireland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Architecture, University College Cork, T12 K8AF Cork, Ireland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Architecture, University College Cork, T12 K8AF Cork, Ireland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Architecture, University College Cork, T12 K8AF Cork, Ireland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Construction Engineering, Faculty of Engineering and Environment, Northumbria University, Newcastle upon Tyne NE1 8ST, UK"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Architecture, University College Cork, T12 K8AF Cork, Ireland"}], "References": [{"Title": "Assigning mountain-valley fold lines of flat-foldable origami patterns based on graph theory and mixed-integer linear programming", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "239", "Issue": "", "Page": "106328", "JournalTitle": "Computers & Structures"}]}, {"ArticleId": 96006089, "Title": "Optimal Maneuver Trajectory Design Based on ARIMA-UKF Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.12677/SEA.2022.114091", "PubYear": 2022, "Volume": "11", "Issue": "4", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "诗文 许", "Affiliation": ""}], "References": []}, {"ArticleId": 96006134, "Title": "Declarative RDF graph generation from heterogeneous (semi-)structured data: A systematic literature review", "Abstract": "More and more data in various formats are integrated into knowledge graphs. However, there is no overview of existing approaches for generating knowledge graphs from heterogeneous (semi-)structured data, making it difficult to select the right one for a certain use case. To support better decision making, we study the existing approaches for generating knowledge graphs from heterogeneous (semi-)structured data relying on mapping languages. In this paper, we investigated existing mapping languages for schema and data transformations, and corresponding materialization and virtualization systems that generate knowledge graphs. We gather and unify 52 articles regarding knowledge graph generation from heterogeneous (semi-)structured data. We assess 15 characteristics on mapping languages for schema transformations, 5 characteristics for data transformations, and 14 characteristics for systems. Our survey paper provides an overview of the mapping languages and systems proposed the past two decades. Our work paves the way towards a better adoption of knowledge graph generation, as the right mapping language and system can be selected for each use case.", "Keywords": "Knowledge graph construction ; Schema transformations ; Data transformations ; Survey ; Declarative", "DOI": "10.1016/j.websem.2022.100753", "PubYear": 2023, "Volume": "75", "Issue": "", "JournalId": 7442, "JournalTitle": "Journal of Web Semantics", "ISSN": "1570-8268", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ghent University – imec – IDLab, Department of Electronics and Information Systems, Technologiepark-Zwijnaarde 122, 9052 Ghent, Belgium;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ghent University – imec – IDLab, Department of Electronics and Information Systems, Technologiepark-Zwijnaarde 122, 9052 Ghent, Belgium"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Ghent University – imec – IDLab, Department of Electronics and Information Systems, Technologiepark-Zwijnaarde 122, 9052 Ghent, Belgium"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Ghent University – imec – IDLab, Department of Electronics and Information Systems, Technologiepark-Zwijnaarde 122, 9052 Ghent, Belgium"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Ghent University – imec – IDLab, Department of Electronics and Information Systems, Technologiepark-Zwijnaarde 122, 9052 Ghent, Belgium"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "KU Leuven – Leuven.AI – <PERSON>, Department of Computer Science, <PERSON><PERSON><PERSON> 5, 2860 Sint-Katelijne-Waver, Belgium;Corresponding authors"}], "References": [{"Title": "Large-scale Semantic Integration of Linked Data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Implementation-independent function reuse", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "946", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Exploiting Declarative Mapping Rules for Generating GraphQL Servers with Morph-GraphQL", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "6", "Page": "785", "JournalTitle": "International Journal of Software Engineering and Knowledge Engineering"}, {"Title": "GTFS-Madrid-Bench: A benchmark for virtual knowledge graph access in the transport domain", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "", "Page": "100596", "JournalTitle": "Journal of Web Semantics"}, {"Title": "ShExML: improving the usability of heterogeneous data mapping languages for first-time users", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "", "Page": "e318", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Building Semantic Knowledge Graphs from (Semi-)Structured Data: A Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "5", "Page": "129", "JournalTitle": "Future Internet"}]}, {"ArticleId": 96006135, "Title": "Autonomous Wireless Sensors Network for the Implementation of a Cyber-Physical System Monitoring Reinforced Concrete Civil Engineering Structures", "Abstract": "This paper presents an autonomous Wireless Sensors Network as the hardware basis for implementing a Cyber-Physical System. The Wireless Sensors Network is composed of Sensing Nodes and Communicating Nodes. The Sensing Nodes can wirelessly transmit data measured in reinforced concrete using Non-Destructive Testing methods over several tens of meters. These are battery-free, and are powered and controlled wirelessly and remotely by a radiative electromagnetic Wireless Power Transfer system tuned by the Communicating Nodes. Each Communicating Node covers a volume of at least 11 meters in all directions for the Wireless Power Transfer. The Wireless Sensors Network meets the requirements of the Simultaneous Wireless Information and Power Transmission paradigm by using a single antenna in the Sensing Nodes (concurrently for power harvesting and data transmission). The proposed system is designed for the Structural Health Monitoring of reinforced concrete civil engineering structures, but can be adapted to other applications, especially in harsh environments.", "Keywords": "Wireless Sensors Network (WSN) ; Wireless Power Transfer (WPT) ; Cyber-Physical System (CPS) ; Internet of Things (IoT) ; Simultaneous Wireless Information ; Power Transmission (SWIPT) ; Structural Health Monitoring (SHM) ; Non-Destructive Testing (NDT)", "DOI": "10.1016/j.ifacol.2022.08.004", "PubYear": 2022, "Volume": "55", "Issue": "8", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Gaël LOUBET", "Affiliation": "LAAS-CNRS, INSA Toulouse, Université de Toulouse, CNRS, Toulouse, FRANCE"}, {"AuthorId": 2, "Name": "Alassane SIDIBE", "Affiliation": "LAAS-CNRS, UWINLOC, Université de Toulouse, CNRS, Toulouse, FRANCE"}, {"AuthorId": 3, "Name": "Alexandru <PERSON>", "Affiliation": "LAAS-CNRS, UPS, Université de Toulouse, CNRS, Toulouse, FRANCE"}, {"AuthorId": 4, "Name": "Daniela DRAGOMIRESCU", "Affiliation": "LAAS-CNRS, INSA Toulouse, Université de Toulouse, CNRS, Toulouse, FRANCE"}], "References": []}, {"ArticleId": 96006167, "Title": "Low-rate Denial of Service attack detection method based on time-frequency characteristics", "Abstract": "<p>In this paper, a real cloud computing platform-oriented Low-rate Denial of Service (LDoS) attack detection method based on time-frequency characteristics of traffic data is proposed. All the traffic data flowing through the Web server is acquired by the collection and storage system, the original traffic data is divided into multiple flow segments by the preprocessing module, and the simple statistical features of several data packets in the flow are extracted by the analysis tool to form the detection sequence. The deep neural network is used to learn the potential time-frequency domain connection in the normal feature sequence and generate the reconstructed sequence. The discrimination module discriminates against the LDoS attack according to the difference between the reconstructed sequence and the input data in the time-frequency domain. The experimental results show that the proposed method can accurately detect the attack features in the stream segments in a very short time, and can achieve high detection accuracy for complex and diverse LDoS attacks. Because only the statistical characteristics of data packets are used, it is not necessary to analyze the data in the packets, which can be adapted to different network environments.</p>", "Keywords": "Real network environment;Time-frequency characteristics;Low-rate denial of service;Attack detection", "DOI": "10.1186/s13677-022-00308-3", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Security, Naval University of Engineering, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Security, Naval University of Engineering, Wuhan, China; College of Computer and Information Technology, Xinyang Normal University, Xinyang, China; Henan Key Laboratory of Analysis and Applications of Education Big Data, Xinyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Security, Naval University of Engineering, Wuhan, China; School of Mathematics and Information Engineering, Xinyang Vocational and Technical College, Xinyang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Information Security, Naval University of Engineering, Wuhan, China"}], "References": [{"Title": "The detection method of low-rate DoS attack based on multi-feature fusion", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "4", "Page": "504", "JournalTitle": "Digital Communications and Networks"}, {"Title": "A user-based video recommendation approach using CAC filtering, PCA with LDOS-CoMoDa", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "7", "Page": "9377", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Service Level Agreement Monitoring as a Service: An Independent Monitoring Service for Service Level Agreements in Clouds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "11", "Issue": "5", "Page": "339", "JournalTitle": "Big Data"}]}, {"ArticleId": 96006172, "Title": "Laser-induced breakdown spectroscopy mediated amplification sensor for copper (Ⅱ) ions detection using click chemistry", "Abstract": "New strategies for super-sensitive detection of toxic heavy-metal-ion pollutants are urgently needed. Here, we outline a simple and highly sensitive assay for copper(II) ions. The method is based on click chemistry and uses laser-induced breakdown spectroscopy (LIBS) as the readout method. Copper(I) ions formed in situ by the reduction of copper(II) ions in the presence of sodium ascorbate (AA) can catalyze the cycloaddition reaction between an alkyne and an azide (CuAAC). In the presence of copper(II) ions and AA, the AgNPs-BSA-Azide can be clicked with the BSA-Alkyne via a cycloaddition reaction to form a BSA-triazole-BSA-AgNPs sandwich conjugate based on this CuAAC&amp;LIBS system. The intensity of the LIBS signal from the residual AgNPs-BSA-Azide in the supernatant is inversely proportional to the concentration of copper(II) ions. The sensor shows a good detection limit of 0.045 nM and a linear relationship between copper ions concentration and the LIBS signal. This click-based method will broaden the potential applications of LIBS for metal detection in drinking water and environment.", "Keywords": "Laser-induced breakdown spectroscopy ; Ag nanoparticles ; Click chemistry ; Copper(Ⅱ) ions detection", "DOI": "10.1016/j.snb.2022.132594", "PubYear": 2022, "Volume": "371", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Cao", "Affiliation": "National Research Center of Intelligent Equipment for Agriculture, Beijing Academy of Agriculture and Forestry Sciences, Beijing 100097, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Research Center of Intelligent Equipment for Agriculture, Beijing Academy of Agriculture and Forestry Sciences, Beijing 100097, China;Guangxi Key Laboratory of Intelligent Control and Maintenance of Power Equipment, Guangxi University, Nanning 530004, China"}, {"AuthorId": 3, "Name": "Shixiang Ma", "Affiliation": "National Research Center of Intelligent Equipment for Agriculture, Beijing Academy of Agriculture and Forestry Sciences, Beijing 100097, China"}, {"AuthorId": 4, "Name": "Daming Dong", "Affiliation": "National Research Center of Intelligent Equipment for Agriculture, Beijing Academy of Agriculture and Forestry Sciences, Beijing 100097, China;Corresponding author"}], "References": [{"Title": "An in-situ plasmonic spectroscopy based biosensor for detection of copper (II) ions highlighting analytical specifications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129103", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 96006176, "Title": "An integrated latent Dirichlet allocation and Word2vec method for generating the topic evolution of mental models from global to local", "Abstract": "Mental models play a crucial role in explaining and driving human innovation activities. To help researchers clarify the changes of mental models in various innovation situations, an exploration of its topic dynamic evolution changes is urgently needed. However, most existing works have discussed the topic-semantic distributions of collected documents along the overall timeline, which ignores the semantic details of fusion and evolution between topics in continuous time. This paper discovers and reveals the multi-level information evolving of topics, by integrating latent Dirichlet allocation (LDA) and Word2vec harmoniously to generate the topic evolution maps of the corpus from global to local perspectives. Which include topic distribution trends and their dynamic evolution under the overall time series, as well as the merging and splitting of semantic information between topics in the adjacent time span. These reveal the correlation between topics and the full life cycle of a topic emerging, developing, maturing, and fading. Then, the integrated method was used to perform an analysis of topic evolution with 3984 abstracts of mental model-related papers published between 1980 and 2020. Finally, the performance of the proposed method was compared to that of three traditional topic evolution generated methods based on the standard evaluation metrics. The experimental results demonstrated that our method outperforms other methods both in terms of the content and strength of topic evolution. The proposed method could mine the latent evolution information more clearly and comprehensively from a vast number of papers and is also suited to the various applications of expert systems related to information mining works.", "Keywords": "Mental models ; Topic evolution ; Word2vec ; Semantic correlation", "DOI": "10.1016/j.eswa.2022.118695", "PubYear": 2023, "Volume": "212", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Mechanism Theory and Equipment Design of Ministry of Education, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Mechanism Theory and Equipment Design of Ministry of Education, Tianjin University, Tianjin, China;Correspondence to: Key Laboratory of Mechanism Theory and Equipment Design of Ministry of Education, Tianjin University, No. 135 Yaguan Road, Haihe Education Park, Tianjin, 300350, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Mechanism Theory and Equipment Design of Ministry of Education, Tianjin University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Mechanism Theory and Equipment Design of Ministry of Education, Tianjin University, Tianjin, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Mechanism Theory and Equipment Design of Ministry of Education, Tianjin University, Tianjin, China;Tianjin Ren’ai College, Tianjin, China"}], "References": [{"Title": "A service recommendation algorithm with the transfer learning based matrix factorization to improve cloud security", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "513", "Issue": "", "Page": "98", "JournalTitle": "Information Sciences"}, {"Title": "Detecting latent topics and trends in educational technologies over four decades using structural topic modeling: A retrospective of all volumes of Computers & Education", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "103855", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 96006183, "Title": "Normal spaces associated with fuzzy nano M -open sets and its application", "Abstract": "In this paper, we introduce some new spaces called fuzzy nano M normal spaces and strongly fuzzy nano M normal spaces with the help of fuzzy nano M open sets in fuzzy nano topological space. Also, find their relations among themselves and with already existing spaces. Also, we study some basic properties and the characterizations of these normal spaces. The stated properties are quantified with numerical data. Furthermore, an algorithm for Multiple Attribute Decision-Making (MADM) with an application regarding candidates choose their works by using fuzzy nano topological spaces is developed. © 2023, International Scientific Research Publications. All rights reserved.", "Keywords": "fuzzy nano M normal space; fuzzy nano M-closed set; fuzzy nano M-open set; Fuzzy nano open set; fuzzy score function; strongly fuzzy nano M normal space", "DOI": "10.22436/jmcs.029.02.05", "PubYear": 2022, "Volume": "29", "Issue": "2", "JournalId": 34363, "JournalTitle": "Journal of Mathematics and Computer Science", "ISSN": "", "EISSN": "2008-949X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Jaya Engineering College, Tamil Nadu, Tiruvallur, 602 024, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematics Section (FEAT), Annamalai University, Annamalai Nagar, 608 002, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "PG and Research Department of Mathematics, Government Arts College (Autonomous), Karur, 639 005, India; Department of Mathematics, Annamalai University, Annamalai Nagar, 608 002, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Annamalai University, Annamalai Nagar, 608 002, India"}], "References": []}, {"ArticleId": 96006192, "Title": "Mathematical Modelling and Heuristic Approaches to Job-shop Scheduling Problem with Conveyor-based Continuous Flow Transporters", "Abstract": "Job-shop Scheduling Problem (JSP) with conveyor-based Continuous Flow Transporters (CFTs) is a general production scheduling problem in the flexible manufacturing systems in practical electronics manufacturing industry. In such a problem setting, the jobs are processed on the machines which are connected in series via the conveyor, and the conveyor segments serve as CFTs by transferring the jobs to the machines automatically. The jobs have to obey First In First Out (FIFO) constraint and limited buffer constraint during their transportation and processing in the system. The objective of scheduling is to find an order in which the jobs enter into the system such that the makespan is minimized. This paper investigates the optimization of the problem by the mathematical modelling and heuristic approaches. The Mathematical Programming (MP) model of the problem is presented and it is used as an exact solution approach for solving small instances optimally. To solve larger problem, the Simulated Annealing (SA) algorithm with a solution perturbation Neighbourhood Generation Scheme (NGS) is proposed to generate near-optimal solutions. The performance of the two approaches to the problem is evaluated by conducting computational experiment. The performance of the MP approach to the problem is numerically investigated, and the performance of the proposed SA algorithm with the solution perturbation NGS is evaluated by comparing against the SA algorithm with two conventional NGSs. The computational results show the superiority of the proposed algorithm over the conventional ones. Furthermore, a comparative study on the performance of the MP approach and the heuristic approach to the problem is implemented. In addition, sensitivity analysis is conducted to investigate the effects of buffer capacity on the performance of the manufacturing system.", "Keywords": "Job-shop scheduling ; Conveyor-based continuous flow transporters ; Limited buffer capacity ; Mathematical modelling ; Simulated annealing algorithm", "DOI": "10.1016/j.cor.2022.105998", "PubYear": 2022, "Volume": "148", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Nanyang Technological University, 50 Nanyang Avenue, 639798, Singapore;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Nanyang Technological University, 50 Nanyang Avenue, 639798, Singapore"}], "References": [{"Title": "Accelerating the calculation of makespan used in scheduling improvement heuristics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "130", "Issue": "", "Page": "105233", "JournalTitle": "Computers & Operations Research"}, {"Title": "Genetic programming-based hyper-heuristic approach for solving dynamic job shop scheduling problem with extended technical precedence constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "134", "Issue": "", "Page": "105401", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 96006202, "Title": "Fixed/Preassigned-time synchronization of high-dimension-valued fuzzy neural networks with time-varying delays via nonseparation approach", "Abstract": "For the high-dimension-valued fuzzy neural networks (HDVFNNs) models with time-varying delays, the fixed/preassigned-time synchronization is explored deeply in this paper. Instead of the separation approach, a direct method in complex field or quaternion skew field is fully utilized, which is suitable for a wider range. Furthermore, two fuzzy controllers without sign function based on the direct method are designed, which can effectively avoid the frequent switching and chattering phenomena involved by sign functions. Then, we derive two sufficient conditions to realize the desired the fixed/preassigned-time synchronization of the addressed HDVFNNs. Finally, the availability of notional researches has been testified by two numerical simulations.", "Keywords": "High-dimension-valued fuzzy neural networks ; Fixed-time synchronization ; Preassigned-time synchronization ; Nonseparation approach", "DOI": "10.1016/j.knosys.2022.109774", "PubYear": 2022, "Volume": "255", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Systems Science, Shandong University of Science and Technology, Qingdao 266590, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Systems Science, Shandong University of Science and Technology, Qingdao 266590, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao 266590, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Systems Science, Shandong University of Science and Technology, Qingdao 266590, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Complexity Science, Qingdao University, Qingdao 266071, China"}], "References": [{"Title": "Finite Time Synchronization of Delayed Quaternion Valued Neural Networks with Fractional Order", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "5", "Page": "3607", "JournalTitle": "Neural Processing Letters"}, {"Title": "Non-chattering quantized control for synchronization in finite–fixed time of delayed Cohen–Grossberg-type fuzzy neural networks with discontinuous activation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "23", "Page": "16557", "JournalTitle": "Neural Computing and Applications"}, {"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> synchronization and stability analysis for neural networks in the fractional-order multi-dimension field", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> Wen", "PubYear": 2021, "Volume": "231", "Issue": "", "Page": "107404", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Novel global polynomial stability criteria of impulsive complex-valued neural networks with multi-proportional delays", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "4", "Page": "2913", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Mean-square stability of stochastic quaternion-valued neural networks with variable coefficients and neutral delays", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Zhen<PERSON> Zhao", "PubYear": 2022, "Volume": "471", "Issue": "", "Page": "130", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 96006212, "Title": "Resonance compensation research of tip-tilt mirror’s 2-DOF tracking-disturbance rejection problem", "Abstract": "This paper discusses the target tracking problem with high-frequency disturbance rejection demand for the tip-tilt mirrors component in electro-optical systems, where the mechanical vibration disturbances always exist near the system’s resonance. After analyzing the influence of resonance to the tracking performance, a modified two degrees of freedom control scheme is proposed. Specifically, utilizing the decoupling capacity, the feedback control without resonance compensation is designed to attenuate system resonance’s neighboring disturbances, and a feedforward controller is added to make up for the sacrifice in tracking performance. As the tracking performances are difficult to be fused and judged, the parameters’ tuning problems are skillfully translated into a proportion optimization problem with a genetic algorithm. By this method, the controller can be finely designed and tuned to match particular systems and meet the performance demands. A series of simulations and experiments illustrate the effectiveness of this new 2-DOF control scheme.", "Keywords": "Resonance compensation ; 2 degrees of freedom design ; Parameter tuning ; Engineering application", "DOI": "10.1016/j.sna.2022.113837", "PubYear": 2022, "Volume": "346", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Optics and Electronics, Chinese Academy of Science, Chengdu 610209, China;Key Laboratory of Optical Engineering, Chinese Academy of Sciences, Chengdu 610209, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Optics and Electronics, Chinese Academy of Science, Chengdu 610209, China;Key Laboratory of Optical Engineering, Chinese Academy of Sciences, Chengdu 610209, China;University of Chinese Academy of Science, Beijing 100039, China;Corresponding author at: Institute of Optics and Electronics, Chinese Academy of Science, Chengdu 610209, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Optics and Electronics, Chinese Academy of Science, Chengdu 610209, China;Key Laboratory of Optical Engineering, Chinese Academy of Sciences, Chengdu 610209, China;University of Chinese Academy of Science, Beijing 100039, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Optics and Electronics, Chinese Academy of Science, Chengdu 610209, China;Key Laboratory of Optical Engineering, Chinese Academy of Sciences, Chengdu 610209, China;University of Chinese Academy of Science, Beijing 100039, China"}], "References": []}, {"ArticleId": 96006219, "Title": "Paving the way for enriched metadata of linguistic linked data", "Abstract": "<p>The need for reusable, interoperable, and interlinked linguistic resources in Natural Language Processing downstream tasks has been proved by the increasing efforts to develop standards and metadata suitable to represent several layers of information. Nevertheless, despite these efforts, the achievement of full compatibility for metadata in linguistic resource production is still far from being reached. Access to resources observing these standards is hindered either by (i) lack of or incomplete information, (ii) inconsistent ways of coding their metadata, and (iii) lack of maintenance. In this paper, we offer a quantitative and qualitative analysis of descriptive metadata and resources availability of two main metadata repositories: LOD Cloud and Annohub. Furthermore, we introduce a metadata enrichment, which aims at improving resource information, and a metadata alignment to META-SHARE ontology, suitable for easing the accessibility and interoperability of such resources.</p>", "Keywords": "", "DOI": "10.3233/SW-222994", "PubYear": 2022, "Volume": "13", "Issue": "6", "JournalId": 13255, "JournalTitle": "Semantic Web", "ISSN": "1570-0844", "EISSN": "2210-4968", "Authors": [{"AuthorId": 1, "Name": "Maria Pia di Buono", "Affiliation": "UniOr NLP Research Group, Department of Literary, Linguistics and Comparative Studies, University of Naples “L’Orientale”, Napoli, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CISUC, Department of Informatics Engineering, University of Coimbra, Coimbra, Portugal"}, {"AuthorId": 3, "Name": "Verginica Barbu Mititelu", "Affiliation": "Romanian Academy Research Institute for Artificial Intelligence, Bucharest, Romania"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Systems and Communication, University of Milano-Bicocca, Milano, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UniOr NLP Research Group, Department of Literary, Linguistics and Comparative Studies, University of Naples “L’Orientale”, Napoli, Italy"}], "References": []}, {"ArticleId": 96006222, "Title": "Fruit detection and positioning technology for a Camellia oleifera C. Abel orchard based on improved YOLOv4-tiny model and binocular stereo vision", "Abstract": "In the complex environment of an orchard, changes in illumination, leaf occlusion, and fruit overlap make it challenging for mobile picking robots to detect and locate oil-seed camellia fruit. To address this problem, YOLO-Oleifera was developed as a fruit detection model method based on a YOLOv4-tiny model, To obtain clustering results appropriate to the size of the Camellia oleifera fruit, the k-means++ clustering algorithm was used instead of the k-means clustering algorithm used by the YOLOv4-tiny model to determine bounding box priors. Two convolutional kernels of 1 × 1 and 3 × 3 were respectively added after the second and third CSPBlock modules of the YOLOv4-tiny model. This model allows the learning of Camellia oleifera fruit feature information and reduces overall computational complexity. Compared with the classic stereo matching method based on binocular camera images, this method innovatively used the bounding box generated by the YOLO-Oleifera model to extract the region of interest of the fruit, and then adaptively performs stereo matching according to the generation mechanism of the bounding box. This allows the determination of disparity and facilitates the subsequent use of the triangulation principle to determine the picking position of the fruit. An ablation experiment demonstrated the effective improvement of the YOLOv4-tiny model. Camellia oleifera fruit images obtained under sunlight and shading conditions were used to test the YOLO-Oleifera model, and the model robustly detected the fruit under different illumination conditions. Occluded Camellia oleifera fruit decreased precision and recall due to the loss of semantic information. Comparison of this model with deep learning models YOLOv5-s,YOLOv3-tiny, and YOLOv4-tiny, the YOLO-Oleifera model achieved the highest AP of 0.9207 with the smallest data weight of 29 MB. The YOLO-Oleifera model took an average of 31 ms to detect each fruit image, fast enough to meet the demand for real-time detection. The algorithm exhibited high positioning stability and robust function despite changes in illumination. The results of this study can provide a technical reference for the robust detection and positioning of Camellia oleifera fruit by a mobile picking robot in a complex orchard environment.", "Keywords": "Mobile harvesting robots ; Binocular stereo vision ; Image detection ; Deep learning ; YOLOv4-tiny model ; Stereo matching ; Fruit positioning", "DOI": "10.1016/j.eswa.2022.118573", "PubYear": 2023, "Volume": "211", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Urban and Rural Construction, Zhongkai University of Agriculture and Engineering, 501 Zhongkai Road, Guangzhou 510225, China;Foshan-Zhongke Innovation Research Institute of Intelligent Agriculture, Foshan 528010, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, South China Agricultural University, 483 Wushan Road, Guangzhou 510642, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, South China Agricultural University, 483 Wushan Road, Guangzhou 510642, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Urban and Rural Construction, Zhongkai University of Agriculture and Engineering, 501 Zhongkai Road, Guangzhou 510225, China;Foshan-Zhongke Innovation Research Institute of Intelligent Agriculture, Foshan 528010, China"}], "References": [{"Title": "Faster R-CNN for multi-class fruit detection using a robotic vision system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "168", "Issue": "", "Page": "107036", "JournalTitle": "Computer Networks"}, {"Title": "A deep neural network approach towards real-time on-branch fruit recognition for precision horticulture", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "113594", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Score-based mask edge improvement of Mask-RCNN for segmentation of fruit and vegetables", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "190", "Issue": "", "Page": "116205", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Tea chrysanthemum detection under unstructured environments using the TC-YOLO model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116473", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Poly-YOLO: higher speed, more precise detection and instance segmentation for YOLOv3", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "10", "Page": "8275", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 96006283, "Title": "A novel end-effector upper limb rehabilitation robot: Kinematics modeling based on dual quaternion and low-speed spiral motion tracking control", "Abstract": "<p>For patients with upper limb dysfunction after stroke, robot-assisted rehabilitation training plays an important role in functional recovery. The existing upper limb rehabilitation robots have some problems, such as complex mechanisms, insufficient compliance, and can only realize the rehabilitation training of shoulder and elbow joints in the horizontal plane. This research proposes a novel end-effector upper limb rehabilitation robot with three degrees of freedom. Two horizontal rotation freedoms are driven by motors and one vertical translation freedom is driven by a pneumatic cylinder. So it can realize the spatial rehabilitation training of shoulder and elbow joints. The rotation and translation transformation of the robot can be represented by a dual quaternion, which is concise in form and clear in the physical meaning. Therefore, this article adopts dual quaternions to complete the robot’s kinematics modeling, inverse kinematics calculation, and terminal spiral motion trajectory planning. To improve the low-speed moving performance of the spiral motion, a sliding mode control strategy plus feedforward compensation is employed to control the displacement of the cylinder. Experiments show that the robot can realize proximal joints training and has good position tracking accuracy (tracking error is within 2 mm) with smoothness under the proposed control strategy, which can guarantee the accuracy and comfort of passive rehabilitation training, contributing to restoring the function of the impairment upper limbs.</p>", "Keywords": "", "DOI": "10.1177/17298806221118855", "PubYear": 2022, "Volume": "19", "Issue": "4", "JournalId": 1212, "JournalTitle": "International Journal of Advanced Robotic Systems", "ISSN": "1729-8814", "EISSN": "1729-8814", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Henan University of Science and Technology, Luoyang, China;Henan Provincial Key Laboratory of Robotics and Intelligent Systems, Luoyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Henan University of Science and Technology, Luoyang, China;Henan Provincial Key Laboratory of Robotics and Intelligent Systems, Luoyang, China;Collaborative Innovation Center of Machinery Equipment Advanced Manufacturing of Henan Province, Luoyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Henan University of Science and Technology, Luoyang, China;Henan Provincial Key Laboratory of Robotics and Intelligent Systems, Luoyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Henan University of Science and Technology, Luoyang, China;Henan Provincial Key Laboratory of Robotics and Intelligent Systems, Luoyang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Zhengzhou University of Light Industry, Zhengzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The First Affiliated Hospital of Henan University of Science and Technology, Luoyang, China"}], "References": [{"Title": "A Novel 3-RRR Spherical Parallel Instrument for Daily Living Emulation (SPINDLE) for Functional Rehabilitation of Patients with Stroke", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "3", "Page": "172988142110123", "JournalTitle": "International Journal of Advanced Robotic Systems"}]}, {"ArticleId": 96006284, "Title": "Hybrid Enhanced Binary Honey Badger Algorithm with Quadratic Programming for Cardinality Constrained Portfolio Optimization", "Abstract": "<p>A portfolio selection problem with cardinality constraints has been proved to be an NP-hard problem, and it is difficult to solve by the traditional optimization methods. This study considers it to be a hybrid of a classical feature selection problem and a standard mean-variance (MV) portfolio selection model. In particular, we propose a new hybrid meta-heuristic algorithm that combines an enhanced binary honey badger algorithm (EBHBA) with quadratic programming to address this issue. First, we employ the proposed EBHBA algorithm to select a portfolio of [Formula: see text] stocks from [Formula: see text] candidate stocks. Second, based on its choice we transform the problem into a mean-variance model, whose objective function could be defined as the fitness function of EBHBA. Finally, the optimal solution to the model could be found with the quadratic programming method. We also test our approach using the benchmark data sets available at the OR-Library involving real capital markets, where indices are derived from major stock markets around the world. Computational results demonstrate that the proposed method can achieve a satisfactory result for portfolio selection with cardinality constraints and perform well in searching non-dominated portfolios with high expected returns.</p>", "Keywords": "Metaheuristics; binary honey badger algorithm; portfolio optimization; cardinality constrained portfolio selection; quadratic programming", "DOI": "10.1142/S0129054122420151", "PubYear": 2022, "Volume": "33", "Issue": "6n07", "JournalId": 9642, "JournalTitle": "International Journal of Foundations of Computer Science", "ISSN": "0129-0541", "EISSN": "1793-6373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Finance and Mathematics, West Anhui University, Lu’an 237012, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Finance and Mathematics, West Anhui University, Lu’an 237012, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Finance and Mathematics, West Anhui University, Lu’an 237012, P. R. China"}, {"AuthorId": 4, "Name": "Guocheng Li", "Affiliation": "School of Finance and Mathematics, West Anhui University, Lu’an 237012, P. R. China"}], "References": [{"Title": "Marine Predators Algorithm: A nature-inspired metaheuristic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "113377", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hybrid Harris Hawks optimization algorithm with simulated annealing for feature selection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "593", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "An improved Dragonfly Algorithm for feature selection", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106131", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Squirrel search algorithm for portfolio optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "114968", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A rapidly converging artificial bee colony algorithm for portfolio optimization", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "233", "Issue": "", "Page": "107505", "JournalTitle": "Knowledge-Based Systems"}]}]