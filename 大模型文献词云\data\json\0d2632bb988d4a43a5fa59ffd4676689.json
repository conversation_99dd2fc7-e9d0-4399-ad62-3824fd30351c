[{"ArticleId": 87893373, "Title": "The impact of blockchain on e-commerce: A framework for salient research topics", "Abstract": "Blockchain-based technologies are predicted as major disruptors for numerous business applications and processes, which bears huge implications for e-commerce. Given the ability of blockchain and related technologies to create so-called “trustless systems” with idiosyncratic properties, various business models and established processes that have emerged over the years to ensure trust, reliability and enforceability in business-to-consumer (B2C), business-to-business (B2B), business-to-government (B2G) and consumer-to-consumer (C2C) relations need to be questioned and potentially adjusted. Blockchain has the potential to shake the foundation of e-commerce by enabling exchange relations that are trustless and operate without dedicated intermediaries or even central authorities in the case of permissionless blockchains. Furthermore, the exchange of information and value between companies and consumers might change considerably by enabling unified access to immutable data along the entire supply chain. In this paper, a framework and 19 high-level research questions are developed to inspire researchers to closely investigate the potential impact of blockchain on e-commerce. The main categories include (a) technological, (b) legal and (c) organizational and quality issues as well as (d) consumer issues. This paper illustrates how blockchain potentially impacts different elements of e-commerce in these respective areas.", "Keywords": "Blockchain ; Distributed Ledger Technology ; E-Commerce ; Research Framework", "DOI": "10.1016/j.elerap.2021.101054", "PubYear": 2021, "Volume": "48", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of International Management, Modul University Vienna, Am Kahlenberg 1, 1190 Vienna, Austria;Corresponding Author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Private Law, Universität Bern, Schanzeneckstrasse 1, Postfach, 3001 Bern, Austria"}], "References": [{"Title": "A survey on the security of blockchain systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "841", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "From blockchain consensus back to Byzantine consensus", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "760", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Security and Privacy on Blockchain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 87893412, "Title": "Recent advancements in self-healing composite elastomers for flexible strain sensors: Materials, healing systems, and features", "Abstract": "This article systematically reviewed recently advanced progress in the development of self-healing composite elastomers for flexible strain sensors. Prevalent self-healing elastomeric matrixes, including natural rubber (NR), polydimethylsiloxane (PDMS), and polyurethane (PU), as well as other novel elastomers, were comprehensively reviewed. The properties of different dynamic linkages, the choice of various conductive fillers, and critical performance parameters were summarized and discussed in detail to provide insights into the rational combination of healing mechanisms and hybrid conductive networks to control the sensing performances. We expect this review could inspire the development of novel self-healing composite elastomers for high-performance flexible strain sensors.", "Keywords": "Strain sensors ; Composite elastomer ; Self-healing ; Conductive polymer ; Sensitivity", "DOI": "10.1016/j.sna.2021.112800", "PubYear": 2021, "Volume": "329", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Packaging Materials and Technology of Hunan Province, Hunan University of Technology, Zhuzhou, 412007, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Building and Real Estate, Hong Kong Polytechnic University, Hong Kong, 518000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Packaging Materials and Technology of Hunan Province, Hunan University of Technology, Zhuzhou, 412007, China;Corresponding authors at: Key Laboratory of Advanced Packaging Materials and Technology of Hunan Province, Hunan University of Technology, Zhuzhou, 412007, China and National Engineering Research Center for Advanced Polymer Processing Technology, Zhengzhou University, Zhengzhou, 450000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Packaging Materials and Technology of Hunan Province, Hunan University of Technology, Zhuzhou, 412007, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Packaging Materials and Technology of Hunan Province, Hunan University of Technology, Zhuzhou, 412007, China;National Engineering Research Center for Advanced Polymer Processing Technology, Zhengzhou University, Zhengzhou, 450000, China;Corresponding authors at: Key Laboratory of Advanced Packaging Materials and Technology of Hunan Province, Hunan University of Technology, Zhuzhou, 412007, China and National Engineering Research Center for Advanced Polymer Processing Technology, Zhengzhou University, Zhengzhou, 450000, China"}], "References": []}, {"ArticleId": 87893417, "Title": "On the solution bound of two-sided scaffold filling", "Abstract": "In this paper, we propose an algorithm which approximates the Two-Sided Scaffold Filling problem to a performance ratio 1.4 + ε . This is achieved through a deep investigation of the optimal solution structure of Two-Sided Scaffold Filling. We make use of a relevant graph aiming at a solution of a Two-Sided Scaffold Filling instance, and evaluate the optimal solution value by the number of connected components in this graph. We show that an arbitrary optimal solution can be transformed into one whose relevant graph admits connected components that are available to compare with the solution of our algorithm in terms of their values. The performance ratio 1.4 + ε is obtained by comparing the bound of such an optimal solution with the solution of our algorithm.", "Keywords": "Algorithm ; Complexity ; Performance ratio ; Genome ; Scaffold", "DOI": "10.1016/j.tcs.2021.04.024", "PubYear": 2021, "Volume": "873", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Jingjing Ma", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, Shandong, China"}, {"AuthorId": 2, "Name": "Dam<PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, Shandong, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, Shandong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Montana State University, Bozeman, MT 59717, USA"}], "References": []}, {"ArticleId": 87893514, "Title": "Multimodal regularized linear models with flux balance analysis for mechanistic integration of omics data", "Abstract": "Motivation <p>High-throughput biological data, thanks to technological advances, have become cheaper to collect, leading to the availability of vast amounts of omic data of different types. In parallel, the in silico reconstruction and modeling of metabolic systems is now acknowledged as a key tool to complement experimental data on a large scale. The integration of these model- and data-driven information is therefore emerging as a new challenge in systems biology, with no clear guidance on how to better take advantage of the inherent multisource and multiomic nature of these data types while preserving mechanistic interpretation.</p> Results <p>Here, we investigate different regularization techniques for high-dimensional data derived from the integration of gene expression profiles with metabolic flux data, extracted from strain-specific metabolic models, to improve cellular growth rate predictions. To this end, we propose ad-hoc extensions of previous regularization frameworks including group, view-specific and principal component regularization and experimentally compare them using data from 1143 Saccharomyces cerevisiae strains. We observe a divergence between methods in terms of regression accuracy and integration effectiveness based on the type of regularization employed. In multiomic regression tasks, when learning from experimental and model-generated omic data, our results demonstrate the competitiveness and ease of interpretation of multimodal regularized linear models compared to data-hungry methods based on neural networks.</p> Availability and implementation <p>All data, models and code produced in this work are available on GitHub at https://github.com/Angione-Lab/HybridGroupIPFLasso_pc2Lasso.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "Lasso;Machine learning;flux balance analysis;multi-omics;regression;regularisation", "DOI": "10.1093/bioinformatics/btab324", "PubYear": 2021, "Volume": "37", "Issue": "20", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing, Engineering and Digital Technologies, Teesside University, Middlesbrough, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing, Engineering and Digital Technologies, Teesside University, Middlesbrough, UK;Department of Biology, University of Padova, Padova, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computing, Engineering and Digital Technologies, Teesside University, Middlesbrough, UK;Healthcare Innovation Centre, Teesside University, Middlesbrough, UK;Centre for Digital Innovation, Teesside University, Middlesbrough, UK"}], "References": [{"Title": "Exploiting transfer learning for the reconstruction of the human gene regulatory network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; Domenica D’Elia", "PubYear": 2020, "Volume": "36", "Issue": "5", "Page": "1553", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 87893768, "Title": "Modeling <PERSON><PERSON><PERSON>’s dynamic modeling problem: drift towards a boundary of safety", "Abstract": "We build a system dynamics model based on a conceptual model originally proposed by safety scientist <PERSON><PERSON> to explore the dynamics of a safety system subject to pressures for performance improvement. <PERSON><PERSON><PERSON> described forces that generate a drift in the boundary of acceptable performance that can push the organization towards “flirting with the margin” and thus operate at very high risk of catastrophic safety failure. Simulations of the model faithfully replicate the behavior described by <PERSON><PERSON><PERSON> and others in a variety of scenarios. Simulation experiments further illuminate the potential for risky behavior and point towards some approaches to better system safety.", "Keywords": "Safety science; Human factors; System dynamics; Organizational drift; Dynamic modeling", "DOI": "10.1007/s10111-021-00668-x", "PubYear": 2022, "Volume": "24", "Issue": "1", "JournalId": 2896, "JournalTitle": "Cognition, Technology & Work", "ISSN": "1435-5558", "EISSN": "1435-5566", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Brandeis University, Waltham, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Florida, Jacksonville, USA"}], "References": []}, {"ArticleId": 87893789, "Title": "An improved salp swarm algorithm for complex multi-modal problems", "Abstract": "<p>In this paper, improved salp swarm algorithm is proposed. The algorithm integrates (1) random opposition-based learning (2) multiple leadership and (3) simulated annealing in swarm intelligence-based metaheuristic salp swarm algorithm. This integration increases the exploration and exploitation of the original salp swarm algorithm. Hence, the effectiveness of the proposed algorithm is better for complex multi-modal problems. The algorithm is tested on several standard numerical benchmark functions and CEC-2015 benchmarks. Results are compared with some well-known metaheuristics. The results represent the merit of the proposed algorithm with respect to other algorithms. The improved salp swarm algorithm is applied for feed-forward neural network training. Performance is compared with other metaheuristic-based feedforward neural network trainers for different data sets. The results show the efficiency and effectiveness of proposed algorithm in solving complex multi-modal problems.</p>", "Keywords": "Optimization; Metaheuristics; Improved salp swarm algorithm; Salp swarm algorithm; Feedforward neural networks training; Classification", "DOI": "10.1007/s00500-021-05757-7", "PubYear": 2021, "Volume": "25", "Issue": "15", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Malaviya National Institute of Technology Jaipur, Jaipur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Malaviya National Institute of Technology Jaipur, Jaipur, India"}], "References": [{"Title": "Numerical optimization and feed-forward neural networks training using an improved optimization algorithm: multiple leader salp swarm algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "3", "Page": "1233", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Gaussian mutational chaotic fruit fly-built optimization and feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112976", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Boosting salp swarm algorithm by sine cosine algorithm and disrupt operator for feature selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "113103", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Improved Salp Swarm Algorithm based on opposition based learning and novel local search algorithm for feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "113122", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Emended salp swarm algorithm for multiobjective electric power dispatch problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106172", "JournalTitle": "Applied Soft Computing"}, {"Title": "An improved scheme for digital mammogram classification using weighted chaotic salp swarm algorithm-based kernel extreme learning machine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106266", "JournalTitle": "Applied Soft Computing"}, {"Title": "A modified salp swarm algorithm for task assignment problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106445", "JournalTitle": "Applied Soft Computing"}, {"Title": "A new hybrid SSA-TA: Salp Swarm Algorithm with threshold accepting for band selection in hyperspectral images", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106534", "JournalTitle": "Applied Soft Computing"}, {"Title": "Selection scheme sensitivity for a hybrid Salp Swarm Algorithm: analysis and applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1149", "JournalTitle": "Engineering with Computers"}, {"Title": "Dynamic Salp swarm algorithm for feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113873", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Hybridizing Gray Wolf Optimization (GWO) with Grasshopper Optimization Algorithm (GOA) for text feature selection and clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106651", "JournalTitle": "Applied Soft Computing"}, {"Title": "An improved image denoising technique using differential evolution-based salp swarm algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "3", "Page": "1941", "JournalTitle": "Soft Computing"}, {"Title": "Multi-area economic dispatch with stochastic wind power using Salp Swarm Algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "", "Page": "100044", "JournalTitle": "Array"}]}, {"ArticleId": 87893847, "Title": "Data Provenance", "Abstract": "Data provenance has evolved from a niche topic to a mainstream area of research in databases and other research communities. This article gives a comprehensive introduction to data provenance. The main focus is on provenance in the context of databases. However, it will be insightful to also consider connections to related research in programming languages, software engineering, semantic web, formal logic, and other communities. The target audience are researchers and practitioners that want to gain a solid understanding of data provenance and the state-of-the-art in this research area. The article only assumes that the reader has a basic understanding of database concepts, but not necessarily any prior exposure to provenance. © 2021 Now Publishers Inc. All rights reserved.", "Keywords": "Databases; Web Science; Metadata Management; Trust and Provenance", "DOI": "10.1561/1900000068", "PubYear": 2021, "Volume": "9", "Issue": "3-4", "JournalId": 32088, "JournalTitle": "Foundations and Trends® in Databases", "ISSN": "1931-7883", "EISSN": "1931-7891", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Illinois Institute of Technology, USA"}], "References": []}, {"ArticleId": 87894070, "Title": "A Hybrid Method of Coreference Resolution in Information Security", "Abstract": "In the field of information security, a gap exists in the study of coreference resolution of entities. A hybrid method is proposed to solve the problem of coreference resolution in information security. The work consists of two parts: the first extracts all candidates (including noun phrases, pronouns, entities, and nested phrases) from a given document and classifies them; the second is coreference resolution of the selected candidates. In the first part, a method combining rules with a deep learning model (Dictionary BiLSTM-Attention-CRF, or DBAC) is proposed to extract all candidates in the text and classify them. In the DBAC model, the domain dictionary matching mechanism is introduced, and new features of words and their contexts are obtained according to the domain dictionary. In this way, full use can be made of the entities and entity-type information contained in the domain dictionary, which can help solve the recognition problem of both rare and long entities. In the second part, candidates are divided into pronoun candidates and noun phrase candidates according to the part of speech, and the coreference resolution of pronoun candidates is solved by making rules and coreference resolution of noun phrase candidates by machine learning. Finally, a dataset is created with which to evaluate our methods using information security data. The experimental results show that the proposed model exhibits better performance than the other baseline models. © 2020 Tech Science Press. All rights reserved.", "Keywords": "BiLSTM-Attention-CRF; Coreference resolution; Hybrid method; Information security; Rules", "DOI": "10.32604/cmc.2020.010855", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "Yongjin Hu", "Affiliation": "Information Engineering University, Zhengzhou, 450000, China"}, {"AuthorId": 2, "Name": "Yuan<PERSON> Guo", "Affiliation": "Information Engineering University, Zhengzhou, 450000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Intelligent Systems Research Centre, School of Computing, Engineering and Intelligent Systems, Ulster University, Magee Campus, Northern Ireland, BT487JL, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Zheng Zhou University, Zhengzhou, 450001, China"}], "References": []}, {"ArticleId": 87894078, "Title": "Image Segmentation of Brain MR Images Using Otsu’s based Hybrid WCMFO Algorithm", "Abstract": "In this study, a novel hybrid Water Cycle Moth-Flame Optimization (WCMFO) algorithm is proposed for multilevel thresholding brain image segmentation in Magnetic Resonance (MR) image slices. WCMFO constitutes a hybrid between the two techniques, comprising the water cycle and moth-flame optimization algorithms. The optimal thresholds are obtained by maximizing the between class variance (<PERSON><PERSON>'s function) of the image. To test the performance of threshold searching process, the proposed algorithm has been evaluated on standard benchmark of ten axial T2-weighted brain MR images for image segmentation. The experimental outcomes infer that it produces better optimal threshold values at a greater and quicker convergence rate. In contrast to other state-of-the-art methods, namely Adaptive Wind Driven Optimization (AWDO), Adaptive Bacterial Foraging (ABF) and Particle Swarm Optimization (PSO), the proposed algorithm has been found to be better at producing the best objective function, Peak Signal-to-Noise Ratio (PSNR), Standard Deviation (STD) and lower computational time values. Further, it was observed thatthe segmented image gives greater detail when the threshold level increases. Moreover, the statistical test result confirms that the best and mean values are almost zero and the average difference between best and mean value 1.86 is obtained through the 30 executions of the proposed algorithm.Thus, these images will lead to better segments of gray, white and cerebrospinal fluid that enable better clinical choices and diagnoses using a proposed algorithm. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Brain MR image; Hybrid WCMFO algorithm; Image segmentation; Multilevel thresholding; <PERSON><PERSON>'s function", "DOI": "10.32604/cmc.2020.09519", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, University College of Engineering Kancheepuram, Kanchipuram, 631552, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University College of Engineering Kancheepuram, Kanchipuram, 631552, India"}], "References": []}, {"ArticleId": 87894080, "Title": "Efficient 2D Analysis Of Interfacial Thermoelastic Stresses in Multiply Bonded Anisotropic Composites With Thin Adhesives", "Abstract": "In engineering practice, analysis of interfacial thermal stresses in composites is a crucial task for assuring structural integrity when sever environmental temperature changes under operations. In this article, the directly transformed boundary integrals presented previously for treating generally anisotropic thermoelasticity in two-dimension are fully regularized by a semi-analytical approach for modeling thin multi-layers of anisotropic/isotropic composites, subjected to general thermal loads with boundary conditions prescribed. In this process, an additional difficulty, not reported in the literature, arises due to rapid fluctuation of an integrand in the directly transformed boundary integral equation. In conventional analysis, thin adhesives are usually neglected due to modeling difficulties. A major concern arises regarding the modeling error caused by such negligence of the thin adhesives. For investigating the effect of the thin adhesives considered, the regularized integral equation is applied for analyzing interfacial stresses in multiply bonded composites when thin adhesives are considered. Since all integrals are completely regularized, very accurate integration values can be still obtained no matter how the source point is close to the integration element. Comparisons are made for some examples when the thin adhesives are considered or neglected. Truly, this regularization task has laid sound fundamentals for the boundary element method to efficiently analyze the interfacial thermal stresses in 2D thin multiply bonded anisotropic composites. © 2020 Tech Science Press. All rights reserved.", "Keywords": "2D anisotropic elasticity; Boundary element method; Multiply bonded composites; Regularization of boundary integrals; Thermal loading", "DOI": "10.32604/cmc.2020.010417", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Aeronautics and Astronautics, National Cheng Kung University, Tainan, 701, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Aeronautics and Astronautics, National Cheng Kung University, Tainan, 701, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Shiraz University, Shiraz, 71936, Iran"}], "References": []}, {"ArticleId": 87894081, "Title": "Performance Anomaly Detection in Web Services: an RNN-based Approach Using Dynamic Quality of Service Features", "Abstract": "Performance anomaly detection is the process of identifying occurrences that do not conform to expected behavior or correlate with other incidents or events in time series data. Anomaly detection has been applied to areas such as fraud detection, intrusion detection systems, and network systems. In this paper, we propose an anomaly detection framework that uses dynamic features of quality of service that are collected in a simulated setup. Three variants of recurrent neural networks-SimpleRNN, long short term memory, and gated recurrent unit are evaluated. The results reveal that the proposed method effectively detects anomalies in web services with high accuracy. The performance of the proposed anomaly detection framework is superior to that of existing approaches using maximum accuracy and detection rate metrics. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Anomaly detection; Point anomaly; Recurrent neural networks; Simulated data; Web services", "DOI": "10.32604/cmc.2020.010394", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Technology, Monash University, Subang Jaya, 47500, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Business IT, Kookmin University, Seoul, Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Technology, Monash University, Subang Jaya, 47500, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematical and Computer Sciences, Indiana University of Pennsylvania, Indiana, United States"}], "References": []}, {"ArticleId": 87894082, "Title": "Structure-Preserving Dynamics of Stochastic Epidemic Model with the Saturated Incidence Rate", "Abstract": "The structure-preserving features of the nonlinear stochastic models are positivity, dynamical consistency and boundedness. These features have a significant role in different fields of computational biology and many more. Unfortunately, the existing stochastic approaches in literature do not restore aforesaid structure-preserving features, particularly for the stochastic models. Therefore, these gaps should be occupied up in literature, by constructing the structure-preserving features preserving numerical approach. This writing aims to describe the structure-preserving dynamics of the stochastic model. We have analysed the effect of reproduction number in stochastic modelling the same as described in the literature for deterministic modelling. The usual explicit stochastic numerical approaches are time-dependent. We have developed the implicitly driven explicit approach for the stochastic epidemic model. We have proved that the newly developed approach is preserving the structural, dynamical properties as positivity, boundedness and dynamical consistency. Finally, convergence analysis of a newly developed approach and graphically illustration is also presented. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Convergence analysis; Epidemic model; Stochastic numerical approaches", "DOI": "10.32604/cmc.2020.010759", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and General Sciences, Prince Sultan University, Riyadh, Saudi Arabia, Department of Medical Research, China Medical University Hospital, China Medical University, Taichung, 40402, Taiwan, Department of M-Commerce and Multimedia Applications, Asia University, Taichung, 41354, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Stochastic Analysis & Optimization Research Group, Department of Mathematics, Air University, PAF Complex E-9, Islamabad, 44000, Pakistan. ; 5 Faculty of Engineering, University of Central Punjab, Lahore, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Stochastic Analysis and Optimization Research Group, Department of Mathematics, Air University, PAF Complex E-9, Islamabad, 44000, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering, University of Central Punjab, Lahore, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Comsats University, Islamabad, Pakistan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Comsats University, Islamabad, Pakistan"}], "References": []}, {"ArticleId": 87894083, "Title": "Modeling Multi-Targets Sentiment Classification Via Graph Convolutional Networks and Auxiliary Relation", "Abstract": "Existing solutions do not work well when multi-targets coexist in a sentence. The reason is that the existing solution is usually to separate multiple targets and process them separately. If the original sentence has N target, the original sentence will be repeated for N times, and only one target will be processed each time. To some extent, this approach degenerates the fine-grained sentiment classification task into the sentence-level sentiment classification task, and the research method of processing the target separately ignores the internal relation and interaction between the targets. Based on the above considerations, we proposes to use Graph Convolutional Network (GCN) to model and process multi-targets appearing in sentences at the same time based on the positional relationship, and then to construct a graph of the sentiment relationship between targets based on the difference of the sentiment polarity between target words. In addition to the standard target-dependent sentiment classification task, an auxiliary node relation classification task is constructed. Experiments demonstrate that our model achieves new comparable performance on the benchmark datasets: SemEval-2014 Task 4, i.e., reviews for restaurants and laptops. Furthermore, the method of dividing the target words into isolated individuals has disadvantages, and the multi-task learning model is beneficial to enhance the feature extraction ability and expression ability of the model. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Deep learning; Graph convolutional networks (GCN); Sentiment analysis", "DOI": "10.32604/cmc.2020.09913", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Chengdu University of Information Technology, Chengdu, 610225, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Chengdu University of Information Technology, Chengdu, 610225, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Chengdu University of Information Technology, Chengdu, 610225, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Central Washington University, Des Moines, WA  98198, United States"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Chengdu University of Information Technology, Chengdu, 610225, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Chengdu University of Information Technology, Chengdu, 610225, China"}], "References": []}, {"ArticleId": 87894084, "Title": "GACNet: A Generative Adversarial Capsule Network for Regional Epitaxial Traffic Flow Prediction", "Abstract": "With continuous urbanization, cities are undergoing a sharp expansion within the regional space. Due to the high cost, the prediction of regional traffic flow is more difficult to extend to entire urban areas. To address this challenging problem, we present a new deep learning architecture for regional epitaxial traffic flow prediction called GACNet, which predicts traffic flow of surrounding areas based on inflow and outflow information in central area. The method is data-driven, and the spatial relationship of traffic flow is characterized by dynamically transforming traffic information into images through a two-dimensional matrix. We introduce adversarial training to improve performance of prediction and enhance the robustness. The generator mainly consists of two parts: abstract traffic feature extraction in the central region and traffic prediction in the extended region. In particular, the feature extraction part captures nonlinear spatial dependence using gated convolution, and replaces the maximum pooling operation with dynamic routing, finally aggregates multidimensional information in capsule form. The effectiveness of the method is evaluated using traffic flow datasets for two real traffic networks: Beijing and New York. Experiments on highly challenging datasets show that our method performs well for this task. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Adversarial training; Dynamic routing; Feature extraction; Nonlinear spatial dependence; Regional traffic flow", "DOI": "10.32604/cmc.2020.09903", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Li", "Affiliation": "National Pilot School of Software, Yunnan University, Kunming, 650091, China"}, {"AuthorId": 2, "Name": "Hao L", "Affiliation": "National Pilot School of Software, Yunnan University, Kunming, 650091, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Pilot School of Software, Yunnan University, Kunming, 650091, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National Pilot School of Software, Yunnan University, Kunming, 650091, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National Pilot School of Software, Yunnan University, Kunming, 650091, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Applied Science, George Washington University, Washington, DC  20052, United States"}], "References": []}, {"ArticleId": 87894085, "Title": "Non-Exchangeable Error Compensation for Strapdown Inertial Navigation System in High Dynamic Environment", "Abstract": "Strapdown non-exchangeable error compensation technology in high dynamic environment is one of the key technologies of strapdown inertial navigation system. Mathematical platform is used in strapdown inertial navigation system instead of physical platform in traditional platform inertial navigation system, which improves reliability and reduces cost and volume of system. The maximum error source of attitude matrix solution is the non-exchangeable error of rotation due to the non-exchangeable of finite rotation of rigid bodies. The rotation non-exchangeable error reaches the maximum in coning motion, although it can be reduced by shortening the correction period and increasing the real-time calculation. The equivalent rotation vector method is used to modify the attitude to reduce the coning error in this paper. Simulation experiments show that the equivalent rotation vector method can effectively suppress the non-exchangeable error and improve the accuracy of attitude calculation. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Coning motion; Equivalent rotation vector; Error compensation; Simulation experiments; Strapdown", "DOI": "10.32604/cmc.2020.07575", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer and Software, Nanjing University of Information Science and Technology, Nanjing, 210044, China, Jiangsu Engineering Center of Network Monitoring, Nanjing University of Information Science and Technology, Nanjing, 210044, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Engineering Center of Network Monitoring, Nanjing University of Information Science and Technology, Nanjing, 210044, China, School of Automation, Nanjing University of Information Science and Technology, Nanjing, 210044, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Ball State University, Muncie, 47306, United States"}], "References": []}, {"ArticleId": 87894086, "Title": "New Three-dimensional Assessment Model and Optimization of Acoustic Positioning System", "Abstract": "This paper addresses the problem of assessing and optimizing the acoustic positioning system for underwater target localization with range measurement. We present a new three-dimensional assessment model to evaluate the optimal geometric beacon formation whether meets user requirements. For mathematical tractability, it is assumed that the measurements of the range between the target and beacons are corrupted with white Gaussian noise with variance, which is distance-dependent. Then, the relationship between DOP parameters and positioning accuracy can be derived by adopting dilution of precision (DOP) parameters in the assessment model. In addition, the optimal geometric beacon formation yielding the best performance can be achieved via minimizing the values of geometric dilution of precision (GDOP) in the case where the target position is known and fixed. Next, in order to ensure that the estimated positioning accuracy on the region of interest satisfies the precision required by the user, geometric positioning accuracy (GPA), horizontal positioning accuracy (HPA) and vertical positioning accuracy (VPA) are utilized to assess the optimal geometric beacon formation. Simulation examples are designed to illustrate the exactness of the conclusion. Unlike other work that only uses GDOP to optimize the formation and cannot assess the performance of the specified size, this new three-dimensional assessment model can evaluate the optimal geometric beacon formation for each dimension of any point in three-dimensional space, which can provide guidance to optimize the performance of each specified dimension. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Acoustic positioning system; DOP; Optimal configuration; Positioning accuracy; Three-dimensional assessment model", "DOI": "10.32604/cmc.2020.010290", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Automation, Harbin Engineering University, Harbin, 150001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Chen", "Affiliation": "College of Automation, Harbin Engineering University, Harbin, 150001, China, Department of Electrical and Electronic Engineering, University of MelbourneVIC  3010, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Automation, Harbin Engineering University, Harbin, 150001, China"}, {"AuthorId": 4, "Name": "Lianhua Yu", "Affiliation": "College of Information and Communication, Harbin Engineering University, Harbin, 150001, China"}, {"AuthorId": 5, "Name": "Chengcai Lv", "Affiliation": "Institute of Deep-Sea Science and Engineering, Chinese Academy of Sciences, Sanya, 572000, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Linyi University, Linyi, 276005, China"}], "References": []}, {"ArticleId": 87894087, "Title": "An Immunization Scheme for Ransomware", "Abstract": "In recent years, as the popularity of anonymous currencies such as Bitcoin has made the tracking of ransomware attackers more difficult, the amount of ransomware attacks against personal computers and enterprise production servers is increasing rapidly. The ransomware has a wide range of influence and spreads all over the world. It is affecting many industries including internet, education, medical care, traditional industry, etc. This paper uses the idea of virus immunity to design an immunization solution for ransomware viruses to solve the problems of traditional ransomware defense methods (such as anti-virus software, firewalls, etc.), which cannot meet the requirements of rapid detection and immediate prevention of new outbreaks attacks. Our scheme includes two parts: server and client. The server provides an immune configuration file and configuration file management functions, including a configuration file module, a cryptography algorithm module, and a display module. The client obtains the immunization configuration file from server in real time, and performs the corresponding operations according to the configuration file to make the computer have an immune function for a specific ransomware, including an update module, a configuration file module, a cryptography algorithm module, a control module, and a log module. This scheme controls mutexes, services, files and registries respectively, to destroy the triggering conditions of the virus and finally achieve the purpose of immunizing a computer from a specific ransomware. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Malware; Malware immunization; Ransomware", "DOI": "10.32604/cmc.2020.010592", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Software College, Northeastern University, Shenyang, 110169, China"}, {"AuthorId": 2, "Name": "Qingyu <PERSON>", "Affiliation": "Software College, Northeastern University, Shenyang, 110169, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Wuhan University, Wuhan, 430072, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, University of Portsmouth, Portsmouth, PO1 2DT, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Software College, Northeastern University, Shenyang, 110169, China"}], "References": []}, {"ArticleId": 87894088, "Title": "Research on Detection Method of Interest Flooding Attack on Content Centric Network", "Abstract": "To improve the attack detection capability of content centric network (CCN), we propose a detection method of interest flooding attack (IFA) making use of the feature of self-similarity of traffic and the information entropy of content name of interest packet. On the one hand, taking advantage of the characteristics of self-similarity is very sensitive to traffic changes, calculating the Hurst index of the traffic, to identify initial IFA attacks. On the other hand, according to the randomness of user requests, calculating the information entropy of content name of the interest packets, to detect the severity of the IFA attack, is. Finally, based on the above two aspects, we use the bilateral detection method based on non-parametric CUSUM algorithm to judge the possible attack behavior in CCN. The experimental results show that flooding attack detection method proposed for CCN can not only detect the attack behavior at the early stage of attack in CCN, but also is more accurate and effective than other methods. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Bilateral detection method; CCN; Information entropy; Interest flooding attack; Self-similar feature", "DOI": "10.32604/cmc.2020.09849", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Internet Culture and Digital Dissemination Research, Beijing, 100101, China, Beijing Advanced Innovation Center for Materials Genome Engineering, Beijing Information Science and Technology University, Beijing, 100101, China, School of Computer, Beijing Information Science and Technology University, Beijing, 100101, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, Beijing Information Science and Technology University, Beijing, 100101, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Science, University of Arkansas at Little Rock, Little Rock, 72204, United States"}], "References": []}, {"ArticleId": 87894089, "Title": "Lattice-Based Searchable Encryption Scheme Against Inside Keywords Guessing Attack", "Abstract": "To save the local storage, users store the data on the cloud server who offers convenient internet services. To guarantee the data privacy, users encrypt the data before uploading them into the cloud server. Since encryption can reduce the data availability, public-key encryption with keyword search (PEKS) is developed to achieve the retrieval of the encrypted data without decrypting them. However, most PEKS schemes cannot resist quantum computing attack, because the corresponding hardness assumptions are some number theory problems that can be solved efficiently under quantum computers. Besides, the traditional PEKS schemes have an inherent security issue that they cannot resist inside keywords guessing attack (KGA). In this attack, a malicious server can guess the keywords encapsulated in the search token by computing the ciphertext of keywords exhaustively and performing the test between the token and the ciphertext of keywords. In the paper, we propose a lattice-based PEKS scheme that can resist quantum computing attacks. To resist inside KGA, this scheme adopts a lattice-based signature technique into the encryption of keywords to prevent the malicious server from forging a valid ciphertext. Finally, some simulation experiments are conducted to demonstrate the performance of the proposed scheme and some comparison results are further shown with respect to other searchable schemes. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Guessing attack; Post-quantum secure", "DOI": "10.32604/cmc.2020.09680", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Nanjing University of Science and Technology, Nanjing, 210094, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Nanjing University of Science and Technology, Nanjing, 210094, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Nanjing University of Science and Technology, Nanjing, 210094, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science, Security and Networks, Japan Advanced Institute of Science and Technology, Ishikawa, 9231292, Japan"}], "References": []}, {"ArticleId": 87894090, "Title": "Research on the Pedestrian Re-identification Method Based on Local Features and Gait Energy Images", "Abstract": "The appearance of pedestrians can vary greatly from image to image, and different pedestrians may look similar in a given image. Such similarities and variabilities in the appearance and clothing of individuals make the task of pedestrian re-identification very challenging. Here, a pedestrian re-identification method based on the fusion of local features and gait energy image (GEI) features is proposed. In this method, the human body is divided into four regions according to joint points. The color and texture of each region of the human body are extracted as local features, and GEI features of the pedestrian gait are also obtained. These features are then fused with the local and GEI features of the person. Independent distance measure learning using the cross-view quadratic discriminant analysis (XQDA) method is used to obtain the similarity of the metric function of the image pairs, and the final similarity is acquired by weight matching. Evaluation of experimental results by cumulative matching characteristic (CMC) curves reveals that, after fusion of local and GEI features, the pedestrian re-identification effect is improved compared with existing methods and is notably better than the recognition rate of pedestrian re-identification with a single feature. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Cross-view quadratic discriminant analysis; Gait energy image; Independent distance metric; Local features; Weight", "DOI": "10.32604/cmc.2020.010283", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hebei University of Science and Technology, Shijiazhuang, 050000, China"}, {"AuthorId": 2, "Name": "Xing Sun", "Affiliation": "Hebei University of Science and Technology, Shijiazhuang, 050000, China"}, {"AuthorId": 3, "Name": "Zhenzhou Wang", "Affiliation": "Hebei University of Science and Technology, Shijiazhuang, 050000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei University of Science and Technology, Shijiazhuang, 050000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Internet of Things and Software Technology, Wuxi Vocational College of Science and Technology, Wuxi, 214028, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Xu", "Affiliation": "Rutgers Business School-Newark, Washington Park, Newark, NJ  07102, United States"}], "References": []}, {"ArticleId": 87894091, "Title": "A Multi-tenant Usage Access Model for Cloud Computing", "Abstract": "Most cloud services are built with multi-tenancy which enables data and configuration segregation upon shared infrastructures. It offers tremendous advantages for enterprises and service providers. It is anticipated that this situation will evolve to foster cross-tenant collaboration supported by Authorization as a service. To realize access control in a multi-tenant cloud computing environment, this study proposes a multi-tenant cloud computing access control model based on the traditional usage access control model by building trust relations among tenants. The model consists of three sub-models, which achieve trust relationships between tenants with different granularities and satisfy the requirements of different application scenarios. With an established trust relation in MT-UCON (Multi-tenant Usage Access Control), the trustee can precisely authorize cross-tenant accesses to the trustor's resources consistent with constraints over the trust relation and other components designated by the trustor. In addition, the security of the model is analyzed by an information flow method. The model adapts to the characteristics of a dynamic and open multi-tenant cloud computing environment and achieves fine-grained access control within and between tenants. © 2020 Tech Science Press. All rights reserved.", "Keywords": "Cloud computing; Multi-tenant; Usage access control model", "DOI": "10.32604/cmc.2020.010846", "PubYear": 2020, "Volume": "64", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sanjiang University, Nanjing, 210012, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Sanjiang University, Nanjing, 210012, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Sanjiang University, Nanjing, 210012, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "International Business Machines Corporation (IBM), New York, United States"}], "References": []}, {"ArticleId": 87894606, "Title": "Local Face Antimagic Evaluations and Coloring of Plane Graphs", "Abstract": "We investigate a local face antimagic labeling of plane graphs, and we introduce a new graph characteristic, namely local face antimagic chromatic number of type (a ; b ; c ). Then we determine the precise value of this parameter for wheels and ladders.", "Keywords": "plane graph; local face antimagic labeling; local face antimagic chromatic number of type ( a ; b ; c )", "DOI": "10.3233/FI-2020-1934", "PubYear": 2020, "Volume": "174", "Issue": "2", "JournalId": 31657, "JournalTitle": "Fundamenta Informaticae", "ISSN": "0169-2968", "EISSN": "1875-8681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematical Sciences, University of Delaware, Newark, DE 19716, USA. "}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Applied Mathematics and Informatics, Technical University, Košice, Slovak Republic. , andrea.f<PERSON><PERSON><PERSON><PERSON>@tuke.sk"}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics and Informatics, Technical University, Košice, Slovak Republic. , andrea.f<PERSON><PERSON><PERSON><PERSON>@tuke.sk"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Indonesia, Kampus UI Depok, Depok 16424, Indonesia. "}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Tunghai University, Taichung, Taiwan, ROC. "}], "References": []}, {"ArticleId": 87894607, "Title": "Combinatorial Algorithms for Binary Operations on LR-tableaux with Entries Equal to 1 with Applications to Nilpotent Linear Operators", "Abstract": "In the paper we investigate an algorithmic associative binary operation * on the set ℒℛ 1 of Littlewood-Richardson tableaux with entries equal to one. We extend * to an algorithmic nonassociative binary operation on the set ℒℛ 1 × ℕ and show that it is equivalent to the operation of taking the generic extensions of objects in the category of homomorphisms from semisimple nilpotent linear operators to nilpotent linear operators. Thus we get a combinatorial algorithm computing generic extensions in this category.", "Keywords": "Littlewood-Richardson tableaux; partitions; invariant subspaces; nilpotent linear operators; generic extensions; pickets; degeneration partial order; combinatorial algorithms", "DOI": "10.3233/FI-2020-1935", "PubYear": 2020, "Volume": "174", "Issue": "2", "JournalId": 31657, "JournalTitle": "Fundamenta Informaticae", "ISSN": "0169-2968", "EISSN": "1875-8681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mathematics and Computer Science, Nicolaus Copernicus University, Chopina 12/18, 87-100 Toruń, Poland. , <EMAIL>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mathematics and Computer Science, Nicolaus Copernicus University, Chopina 12/18, 87-100 Toruń, Poland. , <EMAIL>"}], "References": []}, {"ArticleId": 87894608, "Title": "BCK-codes Based on a Parity Check Matrix", "Abstract": "Hamming codes are of primary concern in information theory and its applications. Despite a number of researches that have been conducted on such codes and their characterizations, dealing with the properties of previously introduced codes in a BCK-algebraic framework has not been considered in earlier works. This paper investigates a code constructed based on BCK-algebraic models and proposes an algorithm corresponding to the presented code. It is noticeable that the suggested rendered algorithm is also established on the basis of the elements of a BCK-algebra. In fact, both the Hamming distance and dimension, associated with the presented code, can be estimated through a BCK-algebra structure due to the mechanism of its algorithm which is heavily dependent on the parity check matrix. In addition, the way in which the codes are designed contributes substantially to classification of them and to extract greater number of their attributes compared to the previous works. The highlight of the proposed method is that the number of atoms of the BCK-algebra plays a key role in calculation of the Hamming distance and dimension of these codes. Moreover, the obtained codes possess specified and recognizable Hamming distance which are essential in performing error-correcting, error-detecting and decoding tasks.", "Keywords": "BCI/BCK-algebra; Binary code; linear code; parity check matrix; error-detecting; Hamming distance; incidence structure; BCK-incidence vector; Algorithm; Orthogonal code; Atom; Generator matrix", "DOI": "10.3233/FI-2020-1936", "PubYear": 2020, "Volume": "174", "Issue": "2", "JournalId": 31657, "JournalTitle": "Fundamenta Informaticae", "ISSN": "0169-2968", "EISSN": "1875-8681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Science and Research Branch, Islamic Azad University (IAU), Tehran, Iran. "}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pure Mathematics, Faculty of Mathematics and Computer, Shahid <PERSON>ar University of Kerman, Kerman, Iran. "}, {"AuthorId": 3, "Name": "Abolfazl Tehranian", "Affiliation": "Department of Mathematics, Science and Research Branch, Islamic Azad University (IAU), Tehran, Iran. "}], "References": []}, {"ArticleId": 87894609, "Title": "Minimal Base for Finite Topological Space by Matrix Method", "Abstract": "Topological base plays a foundational role in topology theory. However, few works have been done to find the minimal base, which would make us difficult to interpret the internal structure of topological spaces. To address this issue, we provide a method to convert the finite topological space into Boolean matrix and some properties of minimal base are investigated. According to the properties, an algorithm(URMB) is proposed. Subsequently, the relationship between topological space and its sub-space with respect to the base is concentrated on by Boolean matrix. Then, a fast algorithm(MMB) is presented, which can avoid a mass of redundant computations. Finally, some numerical experiments are given to show the advantage and the effectiveness of MMB compared with URMB.", "Keywords": "Algorithm; Boolean matrix; Finite topology; Minimal base", "DOI": "10.3233/FI-2020-1937", "PubYear": 2020, "Volume": "174", "Issue": "2", "JournalId": 31657, "JournalTitle": "Fundamenta Informaticae", "ISSN": "0169-2968", "EISSN": "1875-8681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Xiamen University, Xiamen, Fujian 361000, China"}, {"AuthorId": 2, "Name": "Jinjin Li", "Affiliation": "Department of Mathematics and Statistics, Minnan Normal University, Zhangzhou, 363000, Fujian, China. "}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Applied Science, Beijing University of Technology, Beijing 100124, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, Auburn University, Auburn, AL 36849, USA"}], "References": []}, {"ArticleId": 87894610, "Title": "A Propositional Metric Logic with Fixed Finite Ranges", "Abstract": "The aim of this article is developing a formal system suitable for reasoning about the distance between propositional formulas. We introduce and study a formal language which is the extension of the classical propositional language obtained by adding new binary operators D ≤s and D ≥s , s ∈ Range, where Range is a fixed finite set. In our language it is allowed to make formulas of the form D ≤s ( α; β) with the intended meaning ’distance between formulas α and β is less than or equal to s’. The semantics of the proposed language consists of possible worlds with a distance function defined between sets of worlds.", "Keywords": "metric operators; soundness; completeness; compactness", "DOI": "10.3233/FI-2020-1938", "PubYear": 2020, "Volume": "174", "Issue": "2", "JournalId": 31657, "JournalTitle": "Fundamenta Informaticae", "ISSN": "0169-2968", "EISSN": "1875-8681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science, University of Kragujevac, 34000 Kragujevac, Serbia. "}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mathematics, University of Belgrade, 11000 Belgrade, Serbia. "}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science, University of Kragujevac, 34000 Kragujevac, Serbia. "}], "References": []}, {"ArticleId": 87894704, "Title": "Implementation of escape room system based on augmented reality involving deep convolutional neural network", "Abstract": "<p>Escape room is a live-action adventure game, where the players search clues, solve puzzles and achieve the assigned tasks. This paper proposed a novel escape room system combining augmented reality and deep learning technology. The system adopts a client–server architecture and can be divided into the server module, the smart glasses module and the player–hardware interaction module. The player–hardware interaction module consists of subsystems each of which includes a Raspberry Pi 3. <PERSON><PERSON><PERSON><PERSON> is used as the smart glasses in the experiment of the paper. The server communicates with all the Raspberry Pis and HoloLens through TCP/IP protocol and manages all the devices to achieve the game flow by following the process timeline. The smart glasses module provides two display modes, i.e., the AR 3D models display and the 2D text clues display. In the first mode, the SDK Vuforia is used for detection and tracking of markers. In the second mode, the scene images captured by HoloLens camera are sent to the pre-trained image classifier based on deep convolutional neural network. Considering both the image category and the game status value, the server decides the text clue image to be displayed on HoloLens. The accuracy of the image classification model reaches 94.9%, which can be correctly classified for a certain rotation angle and partial occlusion. The integration of AR, deep learning, electronics and escape room games opens up exciting new directions for the development of escape room. Finally, a built mini-escape room is analyzed to prove that the proposed system can support more complicated narratives showing the potential of achieving immersion.</p>", "Keywords": "Augmented reality; Convolutional neural network; Image classification; Escape room game", "DOI": "10.1007/s10055-020-00476-0", "PubYear": 2021, "Volume": "25", "Issue": "3", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechatronics, College of Mechanical Engineering, Chongqing University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechatronics, College of Mechanical Engineering, Chongqing University, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechatronics, College of Mechanical Engineering, Chongqing University, Chongqing, China"}], "References": []}, {"ArticleId": 87894967, "Title": "An Empirical And Comparatively Research On Under-Sampling & Over- Sampling Defect-Prone Data-Sets Model In Light Of Machine Learning", "Abstract": "", "Keywords": "", "DOI": "10.35444/IJANA.2021.12508", "PubYear": 2021, "Volume": "12", "Issue": "5", "JournalId": 65085, "JournalTitle": "International Journal of Advanced Networking and Applications", "ISSN": "0975-0290", "EISSN": "0975-0282", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87895149, "Title": "Advantages of Print Reading over Screen Reading: A Comparison of Visual Patterns, Reading Performance, and Reading Attitudes across Paper, Computers, and Tablets", "Abstract": "We examined the effects of the reading medium (print vs. digital) on readers’ visual patterns, reading performance, and reading attitudes. Two within-subject experiments were conducted with 74 readers, who read articles using three reading media: print, computer, and tablet. The experimental results showed that in terms of visual patterns, readers exhibited a shorter fixation duration and a higher fixation count during print reading than during screen reading; reading performance, as measured on the basis of reading comprehension and reading time, was equivalent across all three media; however, in terms of reading attitude, readers reported higher levels of perceived understanding, perceived confidence, and perceived immersion and lower levels of perceived fatigue for reading printed text than reading from a device screen. Therefore, the performance gap between print and screen reading is narrowing. However, printed text may still be the preferred mode of reading, as demonstrated by the readers’ preferences. Disclosure of potential conflict of interest No potential conflict of interest was reported by the author(s). Additional information Funding This work was supported by the National Research Foundation of Korea (NRF) under Grant number 2017R1D1A1B03034511. Notes on contributors <PERSON> received the MS degree in engineering from the Graduate School of Convergence Science and Technology, Seoul National University. Her research interests include mass media communication, human-computer interaction, technology adoption, and device usage. <PERSON><PERSON><PERSON> is an associate professor at Seoul National University. She received the BA degree in computer science and economics from the University of California, Berkeley. She received the MS and Ph.D. degrees in human-computer interaction from Carnegie Mellon University. Her research interests include human-computer interaction, learning science, and natural language processing.", "Keywords": "", "DOI": "10.1080/10447318.2021.1908668", "PubYear": 2021, "Volume": "37", "Issue": "17", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Graduate School of Convergence Science and Technology, Seoul National University, Seoul, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Convergence Science and Technology, Seoul National University, Seoul, South Korea"}], "References": [{"Title": "Assessing children's reading comprehension on paper and screen: A mode-effect study", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "103861", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 87895528, "Title": "Face Recognition Based on Lightweight Convolutional Neural Networks", "Abstract": "<p>Face recognition algorithms based on deep learning methods have become increasingly popular. Most of these are based on highly precise but complex convolutional neural networks (CNNs), which require significant computing resources and storage, and are difficult to deploy on mobile devices or embedded terminals. In this paper, we propose several methods to improve the algorithms for face recognition based on a lightweight CNN, which is further optimized in terms of the network architecture and training pattern on the basis of MobileFaceNet. Regarding the network architecture, we introduce the Squeeze-and-Excitation (SE) block and propose three improved structures via a channel attention mechanism—the depthwise SE module, the depthwise separable SE module, and the linear SE module—which are able to learn the correlation of information between channels and assign them different weights. In addition, a novel training method for the face recognition task combined with an additive angular margin loss function is proposed that performs the compression and knowledge transfer of the deep network for face recognition. Finally, we obtained high-precision and lightweight face recognition models with fewer parameters and calculations that are more suitable for applications. Through extensive experiments and analysis, we demonstrate the effectiveness of the proposed methods.</p>", "Keywords": "face recognition; convolutional neural network; lightweight neural network; attention mechanism; knowledge distillation face recognition ; convolutional neural network ; lightweight neural network ; attention mechanism ; knowledge distillation", "DOI": "10.3390/info12050191", "PubYear": 2021, "Volume": "12", "Issue": "5", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing 100029, China↑University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing 100029, China↑Authors to whom correspondence should be addressed"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing 100029, China↑Authors to whom correspondence should be addressed"}], "References": []}, {"ArticleId": 87895566, "Title": "A Reliable Authentication Protocol For Peer To Peer Based Applications", "Abstract": "", "Keywords": "", "DOI": "10.35444/IJANA.2021.12507", "PubYear": 2021, "Volume": "12", "Issue": "5", "JournalId": 65085, "JournalTitle": "International Journal of Advanced Networking and Applications", "ISSN": "0975-0290", "EISSN": "0975-0282", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87895664, "Title": "Regular Expressions with Lookahead", "Abstract": "<p> This paper investigates regular expressions which in addition to the standard operators of union, concatenation, and Kleene star, have lookaheads . We show how to translate regular expressions with lookaheads ( REwLA ) to equivalent Boolean automata having at most 3 states more than the length of the REwLA . We also investigate the state complexity when translating REwLA to equivalent deterministic finite automata (DFA). </p>", "Keywords": "Boolean automata; Lookahead expressions; Regular expressions", "DOI": "10.3897/jucs.66330", "PubYear": 2021, "Volume": "27", "Issue": "4", "JournalId": 29193, "JournalTitle": "JUCS - Journal of Universal Computer Science", "ISSN": "0948-695X", "EISSN": "0948-6968", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Science, Stellenbosch University, South Africa"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Division, Stellenbosch University, South Africa"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Division, Stellenbosch University, South Africa"}], "References": []}, {"ArticleId": 87895723, "Title": "Image Compression Approach using Segmentation and Total Variation Regularization", "Abstract": "<p>In this paper we present a hybrid model for image compression based on segmentation and total variation regularization. The main motivation behind our approach is to offer decode image with immediate access to objects/features of interest. We are targeting high quality decoded image in order to be useful on smart devices, for analysis purpose, as well as for multimedia content-based description standards. The image is approximated as a set of uniform regions: The technique will assign well-defined members to homogenous regions in order to achieve image segmentation. The Adaptive fuzzy c-means (AFcM) is a guide to cluster image data. A second stage coding is applied using entropy coding to remove the whole image entropy redundancy. In the decompression phase, the reverse process is applied in which the decoded image suffers from missing details due to the coarse segmentation. For this reason, we suggest the application of total variation (TV) regularization, such as the Rudin-Osher-Fatemi (ROF) model, to enhance the quality of the coded image. Our experimental results had shown that ROF may increase the PSNR and hence offer better quality for a set of benchmark grayscale images.</p>", "Keywords": "", "DOI": "10.46300/9108.2021.15.6", "PubYear": 2021, "Volume": "15", "Issue": "", "JournalId": 75453, "JournalTitle": "International Journal of Computers", "ISSN": "", "EISSN": "1998-4308", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Doctoral School for Science and Technologies Lebanese University Tripoli, Lebanon"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Doctoral School for Science and Technologies Lebanese University Tripoli, Lebanon"}, {"AuthorId": 3, "Name": "Fadi <PERSON>", "Affiliation": "Doctoral School for Science and Technologies Lebanese University Tripoli, Lebanon"}], "References": []}, {"ArticleId": 87895981, "Title": "An enhanced technique of skin cancer classification using deep convolutional neural network with transfer learning models", "Abstract": "Skin cancer is one of the top three perilous types of cancer caused by damaged DNA that can cause death. This damaged DNA begins cells to grow uncontrollably and nowadays it is getting increased speedily. There exist some researches for the computerized analysis of malignancy in skin lesion images. However, analysis of these images is very challenging having some troublesome factors like light reflections from the skin surface, variations in color illumination, different shapes, and sizes of the lesions. As a result, evidential automatic recognition of skin cancer is valuable to build up the accuracy and proficiency of pathologists in the early stages. In this paper, we propose a deep convolutional neural network (DCNN) model based on deep learning approach for the accurate classification between benign and malignant skin lesions. In preprocessing we firstly, apply filter or kernel to remove noise and artifacts; secondly, normalize the input images and extract features that help for accurate classification; and finally, data augmentation increases the number of images that improves the accuracy of classification rate. To evaluate the performance of our proposed, DCNN model is compared with some transfer learning models such as AlexNet, ResNet, VGG-16, DenseNet, MobileNet, etc. The model is evaluated on the HAM10000 dataset and ultimately we obtained the highest 93.16% of training and 91.93% of testing accuracy respectively. The final outcomes of our proposed DCNN model define it as more reliable and robust when compared with existing transfer learning models.", "Keywords": "Skin cancer ; Pre-processing ; Convolutional neural network ; Classification ; Transfer learning", "DOI": "10.1016/j.mlwa.2021.100036", "PubYear": 2021, "Volume": "5", "Issue": "", "JournalId": 78703, "JournalTitle": "Machine Learning with Applications", "ISSN": "2666-8270", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Biomedical Engineering, Islamic University, Kushtia 7003, Bangladesh;https://www.iu.ac.bd"}, {"AuthorId": 2, "Name": "Md <PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Technology, Islamic University, Kushtia 7003, Bangladesh;School of Computer Science, National University of Ireland Galway, H91 TK33 Galway, Republic of Ireland;http://www.nuigalway.ie"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Islamic University, Kushtia 7003, Bangladesh;https://www.iu.ac.bd"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Technology, Islamic University, Kushtia 7003, Bangladesh;https://www.iu.ac.bd"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Islamic University, Kushtia 7003, Bangladesh;Corresponding author;https://www.iu.ac.bd"}], "References": [{"Title": "Vision based Detection and Categorization of Skin lesions using Deep Learning Neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "1726", "JournalTitle": "Procedia Computer Science"}, {"Title": "A Transfer Learning approach for AI-based classification of brain tumors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "", "Page": "100003", "JournalTitle": "Machine Learning with Applications"}, {"Title": "Smart healthcare disease diagnosis and patient management: Innovation, improvement and skill development", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "", "Page": "100011", "JournalTitle": "Machine Learning with Applications"}]}, {"ArticleId": 87896001, "Title": "A Tool Development Framework to Support Design Thinking for Software Engineering", "Abstract": "<p> This research aims to create a framework to guide the development of design thinking support tools - that is, tools that enable people to express themselves creatively and develop as creative thinkers. The main goal is to develop advanced software and social networking sites that empower users to not only be productive, but also have new technologies. Potential users of these interfaces include software with other engineers, various scientists, product and image designers, builders, teachers, students and many more. Improved communication methods can enable effective psychological search, improved interaction between groups, and faster recovery processes. These advanced combinations should also provide strong support for hypothesis formation, rapid testing of alternatives, improved visual perception, and better distribution of results.</p>", "Keywords": "Tool development;software engineering;design thinking", "DOI": "10.24297/ijct.v21i.8998", "PubYear": 2021, "Volume": "21", "Issue": "", "JournalId": 55824, "JournalTitle": "INTERNATIONAL JOURNAL OF COMPUTERS & TECHNOLOGY", "ISSN": "", "EISSN": "2277-3061", "Authors": [{"AuthorId": 1, "Name": "Ayse Ko<PERSON> Arslan", "Affiliation": ""}], "References": []}, {"ArticleId": 87896229, "Title": "Computationally efficient and secure anonymous authentication scheme for cloud users", "Abstract": "<p>In cloud computing, the combinations of various computing units are globally separated, however, electronically connected. In recent years, the number of cloud users has adequately increased in the modern societies. In order to restrict the illegal access of the intruders, a strong user authentication is required for cloud computing environment. In this regard, in this paper, we came up with a computationally productive and safer anonymous authentication pattern for cloud users. Moreover, in our scheme, mutual authentication is performed in an anonymous way between the cloud user and the cloud server to strongly verify the legitimacy of each user before providing an access into the cloud. After the successful anonymous mutual authentication, the targeted cloud service provider and the cloud users make mutual communication between them. In case of any misbehaviouring after successful mutual authentication, an efficient revocation mechanism is proposed through which the trusted third party (TTP) can revoke the cloud users or service providers from the cloud environment. Even though there were many anonymous cloud computing authentication schemes, the previously proposed authentication schemes suffered from high computational cost during certificate verification. Moreover, the section related to analysis proves that the explained scheme is robust enough in terms of security strength to avoid various security attacks and breeches.</p>", "Keywords": "Authentication; Computation complexity; Privacy; Conditional tracking; Data integrity", "DOI": "10.1007/s00779-021-01566-9", "PubYear": 2024, "Volume": "28", "Issue": "1", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, E.G.S.Pillay Engineering College, Nagapattinam, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, University College of Engineering Ariyalur, Ariyalur, India"}, {"AuthorId": 3, "Name": "Fadi <PERSON>", "Affiliation": "Artificial Intelligence Engineering Dept., Research Center for AI and IoT, Near East University, Mersin, Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, GMR Institute of Technology, Rajam, India; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "SNS College of Technology, Coimbatore-35, India"}], "References": [{"Title": "Seamless secure anonymous authentication for cloud-based mobile edge computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "106782", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A smart lightweight privacy preservation scheme for IoT-based UAV communication systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Fadi Al-<PERSON>j<PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "102", "JournalTitle": "Computer Communications"}, {"Title": "A lightweight authentication and secure data access between fog and IoT user", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "1", "Page": "77", "JournalTitle": "International Journal of Electronic Business"}]}, {"ArticleId": 87896250, "Title": "Automatically evaluating the quality of textual descriptions in cultural heritage records", "Abstract": "Metadata are fundamental for the indexing, browsing and retrieval of cultural heritage resources in repositories, digital libraries and catalogues. In order to be effectively exploited, metadata information has to meet some quality standards, typically defined in the collection usage guidelines. As manually checking the quality of metadata in a repository may not be affordable, especially in large collections, in this paper we specifically address the problem of automatically assessing the quality of metadata, focusing in particular on textual descriptions of cultural heritage items. We describe a novel approach based on machine learning that tackles this problem by framing it as a binary text classification task aimed at evaluating the accuracy of textual descriptions. We report our assessment of different classifiers using a new dataset that we developed, containing more than 100K descriptions. The dataset was extracted from different collections and domains from the Italian digital library “Cultura Italia” and was annotated with accuracy information in terms of compliance with the cataloguing guidelines. The results empirically confirm that our proposed approach can effectively support curators (F1 $$\\sim $$ \n ∼ \n  0.85) in assessing the quality of the textual descriptions of the records in their collections and provide some insights into how training data, specifically their size and domain, can affect classification performance.", "Keywords": "Metadata quality; Digital libraries; Cultural heritage; Natural language processing; Machine learning", "DOI": "10.1007/s00799-021-00302-1", "PubYear": 2021, "Volume": "22", "Issue": "2", "JournalId": 2448, "JournalTitle": "International Journal on Digital Libraries", "ISSN": "1432-5012", "EISSN": "1432-1300", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Fondazione Bruno <PERSON>, Trento, Italy;University of Trento, Trento, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Università degli studi di Verona, Verona, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Trento, Trento, Italy"}], "References": [{"Title": "An analysis and comparison of keyword recommendation methods for scientific data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "3", "Page": "307", "JournalTitle": "International Journal on Digital Libraries"}]}, {"ArticleId": 87896252, "Title": "Cooperative verifier-based testing with CoVeriTest", "Abstract": "Testing is a widely applied technique to evaluate software quality, and coverage criteria are often used to assess the adequacy of a generated test suite. However, manually constructing an adequate test suite is typically too expensive, and numerous techniques for automatic test-suite generation were proposed. All of them come with different strengths. To build stronger test-generation tools, different techniques should be combined. In this paper, we study cooperative combinations of verification approaches for test generation, which exchange high-level information. We present CoVeriTest , a hybrid technique for test-suite generation. CoVeriTest iteratively applies different conditional model checkers and allows users to adjust the level of cooperation and to configure individual time limits for each conditional model checker. In our experiments, we systematically study different CoVeriTest cooperation setups, which either use combinations of explicit-state model checking and predicate abstraction, or bounded model checking and symbolic execution. A comparison with state-of-the-art test-generation tools reveals that CoVeriTest achieves higher coverage for many programs (about 15%).", "Keywords": "Test-case generation; Test coverage; Software testing; Conditional model checking; Cooperative verification; Model checking", "DOI": "10.1007/s10009-020-00587-8", "PubYear": 2021, "Volume": "23", "Issue": "3", "JournalId": 15615, "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)", "ISSN": "1433-2779", "EISSN": "1433-2787", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "LMU Munich, Munich, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Technical University of Darmstadt, Darmstadt, Germany"}], "References": [{"Title": "CoVeriTest: interleaving value and predicate analysis for test-case generation", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "6", "Page": "847", "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)"}]}, {"ArticleId": 87896270, "Title": "Knowledge-driven description synthesis for floor plan interpretation", "Abstract": "<p>Image captioning is a widely known problem in the area of AI. Caption generation from floor plan images has applications in indoor path planning, real estate, and providing architectural solutions. Several methods have been explored in the literature for generating captions or semi-structured descriptions from floor plan images. Since only the caption is insufficient to capture fine-grained details, researchers also proposed descriptive paragraphs from images. However, these descriptions have a rigid structure and lack flexibility, making it difficult to use them in real-time scenarios. This paper offers two models, description synthesis from image cue (DSIC) and transformer-based description generation (TBDG), for text generation from floor plan images. These two models take advantage of modern deep neural networks for visual feature extraction and text generation. The difference between both models is in the way they take input from the floor plan image. The DSIC model takes only visual features automatically extracted by a deep neural network, while the TBDG model learns textual captions extracted from input floor plan images with paragraphs. The specific keywords generated in TBDG and understanding them with paragraphs make it more robust in a general floor plan image. Experiments were carried out on a large-scale publicly available dataset and compared with state-of-the-art techniques to show the proposed model’s superiority.</p>", "Keywords": "Floor plan; Captioning; Evaluation; Language modeling", "DOI": "10.1007/s10032-021-00367-3", "PubYear": 2021, "Volume": "24", "Issue": "1-2", "JournalId": 13682, "JournalTitle": "International Journal on Document Analysis and Recognition", "ISSN": "1433-2833", "EISSN": "1433-2825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Jodhpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Jodhpur, India"}, {"AuthorId": 3, "Name": "Gaurav Bhatnagar", "Affiliation": "Indian Institute of Technology, Jodhpur, India"}], "References": [{"Title": "A comparative study of graphic symbol recognition methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "13-14", "Page": "8695", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Scalable logo detection by self co-learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "107003", "JournalTitle": "Pattern Recognition"}, {"Title": "Creating Accessible Online Floor Plans for Visually Impaired Readers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Accessible Computing"}]}, {"ArticleId": 87896276, "Title": "Real-time one-shot learning gesture recognition based on lightweight 3D Inception-ResNet with separable convolutions", "Abstract": "<p>Gesture recognition is a popular research field in computer vision and the application of deep neural networks greatly improves its performance. However, the general deep learning method has a large number of parameters preventing the practical application on resource-limited devices. Meanwhile, collecting large number of training samples is usually time-consuming and difficult. To this end, we propose a lightweight 3D Inception-ResNet to extract discriminative features for real-time one-shot learning gesture recognition which aims to recognize gestures successfully given only one training sample for each new class. For efficient extraction of gesture features, we firstly extend the original 2D Inception-ResNet to the 3D version and then apply two kinds of separable convolutions as well as some other design strategies to reduce the number of parameters and computation complexity making it running in real-time even on CPU for feature extraction. Moreover, the consumption of storage space is also greatly reduced. In order to obtain robust performance for one-shot learning recognition, we employ an evolution mechanism by updating the root sample with innovation of new samples to enhance and improve the performance of the nearest neighbor classifier. Meanwhile, we propose an update strategy of the dynamic threshold to deal with the problem of threshold selection in real-world applications. In order to improve the robustness of recognition performance, we conduct artificial data synthesis to augment our collected dataset. A series of experiments conducted on public datasets and our collected dataset demonstrate the effectiveness of our approach to one-shot learning gesture recognition.</p>", "Keywords": "Gesture recognition; One-shot learning; Inception-ResNet; Separable convolutions; Real-time processing", "DOI": "10.1007/s10044-021-00965-1", "PubYear": 2021, "Volume": "24", "Issue": "3", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "Lianwei Li", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing, China;School of Electrical Engineering and Intelligentization, Dongguan University of Technology, Dongguan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence Research Department, Sony China Research Laboratory, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence Research Department, Sony China Research Laboratory, Beijing, China"}], "References": [{"Title": "One-shot learning gesture recognition based on joint training of 3D ResNet and memory module", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "9-10", "Page": "6727", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Robust hand gesture recognition system based on a new set of quaternion Tchebichef moment invariants", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "3", "Page": "1337", "JournalTitle": "Pattern Analysis and Applications"}]}, {"ArticleId": 87896281, "Title": "The ObReco-360°: a new ecological tool to memory assessment using 360° immersive technology", "Abstract": "One important feature of a neuropsychological test is its ecological validity, which defines how much patients’ test scores are linked to real-life functioning. However, many of the currently available neuropsychological tools show low to moderate levels of ecological validity. Virtual reality (VR) emerged as a possible solution that might enhance the ecological value of standard paper-and-pencil tests, thanks to the possibility of simulating realistic environments and situations where patients can behave as they do in real life. Moreover, a recent kind of virtual environments, the 360° spherical photos and videos, seems to guarantee high levels of graphical realism and lower technical complexity than standard VR, despite their limitations concerning interactive design. In this pilot study, we tested the possible application of 360° technology for the assessment of memory, developing an adaptation of a standardized test. We focused on Free Recall and Recognition accuracies as indexes of memory function, confronting and correlating the performances obtained by the participants in the standard and in the 360° test. The results, even if preliminary, support the use of 360° technology for enhancing the ecological value of standard memory assessment tests.", "Keywords": "Memory; Assessment; 360° video; Object recognition; Virtual reality", "DOI": "10.1007/s10055-021-00526-1", "PubYear": 2022, "Volume": "26", "Issue": "2", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Psychology, University of Milano-Bicocca, Milan, Italy; Mind and Behavior Technological Center, University of Milano-Bicocca, Milan, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychology, Catholic University of the Sacred Heart, Milan, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychology, University of Turin, Turin, Italy"}, {"AuthorId": 4, "Name": "Valentina Mancuso", "Affiliation": "Department of Psychology, eCampus University, Novedrate, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Psychology, Catholic University of the Sacred Heart, Milan, Italy; Applied Technology for Neuro-Psychology Lab, IRCCS Istituto Auxologico Italiano, Milan, Italy"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychology, eCampus University, Novedrate, Italy; Applied Technology for Neuro-Psychology Lab, IRCCS Istituto Auxologico Italiano, Milan, Italy"}], "References": [{"Title": "Development and validation of a simulation workload measure: the simulation task load index (SIM-TLX)", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "4", "Page": "557", "JournalTitle": "Virtual Reality"}]}, {"ArticleId": 87896293, "Title": "Stream runtime verification of real-time event streams with the Striver language", "Abstract": "<p>In this paper, we study the problem of runtime verification of real-time event streams; in particular, we propose a language to describe monitors for real-time event streams that can manipulate data from rich domains. We propose a solution based on stream runtime verification (SRV), where monitors are specified by describing how output streams of data are computed from input streams of data. SRV enables a clean separation between the temporal dependencies among incoming events and the concrete operations that are performed during the monitoring. Most SRV specification languages assume that all streams share a global synchronous clock and divide time in discrete instants. At each instant every input has a reading, and for every instant the monitor computes an output. In this paper, we generalize the time assumption to cover real-time event streams, but keep the explicit time offsets present in some synchronous SRV languages like Lola. The language we introduce, called Striver , shares with SRV the simplicity and economy of operators, and the separation between the reasoning about time and the computation of data values. The version of Striver in this paper allows expressing future and past dependencies. Striver is a general language that allows expressing for certain time domains other real-time monitoring languages, like TeSSLa, and temporal logics, like STL. We show in this paper translations from other formalisms for (piecewise-constant) real-time signals and timed event streams. Finally, we report an empirical evaluation of an implementation of Striver .</p>", "Keywords": "Runtime verification; Stream runtime verification; Formal verification; Formal methods; Specification languages", "DOI": "10.1007/s10009-021-00605-3", "PubYear": 2021, "Volume": "23", "Issue": "2", "JournalId": 15615, "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)", "ISSN": "1433-2779", "EISSN": "1433-2787", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IMDEA Software Institute, Madrid, Spain;Universidad Politécnica de Madrid, Madrid, Spain;CIFASIS, Rosario, Argentina"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IMDEA Software Institute, Madrid, Spain"}], "References": []}, {"ArticleId": 87896309, "Title": "A priori error analysis for a mixed VEM discretization of the spectral problem for the Laplacian operator", "Abstract": "<p>The aim of the present work is to derive error estimates for the Laplace eigenvalue problem in mixed form, implementing a virtual element method. With the aid of the theory for non-compact operators, we prove that the proposed method is spurious free and convergent. Optimal order of convergence for the eigenvalues and eigenfunctions are derived. Finally, we report numerical tests to confirm the theoretical results together with a rigorous computational analysis of the effects of the stabilization parameter, inherent for the virtual element methods, in the computation of the spectrum.</p>", "Keywords": "Mixed virtual element method; Laplace eigenvalue problem; Error estimates; 35P15; 35Q35; 65N15; 65N30; 76B15", "DOI": "10.1007/s10092-021-00412-x", "PubYear": 2021, "Volume": "58", "Issue": "2", "JournalId": 12677, "JournalTitle": "<PERSON><PERSON><PERSON>", "ISSN": "0008-0624", "EISSN": "1126-5434", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "GIMNAP-Departamento de Matemática, Universidad del Bío-Bío, Concepción, Chile"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departamento de Ciencias Exactas, Universidad de Los Lagos, Osorno, Chile"}], "References": [{"Title": "The p - and h p -versions of the virtual element method for elliptic eigenvalue problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "7", "Page": "2035", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "A Virtual Element Method for the Steklov Eigenvalue Problem Allowing Small Edges", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "88", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}]}, {"ArticleId": 87896471, "Title": "Robust fault‐tolerant consensus control for nonlinear multi‐agent systems with prescribed transient and steady‐state performance", "Abstract": "<p>This paper investigates the finite-time consensus tracking problem for second-order nonlinear multi-agent systems subject to the actuator bias fault. Based on the sliding mode control and prescribed performance control, two distributed fault-tolerant consensus protocols are proposed for the sudden bias fault and the slowly varying bias fault of the actuator. The first protocol adopts a passive fault-tolerant control scheme based on the robustness of the system, which has a simple structure, and is timely and effective against sudden faults. In the second protocol, a novel nonlinear recursive sliding mode surface is designed, and a distributed fault observer is proposed to compensate the influences caused by actuator faults actively. Both of the protocols can prescribe the transient and steady-state performance of the system. Finally, the performances of the two protocols are verified by two simulation cases for the attitude systems of multiple unmanned underwater vehicles.</p>", "Keywords": "consensus;fault-tolerant;finite-time stability;multi-agent systems;prescribed performance control;sliding mode control", "DOI": "10.1002/asjc.2544", "PubYear": 2022, "Volume": "24", "Issue": "2", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence, Nankai University, Tianjin, China; Tianjin Key Laboratory of Intelligent Robotics, Nankai University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence, Nankai University, Tianjin, China; Tianjin Key Laboratory of Intelligent Robotics, Nankai University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence, Nankai University, Tianjin, China; Tianjin Key Laboratory of Intelligent Robotics, Nankai University, Tianjin, China"}], "References": [{"Title": "Robust output consensus for a class of fractional‐order interval multi‐agent systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "4", "Page": "1679", "JournalTitle": "Asian Journal of Control"}]}, {"ArticleId": 87896492, "Title": "Measuring Intelligence in Natural and Artificial Systems", "Abstract": "<p>A systematic understanding of the relationship between intelligence and consciousness can only be achieved when we can accurately measure intelligence and consciousness. In other work, I have suggested how the measurement of consciousness can be improved by reframing the science of consciousness as a search for mathematical theories that map between physical and conscious states. This paper discusses the measurement of intelligence in natural and artificial systems. While reasonable methods exist for measuring intelligence in humans, these can only be partly generalized to non-human animals and they cannot be applied to artificial systems. Some universal measures of intelligence have been developed, but their dependence on goals and rewards creates serious problems. This paper sets out a new universal algorithm for measuring intelligence that is based on a system’s ability to make accurate predictions. This algorithm can measure intelligence in humans, non-human animals and artificial systems. Preliminary experiments have demonstrated that it can measure the changing intelligence of an agent in a maze environment. This new measure of intelligence could lead to a much better understanding of the relationship between intelligence and consciousness in natural and artificial systems, and it has many practical applications, particularly in AI safety.</p>", "Keywords": "", "DOI": "10.1142/S2705078521500090", "PubYear": 2021, "Volume": "8", "Issue": "2", "JournalId": 75350, "JournalTitle": "Journal of Artificial Intelligence and Consciousness", "ISSN": "2705-0785", "EISSN": "2705-0793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Middlesex University, London, NW4 4BT, UK"}], "References": [{"Title": "The Relationships Between Intelligence and Consciousness in Natural and Artificial Systems", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "51", "JournalTitle": "Journal of Artificial Intelligence and Consciousness"}]}, {"ArticleId": ********, "Title": "DEQLFER — A Deep Extreme Q-Learning Firefly Energy Efficient and high performance routing protocol for underwater communication", "Abstract": "With an advent of Underwater sensor networks, underwater communication has reached its new dimension of research. These networks are characterized by the elongated end to end delay, high energy utility and most importantly dynamic network topologies. By incorporating these characteristics, numerous automated routing algorithms has been proposed to achieve the energy efficient and low latency data transmission. But still, short-comings still exists due to the above mentioned characteristics and the most comprehensive routing algorithms are badly desired. In this article, a novel routing scheme based on Q-learning framework and Deep Extreme Learning Machines aided with Adaptive Firefly Routing algorithm to address the above mentioned research constraints including energy efficiency and network unsteadiness in underwater communication , that practices the hybrid combination of reward function and adaptive fireflies to determine the optimal routing mechanism. In this algorithm, traditional q-learning mechanism has been replaced by the powerful q-deep extreme learning mechanism which uses the adaptive reward function for the varying underwater environment and to boost the packet-delivery ratio (PDR) and throughputs. Also the paper uses the powerful firefly aided routing mechanism to achieve the energy efficient data transmission and to avoid the void dilemma problems. The extensive experimentations has been conducted on the proposed algorithm and compared with other state of art schemes such as Q deep q-Learning energy aware routing protocol (DQLER), DELR Protocols and VBF protocols in which the proposed algorithm has outperformed than the compared existing algorithms in terms of complexity, energy consumption , packet delivery ratio and end to end delay.", "Keywords": "Underwater sensor networks ; Deep extreme learning machines ; Adaptive firefly algorithms ; Q-learning ; Reward function ; DQLER", "DOI": "10.1016/j.comcom.2021.04.030", "PubYear": 2021, "Volume": "174", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, Vels Institute of Science, Technology & Advanced Studies, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Vels Institute of Science, Technology & Advanced Studies, India"}], "References": []}, {"ArticleId": ********, "Title": "Evaluating disease similarity based on gene network reconstruction and representation", "Abstract": "Motivation <p>Quantifying the associations between diseases is of great significance in increasing our understanding of disease biology, improving disease diagnosis, re-positioning and developing drugs. Therefore, in recent years, the research of disease similarity has received a lot of attention in the field of bioinformatics. Previous work has shown that the combination of the ontology (such as disease ontology and gene ontology) and disease–gene interactions are worthy to be regarded to elucidate diseases and disease associations. However, most of them are either based on the overlap between disease-related gene sets or distance within the ontology’s hierarchy. The diseases in these methods are represented by discrete or sparse feature vectors, which cannot grasp the deep semantic information of diseases. Recently, deep representation learning has been widely studied and gradually applied to various fields of bioinformatics. Based on the hypothesis that disease representation depends on its related gene representations, we propose a disease representation model using two most representative gene resources HumanNet and Gene Ontology to construct a new gene network and learn gene (disease) representations. The similarity between two diseases is computed by the cosine similarity of their corresponding representations.</p> Results <p>We propose a novel approach to compute disease similarity, which integrates two important factors disease-related genes and gene ontology hierarchy to learn disease representation based on deep representation learning. Under the same experimental settings, the AUC value of our method is 0.8074, which improves the most competitive baseline method by 10.1%. The quantitative and qualitative experimental results show that our model can learn effective disease representations and improve the accuracy of disease similarity computation significantly.</p> Availability and implementation <p>The research shows that this method has certain applicability in the prediction of gene-related diseases, the migration of disease treatment methods, drug development and so on.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btab252", "PubYear": 2021, "Volume": "37", "Issue": "20", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Information and Computer Engineering, Northeast Forestry University, Harbin 150004, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information and Computer Engineering, Northeast Forestry University, Harbin 150004, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Computer Engineering, Northeast Forestry University, Harbin 150004, China"}], "References": []}, {"ArticleId": 87896638, "Title": "Gaze Estimation Using Neural Network And Logistic Regression", "Abstract": "<p>Currently, a large number of mature methods are available for gaze estimation. However, most regular gaze estimation approaches require additional hardware or platforms with professional equipment for data collection or computing that typically involve high costs and are relatively tedious. Besides, the implementation is particularly complex. Traditional gaze estimation approaches usually require systematic prior knowledge or expertise for practical operations. Moreover, they are primarily based on the characteristics of pupil and iris, which uses pupil shapes or infrared light and iris glint to estimate gaze, requiring high-quality images shot in special environments and other light source or professional equipment. We herein propose a two-stage gaze estimation method that relies on deep learning methods and logistic regression, which can be applied to various mobile platforms without additional hardware devices or systematic prior knowledge. A set of automatic and fast data collection mechanism is designed for collecting gaze images through a mobile platform camera. Additionally, we propose a new annotation method that improves the prediction accuracy and outperforms the traditional gridding annotation method. Our method achieves good results and can be adapted to different applications.</p>", "Keywords": "", "DOI": "10.1093/comjnl/bxab043", "PubYear": 2022, "Volume": "65", "Issue": "8", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Medical Technology, Health Science Center, Peking University, Beijing 100191, P.R. China;School of Health Humanities, Peking University, Beijing 100191, P.R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Medical Technology, Health Science Center, Peking University, Beijing 100191, P.R. China;Department of Biostatistics, School of Public Health, Peking University, Beijing 100191, P.R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "Institute of Medical Technology, Health Science Center, Peking University, Beijing 100191, P.R. China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Medical Technology, Health Science Center, Peking University, Beijing 100191, P.R. China;Department of Medical Physics, Health Science Center, Peking University, Beijing 100191, P.R. China"}], "References": []}, {"ArticleId": 87896741, "Title": "Off-Policy Evaluation of the Performance of a Robot Swarm: Importance Sampling to Assess Potential Modifications to the Finite-State Machine That Controls the Robots", "Abstract": "<p> Due to the decentralized, loosely coupled nature of a swarm and to the lack of a general design methodology, the development of control software for robot swarms is typically an iterative process. Control software is generally modified and refined repeatedly, either manually or automatically, until satisfactory results are obtained. In this paper, we propose a technique based on off-policy evaluation to estimate how the performance of an instance of control software—implemented as a probabilistic finite-state machine—would be impacted by modifying the structure and the value of the parameters. The proposed technique is particularly appealing when coupled with automatic design methods belonging to the AutoMoDe family, as it can exploit the data generated during the design process. The technique can be used either to reduce the complexity of the control software generated, improving therefore its readability, or to evaluate perturbations of the parameters, which could help in prioritizing the exploration of the neighborhood of the current solution within an iterative improvement algorithm. To evaluate the technique, we apply it to control software generated with an AutoMoDe method, Chocolate − 6 S   . In a first experiment, we use the proposed technique to estimate the impact of removing a state from a probabilistic finite-state machine. In a second experiment, we use it to predict the impact of changing the value of the parameters. The results show that the technique is promising and significantly better than a naive estimation. We discuss the limitations of the current implementation of the technique, and we sketch possible improvements, extensions, and generalizations. </p>", "Keywords": "automatic design;control software architecture;importance sampling;reinforcement learning;swarm robotics", "DOI": "10.3389/frobt.2021.625125", "PubYear": 2021, "Volume": "8", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IRIDIA, Université libre de Bruxelles, Brussels, Belgium."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "IRIDIA, Université libre de Bruxelles, Brussels, Belgium."}], "References": [{"Title": "Disentangling automatic and semi-automatic approaches to the optimization-based design of control software for robot swarms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "9", "Page": "494", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 87897106, "Title": "SAT: A Software for Assessing the Risk of Desertification in Spain", "Abstract": "<p>Desertification is a major global environmental issue exacerbated by climate change. Strategies to combat desertification include prevention which seeks to reverse the process before the system reaches the stable desertified state. One of these initiatives is to implement early warning tools. This paper presents SAT (the Spanish acronym for Early Warning System), a decision support system (DSS), for assessing the risk of desertification in Spain, where 20% of the land has already been desertified and 1% is in active degradation. SAT relies on three versions of a Generic Desertification Model (GDM) that integrates economics and ecology under the predator-prey paradigm. The models have been programmed using Vensim, a type of software used to build and simulate System Dynamics (SD) models. Through Visual Basic programming, these models are operated from the Excel environment. In addition to the basic simulation exercises, specially designed tools have been coupled to assess the risk of desertification and determine the ranking of the most influential factors of the process. The users targeted by SAT are government land-use planners as well as desertification experts. SAT tool is implemented for five case studies, each one of them representing a desertification syndrome identified in Spain. Given the general nature of the tool and the fact that all United Nations Convention to Combat Desertification (UNCCD) signatory countries are committed to developing their National Plans to Combat Desertification (NPCD), SAT could be exported to regions threatened by desertification and expanded to cover more case studies.</p>", "Keywords": "", "DOI": "10.1155/2020/7563928", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto Multidisciplinar para el Estudio del Medio “Ramón Margalef”, Universidad de Alicante, Carretera de San Vicente del Raspeig s/n, 03690 San Vicente del Raspeig, Alicante, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Economía Agraria, Estadística y Gestión de Empresas, Universidad Politécnica de Madrid, 28040 Madrid, Spain"}, {"AuthorId": 3, "Name": "Francisco <PERSON>", "Affiliation": "Instituto Geológico y Minero de España (IGME), 28003 Madrid, Spain;Instituto de Ciencias Químicas Aplicadas, Facultad de Ingeniería, Universidad Autónoma de Chile, 7500138 Santiago, Chile"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centro de Ciencias Humanas y Sociales, Consejo Superior de Investigaciones Científicas, 28037 Madrid, Spain"}], "References": [{"Title": "AQUACOAST: A Simulation Tool to Explore Coastal Groundwater and Irrigation Farming Interactions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}]}, {"ArticleId": 87897107, "Title": "A Statistical Tool to Generate Potential Future Climate Scenarios for Hydrology Applications", "Abstract": "<p>Global warming associated with greenhouse emissions will modify the availability of water resources in the future. Methodologies and tools to assess the impacts of climate change are useful for policy making. In this work, a new tool to generate potential future climate scenarios in a water resources system from historical and regional climate models’ information has been developed. The GROUNDS tool allows generation of the future series of precipitation, temperature (minimum, mean, and maximum), and potential evapotranspiration. It is a valuable tool for assessing the impacts of climate change in hydrological applications since these variables play a significant role in the water cycle, and it can be applicable to any case study. The tool uses different approaches and statistical correction techniques to generate individual local projections and ensembles of them. The non-equifeasible ensembles are created by combining the individual projections whose control or corrected control simulation has a better fit to the historical series in terms of basic and droughts statistics. In this work, the tool is presented, and the methodology implemented is described. It is also applied to a case study to illustrate how the tool works. The tool was previously tested in different typologies of water resources systems that cover different spatial scales (river basin, aquifer, mountain range, and country), obtaining satisfactory results. The local future scenarios can be propagated through appropriate hydrological models to study the impacts on other variables (e.g., aquifer recharge, chloride concentration in coastal aquifers, streamflow, snow cover area, and snow depth). The tool is also useful in quantifying the uncertainties of the future scenarios by combining them with stochastic weather generators.</p>", "Keywords": "", "DOI": "10.1155/2020/8847571", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "Antonio<PERSON><PERSON>-<PERSON>", "Affiliation": "Spanish Geological Survey, Urb. Alcázar del Genil 4, Bajo, 18006 Granada, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Spanish Geological Survey, Urb. Alcázar del Genil 4, Bajo, 18006 Granada, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Pardo-Igúzquiza", "Affiliation": "Spanish Geological Survey, Ríos Rosas 23, 28003 Madrid, Spain"}], "References": []}, {"ArticleId": 87897426, "Title": "Parallel solving of multiple information-coordinated global optimization problems", "Abstract": "This paper proposes an efficient approach for the parallel solution of computationally time consuming problems of multiple global optimization, in which minimized functions can be multiextremal and calculating function values may require huge amounts of computations. The proposed approach is based on the information-statistical theory of global optimization, within which a general computational scheme of global optimization methods is proposed. In the paper, this general scheme is expanded by the possible reuse of search information obtained in the process of computations when solving multiple global optimization problems. Within the framework of the proposed generalized scheme, parallel algorithms are proposed for computational systems with shared and distributed memory. Results of computational experiments demonstrated that the proposed approach can significantly reduce the computational complexity of solving multiple global optimization problems.", "Keywords": "Global optimization ; Dimensionality reduction ; Search information ; Parallel methods of global search ; Computational complexity", "DOI": "10.1016/j.jpdc.2021.04.009", "PubYear": 2021, "Volume": "154", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Informational Technologies, Mathematics and Mechanics, Mathematical Center, Lobachevsky State University of Nizhny Novgorod, Nizhni Novgorod, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informational Technologies, Mathematics and Mechanics, Mathematical Center, Lobachevsky State University of Nizhny Novgorod, Nizhni Novgorod, Russia;Corresponding author"}], "References": []}, {"ArticleId": 87897452, "Title": "Pair-wise ranking based preference learning for points-of-interest recommendation", "Abstract": "Recommending point-of-interest (POI) to users accurately is a hot topic in business. In the past, many researchers proposed recommendation models based on collaborative filtering or matrix factorization from the perspectives of time, geography, and social relationship. However, only a few studies have focused on user preference which is the key factor influencing user decision. This work focuses on studying the representation and mining of user preference from check-in data for POI recommendation. Pair-wise ranking is the common solution for implementing preference learning. However, traditional ways of constructing pair-wise data cut off the connections between multiple options in the decision process, affecting the effectiveness of preference learning. In this work, we change the ratio of negative to positive instance in pair-wise data from 1:1 to k :1 to ensure the data construction in line with the real decision making process. We propose a new negative sampling method taking the geographical distance and POI categorical distance into consideration jointly for enhancing the quality of training data. For our specialized pair-wise data, we propose a new optimization criterion for implementing effective preference learning. Finally, we conduct extensive experiments on two real-world datasets to validate the effectiveness of our proposed approach. The experiment results show that our approach outperforms the state-of-the-art models by at least 19.7% on F1-Score and 24.4% on nDCG . Additionally, our approach can be easily generalized to other domains, such as commodities, news, and movie recommendation.", "Keywords": "Negative sampling ; Neural network ; POI recommendation ; Pair-wise learning ; Semantic representation", "DOI": "10.1016/j.knosys.2021.107069", "PubYear": 2021, "Volume": "225", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SILC Business School, Shanghai University, Shanghai, 201899, China"}, {"AuthorId": 2, "Name": "<PERSON>ng Mu", "Affiliation": "SILC Business School, Shanghai University, Shanghai, 201899, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Decision and Information Sciences, School of Business Administration, Oakland University, Rochester, MI 48309, United States of America;Centre for Data Science and Big Data Analytics, Oakland University, Rochester, MI 48309, United States of America"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Shandong University of Finance and Economics, Jinan, Shandong, 250014, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Management & Engineering, ShangHai University of Finance and Economics, ShangHai, 200433, China;Shanghai Key laboratory of Financial Information Technology, ShangHai, 200433, China"}], "References": [{"Title": "Deep Neural Models and Retrofitting for Arabic Text Categorization", "Authors": "Fatima<PERSON><PERSON><PERSON><PERSON>; <PERSON>; Noureddine En-Nahnahi", "PubYear": 2020, "Volume": "16", "Issue": "2", "Page": "74", "JournalTitle": "International Journal of Intelligent Information Technologies"}]}, {"ArticleId": 87897478, "Title": "Disjoint direct product decompositions of permutation groups", "Abstract": "Let H ≤ S n be an intransitive group with orbits Ω 1 , Ω 2 , … , Ω k . Then certainly H is a subdirect product of the direct product of its projections onto each orbit, H   Ω 1 × H   Ω 2 × … × H   Ω k . Here we provide a polynomial time algorithm for computing the finest partition P of the H -orbits such that H = ∏ c ∈ P H   c and we demonstrate its usefulness in some applications.", "Keywords": "Permutation group ; Computation ; Direct product ; Subdirect product ; Decomposition ; Computer algebra system", "DOI": "10.1016/j.jsc.2021.04.003", "PubYear": 2022, "Volume": "108", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Jack Cole Building, St Andrews, KY16 9SX, UK;https://msc2.host.cs.st-andrews.ac.uk"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Jack Cole Building, St Andrews, KY16 9SX, UK;https://caj.host.cs.st-andrews.ac.uk"}], "References": []}, {"ArticleId": 87897495, "Title": "BESLE: Boundary element software for 3D linear elasticity", "Abstract": "A new software based on boundary element analysis was developed, which has a variety of functionalities that allow simulating practical problems of 3D linear elasticity of solids including: i) static, quasi-static, dynamic, and transient regimes, using ii) morphologies with a single or multiple domains with iii) isotropic and anisotropic materials and iv) Dirichlet or Neumann boundary conditions. Moreover, it comes complete with datasets for the elastic properties of a range of materials, Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements The authors would like to thank to the University of Campinas (Brazil), Brunel University London (UK), and the University of Portsmouth (UK) for the facilities and structure provided to develop this version of BESLE. The project was funded by the National Council for Scientific and Technological Development -CNPq (Grant Numbers: 312493/2013-4 , 154283/2014-2 and 312536/2017-8 ), the Brazilian Coordination for the Improvement of Higher Education Personnel -CAPES (Grant Number: 435214/2019-01 ). This References (44) I<PERSON> et al. Int. J. Solids Struct. (2008) A<PERSON><PERSON><PERSON> et al. Comput. Struct. (2018) <PERSON><PERSON> et al. Comput. Methods Appl. Mech. Eng. (2018) <PERSON><PERSON> et al. Mech. Mater. (2018) A<PERSON><PERSON>. <PERSON> et al. Mech. Mater. (2018) E.L. Albuquerque et al. Int. J. Solids Struct. (2002) R.Q. Rodriguez et al. Eng. Anal. Bound. Elem. (2019) J. Dölz et al. SoftwareX (2020) P.R. Amestoy et al. Parallel Comput. (2006) C.L. Tan et al. Int. J. Solids Struct. (2013) A. Fedorov et al. Magn. Reson. Imaging (2012) A. Ayala et al. J. Comput. Appl. Math. (2020) I. Benedetti et al. Comput. Mater. Sci. (2013) A.F. Galvis et al. Comput. Methods Appl. Mech. Eng. (2020) R. Clough et al. Dynamic of Structures (2003) J.K. Shin et al. Biomed. Res. (2018) C.A. Brebbia et al. Boundary Element Techniques. Theory and Applications in Engineering (1984) J.H. Kane Boundary Element Analysis in Engineering Continuum Mechanics (1994) P.W. Partridge et al. The Dual Reciprocity Boundary Element Method (1991) R.Q. Rodríguez et al. Comput. Model. Eng. Sci. (2014) T.C.T. Ting et al. Q. J. Mech. Appl. Math. (1997) Z. Yao et al. Eng. Comput. (2019) View more references Cited by (0) Recommended articles (6) Research article An arbitrary Lagrangian Eulerian smoothed particle hydrodynamics (ALE-SPH) method with a boundary volume fraction formulation for fluid-structure interaction Engineering Analysis with Boundary Elements, Volume 128, 2021, pp. 274-289 Show abstract We present a new weakly-compressible smoothed particle hydrodynamics (SPH) method capable of modeling non-slip fixed and moving wall boundary conditions. The formulation combines a boundary volume fraction (BVF) wall approach with the transport-velocity SPH method. The resulting method, named SPH-BVF, offers detection of arbitrarily shaped solid walls on-the-fly, with small computational overhead due to its local formulation. This simple framework is capable of solving problems that are difficult or infeasible for standard SPH, namely flows subject to large shear stresses or at moderate Reynolds numbers, and mass transfer in deformable boundaries. In addition, the method extends the transport-velocity formulation to reaction-diffusion transport of mass in Newtonian fluids and linear elastic solids, which is common in biological structures. Taken together, the SPH-BVF method provides a good balance of simplicity and versatility, while avoiding some of the standard obstacles associated with SPH: particle penetration at the boundaries, tension instabilities and anisotropic particle alignments, that hamper SPH from being applied to complex problems such as fluid-structure interaction in a biological system. Research article A multi-GPU method for ADI-based fractional-step integration of incompressible Navier-Stokes equations Computer Physics Communications, Volume 265, 2021, Article 107999 Show abstract A computational method for GPU-accelerated fractional-step integration of incompressible Navier-Stokes equations based on the Alternating Direction Implicit (ADI) method is presented. Non-iterative, direct solution methods used in the semi-implicit fractional-step method take advantage of tridiagonal systems and Fourier transform whose solution can be computed using fast algorithms on a single GPU. However, when data is distributed to multiple GPUs, all-to-all matrix transposition is required, which increases computational cost significantly. In this work, a new strategy that does not require all-to-all transposition is proposed. The computational domain is divided in the wall-normal direction, and decoupled tridiagonal systems are obtained using Parallel Diagonal Dominant (PDD) and Parallel Partition (PPT) methods. An optimal batch size is determined to maximize the performance of PDD and PPT methods within a given amount of GPU memory. Strengths and weaknesses of this type of domain decomposition are investigated in comparison to conventional ways of dividing the domain along streamwise or spanwise directions. Using 8 NVIDIA Tesla P100 GPUs, the utility of the present method is demonstrated in a direct numerical simulation (DNS) of a canonical zero-pressure-gradient turbulent boundary layer and a DNS of a K-type boundary-layer transition on 1.4 billion grid cells. Research article Finite element solver for data-driven finite strain elasticity Computer Methods in Applied Mechanics and Engineering, Volume 379, 2021, Article 113756 Show abstract A nominal finite element solver is proposed for data-driven finite strain elasticity. It bypasses the need for a constitutive model by considering a database of deformation gradient/first Piola–Kirchhoff stress tensors pairs. The boundary value problem is reformulated as the constrained minimization problem of the distance between (i) the mechanical states, i.e. strain–stress, in the body and (ii) the material states coming from the database. The corresponding constraints are of two types: kinematical, i.e. displacement–strain relation, and mechanical, i.e. conservation linear and angular momenta. The solver uses alternated minimization: the material states are determined from a local search in the database using an efficient tree-based nearest neighbor search algorithm, and the mechanical states result from a standard constrained minimization addressed with an augmented Lagrangian approach. The performance of the solver is demonstrated by means of 2D sanity check examples: the data-driven solution converges to the classical finite element solution when the material database increasingly approximates the constitutive model. In addition, we demonstrate that the balance of angular momentum, which was classically not taken into account in previous data-driven studies, must be enforced as a constraint to ensure the convergence of the method. Research article GENE-X: A full- f gyrokinetic turbulence code based on the flux-coordinate independent approach Computer Physics Communications, Volume 264, 2021, Article 107986 Show abstract Understanding and predicting plasma turbulence in the scrape-off layer of a magnetic confinement fusion device is a key open problem in modern plasma physics. The transitional region between the core and scrape-off layer poses a difficult problem for turbulence simulations. The poloidal magnetic field vanishes at the X-point of a fusion device, which introduces a coordinate singularity in the commonly used field-aligned coordinates. In the present work, we present a full- f gyrokinetic code based on a locally field-aligned coordinate system that is flux-coordinate independent and free of singularities. The coordinate system, as well as the equations and numerical methods are described. In addition, careful numerical and physical verifications in closed magnetic flux surfaces are included. Research article FEMS – A Mechanics-oriented Finite Element Modeling Software Computer Physics Communications, Volume 260, 2021, Article 107729 Show abstract This paper is a presentation of a Finite Element Modeling Software named FEMS that integrates mesh generation and adaption features in order to alleviate significantly the difficulty of designing a Finite Element (FE) mesh for a particular problem. FEMS is targeted at engineers and scientists addressing localization problems in mechanics, although it should be suited to many other applications. FEMS is particularly relevant for problems with internal interfaces, both in solid and fluid mechanics, as it has both explicit and implicit interface representation. The former can be generated from signed distance functions using body-fitted meshing capabilities implemented in FEMS, while the latter relies on the level-set method. The choice between the one or the other can be made by the user depending on the severity of deformations in the neighborhood of an interface. During the simulation, FEMS adapts the FE mesh automatically to achieve the best accuracy for a prescribed number of nodes. This is possible for both linear and quadratic interpolation. Additionally, in an updated Lagrangian setting, FEMS triggers mesh adaption automatically to avoid element flipping during node motion. The capabilities of FEMS are demonstrated in this paper for fluid and solid mechanics problems featuring turbulence, multiphase flow, large deformations and plasticity. This wide range of problems that can be handled by FEMS should prove its great interest for the computational mechanics community. Program Title: FEMS CPC Library link to program files: http://dx.doi.org/10.17632/rgv4hkrxjw.1 Licensing provisions: GNU General Public License version 3 Programming language: C/C++ Nature of problem: Partial differential equations in one, two or three dimensions of space related to computational mechanics and used to model large deformations, nonlinear material behavior, incompressibility, heat transfer, turbulent and/or multiphase flow with surface tension. Solution method: Finite element method, higher-order elements, mixed and variational multiscale formulations, level-set method, error estimators, isotropic and anisotropic unstructured mesh adaption, image meshing (from microscopy or tomography sources). Additional comments including restrictions and unusual features: Shared-memory (OpenMP) parallelism, GPU-accelerated, unstructured mesh adaption to the finite element solution, the software is compatible with many element types but its mesh adaption feature is restricted to triangles/tetrahedra. Research article The Ising model with Hybrid Monte Carlo Computer Physics Communications, Volume 265, 2021, Article 107978 Show abstract The Ising model is a simple statistical model for ferromagnetism. There are analytic solutions for low dimensions and very efficient Monte Carlo methods, such as cluster algorithms, for simulating this model in special cases. However most approaches do not generalize to arbitrary lattices and couplings. We present a formalism that allows one to apply Hybrid Monte Carlo (HMC) simulations to the Ising model, demonstrating how a system with discrete degrees of freedom can be simulated with continuous variables. Because of the flexibility of HMC, our formalism is easily generalizable to arbitrary modifications of the model, creating a route to leverage advanced algorithms such as shift preconditioners and multi-level methods, developed in conjunction with HMC. <sup> ☆ </sup> The review of this paper was arranged by Prof. N.S. Scott. <sup> ☆☆ </sup> This paper and its associated computer program are available via the Computer Physics Communications homepage on ScienceDirect ( http://www.sciencedirect.com/science/journal/00104655 ). View full text © 2021 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.cpc.2021.108009", "PubYear": 2021, "Volume": "265", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "Andre<PERSON>", "Affiliation": "School of Mechanical Engineering, University of Campinas, Campinas 13083-860, SP, Brazil;School of Mathematics and Physics, University of Portsmouth, Portsmouth, PO1 2UP, UK;Corresponding author at: School of Mathematics and Physics, University of Portsmouth, Portsmouth, PO1 2UP, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, University of Campinas, Campinas 13083-860, SP, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, University of Campinas, Campinas 13083-860, SP, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, University of Campinas, Campinas 13083-860, SP, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Physics, University of Portsmouth, Portsmouth, PO1 2UP, UK"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, University of Campinas, Campinas 13083-860, SP, Brazil"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Design and Physical Sciences, Brunel University London, Uxbridge UB8 3PH, UK;Department of Civil and Environmental Engineering, Pontifical Catholic University of Rio de Janeiro, Rio de Janeiro 22451-900, Brazil"}], "References": [{"Title": "Bembel: The fast isogeometric boundary element C++ library for La<PERSON>, Helmholtz, and electric wave equation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100476", "JournalTitle": "SoftwareX"}]}, {"ArticleId": 87897834, "Title": "Distance education amid a pandemic: Which psycho‐demographic variables affect students in higher education?", "Abstract": "<p>The Covid-19 pandemic has led to a rapid transition from face-to-face to distance learning. The problems caused by this rapid transition are combined with the negative psychological outcomes of the pandemic, leading to numerous problems and difficulties in the teaching and learning processes. The recentness of these issues and developments requires detailed investigation as to how they affect distance learning. This study aims to investigate the role of psycho-demographic variables in the motivation and attendance of higher education students in distance education within the context of the Covid-19 pandemic. In this descriptive study, we collected data from 1494 Turkish university students via an online survey. Quantitative data were analysed using correlation analysis, t test, one-way ANOVA, multiple linear regression analysis, and structural equation modelling. Stress, anxiety, depression and intolerance of uncertainty were correlated negatively with distance learning motivation and frequency of distance learning attendance. While students who attended distance learning only synchronously joined the courses more frequently, the motivation of those who joined the courses sometimes synchronously and sometimes asynchronously was higher. The strength of the relationships between intolerance of uncertainty and distance learning motivation was significantly increased via anxiety and depression. Findings highlight the need for analysis of psycho-demographic variables while designing and implementing distance education programmes. Psychological variables including stress, anxiety and depression are related to motivation and attendance during distance education. While using both synchronous and asynchronous distance learning enhances motivation, synchronous learning increases attendance.</p> <h3 >Lay Description</h3> <h3 > What is currently known about the subject matter?</h3> Distance education is becoming a popular instructional method especially after the COVID-19 pandemic. The quality of distance education and the level of learning can be affected by extraordinary situations such as the pandemic. Social and emotional aspects of distance education can affect the effectiveness of the instruction. <h3 > What this paper adds to this?</h3> Psychological variables including stress, anxiety and depression are related to motivation and attendance during distance education. The strength of the relationships between intolerance of uncertainty and distance learning motivation was significantly increased via anxiety and depression. <h3 > Implications of study findings for practitioners</h3> Attending distance learning is higher among town and urban residents. Women students attend more but men are more motivated in distance learning. While hybrid distance learning increases motivation, synchronous instruction increases attendance.", "Keywords": "distance learning;motivation;pandemic;anxiety;uncertainty;depression", "DOI": "10.1111/jcal.12544", "PubYear": 2021, "Volume": "37", "Issue": "6", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Educational Sciences, Mardin Artuklu University, Mardin, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Educational Sciences, Mardin Artuklu University, Mardin, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychology, Ordu University, Ordu, Turkey"}, {"AuthorId": 4, "Name": "Halis <PERSON>", "Affiliation": "Department of Educational Sciences, Mardin Artuklu University, Mardin, Turkey"}], "References": []}, {"ArticleId": 87897867, "Title": "Empathy Cannot Sustain Action in Technology Accessibility", "Abstract": "", "Keywords": "\";\"Article_NLM\":\"XML (NLM)\";\"Article_OriginalArticle\":\"ORIGINAL ARTICLE\";\"Article_PaperPendingPublishedDate\":\"Paper pending published: \";\"Article_PDF\":\"PDF\";\"Article_ProvisionalPDF\":\"Provisional PDF\";\"Article_PublishedDate\":\"Published online: \";\"Article_ReadFullText\":\"Read Full Text\";\"Article_ReceivedDate\":\"Received: \";\"Article_ReferenceManager\":\"Reference Manager\";\"Article_ReviewedBy\":\"Reviewed by: \";\"Article_RTInfoText\":\"This article is part of the Research Topic\";\"Article_ShareOn\":\"SHARE ON\";\"Article_SimpleTEXTfile\":\"Simple TEXT file\";\"Article_SupplementalData\":\"SUPPLEMENTAL DATA\";\"Article_TableOfContent\":\"TABLE OF CONTENTS\";\"Article_ViewEnhancedPDF\":\"ReadCube\";\"Article_XML\":\"XML\";\"BrowserWarningText\":\"<h2>Warning!</h2><p>You are using an <strong>outdated</strong> browser. This page doesn't support Internet Explorer 6; 7 and 8.<br />Please <a class=\"blue\" href=\"http://browsehappy.com/\">upgrade your browser</a> or <a class=\"blue\" href=\"http://www.google.com/chromeframe/?redirect=true\">activate Google Chrome Frame</a> to improve your experience.</p>\";\"COMMENT_HEADERTEXT\":\"Comment text too long\";\"COMMENT_WARNINGTEXT\":\"Comments must be less than 4;000 characters. You have entered \";\"Impact_BackToArticle\":\"Back to article\";\"People_Also_LookedAt\":\"People also looked at\"}; return { value: function(key) { if (languageSet[key]) { return languageSet[key]; } else { throw new Error('Unable to get the value status from the language set'); // Use Error; not FRError } } }; })(); var FRJournalDetails = (function() { return { JournalType: 'section'; JournalId: '1511'; SectionId: '963' }; })(); var FRArticleDetails = (function() { return { ArticleId: '617044' }; })(); var FRArticleRecaptchaSettings = (function() { return { RecaptchaSiteKey: '6LdG3i0UAAAAAOC4qUh35ubHgJotEHp_STXHgr_v' }; })(); var addthis_config = addthis_config    {}; addthis_config.data_track_addressbar = false; addthis_config.data_track_clickback = false; .journal-computer-science { background: #fff url('https://3718aeafc638f96f5bd6-d4a9ca15fc46ba40e71f94dec0aad28c.ssl.cf1.rackcdn.com/journal-computer-science.png') no-repeat 100% 0px; } /* Displays/Screens (e.g. 19\" WS @ 1440x900) --------------- */ @media only screen and (max-width: 1649px) { .journal-computer-science { background: #fff url('https://3718aeafc638f96f5bd6-d4a9ca15fc46ba40e71f94dec0aad28c.ssl.cf1.rackcdn.com/journal-computer-science.png') no-repeat 100% 0px; background-size: 48%; }} /* Displays/Screens (e.g. MacBook @ 1280x800) -------------- */ @media only screen and (max-width: 1409px) { .journal-computer-science { background: #fff url('https://3718aeafc638f96f5bd6-d4a9ca15fc46ba40e71f94dec0aad28c.ssl.cf1.rackcdn.com/journal-computer-science.png') no-repeat 100% 0px; background-size: 46%; }} /* Large Devices; Wide Screens --------- */ @media only screen and (max-width : 1250px) { .journal-computer-science { background: #fff url('https://3718aeafc638f96f5bd6-d4a9ca15fc46ba40e71f94dec0aad28c.ssl.cf1.rackcdn.com/journal-computer-science.png') no-repeat 130% 0px; background-size: 59%; }} /* Medium Devices; Desktops ---------- */ @media only screen and (max-width : 992px) { .journal-computer-science { background: #fff url('https://3718aeafc638f96f5bd6-d4a9ca15fc46ba40e71f94dec0aad28c.ssl.cf1.rackcdn.com/journal-computer-science.png') no-repeat 120% 0px; background-size: 64%; }} /* Small Devices; Tablets ------------ */ @media only screen and (max-width : 768px) { .journal-computer-science { background: #fff url('https://3718aeafc638f96f5bd6-d4a9ca15fc46ba40e71f94dec0aad28c.ssl.cf1.rackcdn.com/journal-computer-science.png') no-repeat 110% 50px; background-size: 59%;\n}} /* Small Devices; Tablets --------- */ @media only screen and (max-width : 640px) { .journal-computer-science { background: #fff url('https://3718aeafc638f96f5bd6-d4a9ca15fc46ba40e71f94dec0aad28c.ssl.cf1.rackcdn.com/journal-computer-science.png') no-repeat 100% 70px; background-size: 59%; }} Warning! You are using an outdated browser. This page doesn't support Internet Explorer 6; 7 and 8. Please upgrade your browser or activate Google Chrome Frame to improve your experience.", "DOI": "10.3389/fcomp.2021.617044", "PubYear": 2021, "Volume": "3", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Southampton Education School, University of Southampton, Southampton, United Kingdom"}], "References": []}, {"ArticleId": 87897909, "Title": "Correction to: The AI doctor will see you now: assessing the framing of AI in news coverage", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00146-021-01218-9", "PubYear": 2024, "Volume": "39", "Issue": "3", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "Mercedes Bunz", "Affiliation": "Department of Digital Humanities, King’s College London, London, UK; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Digital Humanities, King’s College London, London, UK"}], "References": []}, {"ArticleId": 87898049, "Title": "Text classification model for methamphetamine-related tweets in Southeast Asia using dual data preprocessing techniques", "Abstract": "<span>Methamphetamine addiction is a prominent problem in Southeast Asia. Drug addicts often discuss illegal activities on popular social networking services. These individuals spread messages on social media as a means of both buying and selling drugs online. This paper proposes a model, the “text classification model of methamphetamine tweets in Southeast Asia” (TMTA), to identify whether a tweet from Southeast Asia is related to methamphetamine abuse. The research addresses the weakness of bag of words (BoW) by introducing BoW and Word2Vec feature selection (BWF) techniques. A domain-based feature selection method was performed using the BoW dataset and Word2Vec. The BWF dataset provided a smaller number of features than the BoW and TF–IDF dataset. We experimented with three candidate classifiers: Support vector machine (SVM), decision tree (J48) and naive bayes (NB). We found that the J48 classifier with the BWF dataset provided the best performance for the TMTA in terms of accuracy (0.815), F-measure (0.818), Kappa (0.528), Matthews correlation coefficient (0.529) and high area under the ROC Curve (0.763). Moreover, TMTA provided the lowest runtime (3.480 seconds) using the J48 with the BWF dataset.</span>", "Keywords": "data preprocessing;feature selection;methamphetamine;text classification;tweet", "DOI": "10.11591/ijece.v11i4.pp3617-3628", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "Narongsak <PERSON>", "Affiliation": "Kasetsart University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kasetsart University"}], "References": []}, {"ArticleId": 87898063, "Title": "Association rule hiding using integer linear programming", "Abstract": "<span>Privacy preserving data mining has become the focus of attention of government statistical agencies and database security research community who are concerned with preventing privacy disclosure during data mining. Repositories of large datasets include sensitive rules that need to be concealed from unauthorized access. Hence, association rule hiding emerged as one of the powerful techniques for hiding sensitive knowledge that exists in data before it is published. In this paper, we present a constraint-based optimization approach for hiding a set of sensitive association rules, using a well-structured integer linear program formulation. The proposed approach reduces the database sanitization problem to an instance of the integer linear programming problem. The solution of the integer linear program determines the transactions that need to be sanitized in order to conceal the sensitive rules while minimizing the impact of sanitization on the non-sensitive rules. We also present a heuristic sanitization algorithm that performs hiding by reducing the support or the confidence of the sensitive rules. The results of the experimental evaluation of the proposed approach on real-life datasets indicate the promising performance of the approach in terms of side effects on the original database.</span>", "Keywords": "association rule hiding;data sanitization;integer linear program;privacy preserving data mining;sensitive rules", "DOI": "10.11591/ijece.v11i4.pp3451-3458", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "Suma B.", "Affiliation": "RV College of Engineering"}, {"AuthorId": 2, "Name": "Shobha G.", "Affiliation": "RV College of Engineering"}], "References": []}, {"ArticleId": 87898065, "Title": "IoT based on secure personal healthcare using RFID technology and steganography", "Abstract": "<p>Internet of things (IoT) makes it attainable for connecting different various smart objects together with the internet. The evolutionary medical model towards medicine can be boosted by IoT with involving sensors such as environmental sensors inside the internal environment of a small room with a specific purpose of monitoring of person's health with a kind of assistance which can be remotely controlled. RF identification (RFID) technology is smart enough to provide personal healthcare providing part of the IoT physical layer through low-cost sensors. Recently researchers have shown more IoT applications in the health service department using RFID technology which also increases real-time data collection. IoT platform which is used in the following research is Blynk and RFID technology for the user's better health analyses and security purposes by developing a two-level secured platform to store the acquired data in the database using RFID and Steganography. Steganography technique is used to make the user data more secure than ever. There were certain privacy concerns which are resolved using this technique. Smart healthcare medical box is designed using SolidWorks health measuring sensors that have been used in the prototype to analyze real-time data.</p>", "Keywords": "NodeMCU;IoT;RFID;sensors;smart healthcare;steganography", "DOI": "10.11591/ijece.v11i4.pp3300-3309", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Ali <PERSON>", "Affiliation": "Asia Pacific University of Technology and Innovation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Asia Pacific University of Technology and Innovation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Asia Pacific University of Technology and Innovation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknologi MARA"}], "References": []}, {"ArticleId": 87898066, "Title": "An FPGA-based network system with service-uninterrupted remote functional update", "Abstract": "<p>The recent emergence of 5G network enables mass wireless sensors deployment for internet-of-things (IoT) applications. In many cases, IoT sensors in monitoring and data collection applications are required to operate continuously and active at all time (24/7) to ensure all data are sampled without loss. Field-programmable gate array (FPGA)-based systems exhibit a balanced processing throughput and datapath flexibility. Specifically, datapath flexibility is acquired from the FPGA-based system architecture that supports dynamic partial reconfiguration feature. However, device functional update can cause interruption to the application servicing, especially in an FPGA-based system. This paper presents a standalone FPGA-based system architecture that allows remote functional update without causing service interruption by adopting a redundancy mechanism in the application datapath. By utilizing dynamic partial reconfiguration, only the updating datapath is temporarily inactive while the rest of the circuitry, including the redundant datapath, remain active. Hence, there is no service interruption and downtime when a remote functional update takes place due to the existence of redundant application datapath, which is critical for network and communication systems. The proposed architecture has a significant impact for application in FPGA-based systems that have little or no tolerance in service interruption.</p>", "Keywords": "dual modular redundancy;dynamic partial reconfiguration;NetFPGA;service-uninterrupted remote functional update", "DOI": "10.11591/ijece.v11i4.pp3222-3228", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universiti Teknologi Malaysia"}], "References": []}, {"ArticleId": 87898067, "Title": "Real-time cloud system for managing blood units and convalescent plasma for COVID-19 patients", "Abstract": "<span>In health care systems, blood management services are essential to saving lives. In such systems, when a unit of blood is required, if the system is not able to provide it on time, sometimes this may lead to patient death, especially in critical cases. Unfortunately, even if the required blood unit is available within the system, contradictions may occur and the required blood unit may not be allocated to critical cases on time, due to the allocation of these units to lower priority cases or due to the isolated operate of blood banks within these systems. So, to overcome these obstacles, we proposed a real-time system on a cloud, to managing blood units within the whole health care system. This system will allocate blood units depends on the deadline and the severity of the case that needs blood, in addition to the types, quantities, and position of available blood units. Where, this system eliminated the need for human intervention in managing blood units, in addition to offering the ability to easily develop the system to deal with new urgent requirements, which need new methods of managing blood units; as is happening today with the COVID-19 epidemic. This system increases the performance, transparency, reliability, and accuracy of blood unit management operations while reducing the required cost and effort.</span>", "Keywords": "blood bank system;blood units management;cloud computing;COVID-19;healthcare system;real-time systems", "DOI": "10.11591/ijece.v11i4.pp3593-3600", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mosul University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mosul University"}], "References": []}, {"ArticleId": ********, "Title": "Design and development of handover simulator model in 5G cellular network", "Abstract": "<p>In the modern era of technology, the high speed internet is the most important part of human life. The current available network is reckoned to be slow in speed and not be up to snuff for data transmission regarding business applications. The objective of handover mechanism is to reassign the current session handle by internet gadget. The globe needs the next generation high mobility and throughput performance based internet model. This research paper explains the proposed method of design and development for handover based 5G cellular network. In comparison to the traditional method, we propose to control the handovers between base-stations using a concentric method. The channel simulator is applied over the range of the frequencies from 500 MHz to 150 GHz and radio frequency for the 700 MHz bandwidth. The performance of the simulation system is calculated on the basis of handover preparation and completion time regarding base station as well as number of users. From this experiment we achieve the 7.08 ms handover preparation time and 9.98 ms handover completion time. The author recommended the minimum handover completion time, perform the high speed for 5G cellular networks.</p>", "Keywords": "handover;LTE;MANET;SDN;simulator", "DOI": "10.11591/ijece.v11i4.pp3310-3318", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Al-Maarif University College"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Tun Hussein <PERSON>"}], "References": []}, {"ArticleId": 87898069, "Title": "Modified digital space vector pulse width modulation realization on low-cost FPGA platform with optimization for 3-phase voltage source inverter", "Abstract": "<p>The realization of power electronic applications on hardware is a challenging task. The digital control circuit strategies are used to overcome the analog control strategies by providing great flexibility with simple equipment and higher switching frequencies. In this manuscript, an area optimized, modified digital space vector (DSV) pulse width modulation is designed and realized on low-cost FPGA. The modified digital space vector pulse width modulation (DSVPWM) uses a phase-locked loop (PLL) to generate clocks using the digital clock manager (DCM). These DCM clocks are used in the DSVPWM module to synchronize the other sub-modules. The voltage generation unit generates the three-phase (3-Ф) voltages and is used in the alpha-beta generation and sector determination unit. The reference active vectors are made by the reference generation unit and used in switching time calculation. The PWM pulses are generated using switching time generation, and lastly, the dead time occurrence unit generates the final SVPWM gate pulses. The modified DSVPWM is synthesized and implemented on Spartan-3E FPGA. The modified DSVPWM utilizes 17% slices, works at 102.45 MHz, and consumes 0.070 W total power. The simulation results and the resource utilization of modified DSVPWM are represented in detail. The modified DSVPWM is compared with existing PWM approaches on different Spartan-series FPGAs with better chip area improvement</p>", "Keywords": "dead time;digital space vector;FPGA;phase-locked loop;pulse width modulation;sector calculation;VSI", "DOI": "10.11591/ijece.v11i4.pp3629-3638", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SJB Institute of Technology Research Centre"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> K. R.", "Affiliation": "SJB Institute of Technology"}], "References": []}, {"ArticleId": 87898070, "Title": "IoT-based air quality monitoring systems for smart cities: A systematic mapping study", "Abstract": "<p><p>The increased level of air pollution in big cities has become a major concern for several organizations and authorities because of the risk it represents to human health. In this context, the technology has become a very useful tool in the contamination monitoring and the possible mitigation of its impact. Particularly, there are different proposals using the internet of things (IoT) paradigm that use interconnected sensors in order to measure different pollutants. In this paper, we develop a systematic mapping study defined by a five-step methodology to identify and analyze the research status in terms of IoT-based air pollution monitoring systems for smart cities. The study includes 55 proposals, some of which have been implemented in a real environment. We analyze and compare these proposals in terms of different parameters defined in the mapping and highlight some challenges for air quality monitoring systems implementation into the smart city context.</p></p>", "Keywords": "air quality monitoring;internet of things;smart cities;systematic mapping study", "DOI": "10.11591/ijece.v11i4.pp3470-3482", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad de Antioquia"}, {"AuthorId": 2, "Name": "<PERSON>.", "Affiliation": "Universidad de Medell´ın"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad de Medell´ın"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad de Antioquia"}], "References": []}, {"ArticleId": 87898071, "Title": "Smart element aware gate controller for intelligent wheeled robot navigation", "Abstract": "<p><p>The directing of a wheeled robot in an unknown moving environment with physical barriers is a difficult proposition. In particular, having an optimal or near-optimal path that avoids obstacles is a major challenge. In this paper, a modified neuro-controller mechanism is proposed for controlling the movement of an indoor mobile robot. The proposed mechanism is based on the design of a modified Elman neural network (MENN) with an effective element aware gate (MEEG) as the neuro-controller. This controller is updated to overcome the rigid and dynamic barriers in the indoor area. The proposed controller is implemented with a mobile robot known as Khepera IV in a practical manner. The practical results demonstrate that the proposed mechanism is very efficient in terms of providing shortest distance to reach the goal with maximum velocity as compared with the MENN. Specifically, the MEEG is better than MENN in minimizing the error rate by 58.33%.</p></p>", "Keywords": "element wise aware gate;indoor environment;mobile robot;modified elman;neuro-controller", "DOI": "10.11591/ijece.v11i4.pp3022-3031", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Baghdad"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mustansiriyah University"}], "References": []}, {"ArticleId": 87898074, "Title": "Improved LEACH protocol for increasing the lifetime of WSNs", "Abstract": "<p><p>Recently, wireless sensor network (WSN) is taking a high place in several applications: military, industry, and environment. The importance of WSNs in current applications makes the WSNs the most developed technology at the research level and especially in the field of communication and computing. However, WSN’s performance deals with a number of challenges. Energy consumption is the most considerable for many researchers because nodes use energy to collect, treat, and send data, but they have restricted energy. For this reason, numerous efficient energy routing protocols have been developed to save the consumption of power. Low energy adaptive clustering hierarchy (LEACH) is considered as the most attractive one in WSNs. In the present document, we evaluate the LEACH approach effectiveness in the cluster-head (CH) choosing and in data transmission, then we propose an enhanced protocol. The proposed algorithm aims to improve energy consumption and prolong the lifetime of WSN through selecting CHs depending on the remaining power, balancing the number of nodes in clusters, determining abandoned nodes in order to send their data to the sink. Then CHs choose the optimal path to achieve the sink. Simulation results exhibit that the enhanced method can decrease the consumption of energy and prolong the life-cycle of the network.</p></p>", "Keywords": "Energy-efficiency;Improved LEACH;Network lifetime;Routing protocol;WSN", "DOI": "10.11591/ijece.v11i4.pp3106-3113", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hassan II University of Casablanca"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hassan II University of Casablanca"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hassan II University of Casablanca"}], "References": []}, {"ArticleId": ********, "Title": "Special Issue on Digital Geometry Processing for Large-Scale Structures and Environments", "Abstract": "<p>The application of digital geometry processing is undergoing an extension from small industrial products to large-scale structures and environments, including plants, factories, ships, bridges, buildings, forests, and indoor/outdoor/urban environments. This extension is being supported by recent advances in long-range 3D laser scanning technology. Laser scanners are mounted on various platforms, such as tripods, wheeled vehicles, airplanes, and UAVs, and the laser scanning systems are used to efficiently acquire dense and accurate digitized 3D data of the geometry, called point clouds, of large-scale structures and environments. As another technology for the acquisition of digital 3D data of structures and environments, 3D reconstruction methods from digital images are also attracting a great deal of attention because of their flexibility.</p><p>The utilization of digital 3D data for various purposes still has many challenges, however, in terms of data processing. The extraction of accurate and meaningful information from the data is an especially important and difficult problem, and many studies on object and scene recognition are being conducted in many fields. How to acquire useful and high-quality digital 3D data of large-scale structures and environments is another problem to be solved for digital geometry processing to be widely used.</p><p>This special issue addresses the latest research advances in digital geometry processing for large-scale structures and environments. It covers a broad range of topics in geometry processing, including new technologies, systems, and reviews for 3D data acquisition, recognition, and modeling of ships, factories, plants, forests, river dikes, and urban environments.</p><p>The papers will help the readers explore and share their knowledge and experience in technologies and development techniques in this area. All papers were refereed through careful peer reviews. We would like to express our sincere appreciation to the authors for their excellent submissions and to the reviewers for their invaluable efforts in producing this special issue.</p>", "Keywords": "", "DOI": "10.20965/ijat.2021.p0257", "PubYear": 2021, "Volume": "15", "Issue": "3", "JournalId": 8593, "JournalTitle": "International Journal of Automation Technology", "ISSN": "1881-7629", "EISSN": "1883-8022", "Authors": [{"AuthorId": 1, "Name": "Hi<PERSON>ki <PERSON>", "Affiliation": "Hokkaido University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nihon University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nippon Institute of Technology"}], "References": []}, {"ArticleId": 87898076, "Title": "Extraction of Guardrails from MMS Data Using Convolutional Neural Network", "Abstract": "<p>Mobile mapping systems can capture point clouds and digital images of roadside objects. Such data are useful for maintenance, asset management, and 3D map creation. In this paper, we discuss methods for extracting guardrails that separate roadways and walkways. Since there are various shape patterns for guardrails in Japan, flexible methods are required for extracting them. We propose a new extraction method based on point processing and a convolutional neural network (CNN). In our method, point clouds and images are segmented into small fragments, and their features are extracted using CNNs for images and point clouds. Then, features from images and point clouds are combined and investigated using whether they are guardrails or not. Based on our experiments, our method could extract guardrails from point clouds with a high success rate.</p>", "Keywords": "point processing;guardrail;mobile mapping system;convolutional neural network;terrestrial laser scanner", "DOI": "10.20965/ijat.2021.p0258", "PubYear": 2021, "Volume": "15", "Issue": "3", "JournalId": 8593, "JournalTitle": "International Journal of Automation Technology", "ISSN": "1881-7629", "EISSN": "1883-8022", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}], "References": []}, {"ArticleId": 87898078, "Title": "Classification of Grass and Forb Species on Riverdike Using UAV LiDAR-Based Structural Indices", "Abstract": "<p> Herbaceous vegetation on riverdikes plays an important role in preventing soil erosion, which, otherwise, may lead to the collapse of riverdikes and consequently, severe flooding. It is crucial for managers to keep suitable vegetation conditions, which include native grass species such as Imperata cylindrica , and to secure visibility of riverdikes for inspection. If managers can efficiently find where suitable grass and unsuitable forb species grow on vast riverdikes, it would help in vegetation management on riverdikes. Classification and quantification of herbaceous vegetation is a challenging task. It requires spatial resolution and accuracy high enough to recognize small, complex-shaped vegetation on riverdikes. Recent developments in unmanned aerial vehicle (UAV) technology combined with light detection and ranging (LiDAR) may offer the solution, since it can provide highly accurate, high-spatial resolution, and denser data than conventional systems. This paper aims to develop a model to classify grass and forb species using UAV LiDAR data alone. A combination of UAV LiDAR-based structural indices, V-bottom (presence of vegetation up to 50 cm from the ground) and V-middle (presence of vegetation 50–100 cm from the ground), was tested and validated in 94 plots owing to its ability to classify grass and forb species on riverdikes. The proposed method successfully classified the “upright” grass species and “falling” grass species / forb species with an accuracy of approximately 83%. Managers can efficiently prioritize the inspection areas on the riverdikes by using this method. The method is versatile and adjustable in other grassland environments. </p>", "Keywords": "UAV;LiDAR;herbaceous vegetation;grass;riverdike", "DOI": "10.20965/ijat.2021.p0268", "PubYear": 2021, "Volume": "15", "Issue": "3", "JournalId": 8593, "JournalTitle": "International Journal of Automation Technology", "ISSN": "1881-7629", "EISSN": "1883-8022", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Agricultural and Life Sciences, The University of Tokyo"}, {"AuthorId": 2, "Name": "Tomoyo F. <PERSON>", "Affiliation": "Field Studies Institute for Environmental Education, Tokyo Gakugei University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Agriculture, Tokyo University of Agriculture"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Environmental Studies, Tokyo City University"}], "References": []}, {"ArticleId": 87898079, "Title": "Research on Identification of Road Features from Point Cloud Data Using Deep Learning", "Abstract": "<p>Laser measurement technology has progressed significantly in recent years, and diverse methods have been developed to measure three-dimensional (3D) objects within environmental spaces in the form of point cloud data. Although such point cloud data are expected to be used in a variety of applications, such data do not possess information on the specific features represented by the points, making it necessary to manually select the target features. Therefore, the identification of road features is essential for the efficient management of point cloud data. As a technology for identifying features from the point cloud data of road spaces, in this research, we propose a method for automatically dividing point cloud data into units of features and identifying features from projected images with added depth information. We experimentally verified that the proposed method accurately identifies and extracts such features.</p>", "Keywords": "i-Construction;road feature;point cloud data;deep learning;feature identification", "DOI": "10.20965/ijat.2021.p0274", "PubYear": 2021, "Volume": "15", "Issue": "3", "JournalId": 8593, "JournalTitle": "International Journal of Automation Technology", "ISSN": "1881-7629", "EISSN": "1883-8022", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Organization for Research and Development of Innovative Science and Technology, Kansai University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Business Administration, Setsunan University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology and Social Sciences, Osaka University of Economics"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Informatics, Kansai University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Informatics, Kansai University"}], "References": [{"Title": "完成平面図を用いた道路面地物の点群データの抽出に関する研究", "Authors": "Kenji NAKAMURA; Yoshinori TSUKADA; Shigenori TANAKA", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "616", "JournalTitle": "Journal of Japan Society for Fuzzy Theory and Intelligent Informatics"}, {"Title": "橋梁の点群データを用いた深層学習による部位識別に関する研究", "Authors": "Yoshinori TSUKADA; Satoshi KUBOTA; Shigenori TANAKA", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "627", "JournalTitle": "Journal of Japan Society for Fuzzy Theory and Intelligent Informatics"}]}, {"ArticleId": 87898082, "Title": "Development of Support System for Ship-Hull Plate Forming Using Laser Scanner", "Abstract": "<p>In this study, we developed a new system that outputs the additional press work procedures necessary to obtain the desired ship-hull surface. This study is unique in terms of determining the additional press work procedures required according to the current plate shape at any work stage by measuring the plate shape using a laser scanner. In the proposed method, a B-spline surface is constructed from a point cloud measured using a laser scanner, and the current plate shape is analyzed based on differential geometry. Additional press lines are estimated based on the difference in the normal curvature along the lines of curvature between the designed target surface and the current surface. We demonstrated the effectiveness of our proposed method through experiments at a shipyard. The proposed system may be used to enhance the efficiency of press work and is expected to be an effective tool for training beginners in the future.</p>", "Keywords": "plate bending;point cloud data;laser scanner;lines of curvature;shipbuilding", "DOI": "10.20965/ijat.2021.p0290", "PubYear": 2021, "Volume": "15", "Issue": "3", "JournalId": 8593, "JournalTitle": "International Journal of Automation Technology", "ISSN": "1881-7629", "EISSN": "1883-8022", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Maritime Research Institute (NMRI)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Maritime Research Institute (NMRI)"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Maritime Research Institute (NMRI)"}], "References": []}, {"ArticleId": 87898086, "Title": "Assessment of voltage stability based on power transfer stability index using computational intelligence models", "Abstract": "<span>In this paper, the importance of voltage stability is explained, which is a great problem in the EPS. The estimation of VS is made a priority so as to make the power system stable and prevent it from reaching voltage collapse. The power transfer stability index (PTSI) is used as a predictor utilized in a PSN to detect the instability of voltages on weakened buses. A PSI is used to obtain a voltage assessment of the PSNs. Two hybrid algorithms are developed. The (CA-NN) and the (PSO-NN). After developing algorithms, they are compared with the actual values of PTSI NR method. The algorithms installed on the 24 bus Iraqi PS. The actual values of PTSI are the targets needed. They are obtained from the NR algorithm when the input data is V<sub>i</sub>, δ<sub>i</sub>, P<sub>d</sub>, Q<sub>d</sub> for the algorithm. The results indicate that a weak bus that approaches voltage collapse and all results were approximately the same. There is a slight difference with the actual results and demonstrated classical methods are slower and less accurate than the hybrid algorithms. It also demonstrates the validation and effectiveness of algorithms (CA-NN, and PSO-NN) for assessing voltage-prioritizing </span><span>algorithms</span><span> (CA-NN). The MATLAB utilized to obtain most of the results.</span>", "Keywords": "culture algorithm;ANN;hybrid algorithm;PSO;system stability;voltage assessment", "DOI": "10.11591/ijece.v11i4.pp2790-2797", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Diyala"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Diyala University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Diyala"}], "References": []}, {"ArticleId": 87898087, "Title": "Numerical analysis of the photovoltaic system inspection with active cooling", "Abstract": "<span>The use of solar energy may replace the present fossil fuel or gas to produce electricity. The goal of this study is to set up a simulation model to survey the performance of a photovoltaic thermal system (PV/T) based on the computational fluid dynamics (CFD) method. Ansys fluent software has been used for the simulation procedure. The electrical panel output and its efficiency were investigated numerically. In addition, the effect of variations in absorbed radiation on inlet fluid and absorber panel temperature on the system performance was investigated. The study was conducted for three cases, in a first case, where there is no refrigerant in the system and in the latter case, at constant fluid rate of the pump, whereas the third case with optimal pump operation. The numerical findings obtained from CFD simulators have been compared with the test records of the experimental results of the literature. The two results have a good agreement. From the obtained results, it can be noted that the system shows a good improvement for the electric net efficiency level of 3.52% with a lower reduction of the thermal system efficiency of 1.96% in comparison to the system when using the constantly high flow rate.</span>", "Keywords": "active cooling;CFD;photovoltaic;thermal system", "DOI": "10.11591/ijece.v11i4.pp2779-2789", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Tikrit university"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tikrit university"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tikrit university"}], "References": []}, {"ArticleId": 87898088, "Title": "A new technology on translating Indonesian spoken language into Indonesian sign language system", "Abstract": "<p>People with hearing disabilities are those who are unable to hear, resulted in their disability to communicate using spoken language. The solution offered in this research is by creating a one way translation technology to interpret spoken language to Indonesian sign language system (SIBI). The mechanism applied here is by catching the sentences (audio) spoken by common society to be converted to texts, by using speech recognition. The texts are then processed in text processing to select the input texts. The next stage is stemming the texts into prefixes, basic words, and suffixes. Each words are then being indexed and matched to SIBI. Afterwards, the system will arrange the words into SIBI sentences based on the original sentences, so that the people with hearing disabilities can get the information contained within the spoken language. This technology success rate were tested using Confusion Matrix, which resulted in precision value of 76%, accuracy value of 78%, and recall value of 79%. This technology has been tested in SMP-LB Karya Mulya on the 7th grader students with the total of 9 students. From the test, it is obtained that 86% of students stated that this technology runs very well.</p>", "Keywords": "hearing disabilities;SIBI;speech recognition;spoken language;translator", "DOI": "10.11591/ijece.v11i4.pp3338-3346", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Udayana University"}, {"AuthorId": 2, "Name": "I Ketut G<PERSON>", "Affiliation": "Udayana University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Udayana University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Udayana University"}], "References": []}, {"ArticleId": 87898091, "Title": "Companies’ perception toward manufacturing execution systems", "Abstract": "<p>The use of information systems in manufacturing sector is very crucial to reach a high level of operational excellence and improve companies’ competitiveness. The use of such systems will definitely increase in the upcoming years, considering the digitalization strategies. Manufacturing execution systems gained a lot of attention in recent years due to showcased benefits in production management operations. Companies that adopted such systems witnessed an increase in process efficiency and enhancements with regards to cost savings and products quality. This paper seeks to analyze what makes the usage of manufacturing execution systems successful among manufacturing companies. We analyzed how the integration capabilities of such systems with other business applications and the company profile impact their usage and consequently the perceived benefits. A case study was conducted with 51 manufacturing companies and data were analyzed using partial least square structural equation modeling technique. The results confirmed the positive and significant impact of the company profile and solution integration capabilities on system usage. In addition, a ranking of solution modules importance for companies was also provided.</p>", "Keywords": "horizontal integration;industry 4.0;MES modules;PLS-SEM", "DOI": "10.11591/ijece.v11i4.pp3347-3355", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Abdelmalek Essaadi University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Abdelmalek Essaadi University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Senior Data Scientist Consultant"}], "References": []}, {"ArticleId": 87898095, "Title": "Introduction of All-Around 3D Modeling Methods for Investigation of Plants", "Abstract": "<p>Digital image phenotyping has become popular in plant research. Plants are complex in shape, and occlusion can often occur. Three-dimensional (3D) data are expected to measure the morphological traits of plants with higher accuracy. Plants have organs with flat and/or narrow shapes and similar component structures are repeated. Therefore, it is difficult to construct an accurate 3D model by applying methods developed for industrial materials and architecture. Here, we review noncontact and all-around 3D modeling and configuration of camera systems to measure the morphological traits of plants in terms of system composition, accuracy, cost, and usability. Typical noncontact 3D measurement methods can be roughly classified into active and passive methods. We describe their advantages and disadvantages. Structure-from-motion/multi-view stereo (SfM/MVS), a passive method, is the most frequently used measurement method for plants. It is described in terms of “forward intersection” and “backward resection.” We recently developed a novel SfM/MVS approach by mixing the forward and backward methods, and we provide a brief overview of our approach in this paper. While various fields are adopting 3D model construction, nonexpert users struggle to use them and end up selecting inadequate methods, which lead to model failure. We hope that this review will help users who are considering starting to construct and measure 3D models.</p>", "Keywords": "three-dimensional modeling;plant;photogrammetry;SfM/MVS;active and passive methods", "DOI": "10.20965/ijat.2021.p0301", "PubYear": 2021, "Volume": "15", "Issue": "3", "JournalId": 8593, "JournalTitle": "International Journal of Automation Technology", "ISSN": "1881-7629", "EISSN": "1883-8022", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Center for Agricultural Information Technology, National Agriculture and Food Research Organization;R&D Initiative, Chuo University;Kazusa DNA Research Institute"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kazusa DNA Research Institute"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kazusa DNA Research Institute"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kazusa DNA Research Institute"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kazusa DNA Research Institute"}], "References": []}, {"ArticleId": 87898127, "Title": "Maximum Reaction-Wheel Array Torque/Momentum Envelopes for General Configurations", "Abstract": "", "Keywords": "Reaction Wheels; Control Moment Gyroscope; Satellites; MATLAB; Numerical Algorithms; Manufacturing Cost; Spacecraft System; Deimos; Earth", "DOI": "10.2514/1.G005570", "PubYear": 2021, "Volume": "44", "Issue": "6", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Korea Advanced Institute of Science and Technology, Daejeon 34141, Republic of Korea"}], "References": []}, {"ArticleId": 87898231, "Title": "Machine Learning Based Crowd Behaviour Analysis and Prediction", "Abstract": "Crowd behaviour has been widely known to have the ability to forecast the events a crowd could create. Crowd management can become extremely efficient if situations such as riots, mob lynching, traffic jams, accidents, stampede, etc. could be predicted beforehand. To this end many researchers have made their contributions in the past and there is still immense work being carried out currently. All the researches worked with different algorithms and techniques to analyze images or videos of crowd scenes for counting the number of people in the crowd, predicting the behaviour of the crowd and classifying an image or video as normal or abnormal crowd event. This paper, hence, is directed towards underlining the some of the major researches in this field, the approaches and algorithms adopted by them and their comparisons. Overall, this paper reviews the past researches and presents a summary of the techniques and strategies employed. At the end of this paper is the future scope of work possible in the field of crowd behaviour analysis, prediction and crowd counting.", "Keywords": "", "DOI": "10.46610/JONSCN.2021.v07i01.004", "PubYear": 2021, "Volume": "7", "Issue": "1", "JournalId": 77122, "JournalTitle": "Journal of Network Security Computer Networks", "ISSN": "", "EISSN": "2581-639X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87898545, "Title": "Trusting Automation: Designing for Responsivity and Resilience", "Abstract": "Objective <p>This paper reviews recent articles related to human trust in automation to guide research and design for increasingly capable automation in complex work environments.</p> Background <p>Two recent trends—the development of increasingly capable automation and the flattening of organizational hierarchies—suggest a reframing of trust in automation is needed.</p> Method <p>Many publications related to human trust and human–automation interaction were integrated in this narrative literature review.</p> Results <p>Much research has focused on calibrating human trust to promote appropriate reliance on automation. This approach neglects relational aspects of increasingly capable automation and system-level outcomes, such as cooperation and resilience. To address these limitations, we adopt a relational framing of trust based on the decision situation, semiotics, interaction sequence, and strategy. This relational framework stresses that the goal is not to maximize trust, or to even calibrate trust, but to support a process of trusting through automation responsivity.</p> Conclusion <p>This framing clarifies why future work on trust in automation should consider not just individual characteristics and how automation influences people, but also how people can influence automation and how interdependent interactions affect trusting automation. In these new technological and organizational contexts that shift human operators to co-operators of automation, automation responsivity and the ability to resolve conflicting goals may be more relevant than reliability and reliance for advancing system design.</p> Application <p>A conceptual model comprising four concepts—situation, semiotics, strategy, and sequence—can guide future trust research and design for automation responsivity and more resilient human–automation systems.</p>", "Keywords": "adaptive automation;autonomy;human–automation interaction;intelligent agents;intersubjectivity;trust", "DOI": "10.1177/00187208211009995", "PubYear": 2023, "Volume": "65", "Issue": "1", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Arizona State University, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Wisconsin Madison, USA"}], "References": [{"Title": "Moving Into the Loop: An Investigation of Drivers’ Steering Behavior in Highly Automated Vehicles", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "4", "Page": "671", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Exploring Trust in Self-Driving Vehicles Through Text Analysis", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "2", "Page": "260", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Towards a Theory of Longitudinal Trust Calibration in Human–Robot Teams", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Mal<PERSON> F<PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "2", "Page": "459", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Human-Centered Artificial Intelligence: Three Fresh Ideas", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "109", "JournalTitle": "AIS Transactions on Human-Computer Interaction"}]}, {"ArticleId": 87898644, "Title": "An improved multi-objective evolutionary algorithm for computation offloading in the multi-cloudlet environment", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11704-020-9346-z", "PubYear": 2021, "Volume": "15", "Issue": "5", "JournalId": 4733, "JournalTitle": "Frontiers of Computer Science", "ISSN": "2095-2228", "EISSN": "2095-2236", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Automation and Electrical Engineering, University of Science and Technology Beijing, Beijing, China;Key Laboratory of Knowledge Automation for Industrial Processes of Ministry of Education, University of Science and Technology Beijing, Beijing, China"}, {"AuthorId": 2, "Name": "Yuanyuan Du", "Affiliation": "Automation and Electrical Engineering, University of Science and Technology Beijing, Beijing, China"}], "References": []}, {"ArticleId": 87898645, "Title": "ABDKS: attribute-based encryption with dynamic keyword search in fog computing", "Abstract": "<p>Attribute-based encryption with keyword search (ABKS) achieves both fine-grained access control and keyword search. However, in the previous ABKS schemes, the search algorithm requires that each keyword to be identical between the target keyword set and the ciphertext keyword set, otherwise the algorithm does not output any search result, which is not conducive to use. Moreover, the previous ABKS schemes are vulnerable to what we call a peer-decryption attack , that is, the ciphertext may be eavesdropped and decrypted by an adversary who has sufficient authorities but no information about the ciphertext keywords.</p><p>In this paper, we provide a new system in fog computing, the ciphertext-policy attribute-based encryption with dynamic keyword search (ABDKS). In ABDKS, the search algorithm requires only one keyword to be identical between the two keyword sets and outputs the corresponding correlation which reflects the number of the same keywords in those two sets. In addition, our ABDKS is resistant to peer-decryption attack, since the decryption requires not only sufficient authority but also at least one keyword of the ciphertext. Beyond that, the ABDKS shifts most computational overheads from resource constrained users to fog nodes. The security analysis shows that the ABDKS can resist Chosen-Plaintext Attack (CPA) and Chosen-Keyword Attack (CKA).</p>", "Keywords": "access control; attribute-based encryption; keyword search; fog computing; outsourcing", "DOI": "10.1007/s11704-020-9472-7", "PubYear": 2021, "Volume": "15", "Issue": "5", "JournalId": 4733, "JournalTitle": "Frontiers of Computer Science", "ISSN": "2095-2228", "EISSN": "2095-2236", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Shandong University, Jinan, China;Key Laboratory of Cryptologic Technology and Information Security, Ministry of Education, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Fudan University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics, Shandong University, Jinan, China;Key Laboratory of Cryptologic Technology and Information Security, Ministry of Education, Jinan, China"}], "References": [{"Title": "Full Verifiability for Outsourced Decryption in Attribute Based Encryption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "3", "Page": "478", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Secure Channel Free Certificate-Based Searchable Encryption Withstanding Outside and Inside Keyword Guessing Attacks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "6", "Page": "2041", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 87898668, "Title": "A penalty scheme and policy iteration for nonlocal HJB variational inequalities with monotone nonlinearities", "Abstract": "We propose a class of numerical schemes for nonlocal HJB variational inequalities (HJBVIs) with monotone nonlinearities arising from mixed optimal stopping and control of processes with infinite activity jumps, where the objective is specified by a monotone recursive preference. The solution and free boundary of the HJBVI are constructed from a sequence of penalized equations, for which the penalization error is estimated. The penalized equation is then discretized by a class of semi-implicit monotone approximations. We present a novel analysis technique for the well-posedness of the discrete equation, and demonstrate the convergence of the scheme, which subsequently gives a constructive proof for the existence of a solution to the penalized equation and variational inequality. We further propose an efficient iterative algorithm with local superlinear convergence for solving the discrete equation. Numerical experiments are presented for an optimal investment problem under ambiguity and a two-dimensional recursive consumption-portfolio allocation problem.", "Keywords": "HJB variational inequalities ; Non-Lipschitz <PERSON> preferences ; Monotone nonlinearity ; Penalization ; Semi-smooth Newton methods ; Optimal investment", "DOI": "10.1016/j.camwa.2021.04.011", "PubYear": 2021, "Volume": "93", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mathematical Institute, University of Oxford, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mathematical Institute, University of Oxford, United Kingdom;Corresponding author"}], "References": []}, {"ArticleId": 87898943, "Title": "A survey on generative adversarial network-based text-to-image synthesis", "Abstract": "The task of text-to-image synthesis is a new challenge in the field of image synthesis. In the earlier research, the task of text-to-image synthesis is mainly to achieve the alignment of words and images by the way of retrieval based on the sentences or keywords. With the development of deep learning, especially the application of deep generative models in image synthesis, image synthesis achieves promising progress. The Generative adversarial networks (GANs) are one of the most significant generative models, and GANs have been successfully applied in computer vision, natural language processing and so on. In this paper, we review and summarize the recent research in GANs-based text-to-image synthesis, and provide a summary of the development of classic and advanced models. The input of the GANs-based text-to-image synthesis is not only the general text description as earlier studies, also includes scene layout and dialog text. The typical structure of each categories is elaborated. The general text-based image synthesis is the most commonly in the text-to-image synthesis, and it is subdivided into three groups based on the improvements of text information utilization, network structure and output control conditions. Through the survey, the detailed and logical overview of the evolution of GANs-based text-to-image synthesis is presented. Finally, the challenged problems and the future development of text-to-image synthesis are discussed.", "Keywords": "Deep learning ; Generative adversarial network (GAN) ; Text-to-image synthesis ; Scene layout", "DOI": "10.1016/j.neucom.2021.04.069", "PubYear": 2021, "Volume": "451", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical, Electrical & Information Engineering, Shandong University, Weihai 264209, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical, Electrical & Information Engineering, Shandong University, Weihai 264209, China"}, {"AuthorId": 3, "Name": "Qingyang Xu", "Affiliation": "School of Mechanical, Electrical & Information Engineering, Shandong University, Weihai 264209, China;Corresponding author"}], "References": [{"Title": "How Generative Adversarial Networks and Their Variants Work", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Semantically consistent text to fashion image synthesis with an enhanced attentional generative adversarial network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "22", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Layout2image: Image Generation from Layout", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "10-11", "Page": "2418", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 87898958, "Title": "Research Librarians' Experiences of Research Data Management Activities at an Academic Library in a Developing Country", "Abstract": "<p>University libraries have archaeologically augmented scientific research by collecting, organizing, maintaining, and availing research materials for access. Researchers reckon that with the expertise acquired from conventional cataloging, classification, and indexing coupled with that attained in the development, along with the maintenance of institutional repositories, it is only rational that libraries take a dominant and central role in research data management and further their capacity as curators. Accordingly, University libraries are expected to assemble capabilities, to manage and provide research data for sharing and reusing efficiently. This study examined research librarians’ experiences of RDM activities at the UON Library to recommend measures to enhance managing, sharing and reusing research data. The study was informed by the DCC Curation lifecycle model and the Community Capability Model Framework (CCMF) that enabled the Investigator to purposively capture qualitative data from a sample of 5 research librarians at the UON Library. The data was analysed thematically to generate themes that enabled the Investigator to address the research problem. Though the UON Library had policies on research data, quality assurance and intellectual property, study findings evidenced no explicit policies to guide each stage of data curation and capabilities. There were also inadequacies in skills and training capability, technological infrastructure and collaborative partnerships. Overall, RDM faced challenges in all the examined capabilities. These challenges limited the managing, sharing, and reusing of research data. The study recommends developing an RDM unit within the UON Library to oversee the implementation of RDM activities by assembling all the needed capabilities (policy guidelines, skills and training, technological infrastructure and collaborative partnerships) to support data curation activities and enable efficient managing, sharing and reusing research data.</p>", "Keywords": "", "DOI": "10.2478/dim-2021-0002", "PubYear": 2021, "Volume": "5", "Issue": "4", "JournalId": 52282, "JournalTitle": "Data and Information Management", "ISSN": "", "EISSN": "2543-9251", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Management , Central China Normal University , Wuhan , China ."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Management , Central China Normal University , Wuhan , China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Education , Kenyatta University , Nairobi , Kenya"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Library and Information Services , University of Nairobi , Nairobi , Kenya"}], "References": []}, {"ArticleId": 87898980, "Title": "Optimized cycle basis in volume integral formulations for large scale eddy-current problems", "Abstract": "We present a Volume Integral formulation for the solution of large scale eddy-current problems coupled with low-rank approximation techniques. Two alternative approaches are introduced to map the problem unknowns into a subset of grid elements forming a base of global or mixed (global and local) cycles, respectively, and guarantee the well-posedness of the problem both in simply and multiply connected domains. The paper shows that the adoption of mixed cycles is computationally more efficient than global ones. In particular, integral formulations based on global cycles cannot be safely coupled with low-rank approximation techniques, which, however, are crucial to increase the size of the largest solvable problem, like the ones involving conducting structures in magnetic confinement fusion devices. The aim of this paper is to demonstrate how such bottleneck can be overcome by considering local and global cycles differently, on the basis of the cohomology theory. An improved, efficient, and robust algorithm for computing a base of global cycles is described in detail. In particular, the presented algorithm is able to almost minimize the cohomology basis length, i.e. the number of mesh edges forming such a basis, in order to allow an efficient solution of large scale problems . Furthermore, a novel and general method to handle global and local cycles together, in the context of low-rank approximated matrices, is shown to be efficient for the solution of large scale eddy-current problems in multiply connected domains. Along the manuscript, pseudo-codes are given, which clarify the proposed methods and help to implement them by Volume Integral Equation practitioners. Introduction Integral formulations have shown to be suitable alternatives to usual Finite Element Methods (FEM) for the solution of eddy-current problems involving complex 3D conducting structures, especially when embedded in large non-conducting regions [1], [2], [3]. An example is represented by magnetic confinement fusion (MCF) devices, where large metallic structures can include a lot of small and tailored components (e.g. the ports for diagnostics, heating and current drive and vacuum systems, the insulations gaps, the divertor plates, etc.). The study of this kind of devices requires both high solution accuracy and high computational efficiency (in terms of solution time and memory) of the formulation, in order to solve large scale problems on standard workstations [4]. In this regard, we compare two Volume Integral approaches, which rely on a discrete reformulation of Maxwell's equations over a pair of interlocked grids [5], [6] and start from a common discrete equation referred as Electric Field Integral Equation (EFIE). Despite both approaches give the same numerical results, the different techniques adopted to impose the solenoidality condition of the current density field lead to a different mapping of the problem unknowns on a basis of grid cycles. The former, called L -approach in this paper, strictly relies on a circuit interpretation of the eddy-current problem, as typical of Partial Element Equivalent Circuit (PEEC) methods [7], [8], [9], [10]. The problem is solved by the direct application of the loop current method of circuit theory, without the introduction of any vector potential, hence the well-posedness is guaranteed both in simply and multiply connected domains. The latter, called C -approach, solves the eddy-current problem by the common definition of the electric vector potential [11]. The well-posedness of the problem in multiply connected domains is addressed with cohomology theory [12], resulting into a topological pre-processing, required by the formulation, to identify a basis of the first cohomology group generators of the conductor's boundary [13]. The aim of the paper is to investigate which approach performs better in the solution of large scale problems, with a particular focus on MCF devices. The coupling of volume integral formulations with low-rank approximation techniques has already demonstrated to be an efficient computational method to increase the size of the largest solvable problem on standard workstations [4], [14], [15]. However, the integral approach has to be carefully formulated in order to obtain an efficient, but at the same time accurate, approximation of the dense matrices arising from the chosen approach. As regards the C -approach, starting from the algorithm described in [16], a new robust algorithm is proposed for the computation of the first group cohomology basis of the conductor's boundary, which ideally matches with the adopted low-rank approximation method. The obtained optimal cohomology basis allows for reducing the computational costs of system assembling and solution, without losing the physical properties introduced by the cohomology generators. Moreover, we also propose a clustering technique which allows for efficiently applying low-rank compression techniques in the C -approach. The algorithm is implemented on the basis of graph theory and of the duality between the interlocked grids. Hence, without losing any formality, it can be easily followed by those unfamiliar with homology and cohomology theories [12]. The algorithm is robust and efficient, since it requires algebraic operations with integer numbers only, avoiding any loss of numerical precision arising from the use of floating point arithmetic. Moreover, in contrast with previous approaches such as [17], it computes an optimal basis, in the sense that, first, it almost minimizes the basis length, i.e. the number of mesh edges which form such a basis, allowing a significant speed-up in the system assembling, which otherwise could represent a bottleneck in the problem solution. Moreover, the computed basis provides a full rank system to be solved, with a time consumption of a few seconds for the basis computation in meshes of tens of thousands of elements, which is essentially negligible if compared to the time required by the system assembly and solution. On the contrary, the adoption of a global basis in the L -approach does not allow an efficient and correct coupling with low-rank approximation techniques, thus limiting the applicability of the L -approach to large scale problems. The paper is organized as follows. In Section 2, the equations of the eddy-current problem are derived for the two presented approaches. In Section 3 and Section 4, the two approaches are explained in detail. Section 5 rigorously describes the implemented algorithm for computing an optimal basis of the first cohomology group, while Section 6 deals with the coupling with low-rank approximation techniques. Numerical results are presented in Section 7 as regards the numerical accuracy and the computational performances of the approaches, and in Section 8 regarding the computational costs of the cohomology basis computation. Finally, in Section 9, conclusions are drawn. Figures Example of dual graph tree-cotree decomposition. (a) and (b) show the tree and cotree edges of G˜, once removed the edges e˜j=D(fj):fj∈∂K, respectively. In (c) one of the independent cycle is shown (g... Non-zero entries (nz) in a 13034×6599 G˜′ matrix computed without any G˜ reordering (a) or using ndmetis (b). L-approach. Example of cohomology generators for a solid torus K. (a) The support [16] of a representative t1 of the H2(K,∂K) generator. It can be thought as a thinned unit current that flows around the torus. In... C-approach. Computing relevant minimal length cohomology generators H1(∂K). Show all figures Section snippets Volume Integral formulation Let Ω ⊂ R 3 be a conducting domain with electric resistivity ρ ( r ) , with r ∈ Ω . Assuming a magneto quasi-static (MQS) approximation of the electromagnetic fields, the following set of equations holds for every point r ∈ Ω : ∇ × e = − i ω b ∇ × h = j ∇ ⋅ b = 0 ∇ ⋅ j = 0 e = ρ j , where ω is the angular frequency, j is the eddy-current density, h and b are the magnetic field and the magnetic flux density, respectively. Taking into account the solenoidality condition of the magnetic flux density (3), Faraday's law (1) is rewritten as ∇ Loop current approach The loop current approach, from now on L -approach, consists in the solution of the electric circuit representing the eddy-current problem on K by means of the loop current method of circuit theory [24]. In this method, one constructs and solves a system of equations in which all the unknowns are loop currents , which may be defined as the currents that flow in a cycle basis of the dual edge-dual node graph of K ˜ , i.e. a maximal set of independent cycles. Kirchhoff's voltage laws (KVL) are Vector potential approach The vector potential approach, from now on C -approach, relies on the use of the electric vector potential [1], [11], [13]. Indeed, since j is a solenoidal field, it can be expressed as the curl of a vector potential in a simply-connected domain. Therefore the vector of currents I is written as I = C T , where the degrees of freedom (DoFs) where the vector T stores the integral of the electric vector potential on primal grid edges. More in general, if Ω is a multiply connected domain, I is redefined as I Computation of an optimal H 1 ( ∂ K ) cohomology basis The computation of a H 1 ( ∂ K ) basis has been discussed in several works and a general and fast method, described in [28], has been developed for the computation of the lazy first cohomology group generators. In this section, the algorithm described in [28] is reviewed and new steps are introduced in order to compute only the relevant generators of the first cohomology group H 1 of the boundary ∂ K . Indeed, the presented procedure allows for computing a matrix H which, once pre-multiplied by C , Low-rank approximation of dense matrices As a common drawback of integral methods, both the presented approaches end up with dense algebraic systems whose storage and assembling cost grow with N 2 , where N is the number of DoFs. Moreover, the solution of (24) or (29) by a direct solver, e.g. using an LU factorization of the system matrix, has a O ( N 3 ) complexity. Nevertheless, coupling integral formulations with low-rank approximation techniques based on hierarchical-matrix representations is an efficient solution to increase the size Numerical examples In this section we test the accuracy and the computational performances of the two discussed formulations on the basis of two numerical examples. First, the TEAM Workshop Problem 7 [42] is chosen as benchmark case for both formulations. In the second example we consider a more complex device consisting of a typical vacuum vessel of a MCF device. While in the former case no low-rank approximation has been implemented, in the latter, because of the large size of the problem, we tested the H 1 ( ∂ K ) basis length sensitivity to basepoints number The last section of this work aims at defining a rule of thumb in choosing the basepoints number in order to compute an optimal first group cohomology generator basis H 1 ( ∂ K ) . The objective of this quantification is to reach a cohomology basis close the absolute minimal basis, but guaranteeing a reasonable computational cost for its evaluation. As a first example, we consider the simple geometry of a thick plate with four holes ( β 1 ( K ) = 4 ) and we compute the minimal length basis achievable starting Conclusions We have presented a Volume Integral (VI) formulation and compared two approaches which both rely on a cycle basis which ensures the solenoidality condition of the current density in simply and multiply connected domains. While the L -approach relies on global cycles only, the C -approach is based on local cycles and combines global cycles only when the domain is not simply connected. Consequently, the C -approach shows better performances both in the pre-processing and in the solution of large Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements This work has been carried out within the framework of the EUROfusion Consortium and has received funding from the Euratom research and training programme 2014-2018 and 2019-2020 under grant agreement No. 633053 . The views and opinions expressed herein do not necessarily reflect those of the European Commission. The authors would like to thank the University of Padova Strategic Research Infrastructure, Grant 2017, for allowing the access to the efficient computational infrastructure “CAPRI: References (46) P. Bettini et al. Fusion Eng. Des. (2017) W. Hackbusch et al. J. Comput. Appl. Math. (2000) P. Dłotko et al. Comput. Phys. Commun. (2013) L. Codecasa et al. J. Comput. Phys. (2010) P. Bettini et al. J. Comput. Phys. (2014) R. Albanese et al. Adv. Imaging Electron Phys. (1997) N. Balabanian et al. Electrical Network Theory (1983) F.P. Andriulli IEEE Trans. Antennas Propag. (2012) E. Tonti The Mathematical Structure of Classical and Relativistic Physics A General Classification Diagram (2013) R. Albanese et al. IEE Proc. A (1988) P. Giblin Graphs, Surfaces and Homology (2010) T. Bauernfeind et al. IEEE Trans. Magn. (2018) G. Rubinacci et al. IEEE Trans. Magn. (2010) A. Ruehli IEEE Trans. Microw. Theory Tech. (1974) T. Cormen et al. Graph Algorithms (2003) P. Bettini et al. IEEE Trans. Magn. (2017) R. Hiptmair et al. SIAM J. Comput. (2002) V. Cirimele et al. Energies (2019) Z. Arai Nonlinear Theory Appl., IEICE (2013) R. Torchio IEEE Trans. Antennas Propag. (2019) P. Bamberg et al. A Course in Mathematics for Students of Physics: Volume 2 (1988) P. Yla-Oijala et al. Prog. Electromagn. Res. (2014) P. Alotto et al. IEEE Trans. Magn. (2016) View more references Cited by (0) Recommended articles (6) Research article Reply Clinical Gastroenterology and Hepatology, 2021 Research article A new algorithm for electrostatic interactions in Monte Carlo simulations of charged particles Journal of Computational Physics, Volume 430, 2021, Article 110099 Show abstract To minimise systematic errors in Monte Carlo simulations of charged particles, long range electrostatic interactions have to be calculated accurately and efficiently. Standard approaches, such as Ewald summation or the naive application of the classical Fast Multipole Method, result in a cost per Metropolis-Hastings step which grows in proportion to some positive power of the number of particles N in the system. This prohibitively large cost prevents accurate simulations of systems with a sizeable number of particles. Currently, large systems are often simulated by truncating the Coulomb potential which introduces uncontrollable systematic errors. In this paper we present a new multilevel method which reduces the computational complexity to O ( log ⁡ ( N ) ) per Metropolis-Hastings step, while maintaining errors which are comparable to direct Ewald summation. We show that compared to related previous work, our approach reduces the overall cost by better balancing time spent in the proposal- and acceptance- stages of each Metropolis-Hastings step. By simulating large systems with up to N = 10 5 particles we demonstrate that our implementation is competitive with state-of-the-art MC packages and allows the simulation of very large systems of charged particles with accurate electrostatics. Research article Metabolic profile of women with premature ovarian insufficiency compared with that of age-matched healthy controls Maturitas, Volume 148, 2021, pp. 33-39 Show abstract . To compare the metabolic profile of women with spontaneous premature ovarian insufficiency (POI) with that of age-matched healthy controls. . A cross-sectional case-control study was conducted using 1:1 matching by age. Women below the age of 40 with spontaneous POI who did not receive any medication ( n = 303) and age-matched healthy women ( n = 303) were included in this study. . Metabolic profiles, including serum levels of total cholesterol (TC), high-density lipoprotein cholesterol (HDL-C), low-density lipoprotein cholesterol (LDL-C), triglycerides (TG), glucose, uric acid, urea and creatinine, were compared between women with POI and controls. For women with POI, factors associated with the metabolic profile were analyzed. . Women with POI were more likely to exhibit increased serum levels of TG (β, 0.155; 95% CI, 0.086, 0.223) and glucose (0.067; 0.052, 0.083), decreased levels of HDL-C (-0.087; -0.123, -0.051), LDL-C (-0.047; -0.091, -0.003) and uric acid (-0.053; -0.090, -0.015), and impaired kidney function (urea [0.070; 0.033, 0.107]; creatinine [0.277; 0.256, 0.299]; eGFR [-0.234; -0.252, -0.216]) compared with controls after adjusting for age and BMI. BMI, parity, gravidity, FSH and E2 levels were independent factors associated with the metabolic profile of women with POI. . Women with POI exhibited abnormalities in lipid metabolism, glucose metabolism, and a decrease in kidney function. In women with POI, early detection and lifelong management of metabolic abnormalities are needed. Research article Issue Highlights Clinical Gastroenterology and Hepatology, Volume 19, Issue 5, 2021, pp. 859-860 Research article Clinical Gastroenterology and Hepatology, 2021 Research article Reply Clinical Gastroenterology and Hepatology, Volume 19, Issue 4, 2021, p. 857 <sup> ☆ </sup> The review of this paper was arranged by Prof. N.S. Scott. View full text © 2021 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.cpc.2021.108004", "PubYear": 2021, "Volume": "265", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centro Ricerche Fusione (CRF), University of Padova, 35127 Padova, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, University of Padova, 35131 Padova, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centro Ricerche Fusione (CRF), University of Padova, 35127 Padova, Italy;Department of Industrial Engineering, University of Padova, 35131 Padova, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Polytechnic Department of Engineering and Architecture, University of Udine, 33100 Udine, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, University of Padova, 35131 Padova, Italy"}], "References": []}, {"ArticleId": 87899069, "Title": "Applying Graph Theory and Mathematical-Computational Modelling to Study a Neurophysiological Circuit", "Abstract": "The aim of the present study is to contribute to the knowledge about the functioning of the neuronal circuits. We built a mathematical-computational model using graph theory for a complex neurophysiological circuit consisting of a reverberating neuronal circuit and a parallel neuronal circuit, which could be coupled. Implementing our model in C++ and applying neurophysiological values found in the literature, we studied the discharge pattern of the reverberant circuit and the parallel circuit separately for the same input signal pattern, examining the influence of the refractory period and the synaptic delay on the respective output signal patterns. Then, the same study was performed for the complete circuit, in which the two circuits were coupled, and the parallel circuit could then influence the functioning of the reverberant. The results showed that the refractory period played an important role in forming the pattern of the output spectrum of a reverberating circuit. The inhibitory action of the parallel circuit was able to regulate the reverberation frequency, suggesting that parallel circuits may be involved in the control of reverberation circuits related to motive activities underlying precision tasks and perhaps underlying neural work processes and immediate memories.", "Keywords": "Mathematical-Computational Modelling;Neurophysiological Circuit;Reverberating Circuit;Parallel Circuit", "DOI": "10.4236/ojmsi.2021.92011", "PubYear": 2021, "Volume": "9", "Issue": "2", "JournalId": 32695, "JournalTitle": "Open Journal of Modelling and Simulation", "ISSN": "2327-4018", "EISSN": "2327-4026", "Authors": [{"AuthorId": 1, "Name": "Camila de Andrade Kali<PERSON>", "Affiliation": "Postgraduate Program in Computational Sciences, Rio de Janeiro State University, Rio de Janeiro, Brazil"}, {"AuthorId": 2, "Name": "Maria Clícia Stelling de Castro", "Affiliation": "Postgraduate Program in Computational Sciences, Rio de Janeiro State University, Rio de Janeiro, Brazil .↑Department of Informatics and Computer Science, Rio de Janeiro State University, Rio de Janeiro, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Postgraduate Program in Computational Sciences, Rio de Janeiro State University, Rio de Janeiro, Brazil .↑Department of Applied Mathematics, Rio de Janeiro State University, Rio de Janeiro, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Postgraduate Program in Computational Sciences, Rio de Janeiro State University, Rio de Janeiro, Brazil .↑Department of Applied Mathematics, Rio de Janeiro State University, Rio de Janeiro, Brazil"}], "References": []}, {"ArticleId": 87899133, "Title": "Event-Based Time-Stamped Claim Logic", "Abstract": "The Event-Based Time-Stamped Claim Logic that we define in this paper allows one to reason about distributed time-stamped claims that can change through time by the occurrence of events. Such a logic is interesting for theoretical reasons, i.e., as a logic per se , but also because it can be applied in a number of different disciplines and application domains (e.g., history, crime forensics or cyber forensics) as it allows one to reason about a huge amount of pieces of evidence collected from different sources over time, where some of the pieces of evidence may be contradictory and some sources considered to be more trustworthy than others. We formalize the language and the semantics of the Event-Based Time-Stamped Claim Logic, provide a sound and complete Hilbert calculus, and consider some concrete examples. We also show that the validity problem for the logic is decidable by providing a tableau-like decision algorithm.", "Keywords": "Events ; Time-stamps ; Linear temporal logic ; Completeness ; Decidability", "DOI": "10.1016/j.jlamp.2021.100684", "PubYear": 2021, "Volume": "121", "Issue": "", "JournalId": 781, "JournalTitle": "Journal of Logical and Algebraic Methods in Programming", "ISSN": "2352-2208", "EISSN": "2352-2216", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Matemática, Instituto Superior Técnico, ULisboa, Lisboa, Portugal;Instituto de Telecomunicações, Lisboa, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Matemática, Instituto Superior Técnico, ULisboa, Lisboa, Portugal;Instituto de Telecomunicações, Lisboa, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Departamento de Matemática, Instituto Superior Técnico, ULisboa, Lisboa, Portugal;Instituto de Telecomunicações, Lisboa, Portugal;Corresponding author at: Departamento de Matemática, Instituto Superior Técnico, ULisboa, Lisboa, Portugal"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Informatics, King's College London, London, UK"}], "References": []}, {"ArticleId": 87899366, "Title": "Identification of Driving Safety Profiles in Vehicle to Vehicle Communication System Based on Vehicle OBD Information", "Abstract": "<p>Driver behavior is a determining factor in more than 90% of road accidents. Previous research regarding the relationship between speeding behavior and crashes suggests that drivers who engage in frequent and extreme speeding behavior are overinvolved in crashes. Consequently, there is a significant benefit in identifying drivers who engage in unsafe driving practices to enhance road safety. The proposed method uses continuously logged driving data to collect vehicle operation information, including vehicle speed, engine revolutions per minute (RPM), throttle position, and calculated engine load via the on-board diagnostics (OBD) interface. Then the proposed method makes use of severity stratification of acceleration to create a driving behavior classification model to determine whether the current driving behavior belongs to safe driving or not. The safe driving behavior is characterized by an acceleration value that ranges from about ±2 m/s2. The risk of collision starts from ±4 m/s2, which represents in this study the aggressive drivers. By measuring the in-vehicle accelerations, it is possible to categorize the driving behavior into four main classes based on real-time experiments: safe drivers, normal, aggressive, and dangerous drivers. Subsequently, the driver’s characteristics derived from the driver model are embedded into the advanced driver assistance systems. When the vehicle is in a risk situation, the system based on nRF24L01 + power amplifier/low noise amplifier PA/LNA, global positioning system GPS, and OBD-II passes a signal to the driver using a dedicated liquid-crystal display LCD and light signal. Experimental results show the correctness of the proposed driving behavior analysis method can achieve an average of 90% accuracy rate in various driving scenarios.</p>", "Keywords": "aggressive driving; vehicle-to-vehicle (V2V); acceleration; speed; GPS aggressive driving ; vehicle-to-vehicle (V2V) ; acceleration ; speed ; GPS", "DOI": "10.3390/info12050194", "PubYear": 2021, "Volume": "12", "Issue": "5", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Electrical and Electronic Engineering, Universiti Tun Hussein <PERSON>, <PERSON><PERSON> 86400, Malaysia;Department of Computer Engineering Techniques, Al-Mustaqbal University College, Babil 51001, Iraq"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Electrical and Electronic Engineering, Universiti Tun Hussein Onn <PERSON>, Parit Raja 86400, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Electronic Engineering, Universiti Tun Hussein Onn <PERSON>, Parit Raja 86400, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Electronic Engineering, Universiti Tun Hussein <PERSON>, <PERSON><PERSON> 86400, Malaysia;Department of Computer Engineering Techniques, Al-Mustaqbal University College, Babil 51001, Iraq"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Electronic Engineering, Universiti Tun Hussein <PERSON>, <PERSON><PERSON> 86400, Malaysia;Department of Computer Engineering Techniques, Al-Mustaqbal University College, Babil 51001, Iraq"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Science and Technology for Innovation, Yamaguchi University, Yamaguchi City 753-8511, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Science and Technology for Innovation, Yamaguchi University, Yamaguchi City 753-8511, Japan"}], "References": []}, {"ArticleId": 87899415, "Title": "A mixed adversarial adaptation network for intelligent fault diagnosis", "Abstract": "<p>Behind the brilliance of the deep diagnosis models, the issue of distribution discrepancy between source training data and target test data is being gradually concerned for catering to more practical and urgent diagnostic requirements. Consequently, advanced domain adaptation algorithms have been introduced to the field of fault diagnosis to address this problem. However, in performing domain adaptation, most existing diagnosis methods only focus on the minimization of marginal distribution divergences and do not consider conditional distribution differences at the same time. In this paper, we present a mixed adversarial adaptation network (MAAN) based intelligent framework for cross-domain fault diagnosis of machinery. In this approach, differences in marginal distribution and conditional distribution are reduced together by the adversarial learning strategy, moreover, a simple adaptive factor is also endowed to dynamically weigh the relative importance of two distributions. Extensive experiments on two kinds of mechanical equipment, i.e. planetary gearbox and rolling bearing, are built to validate the proposed method. Empirical evidence demonstrates that the proposed model outperforms popular deep learning and deep domain adaptation diagnosis methods.</p>", "Keywords": "Adversarial domain adaptation; Marginal distribution; Conditional distribution; Intelligent fault diagnosis", "DOI": "10.1007/s10845-021-01777-0", "PubYear": 2022, "Volume": "33", "Issue": "8", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Reliability and Systems Engineering, Beihang University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory for Manufacturing Systems Engineering, School of Mechanical Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Reliability and Systems Engineering, Beihang University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Manufacturing Systems Engineering, School of Mechanical Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Manufacturing Systems Engineering, School of Mechanical Engineering, Xi’an Jiaotong University, Xi’an, China"}], "References": [{"Title": "A renewable fusion fault diagnosis network for the variable speed conditions under unbalanced samples", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Xingxing Jiang", "PubYear": 2020, "Volume": "379", "Issue": "", "Page": "12", "JournalTitle": "Neurocomputing"}, {"Title": "Intelligent cross-machine fault diagnosis approach with deep auto-encoder and domain adaptation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "383", "Issue": "", "Page": "235", "JournalTitle": "Neurocomputing"}, {"Title": "Unsupervised rotating machinery fault diagnosis method based on integrated SAE–DBN and a binary processor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "8", "Page": "1899", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Wasserstein distance based deep adversarial transfer learning for intelligent fault diagnosis with unlabeled or insufficient labeled data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "409", "Issue": "", "Page": "35", "JournalTitle": "Neurocomputing"}, {"Title": "A comprehensive review on convolutional neural network in machine fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "417", "Issue": "", "Page": "36", "JournalTitle": "Neurocomputing"}, {"Title": "Bearing fault diagnosis base on multi-scale CNN and LSTM model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "4", "Page": "971", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A novel transfer learning fault diagnosis method based on Manifold Embedded Distribution Alignment with a little labeled data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "1", "Page": "151", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 87899441, "Title": "Correction to: Recent developments in photoacoustic imaging and sensing for nondestructive testing and evaluation", "Abstract": "An amendment to this paper has been published and can be accessed via the original article.", "Keywords": "", "DOI": "10.1186/s42492-021-00077-x", "PubYear": 2021, "Volume": "4", "Issue": "1", "JournalId": 4922, "JournalTitle": "Visual Computing for Industry, Biomedicine, and Art", "ISSN": "", "EISSN": "2524-4442", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Michigan-Shanghai Jiao Tong University Joint Institute, Shanghai Jiao Tong University, Shanghai, 200240, China.  . ;Engineering Research Center of Digital Medicine and Clinical Translation, Ministry of Education, Shanghai, 200030, China.  . ;State Key Laboratory of Advanced Optical Communication Systems and Networks, Shanghai Jiao Tong University, Shanghai, 200240, China.  ."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Precision Machinery and Precision Instrumentation, University of Science and Technology of China, Hefei, 230026, Anhui, China.  ."}], "References": [{"Title": "Recent developments in photoacoustic imaging and sensing for nondestructive testing and evaluation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "1", "Page": "6", "JournalTitle": "Visual Computing for Industry, Biomedicine, and Art"}]}, {"ArticleId": 87899459, "Title": "Deep Learning and its Application for Healthcare Delivery in Low and Middle Income Countries", "Abstract": "<p>As anyone who has witnessed firsthand knows, healthcare delivery in low-resource settings is fundamentally different from more affluent settings. Artificial Intelligence, including Machine Learning and more specifically Deep Learning, has made amazing advances over the past decade. Significant resources are now dedicated to problems in the field of medicine, but with the potential to further the digital divide by neglecting underserved areas and their specific context. In the general case, Deep Learning remains a complex technology requiring deep technical expertise. This paper explores advances within the narrower field of deep learning image analysis that reduces barriers to adoption and allows individuals with less specialized software skills to effectively employ these techniques. This enables a next wave of innovation, driven largely by problem domain expertise and the creative application of this technology to unaddressed concerns in LMIC settings. The paper also explores the central role of NGOs in problem identification, data acquisition and curation, and integration of new technologies into healthcare systems.</p>", "Keywords": "NGOs;artificial intelligence;deep learning;digital health;global health;machine learning;point of care diagnosis", "DOI": "10.3389/frai.2021.553987", "PubYear": 2021, "Volume": "4", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Harvard, MA, United States."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "D-tree International, Zanzibar, Tanzania."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Baylor College of Medicine, Houston, TX, United States."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "D-tree International, Lincoln, MA, United States."}], "References": [{"Title": "Fastai: A Layered API for Deep Learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "108", "JournalTitle": "Information"}]}, {"ArticleId": 87899677, "Title": "Who Can Find My Devices? Security and Privacy of Apple’s Crowd-Sourced Bluetooth Location Tracking System", "Abstract": "Abstract \n Overnight, Apple has turned its hundreds-of-million-device ecosystem into the world’s largest crowd-sourced location tracking network called o~ine finding (OF). OF leverages online finder devices to detect the presence of missing o~ine devices using Bluetooth and report an approximate location back to the owner via the Internet. While OF is not the first system of its kind, it is the first to commit to strong privacy goals. In particular, OF aims to ensure finder anonymity, prevent tracking of owner devices, and confidentiality of location reports. This paper presents the first comprehensive security and privacy analysis of OF. To this end, we recover the specifications of the closed-source OF protocols by means of reverse engineering. We experimentally show that unauthorized access to the location reports allows for accurate device tracking and retrieving a user’s top locations with an error in the order of 10 meters in urban areas. While we find that OF’s design achieves its privacy goals, we discover two distinct design and implementation flaws that can lead to a location correlation attack and unauthorized access to the location history of the past seven days, which could deanonymize users. Apple has partially addressed the issues following our responsible disclosure. Finally, we make our research artifacts publicly available.", "Keywords": "", "DOI": "10.2478/popets-2021-0045", "PubYear": 2021, "Volume": "2021", "Issue": "3", "JournalId": 30919, "JournalTitle": "Proceedings on Privacy Enhancing Technologies", "ISSN": "", "EISSN": "2299-0984", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Secure Mobile Networking Lab , Technical University of Darmstadt , Germany"}, {"AuthorId": 2, "Name": "Milan Stute", "Affiliation": "Secure Mobile Networking Lab , Technical University of Darmstadt , Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Secure Mobile Networking Lab , Technical University of Darmstadt , Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Secure Mobile Networking Lab , Technical University of Darmstadt , Germany"}], "References": [{"Title": "Discontinued Privacy: Personal Data Leaks in Apple Bluetooth-Low-Energy Continuity Protocols", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "1", "Page": "26", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}]}, {"ArticleId": 87899796, "Title": "Distribution-free double exponentially and homogeneously weighted moving average Lepage schemes with an application in monitoring exit rate", "Abstract": "Two new distribution-free Lepage-type statistical process monitoring schemes are proposed. One is the double exponentially weighted moving average Lepage scheme, and the other is the homogeneously weighted moving average Lepage scheme. For the past ten years, the distribution-free schemes for jointly monitoring both the location and scale parameters of a process draw an abundance of attention from the researchers. The Lepage statistic is a famous single statistic employed in the distribution-free joint monitoring schemes. The Lepage-type schemes have been widely explored, modified, and improved by researchers recently. The in-control robustness of the distribution-free schemes renders flexibility in its implementation, and they are now playing a pivotal role in the new era of intelligent monitoring. Also, the double exponentially and homogeneously weighted moving average schemes are widely explored in recent years. One of the new charting plans combines the distribution-free approach for joint monitoring and the notion of the double exponentially weighted moving average. Another proposed scheme blends the concepts of distribution-free simultaneous monitoring with the homogeneously weighted moving average. Implementation designs based on both the time-varying and steady-state control limits are considered. A homogeneously weighted moving average Lepage scheme with the time-varying control limit is better than its counterpart with the steady-state control limit in reducing the rate of early false alarms. In general, the double exponentially weighted moving average scheme performs well in detecting small to moderate shifts in the process. The proposed schemes are illustrated with an industrial application in monitoring the e-commerce activity, precisely the online shopper’s intention. Some concluding remarks and future research problems are presented.", "Keywords": "Distribution-free ; Double exponentially weighted moving average (DEWMA) ; Homogeneously weighted moving average (HWMA) ; Joint monitoring ; Lepage statistic", "DOI": "10.1016/j.cie.2021.107370", "PubYear": 2021, "Volume": "161", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physical and Mathematical Science, Faculty of Science, Universiti Tunku Abdul Rahman, Jalan Universiti, Bandar Barat, 31900 Kampar, Perak, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Production, Operations and Decision Sciences Area, XLRI – Xavier School of Management, XLRI Jamshedpur, Jharkhand 831001, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematical and Actuarial Sciences, Lee Kong Chian Faculty of Engineering and Science, Universiti Tunku <PERSON>, <PERSON><PERSON><PERSON>, 43000 Kajang, Selangor, Malaysia;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "Department of Physical and Mathematical Science, Faculty of Science, Universiti Tunku Abdul Rahman, Jalan Universiti, Bandar Barat, 31900 Kampar, Perak, Malaysia"}], "References": [{"Title": "A new distribution-free Phase-I procedure for bi-aspect monitoring based on the multi-sample Cucconi statistic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106760", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 87899797, "Title": "Robotic assistance for industrial sanding with a smooth approach to the surface and boundary constraints", "Abstract": "Surface treatment operations, such as sanding, deburring, finishing, grinding, polishing, etc. are progressively becoming more automated using robotic systems. However, previous research in this field used a completely automatic operation of the robot system or considered a low degree of human-robot interaction. Therefore, to overcome this issue, this work develops a truly synergistic cooperation between the human operator and the robot system to get the best from both. In particular, in the application developed in this work the human operator provides flexibility, guiding the tool of the robot system to treat arbitrary regions of the workpiece surface; while the robot system provides strength, accuracy and security, not only holding the tool and keeping the right tool orientation, but also guaranteeing a smooth approach to the workpiece and confining the tool within the allowed area close to the workpiece. Moreover, to add more flexibility to the proposed method, when the user is not guiding the robot tool, a robot automatic operation is activated to perform the treatment in prior established regions. Furthermore, a camera network is used to get a global view of the robot workspace in order to obtain the workpiece location accurately and in real-time. The effectiveness of the proposed approach is shown with several experiments using a 6R robotic arm.", "Keywords": "Human-robot cooperation ; Smooth approach ; Boundary constraints", "DOI": "10.1016/j.cie.2021.107366", "PubYear": 2021, "Volume": "158", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto de Diseño y Fabricación, Universitat Politècnica de València, Camino de Vera s/n, 46022 Valencia, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instituto de Diseño y Fabricación, Universitat Politècnica de València, Camino de Vera s/n, 46022 Valencia, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Diseño y Fabricación, Universitat Politècnica de València, Camino de Vera s/n, 46022 Valencia, Spain;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Departament d’Enginyeria Electrònica, Universitat de València, Avda de la Universitat s/n, 46100 Burjassot, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Departamento de Ingeniería de Sistemas y Automática, Universidad Miguel Hernández, Avda de la Universidad s/n, 03202 Elche, Spain"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Diseño y Fabricación, Universitat Politècnica de València, Camino de Vera s/n, 46022 Valencia, Spain"}], "References": [{"Title": "Leveraging depth data in remote robot teleoperation interfaces for general object manipulation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "1", "Page": "39", "JournalTitle": "The International Journal of Robotics Research"}]}, {"ArticleId": 87900033, "Title": "The CARESSES Randomised Controlled Trial: Exploring the Health-Related Impact of Culturally Competent Artificial Intelligence Embedded Into Socially Assistive Robots and Tested in Older Adult Care Homes", "Abstract": "<p>This trial represents the final stage of the CARESSES project which aimed to develop and evaluate a culturally competent artificial intelligent system embedded into social robots to support older adult wellbeing. A parallel group, single-blind randomised controlled trial was conducted across older adult care homes in England and Japan. Participants randomly allocated to the Experimental Group or Control Group 1 received a Pepper robot for up 18 h across 2 weeks. Two versions of the CARESSES artificial intelligence were tested: a fully culturally competent system (Experimental Group) and a more limited version (Control Group 1). Control Group 2 (Care As Usual) participants did not receive a robot. Quantitative outcomes of interest reported in the current paper were health-related quality of life (SF-36), loneliness (ULS-8), and perceptions of robotic cultural competence (CCATool-Robotics). Thirty-three residents completed all procedures. The difference in SF-36 Emotional Wellbeing scores between Experimental Group and Care As Usual participants over time was significant (F[1] = 6.614, sig = .019, η<sub>p</sub> <sup>2</sup> = .258), as was the comparison between Any Robot used and Care As Usual (F[1] = 5.128, sig = .031, η<sub>p</sub> <sup>2</sup> = .146). There were no significant changes in SF-36 physical health subscales. ULS-8 loneliness scores slightly improved among Experimental and Control Group 1 participants compared to Care As Usual participants, but this was not significant. This study brings new evidence which cautiously supports the value of culturally competent socially assistive robots in improving the psychological wellbeing of older adults residing in care settings.</p><p>© The Author(s) 2021.</p>", "Keywords": "CARESSES;Cultural competence;Experimental trial;Mental health;Older adults;Socially assistive robots", "DOI": "10.1007/s12369-021-00781-x", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 5388, "JournalTitle": "International Journal of Social Robotics", "ISSN": "1875-4791", "EISSN": "1875-4805", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Bedfordshire, Park Square Campus, Luton, LU1 3JU UK."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Advinia Health Care Limited LTD, 314 Regents Park Road, London, N3 2JX UK."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Bedfordshire, Park Square Campus, Luton, LU1 3JU UK."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Bedfordshire, Park Square Campus, Luton, LU1 3JU UK."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Bedfordshire, Park Square Campus, Luton, LU1 3JU UK."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Informatics, Bioengineering, Robotics and Systems Engineering, University of Genova, Via all'Opera Pia 13, 16145 Genova, Italy."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Science and Technology, Orebro University, Orebro, Sweden."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Informatics, Bioengineering, Robotics and Systems Engineering, University of Genova, Via all'Opera Pia 13, 16145 Genova, Italy."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Informatics, Bioengineering, Robotics and Systems Engineering, University of Genova, Via all'Opera Pia 13, 16145 Genova, Italy."}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Bedfordshire, Park Square Campus, Luton, LU1 3JU UK."}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Advinia Health Care Limited LTD, 314 Regents Park Road, London, N3 2JX UK."}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Advinia Health Care Limited LTD, 314 Regents Park Road, London, N3 2JX UK."}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Japan Advanced Institute of Science and Technology, 1-1 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> 923-1292 Japan."}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nagoya University, Furocho, Chikusaku, Nagoya, Aichi 464-8601 Japan."}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": "University of Bedfordshire, Park Square Campus, Luton, LU1 3JU UK."}, {"AuthorId": 16, "Name": "<PERSON>", "Affiliation": "Department of Informatics, Bioengineering, Robotics and Systems Engineering, University of Genova, Via all'Opera Pia 13, 16145 Genova, Italy."}], "References": [{"Title": "Socially Assistive Robots, Older Adults and Research Ethics: The Case for Case-Based Ethics Training", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "4", "Page": "647", "JournalTitle": "International Journal of Social Robotics"}]}, {"ArticleId": 87900115, "Title": "Software Requirements Selection with Incomplete Linguistic Preference Relations", "Abstract": "<p>Software requirements (SRs) selection is a multicriteria group decision making (MCGDM) problem whose objective is to select the SRs from the pool of the requirements on the basis of different criteria. In MCGDM, different decision makers have different opinions of the same requirement so it is difficult to decide which set of SRs to implement during the different releases of the software. During the MCGDM process, decision makers may use linguistic variables to specify preferences of requirements over other requirements. In real life applications, it has been observed that sometimes decision makers cannot evaluate the SRs due to their lack of knowledge and limited expertise related to the problem domain. In this situation, incomplete linguistic preference relations (LPRs) are constructed. In literature, SRs selection with incomplete LPRs is still an unresearched problem. Therefore, to address this issue, a method is presented for the selection of SRs with incomplete LPRs. Finally, the applicability of the proposed method is explained with the help of an example.</p>", "Keywords": "Software requirements selection; Incomplete linguistic preference relation; Information system; Institute examination system; Functional requirements; Non-functional requirements", "DOI": "10.1007/s12599-021-00696-x", "PubYear": 2021, "Volume": "63", "Issue": "6", "JournalId": 5738, "JournalTitle": "Business & Information Systems Engineering", "ISSN": "2363-7005", "EISSN": "1867-0202", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Automation, Indian Institute of Science, Bangalore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Sciences and Humanities, Faculty of Engineering and Technology, Jamia Millia Islamia, New Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, National Institute of Technology, Kurukshetra, India"}], "References": [{"Title": "Selection of software requirements using TOPSIS under fuzzy environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "44", "Issue": "6", "Page": "503", "JournalTitle": "International Journal of Computers and Applications"}]}, {"ArticleId": 87900190, "Title": "Evaluation of categorical matrix completion algorithms: toward improved active learning for drug discovery", "Abstract": "Motivation <p>High throughput and high content screening are extensively used to determine the effect of small molecule compounds and other potential therapeutics upon particular targets as part of the early drug development process. However, screening is typically used to find compounds that have a desired effect but not to identify potential undesirable side effects. This is because the size of the search space precludes measuring the potential effect of all compounds on all targets. Active machine learning has been proposed as a solution to this problem.</p> Results <p>In this article, we describe an improved imputation method, Impute by Committee, for completion of matrices containing categorical values. We compare this method to existing approaches in the context of modeling the effects of many compounds on many targets using latent similarities between compounds and conditions. We also compare these methods for the task of driving active learning in well-characterized settings for synthetic and real datasets. Our new approach performed the best overall both in the accuracy of matrix completion itself and in the number of experiments needed to train an accurate predictive model compared to random selection of experiments. We further improved upon the performance of our new method by developing an adaptive switching strategy for active learning that iteratively chooses between different matrix completion methods.</p> Availability and implementation <p>A Reproducible Research Archive containing all data and code is available at http://murphylab.cbd.cmu.edu/software.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btab322", "PubYear": 2021, "Volume": "37", "Issue": "20", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "Huangqingbo Sun", "Affiliation": "Computational Biology Department, Carnegie Mellon University, Pittsburgh, PA 15213, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computational Biology Department, Carnegie Mellon University, Pittsburgh, PA 15213, USA;Department of Biological Sciences, Carnegie Mellon University, Pittsburgh, PA 15213, USA;Department of Biomedical Engineering, Carnegie Mellon University, Pittsburgh, PA 15213, USA;Machine Learning Department, Carnegie Mellon University, Pittsburgh, PA 15213, USA"}], "References": []}, {"ArticleId": 87900274, "Title": "RBF NN observer based adaptive feedback control for the ABS system under parametric uncertainties and modelling errors", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJMIC.2020.10037492", "PubYear": 2020, "Volume": "35", "Issue": "4", "JournalId": 12335, "JournalTitle": "International Journal of Modelling, Identification and Control", "ISSN": "1746-6172", "EISSN": "1746-6180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}]