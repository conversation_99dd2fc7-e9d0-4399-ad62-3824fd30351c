{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["编写一个函数\n", "        实现通过传入的分数打印评价"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def print_level(score):\n", "    if score > 90:\n", "        print(\"优秀\")\n", "    elif score < 60:\n", "        print(\"不合格\")\n", "    else:\n", "        print(\"良好\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["良好\n", "不合格\n", "良好\n"]}], "source": ["print_level(90)\n", "print_level(59)\n", "print_level(77)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}