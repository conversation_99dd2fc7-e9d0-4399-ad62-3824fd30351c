[{"ArticleId": 119407219, "Title": "47.2: <i>Invited Paper:</i> Polarization dependent light‐driven liquid crystal elastomer actuators based on photothermal effect", "Abstract": "", "Keywords": "", "DOI": "10.1002/sdtp.15204", "PubYear": 2021, "Volume": "52", "Issue": "S2", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering Southern University of Science and Technology  Xueyuan Road 1088, Nanshan District Shenzhen Guangdong 518055 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering Southern University of Science and Technology  Xueyuan Road 1088, Nanshan District Shenzhen Guangdong 518055 China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering Southern University of Science and Technology  Xueyuan Road 1088, Nanshan District Shenzhen Guangdong 518055 China"}], "References": []}, {"ArticleId": 119407220, "Title": "48.1: <i>Invited Paper:</i> Inkjet printing of homogeneous and green cellulose nanofibrils dielectric for high performance IGZO TFTs", "Abstract": "", "Keywords": "", "DOI": "10.1002/sdtp.15209", "PubYear": 2021, "Volume": "52", "Issue": "S2", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Polymer Optoelectronic Materials and Devices, State Key Laboratory of Luminescent Mate-rials and Devices South China University of Technology  Guangzhou 510640 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Polymer Optoelectronic Materials and Devices, State Key Laboratory of Luminescent Mate-rials and Devices South China University of Technology  Guangzhou 510640 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Agricultural Resources Chemistry and Biotechnology Yulin Normal University  Yulin 537000 China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON> Fang", "Affiliation": "State Key Laboratory of Pulp and Paper Engineering South China University of Technology  Guangzhou 510640 China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Polymer Optoelectronic Materials and Devices, State Key Laboratory of Luminescent Mate-rials and Devices South China University of Technology  Guangzhou 510640 China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Polymer Optoelectronic Materials and Devices, State Key Laboratory of Luminescent Mate-rials and Devices South China University of Technology  Guangzhou 510640 China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Institute of Polymer Optoelectronic Materials and Devices, State Key Laboratory of Luminescent Mate-rials and Devices South China University of Technology  Guangzhou 510640 China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Polymer Optoelectronic Materials and Devices, State Key Laboratory of Luminescent Mate-rials and Devices South China University of Technology  Guangzhou 510640 China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Polymer Optoelectronic Materials and Devices, State Key Laboratory of Luminescent Mate-rials and Devices South China University of Technology  Guangzhou 510640 China"}], "References": []}, {"ArticleId": 119407221, "Title": "53.1: Full‐Color Micro‐LED Display based on MOCVD Growth of Two Types of InGaN/GaN MQWs", "Abstract": "", "Keywords": "", "DOI": "10.1002/sdtp.15230", "PubYear": 2021, "Volume": "52", "Issue": "S2", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Electric Light Sources, School of Information Science and Technology, and Academy of Engineering and Technology Fudan University  Shanghai 200433 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Electric Light Sources, School of Information Science and Technology, and Academy of Engineering and Technology Fudan University  Shanghai 200433 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Electric Light Sources, School of Information Science and Technology, and Academy of Engineering and Technology Fudan University  Shanghai 200433 China"}, {"AuthorId": 4, "Name": "Zexing Yuan", "Affiliation": "Institute for Electric Light Sources, School of Information Science and Technology, and Academy of Engineering and Technology Fudan University  Shanghai 200433 China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Electric Light Sources, School of Information Science and Technology, and Academy of Engineering and Technology Fudan University  Shanghai 200433 China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute for Electric Light Sources, School of Information Science and Technology, and Academy of Engineering and Technology Fudan University  Shanghai 200433 China"}], "References": []}, {"ArticleId": 119407222, "Title": "P‐5.1: Research on the color shift mechanism model of TDDI product ESD test center", "Abstract": "<p>As customers pursue the ultimate pursuit of the display effect of the screen (Panel), display panel manufacturers have increasingly higher requirements for the anti‐static (ESD) of the screen. In the process of testing the ESD performance of the screen, when a large amount of static electricity accumulates on the surface of the Panel, it will usually affect the deflection of the liquid crystal (LC), and the abnormal phenomenon of black and purple appears. This phenomenon is collectively called ESD color shift.</p><p>At present, the most effective way to improve the color shift of ESD in the industry is to plate a high‐resistance film (ATO) on the surface of the display panel (Cell), that is, to plate a conductive layer with low resistance on the surface of the CF(Color Film). This conductive layer passes through the Ag on the TFT. The paste point leads to static electricity; however, due to the high cost of ATO, the production cost ofpanel manufacturers has increased significantly. On the premise of canceling the high‐resistance film, improving the ESD color shift of the screen has become a research hotspot in the industry.</p><p>During the ESD test, the electrostatic gun hits the surface of the T‐LCM, and the static electricity will be transmitted down each layer of the T‐LCM. During this process, the static electricity can be exported through the conductive path designed on the T‐LCM. The conductive path cannot discharge static electricity in time, causing static electricity to reach the liquid crystal. The amount of charge reaching the liquid crystal directly determines whether the liquid crystal is deflected. If the static electricity reaching the liquid crystal is greater than the voltage at which the liquid crystal is deflected, the liquid crystal will be deflected, resulting in ESD color shift.</p><p>Starting from the discussion of the ESD failure mechanism, this paper establishes an ESD color shift failure model; through the analysis of the product structure, each component in the product structure is regarded as an independent capacitor, and the chargedfilm layer (or Parts) quantify the impact of LC, obtain the key factors affecting ESD color shift through comparison, and provide certain guidance for subsequent product design and material selection. The improvement plan proposed in this article has been fully verified on our products, and the ESD color shift is effectively improved under the premise of canceling the high resistance film.</p>", "Keywords": "", "DOI": "10.1002/sdtp.15290", "PubYear": 2021, "Volume": "52", "Issue": "S2", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "Le Sun", "Affiliation": "Ordos Yuansheng Optoelectronics Co. Ltd  Ordos China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>hang", "Affiliation": "Ordos Yuansheng Optoelectronics Co. Ltd  Ordos China"}, {"AuthorId": 3, "Name": "Jingwei Hou", "Affiliation": "Ordos Yuansheng Optoelectronics Co. Ltd  Ordos China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ordos Yuansheng Optoelectronics Co. Ltd  Ordos China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Ordos Yuansheng Optoelectronics Co. Ltd  Ordos China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ordos Yuansheng Optoelectronics Co. Ltd  Ordos China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ordos Yuansheng Optoelectronics Co. Ltd  Ordos China"}], "References": []}, {"ArticleId": 119407223, "Title": "P‐17.2: Automotive multi‐screen with ambient light sensor", "Abstract": "<p>BOE has made a 12.3‐inch automotive multi‐screen module with ambient light sensor. This product integrates the ambient light sensor on the surface of the panel, and the demo can realize 4K high resolution display and can support separate display by screen. At the same time, in order to make the product more beautiful and simple, the ultra narrow splicing design is used.</p>", "Keywords": "", "DOI": "10.1002/sdtp.15394", "PubYear": 2021, "Volume": "52", "Issue": "S2", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Wang", "Affiliation": "Automotive Development Dept. of BOE Corporation  Beijing China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Automotive Development Dept. of BOE Corporation  Beijing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Automotive Development Dept. of BOE Corporation  Beijing China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "Automotive Development Dept. of BOE Corporation  Beijing China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Automotive Development Dept. of BOE Corporation  Beijing China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Automotive Development Dept. of BOE Corporation  Beijing China"}, {"AuthorId": 7, "Name": "Qing Ma", "Affiliation": "Automotive Development Dept. of BOE Corporation  Beijing China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Automotive Development Dept. of BOE Corporation  Beijing China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Automotive Development Dept. of BOE Corporation  Beijing China"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Automotive Development Dept. of BOE Corporation  Beijing China"}], "References": []}, {"ArticleId": 119407224, "Title": "P‐17.4: Simultaneous Localization and Mapping Method and Implementation for AGV", "Abstract": "<p>Along with the social progress and the development of science and technology, AGV as a productivity tool to get the recognition of the world, and to realize the iracy is high, Solid‐state lidar plays an important role in automatic driving, of which solid‐state lidar has a unique scanning mode, low price, intensive scanning points, and good applicability for AGV. Therefore, this paper designs a mobile APP to carry out real‐time control of AGV, and uses solid‐state lidar to carry out real‐time positioning and map building. Finally, the LOAM algorithm ndependent mobile navigation technology is AGV, automatically based on a mission, and key technologies of the AGV in a complex environment work, but the traditional navigation technology existed shortcomings, and laser radar detection range wide, ranging accuis improved to improve the accuracy of map construction. The experimental results show that the proposed method is effective in accuracy and efficiency.</p>", "Keywords": "", "DOI": "10.1002/sdtp.15396", "PubYear": 2021, "Volume": "52", "Issue": "S2", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "North China University of Technology  Beijing China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "North China University of Technology  Beijing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "North China University of Technology  Beijing China"}], "References": []}, {"ArticleId": 119407225, "Title": "16‐1: <i>Student Paper:</i> Optical performance characterization of 5 cm aperture size continuous focus tunable liquid crystal lens for resolving Accommodation‐Convergence mismatch conflict of AR/VR/3D HMDs", "Abstract": "<p>Solving the accommodation‐convergence mismatch issue, which causes eye fatigue, remains a challenge in the field of AR/VR/3D HMDs. We propose a solution to tackle such vision fatigue by using a large aperture (5 cm) continuous focus tunable liquid crystal lens in addition to fixed power VR lens. To reduce the switching speed to achieve desire optical power swing, our device includes phase resets, similar to a Fresnel lens. Introduction of the phase resets increases response speed significantly, however, also introduces diffraction related issues that is the subject of this paper. In this paper, device demonstration and characterization a continuous tunable lens with power from 0 D to 1.6 D is shown.</p>", "Keywords": "", "DOI": "10.1002/sdtp.15444", "PubYear": 2022, "Volume": "53", "Issue": "1", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Materials and Liquid Crystal Institute Kent State University  Ohio USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Reality Labs at Meta  Redmond WA USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Advanced Materials and Liquid Crystal Institute Kent State University  Ohio USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Reality Labs at Meta  Redmond WA USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Reality Labs at Meta  Redmond WA USA"}], "References": [{"Title": "31‐5: Student Paper: Liquid Crystal Based 5 cm Adaptive Focus Lens to Solve Accommodation‐Convergence (AC) Mismatch Issue of AR/VR/3D Displays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "1", "Page": "410", "JournalTitle": "SID Symposium Digest of Technical Papers"}]}, {"ArticleId": 119407226, "Title": "21‐1: <i>Invited Paper:</i> Polarized Emission Thin‐Film Light‐Emitting Diodes", "Abstract": "<p>Organic light emitting diodes (OLEDs) with controlled light emission have many photonic applications. In this paper, using OLEDs for demonstrations and different photonic structures, we demonstrated highly polarized light emission from an OLED. The highly selective diffraction of only the TE waveguide mode is possible by selectively diffracting the TE waveguide mode while suppressing other optical modes. Similar devices are made with perovskites to achieve a current efficiency of 56 cd/A.</p>", "Keywords": "", "DOI": "10.1002/sdtp.15460", "PubYear": 2022, "Volume": "53", "Issue": "1", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Materials Science and Engineering North Carolina State University  Raleigh NC 27606 USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Materials Science and Engineering North Carolina State University  Raleigh NC 27606 USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Materials Science and Engineering North Carolina State University  Raleigh NC 27606 USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Materials Science and Engineering North Carolina State University  Raleigh NC 27606 USA"}], "References": []}, {"ArticleId": 119407227, "Title": "48‐3: <i>Invited Paper:</i> Fiber‐Laser Processing of Si and IGZO Films for Advanced AMOLED Displays on Gen 8 Substrates", "Abstract": "<p>This paper reports on the microstructure of SBA‐crystallized polycrystalline Si films and discusses the potential application of SBA for annealing of amorphous IGZO films. A comparison of the crystallized Si films to those produced using ELA reveals that these strikingly differently generated materials are essentially microstructurally identical. We note that SBA (1) accomplishes the feat while providing certain benefits that address the manufacturing‐related ELA issues regarding operating costs and laser mura, (2) can be configured to execute SLS for realizing high‐mobility TFTs, and (3) may further be utilized for optimal heat treatment of IGZO and other materials.</p>", "Keywords": "", "DOI": "10.1002/sdtp.15563", "PubYear": 2022, "Volume": "53", "Issue": "1", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Columbia University  New York NY 10027 USA"}, {"AuthorId": 2, "Name": "Ruobing <PERSON>", "Affiliation": "Columbia University  New York NY 10027 USA"}, {"AuthorId": 3, "Name": "Jayoung Park", "Affiliation": "Columbia University  New York NY 10027 USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Columbia University  New York NY 10027 USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Columbia University  New York NY 10027 USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Columbia University  New York NY 10027 USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Columbia University  New York NY 10027 USA"}], "References": []}, {"ArticleId": 119407228, "Title": "57‐1: ActiveHogel Light‐field Display: An Application of Next Generation μLED Pixels", "Abstract": "<p> Light‐field displays produce full‐parallax/perspective correct 3D aerial imagery for one or more simultaneous viewers. While there have been a few prototype, large‐format light‐field displays demonstrated using OLED, LCD, LCoS or other spatial light modulator (SLM) technology, they suffered from projection artifacts that degraded the visualization experience. These artifacts can be resolved by developing a custom μLED SLM specially designed for light‐field displays. FoVI <sup>3D</sup> 's ActiveHogel Module and Tile presented in this paper highlight the architecture and benefits of a μLED light‐field display. </p>", "Keywords": "", "DOI": "10.1002/sdtp.15597", "PubYear": 2022, "Volume": "53", "Issue": "1", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "FoVI3D  Austin TX USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "FoVI3D  Austin TX USA"}], "References": []}, {"ArticleId": 119407229, "Title": "P‐42: <i>Student Poster:</i> A New Integrated Scan/Emission Driver Circuit with Progressive Emission Driving Method for Micro‐LED Display", "Abstract": "<p>In this paper, we proposed an a‐IGZO TFT‐based integrated scan/emission driver circuit. The proposed circuit can be applied to micro‐LED pixel circuit using the progressive emission method. Each circuit is designed to prevent leakage current in the depletion mode by using series two transistors (STT) structures, two low supply voltages.</p>", "Keywords": "", "DOI": "10.1002/sdtp.15721", "PubYear": 2022, "Volume": "53", "Issue": "1", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Sungkyunkwan University  Suwon 16419 Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Sungkyunkwan University  Suwon 16419 Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Sungkyunkwan University  Suwon 16419 Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Sungkyunkwan University  Suwon 16419 Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Sungkyunkwan University  Suwon 16419 Korea"}], "References": [{"Title": "61‐2: A Novel Micro‐LED Pixel Circuit Using n‐type LTPS TFT with Pulse Width Modulation Driving", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "1", "Page": "868", "JournalTitle": "SID Symposium Digest of Technical Papers"}, {"Title": "61‐4: A New Pixel Circuit Based on LTPO Backplane Technology for Micro‐LED Display Using PWM Method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "1", "Page": "876", "JournalTitle": "SID Symposium Digest of Technical Papers"}]}, {"ArticleId": 119407230, "Title": "P‐101: Self‐Capacitive Ring Like Touch Sensor Design and Algorithm for OLED On‐Cell Touch Panel", "Abstract": "<p>In this paper, the self‐capacitive ring like touch sensor design and algorithm for OLED on‐cell touch panel is proposed by rearranging the touch sensor pad, and using hybrid algorithm to calculate touch position. Base on ring like touch sensor design and hybrid algorithm, we can improve the touch accuracy of ring region and realize sliding function on smart watch edge without real rotating bezel mechanical parts.</p>", "Keywords": "", "DOI": "10.1002/sdtp.15777", "PubYear": 2022, "Volume": "53", "Issue": "1", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Novatek Microelectronics Corporation  Hsinchu Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Novatek Microelectronics Corporation  Hsinchu Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Novatek Microelectronics Corporation  Hsinchu Taiwan"}], "References": []}, {"ArticleId": 119407231, "Title": "SID 2022 Executive and Program Committee", "Abstract": "", "Keywords": "", "DOI": "10.1002/sdtp.15812", "PubYear": 2022, "Volume": "53", "Issue": "1", "JournalId": 28813, "JournalTitle": "SID Symposium Digest of Technical Papers", "ISSN": "0097-966X", "EISSN": "2168-0159", "Authors": [], "References": []}, {"ArticleId": 119407317, "Title": "Implementation of Android-Based Parking Management Applications", "Abstract": "<p>Parking problems are one of the problems faced in every city, especially in big cities. The availability of limited parking spaces and the unavailability of a parking system that can support the ease of parking management are essential things that must be found as the best solution. The parking application developed can provide information easily. In addition, parking users also get convenience in placing orders, extending time, or canceling. The parking system can provide comfort for parking management to manage parking areas with an efficient system, not only in terms of information but also in terms of payment, by utilizing digital payment methods. The parking application developed based on Android is an alternative that provides user-friendly solutions and parking management. The application has worked well based on trials with various scenarios, both normal scenarios (without cancellation or without extension) and testing with abnormal conditions. From the usability test of 40 respondents with ten questions from the SUS method, an average score of 76 was obtained.</p>", "Keywords": "", "DOI": "10.25139/inform.v7i1.4364", "PubYear": 2022, "Volume": "7", "Issue": "1", "JournalId": 54430, "JournalTitle": "Jurnal INFORM", "ISSN": "2502-3470", "EISSN": "2581-0367", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119407319, "Title": "Science Orbits with an Inner Disturbing Body and an Outer Disturbing Body", "Abstract": "", "Keywords": "Sun Synchronous Orbit; Planets; Natural Satellites; Planetary Science and Exploration; Orbital Property; Inclined Orbit; Space Missions; Asteroids; Moons of Jupiter; Sun-Synchronous Orbit", "DOI": "10.2514/1.G007224", "PubYear": 2023, "Volume": "46", "Issue": "6", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Institute for Astrophysics, 00133 Rome, Italy"}, {"AuthorId": 2, "Name": "Emiliano <PERSON>tore", "Affiliation": "University of Rome “La Sapienza”, 00138 Rome, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Rome “La Sapienza”, 00138 Rome, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University, 210046 Nanjing, People’s Republic of China"}], "References": []}, {"ArticleId": 119407400, "Title": "MVPA and GA Comparison for State Space Optimization at Classic Tetris Game Agent Problem", "Abstract": "<p>Tetris is one of those games that looks simple and easy to play. Although it seems simple, this game requires strategy and continuous practice to get the best score. This is also what makes Tetris often used as research material, especially research in artificial intelligence. These various studies have been carried out. Starting from applying state-space to reinforcement learning, one of the biggest obstacles of these studies is time. It takes a long to train artificial intelligence to play like a Tetris game expert. Seeing this, in this study,  apply the Genetic Algorithms (GA) and the most valuable player (MVPA) algorithm to optimize state-space training so that artificial intelligence (agents) can play like an expert. The optimization means in this research is to find the best weight in the state space with the minimum possible training time to play Tetri<PERSON> with the highest possible value. The experiment results show that GAs and MVPA are very effective in optimizing the state space in the Tetris game. The MVPA algorithm is also faster in finding solutions. The resulting state space weight can also get a higher value than the GA (MVPA value is 249 million, while the GA value is 68 million).</p>", "Keywords": "", "DOI": "10.25139/inform.v7i1.4381", "PubYear": 2022, "Volume": "7", "Issue": "1", "JournalId": 54430, "JournalTitle": "Jurnal INFORM", "ISSN": "2502-3470", "EISSN": "2581-0367", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Picker<PERSON> Pickerling", "Affiliation": ""}], "References": []}, {"ArticleId": 119408104, "Title": "GeoGebra Discovery in Context", "Abstract": "In our contribution we will reflect, through a collection of selected examples, on the potential impact of the GeoGebra Discovery application on different social and educational contexts.", "Keywords": "", "DOI": "10.4204/EPTCS.352.16", "PubYear": 2021, "Volume": "352", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Private University College of Education of the Diocese of Linz, Linz, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Antonio de Nebrija, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Antonio de Nebrija, Madrid, Spain"}], "References": [{"Title": "A Mechanical Geometer", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "4", "Page": "631", "JournalTitle": "Mathematics in Computer Science"}]}, {"ArticleId": 119408171, "Title": "P/B Ratio to Stock Price and Correlation Analysis of Other Financial Indicators", "Abstract": "In recent years, the stock market of Mongolia has become more active than ever with increased number of IPOs and SPOs and addition of number of new products in the market. With the objectives to determine and estimate the relations between P/B ratio to stock rates and other financial indicators. We have conducted the research and analyzed the 2007-2020 trading reports, the dividends distribution, and the financial reports of 42 companies with active trading lists on the Mongolian Stock Exchange identified as I and II classification stocks. We have selected the indicators such as, the size of company, dividend payout ratio, return on assets, return on equity, earnings per share, growth of earnings after taxes to be identified as other financial indicators and we have finalized the estimation. According to the results of regressive analysis, based on 565 monitoring data of 42 companies, the indicators such as, return on assets, return on equity, earnings per share have shown direct correlation to the stock rates; however, other indicators have shown no correlation at all. Thus, further studies on the non-financial factors that have impacts on P/B ratio of stock rates have to be done thoroughly by researchers and analysts.", "Keywords": "Price to Book Ratio;Return on Assets;Return on Equity;Earnings per Share;Price to Sales Ratio", "DOI": "10.4236/ib.2022.141002", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 21352, "JournalTitle": "iBusiness", "ISSN": "2150-4075", "EISSN": "2150-4083", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Accounting Department, Business School of National University of Mongolia, Ulaanbaatar, Mongolia ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Accounting Department, Business School of National University of Mongolia, Ulaanbaatar, Mongolia ."}], "References": []}, {"ArticleId": *********, "Title": "Soft thresholding squeeze-and-excitation network for pose-invariant facial expression recognition", "Abstract": "<p>Pose-invariant facial expression recognition is one of the popular research directions within the field of computer vision, but pose variant usually change the facial appearance significantly, making the recognition results unstable from different perspectives. In this paper, a novel deep learning method, namely, soft thresholding squeeze-and-excitation (ST-SE) block, was proposed to extract salient features of different channels for pose-invariant FER. For the purpose of adapting to different pose-invariant facial images better, global average pooling (GAP) operation was adopted to compute the average value of each channel of the feature map. To enhance the representational power of the network, Squeeze-and-Excitation (SE) block was embedded into the nonlinear transformation layer to filter out the redundant feature information. To further shrink the significant features, the absolute values of GAP and SE were multiplied to calculate the threshold suitable for the current view. And the developed ST-SE block was inserted into ResNet50 for the evaluation of recognition performance. In this study, extensive experiments on four pose-invariant datasets were carried out, i.e., BU-3DFE, Multi-PIE, Pose-RAF-DB and Pose-AffectNet, and the influences of different environments, poses and intensities on expression recognition were specifically analyzed. The experimental results demonstrate the feasibility and effectiveness of our method.</p>", "Keywords": "Pose-invariant facial expression recognition; Squeeze-and-excitation (SE) block; Soft thresholding SE block; Deep residual networks", "DOI": "10.1007/s00371-022-02483-5", "PubYear": 2023, "Volume": "39", "Issue": "7", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical and Information Engineering, Jiangsu University, Zhenjiang City, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical and Information Engineering, Jiangsu University, Zhenjiang City, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical and Information Engineering, Jiangsu University, Zhenjiang City, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical and Information Engineering, Jiangsu University, Zhenjiang City, China"}], "References": [{"Title": "Dual-modality spatiotemporal feature learning for spontaneous facial expression recognition in e-learning using hybrid deep neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "743", "JournalTitle": "The Visual Computer"}, {"Title": "Fast facial expression recognition using local binary features and shallow neural networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "97", "JournalTitle": "The Visual Computer"}, {"Title": "Micro-expression recognition: an updated review of current trends, challenges and solutions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "445", "JournalTitle": "The Visual Computer"}, {"Title": "Multi-level uncorrelated discriminative shared Gaussian process for multi-view facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "143", "JournalTitle": "The Visual Computer"}, {"Title": "Discriminative deep multi-task learning for facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "533", "Issue": "", "Page": "60", "JournalTitle": "Information Sciences"}, {"Title": "OAENet: Oriented attention ensemble for accurate facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107694", "JournalTitle": "Pattern Recognition"}, {"Title": "Exploring multi-scale deformable context and channel-wise attention for salient object detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "428", "Issue": "", "Page": "92", "JournalTitle": "Neurocomputing"}, {"Title": "Landmark guidance independent spatio-channel attention and complementary context information based facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON> Balasubramanian", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "58", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A spatio-temporal integrated model based on local and global features for video expression recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "8", "Page": "2617", "JournalTitle": "The Visual Computer"}, {"Title": "Dynamic multi-channel metric network for joint pose-aware and identity-invariant facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "195", "JournalTitle": "Information Sciences"}, {"Title": "CERN: Compact facial expression recognition net", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "155", "Issue": "", "Page": "9", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 119409110, "Title": "Combining grey clustering and fuzzy grey cognitive maps: an approach to group decision-making on cause-and-effect relationships", "Abstract": "<p>Fuzzy grey cognitive maps (FGCMs) have been widely adopted to support cause-and-effect decision-making under uncertainty. However, capturing the information for the initial state vector and the relationship matrix from various specialists can be cumbersome, which may affect the convergence of FGCMs or cause them to reach a chaotic state. To address this issue, this paper presents a novel group decision approach based on the combination of grey clustering (GC) and fuzzy grey cognitive maps for assessing causal relationships in uncertain environments. The main contribution consists in applying GC as a mean to obtain the initial state vector from the relationship matrix data. This halves the required inputs to the users, reducing uncertainty in the computational model. Also, the proposition brings a method for aggregating the linguistic judgements of multiple decision-makers when assessing causal relationships. The proposition also differs from others in the literature by providing results with lower imprecision levels, named greyness, and lower number of required iterations for the FGCM-based system to converge. A real application was conducted in a technology start-up to test the approach to practical implications. Results allowed the identification of important elements regarding the company’s profile and performance, aiding prioritization and enabling the development of action plans. This paper also includes a comparison of other representative models with the proposed approach, which led to more accurate results. Hence, this study addresses the need for new alternatives to improve the reliability and convergence of FGCM-based systems. Finally, suggestions for future applications are proposed.</p>", "Keywords": "Grey clustering; Fuzzy grey cognitive maps; Group decision-making; Grey systems theory; Organizational culture; Start-up", "DOI": "10.1007/s00500-021-06345-5", "PubYear": 2021, "Volume": "25", "Issue": "24", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Production Engineering Department, São Carlos School of Engineering, University of São Paulo, São Carlos, Brazil; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Production Engineering Department, São Carlos School of Engineering, University of São Paulo, São Carlos, Brazil"}], "References": [{"Title": "Entailment for intuitionistic fuzzy sets based on generalized belief structures", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "6", "Page": "963", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "On the conjunction of possibility measures under intuitionistic evidence sets", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "7", "Page": "7827", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Uncertain database retrieval with measure – Based belief function attribute values under intuitionistic fuzzy set", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "436", "JournalTitle": "Information Sciences"}, {"Title": "Decision making under measure-based granular uncertainty with intuitionistic fuzzy sets", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "8", "Page": "6224", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 119409111, "Title": "Performance assessment and comparison of symmetric and asymmetric augmented data vortex architecture for HPC using efficient algorithm", "Abstract": "<p>The emerging technologies such as high-performance computing, cloud computing and large data centre networks demand very high injection rate, high scalability and very low switching latency (SL) that can manage high network traffic, and buffering and also be cost-effective, so that they can be a viable solution for future manufacturing industry. To overcome the biggest challenge of optical buffering and big quantity of data switching, the Data Vortex (DV) all optical switch innovations and its various enhancements such as Augmented Data Vortex (ADV) seem to be the possible solution and can bring the revolution in the industrial world. In this paper, a new generalized algorithm has been used to simulate the equivalent planar virtual model of 3-D ADV and is named as equivalent planar algorithm (EPA). The algorithm is more versatile and provides more flexible design for multiple level networks. To verify the proposed EPA of ADV (or EPA ADV), the simulations have been performed for various input traffic loads and switch size keeping active angle ‘A’ constant. Also the ADV switch is not studied under asymmetric mode (AM) of operation till now which has been done here. Lastly the paper also provides the performance comparison study of original DV and EPA ADV under symmetric and asymmetric I/O mode. The results obtained from simulation have shown that under modest degree of asymmetric I/O operation of the switch EPA ADV shows performance improvement in injection rate (throughput) and average switch latency (SL) under different traffic pattern as compared to symmetric mode (SM) of operation in ADV.</p>", "Keywords": "High-performance computing; Data vortex (DV) switch; Augmented data vortex (ADV) switch; Interconnection network; Large data centre networks; Switch latency (SL); Symmetric mode (SM); Asymmetric mode", "DOI": "10.1007/s00500-022-07087-8", "PubYear": 2023, "Volume": "27", "Issue": "7", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, Ujjain Engineering College, Ujjain, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, Ujjain Engineering College, Ujjain, India"}], "References": []}, {"ArticleId": 119409112, "Title": "Hybrid visual computing models to discover the clusters assessment of high dimensional big data", "Abstract": "<p>Clusters assessment is a major identified problem in big data clustering. Top big data partitioning techniques, such as, spherical k-means, Mini-batch-k-means are widely used in many large data applications. However, they need prior information about the clusters assessment to discover the quality of clusters over the big data. Existing visual models, namely, clustering with improved visual assessment of tendency, and sample viewpoints cosine-based similarity VAT (SVPCS-VAT), efficiently perform the clusters assessment of big data. For the high-dimensional big data, the SVPCS-VAT is enhanced with the subspace learning techniques, principal component analysis (PCA), linear discriminant analysis (LDA), locality preserving projection (LPP), Neighborhood preserving embedding (NPE). These are used to develop hybrid visual computing models, including PCA-based SVPCS-VAT, LDA-based SVPCS-VAT, and LPP-based SVPCS-VAT, NPE-based SVPCS-VAT to overcome the curse of dimensionality problem. Experimental is conducted on benchmarked datasets to demonstrate and compare the efficiency with the state-of-the-art big data clustering methods.</p>", "Keywords": "Data clustering; Cluster tendency; Visual models; Big data; Subspace learning", "DOI": "10.1007/s00500-022-07092-x", "PubYear": 2023, "Volume": "27", "Issue": "7", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, Dayananda Sagar University, Bangalore, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of CSE, Dayananda Sagar University, Bangalore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, RGM College of Engineering and Technology, Nandyal, India"}], "References": [{"Title": "Visual topic models for healthcare data clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "2", "Page": "545", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "An effective assessment of cluster tendency through sampling based multi-viewpoints visual method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Sampling-based visual assessment computing techniques for an efficient social data clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "8", "Page": "8013", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An extended visual methods to perform data cluster assessment in distributed data systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "6", "Page": "8810", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 119409113, "Title": "A hybrid decision-making framework to manage occupational stress in project-based organizations", "Abstract": "<p>According to recent studies in the field of human resource management (HRM), especially in project-based organizations (PBOs), stress is recognized as a factor that has a paramount significance on the performance of staff. Previous studies in organizational stress management have mainly focused on identifying job stressors and their effects on organizations. Contrary to the previous studies, this paper aims to propose a comprehensive decision-support system that includes identifying stressors, assessing organizational stress levels, and providing solutions to improve the performance of the organization. A questionnaire is designed and distributed among 170 senior managers of a major project-based organization in the field of the energy industry in Iran to determine organizational stressors. Based on the questionnaire results and considering the best worst method (BWM) as an approach to determine the weighting vector, the importance degree of each stressor is calculated. In the next stage, a decision-support model is developed to assess the stress level of a PBO through fuzzy inference systems (FIS). Some main advantages of the proposed hybrid decision-support model include (i) achieving high-reliable results by not-so-time-consuming computational volume and (ii) maintaining flexibility in adding new criteria to assess the occupational stress levels in PBOs. Based on the obtained results, six organizational stressors, including job incongruity, poor organizational structure, poor project environment, work overload, poor job promotion, and type A behavior, are identified. It is also found that the level of organizational stress is not ideal. Finally, some main recommendations are proposed to manage occupational stresses at the optimum level in the considered sector.</p>", "Keywords": "Occupational stress; Best worst method; Project-based organization; Fuzzy inference system", "DOI": "10.1007/s00500-022-07143-3", "PubYear": 2022, "Volume": "26", "Issue": "22", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial Engineering, College of Engineering, University of Tehran, Tehran, Iran; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Industrial Engineering, College of Engineering, University of Tehran, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management, Faculty of Management, University of Tehran, Tehran, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial Engineering, College of Engineering, University of Tehran, Tehran, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Universiti Teknologi Malaysia, Skudai, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Universiti Teknologi Malaysia, Skudai, Malaysia"}], "References": [{"Title": "RETRACTED ARTICLE: Toward a novel method to support decision-making process in health and behavioral factors analysis for the composition of IT projects teams", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "11019", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Supervised machine learning techniques and genetic optimization for occupational diseases risk prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "6", "Page": "4393", "JournalTitle": "Soft Computing"}, {"Title": "Explicit methods for attribute weighting in multi-attribute decision-making: a review study", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "5", "Page": "3127", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "An Integrated Fuzzy Carbon Management-Based Model for Suppliers’ Performance Evaluation and Selection in Green Supply Chain Management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "2", "Page": "712", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Human performance modeling and its uncertainty factors affecting decision making: a survery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "4", "Page": "2851", "JournalTitle": "Soft Computing"}, {"Title": "A new approach for ergonomic risk assessment integrating KEMIRA, best–worst and MCDM methods", "Authors": "Elif <PERSON> Delice; Gülin Feryal Can", "PubYear": 2020, "Volume": "24", "Issue": "19", "Page": "15093", "JournalTitle": "Soft Computing"}, {"Title": "A new decision-making model using complex intuitionistic fuzzy Hamacher aggregation operators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "10", "Page": "7059", "JournalTitle": "Soft Computing"}, {"Title": "Group decision-making framework under linguistic q-rung orthopair fuzzy Einstein models", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "15", "Page": "10309", "JournalTitle": "Soft Computing"}, {"Title": "Extension of TOPSIS model to the decision-making under complex spherical fuzzy information", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "16", "Page": "10771", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 119409114, "Title": "The cost and CO2 emission optimization of reinforced concrete frames with non-prismatic members", "Abstract": "<p>Nowadays, reducing greenhouse gases is a major global challenge. The CO<sub>2</sub> emission has a remarkable contribution and reinforced concrete (RC) frames largely contribute to the emission of this gas. One method to reduce CO<sub>2</sub> emission from the construction industry is the use of optimization methods in the structural design phase. No study has been conducted on the trade-off between structural cost and CO<sub>2</sub> emission of RC frames with non-prismatic columns. Therefore, this paper presents the optimal design of RC frame with non-prismatic beams and non-prismatic columns to minimize the cost and CO<sub>2</sub> emissions. The procedure is applied for RC frames and also for RC industrial frames with pitched roof. Design variables are taken as the dimensions of the cross sections of beams and columns, reinforcements of the beams and columns, compressive strength of the concrete as well as the tapered length for the non-prismatic elements. Optimal structural solutions are obtained utilizing the enhanced colliding bodies optimization algorithm. Here, designs based on minimizing CO<sub>2</sub> emission are compared with the cost-based designs. Results show that in CO<sub>2</sub>-based design, the amount of CO<sub>2</sub> is reduced by 3.8% in RC frames with a 6% increase in cost, and in the RC industrial frames with 1.3% increase in cost, the amount of CO<sub>2</sub> can be reduced by 7%.</p>", "Keywords": "Optimal cost; Optimal CO2 emissions; RC frames; Non-prismatic columns; Non-prismatic beams; Enhanced colliding bodies optimization (ECBO) algorithm", "DOI": "10.1007/s00500-022-07231-4", "PubYear": 2022, "Volume": "26", "Issue": "18", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Iran University of Science and Technology, Tehran, Iran; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Civil Engineering Department, Imam <PERSON> International University, Qazvin, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Civil Engineering Department, Imam <PERSON> International University, Qazvin, Iran"}], "References": [{"Title": "Optimum design of three-dimensional steel frames with prismatic and non-prismatic elements", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "1011", "JournalTitle": "Engineering with Computers"}, {"Title": "Sustainable design of reinforced concrete frames with non-prismatic beams", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "69", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": *********, "Title": "IEESEP: an intelligent energy efficient stable election routing protocol in air pollution monitoring WSNs", "Abstract": "<p>Nowadays, wireless sensor network (WSN) consists of insignificant and low-priced sensing hops (nodes) focusing on gathering eco-friendly information. It may be used in a variation of control systems, environment monitoring such as industrial pollution, disaster management, indoor and outdoor temperature. The comprehensive series of uses of WSNs is constantly growing despite the limitations of sensor nodes (SNs) resources like capacity, a range of communication, etc. The major problems faced in WSNs are the maximum energy consumption (EC) and end-to-end delay (E2D) in relaying information to the destination node. This research work proposes an enhanced Stable election protocol that provides intelligent ways to form an optimal route in the network with the FFBPNN algorithm called IEESEP. In this method, the wireless air pollution monitoring (WAPM) System is proficient on a large dataset comprising all scenarios to create WAPMS reliability and adaptability to the environment. Moreover, it is used for varying cluster-based research methodology to improve the network lifetime. A feed-forward, back propagation (FFBPNN) gives to form an optimal path. It enhances network stability by using parameters like advanced and normal nodes. This protocol provides an effective threshold value for selecting an optimal route on the FFBPNN method. So, our research method is highly energy-efficient, proficient at maximizing SNs packet delivery rate and network lifetime. Experimental outperforms define that it results in an IEESEP protocol delivery rate by 78%, other protocols like SEP and ELDC Protocol by 50% and 27% delivery rate.</p>", "Keywords": "Wireless sensor network; Wireless air pollution monitoring system; Stable election protocol; Energy-efficient and robust routing scheme; Intelligent-based protocol; Feed-forward back propagation neural network", "DOI": "10.1007/s00521-022-07027-5", "PubYear": 2022, "Volume": "34", "Issue": "13", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Ekta Dixit", "Affiliation": "Department of Computer Science, Punjabi University, Patiala, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "PG Department of Computer Science, DAV College, Bathinda, India"}], "References": []}, {"ArticleId": *********, "Title": "EmotiphAI: a biocybernetic engine for real-time biosignals acquisition in a collective setting", "Abstract": "<p>In the latter years, we have been observing a growth in wearable technology for personal use. However, an analysis of the state of the art for wearable technology shows that most devices perform data acquisition from individual subjects only, relying on communication technologies with drawbacks that prevent their use in collective real-world scenarios (e.g. a cinema, a theatre, and related use cases). When analysing the emotional response in groups, two types of emotions appear: individual (influenced by the group) and group-based emotions (towards the group as an identity). To fill the existing gap, we propose a biocybernetic engine for real-time data acquisition of multimodal physiological data in real-world scenarios. Our system extends the state of the art with: (1) real-time data acquisition for the signals being acquired (20 devices at 25 Hz; 10 devices at 60 Hz); (2) creation of a standalone local infrastructure with end-user interface for monitoring the data acquisition; (3) local and cloud-based data storage. We foresee that this platform could be the basis for the creation of large databases in diverse real-world scenarios, namely health and wellbeing, marketing, art performances, and others. As a result, this work will greatly contribute to simplify widespread biosignals data collection from unobtrusive wearables. To evaluate the system, we report a comprehensive assessment based on a set of criteria for data quality analysis.</p>", "Keywords": "Data acquisition; Wearable device; Group emotion; Affective computing; Emotion recognition", "DOI": "10.1007/s00521-022-07191-8", "PubYear": 2023, "Volume": "35", "Issue": "8", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Bioengineering (DBE), Instituto Superior Técnico (IST), Lisboa, Portugal; Instituto de Telecomunicações (IT), Lisboa, Portugal; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institut de Recherche et Coordination Acoustique/Musique, Paris, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Bioengineering (DBE), Instituto Superior Técnico (IST), Lisboa, Portugal; Instituto de Telecomunicações (IT), Lisboa, Portugal"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Bioengineering (DBE), Instituto Superior Técnico (IST), Lisboa, Portugal; Instituto de Telecomunicações (IT), Lisboa, Portugal"}], "References": []}, {"ArticleId": 119409117, "Title": "An image encryption algorithm based on <PERSON><PERSON><PERSON><PERSON> Q-matrix and genetic algorithm", "Abstract": "<p>In this paper, an image encryption algorithm based on Fibonacci Q-matrix and genetic algorithm is proposed. The new four-layer encryption framework of diffusion-scrambling-diffusion-optimization is adopted. Firstly, an improved four-direction diffusion operation is performed on the original image to achieve efficient global diffusion based on row and column vectors and the first-round diffusion matrix is obtained. Secondly, using the Logistic chaotic system to improve the Josephus scrambling, and then use the improved scrambling method to scramble the first-round diffusion matrix, which improves the scrambling effect of the algorithm. Thirdly, the scrambling matrix is divided into blocks, and then the Fibonacci Q-matrix corresponding to each block is dynamically selected by random numbers and the two are rapidly diffused, which further improves the diffusion effect. Finally, in order to make the optimization more targeted, the initial population is generated by the second-round diffusion matrix and the fitness function is selected according to the correlation of the second-round diffusion matrix. In the optimization process, multiple rounds of selection, crossover and fitness calculation are performed on the initial population to obtain the encrypted image. In addition, the initial values of the NHS hyperchaotic system used in the improved four-way diffusion and the Logistic chaotic system used in the improved Josephus scrambling is all generated from the 256-bit hash values of the original image, which makes the proposed algorithm highly sensitive to plaintext image. The experimental results and security analyses show that the algorithm not only has high security but also has certain robustness and real-time performance, which is suitable for practical applications.</p>", "Keywords": "Genetic algorithm; Improved four-way diffusion; Fibonacci Q-matrix diffusion; <PERSON><PERSON> scrambling; Chaos", "DOI": "10.1007/s00521-022-07493-x", "PubYear": 2022, "Volume": "34", "Issue": "21", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Dalian <PERSON> University, Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Dalian <PERSON> University, Dalian, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Computer Science, Zhejiang Normal University, Jinhua, China; Corresponding author."}], "References": [{"Title": "Adaptive encryption of digital images based on lifting wavelet optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "13-14", "Page": "9363", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "RETRACTED ARTICLE: Hybrid optimization with cryptography encryption for medical image security in Internet of Things", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "10979", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Image encryption algorithm for synchronously updating Boolean networks based on matrix semi-tensor product theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "16", "JournalTitle": "Information Sciences"}, {"Title": "Fast image encryption algorithm based on parallel permutation-and-diffusion strategy", "Authors": "<PERSON><PERSON><PERSON> Wang; Hongyu Zhao", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "19005", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A modified method for image encryption based on chaotic map and genetic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "37-38", "Page": "26927", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Cryptanalysis of genetic algorithm-based encryption scheme", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "35-36", "Page": "25259", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An image encryption scheme based on double chaotic cyclic shift and Joseph<PERSON> problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "102699", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Image encryption method based on improved ECC and modified AES algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "13", "Page": "19769", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel image encryption cryptosystem based on true random numbers and chaotic systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "1", "Page": "95", "JournalTitle": "Multimedia Systems"}, {"Title": "New extension of data encryption standard over 128-bit key for digital images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "20", "Page": "13845", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Image cipher using image filtering with 3D DNA-based confusion and diffusion strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Xiangcheng Zhi", "PubYear": 2021, "Volume": "33", "Issue": "23", "Page": "16251", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Image data security using Quasigroup combined with <PERSON>bon<PERSON><PERSON> Q-transformation", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "102941", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "An efficient grayscale image encryption scheme based on variable length row-column swapping operations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "30", "Page": "36305", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel DNA-based key scrambling technique for image encryption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "6", "Page": "3241", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 119409131, "Title": "One-stage explicit trigonometric integrators for effectively solving quasilinear wave equations", "Abstract": "<p>In this paper, one-stage explicit trigonometric integrators for solving quasilinear wave equations are formulated and studied. For solving wave equations, we first introduce trigonometric integrators as the semidiscretization in time and then consider a spectral Galerkin method for the discretization in space. We show that one-stage explicit trigonometric integrators in time have second-order convergence and the result is also true for the fully discrete scheme without requiring any CFL-type coupling of the discretization parameters. The results are proved by using energy techniques, which are widely applied in the numerical analysis of methods for partial differential equations.</p>", "Keywords": "quasilinear wave equations; trigonometric integrators; second-order convergence; energy technique", "DOI": "10.1007/s10092-023-00506-8", "PubYear": 2023, "Volume": "60", "Issue": "1", "JournalId": 12677, "JournalTitle": "<PERSON><PERSON><PERSON>", "ISSN": "0008-0624", "EISSN": "1126-5434", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Nanjing University of Information Science and Technology, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Statistics, Xi’an Jiaotong University, Xi’an, China; Corresponding author."}], "References": [{"Title": "Numerical approximation and simulation of the stochastic wave equation on the sphere", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "59", "Issue": "3", "Page": "1", "JournalTitle": "<PERSON><PERSON><PERSON>"}]}, {"ArticleId": 119409148, "Title": "Pflichten im Auftragsverarbeitungsverhältnis", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00287-021-01395-3", "PubYear": 2021, "Volume": "44", "Issue": "5", "JournalId": 7707, "JournalTitle": "Informatik-Spektrum", "ISSN": "0170-6012", "EISSN": "1432-122X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, Schweiz; Corresponding author."}], "References": []}, {"ArticleId": 119409176, "Title": "Are finite affine topological systems worthy of study?", "Abstract": "<p>There exists the notion of topological system of <PERSON><PERSON>, which provides a common framework for both topological spaces and the underlying algebraic structures of their topologies—locales. A well-known result of <PERSON><PERSON> <PERSON><PERSON> states that every topological space is homeomorphic to a subspace of a product of a finite (three-element) topological space. We have already shown that the space of <PERSON><PERSON> <PERSON><PERSON> is (in general) no longer finite in case of affine topological spaces (inspired by the concept of affine set of <PERSON><PERSON>), which include many-valued topology. This paper provides an analogue of the result of <PERSON><PERSON> <PERSON><PERSON> for topological systems of S<PERSON> Vickers, and also shows that for affine topological systems, an analogue of the above three-element space becomes (in general) infinite. A simple message here is that finite systems play a (probably) less important role in the affine topological setting (for example, in many-valued topology) than they do play in the classical topology.</p>", "Keywords": "Affine set; Affine space; Affine system; (co)product; <PERSON> space; Extremal coseparator; Frame; Powerset functor; Quantale; <PERSON><PERSON><PERSON><PERSON> object; <PERSON><PERSON><PERSON><PERSON> space; Topological category; Topological space; Topological system; Variety", "DOI": "10.1007/s00500-022-07260-z", "PubYear": 2022, "Volume": "26", "Issue": "18", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematical Sciences, Kent State University, Ohio, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Engineering, Czech University of Life Sciences Prague, Prague, Czech Republic; Corresponding author."}], "References": [{"Title": "Are finite affine topological systems worthy of study?", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "18", "Page": "9021", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 119409180, "Title": "Correction to: Research on simulation of 3D human animation vision technology based on an enhanced machine learning algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-022-07267-5", "PubYear": 2023, "Volume": "35", "Issue": "7", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Zhenning Yuan", "Affiliation": "Academic Affairs Office, Qingdao Agricultural University, Qingdao, China; Department of Formative Convergence Arts, Hoseo University, Asan, Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Formative Convergence Arts, Hoseo University, Asan, Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Animation and Communication College, Qingdao Agricultural University, Qingdao, China; Corresponding author."}], "References": []}, {"ArticleId": 119409181, "Title": "Long short-term cognitive networks", "Abstract": "<p>In this paper, we present a recurrent neural system named long short-term cognitive networks (LSTCNs) as a generalization of the short-term cognitive network (STCN) model. Such a generalization is motivated by the difficulty of forecasting very long time series efficiently. The LSTCN model can be defined as a collection of STCN blocks, each processing a specific time patch of the (multivariate) time series being modeled. In this neural ensemble, each block passes information to the subsequent one in the form of weight matrices representing the prior knowledge. As a second contribution, we propose a deterministic learning algorithm to compute the learnable weights while preserving the prior knowledge resulting from previous learning processes. As a third contribution, we introduce a feature influence score as a proxy to explain the forecasting process in multivariate time series. The simulations using three case studies show that our neural system reports small forecasting errors while being significantly faster than state-of-the-art recurrent models.</p>", "Keywords": "Short-term cognitive networks; Recurrent neural networks; Multivariate time series; Interpretability", "DOI": "10.1007/s00521-022-07348-5", "PubYear": 2022, "Volume": "34", "Issue": "19", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Cognitive Science and Artificial Intelligence, Tilburg University, Tilburg, The Netherlands; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Information Systems Group, Eindhoven University of Technology, Eindhoven, The Netherlands"}, {"AuthorId": 3, "Name": "Agnieszka Jastrzębska", "Affiliation": "Faculty of Mathematics and Information Science, Warsaw University of Technology, Warsaw, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Universidad de Talca, Talca, Chile"}], "References": [{"Title": "Pseudoinverse learning of Fuzzy Cognitive Maps for multivariate time series forecasting", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106461", "JournalTitle": "Applied Soft Computing"}, {"Title": "Pattern classification with Evolving Long-term Cognitive Networks", "Authors": "<PERSON><PERSON><PERSON>; Agni<PERSON><PERSON><PERSON>rzę<PERSON>ka; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "548", "Issue": "", "Page": "461", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 119409183, "Title": "Design and experimental research of magnetically excited rotating piezoelectric energy harvester", "Abstract": "<p>Piezoelectric energy harvesting is a widely used and environmentally friendly power generation technology, which can provide power for wireless electronic devices and low-power sensors. In this paper, a magnetically excited rotating piezoelectric energy harvester is proposed, which can generate output voltages of more than 15 V at frequencies below 13 Hz, where the maximum output voltage is 55.62 V. Through the rotation of the rotamer, magnetic coupling between magnetic blocks drives piezoelectric elements to vibrate nonlinearly to generate electrical energy. Structural prototypes with different parameters were designed and produced after analyzing the working principle and theoretical model of the entire piezoelectric system, and a series of experimental verifications were carried out on the theoretical model. The experimental results show that the energy harvester has the best power generation effect when the weight of the magnet mass is 6 g, the radial excitation distance is 5 mm, and multiple piezoelectric beams are connected in series. When the piezoelectric beams are connected in series and rotational speed is 550 r/min, the external load resistance is 75 kΩ, its maximum root mean square output voltage and average output power are 33.12 V and 14.625 mW, respectively. The energy harvesting system can light up 260 LED lights under the excitation of 550 r/min.</p>", "Keywords": "Piezoelectric; Energy harvester; Nonlinear; Magnetic field; Rotational", "DOI": "10.1007/s00542-022-05279-8", "PubYear": 2022, "Volume": "28", "Issue": "7", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronic Engineering, Changchun University of Technology, Changchun, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronic Engineering, Changchun University of Technology, Changchun, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechatronic Engineering, Changchun University of Technology, Changchun, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronic Engineering, Changchun University of Technology, Changchun, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronic Engineering, Changchun University of Technology, Changchun, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Precision Machinery, Zhejiang Normal University, Jinhua, China"}], "References": []}, {"ArticleId": 119409233, "Title": "GANID: a novel generative adversarial network for image dehazing", "Abstract": "<p>This paper presents a novel algorithm to dehaze a given hazy input image using a generative adversarial network (GAN). The proposed GAN structure uses a Feature Residual Dense combined Network (FRDN) as a generator and a Markovian discriminator (PatchGAN) with additional layers as the discriminator. FRDN is capable of extracting contextual information using Feature Module (FM) in conjunction with the Residual Dense Module, and IMCU enhances collaborative learning which enhances its performance. The inclusion of proposed reality and visibility loss functions along with (L_1) loss improves the scene visibility and realness of the dehazed image. The network is trained with images from the benchmark datasets—RESIDE and NTIRE 2021. The proposed technique’s performance is evaluated using various metrics such as PSNR, SSIM, FSIM, FADE, NIQE, and BRISQUE. An average PSNR of 25.701, 32.52, and 33.96 has been obtained with the Indoor Training Set, Synthetic Objective Testing Set indoor, and SOTS outdoor images, respectively. The experimental results reveal that the suggested method’s performance is better compared to other state-of-the-art techniques.</p>", "Keywords": "Image dehazing; Generative adversarial network (GAN); Dilated convolution; Deep learning", "DOI": "10.1007/s00371-022-02536-9", "PubYear": 2023, "Volume": "39", "Issue": "9", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Vision Lab, Department of Electronics and Communication, College of Engineering, APJA K.T.U, Thiruvananthapuram, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Computer Vision Lab, Department of Electronics and Communication, College of Engineering, APJA K.T.U, Thiruvananthapuram, India"}], "References": [{"Title": "Back-projection-based progressive growing generative adversarial network for single image super-resolution", "Authors": "<PERSON><PERSON>ong Ma; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "5", "Page": "925", "JournalTitle": "The Visual Computer"}, {"Title": "Single image deraining via deep shared pyramid network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "7", "Page": "1851", "JournalTitle": "The Visual Computer"}, {"Title": "Joint restoration convolutional neural network for low-quality image super resolution", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "31", "JournalTitle": "The Visual Computer"}, {"Title": "An efficient and contrast-enhanced video de-hazing based on transmission estimation using HSL color model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "7", "Page": "2569", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 119409255, "Title": "A novel fractional-order accumulation grey power model and its application", "Abstract": "<p>Considering the effective service life of products, this study initially defined a generalized fractional-order accumulation generation matrix covering the effective accumulation percentage. We suggested a generalized fractional-order accumulation grey power model (GFAGMP(1,1) model) using this matrix, along with its parameter estimate, error analysis, and time response function solution. We studied transformation and the link from the generalized GM(1,1) model to GFAGMP(1,1) model on the basis of integral and power function transformation and deduced three derivation forms of this model and their application range via the class ratio analysis. Finally, different forecasting models were compared with the actual sales data of Chinese refrigerators. The results of comparison demonstrated the feasibility and effectiveness of the GFAGMP(1,1) model in forecasting the home-appliance supply chain demand in China.</p>", "Keywords": "Grey forecasting; Grey power model; Generalized fractional-order accumulation; Derivation forms", "DOI": "10.1007/s00500-022-07634-3", "PubYear": 2023, "Volume": "27", "Issue": "3", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Yang", "Affiliation": "School of Business Administration, Hunan University, Changsha, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Management, Central China Normal University, Wuhan, People’s Republic of China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Wuhan Institute of Technology, Wuhan, People’s Republic of China"}], "References": [{"Title": "A multivariate grey prediction model with grey relational analysis for bankruptcy prediction problems", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "6", "Page": "4259", "JournalTitle": "Soft Computing"}, {"Title": "Grey–Lotka–Volterra model for the competition and cooperation between third-party online payment systems and online banking in China", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106501", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-attribute group decision making method with dual comprehensive clouds under information environment of dual uncertain Z-numbers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "602", "Issue": "", "Page": "106", "JournalTitle": "Information Sciences"}]}, {"ArticleId": *********, "Title": "Denoising of piecewise constant signal based on total variation", "Abstract": "<p>Total variation denoising (TVD) algorithm is suitable for the restoration of piecewise constant signals (PCS). The amplitude accuracy of discontinuities affects the overall denoising effect. Improving the amplitude accuracy of discontinuities is the difficulty of PCS denoising. Recent papers have presented TVD-based algorithms to improve the denoising precision. After applying the forward–backward splitting (FBS) method, the iteration of the denoising is comprised by the traditional TVD. This paper proposes a new upper bound function to enhance the accuracy of the traditional TVD method. Then, the enhanced TVD is used to update the TVD-based algorithm to improve the denoising efficiency. The experimental results demonstrate that the proposed method has superior performance compared to other methods in PCS denoising.</p>", "Keywords": "Denoising algorithm; Total variation; Forward–backward splitting; Piecewise constant signal", "DOI": "10.1007/s00521-022-06937-8", "PubYear": 2022, "Volume": "34", "Issue": "19", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Donghao Lv", "Affiliation": "School of Automation, China University of Geosciences, Wuhan, China; School of Information Engineering, Inner Mongolia University of Science and Technology, Baotou, China; Hubei Key Laboratory of Advanced Control and Intelligent Automation for Complex Systems, China University of Geosciences, Wuhan, China; Engineering Research Center of Intelligent Technology for Geo-Exploration, Ministry of Education, Wuhan, China"}, {"AuthorId": 2, "Name": "Weihua Cao", "Affiliation": "School of Automation, China University of Geosciences, Wuhan, China; Hubei Key Laboratory of Advanced Control and Intelligent Automation for Complex Systems, China University of Geosciences, Wuhan, China; Engineering Research Center of Intelligent Technology for Geo-Exploration, Ministry of Education, Wuhan, China; Corresponding author."}, {"AuthorId": 3, "Name": "Wenkai Hu", "Affiliation": "School of Automation, China University of Geosciences, Wuhan, China; Hubei Key Laboratory of Advanced Control and Intelligent Automation for Complex Systems, China University of Geosciences, Wuhan, China; Engineering Research Center of Intelligent Technology for Geo-Exploration, Ministry of Education, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Automation, China University of Geosciences, Wuhan, China; Hubei Key Laboratory of Advanced Control and Intelligent Automation for Complex Systems, China University of Geosciences, Wuhan, China; Engineering Research Center of Intelligent Technology for Geo-Exploration, Ministry of Education, Wuhan, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Automation, China University of Geosciences, Wuhan, China; Hubei Key Laboratory of Advanced Control and Intelligent Automation for Complex Systems, China University of Geosciences, Wuhan, China; Engineering Research Center of Intelligent Technology for Geo-Exploration, Ministry of Education, Wuhan, China"}], "References": [{"Title": "Non-convex Total Variation Regularization for Convex Denoising of Signals", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "6-7", "Page": "825", "JournalTitle": "Journal of Mathematical Imaging and Vision"}, {"Title": "Non-convex Total Variation Regularization for Convex Denoising of Signals", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "6-7", "Page": "825", "JournalTitle": "Journal of Mathematical Imaging and Vision"}, {"Title": "Image denoising via structure-constrained low-rank approximation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12575", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Trainable TV-$$L^1$$ model as recurrent nets for low-level vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "18", "Page": "14603", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 119409261, "Title": "Deep learning-based localization and segmentation of wrist fractures on X-ray radiographs", "Abstract": "<p>X-rays are the primary tools in examining the suspected fractures in humans in this era. Manual examination of X rays is a time-consuming process, which requires expert radiologists or trained orthopaedic surgeons. The demanding workloads, unavailability of radiologists in small setups and at primary/community health centres results in the inability to diagnose and delay in the treatment due to unnecessary referrals by the primary care clinicians. Moreover, the shortage of expert radiologists and orthopaedic surgeons in medically under-resourced areas such as rural areas in India have motivated us to develop an automated fracture detection model. We have developed a deep neural network to detect, localize and divide the wrist region into segments to identify fractures around wrist joint in radiographs. The orthopaedic surgeon has manually annotated the fractures by drawing a bounding box and segmented mask. We have utilized two different domains of datasets for the better convergence of the model. There is a similarity in the crack patterns of the surface crack image dataset and the wrist fracture dataset, which consists of 3000 and 315 images. A part of the dataset is made publicly available for research purposes to overcome data collection and labelling barriers for identifying wrist fractures. The crack patterns in the wrist bone samples are learned by effectively transferring the knowledge or weights obtained from surface crack datasets. The proposed architecture utilizes Feature Pyramid Network as the backbone architecture where the last-level max pool layer of the architecture is replaced with the concatenation of AdaptiveConcatPool, AdaptiveAvgPool, AdaptiveMaxPool layers. Additionally, the concept of freezing and unfreezing the network during two phases of transfer learning is utilized for better model convergence. Every radiograph is assigned a ground truth label for evaluating the accuracy of the model. The performance measure for fracture detection and localization is evaluated using the Average precision value using the concept of Intersection over Union. The result of the proposed model is compared against the ground truth label annotated by the radiologists and related studies. The average precision of 92.278 on 50° and 79.003 on a strict scale of 75° was reported for fracture detection. Similarly, the average precision of 77.445 on 50° and 52.156 on a strict scale of 75° was reported for fracture segmentation.</p>", "Keywords": "Deep learning; Radiology; Medical imaging; Convolutional neural networks; X-rays; Object detection", "DOI": "10.1007/s00521-022-07510-z", "PubYear": 2022, "Volume": "34", "Issue": "21", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Petroleum and Energy Studies (UPES), Dehradun, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Petroleum and Energy Studies (UPES), Dehradun, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Government Doon Medical College, Dehradun, India"}], "References": [{"Title": "A survey of fracture detection techniques in bone X-ray images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "6", "Page": "4475", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Progressive Transfer Learning Approach for Identifying the Leaf Type by Optimizing Network Parameters", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "5", "Page": "3653", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 119409262, "Title": "Feature extraction and neural network-based fatigue damage detection and classification", "Abstract": "<p>This paper proposes a methodology for detection and classification of fatigue damage in mechanical structures in the framework of neural networks (NN). The proposed methodology has been tested and validated with polycrystalline-alloy (AL7075-T6) specimens on a laboratory-scale experimental apparatus. Signal processing tools (e.g., discrete wavelet transform and Hilbert transform) have been applied on time series of ultrasonic test signals to extract features that are derived from: (i) Signal envelope, (ii) Low-frequency and high-frequency signal spectra, and (iii) Signal energy. The performance of the neural network, combined with each one of these features, is compared with the ground truth, generated from the original ultrasonic test signals and microscope images. The results show that the NN model, combined with the signal-energy feature, yields the best performance and that it is capable of detecting and classifying the fatigue damage with (up to) 98.5% accuracy.</p>", "Keywords": "Neural networks; Discrete wavelet transform; Hilbert transform", "DOI": "10.1007/s00521-022-07609-3", "PubYear": 2022, "Volume": "34", "Issue": "23", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Taibah University, Medina, Saudi Arabia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Pennsylvania State University, University Park, USA; Department of Mathematics, Pennsylvania State University, University Park, USA"}], "References": []}, {"ArticleId": 119409263, "Title": "A new hand-modeled learning framework for driving fatigue detection using EEG signals", "Abstract": "<p>Fatigue detection is a critical application area for machine learning, and variable input data have been utilized to detect fatigue. One of the most commonly used inputs for fatigue detection is electroencephalography (EEG) signals. The main objective of this study is to accurately detect fatigue using a hand-crafted framework. To achieve this, a new signal classification framework has been proposed, and its performance has been tested on an EEG fatigue detection dataset. Wavelet packet decomposition with 16 mother wavelet functions has been utilized to extract features from the frequency domain and create a multilevel feature extraction method to calculate frequency subbands. To generate classification results, two validation techniques, tenfold cross-validation and leave-one-subject-out (LOSO) validation, have been applied to attain robust classification results. The proposed framework achieved high classification performance with 99.90% and 82.08% classification accuracies using tenfold CV and LOSO CV, respectively. Furthermore, the classification performance of each used method in our framework has been analyzed to understand the driving fatigue classification effect of the machine learning functions used. The proposed framework attained superior classification results, demonstrating its efficacy in accurately detecting fatigue.</p>", "Keywords": "EEG fatigue detection; Signal classification framework; Wavelet packet decomposition; Textural feature extraction", "DOI": "10.1007/s00521-023-08491-3", "PubYear": 2023, "Volume": "35", "Issue": "20", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Digital Forensics Engineering, Technology Faculty, Firat University, Elazig, Turkey; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Elazig Governorship, Interior Ministry, Elazig, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, College of Engineering, Ardahan University, Ardahan, Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Digital Forensics Engineering, Technology Faculty, Firat University, Elazig, Turkey"}], "References": [{"Title": "Smart methodology for safe life on roads with active drivers based on real-time risk and behavioral monitoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "11", "Page": "15361", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A new feature extraction technique based on improved owl search algorithm: a case study in copper electrorefining plant", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "10", "Page": "7749", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Automated accurate fire detection system using ensemble pretrained residual network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117407", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A robust optimal mean cosine angle 2DPCA for image feature extraction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "22", "Page": "20117", "JournalTitle": "Neural Computing and Applications"}, {"Title": "UncertaintyFuseNet: Robust uncertainty-aware hierarchical feature fusion model with Ensemble Monte Carlo Dropout for COVID-19 detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "90", "Issue": "", "Page": "364", "JournalTitle": "Information Fusion"}, {"Title": "Vehicle Interior Sound Classification Based on Local Quintet Magnitude Pattern and Iterative Neighborhood Component Analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "2137653", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "Energy enhancement of routing protocol with hidden Markov model in wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "7", "Page": "5381", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Novel favipiravir pattern-based learning model for automated detection of specific language impairment disorder using vowels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "8", "Page": "6065", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": *********, "Title": "Multi-window Transformer parallel fusion feature pyramid network for pedestrian orientation detection", "Abstract": "<p>In complex traffic scenes, the orientation and location of pedestrians are important criteria for judging their intentions. We note that pedestrians are characterized by variability in appearance and small differences among orientations (especially adjacent orientations), thus causing general object detection algorithms to perform poorly in extracting features. So, extracting more discriminative features is an effective way to solve this problem. To this end, we propose a novel framework to enhance feature extraction involving pedestrian orientation detection (orientation classification and location regression). The framework consists of two modules, multi-window Transformer parallel fusion feature pyramid (MTPF) and gated graph (GG). The MTPF module is used for multi-layer feature fusion, which improves the feature representation of the prediction map by extracting high-level semantic information from deep layers and recovering missing contextual information from shallow layers. Specifically, it is achieved by setting a sliding window on multiple prediction maps and fused by the Transformer. The region proposal is abstracted into a graph with six nodes in the GG module, where each node represents a body part. We utilize GG to learn the spatial dependencies among body parts and learn features by aggregating information from neighbors. Finally, pedestrian orientation classification and location regression are performed on a graph containing rich relationships among nodes. According to the survey, there are currently no methods and datasets that can be directly used for pedestrian orientation detection, so we manually annotate pedestrian orientations on three public datasets containing a large number of pedestrian samples, and compare the proposed method with the current state-of-the-art object detection methods by comparison, the results demonstrate the effectiveness of the proposed method.</p>", "Keywords": "Pedestrian orientation detection; Feature pyramid network; Multi-window Transformer parallel fusion; Gated graph", "DOI": "10.1007/s00530-022-00993-9", "PubYear": 2023, "Volume": "29", "Issue": "2", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Tianjin University of Technology, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Integrated Circuit Science and Engineering, Tianjin University of Technology, Tianjin, China; Corresponding author."}, {"AuthorId": 3, "Name": "Liqing Shan", "Affiliation": "School of Information Science and Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Tianjin University of Technology, Tianjin, China"}], "References": [{"Title": "CornerNet: Detecting Objects as Paired Keypoints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "3", "Page": "642", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Single-shot bidirectional pyramid networks for high-quality object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "401", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Question-relationship guided graph attention network for visual question answer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "2", "Page": "445", "JournalTitle": "Multimedia Systems"}, {"Title": "3D human pose estimation with multi-scale graph convolution and hierarchical body pooling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "2", "Page": "403", "JournalTitle": "Multimedia Systems"}, {"Title": "Multi-scale feature balance enhancement network for pedestrian detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "3", "Page": "1135", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 119409690, "Title": "Experimental investigation of the gate voltage range of negative differential capacitance in ferroelectric transistors", "Abstract": "Conclusion</h3> <p>In this study, the impacts of sweeping rate and device parameters on the gate voltage range of the NDC phenomenon are investigated based on experiments. With the increase of the frequency and dielectric capacitance, the beginning gate voltage of the NC emergence will increase, and the gate duration voltage range and the maximum gate voltage amplification factor appear to increase first and then decrease. This work suggests a big challenge for scaled NCFETs to operate at high frequency with low V <sub>DD</sub>.</p>", "Keywords": "", "DOI": "10.1007/s11432-021-3268-0", "PubYear": 2022, "Volume": "65", "Issue": "6", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices and Circuits (MOE), Institute of Microelectronics, Peking University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices and Circuits (MOE), Institute of Microelectronics, Peking University, Beijing, China; Beijing Laboratory of Future IC Technology and Science, Peking University, Beijing, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices and Circuits (MOE), Institute of Microelectronics, Peking University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices and Circuits (MOE), Institute of Microelectronics, Peking University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices and Circuits (MOE), Institute of Microelectronics, Peking University, Beijing, China; Beijing Laboratory of Future IC Technology and Science, Peking University, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Microelectronic Devices and Circuits (MOE), Institute of Microelectronics, Peking University, Beijing, China; Beijing Laboratory of Future IC Technology and Science, Peking University, Beijing, China; Corresponding author."}], "References": []}, {"ArticleId": 119409700, "Title": "Die primäre und sekundäre Nutzung elektronischer Gesundheitsdaten: Zum Vorschlag der EU-Kommission für einen Europäischen Gesundheitsdatenraum.", "Abstract": "Zusammenfassung</h3> <p>Am 3. Mai 2022 hat die EU-Kommission den Vorschlag einer Verordnung zur Schaffung eines europäischen Gesundheitsdatenraums veröffentlicht. Die Verordnung soll die Rechte von Patientinnen und Patienten stärken und gleichzeitig den freien Verkehr von Gesundheitsdaten fördern. Der Beitrag liefert dazu erste Einblicke in die Vorschriften zur primären und sekundären Nutzung von elektronischen Gesundheitsdaten. </p>", "Keywords": "", "DOI": "10.1007/s11623-022-1631-6", "PubYear": 2022, "Volume": "46", "Issue": "7", "JournalId": 6375, "JournalTitle": "Datenschutz und Datensicherheit - DuD", "ISSN": "1614-0702", "EISSN": "1862-2607", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON>nchen, Deutschland; Corresponding author."}], "References": []}, {"ArticleId": 119409716, "Title": "Full friendly index sets of mCn", "Abstract": "<p>Let G be a connected simple graph with vertex set V ( G ) and edge set E ( G ). A binary vertex labeling f : V ( G ) → ℤ<sub>2</sub>, is said to be friendly if the number of vertices with different labels differs by at most one. Each vertex friendly labeling f induces an edge labeling f *: E ( G ) → ℤ<sub>2</sub>, defined by f *( xy ) = f ( x ) + f ( y ) for each xy ∈ E ( G ). Let e <sub> f *</sub>( i ) = |{ e ∈ E ( G ): f *( e ) = i }|. The full friendly index set of G , denoted by FFI ( G ), is the set { e <sub>f</sub>*(1) − e <sub> f </sub>*(0): f is friendly}. In this paper, we determine the full friendly index set of a family of cycle union graphs which are edge subdivisions of P <sub>2</sub> × P <sub> n </sub>.</p>", "Keywords": "vertex labeling; friendly labeling; full friendly index set; partition; bisection", "DOI": "10.1007/s11704-021-0415-8", "PubYear": 2022, "Volume": "16", "Issue": "3", "JournalId": 4733, "JournalTitle": "Frontiers of Computer Science", "ISSN": "2095-2228", "EISSN": "2095-2236", "Authors": [{"AuthorId": 1, "Name": "Yurong Ji", "Affiliation": "School of Mathematics and Information Science, Henan Polytechnic University, Jiaozuo, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Basic Department, Henan College of Industry and Information Technology, Jiaozuo, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Information Science, Henan Polytechnic University, Jiaozuo, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Information Science, Henan Polytechnic University, Jiaozuo, China; Corresponding author."}], "References": []}, {"ArticleId": 119409755, "Title": "Correction: In Stars We Trust – A Note on Reputation Portability Between Digital Platforms", "Abstract": "", "Keywords": "", "DOI": "10.1007/s12599-022-00762-y", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 5738, "JournalTitle": "Business & Information Systems Engineering", "ISSN": "2363-7005", "EISSN": "1867-0202", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119409762, "Title": "Retraction Note to: Fuzzy signal strength estimated Markov probabilistic graph for efficient handover and seamless data delivery in PAN", "Abstract": "", "Keywords": "", "DOI": "10.1007/s12652-022-03964-0", "PubYear": 2023, "Volume": "14", "Issue": "S1", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Periyar University, Salem, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Periyar University, Salem, India"}], "References": []}, {"ArticleId": 119409771, "Title": "Konzernprivilegien in Compliance-Management-Systemen: Die Konzernlösung des Hinweisgeberschutzgesetzes", "Abstract": "Zusammenfassung</h3> <p>Konzernprivilegien sollen zur Entlastung von Unternehmen beitragen. Gleichzeitig stehen regelmäßig die Interessen von natürlichen Personen einer konsequenten Privilegierung entgegen. Dieser Artikel behandelt das angesprochene Spannungsfeld mit Schwerpunkt auf dem aktuellen Entwurf zum deutschen Hinweisgeberschutzgesetz. </p>", "Keywords": "", "DOI": "10.1007/s11623-023-1722-z", "PubYear": 2023, "Volume": "47", "Issue": "2", "JournalId": 6375, "JournalTitle": "Datenschutz und Datensicherheit - DuD", "ISSN": "1614-0702", "EISSN": "1862-2607", "Authors": [{"AuthorId": 1, "Name": "Falko D. <PERSON>", "Affiliation": "datenschutz nord GmbH, Bremen, Deutschland; Corresponding author."}], "References": []}, {"ArticleId": 119409829, "Title": "Diagnosis of Alzheimer’s disease using 2D dynamic magnetic resonance imaging", "Abstract": "<p>Alzheimer’s disease is a degenerative disease of the central nervous system that occurs primarily in old age. Magnetic resonance imaging (MRI) is the most commonly used type of brain medical image in clinical practice to determine the period of Alzheimer’s disease patients. Inspired by clinical practice, we propose using a 2D reparameterization CNN architecture to classify Alzheimer’s disease. The proposed method markedly improves the performance of Alzheimer’s disease classification in less time. By reparametrizing the attention mechanism, the proposed method achieves an AUC of 0.9849 and an ACC of 0.9625.</p>", "Keywords": "Alzheimer’s disease; Deep learning; Magnetic resonance imaging; Dynamic medical image", "DOI": "10.1007/s12652-021-03678-9", "PubYear": 2023, "Volume": "14", "Issue": "8", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China; Key Laboratory of Software Engineering Technology, Xinjiang University, Urumqi, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Network Center, Xinjiang University, Urumqi, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>g<PERSON> T<PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China; Key Laboratory of Software Engineering Technology, Xinjiang University, Urumqi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China; Key Laboratory of Software Engineering Technology, Xinjiang University, Urumqi, China"}], "References": [{"Title": "Visual saliency guided complex image retrieval", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "64", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "ABCDM: An Attention-based Bidirectional CNN-RNN Deep Model for sentiment analysis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "279", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A Comprehensive Survey of Neural Architecture Search", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 119409830, "Title": "Blockchain-based secure and transparent election and vote counting mechanism using secret sharing scheme", "Abstract": "<p>Privacy and transparency in vote counting are the most prevalent concerns these days due to the involvement of untrusted authorities in the counting process. As a result, the counting process faces significant privacy, trust, and transparency hurdles. Hence, there is a need for an efficient and trusted mechanism to resolve such problems. Blockchain technology has the potential to bring transparency and trust in several applications. Therefore, in this work, we explore blockchain technology in conjunction with a secure partitioning scheme to promote transparency, trust, and privacy between users and participating authorities in a decentralized platform. This paper presents a chaincode-based implementation of our proposed secure and verifiable vote counting mechanism that enables trust and fairness over a decentralized platform. Multiple authorities participate in the vote counting process in a trusted manner to cooperate and coordinate in a decision process over a decentralized platform. Our research exhibits that blockchain technology can eliminate the trust gaps and increase transparency and fairness in the election and vote counting procedure. We register user votes in the blockchain platform based on the secret sharing mechanism to enable fairness and openness between counting authorities. Each vote is recorded into the distributed ledger to support openness and verifiability in our mechanism. The ledger is accessible to every registered user as per the permissioned blockchain policy. We created many authorities in the blockchain network and deployed multiple smart contracts on the Hyperledger platform to analyze the feasibility of our strategy. The performance results are obtained and reported using the Hyperledger Caliper benchmark tool. The results demonstrate that the proposed chaincode-based solution achieves the highest throughput at 200–400 tps for fetching and removing contracts. We achieve the optimal latency of 18.09 s for the vote distribution contract and up to 4.57 s for the counting contract.</p>", "Keywords": "Blockchain; Smart contract; Election; Voter; Secret sharing", "DOI": "10.1007/s12652-022-04108-0", "PubYear": 2023, "Volume": "14", "Issue": "10", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Roorkee, Roorkee, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Delhi Technological University, Delhi, India"}], "References": [{"Title": "Trusted information project platform based on blockchain for sharing strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "3", "Page": "1575", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "RETRACTED ARTICLE: Enhanced security in cloud applications using emerging blockchain security algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "7", "Page": "6933", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Review and analysis of blockchain projects in supply chain management", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "180", "Issue": "", "Page": "724", "JournalTitle": "Procedia Computer Science"}, {"Title": "A conditional privacy-preserving fair electronic payment scheme based on blockchain without trusted third party", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "8", "Page": "10089", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Guaranteeing information integrity and access control in smart cities through blockchain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "9", "Page": "11419", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 119411133, "Title": "Object Tracking Using MEMS Microphone Arrays", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2022.115129", "PubYear": 2022, "Volume": "11", "Issue": "5", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119411238, "Title": "Rendimiento académico y patrones de uso del campus virtual: Un estudio de caso controlado", "Abstract": "", "Keywords": "", "DOI": "10.17013/risti.43.21-37", "PubYear": 2021, "Volume": "", "Issue": "43", "JournalId": 19482, "JournalTitle": "RISTI - Revista Ibérica de Sistemas e Tecnologias de Informação", "ISSN": "", "EISSN": "1646-9895", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> de la Iglesia Villasol", "Affiliation": "Universidad Complutense de Madrid"}], "References": []}, {"ArticleId": 119411310, "Title": "Small Signal Modeling, Control and Experimentation of Boost Converter Including Parasitic Elements", "Abstract": "<p>This paper presents an improved state space average model of a boost DC–DC converter considering all the parasitic elements present in the circuit. The small signal transfer function derived from the modified averaged model is used to design an improved voltage and current mode cascaded control (CMC) using linear quadratic regulator. Major improvements in the proposed CMC technique include improved performance (overshoot, undershoot and settling time for both voltage and current) compared to the conventional controllers and the protection system for limiting overcurrent flow through the circuit. The performance of the proposed controller is evaluated by experimental results. The proposed controller performance is compared with voltage mode as well as CMC-based PI controllers. The experimental results show that the proposed controller improves the ability to track the instantaneous change in reference voltage, maintain stable output voltage during load change and show robustness against converter component value change and exceeding the maximum current limit.</p>", "Keywords": "DC–DC converter; Boost converter; Linear quadratic regulator; Small signal analysis; Parasitic element", "DOI": "10.1007/s40313-021-00735-8", "PubYear": 2021, "Volume": "32", "Issue": "4", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, The University of Alabama, Tuscaloosa, USA; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Zhengzhou University, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, The University of Alabama, Tuscaloosa, USA"}, {"AuthorId": 4, "Name": "Weizhen Dong", "Affiliation": "Department of ECE, The University of Alabama, Tuscaloosa, USA"}], "References": [{"Title": "Hybrid Operational High Step-Up DC–DC Converter", "Authors": "Aluísio Alves de Melo Bento", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "350", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}]}, {"ArticleId": 119411318, "Title": "Design of Discrete Noniterative Algorithms for Center-of-Sets Type Reduction of General Type-2 Fuzzy Logic Systems", "Abstract": "<p>According to the alpha-planes expression theory of general type-2 fuzzy sets, this paper completes the center-of-sets (COS) type reduction and defuzzification for general type-2 fuzzy logic systems (GT2 FLSs). In fact, it still remains an open problem by comparing the prevalent Karnik–Mendel (KM) algorithms and other types of alternative noniterative algorithms. The modules of fuzzy inference, COS type reduction, and defuzzification of Mamdani-type GT2 FLSs on the basis of Nagar-Bardini algorithms, Nie-Tan algorithms, and Begian-Melek-Mendel algorithms are also provided. Six simulation examples are provided to illustrate the performances of corresponding noniterative algorithms. In contrast to the KM algorithms, the suggested three types of noniterative algorithms can get faster convergence speeds.</p>", "Keywords": "Center-of-sets type reduction; General type-2 fuzzy logic systems; Alpha-planes; Noniterative algorithms; Convergence speeds", "DOI": "10.1007/s40815-022-01256-5", "PubYear": 2022, "Volume": "24", "Issue": "4", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Science, Liaoning University of Technology, City of Jinzhou, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Science, Liaoning University of Technology, City of Jinzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Science, Liaoning University of Technology, City of Jinzhou, China"}], "References": [{"Title": "Algorithm for solving group decision-making problems based on the similarity measures under type 2 intuitionistic fuzzy sets environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "10", "Page": "7361", "JournalTitle": "Soft Computing"}, {"Title": "Design of an interval Type-2 fuzzy model with justifiable uncertainty", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "513", "Issue": "", "Page": "206", "JournalTitle": "Information Sciences"}, {"Title": "Comparative study of interval Type-2 and general Type-2 fuzzy systems in medical diagnosis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "525", "Issue": "", "Page": "37", "JournalTitle": "Information Sciences"}, {"Title": "Study on sampling-based discrete noniterative algorithms for centroid type-reduction of interval type-2 fuzzy logic systems", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "15", "Page": "11819", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 119411326, "Title": "The world trade network: country centrality and the COVID-19 pandemic", "Abstract": "<p>International trade is based on a set of complex relationships between different countries that can be modelled as an extremely dense network of interconnected agents. On the one hand, this network might favour the economic growth of countries, but on the other, it can also favour the diffusion of diseases, such as COVID-19. In this paper, we study whether, and to what extent, the topology of the trade network can explain the rate of COVID-19 diffusion and mortality across countries. We compute the countries’ centrality measures and we apply the community detection methodology based on communicability distance. We then use these measures as focal regressors in a negative binomial regression framework. In doing so, we also compare the effects of different measures of centrality. Our results show that the numbers of infections and fatalities are larger in countries with a higher centrality in the global trade network.</p>", "Keywords": "World trade network;Centrality measures;Community detection;COVID-19;91B60;05C82;62P20", "DOI": "10.1007/s41109-022-00452-4", "PubYear": 2022, "Volume": "7", "Issue": "1", "JournalId": 5330, "JournalTitle": "Applied Network Science", "ISSN": "", "EISSN": "2364-8228", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Economics and Management, University of Padova, Padova, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Economics and Management, University of Brescia, Brescia, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Economics and Management, University of Padova, Padova, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics and Quantitative Methods, University of Milano - Bicocca, Milan, Italy; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Statistics and Quantitative Methods, University of Milano - Bicocca, Milan, Italy"}], "References": [{"Title": "Multi-Attribute Community Detection in International Trade Network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "3", "Page": "707", "JournalTitle": "Networks and Spatial Economics"}]}, {"ArticleId": 119411353, "Title": "Lessons from human vision for robotic design", "Abstract": "<p>The visual guidance of goal-directed movements requires transformations of incoming visual information that are different from those required for visual perception. For us to grasp an object successfully, our brain must use just-in-time computations of the object’s real-world size and shape, and its orientation and disposition with respect to our hand. These requirements have led to the emergence of dedicated visuomotor modules in the posterior parietal cortex of the human brain (the dorsal visual stream) that are functionally distinct from networks in the occipito-temporal cortex (the ventral visual stream) that mediate our conscious perception of the world. Although the identification and selection of goal objects and an appropriate course of action depends on the perceptual machinery of the ventral stream and associated cognitive modules, the execution of the subsequent goal-directed action is mediated by dedicated online control systems in the dorsal stream and associated motor areas. The dorsal stream allows an observer to reach out and grasp objects with exquisite ease, but by itself, deals only with objects that are visible at the moment the action is being programmed. The ventral stream, however, allows an observer to escape the present and bring to bear information from the past – including information about the function of objects, their intrinsic properties, and their location with reference to other objects in the world. Ultimately then, both streams contribute to the production of goal-directed actions. The principles underlying this division of labour between the dorsal and ventral streams are relevant to the design and implementation of autonomous robotic systems.</p>", "Keywords": "Perception vs. action; Dorsal visual stream; Ventral visual stream; Tele-assistance; Grasping", "DOI": "10.1007/s43684-021-00002-2", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 90531, "JournalTitle": "Autonomous Intelligent Systems", "ISSN": "", "EISSN": "2730-616X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The Brain and Mind Institute, The University of Western Ontario, London, Canada; Department of Psychology, The University of Western Ontario, London, ON, Canada; Corresponding author."}], "References": []}, {"ArticleId": 119411356, "Title": "Annealing of Monel 400 Alloy Using Principal Component Analysis, Hyper-parameter Optimization, Machine Learning Techniques, and Multi-objective Particle Swarm Optimization", "Abstract": "<p>The purpose of this paper is to investigate the effect of the annealing process at 1000 °C on machining parameters using contemporary techniques such as principal component analysis (PCA), hyper-parameter optimization by Optuna, multi-objective particle swarm optimization, and theoretical validation using the machine learning method. Results after annealing show that there will be a reduction in surface roughness values by 19.61%, tool wear by 6.3%, and an increase in the metal removal rate by 14.98%. The PCA results show that the feed is more significant than the depth of cut and speed. The higher value of the composite primary component will represent optimal factors such as speed of 80, feed of 0.2 and depth of cut of 0.3, and values of principal components like surface roughness ( Ψ <sub>1</sub> = 64.5), tool wear ( Ψ <sub>2</sub> = 22.3) and metal removal rate ( Ψ <sub>3</sub> = 13.2). Hyper-parameter optimization represents speed is directly proportional to roughness, tool wear, and metal removal rate, while feed and depth of cut are inversely proportional. The optimization history plot will be steady, and the prediction accuracy will be 96.96%. Machine learning techniques are employed through the Python language using Google Colab. The estimated values from the decision tree method for surface roughness and tool wear predictions using the AdaBoost algorithm match well with actual values. As per MOPSO (multi-objective particle swarm optimization), the predicted responses are as follows; surface roughness (2.5 μm, 100, 02, 0.45), tool wear (0.31 mm, 40, 0.40, 0.60), and MRR (material removal rate) (5145 mm<sup>3</sup>/min, 100, 0.4, 0.15). As validated by experimentation, there are small variations as the surface roughness varied by 1.56%, tool wear by 6.8%, and MRR by 2.57%.</p>", "Keywords": "Monel-400; Annealing; Principal component analysis; Optuna; Machine learning; Multi-objective particle swarm optimization", "DOI": "10.1007/s44196-022-00070-z", "PubYear": 2022, "Volume": "15", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Industrial Engineering Department, College of Engineering, King Saud University, Riyadh, Saudi Arabia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Industrial Engineering Department, College of Engineering, King Saud University, Riyadh, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Advanced Manufacturing Institute, King Saud University, Riyadh, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Leicester University, Leicester, UK"}], "References": [{"Title": "On hyperparameter optimization of machine learning algorithms: Theory and practice", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}, {"Title": "Multi-criteria decision making approach based on SVTrN Dombi aggregation functions", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "5", "Page": "3685", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Optimal 5G network slicing using machine learning and deep learning concepts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "76", "Issue": "", "Page": "103518", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "Fuzzy harmony search based optimal control strategy for wireless cyber physical system with industry 4.0", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "6", "Page": "1795", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 119411404, "Title": "Analysis of the global trade network using exponential random graph models", "Abstract": "<p>The global trade network has significant importance in analyzing countries’ economic exchanges. Therefore, studying the global trade network and the factors influencing its structure is helpful for both economists and political decision makers. Putting these in mind, we try to analyze the global trade network from various viewpoints. We use the backbone filtering methods to construct a network of essential trades between countries. We analyze the structural, economic, geographical, political, and cultural factors and their effect on the global trade network using exponential random graph models. Additionally, we analyze the global trade network evolution using the separable temporal exponential random models. Our results show multiple structural, economic, geographical, and political factors affect the global trade network structure.</p>", "Keywords": "Complex networks;Trade networks;Exponential random graph models;Temporal exponential random graph models", "DOI": "10.1007/s41109-022-00479-7", "PubYear": 2022, "Volume": "7", "Issue": "1", "JournalId": 5330, "JournalTitle": "Applied Network Science", "ISSN": "", "EISSN": "2364-8228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tehran, Tehran, Iran; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Tehran, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Tehran, Tehran, Iran"}], "References": [{"Title": "Quantitative analysis of trade networks: data and robustness", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Applied Network Science"}]}, {"ArticleId": 119411424, "Title": "A neural network approach for wireless spectrum anomaly detection in 5G-unlicensed network", "Abstract": "<p> The 5G New Radio Unlicensed (5G-U) technology has enabled manufacturing enterprises to deploy their own private industrial networks, making anomaly event detection more necessary for maintaining wireless communication quality. However, existing statistical analysis algorithms cannot efficiently and accurately detect various kinds of anomaly events caused by the complex industrial environment. These events include electromagnetic interference as well as contention between cross-technology devices for unlicensed spectrum resources. To improve the efficiency, we design a classification algorithm that uses feature extraction in the frequency domain and a convolutional neural network model to detect various kinds of anomaly events (e.g., loose antennas and co-channel interference). We prototyped Slade (Spectrum Learning for Anomaly Detection), an anomaly detection system for industrial 5G networks. To evaluate the system, we collect wireless spectrum data with two industrial 5G-U terminals. Our evaluation on the dataset shows that our methodology can accurately detect different anomaly events, with an accuracy of 97.6% and a recall of 97.1%.</p>", "Keywords": "Anomaly detection; 5G-U; Spectrum sensing; Feature extraction; Automatic diagnosis", "DOI": "10.1007/s42486-021-00075-1", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 60068, "JournalTitle": "CCF Transactions on Pervasive Computing and Interaction", "ISSN": "2524-521X", "EISSN": "2524-5228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Energy-Efficient Computing and Applications, Peking University, Beijing, China; Corresponding author."}, {"AuthorId": 2, "Name": "Xiangtian <PERSON>", "Affiliation": "Center for Energy-Efficient Computing and Applications, Peking University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Energy-Efficient Computing and Applications, Peking University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Energy-Efficient Computing and Applications, Peking University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Energy-Efficient Computing and Applications, Peking University, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Beijing Yunzhiruantong Info Tech Ltd., Beijing, China"}, {"AuthorId": 7, "Name": "Linghe Kong", "Affiliation": "Shanghai Jiao Tong University, Shanghai, China"}], "References": []}, {"ArticleId": 119411467, "Title": "Categorizing white blood cells by utilizing deep features of proposed 4B-AdditionNet-based CNN network with ant colony optimization", "Abstract": "<p>White blood cells, WBCs for short, are an essential component of the human immune system. These cells are our body's first line of defense against infections and diseases caused by bacteria, viruses, and fungi, as well as abnormal and external substances that may enter the bloodstream. A wrong WBC count can signify dangerous viral infections, autoimmune disorders, cancer, sarcoidosis, aplastic anemia, leukemia, tuberculosis, etc. A lot of these diseases and disorders can be extremely painful and often result in death. Leukemia is among the more common types of blood cancer and when left undetected leads to death. An early diagnosis is necessary which is possible by looking at the shapes and determining the numbers of young and immature WBCs to see if they are normal or not. Performing this task manually is a cumbersome, expensive, and time-consuming process for hematologists, and therefore computer-aided systems have been developed to help with this problem. This paper proposes an improved method of classification of WBCs utilizing a combination of preprocessing, convolutional neural networks (CNNs), feature selection algorithms, and classifiers. In preprocessing, contrast-limited adaptive histogram equalization (CLAHE) is applied to the input images. A CNN is designed and trained to be used for feature extraction along with ResNet50 and EfficientNetB0 networks. Ant colony optimization is used to select the best features which are then serially fused and passed onto classifiers such as support vector machine (SVM) and quadratic discriminant analysis (QDA) for classification. The classification accuracy achieved on the Blood Cell Images dataset is 98.44%, which shows the robustness of the proposed work.</p>", "Keywords": "White blood cells; CNN; Classification; Preprocessing; Fusion; Selection", "DOI": "10.1007/s40747-021-00564-x", "PubYear": 2022, "Volume": "8", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Campus, Wah Cantt, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Campus, Wah Cantt, Pakistan; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Campus, Wah Cantt, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of IS&E, Canara Engineering College, Mangaluru, India"}], "References": [{"Title": "RETRACTED ARTICLE: Interpolative Leishman-Stained transformation invariant deep pattern classification for white blood cells", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "16", "Page": "12215", "JournalTitle": "Soft Computing"}, {"Title": "An Automatic Nucleus Segmentation and CNN Model based Classification Method of White Blood Cell", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113211", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Classification of white blood cells using deep features obtained from Convolutional Neural Network models based on the combination of feature selection methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106810", "JournalTitle": "Applied Soft Computing"}, {"Title": "Improving Recurrence Prediction Accuracy of Ovarian Cancer Using Multi-phase Feature Selection Methodology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "3", "Page": "206", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "A decision support system for multimodal brain tumor classification using deep learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "4", "Page": "3007", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A framework for offline signature verification system: Best features selection approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "50", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Facial expressions classification and false label reduction using LDA and threefold SVM", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "166", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A distinctive approach in brain tumor detection and classification using MRI", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "118", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 119411637, "Title": "Evaluation of a User Authentication Schema Using Behavioral Biometrics and Machine Learning", "Abstract": "<p>The amount of secure data being stored on mobile devices has grown immensely in recent years. However, the security measures protecting this data have stayed static, with few improvements being done to the vulnerabilities of current authentication methods such as physiological biometrics or passwords. Instead of these methods, behavioral biometrics has recently been researched as a solution to these vulnerable authentication methods. In this study, we aim to contribute to the research being done on behavioral biometrics by creating and evaluating a user authentication scheme using behavioral biometrics. The behavioral biometrics used in this study include touch dynamics and phone movement, and we evaluate the performance of different single-modal and multi-modal combinations of the two biometrics. Using two publicly available datasets - BioIdent and Hand Movement Orientation and Grasp (H-MOG), this study uses seven common machine learning algorithms to evaluate performance. The algorithms used in the evaluation include Random Forest, Support Vector Machine, K-Nearest Neighbor, Naive Bayes, Logistic Regression, Multilayer Perceptron, and Long Short-Term Memory Recurrent Neural Networks, with accuracy rates reaching as high as 86%.</p>", "Keywords": "", "DOI": "10.5539/cis.v15n3p1", "PubYear": 2022, "Volume": "15", "Issue": "3", "JournalId": 16023, "JournalTitle": "Computer and Information Science", "ISSN": "1913-8989", "EISSN": "1913-8997", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>-<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119411779, "Title": "Geographies of Place in Digital Art History", "Abstract": "<p>Art history research examines objects as embedded in a web of relationships, including multiple spatial dimensions (e.g. of the materials, of the artist, of the cultural influences, of the museum collection, and of the temporary exhibitions). However, this richness of nuances is not yet fully encompassed in Linked Open Data standards. This paper aims to examine how the multiplicity of places entangled in art objects can be represented within existing vocabularies (Getty Thesaurus of Geographic Names, Geonames, Pleiades, Trismegistos), ontologies ( CIDOC CRM, Europeana Data Model, Linked Ancient World Data, Wikidata, LIDO) and interconnection formats (Pelagios, World Historical Gazetteer, SENESCHAL, Linked Art). In doing so, this article raises a series of questions concerning the potential and limitation of current solutions for representing geographical information. It highlights the needs for inclusive, interoperable, open and accessible features in LOD systems in spatial humanities and it traces possible areas of inquiry for further research. The article argues that there is the need to develop more granular and comprehensive solutions for encompassing the multiplicity of places that can be enclosed in an art object, and its itineraries across time and space.</p>", "Keywords": "Linked Open Data; spatial humanities; geography; digital art history; cultural heritage; ancient world", "DOI": "10.3366/ijhac.2022.0279", "PubYear": 2022, "Volume": "16", "Issue": "1", "JournalId": 35956, "JournalTitle": "International Journal of Humanities and Arts Computing", "ISSN": "1753-8548", "EISSN": "1755-1706", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119412121, "Title": "Geldwäscheprävention und Datenschutz. Falsche Freunde oder grundlegende Gegner?", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2022.03.11", "PubYear": 2022, "Volume": "", "Issue": "3", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119412192, "Title": "Auftragsverarbeitung unter Beteiligung kirchlicher Unternehmen", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2022.03.06", "PubYear": 2022, "Volume": "", "Issue": "3", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119412367, "Title": "Development and Governance of FAIR Thresholds for a Data Federation", "Abstract": "", "Keywords": "", "DOI": "10.5334/dsj-2022-013", "PubYear": 2022, "Volume": "21", "Issue": "", "JournalId": 11331, "JournalTitle": "Data Science Journal", "ISSN": "", "EISSN": "1683-1470", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Constraint Solving Approaches to the Business-to-Business Meeting Scheduling Problem", "Abstract": "<p>The Business-to-Business Meeting Scheduling problem consists of scheduling a set of meetings between given pairs of participants to an event, while taking into account participants’ availability and accommodation capacity. A crucial aspect of this problem is that breaks in participants’ schedules should be avoided as much as possible. It constitutes a challenging combinatorial problem that needs to be solved for many real world brokerage events.\r In this paper we present a comparative study of Constraint Programming (CP), MixedInteger Programming (MIP) and Maximum Satisfiability (MaxSAT) approaches to this problem. The CP approach relies on using global constraints and has been implemented in MiniZinc to be able to compare CP, Lazy Clause Generation and MIP as solving technologies in this setting. We also present a pure MIP encoding. Finally, an alternative viewpoint is considered under MaxSAT, showing best performance when considering some implied constraints. Experiments conducted on real world instances, as well as on crafted ones, show that the MaxSAT approach is the one with the best performance for this problem, exhibiting better solving times, sometimes even orders of magnitude smaller than CP and MIP.</p>", "Keywords": "constraint satisfaction;scheduling;problem solving;satisfiability", "DOI": "10.1613/jair.1.12670", "PubYear": 2022, "Volume": "74", "Issue": "", "JournalId": 56494, "JournalTitle": "Journal of Artificial Intelligence Research", "ISSN": "", "EISSN": "1076-9757", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "a:1:{s:5:\"en_US\";s:20:\"University of Girona\";}"}], "References": []}, {"ArticleId": 119412708, "Title": "Impact of Imputation Strategies on Fairness in Machine Learning", "Abstract": "<p>Research on Fairness and Bias Mitigation in Machine Learning often uses a set of reference datasets for the design and evaluation of novel approaches or definitions. While these datasets are well structured and useful for the comparison of various approaches, they do not reflect that datasets commonly used in real-world applications can have missing values. When such missing values are encountered, the use of imputation strategies is commonplace. However, as imputation strategies potentially alter the distribution of data they can also affect the performance, and potentially the fairness, of the resulting predictions, a topic not yet well understood in the fairness literature. In this article, we investigate the impact of different imputation strategies on classical performance and fairness in classification settings. We find that the selected imputation strategy, along with other factors including the type of classification algorithm, can significantly affect performance and fairness outcomes. The results of our experiments indicate that the choice of imputation strategy is an important factor when considering fairness in Machine Learning. We also provide some insights and guidance for researchers to help navigate imputation approaches for fairness.</p>", "Keywords": "machine learning", "DOI": "10.1613/jair.1.13197", "PubYear": 2022, "Volume": "74", "Issue": "", "JournalId": 56494, "JournalTitle": "Journal of Artificial Intelligence Research", "ISSN": "", "EISSN": "1076-9757", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University College Dublin"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Nebraska at Omaha"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Strategy and Innovation, Vienna University of Economics and Business (WU)"}], "References": []}, {"ArticleId": 119412713, "Title": "FlowDNN:一种用于快速精确流场预测的物理启发深度神经网络", "Abstract": "<p>for flow-related design optimization problems, e.g., aircraft and automobile aerodynamic design, computational fluid dynamics (CFD) simulations are commonly used to predict flow fields and analyze performance. While important, CFD simulations are a resource-demanding and time-consuming iterative process. The expensive simulation overhead limits the opportunities for large design space exploration and prevents interactive design. In this paper, we propose FlowDNN, a novel deep neural network (DNN) to efficiently learn flow representations from CFD results. FlowDNN saves computational time by directly predicting the expected flow fields based on given flow conditions and geometry shapes. FlowDNN is the first DNN that incorporates the underlying physical conservation laws of fluid dynamics with a carefully designed attention mechanism for steady flow prediction. This approach not only improves the prediction accuracy, but also preserves the physical consistency of the predicted flow fields, which is essential for CFD. Various metrics are derived to evaluate FlowDNN with respect to the whole flow fields or regions of interest (RoIs) (e.g., boundary layers where flow quantities change rapidly). Experiments show that FlowDNN significantly outperforms alternative methods with faster inference and more accurate results. It speeds up a graphics processing unit (GPU) accelerated CFD solver by more than 14 000×, while keeping the prediction error under 5%.</p>", "Keywords": "Deep neural network; Flow prediction; Attention mechanism; Physics-informed loss; 深度神经网络; 流场预测性能; 注意机制; 物理损失函数; TP391", "DOI": "10.1631/FITEE.2000435", "PubYear": 2022, "Volume": "23", "Issue": "2", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, China; State Key Laboratory of High Performance Computing, National University of Defense Technology, Changsha, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, China; State Key Laboratory of High Performance Computing, National University of Defense Technology, Changsha, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, China; State Key Laboratory of High Performance Computing, National University of Defense Technology, Changsha, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Computing, University of Leeds, Leeds, UK"}], "References": []}, {"ArticleId": 119412788, "Title": "Supervised Visual Attention for Simultaneous Multimodal Machine Translation", "Abstract": "<p>There has been a surge in research in multimodal machine translation (MMT), where additional modalities such as images are used to improve translation quality of textual systems. A particular use for such multimodal systems is the task of simultaneous machine translation, where visual context has been shown to complement the partial information provided by the source sentence, especially in the early phases of translation. In this paper, we propose the first Transformer-based simultaneous MMT architecture, which has not been previously explored in simultaneous translation. Additionally, we extend this model with an auxiliary supervision signal that guides the visual attention mechanism using labelled phrase-region alignments. We perform comprehensive experiments on three language directions and conduct thorough quantitative and qualitative analyses using both automatic metrics and manual inspection. Our results show that (i) supervised visual attention consistently improves the translation quality of the simultaneous MMT models, and (ii) fine-tuning the MMT with supervision loss enabled leads to better performance than training the MMT from scratch. Compared to the state-of-the-art, our proposed model achieves improvements of up to 2.3 BLEU and 3.5 METEOR points.</p>", "Keywords": "machine translation;neural networks;natural language;vision", "DOI": "10.1613/jair.1.13546", "PubYear": 2022, "Volume": "74", "Issue": "", "JournalId": 56494, "JournalTitle": "Journal of Artificial Intelligence Research", "ISSN": "", "EISSN": "1076-9757", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Imperial College London"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "a:1:{s:5:\"en_US\";s:23:\"Imperial College London\";}"}, {"AuthorId": 3, "Name": "Lucia <PERSON>", "Affiliation": "Imperial College London"}], "References": []}, {"ArticleId": 119412868, "Title": "基于对称多项式辅助的中国余数定理的脉冲多普勒雷达多目标距离估计算法", "Abstract": "<p>To avoid Doppler ambiguity, pulse Doppler radar may operate on a high pulse repetition frequency (PRF). The use of a high PRF can, however, lead to range ambiguity in many cases. At present, the major efficient solution to solve range ambiguity is based on a waveform design scheme. It adds complexity to a radar system. However, the traditional multiple-PRF-based scheme is difficult to be applied in multiple targets because of unknown correspondence between the target range and measured range, especially using the Chinese remainder theorem (CRT) algorithm. We make a study of the CRT algorithm for multiple targets when the residue set contains noise error. In this paper, we present a symmetry polynomial aided CRT algorithm to effectively achieve range estimation of multiple targets when the measured ranges are overlapped with noise error. A closed-form and robust CRT algorithm for single target and the Aitken acceleration algorithm for finding roots of a polynomial equation are used to decrease the computational complexity of the proposed algorithm.</p>", "Keywords": "Range ambiguity; Erroneous range; Multiple targets; Symmetry polynomial aided Chinese remainder theorem; 距离模糊; 误差距离; 多目标; 对称多项式辅助的中国余数定理; TN953", "DOI": "10.1631/FITEE.2000418", "PubYear": 2022, "Volume": "23", "Issue": "2", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Lab of Radar Signal Processing, Xidian University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Zhao", "Affiliation": "National Lab of Radar Signal Processing, Xidian University, Xi’an, China; Information Sensing and Understanding, Xidian University, Xi’an, China; Corresponding author."}], "References": []}, {"ArticleId": 119412880, "Title": "What is “fake news” and “hate speech” and how do they work in practice?", "Abstract": "", "Keywords": "", "DOI": "10.24989/ocg.v.342.1", "PubYear": 2022, "Volume": "342", "Issue": "", "JournalId": 78644, "JournalTitle": "Central and Eastern European eDem and eGov Days", "ISSN": "2520-3401", "EISSN": "2663-9394", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119412881, "Title": "Open Government & Open Data as a feasible solution?", "Abstract": "", "Keywords": "", "DOI": "10.24989/ocg.v.342.5", "PubYear": 2022, "Volume": "342", "Issue": "", "JournalId": 78644, "JournalTitle": "Central and Eastern European eDem and eGov Days", "ISSN": "2520-3401", "EISSN": "2663-9394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119412886, "Title": "Network Security Threats to Higher Education Institutions", "Abstract": "<p>The Covid-19 pandemic has significantly changed the way higher education institutions (HEIs) operate around the world. Distance learning has become the unique opportunity that the process further education in conditions where most economic activities were put on hold. To ensure the quality of distance learning have been implemented and used extensively online learning platforms, applications, video conferencing and cloud computing facilities in HEIs. However, this has increased the threats to information security, so that in 2020, in the field of education in general, and HEIs in particular, the number of cyber-attacks has increased, which has led to significant financial losses but also to activity interruption and theft of personal data or intellectual property. In order to identify the biggest threats of the 2020 year, for HEIs, several security reports and scientific articles related to the studied field were analyzed, in order to identify the most common security threats. As a result, of the research conducted, the top Cyber threats of HEIs are: malware attacks, DoS / DDos attacks and phishing attacks. Securing university networks in 2020 was a challenge for specialists in this field.</p>", "Keywords": "", "DOI": "10.24989/ocg.v341.24", "PubYear": 2022, "Volume": "341", "Issue": "", "JournalId": 78644, "JournalTitle": "Central and Eastern European eDem and eGov Days", "ISSN": "2520-3401", "EISSN": "2663-9394", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119413937, "Title": "KLASTERISASI VIRUS COVID-19 DI WILAYAH KABUPATEN LAMONGAN DENGAN METODE K-MEANS CLUSTERING", "Abstract": "", "Keywords": "", "DOI": "10.29100/jipi.v6i2.1999", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 59609, "JournalTitle": "JIPI (<PERSON><PERSON><PERSON> da<PERSON>ajaran Informatika)", "ISSN": "", "EISSN": "2540-8984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Purnomo Hadi Susilo", "Affiliation": ""}], "References": []}, {"ArticleId": 119414024, "Title": "Gustos y patrones del comportamiento del consumidor turístico en el Pueblo Mágico de Aquismón, S.L.P.", "Abstract": "<p>El presente trabajo, que identifica los gustos y patrones de comportamiento del consumidor turístico en el Pueblo Mágico de Aquismón, S.L.P., se constituye en dos etapas y responde a recomendaciones de lineamientos federales, estatales y municipales sobre conocer y atender la participación de los visitantes para coadyuvar con información a la oferta de los servicios turísticos existentes. En la primera etapa se realiza investigación documental y en la segunda se diseña un instrumento de investigación de acuerdo a la propuesta de Brown (1980, p. 21).\r Los resultados de la encuesta aplicada indican, respecto a la percepción del consumidor turístico, que la mayoría tuvo una gran experiencia al visitar el Pueblo Mágico de Aquismón y que recomiendan el sitio para que más personas lo visiten. Hay factores de oferta turística que inciden positivamente en la percepción del visitante entre los que se encuentran, principalmente, la Alimentación, el Hospedaje, las artesanías, la gastronomía y su preferencia por el Sótano de las Golondrinas y las cascadas de Tamul; no obstante, los turistas consideran se debe mejorar la seguridad e higiene del municipio, así como aumentar las opciones de transporte.</p>", "Keywords": "Pueblo Mágico;Aquismón;Comportamiento del Consumidor;Consumo turís<PERSON>o", "DOI": "10.29057/est.v8i15.8102", "PubYear": 2022, "Volume": "8", "Issue": "15", "JournalId": 52418, "JournalTitle": "Boletín Científico INVESTIGIUM de la Escuela Superior de Tizayuca", "ISSN": "", "EISSN": "2448-4830", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}], "References": []}, {"ArticleId": 119414025, "Title": "Sistema de control automático basado en PLC y HMI para una máquina estribadora", "Abstract": "<p>Entre la gran variedad de materiales empleados en la industria de la construcción se encuentran unos elementos estructurales llamados estribos, también conocidos como anillos, que son típicamente fabricados de alambre de acero con gran grosor, denominado alambrón, que para casas habitación comúnmente es de calibre de un octavo de pulgada, el cual debe ser doblado para formar rectángulos o cuadros de diferentes medidas, propiamente los estribos, que se utilizan para abrazar, confinar y posicionar a las barras metálicas longitudinales, generalmente varillas, en el armado de columnas, castillos y trabes. Debido al esfuerzo físico considerable que se realiza para doblar manualmente tal material, así como a la alta demanda de estos elementos, en el presente trabajo se propone el desarrollo de un sistema de control para automatizar una máquina estribadora, implementando electroneumática con control basado en PLC y monitoreo mediante HMI. Este trabajo constituye un proyecto académico terminal de aplicación práctica, con enfoque a la automatización de procesos de tipo industrial.</p>", "Keywords": "Automatización;Control basado en PLC;Estribo;HMI;Neumática;Proceso industrial", "DOI": "10.29057/est.v8i15.9125", "PubYear": 2022, "Volume": "8", "Issue": "15", "JournalId": 52418, "JournalTitle": "Boletín Científico INVESTIGIUM de la Escuela Superior de Tizayuca", "ISSN": "", "EISSN": "2448-4830", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}], "References": []}, {"ArticleId": 119414033, "Title": "Arsitektur Sistem Pemerintahan Berbasis Elektronik (SPBE) Pada Domain Aplikasi di Lingkungan Daerah Kabupaten Kuningan", "Abstract": "", "Keywords": "Arsitektur Enterprise;Domain Aplikasi;SPBE", "DOI": "10.29100/jipi.v6i2.2118", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 59609, "JournalTitle": "JIPI (<PERSON><PERSON><PERSON> da<PERSON>ajaran Informatika)", "ISSN": "", "EISSN": "2540-8984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Telkom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universitas Telkom"}], "References": []}, {"ArticleId": 119414034, "Title": "IMPLEMENTASI PSO UNTUK OPTIMASI BOBOT ATRIBUT PADA ALGORITMA C4.5 DALAM PREDIKSI KELULUSAN MAHASISWA", "Abstract": "<p>Ketepatan penyelesaian masa studi mahasiswa merupakan salah satu faktor yang banyak disoroti oleh perguruan tinggi. Algoritma C4.5 merupakan salah satu metode yang dapat digunakan dalam memprediksi kelulusan mahasiswa. Salah satu masalah yang yang terjadi pada data mining adalah semakin banyak penggunaan atribut, akan dapat memengaruhi tingkat akurasi dan kinerja dari algoritma. Penelitian ini akan dilakukan dengan melakukan improvement untuk dapat mengoptimasi nilai akurasi pada Algoritma C4.5. Improvement akan berfokus pada bobot yang berperan sebagai faktor kelulusan mahasiswa. Optimasi pembobotan atribut akan dilakukan menggunakan Algoritma PSO Melalui model yang diusulkan, peneliti akan melihat hasil perbandingan nilai akurasi penerapan Algoritma C4.5 dengan C4.5 yang dioptimasi dengan PSO. Hasil akurasi dari penerapan algortima C4.5 dalam menentukan status kelulusan mahasiswa sebesar 90.73%. Proses optimasi yang dilakukan untuk membobotkan atribut pada algoritma C4.5 menggunakan algoritma PSO menghasilkan nilai akurasi sebesar 97.78%. Peningkatan performa didapat dari perbandingan hasil pengujian kedua algoritma dengan peningkatan akurasi sebesar 7.05%.</p>", "Keywords": "C4.5;PS<PERSON>;predi<PERSON><PERSON> kel<PERSON>", "DOI": "10.29100/jipi.v6i2.2440", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 59609, "JournalTitle": "JIPI (<PERSON><PERSON><PERSON> da<PERSON>ajaran Informatika)", "ISSN": "", "EISSN": "2540-8984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>git<PERSON>", "Affiliation": "Universitas AMIKOM Yogyakarta"}, {"AuthorId": 2, "Name": "Ikmah Ikmah", "Affiliation": "Universitas AMIKOM Yogyakarta"}], "References": []}, {"ArticleId": 119414987, "Title": "Security Model for Randomization-based Protected Caches", "Abstract": "<p>Cache side-channel attacks allow adversaries to learn sensitive information about co-running processes by using only access latency measures and cache contention.This vulnerability has been shown to lead to several microarchitectural attacks. As a promising solution, recent work proposes Randomization-based Protected Caches (RPCs). RPCs randomize cache addresses, changing keys periodically so as to avoid long-term leakage. Unfortunately, recent attacks have called the security of state-of-the-art RPCs into question.In this work, we tackle the problem of formally defining and analyzing the security properties of RPCs. We first give security definitions against access-based cache sidechannel attacks that capture security against known attacks such as Prime+Probe and Evict+Probe. Then, using these definitions, we obtain results that allow to guarantee security by adequately choosing the rekeying period, the key generation algorithm and the cache randomizer, thus providing security proofs for RPCs under certain assumptions.</p>", "Keywords": "Cache side-channel attacks;Timing attacks;Randomization-based protected caches;Randomly-mapped caches;Pseudo-random functions;Security definition", "DOI": "10.46586/tches.v2022.i3.1-25", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Universitat Rovira i Virgili, Tarragona, Spain"}, {"AuthorId": 2, "Name": "Oriol <PERSON>", "Affiliation": "Universitat Rovira i Virgili, Tarragona, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitat Politècnica de València, València, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Barcelona Supercomputing Center, Barcelona, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Barcelona Supercomputing Center, Barcelona, Spain"}], "References": []}, {"ArticleId": 119415005, "Title": "Study of Two System of Caputo Fractional Differential Equations with Initial Conditions via Laplace Transform Method", "Abstract": "", "Keywords": "", "DOI": "10.46719/npsc20212921", "PubYear": 2021, "Volume": "29", "Issue": "2", "JournalId": 58974, "JournalTitle": "Neural, Parallel, and Scientific Computations", "ISSN": "1061-5369", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Aghalaya S Vatsala", "Affiliation": ""}], "References": []}, {"ArticleId": 119415059, "Title": "Preface to TCHES Volume 2021", "Abstract": "", "Keywords": "", "DOI": "10.46586/tches.v2021.i1.I-IV", "PubYear": 2020, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415082, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1084-8045(22)00100-X", "PubYear": 2022, "Volume": "204", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [], "References": []}, {"ArticleId": 119415092, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1574-1192(22)00014-1", "PubYear": 2022, "Volume": "80", "Issue": "", "JournalId": 4135, "JournalTitle": "Pervasive and Mobile Computing", "ISSN": "1574-1192", "EISSN": "1873-1589", "Authors": [], "References": []}, {"ArticleId": 119415115, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0022-0000(21)00074-X", "PubYear": 2021, "Volume": "122", "Issue": "", "JournalId": 4857, "JournalTitle": "Journal of Computer and System Sciences", "ISSN": "0022-0000", "EISSN": "1090-2724", "Authors": [], "References": []}, {"ArticleId": 119415147, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0950-5849(22)00082-9", "PubYear": 2022, "Volume": "147", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [], "References": []}, {"ArticleId": 119415199, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0166-3615(22)00123-3", "PubYear": 2022, "Volume": "141", "Issue": "", "JournalId": 5958, "JournalTitle": "Computers in Industry", "ISSN": "0166-3615", "EISSN": "1872-6194", "Authors": [], "References": []}, {"ArticleId": 119415213, "Title": "Editorial", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0304-3975(22)00423-6", "PubYear": 2022, "Volume": "926", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415222, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0965-9978(22)00075-8", "PubYear": 2022, "Volume": "170", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [], "References": []}, {"ArticleId": 119415239, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1574-1192(22)00121-3", "PubYear": 2022, "Volume": "86", "Issue": "", "JournalId": 4135, "JournalTitle": "Pervasive and Mobile Computing", "ISSN": "1574-1192", "EISSN": "1873-1589", "Authors": [], "References": []}, {"ArticleId": 119415245, "Title": "Contents", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1877-0509(22)00204-6", "PubYear": 2022, "Volume": "199", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 119415475, "Title": "A data-owner centric privacy model with blockchain and adapted attribute-based encryption for internet-of-things and cloud environment", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICS.2022.122374", "PubYear": 2022, "Volume": "17", "Issue": "3/4", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415476, "Title": "A new image encryption algorithm based on cascaded chaos and Arnold transform", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICS.2022.122377", "PubYear": 2022, "Volume": "17", "Issue": "3/4", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415484, "Title": "Ice clamping system in manufacturing systems as a cyber-physical system towards Industry 4.0", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJMIC.2021.122467", "PubYear": 2021, "Volume": "38", "Issue": "1", "JournalId": 12335, "JournalTitle": "International Journal of Modelling, Identification and Control", "ISSN": "1746-6172", "EISSN": "1746-6180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415485, "Title": "Control-oriented observer for cylinder pressure estimation of SI engine using frequency response function", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJMIC.2022.10046040", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 12335, "JournalTitle": "International Journal of Modelling, Identification and Control", "ISSN": "1746-6172", "EISSN": "1746-6180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415496, "Title": "A metaphorical situation annotation framework", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJRIS.2022.10048128", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 10882, "JournalTitle": "International Journal of Reasoning-based Intelligent Systems", "ISSN": "1755-0556", "EISSN": "1755-0564", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415523, "Title": "A new approach for automatic Arabic-text detection and localisation in video frames", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2022.123016", "PubYear": 2022, "Volume": "22", "Issue": "1/2", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415534, "Title": "A model predictive control strategy for field-circuit coupled model of PMSM", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2021.121522", "PubYear": 2021, "Volume": "67", "Issue": "2/3", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415535, "Title": "Gradient iterative-based kernel method for exponential autoregressive models", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2021.121523", "PubYear": 2021, "Volume": "67", "Issue": "2/3", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415536, "Title": "A study on ultrasonic process tomography for dispersed small particle system visualisation", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2021.121528", "PubYear": 2021, "Volume": "67", "Issue": "2/3", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415538, "Title": "Allocation of conference hall booking in Android application using cloud", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCC.2022.128687", "PubYear": 2022, "Volume": "11", "Issue": "5/6", "JournalId": 18415, "JournalTitle": "International Journal of Cloud Computing", "ISSN": "2043-9989", "EISSN": "2043-9997", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "S. Prince <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415544, "Title": "Virtual sports rehabilitation and monitoring system for the elderly based on intelligent interaction and embedded system", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJES.2022.10049607", "PubYear": 2022, "Volume": "15", "Issue": "3", "JournalId": 31256, "JournalTitle": "International Journal of Embedded Systems", "ISSN": "1741-1068", "EISSN": "1741-1076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119415548, "Title": "Privacy-preserving with data optimisation in social networks using ensemble algorithm and K-neural network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJESMS.2022.10046898", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 33706, "JournalTitle": "International Journal of Engineering Systems Modelling and Simulation", "ISSN": "1755-9758", "EISSN": "1755-9766", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}]