[{"ArticleId": 105704204, "Title": "The WSN intrusion detection method based on deep data mining", "Abstract": "Aiming at the defects that wireless sensor network is vulnerable to intrusion attacks and affects communication performance, a WSN intrusion detection method based on deep data mining is presented. The network traffic capture tool is used to capture the data stream of the wireless sensor network, and the captured data stream is cleaned and normalized. An improved deep sparse autoencoder is used to extract WSN intrusion features from the normalized data stream. The extracted intrusion features are taken as the initial data of K-means clustering, to optimize the k-means clustering algorithm (KMCA) by genetic algorithm. The reasonable initial center point searched by the genetic algorithm is taken as the input parameter of the improved KMCA. The proposed method can detect internal attacks, external attacks and other intrusion behaviors in wireless sensor networks, and the false detection rate is less than 1%.", "Keywords": "Deep data mining ; WSN ; intrusion detection method ; k-means clustering ; genetic algorithm", "DOI": "10.1080/23742917.2022.2162195", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 16753, "JournalTitle": "Journal of Cyber Security Technology", "ISSN": "2374-2917", "EISSN": "2374-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The Faculty of Electronics and Information, Sichuan Modern Vocational College, Chengdu, China"}], "References": [{"Title": "RETRACTED ARTICLE: Quality analysis of multi-sensor intrusion detection node deployment in homogeneous wireless sensor networks", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "2", "Page": "1331", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Kappa Updated Ensemble for drifting data stream mining", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "1", "Page": "175", "JournalTitle": "Machine Learning"}, {"Title": "Network intrusion detection using multi-architectural modular deep neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "4", "Page": "3571", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Algorithms and software for data mining and machine learning: a critical comparative view from a systematic review of the literature", "Authors": "Gilda Taranto-Vera; Purificación Galindo-Villardón; <PERSON>-<PERSON>-<PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "10", "Page": "11481", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Multi-fidelity global optimization using a data-mining strategy for computationally intensive black-box problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107212", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A hybrid machine learning model for intrusion detection in VANET", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "104", "Issue": "3", "Page": "503", "JournalTitle": "Computing"}, {"Title": "Using data mining methods to develop manufacturing production rule in IoT environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "3", "Page": "4526", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Trust- and energy-aware cluster head selection in a UAV-based wireless sensor network using Fit-FCM", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "4", "Page": "5610", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Construction and implementation of a college talent cultivation system under deep learning and data mining algorithms", "Authors": "Haizhou Ma; Haizhou Ma; Aiping Din<PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "4", "Page": "5681", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Software-defect prediction within and across projects based on improved self-organizing data mining", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "5", "Page": "6147", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A comprehensive survey on meta-heuristic-based energy minimization routing techniques for wireless sensor network: classification and challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "5", "Page": "6612", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Dimensionality reduction for multivariate time-series data mining", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "7", "Page": "9862", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": *********, "Title": "Criticality-aware priority to accelerate GPU memory access", "Abstract": "<p>Graphic processing units (GPU) concept, combined with CUDA and OpenCL programming models, offers new opportunities to reduce latency and power consumption of throughput-oriented workloads. GPU can execute thousands of parallel threads to hide the memory access latency. However, for some memory-intensive workloads, it is very likely in some time intervals that all threads of a core will be stalled while waiting for their data to be provided by the main memory. In this research, we aim to make GPU memory access latency shorter to increase the thread activity time and to decrease core underutilization. In order to improve non-optimal time of cores, we focus on the memory buffer and the interconnection network to prioritize the packets of the cores with the greatest number of stalled threads. As a result, more critical packets will receive the higher priority in arbitration and resource allocation, so their memory requests will be handled faster, and overall cores’ stall time is reduced. 28% maximum and 12.5% average speed-up improvements among the used benchmarks, without significant effect on system area and power consumption, are reported.</p>", "Keywords": "Cache contention; Graphics processing unit (GPU); Cores stall time; Interconnection network; Locality; Main memory row switching; Priority", "DOI": "10.1007/s11227-022-04657-3", "PubYear": 2023, "Volume": "79", "Issue": "1", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Shahid Beheshti University, Tehran, Iran"}, {"AuthorId": 2, "Name": "Far<PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Shahid Beheshti University, Tehran, Iran"}], "References": [{"Title": "Inter-kernel Reuse-aware Thread Block Scheduling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Architecture and Code Optimization"}, {"Title": "LARA: Locality-aware resource allocation to improve GPU memory-access time", "Authors": "<PERSON><PERSON><PERSON>; Farshad <PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "12", "Page": "14438", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "PAVER", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Architecture and Code Optimization"}]}, {"ArticleId": 105704384, "Title": "Nanofluids application in machining: a comprehensive review", "Abstract": "Abstract </h3> <p>Nanofluids are efficient heat transfer media that have been developed over the past 27 years and have been widely used in the electronic microchannel, engine, spacecraft, nuclear, and solar energy fields. With the high demand for efficient lubricants in manufacturing, the application of nanofluids in machining has become a hot topic in academia and industry. However, in the context of the huge amount of literature in the past decade, existing review cannot be used as a technical manual for industrial applications. There are many technical difficulties in establishing a mature production system, which hinder the large-scale application of nanofluids in industrial production. The physicochemical mechanism underlying the application of nanofluids in machining remains unclear. This paper is a complete review of the process, device, and mechanism, especially the unique mechanism of nanofluid minimum quantity lubrication under different processing modes. In this paper, the preparation, fluid, thermal, and tribological properties of nanofluids are reviewed. The performance of nanofluids in machining is clarified. Typically, in friction and wear tests, the coefficient of friction of jatropha oil-based alumina nanofluids is reduced by 85% compared with dry conditions. The cutting fluid based on alumina nanoparticles improves the tool life by 177–230% in hard milling. The addition of carbon nanotube nanoparticles increases the convective heat transfer coefficient of normal saline by 145.06%. Furthermore, the innovative equipment used in the supply of nanofluids is reviewed, and the atomization mechanisms under different boundary conditions are analyzed. The technical problem of parameterized controllable supply system is solved. In addition, the performance of nanofluids in turning, milling, and grinding is discussed. The mapping relationship between the nanofluid parameters and the machining performance is clarified. The flow field distribution and lubricant wetting behavior under different tool-workpiece boundaries are investigated. Finally, the application prospects of nanofluids in machining are discussed. This review includes a report on recent progress in academia and industry as well as a roadmap for future development.</p>", "Keywords": "Nanofluids; Machining; Turning; Milling; Grinding; Minimum quantity lubrication", "DOI": "10.1007/s00170-022-10767-2", "PubYear": 2024, "Volume": "131", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Qingdao University of Technology, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Song", "Affiliation": "School of Mechanical and Automotive Engineering, Qingdao University of Technology, Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Qingdao University of Technology, Qingdao, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Ultra-Precision Machining Technology, Department of Industrial and Systems Engineering, The Hong Kong Polytechnic University, Hong Kong, China; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, King Fahd University of Petroleum and Minerals, Dhahran, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, IK Gujral Punjab Technical University, Punjab, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, University of Southern California, Los Angeles, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Qingdao University of Technology, Qingdao, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Qingdao University of Technology, Qingdao, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON> Liu", "Affiliation": "School of Mechanical and Automotive Engineering, Qingdao University of Technology, Qingdao, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Qingdao University of Technology, Qingdao, China"}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering, University of Sharjah, Sharjah, United Arab Emirates"}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hanergy (Qingdao) Lubrication Technology Co., Ltd., Qingdao, China"}], "References": [{"Title": "Influence of Al2O3 and TiO2 nanofluid on hard turning performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "5-6", "Page": "2265", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Milling force and surface morphology of 45 steel under different Al2O3 nanofluid concentrations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "Page": "1277", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Performance evaluation of minimum quantity lubrication technique in grinding of AISI 202 stainless steel using nano-MoS2 with vegetable-based cutting fluid", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "1-2", "Page": "125", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Application of Graphene Nanofluid/Ultrasonic Atomization MQL System in Micromilling and Development of Optimal Predictive Model for SKH-9 High-Speed Steel Using Fuzzy-Logic-Based Multi-objective Design", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Jinn-<PERSON>song Tsai", "PubYear": 2020, "Volume": "22", "Issue": "7", "Page": "2101", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Biomimetic integration of MQL and tool surface microstructure in intermittent machining", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "7-8", "Page": "1847", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Temperature of the 45 steel in the minimum quantity lubricant milling with different biolubricants", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "9-10", "Page": "2779", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A novel approach to improve environmentally friendly machining processes using ultrasonic nozzle–minimum quantity lubrication system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "3-4", "Page": "741", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Enhancement of viscosity and thermal conductivity of soybean vegetable oil using nanoparticles to form nanofluids for minimum quantity lubrication machining of difficult-to-cut metals", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "11-12", "Page": "3377", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Analysis, modeling, and multi-objective optimization of machining Inconel 718 with nano-additives based minimum quantity coolant", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107416", "JournalTitle": "Applied Soft Computing"}, {"Title": "Ambient air quantity and cutting performances of water-based Fe3O4 nanofluid in magnetic minimum quantity lubrication", "Authors": "Tao Lv; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "5-6", "Page": "1711", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Multiple Performance Characteristics in the Application of Taguchi Fuzzy Method in Nanofluid/Ultrasonic Atomization Minimum Quantity Lubrication for Grinding Inconel 718 Alloys", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "1", "Page": "294", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Influence of texture shape and arrangement on nanofluid minimum quantity lubrication turning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "1-2", "Page": "631", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Residual stress of grinding cemented carbide using MoS2 nano-lubricant", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "9-10", "Page": "5671", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Effect of B4C on CBN/CuSnTi laser cladding grinding tool", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "9-10", "Page": "6307", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 105704440, "Title": "Late multimodal fusion for image and audio music transcription", "Abstract": "Music transcription, which deals with the conversion of music sources into a structured digital format, is a key problem for Music Information Retrieval (MIR). When addressing this challenge in computational terms, the MIR community follows two lines of research: music documents, which is the case of Optical Music Recognition (OMR), or audio recordings, which is the case of Automatic Music Transcription (AMT). The different nature of the aforementioned input data has conditioned these fields to develop modality-specific frameworks. However, their recent definition in terms of sequence labeling tasks leads to a common output representation, which enables research on a combined paradigm. In this respect, multimodal image and audio music transcription comprises the challenge of effectively combining the information conveyed by image and audio modalities. In this work, we explore this question at a late-fusion level: we study four combination approaches in order to merge, for the first time, the hypotheses regarding end-to-end OMR and AMT systems in a lattice-based search space. The results obtained for a series of performance scenarios–in which the corresponding single-modality models yield different error rates–showed interesting benefits of these approaches. In addition, two of the four strategies considered significantly improve the corresponding unimodal standard recognition frameworks.", "Keywords": "Optical Music Recognition ; Automatic Music Transcription ; Multimodality ; Deep learning ; Connectionist Temporal Classification ; Sequence labeling ; Word graphs", "DOI": "10.1016/j.eswa.2022.119491", "PubYear": 2023, "Volume": "216", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University Institute for Computing Research, University of Alicante, Carretera San Vicente del Raspeig s/n, 03690, Alicante, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University Institute for Computing Research, University of Alicante, Carretera San Vicente del Raspeig s/n, 03690, Alicante, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University Institute for Computing Research, University of Alicante, Carretera San Vicente del Raspeig s/n, 03690, Alicante, Spain"}, {"AuthorId": 4, "Name": "<PERSON>go<PERSON>", "Affiliation": "University Institute for Computing Research, University of Alicante, Carretera San Vicente del Raspeig s/n, 03690, Alicante, Spain"}], "References": [{"Title": "Understanding Optical Music Recognition", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 105704452, "Title": "Providing command and control agility: A software product line approach", "Abstract": "Command and Control (C2) is a broad concept that encompasses the coordination of individuals and organizations towards achieving a goal. However, dynamic and uncertain scenarios, such as military and disaster relief operations, present an inherent challenge to C2 activities. In such situations, plans often need to be changed in the face of unforeseen problems, and even coordination processes may be subject to variation. This dynamism increases the complexity of resource management and requires C2 Agility—i.e., the ability to respond to change in a timely and suitable fashion. Nonetheless, there is a lack of solutions to provide C2 Agility to cope with dynamic contexts. To address this problem, this work proposes a computational model of C2 Agility for a team of autonomous agents. This model describes how to combine reconfiguration of individual team members and of coordination approaches to adapt to context changes. The proposed approach leverages a typed-parameterized extension of a channel system to define the coordinating roles and responsibilities of team members. Each member is modeled as a dynamic software product line, with the inherent ability to reconfigure itself. To assess this model, a team of Unmanned Aerial Vehicles (UAV) performing a reconnaissance mission was simulated. The simulation showed that the proposed model was suitable for dealing with dynamic contexts. Particularly, metrics for the agile approach suggest improved system resilience in the face of induced perturbations, compared to non-agile C2. The obtained results with the proposed software-based simulations showed that the proposed model is useful in providing C2 Agility to the studied scenarios, making the behavior of the entities specified in the model capable of dealing with context changes.", "Keywords": "", "DOI": "10.1016/j.eswa.2022.119473", "PubYear": 2023, "Volume": "216", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Systems Development Center, Brazilian Army, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Brasilia, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Brasilia, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Alves", "Affiliation": "Department of Computer Science, University of Brasilia, Brazil"}, {"AuthorId": 5, "Name": "Edison Pig<PERSON>on de Freitas", "Affiliation": "Institute of Informatics, Federal University of Rio Grande do Sul, Brazil"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Systems Development Center, Brazilian Army, Brazil"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Precise Research Center, NaDI, University of Namur, Belgium"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Precise Research Center, NaDI, University of Namur, Belgium"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Precise Research Center, NaDI, University of Namur, Belgium"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Precise Research Center, NaDI, University of Namur, Belgium"}], "References": [{"Title": "Assessing a swarm-GAP based solution for the task allocation problem in dynamic scenarios", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>on <PERSON> Freitas", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "113437", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 105704522, "Title": "The resistance of leds to the effects of gamma radiation in various operating modes", "Abstract": "The effect of active and passive power modes on the resistance of the LEDs (LEDs) made of multilayer AlGaAs heterostructures to gamma-quantum irradiation was studied. Three characteristic stages of emission power reduction during irradiation are distinguished for the studied LEDs irrespective of the power supply mode. When irradiating LEDs in the active power supply mode, two differently directed processes of changes in the emission power are observed. The assumption is made that the first process is caused by a decrease in the LED emission power due to the injection of appropriate radiation defects. The second process is caused by a partial recovery of the emission power due to radiation, radiation-thermal, and/or electrostimulated annealing of some of the defects created. The observed recovery of the emission power in the active power mode of the LED during irradiation significantly increases its resistance to gamma-ray irradiation.", "Keywords": "", "DOI": "10.17352/tcsit.000060", "PubYear": 2022, "Volume": "7", "Issue": "3", "JournalId": 11192, "JournalTitle": "Trends in Computer Science and  Information Technology", "ISSN": "", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Orlova KN", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>ev V<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Zhamaldinov FF", "Affiliation": ""}], "References": []}, {"ArticleId": 105704582, "Title": "Genesungswünsche vom Petersberg", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2023.01.05", "PubYear": 2023, "Volume": "", "Issue": "1", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105704608, "Title": "Experimental investigation of the low-temperature oil-on-water cooling and lubrication in turning the hardened AISI D2 steel", "Abstract": "<p>In this paper, the influence of the cutting speed, feed, and the depth of cut on the cutting force, surface roughness, cutting temperature, and tool wear were experimentally investigated under the low-temperature oil-on-water (LTOoW) cooling and lubrication condition in turning the hardened tool steel AISI D2 (60 ± 1HRC) with the PCBN cutting tool. The results showed that the three-component cutting forces are F <sub>Y</sub> > F <sub>Z</sub> > F <sub>X</sub>. The influence of the cutting speed on the cutting temperature is slightly more visible compared to the feed and depth of cut. In this experiment, a satisfactory surface roughness value of 0.54 µm can be obtained, gaining the effect of the turning instead of the grinding. The flank wear values of the PCBN tool are 142 µm and 148 µm at the cutting speeds of 55 and 140 m/min, respectively; however, the flank wear abruptly increases to 668 µm at a 495 m/min, which has a very serious impact on the tool life. The abrasive wear is considered to be a predominant wear mechanism on the flank wear of the PCBN tool. The rake face is dominated by crater wear due to the high temperature, high pressure, high stress, and high friction at the chip-tool interface. Compared with dry hard turning (DHT) condition, the lower surface roughness value, lower cutting temperature, and longer tool life can be obtained at LTOoW.</p>", "Keywords": "Hard turning· LTOoW; Cutting force; Surface roughness; Cutting temperature; Tool wear", "DOI": "10.1007/s00170-022-10692-4", "PubYear": 2023, "Volume": "125", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Environment-Friendly Composite Materials of the State Ethnic Affairs Commission, Gansu Provincial Biomass Function Composites Engineering Research Center, Key Laboratory for Utility of Environment-Friendly Composite Materials and Biomass in University of Gansu Province, and School of Chemical Engineering, Northwest Minzu University, Lanzhou, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province, Lanzhou Institute of Technology, Lanzhou, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province, Lanzhou Institute of Technology, Lanzhou, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province, Lanzhou Institute of Technology, Lanzhou, People’s Republic of China"}, {"AuthorId": 5, "Name": "Baodong Li", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province, Lanzhou Institute of Technology, Lanzhou, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Provincial Key Laboratory for Green Cutting Technology and Application of Gansu Province, Lanzhou Institute of Technology, Lanzhou, People’s Republic of China"}], "References": [{"Title": "Machinability investigations of hardened steel with biodegradable oil-based MQL spray system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "3", "Page": "735", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Effect of nozzles on cutting performance when machining with oil-on-water cooling technique", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "1-2", "Page": "313", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 105704630, "Title": "Influence of the Impact Angle on Machining in Powder Jet Processing", "Abstract": "<p>Powder jet machining is a blast machining process in which micrometer-order particles are projected onto a workpiece at near-supersonic speeds, to remove the workpiece (abrasive jet machining (AJM)) or to deposit the particles (powder jet deposition (PJD)). We report a novel dental treatment method for powder jet machining using hydroxyapatite, which is the main component of teeth, as deposited particles. The surfaces and interdental spaces of human teeth are not only flat, but also have complex groove structures. However, PJD and AJM exhibit impact-angle-dependent machining phases. Therefore, it is necessary to investigate the effect of the particle impact angle on machining, before dental treatment. Furthermore, because machining interacts not only with the particle impact angle but also with the particle impact velocity, a comprehensive investigation of the effects of the machining parameters is required, for delineating the phase-transition conditions. Accordingly, in this study, we conducted machining experiments using hydroxyapatite particles (particle diameter, 2.16 μm) and four different blasting angles of 30°, 45°, 60°, and 90°, to infer the machining amount. Machining efficiency was evaluated based on the amount of machining. The impact angles and velocities of the particles were calculated using computational fluid dynamics (CFD). Three-dimensional process mapping was performed using the machining amount, particle impact angle, and particle impact velocity, obtained from the experiments and CFD calculations. The results showed that PJD crossed to AJM at the impact angle of approximately 60°. Moreover, PJD exhibited high processing efficiency for impact angles above 60° and impact velocities in the 280–310 m/s range. In contrast, AJM exhibited high processing efficiency for impact angles below approximately 35° and impact velocities above 310 m/s.</p>", "Keywords": "powder jet deposition;abrasive jet machining;hydroxyapatite;impact angle;machining efficiency", "DOI": "10.20965/ijat.2023.p0005", "PubYear": 2023, "Volume": "17", "Issue": "1", "JournalId": 8593, "JournalTitle": "International Journal of Automation Technology", "ISSN": "1881-7629", "EISSN": "1883-8022", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Finemechanics, Graduate School of Engineering, Tohoku University, 6-6-01 <PERSON><PERSON><PERSON>, Aoba-ku, Sendai, Miyagi 980-8579, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Perioperative Oral Health Management, Tohoku University Hospital, Sendai, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Systems Engineering, Graduate School of Engineering, Tohoku University, Sendai, Japan;Applied Research Laboratory, High Energy Accelerator Research Organization, Tsukuba, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Systems Engineering, Graduate School of Engineering, Tohoku University, Sendai, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Advanced Prosthetic Dentistry, Graduate School of Dentistry, Tohoku University, Sendai, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center for Co-Creation Strategy, Tohoku University, Sendai, Japan"}], "References": []}, {"ArticleId": 105704868, "Title": "The construction of smart city information service system in the era of big data", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJDS.2023.10053182", "PubYear": 2023, "Volume": "8", "Issue": "2", "JournalId": 44182, "JournalTitle": "International Journal of Data Science", "ISSN": "2053-0811", "EISSN": "2053-082X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105705045, "Title": "Strengerer Datenschutz durch Betriebsvereinbarung", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2023.01.08", "PubYear": 2023, "Volume": "", "Issue": "1", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "Tassilo-<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105705251, "Title": "The Effects of Digitally Delivered Nudges in a Corporate Wellness Program", "Abstract": "We investigate how two digitally delivered nudges, namely light social support (nonverbal cues such as kudos or likes) and motivational messaging, affect employees’ self-reported physical activity in an online, corporate wellness program. Within this unique field setting, using data from several years, we found evidence that both types of nudges provide benefits beyond the effect of cash incentives. However, the effects vary by individual, depending on whether the employee is actively engaging in physical activity, and by time, depending on how long the employee has been in the wellness program. We found light social support to be less effective over time, while motivational messages were found to be more effective with the duration in the program and generally more effective for physically inactive users. Our findings have implications for the design of wellness systems, suggesting different approaches depending on an employee’s current activity level and tenure in the program. © 2023 by the Association for Information Systems.", "Keywords": "Digital Nudges; Motivational Messages; Online Health; Online Social Support", "DOI": "10.17705/1jais.00783", "PubYear": 2023, "Volume": "24", "Issue": "1", "JournalId": 24874, "JournalTitle": "Journal of the Association for Information Systems", "ISSN": "", "EISSN": "1536-9323", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Wisconsin-Madison, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Carlson School of Management, University of Minnesota, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Clemson University, United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Carlson School of Management, University of Minnesota, United States"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Massachusetts, Amherst, United States"}], "References": []}, {"ArticleId": 105705262, "Title": "Auxiliary Classifier of Generative Adversarial Network for Lung Cancer Diagnosis", "Abstract": "The classification of lung nodules is a challenging problem as the visual analysis of the nodules and non-nodules revealed homogenous textural patterns. In this work, an Auxiliary Classifier (AC)-Generative Adversarial Network (GAN) based Lung Cancer Classification (LCC) system is developed. The proposed AC-GAN-LCC system consists of three modules; preprocessing, Lungs Region Detection (LRD), and AC-GAN classification. A Wiener filter is employed in the preprocessing module to remove the Gaussian noise. In the LRD module, only the lung regions (left and right lungs) are detected using iterative thresholding and morphological operations. In order to extract the lung region only, flood filling and background subtraction. The detected lung regions are fed to the AC-GAN classifier to detect the nodules. It classifies the nodules into one of the two classes, i.e., binary classification (such as nodules or non-nodules). The AC-GAN is the extended version of the conditional GAN that predicts the label of a given image. Three different optimization techniques, adaptive gradient optimization, root mean square propagation optimization, and Adam optimization are employed for optimizing the AC-GAN architecture. The proposed AC-GAN-LCC system is evaluated on the Lung Image Database Consortium (LIDC) database Computed Tomography (CT) scan images. The proposed AC-GAN-LCC system classifies ∼15000 CT slices (7310 non-nodules and 7685 nodules). It provides an overall accuracy of 98.8% on the LIDC database using Adam optimization by a 10-fold cross-validation approach.", "Keywords": "auxiliary classifier; deep learning; generative adversarial network; image classification system; Lung cancer", "DOI": "10.32604/iasc.2023.032040", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>. <PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Panimalar Institute of Technology, Tamil Nadu, Chennai, India"}, {"AuthorId": 2, "Name": "P. <PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sri Indu College of Engineering and Technology, Hyderabad, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, <PERSON>wa Mahavidyalaya University, (Deemed University), Tamil Nadu, Kanchipuram, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics and Communication Engineering, Saveetha School of Engineering, Saveetha Institute of Medical and Technical Sciences, Tamil Nadu, Chennai, India"}], "References": [{"Title": "An Efficient Method for Covid-19 Detection Using Light Weight Convolutional Neural Network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "2", "Page": "2475", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Deer Hunting Optimization with Deep Learning Model for Lung Cancer Classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "1", "Page": "533", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 105705296, "Title": "USIS: Unsupervised Semantic Image Synthesis", "Abstract": "Semantic Image Synthesis (SIS) is a subclass of I2I (I2) translation where a photorealistic image is synthesized from a segmentation mask. SIS has mainly been addressed as a supervised problem. However, state-of-the-art methods depend on a massive amount of labeled data and cannot be applied in an unpaired setting. On the other hand, generic unpaired I2I frameworks underperform in comparison. In this work, we propose a new framework, Unsupervised Semantic Image Synthesis (USIS), as a first step towards closing the performance gap between paired and unpaired settings. We design a simple and effective learning scheme that combines the fragmented benefits of cycle losses and relationship preservation constraints. Then, we make the discovery that, contrary to I2I translation, discriminator design is crucial for label-to-image translation. To this end, we design a new discriminator with a wavelet-based encoder and a decoder to reconstruct the real images. The self-supervised reconstruction loss in the decoder prevents the encoder from overfitting on a few wavelet coefficients. We test our methodology on 3 challenging datasets and set a new standard for unpaired SIS. The generated images demonstrate significantly better diversity, quality and multimodality.", "Keywords": "", "DOI": "10.1016/j.cag.2022.12.010", "PubYear": 2023, "Volume": "111", "Issue": "", "JournalId": 3909, "JournalTitle": "Computers & Graphics", "ISSN": "0097-8493", "EISSN": "1873-7684", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Signal Processing and System Theory, University of Stuttgart, Stuttgart, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Signal Processing and System Theory, University of Stuttgart, Stuttgart, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Signal Processing and System Theory, University of Stuttgart, Stuttgart, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Signal Processing and System Theory, University of Stuttgart, Stuttgart, Germany"}], "References": [{"Title": "OASIS: Only Adversarial Supervision for Semantic Image Synthesis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "12", "Page": "2903", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 105705335, "Title": "Version 2.0.0 - M-SPARC: Matlab-Simulation Package for Ab-initio Real-space Calculations", "Abstract": "M-SPARC is a Matlab code for performing ab initio <PERSON>–Sham Density Functional Theory simulations. Version 2.0.0 of the software further extends its capability to include relativistic effects, dispersion interactions, and advanced semilocal/nonlocal exchange–correlation functionals. These features significantly increase the fidelity of first principles calculations that can be performed using M-SPARC.", "Keywords": "<PERSON><PERSON>–<PERSON>ham Density Functional Theory ; Electronic structure ; Relativistic effects ; Dispersion interactions ; Meta-GGA functionals ; Hybrid functionals", "DOI": "10.1016/j.softx.2022.101295", "PubYear": 2023, "Volume": "21", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering, Georgia Institute of Technology, Atlanta, GA 30332, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Georgia Institute of Technology, Atlanta, GA 30332, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering, Georgia Institute of Technology, Atlanta, GA 30332, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering, Georgia Institute of Technology, Atlanta, GA 30332, USA;Corresponding author"}], "References": [{"Title": "M-SPARC: Matlab-Simulation Package for Ab-initio Real-space Calculations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100423", "JournalTitle": "SoftwareX"}, {"Title": "SPARC: Simulation Package for Ab-initio Real-space Calculations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "", "Page": "100709", "JournalTitle": "SoftwareX"}, {"Title": "KSSOLV 2.0: An efficient MATLAB toolbox for solving the <PERSON><PERSON><PERSON> equations with plane-wave basis set", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "279", "Issue": "", "Page": "108424", "JournalTitle": "Computer Physics Communications"}, {"Title": "Soft and transferable pseudopotentials from multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "283", "Issue": "", "Page": "108594", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 105705339, "Title": "NEURAL NETWORK ANIMATION OF THE 17TH CENTURY EVENK GIRL", "Abstract": "", "Keywords": "", "DOI": "10.22250/18142400_2022_74_4_03", "PubYear": 2022, "Volume": "", "Issue": "4", "JournalId": 37421, "JournalTitle": "Informatika i sistemy upravleniya", "ISSN": "1814-2400", "EISSN": "1814-2419", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105705388, "Title": "Things are never so bad that they can’t get worse. Inside the collapse of Venezuela. (2022). <PERSON>", "Abstract": "<p>Reseña del libro de <PERSON> (2022). Things are never so bad that they can’t get worse. Inside the collapse of Venezuela [versión kindle]. EE.UU.: St. Martin's Press, 336 págs. ISBN: ‎ 1250266165.</p>", "Keywords": "", "DOI": "10.5354/0719-1529.2022.69362", "PubYear": 2022, "Volume": "31", "Issue": "46", "JournalId": 48966, "JournalTitle": "Comunicación y Medios", "ISSN": "0716-3991", "EISSN": "0716-3991", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad de Chile, Santiago, Chile"}], "References": []}, {"ArticleId": 105705448, "Title": "Machine Learning Based Diagnosis for Diabetic Retinopathy for SKPD-PSC", "Abstract": "The study aimed to apply to Machine Learning (ML) researchers working in image processing and biomedical analysis who play an extensive role in comprehending and performing on complex medical data, eventually improving patient care. Developing a novel ML algorithm specific to Diabetic Retinopathy (DR) is a challenge and need of the hour. Biomedical images include several challenges, including relevant feature selection, class variations, and robust classification. Although the current research in DR has yielded favourable results, several research issues need to be explored. There is a requirement to look at novel pre-processing methods to discard irrelevant features, balance the obtained relevant features, and obtain a robust classification. This is performed using the Steerable Kernalized Partial Derivative and Platt Scale Classifier (SKPD-PSC) method. The novelty of this method relies on the appropriate non-linear classification of exclusive image processing models in harmony with the Platt Scale Classifier (PSC) to improve the accuracy of DR detection. First, a Steerable Filter Kernel Pre-processing (SFKP) model is applied to the Retinal Images (RI) to remove irrelevant and redundant features and extract more meaningful pathological features through Directional Derivatives of Gaussians (DDG). Next, the Partial Derivative Image Localization (PDIL) model is applied to the extracted features to localize candidate features and suppress the background noise. Finally, a Platt Scale Classifier (PSC) is applied to the localized features for robust classification. For the experiments, we used the publicly available DR detection database provided by Standard Diabetic Retinopathy (SDR), called DIARETDB0. A database of 130 image samples has been collected to train and test the ML-based classifiers. Experimental results show that the proposed method that combines the image processing and ML models can attain good detection performance with a high DR detection accuracy rate with minimum time and complexity compared to the state-of-the-art methods. The accuracy and speed of DR detection for numerous types of images will be tested through experimental evaluation. Compared to state-of-the-art methods, the method increases DR detection accuracy by 24% and DR detection time by 37.", "Keywords": "accuracy; Diabetic retinopathy; image localization; machine learning; Platt Scale classifier; retinal images", "DOI": "10.32604/iasc.2023.033711", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Erode Sengunthar Engineering College, Erode, Tamil Nadu, Perundurai, 638057, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems, College of Computer Sciences and Information Technology, King Faisal University, Al Hasa, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems, College of Computer and Information Science, Princess <PERSON><PERSON><PERSON>man University, P.O. BOX 84428, Riyadh, 11671, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Swami Keshvanand Institute of Technology, Management & Gramothan (SKIT), Rajasthan, Jaipur, 302017, India"}], "References": []}, {"ArticleId": 105705449, "Title": "Faster Region Based Convolutional Neural Network for Skin Lesion Segmentation", "Abstract": "The diagnostic interpretation of dermoscopic images is a complex task as it is very difficult to identify the skin lesions from the normal. Thus the accurate detection of potential abnormalities is required for patient monitoring and effective treatment. In this work, a Two-Tier Segmentation (TTS) system is designed, which combines the unsupervised and supervised techniques for skin lesion segmentation. It comprises preprocessing by the median filter, TTS by Colour <i>K</i>-Means Clustering (CKMC) for initial segmentation and Faster Region based Convolutional Neural Network (FR-CNN) for refined segmentation. The CKMC approach is evaluated using the different number of clusters (<i>k</i> = 3, 5, 7, and 9). An inception network with batch normalization is employed to segment melanoma regions effectively. Different loss functions such as Mean Absolute Error (MAE), Cross Entropy Loss (CEL), and Dice Loss (DL) are utilized for performance evaluation of the TTS system. The anchor box technique is employed to detect the melanoma region effectively. The TTS system is evaluated using 200 dermoscopic images from the PH<sup>2</sup> database. The segmentation accuracies are analyzed in terms of Pixel Accuracy (PA) and Jaccard Index (JI). Results show that the TTS system has 90.19% PA with 0.8048 JI for skin lesion segmentation using DL in FR-CNN with seven clusters in CKMC than CEL and MAE.", "Keywords": "clustering; convolution neural network; deep learning; melanoma diagnosis; Skin cancer; unsupervised segmentation", "DOI": "10.32604/iasc.2023.032068", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, St<PERSON>’s College of Engineering, Tamil Nadu, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON> <PERSON><PERSON><PERSON> Rahman Crescent Institute of Science and Technology, Tamil Nadu, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Artificial Intelligence and Data Science, Saveetha Engineering College (Autonomous), Tamil Nadu, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, St<PERSON>’s College of Engineering, Tamil Nadu, Chennai, India"}], "References": [{"Title": "Skin Melanoma Classification System Using Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "1", "Page": "1147", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A Hybrid Artificial Intelligence Model for Skin Cancer Diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "233", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 105705450, "Title": "Trust and QoS-Driven Query Service Provisioning Using Optimization", "Abstract": "The growing advancements with the Internet of Things (IoT) devices handle an enormous amount of data collected from various applications like healthcare, vehicle-based communication, and smart city. This research analyses cloud-based privacy preservation over the smart city based on query computation. However, there is a lack of resources to handle the incoming data and maintain them with higher privacy and security. Therefore, a solution based idea needs to be proposed to preserve the IoT data to set an innovative city environment. A querying service model is proposed to handle the incoming data collected from various environments as the data is not so trusted and highly sensitive towards vulnerability. If handling privacy, other inter-connected metrics like efficiency are also essential, which must be considered to fulfil the privacy requirements. Therefore, this work provides a query-based service model and clusters the query to measure the relevance of frequently generated queries. Here, a Bag of Query (BoQ) model is designed to collect the query from various sources. Validation is done with a descriptive service provisioning model to cluster the query and extract the query’s summary to get the final results. The processed data is preserved over the cloud storage system and optimized using an improved Grey Wolf Optimizer (GWO). It is used to attain global and local solutions regarding privacy preservation. The iterative data is evaluated without any over-fitting issues and computational complexity due to the tremendous data handling process. Based on this analysis, metrics like privacy, efficiency, computational complexity, the error rate is analyzed. The simulation is done with a MATLAB 2020a environment. The proposed model gives a better trade-off in contrast to existing approaches.", "Keywords": "Cloud computing; grey wolf optimization; IoT; privacy; security", "DOI": "10.32604/iasc.2023.028473", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SRM Institute of Engineering and Technology, Ramapuram, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Coimbatore Institute of Technology, Coimbatore, India"}], "References": [{"Title": "Data collection from WSNs to the cloud based on mobile Fog elements", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "864", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 105705454, "Title": "Cryptographic Algorithm for Enhancing Data Security in Wireless IoT Sensor Networks", "Abstract": "Wireless IoT Sensor Network can handle audio, video, text, etc., through the interconnection of ubiquitous devices. The entertainment and application-centric network relies on its autonomous nodes for handling large streams of multimedia data. Security breaches and threats due to insider attacks reduce the data handling and distribution capacity of the nodes. For addressing the insider attacks problem, Session-Critical Distributed Authentication Method (SCDAM) is proposed. The proposed method relies on short-lived concealed authentication based on an improved elliptic curve cryptography (ECC) algorithm. In this authentication, the session time and the interrupts are accounted for, providing end-to-end authentication. The session keys are distributed before and after each interrupt from which the shared data is authenticated. This authentication process uses a linear hash process, ensuring non-repetition of keys in the consecutive sessions. This proposed authentication method is capable of providing improved sessions, less data distribution loss, and complexity.", "Keywords": "elliptic curve cryptography; hash function; Insider attacks; IoT; session authentication", "DOI": "10.32604/iasc.2023.029397", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, SRM Institute of Science and Technology, Kattankulathur, Tamilnadu, Chennai, 603203, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, SRM Institute of Science and Technology, Kattankulathur, Tamilnadu, Chennai, 603203, India"}], "References": [{"Title": "Blockchain-Based Decentralized Reputation Management System for Internet of Everything in 6G-Enabled Cybertwin Architecture", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "4", "Page": "137", "JournalTitle": "Journal of New Media"}, {"Title": "Deformation Expression of Soft Tissue Based on BP Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "2", "Page": "1041", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Virtualized Load Balancer for Hybrid Cloud Using Genetic Algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "3", "Page": "1459", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 105705599, "Title": "Single Phase VSI Behavior Improvement using Combined Feedback and Feedforward Controllers", "Abstract": "<p>Nowadays, the demand for power electronics technology has increased due to the importance of its applications such as power inverters. The power inverter is required to modify the DC power from PV cells to AC power. One of the most common issues of on-grid PV systems is the high variations of the generated DC voltage. This will affect the stability of the generated AC voltage at the Point of Common Coupling (PCC) with the grid. This paper combines the Feed-Forward and Feedback (FFFB) controller in a novel way to reduce the variation of the PV cells DC voltage. This paper presents both mathematical and Simulink models of single-phase voltage source inverters VSI. Then, a case study of 15% disturbance of DC-generated voltage is considered. The static and dynamic behaviour of the single-phase H-bridge inverter is analysed under different loads. The new combination is used to reduce the effect of the disturbance on the performance of the system. Also, the proposed closed-loop controller (FFFB) can reduce the overshoot by 50% less than the feedback controller only. The settling time has been improved by 41%, 61% for RL and induction motor.</p>", "Keywords": "Combined Controller; Disturbance rejection; Feed-forward control; Feedback control; Voltage Source Inverter", "DOI": "10.37394/23203.2022.17.61", "PubYear": 2022, "Volume": "17", "Issue": "", "JournalId": 73207, "JournalTitle": "WSEAS TRANSACTIONS ON SYSTEMS AND CONTROL", "ISSN": "1991-8763", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Power and Mechatronics Engineering, College of Engineering, Tafila Technical University, Tafila 66110, JORDAN"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Jordan Customs, Engineering Department, Amman, JORDAN"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Electrical Engineering Department, College of Engineering, Al-Ahliyya Amman University, Amman 19328, JORDAN"}, {"AuthorId": 4, "Name": "Hesham Al Salem", "Affiliation": "Department of Mechanical Engineering, College of Engineering, Tafila Technical University, Tafila, 66110, JORDAN"}], "References": []}, {"ArticleId": 105705601, "Title": "Review on additive manufacturing and non-destructive testing", "Abstract": "Additive manufacturing is based on high-precision material deposition to build a final part or component by using various techniques. It is being one of the main advances in the fourth industrial revolution. This type of manufacturing is not new, although it is growing. There are many types of additive manufacturing techniques, and the use of efficient inspection methods to ensure a certain level of quality, and to detect faults, porosities, etc., are required in the industry. Nondestructive Testing is widely applied, and particularly in additive manufacturing, to ensure efficient quality control and preventive/predictive maintenance without changing the characteristics and initial state of the material. Each Nondestructive Testing technique is based on different physical principles; therefore, the selection and correct use of each technique depends on the application, the manufacturing process, the type of material and the possible discontinuities, among many others. This article develops a complete, exhaustive, and updated review and analysis of the state of the art of Nondestructive Testing applied in additive manufacturing. The main characteristics of the processes are analyzed, highlighting the most relevant works and the challenges that each technique should face. An analysis of techniques necessary for the development of Nondestructive Evaluation has been carried out, mainly Machine Learning techniques used for the quantification, detection and analysis of defects detected by Nondestructive Testing techniques.", "Keywords": "", "DOI": "10.1016/j.jmsy.2022.12.005", "PubYear": 2023, "Volume": "66", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ingenium Research Group, Universidad de Castilla-La Mancha, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ingenium Research Group, Universidad de Castilla-La Mancha, Spain;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Metallurgy and Materials, University of Birmingham, Birmingham, United Kingdom"}], "References": [{"Title": "Towards defect monitoring for metallic additive manufacturing components using phased array ultrasonic testing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "5", "Page": "1191", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Monitoring and repair of defects in ultrasonic additive manufacturing", "Authors": "Ven<PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "5-6", "Page": "1793", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "In-situ process monitoring for metal additive manufacturing through acoustic techniques using wavelet and convolutional neural network (CNN)", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "11-12", "Page": "3473", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 105705645, "Title": "Complementary and Substitutive Roles of Information Technology in the Relationship between Project Characteristics and Knowledge Integration in Software Teams", "Abstract": "Software development requires the assimilation of team members’ diverse knowledge, ideas, and skills for innovative solutions. A software team’s knowledge integration is affected by project characteristics, such as scale and interdependence, and the team’s use of information technology (IT). We examine how contingencies embedded in these dimensions influence team’s knowledge integration. We argue and show that IT-use plays either complementary or substitutive role in moderating the impacts of project scale and project interdependence on knowledge integration.", "Keywords": "Software development teams ; IT-use ; software projects ; project scale ; project interdependence ; knowledge integration", "DOI": "10.1080/10580530.2022.2028201", "PubYear": 2023, "Volume": "40", "Issue": "1", "JournalId": 21771, "JournalTitle": "Information Systems Management", "ISSN": "1058-0530", "EISSN": "1934-8703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information Systems and Supply Chain Management, University of North Carolina Greensboro, Greensboro, North Carolina, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON>lat School of Business, University of Alabama at Birmingham, Birmingham, Alabama, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Supply Chain and Information Systems Management, University of Tennessee, Knoxville, Tennessee, United States"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Systems and Analytics O.P. <PERSON> Global University, Sonipat, India"}], "References": []}, {"ArticleId": 105705724, "Title": "An Adversarial Dance: Toward an Understanding of Insiders’ Responses to Organizational Information Security Measures", "Abstract": "Despite the increased focus on organizational security policies and programs, some employees continue to engage in maladaptive responses to security measures (i.e., behaviors other than those recommended, intended, or prescribed). To help shed light on insiders’ adaptive and maladaptive responses to IS security measures, we conducted a case study of an organization at the forefront of security policy initiatives. Drawing on the beliefs-actions-outcomes (BAO) model to analyze our case data, we uncover a potentially nonvirtuous cycle consisting of security-related beliefs, actions, and outcomes, which we refer to as an “adversarial dance.” Explaining our results, we describe a novel belief framework that identifies four security belief profiles and uncovers an underexplored outcome of IS security: insiders’ lived security experiences. We find that individuals’ unfavorable lived security experiences produce counterproductive security-related beliefs that, in turn, lead to maladaptive behaviors. Maladaptive behaviors create new potential for security risk, leading to increased organizational security measures to counter them. Thus, the adversarial dance continues, as the new security measures have the potential to reinforce counterproductive security-related beliefs about the importance and risk of IS security and lead to new maladaptive behaviors. To help situate our findings within the current security literature, we integrate the results with prior research based on extant theories. While this paper is not the first to suggest that security measures can elicit maladaptive behaviors, the emergent belief framework and expanded BAO model of IS security constitute an important contribution to the behavioral IS security literature. © 2022, Association for Information Systems. All rights reserved.", "Keywords": "Belief Framework; Beliefs-Actions-Outcomes (BAO) Theory; Information System (IS) Security; Security Adverse Effects; Security Beliefs", "DOI": "10.17705/1jais.00798", "PubYear": 2023, "Volume": "24", "Issue": "1", "JournalId": 24874, "JournalTitle": "Journal of the Association for Information Systems", "ISSN": "", "EISSN": "1536-9323", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Louisiana State University, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Virginia, United States"}], "References": []}, {"ArticleId": 105705759, "Title": "Editorial N° 46", "Abstract": "<p>Este nuevo número de Comunicación y Medios inaugura una nueva etapa en su historia con la aceptación de Elsevier para integrar la prestigiosa base de datos Scopus. Como equipo editorial este logro significa un reconocimiento a la calidad de nuestros artículos, así como también al trabajo que dedicamos a la edición completa de cada número. Más allá de los cuestionamientos legítimos a los mecanismos de indexación y a los indicadores estandarizados, formar parte de Scopus implica una complejización de los procesos editoriales y un desafío para buscar y atraer nuevas voces y enfoques complejos en sintonía con los cambios epocales que las comunicaciones, el periodismo, la cultura y la tecnología atraviesan/atravesamos.</p>", "Keywords": "", "DOI": "10.5354/0719-1529.2022.69386", "PubYear": 2022, "Volume": "31", "Issue": "46", "JournalId": 48966, "JournalTitle": "Comunicación y Medios", "ISSN": "0716-3991", "EISSN": "0716-3991", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad de Chile, Chile"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad de Chile, Chile"}], "References": []}, {"ArticleId": 105705761, "Title": "A Domain-independent Dual-image based Robust Reversible Watermarking", "Abstract": "Robust reversible watermarking has attracted widespread attention in the field of information hiding in recent years. It should not only have robustness against attacks in transmission but also meet the reversibility of distortion-free transmission. According to our best knowledge, the most recent robust reversible watermarking methods adopt a single image as the carrier, which might lead to low efficiency in terms of carrier utilization. To address the issue, a novel dual-image robust reversible watermarking framework is proposed in this paper to effectively utilize the correlation between both carriers (namely dual images) and thus improve the efficiency of carrier utilization. In the dual-image robust reversible watermarking framework, a two-layer robust watermarking mechanism is designed to further improve the algorithm performances, i.e., embedding capacity and robustness. In addition, an optimization model is built to determine the parameters. Finally, the proposed framework is applied in different domains (namely domain-independent), i.e., Slantlet Transform and Singular Value Decomposition domain, and Zernike moments, respectively to demonstrate its effectiveness and generality. Experimental results demonstrate the superiority of the proposed dual-image robust reversible watermarking framework. Copyright © 2022 KSII.", "Keywords": "Domain-independent; Dual-image; Reversible watermarking; Robust Watermarking", "DOI": "10.3837/tiis.2022.12.014", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105705783, "Title": "Radio y salud mental en América Latina, <PERSON> y <PERSON>", "Abstract": "", "Keywords": "radio;salud mental", "DOI": "10.5354/0719-1529.2022.68908", "PubYear": 2022, "Volume": "31", "Issue": "46", "JournalId": 48966, "JournalTitle": "Comunicación y Medios", "ISSN": "0716-3991", "EISSN": "0716-3991", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Director Centro de Producciones Radiofónicas (CPR)"}], "References": []}, {"ArticleId": 105705786, "Title": "Poder político y económico en la televisión local: mediación institucional en canales regionales chilenos", "Abstract": "<p>La televisión local, también denominada de proximidad, está llamada a promover una comunicación democrática y participativa que visibilice los territorios locales y ponga en valor la identidad local, comunal o regional. El artículo analiza la relación entre las mediaciones institucionales de la televisión local y la producción de contenidos de proximidad e identifica aciertos y amenazas. Realizamos entrevistas en profundidad a productores de televisoras locales de la Región de Coquimbo (Chile), zona que contempla población urbana y rural, con ciudades de difícil acceso geográfico y alejadas del centro político tanto regional como capitalino. Los resultados demuestran que las municipalidades son las instituciones que se vinculan más estrecha y frecuentemente con las televisoras locales, ya que son medios utilizados para difundir iniciativas públicas de todo tipo, pero también ponen en pantalla la voz de las autoridades, debilitando —en algunas ocasiones— la independencia y el pluralismo informativo.</p>", "Keywords": "Televisión local; televisión de proximidad; pluralismo; mediaciones institucionales; Televisión chilena.", "DOI": "10.5354/0719-1529.2022.67039", "PubYear": 2022, "Volume": "31", "Issue": "46", "JournalId": 48966, "JournalTitle": "Comunicación y Medios", "ISSN": "0716-3991", "EISSN": "0716-3991", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-Maleb<PERSON><PERSON>", "Affiliation": "Universidad de La Serena, Chile"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad de La Serena, Chile"}], "References": []}, {"ArticleId": 105705799, "Title": "Café Del Cerro: Miles de voces dirán que no fue en vano. (2022). <PERSON>", "Abstract": "<p>Reseña del libro de María Eugen<PERSON>. (2022). Café Del Cerro: Miles de voces dirán que no fue en vano. Santiago: Café del Cerro Ediciones. Tomo I:  impreso. 412 pgs. ISBN: 978-956-410-222-1.  Tomo II: electrónico. 310 pgs. cafedelcerro.cl. </p>", "Keywords": "", "DOI": "10.5354/0719-1529.2022.69361", "PubYear": 2022, "Volume": "31", "Issue": "46", "JournalId": 48966, "JournalTitle": "Comunicación y Medios", "ISSN": "0716-3991", "EISSN": "0716-3991", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Temple University"}], "References": []}, {"ArticleId": 105705866, "Title": "Radio Nacional de Chile. La apuesta radiofónica de la dictadura cívico-militar chilena", "Abstract": "<p>Este artículo expone el desarrollo de la primera radioemisora estatal del país, Radio Nacional de Chile, durante la dictadura (1973-1990). Creada por la Junta Militar, se busca describir tanto el uso que se hizo de esta emisora para las comunicaciones del gobierno, como su influencia en la radiofonía nacional durante este período. Con un marco teórico de naturaleza flexible, con aportaciones de las comunicaciones, la estética y la historia, y una metodología mixta, se da cuenta de un objeto de estudio hasta ahora inédito. Pese al progresivo distanciamiento que fue tomando el aparato estatal de la cultura durante el régimen militar, principalmente por la creciente influencia del neoliberalismo, este no fue completo, como se demuestra en el caso de las radiocomunicaciones. Estas habrían sido rápidamente utilizadas como piezas estratégicas por las autoridades, para su ejercicio del poder y la gobernabilidad de la población, así como para su propia imagen. </p>", "Keywords": "Radiofonía; comunicaciones; estrategia comunicacional; dictadura cívico-militar chilena.", "DOI": "10.5354/0719-1529.2022.66031", "PubYear": 2022, "Volume": "31", "Issue": "46", "JournalId": 48966, "JournalTitle": "Comunicación y Medios", "ISSN": "0716-3991", "EISSN": "0716-3991", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-Leyton", "Affiliation": "Universidad San Sebastián, Chile"}], "References": []}, {"ArticleId": 105705893, "Title": "Malaysian Name-based Ethnicity Classification using LSTM", "Abstract": "Name separation (splitting full names into surnames and given names) is not a tedious task in a multiethnic country because the procedure for splitting surnames and given names is ethnicity-specific. Malaysia has multiple main ethnic groups; therefore, separating Malaysian full names into surnames and given names proves a challenge. In this study, we develop a two-phase framework for Malaysian name separation using deep learning. In the initial phase, we predict the ethnicity of full names. We propose a recurrent neural network with long short-term memory network-based model with character embeddings for prediction. Based on the predicted ethnicity, we use a rule-based algorithm for splitting full names into surnames and given names in the second phase. We evaluate the performance of the proposed model against various machine learning models and demonstrate that it outperforms them by an average of 9%. Moreover, transfer learning and fine-tuning of the proposed model with an additional dataset results in an improvement of up to 7% on average. Copyright © 2022 KSII.", "Keywords": "Deep Learning; Deep Learning-based Name Separation; Ethnicity Classification; LSTM; Machine Learning; Malaysian Name Separation; Recurrent Neural Network", "DOI": "10.3837/tiis.2022.12.004", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105705902, "Title": "Density-based Outlier Detection in Multi-dimensional Datasets", "Abstract": "Density-based outlier detection is one of the hot issues in data mining. A point is determined as outlier on basis of the density of points near them. The existing density-based detection algorithms have high time complexity, in order to reduce the time complexity, a new outlier detection algorithm DODMD (Density-based Outlier Detection in Multidimensional Datasets) is proposed. Firstly, on the basis of ZH-tree, the concept of micro-cluster is introduced. Each leaf node is regarded as a micro-cluster, and the micro-cluster is calculated to achieve the purpose of batch filtering. In order to obtain n sets of approximate outliers quickly, a greedy method is used to calculate the boundary of LOF and mark the minimum value as LOFmin Secondly, the outliers can filtered out by LOFmin, the real outliers are calculated, and then the result set is updated to make the boundary closer. Finally, the accuracy and efficiency of DODMD algorithm are verified on real dataset and synthetic dataset respectively. Copyright © 2022 KSII.", "Keywords": "density-based; micro-cluster; multi-dimensional data; outlier; z-order curve", "DOI": "10.3837/tiis.2022.12.002", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105705997, "Title": "Minimalistic Error via <PERSON><PERSON>bat <PERSON>gor<PERSON> for Attack-Defence Model on Wireless Sensor Networks (WSN)", "Abstract": "Wireless Sensor Networks have become the recent trend to effectively solve the problem in medical fields, primarily in agriculture and others in IoT monitoring. The expenses for ease of use in current domains are particular for cost and energy-effective solutions estimating different for different attack types on Wireless Sensor Network designs. Even though the energy, Routing problems are effectively solved, due to its wireless operative effects, the performance, such as speed and attackers, are reduced due to unevaded attacks. Hence, reducing this problem with energy-featured node optimization, network rate, and unevaded or random attack types like wormholes and black holes would implicate a significant problem in real-time modelling. On this basis, we postulate a solution analysis with the CLIBAT algorithm that implicates different possibilities and its probabilistic approaches, considering a proposed hybrid routing protocol on novel attack and defence algorithms to reduce the attack pattern with Wormhole and black hole attacks. In this perspective, an attack and defence pattern with an intuitive approach is implemented via the Improved Conditionally Expected Criteria feature to emphasize the type of attack (Wormhole or black hole attacks). Also, the Defense algorithm on improved sigmoid function on node characteristics is utilized to implicate with minimum distance formulations on the defense model effectively, MATLAB simulations with the solutions on WSN with CLIBAT algorithm inclusive of attack and defences are effectively removed.", "Keywords": "Attacks; DDoS; Firefly; Leach; Conditional  Logistic Intuitive BAT Algorithm (CLIBAT); Wireless Sensor  Networks (WSN); Distributed Energy-Efficient Clustering  (DEEC); Least Probability Gradient algorithm (LPA); Intuitive  Cumulative Expected Conditionality (ICEC); Multi-Point Route  (MPR).", "DOI": "10.22247/ijcna/2022/217700", "PubYear": 2022, "Volume": "9", "Issue": "6", "JournalId": 39456, "JournalTitle": "International Journal of Computer Networks And Applications", "ISSN": "", "EISSN": "2395-0455", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Koneru Lakshmaiah Education Foundation, Vaddeswaram, Guntur, Andhra Pradesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Koneru Lakshmaiah Education Foundation, Vaddeswaram, Guntur, Andhra Pradesh"}], "References": []}, {"ArticleId": 105706059, "Title": "Aplicación con realidad aumentada para apoyar al aprendizaje del sistema solar para alumnos de 5º y 6º de primaria en Tizayuca Hidalgo, en el periodo de 2020 al 2021", "Abstract": "<p>En este trabajo se presenta el desarrollo una aplicación de realidad aumentada, la cual tiene como objetivo apoyar de manera lúdica a los estudiantes de quinto y sexto grado de primaria para aprender los temas relacionados con nuestro sistema solar. Este contenido temático se encuentra en la materia de ciencias naturales dentro del programa recomendado por la SEP (Secretaria de Educacion Publica). La aplicación cuenta con modelos en 3D que representan los 8 planetas del sistema solar y otro modelo que representa el sol, cada modelo tiene incorporado un audio que se encargará de explicar el planeta. La aplicación cuenta con dos juegos, uno de ellos es una prueba en forma de cuestionario de opción múltiple y el otro es un juego de rompecabezas, con 4 niveles donde los estudiantes tendrán que formar la imagen de un planeta. También, mediante HTML5, CSS y JavaScript, se diseñó una página web que sirve para monitorear los resultados de la prueba que incluye la aplicación. Esta página está vinculada con una base datos. Los docentes podrán consultar los resultados de los alumnos desde su computadora, para esto solo es necesario que los docentes se registren con un correo y establecer una contraseña. Este proyecto no se implementó en el escenario para el cual fue creado debido a la contingencia derivada por el Covid-19 pero se probó el producto final y este funciono de manera adecuada. </p>", "Keywords": "Educacion;Realidad aumentada;3D;Sistema solar", "DOI": "10.29057/est.v8i16.8787", "PubYear": 2023, "Volume": "8", "Issue": "16", "JournalId": 52418, "JournalTitle": "Boletín Científico INVESTIGIUM de la Escuela Superior de Tizayuca", "ISSN": "", "EISSN": "2448-4830", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 3, "Name": "Israel Acuña Galván", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}], "References": []}, {"ArticleId": 105706069, "Title": "Aplicación de la tecnología Blockchain como estrategia tecnológica en la logística de la empresa", "Abstract": "<p>Tecnologías emergentes como Blockchain han revolucionado la forma en que se realiza el trabajo y los procesos en las empresas, es el caso de la aplicación en cadena de suministro donde se han realizado estudios para establecer ventajas competitivas como son la confianza y seguridad en el proceso. La decisión de implementar una tecnología no es cuestión de aplicar la que está en voga, es un proceso de definición de estrategias tecnológicas alineadas a la estrategia empresarial. En este artículo se realiza un estudio de la aplicación de la tecnología Blockchain en la logística, particularmente, en la cadena de suministros como estrategia tecnológica. Se revisaron fuentes de información secundarias y primarias para lograr esclarecer el camino del uso de esta tecnología en este rubro, así mismo se analizaron las técnicas para el establecimiento de la estrategia tecnológica para justificar el uso de la tecnología.</p>", "Keywords": "Blockchain;estrategias tecnológicas;logística;cadena de suministro", "DOI": "10.29057/est.v8i16.8829", "PubYear": 2023, "Volume": "8", "Issue": "16", "JournalId": 52418, "JournalTitle": "Boletín Científico INVESTIGIUM de la Escuela Superior de Tizayuca", "ISSN": "", "EISSN": "2448-4830", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>-León", "Affiliation": "Universidad Politécnica Metropolitana de Hidalgo"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Hidalgo"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad Popular Autónoma del Estado de Puebla"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universidad Politécnica Metropolitana de Hidalgo"}], "References": []}, {"ArticleId": 105706160, "Title": "Proactive and visual approach for product maintainability design", "Abstract": "Maintainability design, as a vital element in product design, is generally conducted after the physical or virtual prototype is done, and this design way always accompanies with the characters of lag, passiveness, subjectivity and lack of relevance. And even design flaws are exposed based on corresponding maintainability analysis, the improvements are hardly finished owing to various practical difficulties. This paper proposed a novel proactive maintainability design method. Based on the maintainability and functionability/structurability (F/S) factors of the product first, a “many to many” mapping relationship between maintainability and F/S spaces is established, in which quantitative assessment and visualization representation are two aspects. Separately, the quantitative assessment provides an objective and precise result of relationship confirmation, and the visualization representation brings a comprehensive and intuitive way for maintainability and F/S designers. Finally, the case study section shows the availability and effectiveness of the methodology by verification and comparison. The proposed methodology considers maintainability affection on F/S just from the start point of product design. Hence, compared with the physical prototype and virtual prototype approach, the implementation of the proposed methodology can make maintainability design and functional/structural design to be conducted almost simultaneously.", "Keywords": "Proactive approach ; Maintainability design ; Quantitative correlation analysis ; Concurrent design ; Data visualization", "DOI": "10.1016/j.aei.2022.101867", "PubYear": 2023, "Volume": "55", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Reliability and Systems Engineering, Beijing University of Aeronautics and Astronautics, Beijing, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Reliability and Systems Engineering, Beijing University of Aeronautics and Astronautics, Beijing, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Reliability and Systems Engineering, Beijing University of Aeronautics and Astronautics, Beijing, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Reliability and Systems Engineering, Beijing University of Aeronautics and Astronautics, Beijing, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technology and Engineering Center for Space Utilization, Chinese Academy of Sciences, Beijing, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Reliability and Systems Engineering, Beijing University of Aeronautics and Astronautics, Beijing, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Reliability and Systems Engineering, Beijing University of Aeronautics and Astronautics, Beijing, PR China;State Key Laboratory of Virtual Reality Technology and System, Beijing, PR China;Corresponding author at: Room 633, Weimin Building, Beijing University of Aeronautics and Astronautics, 37#, Xueyuan Road, Haidian District, Beijing, PR China"}], "References": [{"Title": "Formal techniques for consistency checking of orchestrations of semantic Web services", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "", "Page": "101165", "JournalTitle": "Journal of Computational Science"}, {"Title": "Consistency checking of STNs with decisions: Managing temporal and access-control constraints in a seamless way", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "280", "Issue": "", "Page": "104637", "JournalTitle": "Information and Computation"}, {"Title": "Automatic classification of wall and door BIM element subtypes using 3D geometric deep neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101200", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A judgment-based model for usability evaluating of interactive systems using fuzzy Multi Factors Evaluation (MFE)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "117", "Issue": "", "Page": "108411", "JournalTitle": "Applied Soft Computing"}, {"Title": "Investment estimation of prefabricated concrete buildings based on XGBoost machine learning algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101789", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 105706232, "Title": "Investment and Risk Management with Online News and Heterogeneous Networks", "Abstract": "<p>Stock price movements in financial markets are influenced by large volumes of news from diverse sources on the web, e.g., online news outlets, blogs, social media. Extracting useful information from online news for financial tasks, e.g., forecasting stock returns or risks, is however challenging due to the low signal-to-noise ratios of such online information. Assessing the relevance of each news article to the price movements of individual stocks is also difficult, even for human experts. In this paper, we propose the Guided Global-Local Attention-based Multimodal Heterogeneous Network (GLAM) model, which comprises novel attention-based mechanisms for multimodal sequential and graph encoding, a guided learning strategy, and a multitask training objective. GLAM uses multimodal information, heterogeneous relationships between companies and leverages significant local responses of individual stock prices to online news to extract useful information from diverse global online news relevant to individual stocks for multiple forecasting tasks. Our extensive experiments with multiple datasets show that GLAM out-performs other state-of-the-art models on multiple forecasting tasks, and investment and risk management application case-studies.</p>", "Keywords": "Graph neural networks; transformers; attention mechanisms; time-series forecasting; networks; multimodality; embeddings; finance; natural language processing", "DOI": "10.1145/3532858", "PubYear": 2023, "Volume": "17", "Issue": "2", "JournalId": 10867, "JournalTitle": "ACM Transactions on the Web", "ISSN": "1559-1131", "EISSN": "1559-114X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Singapore Management University, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Singapore Management University, Singapore"}], "References": [{"Title": "DSTP-RNN: A dual-stage two-phase attention-based recurrent neural network for long-term and multivariate time series prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "113082", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep Learning for Time Series Forecasting: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "3", "JournalTitle": "Big Data"}, {"Title": "Applications of deep learning in stock market prediction: Recent progress", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115537", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 105706243, "Title": "COMPUTATIONAL LINGUISTIC MATERIAL FOR VIETNAMESE SPEECH PROCESSING: APPLYING IN VIETNAMESE TEXT-TO-SPEECH", "Abstract": "The motivation of this paper is to propose a set of best-quality linguistic materials for Vietnamese speech processing, which can be used for Vietnamese TTS and ASR problems. This proposed material includes: (1) a pronunciation dictionary, which adapts from X-SAMPA,  (2) a rule-based grapheme to phoneme for Vietnamese. In order to test and evaluate, we have built a Vietnamese TTS system based on the Merlin engine, using the above materials, and evaluating the quality of speech and the accuracy of pronunciation. The results show that the applicability of these materials is favorable for further research and development on Vietnamese speech processing.", "Keywords": "Text-to-speech; Dictionary; Grapheme-to-Phoneme; X-SAMPA; computer coding; speech processing; Vietnamese", "DOI": "10.26483/ijarcs.v13i6.6935", "PubYear": 2022, "Volume": "13", "Issue": "6", "JournalId": 35682, "JournalTitle": "International Journal of Advanced Research in Computer Science", "ISSN": "", "EISSN": "0976-5697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology Hanoi University of Mining and Geology Hanoi, Vietnam"}], "References": []}, {"ArticleId": 105706302, "Title": "Emerging DNA cryptography-based encryption schemes: a review", "Abstract": "Security has been the fundamental apprehension during information transmission and storage. Communication network is inordinately susceptible to intrusion from unpredictable adversaries thus threatening the confidentiality, integrity and authenticity of data. This is where cryptography facilitates us and encodes the original message into an incomprehensible and unintelligible form. DNA cryptography is the latest propitious field in cryptography that has transpired with the advancement of DNA computing. The immense parallelism, unrivalled energy efficiency and exceptional information density of DNA molecules is being traversed for cryptographic purpose. Currently, it is in the preliminary stage and necessitates avid scrutinisation. The foremost hindrance in the field of DNA cryptography is computational complexity and lack of sophisticated laboratories. In this paper, we discuss the existing DNA cryptographic approaches and compare their achievements and limitations to provide a better perception. In the end, a modified version of the DNA cryptography combined with soft computing is also suggested. Copyright © 2023 Inderscience Enterprises Ltd.", "Keywords": "bio-inspired cryptography; DNA computing; DNA cryptography; encryption; security", "DOI": "10.1504/IJICS.2023.128000", "PubYear": 2023, "Volume": "20", "Issue": "1/2", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering, KIIT (Deemed to be University), Patia, Odisha, Bhubaneshwar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering, KIIT (Deemed to be University), Patia, Odisha, Bhubaneshwar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Application, KIIT (Deemed to be University), Patia, Odisha, Bhubaneshwar, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center for Robust Speech Systems, The University of Texas at Dallas, Richardson, TX  75080, United States"}], "References": []}, {"ArticleId": 105706403, "Title": "Inter-comparison of optical and SAR-based forest disturbance warning systems in the Amazon shows the potential of combined SAR-optical monitoring", "Abstract": "More than half a decade after the launch of the Sentinel-1A C-band SAR satellite, several near real-time forest disturbances detection systems based on backscattering time series analysis have been developed and made operational. Every system has its own particular approach to change detection. Here, we have compared the performance of the main SAR-based near real-time operational forest disturbance detection systems produced by research agencies (INPE, in Brazil, CESBIO, in France, JAXA, in Japan, and Wageningen University, in the Netherlands), and compared them to the state-of-the-art optical algorithm, University of Maryland’s GLAD-S2. We implemented an innovative validation protocol, specially conceived to encompass all the analysed systems, which measured every system’s accuracy and detection speed in four different areas of the Amazon basin. The results indicated that, when parametrized equally, all the Sentinel-1 SAR methods outperformed the reference optical method in terms of sample-count F1-Score, having comparable results among them. The GLAD-S2 optical method showed superior results in terms of user’s accuracy (UA), issuing no false detections, but had a lower producer accuracy (PA, 84.88%) when compared to the Sentinel-1 SAR-based systems (PA ∼ 90%). Wageningen University’s system, RADD, proved to be relatively faster, especially in heavily clouded regions, where RADD warnings were issued 41 days before optical ones, and the one that better performs on small disturbed patches ( < 0.25 ha) with a UA of 70.11%. Of all the high-resolution SAR methods, CESBIO’s had the best results regarding UA (99.0%). Finally, we tested the potential of three hypothetical combined optical-SAR systems. The results show that these combined systems would have excellent detection capabilities, exceeding largely the producer’s accuracy of all the tested methods at the cost of a slightly diminished user’s accuracy, and constitute a promising and feasible approach for the forthcoming forest monitoring systems. POLICY HIGHLIGHTS Recently developed automated SAR-based tropical forest disturbance detection systems showed excellent detection accuracies, even in small, difficult-to-spot deforested patches. SAR detections can be as precise and as fast as optical ones, being more precise and faster in very cloudy areas or in areas subjected to illegal mining. The combination of recently developed SAR and optical warnings systems can yield optimized results, in terms of overall accuracy and producer's accuracy. Acknowledgments PlanetScope data used on validation was provided through Norway’s International Climate and Forests Initiative (NICFI). Contains modified Copernicus Sentinel data (2021). Disclosure statement J.D.,S.M.,A.B.,J.R. and M.W. participate or have participated on the development of the analysed systems and hereby declare that they have not, in any way, influenced the validation process, which has been performed by L.L. Data availability statement The data that support the findings of this study are available from the corresponding author, J.D., upon reasonable request. Additional information Funding The first author was funded by the National Council for Scientific and Technological Development, project “Monitoring Brazilian Biomes by Satellite – Building new capacities”/process 444418/2018-0, grant #350303/2021-5, and the Coordenação de Aperfeiçoamento de Pessoal de Nível Superior - Brasil (CAPES) - Finance Code 001, as part of the Internationalization program PrInt-INPE. RADD project received funding through Norway’s Climate and Forest Initiative (NICFI), the US Government’s SilvaCarbon program.", "Keywords": "", "DOI": "10.1080/01431161.2022.2157684", "PubYear": 2023, "Volume": "44", "Issue": "1", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Earth Observation and Geoinformatics Division (DIOTG), National Institute for Space Research (INPE), São José dos Campos, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "SIG Formation, IDGEO, Toulouse, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "GlobEO, Toulouse, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CNRS/CNES/IRD/INRAE/UPS, CESBIO, Toulouse, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Laboratory of Geo-information Science and Remote Sensing, Wageningen University, Wageningen, The Netherlands"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Earth Observation Research Center, Japan Aerospace Exploration Agency (JAXA), Chofu, Japan"}, {"AuthorId": 7, "Name": "Sidnei Sant Anna", "Affiliation": "Earth Observation and Geoinformatics Division (DIOTG), National Institute for Space Research (INPE), São José dos Campos, Brazil"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Earth Observation and Geoinformatics Division (DIOTG), National Institute for Space Research (INPE), São José dos Campos, Brazil"}], "References": [{"Title": "Mitigating the effects of omission errors on area and area change estimates", "Authors": "<PERSON><PERSON>; <PERSON>; Andres B. <PERSON>", "PubYear": 2020, "Volume": "236", "Issue": "", "Page": "111492", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "SAR data for tropical forest disturbance alerts in French Guiana: Benefit over optical imagery", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "252", "Issue": "", "Page": "112159", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Refined algorithm for forest early warning system with ALOS-2/PALSAR-2 ScanSAR data in tropical forest regions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "265", "Issue": "", "Page": "112643", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Cloud Mask Intercomparison eXercise (CMIX): An evaluation of cloud masking algorithms for Landsat 8 and Sentinel-2", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "274", "Issue": "", "Page": "112990", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 105706528, "Title": "High-Speed DSP Pipelining and Retiming techniques for Distributed-Arithmetic RNS-based FIR Filter Design", "Abstract": "<p>Digital FIR Filters plays a major role in many signal processing applications. Generally, these filters are designed with multipliers and adders to find the filter output. This paper acquaints how to reduce the complexity of higher order FIR filter by using performance optimization techniques like retiming and pipelining. The filter’s throughput, energy efficiency, and latency, as well as the complexity of its technology, all need to be improved. By adopting pipelining technique, the arithmetic processes of addition and multiplication are separated. The break addition procedure is retimed. The architecture of Pipelining and Retiming with m-tap filters and n-bit word lengths were designed. The smallest delay achieved by the proposed distributed arithmetic-based FIR Filter with pipelining was 2.564ns for a 4tap implementation receiving an 8bit input, while the largest delay achieved was 56.04ns for a 64-tap implementation receiving a 32-bit word length. Delays as low as 0.68ns for a 4-tap implementation receiving an 8-bit input and as high as 4.53ns for a 64tap implementation receiving a 32bit word length have been achieved by using the suggested distributed arithmetic-based FIR Filter with retiming approach. Delay has been reduced by 73.2% for 4tap with 8bit input and by 91.9% for 64tap with 32bit word length compared to the pipelining approach.</p>", "Keywords": "Area Delay Product; Distributed Arithmetic; Pipelining; Power Delay Product; Retiming", "DOI": "10.37394/23203.2022.17.60", "PubYear": 2022, "Volume": "17", "Issue": "", "JournalId": 73207, "JournalTitle": "WSEAS TRANSACTIONS ON SYSTEMS AND CONTROL", "ISSN": "1991-8763", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Jawaharlal Nehru Technological University Anantapur, Ananthapuramu, Andhra Pradesh, INDIA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Professor, Department of ECE, <PERSON>ee <PERSON>han Engineering College, Andhra Pradesh, INDIA"}], "References": [{"Title": "ASIC implementation of distributed arithmetic based FIR filter using RNS for high speed DSP systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "2", "Page": "259", "JournalTitle": "International Journal of Speech Technology"}]}, {"ArticleId": 105706661, "Title": "Sparse Reconstruction Using Hyperbolic Tangent as Smooth l1-Norm Approximation", "Abstract": "In the Compressed Sensing (CS) framework, the underdetermined system of linear equation (USLE) can have infinitely many possible solutions. However, we intend to find the sparsest possible solution, which is l0-norm minimization. However, finding an l0 norm solution out of infinitely many possible solutions is NP-hard problem that becomes non-convex optimization problem. It has been a practically proven fact that l0 norm penalty can be adequately estimated by l1 norm, which recasts a non-convex minimization problem to a convex problem. However, l1 norm non-differentiable and gradient-based minimization algorithms are not applicable, due to this very reason there is a need to approximate l1 norm by its smooth approximation. Iterative shrinkage algorithms provide an efficient method to numerically minimize l1-regularized least square optimization problem. These algorithms are required to induce sparsity in their solutions to meet the CS recovery requirement. In this research article, we have developed a novel recovery method that uses hyperbolic tangent function to recover undersampled signal/images in CS framework. In our work, l1 norm and soft thresholding are both approximated with the hyperbolic tangent functions. We have also proposed the criteria to tune optimization parameters to get optimal results. The error bounds for the proposed l1 norm approximation are evaluated. To evaluate performance of our proposed method, we have utilized a dataset comprised of 1-D sparse signal, compressively sampled MR image and cardiac cine MRI. The MRI is an important imaging modality for assessing cardiac vascular function. It provides the ejection fraction and cardiac output of the heart. However, this advantage comes at the cost of a slow acquisition process. Hence, it is essential to speed up the acquisition process to take the full benefits of cardiac cine MRI. Numerical results based on performance metrics, such as Structural Similarity (SSIM), Peak Signal to Noise Ratio (PSNR) and Root Mean Square Error (RMSE) show that the proposed tangent hyperbolic based CS recovery offers a much better performance as compared to the traditional Iterative Soft Thresholding (IST) recovery methods.", "Keywords": "", "DOI": "10.3390/computation11010007", "PubYear": 2023, "Volume": "11", "Issue": "1", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Faculty of Engineering and Technology, International Islamic University, Islamabad 44000, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Faculty of Engineering and Technology, International Islamic University, Islamabad 44000, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electrical Section, British Malaysian Institute, Universiti Kuala Lumpur, Gombak 53100, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of British Columbia, Vancouver, BC V6T 1Z4, Canada"}], "References": []}, {"ArticleId": 105706678, "Title": "Cooperative Channel and Optimized Route Selection in Adhoc Network", "Abstract": "Over the last decade, mobile Adhoc networks have expanded dramatically in popularity, and their impact on the communication sector on a variety of levels is enormous. Its uses have expanded in lockstep with its growth. Due to its instability in usage and the fact that numerous nodes communicate data concurrently, adequate channel and forwarder selection is essential. In this proposed design for a Cognitive Radio Cognitive Network (CRCN), we gain the confidence of each forwarding node by contacting one-hop and second level nodes, obtaining reports from them, and selecting the forwarder appropriately with the use of an optimization technique. At that point, we concentrate our efforts on their channel, selection, and lastly, the transmission of data packets via the designated forwarder. The simulation work is validated in this section using the MATLAB program. Additionally, steps show how the node acts as a confident forwarder and shares the channel in a compatible method to communicate, allowing for more packet bits to be transmitted by conveniently picking the channel between them. We calculate the confidence of the node at the start of the network by combining the reliability report for the first hop and the reliability report for the secondary hop. We then refer to the same node as the confident node in order to operate as a forwarder. As a result, we witness an increase in the leftover energy in the output. The percentage of data packets delivered has also increased.", "Keywords": "Adhoc Network; channel selection; confident; forwarder; one-hop; optimized route selection; secondary report", "DOI": "10.32604/iasc.2023.030540", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON>’s Institute of Technology, Tamilnadu, Chennai, 600119, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, SNS College of Technology, Tamilnadu, Coimbatore, 641035, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, SNS College of Engineering, Tamilnadu, Coimbatore, 641107, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Karpagam Institute of Technology, Tamilnadu, Coimbatore, 641032, India"}], "References": []}, {"ArticleId": 105706851, "Title": "Role of gastrointestinal ultrasound in image-guided radiation therapy: A review", "Abstract": "Radiation therapy is one of the main modalities for cancer treatment. New radiotherapy techniques provide conformal radiation dose distribution and need higher accuracy in treatment delivery. Internal organ motions and displacements between and within the radiotherapy treatment fraction affect and weaken the reproducibility of delivering the planned irradiation to the target position. Image-guided radiation therapy (IGRT) can be used as a critical technology to improve patient positioning accuracy and reduce uncertainties in tumor target definition during treatment delivery. Ultrasound, a non-invasive imaging technique, is one of the important modalities in IGRT for accurate pre-treatment patient positioning and fiducial marker placement. The current study purposed to summarize recent advancements in ultrasound applications, techniques, and possible use in radiotherapy for cancers located in the abdomen and pelvic regions. We hope to give the readers a comprehensive understanding of the emerging clinical ultrasound-based IGRT applications and techniques.", "Keywords": "Gastrointestinal ; Cancer ; Ultrasound imaging ; Image-guided radiation therapy", "DOI": "10.1016/j.jrras.2022.100520", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Qiuchen Lu", "Affiliation": "Department of Ultrasound Medicine, Mianyang Central Hospital, School of Medicine, University of Electronic Science and Technology of China, Mianyang, 621000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ultrasound Medicine, Mianyang Central Hospital, School of Medicine, University of Electronic Science and Technology of China, Mianyang, 621000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Wei", "Affiliation": "Department of Ultrasound Medicine, Mianyang Central Hospital, School of Medicine, University of Electronic Science and Technology of China, Mianyang, 621000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ultrasound Medicine, Mianyang Central Hospital, School of Medicine, University of Electronic Science and Technology of China, Mianyang, 621000, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Ultrasound Medicine, Mianyang Central Hospital, School of Medicine, University of Electronic Science and Technology of China, Mianyang, 621000, China;Corresponding author"}], "References": []}, {"ArticleId": 105706949, "Title": "The stream data warehouse: Page replacement algorithms and quality of service metrics", "Abstract": "The stream data warehouse is an answer to the rapidly changing world of data analysis, which demands reliable and up-to-date results, obtained in a near real-time manner. Therefore it is a subject of recent research involving such areas as continuous updates and low-latency response, for example. In this paper, we study the stream adaptation of the OLAP cube and, in particular — its memory paging mechanism. It is driven by the page replacement algorithm, which manages the efficient data transfer and thus supplies users with constantly updatable data cubes. The following paper introduces an entirely novel approach to this topic. By perceiving the page replacement process as a multi-objective optimization problem, we propose three new algorithms that constantly analyze their varying environment and adapt to those changes by adjusting their behavior. Moreover, they consider user-provided constraints, which impose maximal values of specific parameters that cannot be exceeded. In addition to the page replacement algorithms, we propose two distinct quality of service metrics that measure the overall efficiency of data transfer inside the stream data warehouse. In order to verify and compare the new algorithms with their older counterparts, a series of experiments were conducted. Their results have confirmed that the proposed algorithms meet their requirements and visibly outperform the original solutions. The average wait time decreased between 25% and 66% (from 1.3x to 3.0x respectively, depending on the chosen algorithm), whereas the peak wait time decreased by approximately 99% (between 100x and 190x respectively).", "Keywords": "", "DOI": "10.1016/j.future.2023.01.003", "PubYear": 2023, "Volume": "142", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Informatics, Faculty of Automatic Control, Electronics, and Computer Science, Silesian University of Technology, Akademicka 16, 44-100, Gliwice, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Informatics, Faculty of Automatic Control, Electronics, and Computer Science, Silesian University of Technology, Akademicka 16, 44-100, Gliwice, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Informatics, Faculty of Automatic Control, Electronics, and Computer Science, Silesian University of Technology, Akademicka 16, 44-100, Gliwice, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Informatics, Faculty of Automatic Control, Electronics, and Computer Science, Silesian University of Technology, Akademicka 16, 44-100, Gliwice, Poland;Corresponding author"}], "References": [{"Title": "Efficient approach of recent high utility stream pattern mining with indexed list structure and pruning strategy considering arrival times of transactions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "529", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "RHUPS", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 105706976, "Title": "Co-Membership-based Generic Anomalous Communities Detection", "Abstract": "<p>Nowadays, detecting anomalous communities in networks is an essential task in research, as it helps discover insights into community-structured networks. Most of the existing methods leverage either information regarding attributes of vertices or the topological structure of communities. In this study, we introduce the Co-Membership-based Generic Anomalous Communities Detection Algorithm (referred as to <i>CMMAC</i>), a novel and generic method that utilizes the information of vertices co-membership in multiple communities. <i>CMMAC</i> is domain-free and almost unaffected by communities' sizes and densities. Specifically, we train a classifier to predict the probability of each vertex in a community being a member of the community. We then rank the communities by the aggregated membership probabilities of each community's vertices. The lowest-ranked communities are considered to be anomalous. Furthermore, we present an algorithm for generating a community-structured random network enabling the infusion of anomalous communities to facilitate research in the field. We utilized it to generate two datasets, composed of thousands of labeled anomaly-infused networks, and published them. We experimented extensively on thousands of simulated, and real-world networks, infused with artificial anomalies. <i>CMMAC</i> outperformed other existing methods in a range of settings. Additionally, we demonstrated that <i>CMMAC</i> can identify abnormal communities in real-world unlabeled networks in different domains, such as Reddit and Wikipedia.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2023, Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "Anomalous community detection;Anomalous subgraph detection;Anomaly detection;Complex networks analysis;Social networks analysis", "DOI": "10.1007/s11063-022-11103-1", "PubYear": 2023, "Volume": "55", "Issue": "5", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Software and Information Systems Engineering, Ben-Gurion University of the Negev, Beer-Sheva, Israel."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Software and Information Systems Engineering, Ben-Gurion University of the Negev, Beer-Sheva, Israel."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Software and Information Systems Engineering, Ben-Gurion University of the Negev, Beer-Sheva, Israel."}], "References": [{"Title": "Tailored feedforward artificial neural network based link prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "3", "Page": "757", "JournalTitle": "International Journal of Information Technology"}, {"Title": "A comparative study of overlapping community detection methods from the perspective of the structural properties", "Authors": "<PERSON><PERSON><PERSON>; Carolina Ribeiro Xavier; Alexandre <PERSON> Ev<PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Applied Network Science"}, {"Title": "A modified DeepWalk method for link prediction in attributed social network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "10", "Page": "2227", "JournalTitle": "Computing"}]}, {"ArticleId": 105707011, "Title": "Improved bounds for rectangular monotone Min-Plus Product and applications", "Abstract": "In a recent breakthrough paper, <PERSON> et al. (STOC&#x27;22) introduce an O ˜ ( n 3 + ω 2 ) time algorithm to compute Monotone Min-Plus Product between two square matrices of dimensions n × n and polynomial bounded values. This greatly improves upon the previous O ˜ ( n 12 + ω 5 ) time algorithm and as a consequence improves bounds for its applications. Several other applications involve Monotone Min-Plus Product between rectangular matrices, and even if <PERSON> et al.&#x27;s algorithm seems applicable for the rectangular case, the generalization is not straightforward. In this paper we present a generalization of the algorithm of <PERSON> et al. to solve Monotone Min-Plus Product for rectangular matrices with polynomial bounded values. We next use this faster algorithm to improve running times for the following applications of Rectangular Monotone Min-Plus Product: M -bounded Single Source Replacement Path, Batch Range Mode, k -Dyck Edit Distance and 2-approximation of All Pairs Shortest Path. We also improve the running time for Unweighted Tree Edit Distance using the algorithm by <PERSON> et al..", "Keywords": "Algorithms ; Fine-grained complexity ; Monotone Min-Plus Product", "DOI": "10.1016/j.ipl.2023.106358", "PubYear": 2023, "Volume": "181", "Issue": "", "JournalId": 5748, "JournalTitle": "Information Processing Letters", "ISSN": "0020-0190", "EISSN": "1872-6119", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "École Polytechnique Fédérale de Lausanne, Switzerland"}], "References": []}, {"ArticleId": 105707064, "Title": "Canonical and non-canonical functions of NLRP3", "Abstract": "<b  >Background</b> Since its discovery, NLRP3 is almost never separated from its major role in the protein complex it forms with ASC, NEK7 and Caspase-1, the inflammasome. This key component of the innate immune response mediates the secretion of proinflammatory cytokines IL-1β and IL-18 involved in immune response to microbial infection and cellular damage. However, NLRP3 has also other functions that do not involve the inflammasome assembly nor the innate immune response. These non-canonical functions have been poorly studied. Nevertheless, NLRP3 is associated with different kind of diseases probably through its inflammasome dependent function as through its inflammasome independent functions. <b  >Aim of the review</b> The study and understanding of the canonical and non-canonical functions of NLRP3 can help to better understand its involvement in various pathologies. In parallel, the description of the mechanisms of action and regulation of its various functions, can allow the identification of new therapeutic strategies. <b  >Key scientific concepts of the review</b> NLRP3 functions have mainly been studied in the context of the inflammasome, in myeloid cells and in totally deficient transgenic mice. However, for several year, the work of different teams has proven that NLRP3 is also expressed in other cell types where it has functions that are independent of the inflammasome. If these studies suggest that NLRP3 could play different roles in the cytoplasm or the nucleus of the cells, the mechanisms underlying NLRP3 non-canonical functions remain unclear. This is why we propose in this review an inventory of the canonical and non-canonical functions of NLRP3 and their impact in different pathologies.", "Keywords": "NLRP3;inflammasome;myeloid cells;uncanonical functions", "DOI": "10.1016/j.jare.2023.01.001", "PubYear": 2023, "Volume": "53", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculté des Sciences de Santé- University of Burgundy - Dijon - 21000- FRANCE; CAdIR Team - Centre de Recherche INSERM - UMR 1231 - Dijon - 21000 - FRANCE."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculté des Sciences de Santé- University of Burgundy - Dijon - 21000- FRANCE; CAdIR Team - Centre de Recherche INSERM - UMR 1231 - Dijon - 21000 - FRANCE; Université de Bourgogne Franche-Comté - Dijon - 21000 - FRANCE."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculté des Sciences de Santé- University of Burgundy - Dijon - 21000- FRANCE; CAdIR Team - Centre de Recherche INSERM - UMR 1231 - Dijon - 21000 - FRANCE; Department of Biology and Pathology of Tumors - Centre anticancéreux GF Leclerc - Dijon - 21000 - FRANCE. Electronic address:  ."}], "References": []}, {"ArticleId": 105707072, "Title": "Issue Information", "Abstract": "", "Keywords": "", "DOI": "10.1002/sam.11611", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 5556, "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal", "ISSN": "1932-1864", "EISSN": "1932-1872", "Authors": [], "References": []}, {"ArticleId": 105707104, "Title": "Archimedes Optimizer: Theory, Analysis, Improvements, and Applications", "Abstract": "<p>The intricacy of the real-world numerical optimization tribulations has full-fledged and diversely amplified necessitating proficient yet ingenious optimization algorithms. In the domain wherein the classical approaches fall short, the predicament resolving nature-inspired optimization algorithms (NIOA) tend to hit upon an excellent solution to unbendable optimization problems consuming sensible computation time. Nevertheless, in the last few years approaches anchored in nonlinear physics have been anticipated, announced, and flourished. The process based on non-linear physics modeled in the form of optimization algorithms and as a subset of NIOA, in countless cases, has successfully surpassed the existing optimization methods with their effectual exploration knack thus formulating utterly fresh search practices. Archimedes Optimization Algorithm (AOA) is one of the recent and most promising physics optimization algorithms that use meta-heuristics phenomenon to solve real-world problems by either maximizing or minimizing a variety of measurable variables such as performance, profit, and quality. In this paper, Archimedes Optimization Algorithm (AOA) has been discussed in great detail, and also its performance was examined for Multi-Level Thresholding (MLT) based image segmentation domain by considering t-entropy and Tsallis entropy as objective functions. The experimental results showed that among recent Physics Inspired Optimization Algorithms (PIOA), the Archimedes Optimization Algorithm (AOA) produces very promising outcomes with Tsallis entropy rather than with t-entropy in both color standard images and medical pathology images.</p><p>© The Author(s) under exclusive licence to International Center for Numerical Methods in Engineering (CIMNE) 2023, Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "Archimedes optimization algorithm;Image segmentation;Nature-inspired optimization algorithms;Optimization;Physics inspired optimization algorithms;Tsallis;t-entropy", "DOI": "10.1007/s11831-022-09876-8", "PubYear": 2023, "Volume": "30", "Issue": "4", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Application, Midnapore College (Autonomous), Paschim Medinipur, Midnapore, West Bengal India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON><PERSON>zad University of Technology, Kolkata, West Bengal India."}, {"AuthorId": 3, "Name": "Rebika Rai", "Affiliation": "Department of Computer Applications, Sikkim University, Gangtok, Sikkim India."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Application, Midnapore College (Autonomous), Paschim Medinipur, Midnapore, West Bengal India."}], "References": [{"Title": "Nature-Inspired Optimization Algorithms and Their Application in Multi-Thresholding Image Segmentation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "3", "Page": "855", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Equilibrium optimizer: A novel optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105190", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel equilibrium optimization algorithm for multi-thresholding image segmentation problems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "17", "Page": "10685", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Archimedes optimization algorithm: a new metaheuristic algorithm for solving optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1531", "JournalTitle": "Applied Intelligence"}, {"Title": "Cauchy with whale optimizer based eagle strategy for multi-level color hematology image segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "11", "Page": "5917", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An Overview on Nature-Inspired Optimization Algorithms and Their Possible Application in Image Processing Domain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "4", "Page": "614", "JournalTitle": "Pattern Recognition and Image Analysis"}, {"Title": "Randomly Attracted Rough Firefly Algorithm for histogram based fuzzy image clustering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106814", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Opposition-based Laplacian Equilibrium Optimizer with application in Image Segmentation using Multilevel Thresholding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114766", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Flow Direction Algorithm (FDA): A Novel Optimization Approach for Solving Optimization Problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "156", "Issue": "", "Page": "107224", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An efficient multilevel color image thresholding based on modified whale optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "115003", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An enhanced Archimedes optimization algorithm based on Local escaping operator and Orthogonal learning for PEM fuel cell parameter identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "104309", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An Analytical Review on Rough Set Based Image Clustering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "3", "Page": "1643", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "A comparison of novel metaheuristic algorithms on color aerial image multilevel thresholding", "Authors": "<PERSON><PERSON><PERSON> Kurban; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "104410", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Design of Optimal Controllers for Automatic Voltage Regulation Using Archimedes Optimizer", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "2", "Page": "799", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Cuckoo search with differential evolution mutation and Masi entropy for multi-level image segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "3", "Page": "4073", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Whale Optimizer-Based Clustering for Breast Histopathology Image Segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Swarm Intelligence Research"}, {"Title": "Node Localization Algorithm Based on Modified Archimedes Optimization Algorithm in Wireless Sensor Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Journal of Sensors"}]}, {"ArticleId": 105707340, "Title": "Mobile Big Data Analytics for Human Behavior Recognition in Wireless Sensor Network Based on Transfer Learning", "Abstract": "<p>Big data analysis of human behavior can provide the basis and support for the application of various scenarios. Using sensors for human behavior analysis is an effective means of identification method, which is very valuable for research. To address the problems of low recognition accuracy, low recognition efficiency of traditional human behavior recognition (HBR) algorithms in complex scenes, in this paper, we propose an HBR algorithm for Mobile Big data analytics in wireless sensor network using improved transfer learning. First, different wireless sensors are fused to obtain human behavior mobile big data, and then by analyzing the importance of human behavior features (HBF), the dynamic change parameters of HBF extraction threshold are calculated. Second, combined with the dynamic change parameters of threshold, the HBF of complex scenes are extracted. Finally, the best classification function of human behavior in complex scenes is obtained by using the classification function of HBF in complex scenes. Human behavior in complex scenes is classified according to the HBF in the feature set. The HBR algorithm is designed by using the improved transfer learning network to realize the recognition of human behavior in complex scenes. The results show that the proposed algorithm can accurately recognize up to 22 HBF points, and can control the HBR time within 2 s. The human behavior false recognition rate of miscellaneous scenes is less than 10%. The recognition speed is above 10/s, and the recall rate can reach more than 98%, which improves the HBR ability of complex scenes.</p>", "Keywords": "Human behavior recognition; big data analytics; improved transfer learning; behavior classification; complex scenes; wireless sensor network", "DOI": "10.1142/S0219265922420038", "PubYear": 2024, "Volume": "24", "Issue": "Supp01", "JournalId": 20646, "JournalTitle": "Journal of Interconnection Networks", "ISSN": "0219-2659", "EISSN": "1793-6713", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sports Science College, Harbin Normal, University, Harbin 150025, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Physical Education and Training, Harbin Sport University, Harbin 150008, P. R. China"}], "References": [{"Title": "Improved 1D-CNNs for behavior recognition using wearable sensor network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "165", "JournalTitle": "Computer Communications"}, {"Title": "Human Behavior Recognition from Multiview Videos", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "275", "JournalTitle": "Information Sciences"}, {"Title": "Wearable Sensor Data Based Human Behavior Recognition: a Method of Data Feature Extraction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "8", "Page": "1246", "JournalTitle": "Journal of Computer-Aided Design & Computer Graphics"}]}, {"ArticleId": 105707378, "Title": "Optimizing Energy-Latency Tradeoff for Computation Offloading in SDIN-Enabled MEC-based IIoT", "Abstract": "With the aim of tackling the contradiction between computation intensive industrial applications and resource-weak Edge Devices (EDs) in Industrial Internet of Things (IIoT), a novel computation task offloading scheme in SDIN-enabled MEC based IIoT is proposed in this paper. With the aim of reducing the task accomplished latency and energy consumption of EDs, a joint optimization method is proposed for optimizing the local CPU-cycle frequency, offloading decision, and wireless and computation resources allocation jointly. Based on the optimization, the task offloading problem is formulated into a Mixed Integer Nonlinear Programming (MINLP) problem which is a large-scale NP-hard problem. In order to solve this problem in an accessible time complexity, a sub-optimal algorithm GPCOA, which is based on hybrid evolutionary computation, is proposed. Outcomes of emulation revel that the proposed method outperforms other baseline methods, and the optimization result shows that the latency-related weight is efficient for reducing the task execution delay and improving the energy efficiency. Copyright © 2022 KSII.", "Keywords": "Computation offloading; Edge computing; Evolution Computation; SDIN", "DOI": "10.3837/tiis.2022.12.017", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105707381, "Title": "Breast Cancer Detection Using Breastnet-18 Augmentation with Fine Tuned Vgg-16", "Abstract": "Women from middle age to old age are mostly screened positive for Breast cancer which leads to death. Times over the past decades, the overall survival rate in breast cancer has improved due to advancements in early-stage diagnosis and tailored therapy. Today all hospital brings high awareness and early detection technologies for breast cancer. This increases the survival rate of women. Though traditional breast cancer treatment takes so long, early cancer techniques require an automation system. This research provides a new methodology for classifying breast cancer using ultrasound pictures that use deep learning and the combination of the best characteristics. Initially, after successful learning of Convolutional Neural Network (CNN) algorithms, data augmentation is used to enhance the representation of the feature dataset. Then it uses BreastNet18 with fine-tuned VGG-16 model for pre-training the augmented dataset. For feature classification, Entropy controlled Whale Optimization Algorithm (EWOA) is used. The features that have been optimized using the EWOA were utilized to fuse and optimize the data. To identify the breast cancer pictures, training classifiers are used. By using the novel probability-based serial technique, the best-chosen characteristics are fused and categorized by machine learning techniques. The main objective behind the research is to increase tumor prediction accuracy for saving human life. The testing was performed using a dataset of enhanced Breast Ultrasound Images (BUSI). The proposed method improves the accuracy compared with the existing methods.", "Keywords": "breast cancer; classification; classification; data augmentation; Deep learning; feature extraction; optimization; the fusion of features", "DOI": "10.32604/iasc.2023.033800", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Kathir College of Engineering, Coimbatore, 641062, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Bannari Amman Institute of Technology, Tamilnadu, Sathyamangalam, 638401, India"}, {"AuthorId": 3, "Name": "Mofreh A. Hogo", "Affiliation": "Electrical Engineering Department, Faculty of Engineering, Benha University, Benha, 13518, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computers and Information Technology, Taif University, P.O. Box 11099, Taif, 21944, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, College of Computers and Information Technology, Taif University, P.O. Box 11099, Taif, 21944, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Mansoura University, Mansoura, 35516, Egypt; Department of Computational Mathematics, Science, and Engineering (CMSE), Michigan State University, East Lansing, MI  48824, United States"}], "References": [{"Title": "A smooth proximity measure for optimality in multi-objective optimization using <PERSON>s method", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "117", "Issue": "", "Page": "104900", "JournalTitle": "Computers & Operations Research"}, {"Title": "Deep learning for real-time semantic segmentation: Application in ultrasound imaging", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "144", "Issue": "", "Page": "27", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Efficient MCDM Model for Evaluating the Performance of Commercial Banks: A Case Study", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "3", "Page": "2729", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Breast ultrasound tumour classification: A Machine Learning—Radiomics based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "7", "Page": "e12713", "JournalTitle": "Expert Systems"}, {"Title": "IOT-based service migration for connected communities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "96", "Issue": "", "Page": "107530", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Deformation Expression of Soft Tissue Based on BP Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "2", "Page": "1041", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 105707387, "Title": "Interpretive Structural Modeling Based Assessment and Optimization of Cloud with Internet of Things (CloudIoT) Issues Through Effective Scheduling", "Abstract": "Integrated CloudIoT is an emerging field of study that integrates the Cloud and the Internet of Things (IoT) to make machines smarter and deal with real-world objects in a distributed manner. It collects data from various devices and analyses it to increase efficiency and productivity. Because Cloud and IoT are complementary technologies with distinct areas of application, integrating them is difficult. This paper identifies various CloudIoT issues and analyzes them to make a relational model. The Interpretive Structural Modeling (ISM) approach establishes the interrelationship among the problems identified. The issues are categorised based on driving and dependent power, and a hierarchical model is presented. The ISM analysis shows that scheduling is an important aspect and has both (driving and dependence) power to improve the performance of the CloudIoT model. Therefore, existing CloudIoT job scheduling algorithms are analysed, and a cloud-centric scheduling mechanism is proposed to execute IoT jobs on a suitable cloud. The cloud implementation using an open-source framework to simulate Cloud Computing (CloudSim), based on the job’s workload, is presented. Simulation results of the proposed scheduling model indicate better performance in terms of Average Waiting Time (AWT) and makespan than existing cloud-based scheduling approaches.", "Keywords": "cloud-computing; CloudIoT; IoT; scheduling; workload", "DOI": "10.32604/iasc.2023.031931", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "VIT Bhopal University, Bhopal, 466114, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information, Taibah University, Medina, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON>, Lucknow, 226025, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information, Taibah University, Medina, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information, Taibah University, Medina, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information, Taibah University, Medina, Saudi Arabia"}], "References": [{"Title": "Fault Tolerance Based Load Balancing Approach for Web Resources in Cloud Environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "2", "Page": "225", "JournalTitle": "The International Arab Journal of Information Technology"}, {"Title": "Packet Optimization of Software Defined Network Using Lion Optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "2", "Page": "2617", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 105707388, "Title": "A Joint Optimization Algorithm for Renewable Energy System", "Abstract": "Energy sustainability is a hot topic in both scientific and political circles. To date, two alternative approaches to this issue are being taken. Some people believe that increasing power consumption is necessary for countries’ economic and social progress, while others are more concerned with maintaining carbon consumption under set limitations. To establish a secure, sustainable, and economical energy system while mitigating the consequences of climate change, most governments are currently pushing renewable growth policies. Energy markets are meant to provide consumers with dependable electricity at the lowest possible cost. A profit-maximization optimal decision model is created in the electric power market with the combined wind, solar units, loads, and energy storage systems, based on the bidding mechanism in the electricity market and operational principles. This model utterly considers the technological limits of new energy units and storages, as well as the involvement of new energy and electric vehicles in market bidding through power generation strategy and the output arrangement of the virtual power plant’s coordinated operation. The accuracy and validity of the optimal decision-making model of combined wind, solar units, loads, and energy storage systems are validated using numerical examples. Under multi-operating scenarios, the effects of renewable energy output changes on joint system bidding techniques are compared.", "Keywords": "decision-making; electricity market; optimization algorithm; Renewable energy", "DOI": "10.32604/iasc.2023.034106", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "MIMO-Terahertz in 6G Nano-Communications: Channel Modeling and Analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "66", "Issue": "1", "Page": "263", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 105707415, "Title": "Student Group Division Algorithm based on Multi-view Attribute Heterogeneous Information Network", "Abstract": "The student group division is benefit for universities to do the student management based on the group profile. With the widespread use of student smart cards on campus, especially where students living in campus residence halls, students' daily activities on campus are recorded with information such as smart card swiping time and location. Therefore, it is feasible to depict the students with the daily activity data and accordingly group students based on objective measuring from their campus behavior with some regular student attributions collected in the management system. However, it is challenge in feature representation due to diverse forms of the student data. To effectively and comprehensively represent students' behaviors for further student group division, we proposed to adopt activity data from student smart cards and student attributes as input data with taking account of activity and attribution relationship types from different perspective. Specially, we propose a novel student group division method based on a multi-view student attribute heterogeneous information network (MSA-HIN). The network nodes in our proposed MSA-HIN represent students with their multi-dimensional attribute information. Meanwhile, the edges are constructed to characterize student different relationships, such as co-major, co-occurrence, and co-borrowing books. Based on the MSA-HIN, embedded representations of students are learned and a deep graph cluster algorithm is applied to divide students into groups. Comparative experiments have been done on a real-life campus dataset collected from a university. The experimental results demonstrate that our method can effectively reveal the variability of student attributes and relationships and accordingly achieves the best clustering results for group division. © 2022 Korean Society for Internet Information. All rights reserved.", "Keywords": "graph deep clustering; heterogeneous information networks; representation learning; student behavior modeling", "DOI": "10.3837/tiis.2022.12.003", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105707507, "Title": "Factors that Influence the Adoption of Mobile Government (M-gov): A Proposal of A Unified Model", "Abstract": "Many m-gov adoption models have been proposed, which can confound researchers and policymakers. In this article, we reviewed 17 studies on m-gov adoption, identifying 25 different factors. We conducted two focus groups to discuss the adequacy of these factors, generating the first version of a unified model. We tested this model using data from 806 survey respondents from Brazil. The proposed unified model is parsimonious and outperforms other theoretical models.", "Keywords": "Mobile government ; m-gov ; technology adoption ; unified model", "DOI": "10.1080/10580530.2021.1987594", "PubYear": 2023, "Volume": "40", "Issue": "1", "JournalId": 21771, "JournalTitle": "Information Systems Management", "ISSN": "1058-0530", "EISSN": "1934-8703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department: Área das Ciências Naturais, da Computação e das Engenharia, Unidavi (Centro Universitário Para O Desenvolvimento Do Alto Vale Do Itajaí), Rio do Sul (SC), Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department: Bussiness School, Unisinos (Universidade Do Vale Do Rio Dos Sinos) University, Porto Alegre (RS), Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department: Bussiness School, Unisinos (Universidade Do Vale Do Rio Dos Sinos) University, Porto Alegre (RS), Brazil"}], "References": [{"Title": "Mobile Government Adoption Model Based on Combining GAM and UTAUT to Explain Factors According to Adoption of Mobile Government Services", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "3", "Page": "199", "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)"}, {"Title": "The Effect of Gender, Age, and Education on the Adoption of Mobile Government Services: ", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "3", "Page": "35", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "M-government continuance intentions: an instrument development and validation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "1", "Page": "189", "JournalTitle": "Information Technology for Development"}]}, {"ArticleId": 105707518, "Title": "La música en la última etapa del radioteatro El Siniestro Doctor <PERSON> (1960-1980)", "Abstract": "<p>Este estudio musicológico aborda la musicalización en el radioteatro chileno El siniestro Dr. <PERSON>rt<PERSON> de Juan Marino en su última etapa (1960-1980). Los objetivos fueron caracterizar el género de “música de terror”, esclarecer el proceso de musicalización del radioteatro El siniestro Dr. Mortis en el período mencionado, identificar título y procedencia de los trozos musicales usados en cada episodio, y proponer tanto hipótesis hermenéuticas como proyecciones, a partir de la información encontrada. El marco teórico incorporó varios autores, especialmente K. J. <PERSON> y su caracterización del género de música de terror en el cine, y la metodología incluyó entrevista, indagación bibliográfica y escucha de un amplio repertorio musical asociado al Doctor Mortis. Se descubrió una modalidad de trabajo peculiar en la edición de este radioteatro y el uso de piezas de música de concierto, de cine y de librerías musicales. </p>", "Keywords": "radioteatro; terror; El Siniestro Doctor <PERSON>; <PERSON>; musicalización de radioteatro.", "DOI": "10.5354/0719-1529.2022.66864", "PubYear": 2022, "Volume": "31", "Issue": "46", "JournalId": 48966, "JournalTitle": "Comunicación y Medios", "ISSN": "0716-3991", "EISSN": "0716-3991", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad de Chile, Chile"}], "References": []}, {"ArticleId": 105707547, "Title": "Improved Maximum Access Delay Time, Noise Variance, and Power Delay Profile Estimations for OFDM Systems", "Abstract": "In this paper, we propose improved maximum access delay time, noise variance, and power delay profile (PDP) estimation schemes for orthogonal frequency division multiplexing (OFDM) system in multipath fading channels. To this end, we adopt the approximate maximum likelihood (ML) estimation strategy. For the first step, the log-likelihood function (LLF) of the received OFDM symbols is derived by utilizing only the cyclic redundancy induced by cyclic prefix (CP) without additional information. Then, the set of the initial path powers is sub-optimally obtained to maximize the derived LLF. In the second step, we can select a subset of the initial path power set, i.e. the maximum access delay time, so as to maximize the modified LLF. Through numerical simulations, the benefit of the proposed method is verified by comparison with the existing methods in terms of normalized mean square error, erroneous detection, and good detection probabilities. Copyright © 2022 KSII.", "Keywords": "maximum access delay time; ML; multipath fading channel; OFDM; PDP", "DOI": "10.3837/tiis.2022.12.018", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105707667, "Title": "Optimized fuzzy clustering‐based k‐nearest neighbors imputation for mixed missing data in software development effort estimation", "Abstract": "Context Software development effort estimation (SDEE) is one of the most challenging aspects in project management. The presence of missing data (MD) in software attributes makes SDEE even more complex. K‐nearest neighbors imputation (KNNI) has been widely used in SDEE to deal with the MD issue. However, KNNI, in its classical process, has low tolerance to imprecision and uncertainty especially when dealing with categorical features. When dealing with categorical attributes, KNNI uses a classical approach, employing mainly numbers or classical intervals to represent software attributes and similarity measures originally designed for numerical attributes. Objectives This paper evaluates the use of an optimized fuzzy clustering‐based KNNI (FC‐KNNI) and compares it with classical KNN when dealing with mixed data in the context of SDEE. Methods We investigate the effect of two imputation techniques (FC‐KNNI and KNNI) on five SDEE techniques: case‐based reasoning, fuzzy case‐based reasoning, support vector regression, multilayer perceptron, and reduced‐error pruning tree. The evaluation is carried out using six publicly available datasets for SDEE using two performance measures, standardized accuracy (SA), and Pred (0.25). The Wilcoxon statistical test is also performed to assess the significance of results. Results The results are promising in the sense that using an imputation technique designed for mixed data is better than reusing methods originally designed for numerical data. We found that FC‐KNNI significantly outperforms KNNI regardless of the SDEE technique and dataset used. Another important finding is that F‐CBR improved the analogy process compared to CBR. Conclusion The introduction of fuzzy sets and fuzzy clustering in the analogy process improves its performances in terms of SA and Pred (0.25).", "Keywords": "fuzzy logic;imputation;missing data;software development effort estimation", "DOI": "10.1002/smr.2529", "PubYear": 2024, "Volume": "36", "Issue": "4", "JournalId": 2327, "JournalTitle": "Journal of Software: Evolution and Process", "ISSN": "2047-7473", "EISSN": "2047-7481", "Authors": [{"AuthorId": 1, "Name": "<PERSON>bt<PERSON><PERSON>", "Affiliation": "Software Project Management Research Team ENSIAS, Mohammed V University  Rabat Morocco"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Software Project Management Research Team ENSIAS, Mohammed V University  Rabat Morocco;Mohammed VI Polytechnic University  Ben Guerir Morocco"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dept. of Software Engineering and Information Technology, ETS University of Quebec  Montreal Québec Canada"}], "References": [{"Title": "A new fast search algorithm for exact k-nearest neighbors based on optimal triangle-inequality-based check strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "189", "Issue": "", "Page": "105088", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Fuzzy case‐based‐reasoning‐based imputation for incomplete data in software engineering repositories", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "", "JournalTitle": "Journal of Software: Evolution and Process"}]}, {"ArticleId": 105707681, "Title": "Online or offline? Spillover effect of customer-to-customer interaction in a multichannel background", "Abstract": "Purpose Enterprises' multichannel operations provide various avenues for customer interaction; however, existing literature investigating customer-to-customer interaction (CCI) mainly focuses on a single channel. The purpose of this paper is to investigate the spillover effect of CCI and potential underlying mediating mechanisms in different information channels. Design/methodology/approach Three between-subjects experiments with 946 participants were employed to empirically validate the proposed hypotheses in the context of an experiential product and a material product. Findings Results suggest the clear spillover effect of CCI, indicating that positive CCI improves focal customers' satisfaction and purchase intention, whereas negative CCI reduces focal customers' satisfaction and purchase intention. Moreover, CCI's spillover effect varies based on the CCI channel. Offline CCI has a stronger positive spillover effect than online CCI. Contrarily, online CCI has a stronger negative spillover effect than offline CCI. Customer experience and trust are demonstrated to have mediating roles in this process. Originality/value This study is the first to comprehensively understand and compare the CCI spillover effect of the two information channels. The findings add to the existing knowledge of information processing in the psychological mechanisms influencing the belief in addition to providing insights for companies engaged in multichannel operations management across different channels.", "Keywords": "Customer-to-customer interaction (CCI);Spillover effect;Interaction valence;Interaction channel;Multichannel", "DOI": "10.1108/INTR-11-2021-0855", "PubYear": 2023, "Volume": "33", "Issue": "4", "JournalId": 20482, "JournalTitle": "Internet Research", "ISSN": "1066-2243", "EISSN": "2054-5657", "Authors": [{"AuthorId": 1, "Name": "Yucheng Liu", "Affiliation": "School of Data Science, University of Science and Technology of China , Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business Administration, Southwestern University of Finance and Economics , Chengdu, China"}, {"AuthorId": 3, "Name": "Xiangming Ren", "Affiliation": "School of Business Administration, Southwestern University of Finance and Economics , Chengdu, China"}], "References": []}, {"ArticleId": 105707693, "Title": "Promoting preservice teachers’ psychological and pedagogical competencies for online learning and teaching: The T.E.A.C.H. program", "Abstract": "During the widespread COVID-19 pandemic, face-to-face teaching was not viable because many schools were forced to close as a preventive measure. Educators abruptly shifted to online classes without sufficient time and resources to prepare for such an enormous transition. Although shifting from traditional face-to-face format to modern e-learning approach ensured that students could be educated outside of the classroom, its impact on the quality of learning and teaching (L&amp;T) can be mixed. This study aims to address the knowledge gap in conventional teacher training by developing a web-based program called T.E.A.C.H. to enhance preservice teachers&#x27; psychological and pedagogical competencies for conducting online L&amp;T. The program consisted of five modules, each focused on one dimension of psychological competence (creativity, curiosity, love of learning, judgment, and perspective) and applied to the ‘three foci’ for online L&amp;T (attendance and participation, engagement, and assessment). Adopting a quasi-experimental design with matched sampling, a total of 314 preservice teachers were allocated into the intervention or control group. The intervention group was given access to the web-based program to receive training materials, learn about the content, and take part in the online L&amp;T exercises. Program effectiveness was evaluated using pretest and posttest questionnaires, a teaching design task, short quizzes, and a program quality assessment. The results showed that the T.E.A.C.H. program was effective in promoting preservice teachers&#x27; psychological competencies, positive attitudes toward online L&amp;T, self-efficacy to teach in an online format, intentions to use technology for L&amp;T, and online pedagogical skills. The successful implementation of T.E.A.C.H. encourages school leaders, teachers, and teacher professional development providers to utilize this web-based program to enhance online teaching practices.", "Keywords": "Psychological competencies ; Online learning and teaching ; Preservice teachers ; Training program ; Teacher professional development", "DOI": "10.1016/j.compedu.2023.104725", "PubYear": 2023, "Volume": "195", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Psychology, The Education University of Hong Kong, Hong Kong;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychology, The Education University of Hong Kong, Hong Kong"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychology, The Education University of Hong Kong, Hong Kong"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychology, The Education University of Hong Kong, Hong Kong"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Psychology, The Education University of Hong Kong, Hong Kong"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Psychology, The Education University of Hong Kong, Hong Kong"}], "References": [{"Title": "Successful design and delivery of online professional development for teachers: A systematic review of the literature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "104158", "JournalTitle": "Computers & Education"}, {"Title": "Transition to online learning during the COVID-19 pandemic", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "100130", "JournalTitle": "Computers in Human Behavior Reports"}]}, {"ArticleId": 105707699, "Title": "Implications of semi-supervised learning for design pattern selection", "Abstract": "<p>The significant impact of software design patterns on software design quality has led to conducting more research in this field. A design pattern is a proven solution based on software developers’ experience to solve recurring problems, which is used to acquire quality software design. However, due to a large number of design patterns, selecting an appropriate one is quite difficult. To tackle this issue, researchers have proposed different methods to automatically suggest a suitable design pattern (DP) to the designer. Among the various proposed methods, the text classification–based approach has used supervised and unsupervised methods, which have certain issues such as the need for manual dataset labeling, the need for using separate classifiers for each design pattern class, and the multi-class problem. This study addresses the mentioned issues by providing a three-phase method for choosing the appropriate design pattern. The proposed method exploits the semi-supervised learning method. Subsequently, this study proposes an evaluation model using three widely used case studies and 109 real design problems to evaluate the effectiveness of the proposed method. The evaluation results indicate that the performance of the proposed method has improved compared to the supervised learning techniques of Naïve Bayes and KNearestNeighbor.</p>", "Keywords": "Software design patterns; Design problems; Semi-supervised learning; Text categorization; Design pattern selection", "DOI": "10.1007/s11219-022-09610-4", "PubYear": 2023, "Volume": "31", "Issue": "3", "JournalId": 27487, "JournalTitle": "Software Quality Journal", "ISSN": "0963-9314", "EISSN": "1573-1367", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Alzahra University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Alzahra University, Tehran, Iran"}], "References": [{"Title": "A survey on semi-supervised learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "2", "Page": "373", "JournalTitle": "Machine Learning"}]}, {"ArticleId": 105707717, "Title": "SIMULATING THE PROCESS OF PROVIDING ME<PERSON>CA<PERSON> SERVICES FOR A VIRTUAL PATIENT AT THE CLINIC APPOINTMENT WITH THE GENERAL PRACTITIONER IN MARS SIMULATION ENVIRONMENT", "Abstract": "", "Keywords": "", "DOI": "10.22250/18142400_2022_74_4_17", "PubYear": 2022, "Volume": "", "Issue": "4", "JournalId": 37421, "JournalTitle": "Informatika i sistemy upravleniya", "ISSN": "1814-2400", "EISSN": "1814-2419", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105707792, "Title": "Hashtivismo desinformativo: el negacionismo de la dictadura argentina en Instagram", "Abstract": "<p>Este artículo estudia el material engañoso de las publicaciones que acompañan al hashtivismo negacionista #nofueron30000 y #nofueron30mil. Se llevó a cabo un análisis de contenido complementado por un análisis cualitativo de un corpus compuesto por los posts desinformativos más populares (n=52) publicados en Instagram desde el 6/02/2020 hasta el 6/02/2022. Los resultados muestran que los contenidos visuales engañosos que difunden estos hashtags: 1) no necesariamente están en relación con la memoria histórica, 2) priorizan los videos y los memes para 3) ridiculizar a los opositores, pero con una alta carga de incivilidad que derivan, incluso, en discursos de odio. Además 4) usan la manipulación hiperpartidista como estrategia desinformativa y 5) realizan una apropiación de los hashtags de los movimientos contrarios para crear narrativas desinformativas.</p>", "Keywords": "Desinformación; Argentina; Hashtivismo negacionista; Dictadura; Instagram.", "DOI": "10.5354/0719-1529.2022.66765", "PubYear": 2022, "Volume": "31", "Issue": "46", "JournalId": 48966, "JournalTitle": "Comunicación y Medios", "ISSN": "0716-3991", "EISSN": "0716-3991", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CONICET/CITNoBA, Argentina"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CONICET/ CITNoBa/ Universidad Nacional Noroeste, Argentina"}], "References": []}, {"ArticleId": 105707819, "Title": "Assessing the Connections among Top Management Support, IT Assimilation, and the Business Value of IT: A Meta-Analysis", "Abstract": "Scholars and practitioners have long tried to understand the antecedents and consequences of information technology (IT) assimilation. Studies suggest that top management support is an important driver of IT assimilation; however, this broad takeaway provides little substantive guidance to researchers and practitioners. We also have a limited understanding of whether and when IT assimilation creates business value. We take stock of this literature with a meta-analysis. We found that top management support is positively related to IT assimilation, and assimilation is in turn positively related to the business value of IT. We also found that explicit support does not have any special effect on IT assimilation (compared to implicit support) and may not be related to business value at all. However, our results indicate that IT assimilation has a stronger effect on business value at the process level (versus firm level) and for enterprise IT innovations (versus function IT innovations). Finally, we found that support-assimilation and assimilation-value relationships are stronger in high (versus low) power distance cultures. Our collective findings can facilitate future research and help practitioners navigate IT assimilation initiatives. © 2023 by the Association for Information Systems.", "Keywords": "Assimilation; Innovation; IT Value; Meta-Analysis; Top Management Support", "DOI": "10.17705/1jais.00772", "PubYear": 2023, "Volume": "24", "Issue": "1", "JournalId": 24874, "JournalTitle": "Journal of the Association for Information Systems", "ISSN": "", "EISSN": "1536-9323", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Colorado State University, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Wright State University, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Virginia Military Institute, United States"}], "References": []}, {"ArticleId": 105707831, "Title": "THE EFFECTIVE UTILIZATION OF INTERNET <PERSON><PERSON><PERSON>DTH IN ORGANIZATIONAL DEMAND SERVICES AND APPLICATIONS", "Abstract": "This research evaluates the sustainable utilization of Internet bandwidth usage and proposes a framework for organizational Internet Bandwidth demand service. The study highlighted the lack of an understanding of how much internet bandwidth is required for business and individual use and to what extent individual users must be familiar with it. This is crucial because it will enable a smooth data transmission flow back and forward in an organizational network. That is why this study seeks to (1) measure the relationship between organizational learning and effective utilization of internet bandwidth; (2) assess the relationship between organizational learning and organizational performance, and (3) investigate the mediating impact of effective utilization of internet bandwidth in influencing the relationship between organizational learning and organizational performance. We adopted the quantitative research methodology, and a reasonable sample population (n=318) was selected from those Malaysian business firms that use Internet bandwidth. Structural equation modeling (SEM) was utilized to test the relationship among study variables. The finding indicates that; organizational learning is of critical importance in the effective utilization of Internet bandwidth. However, effective bandwidth utilization does not mediate the relationship between organizational learning and performance. This result is interpreted that learning how to use Internet bandwidth improves individual usage and better decision-making in choosing an organization's Internet bandwidth-demand services and applications. Still, it does not significantly increase operational and financial performance.", "Keywords": "", "DOI": "10.17576/apjitm-2022-1102-01", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 43050, "JournalTitle": "Asia-Pacific Journal of Information Technology and Multimedia", "ISSN": "", "EISSN": "2289-2192", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105707977, "Title": "Electrochemical machining of blisk channels via synchronous rotations of the workpiece and the radial feeding cathode employing various feed rates", "Abstract": "<p>Electrochemical machining (ECM) is one of main methods used to process blisks, and it involves two stages: channel machining and blade profile machining. Any uneven distribution of the allowance in channel machining will transfer to the subsequent blade profile machining. Therefore, improving the uniformity of allowance distribution during channel machining is crucial. Herein, a novel blisk channel ECM method is developed, in which the cathode rotates while feeding radially with various feed rates, while the workpiece also rotates slightly. The cathode rotation and radial feeding with various feed rates could adapt to the rotation from the tip to the root of a standard blade and the difference in width between adjacent standard blades at the tip and the root. The slight rotation of workpiece could fit the distorted shape of a standard blade along the radial direction in the axial section, thereby further reducing the difference in allowance for blisk channel ECM. This report establishes the mathematical relationships between the allowance difference based on the electrode rotational angles, and the feed rate of the cathode. These mathematical relationships revealed changes in the optimized rotational angles of the electrodes with the feed depth, which enabled the optimized cathode feed rate to be determined. Three-dimensional dynamic simulations of the blisk channel ECM process were conducted, and the allowance distributions of the predicted blisk channels from various methods were obtained; the results indicate that the machining accuracy was improved for the proposed method. The blisk channels were processed experimentally by several ECM methods for comparison. The proposed method led to allowance differences in the concave and convex parts as low as 0.60 and 0.53 mm, respectively, which corresponded to 34.8% and 55.5% reductions compared with the conventional ECM method. These demonstrated improvements help with subsequent blade profile machining, and prove that the proposed method is effective.</p>", "Keywords": "Electrochemical machining; Blisk channel; Synchronous rotation; Variable feed rate; Difference in allowance", "DOI": "10.1007/s00170-022-10782-3", "PubYear": 2023, "Volume": "125", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Xu", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}], "References": [{"Title": "Study of voltage regulation strategy in electrochemical machining of blisk channels using tube electrodes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "11-12", "Page": "3489", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 105708241, "Title": "STRUCTURAL REPRESENTATIONS OF THE DIAGNOSTIC OBJECT IN THE CONCEPTUAL MODEL", "Abstract": "", "Keywords": "", "DOI": "10.22250/18142400_2022_74_4_64", "PubYear": 2022, "Volume": "", "Issue": "4", "JournalId": 37421, "JournalTitle": "Informatika i sistemy upravleniya", "ISSN": "1814-2400", "EISSN": "1814-2419", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105708307, "Title": "A Gradient Boosted Decision Tree-Based Influencer Prediction in Social Network Analysis", "Abstract": "<p>Twitter, Instagram and Facebook are expanding rapidly, reporting on daily news, social activities and regional or international actual occurrences. Twitter and other platforms have gained popularity because they allow users to submit information, links, photos and videos with few restrictions on content. As a result of technology advances (“big” data) and an increasing trend toward institutionalizing ethics regulation, social network analysis (SNA) research is currently confronted with serious ethical challenges. A significant percentage of human interactions occur on social networks online. In this instance, content freshness is essential, as content popularity declines with time. Therefore, we investigate how influencer content (i.e., posts) generates interactions, as measured by the number of likes and reactions. The Gradient Boosted Decision Tree (GBDT) and the Chaotic Gradient-Based Optimizer are required for estimation (CGBO). Using earlier group interactions, we develop the Influencers Prediction issue in this study’s setting of SN-created groups. We also provide a GBDT-CGBO framework and an efficient method for identifying users with the ability to influence the future behaviour of others. Our contribution is based on logic, experimentation and analytic techniques. The goal of this paper is to find domain-based social influencers using a framework that uses semantic analysis and machine learning modules to measure and predict users’ credibility in different domains and at different times. To solve these problems, future research will have to focus on co-authorship networks and economic networks instead of online social networks. The results show that our GBDT-CGBO method is both useful and effective. Based on the test results, the GBDT-CGBO model can correctly classify unclear data, which speeds up processing and makes it more efficient.</p>", "Keywords": "", "DOI": "10.3390/bdcc7010006", "PubYear": 2023, "Volume": "7", "Issue": "1", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, R.M.K. Engineering College, Kavaraipettai 601206, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Hanyang University, Seoul 04763, Republic of Korea; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Vellore 632014, India"}, {"AuthorId": 4, "Name": "Malliga Subramanian", "Affiliation": "Department of Computer Science and Engineering, Kongu Engineering College, Perundurai 638060, India"}, {"AuthorId": 5, "Name": "<PERSON>elmurug<PERSON>", "Affiliation": "Department of Information Technology, R.M.D. Engineering College, Chennai 601206, India"}], "References": [{"Title": "A New Real-Time Link Prediction Method Based on User Community Changes in Online Social Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "3", "Page": "448", "JournalTitle": "The Computer Journal"}, {"Title": "Time-aware link prediction based on strengthened projection in bipartite networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "506", "Issue": "", "Page": "217", "JournalTitle": "Information Sciences"}, {"Title": "Gradient-based optimizer: A new metaheuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "540", "Issue": "", "Page": "131", "JournalTitle": "Information Sciences"}, {"Title": "Topic detection and sentiment analysis in Twitter content related to COVID-19 from Brazil and the USA", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107057", "JournalTitle": "Applied Soft Computing"}, {"Title": "Blockchain for Securing Healthcare Data Using Squirrel Search Optimization Algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "3", "Page": "1815", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "HHO-Based Vector Quantization Technique for Biomedical Image Compression in Cloud Computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Batholomew C<PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "3", "Page": "", "JournalTitle": "International Journal of Image and Graphics"}, {"Title": "Blockchain with deep learning-enabled secure healthcare data transmission and diagnostic model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "", "JournalTitle": "International Journal of Modeling, Simulation, and Scientific Computing"}, {"Title": "A novel convolutional neural network with gated recurrent unit for automated speech emotion recognition and classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "1", "Page": "54", "JournalTitle": "Journal of Control and Decision"}, {"Title": "Design of fuzzy logic based energy management and traffic predictive model for cyber physical systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "108135", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 105708355, "Title": "Snowmelt detection on the Antarctic ice sheet surface based on XPGR with improved ant colony algorithm", "Abstract": "This study presents an XPGR coupled with an improved ant colony algorithm to automatically detect the Antarctic ice sheet surface snowmelt and acquire high-precision snowmelt information. This approach first enlarges the difference between dry snow and wet snow using the XPGR algorithm and then utilizes an enhanced ant colony algorithm to adaptively find the best threshold for segmenting dry snow and wet snow. The dissimilarity matrix, which determines the initial clustering centre and utilizes Levy flight to modify the clustering radius dynamically, is a major advancement to the ant colony algorithm. The method proposed in this study was compared to the standard XPGR algorithm from October 2017 to February 2018 and from October 2019 to February 2020 to evaluate the practicality and rationale of the proposed method. The further verification of six automatic weather stations (AWS) shows that the method proposed in this study has higher accuracy.", "Keywords": "Antarctic ice sheet ; Ant colony algorithm ; Levy flight ; Dissimilarity matrix ; XPGR ; Snowmelt", "DOI": "10.1080/01431161.2022.2161851", "PubYear": 2023, "Volume": "44", "Issue": "1", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Wang", "Affiliation": "College of Information Science and Engineering, Henan University of Technology, Zhengzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Henan University of Technology, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Henan University of Technology, Zhengzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Wang", "Affiliation": "School of Geosciences and info-physics, Central South University, Changsha, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Henan University of Technology, Zhengzhou, China"}], "References": []}, {"ArticleId": 105708478, "Title": "Temporal Preferences-Based Utility Control for Smart Homes", "Abstract": "The residential sector contributes a large part of the energy to the global energy balance. To date, housing demand has mostly been uncontrollable and inelastic to grid conditions. Analyzing the performance of a home energy management system requires the creation of various profiles of real-world residential demand, as residential demand is complex and includes multiple factors such as occupancy, climate, user preferences, and appliance types. Average Peak Ratio (A2P) is one of the most important parameters when managing an efficient and cost-effective energy system. At the household level, the larger relative magnitudes of certain energy devices make managing this ratio critical, albeit difficult. Various Demand Response (DR) and Demand Side Management (DSM) systems have been proposed to reduce this ratio to 1. The main ways to achieve this are economic incentives, user comfort modeling and control, or preference-based. In this study, we propose a unique opportunistic social time approach called the Time Utility Based Control Feature (TUBCF), which uses the concept of a utility function from economics to model and control consumer devices. We propose a DR model for residential customers to reduce Peak-to-Average Ratio (PAR) and improve customer satisfaction by eliminating Appliance Wait Time (WTA) during peak periods. For PAR reduction and WTA, we propose a system architecture and mathematical formulation. Our proposed model automatically schedules devices based on their temporal preferences and considers six households with different device types and operational characteristics. Simulation results show that using this strategy can reduce A2P by 80% and improve user comfort during peak hours.", "Keywords": "demand shaping; fixed priority; Temporal preferences; utility control", "DOI": "10.32604/iasc.2023.034032", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, University of the Punjab, Gujranwala Campus52250, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Gift University, Punjab, Gujranwala, 52250, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of the Punjab, Jehlum Campus, Jehlum, 49600, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing, The Islamia University of Bahawalpur, Bahawalpur, 63100, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Industrial Engineering Department, College of Engineering, King Saud University, P.O. Box 800, Riyadh, 11421, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Industrial Engineering Department, College of Engineering, King Saud University, P.O. Box 800, Riyadh, 11421, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan, 38541, South Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan, 38541, South Korea"}], "References": []}, {"ArticleId": 105708484, "Title": "A Multi-Modal Deep Learning Approach for Emotion Recognition", "Abstract": "In recent years, research on facial expression recognition (FER) under mask is trending. Wearing a mask for protection from Covid 19 has become a compulsion and it hides the facial expressions that is why FER under the mask is a difficult task. The prevailing unimodal techniques for facial recognition are not up to the mark in terms of good results for the masked face, however, a multimodal technique can be employed to generate better results. We proposed a multimodal methodology based on deep learning for facial recognition under a masked face using facial and vocal expressions. The multimodal has been trained on a facial and vocal dataset. We have used two standard datasets, M-LFW for the masked dataset and CREMA-D and TESS dataset for vocal expressions. The vocal expressions are in the form of audio while the faces data is in image form that is why the data is heterogenous. In order to make the data homogeneous, the voice data is converted into images by taking spectrogram. A spectrogram embeds important features of the voice and it converts the audio format into the images. Later, the dataset is passed to the multimodal for training. neural network and the experimental results demonstrate that the proposed multimodal algorithm outsets unimodal methods and other state-of-the-art deep neural network models.", "Keywords": "covid-19; Deep learning; facial expression recognition; multi-model neural network; spectrogram; speech emotion recognition", "DOI": "10.32604/iasc.2023.032525", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "The Superior University, Lahore, Pakistan; Intelligent Data Visual Computing Research (IDVCR), Lahore, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Superior University, Lahore, Pakistan; Intelligent Data Visual Computing Research (IDVCR), Lahore, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Superior University, Lahore, Pakistan; Intelligent Data Visual Computing Research (IDVCR), Lahore, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National University of Technology, Islamabad, Pakistan"}], "References": [{"Title": "Raspberry Pi assisted face recognition framework for enhanced law-enforcement services in smart cities", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "995", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Multi-modal facial expression feature based on deep-neural networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "1", "Page": "17", "JournalTitle": "Journal on Multimodal User Interfaces"}, {"Title": "CNN-based Broad Learning with Efficient Incremental Reconstruction Model for Facial Emotion Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "10236", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Deep Learning: A Comprehensive Overview on Techniques, Taxonomy, Applications and Research Directions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "6", "Page": "420", "JournalTitle": "SN Computer Science"}, {"Title": "A Multi-Feature Learning Model with Enhanced Local Attention for Vehicle Re-Identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "3549", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Heart Sound Analysis for Abnormality Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "2", "Page": "1195", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Lexicalized Dependency Paths Based Supervised Learning for Relation Extraction", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "3", "Page": "861", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 105708538, "Title": "An Efficient COVID-19 Disease Outbreak Prediction Using BI-SSOA-TMLPNN and ARIMA", "Abstract": "<p>Globally, people’s health and wealth are affected by the outbreak of the corona virus. It is a virus, which infects from common fever to severe acute respiratory syndrome. It has the potency to transmit from one person to another. It is established that this virus spread is augmenting speedily devoid of any symptoms. Therefore, the prediction of this outbreak situation with mathematical modelling is highly significant along with necessary. To produce informed decisions along with to adopt pertinent control measures, a number of outbreak prediction methodologies for COVID-19 are being utilized by officials worldwide. An effectual COVID-19 outbreaks’ prediction by employing Squirrel Search Optimization Algorithm centric Tanh Multi-Layer Perceptron Neural Network (MLPNN) (SSOA-TMLPNN) along with Auto-Regressive Integrated Moving Average (ARIMA) methodologies is proposed here. Initially, from the openly accessible sources, the input time series COVID-19 data are amassed. Then, pre-processing is performed for better classification outcomes after collecting the data. Next, by utilizing Sine-centered Empirical Mode Decomposition (S-EMD) methodology, the data decomposition is executed. Subsequently, the data are input to the Brownian motion Intense (BI) - SSOA-TMLPNN classifier. In this, the diseased, recovered, and death cases in the country are classified. After that, regarding the time-series data, the corona-virus’s future outbreak is predicted by employing ARIMA. Afterwards, data visualization is conducted. Lastly, to evaluate the proposed model’s efficacy, its outcomes are analogized with certain prevailing methodologies. The obtained outcomes revealed that the proposed methodology surpassed the other existing methodologies.</p>", "Keywords": "", "DOI": "10.1142/S0219467823400119", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 14884, "JournalTitle": "International Journal of Image and Graphics", "ISSN": "0219-4678", "EISSN": "1793-6756", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Government Science College, Nrupathunga University, Bengaluru, Karnataka 560001, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Sheela", "Affiliation": "Department of Information Technology, Pentecost University, Sowutoum, Ghana"}], "References": [{"Title": "Predicting the growth and trend of COVID-19 pandemic using machine learning and cloud computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100222", "JournalTitle": "Internet of Things"}, {"Title": "A Methodological Approach for Predicting COVID-19 Epidemic Using EEMD-ANN Hybrid Model", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100228", "JournalTitle": "Internet of Things"}, {"Title": "Robust and optimal predictive control of the COVID-19 outbreak", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "", "Page": "525", "JournalTitle": "Annual Reviews in Control"}, {"Title": "A new SEAIRD pandemic prediction model with clinical and epidemiological data analysis on COVID-19 outbreak", "Authors": "Xian<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "7", "Page": "4162", "JournalTitle": "Applied Intelligence"}, {"Title": "Modelling and Simulation of COVID-19 Outbreak Prediction Using Supervised Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>;  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "3", "Page": "2397", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Predicting the COVID-19 infection with fourteen clinical features using machine learning classification algorithms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "8", "Page": "11943", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 105708563, "Title": "Federated Blockchain Model for Cyber Intrusion Analysis in Smart Grid Networks", "Abstract": "Smart internet of things (IoT) devices are used to manage domestic and industrial energy needs using sustainable and renewable energy sources. Due to cyber infiltration and a lack of transparency, the traditional transaction process is inefficient, unsafe and expensive. Smart grid systems are now efficient, safe and transparent owing to the development of blockchain (BC) technology and its smart contract (SC) solution. In this study, federated learning extreme gradient boosting (FL-XGB) framework has been developed along with BC to learn the intrusion inside the smart energy system. FL is best suited for a decentralized BC-enabled system to adapt learning models for trustworthy and reliable transactions. Many features and attributes of the Third International Knowledge Discovery and Data mining Tools Competition (KDD Cup 1999) dataset have been used in this study to perform experimental analysis. The likelihood of intrusions in the network is mathematically stated. The participant nodes run the BC based FL-Smart Contract (SC) algorithms to detect network intrusions. FL provided aggregated learning results from the experiment that was 99% accurate in predicting network intrusion. The experimentally determined block storage gain and retrieval gain were 97.5% and 95.4% respectively. The intrusion in the smart grid network was evaluated, and the data indicated that there was 1.2% illegal access. Moreover, the learning system’s accuracy, retrieval and storage intrusions, legal access and transaction processing times were considered for comparison. The proposed system outperformed contemporary research-developed systems targeted for the same application. Therefore, this study provides a guaranteed intrusion learning system and secure transaction system for smart grids.", "Keywords": "Blockchain; federated learning system; internet of things; intrusion detection; smart grids", "DOI": "10.32604/iasc.2023.034381", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Sri Si<PERSON> Nadar College of Engineering, Chennai, 603110, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Sri Si<PERSON> Nadar College of Engineering, Chennai, 603110, India"}], "References": [{"Title": "Blockchain for smart grid", "Authors": "Anak Agung Gde Agung; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "3", "Page": "666", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "An efficient XGBoost–DNN-based classification model for network intrusion detection system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12499", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Blockchain-based anomaly detection of electricity consumption in smart grids", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "476", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "From federated learning to federated neural architecture search: a survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "2", "Page": "639", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A survey of federated learning for edge computing: Research problems and solutions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100008", "JournalTitle": "High-Confidence Computing"}, {"Title": "Rapid Fault Analysis by Deep Learning-Based PMU for Smart Grid System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "2", "Page": "1581", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 105708564, "Title": "Smart Nutrient Deficiency Prediction System for Groundnut Leaf", "Abstract": "Prediction of the nutrient deficiency range and control of it through application of an appropriate amount of fertiliser at all growth stages is critical to achieving a qualitative and quantitative yield. Distributing fertiliser in optimum amounts will protect the environment’s condition and human health risks. Early identification also prevents the disease’s occurrence in groundnut crops. A convolutional neural network is a computer vision algorithm that can be replaced in the place of human experts and laboratory methods to predict groundnut crop nitrogen nutrient deficiency through image features. Since chlorophyll and nitrogen are proportionate to one another, the Smart Nutrient Deficiency Prediction System (SNDP) is proposed to detect and categorise the chlorophyll concentration range via which nitrogen concentration can be known. The model’s first part is to perform preprocessing using Groundnut Leaf Image Preprocessing (GLIP). Then, in the second part, feature extraction using a convolution process with Non-negative ReLU (CNNR) is done, and then, in the third part, the extracted features are flattened and given to the dense layer (DL) layer. Next, the Maximum Margin classifier (MMC) is deployed and takes the input from DL for the classification process to find CCR. The dataset used in this work has no visible symptoms of a deficiency with three categories: low level (LL), beginning stage of low level (BSLL), and appropriate level (AL). This model could help to predict nitrogen deficiency before perceivable symptoms. The performance of the implemented model is analysed and compared with ImageNet pre-trained models. The result shows that the CNNR-MMC model obtained the highest training and validation accuracy of 99% and 95%, respectively, compared to existing pre-trained models.", "Keywords": "chlorophyll; CNN; deep learning; groundnut crop; nitrogen deficiency", "DOI": "10.32604/iasc.2023.034280", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, School of Computing, SRM Institute of Science and Technology, Tamil Nadu, Chengalpattu, 603203, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, School of Computing, SRM Institute of Science and Technology, Tamil Nadu, Chengalpattu, 603203, India"}], "References": [{"Title": "Lexicalized Dependency Paths Based Supervised Learning for Relation Extraction", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "3", "Page": "861", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "An Intelligent Fine-Tuned Forecasting Technique for Covid-19 Prediction Using Neuralprophet Model", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "1", "Page": "629", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 105708567, "Title": "Blockchain and Data Integrity Authentication Technique for Secure Cloud Environment", "Abstract": "Nowadays, numerous applications are associated with cloud and user data gets collected globally and stored in cloud units. In addition to shared data storage, cloud computing technique offers multiple advantages for the user through different distribution designs like hybrid cloud, public cloud, community cloud and private cloud. Though cloud-based computing solutions are highly convenient to the users, it also brings a challenge i.e., security of the data shared. Hence, in current research paper, blockchain with data integrity authentication technique is developed for an efficient and secure operation with user authentication process. Blockchain technology is utilized in this study to enable efficient and secure operation which not only empowers cloud security but also avoids threats and attacks. Additionally, the data integrity authentication technique is also utilized to limit the unwanted access of data in cloud storage unit. The major objective of the projected technique is to empower data security and user authentication in cloud computing environment. To improve the proposed authentication process, cuckoo filter and Merkle Hash Tree (MHT) are utilized. The proposed methodology was validated using few performance metrics such as processing time, uploading time, downloading time, authentication time, consensus time, waiting time, initialization time, in addition to storage overhead. The proposed method was compared with conventional cloud security techniques and the outcomes establish the supremacy of the proposed method.", "Keywords": "authentication; Blockchain; cloud computing; data integrity; hash tree; security; signature", "DOI": "10.32604/iasc.2023.032942", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University College of Engineering, Panruti, 607106, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, University College of Engineering, BIT Campus, Anna University, Tiruchirapalli, 620025, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Technical Engineering, The Islamic University, Najaf, Iraq"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Technical Engineering, Al-Hadba University College, Mosul, Iraq"}], "References": [{"Title": "Blockchain-based cloudlet management for multimedia workflow in mobile cloud computing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "15-16", "Page": "9819", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "SBAC: A secure blockchain-based access control framework for information-centric networking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102444", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Blockchain data-based cloud data integrity protection mechanism", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "902", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Hybridization of firefly and Improved Multi-Objective Particle Swarm Optimization algorithm for energy efficient load balancing in Cloud Computing environments", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "36", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Collusion resistant secret sharing scheme for secure data storage and processing over cloud", "Authors": "<PERSON> V.S.; <PERSON><PERSON>; <PERSON><PERSON> P<PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "102869", "JournalTitle": "Journal of Information Security and Applications"}]}, {"ArticleId": 105708579, "Title": "Deep Learning-Based Sign Language Recognition for Hearing and Speaking Impaired People", "Abstract": "Sign language is mainly utilized in communication with people who have hearing disabilities. Sign language is used to communicate with people having developmental impairments who have some or no interaction skills. The interaction via Sign language becomes a fruitful means of communication for hearing and speech impaired persons. A Hand gesture recognition system finds helpful for deaf and dumb people by making use of human computer interface (HCI) and convolutional neural networks (CNN) for identifying the static indications of Indian Sign Language (ISL). This study introduces a shark smell optimization with deep learning based automated sign language recognition (SSODL-ASLR) model for hearing and speaking impaired people. The presented SSODL-ASLR technique majorly concentrates on the recognition and classification of sign language provided by deaf and dumb people. The presented SSODL-ASLR model encompasses a two stage process namely sign language detection and sign language classification. In the first stage, the Mask Region based Convolution Neural Network (Mask RCNN) model is exploited for sign language recognition. Secondly, SSO algorithm with soft margin support vector machine (SM-SVM) model can be utilized for sign language classification. To assure the enhanced classification performance of the SSODL-ASLR model, a brief set of simulations was carried out. The extensive results portrayed the supremacy of the SSODL-ASLR model over other techniques.", "Keywords": "deep learning; disabled people; mask rcnn model; shark smell optimization; Sign language recognition", "DOI": "10.32604/iasc.2023.033577", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, College of Computers and Information Technology, Taif University, P.O. Box 11099, Taif, 21944, Saudi Arabia"}], "References": [{"Title": "Benchmarking deep neural network approaches for Indian Sign Language recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "12", "Page": "6685", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A new deep-learning strawberry instance segmentation methodology based on a fully convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "22", "Page": "15059", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Intelligent Machine Learning Based EEG Signal Classification Model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "1", "Page": "1821", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 105708580, "Title": "Human Factors While Using Head-Up-Display in Low Visibility Flying Conditions", "Abstract": "Flying an aircraft in low visibility is still a challenging task for the pilot. It requires precise and accurate situational awareness (SA) in real-time. A Head-up Display (HUD) is used to project collimated internal and external flight information on a transparent screen in the pilot’s forward field of view, which eliminates the change of eye position between Head-Down-Display (HDD) instruments and outer view through the windshield. Implementation of HUD increases the SA and reduces the workload for the pilot. But to provide a better flying capability for the pilot, projecting extensive information on HUD causes human factor issues that reduce pilot performance and lead to accidents in low visibility conditions. The literature shows that human error is the leading cause of more than 70% of aviation accidents. In this study, the ability of the pilot able to read background and symbology information of HUD at a different level of background seen complexity, such as symbology brightness, transition time, amount of Symbology, size etc., in low visibility conditions is discussed. The result shows that increased complexity on the HUD causes more detection errors. © 2023, Tech Science Press. All rights reserved.", "Keywords": "avionics; background complexity; HUD; human factor; situational awareness", "DOI": "10.32604/iasc.2023.034203", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Academy of Scientific and Innovative Research (AcSIR), Ghaziabad, 201002, India; CSIR-Central Scientific Instruments Organisation160030, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Academy of Scientific and Innovative Research (AcSIR), Ghaziabad, 201002, India; CSIR-Central Scientific Instruments Organisation160030, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Academy of Scientific and Innovative Research (AcSIR), Ghaziabad, 201002, India; CSIR-Central Scientific Instruments Organisation160030, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Academy of Scientific and Innovative Research (AcSIR), Ghaziabad, 201002, India; CSIR-Central Scientific Instruments Organisation160030, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "CSIR-Central Scientific Instruments Organisation160030, India"}], "References": [{"Title": "The Effects of Visual Complexity and Decluttering Methods on Visual Search and Target Detection in Cockpit Displays", "Authors": "<PERSON> <PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "7", "Page": "588", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Effects of luminance contrast and font size on dual‐plane head‐up display legibility (“The Double 007 Rule for HUDs”)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "5", "Page": "328", "JournalTitle": "Journal of the Society for Information Display"}, {"Title": "Inattentional Blindness in Augmented Reality Head-Up Display-Assisted Driving", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "9", "Page": "837", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "In plane sight: Inattentional blindness affects visual detection of external targets in simulated flight", "Authors": "<PERSON> White; <PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "", "Page": "103578", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 105708581, "Title": "Hopping-Aware Cluster Header Capability for Sensor Relocation in Mobile IoT Networks", "Abstract": "Mobile sensor nodes such as hopping sensors are of critical importance in data collection. However, the occurrence of sensing holes is unavoidable due to the energy limitation of the nodes. Thus, it is evident that the relocation of mobile sensors is the most desirable method to recover the sensing holes. The previous research conducted by the authors so far demonstrated the most realistic hopping sensor relocation scheme, which is suitable for the distributed environment. In previous studies, the cluster header plays an essential role in detecting the sensing hole and requesting the neighboring cluster to recover the sensing hole that occurred in the sensor node. However, the limitations of the cluster header in the previously proposed relocation protocol are not fully considered. Because the cluster header jumps more frequently than non-header nodes, its energy consumption is relatively high compared to other nodes. Therefore, it is most likely to lead to header node failure and can lead to data loss on the network. In this paper, the jumping ability and energy consumption of the cluster header are seriously considered. Additional ability to replace cluster headers in case of failure is also implemented. Simulation results show that the data collection time can be further increased, which demonstrates the validity of the proposed algorithms.", "Keywords": "header node; Hopping sensor; mobile internet of things; numerical simulation; relocation protocol", "DOI": "10.32604/iasc.2023.033081", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of IT Convergence Software, Seoul Theological University, Bucheon, 14754, South Korea"}, {"AuthorId": 2, "Name": "Jaeyoung Park", "Affiliation": "Department of Computer Engineering, Hongik University, Seoul, 04066, South Korea"}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Gachon University, Seongnam, 13120, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Incheon National University, Incheon, 22012, South Korea"}], "References": [{"Title": "Success Rate Queue-based Relocation Algorithm of Sensory Network to Overcome Non-uniformly Distributed Obstacles", "Authors": "Sooyeon Park; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "2", "Page": "1181", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Novel Sensing Hole Recovery with Expanded Relay Node Capability", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "44", "Issue": "1", "Page": "663", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 105708752, "Title": "Digital twin-driven vibration amplitude simulation for condition monitoring of axial blowers in blast furnace ironmaking", "Abstract": "Long-term reliable condition monitoring (CM) of blast furnace blowers is essential to avoid catastrophic failure. Due to variable working conditions, the predefined thresholds in current CM systems influence the accuracy of the monitoring process and can lead to misdiagnoses. In order to overcome this limitation, we propose a digital twin (DT)-based scheme to monitor vibrations found in blowers. Factors believed to impact the distribution of vibration amplitudes are analysed using data collected from a constant speed axial blower operating in an industrial commercial environment and, based on which, a machine learning-based adaptive amplitude simulation model is developed on our on-site private cloud computing platform. Outcomes reveal that different guide vane openings in the manufacturing process can cause changes in amplitudes. By integrating the newly-arriving sensor data, vibration amplitudes can be more accurately predicted in the virtual space. The gap between the simulated and actual value narrowed from ±5 µm to within ±3 µm, from which a dynamic threshold can be defined. The resulting DT model, coupled with the on-site private cloud computing platform, which alleviates the shortage of computational and storage capacity in steel plants, allows for a much more effective CM system. GRAPHICAL", "Keywords": "Digital twin ; condition monitoring ; blast furnace ironmaking ; constant speed axial blower ; amplitude prediction", "DOI": "10.1080/********.2022.2152400", "PubYear": 2023, "Volume": "11", "Issue": "1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Management, Shanghai Lixin University of Accounting and Finance, Shanghai, China;Golden Data Ltd., London, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Golden Data Ltd., London, UK"}, {"AuthorId": 3, "Name": "Michael Castle", "Affiliation": "Golden Data Ltd., London, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Golden Data Ltd., London, UK"}], "References": [{"Title": "A novel geodesic flow kernel based domain adaptation approach for intelligent fault diagnosis under varying working condition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "376", "Issue": "", "Page": "54", "JournalTitle": "Neurocomputing"}, {"Title": "Compressor surge control using a new robust adaptive method in the presence of uncertainty and unmatched disturbance", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "1", "Page": "405", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "Cloud-edge orchestration-based bi-level autonomous process control for mass individualization of rapid printed circuit boards prototyping services", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "63", "Issue": "", "Page": "143", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Digital twins-based flexible operating of open architecture production line for individualized manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "101676", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 105708776, "Title": "Data privacy with heuristic anonymisation", "Abstract": "Abundance of data makes privacy more vulnerable than ever as it increases the attackers’ ability to infer confidential data from multiple data sources. Anonymisation protects data privacy by ensuring that critical data are non-unique to any individual so that we can conceal the individual’s identity. Existing techniques aim to minimally alter the original data so that either the anonymised data or its analytical results (e.g., classification) will not disclose certain privacy. Our research aims both. This paper presents HeuristicMin, an anonymisation approach that applies generalisations to satisfy user-specified anonymity requirements while maximising data retention (for analysis purposes). Unlike others, by exploiting monotonicity property of generalisation and simple heuristics for pruning, HeuristicMin provides an efficient exhaustive search for optimal generalised data. The paper articulates different meanings of optimality in anonymisation and compares HeuristicMin with well-known approaches analytically and empirically. HeuristicMin produces competitive results on the classification obtained from the anonymised data. Copyright © 2023 Inderscience Enterprises Ltd.", "Keywords": "anonymisation; bottom-up generalisation; BUG; data generalisation; privacy", "DOI": "10.1504/IJICS.2023.128004", "PubYear": 2023, "Volume": "20", "Issue": "1/2", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Texas Tech UniversityTX, United States"}, {"AuthorId": 2, "Name": "Rattikorn Hewett", "Affiliation": "Department of Computer Science, Texas Tech UniversityTX, United States"}], "References": []}, {"ArticleId": 105708878, "Title": "Digital common(s): the role of digital gamification in participatory design for the planning of high-density housing estates", "Abstract": "<p>“Digital Commons” explores the intersection between participatory design, digital gamification, and community engagement, contextualised in the planning of high-density housing estates in Hong Kong. The research project investigates how digital gamified participatory design can be applied in decision-making processes for the planning of public facilities in high-density housing estates. Focusing on community engagement methods, the project has engaged with residents of a case study housing estate, Jat Min Chuen in the Shatin Wai area of Hong Kong, to facilitate collective planning discussions about the past, present, and future of community facilities. Using a digital community game approach, it has collected opinions and needs from public housing residents, promoted collaborative design thinking processes, and provided a platform for participants to increase their understanding of the complexity of planning problems through 3D visualisation tools. The experiences documented in this study demonstrate how 3D interactivity, real-time engagement, and bottom-up perspectives may enhance the potential of using immersive digital twins during collective decision-making. The gaming outcomes show a high similarity across all teams in close relationship to users’ daily life routines, demonstrating a new powerful role for urban designers as a coordinator of interactive and collaborative planning processes.</p>", "Keywords": "Digital Commons; Participatory Design; Gamification; Community Engagement; High density housing", "DOI": "10.3389/frvir.2022.1062336", "PubYear": 2023, "Volume": "3", "Issue": "", "JournalId": 73463, "JournalTitle": "Frontiers in Virtual Reality", "ISSN": "", "EISSN": "2673-4192", "Authors": [{"AuthorId": 1, "Name": "Provides <PERSON>", "Affiliation": "School of Architecture, The Chinese University of Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Architecture, The Chinese University of Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Architecture, The Chinese University of Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Architecture, The Chinese University of Hong Kong, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Architecture, The Chinese University of Hong Kong, China"}], "References": [{"Title": "Distributed interaction design", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "2", "Page": "82", "JournalTitle": "interactions"}]}, {"ArticleId": 105708894, "Title": "A new chaotic image encryption algorithm based on dynamic DNA coding and RNA computing", "Abstract": "<p>In order to improve the complexity of the chaotic system and ensure the relevant security indicators of the cryptographic algorithm, a new chaotic image encryption algorithm based on dynamic DNA coding and RNA computing is proposed. In this paper, we first construct a four-dimensional hyperchaotic system with more complex dynamics and then use the plaintext-related keystream generated by the hyperchaotic system to dynamically DNA encode the plaintext image, then perform RNA coding conversion and amino acid substitution box generation, and finally use an improved replacement sequence generator to generate pseudo-random sequences for replacement operations to generate the final ciphertext image. Theoretical analysis and simulation results show that the proposed algorithm has excellent performance in security indicators such as key space, the number of pixels change rate, the number average changing intensity, entropy, clipping attack, noise attack, and chosen plaintext attack. Therefore, the algorithm has higher security.</p>", "Keywords": "Hyperchaotic system; DNA encode; RNA coding; Amino acid substitution box; Replacement sequence generator", "DOI": "10.1007/s00371-022-02750-5", "PubYear": 2023, "Volume": "39", "Issue": "12", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Technology, Harbin Institute of Technology, Weihai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Technology, Harbin Institute of Technology, Weihai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computer Science and Technology, Harbin Institute of Technology, Weihai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Technology, Harbin Institute of Technology, Weihai, China"}], "References": [{"Title": "Image encryption using shuffled Arnold map and multiple values manipulations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "189", "JournalTitle": "The Visual Computer"}, {"Title": "Multiple-image encryption algorithm based on bit planes and chaos", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "29-30", "Page": "20753", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An image encryption scheme based on a hybrid model of DNA computing, chaotic systems and hash functions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "33-34", "Page": "24993", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Dynamic phenomena of a financial hyperchaotic system and DNA sequences for image encryption", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "21-23", "Page": "32689", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 105709001, "Title": "User anonymity-based secure authentication protocol for telemedical server systems", "Abstract": "Telemedical server system enables a user to support the monitoring of health at home and access the medical facility over the network. Recently, many schemes have been proposed for providing security in the medical server system. Recently in year 2017, <PERSON><PERSON><PERSON> and <PERSON><PERSON> proposed a scheme for medical applications using two-factor key verification. They claimed that the protocol provides security against all types of known active and passive attacks. In this paper we show that the Limbas<PERSON> and Shivam scheme suffers from user anonymity, replay and impersonation attack. The Limbasiya and Shivam scheme fails to provide low power consumption in terms of cryptographic computational operation and over head to the server. We propose a secure user anonymity-based authentication protocol to remove the weakness of former protocols. Our scheme is more effective in terms of mutual authentication and low power consumption. The performance analysis of our protocol shows less cryptographic computational cost and the server overload. The proposed protocol is tested and analysed using AVISPA security verification to confirm the secure and authentic protocol for telemedical server system. Copyright © 2023 Inderscience Enterprises Ltd.", "Keywords": "authentication; AVISPA; efficiency; smart card; telemedical server", "DOI": "10.1504/IJICS.2023.128015", "PubYear": 2023, "Volume": "20", "Issue": "1/2", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Cybernetics, School of Computer Science and Engineering, University of Petroleum and Energy Studies, Dehradun, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, BML Munjal University, Gurgaon, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Cybernetics, School of Computer Science and Engineering, University of Petroleum and Energy Studies, Dehradun, India"}], "References": []}, {"ArticleId": 105709022, "Title": "Cultural difference of flow experience in the gamified online-learning platform: an explorative study", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSMARTTL.2022.128048", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 46975, "JournalTitle": "International Journal of Smart Technology and Learning", "ISSN": "2056-404X", "EISSN": "2056-4058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105709077, "Title": "ProbD: Faulty Path Detection Based on Probability in Software-Defined Networking", "Abstract": "With the increasing number of switches in Software-Defined Networking (SDN), there are more and more faults rising in the data plane. However, due to the existence of link redundancy and multi-path forwarding mechanisms, these problems cannot be detected in time. The current faulty path detection mechanisms have problems such as the large scale of detection and low efficiency, which is difficult to meet the requirements of efficient faulty path detection in large-scale SDN. Concerning this issue, we propose an efficient network path fault testing model ProbD based on probability detection. This model achieves a high probability of detecting arbitrary path fault in the form of small-scale random sampling. Under a certain path fault rate, ProbD obtains the curve of sample size and probability of detecting arbitrary path fault by randomly sampling network paths several times. After a small number of experiments, the ProbD model can correctly estimate the path fault rate of the network and calculate the total number of paths that need to be detected according to the different probability of detecting arbitrary path fault and the path fault rate of the network. The final experimental results show that, compared with the full path coverage test, the ProbD model based on probability detection can achieve efficient network testing with less overhead. Besides, the larger the network scale is, the more overhead will be saved.", "Keywords": "faulty path detection; Probability detection; software-defined networking", "DOI": "10.32604/iasc.2023.034265", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Yao", "Affiliation": "School of Computer Science and Technology, Hainan University, Haikou, 570228, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hainan University, Haikou, 570228, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hainan University, Haikou, 570228, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hainan University, Haikou, 570228, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hainan University, Haikou, 570228, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Beijing Jiaotong University, Beijing, 10004, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of British Columbia, Vancouver, V5K1K5, Canada"}], "References": [{"Title": "Machine Learning Empowered Security Management and Quality of Service Provision in SDN-NFV Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "3", "Page": "2723", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "An Intelligent Forwarding Strategy in SDN-Enabled Named-Data IoV", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "2949", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Centralized QoS Routing Model for Delay/Loss Sensitive Flows at the SDN-IoT Infrastructure", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "3727", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Mobile Fog Computing by Using SDN/NFV on 5G Edge Nodes", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "2", "Page": "751", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Adaptive Server Load Balancing in SDN Using PID Neural Network Controller", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "42", "Issue": "1", "Page": "229", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": *********, "Title": "100 años de la radio en Chile", "Abstract": "<p>El 19 de agosto de 2022 se conmemoraron los 100 años de la primera transmisión radial en Chile. Un siglo antes, académicos de la Universidad de Chile consiguieron comunicarse entre la casa central de esa casa de estudios, ubicada hasta ahora en plena Alameda, con el edificio que entonces albergaba al diario El Mercurio, en la esquina de las calles Compañía con Morandé, en el centro de Santiago. Esa conexión marca el nacimiento de la radiodifusión en Chile. Qué mejor excusa para la publicación de 100 años de la radio en Chile (1922-2022) (LOM Ediciones), editado por el profesor Raúl <PERSON>ri<PERSON>z, editor invitado de este número especial. El volumen colectivo va acompañado del sitio web 100añosradio.cl, un sitio patrimonial, sonoro, que visibiliza y difunde la memoria de la radio y su importancia en la sociedad chilena. Con ocasión del lanzamiento del libro y del portal digital, la Universidad de Chile conmemoró el centenario de la radio en Chile en una ceremonia en su casa central. En ella, voces y actores que revitalizan hoy los esfuerzos colectivos de antaño en esta revolución de las comunicaciones, honraron esta tradición. En la ceremonia, participaron y reflexionaron sobre los desafíos y proyección de este medio la Subsecreta- ria General de Gobierno de Chile, Valeska Naranjo; la vicerrectora de Extensión y Comunicaciones de la Universidad de Chile, Pilar Barba; el decano de la Facultad de Ciencias Físicas y Matemáticas (FCFM), Francisco Martínez; el jefe de Educación UNESCO Santiago, Valtencir Mendes; el Premio Nacional de Periodismo y voz del Diario de Cooperativa, Sergio Campos; la presidenta del directorio del Centro Cultural Palacio la Moneda, Antonella Estévez; el director de Radio Universidad de Chile, Patricio López; y Marcelo Comparini, periodista de Canal 13, Radio Oasis y Emisor Podcasting. En esta ocasión, reproducimos íntegramente dos de los discursos que se pronunciaron en la conmemoración de los 100 años de la radio en Chile: El de la rectora de la Universidad, Dra. Rosa Devés, y el de Silvia Aguilera, editora de LOM Ediciones. La presente versión fue editada para su lectura fluida, lo que incluyó los títulos y subtítulos.</p>", "Keywords": "", "DOI": "10.5354/0719-1529.2022.69403", "PubYear": 2022, "Volume": "31", "Issue": "46", "JournalId": 48966, "JournalTitle": "Comunicación y Medios", "ISSN": "0716-3991", "EISSN": "0716-3991", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Directora editorial de LOM ediciones, Chile"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Rectora de la Universidad de Chile, Chile"}], "References": []}, {"ArticleId": 105709170, "Title": "New machine learning approaches for real-life human activity recognition using smartphone sensor-based data", "Abstract": "In recent years, mainly due to the application of smartphones in this area, research in human activity recognition (HAR) has shown a continuous and steady growth. Thanks to its wide range of sensors, its size, its ease of use, its low price and its applicability in many other fields, it is a highly attractive option for researchers. However, the vast majority of studies carried out so far focus on laboratory settings, outside of a real-life environment. In this work, unlike in other papers, progress was sought on the latter point. To do so, a dataset already published for this purpose was used. This dataset was collected using the sensors of the smartphones of different individuals in their daily life, with almost total freedom. To exploit these data, numerous experiments were carried out with various machine learning techniques and each of them with different hyperparameters. These experiments proved that, in this case, tree-based models, such as Random Forest, outperform the rest. The final result shows an enormous improvement in the accuracy of the best model found to date for this purpose, from 74.39% to 92.97%.", "Keywords": "HAR ; Human activity recognition ; Machine learning ; Real life ; Smartphones ; Sensors", "DOI": "10.1016/j.knosys.2023.110260", "PubYear": 2023, "Volume": "262", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information Technologies, University of A Coruna, CITIC, 15071 A Coruna, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information Technologies, University of A Coruna, CITIC, 15071 A Coruna, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information Technologies, University of A Coruna, CITIC, 15071 A Coruna, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information Technologies, University of A Coruna, CITIC, 15071 A Coruna, Spain"}], "References": [{"Title": "Cross-subject transfer learning in human activity recognition systems using generative adversarial networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "426", "Issue": "", "Page": "26", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 105709187, "Title": "Tri-training algorithm based on cross entropy and K-nearest neighbors for network intrusion detection", "Abstract": "To address the problem of low detection accuracy due to training noise caused by mislabeling when Tri-training for network intrusion detection (NID), we propose a Tri-training algorithm based on cross entropy and K-nearest neighbors (TCK) for network intrusion detection. The proposed algorithm uses cross-entropy to replace the classification error rate to better identify the difference between the practical and predicted distributions of the model and reduce the prediction bias of mislabeled data to unlabeled data; K-nearest neighbors are used to remove the mislabeled data and reduce the number of mislabeled data. In order to verify the effectiveness of the algorithm proposed in this paper, experiments were conducted on 12 UCI datasets and NSL-KDD network intrusion datasets, and four indexes including accuracy, recall, F-measure and precision were used for comparison. The experimental results revealed that the TCK has superior performance than the conventional Tri-training algorithms and the Tri-training algorithms using only cross-entropy or K-nearest neighbor strategy. © 2022 Korean Society for Internet Information. All rights reserved.", "Keywords": "cross entropy; K-nearest neighbors; network intrusion detection (NID); semi-supervised learning; Tri-training", "DOI": "10.3837/tiis.2022.12.006", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105709188, "Title": "Affective algorithmic composition of music: A systematic review", "Abstract": "<p xml:lang=\"fr\"> \t\t\t<p>Affective music composition systems are known to trigger emotions in humans. However, the design of such systems to stimulate users' emotions continues to be a challenge because, studies that aggregate existing literature in the domain to help advance research and knowledge is limited. This study presents a systematic literature review on affective algorithmic composition systems. Eighteen primary studies were selected from IEEE Xplore, ACM Digital Library, SpringerLink, PubMed, ScienceDirect, and Google Scholar databases following a systematic review protocol. The findings revealed that there is a lack of a unique definition that encapsulates the various types of affective algorithmic composition systems. Accordingly, a unique definition is provided. The findings also show that most affective algorithmic composition systems are designed for games to provide background music. The generative composition method was the most used compositional approach. Overall, there was rather a low amount of research in the domain. Possible reasons for these trends are the lack of a common definition for affective music composition systems and also the lack of detailed documentation of the design, implementation and evaluation of the existing systems.</p> \t\t </p>", "Keywords": "affect; affective algorithmic systems; affective music composition; musical features; music; systematic review;", "DOI": "10.3934/aci.2023003", "PubYear": 2023, "Volume": "3", "Issue": "1", "JournalId": 94718, "JournalTitle": "Applied Computing and Intelligence", "ISSN": "2771-392X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, University of Eastern Finland, Joensuu, Finland"}], "References": [{"Title": "A systematic review of the research trends of machine learning in supply chain management", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "7", "Page": "1463", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Supervised machine learning for audio emotion recognition", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "4", "Page": "637", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "Automation of systematic literature reviews: A systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Cagatay Catal", "PubYear": 2021, "Volume": "136", "Issue": "", "Page": "106589", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 105709197, "Title": "A Lightweight Pedestrian Intrusion Detection and Warning Method for Intelligent Traffic Security", "Abstract": "As a research hotspot, pedestrian detection has a wide range of applications in the field of computer vision in recent years. However, current pedestrian detection methods have problems such as insufficient detection accuracy and large models that are not suitable for large-scale deployment. In view of these problems mentioned above, a lightweight pedestrian detection and early warning method using a new model called you only look once (Yolov5) is proposed in this paper, which utilizing advantages of Yolov5s model to achieve accurate and fast pedestrian recognition. In addition, this paper also optimizes the loss function of the batch normalization (BN) layer. After sparsification, pruning and fine-tuning, got a lot of optimization, the size of the model on the edge of the computing power is lower equipment can be deployed. Finally, from the experimental data presented in this paper, under the training of the road pedestrian dataset that we collected and processed independently, the Yolov5s model has certain advantages in terms of precision and other indicators compared with traditional single shot multiBox detector (SSD) model and fast region-convolutional neural network (Fast R-CNN) model. After pruning and lightweight, the size of training model is greatly reduced without a significant reduction in accuracy, and the final precision reaches 87%, while the model size is reduced to 7,723 KB. Copyright © 2022 KSII.", "Keywords": "computer vision; lightweight; pedestrian detection; Traffic safety; YOLOv5", "DOI": "10.3837/tiis.2022.12.007", "PubYear": 2022, "Volume": "16", "Issue": "12", "JournalId": 10116, "JournalTitle": "KSII Transactions on Internet and Information Systems", "ISSN": "1976-7277", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105709202, "Title": "Editorial Monográfico Nº 46: 100 años de la radio en Iberoamérica. Estudios de radio y futuro del medio", "Abstract": "<p>Los artículos que conforman este número especial buscan llenar un vacío en la producción académica en Iberoamérica. Tres artículos provienen de Chile, uno de Argentina y otro de España. Pese a que no todos los países de la región encuentran representación, la selección destaca por abordar aspectos que han sido menos explorados en los radio studies y que, sin embargo, son de especial interés y se constituyen como una contribución al estudio y comprensión del ecosistema radiofónico y sonoro.</p>", "Keywords": "", "DOI": "10.5354/0719-1529.2022.69364", "PubYear": 2022, "Volume": "31", "Issue": "46", "JournalId": 48966, "JournalTitle": "Comunicación y Medios", "ISSN": "0716-3991", "EISSN": "0716-3991", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad de Chile, Chile"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Complutense de Madrid, España"}], "References": []}, {"ArticleId": 105709349, "Title": "The Effect of Community Managers on Online Idea Crowdsourcing Activities", "Abstract": "<p>In this study, we investigate whether and to what extent community managers in online collaborative communities can stimulate community activities through their engagement. Using a novel data set of 22 large online idea crowdsourcing campaigns, we find that moderate but steady manager activities are adequate to enhance community participation. Moreover, we show that appreciation, motivation, and intellectual stimulation by community managers are positively associated with community participation but that the effectiveness of these communication strategies depends on the form of participation managers wish to encourage. Finally, the data reveal that community manager activities requiring more effort, such as media file uploads vs. simple written comments, have a stronger effect on community participation.</p>", "Keywords": "", "DOI": "10.17705/1jais.00777", "PubYear": 2023, "Volume": "24", "Issue": "1", "JournalId": 24874, "JournalTitle": "Journal of the Association for Information Systems", "ISSN": "", "EISSN": "1536-9323", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Bremen"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Halle Institute for Economic Research / Otto von <PERSON>uericke University Magdeburg"}], "References": []}, {"ArticleId": 105709552, "Title": "An abstraction-refinement framework for verifying strategic properties in multi-agent systems with imperfect information", "Abstract": "We investigate the verification of Multi-Agent Systems against strategic properties expressed in Alternating-time Temporal Logic under the assumptions of imperfect information and perfect recall. To this end, we develop a three-valued semantics for concurrent game structures upon which we define an abstraction method. We prove that concurrent game structures with imperfect information admit perfect information abstractions that preserve three-valued satisfaction. Furthermore, to deal with cases in which the value of a specification is undefined, we develop a novel automata-theoretic technique for the linear-time logic ( LTL ), then apply it to finding “failure” states. The latter can then be fed into a refinement procedure, thus providing a sound, albeit incomplete, verification method. We illustrate the overall procedure in a variant of the Train Gate Controller scenario and a simple voting protocol under imperfect information and perfect recall. We also present an implementation of our procedure and provide preliminary experimental results.", "Keywords": "Multi-agent systems ; Strategic ability ; Alternating-time temporal logic ; Model checking ; Concurrent games", "DOI": "10.1016/j.artint.2022.103847", "PubYear": 2023, "Volume": "316", "Issue": "", "JournalId": 13748, "JournalTitle": "Artificial Intelligence", "ISSN": "0004-3702", "EISSN": "1872-7921", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Imperial College London, United Kingdom;Université d'Evry, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Università di Genova, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Télécom Paris, France;Corresponding author"}], "References": [{"Title": "Verification of multi-agent systems with public actions against strategy logic", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "285", "Issue": "", "Page": "103302", "JournalTitle": "Artificial Intelligence"}, {"Title": "Towards Partial Order Reductions for Strategic Ability", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>a; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "68", "Issue": "", "Page": "817", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Multi-valued Verification of Strategic Ability", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "175", "Issue": "1-4", "Page": "207", "JournalTitle": "Fundamenta Informaticae"}, {"Title": "Approximating Perfect Recall when Model Checking Strategic Abilities: Theory and Applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "897", "JournalTitle": "Journal of Artificial Intelligence Research"}]}, {"ArticleId": 105709741, "Title": "Ionic conductive hydrogels formed through hydrophobic association for flexible strain sensing", "Abstract": "Over the years, hydrogels have been extensively studied and got a lot of attraction as wearable sensors, due to their stretchability and flexibility, however, their application of repetitive sensing is greatly constrained owing to their lengthy response time and poor mechanical properties. In the current study, a stretchable and conductive hydrogels were fabricated by interlinking lauryl methacrylate with acrylamide and sodium alginate via a physical crosslinked network, resulting in enhanced mechanical properties due to micelle dynamic crosslinking points and hydrogen bonding. The observed tensile strain was 1400 % with a fracture stress of 390 kPa and low hysteresis energy. In addition, the hydrogel strain sensor exhibited a wide range of strain (25–300 %), excellent cyclic stability of more than 350 cycles and showed quick response time of 200 ms. Moreover, the hydrogel is also designed to be a built-in sensor to monitor slight and subtle changes in the human body such as wrist, finger flexion, and swallowing. The strain sensors are also capable of recognizing letters written over it sensitively and distinctively. Hence, we believe that these hydrogel-based strain sensors could be applied in future wearable electronic devices and for human movement monitoring.", "Keywords": "Hydrogels ; Hydrophobic associations ; Strain sensor ; Human motion monitoring", "DOI": "10.1016/j.sna.2022.114148", "PubYear": 2023, "Volume": "350", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Polymer Laboratory, National Centre of Excellence in Physical Chemistry, University of Peshawar, Peshawar 25120, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Polymer Laboratory, National Centre of Excellence in Physical Chemistry, University of Peshawar, Peshawar 25120, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Polymer Laboratory, National Centre of Excellence in Physical Chemistry, University of Peshawar, Peshawar 25120, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Polymer Laboratory, National Centre of Excellence in Physical Chemistry, University of Peshawar, Peshawar 25120, Pakistan;Institute for Sustainable Energy, College of Sciences, Shanghai University, Shanghai 200444, PR China;Corresponding author at: Polymer Laboratory, National Centre of Excellence in Physical Chemistry, University of Peshawar, Peshawar 25120, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Sustainable Energy, College of Sciences, Shanghai University, Shanghai 200444, PR China;Corresponding author"}], "References": []}, {"ArticleId": 105709840, "Title": "AI and Games at IJCAI - ECAI 2022", "Abstract": "", "Keywords": "", "DOI": "10.31449/inf.v46i4.4587", "PubYear": 2023, "Volume": "46", "Issue": "4", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jozef <PERSON>"}], "References": []}, {"ArticleId": 105709872, "Title": "Smart Grid Communication Under Elliptic Curve Cryptography", "Abstract": "Smart Grids (SGs) are introduced as a solution for standard power distribution. The significant capabilities of smart grids help to monitor consumer behaviors and power systems. However, the delay-sensitive network faces numerous challenges in which security and privacy gain more attention. Threats to transmitted messages, control over smart grid information and user privacy are the major concerns in smart grid security. Providing secure communication between the service provider and the user is the only possible solution for these security issues. So, this research work presents an efficient mutual authentication and key agreement protocol for smart grid communication using elliptic curve cryptography which is robust against security threats. A trust authority module is introduced in the security model apart from the user and service provider for authentication. The proposed approach performance is verified based on different security features, communication costs, and computation costs. The comparative analysis of experimental results demonstrates that the proposed authentication model attains better performance than existing state of art of techniques.", "Keywords": "elliptic curve cryptography; key management; mutual authentication; Smart grid", "DOI": "10.32604/iasc.2023.029725", "PubYear": 2023, "Volume": "36", "Issue": "2", "JournalId": 15781, "JournalTitle": "Intelligent Automation & Soft Computing", "ISSN": "1079-8587", "EISSN": "2326-005X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>ra Engineering College, Tamilnadu, Salem, 637503, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Government College of Engineering, Tamilnadu, Salem, 636011, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Knowledge Institute of Technology, Tamilnadu, Salem, 637504, India"}], "References": []}, {"ArticleId": 105709944, "Title": "Social Movement Sustainability on Social Media: An Analysis of the Women’s March Movement on Twitter", "Abstract": "Social media has emerged as a powerful medium to organize and mobilize social movements. In particular, the connective action of social media builds associations and allows for the continuity of social movements. Yet there is a lack of research on how connective action emergent from social media messages sustains long-term social movements. Accordingly, in this study, we concentrate on Twitter messages related to Women’s March protests held in 2017, 2018, and 2019. Using an interpretive analysis followed by the topic modeling approach, we analyzed the tweets to identify the different types of messages associated with the movement. These messages were classified using a set of categories and subcategories. Furthermore, we conducted a temporal analysis of the message (sub)categories to understand how distinct messages allow movement continuity beyond a specific protest march. Results suggest that while most of the messages are used to motivate and mobilize individuals, the connective action tactics employed through messages sent before, during, and after the marches allowed Women’s March to become a broader and more persistent movement. We advance theoretical propositions to explain the sustainability of a long-term social movement on social media, exemplified through large-scale connective action that persists over time. In doing so, this study contributes to connective action research by providing message categorization that synthesizes the meaning of message content. The findings could help social movement organizers learn different ways to frame messages that resonate with broader social media users. Moreover, our approach to analyzing a large set of tweets might interest other qualitative researchers. © 2023, Association for Information Systems. All rights reserved.", "Keywords": "Connective Action; Framing; Interpretive Analysis; Mobilization; Movement Sustainability; Social Media; Social Movement; Temporal Analysis; Topic Modeling", "DOI": "10.17705/1jais.00776", "PubYear": 2023, "Volume": "24", "Issue": "1", "JournalId": 24874, "JournalTitle": "Journal of the Association for Information Systems", "ISSN": "", "EISSN": "1536-9323", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Management, University of Massachusetts Boston, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> College of Business, University of Houston, United States"}], "References": []}]