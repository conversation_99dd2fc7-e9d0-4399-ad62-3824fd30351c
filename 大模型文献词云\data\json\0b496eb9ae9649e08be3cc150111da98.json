[{"ArticleId": 119736846, "Title": "Design of a torus magnetorheological finishing (TMRF) device for optical manufacturing", "Abstract": "<p>To address the growing demand for a diverse range of shapes in optical components for evolving optical systems, this paper introduces a novel torus magnetorheological finishing (TMRF) method. This method harnesses the strengths of both magnetorheological finishing (MRF) and ring tools, thereby achieving ring tool with a guaranteed level of machining stability. Magnetic field analysis is performed in accordance with the Biot-Savart law, and the key excitation parameters are optimized, systematically designed, simulated, and experimentally validated. The design parameters comprise the pole width (w), pole gap (e), pole height (H), and the magnetic field’s influence at the loop section (θmag), and the derived design specifications are sequentially w = 10 mm, e = 2 mm, H = 20 mm, θmag = 180°. The simulation of the magnetic field aligns closely with the theoretical calculations, mirroring the observed change trends from empirical magnetic field tests. For the TMRF under both short and long terms fixed-point polishing, the variations in material volume removal rate were ± 3.0% and ± 10.0%, respectively, and the variations in root mean square (RMS) values at the polishing tool’s base were ± 3.8% and ± 2.0%, respectively. The experimental evidence confirms that TMRF exhibits a stable and highly effective polishing performance. Moreover, the design principles elucidated in this paper offer a significant reference for future research.</p>", "Keywords": "Torus magnetorheological finishing; Theoretical analysis; Simulation; Tool influence function", "DOI": "10.1007/s00170-024-14897-7", "PubYear": 2025, "Volume": "136", "Issue": "2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, China; Hunan Key Laboratory of Ultra-Precision Machining Technology, Changsha, China; Laboratory of Science and Technology On Integrated Logistics Support, National University of Defense Technology, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, China; Hunan Key Laboratory of Ultra-Precision Machining Technology, Changsha, China; Laboratory of Science and Technology On Integrated Logistics Support, National University of Defense Technology, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, China; Hunan Key Laboratory of Ultra-Precision Machining Technology, Changsha, China; Laboratory of Science and Technology On Integrated Logistics Support, National University of Defense Technology, Changsha, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, China; Hunan Key Laboratory of Ultra-Precision Machining Technology, Changsha, China; Laboratory of Science and Technology On Integrated Logistics Support, National University of Defense Technology, Changsha, China"}, {"AuthorId": 5, "Name": "Feng Shi", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, China; Hunan Key Laboratory of Ultra-Precision Machining Technology, Changsha, China; Laboratory of Science and Technology On Integrated Logistics Support, National University of Defense Technology, Changsha, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, China; Hunan Key Laboratory of Ultra-Precision Machining Technology, Changsha, China; Laboratory of Science and Technology On Integrated Logistics Support, National University of Defense Technology, Changsha, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence Science and Technology, National University of Defense Technology, Changsha, China; Hunan Key Laboratory of Ultra-Precision Machining Technology, Changsha, China; Laboratory of Science and Technology On Integrated Logistics Support, National University of Defense Technology, Changsha, China"}], "References": [{"Title": "Experimental investigation of mid-spatial frequency surface textures on fused silica after computer numerical control bonnet polishing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "5-6", "Page": "1367", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Extreme pressure and antiwear additives for lubricant: academic insights and perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "1-2", "Page": "1", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "An adaptive bonnet polishing approach based on dual-mode contact depth TIF", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "5-6", "Page": "2183", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 119737031, "Title": "3d human pose estimation based on conditional dual-branch diffusion", "Abstract": "<p>Thanks to the development of 2D keypoint detectors, monocular 3D human pose estimation (HPE) via 2D-to-3D lifting approaches have achieved remarkable improvements. However, monocular 3D HPE is still a challenging problem due to the inherent depth ambiguities and occlusions. Recently, diffusion models have achieved great success in the field of image generation. Inspired by this, we transform 3D human pose estimation problem into a reverse diffusion process, and propose a dual-branch diffusion model so as to handle the indeterminacy and uncertainty of 3D pose and fully explore the global and local correlations between joints. Furthermore, we propose conditional dual-branch diffusion model to enhance the performance of 3D human pose estimation, in which the joint-level semantic information are regarded as the condition of the diffusion model, and integrated into the joint-level representations of 2D pose to enhance the expression of joints. The proposed method is verified on two widely used datasets and the experimental results have demonstrated the superiority.</p>", "Keywords": "Human pose estimation; Diffusion model; Dual-branch; Joint semantics", "DOI": "10.1007/s00530-024-01569-5", "PubYear": 2025, "Volume": "31", "Issue": "1", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "Jinghua Li", "Affiliation": "Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing, China"}, {"AuthorId": 2, "Name": "Zhu<PERSON><PERSON> Bai", "Affiliation": "Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing, China"}, {"AuthorId": 3, "Name": "Dehui Kong", "Affiliation": "Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing, China"}, {"AuthorId": 6, "Name": "Baocai Yin", "Affiliation": "Beijing Key Laboratory of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing, China; Corresponding author."}], "References": [{"Title": "SRDiff: Single image super-resolution with diffusion probabilistic models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "479", "Issue": "", "Page": "47", "JournalTitle": "Neurocomputing"}, {"Title": "Learning Enriched Hop-Aware Correlation for Robust 3D Human Pose Estimation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "6", "Page": "1566", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 119737210, "Title": "Multi-agent dual actor-critic framework for reinforcement learning navigation", "Abstract": "<p>Multi-Agent navigation task remains a fundamental challenge in robotics and autopilots. Reinforcement learning approaches to navigation often struggle to address the value overestimation in dynamic environments, multi-agent interactions, and diverse objectives. In this study, we propose a novel Multi-Agent Dual Actor-Critic (MADAC) framework for reinforcement learning navigation tasks. Our framework consists of dual-Actor and dual-Critic pairs. The dual-Actor component is developed to bolster the diversity of exploration by agents and avoid potential overestimation caused by a single policy, while the dual-Critic is employed to effectively regulate the fluctuation of value function estimates, mitigating inaccuracies in state value estimation and alleviating the issue of overestimation. By incorporating dual Actor-Critic pairs, our framework facilitates effective coordination and learning among multiple agents, leading to improved navigation performance and adaptability in complex environments. We validated the performance of the MADAC framework through extensive experiments in simulated navigation environments. Our results demonstrate that the MADAC framework outperforms SOTA methods in terms of navigation success rate, convergence speed, and robustness to dynamic environments, and scalability to increasing numbers of agents. The MADAC framework offers a promising approach for tackling real-world navigation challenges in dynamic and collaborative environments, with implications for robotics, autonomous systems, and interactive entertainment.</p>", "Keywords": "Multi-agent reinforcement learning; Overestimation; Navigation; Regularization; Actor-critic", "DOI": "10.1007/s10489-024-05933-w", "PubYear": 2025, "Volume": "55", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, North University of China, Taiyuan, China; Shanxi Key Laboratory of Machine Vision and Virtual Reality, Taiyuan, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, North University of China, Taiyuan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sydney Smart Technology College, Northeastern University, Qinhuangdao, China"}, {"AuthorId": 4, "Name": "Ligang He", "Affiliation": "Department of Computer Science, University of Warwick, Coventry, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, North University of China, Taiyuan, China; Shanxi Key Laboratory of Machine Vision and Virtual Reality, Taiyuan, China"}], "References": [{"Title": "Reinforcement learning in robotic applications: a comprehensive survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "2", "Page": "945", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Safety-constrained reinforcement learning with a distributional safety critic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "112", "Issue": "3", "Page": "859", "JournalTitle": "Machine Learning"}, {"Title": "Reinforcement learning algorithms: A brief survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "120495", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Communication compression techniques in distributed deep learning: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "102927", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Better value estimation in Q-learning-based multi-agent reinforcement learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "28", "Issue": "6", "Page": "5625", "JournalTitle": "Soft Computing"}, {"Title": "Optimal demand response based dynamic pricing strategy via Multi-Agent Federated Twin Delayed Deep Deterministic policy gradient algorithm", "Authors": "Haining Ma; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "133", "Issue": "", "Page": "108012", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 119737245, "Title": "Achieving sustainability by additive manufacturing: a state-of-the-art review and perspectives", "Abstract": "", "Keywords": "", "DOI": "10.1080/17452759.2024.2438899", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 7390, "JournalTitle": "Virtual and Physical Prototyping", "ISSN": "1745-2759", "EISSN": "1745-2767", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National University of Singapore, Singapore"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Singapore Centre for 3D Printing (SC3DP), School of Mechanical and Aerospace Engineering, Nanyang Technological University, Singapore;School of Mechanical and Aerospace Engineering, Nanyang Technological University, Singapore"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Product Development Pillar, Singapore University of Technology and Design, Singapore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Singapore Centre for 3D Printing (SC3DP), School of Mechanical and Aerospace Engineering, Nanyang Technological University, Singapore;School of Mechanical and Aerospace Engineering, Nanyang Technological University, Singapore"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Engineering Product Development Pillar, Singapore University of Technology and Design, Singapore"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National University of Singapore, Singapore"}], "References": [{"Title": "Sustainability performance indicators for additive manufacturing: a literature review based on product life cycle studies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "7-8", "Page": "3109", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Deep learning for fabrication and maturation of 3D bioprinted tissues and organs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "3", "Page": "340", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "Laser powder bed fusion for metal additive manufacturing: perspectives on recent developments", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "3", "Page": "359", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "Rapid surface defect identification for additive manufacturing with in-situ point cloud processing and machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "1", "Page": "50", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "Toward in-situ flaw detection in laser powder bed fusion additive manufacturing through layerwise imagery and machine learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "59", "Issue": "", "Page": "12", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Development of ensemble machine learning approaches for designing fiber-reinforced polymer composite strain prediction model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "4", "Page": "3625", "JournalTitle": "Engineering with Computers"}, {"Title": "Eco-friendly additive manufacturing of metals: Energy efficiency and life cycle analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "459", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Optimizing fused deposition modelling parameters based on the design for additive manufacturing to enhance product sustainability", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Muruvvet <PERSON>", "PubYear": 2023, "Volume": "145", "Issue": "", "Page": "103833", "JournalTitle": "Computers in Industry"}, {"Title": "Sustainable 4D printing of magneto-electroactive shape memory polymer composites", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "1-2", "Page": "35", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Valorisation of vegetable food waste utilising three-dimensional food printing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2023, "Volume": "18", "Issue": "1", "Page": "e2146593", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "Multisensor fusion-based digital twin for localized quality prediction in robotic laser-directed energy deposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "84", "Issue": "", "Page": "102581", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Predicting the number of printed cells during inkjet-based bioprinting process based on droplet velocity profile using machine learning approaches", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "35", "Issue": "5", "Page": "2349", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Segmentation-based closed-loop layer height control for enhancing stability and dimensional accuracy in wire-based laser metal deposition", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "86", "Issue": "", "Page": "102683", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "In-process and post-process strategies for part quality assessment in metal powder bed fusion: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "73", "Issue": "", "Page": "75", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "An ontology of eco-design for additive manufacturing with informative sustainability analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "60", "Issue": "", "Page": "102430", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "In-situ process monitoring and adaptive quality enhancement in laser additive manufacturing: A critical review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "74", "Issue": "", "Page": "527", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 119737344, "Title": "Convexification for a coefficient inverse problem for a system of two coupled nonlinear parabolic equations", "Abstract": "A system of two coupled nonlinear parabolic partial differential equations with two opposite directions of time is considered. In fact, this is the so-called “Mean Field Games System” (MFGS), which is derived in the mean field games (MFG) theory. This theory has numerous applications in social sciences. The topic of Coefficient Inverse Problems (CIPs) in the MFG theory is in its infant age, both in theory and computations. A numerical method for this CIP is developed. Convergence analysis ensures the global convergence of this method. Numerical experiments are presented.", "Keywords": "", "DOI": "10.1016/j.camwa.2024.12.004", "PubYear": 2025, "Volume": "179", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Statistics, University of North Carolina at Charlotte, Charlotte, NC, 28223, USA"}, {"AuthorId": 2, "Name": "Jingzhi Li", "Affiliation": "Department of Mathematics & National Center for Applied Mathematics Shenzhen & SUSTech International Center for Mathematics, Southern University of Science and Technology, Shenzhen 518055, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Lanzhou University, Lanzhou 730000, PR China;Corresponding author"}], "References": [{"Title": "A Mean Field Game Inverse Problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "92", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}]}, {"ArticleId": 119737356, "Title": "Dataset of video game-based assessments in digital culture courses at Universidad Indoamerica.", "Abstract": "<p>This dataset contains evaluation results from video game-based assessments administered to first-level university students across six different academic programs at Universidad Indoamérica from October 2022 to August 2024. The data were collected using an adapted version of Pacman through the ClassTools.net platform, where traditional quiz questions were integrated into gameplay mechanics. The dataset comprises 1418 assessment attempts from students in Law, Medicine, Psychology, Clinical Psychology, Architecture, and Nursing programs, documenting their performance in digital culture and computing courses. Each record includes attempt number, timestamp, student identifier, gender, academic period, section, career program, and score achieved. The dataset enables analysis of student performance patterns, learning progression through multiple attempts, and comparative studies across different academic programs and periods. This information can support research in educational gamification, assessment design, and digital learning strategies in higher education.</p><p>© 2024 The Author.</p>", "Keywords": "Digital culture;Educational assessment;Gamification;Learning analytics;Student performance", "DOI": "10.1016/j.dib.2024.111217", "PubYear": 2025, "Volume": "58", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Facultad de Educación, Universidad Indoamérica, Quito, Ecuador."}], "References": [{"Title": "A Systematic Literature Review of Game-Based Assessment Studies: Trends and Challenges", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "4", "Page": "500", "JournalTitle": "IEEE Transactions on Learning Technologies"}]}, {"ArticleId": *********, "Title": "A structural taxonomy for lifted software product line analyses", "Abstract": "A software product line (SPL) is a structured collection of distinct software products developed from a common set of artifacts. SPLs can encompass millions of products, so analysing each product in a brute-force manner is infeasible. To analyse SPLs directly, analyses must be lifted , i.e., redefined to accommodate the semantics of SPLs. Over the past two decades, many kinds of analyses have been lifted from products to SPLs. Looking at the landscape of lifted analyses, we observe various techniques for lifting which vary across numerous dimensions. To help engineers and research navigate this landscape, we propose a classification scheme for lifted analyses based on a set of features lifted analyses can exhibit. We then conduct a systematic literature review (SLR) analysing the landscape of lifted analyses produced over the last 20 years. We analyse 140 research papers which discuss the design and implementation of lifted analyses. We provide quantitative analysis of the types of analyses which have been lifted, and apply our taxonomy to clarify how lifting was accomplished. We discuss examples of how each of the lifting methods have been applied, and identify gaps in the research literature which may provide directions for future work.", "Keywords": "Software product lines; Lifting; Variability-aware analysis; SLR", "DOI": "10.1016/j.jss.2024.112280", "PubYear": 2025, "Volume": "222", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Toronto, Toronto, Ontario, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Toronto, Toronto, Ontario, Canada"}, {"AuthorId": 3, "Name": "Alessio <PERSON>", "Affiliation": "University of Toronto, Toronto, Ontario, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Toronto, Toronto, Ontario, Canada"}], "References": [{"Title": "Automatic and efficient variability-aware lifting of functional programs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A Formal Framework of Software Product Line Analyses", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "Successful combination of database search and snowballing for identification of primary studies in systematic literature studies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "147", "Issue": "", "Page": "106908", "JournalTitle": "Information and Software Technology"}, {"Title": "Adaptive search query generation and refinement in systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "102231", "JournalTitle": "Information Systems"}]}, {"ArticleId": 119737816, "Title": "Quadratic equations in the lamplighter group", "Abstract": "In this paper we study the complexity of solving quadratic equations in the lamplighter group. We give a complete classification of cases (depending on genus and other characteristics of a given equation) when the problem is NP -complete or polynomial-time decidable. We notice that the conjugacy problem can be solved in linear time. Finally, we prove that the problem belongs to the class XP .", "Keywords": "", "DOI": "10.1016/j.jsc.2024.102417", "PubYear": 2025, "Volume": "129", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematical Sciences, Stevens Institute of Technology, Hoboken NJ 07030, United States of America"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematical Sciences, Stevens Institute of Technology, Hoboken NJ 07030, United States of America"}], "References": []}, {"ArticleId": 119737828, "Title": "Predicting depression level based on human activities and feelings: A fuzzy logic-based analysis", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.dsm.2024.11.003", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 84310, "JournalTitle": "Journal of Information Technology and Data Management", "ISSN": "2666-7649", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A Data-Driven Heart Disease Prediction Model Through K-Means Clustering-Based Anomaly Detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Multi‐aspects AI ‐based modeling and adversarial learning for cybersecurity intelligence and robustness: A comprehensive overview", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "5", "Page": "e295", "JournalTitle": "Security and Privacy"}, {"Title": "A new approach to android malware detection using fuzzy logic-based simulated annealing and feature selection", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "4", "Page": "10525", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Patient Questionnaires Based Parkinson’s Disease Classification Using Artificial Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON> M<PERSON><PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "5", "Page": "1821", "JournalTitle": "Annals of Data Science"}]}, {"ArticleId": 119737916, "Title": "Local and global feature attention fusion network for face recognition", "Abstract": "Recognition of low-quality face images remains a challenge due to invisible or deformation in partial facial regions. For low-quality images dominated by missing partial facial regions, local region similarity contributes more to face recognition (FR). Conversely, in cases dominated by local face deformation, excessive attention to local regions may lead to misjudgments, while global features exhibit better robustness. However, most of the existing FR methods neglect the bias in feature quality of low-quality images introduced by different factors. To address this issue, we propose a Local and Global Feature Attention Fusion (LGAF) network based on feature quality. The network adaptively allocates attention between local and global features according to feature quality and obtains more discriminative and high-quality face features through local and global information complementarity. In addition, to effectively obtain fine-grained information at various scales and increase the separability of facial features in high-dimensional space, we introduce a Multi-Head Multi-Scale Local Feature Extraction (MHMS) module. Experimental results demonstrate that the LGAF achieves the best average performance on 4 validation sets (CFP-FP, CPLFW, AgeDB, and CALFW), and the performance on TinyFace and SCFace outperforms the state-of-the-art methods (SoTA).", "Keywords": "", "DOI": "10.1016/j.patcog.2024.111227", "PubYear": 2025, "Volume": "161", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cognitive Computing and Intelligent Information Processing (CCIIP) Laboratory, School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cognitive Computing and Intelligent Information Processing (CCIIP) Laboratory, School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, China;Corresponding author"}], "References": [{"Title": "Self-restrained triplet loss for accurate masked face recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "108473", "JournalTitle": "Pattern Recognition"}, {"Title": "PLFace: Progressive Learning for Face Recognition with <PERSON>", "Authors": "<PERSON><PERSON><PERSON> Huang; <PERSON><PERSON><PERSON>; Guangcheng Wang", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109142", "JournalTitle": "Pattern Recognition"}, {"Title": "Hybrid token transformer for deep face recognition", "Authors": "<PERSON><PERSON><PERSON> Su; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "109443", "JournalTitle": "Pattern Recognition"}, {"Title": "HeadPose-Softmax: Head pose adaptive curriculum learning loss for deep face recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "109552", "JournalTitle": "Pattern Recognition"}, {"Title": "Coupled discriminative manifold alignment for low-resolution face recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "147", "Issue": "", "Page": "110049", "JournalTitle": "Pattern Recognition"}, {"Title": "CoReFace: Sample-guided Contrastive Regularization for Deep Face Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "152", "Issue": "", "Page": "110483", "JournalTitle": "Pattern Recognition"}, {"Title": "Masked face recognition using domain adaptation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "153", "Issue": "", "Page": "110574", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 119737952, "Title": "Optimized Interpretable Generalized Additive Neural Network-based Human Brain Diagnosis using Medical Imaging", "Abstract": "This paper proposes an Optimized Interpretable Generalized Additive Neural Network-based Human Brain Diagnosis using Medical Imaging (IGANN HBD-MI) to address challenges in MRI-based brain tumor classification. Current deep learning models suffer from high parameter dependency, computational complexity, and difficulties with MRI image noise, which can lead to inaccurate classifications. The IGANN HBD-MI framework begins by collecting MRI brain images from the BRaTS 2021 Task 1 Dataset, which are preprocessed using the Pseudolinear Maximum Correntropy Kalman Filter (PMCKF) to enhance image quality by resampling, normalizing intensity, and removing noise. Segmentation is performed using Sparse Reconstructive Multi-View Evidential Clustering (SRMVEC), which isolates Regions of Interest (RoI) crucial for diagnosis. Haralick texture features are extracted using the Synchro Transient Extracting Transform (SET) for significant texture information. Classification is carried out by the Interpretable Generalized Additive Neural Network (IGANN), which categorizes brain images into Native, Post-contrast T1-weighted, T2-weighted, and T2 FLAIR types. The Elite Opposite Sparrow Search Algorithm (EOSSA) optimizes IGANN's parameters to improve classification accuracy. The IGANN HBD-MI method achieves 99.61% accuracy, 98.5% precision, and 97.2% sensitivity, outperforming existing methods for reliable brain disease classification in MRI images.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.112862", "PubYear": 2025, "Volume": "309", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, School of Computing (SoC), Vel Tech Rangarajan Dr. <PERSON> R&D Institute of Science and Technology 600062, India;Corresponding author"}, {"AuthorId": 2, "Name": "Sasidhar A", "Affiliation": "Department of Computer Science and Engineering, University College of Engineering, Panruti 607106, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, School of Computing (SoC), Kalasalingam Academy of Research and Education, Kalsalingam University, Krishnankoil 626126, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, University College of Engineering (BIT Campus), Anna University, Tiruchirappalli, Tamil Nadu 620024, India"}], "References": [{"Title": "A new elite opposite sparrow search algorithm-based optimized LightGBM approach for fault diagnosis", "Authors": "<PERSON><PERSON> Fang; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "8", "Page": "10473", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Review on automated condition assessment of pipelines with machine learning", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "101687", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "MBTFCN: A novel modular fully convolutional network for MRI brain tumor multi-classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "118776", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Optimization empowered hierarchical residual VGGNet19 network for multi-class brain tumour classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "11", "Page": "16691", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Region-based evidential deep learning to quantify uncertainty and improve robustness of brain tumor segmentation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "30", "Page": "22071", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Semantic Segmentation for Brain Injury Using Multi-Class Deep Level Convolution Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "Cybernetics and Systems"}, {"Title": "An enhanced deep learning method for multi-class brain tumor classification using deep transfer learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "20", "Page": "31709", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multi-class classification of Alzheimer's disease detection from 3D MRI image using ML techniques and its performance analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "11", "Page": "33527", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An automatic multi-class lung disease classification using deep learning based bidirectional long short term memory with spiking neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "16", "Page": "49091", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 119737973, "Title": "Complex Architecture Surpasses Ligand Lipophilicity for Efficient Eu(III) Sensing under Acidic Conditions", "Abstract": "Efficient and selective sensing of lanthanides are crucial for identifying valuable lanthanide streams and designing effective extraction/masking agents for mining and nuclear waste management. Luminescence-based sensing often struggles with interference from water molecules. Common approaches to improve emissive lanthanide complexes in water involve increasing ligand lipophilicity and enclosing the emitting metal center. Herein, we have demonstrated that lipophilic ligands do not always lead to higher emissions. When strong coordinating groups are involved in the ligand, the complex may adapt special architecture to compensate the hydrophilic nature of the ligand by creating lipophilic microenvironment and lead to higher emissions. Our findings highlight the importance of the overall lipophilicity of both the ligand and the final complex, which could serve as important reference for future design of efficient lanthanide sensing and emissive complexes.", "Keywords": "", "DOI": "10.1016/j.snb.2024.137106", "PubYear": 2025, "Volume": "426", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, Capital Normal University, Beijing 100048, China;Beijing National Laboratory for Molecular Sciences, Key Laboratory of Polymer Chemistry and Physics of Ministry of Education, Centre for Soft Matter Science and Engineering, College of Chemistry and Molecular Engineering, Peking University, Beijing 100871, China;<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, Capital Normal University, Beijing 100048, China;Beijing National Laboratory for Molecular Sciences, Key Laboratory of Polymer Chemistry and Physics of Ministry of Education, Centre for Soft Matter Science and Engineering, College of Chemistry and Molecular Engineering, Peking University, Beijing 100871, China;<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> contributed equally to this work"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Liu", "Affiliation": "Institute of Nuclear and New Energy Technology, Tsinghua University, Beijing 100084, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, Capital Normal University, Beijing 100048, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing National Laboratory for Molecular Sciences, Key Laboratory of Polymer Chemistry and Physics of Ministry of Education, Centre for Soft Matter Science and Engineering, College of Chemistry and Molecular Engineering, Peking University, Beijing 100871, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, Capital Normal University, Beijing 100048, China;Corresponding author"}], "References": [{"Title": "Design and synthesis of a stable multifunctional photoluminescence sensing material for rare earth ions from a 2D undulating Cd-coordination polymer", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "347", "Issue": "", "Page": "130641", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 119737993, "Title": "A lightweight deep learning model for real-time rectangle NdFeB surface defect detection with high accuracy on a global scale", "Abstract": "<p>To solve the problem that it is difficult to detect dynamic tiny square neodymium-iron-boron (NdFeB) surface defects in the case of limited computing resources, this paper proposes a square NdFeB magnet surface defect detection method based on the YOLO (YOLOv8-FCW) lightweight network. Initially, the lightweight global adaptive feature enhancement module (DFNet) network is used as the backbone feature extraction net-work. By customizing the depth of the feature matrix and reducing unnecessary branch structures, the model complexity is reduced while enhancing the network’s ability to extract multi-scale feature information. Subsequently, the deformable convolution module (DCNv3) is utilized to acquire twice downsampling feature maps without information loss, aiming to expand the receptive field for small-sized defects. Finally, to further improve detection accuracy, the Wise-IOU (WIOU) v3 bounding box loss function is introduced to focus on the samples that are difficult to identify and reduce the gradient penalty for low-quality samples. The experimental results show that the YOLOv8-FCW algorithm achieves a mean Average Precision (mAP@0.5) of 78.6% on the rectangle NdFeB magnet dataset, with a model parameter quantity and computational cost reduction of 33.2% and 24.7%, respectively compared with the baseline, and requires less computational resources for higher detection accuracy compared to other mainstream object detection algorithms. Finally, the model was deployed to industrial Automated Optical Inspection (AOI) devices using TensorRT. This deployment reduced the inference time for a single image to 2.7 ms and increased speed by 6.6 times, enabling dynamic micro-detection of surface defects in square NdFeB.</p>", "Keywords": "Defect detection; NdFeB magnet; YOLOv8; Lightweight network; DFNet; TensoRT", "DOI": "10.1007/s11554-024-01592-9", "PubYear": 2025, "Volume": "22", "Issue": "1", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Xiamen University of Technology, Xiamen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Xiamen University of Technology, Xiamen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Xiamen University of Technology, Xiamen, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Xiamen University of Technology, Xiamen, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "General Manager’s Office, Anjierui Robot Co., LTD, Xiamen, China"}], "References": [{"Title": "Surface defect saliency of magnetic tile", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "85", "JournalTitle": "The Visual Computer"}, {"Title": "Hybrid dilated multilayer faster RCNN for object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Hongguang Pan", "PubYear": 2024, "Volume": "40", "Issue": "1", "Page": "393", "JournalTitle": "The Visual Computer"}, {"Title": "Ghost-YOLO v8: An Attention-Guided Enhanced Small Target Detection Algorithm for Floating Litter on Water Surfaces", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "80", "Issue": "3", "Page": "3713", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 119738042, "Title": "RETRACTION : Model Innovation of Students' Mental Health Education from the Perspective of Big Data", "Abstract": "RETRACTION : <PERSON><PERSON> , <PERSON>,” Expert Systems 40 , no. ( 2023 ): , https://doi.org/10.1111/exsy.12948 . \nThe above article, published online on 11 February 2022 in Wiley Online Library ( wileyonlinelibrary.com ), has been retracted by agreement between the journal Editor‐in‐Chief, <PERSON>; and John Wiley & Sons Ltd. The article was submitted as part of a guest‐edited special issue. Following publication, it has come to our attention that the article was not reviewed in line with the journal's peer review standards. Moreover, multiple inconsistencies and flaws were identified in this article that affect the validity of the conclusions. Relevant information is missing so that the research described is not comprehensible.", "Keywords": "", "DOI": "10.1111/exsy.13814", "PubYear": 2025, "Volume": "42", "Issue": "2", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 119738049, "Title": "RETRACTION : DAE ‐ GAN : An Autoencoder Based Adversarial Network for Gaussian Denoising", "Abstract": "RETRACTION : <PERSON><PERSON> , <PERSON><PERSON> , <PERSON><PERSON> <PERSON><PERSON> , <PERSON><PERSON> , “,” Expert Systems (Early View): , https://doi.org/10.1111/exsy.12709 . \nThe above article, published online on 06 May 2021 in Wiley Online Library ( wileyonlinelibrary.com ), has been retracted by agreement between the journal Editor‐in‐Chief, <PERSON>; and John Wiley & Sons Ltd. The article was submitted as part of a guest‐edited special issue. The retraction has been agreed on as the article was not reviewed in line with the journal's peer review standards. Furthermore, the methodology and model in this manuscript are insufficiently described. Accordingly, the results are not considered reliable. The authors disagree with the retraction.", "Keywords": "", "DOI": "10.1111/exsy.13809", "PubYear": 2025, "Volume": "42", "Issue": "2", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 119738071, "Title": "The Risks of the U.S. Cyber Crisis", "Abstract": "In today’s interconnected world, the ubiquitous influence of digital technology emphasizes the critical need to confront the growing menace of cybercrime. The unrelenting rise of cyber-attacks in the United States poses substantial hazards to individuals, corporations, and nations, jeopardizing economic stability, security, and personal privacy. This study aims to draw awareness of the seriousness of cyber dangers and emphasize employing proactive steps to protect our digital future. This paper stresses the necessity of cybersecurity, digital literacy education, and cybercrime awareness in mitigating these widespread hazards through extensive analysis and empirical research. A more resilient and secure digital environment for current and future generations can be established by cultivating a collective understanding of cyber dangers and developing effective preventative measures.", "Keywords": "Cybersecurity;Digital Literacy Education;Cybercrime Awareness", "DOI": "10.4236/jis.2025.161003", "PubYear": 2025, "Volume": "16", "Issue": "1", "JournalId": 26129, "JournalTitle": "Journal of Information Security", "ISSN": "2153-1234", "EISSN": "2153-1242", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Crescenta Valley High School, La Crescenta, USA"}], "References": []}, {"ArticleId": 119738094, "Title": "Aitken optimizer: an efficient optimization algorithm based on the Aitken acceleration method", "Abstract": "<p>While the optimization algorithm community boasts numerous members, the primary wellspring of inspiration largely stems from collective and social behaviors observed in nature. This paper introduces a new metaphor-free meta-heuristic algorithm called Aitken optimizer (ATK) based on the basic idea of solving groups of equations using iterative methods. ATK is based on the Aitken sequence, which accelerates the immobile point iteration method to improve the algorithm's convergence speed and solution accuracy and reduce the possibility of falling into local optimums. Taking inspiration from the Aitken acceleration method, the ATK integrates two essential rules, namely the Aitken acceleration method search mechanism and the random weighted exponential operator, into its entire search process, which are instrumental in further enhancing the exploration of optimal solutions. ATK was validated using three CEC benchmarks (CEC2017, CEC2020, and CEC2022), and its results were compared with those of three categories of existing optimization algorithms, as follows: (1) the most cited optimizers, including the grey wolf optimizer, whale optimization algorithm, and Harris Hawks optimization, (2) published high-performance algorithms, including BWO, FHO, NRBO, SGA, SCA, CPSOGSA, and KOA, and (3) high-performance optimizers, such as SaDE, JaDE, CJADE, IMODE, EBOwithCMAR, SHADE, LSHADE, AL-SHADE, and LSHADE-cnEpSin. Statistical analysis shows that ATK achieves a winning rate of up to 94.23% across 51 functions in the three test sets compared to 10 competing optimizers. When benchmarked against 9 state-of-the-art high-performance optimizers, ATK's winning rates on the CEC 2017, CEC 2020, and CEC 2022 test suites are 27.59%, 25%, and 66.67%, respectively, with Friedman mean rankings of 2, 3, and 1. The results indicate that ATK, as a high-performance optimizer, demonstrates superior global search capabilities in high-dimensional, non-convex, and multimodal problems, outperforming simpler optimization methods suited for low-dimensional or convex problems. This makes ATK particularly well suited for applications such as engineering design and neural network tuning.</p>", "Keywords": "Aitken optimizer; Meta-heuristic algorithm; Engineering design problems; Neural networks; Data classification problem", "DOI": "10.1007/s11227-024-06709-2", "PubYear": 2025, "Volume": "81", "Issue": "1", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Manufacturing Technology, Ministry of Education, Guizhou University, Guiyang, China; Guizhou Equipment Manufacturing Digital Workshop Modeling and Simulation Engineering Research Center, Guiyang, China"}, {"AuthorId": 2, "Name": "Shengwei Fu", "Affiliation": "Key Laboratory of Advanced Manufacturing Technology, Ministry of Education, Guizhou University, Guiyang, China; Guizhou Equipment Manufacturing Digital Workshop Modeling and Simulation Engineering Research Center, Guiyang, China"}, {"AuthorId": 3, "Name": "Lang<PERSON> Zhang", "Affiliation": "Key Laboratory of Advanced Manufacturing Technology, Ministry of Education, Guizhou University, Guiyang, China; Guizhou Equipment Manufacturing Digital Workshop Modeling and Simulation Engineering Research Center, Guiyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Manufacturing Technology, Ministry of Education, Guizhou University, Guiyang, China; Information Engineering Institute, Chongqing Vocational and Technical University of Mechatronics, Chongqing, China; Guizhou Equipment Manufacturing Digital Workshop Modeling and Simulation Engineering Research Center, Guiyang, China; Corresponding author."}], "References": [{"Title": "Fitness-distance balance (FDB): A new selection method for meta-heuristic search algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "190", "Issue": "", "Page": "105169", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Balancing composite motion optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "520", "Issue": "", "Page": "250", "JournalTitle": "Information Sciences"}, {"Title": "Differential Evolution: A review of more than two decades of research", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103479", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A better balance in metaheuristic algorithms: Does it exist?", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100671", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A population based hybrid FCM-PSO algorithm for clustering analysis and segmentation of brain image", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114121", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Chaos Game Optimization: a novel metaheuristic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "2", "Page": "917", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "RUN beyond the metaphor: An efficient optimization algorithm based on Runge <PERSON> method", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "115079", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Improving Ant Colony Optimization efficiency for solving large TSP instances", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "108653", "JournalTitle": "Applied Soft Computing"}, {"Title": "Beluga whale optimization: A novel nature-inspired metaheuristic algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "251", "Issue": "", "Page": "109215", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Fire Hawk Optimizer: a novel metaheuristic algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "1", "Page": "287", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Special Relativity Search: A novel metaheuristic method based on special relativity physics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "257", "Issue": "", "Page": "109484", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An improved imperialist competition algorithm with adaptive differential mutation assimilation strategy for function optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118686", "JournalTitle": "Expert Systems with Applications"}, {"Title": "RIME: A physics-based optimization", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "532", "Issue": "", "Page": "183", "JournalTitle": "Neurocomputing"}, {"Title": "An adaptive position-guided gravitational search algorithm for function optimization and image threshold segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "121", "Issue": "", "Page": "106040", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "<PERSON><PERSON> optimization algorithm: A new metaheuristic algorithm inspired by <PERSON><PERSON>’s laws of planetary motion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "268", "Issue": "", "Page": "110454", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Development of the Natural Survivor Method (NSM) for designing an updating mechanism in metaheuristic search algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106121", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Rhizostoma optimization algorithm and its application in different real-world optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON>; Habiba A<PERSON>", "PubYear": 2023, "Volume": "13", "Issue": "4", "Page": "4317", "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)"}, {"Title": "Snow ablation optimizer: A novel metaheuristic technique for numerical optimization and engineering design", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "120069", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An exhaustive review of the metaheuristic algorithms for search and optimization: taxonomy, applications, and open challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "11", "Page": "13187", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Analysis of neural networks trained with evolutionary algorithms for the classification of breast cancer histological images", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "120609", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Fitness-Distance-Constraint (FDC) based guide selection method for constrained optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "110479", "JournalTitle": "Applied Soft Computing"}, {"Title": "Improved dwarf mongoose optimization algorithm using novel nonlinear control and exploration strategies", "Authors": "Shengwei Fu; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "233", "Issue": "", "Page": "120904", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Great Wall Construction Algorithm: A novel meta-heuristic algorithm for engineer problems", "Authors": "<PERSON><PERSON><PERSON>; Chang<PERSON> Ren; <PERSON><PERSON>", "PubYear": 2023, "Volume": "233", "Issue": "", "Page": "120905", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Optimizing the initial weights of a PID neural network controller for voltage stabilization of microgrids using a PEO-GA algorithm", "Authors": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "147", "Issue": "", "Page": "110771", "JournalTitle": "Applied Soft Computing"}, {"Title": "Advanced strategies on update mechanism of tree-seed algorithm for function optimization and engineering design problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "236", "Issue": "", "Page": "121312", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Crayfish optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "S2", "Page": "1919", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "RETRACTED: Optical microscope algorithm: A new metaheuristic inspired by microscope magnification for solving engineering optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "279", "Issue": "", "Page": "110939", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Triangulation topology aggregation optimizer: A novel mathematics-based meta-heuristic algorithm for continuous optimization and engineering applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121744", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Development of Intelligent Fault-Tolerant Control Systems with Machine Learning, Deep Learning, and Transfer Learning Algorithms: A Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121956", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A review of metaheuristic algorithms for solving TSP-based scheduling optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "110908", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Sinh Cosh optimizer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "282", "Issue": "", "Page": "111081", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Partial reinforcement optimizer: An evolutionary optimization algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "122070", "JournalTitle": "Expert Systems with Applications"}, {"Title": "<PERSON><PERSON>vy Arithmetic Algorithm: An enhanced metaheuristic algorithm and its application to engineering optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "241", "Issue": "", "Page": "122335", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An efficient manta ray foraging optimization algorithm with individual information interaction and fractional derivative mutation for solving complex function extremum and engineering design problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "150", "Issue": "", "Page": "111042", "JournalTitle": "Applied Soft Computing"}, {"Title": "Machine Learning and Genetic Algorithms: A case study on image reconstruction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "284", "Issue": "", "Page": "111194", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An effective memetic algorithm for distributed flexible job shop scheduling problem considering integrated sequencing flexibility", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "242", "Issue": "", "Page": "122734", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An effective metaheuristic technology of people duality psychological tendency and feedback mechanism-based Inherited Optimization Algorithm for solving engineering applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "244", "Issue": "", "Page": "122732", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Newton-Raphson-based optimizer: A new population-based metaheuristic algorithm for continuous optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "128", "Issue": "", "Page": "107532", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Crested Porcupine Optimizer: A new nature-inspired metaheuristic", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "284", "Issue": "", "Page": "111257", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Red-billed blue magpie optimizer: a novel metaheuristic algorithm for 2D/3D UAV path planning and engineering design problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "6", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 119738125, "Title": "Advanced multiple document summarization <i>via</i> iterative recursive transformer networks and multimodal transformer", "Abstract": "<p>The proliferation of digital information necessitates advanced techniques for multiple document summarization, capable of distilling vast textual data efficiently. Traditional approaches often struggle with coherence, integration of multimodal data, and suboptimal learning strategies. To address these challenges, this work introduces novel neural architectures and methodologies. At its core is recursive transformer networks (ReTran), merging recursive neural networks with transformer architectures for superior comprehension of textual dependencies, projecting a 5–10% improvement in ROUGE scores. Cross-modal summarization employs a multimodal transformer with cross-modal attention, amalgamating text, images, and metadata for more holistic summaries, expecting an 8 to 12% enhancement in quality metrics. Actor-critic reinforcement learning refines training by optimizing summary quality, surpassing Q-learning-based strategies by 5–8%. Meta-learning for zero-shot summarization addresses summarizing unseen domains, projecting a 6–10% uptick in performance. Knowledge-enhanced transformer integrates external knowledge for improved semantic coherence, potentially boosting ROUGE scores by 7 to 12%. These advancements not only improve numerical performance but also produce more informative and coherent summaries across diverse domains and modalities. This work represents a significant stride in multiple document summarization, setting a new benchmark for future research and applications.</p>", "Keywords": "Deep reinforcement learning;Multimodal summarization;Recursive transformer networks;Zero-shot learning", "DOI": "10.7717/peerj-cs.2463", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SCOPE, VIT-AP University, Amaravathi, Andhra Pradesh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "SCOPE, VIT-AP University, Amaravathi, Andhra Pradesh, India"}], "References": [{"Title": "Scientific document summarization in multi-objective clustering framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "2", "Page": "1520", "JournalTitle": "Applied Intelligence"}, {"Title": "A developed framework for multi-document summarization using softmax regression and spider monkey optimization methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "7", "Page": "3313", "JournalTitle": "Soft Computing"}, {"Title": "Abstractive document summarization via multi-template decoding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "9650", "JournalTitle": "Applied Intelligence"}, {"Title": "Feature based cluster ranking approach for single document summarization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "4", "Page": "2057", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Redundancy and coverage aware enriched dragonfly-FL single document summarization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "56", "Issue": "4", "Page": "1195", "JournalTitle": "Language Resources and Evaluation"}, {"Title": "Single document text summarization addressed with a cat swarm optimization approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "10", "Page": "12268", "JournalTitle": "Applied Intelligence"}, {"Title": "Multi-view multi-objective clustering-based framework for scientific document summarization using citation context", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "14", "Page": "18002", "JournalTitle": "Applied Intelligence"}, {"Title": "A sentence is known by the company it keeps: Improving Legal Document Summarization Using Deep Clustering", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "32", "Issue": "1", "Page": "165", "JournalTitle": "Artificial Intelligence and Law"}, {"Title": "Automatic multi-documents text summarization by a large-scale sparse multi-objective optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "4", "Page": "4629", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "CovSumm: an unsupervised transformer-cum-graph-based hybrid document summarization model for CORD-19", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "14", "Page": "16328", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "HierMDS: a hierarchical multi-document summarization model with global–local document dependencies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "25", "Page": "18553", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Graph-based extractive text summarization based on single document", "Authors": "<PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "7", "Page": "18987", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A binary grey wolf optimizer to solve the scientific document summarization problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "8", "Page": "23737", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Self-supervised opinion summarization with multi-modal knowledge graph", "Authors": "Lingyun Jin; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "62", "Issue": "1", "Page": "191", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Multi-language transfer learning for low-resource legal case summarization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "32", "Issue": "4", "Page": "1111", "JournalTitle": "Artificial Intelligence and Law"}, {"Title": "Effective Elytron Vespid-B rank BiLSTM classifier for Multi-Document Summarization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "18", "Page": "54125", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Large text document summarization based on an enhanced fuzzy logic approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Information Technology"}, {"Title": "A deep learning framework for multi-document summarization using LSTM with improved Dingo Optimizer (IDO)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "27", "Page": "69669", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 119738212, "Title": "Knowledge graph embeddings based on 2d convolution and self-attention mechanisms for link prediction", "Abstract": "<p>Link prediction refers to using existing facts in the knowledge graph to predict missing facts. This process can enhance the integrity of the knowledge graph and facilitate various downstream applications. However, existing link prediction models usually extract features only in a global or local scope, resulting in feature extraction being limited to a single scope. Additionally, to achieve optimal results, many models require increasing embedding dimensions and parameter numbers, which can lead to scalability issues when applied to large knowledge graphs. To address these issues, we propose a model that fuses the self-attention mechanism with 2D convolution for the link prediction task. The model utilizes a self-attention mechanism with numerous heads to capture feature interactions between entities and relations in the global scope. Furthermore, we innovatively introduce 2D convolution to capture feature interactions in the local scope. Results using FB15k-237 and WN18RR as standard link prediction benchmarks show that our fusion model has good comparable performance with current state-of-the-art models. In particular, compared to the ConvE model (which uses only 2D convolution), our proposed model achieves 13.7% and 14.7% improvement in MRR metrics, and compared to the SAttLE model (which uses only the self-attention mechanism) achieves 2.5% and 0.5% improvement in MRR metrics. Furthermore, due to the low-dimensional embedding of entities and relations, our proposed model has low complexity, good scalability, and thus can accomplish link prediction tasks on larger knowledge graphs in the real world.</p>", "Keywords": "Knowledge graph; Low-dimensional embedding; 2D convolution; Multi-head self-attention mechanism; Feature interaction; R-Drop structure", "DOI": "10.1007/s10489-024-05977-y", "PubYear": 2025, "Volume": "55", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Information Engineering, Harbin Normal University, Harbin, China"}, {"AuthorId": 2, "Name": "Weidong Ji", "Affiliation": "College of Computer Information Engineering, Harbin Normal University, Harbin, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Information Engineering, Harbin Normal University, Harbin, China"}], "References": [{"Title": "A review: Knowledge reasoning over knowledge graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112948", "JournalTitle": "Expert Systems with Applications"}, {"Title": "GRL: Knowledge graph completion with GAN-based reinforcement learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "209", "Issue": "", "Page": "106421", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "Hierarchical attentive knowledge graph embedding for personalized recommendation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "101071", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "DensE: An enhanced non-commutative representation for knowledge graph embedding with adaptive semantic hierarchy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "476", "Issue": "", "Page": "115", "JournalTitle": "Neurocomputing"}, {"Title": "A comprehensive overview of knowledge graph completion", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "255", "Issue": "", "Page": "109597", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Self-attention presents low-dimensional knowledge graph embeddings for link prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "260", "Issue": "", "Page": "110124", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A Comprehensive Survey on Automatic Knowledge Graph Construction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Relphormer: Relational Graph Transformer for Knowledge Graph Representations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "566", "Issue": "", "Page": "127044", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 119738242, "Title": "Recent advances in AI-powered multimedia visual computing and multimodal signal processing for metaverse era", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-20480-9", "PubYear": 2024, "Volume": "83", "Issue": "42", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [], "References": []}, {"ArticleId": 119738516, "Title": "A simple division-free algorithm for computing Pfaffians", "Abstract": "We present a very simple algorithm for computing Pfaffians which uses no division operations. Essentially, it amounts to iterating matrix multiplication and truncation. Its complexity, for a 2 n × 2 n matrix, is O ( n M ( n ) ) , where M ( n ) is the cost of matrix multiplication. In case of a sparse matrix, M ( n ) is the cost of the dense-sparse matrix multiplication. The algorithm is an adaptation of the Bird algorithm for determinants. We show how to extract, with practically no additional work, the characteristic polynomial and the <PERSON><PERSON><PERSON>ian characteristic polynomial from these algorithms.", "Keywords": "", "DOI": "10.1016/j.ipl.2024.106550", "PubYear": 2025, "Volume": "189", "Issue": "", "JournalId": 5748, "JournalTitle": "Information Processing Letters", "ISSN": "0020-0190", "EISSN": "1872-6119", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Warsaw University of Life Sciences—SGGW, Warsaw, Poland"}], "References": []}, {"ArticleId": 119738571, "Title": "Flap Endonuclease 1 (FEN1) biomarker as a new diagnostic target for the biosensing of cancer", "Abstract": "Flap endonuclease 1 (FEN1) is a structure-specific nuclease that plays a role in DNA replication and genome stability. FEN1 is overexpressed in proliferative cancers and correlates with the grade and aggressiveness of the cancer, suggesting that it could potentially be considered a promising biomarker for various cancer types. Due to the initiation of cancer is related to the lack of the activity of FEN1 nuclease, the detection and quantification of FEN1 is significance for cancer diagnosis in early stage. There are various approaches to detect the different types of cancer biomarkers. In this context, biosensing platforms and nanotechnology-based methods have attracted much attention. To date, the sensitivity and performance of biosensors can enhance by nanomaterials/nanoparticles that have been incorporated in biosensing platforms. This review overview a brief classification and description of the advancement of biosensors and nanobiosensors for the detection and quantitation of FEN1 based on optical and electrochemical platforms.", "Keywords": "Flap endonuclease 1 (FEN1); Biosensor; Nanosensor; Cancer biomarker; CRISPR/Cas12a", "DOI": "10.1016/j.snr.2024.100270", "PubYear": 2025, "Volume": "9", "Issue": "", "JournalId": 72042, "JournalTitle": "Sensors and Actuators Reports", "ISSN": "2666-0539", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Molecular Medicine, Paul <PERSON> Center for Immune Intervention, University Medical Center of the Johannes G<PERSON>nberg-University Mainz, Mainz, Germany;Department of Pharmaceutical Biotechnology, School of Pharmacy, Guilan University of Medical Sciences, Rasht, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmaceutical Biotechnology, School of Pharmacy, Guilan University of Medical Sciences, Rasht, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pharmacology and Toxicology, School of Pharmacy, Guilan University of Medical Sciences, Rasht, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cellular and Molecular Research Center, School of Medicine, Guilan University of Medical Sciences, Rasht, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pharmaceutical Biotechnology, School of Pharmacy, Guilan University of Medical Sciences, Rasht, Iran;Cellular and Molecular Research Center, School of Medicine, Guilan University of Medical Sciences, Rasht, Iran"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Cellular and Molecular Research Center, School of Medicine, Guilan University of Medical Sciences, Rasht, Iran;Student Research Committee, School of Medicine, Guilan University of Medical Sciences, Rasht, Iran;Corresponding author at: Cellular and Molecular Research Center, School of Medicine, Guilan University of Medical Sciences, Rasht, Iran"}], "References": [{"Title": "Ultrasensitive electrochemiluminescence aptasensor for kanamycin detection based on silver nanoparticle-catalyzed chemiluminescent reaction between luminol and hydrogen peroxide", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127367", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Exponential amplification reaction-based fluorescent sensor for the sensitive detection of tumor biomarker flap endonuclease 1", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "346", "Issue": "", "Page": "130457", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A magnetic separation-assisted cascade hybridization chain reaction amplification strategy for sensitive detection of flap endonuclease 1", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "353", "Issue": "", "Page": "131147", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Ultrasensitive turn-on detection of biomarker FEN1 using a CRISPR/Cas13a system integrated with a cleavage-ligation-activation cascade", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "393", "Issue": "", "Page": "134265", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Dual signal amplification-integrated single-molecule biosensing of flap endonuclease 1 in breast cancer tissues", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "394", "Issue": "", "Page": "134383", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "An ultrasensitive biosensor with suppressed background signals for FEN1 detection in a homogeneous reaction via cascade primer exchange reaction and CRISPR/Cas12a system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "403", "Issue": "", "Page": "135194", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "An ultrasensitive electrochemical biosensor based on logic transcription circuit-activated DNAzyme amplifier for flap endonuclease 1 activity detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "415", "Issue": "", "Page": "135986", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 119738645, "Title": "Educational Cyber–Physical Systems (ECPSs) for University 4.0", "Abstract": "<p>University 4.0 represents the adaptation of the Education 4.0 paradigm to the university context. The core principle is the automated supervision of the entire student learning process by an AI-driven computer assistant, allowing for timely adjustments based on the student’s progression. Critical to this process is the assistant’s ability to collect comprehensive information on all student activities within the curriculum, particularly overseeing pedagogical activities in real time to make necessary adaptations. This utilizes Educational Cyber–Physical Systems (ECPSs) to gather all relevant data and extract appropriate information effectively. This article examines a specific case of practical work involving students at two distinct geographical locations collaborating in a blended learning environment. A specialized ECPS is deployed to collect data from equipment at both sites, enabling the modeling of the pedagogical sequence, the cyber–physical system, and the data necessary for monitoring student progress in real time.</p>", "Keywords": "", "DOI": "10.3390/info15120790", "PubYear": 2024, "Volume": "15", "Issue": "12", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universite de Pau et des Pays de l’Adour, E2S, LIUPPA, 40000 Mont-de-Marsan, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, American University of Ras Al Khaimah, Ras Al Khaimah 72603, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universite de Pau et des Pays de l’Adour, E2S, LIUPPA, 64600 Anglet, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Research and Innovation Department, Capgemini Engineering, 92190 Meudon, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Indicatic-AIP, National Institute on ICT, Panamà City 0801, Panama"}], "References": []}, {"ArticleId": 119738646, "Title": "Mechanism of trace-level detection of dibutyl sulfide (DBS) at room temperature using UV-activated MoO3 coated quartz crystal microbalance (QCM) sensor", "Abstract": "Use of chemical warfare agents (CWAs) by terrorists poses significant challenges for law enforcement, emergency responders, and public health authorities. Timely detection of CWAs assumes importance for enhancing preparedness, response, and recovery capabilities. Mustard gas, or sulfur mustard, is a highly dangerous CWA due to its high toxicity, persistence, potential for secondary contamination, and weaponization. Simulants are essential in developing sensors for detecting CWAs, as handling real agents is dangerous outside controlled conditions. One simulant for mustard gas is dibutyl sulfide (DBS). The present study describes the development of a highly efficient MoO<sub>3</sub> thin film-based gas sensor for the trace-level detection of DBS at room temperature. A thin film of MoO<sub>3</sub> was coated on Quartz Crystal Microbalance (QCM) using the RF magnetron sputtering technique. In the presence of ultraviolet light (∼ 355 nm), the sensor exhibited a high sensitivity of 0.4 Hz/ppm in the range of 2 – 400 ppm DBS with a quick response time (∼7 s) and recovery time (∼15 s) towards 2 ppm DBS at room temperature. The MoO<sub>3</sub> sensor also demonstrated high selectivity and stable reproducibility. Hence in the current investigation, the detailed DBS sensing mechanism for RF sputtered MoO<sub>3</sub> thin film is also discussed.", "Keywords": "", "DOI": "10.1016/j.snb.2024.137102", "PubYear": 2025, "Volume": "426", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics and Astrophysics, University of Delhi, Delhi 110 007, India;Department of Physics, <PERSON><PERSON> (University of Delhi), Kalkaji, Govindpuri, New Delhi 110 019, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, At<PERSON> Ram <PERSON> Dharma College (University of Delhi), <PERSON><PERSON><PERSON>, New Delhi 110 021, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Miranda House (University of Delhi), Delhi 110 007, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Miranda House (University of Delhi), Delhi 110 007, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, <PERSON><PERSON> (University of Delhi), Kalkaji, Govindpuri, New Delhi 110 019, India;Correspondence to: Sensing Materials & Devices Laboratory, <PERSON><PERSON> (University of Delhi), Kalkaji, Govindpuri, New Delhi 110 019, India.; Corresponding author"}], "References": [{"Title": "MoO3/Ti3C2Tx MXene nanocomposites with rapid response for enhanced ethanol-sensing at a low temperature", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "378", "Issue": "", "Page": "133216", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Porous MoO3 nanosheets for conductometric gas sensors to detect diisopropylamine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "382", "Issue": "", "Page": "133472", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Insitu controllable synthesis of MoO3 nanoflakes and its temperature-dependent dual selectivity for detection of ethanol and isopropanol", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "408", "Issue": "", "Page": "135548", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 119738657, "Title": "Artificial Intelligence in Quality Control Systems: A Cross-Industry Analysis of Applications, Benefits, and Implementation Frameworks", "Abstract": "This article presents a comprehensive analysis of artificial intelligence applications in quality control across manufacturing, service, and infrastructure maintenance sectors. The article examines how AI-driven systems are transforming traditional quality control processes through automated defect detection, real-time monitoring, and adaptive testing methodologies. Through systematic review of industry implementations and case studies, we investigate the impact of machine learning algorithms, computer vision systems, and deep learning applications on quality assurance processes. The findings demonstrate significant improvements in inspection accuracy, reduction in manual inspection requirements, and enhanced detection of subtle defects across various industrial applications. The article reveals that AI-driven quality control systems offer substantial benefits in terms of operational efficiency, cost reduction, and quality consistency, while also identifying key implementation challenges such as initial infrastructure requirements, data quality concerns, and workforce adaptation needs. Additionally, the article provides insights into emerging trends and future opportunities for AI integration in quality control systems, contributing to the broader understanding of Industry 4.0 implementation strategies. This work serves as a foundational reference for organizations considering AI implementation in their quality control processes and provides a framework for evaluating the potential benefits and challenges across different industrial contexts.", "Keywords": "Artificial Intelligence Quality Control;Machine Learning Inspection Systems;Industrial Process Automation;Smart Manufacturing Analytics;Quality Management Digitalization", "DOI": "10.32628/CSEIT241061162", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Southern California, USA"}], "References": []}, {"ArticleId": 119738758, "Title": "The IDL tool suite: Specifying and analyzing inter-parameter dependencies in web APIs", "Abstract": "Web APIs may include inter-parameter dependencies that limit how input parameters can be combined to call services correctly. These dependencies are extremely common, appearing in 4 out of every 5 APIs. This paper presents the IDL tool suite, a set of software tools for managing inter-parameter dependencies in web APIs. The suite includes a specification language (IDL), an OpenAPI Specification extension (IDL4OAS), an analysis engine (IDLReasoner), a web API, a playground, an AI chatbot, and a website. We also highlight several contributions by different groups of authors where the IDL tool suite has proven useful in the domains of automated testing, code generation, and API gateways. To date, the IDL tool suite has contributed to the detection of more than 200 bugs in industrial APIs, including GitHub, Spotify, and YouTube, among others. Also, IDL has been used to boost automated code generation, generating up to 10 times more code than state-of-the-art generators for web APIs.", "Keywords": "Web API; REST; OpenAPI specification; IDL", "DOI": "10.1016/j.softx.2024.101998", "PubYear": 2025, "Volume": "29", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "SCORE Lab, I3US Institute, Universidad de Sevilla, Seville, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Software Institute, Università della Svizzera italiana, Lugano, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "SCORE Lab, I3US Institute, Universidad de Sevilla, Seville, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "SCORE Lab, I3US Institute, Universidad de Sevilla, Seville, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "SCORE Lab, I3US Institute, Universidad de Sevilla, Seville, Spain"}], "References": [{"Title": "Specification and Automated Analysis of Inter-Parameter Dependencies in Web APIs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "2342", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 119738796, "Title": "CSAT: Configuration structure-aware tuning for highly configurable software systems", "Abstract": "Many modern software systems provide numerous configuration options with a large parameter space that users can adjust for specific running environments. However, configuring such systems always incurs an undue burden on users due to the lack of domain knowledge to understand complex interactions between the performance and the parameters. To address this issue, various tuning techniques have been developed to automatically determine the optimal configuration by either directly searching the configuration space or learning a surrogate model to guide the exploration process. Most previous studies only apply simple search strategies to explore the complex configuration space, which often leads to fruitless attempts in suboptimal areas. Inspired by previous studies, we define configuration structures to describe the positions of various configurations in the performance space of software systems. This idea leads to the design of a novel Configuration Structure-Aware Tuning (CSAT) algorithm. CSAT constructs a structure model for system configurations using the framework of Adaptive Network-based Fuzzy Inference System (ANFIS), learns a comparison-based distribution model through Gaussian Process Regression (GPR), and uses Bayesian Inference to generate potentially promising configurations based on the structure. The experimental results demonstrate that in terms of tuning performance, on average, CSAT outperforms default configurations by 65.51% and outperforms six state-of-the-art tuning algorithms by 22.10%–33.20%. In terms of handling internal constraints, CSAT achieves an average probability of 0.767 in generating valid configurations.", "Keywords": "", "DOI": "10.1016/j.jss.2024.112316", "PubYear": 2025, "Volume": "222", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi’an, 710071, Shaanxi, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi’an, 710071, Shaanxi, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi’an, 710071, Shaanxi, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Data Science, New Jersey Institute of Technology, Newark, 07102, NJ, USA"}], "References": [{"Title": "Learning software configuration spaces: A systematic literature review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "111044", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 119738821, "Title": "MultiGranDTI: an explainable multi-granularity representation framework for drug-target interaction prediction", "Abstract": "<p>Drug-target interaction (DTI) prediction is a tough task with critical applications in drug repurposing and design scenarios, as it significantly reduces resource consumption and accelerates the drug discovery process. With the proliferation of experimentally measured pharmaceutical data and increasingly complex drug-target interactions, deep DTI approaches are becoming increasingly competitive due to their ability to utilize large-scale data in an end-to-end manner. It is fascinating to consider how to consolidate drug-target pair representations at different granularities to enhance deep DTI models with limited performance. The employed models typically involve solely single granular information and thus lead to a significant lack of features. Motivated by this concern, this study proposes an explainable multi-granularity representation framework for DTI prediction (MultiGranDTI). First, a hierarchical network with constraint is devised to enable the natural conversion of drug representations with different granularities, effectively integrating atomic and substructural information. Second, the 1st-order and 2nd-order sequence information of the target protein is modeled and then encoded together with the spatial information. Subsequently, several convolution layers further extract various levels of protein features. Finally, the drug and protein features are concatenated and fed into regular dense layers for interaction prediction. Comprehensive experiments reveal that MultiGranDTI achieves competitive performance in two types of tasks on four benchmark datasets. Additionally, a visualization case shows that our method is capable of efficiently identifying particular functional groups and substructures in molecules and providing a plausible explanation for the predicted results.</p><p>A novel MultiGranDTI model to boost DTI prediction by comprehensively mining the multi-granularity information of compounds and proteins</p>", "Keywords": "Drug design; Multi-granularity; Hierarchical learning; Graph neural network; Interpretability", "DOI": "10.1007/s10489-024-05936-7", "PubYear": 2025, "Volume": "55", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Nuffield Department of Clinical Neurosciences, University of Oxford, Oxford, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The Hong Kong University of Science and Technology, Hong Kong, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China"}], "References": [{"Title": "A Survey of Deep Learning and Its Applications: A New Paradigm to Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "4", "Page": "1071", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Predicting drug–protein interaction using quasi-visual question\nanswering system", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "2", "Page": "134", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "TransformerCPI: improving compound–protein interaction prediction by sequence-based deep learning with self-attention mechanism and label reversal experiments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "16", "Page": "4406", "JournalTitle": "Bioinformatics"}, {"Title": "GRaSP: a graph-based residue neighborhood strategy to predict binding sites", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "Supplement_2", "Page": "i726", "JournalTitle": "Bioinformatics"}, {"Title": "GraphDTA: predicting drug–target binding affinity with graph neural networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "8", "Page": "1140", "JournalTitle": "Bioinformatics"}, {"Title": "KG-DTI: a knowledge graph based deep learning method for drug-target interaction predictions and Alzheimer’s disease drug repositions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "1", "Page": "846", "JournalTitle": "Applied Intelligence"}, {"Title": "Geometric deep learning on molecular representations", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "12", "Page": "1023", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Molecular contrastive learning of representations via graph neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "3", "Page": "279", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Fuzzy hierarchical network embedding fusing structural and neighbor information", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "603", "Issue": "", "Page": "130", "JournalTitle": "Information Sciences"}, {"Title": "Effective drug–target interaction prediction with mutual interaction neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "14", "Page": "3582", "JournalTitle": "Bioinformatics"}, {"Title": "MultiGran-SMILES: multi-granularity SMILES learning for molecular property prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "19", "Page": "4573", "JournalTitle": "Bioinformatics"}, {"Title": "Knowledge graph-enhanced molecular contrastive learning with functional prompt", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "5", "Page": "542", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": *********, "Title": "A Python code for simulations of RHEED intensity oscillations within the one-dimensional dynamical approximation", "Abstract": "We present a Python-based implementation of a practical procedure of construction of simulation program, which facilitates the calculation of changes to the intensity of RHEED oscillations in the function of the glancing angle of incidence of the electron beam, employing various models of crystal potential for heteroepitaxial structures including the possible existence of various diffuse scattering models through the layer parallel to the surface. The calculations are based on the use of a one-dimensional dynamical diffraction theory. Although this theory has some limitations, in practice it is useful under so-called one-beam condition. Computation performance has been improved by using Numba as an open source, NumPy -aware optimising compiler for Python. <b >Program Summary</b> Program Title: PY_RHEED_DIFF CPC Library link to program files: https://doi.org/10.17632/j6jxt9yr3b.1 Licensing provisions: GNU General Public License 3 Programming language: Python 3.12.7 Journal reference of previous version: Computer Physics Communications 185 (2014) 3001–3009 Does the new version supersede the previous version?: Yes. Reasons for the new version: Python, as a powerful, accessible and general-purpose programming language, has gained tremendous popularity in recent years. Python is characterised by a remarkable simplicity that makes it an ideal choice for users for whom knowledge of high-level programming techniques is not the most important in research work. According to users’ suggestions we have developed a Python-based implementation of generic computational model for simulations of changes to the intensity of RHEED oscillations in the function of the glancing angle of incidence of the electron beam, employing various models of crystal potential for heteroepitaxial structures including the possible existence of various diffuse scattering models through the layer parallel to the surface. This version implements improvements for ergonomics, computational performances, readability, and code functionality by adding new capabilities which make the output data generation and visualisation process much more efficient compared to the previous version.", "Keywords": "", "DOI": "10.1016/j.cpc.2024.109467", "PubYear": 2025, "Volume": "308", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computer Science, Maria Curie-Skłodowska University, ul. Akademicka 9, Lublin 20-033, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computer Science, Maria Curie-Skłodowska University, ul. Akademicka 9, Lublin 20-033, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computer Science, Maria Curie-Skłodowska University, ul. Akademicka 9, Lublin 20-033, Poland"}], "References": []}, {"ArticleId": 119738883, "Title": "A mixture learning strategy for predicting aquifer permeability coefficient K", "Abstract": "Aquifers permeability coefficient (K) is critical for understanding, managing, and protecting groundwater resources. However, obtaining reliable K values directly from pumping tests is costly and time-consuming, often yielding suboptimal results that lead to significant financial losses. Recent advances in machine learning offer an alternative, cost-effective approach for estimating K. Yet, the primary challenge lies in the substantial proportion of missing K data, as K measurements can only be recorded in aquifer layers. Such sparse and incomplete data severely limit the effectiveness of classical supervised learning methods. To address this challenge, we propose a mixture learning strategy (MXS) that combines unsupervised and supervised techniques to improve K prediction. First, a K-Means clustering approach is applied to delineate a naïve group of aquifers (NGA), effectively generating proxy labels for layers where direct K measurements are unavailable. Next, these NGA labels are integrated with existing K values to form enhanced input features for supervised prediction. We then apply support vector machines (SVMs) and extreme gradient boosting (XGB) to predict K more accurately. Experimental results show that both SVMs and XGB achieve prediction accuracies exceeding 80% when evaluated using confusion matrices and micro- and macro-averaged precision-recall metrics. Testing the MXS approach on an independent borehole dataset confirms its robustness and effectiveness. By enabling accurate K predictions in the presence of significant data gaps, MXS supports more informed decision-making, reduces the likelihood of unsuccessful pumping tests, and aids in the sustainable planning and management of groundwater resources.", "Keywords": "", "DOI": "10.1016/j.cageo.2024.105819", "PubYear": 2025, "Volume": "196", "Issue": "", "JournalId": 4833, "JournalTitle": "Computers & Geosciences", "ISSN": "0098-3004", "EISSN": "1873-7803", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Geosciences and Info-physics, Central South University, Changsha, Hunan, 410083, China;Hunan Key Laboratory of Nonferrous Resources and Geological Hazards Exploration, Changsha, Hunan, 410083, China;UFR des Sciences de la Terre et des Ressources Minières, Université Félix Houphouët-Boigny, Abidjan, 22 BP 582 Abidjan 22, the Republic of Côte d'Ivoire"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Geosciences and Info-physics, Central South University, Changsha, Hunan, 410083, China;Hunan Key Laboratory of Nonferrous Resources and Geological Hazards Exploration, Changsha, Hunan, 410083, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Geosciences and Info-physics, Central South University, Changsha, Hunan, 410083, China;Guangdong Geological Bureau, Guangzhou, Guangdong, 510700, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Geosciences and Info-physics, Central South University, Changsha, Hunan, 410083, China;Hunan Key Laboratory of Nonferrous Resources and Geological Hazards Exploration, Changsha, Hunan, 410083, China;Corresponding author. School of Geosciences and Info-physics, Central South University, Changsha, Hunan, 410083, China"}], "References": [{"Title": "A machine learning methodology for multivariate pore-pressure prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "104548", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 119738890, "Title": "Adaptive density-based clustering for many objective similarity or redundancy evolutionary optimisation", "Abstract": "With the increase in the number of objectives, the curse of dimensionality will eventually occur in some practical multi-objective optimization problems. This situation will become even worse when the multi-objective changes into many-objective optimization problems (MaOPs) with more than 15 objectives, which makes it more difficult for evolutionary computing. The persistent focus within the field has been on how to reduce the scale of problem-solving and alleviate the complexity of problems by analyzing the linear or nonlinear relationships between various objectives. Traditionally, the objectives relationships are typically presumed to be in conflict with each other, and yet similarity or redundancy may exist in some MaOPS. In this study, an adaptive evolutionary many-objective optimization algorithm with similarity or redundancy reduction based on adaptive density clustering denoted as AENec is proposed for MaOPs. In the AENec, the similarity or redundancy between objectives is analyzed for MaOPs for the first time and the redundance analyses method for objectives(RAMO) is designed with the limited Pareto front structure information during classic MaOPs evolutionary optimization(CMEVO). An adaptive density-based clustering algorithm (AEDBSCAN) is designed to configure the number of adaptive density clusters and automatically determine the extent of objective reduction. In addition, to avoid inconsistencies between the Pareto solutions post-reduction and the original MaOPs, the efficient non-redundant objective aggregation function (NROAF) is also devised. The parameters and algorithmic components of AENec are successfully analyzed on random test cases. The statistical results demonstrate that the proposed method outperforms other competitive algorithms on most test benchmarks and also especially verifies its effectiveness on MaOPs with similar or redundant objective relationships.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126060", "PubYear": 2025, "Volume": "266", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science and Artificial Intelligence, Wenzhou University of Technology, Wenzhou, 325000, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Surveying and Geospatial Engineering, College of Engineering, University of Tehran, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Southeast University, Nanjing, 211189, China;The Key Laboratory of Computer Network and Information Integration (Southeast University), Ministry of Education, 211189, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science and Artificial Intelligence, Wenzhou University of Technology, Wenzhou, 325000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science and Artificial Intelligence, Wenzhou University of Technology, Wenzhou, 325000, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Chinese Academy of Sciences Key Laboratory of Molecular Imaging, Institute of Automation, Beijing 100190, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> Chen", "Affiliation": "College of Computer Science and Artificial Intelligence, Wenzhou University, Wenzhou, Zhejiang 325035, China;Corresponding author"}], "References": [{"Title": "An adaptive penalty-based boundary intersection method for many-objective optimization problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "356", "JournalTitle": "Information Sciences"}, {"Title": "Objective reduction for visualising many-objective solution sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "278", "JournalTitle": "Information Sciences"}, {"Title": "A many-objective particle swarm optimizer based on indicator and direction vectors for many-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "514", "Issue": "", "Page": "166", "JournalTitle": "Information Sciences"}, {"Title": "Enhancing MOEA/D with information feedback models for large-scale many-objective optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "522", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "DLEA: A dynamic learning evolution algorithm for many-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "567", "JournalTitle": "Information Sciences"}, {"Title": "BIRCHSCAN: A sampling method for applying DBSCAN to large datasets", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115518", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An objective reduction method based on advanced clustering for many-objective optimization problems and its human-computer interaction visualization of pareto front", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "93", "Issue": "", "Page": "107266", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Two-type weight adjustments in MOEA/D for highly constrained many-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "592", "JournalTitle": "Information Sciences"}, {"Title": "Evolutionary multi and many-objective optimization via clustering for environmental selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "930", "JournalTitle": "Information Sciences"}, {"Title": "An efficient slime mould algorithm for solving multi-objective optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115870", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An improved competitive particle swarm optimization for many-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "189", "Issue": "", "Page": "116118", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A survey of recommender systems with multi-objective optimization", "Authors": "<PERSON>; <PERSON> (<PERSON><PERSON><PERSON>) <PERSON>", "PubYear": 2022, "Volume": "474", "Issue": "", "Page": "141", "JournalTitle": "Neurocomputing"}, {"Title": "Towards fast approximations for the hypervolume indicator for multi-objective optimization problems by Genetic Programming", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "125", "Issue": "", "Page": "109103", "JournalTitle": "Applied Soft Computing"}, {"Title": "A new two-stage based evolutionary algorithm for solving multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "611", "Issue": "", "Page": "649", "JournalTitle": "Information Sciences"}, {"Title": "ACDB-EA: Adaptive convergence-diversity balanced evolutionary algorithm for many-objective optimization", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101145", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A new adaptive decomposition-based evolutionary algorithm for multi- and many-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119080", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel approach of many-objective particle swarm optimization with cooperative agents based on an inverted generational distance indicator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "623", "Issue": "", "Page": "220", "JournalTitle": "Information Sciences"}, {"Title": "An improved two-archive artificial bee colony algorithm for many-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "236", "Issue": "", "Page": "121281", "JournalTitle": "Expert Systems with Applications"}, {"Title": "High-dimensional interactive adaptive RVEA for multi-objective optimization of polyester polymerization process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "650", "Issue": "", "Page": "119707", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 119738895, "Title": "Towards staircase navigation and maintenance using self-reconfigurable service robot", "Abstract": "The staircase is a fundamental component of buildings that requires proper cleaning and maintenance. This paper illustrates the design and navigation framework of the reconfigurable staircase-service robot called sTetro-C. This novel robotic platform has integrated a robust intra-reconfigurable locomotion system with a stable lifting mechanism and a wet cleaning module to enable smooth navigation on both flat and uneven terrain of the building floor and stairs. Moreover, the staircase climbing framework by sTetro-C utilizes a time-of-flight sensor, a LiDAR sensor, and an RGBD RealSense camera for map building and localization. The control system enables the robot to identify, navigate, and move efficiently on the staircase. The mechanical and electronics systems are also presented with detailed stability, force and kinematic analysis of the lifting and wheels systems, respectively. Simulation results in Gazebo, along with experimental trials in a real environment on the building’s staircase, have demonstrated improved stability of the lifting system and enhanced locomotion of the sTetro-C.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125969", "PubYear": 2025, "Volume": "266", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Communication and Signal Processing Research Group, Faculty of Electrical and Electronics Engineering, Ton Duc Thang University, Ho Chi Minh City, Viet Nam;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Engineering Product Development Pillar, Singapore University of Technology and Design, 8 Somapah Rd, Singapore 487372, Singapore"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Engineering Product Development Pillar, Singapore University of Technology and Design, 8 Somapah Rd, Singapore 487372, Singapore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Columbia University, New York, NY, USA"}, {"AuthorId": 5, "Name": "Phone Thiha Kyaw", "Affiliation": "Engineering Product Development Pillar, Singapore University of Technology and Design, 8 Somapah Rd, Singapore 487372, Singapore"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering and Technology, Nguyen Tat Thanh University, 300A - Ng<PERSON>en <PERSON>t Thanh, Ward 13, District 4, Ho Chi Minh City, Viet Nam"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "HUTECH Institute of Engineering, HUTECH University, Ho Chi Minh City, Viet Nam"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Engineering Product Development Pillar, Singapore University of Technology and Design, 8 Somapah Rd, Singapore 487372, Singapore"}], "References": [{"Title": "Autonomous Floor and Staircase Cleaning Framework by Reconfigurable sTetro Robot with Perception Sensors", "Authors": "<PERSON><PERSON>; <PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Towards optimal hydro-blasting in reconfigurable climbing system for corroded ship hull cleaning and maintenance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Phone Thiha Kyaw", "PubYear": 2021, "Volume": "170", "Issue": "", "Page": "114519", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Heat conduction combined grid-based optimization method for reconfigurable pavement sweeping robot path planning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "152", "Issue": "", "Page": "104063", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Long-term trials for improvement of autonomous area coverage with a Tetris inspired tiling self-reconfigurable system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "206", "Issue": "", "Page": "117810", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Complete coverage path planning for reconfigurable omni-directional mobile robots with varying width using GBNN( n )", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "228", "Issue": "", "Page": "120349", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 119739153, "Title": "Graph-based referring expression comprehension with expression-guided selective filtering and noun-oriented reasoning", "Abstract": "The objective of referring expression comprehension (REC) is to find the common feature domain between language expressions and visual objects. Due to the complex nature of modeling relationships between objects in images, graph-based methods are widely used for the REC task. However, during the process of graph construction, existing graph-based REC methods insufficiently harness the visual information associated with objects in images. Moreover, in modeling the relationships between objects, these methods consider only the relational words of the expression and the positions of the objects, while ignoring the objects themselves. Thus, they are sub-optimal in capturing underlying relationships between the objects and the expression, leading to incorrect predictions when given a complex expression. To address these issues, we propose a plug-and-adapt module called expression-guided selective and filtering module (EGSFM) for graph-based REC methods that constructs an expression-guided filter to adaptively select relevant and important visual features from feature maps of objects. Then, the selected visual object features and the textual features of the expression are jointly used for graph construction. Finally, a noun-oriented reasoning strategy is proposed for graph reasoning and target object matching, with the number of reasoning steps based on the number of nouns or noun phrases in the expression. Extensive experimental results on three challenging public datasets, including RefCOCO, RefCOCO+, and RefCOCOg, show that our method outperforms the compared graph-based methods and is robust to complex language expressions. In addition, our method performs favorably against other state-of-the-art transformer-based methods while consuming much fewer computational resources for training than those methods.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.111222", "PubYear": 2025, "Volume": "161", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "Jingcheng Ke", "Affiliation": "School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, China;This is the first author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Data Science, City University of Macau, Macao Special Administrative Region of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Medical Information Engineering, Guangdong Pharmaceutical University, Guangzhou, China;Corresponding author"}, {"AuthorId": 4, "Name": "Hongqing Ding", "Affiliation": "China Mobile Communications Group Co., Ltd, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "China Mobile Communications Group Co., Ltd, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Shenzhen, China"}], "References": [{"Title": "Visual-to-EEG cross-modal knowledge distillation for continuous emotion recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "108833", "JournalTitle": "Pattern Recognition"}, {"Title": "VLCDoC: Vision-Language contrastive pre-training model for cross-Modal document classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "109419", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep image compression using scene text quality assessment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "109696", "JournalTitle": "Pattern Recognition"}, {"Title": "Encoder–decoder cycle for visual question answering based on perception-action cycle", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "109848", "JournalTitle": "Pattern Recognition"}, {"Title": "Automatic Tracking Method for 3D Human Motion Pose Using Contrastive Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "24", "Issue": "3", "Page": "", "JournalTitle": "International Journal of Image and Graphics"}, {"Title": "CAST: Cross-Modal Retrieval and Visual Conditioning for image captioning", "Authors": "<PERSON> Cao; <PERSON><PERSON> An; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "153", "Issue": "", "Page": "110555", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 119739419, "Title": "Manipulator Trajectory Planning Based on Clustering Curve Discretization and B‐Spline", "Abstract": "", "Keywords": "", "DOI": "10.1002/rob.22485", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "Yanhua Peng", "Affiliation": "School of Mechanical and Electrical Engineering Guilin University of Electronic Technology  Guilin China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering Guilin University of Electronic Technology  Guilin China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering Guilin University of Electronic Technology  Guilin China"}, {"AuthorId": 4, "Name": "Yi<PERSON>g Wei", "Affiliation": "School of Mechanical and Electrical Engineering Guilin University of Electronic Technology  Guilin China"}], "References": [{"Title": "An error-bounded B-spline curve approximation scheme using dominant points for CNC interpolation of micro-line toolpath", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "101930", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A review of vision-aided robotic welding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "103326", "JournalTitle": "Computers in Industry"}, {"Title": "Time-optimal trajectory planning of manipulator with simultaneously searching the optimal path", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "181", "Issue": "", "Page": "446", "JournalTitle": "Computer Communications"}, {"Title": "2D NURBS Curve discretization method with double constraints of area and chord error and contour accuracy prediction modeling", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "102682", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Towards industrial robots as a service (IRaaS): Flexibility, usability, safety and business models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "81", "Issue": "", "Page": "102484", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 119739430, "Title": "On Collaboration and Automation in the Context of Threat Detection and Response with Privacy-Preserving Features", "Abstract": "<p>Organizations and their security operation centers often struggle to detect and respond effectively to an extensive quantity of ever-evolving cyberattacks. While collaboration, such as threat intelligence sharing between security teams, and response automation are often discussed in the cybersecurity community, issues like data sensitivity and confidence in detection may hinder their adoption. This work investigates the potentials and challenges of collaboration and automation to enhance incident response processes. We propose a reference architecture for data sharing in threat detection and response, aiming to boost collaborative and automated efforts across organizations while also considering privacy-preserving features. To address these challenges and potentials, we discuss how such a framework could enhance current response processes within and between organizations, validated with results in local attack detection, incident response, and data sharing.</p>", "Keywords": "", "DOI": "10.1145/3707651", "PubYear": 2025, "Volume": "6", "Issue": "1", "JournalId": 74064, "JournalTitle": "Digital Threats: Research and Practice", "ISSN": "2576-5337", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Fraunhofer FIT, Sankt Augustin, Germany and RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Fraunhofer FIT, Sankt Augustin, Germany and RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 3, "Name": "Milan Cermak", "Affiliation": "Masaryk University, Brno, Czech Republic"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CESNET, Prague, Czech Republic"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "W/Intelligence, WithSecure Corporation, Helsinki, Finland"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 9, "Name": "Avikarsha Mandal", "Affiliation": "Fraunhofer FIT, Sankt Augustin, Germany"}], "References": [{"Title": "The rise of machine learning for detection and classification of malware: Research developments, trends and challenges", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "102526", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "An anomaly detection framework for cyber-security data", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "101941", "JournalTitle": "Computers & Security"}, {"Title": "Secure Evaluation of Quantized Neural Networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "4", "Page": "355", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "Sharing Machine Learning Models as Indicators of Compromise for Cyber Threat Intelligence", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "140", "JournalTitle": "Cybersecurity"}, {"Title": "Review of Human Decision-making during Computer Security Incident Analysis", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "1", "JournalTitle": "Digital Threats: Research and Practice"}, {"Title": "Strengthening Privacy-Preserving Record Linkage using Diffusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "2023", "Issue": "2", "Page": "298", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "Defining the reporting threshold for a cybersecurity incident under the NIS Directive and the NIS 2 Directive", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "1", "Page": "", "JournalTitle": "Journal of Cybersecurity"}, {"Title": "Requirements for Playbook-Assisted Cyber Incident Response, Reporting and Automation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "3", "Page": "1", "JournalTitle": "Digital Threats: Research and Practice"}, {"Title": "Bloom Encodings in DGA Detection: Improving Machine Learning Privacy by Building on Privacy-Preserving Record Linkage", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "30", "Issue": "9", "Page": "1224", "JournalTitle": "JUCS - Journal of Universal Computer Science"}]}, {"ArticleId": 119739465, "Title": "Towards sustainable public and open data ecosystems: An introduction to a special section", "Abstract": "", "Keywords": "", "DOI": "10.1177/15701255241300620", "PubYear": 2024, "Volume": "29", "Issue": "4", "JournalId": 28496, "JournalTitle": "Information Polity", "ISSN": "1570-1255", "EISSN": "1875-8754", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Tartu, Faculty of Science and Technology, Institute of Computer Science, Tartu, Estonia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Namur Digital Institute, School of Management, Université de Namur, Namur, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Delft University of Technology, Faculty of Technology, Policy and Management, Delft, the Netherlands"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Granada, Department of Accounting and Finance, Granada, Spain"}], "References": [{"Title": "Conceptual and normative approaches to AI governance for a global digital ecosystem supportive of the UN Sustainable Development Goals (SDGs)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "2", "Page": "293", "JournalTitle": "AI and Ethics"}]}, {"ArticleId": *********, "Title": "Dual regulation of phaseol on osteoclast formation and osteoblast differentiation by targeting TAK1 kinase for osteoporosis treatment", "Abstract": "<p><b>INTRODUCTION</b>:Osteoporosis is an osteolytic disorder resulting from an inequilibrium between osteoblast-mediated osteogenesis and osteoclast-driven bone absorption. Safe and effective approaches for osteoporosis management are still highly demanded.</p><p><b>PURPOSE</b>:This study aimed to examine the osteoprotective effect and the mechanisms of phaseol (PHA) in vitro and in vivo.</p><p><b>METHODS</b>:Virtual screening identified the potential inhibitors of transforming growth factor-beta-activated kinase 1 (TAK1) from coumestans. The interaction between PHA and TAK1 was investigated by molecular simulation, pronase and thermal resistance assays. The maturation and function of osteoclasts were determined using tartrate-resistant acid phosphatase staining, bone absorption and F-actin ring formation assays. The differentiation and calcification of osteoblasts were assessed by alkaline phosphatase staining and Alizarin Red S staining. The activity of related targets and pathways were detected using immunoblotting, immunofluorescence and co-immunoprecipitation assays. The in vivo osteoprotective effect of P<PERSON> was evaluated using a lipopolysaccharide (LPS)-induced mouse osteoporosis model.</p><p><b>RESULTS</b>:Firstly, we confirmed that TAK1 was essential in controlling bone remodeling by regulating osteogenesis and osteoclastogenesis. Moreover, PHA, a coumestan compound predominantly present in leguminous plants, was identified as a potent TAK1 inhibitor through virtual and real experiments. Subsequently, PHA was observed to enhance osteoblast differentiation and calcification, while suppress osteoclast maturation and bone resorptive function in vitro. Mechanistically, PHA remarkably inhibited the TRAF6-TAK1 complex formation, and inhibited the activation of TAK1, MAPK and NF-κB pathways by targeting TAK1. In the in vivo study, PHA strongly attenuated bone loss, inflammatory responses, and osteoclast over-activation in lipopolysaccharide-induced osteoporosis mice.</p><p><b>CONCLUSION</b>:PHA had a dual-functional regulatory impact on osteogenesis and osteoclastogenesis by targeting TAK1, suppressing TRAF6-TAK1 complex generation, and modulating its associated signaling pathways, ultimately leading to mitigating osteoporosis. This study offered compelling evidence in favor of using PHA for preventing and managing osteoporosis as both a bone anabolic and anti-resorptive agent.</p><p>Copyright © 2024. Published by Elsevier B.V.</p>", "Keywords": "Osteoclastogenesis;Osteogenesis;Osteoporosis;Phaseol;TAK1", "DOI": "10.1016/j.jare.2024.12.009", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "Lihua Tan", "Affiliation": "State Key Laboratory of Quality Research in Chinese Medicine, Institute of Chinese Medical Sciences, University of Macau, Taipa, Macao SAR 999078, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Quality Research in Chinese Medicine, Institute of Chinese Medical Sciences, University of Macau, Taipa, Macao SAR 999078, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Quality Research in Chinese Medicine, Institute of Chinese Medical Sciences, University of Macau, Taipa, Macao SAR 999078, China."}, {"AuthorId": 4, "Name": "Yong<PERSON> Liang", "Affiliation": "State Key Laboratory of Quality Research in Chinese Medicine, Institute of Chinese Medical Sciences, University of Macau, Taipa, Macao SAR 999078, China."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Quality Research in Chinese Medicine, Institute of Chinese Medical Sciences, University of Macau, Taipa, Macao SAR 999078, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Quality Research in Chinese Medicine, Institute of Chinese Medical Sciences, University of Macau, Taipa, Macao SAR 999078, China."}, {"AuthorId": 7, "Name": "Yanbei Tu", "Affiliation": "State Key Laboratory of Quality Research in Chinese Medicine, Institute of Chinese Medical Sciences, University of Macau, Taipa, Macao SAR 999078, China; School of Pharmacy, Jiangsu University, Zhenjiang, Jiangsu 212013, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Quality Research in Chinese Medicine, Institute of Chinese Medical Sciences, University of Macau, Taipa, Macao SAR 999078, China"}], "References": []}, {"ArticleId": 119739502, "Title": "A swin-transformer-based network with inductive bias ability for medical image segmentation", "Abstract": "<p>Accurately segmenting organs, or diseased regions of varying sizes, is a challenge in medical image segmentation tasks with limited datasets. Although Transformer-based methods have self-attention mechanisms, excellent global modelling abilities and can effectively focus on crucial areas in medical images, they still face the intractable issues of computational complexity and excessive reliance on data. The Swin-Transformer has partly addressed the problem of computational complexity, however the method still requires large amounts of training data due to its lack of inductive bias capabilities. When applied to medical image segmentation tasks with small datasets, this leads to suboptimal performances. In contrast, the CNN-based methods can compensate for this limitation. To address this issue further, this paper proposes Swin-IBNet, which combines the Swin-Transformer with a CNN in a novel manner to imbue it with inductive bias capabilities, reducing its reliance on data. During the encoding process of Swin-IBNet, two novel and crucial modules, the feature fusion block (FFB) and the multiscale feature aggregation block (MSFA), are designed. The FFB is responsible for propagating the inductive bias capability to the Swin-Transformer encoder. Different from the previous use of multiscale features, MSFA efficiently leverages multiscale information from different layers through self-learning. This paper not only attempts to analyse the interpretability of the proposed Swin-IBNet but also performs more verifications on the public Synapse, ISIC 2018 and ACDC datasets. The experimental results show that Swin-IBNet is superior to the baseline method, Swin-Unet, and several state-of-the-art methods. Especially on the Synapse dataset, the DSC of Swin-IBNet surpasses that of Swin-Unet by 3.45%.</p>", "Keywords": "Medical image segmentation; Convolutional neural network; Deep learning; Transformer", "DOI": "10.1007/s10489-024-06029-1", "PubYear": 2025, "Volume": "55", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Symbol Computation and Knowledge Engineering of Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Symbol Computation and Knowledge Engineering of Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Symbol Computation and Knowledge Engineering of Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Symbol Computation and Knowledge Engineering of Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Symbol Computation and Knowledge Engineering of Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China; Corresponding author."}], "References": [{"Title": "Multiscale fused network with additive channel–spatial attention for image segmentation", "Authors": "Chengling Gao; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106754", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Unified medical image segmentation by learning from uncertainty in an end-to-end manner", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "241", "Issue": "", "Page": "108215", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "ConvUNeXt: An efficient convolution neural network for medical image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "253", "Issue": "", "Page": "109512", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "TransUNet＋: Redesigning the skip connection to enhance features in medical image segmentation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "256", "Issue": "", "Page": "109859", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Brain tumor segmentation based on the fusion of deep semantics and edge information in multimodal MRI", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "376", "JournalTitle": "Information Fusion"}, {"Title": "An effective CNN and Transformer complementary network for medical image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "109228", "JournalTitle": "Pattern Recognition"}, {"Title": "ViTAEv2: Vision Transformer Advanced by Exploring Inductive Bias for Image Recognition and Beyond", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "5", "Page": "1141", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "FTransCNN: Fusing Transformer and a CNN based on fuzzy logic for uncertain medical image segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101880", "JournalTitle": "Information Fusion"}, {"Title": "GhostFormer: Efficiently amalgamated CNN-transformer architecture for object detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "148", "Issue": "", "Page": "110172", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 119739527, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0957-4174(24)02921-X", "PubYear": 2025, "Volume": "263", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [], "References": []}, {"ArticleId": 119739564, "Title": "A Novel and Accurate BiLSTM Configuration Controller for Modular Soft Robots with Module Number Adaptability", "Abstract": "<p>Modular soft robots (MSRs) exhibit greater potential for sophisticated tasks compared with single-module robots. However, the modular structure incurs the complexity of accurate control and necessitates a control strategy specifically for modular robots. In this article, we introduce a data collection strategy tailored for MSR and a bidirectional long short-term memory (biLSTM) configuration controller capable of adapting to varying module numbers. Simulation cable-driven robots and real pneumatic robots have been included in experiments to validate the proposed approaches. Experimental results have demonstrated that MSRs can explore a larger space, thanks to our data collection method, and our controller can be leveraged despite an increase or decrease in module number. By leveraging the biLSTM, we aim to mimic the physical structure of MSRs, allowing the controller to adapt to module number change. Future work may include a planning method that bridges the task, configuration, and actuation spaces. We may also integrate online components into this controller.</p>", "Keywords": "bidirectional LSTM;configuration control;data-driven control;modular soft robot", "DOI": "10.1089/soro.2024.0015", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12246, "JournalTitle": "Soft Robotics", "ISSN": "2169-5172", "EISSN": "2169-5180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Biorobotics Institute and Department of Excellence in Robotics and AI, Scuola Superiore Sant’Anna, Pisa, Italy."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Biorobotics Institute and Department of Excellence in Robotics and AI, Scuola Superiore Sant’Anna, Pisa, Italy."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Biorobotics Institute and Department of Excellence in Robotics and AI, Scuola Superiore Sant’Anna, Pisa, Italy."}, {"AuthorId": 4, "Name": "Xuyang Ren", "Affiliation": "Multi-scale Medical Robotics Centre and Chow Yuk Ho Technology Centre for Innovative Medicine, The Chinese University of Hong Kong, Hong Kong, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Biorobotics Institute and Department of Excellence in Robotics and AI, Scuola Superiore Sant’Anna, Pisa, Italy."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Biorobotics Institute and Department of Excellence in Robotics and AI, Scuola Superiore Sant’Anna, Pisa, Italy."}], "References": [{"Title": "Novel Bending and Helical Extensile/Contractile Pneumatic Artificial Muscles Inspired by <PERSON> Trunk", "Authors": "Qing<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "5", "Page": "597", "JournalTitle": "Soft Robotics"}, {"Title": "Hierarchical control of soft manipulators towards unstructured interactions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "1", "Page": "411", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "Robust Multimodal Indirect Sensing for Soft Robots Via Neural Network-Aided Filter-Based Estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON> <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "3", "Page": "591", "JournalTitle": "Soft Robotics"}, {"Title": "Closing the Control Loop with Time-Variant Embedded Soft Sensors and Recurrent Neural Networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "6", "Page": "1167", "JournalTitle": "Soft Robotics"}, {"Title": "Harnessing Nonuniform Pressure Distributions in Soft Robotic Actuators", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "2", "Page": "2200330", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Soft Robot Proprioception Using Unified Soft Body Encoding and Recurrent Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "4", "Page": "825", "JournalTitle": "Soft Robotics"}, {"Title": "Curriculum-reinforcement learning on simulation platform of tendon-driven high-degree of freedom underactuated manipulator", "Authors": "Keung Or; Kehua Wu; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "", "Page": "1066518", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Physics‐Informed Neural Networks to Model and Control Robots: A Theoretical and Experimental Investigation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "6", "Issue": "5", "Page": "2300385", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 119739592, "Title": "Novel Hits for Targeting Kidney Failure in Type 2 Diabetes derived via In Silico Screening of the ZINC Natural Product Database", "Abstract": "Renal dysfunction is a common and potentially fatal consequence often noticed in persons who have been diagnosed with type 2 diabetes mellitus (T2DM). The gravity of this complication is underscored by the heightened likelihood of death linked to its advancement. Therefore, it is crucial to prioritize the prevention of the progress of renal impairment. The efficacy of sodium-glucose cotransporter 2 (SGLT2) inhibitors in retarding the advancement of renal dysfunction and albuminuria has been demonstrated, underscoring their potential utility in the management of renal problems. In a quest to unearth natural SGLT2 inhibitors, a comprehensive study was undertaken, encompassing structure-based virtual screening and a range of tools deployed to separate through the extensive ZINC database. Through the application of a pharmacophore model, a cohort of 11,336 natural compounds were discerned from the ZINC database that could potentially serve as SGLT2 inhibitors. Amid this collection, two compounds were singled out via a rigorous assessment of ADME (absorption, distribution, metabolism, and excretion) attributes, oral bioavailability, molecular dynamics parameters, and akin docking affinities to approved inhibitors. Compound 580 emerged as a promising candidate, validated by its congruence with metabolic processes and the absence of proclivities toward cardiotoxic effects. The findings from this investigation serve to reinforce the validation of SGLT2 inhibitors, thus paving the way for comprehensive in vitro and in vivo experimentation. Concurrently, these outcomes act as a catalyst for the innovation of novel inhibitors, heralding a new era of possibilities.", "Keywords": "", "DOI": "10.1016/j.jocs.2024.102497", "PubYear": 2025, "Volume": "85", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medicinal Chemistry, School of Pharmacy, Mashhad University of Medical Sciences, Mashhad, Iran;Student Research Committee, Faculty of Medicine, Mashhad University of Medical Sciences, Mashhad, Iran;Corresponding authors at: Department of Medicinal Chemistry, School of Pharmacy, Mashhad University of Medical Sciences, Mashhad, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biochemistry and Biophysics, Faculty of sciences, Mashhad Branch, Islamic Azad University, Mashhad, Iran;International UNESCO Center for Health-Related Basic Sciences and Human Nutrition, Mashhad University of Medical Sciences, Mashhad, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Medicinal Chemistry, School of Pharmacy, Ardabil University of Medical Sciences, Ardabil, Iran;Pharmaceutical Sciences Research Center, Ardabil University of Medical Sciences, Ardabil, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Biotechnology Research Center, Pharmaceutical Technology Institute, Mashhad University of Medical Sciences, Mashhad, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Student Research Committee, Tabriz University of Medical Sciences, Tabriz, Iran;Department of Nutrition, Faculty of Nutrition and Food Sciences, Tabriz University of Medical Sciences, Tabriz, Iran"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medicinal Chemistry, School of Pharmacy, Mashhad University of Medical Sciences, Mashhad, Iran;Biotechnology Research Center, Pharmaceutical Technology Institute, Mashhad University of Medical Sciences, Mashhad, Iran;Corresponding authors at: Department of Medicinal Chemistry, School of Pharmacy, Mashhad University of Medical Sciences, Mashhad, Iran"}], "References": []}, {"ArticleId": 119739596, "Title": "Adaptive semantic guidance network for video captioning", "Abstract": "Video captioning aims to describe video content using natural language, and effectively integrating information of visual and textual is crucial for generating accurate captions. However, we find that the existing methods over-rely on the language-prior information about the text acquired by training, resulting in the model tending to output high-frequency fixed phrases. In order to solve the above problems, we extract high-quality semantic information from multi-modal input and then build a semantic guidance mechanism to adapt to the contribution of visual semantics and text semantics to generate captions. We propose an Adaptive Semantic Guidance Network (ASGNet) for video captioning. The ASGNet consists of a Semantic Enhancement Encoder (SEE) and an Adaptive Control Decoder (ACD). Specifically, the SEE helps the model obtain high-quality semantic representations by exploring the rich semantic information from visual and textual. The ACD dynamically adjusts the contribution weights of semantics about visual and textual for word generation, guiding the model to adaptively focus on the correct semantic information. These two modules work together to help the model overcome the problem of over-reliance on language priors, resulting in more accurate video captions. Finally, we conducted extensive experiments on commonly used video captioning datasets. MSVD and MSR-VTT reached the state-of-the-art, and YouCookII also achieved good performance. These experiments fully verified the advantages of our method.", "Keywords": "", "DOI": "10.1016/j.cviu.2024.104255", "PubYear": 2025, "Volume": "251", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The School of Automation and Information Engineering, Xi’an University of Technology, Shaanxi, 710048, China"}, {"AuthorId": 2, "Name": "Hong <PERSON>", "Affiliation": "The School of Automation and Information Engineering, Xi’an University of Technology, Shaanxi, 710048, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shanxi Province Intelligent Optoelectronic Sensing Application Technology Innovation Center, Yuncheng University, Shanxi, 044000, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Information and Navigation College of Air Force Engineering University, Shaanxi, 710051, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The School of Automation and Information Engineering, Xi’an University of Technology, Shaanxi, 710048, China"}, {"AuthorId": 6, "Name": "Jing Shi", "Affiliation": "The School of Automation and Information Engineering, Xi’an University of Technology, Shaanxi, 710048, China"}], "References": [{"Title": "A Simple and Light-Weight Attention Module for Convolutional Neural Networks", "Authors": "<PERSON><PERSON> Park; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "4", "Page": "783", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Enhancing the alignment between target words and corresponding frames for video captioning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107702", "JournalTitle": "Pattern Recognition"}, {"Title": "An attention based dual learning approach for video captioning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "117", "Issue": "", "Page": "108332", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multimodal feature fusion based on object relation for video captioning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "1", "Page": "247", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "Collaborative three-stream transformers for video captioning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "235", "Issue": "", "Page": "103799", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "CMGNet: Collaborative multi-modal graph network for video captioning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "103864", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 119739626, "Title": "EAPT: An encrypted traffic classification model via adversarial pre-trained transformers", "Abstract": "Encrypted traffic classification plays a critical role in network traffic management and optimization, as it helps identify and differentiate between various types of traffic, thereby enhancing the quality and efficiency of network services. However, with the continuous evolution of traffic encryption and network applications, a large and diverse volume of encrypted traffic has emerged, presenting challenges for traditional feature extraction-based methods in identifying encrypted traffic effectively. This paper introduces an encrypted traffic classification model via adversarial pre-trained transformers-EAPT. The model utilizes the SentencePiece to tokenize encrypted traffic data, effectively addressing the issue of coarse tokenization granularity, thereby ensuring that the tokenization results more accurately reflect the characteristics of the encrypted traffic. During the pre-training phase, the EAPT employs a disentangled attention mechanism and incorporates a pre-training task similar to generative adversarial networks called Replaced BURST Detection. This approach not only enhances the model’s ability to understand contextual information but also accelerates the pre-training process. Additionally, this method minimizes model parameters, thus improving the model’s generalization capability. Experimental results show that EAPT can efficiently learn traffic features from small-scale unlabeled datasets and demonstrate excellent performance across multiple datasets with a relatively small number of model parameters.", "Keywords": "", "DOI": "10.1016/j.comnet.2024.110973", "PubYear": 2025, "Volume": "257", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Sichuan University, Chengdu, 610207, Sichuan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Cyber Science and Engineering, Sichuan University, Chengdu, 610207, Sichuan, China;Key Laboratory of Data Protection and Intelligent Management of the Ministry of Education, Chengdu, 610207, Sichuan, China;Corresponding author"}, {"AuthorId": 3, "Name": "Dongqing Jia", "Affiliation": "School of Cyber Science and Engineering, Sichuan University, Chengdu, 610207, Sichuan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Sichuan University, Chengdu, 610207, Sichuan, China"}], "References": [{"Title": "Deep packet: a novel approach for encrypted traffic classification using deep learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "1999", "JournalTitle": "Soft Computing"}, {"Title": "CETAnalytics: Comprehensive effective traffic information analytics for encrypted traffic classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "107258", "JournalTitle": "Computer Networks"}, {"Title": "TSCRNN: A novel classification scheme of encrypted traffic based on flow spatiotemporal features for efficient management of IIoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "190", "Issue": "", "Page": "107974", "JournalTitle": "Computer Networks"}, {"Title": "Self-attentive deep learning method for online traffic classification and its interpretability", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "196", "Issue": "", "Page": "108267", "JournalTitle": "Computer Networks"}, {"Title": "AMLBID: An auto-explained Automated Machine Learning tool for Big Industrial Data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "17", "Issue": "", "Page": "100919", "JournalTitle": "SoftwareX"}, {"Title": "Network traffic classification using convolutional neural network and ant-lion optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "108024", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Deep learning for encrypted traffic classification in the face of data drift: An empirical study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "109648", "JournalTitle": "Computer Networks"}, {"Title": "GLADS: A global-local attention data selection model for multimodal multitask encrypted traffic classification of IoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "109652", "JournalTitle": "Computer Networks"}, {"Title": "High-speed encrypted traffic classification by using payload features", "Authors": "<PERSON><PERSON> Yan; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Interaction matters: Encrypted traffic classification via status-based interactive behavior graph", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "155", "Issue": "", "Page": "111423", "JournalTitle": "Applied Soft Computing"}, {"Title": "A balanced supervised contrastive learning-based method for encrypted network traffic classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "145", "Issue": "", "Page": "104023", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 119739646, "Title": "Full-Scale Assessment of the “5GT System” for Tracking and Monitoring of Multimodal Dry Containers", "Abstract": "<p>A novel tracking and monitoring system for ISO 668 dry containers was realized by the ESA-funded “5G SENSOR@SEA” project, integrating 5G cellular technologies for massive Internet of Things with a GEO satellite-optimized backhauling link. The scope is the development of monitoring and tracking new services for multimodal container shipping. With the cooperation of four industrial partners and a telecommunication research center, the so-called “5GT System” was designed, developed, tested and validated up to field trials. Several modules of the system were designed, built and finally installed on the ship and in the teleport: the container tracking devices placed on the containers, the NB-IoT cellular network with optimized satellite backhauling, the Ku-band satellite terminals and the maritime service platform based on the OneM2M standard. The field trial conducted during the intercontinental liner voyage of a container ship showed primary technical achievements, including fair switching between terrestrial and satellite networks, reduction in packet loss in the open sea scenario and seamless integration of the BLE mesh network over the container tracking devices as NB-IoT/BLE LE Mesh gateways.</p>", "Keywords": "", "DOI": "10.3390/iot5040042", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 58626, "JournalTitle": "IoT", "ISSN": "", "EISSN": "2624-831X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Photonic Networks & Technologies National Laboratory, CNIT—National Inter-University Consortium for Telecommunications, Via Moruzzi 1, 56124 Pisa, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Photonic Networks & Technologies National Laboratory, CNIT—National Inter-University Consortium for Telecommunications, Via Moruzzi 1, 56124 Pisa, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Photonic Networks & Technologies National Laboratory, CNIT—National Inter-University Consortium for Telecommunications, Via Moruzzi 1, 56124 Pisa, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Telecommunications, Computer Engineering, and Photonics Institute (TeCIP), Scuola Superiore Sant’Anna, Via Moruzzi1, 56124 Pisa, Italy"}, {"AuthorId": 5, "Name": "Agostino Isca", "Affiliation": "MBI Srl, Via F. <PERSON> 7, 56121 Pisa, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "MBI Srl, Via F. <PERSON> 7, 56121 Pisa, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "MBI Srl, Via F. <PERSON> 7, 56121 Pisa, Italy"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Azcom Technology Srl, Centro Direzionale Milanofiori—Strada 2—Palazzo C, Scala C3, 20057 Assago, Italy"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Sistematica S.p.A, Viale Gorizia 25/C, 00198 Roma, Italy"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "TIM SpA, Via Oriolo Romano 240, 00189 Roma, Italy"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "TIM SpA, Via Oriolo Romano 240, 00189 Roma, Italy"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "TIM SpA, Via Oriolo Romano 240, 00189 Roma, Italy"}], "References": [{"Title": "Development of a Multi-Radio Device for Dry Container Monitoring and Tracking", "Authors": "<PERSON>;  <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "2", "Page": "187", "JournalTitle": "IoT"}]}, {"ArticleId": 119739658, "Title": "SAD: Self-assessment of depression for Bangladeshi university students using machine learning and NLP", "Abstract": "Depressive illness, influenced by social, psychological, and biological factors, is a significant public health concern that necessitates accurate and prompt diagnosis for effective treatment. This study explores the multifaceted nature of depression by investigating its correlation with various social factors and employing machine learning, natural language processing, and explainable AI to analyze depression assessment scales. Data from a survey of 520 Bangladeshi university students, encompassing socio-personal and clinical questions, was utilized in this study. Eight machine learning algorithms with optimized hyperparameters were applied to evaluate eight depression assessment scales, identifying the most effective one. Additionally, ten machine learning models, including five BERT-based and two generative large language models, were tested using three prompting approaches and assessed across four categories of social factors: relationship dynamics, parental pressure, academic contentment, and exposure to violence. The results showed that support vector machines achieved a remarkable 99.14% accuracy with the PHQ9 scale. While considering the social factors, the stacking ensemble classifier demonstrated the best results. Among NLP approaches, BioBERT outperformed other BERT-based models with 90.34% accuracy when considering all social aspects. In prompting approaches, the Tree of Thought prompting on Claude Sonnet surpassed other prompting techniques with 75.00% accuracy. However, traditional machine learning models outshined NLP methods in tabular data analysis, with the stacking ensemble model achieving the highest accuracy of 97.88%. The interpretability of the top-performing classifier was ensured using LIME.", "Keywords": "Depression diagnosis; Machine learning; Explainable AI; Large language models; BERT; Chain of thought; Tree of thought", "DOI": "10.1016/j.array.2024.100372", "PubYear": 2025, "Volume": "25", "Issue": "", "JournalId": 66362, "JournalTitle": "Array", "ISSN": "2590-0056", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, North South University, Bashundhara R/A, Dhaka, 1229, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, North South University, Bashundhara R/A, Dhaka, 1229, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, North South University, Bashundhara R/A, Dhaka, 1229, Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, North South University, Bashundhara R/A, Dhaka, 1229, Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, North South University, Bashundhara R/A, Dhaka, 1229, Bangladesh;Corresponding author"}], "References": [{"Title": "BioBERT: a pre-trained biomedical language representation model for biomedical text mining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1234", "JournalTitle": "Bioinformatics"}, {"Title": "Predicting Anxiety, Depression and Stress in Modern Life using Machine Learning Algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Neha Prerna Tigga", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "1258", "JournalTitle": "Procedia Computer Science"}, {"Title": "AIDA: Artificial intelligence based depression assessment applied to Bangladeshi students", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "18", "Issue": "", "Page": "100291", "JournalTitle": "Array"}, {"Title": "Autoencoders and their applications in machine learning: a survey", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "2", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Sparse feature selection using hypergraph Laplacian-based semi-supervised discriminant analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "157", "Issue": "", "Page": "110882", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 119739707, "Title": "Computation over APT compressed data", "Abstract": "The Arithmetic Progressions Tree ( APT ) is a data structure storing an encoding of a monotonic sequence L in [ 1 . . n ] . Previous work on APTs focused on its theoretical and experimental compression guarantees. This paper is the first to consider computations over APT compressed data. In particular: 1. We show how to perform a search for any sub-sequence/a set of the monotone sequence L in time proportional to the query sub-sequence length/set size multiplied by the size of the APT compressed representation of L . 2. We show how, given the APT compressed representation of the monotone sequence L , we can find a minimum run-length of L in constant time, a maximum run-length of L in O ( log n ) time, and all runs of L in constant time plus the output size. 3. We show how, given the APT compressed representation of the monotone sequence L , we can answer whether a consecutive periodic pattern P is represented by an APT -node in O ( log n ) time and report occurrences of P in L within the processing time of the output size. 4. In addition, we improve the APT construction algorithm time and space complexity.", "Keywords": "", "DOI": "10.1016/j.is.2024.102504", "PubYear": 2025, "Volume": "129", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shenkar College of Engineering.Design.Art, Ramat-Gan, 52526, Israel;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ariel University, Ariel, 40700, Israel"}], "References": [{"Title": "Smaller Compressed Suffix <PERSON>†", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "5", "Page": "721", "JournalTitle": "The Computer Journal"}]}, {"ArticleId": 119739753, "Title": "On Mitigating the Mismatch Between FSK Tags and GFSK Receivers in BLE Backscatter", "Abstract": "", "Keywords": "", "DOI": "10.1109/JRFID.2024.3514534", "PubYear": 2025, "Volume": "9", "Issue": "", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, San Diego State University, San Diego, CA, USA"}], "References": [{"Title": "Sine-Cosine Modulation for SSB Backscatter Radio Applications", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "", "Page": "197", "JournalTitle": "IEEE Journal of Radio Frequency Identification"}]}, {"ArticleId": 119739759, "Title": "Guest Editorial of the Special Issue on IEEE WiSEE 2023 Conference", "Abstract": "", "Keywords": "", "DOI": "10.1109/JRFID.2024.3507492", "PubYear": 2024, "Volume": "8", "Issue": "", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical, Electronic and Information Engineering “G. <PERSON>”, Alma Mater Studiorum—Universtà di Bologna, Bologna, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Telecommunications, Politecnico di Torino, Turin, Italy"}], "References": []}, {"ArticleId": 119739786, "Title": "Blockchain Interoperability Frameworks: A Comparative Analysis of Cross-Chain Solutions for DLT Innovation", "Abstract": "This article comprehensively analyzes blockchain interoperability as a crucial frontier in distributed ledger technology (DLT) innovation. Through systematic examination of current interoperability solutions, technical challenges, and implementation frameworks, the study provides insights into the evolving landscape of cross-chain communication and its implications for industry adoption. The article evaluates prominent interoperability frameworks, including the Interledger Protocol, Cosmos Network, Polkadot, and Canton Network, analyzing their architectural approaches, security mechanisms, and practical applications. Key findings reveal significant progress in addressing fundamental challenges such as protocol standardization, consensus mechanism compatibility, and scalability constraints while highlighting remaining cross-chain security and governance obstacles. The article demonstrates substantial benefits of blockchain interoperability across various sectors, with notable implementations in supply chain management reducing documentation errors by 65%, cross-border financial services decreasing settlement times from days to minutes, and healthcare data exchange improving information accessibility by 75%. Through examination of technical requirements, governance models, and risk mitigation strategies, this research establishes a comprehensive framework for implementing interoperable blockchain solutions while maintaining security and regulatory compliance. The article contributes to the growing knowledge in DLT innovation and provides practical insights for stakeholders working towards achieving seamless blockchain interoperability.", "Keywords": "Blockchain Interoperability;Cross-Chain Communication Protocols;Distributed Ledger Technology (DLT);Multi-Chain Integration Architecture;Consensus Mechanism Compatibility", "DOI": "10.32628/CSEIT241061183", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "NVIDIA, USA"}, {"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "NVIDIA, USA"}], "References": [{"Title": "A Survey of State-of-the-Art on Blockchains", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Blockchain Implementation Method for Interoperability between CBDCs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "5", "Page": "133", "JournalTitle": "Future Internet"}, {"Title": "A Survey on Blockchain Interoperability: Past, Present, and Future Trends", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "8", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Healthcare Data Management System Using Blockchain", "Authors": "<PERSON><PERSON>;  <PERSON>;  <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "382", "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology"}, {"Title": "Accelerating Cross-Shard Blockchain Consensus via Decentralized Coordinators Service With Verifiable Global States", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "4", "Page": "1340", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 119740094, "Title": "Machine learning-based assessment of diabetes risk", "Abstract": "<p>Currently, diabetes is one of the most dangerous diseases in modern society. Prevention is an extremely important aspect in the field of medicine, and the field of artificial intelligence and the healthcare industry are penetrating and integrating with each other, and combining machine models for prediction and diagnosis of diabetes is a big trend. In order to validate the advantages and potential of XGBoost model in the field of diabetes prediction, this study identified 10 key features by processing a medical examination dataset containing 556,495 sample size. Among them, glycated hemoglobin has high clinical value as a predictor. By constructing six machine models (XGBoost, Decision Tree, Logistic Regression, Random Forest, CatBoost, and LightGBM) and comparing their performances, we finally obtained that: the performance of XGBoost is relatively the best, with accuracy of 97.5%, recall of 97%, F1 score of 96.9%, and ROC-AUC score of 0.971.</p>", "Keywords": "Diabetes; Machine learning; Risk; XGBoost; Physical examination indicators", "DOI": "10.1007/s10489-024-05912-1", "PubYear": 2025, "Volume": "55", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Medical Instrumentation, Shanghai University of Medicine & Health Sciences, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Medical Instrumentation, Shanghai University of Medicine & Health Sciences, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Medical Instrumentation, Shanghai University of Medicine & Health Sciences, Shanghai, China"}, {"AuthorId": 4, "Name": "Yichao Sun", "Affiliation": "College of Medical Instrumentation, Shanghai University of Medicine & Health Sciences, Shanghai, China"}, {"AuthorId": 5, "Name": "He Ren", "Affiliation": "College of Medical Instrumentation, Shanghai University of Medicine & Health Sciences, Shanghai, China; Corresponding author."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Medical Instrumentation, Shanghai University of Medicine & Health Sciences, Shanghai, China; Corresponding author."}], "References": [{"Title": "Many-objective optimization of feature selection based on two-level particle cooperation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "532", "Issue": "", "Page": "91", "JournalTitle": "Information Sciences"}, {"Title": "Efficient prediction of early-stage diabetes using XGBoost classifier with random forest feature selection technique", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "22", "Page": "34163", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Performance Evaluation of Deep Dense Layer Neural Network for Diabetes Prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "76", "Issue": "1", "Page": "347", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A Proposed Technique Using Machine Learning for the Prediction of Diabetes Disease through a Mobile App", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "2024", "Issue": "", "Page": "1", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "En-RfRsK: An ensemble machine learning technique for prognostication of diabetes mellitus", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON> Amma N.G.", "PubYear": 2024, "Volume": "25", "Issue": "", "Page": "100441", "JournalTitle": "Egyptian Informatics Journal"}]}, {"ArticleId": 119740106, "Title": "AL-FEW: An enhanced approach for optimized query examples through feature weighting in active learning", "Abstract": "In practical applications of supervised machine learning, one of the challenges is the need for a significant amount of time, effort, and resources to label unlabeled data. In fact, active learning approaches aim to address this issue by querying the most informative data from the unlabeled data and adding it to the labeled dataset without using the entire unlabeled examples. However, this approach may result in biased results if the model used to query the examples to be labeled later is flawed. To address this issue, a new query strategy called AL-FEW (Active Learning based on Feature Estimated Weights) has been introduced, which selects the most informative examples independently of the model. This strategy utilizes feature weights calculated by the Relief method (ReliefF) and <PERSON>’s tau to evaluate changes after the addition of each unlabeled example and to select those that significantly alter the weights of the examples based on labeled data. The results of this study indicate that the query strategy proposed outperforms other strategies, achieving an accuracy of up to 90% for certain datasets. Additionally, the examples selected by this strategy do not exceed an imbalanced ratio of 2, while other strategies can reach up to 3.5 imbalanced ratio.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126045", "PubYear": 2025, "Volume": "266", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Systems Laboratory (LIS), Department of Mathematics and Informatics, Faculty of Sciences Ain <PERSON>ck, Hassan II University of Casablanca, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Systems Laboratory (LIS), Department of Mathematics and Informatics, Faculty of Sciences Ain <PERSON>ck, Hassan II University of Casablanca, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Systems Laboratory (LIS), Department of Mathematics and Informatics, Faculty of Sciences Ain <PERSON>, Hassan II University of Casablanca, Morocco;Corresponding author"}], "References": [{"Title": "Adjusting the imbalance ratio by the dimensionality of imbalanced data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "217", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Active learning based on belief functions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Active learning of driving scenario trajectories", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "113", "Issue": "", "Page": "104972", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "On the application of active learning for efficient and effective IoT botnet detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "40", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A Survey on Feature Selection Techniques Based on Filtering Methods for Cyber Attack Detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "191", "JournalTitle": "Information"}, {"Title": "A new filter-based gene selection approach in the DNA microarray domain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "240", "Issue": "", "Page": "122504", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Survey on Machine Learning Biases and Mitigation Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Digital"}, {"Title": "Elastic net-based high dimensional data selection for regression", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "244", "Issue": "", "Page": "122958", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A meta-active learning approach exploiting instance importance", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "247", "Issue": "", "Page": "123320", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Active learning inspired method in generative models", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "249", "Issue": "", "Page": "123582", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Feature selection based on dynamic crow search algorithm for high-dimensional data classification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "250", "Issue": "", "Page": "123871", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Class-imbalanced semi-supervised learning for large-scale point cloud semantic segmentation via decoupling optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "156", "Issue": "", "Page": "110701", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-view semi-supervised classification via auto-weighted submarkov random walk", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "256", "Issue": "", "Page": "124961", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 119740256, "Title": "ProjecTwin: A digital twin-based projection framework for flexible spatial augmented reality in adaptive assistance", "Abstract": "Establishing an efficient communication channel between the digital manufacturing system and assembly workers is a key research area in smart assembly. Spatial augmented reality (SAR) technology offers a naked-eye display mode that seamlessly integrates virtual information into real industrial scenes, providing an immersive experience for multiple viewers. However, the challenge arises when facing frequent workstation reconfiguration in customized product assembly tasks, requiring a flexible spatial augmented reality (FSAR) mode. To address the issues of uncertainty in projection layout, distortion in projected display, and lack of mobility in the projector of FSAR, a DT (digital twin)-based projection framework called ProjecTwin is proposed. The ProjecTwin framework can simulate real-time projection scenarios and enables the reconfiguration of projection layouts. Based on ProjecTwin, an FSAR assistance method is proposed. An optimization model is employed to solve the DT projection layout issue by considering human-robot collaboration scenarios as constraints. Additionally, a method for reverse generation of DT images is proposed to address the image distortion problem. Furthermore, a DT entity control method based on mobile robots is introduced to tackle the inflexibility of the projector. Three cases are conducted to illustrate how the proposed method supports optimized, adaptive, and human-centric FSAR assistance.", "Keywords": "", "DOI": "10.1016/j.jmsy.2024.11.018", "PubYear": 2025, "Volume": "78", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, The Hong Kong Polytechnic University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, The Hong Kong Polytechnic University;State Key Laboratory of Intelligent Manufacturing Equipment and Technology, Huazhong University of Science and Technology;Corresponding author at: Department of Industrial and Systems Engineering, The Hong Kong Polytechnic University"}], "References": [{"Title": "Digital twin-driven rapid reconfiguration of the automated manufacturing system via an open architecture model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "101895", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Projection-based augmented reality system for assembly guidance and monitoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "1", "Page": "10", "JournalTitle": "Assembly Automation"}, {"Title": "Dynamic projection mapping on deformable stretchable materials using boundary tracking", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "103", "Issue": "", "Page": "61", "JournalTitle": "Computers & Graphics"}, {"Title": "Industry 5.0: Prospect and retrospect", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "", "Page": "279", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A state-of-the-art survey on Augmented Reality-assisted Digital Twin for futuristic human-centric industry transformation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "81", "Issue": "", "Page": "102515", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A verification-oriented and part-focused assembly monitoring system based on multi-layered digital twin", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "68", "Issue": "", "Page": "477", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Digital twin-driven smelting process management method for converter steelmaking", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A vision-language-guided robotic action planning approach for ambiguity mitigation in human–robot collaborative manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "74", "Issue": "", "Page": "1009", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 119740408, "Title": "Occlusion-Preserved Surveillance Video Synopsis with Flexible Object Graph", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11263-024-02302-5", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Guiqing Li", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "An improved surveillance video synopsis framework: a HSATLBO optimization approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "7-8", "Page": "4429", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "MOTChallenge: A Benchmark for Single-Camera Multiple Target Tracking", "Authors": "<PERSON>; Aljos̆a Os̆ep; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "4", "Page": "845", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Interactive visualization-based surveillance video synopsis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "4", "Page": "3954", "JournalTitle": "Applied Intelligence"}, {"Title": "An optimized complex motion prediction approach based on a video synopsis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "11", "Issue": "1", "Page": "88", "JournalTitle": "International Journal of Intelligent Unmanned Systems"}, {"Title": "Object interaction-based surveillance video synopsis", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "4", "Page": "4648", "JournalTitle": "Applied Intelligence"}, {"Title": "A Personalized Video Synopsis Framework for Spherical Surveillance Video", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "45", "Issue": "3", "Page": "2603", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Multiview abnormal video synopsis in real-time", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106406", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 119740483, "Title": "A comprehensive review of usage control frameworks", "Abstract": "The sharing of data and digital assets in a decentralized settling is entangled with various legislative challenges, including, but not limited to, the need to adhere to legal requirements with respect to privacy and copyright. In order to provide more control to data and digital asset owners, usage control could be used to make sure that consumers handle data according to privacy, licenses, regulatory requirements, among others. However, considering that many of the existing usage control frameworks were designed to cater for different use cases (e.g., networking, operating systems, and industry 4.0), there is a need to better understand the existing proposals and how they compare to one another. In this paper, we provide a holistic overview of existing usage control frameworks and their support for a broad set of requirements. We systematically collect requirements that are routinely used to guide the development of usage control solutions, which are classified according to three broad dimensions: specification , enforcement , and system . We use these requirements to conduct a qualitative comparison of the most prominent usage control frameworks found in the literature. Finally, we identify existing gaps, challenges, and opportunities in the field of usage control in general, and in decentralized environments in particular.", "Keywords": "Usage control; Policy languages; Enforcement frameworks; Decentralized systems", "DOI": "10.1016/j.cosrev.2024.100698", "PubYear": 2025, "Volume": "56", "Issue": "", "JournalId": 21523, "JournalTitle": "Computer Science Review", "ISSN": "1574-0137", "EISSN": "1876-7745", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Complex Networks, Vienna University of Economics and Business, Welthandelspl. 1, 1020, Vienna, Austria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Complex Networks, Vienna University of Economics and Business, Welthandelspl. 1, 1020, Vienna, Austria"}], "References": [{"Title": "Policy-based usage control for a trustworthy data sharing platform in smart cities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "998", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "User consent modeling for ensuring transparency and compliance in smart cities", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "4", "Page": "465", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "Machine Understandable Policies and GDPR Compliance Checking", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3", "Page": "303", "JournalTitle": "KI - Künstliche Intelligenz"}, {"Title": "Specifying and verifying usage control models and policies in TLA$$^+$$", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "5", "Page": "685", "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)"}, {"Title": "Hardware Information Flow Tracking", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Consent through the lens of semantics: State of the art survey and best practices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "3", "Page": "647", "JournalTitle": "Semantic Web"}, {"Title": "Analysis of ontologies and policy languages to represent information flows in GDPR", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>-<PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "3", "Page": "709", "JournalTitle": "Semantic Web"}, {"Title": "Assessment Framework for the Identification and Evaluation of Main Features for Distributed Usage Control Solutions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Privacy and Security"}, {"Title": "Governance of Autonomous Agents on the Web: Challenges and Opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}]}, {"ArticleId": 119740500, "Title": "BidCorpus: A Multifaceted Learning Dataset for Public Procurement", "Abstract": "<p>Digital transformation has significantly impacted public procurement, improving operational efficiency, transparency, and competition. This transformation has allowed the automation of data analysis and oversight in public administration. Public procurement involves various stages and generates a multitude of documents. However, experts manually analyze these unstructured textual documents, which are time-consuming and inefficient. To address this issue, we introduce BidCorpus, a novel and comprehensive dataset consisting of thousands of documents related to public procurement, specifically bidding notices from Brazilian public websites. The dataset was labeled using weak supervision techniques, manual labeling, and BERT-based language models. Models trained with these annotated data showed promising results, with metrics greater than 80 % in various experiments. The models could also tolerate intentional changes made to bidding notices to evade fraud detection. All the resources from this work are publicly available, including the documents, pre-processing scripts, and training and evaluation of the models. We expect the dataset and its labels to be of great value to researchers working on public procurement problems.</p><p>© 2024 The Author(s).</p>", "Keywords": "BERT;Bidding notice;NLP;Weak supervision", "DOI": "10.1016/j.dib.2024.111202", "PubYear": 2025, "Volume": "58", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Piauí. Campus Universitário Ministro Petrônio Portella. Teresina, Piauí, Brazil."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal University of Piauí. Campus Universitário Ministro Petrônio Portella. Teresina, Piauí, Brazil."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Piauí. Campus Universitário Ministro Petrônio Portella. Teresina, Piauí, Brazil."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Federal University of Piauí. Campus Universitário Ministro Petrônio Portella. Teresina, Piauí, Brazil."}, {"AuthorId": 5, "Name": "Anselmo Paiva", "Affiliation": "Federal University of Maranhão. Av. dos Portugueses, 1966 São Luís, Maranhão, Brazil."}], "References": [{"Title": "Snorkel: rapid training data creation with weak supervision", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "2-3", "Page": "709", "JournalTitle": "The VLDB Journal"}, {"Title": "Incremental collusive fraud detection in large-scale online auction networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "9", "Page": "7416", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 119740665, "Title": "Exploring the landscape of compressed DeepFakes: Generation, dataset and detection", "Abstract": "In today’s era of social media, where information spreads rapidly through platforms like YouTube, Facebook, and Twitter, the development of generative models have given rise to a phenomenon called DeepFakes. This survey aims to provide a comprehensive overview of compressed DeepFakes research, covering various detection and generation techniques and datasets. It presents the details of detection methods, including experimental settings such as datasets, algorithms, feature selection, and results. The survey also highlights the existing challenges and future directions.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.129116", "PubYear": 2025, "Volume": "619", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Canadian Institute for Cybersecurity, 46 <PERSON><PERSON>, <PERSON><PERSON>, NB E3B 9W4, NB, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science (UNB), 550 Windsor St, Fredericton, E3B 5A3, NB, Canada"}], "References": [{"Title": "Voxceleb: Large-scale speaker verification in the wild", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "101027", "JournalTitle": "Computer Speech & Language"}, {"Title": "DMGAN: Discriminative Metric-based Generative Adversarial Networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "105370", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deepfakes and beyond: A Survey of face manipulation and fake detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "131", "JournalTitle": "Information Fusion"}, {"Title": "Blind MV-based video steganalysis based on joint inter-frame and intra-frame statistics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "6", "Page": "9137", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "CSGAN: Cyclic-Synthesized Generative Adversarial Networks for image-to-image transformation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114431", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Optical Flow based CNN for detection of unlearnt deepfake manipulations", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "146", "Issue": "", "Page": "31", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Deepfake generation and detection, a survey", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "5", "Page": "6259", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "FFR_FD: Effective and fast detection of DeepFakes via feature point defects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "596", "Issue": "", "Page": "472", "JournalTitle": "Information Sciences"}, {"Title": "Towards DeepFake video forensics based on facial textural disparities in multi-color channels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "654", "JournalTitle": "Information Sciences"}, {"Title": "Deep learning for deepfakes creation and detection: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "223", "Issue": "", "Page": "103525", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "A comprehensive overview of Deepfake: Generation, detection, datasets, and opportunities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "513", "Issue": "", "Page": "351", "JournalTitle": "Neurocomputing"}, {"Title": "Watching the BiG artifacts: Exposing DeepFake videos via Bi-granularity artifacts", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109179", "JournalTitle": "Pattern Recognition"}, {"Title": "Three-classification face manipulation detection using attention-based feature decomposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "", "Page": "103024", "JournalTitle": "Computers & Security"}, {"Title": "Deepfacelab: Integrated, flexible and extensible face-swapping framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "109628", "JournalTitle": "Pattern Recognition"}, {"Title": "Qualitative failures of image generation models and their application in detecting deepfakes", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "137", "Issue": "", "Page": "104771", "JournalTitle": "Image and Vision Computing"}, {"Title": "DeepMark: A Scalable and Robust Framework for DeepFake Video Detection", "Authors": "Li Tang; Qingqing Ye; Haibo Hu", "PubYear": 2024, "Volume": "27", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Privacy and Security"}]}, {"ArticleId": 119740734, "Title": "Causal disentanglement for regulating social influence bias in social recommendation", "Abstract": "Social recommendation systems face the problem of social influence bias, which can lead to an overemphasis on recommending items that friends have interacted with. Addressing this problem is crucial, and existing methods often rely on techniques such as weight adjustment or leveraging unbiased data to eliminate this bias. However, we argue that not all biases are detrimental, i.e., some items recommended by friends may align with the user’s interests. Blindly eliminating such biases could undermine these positive effects, potentially diminishing recommendation accuracy. In this paper, we propose a C ausal D isentanglement-based framework for R egulating S ocial influence B ias in social recommendation, named CDRSB, to improve recommendation performance. From the perspective of causal inference, we find that the user social network could be regarded as a confounder between the user and item embeddings (treatment) and ratings (outcome). Due to the presence of this social network confounder, two paths exist from user and item embeddings to ratings: a non-causal social influence path and a causal interest path. Building upon this insight, we propose a disentangled encoder that focuses on disentangling user and item embeddings into interest and social influence embeddings. Mutual information-based objectives are designed to enhance the distinctiveness of these disentangled embeddings, eliminating redundant information. Additionally, a regulatory decoder that employs a weight calculation module to dynamically learn the weights of social influence embeddings for effectively regulating social influence bias has been designed. Experimental results on four large-scale real-world datasets Ciao, Epinions, Dianping, and <PERSON>uban book demonstrate the effectiveness of CDRSB compared to state-of-the-art baselines. We release our code at https://github.com/Lili1013/CDRSB .", "Keywords": "Graph Neural Network (GNN); Social recommendation; Causal inference; Disentanglement; Mutual information", "DOI": "10.1016/j.neucom.2024.129133", "PubYear": 2025, "Volume": "618", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical and Data Engineering, University of Technology Sydney, 15, Broadway, Ultimo, Sydney, 2000, NSW, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical and Data Engineering, University of Technology Sydney, 15, Broadway, Ultimo, Sydney, 2000, NSW, Australia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Chongqing University of Arts and Sciences, 319, Honghe Avenue, Yongchuan District, Chongqing, 402160, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Data Engineering, University of Technology Sydney, 15, Broadway, Ultimo, Sydney, 2000, NSW, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Data Engineering, University of Technology Sydney, 15, Broadway, Ultimo, Sydney, 2000, NSW, Australia"}], "References": [{"Title": "SI-News: Integrating social information for news recommendation with attention-based graph convolutional network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "494", "Issue": "", "Page": "33", "JournalTitle": "Neurocomputing"}, {"Title": "Addressing Confounding Feature Issue for Causal Recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}]}, {"ArticleId": *********, "Title": "Optimal Features Selection for Human Activity Recognition (HAR) System Using Deep Learning Architectures", "Abstract": "One exciting area within computer vision is classifying human activities, which has diverse applications like medical informatics, human-computer interaction, surveillance, and task monitoring systems. In the healthcare field, understanding and classifying patients’ activities is crucial for providing doctors with essential information for medication reactions and diagnosis. While some research methods already exist, utilizing machine learning and soft computational algorithms to recognize human activity from videos and images, there’s ongoing exploration of more advanced computer vision techniques. This paper introduces a straightforward and effective automated approach that involves five key steps: preprocessing, feature extraction technique, feature selection, feature fusion, and finally classification. To evaluate the proposed approach, two commonly used benchmark datasets KTH and Weizmann are employed for training, validation, and testing of ML classifiers. The study’s findings show that the first and second datasets had remarkable accuracy rates of 99.94% and 99.80%, respectively. When compared to existing methods, our approach stands out in terms of sensitivity, accuracy, precision, and specificity evaluation metrics. In essence, this paper demonstrates a practical method for automatically classifying human activities using an optimal feature fusion and deep learning approach, promising a great result that could benefit various fields, particularly in healthcare.", "Keywords": "Surveillance;Optimal Feature;SVM;Complex Tree;Human Activity Recognition;Feature Fusion", "DOI": "10.4236/jcc.2024.1212002", "PubYear": 2024, "Volume": "12", "Issue": "12", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering (CSE), University of Rajshahi, Rajshahi, Bangladesh .; Department of Computer Science and Engineering (CSE), Bangladesh Army University of Engineering & Technology (BAUET), Dayarampur, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering (CSE), University of Rajshahi, Rajshahi, Bangladesh .; Department of Computer Science and Engineering (CSE), Bangladesh Army University of Engineering & Technology (BAUET), Dayarampur, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering (CSE), Bangladesh Army University of Engineering & Technology (BAUET), Dayarampur, Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Computer Science and Engineering (CSE), Bangladesh Army University of Engineering & Technology (BAUET), Dayarampur, Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering (CSE), University of Rajshahi, Rajshahi, Bangladesh"}], "References": [{"Title": "Deep motion templates and extreme learning machine for sign language recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "6", "Page": "1233", "JournalTitle": "The Visual Computer"}, {"Title": "Optimizing Deep Feedforward Neural Network Architecture: A Tabu Search Based Approach", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "3", "Page": "2855", "JournalTitle": "Neural Processing Letters"}, {"Title": "Deep metric learning for open-set human action recognition in videos", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "4", "Page": "1207", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Skeleton-based action recognition via spatial and temporal transformer networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "208-209", "Issue": "", "Page": "103219", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Human Action Recognition and Prediction: A Survey", "Authors": "Yu Kong; Yun Fu", "PubYear": 2022, "Volume": "130", "Issue": "5", "Page": "1366", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Deep Learning Approach for Human Action Recognition Using a Time Saliency Map Based on Motion Features Considering Camera Movement and Shot in Video Image Sequences", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "11", "Page": "616", "JournalTitle": "Information"}]}, {"ArticleId": 119741063, "Title": "Communication-efficient federated learning based on compressed sensing and ternary quantization", "Abstract": "<p>Most existing work on Federated Learning (FL) transmits full-precision weights, which contain a significant amount of redundant information, leading to a substantial communication burden. This issue is particularly pronounced with the growing prevalence of smart mobile and Internet of Things (IoT) devices, where data sharing generates a large communication cost. To address this issue, we propose a communication-efficient Federated Learning algorithm, FedCSTQ, based on compressed sensing (CS) and ternary quantization.FedCSTQ introduces a heuristic sparsification method that enhances information selection, thereby mitigating the accuracy degradation typically associated with CS. Additionally, the algorithm incorporates ternary quantization to process residuals after sparsity, further reducing the impact of accuracy degradation due to sparsity while guaranteeing a small amount of communication overhead. Experiments conducted on the publicly available datasets reveal that FedCSTQ outperforms the standard FL (FedAvg), SignSGD with a majority vote, FL using dithering(CEP-FL), and FL based on Compressed Sensing (CS-FL). Ablation studies further demonstrate the effectiveness of our method.</p>", "Keywords": "Distributed learning; Collaborative work; Federated Learning (FL); Compressed sensing (CS); Signal processing; Quantization (signal); Gradient compression; Communication overhead; Internet of Things (IoT)", "DOI": "10.1007/s10489-024-05979-w", "PubYear": 2025, "Volume": "55", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, Electronics and Information, Guangxi University, Nanning, P.R. China; Guangxi Key Laboratory Multimedia Communications and Network Technology, Guangxi University, Nanning, P.R. China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer, Electronics and Information, Guangxi University, Nanning, P.R. China; Guangxi Key Laboratory Multimedia Communications and Network Technology, Guangxi University, Nanning, P.R. China"}], "References": []}, {"ArticleId": 119741277, "Title": "Improving bug triage with the bug personalized tossing relationship", "Abstract": "<b >Background:</b> In open-source software projects, the main task of bug triage is accurately assigning bugs to appropriate developers. Statistics indicate that about 50% of bugs are reassigned (also called “tossed”) at least once, greatly extending the time for bug fixing. Research studies have shown that combining historical tossing relationships can significantly improve bug triage performance. <b >Objective:</b> The current research on utilizing bug tossing relationships can be mainly divided into two categories: (1) During the reassignment phase, only developers with the highest probability of tossing relationships are selected. (2) Use attribute filtering mechanism to filter and match developers. However, these approaches fail to fully consider the matching degree between developers’ abilities and the knowledge required to fix current bugs. We are attempting to propose an approach to address the above problem. <b >Approach:</b> We propose an approach to improve bug triage with the Bug Personalized Tossing Relationship (BPTRM). It uses a tossing transition probability matrix derived from historical tossing paths to help recommend suitable developers for solving bug reports. <b >Result:</b> Experimental results from various data sets within Eclipse and Mozilla indicate that BPTRM improves average recommendation performance by at least 14.38% compared to different initial assignment approaches. In addition, compared to baselines, BPTRM improves the average accuracy by 14.66% and shortens the average bug tossing length by 16.19%. <b >Conclusion:</b> 1. The BPTRM approach, combined with personalized bug tossing relationships, precisely matches developers’ abilities and the knowledge required to fix current bugs. 2. This effectively improves the bug triage’s accuracy and shortens the bug tossing’s length.", "Keywords": "", "DOI": "10.1016/j.infsof.2024.107642", "PubYear": 2025, "Volume": "179", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Data Science, Qingdao University of Science and Technology, Qingdao, 266100, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science, Qingdao University of Science and Technology, Qingdao, 266100, China"}, {"AuthorId": 3, "Name": "Xinshuang Ren", "Affiliation": "Dawning International Information Industry Co., Ltd., Qingdao, 266100, China"}, {"AuthorId": 4, "Name": "Feng <PERSON>", "Affiliation": "School of Data Science, Qingdao University of Science and Technology, Qingdao, 266100, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "China University of Petroleum (East China), Qingdao, 266580, China"}, {"AuthorId": 6, "Name": "Xingyu Gao", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, 100029, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science, Qingdao University of Science and Technology, Qingdao, 266100, China;Corresponding author"}], "References": [{"Title": "Examining the effects of developer familiarity on bug fixing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "169", "Issue": "", "Page": "110667", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Learning to rank developers for bug report assignment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106667", "JournalTitle": "Applied Soft Computing"}, {"Title": "Learning to rank developers for bug report assignment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106667", "JournalTitle": "Applied Soft Computing"}, {"Title": "A spatial–temporal graph neural network framework for automated software bug triaging", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "241", "Issue": "", "Page": "108308", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Wayback Machine: A tool to capture the evolutionary behavior of the bug reports and their triage process in open-source software systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "189", "Issue": "", "Page": "111308", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Developer load balancing bug triage: Developed load balance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "41", "Issue": "6", "Page": "e13006", "JournalTitle": "Expert Systems"}, {"Title": "S-DABT: Schedule and Dependency-aware Bug Triage in open-source bug tracking systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "151", "Issue": "", "Page": "107025", "JournalTitle": "Information and Software Technology"}, {"Title": "Using Screenshot Attachments in Issue Reports for Triaging", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "7", "Page": "1", "JournalTitle": "Empirical Software Engineering"}]}, {"ArticleId": 119741426, "Title": "An investigation of machine learning strategies for electric motor anomaly detection using vibration and audio signals", "Abstract": "Purpose The objective of this article is to evaluate and compare the performance of two machine learning (ML) algorithms, i.e. support vector machines (SVMs) and random forests (RFs), when classifying seven states of operation of an electric motor using the Mel-frequency cepstral coefficients (MFCCs) as extracted representative features. Design/methodology/approach The extracted MFCCs are calculated using the motor’s vibration and audio signals separately. Findings After the training, the SVM model obtained a mean accuracy of 100% for the MFCCs obtained from database vibration signals and 69.6% for the database of audio signals. Research limitations/implications The ML strategies and results reported are limited to the well-known data for industrial electric motors used in the evaluations, although it was performed tests and cross-validations with unseen data and the information from the confusion matrix. Practical implications The success of these methodologies in defect classification, where the RF presented a mean accuracy of 99.15% for the vibration signals and 63.82% for the audio signal, enables the use of this ML and extracted features as a predictive tool for failure and anomaly detection, lifetime predictions and online real-time monitoring. Originality/value It is the first time that the MFCCs are being used for anomaly detection in vibration and audio signals for electrical motors, as this extracted feature is usually used for human speech identification in the literature.", "Keywords": "", "DOI": "10.1108/EC-03-2024-0206", "PubYear": 2025, "Volume": "42", "Issue": "2", "JournalId": 30507, "JournalTitle": "Engineering Computations", "ISSN": "0264-4401", "EISSN": "1758-7077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Fault detection and diagnosis using vibration signal analysis in frequency domain for electric motors considering different real fault types", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "41", "Issue": "3", "Page": "311", "JournalTitle": "Sensor Review"}, {"Title": "Temperature prediction for electric vehicles of permanent magnet synchronous motor using robust machine learning tools", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "1", "Page": "243", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Wind Turbine Remaining Useful Life Prediction Using Small Dataset and Machine Learning Techniques", "Authors": "<PERSON>; <PERSON> And<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "35", "Issue": "2", "Page": "337", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}]}, {"ArticleId": *********, "Title": "A routing approach based on combination of gray wolf clustering and fuzzy clustering and using multi-criteria decision making approaches for WSN-IoT", "Abstract": "The Internet of Things (IoT) plays a crucial role across diverse sectors such as industrial, educational, and healthcare. Ensuring stable and efficient network performance is essential for these applications, making optimal routing a critical factor. Proper clustering of network nodes is pivotal, as it enhances network efficiency and prolongs its lifetime by reducing energy consumption. This study proposes a novel routing approach for IoT networks based on Wireless Sensor Networks (WSN), called GWFCCV (<PERSON> Wolf and <PERSON><PERSON> Clustering and using Critic and Fuzzy Vikor approaches). Our method employs a combination of Gray Wolf Optimizer (GWO) and Fuzzy C-Means (FCM) for clustering, alongside multi-criteria decision-making techniques to rank and select nodes for efficient routing. Simulation results demonstrate that GWFCCV significantly improves key network parameters, including energy consumption, throughput, and network lifetime, outperforming existing approaches such as EACMRP-MS, FEEC-IIR, and FRLDG. For example, it can increase the lifetime of the network by 5.43 %, 8.3 % and 23.26 %, respectively.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2024.109946", "PubYear": 2025, "Volume": "122", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Future Technology Research Center, National Yunlin University of Science and Technology, Douliou, Yunlin, Taiwan;These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Artificial intelligence and Robotics, Sejong University, Seoul 05006, Republic of Korea;These authors contributed equally to this work"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems, College of Economics and Political Science, Sultan Qaboos University, Muscat 123, Oman"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Dezful Branch, Islamic Azad University, Dezful, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Dezful Branch, Islamic Azad University, Dezful, Iran"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "DTU AI and Data Science Hub (DAIDASH), Duy Tan University, Da Nang, Vietnam"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Duy Tan University, Da Nang, Vietnam;Center of Excellence in Genomics and Precision Dentistry, Department of Physiology, Faculty of Dentistry, Chulalongkorn University, Bangkok 10330, Thailand;Jadara University Research Center, Jadara University, Irbid, Jordan;Corresponding author"}], "References": [{"Title": "Fuzzy TOPSIS and fuzzy VIKOR in selecting green suppliers for sponge iron and steel manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "8", "Page": "6505", "JournalTitle": "Soft Computing"}, {"Title": "Energy Aware Clustering with Multihop Routing Algorithm for Wireless Sensor Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "1", "Page": "233", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Controlling energy aware clustering and multihop routing protocol for\n IoT \n assisted wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "21", "Page": "e7106", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "An Enhanced GWO Algorithm with Improved Explorative Search Capability for Global Optimization and Data Clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "1", "Page": "2166232", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "An optimization method in wireless sensor network routing and IoT with water strider algorithm and ant colony optimization algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "3", "Page": "1527", "JournalTitle": "Evolutionary Intelligence"}]}, {"ArticleId": *********, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1878-7789(24)00062-0", "PubYear": 2024, "Volume": "42", "Issue": "", "JournalId": 6324, "JournalTitle": "Nano Communication Networks", "ISSN": "1878-7789", "EISSN": "1878-7797", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "MDQ: A QoS-Congestion Aware Deep Reinforcement Learning Approach for Multi-Path Routing in SDN", "Abstract": "The challenge of link overutilization in networking persists, prompting the development of load-balancing methods such as multi-path strategies and flow rerouting. However, traditional rule-based heuristics struggle to adapt dynamically to network changes. This leads to complex models and lengthy convergence times, unsuitable for diverse QoS demands, particularly in time-sensitive applications. Existing routing approaches often result in specific types of traffic overloading links or general congestion, prolonged convergence delays, and scalability challenges. To tackle these issues, we propose a QoS-Congestion Aware Deep Reinforcement Learning Approach for Multi-Path Routing in Software-Defined Networking (MDQ). Leveraging Deep Reinforcement Learning, MDQ intelligently selects optimal multi-paths and allocates traffic based on flow needs. We design a multi-objective function using a combination of link and queue metrics to establish an efficient routing policy. Moreover, we integrate a congestion severity index into the learning process and incorporate a traffic classification phase to handle mice-elephant flows, ensuring that diverse class-of-service requirements are adequately addressed. Through an RYU-Docker-based Openflow framework integrating a Live QoS Monitor, DNC Classifier, and Online Routing, results demonstrate a 19%–22% reduction in delay compared to state-of-the-art algorithms, exhibiting robust reliability across diverse scenarios of network dynamics.", "Keywords": "", "DOI": "10.1016/j.jnca.2024.104082", "PubYear": 2025, "Volume": "235", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai 200240, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai 200240, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shanghai Jiao Tong University, Shanghai 200240, PR China"}], "References": [{"Title": "Deep Q-Network and Traffic Prediction based Routing Optimization in Software Defined Networks", "Authors": "EL Hocine <PERSON>; <PERSON><PERSON><PERSON><PERSON> Outtagarts; <PERSON><PERSON>", "PubYear": 2021, "Volume": "192", "Issue": "", "Page": "103181", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "QoS–aware Mesh-based Multicast Routing Protocols in Edge Ad Hoc Networks: Concepts and Challenges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "Deep Learning-Based Network Traffic Prediction for Secure Backbone Networks in Internet of Vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "Using FlowVisor and evolutionary algorithms to improve the switch migration in SDN", "Authors": "<PERSON>", "PubYear": 2024, "Volume": "222", "Issue": "", "Page": "103807", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Early Prevention and Mitigation of Link Flooding Attacks in Software Defined Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "224", "Issue": "", "Page": "103832", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "DQS: A QoS-driven routing optimization approach in SDN using deep reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "188", "Issue": "", "Page": "104851", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": *********, "Title": "LocalDGP: local degree-balanced graph partitioning for lightweight GNNs", "Abstract": "<p>Graph neural networks (GNNs) have been widely employed in various fields including knowledge graphs and social networks. When dealing with large-scale graphs, traditional full-batch training methods suffer from excessive GPU memory consumption. To solve this problem, subgraph sampling methods divide the graph into multiple subgraphs and then train the GNN on each subgraph sequentially, which can reduce GPU memory consumption. However, the existing graph partitioning algorithms (e.g., METIS) require global graph information before partitioning, and consume a significant amount of memory to store this information, which is detrimental for large-scale graph partitioning. Moreover, the GNN parameters in the subgraph sampling methods are shared among all the subgraphs. The structural differences between the subgraphs and the global graph (e.g., differences in node degree distributions) will produce a gradient bias on the subgraphs, resulting in a degradation of GNN accuracy. Therefore, a local degree-balanced graph partitioning algorithm named LocalDGP is proposed in this paper. First, in LocalDGP, only the local graph information is acquired during the partitioning process, which can reduce memory consumption. Second, the nodes are balancedly partitioned into subgraphs based on degree to ensure that the subgraph structure is consistent with the global graph. Extensive experimental results on four graph datasets demonstrate that LocalDGP can improve the accuracy of the GNNs while reducing memory consumption. The code is publicly available at https://github.com/li143yf/LocalDGP .</p>", "Keywords": "Graph neural networks; Graph partitioning; Local information; Memory consumption", "DOI": "10.1007/s10489-024-05964-3", "PubYear": 2025, "Volume": "55", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Shengwei Ji", "Affiliation": "School of Big Data And Artificial Intelligence, Hefei University, Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Big Data And Artificial Intelligence, Hefei University, Hefei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Information Engineering, Hefei University of Technology, Hefei, China; Key Laboratory of Knowledge Engineering with Big Data, Ministry of Education, Hefei, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Big Data And Artificial Intelligence, Hefei University, Hefei, China; Corresponding author."}], "References": [{"Title": "Molecular generative Graph Neural Networks for Drug Discovery", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "450", "Issue": "", "Page": "242", "JournalTitle": "Neurocomputing"}, {"Title": "GC-LSTM: graph convolution embedded LSTM for dynamic network link prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "7", "Page": "7513", "JournalTitle": "Applied Intelligence"}, {"Title": "An evolving graph convolutional network for dynamic functional brain network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "11", "Page": "13261", "JournalTitle": "Applied Intelligence"}, {"Title": "Opinion Leaders for Information Diffusion Using Graph Neural Network in Online Social Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on the Web"}, {"Title": "Modified term frequency-inverse document frequency based deep hybrid framework for sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "21", "Page": "32967", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Long-tailed graph neural networks via graph structure learning for node classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "17", "Page": "20206", "JournalTitle": "Applied Intelligence"}, {"Title": "CIRS: Bursting Filter Bubbles by Counterfactual Interactive Recommender System", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "42", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "A subgraph sampling method for training large-scale graph convolutional network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "649", "Issue": "", "Page": "119661", "JournalTitle": "Information Sciences"}, {"Title": "Neighbour adjusted dispersive flies optimization based deep hybrid sentiment analysis framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "24", "Page": "64393", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Self-Bidirectional Decoupled Distillation for Time Series Classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "8", "Page": "4101", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "LocalTGEP: A Lightweight Edge Partitioner for Time-Varying Graph", "Authors": "Shengwei Ji; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "12", "Issue": "2", "Page": "455", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}]}, {"ArticleId": 119741972, "Title": "Hybrid-ctunet: a double complementation approach for 3D medical image segmentation", "Abstract": "", "Keywords": "", "DOI": "10.1007/s13042-024-02469-w", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Segmentation-based context-aware enhancement network for medical images", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "3", "Page": "963", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}]}, {"ArticleId": 119742055, "Title": "A review on multi-focus image fusion using deep learning", "Abstract": "Multi-focus image fusion (MFIF) is an image enhancement technique that investigates how to obtain a fully focused image from multiple defocus images, providing fundamental services for computer vision fields, such as image recognition, 3D reconstruction, medical diagnosis, computational photography, etc. In recent years, the deep learning-based MFIF has broken through the limitations of traditional MFIF and achieved superior fusion results. Existing reviews on MFIF mainly classify and describe the techniques at the technical level, such as supervised/unsupervised learning methods and network types, but lack discussion and analysis on problem scenarios. Therefore, based on the problem scenarios of MFIF, this paper categorizes deep learning-based MFIF research into six types: MFIF with lightweight networks, MFIF for artifacts and defocus spread effects, MFIF for information preservation, MFIF with unified fusion networks, MFIF on addressing suboptimal initial decision map and MFIF in challenging environments. Furthermore, this paper summarizes commonly used synthesis datasets and real datasets for MFIF, and statistically describes the main evaluation metrics. Finally, this paper analyzes the shortcomings of existing algorithms, and identifies potential future research directions, aiming to provide objective reviews for MFIF researchers focusing on different problem scenarios.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.129125", "PubYear": 2025, "Volume": "618", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, East China University of Science and Technology, Shanghai, 200237, China;Shanghai Key Laboratory of Computer Software Evaluating and Testing (Shanghai Development center of Computer Software Technology), Shanghai, 201203, China;Corresponding author at: School of Information Science and Engineering, East China University of Science and Technology, Shanghai, 200237, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, East China University of Science and Technology, Shanghai, 200237, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Information Technologies, Universidad del Bío-Bío, Chillán, 3780000, Chile"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, East China University of Science and Technology, Shanghai, 200237, China;Shanghai Key Laboratory of Computer Software Evaluating and Testing (Shanghai Development center of Computer Software Technology), Shanghai, 201203, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, East China University of Science and Technology, Shanghai, 200237, China"}, {"AuthorId": 6, "Name": "Chunhua Gu", "Affiliation": "School of Information Science and Engineering, East China University of Science and Technology, Shanghai, 200237, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Engineering, Architecture and Design, Universidad San Sebastián, Santiago, 8320000, Chile"}], "References": [{"Title": "CNNs hard voting for multi-focus image fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "1749", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "IFCNN: A general image fusion framework based on convolutional neural network", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "99", "JournalTitle": "Information Fusion"}, {"Title": "IFCNN: A general image fusion framework based on convolutional neural network", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "99", "JournalTitle": "Information Fusion"}, {"Title": "A novel multi-focus image fusion by combining simplified very deep convolutional networks and patch-based sequential reconstruction strategy", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106253", "JournalTitle": "Applied Soft Computing"}, {"Title": "Real-MFF: A large realistic multi-focus image dataset with ground truth", "Authors": "<PERSON><PERSON> Zhang; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "370", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "MFF-GAN: An unsupervised generative adversarial network with adaptive and gradient joint constraints for multi-focus image fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "40", "JournalTitle": "Information Fusion"}, {"Title": "SESF-Fuse: an unsupervised deep model for multi-focus image fusion", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "11", "Page": "5793", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Multi-focus image fusion techniques: a survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "8", "Page": "5735", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Image fusion meets deep learning: A survey and perspective", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "76", "Issue": "", "Page": "323", "JournalTitle": "Information Fusion"}, {"Title": "SDNet: A Versatile Squeeze-and-Decomposition Network for Real-Time Image Fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "10", "Page": "2761", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "End-to-end learning for simultaneously generating decision map and multi-focus image fusion result", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "204", "JournalTitle": "Neurocomputing"}, {"Title": "LNMF: lightweight network for multi-focus image fusion", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "16", "Page": "22335", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A new multi-focus image fusion method based on multi-classification focus learning and multi-scale decomposition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "2", "Page": "1452", "JournalTitle": "Applied Intelligence"}, {"Title": "A novel approach with the dynamic decision mechanism (DDM) in multi-focus image fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "2", "Page": "1821", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multi-focus image fusion with deep residual learning and focus property detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "86-87", "Issue": "", "Page": "1", "JournalTitle": "Information Fusion"}, {"Title": "ZMFF: Zero-shot multi-focus image fusion", "Authors": "<PERSON>ng<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "92", "Issue": "", "Page": "127", "JournalTitle": "Information Fusion"}, {"Title": "Multi-focus image fusion: Transformer and shallow feature attention matters", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "76", "Issue": "", "Page": "102353", "JournalTitle": "Displays"}, {"Title": "Siamese conditional generative adversarial network for multi-focus image fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "14", "Page": "17492", "JournalTitle": "Applied Intelligence"}, {"Title": "When Multi-Focus Image Fusion Networks Meet Traditional Edge-Preservation Technology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "10", "Page": "2529", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "TPP: Deep learning based threshold post-processing multi-focus image fusion method", "Authors": "<PERSON><PERSON> Fang; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "110", "Issue": "", "Page": "108736", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "MFIF-DWT-CNN: Multi-focus ımage fusion based on discrete wavelet transform with deep convolutional neural network", "Authors": "Derya Avcı; <PERSON><PERSON>; Fatih <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "4", "Page": "10951", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "GIPC-GAN: an end-to-end gradient and intensity joint proportional constraint generative adversarial network for multi-focus image fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Yaoxi Jiang", "PubYear": 2023, "Volume": "9", "Issue": "6", "Page": "7395", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "New insights into multi-focus image fusion: A fusion method based on multi-dictionary linear sparse representation and region fusion model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "105", "Issue": "", "Page": "102230", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 119742186, "Title": "Can earnings conference calls tell more lies? A contrastive multimodal dialogue network for advanced financial statement fraud detection", "Abstract": "Financial statement frauds by listed firms pose significant challenges to public investors and jeopardize the stability of financial markets. Previous studies have identified deceptive verbal and vocal cues from earnings conference calls as indicators of financial statement fraud. However, these studies only extracted managers' verbal and vocal cues separately over the entire call, neglecting the utterance-level fusion between verbal and vocal cues as well as the multi-turn interaction between analysts and managers. To fill this gap, we develop a novel end-to-end c ontrastive m ulti m odal d ialogue network (CMMD) that considers both verbal-vocal fusion and multi-role interactions to uncover hidden deceptive cues in earnings conference calls. The proposed model comprises two core modules, namely, the Multimodal Fusion Learning module and the Dialogue Interaction Learning module . Building on Vrij's verbal-nonverbal complementary mechanisms in deception detection, the designed Multimodal Fusion Learning employs contrastive learning to align verbal and vocal cues and a co-attention mechanism to learn cross-modal interaction. Inspired by the Interpersonal Deception Theory that emphasizes the dynamic interaction process between deceivers and targets, the Dialogue Interaction Learning utilizes a dialogue-aware co-attention mechanism to model multi-turn analyst-manager interaction and uses contrastive learning to improve dialogue representations. Our extensive empirical results show that CMMD achieves 8.64 % improvement in detecting fraudulent cases compared to the best baseline model. As such, our study advances the research frontier in fraud detection and contributes an innovative IT artifact in practice.", "Keywords": "", "DOI": "10.1016/j.dss.2024.114381", "PubYear": 2025, "Volume": "189", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information, Renmin University of China, Beijing 100872, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information, Renmin University of China, Beijing 100872, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information, Renmin University of China, Beijing 100872, China;City University of Hong Kong, Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information, Renmin University of China, Beijing 100872, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The Chinese University of Hong Kong (Shenzhen), Shenzhen 518172, China"}], "References": [{"Title": "Deep learning for detecting financial statement fraud", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "113421", "JournalTitle": "Decision Support Systems"}, {"Title": "Attentive statement fraud detection: Distinguishing multimodal financial data with fine-grained attention", "Authors": "<PERSON>; <PERSON><PERSON> Ma; <PERSON>", "PubYear": 2023, "Volume": "167", "Issue": "", "Page": "113913", "JournalTitle": "Decision Support Systems"}]}, {"ArticleId": 119742283, "Title": "Interpretable CAA classification based on incorporating feature channel attention into LSTM", "Abstract": "The open and broadcast nature of wireless media makes signal transmission among wireless media prone to different types of channel access attacks (CAA), mainly in Medium Access Control (MAC) layer, ranging from constant jamming to protocol manipulation attacks. CAAs can allow an adversary to greatly degrade overall transmission bandwidth or fully hinder legitimate users from access medium. Therefore, it is critical to timely detect and classify CAAs. A few efforts have been made through applying deep neural networks (DNN) for CAA detection. But they still suffer from low accuracy and poor interpretability. In this backdrop, this paper puts forward an interpretable CAA classification DNN model based on feature channel attention (FCA), named FCA-LSTM. After introducing 11 types of CAAs through state transition model, we detail the design of FCA-LSTM, which incorporates three modules, i.e., FCA module, Long Short-Term Memory (LSTM) module, and Grad-CAM module for promoting classification accuracy while reducing the number of parameters. A series of experiments is conducted to compare FCA-LSTM against four benchmarks, including ResNet50, conventional neural network (CNN), Transformer, and LSTM. Results show that FCA-LSTM performs better than four benchmarks in general. Furthermore, the number of parameters and inference time of FCA-LSTM are both much smaller than traditional LSTM. At last, Grad-CAM is utilized to visualize FCA-LSTM’s concern areas of an input sample. This visualization process sheds light on crucial aspects of model’s decision-making process, further fortifying its interpretability and overall reliability.", "Keywords": "", "DOI": "10.1016/j.cose.2024.104252", "PubYear": 2025, "Volume": "150", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The 63rd Research Institute, National University of Defense Technology, Nanjing, 210007, Jiangsu, China"}, {"AuthorId": 2, "Name": "Xiang<PERSON> Wei", "Affiliation": "The 63rd Research Institute, National University of Defense Technology, Nanjing, 210007, Jiangsu, China;Corresponding author"}, {"AuthorId": 3, "Name": "Ji<PERSON><PERSON> Fan", "Affiliation": "The 63rd Research Institute, National University of Defense Technology, Nanjing, 210007, Jiangsu, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The 63rd Research Institute, National University of Defense Technology, Nanjing, 210007, Jiangsu, China"}], "References": [{"Title": "Grad-CAM: Visual Explanations from Deep Networks via Gradient-Based Localization", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "336", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Wi-Fi HaLow for the Internet of Things: An up-to-date survey on IEEE 802.11ah research", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "103036", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "A review on the attention mechanism of deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "48", "JournalTitle": "Neurocomputing"}, {"Title": "Utilize DBN and DBSCAN to detect selective forwarding attacks in event-driven wireless sensors networks", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107122", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "DDoS attack detection and mitigation using deep neural network in SDN environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "138", "Issue": "", "Page": "103661", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 119742544, "Title": "Generative AI in Medicine and Healthcare: Moving Beyond the ‘Peak of Inflated Expectations’", "Abstract": "<p>The rapid development of specific-purpose Large Language Models (LLMs), such as Med-PaLM, MEDITRON-70B, and Med-Gemini, has significantly impacted healthcare, offering unprecedented capabilities in clinical decision support, diagnostics, and personalized health monitoring. This paper reviews the advancements in medicine-specific LLMs, the integration of Retrieval-Augmented Generation (RAG) and prompt engineering, and their applications in improving diagnostic accuracy and educational utility. Despite the potential, these technologies present challenges, including bias, hallucinations, and the need for robust safety protocols. The paper also discusses the regulatory and ethical considerations necessary for integrating these models into mainstream healthcare. By examining current studies and developments, this paper aims to provide a comprehensive overview of the state of LLMs in medicine and highlight the future directions for research and application. The study concludes that while LLMs hold immense potential, their safe and effective integration into clinical practice requires rigorous testing, ongoing evaluation, and continuous collaboration among stakeholders.</p>", "Keywords": "", "DOI": "10.3390/fi16120462", "PubYear": 2024, "Volume": "16", "Issue": "12", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Data Science Institute, Vanderbilt University, Nashville, TN 37240, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Data Science Institute, Vanderbilt University, Nashville, TN 37240, USA"}, {"AuthorId": 3, "Name": "Maged <PERSON><PERSON>", "Affiliation": "School of Medicine, University of Lisbon, 1649-028 Lisbon, Portugal"}], "References": [{"Title": "Generative AI in Medicine and Healthcare: Promises, Opportunities and Challenges", "Authors": "<PERSON><PERSON>; Ma<PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "9", "Page": "286", "JournalTitle": "Future Internet"}, {"Title": "The Era of Artificial Intelligence Deception: Unraveling the Complexities of False Realities and Emerging Threats of Misinformation", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "6", "Page": "299", "JournalTitle": "Information"}]}, {"ArticleId": 119742618, "Title": "Hyper-Personalization in Retail: Leveraging Data Science and Real-Time Streaming", "Abstract": "Personalization has transformed from a luxury feature to a fundamental business necessity in today's digital retail landscape. This comprehensive article explores the evolution from traditional personalization to hyper-personalization, examining the retail sector's technological components, practical applications, and future implications. The article investigates how advanced technologies, including artificial intelligence, machine learning, and real-time data streaming, enable retailers to deliver highly individualized customer experiences. Through a detailed examination of implementation strategies, benefits, and impacts, this analysis demonstrates how retailers leverage sophisticated data science frameworks and dynamic experience customization to enhance customer engagement, improve operational efficiency, and drive business growth. The article particularly highlights the transformative impact of hyper-personalization in specific retail segments, such as apparel, while also exploring the quantifiable benefits for retailers and customers.", "Keywords": "Hyper-Personalization;Real-Time Analytics;Customer Experience Optimization;Retail Digital Transformation;AI-Driven Personalization", "DOI": "10.32628/CSEIT241061179", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Albertsons Companies, USA"}, {"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Albertsons Companies, USA"}], "References": []}, {"ArticleId": 119742631, "Title": "SEAMS: A surrogate-assisted evolutionary algorithm with metric-based dynamic strategy for expensive multi-objective optimization", "Abstract": "In real-world scenarios where resources for evaluating expensive optimization problems are limited and the reliability of trained models is hard to assess, the quality of the non-dominated front formed by algorithms tends to be low. This paper proposes a metric-based surrogate-assisted evolutionary algorithm for multi-objective expensive optimization, incorporating a novel model management strategy that integrates a regeneration mechanism. This approach aims to achieve a well-balanced convergence and diversity, facilitating the attainment of high-quality non-dominated fronts to address expensive multi-objective optimization problems. The model management strategy, based on metrics, comprehensively evaluates the reliability of the classification model and selects appropriate strategies for offspring selection. Moreover, through significance analysis of the population, the regeneration mechanism identifies high-quality dimensions for regenerating offspring. The algorithm maximizes the utilization of the classification model to guide the generation and selection of offspring in the population. Experiments on DTLZ, MaF, WFG, and the high-dimensional portfolio optimization problem demonstrate that the proposed algorithm outperforms nine state-of-the-art surrogate-assisted evolutionary algorithms, highlighting its superior performance across various scenarios.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126050", "PubYear": 2025, "Volume": "265", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Mathematics, Fujian University of Technology, Fujian 350118, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Mathematics, Fujian University of Technology, Fujian 350118, China;Fujian Provincial Key Laboratory of Big Data Mining and Applications, Fujian 350118, China;Correspondence to: College of Computer Science and Mathematics, Fujian University of Technology, No. 69, Xuefu South Road, University Town, Fuzhou City, Fujian Province 350118, China.; Corresponding author"}], "References": [{"Title": "An adaptive Bayesian approach to surrogate-assisted evolutionary multi-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "519", "Issue": "", "Page": "317", "JournalTitle": "Information Sciences"}, {"Title": "Indicator-based Multi-objective Evolutionary Algorithms", "Authors": "<PERSON><PERSON><PERSON>Cardona; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Two infill criteria driven surrogate-assisted multi-objective evolutionary algorithms for computationally expensive problems with medium dimensions", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100774", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Adaptive dropout for high-dimensional expensive multiobjective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "1", "Page": "271", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A Convolutional Neural Network-Based Surrogate Model for Multi-objective Optimization Evolutionary Algorithm Based on Decomposition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "72", "Issue": "", "Page": "101081", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A novel approach of many-objective particle swarm optimization with cooperative agents based on an inverted generational distance indicator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "623", "Issue": "", "Page": "220", "JournalTitle": "Information Sciences"}, {"Title": "Adaptive mating selection based on weighted indicator for Multi/Many-objective evolutionary algorithm", "Authors": "<PERSON><PERSON>; <PERSON> <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "110223", "JournalTitle": "Applied Soft Computing"}, {"Title": "Evolutionary algorithm with individual-distribution search strategy and regression-classification surrogates for expensive optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "634", "Issue": "", "Page": "423", "JournalTitle": "Information Sciences"}, {"Title": "Treed Gaussian Process Regression for Solving Offline Data-Driven Continuous Multiobjective Optimization Problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "4", "Page": "375", "JournalTitle": "Evolutionary Computation"}, {"Title": "A pairwise comparison based surrogate-assisted evolutionary algorithm for expensive multi-objective optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "80", "Issue": "", "Page": "101323", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A multi-objective Chaos Game Optimization algorithm based on decomposition and random learning mechanisms for numerical optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "110525", "JournalTitle": "Applied Soft Computing"}, {"Title": "Bayesian Co-evolutionary Optimization based entropy search for high-dimensional many-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "274", "Issue": "", "Page": "110630", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "μMOSM: A hybrid multi-objective micro evolutionary algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107000", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A deep learning integrated framework for predicting stock index price and fluctuation via singular spectrum analysis and particle swarm optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "54", "Issue": "2", "Page": "1770", "JournalTitle": "Applied Intelligence"}, {"Title": "Surrogate-assisted PSO with archive-based neighborhood search for medium-dimensional expensive multi-objective problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "666", "Issue": "", "Page": "120405", "JournalTitle": "Information Sciences"}, {"Title": "Self-organizing surrogate-assisted non-dominated sorting differential evolution", "Authors": "Aluizio F.R<PERSON>; Lucas <PERSON>; Antônio R.C. Gonçalves", "PubYear": 2024, "Volume": "91", "Issue": "", "Page": "101703", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 119742680, "Title": "On providing multi-level security assurance based on Common Criteria for O-RAN mobile network equipment. A test case: O-RAN Distributed Unit", "Abstract": "Open Radio Access Network (O-RAN) technology introduces disaggregation of RAN network functions, offering enhanced flexibility for extending hardware and software. To ensure interoperability between such components, the O-RAN Alliance (the main Standards Development Organisation of O-RAN) defined a set of new interfaces. The network may be built by integrating components from different providers. The introduction of multi-provider components and functions increases security challenges due to the increase of security surfaces (e.g., new interfaces). Therefore, it is relevant for network operators to gain a certain level of assurance that O-RAN components deployed in the network are secure. This paper proposes a framework for the security evaluation of O-RAN interfaces that provides assurance that the O-RAN component has been tested deeply enough to demonstrate its resilience to attacks. Our proposal is based on Common Criteria standards and provides several security assurance levels depending on the intended use of the O-RAN network. Each security assurance level involves a set of tests, from security conformance tests to specialised fuzzy tests. We have specified them in the Vulnerability assessment for the product, as required in the Common Criteria. The validation of the framework focuses on the O-DU (O-RAN Distributed Unit) component, which is a logical module responsible for the implementation of L2 layer functionalities; nevertheless, it can be easily extended to other O-RAN components: O-CU (O-RAN Central Unit) and O-RU (O-RAN Radio Unit) as well as to Non and Near Real Time Radio Intelligent Controller (RIC). The O-DU evaluation results show that it is possible to provide the evaluation at different levels of security assurance, which correspond to different intended uses of the 5G O-RAN mobile network.", "Keywords": "O-RAN; 5G; Security evaluation; Security testing; Security assurance", "DOI": "10.1016/j.cose.2024.104271", "PubYear": 2025, "Volume": "150", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Telecommunications, Warsaw University of Technology, 00-661 Warszawa, Poland;IT Security Evaluation Facility, National Institute of Telecommunications, 04-894 Warszawa, Poland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Telecommunications, Warsaw University of Technology, 00-661 Warszawa, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Telecommunications, Warsaw University of Technology, 00-661 Warszawa, Poland;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IT Security Evaluation Facility, National Institute of Telecommunications, 04-894 Warszawa, Poland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IT Security Evaluation Facility, National Institute of Telecommunications, 04-894 Warszawa, Poland"}, {"AuthorId": 6, "Name": "Constandino<PERSON>", "Affiliation": "Department of Computer Science, University of Nicosia, 2417 Nicosia, Cyprus"}], "References": [{"Title": "Open RAN security: Challenges and opportunities", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "214", "Issue": "", "Page": "103621", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 119742685, "Title": "Privacy and Security of Wearable Internet of Things: A Scoping Review and Conceptual Framework Development for Safety and Health Management in Construction", "Abstract": "Traditional construction safety monitoring, primarily based on manual observation, is increasingly challenging due to site complexity, human error, and the time-consuming nature of inspections. Wearable Internet of Things (WIoT) devices offer potential solutions by enabling real-time monitoring of workers’ health, environment, and location, enhancing safety management. However, the adoption of WIoT raises privacy and security concerns, including risks of data breaches and unauthorized access to sensitive health information. This study presents a scoping review that explores privacy and security issues related to WIoT-based safety monitoring, analyzing data types, security challenges, and regulatory frameworks. The study concludes with a privacy-informed conceptual framework for WIoT adoption in construction safety, providing a foundational guide for addressing privacy and security in WIoT.", "Keywords": "", "DOI": "10.1016/j.cose.2024.104275", "PubYear": 2025, "Volume": "150", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Civil & Environmental Engineering, and Construction Management, The University of Texas at San Antonio, San Antonio, TX 78249, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Civil & Environmental Engineering, and Construction Management, The University of Texas at San Antonio, San Antonio, TX 78249, United States;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Construction Science, Texas A&M University, College Station, TX 77840, United States"}, {"AuthorId": 4, "Name": "Oluwafemi <PERSON>fe", "Affiliation": "Dept. of Management, Information Systems, & Quantitative Methods, Collat School of Business, The University of Alabama at Birmingham, Birmingham, AL 35294, United States"}], "References": [{"Title": "An exhaustive survey on security and privacy issues in Healthcare 4.0", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "311", "JournalTitle": "Computer Communications"}, {"Title": "Biometric Systems Utilising Health Data from Wearable Devices", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Efficient privacy-preserving authentication protocol using PUFs with blockchain smart contracts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "101958", "JournalTitle": "Computers & Security"}, {"Title": "Empirical analysis of transaction malleability within blockchain-based e-Voting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "102081", "JournalTitle": "Computers & Security"}, {"Title": "A survey on wireless body area networks: architecture, security challenges and research opportunities", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "102211", "JournalTitle": "Computers & Security"}, {"Title": "Towards privacy compliance: A design science study in a small organization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "146", "Issue": "", "Page": "106868", "JournalTitle": "Information and Software Technology"}, {"Title": "A Strong Mutual Authentication Protocol for Securing Wearable Smart Textile Applications", "Authors": "H. D<PERSON>K<PERSON>; M. H. OZCANHAN", "PubYear": 2022, "Volume": "22", "Issue": "1", "Page": "31", "JournalTitle": "Advances in Electrical and Computer Engineering"}, {"Title": "A systematic literature review of methods and datasets for anomaly-based network intrusion detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "102675", "JournalTitle": "Computers & Security"}, {"Title": "The role of IoT sensor in smart building context for indoor fire hazard scenario: A systematic review of interdisciplinary articles", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "", "Page": "100803", "JournalTitle": "Internet of Things"}, {"Title": "Addressing privacy concerns with wearable health monitoring technology", "Authors": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "14", "Issue": "3", "Page": "e1535", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}]}, {"ArticleId": 119742899, "Title": "MLTCN-EEG: metric learning-based temporal convolutional network for seizure EEG classification", "Abstract": "<p>The incorporation of artificial intelligence (AI) into medical data processing is increasingly prevalent due to its diagnostic and analytical competencies. Numerous deep learning models have been applied to medical data analyses, including the Temporal Convolutional Network (TCN) for its competence of temporal pattern abstraction. However, TCN may be suboptimal for modeling longer-range dependencies in EEG data, lacking metric learning and data clustering mechanisms. In other words, there is neither metric learning nor a data clustering mechanism in the conventional TCN architecture. Therefore, an enhanced TCN model is devised, namely Metric Learning-based Temporal Convolutional Network for EEG signals (MLTCN-EEG), to tackle the challenges associated with classifying seizure and non-seizure EEG signals. Specifically, in the proposed model, metric learning is integrated into the TCN architecture to learn discriminative feature spaces. This enhances the extraction of complex patterns inherent in EEG data, boosting the model’s classification competency. In other words, this study formulates and integrates a specialized metric learning component within the temporal convolutional architecture, enabling the model to discern subtle variations crucial for accurate seizure identification. Despite facing the common challenge of unbalanced training data in deep neural network training, this study also explores and assesses two methods for balancing datasets: sub-sampling and Deep Convolutional Generative Adversarial Network (DCGAN). The empirical results demonstrate that MLTCN-EEG with a DCGAN-balanced dataset exhibits superior performance compared to other existing techniques, showcasing its efficacy in distinguishing seizure events with superior classification performance. The proposed model achieves an accuracy of 99.15%, precision of 100%, recall rate of 98.35%, specificity of 100% and a remarkable F 1 score of 99.16% using the University of California Irvine (UCI) Seizure datasets. The proposed model also attained an accuracy of 82.66%, precision of 68.09%, recall rate of 67.95%, specificity of 88.13% and F1 score of 68.02% using Children’s Hospital Boston-Massachusetts Institute of Technology (CHB-MIT) dataset. These results highlight the potential impact of MLTCN-EEG and DCGAN in advancing the classification of epileptic EEG signals for improved medical diagnosis and therapy.</p>", "Keywords": "Seizure; EEG; Classification; Artificial intelligence; Deep learning; Machine learning; Metric learning; TCN", "DOI": "10.1007/s00521-024-10783-1", "PubYear": 2025, "Volume": "37", "Issue": "4", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Information Science and Technology, Multimedia University, Ayer Keroh, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Information Science and Technology, Multimedia University, Ayer Ke<PERSON>h, Malaysia; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Science and Technology, Multimedia University, Ayer Keroh, Malaysia"}, {"AuthorId": 4, "Name": "Wee How <PERSON>h", "Affiliation": "Faculty of Information Science and Technology, Multimedia University, Ayer Keroh, Malaysia"}, {"AuthorId": 5, "Name": "Fu <PERSON>", "Affiliation": "Infineon Technologies, Ayer Keroh, Malaysia"}], "References": [{"Title": "RETRACTED ARTICLE: EEG signal classification using LSTM and improved neural network algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "13", "Page": "9981", "JournalTitle": "Soft Computing"}, {"Title": "A 1D-CNN-Spectrogram Based Approach for Seizure Detection from EEG Signal", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "403", "JournalTitle": "Procedia Computer Science"}, {"Title": "Temporal convolutional neural (TCN) network for an effective weather forecasting using time-series data from the local weather station", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "21", "Page": "16453", "JournalTitle": "Soft Computing"}, {"Title": "A theoretical distribution analysis of synthetic minority oversampling technique (SMOTE) for imbalanced learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "113", "Issue": "7", "Page": "4903", "JournalTitle": "Machine Learning"}]}, {"ArticleId": 119742958, "Title": "Spatio-Temporal Dynamic Interlaced Network for 3D human pose estimation in video", "Abstract": "Recent transformer-based methods have achieved excellent performance in 3D human pose estimation. The distinguishing characteristic of transformer lies in its equitable treatment of each token, encoding them independently. When applied to the human skeleton, transformer regards each joint as an equally significant token. This can lead to a lack of clarity in the extraction of connection relationships between joints, thus affecting the accuracy of relationship information. In addition, transformer also treats each frame of temporal sequences equally. This design can introduce a lot of redundant information in short frames with frequent action changes, which can have a negative impact on learning temporal correlations. To alleviate the above issues, we propose an end-to-end framework, a Spatio-Temporal Dynamic Interlaced Network (S-TDINet), including a dynamic spatial GCN encoder (DSGCE) and an interlaced temporal transformer encoder (ITTE). In the DSGCE module, we design three adaptive adjacency matrices to model spatial correlation from static and dynamic perspectives. In the ITTE module, we introduce a global–local interlaced mechanism to mitigate potential interference from redundant information in fast motion scenarios, thereby achieving more accurate temporal correlation modeling. Finally, we conduct extensive experiments and validate the effectiveness of our approach on two widely recognized benchmark datasets: Human3.6M and MPI-INF-3DHP.", "Keywords": "", "DOI": "10.1016/j.cviu.2024.104258", "PubYear": 2025, "Volume": "251", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Internet of Things, Nanjing University of Posts and Telecommunications, No. 9 Wenyuan Road, Qixia District, Nanjing, Jiangsu, 210023, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Nanjing University of Posts and Telecommunications, No. 9 Wenyuan Road, Qixia District, Nanjing, Jiangsu, 210023, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Nanjing University of Posts and Telecommunications, No. 9 Wenyuan Road, Qixia District, Nanjing, Jiangsu, 210023, China"}, {"AuthorId": 4, "Name": "<PERSON> Qi", "Affiliation": "School of Internet of Things, Nanjing University of Posts and Telecommunications, No. 9 Wenyuan Road, Qixia District, Nanjing, Jiangsu, 210023, China"}, {"AuthorId": 5, "Name": "Zhenjiang Dong", "Affiliation": "School of Computer Science, Nanjing University of Posts and Telecommunications, No. 9 Wenyuan Road, Qixia District, Nanjing, Jiangsu, 210023, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Internet of Things, Nanjing University of Posts and Telecommunications, No. 9 Wenyuan Road, Qixia District, Nanjing, Jiangsu, 210023, China;Corresponding author"}], "References": [{"Title": "Automatic human posture estimation for sport activity recognition with robust body parts detection and entropy markov model", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "14", "Page": "21465", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "4DHumanOutfit: A multi-subject 4D dataset of human motion sequences in varying outfits exhibiting large displacements", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "237", "Issue": "", "Page": "103836", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "SlowFastFormer for 3D human pose estimation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "243", "Issue": "", "Page": "103992", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "DBMHT: A double-branch multi-hypothesis transformer for 3D human pose estimation in video", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "249", "Issue": "", "Page": "104147", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "M-adapter: Multi-level image-to-video adaptation for video action recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "249", "Issue": "", "Page": "104150", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 119743042, "Title": "Erratum to: Adaptive neural network control of a 2-DOF helicopter system considering input constraints and global prescribed performance", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11432-024-4236-x", "PubYear": 2024, "Volume": "67", "Issue": "12", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Guangzhou University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Guangzhou University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Intelligence Science and Technology and Key Laboratory of Intelligent Bionic Unmanned Systems of Ministry of Education, University of Science and Technology Beijing, Beijing, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Intelligence Science and Technology and Key Laboratory of Intelligent Bionic Unmanned Systems of Ministry of Education, University of Science and Technology Beijing, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, South China University of Technology, Guangzhou, China; Pazhou Lab, Guangzhou, China"}], "References": [{"Title": "Adaptive neural network control of a 2-DOF helicopter system considering input constraints and global prescribed performance", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "67", "Issue": "7", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 119743246, "Title": "Multi-domain conditional prior network for water-related optical image enhancement", "Abstract": "Water-related optical image enhancement improves the perception of information for human and machine vision, facilitating the development and utilization of marine resources. Due to the absorption and scattering of light in different water media, water-related optical images typically suffer from color distortion and low contrast. However, existing enhancement methods struggle to accurately simulate the imaging process in real underwater environments. To model and invert the degradation process of water-related optical images, we propose a Multi-domain Conditional Prior Network (MCPN) based on color vector prior and spectrum vector prior for enhancing water-related optical images. MCPN captures color, luminance, and structural priors across different feature spaces, resulting in a lightweight architecture that enhances water-related optical images while preserving critical information fidelity. Specifically, MCPN includes a modulated network, and a conditional network comprises two conditional units. The modulated network is a lightweight Convolutional Neural Network responsible for image reconstruction and local feature refinement. To avoid feature loss from multiple extractions, the Gaussian Conditional Unit (GCU) extracts atmospheric light and color shift information from the input image to form color prior vectors. Simultaneously, incorporating the Fast Fourier Transform, the Spectrum Conditional Unit (SCU) extracts scene brightness and structure to form spectrum prior vectors. These prior vectors are embedded into the modulated network to guide the image reconstruction. MCPN utilizes a PAL-based weighted Selective Supervision (PSS) strategy, selectively adjusting learning weights for images with excessive artificial noise. Experimental results demonstrate that MCPN outperforms existing methods, achieving excellent performance on the UIEB dataset. The PSS also shows fine feature matching in downstream applications.", "Keywords": "", "DOI": "10.1016/j.cviu.2024.104251", "PubYear": 2025, "Volume": "251", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics and Information Engineering, Liaoning Technical University, Huludao, 125105, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Dalian Maritime University, Dalian, 116026, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Dalian Maritime University, Dalian, 116026, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Dalian Maritime University, Dalian, 116026, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics and Information Engineering, Liaoning Technical University, Huludao, 125105, China;Corresponding author"}], "References": [{"Title": "Underwater scene prior inspired deep underwater image and video enhancement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107038", "JournalTitle": "Pattern Recognition"}, {"Title": "Underwater image enhancement using an edge-preserving filtering Retinex algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "17257", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Bayesian retinex underwater image enhancement", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104171", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Underwater image restoration via backscatter pixel prior and color compensation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "111", "Issue": "", "Page": "104785", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A multimodal hyper-fusion transformer for remote sensing image classification", "Authors": "Mengru Ma; <PERSON><PERSON>; Licheng Jiao", "PubYear": 2023, "Volume": "96", "Issue": "", "Page": "66", "JournalTitle": "Information Fusion"}, {"Title": "Uncertainty estimation in HDR imaging with Bayesian neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "156", "Issue": "", "Page": "110802", "JournalTitle": "Pattern Recognition"}, {"Title": "Underwater image enhancement method via multi-feature prior fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "14", "Page": "16435", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 119743281, "Title": "On the possibility of using the NNQS for the <PERSON> equation: О возможности применения метода NNQS для уравнения Клейна–Гордона–Фока", "Abstract": "<p>In this article, we present a method for finding quantum stationary states of the Klein–Gordon–Fock (KGF) equation using neural networks (NNs). The method has been tested on two well-known systems: a relativistic spinless particle in a Coulomb potential, and a one-dimensional relativistic harmonic oscillator. The results of training the neural network for these two systems are presented, as well as the analysis of the training process. The neural network method shows a good agreement with analytical calculations (if they can be found explicitly), providing a promising approach for solving more complex problems in quantum physics and quantum chemistry.</p><p>В этой статье мы представляем метод вычисления стационарных состояний уравнения Клейна-Гордона–Фока с помощью нейронных сетей. Метод был апробирован на двух хорошо известных системах: релятивистской бесспиновой частице в кулоновском потенциале и одномерном релятивистском гармоническом осцилляторе. Представлены результаты обучения нейронной сети для этих двух систем, а также анализ процесса обучения. Метод нейронных сетей показывает хорошее соответствие с результатами аналитических вычислений (если они могут быть найдены в явном виде), что открывает перспективы для решения более сложных задач в области квантовой физики и квантовой химии.</p>", "Keywords": "quantum mechanics;neural network;<PERSON> equation", "DOI": "10.26089/NumMet.v25r435", "PubYear": 2024, "Volume": "25", "Issue": "4", "JournalId": 90160, "JournalTitle": "Numerical Methods and Programming (Vychislitel'nye Metody i Programmirovanie)", "ISSN": "", "EISSN": "1726-3522", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Lomonosov Moscow State University, Faculty of Physics"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Lomonosov Moscow State University, Faculty of Physics"}], "References": []}, {"ArticleId": 119743379, "Title": "A free-standing, flexible electrospun nanofibrous membrane composed of gold nanoclusters for the detection of hydrogen peroxide and its real-time application in cancer detection", "Abstract": "Over the past decades, there is a rapid increase in the number of cancer incidences and mortality rates. The lack of sensitive, rapid, and early detection techniques is the main reason for the higher cancer occurrence rate. In this work, an electrospun nanofibrous membrane of polyvinyl alcohol-gold nanoclusters was developed for the primary detection of cancer cells. The nanofibers exhibited a peak maximum around 647 nm with better reproducibility and stability (90 days at 4 °C). The nanofibers were highly uniform, with an average fiber diameter of 291 ± 47 nm and uniform distribution of AuNCs throughout the nanofiber. The photoluminescence (PL) emission intensity has an inverse relation with the concentration of H<sub>2</sub>O<sub>2</sub> and could detect up to 47 pM with an R<sup>2</sup> value of 0.998, which is smaller than that of the reported optical sensing platforms based on the fluorescence technique. The quenching behaviour mechanism was elucidated through the use of PL, ultraviolet-visible spectroscopy, thermogravimetric analysis, X-ray photoelectron spectroscopy, and energy level diagrams. The fabricated nanofibers were found to be biocompatible with L929 cells. The nanofibers were further utilized for the primary detection of cancer cells, and a drastic reduction in PL intensity around four times lesser than that of normal cells was observed without activators. Spiking of normal cells with H<sub>2</sub>O<sub>2</sub> also exhibited the same behaviour as that of H<sub>2</sub>O<sub>2</sub> treated nanofibers. The present study gives new insights into a prototype that can be used for the rapid and primary detection of cancer cells for early diagnosis.", "Keywords": "", "DOI": "10.1016/j.snb.2024.137078", "PubYear": 2025, "Volume": "426", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Corporate Research and Development Centre (CRDC), HLL Lifecare Ltd, Akkulam, Thiruvananthapuram 695017, India;Faculty of Science, Cochin University of Science and Technology (CUSAT), Edapally, Ernakulam 682022, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Corporate Research and Development Centre (CRDC), HLL Lifecare Ltd, Akkulam, Thiruvananthapuram 695017, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Corporate Research and Development Centre (CRDC), HLL Lifecare Ltd, Akkulam, Thiruvananthapuram 695017, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Corporate Research and Development Centre (CRDC), HLL Lifecare Ltd, Akkulam, Thiruvananthapuram 695017, India;Corresponding author.;ORCID Code:, , https://orcid.org/0000-0003-0244-7255"}], "References": [{"Title": "Bovine serum albumin decorated gold nanoclusters: A fluorescence-based nanoprobe for detection of intracellular hydrogen peroxide", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "327", "Issue": "", "Page": "128886", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 119743440, "Title": "QC Speller: User Interface Design of a Hands-Free Touch-Free Speller with Brain Electroencephalogram Sensory Rhythm", "Abstract": "<p>Nowadays, brain-computer interfaces (BCIs) are being extensively explored by researchers to recover the abilities of motor-impaired individuals and improve their communication. Text entry is one of the most important regular tasks in communication, and BCI has high application potential to develop a speller. Although BCI has been a growing research topic for the last decade, more progress is yet to be made for BCI-based spellers. Two challenges are there in BCI speller development: designing an effective graphical user interface with an optimal arrangement of symbols enabling a minimum number of control commands and reducing users’ efforts in error correction following an efficient error-correction policy. With this scope in mind, this work proposes a novel BCI speller paradigm with an efficient symbol arrangement to improve the text entry rate. Additionally, it provides a user-friendly error correction approach through six text-cursor navigation keys to enhance the accuracy of text entry. The proposed speller includes 36 target symbols and is operated with two control commands obtained from electroencephalogram motor-imagery signals. The experimental results revealed that the proposed speller outperforms the existing motor imagery-based BCI spellers when tested on ten motor-impaired users. This speller achieved a mean performance of 5.20 characters per minute without any typing error and 6.04 characters per minute with a 2.9 percent mean error. The mean correction efficiency was 0.69; that is, users corrected 69 percent of incorrect inputs with a single correction key pressed.</p>", "Keywords": "", "DOI": "10.1145/3705733", "PubYear": 2024, "Volume": "17", "Issue": "4", "JournalId": 17450, "JournalTitle": "ACM Transactions on Accessible Computing", "ISSN": "1936-7228", "EISSN": "1936-7236", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Kharagpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Kharagpur, India"}], "References": [{"Title": "Survey on Brain-Computer Interface", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Passive Brain-Computer Interfaces for Enhanced Human-Robot Interaction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "125", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 119743462, "Title": "A temporal attention-based multi-scale generative adversarial network to fill gaps in time series of MODIS data for land surface phenology extraction", "Abstract": "High-quality and continuous satellite data are essential for land surface studies such as monitoring of land surface phenology, but factors such as cloud contamination and sensor malfunction degrade the quality of remote sensing images and limit their utilization. Filling gaps and recovering missing information in time series of remote sensing images are vital for a wide range of downstream applications, such as land surface phenology extraction. Most existing gap-filling and cloud removal methods focus on individual or multi-temporal image reconstruction, but struggle with continuous and overlapping missing areas in time series data. In this study, we propose a Temporal Attention-Based Multi-Scale Generative Adversarial Network (TAMGAN) to reconstruct time series of Moderate Resolution Imaging Spectroradiometer (MODIS) data. TAMGAN leverages a Generative Adversarial Network (GAN) with a 3-dimensional Convolution Neural Networks (3DCNN) in its generator to reconstruct the missing areas in the annual time series of remote sensing images simultaneously. The temporal attention blocks are designed to capture the changing trends of surface reflectance over time. And multi-scale feature extraction and progressive concatenation are introduced to enhance spectral consistency and provide detailed texture information. Experiments are carried out on MOD09A1 products to evaluate the performance of the proposed network. The results show that TAMGAN outperformed the comparison methods across various evaluation metrics, particularly in handling large and continuous missing areas in the time series. Furthermore, we showcase an example of downstream application by extracting phenological information from the gap-filled products. By effectively filling gaps and removing clouds, our method offers spatial-temporal continuous MODIS surface reflectance data, benefiting downstream applications such as phenology extraction and highlighting the potential of artificial intelligence technique in remote sense data processing.", "Keywords": "", "DOI": "10.1016/j.rse.2024.114546", "PubYear": 2025, "Volume": "318", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Key Laboratory for Urbanization and Geo-Simulation, Sun Yat-sen University, Guangzhou 510275, China;Department of Civil and Environmental Engineering, The Hong Kong University of Science and Technology, Hong Kong, SAR, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mining College, Guizhou University, Guiyang, Guizhou 550025, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Key Laboratory for Urbanization and Geo-Simulation, Sun Yat-sen University, Guangzhou 510275, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Future Urbanity & Sustainable Environment (FUSE) Lab, Division of Landscape Architecture, Department of Architecture, Faculty of Architecture, The University of Hong Kong, Hong Kong, SAR, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Remote Sensing and Geographical Information Systems, Peking University, Beijing 100871, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>an <PERSON>", "Affiliation": "Guangdong Key Laboratory for Urbanization and Geo-Simulation, Sun Yat-sen University, Guangzhou 510275, China;Corresponding author at: Sun Yat-Sen University, School of Geography and Planning B515, Guangzhou 510006, China"}], "References": [{"Title": "Long time-series NDVI reconstruction in cloud-prone regions via spatio-temporal tensor completion", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "264", "Issue": "", "Page": "112632", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A novel algorithm for the generation of gap-free time series by fusing harmonized Landsat 8 and Sentinel-2 observations with PhenoCam time series for detecting land surface phenology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "282", "Issue": "", "Page": "113275", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Deep learning for urban land use category classification: A review and experimental assessment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "311", "Issue": "", "Page": "114290", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 119743470, "Title": "Algorithmic trading strategy based on the integration of deep learning models and natural language processing", "Abstract": "", "Keywords": "", "DOI": "10.1007/s41060-024-00692-w", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 27236, "JournalTitle": "International Journal of Data Science and Analytics", "ISSN": "2364-415X", "EISSN": "2364-4168", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "CNN-FCM: System modeling promotes stability of deep learning in time series prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106081", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Use GBDT to Predict the Stock Market", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "174", "Issue": "", "Page": "161", "JournalTitle": "Procedia Computer Science"}, {"Title": "DTDR–ALSTM: Extracting dynamic time-delays to reconstruct multivariate data for improving attention-based LSTM industrial time series prediction models", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "211", "Issue": "", "Page": "106508", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Stock price prediction using deep learning and frequency decomposition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114332", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Analysis of news sentiments using natural language processing and deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "3", "Page": "931", "JournalTitle": "AI & SOCIETY"}, {"Title": "An application of deep reinforcement learning to algorithmic trading", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "114632", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Share price prediction of aerospace relevant companies with recurrent neural networks based on PCA", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115384", "JournalTitle": "Expert Systems with Applications"}, {"Title": "The impact of word sense disambiguation on stock price prediction", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115568", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Sentiment-influenced trading system based on multimodal deep reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107788", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep learning with small datasets: using autoencoders to address limited datasets in construction management", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107836", "JournalTitle": "Applied Soft Computing"}, {"Title": "Machine learning techniques and data for stock market forecasting: A literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "197", "Issue": "", "Page": "116659", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An ensemble of a boosted hybrid of deep learning models and technical analysis for forecasting stock prices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "594", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "A self-regulated generative adversarial network for stock price movement prediction based on the historical price and tweets", "Authors": "<PERSON><PERSON> Xu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "247", "Issue": "", "Page": "108712", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Investigating the informativeness of technical indicators and news sentiment in financial market price prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "247", "Issue": "", "Page": "108742", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Outperforming algorithmic trading reinforcement learning systems: A supervised approach to the cryptocurrency market", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "117259", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Factors affecting text mining based stock prediction: Text feature representations, machine learning models, and news platforms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "109673", "JournalTitle": "Applied Soft Computing"}, {"Title": "Extending machine learning prediction capabilities by explainable AI in financial time series prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "109876", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep Learning-based Integrated Framework for stock price movement prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109921", "JournalTitle": "Applied Soft Computing"}, {"Title": "Stock Prediction by Integrating Sentiment Scores of Financial News and MLP-Regressor: A Machine Learning Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "218", "Issue": "", "Page": "1067", "JournalTitle": "Procedia Computer Science"}, {"Title": "SSCDV: Social media document embedding with sentiment and topics for financial market forecasting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "245", "Issue": "", "Page": "122988", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Association mining based deep learning approach for financial time-series forecasting", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "155", "Issue": "", "Page": "111469", "JournalTitle": "Applied Soft Computing"}, {"Title": "Volatility forecasting and assessing risk of financial markets using multi-transformer neural network based architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "133", "Issue": "", "Page": "108223", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Explainable assessment of financial experts’ credibility by classifying social media forecasts and checking the predictions with actual market data", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "255", "Issue": "", "Page": "124515", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 119743640, "Title": "Navigate Career Thriving AI Powered Courses Guidance Junction with Students Reviews", "Abstract": "This project introduces a Student Review System, a web-based platform enabling students to share reviews on courses, professors, and facilities, fostering a valuable knowledge base for academic decision-making. Recognizing the challenges students face in choosing career paths, the Navigate Career Thriving AI-Powered Courses Guidance Junction with Student Reviews extends this concept by integrating peer and expert insights, personalized recommendations, and a real-time chatbot. The platform supports review creation, editing, and categorized searches, ensuring transparency and easy access to relevant insights. A user authentication system maintains credibility by preventing misuse. The chatbot provides interactive support, answering queries and offering tailored guidance aligned with individual interests. Additionally, analytical tools empower administrators to monitor feedback trends and address improvement areas, fostering institutional growth. This comprehensive system combines peer feedback and AI-driven recommendations to help students make confident, informed academic and career decisions.", "Keywords": "Student Review System;career guidance;AI-powered recommendation;peer reviews;chatbot integration;educational improvement", "DOI": "10.32628/CSEIT241061128", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "UG Students, Department of Computer Science and Engineering, SVERI’s College of Engineering, Pandharpur, Maharashtra, India"}, {"AuthorId": 2, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "UG Students, Department of Computer Science and Engineering, SVERI’s College of Engineering, Pandharpur, Maharashtra, India"}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": "UG Students, Department of Computer Science and Engineering, SVERI’s College of Engineering, Pandharpur, Maharashtra, India"}, {"AuthorId": 4, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "UG Students, Department of Computer Science and Engineering, SVERI’s College of Engineering, Pandharpur, Maharashtra, India"}, {"AuthorId": 5, "Name": " Sabiya Tamboli", "Affiliation": "UG Students, Department of Computer Science and Engineering, SVERI’s College of Engineering, Pandharpur, Maharashtra, India"}, {"AuthorId": 6, "Name": " <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Assistant Professor, Department of Computer Science and Engineering, SVERI’s College of Engineering, Pandharpur, Maharashtra, India"}], "References": []}, {"ArticleId": 119743830, "Title": "LiDAR Point Inpainting Model Using Smoothness Loss for SLAM in Dynamic Environments", "Abstract": "<p>Since performing simultaneous localization and mapping in dynamic environments is a challenging problem, conventional approaches have used preprocessing to detect and then remove movable objects from images. However, those methods create many holes in the places, where the movable objects are located, reducing the reliability of the estimated pose. In this paper, we propose a model with detailed classification criteria for moving objects and point cloud restoration to handle hole generation and pose errors. Our model includes a moving object segmentation network and an inpainting network with a light detection and ranging sensor. By providing residual images to the segmentation network, the model can classify idle and moving objects. Moreover, we propose a smoothness loss to ensure that the inpainting result of the model naturally connects to the existing background. Our proposed model uses the movable object’s information in an idle state and the inpainted background to accurately estimate the sensor’s pose. To use a ground truth dataset for inpainting, we created a new dataset using the CARLA simulation environment. We use our virtual datasets and the KITTI dataset to verify our model’s performance. In a dynamic environment, our proposed model demonstrates a notable enhancement of approximately 24.7% in pose estimation performance compared to the previous method.</p>", "Keywords": "", "DOI": "10.1177/17298806241297424", "PubYear": 2024, "Volume": "21", "Issue": "6", "JournalId": 1212, "JournalTitle": "International Journal of Advanced Robotic Systems", "ISSN": "1729-8814", "EISSN": "1729-8814", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Han", "Affiliation": "Department of Robotics Engineering, Kwangwoon University, Seoul, South Korea"}, {"AuthorId": 2, "Name": "I <PERSON> Putra <PERSON>", "Affiliation": "Department of Robotics Engineering, Kwangwoon University, Seoul, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Robotics Engineering, Kwangwoon University, Seoul, South Korea"}], "References": []}, {"ArticleId": 119743953, "Title": "Multi-optimization scheme for in-situ training of memristor neural network based on contrastive learning", "Abstract": "<p>Memristor and its crossbar structure have been widely studied and proven to be naturally suitable for implementing vector-matrix multiplier (VMM) operation in neural networks, making it one of the ideal underlying hardware when deploying models on edge smart devices. However, the problem of receiving much useless information is common and the non-ideal characteristics will also affect the system training accuracy and efficiency. Considering these problems, We combine the contrastive learning (CL) into in-situ training process on the memristor crossbar, improving the model feature extraction capability and robustness. Meanwhile, to make the contrastive learning integrate with the crossbar better, we proposed a multi-optimization scheme on the network loss function, model deployment method, and gradient calculation process. We also proposed some compensation strategies to address the key non-ideal characteristics we analyzed and fitted. The test results show that under the scheme proposed, the model for deployment has a high accuracy value at the beginning, reaching 83.18% in only 2 epochs, and can quickly achieve an accuracy of 3.99% increase compared to the average accuracy of the existing algorithms with the energy consumption reduced by about 8 times.</p>", "Keywords": "In-situ training; Contrastive learning; Memristor crossbar; Neural network", "DOI": "10.1007/s10489-024-05957-2", "PubYear": 2025, "Volume": "55", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence, Southwest University, Chongqing, China; Chongqing Key Laboratory of Brain-inspired Computing and Intelligent Chips, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence, Southwest University, Chongqing, China; Chongqing Key Laboratory of Brain-inspired Computing and Intelligent Chips, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence, Southwest University, Chongqing, China; Chongqing Key Laboratory of Brain-inspired Computing and Intelligent Chips, Chongqing, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence, Southwest University, Chongqing, China; Chongqing Key Laboratory of Brain-inspired Computing and Intelligent Chips, Chongqing, China"}], "References": [{"Title": "SSM: a high-performance scheme for in situ training of imprecise memristor neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "407", "Issue": "", "Page": "270", "JournalTitle": "Neurocomputing"}, {"Title": "Introducing ‘Neuromorphic Computing and Engineering’", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "010401", "JournalTitle": "Neuromorphic Computing and Engineering"}, {"Title": "Reduction 93.7% time and power consumption using a memristor-based imprecise gradient update algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "1", "Page": "657", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Accurate inference of gene regulatory interactions from spatial gene expression with deep contrastive learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "3", "Page": "746", "JournalTitle": "Bioinformatics"}, {"Title": "Opportunities for neuromorphic computing algorithms and applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "1", "Page": "10", "JournalTitle": "Nature Computational Science"}, {"Title": "Feed-Forward learning algorithm for resistive memories", "Authors": "<PERSON>; Phrangbokla<PERSON> L<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "102730", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Contrastive learning-based semantic segmentation for In-situ stratified defect detection in additive manufacturing", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "68", "Issue": "", "Page": "465", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 119744001, "Title": "Sentiment and hashtag-aware attentive deep neural network for multimodal post popularity prediction", "Abstract": "<p>Social media users articulate their opinions on a broad spectrum of subjects and share their experiences through posts comprising multiple modes of expression, leading to a notable surge in such multimodal content on social media platforms. Nonetheless, accurately forecasting the popularity of these posts presents a considerable challenge. Prevailing methodologies primarily center on the content itself, thereby overlooking the wealth of information encapsulated within alternative modalities such as visual demographics, sentiments conveyed through hashtags and adequately modeling the intricate relationships among hashtags, texts, and accompanying images. This oversight limits the ability to capture emotional connection and audience relevance, significantly influencing post popularity. To address these limitations, we propose a seNtiment and hAshtag-aware attentive deep neuRal netwoRk for multimodAl posT pOpularity pRediction, herein referred to as NARRATOR that extracts visual demographics from faces appearing in images and discerns sentiment from hashtag usage, providing a more comprehensive understanding of factors influencing post popularity. Moreover, we introduce a hashtag-guided attention mechanism that leverages hashtags as navigational cues, guiding the model’s focus toward the most pertinent features of textual and visual modalities, thus aligning with target audience interests and broader social media context. Experimental results demonstrate that NARRATOR outperforms existing methods by a significant margin on two real-world datasets. Furthermore, ablation studies underscore the efficacy of integrating visual demographics, sentiment analysis of hashtags, and hashtag-guided attention mechanisms in enhancing the performance of post popularity prediction, thereby facilitating increased audience relevance, emotional engagement, and aesthetic appeal.</p>", "Keywords": "Multimodal data analysis; Hashtags; Sentiment; Deep neural network; Social media analysis", "DOI": "10.1007/s00521-024-10755-5", "PubYear": 2025, "Volume": "37", "Issue": "4", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Indore, Indore, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Indore, Indore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Indore, Indore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Indore, Indore, India"}], "References": [{"Title": "Instagram Post Popularity Trend Analysis and Prediction using Hashtag, Image Assessment, and User History Features", "Authors": "", "PubYear": 2020, "Volume": "18", "Issue": "1", "Page": "85", "JournalTitle": "The International Arab Journal of Information Technology"}, {"Title": "Hashtag recommendation for enhancing the popularity of social media posts", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "13", "Issue": "1", "Page": "21", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Social media popularity prediction with multimodal hierarchical fusion model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "80", "Issue": "", "Page": "101490", "JournalTitle": "Computer Speech & Language"}, {"Title": "Social media popularity prediction with multimodal hierarchical fusion model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "80", "Issue": "", "Page": "101490", "JournalTitle": "Computer Speech & Language"}, {"Title": "Popularity Prediction Model With Context, Time and User Sentiment Information: An Optimization Assisted Deep Learning Technique", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "2", "Page": "283", "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems"}, {"Title": "Popularity Prediction Model With Context, Time and User Sentiment Information: An Optimization Assisted Deep Learning Technique", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "2", "Page": "283", "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems"}]}, {"ArticleId": 119744006, "Title": "Autoregressive multimodal transformer for zero-shot sales forecasting of fashion products with exogenous data", "Abstract": "<p>Predicting future sales volumes of fashion industry products is challenging due to rapid market changes and limited historical sales data for recent products. As traditional forecasting methods and machine learning models often fail to address this problem, we propose a novel autoregressive multimodal transformer architecture to anticipate the sales volume of brand-new apparel items by capturing trends among interrelated attributes. In this paper, we utilize authentic data from a fashion company that includes a limited amount of historical time-series sales data and several influencing factors like product image, textual descriptions, and temporal attributes. To mitigate the data inadequacies, we investigate the impact of integrating exogenous knowledge from an e-tailer site filtered with fashion apparel products. Also, we found that employing the zero-shot forecasting approach further aids in forecasting with minimal time-series sales data. Our approach achieves the values of 1.546 and 16.42 in terms of MAE and WAPE, respectively, by leveraging exogenous data compared to existing benchmark models. This study demonstrates the potential of our autoregressive multimodal transformer to predict sales volumes with more precision, and it highlights the importance of incorporating the zero-shot forecasting approach in the dynamic fashion industry.</p>", "Keywords": "Zero-shot sales forecasting; Multimodal learning; Multimodal retrieval; Transformer-based architecture; Influence of exogenous data", "DOI": "10.1007/s10489-024-05972-3", "PubYear": 2025, "Volume": "55", "Issue": "2", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Convergence Engineering, Pusan National University, Busan, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Convergence Engineering, Pusan National University, Busan, South Korea; Corresponding author."}], "References": [{"Title": "Study on convolutional neural network and its application in data mining and sales forecasting for E-commerce", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "2", "Page": "297", "JournalTitle": "Electronic Commerce Research"}, {"Title": "A data aggregation based approach to exploit dynamic spatio-temporal correlations for citywide crowd flows prediction in fog computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "20", "Page": "31401", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Fashion Meets Computer Vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Exploiting dynamic spatio-temporal correlations for citywide traffic flow prediction using attention based neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "577", "Issue": "", "Page": "852", "JournalTitle": "Information Sciences"}, {"Title": "Deep Learning for Demand Forecasting in the Fashion and Apparel Retail Industry", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "2", "Page": "565", "JournalTitle": "Forecasting"}, {"Title": "Dilated Transformer with Feature Aggregation Module for Action Segmentation", "Authors": "<PERSON><PERSON><PERSON> Du; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "5", "Page": "6181", "JournalTitle": "Neural Processing Letters"}, {"Title": "A novel soft attention-based multi-modal deep learning framework for multi-label skin lesion classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "120", "Issue": "", "Page": "105897", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A feature aggregation network for multispectral pedestrian detection", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "19", "Page": "22117", "JournalTitle": "Applied Intelligence"}, {"Title": "Unveiling consumer preferences: A two-stage deep learning approach to enhance accuracy in multi-channel retail sales forecasting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "257", "Issue": "", "Page": "125066", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 119744007, "Title": "Identification of Epileptic Seizures Utilising a Computationally Powerful Spiking Neuron", "Abstract": "<p>Epilepsy is considered a complex neurological condition that causes the central nervous system to malfunction, which results in seizures. Therefore, in-depth identification of the condition with high accuracy is required, and Electroencephalogram (EEG) plays a vital role. To classify seizures from the EEG signals, a computationally efficient Spiking Neural Networks (SNN) classifier inspired by WOLIF that utilises the error-function adjusted by the Grey Wolf Optimizer (GWO) and derived from spiking neurons with Leaky Integrate and Fire (LIF) is employed in this paper. It works on the principle of SNN, which is computationally very efficient and does not need hidden layer(s) even though the data is highly non-linear, such as EEG signals. The features are extracted from the raw EEG signals utilising the Discrete Wavelet Transform (DWT). Then, the information is encoded using the population coding technique since SNN works with temporal information. This study makes use of the epileptic seizures dataset from Bonn University. Four binary classifications between healthy and seizure classes from five data sets were done. Unlike WOLIF, ( lpha) -kernel is used as the synapse model rather than the double-decaying kernel function, improving the overall accuracy. The results shown by the proposed approach are very appealing if both computational efficiency and accuracy are considered the metrics for analysis, which is very important.</p>", "Keywords": "Epilepsy; EEG; DWT; WOLIF; Computationally efficient; SNN", "DOI": "10.1007/s42979-024-03510-z", "PubYear": 2024, "Volume": "5", "Issue": "8", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Information Technology, Siks<PERSON> <PERSON><PERSON> (Deemed to be University), Bhubaneswar, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science and Engineering, National Institute of Technology Silchar, Silchar, India"}], "References": [{"Title": "A comprehensive comparison of handcrafted features and convolutional autoencoders for epileptic seizures detection in EEG signals", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "163", "Issue": "", "Page": "113788", "JournalTitle": "Expert Systems with Applications"}, {"Title": "WOLIF: An efficiently tuned classifier that learns to classify non-linear temporal patterns without hidden layers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "4", "Page": "2173", "JournalTitle": "Applied Intelligence"}, {"Title": "Fuzzy-Based Automatic Epileptic Seizure Detection Framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "3", "Page": "5601", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 119744012, "Title": "Learning and aggregating principal semantics for semantic edge detection in images", "Abstract": "Learning features that contain both local variations and non-local semantics is crucial yet challenging for Semantic Edge Detection (SED), which involves the joint localization and categorization of edges in images. Directly fusing multi-stage detail-to-abstract pixel features from encoders as done in existing SED methods leads to feature representation with either blurred details or limited semantics. In this paper, we present a novel Transformer-based framework for SED, named SEDTR. Our approach employs an efficient semantic aggregation strategy achieved through the design of the Principal Semantics Learning (PSL) module and the Principal Semantics Aggregation (PSA) module. Specifically, the PSL module distills concise principal semantics from high-level features. Subsequently, the multi-stage pixel features are spatially tuned by aggregating semantic clues from the learned principal representations in PSA. Benefiting from the concise principal representation, noise is effectively suppressed while edge points are distinctly highlighted. Additionally, we enhance the pixel features with cross-level complements before PSA to facilitate the semantic aggregation. The PSA-tuned multi-stage features are summed to form the final features for SED. Extensive comparative experiments conducted on the SBD, Cityscapes and ADE20K datasets demonstrate that the proposed model outperforms existing approaches in both edge localization and categorization.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126082", "PubYear": 2025, "Volume": "265", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing University of Technology, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Beijing University of Technology, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing University of Technology, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Machine Perception (MOE), School of Electronics Engineering and Computer Science, Peking University, China"}], "References": [{"Title": "Deep Parametric Active Contour Model for Neurofibromatosis Segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "58", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Multi-scale spatial context-based semantic edge detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "238", "JournalTitle": "Information Fusion"}, {"Title": "DSANet: Dilated spatial attention for real-time semantic segmentation in urban street scenes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115090", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Semantic Edge Detection with Diverse Deep Supervision", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "1", "Page": "179", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Refined edge detection with cascaded and high-resolution convolutional network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "109361", "JournalTitle": "Pattern Recognition"}, {"Title": "Progressive deep snake for instance boundary extraction in medical images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "249", "Issue": "", "Page": "123590", "JournalTitle": "Expert Systems with Applications"}, {"Title": "P2AT: Pyramid pooling axial transformer for real-time semantic segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "255", "Issue": "", "Page": "124610", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dual-modal non-local context guided multi-stage fusion for indoor RGB-D semantic segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "255", "Issue": "", "Page": "124598", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 119744087, "Title": "Implementing the Link‐Cut Tree", "Abstract": "We consider the challenge of implementing the link‐cut tree data structure. This data structure is well known and has had a wide impact on several theoretical results. However, there is a gap in mixed practical applications. We have recently used this data structure to generate uniform spanning trees and to compute a feedback vertex set. In this kind of application, the theoretical performance of the data structure is relevant but only when verified in practice. We thus present two implementations. One implementation is based on pointers and obtains good experimental performance for small problems. The other implementation is based on splay trees and provides amortized worst‐case guarantees. We use a simple API that allows us to explain the expected functionality. Moreover, it is designed to extract full functionality from the data structure while at the same time being as simple and compact as possible, in particular, it provides support for the cycle processing required by our applications. We give an experimental evaluation of both implementations. For completeness, we also review the theoretical analysis of this structure and obtain entropy and static finger bounds.", "Keywords": "", "DOI": "10.1002/spe.3393", "PubYear": 2025, "Volume": "55", "Issue": "4", "JournalId": 1840, "JournalTitle": "Software: Practice and Experience", "ISSN": "0038-0644", "EISSN": "1097-024X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> M<PERSON> S<PERSON>", "Affiliation": "Instituto de Engenharia de Sistemas e Computadores, Investigação e Desenvolvimento em Lisboa (INESC‐ID)  Lisboa Portugal;Instituto Superior Técnico Universidade de Lisboa  Lisboa Portugal"}], "References": [{"Title": "Searching for a Feedback Vertex Set with the link-cut tree", "Authors": "<PERSON><PERSON> M<PERSON>S<PERSON>", "PubYear": 2023, "Volume": "72", "Issue": "", "Page": "102110", "JournalTitle": "Journal of Computational Science"}]}, {"ArticleId": 119744126, "Title": "Sliding mode preview control for discrete SIR model based on modified Euler method", "Abstract": "In this paper, for a class of SIR models with saturated incidence, the SIR model is discretized using the modified <PERSON><PERSON><PERSON> method to form a form in which the coefficient matrix contains unknown parameters. The augmented error system method is constructed and the future information is simulated as a feed-forward and compensated into the SIR model, and the output regulation method of the linear discrete system is used to design the non-singular terminal sliding mode surface and the exponential convergence law, and the suitable performance index is given to obtain the optimal sliding mode preview controller. Finally, numerical simulation is utilized to verify the effectiveness of the theory and methodology of this paper.", "Keywords": "Modified Euler method; non-singular terminal sliding mode surface; preview control; optimal control; saturated incidence", "DOI": "10.3934/mfc.2024049", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Yuan Li", "Affiliation": "School of Science, Shenyang University of Technology, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Science, Shenyang University of Technology, China"}], "References": []}, {"ArticleId": 119744214, "Title": "The Use of Eye-Tracking to Explore the Relationship Between Consumers’ Gaze Behaviour and Their Choice Process", "Abstract": "<p>Eye-tracking technology can assist researchers in understanding motivational decision-making and choice processes by analysing consumers’ gaze behaviour. Previous studies showed that attention is related to decision, as the preferred stimulus is generally the most observed and the last visited before a decision is made. In this work, the relationship between gaze behaviour and decision-making was explored using eye-tracking technology. Images of six wardrobes incorporating different sustainable design strategies were presented to 57 subjects, who were tasked with selecting the wardrobe they intended to keep the longest. The amount of time spent looking was higher when it was the chosen version. Detailed analyses of gaze plots and heat maps derived from eye-tracking records were employed to identify different patterns of gaze behaviour during the selection process. These patterns included alternating attention between a few versions or comparing them against a reference, allowing the identification of stimuli that initially piqued interest but were ultimately not chosen, as well as potential doubts in the decision-making process. These findings suggest that doubts that arise before making a selection warrant further investigation. By identifying stimuli that attract attention but are not chosen, this study provides valuable insights into consumer behaviour and decision-making processes.</p>", "Keywords": "", "DOI": "10.3390/bdcc8120184", "PubYear": 2024, "Volume": "8", "Issue": "12", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering and Construction, Universitat Jaume I, 12071 Castelló, Spain"}, {"AuthorId": 2, "Name": "<PERSON>Porcar", "Affiliation": "Department of Mechanical Engineering and Construction, Universitat Jaume I, 12071 Castelló, Spain"}], "References": []}, {"ArticleId": 119744315, "Title": "Quantitative Measurement of Cyber Resilience: Modeling and Experimentation", "Abstract": "<p> Abstract Cyber resilience is the ability of a system to resist and recover from a cyber attack, thereby restoring the system's functionality. Effective design and development of a cyber resilient system requires experimental methods and tools for quantitative measuring of cyber resilience. This paper describes an experimental method and test bed for obtaining resilience-relevant data as a system (in our case – a truck) traverses its route, in repeatable, systematic experiments. We model a truck equipped with an autonomous cyber-defense system and which also includes inherent physical resilience features. When attacked by malware, this ensemble of cyber-physical features (i.e., “bonware”) strives to resist and recover from the performance degradation caused by the malware's attack. We propose parsimonious mathematical models to aid in quantifying systems’ resilience to cyber attacks. Using the models, we identify quantitative characteristics obtainable from experimental data, and show that these characteristics can serve as useful quantitative measures of cyber resilience. </p>", "Keywords": "", "DOI": "10.1145/3703159", "PubYear": 2025, "Volume": "9", "Issue": "1", "JournalId": 41094, "JournalTitle": "ACM Transactions on Cyber-Physical Systems", "ISSN": "2378-962X", "EISSN": "2378-9638", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "US Army Combat Capabilities Development Command, Army Research Laboratory, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "US Army Combat Capabilities Development Command, Army Research Laboratory, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "US Army Combat Capabilities Development Command, Army Research Laboratory, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Penn State University, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "ICF International, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "US Army Combat Capabilities Development Command, Army Research Laboratory, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of California, Irvine, USA"}], "References": [{"Title": "Cyber resilience in firms, organizations and societies", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100204", "JournalTitle": "Internet of Things"}, {"Title": "Reinforcement Learning for feedback-enabled cyber resilience", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "273", "JournalTitle": "Annual Reviews in Control"}]}, {"ArticleId": 119744333, "Title": "Service engineering for quantum computing: Ensuring high-quality quantum services", "Abstract": "<b >Context:</b> Quantum computing is transforming the world and driving advanced applications in fields such as healthcare and economics. However, ensuring high-quality quantum software remains critical to its adoption across the industry. As quantum technology moves closer to practical applications, it faces significant challenges. Developers face platform-dependent complexities that make the creation of quantum applications a time-consuming process. In addition, the lack of mature tools further hampers progress and can compromise the quality of service. <b >Objective:</b> The objective of this paper is to address the pressing need for quantum software quality assurance, presenting a solution for defining and using quantum services, by employing classical service engineering techniques and methods. <b >Methods:</b> A process is presented for improving the generation, deployment, and quality assessment of quantum services using an extended OpenAPI Specification and the SonarQube tool. This process also integrates the automatic generation of code for the IBM Quantum provider and its deployment in containers ready for user consumption. <b >Results:</b> After a detailed and individualized evaluation of the 40 implementations of quantum algorithms using the developed environment, the results reveal significant variability in the analyzability of the algorithms. This will serve in the future as a reference and guide for the continuous improvement of quantum algorithms in terms of their performance and efficiency in solving complex problems in various quantum application areas. <b >Conclusions:</b> This research offers a fundamental contribution to the evolution of quantum computing by introducing a comprehensive framework for quantum software quality assurance. The proposed approach not only addresses some of the existing problems in quantum software, but also paves the way for the development of quantum algorithms and their servitization.", "Keywords": "", "DOI": "10.1016/j.infsof.2024.107643", "PubYear": 2025, "Volume": "179", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Alarcos Research Group, Universidad de Castilla-La Mancha, Ciudad Real, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Quercus Software Engineering Group, Universidad de Extremadura, Cáceres, Spain;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Quercus Software Engineering Group, Universidad de Extremadura, Cáceres, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Quercus Software Engineering Group, Universidad de Extremadura, Cáceres, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Quercus Software Engineering Group, Universidad de Extremadura, Cáceres, Spain"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Alarcos Research Group, Universidad de Castilla-La Mancha, Ciudad Real, Spain"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Alarcos Research Group, Universidad de Castilla-La Mancha, Ciudad Real, Spain"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Quercus Software Engineering Group, Universidad de Extremadura, Cáceres, Spain"}], "References": [{"Title": "Quantum computing: A taxonomy, systematic review and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "1", "Page": "66", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Quantum service-oriented computing: current landscape and challenges", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "30", "Issue": "4", "Page": "983", "JournalTitle": "Software Quality Journal"}, {"Title": "Software architecture for quantum computing systems — A systematic review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "201", "Issue": "", "Page": "111682", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Let’s do it right the first time: Survey on security concerns in the way to quantum software engineering", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "538", "Issue": "", "Page": "126199", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 119744354, "Title": "Robots and AI: a new economic era. Edited by <PERSON><PERSON> and <PERSON> (2022). Published by Routledge, London ISBN: 9781003275534. https://doi.org/10.4324/9781003275534 (open access)", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00146-024-02154-0", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "Eliaza <PERSON>", "Affiliation": ""}], "References": [{"Title": "Artificial Intelligence (AI) Ethics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "74", "JournalTitle": "Journal of Database Management"}, {"Title": "Robots, artificial intelligence, and service automation (RAISA) in hospitality: sentiment analysis of YouTube streaming data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "1", "Page": "259", "JournalTitle": "Electronic Markets"}, {"Title": "Can Artificial Intelligence Boost Employment in Service Industries? Empirical Analysis Based on China", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>ng <PERSON>ai", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "2080336", "JournalTitle": "Applied Artificial Intelligence"}]}, {"ArticleId": 119744417, "Title": "RETRACTION : Segmentation and Classification of Lymphoblastic Leukaemia Using Quantum Neural Network", "Abstract": "RETRACTION : <PERSON><PERSON> , <PERSON><PERSON> <PERSON><PERSON> , <PERSON><PERSON> , <PERSON><PERSON> <PERSON><PERSON> , “,” Expert Systems (Early View): , https://doi.org/10.1111/exsy.13225 . \nThe above article, published online on 23 December 2022 in Wiley Online Library ( wileyonlinelibrary.com ), has been retracted by agreement between the journal Editor‐in‐Chief, <PERSON>; and John Wiley & Sons Ltd. The article was submitted as part of a guest‐edited special issue. Following publication, it has come to our attention that the article was not reviewed in line with the journal's peer review standards. Furthermore, the use of quantum computing in this manuscript is insufficiently described, and the experimental methods and its supporting information lack sufficient detail to reproduce the findings. The authors disagree with the retraction.", "Keywords": "", "DOI": "10.1111/exsy.13815", "PubYear": 2025, "Volume": "42", "Issue": "2", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 119744719, "Title": "Semantic similarity on multimodal data: A comprehensive survey with applications", "Abstract": "Recently, the revival of the semantic similarity concept has been featured by the rapidly growing artificial intelligence research fueled by advanced deep learning architectures enabling machine intelligence using multimodal data. Thus, semantic similarity in multimodal data has gained substantial attention among researchers. However, the existing surveys on semantic similarity measures are restricted to a single modality, mainly text, which significantly limits the capability to understand the intelligence of real-world application scenarios. This study critically reviews semantic similarity approaches by shortlisting 223 vital articles from the leading databases and digital libraries to offer a comprehensive and systematic literature survey. The notable contribution is to illuminate the evolving landscape of semantic similarity and its crucial role in understanding, interpreting, and extracting meaningful information from multimodal data. Primarily, it highlights the challenges and opportunities inherent in different modalities, emphasizing the significance of advancements in cross-modal and multimodal semantic similarity approaches with potential application scenarios. Finally, the survey concludes by summarizing valuable future research directions. The insights provided in this survey improve the understanding and pave the way for further innovation by guiding researchers in leveraging the strength of semantic similarity for an extensive range of real-world applications.", "Keywords": "Semantic Similarity; Similarity Measures; Multimodal Semantic Similarity; Semantic Similarity Applications; Machine Learning; And Deep Learning", "DOI": "10.1016/j.jksuci.2024.102263", "PubYear": 2024, "Volume": "36", "Issue": "10", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of AI & NLP, Department of Computer Science, College of Science, Mathematics and Technology, Wenzhou-Kean University, 88 Daxue Road, Ouhai, Wenzhou 325060, Zhejiang Province, China;Corresponding author at: 88 Daxue Road, Ouhai, Wenzhou 325060, Zhejiang Province, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Science, Mathematics and Technology, Wenzhou-Kean University, 88 Daxue Road, Ouhai, Wenzhou 325060, Zhejiang Province, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, College of Science, Mathematics and Technology, Wenzhou-Kean University, 88 Daxue Road, Ouhai, Wenzhou 325060, Zhejiang Province, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, College of Art & Sciences, Universiti Utara Malaysia (UUM), Sintok, 06010, Kedah, Malaysia"}], "References": [{"Title": "Beyond context: Exploring semantic similarity for small object detection in crowded scenes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "53", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A novel framework for measuring software quality-in-use based on semantic similarity and sentiment analysis of software reviews", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "113", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Effective spatio-temporal semantic trajectory generation for similar pattern group identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "287", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "A multilingual fuzzy approach for classifying Twitter data using fuzzy logic and semantic similarity", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "12", "Page": "8655", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Semantically Coherent 4D Scene Flow of Dynamic Scenes", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "319", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Hybrid query expansion using lexical resources and word embeddings for sentence retrieval in question answering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "514", "Issue": "", "Page": "88", "JournalTitle": "Information Sciences"}, {"Title": "Improving named entity recognition in noisy user-generated text with local distance neighbor feature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "382", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Deep semantic similarity adversarial hashing for cross-modal retrieval", "Authors": "<PERSON><PERSON><PERSON>ang; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "400", "Issue": "", "Page": "24", "JournalTitle": "Neurocomputing"}, {"Title": "Automatic keyphrase extraction using word embeddings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "8", "Page": "5593", "JournalTitle": "Soft Computing"}, {"Title": "Multi-modal egocentric activity recognition using multi-kernel learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "11", "Page": "16299", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multimodal machine translation through visuals and speech", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Stig-<PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "2-3", "Page": "97", "JournalTitle": "Machine Translation"}, {"Title": "Multimodal machine translation through visuals and speech", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Stig-<PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "2-3", "Page": "97", "JournalTitle": "Machine Translation"}, {"Title": "Measurement of Text Similarity: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "9", "Page": "421", "JournalTitle": "Information"}, {"Title": "A novel approach for automatic Bengali question answering system using semantic similarity analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "4", "Page": "873", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "RETRACTED: A Hybrid Semantic Similarity Measurement for Geospatial Entities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "", "Page": "103526", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Short text similarity measurement methods: a review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "6", "Page": "4699", "JournalTitle": "Soft Computing"}, {"Title": "Evolution of Semantic Similarity—A Survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "TripleRank: An unsupervised keyphrase extraction algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "219", "Issue": "", "Page": "106846", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-modal visual adversarial Bayesian personalized ranking model for recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "572", "Issue": "", "Page": "378", "JournalTitle": "Information Sciences"}, {"Title": "Deep learning based semantic personalized recommendation system", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100028", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Deep learning based semantic personalized recommendation system", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100028", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Knowledge-based sentence semantic similarity: algebraical properties", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "43", "JournalTitle": "Progress in Artificial Intelligence"}, {"Title": "Word Embedding based Textual Semantic Similarity Measure in Bengali", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "193", "Issue": "", "Page": "92", "JournalTitle": "Procedia Computer Science"}, {"Title": "Only overlay text: novel features for TV news broadcast video segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "21", "Page": "30493", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Semantic similarity information discrimination for video captioning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "118985", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Improved Feature Extraction and Similarity Algorithm for Video Object Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Haihua Tang", "PubYear": 2023, "Volume": "14", "Issue": "2", "Page": "115", "JournalTitle": "Information"}, {"Title": "SSM: Stylometric and semantic similarity oriented multimodal fake news detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "5", "Page": "101559", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Cross-modal hash retrieval based on semantic multiple similarity learning and interactive projection matrix learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "648", "Issue": "", "Page": "119571", "JournalTitle": "Information Sciences"}, {"Title": "Optimized Algorithm Design for Text similarity Detection Based on Artificial Intelligence and Natural Language Processing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "228", "Issue": "", "Page": "195", "JournalTitle": "Procedia Computer Science"}, {"Title": "Retraction Note: Simulation of cross-modal image-text retrieval algorithm under convolutional neural network structure and hash method", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "80", "Issue": "9", "Page": "13497", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 119744747, "Title": "ExpDrug: An explainable drug recommendation model based on space feature mapping", "Abstract": "Drug recommendation uses AI technology to combine a patient’s electronic health records with medical knowledge to help doctors recommend safer and more accurate drug combinations. Time-dependence of patients’ historical records plays a crucial role in existing methods, but some of them ignore or disregard the explainability of the recommendation, and some recommendation results are accompanied by high drug-drug interactions (DDIs). Therefore, an explainable drug recommendation model(ExpDrug) with low DDIs is proposed in this article. It maps the ”black-box” features of patient diagnoses, procedures and medications to the explainable aspect features, and minimizes both rating prediction loss and explainable loss to improve recommendation performance. In addition, a controllable threshold strategy is proposed to reduce the DDI rate. Extensive experiments are conducted on the MIMIC-III dataset, and the proposed ExpDrug achieves new state-of-the-art performance across safety, explainability and four accuracy metrics. The source code is publicly available at https://github.com/hyh0606/ExpDrug .", "Keywords": "", "DOI": "10.1016/j.neucom.2024.129021", "PubYear": 2025, "Volume": "619", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Physics and Electronic Engineering, Shanxi University, Taiyuan, 030006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Physics and Electronic Engineering, Shanxi University, Taiyuan, 030006, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Big Data Science and Industry, Shanxi University, Taiyuan, 030006, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Zhu", "Affiliation": "Institute of Big Data Science and Industry, Shanxi University, Taiyuan, 030006, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Big Data Science and Industry, Shanxi University, Taiyuan, 030006, China"}], "References": [{"Title": "SMR: Medical Knowledge Graph Embedding for Safe Medicine Recommendation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "", "Page": "100174", "JournalTitle": "Big Data Research"}, {"Title": "Explainable recommendation based on knowledge graph and multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "3", "Page": "1241", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "On the current state of deep learning for news recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "2", "Page": "1101", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Safe, effective and explainable drug recommendation based on medical data integration", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "5", "Page": "999", "JournalTitle": "User Modeling and User-Adapted Interaction"}, {"Title": "MEGACare: Knowledge-guided multi-view hypergraph predictive framework for healthcare", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "100", "Issue": "", "Page": "101939", "JournalTitle": "Information Fusion"}, {"Title": "StratMed: Relevance stratification between biomedical entities for sparsity on medication recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "284", "Issue": "", "Page": "111239", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 119744773, "Title": "MEVDT: Multi-Modal Event-Based Vehicle Detection and Tracking Dataset", "Abstract": "<p>In this data article, we introduce the Multi-Modal Event-based Vehicle Detection and Tracking (MEVDT) dataset. This dataset provides a synchronized stream of event data and grayscale images of traffic scenes, captured using the Dynamic and Active-Pixel Vision Sensor (DAVIS) 240c hybrid event-based camera. MEVDT comprises 63 multi-modal sequences with approximately 13k images, 5M events, 10k object labels, and 85 unique object tracking trajectories. Additionally, MEVDT includes manually annotated ground truth labels - consisting of object classifications, pixel-precise bounding boxes, and unique object IDs - which are provided at a labeling frequency of 24 Hz. Designed to advance the research in the domain of event-based vision, MEVDT aims to address the critical need for high-quality, real-world annotated datasets that enable the development and evaluation of object detection and tracking algorithms in automotive environments.</p><p>© 2024 The Author(s).</p>", "Keywords": "Computer vision;Event-based vision;Multimodal;Object detection;Object tracking", "DOI": "10.1016/j.dib.2024.111205", "PubYear": 2025, "Volume": "58", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Michigan-Dearborn, 4901 Evergreen Rd, Dearborn, 48128 MI, USA."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Michigan-Dearborn, 4901 Evergreen Rd, Dearborn, 48128 MI, USA."}], "References": [{"Title": "MOTChallenge: A Benchmark for Single-Camera Multiple Target Tracking", "Authors": "<PERSON>; Aljos̆a Os̆ep; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "4", "Page": "845", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "A Reconfigurable Architecture for Real-time Event-based Multi-Object Tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>-<PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Reconfigurable Technology and Systems"}]}]