[{"ArticleId": 109357393, "Title": "Quasi-synchronization and stabilization of discrete-time fractional-order memristive neural networks with time delays", "Abstract": "In this article, the quasi-synchronization (Q-S) and stabilization of a sort of discrete-time fractional-order memristive neural networks (DFMNNs) with time delays are considered. First of all, the definitions of ħ -discrete fractional operator are given, some basic properties of difference operators are gained, and then according to the above definitions and properties, several flesh inequalities of discrete-time fractional-order difference are established, which greatly expands the existing results. In addition, by use of L<PERSON><PERSON>nov direct method and some related lemmas obtained above, the adequate Q-S criteria are obtained for DFMNNs with time delays under the delay feedback controller . At the same time, we also yield the relevant conditions of stabilization. Finally, the validity and availability of the above theoretical results are verified by numerical simulations. Introduction In the last few decades, it is well known that various of neural network (NN) models have been put forward in succession and broadly put into using sign and image processing [1], associative memory [2] and so on, which is the main objective of simulating the function of the human brain via computers. It is worth noting that the classical circuits of NNs are established with resistors. Nevertheless, the scholars have found that resistors were not the perfect device because their values have immutable traits. Fortunately, in [3], <PERSON><PERSON> first came up with individual resistor called the memristor which is able to memorize the quality of charge. Now that the prominent advantages of low energy consumption, rapid speed of processing and nanoscale dimension, experiments show that memristor is an ideal device for constructing synapses of artificial neural networks. Therefore, the introduction of memristor into NNs can better simulate the human brain, which makes it more practical to study the dynamic behaviors of memristive NNs (MNNs) [4], [5], [6], [7]. Numerous scientific studies have shown that most systems in nature are of fractional order, and fractional-order mathematical models are more accurate than integer-order ones to describe the real world. On the one hand, Auastasio [8] has indicated that the oculomotor integrator which translates the decreasing eye speed into the eye position commands may be of fractional order. That is to say, some dynamic characteristics of motor and premotor neurons can be depicted in terms of fractional differentiation with respect to eye position, which is beneficial to efficient information processing of neurons. On the other hand, actual capacitors or inductors have fractional characteristics in the field electronic science. Considering the above nature, it is necessary and feasible to introduce fractional order into MNNs for improving the accuracy of the models. In addition, it is inevitable to encounter time delays, which may lead to more complex dynamic behaviors of the actual system in the process of signal transmission [9], [10]. Coupled with the finiteness of amplifier switching speed in NNs, the existence of time delays is universal, so the study of FMNNs with time delays has important theoretical and practical significance. Now, many scholars are becoming interested in this, and have obtained more and more excellent results [11], [12], [13], [14], [15]. Since stabilization and synchronization are typical behaviors in nature, they have always been the important topics for many scholars to study. Coupled with their potential applications in physics [16], engineering [17] and other fields, studies on them have been more and more extensive. As is known to all, the study of synchronization problems is mainly based on that two or more than two system parameters are identical, however, all system parameters are consistent which are often based on some assumptions of ideal, while parameters are often not completely consistent in real life, based on the above consideration, it stimulates the interest of scholars in Q-S problem. Through design of the appropriate controllers, the slave system can always track the movement trajectory of the master system. This significant advantage of Q-S is often applied in the fields of autonomous navigation and remote operation of multiple autonomous underwater vehicles [18]. Therefore, the deep exploration of Q-S problem has practical applications. In addition, because MNNs can generate a large number of chaotic factors under certain initial conditions, how to synchronize two chaotic systems has become a key problem of chaotic secure communication, synchronization of MNNs has been extensively studied, many meaningful achievements have been achieved [19], [20], [21], [22]. On the other hand, stabilization problem is regarded as a special synchronization problem, that is, to stabilize the considered system to the equilibrium point by designing controllers. We know that in the hardware execution of MNNs, the existence of time delays may destroy the stability of the system, because stability of the system is a necessary condition to ensure its normal operation, so stabilization of MNNs with time delays plays an important practical role, and some achievements have been made [23], [24], [25]. Then, the studies on stabilization and synchronization of FMNNs with time delays are still in the development stage because the connection weights of MNNs with time delays have the characteristics of switching. Therefore, stabilization and synchronization of FMNNs with time delays is still the important research topics in current control theory. It is not hard to see that most of the outstanding works on fractional NNs are concerned with continuous-time systems. However, it is worth mentioning that neural networks are essentially learning algorithms. In the implementation, the digital computer needs to use discrete iteration to perform relevant data calculation. On the other hand, as pointed out in [26], discrete fractional calculus does not cause any numerical error, which is inevitable in the numerical discretization of continuous fractional calculus. Therefore, from the relevant theoretical research to practical application, it is necessary and indispensable to study discrete-time fractional neural networks (DFNNs), and some rich achievements have been achieved [27], [28], [29], [30], [31]. For example, the authors in [32] not only proved discrete-time fractional generalized Halanay inequalities with bounded time delays, but also obtained quasi-synchronization and complete synchronization criteria for DFNNs by using above inequalities and Lyapunov direct method. In [33], the authors gained the discrete Grüss type inequalities through the related properties and definitions of ħ -discrete fractional calculus, which was used for qualitative analysis of the difference equations. Several kinds of discrete-time fractional difference inequalities were established, and new Lyapunov functions were constructed by using the inequalities, ultimately, the asymptotic stability conditions of discrete systems were acquired in [34]. However, as far as we know, there are few research results on synchronization and stabilization of DFMNNs, let alone ħ -difference case. Inspired via the aforesaid discourse, this paper inquires into Q-S and stabilization for delayed DFMNNs by right of delay feedback controller and adaptive controller. The main conspicuous contributions of the manuscript are summarized as follows: (1) Instead of using the maximum absolute value method to deal with the memristor connection weights in [27], this paper introduces uncertain parameters into DFNNs with appropriate changes. (2) Some properties of ħ -difference operator, such as integration by parts and difference operation properties of monomial function, are given by some definitions of ħ -difference operator. (3) By using some of the properties we proved earlier, several novel fractional ħ -difference inequalities are established, which greatly extend the existing results in [29], [35]. (4) This paper constructs a Lyapunov function which is different from the traditional 2-norm. It is based on the ρ -norm and is more general and flexible than the previous results in [32], [34]. (5) By using the time-delay controller and the adaptive control law, synchronization criteria and stabilization conditions of the considered systems are obtained based on the Lyapunov direct method and above gained inequalities. The rest of the manuscript is scheduled as below. Some definitions are raised, and some neoteric fractional difference inequalities are proved as well as DFMNNs are introduced in Section 2. Synchronization criteria are gained and conditions about stabilization are obtained in Section 3. Numerical examples are given to prove validity of the above theory in Section 4. The simple conclusion is got in Section 5. Section snippets Preliminaries and model description Notations ∇ ħ − ν 0 denotes the Caputo-like nabla fractional difference of order ν ( 0 < ν < 1 ) . R is the set of real numbers. We define ( ħ N ) 0 = { m | m = 0 , ħ , 2 ħ , ⋯ } , ( ħ N ) 0 b = { m | m = 0 , ħ , 2 ħ , ⋯ , b , where b = k ħ , k ∈ N + } . Let R n be the set all n -dimensional real vector. The p -norm of x is delimitated as ‖ x ‖ p = ( ∑ j = 1 n ( sup τ ≤ k ≤ 0 ⁡ | x j ( k ) | p ) ) 1 p , for any x = ( x 1 , x 2 , … , x n ) T ∈ R n , sign ( ⋅ ) is the symbolic function. In this section, we will introduce some necessary definitions and properties about discrete fractional-order difference calculation and Main results In this section, we explore Q-S and stabilization issues for DFMNNs. Number simulations In the current portion, we give two numerical examples to certify theoretical results. Example 1 Consider following three-dimensional DFMNN ∇ 0.01 0.95 0 C ϕ k ( r ) = − p k ϕ k ( r ) + ∑ q = 1 3 a k q ( ϕ k ( r ) ) 0.5 × tanh ⁡ ( ϕ q ( r ) ) + ∑ q = 1 3 b k q ( ϕ k ( r ) ) 0.5 × tanh ⁡ ( ϕ q ( r − τ ) ) , in which k = 1 , 2 , 3 , p 1 = 0.25 , p 2 = 0.25 , p 3 = 0.53 , τ = 0.5 , and memristive connection weights are given below: a 11 ( ϕ k ( r ) ) = { − 0.1995 , | ϕ k ( r ) | ≤ 2 , − 0.1357 , | ϕ k ( r ) | > 2 , a 12 ( ϕ k ( r ) ) = { 0.1894 , | ϕ k ( r ) | ≤ 2 , 0.2595 , | ϕ k ( r ) | > 2 . a 13 ( ϕ k ( r ) ) = { − 0.1197 , | ϕ k ( r ) | ≤ 2 , − 0.0165 , | ϕ k ( r ) | > 2 , a 21 ( ϕ k ( r ) ) = { − 0.1692 , | ϕ k ( r ) | ≤ 2 , − 0.1887 Conclusion In this paper, the delayed feedback controller and adaptive controller are designed, and Q-S criteria and stabilization conditions are obtained respectively. It is worth mentioning that some difference inequalities are established based on the properties of ħ -order difference operators, which enriches and improves some related results. Finally, the feasibility of the results is verified by two numerical examples. In addition, with the continuous improvement of the system function, the system CRediT authorship contribution statement Xiao-Li Zhang: Conceptualization, Methodology, Validation, Writing – original draft, Writing – review & editing. Hong-Li Li: Conceptualization, Methodology, Validation, Writing – review & editing. Yongguang Yu: Software, Writing – review & editing. Zuolei Wang: Writing – review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements This work is supported by the National Natural Science Foundation of China (Grant No. 12262035 ), Tianshan Youth Program-Training Program for Excellent Young Scientific and Technological Talents (Grant No. 2019Q017 ). References (41) Y. Huang et al. Finite-time passivity and synchronization of coupled complex-valued memristive neural networks Inf. Sci. (2021) Z. Xu et al. Exponential stability of nonlinear state-dependent delayed impulsive systems with applications Nonlinear Anal. Hybrid Syst. (2021) J. Xiao et al. Novel methods to finite-time Mittag-Leffler synchronization problem of fractional-order quaternion-valued neural networks Inf. Sci. (2020) F. Du et al. New criterion for finite-time synchronization of fractional order memristor-based neural networks with time delay Appl. Math. Comput. (2021) J. Jia et al. Pinning synchronization of fractional-order memristor-based neural networks with multiple time-varying delays via static or dynamic coupling J. Franklin Inst. (2021) Q. Li et al. Global polynomial stabilization of proportional delayed inertial memristive neural networks Inf. Sci. (2023) F. Wei et al. Finite-time stabilization of memristor-based inertial neural networks with time-varying delays combined with interval matrix method Knowl.-Based Syst. (2021) C. Zhou et al. Cluster output synchronization for memristive neural networks Inf. Sci. (2022) X. Yang et al. Quasi-uniform synchronization of fractional-order memristor-based neural networks with delay Neurocomputing (2017) Y. Cao et al. Synchronization of multiple reaction-diffusion memristive neural networks with known or unknown parameters and switching topologies Knowl.-Based Syst. (2022) L. Chen et al. Stability and synchronization of fractional-order memristive neural networks with multiple delays Neural Netw. (2017) G. Wu et al. Lyapunov functions for Riemann-Liouville-like fractional difference equations Appl. Math. Comput. (2017) R. Li et al. Quasi-stability and quasi-synchronization control of quaternion-valued fractional-order discrete-time memristive neural networks Appl. Math. Comput. (2021) X. You et al. Global Mittag-Leffler stability and synchronization of discrete-time fractional-order complex-valued neural networks with time delay Neural Netw. (2020) Y. Chen et al. Finite-time stability of abc type fractional delay difference equations Chaos Solitons Fractals (2021) S. Rashid et al. Novel aspects of discrete dynamical type inequalities within fractional operators having generalized ħ -discrete Mittag-Leffler kernels and application Chaos Solitons Fractals (2021) Y. Wei et al. Lyapunov functions for nabla discrete fractional order systems ISA Trans. (2019) X. Zhang et al. Global Mittag-Leffler synchronization of discrete-time fractional-order neural networks with time delays Appl. Math. Comput. (2022) Z. Chen et al. Event-based fuzzy control for T-S fuzzy networked systems with various data missing Neurocomputing (2020) X. Hu et al. A memristive multilayer cellular neural network with applications to image processing IEEE Trans. Neural Netw. Learn. Syst. (2017) View more references Cited by (0) Recommended articles (6) Research article A holistic valorization of food waste for sustainable biofuel production Valorization of Wastes for Sustainable Development, 2023, pp. 137-154 Show abstract The need for renewable energy with minimal greenhouse gas emissions has increased due to rising demand for usage, increasing adverse effects associated with combustion of fossil fuels and fossil fuel shortage. About one-third of food produced is being wasted before human consumption, with almost all of it presently being burned. Since food waste is insanely rich in organic and nutritional components, it can be highly recommended as a potential feedstock for the manufacture of biofuel. Lot of research has been done on the characteristics of food waste and its management. The most promising solution appears to be the conversion of food waste into biofuel. This strategy has the ability to lessen reliance on petroleum-based products, stabilize food prices, and address society’s attitude about food waste in addition to food waste management. The current situation of food waste, its properties, and the most creative use as a feedstock for biofuels are all summarized in this chapter. Research article Adaptive quasi-synchronization analysis for Caputo delayed Cohen–Grossberg neural networks Mathematics and Computers in Simulation, Volume 212, 2023, pp. 49-65 Show abstract The quasi-synchronization (QS) issues for Caputo delayed Cohen–Grossberg neural networks (CGNNs) are discussed in this article. To begin with, a novel lemma is established by constructing suitable fractional differential inequality. Due to the advantages of adaptive control schemes with reducing control cost and having high tracking accuracy, two different adaptive controllers are designed, respectively. Applying the proposed lemma, inequality techniques and Lagrange’s mean value theorem, the conditions of QS are obtained by selecting appropriate Lyapunov functions. Finally, two numerical examples in different dimensions are shown to test the correctness of the gained theorems. Research article Fixed-time synchronization for delayed inertial complex-valued neural networks Applied Mathematics and Computation, Volume 405, 2021, Article 126272 Show abstract This paper researches the problem of p-norm fixed-time synchronization for a class of delayed inertial complex-valued neural networks (ICVNNs). By using reduced-order transformation and separating real and imaginary parts of complex-valued parameters , the second-order ICVNNs can be converted into the form of first-order real-valued differential equations . Then some new flexible and adjustable algebraic criteria to ensure the fixed-time synchronization of ICVNNs are established by means of the non-smooth Lyapunov function and inequality analytical techniques. Moreover, the settling time of fixed-time synchronization is theoretically estimated, which does not depend on the initial value of systems. Finally, simulation examples and applications are presented to illustrate the validity and availability of the obtained results. Research article New results of quasi-projective synchronization for fractional-order complex-valued neural networks with leakage and discrete delays Chaos, Solitons & Fractals, Volume 159, 2022, Article 112121 Show abstract In this paper, the non-decomposition method is employed to investigate the quasi-projective synchronization of fractional-order complex-valued neural networks (FOCVNNs) with leakage and discrete delays. Firstly, two new inequalities are established in complex domain, which provides a powerful tool to explore the synchronization and stability of complex-valued systems. Secondly, by means of the Banach fixed point theorem, the existence and uniqueness of solution of the delayed FOCVNNs is discussed under certain conditions. Thirdly, a linear complex-valued controller is designed to induce quasi-projective synchronization of the delayed FOCVNNs, and some novel results are given by using the presented inequalities, the non-decomposition method and the Lyapunov stability theory. Further, the error bounds are estimated. It is found that a smaller error bound can be obtained by appropriately increasing the feedback gains. Finally, two numerical examples are given to verify the effectiveness of the theoretical results and the practicability of the synchronization strategy in secure communication. Research article Quasi-projective synchronization of discrete-time fractional-order quaternion-valued neural networks Journal of the Franklin Institute, Volume 360, Issue 4, 2023, pp. 3263-3279 Show abstract In this article, without decomposing the quaternion-valued neural networks (QVNNs) into two complex-valued subsystems or four real-valued subsystems, quasi-projective synchronization of discrete-time fractional-order QVNNs is investigated. To this end, the sign function for quaternion number is introduced and some related properties are given. Then, two inequalities are built according to the nabla fractional difference and quaternion theory. Subsequently, a simple linear quaternion-valued controller is designed, and some synchronization conditions are given by means of our created inequalities. Finally, numerical simulations are given to prove the feasibility and correctness of the theoretical results. Research article Unified dissipativity state estimation for delayed generalized impulsive neural networks with leakage delay effects Knowledge-Based Systems, Volume 254, 2022, Article 109630 Show abstract This paper examined for the first time the challenge of unified dissipativity state estimation of delayed generalized impulsive neural networks (GINNs) with leaky delay effects. To estimate the upper bounds of the Lyapunov Krasovskii functionals (LKFs), the tighter integral inequality (TII) and reciprocally convex inequality (RCI) approaches are employed, and certain criteria are provided in the form of linear matrix inequalities (LMIs). As a response, a new delayed-dependent criterion for establishing whether the estimated error network is unified dissipative is developed. By modifying the values of the suitable matrices, the notion of a unified dissipativity-based estimating state may be used to find the multi-dynamic state estimation of GINNs. The recommended technique’s benefit is displayed by multiple numerical examples, one of which was sponsored by a practical implementation of the benchmark problem (i.e., the quadruple-tank process system (QTPS)) that is involved with reasonable issues, particularly the impulse impacts in the sense of a unified dissipativity performance. View full text © 2023 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119461", "PubYear": 2023, "Volume": "647", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi 830017, China"}, {"AuthorId": 2, "Name": "Hong-<PERSON> Li", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi 830017, China;School of Mathematics, Southeast University, Nanjing 210096, China;Corresponding author at: College of Mathematics and System Sciences, Xinjiang University, Urumqi 830017, China"}, {"AuthorId": 3, "Name": "Yongguang Yu", "Affiliation": "School of Mathematics and Statistics, Beijing Jiaotong University, Beijing 100044, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Yancheng Teachers University, Yancheng 224002, China"}], "References": [{"Title": "Optimal quasi-synchronization of fractional-order memristive neural networks with PSOA", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "13", "Page": "9667", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Novel methods to finite-time Mittag-<PERSON><PERSON> synchronization problem of fractional-order quaternion-valued neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "526", "Issue": "", "Page": "221", "JournalTitle": "Information Sciences"}, {"Title": "Event-based fuzzy control for T-S fuzzy networked systems with various data missing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "417", "Issue": "", "Page": "322", "JournalTitle": "Neurocomputing"}, {"Title": "Synchronization analysis for discrete fractional-order complex-valued neural networks with time delays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "16", "Page": "10503", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Exponential stability of nonlinear state-dependent delayed impulsive systems with applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "", "Page": "101088", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}, {"Title": "Finite-time stabilization of memristor-based inertial neural networks with time-varying delays combined with interval matrix method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "230", "Issue": "", "Page": "107395", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Finite-time passivity and synchronization of coupled complex-valued memristive neural networks", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "775", "JournalTitle": "Information Sciences"}, {"Title": "Cluster output synchronization for memristive neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "459", "JournalTitle": "Information Sciences"}, {"Title": "Synchronization of multiple reaction–diffusion memristive neural networks with known or unknown parameters and switching topologies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "254", "Issue": "", "Page": "109595", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Global polynomial stabilization of proportional delayed inertial memristive neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "623", "Issue": "", "Page": "729", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 109357402, "Title": "Applicability of UAVs in detecting and monitoring burning residue of paddy crops with IoT Integration: A step towards greener environment", "Abstract": "The disposal of paddy-based residue in some parts of Indian north-western states, particularly in Punjab and Haryana, has emerged as a significant issue, leading farmers to resort to on-site burning. This practice wastes valuable resources, has detrimental effects on the local economy, and poses a global environmental challenge. The consequences of burning agricultural residues include soil erosion, carbon sequestration problems, and a negative impact on the ecosystem. Recognizing the importance of managing paddy residue due to its nutrient content and its contribution to soil–plant-atmosphere continuity, this article aims to quantify the amount of residue generated and shed light on the adverse effects of residue burning on human health, soil quality, and the environment in the region. Employing drone technology, the study presents an experimental case based on data collected from selected states in India. Real-time monitoring of agricultural land using unmanned aerial vehicles (UAVs) enables the acquisition of crucial data on paddy residue. The collected images are then transferred to the cloud for analysis and decision-making purposes. By leveraging this approach, the study achieves an accuracy of 96.67% in analysing paddy crop waste and identifying areas where residue burning occurs. The proposed work offers timely assistance to state authorities in pinpointing burning locations promptly, and taking necessary measures accordingly. This monitoring initiative not only contributes to climate control efforts but also aids in combating the rising AQI levels of air pollution, and potentially exploring other means of monitoring various environmental factors.", "Keywords": "", "DOI": "10.1016/j.cie.2023.109524", "PubYear": 2023, "Volume": "184", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computer Technologies and Information Security, Southern Federal University, Taganrog 347922, Russia;Chitkara University, Punjab, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Institute of Management Studies (NMIMS), Computer Engineering, STME, Chandigarh, India"}], "References": [{"Title": "Evolving deep neural networks using coevolutionary algorithms with multi-population strategy", "Authors": "<PERSON>een<PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "13051", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Customer segmentation using K-means clustering and the adaptive particle swarm optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107924", "JournalTitle": "Applied Soft Computing"}, {"Title": "An IoT and Machine Learning Based Intelligent System for the Classification of Therapeutic Plants", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "5", "Page": "4465", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 109357429, "Title": "The Dynamic Spillover Effects of Natural Gas Prices and the United States-Europe LNG Freight Rates", "Abstract": "With the changes in the energy structure of various countries around the world, the demand for natural gas in the world has increased, and the LNG sea transportation market, which is more convenient than pipeline transportation, is gradually expanding. In addition, after the Russia-Ukraine conflict in 2022, the total amount of LNG imported from the United States to Europe soared. This paper takes the U.S. and Europe LNG markets as an example to study the impact of LNG prices on LNG freight rates. This article uses the TVP-VAR-DY model to analyze weekly data from 2019/01/04 to 2023/03/24 and concludes that LNG freight rates receive volatility spillovers from LNG futures and spot prices, with LNG freight rates receiving more volatility spillovers during periods of significant LNG price fluctuations; Secondly, the LNG shipping market is gradually expanding, not only as a receiver of fluctuations, but also as a spillover party of fluctuations.", "Keywords": "LNG spot price ; LNG futures prices ; LNG freight rate ; TVP-VAR-DY model", "DOI": "10.1016/j.procs.2023.08.051", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Maritime University, Shanghai, 201306, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Maritime University, Shanghai, 201306, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Shanghai Maritime University, Shanghai, 201306, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shanghai Maritime University, Shanghai, 201306, China"}], "References": []}, {"ArticleId": 109357431, "Title": "Modelling mutual exclusion in a process algebra with time-outs", "Abstract": "I show that in a standard process algebra extended with time-outs one can correctly model mutual exclusion in such a way that starvation-freedom holds without assuming fairness or justness, even when one makes the problem more challenging by assuming memory accesses to be atomic. This can be achieved only when dropping the requirement of speed independence.", "Keywords": "Mutual exclusion ; Safe registers ; Overlapping reads and writes ; Atomicity ; Speed independence ; Reactive temporal logic ; Kripke structures ; Progress ; Justness ; Fairness ; Safety properties ; Blocking ; Fair schedulers ; Process algebra ; CCS ; Time-outs ; Labelled transition systems ; Petri nets ; Asymmetric concurrency relations ; <PERSON>'s protocol", "DOI": "10.1016/j.ic.2023.105079", "PubYear": 2023, "Volume": "294", "Issue": "", "JournalId": 8189, "JournalTitle": "Information and Computation", "ISSN": "0890-5401", "EISSN": "1090-2651", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Data61, CSIRO, Sydney, Australia;School of Informatics, University of Edinburgh, UK;School of Computer Science and Engineering, University of New South Wales, Sydney, Australia;Correspondence to: School of Informatics, University of Edinburgh, UK"}], "References": []}, {"ArticleId": *********, "Title": "Global Imbalances: Updating Mechanism Review and Descriptive Analysis", "Abstract": "Global imbalance is a phenomenon that one country has a large current account deficit and some other countries have a large current account surplus. The expansion of global imbalance will aggravate trade frictions, especially between surplus countries and deficit countries, and promote trade protectionism in deficit countries, which will bring great risks to global economic growth and financial stability. This paper summarized the existing theories of global imbalances, analyzed the causes and mechanisms of global imbalances, studied the scale and concentration of global imbalances in different historical stages. The results showed that since 1980, the scale of global imbalances has experienced three stages: stability (1980-1995), continuous increase (1996-2008) and effective mitigation (2009-present), and the concentration of global imbalances has fluctuated. The United States has maintained its position as a major deficit country since 1980.", "Keywords": "global imbalance ; concentration ; dynamic change", "DOI": "10.1016/j.procs.2023.08.015", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Yang", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, No. 80 Zhongguancun East Road, Haidian District, Beijing, 100190, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, No. 80 Zhongguancun East Road, Haidian District, Beijing, 100190, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, No. 80 Zhongguancun East Road, Haidian District, Beijing, 100190, China"}], "References": []}, {"ArticleId": 109357454, "Title": "Are Large Traders Harmed by Front-running HFTs?", "Abstract": "This paper studies the influences of a high-frequency trader (HFT) on a large trader whose future trading is predicted by the former. We conclude that HFT always front-runs and the large trader is benefited when: (1) there is sufficient high-speed noise trading; (2) HFT's prediction is vague enough. Besides, we find surprisingly that (1) making HFT's prediction less accurate might decrease large trader's profit; (2) when there is little high-speed noise trading, although HFT nearly does nothing, the large trader is still hurt.", "Keywords": "High-frequency trading ; Prediction ; Front-running ; Large informed trader", "DOI": "10.1016/j.procs.2023.08.007", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LMEQF, Department of Financial Mathematics, School of Mathematical Sciences , Peking University , Beijing 100871 , China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "LMEQF, Department of Financial Mathematics, School of Mathematical Sciences , Peking University , Beijing 100871 , China"}], "References": []}, {"ArticleId": 109357463, "Title": "Identifying Factors and the Relationship between Problematic Social Media Use and Anxieties in Instagram Users: A Deep Investigation-based Dual-stage SEM-ANN Analysis", "Abstract": "This paper aims to identify the factors and the relationship between Problematic Social Media Use (Compulsion and Withdrawal) and Anxieties in Instagram users. The research is descriptive, with a quantitative approach developed through a survey (n=757). After a multi-analytical approach using Covariance-based Structural Equation Modeling (CB-SEM) validated the model, results became inputs to an Artificial Neural Network (ANN) model to predict the Anxieties. Findings pointed out that the most relevant constructs were Shared Content Anxiety and Self Evaluation Anxiety, and the neural network provided empirical evidence to support that Compulsion is the most relevant predictor for Anxieties, increasing its effects. The introduction of this new methodology and the theoretical contribution of the proposed hybrid model opens perspectives of the existing body of knowledge in the literature related to understanding phenomena such as the relationship between Problematic Social Media Use and Anxieties in Instagram users.", "Keywords": "mental health ; social media ; Instagram ; Structural Equation Modeling; Artificial Neural Network", "DOI": "10.1016/j.procs.2023.08.002", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Gabriele Abrantes de Almeida", "Affiliation": "Federal University of Sao Paulo, Osasco, SP 06120-042, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal University of Sao Paulo, Osasco, SP 06120-042, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Sao Paulo, Sao Paulo, SP 05508-010, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Federal University of Sao Paulo, Osasco, SP 06120-042, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Federal University of Sao Paulo, Osasco, SP 06120-042, Brazil"}], "References": [{"Title": "The COVID-19 pandemic: The ‘black swan’ for mental health care and a turning point for e-health", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "", "Page": "100317", "JournalTitle": "Internet Interventions"}, {"Title": "Internet addiction in young adults: A meta-analysis and systematic review", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "107201", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 109357478, "Title": "Measurement of collaboration with agile practices in a Virtual Learning Environment", "Abstract": "Virtual Learning Environments (VLE) are collaborative workspaces for teachers and students, such as Moodle and Google Classroom. However, in many VLEs with online classes, there is a perceived lack of collaboration and student engagement. The goal of our research is to identify the factors that facilitate or hinder collaboration in Google Classroom. The methodology adopted was a survey questionnaire, with 31 questions applied to Public Higher Education Institutions in the State of Rio de Janeiro, with 39 participants. Google Classroom proved to be a good collaborative tool e complies with Agile principles. The benchmark testing performed allowed us to identify the criteria that educators should pay more attention to throughout the courses.", "Keywords": "Agile management ; Benchmark testing ; Collaborative work ; Computer Supported Cooperative Work (CSCW) ; Virtual Learning Environment (VLE)", "DOI": "10.1016/j.procs.2023.07.005", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidade Federal do Rio de Janeiro, Instituto de Computação, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universidade Federal do Rio de Janeiro, Instituto de Computação, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal do Rio de Janeiro, Instituto de Computação, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidade Federal do Rio de Janeiro, Instituto de Computação, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal do Rio de Janeiro, Instituto de Computação, Brazil"}], "References": [{"Title": "CLIQ! Intelligent Question Classifier for the elaboration of exams", "Authors": "<PERSON>; <PERSON>; Mônica F. Silva", "PubYear": 2022, "Volume": "13", "Issue": "", "Page": "100345", "JournalTitle": "Software Impacts"}, {"Title": "Collaborizer: The sizer of the agile collaboration", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "", "Page": "100371", "JournalTitle": "Software Impacts"}]}, {"ArticleId": 109357485, "Title": "Editorial - special issue on autonomy, safety, and security for cyber-physical systems in the process industries", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.dche.2023.100117", "PubYear": 2023, "Volume": "8", "Issue": "", "JournalId": 90723, "JournalTitle": "Digital Chemical Engineering", "ISSN": "2772-5081", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chemical and Biomolecular Engineering, National University of Singapore, Singapore"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Materials Science, Wayne State University, Michigan, USA;Corresponding author"}], "References": [{"Title": "Computational fluid dynamics modeling of a wafer etch temperature control system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "", "Page": "100102", "JournalTitle": "Digital Chemical Engineering"}]}, {"ArticleId": 109357521, "Title": "The method of constructing basic-element base using large language model- Take the issue of rice waste", "Abstract": "The rapid development of artificial intelligence technology has led to the emergence of large language models such as ChatGPT represented by natural language processing technology, but currently there is no effective way to input all the information to be exchanged. In this paper, a method of constructing local basic-element base of input information by combining the large language model with extenics is proposed. Taking rice waste problem as an example, the method is successfully applied to a practical project to verify the feasibility of the method.", "Keywords": "Large language model;Extenics;Basic-element base;Rice waste;ChatGPT", "DOI": "10.1016/j.procs.2023.08.012", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "WA<PERSON>-hao", "Affiliation": "College of Electromechanical Engineering, Guangdong University of Technology, Guangzhou 510006, China;Institute of Extenics and Innovation Methods, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 2, "Name": "LI Ding-jie", "Affiliation": "College of Electromechanical Engineering, Guangdong University of Technology, Guangzhou 510006, China;Institute of Extenics and Innovation Methods, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 3, "Name": "LI Xing-sen", "Affiliation": "College of Electromechanical Engineering, Guangdong University of Technology, Guangzhou 510006, China;Institute of Extenics and Innovation Methods, Guangdong University of Technology, Guangzhou 510006, China"}], "References": [{"Title": "ChatGPT in medicine: an overview of its applications, advantages, limitations, future prospects, and ethical considerations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "", "Page": "1169595", "JournalTitle": "Frontiers in Artificial Intelligence"}]}, {"ArticleId": 109357553, "Title": "Motion-aware and data-independent model based multi-view 3D pose refinement for volleyball spike analysis", "Abstract": "In the volleyball game, estimating the 3D pose of the spiker is very valuable for training and analysis, because the spiker’s technique level determines the scoring or not of a round. The development of computer vision provides the possibility for the acquisition of the 3D pose. Most conventional pose estimation works are data-dependent methods, which mainly focus on reaching a high level on the dataset with the controllable scene, but fail to get good results in the wild real volleyball competition scene because of the lack of large labelled data, abnormal pose, occlusion and overlap. To refine the inaccurate estimated pose, this paper proposes a motion-aware and data-independent method based on a calibrated multi-camera system for a real volleyball competition scene. The proposed methods consist of three key components: 1) By utilizing the relationship of multi-views, an irrelevant projection based potential joint restore approach is proposed, which refines the wrong pose of one view with the other three views projected information to reduce the influence of occlusion and overlap. 2) Instead of training with a large amount labelled data, the proposed motion-aware method utilizes the similarity of specific motion in sports to achieve construct a spike model. Based on the spike model, joint and trajectory matching is proposed for coarse refinement. 3) To finely refine, a point distribution based posterior decision network is proposed. While expanding the receptive field, the pose estimation task is decomposed into a classification decision problem, which greatly avoids the dependence on a large amount of labelled data. The experimental dataset videos with four synchronous camera views are from a real game, the Game of 2014 Japan Inter High School of Men Volleyball. The experiment result achieves 76.25%, 81.89%, and 86.13% success rate at the 30mm, 50mm, and 70mm error range, respectively. Since the proposed refinement framework is based on a real volleyball competition, it is expected to be applied in the volleyball analysis.", "Keywords": "Data independence; Motion-aware model; 3D human pose refinement; Sports analysis in volleyball", "DOI": "10.1007/s11042-023-16369-8", "PubYear": 2024, "Volume": "83", "Issue": "8", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Information, Product and Systems, Waseda University, Kitakyushu, Japan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Xidian University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Information, Product and Systems, Waseda University, Kitakyushu, Japan"}], "References": [{"Title": "Selection of effective machine learning algorithm and Bot-IoT attacks traffic identification for internet of things in smart city", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "433", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "IoT malicious traffic identification using wrapper-based feature selection mechanisms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "101863", "JournalTitle": "Computers & Security"}, {"Title": "Image steganography with N-puzzle encryption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "39-40", "Page": "29951", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "PhyCoVIS: A visual analytic tool of physical coordination for cheer and dance training", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "e1975", "JournalTitle": "Computer Animation and Virtual Worlds"}, {"Title": "Multi-view 3D human pose reconstruction based on spatial confidence point group for jump analysis in figure skating", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "1", "Page": "865", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 109357567, "Title": "Analysis of incentive mechanism and contractor behaviours under informatisation construction in megaprojects", "Abstract": "Purpose As the complexity and uncertainty of megaprojects make it difficult for traditional management models to address the difficulties, this paper aims to design a performance incentive contract through IT applications, thereby promoting the formation of an information-based governance mechanism for megaprojects and facilitating the transformation and upgrading of the construction management model of megaprojects to informatisation. Design/methodology/approach This paper introduced IT applications into the performance assessment and used the proportion of IT applications replacing traditional manual management as a variable. It analysed different replacement ratios to obtain the optimal solution for the change of contractors behaviours and promote the optimal performance incentive for the informatisation in megaprojects. Findings The results show that under the condition of the optimal replacement ratio, achieving the optimal state of a mutual win-win situation is possible for the benefit of both sides. The counter-intuitive finding is that the greater the replacement ratio is not, the better, but those other constraints are also taken into account. Originality/value This study enriched the research of the performance configuration incentive from a practical perspective. It extended the research framework of IT incentive mechanisms in the governance of megaprojects from a management theory perspective. It clarified the role of IT applications in incentive mechanisms and the design process of optimal incentive contracts under different performance incentive states. The incentives made the contractors work harder to meet the owner's requirements, and it could improve the efficiency of megaprojects, thus better achieving megaproject objectives.", "Keywords": "Megaprojects;Incentive mechanisms;IT", "DOI": "10.1108/K-04-2023-0696", "PubYear": 2024, "Volume": "53", "Issue": "12", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management , Southwest Jiaotong University , Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Construction Management Department, Southwest Jiaotong University , Chengdu, China"}], "References": []}, {"ArticleId": 109357587, "Title": "Zero-Shot Learning with Noisy Labels", "Abstract": "Zero-shot learning (ZSL) is an attractive technique that can recognize novel object classes without any visual examples, but most existing methods assume that the class labels of the training instances from seen classes are accurate and reliable. However, in some real-world scenarios, the label quality may be compromised by various factors, and ZSL may suffer from performance degradation under label noise. In this paper, we propose an effective approach that utilizes a robust loss function to handle label noise in seen classes. Specifically, we construct a new denoising framework to reduce the impact of outliers and mislabeled samples. To mitigate overfitting to noisy labelled samples, a robust loss function called ramp-style loss is employed to filter out the negative influence of data with anomalous loss values, using only samples within a normal loss range for training, thereby enhancing the overall robust performance. Additionally, we introduce the concave-convex procedure (CCCP) method to address the non-convexity issue of ramp-style loss and devise an efficient update scheme based on ADMM. Extensive experiments conducted on several benchmark datasets (AWA2, SUN, CUB) demonstrate the superior performance of our framework over state-of-the-art ZSL methods in various noisy-label environments.", "Keywords": "Zero-shot learning ; Noisy labels ; Ramp-style loss ; CCCP framework", "DOI": "10.1016/j.procs.2023.08.049", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Nanjing University of Information Science & Technology, Nanjing, 210044, China"}, {"AuthorId": 2, "Name": "Long Tang", "Affiliation": "School of Artificial Intelligence, Nanjing University of Information Science & Technology, Nanjing, 210044, China;Research Institute of Talent Big Data, Nanjing University of Information Science & Technology, Nanjing, 210044, China"}, {"AuthorId": 3, "Name": "Zhigeng Pan", "Affiliation": "School of Artificial Intelligence, Nanjing University of Information Science & Technology, Nanjing, 210044, China"}], "References": [{"Title": "Valley-loss regular simplex support vector machine for robust multiclass classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106801", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 109357597, "Title": "Can we identify what are the causes and effects of internal migrations? The case of Serbia", "Abstract": "Internal migrations within countries have significant implications for various socio-economic factors and regional development. Understanding the drivers behind internal migrations is crucial for policymakers seeking to address population movements and their consequences. In this paper, we investigate the factors influencing internal migrations in the Republic of Serbia. More specifically, we employ the NOTEARS algorithm, a causal discovery method, to uncover the causal relationships between key factors related to the economy, education, healthcare, tourism, and municipality activity quality. We aim to identify potential causal links between these factors and internal migrations. The results suggest that the number of companies per 1,000 inhabitants influences emigrations in a Belgrade region, while there are no direct or indirect causes of emigrations or immigations for other regions. This analysis contributes to a comprehensive understanding of the dynamics shaping internal migrations in the Republic of Serbia and offers valuable insights for policymakers and researchers alike.", "Keywords": "Internal migration ; Drivers of migration ; Causal Discovery", "DOI": "10.1016/j.procs.2023.08.122", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Belgrade , Faculty of Organizational Sciences , Jove Ilića 154 , 11000 Belgrade , Serbia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Belgrade , Faculty of Organizational Sciences , Jove Ilića 154 , 11000 Belgrade , Serbia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Belgrade , Faculty of Organizational Sciences , Jove Ilića 154 , 11000 Belgrade , Serbia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "World Bank Group , Bulevar Kralja Aleksandra 86 , 11120 Belgrade , Serbia"}], "References": [{"Title": "When causal inference meets deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "8", "Page": "426", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": *********, "Title": "Impact of geopolitical risk on the volatility spillovers among G7 and BRICS stock markets", "Abstract": "Geopolitical risk (GPR) plays an important role in the international financial interactions. Previous studies have mostly analyzed the impact of the GPR on the return or volatility of financial assets, and less attention has been paid on the connection among international stock markets. Therefore, this study attempts to explore the impact of GPR on the volatility spillovers among G7 and BRICS stock markets using the impulse response function. To be specific, TVP-VAR model is utilized to capture the dynamic volatility spillovers among 12 stock markets, and we obtain the total and directional spillover indexes. Then, impulse response function based on the VAR model is applied to capture the dynamic effect of GPR on the spillover series. Based on the weekly price data ranging from August 16, 2002 to April 10, 2023, we find that the volatility spillover index increases when there are large shocks to the market such as war, financial crisis and COVID-19. In addition, the impact of GPR on the total spillover index is negative in the early and late periods, and it shows a more significant positive effect in the middle period. These results reflect the dynamic impact of GPR on the connection among international stock markets, which is essential for the international financial risk management.", "Keywords": "geopolitical risk ; volatility spillover ; TVP-VAR ; impulse response ; stock markets", "DOI": "10.1016/j.procs.2023.08.064", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management and Engineering , Capital University of Economics and Business , Beijing , China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management and Engineering , Capital University of Economics and Business , Beijing , China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management and Engineering , Capital University of Economics and Business , Beijing , China"}], "References": []}, {"ArticleId": 109357621, "Title": "A bibliometrics analysis of enterprise alliance conflict: insights and implications", "Abstract": "Based on the bibliometrics method, this paper intends to detect the research topics and trends in enterprise alliance conflict dynamics. The co-word analysis is applied to capture the research characteristics of enterprise alliance conflict dynamics, such as the evolution of the published articles and journals, cooperation between authors, and research themes. In specific, the terms “enterprise alliance”, “alliance conflict”, and “conflict dynamics” are used to search the relevant literature, and 2124 articles spanning from 01/01/2003 to 07/31/2022 are obtained. The results reveal (1) the number of articles on enterprise alliance conflict dynamics from 2003-2022 experienced a steady increase; (2) the co-author network is sparse, with only a few cooperative groups, which indicates that relevant scholars can further strengthen cooperation and exchanges to better promote the development of alliance conflict dynamics; (3) the research topics in enterprise alliance conflict dynamics are divided into 5 clusters, the specific research themes are studies regarding “conflict dynamics”; studies regarding “ alliance performance and innovation”; studies regarding “conflict diversity; studies regarding “work-family conflict”; studies regarding organization conflict.", "Keywords": "Enterprise alliance ; Conflict dynamics ; Co-word analysis ; Publication activity ; Research topics ; Evolution analysis", "DOI": "10.1016/j.procs.2023.08.073", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Hangzhou Dianzi University, No.1158, 2nd Street Jianggan District, Hangzhou, 310018, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Glorious Sun School of Business and Management, Donghua University, Shanghai, 200051, China"}, {"AuthorId": 3, "Name": "Yuan<PERSON> Cao", "Affiliation": "School of Management, Hangzhou Dianzi University, No.1158, 2nd Street Jianggan District, Hangzhou, 310018, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Hangzhou Dianzi University, No.1158, 2nd Street Jianggan District, Hangzhou, 310018, China"}], "References": []}, {"ArticleId": 109357626, "Title": "The Research On Statistical Identification of Critical Infrastructure Chain Failure Paths", "Abstract": "Using literature studies, case studies, empirical induction and statistical identification, we proposed an analytical framework based on empirical induction to identify and analyze chain failure problems among interdependent critical infrastructures. A case database of critical infrastructure chain failures was established, and as the research emphasis the identification method of critical infrastructure chain failure paths was constructed and designed. The chain failure paths of the critical infrastructure and its sub-networks in China and other countries and regions were discussed. Logical relationships of critical infrastructure chain failures were extracted from the established database covering 229 critical infrastructure chain failure cases. Statistics, identification, and analysis of data were conducted to form a quantitative assessment and understanding of the vulnerability of interdependent critical infrastructure networks. Finally, identification methods and probabilistic statistical methods for chain failure paths of critical infrastructure were designed and applied to the case base of this paper. Using this approach, fatal and severe failure paths of critical infrastructure can be identified and the efficiency of the relevant authorities' inspection work will be improved under cost constraints.", "Keywords": "Critical infrastructure ; Interdependence ; Chain failure ; Statistical identification", "DOI": "10.1016/j.procs.2023.08.031", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Dalian University of Technology, 2 Lingong Road, Ganjingzi District, Dalian 116024, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Dalian University of Technology, 2 Lingong Road, Ganjingzi District, Dalian 116024, China"}, {"AuthorId": 3, "Name": "Chunbing Bao", "Affiliation": "School of Management, Shandong University, 27 Shandanan Road, Licheng District, Jinan 250100, China"}], "References": []}, {"ArticleId": 109357639, "Title": "Evaluation and learning in two-player symmetric games via best and better responses", "Abstract": "This paper focuses on filling the gap between strategy evaluation and strategy learning in two-player symmetric games, as a learning algorithm may converge to the strategies not preferred by an evaluation metric . When a player determines its strategies, it needs to first evaluate candidate strategies without knowing the opponents&#x27; decisions. Then, based on the result of the evaluation, a preferred strategy is selected. On the contrary, many multi-agent reinforcement learning algorithms are constructed provided that the strategies of other players are known in each training episode. In this paper, we first introduce two graph-based metrics grounded on sink equilibrium to characterize the preferred strategies of the players in strategy evaluation. These metrics can be regarded as generalized solution concepts in games. Then, we propose two variants of the classical self-play algorithm, named strictly best-response and weakly better-response self-plays , to learn the strategies for the players. By modeling the learning processes as walks over joint-strategy response digraphs , we prove that under some conditions, the learned strategies by two variants are the preferred strategies under two metrics, respectively, which thus fills the evaluation–learning gap, and ensures that the preferred strategies are learned. We also investigate the relationship between the two metrics.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119459", "PubYear": 2023, "Volume": "647", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Oxford, Oxford, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Tsinghua University, Beijing, 100084, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Tsinghua University, Beijing, 100084, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, Shanghai Jiao Tong University, Shanghai, 200240, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Tsinghua University, Beijing, 100084, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Tsinghua University, Beijing, 100084, China"}], "References": [{"Title": "Extracting tactics learned from self-play in general games", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "277", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 109357693, "Title": "Application of the PrOPPAGA Method in the evaluation of returnable stretches in the context of the processes of early extension of railway concessions", "Abstract": "According to data from the National Association of Railway Carriers - ANTF, the Brazilian rail network is composed of 30,557 km, being distributed in 15 networks granted to the private initiative. Of this total, about 39% (8,335.9 km) are without traffic or with low demand and sometimes completely deteriorated. Given the proximity of the end of the current railway concession contracts, studies for their Early Extension were initiated. In this context, the evaluation of stretches that are without traffic is a strategic decision, both for companies and for the Union. Thus, the article seeks, through the multi-criteria analysis method PrOPPAGA, to evaluate the economic viability of 5 railway stretches that have little or no traffic based on criteria such as demand and operating costs of the stretches. The methodology is also used to carry out the ordering of the stretches from the most viable to the least economically viable. As a result, it was recommended the return 4 of the 5 excerpts, demonstrating the viability of the method as a tool to aid decision-making in this sector. It should be noted that parameters such as strategic interest and desired profit indicators, which can be evaluated in future research, were not incorporated into the research.", "Keywords": "Railways ; Return of sections ; PrOPPAGO Method ; Early Extension", "DOI": "10.1016/j.procs.2023.07.038", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Military Institute of Engineering, Urca, RJ 22290-270, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Military Institute of Engineering, Urca, RJ 22290-270, Brazil"}, {"AuthorId": 3, "Name": "Orivalde <PERSON>", "Affiliation": "Military Institute of Engineering, Urca, RJ 22290-270, Brazil"}, {"AuthorId": 4, "Name": "Anderson Gonçalves Portella", "Affiliation": "Veiga de Almeida University, Rio de Janeiro, RJ, 20271-020, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Military Institute of Engineering, Urca, RJ 22290-270, Brazil;Fluminense Federal University, Niterói, RJ 24210-240, Brazil"}], "References": [{"Title": "Design of a framework of military defense system for governance of geoinformation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "174", "JournalTitle": "Procedia Computer Science"}, {"Title": "Assisting in the choice to fill a vacancy to compose the PROANTAR team: Applying VFT and the CRITIC-GRA-3N methodology", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "214", "Issue": "", "Page": "478", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 109357702, "Title": "Consumer preference disaggregation based on online reviews to support new energy automobile purchase decision", "Abstract": "In recent years, new energy automobiles have developed rapidly and become the choice of more and more consumers. At the same time, a vast number of online reviews of automobiles with inferred consumer preferences emerge on platforms. There is an urgent need to mine useful information from online reviews to support consumer purchase decisions. In this study, we propose a multi-criteria product recommendation method that considers consumer’ preferences and risk psychology estimated from online reviews. Firstly, a sentiment analysis method is developed to mine product performance under different attributes from text reviews. Secondly, a preference model with unknown preference parameters is predefined based on the multi-attribute value theory to represent consumers’ value systems for purchase decision. On this basis, we propose a preference disaggregation analysis method that combines the prospect theory. Consumers’ specific preference models are estimated from online reviews, which are then utilized to measure product performance and generate product recommendations. Finally, the validity of the proposed method is demonstrated by a case study of new energy automobile ranking from Autohome .", "Keywords": "Consumer preference disaggregation ; risk psychology ; product recommendation ; online review ; new energy automobile", "DOI": "10.1016/j.procs.2023.08.013", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu 610064, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu 610064, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu 610064, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Business Administration, Faculty of Business Administration, Southwestern University of Finance and Economics, Chengdu 611130, China"}], "References": []}, {"ArticleId": 109357729, "Title": "Financial Report Similarity and Managerial Stock Sales in Technology Enterprises: Empirical Evidence from Beijing", "Abstract": "This study delves into the connection between the similarity of Management Discussion and Analysis (MD&A) texts and managerial opportunistic stock sales in technology-oriented listed companies in Beijing from 2001 to 2022. The findings reveal that higher MD&A similarity in Beijing's technology companies is associated with an increased likelihood of managerial opportunistic stock sales. This research contributes to mitigating financial risks in Beijing's capital market and provides valuable empirical evidence to support government efforts in fostering the high-quality development of technology enterprises.", "Keywords": "", "DOI": "10.23977/infse.2023.040705", "PubYear": 2023, "Volume": "4", "Issue": "7", "JournalId": 93764, "JournalTitle": "Information Systems and Economics", "ISSN": "2523-6407", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 109357737, "Title": "An Analysis of Pairwise Question Matching with Machine Learning", "Abstract": "n the realm of Natural Language Processing (NLP) and machine learning, lies the challenging quest to detect duplicate question pairs with semantic precision. Our research endeavors to craft a cutting-edge model capable of discerning whether two questions, despite their divergent phrasing, spelling, or grammatical variations, share a common intent on digital forums or search engines. A paramount facet of this study involves the creation and training of an exemplary model using a meticulously curated dataset of labeled question pairs, each annotated as either duplicates or distinct entities. By leveraging state-of-the-art NLP techniques, we aspire to build an exceptionally accurate model that will revolutionize the user search experience by facilitating the identification of duplicate questions. This pioneering research paves the way for a more refined and enhanced approach to tackle the challenges of semantic similarity in the context of question pairs", "Keywords": "", "DOI": "10.30534/ijatcse/2023/021242023", "PubYear": 2023, "Volume": "12", "Issue": "4", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 109357752, "Title": "Assessing customer satisfaction of London luxury hotels with the AHP method and the SERVPERF scale: a case study of customer reviews on TripAdvisor", "Abstract": "Customer feedback and satisfaction are critical indicators of success in the hospitality industry. The influence of customer reviews on the decisions of other customers to visit and on the brand reputation of hotels necessitates that hotel managers conduct a thorough analysis of customer satisfaction. This paper presents an approach for assessing the overall customer satisfaction of hotels by using the Analytic Hierarchy Process (AHP) method, the SERVPERF scale, and the 2-tuple linguistic model. The five dimensions of the SERVPERF scale have been customized to assess customer satisfaction in hotels, considering various hotel aspects such as rooms, service, cleanliness, and so on. The AHP method is applied to obtain the importance of the SERVPERF scale for each aspect of the hotels. The 2-tuple linguistic model is employed to address the issue of information loss in linguistic information fusion and enhance the comprehensibility of the results for hotel managers. The functionality of this proposal has been evaluated through a case study of luxury hotels in London, using 15,511 customer reviews gathered from TripAdvisor. The results show that the proposed model effectively captures and summarizes customer satisfaction with luxury hotels in London, allowing hotel managers to identify areas for improvement and discover potential business opportunities. This ultimately helps to increase customer satisfaction.", "Keywords": "SERVPERF ; Multi-Criteria Decision-Making ; Analytic Hierarchy Process ; 2-tuple linguistic model ; TripAdvisor", "DOI": "10.1016/j.procs.2023.07.011", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Shu", "Affiliation": "Department of Statistics and Data Science, Faculty of Statistics, Complutense University of Madrid, Madrid 28040, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Marketing, Faculty of Commerce and Tourism, Complutense University of Madrid, Madrid 28003, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Marketing, Faculty of Statistics, Complutense University of Madrid, Madrid 28040, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Marketing, Faculty of Commerce and Tourism, Complutense University of Madrid, Madrid 28003, Spain"}], "References": [{"Title": "A business context aware decision-making approach for selecting the most appropriate sentiment analysis technique in e-marketing situations", "Authors": "Itz<PERSON><PERSON><PERSON>l Bueno; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "300", "JournalTitle": "Information Sciences"}, {"Title": "Evaluating public transport service quality using picture fuzzy analytic hierarchy process and linear assignment model", "Authors": "Fat<PERSON>; Szabolcs Duleba; Sarbast Moslem", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106920", "JournalTitle": "Applied Soft Computing"}, {"Title": "An optimal Best-Worst prioritization method under a 2-tuple linguistic environment in decision making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "155", "Issue": "", "Page": "107141", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 109357777, "Title": "Cheap fares for high-credit passengers: mitigating metro’s overcrowding via a credit-based coordinative pricing scheme", "Abstract": "<p>In metropolitans such as Beijing and Shanghai, metro systems are constantly overloaded during rush hours. An effective solution to mitigate the problem of metro overcrowding is to divert demand to other means of transportation. Free-float bike sharing system, following a major trend of “shared economy”, has been proven an effective mechanism to help reduce the load of other ways of transportation; bike sharing is especially appealing for short-distance travelers due to its flexibility and economy. In this paper, we propose to use shared bikes for mitigating the traffic pressure on metros; we do so under a coordinative pricing scheme. A key novelty of our coordinative pricing scheme is that it automatically adjust customers’ fares in accordance with their credit scores. To understand the equilibrium strategic behavior of the two service providers (metro and bike sharing companies) under our credit-based pricing scheme, we investigate several game theoretical models. Our analysis reveals several interesting findings. For example, service providers’ willingness to implement the new pricing scheme is sensitive to their marketing power. Both service providers have an incentive to participate in a coordinated program as long as the metro operator’s marketing power is high and the bike sharing operator’s is low. Moreover, through a comprehensive comparison, we highlight that the metro-lead mechanism with credit-based pricing can strike a better balance of several competing objectives, thereby providing a new idea for collaborative demand management for peak commuting.</p>", "Keywords": "Pricing; Coordinative demand management; Metro; Bike sharing; Credit", "DOI": "10.1007/s42488-023-00093-x", "PubYear": 2023, "Volume": "5", "Issue": "3", "JournalId": 64686, "JournalTitle": "Journal of Data, Information and Management", "ISSN": "2524-6356", "EISSN": "2524-6364", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Beijing University of Chemical Technology, Beijing, China"}], "References": [{"Title": "Optimization of differentiated fares and subsidies for different urban rail transit users", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "179", "Issue": "", "Page": "109144", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 109357850, "Title": "Selection of Agroindustry Real Estate Funds, based on the AHP-Gaussian, for an Investment Portfolio", "Abstract": "One of the problems faced by investors in the stock market is the difficulty in choosing good assets, since, for investors without adequate knowledge, it becomes a subjective and non-quantitative process, which makes it difficult to assess whether the asset is good or not. In this sense, selecting the best FIAgro fund, based on the AHP-Gaussian multicriteria decision method, to compose a diversified investment portfolio. The collection of data referring to FIAgro was based on investment brokerages in data from the Brazilian Stock Exchange. After modeling the AHP-Gaussian method, the result indicated RZAG11 as the best FIAgro and PLCA11 the worst.", "Keywords": "\"Investment ; FIAgro ; Multicriteria Decision Analysis Method ; AHP-Gaussian\"", "DOI": "10.1016/j.procs.2023.08.043", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Federal University of Campina Grande , Campina Grande , PB 58428-830 , Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal University of Campina Grande , Campina Grande , PB 58428-830 , Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Campina Grande , Campina Grande , PB 58428-830 , Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Federal University of Campina Grande , Campina Grande , PB 58428-830 , Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Federal University of Campina Grande , Campina Grande , PB 58428-830 , Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Federal University of Campina Grande , Campina Grande , PB 58428-830 , Brazil"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Federal University of Campina Grande , Campina Grande , PB 58428-830 , Brazil"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Federal University of Campina Grande , Campina Grande , PB 58428-830 , Brazil"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Military Institute of Engineering , Urca , RJ 22290-270 , Brazil"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University , Niterói , RJ 24210-240 , Brazil"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Federal University of Pernambuco , Recife , PE , 50740-600 , Brazil"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "Military Institute of Engineering , Urca , RJ 22290-270 , Brazil"}], "References": [{"Title": "Big Bags Reverse Logistics using Business Intelligence and Multi-Criteria Analysis", "Authors": "Francisco Mendes dos Santos Junior; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "214", "Issue": "", "Page": "172", "JournalTitle": "Procedia Computer Science"}, {"Title": "Consistency Analysis Algorithm for the Multi-criteria Methods of SAPEVO Family", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "214", "Issue": "", "Page": "133", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": *********, "Title": "Systemic ESG risks: industrial analysis", "Abstract": "Environmental, Social, and Governance (ESG) and ESG-related risks are highly important for both companies and investors. Companies considering ESG factors while making business-related decisions and assessing the company's risks outperform other companies over the long term. The aim of the research is to investigate whether ESG factors that are specific to particular industries can be identified as systematic risk factors in various industries, and examine how systemic ESG factors impact the performance of companies. Also, the study highlighted differences in ESG factors’ systemic risks across industries. The regression was built to identify the influence of ESG-related factors on the companies’ financial performance. It is anticipated that systematic ESG risks are significant for companies’ performance, but there are strong industry specifics.", "Keywords": "ESG risks ; Systematic risk ; Environmental factors ; Social factors ; Governance factors ; Industry-specific risks ; Financial performance ; Long-term performance ; Business decisions ; ESG factors ; Industry analysis ; ESG-related risks ; Corporate sustainability ; Company performance ; Regression analysis ; Industry-specific ESG factors ; Sustainable investing ; Risk assessment ; Investor perspective ; ESG integration", "DOI": "10.1016/j.procs.2023.08.095", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HSE University, Pokrovskiy boulevard 11, Moscow, Russian Federation"}, {"AuthorId": 2, "Name": "German Petrov-Nerling", "Affiliation": "HSE University, Pokrovskiy boulevard 11, Moscow, Russian Federation"}], "References": []}, {"ArticleId": *********, "Title": "Algebraically explainable controllers: decision trees and support vector machines join forces", "Abstract": "Recently, decision trees (DT) have been used as an explainable representation of controllers (a.k.a. strategies, policies, schedulers). Although they are often very efficient and produce small and understandable controllers for discrete systems, complex continuous dynamics still pose a challenge. In particular, when the relationships between variables take more complex forms, such as polynomials, they cannot be obtained using the available DT learning procedures. In contrast, support vector machines provide a more powerful representation, capable of discovering many such relationships, but not in an explainable form. Therefore, we suggest to combine the two frameworks to obtain an understandable representation over richer, domain-relevant algebraic predicates. We demonstrate and evaluate the proposed method experimentally on established benchmarks.", "Keywords": "Controller representation; Explainability; Synthesis; Decision tree", "DOI": "10.1007/s10009-023-00716-z", "PubYear": 2023, "Volume": "25", "Issue": "3", "JournalId": 15615, "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)", "ISSN": "1433-2779", "EISSN": "1433-2787", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technical University of Munich, Munich, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technical University of Munich, Munich, Germany; Masaryk University, Brno, Czech Republic"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Technical University of Munich, Munich, Germany"}], "References": [{"Title": "Software Fault Tolerance for Cyber-Physical Systems via Full System Restart", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Cyber-Physical Systems"}]}, {"ArticleId": 109357895, "Title": "Fake Financial News Detection with Deep Learning: Evidence from China", "Abstract": "Although fake news detection has become an emerging research attracting much attention, research in financial sector is very limited. Particularly in China, no public dataset of fake financial news is available. This paper focuses on Chinese financial market and constructs a unique dataset from clarification announcements targeting for financial news with Internet sources. Besides content features and contextual features, financial features are typically added to the feature set due to unique characteristics of financial news. Based on our sample, a deep learning approach is proposed to detect fake financial news, which demonstrates superior performance to several other baseline models, with accuracy of 94.38% and f1-score of 87.67%.The ablation experiment indicates that content features contained in the article itself contribute strongly to detect fake financial news. Finally, Shapley value is used to explain the characteristics of fake financial news compared with real ones in Chinese financial market.", "Keywords": "fake financial news ; detection ; deep learning ; China", "DOI": "10.1016/j.procs.2023.07.022", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information, Central University of Finance and Economics, Beijing, 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information, Central University of Finance and Economics, Beijing, 100081, China"}], "References": [{"Title": "Fake News, Investor Attention, and Market Reaction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "35", "JournalTitle": "Information Systems Research"}, {"Title": "An ensemble machine learning approach through effective feature extraction to classify fake news", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "47", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 109357905, "Title": "A study of factors influencing financial stock prices based on causal inference", "Abstract": "A key challenge in current stock market analysis research is how to extract meaningful and actionable insights from the vast and continuously updated amount of data available. Common data mining and machine learning algorithms primarily focus on uncovering correlations between variables but struggle to determine causality, which hinders investors' ability to make informed decisions. The widely used Granger causality test in stock market analysis has limitations when applied to high-dimensional data, given issues of mis-fitting and inefficiency. As a result, it falls short in coping with the scale of stock data available today. To address these challenges, we employ ANM (Additive Noise Model), a causal inference method based on a nonlinear causal function model, to identify the factors influencing stock price changes. Through experiments, we find that ANM demonstrates a certain level of reliability in factor selection compared to Granger analysis when discovering causal relationships among low-dimensional macroeconomic factors. Additionally, we compare the effects of ANM-selected factors with full factor models and models constructed using PCA factors, combined with machine learning algorithms. The results indicate that the factors identified by ANM outperform the other two methods. Our findings suggest that ANM serves as a valuable approach for factor selection in stock market analysis. By leveraging ANM and considering its results in conjunction with machine learning algorithms, we can more effectively identify influential factors for stock price changes compared to other methodologies. It is important to note that the field of stock market analysis and causal inference is constantly evolving, and different methodologies have their own strengths and weaknesses. Ongoing research and experimentation are essential to further refine and validate these approaches.", "Keywords": "causal inference ; stock price ; conceptual drift", "DOI": "10.1016/j.procs.2023.08.062", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information, Central University of Finance and Economics, Beijing, China"}, {"AuthorId": 2, "Name": "Haifeng Li", "Affiliation": "School of Information, Central University of Finance and Economics, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information, Central University of Finance and Economics, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information, Central University of Finance and Economics, Beijing, China"}], "References": []}, {"ArticleId": 109357954, "Title": "Soft-Computing Techniques to Address Industrial and Environmental Challenges", "Abstract": "", "Keywords": "", "DOI": "10.1080/01969722.2023.2240644", "PubYear": 2024, "Volume": "55", "Issue": "6", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Grupo de Inteligencia Computacional Aplicada (GICAP), Departamento de Ingeniería Informática, Escuela Politécnica Superior, Universidad de Burgos, Burgos, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Grupo de Inteligencia Computacional Aplicada (GICAP), Departamento de Ingeniería Informática, Escuela Politécnica Superior, Universidad de Burgos, Burgos, Spain"}, {"AuthorId": 3, "Name": "Esteban Jove", "Affiliation": "Department of Industrial Engineering, University of A Coruña, A Coruña, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Systems and Computer Networks, Faculty of Information and Communication Technology, Wroclaw University of Science and Technology, Wroclaw, Poland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Departamento de Informática y Automática, Universidad de Salamanca Plaza de la Merced, Salamanca, Spain"}], "References": []}, {"ArticleId": 109357968, "Title": "Defense Perception in the Geopolitical Scope: An exploratory study through unsupervised machine learning", "Abstract": "Regarding military development worldwide, the scope of defense has influenced the economic and technological advancement of many nations, whether to defend their sovereignty or lead military power in the global geopolitical scenario. For the study in question, a database from three others is used, composed of 16 variables and 120 countries, exposing defense figures and economic and social indexes respective to each nation. In this scenario, an exploratory analysis is performed, based on statistical modeling and unsupervised machine learning techniques, exposing correlation points between the variables, listing the most influential and influenced in the evaluation scenario. Using clustering techniques through the K-means algorithm and support of the elbow graph, the possible clusters built for the defense scenario are presented, identifying a favorable number of six groups, thus listing the countries with the greatest similarities between their quantitative variables. Finally, the conclusions of the results found are presented, as well as the limitations of the study and proposals for future studies.", "Keywords": "Defense Perception ; Statistical Modeling ; Unsupervised Machine Learning ; Clustering", "DOI": "10.1016/j.procs.2023.08.039", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil;Naval Systems Analysis Center, Rio de Janeiro, RJ 20091-000, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil;Naval Systems Analysis Center, Rio de Janeiro, RJ 20091-000, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of São Paulo, São Paulo, SP 05508-210, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of São Paulo, São Paulo, SP 05508-210, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil;Naval Systems Analysis Center, Rio de Janeiro, RJ 20091-000, Brazil;Military Institute of Engineering, Urca, RJ 22290-270, Brazil"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil"}], "References": [{"Title": "Design of a framework of military defense system for governance of geoinformation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "174", "JournalTitle": "Procedia Computer Science"}, {"Title": "Exploratory analysis and implementation of machine learning techniques for predictive assessment of fraud in banking systems", "Authors": "<PERSON>; <PERSON> Rocha <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "214", "Issue": "", "Page": "117", "JournalTitle": "Procedia Computer Science"}, {"Title": "Assisting in the choice to fill a vacancy to compose the PROANTAR team: Applying VFT and the CRITIC-GRA-3N methodology", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "214", "Issue": "", "Page": "478", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": *********, "Title": "Review of medical data analysis based on spiking neural networks", "Abstract": "Medical data mainly includes various types of biomedical signals and medical images, which can be used by professional doctors to make judgments on patients' health conditions. However, the interpretation of medical data requires a lot of human cost and there may be misjudgments, so many scholars use neural networks and deep learning to classify and study medical data, which can improve the efficiency and accuracy of doctors and detect diseases early for early diagnosis, etc. Therefore, it has a wide range of application prospects. However, traditional neural networks have disadvantages such as high energy consumption and high latency (slow computation speed). This paper presents recent research on signal classification and disease diagnosis based on a third-generation neural network, the spiking neuron network, using medical data including EEG signals, ECG signals, EMG signals and MRI images. The advantages and disadvantages of pulsed neural networks compared with traditional networks are summarized and its development orientation in the future is prospected.", "Keywords": "Spiking neural network ; Computer-aided diagnosis (CAD) ; Medical data ; Electroencephalogram (EEG) ; Electrocardiogram (ECG) ; Electromyography (EMG) ; Magnetic resonance images (MRI)", "DOI": "10.1016/j.procs.2023.08.138", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of science, China University of Petroleum, Beijing 102249, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of science, China University of Petroleum, Beijing 102249, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of science, China University of Petroleum, Beijing 102249, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of science, China University of Petroleum, Beijing 102249, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of science, China University of Petroleum, Beijing 102249, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of science, China University of Petroleum, Beijing 102249, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "College of science, China University of Petroleum, Beijing 102249, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, Beijing 100190, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "College of science, China University of Petroleum, Beijing 102249, China;Beijing Key Laboratory of Optical Detection Technology for Oil and Gas, China University of Petroleum, Beijing 102249 China"}], "References": [{"Title": "A spiking network classifies human sEMG signals and triggers finger reflexes on a robotic hand", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "103566", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "NeuroSense: Short-term emotion recognition and understanding based on spiking neural network modelling of spatio-temporal EEG patterns", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "434", "Issue": "", "Page": "137", "JournalTitle": "Neurocomputing"}, {"Title": "Classification of Alzheimer’s Disease Using Deep Convolutional Spiking Neural Network", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "2649", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 109358027, "Title": "Risk Perception of the \" Belt and Road\" Countries Based on Global Media Data GDELT", "Abstract": "Rapid and dynamic perception of risks in countries along the “Belt and Road” is of great practical significance for the construction of the “Belt and Road”. The current measurements of country risk are divided into two categories: multi-factor analysis and systematic risk modeling based on capital asset pricing theory. There are problems such as time lag in data updates and inadequate completeness. Risk perception based on big data has the characteristics of wide sources, high timeliness, multiple dimensions, and full coverage, and it can capture potential risk variations earlier and faster. In this study, based on the global media big data GDELT, it is found that the risks of the countries along the “Belt and Road” are mainly focused on politics, military risks, energy trade, terrorism, power struggles, etc. West Asia and North Africa region are at the core of the risk reports in the network of countries mentioned, with the Central and Eastern Europe and Central Asia region playing the role of \"bridge\" nodes. In the “country-topic risk” heterogeneous information network, when the risk topic similarity was set to 0.7, the country risk clustering effect is the best. Syria had always been at high risk. The risk of countries in West Asia and North Africa, such as Afghanistan, Iran, Israel, and Lebanon is also at high risk but slightly fluctuated from year to year. The research results show that the classification of national risks by media big data has strong consistency with existing national risk ratings, so this article proposes to use media big data to enhance the risk perception capabilities of countries along the “Belt and Road”.", "Keywords": "Big data ; GDELT ; Country risk ; Risk Perception", "DOI": "10.1016/j.procs.2023.07.045", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institutes of Science and Development, Chinese Academy of Sciences, Beijing 100190, China;School of Public Policy and Management, University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institutes of Science and Development, Chinese Academy of Sciences, Beijing 100190, China;School of Public Policy and Management, University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institutes of Science and Development, Chinese Academy of Sciences, Beijing 100190, China;School of Public Policy and Management, University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institutes of Science and Development, Chinese Academy of Sciences, Beijing 100190, China;School of Public Policy and Management, University of Chinese Academy of Sciences, Beijing 100049, China"}], "References": []}, {"ArticleId": 109358119, "Title": "Using Adaptive Chaotic Grey Wolf Optimization for the daily streamflow prediction", "Abstract": "Due to the importance of water resources management that requires the optimal model for streamflow prediction, the study of water flow prediction is of great importance. Hence, the present paper aims to introduce an innovative model that can predict streamflow with the highest accuracy. Accordingly, a multilayer perceptron neural network whose optimum neuron number is specified using the evaluation metrics is considered for simulation. The dataset used for this purpose consists of 80% experimental and 20% numerical data. Accordingly, the MLP network used in this research is optimized using various optimizers, namely Particle Swarm Optimization , Genetic Algorithm , Grey Wolf Optimization , Chaotic Grey Wolf Optimization, Advanced Grey Wolf Optimization, and Adaptive Chaotic Grey Wolf Optimization. Considering the statistical results, Adaptive Chaotic Grey Wolf Optimization is introduced as the leading optimizer. The obtained results highlight that Adaptive Chaotic Grey Wolf Optimization used for optimizing the training process of the MLP network with 15 neurons can predict the daily streamflow with the best standard error, mean square error , mean absolute percent error, mean absolute error , and normalized mean square error of about 5.3126, 2.3049, 0.6684, 1.1038, and 0.4483, respectively.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.121113", "PubYear": 2024, "Volume": "237", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer and Information Technology, Beijing Jiaotong University, Beijing 100044, China;Peking Science Technology Service Co., LTD, Beijing 100080, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Statistics and Data Science, Nanjing Audit University, Nanjing 211815, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Peking Science Technology Service Co., LTD, Beijing 100080, China;Academy for Advanced Interdisciplinary Studies, Peking University, Beijing 100091, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Peking Science Technology Service Co., LTD, Beijing 100080, China"}, {"AuthorId": 5, "Name": "Wenbo Li", "Affiliation": "Zhengzhou University, Henan 450001, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Lu", "Affiliation": "Peking Science Technology Service Co., LTD, Beijing 100080, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Hubei University of Economics, Wuhan, Hubei, 430205, China;Corresponding author at: School of Information Engineering, Hubei University of Economics, Wuhan, Hubei, 430205, China (R<PERSON>)"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Agriculture and Resources, Vinh University, Viet Nam"}], "References": []}, {"ArticleId": 109358128, "Title": "Forecasting crude oil futures prices using Extreme Gradient Boosting", "Abstract": "Multi-source data is widely used in the field of energy future prices forecasting, the improvement of forecast ability and data screening are becoming the focus of current research. In this paper, two tree-based models (namely, Random Forest and XGBoost model) are employed to predict China's crude oil future prices. The empirical analysis confirms that Random Forest and XGBoost model have superior prediction performances than benchmark and the XGBoost model performs best. An important finding is that there is a time gap between investor information search and processing because the prediction performance within the time lags is obviously superior than that of the current period.", "Keywords": "Oil futures forecasting ; search index ; XGBoost ; random forest", "DOI": "10.1016/j.procs.2023.08.069", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Tourism , Hunan Normal University , Changsha 410081 , China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Tourism , Hunan Normal University , Changsha 410081 , China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Tourism , Hunan Normal University , Changsha 410081 , China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Tourism Management, Macao Institute for Tourism Studies , Macao , China;Department of Marketing , City University of Hong Kong , Hong Kong , China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Foreign Languages , Hunan University , Changsha , 410082 , China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Tourism , Hunan Normal University , Changsha 410081 , China"}], "References": [{"Title": "Machine Learning based Forecasting Systems for Worldwide International Tourists Arrival", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "11", "Page": "55", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Enhancing short-term forecasting of daily precipitation using numerical weather prediction bias correcting with XGBoost in different regions of China", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> Wu", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105579", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 109358148, "Title": "A New Revised Group DEMATEL method with application on facility location problem", "Abstract": "Multi-criteria methods are commonly used in facility location problem to evaluate different facility locations in the presence of a set of criteria in order to select the best facility location. The increasing availability of Internet-of-Things (IoT) data, can be a help in gathering the necessary data for the location evaluation. The paper proposes a New Revised Group DEMATEL (NRG-DEMATEL) multi-criteria method, aiming to analyse cause-and-effect relationships among a set of criteria. This method provides general conditions which imply the convergence to the null matrix of the sequence of powers of the initial relation matrix, hence the total influence matrix can be computed. DEMATEL is infeasible when the sequence of powers of the initial relation matrix does not converge to the null matrix. The NRG-DEMATEL can be applicable to all situations feasible or infeasible. For the cases that are feasible, our method leads to the same result as the original method DEMATEL. The causality diagram and the digraph, that can visualize the causal relationships of a set of criteria, are built with the NRG-DEMATEL method. In order to reduce the complexity of the digraph, a threshold value is set to filter out negligible effects. Two methods for computing the thresholds are considered in this paper: the arithmetic mean method and the arithmetic mean - standard deviation method. An application of the NRG-DEMATEL to hospital facility problem is realized.", "Keywords": "New Revised Group DEMATEL method ; directed graph ; facility location ; Internet of Things", "DOI": "10.1016/j.procs.2023.07.002", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute for Research and Development in Informatics, 8-10, <PERSON><PERSON><PERSON>, Bucharest, 011455, Romania"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" Institute of Mathematical Statistics and Applied Mathematics of the Romanian Academy, Calea 13 Septembrie, No.13, Bucharest, 050711, Romania"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute for Research and Development in Informatics, 8-10, <PERSON><PERSON><PERSON>, Bucharest, 011455, Romania"}], "References": []}, {"ArticleId": 109358184, "Title": "Study on the image restoration strategy and effect of corporates in crisis situations based on text mining", "Abstract": "A corporate's reputation may be significantly impacted by how it handles emergencies. To investigate the effect of different coping strategies on corporate image restoration, we collected 47 representative events of corporates in different industries as well as relevant comments from Weibo. Based on the image restoration theory proposed by <PERSON><PERSON>, the effects of different strategies on corporate reputation restoration were analyzed by using sentiment analysis and the topic model. We found that the two strategies of corrective action and mortification can usually better alleviate the negative emotions of the public. On the contrary, if corporates respond with strategies of denial, evading responsibility, or reducing offensiveness, there could be strong feelings of dissatisfaction. The case study of the food industry also finds that even if the effect of alleviating the negative emotions of the public is not significant, an appropriate response could shift the attention of some consumers and to some extent serve the purpose of restoring the corporate reputation.", "Keywords": "corporate reputation ; image restoration theory ; sentiment analysis ; LDA", "DOI": "10.1016/j.procs.2023.08.113", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Central University of Finance and Economics, Beijing 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Chi", "Affiliation": "School of Management Science and Engineering, Central University of Finance and Economics, Beijing 100081, China"}, {"AuthorId": 3, "Name": "Aihua Li", "Affiliation": "School of Management Science and Engineering, Central University of Finance and Economics, Beijing 100081, China"}], "References": []}, {"ArticleId": 109358186, "Title": "Deep Learning Based Image Quality Assessment: A Survey", "Abstract": "Image quality assessment (IQA) is the problem of measuring the perceptual quality of images, which is crucial for many image-related applications. It is a difficult task due to the coupling of various degradation and the scarcity of annotations. To facilitate a better understanding of IQA, we survey the recent advances in deep learning based IQA methods, which have demonstrated remarkable performance and innovation in this field. We classify the IQA methods into two main groups: reference-based and reference-free methods. Reference-based methods compare query images with reference images, while reference-free methods do not. We further subdivide reference-based methods into full-reference and reduced-reference methods, depending on the amount of information they need from the reference images, and reference-free methods into single-input, pair-input, and multimodal-input methods, according to the form of input they use. The advantages and limitations of each category are analyzed and some representative examples of state-of-the-art methods are provided. We conclude our paper by highlighting some of the future directions and open challenges in deep learning based IQA.", "Keywords": "Image quality assessment ; deep learning ; review", "DOI": "10.1016/j.procs.2023.08.080", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Science , University of Chinese Academy of Sciences , Beijing 101408 , China;Research Center on Fictitious Economy and Data Science, Chinese Academy of Sciences , Beijing 100190 , China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences , Beijing 100190 , China"}, {"AuthorId": 2, "Name": "<PERSON>g<PERSON>", "Affiliation": "Research Center on Fictitious Economy and Data Science, Chinese Academy of Sciences , Beijing 100190 , China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences , Beijing 100190 , China;School of Computer Science and Technology , University of Chinese Academy of Sciences , Beijing 101408 , China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management , University of Chinese Academy of Sciences , Beijing 101408 , China;Research Center on Fictitious Economy and Data Science, Chinese Academy of Sciences , Beijing 100190 , China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences , Beijing 100190 , China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Economics and Management , University of Chinese Academy of Sciences , Beijing 101408 , China;Research Center on Fictitious Economy and Data Science, Chinese Academy of Sciences , Beijing 100190 , China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences , Beijing 100190 , China;College of Information Science and Technology , University of Nebraska at Omaha , NE 68182 , USA"}], "References": []}, {"ArticleId": 109358195, "Title": "An Intelligent System for Detecting Fake News", "Abstract": "A significant task facing machine learning and natural language processing is real-time recognition of fake news generated automatically. Social media platforms contribute to the spread of uncertain information, context in which people of different backgrounds all over the world interact. The purpose of this work is to demonstrate the significant role of artificial intelligence in the remarkable generation of the content (here, content with a low degree of trust). As a result of this survey, after identifying and analyzing main research trends in fake news detection, current opportunities are highlighted where specific recommendations could be exploited as solutions for users, especially, on social media. Moreover, the experiments show that fake news can be generated easily through various interventions into the true news. It is about the fact distortion, subject-object exchange and cause confounding. Also, this work highlights the power of generating news with suspicious content using different classifiers of fake like Fakebox and the highly publicized ChatGPT.", "Keywords": "fake news detection ; content generation ; news recommender systems ; AI", "DOI": "10.1016/j.procs.2023.08.088", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Science, “<PERSON><PERSON><PERSON>” University, General <PERSON><PERSON><PERSON>, 16, 700483, Iasi, Romania;Institute of Computer Science, Romanian Academy - Iasi branch, Bulevardul Carol I, 8, 700505, Romania"}], "References": [{"Title": "Recommender systems for smart cities", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON>; <PERSON>-Cediel", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101545", "JournalTitle": "Information Systems"}, {"Title": "Deep learning for fake news detection: A comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "133", "JournalTitle": "AI Open"}]}, {"ArticleId": 109358206, "Title": "Identification of creativity in collaborative conversations based on the polyphonic model", "Abstract": "The paper presents a theoretical approach and a set of experiments that operationalize it for the identification of creative moments in conversations. State-of-the-art artificial intelligence technology is used for the operationalization: natural language processing, machine learning, and deep neural networks The approach is based on the polyphonic model introduced by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, which starts from <PERSON>'s analogy of discourse building in texts with polyphonic music. The divergent and convergent steps of creativity are related to the inter-animation of voices through dissonances and consonances in polyphonic, contrapuntal music.", "Keywords": "polyphonic model ; creativity ; brainstorming ; collaboration ; computer-supported collaborative learning ; natural language processing ; deep learning", "DOI": "10.1016/j.procs.2023.08.087", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University Politehnica of Bucharest, Computer Science Department, Bucharest 060042, Romania;Research Institute for Artificial Intelligence “<PERSON><PERSON>, Romanian Academy, Bucharest 050711, Romania"}], "References": []}, {"ArticleId": 109358213, "Title": "Assessment on High-quality Development of Guizhou's Agricultural Economy Based on Hesitant Fuzzy Linguistic Term Sets", "Abstract": "In order to scientifically evaluate the quality of agricultural economic development in Guizhou's prefecture-level cities, the article constructs an index system for evaluating the quality development level of agricultural economy from four aspects, including optimization of agricultural economy structure, mechanization level of agricultural economy, green development of agricultural economy, and openness and sharing of agricultural economy, with reference to the current situation of agricultural economy development in nine cities and municipalities of Guizhou Province; and selects the original data of indicators from 2017 to 2021, and uses the hesitant fuzzy linguistic PROMETHEE method to the empirical evaluation of the quality of agricultural economic development was conducted. The empirical results show that Zunyi, Bijie and Guiyang have a high level of agricultural economic development, ranking among the top three in the province, and have a greater advantage in development level compared with other cities and municipalities, while Qianxinan Autonomous Prefecture has the lowest level of agricultural economic development. The level of high-quality development of the agricultural economy in Guizhou province shows overall growth but uneven regional development. In response to these problems, this paper gives policy advice.", "Keywords": "Agricultural Economic ; Hesitant Fuzzy Linguistic Term Sets ; PROMETHEE Method ; High-quality Development ; Guizhou Province", "DOI": "10.1016/j.procs.2023.08.027", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>ng", "Affiliation": "School of Big Data Application and Economics, Guizhou University of Finance and Economics, Guiyang 550025, Guizhou, China;Guizhou Institution for Technology Innovation & Entrepreneurship Investment, Guizhou University of Finance and Economics, Guiyang 550025, Guizhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Big Data Application and Economics, Guizhou University of Finance and Economics, Guiyang 550025, Guizhou, China"}], "References": []}, {"ArticleId": 109358235, "Title": "Analysis of the Effectiveness of Carbon Emission Trading Market in China", "Abstract": "As global climate change intensifies, carbon emissions trading has become an important tool for countries to address climate change. China, as the world's largest carbon emitter, has been conducting carbon trading pilots since 2011, and officially launched a national carbon market in 2017. The construction of China's carbon market is crucial for China and global efforts to address climate change. Market efficiency is a key indicator of its success. This article aims to measure and analyze the effectiveness of China's carbon market, and to explore policy measures to enhance its effectiveness. It selected data from effective trading days and conducted an exploratory study on the effectiveness of China's pilot carbon markets. To analyze the effectiveness of the carbon market, we combined the efficient market hypothesis theory with the fractal market hypothesis theory, adopted the variance ratio test method, and the rescaled range analysis method to conduct a comprehensive analysis of the Chinese carbon market, while also accounting for the reasons behind their computational inconsistency. As our research shows that China's pilot carbon market has not reached the weak efficiency level, we have provided some suggestions based on the current operational status of China's carbon market.", "Keywords": "market effectiveness ; variance ratio test ; rescaled range analysis", "DOI": "10.1016/j.procs.2023.08.136", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 2, "Name": "Shuyang Peng", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100190, China;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> (<PERSON>) Li", "Affiliation": "Dornslife School of Arts and Science, University of Southern California, Los Angeles, CA 90007, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100190, China;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences, Beijing, 100190, China"}], "References": []}, {"ArticleId": 109358297, "Title": "Representing and processing polygonal maps with Sector Lists", "Abstract": "In this paper we describe a novel data structure called Sector Lists (SLs) to represent discrete scalar fields equivalent to polygonal maps. The idea is to model scalar fields as a sum of elementary, wedge-shaped constant fields — what we call sectors . Sectors are organized in sorted lists in order to support an efficient processing using algorithms based on the sweeping plane paradigm. As a result, SLs combine properties often associated with rasters with those of general polygons, in the sense the borders of regions mapped to the same value can be represented exactly. Algorithms to convert, evaluate, add, transform and morph SLs are described herein, with proof-of-concept implementations publicly available. We also show how several operations commonly supported by raster and polygon-based representations can be computed with SLs. In particular, we include the results of two experiments that demonstrate the flexibility and efficiency of our prototype implementation.", "Keywords": "", "DOI": "10.1016/j.cageo.2023.105418", "PubYear": 2023, "Volume": "179", "Issue": "", "JournalId": 4833, "JournalTitle": "Computers & Geosciences", "ISSN": "0098-3004", "EISSN": "1873-7803", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "COPPE - Programa de Pós-Graduação em Engenharia de Sistemas e Computação da Universidade Federal do Rio de Janeiro, Rio de Janeiro, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "COPPE - Programa de Pós-Graduação em Engenharia de Sistemas e Computação da Universidade Federal do Rio de Janeiro, Rio de Janeiro, Brazil"}], "References": [{"Title": "Fast summarizing algorithm for polygonal statistics over a regular grid", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "104524", "JournalTitle": "Computers & Geosciences"}, {"Title": "Comparison of different implementations of a raster map calculator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "154", "Issue": "", "Page": "104824", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 109358321, "Title": "Fintech and corporate governance: at times of financial crisis", "Abstract": "The objective of this research is to probe the moderating role of Big Four auditors (a representative of corporate governance) on the market performance of firms during the pandemic period, with specific focus on Fintech and non-Fintech firms. Design/Methodology: Employing data from 48 Fintech and 140 non-Fintech firms spanning 2010 to 2021, the study utilizes ordinary least squares, quantile regression, and dynamic Generalised Moments Method (GMM) regression to assess the implications of engaging with a Big Four auditor on firms' market performance during the pandemic. The study reveals that Fintech firms, compared to their non-Fintech counterparts, displayed a significantly poorer market performance by 110.4% during the pandemic. Additionally, Fintech firms audited by a Big Four auditor experienced a decline in market performance by 101.9%, indicating a potential negative impact of Big Four auditors' engagement for Fintech firms in crisis periods. The outcomes of this research underscore the importance of corporate governance during financial crises, and its influence on shareholder perception, especially in the context of Fintech firms. As such, it provides meaningful insights for governments, policymakers, and various practitioners including firm shareholders and start-up entrepreneurs. This study introduces a novel examination of the moderating effect of Big Four auditors on firms' market performance during a pandemic, especially in the context of Fintech firms. By shedding light on the relationship between corporate governance and market performance during crises, it fills a significant gap in the existing literature.", "Keywords": "Big four; Governance; Fintech; Market performance; GMM; Quantile regression", "DOI": "10.1007/s10660-023-09733-1", "PubYear": 2024, "Volume": "24", "Issue": "1", "JournalId": 2555, "JournalTitle": "Electronic Commerce Research", "ISSN": "1389-5753", "EISSN": "1572-9362", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Accounting, School of Business, Monash University, Bandar Sunway, Malaysia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Pôle Paris Alternance (PPA) Business School, Paris, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INTI International College Penang School of Business, Bayan Lepas, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Business, Liwa College, Abu Dhabi, UAE"}, {"AuthorId": 5, "Name": "Kaveh <PERSON>ei", "Affiliation": "Department of Accounting, School of Business, Monash University, Bandar Sunway, Malaysia"}], "References": [{"Title": "Fintech Growth during COVID-19 in MENA Region: Current Challenges and Future prospects", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "24", "Issue": "1", "Page": "371", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Does electronic economics matter to financial technology firms?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "24", "Issue": "1", "Page": "393", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Uncovering research trends and opportunities on FinTech: a scientometric analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "24", "Issue": "1", "Page": "105", "JournalTitle": "Electronic Commerce Research"}]}, {"ArticleId": *********, "Title": "Study on formability and deformation behavior of friction stir welding AA2024-7075 joint", "Abstract": "<p>Aluminum alloy tailor-welded blank (TWB) forming can achieve both material lightweight and structural lightweight, especially that dissimilar aluminum alloy TWB forming can better adapt to the different performance requirements of different parts of aerospace and automotive lightweight components to further achieve weight reduction and has a wide range of engineering application prospects. In this work, 2024-O and 7075-O aluminum alloy are used as the object of study. By observing the microstructure and measuring mechanical properties, establish the multiple- materials strength-mismatched of dissimilar friction stir welding (FSW) AA2024-7075 joints, to research the formability and deformation behavior of dissimilar FSW AA2024-7075 joints under different strength-mismatched conditions. Results show that during the tensile deformation of the dissimilar FSW AA2024-7075 joint, the equivalent stresses and strains in the weld nucleus area (WNZ) experience several abrupt changes. In the mixed WNZ of microstructures of the 2024WNZ and 7075WNZ, the AA7075 with high yield strength will have a restraining and protective effect on the AA2024 with low yield strength, which suppresses the deformation of AA2024. The heterogeneous microstructure of the dissimilar FSW AA2024-7075 joint leads to severe inhomogeneous deformation, which reduces the elongation. The 350 ℃ annealing heat treatment effectively reduces the inhomogeneity of the microstructure of each zone, lowers the difference of mechanical properties between AA2024 and AA7075, improves the uniformity of deformation of the dissimilar FSW AA2024-7075 joint, and increases the elongation from 8.5% to 12.8%.</p>", "Keywords": "Dissimilar aluminum alloy; Friction stir welding (FSW); Formability; Deformation behavior", "DOI": "10.1007/s00170-023-12081-x", "PubYear": 2023, "Volume": "128", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Advanced Technology of Automobile Components, Wuhan University of Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Advanced Technology of Automobile Components, Wuhan University of Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Metallurgical Equipment and Control Technology of Ministry of Education, Wuhan University of Science and Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Hubei Key Laboratory of Advanced Technology of Automobile Components, Wuhan University of Technology, Wuhan, People’s Republic of China; Hubei Longzhong Laboratory, Xiangyang, People’s Republic of China; Hubei Research Center for New Energy & Intelligent Connected Vehicle, Wuhan University of Technology, Wuhan, People’s Republic of China"}], "References": []}, {"ArticleId": 109358360, "Title": "Tuning of controller parameters using Pythagorean fuzzy similarity measure based on multicriteria decision making for stable and time delayed unstable plants", "Abstract": "<p> This paper proposes a tuning method based on the Pythagorean fuzzy similarity measure and multi-criteria decision-making to determine the most suitable controller parameters for Fractional-order Proportional Integral Derivative (FOPID) and Integer-order Proportional Integral-Proportional Derivative (PI-PD) controllers. Due to the power of the Pythagorean fuzzy approach to evaluate a phenomenon with two memberships known as membership and non-membership, a multi-objective cost function based on the Pythagorean similarity measure is defined. The transient and steady-state properties of the system output were used for the multi-objective cost function. Thus, the determination of the controller parameters was considered a multi-criteria decision-making problem. Ant colony optimization for continuous domains (ACO <sub>R</sub> ) and artificial bee colony (ABC) optimization are utilized to minimize multi-objective cost functions. The proposed method in the study was applied to three different systems: a second-order non-minimum phase stable system, a first-order unstable system with time delay, and a fractional-order unstable system with time delay, to validate its effectiveness. The cost function utilized in the proposed method is compared with the performance measures widely used in the literature based on the integral of the error, such as IAE (Integral Absolute Error), ITAE (Integral Time Absolute Error), ISE (Integral Square Error), and ITSE (Integral Time Square Error). The proposed method provides a more effective control performance by improving the system response characteristics compared to other cost functions. With the proposed method, the undershoot rate could be significantly reduced in the non-minimum phase system. In the other two systems, significant improvements were achieved compared to other methods by reducing the overshoot rate and oscillation. The proposed method does not require knowing the mathematical model of the system and offers a solution that does not require complex calculations. The proposed method can be used alone. Or it can be used as a second and fine-tuning method after a tuning process. </p>", "Keywords": "Pythagorean fuzzy logic;  Multi-attribute decision making;  Fractional-order PID;  PI-PD;  Optimization", "DOI": "10.7717/peerj-cs.1504", "PubYear": 2023, "Volume": "9", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering and Architecture, Department of Electrical-Electronic Engineering, Tokat Gaziosmanpasa University, Tokat, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering and Architecture, Department of Electrical-Electronic Engineering, Tokat Gaziosmanpasa University, Tokat, Turkey"}], "References": [{"Title": "Revisiting four approximation methods for fractional order transfer function implementations: Stability preservation, time and frequency response matching analyses", "Authors": "Furkan Nur Deniz; Baris <PERSON>kant <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "49", "Issue": "", "Page": "239", "JournalTitle": "Annual Reviews in Control"}, {"Title": "Tuning optimal PID controllers for open loop unstable first order plus time delay systems by minimizing ITAE criterion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "1", "Page": "123", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "A novel algorithm for global optimization: Rat Swarm Optimizer", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Atulya Nagar", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "8457", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": *********, "Title": "Optimal monetary policy under currency mismatches in imperfect financial market", "Abstract": "There is a large body of evidence documenting that many emerging market economies are subject to external financial shocks. We propose a two-country Dynamic General Equilibrium Model(DSGE) with three market imperfections: currency mismatches, moral hazard in banking and dominant currency pricing. Building upon <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (2020) and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2020), we analyze the optimal monetary policy in response to foreign interest rate shocks under the presence of the frictions. The results show that foreign monetary policy tightening leads to sharper local currency depreciation when import and export prices are set in the dominant currency compared to producer currency pricing. The optimal policy response to the foreign interest rate hike is a foreign exchange (FX) intervention with relatively strict inflation targeting. Macroprudential measures offer a modest macroeconomic and financial stabilization effect. We also conclude that domestically-produced goods inflation targeting alone is the worst monetary approach in EMs that attribute several real economy and financial imperfections. Nevertheless, allowing for integration of different monetary tools as well as introduction of FX hedging would be an interesting extension of our analysis.", "Keywords": "exchange rate ; financial frictions ; macroeconomic policy ; foreign interest rate shock ; DSGE", "DOI": "10.1016/j.procs.2023.08.091", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bank of Russia, 12 Neglinnaya Street, Moscow, 107016 Russia"}], "References": []}, {"ArticleId": *********, "Title": "A method for calculating two-dimensional spatially extension distances and its clustering algorithm", "Abstract": "This paper presents a novel method for calculating the extension distance between a point and a two-dimensional convex set. The proposed method builds upon existing techniques by employing a line traversal approach. By traversing the convex set with a line originating from the point of interest, the extension distance is computed based on the intersection points between the line and the set along the two coordinate axes. The weights assigned to the intersection points play a crucial role in the distance calculation. By summing the extension distances in both directions, the overall extension distance of the point with respect to the set is obtained. Furthermore, a clustering algorithm is introduced, which incorporates the concept of an interaction factor commonly used in engineering experiments. The multidimensional data is projected onto feature planes created by combining pairs of features. The calculation method for extension distances between points and convex sets in two dimensions is applied to determine the distances between points and cluster classes (or cluster centers). The distances on each feature plane are then weighted, summed, and analyzed to establish the relationships between points and cluster classes, thereby achieving clustering. The feasibility of the proposed algorithm is validated through comparative experiments k-means algorithm on the iris dataset and the wine dataset. The experimental results confirm the effectiveness and viability of the proposed approach.", "Keywords": "extenics ; entenics distance ; cluster algorithm", "DOI": "10.1016/j.procs.2023.08.105", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Yaocong Qin", "Affiliation": "Research Institute of Extenics and Innovation Methods, GuangZhou 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Institute of Extenics and Innovation Methods, GuangZhou 510006, China"}], "References": [{"Title": "Intelligent Problem Solving Model and its Cross Research Directions Based on Factor Space and Extenics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "3", "Page": "469", "JournalTitle": "Annals of Data Science"}, {"Title": "Design of distributed hybrid electric tractor based on axiomatic design and Extenics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101765", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 109358378, "Title": "Fast density peaks clustering algorithm based on improved mutual K-nearest-neighbor and sub-cluster merging", "Abstract": "Density peaks clustering (DPC) has had an impact in many fields, as it can quickly select centers and effectively process complex data. However, it also has low operational efficiency and a “Domino” effect. To solve these defects, we propose a fast density peaks clustering algorithm based on improved mutual K-nearest-neighbor and sub-cluster merging (KS-FDPC). The new algorithm adopts a partitioning-merging strategy. By dividing the data into multiple sub-clusters, the impact range of high-density points on subsequent allocation points can be reduced. And the fast nearest neighbors search improves the speed of DPC. In the experiment, KS-FDPC is used to compare with eight improved DPC algorithms on eight synthetic data and eight UCI data. The results indicate that the overall clustering performance of KS-FDPC is superior to other algorithms. Moreover, KS-FDPC runs faster than other algorithms. Therefore, KS-FDPC is an effective improvement of DPC. Introduction As an inter-discipline in machine learning and database, data mining can search for hidden knowledge from massive data to support decision-making. As a preprocessing method, clustering makes a difference in data mining. By grouping different data, clustering aims to maximize intra-class affinity and minimize inter-class affinity. Each class shows some overall characteristics of data distribution so that one or more of them can be efficiently processed under the condition of limited resources. Clustering analysis has extensive applications, including text mining [1], image retrieval [2], social networking [3], and e-commerce shopping [4]. Recently a mass of excellent clustering algorithms has been put forward [5], [6], [7], [8], [9], among which partition-based [5], [6], [7] and density-based clustering methods [8], [9] are the main research directions of clustering algorithms. K-means [10] and GMM [11] are two classical partition-based clustering algorithms with fast convergence speed and simple implementation. However, the density-based clustering algorithms [12], [13] have more advantages when dealing with data with complex shapes. But this kind of algorithm often needs to calculate the similarity between arbitrary sample pairs, which has high time complexity and can not efficiently process a large number of data. Density peaks clustering (DPC) [14] is one of the density-based clustering algorithms. It uses the similarity matrix to estimate the local density of each sample and then combines the relative distance to select center points and complete the clustering. The algorithm is apt to program and can effectively deal with many non-convex datasets. However, the higher complexity (O( n <sup>2</sup>)) required by DPC will limit its application on a large amount of data [17], [18]. What’s more, DPC will produce a “Domino” effect when assigning non-central points [20], that is, the wrong allocation of a sample will produce a chain reaction. Aiming at the clustering efficiency and “Domino” effect of DPC, we propose a fast density peaks clustering based on improved mutual K-nearest neighbors and sub-cluster merging (KS-FDPC). KS-FDPC takes far less time to process large-scale data than other improved DPC algorithms. In KS-FDPC, we have mainly made the following three contributions: 1) The improved mutual KNN is adopted to divide the dataset and calculate the sample density. For the relative distance of each sample point, we only search it within the mutual KNN, so the time complexity of the algorithm can be effectively reduced to O( n log n ). 2) After generating reams of initial sub-clusters, the computed inter-cluster similarities are sorted from large to small. The two sub-clusters with maximum similarity are merged in turn. The algorithm stops until the number of sub-clusters generated equals that of real sub-clusters, or the maximum similarity between sub-clusters is zero. 3) We replace the fully connected matrix in KS-FDPC with a sparse matrix to cut down the space resource and deal with large-scale datasets. In this section, we briefly introduce the DPC algorithm and its shortcomings and propose a new algorithm. The research on the DPC algorithm and the details of the new algorithm will be introduced in subsequent sections. The structure of the remaining part is as follows. In Section 2 we introduce the DPC algorithm and analyze its shortcomings. In Section 3, the idea and steps of KS-FDPC are described in detail. We present and analyze the experimental results obtained by all algorithms in Section 4. Finally, the full text and the next work are summarized. Section snippets Related work For sake of clarity, two tables, Table 1 and Table 2, are provided to describe the abbreviations and symbols of the entire text, respectively. KS-FDPC algorithm KS-FDPC mainly consists of three parts, including initial sub-cluster partitioning, initial sub-cluster merging, and final sub-cluster merging. Next, we will introduce these three parts one by one. Experiments For verifying the running rate of KS-FDPC and its improvement on the “Domino” effect on non-convex datasets, we conduct experiments on artificial and UCI datasets in this section. In addition to DPC [14], we also compare KS-FDPC with DPC-KNN-PCA [23], DPCSA [24], SDPC [25], FSDPC [26], PS-DPC [27], LDP-MST [31] and HC-LCCV [38] algorithms. DPC-KNN-PCA utilizes both data dimension reduction and density optimization, wherein only parameter K has a great influence on the results. DPCSA also Conclusion In this article, a fast density peaks clustering algorithm based on improved mutual K-nearest neighbors and sub-cluster merging is proposed. This algorithm uses Kd-tree to accelerate the nearest neighbors search while dividing the dataset into multiple sub-clusters to alleviate the “Domino” effect. To deal with large-scale datasets, the fully connected matrix of the algorithm is replaced by a sparse matrix to reduce the space complexity. In the experiment, we mainly compared KS-FDPC with eight CRediT authorship contribution statement Chao Li: Conceptualization, Methodology, Software. Shifei Ding: Supervision. Xiao Xu: Supervision. Haiwei Hou: Writing – review & editing. Ling Ding: . Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgement This work is supported by the National Natural Science Foundation of China (no.62276265, no.61976216 and no.62206296). References (39) E. Zhu et al. Fast and stable clustering analysis based on Grid-mapping K-means algorithm and new clustering validity index Neurocomputing (2019) R. Liu et al. Shared-nearest-neighbor-based clustering by fast search and find of density peaks Inf. Sci. (2018) J. Xie et al. Robust clustering by detecting density peaks and assigning points based on fuzzy weighted K-nearest neighbors Inf. Sci. (2016) S. Ding et al. An improved density peaks clustering algorithm based on natural neighbor with a merging strategy Inf. Sci. (2023) M. Du et al. Study on density peaks clustering based on k-nearest neighbors and principal component analysis Knowl.-Based Syst. (2016) X. Xu et al. An improved density peaks clustering algorithm with fast finding cluster centers Knowl.-Based Syst. (2018) X. Xu et al. A fast density peaks clustering algorithm with sparse search Inf. Sci. (2021) S. Sieranoja et al. Fast and general density peaks clustering Pattern Recogn. Lett. (2019) Y. Wang et al. VDPC: variational density peaks clustering algorithm Inf. Sci. (2023) D. Fuentealba et al. Effects on time and quality of short text clustering during real-time presentations IEEE Lat. Am. Trans. (2021) Rudrappa G, Vijapur N. Cloud Classification using K-Means Clustering and Content based Image Retrieval Technique. 2020... A. Pister et al. Integrating prior knowledge in mixed-initiative social network clustering IEEE Trans. Vis. Comput. Graph. (2021) Peng M, Hu B, Tan Z. Hierarchical Structure of E-commerce Big Data Clustering Based on Hadoop Platform. 2021 5th... M. Li et al. The seeding algorithms for spherical k-means clustering J. Glob. Optim. (2020) L. Yao et al. Scalable semisupervised GMM for big data quality prediction in multimode processes IEEE Trans. Ind. Electron. (2019) Jebari S, Smiti A, Louati A. AF-DBSCAN: An unsupervised Automatic Fuzzy Clustering method based on DBSCAN approach.... Xu X, Ding S, Sun T. A Fast Density Peaks Clustering Algorithm Based on Pre-Screening. 2018 IEEE International... Macqueen J. Some methods for classification and analysis of multivariate observations. Proceedings of the 5th Berkeley... A.P. Dempster et al. Maximum likelihood from incomplete data via the EM algorithm J. R. Stat. Soc. Ser. B (1977) View more references Cited by (0) Recommended articles (6) Research article Group decision making with incomplete interval-valued linguistic intuitionistic fuzzy preference relations Information Sciences, Volume 647, 2023, Article 119451 Show abstract With the increasing complexity and indeterminacy intensity of the decision making environment, there is a growing lack of widespread utility of traditional preference representations. As a practical yet valid preference expression, interval-valued linguistic intuitionistic fuzzy preference relations (IVLIFPRs) can efficiently represent the pairwise cognitions qualitatively and provide the inclusive support. This study aims at forming a novel group decision making (GDM) method based on the consistency and consensus preference analysis for IVLIFPRs. First, the new concepts of additive consistency and acceptably additive consistency for IVLIFPRs are defined. Based on these definitions, two optimization models are constructed to obtain the incomplete information and acceptably additive consistent IVLIFPRs, respectively. Then, inspired by the Markov model, the derivation approach of DMs' weights is designed. Afterward, the consensus improving process is constructed under the premise of acceptable consistency and smallest information distortion. Accordingly, the normalized priority weights of alternatives are further determined. Thus, a mechanism for the GDM method with incomplete and unacceptably consistent IVLIFPRs is developed. Finally, an application and comparison analysis show that the proposed method comprehensively considers the key steps in the GDM process, so that the logical rationality and technical superiority are well guaranteed. Research article Applying evidence-based strategies for public health preparedness and emergency management Principles and Application of Evidence-based Public Health Practice, 2024, pp. 49-70 Show abstract Public health practice includes delivering preventive, promotive, curative, and rehabilitative healthcare services at normal times and during emergencies. Though management of public health emergencies or disasters is integral to public health practice, it remains a neglected area across countries. Public health emergency and disaster terminologies are interchangeably used and nearly carry the same definitions. A routine practice situation becomes an emergency when the scale, timing, or unpredictability threatens to overwhelm routine capabilities to tackle them. In this background, the public health emergency is not the same for all conditions, people, settings, countries, and time periods. The principles and capabilities of a public health emergency preparedness and response need to be adopted in the routine public health practice for effective prevention and management of any emergency. Evidence-based strategies must be generated and used to prepare and manage public health emergencies. Advanced technologies potentially improve the efficiency of the public health system in public health preparedness and response. The growing threats of public health emergencies, especially due to globalization, need to be predicted and prevented in time. Research article Fast density estimation for density-based clustering methods Neurocomputing, Volume 532, 2023, pp. 170-182 Show abstract Density-based clustering algorithms are widely used for discovering clusters in pattern recognition and machine learning. They can deal with non-hyperspherical clusters and are robust to outliers. However, the runtime of density-based algorithms is heavily dominated by neighborhood finding and density estimation which is time-consuming. Meanwhile, the traditional acceleration methods using indexing techniques such as KD-tree may not be effective when the dimension of the data increases. To address these issues, this paper proposes a fast range query algorithm, called Fast Principal Component Analysis Pruning (FPCAP), with the help of the fast principal component analysis technique in conjunction with geometric information provided by the principal attributes of the data. Based on FPCAP, a framework for accelerating density-based clustering algorithms is developed and successfully applied to accelerate the Density Based Spatial Clustering of Applications with Noise (DBSCAN) algorithm and the BLOCK-DBSCAN algorithm, and improved DBSCAN (called IDBSCAN) and improved BLOCK-DBSCAN (called BLOCK-IDBSCAN) are then obtained, respectively. IDBSCAN and BLOCK-IDBSCAN preserve the advantage of DBSCAN and BLOCK-DBSCAN, respectively, while greatly reducing the computation of redundant distances. Experiments on seven benchmark datasets demonstrate that the proposed algorithm improves the computational efficiency significantly. Research article Varying-scale HCA-DBSCAN-based anomaly detection method for multi-dimensional energy data in steel industry Information Sciences, Volume 647, 2023, Article 119479 Show abstract The quality of the acquisition data in the energy system of steel industry is the basis of prediction analysis and scheduling operation. Facing with its multi-dimensional and high-noise characteristics, in this study, an anomaly detection method based on a varying-scale hypercube accelerated density based spatial clustering for applications with noise (VHCA-DBSCAN) is proposed. An HCA-DBSCAN model based on the Gaussian probability density estimation is firstly established, in which the searching radius is adaptively calculated to identify the suspected outliers efficiently. Then, a traversal search strategy for hypercube segmentation is designed, where the lengths of the edges are varying according to the characteristics of each dimension. Finally, a modified local outlier factor (LOF) by using the approximate upper boundary is presented to evaluate the degree of the suspected outliers, and the confirmed ones are identified by collaboratively considering the manufacturing signals. Validation experiments by employing real-world data from a typical steel enterprise are carried out, and the results indicate that the proposed method exhibits reliable performance and high efficiency when facing with the anomaly detection problem of multi-dimensional industrial data. Research article Density peaks clustering based on balance density and connectivity Pattern Recognition, Volume 134, 2023, Article 109052 Show abstract Density peaks clustering (DPC) algorithm regards the density peaks as the potential cluster centers, and assigns the non-center point into the cluster of its nearest higher-density neighbor. Although DPC can discover clusters with arbitrary shapes, it has some limitations. On the one hand, the density measure of DPC fails to eliminate the density difference among different clusters, which affects the accuracy of recognizing cluster center. On the other hand, the nearest higher-density point is determined without considering connectivity, which leads to continuously clustering errors. Therefore, DPC fails to obtain satisfactory clustering results on datasets with great density difference among clusters. In order to eliminate these limitations, a novel DPC algorithm based on balance density and connectivity (BC-DPC) is proposed. First, the balance density is proposed to eliminate the density difference among different clusters to accurately recognize cluster centers. Second, the connectivity between a data point and its nearest higher-density point is guaranteed by mutual nearest neighbor relationship to avoid continuously clustering errors. Finally, a fast search strategy is proposed to find the nearest higher-density point. The experimental results on synthetic, UCI, and image datasets demonstrate the efficiency and effectiveness of the proposed algorithm in this paper. Research article EADP: An extended adaptive density peaks clustering for overlapping community detection in social networks Neurocomputing, Volume 337, 2019, pp. 287-302 Show abstract Overlapping community detection plays an important role in studying social networks. The existing overlapping community detection methods seldom perform well on networks with complex weight distribution. Density peaks clustering (DPC) is capable of finding communities with arbitrary shape efficiently and accurately. However, DPC fails to be applied to overlapping community detection directly. In this paper, we propose an extended adaptive density peaks clustering for overlapping community detection, called EADP. To handle both weighted and unweighted social networks, EADP takes weights into consideration and incorporates a novel distance function based on common nodes to measure the distance between nodes. Moreover, unlike DPC choosing cluster centers by hand, EADP adopts a linear fitting based strategy to choose cluster centers adaptively. Experiments on real-world social networks and synthetic networks show that EADP is an effective overlapping community detection algorithm. Compared with the state-of-the-art methods, EADP performs better on those networks with complex weight distribution. View full text © 2023 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119470", "PubYear": 2023, "Volume": "647", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221116, China;Mine Digitization Engineering Research Center of Ministry of Education of the People's Republic of China, Xuzhou 221116, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221116, China;Mine Digitization Engineering Research Center of Ministry of Education of the People's Republic of China, Xuzhou 221116, China;Corresponding author at: School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221116, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221116, China;Mine Digitization Engineering Research Center of Ministry of Education of the People's Republic of China, Xuzhou 221116, China"}, {"AuthorId": 4, "Name": "Haiwei <PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221116, China;Mine Digitization Engineering Research Center of Ministry of Education of the People's Republic of China, Xuzhou 221116, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin 300350, China"}], "References": [{"Title": "Mk-NNG-DPC: density peaks clustering based on improved mutual K-nearest-neighbor graph", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "1179", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Density peaks clustering based on circular partition and grid similarity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "7", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "McDPC: multi-center density peak clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "17", "Page": "13465", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A fast density peaks clustering algorithm with sparse search", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "554", "Issue": "", "Page": "61", "JournalTitle": "Information Sciences"}, {"Title": "VDPC: Variational density peak clustering algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "621", "Issue": "", "Page": "627", "JournalTitle": "Information Sciences"}, {"Title": "A Sampling-Based Density Peaks Clustering Algorithm for Large-Scale Data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "109238", "JournalTitle": "Pattern Recognition"}, {"Title": "An improved density peaks clustering algorithm based on natural neighbor with a merging strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "252", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 109358383, "Title": "Analyzing Public Perception of Educational Books via Text Mining of Online Reviews", "Abstract": "With the development of the era of big data, online reviews have become an important basis for influencing the decisions of consumers, sellers and producers. The research on the publishing of educational books from the perspective of public demand side can provide new ideas for the development of book publishing. This paper explores the public's perception of educational books by text mining reviews of educational books on Douban Books, using a combination of keyword analysis, topic analysis and aspect-level sentiment analysis using tools such as Python. The results indicate that the content quality of educational books strongly influences users' perception. This study reveals the public's perspectives on different aspects of educational books, aiding publishers and authors in gaining deeper insights into areas that require attention and improvement. Based on the analysis results, scientific recommendations are proposed to enhance the quality and publishing standards of educational books, assisting publishers in making publishing decisions that meet the evolving needs of the public. This research contributes to the development of educational book publishing by providing valuable insights and recommendations.", "Keywords": "book publishing research ; Aspect level sentiment analysis ; Text mining ; Douban Books", "DOI": "10.1016/j.procs.2023.08.030", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Management Science and Engineering, Hebei University of Economics and Business Shijiazhuang, Hebei 050061, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Management Science and Engineering, Hebei University of Economics and Business Shijiazhuang, Hebei 050061, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Management Science and Engineering, Hebei University of Economics and Business Shijiazhuang, Hebei 050061, China"}], "References": [{"Title": "Customer preferences extraction for air purifiers based on fine-grained sentiment analysis of online reviews", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "228", "Issue": "", "Page": "107259", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Sourcing product innovation intelligence from online reviews", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "113751", "JournalTitle": "Decision Support Systems"}, {"Title": "Leveraging genre classification with RNN for Book recommendation", "Authors": "Mala Sara<PERSON>wat;  <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "7", "Page": "3751", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 109358488, "Title": "Recognition of Hindi Character Using OCR-Technology: A Review", "Abstract": "Recognition of character is a technique that enables the transformation of various kinds of scanned papers into an editable, readable, and searchable format. In the last two decades, several researchers and technologists have been continuously working in this field to enhance the rate of accuracy. Recognition of character is classified into printed, hand-written, and characters written at image recognition. Recognition of character is the major area of research in the field of pattern recognition. This paper presents an overview of Hindi character recognition by utilizing the optical character recognition (OCR) technique. We surveyed some major research breakthroughs in character recognition, especially for Hindi characters. This research article focuses to provide a deeper insight into the researchers and technologists working in the field of recognition of Hindi-character.", "Keywords": "", "DOI": "10.30534/ijatcse/2023/071242023", "PubYear": 2023, "Volume": "12", "Issue": "4", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 109358514, "Title": "Revelation of hidden 2D atmospheric turbulence strength fields from turbulence effects in infrared imaging", "Abstract": "Turbulence exists widely in the natural atmosphere and in industrial fluids. Strong randomness, anisotropy and mixing of multiple-scale eddies complicate the analysis and measurement of atmospheric turbulence. Although the spatially integrated strength of atmospheric turbulence can be roughly measured indirectly by Doppler radar or laser, direct measurement of two-dimensional (2D) strength fields of atmospheric turbulence is challenging. Here we attempt to solve this problem through infrared imaging. Specifically, we propose a physically boosted cooperative learning framework, termed the PBCL, to quantify 2D turbulence strength from infrared images. To demonstrate the capability of the PBCL, we constructed a dataset with 137,336 infrared images and corresponding 2D turbulence strength fields. The experimental results show that cooperative learning brings performance improvements, enabling the PBCL to simultaneously learn turbulence strength fields and inhibit adverse turbulence effects in images. Our work demonstrates the potential of imaging in measuring physical quantity fields. This study introduces a physically boosted cooperative learning framework (PBCL) to reveal 2D atmospheric turbulence strength fields from turbulence-distorted infrared images. The PBCL is further demonstrated to inhibit adverse turbulence effects in images.", "Keywords": "Computational science;Fluid dynamics;Imaging and sensing;Computer Science;general", "DOI": "10.1038/s43588-023-00498-z", "PubYear": 2023, "Volume": "3", "Issue": "8", "JournalId": 83518, "JournalTitle": "Nature Computational Science", "ISSN": "", "EISSN": "2662-8457", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Image Processing Center, Beihang University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Image Processing Center, Beihang University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Image Processing Center, Beihang University, Beijing, China"}, {"AuthorId": 4, "Name": "Xiangzhi Bai", "Affiliation": "Image Processing Center, Beihang University, Beijing, China; State Key Laboratory of Virtual Reality Technology and Systems, Beihang University, Beijing, China; Advanced Innovation Center for Biomedical Engineering, Beihang University, Beijing, China"}], "References": [{"Title": "Blind de-convolution of images degraded by atmospheric turbulence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106131", "JournalTitle": "Applied Soft Computing"}, {"Title": "Neutralizing the impact of atmospheric turbulence on complex scene imaging via deep learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "10", "Page": "876", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 109358530, "Title": "Creating Universum for class imbalance via locality and its application in multiview subspace learning", "Abstract": "Since the existing methods for generating Universum fail in class imbalance cases, we propose a new method for creating Universum via locality and betweenness , named L-CIBU. Compared to existing methods for generating Universum, L-CIBU not only generates appropriate Universum in the case of class imbalance but also has higher efficiency. Given the existence of class imbalance in multiview learning, we introduce Universum to multiview subspace learning (MvSL) to further enhance its performance and L-CIBU happens to be able to overcome this problem. As far as we know, this is the first time to introduce Universum into MvSL with more than two views. Specifically, we introduce Universum created by L-CIBU into multiview discriminant analysis (MvDA) and propose Universum MvDA (UMvDA). By analyzing several randomly generated imbalanced datasets, it is intuitively demonstrated that L-CIBU is more effective than FIBU and CIBU in the case of class imbalance. The experimental results on two real datasets show that the time required for creating the Universum using L-CIBU is 25 % ∼ 95 % less than that of FIBU and CIBU. Moreover, UMvDA achieves an accuracy improvement of approximately 1 % ∼ 6 % compared to MvDA, Universum canonical correlation analysis (UCCA), and Universum discriminant canonical correlation analysis (UDCCA).", "Keywords": "", "DOI": "10.1016/j.ins.2023.119478", "PubYear": 2023, "Volume": "647", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Westlake University, Hangzhou, 310024, PR China;Westlake Institute for Advanced Study, Westlake University, Hangzhou, 310024, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Westlake University, Hangzhou, 310024, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "Jia<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, Beijing University of Posts and Telecommunications, Beijing, 100876, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Management School, Hainan University, Haikou, 570228, PR China;Corresponding author"}, {"AuthorId": 5, "Name": "Yuan<PERSON><PERSON>", "Affiliation": "Management School, Hainan University, Haikou, 570228, PR China"}], "References": [{"Title": "Multi-view subspace learning via bidirectional sparsity", "Authors": "<PERSON><PERSON><PERSON> Fan; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107524", "JournalTitle": "Pattern Recognition"}, {"Title": "Robust multi-view discriminant analysis with view-consistency", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "596", "Issue": "", "Page": "153", "JournalTitle": "Information Sciences"}, {"Title": "KNN weighted reduced universum twin SVM for class imbalance learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "245", "Issue": "", "Page": "108578", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Adaptive robust Adaboost-based twin support vector machine with universum data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "609", "Issue": "", "Page": "1334", "JournalTitle": "Information Sciences"}, {"Title": "Robust multi-view learning via adaptive regression", "Authors": "Bingbing Jiang; <PERSON><PERSON>; Xingyu Wu", "PubYear": 2022, "Volume": "610", "Issue": "", "Page": "916", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 109358564, "Title": "Joint online 3D trajectory optimisation and power allocation of UAVs as mobile access points in a downlink distributed multi-user MIMO system", "Abstract": "Unmanned aerial vehicles (UAVs) are introduced as one of the key-enablers for 5G beyond networks due to their low cost and flexible deployment, and mobility degrees-of-freedom. Moreover, low sensitivity to blockage and uncorrelated channels in distributed multiple-input multiple-output (D-MIMO) systems, and their extension which is called cell-free massive MIMO , have made them an attractive research field. The location and number of access points (APs) in D-MIMO systems highly influence the system performance in terms of energy and spectral efficiency that can be handled by UAVs mobility and flexible deployment. In this paper, joint power allocation , and 3D trajectory design in a D-MIMO system for a downlink scenario where multiple UAVs serve as mobile APs (MbAPs) is investigated. Contrary to the existing works in multi-mobile base stations, we introduce space-division multiple access (SDMA) for the online trajectory design as a multi-access technology which does not suffer from the non-casual channel property in traditional trajectory designs. In the proposed design, the smallest user’s ergodic rate lower bound is maximised by jointly optimising MbAPs’ 3D trajectory and transmit power. The block coordinate descent (BCD) algorithm is deployed to break the main problem into two sub-problems and iteratively optimise over the two. Considering the non-convex nature of both of sub-problems, successive convex approximation is also exploited. For performance analysis, complexity and convergence property of the proposed algorithm are investigated. Finally, numerical results validate the improved performance of the proposed system in a practical scenario, where users can connect and disconnect to and from the network.", "Keywords": "", "DOI": "10.1016/j.phycom.2023.102163", "PubYear": 2023, "Volume": "60", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Iran University of Science and Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Iran University of Science and Technology, Tehran, Iran"}, {"AuthorId": 3, "Name": "Abolfazl Falahati", "Affiliation": "Department of Electrical Engineering, Iran University of Science and Technology, Tehran, Iran;Corresponding author"}], "References": [{"Title": "Spectral efficiency in non-terrestrial heterogeneous networks with spectrum underlay access", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "46", "Issue": "", "Page": "101313", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 109358570, "Title": "Robust stability of dynamical neural networks with multiple time delays: a review and new results", "Abstract": "<p>Robust stability properties of continuous-time dynamical neural networks involving time delay parameters have been extensively studied, and many sufficient criteria for robust stability of various classes of delayed dynamical neural networks have been obtained in the past literature. The class of activation functions and the types of delay terms involved in the mathematical models of dynamical neural networks are two main parameters in the determination of stability conditions for these neural network models. In this article, we will analyse a neural network model of relatively having a more complicated mathematical form where the neural system has the multiple time delay terms and the activation functions satisfy the Lipschitz conditions. By deriving a new and alternative upper bound value for the (l_2) -norm of uncertain intervalised matrices and constructing some various forms of the same type of a Lyapunov functional, this paper will first propose new results on global robust stability of dynamical Hopfield neural networks having multiple time delay terms in the presence of the <PERSON><PERSON><PERSON><PERSON> activation functions. Then, we show that some simple modified changes in robust stability conditions proposed for multiple delayed Hopfield neural network model directly yield robust stability conditions of multiple delayed Cohen-Grossberg neural network model. We will also make a very detailed review of the previously published robust stability research results, which are basically in the nonsingular M-matrix or various algebraic inequalities forms. In particular, the robust stability results proposed in this paper are proved to generalize almost all previously reported robust stability conditions for multiple delayed neural network models. Some concluding remarks and future works regarding robust stability analysis of dynamical neural systems are addressed.</p>", "Keywords": "Dynamical neural systems; Interval matrices; Multiple time delays; Lipschi<PERSON> functions; <PERSON><PERSON><PERSON>nov stability theorems", "DOI": "10.1007/s10462-023-10552-x", "PubYear": 2023, "Volume": "56", "Issue": "S2", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering and Architecture, Istanbul Nisantasi University, Istanbul, Turkey"}, {"AuthorId": 2, "Name": "Ozlem Faydasicok", "Affiliation": "Department of Mathematic, Faculty of Science, Istanbul University, Istanbul, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Istanbul University-Cerrahpasa, Istanbul, Turkey"}], "References": [{"Title": "New criteria on the finite-time stability of fractional-order BAM neural networks with time delay", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "4501", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 109358595, "Title": "Multi-kernel learning for multi-label classification with local Rademacher complexity", "Abstract": "Multi-label classification aims to construct prediction models from input space to output space for multi-label datesets. However, the feature space of multi-label dataset and the hypothesis space of classifier are always complex. In order to obtain high performance multi-kernel learning algorithms, the core problem is to design algorithms that compress both hypothesis space and feature space. In this paper, by combining the local Rademacher complexity and Hilbert-Schmidt independence criterion (HSIC), an effective multi-kernel learning algorithm for multi-label classification is proposed to compress the feature space and hypothesis space simultaneously. Based on the tail sum of eigenvalues of the integral operator corresponding to kernels, the upper bound of local Rademacher complexity of linear functions class in the feature space is determined. The Hilbert-Schmidt independence criterion is applied to maximize the correlation between the convex combination of the input kernel matrices and the ideal kernel matrix in the label space. Finally, the Laplace regularization model is built by controlling the tail sum of eigenvalues of the integral operator corresponding to kernels of this criterion. Therefore, the above process obtains a combined kernel, simultaneously by sorting the coefficients of the combined kernel, a feature selection algorithm can be constructed. On the basis, the combined kernel can also be used to design a new binary classifier for multi-label classification. The experimental results verify that our proposed multi-kernel learning algorithms can effectively compress the hypothesis space, while the feature selection algorithms can effectively compress the feature space.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119462", "PubYear": 2023, "Volume": "647", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of control and computer engineering, North China Electric Power University, Beijing 100206, PR China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of mathematics and physics, North China Electric Power University, Beijing 100206, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of mathematics and physics, North China Electric Power University, Beijing 100206, PR China"}], "References": [{"Title": "Identification of Drug–Target Interactions via Dual Laplacian Regularized Least Squares with Multiple Kernel Fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "204", "Issue": "", "Page": "106254", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An efficient Pareto-based feature selection algorithm for multi-label classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "581", "Issue": "", "Page": "428", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 109358604, "Title": "Crude oil price prediction using temporal fusion transformer model", "Abstract": "In this paper, we applied the temporal fusion transformer model to the crude oil price movement modeling and forecasting. The temporal fusion transformer model has been adopted in the crude oil price forecasting model using the attention mechanism, to capture the different level of autocorrelations among observations in the crude oil prices. Empirical evaluation of the transformer based multi-horizon ahead crude oil price forecasting model has been conducted. Experiment results show that the introduction of transformer model in the forecasting process has improved the forecasting accuracy significantly at longer time horizon.", "Keywords": "Crude oil price forecasting ; Temporal Fusion Transformer model ; ARIMA", "DOI": "10.1016/j.procs.2023.08.070", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Tourism , Hunan Normal University , Changsha 410081 , China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Tourism , Hunan Normal University , Changsha 410081 , China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Tourism , Hunan Normal University , Changsha 410081 , China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Tourism Management, Macao Institute for Tourism Studies , Macao , China;Department of Marketing , City University of Hong Kong , Hong Kong , China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Foreign Languages , Hunan University , Changsha , 410082 , China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Tourism , Hunan Normal University , Changsha 410081 , China"}], "References": [{"Title": "A hybrid approach of adaptive wavelet transform, long short-term memory and ARIMA-GARCH family models for the stock index prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115149", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Forecasting crude oil risk: A multiscale bidirectional generative adversarial network based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "118743", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Forecasting crude oil futures prices using BiLSTM-Attention-CNN model with Wavelet transform", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "109723", "JournalTitle": "Applied Soft Computing"}, {"Title": "High-frequency forecasting of the crude oil futures price with multiple timeframe predictions fusion", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "217", "Issue": "", "Page": "119580", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Crude oil price forecasting with machine learning and Google search data: An accuracy comparison of single-model versus multiple-model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106266", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 109358648, "Title": "To see further: Knowledge graph-aware deep graph convolutional network for recommender systems", "Abstract": "Applying a graph convolutional network (GCN) or its variants to user-item interaction graphs is one of the most commonly used approaches for learning the representation of users and items in modern recommender systems . However, these models perform poorly in recommending high-order items due to the over-smoothing when the GCN deepens and the lack of connectivity between the cold-start items and the target user. To improve the ability to recommend higher-order items, we propose our Knowledge graph-aware Deep Graph Convolutional Network with Initial Residual Connection (KDGCN-IC) and Knowledge graph-aware Deep Graph Convolutional Network with Dense Connection (KDGCN-DC) methods. First, we introduce an item knowledge graph to rebuild the connectivity between nodes in the user-item graph, especially the users and the cold-start items. Then, we present our methods for improving the information flow by reusing features to alleviate the over-smoothing issue to deepen the GCN to capture the higher-order collaborative signal. More specifically, KDGCN-IC reuses the initial feature information, and KDGCN-DC reuses the feature information of each layer. Extensive experiments demonstrated that our models were able to achieve substantial improvement over state-of-the-art models in so far as their recommendation accuracy and higher-order recommendations.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119465", "PubYear": 2023, "Volume": "647", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Remote Sensing and Information Engineering, Wuhan University, Wuhan, Hubei, China;Changjiang Spatial Information Technology Engineering Co., Ltd, Wuhan, China;Water Resources Information Perception and Big Data Engineering Research Center of Hubei Province, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Remote Sensing and Information Engineering, Wuhan University, Wuhan, Hubei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Remote Sensing and Information Engineering, Wuhan University, Wuhan, Hubei, China;Corresponding author"}, {"AuthorId": 4, "Name": "Yansheng Li", "Affiliation": "School of Remote Sensing and Information Engineering, Wuhan University, Wuhan, Hubei, China;Hubei Luojia Laboratory, Wuhan, Hubei, China;Corresponding author at: School of Remote Sensing and Information Engineering, Wuhan University, Wuhan, Hubei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Basic Geographic Information Center of Guizhou Province, Guiyang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "The First Institute of Photogrammetry and Remote Sensing, Ministry of Natural Resources, China"}], "References": []}, {"ArticleId": 109358674, "Title": "Prediction of mechanistic subtypes of Parkinson’s using patient-derived stem cell models", "Abstract": "Parkinson’s disease is a common, incurable neurodegenerative disorder that is clinically heterogeneous: it is likely that different cellular mechanisms drive the pathology in different individuals. So far it has not been possible to define the cellular mechanism underlying the neurodegenerative disease in life. We generated a machine learning-based model that can simultaneously predict the presence of disease and its primary mechanistic subtype in human neurons. We used stem cell technology to derive control or patient-derived neurons, and generated different disease subtypes through chemical induction or the presence of mutation. Multidimensional fluorescent labelling of organelles was performed in healthy control neurons and in four different disease subtypes, and both the quantitative single-cell fluorescence features and the images were used to independently train a series of classifiers to build deep neural networks. Quantitative cellular profile-based classifiers achieve an accuracy of 82%, whereas image-based deep neural networks predict control and four distinct disease subtypes with an accuracy of 95%. The machine learning-trained classifiers achieve their accuracy across all subtypes, using the organellar features of the mitochondria with the additional contribution of the lysosomes, confirming the biological importance of these pathways in Parkinson’s. Altogether, we show that machine learning approaches applied to patient-derived cells are highly accurate at predicting disease subtypes, providing proof of concept that this approach may enable mechanistic stratification and precision medicine approaches in the future.", "Keywords": "High-throughput screening;Neurodegeneration", "DOI": "10.1038/s42256-023-00702-9", "PubYear": 2023, "Volume": "5", "Issue": "8", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "London, UK Department of Clinical and Movement Neurosciences, UCL Queen Square Institute of Neurology. ;London, UK The Francis Crick Institute, King's Cross."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "London, UK Department of Clinical and Movement Neurosciences, UCL Queen Square Institute of Neurology. ;London, UK The Francis Crick Institute, King's Cross."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "London, UK Department of Clinical and Movement Neurosciences, UCL Queen Square Institute of Neurology. ;London, UK The Francis Crick Institute, King's Cross."}, {"AuthorId": 4, "Name": "Giulia <PERSON>", "Affiliation": "Faculty, Marylebone, London, UK."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty, Marylebone, London, UK."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty, Marylebone, London, UK."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "London, UK The Francis Crick Institute, King's Cross."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, Republic of Korea Institute for IT Convergence, KAIST."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Edinburgh, UK EaStCHEM School of Chemistry, The University of Edinburgh. ;Edinburgh, UK IRR Chemistry Hub, Institute for Regeneration and Repair, The University of Edinburgh."}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Edinburgh, UK EaStCHEM School of Chemistry, The University of Edinburgh. ;Edinburgh, UK IRR Chemistry Hub, Institute for Regeneration and Repair, The University of Edinburgh."}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "London, UK Department of Clinical and Movement Neurosciences, UCL Queen Square Institute of Neurology. ;London, UK The Francis Crick Institute, King's Cross."}, {"AuthorId": 12, "Name": "Minee L. Choi", "Affiliation": "London, UK Department of Clinical and Movement Neurosciences, UCL Queen Square Institute of Neurology. ;London, UK The Francis Crick Institute, King's Cross. ;Daejeon, Republic of Korea Department of Brain & Cognitive Sciences, KAIST."}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "London, UK Department of Clinical and Movement Neurosciences, UCL Queen Square Institute of Neurology. ;London, UK The Francis Crick Institute, King's Cross."}], "References": []}, {"ArticleId": 109358698, "Title": "Research on Liquidity Measurement and Influencing Factors of China Carbon Emission Allocation Trading Market", "Abstract": "Considering multi-dimensional concepts of liquidity, we introduces two classical liquidity measures to quantify the liquidity of China carbon emission allowance(CCEA) trading markets, and analyze their trading activities. Then monthly unbalanced panel was constructed based on daily trading data to further study on the influencing factors of CCEA market. We find that: 1) No matter what method we take, the Hubei pilot is the most liquid in the context of overall running periods, and the national carbon market and Guangdong pilot are also well performed compared to the other markets. 2)The foundation of national generalized market could bring about noticeable changes on the liquidity of carbon market, and Guangdong market which is one of the 8 carbon market pilots become more developed and liquid, while no significant variation in the rest of 7 pilots. 3) The emission control enterprises of each pilot have no inborn incentive to trade proactively, therefore transactions are concentrated on the performance cycle. 4)Regression results show that the price at the end of each month, trading volumes aggregated in each month, trading frequency of each month are all positively correlated with the liquidity, and return volatility of each month, carbon market fragmentation are negatively related to the liquidity. Finally we put forward some suggestion and policy recommendation rested on the above qualitative and quantitative analysis.", "Keywords": "CEA;Carbon market;Liquidity;Measurement;Influencing factors", "DOI": "10.1016/j.procs.2023.08.137", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100190, China;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences, Beijing, 100190, China"}, {"AuthorId": 2, "Name": "Shuyang Peng", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100190, China;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100190, China;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences, Beijing, 100190, China"}], "References": []}, {"ArticleId": 109358724, "Title": "A novel robustness PROMETHEE method by learning interactive criteria and historical information for blockchain technology-enhanced supplier selection", "Abstract": "In a complex transaction and operation environment, supply chain usually faces the disruption risk, especially the supplier selection. Fortunately, the blockchain technology not only can provide traceability and decentration for supply chain management, but also can increase supply chain resilience. With the aid of blockchain technology, this paper deeply proposes a two-stage performance measure model to assess suppliers reinforcing supply chain resilience. Firstly, we screen out the assessment criteria of supply chain and blockchain by using complex network. Subsequently, for depicting the hesitation of multiple experts, we formulate the generation mechanism of linguistic distribution. Besides, in the blockchain technology-enabled supply chain, the criteria of supply chain and blockchain are interactive. Therefore, considering the interactive criterion, we combine the PROMETHEE method with the internal interactive index to measure supplier performance in the first stage. Meanwhile, the robustness PROMETHEE method is further improved by designing three types of aggregation models. In the second stage, we also concern the supply chain situation before adopting blockchain technology. In this case, this paper further designs a correction method of supplier performance measure based on supplier historical information in order to select the reliable supplier. Finally, we verify the effectiveness and rationality of our proposed method. In general, this study enriches the evaluation criteria of supplier performance measure by considering the influence of blockchain technology and provides the correspond decision support for blockchain technology-enabled supply chain management.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.121107", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management and Economics, University of Electronic Science and Technology of China, Chengdu 610054, China;Corresponding author"}, {"AuthorId": 2, "Name": "Yuanyuan Fu", "Affiliation": "School of Management and Economics, University of Electronic Science and Technology of China, Chengdu 610054, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Thapar Institute of Engineering and Technology, Deemed University, Patiala, Punjab 140074, India"}], "References": [{"Title": "A likelihood-based multi-criteria sustainable supplier selection approach with complex preference information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "536", "Issue": "", "Page": "135", "JournalTitle": "Information Sciences"}, {"Title": "A PROMETHEE II Approach Based on Probabilistic Hesitant Fuzzy Linguistic Information with Applications to Multi-Criteria Group Decision-Making (ICSSE 2020)", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "6", "Page": "1556", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "A novel PROMETHEE method based on GRA-DEMATEL for PLTSs and its application in selecting renewable energies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "142", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 109358803, "Title": "Review and research frontier analysis of low carbon economy – Based on bibliometric methods", "Abstract": "Research in the carbon field has experienced rapid growth in recent years and has attracted widespread academic attention. This paper conducts a comprehensive analysis of the carbon field using bibliometric methods. By collecting a large number of academic publications related to carbon and utilizing bibliometric indicators, We performed statistical and descriptive analyses on the development trends, research areas, research institutions, and authors in the field of carbon economy literature. Additionally, we employed co-citation analysis and keyword co-occurrence analysis to identify the current research focuses in the carbon field and explore the temporal changes in research hotspots. Ultimately, we analyzed the future development trends of the carbon field, providing valuable insights for scholars to conduct further research in this field. These research findings are of great significance in promoting the development and application of the carbon economy field.", "Keywords": "bibliometric ; bibliometrix ; co-occurrence of keywords ; co-citation analysis", "DOI": "10.1016/j.procs.2023.08.118", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Tong<PERSON>ng Yao", "Affiliation": "School of Economics and Management , University of Chinese Academy Sciences , Beijing 100190 , China;Research Center on Fictitious Economy and Data Science, Chinese Academy of Sciences , Beijing 100190 , China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences , Beijing 100190 , China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Economics and Management , University of Chinese Academy Sciences , Beijing 100190 , China;Research Center on Fictitious Economy and Data Science, Chinese Academy of Sciences , Beijing 100190 , China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences , Beijing 100190 , China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Management and Economics, Beijing Institute of Technology , Beijing 100081 , China"}], "References": []}, {"ArticleId": 109358819, "Title": "Construction of risk response scenarios for the emergency material support system", "Abstract": "Abstracts Representing disaster scenarios and evaluating the emergency material support system (EMSS) is crucial to enhance emergency material support capabilities. Current representation methods for EMSS mostly focused on the task response procedure during emergencies, rarely involved the response process analysis for disaster scenarios. This study utilizes an ontological method to construct a representation of risk response scenarios for the EMSS. It can be achieved by representing scenario feature elements, scenario structure elements, scenario constraint elements, and scenario attribute elements through the four dimensions. The scenario representation can generate different setting schemes for EMSS. An example scenario was presented based on the measures implemented by the Chinese government during the COVID-19 epidemic's closure of Wuhan. The findings of this research can provide valuable support for making risk-informed decisions regarding EMSS.", "Keywords": "Emergency material support system (EMSS) ; Scenario construction ; Ontological method", "DOI": "10.1016/j.procs.2023.08.077", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institutes of Science and Development, Chinese Academy of Sciences, Beijing, 100190, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institutes of Science and Development, Chinese Academy of Sciences, Beijing, 100190, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institutes of Science and Development, Chinese Academy of Sciences, Beijing, 100190, China"}], "References": []}, {"ArticleId": 109358868, "Title": "Finding a City Name in a Traffic Sign: Effects of Word Case and Visual Motion", "Abstract": "Objectives <p>To investigate the word recognition effects of the use of all-uppercase (e.g., VALENCIA) or titled-case (e.g., Valencia) for city names in traffic signs, controlling for word size, and comparing stationary and dynamic viewing situations.</p> Background <p>Prior studies provide mixed evidence regarding the effects of word case on the recognition of city names in traffic signs. Moreover, the evidence on the potential impact of visual motion on these effects is scarce.</p> Method <p>We carried out an experimental study using simulated traffic signs. The task was to indicate, for each sign, whether it contained a given city name or not (word search task, 50% positive trials). Visual motion of signs was manipulated as a between-participants factor: stationary (the sign was still) versus dynamic (the sign expanded as if the participant was approaching to it). Word case was manipulated as a within-participants factor: all-uppercase versus two titled-case conditions varying in font size: width-matched titled-case and point size-matched titled-case.</p> Results <p>In both the stationary and dynamic conditions, all-uppercase resulted in more incorrect responses and slower latencies than width-matched titled-case. When compared to point size-matched titled-case, all-uppercase produced slower correct responses in the stationary condition, whereas faster in the dynamic condition.</p> Conclusion <p>Other factors being equal, all-uppercase city names will be recognized worse than their titled-case versions in traffic signs, both in stationary and dynamic situations.</p> Application <p>Results in the current experimental study would be of interest in the design of traffic signs and other circumstances in which text is presented in motion.</p>", "Keywords": "display design principles;highway systems;information processing;language;visual search", "DOI": "10.1177/00187208231192756", "PubYear": 2024, "Volume": "66", "Issue": "7", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Psicología Básica / ERI Lectura, Universidad de Valencia, Valencia, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Psicología Básica / ERI Lectura, Universidad de Valencia, Valencia, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Departamento de Psicología Evolutiva y de la Educación / ERI Lectura, Universidad de Valencia, Valencia, Spain"}], "References": []}, {"ArticleId": 109358880, "Title": "Ideal dynamic threshold Multi-secret data sharing in smart environments for sustainable cities", "Abstract": "Nowadays, with continuous integration of big data, artificial intelligence and cloud computing technologies, there are increasing demands and specific requirements for data sharing in sustainable smart cities: (1) practical data sharing should be implemented in the non-interactive fashion without a trusted third party to be involved; (2) dynamic thresholds are preferred since the participants may join or leave at any time; (3) multi-secret sharing is desirable to increase the packing capacity. To fulfil these requirements, we propose a general construction of ideal threshold changeable multi-secret sharing scheme (TCMSS) with information-theoretic security, in which polynomials are employed to achieve dealer-free and non-interactive in the secret reconstruction phase. The TCMSS scheme can be built on any existing linear secret sharing scheme , and it is simpler and more efficient than the existing TCSS schemes in the literature. The main difference between TCMSS and Shamir’s SS is that univariate polynomial is used in <PERSON><PERSON><PERSON>’s SS to generate the shares for all shareholders; while in TCMSS, each shareholder can recover her own univariate polynomial using her share. This article demonstrates that with this novel modification, the classic polynomial-based SS can be transformed into an ideal TCMSS. Moreover, the TCMSS scheme is lightweight and it can resist both internal and external attacks. It does not require pairwise key distribution and its secret reconstruction phase is improved with enhanced properties. Therefore, the designed proposal is fairly suitable and attractive to be deployed in sustainable cities.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119488", "PubYear": 2023, "Volume": "647", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hubei Provincial Key Laboratory of Artificial Intelligence and Smart Learning, Central China Normal University, Wuhan 430079, China;Computer School, Central China Normal University, 430079 Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Wuhan University of Technology, Wuhan 430071, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Electrical Engineering, University of Missouri-Kansas City, Kansas City, 64110 MO, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computing, The Hong Kong Polytechnic University, Hong Kong 25809, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer School, Central China Normal University, 430079 Wuhan, China;Computer School, Central China Normal University, 430079 Wuhan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of artificial intelligence in education, Central China Normal University, 430079 Wuhan, China"}], "References": [{"Title": "Optimized Fuzzy Commitment Based Key Agreement Protocol for Wireless Body Area Network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "2", "Page": "839", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Secret sharing with secure secret reconstruction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "519", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "Threshold changeable secret sharing with secure secret reconstruction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "157", "Issue": "", "Page": "105928", "JournalTitle": "Information Processing Letters"}, {"Title": "Construction of Lightweight Authenticated Joint Arithmetic Computation for 5G IoT Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "1", "Page": "208", "JournalTitle": "The Computer Journal"}]}, {"ArticleId": 109358898, "Title": "FDA-MIMO Radar Target Recognition Based on SVM Classification with Multi-channel Feature Extraction", "Abstract": "FDA-MIMO radar has good anti-active interference performance and has received extensive attention due to its distance dependence. However, there is still a lack of target and interference recognition methods for FDA-MIMO radar in complex environments. Therefore, in this paper, we propose a multi-channel feature extraction classification method based on the support vector machine (SVM), which realizes the classification of four active interferences and targets. In short, we classify the fine signal characteristics of different interference signals by extracting effective features and selecting efficient classifiers. Simulation results show that when the interference to noise ratio (INR) is greater than -15 dB, the recognition accuracy is greater than 0.95. It also shows that the proposed method can distinguish target and interference well in a variety of complex environments.", "Keywords": "Frequency diversity array ; FDA-MIMO ; feature extraction ; multi-channel signal processing ; SVM ; target recognition", "DOI": "10.1016/j.procs.2023.07.008", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering , University of Electronic Science and Technology of China , Chengdu , 611731 , China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering , University of Electronic Science and Technology of China , Chengdu , 611731 , China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering , University of Electronic Science and Technology of China , Chengdu , 611731 , China"}], "References": []}, {"ArticleId": 109358933, "Title": "Emotions Memory: Effects of Farmers' Markets on Place Attachment", "Abstract": "As urbanization advances and policies are implemented, farmer's markets, as an important part of urban life, face dilemmas that urgently require academic exploration of countermeasures for market development models. This study takes Xuzhou Jiefangqiao Farmers' Market as an example, and under the guidance of place attachment theory, it integrates the questionnaire and observations, combined with SPSS software to analyze the case place site situation and sort out the factors influencing attachment perception. Extenics innovation method and Superiority evaluation strategy were used to investigate the results of the place attachment study. A quantitative conservation renewal strategy to improve the perception of place attachment of farmers' market users is proposed.", "Keywords": "Place Attachment ; Farmer's Market ; Design Practice ; Environmental Perception ; Extenics", "DOI": "10.1016/j.procs.2023.08.020", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Art and Design, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Art and Design, Beijing Institute of Technology, Beijing 100081, China"}], "References": []}, {"ArticleId": 109358943, "Title": "Ensemble technique to predict post-earthquake damage of buildings integrating tree-based models and tabular neural networks", "Abstract": "In this paper, we develop a novel ensemble model for seismic building damage prediction that leverages machine learning algorithms of two completely different mechanisms, tree-based models and tabular neural networks. The ensemble model can break the limitations of individual models and elevate the predictive power to a superior level. Ensemble techniques of stacking and boosting are utilized for model integration, and an optimization method is also proposed to eliminate the negative sub models based on the stacking coefficient map and the boosting process. On the database of the 2015 Nepal earthquake including 762,094 building samples, our developed ensemble model using boosting technique outperforms any other individual models and its accuracy also surpasses that of another research using the same database. Moreover, the technique of fuzzy prediction is proposed to address the inherent errors in the database caused by volunteers’ subjectivity, which preserves the uncertainty between two adjacent damage grades, and the accuracy in the testing set is increased to 76.37% by applying it. Finally, by using the S<PERSON><PERSON>y additive explanations method, our ensemble model is shown to have explicit physical significance and provides valuable insights for seismic mitigation efforts.", "Keywords": "Seismic building damage prediction ; Machine learning ; Ensemble ; Tree-based model ; Tabular neural network", "DOI": "10.1016/j.compstruc.2023.107114", "PubYear": 2023, "Volume": "287", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Highway, Chang'an University, Xi'an 710064, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "North Minzu University, Yinchuan 750000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Highway, Chang'an University, Xi'an 710064, China;Department of Civil Engineering, Aalto University, Espoo 02150, Finland;Corresponding authors"}, {"AuthorId": 4, "Name": "Jinxing Lai", "Affiliation": "School of Highway, Chang'an University, Xi'an 710064, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Highway, Chang'an University, Xi'an 710064, China"}], "References": [{"Title": "Threshold optimization for F measure of macro-averaged precision and recall", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107250", "JournalTitle": "Pattern Recognition"}, {"Title": "Failure mode classification and bearing capacity prediction for reinforced concrete columns based on ensemble machine learning algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "45", "Issue": "", "Page": "101126", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "An ensemble machine learning approach through effective feature extraction to classify fake news", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "47", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Deep learning techniques for predicting nonlinear multi-component seismic responses of structural buildings", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "252", "Issue": "", "Page": "106570", "JournalTitle": "Computers & Structures"}, {"Title": "Prediction of seismic damage spectra using computational intelligence methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "253", "Issue": "", "Page": "106584", "JournalTitle": "Computers & Structures"}, {"Title": "Confidence interval for micro-averaged F1 and macro-averaged F1 scores", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "5", "Page": "4961", "JournalTitle": "Applied Intelligence"}, {"Title": "Tabular data: Deep learning is not all you need", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "", "Page": "84", "JournalTitle": "Information Fusion"}, {"Title": "Dynamic seismic damage assessment of distributed infrastructure systems using graph neural networks and semi-supervised machine learning", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "168", "Issue": "", "Page": "103113", "JournalTitle": "Advances in Engineering Software"}, {"Title": "Ensemble deep learning: A review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "105151", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Machine learning-based prediction of seismic limit-state capacity of steel moment-resisting frames considering soil-structure interaction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "274", "Issue": "", "Page": "106886", "JournalTitle": "Computers & Structures"}, {"Title": "Attention-based LSTM (AttLSTM) neural network for Seismic Response Modeling of Bridges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "275", "Issue": "", "Page": "106915", "JournalTitle": "Computers & Structures"}, {"Title": "Data-driven prediction of critical flutter velocity of long-span suspension bridges using a probabilistic machine learning approach", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "280", "Issue": "", "Page": "107002", "JournalTitle": "Computers & Structures"}]}, {"ArticleId": 109358984, "Title": "Understanding and improving adversarial transferability of vision transformers and convolutional neural networks", "Abstract": "Convolutional neural networks (CNNs) and visual transformers (ViTs) are both known to be vulnerable to adversarial examples . Recent work has illustrated the existence of transferability between the two, but the experimental performance is generally mediocre. To enhance the transferability of adversarial examples between CNNs and ViTs, we propose a novel attack on the phenomenon that CNNs and ViTs differ significantly in their inductive bias, which not only attacks the same inductive bias between the two classes of models, but also suppresses the unique of ViTs. We evaluate the effectiveness of our approach through extensive experiments on state-of-the-art ViTs, CNNs, and robustly trained CNNs, and demonstrate significant improvements in transferability, both between ViTs and from ViTs to CNNs. The code for our project is available at https://github.com/chenxiaoyupetter/inductive-biase-attack .", "Keywords": "", "DOI": "10.1016/j.ins.2023.119474", "PubYear": 2023, "Volume": "648", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Posts and Telecommunications, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Nanjing University of Posts and Telecommunications, China"}, {"AuthorId": 3, "Name": "Huanhuan Lv", "Affiliation": "Nanjing University, China"}, {"AuthorId": 4, "Name": "Shang<PERSON> Liu", "Affiliation": "Nanjing University of Posts and Telecommunications, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing University of Posts and Telecommunications, China;Corresponding author"}], "References": [{"Title": "ERGCN: Data enhancement-based robust graph convolutional network against adversarial attacks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "617", "Issue": "", "Page": "234", "JournalTitle": "Information Sciences"}, {"Title": "Black-box attacks against log anomaly detection with adversarial examples", "Authors": "<PERSON><PERSON> Lu; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "619", "Issue": "", "Page": "249", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 109359007, "Title": "AIoT for sustainable manufacturing: Overview, challenges, and opportunities", "Abstract": "The integration of IoT and AI has gained significant attention as an emerging means to digitize manufacturing industries and drive sustainability in the context of Industry 4.0 . In recent times, there has been a merging of AI and IoT technologies to form an “Artificial Intelligence of Things” (AIoT) infrastructure. This integration aims to enhance various aspects such as human–machine interactions, operations in the field of IoT, big data analytics , and more. AIoT-based solutions offer numerous benefits to the manufacturing industry. These solutions improve efficiency, reduce waste, and enhance safety measures. By utilizing AIoT, manufacturers are able to achieve Industry 4.0 goals and increase productivity through automation, process optimization, and more informed decision-making. Additionally, the adoption of AI and IoT-based solutions in manufacturing companies has increased substantially. These solutions enable the early detection and prevention of defects in equipment, leading to the production of high-quality products. By minimizing waste, reducing costs, improving efficiency, and boosting productivity, manufacturers can further optimize their operations. Academic researchers and industry practitioners are currently prioritizing the development of highly advanced and streamlined AIoT-based solutions specifically designed for sustainable manufacturing. The primary objectives of this paper are (i) to provide a comprehensive overview of the domain-centric AIoT-based industry technology for sustainable manufacturing; (ii) to conduct a thorough survey of the existing research on AIoT-enabled manufacturing; (iii) to discuss the current challenges faced by AIoT in the context of sustainable manufacturing and explore the research prospects in this field. Therefore, this paper presents a systematic review of state-of-the-art AIoT-based techniques employed in industries for sustainable manufacturing and analyzes the key contributions and opportunities. Finally, the key challenges are explained for future research prospects.", "Keywords": "", "DOI": "10.1016/j.iot.2023.100901", "PubYear": 2023, "Volume": "24", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of Technology Sydney, Sydney, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Data Science Institute, University of Technology Sydney, Sydney, Australia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Technology Sydney, Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Technology Sydney, Sydney, Australia"}, {"AuthorId": 5, "Name": "G<PERSON><PERSON> Xu", "Affiliation": "Data Science Institute, University of Technology Sydney, Sydney, Australia"}], "References": [{"Title": "A Comprehensive Survey on Attacks, Security Issues and Blockchain Solutions for IoT and IIoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102481", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Implementing a blockchain-based local energy market: Insights on communication and scalability", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "158", "JournalTitle": "Computer Communications"}, {"Title": "The implementation to intelligent linkage service over AIoT hierarchical for material flow management", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "2207", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Deep transfer learning for conditional shift in regression", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107216", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An edge-cloud collaborative computing platform for building AIoT applications efficiently", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "Constructing a prior-dependent graph for data clustering and dimension reduction in the edge of AIoT", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "381", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "An effective resource scheduling model for edge cloud oriented AIoT", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "e6720", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Enabling Ambient Intelligence of Things (AIoT) healthcare system architectures", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "198", "Issue": "", "Page": "186", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 109359053, "Title": "Introductory Word", "Abstract": "", "Keywords": "", "DOI": "10.5300/2023-1-2/1", "PubYear": 2023, "Volume": "", "Issue": "1", "JournalId": 24806, "JournalTitle": "Zpravodaj Československého sdružení uživatelů TeXu", "ISSN": "1211-6661", "EISSN": "1213-8185", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109359072, "Title": "A Research on Exhibition Marketing Strategies under the Era of \"Internet Plus\"", "Abstract": "Today, the era of \"Internet Plus\" has brought great overturns to exhibition marketing, e.g. the change of exhibition form, the improvement of audience participation, digital market insight, the trend of online and offline integration and the influence of social media. This paper mainly discusses the connotation of exhibition marketing, explores the current status and problems of online exhibition marketing, and finally proposes the optimization path of exhibition marketing strategies under the background of \"Internet Plus\".", "Keywords": "", "DOI": "10.23977/infse.2023.040704", "PubYear": 2023, "Volume": "4", "Issue": "7", "JournalId": 93764, "JournalTitle": "Information Systems and Economics", "ISSN": "2523-6407", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 109359097, "Title": "Multirobot Allocation In A Flexible Manufacturing System, Using Reinforcement Learning For Decision-Making, Case of Study", "Abstract": "This paper presents a reinforcement learning approach for decision-making in assigning multiple robots in a flexible manufacturing system. The algorithm allows for learning the optimal action policy for assigning tasks to multiple robots, outperforming other methods. The case study involved three robots and four tasks, some requiring cooperation. The study demonstrates the effectiveness of the approach in comparison to other methods and the results indicate that the reinforcement learning approach increases production efficiency and reduces task completion time compared to traditional assignment methods.", "Keywords": "Multirobots ; Allocation ; FMS ; Reinforcement Learning", "DOI": "10.1016/j.procs.2023.07.006", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Pontificia Universidad Católica de Valparaíso, Zip Code 2430000, Chile"}], "References": [{"Title": "Scheduling of Resource Allocation Systems with Timed Petri Nets: A Survey", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "11", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A review of the applications of multi-agent reinforcement learning in smart factories", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "", "Page": "330", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 109359113, "Title": "The Robot@Home2 dataset: A new release with improved usability tools", "Abstract": "Released in 2017, Robot@Home is a dataset captured by a mobile robot during indoor navigation sessions in apartments. This paper presents Robot@Home2, an enhanced version of the Robot@Home dataset, aimed at improving usability and functionality for developing and testing mobile robotics and computer vision algorithms. Robot@Home2 consists of three main components. Firstly, a relational database that states the contextual information and data links, compatible with Standard Query Language. Secondly,a Python package for managing the database, including downloading, querying, and interfacing functions. Finally, learning resources in the form of Jupyter notebooks, runnable locally or on the Google Colab platform, enabling users to explore the dataset without local installations. These freely available tools are expected to enhance the ease of exploiting the Robot@Home dataset and accelerate research in computer vision and robotics.", "Keywords": "Dataset ; Mobile robotics ; Relational database ; Python ; Jupyter ; Google Colab", "DOI": "10.1016/j.softx.2023.101490", "PubYear": 2023, "Volume": "23", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Machine Perception and Intelligent Robotics group (MAPIR), Department of System Engineering and Automation, Málaga Institute for Mechatronics Engineering and Cyber-Physical systems (IMECH.UMA), University of Málaga, Blvr. <PERSON>, 35, 29071, Málaga, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Machine Perception and Intelligent Robotics group (MAPIR), Department of System Engineering and Automation, Málaga Institute for Mechatronics Engineering and Cyber-Physical systems (IMECH.UMA), University of Málaga, Blvr. <PERSON>, 35, 29071, Málaga, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Machine Perception and Intelligent Robotics group (MAPIR), Department of System Engineering and Automation, Málaga Institute for Mechatronics Engineering and Cyber-Physical systems (IMECH.UMA), University of Málaga, Blvr. <PERSON>, 35, 29071, Málaga, Spain"}], "References": [{"Title": "The Open Images Dataset V4", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "7", "Page": "1956", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Advanced mapping robot and high-resolution dataset", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "103559", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Robot Operating System 2: Design, architecture, and uses in the wild", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "66", "Page": "eabm6074", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 109359129, "Title": "Development of a computational tool in the Python language for the application of the AHP-Gaussian method", "Abstract": "A known barrier to the adoption of mathematical methods in decision-making in everyday problems is the lack of basic knowledge of the applicable methods, as well as of adequate tools for resolution, without the requirement of deepening in specific disciplines. In this context, this work proposes an interactive tool developed in the Python language that enables the resolution of decision-making problems using the AHP-Gaussian method, from an online environment of free access. A hypothetical problem is solved with the tool, demonstrating its operation and the results obtained. The solution to a problem from the literature was also reproduced, demonstrating its applicability to real and complex problems. The tool proved to be a viable alternative to enable the access of people with little computational and mathematical instruction to a promising method that has been achieving good results in applications of the most different areas of knowledge.", "Keywords": "Analytic Hierarchy Process ; decision making ; multicriteria", "DOI": "10.1016/j.procs.2023.07.048", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of São Paulo, São Paulo, SP, 05508-210, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Fávero", "Affiliation": "University of São Paulo, São Paulo, SP, 05508-210, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil;Military Institute of Engineering, Urca, RJ 22290-270, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Brazilian Navy, Rio de Janeiro, RJ 20091-000, Brazil"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "University of São Paulo, São Paulo, SP, 05508-210, Brazil"}], "References": [{"Title": "Sports decision-making model based on data mining and neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "9", "Page": "3911", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Design of a framework of military defense system for governance of geoinformation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "174", "JournalTitle": "Procedia Computer Science"}, {"Title": "Classification Performance Evaluation from Multilevel Logistic and Support Vector Machine Algorithms through Simulated Data in Python", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "214", "Issue": "", "Page": "511", "JournalTitle": "Procedia Computer Science"}, {"Title": "Assisting in the choice to fill a vacancy to compose the PROANTAR team: Applying VFT and the CRITIC-GRA-3N methodology", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "214", "Issue": "", "Page": "478", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 109359131, "Title": "A multicriteria DM framework in the eco-management of operations: a case of organic waste reuse", "Abstract": "This article presents a case study for decision-making regarding the reuse of organic waste. It introduces a framework for eco-management operations using a multi-criteria approach based on the perspective of organizational stakeholders. Applying the Analytical Hierarchical Process (AHP) it was possible to prioritize options in an environmentally sustainable and economically viable way. The framework integrates several criteria, such as environmental impact, economic viability, social acceptability and regulatory compliance, to provide a comprehensive and robust assessment of alternatives. A decision model is developed to identify the main concern attributes for determining the most suitable process for organic waste treatment. Empirical data has been collected from a central supplier located in the metropolitan region, in Chile. This supplier site contributes as an externality approximately 50% of the waste of the neighborhood where it is located and approximately 10% of the operational costs for the organization. The procedure is followed according to the methodological approach of the study including the experience and perspectives of the actors of the organization. The case study results indicate the efficacy and practicality of the multi-criteria framework in supporting eco-management decision-making processes.", "Keywords": "Multicriteria decision support ; AHP ; Organic waste reuse ; Eco-management operations", "DOI": "10.1016/j.procs.2023.08.044", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Santiago of Chile , Industrial Engineering Department , Av. Ecuador 3769 , Santiago , 9160000 , Chile"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Santiago of Chile , Industrial Engineering Department , Av. Ecuador 3769 , Santiago , 9160000 , Chile"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Santiago of Chile , Industrial Engineering Department , Av. Ecuador 3769 , Santiago , 9160000 , Chile"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Santiago of Chile , Industrial Engineering Department , Av. Ecuador 3769 , Santiago , 9160000 , Chile"}], "References": []}, {"ArticleId": 109359255, "Title": "Pengembangan Aplikasi Evaluasi Kegiatan Berbasis Android menggunakan Metode RAD (Rapid Application Development)", "Abstract": "<p>The development of technology is very fast, especially in the field of information systems. Companies, organizations, schools and governments definitely need the role of information technology in data security or management. The use of the internet is very much needed in government agencies because the internet is used as a means to provide fast and easy services to people who want to handle important documents. In this research, we developed an Android-based application to evaluate activities have been carried out. We named this application EL-AKIP which is an android-based application for Grobogan Regency designed using web 2.0 and restful api using the RAD (Rapid Application Development) method. The use of Rapid Application Development (RAD), which is a software development process model that is incremental, especially for short processing times. Application testing uses the black box testing method where the results show that the application passes the test and is feasible to use</p>", "Keywords": "Application;Android;Evaluation;RAD", "DOI": "10.46772/intech.v5i1.1082", "PubYear": 2023, "Volume": "5", "Issue": "1", "JournalId": 79305, "JournalTitle": "Jurnal Ilmiah Intech : Information Technology Journal of UMUS", "ISSN": "", "EISSN": "2685-4902", "Authors": [{"AuthorId": 1, "Name": "Egia Rosi Subhiyakto", "Affiliation": "Program Studi Teknik Informatika, Fakultas <PERSON>, Universitas Dian <PERSON>, Semarang, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas <PERSON>, Universitas Dian <PERSON>, Semarang, Indonesia"}, {"AuthorId": 3, "Name": "Charesta Vida Reswara", "Affiliation": "Program Studi Teknik Informatika, Fakultas <PERSON>, Universitas Dian <PERSON>, Semarang, Indonesia"}], "References": []}, {"ArticleId": 109359294, "Title": "Stock price prediction using a novel approach in Gaussian mixture model-hidden <PERSON><PERSON> model", "Abstract": "Purpose This study aims to provide the best estimate of a stock's next day's closing price for a given day with the help of the hidden Markov model–Gaussian mixture model (HMM-GMM). The results were compared with <PERSON> and <PERSON>’s (2005) study using HMM and artificial neural network (ANN). Design/methodology/approach The study adopted an initialization approach wherein the hidden states of the HMM are modelled as GMM using two different approaches. Training of the HMM-GMM model is carried out using two methods. The prediction was performed by taking the closest closing price (having a log-likelihood within the tolerance range) to that of the present one as the closing price for the next day. Mean absolute percentage error (MAPE) has been used to compare the proposed GMM-HMM model against the models of the research study (<PERSON> and <PERSON>, 2005). Findings Comparing this study with <PERSON> and <PERSON> (2005) reveals that the proposed model outperformed in 66 out of the 72 different test cases. The results affirm that the model can be used for more accurate time series prediction. Further, compared with the results of the ANN model from <PERSON>'s study, the proposed HMM model outperformed 24 of the 36 test cases. Originality/value The study introduced a novel initialization and two training/prediction approaches for the HMM-GMM model. It is to be noted that the study has introduced a GMM-HMM-based closing price estimator for stock price prediction. The proposed method of forecasting the stock prices using GMM-HMM is explainable and has a solid statistical foundation.", "Keywords": "Gaussian mixture model;Hidden Markov model;Probabilistic model;Markov model;Statistical model;Stock price prediction", "DOI": "10.1108/IJICC-03-2023-0050", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 26058, "JournalTitle": "International Journal of Intelligent Computing and Cybernetics", "ISSN": "1756-378X", "EISSN": "1756-3798", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology Tiruchirappalli , Tiruchirappalli, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Tiruchirappalli , Tiruchirappalli, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Institute of Technology Tiruchirappalli , Tiruchirappalli, India"}], "References": [{"Title": "A novel graph convolutional feature based convolutional neural network for stock trend prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "556", "Issue": "", "Page": "67", "JournalTitle": "Information Sciences"}, {"Title": "S_I_LSTM: stock price prediction based on multiple data sources and sentiment analysis", "Authors": "<PERSON><PERSON><PERSON> Wu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "44", "JournalTitle": "Connection Science"}, {"Title": "Forecasting the Dynamic Correlation of Stock Indices Based on Deep Learning Method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "61", "Issue": "1", "Page": "35", "JournalTitle": "Computational Economics"}, {"Title": "Research on stock trend prediction method based on optimized random forest", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "1", "Page": "274", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": 109359338, "Title": "Simulating all archetypes of SQL injection vulnerability exploitation using reinforcement learning agents", "Abstract": "Vulnerabilities such as SQL injection represent a serious challenge to security. While tools with a pre-defined logic are commonly used in the field of penetration testing, the continually evolving nature of the security challenge calls for models able to learn autonomously from experience. In this paper we build on previous results on the development of reinforcement learning models devised to exploit specific forms of SQL injection, and we design agents that are able to tackle a varied range of SQL injection vulnerabilities, virtually comprising all the archetypes normally considered by experts. We show that our agents, trained on a synthetic environment, perform a transfer of learning among the different SQL injections challenges; in particular, they learn to use their queries to efficiently gain knowledge about multiple vulnerabilities at once. We also introduce a novel and more versatile way to interpret server messages that reduces reliance on expert inputs. Our simulations show the feasibility of our approach which easily deals with a number of homogeneous challenges, as well as some of its limitations when presented with problems having higher degrees of uncertainty.", "Keywords": "SQL injection; Capture the flag; Vulnerability detection; Autonomous agents; Reinforcement learning; Q-learning", "DOI": "10.1007/s10207-023-00738-3", "PubYear": 2024, "Volume": "23", "Issue": "1", "JournalId": 25892, "JournalTitle": "International Journal of Information Security", "ISSN": "1615-5262", "EISSN": "1615-5270", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, University of Oslo, Oslo, Norway; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Security and Communication Technology, Norwegian University of Science and Technology, Trondheim, Norway"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Warwick, Coventry, UK"}], "References": [{"Title": "Reinforcement Learning for Efficient Network Penetration Testing", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "6", "JournalTitle": "Information"}, {"Title": "Machine Learning Cyberattack and Defense Strategies", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101738", "JournalTitle": "Computers & Security"}, {"Title": "Simulating SQL injection vulnerability exploitation using Q-learning reinforcement learning agents", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "102903", "JournalTitle": "Journal of Information Security and Applications"}]}, {"ArticleId": 109359437, "Title": "The characterization of thermal perception in recreational surfers wearing wetsuits", "Abstract": "<p>The purpose of this study was to characterize the perception of heat loss, comfort, and wetness in recreational surfers wearing wetsuits, to compare these data with changes in skin temperature reported in prior studies, and to examine the impact of wetsuit thickness, zipper location, and accessory use on thermal sensation and comfort. Following their surf session, nine-hundred and three male (n = 735) and female (n = 168) recreational surfers responded to a series of questions regarding thermal comfort/sensation, wetsuit characteristics, and surfing history. Average whole body thermal sensation rating was 0.8 ± 3.6 on a scale of -10 to +10 and average whole body thermal comfort rating was 1.5 ± 1.2, midway between \"just comfortable\" and \"comfortable.\" Overall, surfers felt coldest in their feet, hands, and head. Under their wetsuits, surfers felt the coldest, wettest, and least comfortable in their chest, lower legs, lower arms, and upper back. Wetsuit accessory use had the greatest impact on regions identified as coldest, least comfortable, and wettest. These data suggest that wetsuit design should focus on optimizing water access points and improving accessories for the feet, hands, and head.</p><p>Copyright © 2023 The Authors. Published by Elsevier Ltd.. All rights reserved.</p>", "Keywords": "Surfing;Thermal comfort;Thermal perception;Thermal sensation;Wetsuit", "DOI": "10.1016/j.apergo.2023.104108", "PubYear": 2023, "Volume": "113", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept of Kinesiology, California State University, San Marcos, CA, 92096, USA."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dept of Kinesiology, California State University, San Marcos, CA, 92096, USA. Electronic address:  ."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "ThermosenseLab, Skin Sensing Research Group, School of Health Sciences, University of Southampton, Southampton, UK."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Dept of Kinesiology, California State University, San Marcos, CA, 92096, USA."}], "References": []}, {"ArticleId": *********, "Title": "Isogeometric bending and free vibration analyses of carbon nanotube-reinforced magneto-electric-elastic microplates using a four variable refined plate theory", "Abstract": "The classical continuum mechanics theory is inadequate for modeling the mechanical responses of the microstructures due to its failure to account for size effects. Instead, modified strain gradient theory (MSGT) is considered as one of the most accurate theories due to its ability to adjust three length scale parameters (LSPs). Isogeometric approach (IGA) is a computational technique that is capable of accurately solving complex problems. For those reasons, the size-dependent analysis of carbon nanotube-reinforced magneto-electric-elastic microplates based on the MSGT are firstly proposed. The present approach uses the refined plate theory (RPT) with four variables, the MSGT and IGA. The magnetic and electric potentials are assumed to be a combination of a half-cosine and linear variation to satisfy <PERSON>’s equations. Different types of functionally graded carbon nanotubes (CNTs) including UD, FG-X, FG-O and FG-V are reinforced in the magneto-electric-elastic matrix of the microplates. Governing equations are derived using the extended virtual work principle and then solved by IGA to determine the deflection and natural frequency of the microplates. The influence of the LSPs, CNT distributions, CNT’s volume fraction, matrix’s volume fraction, magnetic potential, electric voltage and geometry on the deflection and natural frequency of the microplates are studied and discussed.", "Keywords": "Carbon nanotube reinforced magneto-electric-elastic (CNT-MEE) microplates ; Isogeometric analysis ; Modified strain gradient theory ; A refined plate theory ; Small-scaled effects", "DOI": "10.1016/j.compstruc.2023.107121", "PubYear": 2023, "Volume": "287", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Civil Engineering, Ho Chi Minh City University of Technology and Education (HCMUTE), Ho Chi Minh City, Viet Nam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Computational Mechanics, Institute for Computational Science, Ton Duc Thang University, Ho Chi Minh City, Viet Nam;Faculty of Civil Engineering, Ton Duc Thang University, Ho Chi Minh City, Viet Nam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Civil Engineering, HUTECH University, Ho Chi Minh City, Viet Nam;Corresponding author: Faculty of Civil Engineering, HUTECH University, Ho Chi Minh City, Viet Nam (P. <PERSON>-Van)"}], "References": [{"Title": "A modified strain gradient meshfree approach for functionally graded microplates", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S5", "Page": "4545", "JournalTitle": "Engineering with Computers"}, {"Title": "Adaptive enriched geometry independent field approximation for 2D time-harmonic acoustics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "263", "Issue": "", "Page": "106728", "JournalTitle": "Computers & Structures"}]}, {"ArticleId": 109359456, "Title": "Collaborative Work Alternatives with ChatGPT Based on Evaluation Criteria for its Use in Higher Education: Application of the PROMETHEE-SAPEVO-M1 Method", "Abstract": "The objective of this article is to adopt the integration of two methods of Multicriteria Decision Support, based on the axiomatic models PROMETHEE and SAPEVO-M1, aggregating data of a qualitative nature through ordinal entries to analyze collaborative work alternatives with ChatGPT from evaluation criteria for its use in higher education. It is highlighted that the alternative with the best performance is ‘Support for Autonomous Learning,’ presenting the highest positive flow and the lowest negative flow, exposing a natural preference over the set. In this study, ‘Emotional Support’ was the worst alternative. It occurs because the tool is still under discussion when addressing issues such as the lack of human interaction, reduced critical thinking, and less empathy.", "Keywords": "Artificial intelligence ; ChatGPT ; collaborative work ; Higher Education ; PROMETHEE-SAPEVO-M1 method", "DOI": "10.1016/j.procs.2023.07.025", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Federal University of Sao Paulo, Osasco, SP 06120-042, Brazil;Fluminense Federal University, Niterói, RJ 24210-240, Brazil;University of São Paulo (USP), São Paulo, SP, 05508-210, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of São Paulo (USP), São Paulo, SP, 05508-210, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-240, Brazil;Military Institute of Engineering, Urca, RJ 22290-270, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of São Paulo (USP), São Paulo, SP, 05508-210, Brazil"}], "References": []}, {"ArticleId": 109359461, "Title": "The Digital Industrial Cluster (DIC) in a post-pandemic era: Exploring its theoretical deployment and potential benefits", "Abstract": "This conceptual research aims to deepen the theoretical discussion about the Digital Industrial Cluster (DIC) in a post-pandemic world. The DIC represents a novel model of digital agglomeration, building upon the evolution of Industrial Clusters. The research takes a discursive and theoretical approach, exploring the deployment of the DIC through the analysis of scientific publications on digital agglomeration. It also considers previous research on the pandemic's impact on globalization and digital agglomeration, as well as literature on innovation and Industry 4.0 for digital policy. The findings propose a seven-layers model of digital transformation necessary for deploying a DIC. The research presents expected externalities and key challenges for DIC deployment, with innovation and Industry 4.0 remaining central to digital agglomeration. This study aims to assist policymakers and cluster organizations in visualizing the organization and development of a real-world DIC. However, empirical research is necessary to evaluate the potential benefits and identify regions suitable for participation in such a project.", "Keywords": "digital agglomeration ; digital economy ; digitalization ; pandemic ; Industry 4.0", "DOI": "10.1016/j.procs.2023.08.098", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of the Basque Country/Euskal Herriko Unibersitatea (UPV/EHU), Bilbao (Bizkaia), 48015, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Tecnológica del Suroeste de Guanajuato, Valle de Santiago (Guanajuato), 38400, Mexico"}], "References": [{"Title": "Roadmap for digital technology to foster India’s MSME ecosystem—opportunities and challenges", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "4", "Page": "233", "JournalTitle": "CSI Transactions on ICT"}, {"Title": "The adoption of ICT as an enabler of frugal innovation to achieve customer satisfaction. The mediating effect of frugal innovation", "Authors": "<PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "198", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 109359483, "Title": "A Faster DBSCAN Algorithm Based on Self-Adaptive Determination of Parameters", "Abstract": "The DBSCAN algorithm is a well-known cluster method that is density-based and has the advantage of finding clusters of different shapes, but it also has certain shortcomings, one of which is that it cannot determine the two important parameters Eps ( neighborhood of a point) and Mints (minimum number of points) by itself, and the other is that it takes a long time to traverse all points when dataset is large. In this paper, we propose an improved method which is named as K-DBSCAN to improve the running efficiency based on self-adaptive determination of parameters and this method changes the way of traversing and only deals with core points. Experiments show that it outperforms DBSCAN algorithms in terms of running time efficiency.", "Keywords": "Data-mining ; Cluster ; K-DBSCAN ; Self-adaptive", "DOI": "10.1016/j.procs.2023.07.017", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Central University of Finance and Economics, School of management science and Engineering, Beijing 102206, P.R. China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Central University of Finance and Economics, School of management science and Engineering, Beijing 102206, P.R. China"}, {"AuthorId": 3, "Name": "Aihua Li", "Affiliation": "Central University of Finance and Economics, School of management science and Engineering, Beijing 102206, P.R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Chi", "Affiliation": "Central University of Finance and Economics, School of management science and Engineering, Beijing 102206, P.R. China"}, {"AuthorId": 5, "Name": "Lihua Chen", "Affiliation": "Digital China Group Co., Ltd, Beijing 100190, P.R. China"}], "References": [{"Title": "A fast DBSCAN algorithm for big data based on efficient density calculation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117501", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 109359488, "Title": "Robust Bidirectional Long Short-Term Memory-Based Class Imbalance Handling in Dyslexia Prediction at its Early Stage", "Abstract": "<p>Dyslexia is a neurological condition that presents difficulties and obstacles in learning, particularly in reading. Early diagnosis of dyslexia is crucial for children, as it allows the implementation of appropriate resources and specialized software to enhance their skills. However, the evaluation process can be expensive, time-consuming, and emotionally challenging. In recent years, researchers have turned to machine learning and deep learning techniques to detect dyslexia using datasets obtained from educational and healthcare institutions. Despite the existence of several deep learning models for dyslexia prediction, the problem of handling class imbalance significantly impacts the accuracy of detection. Therefore, this study proposes a robust deep learning model based on a variant of long short-term memory (LSTM) to address this issue. The advantage of Bidirectional LSTM, which has the ability to traverse both forward and backward, improves the pattern of understanding very effectively. Still, the problem of assigning values to the hyper-parameters in BLSTM is the toughest challenge which has to be assigned in a random manner. To overcome this difficulty, the proposed model induced a behavioral model known as Red Fox Optimization algorithm (RFO). Based on the inspiration of red fox searching behavior, this proposed work utilized the local and the global search in assigning and fine-tuning the values of hyper-parameters to handle the class imbalance in dyslexia dataset. The performance evaluation is conducted using two different dyslexia datasets (i.e., dyslexia 12_14 & real-time dataset). The simulation results explore that the proposed robust Bidirectional Long Short-Term Memory accomplishes the highest detection rate with reduced error rate compared to other deep learning models.</p>", "Keywords": "Dyslexia; Neurological disorder; Deep learning; Machine learning; Bidirectional long short-term memory; Red Fox optimization; Class imbalance; Hyperparameter", "DOI": "10.1007/s42979-023-02049-9", "PubYear": 2023, "Volume": "4", "Issue": "5", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, CHRIST (Deemed to be University), Bengaluru, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, CHRIST (Deemed to be University), Bengaluru, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science, CHRIST (Deemed to be University), Bengaluru, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "SRM Trichy Arts and Science College, Tiruchirappalli, India"}], "References": [{"Title": "Red fox optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "114107", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 109359524, "Title": "Toward multi-target self-organizing pursuit in a partially observable Markov game", "Abstract": "The multiple-target self-organizing pursuit (SOP) problem has wide applications and has been considered a challenging self-organization game for distributed systems, in which intelligent agents cooperatively pursue multiple dynamic targets with partial observations. This work proposes a framework for decentralized multi-agent systems to improve the implicit coordination capabilities in search and pursuit. We model a self-organizing system as a partially observable Markov game (POMG) featured by large-scale, decentralization, partial observation, and noncommunication. The proposed distributed algorithm–fuzzy self-organizing cooperative coevolution (FSC2) is then leveraged to resolve the three challenges in multi-target SOP: distributed self-organizing search (SOS), distributed task allocation, and distributed single-target pursuit. FSC2 includes a coordinated multi-agent deep reinforcement learning (MARL) method that enables homogeneous agents to learn natural SOS patterns. Additionally, we propose a fuzzy-based distributed task allocation method, which locally decomposes multi-target SOP into several single-target pursuit problems. The cooperative coevolution principle is employed to coordinate distributed pursuers for each single-target pursuit problem. Therefore, the uncertainties of inherent partial observation and distributed decision-making in the POMG can be alleviated. The experimental results demonstrate that by decomposing the SOP task, FSC2 achieves superior performance compared with other implicit coordination policies fully trained by general MARL algorithms. The scalability of FSC2 is proved that up to 2048 FSC2 agents perform efficient multi-target SOP with almost 100% capture rates. Empirical analyses and ablation studies verify the interpretability , rationality, and effectiveness of component algorithms in FSC2.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119475", "PubYear": 2023, "Volume": "648", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Brain-inspired Intelligent Computation, Department of Computer Science and Engineering, Southern University of Science and Technology, China;Centre for Artificial Intelligence, CIBCI Lab, Faculty of Engineering and Information Technology, University of Technology Sydney, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Artificial Intelligence, CIBCI Lab, Faculty of Engineering and Information Technology, University of Technology Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer and Information Science, Southwest University, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, ShanghaiTech University, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Brain-inspired Intelligent Computation, Department of Computer Science and Engineering, Southern University of Science and Technology, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Artificial Intelligence, CIBCI Lab, Faculty of Engineering and Information Technology, University of Technology Sydney, Australia;Corresponding authors"}], "References": [{"Title": "Cooperative coevolution of real predator robots and virtual robots in the pursuit domain", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106098", "JournalTitle": "Applied Soft Computing"}, {"Title": "Cooperative control for multi-player pursuit-evasion games with reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "412", "Issue": "", "Page": "101", "JournalTitle": "Neurocomputing"}, {"Title": "Implicit coordination for 3D underwater collective behaviors in a fish-inspired robot swarm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "50", "Page": "eabd8668", "JournalTitle": "Science Robotics"}, {"Title": "Multi-agent machine learning in self-organizing systems", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "581", "Issue": "", "Page": "194", "JournalTitle": "Information Sciences"}, {"Title": "Distributed dynamic event-triggered communication and control for multi-agent consensus: A hybrid system approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "618", "Issue": "", "Page": "191", "JournalTitle": "Information Sciences"}, {"Title": "Graph attention mechanism based reinforcement learning for multi-agent flocking control in communication-restricted environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "620", "Issue": "", "Page": "142", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 109359550, "Title": "Cross cultural Comparative Study on Emotional Analysis of Social Media", "Abstract": "With the popularization of social media and the acceleration of globalization, cross-cultural social media research has become an increasingly important research field. This article takes cultural differences as the starting point to explore the use and impact of social media in cross-cultural environments. The study used a combination of quantitative and qualitative methods to collect social media data from users from different cultural backgrounds, and conducted data analysis and comparison. Research has found that users from different cultural backgrounds have significant differences in social media usage behavior, content expression, and interaction methods. At the same time, the use of cross-cultural social media will also have different impacts on users’ cultural identity, social relations and mental health. The research results have certain reference value for promoting cross-cultural communication, enhancing cultural understanding, and improving the effectiveness of social media usage.", "Keywords": "Text Mining ; Sentiment analysis ; Topic clustering ; Cross cultural exchange", "DOI": "10.1016/j.procs.2023.08.032", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Management Science and Engineering , Hebei University of Economics and Business , Shijiazhuang , Hebei 050061 , China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Management Science and Engineering , Hebei University of Economics and Business , Shijiazhuang , Hebei 050061 , China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Management Science and Engineering , Hebei University of Economics and Business , Shijiazhuang , Hebei 050061 , China"}], "References": []}, {"ArticleId": 109359574, "Title": "Multi-objective Reinforcement Learning – Concept, Approaches and Applications", "Abstract": "Real-world decision-making tasks are generally complicated and require trade-offs between multiple, even conflicting, objectives. As the advent and great development of advanced information technology, it has evolved into using reinforcement learning (RL) algorithms to tackle the multi-objective decision making (MODM) problems. In this paper, we will first identify the basic concepts and factors when modelling the MODM tasks with reinforcement learning, and then review the traditional RL, such as Sarsa, Q-Learning, Policy Gradients, Actor-Critic, Monte-Carlo learning, and modern deep RL algorithms applied in this process. Furthermore, the specific practical scenarios described in MODM problems will be summarized through analyzing some typical articles. Finally, the future trends of multi-objective reinforcement learning will be discussed.", "Keywords": "Multi-objective decision making ; Reinforcement Learning ; Survey", "DOI": "10.1016/j.procs.2023.08.018", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing, 100190, China;University of Birmingham, Edgbaston, Birmingham, B15 2TT, United Kingdom;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing, 100190, China;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing, 100190, China;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences, Beijing 100190, China"}], "References": [{"Title": "Toll-based reinforcement learning for efficient equilibria in route choice", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "", "Page": "e8", "JournalTitle": "The Knowledge Engineering Review"}, {"Title": "A multi-objective deep reinforcement learning framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "103915", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 109359581, "Title": "Code and Data Repository for Software for Data-Based Stochastic Programming Using Bootstrap Estimation", "Abstract": "Data-based, two-stage stochastic programming using bootstrap and bagging for confidence intervals.", "Keywords": "", "DOI": "10.1287/ijoc.2022.0253.cd", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109359602, "Title": "Power battery closed-loop supply chain decision of green investment under different subsidy objects", "Abstract": "The ideal closed-loop supply chain (CLSC) for power battery recycling is \"production - transaction - recycling - echelon utilization - remanufacturing\". For the purpose of boosting the utilization efficiency of power battery, this paper constructs two models on the echelon utilization of power battery to influence of the government subsidies to the power battery manufacturer or the recycler on CLSC. We focus on exploring the influence of the retailer's green investment in logistics and the sharing costs of manufacturer on the equilibrium results and profits of CLSC. The results show that when the power battery manufacturer's sharing coefficient is maintained in a low range, the investment level of green logistics can be promoted and the profit maximization of the CLSC can be achieved, which has a positive effect on the establishment of a good corporate image and the increase of demand. Furthermore, the power battery manufacturer ought to choose reasonable sharing coefficient to the positive effect of cost sharing on CLSC. In addition to increasing the number of used batteries to be recycled, government subsidies can also enhance the recycled efficiency of power batteries.", "Keywords": "Closed-loop supply chain ; Government subsidies ; Green investment ; Cost sharing", "DOI": "10.1016/j.procs.2023.08.102", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Hebei University of Economics and Business, Shijiazhuang 050061, P. R. <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Hebei University of Economics and Business, Shijiazhuang 050061, P. R. <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Management Science and Engineering, Hebei University of Economics and Business, Shijiazhuang 050061, P. R. <PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Hebei University of Economics and Business, Shijiazhuang 050061, P. R. <PERSON>"}], "References": []}, {"ArticleId": 109359604, "Title": "The Utility Impact of Differential Privacy on Credit Card Data in Machine Learning Algorithms", "Abstract": "With the development of networks and financial technologies, credit card data is increasingly being used in various fields of data analysis such as user behavior, financial transactions, and market analysis. These fields often use multiple machine learning algorithms for data mining on credit card dataset. It is worth noting that credit card data contains more diverse and comprehensive information compared to traditional data, and the dataset may contain multiple data types. At the same time, credit card data may also expose users’ privacy information. Differential privacy algorithms can add random noise to the data set, protecting sensitive information while ensuring certain data utility. However, there has been little research on the use of differential privacy algorithms on credit card data in multiple machine learning algorithms, and there has been insufficient exploration of the utility impact of differential privacy on complex credit card data. These research gaps exist in both the financial technology and privacy protection industries. Therefore, this paper applies differential privacy to credit card data in multiple classic machine learning algorithms, discusses the utility impact of various differential privacy algorithms on credit card data, and compares the performance of credit card data sets protected by differential privacy in different algorithms.", "Keywords": "credit card default ; differential privacy ; machine learning algorithm", "DOI": "10.1016/j.procs.2023.08.036", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Xiaopeng LUO", "Affiliation": "Hong Kong Baptist University , Kowloon Tong , Hong Kong , China;IRADS, BNU-HKBU United International College , Zhuhai , China"}, {"AuthorId": 2, "Name": "Siyuan WANG", "Affiliation": "Faculty of Science and Technology, BNU-HKBU United International College , Zhuhai , China"}, {"AuthorId": 3, "Name": "Haolong CHEN", "Affiliation": "Faculty of Science and Technology, BNU-HKBU United International College , Zhuhai , China"}, {"AuthorId": 4, "Name": "Zongwei LUO", "Affiliation": "BNU-UIC Institute of AI and Future Networks, Beijing Normal University, Zhuhai, China, 519000;Artificial Intelligence and Data Science Research Hub, BNU-HKBU United International College , Zhuhai , China"}], "References": []}, {"ArticleId": 109359657, "Title": "Research on conductive knowledge mining for dust removal strategy in fully mechanized mining faces", "Abstract": "In coal mining, dust production in fully mechanized mining faces poses a significant health risk to underground workers. Therefore, it is crucial to research effective dust removal strategies for these mining faces. This paper applies conductive knowledge mining to study the environmental data of fully mechanized mining faces. By implementing active extension transformation in spray dust removal, the conductive effect, support degree and confidence degree of each information element before and after the transformation are calculated, and conductive effect intervals are obtained. Relevant conductive knowledge is then extracted to identify issues in the current dust removal strategy. By combining the obtained conductive knowledge with domain knowledge, an effective dust removal strategy is proposed for fully mechanized mining faces.", "Keywords": "extension data mining ; conductive knowledge mining ; fully mechanized mining faces ; dust removal strategy", "DOI": "10.1016/j.procs.2023.08.060", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electromechanical Engineering, Guangdong University of Technology, Guangzhou 510006, China;Institute of Extenics and Innovation Methods, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electromechanical Engineering, Guangdong University of Technology, Guangzhou 510006, China;Institute of Extenics and Innovation Methods, Guangdong University of Technology, Guangzhou 510006, China"}], "References": []}, {"ArticleId": 109359680, "Title": "Efficient and privacy-preserving tree-based inference via additive homomorphic encryption", "Abstract": "Due to the excellent efficiency and high interpretability , coupled with the accuracy comparable to deep learning on tabular data, various tree-based models have been widely concerned and succeeded in many fields like medical diagnosis, credit evaluation, and fault detection . However, the model owner may face dilemmas such as compromised privacy and insufficient machine performance when providing tree-based inference services. Therefore, this paper proposes an efficient and privacy-preserving tree-based inference scheme, through which users can enjoy secure, high-accuracy, and transparent outsourcing inference services from one cloud. Specifically, by adopting additive homomorphic encryption as the underlying privacy-preserving technique, we design a suite of perturbation and recovery methods, processing cipher-based inference while masking the inference path. Meanwhile, without additional interactions, users can directly obtain high-accuracy results comparable to non-privacy inference, so the used privacy-preserving methods seem transparent to users. Moreover, through optimizing algorithm design and precalculating partial ciphertexts , the efficiency of our scheme is improved significantly. Formal security analysis demonstrates that our scheme achieves selective security under the honest-but-curious model. Extensive experimental evaluation shows the lossless accuracy, low complexity, and high efficiency of our scheme, which is practical to real-world scenes, especially for large-scale models.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119480", "PubYear": 2023, "Volume": "650", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi'an, 710071, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi'an, 710071, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi'an, 710071, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Lu", "Affiliation": "Faculty of Computer Science, University of New Brunswick, Fredericton, NB E3B 5A3, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi'an, 710071, China"}], "References": []}, {"ArticleId": 109359682, "Title": "Enriching Word Information Representation for Chinese Cybersecurity Named Entity Recognition", "Abstract": "<p>Named entity recognition (NER) is a word-level sequence tagging task. The key of Chinese cybersecurity NER is to obtain meaningful word representations and to delicately model the inter-word relations. However, Chinese is a language of compound words and lacks morphological inflections. Moreover, the role and meaning of a word depends on the context in a complicated way. In this paper, we present an NER model named Star-HGCN, short for Star-Transformer with Hybrid embeddings and Graph Convolutional Network. To make full use of the intra-word information, we set a hybrid embedding layer at the very beginning, which enriches word representations with character-level information and part-of-speech features. More importantly, we further enhance the hybrid embeddings by modeling inter-word implicit local and long-range semantic associations using the efficient Star-Transformer architecture, and modeling the explicit syntactic dependencies between words in the dependency tree using the graph convolutional network. Experiments on the Chinese cybersecurity dataset show that our model is superior to other neural network methods for NER, and achieves a significant relative improvement of 36.59% for the class of software entities. Experiments on other public datasets also validate the effectiveness of the model on other general and specific domains.</p>", "Keywords": "Named entity recognition; Cybersecurity; Hybrid embedding; Inter-word dependency; Star-transformer; Graph convolutional network", "DOI": "10.1007/s11063-023-11280-7", "PubYear": 2023, "Volume": "55", "Issue": "6", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Data Science, Taiyuan University of Technology, Taiyuan, China; Institute of Public-Safety and Big Data, Taiyuan University of Technology, Taiyuan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Data Science, Taiyuan University of Technology, Taiyuan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Data Science, Taiyuan University of Technology, Taiyuan, China; Institute of Public-Safety and Big Data, Taiyuan University of Technology, Taiyuan, China; Health Big Data Research Center, Changzhi Medical College, Changzhi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Public-Safety and Big Data, Taiyuan University of Technology, Taiyuan, China; Center of Information Management and Development, Taiyuan University of Technology, Taiyuan, China"}], "References": [{"Title": "Named Entity Recognition by Using XLNet-BiLSTM-CRF", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "5", "Page": "3339", "JournalTitle": "Neural Processing Letters"}, {"Title": "TFM: A Triple Fusion Module for Integrating Lexicon Information in Chinese Named Entity Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "3425", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 109359769, "Title": "I-Cubid: a nonlinear cubic graph-based approach to visualize and in-depth browse Flickr image results", "Abstract": "<p>The existing image search engines allow web users to explore images from the grids. The traditional interaction is linear and lookup-based. Notably, scanning web search results is horizontal-vertical and cannot support in-depth browsing. This research emphasizes the significance of a multidimensional exploration scheme over traditional grid layouts in visually exploring web image search results. This research aims to antecedent the implications of visualization and related in-depth browsing via a multidimensional cubic graph representation over a search engine result page (SERP). Furthermore, this research uncovers usability issues in the traditional grid and 3-dimensional web image search space. We provide multidimensional cubic visualization and nonlinear in-depth browsing of web image search results. The proposed approach employs textual annotations and descriptions to represent results in cubic graphs that further support in-depth browsing via a search user interface (SUI) design. It allows nonlinear navigation in web image search results and enables exploration, browsing, visualization, previewing/viewing, and accessing images in a nonlinear, interactive, and usable way. The usability tests and detailed statistical significance analysis confirm the efficacy of cubic presentation over grid layouts. The investigation reveals improvement in overall user satisfaction, screen design, information & terminology, and system capability in exploring web image search results.</p>", "Keywords": "Nonlinear exploration; In-depth browsing; Usability; Visualization; Search user interfaces; Web search", "DOI": "10.7717/peerj-cs.1476", "PubYear": 2023, "Volume": "9", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Quaid-i-Azam University, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Quaid-i-Azam University, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Quaid-i-Azam University, Islamabad, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Quaid-i-Azam University, Islamabad, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Sciences, Prince Sultan University, Riyadh, Saudi Arabia"}], "References": [{"Title": "A review on visual content-based and users’ tags-based image annotation: methods and techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "29-30", "Page": "21679", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "MIRRE approach: nonlinear and multimodal exploration of MIR aggregated search results", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "13", "Page": "20217", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An architecture for non-linear discovery of aggregated multimedia document web search results", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": *********, "Title": "Smartphone spectatorship in unenclosed environments: The physiological impacts of visual and sonic distraction during movie watching on mobile devices", "Abstract": "Smartphones’ popularity is largely based on their pervasiveness, portability, and the wide range of functions they encompass: they can play high-definition moving-image content without spatial and temporal constraints. However, the lack of spatial and temporal frameworks can account for distractions. Distractions (generally sonic or visual information) can originate from the surrounding environment or from the device itself and they may or may not hold semantic links to the content being watched. In this paper, we argue that the distraction effect in movie watching is based on a distractor’s modality, neutrality, and ecological relevance to the movie. To test the effects of these properties, we recorded viewers’ gaze and electrodermal activity while they watched a narrative film sequence on smartphone and projector screens in the presence of sonic and visual distractors. We found that screen type can affect attention and arousal: in comparison to projector viewers, smartphone viewers experienced lower arousal and were more likely to shift their attention from the movie even when a distractor closely related to the movie was played. It was also observed that distractors that require urgent attention and are unrelated to the movie redirect the viewer’s attention and increases electrodermal activity values. In contrast, distractors with ecological relevance to the movie are less likely to induce changes in attention and arousal.", "Keywords": "Smartphone ; Spectatorship ; Attention ; Distraction ; Arousal ; Electrodermal activity ; Eye tracking", "DOI": "10.1016/j.entcom.2023.100598", "PubYear": 2024, "Volume": "48", "Issue": "", "JournalId": 21223, "JournalTitle": "Entertainment Computing", "ISSN": "1875-9521", "EISSN": "1875-953X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Trinity Long Room Hub Arts & Humanities Research Institute; ADAPT Centre of Excellence for AI-Driven Digital Content Technology, Trinity College Dublin, College Green, Dublin 2, Ireland;Aalto Behavioral Laboratory, Aalto University, Otakaari 5 I, 02150 Espoo, Finland;Corresponding author at: Trinity Long Room Hub Arts & Humanities Research Institute, Trinity College Dublin, College Green, Dublin 2, Ireland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Psychology, University College Dublin, John <PERSON> Building, Stillorgan Rd, Belfield, Dublin 4, Ireland"}], "References": []}, {"ArticleId": 109359791, "Title": "Proposal of a diversified investment portfolio in stocks: An approach to AHP-Gaussian method", "Abstract": "The purpose of this article is to assist decision-making in the selection of diversified investment portfolios in Brazilian stocks, proposing alternatives in a hierarchical manner within each sector of the Stock Exchange. Among various multicriteria decision methods, the AHP-Gaussian method, a new approach to the traditional AHP that does not require prior evaluation by a decision-maker, was chosen. Using criteria from fundamental analysis of companies, the methodology hierarchizes alternatives within each sector of the Stock Exchange. As a result, the proposal suggests the best companies for investment in each sector, which can be viewed through a web application that allows filters according to the investor's needs. Thus, the methodology presented in this study can provide valuable support for decision-making in the financial market.", "Keywords": "Analytical Hierarchy Process ; Financial Market ; AHP-Gaussian", "DOI": "10.1016/j.procs.2023.07.056", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of São Paulo, São Paulo, SP 05508-010, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of São Paulo, São Paulo, SP 05508-010, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-249, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Fluminense Federal University, Niterói, RJ 24210-249, Brazil;Military Institute of Engineering, Urca, RJ 22290-270, Brazil"}], "References": [{"Title": "Design of a framework of military defense system for governance of geoinformation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "174", "JournalTitle": "Procedia Computer Science"}, {"Title": "Assisting in the choice to fill a vacancy to compose the PROANTAR team: Applying VFT and the CRITIC-GRA-3N methodology", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "214", "Issue": "", "Page": "478", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 109359796, "Title": "Correction to: Potential cyber threats of adversarial attacks on autonomous driving models", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11416-023-00497-8", "PubYear": 2024, "Volume": "20", "Issue": "2", "JournalId": 3946, "JournalTitle": "Journal of Computer Virology and Hacking Techniques", "ISSN": "", "EISSN": "2263-8733", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Data Analysis and Machine Learning, Financial University Under the Government of the Russian Federation, Moscow, Russia; Corresponding author."}], "References": []}, {"ArticleId": 109359804, "Title": "Volatility and risk contagion of international stock market in the context of COVID-19", "Abstract": "In order to reveal the impact of the outbreak of COVID-19 epidemic on risk contagion in the international stock market, this paper establishes an ARMA-GARCH-Copula model based on the R-Vine structure to empirically analyze the stock index data of 19 important countries(regions) around the world, and uses complex network to display the risk contagion path. This research shows that the risk contagion correlation of the global stock market has significantly improved due to the impact of COVID-19; The correlation of risk transmission among European countries is significantly higher than that of other regions, reflecting a closer integration connection between EU countries; Finally, unlike previous literature, this article finds that stock market of Hong Kong in China has become an important node in the international stock market, playing an important role in the risk network before and after the epidemic.", "Keywords": "COVID-19 ; R-Vine-Copula ; risk contagion ; complex network", "DOI": "10.1016/j.procs.2023.07.050", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100090, China;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining & Knowledge Management, Chinese Academy of Sciences, Beijing, 100190 P.R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100090, China;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100090, China;Research Center on Fictitious Economy & Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining & Knowledge Management, Chinese Academy of Sciences, Beijing, 100190 P.R. China"}], "References": []}, {"ArticleId": 109359808, "Title": "Pricing decision of dual-channel supply chain with members risk-aversion under the participation of e-commerce platform", "Abstract": "In this paper, it considers a two-stage dual-channel supply chain model composed of a risk aversion manufacturer, a risk aversion retailer and a risk-neutral e-commerce platform. Under the condition of information symmetry, it is supposed that the manufacturer is financially constrained. Besides, the analysis is based on Stackelberg game theory. The equilibrium price without capital constraint is compared with the equilibrium price in three cases of e-commerce platform financing, bank loan financing and prepayment financing. Then the effects of risk aversion coefficients of the manufacturer and the retailer on equilibrium prices are compared in four cases. The influence of different financing rates on wholesale price, direct selling price and retail price is analyzed simultaneously.", "Keywords": "Dual channel supply chain ; Risk aversion ; Pricing decisions", "DOI": "10.1016/j.procs.2023.07.020", "PubYear": 2023, "Volume": "221", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Yihua Ma", "Affiliation": "School of Management Science and Engineering, Hebei University of Economics and Business, Shijiazhuang 050061, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Hebei University of Economics and Business, Shijiazhuang 050061, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Hebei University of Economics and Business, Shijiazhuang 050061, PR China"}], "References": []}]