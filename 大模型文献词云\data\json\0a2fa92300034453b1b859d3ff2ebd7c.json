[{"ArticleId": 78183079, "Title": "Context-based Co-presence detection techniques: A survey", "Abstract": "In this paper, we present a systematic survey on the contextual information based proximity detection techniques. These techniques are heavily used for improving security and usability in Zero-Interaction based Co-presence Detection and Authentication (ZICDA) systems. In particular, this survey includes a discussion on the possible adversary and communication models along with the existing security attacks on ZICDA systems. It also reviews the state-of-the-art proximity detection techniques that make use of contextual information. The proximity detection techniques are commonly referred as Contextual Co-presence (COCO) protocols. The COCO protocols dynamically collect and use contextual information to improve the security of ZICDA systems during the proximity verification process. Finally, we summarize the significant challenges and suggest possible innovative and efficient future solutions for securely detecting co-presence between devices in the presence of adversaries. The proximity verification techniques presented in the literature usually involve several trade-offs between metrics such as efficiency, security, deployment cost, and usability. At present, there is no ideal solution which adequately addresses the trade-off between these metrics. Therefore, we trust that this review gives an insight into the strengths and shortcomings of the known research methodologies and pave the way for the design of future practical, secure, and efficient solutions.", "Keywords": "Relay attack ; Zero-interaction authentication ; Context-aware ; Sensor modalities ; Distance bounding ; RFID ; Proximity detection", "DOI": "10.1016/j.cose.2019.101652", "PubYear": 2020, "Volume": "88", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Padua, Padua, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Padua, Padua, Italy;Department of Computer Science and Engineering, Manipal University Jaipur, Jaipur, India;Corresponding author."}], "References": []}, {"ArticleId": 78183443, "Title": "Assessing the suitability of FROM-GLC10 data for understanding agricultural ecosystems in China: Beijing as a case study", "Abstract": "The first 10 m resolution global land-cover map, FROM-GLC10 was released in early March 2019. Due to its high spatial resolution and reliable global accuracy, we evaluated the suitability of FROM-GLC10 data for understanding agricultural ecosystems in Beijing using a comparable vector data set, Google Earth images and field survey data. The overall accuracy (OA) for FROM-GLC10 based on three data sets was 71.08%, 79.63% and 80.36%, respectively. Meanwhile, there were notable misclassifications between cropland, grassland and forest. The limited accuracy for these vegetation types might be attributed to the spatially mixed vegetation structures, seasonal variations of vegetation and the temporal inconsistence between Landsat and Sentinel data set. Due to its satisfactory OA, FROM-GLC10 data have the potential to be widely employed for evaluating the progress of large-scale ecological restoration projects. On the other hand, the data producers, who can pre-set and classify some customized land-cover types, consider time-series analysis and big data fusion methods and conduct large-scale verification, and data users, who can integrate different data sources, should work together to enhance the suitability and reliability of FROM-GLC10 data for understanding agricultural ecosystems in China.", "Keywords": "", "DOI": "10.1080/2150704X.2019.1677966", "PubYear": 2020, "Volume": "11", "Issue": "1", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Research Center for Information Technology in Agriculture, Beijing Academy of Agricultural and Forestry Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Gao", "Affiliation": "College of Land Science and Technology, China Agricultural University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Research Center for Information Technology in Agriculture, Beijing Academy of Agricultural and Forestry Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Global Change and Earth System Science, Beijing Normal University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Global Change and Earth System Science, Beijing Normal University, Beijing, China"}], "References": []}, {"ArticleId": 78183546, "Title": "Brightness gradient-corrected hyperspectral image mosaics for fractional vegetation cover mapping in northern California", "Abstract": "", "Keywords": "", "DOI": "10.1080/2150704X.2019.1670518", "PubYear": 2020, "Volume": "11", "Issue": "1", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Geography Department, Humboldt-Universität zu Berlin, Berlin, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Geography Department, Humboldt-Universität zu Berlin, Berlin, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Geography Department, Humboldt-Universität zu Berlin, Berlin, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Center for Interdisciplinary Geospatial Analysis, Department of Geography, Environment, and Planning, Sonoma State University, Rohnert Park, CA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Geography Department, Humboldt-Universität zu Berlin, Berlin, Germany;Integrative Research Institute on Transformations of Human-Environment Systems (IRI THESys), Humboldt-Universität zu Berlin, Berlin, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Geography Department, Humboldt-Universität zu Berlin, Berlin, Germany;Integrative Research Institute on Transformations of Human-Environment Systems (IRI THESys), Humboldt-Universität zu Berlin, Berlin, Germany"}], "References": []}, {"ArticleId": 78183594, "Title": "Leverage side information for top‐N recommendation with latent Gaussian process", "Abstract": "<p>Currently, Recommender Systems (RS) have been ubiquitously applied to various online applications and obtained tremendous success due to their capability to overcome information overload; however, the available Side Information (SI), such as demographics and attributions of items, is always neglected. Actually, SI could reflect user's interests and preference, and even influence user's decision over items; therefore, it would be greatly helpful to leverage side information to improve the performance of RS, which is already partly certificated in previous research studies. Motivated by this, in this article, a novel multi-task learning-based recommendation system referred to as LGP-RS is proposed, which takes utility functions mapping from the non-linear side information to low-rank feature space with Gaussian process (GP) priors, for users and items, respectively. In addition, Laplace approximation method is employed to approximate the posterior distribution for the utility functions, and gradient-based method is employed to learn the hyperparameters for GP. This flexible approach is able to capture the non-linear high-order interactions within side information, reduce the prediction uncertainty, and provide personalized top-N recommendation. Experimental analysis over three real-world datasets demonstrates that LGP-RS could deal with both explicit and implicit side information simultaneously, and significantly outperforms state-of-the-art approaches.</p>", "Keywords": "Gaussian process;Laplace approximation;recommendation system;side information", "DOI": "10.1002/cpe.5534", "PubYear": 2021, "Volume": "33", "Issue": "12", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu, China; <PERSON>, School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu 611731, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Huaihua University, Huaihua, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu, China"}], "References": []}, {"ArticleId": 78183628, "Title": "An Analysis of a Stress Checking System", "Abstract": "<p class=\"global-para-14\"> <p>We developed a system that can objectively evaluate fatigue of long Visual Display Terminals, using both electrocardiogram data and self-reporting questionnaires. The objective was to identify the elements that contribute to stress within the organization. R-to-R interval (RRI) analysis of the electrocardiogram data allows the establishment of objective stress indices, and the rationalization of diagnosis. We therefore propose the introduction of biometric measurement to stress checking systems.</p> </p>", "Keywords": "Introduction of stress check system;Evaluation of VDT work fatigue;RRI analysis", "DOI": "10.5057/ijae.IJAE-D-19-00006", "PubYear": 2020, "Volume": "19", "Issue": "1", "JournalId": 24153, "JournalTitle": "International Journal of Affective Engineering", "ISSN": "", "EISSN": "2187-5413", "Authors": [{"AuthorId": 1, "Name": "Hiromi FUJIMORI", "Affiliation": "Aoyama Gakuin University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Kogakuin University"}, {"AuthorId": 3, "Name": "Hisaya TANAKA", "Affiliation": "Kogakuin University"}], "References": []}, {"ArticleId": 78183667, "Title": "Evaluation and Proceed Method for Design of Pharmaceutical Product Containers Aimed at Satisfying Both of Safety and Usability", "Abstract": "<p class=\"global-para-14\"> <p>It has become a serious social concern that cases of accidental ingestion of pharmaceutical products are increasing. For example, elderly persons sometimes accidentally ingest the products together with the containers, and children may open pill containers and accidentally swallow the contents. We conducted this study to understand several contradictory conditions from expertise point of affective engineering and user-centered design and to obtain design indicator for overall solution, in order to solve these social problems related to pill container. In this paper, as for first step, we consider a method for evaluating child resistant features of pill containers that are difficult for children, but relatively easy for ordinary people, to open, especially in order to prevent children from accidentally swallowing pharmaceutical products. It consists of three experiments that are (a) container-opening test by children, (b) impression-evaluation test related to factor of safety, and (c) PTP-opening test by adults.</p> </p>", "Keywords": "Design evaluation;Child resistant pill containers;User-centered design approach", "DOI": "10.5057/ijae.IJAE-D-18-00033", "PubYear": 2020, "Volume": "19", "Issue": "1", "JournalId": 24153, "JournalTitle": "International Journal of Affective Engineering", "ISSN": "", "EISSN": "2187-5413", "Authors": [{"AuthorId": 1, "Name": "Na<PERSON>ge AKITA", "Affiliation": "Kyushu University"}, {"AuthorId": 2, "Name": "Yoshitsugu MORITA", "Affiliation": "Kyushu University"}, {"AuthorId": 3, "Name": "Hisao SHIIZUKA", "Affiliation": "Shiizuka Kansei Engineering Laboratory, Co., Ltd."}], "References": []}, {"ArticleId": 78183668, "Title": "Comparing the Features of the Diffuse Alpha Pattern with the Normal Alpha Pattern using Wavelet-crosscorrelation Analysis", "Abstract": "<p class=\"global-para-14\"> <p>In some individuals, alpha waves appear in many regions of the brain, and this is considered abnormal electroencephalography (EEG). This phenomenon is known as diffuse alpha pattern. This study aims to extract and compare the features of the connectivity between the parts of the brain. The EEGs of healthy individuals and mental disorder patients with the diffuse alpha pattern were analyzed using wavelet-crosscorrelation analysis. Five epochs of 2 seconds were analyzed for each subject. Wavelet-crosscorrelation coefficients were calculated for frequencies in the alpha band for all epochs in each subject. The results showed that the WCC values were higher in the patients with diffuse alpha. The highest wavelet power spectra in the alpha band of the patients occurred at a lower frequency. Our results suggest that the connectivity strength along the sagittal and occipital orientations in the brain of the patients could be stronger than healthy individuals. </p> </p>", "Keywords": "Electroencephalography;Diffuse alpha pattern;Wavelet-crosscorrelation analysis", "DOI": "10.5057/ijae.IJAE-D-18-00034", "PubYear": 2020, "Volume": "19", "Issue": "1", "JournalId": 24153, "JournalTitle": "International Journal of Affective Engineering", "ISSN": "", "EISSN": "2187-5413", "Authors": [{"AuthorId": 1, "Name": "Steven MA CARPELS", "Affiliation": "University of Hyogo, Graduate School of Applied Informatics"}, {"AuthorId": 2, "Name": "Yuji INOGUCHI", "Affiliation": "HORIBA Ltd."}, {"AuthorId": 3, "Name": "Shohei KOBAYASHI", "Affiliation": "University of Hyogo, Graduate School of Applied Informatics"}, {"AuthorId": 4, "Name": "Ayumi MURAMATSU", "Affiliation": "University of Hyogo, Graduate School of Applied Informatics"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Hyogo, Graduate School of Applied Informatics"}, {"AuthorId": 6, "Name": "Masato ITO", "Affiliation": "University of Hyogo, Graduate School of Applied Informatics"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>-MATSUMOTO", "Affiliation": "University of Hyogo, Graduate School of Applied Informatics"}], "References": []}, {"ArticleId": 78183883, "Title": "Polynomial Time Schedulability Test for Periodic Non-Preemptive 2-Task System", "Abstract": "Non-preemptive scheduling of tasks is often preferred in safety-critical systems because of its simplicity in implementation. But, one of the major issues with non-preemptive scheduling is that no known polynomial time schedulability condition exists. The general problem of schedulability of a set of tasks on a single processor under such scheme is known to be NP-Hard [1] . People have attempted to propose different sufficient or necessary conditions for schedulability of a set of tasks under various constrained scenarios. In this work, we explore schedulability condition for 2-task systems when the tasks are scheduled non-preemptively and periodically using work conserving model of execution. We assume that the tasks are released at the same point of time initially. We show that the proposed condition for 2-task systems can be evaluated in polynomial time.", "Keywords": "Periodic ; Non-preemptive ; Schedulability ; Real-time systems ; Scheduling", "DOI": "10.1016/j.ipl.2019.105867", "PubYear": 2020, "Volume": "154", "Issue": "", "JournalId": 5748, "JournalTitle": "Information Processing Letters", "ISSN": "0020-0190", "EISSN": "1872-6119", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Patna, Patna, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Patna, Patna, India"}], "References": []}, {"ArticleId": 78183914, "Title": "The ICTBioMed NCIP Hub: Cancer research in a science gateway consortium", "Abstract": "ICTBioMed, the International Consortium for Technology in Biomedicine is a consortium for biomedical collaborative research and the consortium partners believe in decentralization and democratization of science. The consortium works on strategies to develop ready-to-use research initiatives bridging the gap between research, implementation and ready-to-use science. The consortium has identified real science use cases with the help of the collaborative partners to get access of shared data and methods using the principles of science gateways. ICTBioMed has set up an NCIP Hub instance based upon the HUBzero framework and started using it for community research activities. It is an environment for scientific collaboration where researchers can access and take advantage of a variety of resources shared by others. The NCIP Hub supports members with shared access to data, tools, and standards across the cancer research community. Cancer researchers, in principle, have access to a wide range of data and software tools but in practice, however, they make limited use of these resources. Barriers that restrict access include the need to install software locally, inconsistent user interfaces, security concerns and poor documentation. ICTBioMed is developing a cancer science gateway that will greatly simplify analysis in cancer imaging research by offering a single point of entry and a unified interface for multiple tools, databases, and image repositories widely used by the community. The consortium aims at borderless research with a common goal to solve real life biomedical problems by facilitating collaborative research.", "Keywords": "Biomedicine ; Cloud ; ICTBioMed ; NCIP Hub ; HUBzero ; Cancer research", "DOI": "10.1016/j.future.2019.10.011", "PubYear": 2020, "Volume": "105", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Development of Advanced Computing (C-DAC), India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Development of Advanced Computing (C-DAC), India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Development of Advanced Computing (C-DAC), India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Poznan Supercomputing and Networking Center (PSNC), Poland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Open Health Systems Laboratory (OHSL), USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Open Health Systems Laboratory (OHSL), USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Purdue University, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Arizona State University (ASU), USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "University of Notre Dame, Notre Dame, IN 46556, USA;Corresponding author"}], "References": []}, {"ArticleId": 78184963, "Title": "Development of a social media process model for fashion and apparel supply chain decisions", "Abstract": "Fashion and apparel supply chains are complex and volatile. Thiscauses uncertainties for supply chain decisions. For reducing the uncertainty, decision-maker use different information sources. Typically, historical sales data is consulted for enhancing the decision base. Customer preferences are mostly publicly accessible via different social media channels. Motivated by the increased availability of customer preferences, this paper proposesa process model for exploiting social media data for fashion andapparel supply chain decisions. Characteristics of fashion andapparel supply chains and social media data are illustrated. Basedon these characteristics, functional requirements for a processmodel are elaborated. In particular, the veracity feature is takeninto consideration. Following these requirements, a process modelconsisting of the three main layers: process, social media and textmining is developed. A case study is framed around conducted following the proposed process model. Two years of fashion blogdata is extracted, and dictionary-based keyword extraction, rulebased classification, as well as automatic frequency analyses, areconducted. The proposed process model enables structural andtargeted exploitation of social media data for fashion and apparel supply chain decisions.", "Keywords": "Social media data ; fashion and apparel supply chains ; process model ; veracity ; fashion blogs", "DOI": "10.1080/23335777.2019.1680577", "PubYear": 2020, "Volume": "6", "Issue": "2", "JournalId": 2756, "JournalTitle": "Cyber-Physical Systems", "ISSN": "2333-5777", "EISSN": "2333-5785", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "International Graduate School for Dynamics in Logistics, University of Bremen, Bremen, Germany"}], "References": []}, {"ArticleId": 78185436, "Title": "A heterogeneous QUALIFLEX method with criteria interaction for multi-criteria group decision making", "Abstract": "Green supplier selection complexity involves such problems which usually need to offer the management of dependencies among criteria in the selection process. Meanwhile, heterogeneous contexts are adopted based on different criteria, which drives to propose a novel multi-criteria group decision making (MCGDM) method. To solve this problem, this paper proposes a new MCGDM approach for heterogeneous information and dependent criteria based on the QUALItative FLEXible (QUALIFLEX) method and Choquet integral. For managing dependency among criteria, a new graphical representation of criteria interaction is presented and the identification of fuzzy measure is then obtained considering group consensus reaching. The multi-criteria heterogeneous QUALIFLEX method with regard to dependency of criteria is finally applied to a green supplier selection problem and a comparative analysis is performed to illustrate its feasibility and effectiveness.", "Keywords": "Multiple criteria group decision making ; QUALIFLEX approach ; Fuzzy measure ; Choquet integral ; Green supplier selection", "DOI": "10.1016/j.ins.2019.10.044", "PubYear": 2020, "Volume": "512", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management and Economics, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 2, "Name": "Jindong Qin", "Affiliation": "School of Management and Economics, Beijing Institute of Technology, Beijing 100081, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Management and Economics, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Management and Economics, Beijing Institute of Technology, Beijing 100081, China"}], "References": []}, {"ArticleId": 78185477, "Title": "High-order numerical simulation of axisymmetric wave phase conjugation", "Abstract": "In the present paper, a general physico-mathematical model of magneto-acoustic Wave Phase Conjugation (WPC) is proposed, which includes wave propagation both in solid zones (the conjugator itself and other solid regions, if present) and in fluid zones surrounding the conjugator. Acoustic waves in the solid zones are assumed to behave linearly while in the fluid zones the waves may be either linear or non-linear. The axisymmetric governing equations of the model are discretized on an unstructured triangular mesh with a modified version of the Nodal Discontinuous Galerkin (NDG) method, which is a compact, high-order technique based on non-collocated solution and flux bases. The third-order Strong Stability-Preserving (SSP) Runge-Kutta (RK) scheme is used for integration in time. To avoid any false reflections from the outer boundaries of the computational domain the Nearly Perfectly Matched Layer (NPML) technique is adopted. A comprehensive set of test problems addressing all facets of the proposed numerical model is used for verification via comparison with analytical solutions. After the accuracy, performance, and reliability of the proposed model are established, two WPC problems are numerically simulated for the linear and weakly non-linear regimes. The main WPC properties, such as retro-focusing and parametric resonance, are observed in the numerical experiments with good accuracy.", "Keywords": "Wave phase conjugation ; CFD ; Nodal discontinuous Galerkin ; High-order ; Weakly non-linear ; 2D/Axisymmetric", "DOI": "10.1016/j.compfluid.2019.104353", "PubYear": 2020, "Volume": "197", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, McGill University, Montreal, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, McGill University, Montreal, Canada"}], "References": []}, {"ArticleId": 78185585, "Title": "Understanding minority costumes: a computer vision perspective", "Abstract": "<p>It is an extremely interesting work to understand the minority costumes in computer vision and ethnology community. It explored some crucial clue for understanding minority costumes via computer vision technology. As it known to all, complicated and subtle structure between different minority costumes lead it becomes hard work to recognize them with computer vision even people. An intelligent framework is proposed for understanding minority costumes from computer vision perspective in this paper. First, the images are converted into grayscale ones as the digital image processing pipeline; then, the grayscale images are segmented with the help of structured forests algorithm; after that, a new Revised Histogram of Oriented Gradient is proposed to compute the feature for each segmented gray minority costume image. At the last, the random forests method is used as the classifier for this minority costumes understanding intelligent system. For lack of acknowledged minority costume image data sets, we evaluated the performances of the proposed method on self-construct data set, and the experimental results are presented.</p>", "Keywords": "Minority costumes understanding; Image processing; HOG; Random forests", "DOI": "10.1007/s00530-019-00637-5", "PubYear": 2020, "Volume": "26", "Issue": "2", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science and Information Engineering, Guizhou Minzu University, Guiyang, China;Academic Affairs Office, Guizhou Minzu University, Guiyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science and Information Engineering, Guizhou Minzu University, Guiyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science and Information Engineering, Guizhou Minzu University, Guiyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science and Information Engineering, Guizhou Minzu University, Guiyang, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Data Science and Information Engineering, Guizhou Minzu University, Guiyang, China;Key Laboratory of Pattern Recognition and Intelligent Systems of Guizhou, Guiyang, China"}], "References": []}, {"ArticleId": 78185598, "Title": "Toward multimodal corpus pragmatics: Rationale, case, and agenda", "Abstract": "<p>Human interaction is multimodal in nature, and meaning in discourse is created through an interplay of an array of modalities. Inspired by the integration of multimodality and corpus pragmatics, we are concerned with a general multimodal framework that facilitates the exploration on pragmatic questions by using corpus methods. By demonstrating how the study on speech acts in situated discourse is benefited from the multimodal corpus approach, we claim that the scope and methods of pragmatic studies will be enriched and the classic pragmatic theories could be further developed toward multimodal corpus pragmatics. This article, therefore, hopes to inspire further theoretical discussion and case study in this domain.</p>", "Keywords": "", "DOI": "10.1093/llc/fqz080", "PubYear": 2021, "Volume": "36", "Issue": "1", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tongji University, China"}], "References": []}, {"ArticleId": 78185629, "Title": "Three-Dimensional Cooperative Homing Guidance Law with Field-of-View Constraint", "Abstract": "", "Keywords": "Homing Guidance; Control Guidance; Consensus Algorithm; Missile Systems; Model Predictive Control; Numerical Simulation; Multi Agent System; Proportional Navigation; Feedback Linearization; Command Missile", "DOI": "10.2514/1.G004681", "PubYear": 2020, "Volume": "43", "Issue": "2", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, 100081 Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, 100081 Beijing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, 100081 Beijing, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, 100081 Beijing, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Missouri, Columbia, Missouri 65211"}], "References": []}, {"ArticleId": ********, "Title": "Link quality and energy utilization based preferable next hop selection routing for wireless body area networks", "Abstract": "The rising population and cost of medical services triggered the new technologies including Wireless Body Area Networks (WBANs) based on smart and intelligent biosensors nodes for sensing and monitoring the patient vital signs. The biosensor nodes have implanted inside or outside the human body to send the medical information to medical centers. For data dissemination in these services, different types of solutions and model have been designed to address the interference, body movement, disconnection quality of services issues in the network. This paper presents an Energy Aware Routing (EAR) protocol to minimize energy utilization and select preferable next hop by evaluating the link quality of sensor nodes. The proposed protocol evaluates the energy level, link quality, and remaining energy level to balance the load, minimize the energy utilization, and enhance the data transmission. Various simulations have conducted to evaluate the proposed protocols performance in terms of energy consumption, data delivery, delay, and data throughput. Experimental results indicated that the proposed protocol has a better mechanism for date routing and better solution to minimize the energy of sensor nodes in WBANs.", "Keywords": "Energy efficiency ; Routing ; Link quality ; Interference ; Energy consumption ; Data delivery", "DOI": "10.1016/j.comcom.2019.10.030", "PubYear": 2020, "Volume": "149", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Bahria University, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Kyungpook National University, Daegu, Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Xidian University, Xi’an, 710071, China;Department of Embedded Systems Engineering, Incheon National University, Incheon, 22012, Korea;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Embedded Systems Engineering, Incheon National University, Incheon, 22012, Korea"}], "References": []}, {"ArticleId": 78186375, "Title": "What does existing NeuroIS research focus on?", "Abstract": "NeuroIS is a research field in which neuroscience theories and tools are used to better understand information systems phenomena. At present, NeuroIS is still an emerging field in information systems, and the number of available studies is limited. Because researchers who plan or execute NeuroIS research need to understand the status of the existing empirical research published in relevant journals, we have analyzed 78 empirical articles and put forward an integrative framework for understanding what existing NeuroIS research focuses on. Our framework is built upon stimulus–organism–response theory, which explains that stimulus factors can affect users’ psychological processes, which further lead to their responses. Then, we review the collected articles and summarize their findings to give more details of NeuroIS studies. Through this literature review, we identify several opportunities for future NeuroIS research in terms of influencing factors, measurement instruments, and subjects. We believe that our work will provide some meaningful insight for future NeuroIS research.", "Keywords": "NeuroIS ; Literature review ; Stimulus–organism–response theory ; Neuroscience tools ; Information systems", "DOI": "10.1016/j.is.2019.101462", "PubYear": 2020, "Volume": "89", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information, Renmin University of China, Beijing 100872, PR China|Research Institute of Smart Senior Care, Renmin University of China, Beijing 100872, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information, Renmin University of China, Beijing 100872, PR China|Research Institute of Smart Senior Care, Renmin University of China, Beijing 100872, PR China;Corresponding author at: School of Information, Renmin University of China, Beijing 100872, PR China"}], "References": []}, {"ArticleId": 78186535, "Title": "A high-throughput shared service to estimate evapotranspiration using Landsat imagery", "Abstract": "The execution of workflows to estimate time series of evapotranspiration (ET) from Earth Observation Satellite (EOS) data is a computing intensive activity. When running these workflows on their own personal computers, users usually need to restrict one or more of the following dimensions: resolution of input EOS data, size of the area of interest, and/or duration of the time series. Recently, a large public cloud provider has made available a platform for the parallel execution of these workflows. Although this system addresses the performance issue, it still presents limitations. In particular, it provides little support for the management of metadata associated with the executions that generated the ET estimations, impairing the effective sharing of such data. Additionally, it does not allow the reuse of already available implementations of ET estimation algorithms. Moreover, the service governance model is entirely defined by that service provider, raising risks that users may not want to face. We address all these challenges by leveraging software containerization technology. Firstly, we use containers to facilitate the deployment of independent and customizable ET processing services on top of either public or private clouds. This not only addresses the performance issue, but also provides freedom for each service to define its own governance model, in consonance with the needs of users. Secondly, we define a simple protocol allowing the easy integration of different container-based implementations of the different stages of the workflow, including those already available. Finally, the service automatically collects provenance information required for the effective sharing of the output it generates. The paper presents the architecture of the proposed service, emphasizing how it addresses the above-mentioned challenges, and provides a performance assessment of a small deployment. It also includes a discussion on possible applications that can benefit from the proposed service.", "Keywords": "Remote sensing ; Surface energy balance methods ; Software containerization ; Parallel computing ; Data provenance ; Data and resource sharing", "DOI": "10.1016/j.cageo.2019.104341", "PubYear": 2020, "Volume": "134", "Issue": "", "JournalId": 4833, "JournalTitle": "Computers & Geosciences", "ISSN": "0098-3004", "EISSN": "1873-7803", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Production Engineering, Federal University of Campina Grande, Rua Luiz Grande s/n, 58540-000 Sumé, PB, Brazil;Forest Research Centre (CEF), School of Agriculture, University of Lisbon, Tapada da Ajuda, 1349-017 Lisboa, Portugal"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing and Systems, Federal University of Campina Grande, Av. <PERSON><PERSON><PERSON> s/n, 58429-900 Campina Grande, PB, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing and Systems, Federal University of Campina Grande, Av. <PERSON><PERSON><PERSON> s/n, 58429-900 Campina Grande, PB, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Federal University of Campina Grande, Av. <PERSON><PERSON><PERSON> s/n, 58429-900 Campina Grande, PB, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, Federal University of Campina Grande, Av. <PERSON><PERSON><PERSON> s/n, 58429-900 Campina Grande, PB, Brazil"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Forest Research Centre (CEF), School of Agriculture, University of Lisbon, Tapada da Ajuda, 1349-017 Lisboa, Portugal"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Computing and Systems, Federal University of Campina Grande, Av. <PERSON><PERSON><PERSON> s/n, 58429-900 Campina Grande, PB, Brazil;Corresponding author"}], "References": []}, {"ArticleId": 78186589, "Title": "Improved local search for graph edit distance", "Abstract": "The graph edit distance (GED) measures the dissimilarity between two graphs as the minimal cost of a sequence of elementary operations transforming one graph into another. This measure is fundamental in many areas such as structural pattern recognition or classification. However, exactly computing GED is NP -hard. Among different classes of heuristic algorithms that were proposed to compute approximate solutions, local search based algorithms provide the tightest upper bounds for GED. In this paper, we present K-REFINE and RANDPOST . K-REFINE generalizes and improves an existing local search algorithm and performs particularly well on small graphs. RANDPOST is a general warm start framework that stochastically generates promising initial solutions to be used by any local search based GED algorithm. It is particularly efficient on large graphs. An extensive empirical evaluation demonstrates that both K-REFINE and RANDPOST perform excellently in practice.", "Keywords": "Graph edit distance ; Local search ; Stochastic warm start ; 41A05 ; 41A10 ; 65D05 ; 65D17", "DOI": "10.1016/j.patrec.2019.10.028", "PubYear": 2020, "Volume": "129", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ENSICAEN, UNICAEN, CNRS, GREYC, Normandie Univ, Caen 14000, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science, Free University of Bozen-Bolzano, Piazza Dominicani 3, Bolzano 39100, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ENSICAEN, UNICAEN, CNRS, GREYC, Normandie Univ, Caen 14000, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ENSICAEN, UNICAEN, CNRS, GREYC, Normandie Univ, Caen 14000, France"}], "References": [{"Title": "Trends in graph-based representations for Pattern Recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "3", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Fast linear sum assignment with error-correction and no cost constraints", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "37", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "On the exact computation of the graph edit distance", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "46", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": ********, "Title": "Testing implementation of FAMTAR: Adaptive multipath routing", "Abstract": "Flow-Aware Multi-Topology Adaptive Routing (FAMTAR) is a new approach to multipath and adaptive routing in IP networks which enables automatic use of alternative paths when the primary one becomes congested. It provides more efficient network resource utilization and higher quality of transmission compared to standard IP routing. However, thus far it has only been evaluated through simulations. In this paper we share our experiences from building a real-time FAMTAR router and present results of its tests in a physical network. The results are in line with those obtained previously through simulations and they open the way to implementation of a production grade FAMTAR router.", "Keywords": "Router ; Multipath routing ; Adaptive routing ; SDN ; Traffic engineering ; Click modular router ; Testing", "DOI": "10.1016/j.comcom.2019.10.029", "PubYear": 2020, "Volume": "149", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Telecommunications, AGH University of Science and Technology, Kraków, Poland;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Telecommunications, AGH University of Science and Technology, Kraków, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Telecommunications, AGH University of Science and Technology, Kraków, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Telecommunications, AGH University of Science and Technology, Kraków, Poland"}], "References": []}, {"ArticleId": ********, "Title": "SQL Versus NoSQL Databases to Assess Their Appropriateness for Big Data Application", "Abstract": "Background: \nNowadays, the digital world is rising rapidly and becoming very difficult in\nnature's quantity, diversity, and speed. Recently, there have been two major changes in data management,\nwhich are NoSQL databases and Big Data Analytics. While evolving with the diverse reasons,\ntheir independent growths balance each other and their convergence would greatly benefit organization\nto make decisions on-time with the amount of multifaceted data sets that might be semi\nstructured, structured, and unstructured. Though several software solutions have come out to support\nBig Data analytics on the one hand, on the other hand, there have been several packages of NoSQL\ndatabase available in the market.\n \n \n Aim and Methods: \nThe main goal of this article is to give comprehension of their perspective and a\ncomplete study to associate the future of the emerging several important NoSQL data models.\n \n \n Results: \n Evaluating NoSQL databases for Big Data analytics with traditional SQL performance\nshows that NoSQL database is a superior alternative for industry condition need high-performance\nanalytics, adaptability, simplicity, and distributed large data scalability.\n \n \n Conclusion: \n In this article we conclude with the adoption of NoSQL in various markets.", "Keywords": "", "DOI": "10.2174/2213275912666191028111632", "PubYear": 2021, "Volume": "14", "Issue": "4", "JournalId": 31517, "JournalTitle": "Recent Patents on Computer Sciencee", "ISSN": "2213-2759", "EISSN": "1874-4796", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Systems, University of Nizwa, Oman"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computing & Informatics, Mazoon College, Oman"}], "References": []}, {"ArticleId": 78187488, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0010-4655(19)30327-3", "PubYear": 2020, "Volume": "246", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [], "References": []}, {"ArticleId": 78187832, "Title": "Data reconstruction at surface in immersed-boundary methods", "Abstract": "This work compares interpolation techniques for data reconstruction at the surface in an immersed-boundary method. Three different methods of surface pressure reconstruction based on inverse distances are presented, which are christened as: Inverse Distance Weight (IDW) method, Inverse Distance Weight at Interpolation Point method (IDW-IP) and Inverse Distance Weight based on Upwinding (IDW-Upwind) method. Additionally, shear stress at the immersed surface is determined using two approaches: direct interpolation of velocity gradient at the surface using IDW method, and interpolation of velocity at a point along the surface normal using IDW-IP method. The interpolation methods are verified against analytic solutions of ideal flow past a circular cylinder and subsonic-supersonic inviscid flow in a convergent-divergent nozzle, and validated against laminar flow simulations of Mach 0.5 flow past a NACA0012 airfoil, Mach 2.0 flow past a circular cylinder, and Mach 3.0 flow past a 10<sup>∘</sup> ramp. The verification cases show that while the pressure values reconstructed at the surface by the three interpolation methods are very similar for the incompressible flow, the IDW-Upwind method produces the sharpest pressure rise across the normal shock in the convergent-divergent nozzle. Comparisons of the reconstructed surface pressure coefficient ( C<sub>p</sub> ) and skin-friction coefficient ( C<sub>f</sub> ) with values available from literature or ANSYS-Fluent simulations conducted as part of the validation study show good match, but indicate that the reconstructed pressure and shear stress values at the immersed surface has noise, which, however, reduces with grid refinement. Further, the IDW and IDW-Upwind method for pressure reconstruction, and the gradient reconstruction based method for shear stress calculation are shown to produce less noise in computed values. Integrated drag and lift values using the reconstructed surface pressure and shear stress indicate that while the different methods used for pressure reconstruction result in similar values of aerodynamic loads, the gradient-based shear stress calculations result in more accurate load estimation. Finally, one of the interpolation methods (IDW-Upwind) is used to investigate the variation of the surface pressure coefficient with time for a NACA0012 airfoil undergoing non-periodic plunge motion in a Mach 0.2 flow. The computed surface pressure coefficients are correlated with the leading and trailing edge vortices in the flow field.", "Keywords": "", "DOI": "10.1016/j.compfluid.2019.104236", "PubYear": 2020, "Volume": "196", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IIT-Madras, Chennai, Tamil Nadu 600036, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "IIT-Madras, Chennai, Tamil Nadu 600036, India;Corresponding author."}], "References": []}, {"ArticleId": 78187834, "Title": "LLVM-based automation of memory decoupling for OpenCL applications on FPGAs", "Abstract": "The availability of OpenCL High-Level Synthesis (OpenCL-HLS) has made FPGAs an attractive platform for power-efficient high-performance execution of massively parallel applications. At the same time, new design challenges emerge for massive thread-level parallelism on FPGAs. One major execution bottleneck is the high number of memory stalls exposed to data-path which overshadows the benefits of data-path customization. This article presents a novel LLVM-based tool for decoupling memory access from computation when synthesizing massively parallel OpenCL kernels on FPGAs. To enable systematic decoupling, we use the idea of kernel parallelism and implement a new parallelism granularity that breaks down kernels to separate data-path and memory-path (memory read/write) which work concurrently to overlap the computation of current threads<sup>[1]</sup> with the memory access of future threads (memory pre-fetching at large scale). At the same time, this paper proposes an LLVM-based static analysis to detect the decouplable data for resolving the data dependency and maximize concurrency across the kernels. The experimental results on eight Rodinia benchmarks on Intel Stratix V FPGA demonstrate significant performance and energy improvement over the baseline implementation using Intel OpenCL SDK. The proposed sub-kernel parallelism achieves more than 2x speedup, with only 3% increase in resource utilization, and 7% increase in power consumption which reduces the overall energy consumption more than 40%.", "Keywords": "OpenCL ; LLVM ; FPGA ; Parallelism", "DOI": "10.1016/j.micpro.2019.102909", "PubYear": 2020, "Volume": "72", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of North Carolina Charlotte, NC, 28223 USA;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of North Carolina Charlotte, NC, 28223 USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of North Carolina Charlotte, NC, 28223 USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of North Carolina Charlotte, NC, 28223 USA"}], "References": []}, {"ArticleId": 78188278, "Title": "DEMINER\n : test generation for high test coverage through mutant exploration", "Abstract": "<p>Most software testing techniques test a target program as it is and fail to utilize valuable information of diverse test executions on many variants/mutants of the original program in test generation.</p> <p>This paper proposes a new test generation technique DEMINER , which utilizes mutant executions to guide test generation on the original program for high test coverage.</p> <p> DEMINER first generates various mutants of an original target program and then extracts runtime information of mutant executions, which covered unreached branches by the mutation effects. Using the obtained runtime information, DEMINER inserts guideposts , artificial branches to replay the observed mutation effects, to the original target programs. Finally, DEMINER runs automated test generation on the original program with guideposts and achieves higher test coverage.</p> <p>We implemented DEMINER for C programs through software mutation and guided test generation such as concolic testing and fuzzing. We have shown the effectiveness of DEMINER on six real‐world target programs: Busybox‐ls , Busybox‐printf , Coreutils‐sort, GNU‐find , GNU‐grep and GNU‐sed . The experiment results show that DEMINER improved branch coverage by 63.4% and 19.6% compared with those of the conventional concolic testing techniques and the conventional fuzzing techniques on average, respectively.</p>", "Keywords": "automated test generation;concolic testing;fuzzing;mutation analysis;test coverage", "DOI": "10.1002/stvr.1715", "PubYear": 2021, "Volume": "31", "Issue": "1-2", "JournalId": 9990, "JournalTitle": "Software Testing, Verification and Reliability", "ISSN": "0960-0833", "EISSN": "1099-1689", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, KAIST, Daejeon, South Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Electrical Engineering, Handong Global University, Pohang, South Korea"}], "References": []}, {"ArticleId": 78189119, "Title": "Experimental Evaluation of Image Segmentation for Heart Images", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAET.2021.10024945", "PubYear": 2021, "Volume": "15", "Issue": "2/3", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 78190313, "Title": "A transparent ZnO nanowire MEMS gas sensor prepared by an ITO micro-heater", "Abstract": "This sturdy constructs a 3D-like heterogeneous device integrated structure using a transparent ZnO nanowire MEMS gas sensor and a blue LED. For the transparent gas sensor, ITO is used for the electrodes and the micro heater. The thermal image shows that the micro heater provides a heat source. The sensors exhibit a higher sensitivity to NO than other gases (C<sub>2</sub>H<sub>5</sub>OH, HCHO, H<sub>2</sub>S) at the optimal operating temperature of 200 ℃. For the MEMS gas sensor with blue light illumination from the bottom of the sensors, the results show that the blue light increases the carrier concentration in the ZnO nanowires. The response value to 50 ppb NO gas is increased from 48.13% to 86.17%.", "Keywords": "ZnO nanowires ; Gas sensor ; MEMS ; ITO", "DOI": "10.1016/j.snb.2019.127319", "PubYear": 2020, "Volume": "304", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, National Kaohsiung University of Science and Technology, Kaohsiung, 807, Taiwan, ROC;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Microelectronics and Department of Electrical Engineering, National Cheng Kung University, Tainan, 701, Taiwan, ROC"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Epistar Corporation, Tainan, 741, Taiwan, ROC"}], "References": []}, {"ArticleId": 78191611, "Title": "Oil palm plantation mapping from high-resolution remote sensing images using deep learning", "Abstract": "Oil palm plantation mapping is an important task in land planning and management in Malaysia. Most existing studies were based on satellite images using traditional machine learning or image segmentation methods. In order to obtain finer oil palm plantation maps from high spatial-resolution satellite images, we proposed a novel deep learning-based semantic segmentation approach, named Residual Channel Attention Network (RCANet). It consists of an encoder-decoder architecture and a post-processing component. The Residual Channel Attention Unit (RCAU) designed in our proposed approach reuses the low-level features extracted from the encoder part through upsampling, effectively enhancing the discriminative features and suppressing the indiscriminate features. We extended the fully connected Conditional Random Field (FC-CRF) in the post-processing to further refine the segmentation results. Experiment results were evaluated by our proposed Malaysian Oil Palm Plantation Dataset (MOPPD), which was collected from the Google Earth high spatial-resolution image and published in this article. Our proposed method achieves the overall accuracy (OA) of 96.88% and mean Intersection-over-Union (mean IoU) of 90.58%, improving the OA by 2.03%-3.96% and the mean IoU by 2.13%-5.44% compared with other semantic segmentation methods (i.e. Fully Connected Network, U-Net and Fully Connected DenseNet). In addition, we exhibited the results of the oil palm plantation mapping in large-scale areas (around 320 km<sup>2</sup>) and demonstrated the effectiveness of our method for large-scale mapping.", "Keywords": "", "DOI": "10.1080/********.2019.1681604", "PubYear": 2020, "Volume": "41", "Issue": "5", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing, China;Joint Center for Global Change Studies (JCGCS), Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing, China;Joint Center for Global Change Studies (JCGCS), Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing, China;Joint Center for Global Change Studies (JCGCS), Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing, China;Joint Center for Global Change Studies (JCGCS), Beijing, China;Department of Computer Science and Technology, Tsinghua University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing, China;Joint Center for Global Change Studies (JCGCS), Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing, China;Joint Center for Global Change Studies (JCGCS), Beijing, China"}, {"AuthorId": 7, "Name": "Maocai Xia", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing, China;Joint Center for Global Change Studies (JCGCS), Beijing, China"}], "References": []}, {"ArticleId": 78191624, "Title": "Assessing the contributions of climate change and human activities to cropland productivity by means of remote sensing", "Abstract": "It is essential for quantitatively assessing the influences of climate change (CC) and human activities (HA) on cropland productivity to clarify the associated drive mechanisms. However, few studies have quantitatively evaluated their relative contributions to cropland productivity in China. In this study, net primary productivity (NPP) was chosen as an indicator of cropland productivity. The actual NPP (ANPP) represents the combined effects of CC and HA on cropland productivity, and the potential NPP (PNPP) represents the effect of CC on cropland productivity. The difference between the actual and the potential NPP (HNPP) represents the effect of HA on cropland productivity. Meanwhile, we designed six scenarios to quantify the contributions of CC and HA to the inter-annual variation in ANPP. Our results showed that cropland ANPP in China as a whole increased at a rate of 3.68 g C m<sup>−2</sup> year<sup>−1</sup> (<i>p</i> < 0.01) between 2000 and 2015. The contributions of CC and HA to the variations in cropland ANPP were 3.23 g C m<sup>−2</sup> year<sup>−1</sup> (<i>p</i> < 0.01) and 0.45 g C m<sup>−2</sup> year<sup>−1</sup> (<i>p</i> > 0.1), respectively. Furthermore, we found that 79.43% of total cropland area showed an inter-annual increase in ANPP, in which 56.22% of cropland area with increase in ANPP was predominantly influenced by CC, compared to 43.78% predominantly influenced by HA. Conversely, 20.57% of total cropland area showed an inter-annual decrease in ANPP, with 70.62% of cropland area with decrease in ANPP being predominantly influenced by HA compared with only 29.38% predominantly influenced by CC. Overall, CC was the dominant factor affecting increase in cropland ANPP, whereas HA was the dominant factor affecting decrease in cropland ANPP. We conclude that effective measures for protecting cropland resources need to be further strengthened, despite CC primarily promoting cropland ANPP.", "Keywords": "", "DOI": "10.1080/********.2019.1681603", "PubYear": 2020, "Volume": "41", "Issue": "5", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Key Laboratory for Urbanization and Geo-simulation, School of Geography and Planning, Sun Yat-sen University, Guangzhou, Guangdong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Key Laboratory for Urbanization and Geo-simulation, School of Geography and Planning, Sun Yat-sen University, Guangzhou, Guangdong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Key Laboratory for Urbanization and Geo-simulation, School of Geography and Planning, Sun Yat-sen University, Guangzhou, Guangdong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MEP, South China Institute of Environmental Science, Guangzhou, China"}, {"AuthorId": 5, "Name": "Jinpei Ou", "Affiliation": "Guangdong Key Laboratory for Urbanization and Geo-simulation, School of Geography and Planning, Sun Yat-sen University, Guangzhou, Guangdong, China"}], "References": []}, {"ArticleId": 78192067, "Title": "A nitrogen spectral response model and nitrogen estimation of summer maize during the entire growth period", "Abstract": "Timely and effective prediction of nitrogen content in summer maize could provide support data for precise fertilization. In this study, the feasibility and expansibility of predicting the nitrogen mechanism model of summer maize leaves through its entire growth period were investigated on the basis of the theory of leaf radiation transmission mechanism. A complete random test of data from two maize varieties and two nitrogen fertilizer applications in 2017 was conducted. Three versions of the leaf optical PROperties SPECTra (PROSPECT) model, namely, PROSPECT-4, PROSPECT-5, and PROSPECT-D were used to link the established leaf nitrogen density (LND) and chlorophyll-<i>a + b</i> (chl-<i>a + b</i>) models, that is, chl-<i>a + b</i>-LND model. A nitrogen response transfer model (N-RTM) was established by linking the optimal PROSPECT and chl-<i>a + b</i>-LND models. Results were as follows. (1) chl-<i>a + b</i> estimation using the PROSPECT-D model yielded the highest accuracy (the coefficient of determination (<i>R</i><sup>2</sup>) = 0.774, the normalized root mean squared error (nRMSE) = 13.19%) among the three PROSPECT models, it shows that the model considering more factors can better reflect the internal law of blade, and could be used as the basic model of N-RTM; (2) Established chl-<i>a + b</i>-LND models based on the dataset from each growth stage showed differences using the confidence interval method, and the <i>R</i><sup>2</sup> values of the optimal regression model at V12, VT, and R3 were 0.794, 0.781, and 0.821, respectively. Based on the changes of chl-<i>a + b</i> and LND during the growth period, a piecewise model was constructed; (3) The <i>R</i><sup>2</sup> and nRMSE values between the measured and estimated LNDs were 0.656% and 22.86%, respectively. The validation results are better than the traditional empirical model. The results showed that the segmented model, which considered the interaction of various factors within the leaves and the change of chl-<i>a + b</i>-LND during the growth period, had better performance in nitrogen monitoring. The constructed nitrogen model in this study preliminarily realized the remote sensing prediction of the nitrogen mechanism model and had a certain mechanism.", "Keywords": "", "DOI": "10.1080/********.2019.1677967", "PubYear": 2020, "Volume": "41", "Issue": "5", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Quantitative Remote Sensing in Agriculture of Ministry of Agriculture and Rural Affairs P. R. China, Beijing Research Center for Information Technology in Agriculture, Beijing, China;College of Geomatics, Shandong University of Science and Technology, Qingdao, Shandong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Geomatics, Shandong University of Science and Technology, Qingdao, Shandong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Quantitative Remote Sensing in Agriculture of Ministry of Agriculture and Rural Affairs P. R. China, Beijing Research Center for Information Technology in Agriculture, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Quantitative Remote Sensing in Agriculture of Ministry of Agriculture and Rural Affairs P. R. China, Beijing Research Center for Information Technology in Agriculture, Beijing, China;College of Geomatics, Shandong University of Science and Technology, Qingdao, Shandong, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Quantitative Remote Sensing in Agriculture of Ministry of Agriculture and Rural Affairs P. R. China, Beijing Research Center for Information Technology in Agriculture, Beijing, China"}], "References": []}, {"ArticleId": 78192095, "Title": "Special issue on real‐time behavioral monitoring in IoT applications using big data analytics", "Abstract": "", "Keywords": "", "DOI": "10.1002/cpe.5529", "PubYear": 2021, "Volume": "33", "Issue": "4", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Incheon National University, Incheon, South Korea; <PERSON><PERSON><PERSON><PERSON>, Incheon National University, Incheon 22012, South Korea."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université du Québec à Chicoutimi, Chicoutimi, Quebec, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Naples Federico II, Naples, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Kyungpook National University, Daegu, South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Manchester Metropolitan University, Manchester, UK"}], "References": []}, {"ArticleId": 78192098, "Title": "BoVSG: bag of visual SubGraphs for remote sensing scene classification", "Abstract": "Remote sensing scene classification is gaining much more interest in the recent few years for many strategic fields such as security, land cover and land use monitoring. Several methods have been proposed in the literature and they can be divided into three main classes based on the features used: handcrafted features, features obtained by unsupervised learning and those obtained from deep learning. Handcrafted features are generally time consuming and suboptimal. Unsupervised learning based features which have been proposed later gave better results but their performances are still limited because they mainly rely on shallow networks and are not able to extract powerful features. Deep learning based features are recently investigated and gave interesting results. But, they cannot be usually used because of the scarcity of labelled remote sensing images and are also computationally expensive. Most importantly, whatever kind of feature is used, the neighbourhood information of them is ignored. In this paper, we propose a novel remote sensing scene representation and classification approach called Bag of Visual SubGraphs (BoVSG). First, each image is segmented into superpixels in order to summarize the image content while retaining relevant information. Then, the superpixels from all images are clustered according to their colour and texture features and a random label is assigned to each cluster that probably corresponds to some material or land cover type. Thus superpixels belonging to the same cluster have the same label. Afterwards, each image is modelled with a graph where nodes correspond to labelled superpixels and edges model spatial neighbourhoods. Finally, each image is represented by a histogram of the most frequent subgraphs corresponding to land cover adjacency patterns. This way, local spatial relations between the nodes are also taken into account. Resultant feature vectors are classified using standard classification algorithms. The proposed approach is tested on three popular datasets and its performance outperforms state-of-the-art methods, including deep learning methods. Besides its accuracy, the proposed approach is computationally much less expensive than deep learning methods.", "Keywords": "", "DOI": "10.1080/********.2019.1681602", "PubYear": 2020, "Volume": "41", "Issue": "5", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ISAMM, RIADI LR99ES26, University of Manouba, Manouba, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ISAMM, RIADI LR99ES26, University of Manouba, Manouba, Tunisia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Geodetic and Geological Information Technologies Department, Middle East Technical University, Ankara, Turkey"}], "References": []}, {"ArticleId": 78193313, "Title": "New protocols of cognitive data management and sharing in cloud computing", "Abstract": "<p>In this paper, we shall discuss new classes of cognitive systems dedicated the tasks of dividing and managing shared information not only in simple horizontal structures but also primarily in vertical structures. An example of such a structure is a computer cloud where, from various levels of access to information, one can execute the management process, both with respect to the entire information resources, as well as to individual pieces of data, including shared information. A special type of information is a service, which, similar to various types of data, is subject to the concealment process. In this work, we shall present universal algorithms for shared data management, such that can be used in concealment protocols both for data and services. Due to the above, this paper will treat the term “service” as a special type of data, constituting the basis of their sharing and management processes. Moreover, we shall determine the impact of the proposed algorithms on the enhancement of data/service management processes in Cloud Computing as well as the impact of the service management and data sharing processes in Could Computing on decision‐taking processes in systems supporting the management processes.</p>", "Keywords": "cloud computing;cognitive data management;cognitive data sharing", "DOI": "10.1002/cpe.5546", "PubYear": 2020, "Volume": "32", "Issue": "18", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Cryptography and Cognitive Informatics, Pedagogical University of Krakow, Kraków, Poland; Lidia Ogiela, Department of Cryptography and Cognitive Informatics, Pedagogical University of Krakow, Podchorążych 2 Street, 30‐084 Kraków, Poland."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Advanced Sciences, Hosei University, Tokyo, Japan"}], "References": [{"Title": "Cognitive and innovative computation paradigms for big data and cloud computing applications", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "8", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 78194135, "Title": "An evaluation method of scientific research team influence based on heterogeneity and node similarity of content and structure", "Abstract": "<p>Science research has general rules of development, is like any other social activity. With the improvement of science and technology, scientific problems have become more complex and systematic, individual approach has been replaced by teamwork in scientific research. This paper takes scientific research team cooperative network as research object, analyzes the influence of scientific research teams in the cooperative network from the aspect of node heterogeneity and node similarity of content and structure, and puts forward the influence evaluation method of scientific research team. A scientific research team cooperation network is constructed as the unweighted and undirected graph by the cooperation relationship data of scientific research teams, including co-author, citation, project cooperation and son on. In this network, the scientific research teams are take nodes, and the cooperative relationships between scientific research teams are take as edges. The major factors of scientific research team influence are analyzed, including node heterogeneity and relationship strength between nodes, then a weight and attributed graph is constructed by the research direction of scientific research team and is weighted based on the similarity of nodes’ content and structure by the SimRank model and the Jaccard similarity method. An influence evaluation method was proposed based on the impact of node subjective heterogeneity and node domain heterogeneity, and An influence spread model based on SIR model was given for verifying the proposed influence evaluation method.</p>", "Keywords": "Influence evaluation; Scientific research team; Cooperation network; Heterogeneity; Node similarity", "DOI": "10.1007/s12652-019-01547-0", "PubYear": 2020, "Volume": "11", "Issue": "9", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Shijiazhuang Tiedao University, Shijiazhuang, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Shijiazhuang Tiedao University, Shijiazhuang, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "@qq.com;School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "@qq.com;School of Information Science and Technology, Shijiazhuang Tiedao University, Shijiazhuang, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "@qq.com;Institute of Scientific and Technical Information of Heibei Province, Shijiazhuang, China"}], "References": []}, {"ArticleId": 78194272, "Title": "Hybrid and hierarchical fusion networks: a deep cross-modal learning architecture for action recognition", "Abstract": "<p>Two-stream networks have provided an alternate way of exploiting the spatiotemporal information for action recognition problem. Nevertheless, most of the two-stream variants perform the fusion of homogeneous modalities which cannot efficiently capture the action-motion dynamics from the videos. Moreover, the existing studies cannot extend the streams beyond the number of modalities. To address these limitations, we propose a hybrid and hierarchical fusion (HHF) networks. The hybrid fusion handles non-homogeneous modalities and introduces a cross-modal learning stream for effective modeling of motion dynamics while extending the networks from existing two-stream variants to three and six streams. On the other hand, the hierarchical fusion makes the modalities consistent by modeling long-term temporal information along with the combination of multiple streams to improve the recognition performance. The proposed network architecture comprises of three fusion tiers: the hybrid fusion itself, the long-term fusion pooling layer which models the long-term dynamics from RGB and optical flow modalities, and the adaptive weighting scheme for combining the classification scores from several streams. We show that the hybrid fusion has different representations from the base modalities for training the cross-modal learning stream. We have conducted extensive experiments and shown that the proposed six-stream HHF network outperforms the existing two- and four-stream networks, achieving the state-of-the-art recognition performance, 97.2% and 76.7% accuracies on UCF101 and HMDB51 datasets, respectively, which are widely used in action recognition studies.</p>", "Keywords": "Action recognition; Deep architectures; Inception-ResNets; Video representations; Non-homogeneous fusion", "DOI": "10.1007/s00521-019-04578-y", "PubYear": 2020, "Volume": "32", "Issue": "14", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Management Engineering, Hankuk University of Foreign Studies, Global Campus, Yongin-si, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Management Engineering, Hankuk University of Foreign Studies, Global Campus, Yongin-si, South Korea"}], "References": []}, {"ArticleId": 78194567, "Title": "Leveraging Viscous Peeling to Create and Activate Soft Actuators and Microfluidic Devices", "Abstract": "<p>The research fields of microfluidics and soft robotics both involve complex small-scale internal channel networks, embedded within a solid structure. This study examines leveraging viscous peeling as a mechanism to create and activate soft actuators and microchannel networks, including complex elements such as valves, without the need for fabrication of structures with micron-scale internal cavities. We consider configurations composed of an internal slender structure embedded within another elastic solid. Pressurized viscous fluid is introduced into the interface between the two solids, thus peeling the two elastic structures and creating internal cavities. Since the gap between the solids is determined by the externally applied pressure, the characteristic size of the fluid network may vary with time and be much smaller than the resolution of the fabrication method. This study presents a model for the highly nonlinear elastic-viscous dynamics governing the flow and deformation of such configurations. Fabrication and experimental demonstrations of micron-scale valves and channel networks created from millimeter scale structures are presented, as well as the transient dynamics of viscous peeling-based soft actuators. The experimental data are compared with the suggested model, showing very good agreement.</p>", "Keywords": "fluid–structure interaction;microfluidics;soft robotics;viscous flow", "DOI": "10.1089/soro.2019.0005", "PubYear": 2020, "Volume": "7", "Issue": "1", "JournalId": 12246, "JournalTitle": "Soft Robotics", "ISSN": "2169-5172", "EISSN": "2169-5180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Technion Autonomous Systems Program, Technion—Israel Institute of Technology, Haifa, Israel."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Mechanical Engineering, Technion—Israel Institute of Technology, Haifa, Israel."}, {"AuthorId": 3, "Name": "Yizhar Or", "Affiliation": "Technion Autonomous Systems Program, Technion—Israel Institute of Technology, Haifa, Israel.;Faculty of Mechanical Engineering, Technion—Israel Institute of Technology, Haifa, Israel."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Technion Autonomous Systems Program, Technion—Israel Institute of Technology, Haifa, Israel.;Faculty of Mechanical Engineering, Technion—Israel Institute of Technology, Haifa, Israel."}], "References": []}, {"ArticleId": 78194614, "Title": "A general generative adversarial capsule network for hyperspectral image spectral-spatial classification", "Abstract": "A novel generative adversarial capsule network (Caps-GAN) model for hyperspectral image spectral-spatial classification is proposed in this Letter, which can effectively solve the scarce availability problem of annotated samples and improve classification performance. In the proposed method, a series of deconvolutional layers are utilized to generate fake samples as real as training samples with additional label information and 3D capsule network (CapsNet) is designed to discriminate the inputs, which can achieve higher classification performance than convolutional neural networks (CNNs) by considering spatial relationships in images. Furthermore, the generated samples with labels and training samples are put into discriminator for joint training, and the trained discriminator can determine the authenticity of input sample as well as the class label. This auxiliary conditional generative adversarial training strategy can effectively improve the generalization capability of the capsule network when labelled samples are limited. The Pavia University and Indian Pines images are used to evaluate the classification performance, and the overall accuracies of proposed method for these two datasets achieve <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/trsl20/2020/trsl20.v011.i01/2150704x.2019.1681598/20191114/images/trsl_a_1681598_ilm0001.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/trsl20/2020/trsl20.v011.i01/2150704x.2019.1681598/20191114/images/trsl_a_1681598_ilm0001.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> 99.23 % and <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/trsl20/2020/trsl20.v011.i01/2150704x.2019.1681598/20191114/images/trsl_a_1681598_ilm0002.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/trsl20/2020/trsl20.v011.i01/2150704x.2019.1681598/20191114/images/trsl_a_1681598_ilm0002.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> 99.12 % , respectively. The comparative experimental results reveal that the proposed model can improve the classification accuracy and provide competitive results compared with state-of-the-art methods, especially when there are few annotated samples.", "Keywords": "", "DOI": "10.1080/2150704X.2019.1681598", "PubYear": 2020, "Volume": "11", "Issue": "1", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information Engineering University, Zhengzhou, China"}], "References": []}, {"ArticleId": 78194896, "Title": "Parameter uncertainty estimation of transient storage model using Bayesian inference with formal likelihood based on breakthrough curve segmentation", "Abstract": "The transient storage model (TSM) for the analysis of pollutant mixing in rivers has been hampered by parameter uncertainties due to the equifinality problem. The generalized uncertainty estimation method, which was frequently used to quantify the parameter uncertainty of TSM, has been criticized because this method uses informal likelihood which can cause overestimation of the uncertainty. Thus, in this study, we suggest a Bayesian inference method using a segment mixture (SM) likelihood, which is a formal likelihood based on the mixture distribution of the segmented breakthrough curve. The uncertainty estimation was conducted using three synthetic data and the real tracer test data achieved from Uvas Creek in the USA. The results show that the SM likelihood estimated uncertainties of TSM appropriately by correctly representing the error distribution of the TSM and identifying the behavioral parameters.", "Keywords": "Transient storage model ; Parameter uncertainty estimation ; Bayesian inference ; Formal likelihood ; GLUE ; Breakthrough curve segmentation", "DOI": "10.1016/j.envsoft.2019.104558", "PubYear": 2020, "Volume": "123", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of Civil and Environmental Engineering, Seoul National University, 1 Gwanak-ro, Gwanak-gu, Seoul, 08826, South Korea"}, {"AuthorId": 2, "Name": "Il Won Seo", "Affiliation": "Dept. of Civil and Environmental Engineering, Seoul National University, 1 Gwanak-ro, Gwanak-gu, Seoul, 08826, South Korea;Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON>", "Affiliation": "Dept. of Civil and Environmental Engineering, Seoul National University, 1 Gwanak-ro, Gwanak-gu, Seoul, 08826, South Korea"}], "References": []}, {"ArticleId": 78195417, "Title": "Generating word and document matrix representations for document classification", "Abstract": "<p>We present an effective word and document matrix representation architecture based on a linear operation, referred to as doc2matrix, to learn representations for document-level classification. It uses a matrix to present each word or document, which is different from the traditional form of vector representation. Doc2matrix defines proper subwindows as the scale of text. A word matrix and a document matrix are generated by stacking the information of these subwindows. Our document matrix not only contains more fine-grained semantic and syntactic information than the original representation but also introduces abundant two-dimensional features. Experiments conducted on four document-level classification tasks demonstrate that the proposed architecture can generate higher-quality word and document representations and outperform previous models based on linear operations. We can see that compared to different classifiers, a convolutional-based classifier is more suitable for our document matrix. Furthermore, we also demonstrate that the convolution operation can better capture the two-dimensional features of the proposed document matrix by the analysis from both theoretical and experimental perspectives.</p>", "Keywords": "Document-level classification; Word matrix; Document matrix; Subwindows", "DOI": "10.1007/s00521-019-04541-x", "PubYear": 2020, "Volume": "32", "Issue": "14", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Dalian University of Technology, Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Dalian University of Technology, Dalian, China"}], "References": []}, {"ArticleId": 78195419, "Title": "A new algorithm for normal and large-scale optimization problems: Nomadic People Optimizer", "Abstract": "<p>Metaheuristic algorithms have received much attention recently for solving different optimization and engineering problems. Most of these methods were inspired by nature or the behavior of certain swarms, such as birds, ants, bees, or even bats, while others were inspired by a specific social behavior such as colonies, or political ideologies. These algorithms faced an important issue, which is the balancing between the global search (exploration) and local search (exploitation) capabilities. In this research, a novel swarm-based metaheuristic algorithm which depends on the behavior of nomadic people was developed, it is called “Nomadic People Optimizer (NPO)”. The proposed algorithm simulates the nature of these people in their movement and searches for sources of life (such as water or grass for grazing), and how they have lived hundreds of years, continuously migrating to the most comfortable and suitable places to live. The algorithm was primarily designed based on the multi-swarm approach, consisting of several clans and each clan looking for the best place, in other words, for the best solution depending on the position of their leader. The algorithm is validated based on 36 unconstrained benchmark functions. For the comparison purpose, six well-established nature-inspired algorithms are performed for evaluating the robustness of NPO algorithm. The proposed and the benchmark algorithms are tested for large-scale optimization problems which are associated with high-dimensional variability. The attained results demonstrated a remarkable solution for the NPO algorithm. In addition, the achieved results evidenced the potential high convergence, lower iterations, and less time-consuming required for finding the current best solution.</p>", "Keywords": "Nature-inspired algorithm; Metaheuristics; Nomadic People Optimizer; Benchmark test functions", "DOI": "10.1007/s00521-019-04575-1", "PubYear": 2020, "Volume": "32", "Issue": "14", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Research and Development, Duy Tan University, Da Nang, Vietnam;College of Computer Science and Information Technology, University of Anbar, Ramadi, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing, College of Computing and Applied Sciences, University Malaysia Pahang, Gambang, Malaysia"}], "References": [{"Title": "Shear strength of SFRCB without stirrups simulation: implementation of hybrid artificial intelligence model", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "1", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 78195872, "Title": "CompactNets: Compact Hierarchical Compositional Networks for Visual Recognition", "Abstract": "CNN-based models currently provide state-of-the-art performance in image categorization tasks. While these methods are powerful in terms of representational capacity, they are generally not conceived with explicit means to control complexity. This might lead to scenarios where resources are used in a non-optimal manner, increasing the number of unspecialized or repeated neurons, and overfitting to data. In this work we propose CompactNets, a new approach to visual recognition that learns a hierarchy of shared, discriminative, specialized, and compact representations. CompactNets naturally capture the notion of compositional compactness , a characterization of complexity in compositional models, consisting on using the smallest number of patterns to build a suitable visual representation. We employ a structural regularizer with group-sparse terms in the objective function, that induces on each layer, an efficient and effective use of elements from the layer below. In particular, this allows groups of top-level features to be specialized based on category information. We evaluate CompactNets on the ILSVRC12 dataset, obtaining compact representations and competitive performance, using an order of magnitude less parameters than common CNN-based approaches. We show that CompactNets are able to outperform other group-sparse-based approaches, in terms of performance and compactness. Finally, transfer-learning experiments on small-scale datasets demonstrate high generalization power, providing remarkable categorization performance with respect to alternative approaches.", "Keywords": "Deep learning ; Regularization ; Group sparsity ; Image categorization", "DOI": "10.1016/j.cviu.2019.102841", "PubYear": 2020, "Volume": "191", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Pontificia Universidad Católica de Chile, Vicuña Mackenna 4860, Santiago 7820436, Chile;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The Johns Hopkins University, 3400 N. Charles Street, Baltimore, MD 21218, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Pontificia Universidad Católica de Chile, Vicuña Mackenna 4860, Santiago 7820436, Chile"}], "References": []}, {"ArticleId": 78196682, "Title": "Two new methods for decision-making with incomplete reciprocal fuzzy preference relations based on additive consistency", "Abstract": "In this paper, we propose two new methods to solve the problem of incomplete reciprocal fuzzy preference relations. Sometimes decision-maker(s) might not be able to provide complete information about their preferences on the alternatives. In general, an incomplete preference relation can be completed if at least a set of <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tjms20/0/tjms20.ahead-of-print/02286203.2019.1661336/20191027/images/tjms_a_1661336_ilm0001.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tjms20/0/tjms20.ahead-of-print/02286203.2019.1661336/20191027/images/tjms_a_1661336_ilm0001.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> n − 1 nonleading diagonal preference values are known and each one of the alternatives is compared directly or indirectly at least once. However, this is not always the case. Sometimes, the decision-maker(s) might not be able to provide any information with regards to at least one of the alternatives, which is called the ignorance situation. We propose two new methods based on additive consistency for solving these two problems in a multi-attributes/group decision-making environment. The first method is based on a system of equations, which is suitable for estimating missing information in the general case. The second method, which is an extension of the system of equations, utilizes a goal programming model to address the ignorance situations. Our validation of these two methods shows that the proposed methods generate a high consistency level irrespective of the nature of the problem under study. The proposed methods outperform other comparable methods without a need to modify or change the original decision-maker(s) preferences.", "Keywords": "Decision making ; reciprocal fuzzy preference ; multiple criteria analysis ; incomplete relations ; ignorance situation", "DOI": "10.1080/02286203.2019.1661336", "PubYear": 2021, "Volume": "41", "Issue": "1", "JournalId": 4075, "JournalTitle": "International Journal of Modelling and Simulation", "ISSN": "0228-6203", "EISSN": "1925-7082", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering and Computer Sciences, Concordia University, Montréal, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering and Computer Sciences, Concordia University, Montréal, Canada"}], "References": []}, {"ArticleId": 78196704, "Title": "SECUR-AMA: Active Malware Analysis Based on Monte Carlo Tree Search for Android Systems", "Abstract": "We propose SECUR-AMA, an Active Malware Analysis (AMA) framework for Android. (AMA) is a technique that aims at acquiring knowledge about target applications by executing actions on the system that trigger responses from the targets. The main strength of this approach is the capability of extracting behaviors that would otherwise remain invisible. A key difference from other analysis techniques is that the triggering actions are not selected randomly or sequentially, but following strategies that aim at maximizing the information acquired about the behavior of the target application. Specifically, we design SECUR-AMA as a framework implementing a stochastic game between two agents: an analyzer and a target application. The strategy of the analyzer consists in a reinforcement learning algorithm based on Monte Carlo Tree Search (MCTS) to efficiently search the state and action spaces taking into account previous interactions in order to obtain more information on the target. The target model instead is created online while playing the game, using the information acquired so far by the analyzer and using it to guide the remainder of the analysis in an iterative process. We conduct an extensive evaluation of SECUR-AMA analyzing about 1200 real Android malware divided into 24 families (classes) from a publicly available dataset, and we compare our approach with multiple state-of-the-art techniques of different types, including passive and active approaches. Results show that SECUR-AMA creates more informative models that allow to reach better classification results for most of the malware families in our dataset.", "Keywords": "Active malware analysis ; Reinforcement learning ; Monte Carlo tree search", "DOI": "10.1016/j.engappai.2019.103303", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Verona, Department of Computer Science, Strada le Grazie 15, 37134 Verona, Italy;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Verona, Department of Computer Science, Strada le Grazie 15, 37134 Verona, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Verona, Department of Computer Science, Strada le Grazie 15, 37134 Verona, Italy"}], "References": []}, {"ArticleId": 78197321, "Title": "Financial hedging in energy market by cross-learning machines", "Abstract": "<p>The recent price volatility in the energy market highlights the importance of financial hedging and the need of its incorporation into an investor’s set of portfolio strategies. Realizing the rapid advancement of artificial intelligence, our study empirically examines the use of machine learning models for hedging price risk associated with the holding of energy-based financial product. From a technical perspective, kernel regression and support vector machine are trained to estimate the time-varying optimal hedge ratio given the observed price movement and other factors. The estimated hedge ratio is then employed to guide the price hedging strategy of the crude oil contracts traded in the commodity exchanges. The two machine approaches’ hedging effectiveness against price risk is also compared with those of the un-hedged portfolio as well as a well-studied econometric time series approach. Our results indicate that the two forms of learning machines substantially outperform time series model and no-hedging over the out-of-sample period but neither machine dominates another across all testing scenarios. Given these mixed results between kernel regression and support vector machine, we propose and develop a kernel-supervised support vector machine to take advantage of the complementary nature of the two machine learning approaches and enhance the supervised learning process through hierarchical/sequential infusion of information. Cross-learning empirical testing shows that the proposed cross-learning machine is more effective in hedging than individual kernel regression and support vector machine. Furthermore, our study evaluates the impact of incorporating the coefficient of absolute risk aversion and the transaction costs to machine learning models. In general, cross-learning machine outperforms others in all tested scenarios.</p>", "Keywords": "Energy financial market; Time-varying commodity hedging; Machine learning; Cross-training algorithm; Kernel regression; Support vector machine", "DOI": "10.1007/s00521-019-04572-4", "PubYear": 2020, "Volume": "32", "Issue": "14", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Finance, National Chung Cheng University, Chiayi, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Management Science and Statistics, University of Texas at San Antonio, San Antonio, USA"}, {"AuthorId": 3, "Name": "Shaotao Pan", "Affiliation": "Data Product Development, Austin, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ministry of Justice Investigation Bureau, Taipei, Taiwan"}], "References": []}, {"ArticleId": 78197429, "Title": "Forest signal detection for photon counting LiDAR using Random Forest", "Abstract": "ICESat (The Ice, Cloud, and Land Elevation Satellite)-2, as the new generation of NASA (National Aeronautics and Space Administration)’s ICESat mission, had been successfully launched in September 2018. The sensor onboard the satellite is a newly designed photon counting LiDAR (Light Detection And Ranging) system for the first time used in space. From the currently released airborne simulation data, it can be seen that there exist numerous noise photons scattering from the atmosphere to even below the ground, especially for the vegetation areas. Therefore, relevant research on methods to distinguish the signal photons effectively is crucial for further forestry applications. In this paper, a machine learning based approach was proposed to detect the potential signal photons from 14 MATLAS datasets across 3 sites in the USA. We found that k-NN (k-Nearest Neighbour) distance and the reachability of the photon towards the nearby signal centre showed good stability and contributed to a robust model establishment. The relevant quantitative assessment demonstrated that the machine learning approach could achieve high detection accuracy over 85% based on a very limited number of samples even in rough terrain conditions. Further analysis proved the potential of model transferability across different sites.", "Keywords": "", "DOI": "10.1080/2150704X.2019.1682708", "PubYear": 2020, "Volume": "11", "Issue": "1", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Digital Earth Science, Institute of Remote Sensing and Digital Earth, Chinese Academy of Sciences, Beijing, China;Institute of Forest Resource Information Techniques, Chinese Academy of Forestry, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Forest Resource Information Techniques, Chinese Academy of Forestry, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Forest Resource Information Techniques, Chinese Academy of Forestry, Beijing, China"}, {"AuthorId": 4, "Name": "Hao Lu", "Affiliation": "School of Information Science and Technology, Beijing Forestry University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Global Environmental Modelling and Earth Observation (GEMEO), Department of Geography, Swansea University, Swansea, UK"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Global Environmental Modelling and Earth Observation (GEMEO), Department of Geography, Swansea University, Swansea, UK"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Digital Earth Science, Institute of Remote Sensing and Digital Earth, Chinese Academy of Sciences, Beijing, China"}], "References": []}, {"ArticleId": 78197631, "Title": "Design of resistive random access memory cell and its architecture", "Abstract": "<p>This paper provides an insight into an alternative technology which makes use of new circuit element called memristor which can be scaled down to advanced technology generation. Power gating technique cannot be used on generally used volatile memories to reduce power dissipation. To overcome this problem nonvolatile memories are required. The nonvolatile memory bitcell used in this article is suitable candidate to replace the generally used memory bitcell like volatile 6T-SRAM bitcell. This article proposes 2T1M (two transistor and one memristor)-based bitcell for data storage. Its operations such as read and write are discussed and the results obtained during various simulations are thoroughly explained. 2T1M-based RRAM bitcell is designed along with write driver and read circuitry. Read time and write time of the proposed RRAM cell are observed by varying supply voltage. An 8 × 8 RRAM architecture based on the proposed bitcell has been also designed. All the simulations are done on the SPICE to show the robustness of proposed 2T1M-based bitcell.</p>", "Keywords": "", "DOI": "10.1007/s00542-019-04663-1", "PubYear": 2020, "Volume": "26", "Issue": "4", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, BIT, Mesra, Ranchi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, BIT, Mesra, Ranchi, India"}], "References": []}, {"ArticleId": 78197822, "Title": "Layered non-photorealistic rendering with anisotropic depth-of-field filtering", "Abstract": "<p>In this paper, we provide a layered non-photorealistic rendering (NPR) technique that automatically extracts the depth of field (DoF) shown in the picture and adjusts the degree of abstraction accordingly. We use an RGB channel to efficiently classify the DoF region anisotropically. Based on the DoF values, we abstract the color and adjust the thickness of the line. We use anisotropic DoF-based filtering to improve the abstraction quality by finding the blur region using cross-correlation filtering and anisotropically calculating the weight map. Our approach has greatly improved the quality of abstraction in terms of performance and design. The algorithm is also fast and simple to implement. Experimental results show well the characteristics and style of the DoF of the original photograph.</p>", "Keywords": "Anisotropic depth of field; Image abstraction; Non-photorealistic rendering; Image processing", "DOI": "10.1007/s11042-019-08387-2", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kangnam University, Yongin-si, South Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hallym University, Chuncheon, South Korea"}], "References": []}, {"ArticleId": 78198197, "Title": "Bi-directional LSTM Model with Symptoms-Frequency Position Attention for Question Answering System in Medical Domain", "Abstract": "<p>Online medical intelligent question answering system plays an increasingly important role as a supplement of the traditional medical service systems. The purpose is to provide quick and concise feedback on users’ questions through natural language. The technical challenges mainly lie in symptom semantic understanding and representation of users’ description. Although the performance of phrase-level and numerous attention models have been improved, the lexical gap and position information are not emphasized enough. This paper combines word2vec and the Chinese Ci-Lin [it is a dictionary that plays an auxiliary role in word2vec where processing Chinese ( https://www.ltp-cloud.com/download )] to propose synonyms-subject replacement mechanism (i.e., map common words as kernel words) and realize the normalization of the semantic representation; Meanwhile, based on the bi-directional LSTM model, this paper introduces a method of the combination of adaptive weight assignment techniques and positional context, enhancing attention to the typical symptoms of the disease. More attention weight is given to the neighboring words and propose the Bi-directional Long Short Term Memory Model with Symptoms-Frequency Position Attention (BLSTM-SFPA). The good performance of the BLSTM-SFPA model has been demonstrated in comparative experiments on the medical field dataset (MED-QA and GD-QA). </p>", "Keywords": "Question answering; BLSTM model; Answer selection; Semantic positional-based attention", "DOI": "10.1007/s11063-019-10136-3", "PubYear": 2020, "Volume": "51", "Issue": "2", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Agri-Product Quality Traceability, Beijing Technology and Business University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Agri-Product Quality Traceability, Beijing Technology and Business University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Engineering Laboratory for Agri-Product Quality Traceability, Beijing Technology and Business University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Agri-Product Quality Traceability, Beijing Technology and Business University, Beijing, China"}, {"AuthorId": 5, "Name": "Qingyu Jin", "Affiliation": "National Engineering Laboratory for Agri-Product Quality Traceability, Beijing Technology and Business University, Beijing, China"}], "References": []}, {"ArticleId": 78198209, "Title": "Control of weld penetration depth using relative fluctuation coefficient as feedback", "Abstract": "<p>The monitoring and control of weld penetration in pulsed gas metal arc welding (GMAW-P) is considerably challenging, especially in field applications. The metal transfer and pulse current in GMAW-P complicate the identification of weld penetration. In previous studies, the authors found that both the change in arc voltage during the peak current period and the average arc voltage during the peak current period can be used for condition monitoring of weld pool surface and thus for the estimation of GMAW-P penetration depth. In the present work, the relative fluctuation coefficient ( C <sub> RF </sub>) of weld pool surface is proposed by combining these two signals to predict the weld penetration depth. Model predictive control using this coefficient as feedback is employed to control the penetration depth. The experimental results show that uniform weld penetration depth can be obtained by the adaptive control algorithm. The practice attempted in this work can be expected to be a candidate solution for GMAW-P penetration control, which is easy to implement in field applications.</p>", "Keywords": "Arc sensing; Weld pool surface; Nonlinear model; Predictive control; Weld penetration; Penetration depth; GMAW-P", "DOI": "10.1007/s10845-019-01506-8", "PubYear": 2020, "Volume": "31", "Issue": "5", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Advanced Joining Technology, School of Materials Science and Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Wang", "Affiliation": "Tianjin Key Laboratory of Advanced Joining Technology, School of Materials Science and Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Advanced Joining Technology, School of Materials Science and Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Advanced Joining Technology, School of Materials Science and Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Advanced Joining Technology, School of Materials Science and Engineering, Tianjin University, Tianjin, China"}], "References": []}, {"ArticleId": 78198630, "Title": "Adaptive Fuzzy Control of Robot Manipulators with Asymptotic Tracking Performance", "Abstract": "Abstract <p>Adaptive fuzzy controllers are powerful intelligent control tools applied to handle the complexity of robot manipulators; however, their tracking performances are considerably affected by the fuzzy approximation error. This paper introduces a decentralized adaptive fuzzy control (AFC) design based on voltage control strategy, equipped with a novel fuzzy approximation error compensator. Asymptotic tracking performance is achieved by means of compensating the fuzzy approximation error adaptively. The proposed design simplifies the complex control procedure of electrically driven robot manipulators, and it is independent from the mathematical model. The asymptotic stability of the proposed controller has been guaranteed and proven via <PERSON>balat’s lemma. The control approach is simulated on a four-link SCARA robot driven by permanent magnet DC motors, and the simulation results reveal the improvement in tracking the desired trajectory, compared to a previous AFC based on voltage control strategy without fuzzy approximation error compensation.</p>", "Keywords": "Adaptive fuzzy control; Electrically driven SCARA robot; Fuzzy approximation error; Stability analysis", "DOI": "10.1007/s40313-019-00496-5", "PubYear": 2020, "Volume": "31", "Issue": "1", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Robotic Engineering, Shahrood University of Technology, Shahrood, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Robotic Engineering, Shahrood University of Technology, Shahrood, Iran"}], "References": []}, {"ArticleId": 78198828, "Title": "Building social networking services systems using the relational shared-nothing parallel DBMS", "Abstract": "We propose methods to enable the relational model to meet scalability and functionality needs of a large-scale social networking services (SNS) system. NewSQL has emerged recently indicating that shared-nothing parallel relational DBMSs can be used to guarantee the ACID properties of transactions while keeping the high scalability of NoSQL. Leading commercial SNS systems, however, rely on a graph – not relational – data model with key–value storage and, for certain operations, suffer overhead of unnecessarily accessing multiple system nodes. Exploiting higher semantics with the relational data model could be the remedy. The solution we offer aims to perform a transaction as a set of independent local transactions whenever possible based on the conceptual semantics of the SNS database schema. First, it hierarchically clusters entities that are sitting on a path of frequently navigated one-to-many relationships, thereby avoiding inter-node joins. Second, when a multi-node delete transaction is performed over many-to-many relationships, it defers deletion of related references until they are accessed later, thereby amortizing the cost of multi-node updates. These solutions have been implemented in Odysseus/SNS — an SNS system using a shared nothing parallel DBMS. Performance evaluation using synthetic workload that reflects the real SNS workload demonstrates significant improvement in processing time. We also note that our work is the first to present the entity-relationship schema and its relational representation of the SNS database.", "Keywords": "", "DOI": "10.1016/j.datak.2019.101756", "PubYear": 2020, "Volume": "125", "Issue": "", "JournalId": 5635, "JournalTitle": "Data & Knowledge Engineering", "ISSN": "0169-023X", "EISSN": "1872-6933", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, KAIST, Daejeon, Korea;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, KAIST, Daejeon, Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, KAIST, Daejeon, Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, KAIST, Daejeon, Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, KAIST, Daejeon, Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, KAIST, Daejeon, Korea"}, {"AuthorId": 7, "Name": "Ilyeop Yi", "Affiliation": "School of Computing, KAIST, Daejeon, Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Vermont, Burlington, VT, USA"}], "References": []}, {"ArticleId": 78199549, "Title": "Judgemental errors in aviation maintenance", "Abstract": "<p>Aircraft maintenance is a critical success factor in the aviation sector, and incorrect maintenance actions themselves can be the cause of accidents. Judgemental errors are the top causal factors of maintenance-related aviation accidents. This study asks why judgemental errors occur in maintenance. Referring to six aviation accidents, we show how various biases contributed to those accidents. We first filtered aviation accident reports, looking for accidents linked to errors in maintenance judgements. We analysed the investigation reports, as well as the relevant interview transcriptions. Then we set the characteristics of the actions behind the accidents within the context of the literature and the taxonomy of reasons for judgemental biases. Our results demonstrate how various biases, such as theory-induced blindness, optimistic bias, and substitution bias misled maintenance technicians and eventually become the main cause of a catastrophe. We also find these biases are interrelated, with one causing another to develop. We discuss how these judgemental errors could relate to loss of situation awareness, and suggest interventions to mitigate them.</p>", "Keywords": "Judgemental error; Heuristics; Aviation maintenance; Situation awareness", "DOI": "10.1007/s10111-019-00609-9", "PubYear": 2020, "Volume": "22", "Issue": "4", "JournalId": 2896, "JournalTitle": "Cognition, Technology & Work", "ISSN": "1435-5558", "EISSN": "1435-5566", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Operations and Maintenance, Luleå University of Technology, Luleå, Sweden"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Division of Operations and Maintenance, Luleå University of Technology, Luleå, Sweden"}], "References": []}, {"ArticleId": 78199833, "Title": "A Weakly Supervised Multi-task Ranking Framework for Actor–Action Semantic Segmentation", "Abstract": "<p>Modeling human behaviors and activity patterns has attracted significant research interest in recent years. In order to accurately model human behaviors, we need to perform fine-grained human activity understanding in videos. Fine-grained activity understanding in videos has attracted considerable recent attention with a shift from action classification to detailed actor and action understanding that provides compelling results for perceptual needs of cutting-edge autonomous systems. However, current methods for detailed understanding of actor and action have significant limitations: they require large amounts of finely labeled data, and they fail to capture any internal relationship among actors and actions. To address these issues, in this paper, we propose a novel Schatten p -norm robust multi-task ranking model for weakly-supervised actor–action segmentation where only video-level tags are given for training samples. Our model is able to share useful information among different actors and actions while learning a ranking matrix to select representative supervoxels for actors and actions respectively. Final segmentation results are generated by a conditional random field that considers various ranking scores for video parts. Extensive experimental results on both the actor–action dataset and the Youtube-objects dataset demonstrate that the proposed approach outperforms the state-of-the-art weakly supervised methods and performs as well as the top-performing fully supervised method.</p>", "Keywords": "Weakly supervised learning; Actor–action semantic segmentation; Multi-task ranking", "DOI": "10.1007/s11263-019-01244-7", "PubYear": 2020, "Volume": "128", "Issue": "5", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "Yan Yan", "Affiliation": "Department of Computer Science, Texas State University, San Marcos, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Rochester, Rochester, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Cell and Developmental Biology, Biophysics, University of Michigan, Ann Arbor, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, University of Michigan, Ann Arbor, USA"}], "References": []}, {"ArticleId": 78200232, "Title": "Data mining algorithms for bridge health monitoring: Kohonen clustering and LSTM prediction approaches", "Abstract": "Abstract \nIn recent years, bridge health monitoring system has been widely used to deal with massive data produced with the continuous growth of monitoring time. However, how to effectively use these data to comprehensively analyze the state of a bridge and provide early warning of bridge structure changes is an important topic in bridge engineering research. This paper utilizes two algorithms to deal with the massive data, namely Kohonen neural network and long short-term memory (LSTM) neural network. The main contribution of this study is using the two algorithms for health state evaluation of bridges. The Kohonen clustering method is shown to be effective for getting classification pattern in normal operating condition and is straightforward for outliers detection. In addition, the LSTM prediction method has an excellent prediction capability which can be used to predict the future deflection values with good accuracy and mean square error. The predicted deflections agree with the true deflections, which indicate that the LSTM method can be utilized to obtain the deflection value of structure. What’s more, we can observe the changing trend of bridge structure by comparing the predicted value with its limit value under normal operation.", "Keywords": "Health monitoring; Structural assessment; Kohonen clustering; Time series; Long short-term memory", "DOI": "10.1007/s11227-019-03045-8", "PubYear": 2020, "Volume": "76", "Issue": "2", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "@qq.com;Hubei Communications Investment Intelligent Detection CO., Ltd, Wuhan, China;School of Civil Engineering and Architecture, Wuhan University of Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "@qq.com;Hubei Communications Investment Intelligent Detection CO., Ltd, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "@qq.com;Hubei Communications Investment Intelligent Detection CO., Ltd, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "@whut.edu.cn;School of Computer Science and Technology, Wuhan University of Technology, Wuhan, China"}], "References": []}, {"ArticleId": 78200262, "Title": "Non-iterative parameter estimation of the 2R-1C model suitable for low-cost embedded hardware", "Abstract": "<p>Parameter estimation of the 2R-1C model is usually performed using iterative methods that require high-performance processing units. Consequently, there is a strong motivation to develop less time-consuming and more power-efficient parameter estimation methods. Such low-complexity algorithms would be suitable for implementation in portable microcontroller-based devices. In this study, we propose the quadratic interpolation non-iterative parameter estimation (QINIPE) method, based on quadratic interpolation of the imaginary part of the measured impedance, which enables more accurate estimation of the characteristic frequency. The 2R-1C model parameters are subsequently calculated from the real and imaginary parts of the measured impedance using a set of closed-form expressions. Comparative analysis conducted on the impedance data of the 2R-1C model obtained in both simulation and measurements shows that the proposed QINIPE method reduces the number of required measurement points by 80% in comparison with our previously reported non-iterative parameter estimation (NIPE) method, while keeping the relative estimation error to less than 1% for all estimated parameters. Both non-iterative methods are implemented on a microcontroller-based device; the estimation accuracy, RAM, flash memory usage, and execution time are monitored. Experiments show that the QINIPE method slightly increases the execution time by 0.576 ms (about 6.7%), and requires 24% (1.2 KB) more flash memory and just 2.4% (32 bytes) more RAM in comparison to the NIPE method. However, the impedance root mean square errors (RMSEs) of the QINIPE method are decreased to 42.8% (for the real part) and 64.5% (for the imaginary part) of the corresponding RMSEs obtained using the NIPE method. Moreover, we compared the QINIPE and the complex nonlinear least squares (CNLS) estimation of the 2R-1C model parameters. The results obtained show that although the estimation accuracy of the QINIPE is somewhat lower than the estimation accuracy of the CNLS, it is still satisfactory for many practical purposes and its execution time reduces to \\({1 \\over {45}} - {1 \\over {30}}\\) .</p>", "Keywords": "2R-1C model; Embedded systems; Parameter estimation; Non-iterative methods; Quadratic interpolation; O231", "DOI": "10.1631/FITEE.1900112", "PubYear": 2020, "Volume": "21", "Issue": "3", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering, University of Banja Luka, Banja Luka, Bosnia and Herzegovina"}, {"AuthorId": 2, "Name": "Zdenka Babić", "Affiliation": "Faculty of Electrical Engineering, University of Banja Luka, Banja Luka, Bosnia and Herzegovina"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Electrical Engineering, University of Banja Luka, Banja Luka, Bosnia and Herzegovina"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Technical Sciences, University of Novi Sad, Novi Sad, Republic of Serbia"}], "References": []}, {"ArticleId": 78200572, "Title": "Iris‐based continuous authentication in mobile ad hoc network", "Abstract": "<p>Mobile Ad hoc Networks (MANETs) are temporary in nature, with security concerns being more vulnerable than the other types of networks. Most of the works concentrate on either authentication or intrusion detection. This framework considers both intrusion detection and authentication continuously. It has been done for different layers of security for finding the intruder and eliminate from the network. Iris images have been used in this work for the purpose of authentication. Detection and response engines have been used for intrusion detection and high security in ad hoc network. The simulation results clearly show the iris-based continuous authentication detecting most of the intruders and eliminates from the network.</p>", "Keywords": "ad hoc network;continuous authentication;intrusion detection and iris", "DOI": "10.1002/cpe.5542", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "SSN College of Engineering, Anna University, Chennai, India; <PERSON><PERSON>, SSN College of Engineering, Anna University, Chennai-603 110, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, SSN College of Engineering, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, SSN College of Engineering, Chennai, India"}], "References": []}, {"ArticleId": ********, "Title": "An ICA-based method for stress classification from voice samples", "Abstract": "<p>Emotion detection is a hot topic nowadays for its potential application to intelligent systems in different fields such as neuromarketing, dialogue systems, friendly robotics, vending platforms and amiable banking. Nevertheless, the lack of a benchmarking standard makes it difficult to compare results produced by different methodologies, which could help the research community improve existing approaches and design new ones. Besides, there is the added problem of accurate dataset production. Most of the emotional speech databases and associated documentation are either privative or not publicly available. Therefore, in this work, two stress-elicited databases containing speech from male and female speakers were recruited, and four classification methods are compared in order to detect and classify speech under stress. Results from each method are presented to show their quality performance, besides the final scores attained, in what is a novel approach to the field of study.</p>", "Keywords": "ICA; PCA; Speech; Stress; Classification", "DOI": "10.1007/s00521-019-04549-3", "PubYear": 2020, "Volume": "32", "Issue": "24", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for Biomedical Technology, Universidad Politécnica de Madrid, Pozuelo de Alarcón, Madrid, Spain;Escuela Técnica Superior de Ingeniería Informática, Universidad Rey Juan Carlos, Móstoles, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center for Biomedical Technology, Universidad Politécnica de Madrid, Pozuelo de Alarcón, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Biomedical Technology, Universidad Politécnica de Madrid, Pozuelo de Alarcón, Madrid, Spain;Escuela Técnica Superior de Ingeniería Informática, Universidad Rey Juan Carlos, Móstoles, Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Center for Biomedical Technology, Universidad Politécnica de Madrid, Pozuelo de Alarcón, Madrid, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Center for Biomedical Technology, Universidad Politécnica de Madrid, Pozuelo de Alarcón, Madrid, Spain"}], "References": []}, {"ArticleId": ********, "Title": "Energy-efficient routing model for water pipeline monitoring based on wireless sensor networks", "Abstract": "In wireless sensor networks (WSNs), water pipeline monitoring is one of the domain applications that require lower energy consumption. Sensor nodes have to sense environment parameters and route the leak data to a central node in low power consumption. Several routing techniques have been proposed to enhance the network lifetime in WSNs. In this paper, we propose a heterogeneous two-tiered routing model in low power consumption. Two routing algorithms are applied in each tier of this model. To evaluate the performance capability of the proposed solution, comparison results based on the simulation tool Cooja applied in the WSN operating system Contiki are presented. We detail, also, a new prototyping based on several platforms to validate the proposed approach. This prototype is experienced in a real water pipeline testbed designed for our application.", "Keywords": "Heterogeneous model ; wireless sensor networks ; routing protocol ; low power ; testbed", "DOI": "10.1080/1206212X.2019.1682239", "PubYear": 2022, "Volume": "44", "Issue": "1", "JournalId": 4962, "JournalTitle": "International Journal of Computers and Applications", "ISSN": "1206-212X", "EISSN": "1925-7074", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CES Research Unit, National School of Engineers of Sfax, Digital Research Center (CRNS), Technopark Sfax, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CES Research Unit, National School of Engineers of Sfax, Digital Research Center (CRNS), Technopark Sfax, Tunisia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CES Research Unit, National School of Engineers of Sfax, Digital Research Center (CRNS), Technopark Sfax, Tunisia"}], "References": []}, {"ArticleId": ********, "Title": "Multi-physics coupling simulation in virtual reactors", "Abstract": "<p>Nuclear power stations involve a range of complex and interacting multi-physical processes. With the rapid development of high-performance computing technology, accurate multi-physics simulation in virtual reactors has drawn more and more attention in industry and academia. Great efforts have been made toward the simulation of multi-physics coupling processes in reactors. The interpretations of many terms that describe multi-physics simulation vary in different literatures. We organize and discuss some important terms relevant to the multi-physics coupling simulation. We compare the three most frequently used multi-physics coupling strategies: the operator splitting, Picard iteration, and Jacobian-Free Newton–<PERSON>ov methods. We summarize three main viewpoints on the degree of coupling of the three strategies (loose, tight, or full coupling). Then we review the coupling software and corresponding coupling strategies in some representative virtual reactor projects. We present the research focuses of Spider coupling platform. The Spider is developed in the China Virtual Reactor (CVR) project. The multi-physics phenomena are considered in the CVR project from three scales: fuel scale, reactor core scale, and system scale. Both loose and tight coupling strategies are supported in the Spider platform.</p>", "Keywords": "", "DOI": "10.1177/0037549719881204", "PubYear": 2021, "Volume": "97", "Issue": "10", "JournalId": 4563, "JournalTitle": "SIMULATION", "ISSN": "0037-5497", "EISSN": "1741-3133", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer and Communication Engineering, University of Science and Technology Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Division of Reactor Engineering Technology Research, China Institute of Atomic Energy, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer and Communication Engineering, University of Science and Technology Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer and Communication Engineering, University of Science and Technology Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Communication Engineering, University of Science and Technology Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computer and Communication Engineering, University of Science and Technology Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Computer and Communication Engineering, University of Science and Technology Beijing, China"}], "References": []}, {"ArticleId": 78201627, "Title": "A benefits framework for public access ICT4D programmes", "Abstract": "<p>There have been expectations across the developing world that information and communications technologies (ICTs) will deliver benefits to marginalised people. Despite many programmes related to the promotion of Internet uptake and usage amongst poorer communities, evidence regarding such benefit is lacking, and there is little agreement in respect of frameworks and metrics to evaluate the benefits thereof. This paper reports on a case study that investigated the nature of a public access information and communication technologies for development (ICT4D) programme and presents a framework for assessing the associated benefits in the South African context. The capability approach is used as a lens to assess the process of providing opportunities to ordinary citizens through ICT access and use. By operationalising agency and conversion factors, the study identified inhibitors and enhancers of opportunities and choices in realising benefits. Through the application of a critical realism paradigm and a mixed method analytical approach, perceptions of a changed life, aspirations for a better life, and “hope” emerged as key intangible benefits. The paper presents an ICT benefits framework, which serves as an important contribution to the ICT4D evaluation field. A novel outcome of the study is that it provides evidence of constructs that to date have been considered as intangible benefits in the literature.</p>", "Keywords": "capability approach;development;evaluation;ICT4D;information and communications technologies;public access centres", "DOI": "10.1002/isd2.12119", "PubYear": 2020, "Volume": "86", "Issue": "2", "JournalId": 6046, "JournalTitle": "The Electronic Journal of Information Systems in Developing Countries", "ISSN": "1681-4835", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Postgraduate Studies, Cape Peninsula University of Technology, Cape Town, South Africa"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Information Systems, University of the Western Cape, Cape Town, South Africa"}], "References": []}, {"ArticleId": 78201629, "Title": "Discrete Spider Monkey Optimization for Travelling Salesman Problem", "Abstract": "Meta-heuristic algorithms inspired by biological species have become very popular in recent years. Collective intelligence of various social insects such as ants, bees, wasps, termites, birds, fish, has been investigated to develop a number of meta-heuristic algorithms in the general domain of swarm intelligence (SI). The developed SI algorithms are found effective in solving different optimization tasks. Travelling Salesman Problem (TSP) is the combinatorial optimization problem where a salesman starting from a home city travels all the other cities and returns to home city in the shortest possible path. TSP is a popular problem due to the fact that the instances of TSP can be applied to solve real-world problems, implication of which turns TSP into a standard test bench for performance evaluation of new algorithms. Spider Monkey Optimization (SMO) is a recent addition to SI algorithms based on the social behaviour of spider monkeys. SMO implicitly adopts grouping and regrouping for the interactions to improve solutions; such multi-population approach is the motivation of this study to develop an effective method for TSP. This paper presents an effective variant of SMO to solve TSP called discrete SMO (DSMO). In DSMO, every spider monkey represents a TSP solution where Swap Sequence (SS) and Swap Operator (SO) based operations are employed, which enables interaction among monkeys in obtaining the optimal TSP solution. The SOs are generated using the experience of a specific spider monkey as well as the experience of other members (local leader, global leader, or a randomly selected spider monkey) of the group. The performance and effectiveness of the proposed method have been verified on a large set of TSP instances and the outcomes are compared to other well-known methods. Experimental results demonstrate the effectiveness of the proposed DSMO for solving TSP.", "Keywords": "Travelling Salesman Problem ; Swap Sequence ; Swap Operator ; Partial Search ; Spider Monkey Optimization", "DOI": "10.1016/j.asoc.2019.105887", "PubYear": 2020, "Volume": "86", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "M.A.<PERSON>. <PERSON>", "Affiliation": "Department of CSE, Khulna University of Engineering & Technology, Khulna, Bangladesh"}, {"AuthorId": 2, "Name": "Safial Islam Ayon", "Affiliation": "Department of CSE, Khulna University of Engineering & Technology, Khulna, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Khulna University of Engineering & Technology, Khulna, Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Engineering and Intelligent Systems, Ulster University, United Kingdom;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The Ohio State University, USA"}], "References": []}, {"ArticleId": 78201678, "Title": "Electroactive NPs and D-amino acids oxidase engineered electrochemical chiral sensor for D-alanine detection", "Abstract": "A simple and accurate magnetic electrochemical sensor is developed for the ultrasensitive chiral recognition of D-amino acids (D-AAs). Fe<sub>3</sub>O<sub>4</sub>@ [email&#160;protected] @Cu<sub>x</sub>O nanoparticles (NPs) (x = 1, 2) are controllable prepared and used as the electrochemical beacons. The controllable integration of plasmonic metal NPs, semiconductors and magnetic NPs enables multilayered Fe<sub>3</sub>O<sub>4</sub>@ [email&#160;protected] @Cu<sub>x</sub>O NPs to display a sharp copper stripping peak at -0.16 V, owning to the electron transfer from Cu<sup>+</sup> to Cu<sup>2+</sup> and the synergistic electron transfer of Au and Ag double layers. D-AAs can be catalyzed by D-amino acid oxidase (DAAO) to produce H<sub>2</sub>O<sub>2</sub>. The autocatalytic oxidation reaction of Cu<sub>x</sub>O shell with the existence of H<sub>2</sub>O<sub>2</sub> induces the decrease of electrochemical signals of Fe<sub>3</sub>O<sub>4</sub>@ [email&#160;protected] @Cu<sub>x</sub>O NPs. Fe<sub>3</sub>O<sub>4</sub>@ [email&#160;protected] @Cu<sub>x</sub>O NPs served as electrochemical beacons achieve the sensitive and accurate enantioselective recognition of D-alanine (D-Ala) in the range from 100 pM to 10 μM. The limit of detection (LOD) is as low as 52 pM. This developed protocol can be extended to the fabrication of a large set of electroactive labels by the introduction of plasmonic metal NPs with high electron transfer efficiency, for the reliable enantioselective recognition with high sensitivity.", "Keywords": "Electroactive ; Core-Shell ; Oxidase ; Detection ; Cu<sub>x</sub>O NPs", "DOI": "10.1016/j.snb.2019.127333", "PubYear": 2020, "Volume": "304", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Synthetic and Biological Colloids, Ministry of Education, International Joint Research Center for Photoresponsive Molecules and Materials, School of Chemical and Material Engineering, Jiangnan University, Wuxi, Jiangsu 214122, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Science and Technology, Hebei Agricultural University, Cangzhou, Hebei, 061100, China"}, {"AuthorId": 3, "Name": "Lixia Shi", "Affiliation": "Key Laboratory of Synthetic and Biological Colloids, Ministry of Education, International Joint Research Center for Photoresponsive Molecules and Materials, School of Chemical and Material Engineering, Jiangnan University, Wuxi, Jiangsu 214122, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Synthetic and Biological Colloids, Ministry of Education, International Joint Research Center for Photoresponsive Molecules and Materials, School of Chemical and Material Engineering, Jiangnan University, Wuxi, Jiangsu 214122, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Synthetic and Biological Colloids, Ministry of Education, International Joint Research Center for Photoresponsive Molecules and Materials, School of Chemical and Material Engineering, Jiangnan University, Wuxi, Jiangsu 214122, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Synthetic and Biological Colloids, Ministry of Education, International Joint Research Center for Photoresponsive Molecules and Materials, School of Chemical and Material Engineering, Jiangnan University, Wuxi, Jiangsu 214122, China;Corresponding author"}], "References": []}, {"ArticleId": 78201744, "Title": "A portable plug-and-play syringe pump using passive valves for microfluidic applications", "Abstract": "Portable and autonomous pumps are in critical demand for low-cost point-of-care testing (POCT) applications in microfluidic fields. In microfluidic laboratories, commercial pumps (e.g., syringe pumps, pressure pumps, or peristaltic pumps) are widely used for precise fluid delivery, however they can’t be integrated into miniaturized microfluidic devices due to their bulky sizes and high costs. Here, we propose a portable plug-and-play syringe pump with autonomous flow delivery function. Fluid stored in the pump is driven by a specially designed compression spring mechanism, and it is controlled by a microfluidic flow regulatory chip which contains three passive valves. Importantly, the liquid flow rate through the passive valve is independent of the fluidic pressure induced by the varied spring compression force, and constant flow autoregulation is realized through the microfluidic flow regulatory chip in the pump. This feature has been used for providing steady fluid infusions for passive sample mixing in a T-junction microfluidic mixer or high efficiency particle separation in an inertial microfluidic chip. We envision that the self-sufficient and portable plug-and-play syringe pump will facilitate the miniaturization of microfluidic systems which will shed light on the large-scale application of microfluidic technologies.", "Keywords": "Microfluidics ; Passive valve ; Plug-and-play pump ; Flow control ; Flow mixing ; Particle separation", "DOI": "10.1016/j.snb.2019.127331", "PubYear": 2020, "Volume": "304", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Hohai University, Changzhou, 213022, China;School of Mechanical Engineering, Jiangsu Key Laboratory for Design and Manufacture of Micro-Nano Biomedical Instruments, Southeast University, Nanjing, 211189, China;Corresponding author at: College of Mechanical and Electrical Engineering, Hohai University, Changzhou, 213022, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Hohai University, Changzhou, 213022, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Hohai University, Changzhou, 213022, China"}], "References": []}, {"ArticleId": 78201994, "Title": "On the effects of pseudorandom and quantum-random number generators in soft computing", "Abstract": "Abstract \n In this work, we argue that the implications of pseudorandom and quantum-random number generators (PRNG and QRNG) inexplicably affect the performances and behaviours of various machine learning models that require a random input. These implications are yet to be explored in soft computing until this work. We use a CPU and a QPU to generate random numbers for multiple machine learning techniques. Random numbers are employed in the random initial weight distributions of dense and convolutional neural networks, in which results show a profound difference in learning patterns for the two. In 50 dense neural networks (25 PRNG/25 QRNG), QRNG increases over PRNG for accent classification at + 0.1%, and QRNG exceeded PRNG for mental state EEG classification by + 2.82%. In 50 convolutional neural networks (25 PRNG/25 QRNG), the MNIST and CIFAR-10 problems are benchmarked, and in MNIST the QRNG experiences a higher starting accuracy than the PRNG but ultimately only exceeds it by 0.02%. In CIFAR-10, the QRNG outperforms PRNG by + 0.92%. The n -random split of a Random Tree is enhanced towards and new Quantum Random Tree (QRT) model, which has differing classification abilities to its classical counterpart, 200 trees are trained and compared (100 PRNG/100 QRNG). Using the accent and EEG classification data sets, a QRT seemed inferior to a RT as it performed on average worse by − 0.12%. This pattern is also seen in the EEG classification problem, where a QRT performs worse than a RT by − 0.28%. Finally, the QRT is ensembled into a Quantum Random Forest (QRF), which also has a noticeable effect when compared to the standard Random Forest (RF). Ten to 100 ensembles of trees are benchmarked for the accent and EEG classification problems. In accent classification, the best RF (100 RT) outperforms the best QRF (100 QRF) by 0.14% accuracy. In EEG classification, the best RF (100 RT) outperforms the best QRF (100 QRT) by 0.08% but is extremely more complex, requiring twice the amount of trees in committee. All differences are observed to be situationally positive or negative and thus are likely data dependent in their observed functional behaviour.", "Keywords": "Quantum computing; Soft computing; Machine learning; Neural networks; Classification", "DOI": "10.1007/s00500-019-04450-0", "PubYear": 2020, "Volume": "24", "Issue": "12", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Engineering and Applied Science, Aston University, Birmingham, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Applied Science, Aston University, Birmingham, UK"}, {"AuthorId": 3, "Name": "Diego <PERSON>", "Affiliation": "School of Engineering and Applied Science, Aston University, Birmingham, UK"}], "References": [{"Title": "An innovative approach for bond strength modeling in FRP strip-to-concrete joints using adaptive neuro–fuzzy inference system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "1083", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 78202089, "Title": "Global matching of point clouds for scan registration and loop detection", "Abstract": "We present a robust Global Matching technique focused on 3D mapping applications using laser range-finders. Our approach works under the assumption that places can be recognized by analyzing the projection of the observed points along the gravity direction. Relative poses between pairs of 3D point clouds are estimated by aligning their 2D projective representations and benefiting from the corresponding dimensional reduction. We present the complete processing pipeline for two different applications that use the global matcher as a core component: First, the global matcher is used for the registration of static scan sets where no a-priori information of the relative poses is available. It is combined with an effective procedure for validating the matches that exploits the implicit empty space information associated to single acquisitions. In the second use case, the global matcher is used for the loop detection required for 3D SLAM applications. We use an Extended Kalman Filter to obtain a belief of the map poses, which allows to validate matches and to execute hierarchical overlap tests, which reduce the number of potential matches to be evaluated. Additionally, the global matcher is combined with a fast local technique. In both use cases, the global reconstruction problem is modeled as a sparse graph, where scan poses (nodes) are connected through matches (edges). The graph structure allows formulating a sparse global optimization problem that optimizes scan poses, considering simultaneously all accepted matches. Our approach is being used in production systems and has been successfully evaluated on several real and publicly available datasets.", "Keywords": "Global registration ; Loop detection ; Place recognition ; SLAM", "DOI": "10.1016/j.robot.2019.103324", "PubYear": 2020, "Volume": "123", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "European Commission, Joint Research Centre (JRC), Via Enrico Fermi 2749, <PERSON><PERSON> (VA), Italy;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "European Commission, Joint Research Centre (JRC), Via Enrico Fermi 2749, <PERSON><PERSON> (VA), Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "European Commission, Joint Research Centre (JRC), Via Enrico Fermi 2749, <PERSON><PERSON> (VA), Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "European Commission, Joint Research Centre (JRC), Via Enrico Fermi 2749, <PERSON><PERSON> (VA), Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "European Commission, Joint Research Centre (JRC), Via Enrico Fermi 2749, <PERSON><PERSON> (VA), Italy"}], "References": []}, {"ArticleId": 78202093, "Title": "Buffer-aided cooperative spectrum sharing with full-duplex wireless-powered relay", "Abstract": "A buffer-aided wireless energy harvesting and information transfer protocol is proposed for full-duplex cooperative cognitive radio networks. In particular, during a transmission block, a secondary transmitter (ST) as a full-duplex relay first scavenges dedicated radio frequency (RF) energy signal transmitting from primary transmitter (PT), and then assists to transmit primary signals by using the harvested energy in exchange for the opportunity of spectrum sharing, where the primary and secondary signals are combined linearly. A buffer with limited-size is implemented on the ST and secondary receiver (SR) to storage the decoded current primary data. The SR performs successive interference decoding and cancelation to obtain desired secondary signal. The primary receiver (PR) detects the desired primary signal by treating the secondary signal as interference. Based on this, the closed-form expressions of the exact outage probabilities for both the primary and secondary systems are derived. Optimal energy harvesting duration and power allocation factor are determined by maximizing the achievable throughput of secondary system while maintaining the transmission performance of the primary system. Simulation results demonstrate that the proposed scheme achieves effective primary transmission while provides a superior transmission performance for the secondary system.", "Keywords": "Cognitive radio ; Spectrum sharing ; Energy harvesting ; Outage probability ; Joint optimization", "DOI": "10.1016/j.comnet.2019.106974", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, South China University of Technology, No. 381 Wushan Road, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, South China University of Technology, No. 381 Wushan Road, Guangzhou, China;Corresponding author."}], "References": []}, {"ArticleId": 78202165, "Title": "High-throughput zebrafish intramuscular recording assay", "Abstract": "Despite tremendous efforts in utilizing zebrafish in neurological disease studies, owing to their easy handling, low cost, fast growth cycle, fecundity, high genetic similarity to humans, and transparency, the high-throughput electrophysiology methods for zebrafish are still absent. Although methods to detect intramuscular activities of adult and larval zebrafish have been previously introduced, the methods are complicated and time-consuming. Therefore, they have not been widely used in the zebrafish research community. We propose a high-throughput muscular activity measurement method using a simple and clever way of trapping zebrafish with microfluidic chip technology. Zebrafish larvae at 5 days post-fertilization were successfully retained in the designed microfluidic chip and were able to maintain their respiration for more than 24 h as long as the water was supplied through the inlet of the chip. The intramuscular activities of the larvae were obtained while they remained in the water. Significantly, this is the first reported method that can be used for measuring intramuscular activities while the larvae are inside the water. As a demonstration, we successfully modulated the locomotor activities of zebrafish using three different chemicals, proving that the developed method can be useful in monitoring intramuscular activities from multiple larvae and assessing the efficacy of pharmaceuticals.", "Keywords": "", "DOI": "10.1016/j.snb.2019.127332", "PubYear": 2020, "Volume": "304", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Division of Fundamental Neurobiology, Krembil Research Institute, University Health Network, Toronto, ON M5T 2S8, Canada;Collaborative Program in Neuroscience, University of Toronto, Toronto, ON M5S 1A1, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Chosun University, Gwangju 61452, Republic of Korea;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Robotics Engineering, Daegu Gyeongbuk Institute of Science and Technology (DGIST), Daegu 42988, Republic of Korea;Corresponding authors"}], "References": []}, {"ArticleId": 78202167, "Title": "Quaternion broad learning system: A novel multi-dimensional filter for estimation and elimination tremor in teleoperation", "Abstract": "In this paper, a novel quaternion broad learning system is proposed in this paper for tremor estimation and elimination in teleoperation. In the new proposed QBLS, the architecture can be divided into three layers, including quaternion feature layer, enhancement layer and the output layer. In quaternion feature layer, a quaternion-value auto-encoder (QAE) based on the quaternion algebra is proposed and employed to extract the unsupervised features in quaternion domain. Moreover, the enhancement nodes are mapped to improve the system’s regression ability in enhancement layer. In the output layer, the nodes of feature layer and enhancement layer are concatenated to map the output of QBLS. The weight parameters of output layer can be calculated by the minimum norm least squares solutions. In addition, the semi-physical simulation experiment is completed and the new proposed QBLS has been compared with some existing methods. Finally, the effectiveness and efficiency of QBLS are demonstrated by experimental results.", "Keywords": "Quaternion broad learning system(QBLS) ; Teleoperation ; Physiological tremor", "DOI": "10.1016/j.neucom.2019.10.059", "PubYear": 2020, "Volume": "380", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, Guangdong 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, Guangdong 510006, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, South China University of Technology, Guangzhou, Guangdong 510006, China;Navigation College, Dalian Maritime University, Dalian 116026, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, Guangdong 510006, China"}], "References": []}, {"ArticleId": 78202170, "Title": "Merged Tree-CAT: A fast method for building precise computerized adaptive tests based on decision trees", "Abstract": "Over the last few years, there has been an increasing interest in the creation of Computerized Adaptive Tests (CATs) based on Decision Trees (DTs). Among the available methods, the Tree-CAT method has been able to demonstrate a mathematical equivalence between both techniques. However, this method has the inconvenience of requiring a high performance cluster while taking a few days to perform its computations. This article presents the Merged Tree-CAT method, which extends the Tree-CAT technique, to create CATs based on DTs in just a few seconds in a personal computer. In order to do so, the Merged Tree-CAT method controls the growth of the tree by merging those branches in which both the distribution and the estimation of the latent level are similar. The performed experiments show that the proposed method obtains estimations of the latent level which are comparable to the obtained by the state-of-the-art techniques, while drastically reducing the computational time.", "Keywords": "Computerized adaptive tests ; Decision trees ; Linear programming", "DOI": "10.1016/j.eswa.2019.113066", "PubYear": 2020, "Volume": "143", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Carlos III de Madrid, Department of Statistics, Leganés, Spain;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": "Universidad Carlos III de Madrid, Department of Statistics, Leganés, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad Carlos III de Madrid, Department of Statistics, Leganés, Spain"}, {"AuthorId": 4, "Name": "<PERSON>Cuadrado", "Affiliation": "Universidad Autónoma de Madrid, Department of Developmental and Educational Psychology, Cantoblanco, Spain"}], "References": []}, {"ArticleId": 78202283, "Title": "Design of a Trustless Smart City system: The #SmartME experiment", "Abstract": "A Smart City is an example of a domain where citizens and institutions, at any level, should be empowered to cooperate for the greater good of the respective communities, and one of the most powerful ways to attain that is by actually decentralizing any process that can escape single-authority control and the grip of centrally-managed bureaucracies, whilst at the same time not relying on any single party trusting any other. In line with this key insight, in this paper we have designed and implemented a decentralized, trustless DLT-based system for (environmental) sensing, data acquisition, storage, and consumption, in the context of a real-world Smart City deployment, #SmartME, and with the participation of independent, institutional, stakeholders. This Open Data system has been devised to be user-friendly, with an end-user wizard for trustless (i.e., independent) data audit. Indeed, the data collection layer does not trust any IoT node by default, but requires nodes to authenticate themselves when sending readings. Last but not least, the storage layer is again trustless, as institutions can pool together their resources, without trusting one another, nor entrusting the administration of the system to any third party. In this work we extend our preliminary proof-of-concept design, and explore the expected outcomes of scaling the system to comprise an even greater number of active institutions participating in the validation process, by carrying out simulation experiments. Results from these experiments help us in validating our approach and assessing its scalability.", "Keywords": "Smart City ; Decentralization ; DLT ; Blockchain ; Consensus ; Open data", "DOI": "10.1016/j.iot.2019.100126", "PubYear": 2020, "Volume": "10", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology Allahabad, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Messina, Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Messina, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Messina, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology Allahabad, India"}], "References": []}, {"ArticleId": 78202627, "Title": "A Survey on Pavement Sectioning in Network Level and an Intelligent Homogeneous Method by Hybrid PSO and GA", "Abstract": "<p>Pavement homogenous sectioning is a vital step in pavement management system (PMS) analysis. Sections can be created using either fixed, dynamic or static sectioning principles. Each of these has various implications with regard to the data collection and decision making strategy of management. Pavement management with true homogeneous section selection has great importance for cost minimization over a specified time period when modifying the pavement deterioration based on correct decisions in the PMS. This issue was proposed for cost reduction, minimization of sectioning errors, and accuracy improvement of pavement network analysis. Thus, the focus of this research is to investigate efficient hybrid methods applied for reducing complexity involved in this problem. Results show that various combinations of hybrid particle swarm optimization (PSO) and genetic algorithm (GA) were used for analysis of a given pavement network that play a better role as section makers than single GA or PSO in terms of network sectioning error, computation time (CPU time), and number of sections as well as convergence diagrams for network, project, and section management levels. Results indicated that hybrid approaches provide a highly suitable solution in a short time for each pavement branch in massive networks with big data and minimize the costs involved in the sectioning process.</p>", "Keywords": "", "DOI": "10.1007/s11831-019-09360-w", "PubYear": 2020, "Volume": "27", "Issue": "3", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Young Researchers and Elites Club, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Amirkabir University of Technology (AUT), Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Amirkabir University of Technology (AUT), Tehran, Iran"}], "References": []}, {"ArticleId": 78202740, "Title": "Grafting for combinatorial binary model using frequent itemset mining", "Abstract": "Abstract \nWe consider the class of linear predictors over all logical conjunctions of binary attributes, which we refer to as the class of combinatorial binary models (CBMs) in this paper. CBMs are of high knowledge interpretability but naïve learning of them from labeled data requires exponentially high computational cost with respect to the length of the conjunctions. On the other hand, in the case of large-scale datasets, long conjunctions are effective for learning predictors. To overcome this computational difficulty, we propose an algorithm, GRAfting for Binary datasets (GRAB) , which efficiently learns CBMs within the $$L_1$$ L 1 -regularized loss minimization framework. The key idea of GRAB is to adopt weighted frequent itemset mining for the most time-consuming step in the grafting algorithm, which is designed to solve large-scale $$L_1$$ L 1 -RERM problems by an iterative approach. Furthermore, we experimentally showed that linear predictors of CBMs are effective in terms of prediction accuracy and knowledge discovery.", "Keywords": "Combinatorial Boolean model; Sparse learning; Knowledge discovery; Frequent itemset mining", "DOI": "10.1007/s10618-019-00657-9", "PubYear": 2020, "Volume": "34", "Issue": "1", "JournalId": 3928, "JournalTitle": "Data Mining and Knowledge Discovery", "ISSN": "1384-5810", "EISSN": "1573-756X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Tokyo, Bunkyo-ku, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The University of Tokyo, Bunkyo-ku, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Tokyo, Bunkyo-ku, Japan"}], "References": []}, {"ArticleId": 78202876, "Title": "Counting crowds using a scale-distribution-aware network and adaptive human-shaped kernel", "Abstract": "Intelligent bus system plays a key role in the modern smart city. The number of passengers in the buses or at the stations is necessary for making an optimal scheduling policy of public buses. We develop a crowd counting algorithm to provide the counting information for a bus dispatch system in a human–machine system. In consideration of the challenges (e.g., pedestrian occlusions, non-uniform crowd distributions, and scale variations) existed in hand-crafted features based crowd counting, a scale-distribution-aware multi-column convolutional neural network (SDA-MCNN) is presented to count crowds by summing up the output (denoted as the density map) of the SDA-MCNN. The SDA-MCNN is robust to scale variations by processing a crowd image with multiple convolutional neural network (CNN) columns and minimizing the per-scale loss. A weighted Euclidean loss is proposed to handle non-uniform crowd distributions. The loss can increase activations in dense regions and restrain activations in backgrounds. A new approach to estimate perspective maps of dense crowds is put forward to offer necessary information for generating density maps with human-shaped kernels. Evaluations on benchmarks are performed with other state-of-the-art counting approaches using deep neural networks. Comparative results verify the accuracy of our counting approach in challenging crowds. Evaluations on the real world BUS data reveal the accuracy of the proposed approach in counting passengers in spite of the complex environment.", "Keywords": "Crowd counting ; Intelligent bus system ; Multi-column convolutional neural network ; Weighted euclidean loss ; Human-machine system", "DOI": "10.1016/j.neucom.2019.02.071", "PubYear": 2020, "Volume": "390", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Science and Engineering, Changzhou University, Changzhou 213000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Science and Engineering, Changzhou University, Changzhou 213000, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of IoT Engineering, College of Hohai University, Changzhou 213000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Liu", "Affiliation": "Department of Electronic Engineering, College of Information Science and Engineering, Ocean University of China, Qingdao 266100, China;Corresponding author"}, {"AuthorId": 5, "Name": "Jidong Lv", "Affiliation": "Department of Information Science and Engineering, Changzhou University, Changzhou 213000, China"}], "References": []}, {"ArticleId": 78203238, "Title": "Decreasing the problematic use of an information system: An empirical investigation of smartphone game players", "Abstract": "<p>Based on protection motivation theory, this study aims to identify factors that influence the intention to decrease problematic smartphone game use, including the effects of a gaming habit and subjective norms. The research model is tested with a scenario‐based survey method. Players' perceived threat refers to the negative consequences (eg, reduced performance or social isolation) that are caused by problematic smartphone game use. The findings indicate that under high fear appeals, threat appraisal creates fearful emotions about suffering from the negative consequences, which further reduces the gaming habit and activates the intention to decrease use. Self‐efficacy in decreasing game playing, the response efficacy of decreasing usage in avoiding the threat and subjective norms effectively promote users' intention to decrease use. Moreover, response costs reduce the intention to decrease use. The findings provide insights into how to activate users' intention to decrease problematic use, and the resulting implications are discussed.</p>", "Keywords": "gaming habit;intention to decrease use;problematic use of smartphone games;protection motivation theory;subjective norms", "DOI": "10.1111/isj.12264", "PubYear": 2020, "Volume": "30", "Issue": "3", "JournalId": 7047, "JournalTitle": "Information Systems Journal", "ISSN": "1350-1917", "EISSN": "1365-2575", "Authors": [{"AuthorId": 1, "Name": "Chongyang Chen", "Affiliation": "Research Center for Smarter Supply Chain, Dongwu Business School, Soochow University, Suzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "International School of Business and Finance, Sun Yat‐sen University, Zhuhai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Xi'an Jiao Tong University, Xi'an, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Information Systems, City University of Hong Kong, Hong Kong"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Research Center for Smarter Supply Chain, Dongwu Business School, Soochow University, Suzhou, China"}], "References": []}, {"ArticleId": 78203242, "Title": "Microfluidic Paper-Based Analytical Devices for Colorimetric Detection of Lactoferrin", "Abstract": "<p> Lactoferrin is an abundant glycoprotein in human body fluids and is known as a biomarker for various diseases. Therefore, point-of-care testing (POCT) for lactoferrin is of interest. Microfluidic paper-based analytical devices (µPADs) have gained a lot of attention as next-generation POCT device candidates, due to their inexpensiveness, operational simplicity, and being safely disposable. This work presents a colorimetric sensing approach for quantitative lactoferrin analysis. The detection mechanism takes advantage of the high affinity of lactoferrin to ferric ions (Fe <sup>3+</sup> ). Lactoferrin is able to displace an indicator from a colorimetric 2-(5-bromo-2-pyridylazo)-5-diethylaminophenol (5-Br-PADAP)-Fe <sup>3+</sup> complex, resulting in a color change. A 5-Br-PADAP-Fe <sup>3+</sup> complex was encapsulated into water-dispersible poly(styrene- block-vinylpyrrolidone) particles, whose physical entrapment in the cellulosic fiber network results in the immobilization of the complex to the paper matrix. The complex-encapsulating particles showed a color change response in accordance with lactoferrin concentration. Both color intensity-based paper well plates and distance readout-based µPADs are demonstrated. Color intensity-based devices allowed quantitative analysis of lactoferrin concentrations with a limit of detection of 110 µg/mL, using a smartphone and a color readout app. On the other hand, distance readout-based µPADs showed changes of the length of colored sections in accordance with lactoferrin concentration. In summary, we successfully developed both colorimetric intensity-based paper wells and distance-based µPADs for lactoferrin detection. This work demonstrates a user-friendly colorimetric analysis platform for lactoferrin without requiring lab equipment and expensive antibodies. </p>", "Keywords": "colorimetric detection;lactoferrin;microfluidic paper-based analytical devices;µPADs", "DOI": "10.1177/2472630319884031", "PubYear": 2020, "Volume": "25", "Issue": "1", "JournalId": 19198, "JournalTitle": "SLAS TECHNOLOGY: Translating Life Sciences Innovation", "ISSN": "2472-6303", "EISSN": "2472-6311", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Chemistry, Keio University, Yokohama, Kanagawa, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Chemistry, Keio University, Yokohama, Kanagawa, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Chemistry, Keio University, Yokohama, Kanagawa, Japan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Applied Chemistry, Keio University, Yokohama, Kanagawa, Japan"}], "References": []}, {"ArticleId": 78203295, "Title": "NOMA based CR for QAM-64 and QAM-256", "Abstract": "Non-Orthogonal multiple access (NOMA) and Cognitive radio (Cr) are seen as one of the most promising techniques, which improves the utilization of the spectrum in 5G. The expanding number of wireless applications like new gadgets, IOT brought about developing a block in the ISM groups. The FCC requested to permit unlicensed clients to work in the void area without obstruction to an authorized guest. Cr gives an answer for an extra range prerequisite issue for productive spectrum usage. The foremost condition for permitting CRs to utilize spectrum is not causing obstruction to licensed users. Spectrum sensing permit secondary users (Su) to separately recognize the idle portions of the spectrum, and thus evade obstruction to licensed users. In existing spectrum sensing techniques, SU can only utilize the unused spectrum when PU is not present. Therefore, spectrum exploitation of the conventional system is very low. In recent times NOMA has been projected to utilize the spectrum in an efficient manner. The proposed work permits the SU to utilize a spectrum of PU, both at its absence. Spectrum sensing in NOMA is not explored so far. Hence, in this paper, NOMA based matched filter detection is designed for QAM-64 and QAM-256. Matlab simulation is applied to study the operation of the proposed detection technique in NOMA in respect of several parameters like bit error rate (BER) Vs signal to noise ratio (SNR), the probability of detection (Pd), and probability of false alarm (Pfa).", "Keywords": "Matched filter ; Cognitive radio (Cr) ; NOMA-OFDM", "DOI": "10.1016/j.eij.2019.10.004", "PubYear": 2020, "Volume": "21", "Issue": "2", "JournalId": 5980, "JournalTitle": "Egyptian Informatics Journal", "ISSN": "1110-8665", "EISSN": "2090-4754", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Electronics and Communication, JECRC University, Jaipur 303905, India;Corresponding author at: Dept. of Electronics and Communication, JECRC University, Jaipur 303905, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Electronics and Communication, JECRC University, Jaipur 303905, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Electronics and Communication, JECRC University, Jaipur 303905, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Electronics and Communication, JECRC University, Jaipur 303905, India"}], "References": []}, {"ArticleId": 78203451, "Title": "A comparison of machine and deep-learning algorithms applied to multisource data for a subtropical forest area classification", "Abstract": "This work explores the integration of airborne Light Detection and Ranging (LiDAR) data and WorldView-2 (WV2) images to classify the land cover of a subtropical forest area in Southern Brazil. Different deep and machine learning methods were used: one based on convolutional neural network (CNN) and three ensemble methods. We adopted both pixel- (in the case of CNN) and object-based approaches. The results demonstrated that the integration of LiDAR and WV2 data led to a significant increase (7% to 16%) in accuracies for all classifiers, with kappa coefficient (<i>κ</i>) ranging from 0.74 for the random forest (RF) classifier associated with the WV2 dataset, to 0.92 for the forest by penalizing attributes (FPA) with the full (LiDAR + WV2) dataset. Using the WV2 dataset solely, the best <i>κ</i> was 0.81 with CNN classifier, while for the LiDAR dataset, the best <i>κ</i> was 0.8 with the rotation forest (RotF) algorithm. The use of LiDAR data was especially useful for the discrimination of vegetation classes because of the different height properties among them. In its turn, the WV2 data provided better performance for classes with less structure variation, such as field and bare soil. All the classification algorithms had a nearly similar performance: the results vary slightly according to the dataset used and none of the methods achieved the best accuracy for all classes. It was noticed that both datasets (WV2 and LiDAR) even when applied alone achieved good results with deep and machine learning methods. However, the advantages of integrating active and passive sensors were evident. All these methods provided promising results for land cover classification experiments of the study area in this work.", "Keywords": "", "DOI": "10.1080/********.2019.1681600", "PubYear": 2020, "Volume": "41", "Issue": "5", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Remote Sensing, National Institute for Space Research (INPE), São José dos Campos, Brazil"}, {"AuthorId": 2, "Name": "C<PERSON> <PERSON><PERSON>", "Affiliation": "Division of Remote Sensing, National Institute for Space Research (INPE), São José dos Campos, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Forest Engineering, Santa Catarina State University (UDESC), Lages, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Forest Engineering, Santa Catarina State University (UDESC), Lages, Brazil"}, {"AuthorId": 5, "Name": "L. E. C. La Rosa", "Affiliation": "Department of Electrical Engineering, Pontiﬁcal Catholic University of Rio de Janeiro (PUC), Rio de Janeiro, Brazil"}, {"AuthorId": 6, "Name": "J. D. B. Castro", "Affiliation": "Department of Electrical Engineering, Pontiﬁcal Catholic University of Rio de Janeiro (PUC), Rio de Janeiro, Brazil"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Pontiﬁcal Catholic University of Rio de Janeiro (PUC), Rio de Janeiro, Brazil"}], "References": []}, {"ArticleId": 78203479, "Title": "Enabling technologies and tools for digital twin", "Abstract": "Digital twin is revolutionizing industry. Fired by sensor updates and history data, the sophisticated models can mirror almost every facet of a product, process or service. In the future, everything in the physical world would be replicated in the digital space through digital twin technology. As a cutting-edge technology, digital twin has received a lot of attention. However, digital twin is far from realizing their potential, which is a complex system and long-drawn process. Researchers must model all the different parts of the objects or systems. Varied types of data needed to be collected and merged. Many researchers and participators in engineering are not clear which technologies and tools should be used. 5-dimension digital twin model provides reference guidance for understanding and implementing digital twin. From the perspective of 5-dimension digital twin model, this paper tries to investigate and summarize the frequently-used enabling technologies and tools for digital twin to provide technologies and tools references for the applications of digital twin in the future.", "Keywords": "Digital twin ; Five-dimension model ; Enabling technologies ; Enabling tools", "DOI": "10.1016/j.jmsy.2019.10.001", "PubYear": 2021, "Volume": "58", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "Qinglin Qi", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing, 100083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing, 100083, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, 250061, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "LURPA, ENS Paris-Saclay, Universite Paris-Sud, Universite Paris-Saclay, 94235, <PERSON><PERSON><PERSON>, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Manufacturing Engineering, University of New South Wales, Sydney, 2053, Australia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, 250061, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Production Engineering, KTH Royal Institute of Technology, Stockholm, Sweden"}, {"AuthorId": 8, "Name": "A.Y.C. Nee", "Affiliation": "Department of Mechanical Engineering, National University of Singapore, Singapore, Singapore"}], "References": []}, {"ArticleId": 78203480, "Title": "Night-time lights are more strongly related to urban building volume than to urban area", "Abstract": "A strong relationship between night-time light (NTL) data and the areal extent of urbanized regions has been observed frequently. As urban regions have an important vertical dimension, it is hypothesized that the strength of the relationship with NTL can be increased by consideration of the volume rather than simply the area of urbanized land. Relationships between NTL and the area and volume of urbanized land were determined for a set of towns and cities in the UK, the conterminous states of the USA and countries of the European Union. Strong relationships between NTL and the area urbanized were observed, with correlation coefficients ranging from 0.9282 to 0.9446. Higher correlation coefficients were observed for the relationship between NTL and urban building volume, ranging from 0.9548 to 0.9604; The difference in the correlations obtained with volume and with area was statistically significant at the 95% level of confidence. Studies using NTL data may be strengthened by consideration of the volume rather than just area of urbanized land.", "Keywords": "", "DOI": "10.1080/2150704X.2019.1682709", "PubYear": 2020, "Volume": "11", "Issue": "1", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory for Environment and Disaster Monitoring and Evaluation, Institute of Geodesy and Geophysics, Chinese Academy of Sciences, Wuhan, China;The University of Chinese Academy of Sciences, Beijing, China;School of Geography, University of Nottingham, University Park, Nottingham, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Geography, University of Nottingham, University Park, Nottingham, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Geography, University of Nottingham, University Park, Nottingham, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Geography, University of Nottingham, University Park, Nottingham, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Environment and Disaster Monitoring and Evaluation, Institute of Geodesy and Geophysics, Chinese Academy of Sciences, Wuhan, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Key Laboratory for Environment and Disaster Monitoring and Evaluation, Institute of Geodesy and Geophysics, Chinese Academy of Sciences, Wuhan, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Key Laboratory for Environment and Disaster Monitoring and Evaluation, Institute of Geodesy and Geophysics, Chinese Academy of Sciences, Wuhan, China"}], "References": []}, {"ArticleId": 78203661, "Title": "Mixed-dense connection networks for image and video super-resolution", "Abstract": "Efficiency of gradient propagation in intermediate layers of convolutional neural networks is of key importance for super-resolution task. To this end, we propose a deep architecture for single image super-resolution (SISR), which is built using efficient convolutional units we refer to as mixed-dense connection blocks (MDCB). The design of MDCB combines the strengths of both residual and dense connection strategies, while overcoming their limitations. To enable super-resolution for multiple factors, we propose a scale-recurrent framework which reutilizes the filters learnt for lower scale factors recursively for higher factors. This leads to improved performance and promotes parametric efficiency for higher factors. We train two versions of our network to enhance complementary image qualities using different loss configurations. We further employ our network for video super-resolution task, where our network learns to aggregate information from multiple frames and maintain spatio-temporal consistency. The proposed networks lead to qualitative and quantitative improvements over state-of-the-art techniques on image and video super-resolution benchmarks.", "Keywords": "Super-resolution ; Deep learning ; Residual networks ; Dense connection ; Video super-resolution", "DOI": "10.1016/j.neucom.2019.02.069", "PubYear": 2020, "Volume": "398", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IPCV Lab, Department of Electrical Engineering, Indian Institute of Technology Madras, TN, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IPCV Lab, Department of Electrical Engineering, Indian Institute of Technology Madras, TN, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPCV Lab, Department of Electrical Engineering, Indian Institute of Technology Madras, TN, India"}], "References": []}, {"ArticleId": 78204201, "Title": "Optimizing speed/accuracy trade-off for person re-identification via knowledge distillation", "Abstract": "Finding a person across a camera network plays an important role in video surveillance. For a real-world person re-identification application, in order to guarantee an optimal time response, it is crucial to find the balance between accuracy and speed. We analyse this trade-off, comparing a classical method, that comprises hand-crafted feature description and metric learning, in particular, LOMO and XQDA, to deep learning based techniques, using image classification networks, ResNet and MobileNets. Additionally, we propose and analyse network distillation as a learning strategy to reduce the computational cost of the deep learning approach at test time. We evaluate both methods on the Market-1501 and DukeMTMC-reID large-scale datasets, showing that distillation helps reducing the computational cost at inference time while even increasing the accuracy performance.", "Keywords": "Person re-identification ; Network distillation ; Image retrieval ; Model compression ; Surveillance", "DOI": "10.1016/j.engappai.2019.103309", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "United Technology Research Centre Ireland, 4th Floor, Penrose Business Center, Penrose Wharf, Cork City, Co. Cork, Republic of Ireland;Correspondence to: Computer Vision Center, Universitat Autònoma de Barcelona, Edifici O, 08193 Bellaterra, Spain."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Vision Center, Universitat Autònoma de Barcelona, Edifici O, 08193 Bellaterra, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "United Technology Research Centre Ireland, 4th Floor, Penrose Business Center, Penrose Wharf, Cork City, Co. Cork, Republic of Ireland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "United Technology Research Centre Ireland, 4th Floor, Penrose Business Center, Penrose Wharf, Cork City, Co. Cork, Republic of Ireland"}], "References": []}, {"ArticleId": 78204590, "Title": "LSH-based distributed similarity indexing with load balancing in high-dimensional space", "Abstract": "<p>Locality-sensitive hashing (LSH) and its variants are well-known indexing schemes for solving the similarity search problem in high-dimensional space. Traditionally, these indexing schemes are centrally managed and multiple hash tables are needed to guarantee the search quality. However, due to the limitation of storage space and processing capacity of the server, the centralized indexing schemes become impractical for massive data objects. Therefore, several distributed indexing schemes based on peer-to-peer (P2P) networks are proposed, whereas how to ensure load balancing is still one of the key issues. To solve the problem, in this paper, we propose two theoretical LSH-based data distribution models in P2P networks for datasets with homogeneous and heterogeneous (l_2) norms, respectively. Unlike earlier schemes, to our knowledge, we focus on load balancing for a single hash table rather than multiple tables, which has not been considered previously. Then, we propose a static distributed indexing scheme with a novel load balancing indexing mapping method based on the cumulative distribution function by our models. Furthermore, we propose a dynamic load rebalancing algorithm using virtual node method of P2P networks to make the static indexing scheme more practical and robust. The experiments based on synthetic and real datasets show that the proposed distributed similarity indexing schemes are effective and efficient for load balancing in similarity indexing of high-dimensional space.</p>", "Keywords": "Locality-sensitive hashing; Similarity search; P2P networks; Load balancing; High-dimensional space", "DOI": "10.1007/s11227-019-03047-6", "PubYear": 2020, "Volume": "76", "Issue": "1", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer, Nanjing University of Posts and Telecommunications, Nanjing, China;Jiangsu Key Laboratory of Big Data Security and Intelligent Processing, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer, Nanjing University of Posts and Telecommunications, Nanjing, China;Jiangsu Key Laboratory of Big Data Security and Intelligent Processing, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Liu", "Affiliation": "School of Computer, Nanjing University of Posts and Telecommunications, Nanjing, China;Jiangsu Key Laboratory of Big Data Security and Intelligent Processing, Nanjing, China"}], "References": []}, {"ArticleId": 78204823, "Title": "How meaningful are similarities in deep trajectory representations?", "Abstract": "Finding similar trajectories is an important task in moving object databases. However, classical similarity models face several limitations, including scalability and robustness. Recently, an approach named t2vec proposed transforming trajectories into points in a high dimensional vector space, and this transformation approximately keeps distances between trajectories. t2vec overcomes that scalability limitation: Now it is possible to cluster millions of trajectories. However, the semantics of the learned similarity values – and whether they are meaningful – is an open issue. One can ask: How does the configuration of t2vec affect the similarity values of trajectories? Is the notion of similarity in t2vec similar, different, or even superior to existing models? As for any neural-network-based approach, inspecting the network does not help to answer these questions. So the problem we address in this paper is how to assess the meaningfulness of similarity in deep trajectory representations. Our solution is a methodology based on a set of well-defined, systematic experiments. We compare t2vec to classical models in terms of robustness and their semantics of similarity, using two real-world datasets. We give recommendations which model to use in possible application scenarios and use cases. We conclude that using t2vec in combination with classical models may be the best way to identify similar trajectories. Finally, to foster scientific advancement, we give the public access to all trained t2vec models and experiment scripts. To our knowledge, this is the biggest collection of its kind.", "Keywords": "Trajectory similarity ; Trajectory embedding models ; Moving object databases ; Trajectory databases ; Trajectory clustering ; Deep learning", "DOI": "10.1016/j.is.2019.101452", "PubYear": 2021, "Volume": "98", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, 76131 Karlsruhe, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, 76131 Karlsruhe, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, 76131 Karlsruhe, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, 76131 Karlsruhe, Germany"}], "References": []}, {"ArticleId": 78205082, "Title": "The impact of trust and information networks on teachers’ job satisfaction", "Abstract": "<h3>Purpose</h3> <p>The purpose of this paper is to provide an understanding of how two types of informal social networks – those related to instrumental purposes of information sharing and those related to expressive purposes of interpersonal trust – impact teachers’ job satisfaction.</p> <h3>Design/methodology/approach</h3> <p>This paper uses social network analysis (SNA) degree and betweenness measures and job satisfaction scales from the Job Diagnostic Survey to collect longitudinal data from employees in one of the vocational schools in Saint Petersburg, Russia via structured interviews. Data on a total of 354 ties were analysed for 40 ego networks in 2018 and 33 ego networks in 2019.</p> <h3>Findings</h3> <p>The obtained results partially confirm the positive effect of teachers’ position in instrumental and expressive networks on job satisfaction. More centrally positioned teachers were more satisfied with peers and colleagues. They feel more secure in regard to job security, given the unique and multi-faceted knowledge they possess. Structural diversity of the network, as well as the category of a teacher (core subject or vocational subject), are found to explain the uneven evolvement of network size. The authors argue that the decrease in network size can be treated as a positive externality of changes in an informal network. The variation in teachers’ experience seems to explain both job satisfaction and network composition.</p> <h3>Research limitations/implications</h3> <p>The paper is based on a case study and its findings are limited to one particular organization. Nonetheless, the proposed SNA application is of potential value for similar organizations in terms of enhancing their capacity to benefit from networks. This study uses a structured interview to collect network data and job satisfaction data. However, overt observation or secondary data on written communication (e-mail, reports) may provide additional insights about the sought impact in the context of school.</p> <h3>Practical implications</h3> <p>Both teachers and managers benefit from the results of the paper. Educational policymakers and schools’ administration can exploit the bird’s eye view on an organization that SNA provides. By identifying focal employees and their attitude towards school, one receives an opportunity to prevent structural holes, organizational conflicts and uneven distribution of workload. Novice teachers can nurture their well-being by enhancing personal and instrumental social networks at the start of their careers. Experienced teachers benefit from social cooperation as it fosters the exchange of experience and skills, which is vital for job retention.</p> <h3>Originality/value</h3> <p>This research extends the understanding of the role of different kinds of social networks in teachers’ job satisfaction. The paper provides new insights into the SNA application to vocational schools and developing economies. Authors address teachers’ informal networks both from ego and complete network analyses to provide the holistic, yet detailed view. The use of longitudinal data advances the understanding of how personal and group networks develop over time.</p>", "Keywords": "Job satisfaction;Secondary education;Social network analysis;Social networks;Informal communication;Information network;Trust network;Vocational school", "DOI": "10.1108/K-04-2019-0298", "PubYear": 2020, "Volume": "49", "Issue": "1", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Research University Higher School of Economics , St. Petersburg, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business and Management, Lappeenranta-Lahti University of Technology LUT , Lappeenranta, Finland"}], "References": []}, {"ArticleId": 78205159, "Title": "Hybridizing grey wolf optimization with neural network algorithm for global numerical optimization problems", "Abstract": "<p>This paper proposes a novel hybrid algorithm, called grey wolf optimization with neural network algorithm (GNNA), for solving global numerical optimization problems. The core idea of GNNA is to make full use of good global search ability of neural network algorithm (NNA) and fast convergence of grey wolf optimizer (GWO). Moreover, both NNA and GWO are improved to boost their own advantages. For NNA, an improved NNA is given to strengthen the exploration ability of NNA by discarding transfer operator and introducing random modification factor. For GWO, an enhanced GWO is presented, which adjusts the exploration rate based on reinforcement learning principles. Then the improved NNA and the enhanced GWO are hybridized by dynamic population mechanism. A comprehensive set of 23 well-known unconstrained benchmark functions are employed to examine the performance of GNNA compared with 13 metaheuristic algorithms. Such comparisons suggest that the combination of the improved NNA and the enhanced GWO is very effective and GNNA is clearly seen to be more successful in both solution quality and computational efficiency.</p>", "Keywords": "Artificial neural networks; Reinforcement learning; Grey wolf optimizer; Numerical optimization", "DOI": "10.1007/s00521-019-04580-4", "PubYear": 2020, "Volume": "32", "Issue": "14", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, People’s Republic of China;College of Applied Science and Technology, Hainan University, Haikou, People’s Republic of China"}], "References": []}, {"ArticleId": 78205798, "Title": "Corner Detection Using Multi-directional Structure Tensor with Multiple Scales", "Abstract": "<p>Corners are important features for image analysis and computer vision tasks. Local structure tensors with multiple scales are widely used in intensity-based corner detectors. In this paper, the properties of intensity variations of a step edge, L-type corner, Y- or T-type corner, X-type corner, and star-type corner are investigated. The properties that we obtained indicate that the image intensity variations of a corner are not always large in all directions. The properties also demonstrate that existing structure tensor-based corner detection methods cannot depict the differences of intensity variations well between edges and corners which result in wrong corner detections. We present a new technique to extract the intensity variations from input images using anisotropic Gaussian directional derivative filters with multiple scales. We prove that the new extraction technique on image intensity variation has the ability to accurately depict the characteristics of edges and corners in the continuous domain. Furthermore, the properties of the intensity variations of step edges and corners enable us to derive a new multi-directional structure tensor with multiple scales, which has the ability to depict the intensity variation differences well between edges and corners in the discrete domain. The eigenvalues of the multi-directional structure tensor with multiple scales are used to develop a new corner detection method. Finally, the criteria on average repeatability (under affine image transformation, JPEG compression, and noise degradation), region repeatability based on the Oxford dataset, repeatability metric based on the DTU dataset, detection accuracy, and localization accuracy are used to evaluate the proposed detector against ten state-of-the-art methods. The experimental results show that our proposed detector outperforms all the other tested detectors.</p>", "Keywords": "Corner detection; Image intensity variation extraction; Anisotropic Gaussian directional derivative filters; Multi-directional structure tensor with multiple scales", "DOI": "10.1007/s11263-019-01257-2", "PubYear": 2020, "Volume": "128", "Issue": "2", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical and Information, Xi’an Polytechnic University, Xi’an, China"}, {"AuthorId": 2, "Name": "Changming Sun", "Affiliation": "CSIRO Data61, Epping, Australia"}], "References": []}, {"ArticleId": 78206189, "Title": "Spectra, Hitting Times and Resistance Distances of q - Subdivision Graphs", "Abstract": "Abstract <p>Subdivision, triangulation, Kronecker product, corona product and many other graph operations or products play an important role in complex networks. In this paper, we study the properties of $q$-subdivision graphs, which have been applied to model complex networks. For a simple connected graph $G$, its $q$-subdivision graph $S_q(G)$ is obtained from $G$ through replacing every edge $uv$ in $G$ by $q$ disjoint paths of length 2, with each path having $u$ and $v$ as its ends. We derive explicit formulas for many quantities of $S_q(G)$ in terms of those corresponding to $G$, including the eigenvalues and eigenvectors of normalized adjacency matrix, two-node hitting time, Kemeny constant, two-node resistance distance, Kirchhoff index, additive degree-Kirchhoff index and multiplicative degree-Kirchhoff index. We also study the properties of the iterated $q$-subdivision graphs, based on which we obtain the closed-form expressions for a family of hierarchical lattices, which has been used to describe scale-free fractal networks.</p>", "Keywords": "", "DOI": "10.1093/comjnl/bxz141", "PubYear": 2021, "Volume": "64", "Issue": "1", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Intelligent Information, Shanghai 200433, China;School of Mathematical Sciences, Fudan University, Shanghai 200433, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Intelligent Information, Shanghai 200433, China;School of Computer Science, Fudan University, Shanghai 200433, China;Fudan-Zhongan Joint Laboratory of Blockchain and Information Security, Fudan University, Shanghai, 200433, China;Shanghai Engineering Research Institute of Blockchain, Fudan University, Shanghai 200433, China"}], "References": []}, {"ArticleId": 78206190, "Title": "Multiantenna Receiver Signal Detection in AmBC Based on Cluster Analysis", "Abstract": "Abstract <p>Ambient backscatter communication (AmBC) has great application prospects in the green Internet of Things due to its shared nature of energy and spectrum. In this paper, we have done research on the signal detection problem in the AmBC system and proposed to use the k-means clustering analysis algorithm to detect the received backscattered signal. At the same time, in order to obtain a more obvious clustering center, this paper introduces a multi-antenna receiver channel detection mechanism. The proposed method can compare and analyze the energy received by the receiver antennas in an extremely short time slots. The channel state information between the reader and the backscatter tag is obtained, and the optimal communication channel is selected, thereby effectively improving the clustering effect. Finally, a large number of experimental simulations are provided to compare and analyze the corresponding BER performance to confirm our theoretical research.</p>", "Keywords": "", "DOI": "10.1093/comjnl/bxz143", "PubYear": 2020, "Volume": "63", "Issue": "6", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Computer, Taiyuan University of Technology, 209, Daxue Street, Jinzhong, Shanxi, China, 030600"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information and Computer, Taiyuan University of Technology, 209, Daxue Street, Jinzhong, Shanxi, China, 030600"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Information and Computer, Taiyuan University of Technology, 209, Daxue Street, Jinzhong, Shanxi, China, 030600"}], "References": []}, {"ArticleId": 78206720, "Title": "Development of deep learning method for lead content prediction of lettuce leaf using hyperspectral images", "Abstract": "The validity and reliability of visible–near infrared (Vis–NIR) hyperspectral imaging were investigated for the determination of lead concentration in lettuce leaves. Besides, a method involving wavelet transform and stacked auto-encoders (WT-SAE) is proposed to decompose the spectral data in the multi-scale transform and obtain the deep spectral features. The Vis–NIR hyperspectral images of 1120 lettuce leaf samples were obtained and the whole region of lettuce leaf sample spectral data was collected and preprocessed. In addition, WT-SAE the deep spectral features using db5 as wavelet basis function, and support vector machine regression (SVR) was used for regression modelling. Furthermore, the best prediction performances for detecting lead (Pb) concentration in lettuce leaves was obtained from raw data set, with coefficient of determination for calibration (<i>R</i><sub>c</sub><sup>2</sup>) of 0.9911, root mean square error for calibration (RMSEC) of 0.05187 <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/********.2019.1685721/20191208/images/tres_a_1685721_ilm0001.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/********.2019.1685721/20191208/images/tres_a_1685721_ilm0001.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> m g k g − 1 , coefficient of determination for prediction (<i>R</i><sub>p</sub><sup>2</sup>) of 0.9590, root mean square error for prediction (RMSEP) of 0.05587 <img src=\"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/********.2019.1685721/20191208/images/tres_a_1685721_ilm0002.gif\" alt=\"\" /> <img src=\"//:0\" alt=\"\" class=\"mml-formula\" data-formula-source=\"{\"type\" : \"image\", \"src\" : \"/na101/home/<USER>/publisher/tandf/journals/content/tres20/2020/tres20.v041.i06/********.2019.1685721/20191208/images/tres_a_1685721_ilm0002.gif\"}\" /> <img src=\"//:0\" alt=\"\" data-formula-source=\"{\"type\" : \"mathjax\"}\" /> m g k g − 1 and residual predictive deviation (RPD) of 3.251 using db5 as wavelet basis function with wavelet fifth layer decomposition. The results of this study indicated that WT-SAE can effectively select the optimal deep spectral features and Vis–NIR hyperspectral imaging has great potential for detecting lead content in lettuce leaves.", "Keywords": "", "DOI": "10.1080/********.2019.1685721", "PubYear": 2020, "Volume": "41", "Issue": "6", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Jiangsu University, Zhenjiang, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, Jiangsu University, Zhenjiang, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, Jiangsu University, Zhenjiang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, Jiangsu University, Zhenjiang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Jiangsu University, Zhenjiang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Food and Biological Engineering, Jiangsu University, Zhenjiang, China"}], "References": []}, {"ArticleId": 78206890, "Title": "A cross-modal multimedia retrieval method using depth correlation mining in big data environment", "Abstract": "<p>Cross-media retrieval is a technology aimed at breaking through the shackles of single-mode retrieval technology, which is limited to the same multimedia form. It is also hoped to be able to search each other across the media form. Comprehensive processing of different multimedia morphological data is an urgent problem to be solved in cross-media retrieval area, in other words, the semantic relationship between potential features should be mined, which will improve their similarity. To solve the above problems, a deep correlation mining method is proposed, which trains different media features by deep learning, and then fuses the correlation between the trained features to solve the heterogeneity between different features, which will make the features of different multimedia data comparable. On this basis, <PERSON>enberg-<PERSON> method is applied to solve the problem that deep learning is easy to fall into local minimum solution in gradient training. Experiments on different databases show that the proposed method is effective in the field of cross-media retrieval. Compared with other advanced multimedia retrieval methods, the proposed method has achieved better retrieval results.</p>", "Keywords": "Deep correlation mining; Cross-modal; Big data; Multimedia retrieval; Similarity; Levenberg-Mar<PERSON>rt method", "DOI": "10.1007/s11042-019-08238-0", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Pingdingshan University, Pingdingshan, China"}, {"AuthorId": 2, "Name": "Lu Miao", "Affiliation": "School of Software, Pingdingshan University, Pingdingshan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Pingdingshan University, Pingdingshan, China"}], "References": []}, {"ArticleId": 78206958, "Title": "Generalized Born radii computation using linear models and neural networks", "Abstract": "Motivation <p>Implicit solvent models play an important role in describing the thermodynamics and the dynamics of biomolecular systems. Key to an efficient use of these models is the computation of generalized Born (GB) radii, which is accomplished by algorithms based on the electrostatics of inhomogeneous dielectric media. The speed and accuracy of such computations are still an issue especially for their intensive use in classical molecular dynamics. Here, we propose an alternative approach that encodes the physics of the phenomena and the chemical structure of the molecules in model parameters which are learned from examples.</p> Results <p>GB radii have been computed using (i) a linear model and (ii) a neural network. The input is the element, the histogram of counts of neighbouring atoms, divided by atom element, within 16 Å. Linear models are ca. 8 times faster than the most widely used reference method and the accuracy is higher with correlation coefficient with the inverse of ‘perfect’ GB radii of 0.94 versus 0.80 of the reference method. Neural networks further improve the accuracy of the predictions with correlation coefficient with ‘perfect’ GB radii of 0.97 and ca. 20% smaller root mean square error.</p> Availability and implementation <p>We provide a C program implementing the computation using the linear model, including the coefficients appropriate for the set of Bondi radii, as Supplementary Material. We also provide a Python implementation of the neural network model with parameter and example files in the Supplementary Material as well.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btz818", "PubYear": 2020, "Volume": "36", "Issue": "6", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Informatics and Physics, University of Udine, Udine 33100, Italy;Faculty of Science, Cairo University, Giza 12613, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Science and Math Division, New York University at Abu Dhabi, PO Box 129188, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Informatics and Physics, University of Udine, Udine 33100, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Informatics and Physics, University of Udine, Udine 33100, Italy"}], "References": []}, {"ArticleId": 78207300, "Title": "Evolving graphs with semantic neutral drift", "Abstract": "Abstract \n We introduce the concept of Semantic Neutral Drift (SND) for genetic programming (GP), where we exploit equivalence laws to design semantics preserving mutations guaranteed to preserve individuals’ fitness scores. A number of digital circuit benchmark problems have been implemented with rule-based graph programs and empirically evaluated, demonstrating quantitative improvements in evolutionary performance. Analysis reveals that the benefits of the designed SND reside in more complex processes than simple growth of individuals, and that there are circumstances where it is beneficial to choose otherwise detrimental parameters for a GP system if that facilitates the inclusion of SND.", "Keywords": "Genetic programming; Evolutionary algorithms; Neutral drift; Semantic equivalence; Mutation operators; Graph programming", "DOI": "10.1007/s11047-019-09772-4", "PubYear": 2021, "Volume": "20", "Issue": "1", "JournalId": 1251, "JournalTitle": "Natural Computing", "ISSN": "1567-7818", "EISSN": "1572-9796", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of York, York, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of York, York, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of York, York, UK"}], "References": []}, {"ArticleId": 78207376, "Title": "RETRACTED ARTICLE: Improvement in Hadoop performance using integrated feature extraction and machine learning algorithms", "Abstract": "<p>Big Data has been a term used in datasets which are complex and large in such a way there are some traditional technologies of data processing which are not adequate. Big Data can revolutionize most aspects in society such as collection or management of data from Big Data which is challenging and also very complex. The Hadoop has been designed for processing a large amount of unstructured and complex data. It has provided with a large amount of storage for data along with the ability to be able to tackle unlimited and concurrent tasks or jobs. The selection of features is an extremely powerful technique in the reduction of dimensionality and is also the most important step in machine learning applications. In recent decades, data is getting larger in a progressive manner in terms of instances and numbers making it very hard to deal with the problem of feature selection. In order to cope with such an epoch of Big Data, there are some more new techniques that are required to address the problem in a more efficient manner. At the same time, the suitability of the algorithms currently used may not be applicable especially when the size of data is above hundreds of gigabytes. For the purpose of this work, the correlation-based feature selection along with mutual information-based methods of feature selection was used for improving the performance. The AdaBoost and the support vector machine based classifiers have been used for improving their accuracy. The results of the experiment prove that the method proposed was able to achieve better performance compared to that of the other methods.</p>", "Keywords": "Big Data; Hadoop system; MapReduce; Feature selection; Correlation-based feature selection (CFS); Mutual information (MI); AdaBoost and support vector machine (SVM)", "DOI": "10.1007/s00500-019-04453-x", "PubYear": 2020, "Volume": "24", "Issue": "1", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of CSE, Excel Engineering College, Komarapalayam, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, Excel Engineering College, Komarapalayam, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of IT, K S Rangasamy College of Technology, Tiruchengode, India"}], "References": []}, {"ArticleId": 78207402, "Title": "Point set registration based on feature point constraints", "Abstract": "<p>Point set registration is a fundamental task in computer graphics. We present a novel volumetric registration method for three-dimensional solid shapes. The input data include a pair of three-dimensional point sets: a point set of a complete bone and another one from an incomplete bone, such as a hand bone with a hole in the wrist. We achieve the registration by deforming the complete model toward the incomplete model in the guidance of feature point constraints. Our method first performs an initial alignment owing to given data in an arbitrary position, orientation and scale, and then performs a volumetric registration that utilizes as much volumetric information as possible. Our solution is more adaptive to different sceneries such as the volume data have foramen, outlier and hole, and more accurate in comparison with both state-of-the-art rigid and non-rigid registration algorithms.</p>", "Keywords": "Computer graphics; Point set registration; Point-based models; Volumetric registration", "DOI": "10.1007/s00371-019-01771-x", "PubYear": 2020, "Volume": "36", "Issue": "9", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, University of Jinan, Jinan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, University of Jinan, Jinan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, University of Jinan, Jinan, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, University of Jinan, Jinan, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, University of Jinan, Jinan, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computing Science and Information Engineering, Qilu Institute of Technology, Jinan, People’s Republic of China"}], "References": []}, {"ArticleId": 78207511, "Title": "Revealing the hidden features in traffic prediction via entity embedding", "Abstract": "<p>Models based on neural networks (NN) have been used widely and successfully in traffic prediction resulting in improved accuracy and efficiency in traffic flow, speed, passenger flow, and delay. Input data include continuous and discrete variables and these impact traffic changes both internally and externally. However, few studies have focused on discrete traffic-related variables in NN-based forecasting models. Inappropriate utilization of discrete variables may cause useful factors to become insignificant and lead to an inefficient forecasting model. In this paper, a NN-based model is used to predict traffic flow of a bike-sharing system in Suzhou, China. The model only uses external and discrete variables like weather, places of interest (POIs), and holiday periods. We applied both entity embedding and one-hot encoding for the data preprocessing of these variables. The results show that (1) Entity embedding can effectively increase the continuity of categorical variables and slightly improve the prediction efficiency for the NN model; and (2) The hidden relationship in variables can be identified through visual analysis, and the trained embedding vectors can also be used in traffic-related tasks.</p>", "Keywords": "Neural networks; Visualization; Traffic prediction; Entity embedding; One-hot encoding", "DOI": "10.1007/s00779-019-01333-x", "PubYear": 2021, "Volume": "25", "Issue": "1", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Transport Studies, Monash University, Clayton, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Qatar University, Doha, Qatar"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Transport Studies, Monash University, Clayton, Australia"}], "References": []}, {"ArticleId": 78207764, "Title": "The use of a weighted affective lexicon in a tutoring system to improve student motivation to learn", "Abstract": "<p>In recent years, several educational systems have integrated text-based affective feedback. However, analyses of the educational lexicon from the affectivity perspective are scarce. The provision of affective support by these systems is significant because it improves student motivation to learn. This study analyzes the use of a weighted affective lexicon in tutoring systems to improve student motivation. First, based on the suggestions of 166 undergraduate students, this study constructed and evaluated an educational lexicon in Spanish. Additionally, after considering the assessment of another group of 185 undergraduate students, this study provides an interpretation of words/phrases on a valence and arousal scale. The authors carried out a comprehensive analysis to categorize the words/phrases. Furthermore, this study integrates an affective lexicon in a tutoring system. Finally, this paper presents a review of the impacts of this system on the motivation of its potential users. The authors believe that the results of the present study will support the development of a highly adopted Intelligent Tutoring Systems (ITSs), which will benefit prospective users.</p>", "Keywords": "affective evaluation;affective lexicon;intelligent tutoring systems;student motivation", "DOI": "10.1111/exsy.12483", "PubYear": 2021, "Volume": "38", "Issue": "5", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma de Baja California, Baja California, Mexico; Instituto Tencnológico de Tijuana, Tijuana, Mexico"}, {"AuthorId": 2, "Name": "Reyes <PERSON>‐<PERSON>", "Affiliation": "Universidad Autónoma de Baja California, Baja California, Mexico"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad de Colima, Colima, Mexico"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma de Sinaloa Sinaloa, Mexico"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto Tencnológico de Tijuana, Tijuana, Mexico"}], "References": []}, {"ArticleId": 78207883, "Title": "Integrated computational intelligent paradigm for nonlinear electric circuit models using neural networks, genetic algorithms and sequential quadratic programming", "Abstract": "<p>In this paper, a novel application of biologically inspired computing paradigm is presented for solving initial value problem (IVP) of electric circuits based on nonlinear RL model by exploiting the competency of accurate modeling with feed forward artificial neural network (FF-ANN), global search efficacy of genetic algorithms (GA) and rapid local search with sequential quadratic programming (SQP). The fitness function for IVP of associated nonlinear RL circuit is developed by exploiting the approximation theory in mean squared error sense using an approximate FF-ANN model. Training of the networks is conducted by integrated computational heuristic based on GA-aided with SQP, i.e., GA-SQP. The designed methodology is evaluated to variants of nonlinear RL systems based on both AC and DC excitations for number of scenarios with different voltages, resistances and inductance parameters. The comparative studies of the proposed results with <PERSON>’s numerical solutions in terms of various performance measures verify the accuracy of the scheme. Results of statistics based on Monte-Carlo simulations validate the accuracy, convergence, stability and robustness of the designed scheme for solving problem in nonlinear circuit theory.</p>", "Keywords": "Artificial neural networks; Nonlinear systems; Nonlinear electric circuits; Genetic algorithms; Sequential quadratic programming", "DOI": "10.1007/s00521-019-04573-3", "PubYear": 2020, "Volume": "32", "Issue": "14", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Pakistan Institute of Engineering and Applied Sciences, Nilore, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Information Sciences, Pakistan Institute of Engineering and Applied Sciences, Nilore, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Biomedical Engineering, Centre for Health Technologies, Department of Engineering and IT, University of Technology, Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, COMSATS University Islamabad, Attock, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, COMSATS University Islamabad, Attock, Pakistan"}], "References": []}, {"ArticleId": 78208661, "Title": "Grad-CAM: Visual Explanations from Deep Networks via Gradient-Based Localization", "Abstract": "<p>We propose a technique for producing ‘visual explanations’ for decisions from a large class of Convolutional Neural Network (CNN)-based models, making them more transparent and explainable. Our approach—Gradient-weighted Class Activation Mapping (Grad-CAM), uses the gradients of any target concept (say ‘dog’ in a classification network or a sequence of words in captioning network) flowing into the final convolutional layer to produce a coarse localization map highlighting the important regions in the image for predicting the concept. Unlike previous approaches, Grad-CAM is applicable to a wide variety of CNN model-families: (1) CNNs with fully-connected layers ( e.g. VGG), (2) CNNs used for structured outputs ( e.g. captioning), (3) CNNs used in tasks with multi-modal inputs ( e.g. visual question answering) or reinforcement learning, all without architectural changes or re-training . We combine Grad-CAM with existing fine-grained visualizations to create a high-resolution class-discriminative visualization, Guided Grad-CAM, and apply it to image classification, image captioning, and visual question answering (VQA) models, including ResNet-based architectures. In the context of image classification models, our visualizations (a) lend insights into failure modes of these models (showing that seemingly unreasonable predictions have reasonable explanations), (b) outperform previous methods on the ILSVRC-15 weakly-supervised localization task, (c) are robust to adversarial perturbations, (d) are more faithful to the underlying model, and (e) help achieve model generalization by identifying dataset bias. For image captioning and VQA, our visualizations show that even non-attention based models learn to localize discriminative regions of input image. We devise a way to identify important neurons through Grad-CAM and combine it with neuron names (Bau et al. in Computer vision and pattern recognition, 2017) to provide textual explanations for model decisions. Finally, we design and conduct human studies to measure if Grad-CAM explanations help users establish appropriate trust in predictions from deep networks and show that Grad-CAM helps untrained users successfully discern a ‘stronger’ deep network from a ‘weaker’ one even when both make identical predictions. Our code is available at https://github.com/ramprs/grad-cam/ , along with a demo on CloudCV (Agrawal et al., in: Mobile cloud visual media computing, pp 265–290. Springer, 2015) ( http://gradcam.cloudcv.org ) and a video at http://youtu.be/COjUB9Izk6E .</p>", "Keywords": "Grad-CAM; Visual explanations; Visualizations; Explanations; Interpretability; Transparency", "DOI": "10.1007/s11263-019-01228-7", "PubYear": 2020, "Volume": "128", "Issue": "2", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, USA;Facebook AI Research, Menlo Park, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, USA;Facebook AI Research, Menlo Park, USA"}], "References": []}, {"ArticleId": 78208932, "Title": "Neural-network-based adaptive quasi-consensus of nonlinear multi-agent systems with communication constrains and switching topologies", "Abstract": "The quasi-consensus problem is investigated for a class of nonlinear multi-agent systems with communication constrains and switching topologies, where each agent is assumed to share information only with its neighbors on some disconnected time intervals, and the underlying topology is time-varying. Due to the approximation capability of neural networks, the uncertain nonlinear dynamics is compensated by the adaptive neural network scheme. A novel neural-network-based adaptive intermittent control protocol is proposed based on each agent maintaining a neural network parametric approximator. Some novel and simple quasi-consensus criteria are derived by the Lya<PERSON>nov stability theory and matrix analysis, it is proved that quasi-consensus can be reached if the measure of communication is larger than a threshold value. By the theoretical analysis, the consensus error can be reduced as small as desired. Then, the proposed method is used to consensus of multi-agent systems with known nonlinear dynamics. Finally, two simulation examples are provided to demonstrate the effectiveness of the obtained theoretical results.", "Keywords": "Multi-agent systems ; Quasi-consensus ; Adaptive intermittent control ; Neural networks ; Adaptive approximation", "DOI": "10.1016/j.nahs.2019.100833", "PubYear": 2020, "Volume": "35", "Issue": "", "JournalId": 5866, "JournalTitle": "Nonlinear Analysis: Hybrid Systems", "ISSN": "1751-570X", "EISSN": "1878-7460", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Information and Engineering , Jiaxing University, Zhejiang, 314001, PR China;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Information and Engineering , Jiaxing University, Zhejiang, 314001, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Mathematics and Information and Engineering , Jiaxing University, Zhejiang, 314001, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Mathematics and Systems Science, Shandong University of Science and Technology, Shandong, 266590, PR China"}], "References": []}, {"ArticleId": 78209002, "Title": "Droplet Combinations: A Scalable Microfluidic Platform for Biochemical Assays", "Abstract": "<p>Droplet-based microfluidics holds enormous potential for transforming high-throughput drug screening. Miniaturization through droplets in combination with automation contributes to reduce reagent use and analysis time as well as minimizing or eliminating labor-intensive steps leading to associated reductions in cost. In this paper, we demonstrate the potential of automated and cost-effective microfluidic droplet-generating technology in the context of an enzymatic activity assay for screening collagenase inhibitors. Experimental results show reproducible and accurate creation and mixing of droplet combinations resulting in biochemical data comparable to data produced by an industry standard instrument. This microfluidic platform that can generate and combine multiple droplets represents a promising tool for high-throughput drug screening.</p>", "Keywords": "droplets;drug combinations;high-throughput screening;microfluidics", "DOI": "10.1177/2472630319883830", "PubYear": 2020, "Volume": "25", "Issue": "2", "JournalId": 19198, "JournalTitle": "SLAS TECHNOLOGY: Translating Life Sciences Innovation", "ISSN": "2472-6303", "EISSN": "2472-6311", "Authors": [{"AuthorId": 1, "Name": "Finola E<PERSON>", "Affiliation": "Hooke Bio Ltd, Shannon, Co. Clare, Ireland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hooke Bio Ltd, Shannon, Co. Clare, Ireland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hooke Bio Ltd, Shannon, Co. Clare, Ireland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hooke Bio Ltd, Shannon, Co. Clare, Ireland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Hooke Bio Ltd, Shannon, Co. Clare, Ireland"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Hooke Bio Ltd, Shannon, Co. Clare, Ireland;University of Limerick, Bernal Institute, Limerick, Ireland"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Hooke Bio Ltd, Shannon, Co. Clare, Ireland;University of Limerick, Bernal Institute, Limerick, Ireland"}, {"AuthorId": 8, "Name": "Lorraine B. Bible", "Affiliation": "Hooke Bio Ltd, Shannon, Co. Clare, Ireland;University of Limerick, Bernal Institute, Limerick, Ireland"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Hooke Bio Ltd, Shannon, Co. Clare, Ireland"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "University of Limerick, Bernal Institute, Limerick, Ireland"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "University of Limerick, Bernal Institute, Limerick, Ireland"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Pharmacology and Therapeutics, Biomedical Sciences, Dangan, NUI Galway, Galway, Ireland"}, {"AuthorId": 13, "Name": "<PERSON><PERSON>", "Affiliation": "Pharmacology and Therapeutics, Biomedical Sciences, Dangan, NUI Galway, Galway, Ireland"}, {"AuthorId": 14, "Name": "<PERSON>", "Affiliation": "University of Limerick, Bernal Institute, Limerick, Ireland"}], "References": []}]