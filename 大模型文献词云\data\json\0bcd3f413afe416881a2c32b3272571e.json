[{"ArticleId": 114304395, "Title": "Математична модель формування векторного уявлення україномовного тексту", "Abstract": "<p>У статті розглядається система яка визначає тональність тексту надісланого користувачем. Метою даної статті є побудова математичної моделі обробки текстових повідомлень, та визначення настрою в поставленому реченні. Використання даного модулю визначення тональності україномовного тексту може відігравати ключову роль у різних галузях та має велику перспективу для подальшої модернізації та розвинення в різних сферах діяльності. У цій роботі розроблено ефективну модель та датасет для обробки моделлю машинного навчання BERT. Заборонована модель та функція досягла досить високих показників в точності визначення тональності та настрою користувача. Модуль, який запропоновано на даний момент реалізована в форматі боту для соціальної мережі, яка визначає тональність у реальному часі</p>", "Keywords": "vectorisation text vectorisation; tone detection system; sentence processing; work with Telegram", "DOI": "10.36910/6775-2524-0560-2024-54-22", "PubYear": 2024, "Volume": "", "Issue": "54", "JournalId": 71325, "JournalTitle": "КОМП’ЮТЕРНО-ІНТЕГРОВАНІ ТЕХНОЛОГІЇ: ОСВІТА, НАУКА, ВИРОБНИЦТВО", "ISSN": "2524-0552", "EISSN": "2524-0560", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>. <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114304483, "Title": "Reducing the number of LUTs for Mealy FSMs with state transformation", "Abstract": "", "Keywords": "", "DOI": "10.61822/amcs-2024-0012", "PubYear": 2024, "Volume": "34", "Issue": "1", "JournalId": 7288, "JournalTitle": "International Journal of Applied Mathematics and Computer Science", "ISSN": "", "EISSN": "2083-8492", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114304504, "Title": "Novel path planning method using marine predator algorithm for mobile robot", "Abstract": "<p>The main goal of robot path planning is to design an optimal path for a robot to navigate from its starting point to its goal while avoiding obstacles and optimizing certain criteria. A novel method using marine predator algorithm which is used in the field of robot path planning is presented. The proposed method has two steps. First step is to build a mathematical model of path planning while second step is optimization process using marine predator algorithm. Simulation results show that the proposed method works well and has good performance in different situations. Therefore, this method is an effective method for robot path planning and related applications.</p>", "Keywords": "autonomous driving; marine predator algorithm; metaheuristic; optimization; robot path planning", "DOI": "10.24425/acs.2024.149659", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 30654, "JournalTitle": "Archives of Control Sciences", "ISSN": "1230-2384", "EISSN": "2300-2611", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic and ElectricalEngineering, Bengbu University, Bengbu 233030, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information Engineering, Bengbu University, Bengbu233030, China"}], "References": []}, {"ArticleId": 114304564, "Title": "FPGA-SoC implementation of YOLOv4 for flying-object detection", "Abstract": "<p>Flying-object detection has become an increasingly attractive avenue for research, particularly with the rising prevalence of unmanned aerial vehicle (UAV). Utilizing deep learning methods offers an effective means of detection with high accuracy. Meanwhile, the demand to implement deep learning models on embedded devices is growing, fueled by the requirement for capabilities that are both real-time and power efficient. FPGA have emerged as the optimal choice for its parallelism, flexibility and energy efficiency. In this paper, we propose an FPGA-based design for YOLOv4 network to address the problem of flying-object detection. Our proposed design explores and provides a suitable solution for overcoming the challenge of limited floating-point resources while maintaining the accuracy and obtain real-time performance and energy efficiency. We have generated an appropriate dataset of flying objects for implementing, training and fine-tuning the network parameters base on this dataset, and then changing some suitable components in the YOLO networks to fit for the deployment on FPGA. Our experiments in Xilinx ZCU104 development kit show that with our implementation, the accuracy is competitive with the original model running on CPU and GPU despite the process of format conversion and model quantization. In terms of speed, the FPGA implementation with the ZCU104 kit is inferior to the ultra high-end GPU, the RTX 2080Ti, but outperforms the GTX 1650. In terms of power consumption, the FPGA implementation is significantly lower than the GPU GTX 1650 about 3 times and about 7 times lower than RTX 2080Ti. In terms of energy efficiency, FPGA is completely superior to GPU with 2–3 times more efficient than the RTX 2080Ti and 3–4 times that of the GTX 1650.</p>", "Keywords": "FPGA; YOLO; Neural network; UAV; Object detection; Vitis HLS", "DOI": "10.1007/s11554-024-01440-w", "PubYear": 2024, "Volume": "21", "Issue": "3", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Hanoi University of Science and Technology, Hanoi, Vietnam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Control Automation in Production and Improvement of Technology Institute, Hanoi, Vietnam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Hanoi University of Science and Technology, Hanoi, Vietnam; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Hanoi University of Science and Technology, Hanoi, Vietnam"}], "References": [{"Title": "Pruning filters with L1-norm and capped L1-norm for CNN compression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "2", "Page": "1152", "JournalTitle": "Applied Intelligence"}, {"Title": "Resource-constrained FPGA implementation of YOLOv2", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "19", "Page": "16989", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An automatic fire detection system based on deep convolutional neural networks for low-power, resource-constrained devices", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "18", "Page": "15349", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Object detection using YOLO: challenges, architectural successors, datasets and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "6", "Page": "9243", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 114304592, "Title": "Paper-based microfluidic chip for sweat volume, ECG and EMG monitoring", "Abstract": "With the development of wearable devices, the application of non-invasive wearable devices to monitor sweat in real time is the most important for human health. Here, a paper-based microfluidic chip for monitoring of sweat loss during exercise is proposed. Highly absorbent rice paper is selected as the paper-based material, and the capillary action of the paper base is used to actively adsorb sweat, and it can be used repeatedly. As the electrode material, the conductive cloth has good tensile properties and bending resistance, which ensures that the change of resistance is caused by the amount of sweat entering. Other materials of the chip are highly hydrophobic, ensuring that all sweat will be absorbed by the paper base. The sweat volume monitored by the microfluidic chip in real time is 0.5μl-225μl. Compared with rigid electrodes and wet electrodes, the application of flexible electrodes allows the chip to fit well with the skin, the ECG waveform is clearer and has greater amplitude, and the EMG is smoother and has a greater signal-to-noise ratio.", "Keywords": "", "DOI": "10.1016/j.sna.2024.115264", "PubYear": 2024, "Volume": "371", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Northeast Electric Power University, China"}, {"AuthorId": 2, "Name": "Gang Li", "Affiliation": "Northeast Electric Power University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northeast Electric Power University, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Jilin University of Medicine, China"}, {"AuthorId": 5, "Name": "Hongxu Pan", "Affiliation": "Northeast Electric Power University, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Northeast Electric Power University, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON> Chen", "Affiliation": "Northeast Electric Power University, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northeast Electric Power University, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Northeast Electric Power University, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Northeast Electric Power University, China;Corresponding author"}], "References": [{"Title": "Microfluidic paper-based wearable electrochemical biosensor for reliable cortisol detection in sweat", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "379", "Issue": "", "Page": "133258", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 114304767, "Title": "Crisis talk: analysis of the public debate around the energy crisis and cost of living", "Abstract": "<p>A prominent media topic in the UK in the early 2020s is the energy crisis affecting the UK and most of Europe. It brings into a single public debate issues of energy dependency and sustainability, fair distribution of economic burdens and cost of living, as well as climate change, risk, and sustainability. In this paper, we investigate the public discourse around the energy crisis and cost of living to identify how these pivotal and contradictory issues are reconciled in this debate and to identify which social actors are involved and the role they play. We analyse a document corpus retrieved from UK newspapers from January 2014 to March 2023. We apply a variety of natural language processing and data visualisation techniques to identify key topics, novel trends, critical social actors, and the role they play in the debate, along with the sentiment associated with those actors and topics. We combine automated techniques with manual discourse analysis to explore and validate the insights revealed in this study. The findings verify the utility of these techniques by providing a flexible and scalable pipeline for discourse analysis and providing critical insights for cost of living—energy crisis nexus research.</p>", "Keywords": "Energy crisis; Climate change; Media; Discourse; NLP", "DOI": "10.1007/s13278-024-01233-w", "PubYear": 2024, "Volume": "14", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Goldsmiths, University of London, London, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, University of London, London, UK; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON>", "Affiliation": "Goldsmiths, University of London, London, UK"}], "References": [{"Title": "The InsightsNet Climate Change Corpus (ICCC): Compiling a Multimodal Corpus of Discourses in a Multi-Disciplinary Domain", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "3", "Page": "177", "JournalTitle": "Datenbank-Spektrum"}]}, {"ArticleId": *********, "Title": "Situation modeling and evaluation for complex systems: A case study of satellite attitude control system", "Abstract": "Because the dependence, competition, correlation, and other complex interactions among the components and between system and environment, the complex system is difficult to be modeled directly. As an important spacecraft system, satellite needs to meet the high reliability demand. However, the increasingly complex satellite system structure and complex space environment interference make the on-orbit satellite failure inevitable. Modeling and evaluation of satellite conditions can identify potential problems of key system components in time. Firstly, in order to accurately describe the system components and measure the reliability, the correlative damage coefficient is introduced to describe the component degradation process, and the component reliability model of the satellite attitude control system is established. Then, to solve the problem of large state space and changeable condition of system reliability model, the concept of tangent order pair is introduced into the multi-state dynamic fault tree of system. The depth-first search algorithm is used to sort the underlying event components of the fault tree, and the multi-state multi-value decision graph model of different state events is constructed. The minimum cut set of the model is obtained to evaluate the system state. The experimental results show that the proposed method can effectively simulate the system situation under different fault modes, effectively reduce the state space of the system reliability model, and improve the convergence and accuracy of the system fault probability evaluation.", "Keywords": "Satellite attitude control system; Situation modeling; Reliability assessment; Fault tree; Multi-state multi-value decision diagram", "DOI": "10.1016/j.aei.2024.102505", "PubYear": 2024, "Volume": "61", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Civil Aviation College, Shenyang Aerospace University, Shenyang 110136, China;Henan Shijia Photons Technology Co., Ltd., Hebi 458030, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Civil Aviation College, Shenyang Aerospace University, Shenyang 110136, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Civil Aviation College, Shenyang Aerospace University, Shenyang 110136, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Civil Aviation College, Shenyang Aerospace University, Shenyang 110136, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University, Shenyang 110819, China"}], "References": [{"Title": "Multi-domain ubiquitous digital twin model for information management of complex infrastructure systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "101951", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 114304984, "Title": "Improved Lightweight Head Detection Based on GhostNet-SSD", "Abstract": "This abstract proposes an algorithm for human head detection in elevator cabins that addresses the challenges of improving detection accuracy, reducing detection speed, and decreasing the number of parameters. The algorithm is based on GhostNet-SSD and includes several improvements, such as an efficient coordinate attention mechanism to replace the Squeeze-and-Excitation attention mechanism, optimization of auxiliary convolutional layer with large parameter weight, and adjustment of anchor ratio based on the statistical results of human head labeling frame. In addition, data normalization and convolutional fusion methods are used for inference acceleration. The algorithm was tested on JETSON XAVIER NX development board and achieved a new state-of-the-art 97.91% AP at 61FPS, outperforming other detectors with similar inference speed. The effectiveness of each component was validated through careful experimentation.", "Keywords": "Human head detection; GhostNet-SSD; Attention mechanism; Convolution neural network; Computer vision", "DOI": "10.1007/s11063-024-11563-7", "PubYear": 2024, "Volume": "56", "Issue": "2", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information, Guizhou University of Finance and Economics, Guiyang, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Guo", "Affiliation": "School of Information, Guizhou University of Finance and Economics, Guiyang, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information, Guizhou University of Finance and Economics, Guiyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information, Guizhou University of Finance and Economics, Guiyang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Intelligent Manufacturing, Shunde Polytechnic, Shunde, China; Corresponding author."}], "References": [{"Title": "Improving waiting time and energy consumption performance of a bi-objective genetic algorithm embedded in an elevator group control system through passenger flow estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "24", "Page": "13673", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 114305029, "Title": "Using dynamic knowledge graphs to detect emerging communities of knowledge", "Abstract": "Knowledge graphs represent relationships between entities. These graphs can take dynamic forms to trace changes along time through text models and further used by reasoning systems with the intent to answer queries. In this research we explore their applicability for extracting temporal patterns of knowledge in the form of communities. To this end, we propose a method for generating knowledge relationships over unconnected components of a knowledge graph, allowing for a targeted exploration of emerging contents in corpora. This analysis is applied to the corpora of the Conference on Knowledge Discovery and Data Mining (KDD) publications over the last decade. We find the key knowledge communities over time and rank the underlying concepts. Results show that the publication efforts increasingly focus on graph research and the creation of relationships instead of new concepts. The acquired results confirm the validity of the proposed knowledge discovery methodology for community-centered analysis of emerging changes in dynamic knowledge graphs.", "Keywords": "Knowledge graphs; Dynamic; Community finding; Network science; Text2kg; KDD", "DOI": "10.1016/j.knosys.2024.111671", "PubYear": 2024, "Volume": "294", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "Joao T. <PERSON>", "Affiliation": "INESC-ID and Instituto Superior Tecnico, Universidade de Lisboa, Lisbon, Portugal;Department of Transport, LNEC, Lisbon, Portugal;Corresponding author at: INESC-ID and Instituto Superior Tecnico, Universidade de Lisboa, Lisbon, Portugal"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Transport, LNEC, Lisbon, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INESC-ID and Instituto Superior Tecnico, Universidade de Lisboa, Lisbon, Portugal"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "INESC-ID and Instituto Superior Tecnico, Universidade de Lisboa, Lisbon, Portugal"}], "References": [{"Title": "A review: Knowledge reasoning over knowledge graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112948", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Online summarization of dynamic graphs using subjective interestingness for sequential data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "1", "Page": "88", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Generating knowledge graphs by employing Natural Language Processing and Machine Learning techniques within the scholarly domain", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "253", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A Comprehensive Survey of Knowledge Graph-Based Recommender Systems: Technologies, Development, and Contributions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "6", "Page": "232", "JournalTitle": "Information"}, {"Title": "Creating knowledge graphs for geographic data on the web", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "Winter", "Page": "1", "JournalTitle": "ACM SIGWEB Newsletter"}, {"Title": "Efficiently embedding dynamic knowledge graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "250", "Issue": "", "Page": "109124", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 114305077, "Title": "Machine learning with a reject option: a survey", "Abstract": "<p>Machine learning models always make a prediction, even when it is likely to be inaccurate. This behavior should be avoided in many decision support applications, where mistakes can have severe consequences. <PERSON><PERSON><PERSON> already studied in 1970, machine learning with rejection recently gained interest. This machine learning subfield enables machine learning models to abstain from making a prediction when likely to make a mistake. This survey aims to provide an overview on machine learning with rejection. We introduce the conditions leading to two types of rejection, ambiguity and novelty rejection, which we carefully formalize. Moreover, we review and categorize strategies to evaluate a model’s predictive and rejective quality. Additionally, we define the existing architectures for models with rejection and describe the standard techniques for learning such models. Finally, we provide examples of relevant application domains and show how machine learning with rejection relates to other machine learning research areas.</p>", "Keywords": "Machine learning with rejection; Supervised learning; Trustworthy machine learning; 68T05; 68T02", "DOI": "10.1007/s10994-024-06534-x", "PubYear": 2024, "Volume": "113", "Issue": "5", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Siemens Digital Industries Software, Leuven, Belgium; Department of Computer Science, KU Leuven, Leuven, Belgium; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, KU Leuven, Leuven, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, KU Leuven, Leuven, Belgium; OSG bv, Natus Medical, Kontich, Belgium; University of Antwerp, Antwerp, Belgium"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "KU Leuven; Leuven.AI, Leuven, Belgium"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "KU Leuven; Leuven.AI, Leuven, Belgium"}], "References": [{"Title": "Deep neural rejection against adversarial examples", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "1", "Page": "1", "JournalTitle": "EURASIP Journal on Information Security"}, {"Title": "Probabilistic personalised cascade with abstention", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "147", "Issue": "", "Page": "8", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Multilabel Classification with Partial Abstention: Bayes-Optimal Prediction under Label Independence", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "72", "Issue": "", "Page": "613", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Bounded-abstaining classification for breast tumors in imbalanced ultrasound images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "2", "Page": "", "JournalTitle": "International Journal of Applied Mathematics and Computer Science"}]}, {"ArticleId": 114305101, "Title": "Вплив використання штучного інтелекту на управління ризиками в проєктах: можливості та виклики", "Abstract": "<p>У статті досліджено важливість і необхідність використання штучного інтелекту в управлінні. Ефективність управління завжди визначає успіх будь-якого проєкту, а помилки при прийнятті адміністративних рішень можуть бути важливими, особливо на ранніх стадіях створення, розробки або планування. Окремі проєкти здебільшого є унікальними подіями, які дуже важко стандартизувати, але це процеси, які значною мірою охоплюють управління. З'являється нова модель управління проєктами, заснована на обов'язковому використанні програмного забезпечення на базі штучного інтелекту для автоматизації управлінської діяльності, яка враховує традиційний підхід до управління проєктами. Розглянуто особливості спеціальних програмних елементів, що забезпечують ефективне використання цифрових інструментів управління проєктами. Зʼясовано, що в умовах сучасності штучний інтелект – перспективна технологія, яка може ефективно впливати на процеси управління, прийняття рішень та комунікацію з громадськістю. Зазначено, що крім перспективних ознак, використання штучного інтелекту характеризується і великими ризиками. Визначено поняття «штучного інтелекту» в контексті управління, а також акцентовано на його важливості для сучасного суспільства в цілому. В управлінні штучний інтелект характеризується такими властивостями, як автоматизація та оптимізація процесів, удосконалення процесу прийняття рішень на основі аналізу великих обсягів даних. У основних результатах дослідження наведено приклади успішного впровадження штучного інтелекту у сфері управління проєктами. Штучний інтелект корисний інструмент для автоматизації щоденних операцій, аналізу даних та прогнозування, аналізу політики, покращення обслуговування громадян, планування ресурсів, підтримки прийняття рішень та багатьох інших аспектів управління.</p>", "Keywords": "artificial intelligence; management process; risks and opportunities; decision-making; current state of development", "DOI": "10.36910/6775-2524-0560-2024-54-33", "PubYear": 2024, "Volume": "", "Issue": "54", "JournalId": 71325, "JournalTitle": "КОМП’ЮТЕРНО-ІНТЕГРОВАНІ ТЕХНОЛОГІЇ: ОСВІТА, НАУКА, ВИРОБНИЦТВО", "ISSN": "2524-0552", "EISSN": "2524-0560", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114305192, "Title": "A Method for Detecting Abnormal Nodes in Wireless Networks Based on Graph Signal Processing", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.143070", "PubYear": 2024, "Volume": "14", "Issue": "3", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "治斌 张", "Affiliation": ""}], "References": []}, {"ArticleId": 114305197, "Title": "A new multi-attribute group decision-making method based on probabilistic multi-valued linguistic spherical fuzzy sets for the site selection of charging piles", "Abstract": "<p>Motivated by the concepts of low carbon and environmental protection, electric vehicles have received much attention and become more and more popular all around the world. The expanding demand for electric vehicles has driven the rapid development of the charging pile industry. One of the prominent issues in charging pile industry is to determine their sites, which is a complex decision-making problem. As a matter of factor, the process of charging piles sites selection can be regarded as multi-attribute group decision-making (MAGDM), which is the main topic of this paper. The recently proposed linguistic spherical fuzzy sets (LSFSs) composed of the linguistic membership degree, linguistic abstinence degree and linguistic non-membership degree are powerful tools to express the evaluation information of decision makers (DMs). Based on the concept of LSFSs, we introduce probabilistic multi-valued linguistic spherical fuzzy sets (PMVLSFSs), which can describe DMs’ fuzzy evaluation information in a more refined and accurate way. The operation rules of PMVLSFSs are also developed in this article. To effectively aggregate PMVLSFSs, the probabilistic multi-valued linguistic spherical fuzzy power generalized Maclaurin symmetric mean operator and the probabilistic multi-valued linguistic spherical fuzzy power weighted generalized Maclaurin symmetric mean are put forward. Based on the above aggregation operators, a new method for MAGDM problem with PMVLSFSs is established. Further, a practical case of suitable site selection of charging pile is used to verify the practicability of this method. Lastly, comparative analysis with other methods is performed to illustrate the advantages and stability of proposed method.</p>", "Keywords": "generalized <PERSON><PERSON><PERSON> symmetric mean; power generalized <PERSON><PERSON><PERSON> symmetric mean; site selection of charging piles, probabilistic multi-valued linguistic spherical fuzzy sets", "DOI": "10.24425/acs.2024.149657", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 30654, "JournalTitle": "Archives of Control Sciences", "ISSN": "1230-2384", "EISSN": "2300-2611", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Beĳing Jiaotong University, Beĳing, China;Beĳing Logistics Informatics Research Base, Beĳing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Beĳing Jiaotong University, Beĳing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, BeihangUniversity, Beĳing, China"}], "References": []}, {"ArticleId": 114305228, "Title": "Intelligent Machine Learning Based Brain Tumor Segmentation through Multi-Layer Hybrid U-Net with CNN Feature Integration", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.047917", "PubYear": 2024, "Volume": "79", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A Survey on the Explainability of Supervised Machine Learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "70", "Issue": "", "Page": "245", "JournalTitle": "Journal of Artificial Intelligence Research"}]}, {"ArticleId": 114305422, "Title": "Evaluating ChatGPT’s Consciousness and Its Capability to Pass the Turing Test: A Comprehensive Analysis", "Abstract": "This study explores the capabilities of ChatGPT, specifically in relation to consciousness and its performance in the Turing Test. The article begins by examining the diverse perspectives among both the cognitive and AI researchers regarding ChatGPT’s ability to pass the Turing Test. It introduces a hierarchical categorization of the test versions, suggesting that ChatGPT approaches success in the test, albeit primarily with na?ve users. Expert users, conversely, can easily identify its limitations. The paper presents various theories of consciousness, with a particular focus on the Integrated Information Theory proposed by <PERSON><PERSON><PERSON>. This theory serves as the framework for assessing ChatGPT’s level of consciousness. Through an evaluation based on the five axioms and theorems of IIT, the study finds that ChatGPT surpasses previous AI systems in certain aspects; however, ChatGPT significantly falls short of achieving a level of consciousness, particularly when compared to biological sentient beings. The paper concludes by emphasizing the importance of recognizing ChatGPT and similar generative AI models as highly advanced and intelligent tools, yet distinctly lacking the consciousness attributes found in advanced living organisms.", "Keywords": "Cognitive Science;Integrated Information Theory;Artificial Intelligence;Large Language Models", "DOI": "10.4236/jcc.2024.123014", "PubYear": 2024, "Volume": "12", "Issue": "3", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Intelligent Systems, Jozef Stefan Institute, Ljubljana, Slovenia .; Jozef <PERSON> Postgraduate School, Jozef Stefan Institute, Ljubljana, Slovenia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Intelligent Systems, Jozef Stefan Institute, Ljubljana, Slovenia"}], "References": [{"Title": "GPT-3: Its Nature, Scope, Limits, and Consequences", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "4", "Page": "681", "JournalTitle": "Minds and Machines"}, {"Title": "The Turing Test is a Thought Experiment", "Authors": "Bernardo <PERSON>", "PubYear": 2023, "Volume": "33", "Issue": "1", "Page": "1", "JournalTitle": "Minds and Machines"}, {"Title": "ChatGPT: Jack of all trades, master of none", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101861", "JournalTitle": "Information Fusion"}, {"Title": "ChatGPT: deconstructing the debate and moving it forward", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "39", "Issue": "5", "Page": "2221", "JournalTitle": "AI & SOCIETY"}, {"Title": "“Personhood and AI: Why large language models don’t understand us”", "Authors": "<PERSON>", "PubYear": 2024, "Volume": "39", "Issue": "5", "Page": "2499", "JournalTitle": "AI & SOCIETY"}, {"Title": "Fluent but Not Factual: A Comparative Analysis of ChatGPT and Other AI Chatbots’ Proficiency and Originality in Scientific Writing for Humanities", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "10", "Page": "336", "JournalTitle": "Future Internet"}]}, {"ArticleId": 114305521, "Title": "Improving query processing in blockchain systems by using a multi-level sharding mechanism", "Abstract": "<p>With the distributed and decentralized nature of blockchain, and with its sequential data access, query processing emerges as a challenging issue in the blockchain systems. These features hinder efficient query processing and make it difficult to guarantee the validity and privacy-preserving of query results. Several solutions have been proposed to tackle the efficiency, reliability, and privacy challenges of query processing in blockchain systems. There has been rarely a comprehensive solution addressing all of these issues. In addition, the existing solutions often assume that the blockchain nodes are homogeneous in terms of their capabilities and available resources, while the blockchain nodes can have heterogeneous computational, communication, and storage resources, and can also contribute to the blockchain network in different manners. This work, considering the heterogeneity of network nodes, introduces a multi-level and score-based sharding solution for query processing where the nodes are organized into a hierarchical tree-like structure based on their score and store a proportion of transaction data in a DAG-based data structure resulting in an efficient query time. Additionally, the nodes reach a consensus over the query results from the bottom to the top of the hierarchical structure enabling reliable and fast query processing. The experiments conducted during the evaluation show that the efficiency of the proposed work is near that of relational databases in terms of query response time. It also provides a high validity rate taking advantage of its hierarchical consensus mechanism and preserves the privacy of query results using a delegation-based integration method where the final query result is integrated by the client’s representative.</p>", "Keywords": "Query processing; Blockchain; Sharding; Consensus; Heterogeneity", "DOI": "10.1007/s11227-024-06037-5", "PubYear": 2024, "Volume": "80", "Issue": "10", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, South Tehran Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, South Tehran Branch, Islamic Azad University, Tehran, Iran; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, South Tehran Branch, Islamic Azad University, Tehran, Iran"}], "References": [{"Title": "Revealing Every Story of Data in Blockchain Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "49", "Issue": "1", "Page": "70", "JournalTitle": "ACM SIGMOD Record"}, {"Title": "Trusted data sharing with flexible access control based on blockchain", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "78", "Issue": "", "Page": "103543", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "A blockchain index structure based on subchain query", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "A blockchain-based secure storage and access control scheme for supply chain finance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "1", "Page": "109", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "On efficient top-k transaction path query processing in blockchain database", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "141", "Issue": "", "Page": "102079", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "An effective method for the protection of user health topic privacy for health information services", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "6", "Page": "3837", "JournalTitle": "World Wide Web"}]}, {"ArticleId": 114305549, "Title": "A survey on multi-lingual offensive language detection", "Abstract": "<p>The prevalence of offensive content on online communication and social media platforms is growing more and more common, which makes its detection difficult, especially in multilingual settings. The term “Offensive Language” encompasses a wide range of expressions, including various forms of hate speech and aggressive content. Therefore, exploring multilingual offensive content, that goes beyond a single language, focus and represents more linguistic diversities and cultural factors. By exploring multilingual offensive content, we can broaden our understanding and effectively combat the widespread global impact of offensive language. This survey examines the existing state of multilingual offensive language detection, including a comprehensive analysis on previous multilingual approaches, and existing datasets, as well as provides resources in the field. We also explore the related community challenges on this task, which include technical, cultural, and linguistic ones, as well as their limitations. Furthermore, in this survey we propose several potential future directions toward more efficient solutions for multilingual offensive language detection, enabling safer digital communication environment worldwide.</p>", "Keywords": "Hate speech;Literature review;Multilingualism;Offensive language;Social media", "DOI": "10.7717/peerj-cs.1934", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samovar, Telecom SudParis, Institut Polytechnique de Paris, Palaiseau, France."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Samovar, Telecom SudParis, Institut Polytechnique de Paris, Palaiseau, France."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Samovar, Telecom SudParis, Institut Polytechnique de Paris, Palaiseau, France."}, {"AuthorId": 4, "Name": "Praboda Rajapaksha", "Affiliation": "Samovar, Telecom SudParis, Institut Polytechnique de Paris, Palaiseau, France."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Samovar, Telecom SudParis, Institut Polytechnique de Paris, Palaiseau, France."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Samovar, Telecom SudParis, Institut Polytechnique de Paris, Palaiseau, France."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Samovar, Telecom SudParis, Institut Polytechnique de Paris, Palaiseau, France."}], "References": [{"Title": "Hate Speech Detection on Multilingual Twitter Using Convolutional Neural Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "1", "Page": "81", "JournalTitle": "Revue d'intelligence artificielle"}, {"Title": "Cross-lingual learning for text processing: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113765", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Survey of Multilingual Neural Machine Translation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Resources and benchmark corpora for hate speech detection: a systematic review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "55", "Issue": "2", "Page": "477", "JournalTitle": "Language Resources and Evaluation"}, {"Title": "Online Multilingual Hate Speech Detection: Experimenting with Hindi and English Social Media", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "1", "Page": "5", "JournalTitle": "Information"}, {"Title": "Challenges of Hate Speech Detection in Social Media", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Bangla hate speech detection on social media using attention-based recurrent neural network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "1", "Page": "578", "JournalTitle": "Journal of Intelligent Systems"}, {"Title": "Towards generalisable hate speech detection: a review on obstacles and solutions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Investigating cross-lingual training for offensive language detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "2", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Automatic Detection of Cyberbullying and Abusive Language in Arabic Content on Social Networks: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "189", "Issue": "", "Page": "156", "JournalTitle": "Procedia Computer Science"}, {"Title": "A multilingual offensive language detection method based on transfer learning from transformer fine-tuning model", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; Noureddine <PERSON> Nahnahi", "PubYear": 2022, "Volume": "34", "Issue": "8", "Page": "6048", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "An Evaluation of Multilingual Offensive Language Identification Methods for the Languages of India", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "306", "JournalTitle": "Information"}, {"Title": "Towards multidomain and multilingual abusive language detection: a survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "1", "Page": "17", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "Arabic Offensive and Hate Speech Detection Using a Cross-Corpora Multi-Task Learning Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "4", "Page": "69", "JournalTitle": "Informatics"}, {"Title": "Multilingual Offensive Language Identification for Low-resource Languages", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Hate speech detection on Twitter using transfer learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "101365", "JournalTitle": "Computer Speech & Language"}, {"Title": "Cross-lingual offensive speech identification with transfer learning for low-resource languages", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "108005", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Predicting the type and target of offensive social media posts in Marathi", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Detecting offensive speech in conversational code-mixed dialogue on social media: A contextual dataset and benchmark experiments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119342", "JournalTitle": "Expert Systems with Applications"}, {"Title": "HateCircle and Unsupervised Hate Speech Detection incorporating Emotion and Contextual Semantic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "A literature survey on multimodal and multilingual automatic hate speech identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "3", "Page": "1203", "JournalTitle": "Multimedia Systems"}, {"Title": "Label modification and bootstrapping for zero-shot cross-lingual hate speech detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "57", "Issue": "4", "Page": "1515", "JournalTitle": "Language Resources and Evaluation"}, {"Title": "Enhancing social network hate detection using back translation and GPT-3 augmentations during training and test-time", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101887", "JournalTitle": "Information Fusion"}, {"Title": "Arabic Toxic Tweet Classification: Leveraging the AraBERT Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "4", "Page": "170", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "The effect of rebalancing techniques on the classification performance in cyberbullying datasets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "3", "Page": "1049", "JournalTitle": "Neural Computing and Applications"}, {"Title": "RETRACTED ARTICLE: Multilingual hate speech detection sentimental analysis on social media platforms using optimal feature extraction and hybrid diagonal gated recurrent neural network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "17", "Page": "19515", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 114305568, "Title": "Scaling private proximity testing protocols for geofenced information exchange: A metropolitan-wide case study", "Abstract": "Private Proximity Testing (PPT) protocols allow two entities to exchange location-and-time-specific information without any tangible breach of privacy between the two or any third-party intermediary such as a server or a service provider. Numerous PPT protocols have been proposed, however, to our knowledge, experimentation relies on simulations and algorithmic complexity analysis, without tests on large-scale scenarios and actual information exchange over wide metropolitan areas. In this paper, we implement and evaluate a novel PPT geofencing algorithm based on RSA factorization. The prototype used in this work was built in Android and Kotlin, initially funded by Google Digital News Initiative Innovation Fund (digitalnewsinitiative.com). We utilize WoM to implement and test a geofenced PPT protocol using prime factorization over a wide area while users move around different municipalities and (i) present findings concerning efficiency and feasibility of prime factorization PPT implementations over large geographical distributions in terms of energy and data needs, (ii) evaluate complementary technologies needed for such private distribution of location-and-time-specific information like geospatial fences and fragmentation and (iii) consider numerous legal considerations under the EU legal context.", "Keywords": "", "DOI": "10.1016/j.comnet.2024.110381", "PubYear": 2024, "Volume": "245", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Info. and Comm. Systems Engineering, University of the Aegean, Samos, 83200, Greece;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, University of Piraeus, Piraeus, 18534, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Info. and Comm. Systems Engineering, University of the Aegean, Samos, 83200, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Info. and Comm. Systems Engineering, University of the Aegean, Samos, 83200, Greece"}], "References": []}, {"ArticleId": *********, "Title": "iCardo 3.0: ECG-Based Prediction of Conduction Disturbances Using Demographic Features", "Abstract": "<p>Cardiovascular disease (CVD) is one of the most contributing diseases to premature mortality around the globe. Low- and middle-income countries like India account for almost 80% of global CVD fatalities. Predicting cardiovascular diseases at an early stage can improve the quality of life. Electrocardiography (ECG) is one of the non-invasive methods to assess heart function disorders and CVDs. The paper presents the prediction of conduction disturbance or disorders (CD) through a 12-lead electrocardiogram (ECG) that leads to chronic heart failure or cardiac arrest. Three ensemble machine learning models i.e., random forest (RF), XGBoost, and the support vector machine, are used to classify the conduction disturbance subjects from the ‘normal’ subjects. In addition to this, the paper also presents a comparative study to show the effect of two demographic features, ‘age’ and ‘sex’ on the prediction of conduction disturbance’s subjects. The performance of the classifiers’ is measured in terms of accuracy, precision, recall, and F1 score. Tenfold cross-validation is utilised, and the receiver operating curve is traced for each of the combinations of tenfold cross-validation. The performance is measured with a confusion matrix for all three classifiers. The performance with RF and XGBoost performance is similar in terms of accuracy, whereas the total number of true predictions is higher in the case of RF. The proposed model would be useful for continuous monitoring and prediction conduction disturbance in the smart healthcare framework.</p>", "Keywords": "Machine learning; Cardiovascular disease; Health failure; Conduction disturbance; Electrocardiography (ECG); Smart healthcare", "DOI": "10.1007/s42979-024-02701-y", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics and Communication Engineering, Malaviya National Institute of Technology, Jaipur, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics and Communication Engineering, Malaviya National Institute of Technology, Jaipur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, University of North Texas, Denton, USA"}], "References": [{"Title": "A smart healthcare monitoring system for heart disease prediction based on ensemble deep learning and feature fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "208", "JournalTitle": "Information Fusion"}, {"Title": "A new smart healthcare framework for real-time heart disease detection based on deep and machine learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Smart Healthcare: Disease Prediction Using the Cuckoo-Enabled Deep Classifier in IoT Framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}, {"Title": "Automated Detection of Left Bundle Branch Block from ECG Signal Utilizing the Maximal Overlap Discrete Wavelet Transform with ANFIS", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "6", "Page": "93", "JournalTitle": "Computers"}, {"Title": "An RHMIoT Framework for Cardiovascular Disease Prediction and Severity Level Using Machine Learning and Deep Learning Algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Ambient Computing and Intelligence"}, {"Title": "Improved Smart Healthcare System of Cloud-Based IoT Framework for the Prediction of Heart Disease", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "52", "Issue": "2", "Page": "529", "JournalTitle": "Information Technology And Control"}]}, {"ArticleId": 114305615, "Title": "Eco-design tool to support the design of industrial electric vehicles. The case studies of an electric shuttle and an autonomous mobile robot", "Abstract": "The benefits of process optimization brought by multiple tools that appeared in shopfloors with the fourth industrial revolution are undiscussed; however, they need electricity to run and require critical materials. Additionally, the significant impact on sustainability that early design decisions can have over the entire lifecycle is well-recognized. The literature counts several environmental analyses of electric vehicles but narrows almost uniquely on passengers’ cars. Currently, the literature should i) enwiden the range of analyzed products, ii) consider all stages of the product life cycle, iii) provide tools suitable for the early stage of design, able to return consistent results handling very little data. As electrification is concerned, in the literature there are approaches intended to assess the environmental impacts or focused on the design tool. The proposed approach, further applied to develop an eco-design tool, overcomes the existing literature by providing a tool i) able to handle few data, ii) that considers all the product lifecycle phases, and iii) allows designers to assess and compare alternative scenarios. A method is proposed, and a tool derived. Two applications concern an electric shuttle and an autonomous mobile robot; with the latter the gap of assessing the environmental impact of autonomous mobile robots is also filled. The obtained results are reasonably comparable with other existing works. Results are compared to a full LCA for the frame assembly and prove that i) the tool is reliable, and it more likely overestimates the impacts; ii) the design phase is subjected to high variability, and this affects the tool results. Future works may introduce additional types of batteries, deeper focus on the manufacturing phase; machine learning techniques may support future extension of the tool and create parametric models for conceptual and early design. The proposed method and tool can be extended to the economic sphere.", "Keywords": "Eco-design ; Tool ; Electric vehicles ; Autonomous vehicles ; Simplified analysis ; Environmental analysis", "DOI": "10.1016/j.jii.2024.100605", "PubYear": 2024, "Volume": "39", "Issue": "", "JournalId": 43270, "JournalTitle": "Journal of Industrial Information Integration", "ISSN": "2467-964X", "EISSN": "2452-414X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Università Politersità Politecnica delle Marche, Via Brecce Bianche 12, 60131, Ancona, Italy"}, {"AuthorId": 2, "Name": "Federica <PERSON>", "Affiliation": "Università Politersità Politecnica delle Marche, Via Brecce Bianche 12, 60131, Ancona, Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Università eCampus, Via Isimbardi, 10 - 22060 Novedrate, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Università Politersità Politecnica delle Marche, Via Brecce Bianche 12, 60131, Ancona, Italy"}], "References": [{"Title": "Design of a reference architecture for developing smart warehouses in industry 4.0", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Cagatay Catal", "PubYear": 2021, "Volume": "124", "Issue": "", "Page": "103343", "JournalTitle": "Computers in Industry"}, {"Title": "Environmental and economic sustainability assessment of an industry 4.0 application: the AGV implementation in a food industry", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "5-6", "Page": "2937", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "How de-manufacturing supports circular economy linking design and EoL - a literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "63", "Issue": "", "Page": "118", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 114305623, "Title": "Uncovering Flat and Hierarchical Topics by Community Discovery on Word Co-occurrence Network", "Abstract": "<p>Topic modeling aims to discover latent themes in collections of text documents. It has various applications across fields such as sociology, opinion analysis, and media studies. In such areas, it is essential to have easily interpretable, diverse, and coherent topics. An efficient topic modeling technique should accurately identify flat and hierarchical topics, especially useful in disciplines where topics can be logically arranged into a tree format. In this paper, we propose Community Topic, a novel algorithm that exploits word co-occurrence networks to mine communities and produces topics. We also evaluate the proposed approach using several metrics and compare it with usual baselines, confirming its good performances. Community Topic enables quick identification of flat topics and topic hierarchy, facilitating the on-demand exploration of sub- and super-topics. It also obtains good results on datasets in different languages.</p><p>© The Author(s) 2024.</p>", "Keywords": "Community mining;Data mining;Graphs;Hierarchical topics;Information networks;Natural language processing;Topic modeling", "DOI": "10.1007/s41019-023-00239-2", "PubYear": 2024, "Volume": "9", "Issue": "1", "JournalId": 15887, "JournalTitle": "Data Science and Engineering", "ISSN": "2364-1185", "EISSN": "2364-1541", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Alberta, Edmonton, AB T6G 2R3 Canada. ;Alberta Machine Intelligence Institute, Edmonton, AB T5J 3B1 Canada."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Alberta, Edmonton, AB T6G 2R3 Canada. ;Alberta Machine Intelligence Institute, Edmonton, AB T5J 3B1 Canada."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitè de Sherbrooke, Sherbrooke, QC J1K 2R1 Canada."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hubert <PERSON> Laboratory, Universitè <PERSON>, Saint-Etienne, France."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Alberta, Edmonton, AB T6G 2R3 Canada. ;Alberta Machine Intelligence Institute, Edmonton, AB T5J 3B1 Canada."}], "References": [{"Title": "A review of topic modeling methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "101582", "JournalTitle": "Information Systems"}, {"Title": "A comparative study of overlapping community detection methods from the perspective of the structural properties", "Authors": "<PERSON><PERSON><PERSON>; Carolina Ribeiro Xavier; Alexandre <PERSON> Ev<PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Applied Network Science"}, {"Title": "The Evolution of Topic Modeling", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "10s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 114305859, "Title": "Speckle Noise Removal: A Local Structure Preserving Approach", "Abstract": "<p>This paper proposes a speckle noise removal approach for clinical ultrasound images by doing outlier removal and smoothening operations alternately. During the initial investigation, it was found that the log-transformed ultrasound image follows Fisher–Tippet<PERSON> distribution and has fixed median absolute deviation (MAD). Hence, the noise in log-transformed ultrasound images behaves like white Gaussian noise with transients or outliers. Therefore, the de-noising problem can be considered as the removal of outliers followed by smoothening. These two processes are unified in one framework by defining a Bayesian Maximum-a-Posteriori (MAP) estimation function. This function has two terms: fidelity and regularizer. The fidelity is derived using the proposed generalized Fisher–Tippett distribution, whereas a weighted total variation is used as a regularizer. A regularizer weigh scheme is introduced to preserve edges in the images. The weights are computed using echo-texture graded local-oriented structure information present in an image. To obtain tissue-specific echo-texture, fuzzy C -means clustering is deployed for grouping similar tissue echo-textures. This grouping will help to discriminate the proper boundary of the tissue. To extract the original image, the MAP function is minimized and is performed using the generalized Bregman alternate method of multipliers. Ten different existing techniques are used to compare the performance of the proposed method on both phantom and clinical ultrasound images. The proposed approach achieved a signal-to-noise ratio in the range of 5–10 and a peak signal-to-noise ratio in the range of 67–70. Structural preservation metrics like figure of merit came out to be as high as 0.8. Moreover, using the proposed approach lower signal suppression index and higher effective number of lookup values are achieved for the restored clinical ultrasound images. The proposed algorithm can provide better piecewise smoothness and high contrast in despeckled images. Along with it, the edges are seen to be well preserved. Both qualitative and quantitative analysis support the efficacy of the approach compared to state-of-the-art methods.</p>", "Keywords": "Clinical ultrasound; <PERSON>–<PERSON> distribution; Total variation; De-speckling; Bayesian map", "DOI": "10.1007/s42979-024-02655-1", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, Ecole Centrale School of Engineering, Mahindra University, Hyderabad, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Jadavpur University, Kolkata, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Machine Intelligence Unit, Indian Statistical Institute, Kolkata, India"}], "References": []}, {"ArticleId": 114305924, "Title": "Directional multiobjective optimization of metal complexes at the billion-system scale", "Abstract": "<p>The discovery of transition metal complexes (TMCs) with optimal properties requires large ligand libraries and efficient multiobjective optimization algorithms. Here we provide the tmQMg-L library, containing 30k diverse and synthesizable ligands with robustly assigned charges and metal coordination modes. tmQMg-L enabled the generation of 1.37 million palladium TMCs, which were used to develop and benchmark the Pareto-Lighthouse multiobjective genetic algorithm (PL-MOGA). With fine control over aim and scope, this algorithm maximized both the polarizability and highest occupied molecular orbital-lowest unoccupied molecular orbital gap of the TMCs within selected regions of the Pareto front, without requiring prior knowledge on the objective limits. Instead of genetic operations on small ligand fragments, the PL-MOGA did whole-ligand mutation and crossover operations, which in chemical spaces containing billions of systems, yielded thousands of highly diverse TMCs in an interpretable manner.</p><p>© 2024. The Author(s), under exclusive licence to Springer Nature America, Inc.</p>", "Keywords": "", "DOI": "10.1038/s43588-024-00616-5", "PubYear": 2024, "Volume": "4", "Issue": "4", "JournalId": 83518, "JournalTitle": "Nature Computational Science", "ISSN": "", "EISSN": "2662-8457", "Authors": [{"AuthorId": 1, "Name": "Hannes Kneiding", "Affiliation": "Hylleraas Centre for Quantum Molecular Sciences, Department of Chemistry, University of Oslo, Oslo, Norway."}, {"AuthorId": 2, "Name": "Ainara Nova", "Affiliation": "Hylleraas Centre for Quantum Molecular Sciences, Department of Chemistry, University of Oslo, Oslo, Norway. ;Centre for Materials Science and Nanotechnology, Department of Chemistry, University of Oslo, Oslo, Norway."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hylleraas Centre for Quantum Molecular Sciences, Department of Chemistry, University of Oslo, Oslo, Norway"}], "References": [{"Title": "Multi-objective goal-directed optimization of de novo stable organic radicals for aqueous redox flow batteries", "Authors": "<PERSON><PERSON><PERSON> S. V.; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "8", "Page": "720", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 114305952, "Title": "Неперервна оптимізація та управління ризиками при впровадженні методології DevOps в іт інфраструктурах", "Abstract": "<p>Автори статті пропонують комплексний погляд на важливі процедури та методи впровадження методології DevOps у сучасній сфері ІТ. Зокрема, у статті розглядають основні принципи DevOps, їхня роль у прискоренні циклу розробки програмного забезпечення та забезпеченні неперервного постачання нових версій високоякісних програмних продуктів. Підкреслюється, що запровадження методології DevOps не лише спрощує процедури інтеграції та розгортання, але й створює умови для постійної оптимізації процесів, виявлення та управління ризиками. Дослідники підкреслюють важливість реалізації принципів неперервного моніторингу, аналізу та оптимізації процесів, які можуть бути досягнуті завдяки застосуванню DevOps. Особлива увага в статті приділяється практичному застосуванню цих методів у реальних проектах та їхньому впливу на покращення якості та швидкості розгортання програмного забезпечення. Зазначається, що завдяки впровадженню методології DevOps компанії можуть досягати значного зростання ефективності та конкурентоспроможності, а також забезпечувати високу якість своїх продуктів у вимогливому ринковому середовищі. У стаття акцентується увага на важливості підготовки кваліфікованого персоналу та проведення SWOT-аналізу перед впровадженням методології DevOps. Висвітлюється потреба відповідної підготовки та навчання персоналу для розуміння та ефективного впровадження цієї методології, а також важливість наявності в команді фахівців з відповідними навичками та досвідом. Розглядається важливість проведення SWOT-аналізу для об'єктивної оцінки сильних та слабких сторін компанії, а також виявлення можливостей та загроз, пов'язаних з впровадженням методології DevOps. Це допомагає в розробці стратегії впровадження, визначення потенційних ризиків та пошуку шляхів їхнього уникнення або зменшення впливу на процеси розробки та управління ІТ-проектами.</p>", "Keywords": "Infrastructure; risk assesement; DevOps; risk management; scaling; continuous integration; CI/CD; infrastructure as code", "DOI": "10.36910/6775-2524-0560-2024-54-21", "PubYear": 2024, "Volume": "", "Issue": "54", "JournalId": 71325, "JournalTitle": "КОМП’ЮТЕРНО-ІНТЕГРОВАНІ ТЕХНОЛОГІЇ: ОСВІТА, НАУКА, ВИРОБНИЦТВО", "ISSN": "2524-0552", "EISSN": "2524-0560", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>. <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114306016, "Title": "From Standard Policy-Based Zero Trust to Absolute Zero Trust (AZT): A Quantum Leap to Q-Day Security", "Abstract": "Cybercrime is projected to cost a whopping $23.8 Trillion by 2027. This is essentially because there’s no computer network that’s not vulnerable. Fool-proof cybersecurity of personal data in a connected computer is considered practically impossible. The advent of quantum computers (QC) will worsen cybersecurity. QC will be a boon for data-intensive industries by drastically reducing the computing time from years to minutes. But QC will render our current cryptography vulnerable to quantum attacks, breaking nearly all modern cryptographic systems. Before QCs with sufficient qubits arrive, we must be ready with quantum-safe strategies to protect our ICT infrastructures. Post-quantum cryptography (PQC) is being aggressively pursued worldwide as a defence from the potential Q-day threat. NIST (National Institute of Standards and Technology), in a rigorous process, tested 82 PQC schemes, 80 of which failed after the final round in 2022. Recently the remaining two PQCs were also cracked by a Swedish and a French team of cryptographers, placing NIST’s PQC standardization process in serious jeopardy. With all the NIST-evaluated PQCs failing, there’s an urgent need to explore alternate strategies. Although cybersecurity heavily relies on cryptography, recent evidence indicates that it can indeed transcend beyond encryption using Zero Vulnerability Computing (ZVC) technology. ZVC is an encryption-agnostic absolute zero trust (AZT) approach that can potentially render computers quantum resistant by banning all third-party permissions, a root cause of most vulnerabilities. Unachievable in legacy systems, AZT is pursued by an experienced consortium of European partners to build compact, solid-state devices that are robust, resilient, energy-efficient, and with zero attack surface, rendering them resistant to malware and future Q-Day threats.", "Keywords": "Cybersecurity;Quantum Computers;Post Quantum Cryptography;Q-Day;Zero Trust", "DOI": "10.4236/jcc.2024.123016", "PubYear": 2024, "Volume": "12", "Issue": "3", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Blockchain 5.0 Ltd Kesklinnalinnaosa, Tallinn, Estonia"}], "References": [{"Title": "Superintelligence Cannot be Contained: Lessons from Computability Theory", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "70", "Issue": "", "Page": "65", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Looking to the future of the cyber security landscape", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "3", "Page": "8", "JournalTitle": "Network Security"}, {"Title": "The Impact of Cloud Computing on Network Security and the Risk for Organization Behaviors", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "1", "Page": "195", "JournalTitle": "Webology"}, {"Title": "Will Zero Vulnerability Computing (ZVC) Ever Be Possible? Testing the Hypothesis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "8", "Page": "238", "JournalTitle": "Future Internet"}, {"Title": "Serious Games with SysML: Gamifying Threat Modelling in a Small Business Setting", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "S2", "Page": "119", "JournalTitle": "INCOSE International Symposium"}, {"Title": "The Future of Cybersecurity in the Age of Quantum Computers", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "11", "Page": "335", "JournalTitle": "Future Internet"}, {"Title": "Exploiting Intermediate Value Leakage in Dilithium: A Template-Based Approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "2023", "Issue": "4", "Page": "188", "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems"}]}, {"ArticleId": 114306047, "Title": "Методика випереджаючого викладання навчальної дисципліни «методологія наукових досліджень» здобувачам другого (магістерського) рівня вищої освіти освітньої програми «комп’ютерні науки»", "Abstract": "<p>Мета статті полягає у обґрунтуванні методики випереджаючого викладання дисципліни «Методологія наукових досліджень» здобувачам освітньої програми «Комп’ютерні науки» галузі знань 12 Інформаційні технології спеціальності 122 Комп’ютерні науки другого (магістерського) рівня вищої освіти денної та заочної форм навчання. Результатом дослідження стало обґрунтування методики випереджаючого викладання освітньої компоненти «Методологія наукових досліджень» на засадах рольової гри здобувачів вищої освіти у майбутнього викладача-лектора. За рахунок застосування оберненого перетворення здобувача вищої освіти у викладача досягнуто навчально-розвиваючу мету набуття студентами первинного навчального квазі-професійного досвіду із застосування методології наукових досліджень та практики викладацької діяльності. Науковою новизною є подальший розвиток теорії та практики методики викладання навчальної дисципліни «Методологія наукових досліджень» на кафедрі «Комп’ютерні науки» здобувачам другого (магістерського) рівня вищої освіти освітньої програми «Комп’ютерні науки» на основі ігрового (гейміфікованого) підходу.</p>", "Keywords": "teaching methods; academic discipline; methodology of scientific research; applicant; student; computer science", "DOI": "10.36910/6775-2524-0560-2024-54-13", "PubYear": 2024, "Volume": "", "Issue": "54", "JournalId": 71325, "JournalTitle": "КОМП’ЮТЕРНО-ІНТЕГРОВАНІ ТЕХНОЛОГІЇ: ОСВІТА, НАУКА, ВИРОБНИЦТВО", "ISSN": "2524-0552", "EISSN": "2524-0560", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "A categorical approach for defining digital twins in the AECO industry", "Abstract": "<p>Operations and Maintenance (O&M) costs account for 60-80% of a facility’s lifecycle costs. Using Digital Twins (DTs) can aid in making O&M more effective and efficient, leading to time and cost savings. The concept of DT started in the Aerospace domain, and other industries eventually adopted it. DTs are a new concept to the Architecture, Engineering, Construction, and Operations (AECO) Industry, and there is a lot of confusion around this concept. The purpose of this paper is to provide a DT definition along with a classification structure to create a common ground for understanding DTs in the AECO industry, which leads to easier adoption of DTs. A systematic literature review was completed to identify the existing DT definitions and classification approaches. Then, through a content analysis, the core components of definitions were extracted. The identified components were used to develop a comprehensive and inclusive DT definition for the AECO industry, using the domain language. In a similar fashion, existing DT classification structures were studied, and their components were identified through content analysis. Using the identified components, a DT classification structure was proposed for the AECO industry using domain concepts and terms. The results were validated and refined through a series of semi-structured expert interviews and surveys. Interviewees and survey participants comprised DT experts from academia and industry with diverse backgrounds. The components of the proposed DT definition include virtual representation, data connection between physical and digital entities, analysis, actuation, and frequency of updates. The classification structure consisted of three DT categories, namely Digital Twin Prototype (DTP), Digital Shadow (DS), and Cyber-Physical System (CPS).</p>", "Keywords": "AECO; Classification System; Common Data Environment; Cyber-Physical Systems; Digital Shadows; Digital Twins; Information Management", "DOI": "10.36680/j.itcon.2024.010", "PubYear": 2024, "Volume": "29", "Issue": "", "JournalId": 70075, "JournalTitle": "Journal of Information Technology in Construction", "ISSN": "", "EISSN": "1874-4753", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Architectural Engineering, The Pennsylvania State University, University Park, PA, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Architectural Engineering, The Pennsylvania State University, University Park, PA, United States"}], "References": [{"Title": "Trade study to select best alternative for cable and pulley simulation for cranes on offshore vessels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "2", "Page": "177", "JournalTitle": "Systems Engineering"}, {"Title": "Review of digital twin about concepts, technologies, and industrial applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "346", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Digital Twin: Generalization, characterization and implementation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "113524", "JournalTitle": "Decision Support Systems"}, {"Title": "Digital twin paradigm: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "130", "Issue": "", "Page": "103469", "JournalTitle": "Computers in Industry"}]}, {"ArticleId": 114306170, "Title": "Topology optimization of three-dimensional structures subject to self-weight loading", "Abstract": "Purpose Topology optimization of structures under self-weight loading is a challenging problem which has received increasing attention in the past years. The use of standard formulations based on compliance minimization under volume constraint suffers from numerous difficulties for self-weight dominant scenarios, such as non-monotonic behaviour of the compliance, possible unconstrained character of the optimum and parasitic effects for low densities in density-based approaches. This paper aims to propose an alternative approach for dealing with topology design optimization of structures into three spatial dimensions subject to self-weight loading. Design/methodology/approach In order to overcome the above first two issues, a regularized formulation of the classical compliance minimization problem under volume constraint is adopted, which enjoys two important features: (a) it allows for imposing any feasible volume constraint and (b) the standard (original) formulation is recovered once the regularizing parameter vanishes. The resulting topology optimization problem is solved with the help of the topological derivative method, which naturally overcomes the above last issue since no intermediate densities (grey-scale) approach is necessary. Findings A novel and simple approach for dealing with topology design optimization of structures into three spatial dimensions subject to self-weight loading is proposed. A set of benchmark examples is presented, showing not only the effectiveness of the proposed approach but also highlighting the role of the self-weight loading in the final design, which are: (1) a bridge structure is subject to pure self-weight loading; (2) a truss-like structure is submitted to an external horizontal force (free of self-weight loading) and also to the combination of self-weight and the external horizontal loading; and (3) a tower structure is under dominant self-weight loading. Originality/value An alternative regularized formulation of the compliance minimization problem that naturally overcomes the difficulties of dealing with self-weight dominant scenarios; a rigorous derivation of the associated topological derivative; computational aspects of a simple FreeFEM implementation; and three-dimensional numerical benchmarks of bridge, truss-like and tower structures.", "Keywords": "Structural topology optimization;Self-weight loading;Topological derivative method", "DOI": "10.1108/EC-11-2023-0791", "PubYear": 2024, "Volume": "41", "Issue": "2", "JournalId": 30507, "JournalTitle": "Engineering Computations", "ISSN": "0264-4401", "EISSN": "1758-7077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Coordenação de Métodos Matemáticos e Computacionais, Laboratório Nacional de Computação Científica – LNCC/MCT, Petrópolis, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Coordenação de Métodos Matemáticos e Computacionais, Laboratório Nacional de Computação Científica – LNCC/MCT, Petrópolis, Brazil"}], "References": [{"Title": "Topological sensitivity analysis with respect to a small idealized bolt", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "115", "JournalTitle": "Engineering Computations"}, {"Title": "Imaging of small penetrable obstacles based on the topological derivative method", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "201", "JournalTitle": "Engineering Computations"}, {"Title": "Topological sensitivity analysis revisited for time-harmonic wave scattering problems. Part I: the free space case", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "232", "JournalTitle": "Engineering Computations"}, {"Title": "An introduction to the topological derivative", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "3", "JournalTitle": "Engineering Computations"}, {"Title": "Topological derivatives via one-sided derivative of parametrized minima and minimax", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "34", "JournalTitle": "Engineering Computations"}, {"Title": "Inverse homogenization using the topological derivative", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "337", "JournalTitle": "Engineering Computations"}, {"Title": "Brittle fracture on plates governed by topological derivatives", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "421", "JournalTitle": "Engineering Computations"}, {"Title": "Experimental validation of a topological derivative-based crack growth control method using digital image correlation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "438", "JournalTitle": "Engineering Computations"}, {"Title": "A topology optimization algorithm based on topological derivative and level-set function for designing phononic crystals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "354", "JournalTitle": "Engineering Computations"}, {"Title": "Optimum design of two-material bending plate compliant devices", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "395", "JournalTitle": "Engineering Computations"}, {"Title": "Topology optimization of structures subject to self-weight loading under stress constraints", "Authors": "<PERSON><PERSON><PERSON> dos Santos; Cinthia Gomes Lopes", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "380", "JournalTitle": "Engineering Computations"}, {"Title": "Topological sensitivity analysis revisited for time-harmonic wave scattering problems. Part II: recursive computations by the boundary integral equation method", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "272", "JournalTitle": "Engineering Computations"}, {"Title": "Adjoint-based methods to compute higher-order topological derivatives with an application to elasticity", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "60", "JournalTitle": "Engineering Computations"}, {"Title": "On the justification of topological derivative for wave-based qualitative imaging of finite-sized defects in bounded media", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "313", "JournalTitle": "Engineering Computations"}, {"Title": "Shape optimization in acoustic–structure interaction", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "172", "JournalTitle": "Engineering Computations"}, {"Title": "Guest editorial", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "1", "JournalTitle": "Engineering Computations"}, {"Title": "Shape and topology optimal design problems in electromagnetic casting", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "147", "JournalTitle": "Engineering Computations"}]}, {"ArticleId": 114306202, "Title": "Impact of Various Solvents on Extraction of Anthocyanins from Blueberry for Use in Dye-sensitized Solar Cells", "Abstract": "We extracted anthocyanins from a natural dye source, blueberry, by a solvent extraction method for use as sensitizers in the fabrication of dye-sensitized solar cells (DSSCs). In the extraction of anthocyanins, we used solvents such as acetonitrile, tert-butanol, ethanol, and acetone, and their effects on the performance of DSSCs were examined. Currently, the available commercial-grade titanium dioxide (TiO2) powder is composed of 80 mol% rutile and 20 mol% anatase phases. In the preparation of the photoanode, the TiO2 powder was applied by a doctor blade technique. The prepared photoanodes were immersed in the extracted anthocyanin dye and exposed for different durations while being shielded from light throughout the process. To prepare electrodes, a platinum film approximately 1 nm thick was sputter-coated onto an indium tin oxide (ITO) glass substrate. Finally, the coated photoanodes were sealed with the electrode by dye-soaking. To evaluate the performance of the fabricated DSSCs, the incident photon-toelectron conversion efficiency (IPCE) was measured by ultraviolet-visible spectroscopy (UV- VIS) and a solar simulator. The results showed that DSSCs with the dye extracted from blueberry in tert-butanol for 12 h showed the best efficiency. In this study, tert-butanol was the best extraction solvent for the fabrication of DSSCs with anthocyanins extracted from blueberry with an efficiency of 0.45% and a fill factor of 68.20%. Further study is required to find a more appropriate solvent and extraction method, while the result of this study proved that the use of a dye from a natural dye source such as blueberry in solar cell technology is promising. © 2024 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "blueberry juice; dye-sensitized solar cells; frame; natural dye source; photoanodes", "DOI": "10.18494/SAM4729", "PubYear": 2024, "Volume": "36", "Issue": "3", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Zhan-<PERSON>g Yuan", "Affiliation": "School of Ocean Information Engineering, Jimei University, Xiamen, 361021, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, National Formosa University, Yunlin, 632, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, National Formosa University, Yunlin, 632, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, National Formosa University, Yunlin, 632, Taiwan"}, {"AuthorId": 5, "Name": "Teen-<PERSON>", "Affiliation": "Department of Electronic Engineering, National Formosa University, Yunlin, 632, Taiwan"}], "References": []}, {"ArticleId": 114306211, "Title": "Rethinking compensation in light of the development of AI", "Abstract": "", "Keywords": "", "DOI": "10.1080/13600869.2024.2324554", "PubYear": 2024, "Volume": "38", "Issue": "3", "JournalId": 25543, "JournalTitle": "International Review of Law, Computers & Technology", "ISSN": "1360-0869", "EISSN": "1364-6885", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "STeP Research Group, Faculty of Law, University of Groningen, Groningen, The Netherlands;ISLC, University of Milan, Milan, Italy"}], "References": [{"Title": "A first critical analysis of the European approach to damage caused by artificial intelligence enabled by global navigation satellite systems. A bridge to nowhere or a cloud with a silver lining?", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "2", "Page": "147", "JournalTitle": "International Review of Law, Computers & Technology"}]}, {"ArticleId": 114306228, "Title": "Control Strategies for Photovoltaic Energy Storage Systems", "Abstract": "In recent years, with the impact of global climate change and specific commitments, such as the European Climate Law, to reduce greenhouse gas emissions by 55% from 1990 levels by 2030, renewable energy has become an indispensable application in achieving carbon emission reduction goals. Taiwan has also been actively promoting the development of renewable energy, with a target to reach 20 GW of solar power generation by 2025. With a large amount of renewable energy integrated into the grid, its intermittent and unpredictable nature poses significant challenges to grid stability and power dispatch, particularly evident in the formation of the duck curve during the second-peak load period. Balancing power supply and demand is a critical issue, especially after the sun sets. To address this problem, the Taiwan Power Company has proposed the construction of solar energy storage systems (ESSs), and this study is focused on the development of a solar ESS controller using a programmable logic controller platform along with power sensors. The controller is designed to measure the real-time power generation of solar photovoltaic systems and to develop a control strategy for the ESS. It enables the solar photovoltaic system to charge the ESS during the day and discharge it during the second-peak load period. This approach not only helps mitigate the challenges of second-peak load power dispatch but also enhances the utilization of power lines effectively. © 2024 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "duck curve; energy storage systems; photovoltaic energy storage systems; second-peak load", "DOI": "10.18494/SAM4732", "PubYear": 2024, "Volume": "36", "Issue": "3", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Taiwan University of Science and Technology, No. 43, Keelung Rd., Sec. 4, Da'an Dist., Taipei City, 106335, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Energy and Sustainability Technology, National Taiwan University of Science and Technology, No. 43, Keelung Rd., Sec. 4, Da'an Dist., Taipei City, 106335, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Taiwan University of Science and Technology, No. 43, Keelung Rd., Sec. 4, Da'an Dist., Taipei City, 106335, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Chin-Yi University of Technology No. 57, Sec. 2, Zhongshan Rd., Taiping Dist., Taichung, 411030, Taiwan"}], "References": []}, {"ArticleId": 114306343, "Title": "A comparative assessment of OMP and MATLAB for parallel computation", "Abstract": "<p>The prime goal of parallel computing is the simultaneous parallel execution of several program instructions. Consequently, to accomplish this, the program should be divided into independent sets so that each processor can execute its program part concurrently with the other processors. This study compares OMP and MATLAB, two important parallel computing simulation tools, through the use of a dense matrix multiplication technique. The results showed that OMP outperformed the MATLAB parallel environment by over 8 times in sequential execution and 6 times in parallel execution. From this proposed method, it was also observed that OMP with an even slower processor performs much better than MATLAB with a higher processor. Thus, the present analysis indicates that OMP is a superior environment for parallel computing and should be preferred over parallel MATLAB.</p>", "Keywords": "", "DOI": "10.3233/HIS-240001", "PubYear": 2024, "Volume": "20", "Issue": "1", "JournalId": 36703, "JournalTitle": "International Journal of Hybrid Intelligent Systems", "ISSN": "1448-5869", "EISSN": "1875-8819", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A Systematic Survey of General Sparse Matrix-matrix Multiplication", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>xing Ji; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "12", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 114306602, "Title": "Multimodal Data Modeling Technology and Its App-19lication for Cloud-edge-device Collaboration", "Abstract": "", "Keywords": "", "DOI": "10.21655/ijsi.1673-7288.00319", "PubYear": 2024, "Volume": "14", "Issue": "1", "JournalId": 87794, "JournalTitle": "International Journal of Software and Informatics", "ISSN": "1673-7288", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer, Harbin Institute of Technology, Harbin 150001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Wang", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114306730, "Title": "An optimal hybrid quadcopter control technique with MPC-based backstepping", "Abstract": "<p>Quadcopter unmanned aerial vehicle is a multivariable, coupled, unstable, and underactuated system with inherent nonlinearity. It is gaining popularity in various applications and has been the subject of numerous research studies. However, modelling and controlling a quadcopter to follow a trajectory is a challenging issue for which there is no unique solution. This study proposes an optimal hybrid quadcopter control with MPC-based backstepping control for following a reference trajectory. The outer-loop controller (backstepping controller) regulates the quadcopter’s position, whereas the inner-loop controller (Model Predictive Control) regulates its attitude. The translational and rotational dynamics of the quadcopter are analyzed utilizing the Newton-Euler method. After that, the backstepping controller (BC) is created, which is a recurrent control method according to <PERSON><PERSON><PERSON><PERSON>’s theory that utilizes a genetic algorithm (GA) to choose the controller parameters automatically. In order to apply a linear control technique in the presence of nonlinearities in the quadcopter dynamics, Linear Parameter Varying (LPV) Model Predictive Control (MPC) structure is developed. Simulation validated the dynamic performance of the proposed optimal hybrid MPC-based backstepping controller of the quadcopter in following a given reference trajectory. The simulations demonstrate the fact that using a command control input in trajectory tracking, the proposed control algorithm offers suitable tracking over the assigned position references with maximum appropriate tracking errors of 0.1 m for the �� and �� positions and 0.15 m for the �� position.</p>", "Keywords": "backstepping control; genetic algorithm; linear parameter varying; Model Predictive Control; quadcopter; UAV", "DOI": "10.24425/acs.2024.149651", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 30654, "JournalTitle": "Archives of Control Sciences", "ISSN": "1230-2384", "EISSN": "2300-2611", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechatronic Engineering,Univeristy of Nigeria, Nsukka, Enugu State, Nigeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronicand Computer Engineering, University of Nigeria, Nsukka, Enugu State, Nigeria"}, {"AuthorId": 3, "Name": "Mmasom I. Ndefo", "Affiliation": "Department of Electronicand Computer Engineering, University of Nigeria, Nsukka, Enugu State, Nigeria"}, {"AuthorId": 4, "Name": "Oluchi C. <PERSON>", "Affiliation": "Department of Electrical Engineering, Universityof Nigeria, Nsukka, Enugu State, Nigeria"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electronicand Computer Engineering, University of Nigeria, Nsukka, Enugu State, Nigeria"}, {"AuthorId": 6, "Name": "Oz<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechatronic Engineering and DepartmentofAgricultural and Bioresources Engineering,Univeristy of Nigeria, Nsukka, Enugu State, Nigeria"}], "References": []}, {"ArticleId": 114306899, "Title": "Impact of mobile application loading icon type and animation frequency on user time perception and emotion", "Abstract": "As the status indicator of system programs, loading icons have a significant role in human–computer interaction. This study explored the effect of the visual presentation of mobile application loading icons on users&#x27; time perception and emotion. The two variables adopted in this experiment were the type of loading icons and the animation frequencies. Three types of loading icons were used: circle type, aligned dots type, and logo type. Four frequencies were chosen: 3 Hz, 2 Hz, 1.5 Hz, and 1 Hz (changing every 333 ms, 500 ms, 666 ms, and 1000 ms). Theories related to subjective temporal experience and the three-dimensional emotion model of pleasure-arousal-dominance were utilized in this experiment to measure the user&#x27;s time perception and emotion comprehensively. The experiment in this study was a 3x4 within-subject design. A total of 67 university students aged 18–26 participated in this experiment. The results showed that users&#x27; time perception and emotions were influenced by icon types and animation frequencies. Users who experience logo type icons were more inclined to generate positive evaluations. Loading icons with higher animation frequency had more positive user perception evaluation, but for more complex icons, excessively high animation frequency probably led to a negative visual experience. These findings might help improve the waiting experience of users when using the applications.", "Keywords": "", "DOI": "10.1016/j.displa.2024.102687", "PubYear": 2024, "Volume": "83", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Design, South China University of Technology, Guangzhou 510006, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Design, South China University of Technology, Guangzhou 510006, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Design, South China University of Technology, Guangzhou 510006, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Design, South China University of Technology, Guangzhou 510006, China"}], "References": [{"Title": "The effect of visual feedback types on the wait indicator interface of a mobile application", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101928", "JournalTitle": "Displays"}, {"Title": "Measuring subjectively experienced time in usability and user experience testing scenarios", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "102399", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Countdown Timer Speed", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "The effect of mobile applications’ initial loading pages on users’ mental state and behavior", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "102007", "JournalTitle": "Displays"}, {"Title": "The influence of audio effects and attention on the perceived duration of interaction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "159", "Issue": "", "Page": "102756", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 114307020, "Title": "Evaluation of sustainable energy systems in smart cities using a Multi-Expert Pythagorean fuzzy BWM & TOPSIS methodology", "Abstract": "Smart cities are technological settlements using the collected data to utilize resources and services effectively by combining information and communication technologies with various tools connected to the internet of things network. Sustainable energy systems in smart cities are systems can be evaluated by using multiple criteria decision making methods or methodologies based on several vague/imprecise evaluation criteria. In this paper, sustainable energy systems in smart cities are evaluated by interval-valued Pythagorean fuzzy (IVPF) sets with an integrated optimization based multi-expert fuzzy Best Worst Method (BWM) and TOPSIS methodology that can better handle uncertainty and vagueness in experts’ linguistic assessments than existing methodologies. The considered criteria are weighted by multi-expert IVPF Best Worst Method, which has become a popular weighting method in recent years. Later, the energy alternatives for a real case study are prioritized by multi-expert IVPF TOPSIS method. In the analysis, the most important criterion is found as Environmental sustainability (C1) with the defuzzified weight of 0.218 while the other weights are as initial investment (C2) with 0.196, operating expenses (C3) with 0.163, technical feasibility (C4) with 0.154, social acceptability (C5) with 0.140, and scalability (C6) with 0.129. The obtained results indicate that “Investing in advanced technologies” in a smart city with relative degree of closeness (RDC) value of 0.798, has been determined as the best alternative among the considered five alternatives. It is closely followed by “Developing a transportation system” with the RDC value of 0.681. Sensitivity analysis shows that the ranking results are quite robust and reliable. The comparative analysis with crisp BWM and TOPSIS methodology is applied to check the validity of the proposed methodology.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.123874", "PubYear": 2024, "Volume": "250", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Industrial Engineering, Istanbul Bilgi University, Eski Silahtarağa Elektrik Santrali, Eyüpsultan, 34060 Istanbul, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Istanbul Technical University, Macka, 34367 Istanbul, Turkey"}, {"AuthorId": 3, "Name": "Başar Özta<PERSON>şi", "Affiliation": "Department of Industrial Engineering, Istanbul Technical University, Macka, 34367 Istanbul, Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Istanbul Technical University, Macka, 34367 Istanbul, Turkey"}], "References": [{"Title": "Recommender systems for smart cities", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON>; <PERSON>-Cediel", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101545", "JournalTitle": "Information Systems"}, {"Title": "A novel approach integrating AHP and TOPSIS under spherical fuzzy sets for advanced manufacturing system selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "103988", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Intuitionistic fuzzy soft decision making method based on CoCoSo and CRITIC for CCN cache placement strategy selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "2", "Page": "1567", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Application of novel intuitionistic fuzzy BWAHP process for analysing the efficiency of water treatment plant", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "24", "Page": "17389", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Decision support modeling for multiple criteria assessments using a likelihood-based consensus ranking method under Pythagorean fuzzy uncertainty", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "6", "Page": "4879", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A novel Interval Type-2 Fuzzy best-worst method and combined compromise solution for evaluating eco-friendly packaging alternatives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "200", "Issue": "", "Page": "117188", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An Extended QUALIFLEX Method with Comprehensive Weight for Green Supplier Selection in Normal q-Rung Orthopair Fuzzy Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "5", "Page": "2174", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Road safety assessment and risks prioritization using an integrated SWARA and MARCOS approach under spherical fuzzy environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "6", "Page": "4549", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A sustainability evaluation framework for the urban energy Internet using the Fermatean fuzzy Aczel-Alsina hybrid MCDM method", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "122115", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114307160, "Title": "A Review on Intrusion Detection System for IoT based Systems", "Abstract": "<p>One of the key objectives of intelligent Internet of Things-based systems is to improve people's quality of life in terms of simplicity and efficiency. The paradigm for the Internet of Things (IoT) has surfaced recently as a technology to construct intelligent IoT systems. Security and privacy are essential considerations for all intelligent systems built on the Internet of Things concept. Because of the restricted processing and storage capabilities of IoT devices as well as their unique protocols, traditional IDSs are not a practical choice in an IoT environment. An overview of the most recent IDSs created for the IoT paradigm is given in this article, with particular attention to the techniques, features, and procedures of each. This essay also offers a thorough analysis of the IoT architecture, new security flaws, and how they relate to the layers of the IoT architecture. This study suggests that, despite previous studies on the design and implementation of integrated information systems in IoT paradigms, it is still an important task to develop efficient, reliable or trustworthy integrated information systems for IoT-based intelligent systems. This review concludes with future perspectives and important aspects to consider in the development of these IDS.</p>", "Keywords": "Intrusion detection systems; Internet of things; Smart environments", "DOI": "10.1007/s42979-024-02702-x", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Applications, Panjab University, Chandigarh, India; Corresponding author."}], "References": []}, {"ArticleId": 114307235, "Title": "Методологія та принципи виявлення об'єктів за допомогою деформованих згорткових мереж", "Abstract": "<p>У динамічній сфері комп’ютерного зору впровадження штучного інтелекту започаткувало трансформаційну еру, пропонуючи неперевершену точність і ефективність ідентифікації об’єктів на зображеннях і відео. Це дослідження заглиблюється в сферу виявлення об’єктів, керованих штучним інтелектом, з особливим акцентом на ключовій ролі метаданих у покращенні розуміння та корисності при роботі з розпізнаними об’єктами. Співпраця між штучним інтелектом і метаданими не тільки підвищує точність виявлення об’єктів, але й відкриває інноваційні шляхи для вилучення й аналізу інформації. Метадані охоплюють ключові деталі, такі як клас об’єкта, місце виявлення, час виникнення та взаємозв’язки між об’єктами, надаючи інформацію для наступних програм, таких як автономні транспортні засоби, спостереження та доповнена реальність. Дослідницька стаття служить демонстрацією інтеграції вилучення метаданих і керування ними з системами виявлення об’єктів на основі штучного інтелекту, що підвищує точність ідентифікації та відстеження об’єктів. Це дослідження висвітлює взаємодію між штучним інтелектом і комп’ютерним зором, формуючи ландшафт, де точність і адаптивність заново визначають межі можливостей виявлення об’єктів. Співпраця між штучним інтелектом і метаданими стає ключовим рушієм у підвищенні загальної ефективності та результативності систем розпізнавання об’єктів, пропонуючи зазирнути в майбутнє інтелектуального аналізу зображень і відео.</p>", "Keywords": "artificial intelligence; convolutional networks; data science; data analysis; data Processing; data Presentations", "DOI": "10.36910/6775-2524-0560-2024-54-18", "PubYear": 2024, "Volume": "", "Issue": "54", "JournalId": 71325, "JournalTitle": "КОМП’ЮТЕРНО-ІНТЕГРОВАНІ ТЕХНОЛОГІЇ: ОСВІТА, НАУКА, ВИРОБНИЦТВО", "ISSN": "2524-0552", "EISSN": "2524-0560", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114307280, "Title": "Prediction of Covid-19 confirmed cases and deaths using hybrid support vector machine-Taguchi method", "Abstract": "With the emergence of the COVID-19 pandemic, experts aim to predict the number of confirmed cases and take measures to reduce death rates in order to be prepared for future pandemics. Determining the variables that affect the confirmed cases and deaths and predicting the number of confirmed cases and deaths is important for taking precautions quickly. Many variables are affecting the confirmed cases and death rate of COVID-19 and causing the uncertainty in the process to increase even more. In this study, 19 variables affecting the number of confirmed cases and deaths from COVID-19 were determined. These variables are used as input in the support vector machine (SVM) to predict the number of confirmed cases and deaths. The hyperparameters of SVM, such as kernel type, C , and ɣ values, were determined by the <PERSON>uchi method. The hybrid method determines the optimum hyperparameter values of SVM in a short time and is less labor-intensive compared with other hyperparameter tuning methods. The application dataset was obtained from 51 countries&#x27; data. At the end of the study, the hybrid SVM-Taguchi method determined the number of confirmed cases and deaths by 91.8% and 94.2%, respectively. Besides, the hybrid SVM-Taguchi method was compared with different artificial intelligence methods. The hybrid SVM-Taguchi method gave better results according to the statistical performance criteria.", "Keywords": "", "DOI": "10.1016/j.cie.2024.110103", "PubYear": 2024, "Volume": "191", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Seda Hatice Gökler", "Affiliation": "Department of Industrial Engineering, Kahramanmaraş Sütçü İmam University, Kahramanmaraş 46040, Turkey"}], "References": [{"Title": "On hyperparameter optimization of machine learning algorithms: Theory and practice", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}, {"Title": "Prediction of COVID-19 Confirmed Cases Using Gradient Boosting Regression Method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "66", "Issue": "1", "Page": "315", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Real-time measurement of the uncertain epidemiological appearances of COVID-19 infections", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107039", "JournalTitle": "Applied Soft Computing"}, {"Title": "Covariance matrix forecasting using support vector regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "10", "Page": "7029", "JournalTitle": "Applied Intelligence"}, {"Title": "Machine Learning Approaches Reveal That the Number of Tests Do Not Matter to the Prediction of Global Confirmed COVID-19 Cases", "Authors": "<PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "", "Page": "561801", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "An optimized support vector regression for prediction of bearing degradation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "108008", "JournalTitle": "Applied Soft Computing"}, {"Title": "Prairie Dog Optimization Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "22", "Page": "20017", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Gazelle optimization algorithm: a novel nature-inspired metaheuristic optimizer", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "5", "Page": "4099", "JournalTitle": "Neural Computing and Applications"}, {"Title": "DETDO: An adaptive hybrid dandelion optimizer for engineering optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "57", "Issue": "", "Page": "102004", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A novel hybrid PSO- and GS-based hyperparameter optimization algorithm for support vector regression", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "27", "Page": "19961", "JournalTitle": "Neural Computing and Applications"}, {"Title": "<PERSON><PERSON><PERSON> shark optimizer: A novel nature-inspired algorithm for engineering optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "58", "Issue": "", "Page": "102210", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 114307312, "Title": "Unveiling Marketing Potential: Harnessing Advanced Analytics and Machine Learning for Gold Membership Strategy Optimization in a Superstore", "Abstract": "<p>This research paper presents a comprehensive case study conducted in a superstore, introducing a novel gold membership offer and employing sophisticated analytics and machine learning methodologies to identify potential customers. The primary objective of this study is to explore available data to discern the factors influencing customers’ responses to a new supermarket offering. Subsequently, a predictive model is developed to accurately gauge the likelihood of a favorable customer response. In pursuit of enhancing marketing strategies and bolstering sales, this study employs a suite of machine learning techniques, including decision trees, support vector machines, random forests, and XGBoost. Furthermore, the study incorporates metaheuristic optimization algorithms such as grey wolf optimization, slime mold algorithm, multi-verse optimizer, and particle swarm optimization to fine-tune hyperparameters of the machine learning models. These optimization algorithms serve as effective search mechanisms, facilitating the identification of optimal solutions and significantly improving classification performance in the context of the complex superstore problem. The research findings highlight the substantial impact of the metaheuristic strategy, specifically grey wolf optimization, on the performance of all machine learning models. Notably, the random forest model achieved the highest accuracy of 95% with the application of grey wolf optimization. Moreover, the decision tree model demonstrated remarkable improvement in accuracy following hyperparameter tuning with grey wolf optimization. Collectively, these results underscore the critical role of metaheuristic optimization in enhancing the performance of machine learning models for marketing strategies in the superstore industry.</p>", "Keywords": "XGBoost; SVM; GWO; PSO; SMA; MVO; Supermarket; Decision tree; Random Forest; Optimization; Hyperparameter", "DOI": "10.1007/s42979-024-02700-z", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Swami Keshvanand Institute of Technology, Management & Gramothan, Jaipur, India; Department of Electrical Engineering, Malaviya National Institute of Technology, Jaipur, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Swami Keshvanand Institute of Technology, Management & Gramothan, Jaipur, India; Department of Computer Science and Engineering, Malaviya National Institute of Technology, Jaipur, India; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Malaviya National Institute of Technology, Jaipur, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Malaviya National Institute of Technology, Jaipur, India"}], "References": [{"Title": "Slime mould algorithm: A new method for stochastic optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "300", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A comprehensive survey on support vector machine classification: Applications, challenges and trends", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "408", "Issue": "", "Page": "189", "JournalTitle": "Neurocomputing"}, {"Title": "A multi-objective multi-verse optimizer algorithm to solve environmental and economic dispatch", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "146", "Issue": "", "Page": "110650", "JournalTitle": "Applied Soft Computing"}, {"Title": "Stock price forecasting using PSO hypertuned neural nets and ensembling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>.", "PubYear": 2023, "Volume": "147", "Issue": "", "Page": "110835", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 114307480, "Title": "Research on Fake News Detection Method Using Heterogeneous Graph Fusion with Background Knowledge", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.143068", "PubYear": 2024, "Volume": "14", "Issue": "3", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [], "References": []}, {"ArticleId": 114307585, "Title": "Cascade operation-enhanced high-resolution representation learning for meticulous segmentation of bridge cracks", "Abstract": "High-resolution (HR) crack images have proven valuable for bridge inspection using unmanned aerial vehicles (UAVs), offering fine details crucial for accurate segmentation. Traditional deep learning (DL) struggles with HR images due to downsampling issues and limited computational resources. To address this, we propose Cascade-FcaHRNet, a HR representation learning-based multiscale architecture. It incorporates a frequency-channel attention mechanism to capture tiny crack features, a two-stage cascade operation for global and local refinement, and a region-sensitive loss to avoid ambiguous predictions. Ablation studies confirm the effectiveness of these modifications. Robustness experiments show improvements in performance metrics for crack segmentation. In a field test, the Cascade-FcaHRNet accurately segments bridge cracks wider than 0.5 mm from 4 K resolution images, enhancing safety and efficiency in UAV-based bridge inspection. The approach holds potential for developing scientifically sound maintenance and management strategies.", "Keywords": "", "DOI": "10.1016/j.aei.2024.102508", "PubYear": 2024, "Volume": "61", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Civil Engineering, Hunan University, Changsha, China;Bartlett School of Sustainable Construction, University College London, London, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Bartlett School of Sustainable Construction, University College London, London, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Civil Engineering, Hunan University, Changsha, China;Key Laboratory of Damage Diagnosis for Engineering Structures of Hunan Province, Hunan University, Changsha, China;Corresponding author"}], "References": [{"Title": "Distribution equalization learning mechanism for road crack detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "424", "Issue": "", "Page": "193", "JournalTitle": "Neurocomputing"}, {"Title": "A survey on deep learning and its applications", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100379", "JournalTitle": "Computer Science Review"}, {"Title": "Transformers in Vision: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "10s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A Generative adversarial learning strategy for enhanced lightweight crack delineation networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101575", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Automatic defect detection of texture surface with an efficient texture removal network", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "101672", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "UAV imagery based potential safety hazard evaluation for high-speed railroad using Real-time instance segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101819", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Defect-aware transformer network for intelligent visual surface defect detection", "Authors": "Hongbing Shang; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101882", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Intelligent analysis method of dam material gradation for asphalt-core rock-fill dam based on enhanced Cascade Mask R-CNN and GCNet", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "102001", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "DenseSPH-YOLOv5: An automated damage detection model based on DenseNet and Swin-Transformer prediction head-enabled YOLOv5 with attention mechanism", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "102007", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 114307693, "Title": "Biomechanical analysis of real‐time vibration exposure during mini combine harvester operation: A hybrid ANN–GA approach", "Abstract": "This research focuses on designing and evaluating ergonomic self‐propelled machinery seats to reduce whole‐body vibration (WBV) exposure among male and female agricultural workers. Subjects without musculoskeletal disorders were selected, and their anthropometric parameters were analyzed. An ergonomically refined seat, considering anthropometric dimensions and vibration reduction, was developed and tested. Vibration isolators using piezoelectric material enhanced operator comfort. In a laboratory experiment, real‐time one‐third octave band WBV data were collected using various seat types and engine speeds. At 1200 rpm, female operators experienced WBV levels between 3.42 and 13.40 m/s², while males ranged from 3.13 to 12.20 m/s². At 1600 rpm, females (T‐1) had WBV levels of 20.20–42.39 m/s², and males recorded 18.90–40.12 m/s². At 2000 rpm (T‐1), female operators WBV ranged from 246.71 to 303.45 m/s², and males from 248.10 to 300.13 m/s². At 2400 rpm (T‐1), female operators experienced WBV from 385.29 to 457.87 m/s², and males from 381.57 to 445.50 m/s². An integrated approach with artificial neural networks and genetic algorithms optimized machine operating parameters, resulting in minimum WBV levels. The highly accurate Multilayer Feed‐Forward Artificial Neural Network model (2‐10‐1) had a correlation ( R ) of 0.996 and a low mean‐squared error of 0.198. This research underscores the effectiveness of seat isolators in reducing vibrations and highlights the importance of considering both seat design and engine speed, especially concerning gender‐specific differences in vibration tolerance. It provides valuable insights for improving the comfort and safety of self‐propelled machinery operators in agriculture.", "Keywords": "agricultural worker safety;anthropometric measurements;artificial neural network optimization;ergonomic design;physiological responses;vibration isolators;whole-body vibration reduction", "DOI": "10.1002/rob.22328", "PubYear": 2024, "Volume": "41", "Issue": "7", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Kharagpur  Kharagpur West Bengal India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Kharagpur  Kharagpur West Bengal India"}, {"AuthorId": 3, "Name": " Ambuj", "Affiliation": "Indian Institute of Technology Kharagpur  Kharagpur West Bengal India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Kharagpur  Kharagpur West Bengal India"}], "References": [{"Title": "A finite element modeling‐based approach to predict vibrations transmitted through different body segments of the operator within the workspace of a small tractor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "6", "Page": "1543", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Real‐time vibration monitoring and analysis of agricultural tractor drivers using an IoT‐based system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "7", "Page": "1723", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Modeling and optimization using artificial neural network and genetic algorithm of self‐propelled machine reach envelope", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "41", "Issue": "7", "Page": "2373", "JournalTitle": "Journal of Field Robotics"}]}, {"ArticleId": 114307858, "Title": "A Study on Enhancing Chip Detection Efficiency Using the Lightweight Van-YOLOv8 Network", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.048510", "PubYear": 2024, "Volume": "79", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A Review of Yolo Algorithm Developments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "1066", "JournalTitle": "Procedia Computer Science"}, {"Title": "Attention-based deep learning for chip-surface-defect detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "3-4", "Page": "1957", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 114307867, "Title": "A Metaheuristic Perspective on Extracting Numeric Association Rules: Current Works, Applications, and Recommendations", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11831-024-10109-3", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A novel hybrid GA–PSO framework for mining quantitative association rules", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "6", "Page": "4645", "JournalTitle": "Soft Computing"}, {"Title": "Performance analysis of multi-objective artificial intelligence optimization algorithms in numerical association rule mining", "Authors": "<PERSON><PERSON>; Bilal Alatas", "PubYear": 2020, "Volume": "11", "Issue": "8", "Page": "3449", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Performance analysis of multi-objective artificial intelligence optimization algorithms in numerical association rule mining", "Authors": "<PERSON><PERSON>; Bilal Alatas", "PubYear": 2020, "Volume": "11", "Issue": "8", "Page": "3449", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Privacy-preserving in association rule mining using an improved discrete binary artificial bee colony", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "113097", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Exploring Technology Influencers from Patent Data Using Association Rule Mining and Social Network Analysis", "Authors": "Pranomkorn Ampornphan; Sutep Tongngam", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "333", "JournalTitle": "Information"}, {"Title": "RETRACTED ARTICLE: Association rule mining using fuzzy logic and whale optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "1431", "JournalTitle": "Soft Computing"}, {"Title": "Differential evolution and sine cosine algorithm based novel hybrid multi-objective approaches for numerical association rule mining", "Authors": "<PERSON><PERSON>; Bilal Alatas", "PubYear": 2021, "Volume": "554", "Issue": "", "Page": "198", "JournalTitle": "Information Sciences"}, {"Title": "An Association Rule Mining Approach to Discover Demand and Supply Patterns Based on Thai Social Media Data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "1", "JournalTitle": "International Journal of Knowledge and Systems Science"}, {"Title": "A predictive GA-based model for closed high-utility itemset mining", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107422", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Systematic Assessment of Numerical Association Rule Mining Methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "5", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "An Efficient Method for Mining Rare Association Rules: A Case Study on Air Pollution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "2150018", "JournalTitle": "International Journal on Artificial Intelligence Tools"}, {"Title": "A Discrete Crow Search Algorithm for Mining Quantitative Association Rules", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "101", "JournalTitle": "International Journal of Swarm Intelligence Research"}, {"Title": "Chaos numbers based a new representation scheme for evolutionary computation: Applications in evolutionary association rule mining", "Authors": "<PERSON><PERSON>; Bilal Alatas", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "e6744", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Fuzzy association rule mining approach to identify e-commerce product association considering sales amount", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "2", "Page": "1551", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Prairie Dog Optimization Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "22", "Page": "20017", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A Multiobjective Crystal Optimization-based association rule mining enhanced with TOPSIS for predictive maintenance analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "2782", "JournalTitle": "Procedia Computer Science"}, {"Title": "Mountain Gazelle Optimizer: A new Nature-inspired Metaheuristic Algorithm for Global Optimization Problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "174", "Issue": "", "Page": "103282", "JournalTitle": "Advances in Engineering Software"}, {"Title": "A modified multi-objective slime mould algorithm with orthogonal learning for numerical association rules mining", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "8", "Page": "6125", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Solution-based tabu search for the capacitated dispersion problem", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "223", "Issue": "", "Page": "119856", "JournalTitle": "Expert Systems with Applications"}, {"Title": "DETDO: An adaptive hybrid dandelion optimizer for engineering optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "57", "Issue": "", "Page": "102004", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A multi-objective Chaos Game Optimization algorithm based on decomposition and random learning mechanisms for numerical optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "110525", "JournalTitle": "Applied Soft Computing"}, {"Title": "An efficient adaptive large neighborhood search algorithm based on heuristics and reformulations for the generalized quadratic assignment problem", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "106802", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "<PERSON><PERSON><PERSON> shark optimizer: A novel nature-inspired algorithm for engineering optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "58", "Issue": "", "Page": "102210", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Modified Elite Opposition-Based Artificial Hummingbird Algorithm for Designing FOPID Controlled Cruise Control System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "38", "Issue": "2", "Page": "169", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 114307878, "Title": "Digital Management Mode of Real Estate Marketing based on Big Data and Artificial Intelligence", "Abstract": "<p>To cope with the pressure on sales information processing as the real estate industry grows, the study builds a real estate digital marketing management system design based on the analysis of real estate marketing needs to meet the needs of real estate marketers for digital information processing, and builds a hybrid recommendation model using a combination of Gradient Boosting Decision Tree (GBDT) technology and Logistic Regression (LR) to accurately recommend real estate potential purchase users. The GBDT-LR model performance test results show an accuracy of 94.63% and a regression rate of 94.82%, which is particularly good in terms of classification accuracy, and the system CPU occupancy rate basically stays below 30% during the whole script running period, and the system still maintains good system stability when the TPS user concurrency is 150, and it’s using experience is better. The comparison of the ROC curve of the GBDT-LR model shows that the GBDT-LR model's accuracy is as high as 92%, which is better than the performance of most of the classification models, and it can meet the practical application requirements of the real estate industry and provide a good solution for the real estate industry. It can meet the actual application requirements of the real estate industry and provide a scientific and systematic digital management solution for the real estate industry.</p>", "Keywords": "digital management; Gradient Boosting Decision Tree; Logistic Regression; Machine algorithm; Marketing management; real estates", "DOI": "10.37394/232018.2024.12.26", "PubYear": 2024, "Volume": "12", "Issue": "", "JournalId": 73734, "JournalTitle": "WSEAS TRANSACTIONS ON COMPUTER RESEARCH", "ISSN": "1991-8755", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lyceum of the Philippines University Manila Campus, Manila 1002, PHILIPPINES"}], "References": [{"Title": "A Novel Adaptive Mutation PSO Optimized SVM Algorithm for sEMG-Based Gesture Recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}, {"Title": "Semantics-Aware Context-Based Learner Modelling Using Normalized PSO for Personalized E-learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Journal of Web Engineering"}]}, {"ArticleId": 114307908, "Title": "Fast and exact fixed-radius neighbor search based on sorting", "Abstract": "<p>Fixed-radius near neighbor search is a fundamental data operation that retrieves all data points within a user-specified distance to a query point. There are efficient algorithms that can provide fast approximate query responses, but they often have a very compute-intensive indexing phase and require careful parameter tuning. Therefore, exact brute force and tree-based search methods are still widely used. Here we propose a new fixed-radius near neighbor search method, called SNN, that significantly improves over brute force and tree-based methods in terms of index and query time, provably returns exact results, and requires no parameter tuning. SNN exploits a sorting of the data points by their first principal component to prune the query search space. Further speedup is gained from an efficient implementation using high-level basic linear algebra subprograms (BLAS). We provide theoretical analysis of our method and demonstrate its practical performance when used stand-alone and when applied within the DBSCAN clustering algorithm.</p>", "Keywords": "Fixed-radius search;Near neighbor search", "DOI": "10.7717/peerj-cs.1929", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Charles University Prague, Prague, Czech Republic"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Manchester, Manchester, United Kingdom"}], "References": [{"Title": "ANN-Benchmarks: A benchmarking tool for approximate nearest neighbor algorithms", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "101374", "JournalTitle": "Information Systems"}, {"Title": "A novel density-based clustering algorithm using nearest neighbor graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107206", "JournalTitle": "Pattern Recognition"}, {"Title": "Refining a k -nearest neighbor graph for a computationally efficient spectral clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "107869", "JournalTitle": "Pattern Recognition"}, {"Title": "Efficient k -nearest neighbor search based on clustering and adaptive k values", "Authors": "<PERSON>; <PERSON>-<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108356", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 114308019, "Title": "Dataset Generation Methodology: Towards Application of Machine Learning in Industrial Water Treatment Security", "Abstract": "<p>Successful cyber attacks against industrial systems, such as water treatment systems, can lead to irreparable consequences for public health and the economy. Machine learning and deep learning could help detecting and forecasting previously unknown cyber attacks but require specific datasets. The number of publicly available datasets in this field is very limited and the majority of the publicly available datasets used in cyber security tasks have severe flows. In this paper, the authors introduce the unified methodology for the generation of the dataset for industrial water treatment security. Detailed specification of stages of the methodology is given. The paper ends with a usage scenario describing preparatory stages for dataset generation for the cybersecurity research in water treatment systems, namely, specification of the technological process, testbed development, and development of the attack model for the considered technological process. The developed methodology will be used for the dataset generation, that, in turn, will be used to develop and test cyber attack detection methods based on machine learning and deep learning, and to strengthen the water treatment systems’ security.</p>", "Keywords": "Water treatment system; Dataset; Testbed; Cyber security; Methodology; Cyber attacks", "DOI": "10.1007/s42979-024-02704-9", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Computer Security Problems, St. Petersburg Federal Research Center of Russian Academy of Sciences, St. Petersburg, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory of Computer Security Problems, St. Petersburg Federal Research Center of Russian Academy of Sciences, St. Petersburg, Russian Federation; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Computer Security Problems, St. Petersburg Federal Research Center of Russian Academy of Sciences, St. Petersburg, Russian Federation; Empress <PERSON> Petersburg Mining University, St. Petersburg, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Laboratory of Computer Security Problems, St. Petersburg Federal Research Center of Russian Academy of Sciences, St. Petersburg, Russian Federation"}], "References": [{"Title": "A machine learning based attack detection and mitigation using a secure SaaS framework", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "7", "Page": "4047", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Deep Learning-based Anomaly Detection in Cyber-physical Systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Threat modelling for industrial cyber physical systems in the era of smart manufacturing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "137", "Issue": "", "Page": "103611", "JournalTitle": "Computers in Industry"}, {"Title": "Datasets are not enough: Challenges in labeling network traffic", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "102810", "JournalTitle": "Computers & Security"}, {"Title": "SCADA vulnerabilities and attacks: A review of the state‐of‐the‐art and open issues", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "", "Page": "103028", "JournalTitle": "Computers & Security"}, {"Title": "A review of Machine Learning-based zero-day attack detection: Challenges and future directions", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "198", "Issue": "", "Page": "175", "JournalTitle": "Computer Communications"}, {"Title": "Image-Based Approach to Intrusion Detection in Cyber-Physical Objects", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "12", "Page": "553", "JournalTitle": "Information"}]}, {"ArticleId": 114308208, "Title": "Solar based Smart Sanitizer Dispenser System with Internet of Things (IoT) Technology", "Abstract": "In response to the growing demand for efficient and sustainable hygiene solutions, this project introduces a Solar-Powered Smart Sanitizer Dispenser System leveraging IoT technology. The system aims to address the need for touchless and accessible hand sanitization in various environments, including public spaces, healthcare facilities, and commercial establishments. By integrating solar power as the primary energy source, the system offers autonomy and eco-friendliness, reducing dependency on conventional electricity grids and minimizing environmental impact. The core components of the system include a smart dispenser unit equipped with sensors for detecting hand proximity, a reservoir for storing sanitizer solution, and a microcontroller unit responsible for data processing and IoT connectivity. Through the IoT interface, users can remotely monitor sanitizer levels, receive real-time usage statistics, and manage dispenser settings, enhancing operational efficiency and maintenance. Furthermore, the incorporation of solar panels ensures continuous operation even in off-grid locations, making the system suitable for deployment in remote or outdoor settings where access to electricity may be limited. Maximizing the efficacy of solar power conversion and extending operational uptime.", "Keywords": "Solar Power;Smart Sanitizer Dispenser;IoT Technology;Touchless Hygiene;Sustainability;Remote Monitoring;Energy Efficiency;Environmental Impact;Public Health;Renewable Energy Integration", "DOI": "10.32628/CSEIT2410226", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty, Information Technology Department, College of Computing and Information Sciences, University of Technology and Applied Sciences, Salalah, Oman"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Aqil Alawi Al Murazza Ba Abood Al Murazza Ba Abood", "Affiliation": "Student, Information Technology Department, College of Computing and Information Sciences, University of Technology and Applied Sciences, Salalah, Oman"}, {"AuthorId": 3, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Faculty, Information Technology Department, College of Computing and Information Sciences, University of Technology and Applied Sciences, Salalah, Oman"}], "References": [{"Title": "IoT Based Hand Sanitizer", "Authors": "<PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON>;  <PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "331", "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology"}]}, {"ArticleId": 114308278, "Title": "Correlation Composition Awareness Model with Pair Collaborative Localization for IoT Authentication and Localization", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.048621", "PubYear": 2024, "Volume": "79", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Survey of Localization for Internet of Things Nodes: Approaches, Challenges and Open Issues", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "8", "Page": "210", "JournalTitle": "Future Internet"}, {"Title": "A lightweight federated learning based privacy preserving B5G pandemic response network using unmanned aerial vehicles: A proof-of-concept", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "108672", "JournalTitle": "Computer Networks"}, {"Title": "Hybrid Intrusion Detection using MapReduce based Black Widow Optimized Convolutional Long Short-Term Memory Neural Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "194", "Issue": "", "Page": "116545", "JournalTitle": "Expert Systems with Applications"}, {"Title": "FELIDS: Federated learning-based intrusion detection system for agricultural Internet of Things", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "165", "Issue": "", "Page": "17", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "A WKNN-based approach for NB-IoT sensors localization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "1", "Page": "175", "JournalTitle": "Digital Communications and Networks"}, {"Title": "A Two-layer Fog-Cloud Intrusion Detection Model for IoT Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "", "Page": "100557", "JournalTitle": "Internet of Things"}, {"Title": "HBFL: A hierarchical blockchain-based federated learning framework for collaborative IoT intrusion detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "103", "Issue": "", "Page": "108379", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Maximizing coverage and maintaining connectivity in WSN and decentralized IoT: an efficient metaheuristic-based method for environment-aware node deployment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "1", "Page": "611", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Towards accurate and privacy-preserving localization using anchor quality assessment in Internet of Things", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "524", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "An eSIM-based remote credential provisioning and authentication protocol for IoT devices in 5G cellular network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "", "Page": "100876", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 114308304, "Title": "Review on research and development of planetary penetrator", "Abstract": "A planetary penetrator is a kind of machine that can use its special structure to independently penetrate an exoplanet and sample it for storage. It is a hot research topic in the world and has important development potential in the field of deep space exploration. Based on the analysis of relevant literature, this paper classifies and compares various representative planetary penetrator projectiles around the current research status. The development progress and key technologies of the planetary penetrator are analyzed, the current technical challenges are summarized, and the development trend of the planetary penetrator has prospected. The planetary probe has a broad application prospect, and its development will provide technical support for humans to establish extraterrestrial habitat, and play an important role in the field of deep space exploration. Currently, planetary penetrator research is still in its infancy, and the field is full of possibilities that need to be explored and improved.", "Keywords": "", "DOI": "10.1002/rob.22326", "PubYear": 2024, "Volume": "41", "Issue": "5", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation Chinese Academy of Sciences Shenyang China;Institutes for Robotics and Intelligent Manufacturing Chinese Academy of Sciences Shenyang China;University of Chinese Academy of Sciences Beijing China"}, {"AuthorId": 2, "Name": "Jingkai Feng", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation Chinese Academy of Sciences Shenyang China;Institutes for Robotics and Intelligent Manufacturing Chinese Academy of Sciences Shenyang China;University of Chinese Academy of Sciences Beijing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation Chinese Academy of Sciences Shenyang China;Institutes for Robotics and Intelligent Manufacturing Chinese Academy of Sciences Shenyang China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation Chinese Academy of Sciences Shenyang China;Institutes for Robotics and Intelligent Manufacturing Chinese Academy of Sciences Shenyang China"}, {"AuthorId": 5, "Name": "Yuanzheng Tian", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation Chinese Academy of Sciences Shenyang China;Institutes for Robotics and Intelligent Manufacturing Chinese Academy of Sciences Shenyang China"}, {"AuthorId": 6, "Name": "Da Lu", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation Chinese Academy of Sciences Shenyang China;Institutes for Robotics and Intelligent Manufacturing Chinese Academy of Sciences Shenyang China"}], "References": [{"Title": "Passive Morphological Adaptation for Obstacle Avoidance in a Self-Growing Robot Produced by Additive Manufacturing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "85", "JournalTitle": "Soft Robotics"}, {"Title": "Controlling subterranean forces enables a fast, steerable, burrowing soft robot", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "55", "Page": "eabe2922", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 114308375, "Title": "Saliency information and mosaic based data augmentation method for densely occluded object recognition", "Abstract": "<p>Data augmentation methods are crucial to improve the accuracy of densely occluded object recognition in the scene where the quantity and diversity of training images are insufficient. However, the current methods that use regional dropping and mixing strategies suffer from the problem of missing foreground objects and redundant background features, which can lead to densely occluded object recognition issues in classification or detection tasks. Herein, saliency information and mosaic based data augmentation method for densely occluded object recognition is proposed, which utilizes saliency information as prior knowledge to supervise the mosaic process of training images containing densely occluded objects. And the method uses fogging processing and class label mixing to construct new augmented images, in order to improve the accuracy of image classification and object recognition tasks by augmenting the quantity and diversity of training images. Extensive experiments on different classification datasets with various CNN architectures prove the effectiveness of our method.</p>", "Keywords": "Data augmentation; Object recognition; Saliency information; Mosaic process; Image fogging processing", "DOI": "10.1007/s10044-024-01258-z", "PubYear": 2024, "Volume": "27", "Issue": "2", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China"}, {"AuthorId": 2, "Name": "Xiangfeng Luo", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China"}], "References": [{"Title": "Deep learning-based data augmentation method and signature verification system for offline handwritten signature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>lu", "PubYear": 2021, "Volume": "24", "Issue": "1", "Page": "165", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "Saliency detection based on hybrid artificial bee colony and firefly optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "4", "Page": "757", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "An overview of mixing augmentation methods and augmentation strategies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "3", "Page": "2111", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Hyper-sausage coverage function neuron model and learning algorithm for image classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "109216", "JournalTitle": "Pattern Recognition"}, {"Title": "A Comprehensive Survey of Image Augmentation Techniques for Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "137", "Issue": "", "Page": "109347", "JournalTitle": "Pattern Recognition"}, {"Title": "CPSS-FAT: A consistent positive sample selection for object detection with full adaptive threshold", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "109627", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 114308390, "Title": "Multivocal literature review on zero-trust security implementation", "Abstract": "The sudden shift from physical office location to a fully remote or hybrid work model accelerated by the COVID19 pandemic, is a phenomenon that changed how organizations traditionally operated and thereby introduced new vulnerabilities and consequently changed the cyber threat landscape. This has led organizations around the globe to seek new approaches to protect their enterprise network. One such approach is the adoption of the Zero Trust security approach due to its many advantages over the traditional/perimeter security approach. Although zero trust presents a stronger defense approach over the perimeter security model, organizations are hesitant to fully embrace it. This is partly due to the lack of a unified zero-trust implementation framework that can be used to guide its adoption. As such, we conducted a multivocal review that included literature from both academic and non-academic sources to consolidate knowledge on the state-of-the-art of zero-trust implementation and identify gaps in literature. Our result shows that existing papers tend to have a narrow viewpoint on the approach of implementing zero trust, rather than an encompassing viewpoint that can provide a more holistic view on the topic. We developed a conceptual framework that articulates the five core components involved in the implementation of zero trust security, guided by key questions designed to guide the implementation process.", "Keywords": "", "DOI": "10.1016/j.cose.2024.103827", "PubYear": 2024, "Volume": "141", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Technology, University of Cincinnati, Cincinnati, OH, 45220, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Technology, University of Cincinnati, Cincinnati, OH, 45220, USA"}], "References": [{"Title": "Cloud-Based Zero Trust Access Control Policy: An Approach to Support Work-From-Home Driven by COVID-19 Pandemic", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON> <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "3-4", "Page": "599", "JournalTitle": "New Generation Computing"}, {"Title": "Never trust, always verify: A multivocal literature review on current knowledge and research gaps of zero-trust", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "102436", "JournalTitle": "Computers & Security"}, {"Title": "A Blockchain-Based Access Control Scheme for Zero Trust Cross-Organizational Data Sharing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}]}, {"ArticleId": 114308534, "Title": "Correcting inconsistencies in knowledge graphs with correlated knowledge", "Abstract": "Knowledge graphs (KGs) have been widely applied for semantic representation and intelligent decision-making. The usefulness and usability of KGs is often limited by quality of KGs. One common issue is the presence of inconsistent assertions in KGs. Inconsistencies in KGs are often caused by diverse data that are applied for automatically constructing large-scale KGs. To improve quality of KGs, in this paper, we investigate how to detect and correct inconsistent triples in KGs. We first identify entity-related inconsistency, relation-related inconsistency and type-related inconsistency. On the basis, we propose a framework of correcting the identified inconsistencies, which combines candidate generation, link prediction and constraint validation. We evaluate the proposed correction framework in the real-word dataset FB15k (from Freebase). The promising results confirm the capability of our framework in correcting the inconsistencies of knowledge graphs.", "Keywords": "", "DOI": "10.1016/j.bdr.2024.100450", "PubYear": 2024, "Volume": "36", "Issue": "", "JournalId": 3265, "JournalTitle": "Big Data Research", "ISSN": "2214-5796", "EISSN": "2214-580X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing University of Aeronautics and Astronautics, Nanjing 211106, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Massive Personalized Customization System and Technology, Qingdao, 266100, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Massive Personalized Customization System and Technology, Qingdao, 266100, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Aeronautics and Astronautics, Nanjing 211106, China"}, {"AuthorId": 5, "Name": "Jing <PERSON>", "Affiliation": "Nanjing University of Aeronautics and Astronautics, Nanjing 211106, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Aeronautics and Astronautics, Nanjing 211106, China;Corresponding author"}], "References": []}, {"ArticleId": 114308571, "Title": "Stability analysis of time-varying neutral-type stochastic systems with both discrete and distributed delays", "Abstract": "The uniform stability analysis of time-varying neutral-type stochastic systems with both discrete and distributed delays is investigated in this paper. By the method based on Lyapunov function and some inequality techniques, novel stability criteria are obtained to ensure the uniform stability and uniform exponential stability in mean square of the studied system, respectively. The established results are characterized by the fact that the time-derivatives of the constructed <PERSON><PERSON><PERSON><PERSON> function are permissible for sign indefinite. Furthermore, by selecting a specific <PERSON><PERSON><PERSON>nov function, novel explicit stability criteria are given, which is less conservative than the exist results. At last two examples are provided to illustrate the effectiveness and superiority of the proposed theoretical results.", "Keywords": "", "DOI": "10.1016/j.sysconle.2023.105643", "PubYear": 2023, "Volume": "181", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics, Harbin Institute of Technology, Harbin, 150001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics, Harbin Institute of Technology, Harbin, 150001, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Telecommunication Satellite, China Academy of Space Technology, Beijing, 100094, China"}], "References": [{"Title": "<PERSON><PERSON>r criteria for exponential stability of functional differential equations", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "104642", "JournalTitle": "Systems & Control Letters"}, {"Title": "On exponential stability in mean square of neutral stochastic functional differential equations", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "154", "Issue": "", "Page": "104965", "JournalTitle": "Systems & Control Letters"}, {"Title": "A novel approach to exponential stability in mean square of stochastic difference systems with delays", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "168", "Issue": "", "Page": "105372", "JournalTitle": "Systems & Control Letters"}]}, {"ArticleId": 114308647, "Title": "A comparative study of methods for estimating model-agnostic Shapley value explanations", "Abstract": "Shapley values originated in cooperative game theory but are extensively used today as a model-agnostic explanation framework to explain predictions made by complex machine learning models in the industry and academia. There are several algorithmic approaches for computing different versions of Shapley value explanations. Here, we consider Shapley values incorporating feature dependencies, referred to as conditional Shapley values, for predictive models fitted to tabular data. Estimating precise conditional Shapley values is difficult as they require the estimation of non-trivial conditional expectations. In this article, we develop new methods, extend earlier proposed approaches, and systematize the new refined and existing methods into different method classes for comparison and evaluation. The method classes use either Monte Carlo integration or regression to model the conditional expectations. We conduct extensive simulation studies to evaluate how precisely the different method classes estimate the conditional expectations, and thereby the conditional Shapley values, for different setups. We also apply the methods to several real-world data experiments and provide recommendations for when to use the different method classes and approaches. Roughly speaking, we recommend using parametric methods when we can specify the data distribution almost correctly, as they generally produce the most accurate Shapley value explanations. When the distribution is unknown, both generative methods and regression models with a similar form as the underlying predictive model are good and stable options. Regression-based methods are often slow to train but quickly produce the Shapley value explanations once trained. The vice versa is true for Monte Carlo-based methods, making the different methods appropriate in different practical situations.", "Keywords": "Explainable artificial intelligence; S<PERSON>pley values; Model-agnostic explanation; Prediction explanation; Feature dependence; Feature importance", "DOI": "10.1007/s10618-024-01016-z", "PubYear": 2024, "Volume": "38", "Issue": "4", "JournalId": 3928, "JournalTitle": "Data Mining and Knowledge Discovery", "ISSN": "1384-5810", "EISSN": "1573-756X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, University of Oslo, Oslo, Norway; The Alan Turing Institute, London, UK; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, University of Oslo, Oslo, Norway"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Norwegian Computing Center, Oslo, Norway"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Norwegian Computing Center, Oslo, Norway"}], "References": [{"Title": "From local explanations to global understanding with explainable AI for trees", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "56", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Algorithms to estimate S<PERSON>pley value feature attributions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "6", "Page": "590", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 114308782, "Title": "The differential effects of self-view in virtual meetings when speaking vs. listening", "Abstract": "", "Keywords": "", "DOI": "10.1080/0960085X.2024.2325350", "PubYear": 2025, "Volume": "34", "Issue": "2", "JournalId": 3498, "JournalTitle": "European Journal of Information Systems", "ISSN": "0960-085X", "EISSN": "1476-9344", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Chair of Business Informatics, esp. Social Media and Society, University of Potsdam, Potsdam, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chair of Business Informatics, esp. Social Media and Society, University of Potsdam, Potsdam, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Weizenbaum Institute for the Networked Society and University of Potsdam, Berlin, Germany"}], "References": [{"Title": "An affordance perspective of team collaboration and enforced working from home during COVID-19", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "4", "Page": "429", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Virtually in this together – how web-conferencing systems enabled a new virtual togetherness during the COVID-19 crisis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "5", "Page": "563", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Adjusting to epidemic-induced telework: empirical insights from teleworkers in France", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "1", "Page": "69", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Productive and connected while working from home: what client-facing remote workers can learn from telenurses about ‘belonging through technology’", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "1", "Page": "89", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Meeting Effectiveness and Inclusiveness in Remote Collaboration", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Reviewing the Contributing Factors and Benefits of Distributed Collaboration", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "47", "Issue": "", "Page": "476", "JournalTitle": "Communications of the Association for Information Systems"}, {"Title": "Fighting Zoom Fatigue: Keeping the Zoombies at Bay", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "40", "JournalTitle": "Communications of the Association for Information Systems"}, {"Title": "The constant mirror: Self-view and attitudes to virtual meetings", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "107110", "JournalTitle": "Computers in Human Behavior"}, {"Title": "On the stress potential of videoconferencing: definition and root causes of Zoom fatigue", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "1", "Page": "153", "JournalTitle": "Electronic Markets"}, {"Title": "Video-conferencing usage dynamics and nonverbal mechanisms exacerbate Zoom Fatigue, particularly for women", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; A.C.M<PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "", "Page": "100271", "JournalTitle": "Computers in Human Behavior Reports"}]}, {"ArticleId": 114308990, "Title": "NGPCA: Clustering of high-dimensional and non-stationary data streams", "Abstract": "Neural Gas Principal Component Analysis (NGPCA) is an online clustering algorithm. An NGPCA model is a mixture of local PCA units and combines dimensionality reduction with vector quantization. Recently, NGPCA has been extended with an adaptive learning rate and an adaptive potential function for accurate and efficient clustering of high-dimensional and non-stationary data streams. The algorithm achieved highly competitive results on clustering benchmark datasets compared to the state of the art. Our implementation of the algorithm was developed in MATLAB and is available as open source. This code can be easily applied to the clustering of stationary and non-stationary data.", "Keywords": "NGPCA ; Clustering ; Data stream clustering ; Local PCA ; MATLAB", "DOI": "10.1016/j.simpa.2024.100635", "PubYear": 2024, "Volume": "20", "Issue": "", "JournalId": 66395, "JournalTitle": "Software Impacts", "ISSN": "2665-9638", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for Applied Data Science Gütersloh, Faculty of Engineering and Mathematics, Bielefeld University of Applied Science and Arts, Bielefeld, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Group, Faculty of Technology, Bielefeld University, Bielefeld, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Applied Data Science Gütersloh, Faculty of Engineering and Mathematics, Bielefeld University of Applied Science and Arts, Bielefeld, Germany"}], "References": [{"Title": "Data stream clustering: a review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Volkan Atalay", "PubYear": 2021, "Volume": "54", "Issue": "2", "Page": "1201", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Adaptive local Principal Component Analysis improves the clustering of high-dimensional data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "146", "Issue": "", "Page": "110030", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 114309020, "Title": "Оцінка наукової області комп’ютерна фізика за аналітичними можливостями бази даних Scopus", "Abstract": "<p>У статті показано аналітичні можливості міжнародної наукометричної бази даних Scopus. Проведено порівняльний аналіз передових публікацій світових і українських науковців в предметній категорії комп’ютерна фізика, індексованих у БД Scopus. Окреслено коло провідних видань, розглянуто особливості міжнародної співпраці у предметній категорії, проаналізовано фактори впливовості вчених різних країн на розвиток цієї наукової області. Сформульовано рекомендації для одержання якісної аналітичної оцінки результатів при аналізі і обробці даних з використанням БД Scopus</p>", "Keywords": "computer physics; publications; scientific research; Scopus database", "DOI": "10.36910/6775-2524-0560-2024-54-27", "PubYear": 2024, "Volume": "", "Issue": "54", "JournalId": 71325, "JournalTitle": "КОМП’ЮТЕРНО-ІНТЕГРОВАНІ ТЕХНОЛОГІЇ: ОСВІТА, НАУКА, ВИРОБНИЦТВО", "ISSN": "2524-0552", "EISSN": "2524-0560", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114309048, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0167-8655(24)00096-5", "PubYear": 2024, "Volume": "180", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "Collaborative transportation for attended home deliveries", "Abstract": "Attended home deliveries (AHDs) are characterized by dynamic customer acceptance and narrow customer‐specific delivery time windows. Both impede efficient routing and thus make AHDs very costly. In this article, we explore how established horizontal collaborative transportation planning methods can be adapted to render AHDs more efficient. The general idea is to enable request reallocation between multiple collaborating carriers after the order capture phase. We use an established centralized reallocation framework that allows participating carriers to submit delivery requests for reallocation. We extend this framework for AHD specifics such as the dynamic arrival of customer requests and information about delivery time windows. Using realistic instances based on the city of Vienna, we quantify the collaboration savings by solving the underlying routing and reallocation problems. We show that narrow time windows can lower the savings obtainable by the reallocation by up to 15%. Therefore, we suggest enhancing the decision processes of request selection and request bundling using information about delivery time windows. Our findings demonstrate that adapting methods of request selection and bundle generation to environments with narrow time windows can increase collaboration savings by up to 25% and 35%, respectively in comparison to methods that work well only when no time windows are imposed.", "Keywords": "attended home delivery;collaborative transportation planning;combinatorial auction;operations research;time windows;vehicle routing", "DOI": "10.1002/net.22216", "PubYear": 2024, "Volume": "84", "Issue": "1", "JournalId": 7540, "JournalTitle": "Networks", "ISSN": "0028-3045", "EISSN": "1097-0037", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Decisions and Analytics University of Vienna  Wien Austria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Business Decisions and Analytics University of Vienna  Wien Austria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Production Management and Logistics University of Klagenfurt  Klagenfurt Austria"}], "References": []}, {"ArticleId": *********, "Title": "Voting Model Strategies for Reliable Categorical IoT-DDoS Attack Prediction", "Abstract": "This research focuses on developing reliable categorical IoT-DDoS attack prediction models using ensemble voting strategies. The study explores various machine learning algorithms suitable for categorical data analysis, employing feature engineering techniques to preprocess IoT data. Ensemble learning methodologies, including bagging, boosting, and stacking, are then utilized to build robust prediction models. Evaluation metrics such as precision, recall, F1-score, and AUC-ROC are used to assess model performance, demonstrating the effectiveness of ensemble voting models in reliably predicting IoT-DDoS attacks. Comparative analyses with individual classifiers highlight the advantages of ensemble approaches in terms of predictive accuracy and robustness against data imbalances and noise. This work contributes to advancing IoT security by providing a practical framework for deploying predictive models that aid in early detection and mitigation of DDoS attacks, enhancing overall resilience against cyber threats in IoT ecosystems.", "Keywords": "IoT-DDoS;Ensemble Voting Strategies;Categorical Data Analysis;Machine Learning Algorithms;Feature Engineering", "DOI": "10.32628/CSEIT2410223", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Scholar, Department of Computer Engineering, Sigma Institute of Engineering, Gujarat, India"}, {"AuthorId": 2, "Name": "Dr. <PERSON><PERSON><PERSON>", "Affiliation": "Professor & Head of Department, Department of Computer Engineering, Sigma University, Gujarat, India"}], "References": [{"Title": "Utilization of blockchain for mitigating the distributed denial of service attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "3", "Page": "", "JournalTitle": "Security and Privacy"}, {"Title": "Survey on IoT security: Challenges and solution using machine learning, artificial intelligence and blockchain technology", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100227", "JournalTitle": "Internet of Things"}, {"Title": "AS-IDS: Anomaly and Signature Based IDS for the Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "Machine Learning based Attacks Detection and Countermeasures in IoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "2", "Page": "", "JournalTitle": "International Journal of Communication Networks and Information Security (IJCNIS)"}, {"Title": "Towards a machine learning-based framework for DDOS attack detection in software-defined IoT (SD-IoT) networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106432", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 114309576, "Title": "A multidimensional integrated micro three-component fluxgate sensor based on microelectromechanical system technology", "Abstract": "In this paper, a miniature three-component fluxgate sensor with U-shaped structure is fabricated by the multidimensional integration technology. We manufacture a batch of single axis solenoid fluxgate chips based on Micro Electromechanical System （MEMS） technology, which possesses the Co-based amorphous ribbon core and solenoid excitation-detection coil. According to multidimensional integration theory, a full integrated three-component fluxgate sensors is achieved through orthogonal linking of printed circuit board （PCB） substrates and bonding between single axis fluxgate chip and substrate. The chips used in the X-Y-Z axis have a sensitivity of 1947 V/T, 1951 V/T, and 1949 V/T, and noise power spectral density of 0.061 nT√Hz@1HZ, 0.077 nT√Hz@1HZ, and 0.087 nT√Hz@1HZ, and time-drift peaks of 21 nT, 17.4 nT, and 14.8 nT, respectively. The linear range of three chips is 0–70μT. After least square error correction, the peak-to-peak value of the in-situ total magnetic field decreases from 2.4 μT to 0.22 μT.", "Keywords": "", "DOI": "10.1016/j.sna.2024.115315", "PubYear": 2024, "Volume": "371", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Thin Film and Microfabrication Technology (Ministry of Education), Department of Micro/Nano Electronics, School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai, CO 200240, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Thin Film and Microfabrication Technology (Ministry of Education), Department of Micro/Nano Electronics, School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai, CO 200240, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Submarine Geosciences, Second Institute of Oceanography, Ministry of Natural Resources, Hangzhou, CO 310012, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Ship Development and Design Center, Wuhan, CO 430064, China"}, {"AuthorId": 5, "Name": "Xuecheng Sun", "Affiliation": "Research and Development Center of Microelectronics, School of Mechatronic Engineering and Automation, Shanghai University, Shanghai, CO 200444, China;Key Laboratory of Advanced Display and System Applications of Ministry of Education, Shanghai, CO 200072, China;Guizhou Aerospace Institute of Measuring and Testing Technology, Guiyang, CO 550009, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Thin Film and Microfabrication Technology (Ministry of Education), Department of Micro/Nano Electronics, School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai, CO 200240, China;Corresponding author"}], "References": [{"Title": "Offset drift in orthogonal fluxgate and importance of closed-loop operation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "342", "Issue": "", "Page": "113583", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 114309593, "Title": "A Survey on Transforming Healthcare with IoMT : The Power of Connected Medical Devices", "Abstract": "This thorough analysis examines current developments in medical technology, with an emphasis on the Internet of Medical Things (IoMT) and its uses. It covers IoMT's contribution in creating wearable internet monitors for dispersed monitoring systems, addressing pregnancy-related issues, and is divided into four subject groups. Along with blockchain adaption, it also highlights privacy and dependability in IoMT-based health monitoring systems. The paper also emphasizes the contributions of IoMT to healthcare diagnosis and monitoring, including machine learning and edge-AI-based cardiovascular health monitoring and fall prediction. It also covers data science and healthcare informatics techniques, such as using lightweight cryptography to ensure real-time security in remote patient monitoring and identifying unusual user behavior. IoMT has the potential to transform patient care, diagnostics, and data-driven decision-making across a range of healthcare areas, as the paper highlights overall.", "Keywords": "Medical Technology;Internet of Medical Things;Wearable Devices;Remote Monitoring;Healthcare Diagnosis;Data Science;Distributed Monitoring;Pregnancy;Reliability;Privacy;Blockchain;Healthcare Monitoring;Fall Prediction;Cardiovascular Health;Machine Learning;Edge-AI;Healthcare Informatics;Anomaly Detection;Security;Cryptography;Patient Care", "DOI": "10.32628/CSEIT2410219", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "Dr. <PERSON><PERSON>", "Affiliation": "Associate Professor <PERSON>um ANO, Department of Software Systems, PSG College of Arts & Science, Coimbatore, India"}, {"AuthorId": 2, "Name": "Dr. <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Associate Professor & Head, Department of Software Systems, PSG College of Arts & Science, Coimbatore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Software Systems, PSG College of Arts & Science, Coimbatore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Software Systems, PSG College of Arts & Science, Coimbatore, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Systems, PSG College of Arts & Science, Coimbatore, India"}], "References": []}, {"ArticleId": 114309670, "Title": "Tandem hybridization chain reaction and selective coordination enable fluorescence detection of exosomes in lung cancer", "Abstract": "Liquid biopsy based on tumor-derived exosomes (TDEs) is potentially effective in assisting early cancer diagnosis. However, accurate quantification of the approach remains challenging due to the low concentration and heterogeneity of TDEs. This work involved designing a homogeneous enzyme-free fluorescent sensing strategy by targeting programmed cell death ligand 1 (PD-L1) that was overexpressed on the surface of TDEs, coupled with the tandem hybridization chain reaction (HCR) and the selective coordination of Cu<sup>2+</sup>. The aptamer was used as a specific recognition probe for binding with the target protein, thereby regulating the tandem HCR reactions and achieving signal amplification. The coordination between Cu<sup>2+</sup> and double-stranded DNA (dsDNA) can further regulate the fluorescence intensity of the signal molecule, calcein, for precise quantification of TDEs. The results showed that the detection limit (LOD) for TDEs can be as low as 100 particles/mL. Moreover, the proposed strategy can accurately differentiate between lung cancer patients (n = 25) and healthy donors (n = 12), with a sensitivity of 92%, specificity of 100%, and an area under the receiver operating characteristic curve (AUC) of 0.963. The fluorescence analysis also demonstrated a strong correlation with the computed tomography scans and pathological analysis. Therefore, the proposed method in this study can provide more insights into assisting in the early diagnosis of lung cancer through TDEs-based liquid biopsy.", "Keywords": "", "DOI": "10.1016/j.snb.2024.135722", "PubYear": 2024, "Volume": "410", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Laboratory Medicine, Med+X Center for Manufacturing, National Clinical Research Center for Geriatrics, West China Hospital, Sichuan University, Chengdu, Sichuan 610041, China"}, {"AuthorId": 2, "Name": "<PERSON>g<PERSON><PERSON> Shen", "Affiliation": "Department of Laboratory Medicine, Med+X Center for Manufacturing, National Clinical Research Center for Geriatrics, West China Hospital, Sichuan University, Chengdu, Sichuan 610041, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Laboratory Medicine, Med+X Center for Manufacturing, National Clinical Research Center for Geriatrics, West China Hospital, Sichuan University, Chengdu, Sichuan 610041, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Laboratory Medicine, Med+X Center for Manufacturing, National Clinical Research Center for Geriatrics, West China Hospital, Sichuan University, Chengdu, Sichuan 610041, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Center for Archaeological Science, Sichuan University, Chengdu, Sichuan 610064, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Chemistry and Material Science, Sichuan Normal University, Chengdu, Sichuan 610068, China;Corresponding authors"}, {"AuthorId": 7, "Name": "Piaopia<PERSON> Chen", "Affiliation": "Department of Laboratory Medicine, Med+X Center for Manufacturing, National Clinical Research Center for Geriatrics, West China Hospital, Sichuan University, Chengdu, Sichuan 610041, China;Corresponding authors"}], "References": []}, {"ArticleId": 114309717, "Title": "Knowledge distillation-based information sharing for online process monitoring in decentralized manufacturing system", "Abstract": "<p>In advanced manufacturing, the incorporation of sensing technology provides an opportunity to achieve efficient in situ process monitoring using machine learning methods. Meanwhile, the advances of information technologies also enable a connected and decentralized environment for manufacturing systems, making different manufacturing units in the system collaborate more closely. In a decentralized manufacturing system, the involved units may fabricate same or similar products and deploy their own machine learning model for online process monitoring. However, due to the possible inconsistency of task progress during the operation, it is also common that some units have more informative data while some have less informative data. Thus, the monitoring performance of machine learning model for each unit may highly vary. Therefore, it is extremely valuable to achieve efficient and secured knowledge sharing among the units in a decentralized manufacturing system for enhancement of poorly performed models. To realize this goal, this paper proposes a novel knowledge distillation-based information sharing (KD-IS) framework, which could distill informative knowledge from well performed models to improve the monitoring performance of poorly performed models. To validate the effectiveness of this method, a real-world case study is conducted in a connected fused filament fabrication (FFF)-based additive manufacturing (AM) platform. The experimental results show that the developed method is very efficient in improving model monitoring performance at poorly performed models, with solid protection on potential data privacy.</p>", "Keywords": "Additive manufacturing; Decentralized manufacturing system; In situ process monitoring; Knowledge distillation; Machine learning", "DOI": "10.1007/s10845-024-02348-9", "PubYear": 2025, "Volume": "36", "Issue": "3", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial Engineering and Management, Oklahoma State University, Stillwater, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial Engineering and Management, Oklahoma State University, Stillwater, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Industrial Engineering and Management, Oklahoma State University, Stillwater, USA; Corresponding author."}], "References": [{"Title": "Fault detection based on one-class deep learning for manufacturing applications limited to an imbalanced database", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "357", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Knowledge Distillation: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "6", "Page": "1789", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Failure prediction in production line based on federated learning: an empirical study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "8", "Page": "2277", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Identifying manufacturing operational conditions by physics-based feature extraction and ensemble clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "162", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Continual learning of neural networks for quality prediction in production using memory aware synapses and weight transfer", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "1", "Page": "283", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Detecting cyberattacks using anomaly detection in industrial control systems: A Federated Learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "132", "Issue": "", "Page": "103509", "JournalTitle": "Computers in Industry"}, {"Title": "Using Autoencoders for Anomaly Detection and Transfer Learning in IoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "7", "Page": "88", "JournalTitle": "Computers"}, {"Title": "In-situ point cloud fusion for layer-wise monitoring of additive manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "210", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "From IoT-based cloud manufacturing approach to intelligent additive manufacturing: industrial Internet of Things—an overview", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "3-4", "Page": "1461", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Securing cyber-physical additive manufacturing systems by in-situ process authentication using streamline video analysis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "429", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "An LSTM-autoencoder based online side channel monitoring approach for cyber-physical attack detection in additive manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "4", "Page": "1815", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Application of sophisticated sensors to advance the monitoring of machining processes: analysis and holistic review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "3-4", "Page": "989", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Sensor Data Protection Through Integration of Blockchain and Camouflaged Encryption in Cyber-Physical Manufacturing Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "24", "Issue": "7", "Page": "1", "JournalTitle": "Journal of Computing and Information Science in Engineering"}]}, {"ArticleId": 114309803, "Title": "A Machine Learning Approach to Detect Lung Nodules Using Reinforcement Learning Based on Imbalanced Classification", "Abstract": "<p>Lung cancer is one of the fatal diseases affecting millions of people globally. Importantly, accuracy and rapid lung nodules detection on CT images are significant challenges. Doctors and radiologists' manual detection of lung nodules have low efficiency due to the variety of size, shape, and nodule’s location. Approaches based on machine learning play a fundamental and important role in the integration and analysis of these large and complex data sets. Considering the different viewpoints of learning methods and diverse lung cancer data, machine learning is an attractive and interesting method for researchers. This paper proposes a novel machine-learning approach to detect lung nodules using reinforcement learning based on imbalanced classification. Due to many healthy data, the ANN classifier tends towards this class rather than the suspicious one. To avoid this problem, we apply reinforcement learning to learn ANN parameters. The results obtained from our proposed model on the LIDC-IDRI data set showed better results than state of the arts on standard performance metrics such as IOU and HD. Also, according to accuracy metrics proposed method have the highest value of 94.91, compared to the previous best methods. Compared with advanced approaches, experimental results on the LIDC dataset indicated that the proposed model could detect various nodules and achieve the highest accuracy at the lowest time. Due to the nature of imbalanced data on most cancers, the proposed method can be adapted to other cancers.</p>", "Keywords": "Lung cancer; CT images; Machine learning approach; Reinforcement learning; Imbalanced classification", "DOI": "10.1007/s42979-024-02678-8", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management Systems, Amirkabir University of Technology (Tehran Polytechnic), Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering and Management Systems, Amirkabir University of Technology (Tehran Polytechnic), Tehran, Iran; Corresponding author."}], "References": [{"Title": "A framework for offline signature verification system: Best features selection approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "50", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Lungs cancer classification from CT images: An integrated design of contrast based classical features fusion and selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "77", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Deep reinforcement learning for imbalanced classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "8", "Page": "2488", "JournalTitle": "Applied Intelligence"}, {"Title": "Synthetic CT images for semi-sequential detection and segmentation of lung nodules", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1616", "JournalTitle": "Applied Intelligence"}, {"Title": "Automatic prognosis of lung cancer using heterogeneous deep learning models for nodule detection and eliciting its morphological features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>rk<PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "4", "Page": "2471", "JournalTitle": "Applied Intelligence"}, {"Title": "Lung cancer detection using enhanced segmentation accuracy", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "6", "Page": "3391", "JournalTitle": "Applied Intelligence"}, {"Title": "A deep learning system that generates quantitative CT reports for diagnosing pulmonary Tuberculosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "6", "Page": "4082", "JournalTitle": "Applied Intelligence"}, {"Title": "A comprehensive survey on feature selection in the various fields of machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "4", "Page": "4543", "JournalTitle": "Applied Intelligence"}, {"Title": "Segmentation and classification on chest radiography: a systematic survey", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "3", "Page": "875", "JournalTitle": "The Visual Computer"}, {"Title": "A bi-directional deep learning architecture for lung nodule semantic segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "11", "Page": "5245", "JournalTitle": "The Visual Computer"}, {"Title": "A GAN-based method for 3D lung tumor reconstruction boosted by a knowledge transfer approach", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "28", "Page": "44359", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 114309936, "Title": "Loose–tight cluster regularization for unsupervised person re-identification", "Abstract": "<p>Unsupervised person re-identification (Re-ID) is a critical and challenging task in computer vision. It aims to identify the same person across different camera views or locations without using any labeled data or annotations. Most existing unsupervised Re-ID methods adopt a clustering and fine-tuning strategy, which alternates between generating pseudo-labels through clustering and updating the model parameters through fine-tuning. However, this strategy has two major drawbacks: (1) the pseudo-labels obtained by clustering are often noisy and unreliable, which may degrade the model performance; and (2) the model may overfit to the pseudo-labels and lose its generalization ability during fine-tuning. To address these issues, we propose a novel method that integrates silhouette coefficient-based label correction and contrastive loss regularization based on loose–tight cluster guidance. Specifically, we use silhouette coefficients to measure the quality of pseudo-labels and correct the potential noisy labels, thereby reducing their negative impact on model training. Moreover, we introduce a new contrastive loss regularization term that consists of two components: a cluster-level contrast loss that encourages the model to learn discriminative features, and a regularization loss that prevents the model from overfitting to the pseudo-labels. The weights of these components are dynamically adjusted according to the silhouette coefficients. Furthermore, we adopt Vision Transformer as the backbone network to extract more robust features. We conduct extensive experiments on several public datasets and demonstrate that our method achieves significant improvements over the state-of-the-art unsupervised Re-ID methods.</p>", "Keywords": "Unsupervised person Re-ID; Silhouette coefficient; Loose–tight cluster; Regularization", "DOI": "10.1007/s00371-024-03329-y", "PubYear": 2025, "Volume": "41", "Issue": "1", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Fudan University, Shanghai, China"}, {"AuthorId": 4, "Name": "Pengju <PERSON>", "Affiliation": "Henan University of Science and Technology, Luoyang, China; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China; Lishui Institute of Hangzhou Dianzi University, Lishui, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China; Lishui Institute of Hangzhou Dianzi University, Lishui, China"}, {"AuthorId": 7, "Name": "Chenggang Yan", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China"}], "References": [{"Title": "AeS‐GCN: Attention‐enhanced semantic‐guided graph convolutional networks for skeleton‐based action recognition", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "3-4", "Page": "e2070", "JournalTitle": "Computer Animation and Virtual Worlds"}, {"Title": "Research on occlusion block face recognition based on feature point location", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "3-4", "Page": "e2094", "JournalTitle": "Computer Animation and Virtual Worlds"}, {"Title": "Hybrid feature constraint with clustering for unsupervised person re-identification", "Authors": "<PERSON><PERSON> Si; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "10", "Page": "5121", "JournalTitle": "The Visual Computer"}, {"Title": "Region feature smoothness assumption for weakly semi‐supervised crowd counting", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "3-4", "Page": "e2173", "JournalTitle": "Computer Animation and Virtual Worlds"}, {"Title": "Bidirectional temporal feature for 3D human pose and shape estimation from a video", "Authors": "Libo Sun; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "3-4", "Page": "e2187", "JournalTitle": "Computer Animation and Virtual Worlds"}]}, {"ArticleId": 114310062, "Title": "Research on Civil Aviation Meteorological Agency Reporting System Based on SMS Transparent Transmission Technology", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.143064", "PubYear": 2024, "Volume": "14", "Issue": "3", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "福康 陈", "Affiliation": ""}], "References": []}, {"ArticleId": 114310099, "Title": "Graph neural networks in vision-language image understanding: a survey", "Abstract": "2D image understanding is a complex problem within computer vision, but it holds the key to providing human-level scene comprehension. It goes further than identifying the objects in an image, and instead, it attempts to understand the scene. Solutions to this problem form the underpinning of a range of tasks, including image captioning, visual question answering (VQA), and image retrieval. Graphs provide a natural way to represent the relational arrangement between objects in an image, and thus, in recent years graph neural networks (GNNs) have become a standard component of many 2D image understanding pipelines, becoming a core architectural component, especially in the VQA group of tasks. In this survey, we review this rapidly evolving field and we provide a taxonomy of graph types used in 2D image understanding approaches, a comprehensive list of the GNN models used in this domain, and a roadmap of future potential developments. To the best of our knowledge, this is the first comprehensive survey that covers image captioning, visual question answering, and image retrieval techniques that focus on using GNNs as the main part of their architecture.", "Keywords": "Graph neural networks; Image captioning; Visual question answering; Image retrieval", "DOI": "10.1007/s00371-024-03343-0", "PubYear": 2025, "Volume": "41", "Issue": "1", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Digital Environment Research Institute, Queen Mary University London, London, UK; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Digital Environment Research Institute, Queen Mary University London, London, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Digital Environment Research Institute, Queen Mary University London, London, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, The Hong Kong Polytechnic University, Hung Hom, Hong Kong"}], "References": [{"Title": "Learning visual relationship and context-aware attention for image captioning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107075", "JournalTitle": "Pattern Recognition"}, {"Title": "Skeleton-based action recognition by part-aware graph convolutional networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "621", "JournalTitle": "The Visual Computer"}, {"Title": "Multi-level colored directional motif histograms for content-based image retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "9", "Page": "1847", "JournalTitle": "The Visual Computer"}, {"Title": "Multiple answers to a question: a new approach for visual question answering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "119", "JournalTitle": "The Visual Computer"}, {"Title": "Dual-CNN: A Convolutional language decoder for paragraph image captioning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "92", "JournalTitle": "Neurocomputing"}, {"Title": "What we see in a photograph: content selection for image captioning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "6", "Page": "1309", "JournalTitle": "The Visual Computer"}, {"Title": "Cross-modal knowledge reasoning for knowledge-based visual question answering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107563", "JournalTitle": "Pattern Recognition"}, {"Title": "Visual question answering model based on graph neural network and contextual attention", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "104165", "JournalTitle": "Image and Vision Computing"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "Knowledge Graphs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Residual connection-based graph convolutional neural networks for gait recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> S<PERSON> <PERSON><PERSON>; Marina L. <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "9-11", "Page": "2713", "JournalTitle": "The Visual Computer"}, {"Title": "Fact-based visual question answering via dual-process system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "237", "Issue": "", "Page": "107650", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "RETRACTED ARTICLE: Video captioning: a review of theory, techniques and practices", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "25", "Page": "35619", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Advanced graph and sequence neural networks for molecular property prediction and drug discovery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "9", "Page": "2579", "JournalTitle": "Bioinformatics"}, {"Title": "Multimodal high-order relational network for vision-and-language tasks", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "492", "Issue": "", "Page": "62", "JournalTitle": "Neurocomputing"}, {"Title": "3D Shape Analysis Through a Quantum Lens: the Average Mixing Kernel Signature", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "6", "Page": "1474", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Hypergraph attentional convolutional neural network for salient object detection", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "7", "Page": "2881", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 114310135, "Title": "A group incremental feature selection based on knowledge granularity under the context of clustering", "Abstract": "<p>As a widely used data preprocessing method, feature selection with rough sets aims to delete redundant conditional features. However, most of the traditional feature selection methods target to the static data set environment, and the importance of features is used as the base for feature selection. These methods only consider the importance of features themselves and do not consider the impact of features on classification. In order to overcome such shortcomings, we first use the information of knowledge granules to calculate the similarity of samples in the same cluster and samples in different clusters; Secondly, from the perspective of clustering, we stick to the principle that the samples in the same cluster are as close as possible, and the samples in different clusters are as far away as possible, then a feature selection model of knowledge granularity (in short SKG) based on the clustering background is designed; Thirdly, in order to make the SKG model adapt to the reduction of dynamic data sets, we discuss the incremental learning mechanism of sample and feature changes, and two incremental models SKGOA and SKGAA are designed to deal with the dynamic feature reduction when some samples and features are added into the decision system. Finally, some numerical experiments are conducted to assess the performance of the proposed algorithms, and the results shown that our approaches are of a prominent advantage in terms of computational time and classification accuracy.</p>", "Keywords": "Feature selection; Context of clustering; Knowledge granularity; Incremental learning", "DOI": "10.1007/s13042-024-02113-7", "PubYear": 2024, "Volume": "15", "Issue": "9", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Multi-Source Information Mining and Security, Guangxi Normal University, Guilin, China; College of Computer Science and Engineering, Guangxi Normal University, Guilin, China; Institute of Computer and Artificial Intelligence, Chaohu University, Hefei, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Computer and Artificial Intelligence, Chaohu University, Hefei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computer and Artificial Intelligence, Chaohu University, Hefei, China"}, {"AuthorId": 4, "Name": "Houjiang He", "Affiliation": "School of Foreign Languages, Chaohu University, Hefei, China"}], "References": [{"Title": "Similarity-based attribute reduction in rough set theory: a clustering perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "1047", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Incremental mechanism of attribute reduction based on discernible relations for dynamically increasing attribute", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "1", "Page": "321", "JournalTitle": "Soft Computing"}, {"Title": "InOvIn: A fuzzy-rough approach for detecting overlapping communities with intrinsic structures in evolving networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106096", "JournalTitle": "Applied Soft Computing"}, {"Title": "Incremental attribute reduction with rough set for dynamic datasets with simultaneously increasing samples and attributes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "1339", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Knowledge granularity based incremental attribute reduction for incomplete decision systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "1141", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Incremental feature selection based on fuzzy rough sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "536", "Issue": "", "Page": "185", "JournalTitle": "Information Sciences"}, {"Title": "Knowledge granularity reduction for decision tables", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "3", "Page": "569", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Attribute reduction methods in fuzzy rough set theory: An overview, comparative experiments, and new directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107353", "JournalTitle": "Applied Soft Computing"}, {"Title": "Incremental neighborhood entropy-based feature selection for mixed-type data under the variation of feature set", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "5", "Page": "4792", "JournalTitle": "Applied Intelligence"}, {"Title": "Dynamic interaction feature selection based on fuzzy rough set", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "581", "Issue": "", "Page": "891", "JournalTitle": "Information Sciences"}, {"Title": "Matrix representation of the conditional entropy for incremental feature selection on multi-source data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>;  <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "591", "Issue": "", "Page": "263", "JournalTitle": "Information Sciences"}, {"Title": "Incremental feature selection by sample selection and feature-based accelerator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108800", "JournalTitle": "Applied Soft Computing"}, {"Title": "Normalized Mutual Information-based equilibrium optimizer with chaotic maps for wrapper-filter feature selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "118107", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Incremental feature selection approach to interval-valued fuzzy decision information systems based on λ -fuzzy similarity self-information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "625", "Issue": "", "Page": "593", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 114310186, "Title": "Fully discrete approximations and an a priori error analysis of a two-temperature thermo-elastic model with microtemperatures", "Abstract": "", "Keywords": "", "DOI": "10.61822/amcs-2024-0007", "PubYear": 2024, "Volume": "34", "Issue": "1", "JournalId": 7288, "JournalTitle": "International Journal of Applied Mathematics and Computer Science", "ISSN": "", "EISSN": "2083-8492", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114310188, "Title": "Degradation tolerant optimal control design for stochastic linear systems", "Abstract": "", "Keywords": "", "DOI": "10.61822/amcs-2024-0001", "PubYear": 2024, "Volume": "34", "Issue": "1", "JournalId": 7288, "JournalTitle": "International Journal of Applied Mathematics and Computer Science", "ISSN": "", "EISSN": "2083-8492", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114310197, "Title": "Navi: Data Analysis System Powered by Natural Language Interaction", "Abstract": "", "Keywords": "", "DOI": "10.21655/ijsi.1673-7288.00321", "PubYear": 2024, "Volume": "14", "Issue": "1", "JournalId": 87794, "JournalTitle": "International Journal of Software and Informatics", "ISSN": "1673-7288", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Technology and Applications, Qinghai University, Xining 810016, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Ji<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114310273, "Title": "Використання методу багатовимірної середньої для оптимізації формування рейтингу науково-педагогічних працівників вузу на прикладі волинського національного університету імені Лесі Українки", "Abstract": "<p>В роботі проаналізовано наявні інструменти для формування рейтингу, створено власний програмний продукт для імпортування даних викладачів із селекцією необхідних параметрів з існуючих баз даних. У самій програмі передбачено кілька алгоритмів для обрахунку за методом багатовимірної середньої рейтингу викладачів, сортування даних в порядку спадання (зростання) та візуалізацію результатів</p>", "Keywords": "", "DOI": "10.36910/6775-2524-0560-2024-54-26", "PubYear": 2024, "Volume": "", "Issue": "54", "JournalId": 71325, "JournalTitle": "КОМП’ЮТЕРНО-ІНТЕГРОВАНІ ТЕХНОЛОГІЇ: ОСВІТА, НАУКА, ВИРОБНИЦТВО", "ISSN": "2524-0552", "EISSN": "2524-0560", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "MPC-Based Routing and Tracking Architecture for Safe Autonomous Driving in Urban Traffic", "Abstract": "<p>This paper presents a configurable routing and tracking architecture that uses multi-objective Model Predictive Control (MPC) as its driving algorithm to guarantee safe autonomous driving of different vehicle types. The architecture consists of three main components and primarily relies on labeled map data to generate optimal path and velocity trajectories in accordance with the vehicle type and the desired control objectives. We begin with introducing the overall system architecture and its different inputs, outputs, and components. We also briefly explain the open-source services utilized in this work for trajectory generation, namely OpenStreetMap and GraphHopper. We then focus on formulating the multi-objective MPC problem and its vehicle-specific constraints, which is solved offline to generate the reference path and velocity trajectories. Afterwards, we discuss some adaptions to the system model and the controller operating strategy to incorporate real-time tracking of these trajectories while guaranteeing collision avoidance. Finally, we successfully demonstrate the system’s feasibility by numerically evaluating its performance in a typical urban driving scenario for different vehicles.</p>", "Keywords": "Autonomous vehicles; Route planning; Trajectory following; Multi-objective MPC; GraphHopper; OpenStreetMap", "DOI": "10.1007/s42979-024-02732-5", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering, Institute of Applied Mathematics and Scientific Computing, University of the Bundeswehr Munich, Munich, Germany; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Aerospace Engineering, Institute of Applied Mathematics and Scientific Computing, University of the Bundeswehr Munich, Munich, Germany"}], "References": [{"Title": "Model predictive control for autonomous ground vehicles: a review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "1", "JournalTitle": "Autonomous Intelligent Systems"}]}, {"ArticleId": 114310388, "Title": "Enhancing sustainable supply chain readiness to adopt blockchain: A decision support approach for barriers analysis", "Abstract": "Blockchain technology (BT) enhances the capacity to monitor products consistently, fostering supply chain responsiveness to a wide range of societal and environmental issues. Although BT is known as an innovative tool, there exist potential operational and organizational challenges affecting BT adoption. This study proposes a decision support approach to leverage risk management to analyze potential barriers associated with BT adoption in sustainable supply chains (SSCs). This approach is developed to model how the economic, social, and environmental-related barriers (e.g., energy consumption) and their corresponding risk factors are interrelated. To model the causal relationships (CRs) among the barriers identified through the literature review, the fuzzy cognitive map advanced by Z-number theory is embedded in the proposed approach. Then, a hybrid learning algorithm is employed to determine the criticality of the barriers. As the reliability of information affects the accuracy of decision-making, the Z-number theory applies uncertainty and reliability simultaneously in specifying the values of risk factors and the weights of the CRs. Taking advantage of the learning algorithm and Z-number theory, the findings show a reliable and unbiased ranking compared to the failure mode and effect analysis. This helps managers develop more efficient mitigation strategies to deal with critical barriers. The results of the study also imply that adoption costs, extra audits, and regulatory uncertainty are the critical barriers affecting SSC readiness.", "Keywords": "Blockchain technology; Decision support approach; Sustainable supply chain; Barriers and risk analysis; Z-number theory", "DOI": "10.1016/j.engappai.2024.108151", "PubYear": 2024, "Volume": "133", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Engineering, University of British Columbia, Okanagan Campus, Kelowna, BC, V1V 1V7, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>pour Tosarkani", "Affiliation": "School of Engineering, University of British Columbia, Okanagan Campus, Kelowna, BC, V1V 1V7, Canada;Corresponding author"}], "References": [{"Title": "Barriers to implementation of blockchain into supply chain management using an integrated multi-criteria decision-making method: a numerical example", "Authors": "<PERSON><PERSON><PERSON>ü<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "19", "Page": "14771", "JournalTitle": "Soft Computing"}, {"Title": "Concept design evaluation by using Z-axiomatic design", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "122", "Issue": "", "Page": "103278", "JournalTitle": "Computers in Industry"}, {"Title": "Risk assessment in discrete production processes considering uncertainty and reliability: Z-number multi-stage fuzzy cognitive map with fuzzy learning algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "2", "Page": "1349", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Fuzzy cognitive maps in systems risk analysis: a comprehensive review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "2", "Page": "621", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A multi-criteria decision making method based on DNMA and CRITIC with linguistic D numbers for blockchain platform evaluation", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104200", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "CaFtR: A Fuzzy Complex Event Processing Method", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "2", "Page": "1098", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "A multicriteria decision making methodology based on two-dimensional uncertainty by hesitant Z-fuzzy linguistic terms with an application for blockchain risk evaluation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "108014", "JournalTitle": "Applied Soft Computing"}, {"Title": "Sustainability risk assessment of blockchain adoption in sustainable supply chain: An integrated method", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "171", "Issue": "", "Page": "108378", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "The adoption of new technologies for sustainable risk management in logistics planning: A sequential dynamic approach", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "173", "Issue": "", "Page": "108627", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Research on significant factors affecting adoption of blockchain technology for enterprise distributed applications based on integrated MCDM FCEM-MULTIMOORA-FG method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "118", "Issue": "", "Page": "105699", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "The appropriation of blockchain implementation in the supply chain of SMES based on fuzzy LMAW", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106169", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A decision-making framework for blockchain platform evaluation in spherical fuzzy environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "120833", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Evaluating the blockchain-based healthcare supply chain using interval-valued Pythagorean fuzzy entropy-based decision support system", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107112", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 114310720, "Title": "ChatGPT-powered Inquiry-based Learning Model of Training for Intelligent Car Racing Competition", "Abstract": "In this study, we explore the application of an inquiry-based learning model powered by ChatGPT in the context of intelligent car racing competition training. We address four key aspects: (1) the construction of a knowledge and skill acquisition process through student interactions with ChatGPT to facilitate the progressive development of problem-solving strategies and approaches; (2) project-based learning for interdisciplinary students participating in the competition, where students are grouped in accordance with their backgrounds and engage in tasks such as vehicle design and optimization, electrical drive and control algorithm adaptation, and sensor circuit design and calibration; (3) the paradigm shift in the role of teachers, transitioning from knowledge providers to co-coaches alongside ChatGPT, allowing teachers to allocate more time to monitor the progress of different student groups and design learning objectives; and (4) knowledge building and prompt engineering during different stages of the training process, where students employ various questions and prompts to interact with ChatGPT, thereby constructing domain-specific knowledge and improving the quality and effectiveness of knowledge acquisition. By leveraging ChatGPT as a conversational agent, students engage in a dynamic learning process that fosters their understanding of research problems and nurtures their problem-solving skills. Integrating an inquiry-based approach, project-based learning, and teacher-student collaboration with ChatGPT empowers students to acquire essential knowledge and cultivate critical thinking abilities, contributing to their overall growth and readiness for intelligent car racing competitions. The findings of this study shed light on the efficacy of ChatGPT-powered inquiry-based learning models in preparing students for complex and interdisciplinary challenges in the field of intelligent car racing. © 2024 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "ChatGPT; inquiry-based learning; intelligent car racing competition; project-based learning; prompt engineering", "DOI": "10.18494/SAM4726", "PubYear": 2024, "Volume": "36", "Issue": "3", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Railway, Shandong Polytechnic, No. 23000, Jingshidong Road, Shandong Province, Jinan, 250104, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Education, Huanggang Normal University, No. 146, Xingang Second Road, Development Zone, Hubei Province, Huanggang, 438000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Urban Rail, Shandong Polytechnic, No. 23000, Jingshidong Road, Shandong Province, Jinan, 250104, China"}], "References": []}, {"ArticleId": 114310817, "Title": "Design and analysis of quantum machine learning: a survey", "Abstract": "", "Keywords": "", "DOI": "10.1080/09540091.2024.2312121", "PubYear": 2024, "Volume": "36", "Issue": "1", "JournalId": 9414, "JournalTitle": "Connection Science", "ISSN": "0954-0091", "EISSN": "1360-0494", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan, People’s Republic of China;College of Computer Science and Electronic Engineering, Hunan University, Changsha, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, University of Technology, Xiamen, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Applied Mathematics, Silesian University of Technology, Gliwice, Poland"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan, People’s Republic of China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Hunan University of Science and Technology, Xiangtan, People’s Republic of China"}], "References": [{"Title": "Integrated Content and Network-Based Service Clustering and Web APIs Recommendation for Mashup Development", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "99", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Support vector machines on the D-Wave quantum annealer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "248", "Issue": "", "Page": "107006", "JournalTitle": "Computer Physics Communications"}, {"Title": "Quantum-inspired ant lion optimized hybrid k-means for cluster analysis and intrusion detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106167", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Role of machine learning in medical research: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100370", "JournalTitle": "Computer Science Review"}, {"Title": "Machine learning in the quantum realm: The state-of-the-art, challenges, and future vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "194", "Issue": "", "Page": "116512", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Quantum K-means clustering method for detecting heart disease using quantum circuit approach", "Authors": "<PERSON> <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "18", "Page": "13255", "JournalTitle": "Soft Computing"}, {"Title": "A reputation mechanism based Deep Reinforcement Learning and blockchain to suppress selfish node attack motivation in Vehicular Ad-Hoc Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "17", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Challenges and opportunities in quantum machine learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "9", "Page": "567", "JournalTitle": "Nature Computational Science"}, {"Title": "Quantum machine learning in medical image analysis: A survey", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "525", "Issue": "", "Page": "42", "JournalTitle": "Neurocomputing"}, {"Title": "Quantum Machine Learning: Scope for real-world problems", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "218", "Issue": "", "Page": "2612", "JournalTitle": "Procedia Computer Science"}, {"Title": "Algebraic Structure Based Clustering Method from Granular Computing Prospective", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "1", "Page": "121", "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems"}, {"Title": "A novel authentication and key agreement scheme for Internet of Vehicles", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "145", "Issue": "", "Page": "415", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Subtleties in the trainability of quantum machine learning models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Quantum Machine Intelligence"}, {"Title": "MMDS: A secure and verifiable multimedia data search scheme for cloud-assisted edge computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "151", "Issue": "", "Page": "32", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "LightPay: A Lightweight and Secure Off-Chain Multi-Path Payment Scheme Based on Adapter Signatures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "4", "Page": "1622", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 114310914, "Title": "Development of Estimation Techniques for Solar Radiation, NDVI and Net Primary Productivity", "Abstract": "<p>Net Primary Productivity (NPP) is a fundamental ecological metric that underpins the functioning of ecosystems. NPP estimation using the Carnegie–Ames–Stanford Approach (CASA) biosphere model is carried out for the Roorkee and Hyderabad study sites. The CASA model estimates the NPP at regional and global scales by using data of environmental factors like temperature, precipitation, total solar radiation, and the normalized difference vegetation index. For the study sites, the NPP is estimated using the CASA model, with high agreement between modeled and observed values. The findings show the usefulness of the CASA model for calculating the NPP. For the Roorkee site, the NPP estimates are significantly higher than those for the Hyderabad site. To determine the solar radiation for the Roorkee and Hyderabad stations, this study examines the monthly average values of cloud cover fraction, the duration of sunny hours and global solar radiation on a horizontal surface. It is found out that the relationship between cloud cover fraction and the maximum number of hours that can be spent in the sun is nonlinear. This results in a nonlinear relationship between cloud cover fraction and the solar radiation. The moderate resolution imaging spectroradiometer (MODIS) composite and the Landsat imagery are combined to generate the synthetic normalized difference vegetation index (NDVI) images which are further used as remote sensing data for the estimation of NPP. This study employs a hybrid methodology that combines smoothing, filtering, and regression analysis techniques. The combined mode filter combines the forward and backward Ka<PERSON> filters.</p>", "Keywords": "Solar radiation; NDVI; NPP; CASA; Kalman Filter; Temperature; MODIS", "DOI": "10.1007/s42979-024-02720-9", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ECE Department, Indian Institute of Technology Roorkee, Roorkee, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ECE Department, Indian Institute of Technology Roorkee, Roorkee, India; Corresponding author."}], "References": [{"Title": "<PERSON><PERSON> filter method for generating time-series synthetic Landsat images and their uncertainty from Landsat and MODIS observations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "239", "Issue": "", "Page": "111628", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 114310940, "Title": "The Application Progress of Common Machine Learning Techniques in Traditional Chinese Medicine Syndrome Diagnosis", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.143067", "PubYear": 2024, "Volume": "14", "Issue": "3", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [], "References": []}, {"ArticleId": 114310954, "Title": "Infrared and Visible Image Fusion Based on Res2Net-Transformer Automatic Encoding and Decoding", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.048136", "PubYear": 2024, "Volume": "79", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Image fusion in the loop of high-level vision tasks: A semantic-aware real-time infrared and visible image fusion network", "Authors": "Linfeng Tang; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "82", "Issue": "", "Page": "28", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 114311075, "Title": "Multi-Class Recognition of Soybean Leaf Diseases using a Conv-LSTM Model", "Abstract": "This research presents an innovative approach for multi-class recognition of soybean leaf diseases using a Convolutional Long Short-Term Memory (Conv-LSTM) model. The model integrates the spatial learning capabilities of convolutional layers with the temporal dependencies of LSTM units, addressing the critical need for accurate disease detection in agriculture, particularly in soybean cultivation where leaf diseases significantly impact crop yield and quality. Through comparative experiments with established deep learning models such as AlexNet, VGG16, and ResNet50, the Conv-LSTM model demonstrates superior performance in terms of accuracy, precision, recall, and F1 score. By effectively capturing both spatial and temporal features in soybean leaf images, the Conv-LSTM model showcases its potential to enhance disease detection accuracy, supporting precision agriculture practices and enabling timely interventions to mitigate crop losses caused by diseases.", "Keywords": "Soybean Leaf Disease;CNN-LSTM;AlexNet;VggNet;ResNet", "DOI": "10.32628/CSEIT2410217", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Scholar, Department of Computer Engineering, Sigma Institute of Engineering, Gujarat, India"}, {"AuthorId": 2, "Name": "Dr. <PERSON><PERSON><PERSON>", "Affiliation": "Professor & Head of Department, Department of Computer Engineering, Sigma University, Gujarat, India"}], "References": []}, {"ArticleId": *********, "Title": "Getting global cooperation right on internet governance: strategic roadmaps for the future", "Abstract": "", "Keywords": "", "DOI": "10.1080/13600869.2024.2330035", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 25543, "JournalTitle": "International Review of Law, Computers & Technology", "ISSN": "1360-0869", "EISSN": "1364-6885", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Clinical Professor of Business Law and Tax, Florida International University, Miami, FL, USA"}], "References": []}, {"ArticleId": *********, "Title": "Automatic Liver Segmentation from Multiphase CT Using Modified SegNet and ASPP Module", "Abstract": "<p>Liver cancer is one of the dominant causes of cancer death worldwide. Computed Tomography (CT) is the commonly used imaging modality for diagnosing it. Computer-based liver cancer diagnosis systems can assist radiologists in image interpretation and improve diagnosis speed and accuracy. Since liver segmentation is crucial to such systems, researchers are relentlessly pursuing various segmentation approaches. A clinically viable computer-aided system requires examining multiphase CT images. However, most of the research focuses only on the portal venous phase. In this work, we developed an automatic and efficient Deep Learning (DL) method using SegNet, atrous spatial pyramid pooling module and leaky ReLU layers for liver segmentation from quadriphasic abdominal CT volumes. The proposed method was validated on two datasets, an internal institutional dataset consisting of multiphase CT and a public dataset of portal venous phase CT volumes. The Dice Coefficients (DC) obtained were greater than 96% for the latter dataset and the portal venous phase of the former. For arterial, delayed and plain CT phases of the former dataset, the DC achieved were 94.61%, 95.01% and 93.23%, respectively. Experiments showed that our model performed better than the other state-of-the-art DL models. Ablation studies have revealed that the proposed model leverages the strengths of all the three components that make it up. The promising performance of the proposed method suggests that it is appropriate for incorporation in hepatic cancer diagnosis systems.</p>", "Keywords": "Liver segmentation; SegNet; Computed tomography; Liver cancer; Computer-aided diagnosis; Atrous spatial pyramid pooling", "DOI": "10.1007/s42979-024-02719-2", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Instrumentation and Control Engineering, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Instrumentation and Control Engineering, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal, India; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Radiodiagnosis and Imaging, Kasturba Medical College, Manipal Academy of Higher Education, Manipal, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal, India"}], "References": [{"Title": "Automatic liver segmentation from abdominal CT volumes using improved convolution neural networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "1", "Page": "111", "JournalTitle": "Multimedia Systems"}, {"Title": "Dynamic adaptive residual network for liver CT image segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "91", "Issue": "", "Page": "107024", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Adaptable volumetric liver segmentation model for CT images using region-based features and convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "505", "Issue": "", "Page": "388", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 114311187, "Title": "The existence of mild solutions and approximate controllability for nonlinear fractional neutral evolution systems", "Abstract": "", "Keywords": "", "DOI": "10.61822/amcs-2024-0002", "PubYear": 2024, "Volume": "34", "Issue": "1", "JournalId": 7288, "JournalTitle": "International Journal of Applied Mathematics and Computer Science", "ISSN": "", "EISSN": "2083-8492", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Touria Karite", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114311192, "Title": "Dual-Task Learning Residual Channel Attention Mechanism for Handwritten Signature Verification Based on Bidirectional LSTM", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.143066", "PubYear": 2024, "Volume": "14", "Issue": "3", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "方军 栾", "Affiliation": ""}], "References": [{"Title": "OSVFuseNet: Online Signature Verification by feature fusion and depth-wise separable convolution based deep learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "409", "Issue": "", "Page": "157", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 114311213, "Title": "Цифрова стійкість: оцінка ролі інформаційних технологій у забезпеченні безперервності підготовки фахівців в кризових умовах", "Abstract": "<p>В дослідженні проаналізовано вплив цифрової стійкості на якість і ефективність підготовки фахівців. Виокремлено конкретні технологічні проблеми, з якими стикаються здобувачі вищої освіти під час кризових умов, запропоновано ефективні рішення, враховуючи доступність та технологічну підтримку для різноманітних потреб у навчанні. Встановлено, що успішні стратегії цифрової стійкості передбачають активне впровадження адаптивних технологій, планування на випадок непередбачених ситуацій і розробку гнучких моделей навчання для реагування на несподівані виклики. Вивчено досвід студентів щодо ролі інформаційних технологій у дистанційному навчанні в умовах кризи, враховуючи їхні особистісні цілі, уподобання та очікування. Проведено порівняльний аналіз ефективності різних інформаційних технологій, які використовуються для підготовки спеціалістів в кризових умовах, оцінивши зручність їхнього використання та адаптованість до різноманітних навчальних середовищ. Вивчено стратегії, які використовують навчальні заклади для створення стійкості до цифрових технологій, наголошуючи на важливості інституційної політики, професійного розвитку викладачів та інвестицій в інфраструктуру.</p>", "Keywords": "war in Ukraine; learning technologies; distance education; blended learning; educational resources; student-centered education", "DOI": "10.36910/6775-2524-0560-2024-54-16", "PubYear": 2024, "Volume": "", "Issue": "54", "JournalId": 71325, "JournalTitle": "КОМП’ЮТЕРНО-ІНТЕГРОВАНІ ТЕХНОЛОГІЇ: ОСВІТА, НАУКА, ВИРОБНИЦТВО", "ISSN": "2524-0552", "EISSN": "2524-0560", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114311264, "Title": "Оценка рисков информационной безопасности в отраслевой информационной системе на основе теории нечетких множеств и искусственной нейронной сети", "Abstract": "<p>Information security risk assessment is a crucial component of industrial management techniques that aids in identifying, quantifying, and evaluating risks in comparison to criteria for risk acceptance and organizationally pertinent objectives. Due to its capacity to combine several parameters to determine an overall risk, the traditional fuzzy-rule-based risk assessment technique has been used in numerous industries. The technique has a drawback because it is used in situations where there are several parameters that need to be evaluated, and each parameter is expressed by a different set of linguistic phrases. In this paper, fuzzy set theory and an artificial neural network (ANN) risk prediction model that can solve the issue at hand are provided. Also developed is an algorithm that may change the risk-related factors and the overall risk level from a fuzzy property to a crisp-valued attribute is developed. The system was trained by using twelve samples representing 70%, 15%, and 15% of the dataset for training, testing, and validation, respectively. In addition, a stepwise regression model has also been designed, and its results are compared with the results of ANN. In terms of overall efficiency, the ANN model (R2= 0.99981, RMSE=0.00288, and MSE=0.00001,) performed better, though both models are satisfactory enough. It is concluded that a risk-predicting ANN model can produce accurate results as long as the training data accounts for all conceivable conditions.</p>", "Keywords": "artificial neural network; cement industry; fuzzy set theory; industry information system; risk; risk assessment", "DOI": "10.15622/ia.23.2.9", "PubYear": 2024, "Volume": "23", "Issue": "2", "JournalId": 82239, "JournalTitle": "Информатика и автоматизация", "ISSN": "2713-3192", "EISSN": "2713-3206", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of information technology security (FBI), ITMO University, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology, Allahabad, India"}], "References": [{"Title": "Machine Learning: Algorithms, Real-World Applications and Research Directions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Deep Learning: A Comprehensive Overview on Techniques, Taxonomy, Applications and Research Directions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "6", "Page": "420", "JournalTitle": "SN Computer Science"}, {"Title": "Information security risk assessments following cybersecurity breaches: The mediating role of top management attention to cybersecurity", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "", "Page": "102974", "JournalTitle": "Computers & Security"}, {"Title": "Analysis of Cyber Security Attacks and Its Solutions for the Smart Grid Using Machine Learning and Blockchain Methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "2", "Page": "83", "JournalTitle": "Future Internet"}, {"Title": "Employee Productivity Assessment Using Fuzzy Inference System", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "7", "Page": "423", "JournalTitle": "Information"}]}, {"ArticleId": 114311275, "Title": "Corrigendum: Q<PERSON>si–Monte Carlo Finite Element Analysis for Wave Propagation in Heterogeneous Random Media", "Abstract": "", "Keywords": "quasi–<PERSON> Carlo method; finite element method; wave propagation; 35J05; 35R60; 65D30; 65D32; 65N30", "DOI": "10.1137/23M1624609", "PubYear": 2024, "Volume": "12", "Issue": "1", "JournalId": 11089, "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification", "ISSN": "", "EISSN": "2166-2525", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics & Statistics, Colorado School of Mines, Golden, CO 80401 USA."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Statistics, University of New South Wales, Sydney NSW 2052 Australia."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Statistics, University of New South Wales, Sydney NSW 2052 Australia."}], "References": [{"Title": "Quasi-Monte Carlo Finite Element Analysis for Wave Propagation in Heterogeneous Random Media", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "106", "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification"}]}, {"ArticleId": *********, "Title": "A Consolidated MCDM Framework for Overall Performance Assessment of Listed Insurance Companies Based on Ranking Strategies", "Abstract": "The main objective of this study is to analyze the performance of non-life insurance companies operating in the Turkish insurance industry with a hybrid model including Pythagorean Fuzzy Analytic Hierarchy Process (PFAHP) and Multi-Attributive Ideal-Real Comparative Analysis (MAIRCA) methods. For this purpose, the performance assessment indicators, consisting of fourteen sub-criteria in three dimensions are taken into account for comparing five insurance companies traded on the Borsa Istanbul (BIST) over five consecutive years (2015 to 2019). Subsequently, year-wise rankings are aggregated using the Borda count (BC) procedure. The results of PFAHP indicate that service network is the most important main criterion (dimension) for performance assessment of non-life insurance companies, followed by stock market performance and financial ratios that come in the second and third ranks, respectively. Furthermore, the results of MAIRCA based on BC procedure reveal that Halk Sigorta, a state-owned insurance company, is the most successful company in terms of selected performance indicators in the period examined. A comprehensive sensitivity analysis is performed in order to test stability and the robustness of the results from the proposed framework, and the results of sensitivity analysis confirms the rationality and robustness of the suggested integrated MCDM framework. As a result, the suggested assessment framework can be applied by different decision-making groups in the industry as a valuable and practical decision-making tool for monitoring and improving the performance of insurance companies. Finally, some of managerial implications are also discussed.", "Keywords": "Turkish insurance sector; Non-life insurance companies; Performance analysis; Pythagorean fuzzy AHP; MAIRCA; Borda count", "DOI": "10.1007/s10614-024-10578-5", "PubYear": 2025, "Volume": "65", "Issue": "1", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "Özcan Işık", "Affiliation": "Department of Finance and Banking, Sivas Cumhuriyet University, Sivas, Turkey; Corresponding author."}, {"AuthorId": 2, "Name": "Ahmet Çalık", "Affiliation": "Department of Business Administration, Faculty of Economics and Administrative Sciences, Balıkesir University, Bigadiç, Balıkesir, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics, Shandong University of Finance and Economics, Jinan, China"}], "References": [{"Title": "Hospital service quality evaluation: an integrated model based on Pythagorean fuzzy AHP and fuzzy TOPSIS", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "5", "Page": "3237", "JournalTitle": "Soft Computing"}, {"Title": "A novel spherical fuzzy analytic hierarchy process and its renewable energy application", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "6", "Page": "4607", "JournalTitle": "Soft Computing"}, {"Title": "Multiattribute group decision making based on neutrality aggregation operators of q-rung orthopair fuzzy sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "427", "JournalTitle": "Information Sciences"}, {"Title": "A novel Pythagorean fuzzy AHP and fuzzy TOPSIS methodology for green supplier selection in the Industry 4.0 era", "Authors": "Ahmet Çalık", "PubYear": 2021, "Volume": "25", "Issue": "3", "Page": "2253", "JournalTitle": "Soft Computing"}, {"Title": "Bibliometric analysis on Pythagorean fuzzy sets during 2013–2020", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "2", "Page": "104", "JournalTitle": "International Journal of Intelligent Computing and Cybernetics"}, {"Title": "A Pythagorean fuzzy number-based integration of AHP and WASPAS methods for refugee camp location selection problem: a real case study for Istanbul, Turkey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "22", "Page": "15751", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An extended MAIRCA method using intuitionistic fuzzy sets for coronavirus vaccine selection in the age of COVID-19", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "7", "Page": "5603", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Pythagorean Fuzzy Based AHP-VIKOR Integration to Assess Rail Transportation Systems in Turkey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "2", "Page": "620", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "A combination of DEA and AIMSUN to manage big data when evaluating the performance of bus lines", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "618", "Issue": "", "Page": "72", "JournalTitle": "Information Sciences"}, {"Title": "A new hybrid Pythagorean fuzzy AHP and COCOSO MCDM based approach by adopting artificial intelligence technologies", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "7", "Page": "1279", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}]}, {"ArticleId": 114311414, "Title": "Public Opinions on ChatGPT : An Analysis of Reddit Discussions by Using Sentiment Analysis, Topic Modeling, and SWOT Analysis", "Abstract": "<p>The sudden arrival of AI (Artificial Intelligence) into people's daily lives all around the world was marked by the introduction of ChatGPT, which was officially released on November 30, 2022. This AI invasion in our lives drew the attention of not only tech enthusiasts but also scholars from diverse fields, as its capacity extends across various fields. Consequently, numerous articles and journals have been discussing ChatGPT, making it a headline for several topics. However, it does not reflect most public opinion about the product. Therefore, this paper investigated the public's opinions on ChatGPT through topic modelling, Vader-based sentiment analysis and SWOT analysis. To gather data for this study, 202905 comments from the Reddit platform were collected between December 2022 and December 2023. The findings reveal that the Reddit community engaged in discussions related to ChatGPT, covering a range of topics including comparisons with traditional search engines, the impacts on software development, job market, and education industry, exploring ChatGPT's responses on entertainment and politics, the responses from <PERSON>, the alter ego of Chat<PERSON>T, the ethical usage of user data as well as queries related to the AI-generated images. The sentiment analysis indicates that most people hold positive views towards this innovative technology across these several aspects. However, concerns also arise regarding the potential negative impacts associated with this product. The SWOT analysis of these results highlights both the strengths and pain points, market opportunities and threats associated with ChatGPT. This analysis also serves as a foundation for providing recommendations aimed at the product development and policy implementation in this paper.</p>", "Keywords": "", "DOI": "10.1162/dint_a_00250", "PubYear": 2024, "Volume": "6", "Issue": "2", "JournalId": 64768, "JournalTitle": "Data Intelligence", "ISSN": "", "EISSN": "2641-435X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "International College of Digital Innovation and Financial Technology 239 Nimmanhaemin Road, Suthep, Muang, Chiang Mai 50200, Thailand"}, {"AuthorId": 2, "Name": "Piyachat <PERSON>", "Affiliation": "International College of Digital Innovation and Financial Technology 239 Nimmanhaemin Road, Suthep, Muang, Chiang Mai 50200, Thailand"}], "References": [{"Title": "This new conversational AI model can be your friend, philosopher, and guide ... and even your worst enemy", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "1", "Page": "100676", "JournalTitle": "Patterns"}, {"Title": "“What Can ChatGPT Do?” Analyzing Early Reactions to the Innovative AI Chatbot on Twitter", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "1", "Page": "35", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "Evaluation on ChatGPT for Chinese Language Understanding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "4", "Page": "885", "JournalTitle": "Data Intelligence"}]}, {"ArticleId": 114311436, "Title": "The Concept of Processing, Analysis and Visualization of Geophysical Data Based on Elements of Tensor Calculus", "Abstract": "<p>One of the main approaches to processing, analysis and visualization of geophysical data is the use of geographic information systems and technologies, which is due to their geospatial reference. At the same time, the complexity of presenting geophysical data is associated with their complex structure, which involves many components that have the same geospatial reference. Vivid examples of data of such a structure and format are gravitational and geomagnetic fields, which in the general case are specified by three and four-component vectors with multidirectional coordinate axes. At the same time, today there are no solutions that allow visualizing these data in a complex without decomposing them into individual scalar values, which, in turn, can be presented in the form of one or many spatial layers. In this regard, the work proposes a concept that uses elements of tensor calculus for processing, storing and visualizing information of this format. In particular, a mechanism for tensor representation of field components has been formalized with the possibility of combining it with other data of the same format, on the one hand, and convolution when combined with data of a lower rank. Using the example of a hybrid relational-hierarchical data model, a mechanism for storing information on tensor fields is proposed, which provides for the possibility of describing and subsequently applying transformation instructions when transitioning between different coordinate systems. The paper discusses the use of this approach in the transition from the Cartesian to the spherical coordinate system when representing the parameters of the geomagnetic field. For complex visualization of tensor field parameters, an approach based on the use of tensor glyphs is proposed. The latter are superellipses with axes corresponding to the rank of the tensor. In this case, the attribute values themselves are proposed to be visualized relative to the corresponding axes of the graphic primitive in such a way that the data distribution can be specified by varying the gradient of the corresponding monochrome representation of the parameter along the corresponding axis. The performance of the proposed concept was investigated during a comparative analysis of the tensor approach with known solutions based on the scalar decomposition of the corresponding complex values with their subsequent representation in the form of one or many spatial layers. The analysis showed that the use of the proposed approach will significantly increase the visibility of the generated geospatial image without the need for complex overlapping of spatial layers.</p>", "Keywords": "geographic information technologies; glyphs; superellipses; tensor calculus; tensor fields", "DOI": "10.15622/ia.23.2.10", "PubYear": 2024, "Volume": "23", "Issue": "2", "JournalId": 82239, "JournalTitle": "Информатика и автоматизация", "ISSN": "2713-3192", "EISSN": "2713-3206", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computational mathematics and cybernetics institute of mathematics, informatics and robotics, Ufa University of Science and Technology. Research interests: geoinformation and web technologies, systems of information storing and processing., 12, Karl Marx St., Ufa, 450077, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computational mathematics and cybernetics institute of mathematics, informatics and robotics, Ufa University of Science and Technology. Research interests: geoinformation and web technologies, systems of information storing and processing., 12, Karl Marx St., Ufa, 450077, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computational mathematics and cybernetics institute of mathematics, informatics and robotics, Ufa University of Science and Technology. Research interests: geoinformation and web technologies, systems of information storing and processing., 12, Karl Marx St., Ufa, 450077, Russian Federation"}], "References": [{"Title": "Feature Driven Combination of Animated Vector Field Visualizations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "3", "Page": "429", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Geoinformation system for analyzing the dynamics of extreme geomagnetic disturbances from observations of ground stations", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "5", "Page": "782", "JournalTitle": "Computer Optics"}, {"Title": "Geoinformation system for analyzing the dynamics of extreme geomagnetic disturbances from observations of ground stations", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "5", "Page": "782", "JournalTitle": "Computer Optics"}, {"Title": "Visualization of Tensor Fields in Mechanics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "6", "Page": "135", "JournalTitle": "Computer Graphics Forum"}, {"Title": "System for dynamic visualization of geomagnetic disturbances according to the data of ground magnetic stations", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "1", "Page": "162", "JournalTitle": "Scientific Visualization"}, {"Title": "Skyscraper visualization of multiple time-dependent scalar fields on surfaces", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "22", "JournalTitle": "Computers & Graphics"}]}, {"ArticleId": 114311439, "Title": "SARN: Script-aware recognition network for scene multilingual text recognition", "Abstract": "Recent research in script-specific text recognition has achieved remarkable performance on scene text images. However, the challenges of multilingual text in real-world images have not been effectively addressed, thereby limiting the practical application. For multilingual text recognition, the prevailing single-head scheme that uses the same recognition head for all scripts suffers from the issues of enormous alphabet size and linguistic knowledge discrepancies. Meanwhile, the multi-head scheme, which assigns distinct heads for individual scripts, encounters error accumulation within the two-step inference process. In this paper, we explore the influence of script attribute information within existing methods on achieving accurate recognition. Correspondingly, we propose a script-aware recognition network encompassing script identification and text recognition, which leverages script information to make character features more discriminative in text recognition. In particular, we design the script-aware module to utilize script information at the local level, global level, and their combination, respectively. Furthermore, our method is extended to the multi-head scheme, utilizing mutual assistance between script and character features to improve the performance in both script identification and text recognition tasks. Extensive experiments demonstrate that our method obtains superior results in multilingual text recognition, and reveal the importance of script information in alleviating the issues of single-head and multi-head recognition scheme.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.123753", "PubYear": 2024, "Volume": "250", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "Qingzhi Hou", "Affiliation": "State Key Laboratory of Hydraulic Engineering Intelligent Construction and Operation, Tianjin University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Song", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China;Correspondence to: College of Intelligence and Computing, Tianjin University, Tianjin, 300000, China.; Corresponding author"}], "References": [{"Title": "MuLTReNets: Multilingual text recognition networks for simultaneous script identification and handwriting recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107555", "JournalTitle": "Pattern Recognition"}, {"Title": "MASTER: Multi-aspect non-local network for scene text recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "107980", "JournalTitle": "Pattern Recognition"}, {"Title": "An extended attention mechanism for scene text recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117377", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Rethinking text rectification for scene text recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "219", "Issue": "", "Page": "119647", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114311467, "Title": "Enhancing reliability of failure modes and effects analysis dealing with linguistic distribution assessments: A consistency based approach", "Abstract": "Failure modes and effects analysis is a proactive reliability-management engineering technique, it has been applied to improve reliability of processes, products, and services. Traditional failure modes and effects analysis method manifests three deficiencies with regard to form of risk assessments, determination of risk factor weights, and exploitation of failure modes’ risk levels. Although numerous studies have contributed to surmount these deficiencies, existing failure modes and effects analysis methods still face such issues. Moreover, a novel drawback has raised when failure modes and effects analysis participants adopt linguistic terms as risk assessments of failure modes, the reliability of linguistic distribution assessments as well as failure modes and effects analysis groups (participants with similar knowledge background are previously classified into a failure modes and effects analysis group) is overlooked. Therefore, this paper introduces the transformation of linguistic distribution assessments into fuzzy preference relations, as more effective modeling for investigating consistency, by using the newly defined possibility degrees of linguistic distribution assessments will be applied to devise four algorithms for enhancing reliability of assessments. Hence, the three deficiencies of traditional failure modes and effects analysis method are solved by means of a novel failure modes and effects analysis framework. Finally, a real example with respect to ranking failure modes of worm wheel grinding machine is developed to verify the performance and advantages of the proposed method, in which ten failure modes are ranked and the one with the highest risk level is selected, and consistency of risk assessments is improved to be acceptable.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.108333", "PubYear": 2024, "Volume": "133", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Decision Sciences Institute, Fuzhou University, Fujian, 350116, China;Department of Computer Science, University of Jaén, Jaén, 23071, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Decision Sciences Institute, Fuzhou University, Key Laboratory of Spatial Data Mining & Information Sharing of Ministry of Education, Fujian, 350116, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Jaén, Jaén, 23071, Spain"}], "References": [{"Title": "Managing personalized individual semantics and consensus in linguistic distribution large-scale group decision making", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "20", "JournalTitle": "Information Fusion"}, {"Title": "A method based on the disappointment almost stochastic dominance degree for the multi-attribute decision making with linguistic distributions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "10", "JournalTitle": "Information Fusion"}, {"Title": "Prospect theory-based group decision-making with stochastic uncertainty and 2-tuple aspirations under linguistic assessments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "81", "JournalTitle": "Information Fusion"}, {"Title": "Failure mode and effects analysis (FMEA) for risk assessment based on interval type-2 fuzzy evidential reasoning method", "Authors": "Jindong Qin; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106134", "JournalTitle": "Applied Soft Computing"}, {"Title": "Distributed linguistic representations in decision making: Taxonomy, key elements and applications, and challenges in data science and explainable artificial intelligence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "65", "Issue": "", "Page": "165", "JournalTitle": "Information Fusion"}, {"Title": "Multi-attribute group decision making method based on prospect theory under hesitant probabilistic fuzzy environment", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106804", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Failure mode and effect analysis: An interval-valued intuitionistic fuzzy cloud theory-based method", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "106834", "JournalTitle": "Applied Soft Computing"}, {"Title": "Score function based on concentration degree for probabilistic linguistic term sets: An application to TOPSIS and VIKOR", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "551", "Issue": "", "Page": "270", "JournalTitle": "Information Sciences"}, {"Title": "A cohesion-driven consensus reaching process for large scale group decision making under a hesitant fuzzy linguistic term sets environment", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "155", "Issue": "", "Page": "107158", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A consensus process based on regret theory with probabilistic linguistic term sets and its application in venture capital", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "562", "Issue": "", "Page": "347", "JournalTitle": "Information Sciences"}, {"Title": "Regret Theory-Based Case-Retrieval Method with Multiple Heterogeneous Attributes and Incomplete Weight Information", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1022", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Interactive multi-criteria group decision-making with probabilistic linguistic information for emergency assistance of COVID-19", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107383", "JournalTitle": "Applied Soft Computing"}, {"Title": "Group risk assessment in failure mode and effects analysis using a hybrid probabilistic hesitant fuzzy linguistic MCDM method", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "188", "Issue": "", "Page": "116013", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A two-sided matching decision-making approach based on prospect theory under the probabilistic linguistic environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "8", "Page": "3921", "JournalTitle": "Soft Computing"}, {"Title": "Improving consistency based on regret theory: A multi-attribute group decision making method with linguistic distribution assessments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "221", "Issue": "", "Page": "119748", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel failure mode and effect analysis method with spherical fuzzy entropy and spherical fuzzy weight correlation coefficient", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106163", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Handling multi-granular hesitant information: A group decision-making method based on cross-efficiency with regret theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "227", "Issue": "", "Page": "120332", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Reinforcement and deep reinforcement learning-based solutions for machine maintenance planning, scheduling policies, and optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "70", "Issue": "", "Page": "244", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A social network analysis-based model for failure mode and effect analysis under linguistic preference relation environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107119", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A novel Multi-Criteria Decision Making framework based on Evidential Reasoning dealing with missing information from online reviews", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "106", "Issue": "", "Page": "102264", "JournalTitle": "Information Fusion"}, {"Title": "Probability estimation and structured output prediction for learning preferences in last mile delivery", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "189", "Issue": "", "Page": "109932", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Robust two-stage minimum asymmetric cost consensus models under uncertainty circumstances", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "663", "Issue": "", "Page": "120279", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 114311496, "Title": "Stopping fake news: Who should be banned?", "Abstract": "<p>Fake news and misinformation spread in online social networks in a manner similar to contagious diseases. One possibility to thwart the contagion cascade is to selectively remove a small number of nodes from the network. Although most of the literature has focused on the selection of those nodes on the basis of their topological position in the network, we pose that attributes of the nodes themselves can be more relevant in certain situations. In order to demonstrate this hypothesis, we introduce a new model of news propagation that accounts for nodes’ attributes. In particular, we introduce three important characteristics of a node: the influence capacity, the resistance to be influenced and the resistance to become an information spreader. Besides offering an intuitive justification for the model and these new parameters, we relate them to other proposals in the literature. Under the new model and using numerical simulations on both synthetic and real life networks, we show that nodes’ attributes can be more important than their graph structural properties in choosing an adequate set of vertices to be removed with the purpose of mitigating fake news propagation. Furthermore, our results suggest that removal of nodes with high influence power is more effective in denser networks and when the influence of a few nodes is much larger than that of the general population.</p>", "Keywords": "Complex networks; Misinformation; Information diffusion; Online social networks", "DOI": "10.1007/s41060-024-00532-x", "PubYear": 2024, "Volume": "18", "Issue": "3", "JournalId": 27236, "JournalTitle": "International Journal of Data Science and Analytics", "ISSN": "2364-415X", "EISSN": "2364-4168", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto Tecnológico de Buenos Aires (ITBA), Ciudad Autónoma de Buenos Aires, Argentina; Department, National Scientific and Technical Research Council (CONICET), Ciudad Autónoma de Buenos Aires, Argentina; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departamento de Estatística e Matemática Aplicada, Universidade Federal do Ceará, Campus do Pici, Fortaleza, Brazil; Programa de Pós-Graduação em Engenharia de Produção, Universidade Federal de Pernambuco, Cidade Universitária, Recife, Brazil"}], "References": [{"Title": "Combating misinformation online: re-imagining social media for policy-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "4", "Page": "1", "JournalTitle": "Internet Policy Review"}, {"Title": "The perils of legally defining disinformation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "4", "Page": "", "JournalTitle": "Internet Policy Review"}]}, {"ArticleId": 114311638, "Title": "Proving new directed tile complexity lower bounds at temperature 1 by folding between 2D and just-barely 3D self-assembly", "Abstract": "<p>We study the problem of determining the size of the smallest tile set that uniquely self-assembles into a given target shape in Winfree’s abstract Tile Assembly Model (aTAM), an elegant theoretical model of DNA tile self-assembly. This problem is also known as the “directed tile complexity” problem. We prove two main results related to the directed tile complexity problem within a variant of the aTAM in which the minimum binding strength threshold (temperature) is set to 1. For our first result, self-assembly happens in a “just-barely 3D” setting, where self-assembling unit cubes are allowed to be placed in the \\(z=0\\) and \\(z=1\\) planes. This is the same setting in which <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (DNA 2021) recently proved lower and upper bounds on the directed tile complexity of a just-barely 3D \\(k \\times N\\) rectangle at temperature 1 of \\(\\Omega \\left( N^{\\frac{1}{k}}\\right) \\) and \\(O\\left( N^{\\frac{1}{k-1}}+\\log N\\right) \\) , respectively, the latter of which does not hold for \\(k=2\\) . Our first result closes this gap for \\(k=2\\) by proving an asymptotically tight bound of \\(\\Theta (N)\\) on the directed tile complexity of a just-barely 3D \\(2 \\times N\\) rectangle at temperature 1. Our proof uses a novel process by which a just-barely 3D assembly sequence is “unfolded” to an equivalent 2D assembly sequence. For our second result, we use the aforementioned lower bound by <PERSON>rcy, <PERSON> and Withers and a novel process that is complementary-in-spirit to our 3D-to-2D unfolding process, by which we “fold” a 2D tile assembly to an equivalent just-barely 3D assembly to prove a new lower bound on the directed tile complexity of a 2D \\(k \\times N\\) rectangle at temperature 1 of \\(\\Omega \\left( \\frac{N^{\\frac{2}{k + (k \\bmod 2)}}}{k} \\right) \\) . For fixed k , our new bound gives a nearly quadratic improvement over, and matches for general even values of \\(k < \\frac{\\log N}{\\log \\log N - \\log \\log \\log N}\\) the state of the art lower bound on the directed tile complexity of a \\(k \\times N\\) rectangle at temperature 1 by Furcy, Summers and Wendlandt (DNA 2019) of \\(\\Omega \\left( N^{\\frac{1}{k}}\\right) \\) . While both of our results represent improvements over previous corresponding state of the art results, the proofs thereof are facilitated by novel examples of reasoning about tile self-assembly happening in 2D (just-barely 3D) as though it is happening in just-barely 3D (2D).</p>", "Keywords": "", "DOI": "10.1007/s11047-024-09979-0", "PubYear": 2025, "Volume": "24", "Issue": "1", "JournalId": 1251, "JournalTitle": "Natural Computing", "ISSN": "1567-7818", "EISSN": "1572-9796", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science Department, University of Wisconsin Oshkosh, Oshkosh, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science Department, University of Wisconsin Oshkosh, Oshkosh, USA; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computer Science Department, University of Wisconsin Oshkosh, Oshkosh, USA"}], "References": []}, {"ArticleId": 114311655, "Title": "Improved Boost Converter with Functions of Adjustable Output Voltages and Currents", "Abstract": "In this paper, we propose an improved boost converter with functions of adjustable output voltages and currents. The DC sources of electronic products using batteries have become indispensable equipment. These charging sources of the diverse batteries must be provided by switching power converters. To obtain functions of adjustable output voltages and currents, a conventional boost converter is usually accepted. However, the power semiconductor devices (power switches and power diodes) of the conventional boost converter are operated at high frequency, which causes high power losses, high temperatures, and low conversion efficiency. To overcome these disadvantages, a passive lossless snubber is incorporated in the conventional boost converter to reduce power losses and increase conversion efficiency. In this paper, we propose an improved boost converter to implement functions of adjustable output voltages and currents. We compare the performance and efficiency between the conventional and improved boost converter with functions of adjustable output voltages and currents. Theoretical analysis and experimental results confirmed that the improved boost converters with functions of adjustable output voltages and currents has lower power losses, lower temperatures and higher conversion efficiency than the conventional boost converter. © MYU K.K.", "Keywords": "hard-switching; soft-switching", "DOI": "10.18494/SAM4722", "PubYear": 2024, "Volume": "36", "Issue": "3", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Chin-Yi University of Technology, Taichung, 41170, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Chin-Yi University of Technology, Taichung, 41170, Taiwan"}], "References": []}, {"ArticleId": 114311662, "Title": "Building Facility Management System Using Sensors and Digital Technologies", "Abstract": "Globally, 50.44% of buildings are over 30 years old. Old buildings have problems such as obsolete safety monitoring systems and poor management efficiency caused by the use of old equipment. For the better management of buildings and their facilities, we developed an efficient management system based on digitized data from sensors in each device that is connected to the system. The system monitored the operation of each device in real time and sent messages to maintenance personnel in case of any abnormality in each device. This system ensured the normal operation of the building and saved costs and energy via efficient functions. The system was developed and implemented for one year. The operation outcome showed the completeness, relevance, standardization, and visualization of collected data and information. It also enabled the effective scheduling of operation and maintenance by querying the data from each device for intelligent building management. © 2024 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "AIoT; BIM; facility management; old buildings; sensors", "DOI": "10.18494/SAM4869", "PubYear": 2024, "Volume": "36", "Issue": "3", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Chung Hua University, No. 707, WuFu Road, Section 2, Hsinchu, 30012, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Architecture and Urban Planning, Chung Hua University, No. 707 WuFu Road Section 2, Hsinchu, 30012, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Architecture and Urban Planning, Chung Hua University, No. 707 WuFu Road Section 2, Hsinchu, 30012, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Chung Hua University, No. 707, WuFu Road, Section 2, Hsinchu, 30012, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Architecture and Design, Chung Hua University, No. 707 WuFu Road Section 2, Hsinchu, 30012, Taiwan"}], "References": []}, {"ArticleId": 114311687, "Title": "A compact RTK‐GNSS device for high‐precision localization of outdoor mobile robots", "Abstract": "Accurate location information is crucial for ensuring the reliability of service robots, such as field vehicles and agricultural robots. These practical robots often encounter fluctuating or inclined terrains in dynamic conditions during operation. Additionally, the performance of mobile robots depends on the physical size and weight of the localization system–physically heavy systems may degrade performance. In this paper, we present a compact, lightweight RTK‐GNSS device weighing under 40 g, which achieves centimeter‐level accuracy across different attitudes and dynamic situations. To enhance signal reception and positioning performance, we lessen noise levels in the power circuit and radio‐frequency signal conditioning using a low‐dropout regulator and low‐noise amplifier. Utilizing the configured device, we verified centimeter‐level accuracy through experiments of tracking an unmanned ground vehicle. The potential of the device is substantiated via three application scenarios—a localization of a moving human for a wearable device, a heading estimation of a mobile robot in stationary conditions, and practical field tests in various signal‐reception environments. Attributable to its compact dimensions and lightweight features, the proposed device can yield a low system strain while maintaining high accuracy and reliable performance, rendering it highly adaptable for various robotic applications.", "Keywords": "", "DOI": "10.1002/rob.22317", "PubYear": 2024, "Volume": "41", "Issue": "5", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering Korea Advanced Institute of Science and Technology (KAIST) Daejeon Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Fitogether Incorporated Seoul Republic of Korea"}, {"AuthorId": 3, "Name": "Dong‐Wook <PERSON>", "Affiliation": "Department of Aerospace Engineering Korea Advanced Institute of Science and Technology (KAIST) Daejeon Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering Korea Advanced Institute of Science and Technology (KAIST) Daejeon Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Football Technology & Innovation Fédération Internationale de Football Association (FIFA) Zurich Switzerland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Football Technology & Innovation Fédération Internationale de Football Association (FIFA) Zurich Switzerland"}, {"AuthorId": 7, "Name": "Chulwoo Park", "Affiliation": "Fitogether Incorporated Seoul Republic of Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fitogether Incorporated Seoul Republic of Korea"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON> Lee", "Affiliation": "Department of Aerospace Engineering Korea Advanced Institute of Science and Technology (KAIST) Daejeon Republic of Korea"}], "References": []}]