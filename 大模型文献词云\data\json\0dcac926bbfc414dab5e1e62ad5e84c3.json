[{"ArticleId": 117621689, "Title": "A Clustering Pruning Method Based on Multidimensional Channel Information", "Abstract": "<p>Pruning convolutional neural networks offers a promising solution to mitigate the computational complexity challenges encountered during application deployment. However, prevalent pruning techniques primarily concentrate on model parameters or feature mapping analysis to devise static pruning strategies, often overlooking the underlying feature extraction capacity of convolutional kernels. To address this, the study first quantitatively expresses the feature extraction capability of convolutional channels from three aspects: global features, distribution metrics, and directional metrics. It explores the multi-dimensional information of the channels, calculates the overall expectation, variance, and cosine distance from the unit vector as the quantitative results of the channels. Subsequently, a clustering algorithm is employed to categorize the multidimensional information. This approach ensures that convolutional channels grouped within each cluster possess similar feature extraction capabilities. An enhanced differential evolutionary algorithm is utilized to optimize the number of clustering centers across all convolutional layers, ensuring optimal grouping. The final step involves achieving channel sparsification through the calculation of crowding distances for each sample within its designated cluster. This preserves a diverse subset of channels that are critical for maintaining model accuracy. Extensive empirical evaluations conducted on three benchmark image classification datasets demonstrate the efficacy of this method. For instance, on the ImageNet dataset, the ResNet-50 model experiences a substantial reduction in FLOPs by 58.43% while incurring a minimal decrease in TOP-1 accuracy of only 1.15%.</p>", "Keywords": "Channel pruning; Efficient reasoning; Multidimensional information; Network compression", "DOI": "10.1007/s11063-024-11684-z", "PubYear": 2024, "Volume": "56", "Issue": "5", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Dynamic Measurement Technology, North University of China, Taiyuan, China; School of Electrical and Control Engineering, North University of China, Taiyuan, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Dynamic Measurement Technology, North University of China, Taiyuan, China; School of Electrical and Control Engineering, North University of China, Taiyuan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Dynamic Measurement Technology, North University of China, Taiyuan, China; School of Electrical and Control Engineering, North University of China, Taiyuan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Coal Mine Disaster Dynamics and Control, Chongqing University, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Dynamic Measurement Technology, North University of China, Taiyuan, China; School of Electrical and Control Engineering, North University of China, Taiyuan, China"}], "References": [{"Title": "Bi-Real Net: Binarizing Deep Network Towards Real-Network Performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "1", "Page": "202", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Filter pruning with a feature map entropy importance criterion for convolution neural networks compressing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "461", "Issue": "", "Page": "41", "JournalTitle": "Neurocomputing"}, {"Title": "Filter pruning via separation of sparsity search and model training", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "462", "Issue": "", "Page": "185", "JournalTitle": "Neurocomputing"}, {"Title": "FPC: Filter pruning via the contribution of output feature map for deep convolutional neural networks acceleration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "238", "Issue": "", "Page": "107876", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An optimal-score-based filter pruning for deep convolutional neural networks", "Authors": "Shrutika <PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "15", "Page": "17557", "JournalTitle": "Applied Intelligence"}, {"Title": "Channel pruning based on convolutional neural network sensitivity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "507", "Issue": "", "Page": "97", "JournalTitle": "Neurocomputing"}, {"Title": "EACP: An effective automatic channel pruning for neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "526", "Issue": "", "Page": "131", "JournalTitle": "Neurocomputing"}, {"Title": "Convolutional neural network pruning based on multi-objective feature map selection for image classification", "Authors": "<PERSON><PERSON><PERSON> Jiang; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "110229", "JournalTitle": "Applied Soft Computing"}, {"Title": "Filter pruning by quantifying feature similarity and entropy of feature maps", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "544", "Issue": "", "Page": "126297", "JournalTitle": "Neurocomputing"}, {"Title": "Loss-aware automatic selection of structured pruning criteria for deep neural network acceleration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "104745", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 117621748, "Title": "Securing online integrity: a hybrid approach to deepfake detection and removal using Explainable AI and Adversarial Robustness Training", "Abstract": "As deepfake technology becomes increasingly sophisticated, the proliferation of manipulated images presents a significant threat to online integrity, requiring advanced detection and mitigation strategies. Addressing this critical challenge, our study introduces a pioneering approach that integrates Explainable AI (XAI) with Adversarial Robustness Training (ART) to enhance the detection and removal of deepfake content. The proposed methodology, termed XAI-ART, begins with the creation of a diverse dataset that includes both authentic and manipulated images, followed by comprehensive preprocessing and augmentation. We then employ Adversarial Robustness Training to fortify the deep learning model against adversarial manipulations. By incorporating Explainable AI techniques, our approach not only improves detection accuracy but also provides transparency in model decision-making, offering clear insights into how deepfake content is identified. Our experimental results underscore the effectiveness of XAI-ART, with the model achieving an impressive accuracy of 97.5% in distinguishing between genuine and manipulated images. The recall rate of 96.8% indicates that our model effectively captures the majority of deepfake instances, while the F1-Score of 97.5% demonstrates a well-balanced performance in precision and recall. Importantly, the model maintains high robustness against adversarial attacks, with a minimal accuracy reduction to 96.7% under perturbations.", "Keywords": "Adversarial attacks; Adversarial Robustness Training (ART); deepfake technology; detection strategies; digital content integrity; Explainable AI (XAI)", "DOI": "10.1080/00051144.2024.2400640", "PubYear": 2024, "Volume": "65", "Issue": "4", "JournalId": 7651, "JournalTitle": "Automatika", "ISSN": "0005-1144", "EISSN": "1848-3380", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Hindusthan Institute of Technology, Coimbatore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Hindusthan Institute of Technology, Coimbatore, India"}], "References": [{"Title": "Adversarial Machine Learning Attacks and Defense Methods in the Cyber Security Domain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "ResNet-Swish-Dense54: a deep learning approach for deepfakes detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "12", "Page": "6323", "JournalTitle": "The Visual Computer"}, {"Title": "An explainable deepfake detection framework on a novel unconstrained dataset", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "4", "Page": "4425", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A comprehensive taxonomy on multimedia video forgery detection techniques: challenges and novel trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "2", "Page": "4241", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Secure and Trustworthy Artificial Intelligence-Extended Reality (AI-XR) for Metaverses", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "7", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Secure and Trustworthy Artificial Intelligence-Extended Reality (AI-XR) for Metaverses", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "7", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Artificial Intelligence Trust, Risk and Security Management (AI TRiSM): Frameworks, applications, challenges and future research directions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "240", "Issue": "", "Page": "122442", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deepfake detection using deep learning methods: A systematic and comprehensive review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "14", "Issue": "2", "Page": "e1520", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "Deepfake Detection and Classification of Images from Video: A Review of Features, Techniques, and Challenges", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "13", "Issue": "2", "Page": "20", "JournalTitle": "International Journal of Intelligent Information Systems"}, {"Title": "Adversarial attacks and defenses in person search: A systematic mapping study and taxonomy", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "148", "Issue": "", "Page": "105096", "JournalTitle": "Image and Vision Computing"}, {"Title": "Deepfake video detection: challenges and opportunities", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "6", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A review of deep learning‐based approaches for deepfake content detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> A. P. Costa", "PubYear": 2024, "Volume": "41", "Issue": "8", "Page": "", "JournalTitle": "Expert Systems"}]}, {"ArticleId": *********, "Title": "Globus service enhancements for exascale applications and facilities", "Abstract": "<p>Many extreme-scale applications require the movement of large quantities of data to, from, and among leadership computing facilities, as well as other scientific facilities and the home institutions of facility users. These applications, particularly when leadership computing facilities are involved, can touch upon edge cases (e.g., terabyte files) that had not been a focus of previous Globus optimization work, which had emphasized rather the movement of many smaller (megabyte to gigabyte) files. We report here on how automated client-driven chunking can be used to accelerate both the movement of large files and the integrity checking operations that have proven to be essential for large data transfers. We present detailed performance studies that provide insights into the benefits of these modifications in a range of file transfer scenarios.</p>", "Keywords": "", "DOI": "10.1177/10943420241281744", "PubYear": 2024, "Volume": "38", "Issue": "6", "JournalId": 3326, "JournalTitle": "The International Journal of High Performance Computing Applications", "ISSN": "1094-3420", "EISSN": "1741-2846", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Data Science and Learning Division, Argonne National Laboratory, Lemont, IL, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Globus, University of Chicago, Chicago, IL, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Center for Computational Sciences, Oak Ridge National Laboratory, Oak Ridge, TN, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Data Science and Learning Division, Argonne National Laboratory, Lemont, IL, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Data Science and Learning Division, Argonne National Laboratory, Lemont, IL, USA;Department of Computer Science, University of Chicago, Chicago, IL, USA"}], "References": [{"Title": "Design and Evaluation of a Simple Data Interface for Efficient Data Transfer across Diverse Storage", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Modeling and Performance Evaluation of Computing Systems"}]}, {"ArticleId": 117621818, "Title": "Correction: The case for a broader approach to AI assurance: addressing “hidden” harms in the development of artificial intelligence", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00146-024-02001-2", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117621874, "Title": "Analyzing and Predicting Consumer Response to Short Videos in E-Commerce", "Abstract": "<p>This study analyzes the drivers of and predicts the outcome of consumer response to e-commerce short videos (ESVs) in terms of viewing duration. We first construct a large-scale ESV dataset that contains 23,001 ESVs across 40 product categories. The dataset consists of the consumer response label in terms of average viewing durations and human-annotated ESV content attributes. Relying on the constructed dataset, we employ econometric modeling and deep learning methods to comprehensively understand and predict consumer response to ESVs. First, by employing the mixed-effects model, we find that ESV content attributes for product description, product demonstration, pleasure, and aesthetics are key determinants of ESV viewing duration. Subsample analyses further show the heterogeneous effects of different content attributes on ESV viewing response across product categories. Second, we design a content-based multimodal-multitask framework to predict consumers’ viewing response to ESVs. We propose an information distillation module to extract the shared, special, and conflicted information from ESV multimodal features. Additionally, we employ a hierarchical multitask classification module to capture the feature-level and label-level dependencies. By conducting extensive experiments, we demonstrate that the prediction performance of our proposed framework is superior to that of other state-of-the-art models. Taken together, our paper provides novel theoretical and methodological contributions to the Information Systems and relevant literatures.</p>", "Keywords": "", "DOI": "10.1145/3690393", "PubYear": 2024, "Volume": "15", "Issue": "4", "JournalId": 24450, "JournalTitle": "ACM Transactions on Management Information Systems", "ISSN": "2158-656X", "EISSN": "2158-6578", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>*", "Affiliation": "Shenzhen Finance Institute, School of Management and Economics, The Chinese University of Hong Kong - Shenzhen, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "China Telecom Artificial Intelligence Technology Co. Ltd, Beijing, China and China Telecom Corporation Limited, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Zhejiang Lab, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, National University of Singapore, Singapore, Singapore"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Beijing Institute for General Artificial Intelligence, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Zhejiang University, Hangzhou, China"}, {"AuthorId": 7, "Name": "Xiaobo Li", "Affiliation": "Ant Group CO Ltd, Hangzhou, China"}], "References": [{"Title": "Modality-specific and shared generative adversarial network for cross-modal retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "107335", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 117621925, "Title": "Limited value of EEG source imaging for decoding hand movement and imagery in youth with brain lesions", "Abstract": "", "Keywords": "", "DOI": "10.1080/2326263X.2024.2400741", "PubYear": 2024, "Volume": "11", "Issue": "3", "JournalId": 24751, "JournalTitle": "Brain-Computer Interfaces", "ISSN": "2326-263X", "EISSN": "2326-2621", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Bloorview Research Institute, Holland Bloorview Kids Rehabilitation Hospital, Toronto, Canada;Institute of Biomedical Engineering, University of Toronto, Toronto, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bloorview Research Institute, Holland Bloorview Kids Rehabilitation Hospital, Toronto, Canada;Institute of Biomedical Engineering, University of Toronto, Toronto, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bloorview Research Institute, Holland Bloorview Kids Rehabilitation Hospital, Toronto, Canada;Institute of Biomedical Engineering, University of Toronto, Toronto, Canada"}], "References": []}, {"ArticleId": 117622059, "Title": "Control-Oriented Modeling of a Solid Oxide Fuel Cell Affected by Redox Cycling Using a Novel Deep Learning Approach", "Abstract": "<p>A solid oxide fuel cell (SOFC) is a multiphysics system that involves heat transfer, mass transport, and electrochemical reactions to produce electrical power. Reduction and re-oxidation (Redox) cycling is a destructive reaction that can occur during SOFC operation. Redox induces various degradation mechanisms, such as electrode delamination, nickel agglomeration, and microstructural changes, which should be mitigated. The interplay of these mechanisms makes a post-Redox SOFC a nonlinear, time-varying, nonstationary dynamic system. Physics-based modeling of these complexities often leads to computationally expensive equations that are not suitable for the control and diagnostics of SOFCs. Here, a data-driven approach based on dilated convolutions and a self-attention mechanism is introduced to effectively capture the dynamics underlying SOFCs affected by Redox. Controlled Redox cycles are designed to collect appropriate experimental data for developing deep learning models, which are lacking in the current literature. The performance of the proposed model is validated on diverse unseen data sets gathered from different fuel cells and benchmarked against state-of-the-art models, in terms of prediction accuracy and computation complexity. The results indicate 31% accuracy improvement and 27% computation speed enhancement compared to the benchmarks.</p>", "Keywords": "Modeling;Solid oxide fuel cells;Fuel cells;Cycles;Computation;Dynamics (Mechanics);Oxidation;Deep learning", "DOI": "10.1115/1.4066268", "PubYear": 2025, "Volume": "147", "Issue": "2", "JournalId": 9493, "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control", "ISSN": "0022-0434", "EISSN": "1528-9028", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, University of Alberta , Edmonton, AB T6G 2R3, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, University of Alberta , Edmonton, AB T6G 1H, Canada; University of Alberta"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Research and Technology Center, Cummins Inc , Columbus, IN 47202-3005; Cummins (United States)"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Research and Technology Center, Cummins Inc , Columbus, IN 47201"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering Department, University of Alberta , Edmonton, AB T6G 1H9, Canada; University of Alberta"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering Department, University of Alberta , Edmonton, AB T6G 2G8, Canada"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, University of Alberta , Edmonton, AB T6G 1H9, Canada"}], "References": [{"Title": "A Particle Filter and Long Short-Term Memory Fusion Technique for Lithium-Ion Battery Remaining Useful Life Prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "143", "Issue": "6", "Page": "", "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control"}, {"Title": "A Multirange Vehicle Speed Prediction With Application to Model Predictive Control-Based Integrated Power and Thermal Management of Connected Hybrid Electric Vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "144", "Issue": "1", "Page": "", "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control"}, {"Title": "Neural Network-Based Electric Vehicle Range Prediction for Smart Charging Optimization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "144", "Issue": "1", "Page": "", "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control"}, {"Title": "Temporal dilated convolution and nonlinear autoregressive network for predicting solid oxide fuel cell performance", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "136", "Issue": "", "Page": "108994", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 117622092, "Title": "周期離散時間系に基づく一脚ホッピングロボットの姿勢安定化制御", "Abstract": "", "Keywords": "", "DOI": "10.7210/jrsj.39.735", "PubYear": 2021, "Volume": "39", "Issue": "8", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Science and Engineering, Ibaraki University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Science and Engineering, Ibaraki University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Science and Engineering, Ibaraki University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Science and Engineering, Ibaraki University"}], "References": []}, {"ArticleId": 117622364, "Title": "A Compact Slot-Based Bi-Directional UHF RFID Reader Antenna for Far-Field Applications", "Abstract": "", "Keywords": "", "DOI": "10.1109/JRFID.2024.3457691", "PubYear": 2024, "Volume": "8", "Issue": "", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Science, University of Delhi South Campus, New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Science, University of Delhi South Campus, New Delhi, India"}, {"AuthorId": 3, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Science, University of Delhi South Campus, New Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Science, University of Delhi South Campus, New Delhi, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Science, University of Delhi South Campus, New Delhi, India"}], "References": [{"Title": "Investigation of Circularly Polarized CPW Fed Antenna as a 2.45 GHz RFID Reader", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "", "Page": "593", "JournalTitle": "IEEE Journal of Radio Frequency Identification"}, {"Title": "Circular Slot-Based Microstrip Circularly Polarized Antenna for 2.45-GHz RFID Reader Applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "", "Page": "10", "JournalTitle": "IEEE Journal of Radio Frequency Identification"}]}, {"ArticleId": 117622386, "Title": "Reliability and Optimization of Wire Bonding in Power Microelectronic Devices", "Abstract": "<p>A numerical investigation of the probabilistic approach in estimating the reliability of wire bonding is presented along with a reliability based design optimization methodology for microelectronic devices structures.</p>", "Keywords": "", "DOI": "10.31399/asm.edfa.2024-3.p028", "PubYear": 2024, "Volume": "26", "Issue": "3", "JournalId": 97479, "JournalTitle": "EDFA Technical Articles", "ISSN": "1537-0755", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Norelislam <PERSON>", "Affiliation": "Science and Engineering Laboratory, ENSA, University Ibn Tofail , Kénitra, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "EST, University Ibn Tofail , Kénitra, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Mechanics of Normandy , INSA, Rouen, France"}], "References": []}, {"ArticleId": 117622413, "Title": "An improved continuous and discrete Harris Hawks optimiser applied to feature selection for image steganalysis", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCSE.2024.141339", "PubYear": 2024, "Volume": "27", "Issue": "5", "JournalId": 8309, "JournalTitle": "International Journal of Computational Science and Engineering", "ISSN": "1742-7185", "EISSN": "1742-7193", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117622507, "Title": "Call with eyes: A robust interface based on ANN to assist people with locked-in syndrome", "Abstract": "Given the critical need for effective communication for individuals with locked-in syndrome (LIS), significant efforts have been made to develop assistive technologies that minimize error margins and enhance reliability. In this work, we present a Python-based interface specifically designed to assist individuals with LIS, utilizing the face mesh library to reduce variability in facial illumination and background, thereby improving the detection of key facial points. The interface captures the spatial position of each iris center, along with other points of interest in the patient's eyes (30 points in total), calculating 54 Euclidean distances. Data from 10 individuals were used to train an artificial neural network (ANN) capable of identifying and classifying four gestures: vertical eye movements (looking up and looking down), closed eyes, and open eyes (no movement), achieving an accuracy of 99.8%. This ANN model was integrated into the developed interface, allowing the user to navigate through a vertical menu with five options. The user can position over an option using eye movements and select the desired option by closing their eyes. The interface was validated with five individuals whose data were not used to train the ANN, achieving error-free selection of all five options. The proposed system demonstrates superior performance compared to other systems, with the added advantage of being a simple, robust solution that does not require calibration for new users.", "Keywords": "ANN network; Gesture recognition; Locked-in syndrome; Interface", "DOI": "10.1016/j.softx.2024.101883", "PubYear": 2024, "Volume": "27", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>Vargas", "Affiliation": "Centro de Investigación en Ingeniería y Ciencias Aplicadas (CIICAp), Universidad Autónoma del Estado de Morelos, Cuernavaca, Morelos 62209, Mexico"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centro de Investigación en Ingeniería y Ciencias Aplicadas (CIICAp), Universidad Autónoma del Estado de Morelos, Cuernavaca, Morelos 62209, Mexico;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centro de Investigación en Ingeniería y Ciencias Aplicadas (CIICAp), Universidad Autónoma del Estado de Morelos, Cuernavaca, Morelos 62209, Mexico"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Centro de Investigación en Ingeniería y Ciencias Aplicadas (CIICAp), Universidad Autónoma del Estado de Morelos, Cuernavaca, Morelos 62209, Mexico"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Centro de Investigación en Ingeniería y Ciencias Aplicadas (CIICAp), Universidad Autónoma del Estado de Morelos, Cuernavaca, Morelos 62209, Mexico"}, {"AuthorId": 6, "Name": "<PERSON>-Alatorre", "Affiliation": "Centro de Investigación en Ingeniería y Ciencias Aplicadas (CIICAp), Universidad Autónoma del Estado de Morelos, Cuernavaca, Morelos 62209, Mexico"}], "References": [{"Title": "Driver eye movements in relation to unfamiliar traffic signs: An eye tracking study", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "103191", "JournalTitle": "Applied Ergonomics"}, {"Title": "A Novel Approach on Converting Eye Blink Signals in EEG to Speech with Cross Correlation Technique", "Authors": "N. IKIZLER; G. EKIM; A. ATASOY", "PubYear": 2023, "Volume": "23", "Issue": "2", "Page": "29", "JournalTitle": "Advances in Electrical and Computer Engineering"}]}, {"ArticleId": 117622562, "Title": "From batch to continuous tablet manufacturing: a control perspective", "Abstract": "Despite manufacturing innovations and the technologies on the rise, solid oral dosage in the pharmaceutical industry is still mass production. Although this is efficient and cost-effective, it is typically based on a ‘one-size-fits-all’ product concept and lacks the flexibility and agility required to fully meet the needs of the individual patient. Nowadays pharmaceutical industry is experiencing a paradigm shift from batch to continuous manufacturing. This will lead to increased flexibility to target diverse populations as well as more consistent product quality to ensure best efficacy. Continuous processing integrated with online/inline monitoring tools coupled with an efficient automatic feedback control system is highly desired by the pharmaceutical industry. To facilitate the transition from the batch wise production to continuous manufacturing in the pharma industry engineering tools are needed. Hence, the aim of this paper is to enhance the advantage of modeling and control techniques in the field of pharmaceutical applications.", "Keywords": "continuous manufacturing; process control; advanced manufacturing; personalized medicine", "DOI": "10.1016/j.ifacol.2021.10.316", "PubYear": 2021, "Volume": "54", "Issue": "15", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Research group of Dynamical Systems and Control, Ghent University, Tech Lane Science Park 125, 9052, Ghent, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Research group of Dynamical Systems and Control, Ghent University, Tech Lane Science Park 125, 9052, Ghent, Belgium;EEDT—Core Lab on Decision and Control, Flanders Make Consortium, Tech Lane Science Park 131, 9052, Ghent, Belgium"}], "References": []}, {"ArticleId": 117622570, "Title": "DEPENDENCE OF GENERA<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> RHINITS INCIDENCE OF THE POPULATION ON THE PERFORMANCE OF THE INCINERATION PLANT (Eng)", "Abstract": "Allergic rhinitis incidence may be caused by the pollution of the environment, in particular, by flue gases of the incineration plants, this gas is a mixture of 27 components. Determination of the regression dependence of general allergic rhinitis incidence of the population on the performance of the incineration plant is a relevant scientific engineering problem as it can be used for the prediction of such incidence rate. Objective of the research is the construction by means of regressive analysis the regressional dependence of general allergic rhinitis incidence rate of the population on the performance of the incineration plant, this dependence can be used for the prediction of the incidence rate. In the process of studies the method of regression analysis of the results of single-factor experiments and other paired dependences with the selection of the most adequate type of function from sixteen most widely used variants by the criterion of maximum values of the correlation coefficient was used. Regression was performed on the base of the linearized transformations, which enable to reduce non-linear dependence to linear one. Determination of the coefficients of the regression equations was performed, applying the method of the least squares by means of the developed computer program \"RegAnaliz\". Regression dependence of the allergic rhinitis prevalence rate on the performance of the incineration plant was obtained, it can be used for the prediction of the incidence rate of the disease. Graphic dependence of the allergic rhinitis prevalence rate of the population on the performance of the incineration plant was constructed, it enables to illustrate the dependence and show the sufficient coincidence of the theoretical results with actual data. It is established that the allergic rhinitis prevalence rate of the population increases with the growth of the incineration plant performance in power dependence.", "Keywords": "incineration plant;solid municipal waste;incidence rate;allergic rhinitis;regression analysis", "DOI": "10.31649/2307-5392-2023-3-24-29", "PubYear": 2023, "Volume": "", "Issue": "3", "JournalId": 71785, "JournalTitle": "Scientific Works of Vinnytsia National Technical University", "ISSN": "", "EISSN": "2307-5392", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Vinnytsia National Technical University"}, {"AuthorId": 2, "Name": "<PERSON>. <PERSON><PERSON>", "Affiliation": "Vinnytsia M<PERSON> National Medical University"}, {"AuthorId": 3, "Name": "L. <PERSON><PERSON>", "Affiliation": "Vinnytsia M. Kotsiubynskyi State Pedagogical University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Vinnytsia M<PERSON> National Medical University"}], "References": []}, {"ArticleId": 117622614, "Title": "Inductive Plasma Excitation Forcing Configuration on Reduction of Tip Distorted Inflow Effect on the Aerodynamic Stability of Axial Compressor Rotor", "Abstract": "This research delves into the mitigating impacts of dielectric barrier discharge (DBD) plasma excitation induced forcing orientation against the detrimental consequences of distinct radial tip distortions which in turn affect the axial compressor rotor performance and alters the flow structure at the tip region. Full annulus transient CFD simulation was utilized to evaluate the consequences of plasma actuation at distorted conditions with different blockage percentages. Beyond flow field and frequency analysis, the study further characterized rotor performance under different conditions by evaluating key performance metrics, including total pressure rise coefficient, stall margin variation, and span-wise rotor inlet velocity distribution. The injection of momentum caused by plasma actuators to the low-energy region behind the distortion screens proved to be effective on rotor aerodynamic stability facing radial tip distortion. In the case where 15 % of the inlet area was blocked, the stall margin varied from -8 % to -3.5 % with axial plasma actuators in action. However, the best configuration of plasma actuators for the enhancement of the stall margin and flow characteristics was identified to have opposite forcing direction with respect to the rotor rotational velocity. Additionally, these actuators suppressed frequencies caused by fluctuations in the rotor blade row tip leakage vortex, suggesting an improvement in the flow pattern within the rotor tip area.", "Keywords": "", "DOI": "10.1016/j.compfluid.2024.106433", "PubYear": 2024, "Volume": "284", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Combustion and Propulsion Research Laboratory, Faculty of Aerospace Engineering, K. N. Toosi University of Technology, 16765-3381 Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Combustion and Propulsion Research Laboratory, Faculty of Aerospace Engineering, K. N. Toosi University of Technology, 16765-3381 Tehran, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Aerodynamic Research Laboratory, Faculty of Aerospace Engineering, K. N. Toosi University of Technology, 16765-3381 Tehran, Iran"}], "References": [{"Title": "Numerical modeling of an inductively coupled plasma torch using OpenFOAM", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "104807", "JournalTitle": "Computers & Fluids"}, {"Title": "Flow Control around NACA0015 Airfoil Using a Dielectric Barrier Discharge Plasma Actuator over a Wide Range of the Reynolds Number", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "1", "Page": "43", "JournalTitle": "Actuators"}]}, {"ArticleId": 117622817, "Title": "Evaluation of SeeColors filters for color vision correction and comparative analysis with EnChroma glasses", "Abstract": "The purpose of this study was to evaluate the ability of SeeColors filters to enhance color vision test results using the electronic version of the Farnsworth D-15 (E-D15) and Ishihara tests on a Samsung TV, and to compare their effectiveness with EnChroma glasses. Sixty-six subjects with congenital red–green color vision deficiency were tested. For both protan and deutan groups, the confusion angle shifted to negative values, with SeeColors filters exhibiting a greater effect than EnChroma glasses. In the deutan group, the TES, S-index, and C-index of the E-D15 test decreased, with the SeeColors D30 filter having a more pronounced effect than EnChroma deutan glasses. In the protan group, while EnChroma protan glasses tended to decrease the TES, S-index, and C-index, the SeeColors P30 filter increased them. For both groups, TS and TN of the Ishihara tests improved, with the SeeColors D30 filter demonstrating a stronger effect than EnChroma deutan glasses. The study concluded that both the SeeColors D30 filter and EnChroma deutan glasses were beneficial for deutans, albeit the SeeColors D30 filter was superior. In protans, neither the SeeColors P30 filter nor EnChroma protan glasses showed significant effectiveness, but the SeeColors P30 filter did improve performance in the pseudoisochromatic task.", "Keywords": "Color vision deficiency; SeeColors filter; EnChroma glasses; Farnsworth D15 test; <PERSON><PERSON><PERSON> test", "DOI": "10.1016/j.displa.2024.102831", "PubYear": 2024, "Volume": "85", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Linping Campus, The Second Affiliated Hospital of Zhejiang University, School of Medicine, No. 369 Yingbin Rd, Linping District, Hangzhou 311100, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Linping Campus, The Second Affiliated Hospital of Zhejiang University, School of Medicine, No. 369 Yingbin Rd, Linping District, Hangzhou 311100, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Linping Campus, The Second Affiliated Hospital of Zhejiang University, School of Medicine, No. 369 Yingbin Rd, Linping District, Hangzhou 311100, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Linping Campus, The Second Affiliated Hospital of Zhejiang University, School of Medicine, No. 369 Yingbin Rd, Linping District, Hangzhou 311100, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Linping Campus, The Second Affiliated Hospital of Zhejiang University, School of Medicine, No. 369 Yingbin Rd, Linping District, Hangzhou 311100, China"}, {"AuthorId": 6, "Name": "Hai <PERSON>", "Affiliation": "TÜV Rheinland Building, No. 177, Lane 777, West Guangzhong Rd, Jingan District, Shanghai 200072, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TÜV Rheinland Building, No. 177, Lane 777, West Guangzhong Rd, Jingan District, Shanghai 200072, China;Corresponding author"}], "References": []}, {"ArticleId": 117622929, "Title": "Ultrasonic assisted electropolishing to reduce the surface roughness of laser powder bed fusion based additively manufactured copper heat exchanger components", "Abstract": "<p>As-built additively manufactured components for heat exchanger applications, suffer from high surface roughness. In this study, a novel ultrasonic-assisted electropolishing setup is designed and tested to reduce the internal surface roughness of a Laser Powder Bed Fusion (L-PBF) additively manufactured copper (GRCop-42) heat exchanger component. In the electropolishing setup, the surface of the as-built heat exchanger is used as the anode while a bulk copper rod is used as the cathode. An electropolishing fluid consisting of CuCl<sub>2</sub>, HCl, and SiC abrasive particles was used as electrolyte media, along with ultrasonication. The effect of chemical constituents, ultrasonic duration, and abrasive particles on surface roughness reduction was studied. It was observed that the ultrasonic agitation condition assisted with the abrasive particles was beneficial to reduce the surface roughness of the heat exchanger sample (over 50%) through acoustic streaming and scrubbing effect. An addition of 1% bio-based trihexyltetradecyl phosphonium saccharinate ionic liquid improved the corrosion properties for the copper surface. Overall, to reduce the roughness of L-PBF additively manufactured samples of complicated shapes with internal features, this process could be a viable technique, where mechanical polishing is not possible and internal surfaces are not accessible.</p>", "Keywords": "Abrasive; Copper; Heat-exchanger; Roughness; Additive manufacturing; Ultrasonication", "DOI": "10.1007/s00170-024-14399-6", "PubYear": 2024, "Volume": "134", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Nevada Reno, Reno, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Nevada Reno, Reno, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Nevada Reno, Reno, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of New Mexico, Albuquerque, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Nevada Reno, Reno, USA; Corresponding author."}], "References": []}, {"ArticleId": 117623025, "Title": "Ethical aspects of ChatGPT: An approach to discuss and evaluate key requirements from different ethical perspectives", "Abstract": "", "Keywords": "", "DOI": "10.1007/s43681-024-00571-x", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 79924, "JournalTitle": "AI and Ethics", "ISSN": "2730-5953", "EISSN": "2730-5961", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Embedding Values in Artificial Intelligence (AI) Systems", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "385", "JournalTitle": "Minds and Machines"}, {"Title": "Lessons learned from AI ethics principles for future actions", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "41", "JournalTitle": "AI and Ethics"}, {"Title": "Algorithmic injustice: a relational ethics approach", "Authors": "<PERSON><PERSON>hane", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "100205", "JournalTitle": "Patterns"}, {"Title": "Responsible innovation, anticipation and responsiveness: case studies of algorithms in decision support in justice and security, and an exploration of potential, unintended, undesirable, higher-order effects", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "4", "Page": "501", "JournalTitle": "AI and Ethics"}, {"Title": "Datasheets for datasets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "12", "Page": "86", "JournalTitle": "Communications of the ACM"}, {"Title": "Algorithmic fairness through group parities? The case of COMPAS-SAPMOC", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "38", "Issue": "2", "Page": "459", "JournalTitle": "AI & SOCIETY"}, {"Title": "Meaningful human control of drones: exploring human–machine teaming, informed by four different ethical perspectives", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "1", "Page": "281", "JournalTitle": "AI and Ethics"}, {"Title": "Moral transparency of and concerning algorithmic tools", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "2", "Page": "585", "JournalTitle": "AI and Ethics"}, {"Title": "Ethics as a Participatory and Iterative Process", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "5", "Page": "27", "JournalTitle": "Communications of the ACM"}, {"Title": "ChatGPT: More Than a “Weapon of Mass Deception” Ethical Challenges and Responses from the Human-Centered Artificial Intelligence (HCAI) Perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>B<PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "17", "Page": "4853", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 117623071, "Title": "TFformer: A time-frequency domain bidirectional sequence-level attention based transformer for interpretable long-term sequence forecasting", "Abstract": "Transformer methods have shown strong predictive performance in long-term time series prediction. However, its attention mechanism destroys temporal dependence and has quadratic complexity. This makes prediction processes difficult to interpret, limiting their application in tasks requiring interpretability. To address this issue, this paper proposes a highly interpretable long-term sequence forecasting model, TFformer. TFformer decomposes time series into low frequency trend component and high frequency period component by frequency decomposition, and forecasts them respectively. The periodic information in high-frequency component is enhanced with the sequential frequency attention, and then the temporal patterns of the two components are obtained by feature extraction. According to the period property in time domain, TFformer through periodic extension to predict the future period patterns using sequential periodic matching attention. Finally, the predicted future period pattern and the extracted trend pattern are reconstructed to future series. TFformer provides an interpretable forecasting process with low time complexity, as it retains temporal dependence using sequence-level attentions. TFformer achieves significant prediction performance in both univariate and multivariate forecasting across six datasets. Detailed experimental results and analyses verify the effectiveness and generalization of TFformer.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.110994", "PubYear": 2025, "Volume": "158", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Shandong University, Jinan 250101, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Shandong University, Jinan 250101, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Shandong University, Jinan 250101, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Shandong University, Jinan 250101, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Shandong University, Jinan 250101, China;Shandong Provincial Laboratory of Future Intelligence and Financial Engineering, Yantai 264005, China;Corresponding author at: School of Software, Shandong University, Jinan 250101, China"}], "References": [{"Title": "Estimation of the foetal heart rate baseline based on singular spectrum analysis and empirical mode decomposition", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "126", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "View-invariant action recognition via Unsupervised AttentioN Transfer (UANT)", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107807", "JournalTitle": "Pattern Recognition"}, {"Title": "Interpretable deep-learning models to help achieve the Sustainable Development Goals", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "11", "Page": "926", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Hierarchical electricity time series prediction with cluster analysis and sparse penalty", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "108555", "JournalTitle": "Pattern Recognition"}, {"Title": "Data augmentation for univariate time series forecasting with neural networks", "Authors": "Artemios<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109132", "JournalTitle": "Pattern Recognition"}, {"Title": "3D Medical image segmentation using parallel transformers", "Authors": "<PERSON><PERSON> Yan; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "109432", "JournalTitle": "Pattern Recognition"}, {"Title": "Dynamic graph structure learning for multivariate time series forecasting", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "109423", "JournalTitle": "Pattern Recognition"}, {"Title": "Asset correlation based deep reinforcement learning for the portfolio selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "221", "Issue": "", "Page": "119707", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 117623106, "Title": "UAV image object detection based on self-attention guidance and global feature fusion", "Abstract": "Unmanned aerial vehicle (UAV) image object detection has garnered considerable attentions in fields such as Intelligent transportation, urban management and agricultural monitoring. However, it suffers from key challenges of the deficiency in multi-scale feature extraction and the inaccuracy when processing complex scenes and small-sized targets in practical applications. To address this challenge, we propose a novel UAV image object detection network based on self-attention guidance and global feature fusion, named SGGF-Net. First, in order to optimizing feature extraction in global perspective and enhancing target localization precision, the global feature extraction module (GFEM) is introduced by exploiting the self-attention mechanism to capture and integrate long-range dependencies within images. Second, a normal distribution-based prior assigner (NDPA) is developed by measuring the resemblance between ground truth and the priors, which improves the precision of target position matching and thus handle the problem of inaccurate localization of small targets. Furthermore, we design an attention-guided ROI pooling module (ARPM) via a deep fusion strategy of multilevel features for optimizing the integration of multi-scale features and improving the quality of feature representation. Finally, experimental results demonstrate the effectiveness of the proposed SGGF-Net approach.", "Keywords": "", "DOI": "10.1016/j.imavis.2024.105262", "PubYear": 2024, "Volume": "151", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "Jing <PERSON>", "Affiliation": "School of Information Science and Technology, Shijiazhuang Tiedao University, Shijiazhuang 050043, Hebei, China;Hebei Key Laboratory of Electromagnetic Environmental Effects and Information Processing, Shijiazhuang 050043, Hebei, China;Shijiazhuang Key Laboratory of Artificial Intelligence, Shijiazhuang 050043, Hebei, China;Corresponding author"}, {"AuthorId": 2, "Name": "Haiyang Hu", "Affiliation": "School of Information Science and Technology, Shijiazhuang Tiedao University, Shijiazhuang 050043, Hebei, China;Hebei Key Laboratory of Electromagnetic Environmental Effects and Information Processing, Shijiazhuang 050043, Hebei, China;Shijiazhuang Key Laboratory of Artificial Intelligence, Shijiazhuang 050043, Hebei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Shijiazhuang Tiedao University, Shijiazhuang 050043, Hebei, China;Hebei Key Laboratory of Electromagnetic Environmental Effects and Information Processing, Shijiazhuang 050043, Hebei, China;Shijiazhuang Key Laboratory of Artificial Intelligence, Shijiazhuang 050043, Hebei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Shijiazhuang Tiedao University, Shijiazhuang 050043, Hebei, China;Hebei Key Laboratory of Electromagnetic Environmental Effects and Information Processing, Shijiazhuang 050043, Hebei, China;Shijiazhuang Key Laboratory of Artificial Intelligence, Shijiazhuang 050043, Hebei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Shijiazhuang Tiedao University, Shijiazhuang 050043, Hebei, China;Hebei Key Laboratory of Electromagnetic Environmental Effects and Information Processing, Shijiazhuang 050043, Hebei, China;Shijiazhuang Key Laboratory of Artificial Intelligence, Shijiazhuang 050043, Hebei, China"}], "References": [{"Title": "mSODANet: A network for multi-scale object detection in aerial images using hierarchical dilated convolutions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "108548", "JournalTitle": "Pattern Recognition"}, {"Title": "PS-YOLO: a small object detector based on efficient convolution and multi-scale feature fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "30", "Issue": "5", "Page": "1", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 117623152, "Title": "tsRNA modifications: An emerging layer of biological regulation in disease", "Abstract": "<p><b>BACKGROUND</b>:Transfer RNA (tRNA)-derived small RNA (tsRNA) represents an important and increasingly valued type of small non-coding RNA (sncRNA). The investigation of tRNA and tsRNA modification crosswalks has not only provided novel insights into the information and functions of tsRNA, but has also expanded the diversity and complexity of the tsRNA biological regulation network.</p><p><b>AIM OF REVIEW</b>:Comparing with other sncRNAs, tsRNA biogenesis show obvious correlation with RNA modifications from mature tRNA and harbor various tRNA modifications. In this review, we aim to present the current aspect of tsRNA modifications and that modified tsRNA shape different regulatory mechanisms in physiological and pathological processes.</p><p><b>KEY SCIENTIFIC CONCEPTS OF REVIEW</b>:Strategies for studying tsRNA mechanisms include its specific generation and functional effects induced by sequence/RNA modification/secondary structure. tsRNAs could harbor more than one tRNA modifications such as 5-methylcytosine (m<sup>5</sup>C), N1-methyladenosine (m<sup>1</sup>A), pseudouridine (Ψ) and N7-methylguanosine (m<sup>7</sup>G). This review consolidates the current knowledge of tRNA modification regulating tsRNA biogenesis, outlines the functional roles of various modified tsRNA and highlights their specific contributions in various disease pathogenesis. Therefore, the improvement of tsRNA modification detection technology and the introduction of experimental methods of tsRNA modification are conducive to further broadening the understanding of tsRNA function at the level of RNA modification.</p><p>Copyright © 2024. Production and hosting by Elsevier B.V.</p>", "Keywords": "Cancer;Epigenetic regulation;Modified tsRNA;RNA modification;tRNA-derived small RNA", "DOI": "10.1016/j.jare.2024.09.010", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Translational Medicine, The First Affiliated Hospital, Sun Yat-sen University, Guangzhou, Guangdong 510080, PR China."}, {"AuthorId": 2, "Name": "<PERSON>g <PERSON>", "Affiliation": "Department of Otolaryngology-Head & Neck Surgery, The First Affiliated Hospital, Sun Yat-sen University, Guangzhou 510220, PR China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Urology, The Second Affiliated Hospital of Guangzhou Medical University, Guangzhou 510220, PR China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Otolaryngology-Head & Neck Surgery, The First Affiliated Hospital, Sun Yat-sen University, Guangzhou 510220, PR China"}, {"AuthorId": 5, "Name": "Weidong Ji", "Affiliation": "Center for Translational Medicine, The First Affiliated Hospital, Sun Yat-sen University, Guangzhou, Guangdong 510080, PR China"}], "References": []}, {"ArticleId": 117623163, "Title": "Omnificence or Differentiation? An Empirical Study of Knowledge Structure and Career Development of IT Workers", "Abstract": "<p>Amid the growing importance of information technology (IT) in the business landscape, the pivotal role of IT knowledge on the demand side of the labor market, at both industry and firm levels, is well documented. However, the important labor supply side concerning IT workers has remained largely unknown. This raises challenges about how IT professionals should strategically cultivate their IT knowledge structures toward a sustainable, well-compensated career path. This paper bridges this gap by examining how different types of IT knowledge structures of IT workers impact their salaries and job security over time. We theorize, define, and operationalize two new metrics to characterize the knowledge structures of an IT worker. Knowledge omnificence measures the breadth of an IT worker’s own knowledge structure, whereas knowledge differentiation assesses the extent of difference between one’s knowledge set and those of their peers. By analyzing extensive career data of IT workers from 2000 to 2016, we demonstrated that, on average, a high level of IT knowledge differentiation or omnificence yields positive economic returns for IT workers. However, there is an intriguing twist: such a positive relationship is not monotonic. The most advantageous strategy is to acquire IT knowledge at moderate levels of knowledge omnificence and differentiation. Further, our results revealed another new twist: to increase salary potential or pursue a better position, one should aim for knowledge omnificence, whereas those valuing job security should aim for knowledge differentiation. This aligns with our theoretical rationale that utilizes a structured framework, integrating the dynamic capability framework and the boundaryless career theory. Besides, we found that both knowledge omnificence and differentiation reduced gender disparity in the labor market. In particular, females benefited more, with a 14.74% (or 1.38%) increase in salary, from having a one-unit increase in knowledge omnificence (or differentiation). This study holds critical managerial implications for IT workers, firms, and policymakers. It emphasizes the importance of strategic management of IT knowledge structure in enabling IT workers to thrive in the dynamic and competitive IT job market.</p><p>History: Manju Ahuja, Senior Editor; Yuliang Yao, Associate Editor.</p><p>Funding: This work was supported by the National Natural Science Foundation of China [Grant 72272003].</p><p>Supplemental Material: The online appendix is available at https://doi.org/10.1287/isre.2022.0634 .</p>", "Keywords": "IT knowledge structure; knowledge omnificence; knowledge differentiation; IT trends; knowledge dynamics; career development", "DOI": "10.1287/isre.2022.0634", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 11444, "JournalTitle": "Information Systems Research", "ISSN": "1047-7047", "EISSN": "1526-5536", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guanghua School of Management, Peking University, Beijing 100871, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> (Eric) <PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> School of Management, The University of Texas at Dallas, Dallas, Texas 75080"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Questrom School of Business, Boston University, Boston, Massachusetts 02215"}], "References": [{"Title": "Predicting Labor Market Competition: Leveraging Interfirm Network and Employee Skills", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "4", "Page": "1443", "JournalTitle": "Information Systems Research"}, {"Title": "Information Technology Skills and Labor Market Outcomes for Workers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "2", "Page": "437", "JournalTitle": "Information Systems Research"}, {"Title": "Task Characteristics and Incentives in Collaborative Problem Solving: Evidence from Three Field Experiments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> (<PERSON>) <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Information Systems Research"}]}, {"ArticleId": 117623232, "Title": "Sentiment Analysis of Student Texts Using the CNN-BiGRU-AT Model", "Abstract": "<p>For most current sentiment analysis models, it is difficult to capture the complex semantic and grammatical information in the text, and they are not fully applicable to the analysis of student sentiments. A novel student text sentiment analysis model using the convolutional neural network with the bidirectional gated recurrent unit and an attention mechanism, called CNN-BiGRU-AT model, is proposed. Firstly, the text is divided into multiple sentences, and the convolutional neural network (CNN) is used to extract n-gram information of different granularities from each sentence to construct a sentence-level feature representation. Then, the sentences are sequentially integrated through the bidirectional gated recurrent unit (BiGRU) to extract the contextual semantic information features of the text. Finally, an attention mechanism is added to the CNN-BiGRU model, and different learning weights are applied to the model by calculating the attention score. The top-down text features of “word-sentence-text” are input into the softmax classifier to realize sentiment classification. Based on the weibo_senti_100 k dataset, the proposed model is experimentally demonstrated. The results show that the accuracy rate and recall rate of its classification mostly exceed 0.9, and the F1 value is not lower than 0.8, which are better than the results of other models. The proposed model can provide a certain reference for the related students’ text sentiment analysis research.</p>", "Keywords": "", "DOI": "10.1155/2021/8405623", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Changshu Institute of Technology, Changshu, Jiangsu 215500, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Changshu Institute of Technology, Changshu, Jiangsu 215500, China"}, {"AuthorId": 3, "Name": "Zhengjiang Qian", "Affiliation": "School of Computer Science and Engineering, Changshu Institute of Technology, Changshu, Jiangsu 215500, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Changshu Institute of Technology, Changshu, Jiangsu 215500, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Changshu Institute of Technology, Changshu, Jiangsu 215500, China"}], "References": [{"Title": "Geospatial and Social Media Analytics for Emotion Analysis of Theme Park Visitors using Text Mining and GIS", "Authors": "Dr. <PERSON>;  Prof<PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "2", "Page": "100", "JournalTitle": "Journal of Information Technology and Digital World"}, {"Title": "Semi-supervised learning for facial expression-based emotion recognition in the continuous domain", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "37-38", "Page": "28169", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Investigating the Relationship Between Emotion Recognition Software and Usability Metrics", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "2", "Page": "139", "JournalTitle": "i-com"}, {"Title": "Attention-based label consistency for semi-supervised deep learning based image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "453", "Issue": "", "Page": "731", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 117623260, "Title": "A social network user behaviour data recommendation system based on fuzzy partition clustering", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2024.141355", "PubYear": 2024, "Volume": "74", "Issue": "1/2", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117623295, "Title": "The Study of the Strategic Consequences of a Scoring Model Disclosure", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0005117924080046", "PubYear": 2024, "Volume": "85", "Issue": "8", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "New Economic School, Moscow, Russia"}, {"AuthorId": 2, "Name": "M<PERSON> <PERSON><PERSON>", "Affiliation": "HSE University, Moscow, St. Petersburg, Russia"}], "References": [{"Title": "DGHNL: A new deep genetic hierarchical network of learners for prediction of credit scoring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "516", "Issue": "", "Page": "401", "JournalTitle": "Information Sciences"}, {"Title": "Incentive Compatibility and Strategy-Proofness of Mechanisms of Organizational Behavior Control: Retrospective, State of the Art, and Prospects of Theoretical Research", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "7", "Page": "1119", "JournalTitle": "Automation and Remote Control"}]}, {"ArticleId": 117623371, "Title": "Integrating YOLOv8 and CSPBottleneck based CNN for enhanced license plate character recognition", "Abstract": "<p>The paper introduces an integrated methodology for license plate character recognition, combining YOLOv8 for segmentation and a CSPBottleneck-based CNN classifier for character recognition. The proposed approach incorporates pre-processing techniques to enhance the recognition of partial plates and augmentation methods to address challenges arising from colour diversity. Performance analysis demonstrates YOLOv8’s high segmentation accuracy and fast processing time, complemented by precise character recognition and efficient processing by the CNN classifier. The integrated system achieves an overall accuracy of 99.02% with a total processing time of 9.9 ms, offering a robust solution for automated license plate recognition (ALPR) systems. The integrated approach presented in the paper holds promise for the practical implementation of ALPR technology and further development in the field of license plate recognition systems.</p>", "Keywords": "Computer vision; Optical character recognition; Deep learning; Artificial intelligence; ALPR", "DOI": "10.1007/s11554-024-01537-2", "PubYear": 2024, "Volume": "21", "Issue": "5", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, GJUS &T, Hisar, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, GJUS &T, Hisar, India"}], "References": [{"Title": "An automated license plate detection and recognition system based on wavelet decomposition and CNN", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "", "Page": "100040", "JournalTitle": "Array"}, {"Title": "Research on license plate location and recognition in complex environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "4", "Page": "823", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "Enhancement of license plate recognition performance using Xception with Mish activation function", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "11", "Page": "16793", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Enhanced licence plate detection using YOLO framework in challenging environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "1", "Issue": "1", "Page": "", "JournalTitle": "International Journal of Computational Vision and Robotics"}]}, {"ArticleId": 117623435, "Title": "Information scheduling method of big data platform based on ant colony algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2024.141353", "PubYear": 2024, "Volume": "74", "Issue": "1/2", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Tong", "Affiliation": ""}, {"AuthorId": 2, "Name": "Yanming Wan", "Affiliation": ""}], "References": []}, {"ArticleId": 117623440, "Title": "Fake content detection on benchmark dataset using various deep learning models", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCSE.2024.141341", "PubYear": 2024, "Volume": "27", "Issue": "5", "JournalId": 8309, "JournalTitle": "International Journal of Computational Science and Engineering", "ISSN": "1742-7185", "EISSN": "1742-7193", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117623445, "Title": "From Digital Art to Crypto Art: The Evolution of Art Brought by NFT", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2395088", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer and Information Science, University of Macau, Macau, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 4, "Name": "Zhenqing Gu", "Affiliation": "Guangzhou Academy of Fine Arts, Guangzhou Academy of Fine Arts, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "CRRC Zhuzhou Institute, Zhuzhou, China;Tengen Intelligence Institute, Zhuzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, City University of Hong Kong, Hong Kong, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Information Science, University of Macau, Macau, China"}], "References": [{"Title": "The influence of modern trends in digital art on the content of training in computer graphics and digital design", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Ukrainian Journal of Educational Studies and Information Technology"}, {"Title": "Community Impact on a Cryptocurrency: Twitter Comparison Example Between Dogecoin and Litecoin", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "5", "Issue": "", "Page": "6", "JournalTitle": "Frontiers in Blockchain"}, {"Title": "In the Age of Collaboration, the Computer-Aided Design Ecosystem is Behind: An Interview Study of Distributed CAD Practice", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "\"Centralized or Decentralized?\": Concerns and Value Judgments of Stakeholders in the Non-Fungible Tokens (NFTs) Market", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 117623549, "Title": "Splitting Plane Graphs to Outerplanarity", "Abstract": "Vertex splitting replaces a vertex by two copies and partitions its incident edges amongst the copies. This problem has been studied as a graph editing operation to achieve desired properties with as few splits as possible, most often planarity, for which the problem is NP-hard.Here we study how to minimize the number of splits to turn a plane graph into an outerplane one. We tackle this problem by establishing a direct connection between splitting a plane graph to outerplanarity, finding a connected face cover, and finding a feedback vertex set in its dual. We prove NP-completeness for plane biconnected graphs, while we show that a polynomial-time algorithm exists for maximal planar graphs. Additionally, we show upper and lower bounds for certain families of maximal planar graphs. Finally, we provide a SAT formulation for the problem, and evaluate it on a small benchmark.", "Keywords": "vertex splitting;outerplanarity;feedback vertex set", "DOI": "10.7155/jgaa.v28i3.2970", "PubYear": 2024, "Volume": "28", "Issue": "3", "JournalId": 16217, "JournalTitle": "Journal of Graph Algorithms and Applications", "ISSN": "", "EISSN": "1526-1719", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TU Wien"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "TU Wien"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TU Wien"}], "References": []}, {"ArticleId": 117623686, "Title": "Perpetual innovative products from circular factories for sustainable production", "Abstract": "", "Keywords": "", "DOI": "10.1515/auto-2024-0107", "PubYear": 2024, "Volume": "72", "Issue": "9", "JournalId": 16752, "JournalTitle": "at - Automatisierungstechnik", "ISSN": "0178-2312", "EISSN": "2196-677X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology (KIT), wbk Institute of Production Science , Karlsruhe , Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology (KIT), wbk Institute of Production Science , Karlsruhe , Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology (KIT), Institute for Automation and Applied Informatics (IAI) , Eggenstein-Leopoldshafen , Germany"}], "References": []}, {"ArticleId": 117623826, "Title": "Soft Upper‐Limb Wearable Robotic Devices: Technology and Applications", "Abstract": "One of the practical applications in the field of soft robotics involves the development of soft robotic wearable devices. These devices make use of their intrinsically compliant structures to interact safely and harmoniously with the human body. While soft wearable robots demonstrate their utility in lower‐limb applications for locomotion, the upper‐limb domain offers significant prospects in a wide range of applications that soft robotic technology can address. In this review, the current state of technology in the field of soft wearable upper limbs is systematically analyzed and categorized. Categorizations are made based on their applications in rehabilitation, activities of daily living support, and human augmentation. Furthermore, in this study, also contemporary technological aspects, encompassing sensing technology and control systems, are explored. Despite exciting potential in this domain, several limitations from existing devices inherently impede widespread adoption and thus hinder further progress in the field. In this study, also an overview of the different facets of the domain is provided and key considerations for the advancement of soft wearable robotic devices intended for upper‐limb applications are prescribed.", "Keywords": "", "DOI": "10.1002/aisy.*********", "PubYear": 2024, "Volume": "6", "Issue": "12", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Biomedical Engineering Faculty of Engineering and Tyree Institute of Health Engineering (IHealthE) UNSW Sydney  Sydney NSW 2052 Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Biomedical Engineering Faculty of Engineering and Tyree Institute of Health Engineering (IHealthE) UNSW Sydney  Sydney NSW 2052 Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Graduate School of Biomedical Engineering Faculty of Engineering and Tyree Institute of Health Engineering (IHealthE) UNSW Sydney  Sydney NSW 2052 Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Biomedical Engineering Faculty of Engineering and Tyree Institute of Health Engineering (IHealthE) UNSW Sydney  Sydney NSW 2052 Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Graduate School of Biomedical Engineering Faculty of Engineering and Tyree Institute of Health Engineering (IHealthE) UNSW Sydney  Sydney NSW 2052 Australia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Biomedical Engineering Faculty of Engineering and Tyree Institute of Health Engineering (IHealthE) UNSW Sydney  Sydney NSW 2052 Australia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Biomedical Engineering Faculty of Engineering and Tyree Institute of Health Engineering (IHealthE) UNSW Sydney  Sydney NSW 2052 Australia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Biomedical Engineering Faculty of Engineering and Tyree Institute of Health Engineering (IHealthE) UNSW Sydney  Sydney NSW 2052 Australia"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Graduate School of Biomedical Engineering Faculty of Engineering and Tyree Institute of Health Engineering (IHealthE) UNSW Sydney  Sydney NSW 2052 Australia"}, {"AuthorId": 10, "Name": "Thanh Nho Do", "Affiliation": "Graduate School of Biomedical Engineering Faculty of Engineering and Tyree Institute of Health Engineering (IHealthE) UNSW Sydney  Sydney NSW 2052 Australia"}], "References": [{"Title": "The impact of the input interface in a virtual environment: the Vive controller and the Myo armband", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "483", "JournalTitle": "Virtual Reality"}, {"Title": "Fluidic Fabric Muscle Sheets for Wearable and Soft Robotics", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "2", "Page": "179", "JournalTitle": "Soft Robotics"}, {"Title": "Cable driven exoskeleton for upper-limb rehabilitation: A design review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "126", "Issue": "", "Page": "103445", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "A voice activated bi-articular exosuit for upper limb assistance during lifting tasks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "66", "Issue": "", "Page": "101995", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Soft Actuators for Soft Robotic Applications: A Review", "Authors": "<PERSON><PERSON>k <PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>had <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "10", "Page": "2000128", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "A review of soft wearable robots that provide active assistance: Trends, common actuation methods, fabrication, and applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "e3", "JournalTitle": "Wearable Technologies"}, {"Title": "An Exoneuromusculoskeleton for Self-Help Upper Limb Rehabilitation After Stroke", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "1", "Page": "14", "JournalTitle": "Soft Robotics"}, {"Title": "An Assistive Soft Wrist Exosuit for Flexion Movements With an Ergonomic Reinforced Glove", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "182", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Remote Actuation Systems for Fully Wearable Assistive Devices: Requirements, Selection, and Optimization for Out-of-the-Lab Application of a Hand Exoskeleton", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "187", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "A review: A Comprehensive Review of Soft and Rigid Wearable Rehabilitation and Assistive Devices with a Focus on the Shoulder Joint", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "A systematic mapping study of robotics in human care", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "144", "Issue": "", "Page": "103833", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "A survey on soft lower limb cable-driven wearable robots without rigid links and joints", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "144", "Issue": "", "Page": "103846", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Review of Dielectric Elastomer Actuators and Their Applications in Soft Robots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "10", "Page": "2000282", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Twisting and Braiding Fluid-Driven Soft Artificial Muscle Fibers for Robotic Applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "4", "Page": "820", "JournalTitle": "Soft Robotics"}, {"Title": "Soft Actuators and Robotic Devices for Rehabilitation and Assistance", "Authors": "<PERSON> Pan; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "4", "Page": "2100140", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "An industrial exoskeleton user acceptance framework based on a literature review of empirical studies", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "100", "Issue": "", "Page": "103615", "JournalTitle": "Applied Ergonomics"}, {"Title": "A systematic literature review of evidence for the use of assistive exoskeletons in defence and security use cases", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "1", "Page": "61", "JournalTitle": "Ergonomics"}, {"Title": "Effects of Upper-Limb Exoskeletons Designed for Use in the Working Environment—A Literature Review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "", "Page": "82", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "A textile exomuscle that assists the shoulder during functional movements for everyday life", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "6", "Page": "574", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Active textile: woven-cloth-like mechanisms consist of thin McKibben actuators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "7", "Page": "480", "JournalTitle": "Advanced Robotics"}, {"Title": "Soft Wearable Rehabilitation Robots with Artificial Muscles based on Smart Materials: A Review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "4", "Page": "2200159", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Fabrication, nonlinear modeling, and control of woven hydraulic artificial muscles for wearable applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "360", "Issue": "", "Page": "114555", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Robotic Cardiac Compression Device Using Artificial Muscle Filaments for the Treatment of Heart Failure", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "6", "Issue": "3", "Page": "2300464", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Shape Programmable and Multifunctional Soft Textile Muscles for Wearable and Soft Robotics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "6", "Issue": "9", "Page": "2300875", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "A portable inflatable soft wearable robot to assist the shoulder during industrial work", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "9", "Issue": "91", "Page": "eadi2377", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 117623888, "Title": "A facile method to fabricate auxetic polymer foams", "Abstract": "<p>This paper presents a new efficient method for manufacturing auxetic foams, a subcategory of metamaterials with intriguing mechanical properties. Unlike previous methods that require two steps involving heating or the use of a chemical solvent, the present method involves compressing the foam during the manufacturing process after cells have been formed in the die, but while the material remains soft. This one-step process is more time-efficient, energy-efficient, and flexible; it also requires fewer facilities and materials. After the manufacturing process, various mechanical properties of the auxetic foams were evaluated by compression tests (energy absorption, mean force, and maximum force) and indentation tests (stiffness, absorbed energy, and hysteresis energy). The results confirmed that the auxetic foams exhibited superior behavior compared with conventional foam at the same density. To further investigate the foam microstructures and deformation mechanisms, in situ compression tests were conducted; the macro behaviors of the foams were explained based on these observations. Overall, this paper presents a promising approach for the manufacturing of auxetic foams with improved mechanical properties that can be used in applications typically dominated by conventional foams.</p>", "Keywords": "", "DOI": "10.1177/1045389X241264849", "PubYear": 2024, "Volume": "35", "Issue": "17", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Isfahan University of Technology, Isfahan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Isfahan University of Technology, Isfahan, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Isfahan University of Technology, Isfahan, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Isfahan University of Technology, Isfahan, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>g-<PERSON><PERSON><PERSON> Yu", "Affiliation": "Department of Materials Science and Engineering, Research Institute of Advanced Materials, Seoul National University, Gwanak-gu, Seoul, Korea"}], "References": []}, {"ArticleId": 117623917, "Title": "Issue Information", "Abstract": "<p>No abstract is available for this article.</p>", "Keywords": "", "DOI": "10.1002/jsid.1245", "PubYear": 2024, "Volume": "32", "Issue": "9", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [], "References": []}, {"ArticleId": 117624039, "Title": "Beta-CoRM: A Bayesian Approach for n-gram Profiles Analysis", "Abstract": "n -gram profiles have been successfully and widely used to analyse long sequences of potentially differing lengths for clustering or classification. Mainly, machine learning algorithms have been used for this purpose but, despite their predictive performance, these methods cannot discover hidden structures or provide a full probabilistic representation of the data. A novel class of Bayesian generative models designed for n -gram profiles used as binary attributes have been designed to address this. The flexibility of the proposed modelling allows to consider a straightforward approach to feature selection in the generative model. Furthermore, a slice sampling algorithm is derived for a fast inferential procedure, which is applied to synthetic and real data scenarios and shows that feature selection can improve classification accuracy.", "Keywords": "Bayesian statistics; Cyber security; Feature selection; Labelled data; n -Grams", "DOI": "10.1016/j.csda.2024.108056", "PubYear": 2025, "Volume": "202", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Sciences, UNAM, Mexico City, Mexico;Department of Probability and Statistics, IIMAS, UNAM, Mexico City, Mexico;Corresponding author at: Department of Mathematics, Faculty of Sciences, UNAM, Escolar 3000, Mexico City, 04510, Mexico"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Statistical Science, University College London, London, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Natural and Applied Sciences, Duke Kunshan University, Kunshan, China;School of Mathematics, Statistics and Physics, Newcastle University, Newcastle, UK"}], "References": []}, {"ArticleId": 117624062, "Title": "Multihead Attention U‐Net for Magnetic Particle Imaging–Computed Tomography Image Segmentation", "Abstract": "Magnetic particle imaging (MPI) is an emerging noninvasive molecular imaging modality with high sensitivity and specificity, exceptional linear quantitative ability, and potential for successful applications in clinical settings. Computed tomography (CT) is typically combined with the MPI image to obtain more anatomical information. Herein, a deep learning‐based approach for MPI‐CT image segmentation is presented. The dataset utilized in training the proposed deep learning model is obtained from a transgenic mouse model of breast cancer following administration of indocyanine green (ICG)‐conjugated superparamagnetic iron oxide nanoworms (NWs‐ICG) as the tracer. The NWs‐ICG particles progressively accumulate in tumors due to the enhanced permeability and retention (EPR) effect. The proposed deep learning model exploits the advantages of the multihead attention mechanism and the U‐Net model to perform segmentation on the MPI‐CT images, showing superb results. In addition, the model is characterized with a different number of attention heads to explore the optimal number for our custom MPI‐CT dataset.", "Keywords": "", "DOI": "10.1002/aisy.202400007", "PubYear": 2024, "Volume": "6", "Issue": "10", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Michigan State University  East Lansing Michigan 48824 USA;Institute for Quantitative Health Science and Engineering Michigan State University  East Lansing Michigan 48824 USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Michigan State University  East Lansing Michigan 48824 USA;Institute for Quantitative Health Science and Engineering Michigan State University  East Lansing Michigan 48824 USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Michigan State University  East Lansing Michigan 48824 USA;Institute for Quantitative Health Science and Engineering Michigan State University  East Lansing Michigan 48824 USA"}, {"AuthorId": 4, "Name": "Chia<PERSON>", "Affiliation": "Institute for Quantitative Health Science and Engineering Michigan State University  East Lansing Michigan 48824 USA;Department of Chemistry Michigan State University  East Lansing Michigan 48824 USA"}, {"AuthorId": 5, "Name": "Cheng<PERSON><PERSON>", "Affiliation": "Institute for Quantitative Health Science and Engineering Michigan State University  East Lansing Michigan 48824 USA;Department of Biomedical Engineering Michigan State University  East Lansing Michigan 48824 USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pathobiology and Diagnostic Investigation College of Veterinary Medicine Michigan State University  East Lansing Michigan 48824 USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Periodontics and Oral Medicine University of Michigan  Ann Arbor Michigan 48109 USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Departments of Radiology and Biomedical Engineering University of Michigan  Ann Arbor Michigan 481054 USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Surgery Michigan State University  East Lansing Michigan 48823 USA"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Quantitative Health Science and Engineering Michigan State University  East Lansing Michigan 48824 USA;Department of Chemistry Michigan State University  East Lansing Michigan 48824 USA;Department of Biomedical Engineering Michigan State University  East Lansing Michigan 48824 USA"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Quantitative Health Science and Engineering Michigan State University  East Lansing Michigan 48824 USA;Department of Biomedical Engineering School of Engineering King <PERSON>'s Institute of Technology Ladkrabang  Bangkok 10520 Thailand"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Michigan State University  East Lansing Michigan 48824 USA;Institute for Quantitative Health Science and Engineering Michigan State University  East Lansing Michigan 48824 USA;Department of Biomedical Engineering Michigan State University  East Lansing Michigan 48824 USA"}], "References": [{"Title": "A review on the attention mechanism of deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "48", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 117624116, "Title": "Comparative Study Between the OSI Model and the TCP/IP Model: Architecture and Protocols in Computer Networking Systems", "Abstract": "<p>In the world of computing and networking, it is critical to understand the theoretical framework that underlies data communication to ensure the best performance and compatibility between various systems. Both the Open Systems Interconnection (OSI) and Transmission Control Protocol/Internet Protocol (TCP/IP) models offer a comprehensive perspective on how the entire communication operation is separated into various layers, with each layer assigned particular roles and functions. This paper compares the OSI and TCP/IP models in terms of the functionalities and responsibilities of the various layers in each model, with an emphasis on how these layers work together to ensure effective and robust network communications. The paper covers encapsulating and decapsulating processes, managing sessions, and ensuring reliable data transmission. The TCP/IP model focuses on practical application with four main layers, while the OSI model has seven detailed layers that provide a theoretical and structured approach. The goal of both models is reliable and standardized communication, which in turn promotes interoperability in a variety of network environments.</p>", "Keywords": "", "DOI": "10.18535/ijecs/v13i08.4880", "PubYear": 2024, "Volume": "13", "Issue": "8", "JournalId": 17925, "JournalTitle": "International Journal Of Engineering And Computer Science", "ISSN": "", "EISSN": "2319-7242", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117624291, "Title": "Enhancing Flight Delay Predictions Using Network Centrality Measures", "Abstract": "<p>Accurately predicting flight delays remains a significant challenge in the aviation industry due to the complexity and interconnectivity of its operations. The traditional prediction methods often rely on meteorological conditions, such as temperature, humidity, and dew point, as well as flight-specific data like departure and arrival times. However, these predictors frequently fail to capture the nuanced dynamics that lead to delays. This paper introduces network centrality measures as novel predictors to enhance the binary classification of flight arrival delays. Additionally, it emphasizes the use of tree-based ensemble models, specifically random forest, gradient boosting, and CatBoost, which are recognized for their superior ability to model complex relationships compared to single classifiers. Empirical testing shows that incorporating centrality measures improves the models’ average performance, with random forest being the most effective, achieving an accuracy rate of 86.2%, surpassing the baseline by 1.7%.</p>", "Keywords": "", "DOI": "10.3390/info15090559", "PubYear": 2024, "Volume": "15", "Issue": "9", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Georgia Southern University, Statesboro, GA 30458, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Georgia Southern University, Statesboro, GA 30458, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Georgia Southern University, Statesboro, GA 30458, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Georgia Southern University, Statesboro, GA 30458, USA"}], "References": [{"Title": "A CNN-LSTM framework for flight delay prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "227", "Issue": "", "Page": "120287", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 117624304, "Title": "Enhancing Literature Review Efficiency: A Case Study on Using Fine-Tuned BERT for Classifying Focused Ultrasound-Related Articles", "Abstract": "<p>Over the past decade, focused ultrasound (FUS) has emerged as a promising therapeutic modality for various medical conditions. However, the exponential growth in the published literature on FUS therapies has made the literature review process increasingly time-consuming, inefficient, and error-prone. Machine learning approaches offer a promising solution to address these challenges. Therefore, the purpose of our study is to (1) explore and compare machine learning techniques for the text classification of scientific abstracts, and (2) integrate these machine learning techniques into the conventional literature review process. A classified dataset of 3588 scientific abstracts related and unrelated to FUS therapies sourced from the PubMed database was used to train various traditional machine learning and deep learning models. The fine-tuned Bio-ClinicalBERT (Bidirectional Encoder Representations from Transformers) model, which we named FusBERT, had comparatively optimal performance metrics with an accuracy of 0.91, a precision of 0.85, a recall of 0.99, and an F1 of 0.91. FusBERT was then successfully integrated into the literature review process. Ultimately, the integration of this model into the literature review pipeline will reduce the number of irrelevant manuscripts that the clinical team must screen, facilitating efficient access to emerging findings in the field.</p>", "Keywords": "", "DOI": "10.3390/ai5030081", "PubYear": 2024, "Volume": "5", "Issue": "3", "JournalId": 69255, "JournalTitle": "AI", "ISSN": "", "EISSN": "2673-2688", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science, University of Virginia, Charlottesville, VA 22903, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Focused Ultrasound Foundation, Charlottesville, VA 22903, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Data Science, University of Virginia, Charlottesville, VA 22903, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science, University of Virginia, Charlottesville, VA 22903, USA"}, {"AuthorId": 5, "Name": "<PERSON> <PERSON>", "Affiliation": "School of Data Science, University of Virginia, Charlottesville, VA 22903, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Focused Ultrasound Foundation, Charlottesville, VA 22903, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Focused Ultrasound Foundation, Charlottesville, VA 22903, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Focused Ultrasound Foundation, Charlottesville, VA 22903, USA"}], "References": []}, {"ArticleId": 117624308, "Title": "Technostress or Reaction to Techno-Stressors? Validation of Bilingual Techno-Stressors Index (TSI-II) and a Second-Order Formative Model of Techno-distress Among Canadian Legal Professionals", "Abstract": "Technostress is a phenomenon that needs to be seen as a process rather than a result. This requires the adaptation of measurement tools accordingly. Legal professionals are particularly exposed to technostress. This paper presents the validation of the TSI-II, an updated and bilingual version of the Techno-Stressors Index (TSI). This updated instrument was tested (French-n = 35; English-n = 30) and then retested (Overall-n = 4482; FR-n<sub>1</sub> = 544; ENG-n<sub>2</sub> = 3938) in both languages among Canadian legal professionals. Using the TSI-II, this paper proposes a second-order formative model of techno-distress, including seven techno-stressors, which captures the recent developments associated with the evolution of the technostress literature. Following the best practices for scale development, TSI-II presents excellent properties and is a good predictor of perceived stress among legal professionals. This validation aligns with developments in technostress literature, namely, the conceptual evolution of techno-distress as a component of the technostress process.", "Keywords": "Techno-stressors; Technostress; Techno-distress; Bilingual measurement scale; Second-order formative model", "DOI": "10.1016/j.chbr.2024.100485", "PubYear": 2024, "Volume": "16", "Issue": "", "JournalId": 75723, "JournalTitle": "Computers in Human Behavior Reports", "ISSN": "2451-9588", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management and Human Resource Management, Université de Sherbrooke, J1K 2R1, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management and Human Resource Management, Université de Sherbrooke, J1K 2R1, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of SIMQG, Université de Sherbrooke, J1K 2R1, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management and Human Resource Management, Université de Sherbrooke, J1K 2R1, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management and Human Resource Management, Université de Sherbrooke, J1K 2R1, Canada"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of SIMQG, Université de Sherbrooke, J1K 2R1, Canada"}], "References": [{"Title": "New Techno-Stressors Among Knowledge Professionals: The Contribution of Artificial Intelligence and Websites that Misinform Clients", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "136", "JournalTitle": "International Journal of Electronic Commerce"}, {"Title": "Knowledge coordination via digital artefacts in highly dispersed teams", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "3", "Page": "520", "JournalTitle": "Information Systems Journal"}, {"Title": "Can AI really help? The double-edged sword effect of AI assistant on employees’ innovation behavior", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "150", "Issue": "", "Page": "107987", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Techno‐eustress creators: Conceptualization and empirical validation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "34", "Issue": "6", "Page": "2097", "JournalTitle": "Information Systems Journal"}]}, {"ArticleId": 117624389, "Title": "Novel interpretable and robust web-based AI platform for phishing email detection", "Abstract": "Phishing emails continue to pose a significant threat, causing financial losses and security breaches. This study addresses limitations in existing research, such as reliance on proprietary datasets and lack of real-world application, by proposing a high-performance machine learning model for email classification. Utilizing a comprehensive and largest available public dataset, the model achieves a f1 score of 0.99 and is designed for deployment within relevant applications. Additionally, Explainable AI (XAI) is integrated to enhance user trust. This research offers a practical and highly accurate solution, contributing to the fight against phishing by empowering users with a real-time web-based application for phishing email detection.", "Keywords": "Phishing emails; Machine learning model; Email classification; Dataset; Explainable AI; User trust; Web-based application", "DOI": "10.1016/j.compeleceng.2024.109625", "PubYear": 2024, "Volume": "120", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing Science, AFG College with the University of Aberdeen, Doha, Qatar"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computing Science, AFG College with the University of Aberdeen, Doha, Qatar"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Liberal Arts Bangladesh, Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Liberal Arts Bangladesh, Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, College of Engineering, Qatar University, Qatar"}, {"AuthorId": 6, "Name": "SM Ashfaq <PERSON>", "Affiliation": "Qatar Emiri Naval Forces, Gulf Arabian, PO BOX 2237, Doha, Qatar;Corresponding author at: Qatar Emiri Naval Forces, Gulf Arabian, POBOX 2237 Doha, Qatar"}], "References": [{"Title": "A novel hybrid approach of SVM combined with NLP and probabilistic neural network for email phishing", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "486", "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)"}, {"Title": "Applicability of machine learning in spam and phishing email filtering: review and approaches", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "7", "Page": "5019", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Spam Email Detection Using Deep Learning Techniques", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "853", "JournalTitle": "Procedia Computer Science"}, {"Title": "A lifelong spam emails classification model", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "20", "Issue": "1/2", "Page": "35", "JournalTitle": "Applied Computing and Informatics"}, {"Title": "Applying machine learning and natural language processing to detect phishing email", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "102414", "JournalTitle": "Computers & Security"}, {"Title": "Enterprise Credential Spear-phishing attack detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "94", "Issue": "", "Page": "107363", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Modeling Hybrid Feature-Based Phishing Websites Detection Using Machine Learning Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "1", "Page": "217", "JournalTitle": "Annals of Data Science"}, {"Title": "A review of spam email detection: analysis of spammer strategies and the dataset shift problem", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "2", "Page": "1145", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Highly accurate phishing URL detection based on machine learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "7", "Page": "9233", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Phish-Sight: a new approach for phishing detection using dominant colors on web pages and machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "4", "Page": "881", "JournalTitle": "International Journal of Information Security"}, {"Title": "Real-time phishing detection using deep learning methods by extensions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "14", "Issue": "3", "Page": "3021", "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)"}, {"Title": "An improved transformer‐based model for detecting phishing, spam and ham emails: A large language model approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "7", "Issue": "5", "Page": "e402", "JournalTitle": "Security and Privacy"}]}, {"ArticleId": 117624477, "Title": "Learning human actions from complex manipulation tasks and their transfer to robots in the circular factory", "Abstract": "<p>Process automation is essential to establish an economically viable circular factory in high-wage locations. This involves using autonomous production technologies, such as robots, to disassemble, reprocess, and reassemble used products with unknown conditions into the original or a new generation of products. This is a complex and highly dynamic issue that involves a high degree of uncertainty. To adapt robots to these conditions, learning from humans is necessary. Humans are the most flexible resource in the circular factory and they can adapt their knowledge and skills to new tasks and changing conditions. This paper presents an interdisciplinary research framework for learning human action knowledge from complex manipulation tasks through human observation and demonstration. The acquired knowledge will be described in a machine-executable form and will be transferred to industrial automation execution by robots in a circular factory. There are two primary research objectives. First, we investigate the multi-modal capture of human behavior and the description of human action knowledge. Second, the reproduction and generalization of learned actions, such as disassembly and assembly actions on robots is studied.</p>", "Keywords": "", "DOI": "10.1515/auto-2024-0008", "PubYear": 2024, "Volume": "72", "Issue": "9", "JournalId": 16752, "JournalTitle": "at - Automatisierungstechnik", "ISSN": "0178-2312", "EISSN": "2196-677X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute of Human and Industrial Engineering , Engler-Bunte-Ring 4, 76131 Karlsruhe , Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute of Human and Industrial Engineering , Engler-Bunte-Ring 4, 76131 Karlsruhe , Germany"}, {"AuthorId": 3, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute for Anthropomatics and Robotics, High Performance Humanoid Technologies , Adenauerring 12, 76131 Karlsruhe , Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute of Industrial Information Technology , Hertzstraße 16, 76187 Karlsruhe , Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute for Anthropomatics and Robotics, Computer Vision for Human-Computer Interaction Lab , Adenauerring 10 , 76131 Karlsruhe , Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "University of Stuttgart, Institute for Artificial Intelligence , Universitätsstraße 32, 70569 Stuttgart , Germany"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute for Anthropomatics and Robotics, Computer Vision for Human-Computer Interaction Lab , Adenauerring 10 , 76131 Karlsruhe , Germany"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute for Anthropomatics and Robotics, Autonomous Learning Robots Lab , Engler-Bunte-Ring 8, 76131 Karlsruhe , Germany"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute of Industrial Information Technology , Hertzstraße 16, 76187 Karlsruhe , Germany"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute for Anthropomatics and Robotics, High Performance Humanoid Technologies , Adenauerring 12, 76131 Karlsruhe , Germany"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute of Human and Industrial Engineering , Engler-Bunte-Ring 4, 76131 Karlsruhe , Germany"}], "References": [{"Title": "NeRF", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "1", "Page": "99", "JournalTitle": "Communications of the ACM"}, {"Title": "An ontology for remanufacturing systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "6", "Page": "534", "JournalTitle": "at - Automatisierungstechnik"}, {"Title": "Erfassung und Interpretation menschlicher Handlungen für die Programmierung von Robotern in der Produktion", "Authors": "<PERSON> <PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "6", "Page": "517", "JournalTitle": "at - Automatisierungstechnik"}, {"Title": "Human-centred assembly and disassembly systems: a survey on technologies, ergonomic, productivity and optimisation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "10", "Page": "1722", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 117624749, "Title": "QBF Merge Resolution is powerful but unnatural", "Abstract": "<p>The Merge Resolution proof system (M-Res) for QBFs, proposed by <PERSON><PERSON><PERSON><PERSON> et al. in 2019, explicitly builds partial strategies inside refutations. The original motivation for this approach was to overcome the limitations encountered in long-distance Q-Resolution proof system (LD-Q-Res), where the syntactic side-conditions, while prohibiting all unsound resolutions, also end up prohibiting some sound resolutions. However, while the advantage of M-Res over many other resolution-based QBF proof systems was already demonstrated, a comparison with LD-Q-Res itself had remained open. In this paper, we settle this question. We show that M-Res has an exponential advantage over not only LD-Q-Res, but even over LQU$^+$-Res and IRM, the most powerful among currently known resolution-based QBF proof systems. Combining this with results from <PERSON><PERSON><PERSON><PERSON> et al. 2020, we conclude that M-Res is incomparable with LQU-Res and LQU$^+$-Res. Our proof method reveals two additional and curious features about M-Res: (i) M-Res is not closed under restrictions, and is hence not a natural proof system, and (ii) weakening axiom clauses with existential variables provably yields an exponential advantage over M-Res without weakening. We further show that in the context of regular derivations, weakening axiom clauses with universal variables provably yields an exponential advantage over M-Res without weakening. These results suggest that M-Res is better used with weakening, though whether M-Res with weakening is closed under restrictions remains open. We note that even with weakening, M-Res continues to be simulated by eFrege $+$ $\\forall$red (the simulation of ordinary M-Res was shown recently by Chew and Slivovsky).</p>", "Keywords": "Computer Science - Computational Complexity;Computer Science - Logic in Computer Science", "DOI": "10.46298/lmcs-20(3:22)2024", "PubYear": 2024, "Volume": "20, Issue 3", "Issue": "", "JournalId": 10075, "JournalTitle": "Logical Methods in Computer Science", "ISSN": "", "EISSN": "1860-5974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117624759, "Title": "Three-dimensional mesoscopic investigation of directional coalescence of two droplets impacting on a wall with wettability difference", "Abstract": "In this work, a three-dimensional (3D) nonorthogonal pseudopotential lattice Boltzmann method (LBM) was proposed to investigate the coalescence dynamics of two droplets impacting on a wall with wettability difference. The influences of the wettability difference, Weber number, offset distance on the low-wettability side on the coalescence dynamics and the contact-line evolution processes were systematically examined. Both symmetric and asymmetric distributions of the droplet-coalescence behaviors were considered. Our findings reveal that the wettability difference has a significant influence on the asymmetric-retracting and wetting-equilibrium stages, identifying three modes: pin-slip, slip and no-rebound, and slip and rebound. The rebound time is dominated by the high-wettability wall. At a larger Weber number, droplets exhibit a large retracting velocity, which results in increased pumping velocity and earlier rebound time. In addition, a dramatic retraction of the three-phase contact line (TPCL) on the low-wettability wall is observed, leading to the detachment of the liquid bridge from the low-wettability wall, and the formation of a cavity. With increasing offset distance on the low-wettability wall, three different evolution modes are found: coalescence-rebound, coalescence-separation, and non-coalescence. A power function relationship is reported between the Weber number We and the offset distance L * both on the high-wettability wall and low-wettability wall for three modes of coalescence behavior with We ∼ L * α . The value of the exponent α ranges from 4.6 to 7.4. This study showcases the effectiveness of the 3D nonorthogonal pseudopotential LBM in predicting the complex interface phenomena and characteristics of the multiphase flow structures under investigation.", "Keywords": "", "DOI": "10.1016/j.compfluid.2024.106423", "PubYear": 2024, "Volume": "284", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "Pengcheng Zhu", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, Sichuan University, 610065 Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, Sichuan University, 610065 Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, Sichuan University, 610065 Chengdu, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, Sichuan University, 610065 Chengdu, China"}], "References": [{"Title": "Droplet impact on nano-textured bumps: Topology effects", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "218", "Issue": "", "Page": "104844", "JournalTitle": "Computers & Fluids"}, {"Title": "Droplet spreading dynamics on hydrophobic textured surfaces: A lattice Boltzmann study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "231", "Issue": "", "Page": "105063", "JournalTitle": "Computers & Fluids"}, {"Title": "Non-condensable gas bubble dissolution with a modified tunable surface tension multicomponent lattice <PERSON><PERSON><PERSON> model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "233", "Issue": "", "Page": "105252", "JournalTitle": "Computers & Fluids"}, {"Title": "Three-dimensional study of double droplets impact on a wettability-patterned surface", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "248", "Issue": "", "Page": "105669", "JournalTitle": "Computers & Fluids"}, {"Title": "Droplet splitting on chemically heterogeneous surface: A 3D lattice Boltzmann study", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "270", "Issue": "", "Page": "106149", "JournalTitle": "Computers & Fluids"}, {"Title": "<PERSON><PERSON><PERSON>mann Investigation of Droplet Interactions with Non-Uniform Chemically Patterned Surfaces", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "281", "Issue": "", "Page": "106377", "JournalTitle": "Computers & Fluids"}]}, {"ArticleId": 117624907, "Title": "Mobile laser scanning as reference for estimation of stem attributes from airborne laser scanning", "Abstract": "The acquisition of high-quality reference data is essential for effectively modelling forest attributes. Incorporating close-range Light Detection and Ranging (LiDAR) systems into the reference data collection stage of remote sensing-based forest inventories can not only increase data collection efficiency but also increase the number of attributes measured with high quality. Therefore, we propose a model-based forest inventory method that uses reference data collected by a car-mounted mobile laser scanning (MLS) system along boreal forest roads. This approach is used for the estimation of diameter at breast height (DBH) and stem volume at the individual tree-level from airborne laser scanning (ALS) data. In addition, we compare the estimates obtained using the proposed method with the ones derived from reference data collected by traditional field inventory of 265 field plots systematically distributed over the study area. The accuracy of the estimates remained comparable regardless of the reference dataset used for estimation of DBH and stem volume. When using the field inventory dataset for model training, the root mean square error (RMSE) of DBH estimates were 4.06 cm (18.8 %) for Norway spruce trees, 6.3 cm (29.6 %) for Scots pine and 8.61 cm (55.9 %) for deciduous trees. Similarly, when evaluating predictions based on the MLS dataset as reference, RMSEs were equal to 3.97 cm (18.4 %) for Norway spruce, 6.12 cm (28.8 %) for Scots pine, and 8.98 cm (58.3 %) for deciduous trees. In general, biases were below 1 cm for most species classes, with the exception of deciduous trees. The accuracy of stem volume also had RMSEs varying across different tree species. For the estimates based on traditional field inventory, the RMSEs were 0.176 m<sup>3</sup> (38.8 %) for Norway spruce, 0.228 m<sup>3</sup> (52.4 %) for Scots pine and 0.246 m<sup>3</sup> (158 %) for deciduous trees. When using the MLS dataset as a reference, the RMSEs were equal to 0.176 m<sup>3</sup> (38.8 %), 0.228 m<sup>3</sup> (52.4 %), and 0.246 m<sup>3</sup> (158 %) for Norway spruce, Scots pine, and deciduous trees, respectively. Car-mounted MLS demonstrated its potential as an efficient alternative for collecting reference data in remote sensing-based forest inventories, which could complement traditional methods.", "Keywords": "Mobile laser scanning; Forest inventory; Stem diameter; Stem volume; Training data", "DOI": "10.1016/j.rse.2024.114414", "PubYear": 2024, "Volume": "315", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Forest Resource Management, Swedish University of Agricultural Sciences, Umeå, Sweden;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Forest Resource Management, Swedish University of Agricultural Sciences, Umeå, Sweden"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Forest Resource Management, Swedish University of Agricultural Sciences, Umeå, Sweden"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Forest Resource Management, Swedish University of Agricultural Sciences, Umeå, Sweden"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Forest Resource Management, Swedish University of Agricultural Sciences, Umeå, Sweden"}], "References": [{"Title": "Terrestrial laser scanning in forest ecology: Expanding the horizon", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "251", "Issue": "", "Page": "112102", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "lidR: An R package for analysis of Airborne Laser Scanning (ALS) data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "251", "Issue": "", "Page": "112061", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Tree crown segmentation in three dimensions using density models derived from airborne laser scanning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "1", "Page": "299", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Two-phase forest inventory using very-high-resolution laser scanning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "271", "Issue": "", "Page": "112909", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Non-destructive estimation of individual tree biomass: Allometric models, terrestrial and UAV laser scanning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "280", "Issue": "", "Page": "113180", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 117624917, "Title": "Mathematical models for analysis of temperature regimes in vehicle braking systems", "Abstract": "Linear and non-linear mathematical models for the determination of the temperature field, and subsequently for the analysis of temperature regimes in the braking systems of vehicles, which are geometrically depicted as isotropic spatial heat-active media that are subject to internal local thermal heating, have been developed. With the use of classical methods, it is not possible to obtain analytical solutions of linear and nonlinear boundary value problems of mathematical physics in a closed form. This is especially the case when the right-hand sides of differential equations with partial derivatives and boundary conditions are discontinuous functions. The given approach is based on the application of the apparatus of generalized functions to describe the local concentration of thermal influence. This made it possible to apply the integral transformation and, on this basis, to obtain analytical solutions of both linear and nonlinear boundary value problems. In the case of a nonlinear boundary value problem, the <PERSON><PERSON><PERSON> transformation was applied, using which the original nonlinear heat conduction equation and nonlinear boundary conditions were linearized, and as a result, a linearized second-order differential equation with partial derivatives and boundary conditions with a discontinuous right-hand side were obtained. To solve the linear boundary value problem, as well as the obtained linearized boundary value problem with respect to the <PERSON><PERSON><PERSON> transformation, the <PERSON><PERSON><PERSON> integral transformation method was used, as a result of which analytical solutions of these problems were obtained. For a heat-sensitive environment, as an example, a linear dependence of the coefficient of thermal conductivity of the structural material of the structure on temperature, which is often used in many practical problems, was chosen. As a result, an analytical relationship was obtained for determining the temperature distribution in this medium. On the basis of the developed mathematical models, a computational algorithm was created and on this basis, software tools were created, using which the heat exchange processes in the middle of the brake structures for the selected materials of the brake pads were analyzed in terms of their effectiveness, as well as the determination of the optimal temperature values for the effective operation of the braking system of vehicles. The developed linear and nonlinear mathematical models for determining the temperature field in spatial heat-active media with internal heating make it possible to analyze their thermal stability. As a result, it becomes possible to increase it and protect it from overheating, which can cause the destruction of not only individual nodes and individual elements, but also the entire structure.", "Keywords": "", "DOI": "10.23939/ujit2024.01.102", "PubYear": 2024, "Volume": "6", "Issue": "1", "JournalId": 86518, "JournalTitle": "Ukrainian Journal of Information Technology", "ISSN": "2707-1898", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117624957, "Title": "MILG: Realistic lip-sync video generation with audio-modulated image inpainting", "Abstract": "Existing lip synchronization (lip-sync) methods generate accurately synchronized mouths and faces in a generated video. However, they still confront the problem of artifacts in regions of non-interest (RONI), e.g. , background and other parts of a face, which decreases the overall visual quality. To solve these problems, we innovatively introduce diverse image inpainting to lip-sync generation. We propose Modulated Inpainting Lip-sync GAN (MILG), an audio-constraint inpainting network to predict synchronous mouths. MILG utilizes prior knowledge of RONI and audio sequences to predict lip shape instead of image generation, which can keep the RONI consistent. Specifically, we integrate modulated spatially probabilistic diversity normalization (MSPD Norm) in our inpainting network, which helps the network generate fine-grained diverse mouth movements guided by the continuous audio features. Furthermore, to lower the training overhead, we modify the contrastive loss in lip-sync to support small-batch-size and few-sample training. Extensive experiments demonstrate that our approach outperforms the existing state-of-the-art of image quality and authenticity while keeping lip-sync.", "Keywords": "Lip-sync; Image inpainting; Face generation; Modulated SPD normalization", "DOI": "10.1016/j.visinf.2024.08.002", "PubYear": 2024, "Volume": "8", "Issue": "3", "JournalId": 47888, "JournalTitle": "Visual Informatics", "ISSN": "2543-2656", "EISSN": "2468-502X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Software Technology, Zhejiang University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Technology, Zhejiang University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science, Zhejiang University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Zhejiang University, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science, Zhejiang University, Hangzhou, China;Corresponding author"}, {"AuthorId": 6, "Name": "Shouling Ji", "Affiliation": "College of Computer Science, Zhejiang University, Hangzhou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science, Zhejiang University, Hangzhou, China"}], "References": [{"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": *********, "Title": "A hybrid GRASP and tabu-search heuristic and an exact method for a variant of the multi-compartment vehicle routing problem", "Abstract": "In this paper, we address a classical MCVRP variant characterized by a single vehicle type that has one exclusive compartment for each product type, fixed compartment sizes, limited total route time, and the fulfillment of all customer demands by a single vehicle on a single route. For this variant, we present a Tabu-Search-based metaheuristic hybridized with a GRASP method applied in the construction phase. We also present an exact algorithm for the problem. The proposed exact algorithm is a branch-and-cut-and-price coded within the VRPSolver framework. We apply our approaches to 237 benchmark literature instances. The results show that our heuristic approach presents a smaller average gap than all literature heuristics. Besides the exact approach outperforms the literature on exact methods and presents several optimal solutions for the first time.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125319", "PubYear": 2025, "Volume": "259", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Engenharia de Produção, Universidade Estadual do Norte Fluminense, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Engenharia de Produção, Universidade Federal Fluminense, Brazil;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto Federal de Educação, Ciência e Tecnologia Fluminense IFF, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Engenharia de Produção, Universidade Federal Fluminense, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto Federal de Educação, Ciência e Tecnologia Fluminense IFF, Brazil"}], "References": []}, {"ArticleId": 117624992, "Title": "Experimental dataset for loads on hard rock shotcrete tunnel linings in a laboratory environment", "Abstract": "<p>To improve the understanding of failure mechanisms and behaviour of hard rock tunnel linings, local load conditions were experimentally simulated and monitored using a comprehensive set of sensors and imaging techniques. The data includes measurements from distributed optical fiber sensors (DOFS), high-resolution cameras, load cells, pressure cells and LVDTs. Two types of loads were examined: rock block load and bond loss combined with a distributed load over the area of lost bond. The experiments replicated these conditions and were conducted in a laboratory setting where the shotcrete and substrate rock were substituted by cast fiber reinforced concrete (FRC) and cast concrete, respectively. To facilitate the loads, concrete cones were cast into the substrate concrete and pushed through the FRC top layer with a hydraulic jack to mimic rock block loads. To simulate the bond loss and the associated distributed load, lifting bags were installed and inflated between the FRC layer and substrate cast concrete. All specimens were monitored using DOFS embedded in two perpendicular directions and in two layers in the top FRC layer. In addition, the hydraulic jack was instrumented with LVDTs and load cells to measure displacement and load, and the pressure in the lifting bags was monitored using a pressure cell. Two cameras continuously photographed the top surface of the FRC layer, which had been painted with a speckle pattern, during the testing and the pictures can be used for digital image correlation (DIC). Lastly, each specimen was scanned with a 3D scanner prior to and after testing of the specimen.</p><p>© 2024 The Author(s).</p>", "Keywords": "Block load in tunnels;Digital image correlation;Distributed load in tunnels;Distributed optical fiber sensors", "DOI": "10.1016/j.dib.2024.110920", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Chalmers University of Technology, Chalmersplatsen 4, 412 96 Göteborg, Sweden."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chalmers University of Technology, Chalmersplatsen 4, 412 96 Göteborg, Sweden."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Chalmers University of Technology, Chalmersplatsen 4, 412 96 Göteborg, Sweden."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Chalmers University of Technology, Chalmersplatsen 4, 412 96 Göteborg, Sweden. ;NCC, Drakegatan 10, 412 50 Göteborg, Sweden."}], "References": []}, {"ArticleId": 117625042, "Title": "Modification of coot optimization algorithm (COA) with adaptive sigmoid increasing inertia weight for global optimization", "Abstract": "<p xml:lang=\"fr\"><p>In this paper, the classical coot optimization algorithm (COA) is modified to improve its overall performance in the exploration phase by adding an adaptive sigmoid inertia weight-based method. The modified coot optimization algorithm (mCOA) was successfully assessed using 13 standard benchmark test functions, which are frequently used to evaluate metaheuristic optimization algorithms. The MATLAB software was utilized to conduct simulation tests, and the outcome was compared with the performance of the original COA, the particle swarm optimization, and the genetic algorithm reported in the literature. The findings showed that the proposed algorithm outperformed the other algorithms on ten (10) of the 13 benchmark functions, while it maintained a competitive performance on the remaining three benchmark test functions. This indicates that mCOA provides a significant improvement to the original COA, thus making it suitable for resolving optimization problems in diverse fields. As a result, the proposed algorithm is recommended for adoption to solve real-life engineering optimization problems.</p></p>", "Keywords": "coot optimization algorithm; modification; algorithm; adaptive weight;", "DOI": "10.3934/aci.2024006", "PubYear": 2024, "Volume": "4", "Issue": "1", "JournalId": 94718, "JournalTitle": "Applied Computing and Intelligence", "ISSN": "2771-392X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electricals and Electronics Engineering, Kwame Nkrumah University of Science and Technology, Kumasi, Ghana"}], "References": [{"Title": "A new optimization method based on COOT bird natural life model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115352", "JournalTitle": "Expert Systems with Applications"}, {"Title": "No Free Lunch in imbalanced learning", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107222", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "The Mountain Gazelle Optimizer for truss structures optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "2", "Page": "116", "JournalTitle": "Applied Computing and Intelligence"}, {"Title": "Modified Coot bird optimization algorithm for solving community detection problem in social networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "10", "Page": "5595", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A review of nature-inspired algorithms on single-objective optimization problems from 2019 to 2023", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "5", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 117625204, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0747-5632(24)00308-X", "PubYear": 2024, "Volume": "161", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [], "References": []}, {"ArticleId": 117625254, "Title": "Feature extraction via 3-D homogeneous attribute decomposition for hyperspectral imagery classification", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2024.2394234", "PubYear": 2024, "Volume": "45", "Issue": "18", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Hunan Institute of Science and Technology, Yueyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Hunan Institute of Science and Technology, Yueyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Hunan Institute of Science and Technology, Yueyang, China"}, {"AuthorId": 4, "Name": "Wujin Li", "Affiliation": "School of Information Science and Technology, Hunan Institute of Science and Technology, Yueyang, China"}], "References": [{"Title": "Spectral‐spatial sequence characteristics‐based convolutional transformer for hyperspectral change detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "4", "Page": "1237", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": 117625282, "Title": "Study of microstructure evolution in the aluminum‒magnesium alloy AlMg6 after explosive welding and heat treatment", "Abstract": "<p>Aluminum‒magnesium alloys are widely used in the shipbuilding and railcar industries because of their high specific strength and corrosion resistance. However, the welding of aluminum–magnesium alloys to steel poses a number of significant issues, mainly due to the formation of brittle intermetallic compounds (IMCs) and adiabatic shear bands (ASBs). The effects of IMCs on the mechanical properties of joints have been well researched, but there is a limited understanding of how ASBs and magnesium affect these properties. In this work, the effects of ASBs and Mg<sub>2</sub>Al<sub>3</sub> on the mechanical properties of explosive-welded bimetals are studied. Optical microscopy, electron microscopy, confocal laser scanning microscopy, and electron backscatter diffraction (EBSD) were performed. Additionally, Vickers hardness tests, tear strength tests, and bending tests were carried out to determine the mechanical properties of the specimens. The results of the study revealed that at the weld interface after explosive welding, a zone of fine dark-etching Mg<sub>2</sub>Al<sub>3</sub> phase accumulation up to 10 µm thick and an ASB zone up to 400 µm thick formed. The thickness and included volume of both zones increase from 3 to 20 vol.% with increasing detonation velocity. The tear strength also increases (from 80 to 230 MPa). After heat treatment at 200 °C for 1 h, the specimens survived at twice the bending angle, indicating good plastic properties. Thus, the results help elucidate the influence of Mg<sub>2</sub>Al<sub>3</sub> and ASBs on the properties of bimetals with AlMg6 alloys obtained via explosive welding.</p>", "Keywords": "Al–Mg alloys; Explosive welding; EBSD analysis; Weld interface; Microstructure", "DOI": "10.1007/s00170-024-14404-y", "PubYear": 2024, "Volume": "134", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Merzhanov Institute of Structural Macrokinetics and Materials Science, Russian Academy of Sciences, Chernogolovka, Russia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Merzhanov Institute of Structural Macrokinetics and Materials Science, Russian Academy of Sciences, Chernogolovka, Russia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Merzhanov Institute of Structural Macrokinetics and Materials Science, Russian Academy of Sciences, Chernogolovka, Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Polzunov Altai State Technical University, Barnaul, Russia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Polzunov Altai State Technical University, Barnaul, Russia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Merzhanov Institute of Structural Macrokinetics and Materials Science, Russian Academy of Sciences, Chernogolovka, Russia; Corresponding author."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Belgorod State University, Belgorod, Russia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Belgorod State University, Belgorod, Russia"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Belgorod State University, Belgorod, Russia"}], "References": [{"Title": "Brass/Invar bimetal by explosive welding", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "1-2", "Page": "357", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Experimental and simulation studies on explosive welding of AZ31B-Al 5052 alloys", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "127", "Issue": "5-6", "Page": "2387", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Hazardous effects and microstructure of explosive welding under vacuum environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "130", "Issue": "7-8", "Page": "3741", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 117625318, "Title": "Multi-objective service composition optimization problem in IoT for agriculture 4.0", "Abstract": "<p>One of the most well-known names that has recently attained new heights and set a standard is Internet of Things (IoT). IoT aims to connect all physical devices in such a way that they are subject to human control over the Internet.The emergence of IoT in almost all the industries has redesigned them including smart agriculture. In today’s world, the growth in agriculture sector is rapid, smarter and precise than ever. In case of IoT, the objects are termed as services, sometimes with similar functionalities but distinct quality of service parameters. As the user’s requirements are complex, a single service cannot fulfil them efficiently. So, service composition is the solution. These services known as atomic services, are represented as workflow, with each of them having distinct candidate composite services. Fulfilling these Quality of Service (QoS) constraints makes it a NP-hard problem which can’t be solved using traditional approaches. Hence, comes the concept of evolutionary approaches. In this paper one of the evolutionary approach- NSGA-II is used to optimize the production of apple by composing the various services, taking into account the cost and time as multi-objective problem to be solved. This is for the very first time that QoS aware service composition problem has been optimized in smart agriculture as found in the literature. Results are further compared with multi-objective genetic algorithm (MOGA) and it has been found that NSGA-II outperforms MOGA by generating well-proportioned pareto optimal solutions.</p>", "Keywords": "Internet of things; Service composition; Quality of service; NSGA-II; Smart agriculture; Optimization; MOGA; 90B50", "DOI": "10.1007/s00607-024-01346-2", "PubYear": 2024, "Volume": "106", "Issue": "12", "JournalId": 10374, "JournalTitle": "Computing", "ISSN": "0010-485X", "EISSN": "1436-5057", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Jaypee University of Information Technology, Solan, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Jaypee University of Information Technology, Solan, India; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Jaypee University of Information Technology, Solan, India"}], "References": [{"Title": "Privacy-aware cloud service composition based on QoS optimization in Internet of Things", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "11", "Page": "5295", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Multi-objective Optimization using NSGA II for service composition in IoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "1928", "JournalTitle": "Procedia Computer Science"}, {"Title": "QoS-driven metaheuristic service composition schemes: a comprehensive overview", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "5", "Page": "3749", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "An optimization scheme for IoT based smart greenhouse climate control with efficient energy consumption", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "104", "Issue": "2", "Page": "433", "JournalTitle": "Computing"}, {"Title": "Classification and yield prediction in smart agriculture system using IoT", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "8", "Page": "10235", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Application of Bio and Nature-Inspired Algorithms in Agricultural Engineering", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "3", "Page": "1979", "JournalTitle": "Archives of Computational Methods in Engineering"}]}, {"ArticleId": 117625519, "Title": "Design and Management of Micro-Teaching Mode of Innovative Employment Education in Universities Driven by Big Data-Driven Approach: ", "Abstract": "<p>At present, the innovative employment education mode has been popularized in colleges and universities, and China has also begun to vigorously develop innovative employment education. According to the investigation, there are some disadvantages in the process of innovative employment education, which greatly hinder the teaching effect of innovative employment education. Based on the big data-driven method, this paper takes the micro-course education mode in innovative employment education as an example and makes a systematic study. This paper mainly analyzes three main indexes involved in innovative employment education. In this paper, three typical big data-driven methods are introduced, and the corresponding prediction and evaluation index values are predicted and analyzed. The analysis and prediction results show that the prediction effect based on extreme learning machine is the best. In addition, the results of mathematical fitting show that the corresponding predicted values show a good power function relationship at the personal level and the social level.</p>", "Keywords": "", "DOI": "10.4018/IJISMD.353900", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 28854, "JournalTitle": "International Journal of Information System Modeling and Design", "ISSN": "1947-8186", "EISSN": "1947-8194", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Huanghuai University, China"}], "References": []}, {"ArticleId": 117625575, "Title": "Media Learning The Introduction of Fruit with Using Technology Augmented Reality", "Abstract": "<p>At this time the development of science and technology is growing rapidly, especially information technology. And this research is motivated by the rapid development of technology in the field of education, one of which is AUGMENTED REALITY (AR) technology. This is because the use of Augmented Reality (AR) technology can be implemented as a learning media for the introduction of fruit for kindergarten children, Augmented Reality (AR) is an environment that incorporates 3D virtual objects into the real environment. This is because the use of AR is very interesting and can be developed wider. In this study, Game Engine UNITY was used to build Android-based applications and Visual Studio Code as a text editor to build an application. Accompanied by a book or paper containing a marked image which when directed to the application can display a 3D object visualization. This application can be useful for early childhood, especially at the level of Kindergarten or RA level education in the introduction of fruit. It can be concluded that this application is built capable of displaying 3D object visualization in order to attract students in studying fruits, and the application built is easy to use.</p>", "Keywords": "Augmented Reality;3D;TK", "DOI": "10.32764/newton.v1i2.1856", "PubYear": 2021, "Volume": "1", "Issue": "2", "JournalId": 97517, "JournalTitle": "NEWTON: Networking and Information Technology", "ISSN": "", "EISSN": "2797-0728", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas KH. A<PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitas KH. A<PERSON><PERSON>"}], "References": []}, {"ArticleId": 117625593, "Title": "A cell membrane-targeted photosensitizer for cancer photodynamic therapy and dynamic ablation process tracking", "Abstract": "Photodynamic therapy (PDT) uses photosensitizer (PS) to generate reactive oxygen species (ROS) under light to trigger cell apoptosis in cancer treatment. Organelle-targeted PS can effectively enhance the efficacy of phototherapy by specifically destroying subcellular organelles. The cell membrane, as an important organelle, is the “protective wall” of cells maintaining the integrity and normal life activities of cells. The damage to cell membranes is fatal to cells. However, utilizing functional PS to achieve cell membrane-targeted PDT, and realize real-time and accurate in situ reporting of cell membrane damage and monitoring of the treatment process, are challenging tasks. In this work, we prepared a photosensitizer (2TPAO) with aggregation-induced emission (AIE) characteristics, good water solubility, strong near-infrared fluorescence, and the capability for specific imaging of cell membranes. Moreover, 2TPAO could effectively produce ROS under visible light irradiation and achieve photodynamic ablation of tumor cells and tumors. Meanwhile, during PDT, the fluorescence changes of 2TPAO indicated the destruction of cell membranes, allowing intuitive determination of the phototherapy results. Therefore, 2TPAO is an effective cell membrane-targeted PS for cancer PDT.", "Keywords": "", "DOI": "10.1016/j.snb.2024.136629", "PubYear": 2025, "Volume": "422", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry, Central China Normal University, 152 Luoyu Road, Wuhan 430079, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Pharmacy, Hubei University of Chinese Medicine, 16 Huangjiahu West Road, Wuhan 430065, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry, Central China Normal University, 152 Luoyu Road, Wuhan 430079, China;Corresponding authors"}], "References": [{"Title": "A lipid droplet-targeted multifunctional AIE-active fluorescent probe for hydrogen peroxide detection and imaging-guided photodynamic therapy", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "375", "Issue": "", "Page": "132892", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "From cell membrane to mitochondria: Time-dependent AIE photosensitizer for fluorescence imaging and photodynamic anticancer therapy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "393", "Issue": "", "Page": "134255", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 117625654, "Title": "Retraction notice: Intrusion detection in mobile ad-hoc network using Hybrid Reactive Search and Bat Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1108/IJIUS-08-2024-0229", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 30076, "JournalTitle": "International Journal of Intelligent Unmanned Systems", "ISSN": "2049-6427", "EISSN": "2049-6435", "Authors": [], "References": []}, {"ArticleId": 117625663, "Title": "Obstáculos en el camino: cómo afectan las aceras a la movilidad peatonal", "Abstract": "<p>En México, la mayoría de las personas hacemos uso de las vías peatonales diariamente, desafortunadamente muchas veces las condiciones de dichas vías no son óptimas y originan riesgos para el peatón. En el presente trabajo se expone la problemática originada por el uso inadecuado de las aceras. Los registros que se obtuvieron fueron a través de la observación. El trabajo de campo se realizó en los municipios de Heroica Matamoros, San Fernando y Valle Hermoso ubicados en el estado de Tamaulipas, México. Como resultado, se pudo identificar que las vías peatonales se encuentran obstruidas y en malas condiciones, lo que impide la correcta movilidad de las personas. Esto se acentúa cuando se trata de peatones con movilidad reducida; los principales factores limitantes fueron: construcciones, señalización, vehículos estacionados, escombro y vegetación. Ya que el uso inadecuado de aceras es generalizado, esto representa un grave problema para el usuario al verse forzado a utilizar la calle para transitar, por lo que se hace necesario promover una cultura vial positiva a través de la aplicación de políticas públicas que garanticen espacios seguros para los peatones, así como un sistema efectivo de sanciones para las personas que obstruyen la libre circulación.</p>", "Keywords": "", "DOI": "10.22201/ceide.16076079e.2024.25.5.5", "PubYear": 2024, "Volume": "25", "Issue": "5", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Autónoma de Tamaulipas (UAMM)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Autónoma de Tamaulipas (UAMM)"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma de Tamaulipas (UAMM)"}, {"AuthorId": 4, "Name": "Fabiola <PERSON> Cárdenas", "Affiliation": "Universidad Autónoma de Tamaulipas (UAMM)"}], "References": []}, {"ArticleId": 117625671, "Title": "Mobile-Based Graduate Data Information System Design with Framework 7", "Abstract": "<p>Graduates are an inseparable part of an educational institution that can help realize the vision and mission. Besides that, graduates' success in the community can be an indicator of the quality of the education. Therefore, relationships or networks between graduates must be closely intertwined so that later graduates can help each other and share experiences or knowledge with graduates who have just finished their education. Mobile-Based Graduate Data Information System Design with Framework7 is a medium that can facilitate graduates at the Bahrul Ulum Islamic Boarding School Tambakberas Jombang Foundation in recording and collecting graduates spread across various regions. By using the waterfall method as an application design method. This mobile-based application was created to facilitate access to information media according to the current developments. The software used to design this information system is Visual Studio Code, using Mysql as a database and using Framework 7 for mobile application development. With the construction of the Mobile-Based Graduate Data Information System Design, it is hoped that graduates will be able to find out more about graduate data information that is spread out later and can strengthen the synergy between graduates.</p>", "Keywords": "Graduate;Waterfall;Framework7", "DOI": "10.32764/newton.v1i2.1910", "PubYear": 2021, "Volume": "1", "Issue": "2", "JournalId": 97517, "JournalTitle": "NEWTON: Networking and Information Technology", "ISSN": "", "EISSN": "2797-0728", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas KH. A<PERSON><PERSON>"}, {"AuthorId": 2, "Name": "Moch Khoirul Faizin", "Affiliation": "Universitas KH. A<PERSON><PERSON>"}], "References": []}, {"ArticleId": 117625729, "Title": "A Face Recognition Method for Sports Video Based on Feature Fusion and Residual Recurrent Neural Network", "Abstract": "Face recognition technology has penetrated into people's daily life and work fields, and has also been widely applied in sports videos. A video face recognition technology based on feature fusion and residual recurrent neural network is proposed to address the issue of image pose deviation caused by non cooperative situations. Due to the large number of missing high-frequency data in low resolution facial images, a ternary adversarial reconstruction network was first proposed. It achieves correct image matching through the spatial distance of each image, improving the robustness of the model. But for facial recognition in video sequences, higher precision key feature extraction is required. Therefore, this study introduces a residual recurrent neural network to optimize it, and designs its feature fusion and recognition network modules to compensate and extract relevant information before and after frames. Finally, performance verification analysis is conducted on the proposed model, indicating that the recognition accuracy of the recognition system reached 98.3%. In summary, the residual recurrent neural network based on the ternary adversarial reconstruction network framework constructed in this study can effectively achieve video oriented facial recognition.", "Keywords": "", "DOI": "10.31449/inf.v48i12.5968", "PubYear": 2024, "Volume": "48", "Issue": "12", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117625830, "Title": "A proposal for a minimum viable mission assurance (MVMA) process for small and medium satellites", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJASM.2024.10066495", "PubYear": 2024, "Volume": "17", "Issue": "4", "JournalId": 10974, "JournalTitle": "International Journal of Agile Systems and Management", "ISSN": "1741-9174", "EISSN": "1741-9182", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>uza", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117625936, "Title": "Large Language Models Meet User Interfaces: The Case of Provisioning Feedback", "Abstract": "Incorporating Generative Artificial Intelligence (GenAI), especially Large Language Models (LLMs), into educational settings presents valuable opportunities to boost the efficiency of educators and enrich the learning experiences of students. A significant portion of the current use of LLMs by educators has involved using conversational user interfaces (CUIs), such as chat windows, for functions like generating educational materials or offering feedback to learners. The ability to engage in real-time conversations with LLMs, which can enhance educators' domain knowledge across various subjects, has been of high value. However, it also presents challenges to LLMs' widespread, ethical, and effective adoption. Firstly, educators must have a degree of expertise, including tool familiarity, AI literacy and prompting to effectively use CUIs, which can be a barrier to adoption. Secondly, the open-ended design of CUIs makes them exceptionally powerful, which raises ethical concerns, particularly when used for high-stakes decisions like grading. Additionally, there are risks related to privacy and intellectual property, stemming from the potential unauthorised sharing of sensitive information. Finally, CUIs are designed for short, synchronous interactions and often struggle and hallucinate when given complex, multi-step tasks (e.g., providing individual feedback based on a rubric on a large scale). To address these challenges, we explored the benefits of transitioning away from employing LLMs via CUIs to the creation of applications with user-friendly interfaces that leverage LLMs through API calls. We first propose a framework for pedagogically sound and ethically responsible incorporation of GenAI into educational tools, emphasizing a human-centred design. We then illustrate the application of our framework to the design and implementation of a novel tool called Feedback Copilot, which enables instructors to provide students with personalized qualitative feedback on their assignments in classes of any size. An evaluation involving the generation of feedback from two distinct variations of the Feedback Copilot tool, using numerically graded assignments from 338 students, demonstrates the viability and effectiveness of our approach. Our findings have significant implications for GenAI application researchers, educators seeking to leverage accessible GenAI tools, and educational technologists aiming to transcend the limitations of conversational AI interfaces, thereby charting a course for the future of GenAI in education.", "Keywords": "Artificial intelligence; Large language models; Generative artificial intelligence; Interfaces; Feedback; Learning analytics", "DOI": "10.1016/j.caeai.2024.100289", "PubYear": 2024, "Volume": "7", "Issue": "", "JournalId": 79689, "JournalTitle": "Computers and Education: Artificial Intelligence", "ISSN": "2666-920X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, The University of Queensland, St Lucia, QLD, 4072, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Teaching and Learning Innovation, The University of Queensland, St Lucia, QLD, 4072, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, The University of Queensland, St Lucia, QLD, 4072, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, The University of Queensland, St Lucia, QLD, 4072, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, The University of Queensland, St Lucia, QLD, 4072, Australia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Learning Analytics, Faculty of Information Technology, Monash University, Melbourne, VIC, 3800, Australia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of Auckland, 38 Princes Street Auckland, 1010, New Zealand"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, The University of Queensland, St Lucia, QLD, 4072, Australia"}], "References": [{"Title": "A review of automated feedback systems for learners: Classification framework, challenges and opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "162", "Issue": "", "Page": "104094", "JournalTitle": "Computers & Education"}, {"Title": "Impact of AI assistance on student agency", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "210", "Issue": "", "Page": "104967", "JournalTitle": "Computers & Education"}, {"Title": "Explainability for Large Language Models: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Computing Education in the Era of Generative AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "67", "Issue": "2", "Page": "56", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 117626031, "Title": "The h-Component Diagnosability of Alternating Group Graphs", "Abstract": "<p>With the rapid expansion of multiprocessor systems, the fault diagnosis is becoming more and more important. The [Formula: see text]-component diagnosability of a multiprocessor system, is proposed to extend the traditional diagnosability and has been investigated widely. In this paper, we prove that under both the PMC model and MM* model the [Formula: see text]-component and [Formula: see text]-component diagnosability of an [Formula: see text]-dimensional alternating group graph are [Formula: see text] and [Formula: see text] respectively.</p>", "Keywords": "Component diagnosability; alternating group graphs; PMC model and MM* model", "DOI": "10.1142/S0129054124300018", "PubYear": 2025, "Volume": "36", "Issue": "2", "JournalId": 9642, "JournalTitle": "International Journal of Foundations of Computer Science", "ISSN": "0129-0541", "EISSN": "1793-6373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Qinghai Normal University, Xining, Qinghai 810008, P. R. <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Qinghai Normal University, Xining, Qinghai 810008, P. R. <PERSON>;Academy of Plateau Science and Sustainability, People’s Government of Qinghai Province and Beijing Normal University, Xining, Qinghai 810008, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Qinghai Normal University, Xining, Qinghai 810008, P. R. <PERSON>;Academy of Plateau Science and Sustainability, People’s Government of Qinghai Province and Beijing Normal University, Xining, Qinghai 810008, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Academy of Plateau Science and Sustainability, People’s Government of Qinghai Province and Beijing Normal University, Xining, Qinghai 810008, P. R. <PERSON>"}], "References": [{"Title": "The Non-inclusive Diagnosability of Hypercubes under the MM* Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "7", "Page": "929", "JournalTitle": "International Journal of Foundations of Computer Science"}, {"Title": "The Component Diagnosability of Hypercubes with Large-Scale Faulty Nodes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "5", "Page": "1129", "JournalTitle": "The Computer Journal"}, {"Title": "The Component Diagnosability of General Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "1", "Page": "67", "JournalTitle": "International Journal of Foundations of Computer Science"}, {"Title": "Component Fault Diagnosis and Fault Tolerance of Alternating Group Graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "5", "Page": "1184", "JournalTitle": "The Computer Journal"}, {"Title": "r-component diagnosability of hypercubes under the PMC model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "933", "Issue": "", "Page": "114", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Component Connectivity of Alternating Group Networks and Godan Graphs", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "4", "Page": "395", "JournalTitle": "International Journal of Foundations of Computer Science"}, {"Title": "Extra (component) connectivity and diagnosability of bubble sort networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "940", "Issue": "", "Page": "180", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 117626032, "Title": "Exploring Frontier Technologies in Video-Based Person Re-Identification: A Survey on Deep Learning Approach", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.054895", "PubYear": 2024, "Volume": "81", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Xizhan Gao", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Weighted triple-sequence loss for video-based person re-identification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "381", "Issue": "", "Page": "314", "JournalTitle": "Neurocomputing"}, {"Title": "Relation-based global-partial feature learning network for video-based person re-identification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "488", "Issue": "", "Page": "424", "JournalTitle": "Neurocomputing"}, {"Title": "A sparse graph wavelet convolution neural network for video-based person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "108708", "JournalTitle": "Pattern Recognition"}, {"Title": "Co-segmentation inspired attention module for video-based computer vision tasks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "223", "Issue": "", "Page": "103532", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Where to look: Multi-granularity occlusion aware for video person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "536", "Issue": "", "Page": "137", "JournalTitle": "Neurocomputing"}, {"Title": "Multi-scale Spatio-temporal Feature Adaptive Aggregation for Video-based Person Re-identification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "299", "Issue": "", "Page": "111980", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 117626064, "Title": "Optimal charging scheduling for Indoor Autonomous Vehicles in manufacturing operations", "Abstract": "Indoor Autonomous Vehicles (IAVs) have become instrumental in modern logistics, particularly in dynamic operational environments. Their consistent availability is crucial for both timely task execution and energy efficiency in smart factories. Therefore, an inefficient charging schedule can waste energy, compromising production and warehousing efficiency. In addition, formulating an effective charging schedule is challenging due to the nonlinearity of its design and the uncertainties inherent in smart factory settings. In contrast to previous studies that either simplify the problem using linearization-based methods or approach it with computationally demanding algorithms, this paper efficiently addresses the nonlinear challenge, ensuring a closer alignment with real-world conditions. Hence, a simulation environment is first designed to replicate a smart factory, including validated models of IAVs, Charging Stations (CSs), and a variety of unpredictable static and dynamic obstacles. A comprehensive dataset is then provided from this simulation setup. Utilizing this dataset, a model tailored to ascertain the travel time of IAVs, accounting for inherent uncertainties, is trained using Deep Neural Networks (DNNs). Subsequently, a Mixed-Integer Nonlinear Programming (MINLP) problem is formulated to design the optimization task. Finally, integration of the DNNs’ model with the Branch-and-Bound (BnB) approach, forming the BnBD method, streamlines the determination of the optimal charging schedule. Experimental results highlight significant improvements in charging scheduling, establishing this approach as a viable and promising solution for manufacturing operations.", "Keywords": "", "DOI": "10.1016/j.aei.2024.102804", "PubYear": 2024, "Volume": "62", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Québec at Trois-Rivières, Trois-Rivières, G8Z 4M3, QC, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Québec at Trois-Rivières, Trois-Rivières, G8Z 4M3, QC, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Québec at Trois-Rivières, Trois-Rivières, G8Z 4M3, QC, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Québec at Trois-Rivières, Trois-Rivières, G8Z 4M3, QC, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Québec at Trois-Rivières, Trois-Rivières, G8Z 4M3, QC, Canada"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Québec at Trois-Rivières, Trois-Rivières, G8Z 4M3, QC, Canada"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Sharif University of Technology, Tehran, Iran"}], "References": [{"Title": "Memetic algorithm for solving flexible flow-shop scheduling problems with dynamic transport waiting times", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "105984", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "AGV routing and motion planning in a flexible manufacturing system using a fuzzy-based genetic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "7-8", "Page": "1801", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Modeling the electrical power and energy consumption of automated guided vehicles to improve the energy efficiency of production systems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "1-2", "Page": "481", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A state-of-the-art review on mobile robotics tasks using artificial intelligence and visual data", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114195", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Building Information Model enabled Multiple Traveling Salesman Problem for building interior patrols", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101237", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Head pose estimation using deep neural networks and 3D point clouds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108210", "JournalTitle": "Pattern Recognition"}, {"Title": "Combining empirical mode decomposition and deep recurrent neural networks for predictive maintenance of lithium-ion battery", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "", "Page": "101405", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A simulation and control framework for AGV based transport systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "102430", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Industrial internet of things-driven storage location assignment and order picking in a resource synchronization and sharing-based robotic mobile fulfillment system", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101540", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A spatio-temporal constrained hierarchical scheduling strategy for multiple warehouse mobile robots under industrial cyber–physical system", "Authors": "<PERSON><PERSON> Lian; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101572", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A route and speed optimization model to find conflict-free routes for automated guided vehicles in large warehouses based on quick response code technology", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101604", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "How to pick a mobile robot simulator: A quantitative comparison of CoppeliaSim, Gazebo, MORSE and Webots with a focus on accuracy of motion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "102629", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Learning human-process interaction in manual manufacturing job shops through indoor positioning systems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "151", "Issue": "", "Page": "103984", "JournalTitle": "Computers in Industry"}, {"Title": "Energy-efficient motion planning of an autonomous forklift using deep neural networks and kinetic model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121623", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Design optimization for pressurized water reactor using improved quantum fish swarm algorithm and intuitionistic linguistic decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "59", "Issue": "", "Page": "102315", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Digital twin-driven multi-dimensional assembly error modeling and control for complex assembly process in Industry 4.0", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "60", "Issue": "", "Page": "102390", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Degradation path approximation for remaining useful life estimation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "60", "Issue": "", "Page": "102422", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 117626114, "Title": "Sed de respuestas: explorando los misterios detrás de las sequías", "Abstract": "<p>Las sequías son fenómenos naturales que tienen consecuencias significativas para los seres humanos. Según un estudio de la Organización Mundial Meteorológica, las sequías han causado pérdidas globales superiores a los 250 mil millones de dólares y la muerte de 650,000 personas en un período de 50 años. Aunque en redes sociales y medios convencionales se encuentran diversos contenidos sobre experiencias personales, noticias y fotografías relacionadas con las sequías, hay una falta de difusión sobre los fenómenos meteorológicos que las originan. Entre estos fenómenos destacan “El Niño Oscilación del Sur” (enos), las células de circulación general atmosférica, las regiones monzónicas y las corrientes de chorro. Estos fenómenos, junto con la interacción con los océanos, que actúan como grandes fuentes de humedad, juegan un papel crucial en la formación de sequías. Además, las actividades humanas han amplificado el impacto de estos fenómenos naturales, impidiendo que el planeta desarrolle mecanismos naturales de adaptación a la velocidad necesaria. Comprender tanto los fenómenos meteorológicos que promueven las sequías como la influencia humana en acentuar sus efectos es fundamental para desarrollar planes efectivos de respuesta y mitigación a nivel local y global.</p>", "Keywords": "", "DOI": "10.22201/ceide.16076079e.2024.25.5.9", "PubYear": 2024, "Volume": "25", "Issue": "5", "JournalId": 47622, "JournalTitle": "Revista Digital Universitaria", "ISSN": "1607-6079", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Autónoma de Tamaulipas (UAT)"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma de Tamaulipas (UAT)"}], "References": []}, {"ArticleId": 117626265, "Title": "Optimization of Reservoir Operation by Sine Cosine Algorithm: a Case of Study in Algeria", "Abstract": "The optimal operation of the reservoir has vital importance in water engineering. In the presented article, a new optimization method, named sine cosine algorithm (SCA) was employed to obtain operating policy for an irrigation system. The SCA was utilized for the monthly operation of the Boukerdane Dam placed in the north of Algeria. The fitness function was the minimization of the total shortage for the studied period. Three scenarios considering three different seasons of inflow (dry, normal and wet) are used to optimize the reservoir system’s operation. The SCA outputs were compared with particle swarm optimization (PSO) and kidney algorithm (KA). The outcomes indicated that the SCA surpassed the PSO and KA in convergence rate. The general results indicated the low speed of KA and PSO in achieving convergence. The results indicated that the highest RES (resiliency index), SUS (sustainability index) and REL (reliability index) achieved by the SCA were 65, 86 and 92 %, respectively. Comparing the third scenario with the first and second scenarios, it was observed that the third scenario (wet seasons) improved the results.", "Keywords": "", "DOI": "10.1016/j.suscom.2024.101035", "PubYear": 2024, "Volume": "44", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ecole Nationale Superieure d’Hydraulique, MVRE, Blida, Algeria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ecole Nationale Superieure d’Hydraulique, MVRE, Blida, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ecole Nationale Superieure d’Hydraulique, MVRE, Blida, Algeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Technology, Ilia State University, Tbilisi, Georgia"}, {"AuthorId": 5, "Name": "Mohammd Ehteram", "Affiliation": "Department of Water Engineering and Hydraulic Structures, Faculty of Civil Engineering, Semnan University, Iran"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Civil Engineering department, Faculty of Engineering, Malaysia;University of Malaya, Malaysia"}], "References": []}, {"ArticleId": 117626275, "Title": "Approximating neural distinguishers using differential-linear imbalance", "Abstract": "<p>At CRYPTO 2019, <PERSON><PERSON> first proposed neural distinguishers (NDs) on SPECK32, which are superior to the distinguishers based on the differential distribution table (DDT). <PERSON><PERSON><PERSON> et al. noted that NDs rely on the differential distribution of the last three rounds, and <PERSON><PERSON> et al. pointed out that NDs depend on the strong correlations between the bit values of ciphertext pairs satisfying the expected differential. Hence, one may guess that there exist deep relations between NDs and the differential-linear imbalances. To approximate NDs under a single ciphertext pair, we utilize differential-linear imbalances to construct simplified distinguishers. These newly constructed distinguishers offer comparable distinguishing advantages to that of NDs but with reduced time complexities. For instance, one such simplified distinguisher has only (2^{-1.35}) of the original time complexity of NDs. Our experiments demonstrate that these new distinguishers achieve a matching rate of 98.2% for 5-round SPECK32 under a single ciphertext pair. Furthermore, we achieve the highest accuracies for 7-round and 8-round SPECK32 up to date by using a maximum of 512 ciphertext pairs. Finally, by replacing NDs with simplified distinguishers, we significantly reduce the time complexities of differential-neural attacks on 11–14 rounds of SPECK32.</p>", "Keywords": "Neural distinguisher; Differential-linear imbalance; Differential-neural attack; SPECK32", "DOI": "10.1007/s11227-024-06375-4", "PubYear": 2024, "Volume": "80", "Issue": "19", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "Guangqiu Lv", "Affiliation": "Department of Applied Mathematics, PLA Information Engineering University, Zhengzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, PLA Information Engineering University, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, PLA Information Engineering University, Zhengzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, PLA Information Engineering University, Zhengzhou, China; Corresponding author."}], "References": [{"Title": "A New Neural Distinguisher Considering Features Derived From Multiple Ciphertext Pairs", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "6", "Page": "1419", "JournalTitle": "The Computer Journal"}, {"Title": "A Cipher-Agnostic Neural Training Pipeline with Automated Finding of Good Input Differences", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "184", "JournalTitle": "IACR Transactions on Symmetric Cryptology"}]}, {"ArticleId": 117626278, "Title": "Detecting bipolar disorder on social media by post grouping and interpretable deep learning", "Abstract": "<p>Bipolar disorder is a disorder in which a person expresses manic and depressed emotions repeatedly. Diagnosing bipolar disorder accurately can be difficult because other mood disorders or even regular mood changes may have similar symptoms. Therefore, psychiatrists need to spend a long time observing and interviewing clients to make the diagnosis. Recent studies have trained machine learning models for detecting bipolar disorder on social media. However, most of these studies focused on increasing the accuracy of the model without explaining the classification results. Although the posts of a bipolar disorder user can be observed manually, doing so is not practical since a user can have many posts which may not depict any signs of bipolar disorder. Without any explanations, the trustworthiness of the model decreases. We propose a deep learning model that not only detects and classifies bipolar disorder users but also explains how the model generates the classification results. The posts are first grouped using Latent Dirichlet Allocation, a method commonly used to classify the topic of a text. These groups are then input into the model, and attention mechanisms are utilized to determine which groups have more attention weights and are considered more heavily. Finally, an explanation of the classification results is obtained by visualizing the attention weights. Several case studies are presented to demonstrate the explanations generated through our proposed model. Our model is also compared to other models, achieving the best performance with an F1-Score of 0.92.</p>", "Keywords": "Bipolar detection; Social media; Deep learning; Topic modeling; Model explanation", "DOI": "10.1007/s10844-024-00884-7", "PubYear": 2025, "Volume": "63", "Issue": "1", "JournalId": 7081, "JournalTitle": "Journal of Intelligent Information Systems", "ISSN": "0925-9902", "EISSN": "1573-7675", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, Asia University, Taichung, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Interdisciplinary Program of Education/Department of Educational Psychology and Counseling, National Tsing Hua University, Hsinchu, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> L. <PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, Asia University, Taichung, Taiwan; Department of Computer Science, National Tsing Hua University, Hsinchu, Taiwan; Corresponding author."}], "References": [{"Title": "Finding discriminatory features from electronic health records for depression prediction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "2", "Page": "371", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Topic2Labels: A framework to annotate and classify the social media data through LDA topics and deep learning models for crisis response", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "195", "Issue": "", "Page": "116562", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multimodal time-aware attention networks for depression detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "59", "Issue": "2", "Page": "319", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Emotion fusion for mental illness detection from social media: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "92", "Issue": "", "Page": "231", "JournalTitle": "Information Fusion"}, {"Title": "The detection of mental health conditions by incorporating external knowledge", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "61", "Issue": "2", "Page": "497", "JournalTitle": "Journal of Intelligent Information Systems"}]}, {"ArticleId": 117626360, "Title": "Tensile properties of 3D-printed PLA prismatic cellular structures: an experimental investigation", "Abstract": "Advancements in additive manufacturing have significantly increased the use of cellular structures in product development, especially in the automotive, aerospace, and biomedical industries, due to their enhanced strength-to-weight ratio and energy-absorbing capabilities. This study investigates the tensile properties of 3D-printed PLA prismatic cellular structures, focusing on the effects of fillet radius, wall thickness, and cell size on tensile strength, <PERSON>’s modulus, and strength-to-weight ratio. Using a full factorial design and ANOVA, we examined the impact and interaction of each geometrical parameter. Our findings show that triangular cellular structures exhibit a higher stiffness of 1.36 GPa and tensile strength of 24.28 MPa, resulting in a notable 5.78 MPa/gram strength-to-weight ratio. Increasing cell count and wall thickness enhances both tensile strength and <PERSON>’s modulus, whereas adding fillet radii at corners reduces these properties. Fracture behaviors are influenced by geometrical design: shorter, thicker walls lead to progressive crack propagation, while longer, thinner walls tend to fail catastrophically. Fillet radius introduction shifts the fracture initiation point from the nodes. ANOVA results indicate that wall thickness and cell size significantly affect tensile strength and <PERSON>’s modulus, contributing 36.53% and 53.54%, respectively.", "Keywords": "Cellular structure; Additive manufacturing; Cell size; Wall thickness; Fillet radius; Strength-to-weight ratio", "DOI": "10.1007/s00170-024-14343-8", "PubYear": 2024, "Volume": "134", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, CECOS University of IT & Emerging Sciences, Peshawar, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Mechanical, Biomedical and Design Engineering, Aston University, Birmingham, UK; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Materials Laboratory (AML), Ariston Energy Solutions, Peshawar, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Department, College of Engineering, University of Bahrain, Isa Town, Bahrain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "U.S.-Pakistan Center for Advanced Studies in Energy, University of Engineering and Technology, Peshawar, Pakistan"}], "References": [{"Title": "3D-printed sensors: Current progress and future challenges", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "305", "Issue": "", "Page": "111916", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 117626441, "Title": "Time-Efficient Neural-Network-Based Dynamic Area Optimization Algorithm for High-Altitude Platform Station Mobile Communications", "Abstract": "<p>There is a growing interest in high-altitude platform stations (HAPSs) as potential telecommunication infrastructures in the stratosphere, providing direct communication services to ground-based smartphones. Enhanced coverage and capacity can be realized in HAPSs by adopting multicell configurations. To improve the communication quality, previous studies have investigated methods based on search algorithms, such as genetic algorithms (GAs), which dynamically optimize antenna parameters. However, these methods face hurdles in swiftly adapting to sudden distribution shifts from natural disasters or major events due to their high computational requirements. Moreover, they do not utilize the previous optimization results, which require calculations each time. This study introduces a novel optimization approach based on a neural network (NN) model that is trained on GA solutions. The simple model is easy to implement and allows for instantaneous adaptation to unexpected distribution changes. However, the NN faces the difficulty of capturing the dependencies among neighboring cells. To address the problem, a classifier chain (CC), which chains multiple classifiers to learn output relationships, is integrated into the NN. However, the performance of the CC depends on the output sequence. Therefore, we employ an ensemble approach to integrate the CCs with different sequences and select the best solution. The results of simulations based on distributions in Japan indicate that the proposed method achieves a total throughput whose cumulative distribution function (CDF) is close to that obtained by the GA solutions. In addition, the results show that the proposed method is more time-efficient than GA in terms of the total time required to optimize each user distribution.</p>", "Keywords": "", "DOI": "10.3390/fi16090332", "PubYear": 2024, "Volume": "16", "Issue": "9", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technology Research Laboratory, SoftBank Corp., Koto-ku, Tokyo 135-0064, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technology Research Laboratory, SoftBank Corp., Koto-ku, Tokyo 135-0064, Japan; Department of Information and Computer Science, Faculty of Science and Technology, Keio University, Yokohama 223-8522, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Technology Research Laboratory, SoftBank Corp., Koto-ku, Tokyo 135-0064, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Computer Science, Faculty of Science and Technology, Keio University, Yokohama 223-8522, Japan"}], "References": []}, {"ArticleId": *********, "Title": "Enhancing stock market Forecasting: A hybrid model approach for accurate prediction of S&P 500 and CSI 300 future prices", "Abstract": "This paper investigates the challenging domain of stock market prediction, a significant aspect of financial markets. It focuses on developing predictive models to forecast stock prices accurately, vital for mitigating losses and maximizing gains amidst the inherent unpredictability and volatility of the market. The study comprehensively analyzes various predictive models, including time series analysis and advanced machine learning techniques. It highlights the superiority of ensemble or hybrid models in enhancing prediction reliability. Central to this research is the development of a model incorporating detailed data collection, thorough analysis, and state-of-the-art machine learning methods, achieving notable predictive accuracy. This approach underscores the benefits of data-centric strategies in today’s rapidly evolving business environment and the widespread applicability of predictive analytics. The model outperforms conventional methods by decomposing time series into simpler components and optimizing hyperparameters, thereby enhancing prediction accuracy, as demonstrated by performance testing on the S&P 500 and CSI 300 indices. The RMSE, MAE, and R 2 values of the MEME-AO-LSTM model are 27.12, 19.43, and 0.992, respectively, which serve as evidence of this. The model’s generalizability and high performance are demonstrated by its efficacy in a variety of major markets, including the NASDAQ 100, Nikkei 225, FTSE, DAX, SSE, and KOSPI. Additionally, the model’s adaptability under diverse market conditions is demonstrated through its evaluation of its robustness in response to significant events, such as the economic stimulus responses to the COVID-19 pandemic and the geopolitical tensions resulting from the tension and conflict between Russia and Ukraine. Consequently, the proposed methodology has the potential to help investors achieve substantial and advantageous returns.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125380", "PubYear": 2025, "Volume": "260", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Qing Ge", "Affiliation": "International Business School, Yunnan University of Finance and Economics, Kunming, Yunnan, 650221, China;Corresponding author"}], "References": [{"Title": "A systematic review of fundamental and technical analysis of stock market predictions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "4", "Page": "3007", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A review on the long short-term memory model", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "5929", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Transformer-based attention network for stock movement prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "117239", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-step-ahead stock price index forecasting using long short-term memory model with multivariate empirical mode decomposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "297", "JournalTitle": "Information Sciences"}, {"Title": "A Stock Price Forecasting Model Integrating Complementary Ensemble Empirical Mode Decomposition and Independent Component Analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Machine learning approaches in stock market prediction: A systematic literature review", "Authors": "Latrisha <PERSON>; Jeta N<PERSON>; Call<PERSON>", "PubYear": 2023, "Volume": "216", "Issue": "", "Page": "96", "JournalTitle": "Procedia Computer Science"}, {"Title": "Stock index forecasting based on multivariate empirical mode decomposition and temporal convolutional networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110356", "JournalTitle": "Applied Soft Computing"}, {"Title": "VGC-GAN: A multi-graph convolution adversarial network for stock price prediction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "236", "Issue": "", "Page": "121204", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A granular sigmoid extreme learning machine and its application in a weather forecast", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "147", "Issue": "", "Page": "110799", "JournalTitle": "Applied Soft Computing"}, {"Title": "Series decomposition Transformer with period-correlation for stock market index prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121424", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Optimization of Traditional Stock Market Strategies Using the LSTM Hybrid Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "3", "Page": "136", "JournalTitle": "Information"}, {"Title": "Recurrent context layered radial basis function neural network for the identification of nonlinear dynamical systems", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "580", "Issue": "", "Page": "127524", "JournalTitle": "Neurocomputing"}, {"Title": "A Hybrid Framework for Evaluating Financial Market Price: An Analysis of the Hang Seng Index Case Study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "6", "Page": "", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}]}, {"ArticleId": 117626492, "Title": "Event-Triggered <PERSON><PERSON><PERSON> of T–S Fuzzy Positive Multi-Agent Systems Based on Compensator and Disturbance Observer", "Abstract": "", "Keywords": "", "DOI": "10.1007/s40815-024-01816-x", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Adaptive event-triggered dynamic distributed control of switched positive systems with switching faults", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "48", "Issue": "", "Page": "101328", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}, {"Title": "Fuzzy Fixed-Time Fault-Tolerant Control of Uncertain Nonlinear Systems with Non-affine Faults and Its Application in Manipulator Systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "26", "Issue": "2", "Page": "540", "JournalTitle": "International Journal of Fuzzy Systems"}]}, {"ArticleId": 117626498, "Title": "Analysis of stability and bifurcation and design of optimal control for phytoplankton–zooplankton–fish model with additional food and fish harvesting", "Abstract": "", "Keywords": "", "DOI": "10.1080/02286203.2024.2394759", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4075, "JournalTitle": "International Journal of Modelling and Simulation", "ISSN": "0228-6203", "EISSN": "1925-7082", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center of Excellence in Intelligent Control Automation of Process Systems, Department of Electrical Engineering, Faculty of Engineering, Chulalongkorn University, Bangkok, Thailand"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center of Excellence in Intelligent Control Automation of Process Systems, Department of Electrical Engineering, Faculty of Engineering, Chulalongkorn University, Bangkok, Thailand"}, {"AuthorId": 3, "Name": "PooGyeon Park", "Affiliation": "Department of Electrical Engineering, Pohang University of Science and Technology, Pohang, Republic of Korea"}], "References": [{"Title": "Provision of additional food as a tool of biological control in a delayed predator–prey interaction with prey refuge", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "42", "Issue": "4", "Page": "570", "JournalTitle": "International Journal of Modelling and Simulation"}, {"Title": "Effects of supplying additional food for a scavenger species in a prey-predator-scavenger model with quadratic harvesting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "43", "Issue": "3", "Page": "250", "JournalTitle": "International Journal of Modelling and Simulation"}, {"Title": "Stability and bifurcation analysis of a phytoplankton-zooplankton-fish model involving fear in zooplankton species and fish harvesting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "43", "Issue": "5", "Page": "706", "JournalTitle": "International Journal of Modelling and Simulation"}]}, {"ArticleId": 117626512, "Title": "Enhancing eyeglasses removal in facial images: a novel approach using translation models for eyeglasses mask completion", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-20101-5", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Eyeglasses removal based on attributes detection and improved TV restoration model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "2", "Page": "2691", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Eyeglass Remover Network based on a Synthetic Image Dataset", "Authors": "", "PubYear": 2021, "Volume": "15", "Issue": "4", "Page": "1486", "JournalTitle": "KSII Transactions on Internet and Information Systems"}, {"Title": "A comprehensive review of facial expression recognition techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "1", "Page": "73", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 117626530, "Title": "Scalable data-driven micromechanics model trained with pairwise fiber data for composite materials with randomly distributed fibers", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00366-024-02059-y", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> Hong", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Parameterization-based neural network: predicting non-linear stress–strain response of composites", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "3", "Page": "1621", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 117626571, "Title": "Changes in forklift driving performance and postures among novices resulting from training using a high-fidelity virtual reality simulator: An exploratory study", "Abstract": "Virtual reality (VR) has emerged as a promising tool for training. Our study focused on training for forklift driving, to address an ongoing worker shortage, and the unknown impact of repeated VR training on task performance and kinematic adaptations. We trained 20 novice participants using a VR forklift simulator over two days, with two trials on each day, and including three different driving lessons of varying difficulties. Driving performance was assessed using task completion time, and we quantified kinematics of the head, shoulder, and lumbar spine. Repeated training reduced task completion time (up to ∼29.8% of initial trial) and decreased both kinematic variability and peak range of motion, though these effects were larger for lessons requiring higher precision than simple driving maneuvers. Our results highlight the potential of VR as an effective training environment for novice drivers and suggest that monitoring kinematics could help track skill acquisition during such training.", "Keywords": "", "DOI": "10.1016/j.ergon.2024.103648", "PubYear": 2024, "Volume": "104", "Issue": "", "JournalId": 19885, "JournalTitle": "International Journal of Industrial Ergonomics", "ISSN": "0169-8141", "EISSN": "1872-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, Virginia Tech, VA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, Virginia Tech, VA, USA"}, {"AuthorId": 3, "Name": "Sunwo<PERSON> Kim", "Affiliation": "Department of Industrial and Systems Engineering, Virginia Tech, VA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, Virginia Tech, VA, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, Virginia Tech, VA, USA"}, {"AuthorId": 6, "Name": "Sol Lim", "Affiliation": "Department of Industrial and Systems Engineering, Virginia Tech, VA, USA;Corresponding author. 549 Whittemore Hall, 1185 Perry Street, Blacksburg, VA, 24061, USA"}], "References": [{"Title": "Operational workload balancing in manual order picking", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "106269", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Learning game for improving forklift drivers’ safety awareness", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "4", "Page": "743", "JournalTitle": "Cognition, Technology & Work"}, {"Title": "Learning effects and mental fatigue of forklift operators in food retail logistics: An empirical analysis through the lens of behavioral operations management", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "19", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 117626624, "Title": "Direct velocity planning on parameterized tool path for deterministic polishing with robust dynamic limitation", "Abstract": "<p>In deterministic polishing, optimizing dwell time should meet polishing accuracy with machine dynamic limitations. Directly constraining and optimizing numerous discrete dwell times not only reduces computational efficiency but also complicates balancing polishing accuracy and acceleration constraints. This study employs shared parameter B-splines to continuously describe the polishing path and velocity curve, inherently incorporating both dwell time and acceleration into these splines. By leveraging the properties of B-splines, velocity boundaries can be flexibly limited on each spline segment, facilitating the management of issues at path corners and edges. Local velocity constraint, knot elimination, and piston adjustment are combined to ensure robust dynamic constraints on the XY axes for different tool paths while maintaining computational accuracy. With the complete polishing motion optimized, the matrix replacement method effectively reveals differences in polishing errors between discrete dwell time optimization and continuous polishing motion. The cPVT interpolation is utilized to accurately approximate the planned motion through dense resampling. Simulation on both raster and random paths shows that the proposed method can provide a more practical and feasible polishing motion. Bonnet polishing experiments demonstrate that the proposed method achieves a 43% improvement in PV compared to the LSQR algorithm.</p>", "Keywords": "Dwell time; B-spline; Bonnet polishing; Dynamic limitation", "DOI": "10.1007/s00170-024-14271-7", "PubYear": 2024, "Volume": "134", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 3, "Name": "Yufeng Yuan", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Zhao", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Yin", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, Shanghai, China; Corresponding author."}], "References": []}, {"ArticleId": 117626686, "Title": "Inverse design of a novel multiport power divider based on hybrid neural network", "Abstract": "", "Keywords": "", "DOI": "10.1002/cpe.8276", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Millennium Energy Technologies, Ltd  Xinxiang China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Henan Provincial Key Laboratory of Cyberspace Situational Awareness Zhengzhou Science and Technology Institute  Zhengzhou China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Henan Provincial Key Laboratory of Cyberspace Situational Awareness Zhengzhou Science and Technology Institute  Zhengzhou China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Millennium Energy Technologies, Ltd  Xinxiang China"}], "References": [{"Title": "An integrated framework of deep learning and knowledge graph for prediction of stock price trend: An application in Chinese stock exchange market", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106205", "JournalTitle": "Applied Soft Computing"}, {"Title": "Hybrid optimization algorithm for optimal designing of microstrip patch antenna", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "7", "Page": "e7603", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Deep Learning for Inverse Design of Broadband Quasi-<PERSON><PERSON>", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "2023", "Issue": "", "Page": "1", "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering"}, {"Title": "Antenna Modeling based on Meta-Heuristic Intelligent Algorithms and Neural Networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "159", "Issue": "", "Page": "111623", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 117626708, "Title": "On soft refined 2-normed spaces", "Abstract": "", "Keywords": "", "DOI": "10.22436/jmcs.037.01.01", "PubYear": 2024, "Volume": "37", "Issue": "1", "JournalId": 34363, "JournalTitle": "Journal of Mathematics and Computer Science", "ISSN": "", "EISSN": "2008-949X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117626890, "Title": "Determining Novice and Expert Status in Human–Automation Interaction Through Hidden Markov Models", "Abstract": "", "Keywords": "", "DOI": "10.1080/08839514.2024.2402174", "PubYear": 2024, "Volume": "38", "Issue": "1", "JournalId": 7360, "JournalTitle": "Applied Artificial Intelligence", "ISSN": "0883-9514", "EISSN": "1087-6545", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, <PERSON> Jr. School of Engineering, Duke University, Durham, North Carolina, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Engineering and Computing, George Mason University, Fairfax, Virginia, USA"}, {"AuthorId": 3, "Name": "Hai<PERSON> Zhu", "Affiliation": "Department of Electrical and Computer Engineering, <PERSON> Jr. School of Engineering, Duke University, Durham, North Carolina, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, <PERSON> Jr. School of Engineering, Duke University, Durham, North Carolina, USA"}], "References": []}, {"ArticleId": 117626946, "Title": "Applications of blockchain technology in privacy preserving and data security for real time (data) applications", "Abstract": "Blockchain (BC) technology has been incorporated into the infrastructure of different kinds of applications that require transparency, reliability, security, and traceability. However, the BC still has privacy issues because of the possibility of privacy leaks when using publicly accessible transaction information, even with the security features offered by BCs. Specifically, certain BCs are implementing security mechanisms to address data privacy to prevent privacy issues, facilitates attack‐resistant digital data sharing and storage platforms. Hence, this proposed review aims to give a comprehensive overview of BC technology, to shed light on security issues related to BC, and to emphasize the privacy requirements for existing applications. Many proposed BC applications in asset distribution, data security, the financial industry, the Internet of Things, the healthcare sector, and AI have been explored in this article. It presents necessary background knowledge about BC and privacy strategies for obtaining these security features as part of the evaluation. This survey is expected to assist readers in acquiring a complete understanding of BC security and privacy in terms of approaches, ideas, attributes, and systems. Subsequently, the review presents the findings of different BC works, illustrating several efforts that tackled privacy and security issues. Further, the review offers a positive strategy for the previously described integration of BC for security applications, emphasizing its possible significant gaps and potential future development to promote BC research in the future.", "Keywords": "blockchain technology;consensus mechanism;data security;privacy-preservation;real-time applications", "DOI": "10.1002/cpe.8277", "PubYear": 2024, "Volume": "36", "Issue": "26", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Jawaharlal Nehru Engineering College  Aurangabad India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Maharashtra Institute of Technology  Aurangabad India"}], "References": [{"Title": "Detecting seam carved images using uniform local binary patterns", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "13-14", "Page": "8415", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Security and Privacy on Blockchain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Improved collaborative filtering recommendation algorithm based on differential privacy protection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> Shi; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "7", "Page": "5161", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Applications of blockchain in ensuring the security and privacy of electronic health record systems: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "101966", "JournalTitle": "Computers & Security"}, {"Title": "Untangling blockchain technology: A survey on state of the art, security threats, privacy services, applications and future research directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "90", "Issue": "", "Page": "106897", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Privacy-preserving blockchain-based federated learning for traffic flow prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "328", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Identification and Authentication in Healthcare Internet-of-Things Using Integrated Fog Computing Based Blockchain Model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "", "Page": "100422", "JournalTitle": "Internet of Things"}, {"Title": "BCHealth: A Novel Blockchain-based Privacy-Preserving Architecture for IoT Healthcare Applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "180", "Issue": "", "Page": "31", "JournalTitle": "Computer Communications"}, {"Title": "BlockMedCare: A healthcare system based on IoT, Blockchain and IPFS for data management security", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "23", "Issue": "2", "Page": "329", "JournalTitle": "Egyptian Informatics Journal"}, {"Title": "Towards a blockchain-SDN-based secure architecture for cloud computing in smart industrial IoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>ab S. <PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "2", "Page": "411", "JournalTitle": "Digital Communications and Networks"}, {"Title": "A Privacy-Preserving Transparent Central Bank Digital Currency System Based on Consortium Blockchain and Unspent Transaction Outputs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "4", "Page": "2372", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": *********, "Title": "Arrangement Remote Monitoring and Operation Automated Systems at the Section of Controlled Point ‒ Dispatch Point Based on Universal UCSUT-01 Controller", "Abstract": "<p>Problem statement. The organizing of perspective automatic systems for distance control and operating (ASDCO) of industrial objects demands using the universal controllers (UC), including the admission of wire and radio interfaces for exchanging dates with dispatcher points (DP) and other controlling points (CP). For solution this problem, in SPbSUT were designed the universal controller named UCSUT-01, the structure and mane functions of it were described in this article. The purpose of the work. the exist algorithms of data collection from sensors and protocols of data transmission between DP and CP don’t provides in full measure the requirements to modern ASDCO. So in this article suggests the improved procedures of telemetering and protocols of data exchanging. Result: were made the analysis of technical requirements to UC CP and designed the procedures of current telecontrolling and archives forming, when the hourly average means and waveforms are remembered; are suggested the original protocols of data exchanging between UC and OPC-server; are discussed the increasing of information capacity of control parts wire local complexing of UC. Novelty: Universal controller, which was designed, with collection of original protocols of data exchange possess more wide function possibility then other types of controllers. Practical significance: the result of designing can be used to organize the modern Automatical telemetry systems.</p>", "Keywords": "", "DOI": "10.31854/2307-1303-2024-12-1-1-15", "PubYear": 2024, "Volume": "12", "Issue": "1", "JournalId": 70628, "JournalTitle": "Telecom IT", "ISSN": "", "EISSN": "2307-1303", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117627119, "Title": "DRMAT: A multivariate algorithm for detecting breakpoints in multispectral time series", "Abstract": "Ecosystem dynamics and ecological disturbances manifest as breakpoints in long-term multispectral remote sensing time series. Typically, these breakpoints are captured using univariate methods applied individually to each band, with subsequent integration of the results. However, multivariate analysis provides a promising way to fully incorporate the multispectral bands into breakpoints detection methods, but it has been rarely applied in monitoring ecosystem dynamics and detecting ecological disturbances. In this research, we developed a multivariate algorithm, named breakpoints-Detection algoRithm using MultivAriate Time series (DRMAT). DRMAT can fully use multispectral bands simultaneously with the consideration of the inter-correlation among bands. It decomposes a multivariate time series into trend, seasonality, and noise, iteratively segmenting the detrended/de-seasonalized signals. We quantitatively evaluated DRMAT using both simulated multivariate data and randomly sampled real-world data, including subtle land cover changes caused by forest disturbances (depletions) and recovery (return of vegetation), as well as subtle changes over a broad range of land cover types. We also qualitatively assessed DRMAT in mapping real-world disturbances. For simulated data with prescribed breakpoints in both trend and seasonality, DRMAT detected breakpoints in trend with an F1 score of 85.5 % and in seasonality with an F1 score of 91.7 %. For real-world data in forested land cover, DRMAT unveiled both disturbances and subsequent recovery with an F1 score of 95.1 % for disturbances and 77.1 % for recovery. It detected disturbances in broader land cover types with an F1 score of 84.0 %. We demonstrated that using all-band data was more accurate than using selected bands in breakpoint detection. The inclusion of vegetation indices as model inputs did not improve accuracy unless the original input bands lacked the specific band information in the vegetation indices. As a multivariate approach, DRMAT leverages the full information in the multispectral data and avoids the necessity of integrating results derived from individual bands.", "Keywords": "", "DOI": "10.1016/j.rse.2024.114402", "PubYear": 2024, "Volume": "315", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Environmental Science Graduate Program, The Ohio State University, Columbus, OH 43210, USA;School of Environment and Natural Resources, The Ohio State University, Columbus, OH 43210, USA;Corresponding authors at: Environmental Science Graduate Program, The Ohio State University, Columbus, OH 43210, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Canadian Forest Service (Pacific Forestry Centre), Natural Resources Canada, Victoria, British Columbia, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Natural Resources and the Environment, University of Connecticut, Storrs, CT 06269, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Laboratory of Geo-Information Science and Remote Sensing, Wageningen University and Research, Droevendaalsesteeg 3, 6708, PB, Wageningen, the Netherlands"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Geo-Information Science and Remote Sensing, Wageningen University and Research, Droevendaalsesteeg 3, 6708, PB, Wageningen, the Netherlands"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Environment and Natural Resources, The Ohio State University, Columbus, OH 43210, USA;School of Earth Sciences, The Ohio State University, Columbus, OH 43210, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Environmental Science Graduate Program, The Ohio State University, Columbus, OH 43210, USA;Department of Civil, Environmental and Geodetic Engineering, The Ohio State University, Columbus, OH 43210, USA"}, {"AuthorId": 8, "Name": "Yongyang Cai", "Affiliation": "Department of Agricultural, Environmental, and Development Economics, The Ohio State University, Columbus, OH 43210, USA"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Climate and Carbon Neutrality, The University of Hong Kong, Hong Kong SAR 999077, China;Department of Geography, The University of Hong Kong, 999077, Hong Kong, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Natural Capital Project, Stanford University, Stanford, CA 94305, USA;The Woods Institute for the Environment, Stanford University, Stanford, CA 94305, USA"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Environmental Science Graduate Program, The Ohio State University, Columbus, OH 43210, USA;School of Environment and Natural Resources, The Ohio State University, Columbus, OH 43210, USA;Corresponding authors at: Environmental Science Graduate Program, The Ohio State University, Columbus, OH 43210, USA"}], "References": [{"Title": "Continuous monitoring of land disturbance based on Landsat time series", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "238", "Issue": "", "Page": "111116", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Satellite image texture captures vegetation heterogeneity and explains patterns of bird richness", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "253", "Issue": "", "Page": "112175", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Global land characterisation using land cover fractions at 100 m resolution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "259", "Issue": "", "Page": "112409", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Detecting subtle change from dense Landsat time series: Case studies of mountain pine beetle and spruce beetle disturbance", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "263", "Issue": "", "Page": "112560", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Mapping causal agents of disturbance in boreal and arctic ecosystems of North America using time series of Landsat data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "272", "Issue": "", "Page": "112935", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Near-real-time monitoring of land disturbance with harmonized Landsats 7–8 and Sentinel-2 data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "278", "Issue": "", "Page": "113073", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Trend, seasonality, and abrupt change detection method for land surface temperature time-series analysis: Evaluation and improvement", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "280", "Issue": "", "Page": "113222", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Remote sensing of land change: A multifaceted perspective", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "282", "Issue": "", "Page": "113266", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Evaluating the saturation effect of vegetation indices in forests using 3D radiative transfer simulations and satellite observations", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "295", "Issue": "", "Page": "113665", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Climate change impacts on crop yields: A review of empirical findings, statistical crop models, and machine learning methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "179", "Issue": "", "Page": "106119", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 117627147, "Title": "Machine learning-based prediction and optimisation framework for as-extruded cell viability in extrusion-based 3D bioprinting", "Abstract": "", "Keywords": "", "DOI": "10.1080/17452759.2024.2400330", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 7390, "JournalTitle": "Virtual and Physical Prototyping", "ISSN": "1745-2759", "EISSN": "1745-2767", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Division of Chemical Engineering, Department of Materials Engineering Science, Graduate School of Engineering Science, Osaka University, Toyonaka, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Chemical Engineering, Department of Materials Engineering Science, Graduate School of Engineering Science, Osaka University, Toyonaka, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Chemical Engineering, Department of Materials Engineering Science, Graduate School of Engineering Science, Osaka University, Toyonaka, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Chemical Engineering, Department of Materials Engineering Science, Graduate School of Engineering Science, Osaka University, Toyonaka, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Chemical Engineering, Department of Materials Engineering Science, Graduate School of Engineering Science, Osaka University, Toyonaka, Japan"}], "References": [{"Title": "Relation between prognostics predictor evaluation metrics and local interpretability SHAP values", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "306", "Issue": "", "Page": "103667", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": 117627177, "Title": "Computational and experimental investigation of thermally auxetic multi-metal lattice structures produced by laser powder bed fusion", "Abstract": "", "Keywords": "", "DOI": "10.1080/17452759.2024.2396069", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 7390, "JournalTitle": "Virtual and Physical Prototyping", "ISSN": "1745-2759", "EISSN": "1745-2767", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Machine and Industrial Design, BUT Brno Brno, Czech Republic"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Advanced Manufacturing Laboratory, Department of Mechanical and Process Engineering, ETH Zürich Zürich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Machine and Industrial Design, BUT Brno Brno, Czech Republic"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Advanced Manufacturing Laboratory, Department of Mechanical and Process Engineering, ETH Zürich Zürich, Switzerland"}], "References": [{"Title": "iCorrVision-2D: An integrated python-based open-source Digital Image Correlation software for in-plane measurements (Part 1)", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "", "Page": "101131", "JournalTitle": "SoftwareX"}]}, {"ArticleId": 117627263, "Title": "A TCCM-Based Review of Consumer Behaviour Towards Digital Voice Assistants and Future Research Agenda", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2399415", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "P.G Department of Commerce, University of Jammu, Jammu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "P.G Department of Commerce, University of Jammu, Jammu, India"}], "References": [{"Title": "How perceptions of intelligence and anthropomorphism affect adoption of personal intelligent agents", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "2", "Page": "343", "JournalTitle": "Electronic Markets"}, {"Title": "User preferences for privacy features in digital assistants", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "2", "Page": "411", "JournalTitle": "Electronic Markets"}, {"Title": "The role of user perceptions of intelligence, anthropomorphism, and self-extension on continuance of use of personal intelligent agents", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "3", "Page": "601", "JournalTitle": "European Journal of Information Systems"}, {"Title": "The digital harms of smart home devices: A systematic literature review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "145", "Issue": "", "Page": "107770", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Voice Assistant, Buy Coffee Capsules!: Understanding the Determinants of Consumers' Intention to Use Voice Commerce", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "54", "Issue": "3", "Page": "137", "JournalTitle": "ACM SIGMIS Database"}, {"Title": "Artificial Intelligence-Based Conversational Agents Used for Sustainable Fashion: Systematic Literature Review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 117627339, "Title": "On the Understandability of Design-Level Security Practices in Infrastructure-as-Code Scripts and Deployment Architectures", "Abstract": "<p>Infrastructure as Code (IaC) automates IT infrastructure deployment, which is particularly beneficial for continuous releases, for instance, in the context of microservices and cloud systems. Despite its flexibility in application architecture, neglecting security can lead to vulnerabilities. The lack of comprehensive architectural security guidelines for IaC poses challenges in adhering to best practices. We studied how developers interpret IaC scripts (source code) in two IaC technologies, Ansible and Terraform, compared to semi-formal IaC deployment architecture models and metrics regarding design-level security understanding. In a controlled experiment involving ninety-four participants, we assessed the understandability of IaC-based deployment architectures through source code inspection compared to semi-formal representations in models and metrics.</p><p> We hypothesized that providing semi-formal IaC deployment architecture models and metrics as supplementary material would significantly improve the comprehension of IaC security-related practices, as measured by task correctness . Our findings suggest that semi-formal IaC deployment architecture models and metrics as supplementary material enhance the understandability of IaC security-related practices without significantly increasing duration . We also observed a significant correlation between task correctness and duration when models and metrics were provided. </p>", "Keywords": "", "DOI": "10.1145/3691630", "PubYear": 2025, "Volume": "34", "Issue": "1", "JournalId": 14907, "JournalTitle": "ACM Transactions on Software Engineering and Methodology", "ISSN": "1049-331X", "EISSN": "1557-7392", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Vienna, Vienna, Austria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Vienna, Vienna, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Vienna, Vienna, Austria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Vienna, Vienna, Austria"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Software Security, Hamburg University of Technology, Hamburg, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Software Security, Hamburg University of Technology, Hamburg, Germany"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Software Security, Hamburg University of Technology, Hamburg, Germany"}], "References": [{"Title": "Measuring the accuracy of software vulnerability assessments: experiments with students and professionals", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "2", "Page": "1063", "JournalTitle": "Empirical Software Engineering"}, {"Title": "Security Smells in Ansible and Chef <PERSON>ts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "The do’s and don’ts of infrastructure code: A systematic gray literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "137", "Issue": "", "Page": "106593", "JournalTitle": "Information and Software Technology"}, {"Title": "On the Understandability of Language Constructs to Structure the State and Behavior in Abstract State Machine Specifications: A Controlled Experiment", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "110987", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 117627358, "Title": "Distributed fixed-time optimal flocking for second-order multi-agent systems", "Abstract": "In this paper, we investigate the problem of distributed optimization for second-order multi-agent systems with fixed-time flocking. The objective is to concurrently steer the agents towards a common velocity while optimizing the global objective function. To solve the problem, we first design a fixed-time estimator to predict the global gradient of the objective functions, then based on which, each agent is endowed with fixed-time tracking controller to track the optimal solution. Motivated by the fact that the relative velocity information is difficult to obtain accurately, the proposed algorithm is designed without using neighbors’ velocity. The upper bounds of the settling time are provided without relying on the initial states of the agents, only requiring the adjustment of parameters. The effectiveness of the proposed control protocol is also demonstrated through numerical simulations.", "Keywords": "", "DOI": "10.1142/S2301385025500736", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 26691, "JournalTitle": "Unmanned Systems", "ISSN": "2301-3850", "EISSN": "2301-3869", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Intelligent Manufacturing, Nanyang Institute of Technology, Nanyang, Henan, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Intelligent Manufacturing, Nanyang Institute of Technology, Nanyang, Henan, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Intelligent Manufacturing, Nanyang Institute of Technology, Nanyang, Henan, P. R. China"}], "References": []}, {"ArticleId": 117627388, "Title": "Exploring Soliton Solutions for Fractional Nonlinear Evolution Equations: A Focus on Regularized Long Wave and Shallow Water Wave Models with Beta Derivative", "Abstract": "<p>The fractional regularized long wave equation and the fractional nonlinear shallow-water wave equation are the noteworthy models in the domains of fluid dynamics, ocean engineering, plasma physics, and microtubules in living cells. In this study, a reliable and efficient improved F-expansion technique, along with the fractional beta derivative, has been utilized to explore novel soliton solutions to the stated wave equations. Consequently, the study establishes a variety of reliable and novel soliton solutions involving trigonometric, hyperbolic, rational, and algebraic functions. By setting appropriate values for the parameters, we obtained peakons, anti-peakon, kink, bell, anti-bell, singular periodic, and flat kink solitons. The physical behavior of these solitons is demonstrated in detail through three-dimensional, two-dimensional, and contour representations. The impact of the fractional-order derivative on the wave profile is notable and is illustrated through two-dimensional graphs. It can be stated that the newly established solutions might be further useful for the aforementioned domains.</p>", "Keywords": "", "DOI": "10.3390/computation12090187", "PubYear": 2024, "Volume": "12", "Issue": "9", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Daffodil International University, Savar, Dhaka 1216, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Mathematics and Statistics, College of Science, Taif University, P.O. Box 11099, Taif 21944, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, University of Rajshahi, Rajshahi 6205, Bangladesh"}], "References": []}, {"ArticleId": 117627603, "Title": "Towards Effective Adaptive Revision: Comparative Analysis of Online Assessment Platforms through the Combined AHP-MCDM Approach", "Abstract": "This study presents a detailed comparative analysis of online evaluation platforms aimed at identifying the most suitable one for integrating an adaptive revision plugin. The study uses both the analytic hierarchy process (AHP) and multi-criteria decision making (MCDM) to give a structured and thorough evaluation of different learning management systems (LMSs). By scrutinizing the capabilities, interfaces, and underlying technologies of these systems, the analysis seeks to pinpoint the platform that offers optimal compatibility, flexibility, functionality, and user experience. The central objective of this study is to enhance the effectiveness of adaptive learning tools within educational technologies. This is achieved by evaluating different platforms to ascertain which one best supports the integration of adaptive revision functionalities. Our comprehensive analysis clearly identifies Moodle as the superior platform due to its robust adaptability and enhanced customization capabilities, which are essential for implementing effective adaptive learning tools. The results underscore Moodle’s potential to significantly enhance the educational experience by supporting tailored learning paths and dynamic content adjustments. This study not only highlights Moodle’s advantages but also sets the groundwork for future advancements in adaptive educational technologies.", "Keywords": "comparative study;platforms;online assessment;revision plug-in", "DOI": "10.3991/ijim.v18i17.49157", "PubYear": 2024, "Volume": "18", "Issue": "17", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chouaib <PERSON>ukkali University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "STIC Laboratory, Faculty of Science, Chouaib Doukkali University EL Jadida, Morocco"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "STIC Laboratory, Faculty of Science, Chouaib Doukkali University EL Jadida, Morocco"}], "References": []}, {"ArticleId": 117627687, "Title": "Retraction Note: A novel diversity-based ensemble approach with genetic algorithm for effective disease diagnosis", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-024-10111-8", "PubYear": 2024, "Volume": "28", "Issue": "S1", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Warangal, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Warangal, India"}], "References": []}, {"ArticleId": 117628031, "Title": "Monitoring Automation System Design Hydroponics Based on Chatbot", "Abstract": "<p>The development of information technology has had a tremendous impact on human life. The impact is felt in almost all aspects of life, including agriculture. Farming with hydroponic systems has been widely developed, including the role of information technology to improve the quality and efficiency of crop management. A smart hydroponic system was designed to be able to detect and perform actions automatically. Various sensors are installed for multiple needs, such as checking nutrient levels and adding them when nutrients are running out, controlling water ph levels, controlling water temperature, controlling water levels, etc. To optimize this smart hydroponic system, facilities are needed for monitoring data on the system. This research makes monitoring of a hydroponic automation system based on a telegram chatbot. The chatbot can be obtained from the TDS sensor, Ph sensor, water level sensor, and temperature sensor. According to the incoming request, data from each sensor is sent via IoT with the ESP8266 MCU node and Firebased database to the chatbot. This study uses the waterfall software development method. The results showed that monitoring data on the hydroponic automation system could run well with an average time needed to access 1.44 seconds.</p>", "Keywords": "", "DOI": "10.32764/newton.v1i2.1616", "PubYear": 2021, "Volume": "1", "Issue": "2", "JournalId": 97517, "JournalTitle": "NEWTON: Networking and Information Technology", "ISSN": "", "EISSN": "2797-0728", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas KH. A<PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitas KH. A<PERSON><PERSON>"}], "References": []}, {"ArticleId": 117628046, "Title": "Structural graph federated learning: Exploiting high-dimensional information of statistical heterogeneity", "Abstract": "With the recent progress in graph-federated learning (GFL), it has demonstrated a promising performance in effectively addressing challenges associated with heterogeneous clients. Although the majority of advances in GFL have been focused on techniques for elucidating the intricate relationships among clients, existing GFL methods have two limitations. First, current methods comprising the use of low-dimensional graphs fail to accurately depict the associations between clients, thereby compromising the performance of GFL. Second, these methods may disclose additional information when sharing client-side hidden representations. This paper presents a structural GFL (SGFL) framework and a suite of novel optimization methods. SGFL addresses the limitations of existing GFL approaches with three original contributions. Firstly, our approach advocates the dynamic construction of federated learning (FL) graphs by leveraging the high-dimensional information inherent among clients, while enabling the discovery of hierarchical communities within clients. Secondly, we present SG-FedX, a novel federated stochastic gradient optimization algorithm that mitigates the effects of heterogeneity by intelligently using a global representation. Furthermore, SG-FedX introduces a strict sharing mechanism that protects client privacy more effectively by refraining from sharing client information beyond the model parameters. Our comparative evaluations, conducted against ten representative FL algorithms under challenging non-independently-and-identically-distributed settings, demonstrated the superior performance of SG-FedX. It was noted that, in the cross-dataset scenarios, SG-FedX outperformed the second-best baseline by 8.12% and 7.91% in personalization and generalization performance, respectively.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.112501", "PubYear": 2024, "Volume": "304", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory for Big Data and Decision, National University of Defense Technology, Changsha, 410073, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory for Big Data and Decision, National University of Defense Technology, Changsha, 410073, China;Corresponding author"}, {"AuthorId": 3, "Name": "Weidong Bao", "Affiliation": "Laboratory for Big Data and Decision, National University of Defense Technology, Changsha, 410073, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Software Development Environment, School of Cyber Science and Technology, Beihang University, Beijing, 100191, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory for Big Data and Decision, National University of Defense Technology, Changsha, 410073, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Strategic Assessments and Consultation Institute, Academy of Military Science, Beijing, 100097, China"}], "References": [{"Title": "Federated learning of molecular properties with graph neural networks in a heterogeneous setting", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "6", "Page": "100521", "JournalTitle": "Patterns"}, {"Title": "Towards multi-dimensional knowledge-aware approach for effective community detection in LBSN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "4", "Page": "1435", "JournalTitle": "World Wide Web"}, {"Title": "Graph-regularized federated learning with shareable side information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "257", "Issue": "", "Page": "109960", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Artificial intelligence enabled cyber security defense for smart cities: A novel attack detection framework based on the MDATA model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "276", "Issue": "", "Page": "110781", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Course map learning with graph convolutional network based on AuCM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "5", "Page": "3483", "JournalTitle": "World Wide Web"}, {"Title": "Intelligent fault diagnosis via ring-based decentralized federated transfer learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "284", "Issue": "", "Page": "111288", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An efficient federated learning framework for graph learning in hyperbolic space", "Authors": "Haizhou Du; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "289", "Issue": "", "Page": "111438", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Towards cross-silo federated learning for corporate organizations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "289", "Issue": "", "Page": "111501", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": *********, "Title": "An Empirical Evaluation of Large Language Models in Static Code Analysis for PHP Vulnerability Detection", "Abstract": "<p> Web services play an important role in our daily lives. They are used in a wide range of activities, from online banking and shopping to education, entertainment and social interactions. Therefore, it is essential to ensure that they are kept as secure as possible. However &ndash; as is the case with any complex software system &ndash; creating a sophisticated software free from any security vulnerabilities is a very challenging task. One method to enhance software security is by employing static code analysis. This technique can be used to identify potential vulnerabilities in the source code before they are exploited by bad actors. This approach has been instrumental in tackling many vulnerabilities, but it is not without limitations. Recent research suggests that static code analysis can benefit from the use of large language models (LLMs). This is a promising line of research, but there are still very few and quite limited studies in the literature on the effectiveness of various LLMs at detecting vulnerabilities in source code. This is the research gap that we aim to address in this work. Our study examined five notable LLM chatbot models: ChatGPT 4, ChatGPT 3.5, <PERSON>, Bard/Gemini <sup>1</sup> , and Llama-2, assessing their abilities to identify 104 known vulnerabilities spanning the Top-10 categories defined by the Open Worldwide Application Security Project (OWASP). Moreover, we evaluated issues related to these LLMs&rsquo; false-positive rates using 97 patched code samples. We specifically focused on PHP vulnerabilities, given its prevalence in web applications. We found that ChatGPT-4 has the highest vulnerability detection rate, with over 61.5% of vulnerabilities found, followed by ChatGPT-3.5 at 50%. Bard has the highest rate of vulnerabilities missed, at 53.8%, and the lowest detection rate, at 13.4%. For all models, there is a significant percentage of vulnerabilities that were classified as partially found, indicating a level of uncertainty or incomplete detection across all tested LLMs. Moreover, we found that ChatGPT-4 and ChatGPT-3.5 are consistently more effective across most categories, compared to other models. Bard and Llama-2 display limited effectiveness in detecting vulnerabilities across the majority of categories listed. Surprisingly, our findings reveal high false positive rates across all LLMs. Even the model demonstrating the best performance (ChatGPT-4) notched a false positive rate of nearly 63%, while several models glaringly under-performed, hitting startlingly bad false&nbsp;positive rates of over 90%. Finally, simultaneously deploying multiple LLMs for static analysis resulted in only a marginal enhancement in the rates of vulnerability detection. We believe these results are generalizable to most other programming languages, and hence far from being limited to PHP only. </p>", "Keywords": "", "DOI": "10.3897/jucs.134739", "PubYear": 2024, "Volume": "30", "Issue": "9", "JournalId": 29193, "JournalTitle": "JUCS - Journal of Universal Computer Science", "ISSN": "0948-695X", "EISSN": "0948-6968", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117628115, "Title": "Post-earthquake debris waste management with interpretive-structural-modeling and decision-making-trial, and evaluation-laboratory under neutrosophic fuzzy sets", "Abstract": "This study investigates the barriers to managing debris waste in the post-earthquake period in Türkiye following the significant earthquakes of 2023. Debris waste in the aftermath of earthquakes poses significant challenges to society and the environment. Thus, the critical need for effective debris waste management is essential to identifying and prioritizing barriers that impede the process. The study fills a gap in the literature by integrating Interpretive-Structural-Modeling (ISM), Decision-Making-Trial, and Evaluation-Laboratory (DEMATEL) and Neutrosophic Fuzzy Sets (NFSs) methodologies to assess barriers in the management of earthquake debris waste. The integrated approach considers technical, economic, regularity, environmental, and social dynamics influencing debris waste management. The results show that the first causal barrier is the “lack of legal enforcement,” which has the most significant impact. The lack of legal regulations is caused by insufficient financial, technical, and institutional capacity, especially in Turkiye and similar developing countries. Secondly, the “lack of awareness regarding the environment” is another barrier to effectively managing post-earthquake debris. To improve post-earthquake debris management, the study highlights the importance of barriers for policymakers to create effective and sustainable management strategies. It also contributes to advancing circular economy practices and achieving sustainable development goals 3-8-9 and 11. Further, it assists managers and policymakers in effectively managing debris waste after earthquakes by providing insights into the interrelationships of barriers and mitigation strategies.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.109251", "PubYear": 2024, "Volume": "138", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Science and Engineering, <PERSON>ad <PERSON> University, Qatar Foundation, Doha, Qatar;Department of Industrial Engineering, Yildiz Technical University, Besiktas, 34349 Istanbul, Turkiye;Corresponding author. College of Science and Engineering, Hamad Bin Khalifa University, Qatar Foundation, Doha, Qatar"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Yildiz Technical University, Besiktas, 34349 Istanbul, Turkiye"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Turkish Naval Academy, National Defence University, 34942 Tuzla, Istanbul, Turkiye;Department of Electrical and Computer Engineering, Lebanese American University, Byblos, Lebanon;Department of Information Technologies, Western Caspian University, Baku 1001, Azerbaijan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> <PERSON> School of Global Management, 15 Carter St, Lidcombe, NSW 2141, Australia"}], "References": [{"Title": "A novel hybrid MCDM model for machine tool selection using fuzzy DEMATEL, entropy weighting and later defuzzification VIKOR", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106207", "JournalTitle": "Applied Soft Computing"}, {"Title": "Neutrosophic fuzzy set and its application in decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "5017", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A leanness assessment methodology based on neutrosophic DEMATEL", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "59", "Issue": "", "Page": "320", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Customer-oriented product design using an integrated neutrosophic AHP & DEMATEL & QFD methodology", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "", "Page": "108445", "JournalTitle": "Applied Soft Computing"}, {"Title": "Intuitionistic fuzzy DEMATEL for developing causal relationship of water security", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "3", "Page": "520", "JournalTitle": "International Journal of Intelligent Computing and Cybernetics"}]}, {"ArticleId": 117628120, "Title": "„Stop it, <PERSON><PERSON>!“ – Legally Secure and Interest-Based Data Sharing in the Age of Modern (Cyber) Technology", "Abstract": "<p>As part of the modern Internet, Internet of Things (IoT) devices are particularly indispensable. The reason these devices fit so seamlessly into our everyday lives is that they constantly generate, process and evaluate data (using the Internet) and can react smoothly to circumstances &ndash; such as the refrigerator that automatically reorders missing food or a maintenance monitory system in smart homes that assigns digital maintenance orders to the responsible tradesmen. These data flows and underlying information are the subject of a wide variety of legal projects: Europe aims to become a pioneer of the data economy by providing access to available data accessible for further value and business models. &nbsp;Subject matter includes both personal data and non-personal data, such as IoT machine data. To achieve these politicaleconomic goals, but also to ensure the interests of users and especially manufacturers in confidentiality and fair competition, we introduce the person of the neutral data intermediary: the data innovator.</p>", "Keywords": "", "DOI": "10.3897/jucs.132132", "PubYear": 2024, "Volume": "30", "Issue": "9", "JournalId": 29193, "JournalTitle": "JUCS - Journal of Universal Computer Science", "ISSN": "0948-695X", "EISSN": "0948-6968", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 117628127, "Title": "Designing Coins with Evolutionary Computation", "Abstract": "<p>In recent years, the application of Artificial Intelligence (AI) for creative and artistic endeavours has attracted considerable attention, increasing the opportunities to use AI for many art and design tasks. This paper describes our response to a unique challenge presented by the Portuguese National Press-Mint (INCM): to use AI to design a commemorative coin that celebrates the \"digital world\". We explain the process of this coin's co-creation, from conceptualisation to production, highlighting the design process, key obstacles encountered, and technical innovations made to meet the challenge. These include developing an evolutionary art system guided by Contrastive Language-Image Pre-training (CLIP) and Machine Learning (ML)-based aesthetic models, a system for prompt evolution, and a representation for encoding genotypes in mintable format. This collaboration produced a limited edition 10 euro silver proof coin (Figure 1), with a total of 4,000 units minted by the National Press-Mint. The coin was met with enthusiasm, selling out within two months. This work contributes to Computational Creativity (CC), particularly co-creativity, co-design, and digital art, and represents a significant step in using AI for Numismatics.</p>", "Keywords": "", "DOI": "10.1145/3695933.3695934", "PubYear": 2024, "Volume": "17", "Issue": "2", "JournalId": 29386, "JournalTitle": "ACM SIGEVOlution", "ISSN": "1931-8499", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Coimbra, CISUC/LASI, DEI, Coimbra, Portugal"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Coimbra, CISUC/LASI, DEI, Coimbra, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Coimbra, CISUC/LASI, DEI, Coimbra, Portugal"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Coimbra, CISUC/LASI, DEI, Coimbra, Portugal and also with Vrije Universiteit Brussel, Brussels, Belgium"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Coimbra, CISUC/LASI, DEI, Coimbra, Portugal"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Coimbra, CISUC/LASI, DEI, Coimbra, Portugal"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Coimbra, CISUC/LASI, DEI, Coimbra, Portugal"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "University of Coimbra, CISUC/LASI, DEI, Coimbra, Portugal"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "University of Coimbra, CISUC/LASI, DEI, Coimbra, Portugal"}], "References": []}, {"ArticleId": 117628212, "Title": "Inhibitory neuron links the causal relationship from air pollution to psychiatric disorders: a large multi-omics analysis", "Abstract": "<p>Psychiatric disorders are severe health challenges that exert a heavy public burden. Air pollution has been widely reported as related to psychiatric disorder risk, but their casual association and pathological mechanism remained unclear. Herein, we systematically investigated the large genome-wide association studies (6 cohorts with 1,357,645 samples), single-cell RNA (26 samples with 157,488 cells), and bulk-RNAseq (1595 samples) datasets to reveal the genetic causality and biological link between four air pollutants and nine psychiatric disorders. As a result, we identified ten positive genetic correlations between air pollution and psychiatric disorders. Besides, PM2.5 and NO<sub>2</sub> presented significant causal effects on schizophrenia risk which was robust with adjustment of potential confounders. Besides, transcriptome-wide association studies identified the shared genes between PM2.5/NO2 and schizophrenia. We then discovered a schizophrenia-derived inhibitory neuron subtype with highly expressed shared genes and abnormal synaptic and metabolic pathways by scRNA analyses and confirmed their abnormal level and correlations with the shared genes in schizophrenia patients in a large RNA-seq cohort. Comprehensively, we discovered robust genetic causality between PM2.5, NO<sub>2</sub>, and schizophrenia and identified an abnormal inhibitory neuron subtype that links schizophrenia pathology and PM2.5/NO2 exposure. These discoveries highlight the schizophrenia risk under air pollutants exposure and provide novel mechanical insights into schizophrenia pathology, contributing to pollutant-related schizophrenia risk control and therapeutic strategies development.</p>", "Keywords": "Psychiatric disorders;PM2.5;NO2;Mendelian randomization;Single-cell RNA analyses", "DOI": "10.1186/s40537-024-00960-3", "PubYear": 2024, "Volume": "11", "Issue": "1", "JournalId": 2151, "JournalTitle": "Journal of Big Data", "ISSN": "", "EISSN": "2196-1115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Liang", "Affiliation": "Department of Neurosurgery, Xiangya Hospital, Central South University, Changsha, P. R. China; Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; National Clinical Research Center for Geriatric Disorders, Xiangya Hospital, Central South University, Changsha, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, Xiangya Hospital, Central South University, Changsha, P. R. China; Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; National Clinical Research Center for Geriatric Disorders, Xiangya Hospital, Central South University, Changsha, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, Xiangya Hospital, Central South University, Changsha, P. R. China; Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; National Clinical Research Center for Geriatric Disorders, Xiangya Hospital, Central South University, Changsha, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; College of Life Science and Technology, Huazhong University of Science and Technology, Wuhan, P. R. China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, Xiangya Hospital, Central South University, Changsha, P. R. China; Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; National Clinical Research Center for Geriatric Disorders, Xiangya Hospital, Central South University, Changsha, P. R. China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; Department of Neurosurgery, The Second Affiliated Hospital of Chongqing Medical University, Chongqing, P. R. China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; Department of Oncology, Zhujiang Hospital, Southern Medical University, Guangzhou, P. R. China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; Department of Neurosurgery, The First Affiliated Hospital of Kunming Medical University, Kunming, Yunnan, P. R. China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, Xiangya Hospital, Central South University, Changsha, P. R. China; Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; National Clinical Research Center for Geriatric Disorders, Xiangya Hospital, Central South University, Changsha, P. R. China"}, {"AuthorId": 10, "Name": "Fan Fan", "Affiliation": "Department of Neurosurgery, Xiangya Hospital, Central South University, Changsha, P. R. China; Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; National Clinical Research Center for Geriatric Disorders, Xiangya Hospital, Central South University, Changsha, P. R. China; Corresponding author."}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, Xiangya Hospital, Central South University, Changsha, P. R. China; Hypothalamic Pituitary Research Centre, Xiangya Hospital, Central South University, Changsha, P. R. China; National Clinical Research Center for Geriatric Disorders, Xiangya Hospital, Central South University, Changsha, P. R. China; Corresponding author."}], "References": [{"Title": "Conventional dendritic cell 2 links the genetic causal association from allergic asthma to COVID-19: a Mendelian randomization and transcriptomic study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}]}]