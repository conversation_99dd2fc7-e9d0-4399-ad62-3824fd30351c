[{"ArticleId": 90469301, "Title": "Implementation strategy of physical entity for manufacturing system digital twin", "Abstract": "Digital Twin (DT) technology, as one of the top ten strategic technology trends for 2020, has received widespread attentions and gradually being used in manufacturing system. However, for the implementation strategy of these applications, most of the current researches are focused on the digital aspect (data processing, algorithm application research, etc.), few researches have been done on the physical entity aspect to guide the design of deployment scheme, which is very important for the DT implementation effect. Motivated by this need, the implementation strategy of physical entity for manufacturing system DT is studied in this paper. This strategy firstly performs the application-oriented requirements analysis of physical entity for manufacturing system DT, and then an optimal requirements deployment scheme with Axiomatic Design (AD) theory is researched. With this effort, the implementation strategy of the application from the top level is clarified, and the implementation scheme can be more scientific, giving full play to the advantages of DT technology to solve the specific application problems of physical entities. A case study for DT based Computer Numerical Control Machine Tools (CNCMT) cutting tool life prediction is carried out to show the implementation flow of the proposed strategy, and its operability and effectiveness is verified.", "Keywords": "Digital twin (DT) ; Physical entity ; Analytic hierarchy process (AHP) ; Axiomatic design (AD)", "DOI": "10.1016/j.rcim.2021.102259", "PubYear": 2022, "Volume": "73", "Issue": "", "JournalId": 5910, "JournalTitle": "Robotics and Computer-Integrated Manufacturing", "ISSN": "0736-5845", "EISSN": "1879-2537", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China;Key Laboratory of High Efficiency and Clean Mechanical Manufacture at Shandong University, Ministry of Education, Jinan, China;National Demonstration Center for Experimental Mechanical Engineering Education at Shandong University, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China;Key Laboratory of High Efficiency and Clean Mechanical Manufacture at Shandong University, Ministry of Education, Jinan, China;National Demonstration Center for Experimental Mechanical Engineering Education at Shandong University, Jinan, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Wang", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China;Key Laboratory of High Efficiency and Clean Mechanical Manufacture at Shandong University, Ministry of Education, Jinan, China;National Demonstration Center for Experimental Mechanical Engineering Education at Shandong University, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China;Key Laboratory of High Efficiency and Clean Mechanical Manufacture at Shandong University, Ministry of Education, Jinan, China;National Demonstration Center for Experimental Mechanical Engineering Education at Shandong University, Jinan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Peng Cheng Laboratory, Shenzhen 518000, China"}], "References": [{"Title": "A digital twin-driven approach for the assembly-commissioning of high precision products", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101839", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Digital Twin-driven smart manufacturing: Connotation, reference model, applications and research issues", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101837", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "DT-II:Digital twin enhanced Industrial Internet reference framework towards smart manufacturing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "", "Page": "101881", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Digital twin-driven rapid reconfiguration of the automated manufacturing system via an open architecture model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "101895", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A hybrid predictive maintenance approach for CNC machine tool driven by Digital Twin", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "", "Page": "101974", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 90469390, "Title": "Length-constrained Neural Machine Translation using Length Prediction and Perturbation into Length-aware Positional Encoding", "Abstract": "Neural machine translation often suffers from an under-translation problem owing to its limited modeling of the output sequence lengths. In this study, we propose a novel approach to training a Transformer model using length constraints based on length-aware positional encoding (PE). Because length constraints with exact target sentence lengths degrade the translation performance, we add a random perturbation with a uniform distribution within a certain range to the length constraints in the PE during the training. In the inference step, we predicted the output lengths from the input sequences using a length prediction model based on a large-scale pre-trained language model. In Japanese-to-English and English-to-Japanese translation, experimental results show that the proposed perturbation injection improves the robustness of the length prediction errors, particularly within a certain range.", "Keywords": "Positional Encoding;Neural Machine Translation", "DOI": "10.5715/jnlp.28.778", "PubYear": 2021, "Volume": "28", "Issue": "3", "JournalId": 23347, "JournalTitle": "Journal of Natural Language Processing", "ISSN": "1340-7619", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Currently with NTT Communication Science Laboratories"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nara Institute of Science and Technology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Nara Institute of Science and Technology"}], "References": []}, {"ArticleId": 90469408, "Title": "How Low Can You Go?", "Abstract": "<p>We contribute by systematically analysing the performance trade-offs, costs (privacy loss and deployment cost) and limits of low-resolution thermal array sensors for occupancy detection. First, to assess performance limits, we manipulate the frame rate and resolution of images to establish the lowest possible values where reliable occupancy information can be captured. We also assess the effect of different viewing angles on the performance. We analyse performance using two datasets, an open-source dataset of thermal array sensor measurements (TIDOS) and a proprietary dataset that is used to validate the generality of the findings and to study the effect of different viewing angles. Our results show that even cameras with a 4 × 2 resolution - significantly lower than what has been used in previous research - can support reliable detection, as long as the frame rate is at least 4 frames per second. The lowest tested resolution, 2 × 2, can also offer reliable detection rates but requires higher frame rates (at least 16 frames per second) and careful adjustment of the camera viewing angle. We also show that the performance is sensitive to the viewing angle of the sensor, suggesting that the camera's field-of-view needs to be carefully adjusted to maximize the performance of low-resolution cameras. Second, in terms of costs, using a camera with only 4 × 2 resolution reveals very few insights about the occupants' identity or behaviour, and thus helps to preserve their privacy. Besides privacy, lowering the resolution and frame rate decreases manufacturing and operating costs and helps to make the solution easier to adopt. Based on our results, we derive guidelines on how to choose sensor resolution in real-world deployments by carrying out a small-scale trade-off analysis that considers two representative buildings as potential deployment areas and compares the cost, privacy and accuracy trade-offs of different resolutions.</p>", "Keywords": "HVAC; Internet of Things; Occupancy Detection; Smart homes; Thermal Sensing", "DOI": "10.1145/3478104", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Helsinki, Helsinki, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Helsinki, Helsinki, Finland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Helsinki, Helsinki, Finland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tartu, Tartu, Estonia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Helsinki, Helsinki, Finland"}], "References": []}, {"ArticleId": 90469409, "Title": "Context-aware Adaptive Surgery", "Abstract": "<p>Deep Neural Networks (DNNs) have made massive progress in many fields and deploying DNNs on end devices has become an emerging trend to make intelligence closer to users. However, it is challenging to deploy large-scale and computation-intensive DNNs on resource-constrained end devices due to their small size and lightweight. To this end, model partition, which aims to partition DNNs into multiple parts to realize the collaborative computing of multiple devices, has received extensive research attention. To find the optimal partition, most existing approaches need to run from scratch under given resource constraints. However, they ignore that resources of devices (e.g., storage, battery power), and performance requirements (e.g., inference latency), are often continuously changing, making the optimal partition solution change constantly during processing. Therefore, it is very important to reduce the tuning latency of model partition to realize the real-time adaption under the changing processing context. To address these problems, we propose the Context-aware Adaptive Surgery (CAS) framework to actively perceive the changing processing context, and adaptively find the appropriate partition solution in real-time. Specifically, we construct the partition state graph to comprehensively model different partition solutions of DNNs by import context resources. Then \"the neighbor effect\" is proposed, which provides the heuristic rule for the search process. When the processing context changes, CAS adopts the runtime search algorithm, Graph-based Adaptive DNN Surgery (GADS), to quickly find the appropriate partition that satisfies resource constraints under the guidance of the neighbor effect. The experimental results show that CAS realizes adaptively rapid tuning of the model partition solutions in 10ms scale even for large DNNs (2.25x to 221.7x search time improvement than the state-of-the-art researches), and the total inference latency still keeps the same level with baselines.</p>", "Keywords": "Adaptive model partition; Collaborative model computing; Context perception; Edge intelligence", "DOI": "10.1145/3478073", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University, Xi'an, Shaanxi, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Northwestern Polytechnical University, Xi'an, Shaanxi, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University, Xi'an, Shaanxi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University, Xi'an, Shaanxi, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University, Xi'an, Shaanxi, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University, Xi'an, Shaanxi, China"}], "References": [{"Title": "Making It Personal", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 90469413, "Title": "Location Privacy Protection via Delocalization in 5G Mobile Edge Computing Environment", "Abstract": "", "Keywords": "", "DOI": "10.1109/TSC.2021.3112659", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Differential Privacy-Based Location Protection in Spatial Crowdsourcing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "45", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Trading off Between Multi-Tenancy and Interference: A Service User Allocation Game", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "1980", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Efficient Verification of Edge Data Integrity in Edge Computing Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "6", "Page": "3233", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 90469565, "Title": "Improved Method for Organizing Information Contained in Multiple Documents into a Table", "Abstract": "<PERSON><PERSON><PERSON> et al. (2018) have proposed a method for organizing the information contained in multiple documents into a table without limiting the information to be extracted. In this study, we propose a method for improving the accuracy of these tables. In our proposed method, information is first clustered hierarchically. Next, for the results of hierarchical clustering (with the number of clusters ranging from 1 to n ), the degree of filling and the information density of the resulting table are calculated. The number of clusters when the balance between these two indicators is optimal is chosen as the optimal number of clusters. The results of the method using the chosen number of clusters are organized into a table. In the conventional method, the number of clusters estimated by the X -means method tends to be too small. As demonstrated by the results of experiments using 15 types of multiple documents, the proposed method improves this problem, with its estimated number of clusters being closer to the optimum. The average evaluation result in the tables (F-measure) when applying the conventional method was 0.43; the proposed method improves this to 0.65. We therefore confirm the effectiveness of the proposed method.", "Keywords": "Information Extraction;Table;X-means;Hierarchical Clustering", "DOI": "10.5715/jnlp.28.802", "PubYear": 2021, "Volume": "28", "Issue": "3", "JournalId": 23347, "JournalTitle": "Journal of Natural Language Processing", "ISSN": "1340-7619", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tottori University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tottori University"}, {"AuthorId": 3, "Name": "Qing Ma", "Affiliation": "Ryukoku University"}], "References": []}, {"ArticleId": 90469571, "Title": "CellSense", "Abstract": "<p>Data from the cellular network have been proved as one of the most promising way to understand large-scale human mobility for various ubiquitous computing applications due to the high penetration of cellphones and low collection cost. Existing mobility models driven by cellular network data suffer from sparse spatial-temporal observations because user locations are recorded with cellphone activities, e.g., calls, text, or internet access. In this paper, we design a human mobility recovery system called CellSense to take the sparse cellular billing data (CBR) as input and outputs dense continuous records to recover the sensing gap when using cellular networks as sensing systems to sense the human mobility. There is limited work on this kind of recovery systems at large scale because even though it is straightforward to design a recovery system based on regression models, it is very challenging to evaluate these models at large scale due to the lack of the ground truth data. In this paper, we explore a new opportunity based on the upgrade of cellular infrastructures to obtain cellular network signaling data as the ground truth data, which log the interaction between cellphones and cellular towers at signal levels (e.g., attaching, detaching, paging) even without billable activities. Based on the signaling data, we design a system CellSense for human mobility recovery by integrating collective mobility patterns with individual mobility modeling, which achieves the 35.3% improvement over the state-of-the-art models. The key application of our recovery model is to take regular sparse CBR data that a researcher already has, and to recover the missing data due to sensing gaps of CBR data to produce a dense cellular data for them to train a machine learning model for their use cases, e.g., next location prediction.</p>", "Keywords": "Cellular Network; Human Mobility; Signaling", "DOI": "10.1145/3478087", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Rutgers University, Piscataway, NJ, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Lehigh University, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Rutgers University, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Rutgers University, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "SIAT, Chinese Academy of Sciences & Shenzhen Beidou Intelligent Technology Co., Ltd."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Rutgers University, USA"}], "References": [{"Title": "CellPred", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "PrivateBus", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "RISC", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Will You Come Back / Check-in Again?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Extending Coverage of Stationary Sensing Systems with Mobile Sensing Systems for Human Mobility Modeling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 90469572, "Title": "VeriMask", "Abstract": "<p>The US CDC has recognized moist-heat as one of the most effective and accessible methods of decontaminating N95 masks for reuse in response to the persistent N95 mask shortages caused by the COVID-19 pandemic. However, it is challenging to reliably deploy this technique in healthcare settings due to a lack of smart technologies capable of ensuring proper decontamination conditions of hundreds of masks simultaneously. To tackle these challenges, we developed an open-source wireless sensor platform---VeriMask1 ---that facilitates per-mask verification of the moist-heat decontamination process. VeriMask is capable of monitoring hundreds of masks simultaneously in commercially available heating systems and provides a novel throughput-maximization functionality to help operators optimize the decontamination settings. We evaluate VeriMask in laboratory and real-scenario clinical settings and find that it effectively detects decontamination failures and operator errors in multiple settings and increases the mask decontamination throughput. Our easy-to-use, low-power, low-cost, scalable platform integrates with existing hospital protocols and equipment, and can be broadly deployed in under-resourced facilities to protect front-line healthcare workers by lowering their risk of infection from reused N95 masks. We also memorialize the design challenges, guidelines, and lessons learned from developing and deploying VeriMask during the COVID-19 Pandemic. Our hope is that by reflecting and reporting on this design experience, technologists and front-line health workers will be better prepared to collaborate for future pandemics, regarding mask decontamination, but also other forms of crisis tech.</p>", "Keywords": "COVID-19; N95 Masks Decontamination; Wireless Sensor", "DOI": "10.1145/3478105", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "EECS Department, University of Michigan, Ann Arbor, Michigan, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ECE Department, Northwestern University, Evanston, Illinois, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CISE Department, University of Florida, Gainesville, Florida, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ECE & CS Department, Northwestern University, Evanston, Illinois, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "EECS Department, University of Michigan, Ann Arbor, Michigan, USA"}], "References": [{"Title": "Soil-Monitoring Sensor Powered by Temperature Difference between Air and Shallow Underground Soil", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "A Multi-Sensor Approach to Automatically Recognize Breaks and Work Activities of Knowledge Workers in Academia", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Predicting Subjective Measures of Social Anxiety from Sparsely Collected Mobile Sensor Data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Critical Factors for Implementing Open Source Hardware in a Crisis: Lessons Learned from the COVID-19 Pandemic", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "", "JournalTitle": "Journal of Open Hardware"}]}, {"ArticleId": 90469573, "Title": "Audio Keyword Reconstruction from On-Device Motion Sensor Signals via Neural Frequency Unfolding", "Abstract": "<p>In this paper, we present a novel deep neural network architecture that reconstructs the high-frequency audio of selected spoken human words from low-sampling-rate signals of (ego-)motion sensors, such as accelerometer and gyroscope data, recorded on everyday mobile devices. As the sampling rate of such motion sensors is much lower than the Nyquist rate of ordinary human voice (around 6kHz+), these motion sensor recordings suffer from a significant frequency aliasing effect. In order to recover the original high-frequency audio signal, our neural network introduces a novel layer, called the alias unfolding layer, specialized in expanding the bandwidth of an aliased signal by reversing the frequency folding process in the time-frequency domain. While perfect unfolding is known to be unrealizable, we leverage the sparsity of the original signal to arrive at a sufficiently accurate statistical approximation. Comprehensive experiments show that our neural network significantly outperforms the state of the art in audio reconstruction from motion sensor data, effectively reconstructing a pre-trained set of spoken keywords from low-frequency motion sensor signals (with a sampling rate of 100-400 Hz). The approach demonstrates the potential risk of information leakage from motion sensors in smart mobile devices.</p>", "Keywords": "Deep learning; Motion sensors; Time frequency analysis", "DOI": "10.1145/3478102", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "George <PERSON> University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Li", "Affiliation": "University of Illinois at Urbana-Champaign"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign"}], "References": [{"Title": "BitLight", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 90469576, "Title": "Is Someone Listening?", "Abstract": "<p>Smart devices with the capability to record audio can create a trade-off for users between convenience and privacy. To understand how users experience this trade-off, we report on data from 35 interview, focus group, and design workshop participants. Participants' perspectives on smart-device audio privacy clustered into the pragmatist, guardian, and cynic perspectives that have previously been shown to characterize privacy concerns in other domains. These user groups differed along four axes in their audio-related behaviors (for example, guardians alone say they often move away from a microphone when discussing a sensitive topic). Participants surfaced three usage phases that require design consideration with respect to audio privacy: 1) adoption, 2) in-the-moment recording, and 3) downstream use of audio data. We report common design solutions that participants created for each phase (such as indicators showing when an app is recording audio and annotations making clear when an advertisement was selected based on past audio recording).</p>", "Keywords": "Privacy perceptions; audio recording; smart devices", "DOI": "10.1145/3478091", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Washington Information School, Seattle, WA, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Washington Information School, Seattle, WA, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "PRA Health Sciences, Raleigh, NC, University of California San Diego, Protolab, San Diego, CA, United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Washington Information School, Seattle, WA, United States"}], "References": []}, {"ArticleId": 90469579, "Title": "FaceSense", "Abstract": "<p>Face touch is an unconscious human habit. Frequent touching of sensitive/mucosal facial zones (eyes, nose, and mouth) increases health risks by passing pathogens into the body and spreading diseases. Furthermore, accurate monitoring of face touch is critical for behavioral intervention. Existing monitoring systems only capture objects approaching the face, rather than detecting actual touches. As such, these systems are prone to false positives upon hand or object movement in proximity to one's face (e.g., picking up a phone). We present FaceSense, an ear-worn system capable of identifying actual touches and differentiating them between sensitive/mucosal areas from other facial areas. Following a multimodal approach, FaceSense integrates low-resolution thermal images and physiological signals. Thermal sensors sense the thermal infrared signal emitted by an approaching hand, while physiological sensors monitor impedance changes caused by skin deformation during a touch. Processed thermal and physiological signals are fed into a deep learning model (TouchNet) to detect touches and identify the facial zone of the touch. We fabricated prototypes using off-the-shelf hardware and conducted experiments with 14 participants while they perform various daily activities (e.g., drinking, talking). Results show a macro-F1-score of 83.4% for touch detection with leave-one-user-out cross-validation and a macro-F1-score of 90.1% for touch zone identification with a personalized model.</p>", "Keywords": "face touch detection; multimodal deep learning; thermo-physiological sensing", "DOI": "10.1145/3478129", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, CO"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dartmouth College, Hanover, NH"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dartmouth College"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Texas at Arlington"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "University of Texas at Arlington"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Dartmouth College"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder and Oxford University"}], "References": [{"Title": "DeepMV", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "A Scalable Solution for Signaling Face Touches to Reduce the Spread of Surface-based Pathogens", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "FaceGuard: A Wearable System To Avoid Face Touching", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "", "Page": "47", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 90469581, "Title": "Multi-Head Spatio-Temporal Attention Mechanism for Urban Anomaly Event Prediction", "Abstract": "<p>Timely forecasting the urban anomaly events in advance is of great importance to the city management and planning. However, anomaly event prediction is highly challenging due to the sparseness of data, geographic heterogeneity (e.g., complex spatial correlation, skewed spatial distribution of anomaly events and crowd flows), and the dynamic temporal dependencies.</p><p>In this study, we propose M-STAP, a novel Multi-head Spatio-Temporal Attention Prediction approach to address the problem of multi-region urban anomaly event prediction. Specifically, M-STAP considers the problem from three main aspects: (1) extracting the spatial characteristics of the anomaly events in different regions, and the spatial correlations between anomaly events and crowd flows; (2) modeling the impacts of crowd flow dynamic of the most relevant regions in each time step on the anomaly events; and (3) employing attention mechanism to analyze the varying impacts of the historical anomaly events on the predicted data. We have conducted extensive experimental studies on the crowd flows and anomaly events data of New York City, Melbourne and Chicago. Our proposed model shows higher accuracy (41.91% improvement on average) in predicting multi-region anomaly events compared with the state-of-the-arts.</p>", "Keywords": "anomaly event prediction; crowd flow; multi-head self-attention", "DOI": "10.1145/3478099", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Connecticut, Department of Computer Science & Engineering, Storrs, CT, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Connecticut, Department of Computer Science & Engineering, Storrs, CT, USA"}, {"AuthorId": 3, "Name": "Suining He", "Affiliation": "University of Connecticut, Department of Computer Science & Engineering, Storrs, CT, USA"}], "References": []}, {"ArticleId": 90469582, "Title": "<PERSON><PERSON><PERSON><PERSON>", "Abstract": "<p>Dangerous driving due to drowsiness and distraction is the main cause of traffic accidents, resulting in casualties and economic loss. There is an urgent need to address this problem by accurately detecting dangerous driving behaviors and generating real-time alerts. Inspired by the observation that dangerous driving actions induce unique acoustic features that respond to the signal of an acoustic source, we present the DriverSonar system in this paper. The proposed system detects dangerous driving actions and generates real-time alarms using off-the-shelf smartphones. Compared with the state-of-the-arts, the DriverSonar system does not require dedicated sensors but just uses the built-in speaker and microphone in a smartphone. Specifically, DriverSonar is able to recognize head/hand motions such as nodding, yawning, and abrupt adjustment of the steering wheel. We design, implement and evaluate DriverSonar with extensive experiments. We conduct both simulator-based and and real driving-based experiments (IRB-approved) with 30 volunteers for a period over 12 months. Experiment results show that the proposed system can detect drowsy and distraction related dangerous driving actions at an precision up to 93.2% and a low false acceptance rate of 3.6%.</p>", "Keywords": "Acoustic Tracking; Dangerous Driving Identification; Device-free", "DOI": "10.1145/3478084", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "Hongbo Jiang", "Affiliation": "Hunan University, China"}, {"AuthorId": 2, "Name": "Jingyang Hu", "Affiliation": "Hunan University, China"}, {"AuthorId": 3, "Name": "Dai<PERSON> Liu", "Affiliation": "Hunan University, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Massachusetts Amherst, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hunan University, China"}], "References": []}, {"ArticleId": 90469624, "Title": "Force model of freeform surface multi-axis machining with fillet end mill based on analytical contact analysis", "Abstract": "<p>In the multi-axis machining of freeform surface, compared with ball end mill, the fillet end mill has higher machining efficiency under the same residual height and has been widely used. As the most important physical quantity in machining process, milling force has always been the focus of research. In this paper, the contact geometry between fillet end mill and freeform surface is analyzed by analytical method, and then the milling force prediction model of multi-axis machining is established. Based on differential discretization, the cutter location point of multi-axis machining of freeform surface is approximate to multi-axis machining of oblique plane, which simplifies the research object. The inclination angle is defined to describe the relationship among cutter axis, feed, and workpiece in cutter coordinate system. The space range of the cutting edge element participating in material cutting is constructed by the swept surface of previous tool path, the to-be machined surface and the feed direction surface, and the in-cut cutting edge is determined by judging the cutting edge element one by one. Considering cutter run-out, the element cutting forces on the cylindrical and fillet surfaces of fillet end mill are derived, and all the element forces within in-cut cutting edge are summed by vector to obtain the overall milling force of fillet end mill. Simulation results show that, compared with the solid method, this contact analysis method between cutter and workpiece can take both efficiency and accuracy into account. In the machining experiment, the measured force and predicted force are consistent in trend and amplitude, which verifies the effectiveness of the milling force prediction model.</p>", "Keywords": "Fillet end mill; In-cut cutting edge; Milling force; Freeform surface; Multi-axis machining", "DOI": "10.1007/s00170-021-07962-y", "PubYear": 2022, "Volume": "118", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Precision and Non-traditional Machining Technology of Ministry of Education, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Wei", "Affiliation": "Key Laboratory for Precision and Non-traditional Machining Technology of Ministry of Education, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory for Precision and Non-traditional Machining Technology of Ministry of Education, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory for Precision and Non-traditional Machining Technology of Ministry of Education, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 5, "Name": "Hang <PERSON>", "Affiliation": "Key Laboratory for Precision and Non-traditional Machining Technology of Ministry of Education, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory for Precision and Non-traditional Machining Technology of Ministry of Education, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory for Precision and Non-traditional Machining Technology of Ministry of Education, Dalian University of Technology, Dalian, People’s Republic of China"}], "References": []}, {"ArticleId": 90469644, "Title": "Comparing Mixed-Integer Programming and Constraint Programming Models for the Hybrid Flow Shop Scheduling Problem with Dedicated Machines", "Abstract": "The paper considers a two-stage hybrid flow shop scheduling problem with dedicated machines and release dates. Each job must be first processed on the single machine of stage 1, and then, the job is processed on one of the two dedicated machines of stage 2, depending on its type. Moreover, the jobs are available for processing at their respective release dates. Our goal is to obtain a schedule that minimizes the makespan. This problem is strongly NP-hard. In this paper, two mathematical models are developed for the problem: a mixed-integer programming model and a constraint programming model. The performance of these two models is compared on different problem configurations. And the results show that the constraint programming outperforms the mixed-integer programming in finding optimal solutions for large problem sizes (450 jobs) with very reasonable computing times.", "Keywords": "Constraint programming; CP optimizer; Cplex; Dedicated machines; Hybrid flow shop scheduling; Mixed-integer programming", "DOI": "10.18280/jesa.540408", "PubYear": 2021, "Volume": "54", "Issue": "4", "JournalId": 14230, "JournalTitle": "Journal Européen des Systèmes Automatisés", "ISSN": "1269-6935", "EISSN": "2116-7087", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, National Engineering school of Tunis, University of Tunis El Manar, Tunis, 1002, Tunisia; ESPRIT School of Engineering, Tunis, 1002, Tunisia"}], "References": []}, {"ArticleId": 90469650, "Title": "Development of an empirical process model for adjusted porosity in laser-based powder bed fusion of Ti-6Al-4V", "Abstract": "A promising approach to address the mismatch of bone and implant stiffness, leading to the stress-shielding phenomenon, is the application of functionally graded materials with adjusted porosity. Although defect formation and porosity in laser-based powder bed fusion of metals (PBF-LB/M) are already widely investigated, so far there is little research on the influences and parameter interactions regarding the pore characteristics. This work therefore aims to provide an empirical process model for the generation of gas porosity in the PBF-LB process of Ti-6Al-4V. Parts with closed locally adjusted porosity of $\\sim $ \n ∼ \n 6 % achieved through gaseous pores instead of lack of fusion defects or lattice structures were built by PBF-LB. Parameter variation and evaluation of relative density, pore size and sphericity was done in accordance with the design of experiments approach. A parameter set for maximum gas porosity (laser power of 189 W, scanning speed of 375 mm/s, hatch spacing of 150 μm) was determined for a constant layer thickness of 30 μm and a spot diameter of 35 μm. Tensile tests were conducted with specimens consisting of a core with maximum gas porosity or lack of fusion porosity, respectively, and a dense skin as well as fully dense specimens. Whereas lack of fusion defects can lead to significant reduction of stiffness of 32.2 %, the elastic modulus remained unchanged at 110.0 GPa when implementing spherical pores. Nevertheless, the found superior strength and ductility of specimens with gas porous core (> 1100 MPa and > 0.05 mm/mm, respectively) underline the advantages of adjusted porosity for the application in functionally graded materials and lightweight applications.", "Keywords": "Additive manufacturing; Laser-based powder bed fusion; Porosity; Functionally graded materials; Ti-6Al-4V; Design of experiments", "DOI": "10.1007/s00170-021-07847-0", "PubYear": 2022, "Volume": "118", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laser Zentrum Hannover e.V., Hannover, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laser Zentrum Hannover e.V., Hannover, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laser Zentrum Hannover e.V., Hannover, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laser Zentrum Hannover e.V., Hannover, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Laser Zentrum Hannover e.V., Hannover, Germany"}], "References": [{"Title": "A study on surface morphology and tension in laser powder bed fusion of Ti-6Al-4V", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "9-10", "Page": "2891", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 90469653, "Title": "High resolution screen-printing of carbon black/carbon nanotube composite for stretchable and wearable strain sensor with controllable sensitivity", "Abstract": "With the growing trend of accelerating sensor miniaturization, high-resolution printing technology of stretchable sensors is becoming increasingly important. In this study, we demonstrated stretchable strain sensor with various line resolutions from 50 to 500 μ m based on CB/CNT composite using screen printing process for human motion detection. To optimize composite formula, we systematically investigated rheological properties of the composites, resulting in realization of screen printed pattens with 50–500 μ m of line width; and also investigated the relation among printed line width, resistance, conductivity, and line thickness. The printed pattern elastic strain sensor exhibited a controllable gauge factor (G.F.) such as 2.6 at 5% strain and 4.1 at 50% strain for linewidths of 50 and 500 μ m, respectively, for accurate human motion detection and stable performance was also detected for 300 bending tests. From the result of this study, we proved that the printed pattern can be a potential stretchable strain sensor with controllable sensitivity and high resistance to temperature change.", "Keywords": "High resolution printing ; Composite rheology ; Screen printing ; Stretchable pattern ; Human motion", "DOI": "10.1016/j.sna.2021.113098", "PubYear": 2021, "Volume": "332", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>gjong Go", "Affiliation": "Department of Flexible and Printable Electronics, LANL-JBNU Engineering Institute, Jeonbuk National University, Jeonju 54896, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Flexible and Printable Electronics, LANL-JBNU Engineering Institute, Jeonbuk National University, Jeonju 54896, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Applied Physics “Nell<PERSON>, National Research Council, Via Madonna del Piano 10, Sesto Fi<PERSON>ntino 50019, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Integrative Engineering, Chung-Ang University, Seoul 06974, Republic of Korea;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Flexible and Printable Electronics, LANL-JBNU Engineering Institute, Jeonbuk National University, Jeonju 54896, Republic of Korea;Corresponding authors"}], "References": [{"Title": "Mulberry paper-based graphene strain sensor for wearable electronics with high mechanical strength", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "301", "Issue": "", "Page": "111697", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 90469671, "Title": "The evaluation of dripping behaviors during the manufacturing process based on image processing method: Application to the Ginkgo biloba leaf dripping pills", "Abstract": "The aim of this paper was to investigate the droplet morphology and evaluate the dripping behaviors during the dripping process based on image processing technology. An image acquisition system, which consisted of an industrial camera and a light source, was established. Four indexes including the area, the shape aspect ratio of the droplet, the shape parameter and the contact angle of the droplet images were studied to characterize the droplet morphology. The algorithms for calculating the image indexes and grouping the dripping cycles were designed. The relationships between the droplet morphology and the dripping behavior were conducted, such as the drop speed and the dripping pill weight. The method was well applied to Ginkgo biloba leaf dripping pills. The result showed that the variation trends of the area, the shape aspect ratio of the droplet and the contact angle could effectively characterize the droplet morphology and represent the dripping pill weight fluctuation.", "Keywords": "Image processing ; Image indexes ; Droplet morphology characterization ; Dripping behaviors ; Ginkgo biloba leaf dripping pills", "DOI": "10.1016/j.eswa.2021.115897", "PubYear": 2022, "Volume": "187", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Pharmaceutical Informatics Institute, College of Pharmaceutical Sciences, Zhejiang University, Hangzhou 310058, China;@qq.com"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Pharmaceutical Informatics Institute, College of Pharmaceutical Sciences, Zhejiang University, Hangzhou 310058, China;Corresponding author at: Zhejiang University, No. 866 Yuhangtang Road, Hangzhou 310058, China"}], "References": []}, {"ArticleId": 90469703, "Title": "Seamless connectivity with 5G enabled unmanned aerial vehicles base stations using machine programming approach", "Abstract": "<p>Deployment of a small unmanned aerial vehicle (UAV) mounted 5G base station is a promising solution for providing seamless network connectivity to users in a modern, data-centric thrust areas. The key challenge is to find the location, the height and the optimum number of mounts. A machine programming based approach is proposed here for optimal placement of UAV-mounted base station. The location of the deployment is determined using three clustering algorithms such as K -means, K -medoids, and fuzzy cluster means. Different sets of UAV-mounted base stations have been deployed with variable user density at different heights. The impact on the network performance has been quantified through measurements of received power, signal to interference plus noise ratio (SINR), and path loss per active user equipment (UEs). To gain further insights, a scenario where UEs are connected only to the terrestrial base station, that is, the network is devoid of any sort of UAV mounted base station is evaluated. Numerical computations reaffirm that the proposed technique reduces the average path loss of the active UEs. Moreover, the use of height-mounted base station also alleviates the issues arising due to low SINR values. The said technique shows immense potential in terms of seamless connectivity to end users in events of emergency and remote deployment scenarios, where ground-based base station is not possible. The big transition of on- demand connectivity for 5G networks shall be benefitted from purpose-built UAV infrastructure with specific locations or areas in mind.</p>", "Keywords": "5G network;centroid based clustering;machine programming;UAV mounted base station", "DOI": "10.1111/exsy.12828", "PubYear": 2022, "Volume": "39", "Issue": "5", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE National Institute of Technology Patna  Patna Bihar India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE National Institute of Technology Patna  Patna Bihar India"}], "References": [{"Title": "DroneCells: Improving spectral efficiency using drone-mounted flying base stations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "102895", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 90469740, "Title": "A Study of Gender Bias in Face Presentation Attack and Its Mitigation", "Abstract": "<p>In biometric systems, the process of identifying or verifying people using facial data must be highly accurate to ensure a high level of security and credibility. Many researchers investigated the fairness of face recognition systems and reported demographic bias. However, there was not much study on face presentation attack detection technology (PAD) in terms of bias. This research sheds light on bias in face spoofing detection by implementing two phases. First, two CNN (convolutional neural network)-based presentation attack detection models, ResNet50 and VGG16 were used to evaluate the fairness of detecting imposer attacks on the basis of gender. In addition, different sizes of Spoof in the Wild (SiW) testing and training data were used in the first phase to study the effect of gender distribution on the models’ performance. Second, the debiasing variational autoencoder (DB-VAE) (<PERSON><PERSON>, <PERSON><PERSON>, et al., Uncovering and Mitigating Algorithmic Bias through Learned Latent Structure) was applied in combination with VGG16 to assess its ability to mitigate bias in presentation attack detection. Our experiments exposed minor gender bias in CNN-based presentation attack detection methods. In addition, it was proven that imbalance in training and testing data does not necessarily lead to gender bias in the model’s performance. Results proved that the DB-VAE approach (<PERSON><PERSON>, <PERSON><PERSON>, et al., Uncovering and Mitigating Algorithmic Bias through Learned Latent Structure) succeeded in mitigating bias in detecting spoof faces.</p>", "Keywords": "gender bias; presentation attack detection; debiasing variational autoencoder; convolutional neural network gender bias ; presentation attack detection ; debiasing variational autoencoder ; convolutional neural network", "DOI": "10.3390/fi13090234", "PubYear": 2021, "Volume": "13", "Issue": "9", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, North Carolina A & T State University, Greensboro, NC 27411, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, North Carolina A & T State University, Greensboro, NC 27411, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, North Carolina A & T State University, Greensboro, NC 27411, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Winston-Salem State University, Winston-Salem, NC 27110, USA"}], "References": [{"Title": "Investigating bias in deep face analysis: The KANFace dataset and empirical study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "103954", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 90469757, "Title": "Proceedings 37th International Conference on Logic Programming (Technical Communications)", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.2", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University"}], "References": []}, {"ArticleId": 90469940, "Title": "A new iterative method for solving a class of two-by-two block complex linear systems", "Abstract": "<p>We present a stationary iteration method, namely Alternating Symmetric positive definite and Scaled symmetric positive semidefinite Splitting (ASSS), for solving the system of linear equations obtained by using finite element discretization of a distributed optimal control problem together with time-periodic parabolic equations. An upper bound for the spectral radius of the iteration method is given which is always less than 1. So convergence of the ASSS iteration method is guaranteed. The induced ASSS preconditioner is applied to accelerate the convergence speed of the GMRES method for solving the system. Numerical results are presented to demonstrate the effectiveness of both the ASSS iteration method and the ASSS preconditioner.</p>", "Keywords": "Iterative; PDE-constrained; Optimization; Convergence; Finite element; GMRES; Preconditioning; 49M25; 49K20; 65F10; 65F50", "DOI": "10.1007/s10092-021-00435-4", "PubYear": 2021, "Volume": "58", "Issue": "4", "JournalId": 12677, "JournalTitle": "<PERSON><PERSON><PERSON>", "ISSN": "0008-0624", "EISSN": "1126-5434", "Authors": [{"AuthorId": 1, "Name": "Davod Kho<PERSON>", "Affiliation": "Faculty of Mathematical Sciences, University of Guilan, Rasht, Iran;Center of Excellence for Mathematical Modelling, Optimization and Combinational Computing (MMOCC), University of Guilan, Rasht, Iran"}], "References": []}, {"ArticleId": 90469974, "Title": "Effect of Black Hole Attack in Different Mobility Models of MANET using OLSR protocol", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICS.2022.10041136", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "My <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90469981, "Title": "РАСПРЕДЕЛЕНИЕ НЕОДНОРОДНОГО НАБОРА РЕСУРСОВ ПРИ СОСТАВЛЕНИИ МНОГОПРОЦЕССОРНОГО РАСПИСАНИЯ", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0002338821050085", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 9501, "JournalTitle": "Известия Российской академии наук. Теория и системы управления", "ISSN": "0002-3388", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90469996, "Title": "Controller design via model order reduction for interval systems using <PERSON><PERSON><PERSON><PERSON> theorem and Nevalinna-pick theory: a case study", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCIP.2021.10041143", "PubYear": 2021, "Volume": "3", "Issue": "3", "JournalId": 25167, "JournalTitle": "International Journal of System Control and Information Processing", "ISSN": "1759-9334", "EISSN": "1759-9342", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90470001, "Title": "Soft measurement of dioxin emission concentration based on deep forest regression algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCIP.2021.10041144", "PubYear": 2021, "Volume": "3", "Issue": "3", "JournalId": 25167, "JournalTitle": "International Journal of System Control and Information Processing", "ISSN": "1759-9334", "EISSN": "1759-9342", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90470084, "Title": "Flexible Defense Succeeds Creative Attacks!—A Simulation Approach Based on Position Data in Professional Football", "Abstract": "Introduction: The key to success is finding the perfect mixture of tactical patterns and sudden breaks of them, which depends on the behavior of the opponent team and is not easy to estimate by just watching matches. According to the specific tactical team behavior of “attack vs. defense” professional football matches are investigated based on a simulation approach, professional football matches are investigated according to the specific tactical team behavior of “attack vs. defense.” Methods: The formation patterns of all the sample games are categorized by SOCCER© for defense and attack. Monte Carlo-Simulation can evaluate the mathematical, optimal strategy. The interaction simulation between attack and defense shows optimal flexibility rates for both tactical groups. Approach: A simulation approach based on 40 position data sets of the 2014/15 German Bundesliga has been conducted to analyze and optimize such strategic team behavior in professional soccer. Results: The results revealed that both attack and defense have optimal planning rates to be more successful. The more complex the success indicator, the more successful attacking player groups get. The results also show that defensive player groups always succeed in attacking groups below a specific planning rate value. Conclusion: Groups are always succeeding. The simulation-based position data analysis shows successful strategic behavior patterns for attack and defense. Attacking player groups need very high flexibility to be successful (stay in ball possession). In contrast, defensive player groups only need to be below a defined flexibility rate to be guaranteed more success.", "Keywords": "Strategy;KPI;Offensive and Defensive Dynamics;Ball Contact;Ball Possession;Passes", "DOI": "10.4236/jsea.2021.149029", "PubYear": 2021, "Volume": "14", "Issue": "9", "JournalId": 15771, "JournalTitle": "Journal of Software Engineering and Applications", "ISSN": "1945-3116", "EISSN": "1945-3124", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "German University of Sport Science, Cologne, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "German University of Sport Science, Cologne, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Mainz, Mainz, Germany"}], "References": []}, {"ArticleId": 90470111, "Title": "Key Lessons from Workshop on New Normal Communication for Young Researchers", "Abstract": "", "Keywords": "", "DOI": "10.5715/jnlp.28.901", "PubYear": 2021, "Volume": "28", "Issue": "3", "JournalId": 23347, "JournalTitle": "Journal of Natural Language Processing", "ISSN": "1340-7619", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Stony Brook University"}], "References": []}, {"ArticleId": 90470116, "Title": "NLP2021 Workshop: Evaluation and Quality Estimation of Text—How Do We Judge Human- and Machine-generated Text Good or Bad?", "Abstract": "", "Keywords": "", "DOI": "10.5715/jnlp.28.895", "PubYear": 2021, "Volume": "28", "Issue": "3", "JournalId": 23347, "JournalTitle": "Journal of Natural Language Processing", "ISSN": "1340-7619", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nara Institute of Science and Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tokyo Metropolitan University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ehime University"}], "References": []}, {"ArticleId": 90470161, "Title": "EchoSpot", "Abstract": "<p>Indoor localization has played a significant role in facilitating a collection of emerging applications in the past decade. This paper presents a novel indoor localization solution via inaudible acoustic sensing, called EchoSpot, which relies on only one speaker and one microphone that are readily available on audio devices at households. We program the speaker to periodically send FMCW chirps at 18kHz-23kHz and leverage the co-located microphone to capture the reflected signals from the body and the wall for analysis. By applying the normalized cross-correlation on the transmitted and received signals, we can estimate and profile their time-of-flights (ToFs). We then eliminate the interference from device imperfection and environmental static objects, able to identify the ToFs corresponding to the direct reflection from human body. In addition, a new solution to estimate the ToF from wall reflection is designed, assisting us in spotting a human location in the two-dimensional space. We implement EchoSpot on three different types of speakers, e.g., Amazon Echo, Edifier R1280DB, and Logitech z200, and deploy them in real home environments for evaluation. Experimental results exhibit that EchoSpot achieves the mean localization errors of 4.1cm, 9.2cm, 13.1cm, 17.9cm, 22.2cm, respectively, at 1m, 2m, 3m, 4m, and 5m, comparable to results from the state-of-the-arts while maintaining favorable advantages.</p>", "Keywords": "Device-free; Inaudible Acoustic Sensing; Kalman Filter; Localization", "DOI": "10.1145/3478095", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Louisiana at Lafayette, Louisiana, Lafayette, USA"}, {"AuthorId": 2, "Name": "Jiadong Lou", "Affiliation": "University of Louisiana at Lafayette, Louisiana, Lafayette, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Louisiana at Lafayette, Louisiana, Lafayette, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Louisiana at Lafayette, Louisiana, Lafayette, USA"}], "References": []}, {"ArticleId": 90470304, "Title": "Correction to: Development of integrated deep learning and machine learning algorithm for the assessment of landslide hazard potential", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-021-06249-4", "PubYear": 2021, "Volume": "25", "Issue": "21", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Cyber Security and Data Science, Riphah International University, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Cyber Security and Data Science, Riphah International University, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, COMSATS University Islamabad, Wah Cantt, Pakistan"}], "References": []}, {"ArticleId": 90470313, "Title": "Distilling blockchain requirements for digital investigation platforms", "Abstract": "When handling a security incident, there is a lot of information that needs to be stored, processed, and analyzed. As a result of the volume of information and the necessity to deal with a security incident investigation promptly, different forensic tools have been developed to provide cyber threat intelligence and security incident response management platforms and solutions. These platforms enable responders to effectively collaborate in identifying and investigating incidents, manage their work on a case from creation until resolution or completion, and automate incident response tasks with the external threat information. Since incident response services are a growing priority at organizations, there is a pressing need for a trustworthy and transparent way to maintain the authenticity and integrity of investigative actions that is independently verifiable. Generally, security incident case management allows a security analyst to add related logs. Asides from the possibility of a log being deleted, it is difficult to audit the log for traceability and provenance if a user decides to be malicious. To address this problem, we propose utilizing a blockchain ledger for security investigative actions and associated metadata by extracting requirements for cybersecurity incident response from the models gathered through the analysis of an open-source incident management platform. We demonstrate the applicability of the proposed techniques and methods by investigating a case scenario of evidence actions within TheHive security incident response platform (SIRP).", "Keywords": "Evidence provenance and integrity ; Blockchain integration ; Chain of custody ; Digital forensics ; Incident response", "DOI": "10.1016/j.jisa.2021.102969", "PubYear": 2021, "Volume": "62", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University College Dublin, Ireland School of Electronics, Electrical Engineering and Computer Science, Queen’s University Belfast, United Kingdom"}], "References": [{"Title": "Applications of blockchain in ensuring the security and privacy of electronic health record systems: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "101966", "JournalTitle": "Computers & Security"}, {"Title": "LEChain: A blockchain-based lawful evidence management scheme for digital forensics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "406", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Internet-of-Forensic (IoF): A blockchain based digital forensics framework for IoT applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "13", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 90470349, "Title": "Proceedings 37th International Conference on Logic Programming (Technical Communications)", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.8", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Rice University"}], "References": []}, {"ArticleId": 90470374, "Title": "Pushing the Limits of Long Range Wireless Sensing with LoRa", "Abstract": "<p>Wireless sensing is an exciting new research area which enables a large variety of applications ranging from coarse-grained daily activity recognition to fine-grained vital sign monitoring. While promising in many aspects, one critical issue is the limited sensing range because weak reflection signals are used for sensing. Recently, LoRa signals are exploited for wireless sensing, moving a big step towards long-range sensing. Although promising, there is still a huge room for improvement. In this work, we qualitatively characterize the relationship between target movements and target-induced signal variations, and propose signal processing methods to enlarge the induced signal variation to achieve a longer sensing range. Experiment results show that the proposed system (1) pushes the contact-free sensing range of human walking from the state-of-the-art 50 m to 120 m; (2) achieves a sensing range of 75 m for fine-grained respiration sensing; and (3) demonstrates human respiration sensing even through seven concrete walls.</p>", "Keywords": "Contact-free; LoRa sensing; Long range sensing; Through-wall sensing", "DOI": "10.1145/3478080", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Computer Sciences, University of Massachusetts Amherst, USA"}, {"AuthorId": 2, "Name": "Yuqing Yin", "Affiliation": "College of Information and Computer Sciences, University of Massachusetts Amherst, USA, School of Computer Science and Technology, China University of Mining and Technology, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Computer Sciences, University of Massachusetts Amherst, USA"}], "References": []}, {"ArticleId": 90470375, "Title": "Research on Classification Method of Eggplant Seeds Based on Machine Learning and Multispectral Imaging Classification Eggplant Seeds", "Abstract": "<p>In this study, eggplant seeds of fifteen different varieties were selected for discriminant analyses with a multispectral imaging technique. Seventy-eight features acquired with the multispectral images were extracted from individual eggplant seeds, which were then classified using SVM and a one-dimensional convolutional neural network (1D-CNN), and the overall accuracy was 90.12% and 94.80%, respectively. A two-dimensional convolutional neural network (2D-CNN) was also adopted for discrimination of seed varieties, and an accuracy of 90.67% was achieved. This study not only demonstrated that multispectral imaging combining machine learning techniques could be used as a high-throughput and nondestructive tool to discriminate seed varieties but also revealed that the shape of the seed shell may not be exactly the same as the female parents due to the genetic and environmental factors.</p>", "Keywords": "", "DOI": "10.1155/2021/8857931", "PubYear": 2021, "Volume": "2021", "Issue": "1", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei Agricultural University, Baoding, Hebei Province 071001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hebei Agricultural University, Baoding, Hebei Province 071001, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Xingtai Power Supply Company of State Grid Corporation of China, Xingtai, Hebei Province 054000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>o", "Affiliation": "Hebei Agricultural University, Baoding, Hebei Province 071001, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Biopute Technology Company, Limited, Beijing 100193, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hebei Agricultural University, Baoding, Hebei Province 071001, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Hebei Agricultural University, Baoding, Hebei Province 071001, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hebei Agricultural University, Baoding, Hebei Province 071001, China"}], "References": []}, {"ArticleId": 90470383, "Title": "<PERSON><PERSON><PERSON>", "Abstract": "<p>An absolute mid-air pointing technique requires a preprocess called registration that makes the system remember the 3D positions and types of objects in advance. Previous studies have simply assumed that the information is already available because it requires a cumbersome process performed by an expert in a carefully calibrated environment. We introduce Overthere, which allows the user to intuitively register the objects in a smart environment by pointing to each target object a few times. To ensure accurate and coherent pointing gestures made by the user regardless of individual differences between them, we performed a user study and identified a desirable gesture motion for this purpose. In addition, we provide the user with various feedback to help them understand the current registration progress and adhere to required conditions, which will lead to accurate registration results. The user studies show that Overthere is sufficiently intuitive to be used by ordinary people.</p>", "Keywords": "3D positioning; absolute pointing interface; freehand pointing; object registration", "DOI": "10.1145/3478128", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "Hyunggoog Seo", "Affiliation": "Korea Advanced Institute of Science and Technology, Daejeon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Korea Advanced Institute of Science and Technology, Daejeon, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Korea Advanced Institute of Science and Technology, Daejeon, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Korea Advanced Institute of Science and Technology, Daejeon, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Korea Advanced Institute of Science and Technology, Daejeon, Republic of Korea"}], "References": [{"Title": "Deep learning-based smart task assistance in wearable augmented reality", "Authors": "<PERSON><PERSON>ong<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "101887", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 90470386, "Title": "A SAR Target Recognition Method Based on Decision Fusion of Multiple Features and Classifiers", "Abstract": "<p>A synthetic aperture radar (SAR) target recognition method combining multiple features and multiple classifiers is proposed. The Zernike moments, kernel principal component analysis (KPCA), and monographic signals are used to describe SAR image features. The three types of features describe SAR target geometric shape features, projection features, and image decomposition features. Their combined use can effectively enhance the description of the target. In the classification stage, the support vector machine (SVM), sparse representation-based classification (SRC), and joint sparse representation (JSR) are used as the classifiers for the three types of features, respectively, and the corresponding decision variables are obtained. For the decision variables of the three types of features, multiple sets of weight vectors are used for weighted fusion to determine the target label of the test sample. In the experiment, based on the MSTAR dataset, experiments are performed under standard operating condition (SOC) and extended operating conditions (EOCs). The experimental results verify the effectiveness, robustness, and adaptability of the proposed method.</p>", "Keywords": "", "DOI": "10.1155/2021/1258219", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science, Huanggang Normal University, HuangGang 438000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Jiang", "Affiliation": "College of Computer Science, Huanggang Normal University, HuangGang 438000, China"}, {"AuthorId": 3, "Name": "Yurong Guan", "Affiliation": "College of Computer Science, Huanggang Normal University, HuangGang 438000, China"}, {"AuthorId": 4, "Name": "Qing<PERSON> Wang", "Affiliation": "College of Computer Science, Huanggang Normal University, HuangGang 438000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science, Huanggang Normal University, HuangGang 438000, China"}], "References": [{"Title": "Target Recognition of Synthetic Aperture Radar Images Based on Two-Phase Sparse Representation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "", "Page": "1", "JournalTitle": "Journal of Sensors"}, {"Title": "Combination of Joint Representation and Adaptive Weighting for Multiple Features with Application to SAR Target Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}]}, {"ArticleId": 90470439, "Title": "FG-LiquID", "Abstract": "<p>Contact-less liquid identification via wireless sensing has diverse potential applications in our daily life, such as identifying alcohol content in liquids, distinguishing spoiled and fresh milk, and even detecting water contamination. Recent works have verified the feasibility of utilizing mmWave radar to perform coarse-grained material identification, e.g., discriminating liquid and carpet. However, they do not fully exploit the sensing limits of mmWave in terms of fine-grained material classification. In this paper, we propose FG-LiquID, an accurate and robust system for fine-grained liquid identification. To achieve the desired fine granularity, FG-LiquID first focuses on the small but informative region of the mmWave spectrum, so as to extract the most discriminative features of liquids. Then we design a novel neural network, which uncovers and leverages the hidden signal patterns across multiple antennas on mmWave sensors. In this way, FG-LiquID learns to calibrate signals and finally eliminate the adverse effect of location interference caused by minor displacement/rotation of the liquid container, which ensures robust identification towards daily usage scenarios. Extensive experimental results using a custom-build prototype demonstrate that FG-LiquID can accurately distinguish 30 different liquids with an average accuracy of 97%, under 5 different scenarios. More importantly, it can discriminate quite similar liquids, such as liquors with the difference of only 1% alcohol concentration by volume.</p>", "Keywords": "60GHz mmWave; Contactless Sensing; FMCW Radar; Liquid Identification; Material Identification; Neural Networks; Wireless Sensing", "DOI": "10.1145/3478075", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 5, "Name": "Huadong Ma", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}], "References": [{"Title": "Real-time Arm Gesture Recognition in Smart Home Scenarios via Millimeter Wave Sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 90470442, "Title": "SyncUp", "Abstract": "<p>The beauty of synchronized dancing lies in the synchronization of body movements among multiple dancers. While dancers utilize camera recordings for their practice, standard video interfaces do not efficiently support their activities of identifying segments where they are not well synchronized. This thus fails to close a tight loop of an iterative practice process (i.e., capturing a practice, reviewing the video, and practicing again). We present SyncUp, a system that provides multiple interactive visualizations to support the practice of synchronized dancing and liberate users from manual inspection of recorded practice videos. By analyzing videos uploaded by users, SyncUp quantifies two aspects of synchronization in dancing: pose similarity among multiple dancers and temporal alignment of their movements. The system then highlights which body parts and which portions of the dance routine require further practice to achieve better synchronization. The results of our system evaluations show that our pose similarity estimation and temporal alignment predictions were correlated well with human ratings. Participants in our qualitative user evaluation expressed the benefits and its potential use of SyncUp, confirming that it would enable quick iterative practice.</p>", "Keywords": "Synchronized dancing; computer vision; practice support; visualization", "DOI": "10.1145/3478120", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Interactive Intelligent Systems Lab., The University of Tokyo, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Interactive Intelligent Systems Lab., The University of Tokyo, Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Interactive Intelligent Systems Lab., The University of Tokyo, Tokyo, Japan"}], "References": [{"Title": "Dance Interactive Learning Systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 90470444, "Title": "Self-supervised Learning for Reading Activity Classification", "Abstract": "<p>Reading analysis can relay information about user's confidence and habits and can be used to construct useful feedback. A lack of labeled data inhibits the effective application of fully-supervised Deep Learning (DL) for automatic reading analysis. We propose a Self-supervised Learning (SSL) method for reading analysis. Previously, SSL has been effective in physical human activity recognition (HAR) tasks, but it has not been applied to cognitive HAR tasks like reading. We first evaluate the proposed method on a four-class classification task on reading detection using electrooculography datasets, followed by an evaluation of a two-class classification task of confidence estimation on multiple-choice questions using eye-tracking datasets. Fully-supervised DL and support vector machines (SVMs) are used as comparisons for the proposed SSL method. The results show that the proposed SSL method is superior to the fully-supervised DL and SVM for both tasks, especially when training data is scarce. This result indicates the proposed method is the superior choice for reading analysis tasks. These results are important for informing the design of automatic reading analysis platforms.</p>", "Keywords": "Self-supervised learning; confidence estimation; fully-supervised deep learning; reading analysis; reading detection", "DOI": "10.1145/3478088", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Osaka Prefecture University, Sakai, Japan and BSMRSTU, Gopalganj, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Osaka Prefecture University, Sakai, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Osaka Prefecture University, Sakai, Japan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Osaka Prefecture University, Sakai, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Osaka Prefecture University, Sakai, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Osaka Prefecture University, Sakai, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Osaka Prefecture University, Sakai, Japan"}], "References": []}, {"ArticleId": 90470497, "Title": "Hybrid autonomous controller for bipedal robot balance with deep reinforcement learning and pattern generators", "Abstract": "Recovering after an abrupt push is essential for bipedal robots in real-world applications within environments where humans must collaborate closely with robots. There are several balancing algorithms for bipedal robots in the literature, however most of them either rely on hard coding or power-hungry algorithms. We propose a hybrid autonomous controller that hierarchically combines two separate, efficient systems, to address this problem. The lower-level system is a reliable, high-speed, full state controller that was hardcoded on a microcontroller to be power efficient. The higher-level system is a low-speed reinforcement learning controller implemented on a low-power onboard computer. While one controller offers speed, the other provides trainability and adaptability. An efficient control is then formed without sacrificing adaptability to new dynamic environments. Additionally, as the higher-level system is trained via deep reinforcement learning, the robot could learn after deployment, which is ideal for real-world applications. The system’s performance is validated with a real robot recovering after a random push in less than 5 s, with minimal steps from its initial positions. The training was conducted using simulated data.", "Keywords": "Bipedal robot ; Pattern generator ; Reinforcement learning ; Hybrid controller", "DOI": "10.1016/j.robot.2021.103891", "PubYear": 2021, "Volume": "146", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Loughborough University, Loughborough, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Loughborough University, Loughborough, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Loughborough University, Loughborough, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Sport, Exercise and Health Sciences, Loughborough University, Loughborough, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Motion Robotics LTD, Southampton, UK"}], "References": []}, {"ArticleId": 90470544, "Title": "Frontcover", "Abstract": "", "Keywords": "", "DOI": "10.1109/TAI.2021.3109997", "PubYear": 2021, "Volume": "2", "Issue": "3", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [], "References": []}, {"ArticleId": 90470545, "Title": "Reducing Muscle Activity when Playing Tremolo by Using Electrical Muscle Stimulation to Learn Efficient Motor Skills", "Abstract": "<p>When beginners play the piano, the activity of the forearm muscles tends to be greater than that of experts because beginners move their fingers with more force than necessary. Reducing forearm muscle activity is important for pianists to prevent fatigue and injury. However, it is difficult for beginners to learn how to do so by themselves. We propose using electrical muscle stimulation (EMS) to teach beginners how to reduce this muscle activity while playing a tremolo: a rapid alternation between two notes. Since experts use wrist rotation efficiently when playing tremolos, we propose an EMS-based support system that applies EMS not to muscles that are relevant to moving the fingers but to the supinator and pronator teres muscles, which are involved in wrist rotation. We conducted a user study with 16 beginners to investigate how the forearm muscle activity on the extensor pollicis longus and digitorum muscles changed when using our EMS-based support system. We divided the participants into two groups: an experimental group who practiced by themselves with EMS and a control group who practiced by themselves without EMS and then practiced with instruction. When practicing by themselves, practicing with EMS was more effective than that without EMS; the activity levels of the extensor pollicis longus and digitorum muscles were significantly lower with EMS, and the participants felt less fatigue when playing tremolos. By comparing the improvement in reducing muscle activity between practicing with EMS and practicing with instruction, there was no significant difference. The results suggest that our EMS-based support system can reduce target muscle activity by applying EMS to other muscles to teach beginners how to move limbs efficiently.</p>", "Keywords": "electrical muscle stimulation; electromyography; muscle activity; piano; tremolo", "DOI": "10.1145/3478110", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NTT Human Informatics Laboratories, NTT Corporation, Yokosuka, Kanagawa, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NTT Human Informatics Laboratories, NTT Corporation, Yokosuka, Kanagawa, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "NTT Basic Research Laboratories, Bio-Medical Informatics Research Center, NTT Corporation, Atsugi, Kanagawa, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NTT Human Informatics Laboratories, NTT Corporation, Yokosuka, Kanagawa, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NTT Human Informatics Laboratories, NTT Corporation, Yokosuka, Kanagawa, Japan"}], "References": []}, {"ArticleId": 90470551, "Title": "A City-Wide Crowdsourcing Delivery System with Reinforcement Learning", "Abstract": "<p>The revolution of online shopping in recent years demands corresponding evolution in delivery services in urban areas. To cater to this trend, delivery by the crowd has become an alternative to the traditional delivery services thanks to the advances in ubiquitous computing. Notably, some studies use public transportation for crowdsourcing delivery, given its low-cost delivery network with millions of passengers as potential couriers. However, multiple practical impact factors are not considered in existing public-transport-based crowdsourcing delivery studies due to a lack of data and limited ubiquitous computing infrastructures in the past. In this work, we design a crowdsourcing delivery system based on public transport, considering the practical factors of time constraints, multi-hop delivery, and profits. To incorporate the impact factors, we build a reinforcement learning model to learn the optimal order dispatching strategies from massive passenger data and package data. The order dispatching problem is formulated as a sequential decision making problem for the packages routing, i.e., select the next station for the package. A delivery time estimation module is designed to accelerate the training process and provide statistical delivery time guarantee. Three months of real-world public transportation data and one month of package delivery data from an on-demand delivery platform in Shenzhen are used in the evaluation. Compared with existing crowdsourcing delivery algorithms and widely used baselines, we achieve a 40% increase in profit rates and a 29% increase in delivery rates. Comparison with other reinforcement learning algorithms shows that we can improve the profit rate and the delivery rate by 9% and 8% by using time estimation in action filtering. We share the data used in the project to the community for other researchers to validate our results and conduct further research.1 [1].</p>", "Keywords": "Crowdsourced Labor; Crowdsourcing; Reinforcement Learning; Sharing Economy", "DOI": "10.1145/3478117", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Alibaba Group, Shanghai, China, University of Minnesota, Minneapolis, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Southeast University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Central South University, Changsha, China"}, {"AuthorId": 4, "Name": "Mingming Lu", "Affiliation": "Central South University, Changsha, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Rutgers University, United States"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Southeast University, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "DGIST, Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Alibaba Group, Shanghai, China, University of Minnesota, Minneapolis, United States"}], "References": []}, {"ArticleId": 90470553, "Title": "Fall Detection via Inaudible Acoustic Sensing", "Abstract": "<p>The fall detection system is of critical importance in protecting elders through promptly discovering fall accidents to provide immediate medical assistance, potentially saving elders' lives. This paper aims to develop a novel and lightweight fall detection system by relying solely on a home audio device via inaudible acoustic sensing, to recognize fall occurrences for wide home deployment. In particular, we program the audio device to let its speaker emit 20kHz continuous wave, while utilizing a microphone to record reflected signals for capturing the Doppler shift caused by the fall. Considering interferences from different factors, we first develop a set of solutions for their removal to get clean spectrograms and then apply the power burst curve to locate the time points at which human motions happen. A set of effective features is then extracted from the spectrograms for representing the fall patterns, distinguishable from normal activities. We further apply the Singular Value Decomposition (SVD) and K-mean algorithms to reduce the data feature dimensions and to cluster the data, respectively, before input them to a Hidden Markov Model for training and classification. In the end, our system is implemented and deployed in various environments for evaluation. The experimental results demonstrate that our system can achieve superior performance for detecting fall accidents and is robust to environment changes, i.e., transferable to other environments after training in one environment.</p>", "Keywords": "Device-free; Fall Detection; Hidden Markov Model; Ultrasonic", "DOI": "10.1145/3478094", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Louisiana at Lafayette, Louisiana, Lafayette, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Louisiana at Lafayette, Louisiana, Lafayette, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The University of Texas at Arlington, Arlington, Texas, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Louisiana at Lafayette, Louisiana, Lafayette, USA"}], "References": []}, {"ArticleId": 90470579, "Title": "Proceedings 37th International Conference on Logic Programming (Technical Communications)", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.10", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Stony Brook University"}], "References": []}, {"ArticleId": 90470580, "Title": "Proceedings 37th International Conference on Logic Programming (Technical Communications)", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.4", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of California, Berkeley"}], "References": []}, {"ArticleId": 90470582, "Title": "Proof of Principle of a Fuel Injector Based on a Magnetostrictive Actuator", "Abstract": "<p>One of the goals of modern internal combustion engines is the NOx-soot trade-off, and this would be better achieved by a better control of the fuel injection. Moreover, this feature can be also useful for high-performance hydraulic systems. Actual fuel injection technology either allows only the control of the injection time or it is based on very complex mechanical-hydraulic systems, as in the case of piezo-actuators. This work describes the basic steps that brought the authors to the realization of a concept fuel injector based on a Terfenol-D magnetostrictive actuator that could overcome the previous issues, being both simple and controllable. The study provides the design, development, and a feasibility analysis of a magnetostrictive actuator for fuel injection, by providing a basic magneto-static analysis of the actuator, the adaptation of a suitable standard fuel injector, and its experimental testing in a lab environment, with different shapes and amplitude of the reference signal to follow.</p>", "Keywords": "fuel injection; magnetostrictive actuators; experimental measurements fuel injection ; magnetostrictive actuators ; experimental measurements", "DOI": "10.3390/act10090237", "PubYear": 2021, "Volume": "10", "Issue": "9", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Istituto di Scienze e Tecnologie per l’Energia e la Mobilità Sostenibili (STEMS)-CNR, 80125 Napoli, Italy↑These authors contributed equally to this work. Academic Editor: <PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Ingegneria, Università degli Studi del Sannio, 82100 Benevento, Italy↑Author to whom correspondence should be addressed.↑These authors contributed equally to this work. Academic Editor: <PERSON><PERSON>"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Istituto di Scienze e Tecnologie per l’Energia e la Mobilità Sostenibili (STEMS)-CNR, 80125 Napoli, Italy↑These authors contributed equally to this work. Academic Editor: <PERSON><PERSON>"}, {"AuthorId": 4, "Name": "Ciro Visone", "Affiliation": "Dipartimento di Ingegneria Elettrica e delle Tecnologie dell’Informazione, Università degli Studi di Napoli Federico II, 80125 Napoli, Italy↑These authors contributed equally to this work. Academic Editor: <PERSON><PERSON>"}], "References": [{"Title": "Modeling and Experimental Study of Oil-Cooled Stacked Giant Magnetostrictive Actuator for Servo Valve", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "2", "Page": "37", "JournalTitle": "Actuators"}]}, {"ArticleId": 90470694, "Title": "Client-aided Robust Bit-composition Protocol with Deterministic Cheater Identification in Standard Model", "Abstract": "Secure multiparty computation (MPC) enables parties to compute an arbitrary function without revealing each party’s inputs. A typical MPC is secret-sharing based MPC (SS-MPC). In the SS-MPC, each party distributes its inputs, and the computation proceeds with secret shares that look exactly like random numbers distributed among the parties. In the SS-MPC protocol, the parties can compute any function represented as a circuit by using shares locally and communicating among the parties. In particular, when the parties compute a complex function composed of binary and arithmetic circuits, an efficient share conversion protocol facilitates the computation of it. An important conversion protocol is a bit-composition protocol that converts a k-dimensional vector with shares on ℤ2 (i.e., shares of binary sequence) to shares on ℤ2k (i.e., shares of decimal value). Previous studies proposed a maliciously secure bit-composition protocol with robustness, which is a security notion that all parties learn the correct output regardless of the attacker’s behaviour. However, its security is dependent on a statistical parameter or proved in the random oracle model. In this paper, we propose a novel bit-composition protocol with robustness independent of a statistical parameter by introducing additional clients generating the pair of shares of random values only in the offline phase (which can be performed without the parties’ inputs). Our protocol is based on a maliciously secure four-party protocol with one corruption using replicated secret sharing. The security of our protocol is proved in the standard model (which is a weaker assumption than the random oracle model). Our protocol achieves efficiency and the strongest security simultaneously. We also propose a protocol for the Hamming distance with robustness by modifying our bit-composition protocol. It can achieve a secure iris recognition service via MPC with robustness. Furthermore, we extend our protocol with a constant number of parties and clients to one with an arbitrary number of parties and clients. © 2021, Information Processing Society of Japan. All rights reserved.", "Keywords": "Bit-composition protocol; Robustness; Secret sharing; Secure multiparty computation", "DOI": "10.2197/ipsjjip.29.515", "PubYear": 2021, "Volume": "29", "Issue": "", "JournalId": 18954, "JournalTitle": "Journal of Information Processing", "ISSN": "", "EISSN": "1882-6652", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NEC Corporation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Tsukuba"}], "References": [{"Title": "FLASH: Fast and Robust Framework for Privacy-preserving Machine Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "2", "Page": "459", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "FLASH: Fast and Robust Framework for Privacy-preserving Machine Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "2", "Page": "459", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "Secure Evaluation of Quantized Neural Networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "4", "Page": "355", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}]}, {"ArticleId": 90470731, "Title": "How Should Automated Vehicles Communicate Critical Situations?", "Abstract": "<p>Passengers of automated vehicles will likely engage in non-driving related activities like reading and, therefore, be disengaged from the driving task. However, especially in critical situations such as unexpected pedestrian crossings, it can be assumed that passengers request information about the vehicle's intention and an explanation. Some concepts were proposed for such communication from the automated vehicle to the passenger. However, results are not comparable due to varying information content and scenarios. We present a comparative study in Virtual Reality (N=20) of four visualization concepts and a baseline with Augmented Reality, a Head-Up Display, or Lightbands. We found that all concepts were rated reasonable and necessary and increased trust, perceived safety, perceived intelligence, and acceptance compared to no visualization. However, when visualizations were compared, there were hardly any significant differences between them.</p>", "Keywords": "Autonomous vehicles; interface design", "DOI": "10.1145/3478111", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Media Informatics, Ulm University, Ulm, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Media Informatics, Ulm University, Ulm, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Psychology and Education, Dept. Human Factors, Ulm University, Ulm, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Media Informatics, Ulm University, Ulm, Germany"}], "References": [{"Title": "The More You Know: Trust Dynamics and Calibration in Highly Automated Driving and the Effects of Take-Overs, System Malfunction, and System Transparency", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "5", "Page": "718", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Evaluations of Different Human Machine Interfaces for Presenting Right-Turn Timing at Intersections", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "19", "Issue": "1", "Page": "71", "JournalTitle": "International Journal of Intelligent Transportation Systems Research"}]}, {"ArticleId": 90470732, "Title": "DualRing", "Abstract": "<p>We present DualRing, a novel ring-form input device that can capture the state and movement of the user's hand and fingers. With two IMU rings attached to the user's thumb and index finger, DualRing can sense not only the absolute hand gesture relative to the ground but also the relative pose and movement among hand segments. To enable natural thumb-to-finger interaction, we develop a high-frequency AC circuit for on-body contact detection. Based on the sensing information of DualRing, we outline the interaction space and divide it into three sub-spaces: within-hand interaction, hand-to-surface interaction, and hand-to-object interaction. By analyzing the accuracy and performance of our system, we demonstrate the informational advantage of DualRing in sensing comprehensive hand gestures compared with single-ring-based solutions. Through the user study, we discovered the interaction space enabled by DualRing is favored by users for its usability, efficiency, and novelty.</p>", "Keywords": "hand gesture sensing; hand interaction; ring form device", "DOI": "10.1145/3478114", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Technology, Key Laboratory of Pervasive Computing, Ministry of Education, Tsinghua University, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Technology, Key Laboratory of Pervasive Computing, Ministry of Education, Tsinghua University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Key Laboratory of Pervasive Computing, Ministry of Education, Tsinghua University, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Key Laboratory of Pervasive Computing, Ministry of Education, Tsinghua University, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Key Laboratory of Pervasive Computing, Ministry of Education, Tsinghua University, China"}], "References": [{"Title": "Designing and Evaluating Hand-to-Hand Gestures with Dual Commodity Wrist-Worn Devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Keep the Phone in Your Pocket", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "QwertyRing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 90470733, "Title": "ALWAES", "Abstract": "<p>For an online delivery platform, accurate physical locations of merchants are essential for delivery scheduling. It is challenging to maintain tens of thousands of merchant locations accurately because of potential errors introduced by merchants for profits (e.g., potential fraud). In practice, a platform periodically sends a dedicated crew to survey limited locations due to high workforce costs, leaving many potential location errors. In this paper, we design and implement ALWAES, a system that automatically identifies and corrects location errors based on fundamental tradeoffs of five measurement strategies from manual, physical, and virtual data collection infrastructures for online delivery platforms. ALWAES explores delivery data already collected by platform infrastructures to measure the travel time of couriers between merchants and verify all merchants' locations by cross-validation automatically. We explore tradeoffs between performance and cost of different measurement approaches. By comparing with the manually-collected ground truth, the experimental results show that ALWAES outperforms three other baselines by 32.2%, 41.8%, and 47.2%, respectively. More importantly, ALWAES saves 3,846 hours of the delivery time of 35,005 orders in a month and finds new erroneous locations that initially were not in the ground truth but are verified by our field study later, accounting for 3% of all merchants with erroneous locations.</p>", "Keywords": "CrowdSourcing; Localization; Machine Learning; Online delivery", "DOI": "10.1145/3478081", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "Dongz<PERSON> Jiang", "Affiliation": "Peking University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Alibaba Group, Shanghai, China, University of Minnesota, Minneapolis, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Alibaba Group, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Peking University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Alibaba Group, Shanghai, China, University of Minnesota, Minneapolis, United States"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Lehigh University, Bethlehem, Pennsylvania, United States"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Rutgers University, New Jersey, United States"}], "References": []}, {"ArticleId": 90470763, "Title": "A Review of Tabular Data Synthesis Using GANs on an IDS Dataset", "Abstract": "<p>Recent technological innovations along with the vast amount of available data worldwide have led to the rise of cyberattacks against network systems. Intrusion Detection Systems (IDS) play a crucial role as a defense mechanism in networks against adversarial attackers. Machine Learning methods provide various cybersecurity tools. However, these methods require plenty of data to be trained efficiently, which may be hard to collect or to use due to privacy reasons. One of the most notable Machine Learning tools is the Generative Adversarial Network (GAN), and it has great potential for tabular data synthesis. In this work, we start by briefly presenting the most popular GAN architectures, VanillaGAN, WGAN, and WGAN-GP. Focusing on tabular data generation, CTGAN, CopulaGAN, and TableGAN models are used for the creation of synthetic IDS data. Specifically, the models are trained and evaluated on an NSL-KDD dataset, considering the limitations and requirements that this procedure needs. Finally, based on certain quantitative and qualitative methods, we argue and evaluate the most prominent GANs for tabular network data synthesis.</p>", "Keywords": "GAN; tabular data generation; synthetic dataset; NSL-KDD dataset; IDS GAN ; tabular data generation ; synthetic dataset ; NSL-KDD dataset ; IDS", "DOI": "10.3390/info12090375", "PubYear": 2021, "Volume": "12", "Issue": "9", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Synelixis Solutions S.A., 34100 <PERSON><PERSON><PERSON>, Greece ↑ Author to whom correspondence should be addressed. Academic Editors: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Synelixis Solutions S.A., 34100 Chalkida, Greece"}, {"AuthorId": 3, "Name": "Terpsichori-<PERSON>", "Affiliation": "Synelixis Solutions S.A., 34100 Chalkida, Greece"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Synelixis Solutions S.A., 34100 Chalkida, Greece"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Synelixis Solutions S.A., 34100 Chalkida, Greece"}], "References": [{"Title": "Analysis of KDD-Cup’99, NSL-KDD and UNSW-NB15 Datasets using Deep Learning in IoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "1561", "JournalTitle": "Procedia Computer Science"}, {"Title": "Application of Blockchain Technology in Dynamic Resource Management of Next Generation Networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "12", "Page": "570", "JournalTitle": "Information"}, {"Title": "Pros and cons of GAN evaluation measures: New developments", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "215", "Issue": "", "Page": "103329", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 90470776, "Title": "Proceedings 37th International Conference on Logic Programming (Technical Communications)", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.1", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Google AI"}], "References": []}, {"ArticleId": 90470854, "Title": "ISACS", "Abstract": "<p>90% of retail sales occur in physical stores. In these physical stores 40% of shoppers leave the store based on the wait time. Autonomous stores can remove customer waiting time by providing a receipt without the need for scanning the items. Prior approaches use computer vision only, combine computer vision with weight sensors, or combine computer vision with sensors and human product recognition. These approaches, in general, suffer from low accuracy, up to hour long delays for receipt generation, or do not scale to store level deployments due to computation requirements and real-world multiple shopper scenarios.</p><p>We present ISACS, which combines a physical store model (e.g. customers, shelves, and item interactions), multi-human 3D pose estimation, and live inventory monitoring to provide an accurate matching of multiple people to multiple products. ISACS utilizes only shelf weight sensors and does not require visual inventory monitoring which drastically reduces the computational requirements and thus is scalable to a store-level deployment. In addition, ISACS generates an instant receipt by not requiring human intervention during receipt generation. To fully evaluate the ISACS, we deployed and evaluated our approach in an operating convenience store covering 800 square feet with 1653 distinct products, and more than 20,000 items. Over the course of 13 months of operation, ISACS achieved a receipt daily accuracy of up to 96.4%. Which translates to a 3.5x reduction in error compared to self-checkout stations.</p>", "Keywords": "autonomous checkout; human tracking; instant receipt; inventory monitoring; retail; sensor fusion", "DOI": "10.1145/3478086", "PubYear": 2021, "Volume": "5", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Moffett Field, California, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "AiFi Inc. Santa Clara, California, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Moffett Field, California, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Stanford University, Stanford, California, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Michigan, Ann Arbor, Michigan, USA"}], "References": [{"Title": "A self-attention-based destruction and construction learning fine-grained image classification method for retail product recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> Li", "PubYear": 2020, "Volume": "32", "Issue": "18", "Page": "14613", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 90470869, "Title": "How resource utilization influences UI responsiveness of Android software", "Abstract": "Context: The rapid responsiveness of smartphones is critical to user experience. Excessive resource utilization is typically considered as one of the major factors leading to laggy-UI. Much work focuses on modifying the design of Android systems and software to reduce their resource utilization. However, laggy-UI is still quite common on Android devices, especially the low-end ones. One reason is that developers still lack a clear understanding about how the utilization of various resources ( e.g. , CPU and memory) affects Android responsiveness, which leads to the inadequacy of existing performance optimization measures. Objective: The objective of this paper is to obtain a systematical understanding of how the utilization of various resources ( e.g. , CPU and memory) affects Android responsiveness. Then accordingly, we aim to figure out the root cause(s) of laggy-UI. Methods: First, we conduct a set of controlled experiments on two Android devices with a stress test tool. Second, we further test 36 real-life Android software to study whether the competition of resource(s), the root factor(s) causing laggy-UI, is severe in real-life scenarios. Results: The experimental results show that CPU competition is the root cause and other resources have no observable impact on Android responsiveness, except in extreme cases, e.g. , utilization reaches almost 100%. We also find out CPU competition is quite common for existing Android software when it is running in the background. Conclusion: Through stress testing and real-life Android software testing, this work unveils that CPU competition should be the main problem to be solved. Our experimental results deepen and update previous perceptions of resources’ impact on Android responsiveness. Based on these findings, we provide a set of suggestions for designing high-performance Android systems and software, and effective performance optimization tools.", "Keywords": "Resource utilization ; Android performance ; Laggy-UI ; Empirical study", "DOI": "10.1016/j.infsof.2021.106728", "PubYear": 2022, "Volume": "141", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "Jiaojiao Fu", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China Shanghai Key Laboratory of Intelligent Information Processing, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China Shanghai Key Laboratory of Intelligent Information Processing, Shanghai, China;@fudan.edu.cn"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China Shanghai Key Laboratory of Intelligent Information Processing, Shanghai, China;Corresponding author at: School of Computer Science, Fudan University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China Shanghai Key Laboratory of Intelligent Information Processing, Shanghai, China"}], "References": [{"Title": "Characterizing the evolution of statically-detectable performance issues of Android apps", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "4", "Page": "2748", "JournalTitle": "Empirical Software Engineering"}]}, {"ArticleId": 90470917, "Title": "Proceedings 37th International Conference on Logic Programming (Technical Communications)", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.29", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90470968, "Title": "AHNA: Adaptive representation learning for attributed heterogeneous networks", "Abstract": "<p>Meta-path-based random walk strategy has attracted tremendous attention in heterogeneous network representation, which can capture network semantics with heterogeneous neighborhoods of nodes. Despite the success of meta-path-based random walk strategy in plain heterogeneous networks which contain no attributes, it remains unexplored how meta-path-based random walk strategy could be utilized on attributed heterogeneous networks to simultaneously capture structural heterogeneity and attribute proximity. Moreover, the importance of node attributes and structural relations generally varies across data sets, thus requiring careful considerations when they are incorporated into representations. To tackle these problems, we propose a novel method, A ttributed H eterogeneous N etwork embedding based on A ggregate-path (AHNA), which generates aggregate-path-based random walks on attributed heterogeneous networks and adaptively fuses topological structures and node attributes based on the learned importance. Specifically, AHNA first converts node attributes to additional links in the network to deal with the heterogeneity of structures and attributes, which is followed by an adaptive random walk strategy to strike the importance balance between node attributes and topological structures, thereby generating high-quality representations. Extensive experiments are conducted on three real-world data sets, where AHNA outperforms state-of-the-art approaches by up to 22.7%, 2.6%, and 2.3% on link prediction, community detection, and node classification, respectively. Moreover, our qualitative analysis indicates that AHNA can capture different balances of topological structures and node attributes on various data sets and thus boost the quality of node representations.</p>", "Keywords": "adaptive;attributed heterogeneous network;balance;network representation", "DOI": "10.1002/int.22664", "PubYear": 2022, "Volume": "37", "Issue": "2", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 3, "Name": "Xingxing Xing", "Affiliation": "Netease Games UX Center, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, National University of Defense Technology, Changsha, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou, China"}], "References": [{"Title": "CMG2Vec: A composite meta-graph based heterogeneous information network embedding approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106661", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "WSDM'21", "Authors": "<PERSON>; <PERSON><PERSON>-<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "Summer", "Page": "1", "JournalTitle": "ACM SIGWEB Newsletter"}]}, {"ArticleId": 90470980, "Title": "АНАЛИЗ РАВНОДОЛЕВОГО И УРАВНИТЕЛЬНОГО РАСПРЕДЕЛЕНИЯ ПОТОКОВ ПРИ МАКСИМАЛЬНОЙ ЗАГРУЗКЕ МНОГОПОЛЬЗОВАТЕЛЬСКОЙ СЕТИ", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0002338821050140", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 9501, "JournalTitle": "Известия Российской академии наук. Теория и системы управления", "ISSN": "0002-3388", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Ю. <PERSON><PERSON><PERSON>н<PERSON>о", "Affiliation": ""}, {"AuthorId": 2, "Name": "И. А<PERSON> Назарова", "Affiliation": ""}], "References": []}, {"ArticleId": 90470990, "Title": "О МАТЕМАТИЧЕСКОМ МОДЕЛИРОВАНИИ ДИНАМИКИ МНОГОЗВЕННЫХ СИСТЕМ И ЭКЗОСКЕЛЕТОВ", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0002338821040028", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 9501, "JournalTitle": "Известия Российской академии наук. Теория и системы управления", "ISSN": "0002-3388", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90470994, "Title": "СТАБИЛИЗАЦИЯ ЛИНЕЙНЫХ ДИНАМИЧЕСКИХ ОБЪЕКТОВ ПО ИЗМЕРЯЕМОМУ С ОШИБКОЙ СОСТОЯНИЮ ПРИ ОГРАНИЧЕНИЯХ НА ФАЗОВЫЕ И УПРАВЛЯЮЩИЕ ПЕРЕМЕННЫЕ", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0002338821050036", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 9501, "JournalTitle": "Известия Российской академии наук. Теория и системы управления", "ISSN": "0002-3388", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90471011, "Title": "Influence of Social Media and Online Reviews on University Students Purchasing Decisions", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIMA.2021.10041137", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 16124, "JournalTitle": "International Journal of Internet Marketing and Advertising", "ISSN": "1477-5212", "EISSN": "1741-8100", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90471124, "Title": "Generating Concurrent Programs From Sequential Data Structure Knowledge Using Answer Set Programming", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.36", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Texas at Dallas"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Texas at Dallas"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Texas at Dallas"}], "References": []}, {"ArticleId": 90471130, "Title": "HVEI Conference Overview and Papers Program", "Abstract": "", "Keywords": "", "DOI": "10.2352/ISSN.2470-1173.2021.11.HVEI-A11", "PubYear": 2021, "Volume": "33", "Issue": "11", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 90471544, "Title": "Advanced optical methods for safe image reproduction on automotive displays", "Abstract": "<p>Modern cars are equipped with camera monitor systems (CMSs), such as a backup camera or side-mirror replacement. These systems are expected to perform optimally and achieve high safety levels (ASIL). Currently, only digital data are supervised in CMSs and safety mechanisms for such systems are individually derived on a case-by-case basis which is not effective. This study proposes generic optical supervision for displays of automotive CMS. This paper introduces “light-to-light” (camera to display output) protection for both in-car CMS and remote operator monitors used in autonomous car fleet operation centers. The first method is based on photodiodes attached to the display to optically supervise, for instance, the speedometer of vehicles. By combining intensities of photodiodes with calibration data, we can compare the measured speed with the value from CAN (Controller Area Network) data. The second method that entails capturing the display content using a camera enables top safety levels for both in-car displays and remote operator monitors. This safeguarding was successfully verified by conventional image processing and artificial intelligence (AI)-based analysis methods. Our results demonstrate that AI methods allow a substantial reduction in the wireless transmission bandwidth from a car to a remote operator compared with conventional image processing.</p>", "Keywords": "artificial intelligence;automotive displays;camera monitor systems;functional safety;machine learning;object recognition;optical measurement", "DOI": "10.1002/jsid.1079", "PubYear": 2022, "Volume": "30", "Issue": "1", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [{"AuthorId": 1, "Name": "Karlheinz Blankenbach", "Affiliation": "Display Lab, Pforzheim University, Pforzheim, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Display Lab, Pforzheim University, Pforzheim, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Artificial Intelligence Lab, Pforzheim University, Pforzheim, Germany"}], "References": [{"Title": "Advanced methods for safe visualization on automotive displays", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "6", "Page": "483", "JournalTitle": "Journal of the Society for Information Display"}]}, {"ArticleId": 90471625, "Title": "Comparison and Transition of Research Focus on Application of IT in Education", "Abstract": "<p>The application of Information Technology (IT) in education has opened new scenarios for this ancient process. With rapidly changing field of IT, the adoption of IT in education has been changed drastically. It is quite difficult for researchers to keep pace with changing research trends. An analysis based on the keywords could provide a synopsis on the use of IT in education. The keywords can be extracted and clustered to draw a sketch of trend changes over time. In this paper, we propose two empirical methods based on classic TF/IDF, i.e., overall rating (OR) and dynamic character (DC) of a keyword for in-depth keyword analysis to examine changing trends in research. The method help in disclosing time based changes in research focuses by comparing TF/IDF weights of keywords in different years. A total of 8131 scholarly articles from 12 well recognized journals were used in this analysis. The analysis shows that proposed methods provide sufficient insight into the research trends of application of IT in education in 11 years, i.e., 2007-2017.</p>", "Keywords": "", "DOI": "10.4018/IJICTE.288545", "PubYear": 2022, "Volume": "18", "Issue": "1", "JournalId": 22087, "JournalTitle": "International Journal of Information and Communication Technology Education", "ISSN": "1550-1876", "EISSN": "1550-1337", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "South China Normal University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Akram", "Affiliation": "South China Normal University, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "South China Normal University, China"}], "References": []}, {"ArticleId": 90471722, "Title": "Evaluating State Universities Websites Visibility in One Philippine Region Using Search Engine Optimization Tools", "Abstract": "This study analyses the visibility of the State Universities' website in Region 4A, Philippines during the pandemic wherein universities halted most of the physically performed transactions and started the delivery of services online through their websites. An attempt has been taken to help web content creators and developers to make their websites visible and easier to find in search engines. The websites' visibility was measured by collecting data through Search Engine Optimization tools, specifically Alexa, CheckPageRank, SEO Analyzer, and Moz Link Explorer. The collected data are the websites' global and national traffic rank, domain authority, PageRank, loading time and speed, daily pageviews, bounce rate, daily time on site, and site linking in. Websites were ranked based on how good or bad their SEO tool results. The analyzed result suggests that UPLB performed the most optimization since it ranks first in most of the tools. It was followed by BatSU and CvSU websites which consistently placed second and third in the rank list, respectively. The LSPU website occupies fourth place in the rank list, the SLSU website in fifth place, and the URS website in the last place. This study suggests that the least performing websites need to establish more high-quality website links and to create more user-needed contents to avoid an increase in bounce rate.", "Keywords": "Search Engine Optimization; Management Information System; Web Service and Web Data Management; Website Visibility; State Universities", "DOI": "10.13189/csit.2021.090201", "PubYear": 2021, "Volume": "9", "Issue": "2", "JournalId": 38898, "JournalTitle": "Computer Science and Information Technology", "ISSN": "2331-6063", "EISSN": "2331-6071", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Jr. N. Del Rosario", "Affiliation": "College of Computer Studies, Laguna State Polytechnic University, Brgy. Del Remedio, San Pablo City 4000, Laguna, Philippines"}], "References": []}, {"ArticleId": 90471735, "Title": "Normalised stabilisation for singular semi-Markov jump systems with discontinuous jump states", "Abstract": "This paper addresses the almost surely exponential (ASE) stabilisation problem of continuous-time singular semi-Markov jump systems (s-MJSs), where the state at jump instants is discontinuous. Because of such a discontinuity existing, the consistency of initial condition at jump instants is impossible to be ensured but is an essential issue of singular systems. A proportional-derivative (PD) state feedback controller is proposed to deal with it such that the closed-loop singular s-MJSs is normal and ASE-stable. New sufficient conditions for the existence of such a controller are derived as linear matrix inequalities, which includes some existing considered systems as special situations. Two numerical examples are used to show the effectiveness and superiority of the methods.", "Keywords": "Semi-Markovian jump systems ; singular systems ; proportional-derivative controller ; discontinuous jump states ; linear matrix inequalities (LMIs)", "DOI": "10.1080/23307706.2021.1974319", "PubYear": 2022, "Volume": "9", "Issue": "3", "JournalId": 30013, "JournalTitle": "Journal of Control and Decision", "ISSN": "2330-7706", "EISSN": "2330-7714", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Control Engineering, Liaoning Petrochemical University, Fushun, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Control Engineering, Liaoning Petrochemical University, Fushun, People's Republic of China"}], "References": []}, {"ArticleId": 90471766, "Title": "Internet of things-enabled real-time health monitoring system using deep learning", "Abstract": "<p>Smart healthcare monitoring systems are proliferating due to the Internet of Things (IoT)-enabled portable medical devices. The IoT and deep learning in the healthcare sector prevent diseases by evolving healthcare from face-to-face consultation to telemedicine. To protect athletes' life from life-threatening severe conditions and injuries in training and competitions, real-time monitoring of physiological indicators is critical. In this research work, we present a deep learning-based IoT-enabled real-time health monitoring system. The proposed system uses wearable medical devices to measure vital signs and apply various deep learning algorithms to extract valuable information. For this purpose, we have taken Sanda athletes as our case study. The deep learning algorithms help physicians properly analyze these athletes' conditions and offer the proper medications to them, even if the doctors are away. The performance of the proposed system is extensively evaluated using a cross-validation test by considering various statistical-based performance measurement metrics. The proposed system is considered an effective tool that diagnoses dreadful diseases among the athletes, such as brain tumors, heart disease, cancer, etc. The performance results of the proposed system are evaluated in terms of precision, recall, AUC, and F1, respectively.</p><p>© The Author(s), under exclusive licence to Springer-Verlag London Ltd., part of Springer Nature 2021.</p>", "Keywords": "Deep learning;Deep neural network;Diseases;Healthcare system;Internet of things", "DOI": "10.1007/s00521-021-06440-6", "PubYear": 2023, "Volume": "35", "Issue": "20", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Xingdong Wu", "Affiliation": "Physical Education Department, Institute of Disaster Prevention, Langfang, 065201 Hebei China."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Physical Education, Hengshui University, Hengshui, 053000 Hebei China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Physical Education, Soochow University, 215000, Suzhou, Jiangsu China. ;Institute of Physical Education and Health, Yulin Normal University, Yulin, 537000 Guangxi China."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hankuk University of Foreign Studies, Yongin-si, Republic of Korea."}], "References": [{"Title": "A novel hybrid deep learning approach including combination of 1D power signals and 2D signal images for power quality disturbance classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114785", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A model-based collaborate filtering algorithm based on stacked AutoEncoder", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "4", "Page": "2503", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Marginal and average weight-enabled data aggregation mechanism for the resource-constrained networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "101", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 90471777, "Title": "Improving performance and data transmission security in VANETs", "Abstract": "This article proposes a new approach to achieve fast and reliable transfer of data and uses machine learning techniques for data processing to improve the performance and data transmission security of the vehicular network. The proposed approach is the combination of 5G cellular network and alternative data transmission channels. The data collection experiment took place within different areas of the city of Berlin over a 3-month time period and involved the use of 5G technologies. The study carried out the analysis and classification of big data with the help of position-based routing protocols and the Support Vector Machine algorithms. The said techniques were employed to detect non-line-of-sight (NLoS) conditions in real time, which ensure the secure transmission of data without the loss or degradation of network performance. The novelty of the work is that it tackles various traffic scenarios (the extent of road congestion can affect the quality of big data transmission) and offers a way to improve big data transfer using the Support Vector Machine technology. The study results show that the proposed approach is effective enough with big data and can be employed to improve the performance of urban VANET networks and data transmission security. The study results can be useful in developing high-performance 5G-VANET applications to improve traffic safety in urban vehicular environments.", "Keywords": "Vehicular ad hoc networks ; Big data ; Support Vector Machine ; Machine learning ; 5G", "DOI": "10.1016/j.comcom.2021.09.005", "PubYear": 2021, "Volume": "180", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Wenzhou Polytechnic, Wenzhou, Zhejiang Province, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Foreign Languages, Peoples’ Friendship University of Russia (RUDN University), Moscow, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Sakarya University, Esentepe Campus, Sakarya, 54187, Turkey"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering, Sakarya University, Esentepe Campus, Sakarya, 54187, Turkey"}], "References": [{"Title": "Visual saliency guided complex image retrieval", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "64", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "An overview of Internet of Things (IoT): Architectural aspects, challenges, and protocols", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "21", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "IoT transaction processing through cooperative concurrency control on fog–cloud computing environment", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "8", "Page": "5695", "JournalTitle": "Soft Computing"}, {"Title": "Machine Learning Models for Secure Data Analytics: A taxonomy and threat model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "406", "JournalTitle": "Computer Communications"}, {"Title": "Towards secure and practical consensus for blockchain based VANET", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "545", "Issue": "", "Page": "170", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 90471783, "Title": "Deep robust image deblurring via blur distilling and information comparison in latent space", "Abstract": "Current deep deblurring methods pay main attention to learning a transferring network to transfer synthetic blurred images to clean ones. Though achieving significant performance on the training datasets, they still suffer from a weaker generalization capability from training datasets to others with different synthetic blurs, thus resulting in significantly inferior performance on testing datasets. In order to alleviate this problem, we propose a latent contrastive model, Blur Distilling and Information Reconstruction Networks ( BDIRNet ), to learn image prior and improve the robustness of deep deblurring. The proposed BDIRNet consists of a blur removing network ( DistillNet ) and a reconstruction network ( RecNet ). Two kinds of images with almost the same information but different qualities are input into DistillNet to extract identical structure information via contrast latent information and purify the perturbations from other unimportant information like blur. While the RecNet is utilized to reconstruct sharp images based on the extracted information. In addition, inside the DistillNet and RecNet, a statistical anti-interference distilling ( SAID ) and anti-interference reconstruction ( SAIR ) modules are proposed to further enhance the robustness of our methods, respectively. Extensive experiments on different datasets show that the proposed methods achieve improved and robust results compared to recent state-of-the-art methods.", "Keywords": "Image deblur ; Deep network ; Blur distilling ; Information comparison", "DOI": "10.1016/j.neucom.2021.09.019", "PubYear": 2021, "Volume": "466", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering and Computer Science, Australian National University, Canberra 2601, Australia;School of electronic and information engineering, Hebei University of Technology, Tianjin 300401, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering and Computer Science, Australian National University, Canberra 2601, Australia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Tencent AI Lab, Shenzhen 518000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering and Computer Science, Australian National University, Canberra 2601, Australia"}, {"AuthorId": 5, "Name": "Hongdong Li", "Affiliation": "College of Engineering and Computer Science, Australian National University, Canberra 2601, Australia"}], "References": []}, {"ArticleId": 90471839, "Title": "Centrosymmetric‐ and Axisymmetric‐Patterned Flexible Tactile Sensor for Roughness and Slip Intelligent Recognition", "Abstract": "<p>Next-generation robots are being designed to function autonomously in complex and unstructured environments. In particular, based on the real-time measurement and differentiation of normal pressure and shear force, robots can be equipped with the capabilities of damage-free grasp within minimum force limits, as well as dexterous operation through surface roughness and slip information. Herein, a flexible tactile sensor with a small cylinder protrusion and four arc-shaped protrusions is developed. Due to its center symmetry and axisymmetry characteristics, the normal pressure and shear force can be decoupled from the complex stress without any interference from torsion. The flexible tactile sensor exhibits good linearity and superior cycling stability and is capable of determining the magnitude and direction of the applied force accurately. The flexible tactile sensor is comfortable to wear, and it is integrated onto the manipulator to realize various delicate and dexterous tasks, such as pressure detection, interaction with fragile objects, and roughness identification. Moreover, intelligent recognition of the sliding and stationary states can be achieved by decoding signals of sliding friction and static friction from the feedback information, leading to real time and precise adjustment of the grasping state of the manipulator.</p>", "Keywords": "complex stresses;flexible tactile sensors;intelligent recognition;shear forces", "DOI": "10.1002/aisy.202100072", "PubYear": 2022, "Volume": "4", "Issue": "1", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Applied Mechanics Laboratory, Department of Engineering Mechanics, Tsinghua University, Beijing, 100084 China; Center for Flexible Electronics Technology, Tsinghua University, Beijing, 100084 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Future Technology, University of Chinese Academy of Sciences, Beijing, 100049 China; The State Key Laboratory of Management and Control for Complex Systems, Institute of Automation, Chinese Academy of Sciences, Beijing, 100190 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Future Technology, University of Chinese Academy of Sciences, Beijing, 100049 China; The State Key Laboratory of Management and Control for Complex Systems, Institute of Automation, Chinese Academy of Sciences, Beijing, 100190 China"}, {"AuthorId": 4, "Name": "Haibo Li", "Affiliation": "Applied Mechanics Laboratory, Department of Engineering Mechanics, Tsinghua University, Beijing, 100084 China; Center for Flexible Electronics Technology, Tsinghua University, Beijing, 100084 China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Future Technology, University of Chinese Academy of Sciences, Beijing, 100049 China; The State Key Laboratory of Management and Control for Complex Systems, Institute of Automation, Chinese Academy of Sciences, Beijing, 100190 China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Applied Mechanics Laboratory, Department of Engineering Mechanics, Tsinghua University, Beijing, 100084 China; Center for Flexible Electronics Technology, Tsinghua University, Beijing, 100084 China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Institute of Flexible Electronics Technology of THU, Zhejiang, Jiaxing, 314000 China; Qiantang Science and Technology Innovation Center, Hangzhou, 310016 China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Applied Mechanics Laboratory, Department of Engineering Mechanics, Tsinghua University, Beijing, 100084 China; Center for Flexible Electronics Technology, Tsinghua University, Beijing, 100084 China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "School of Future Technology, University of Chinese Academy of Sciences, Beijing, 100049 China; The State Key Laboratory of Management and Control for Complex Systems, Institute of Automation, Chinese Academy of Sciences, Beijing, 100190 China; The Center for Excellence in Brain Science and Intelligence Technology, Chinese Academy of Sciences, Shanghai, 200031 China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Applied Mechanics Laboratory, Department of Engineering Mechanics, Tsinghua University, Beijing, 100084 China; Center for Flexible Electronics Technology, Tsinghua University, Beijing, 100084 China"}], "References": [{"Title": "Wearable and Stretchable Strain Sensors: Materials, Sensing Mechanisms, and Applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "8", "Page": "2000039", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Microporous Induced Fully Printed Pressure Sensor for Wearable Soft Robotics Machine Interfaces", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "12", "Page": "2000179", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Robots learn to identify objects by feeling", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "49", "Page": "eabf1502", "JournalTitle": "Science Robotics"}, {"Title": "Soft sensors that can feel it all", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "49", "Page": "eabf0894", "JournalTitle": "Science Robotics"}, {"Title": "SensAct: The Soft and Squishy Tactile Sensor with Integrated Flexible Actuator", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "3", "Page": "1900145", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 90471891, "Title": "ОПТИМАЛЬНОЕ УПРАВЛЕНИЕ ПРОДОЛЬНЫМ ДВИЖЕНИЕМ УПРУГОГО СТЕРЖНЯ С ПОМОЩЬЮ ГРАНИЧНЫХ СИЛ", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0002338821050097", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 9501, "JournalTitle": "Известия Российской академии наук. Теория и системы управления", "ISSN": "0002-3388", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Г. <PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90471906, "Title": "The impact of payment methods and payment-related marketing communications on e-commerce retailer trust - an empirical consumer analysis of Indonesian e-commerce start-ups", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJEB.2021.10041142", "PubYear": 2021, "Volume": "16", "Issue": "4", "JournalId": 8392, "JournalTitle": "International Journal of Electronic Business", "ISSN": "1470-6067", "EISSN": "1741-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90471954, "Title": "РАСЧЕТНЫЙ СПОСОБ ОПРЕДЕЛЕНИЯ МАРШРУТНЫХ КОРРЕСПОНДЕНЦИЙ ПАССАЖИРОПОТОКОВ ПРИ ОБРАБОТКЕ ДАННЫХ ВХОДА И ВЫХОДА", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0002338821050073", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 9501, "JournalTitle": "Известия Российской академии наук. Теория и системы управления", "ISSN": "0002-3388", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "В. Н<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90471974, "Title": "Digital Public Services", "Abstract": "<p>Digitale Technologien haben unseren Alltag durchdrungen. Wirtschaft, Politik und Wissenschaft haben diese Entwicklung aufgegriffen und beschäftigen sich verstärkt mit der digitalen Transformation. Im öffentlichen Sektor werden dabei zwei Begriffe häufig diskutiert, das e‑Government und die Smart City. E‑Government steht in erster Linie für die digitale Bereitstellung öffentlicher Informationen durch die Nutzung von internet- und webbasierten Technologien, Smart City hingegen adressiert die Anwendung von digitalen Technologien, um nebst der Effizienz von städtischen oder regionalen Prozessen auch die Lebensqualität der Bevölkerung zu steigern. Hierbei spielen öffentliche Dienstleistungen eine wesentliche Rolle. Damit sind alle jene Leistungen gemeint, die der Staat für seine Einwohnerinnen erbringt, um sowohl ihre existentiellen als auch ihre politischen Bedürfnisse zu decken. Dieser Artikel führt zum einen den Begriff Digital Public Service beziehungsweise digitale öffentliche Dienstleistung ein und erläutert darauf aufbauend das e‑Government und die Smart City. Im Weiteren werden die Motive und Treiber für die Digitalisierung im öffentlichen Sektor präsentiert wie auch die Voraussetzungen für digitale öffentliche Dienstleistungen diskutiert.</p>", "Keywords": "Digital Public Service; Digitale Transformation; Digitalisierung; E‑Government; Motive; Public Service; Smart City; Treiber; Voraussetzung; Digital public service; Digital transformation; Digitalization; E‑government; Motive; Public service; Smart city; Driver; Prerequisite", "DOI": "10.1365/s40702-021-00785-1", "PubYear": 2021, "Volume": "58", "Issue": "5", "JournalId": 15010, "JournalTitle": "HMD Praxis der Wirtschaftsinformatik", "ISSN": "1436-3011", "EISSN": "2198-2775", "Authors": [{"AuthorId": 1, "Name": "Sara <PERSON>Onofrio", "Affiliation": "IT Business Integration, Genossenschaft Migros Zürich, Zürich, Schweiz"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Die Schweizerische Post, Bern, Schweiz"}], "References": []}, {"ArticleId": 90471980, "Title": "A Novel Intelligent System for Detection of Type 2 Diabetes with Modified Loss Function and Regularization", "Abstract": "<p>Type 2 Diabetes (T2DM) makes up about 90% of diabetes cases, as well as tough restriction on continuous monitoring and detecting become one of key aspects in T2DM. This research aims to develop an ensemble of several machine learning and deep learning models for early detection of T2DM with high accuracy. With high diversity of models, the ensemble provides more excessive performance than single models. Methodology. The proposed system is modified enhanced ensemble of machine learning models for T2DM prediction. It is composed of Logistic Regression, Random Forest, SVM, and Deep Neural Network models to generate a modified ensemble model. Results. The output of each model in the modified ensemble is used to figure out the final output of the system. The datasets being used for these models include Practice Fusion HER, Pima Indians diabetic’s data, UCI AIM94 Dataset and CA Diabetes Prevalence 2014. In comparison to the previous solutions, the proposed ensemble model solution exposes the effectiveness of accuracy, sensitivity, and specificity. It provides an accuracy of 87.5 from 83.51% in average, sensitivity of 35.8 from 29.59% as well as specificity of 98.9 from 96.27%. The processing time of the proposed model solution with 96.6 ms is faster than the state-of-the-art with 97.5 ms. Conclusion. The proposed modified enhanced system in this work improves the overall prediction capability of T2DM using an ensemble of several machine learning and deep learning models. A majority voting scheme utilizes the output from several models to make the final accurate prediction. Regularization function in this work is modified in order to include the regularization of all the models in ensemble, that helps prevent the overfitting and encourages the generalization capacity of the proposed system.</p>", "Keywords": "", "DOI": "10.1134/S0361768821050054", "PubYear": 2021, "Volume": "47", "Issue": "5", "JournalId": 15140, "JournalTitle": "Programming and Computer Software", "ISSN": "0361-7688", "EISSN": "1608-3261", "Authors": [{"AuthorId": 1, "Name": "G. C<PERSON>", "Affiliation": "School of Computing and Mathematics, Charles <PERSON> University (CSU), Wagga, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Mathematics, Charles <PERSON> University (CSU), Wagga, Australia;School of Computer Data and Mathematical Sciences, University of Western Sydney (UWS), Sydney, Australia;cchool of Information Technology, Southern Cross University (SCU), Sydney, Australia;Asia Pacific International College (APIC), Information Technology Department, Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, The University of Da Nang – University of Science and Education, Da Nang, Vietnam"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Information Technology, The University of Da Nang – University of Science and Education, Da Nang, Vietnam"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Technology Baghdad, Baghdad, Iraq"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Mathematics, Charles <PERSON> University (CSU), Wagga, Australia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, The University of Da Nang – University of Science and Education, Da Nang, Vietnam"}], "References": []}, {"ArticleId": 90471988, "Title": "Evaluation of split-and-rephrase output of the knowledge extraction tool in the intelligent tutoring system", "Abstract": "Various approaches to text simplification have been proposed in an attempt to increase text readability. The rephrasing of syntactically and semantically complex structures is still challenging. A pedagogically motivated simplified version of the same text can have both positive and negative side effects. On the one hand, it can facilitate reading comprehension because of much shorter sentences and a limited vocabulary, but on the other hand, the simplified text often lacks coherence, unity and style. Therefore, reasonable trade-offs among linguistic simplicity, naturalness and informativeness are highly needed. This is a survey paper that discusses state-of-the-art approaches to sentence/text simplification and evaluation methods, along with an empirical evaluation of our approach. The quality of sentence splitting, using the knowledge extraction tool SAAT was compared to state-of-the-art syntactic simplification systems. The research was carried out on the WikiSplit, the HSplit and MinWikiSplit simplification corpora. Automatic metrics for the HSplit showed that the SAAT outperformed other TS systems in all categories. For the WikiSplit dataset, automatic metrics scores were slightly lower than that of the baseline system DisSim. However, the human evaluation showed that DisSim outperformed the SAAT in terms of simplicity and grammar. The quality of AG18copy output corresponded to that of the SAAT. The inter-annotator agreement was calculated. Research limitations as well as suggestions for future research were also provided.", "Keywords": "Intelligent tutoring system ; Natural language processing ; Knowledge extraction ; Text simplification", "DOI": "10.1016/j.eswa.2021.115900", "PubYear": 2022, "Volume": "187", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Split, Faculty of Science, Ruđera Boškovića 33, Split, Croatia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Split, Faculty of Science, Ruđera Boškovića 33, Split, Croatia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Split, Catholic Faculty of Theology, Ul. Zrinsko Frankopanska 19, Split, Croatia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Mostar, Faculty of Science and Education, Trg hrvatskih velikana 1, Mostar, Bosnia and Herzegovina"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Mostar, Faculty of Philosophy, Matice hrvatske bb, Mostar, Bosnia and Herzegovina"}], "References": [{"Title": "Data-Driven Sentence Simplification: Survey and Benchmark", "Authors": "<PERSON>; <PERSON>on; Lucia <PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "1", "Page": "135", "JournalTitle": "Computational Linguistics"}]}, {"ArticleId": 90472024, "Title": "Enhancing long tail item recommendation in collaborative filtering: An econophysics-inspired approach", "Abstract": "Recommender systems have been immensely successful in overcoming information overload problem through personalized suggestions to consumers. Traditional recommendation algorithms tend to recommend more popular items. A significant number of items in an enterprise are non-popular (long tail items) due to lack of visibility in recommendations. These long tail items are left unsold and result in a significant loss to the business. The consumers on the other end are deprived of receiving relevant item recommendations. In this paper, we propose two approaches inspired from econophysics to recommend long tail items. The proposed approaches selectively inject ratings to the long tail items to diminish the bias towards the popular items by utilizing the existing rating information. Subsequently, the injected rating datasets are used to provide recommendations. The results on real-world datasets show that the proposed approaches outperform the existing techniques in mitigating long tail effect with little or no drop in accuracy.", "Keywords": "Long tail items ; Collaborative filtering ; Power-law distribution ; Econophysics ; Rating injection", "DOI": "10.1016/j.elerap.2021.101089", "PubYear": 2021, "Volume": "49", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Institute of Technology Rourkela, Rourkela, India"}, {"AuthorId": 2, "Name": "Bidyut Kr<PERSON>", "Affiliation": "National Institute of Technology Rourkela, Rourkela, India;Corresponding author"}], "References": [{"Title": "Mitigating long tail effect in recommendations using few shot learning technique", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "112887", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 90472035, "Title": "An optimal variable exponent model for Magnetic Resonance Images denoising", "Abstract": "This paper investigates a novel PDE-constrained optimization model with discontinuous variable exponent p ( x ) identification. Since the parameter p is always related to a better approximation of the image gradient, its computation plays a critical role in preserving the image texture. Analytically, we include results on the approximation of this parameter as well as the resolution of the encountered PDE in a well posed framework. In addition, to resolve the PDE-constrained minimization problem, we proposed a modified primal-dual algorithm. Finally, numerical results are provided to compute the parameter p and also to remove high intensity of noise. The proposed algorithm simultaneously keep safe fine details and important features in medical image applications (Magnetic Resonance Images (MRI)) with numerous comparisons to show the performance of the proposed approach.", "Keywords": "MRI denoising ; PDE-constrained optimization ; Variable exponent ; Primal- dual", "DOI": "10.1016/j.patrec.2021.08.031", "PubYear": 2021, "Volume": "151", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "FP <PERSON>, Université Ibn zohr, Maroc., Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "EMI, FST Béni-<PERSON><PERSON>, Université Sultan <PERSON>, Maroc., Morocco;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "EMI, FST Béni-<PERSON><PERSON>, Université Sultan <PERSON>, Maroc., Morocco"}], "References": [{"Title": "An improved nonlocal maximum likelihood estimation method for denoising magnetic resonance images with spatially varying noise levels", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "34", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Brain tumor segmentation and classification from magnetic resonance images: Review of selected methods from 2014 to 2019", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "244", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Active deep neural network features selection for segmentation and recognition of brain tumors using MRI images", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "181", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "CNN-DMRI: A Convolutional Neural Network for Denoising of Magnetic Resonance Images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "57", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 90472055, "Title": "Barriers of managing cloud outsource software development projects: a multivocal study", "Abstract": "<p>Management of COSD projects is a challenging task due to number of distant development locations in different time zones, client and vendor organizations, different cloud deployment models and range of different service level agreements. The objective of this study is to identify the barriers associated with managing COSD projects. We implemented a Multivocal Literature Review to identify barriers that influence management of COSD projects. We identified 21 COSD management barriers from 165 primary studies. The comparison between the barriers identified from formal and grey literature indicate that there are similarities between the barriers investigated from both types of literature. Moreover, client-vendor analysis shows that there is no significant difference between COSD management barriers associated with both types of organizations. We believe that the study findings will assist both research and industry community to better understand and manage COSD projects.</p>", "Keywords": "Cloud outsource software development; Software outsourcing; Cloud computing; Barriers; Multivocal literature review", "DOI": "10.1007/s11042-021-11245-9", "PubYear": 2022, "Volume": "81", "Issue": "25", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, Lappeenranta-Lahti University of Technology (LUT), Lappeenranta, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information and Computer Science Department, King <PERSON> University of Petroleum and Minerals, Dhahran, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Post Graduate Studies and Research in Mathematics, <PERSON><PERSON><PERSON> Government Post Graduation College, College of Chhindwara University, Betul, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "STC’s Artificial Intelligence Chair, Department of Information Systems, College of Computer and Information Sciences, King Saud University, Riyadh, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "STC’s Artificial Intelligence Chair, Department of Information Systems, College of Computer and Information Sciences, King Saud University, Riyadh, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, College of Computer and Information Sciences, King Saud University, Riyadh, Saudi Arabia"}], "References": [{"Title": "A robust framework for cloud‐based software development outsourcing factors using analytical hierarchy process", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "2", "Page": "e2275", "JournalTitle": "Journal of Software: Evolution and Process"}, {"Title": "Agile trends in Chinese global software development industry: Fuzzy AHP based conceptual mapping", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107090", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 90472118, "Title": "Incomplete multi-modal brain image fusion for epilepsy classification", "Abstract": "Multi-modal brain imaging data reflect brain structural and functional information from different aspects, which have been widely used in brain disease diagnosis, including epilepsy and Alzheimer's disease . In practice, it is difficult to obtain all the modalities of each subject due to high cost or equipment limitation. Therefore, it is highly essential to fuse incomplete multi-modality data to improve the diagnostic accuracy . The traditional methods need to perform data cleansing and discard incomplete subjects from the data, which leads to inefficient training and poor robustness. For addressing this problem, this paper proposes an incomplete multi-modality data fusion method based on low-rank representation for the diagnosis of epilepsy and its subtypes. Specifically, we designed an objective function that simultaneously learns the low-rank representation of the complete modality part, and recovers the incomplete modality by the correlation between different modalities. The proposed model can be optimized by using alternating direction method of multipliers . Extensive evaluation of the proposed method on epilepsy classification task with incomplete DTI and fMRI data showed that our method can achieve promising classification results in identifying epilepsy and its subtypes. Introduction Epilepsy is a recurrent neurological disease caused by abnormally synchronized activities of a large number of neurons [1], [2]. It is the second most common neurological disease after stroke, which accounts for more than 1% of the world's population [3]. There are mainly two subtypes of epilepsy, including temporal lobe epilepsy (TLE) and frontal lobe epilepsy (FLE). As the most common subtype of epilepsy, TLE refers to the seizures originate from the temporal lobe, or first transmitted to the temporal lobe structure via pathways. FLE is a kind of epilepsy that originates from the frontal lobe, accounting for 20% to 30% of all the epilepsy patients. Accurate diagnosis of subtypes of epilepsy is important for the treatment of patients. Therefore, it is necessary to develop an effective diagnosis method for the classification of epilepsy and its subtypes. In recent years, functional magnetic resonance imaging (fMRI) [4], [5], [6], [7] based on blood oxygen level-dependent (BOLD) contrast and diffusion tensor imaging (DTI) [8], [9] are two important brain imaging techniques. FMRI captures the temporal coherence of spontaneous BOLD fluctuations in the brain areas [10], and DTI fiber bundle imaging reveals the structural connectivity of the brain networks. Many machine learning based methods for distinguishing between brain disease patients and normal people have been proposed [11], [12], [13]. It has been found that the coupling of functional and structural networks is destroyed in disease-specific states [14], [15], [16]. Some studies have explored the differences between the epilepsy group and the healthy group [17], [18] with multi-modal brain image data. The integration of functional and structural information from fMRI and DTI may benefit the detection of subtle brain pathophysiological abnormalities more accurately than using single modality [19], [20]. In practice, it is still an increasing challenge of incomplete multi-modal data fusion problem [20], [21]. In the field of medical imaging analysis, it is often the incomplete case that some samples lose a certain modality instead of losing part of the features in some modalities. For instance, some participants cannot cooperate with data collection during all the imaging processes. In addition, because of noise or head moving in DTI imaging, it may fail to track fiber. This further leads to the failure of building DTI's structural network. Fig. 1 shows the case that lacking an entire modality in some samples affects learning the decision hyperplane, making it difficult to obtain an accurate diagnosis. For example, in a modality, if the test sample is close to the missing sample, it may be misclassified, thus affecting the results of multi-modal fusion classification. In machine learning, researchers have proposed various methods for dealing with incomplete data. These methods can mainly be categorized into three [22], [23]: 1) discarding samples with missing features; 2) recovering missing values by single modal based approach; 3) recovering missing values by multi-view based approach. The first category may result in a substantial loss of useful information as well as a significant reduction in the number of samples, which affects the classification performance of the classifier. In the second category, the common used methods include k-Nearest Neighbors (KNN) [24], singular value decomposition (SVD) [25] and expectation maximization (EM) [26]. Although these approaches may help dealing with incomplete data with a few randomly missing features, they often have poor performance when large amounts of data are missing [27]. Because it cannot effectively use prior knowledge of multi-modality data. In addition, these baseline interpolation methods may introduce additional imputation artifacts, resulting in reduced data quality [28]. Cai et al. [29] and Candes et al. [30] proposed to trace norm minimization to calculate missing data respectively. The third category, i.e. multi-view based approach, usually outperforms the first two types of strategies [20], and has shown superior accuracy in disease diagnosis. For example, Yuan et al. [31] proposed an incomplete multi-source feature learning (iMSF) and applied it into Alzheimer's disease classification. Liu et al [22], [27] introduced hypergraph for multi-source data recovering, and achieve good performance in Alzheimer’s disease classification. Li et al. [32] proposed a partial multi-view clustering (PVC) algorithm that uses the non-negative matrix factorization (NMF) framework to create potential subspaces to solve the problem that each view has some missing information. In addition, Rai et al. [33] proposed the graph regularized PVC (GPMVC) algorithm, which is an improvement of the PVC algorithm and can utilize the intrinsic geometry of the data distribution in each view. Most of these methods assume that the missing locations are random in feature level, and they are usually unable to process missing data in modality level. In this paper, we propose an incomplete multi-modality data learning method based on low-rank representation (LRR). Specifically, we design an objective function to learn the low-rank structure from the complete modality and utilize the consistency between the modalities to recover the incomplete modality. Compared to the traditional approaches, our method can handle missing data in the whole modality and explicitly utilize the consistency between modalities in the learning model. In brief, our proposed method has the following advantages: 1) The proposed method can handle the missing data in the modality level, not just the case that data are lost in random features, which makes it suitable for a further wide field of disease diagnosis application; 2) We extract the intrinsic structure information by low-rank constraint and embed the correlation of multi-modality data into the learning model to achieve better diagnostic performance; 3) To the best of our knowledge, this is the first work using incomplete fMRI and DTI data for the prediction of epilepsy and its subtypes. The experimental results show the proposed method achieves promising diagnosis results in several indicators. The remainder of the paper is organized as follows. In Section II, we introduce related work and materials used in the study. Then, we present the proposed incomplete multi-modality data learning method in Section III. Section IV provides the experimental results on epilepsy dataset. In Section V, we give an analysis of the experimental results. Finally, we summarize our work and provide our follow-up research plans in Section VI. Section snippets Low-rank representation Recently, LRR has achieved success in feature extraction and subspace learning. Given a set of samples, LRR seeks the low-rank component of all samples as bases in a dictionary so that the data can be represented as a linear combination of bases [34]. LRR differs from the sparse representation (SR) [35] that computes the sparse representation of each data vector separately, whereas LRR is intended to jointly compute the lowest rank representation of the whole sample set [34]. LRR can capture Proposed method Let C = c 1 , c 2 , ⋯ , c n ∈ R m c × n and D = d 1 , d 2 , ⋯ , d n ∈ R m d × n represent two different modalities of the subjects, where n is the number of subjects, m c and m d are the number of features of modality C and modality D , respectively. c i and d i denotes the feature vector of the two modalities, respectively. Without loss of generality, let C represent the modality with complete data, and let D denote the modality with several missing samples. We divide D into two parts: D 1 = d 1 , 1 , d 1 , 2 , ⋯ , d 1 , n 1 ∈ R m d × n 1 and D 2 = d 2 , 1 , d 2 , 2 , ⋯ , Experiment setup We evaluate our model for the diagnostic classification of individuals on the epilepsy dataset. And we adopted 10-fold cross-validation for verifying the performance of the methods. To be specific, the dataset is equally divided into 10 subsets, and each subset is, in turn, selected as a test set, and all remaining subsets are used for training. To avoid potential deviations during the dataset segmentation, we repeat this process 10 times. For the classifier, we carry out the SVM with the greedy Analysis of the above experimental results In the first experiment, we compared the result of our method with the incomplete sample exclusion method under different missing rates. The experimental results demonstrated that our method is better than that of incomplete sample exclusion method. The reason might be that the comparison method discards a lot of useful information, and our method can effectively utilize the incomplete data. Then, we compared our approach with the baseline interpolation methods by the measures of BAC and AUC Conclusion In summary, we propose an incomplete multi-modality data fusion algorithm based on low-rank representation and applied it into epilepsy and its subtypes classification. Specifically, we learn the low-rank relationship between sample groups through the complete modality, and supplement the missing data by adopting the consistency of self-representation models in different modalities. Not only our method can process randomly missing data, but also handle the task that the entire modality of some CRediT authorship contribution statement Qi Zhu: Methodology, Writing – original draft. Huijie Li: Data curation, Software, Visualization. Haizhou Ye: Software. Zhiqiang Zhang: Validation. Ran Wang: Methodology, Formal analysis. Zizhu Fan: Formal analysis. Daoqiang Zhang: Supervision. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements This work was supported in part by National Natural Science Foundation of China (Nos. 62076129, 62136004, 61732006, 61876082, 61501230 and 61991401), National Science and Technology Major Project (No. 2018ZX10201002), and the National Key R&D Program of China (Grant Nos.: 2018YFC2001600, 2018YFC2001602). References (50) J.J.-Y. Wang et al. Feature selection and multi-kernel learning for sparse representation on a manifold Neural Networks (2014) N. Zhang et al. Low-rank representation based discriminative projection for robust feature extraction Neurocomputing (2013) C. Luo et al. Dynamic probabilistic rough sets with incomplete data Inf. Sci. (Ny) (2017) M. Liu et al. View-aligned hypergraph learning for Alzheimer’s disease diagnosis with incomplete multi-modality data Med. Image Anal. (Feb. 2017) S. Xiang et al. Bi-level multi-source learning for heterogeneous block-wise missing data Neuroimage (2014) Z. Zhu et al. A novel multi-modality image fusion method based on image decomposition and sparse representation Inf. Sci. (Ny) (2018) W. Cao et al. Abnormal asymmetry in benign epilepsy with unilateral and bilateral centrotemporal spikes: a combined fMRI and DTI study Epilepsy Res. (2017) D. Pustina et al. Predicting the laterality of temporal lobe epilepsy from PET, MRI, and DTI: a multimodal study NeuroImage Clin. (2015) P. Sanches et al. Voxel-based analysis of diffusion tensor imaging in patients with mesial temporal lobe epilepsy Epilepsy Res. (2017) Z. Mao et al. Spatio-temporal deep learning method for adhd fmri classification Inf. Sci. (Ny) (2019) R.D. Thijs et al. Epilepsy in adults Lancet (2019) S.L. Moshé et al. Epilepsy: new advances Lancet (2015) E. Beghi The epidemiology of epilepsy Neuroepidemiology (2020) E. Ebrahimzadeh et al. Localizing confined epileptic foci in patients with an unclear focus or presumed multifocality using a component-based EEG-fMRI method Cogn. Neurodyn. (2021) H. Huang et al. Modeling task fMRI data via deep convolutional autoencoder IEEE Trans. Med. Imaging (Jul. 2018) K. Specht Current challenges in translational and clinical fMRI and future directions Front. psychiatry (2020) J. Yuan, X. Ran, K. Liu, C. Yao, Y. Yao, H. Wu, Q. Liu, “Machine Learning Applications on Neuroimaging for Diagnosis... D. Gutierrez-Barragan et al. Infraslow state fluctuations govern spontaneous fMRI network dynamics Curr. Biol. (2019) B. Hunyadi, Aacute, and la, “Learning from Structured EEG and fMRI Data Supporting the Diagnosis of Epilepsy (Leren van... L. Douw et al. ‘Functional connectivity’ is a sensitive predictor of epilepsy diagnosis after the first seizure PLoS One (2010) M. Han, L. Sun, “EEG signal classification for epilepsy diagnosis based on AR model and RVM,” in International... M.G. Preti et al. Decoupling of brain function from structure reveals regional behavioral specialization in humans Nat. Commun. (2019) R. Pienaar et al. White matter maturation reshapes structural connectivity in the late developing human brain Proc. Natl. Acad. Sci. (2010) K. Batista-García-Ramó et al. What we know about the brain structure–function relationship Behav. Sci. (Basel) (2018) B. Khotimah et al. Optimization of feature selection using genetic algorithm in naïve Bayes classification for incomplete data Int. J. Intell. Eng. Syst (2020) View more references Cited by (0) Recommended articles (6) View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.09.035", "PubYear": 2022, "Volume": "582", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing 211106, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing 211106, PR China"}, {"AuthorId": 3, "Name": "Haizhou Ye", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing 211106, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Medical Imaging, Jinling Hospital, Nanjing University School of Medicine, Nanjing 210002, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing 211106, PR China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Basic Science, East China Jiaotong University, Nanchang 330013, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing 211106, PR China;Corresponding authors"}], "References": []}, {"ArticleId": 90472119, "Title": "Feature selection using <PERSON><PERSON>’s law to support detection of malicious social media bots", "Abstract": "The increased amount of high-dimensional imbalanced data in online social networks challenges existing feature selection methods. Although feature selection methods such as principal component analysis (PCA) are effective for solving high-dimensional imbalanced data problems, they can be computationally expensive. Hence, an effortless approach for identifying meaningful features that are indicative of anomalous behaviour between humans and malicious bots is presented herein. The most recent Twitter dataset that encompasses the behaviour of various types of malicious bots (including fake followers, retweet spam, fake advertisements, and traditional spambots) is used to understand the behavioural traits of such bots. The approach is based on <PERSON><PERSON>’s law for predicting the frequency distribution of significant leading digits. This study demonstrates that features closely obey <PERSON><PERSON>’s law on a human dataset, whereas the same features violate <PERSON><PERSON>’s law on a malicious bot dataset. Finally, it is demonstrated that the features identified by <PERSON><PERSON>’s law are consistent with those identified via PCA and the ensemble random forest method on the same datasets. This study contributes to the intelligent detection of malicious bots such that their malicious activities , such as the dissemination of spam, can be minimised. Introduction Online social networks (OSNs) are web service platforms that enable users to interact virtually in real time through posting information and sending messages. This interaction (which is associated with the number of posts) enriches OSNs with large data volumes of end-user behaviour [1], [2]. OSNs are open platforms because anyone can create an account without being subjected to intensive verification processes [3]. In other words, OSNs implement simple authentication methods such as one-time passcodes and CAPTCHA methods [4] to prevent access to non-human users. However, these methods can be circumvented; hence, OSNs are being targeted for malicious activities such as the spread of fake news [47], trolling<sup>1</sup> [5], and Sybil attacks<sup>2</sup>[6], all of which impose serious threats. OSN users are not limited to humans; in fact, social media bots (bots) are widely prevalent [48]. Half-human and half-bot accounts exist between humans and bots, and they are known as cyborg accounts [8], [9]. Bots can be used for not only legitimate purposes, such as news or weather updates, but also for malicious activities [10], [48]. According to the authors of [7], several malicious bots have been identified as engaging in the dissemination of misinformation on Instagram by posting false negative comments through hashtag hijacking . Spreading fake news on OSNs is a serious concern, as such activities can agitate users as well as influence public opinion [47]. Digital marketing is a vital strategy for many businesses and political parties; therefore, bots that engage in trolling activities to influence public opinion must be identified. The detection of malicious bots on OSNs is a well-known problem that has been investigated extensively by researchers, who subsequently proposed machine and deep learning models for detecting such bots [48]. When developing a predictive machine-learning model, a set of features that may include the daily average number of posts for an account is considered. It is crucial to consider the optimum set of features when designing a machine-learning model [5]. For example, consider a Twitter user account with many attributes, such as user-id, screen name, and location. These attributes are used to create features such as the screen name length [32]; subsequently, these attributes and features are used to design effective machine-learning models that can differentiate between malicious bot and human accounts [48]. The most well-known machine-learning-based bot detection tool is the botometer [33] tool. The high-dimensional and velocity aspects of OSN data necessitate an effortless feature selection method that can support machine-learning-based models for malicious bot detection [11], [12]. The majority of feature selection methods are embedded or based on machine learning, such as support vector machine (SVM) [13], neural networks (NN) [12], and ensemble methods [45]. However, embedded feature selection methods are not effective for solving binary classification problems involving an imbalanced dataset [13], such as the case study reported herein. Although machine-learning-based feature selection methods such as those mentioned above and PCA can be used to solve high-dimensional imbalanced dataset problems, their computational cost can be high [14], [46]. Hence, an effortless approach to identify meaningful features that can differentiate between human and malicious bot accounts is proposed herein. A feature is only meaningful if it is indicative of anomalous behaviour between human and malicious bot accounts [16], [23]; for example, malicious bot accounts may post content more frequently than humans. To identify meaningful features, we adopted Benford’s law, which states that the distribution of the first significant leading digit (FSLD) on a “naturally occurring” dataset is non-uniform [15]. The meaning of a naturally occurring dataset, as opposed to a fabricated or inflated dataset [17], [35], is discussed later herein. For example, consider a feature constituting a Twitter dataset known as status_count , which counts the number of tweets an account contains at a particular discrete time t . Let us consider that 1000 Twitter users are selected randomly and the status_count of each user is examined. The first user may have 3 24 tweets, the second user may have 8 7 tweets, and so on, until the 1000th user. The following question arises: What is the distribution of the FSLD (in bold) for all 1000 users, or what is the likelihood that the FSLD for status_count begins with digits 1, 2,.., 9? Digit 1 is expected to occur approximately 30% more frequently as an FSLD than digit 9 [16], [44]. The same logic can be applied to examine the FSLD distribution of other numeric Twitter-based features, such as followers_count, which will be discussed in Sections II and IV. Benford’s law can be implemented easily and does not require parameter fitting. Hence, it is superior to other non-uniform distributions, such as the power law and Zipf’s law [17]. (i) What are the behavioural traits of malicious bots and humans using a Twitter dataset? (ii) Investigate features based on Twitter attributes to determine if they obey Benford’s law. (iii) Demonstrate that Benford’s law can effectively identify meaningful features that can differentiate malicious bots from humans, even on a high-dimensional imbalanced dataset. (iv) Demonstrate that features identified by Benford’s law are consistent with prevalent feature selection methods, which include the PCA and the ensemble random forest, on the same Twitter datasets. Section snippets Literature review In this section, we discuss key literature pertaining to bot detection, feature selection, and Benford's law. The authors of [16] were the first to apply Benford's law to data on OSNs [16]; they discovered that certain user features on OSNs conformed to Benford's law. Furthermore, they reported that Benford's law can be used to detect users who display anomalous behaviour (some of these were discovered to be bots). Datasets were extracted from different OSNs (including Facebook and Twitter) to Benford’s Law In this section, we describe Benford’s law and its dataset conditions. Additionally, we demonstrate empirically that the FSLD distribution of a Twitter dataset should conform to Benford’s law. For this study, it suffices to demonstrate Benford’s law empirically, since we are addressing the application of this law. A mathematical proof of Benford’s law is available in [15], [17]. Benford’s law was discovered in 1881 by the astronomer Simon Newcomb; since then, it has been applied in various Data preparation Big data are characterised by their volume, velocity, and variety [36]. Volume describes the amount of data that is being generated, velocity describes the speed at which the data is generated, and variety describes the different types of data [36]. A Twitter dataset is a classic example of big data, as users continuously interact with one another via text messages and live streams to share pictures and videos (among other things). To test the effectiveness of Benford’s law in differentiating Discussion of Results Benford’s law was applied to a real Twitter dataset from the Bot Repository, which comprises data of humans and various types of malicious bots. We experimentally identified features that can differentiate between malicious bot and human datasets using the FSLD test. It was discovered that Benford’s law only focused on the FSLD distribution; therefore, it was not affected by imbalanced datasets. The results indicated that screen_name length, reply_count, hashtag_count, URL_count, and Conclusions and Implications Machine-learning models can only benefit from intelligent feature selection techniques, particularly on high-velocity and dimensional imbalanced datasets. We believe that this study contributes to the further development of intelligent feature selection methods for designing effective machine-learning models that can detect malicious bots. Herein, a simplified feature selection method for a high-dimensional imbalanced Twitter dataset using Benford’s law was proposed. The findings of this study Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements Innocent Mbona would like to thank the University of Pretoria and Bank Seta for funding this study. References (48) Majd Latah Detection of malicious social bots: a survey and a refined taxonomy Expert Syst. Appl. (2020) Alessandro Bondielli et al. A survey on fake news and rumour detection techniques Inf. Sci. (Ny) (2019) E. Druică et al. Benford's law and the limits of digit analysis Int. J. Account. Inform. Syst. (2018) S. Cresci et al. Fame for sale: Efficient detection of fake Twitter followers Decis. Support Syst. (2015) X. Zheng et al. Detecting spammers on social networks Neurocomputing (2015) D. Savage et al. Anomaly detection in online social networks Soc. Netw. (2014) M. Morchid et al. Feature selection using principal component analysis for massive retweet detection Pattern Recogn. Lett. (2014) X. Tao et al. Self-adaptive cost weights-based support vector machine cost-sensitive ensemble for imbalanced data classification Inf. Sci. (2019) G. Chandrashekar et al. A survey on feature selection methods Comput. Electr. Eng. (2014) S. Maldonado et al. Feature selection for high-dimensional class-imbalanced data sets using support vector machines Inf. Sci. (2014) L. Yin et al. Feature selection for high-dimensional imbalanced data Neurocomputing (2013) S.M. Erfani et al. High-dimensional and large-scale anomaly detection using a linear one-class SVM with deep learning Pattern Recogn. (2016) X. Xu et al. A survey of CAPTCHA technologies to distinguish between human and computer Neurocomputing (2020) M. Tsikerdekis et al. Online deception in social media Commun. ACM (2014) M. Tsikerdekis et al. Detecting and preventing online identity deception in social networking services IEEE Internet Comput. (2015) D. B. Kurka, A. Godoy, and F. J. Von Zuben, “Online social network analysis: A survey of research applications in... P. Galán-García, J. G. d. l. Puerta, C. L. Gómez, I. Santos, and P. G. Bringas, “Supervised machine learning for the... M. Al-Qurishi et al. Sybil defense techniques in online social networks: a survey IEEE Access (2017) F. C. Akyon and M. E. Kalfaoglu, “Instagram Fake and Automated Account Detection,” in 2019 Innovations in Intelligent... Z.i. Chu et al. Detecting automation of twitter accounts: are you a human, bot, or cyborg? IEEE Trans. Depend. Secure Comput. (2012) Z. Chu, S. Gianvecchio, H. Wang, and S. Jajodia, “Who is tweeting on Twitter: human, bot, or cyborg?,” in Proceedings... V.S. Subrahmanian et al. The DARPA twitter bot challenge Computer (2016) S. García et al. Feature selection Intell. Syst. Ref. Libr. (2015) A. Berger et al. An introduction to Benford's law (2015) View more references Cited by (0) Recommended articles (6) View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.09.038", "PubYear": 2022, "Volume": "582", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Pretoria, 0002, Pretoria, South Africa;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Pretoria, 0002, Pretoria, South Africa"}], "References": [{"Title": "Detection of malicious social bots: A survey and a refined taxonomy", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "113383", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A survey of CAPTCHA technologies to distinguish between human and computer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "408", "Issue": "", "Page": "292", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 90472132, "Title": "Proceedings 37th International Conference on Logic Programming (Technical Communications)", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.21", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Nebraska Omaha"}], "References": []}, {"ArticleId": 90472134, "Title": "A Note on Occur-Check", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.17", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90472135, "Title": "Proceedings 37th International Conference on Logic Programming (Technical Communications)", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.7", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Portland State University"}], "References": []}, {"ArticleId": 90472141, "Title": "Deep learning for emotion driven user experiences", "Abstract": "Deep knowledge about user characteristics and behaviors opens new and promising landscapes to the User Experience. Thanks to emerging machine learning techniques, a new form of communication channel among users and applications may be exploited for customizing and finely tuning the dynamic behavior of applications to the peculiarities of their users. This work investigates the empathic improvement of the User Experiences and exploits inferences on user expressions for activating gaming and entertainment events, that are adopted in a cinematic way to create dynamic application behaviors. The presented approach is applied to a third/first-person horror adventure and a classic table game. As already verified in a preliminary phase of the research, user impressions, collected as subjective evaluation in a controlled experiment, are positive and encourage further steps, like the evaluation of other inferences on users.", "Keywords": "Deep learning ; Computer vision ; User emotions ; User experience", "DOI": "10.1016/j.patrec.2021.09.004", "PubYear": 2021, "Volume": "152", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Science, University of Salerno, Fisciano (SA) 84084, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Science, University of Salerno, Fisciano (SA) 84084, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Naples Parthenope, Centro Direzionale di Napoli, Isola C4, Naples, Italy, 80143"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Computer Science, University of Salerno, Fisciano (SA) 84084, Italy"}], "References": []}, {"ArticleId": 90472192, "Title": "Der Einsatz von Virtual-Reality Lernszenarien für Softskills-Trainings", "Abstract": "<p lang=\"de\"> Zusammenfassung <p>Der Artikel zeigt den wirkungsvollen Einsatz von Virtual-Reality-Trainings im Softskills-Bereich besonders für Kommunikations‑, Präsentations- oder Verhandlungskompetenzen. Zunächst werden auf die Vorteile im Vergleich zum klassischen Setting eingegangen, wie etwa die Unabhängigkeit von einer physischen Präsenz von Trainingspartnern, was VR-Trainings in Zeiten der COVID-19 Krise noch attraktiver werden lassen. Nachdem auf das Risiko einer kognitiven Überlastung in einer VR-Umgebung eingegangen wird, wird ein Fallbeispiel eines VR-Szenarios im Softskills-Bereich, das in einem österreichischen Unternehmen eingesetzt wird, dargestellt. Die Ergebnisse einer quantitativen Erhebung zeigen, dass die Gestaltung der VR-Lernsequenzen mit einer niedrigen kognitiven Belastung und förderlich für den Lernprozess wahrgenommen wurde. <PERSON><PERSON><PERSON><PERSON> Faktoren, die zu diesem Ergebnis führten, wurden in einer anschließenden qualitativen Befragung erhoben. Der Artikel schließt mit Handlungsempfehlungen für den didaktischen Aufbau von VR-Szenarien, die aus den gewonnenen Erkenntnissen unter Einbeziehung einiger Gestaltungsprinzipien aus der Theorie der kognitiven Beanspruchung abgeleitet werden.</p></p>", "Keywords": "Virtuelle Realität; Softskills-Trainings; Theorie der kognitiven Belastung; Kognitive Effekte; VR-Design; Virtual Reality; Softskills-trainings; Cognitive load theory; Cognitive effects; VR-design", "DOI": "10.1365/s40702-021-00784-2", "PubYear": 2022, "Volume": "59", "Issue": "1", "JournalId": 15010, "JournalTitle": "HMD Praxis der Wirtschaftsinformatik", "ISSN": "1436-3011", "EISSN": "2198-2775", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department für Weiterbildungsforschung und Bildungstechnologien, Donau-Universität Krems, Krems, Österreich"}, {"AuthorId": 2, "Name": "Stefan <PERSON>", "Affiliation": "Department für Weiterbildungsforschung und Bildungstechnologien, Donau-Universität Krems, Krems, Österreich"}], "References": []}, {"ArticleId": 90472245, "Title": "Design and development of a microfluidic droplet generator with vision sensing for lab-on-a-chip devices", "Abstract": "Lab on a Chip (LOC) devices minimize, integrate, automate, and parallelize laboratory functions such as mixing, separation, and incubation on a single chip. Droplet generation is one key aspect in LOC devices which allows to conduct various chemical and biochemical assays enabling biological cell studies, high throughput drug development, and diagnostic screenings. This paper presents, modelling, simulation, and experimentation of an active droplet generator that is widely used in LOC devices. The model geometry used in this study was based on a flow focusing method of droplet generation. Droplet generation from the numerical simulations was observed within flow rate ratios ranging from 0.2 to 4 using optimised droplet contraction width of the generator model. Subsequently, a prototype droplet generator was designed and developed from Polymethyl Methacrylate (PMMA) material using a layer-based fabrication method. Based on the experimental setup presented here, the calculated ratios of flow rates obtained for different voltage values have shown that the formation of droplets occur between flow rate ratios of 1.04 and 4.74. In addition, morphological parameters of the droplet images extracted from a digital image processing algorithm show that the mean diameter of the droplets decreases with decreasing flow rate ratios.", "Keywords": "Active droplet generator ; Microfluidics ; Lab-on-a-chip ; Flow-focusing ; Vision sensing", "DOI": "10.1016/j.sna.2021.113047", "PubYear": 2021, "Volume": "332", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Advanced Mechatronic Systems, University of Moratuwa, Katubedda 10400, Sri Lanka;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Advanced Mechatronic Systems, University of Moratuwa, Katubedda 10400, Sri Lanka"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Advanced Mechatronic Systems, University of Moratuwa, Katubedda 10400, Sri Lanka"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Advanced Mechatronic Systems, University of Moratuwa, Katubedda 10400, Sri Lanka"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Moratuwa, Katubedda 10400, Sri Lanka"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Advanced Mechatronic Systems, University of Moratuwa, Katubedda 10400, Sri Lanka;Department of Mechanical Engineering, University of Moratuwa, Katubedda 10400, Sri Lanka"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Engineering and Built Environment, Griffith University, Australia"}], "References": []}, {"ArticleId": 90472247, "Title": "Strictifying and taming directed paths in Higher Dimensional Automata", "Abstract": "<p> Directed paths have been used by several authors to describe concurrent executions of a program. Spaces of directed paths in an appropriate state space contain executions with all possible legal schedulings. It is interesting to investigate whether one obtains different topological properties of such a space of executions if one restricts attention to schedulings with “nice” properties, e.g. involving synchronisations. This note shows that this is not the case, i.e. that one may operate with nice schedulings without inflicting any harm. Several of the results in this note had previously been obtained by <PERSON><PERSON><PERSON><PERSON> in Ziemiański (2017. Applicable Algebra in Engineering, Communication and Computing 28 497–525; 2020a. Journal of Applied and Computational Topology 4 (1) 45–78). We attempt to make them accessible for a wider audience by giving an easier proof for these findings by an application of quite elementary results from algebraic topology; notably the nerve lemma. </p>", "Keywords": "Higher Dimensional Automata; d-path; strict; tame; serial; parallel; homotopy equivalence; nerve lemma", "DOI": "10.1017/S0960129521000128", "PubYear": 2021, "Volume": "31", "Issue": "2", "JournalId": 10211, "JournalTitle": "Mathematical Structures in Computer Science", "ISSN": "0960-1295", "EISSN": "1469-8072", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematical Sciences, Aalborg University, Skjernvej 4A, Aalborg Øst DK-9220, Denmark Email; Corresponding author."}], "References": []}, {"ArticleId": 90472254, "Title": "Open Access and Data Sharing of Nucleotide Sequence Data", "Abstract": "<p>Open access, free access, and the public domain are different concepts. The International Nucleotide Sequence Database Collaboration (INSDC) permanently guarantees free and unrestricted access to nucleotide sequence data for all researchers, irrespective of nationality or affiliation. However, recent virus information is primarily distributed via the restricted-access repository known as the Global Initiative on Sharing Avian Flu Data (GISAID) supported by the World Health Organization. As compensation for the restriction, GISAID needs to meet its initial goal of benefit-sharing among countries and to curb ongoing vaccine diplomacy campaigns.</p>", "Keywords": "Nucleotide Sequence data (NSD); International Nucleotide Sequence Database Collaboration (INSDC); Global Initiative on Sharing Avian Flu Data (GISAID); Convention on Biological Diversity (CBD); Nagoya Protocol;Nucleotide Sequence data (NSD);International Nucleotide Sequence Database Collaboration (INSDC);Global Initiative on Sharing Avian Flu Data (GISAID);Convention on Biological Diversity (CBD);Nagoya Protocol", "DOI": "10.5334/dsj-2021-028", "PubYear": 2021, "Volume": "20", "Issue": "", "JournalId": 11331, "JournalTitle": "Data Science Journal", "ISSN": "", "EISSN": "1683-1470", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bioinformation & DDBJ Center, National Institute of Genetics, Yata 1111, Mishima Shizuoka 411–8540; RIKEN Center for Sustainable Resource Science, 1-7-22 Tsurumi, Yokohama, Kanagawa 230-0045"}], "References": []}, {"ArticleId": 90472292, "Title": "CWI: A multimodal deep learning approach for named entity recognition from social media using character, word and image features", "Abstract": "<p>Named entity recognition (NER) from social media posts is a challenging task. User-generated content that forms the nature of social media is noisy and contains grammatical and linguistic errors. This noisy content makes tasks such as NER much harder. We propose two novel deep learning approaches utilizing multimodal deep learning and transformers. Both of our approaches use image features from short social media posts to provide better results on the NER task. On the first approach, we extract image features using InceptionV3 and use fusion to combine textual and image features. This approach presents more reliable name entity recognition when the images related to the entities are provided by the user. On the second approach, we use image features combined with text and feed it into a BERT-like transformer. The experimental results using precision, recall, and F1 score metrics show the superiority of our work compared to other state-of-the-art NER solutions.</p>", "Keywords": "Deep learning; Named entity recognition; Multimodal learning; Transformer", "DOI": "10.1007/s00521-021-06488-4", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Orleans, Orleans, France"}], "References": [{"Title": "Deep Learning--based Text Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deep Learning--based Text Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 90472352, "Title": "A Logic-based Multi-agent System for Ethical Monitoring and Evaluation of Dialogues", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.345.32", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "DISIM, University of L'Aquila, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "DISIM, University of L'Aquila, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "DISIM, University of L'Aquila, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "DIB & CILA, University of Bari \"Aldo Moro\", Italy"}], "References": [{"Title": "Logic Programming and Machine Ethics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "325", "Issue": "", "Page": "6", "JournalTitle": "Electronic Proceedings in Theoretical Computer Science"}]}, {"ArticleId": 90472401, "Title": "Issue Information", "Abstract": "", "Keywords": "", "DOI": "10.1002/cpe.5883", "PubYear": 2021, "Volume": "33", "Issue": "19", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [], "References": []}, {"ArticleId": 90472403, "Title": "Small files’ problem in Hadoop: A systematic literature review", "Abstract": "Apache Hadoop is an open-source software library which integrates a wide variety of software tools and utilities to facilitate the distributed batch processing of big data sets. Hadoop ecosystem comprises two major components - Hadoop Distributed File System (HDFS), which is primarily used for storage and MapReduce, which is primarily used for processing of the files. The performance of Hadoop holds back when it comes to storage and processing of small files. Small files are essentially the files that are significantly smaller in size when compared to the default block size of HDFS. This is because each small file consumes a block individually leading to excessive memory requirement, access time and processing time. Scaling the memory, allowing access latencies and processing delays beyond a limit is not an option. Henceforth, in this paper, a Systematic Literature Review has been performed to provide a comprehensive and exhaustive gestalt of the small files&#x27; problem in Hadoop. The paper defines a comprehensive taxonomy of Hadoop ecosystem and its’ existent small files problem. Further, the study also attempts to critically analyze the solutions that have been proposed to overcome this problem. These solutions have been analytically studied to identify the set of parameters that should be considered in impending while proposing solutions pertaining to this problem.", "Keywords": "Hadoop ; Hadoop Distributed File System ; Small files ; MapReduce ; File merging", "DOI": "10.1016/j.jksuci.2021.09.007", "PubYear": 2022, "Volume": "34", "Issue": "10", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Engineering, J<PERSON><PERSON><PERSON> University of Science and Technology, Faridabad, Haryana, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Engineering, J<PERSON><PERSON>. Bose University of Science and Technology, Faridabad, Haryana, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Engineering, J<PERSON><PERSON>. Bose University of Science and Technology, Faridabad, Haryana, India"}], "References": []}, {"ArticleId": 90472424, "Title": "ИДЕНТИФИКАЦИЯ ОБЪЕКТОВ ПРИ СТРУКТУРНО-СИСТЕМНОМ МОНИТОРИНГЕ ОБСТАНОВКИ", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0002338821050139", "PubYear": 2021, "Volume": "", "Issue": "5", "JournalId": 9501, "JournalTitle": "Известия Российской академии наук. Теория и системы управления", "ISSN": "0002-3388", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "С. Н. Разиньков", "Affiliation": ""}, {"AuthorId": 3, "Name": "А. В. Тим<PERSON><PERSON><PERSON>нко", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90472436, "Title": "Range free localization technique under erroneous estimation in wireless sensor networks", "Abstract": "<p>Minkowski timespace has the capability to overcome the limited accuracy of L <sub>2</sub>-norm based range-free localization methods. This paper proposes the concept of Minkowski triangulation uncertainty (MTU) in wireless sensor networks (WSNs) for localization of unknown target. To set up a localization framework, triangulation uncertainty parameter is defined using Lemma 3.1 . A two-stage estimation algorithm is then presented: countLocalized and countAnchor . countLocalized computes the number of localized sensor nodes by leveraging the uncertainty strategy based upon indeterminate independent measurement. countAnchor designates the anchor nodes to triangulate the unknown target by formulating a convex hull model. The convex hull is the Minkowski sum of the actual and projected positions of the two vector node positions. The proposed MTU technique establishes that the number of triangulations formed by <PERSON><PERSON> method is inclusive of the triangulations formed by conventional L <sub>2</sub>-norm range of sensor nodes in a WSN. Measurement strategies such as angle, distance and positioning error are compared in the simulation. The said technique links Minkowski space to localization by ensuring efficiency in large target areas and number of nodes in manifolds. Results confirm that the MTU technique is better than the existing models by at least 12%, 50%, 5.5% and 24% in terms of localization ratio, localization error, neighbour anchor nodes and network connectivity, respectively.</p>", "Keywords": "Error estimation; Localization uncertainty; Minkowski distance; Triangulation; Wireless sensor networks", "DOI": "10.1007/s11227-021-04075-x", "PubYear": 2022, "Volume": "78", "Issue": "4", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": " Prateek", "Affiliation": "WSN Lab, Department of Electronics and Communication Engineering, National Institute of Technology Patna, Patna, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "WSN Lab, Department of Electronics and Communication Engineering, National Institute of Technology Patna, Patna, India"}], "References": [{"Title": "C-TOL: Convex triangulation for optimal node localization with weighted uncertainties", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "46", "Issue": "", "Page": "101300", "JournalTitle": "Physical Communication"}, {"Title": "An enhanced scheme for mutual authentication for healthcare services", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "2", "Page": "150", "JournalTitle": "Digital Communications and Networks"}]}, {"ArticleId": 90472485, "Title": "Open-source concealed EEG data collection for Brain-computer-interfaces - neural observation through OpenBCI amplifiers with around-the-ear cEEGrid electrodes", "Abstract": "Observing brain activity in real life offers exciting possibilities like the support of physical health, mental well-being, and thought-controlled interaction modalities. The development of such applications is, however, strongly impeded by low accessibility to research-grade neural data and a lack of easy-to-use and comfortable sensors. To overcome these challenges, we present the cost-effective adaptation of concealed around-the-ear EEG electrodes (cEEGrids) to the open-source OpenBCI EEG signal acquisition platform. The system combines publicly available electronic components with 3D-printed parts, forming an easily replicable recording system for prolonged use and flexible application development. To demonstrate the system’s feasibility, experimentally induced changes in visual stimulation and mental workload are replicated. Lastly, a novel application area is investigated, the observation of flow experiences through observation of temporal Alpha power changes. Support for a link between temporal Alpha power and flow is found, indicating an efficient engagement of verbal-analytic reasoning with intensified flow levels.", "Keywords": "Concealed EEG ; OpenBCI ; cEEGrid ; Brain-Computer-Interface ; wearables", "DOI": "10.1080/2326263X.2021.1972633", "PubYear": 2021, "Volume": "8", "Issue": "4", "JournalId": 24751, "JournalTitle": "Brain-Computer Interfaces", "ISSN": "2326-263X", "EISSN": "2326-2621", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Information Systems and Marketing (IISM, Karlsruhe Institute of Technology (KIT), Karlsruhe, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Information Systems and Marketing (IISM, Karlsruhe Institute of Technology (KIT), Karlsruhe, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics, Information, and Bioengineering, Politecnico Di Milano, Milan, Italy"}], "References": []}, {"ArticleId": 90472508, "Title": "Exploring the Internet of Things sequence-structure detection and supertask network generation of temporal-spatial-based graph convolutional neural network", "Abstract": "<p>The study is designed to improve the efficiency of Internet of Things (IoT) structure detection and achieve the smooth operation of IoT networks. First, the connection between the IoT network structure and maxdegree is investigated based on analyzing the IoT supertask network structure to find the main influence factor of maxdegree, along with the conditions for obtaining the optimal maxdegree. Second, a structural algorithm model of optimal supertask network is proposed as the foundation for achieving the minimum maxdegree. Finally, the human behavior recognition database is taken as the research object to verify its performance through the specific instance data. The IoT network structure factors are proved to include the task quantity, resource capacity, number of networks, and Communication Calculation Ratio (CCR). The experimental results also show that the principal factor that affects maxdegree is the number of different tasks. Besides, there is a mutually positive interaction between the network structure and the IoT maxdegree, which complement each other and form the core network of IoT. Moreover, the results reflect the good performance on different datasets of the supertask IoT network structure for the human behavior recognition database. There exists the optimal maxdegree of the model under the condition of 40 tasks, 32 resources, 6 networks and CCR of 6. Furthermore, the proposed algorithm has a shorter length and lower complexity than other related algorithms, which is very suitable for the construction of IoT networks. The research results can provide some references and practical value for the construction and data processing of the IoT structure.</p>", "Keywords": "Graph Convolutional Neural Network; Internet of Things; Sequence-structure detection; Supertask gateway; Maxdegree", "DOI": "10.1007/s11227-021-04041-7", "PubYear": 2022, "Volume": "78", "Issue": "4", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, South China University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, South China University of Technology, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, South China University of Technology, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, South China University of Technology, Guangzhou, China"}], "References": [{"Title": "Application of the best evacuation model of deep learning in the design of public structures", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "103975", "JournalTitle": "Image and Vision Computing"}, {"Title": "Progressive perception-oriented network for single image super-resolution", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "769", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 90472525, "Title": "Promoting trust through linguistic features of provider profiles in the sharing economy", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJEB.2021.10041152", "PubYear": 2021, "Volume": "16", "Issue": "4", "JournalId": 8392, "JournalTitle": "International Journal of Electronic Business", "ISSN": "1470-6067", "EISSN": "1741-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}]