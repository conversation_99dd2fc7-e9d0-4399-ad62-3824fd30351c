[{"ArticleId": 114734446, "Title": "動力学的解析に基づく円形指による2nd-order form closure把持のための部品のアライメントの実現性の考察", "Abstract": "To achieve grasping parts with a variety of geometries and initial pose errors, several robotic hands have been developed that can self-align the parts and realize form closure grasps. Depending on the hand mechanism and the part geometry, a 2nd-order form closure grasp may be required. This paper discusses the feasibility of alignment for 2nd-order form closure grasps of 2D parts with circular fingers based on the required finger force calculated from dynamic analysis, assuming that initial orientational errors of the parts exist. Experiments on the alignment will also be conducted to examine its feasibility in the real environment.", "Keywords": "Alignment;2nd-order Form Closure;Pushing Operation;Dynamics;Robotic Hand", "DOI": "10.7210/jrsj.42.291", "PubYear": 2024, "Volume": "42", "Issue": "3", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Systems Engineering, Wakayama University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Systems Engineering, Wakayama University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Systems Engineering, Wakayama University"}], "References": [{"Title": "Robust form-closure grasp planning for 4-pin gripper using learning-based Attractive Region in Environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "384", "Issue": "", "Page": "268", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 114734575, "Title": "Dynamic Cognitive Load Assessment in Virtual Reality", "Abstract": "Background <p>Recent advances in non-invasive physiologic monitoring leverage machine learning to provide unobtrusive, real-time assessments of a learner’s cognitive load (CL) as they engage in specific tasks. However, the performance characteristics of these novel composite physiologic CL measures are incompletely understood.</p> Objectives <p>We aimed to 1) explore the feasibility of measuring CL in real time using physiologically-derived inputs; 2) evaluate the performance characteristics of a novel composite CL measure during simulated virtual reality resuscitations; and 3) understand how this measure compares to traditional, self-reported measures of CL.</p> Methods <p><PERSON><PERSON> (PGY1-2 pediatric residents) and expert (pediatric emergency medicine fellows and attendings) participants completed four virtual reality simulations as team leader. The scenario content (status epilepticus versus anaphylaxis) and level of distraction (high versus low) were manipulated. Cognitive load was measured in all participants using electroencephalography and electrocardiography data (“real-time CL”) as well as through self-report (NASA-TLX). Scenario performance also was measured.</p> Results <p> Complete data were available for 6 experts and 6 novices. Experts generally had lower CL than novices on both measures. Both measures localized the most significant differences between groups to the anaphylaxis scenarios (real-time CL [low-distraction] <PERSON>’s d -1.33 [95% CI -.2.56, -0.03] and self-reported CL [high-distraction] <PERSON>’s d -1.41 [95% CI -2.67, -0.10]). No consistent differences were seen with respect to level of distraction. Performance was similar between the two groups, though both exhibited fewer errors over time (F <sub>(3,48)</sub> = 5.75, p = .002). </p> Conclusion <p>It is feasible to unobtrusively measure cognitive load in real time during virtual reality simulations. There was convergence between the two CL measures: in both, experts had lower CL than novices, with the most significant effect size differences in the more challenging anaphylaxis scenarios.</p>", "Keywords": "", "DOI": "10.1177/10468781241248821", "PubYear": 2024, "Volume": "55", "Issue": "4", "JournalId": 3394, "JournalTitle": "Simulation & Gaming", "ISSN": "1046-8781", "EISSN": "1552-826X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Columbia University Vagelos College of Physicians and Surgeons, New York, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Aptima, Inc., Woburn, MA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Kalo Labs, LLC, River Edge, NJ, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Children’s Hospital Los Angeles, Los Angeles, CA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Columbia University Vagelos College of Physicians and Surgeons, New York, NY, USA"}], "References": [{"Title": "Association between Clinical Simulation Design Features and Novice Healthcare Professionals’ Cognitive Load: A Systematic Review and Meta-Analysis", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "5", "Page": "538", "JournalTitle": "Simulation & Gaming"}]}, {"ArticleId": 114734594, "Title": "Job satisfaction through the perspective of emotional labor", "Abstract": "", "Keywords": "", "DOI": "10.1108/K-07-2023-1288", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114734609, "Title": "Detecting Malware Activity Using Machine Learning", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2024.13453", "PubYear": 2024, "Volume": "13", "Issue": "4", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Sanket Deore", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114734626, "Title": "Neural Network Feature Selection Based on Collaborative Filtering Recommender Systems for User Classification", "Abstract": "", "Keywords": "", "DOI": "10.54216/FPA.150214", "PubYear": 2024, "Volume": "15", "Issue": "2", "JournalId": 91518, "JournalTitle": "Fusion: Practice and Applications", "ISSN": "2770-0070", "EISSN": "2692-4048", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computing and Informatics, Multimedia University, 63100, Cyberjaya, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> ..", "Affiliation": "Faculty of Computing and Informatics, Multimedia University, 63100, Cyberjaya, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing and Informatics, Multimedia University, 63100, Cyberjaya, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing and Informatics, Multimedia University, 63100, Cyberjaya, Malaysia"}], "References": []}, {"ArticleId": 114734628, "Title": "Exploring the effects of overvoltage unbalances on three phase induction motors: Insights from motor current spectral analysis and discrete wavelet transform energy assessment", "Abstract": "This paper explores the diagnosis of faults related to 10 % and 20 % overvoltages in phase A of a three-phase induction motor. The study is based on numerical simulation results using MATLAB software. The primary objective of this research is to investigate the impact of such voltage unbalance on induction motors, principally on the three-phase stator currents and the electromagnetic torque. The results demonstrated that this voltage aberration causes significant harm to the motor. This is evidenced by the resulting highly unbalanced stator currents and oscillatory torque leading to subsequent mechanical disturbances. The study&#x27;s second objective is to diagnose anomalies using Motor Current Spectral Analysis (MCSA), encompassing a comprehensive analysis of all three phases. The analysis proved to be effective in detecting such faulty circumstances. The direct current ( I<sub>d</sub> ) and the quadrature current ( I<sub>q</sub> ) were also analyzed to reinforce the study. Providing an alternative perspective to explore the consequences of the imbalance. To enhance these findings and validate the previous analysis, the energy analysis based on Discrete Wavelet Transform (DWT) was applied to the three phases, I<sub>d</sub> and I<sub>q</sub> currents. The assessment focused on energy analysis, enabled the detection of abnormal conditions, and demonstrated a noteworthy coherence with spectral analysis. The observed congruence provides evidence for the dependability and effectiveness of these methods in identifying faults, whether they are used in combination or separately.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2024.109242", "PubYear": 2024, "Volume": "117", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratoire des systèmes électromécaniques, Department of Electromechanical, <PERSON>ji <PERSON> – Annaba University, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Higher School of Technology and Engineering, EEA Department, 23005, Annaba, Algeria;Correspondence author.; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Unité de Recherche en Energie Renouvelable en Milieu Saharien, URERMS, Centre de développement des Energies Renouvelables, CDER, 01000 Adrar, Algeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Reasearch Center in Industrial Technologies CRTI P.O. Box 64, Cheraga, Algiers, Algeria"}], "References": [{"Title": "Review on Machine Learning Algorithm Based Fault Detection in Induction Motors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "1929", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Wavelet packet and fuzzy logic theory for automatic fault detection in induction motor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "21", "Page": "11935", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 114734642, "Title": "Applying Generative Machine Learning to Intrusion Detection: A Systematic Mapping Study and Review", "Abstract": "<p>Intrusion Detection Systems (IDSs) are an essential element of modern cyber defense, alerting users to when and where cyber-attacks occur. Machine learning can enable IDSs to further distinguish between benign and malicious behaviors, but it comes with several challenges, including lack of quality training data and high false positive rates. Generative Machine Learning Models (GMLMs) can help overcome these challenges. This paper offers an in-depth exploration of GMLMs’ application to intrusion detection. It gives: (1) a systematic mapping study of research at the intersection of GMLMs and IDSs, and (2) a detailed review providing insights and directions for future research.</p>", "Keywords": "", "DOI": "10.1145/3659575", "PubYear": 2024, "Volume": "56", "Issue": "10", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of EECS, Washington State University, Pullman, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Montana State University, Bozeman, United States and Idaho National Laboratory, Idaho Falls, United States"}, {"AuthorId": 3, "Name": "Haipeng <PERSON>ai", "Affiliation": "School of EECS, Washington State University, Pullman, United States"}, {"AuthorId": 4, "Name": "Assefaw Gebremedhin", "Affiliation": "School of EECS, Washington State University, Pullman, United States"}], "References": [{"Title": "A review of generative adversarial networks and its application in cybersecurity", "Authors": "<PERSON>ka <PERSON>; Ogban-<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "1721", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "An analysis of generative adversarial networks and variants for image synthesis on MNIST dataset", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "19-20", "Page": "13725", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "False data injection attack (FDIA): an overview and new metrics for fair evaluation of its countermeasure", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "1", "Page": "1", "JournalTitle": "Complex Adaptive Systems Modeling"}, {"Title": "On the Variety and Veracity of Cyber Intrusion Alerts Synthesized by Generative Adversarial Networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Management Information Systems"}, {"Title": "Poly-GAN: Multi-conditioned GAN for fashion synthesis", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "414", "Issue": "", "Page": "356", "JournalTitle": "Neurocomputing"}, {"Title": "A survey of intrusion detection from the perspective of intrusion datasets and machine learning techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "44", "Issue": "7", "Page": "659", "JournalTitle": "International Journal of Computers and Applications"}, {"Title": "Malware classification and composition analysis: A survey of recent developments", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "59", "Issue": "", "Page": "102828", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "M3GAN: A masking strategy with a mutable filter for multidimensional anomaly detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "271", "Issue": "", "Page": "110585", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 114734683, "Title": "An exploratory study on manifesting decision-inertia in a 360-degree extended reality terrorist incident", "Abstract": "Emergency response decision making is crucial in managing critical incidents; however, several studies have demonstrated the negative effects of decision inertia. Understanding the manifestation and impact of decision inertia, as well as utilising extended reality (XR) technology with 360-degree immersion, should enhance decision making in high-stress environments and improve emergency response efforts. This study investigated decision inertia, using 109 participants, in an XR 360-degree environment and its impact on decision-making outcomes. The findings revealed that participants often opted for a sub-optimal outcome, and decision inertia scores varied across these outcomes. Linear regression analysis demonstrated that decision inertia scores significantly predicted decision outcomes, with higher decision inertia scores associated with sub-optimal decision-making. Participants prior moral decision-making did influence subsequent immersive reality decision outcomes and demonstrated a Bayesian updating effect. The Structured Tabular Thematic Analysis highlighted the importance of information validity, decision confidence, and scenario fidelity in decision-making within the immersive environment. The study provides insights into decision inertia in immersive virtual reality critical incidents and offers practical solutions for improving decision-making processes in emergency response contexts.", "Keywords": "Decision-making; Extended reality; Immersive environments; Emergency response", "DOI": "10.1007/s10111-024-00761-x", "PubYear": 2024, "Volume": "26", "Issue": "3", "JournalId": 2896, "JournalTitle": "Cognition, Technology & Work", "ISSN": "1435-5558", "EISSN": "1435-5566", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Criminology and Criminal Justice, University of Portsmouth, Portsmouth, England; Department of Policing, Criminology and Forensics, University of Winchester, Winchester, England; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Criminology and Criminal Justice, University of Portsmouth, Portsmouth, England"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Criminology and Criminal Justice, University of Portsmouth, Portsmouth, England"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Criminology and Criminal Justice, University of Portsmouth, Portsmouth, England"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Criminology and Criminal Justice, University of Portsmouth, Portsmouth, England"}], "References": [{"Title": "Threat assessment, sense making, and critical decision-making in police, military, ambulance, and fire services", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "3", "Page": "423", "JournalTitle": "Cognition, Technology & Work"}]}, {"ArticleId": 114734684, "Title": "人が“こころ''を感じる気の利いたロボットの実現を目指して", "Abstract": "", "Keywords": "Multimodal Information Processing;Language Understanding;Embodied Dialogue;Large Language Models;Foundation Models", "DOI": "10.7210/jrsj.42.247", "PubYear": 2024, "Volume": "42", "Issue": "3", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ritsumeikan University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Osaka University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Osaka University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology, Maizuru College"}], "References": []}, {"ArticleId": *********, "Title": "A Survey on Resilience in Information Sharing on Networks: Taxonomy and Applied Techniques", "Abstract": "<p>Information sharing is vital in any communication network environment to enable network operating services take decisions based on the information collected by several deployed computing devices. The various networks that compose cyberspace, as Internet-of-Things (IoT) ecosystems, have significantly increased the need to constantly share information, which is often subject to disturbances. In this sense, the damage of anomalous operations boosted researches aimed at improving resilience to information sharing. Hence, in this survey, we present a systematization of knowledge about scientific efforts for achieving resilience to information sharing on networks. First, we introduce a taxonomy to organize the strategies applied to attain resilience to information sharing on networks, offering brief concepts about network anomalies and connectivity services. Then, we detail the taxonomy in the face of malicious threats, network disruptions, and performance issues, discussing the presented solutions. Next, we analyze the techniques existing in the literature to foster resilience to information exchanged on communication networks to verify their benefits and constraints. Throughout the text, we highlight and argue issues that restrain the use of these techniques during the design and runtime.</p>", "Keywords": "", "DOI": "10.1145/3659944", "PubYear": 2024, "Volume": "56", "Issue": "12", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Parana, Curitiba, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Minas Gerais, Belo Horizonte, Brazil"}], "References": [{"Title": "Energy aware decision stump linear programming boosting node classification based data aggregation in WSN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "155", "Issue": "", "Page": "133", "JournalTitle": "Computer Communications"}, {"Title": "Designing an efficient clustering strategy for combined Fog-to-Cloud scenarios", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "392", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "On Resilience in Cloud Computing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Secure Intruder Information Sharing in Wireless Sensor Network for Attack Resilient Routing", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "504", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "A hybrid machine learning model for intrusion detection in VANET", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "104", "Issue": "3", "Page": "503", "JournalTitle": "Computing"}, {"Title": "A Survey on Resilience in the IoT", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "7", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": *********, "Title": "Skin Care Disease Analysis and Detection Using Machine Learning", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2024.13466", "PubYear": 2024, "Volume": "13", "Issue": "4", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Prof. <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114734841, "Title": "Strategic team design for sustainable effectiveness: A data-driven analytical perspective and its implications", "Abstract": "Teams are building blocks of organizations and essential inputs of organizational success. This article studies a data-driven analytical approach that exploits the rich data accumulated in organizations in the digital era to design teams, including prescribing team composition and formation decisions. We propose to evaluate a team regarding its performance and temporal stability, referred to as sustainable effectiveness (SE). Our approach estimates the team's performance and stability using machine learning models. It then optimizes an integrated objective of the team's performance and stability through mixed-integer programming models formulated according to predictive models. Consequently, this approach mines meaningful team compositions from historical data and guides strategic team formation accordingly. We conduct empirical studies using authentic data from our partner company in the real estate brokerage industry. The findings reveal that teams that adhere to our model's recommendations achieve an average percentage improvement of 153.1% to 156.5% higher than the benchmark teams, particularly when recruiting one or two members in their actual SE during the post-formation period. We further disclose the mechanism underlying this improvement from the perspective of changes in team compositions. Our study provides a decision support tool for team design and ensuing team dynamic management.", "Keywords": "", "DOI": "10.1016/j.dss.2024.114227", "PubYear": 2024, "Volume": "181", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business, Sun Yat-sen University, No. 135 Xingang West Road, Guangzhou 510275, Guangdong, China"}, {"AuthorId": 2, "Name": "Qin Su", "Affiliation": "<PERSON><PERSON><PERSON> Jiaotong-Liverpool University, Suzhou Dushu Lake Science and Education, Suzhou 215123, Jiangsu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Yu", "Affiliation": "School of Business, Sun Yat-sen University, No. 135 Xingang West Road, Guangzhou 510275, Guangdong, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Service Science and Operations Management, School of Management, Zhejiang University, 866 Yuhangtang Road, Hangzhou 310058, Zhejiang, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management and Marketing, Faculty of Business, The Hong Kong Polytechnic University, Hong Kong, China"}], "References": [{"Title": "Incorporating geographical location for team formation in social coding sites", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "1", "Page": "153", "JournalTitle": "World Wide Web"}, {"Title": "Employees recruitment: A prescriptive analytics approach via machine learning and mathematical programming", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "113290", "JournalTitle": "Decision Support Systems"}, {"Title": "From predictive to prescriptive analytics: A data-driven multi-item newsvendor model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "113340", "JournalTitle": "Decision Support Systems"}, {"Title": "A prescriptive analytics framework for efficient E-commerce order delivery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "147", "Issue": "", "Page": "113584", "JournalTitle": "Decision Support Systems"}, {"Title": "Job satisfaction and employee turnover determinants in Fortune 50 companies: Insights from employee reviews from Indeed.com", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "148", "Issue": "", "Page": "113582", "JournalTitle": "Decision Support Systems"}, {"Title": "A Comprehensive Review and a Taxonomy Proposal of Team Formation Problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON> (Pano) Santos; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "7", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "JANOS: An Integrated Predictive and Prescriptive Modeling Framework", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "2", "Page": "807", "JournalTitle": "INFORMS Journal on Computing"}]}, {"ArticleId": *********, "Title": "A novel diagnostic framework based on vibration image encoding and multi-scale neural network", "Abstract": "Intelligent fault diagnosis employing CNN-based techniques has demonstrated promising results in rotating machinery maintenance and management. However, most approaches that rely on time-series vibration signals fail to incorporate the physical knowledge of faults into feature learning, and are subject to obtaining the correlation between input and output characteristics. Furthermore, it is challenging for the neural network to improve feature extraction due to the rigid convolution kernel and the limited receptive field. To address these issues, an intelligent fault diagnosis framework is developed based on vibration image encoding and multi-scale neural networks. Firstly, a novel image encoding rule is designed to convert time-series signals into vibration images, which incorporate the impact characteristics of faults into the model inputs to enhance fault information. Secondly, a multi-scale feature fusion network with dilatated and irregular sampling convolution block is constructed for feature extraction and classification. In addition, a new regularization term is designed to avoid training overfitting. Finally, Grad-CAM is introduced to visualize the region of interest of the model. The proposed method is verified on the datasets with different fault types from our laboratory and DIRG bearing dataset with different fault severities from Politecnico di Torino. The experimental results demonstrate that our diagnostic framework exhibits good generalization and noise robustness, and can achieve high-precision fault diagnosis.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124054", "PubYear": 2024, "Volume": "251", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Yanshan University, Qinhuangdao, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Yanshan University, Qinhuangdao, PR China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Yanshan University, Qinhuangdao, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Yanshan University, Qinhuangdao, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Sun", "Affiliation": "Yanshan University, Qinhuangdao, PR China"}, {"AuthorId": 6, "Name": "Jing<PERSON> Liu", "Affiliation": "Yanshan University, Qinhuangdao, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Yanshan University, Qinhuangdao, PR China;Corresponding authors"}], "References": [{"Title": "An improved feature extraction method using texture analysis with LBP for bearing fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "106019", "JournalTitle": "Applied Soft Computing"}, {"Title": "Driver behavior detection and classification using deep convolutional neural networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113240", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hybrid fine-tuned VMD and CNN scheme for untrained compound fault diagnosis of rotating machinery with unequal-severity faults", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114094", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An end-to-end framework combining time–frequency expert knowledge and modified transformer networks for vibration signal classification", "Authors": "Can-can Jin; <PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "114570", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Unsupervised domain-share CNN for machine fault transfer diagnosis from steady speeds to time-varying speeds", "Authors": "Hongru Cao; Haidong Shao; Xiang <PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "186", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Dilated convolutional neural network based model for bearing faults and broken rotor bar detection in squirrel cage induction motors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116290", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An improved feature pyramid network for object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "483", "Issue": "", "Page": "127", "JournalTitle": "Neurocomputing"}, {"Title": "A novel intelligent fault diagnosis method of rotating machinery based on signal-to-image mapping and deep Gabor convolutional adaptive pooling network", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "117716", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel deep convolutional image-denoiser network for structural vibration signal denoising", "Authors": "<PERSON><PERSON>g <PERSON>; Haibei <PERSON>; Cheng Yuan", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105507", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Wind turbine fault detection based on deep residual networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119102", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Research on fault diagnosis method of MS-CNN rolling bearing based on local central moment discrepancy", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101797", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Deep branch attention network and extreme multi-scale entropy based single vibration signal-driven variable speed fault diagnosis scheme for rolling bearing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101844", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A zero-shot fault semantics learning model for compound fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "221", "Issue": "", "Page": "119642", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Condition Monitoring using Machine Learning: A Review of Theory, Applications, and Recent Advances", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "221", "Issue": "", "Page": "119738", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-modality multi-scale cardiovascular disease subtypes classification using Raman image and medical history", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "224", "Issue": "", "Page": "119965", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel deep clustering network using multi-representation autoencoder and adversarial learning for large cross-domain fault diagnosis of rolling bearings", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "120066", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114734862, "Title": "Nanowire Sensor Calibration and Performance Evaluation in Microfluidic Flow Velocity Monitoring", "Abstract": "A microfluidic chip, integrated with a contact flow velocity sensor, was fabricated. A single gold nanowire prepared by nanoskiving was positioned across the bottom of a microfluidic channel. Real-time monitoring of the microfluidic flow velocity is achieved by leveraging the principle of forced heat exchange between the fluid and the solid nanowire. To harmonize theoretical calculations and experiments, leading to the derivation of a conversion formula between the flow equivalence factor and the flow velocity. Analysis of experimental data, specifically curves of resistance versus time, yields two characteristic parameters for the nanowire sensor: the step size of the flow equivalence factor and the resistivity variation. The performance of nanowire sensor was further analyzed by examining the patterns exhibited in the relationship between characteristic parameters (resolution and response characteristics) and both voltage and inlet flow rate. This work has potential applications in biomedical sensing, microfluidic mixing, and other areas where flow velocity control is required.", "Keywords": "", "DOI": "10.1016/j.sna.2024.115397", "PubYear": 2024, "Volume": "372", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Precision Engineering, School of Mechatronics Engineering, Harbin Institute of Technology, Harbin 150001, China"}, {"AuthorId": 2, "Name": "Yongda Yan", "Affiliation": "State Key Laboratory of Robotics and System (HIT), Harbin Institute of Technology, Harbin 150001, China;Center for Precision Engineering, School of Mechatronics Engineering, Harbin Institute of Technology, Harbin 150001, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Precision Engineering, School of Mechatronics Engineering, Harbin Institute of Technology, Harbin 150001, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Robotics and System (HIT), Harbin Institute of Technology, Harbin 150001, China;Center for Precision Engineering, School of Mechatronics Engineering, Harbin Institute of Technology, Harbin 150001, China;Corresponding author at: State Key Laboratory of Robotics and System (HIT), Harbin Institute of Technology, Harbin 150001, China"}], "References": [{"Title": "A low-cost microwave metamaterial-inspired sensing platform for quantitative paper microfluidic analytical devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "363", "Issue": "", "Page": "114684", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 114734897, "Title": "Reject inference in credit scoring based on cost-sensitive learning and joint distribution adaptation method", "Abstract": "As traditional credit evaluation methods generally only use accepted sample modeling, the rejected data is omitted, which means the model's prediction of new customers is biased. However, reject inference can be used to solve this credit evaluation sample selection bias. This paper proposes a new reject inference method based on joint distribution adaptation (JDA) and cost-sensitive semi-supervised support vector machines (CS4VM). First, this method uses both accepted (labeled) samples and rejected (unlabeled) samples modeling, which overcomes the deviations in traditional credit evaluation methods. Second, as the accepted sample and the rejected sample distributions are different, this method reduces the distribution differences between the accepted and rejected sample sets, which ensures that the sample data conforms to the basic assumptions in the semi-supervised model, and improves the performance of the classification model. Third, this method reduces the overall cost in the actual credit business by considering both the traditional misclassification costs when mining the default samples and the different decision weights for the accepted and rejected samples. Finally, an empirical study verifies the excellent predictive performance of the proposed method and effectively reduces the total credit costs.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124072", "PubYear": 2024, "Volume": "251", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Finance, Southwestern University of Finance and Economics, Chengdu 611130, PR China;Fintech Innovation Center, Southwestern University of Finance and Economics, Chengdu 611130, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Finance, Southwestern University of Finance and Economics, Chengdu 611130, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Finance, Southwestern University of Finance and Economics, Chengdu 611130, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Accounting, Southwestern University of Finance and Economics, Chengdu 611130, PR China;Corresponding author"}], "References": [{"Title": "Deep generative models for reject inference in credit scoring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "196", "Issue": "", "Page": "105758", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A new approach in reject inference of using ensemble learning based on global semi-supervised framework", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "382", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Three-stage reject inference learning framework for credit scoring using unsupervised transfer learning and three-way decision theory", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "113366", "JournalTitle": "Decision Support Systems"}, {"Title": "A graph-based semi-supervised reject inference framework considering imbalanced data distribution for consumer credit scoring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "107259", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep reinforcement learning with the confusion-matrix-based dynamic reward function for customer credit scoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "200", "Issue": "", "Page": "117013", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A CWGAN-GP-based multi-task learning model for consumer credit scoring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "206", "Issue": "", "Page": "117650", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Bagging Supervised Autoencoder Classifier for credit scoring", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "118991", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Optimal cost-sensitive credit scoring using a new hybrid performance metric", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119232", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Credit risk evaluation using clustering based fuzzy classification method", "Authors": "Furkan Baser; <PERSON><PERSON><PERSON>; <PERSON><PERSON> Selcuk-Kestel", "PubYear": 2023, "Volume": "223", "Issue": "", "Page": "119882", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Consumer credit risk assessment: A review from the state-of-the-art classification algorithms, data traits, and learning methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121484", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114734933, "Title": "Mechanizing Session-Types: Enforcing Linearity without Linearity: Invited Talk at the 18th International Workshop on Logical and Semantic Frameworks, with Applications and 10th Workshop on Horn Clauses for Verification and Synthesis", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.402.3", "PubYear": 2024, "Volume": "402", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, McGill University, Montreal, Canada"}], "References": [{"Title": "Mechanizing Session-Types using a Structural View: Enforcing Linearity without Linearity", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "374", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 114734941, "Title": "LogGT: Cross-system log anomaly detection via heterogeneous graph feature and transfer learning", "Abstract": "Automated system log anomaly detection plays a crucial role in ensuring service reliability. Existing methods incompletely utilize structured log entries, resulting in the loss of key information such as components and time. Besides, due to the limitations of labeled data, models trained by a single system are difficult to apply to other systems. Therefore, we propose a cross-system log anomaly detection method named LogGT, which simultaneously models log events, components and time, leveraging labeled system knowledge to achieve anomaly detection in unlabeled systems. Specifically, we firstly design a heterogeneous graph to accurately represent the interactions between different events and components in the log sequence. Then, in order to avoid noise interference and conduct cross-system semantic analysis, we employ BERT to extract log sentence vectors, and Normalizing Flow is used to optimize them for smoother node embedding. A Graph Transformer Architecture with Time Intervals (GTAT) is proposed to model heterogeneous graphs by integrating time feature, allowing for a comprehensive analyze of execution order and time anomalies. Additionally, we design a semantic weighting method and utilize a novel domain-adapted transfer learning technology to effectively transfer the heterogeneous graph features of the source system to the target system. Experimental results demonstrate that LogGT outperforms five log anomaly detection methods, achieving an average anomaly detection F1-score higher than 0.95. Moreover, the AUC value of GTAT exceeds the sequence model by more than 2.3%.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124082", "PubYear": 2024, "Volume": "251", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information Science and Technology College, Dalian Maritime University, Dalian, Liaoning, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Science and Technology College, Dalian Maritime University, Dalian, Liaoning, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Science and Technology College, Dalian Maritime University, Dalian, Liaoning, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Science and Technology College, Dalian Maritime University, Dalian, Liaoning, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Information Science and Technology College, Dalian Maritime University, Dalian, Liaoning, China"}], "References": [{"Title": "LogNADS: Network anomaly detection scheme based on log semantics representation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "124", "Issue": "", "Page": "390", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A Survey on Automated Log Analysis for Reliability Engineering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "LightLog: A lightweight temporal convolutional network for log anomaly detection on the edge", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "108616", "JournalTitle": "Computer Networks"}, {"Title": "Relation-aware Graph Convolutional Networks for Multi-relational Network Alignment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "MLog: Mogrifier LSTM-Based Log Anomaly Detection Approach Using Semantic Representation", "Authors": "Yuanyuan Fu; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "5", "Page": "3537", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Discrete log anomaly detection: A novel time-aware graph-based link prediction approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "647", "Issue": "", "Page": "119576", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 114734955, "Title": "A novel approach to optimize an integrated network design and pricing of a healthcare supply chain", "Abstract": "Adopting various pricing policies has been highly regarded in recent years for setting prices and increasing firms' profits. One of the most common steps in pricing is to identify costs. Since a significant part of costs is related to the corresponding supply chain, many researchers in different fields have used decision-making for simultaneous pricing and network design. However, there is no such approach in the field of healthcare. This paper tries to fill this gap by formulating a mixed-integer nonlinear bi-level programming model examining the interaction of hospitals and their medicines suppliers. At the upper level, there is a competitive market where a new firm (entrant) intends to enter the market and faces the challenge of pricing medicines and making network design decisions. At the lower level, there is a private hospital competing with a public hospital, and it also struggles with healthcare services pricing and supplier selection. A comprehensive utility function that considers healthcare services prices, quality, waiting time, health insurance, readmission rate, and referral rate is extended at this level. Three novel meta -heuristic algorithms are recommended, including bi-level, improved fruit fly, jellyfish optimization, and forensic-based investigation optimization algorithms to solve the presented complex mathematical problem.", "Keywords": "Healthcare; Pricing; Network design; Supply chain; Bi-level programming; Artificial intelligence", "DOI": "10.1016/j.eswa.2024.123976", "PubYear": 2024, "Volume": "252", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Industrial Engineering, College of Engineering, University of Tehran, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Industrial Engineering, College of Engineering, University of Tehran, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Business Analytics & Operations, Surrey Business School, University of Surrey, Guildford, UK;Department of Systems Engineering, Faculty of Economics, Technical University of Ostrava, Czechia;Corresponding author at: Faculty of Arts and Social Sciences, University of Surrey, Alexander Fleming Rd, Guildford GU2 7XH, UK"}], "References": [{"Title": "Joint location and pricing within a user-optimized environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "1", "Page": "61", "JournalTitle": "EURO Journal on Computational Optimization"}, {"Title": "FBI inspired meta-optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106339", "JournalTitle": "Applied Soft Computing"}, {"Title": "Public and private healthcare coordination: An analysis of contract mechanisms based on subsidy payments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "106526", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A multi-modal competitive hub location pricing problem with customer loyalty and elastic demand", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "105048", "JournalTitle": "Computers & Operations Research"}, {"Title": "Mitigating the risk of hazardous materials transportation: A hierarchical approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "106735", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Fast-moving consumer goods network design with pricing policy in an uncertain environment with correlated demands", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "153", "Issue": "", "Page": "106997", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An integrated sustainable medical supply chain network during COVID-19", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "104188", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 114734964, "Title": "River ecosystem health assessment in the Qinghai-Tibet Plateau: A novel hybrid method based on artificial intelligence and multi-source data fusion", "Abstract": "River ecosystem health assessment (REHA), an effective approach for identifying river ecosystem health, is crucial for achieving sustainable river management and ensuring water security. However, existing REHA methods still fail to consider the cumulated influences of uncertain inputs, stochastic environment and limited rationality of decision makers on REHA. Additionally, current REHA studies have mainly concentrated on plain areas, while the Qinghai-Tibet Plateau (QTP) remains largely unknown. Developing REHA techniques for plateau rivers is an urgent matter, due to the heightened fragility and complexity of river ecosystems in the QTP. To accurately assess river ecosystem health in the QTP, this study proposed Pythagorean fuzzy cloud (PFC) via coupling the Pythagorean fuzzy sets and cloud model. A novel PFC-TODIM model was developed by extending TODIM (the acronym in Portuguese for interactive and multicriteria decision making) to the Pythagorean fuzzy environment. The hybrid decision making framework was then created to handle REHA with uncertain inputs and stochastic environment, and the Senge Tsangpo River (STR) served as a case study in the QTP. We developed the indicator system based on multi-source data fusion, and employed Bayesian model averaging (BMA) method to reveal the potential risks and driving factors of river ecosystem health. Results showed that the developed models considered the limited rationality of decision makers, effectively handled REHA with uncertainties, and avoided overestimating river health levels due to ignoring the randomness and fuzziness of REHA. In STR, health statuses exhibited marked spatial differences. Sampling sites of 9.091%, 77.273 % and 13.636 % were excellent, healthy and subhealthy, respectively. Our findings highlight that dams, urban development, fish release, and grazing have adverse impacts on STR health, and effective protection measures are required to minimize human interferences on ecologically fragile areas. These findings can improve our understanding of the human disturbances and natural factors that interfere with river ecosystem health in the QTP.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124078", "PubYear": 2024, "Volume": "251", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Collaborative Innovation Center of Sustainable Forestry in Southern China of Jiangsu Province, Nanjing Forestry University, Nanjing 210037, China;Jiangsu Provincial Key Lab of Soil Erosion and Ecological Restoration, Nanjing Forestry University, Nanjing 210037, China;State Key Laboratory of Hydrology-Water Resources and Hydraulic Engineering, Nanjing Hydraulic Research Institute, Nanjing 210029, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydrology-Water Resources and Hydraulic Engineering, Nanjing Hydraulic Research Institute, Nanjing 210029, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Hydrology-Water Resources and Hydraulic Engineering, Nanjing Hydraulic Research Institute, Nanjing 210029, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Geography and Ocean Science, Nanjing University, Nanjing, Jiangsu 210023, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydrology-Water Resources and Hydraulic Engineering, Nanjing Hydraulic Research Institute, Nanjing 210029, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydrology-Water Resources and Hydraulic Engineering, Nanjing Hydraulic Research Institute, Nanjing 210029, China"}, {"AuthorId": 8, "Name": "<PERSON>ze Li", "Affiliation": "State Key Laboratory of Hydrology-Water Resources and Hydraulic Engineering, Nanjing Hydraulic Research Institute, Nanjing 210029, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Departmnent of Geography and Environmental Sciences, University of Reading, Reading RG6 6AB, UK;Corresponding authors"}], "References": [{"Title": "Water quality evolution mechanism modeling and health risk assessment based on stochastic hybrid dynamic systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116404", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Automatic labeling of river restoration project documents based on project objectives and restoration methods", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "197", "Issue": "", "Page": "116754", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Randomness-oriented Multi-dimensional Cloud-based belief rule Base approach for complex system modeling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117283", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114735062, "Title": "Transferable multi-objective factory layout planning using simulation-based deep reinforcement learning", "Abstract": "Factory layout planning aims at finding an optimized layout configuration under consideration of varying influences such as the material flow characteristics. Manual layout planning can be characterized as a complex decision-making process due to a large number of possible placement options. Automated planning approaches aim at reducing the manual planning effort by generating optimized layout variants in the early stages of layout planning. Recent developments have introduced deep Reinforcement Learning (RL) based planning approaches that allow to optimize a layout under consideration of a single optimization criterion. However, within layout planning, multiple partially conflicting planning objectives have to be considered. Such multiple objectives are not considered by existing RL-based approaches. This paper addresses this research gap by presenting a novel deep RL-based layout planning approach that allows consideration of multiple objectives for optimization. Furthermore, existing RL-based planning approaches only consider analytically formulated objectives such as the transportation distance. Consequently, dynamic influences in the material flow are neglected which can result in higher operational costs of the future factory. To address this issue, a discrete event simulation module is developed that allows simulating manufacturing and material flow processes simultaneously for any layout configuration generated by the RL approach. Consequently, the presented approach considers material flow simulation results for multi-objective optimization. To investigate the capabilities of RL-based factory layout planning, different RL architectures are compared based on a simplified application scenario. Throughput time, media supply, and material flow clarity are considered as optimization objectives. The best performing architecture is then applied to an exemplary application scenario and compared with the results obtained by a combined version of the genetic algorithm and tabu search, the non-dominated sorting genetic algorithm, and the optimal solution. Finally, two industrial planning scenarios, one focusing on brownfield and one on greenfield planning, are considered. The results show that the performance of RL compared to meta-heuristics depends on the considered computation time. With time the results generated by the RL approach exceed the quality of the best conventional solution by up to 11%. Finally, the potential of applying transfer learning is investigated for three different application scenarios. It is observed that RL can learn generalized patterns for factory layout planning, which allows to significantly reduce the required training time and can lead to improved solution quality. Thus, the use of pre-trained RL models shows a substantial performance potential for automated factory layout planning which cannot be achieved with conventional automated planning approaches.", "Keywords": "ANN Artificial Neural Networks; CNN Convolutional Neural Network; DDQN Double Deep Q Learning; DES Discrete Event Simulation; GA Genetic Algorithm; GNN Graph Neural Network; NSGA Non-Dominated Sorting Genetic Algorithm; PER Prioritized Experience Replay; Rainbow DQN Rainbow Deep Q Learning; RL Reinforcement Learning; SA Simulated Annealing; TS Tabu Search; Facility layout problem; Reinforcement learning; Multi-objective optimization; Discrete event simulation; Material flow", "DOI": "10.1016/j.jmsy.2024.04.007", "PubYear": 2024, "Volume": "74", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Manufacturing Technology and Production Systems, RPTU Kaiserslautern, Germany;Correspondence to: P.O. Box 3049, 67653 Kaiserslautern, Germany.; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Manufacturing Technology and Production Systems, RPTU Kaiserslautern, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute for Manufacturing Technology and Production Systems, RPTU Kaiserslautern, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute for Manufacturing Technology and Production Systems, RPTU Kaiserslautern, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Manufacturing Technology and Production Systems, RPTU Kaiserslautern, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, UC Davis, CA, United States"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Institute for Manufacturing Technology and Production Systems, RPTU Kaiserslautern, Germany"}], "References": [{"Title": "Reinforcement learning for facilitating human-robot-interaction in manufacturing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "326", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "Multi-objective optimization and innovization-based knowledge discovery of sustainable machining process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "", "Page": "636", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Opportunistic maintenance scheduling with deep reinforcement learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "", "Page": "518", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Dynamic scheduling of tasks in cloud manufacturing with multi-agent reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "", "Page": "130", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A practical guide to multi-objective reinforcement learning and planning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "1", "JournalTitle": "Autonomous Agents and Multi-Agent Systems"}, {"Title": "Explainable generative design in manufacturing for reinforcement learning based factory layout planning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "72", "Issue": "", "Page": "74", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Multi agent reinforcement learning for online layout planning and scheduling in flexible assembly systems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "35", "Issue": "8", "Page": "3917", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 114735073, "Title": "AFSRNet: learning local descriptors with adaptive multi-scale feature fusion and symmetric regularization", "Abstract": "<p>Multi-scale feature fusion has been widely used in handcrafted descriptors, but has not been fully explored in deep learning-based descriptor extraction. Simple concatenation of descriptors of different scales has not been successful in significantly improving performance for computer vision tasks. In this paper, we propose a novel convolutional neural network, based on center-surround adaptive multi-scale feature fusion. Our approach enables the network to focus on different center-surround scales, resulting in improved performance. We also introduce a novel regularization technique that uses second-order similarity to constrain the learning of local descriptors, based on the symmetric property of the similarity matrix. The proposed method outperforms single-scale or simple-concatenation descriptors on two datasets and achieves state-of-the-art results on the <PERSON> dataset. Furthermore, our method demonstrates excellent generalization ability on the HPatches dataset. Our code is released on GitHub: https://github.com/Leung-GD/AFSRNet/tree/main .</p>", "Keywords": "Local descriptor; Multi-scale feature fusion; Symmetric regularization", "DOI": "10.1007/s10489-024-05418-w", "PubYear": 2024, "Volume": "54", "Issue": "7", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Guangdong University Of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong University Of Technology, Guangzhou, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The Hong Kong Polytechnic University, HongKong, China"}], "References": [{"Title": "Image Matching from Handcrafted to Deep Features: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "1", "Page": "23", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Image Matching Across Wide Baselines: From Paper to Practice", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "2", "Page": "517", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Learning local descriptors with multi-level feature aggregation and spatial context pyramid", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "461", "Issue": "", "Page": "99", "JournalTitle": "Neurocomputing"}, {"Title": "MFANet: Multi-scale feature fusion network with attention mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Qingcheng Cao", "PubYear": 2023, "Volume": "39", "Issue": "7", "Page": "2969", "JournalTitle": "The Visual Computer"}, {"Title": "Multimodal hybrid features in 3D ear recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "10", "Page": "11618", "JournalTitle": "Applied Intelligence"}, {"Title": "FeMIP: detector-free feature matching for multimodal images with policy gradient", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "20", "Page": "24068", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 114735119, "Title": "A Probabilistic Reliable Linguistic Model for Blockchain-Based Student Information Management System Assessment", "Abstract": "The growing necessity for robust security and trust in systems has prompted the widespread adoption of blockchain technology in student information management systems (SIMS). While existing literature has concentrated on the development of blockchain-based student information management systems (BC-SIMS), a notable gap remains in the evaluation of these systems. This study addresses this gap by employing a Multi-Criteria Decision-Making (MCDM) approach. Recognizing the inherent uncertainties in BC-SIMS evaluation due to inadequate information, the study introduces the Probabilistic Reliable Linguistic Term Sets (PRLTSs) theory to address these problems. As a result, the Measurement of Alternatives and Ranking according to the Compromise Solution (MARCOS) method is extended into the Probabilistic Reliable Linguistic environment, resulting in the Probabilistic Reliable Linguistic-Measurement of Alternatives and Ranking according to the Compromise Solution (PRL-MARCOS) method. This method supports the evaluation and selection decisions on BC-SIMS in higher education institutions. The study further designs the Probabilistic Reliable Linguistic-Coefficient of Variation (PRL-CV) method to determine the relative importance of selection criteria. Twenty selection criteria for BC-SIMS are identified through a literature review, and an illustrative case study is employed to assess the practicality and validity of the proposed PRL-CV-MARCOS approach in a higher educational institution. Experimental results highlight cost reduction (0.0778), interoperability (0.0717), security (0.0628), service improvement (0.0557) and responsiveness (0.0538) as the top five criteria for BC-SIMS selection, with BC-SIMS P 5 identified as the optimal solution for managing student information. Finally, a comprehensive comparative and sensitivity analysis verifies the stability and effectiveness of the proposed decision support model.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111645", "PubYear": 2024, "Volume": "159", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Education, Zhejiang Normal University, Jinhua City, Zhejiang 321004, China;Zhejiang Key Laboratory of Intelligent Education Technology and Application, Zhejiang Normal University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Psychology, Zhejiang Normal University, Jinhua City, Zhejiang 321004, China"}, {"AuthorId": 3, "Name": "Collins Opoku Antwi", "Affiliation": "College of Geography and Environmental Science, Zhejiang Normal University, Jinhua City, Zhejiang 321004, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information and Software Engineering, University of Electronic Science and Technology of China, Chengdu 610054, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Business, University for Development Studies, P. O. Box 1350, Tamale, Ghana"}, {"AuthorId": 6, "Name": "Xiao<PERSON> Ma", "Affiliation": "School of Education, Zhejiang Normal University, Jinhua City, Zhejiang 321004, China;Zhejiang Key Laboratory of Intelligent Education Technology and Application, Zhejiang Normal University"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang Key Laboratory of Intelligent Education Technology and Application, Zhejiang Normal University;School of Computer Science and Technology, Zhejiang Normal University, Jinhua, Zhejiang 321004, China;Corresponding author at: Zhejiang Key Laboratory of Intelligent Education Technology and Application, Zhejiang Normal University"}], "References": [{"Title": "Literature review of Industry 4.0 and related technologies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "1", "Page": "127", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Sustainable supplier selection in healthcare industries using a new MCDM method: Measurement of alternatives and ranking according to COmpromise solution (MARCOS)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106231", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Evaluating the feasibility of blockchain in logistics operations: A decision framework", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "158", "Issue": "", "Page": "113543", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Blockchain in healthcare: A systematic literature review, synthesizing framework and future research agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "122", "Issue": "", "Page": "103290", "JournalTitle": "Computers in Industry"}, {"Title": "A framework of blockchain technology adoption: An investigation of challenges and expected value", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "3", "Page": "103444", "JournalTitle": "Information & Management"}, {"Title": "A multi-criteria decision making method based on DNMA and CRITIC with linguistic D numbers for blockchain platform evaluation", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104200", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Blockchain Applications in Agribusiness: A Systematic Review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "4", "Page": "95", "JournalTitle": "Future Internet"}, {"Title": "Probabilistic reliable linguistic term sets applied to investment project selection with the gained and lost dominance score method", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "2163", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Smart Contracts Based on Blockchain for Decentralized Learning Management System", "Authors": "<PERSON><PERSON>; Tallat Naz", "PubYear": 2021, "Volume": "2", "Issue": "4", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "A systematic review of blockchain scalability: Issues, solutions, analysis and future research", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Ray C.C. Cheung", "PubYear": 2021, "Volume": "195", "Issue": "", "Page": "103232", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Verification of University Student and Graduate Data using Blockchain Technology", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "5", "Page": "1", "JournalTitle": "International Journal of Computers Communications & Control"}, {"Title": "A q-rung orthopair fuzzy MARCOS method using novel score function and its application to solid waste management", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "8", "Page": "8770", "JournalTitle": "Applied Intelligence"}, {"Title": "A Fuzzy-Multi Attribute Decision Making approach for efficient service selection in cloud environments", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "206", "Issue": "", "Page": "117526", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Integrated intelligent decision support model for ranking regional transport infrastructure programmes based on performance assessment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "222", "Issue": "", "Page": "119852", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A generalized linguistic gained and lost dominance score method for landslide hazard treatment", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "145", "Issue": "", "Page": "110567", "JournalTitle": "Applied Soft Computing"}, {"Title": "A probabilistic reliable linguistic PROBID method for selecting electronic mental health platforms considering users’ bounded rationality", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "", "Page": "106716", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An intuitionistic fuzzy-based model for performance evaluation of EcoPorts", "Authors": "Galip <PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107192", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 114735153, "Title": "Three-way decision method based on triangular norms in incomplete information systems and its applications in medical diagnosis", "Abstract": "Three-way decision, as an extension of traditional two-way decision, was proposed by <PERSON> in 2009, which can effectively avoid unnecessary losses caused by incorrect decisions in the decision-making process. Meanwhile, incomplete hybrid information systems represent the database of the relationship between objects and attributes, which refers to a system with multiple data and missing data. In this paper, based on the fact that many existing literatures involving incomplete hybrid information systems did not fully consider the impact of different conditional attributes on decision attributes and lacked effective aggregation methods to integrate weights and distances, we propose a new three-way decision method to deal with incomplete hybrid information systems with the help of triangular norms. First, in incomplete hybrid information systems, we redefine the distance between two objects based on conditional attributes and give the calculation formula of different data attributes in conditional attributes. At the same time, we define a new weight calculation method based on the relationship between conditional attributes and decision attributes. And then, by the distance between different conditional attributes and their corresponding weights, the hybrid distance is obtained using triangular norms. Furthermore, we use the hybrid distance to get the tolerance relation on the target set of an incomplete hybrid information system, thereby, using this tolerance relation, we get a new decision theoretic rough set model and the corresponding decision rules. Finally, by two experiments involving medical diagnosis, we demonstrate that our model has better classification ability, lower misclassification rate, and better stability compared to other corresponding models, thereby confirming that the proposed model provides a new and effective method for handling incomplete hybrid information systems in practical applications.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111657", "PubYear": 2024, "Volume": "159", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Statistics, Northwest Normal University, Lanzhou 730070, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Statistics, Northwest Normal University, Lanzhou 730070, PR China;Gansu Provincial Research Center for Basic Disciplines of Mathematics and Statistics, Lanzhou, PR China;Corresponding author at: College of Mathematics and Statistics, Northwest Normal University, Lanzhou 730070, PR China"}], "References": [{"Title": "Interval-valued fuzzy reasoning algorithms based on Schweizer–Sklar t-norms and its application", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103313", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A three-way decision method in a hybrid decision information system and its application in medical diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "7", "Page": "4707", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A novel three-way decision method in a hybrid information system with images and its application in medical diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "103651", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "The geometry of three-way decision", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "9", "Page": "6298", "JournalTitle": "Applied Intelligence"}, {"Title": "Group decision-making based on the aggregation of Z-numbers with Archimedean t-norms and t-conorms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "569", "Issue": "", "Page": "264", "JournalTitle": "Information Sciences"}, {"Title": "Convex combination-based consensus analysis for intuitionistic fuzzy three-way group decision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "542", "JournalTitle": "Information Sciences"}, {"Title": "TWD-SFNN: Three-way decisions with a single hidden layer feedforward neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "579", "Issue": "", "Page": "15", "JournalTitle": "Information Sciences"}, {"Title": "On three perspectives for deriving three-way decision with linguistic intuitionistic fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "588", "Issue": "", "Page": "350", "JournalTitle": "Information Sciences"}, {"Title": "Three-way decision model under a large-scale group decision-making environment with detecting and managing non-cooperative behaviors in consensus reaching process", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "7", "Page": "5517", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A cost-sensitive temporal-spatial three-way recommendation with multi-granularity decision", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "670", "JournalTitle": "Information Sciences"}, {"Title": "A regret theory-based three-way decision approach with three strategies", "Authors": "<PERSON><PERSON> Zhu; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "595", "Issue": "", "Page": "89", "JournalTitle": "Information Sciences"}, {"Title": "General three-way decision models on incomplete information tables", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "605", "Issue": "", "Page": "136", "JournalTitle": "Information Sciences"}, {"Title": "A three-way multi-attribute decision making method based on regret theory and its application to medical data in fuzzy environments", "Authors": "<PERSON><PERSON> Zhu; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108975", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel behavioral three-way decision model with application to the treatment of mild symptoms of COVID-19", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "109055", "JournalTitle": "Applied Soft Computing"}, {"Title": "A three-way decision method with prospect theory to multi-attribute decision-making and its applications under hesitant fuzzy environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "109283", "JournalTitle": "Applied Soft Computing"}, {"Title": "A three-way decision approach with a probability dominance relation based on prospect theory for incomplete information systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "611", "Issue": "", "Page": "199", "JournalTitle": "Information Sciences"}, {"Title": "Regret-based three-way decision making with possibility dominance and SPA theory in incomplete information system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118688", "JournalTitle": "Expert Systems with Applications"}, {"Title": "The intuitionistic fuzzy concept-oriented three-way decision model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "619", "Issue": "", "Page": "52", "JournalTitle": "Information Sciences"}, {"Title": "A TFN-based uncertainty modeling method in complex evidence theory for decision making", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "619", "Issue": "", "Page": "193", "JournalTitle": "Information Sciences"}, {"Title": "A relative granular ratio-based outlier detection method in heterogeneous data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "622", "Issue": "", "Page": "710", "JournalTitle": "Information Sciences"}, {"Title": "Three-way neighborhood based stream computing for incomplete hybrid information system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "262", "Issue": "", "Page": "110232", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Attribute reduction for hybrid data based on fuzzy rough iterative computation model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "632", "Issue": "", "Page": "555", "JournalTitle": "Information Sciences"}, {"Title": "Social network trust relationship environment based advanced ovarian cancer treatment decision-making model: An approach based on linguistic information with experts’ multiple confidence levels", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "229", "Issue": "", "Page": "120407", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Convex granules and convex covering rough sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "", "Page": "106509", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "FTransCNN: Fusing Transformer and a CNN based on fuzzy logic for uncertain medical image segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101880", "JournalTitle": "Information Fusion"}, {"Title": "Three-way decisions based on hesitant sets over three-way decision spaces", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "647", "Issue": "", "Page": "119365", "JournalTitle": "Information Sciences"}, {"Title": "Consensus of three-way group decision with weight updating based on a novel linguistic intuitionistic fuzzy similarity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "648", "Issue": "", "Page": "119537", "JournalTitle": "Information Sciences"}, {"Title": "Three-way imbalanced learning based on fuzzy twin SVM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "150", "Issue": "", "Page": "111066", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 114735186, "Title": "Development of real-time brain-computer interface control system for robot", "Abstract": "Electroencephalogram (EEG)-based brain-computer interfaces (BCI) have been considered a prevailing non-invasive method for collecting human biomedical signals by attaching electrodes to the scalp. However, it is difficult to detect and use these signals to control an online BCI robot in a real environment owing to environmental noise. In this study, a novel state recognition model is proposed to determine and improve EEG signal states. First, a Long Short-Term Memory Convolutional Neural Network (LSTM-CNN) was designed to extract EEG features along the time sequence. During this process, errors caused by the randomness of the mind or external environmental factors may be generated. Thus, an actor-critic based decision-making model was proposed to correct these errors. The model consists of two networks that can be used to predict the final signal state based on both the current signal state probability and past signal state probabilities. Subsequently, a hybrid BCI real-time control system application is proposed to control a BCI robot. The Unicorn Hybrid Black EEG device was used to acquire brain signals. A data transmission system was constructed using OpenViBE to transfer data. An EEG classification system was built to classify the BCI commands. In this experiment, EEG data from five subjects were collected to train and test the performance and reliability of the proposed control system. The system records the time spent by the robot and the moving distance. Experimental results were provided to demonstrate the feasibility of the real-time control system. Compared to similar BCI studies, the proposed hybrid BCI real-time control system can accurately classify seven BCI commands in a more reliable and precise manner. Overall, the offline testing accuracy was 87.20%. When we apply the proposed system to control a BCI robot in a real environment, the average online control accuracy is 93.12%, and the mean information transmission rate is 67.07 bits/min, which is better than those of some state-of-the-art control systems. This shows that the proposed hybrid BCI real-time control system demonstrated higher reliability, which can be used in practical BCI control applications.", "Keywords": "Brain computer interface; Electroencephalogram; EEG robot", "DOI": "10.1016/j.asoc.2024.111648", "PubYear": 2024, "Volume": "159", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Artificial Intelligence and Big Data for Medical Science, Shandong First Medical University & Shandong Academy of Medical Sciences, Jinan 250117, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Building Environment, Faculty of Design, Architecture and Building, University of Technology Sydney, Ultimo, NSW 2007, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical and Data Engineering, Faculty of Engineering and IT, University of Technology Sydney, Ultimo, NSW 2007, Australia;Corresponding author"}], "References": [{"Title": "Multi-classification for EEG motor imagery signals using data evaluation-based auto-selected regularized FBCSP and convolutional neural network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "16", "Page": "12001", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": *********, "Title": "Immersive situational analysis method based on generalized augmented grid statistics", "Abstract": "The governance of Virtual-Reality Integration, which seamlessly merges the virtual world (metaverse) with the physical reality, represents an emerging approach to addressing perception and comprehension challenges in complex computational environments. Such Virtual-Reality Integration systems have the capability to streamline data analysis complexity, offer real-time visualization, and provide user-centric interaction, thereby delivering crucial support for data analysis and profound decision-making in complex computational settings. In this paper, we introduce a real-time perception and interaction methodology that combines computer vision with Virtual-Reality Integration technology. We employ the Grid-ORB algorithm-based approach for high-precision feature extraction and three-dimensional registration tracking on resource-constrained devices, enabling the perception of physical entities. Furthermore, we utilize the Kriging method, augmented with a drift term, to fill gaps in numerical physical space data, aiding users in observing real-world physical values and trend fluctuations. To facilitate a unified cognitive experience for data and knowledge, we devise a user-centric interaction interface using augmented reality technology. Within this interface, users can interact with charts and controls through methods such as eye movement and gestures. Finally, we validate our system within a real thermodynamics experimental environment, with results demonstrating a significant enhancement in user efficiency for comprehending data and knowledge within complex environments.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111651", "PubYear": 2024, "Volume": "162", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Network Information Center, Chinese Academy of Sciences, China;University of Chinese Academy of Sciences, China"}, {"AuthorId": 2, "Name": "Guihua Shan", "Affiliation": "Computer Network Information Center, Chinese Academy of Sciences, China;University of Chinese Academy of Sciences, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Coggin College of Business, University of North Florida, United States of America"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Management Development Institute, Gurgaon, India"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Computer Network Information Center, Chinese Academy of Sciences, China;University of Chinese Academy of Sciences, China;Corresponding author at: Computer Network Information Center, Chinese Academy of Sciences, China"}], "References": [{"Title": "Image Matching from Handcrafted to Deep Features: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "1", "Page": "23", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 114735237, "Title": "Newton Geometric Iterative Method for B-Spline Curve and Surface Approximation", "Abstract": "We introduce a progressive and iterative method for B-spline curve and surface approximation, incorporating parameter correction based on the Newton iterative method. While parameter corrections have been used in existing Geometric Approximation (GA) methods to enhance approximation quality, they suffer from low computational efficiency. Our approach unifies control point updates and parameter corrections in a progressive and iterative procedure, employing a one-step strategy for parameter correction. We provide a theoretical proof of convergence for the algorithm, demonstrating its superior computational efficiency compared to current GA methods. Furthermore, the provided convergence proof offers a methodology for proving the convergence of existing GA methods with location parameter correction.", "Keywords": "", "DOI": "10.1016/j.cad.2024.103716", "PubYear": 2024, "Volume": "172", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Weihai, China"}, {"AuthorId": 2, "Name": "Pengbo Bo", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Weihai, China;Corresponding author"}], "References": [{"Title": "On extended progressive and iterative approximation for least squares fitting", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "591", "JournalTitle": "The Visual Computer"}, {"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> progressive iterative approximation (GS-PIA) for subdivision surface interpolation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "1", "Page": "139", "JournalTitle": "The Visual Computer"}, {"Title": "Improving geometric iterative approximation methods using local approximations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "116", "Issue": "", "Page": "33", "JournalTitle": "Computers & Graphics"}, {"Title": "Constrained least square progressive and iterative approximation (CLSPIA) for B-spline curve and surface fitting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Chongyang Deng", "PubYear": 2024, "Volume": "40", "Issue": "6", "Page": "4427", "JournalTitle": "The Visual Computer"}, {"Title": "Full-LSPIA: A Least-Squares Progressive-Iterative Approximation Method with Optimization of Weights and Knots for NURBS Curves and Surfaces", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "169", "Issue": "", "Page": "103673", "JournalTitle": "Computer-Aided Design"}]}, {"ArticleId": 114735290, "Title": "A Hybrid Neural Network-based Fast Financial Fraud Detection Model", "Abstract": "With the increasing number of financial transactions, financial fraud has become increasingly serious for financial institutions and the public. The core idea of this model is to integrate multiple neural network structures and utilize their respective advantages to improve the performance of fraud detection. Firstly, we employed the convolutional neural network with interpretable blocks (CNNIB) convolutional neural network (CNN) to extract key features from the data to capture patterns and patterns in fraud cases. Secondly, we introduced the autoencoder generative adversarial network (AE-GAN) adversarial network to perform feature analysis on sequence data to capture temporal features in transaction sequences. Finally, we used differential detection for classification to determine whether transactions were fraudulent. An independent detection module was established to accelerate the recognition of financial fraud, and parameter indicators were optimized. Finally, a hybrid neural network model was established. The experimental results indicate that our model has achieved significant results in quickly detecting financial fraud; compared with traditional single neural network models, hybrid neural network models have significant improvements in accuracy and efficiency. In addition, we conducted in-depth analysis of the model and revealed its performance stability under different training set sizes and data distributions. Our research findings provide an effective tool for financial institutions to quickly identify financial fraud.", "Keywords": "", "DOI": "10.1142/S0218126624502773", "PubYear": 2024, "Volume": "33", "Issue": "15", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CSG Digital Enterprise Technology (Guangdong), Co., Ltd., Guangzhou City 510700, P. R. China;School of Management, Guangdong University of Technology, Guangzhou 510700, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSG Digital Enterprise Technology (Guangdong), Co., Ltd., Guangzhou City 510700, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "CSG Digital Enterprise Technology (Guangdong), Co., Ltd., Guangzhou City 510700, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "CSG Digital Enterprise Technology (Guangdong), Co., Ltd., Guangzhou City 510700, P. R. China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSG Digital Enterprise Technology (Guangdong), Co., Ltd., Guangzhou City 510700, P. R. China"}], "References": [{"Title": "Ensemble of deep sequential models for credit card fraud detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "106883", "JournalTitle": "Applied Soft Computing"}, {"Title": "Ensemble of deep sequential models for credit card fraud detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "106883", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep Graph neural network-based spammer detection under the perspective of heterogeneous cyberspace", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "205", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Deep recurrent neural network-based autoencoder for photoplethysmogram artifacts filtering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "92", "Issue": "", "Page": "107065", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Evaluating chaotic functions with flower pollination algorithm for modelling an optimized low complexity neural network based NAV predictor model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "18", "Page": "9395", "JournalTitle": "Soft Computing"}, {"Title": "Opportunistic capacity based resource allocation for 6G wireless systems with network slicing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "390", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Anomaly detection method for vehicular network based on collaborative deep support vector data description", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "101940", "JournalTitle": "Physical Communication"}, {"Title": "Convolutional neural network-based high-precision and speed detection system on CIDDS-001", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "102130", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "Reliability–Security Tradeoff Analysis in mmWave Ad Hoc–based CPS", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "20", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}, {"Title": "Enhanced Neural Network-Based Univariate Time-Series Forecasting Model for Big Data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "12", "Issue": "2", "Page": "83", "JournalTitle": "Big Data"}, {"Title": "Hybrid gated recurrent unit and convolutional neural network-based deep learning mechanism for efficient shilling attack detection in social networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "108", "Issue": "", "Page": "108673", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 114735453, "Title": "Foot-end Global Trajectory Planning via GCN-based Heuristic Tree Search for Crossing Obstacles", "Abstract": "Achieving smooth motion for multi-legged robots on complex terrains is a significant focus of research. When encountering high obstacles, robots often need to alter their motion direction to avoid them, increasing redundancy in their motion trajectories. To address this challenge, this paper proposes a method for planning the foot-end trajectory during the swing phase while considering obstacle avoidance without modifying the fuselage trajectory. The method combines the Global Optimal Path Search Tree (GOPST) algorithm and a prior path estimation method utilizing Graph Convolutional Network (GCN). The GOPST explores multiple global paths by conducting local path tree searches in each step, guided by an objective function. To enhance efficiency, redundant search branches with high intensity or high possibility of collision with obstacles or other feet, etc. are eliminated using an event-triggering mechanism based on expert constraints. Another objective function is also formulated to obtain an optimal path that offers a larger safety space and a shorter path length. The optimal path nodes and their environmental features are integrated into a GCN for training. Before the operation of the GOPST, the GCN network provides a preliminary path with fast estimation speed. If the estimated path falls outside the safety margin, the GOPST is reactivated to explore a reliable path. Numerical simulation results validate that the GOPST–GCN approach can rapidly generate a smooth trajectory within the safety space of the foot-end workspace. Furthermore, the search time for finding the optimal path in an untrained environment decreases as the number of tests increases. Experimental verification confirms that robots successfully avoid obstacles by employing foot-end swinging without altering the initial motion direction of the fuselage. The GOPST–GCN algorithm is publicly available at https://github.com/bjmyX/GOPST-GCN-a-foot-end-path-planning-method- .", "Keywords": "", "DOI": "10.1142/S2301385025500414", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 26691, "JournalTitle": "Unmanned Systems", "ISSN": "2301-3850", "EISSN": "2301-3869", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Intelligent Control and Decision of Complex Systems, School of Automation, Beijing Institute of Technology, Beijing 100081, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Intelligent Control and Decision of Complex Systems, School of Automation, Beijing Institute of Technology, Beijing 100081, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Intelligent Control and Decision of Complex Systems, School of Automation, Beijing Institute of Technology, Beijing 100081, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Intelligent Control and Decision of Complex Systems, School of Automation, Beijing Institute of Technology, Beijing 100081, P. R. China"}], "References": []}, {"ArticleId": 114735488, "Title": "Formalizing Factorization on Euclidean Domains and Abstract Euclidean Algorithms", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.402.5", "PubYear": 2024, "Volume": "402", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universidade Federal de Goiás"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade de Brasília"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidade Federal de Catalão"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidade de Brasília"}], "References": [{"Title": "Exploring the Structure of an Algebra Text with Locales", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "6", "Page": "1093", "JournalTitle": "Journal of Automated Reasoning"}, {"Title": "Formalization of Ring Theory in PVS", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "65", "Issue": "8", "Page": "1231", "JournalTitle": "Journal of Automated Reasoning"}]}, {"ArticleId": 114735493, "Title": "Design and Analysis of Low Power Approximate Multiplier Using Novel Compressor", "Abstract": "<p>The multiplier is one of the most essential arithmetic blocks in computer architecture, as it has an impact on the system’s overall performance. Approximate computing help in improving multiplier performance with low power consumption at the expense of computing precision. In this paper, approximate novel compressors are proposed and further used for the implementation of the proposed approximate multiplier. In the multiplication, process compressors are used for the reduction of partial products with low consumption of power. In comparison to the exact multiplier, the proposed multiplier shows efficient results in terms of Look-up tables, area, memory utilization, and power consumption. The validation of the approximate multiplier is done in an error-tolerant application. In this paper, validation is done in an image processing application for image blending which results in 23.87 dB and 22.7 PSNR values for set 1 and set 2 respectively.</p>", "Keywords": "Approximate compressor; Approximate multiplier; Error-resilient application; System generator; Approximate computing", "DOI": "10.1007/s42979-024-02738-z", "PubYear": 2024, "Volume": "5", "Issue": "5", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, Chandigarh University, Gharuan, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, Jaypee University of Information Technology, Solan, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, Jaypee University of Information Technology, Solan, India"}], "References": []}, {"ArticleId": 114735495, "Title": "Airline reviews processing: Abstractive summarization and rating-based sentiment classification using deep transfer learning", "Abstract": "Opinion summarization and sentiment classification are key processes for understanding, analyzing, and leveraging information from customer opinions. The rapid and ceaseless increase in big data of reviews on e-commerce platforms, social media, or review portals becomes a stimulus for the automation of these processes. In recent years, deep transfer learning has opted to solve many challenging tasks in Natural Language Processing (NLP) relieving the hassles of exhaustive training and the requirement of extensive labelled datasets. In this work, we propose frameworks for Abstractive Summarization (ABS) and Sentiment Analysis (SA) of airline reviews using Pretrained Language Models (PLM). The abstractive summarization model goes through two finetuning stages, the first one, for domain adaptation and the second one, for final task learning. Several studies in the literature empirically demonstrate that review rating has a positive correlation with sentiment valence. For the sentiment classification framework, we used the rating value as a signal to determine the review sentiment, and the model is built on top of BERT (Bidirectional Encoder Representations from Transformers) architecture. We evaluated our models comprehensively with multiple metrics. Our results indicate competitive performance of the models in terms of most of the evaluation metrics.", "Keywords": "Airline reviews; Domain adaptation; Opinion summarization; Review rating; Sentiment classification; Two-stage finetuning", "DOI": "10.1016/j.jjimei.2024.100238", "PubYear": 2024, "Volume": "4", "Issue": "2", "JournalId": 83128, "JournalTitle": "International Journal of Information Management Data Insights", "ISSN": "2667-0968", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Doctor of Computer Science – BINUS Graduate Program, Bina Nusantara University, Jakarta, Indonesia;Corresponding author at: Department of Doctor of Computer Science – BINUS Graduate Program, Bina Nusantara University, Anggrek Campus, Jl. Kebon Jeruk Raya No. 27, Kebon Jeruk, Jakarta, Indonesia"}, {"AuthorId": 2, "Name": "Ford Lumban Gaol", "Affiliation": "Department of Doctor of Computer Science – BINUS Graduate Program, Bina Nusantara University, Jakarta, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Econometrics and Statistics - The University of Chicago, Booth School of Business"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, School of Computer Science, Bina Nusantara University, Jakarta 11480, Indonesia"}], "References": [{"Title": "Text Mining in Big Data Analytics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "Text mining with sentiment analysis on seafarers’ medical documents", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100005", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Applications of text mining in services management: A systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100008", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Abstractive Review Summarization based on Improved Attention Mechanism with Pointer Generator Network Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "1", "Page": "77", "JournalTitle": "Webology"}, {"Title": "Pre-trained models: Past, present and future", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "225", "JournalTitle": "AI Open"}, {"Title": "A sentiment analysis framework to classify instances of sarcastic sentiments within the aviation sector", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "2", "Page": "100180", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Methodical Systematic Review of Abstractive Summarization and Natural Language Processing Models for Biomedical Health Informatics: Approaches, Metrics and Challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "A data package for abstractive opinion summarization, title generation, and rating-based sentiment prediction for airline reviews", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "", "Page": "109535", "JournalTitle": "Data in Brief"}, {"Title": "AraXLNet: pre-trained language model for sentiment analysis of Arabic", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}]}, {"ArticleId": 114735511, "Title": "A Dataset on WhatsApp Groups Effectiveness in Inducting First Years to University", "Abstract": "<p>WhatsApp as the most popular instant messaging technology that has created opportunities for online cooperation and teamwork among students in the university context [1] and allows students to communicate even outside school hours [2]. Transitioning from high school to university can be a challenging experience for many students, particularly those who are entering a new academic atmosphere with greater autonomy and higher workload [3]. This data publication [4] presents a dataset capturing the perceptions of first-year university students on the utilization of WhatsApp as a means of continuous induction into university life. The dataset includes responses from participants on 7 background questions and 14 Likert scale questions, measuring agreeableness on the academic dimension of the academic dropout wheel [5]. The questionnaire aimed to investigate the effectiveness of WhatsApp groups established by academic support staff in facilitating the integration of first-year students into both formal academic structures and informal social systems within the campus. The data may offer valuable insights into the potential benefits of WhatsApp as an educational tool and its impact on fostering a positive academic environment, ultimately helping to reduce dropout rates in institutions of higher learning. Researchers, educators, and policymakers can utilize this dataset to gain a deeper understanding of the role that WhatsApp can play in enhancing student engagement and promoting successful integration into university life. By harnessing the information from this dataset, institutions can develop targeted strategies to provide effective support systems for first-year students, thereby promoting academic success and retention.</p><p>© 2024 The Author(s).</p>", "Keywords": "Academic support;Dropout;Student orientation", "DOI": "10.1016/j.dib.2024.110456", "PubYear": 2024, "Volume": "54", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>, <PERSON> drive, Mthatha, South Africa."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON>, <PERSON> drive, Mthatha, South Africa."}], "References": []}, {"ArticleId": 114735517, "Title": "<PERSON><PERSON><PERSON> Segmentasi Citra Daun Bawang Dengan Metode Adaptive Thesholding dan K-Means Clustering", "Abstract": "<p>Segmentasi citra yang akurat memiliki dampak signifikan pada hasil analisis citra secara keseluruhan. Penelitian ini bertujuan untuk membandingkan metode Adaptive Thresholding dan K-Means Clustering dalam segmentasi citra daun bawang merah dengan latar belakang yang berbeda. Dengan menggunakan analisis kuantitatif terhadap 25 citra daun bawang yang beragam, hasil penelitian menunjukkan bahwa Adaptive Thresholding menghasilkan segmentasi yang memuaskan dalam skala warna hitam dan putih, sementara K-Means Clustering dengan ekstraksi fitur juga memberikan hasil yang memuaskan. Analisis berbasis aplikasi web dalam 5 skenario mengonfirmasi keefektifan kedua metode tersebut. Adaptive Thresholding mencapai Jaccard index sebesar 0.92, Rand index sebesar 0.85, dan F1 score sebesar 0.95. Sedangkan K-Means Clustering memiliki Jaccard index sebesar 0.64, Rand index sebesar 0.69, dan F1 score sebesar 0.71 pada skenario latar belakang mediatanam. Me<PERSON>pun demikian, hasil segmentasi terbaik diperoleh dengan menggunakan Adaptive Thresholding pada latar belakang Putih Cahaya Terang, dengan Jaccard index sebesar 0.96, Rand index sebesar 0.91, dan F1 score sebesar 0.98. Penelitian ini memberikan rekomendasi untuk segmentasi optimal citra daun bawang merah dengan latar belakang yang berbeda, dengan menekankan keefektifan Adaptive Thresholding dalam mencapai tingkat akurasi tinggi melalui ekstraksi fitur bentuk dan tekstur. Pencahayaan yang memadai saat pengambilan citra merupakan faktor penting untuk mencapai hasil segmentasi yang optimal.</p>", "Keywords": "pengolahan citra;daun bawang merah;k-means clustering;adaptive thresholding;segmentasi", "DOI": "10.31328/jointecs.v8i3.4791", "PubYear": 2023, "Volume": "8", "Issue": "3", "JournalId": 54686, "JournalTitle": "JOINTECS (Journal of Information Technology and Computer Science)", "ISSN": "2541-3619", "EISSN": "2541-6448", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Nusantara PGRI Kediri"}, {"AuthorId": 2, "Name": "Firmansyah Mukti Wijaya", "Affiliation": "Universitas Nusantara PGRI Kediri"}], "References": []}, {"ArticleId": 114735569, "Title": "Proof Terms for Higher-Order Rewriting and Their Equivalence: Invited Talk at the 18th International Workshop on Logical and Semantic Frameworks, with Applications and 10th Workshop on Horn Clauses for Verification and Synthesis", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.402.1", "PubYear": 2024, "Volume": "402", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad de Buenos Aires, CONICET/UNQ/ICC, Buenos Aires, Argentina"}], "References": []}, {"ArticleId": 114735573, "Title": "<PERSON><PERSON><PERSON><PERSON>'s Epistemic Logic in Isabelle/HOL", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.402.4", "PubYear": 2024, "Volume": "402", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Iowa State University, Ames, Iowa"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Iowa State University, Ames, Iowa"}], "References": []}, {"ArticleId": 114735604, "Title": "Examining the moderating role of online celebrity trustworthiness and risk propensity in UTAUT2 framework: A mixed-method approach", "Abstract": "Utilizing the Unified Theory of Acceptance and Use of Technology 2 (UTAUT2) framework, this research delved into the behavioral intentions and actual usage of fantasy sports gaming apps. The moderating influence of celebrity trustworthiness and risk propensity is also explored. The proposed hypotheses were tested using responses from a substantial sample of 460 sports fantasy gaming app users. The research deployed a mixed-method approach, combining both covariance-based and partial-least square structural equation modeling tech- niques. The investigation findings revealed an influence on behavioral intentions and actual use. Specifically, performance expectancy, hedonic motivation, habit, and social influence emerged as significant determinants affecting users’ intentions and behaviors. The study indicates a significant moderating role of celebrity trustworthiness on the relationship be- tween performance expectancy, hedonic motivation, habit, and social influence concerning behavioral intentions. Risk propensity moderated the relationship between behavioral inten- tions and actual usage behavior. As a contribution to the expanding literature, the research extended the application of the UTAUT2 framework to the context of sports fantasy gaming apps. The research also provides valuable insights for researchers and professionals associ- ated with sports fantasy gaming apps and future research directions.", "Keywords": "UTAUT2; Sports fantasy games; Celebrity trustworthiness; Risk propensity; Behavioural intentions; Actual use", "DOI": "10.1016/j.jjimei.2024.100239", "PubYear": 2024, "Volume": "4", "Issue": "2", "JournalId": 83128, "JournalTitle": "International Journal of Information Management Data Insights", "ISSN": "2667-0968", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Business & Management, Christ University, Bangaluru, Karnataka, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "SoS in Management, Jiwaji University, Gwalior, Madhya Pradesh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Amity School of Communication, Amity University Haryana, India;Corresponding author"}], "References": [{"Title": "Using smartwatches for fitness and health monitoring: the UTAUT2 combined with threat appraisal as moderators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "3", "Page": "282", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Curiosity on Cutting-Edge Technology via Theory of Planned Behavior and Diffusion of Innovation Theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "1", "Page": "100152", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Analyzing effect of fear and uncertainty avoidance on use behavior of learning management system: Post COVID-19 era", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "2", "Page": "100197", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Adoption and impacts of generative artificial intelligence: Theoretical underpinnings and research agenda", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "4", "Issue": "1", "Page": "100232", "JournalTitle": "International Journal of Information Management Data Insights"}]}, {"ArticleId": 114735622, "Title": "The role of microtransactions in impulse buying and purchase intention in the video game market", "Abstract": "Despite having great potential, microtransactions are an understudied business model in the video game industry. The current study explores the buying process of microtransactions to highlight the most important factors affecting such behavior. Using pre-validated scales, a questionnaire was administered online to 301 participants who were asked to evaluate their impulse buying tendency (IBT) and purchase intention (PI) for microtransactions within gaming scenarios. The study found a positive relationship between drivers, such as performance, hedonic content and social factors, flow experience, and impulse buying. This tendency leads to a higher intention to purchase microtransactions. Businesses can utilize the paper’s findings to tailor their microtransaction content to these drivers, leading to higher profits.", "Keywords": "Microtransactions; Gaming; Impulse Buying; Mobile Commerce", "DOI": "10.1016/j.entcom.2024.100693", "PubYear": 2024, "Volume": "50", "Issue": "", "JournalId": 21223, "JournalTitle": "Entertainment Computing", "ISSN": "1875-9521", "EISSN": "1875-953X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Universidade NOVA de Lisboa, Portugal;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Business Research Unit (BRU-IUL), Instituto Universitário de Lisboa (ISCTE-IUL), Lisboa, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto Universitário de Lisboa (ISCTE-IUL), ISTAR, Lisboa, Portugal;Polytechnic Institute of Coimbra, Technology Management School of Oliveira do Hospital, Portugal;Centre Bio R&D Unit, Association BLC3 - Technology and Innovation Campus, Oliveira do Hospital, Portugal"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Instituto Universitário de Lisboa (ISCTE-IUL), Lisboa, Portugal"}], "References": [{"Title": "The impact of social media celebrities' posts and contextual interactions on impulse buying in social commerce", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "106178", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Shift from game-as-a-product to game-as-a-service research trends", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "2", "Page": "79", "JournalTitle": "Service Oriented Computing and Applications"}]}, {"ArticleId": 114735654, "Title": "An adaptive MAC-agnostic ranging scheme for underwater acoustic mobile networks", "Abstract": "In the last ten years several simulation studies on Autonomous Underwater Vehicle (AUV) swarm fleet formation have been performed, and some preliminary sea demonstrations of proof-of-concept prototypes were carried out. However, their actual realization is hindered by the challenges imposed by the underwater acoustic channel and the difficulties of keeping track of the vehicles’ positions due to the long latency required by traditional Two-Way Travel-Time (TWTT) ranging measurements, that require a specific signaling, hence limiting the throughput of the underwater network. Although One-Way Travel-Time (OWTT) halves the latency, it requires a high precision oscillator, such as an atomic clock or an oven controlled crystal oscillator (OCXO), to be installed in each modem processing unit: while atomic clocks are still very expensive, OCXO are very power demanding, making their application to underwater acoustic networks not always possible, especially in the case of low cost vehicle swarms. In this paper we present a network protocol stack able to perform ranging and localization within the communication task in underwater acoustic networks, limiting the network overhead. Specifically, new layers have been added to the preexisting DESERT Underwater protocol stack to perform the ranging tasks without compromising the correct operation of the communication network. A ranging layer is placed on top of the Medium Access Control (MAC) scheme, allowing the latter to be changed according to the network topology and requirements. This MAC-agnostic ranging layer is further optimized by adapting the amount of data transmitted according to the channel state, and the ranging entries inserted in the data packets according to the least recent information transmitted, hence minimizing the Age of Information. Simulation results obtained with the DESERT Underwater Framework show how this layer allows all AUVs in the swarm to know their distance from every other node in the network, thus limiting the probability of vehicle collisions and allowing better mission coordination.", "Keywords": "Underwater acoustic networks; Ranging in underwater networks; DESERT Underwater Framework; Network simulation; Age of information; Value of information", "DOI": "10.1016/j.comnet.2024.110430", "PubYear": 2024, "Volume": "247", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Padova, Department of Information Engineering (DEI), via Gradenigo 6/b, Padova, 35131, PD, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Padova, Department of Information Engineering (DEI), via Gradenigo 6/b, Padova, 35131, PD, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Padova, Department of Information Engineering (DEI), via Gradenigo 6/b, Padova, 35131, PD, Italy"}], "References": []}, {"ArticleId": 114735718, "Title": "Design and research of educational mode in context of teaching gamification", "Abstract": "With the development of science and technology, the education model is also constantly evolving. Teaching games is an increasingly concerned educational strategy, which can improve students’ interest and participation in learning through gamification. In order to further optimize the effect of teaching gamification, this paper uses a clustering algorithm to analyse students’ learning situation and determine the main factors affecting students’ participation, including learning motivation, learning tasks, social interaction and game experience. Based on the results of learning situation analysis, the naive <PERSON><PERSON> algorithm is used to analyse students learning behaviour. The model can predict students’ future learning needs and interests according to their learning history and behaviour, so as to recommend the most suitable gamified learning resources for them. The design and implementation of this model can help to improve the effect of teaching gamification and provide students with a more personalized learning experience. At the same time, the model also helps teachers to better understand students’ learning needs and interests, so as to better guide their learning.", "Keywords": "", "DOI": "10.1016/j.entcom.2024.100685", "PubYear": 2024, "Volume": "50", "Issue": "", "JournalId": 21223, "JournalTitle": "Entertainment Computing", "ISSN": "1875-9521", "EISSN": "1875-953X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Culture and Creativity, Beijing Normal University-Hongkong Bapitst University United International College, Zhuhai 519087, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Digtal Art and Design, Chengdu Neusoft University, Chengdu Sichuan 611844, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Culture and Creativity, Beijing Normal University-Hongkong Bapitst University United International College, Zhuhai 519087, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Advanced Manufacturing, Guangdong Songshan Polytechnic, Shaoguan Guangdong 512000, China"}], "References": [{"Title": "Improving Semantic Information Retrieval Using Multinomial Naive Bayes Classifier and Bayesian Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "272", "JournalTitle": "Information"}]}, {"ArticleId": 114735724, "Title": "A Comparison of Onshore Oil and Gas Transmission Pipeline Incident Statistics in Canada and the United States", "Abstract": "This study analyzes the mileage and incident data between 1995 and 2016 corresponding to the onshore oil and natural gas transmission pipelines regulated by the Canada Energy Regulator (CER) and Pipeline and Hazardous Materials Safety Administration (PHMSA) of the United States. The analysis indicates that the material/weld/equipment failure is the leading failure cause for both CER and PHMSA pipeline incidents. The annual average incident rates of the CER and PHMSA pipelines are in the order of 10<sup>−3</sup> per km except for the PHMSA gas pipelines, the annual incident rate of which is in the order of 10<sup>−4</sup> per km. The annual average rupture rates of the CER and PHMSA pipelines vary from 3.5 × 10<sup>−5</sup> to 4.5 × 10<sup>−5</sup> per km. The F-N curves for the PHMSA pipelines are developed based on the mileage and incident data to quantify the societal risks posed by the pipeline in general.", "Keywords": "CER; PHMSA; Onshore transmission pipelines; Incident; Rupture; F-N curve", "DOI": "10.1016/j.ijcip.2024.100679", "PubYear": 2024, "Volume": "45", "Issue": "", "JournalId": 6406, "JournalTitle": "International Journal of Critical Infrastructure Protection", "ISSN": "1874-5482", "EISSN": "2212-2087", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil & Environmental Engineering, The University of Western Ontario, London, ON, Canada N6A 5B9"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil & Environmental Engineering, The University of Western Ontario, London, ON, Canada N6A 5B9;Corresponding author"}], "References": []}, {"ArticleId": 114735953, "Title": "Bayesian Optimization for Digging Control of Wheel-Loader Using Robot Manipulator", "Abstract": "<p>Wheel loaders are construction machines that are mainly used for excavating and loading sedimented ground into dump trucks. The objects to be excavated range from large materials, such as blast rock and crushed stone, to small materials, such as gravel, slag, and coal ash. Therefore, the excavation operation of wheel loaders requires a high skill level to cope with various terrains and soil types. As worker numbers at quarry sites decline, developing highly automated technology to replace operators is crucial. In particular, the geometry of the ground to be excavated by the wheel loader changes with each excavation, so the control parameters must be adapted sequentially during automated excavation. In this study, we proposed an online learning method using Bayesian optimization to search for control parameters from multiple trials and modify them sequentially. In particular, we formulate a multi-objective optimization problem maximizing a weighted linear combination of the payload and workload as an objective function. To validate the proposed method, we constructed an environment in which repeated digging tests can be performed using a robot manipulator with a bucket attached. Through comparative tests between feed-forward control, in which the robot moves along a fixed trajectory independent of the digging reaction force, and off-line control, in which the robot modifies the digging trajectory in response to the digging reaction force, we compared the ability of these methods to cope with terrain volume that is different from that of the optimization trial.</p>", "Keywords": "Bayesian optimization;wheel-loader;excavation robot", "DOI": "10.20965/jrm.2024.p0273", "PubYear": 2024, "Volume": "36", "Issue": "2", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Osaka University, 2-1 Yamadaoka, Suita, Osaka 565-0871, Japan;Manufacturing Engineering Development Center, Komatsu Ltd., 3-1-1 Ueno, Hirakata, Osaka 573-1011, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Osaka University, 2-1 <PERSON>madaoka, Suita, Osaka 565-0871, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Osaka University, 2-1 <PERSON>madaoka, Suita, Osaka 565-0871, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Osaka University, 2-1 <PERSON>madaoka, Suita, Osaka 565-0871, Japan"}], "References": [{"Title": "Bayesian optimization with safety constraints: safe and automatic parameter tuning in robotics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "112", "Issue": "10", "Page": "3713", "JournalTitle": "Machine Learning"}, {"Title": "An Optimal Design Methodology for the Trajectory of Hydraulic Excavators Based on Genetic Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "1248", "JournalTitle": "Journal of Robotics and Mechatronics"}]}, {"ArticleId": 114736040, "Title": "Designing a sustainable plastic bottle reverse logistics network: A data-driven optimization approach", "Abstract": "Management of recovery options for plastic beverage containers involves some challenges. Most materials of used containers are recycled and are used to produce new products. The locations of collection facilities are the strategic decisions, affecting the amount collected and the total cost of a Reverse Logistics Network (RLN). In this study, a multi-objective (MO) optimization model is introduced to configure a plastic beverage container RLN, considering economic, environmental, and social objectives. This study also implements a scenario-based possibilistic approach to handle the uncertainty of the parameters. Furthermore, a data-driven fuzzy optimization framework is developed to consider the overlapping and multi-clustered characteristics of historical data samples. The application of the proposed method is demonstrated by considering a network in Vancouver, Canada. The numerical results reveal that the optimal configuration of the RLN resulting from the proposed MO model exhibits significant sensitivity to fluctuations in costs, demands, and the prioritization of the objective functions. Additionally, the proposed data-driven framework can incorporate decision makers' preferences when tuning the conservatism degree of uncertain parameters and the preference level of different objectives of the MO model. Moreover, the developed data-driven algorithm can reduce over-conservatism by 14% and guarantee the feasibility of optimal solutions compared to other data-driven strategies.", "Keywords": "Plastic bottles; Data-driven optimization; Uncertainty; Multi-objective programming; Reverse logistics", "DOI": "10.1016/j.eswa.2024.123918", "PubYear": 2024, "Volume": "251", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>pour Tosarkani", "Affiliation": "School of Engineering, University of British Columbia, Kelowna, BC, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, Toronto Metropolitan University, ON, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, University of British Columbia, Kelowna, BC, Canada"}], "References": [{"Title": "Data-driven robust optimization for wastewater sludge-to-biodiesel supply chain design", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "105944", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Effect of carbon tax on reverse logistics network design", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106184", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Integrated optimization model for hydrogen supply chain network design and hydrogen fueling station planning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "106683", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "A two-stage stochastic programming model for multi-period reverse logistics network design with lot-sizing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "106397", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A review of closed-loop supply chain models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "4", "Page": "279", "JournalTitle": "Journal of Data, Information and Management"}, {"Title": "Preference degree of triangular fuzzy numbers and its application to multi-attribute group decision making", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "114982", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An interval valued neutrosophic decision-making structure for sustainable supplier selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115354", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Designing a sustainable–resilient disaster waste management system under hybrid uncertainty: A case study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>-Moghad<PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104459", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A proposed method for third-party reverse logistics partner selection and order allocation in the cellphone industry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "162", "Issue": "", "Page": "107719", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Modelling uncertainty in sustainable-green integrated reverse logistics network using metaheuristics optimization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "107828", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "VeRoViz: A Vehicle Routing Visualization Toolkit", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "4", "Page": "1842", "JournalTitle": "INFORMS Journal on Computing"}, {"Title": "A multi-objective optimization framework for a sustainable closed-loop supply chain network in the olive industry: Hybrid meta-heuristic algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>-Moghad<PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117566", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-objective closed-loop supply chain network design: A novel robust stochastic, possibilistic, and flexible approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "206", "Issue": "", "Page": "117807", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Recovery center selection for end-of-life automotive lithium-ion batteries using an integrated fuzzy WASPAS approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "206", "Issue": "", "Page": "117827", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Data-driven robust optimization using deep neural networks", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "151", "Issue": "", "Page": "106087", "JournalTitle": "Computers & Operations Research"}, {"Title": "A customized multi-neighborhood search algorithm using the tabu list for a sustainable closed-loop supply chain network under uncertainty", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "110495", "JournalTitle": "Applied Soft Computing"}, {"Title": "Designing a dual-channel closed loop supply chain network using advertising rate and price-dependent demand: Case study in tea industry", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "233", "Issue": "", "Page": "120936", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Ranking fuzzy numbers using additive priority degrees", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "234", "Issue": "", "Page": "121019", "JournalTitle": "Expert Systems with Applications"}, {"Title": "On optimality for fuzzy optimization problems with granular differentiable fuzzy objective functions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "240", "Issue": "", "Page": "121891", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A multi-product and multi-period supply chain network design problem with price-sensitive demand and incremental quantity discount", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "122005", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Optimal integration of electricity supply chain and sustainable closed-loop supply chain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "241", "Issue": "", "Page": "122464", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Evaluation and ranking of fuzzy sets under equivalence fuzzy relations as α − certainty and β − possibility", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "248", "Issue": "", "Page": "123175", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114736203, "Title": "Novel key-integration to safeguard counting-based secret-sharing from possibilities of cyberattack breaches", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-19027-9", "PubYear": 2025, "Volume": "84", "Issue": "9", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Faiza Al-Shaarani", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Refining Arabic text stego-techniques for shares memorization of counting-based secret sharing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "9", "Page": "1108", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Hiding shares by multimedia image steganography for optimized counting-based secret sharing", "Authors": "<PERSON><PERSON>; Maimo<PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7951", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Smart expansion of target key for more handlers to access multimedia counting-based secret sharing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "17373", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Remodeling randomness prioritization to boost-up security of RGB image encryption", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "18", "Page": "28521", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Securing matrix counting-based secret-sharing involving crypto steganography", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "6909", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Adopting counting-based secret-sharing for e-Video Watermarking allowing Fractional Invalidation", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "7", "Page": "9527", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Boosting image watermarking authenticity spreading secrecy from counting‐based secret‐sharing", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "2", "Page": "440", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "Improving grayscale steganography to protect personal information disclosure within hotel services", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "21", "Page": "30663", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Analysis of community question‐answering issues via machine learning and deep learning: State‐of‐the‐art review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "1", "Page": "95", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "Redefining food safety traceability system through blockchain: findings, challenges and open issues", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "14", "Page": "21243", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Integrity verification of Holy Quran verses recitation via incomplete watermarking authentication", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "4", "Page": "997", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Systematic literature review and analysis for Arabic text steganography method practically", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "23", "Issue": "4", "Page": "177", "JournalTitle": "Egyptian Informatics Journal"}, {"Title": "Is blind image steganalysis practical using feature-based classification?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "2", "Page": "4579", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Towards improving the performance of blind image steganalyzer using third-order SPAM features and ensemble classifier", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "76", "Issue": "", "Page": "103541", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Integrating machine learning and features extraction for practical reliable color images steganalysis classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "19", "Page": "13877", "JournalTitle": "Soft Computing"}, {"Title": "Regulating Kashida Arabic steganography to improve security and capacity performance", "Authors": "<PERSON><PERSON>", "PubYear": 2025, "Volume": "84", "Issue": "9", "Page": "5457", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 114736468, "Title": "From Seek-and-Destroy to Split-and-Destroy: Connection Partitioning as an Effective Tool against Low-Rate DoS Attacks", "Abstract": "<p>Low-rate Denial of Service (LDoS) attacks are today considered one of the biggest threats against modern data centers and industrial infrastructures. Unlike traditional Distributed Denial of Service (DDoS) attacks that are mainly volumetric, LDoS attacks exhibit a very small network footprint, and therefore can easily elude standard detection and defense mechanisms. This work introduces a defense strategy that may prove particularly effective against attacks that are based on long-lived connections, an inherent trait of LDoS attacks. Our approach is based on iteratively partitioning the active connections of a victim server across a number of replica servers, and then re-evaluating the health status of each replica instance. At its core, this approach relies on live migration and containerization technologies. The main advantage of the proposed approach is that it can discover and isolate malicious connections with virtually no information about the type and characteristics of the performed attack. Additionally, while the defense takes place, there is little to no indication of the fact to the attacker. We assess various rudimentary schemes to quantify the scalability of our approach. The results from the simulations indicate that it is possible to save the vast majority of the benign connections (80%) in less than 5 min.</p>", "Keywords": "", "DOI": "10.3390/fi16040137", "PubYear": 2024, "Volume": "16", "Issue": "4", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Security and Communication Technology, Norwegian University of Science and Technology, 2802 Gjøvik, Norway"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Idaho, Idaho Falls, ID 83402, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Idaho, Idaho Falls, ID 83402, USA"}], "References": [{"Title": "Model-based evaluation of combinations of Shuffle and Diversity MTD techniques on the cloud", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "507", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "MF-Adaboost: LDoS attack detection based on multi-features and improved Adaboost", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "347", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "The detection method of low-rate DoS attack based on multi-feature fusion", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "4", "Page": "504", "JournalTitle": "Digital Communications and Networks"}]}, {"ArticleId": 114736671, "Title": "Novel hybrid optimization based adaptive deep convolution neural network approach for human activity recognition system", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-19095-x", "PubYear": 2025, "Volume": "84", "Issue": "9", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Hybrid deep learning approaches for smartphone sensor-based human activity recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "28-29", "Page": "35585", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "DanHAR: Dual Attention Network for multimodal human activity recognition using wearable sensors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107728", "JournalTitle": "Applied Soft Computing"}, {"Title": "A federated learning system with enhanced feature extraction for human activity recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "229", "Issue": "", "Page": "107338", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-sensor information fusion based on machine learning for real applications in human activity recognition: State-of-the-art and research challenges", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "241", "JournalTitle": "Information Fusion"}, {"Title": "Inception inspired CNN-GRU hybrid network for human activity recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "4", "Page": "5369", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Human activity recognition using wearable sensors by heterogeneous convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116764", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Human Activity Recognition on Microcontrollers with Quantized and Adaptive Deep Neural Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "Human activity recognition using marine predators algorithm with deep learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "340", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 114736731, "Title": "Cognitive Consistency in Uncertain and Preference Involved Weights Determination", "Abstract": "<p>In uncertain information environment, bi-polar preferences can be elicited from experts and processed to be exerted over some weights determination for multiple-agents evaluation. Recently, some weighting methodologies and models in uncertain and preference involved environment with multiple opinions from multiple experts are proposed in some literature. However, in that existing method, when collecting different types of preferences from a single expert, sometimes some subtle cognitive inconsistency may occur. To eliminate such inconsistency, this work elaborately analyzes the possible reasons and proposes some amendment together with a new distinguishable set of formulations for modeling. In addition, we further consider two situations of the weighting models for the problem, with one only considering the situation of single expert with no risk of cognitive inconsistency and the other considering the case of multiple experts wherein some inconsistency might occur. Numerical example and comparison are also presented accordingly.</p>", "Keywords": "Aggregation operators; basic uncertain information; bi-polar preferences; decision making; ordered weighted averaging operators; weights determination", "DOI": "10.1142/S0218488524500107", "PubYear": 2024, "Volume": "32", "Issue": "2", "JournalId": 15072, "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems", "ISSN": "0218-4885", "EISSN": "1793-6411", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automobile and Traffic Engineering, Hubei University of Arts and Sciences, Xiangyang, 441053, P. R. China;Business School, Nanjing Normal University, Nanjing, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Machine Intelligence Institute, Iona College New Rochelle, NY 10801, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Civil Engineering, Slovak University of Technology, Radlinského 11, Sk-810 05 Bratislava, Slovakia;Institute for Research and Applications of Fuzzy Modeling, University of Ostrava, CE IT4Innovations, 30. dubna 22, 701 03 Ostrava, Czech Republic"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Southwest University, Chongqing, 400715, China"}, {"AuthorId": 5, "Name": "Chiranjibe Jana", "Affiliation": "Saveetha School of Engineering, Saveetha Institute of Medical and Technical Sciences (SIMATS), Chennai 602105, Tamil Nadu, India"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Automobile and Traffic Engineering, Hubei University of Arts and Sciences, Xiangyang, 441053, P. R. China;Hubei Key Laboratory of Power System Design and Test for Electrical Vehicle, Hubei University of Arts and Science, Xiangyang, 441053, P. R. China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, Computer Science and Mathematics, Public University of Navarre Campus Arrosadia sn, 31006 Pamplona, Spain"}], "References": [{"Title": "Basic uncertain information soft set and its application to multi-criteria group decision making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "103871", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Interval-valued seminormed fuzzy operators based on admissible orders", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "96", "JournalTitle": "Information Sciences"}, {"Title": "Sustainable building material selection: An integrated multi-criteria large group decision making framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107903", "JournalTitle": "Applied Soft Computing"}, {"Title": "Families of Value-Dependent Preference Aggregation Operators with Their Structures and Properties", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "2", "Page": "209", "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems"}, {"Title": "Ordered weighted averaging operators for basic uncertain information granules", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "645", "Issue": "", "Page": "119357", "JournalTitle": "Information Sciences"}, {"Title": "A Modified TOPSIS Approach with Three-Way Decision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "5", "Page": "795", "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems"}, {"Title": "A Weight Determination Model in Uncertain and Complex Bi-Polar Preference Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "5", "Page": "713", "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems"}]}, {"ArticleId": 114736737, "Title": "Future rescue management: Design specifications for a 5G-enabled emergency response information system", "Abstract": "<p>Today, access to real-time information from emergency scenes is still limited for emergency medical services, fire departments, and their professionals, also called first responders. Emergency Response Information Systems (ERISs) have recently been discussed in the literature as a potential solution to this problem. Using the Design Science Research (DSR) paradigm, we present a novel 5G-enabled ERIS (5G-ERIS) design that leverages 5G mobile network technologies to offer diverse real-time information. We provide a user-centered examination of design specifications for a 5G-ERIS based on a smart city digital twin. Based on literature and qualitative expert interviews with several first responders in Germany, we derive how emergency medical services and fire departments can improve their decision-making with this 5G-ERIS. Based on existing 5G application architectures, we structure our identified design specifications into four system layers. Our findings provide an essential knowledge base for the successful development, deployment, and long-term use of 5G-ERISs. We stimulate a broader discussion on the design objectives and specifications of 5G-ERISs in theory and practice.</p>", "Keywords": "", "DOI": "10.3233/IDT-230476", "PubYear": 2024, "Volume": "18", "Issue": "2", "JournalId": 36670, "JournalTitle": "Intelligent Decision Technologies", "ISSN": "1872-4981", "EISSN": "1875-8843", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Information Systems Institute, Leibniz University Hannover, Hannover, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Information Systems Institute, Leibniz University Hannover, Hannover, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "OFFIS e. V. – Institute for Information Technology, Oldenburg, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ATS Elektronik GmbH, Albert-Einstein-Straße 3, Wunstorf, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Information Systems Institute, Leibniz University Hannover, Hannover, Germany"}], "References": [{"Title": "Design of search and rescue system using autonomous Multi-UAVs", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "4", "Page": "553", "JournalTitle": "Intelligent Decision Technologies"}, {"Title": "Smart healthcare in smart cities: wireless patient monitoring system using IoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "11", "Page": "12230", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Secure and lightweight privacy preserving Internet of things integration for remote patient monitoring", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "6895", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Architectures of analytics intelligent decision technologies systems (IDTS) for the COVID-19 pandemic", "Authors": "<PERSON>; <PERSON>; <PERSON>-<PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "1", "Page": "263", "JournalTitle": "Intelligent Decision Technologies"}, {"Title": "Virtual and Augmented Reality in the Disaster Management Technology: A Literature Review of the Past 11 years", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "30", "JournalTitle": "Frontiers in Virtual Reality"}]}, {"ArticleId": 114736818, "Title": "社会実装ロボット教育とシステム開発プロセス教育", "Abstract": "", "Keywords": "System Integration Education;Micro Intelligent Robot System;Social Implementation Education", "DOI": "10.7210/jrsj.42.207", "PubYear": 2024, "Volume": "42", "Issue": "3", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Control System Engineering, National Institute of Technology(KOSEN), Numazu College"}], "References": []}, {"ArticleId": 114736831, "Title": "Weakly privileged learning with knowledge extraction", "Abstract": "Learning using privileged information (LUPI) has shown promise in improving supervised learning by embedding additional knowledge. However, its reliance on the assumption of readily available privileged information may not hold true in practical scenarios due to limitations in access or confidentiality. To address these challenges, this paper presents a novel weakly privileged learning (WPL) framework, integrating knowledge extraction methods within the LUPI context. An effective strategy is proposed to implement the WPL framework, where knowledge extraction techniques generate a weight matrix as weak privileged information. Extensive experiments employing various existing knowledge extraction techniques demonstrate that the proposed WPL outperforms traditional supervised learning and approaches the performance of standard privileged learning where privileged information is given in advance. This research establishes WPL as a promising learning paradigm, addressing limitations in privileged information availability and advancing the field of machine learning in practical settings.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.110517", "PubYear": 2024, "Volume": "153", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Beijing University of Posts and Telecommunications, Beijing 100876, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Beijing Technology and Business University, Beijing 100048, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "DongFang Hospital of Beijing University of Chinese Medicine, Beijing 100078, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Chinese Academy of Sciences, Beijing 100190, China;Research Center on Fictitious Economy and Data Science, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Big Data Mining and Knowledge Management, Chinese Academy of Sciences, Beijing 100190, China;MOE Social Science Laboratory of Digital Economic Forecasts and Policy Simulation at UCAS, Beijing 100190, China;Correspondence to: No.80 of Zhongguancun East Road, Haidian District, Beijing, 100190, China.; Corresponding author"}], "References": [{"Title": "Deep and interpretable regression models for ordinal outcomes", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108263", "JournalTitle": "Pattern Recognition"}, {"Title": "Incomplete-view oriented kernel learning method with generalization error bound", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "581", "Issue": "", "Page": "951", "JournalTitle": "Information Sciences"}, {"Title": "Progressive privileged knowledge distillation for online action detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "108741", "JournalTitle": "Pattern Recognition"}, {"Title": "Privileged multi-task learning for attribute-aware aesthetic assessment", "Authors": "<PERSON><PERSON> Shu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "132", "Issue": "", "Page": "108921", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-step ahead tourism demand forecasting: The perspective of the learning using privileged information paradigm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "210", "Issue": "", "Page": "118502", "JournalTitle": "Expert Systems with Applications"}, {"Title": "ML-DSVM+: A meta-learning based deep SVM+ for computer-aided diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109076", "JournalTitle": "Pattern Recognition"}, {"Title": "Knowledge-aware document summarization: A survey of knowledge, embedding methods and architectures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "257", "Issue": "", "Page": "109882", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Privileged information learning with weak labels", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110298", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 114736836, "Title": "A Portal to Browse Top Universities According to Your Preferences : Result", "Abstract": "Develop a platform that empowers students, educators, policymakers, and the public by offering insights into the educational landscape. This website will be user-friendly and informative, providing a comprehensive overview of universities based on statistical metrics, emphasizing data representation. Users can explore academic achievements through metrics such as pass rates, average GPA, and research output percentages. Additionally, the platform allows for easy comparisons between universities to help users assess each institution's strengths and weaknesses.", "Keywords": "University;Education;Platform;Portal;Information", "DOI": "10.32628/CSEIT2410284", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Student, Department of Computer Science and Engineering, Sipna COET, Amravati, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Student, Department of Computer Science and Engineering, Sipna COET, Amravati, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Assistant Professor, Department of Computer Science and Engineering, Sipna COET, Amravati, India"}], "References": []}, {"ArticleId": 114736843, "Title": "Codes for exact support recovery of sparse vectors from inaccurate linear measurements and their decoding", "Abstract": "<p>We construct codes that allow to exactly recover the support of an unknown sparse vector with almost equal absolute values of all nonzero coordinates given results of linear measurements in the presence of noise with ℓp-norm bounded from above. We propose a decoding algorithm with asymptotically minimum complexity.</p>", "Keywords": "compressed sensing;sparse vector support;Group testing;false coin problem;signature codes for a noisy multiple-access adder channel;multimedia digital fingerprinting codes;сжатие измерений;носитель разреженного вектора;Групповое тестирование;поиск фальшивых монет;сигнатурные коды для суммирующего канала с множественным доступом и шумом;мультимедийные коды цифровых отпечатков пальцев", "DOI": "10.31857/S0555292323010023", "PubYear": 2023, "Volume": "59", "Issue": "1", "JournalId": 61888, "JournalTitle": "Проблемы передачи информации", "ISSN": "0555-2923", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitat Politicnica de Catalunya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Skolkovo Institute of Science and Technology (Skoltech)"}, {"AuthorId": 3, "Name": "S. A Kruglik", "Affiliation": "Nanyang Technological University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Tsukuba"}], "References": [{"Title": "Существование и конструкции мультимедийных кодов, способных находить полную коалицию при атаке усреднения и шуме", "Authors": "Е.Е<PERSON>г<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; Г.<PERSON><PERSON>ий", "PubYear": 2020, "Volume": "56", "Issue": "4", "Page": "97", "JournalTitle": "Проблемы передачи информации"}, {"Title": "Разделимые коды для защиты мультимедиа от нелегального копирования коалициями", "Authors": "Е.Е<PERSON> Ег<PERSON><PERSON><PERSON>; Г.<PERSON><PERSON>кий", "PubYear": 2021, "Volume": "57", "Issue": "2", "Page": "90", "JournalTitle": "Проблемы передачи информации"}]}, {"ArticleId": 114736859, "Title": "Overparameterized maximum likelihood tests for detection of sparse vectors", "Abstract": "<p>We address the problem of detecting a sparse high-dimensional vector against white Gaussian noise. An unknown vector is assumed to have only p nonzero components, whose positions and sizes are unknown, the number p being on one hand large but on the other hand small as compared to the dimension. The maximum likelihood (ML) test in this problem has a simple form and, certainly, depends of p. We study statistical properties of overparametrized ML tests, i.e., those constructed based on the assumption that the number of nonzero components of the vector is q (q > p) in a situation where the vector actually has only p nonzero components. We show that in some cases overparametrized tests can be better than standard ML tests.</p>", "Keywords": "Sparse vector;white Gaussian noise;maximum likelihood test;разреженный вектор;белый гауссовский шум;тест максимального правдоподобия", "DOI": "10.31857/S0555292323010047", "PubYear": 2023, "Volume": "59", "Issue": "1", "JournalId": 61888, "JournalTitle": "Проблемы передачи информации", "ISSN": "0555-2923", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kharkevich Institute for Information Transmission Problems of the Russian Academy of Sciences"}], "References": [{"Title": "Two Models of Double Descent for Weak Features", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "4", "Page": "1167", "JournalTitle": "SIAM Journal on Mathematics of Data Science"}, {"Title": "Understanding deep learning (still) requires rethinking generalization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "3", "Page": "107", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 114736866, "Title": "Geometric interpretation of the entropy of so c systems", "Abstract": "<p>We consider a geometric approach to the notion of metric entropy. We justify the possibility of this approach for the class of Borel invariant ergodic probability measures on so c systems, which is the rst result of such generality for non-Markovian systems.</p>", "Keywords": "metric entropy;local boundary deformation rate;symbolic system;Synchronized system;so c system;invariant ergodic measure;derived space;Markovian boundary;метрическая энтропия;локальная скорость деформации границ;символическая система;синхронизованная система;софическая система;инвариантная эргодическая мера;производное пространство;марковская граница", "DOI": "10.31857/S0555292323020043", "PubYear": 2023, "Volume": "59", "Issue": "2", "JournalId": 61888, "JournalTitle": "Проблемы передачи информации", "ISSN": "0555-2923", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Lomonosov Moscow State University"}], "References": [{"Title": "Геометрическая интерпретация энтропии: новые результаты", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Г.Д.", "PubYear": 2021, "Volume": "57", "Issue": "3", "Page": "90", "JournalTitle": "Проблемы передачи информации"}]}, {"ArticleId": 114736956, "Title": "E-commerce Website Performance Evaluation: Technology, Strategy and Metrics", "Abstract": "<p>Customers can shop conveniently online with the help of the Cooperative Store Management System, an e-commerce web application, without having to go to the store in person. This technology seeks to lessen the effort of sales staff and eliminate the possibility of manual errors by automating data entry operations. Customers can save costs dramatically and gain valuable time by using this method. In addition, clients may take advantage of increased convenience and better service quality because to the fact that these services are available from the comfort of their homes, which promotes customer retention and draws in new customers. Work has unraveled the intricacies of web development process, shedding light on the challenges faced and the inventive solutions devised. From the complexities of user interface (UI) and user experience (UX) design to the thoughtful analysis of essential qualities for our online store, every facet has been meticulously examined to ensure that the platform prioritizes user involvement, seamless navigation, and efficient communication channels. A detailed study is done to analyse the drawbacks of using marketplaces instead of self-owned sites for small scale business. This work shed light on the processes that went into the creation of a website. Additionally, it provides a thorough analysis of the architecture, Technology, and system's functionality. Facebook News Feed that uses React for dynamic contents, react.js allows for efficient updates to the news feed in real-time without having to reload the entire page. while in LinkedIn Mobile its mobile app uses AngularJS for front-end development. Very limited amount of research work done till date explores the issue of web strategy in web site evaluation, and none includes web strategy in their evaluation frameworks. Citing above problem, a strategic framework was adopted to ensure consistency and provide an efficient solution. AngularJS provides a structured framework for building dynamic and responsive user interfaces. For the use case - MERN Stack website, the Largest Contentful Paint comes as 3.8 sec, First Input Delay comes as 20 milli-sec and cumulative layout shifts come with no delay. These scores provide suggestions for improving various aspects of performance, such as optimizing images, leveraging browser caching, and minimizing render-blocking resources.</p>", "Keywords": "", "DOI": "10.9734/ajrcos/2024/v17i6461", "PubYear": 2024, "Volume": "17", "Issue": "6", "JournalId": 63540, "JournalTitle": "Asian Journal of Research in Computer Science", "ISSN": "", "EISSN": "2581-8260", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": ". <PERSON><PERSON>ya", "Affiliation": ""}], "References": []}, {"ArticleId": 114736959, "Title": "An Architectural View of VANETs Cloud: its Models, Services, Applications and Challenges", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJWGS.2024.10063636", "PubYear": 2024, "Volume": "20", "Issue": "3", "JournalId": 17784, "JournalTitle": "International Journal of Web and Grid Services", "ISSN": "1741-1106", "EISSN": "1741-1114", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114736971, "Title": "Testing the satisfiability of algebraic formulas over the field of two elements", "Abstract": "<p>We construct a probabilistic polynomial algorithm for testing the satisfiability of algebraic formulas of depth 3 over the two-element field, with addition as the top operation in the formulas. An algorithm with the same characteristics exists for the problem of testing whether a polynomial given by formulas of this type is identically zero (PIT problem). However, these problems and algorithms for their solution are essentially different. The probabilistic algorithm for the PIT problem is based on the <PERSON><PERSON><PERSON> lemma, whereas the satisfiability testing algorithm proposed in this paper is based on the Valiant-<PERSON> lemma.</p>", "Keywords": "satis ability of Boolean formulas;probabilistic algorithm;Algebraic formulas;Выполнимость булевых формул;вероятностный алгоритм;алгебраические формулы", "DOI": "10.31857/S0555292323010059", "PubYear": 2023, "Volume": "59", "Issue": "1", "JournalId": 61888, "JournalTitle": "Проблемы передачи информации", "ISSN": "0555-2923", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Federal Research Center “Computer Science and Control” of the Russian Academy of Sciences;National Research University-Higher School of Economics;Moscow Institute of Physics and Technology (State University)"}], "References": [{"Title": "CNF Satisfiability in a Subspace and Related Problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "84", "Issue": "11", "Page": "3276", "JournalTitle": "Algorithmica"}]}, {"ArticleId": 114736979, "Title": "Existence of sequences satisfying bilinear type recurrence relations", "Abstract": "<p>We study sequences $\\left\\{A_n\\right\\}_{n=-\\infty}^{+\\infty}$ of elements of an arbitrary field $\\mathbb{F}$ that satisfy decompositions of the form $A_{m+n} A_{m-n}=a_1(m) b_1(n)+a_2(m) b_2(n)$, $A_{m+n+1} A_{m-n}=\\tilde a_1(m) \\tilde b_1(n)+\\tilde a_2(m) \\tilde b_2(n)$, where $a_1,a_2,b_1,b_2\\colon \\mathbb{Z}\\to\\mathbb{F}$. We prove some results concerning the existence and uniqueness of such sequences. The results are used to construct analogs of the <PERSON><PERSON><PERSON><PERSON> and ElG<PERSON> cryptographic algorithms. The discrete logarithm problem is considered in the group $(S,+)$, where the set $S$ consists of quadruples $S(n)=(A_{n-1},A_n, A_{n+1}, A_{n+2})$, $n\\in\\mathbb{Z}$, and $S(n)+S(m)=S(n+m)$.</p>", "Keywords": "nonlinear recurrence sequences;Somos sequences;public-key cryptography;нелинейные рекуррентные последовательности;последовательности Сомоса;криптография с открытым ключом", "DOI": "10.31857/S0555292323020079", "PubYear": 2023, "Volume": "59", "Issue": "2", "JournalId": 61888, "JournalTitle": "Проблемы передачи информации", "ISSN": "0555-2923", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Higher School of Economics-National Research University;Khabarovsk Branch of the Institute of Applied Mathematics of the Far East Branch of the Russian Academy of Sciences"}], "References": []}, {"ArticleId": 114737093, "Title": "Deep reinforcement learning framework for end-to-end semiconductor process control", "Abstract": "<p>This work focuses on bringing state-of-the-art artificial intelligence and deep reinforcement learning to the manufacturing of semiconductor devices. The main goal of this research is to lay down the foundation of an end-to-end system for manufacturing control. In contrast to most of the research published in this field, the goal of this algorithm is not to conceptualize the different processes as separate entities but rather to try to control the chain of semiconductor processes as a whole. Therefore, we treat the process chain as an ecosystem where we control all processes using agents that learn to cooperate to produce the best wafers and semiconductor devices. In order to achieve that, we developed an architecture called an ensemble of independent preprocessors (EIP). We applied it with the deep reinforcement learning technique called soft actor-critic (SAC) in various environments, including environments comporting simulated semiconductor devices. The introduced technique strongly outperformed standard statistical methods and allowed us to find new sets of optimal manufacturing parameters different from the true ones. We hope that this work and the presentation of our framework will encourage practitioners to experiment with such systems and make further advancements in the field of decision-making for semiconductor manufacturing.</p>", "Keywords": "Artificial intelligence; Semiconductor manufacturing; Reinforcement learning; End-to-end system", "DOI": "10.1007/s00521-024-09710-1", "PubYear": 2024, "Volume": "36", "Issue": "20", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Integrated Circuits, Tsinghua University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Integrated Circuits, Tsinghua University, Beijing, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Integrated Circuits, Tsinghua University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Integrated Circuits, Tsinghua University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Integrated Circuits, Tsinghua University, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Integrated Circuits, Tsinghua University, Beijing, China"}], "References": [{"Title": "Recursive Bayesian state estimation method for run‐to‐run control in high‐mixed semiconductor manufacturing process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "3", "Page": "1177", "JournalTitle": "Asian Journal of Control"}, {"Title": "Quality monitoring in multistage manufacturing systems by using machine learning techniques", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "8", "Page": "2471", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 114737135, "Title": "Fixed-time convergence ZNN model for solving rectangular dynamic full-rank matrices inversion", "Abstract": "The Moore–<PERSON>rose inverse of dynamic matrices has found widespread application and has garnered significant attention. The zeroing neural network (ZNN) has proven to be an effective solution for computing the <PERSON> inverse in dynamic matrices. This paper proposes a novel unified fixed-time ZNN (UFTZNN) model designed to achieve fixed-time convergence and solve both left and right inverse problems using a single model. Theoretical analysis of the convergence and robustness of the UFTZNN model is rigorously presented. Numerical simulations comparing the UFTZNN with existing ZNN models confirm its superiority in addressing left and right inverse problems, convergence time, and robustness. The UFTZNN model is applied to the inverse kinematic tracking problem of a six-degree-of-freedom manipulator-based photoelectric tracking system to demonstrate its potential applications and effectiveness.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.123992", "PubYear": 2024, "Volume": "251", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Key Laboratory of Optical Field Manipulation Science and Technology, Chinese Academy of Sciences, Chengdu, 610209, China;Key Laboratory of Optical Engineering, Chinese Academy of Sciences, Chengdu, 610209, China;Institute of Optics and Electronics, Chinese Academy of Sciences, Chengdu, 610209, China;University of Chinese Academy of Science, Beijing, 100049, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang Lab, Hangzhou, 311121, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology and Electrical Engineering, University of Oulu, Oulu, 90570, Finland;VTT-Technical Research Centre of Finland, Oulu, 90590, Finland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory of Optical Field Manipulation Science and Technology, Chinese Academy of Sciences, Chengdu, 610209, China;Key Laboratory of Optical Engineering, Chinese Academy of Sciences, Chengdu, 610209, China;Institute of Optics and Electronics, Chinese Academy of Sciences, Chengdu, 610209, China;University of Chinese Academy of Science, Beijing, 100049, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National Key Laboratory of Optical Field Manipulation Science and Technology, Chinese Academy of Sciences, Chengdu, 610209, China;Key Laboratory of Optical Engineering, Chinese Academy of Sciences, Chengdu, 610209, China;Institute of Optics and Electronics, Chinese Academy of Sciences, Chengdu, 610209, China;University of Chinese Academy of Science, Beijing, 100049, China"}], "References": [{"Title": "Noise-tolerant neural algorithm for online solving time-varying full-rank matrix <PERSON>Pen<PERSON> inverse problems: A control-theoretic approach", "Authors": "<PERSON>hong<PERSON> Sun; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "413", "Issue": "", "Page": "158", "JournalTitle": "Neurocomputing"}, {"Title": "Improved recurrent neural networks for solving Moore-Penrose inverse of real-time full-rank matrix", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "418", "Issue": "", "Page": "221", "JournalTitle": "Neurocomputing"}, {"Title": "Noise-suppressing zeroing neural network for online solving time-varying matrix square roots problems: A control-theoretic approach", "Authors": "<PERSON><PERSON><PERSON> Sun; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "116272", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Novel activation functions-based ZNN models for fixed-time solving dynamirc Sylvester equation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Jianqing Gong", "PubYear": 2022, "Volume": "34", "Issue": "17", "Page": "14297", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A parameter-changing zeroing neural network for solving linear equations with superior fixed-time convergence", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "208", "Issue": "", "Page": "118086", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Zeroing neural network with fuzzy parameter for cooperative manner of multiple redundant manipulators", "Authors": "Ying <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "118735", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114737179, "Title": "Self-supervised action representation learning from partial consistency skeleton sequences", "Abstract": "<p>In recent years, self-supervised representation learning for skeleton-based action recognition has achieved remarkable results using skeleton sequences with the advance of contrastive learning methods. However, existing methods often overlook the local information within the skeleton data, so as to not efficiently learn fine-grained features. To leverage local features to enhance representation capacity and capture discriminative representations, we design an adaptive self-supervised contrastive learning framework for action recognition called AdaSCLR. In AdaSCLR, we introduce an adaptive spatiotemporal graph convolutional network to learn the topology of different samples and hierarchical levels and apply an attention mask module to extract salient and non-salient local features from the global features, emphasizing their significance and facilitating similarity-based learning. In addition, AdaSCLR extracts information from the upper and lower limbs as local features to assist the model in learning more discriminative representation. Experimental results show that our approach is better than the state-of-the-art methods on NTURGB+D, NTU120-RGB+D, and PKU-MMD datasets.</p>", "Keywords": "Skeleton-based action recognition; Partial feature refinement; Self-supervised contrastive learning; Graph convolution network", "DOI": "10.1007/s00521-024-09671-5", "PubYear": 2024, "Volume": "36", "Issue": "20", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, China; Corresponding author."}], "References": [{"Title": "Augmented Skeleton Based Contrastive Action Learning with Momentum LSTM for Unsupervised Action Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "569", "Issue": "", "Page": "90", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 114737225, "Title": "“Choose with your eyes closed” instore shopping experience and spatial maps in visually impaired and sighted persons", "Abstract": "<p>The literature on the consumption patterns of specific categories of consumers, such as people with disability, remains limited to date. This study explored the explicit consumer experience of a group of visually impaired (VI) consumers in-store using behavioural and self-report measures. A VI and a control group explored three different product shelves and manipulated target products inside the supermarket. Behavioural and self-report data were collected in relation to three main different phases of the in-store shopping experience: (i) the identification of a product; (ii) the style of product purchase; (iii) the consumers experience. Results showed that in the VI group, accuracy and reaction time vary by product category. Touch was the sense that most guided VI group’s product recognition, but it was also found to be significantly used by the controls across product categories. Higher levels of disorientation, difficulty in finding products, and repeating the route independently were found for VI. The results could encourage the use of tactile touchpoints, braille maps or an initial guided exploration of the supermarket, to allow the VI to memorize the internal layout of the different product categories and allow them to shop independently. This paper extends the literature on the consumption patterns of specific categories of consumers, such as people with disability, that remains limited to date. It also represents the first in-store consumer research took place in Italy comparing Italian VI people with sighted consumers behaviour.</p>", "Keywords": "Visually impaired; Shopping experience; Product purchase; Spatial maps; Behavioural measures", "DOI": "10.1007/s10209-024-01115-0", "PubYear": 2025, "Volume": "24", "Issue": "1", "JournalId": 1397, "JournalTitle": "Universal Access in the Information Society", "ISSN": "1615-5289", "EISSN": "1615-5297", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "International research center for Cognitive Applied Neuroscience (IrcCAN), Università Cattolica del Sacro Cuore, Milan, Italy; Research Unit in Affective and Social Neuroscience, Department of Psychology, Università Cattolica del Sacro Cuore, Milan, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "International research center for Cognitive Applied Neuroscience (IrcCAN), Università Cattolica del Sacro Cuore, Milan, Italy; Research Unit in Affective and Social Neuroscience, Department of Psychology, Università Cattolica del Sacro Cuore, Milan, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "International research center for Cognitive Applied Neuroscience (IrcCAN), Università Cattolica del Sacro Cuore, Milan, Italy; Research Unit in Affective and Social Neuroscience, Department of Psychology, Università Cattolica del Sacro Cuore, Milan, Italy; Corresponding author."}], "References": []}, {"ArticleId": 114737370, "Title": "An efficient Analysis of the Fusion of Statistical-Centred Clustering and Machine Learning for WSN Energy Efficiency", "Abstract": "", "Keywords": "", "DOI": "10.54216/FPA.150217", "PubYear": 2024, "Volume": "15", "Issue": "2", "JournalId": 91518, "JournalTitle": "Fusion: Practice and Applications", "ISSN": "2770-0070", "EISSN": "2692-4048", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, Patna, India"}, {"AuthorId": 2, "Name": "Bala Dhandayuthapani. V.", "Affiliation": "Department of IT, College of Computing and Information Sciences, University of Technology and Applied Sciences, Shinas campus, Oman"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Professor, Department of Computer Science  Engineering, Guru Nanak Instructions Technical Campus (Autonomous), Ibrahimpatnam, Ranga Reddy district, Hyderabad - 501506, Telagana State, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Assistant Professor, Department of, C.S. E., B. P. Man<PERSON> College of Engineering, Madhepura, Bihar, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Associate Professor (Research), Department of Computer Science and Engineering, Koneru Lakshm<PERSON>h Education Foundation, Vaddeswaram, Guntur-522302, Andhra Pradesh, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Professor, Dept.of Artificial Intelligence and Data Science, PES Modern College of Engineering,   Shivajinagar, Pune-411005, India"}], "References": []}, {"ArticleId": 114737371, "Title": "PlaneAC: Line-guided planar 3D reconstruction based on self-attention and convolution hybrid model", "Abstract": "Planar 3D reconstruction aims to simultaneously extract plane instances and reconstruct the local 3D model through the estimated plane parameters. Existing methods achieve promising results either through self-attention or convolution neural network (CNNs), but usually ignore the complementary properties of them. In this paper, we propose a line-guided planar 3D reconstruction method PlaneAC, which leverages the advantage of self-attention and CNNs to capture long-range dependencies and alleviate the computational burden. In addition, explicit connection between two adjacent attention layers is built for better leveraging the transferable knowledge and facilitating the information flow between tokenized feature from different layers. Therefore, the subsequent attention layer can directly interact with previous results. Finally, a line segment filtering method is presented to remove irrelevant guiding information from indistinctive line segments extracted from the image. Extensive experiments on ScanNet and NYUv2 public datasets demonstrate the preferable performance of our proposed method, and the results show that PlaneAC achieves a better trade-off between accuracy and computation cost compared with other state-of-the-art methods.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.110519", "PubYear": 2024, "Volume": "153", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China;Beijing Key Laboratory of Computational Intelligence and Intelligent System, Beijing University of Technology, Beijing, 100124, China;Correspondence to: Beijing University of Technology, No.100, Pingleyuan, Chaoyang District, Beijing, China; Corresponding author"}, {"AuthorId": 3, "Name": "Fuji Fu", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}], "References": [{"Title": "Optical flow-based structure-from-motion for the reconstruction of epithelial surfaces", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107391", "JournalTitle": "Pattern Recognition"}, {"Title": "Blitz-SLAM: A semantic SLAM in dynamic environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108225", "JournalTitle": "Pattern Recognition"}, {"Title": "Transformers in Vision: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "10s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "ViXNet: Vision Transformer with Xception Network for deepfakes based video and image forgery detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "210", "Issue": "", "Page": "118423", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dynamic dense CRF inference for video segmentation and semantic SLAM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109023", "JournalTitle": "Pattern Recognition"}, {"Title": "Accurate light field depth estimation under occlusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "109415", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-stage information diffusion for joint depth and surface normal estimation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "109660", "JournalTitle": "Pattern Recognition"}, {"Title": "Structural asymmetric convolution for wireframe parsing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "128", "Issue": "", "Page": "107410", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 114737409, "Title": "A secure data hiding system in biomedical images using grain 128a algorithm, logistic mapping and elliptical curve cryptography", "Abstract": "<p>Telemedicine and teleconsultations have become rampant today, consequent to the pandemic scenario as well as the availability of seamless internet connectivity. In this context, maintaining the integrity and confidentiality of a medical record has become more significant than ever before. A novel data-hiding methodology based upon Grain 128a algorithm and two-dimensional logistic mapping for the security of biomedical images in applications of telemedicine is proposed in this research article. In this endeavor, the name of the medical practitioner and diagnosis results are embedded into the MRI brain image of a patient with astrocytoma. A highlight of this work is the use of a multilevel system for enhanced security. Initially, Grain 128a is used for deriving the initial factors of the logistic mapping using a 512-bit confidential key. Grain 128a is able to withstand various types of single-key attacks. Logistic Mapping is used to generate two sequences, one for the X and the other for the Y direction. Grain 128a algorithm and logistic mapping are combined to enhance the security as well as the sensitivity of the data hiding system for a single variation in the secret key. A bit diffusion and confusion process is used to identify the pixel locations in which the name of the practitioner and the diagnosis result must be embedded. The doctor’s name and diagnosis result are converted into binary and introduced into the least significant bit positions (LSBs) of those pixel locations. Finally, encryption is done on the data-hided image using Elliptical Curve Cryptography (ECC) for enhanced security. The system performance is analyzed using different measures such as histogram analysis, structural similarity index (SSIM), universal image quality index (UIQI), <PERSON>’s correlation coefficient (PCC), entropy, peak signal-to-noise ratio (PSNR), etc. The novel system is compared with two existing techniques and observed that it has better performance in terms of PSNR and SSIM. The security of the encryption system is measured by means of histogram analysis, correlation coefficient, PSNR, entropy, etc. The performance of the encryption system has been compared with the current state of art technique and it is found that the proposed system outperforms the current state of art techniques in every performance measure.</p>", "Keywords": "Grain 128a; Logistic mapping; MRI; Data hiding; Elliptic curve cryptography; PSNR", "DOI": "10.1007/s11042-024-19147-2", "PubYear": 2025, "Volume": "84", "Issue": "4", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engg, Rajiv Gandhi Institute of Technology, Kottayam, India; APJ <PERSON> Technological University, Thiruvananthapuram, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "APJ Abdul <PERSON> Technological University, Thiruvananthapuram, India; Department of Electronics and Communication Engineering, Government Engineering College, Idukki, India"}], "References": [{"Title": "Embedding electronic patient information in clinical images: an improved and efficient reversible data hiding technique", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "19-20", "Page": "12869", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Robust image steganography approach based on RIWT-Laplacian pyramid and histogram shifting using deep learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "4", "Page": "651", "JournalTitle": "Multimedia Systems"}, {"Title": "Image steganography for securing secret data using hybrid hiding model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "5", "Page": "7749", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A new data hiding approach for image steganography based on visual color sensitivity", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "15", "Page": "23393", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Hiding patient information in medical images: an enhanced dual image separable reversible data hiding algorithm for E-healthcare", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "1", "Page": "321", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 114737415, "Title": "A unified mixed finite element method for fourth-order time-dependent problems using biorthogonal systems", "Abstract": "This article introduces a unified mixed finite element framework based on a saddle-point formulation that applies to time-dependent fourth order linear and nonlinear problems with clamped, simply supported, and Cahn-<PERSON> type boundary conditions. The classical mixed formulations lead to large matrix systems that demand huge storage and computational time making the schemes expensive, especially for the time-dependent problems. The proposed scheme circumvents this by employing biorthogonal basis functions that lead to sparse and positive-definite systems. The article discusses a mixed finite element method for the biharmonic problem and the time-dependent linear and nonlinear versions of the extended Fisher-Kolmogorov equations equipped with the aforementioned boundary conditions. The wellposedness of the scheme is discussed and a priori error estimates are presented for the semi-discrete and fully discrete finite element schemes. The numerical experiments validate the theoretical estimates derived in the paper.", "Keywords": "Extended Fisher-Kolmogorov problem; Saddle point formulation; Mixed finite elements; Biorthogonal basis functions; Error estimates", "DOI": "10.1016/j.camwa.2024.04.013", "PubYear": 2024, "Volume": "165", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, National Institute of Technology Silchar, Assam 788010, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "School of Information and Physical Sciences, College of Engineering, Science and Environment, University of Newcastle, University Drive, Callaghan, NSW 2308, Australia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology Bombay, Mumbai, Maharashtra 400076, India"}], "References": [{"Title": "<PERSON> for the fourth-order nonlinear reaction-diffusion problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "229", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": *********, "Title": "COMPUTING THE CONNECTED COMPONENTS OF THE COMPLEMENT TO THE AMOEBA OF A POLYNOMIAL IN SEVERAL COMPLEX VARIABLES", "Abstract": "<p>In this paper, we propose a method for computing and visualizing the amoeba of a Laurent polynomial in several complex variables, which is applicable in arbitrary dimension. The algorithms developed based on this method are implemented as a free web service (http://amoebas.ru), which enables interactive computation of amoebas for polynomials in two variables, as well as provides a set of computed amoebas and their cross-sections in higher dimensions. The correctness and running time of the proposed algorithms are tested using a set of optimal polynomials in two, three, and four variables, which are generated using Mathematica computer algebra system. The developed program code makes it possible, in particular, to generate optimal hypergeometric polynomials in an arbitrary number of variables supported in an arbitrary zonotope given by a set of generating vectors.</p>", "Keywords": "", "DOI": "10.31857/S0132347423020164", "PubYear": 2023, "Volume": "", "Issue": "2", "JournalId": 58312, "JournalTitle": "Программирование", "ISSN": "0132-3474", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "T. A. ZHUKOV", "Affiliation": "Plekhanov Russian University of Economics"}, {"AuthorId": 2, "Name": "T. M. SADYKOV", "Affiliation": "Plekhanov Russian University of Economics"}], "References": []}, {"ArticleId": 114737438, "Title": "Invariant measures for contact processes with state-dependent birth and death rates", "Abstract": "<p>We consider contact processes on locally compact separable metric spaces with birth and death rates that are heterogeneous in space. We formulate conditions on the rates that ensure the existence of invariant measures of contact processes. One of the crucial conditions is the so-called critical regime condition. To prove the existence of invariant measures, we use the approach proposed in our preceding paper. We discuss in detail the multi-species contact model with a compact space of marks (species) in which both birth and death rates depend on the marks.</p>", "Keywords": "multi-species continuous contact model;birth and death process in continuum;Critical regime;correlation functions;маркированная модель контактов в непрерывном пространстве;процесс рождения и гибели в непрерывной среде;критический режим;корреляционные функции", "DOI": "10.31857/S0555292323020055", "PubYear": 2023, "Volume": "59", "Issue": "2", "JournalId": 61888, "JournalTitle": "Проблемы передачи информации", "ISSN": "0555-2923", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "E. A <PERSON>zhina", "Affiliation": "Kharkevich Institute for Information Transmission Problems of the Russian Academy of Sciences"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Kharkevich Institute for Information Transmission Problems of the Russian Academy of Sciences"}], "References": []}, {"ArticleId": 114737497, "Title": "Overcoming Catastrophic Forgetting in Continual Fine-Grained Urban Flow Inference", "Abstract": "<p>Citywide fine-grained urban flow inference (FUFI) problem aims to infer the high-resolution flow maps from the coarse-grained ones, which plays an important role in sustainable and economic urban computing and intelligent traffic management. Previous models tackle this problem from spatial constraint, external factors and memory cost. However, utilizing the new urban flow maps to calibrate the learned model is very challenging due to the “catastrophic forgetting” problem and is still under-explored. In this paper, we make the first step in FUFI and present CUFAR – Continual Urban Flow inference with augmented Adaptive knowledge Replay – a novel framework for inferring the fine-grained citywide traffic flows. Specifically, (1) we design a spatial-temporal inference network that can extract better flow map features from both local and global levels; (2) then we present an augmented adaptive knowledge replay (AKR) training algorithm to selectively replay the learned knowledge to facilitate the learning process of the model on new knowledge without forgetting. We apply several data augmentation techniques to improve the generalization capability of the learning model, gaining additional performance improvements. We also propose a knowledge discriminator to avoid the “negative replaying” issue introduced by noisy urban flow maps. Extensive experiments on two large-scale real-world FUFI datasets demonstrate that our proposed model consistently outperforms strong baselines and effectively mitigates the forgetting problem.</p>", "Keywords": "", "DOI": "10.1145/3660523", "PubYear": 2024, "Volume": "10", "Issue": "4", "JournalId": 22173, "JournalTitle": "ACM Transactions on Spatial Algorithms and Systems", "ISSN": "2374-0353", "EISSN": "2374-0361", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Software Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Software Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information and Software Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 5, "Name": "<PERSON>ce Trajcevski", "Affiliation": "Department of Electrical and Computer Engineering, Iowa State University, Ames, United States"}], "References": [{"Title": "Semi-supervised Trajectory Understanding with POI Attention for End-to-End Trip Recommendation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Spatial Algorithms and Systems"}, {"Title": "SeqST-GAN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Spatial Algorithms and Systems"}, {"Title": "Artificial intelligence-based vehicular traffic flow prediction methods for supporting intelligent transportation systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "182", "Issue": "", "Page": "107484", "JournalTitle": "Computer Networks"}, {"Title": "Machine Learning-based traffic prediction models for Intelligent Transportation Systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "181", "Issue": "", "Page": "107530", "JournalTitle": "Computer Networks"}, {"Title": "Self-supervised human mobility learning for next location prediction and trajectory classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "228", "Issue": "", "Page": "107214", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Online continual learning in image classification: An empirical survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "469", "Issue": "", "Page": "28", "JournalTitle": "Neurocomputing"}, {"Title": "Automated Urban Planning for Reimagining City Configuration via Adversarial Learning: Quantification, Generation, and Evaluation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Spatial Algorithms and Systems"}, {"Title": "Graph neural network for traffic forecasting: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "117921", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114737508, "Title": "パラレルワイヤ脚の跳躍性能に関する力学モデルに基づく検討と実機における検証", "Abstract": "Parallel wire-driven legs have been proposed as a leg structure that enables both high and continuous jumping. On the other hand, the jumping ability of multiple leg structures including parallel wire-driven legs has not been compared based on dynamic models. In this study, we will develop dynamic models of parallel wire-driven legs and other representative leg structures, and compare their jumping abilities under the same conditions. In addition, the errors between the model and the actual robot are discussed based on the data from the jumping experiment of the parallel wire-driven leg robot RAMIEL.", "Keywords": "Wire-driven;Legged Robot;Parallel-wire;Jumping Robot", "DOI": "10.7210/jrsj.42.274", "PubYear": 2024, "Volume": "42", "Issue": "3", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "Temma Suzuki", "Affiliation": "Graduate School of Information Science and Technology, The University of Tokyo"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Information Science and Technology, The University of Tokyo"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Information Science and Technology, The University of Tokyo"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Information Science and Technology, The University of Tokyo"}], "References": [{"Title": "Learning robust perceptive locomotion for quadrupedal robots in the wild", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "62", "Page": "eabk2822", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 114737546, "Title": "ProRLearn: boosting prompt tuning-based vulnerability detection by reinforcement learning", "Abstract": "<p>Software vulnerability detection is a critical step in ensuring system security and data protection. Recent research has demonstrated the effectiveness of deep learning in automated vulnerability detection. However, it is difficult for deep learning models to understand the semantics and domain-specific knowledge of source code. In this study, we introduce a new vulnerability detection framework, ProRLearn, which leverages two main techniques: prompt tuning and reinforcement learning. Since existing fine-tuning of pre-trained language models (PLMs) struggles to leverage domain knowledge fully, we introduce a new automatic prompt-tuning technique. Precisely, prompt tuning mimics the pre-training process of PLMs by rephrasing task input and adding prompts, using the PLM’s output as the prediction output. The introduction of the reinforcement learning reward mechanism aims to guide the behavior of vulnerability detection through a reward and punishment model, enabling it to learn effective strategies for obtaining maximum long-term rewards in specific environments. The introduction of reinforcement learning aims to encourage the model to learn how to maximize rewards or minimize penalties, thus enhancing performance. Experiments on three datasets (FFMPeg+Qemu, Reveal, and Big-Vul) indicate that ProRLearn achieves performance improvement of 3.27–70.96% over state-of-the-art baselines in terms of F1 score. The combination of prompt tuning and reinforcement learning can offer a potential opportunity to improve performance in vulnerability detection. This means that it can effectively improve the performance in responding to constantly changing network environments and new threats. This interdisciplinary approach contributes to a better understanding of the interplay between natural language processing and reinforcement learning, opening up new opportunities and challenges for future research and applications.</p>", "Keywords": "Vulnerability detection; Prompt tuning; Pre-trained language model; Reinforcement learning", "DOI": "10.1007/s10515-024-00438-9", "PubYear": 2024, "Volume": "31", "Issue": "2", "JournalId": 1834, "JournalTitle": "Automated Software Engineering", "ISSN": "0928-8910", "EISSN": "1573-7535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Nantong University, Nantong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Nantong University, Nantong, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Nantong University, Nantong, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Nantong University, Nantong, China"}], "References": [{"Title": "Boosting methods for multi-class imbalanced data classification: an experimental review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "Pre-trained models: Past, present and future", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "225", "JournalTitle": "AI Open"}, {"Title": "Just-in-time software vulnerability detection: Are we there yet?", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "188", "Issue": "", "Page": "111283", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Pre-train, Prompt, and Predict: A Systematic Survey of Prompting Methods in Natural Language Processing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A survey on physarum polycephalum intelligent foraging behaviour and bio-inspired applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "1", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "PTR: Prompt Tuning with Rules for Text Classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "182", "JournalTitle": "AI Open"}]}, {"ArticleId": 114737717, "Title": "Survival Prediction of Children after Bone Marrow Transplant Using Machine Learning Algorithms", "Abstract": "<p>Bone marrow is the source of many blood-related diseases, such as blood cancers, and Bone Marrow Transplantation (BMT), also known as Hematopoietic Stem Cell Transplantation (HSCT), is a life-saving surgical procedure. However, this treatment is associated with a high risk of mortality. Predicting survival after BMT is therefore essential for effective and accurate treatment. BMT is considered a treatment-related mortality due to several primary causes of death such as infections, toxicity, and Graft-versus-Host Disease (GvHD) that occur after treatment. In addition, several risk factors affect the success of BMT and long-term survival after treatment. Therefore, there is a need for a prediction system based on machine learning techniques that can predict whether the patient will survive after BMT or not, which will definitely help the physicians to make the right decisions before performing the surgery for the patient. In this paper, using a publicly available BMT dataset from the University of California, Irvine ML repository (UCI ML repository), different machine learning models were investigated to predict the survival status of children undergoing BMT treatment. In particular, Random Forest (RF), Bagging Classifier, Extreme Gradient Boost (XGBoost), Adaptive Boosting (AdaBoost), Decision Tree (DT), Gradient Boost (GB), and K-Nearest Neighbors (KNN) were trained on the given dataset. The dataset consists of 45 variables after applying a series of preprocessing steps and removing the multicollinearity features based on the correlation heat map. Then, a feature engineering and modelling step was applied to identify the most significant features, followed by the use of machine learning models to simplify the overall classification process. It’s important to note that the most important features obtained by DT and those obtained by GB were the most suitable for training the Bagging classifier and the KNN model, respectively. In addition to that, hyper-parameters optimization using Grid Search Cross-Validation (GSCV) was applied to both approaches to improve the accuracy of the survival prediction. RF, AdaBoost, GB, and Bagging techniques have achieved the best accuracy of 97.37%</p>", "Keywords": "", "DOI": "10.34028/iajit/21/3/4", "PubYear": 2024, "Volume": "21", "Issue": "3", "JournalId": 69739, "JournalTitle": "The International Arab Journal of Information Technology", "ISSN": "1683-3198", "EISSN": "2309-4524", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114737806, "Title": "Resistance Is Your Friend: Resistance to innovation proposals is a gift. Harness it and your innovation will move faster.", "Abstract": "Resistance to innovation proposals is a gift. Harness it and your innovation will move faster.", "Keywords": "", "DOI": "10.1145/3654696", "PubYear": 2024, "Volume": "67", "Issue": "6", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "U.S. Naval Postgraduate School, Monterey, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "U.S. Naval Postgraduate School, Monterey, CA, USA"}], "References": []}, {"ArticleId": 114737871, "Title": "Development of Wireless Communication Status Monitor Function for Mobile Robot Tele-Operation", "Abstract": "<p>At unmanned construction sites, machinery generally stops functioning or tele-operation is interrupted owing to the problems related to wireless communication. However, the entry of human workers into such sites is often restricted, making it extremely difficult for workers to resolve problems or restore connections in wireless communication. In this study, we interviewed personnel from relevant industries to identify common issues in wireless communication. To address these issues, we focused on improving the availability of wireless communication and proposed a wireless communication status monitoring function. The results from a prototype of the proposed function are also presented.</p>", "Keywords": "wireless communication;unmanned construction;communication protection;disconnection precursor detection;downtime reduction", "DOI": "10.20965/jrm.2024.p0365", "PubYear": 2024, "Volume": "36", "Issue": "2", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Technology & Intelligence Integration, IHI Corporation, 1 Shin-Nakahara-cho, Isogo-ku, Yokohama 235-8501, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technology & Intelligence Integration, IHI Corporation, 1 Shin-Nakahara-cho, Isogo-ku, Yokohama 235-8501, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Technology & Intelligence Integration, IHI Corporation, 1 Shin-Nakahara-cho, Isogo-ku, Yokohama 235-8501, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technology & Intelligence Integration, IHI Corporation, 1 Shin-Nakahara-cho, Isogo-ku, Yokohama 235-8501, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Public Works Research Institute, 1-6 Minamihara, Tsukuba, Ibaraki 305-8516, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Public Works Research Institute, 1-6 Minamihara, Tsukuba, Ibaraki 305-8516, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Public Works Research Institute, 1-6 Minamihara, Tsukuba, Ibaraki 305-8516, Japan"}], "References": []}, {"ArticleId": 114737974, "Title": "Resource-constrained project scheduling with multiple states: Bi-objective optimization model and case study of aircraft maintenance", "Abstract": "This paper introduces a new resource-constrained project scheduling problem in which the project has multiple possible states and whether an activity can be performed at a certain time depends on the state at that time. We are motivated by aircraft maintenance projects where activities need to be performed in particular aircraft states, such as power on/off and jack on/off. We formulate a bi-objective optimization model for this problem, which determines the schedule of activities and the selection of states over the planning horizon, subject to resource constraints, precedence relationship, and state restrictions. The model concerns both the minimization of the project duration and the minimization of the number of state changes over time. A heuristic algorithm, which utilizes genetic algorithm techniques, is developed to obtain Pareto efficient solutions of the model. Its effectiveness is demonstrated through a comparison with CPLEX and an adaptation of NSGA-II. Furthermore, we conduct a case study about aircraft maintenance C-check projects in a major airline. The case study demonstrates that our optimization-based approach is applicable and beneficial to the project scheduling practice with the consideration of states.", "Keywords": "", "DOI": "10.1016/j.cie.2024.110169", "PubYear": 2024, "Volume": "191", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Lingnan College, Sun Yat-sen University, Guangzhou 510275, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Business Administration, Hunan University, Changsha 410082, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Business, Sun Yat-sen University, Guangzhou 510275, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "China Southern Airlines, China"}], "References": [{"Title": "A memetic algorithm to address the multi-node resource-constrained project scheduling problem", "Authors": "<PERSON>; <PERSON>A<PERSON>ole<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "4", "Page": "413", "JournalTitle": "Journal of Scheduling"}, {"Title": "Multi-Objective multi-skill resource-constrained project scheduling problem with skill switches: Model and evolutionary approaches", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "167", "Issue": "", "Page": "107897", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Tabu search for proactive project scheduling problem with flexible resources", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "153", "Issue": "", "Page": "106185", "JournalTitle": "Computers & Operations Research"}, {"Title": "A priority rule for scheduling shared due dates in the resource-constrained project scheduling problem", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "183", "Issue": "", "Page": "109442", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Integrated resource-constrained project scheduling and material ordering problem considering storage space allocation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "185", "Issue": "", "Page": "109608", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Metaheuristics for the bi-objective resource-constrained project scheduling problem with time-dependent resource costs: An experimental comparison", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "163", "Issue": "", "Page": "106489", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 114737994, "Title": "Distance metric learning-based multi-granularity neighborhood rough sets for attribute reduction", "Abstract": "Attribute reduction is a hot research topic in data mining, in which rough set theory-based attribute reduction methods have been widely focused. The neighborhood rough set (NRS) model has good generalization performance and practicality in uncertainty reasoning, so it is often used for attribute reduction in recent years. Calculating the distance between samples in different attribute spaces is a key step in the NRS-based attribute reduction methods, which directly affects the performance of the reduction algorithm. However, the NRS model uses a fixed computational paradigm in calculating the distance between samples and does not consider the influence of labels in the attribute spaces on the distance calculation, which is not conducive to improving the performance of the reduction algorithm. Distance metric learning takes full account of the labels information in the multi-dimensional attribute space, and it can learn the distance between samples by taking into account the integrated principle that samples with the same label are closer and samples with different labels are further away, which helps to reduce the classification uncertainty. Inspired by this, this paper firstly incorporates distance metric learning into the NRS model from the perspective of multi-dimensional attribute space and proposes a distance metric learning-based multi-granularity neighborhood rough set (DmlMNRS) model. The related properties of the DmlMNRS model are also introduced and proved. Then, the DmlMNRS-based attribute reduction criterion and the significance of the attributes are defined. A DmlMNRS-based heuristic attribute reduction (DMNHAR) algorithm is designed based on this. Finally, experiments are performed on fifteen publicly datasets, and the experimental results show that the proposed algorithm has better robustness and classification performance.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111656", "PubYear": 2024, "Volume": "159", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Chongqing Normal University, Chongqing, 401331, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Chongqing Normal University, Chongqing, 401331, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Chongqing Normal University, Chongqing, 401331, PR China;Corresponding author"}, {"AuthorId": 4, "Name": "Weihua Xu", "Affiliation": "College of Artificial Intelligence, Southwest University, Chongqing, 400715, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Artificial Intelligence, Southwest Jiaotong University, Chengdu, 611756, PR China"}], "References": [{"Title": "An efficient feature selection based Bayesian and Rough set approach for intrusion detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "105980", "JournalTitle": "Applied Soft Computing"}, {"Title": "Self-adaptive parameter and strategy based particle swarm optimization for large-scale feature selection problems with multiple classifiers", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106031", "JournalTitle": "Applied Soft Computing"}, {"Title": "Feature selection via normative fuzzy information weight with application into tumor classification", "Authors": "<PERSON><PERSON><PERSON> Dai; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "106299", "JournalTitle": "Applied Soft Computing"}, {"Title": "Revisiting metric learning for few-shot image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "406", "Issue": "", "Page": "49", "JournalTitle": "Neurocomputing"}, {"Title": "A tutorial on distance metric learning: Mathematical foundations, algorithms, experimental analysis, prospects and challenges", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "425", "Issue": "", "Page": "300", "JournalTitle": "Neurocomputing"}, {"Title": "A novel approach to attribute reduction based on weighted neighborhood rough sets", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "220", "Issue": "", "Page": "106908", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Neighborhood rough sets with distance metric learning for feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "224", "Issue": "", "Page": "107076", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel cause analysis approach of grey reasoning Petri net based on matrix operations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "1", "Page": "1", "JournalTitle": "Applied Intelligence"}, {"Title": "A novel hybrid feature selection method considering feature interaction in neighborhood rough set", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107167", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Feature selection using Fisher score and multilabel neighborhood rough sets for multilabel classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "887", "JournalTitle": "Information Sciences"}, {"Title": "Dynamic updating approximations of local generalized multigranulation neighborhood rough set", "Authors": "Weihua Xu; Ke<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "8", "Page": "9148", "JournalTitle": "Applied Intelligence"}, {"Title": "Local knowledge distance for rough approximation measure in multi-granularity spaces", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "605", "Issue": "", "Page": "413", "JournalTitle": "Information Sciences"}, {"Title": "A noise-aware fuzzy rough set approach for feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "250", "Issue": "", "Page": "109092", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A soft neighborhood rough set model and its applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "185", "JournalTitle": "Information Sciences"}, {"Title": "Anomaly detection based on weighted fuzzy-rough density", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109995", "JournalTitle": "Applied Soft Computing"}, {"Title": "MFGAD: Multi-fuzzy granules anomaly detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "95", "Issue": "", "Page": "17", "JournalTitle": "Information Fusion"}, {"Title": "Two-Layer Information Granulation: Mapping-Equivalence Neighborhood Rough Set and Its Attribute Reduction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "2", "Page": "2059", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 114738007, "Title": "Deep SORT Related Studies", "Abstract": "Computer vision is the field of computer science in which computers are made capable to see and recognize like human being. Deep learning is using multiple layers for the purpose of understanding and recognizing various objects. Deep Simple Real Time Tracker is the area in which the objects are tracked in real time from multiple images and videos. Many researchers have contributed to the field and various algorithms have been proposed. The current study presents the deep SORT related studies in which the various algorithms have been presented for the sake of understanding and starting point for the researchers interested in computer vision and deep sorting. The single shot detection, feature extraction, have been explained along with the research conducted. Feature selection and extraction, matching recognition, object tracking through frames have been appended to the current study.", "Keywords": "Object Detection;Deep Learning;Object Tracking;Matching And Recognition;Simple Real Time Tracker", "DOI": "10.32628/CSEIT2410230", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Technology, Faculty of Information Science and Engineering, Ocean University of China"}, {"AuthorId": 2, "Name": "Qinbo Qinbo", "Affiliation": "Department of Computer Science and Technology, Faculty of Information Science and Engineering, Ocean University of China"}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Faculty of Information Science and Engineering, Ocean University of China"}], "References": [{"Title": "A Review of Yolo Algorithm Developments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "1066", "JournalTitle": "Procedia Computer Science"}, {"Title": "Word2Vec and LSTM based deep learning technique for context-free fake news detection", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "1", "Page": "919", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A comprehensive comparative study on intelligence based optimization algorithms used for maximum power tracking in grid-PV systems", "Authors": "Marlin S; Sundarsingh Jebaseelan", "PubYear": 2024, "Volume": "41", "Issue": "", "Page": "100946", "JournalTitle": "Sustainable Computing: Informatics and Systems"}, {"Title": "Deep feature extraction with tri-channel textual feature map for text classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "178", "Issue": "", "Page": "49", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Deep learning and multi-modal fusion for real-time multi-object tracking: Algorithms, challenges, datasets, and comparative study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "105", "Issue": "", "Page": "102247", "JournalTitle": "Information Fusion"}, {"Title": "Dual enhanced semantic hashing for fast image retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "25", "Page": "67083", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Transformer-based assignment decision network for multiple object tracking", "Authors": "<PERSON> P<PERSON>ta; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "241", "Issue": "", "Page": "103957", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Multi-object tracking with adaptive measurement noise and information fusion", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "144", "Issue": "", "Page": "104964", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 114738084, "Title": "Intelligent data-driven acquisition method for user requirements", "Abstract": "<p>Consumer behavior has changed due to digitization. Online shoppers now refer to user reviews containing comprehensive data produced in real-time, which can be used to determine users’ needs. This paper combines Kansei engineering and natural language processing techniques to extract information on users’ needs from online reviews and provide guidance for subsequent product improvements and development. A crawler tool was used to collect a large number of online reviews for a target product. Frequency analysis was then applied to the text to filter out the product components worth analyzing. The results were categorized and aggregated by experts before sentiment analysis was performed on statements containing the selected adjectives. Finally, the user needs identified could be inputted to Kansei engineering for further product design. This paper verifies the merit of the above method when applied to the mountain bike product category on Amazon. The method proved to be a quick and efficient way to attain accurate product evaluations from end-users and thus represents a feasible approach to intelligently determining user preferences.</p>", "Keywords": "Data-driven design; Web crawler; Sentiment analysis; Kansei engineering", "DOI": "10.1007/s00779-024-01804-w", "PubYear": 2024, "Volume": "28", "Issue": "3-4", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "<PERSON>henwei You", "Affiliation": "School of Digital Media and Design Arts, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Design Arts, Beijing University of Technology, Beijing, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Digital Media and Design Arts, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Design Arts, Beijing University of Technology, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Design, Chang Gung University, Taoyuan, Taiwan"}], "References": [{"Title": "E-Revenue Adoption in State Internal Revenue Service", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "41", "JournalTitle": "Journal of Organizational and End User Computing"}, {"Title": "User's Segmentation on Continued Knowledge Management System Use in the Public Sector", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "19", "JournalTitle": "Journal of Organizational and End User Computing"}, {"Title": "A Novel Trust Model for Secure Group Communication in Distributed Computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Organizational and End User Computing"}, {"Title": "The modified Kansei Engineering-based application for sustainable service design", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "", "Page": "102985", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Integrating aesthetic and emotional preferences in social robot design: An affective design approach with Kansei Engineering and Deep Convolutional Generative Adversarial Network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "83", "Issue": "", "Page": "103128", "JournalTitle": "International Journal of Industrial Ergonomics"}]}, {"ArticleId": 114738125, "Title": "A graph attention network with contrastive learning for temporal review-based recommendations", "Abstract": "In recent years, review-based recommendations have recently attracted extensive attention from researchers. However, applying reviews to improve recommendation performance still faces some problems that remain unsolved: (1) Most existing works cannot capture the time-varying user preferences and high-order collaborative features. (2) Most of the existing works often use supervised learning to train models with a limited interaction between users and items, which is not sufficient to learn the more accurate recommendation models. To overcome these problems, we propose a new G raph A ttention N etwork with C ontrastive L earning for temporary review-based recommendation, named GANCL. Specifically, to capture dynamic user preferences and high-order collaborative features, we design a user–item bipartite graph with time-series review information and ratings as its edges, and then use the graph attention and different gating mechanisms to extract the corresponding features. To make full use of the limited interaction between the users and items, we use the contrastive learning paradigm for the nodes and edges in the bipartite graph to more effectively model the user–item interaction. A large number of experiments on five public data from Amazon prove that the performance of the GANCL is improved by 2.76% and 2.83% respectively compared with the state-of-the-art algorithms in MSE and MAE.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111652", "PubYear": 2024, "Volume": "159", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information, Shanghai Dianji University, No. 300 Shuihua Road, Pudong New District, Shanghai, 201306, China;Engineering Research Center of Learning-Based Intelligent System, Ministry of Education, Tianjin University of Technology, No. 391 Bin Shui Xi Dao Road, Xiqing District, Tianjin, 300384, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Research Center of Learning-Based Intelligent System, Ministry of Education, Tianjin University of Technology, No. 391 Bin Shui Xi Dao Road, Xiqing District, Tianjin, 300384, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Research Center of Learning-Based Intelligent System, Ministry of Education, Tianjin University of Technology, No. 391 Bin Shui Xi Dao Road, Xiqing District, Tianjin, 300384, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Information, Shanghai Dianji University, No. 300 Shuihua Road, Pudong New District, Shanghai, 201306, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Information and Electrical Engineering, Asia University, No. 500 Liufeng Road, Wufeng District, Taiwan Province, Taichung, 41354, China"}], "References": [{"Title": "A Primer on Contrastive Pretraining in Language Processing: Methods, Lessons Learned, and Perspectives", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "10", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Modeling User Reviews through Bayesian Graph Attention Networks for Recommendation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Graph Neural Pre-training for Recommendation with Side Information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}]}, {"ArticleId": 114738168, "Title": "Securing cloud-based healthcare applications with a quantum-resistant authentication and key agreement framework", "Abstract": "A biosensor is a method for transmitting various physical phenomena, such as body temperature, electrocardiogram (ECG), pulse, blood pressure, electroencephalogram (EEG), and respiratory rate. This transmission occurs through the utilization of a Wireless Body Area Network (WBAN) when remotely diagnosing patients via Internet-of-Medical-Things (IoMT). However, the transmission of sensitive data from IoMT through WBAN via an insecure channel exposes it to various threats, necessitating the implementation of robust measures to guarantee security against potential adversaries. To address the security concerns associated with patient monitoring in healthcare systems and achieve the necessary security and privacy requirements during communication, a robust authentication framework is indispensable. Hence, it introduces an agile and robust post-quantum authentication framework for cloud-based healthcare applications, effectively mitigating the vulnerabilities identified in the recent literature. This framework is designed to protect against quantum attacks using the Kyber. A formal security verification of the proposed protocol is presented using AVISPA, as well as informally. Additionally, a comparison with the previous works is made regarding both performance and security. The comparison results conclusively show that our proposed framework is better regarding both measures.", "Keywords": "", "DOI": "10.1016/j.iot.2024.101200", "PubYear": 2024, "Volume": "26", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Signals and Systems Analysis, University of M’sila, M’Sila, Algeria;Department of Computer Science, University of M’sila, BP. 166 Ichebilia, 28000 M’sila, Algeria;Corresponding author at: Laboratory of Signals and Systems Analysis, University of M’sila, M’Sila, Algeria"}, {"AuthorId": 2, "Name": "Noured<PERSON>", "Affiliation": "Laboratory of Informatics and its Applications of M’sila (LIAM), University of M’sila, M’sila, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chair of Security and Theoretical Computer Science, University of Tartu, Tartu, Estonia;Department of Computer Engineering, Istinye University, 34010, Istanbul, Turkiye"}], "References": [{"Title": "Cloud-based authenticated protocol for healthcare monitoring system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "8", "Page": "3431", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Healthcare and patient monitoring using IoT", "Authors": "<PERSON><PERSON><PERSON><PERSON>; R. <PERSON>; H. Ertürk Çetin", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100173", "JournalTitle": "Internet of Things"}, {"Title": "A lightweight and secure two-factor authentication scheme for wireless body area networks in health-care IoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "177", "Issue": "", "Page": "107333", "JournalTitle": "Computer Networks"}, {"Title": "An End-to-End Authentication Scheme for Healthcare IoT Systems Using WMSN", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "1", "Page": "607", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Authentication Schemes for Healthcare Applications Using Wireless Medical Sensor Networks: A Survey", "Authors": "<PERSON><PERSON>; Noureddine <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "5", "Page": "382", "JournalTitle": "SN Computer Science"}, {"Title": "Design of Inter-BAN Authentication Protocols for WBAN in a Cloud-Assisted Environment", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "124", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "The Internet of Things (IoT) in healthcare: Taking stock and moving forward", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "", "Page": "100721", "JournalTitle": "Internet of Things"}, {"Title": "A novel authentication protocol to ensure confidentiality among the Internet of Medical Things in covid-19 and future pandemic scenario", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "", "Page": "100797", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 114738203, "Title": "Heterogeneous graph inference with range constrainted L 2 , 1 -collaborative matrix factorization for small molecule-miRNA association prediction", "Abstract": "<p>MicroRNAs (miRNAs) play a vital role in regulating gene expression and various biological processes. As a result, they have been identified as effective targets for small molecule (SM) drugs in disease treatment. Heterogeneous graph inference stands as a classical approach for predicting SM-miRNA associations, showcasing commendable convergence accuracy and speed. However, most existing methods do not adequately address the inherent sparsity in SM-miRNA association networks, and imprecise SM/miRNA similarity metrics reduce the accuracy of predicting SM-miRNA associations. In this research, we proposed a heterogeneous graph inference with range constrained L<sub>2,1</sub>-collaborative matrix factorization (HGIRCLMF) method to predict potential SM-miRNA associations. First, we computed the multi-source similarities of SM/miRNA and integrated these similarity information into a comprehensive SM/miRNA similarity. This step improved the accuracy of SM and miRNA similarity, ensuring reliability for the subsequent inference of the heterogeneity map. Second, we used a range constrained L<sub>2,1</sub>-collaborative matrix factorization (RCLMF) model to pre-populate the SM-miRNA association matrix with missing values. In this step, we developed a novel matrix decomposition method that enhances the robustness and formative nature of SM-miRNA edges between SM networks and miRNA networks. Next, we built a well-established SM-miRNA heterogeneous network utilizing the processed biological information. Finally, HGIRCLMF used this network data to infer unknown association pair scores. We implemented four cross-validation experiments on two distinct datasets, and HGIRCLMF acquired the highest areas under the curve, surpassing six state-of-the-art computational approaches. Furthermore, we performed three case studies to validate the predictive power of our method in practical application.</p><p>Copyright © 2024. Published by Elsevier Ltd.</p>", "Keywords": "Constrained matrix factorization;Heterogeneous graph inference;L(2;1)-norm regularization;Multi-source similarity;SM-miRNA association prediction", "DOI": "10.1016/j.compbiolchem.2024.108078", "PubYear": 2024, "Volume": "110", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao Institute of Software, China University of Petroleum, Qingdao 266580, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao Institute of Software, China University of Petroleum, Qingdao 266580, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao Institute of Software, China University of Petroleum, Qingdao 266580, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao Institute of Software, China University of Petroleum, Qingdao 266580, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao Institute of Software, China University of Petroleum, Qingdao 266580, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Control Engineering, Qingdao University of Technology, Qingdao 266525, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao Institute of Software, China University of Petroleum, Qingdao 266580, China."}], "References": [{"Title": "Graph embedding on biomedical networks: methods, applications and evaluations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1241", "JournalTitle": "Bioinformatics"}, {"Title": "MVGCN: data integration through multi-view graph convolutional network for predicting links in biomedical bipartite networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "426", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": *********, "Title": "A metaphysical account of agency for technology governance", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00146-024-01941-z", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Moral zombies: why algorithms are not moral agents", "Authors": "Carissa <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "2", "Page": "487", "JournalTitle": "AI & SOCIETY"}, {"Title": "Strictly Human: Limitations of Autonomous Systems", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "2", "Page": "269", "JournalTitle": "Minds and Machines"}, {"Title": "Meaningful human control: actionable properties for AI system development", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "1", "Page": "241", "JournalTitle": "AI and Ethics"}]}, {"ArticleId": 114738354, "Title": "An Alternative Method for Upgrading the Conventional Decision Tree Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.18494/SAM5029", "PubYear": 2024, "Volume": "36", "Issue": "4", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Suppa<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Veera Boonjing", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114738427, "Title": "Bake off redux: a review and experimental evaluation of recent time series classification algorithms", "Abstract": "In 2017, a research paper (<PERSON><PERSON><PERSON> et al. Data Mining and Knowledge Discovery 31(3):606-660. 2017) compared 18 Time Series Classification (TSC) algorithms on 85 datasets from the University of California, Riverside (UCR) archive. This study, commonly referred to as a ‘bake off’, identified that only nine algorithms performed significantly better than the Dynamic Time Warping (DTW) and Rotation Forest benchmarks that were used. The study categorised each algorithm by the type of feature they extract from time series data, forming a taxonomy of five main algorithm types. This categorisation of algorithms alongside the provision of code and accessible results for reproducibility has helped fuel an increase in popularity of the TSC field. Over six years have passed since this bake off, the UCR archive has expanded to 112 datasets and there have been a large number of new algorithms proposed. We revisit the bake off, seeing how each of the proposed categories have advanced since the original publication, and evaluate the performance of newer algorithms against the previous best-of-category using an expanded UCR archive. We extend the taxonomy to include three new categories to reflect recent developments. Alongside the originally proposed distance, interval, shapelet, dictionary and hybrid based algorithms, we compare newer convolution and feature based algorithms as well as deep learning approaches. We introduce 30 classification datasets either recently donated to the archive or reformatted to the TSC format, and use these to further evaluate the best performing algorithm from each category. Overall, we find that two recently proposed algorithms, MultiROCKET+Hydra (<PERSON> et al. 2022) and HIVE-COTEv2 (<PERSON> et al. Mac<PERSON> Learn 110:3211-3243. 2021), perform significantly better than other approaches on both the current and new TSC problems.", "Keywords": "Time series classification; Bake off; HIVE-COTE; ROCKET; UCR Archive", "DOI": "10.1007/s10618-024-01022-1", "PubYear": 2024, "Volume": "38", "Issue": "4", "JournalId": 3928, "JournalTitle": "Data Mining and Knowledge Discovery", "ISSN": "1384-5810", "EISSN": "1573-756X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electronics and Computer Science, University of Southampton, Southampton, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Humboldt-Universität zu Berlin, Berlin, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electronics and Computer Science, University of Southampton, Southampton, United Kingdom; School of Computing Sciences, University of East Anglia, Norwich, United Kingdom; Corresponding author."}], "References": [{"Title": "A semi-supervised model for knowledge graph embedding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "1", "Page": "1", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "TS-CHIEF: a scalable and accurate forest algorithm for time series classification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3", "Page": "742", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "ROCKET: exceptionally fast and accurate time series classification using random convolutional kernels", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "5", "Page": "1454", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "InceptionTime: Finding AlexNet for time series classification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "6", "Page": "1936", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Detecting voids in 3D printing using melt pool time series data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "3", "Page": "845", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "The great multivariate time series classification bake off: a review and experimental evaluation of recent algorithmic advances", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "2", "Page": "401", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Time series extrinsic regression", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "3", "Page": "1032", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "HIVE-COTE 2.0: a new meta ensemble for time series classification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "11-12", "Page": "3211", "JournalTitle": "Machine Learning"}, {"Title": "MultiRocket: multiple pooling operators and transformations for fast and effective time series classification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "5", "Page": "1623", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Hydra: competing convolutional kernels for fast and accurate time series classification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "5", "Page": "1779", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "WEASEL 2.0: a random dilated dictionary transform for fast, accurate and memory constrained time series classification", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "112", "Issue": "12", "Page": "4763", "JournalTitle": "Machine Learning"}, {"Title": "quant: a minimalist interval method for time series classification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "38", "Issue": "4", "Page": "2377", "JournalTitle": "Data Mining and Knowledge Discovery"}]}, {"ArticleId": 114738464, "Title": "Series of formulas for b<PERSON><PERSON><PERSON><PERSON><PERSON> parameters in the theory of polar codes", "Abstract": "<p><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> parameters are used in the theory of polar codes to determine positions of frozen and information bits. These parameters characterize rate of polarization of channels WN(i), 1 ≤ i ≤ N, which are constructed in a special way from the original channel W, where N = 2n is the channel length, n = 1, 2, .... In the case where W is a binary symmetric memoryless channel, we present two series of formulas for the parameters Z(WN(i)): for i = N - 2k + 1, 0 ≤ k ≤ n, and for i = N/2 - 2k + 1, 1 ≤ k ≤ n - 2. The formulas require of the order of $\\binom{2^{n-k}+2^k-1}{2^k} 2^{2^k}$ addition operations for the first series and of the order of $\\binom{2^{n-k-1}+2^k-1}{2^k} 2^{2^k}$ for the second. In the cases i = 1, N/4 + 1, N/2 + 1, N, the obtained expressions for the parameters have been simplified by computing the sums in them. We show potential generalizations for the values of i in the interval (N/4, N). We also study combinatorial properties of the polarizing matrix GN of a polar code with <PERSON><PERSON><PERSON><PERSON>’s kernel. In particular, we establish simple recurrence relations between rows of the matrices GN and GN/2.</p>", "Keywords": "polar code;Bhattacharyya parameter;polarizing matrix;полярный код;параметр Бхаттачарьи;поляризационная матрица", "DOI": "10.31857/S0555292323010011", "PubYear": 2023, "Volume": "59", "Issue": "1", "JournalId": 61888, "JournalTitle": "Проблемы передачи информации", "ISSN": "0555-2923", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "S. G Kolesnikov", "Affiliation": "Siberian Federal University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Siberian Federal University"}], "References": []}, {"ArticleId": 114738523, "Title": "Model predictive control for a bending pneumatic muscle based on an online modified generalized <PERSON><PERSON><PERSON> model", "Abstract": "<p>Pneumatic actuators exhibit significant potential across various applications owing to their compliance, yet achieving precise motion control remains challenging due to rate-dependent and asymmetric hysteresis. While the Prandtl–<PERSON> model adeptly captures intricate hysteresis traits, its practical control usage often necessitates intricate inversions, resulting in elevated computational burden and limited accommodation of system uncertainties and model inaccuracies. This study introduces an online, rate-dependent modified generalized Prandtl–Ishlinskii model derived via the gradient descent algorithm. This model is seamlessly amalgamated with a model predictive control strategy, addressing the inversion challenge inherent in the Prandtl–<PERSON> model. Leveraging integration with a three-layer fuzzy neural network controller, the proposed approach achieves closed-loop trajectory tracking control for a soft bending pneumatic muscle. Convergence analysis, grounded in <PERSON><PERSON><PERSON><PERSON> theory, underscores the efficacy of the proposed model. Comprehensive real-world comparative experiments affirm the approach’s effectiveness and reliability.</p>", "Keywords": "Bending pneumatic muscle; Hysteresis; Prand<PERSON><PERSON><PERSON><PERSON><PERSON> model; Model predictive control", "DOI": "10.1007/s00521-024-09666-2", "PubYear": 2024, "Volume": "36", "Issue": "20", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Applied Science, University of Pennsylvania, Philadelphia, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan, China; Corresponding author."}], "References": [{"Title": "Position and Force Control of a Soft Pneumatic Actuator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "3", "Page": "550", "JournalTitle": "Soft Robotics"}, {"Title": "Hierarchical control of soft manipulators towards unstructured interactions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "1", "Page": "411", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "Modeling and Control of Hysteresis Characteristics of Piezoelectric Micro-Positioning Platform Based on Duhem Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "5", "Page": "122", "JournalTitle": "Actuators"}]}, {"ArticleId": 114738651, "Title": "Fuzzy Data Association-Towards Better Uncertainty Tracking in Clutter Environments", "Abstract": "<p>The goal of explainable artificial intelligence (XAI) is to solve problems in a way that humans can understand how it does it. For data association there is growing demand for XAI, in which the measurement uncertainty and target (dynamic or/and measurement) model uncertainty are two fundamental problems in maneuvering target tracking in clutter. It commonly suffers of false alarms and missed detections. These situations focus on enhancing explainability, mitigating bias and creating better outcomes for all. Most the probabilistic data association (PDA) methods are weakly able, or even unable, to explain data association. To overcome these situations, the XAI components employed of two modules of Fuzzy-joint probability data association (FJDA) and Fuzzy maneuver compensator (FMC) are first established. Next, these two modules are further employed to construct maneuver tracking scheme, FJDA is then utilized to evaluate the association degree of measurements belonging to different targets and FMC plays compensation role in accordance with maneuver need. The performances of the proposed maneuver tracking scheme were compared with the PDA method and the joint probabilistic data association (JPDA) method using simulated radar surveillance data under a high cluttered environment. The numerical simulation proposed maneuver tracking scheme embedded XAI components FJDA/FCM having a remarkable improvement, due to fully utilize the useful knowledge information in the data association and reduces the impact of measurement uncertainties of the maneuvering target tracking with changing dynamics.</p>", "Keywords": "Probabilistic data association; fuzzy logic framework; cluttered environments", "DOI": "10.1142/S0218488524500089", "PubYear": 2024, "Volume": "32", "Issue": "2", "JournalId": 15072, "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems", "ISSN": "0218-4885", "EISSN": "1793-6411", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Medical and Healthcare Business, Hsin Sheng College of Medical Care and Management, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, Yu Da University of Science and Technology, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, Yuan Ze University, 135 Yuan-Tung Rd., Zhongli District, Taoyuan, Taiwan, P. R. China"}], "References": []}, {"ArticleId": *********, "Title": "Development and Evaluation of Mobility and Excavation Rover Toward Lunar Base Construction", "Abstract": "<p>The exploration and utilization of water resources on the Moon are of substantial global interest. To utilize lunar resources and construct bases, the construction machinery should travel over the lunar surface (which is mainly covered with powdery regolith) and excavate the regolith. However, various technical issues should be resolved to achieve this efficiently. In this study, a new platform rover was developed, and its motion behavior was analyzed to better understand the traveling and excavation behaviors of construction machinery on the Moon. The rover is a four-track vehicle equipped with a robotic arm consisting of a boom, arm, and bucket. To analyze the rover’s motion behavior in sandy terrain, we first developed a simulator based on terramechanics and performed a numerical analysis. Subsequently, various experiments were conducted using the rover in the JAXA Space Exploration Field, which simulates the lunar environment. In the experiments, the rover traveled over level and sloped terrains and excavated the ground. The simulation and experimental results revealed similar trends in the traveling and excavation behaviors of the rover. These results can serve as basic guidelines for the design and operation of construction machinery on the Moon.</p>", "Keywords": "lunar base;construction;rover;mobility;excavation", "DOI": "10.20965/jrm.2024.p0334", "PubYear": 2024, "Volume": "36", "Issue": "2", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Japan Aerospace Exploration Agency, 3-1-1 <PERSON><PERSON><PERSON><PERSON>, Chuo-k<PERSON>, Sagamihara, Kanagawa 252-5210, Japan"}], "References": [{"Title": "An Optimal Design Methodology for the Trajectory of Hydraulic Excavators Based on Genetic Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "1248", "JournalTitle": "Journal of Robotics and Mechatronics"}]}, {"ArticleId": 114738697, "Title": "Adaptive control of pneumatic end-effector polishing force based on dual extended state observer", "Abstract": "<p>In the process of aero-engine blade polishing, parameter uncertainties and unmodeled disturbances in the pneumatic end-effector cause fluctuation of the polishing force, affecting the quality of blade processing. To solve the problems mentioned above, a polishing force adaptive controller based on dual extended state observer is proposed. A novel mathematical model of the pneumatic end-effector is established based on the flow characteristics near the zero position of the proportional valve, and a matched disturbance factor is introduced. The controller proposed in this paper contains parameter adaptive law and dual extended state observer. The former aims to compensate for the parameter uncertainties, and the latter aims to compensate for the matched and unmatched disturbances, respectively. The experimental analysis including aero-engine blade polishing constant contact stress control is applied. Simulation and experimental studies show that the controller proposed in this paper can effectively compensate for the influence of parameter uncertainties and unmodeled disturbances on the fluctuation of the polishing force. The fluctuation of the polishing force is 0.41 N, and the quality of the surface finish of aero-engine blades is improved.</p>", "Keywords": "Robot polishing; Pneumatic end-effector; Dual extended state observer; Adaptive control", "DOI": "10.1007/s00170-024-13531-w", "PubYear": 2024, "Volume": "132", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei Key Laboratory of Robot Perception and Human Machine Fusion, School of Mechanical Engineering, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>g Jin", "Affiliation": "Hebei Key Laboratory of Robot Perception and Human Machine Fusion, School of Mechanical Engineering, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei Key Laboratory of Robot Perception and Human Machine Fusion, School of Mechanical Engineering, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hebei Key Laboratory of Robot Perception and Human Machine Fusion, School of Mechanical Engineering, Hebei University of Technology, Tianjin, China; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei Key Laboratory of Robot Perception and Human Machine Fusion, School of Mechanical Engineering, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei Key Laboratory of Robot Perception and Human Machine Fusion, School of Mechanical Engineering, Hebei University of Technology, Tianjin, China"}], "References": [{"Title": "Constant force control for aluminum wheel hub grinding based on ESO + backstepping", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "49", "Issue": "5", "Page": "824", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "Review on robot-assisted polishing: Status and future trends", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "80", "Issue": "", "Page": "102482", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A model for material removal of robot-assisted blade polishing using abrasive cloth wheel", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "7-8", "Page": "2819", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Vibration suppression in macro–micro grinding system of aeroengine blade based on impedance compensation prediction control strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "1-2", "Page": "793", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 114738791, "Title": "Reachable set performance and quantized sampled-data synchronization analysis of neural networks under random packet dropouts via enhanced looped functional", "Abstract": "The study aims to present the synchronization concerns of neural networks with stochastic packet dropouts utilizing a quantized memory sampled-data control (QMSDC) scheme and the problem of reachable set analysis (RSA) for NNs under the influence of external disturbances are discussed. To do this, a novel free-weighting matrix integral inequality (FWMII) is proposed with an augmented Lyapunov-functional. Furthermore, at this time, we show that the proposed inequality is less conservative due to the delay information. In the meantime, a novel enhanced looped function is created that utilizes not only sampling information but also incorporates both transmission delay information and a quantized sampling pattern. Next, using the proposed FWMII technique, several sufficient conditions in terms of linear matrix inequalities (LMIs) are derived to suggest how an augmented closed-loop system can achieves stochastic mean-square exponential synchronization under random packet loss situations. Finally, the proposed approach demonstrates its usefulness and superiority by comparing it with existing methods through numerical simulation of the secure communications.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124055", "PubYear": 2024, "Volume": "252", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The School of IT Information and Control Engineering, Kunsan National University, Gunsan 54150, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The School of IT Information and Control Engineering, Kunsan National University, Gunsan 54150, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The School of IT Information and Control Engineering, Kunsan National University, Gunsan 54150, Republic of Korea;Corresponding author"}], "References": [{"Title": "Augmented two-side-looped <PERSON><PERSON><PERSON><PERSON> functional for sampled-data-based synchronization of chaotic neural networks with actuator saturation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "422", "Issue": "", "Page": "287", "JournalTitle": "Neurocomputing"}, {"Title": "Robust synchronization of uncertain delayed neural networks with packet dropout using sampled-data control", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "12", "Page": "9054", "JournalTitle": "Applied Intelligence"}, {"Title": "Sampled-data control for synchronization of Markovian jumping neural networks with packet dropout", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "8", "Page": "8898", "JournalTitle": "Applied Intelligence"}, {"Title": "Quantized control for finite-time synchronization of delayed fractional-order memristive neural networks: The Gronwall inequality approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119310", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Synchronization sampled-data control of uncertain neural networks under an asymmetric <PERSON><PERSON><PERSON><PERSON><PERSON> functional method", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "239", "Issue": "", "Page": "122475", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114738870, "Title": "Efficient loss updated XGBoost with deep emended genetic algorithm for detecting online fraudulent transactions", "Abstract": "<p>In the fast-paced technological era, online financial transactions have gained widespread use as it offers significant merits to customers for easy transfer of money through smart phones. Nevertheless, fraudulent transactions put individual’s money into risk, for which, suitable approaches are required to detect such deceits. Concurrently, with the progress of ML (Machine Learning) approaches, existing works have bidden to identify the fraudulent and normal transactions. However, studies lacked in accordance with accuracy rate and only limited focus has been provided for detection of generalized fraudulent transactions. Considering this, the current study considers IoT fraud dataset and proposes DEGA (Deep Emended Genetic Algorithm) to attain better performance for detecting fraudulent and normal transactions. This model employs a competitive approach, integrating, new crossover and selection methods. This intend to improvise the ability of global search and partition the chromosomes into losers and winners. This ensures high quality parent for selection. Besides, a dynamic-mutation function is also proposed for enhancing the model’s searching ability. Subsequently, the study proposes EL-UXGB (Efficient Loss-Updated eXtreme Gradient Boosting) wherein dual sigmoid loss functions are proposed to resolve the imbalanced label cases. The overall performance of this study is assessed through analysis that confirms its effectiveness in detecting fraudulent transactions.</p>", "Keywords": "Machine Learning; Genetic Algorithm; eXtreme Gradient Boosting; Fraudulent Transactions", "DOI": "10.1007/s11042-024-19183-y", "PubYear": 2024, "Volume": "83", "Issue": "37", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Scholar, Department of Computer Applications, St<PERSON>Peter’s Institute of Higher Education & Research, Chennai, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Applications, St<PERSON>Peter’s Institute of Higher Education & Research, Chennai, India"}], "References": [{"Title": "HOBA: A novel feature engineering methodology for credit card fraud detection with a deep learning architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "557", "Issue": "", "Page": "302", "JournalTitle": "Information Sciences"}, {"Title": "Towards automated feature engineering for credit card fraud detection using multi-perspective HMMs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "393", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Data engineering for fraud detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "150", "Issue": "", "Page": "113492", "JournalTitle": "Decision Support Systems"}, {"Title": "A Perceptron Based Neural Network Data Analytics Architecture for the Detection of Fraud in Credit Card Transactions in Financial Legacy Systems", "Authors": "<PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "", "Page": "358", "JournalTitle": "WSEAS TRANSACTIONS ON SYSTEMS AND CONTROL"}, {"Title": "Speech emotion recognition using optimized genetic algorithm-extreme learning machine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "17", "Page": "23963", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A Proposed Fraud Detection Model based on e-Payments Attributes a Case Study in Egyptian e-Payment Gateway", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "5", "Page": "179", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Performance analysis of metaheuristics based hyperparameters optimization for fraud transactions detection", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "2", "Page": "921", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Credit card fraud detection using ensemble data mining methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "19", "Page": "29057", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Smart credit card fraud detection system based on dilated convolutional neural network with sampling technique", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "20", "Page": "31691", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Mobile money fraud detection using data analysis and visualization techniques", "Authors": "Rizik <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "6", "Page": "17093", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A voting ensemble machine learning based credit card fraud detection using highly imbalance data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "18", "Page": "54729", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 114738899, "Title": "機械・知能系における RX カリキュラムのグランドデザイン", "Abstract": "", "Keywords": "Robotics Education;RX Curriculum;3D Technology;ROS;Robot System Integration", "DOI": "10.7210/jrsj.42.215", "PubYear": 2024, "Volume": "42", "Issue": "3", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Mechanical and Intelligent Systems Engineering, Department of Engineering for Future Innovation, National Institute of Technology, Ichinoseki College"}], "References": []}, {"ArticleId": 114738917, "Title": "Enhancing English Learning for Special Needs Students through Technology", "Abstract": "<p>In the realm of educational instruction, teachers encounter a diverse array of students, each with unique learning styles and potential challenges. Among these learners are those with distinct needs, requiring tailored approaches to facilitate their academic progress. These students often encounter hurdles across various educational facets, necessitating personalized strategies to enhance their learning experiences and self-expression. Thus, the focus of this study is to investigate whether the integration of technological tools such as laptops and tablets, coupled with multimedia elements, can serve as effective motivators and engagement enhancers for students with specific learning requirements.\r This qualitative study adopts an observational approach, examining the impact of technology integration on students with special needs across six primary schools in municipalities of Gjilan and Prizren, Kosovo. The primary objective is to gauge the efficacy of technology-assisted instruction, particularly in the context of English language learning. Through a four-week observation period, conducted twice weekly, the study aims to discern the differential outcomes between traditional instructional methods and those supplemented by technology applications.\r The study results revealed that in technology-driven English language lessons, special needs students were more motivated to get involved in the lesson, worked together, and participated actively in the classroom activities.</p>", "Keywords": "", "DOI": "10.9734/ajrcos/2024/v17i6462", "PubYear": 2024, "Volume": "17", "Issue": "6", "JournalId": 63540, "JournalTitle": "Asian Journal of Research in Computer Science", "ISSN": "", "EISSN": "2581-8260", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114738944, "Title": "Pseudo Random Number Generator Based on Cellular Automata with Self Organized Criticality", "Abstract": "<p>In cryptography, pseudo-random numbers are crucial. The degree of strength of a cryptographic technique is directly influenced by the key’s randomness. Although numerous researchers have shown that cellular automata (CA) are effective as pseudo-random number generators (PRNGs), the fundamental CA structure is being changed to increase the efficiency in terms of randomness. Here, <PERSON><PERSON>'s Ant Model and Programmable Controllable Cellular Automata (PCCA) in combination with a sand pile are proposed. Sand pile model and <PERSON><PERSON>’s Ant model are built for the generation of Rule control word for the successive iterations and Cell control word in selecting controllable cells for iterations respectively. The Controllable Cellular Automaton types used here are CCA4 and CCA5. Both Sand pile model and <PERSON><PERSON>’s Ant model exhibit chaotic behaviour. Two different rule sets are used and their results are compared. Also the frequency of each rule in the rule set are determined and found to be equally utilized. Due to the integration of these systems with cellular automata, which likewise display dynamic chaotic behaviour. Its results are greater efficiency in terms of randomness. Pseudo Random number sequence generated by this proposed PCCA is benchmarked with the statistical NIST test. The experiment shows that the resulting random sequence is highly random. Their entropies are calculated and the respective graphs are plotted.</p>", "Keywords": "Pseudo Random number; Programmable Controllable Cellular Automata (PCCA); Cellular automata (CA); Pseudo-Random Number Generators (PRNGs); Cryptography", "DOI": "10.1007/s42979-024-02750-3", "PubYear": 2024, "Volume": "5", "Issue": "5", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Ramco Institute of Technology, Rajapalayam, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Ramco Institute of Technology, Rajapalayam, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Ramco Institute of Technology, Rajapalayam, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Ramco Institute of Technology, Rajapalayam, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Ramco Institute of Technology, Rajapalayam, India"}], "References": [{"Title": "A novel image encryption scheme based on pseudo-random coupled map lattices with hybrid elementary cellular automata", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "593", "Issue": "", "Page": "121", "JournalTitle": "Information Sciences"}, {"Title": "Key-based dynamic S-Box approach for PRESENT lightweight block cipher", "Authors": "", "PubYear": 2023, "Volume": "17", "Issue": "12", "Page": "3398", "JournalTitle": "KSII Transactions on Internet and Information Systems"}]}, {"ArticleId": *********, "Title": "Box-Behnken modeling to optimize the engineering response and the energy expenditure in material extrusion additive manufacturing of short carbon fiber reinforced polyamide 6", "Abstract": "The field of production engineering is constantly attempting to be distinguished for promoting sustainability, energy efficiency, cost-effectiveness, and prudent material consumption. In this study, three control parameters (3D printing settings), namely nozzle temperature, travel speed, and layer height (L H ) are being investigated on polyamide 6/carbon fiber (15 wt%) tensile specimens. The aim is the optimum combination of energy efficiency and mechanical performance of the specimens. For the analysis of the results, the Box-Behnken design-of-experiment was applied along with the analysis of variance. The statistical analysis conducted based on the experimental results, indicated the importance of the L H control setting, as to affecting the mechanical strength. In particular, the best tensile strength value (σ B  = 83.52 MPa) came from the 0.1 mm L H . The same L H , whereas caused the highest energy consumption in 3D printing (E PC = 0.252 MJ) and printing time (P T = 2272 s). The lowest energy consumption (E PC = 0.036 MJ) and printing time (PT = 330 s) were found at 0.3 mm L H . Scanning electron microscopy was employed as a part of the manufactured specimens’ 3D printing quality evaluation, while Thermogravimetric analysis was also conducted. The modeling approach led to the formation of equations for the prediction of critical metrics related to energy consumption and the mechanical performance of composite parts built with the MEX 3D printing method. These equations proved their reliability through a confirmation run, which showed that they can safely be applied, within specific boundaries, in real-life applications.\n Graphical abstract", "Keywords": "Polyamide 6; Carbon fiber; Box-Behnken design; Energy efficiency; Material extrusion (MEX); 3D printing", "DOI": "10.1007/s00170-024-13617-5", "PubYear": 2024, "Volume": "132", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Hellenic Mediterranean University, Heraklion, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Hellenic Mediterranean University, Heraklion, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Hellenic Mediterranean University, Heraklion, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Hellenic Mediterranean University, Heraklion, Greece"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering, Hellenic Mediterranean University, Chania, Greece"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Hellenic Mediterranean University, Heraklion, Greece; Corresponding author."}], "References": [{"Title": "Processing, mechanical characterization, and micrography of 3D-printed short carbon fiber reinforced polycarbonate polymer matrix composite material", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "7-8", "Page": "3185", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Parametric optimization of material extrusion 3D printing process: an assessment of Box-Behnken vs. full-factorial experimental approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "5-6", "Page": "3163", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Optimization of friction stir welding for various tool pin geometries: the weldability of Polyamide 6 plates made of material extrusion additive manufacturing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "7-8", "Page": "2931", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Compressive response versus power consumption of acrylonitrile butadiene styrene in material extrusion additive manufacturing: the impact of seven critical control parameters", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "3-4", "Page": "1233", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 114738971, "Title": "Guest Editorial: Advanced image restoration and enhancement in the wild", "Abstract": "", "Keywords": "", "DOI": "10.1049/cvi2.12283", "PubYear": 2024, "Volume": "18", "Issue": "4", "JournalId": 11350, "JournalTitle": "IET Computer Vision", "ISSN": "1751-9632", "EISSN": "1751-9640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National University of Defense Technology  Changsha China"}, {"AuthorId": 2, "Name": "Juncheng Li", "Affiliation": "Shanghai University  Shanghai China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Tokyo  Bunkyo‐ku China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Würzburg  Würzburg Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Defense Technology & Sun Yat‐sen University  Shenzhen China"}], "References": []}, {"ArticleId": 114739053, "Title": "Hybrid Deep Learning and FAST–BRISK 3D Object Detection Technique for Bin-picking Application", "Abstract": "", "Keywords": "", "DOI": "10.18494/SAM4840", "PubYear": 2024, "Volume": "36", "Issue": "4", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114739168, "Title": "Prediction of Flying Height Using Deep Neural Network Based on Particle Swarm Optimization in Hard Disk Drive Manufacturing Process", "Abstract": "", "Keywords": "", "DOI": "10.18494/SAM4825", "PubYear": 2024, "Volume": "36", "Issue": "4", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 114739208, "Title": "A conceptual model of agile manufacturing in the shipbuilding industry for improving shipyard performance", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJASM.2024.10063658", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 10974, "JournalTitle": "International Journal of Agile Systems and Management", "ISSN": "1741-9174", "EISSN": "1741-9182", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> Sutri<PERSON>no", "Affiliation": ""}], "References": []}, {"ArticleId": 114739227, "Title": "Efficient Virtual Machine Placement Strategy Based on Enhanced Genetic Approach", "Abstract": "<p>The background of the study is rooted in the critical importance of efficient virtual machine (VM) placement in cloud computing environments. VM placement efficiency is critical in cloud computing, especially when utilizing an improved genetic technique. This paper incorporates genetic meta-heuristic to integrate VMs into the minimal number of physical machines (PMs). In order to describe the fitness function in the proposed algorithm (GaMat and GaLin), we have also incorporated predicted usage of PMs CPU usages. The aim of the article is to demonstrate the effectiveness of genetic meta-heuristic in improving VMs placement efficiency in cloud computing environments. The study focuses on minimizing energy consumption (EC) VM migration and violations of the Service Level Agreement (SLA) by integrating VMs into the minimal number of PMs using the proposed algorithms. The algorithms’ performance is evaluated by comparing them with the best-fit power-aware decreasing (Pa) VM placement strategy, based on metrics like EC, VM migration, and SLA violations. Tests were conducted in CloudSim through detailed simulations using actual workload data. The average values of the performance metrics for 10 days of the workload are collected for the proposed VM placement approach. The proposed work reduces EC by 25%, VM migration more than 50% and SLA by 58% when compared to the power aware best fit decreasing. The results of the simulations are interpreted and analysed, which shows the effectiveness of the proposed algorithms in contrast to the best-fit Pa strategy. In conclusion, the study demonstrates the effectiveness of genetic meta-heuristic based proposed algorithms (GaMat and GaLin) in optimizing VM placement in cloud computing environments. By integrating VMs into the minimal number of PMs while considering predicted resource usage, GaMat and GaLin significantly reduce EC, VM migration, and SLA violations compared to the best-fit power-aware decreasing (Pa) strategy.</p>", "Keywords": "VM placement; Energy consumption; Genetic algorithm; VM consolidation; Service level agreement", "DOI": "10.1007/s42979-024-02832-2", "PubYear": 2024, "Volume": "5", "Issue": "5", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Garhwal University, Garhwal, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Garhwal University, Garhwal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graphic Era University, Dehradun, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "BT Kumaon Institute of Technology, Almora, Dwarahat, India"}], "References": [{"Title": "A hybrid energy–Aware virtual machine placement algorithm for cloud environments", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113306", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 114739485, "Title": "Investigation of interactions between fiber lasers and Si3N4 sheets for drilling square microholes with multi-ring strategy", "Abstract": "<p>A high pulsed fiber laser was utilized to drill square microholes in Si<sub>3</sub>N<sub>4</sub> sheets in an atmospheric environment. Various processing parameters including scan spacing, number of scan passes, and number of multi-ring paths with a multi-ring strategy were adjusted to laser-drill Si<sub>3</sub>N<sub>4</sub> sheets. The geometric characteristics of laser-drilled square microholes with shoulder height, taper angle, and corner radius were measured using a laser scanning microscope. X-ray diffraction was applied to examine the residual stress of the Si<sub>3</sub>N<sub>4</sub> sheets before and after laser drilling. Moreover, the heat-affected zone and element content were examined using a scanning electron microscope. The experimental results exhibited that the optimal shoulder height of the square microhole drilled with the multi-ring strategy was 6.67 ± 0.21 μm, which was approximately 77% lower than that of 29.05 ± 10.95 μm for the square microhole drilled with the single-ring strategy. Moreover, the residual stresses of the original Si<sub>3</sub>N<sub>4</sub> sheet and the square microholes laser-drilled by single-ring and multi-ring strategies were − 181.2 ± 41, 164.5 ± 31.9, and 104.6 ± 7.8 MPa, respectively. The proposed laser drilling technology with the multi-ring strategy can be widely used in the semiconductor industry for probe cards.</p>", "Keywords": "Fiber laser; Si3N4 sheet; Multi-ring strategy; Laser drilling technology; Residual stress; Probe card", "DOI": "10.1007/s00170-024-13383-4", "PubYear": 2024, "Volume": "132", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taipei University of Technology, Taipei, Taiwan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taipei University of Technology, Taipei, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Taiwan Instrument Research Institute, National Applied Research Laboratories, Hsinchu, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Taiwan Instrument Research Institute, National Applied Research Laboratories, Hsinchu, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON>l-<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Ming Chi University of Technology, New Taipei City, Taiwan; Research Center for Intelligent Medical Devices, Ming Chi University of Technology, New Taipei City, Taiwan; Department of Mechanical Engineering, Chang Gung University, Taoyuan City, Taiwan; Corresponding author."}], "References": []}]