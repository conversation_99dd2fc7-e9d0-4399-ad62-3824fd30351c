[{"ArticleId": 80291507, "Title": "Organization of an Agents’ Formation through a Cellular Automaton", "Abstract": "<p>The paper considers the algorithm of the distributed organization of an agents’ formation specified by a graph of agents and a numerical simulation of such an algorithm. We describe a cellular automaton simulating the motion of agents, and study its features in connection with the type of landscape along which agents move. The specified cellular automaton has two representations—one-dimensional one and two-dimensional one.</p>", "Keywords": "cellular automaton; autonomous agents; reflexive agents; agents’ formation control", "DOI": "10.1134/S0005117920010130", "PubYear": 2020, "Volume": "81", "Issue": "1", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "University of Information Technology and Management in Rzeszow, Rzeszow, Poland"}], "References": []}, {"ArticleId": 80291514, "Title": "Intelligent Control Systems and Fuzzy Controllers. I. Fuzzy Models, Logical-Linguistic and Analytical Regulators", "Abstract": "<p>The problems of control systems intellectualization are observed. The necessity of intellectualization of a wide range of systems and control methods is proved. The hierarchy of levels of intellectual control observed and comparison analysis of different artificial intelligence devices given. Importance of target setting’s automation problems’ solving in control systems is pointed out, as well as intellectualization of anthropocentric systems, including the ones based on fuzzy logic and case-based reasoning. The logical-linguistic and analytical, fuzzy controllers are considered, based on fuzzy logics of <PERSON><PERSON><PERSON>, implication of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. An overview of the Mamdani-type controllers, controllers based on TS-model is provided. The conditions of optimality and stability of control systems with Mamdani fuzzy controllers are analyzed. The Sugeno dynamic models and the ANFIS adaptive models and the methods of learning developed on the basis of fuzzy controllers are considered.</p>", "Keywords": "intelligent control; logical-linguistic controllers; stability conditions; the Sugeno and Mamdani controllers; TS-model; membership function; fuzzification; defuzzification", "DOI": "10.1134/S0005117920010142", "PubYear": 2020, "Volume": "81", "Issue": "1", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Trapeznikov Institute of Control Sciences, Russian Academy of Sciences, Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Lipetsk State Technical University, Lipetsk, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Trapeznikov Institute of Control Sciences, Russian Academy of Sciences, Moscow, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Trapeznikov Institute of Control Sciences, Russian Academy of Sciences, Moscow, Russia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Lipetsk State Technical University, Lipetsk, Russia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Internet Holding E-generator, Ltd., Moscow, Russia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Trapeznikov Institute of Control Sciences, Russian Academy of Sciences, Moscow, Russia"}], "References": []}, {"ArticleId": 80291515, "Title": "Knowledge granularity based incremental attribute reduction for incomplete decision systems", "Abstract": "<p>Attribute reduction is an important application of rough set theory. With the dynamic changes of data becoming more and more common, traditional attribute reduction, also called static attribute reduction, is no longer efficient. How to update attribute reducts efficiently gets more and more attention. In the light of the variation about the number of objects, we focus on incremental attribute reduction approaches based on knowledge granularity which can be used to measure the uncertainty in incomplete decision systems. We first introduce incremental mechanisms to calculate knowledge granularity for incomplete decision systems when multiple objects vary dynamically. Then, incremental attribute reduction algorithms for incomplete decision systems when adding multiple objects and when deleting multiple objects are proposed respectively. Finally, comparative experiments on different real-life data sets are conducted to demonstrate the effectiveness and efficiency of the proposed incremental algorithms for updating attribute reducts with the variation of multiple objects in incomplete decision systems.</p>", "Keywords": "Incremental attribute reduction; Knowledge granularity; Incomplete decision system; Rough sets", "DOI": "10.1007/s13042-020-01089-4", "PubYear": 2020, "Volume": "11", "Issue": "5", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "Chu<PERSON><PERSON>", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Computing and Language Information Processing, Hunan Normal University, Changsha, China"}, {"AuthorId": 2, "Name": "Jianhua Dai", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Computing and Language Information Processing, Hunan Normal University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Computing and Language Information Processing, Hunan Normal University, Changsha, China"}], "References": [{"Title": "An incremental attribute reduction approach based on knowledge granularity for incomplete decision systems", "Authors": "<PERSON><PERSON><PERSON>; Ji<PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "4", "Page": "545", "JournalTitle": "Granular Computing"}]}, {"ArticleId": 80291562, "Title": "A non-intrusive approach for efficient stochastic emulation and optimization of model-based nitrate-loading management decision support", "Abstract": "Use of physically-motivated numerical models like groundwater flow-and-transport models for probabilistic impact assessments and optimization under uncertainty (OUU) typically incurs such a computational burdensome that these tools cannot be used during decision making. The computational challenges associated with these models can be addressed through emulation. In the land-use/water-quality context, the linear relation between nitrate loading and surface-water/groundwater nitrate concentrations presents an opportunity for employing an efficient model emulator through the application of impulse-response matrices. When paired with first-order second-moment techniques, the emulation strategy gives rise to the “stochastic impulse-response emulator” (SIRE). SIRE is shown to facilitate non-intrusive, near-real time, and risk-based evaluation of nitrate-loading change scenarios, as well as nitrate-loading OUU subject to surface-water/groundwater concentration constraints in high decision variable and parameter dimensions. Two case studies are used to demonstrate SIRE in the nitrate-loading context.", "Keywords": "Emulation under uncertainty ; Groundwater modeling ; Nitrate ; Decision support ; Optimization under uncertainty", "DOI": "10.1016/j.envsoft.2020.104657", "PubYear": 2020, "Volume": "126", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "GNS Science, New Zealand;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "GNS Science, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "U.S. Geological Survey, United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "U.S. Geological Survey, United States"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Market Economics, New Zealand"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "GNS Science, New Zealand||U.S. Geological Survey, United States|Market Economics, New Zealand"}], "References": [{"Title": "Disentangling environmental and economic contributions to hydro-economic model output uncertainty: An example in the context of land-use change impact assessment", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "127", "Issue": "", "Page": "104653", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 80291584, "Title": "The AI Settlement Generation Challenge in Minecraft", "Abstract": "<p>This article outlines what we learned from the first year of the AI Settlement Generation Competition in Minecraft, a competition about producing AI programs that can generate interesting settlements in Minecraft for an unseen map. This challenge seeks to focus research into adaptive and holistic procedural content generation. Generating Minecraft towns and villages given existing maps is a suitable task for this, as it requires the generated content to be adaptive, functional, evocative and aesthetic at the same time. Here, we present the results from the first iteration of the competition. We discuss the evaluation methodology, present the different technical approaches by the competitors, and outline the open problems.</p>", "Keywords": "Competition; Generative design; Procedural content generation; Minecraft", "DOI": "10.1007/s13218-020-00635-0", "PubYear": 2020, "Volume": "34", "Issue": "1", "JournalId": 4294, "JournalTitle": "KI - Künstliche Intelligenz", "ISSN": "0933-1875", "EISSN": "1610-1987", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Adaptive Systems Research Group, University of Hertfordshire, Hatfield, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Game Innovation Lab, New York University, New York, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Game Innovation Lab, New York University, New York, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Game Innovation Lab, New York University, New York, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Game Innovation Lab, New York University, New York, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Game Innovation Lab, New York University, New York, USA"}, {"AuthorId": 7, "Name": "S<PERSON><PERSON>g Ye", "Affiliation": "Game Innovation Lab, New York University, New York, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON> Cao", "Affiliation": "Game Innovation Lab, New York University, New York, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Game Innovation Lab, New York University, New York, USA"}], "References": []}, {"ArticleId": 80291587, "Title": "Behind DeepMind’s AlphaStar AI that Reached Grandmaster Level in StarCraft II", "Abstract": "", "Keywords": "", "DOI": "10.1007/s13218-020-00642-1", "PubYear": 2020, "Volume": "34", "Issue": "1", "JournalId": 4294, "JournalTitle": "KI - Künstliche Intelligenz", "ISSN": "0933-1875", "EISSN": "1610-1987", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IT University of Copenhagen and modl.ai, Copenhagen, Denmark"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LIACS, Universiteit Leiden, Leiden, The Netherlands"}], "References": []}, {"ArticleId": 80291648, "Title": "Special Issue on AI in Games", "Abstract": "", "Keywords": "", "DOI": "10.1007/s13218-020-00645-y", "PubYear": 2020, "Volume": "34", "Issue": "1", "JournalId": 4294, "JournalTitle": "KI - Künstliche Intelligenz", "ISSN": "0933-1875", "EISSN": "1610-1987", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IT University of Copenhagen and modl.ai, Copenhagen, Denmark"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LIACS, Universiteit Leiden, Leiden, The Netherlands"}], "References": []}, {"ArticleId": 80291711, "Title": "Analysis of electronic health records based on long short‐term memory", "Abstract": "<p>There is a large amount of historical data of the patient's hospitalization named the electronic health records (EHRs), but the data are not fully utilized for great challenges as poor quality, high dimension, and so on. Previous studies have primarily used machine learning methods that rely heavily on manual extraction of features. Recently, many deep learning approaches are applied to predictive model of EHRs. Recurrent neural networks (RNN) are often used to model EHR data, but RNN performance degrades in the face of large sequence lengths. To solve these challenges, we develop a long short‐term memory with attention mechanism for mortality prediction. The dataset used in this article is the Medical Information Mart for Intensive Care III, which contains comprehensive clinical data for the patients. The experimental results demonstrate that the predicted results can be effectively interpreted using the attention mechanism. Compared with other baseline models, our model improves the accuracy of prediction, and helps doctors reduce the average diagnostic time.</p>", "Keywords": "attention mechanism;deep learning;electronic health records;long short‐term memory networks;recurrent neural network", "DOI": "10.1002/cpe.5684", "PubYear": 2020, "Volume": "32", "Issue": "14", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China; Shandong Provincial Key Laboratory for Distributed Computer Software Novel Technology, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Psychiatry, Jining Mental Hospital, Jining, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China; Shandong Provincial Key Laboratory for Distributed Computer Software Novel Technology, Jinan, China"}, {"AuthorId": 4, "Name": "Feng <PERSON>", "Affiliation": "Key Laboratory of TCM Data Cloud Service in Universities of Shandong, Shandong Management University, Jinan, China"}], "References": [{"Title": "A Gaussian error correction multi‐objective positioning model with NSGA‐II", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "An under‐sampled software defect prediction method based on hybrid multi‐objective cuckoo search", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 80291965, "Title": "High order discriminant analysis based on Riemannian optimization", "Abstract": "Supervised learning of linear discriminant analysis is a well-known algorithm in machine learning, but most of the discriminant relevant algorithms are generally fail to discover the nonlinear structures in dimensionality reduction. To address such problem, thus we propose a novel method for dimensionality reduction of high-dimensional dataset, named manifold-based high order discriminant analysis (MHODA). Transforming the optimization problem from the constrained Euclidean space to a restricted search space of Riemannian manifold and employing the underlying geometry of nonlinear structures, it takes advantage of the fact that matrix manifold is actually of low dimension embedded into the ambient space. More concretely, we update the projection matrices for optimizing over the Stiefel manifold, and exploit the second order geometry of trust-region method. Moreover, in order to validate the efficiency and accuracy of the proposed algorithm, we conduct clustering and classification experiments by using six benchmark datasets. The numerical results demonstrate that MHODA is superiority to the most state-of-the-art methods.", "Keywords": "Classification ; Clustering ; Dimensionality reduction ; Discriminant analysis ; Product manifold ; Riemannian optimization ; Stiefel manifold", "DOI": "10.1016/j.knosys.2020.105630", "PubYear": 2020, "Volume": "195", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Information Technology, Sun Yat-sen University, Guangzhou, Guangdong, 510006, China"}, {"AuthorId": 2, "Name": "Zhengming Ma", "Affiliation": "School of Electronics and Information Technology, Sun Yat-sen University, Guangzhou, Guangdong, 510006, China;Corresponding author"}], "References": []}, {"ArticleId": 80292028, "Title": "Extracting Polarity Shifting Patterns from Any Corpus Based on Natural Annotation", "Abstract": "In recent years, online sentiment texts are generated by users in various domains and in different languages. Binary polarity classification (positive or negative) on business sentiment texts can help both companies and customers to evaluate products or services. Sometimes, the polarity of sentiment texts can be modified, making the polarity classification difficult. In sentiment analysis, such modification of polarity is termed as\n polarity shifting \n , which shifts the polarity of a sentiment clue (emotion, evaluation, etc.). It is well known that detection of polarity shifting can help improve sentiment analysis in texts. However, to detect polarity shifting in corpora is challenging: (1) polarity shifting is normally sparse in texts, making human annotation difficult; (2) corpora with dense polarity shifting are few; we may need polarity shifting patterns from various corpora.\n \n \n In this article, an approach is presented to extract polarity shifting patterns from any text corpus. For the first time, we proposed to select texts rich in polarity shifting by the idea of\n natural annotation \n , which is used to replace human annotation. With a sequence mining algorithm, the selected texts are used to generate polarity shifting pattern candidates, and then we rank them by C-value before human annotation. The approach is tested on different corpora and different languages. The results show that our approach can capture various types of polarity shifting patterns, and some patterns are unique to specific corpora. Therefore, for better performance, it is reasonable to construct polarity shifting patterns directly from the given corpus.", "Keywords": "Sentiment analysis; natural annotation; polarity shifting; prior polarity; sequence mining", "DOI": "10.1145/3345518", "PubYear": 2020, "Volume": "19", "Issue": "2", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "1. College of Computer and Control Engineering, Minjiang University; 2. Fujian Provincial Key Laboratory of Information Processing and Intelligent Control, Minjiang University; 3. Internet Innovation Research Center of Humanities and Social Sciences Base of Colleges and Universities in Fujian, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Control Engineering, Minjiang University, Fujian Province, China"}, {"AuthorId": 3, "Name": "Yuanzheng Cai", "Affiliation": "College of Computer and Control Engineering, Minjiang University, Fujian Province, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Control Engineering, Minjiang University, Fujian Province, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Computer and Control Engineering, Minjiang University, Fujian Province, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "1. College of Mathmetics and Computer Science, Fuzhou University; 2. Fujian Provincial Key Laboratory of Information Processing and Intelligent Control, Minjiang University, Fuzhou, Fujian Province, China"}], "References": []}, {"ArticleId": 80292029, "Title": "Discovering Underlying Plans Based on Shallow Models", "Abstract": "Plan recognition aims to discover target plans (i.e., sequences of actions) behind observed actions, with history plan libraries or action models in hand. Previous approaches either discover plans by maximally “matching” observed actions to plan libraries, assuming target plans are from plan libraries, or infer plans by executing action models to best explain the observed actions, assuming that complete action models are available. In real-world applications, however, target plans are often not from plan libraries, and complete action models are often not available, since building complete sets of plans and complete action models are often difficult or expensive. In this article, we view plan libraries as corpora and learn vector representations of actions using the corpora; we then discover target plans based on the vector representations. Specifically, we propose two approaches, DUP and RNNPlanner, to discover target plans based on vector representations of actions. DUP explores the EM-style (Expectation Maximization) framework to capture local contexts of actions and discover target plans by optimizing the probability of target plans, while RNNPlanner aims to leverage long-short term contexts of actions based on RNNs (Recurrent Neural Networks) framework to help recognize target plans. In the experiments, we empirically show that our approaches are capable of discovering underlying plans that are not from plan libraries without requiring action models provided. We demonstrate the effectiveness of our approaches by comparing its performance to traditional plan recognition approaches in three planning domains. We also compare DUP and RNNPlanner to see their advantages and disadvantages.", "Keywords": "Plan recognition; action representation; recurrent neural networks; shallow model", "DOI": "10.1145/3368270", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Sun Yat-Sen University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Arizona State University, Tempe, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Arizona State University, Tempe, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Sun Yat-Sen University, Guangzhou, China"}], "References": []}, {"ArticleId": 80292032, "Title": "Using Sub-Optimal Plan Detection to Identify Commitment Abandonment in Discrete Environments", "Abstract": "Assessing whether an agent has abandoned a goal or is actively pursuing it is important when multiple agents are trying to achieve joint goals, or when agents commit to achieving goals for each other. Making such a determination for a single goal by observing only plan traces is not trivial, as agents often deviate from optimal plans for various reasons, including the pursuit of multiple goals or the inability to act optimally. In this article, we develop an approach based on domain independent heuristics from automated planning, landmarks, and fact partitions to identify sub-optimal action steps—with respect to a plan—within a fully observable plan execution trace. Such capability is very important in domains where multiple agents cooperate and delegate tasks among themselves, such as through\n social commitments \n , and need to ensure that a delegating agent can infer whether or not another agent is actually progressing towards a delegated task. We demonstrate how a creditor can use our technique to determine—by observing a trace—whether a debtor is honouring a commitment. We empirically show, for a number of representative domains, that our approach infers sub-optimal action steps with very high accuracy and detects commitment abandonment in nearly all cases.", "Keywords": "Commitments; domain-independent heuristics; landmarks; optimal plan; plan abandonment; plan execution; sub-optimal plan", "DOI": "10.1145/3372119", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "PUCRS, Porto Alegre, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Aberdeen, Aberdeen, Scotland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "PUCRS, Porto Alegre, Brazil"}], "References": []}, {"ArticleId": 80292033, "Title": "Newton Methods for Convolutional Neural Networks", "Abstract": "Deep learning involves a difficult non-convex optimization problem, which is often solved by stochastic gradient (SG) methods. While SG is usually effective, it may not be robust in some situations. Recently, Newton methods have been investigated as an alternative optimization technique, but most existing studies consider only fully connected feedforward neural networks. These studies do not investigate some more commonly used networks such as Convolutional Neural Networks (CNN). One reason is that Newton methods for CNN involve complicated operations, and so far no works have conducted a thorough investigation. In this work, we give details of all building blocks, including the evaluation of function, gradient, Jacobian, and Gauss-Newton matrix-vector products. These basic components are very important not only for practical implementation but also for developing variants of Newton methods for CNN. We show that an efficient\n MATLAB \n implementation can be done in just several hundred lines of code. Preliminary experiments indicate that Newton methods are less sensitive to parameters than the stochastic gradient approach.", "Keywords": "Convolution neural networks; large-scale classification; newton methods; subsampled Hessian", "DOI": "10.1145/3368271", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Rakuten Institute of Technology, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Taiwan University, Taipei City, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Taiwan University, Taipei City, Taiwan"}], "References": []}, {"ArticleId": 80292035, "Title": "Punjabi to ISO 15919 and Roman Transliteration with Phonetic Rectification", "Abstract": "Transliteration removes the script barriers. Unfortunately, Punjabi is written in four different scripts, i.e., Gurmukhi, Shahmukhi, Devnagri, and Latin. The Latin script is understandable for nearly all factions of the Punjabi community. The objective of our work is to transliterate the Punjabi Gurmukhi script into Latin script. There has been considerable progress in Punjabi to Latin transliteration, but the accuracy of present-day systems is less than 50% (Google Translator has approximately 45% accuracy). We do not have the facility of a rich parallel corpus for Punjabi, so we cannot use the corpus-based techniques of machine learning that are in vogue these days. The existing systems of transliteration follow grapheme-based approach. The grapheme-based transliteration is unable to handle many scenarios such as tones, inherent schwa, glottal stops, nasalization, and gemination. In this article, the grapheme-based transliteration has been augmented with phonetic rectification where the Punjabi script is rectified phonetically before applying character-to-character mapping. Handling the inherent short vowel schwa was the major challenge in phonetic rectification. Instead of following the fixed syllabic pattern, we devised a generic finite state transducer to insert schwa. The accuracy of our transliteration system is approximately 96.82%.", "Keywords": "Punjabi; Transliteration; computational linguistics; natural language processing; phonology", "DOI": "10.1145/3359991", "PubYear": 2020, "Volume": "19", "Issue": "2", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Engineering and Technology Lahore, Pakistan and Faculty of Information Technology, University of Central Punjab, Pakistan"}, {"AuthorId": 2, "Name": "Dr. <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Engineering and Technology Lahore, Pakistan"}], "References": []}, {"ArticleId": 80292036, "Title": "Travel Recommendation via Fusing Multi-Auxiliary Information into Matrix Factorization", "Abstract": "As an e-commerce feature, the personalized recommendation is invariably highly-valued by both consumers and merchants. The e-tourism has become one of the hottest industries with the adoption of recommendation systems. Several lines of evidence have confirmed the travel-product recommendation is quite different from traditional recommendations. Travel products are usually browsed and purchased relatively infrequently compared with other traditional products (e.g., books and food), which gives rise to the extreme sparsity of travel data. Meanwhile, the choice of a suitable travel product is affected by an army of factors such as departure, destination, and financial and time budgets. To address these challenging problems, in this article, we propose a Probabilistic Matrix Factorization with Multi-Auxiliary Information (PMF-MAI) model in the context of the travel-product recommendation. In particular, PMF-MAI is able to fuse the probabilistic matrix factorization on the user-item interaction matrix with the linear regression on a suite of features constructed by the multiple auxiliary information. In order to fit the sparse data, PMF-MAI is built by a whole-data based learning approach that utilizes unobserved data to increase the coupling between probabilistic matrix factorization and linear regression. Extensive experiments are conducted on a real-world dataset provided by a large tourism e-commerce company. PMF-MAI shows an overwhelming superiority over all competitive baselines on the recommendation performance. Also, the importance of features is examined to reveal the crucial auxiliary information having a great impact on the adoption of travel products.", "Keywords": "Travel product recommendation; linear regression; multiple auxiliary information; probabilistic matrix factorization; recommender systems", "DOI": "10.1145/3372118", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Finance and Economics, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing University of Finance and Economics, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Arizona, Tucson, Arizona, USA"}], "References": []}, {"ArticleId": 80292037, "Title": "Is Rank Aggregation Effective in Recommender Systems? An Experimental Analysis", "Abstract": "Recommender Systems are tools designed to help users find relevant information from the myriad of content available online. They work by actively suggesting items that are relevant to users according to their historical preferences or observed actions. Among recommender systems, top-\n N \n recommenders work by suggesting a ranking of\n N \n items that can be of interest to a user. Although a significant number of top-\n N \n recommenders have been proposed in the literature, they often disagree in their returned rankings, offering an opportunity for improving the final recommendation ranking by aggregating the outputs of different algorithms.\n \n \n Rank aggregation was successfully used in a significant number of areas, but only a few rank aggregation methods have been proposed in the recommender systems literature. Furthermore, there is a lack of studies regarding rankings’ characteristics and their possible impacts on the improvements achieved through rank aggregation. This work presents an extensive two-phase experimental analysis of rank aggregation in recommender systems. In the first phase, we investigate the characteristics of rankings recommended by 15 different top-\n N \n recommender algorithms regarding agreement and diversity. In the second phase, we look at the results of 19 rank aggregation methods and identify different scenarios where they perform best or worst according to the input rankings’ characteristics.\n \n Our results show that supervised rank aggregation methods provide improvements in the results of the recommended rankings in six out of seven datasets. These methods provide robustness even in the presence of a big set of weak recommendation rankings. However, in cases where there was a set of non-diverse high-quality input rankings, supervised and unsupervised algorithms produced similar results. In these cases, we can avoid the cost of the former in favor of the latter.", "Keywords": "Rank aggregation; machine learning; recommender systems", "DOI": "10.1145/3365375", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Universidade Federal de Minas Gerais"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Universidade Federal de Minas Gerais"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, Universidade Federal de Minas Gerais"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Universidade Federal Lavras"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, Universidade Federal de Minas Gerais"}], "References": []}, {"ArticleId": 80292039, "Title": "Web Table Extraction, Retrieval, and Augmentation", "Abstract": "Tables are powerful and popular tools for organizing and manipulating data. A vast number of tables can be found on the Web, which represent a valuable knowledge resource. The objective of this survey is to synthesize and present two decades of research on web tables. In particular, we organize existing literature into six main categories of information access tasks: table extraction, table interpretation, table search, question answering, knowledge base augmentation, and table augmentation. For each of these tasks, we identify and describe seminal approaches, present relevant resources, and point out interdependencies among the different tasks.", "Keywords": "Table extraction; table augmentation; table interpretation; table mining; table retrieval; table search", "DOI": "10.1145/3372117", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Stavanger, Stavanger, Norway"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Stavanger, Stavanger, Norway"}], "References": []}, {"ArticleId": 80292042, "Title": "Isarn Dharma Word Segmentation Using a Statistical Approach with Named Entity Recognition", "Abstract": "In this study, we developed an Isarn Dharma word segmentation system. We mainly focused on solving the word ambiguity and unknown word problems in unsegmented Isarn Dharma text. Ambiguous Isarn Dharma words occur frequently in word construction due to the writing style without tone markers. Thus, words can be interpreted as having different tones and meanings in the same writing text. To overcome these problems, we developed an Isarn Dharma character cluster–(IDCC) based statistical model and affixation and integrated it with the named entity recognition method (IDCC-C-based statistical model and affixation with named entity recognition (NER)). This method integrates the IDCC-based and character-based statistical models to distinguish the word boundaries. The IDCC-based statistical model utilizes the IDCC feature to disambiguate any ambiguous words. The unknown words are handled using the character-based statistical model, based on the character features. In addition, linguistic knowledge is employed to detect the boundaries of a new word based on the construction morphology and NER. In evaluations, we compared the proposed method with various word segmentation methods. The experimental results showed that the proposed method performed slightly better than the other methods when the corpus size increased. Using the test set, the proposed method obtained the best F-measure of 92.19, an F-measure that was better than the IDCC longest matching grouping at 2.85.", "Keywords": "<PERSON><PERSON>; <PERSON><PERSON>; statistical model; word segmentation approach", "DOI": "10.1145/3359990", "PubYear": 2020, "Volume": "19", "Issue": "2", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Natural Language and Speech Processing Laboratory (NLSP), Khon Kaen University, Khon Kaen, Thailand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Natural Language and Speech Processing Laboratory (NLSP), Khon Kaen University, Khon Kaen, Thailand"}], "References": []}, {"ArticleId": 80292045, "Title": "Subword Attentive Model for Arabic Sentiment Analysis", "Abstract": "Social media data is unstructured data where these big data are exponentially increasing day to day in many different disciplines. Analysis and understanding the semantics of these data are a big challenge due to its variety and huge volume. To address this gap, unstructured Arabic texts have been studied in this work owing to their abundant appearance in social media Web sites. This work addresses the difficulty of handling unstructured social media texts, particularly when the data at hand is very limited. This intelligent data augmentation technique that handles the problem of less availability of data are used. This article has proposed a novel architecture for hand Arabic words classification and understands based on convolutional neural networks (CNNs) and recurrent neural networks. Moreover, the CNN technique is the most powerful for the analysis of Arabic tweets and social network analysis. The main technique used in this work is character-level CNN and a recurrent neural network stacked on top of one another as the classification architecture. These two techniques give 95% accuracy in the Arabic texts dataset.", "Keywords": "Arabic; convolutional neural network; data augmentation; gated recurrent unit; sentiment evaluation; unstructured texts", "DOI": "10.1145/3360016", "PubYear": 2020, "Volume": "19", "Issue": "2", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Balqa Applied University, Salt, Jordan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Zagazig University, Egypt"}], "References": []}, {"ArticleId": 80292047, "Title": "Characteristics of lightning signals over the Tibetan Plateau and the capability of FY-4A LMI lightning detection in the Plateau", "Abstract": "Because of its topographic features, lightning flashes are unique over the Tibetan Plateau. The Lightning Mapper Imager (LMI) on board the launched China’s Fengyun (FY)-4A geostationary satellite is a useful tool for studying these lightning flashes. In this study, firstly, based on data acquired by the Lightning Imaging Sensor (LIS) on board the Tropical Rainfall Measuring Mission (TRMM) satellite in the period 1998–2013, the optical radiation characteristics of lightning flashes over the Tibetan Plateau were compared with those over the central and eastern land region (CELR) of China at the same latitude. The results showed that lightning flashes over the Tibetan Plateau had a mean optical radiance of 422.35 μJ sr<sup>–1</sup> m<sup>–2</sup> nm<sup>–1</sup>, a mean duration of 210.79 ms and a mean radiation area of 217.33 km<sup>2</sup>, whereas the lightning flashes over the CELR had a mean optical radiance of 692.83 μJ sr<sup>–1</sup> m<sup>–2</sup> nm<sup>–1</sup>, a mean duration of 295.29 ms and a mean radiation area of 290.85 km<sup>2</sup>. Evidently, lightning flashes over the Tibetan Plateau had significantly lower radiance, shorter life cycles, and smaller radiation areas than those over the CELR. Secondly, the data for lightning events over the Tibetan Plateau acquired from the FY-4A LMI and International Space Station (ISS) LIS were compared from 21 March 2017 to 25 September 2017, and from 20 March 2018 to 18 September 2018. The correlation between the frequency distribution of the optical radiance of lightning events observed by the two lightning imagers was 99.33%, but due to the footprint area of a LMI pixel is larger than that of a LIS pixel, the mean radiance of lightning events over the Tibetan Plateau measured by the FY-4A LMI was only 1/5 of that measured by the ISS LIS. Based on the statistical results, a time/space coincidence window of 2.1 s/33 km was selected. A resulting mean coincidence ratio (CR) of 3.66% was found for the FY-4A LMI and ISS LIS lightning events for the Tibetan Plateau. The spatial distribution of the CR values was correlated with the optical radiance of lightning events and the duration of lightning flashes as well as the number of events in flashes, with the Pearson’s correlation coefficients (<i>r</i>) of 0.80, 0.82 and 0.78, respectively. These results suggest that the optical radiation characteristics of lightning flashes over the Tibetan Plateau have a great impact on their detection by the FY-4A LMI. For observation of lightning flashes over the Tibetan Plateau using the FY-4A LMI, it is necessary to enable the Real-Time Event Processor (RTEP) algorithm to more finely distinguish various regions based on lightning flash characteristics and to set a more flexible and smaller threshold for extracting lightning events.", "Keywords": "", "DOI": "10.1080/01431161.2020.1723176", "PubYear": 2020, "Volume": "41", "Issue": "12", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Satellite Meteorological Center, China Meteorological Administration, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Satellite Meteorological Center, China Meteorological Administration, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Satellite Meteorological Center, China Meteorological Administration, Beijing, China"}], "References": []}, {"ArticleId": 80292169, "Title": "A note on parallel approximate pseudoinverse matrix techniques for solving linear least squares problems", "Abstract": "A new parallel generic approximate sparse pseudoinverse matrix technique using a decoupled column-wise approach, based on modified row-threshold incomplete QR factorization techniques, is proposed. The explicit preconditioned conjugate gradient least squares method in conjunction with the new parallel generic approximate sparse pseudoinverse matrix technique is used for solving linear least square problems. Numerical results indicating the applicability and effectiveness of the proposed parallel generic approximate sparse pseudoinverse matrix techniques for solving various model problems are given.", "Keywords": "Incomplete QR decomposition ; <PERSON><PERSON><PERSON> approximate pseudoinverse ; Explicit preconditioned conjugate gradient least squares method ; Linear least squares problems", "DOI": "10.1016/j.jocs.2020.101092", "PubYear": 2020, "Volume": "41", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "Anastasia-<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics and Telematics, Harokopio University of Athens, GR 177 78 Athens, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, School of Engineering, Democritus University of Thrace, University Campus, Kimmeria, GR 67100 Xanthi, Greece"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, School of Engineering, Democritus University of Thrace, University Campus, Kimmeria, GR 67100 Xanthi, Greece;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics and Telematics, Harokopio University of Athens, GR 177 78 Athens, Greece"}], "References": []}, {"ArticleId": 80292511, "Title": "Issue Information", "Abstract": "<p>No abstract is available for this article.</p>", "Keywords": "", "DOI": "10.1002/smr.2187", "PubYear": 2020, "Volume": "32", "Issue": "3", "JournalId": 2327, "JournalTitle": "Journal of Software: Evolution and Process", "ISSN": "2047-7473", "EISSN": "2047-7481", "Authors": [], "References": []}, {"ArticleId": 80292534, "Title": "Motivations of employees' communicative behaviors on social media", "Abstract": "Purpose Given that an increasing number of social media platforms allow employees to share company-related information, the present study seeks to understand their complicated motivations for social media behaviors. Specifically, this study explores the antecedents of employees' positive and negative company-related information-sharing intentions on two distinctive social media platforms, personal (e.g. Facebook) and anonymous social networking sites (e.g. Glassdoor). Design/methodology/approach An online survey was conducted with 419 full-time employees in the United States from various industry sectors. Findings Individual (enjoyment, venting negative feelings, and self-enhancement), interpersonal (bonding and bridging ties), and organizational (organization–employee relationship and perceived external prestige) factors are considerably and distinctly associated with employees' behavioral intentions on different social media platforms. Originality/value This study is among the first to understand employees' communicative behaviors on social media (sECB) by linking diverse levels of motivational factors: individual, interpersonal, and organizational using a theoretical framework of socioecological model (SEM). This study also provides significant practical guidelines for organizational leaders and platform operators by explicating the dynamics of employee motives in engaging in a variety of social media platforms.", "Keywords": "Employee behaviour;Social media", "DOI": "10.1108/INTR-06-2019-0264", "PubYear": 2020, "Volume": "30", "Issue": "3", "JournalId": 20482, "JournalTitle": "Internet Research", "ISSN": "1066-2243", "EISSN": "2054-5657", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Miami , Coral Gables, Florida, USA"}], "References": []}, {"ArticleId": 80292595, "Title": "Risk investment decisions within the deterministic equivalent income model", "Abstract": "<h3>Purpose</h3> <p>The purpose of this paper is to establish a deterministic equivalent income model (DEIM) based on the risk cost (RC) and risk aversion of investors. The model fully considers both subjective and objective factors that affect risk investment and reasonably evaluates risk investment schemes to choose the correct investment scheme and gain greater investment returns.</p> <h3>Design/methodology/approach</h3> <p>The utility function is used to measure the extent to which an investor is satisfied by investment returns in various scenarios. Risk aversion expresses subjective attitude of investors to risk. RC represents risk loss in currency. This methodology is based on risk aversion function, utility function and RC theory to establish DEIM.</p> <h3>Findings</h3> <p>This study shows that investors with different risk preferences have different certainty equivalent returns (CER), so their choices of investment options change accordingly.</p> <h3>Practical implications</h3> <p>In this paper, the authors use DEIM to test an investment case and conclude that the CER and investment scheme both change with different risk preferences. At the same time, case analysis shows that DEIM is reasonable and stable when evaluating risk investment schemes.</p> <h3>Originality/value</h3> <p>In this study, the authors innovate by introducing both the RC and risk aversion degree into risk investment schemes evaluation and by deriving a utility function from the absolute risk aversion function to build a utility decision matrix and establish DEIM. The model combines the subjective and objective factors that influence risk investment decisions.</p>", "Keywords": "Absolute risk aversion;Determined equivalent income model;Risk cost;Utility function", "DOI": "10.1108/K-04-2019-0275", "PubYear": 2021, "Volume": "50", "Issue": "2", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Management Science and Engineering, Shandong University of Finance and Economics , Jinan, China and Qilu Normal University , Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Management Science and Engineering, Shandong University of Finance and Economics , Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bank of Communications, Jinan, China"}, {"AuthorId": 4, "Name": "Xianpei Hong", "Affiliation": "School of Business Administration, Guangdong University of Finance and Economics , Guangzhou, China"}], "References": [{"Title": "Risk appetite dual hesitant fuzzy three-way decisions with TODIM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "585", "JournalTitle": "Information Sciences"}]}, {"ArticleId": ********, "Title": "ConceptGraph: A Formal Model for Interpretation and Reasoning During Visual Analysis", "Abstract": "<p>In order to discuss the kinds of reasoning a visualization supports and the conclusions that can be drawn within the analysis context, a theoretical framework is needed that enables a formal treatment of the reasoning process. Such a model needs to encompass three stages of the visualization pipeline: encoding, decoding and interpretation. The encoding details how data are transformed into a visualization and what can be seen in the visualization. The decoding explains how humans construct graphical contexts inside the depicted visualization and how they interpret them assigning meaning to displayed structures according to a formal reasoning strategy. In the presented model, we adapt and combine theories for the different steps into a unified formal framework such that the analysis process is modelled as an assignment of meaning to displayed structures according to a formal reasoning strategy. Additionally, we propose the ConceptGraph, a combined graph‐based representation of the finite‐state transducers resulting from the three stages, that can be used to formalize and understand the reasoning process. We apply the new model to several visualization types and investigate reasoning strategies for various tasks.</p>", "Keywords": "information visualization;visualization;scientific visualization;visual analytics;• Human‐centred computing → Visualization theory;concepts and paradigms;• Theory of computation → Automata extensions;Transducers;Theory and algorithms for application domains", "DOI": "10.1111/cgf.13899", "PubYear": 2020, "Volume": "39", "Issue": "6", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "TU Kaiserslautern, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "TU Kaiserslautern, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "TU Kaiserslautern, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "TU Kaiserslautern, Germany"}], "References": []}, {"ArticleId": 80292774, "Title": "Training bidirectional generative adversarial networks with hints", "Abstract": "The generative adversarial network (GAN) is composed of a generator and a discriminator where the generator is trained to transform random latent vectors to valid samples from a distribution and the discriminator is trained to separate such “fake” examples from true examples of the distribution, which in turn forces the generator to generate better fakes. The bidirectional GAN (BiGAN) also has an encoder working in the inverse direction of the generator to produce the latent space vector for a given example. This added encoder allows defining auxiliary reconstruction losses as hints for a better generator. On five widely-used data sets, we showed that BiGANs trained with the <PERSON><PERSON><PERSON> loss and augmented with hints learn better generators in terms of image generation quality and diversity, as measured numerically by the 1-nearest neighbor test, Fréchet inception distance, and reconstruction error, and qualitatively by visually analyzing the generated samples.", "Keywords": "Generative Modeling ; Generative Adversarial Networks ; Unsupervised Learning ; Autoencoders ; Neural Networks ; Deep Learning ; 00-01 ; 99-00", "DOI": "10.1016/j.patcog.2020.107320", "PubYear": 2020, "Volume": "103", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Boğaziçi University, TR-34342 İstanbul, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Boğaziçi University, TR-34342 İstanbul, Turkey"}], "References": [{"Title": "How Generative Adversarial Networks and Their Variants Work", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 80292799, "Title": "From the Editor-in-Chief", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10846-020-01161-9", "PubYear": 2020, "Volume": "99", "Issue": "2", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> School of Engineering and Computer Science, University of Denver, Denver, USA"}], "References": []}, {"ArticleId": 80292862, "Title": "Learning shape and motion representations for view invariant skeleton-based action recognition", "Abstract": "Skeleton-based action recognition is an increasing attentioned task that analyses spatial configuration and temporal dynamics of a human action from skeleton data, which has been widely applied in intelligent video surveillance and human-computer interaction. How to design an effective framework to learn discriminative spatial and temporal characteristics for skeleton-based action recognition is still a challenging problem. The shape and motion representations of skeleton sequences are the direct embodiment of spatial and temporal characteristics respectively, which can well address for human action description. In this work, we propose an original unified framework to learn comprehensive shape and motion representations from skeleton sequences by using Geometric Algebra. We firstly construct skeleton sequence space as a subset of Geometric Algebra to represent each skeleton sequence along both the spatial and temporal dimensions. Then rotor-based view transformation method is proposed to eliminate the effect of viewpoint variation, which remains the relative spatio-temporal relations among skeleton frames in a sequence. We also construct spatio-temporal view invariant model (STVIM) to collectively integrate spatial configuration and temporal dynamics of skeleton joints and bones. In STVIM, skeleton sequence shape and motion representations which mutually compensate are jointly learned to describe skeleton-based actions comprehensively. Furthermore, a selected multi-stream Convolutional Neural Network is employed to extract and fuse deep features from mapping images of the learned representations for skeleton-based action recognition. Experimental results on NTU RGB+D, Northwestern-UCLA and UTD-MHAD datasets consistently verify the effectiveness of our proposed method and the superior performance over state-of-the-art competitors.", "Keywords": "Human action recognition ; Skeleton sequence ; Representation learning ; View invariant ; Geometric Algebra", "DOI": "10.1016/j.patcog.2020.107293", "PubYear": 2020, "Volume": "103", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ATR National Key Laboratory of Defense Technology, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ATR National Key Laboratory of Defense Technology, Shenzhen University, Shenzhen 518060, China;@email.szu.edu.cn"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "ATR National Key Laboratory of Defense Technology, Shenzhen University, Shenzhen 518060, China"}], "References": [{"Title": "Cluster-wise learning network for multi-person pose estimation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107074", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 80292869, "Title": "Joint image deblurring and matching with feature-based sparse representation prior", "Abstract": "Image matching aims to find a similar area of the small image in the large image, which is one of the key steps in image fusion and vision-based navigation; however, most matching methods perform poorly when the images to be matched are blurred. Traditional approaches for blurred image matching usually follow a two-stage framework - first resorting to image deblurring and then performing image matching with the recovered image. However, the matching accuracy of these methods often suffers greatly from the deficiency of image deblurring. Recently, a joint image deblurring and matching method that utilizes the sparse representation prior to exploit the correlation between deblurring and matching was proposed to address this problem and found to obtain a higher matching accuracy. Yet, that technique is not efficient when the image is seriously blurred, and the method’s time complexity is excessive. In this paper, we propose a joint image deblurring and matching approach with a feature-based sparse representation prior. Our approach utilizes two-directional two-dimensional (2 D )<sup>2</sup>PCA to extract feature vectors from images and obtains a sparse representation prior in a robust feature space rather than the original pixel space, thus mitigating the influence of image blur. Moreover, the reduction in the feature dimension can also increase the computational efficiency. Extensive experiments show that our approach significantly outperforms state-of-the-art approaches in terms of both accuracy and speed.", "Keywords": "Blurred image matching ; Joint image deblurring and matching ; Sparse representation priorsparse ; (2 D )<sup>2</sup>PCA feature", "DOI": "10.1016/j.patcog.2020.107300", "PubYear": 2020, "Volume": "103", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "Juncai Peng", "Affiliation": "National Key Laboratory of Science and Technology on Multispectral Information Processing, School of Artificial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan 430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory of Science and Technology on Multispectral Information Processing, School of Artificial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan 430074, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Laboratory of Science and Technology on Multispectral Information Processing, School of Artificial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan 430074, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Laboratory of Science and Technology on Multispectral Information Processing, School of Artificial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan 430074, China"}], "References": []}, {"ArticleId": 80293095, "Title": "Effects of cue congruence and perceived cue authenticity in online group buying", "Abstract": "Purpose The study investigates how the congruence of online deal popularity and star rating influences service quality expectation in online group buying (OGB) websites. It also investigates the role of authenticity perceptions of online cues. Design/methodology/approach Two experiments are used to assess the effects of congruence between deal popularity and star rating on service quality expectation for service deals in an OGB website. Findings The findings suggest that a combination of congruently high deal popularity and high star rating has a stronger effect on expected service quality than a combination of congruently low cues. The findings further suggest that expected service quality is greater under the combination of high deal popularity and low star rating than the combination of low deal popularity and high star rating, showing the differences between incongruent cue combinations. The findings also show the moderating effect of consumer authenticity perceptions of cues on the expected service quality. Originality/value The novel contribution of the study is to extend cue congruence theory to explain how congruent online information cues and the consumers' authenticity perceptions of the cues influence consumers' judgment of online deals. The contribution is validated empirically in the context of OGB. The findings advance current knowledge concerning how consumers use online information cues.", "Keywords": "Deal popularity;Star rating;Cue congruence;Perceived authenticity;Online group buying", "DOI": "10.1108/INTR-11-2018-0477", "PubYear": 2020, "Volume": "30", "Issue": "3", "JournalId": 20482, "JournalTitle": "Internet Research", "ISSN": "1066-2243", "EISSN": "2054-5657", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Adelaide Business School , University of Adelaide , Adelaide, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Adelaide Business School , University of Adelaide , Adelaide, Australia"}, {"AuthorId": 3, "Name": "Indrit <PERSON>", "Affiliation": "Adelaide Business School , University of Adelaide , Adelaide, Australia"}], "References": []}, {"ArticleId": 80293238, "Title": "DATimeS: A machine learning time series GUI toolbox for gap-filling and vegetation phenology trends detection", "Abstract": "Optical remotely sensed data are typically discontinuous, with missing values due to cloud cover. Consequently, gap-filling solutions are needed for accurate crop phenology characterization. The here presented Decomposition and Analysis of Time Series software (DATimeS) expands established time series interpolation methods with a diversity of advanced machine learning fitting algorithms (e.g., Gaussian Process Regression: GPR) particularly effective for the reconstruction of multiple-seasons vegetation temporal patterns. DATimeS is freely available as a powerful image time series software that generates cloud-free composite maps and captures seasonal vegetation dynamics from regular or irregular satellite time series. This work describes the main features of DATimeS, and provides a demonstration case using Sentinel-2 Leaf Area Index time series data over a Spanish site. GPR resulted as an optimum fitting algorithm with most accurate gap-filling performance and associated uncertainties. DATimeS further quantified LAI fluctuations among multiple crop seasons and provided phenological indicators for specific crop types.", "Keywords": "Gap-filling ; Machine learning ; Vegetation phenology ; Remote sensing", "DOI": "10.1016/j.envsoft.2020.104666", "PubYear": 2020, "Volume": "127", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "Santiago Belda", "Affiliation": "Image Processing Laboratory (IPL), University of Valencia, C/Catedrático <PERSON> 2, 46980, Paterna, Valencia, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Image Processing Laboratory (IPL), University of Valencia, C/Catedrático <PERSON> 2, 46980, Paterna, Valencia, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Image Processing Laboratory (IPL), University of Valencia, C/Catedrático <PERSON> 2, 46980, Paterna, Valencia, Spain"}, {"AuthorId": 4, "Name": "<PERSON>Caicedo", "Affiliation": "CONACYT-U<PERSON>, Secretariat of Research and Postgraduate, C/3, 63173, Tepic, Mexico"}, {"AuthorId": 5, "Name": "<PERSON>idal Amin", "Affiliation": "Image Processing Laboratory (IPL), University of Valencia, C/Catedrático <PERSON> 2, 46980, Paterna, Valencia, Spain"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Image Processing Laboratory (IPL), University of Valencia, C/Catedrático <PERSON> 2, 46980, Paterna, Valencia, Spain"}, {"AuthorId": 7, "Name": "Jochem Verrelst", "Affiliation": "Image Processing Laboratory (IPL), University of Valencia, C/Catedrático <PERSON> 2, 46980, Paterna, Valencia, Spain"}], "References": [{"Title": "A review of vegetation phenological metrics extraction using time-series, multispectral satellite data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111511", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Visualizing and labeling dense multi-sensor earth observation time series: The EO Time Series Viewer", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "125", "Issue": "", "Page": "104631", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 80293366, "Title": "Intelligent Nanoarchitectonics for Self‐Assembling Systems", "Abstract": "<p>For the sustainable developments of life and society, various problems such as environmental, energy, and biohealth issues must be solved by a wide range of scientific and technical efforts. Therefore, the fabrication of functional materials and systems with strategic intelligence is required. As seen in the evolution processes in nature, self‐assembling processes are capable of creating highly intelligent materials and systems. This task would be taken by an emerging concept, nanoarchitectonics, through the combination of nanotechnology concepts with other scientific disciplines such as materials science, supramolecular chemistry, organic chemistry, and bio‐related science and technology. Herein, several examples are presented to overview intelligent nanoarchitectonics for the creation of functional materials and systems mainly through self‐assembly in various scale ranges. These examples are classified into several sections according to atom‐level, molecular‐level, materials‐level, and life‐level intelligent assembly, where several key items such as atom switch devices, molecular switches, molecular machines, shape‐shifting and shape‐specified assemblies, and cell control at interfaces are included. Discussions on these examples show a high possibility of the nanoarchitectonics' approach in intelligent fabrication of functional materials.</p>", "Keywords": "cell controls;interfaces;molecular machines;nanoarchitectonics;\nself-assemblies", "DOI": "10.1002/aisy.201900157", "PubYear": 2020, "Volume": "2", "Issue": "4", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "World Premier International (WPI) Research Center for Materials Nanoarchitectonics (MANA), National Institute for Materials Science (NIMS), 1-1 Namiki, Tsukuba, 305-0044 Japan; Department of Advanced Materials Science, Graduate School of Frontier Sciences, The University of Tokyo, 5-1-5 Kashiwanoha, Kashiwa, Chiba, 277-8561 Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "World Premier International (WPI) Research Center for Materials Nanoarchitectonics (MANA), National Institute for Materials Science (NIMS), 1-1 Namiki, Tsukuba, 305-0044 Japan"}], "References": []}, {"ArticleId": 80293375, "Title": "Dataset of allele, genotype and haplotype frequencies of four polymorphisms filaggrin gene in Russian patients with atopic dermatitis", "Abstract": "<p>Data on the allele, genotype and haplotype frequencies of four single nucleotide polymorphisms (SNPs) (rs3126085, rs12144049, rs471144 and rs4363385) <i>filaggrin</i> (<i>FLG</i>) gene in Russian patients with atopic dermatitis are presented. Genome-wide association studies identified these SNPs could be significant genetic markers associated with atopic dermatitis. The frequencies of alleles, genotypes and haplotypes of four SNPs were calculated in 3 groups: entire sample, females and males. No significant differences in the allele, genotype and haplotype frequencies between males and females with AD patients were observed.</p><p>© 2020 Published by Elsevier Inc.</p>", "Keywords": "Atopic dermatitis;FLG;Female;Male;Single nucleotide polymorphism", "DOI": "10.1016/j.dib.2020.105307", "PubYear": 2020, "Volume": "29", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Medical Biological Disciplines, Belgorod State University, 308015, Belgorod, Russia."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medical Biological Disciplines, Belgorod State University, 308015, Belgorod, Russia."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Medical Biological Disciplines, Belgorod State University, 308015, Belgorod, Russia."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biology, Medical Genetics and Ecology, Kursk State Medical University, 305041, Kursk, Russia."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medical Biological Disciplines, Belgorod State University, 308015, Belgorod, Russia."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Medical Biological Disciplines, Belgorod State University, 308015, Belgorod, Russia."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medical Biological Disciplines, Belgorod State University, 308015, Belgorod, Russia."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Medical Biological Disciplines, Belgorod State University, 308015, Belgorod, Russia."}], "References": []}, {"ArticleId": 80293493, "Title": "Textual data summarization using the Self-Organized Co-Clustering model", "Abstract": "Recently, different studies have demonstrated the use of co-clustering, a data mining technique which simultaneously produces row-clusters of observations and column-clusters of features. The present work introduces a novel co-clustering model to easily summarize textual data in a document-term format. In addition to highlighting homogeneous co-clusters as other existing algorithms do we also distinguish noisy co-clusters from significant co-clusters, which is particularly useful for sparse document-term matrices. Furthermore, our model proposes a structure among the significant co-clusters, thus providing improved interpretability to users. The approach proposed contends with state-of-the-art methods for document and term clustering and offers user-friendly results. The model relies on the Poisson distribution and on a constrained version of the Latent Block Model, which is a probabilistic approach for co-clustering. A Stochastic Expectation-Maximization algorithm is proposed to run the model’s inference as well as a model selection criterion to choose the number of co-clusters. Both simulated and real data sets illustrate the efficiency of this model by its ability to easily identify relevant co-clusters.", "Keywords": "Co-Clustering ; Document-term matrix ; Latent block model", "DOI": "10.1016/j.patcog.2020.107315", "PubYear": 2020, "Volume": "103", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Université de Lyon, Lyon 2 & ERIC EA3083, 5 Avenue Pierre Mendès France, 69500 Bron, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Université de Lyon, Lyon 2 & ERIC EA3083, 5 Avenue Pierre Mendès France, 69500 Bron, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Université de Lille - UFR de Mathématiques - Cité Scientifique - 59655 Villeneuve d’Ascq Cedex, France;INRIA, 40, av. Halley - Bât A - Park Plaza 59650 Villeneuve d’Ascq"}], "References": [{"Title": "Model-based co-clustering for mixed type data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "106866", "JournalTitle": "Computational Statistics & Data Analysis"}]}, {"ArticleId": 80293522, "Title": "Secreted microRNA data from the parasitic filarial nematode Acanthocheilonema viteae", "Abstract": "<p>microRNAs (miRNAs) are an abundant class of non-coding RNA species with important regulatory roles in gene expression at the posttranscriptional level. The helminth <i>Acanthocheilonema viteae</i> serves as model organism for research on parasitic filarial nematodes. Total RNA secreted or excreted <i>in vitro</i> by 1500 adult female and male <i>A. viteae</i> over 3 weeks was isolated from culture media previously processed by differential ultracentrifugation. miRNA sequencing revealed the presence of 360 unique miRNA candidates released by adult <i>A. viteae in vitro</i>. Among them, 74 high-confidence unique miRNAs<i>,</i> as well as several potential novel miRNA candidates were discovered. A large proportion of the sequenced miRNA candidates appeared differentially expressed between the male and female samples based on normalized copy count. The presence of extracellular vesicles, often rich in miRNAs, could not be confirmed unambiguously by transmission electron microscopy.</p><p>© 2020 The Author(s).</p>", "Keywords": "Excretory-secretory;Extracellular vesicle;Filarial nematode;Parasite;microRNA", "DOI": "10.1016/j.dib.2020.105334", "PubYear": 2020, "Volume": "29", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Parasitology, University of Zurich, Winterthurerstrasse 266a, CH-8057, Zurich, Switzerland."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Swiss Tropical and Public Health Institute, Socinstrasse 57, CH-4051, Basel, Switzerland."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Swiss Tropical and Public Health Institute, Socinstrasse 57, CH-4051, Basel, Switzerland."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Parasitology, University of Zurich, Winterthurerstrasse 266a, CH-8057, Zurich, Switzerland."}], "References": []}, {"ArticleId": 80293523, "Title": "WSEmail", "Abstract": "<p>Web services offer an opportunity to redesign a variety of older systems to exploit the advantages of a flexible, extensible, secure set of standards. In this work, we revisit WSEmail, a system proposed over 10 years ago to improve email by redesigning it as a family of web services. WSEmail offers an alternative vision of how instant messaging and email services could have evolved, offering security, extensibility, and openness in a distributed environment instead of the hardened walled gardens that today’s rich messaging systems have become. WSEmail’s architecture, especially its automatic plug-in download feature, allows for rich extensions without changing the base protocol or libraries. We demonstrate WSEmail’s flexibility using three business use cases: secure channel instant messaging, business workflows with routed forms, and on-demand attachments. Since increased flexibility often mitigates against security and performance, we designed WSEmail with security in mind and formally proved the security of one of its core protocols (on-demand attachments) using the TulaFale and ProVerif automated proof tools. We provide performance measurements for WSEmail functions in a prototype we implemented using .NET. Our experiments show a latency of about a quarter of a second per transaction under load.</p>", "Keywords": "Electronic mail; Web services; Security; On-demand attachments; Rich messaging; Email workflow", "DOI": "10.1007/s11761-019-00283-9", "PubYear": 2020, "Volume": "14", "Issue": "1", "JournalId": 4313, "JournalTitle": "Service Oriented Computing and Applications", "ISSN": "1863-2386", "EISSN": "1863-2394", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Kinneret Academic College, Zemach Regional Center, Jordan Valley, Israel"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Pennsylvania Philadelphia, Pennsylvania, USA;Rowan University, Glassboro, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Illinois at Urbana-Champaign, Urbana Illinois, USA"}], "References": []}, {"ArticleId": 80293526, "Title": "Current and future of technologies and services in smart e-learning", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11761-020-00288-9", "PubYear": 2020, "Volume": "14", "Issue": "1", "JournalId": 4313, "JournalTitle": "Service Oriented Computing and Applications", "ISSN": "1863-2386", "EISSN": "1863-2394", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Engineering Lab of Big Data Analytics, Faculty of Electronic and Information Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "MOE Key Lab of INNS, School of Computer Science and Technology, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Computing, Coventry University, Coventry, UK"}], "References": []}, {"ArticleId": ********, "Title": "Study on the algorithm for smart community sensor network routing with adaptive optimization via cluster head election", "Abstract": "<p>To reduce the uneven energy consumption for the data transmission and extend network life of intelligent community sensor network, an adaptive routing optimized algorithm for intelligent community sensor networks with cluster head election is proposed. In this algorithm, a three‐dimensional clustering method adapted to the structure of intelligent community sensor network is proposed. The three‐dimensional clustering method uses the cluster head election mechanism based on minimizing the total transmission loss to optimize the energy of the intelligent community sensor network. Second, an adaptive ant colony propagation method is proposed to solve the problem of intercluster data propagation after clustering. With the best path finding algorithm of ant colony algorithm, energy balance routing with lower energy loss and lower packet error rate is proposed. Finally, the simulation results show that the algorithm has better performance in reducing energy consumption and delay, improving transmission efficiency and node survival time.</p>", "Keywords": "adaptive ant colony;intelligent community;network life;sensor networks;three‐dimensional clustering routing", "DOI": "10.1111/coin.12304", "PubYear": 2020, "Volume": "36", "Issue": "4", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Computer, Henan University of Engineering, Zhengzhou, China; Henan IoT Engineering Research Center of Smart Building, Zhengzhou, China"}], "References": []}, {"ArticleId": ********, "Title": "A 2020 perspective on “From buzz to bucks: The impact of social media opinions on the locus of innovation”: From surfaces to essences", "Abstract": "Social media is believed as a privileged vehicle affording numerous ideas with multi-faceted insights from the crowds. In addition to the entertainment resource, social media, user-generated content (UGC) there, can also empower ideation and commercialization of innovation. We have proposed a novel approach to explore the pivotal role of social media in shaping corporation innovation performance in our article in 2018. In this commentary, we extend the literature review with more recent insights and identify the new possible directions to further inspire future research. Specifically, understanding the linkage between social media and organization innovation can be enriched via advancing deep learning methods, improving multi-modal data analysis, and fostering digital innovation.", "Keywords": "Social media ; Organization innovation ; Machine learning ; Text mining ; Empirical analysis", "DOI": "10.1016/j.elerap.2020.100964", "PubYear": 2020, "Volume": "40", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information, Renmin University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Management, Nanjing University, Nanjing, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Digitalization, Copenhagen Business School, Copenhagen, Denmark"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Management, Nanjing University, Nanjing, China"}], "References": []}, {"ArticleId": 80293837, "Title": "Convergence of the non-uniform Physarum dynamics", "Abstract": "The Physarum computing model is an analog computing model motivated by the network dynamics of the slime mold Physarum Polycephalum. In previous works, it was shown that it can solve a class of linear programs. We extend these results to a more general dynamics motivated by situations where the slime mold operates in a non-uniform environment. Let c ∈ Z > 0 m , A ∈ Z n × m , and b ∈ Z n . We show under fairly general conditions that the non-uniform Physarum dynamics x ˙ e = a e ( x , t ) (   q e   − x e ) converges to the optimum solution x ⁎ of the weighted basis pursuit problem minimize c T x subject to A f = b and   f   ≤ x . Here, f and x are m -dimensional vectors of real variables, q minimizes the energy ∑ e ( c e / x e ) q e 2 subject to the constraints A q = b and supp ( q ) ⊆ supp ( x ) , and a e ( x , t ) > 0 is the reactivity of edge e to the difference   q e   − x e at time t and in state x . Previously convergence was only shown for the uniform case a e ( x , t ) = 1 for all e , x , and t . We also show convergence for the dynamics x ˙ e = x e ( g e (   q e   x e ) − 1 ) , where each g e is an increasing differentiable function with g e ( 1 ) = 1 (satisfying some mild conditions). Previously, convergence was only shown for the special case of the shortest path problem on a graph consisting of two nodes connected by parallel edges.", "Keywords": "Physarum dynamics ; Convergence", "DOI": "10.1016/j.tcs.2020.02.032", "PubYear": 2020, "Volume": "816", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Max <PERSON>ck Institute for Informatics, Saarland Informatics Campus, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Max <PERSON>ck Institute for Informatics, Saarland Informatics Campus, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Max <PERSON>ck Institute for Informatics, Saarland Informatics Campus, Germany;Corresponding author"}], "References": [{"Title": "Convergence of the non-uniform directed Physarum model", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "816", "Issue": "", "Page": "184", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 80293852, "Title": "Reliability analysis of subsystem in dual cubes", "Abstract": "The probability of failing processors in the multiprocessor system increases as the cardinality of system grows. The subsystem reliability in a system, defined as the probability that a subsystem of a specified cardinality is operational with the emergence of failed nodes. In this paper, we derive an approximation and an upper bound on the probability of a subgraph D n − 1 being fault-free under the probabilistic fault model. Numerical simulations indicate that these two analytical results we get are in good consistency, especially when the node reliability is at a low level.", "Keywords": "Dual cube ; Subsystem reliability ; Principle of inclusion-exclusion ; Probabilistic fault model", "DOI": "10.1016/j.tcs.2020.02.028", "PubYear": 2020, "Volume": "816", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Jimei University, Xiamen, Fujian 361021, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Jimei University, Xiamen, Fujian 361021, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Computer Science, Fujian Normal University, Fuzhou, Fujian 350007, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Taiyuan University of Technology, Taiyuan, Shanxi 030024, PR China"}], "References": []}, {"ArticleId": 80293883, "Title": "Similarity measure with indeterminate parameters regarding cubic hesitant neutrosophic numbers and its risk grade assessment approach for prostate cancer patients", "Abstract": "<p>The prostate cancer (PC) of older men has become an important problem with the increment of aging degree in current society. Then, it is necessary to give the timely detection and reasonable risk assessment of a PC patient so as to provide a suitable treatment option for the PC patient. However, there may exist the hybrid information of an interval-valued fuzzy number (IVFN) and a hesitant indeterminate number/hesitant neutrosophic number (HNN) together in risk data of PC patients, while existing PC risk grade assessment approaches with the cubic hesitant fuzzy set (CHFS) composed of an IVFN and a hesitant fuzzy set cannot carry out such a risk grade assessment problem with the hybrid information of both IVFN and HNN. To solve the issue, for the first time this study proposes a cubic hesitant neutrosophic number (CHNN) to express the hybrid information of both IVFN and HNN and then introduces a similarity measure with an indeterminate parameter regarding CHNNs for the risk grade assessment of PC patients. In this paper, therefore, a concept of a CHNN set is firstly presented to suitably express the hybrid information of both IVFN and HNN as the generalization of CHFS. Then, the CHNN set is transformed into the parameterized CHFS (P-CHFS) for de-neutrosophication by means of an indeterminate parameter. Next, generalized distance and similarity measures between P-CHFSs are proposed based on the least common multiple cardinality/number (LCMC) extension method. Thus, a PC risk assessment approach is developed by using the similarity measure of P-CHFSs with the physician’s optimistic, moderate and pessimistic attitudes under CHNN environment. Finally, sixteen clinical cases are provided as risk assessment examples of PC patients to indicate the effectiveness and applicability of the proposed PC risk assessment approach in CHNN setting. However, the proposed assessment approach can effectively and flexibly deal with assessment problems of PC risk grades regarding the physicians’ optimistic, moderate and pessimistic attitudes in CHNN setting, which shows its advantage.</p>", "Keywords": "Prostate cancer; Risk grade assessment; Cubic hesitant neutrosophic number; Parameterized cubic hesitant fuzzy set; Generalized distance; Similarity measure", "DOI": "10.1007/s10489-020-01653-z", "PubYear": 2020, "Volume": "50", "Issue": "7", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Shaoxing Second Hospital, Shaoxing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical engineering and Automation, Shaoxing University, Shaoxing, People’s Republic of China"}], "References": [{"Title": "Group decision making based on power Heronian aggregation operators under neutrosophic cubic environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "1971", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 80293992, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0925-2312(20)30273-3", "PubYear": 2020, "Volume": "385", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [], "References": []}, {"ArticleId": 80294356, "Title": "Information spreading in a population modeled by continuous asynchronous probabilistic cellular automata", "Abstract": "In this paper, we propose a model for information propagation in a population based on cellular automata. Different to what is commonly used in the models of the area, instead of a binary level for the information, individuals have a level of knowledge. Moreover, the population can have a marketing campaign to help to spread the information with a certain limit due to the consideration of marketing rejection in the model. Numerical simulations show that these campaigns must be wisely set in order to not saturate the population and decrease the marketing balance due to the rejection. The simulation data is statistically analyzed by using principal component analysis in order to identify the most relevant variables for the model output. The conclusion is that dealing with this rejection is difficult, as well as choosing the percentage of the population which will receive the marketing, and along with the weight for the word-of-mouth were the most important variables of the model according to the simulations and the principal component analysis.", "Keywords": "Continuous asynchronous probabilistic cellular automata ; Information spreading ; Marketing campaign model ; Principal component analysis ; Rumor propagation ; Viral marketing", "DOI": "10.1016/j.comcom.2020.02.074", "PubYear": 2020, "Volume": "154", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Informatics and Knowledge Management Graduate Program, Universidade Nove de Julho, Rua <PERSON>, 235/249, CEP: 05001-001, São Paulo, SP, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Informatics and Knowledge Management Graduate Program, Universidade Nove de Julho, Rua <PERSON>, 235/249, CEP: 05001-001, São Paulo, SP, Brazil;Industrial Engineering Graduate Program, Universidade Nove de Julho, Rua Vergueiro, 235/249, CEP: 05001-001, São Paulo, SP, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Informatics and Knowledge Management Graduate Program, Universidade Nove de Julho, R<PERSON>, 235/249, CEP: 05001-001, São Paulo, SP, Brazil;Corresponding author"}], "References": []}, {"ArticleId": 80294367, "Title": "Enhancing MOEA/D with information feedback models for large-scale many-objective optimization", "Abstract": "A multi-objective evolutionary algorithm based on decomposition (MOEA/D) is a classic decomposition-based multi-objective optimization algorithm. In the standard MOEA/D algorithm, the update process of individuals is a forward search process without using the information of previous individuals. However, there is a lot of useful information in the previous iteration. Information Feedback Models (IFM) is a new strategy which can incorporate the information from previous iteration into the updating process. Therefore, this paper proposes a MOEA/D algorithm based on information feedback model, called MOEA/D-IFM. According to the different information feedback models, this paper proposes six variants of MOEA/D, and these algorithms can be divided into two categories according to the way of selecting individuals whether it is random or fixed. At the same time, a new selection strategy has been introduced to further improve the performance of MOEA/D-IFM. The experiments were carried out in four aspects. MOEA/D-IFM were compared with other state-of-the-art multi-objective evolutionary algorithms using CEC 2018 problems in two aspects. The best one of the six improved algorithms was chosen to test on large-scale many-objective problems. In addition, we also use MOEA/D-IFM to solve multi-objective backpack problems.", "Keywords": "Benchmark ; Decomposition ; Evolutionary algorithms ; Information feedback models ; Many-objective ; Multi-objective 0-1 knapsack problem", "DOI": "10.1016/j.ins.2020.02.066", "PubYear": 2020, "Volume": "522", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Ocean University of China, 266100 Qingdao, China;Department of Computer Science and Technology, Ocean University of China, 266100 Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Ocean University of China, 266100 Qingdao, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, State University of New York, New Paltz, New York, 12561, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Engineering Management, National Tsing Hua University, Hsinchu, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University of Finance and Economics, Jinan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Ocean University of China, 266100 Qingdao, China;Department of Computer Science and Technology, Ocean University of China, 266100 Qingdao, China"}], "References": [{"Title": "Behavior of crossover operators in NSGA-III for large-scale optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "470", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 80294403, "Title": "DNN-based Speech Synthesis using Dialogue-Act Information and Its Evaluation with Respect to Illocutionary Act Naturalness", "Abstract": "<p class=\"global-para-14\"> <p>This paper aims at improving naturalness of synthesized speech generated by a text-to-speech (TTS) systemwithin a spoken dialogue system with respect to “how natural the system’s intention is perceived via the synthesizedspeech”. We call this measure “illocutionary act naturalness” in this paper. To achieve this aim, we propose toutilize dialogue-act (DA) information as an auxiliary feature for a deep neural network (DNN)-based speech synthesissystem. First, we construct a speech database with DA tags. Second, we build the proposed DNN-based speechsynthesis system based on the database. Then, we evaluate the proposed method by comparing its performance withtwo conventional hidden Markov model (HMM)-based speech synthesis systems, namely, the style-mixed modelingmethod and the style adaptation method. The objective evaluation results show that the proposed method overwhelmsthe style-mixed modeling method in the accuracy of reproduction of global prosodic characteristics of dialogue-acts.They also reveal that the proposed method overwhelms the style adaptation method in the accuracy of reproduction of sentence final tone characteristics of dialogue-acts. The subjective evaluation results also show that the proposed method improves the illocutionary act naturalness compared with the two conventional methods.</p> </p>", "Keywords": "text-to-speech;spoken dialogue system;dialogue-act;illocutionary act naturalness", "DOI": "10.1527/tjsai.A-J81", "PubYear": 2020, "Volume": "35", "Issue": "2", "JournalId": 8299, "JournalTitle": "Transactions of the Japanese Society for Artificial Intelligence", "ISSN": "1346-0714", "EISSN": "1346-8030", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "NTT Communication Science Laboratories"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NTT Media Intelligence Laboratories"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NTT Communication Science Laboratories"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NTT Communication Science Laboratories"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "NTT Communication Science Laboratories"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "NTT Communication Science Laboratories"}], "References": []}, {"ArticleId": 80294488, "Title": "Game AI General Theory and its Implementation in AAA Digital Game", "Abstract": "<p class=\"global-para-14\"> <p>A game AI general theory has been researched and developed in game industry in the world, and a new game AI general theory of AI in digital game is supposed with three AIs. For a large scale of game, a game AI system consists of three types of AI such as meta-AI, character AI, and navigation AI. A meta-AI is to control a game dynamically from a bird-view by watching a player’s behavior. A character AI is a brain of a game character such as a buddy, a monster, or a villager to make a decision in real-time. A navigation AI is to recognize an environment of a game to find a path or a best location to move dynamically. Especially, character AI is a main topic to study in game development, and it includes many fields such as multi-layered structure, character animation, agent architecture, decision-making modules, and so on. A new method of decision-making of combination of behavior trees and state machines is supposed. It is called AI Graph. The game AI general theory was applied to an AI system of an action-RPG game “FINAL FANTASY XV”. The results are showed in the paper. All characters’ decision-making system in FINAL FANTASY XV are based on AI Graphs. An AI Graph Editor is a tool to make an AI Graph only by using a mouse and simple text inputs. A dynamics of the new method is showed by explaining AI Graph Editor precisely.</p> </p>", "Keywords": "game AI;behavior tree;state machine;navigation AI;meta-AI;character AI;decision-making;AI Graph", "DOI": "10.1527/tjsai.B-J64", "PubYear": 2020, "Volume": "35", "Issue": "2", "JournalId": 8299, "JournalTitle": "Transactions of the Japanese Society for Artificial Intelligence", "ISSN": "1346-0714", "EISSN": "1346-8030", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SQUARE ENIX CO., LTD."}], "References": []}, {"ArticleId": 80294502, "Title": "Sub-Pixel Edge Detection Algorithm Based on Canny–Zernike Moment Method", "Abstract": "In order to solve the problems of low efficiency and long running time caused by the traditional Zernike moment method for convolution calculation of the whole image, this paper combines the canny detection algorithm with the Zernike moment method. First, the canny edge detection algorithm, which combined with the Otsu threshold method, is used to extract the pixel edge of the image. Then an improved Hough transform method is used to fit the geometric edge in the image. Based on this, the Zernike moment method is applied to realize sub-pixel positioning of images. The algorithm improves the deficiencies of direct sub-pixel detection, improving accuracy and reducing running time. To verify the effectiveness of the proposed algorithm, the algorithm is applied to the dimension measurement experiment of T-type guide way. The results clearly show that the algorithm is superior to the traditional algorithm in accuracy.", "Keywords": "", "DOI": "10.1142/S0218126620502382", "PubYear": 2020, "Volume": "29", "Issue": "15", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80294522, "Title": "Improved Whale Optimized MLP Neural Network-Based Learning Mechanism for Multiuser Detection in MIMO Communication System", "Abstract": "<p>Direct-Sequence Code Division Multiple Access (DS-CDMA) is a digital method to spread spectrum modulation for digital signal transmission. We propose to detect signal in DS-CDMA communication using the learning mechanism. Initially, the user signals are spread using the respective pseudo-noise (PN) code where the input signal is multiplied with the code which is then modulated using the quadrature phase shift keying (QPSK) modulator. The modulated signal is then transmitted in a 3G/4G channel considering all types of fading. The transmitted signal is received by the antenna array which is performed by demodulation. We propose to adaptively assign the weights by employing Improved Whale Optimized Multi-Layer Perceptron Neural Network (IWMLP-NN)-based learning mechanism. To design IWMLP-NN, Improved Whale Optimization Algorithm is combined with multilayer perceptron neural network. This is used instead of the normal Multiple Signal Classification (MUSIC) and least mean squares (LMS)/root-mean-square (RMS) algorithms used in beam-forming networks. After assigning weight through IWMLP-NN-based learning mechanism, we de-spread to get the original user data. We have compared our proposed technique with the normal techniques with the help of plots of Bit Error Rate (BER) versus Signal-to-Noise Ratio (SNR). We use both the AWGN channel and fading channel for analysis. Experimental results prove that our proposed method achieves better BER performance results even with deep fading.</p>", "Keywords": "", "DOI": "10.1142/S0218126620502394", "PubYear": 2020, "Volume": "29", "Issue": "15", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation, SRM Valliammai Engineering College, Kattankulathur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation, SRM Valliammai Engineering College, Kattankulathur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, College of Engineering, Anna University, Chennai, India"}], "References": [{"Title": "Short-term natural gas consumption prediction based on Volterra adaptive filter and improved whale optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103323", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 80294545, "Title": "An Area-Power Efficient Denoising Hardware Architecture for Real EOG Signal", "Abstract": "This paper presents an optimized noise reduction hardware architecture for real electrooculogram (EOG) system. The proposed denoise architecture is developed using differential evolution (DE) algorithm. The algorithm design the filter with fewer sign-power-of-two (SPT) terms to optimize the denoise filter hardware with desired frequency response. The proposed denoise filter architecture with a DE coefficient set uses shift and add approach and is implemented in gate-level Verilog HDL. The real EOG denoise filter’s functionality is checked with Altera DSP Builder and synthesized using Cadence RTL compiler. Both FPGA and ASIC synthesis results are compared with the recently published works. The area and power consumption results show that the proposed filter occupies less area and with low power consumption as compared to the existing architectures.", "Keywords": "", "DOI": "10.1142/S0218126620502436", "PubYear": 2020, "Volume": "29", "Issue": "15", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, V R Sid<PERSON>rtha Engineering College, Vijayawada 520007, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, JNTUK University College of Engineering, Kakinada 522603, India"}], "References": []}, {"ArticleId": 80294575, "Title": "A New Secure and Efficient Approach for TRNG and Its Post-Processing Algorithms", "Abstract": "<p>Random numbers are important parameters for the security of cryptographic applications. In this study, a secure and efficient generator is proposed to generate random numbers. The first part of the generator is a true random number generator that consists of chaotic systems implemented on FPGA. The second part of the generator is a post-processing algorithm used to overcome the problems that emerge from the generator or environmental factors. As the post-processing algorithm, <PERSON><PERSON><PERSON>, the latest standard of hash algorithm, was rearranged and used. Random numbers with the proposed approach meet the security requirements for cryptographic applications. Furthermore, the NIST 800-22 test suite and autocorrelation test are used to ensure the generated numbers have no statistical weakness. The successful test results demonstrate the security of the generated numbers. An important advantage of the proposed generator does not cause any data loss and perform 100% efficiency although data loss can be up to 70% in some post-processing algorithms.</p>", "Keywords": "", "DOI": "10.1142/S0218126620502448", "PubYear": 2020, "Volume": "29", "Issue": "15", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Malatya Turgut Özal University, Malatya Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Malatya Turgut Özal University, Malatya Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Malatya Turgut Özal University, Malatya Turkey"}], "References": []}, {"ArticleId": 80294909, "Title": "A survey on security challenges in cloud computing: issues, threats, and solutions", "Abstract": "<p>Cloud computing has gained huge attention over the past decades because of continuously increasing demands. There are several advantages to organizations moving toward cloud-based data storage solutions. These include simplified IT infrastructure and management, remote access from effectively anywhere in the world with a stable Internet connection and the cost efficiencies that cloud computing can bring. The associated security and privacy challenges in cloud require further exploration. Researchers from academia, industry, and standards organizations have provided potential solutions to these challenges in the previously published studies. The narrative review presented in this survey provides cloud security issues and requirements, identified threats, and known vulnerabilities. In fact, this work aims to analyze the different components of cloud computing as well as present security and privacy problems that these systems face. Moreover, this work presents new classification of recent security solutions that exist in this area. Additionally, this survey introduced various types of security threats which are threatening cloud computing services and also discussed open issues and propose future directions. This paper will focus and explore a detailed knowledge about the security challenges that are faced by cloud entities such as cloud service provider, the data owner, and cloud user.</p>", "Keywords": "Cloud computing; Security; Threats; Vulnerabilities; Data protection", "DOI": "10.1007/s11227-020-03213-1", "PubYear": 2020, "Volume": "76", "Issue": "12", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Mathematics and Computer, Shahid Bahonar University of Kerman, Kerman, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Mathematics and Computer, Shahid Bahonar University of Kerman, Kerman, Iran"}], "References": []}, {"ArticleId": 80294915, "Title": "Numerical Solution of Monge–<PERSON><PERSON><PERSON> Equations via a Dynamic Formulation", "Abstract": "<p>We extend our previous work on a biologically inspired dynamic Monge–<PERSON> model (<PERSON><PERSON><PERSON> et al. in SIAM J Appl Math 78:651–676, 2018) and propose it as an effective tool for the numerical solution of the (L^{1}) -PDE based optimal transportation model. We first introduce a new Lyapunov-candidate functional and show that its derivative along the solution trajectory is strictly negative. Moreover, we are able to show that this functional admits the optimal transport density as a unique minimizer, providing further support to the conjecture that our dynamic model is time-asymptotically equivalent to the Monge–<PERSON> equations governing (L^{1}) optimal transport. Remarkably, this newly proposed Lyapunov-candidate functional can be effectively used to calculate the Wasserstein-1 (or earth mover’s) distance between two measures. We numerically solve these equations via a simple approach based on standard forward Euler time stepping and linear Galerkin finite element. The accuracy and robustness of the proposed solver is verified on a number of test problems of mixed complexity also in comparison with other approaches proposed in the literature. Numerical results show that the proposed scheme is very efficient and accurate for the calculation the Wasserstein-1 distances.</p>", "Keywords": "Monge<PERSON><PERSON> equations; Optimal transport; Numerical solution; Earth mover’s distance; 35M20; 65M60; 65M12", "DOI": "10.1007/s10915-020-01170-8", "PubYear": 2020, "Volume": "82", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centro di Ricerca Matematica Ennio De Giorgi, Scuola Normale Superiore, Pisa, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Gran Sasso Science Institute, L’Aquila, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics “<PERSON><PERSON><PERSON>, University of Padua, Padua, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics “<PERSON><PERSON><PERSON>, University of Padua, Padua, Italy"}], "References": []}, {"ArticleId": 80294922, "Title": "User experience-driven secure task assignment in spatial crowdsourcing", "Abstract": "<p>With the ubiquity of mobile devices and wireless networks, Spatial Crowdsourcing (SC) has earned considerable importance and attention as a new strategy of problem-solving. Tasks in SC have location constraints and workers need to move to certain locations to perform them. Current studies mainly focus on maximizing the benefits of the SC platform. However, user average waiting time, which is an important indicator of user experience, has been overlooked. To enhance user experience, the SC platform needs to collect lots of data from both workers and users. During this process, the private information may be compromised if the platform is not trustworthy. In this paper, we first define user experience-driven secure task assignment problem and propose two privacy-preserving online task assignment strategies to minimize the average waiting time. We securely construct an encrypted bipartite graph to protect private data. Based on this encrypted graph, we propose a secure <PERSON><PERSON>-<PERSON><PERSON><PERSON> algorithm to realize task assignment without privacy disclosure. Theoretical analysis shows the security of our approach and experimental results demonstrates its efficiency and effectiveness.</p>", "Keywords": "Spatial crowdsourcing; Task assignment; User experience; Privacy-preserving", "DOI": "10.1007/s11280-019-00728-3", "PubYear": 2020, "Volume": "23", "Issue": "3", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Artificial Intelligence, School of Computer Science and Technology, Soochow University, Suzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Artificial Intelligence, School of Computer Science and Technology, Soochow University, Suzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence, School of Computer Science and Technology, Soochow University, Suzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Macquarie University, Sydney, Australia"}, {"AuthorId": 5, "Name": "Qing Li", "Affiliation": "Department of Computing, The Hong Kong Polytechnic University, Hong Kong, China"}], "References": []}, {"ArticleId": 80294928, "Title": "Robust event-driven particle tracking in complex geometries", "Abstract": "Particle tracking, that is, the repeated localization of particles within a grid by means of tracking the particles’ trajectories, is routinely applied in particle-based schemes where the domain is described by an unstructured polyhedral grid. A range of tracking algorithms are available in the literature, which are inherently similar to algorithmic approaches common both in event-driven particle dynamics (EDPD) and ray-tracing methods. We propose a reformulation of existing particle tracking algorithms in the context of EDPD. On the one hand, this resolves inconsistencies in the mapping between particle positions and grid cells triggered, e.g., by imperfect grids. More importantly, it allows the specification of solid objects via constructive solid geometry (CSG), a standard technique for the modeling of solids in computer-aided design. While usually considered contrary approaches, our description of the computational domain as the combination of a bounding volume defined by an unstructured grid and solids modeled via CSG embedded into this volume can be highly advantageous. The two different approaches of modeling the computational domain complement each other perfectly, as the CSG representation is not only efficient in terms of memory and computing time, but also avoids the challenges of generating finely resolved unstructured grids in the presence of complicated boundaries. These benefits, as well as the positive impact of several algorithmic optimizations of the extended tracking algorithm, are exemplified via a particle-based simulation of a gas flow through a highly porous medium.", "Keywords": "Particle tracking ; Particle sorting ; Event-driven dynamics ; Constructive solid geometry ; Complex geometries", "DOI": "10.1016/j.cpc.2020.107229", "PubYear": 2020, "Volume": "254", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Multiscale Simulation, Friedrich-Alexander-Universität Erlangen-Nürnberg, Cauerstraße 3, 91058 Erlangen, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Engineering, University of Aberdeen, Fraser Noble Building, AB24 3UE, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Multiscale Simulation, Friedrich-Alexander-Universität Erlangen-Nürnberg, Cauerstraße 3, 91058 Erlangen, Germany;Corresponding author"}], "References": []}, {"ArticleId": 80295001, "Title": "Ball-burnishing factors affecting residual stress of AISI 8620 steel", "Abstract": "<p>Ball burnishing is a superfinishing operation performed to improve surface and subsurface integrity. In the present study, a model was developed to confirm that the ball burnishing of AISI 8620 steel affects its microstructures, roughness, and residual stress. As a part of the process, the influence of different parameters on the residual stress surface in the axial direction was investigated. Samples were prepared by heat treatment and turning. The ball-burnishing process was performed, and the improvements on the surface and subsurface integrity were found to be significant. Residual stress after ball burnishing was found to be affected by pressure and feed rate to a statistically significant extent. Stress was found to be in the form of compression. The steel displayed significant improvements in surface roughness and residual stress following burnishing. The linear regression models derived from the data for the relationship between burnishing factors and residual stress offer R <sup>2</sup> values of 80.88%. Results suggest that the ball-burnishing process enhanced the properties of AISI 8620 steel.</p>", "Keywords": "Residual stress; Ball burnishing; AISI 8620 steel; Modeling", "DOI": "10.1007/s00170-020-05119-x", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "Abdul<PERSON><PERSON>", "Affiliation": "Mechanical, Industrial, and Manufacturing Engineering Department, The University of Toledo, Toledo, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical, Industrial, and Manufacturing Engineering Department, The University of Toledo, Toledo, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Light Vehicle Division-Engineering NVH, Dana Incorporated, Maumee, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Mechanical Engineering Department, Northern Border University, Arar, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering Department, Jazan University, Jazan, Saudi Arabia"}], "References": []}, {"ArticleId": 80295039, "Title": "Equilibrium point-based control of muscle-driven anthropomorphic legs reveals modularity of human motor control during pedalling", "Abstract": "This study aims at shedding new light on the human motor control during forward and backward pedalling motions from a viewpoint of the mechanical impedance, determined by the coordinated activations of multiple pairs of agonist-antagonist muscles, called equilibrium point (EP)-based synergy. First, human movements and electromyograms as muscle activations during forward and backward pedalling motions were measured, and using our working model proposed earlier, they were analysed to obtain EP-based synergies, i.e. a set of synergy vectors and the corresponding time-courses of synergy activation coefficients associated with a virtual trajectory, for each pedalling direction. We showed the similarity in the EP-based synergies between forward and backward motions. We performed a robot experiment, where we used the EP-based synergies for the forward pedalling with their time-reversed synergy activation coefficients and a phase-shift in a coefficient for a specific synergy vector to actuate a robot of muscle-driven anthropomorphic legs for achieving a backward pedalling. Comparisons between the backward motion in the robot and that in human confirmed that they were mostly the same. Our results support our working model that the human motor control shares the common EP-based synergy and the associated mechanical impedance for controlling the forward and backward pedalling motions. <img src=\"//:0\" data-src='{\"type\":\"image\",\"src\":\"/na101/home/<USER>/publisher/tandf/journals/content/tadr20/2020/tadr20.v034.i05/01691864.2019.1708790/20200229/images/medium/tadr_a_1708790_uf0001_oc.jpg\"}' />", "Keywords": "Human motor control ; muscle synergy ; pedalling ; musculoskeletal robot ; rehabilitation robotics", "DOI": "10.1080/01691864.2019.1708790", "PubYear": 2020, "Volume": "34", "Issue": "5", "JournalId": 5595, "JournalTitle": "Advanced Robotics", "ISSN": "0169-1864", "EISSN": "1568-5535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Science and Bioengineering, Graduate School of Engineering Science, Osaka University, Osaka, Japan"}, {"AuthorId": 2, "Name": "Hiroaki <PERSON>", "Affiliation": "Department of Mechanical Science and Bioengineering, Graduate School of Engineering Science, Osaka University, Osaka, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Massachusetts Institute of Technology, Cambridge, MA, USA"}], "References": []}, {"ArticleId": 80295060, "Title": "Dynamic reputation–based consensus mechanism: Real-time transactions for energy blockchain", "Abstract": "<p>The energy blockchain is a distributed Internet protocol for energy transactions between nodes in power systems. The consensus algorithm is the core component of the energy blockchain and has an essential impact on its application. At present, in the implementation of the energy blockchain, there are problems such as low transaction throughput (transactions per second) and high latency, which cannot meet the application requirements of real-time processing transactions in the energy field. To this end, according to the analysis of conventional blockchain consensus algorithm and traditional practical Byzantine fault tolerance algorithm, a dynamic-reputation practical Byzantine fault tolerance algorithm for the energy blockchain is proposed. The dynamic-reputation practical Byzantine fault tolerance algorithm adopts a credit-based consortium node consensus election method. The monitoring node divides the remaining nodes into two types of nodes according to the reputation value: the consensus node and the secondary node, which, respectively, participate in different stages of the block generation process, and dynamically update the consensus nodes with low reputation ratings. By constructing the experimental platform simulation, the test results verify the effectiveness of the dynamic-reputation practical Byzantine fault tolerance algorithm. Compared with the algorithm of the fabric platform, the dynamic-reputation practical Byzantine fault tolerance algorithm improves the transaction processing speed and is suitable for the blockchain application in the energy field.</p>", "Keywords": "", "DOI": "10.1177/1550147720907335", "PubYear": 2020, "Volume": "16", "Issue": "3", "JournalId": 1187, "JournalTitle": "International Journal of Distributed Sensor Networks", "ISSN": "1550-1329", "EISSN": "1550-1477", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming, China;Computer Technology Application Key Lab of Yunnan Province, Kunming University of Science and Technology, Kunming, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Grid Corporation of China, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Grid Information & Telecommunication Co., Ltd, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming, China;Computer Technology Application Key Lab of Yunnan Province, Kunming University of Science and Technology, Kunming, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming, China;Computer Technology Application Key Lab of Yunnan Province, Kunming University of Science and Technology, Kunming, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming, China;Computer Technology Application Key Lab of Yunnan Province, Kunming University of Science and Technology, Kunming, China"}], "References": []}, {"ArticleId": 80295077, "Title": "Charging wireless sensor network security technology based on encryption algorithms and dynamic model", "Abstract": "<p>With the popularization of the Internet, the related information technology is developing faster and faster, and the scale and complexity of the network are also increasing. Wireless networks cover all aspects of life, along with it, network security issues have gradually emerged. In recent years, network security vulnerabilities have been exposed continuously, from WiFi to Bluetooth, people gradually realize the security of wireless networks. The purpose of this article is to solve the existing security problems and study the operation process of RC4 and Advanced Encryption Standard algorithms, and the improvement scheme is put forward. This article is based on the inherent media access control address filtering technology of wireless network card; a dynamic security model for wireless networks is proposed and constructed. Devices accessing the network use 802.1x authentication method and distribute and set security status values for each device, the authentication server uses Remote Authentication Dial in User Service. This article uses the method of virtual private network encryption network to provide an encryption layer for communication between devices and wireless networks, thus, the data in network transmission can be encrypted and protected. In this article, the structure and strategy of Remote Authentication Dial in User Service are changed, in order to ensure the high security of wireless network equipment in the connection process. In the testing phase of this article, we have tested the model in detail several times. Tests are divided into middleman and session interception tests. A large number of test results show that this model can improve the security of wireless network and has good performance.</p>", "Keywords": "", "DOI": "10.1177/1550147720901999", "PubYear": 2020, "Volume": "16", "Issue": "3", "JournalId": 1187, "JournalTitle": "International Journal of Distributed Sensor Networks", "ISSN": "1550-1329", "EISSN": "1550-1477", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Li", "Affiliation": "State Key Lab of Astronautic Dynamics, Xi’an, Shaanxi, China;China Xi’an Satellite Control Center, Xi’an, Shaanxi, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Lab of Astronautic Dynamics, Xi’an, Shaanxi, China;China Xi’an Satellite Control Center, Xi’an, Shaanxi, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Lab of Astronautic Dynamics, Xi’an, Shaanxi, China;China Xi’an Satellite Control Center, Xi’an, Shaanxi, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Lab of Astronautic Dynamics, Xi’an, Shaanxi, China;China Xi’an Satellite Control Center, Xi’an, Shaanxi, China"}], "References": []}, {"ArticleId": 80295104, "Title": "A secure image authentication scheme based on dual fragile watermark", "Abstract": "<p>Both security and tamper localization are essential for fragile watermarking techniques. Embedded fragile watermark should be sensitive enough to cover images. But high sensitivity maybe bring inaccurate tamper localization, and both high sensitivity and accurate tamper localization seem contradictory. This paper proposes a watermark scheme based on dual fragile watermark: diffusion watermark and authentication watermark. The diffusion watermark is determined by a cover image, a secret key and two random numbers via nonlinear transformations, and the authentication watermark is generated from the cover image, the diffusion watermark and another secret key. The diffusion watermark and the authentication watermark have high sensitivity to cover images and at meantime the authentication watermark can verify the integrity of images and localize tampered area. The scrambled authentication watermark and the diffusion watermark are arbitrarily embedded into the two lowest significant bit layers of the cover image through a random sequence controlled by a secret key. The design aims to enhance the security of fragile watermarking, and the statistical results and security analysis show that this scheme can resist chosen cover-image attacks.</p>", "Keywords": "Image authentication; Dual fragile watermark; Chosen cover image attacks; Collage attacks; 3D Arnold transformation", "DOI": "10.1007/s11042-019-08594-x", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Science, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Beijing University of Posts and Telecommunications, Beijing, China"}], "References": []}, {"ArticleId": 80295105, "Title": "Re‐detection object tracking algorithm in the cyber physical system", "Abstract": "Cyber physical system (CPS) is a complex system combining computation, network and physics; object tracking is an important application of CPS. To solve the problem that the traditional kernel correlation filtering tracking algorithm cannot recover the lost object, the authors propose a re-detection object tracking algorithm. The proposed algorithm mainly designs a new adaptive detection criterion. By comparing the value of detection criterion and the value of the experience threshold, it can be judged whether the current target is lost or not. When the object tracking fails, the proposed method can generate target candidate boxes by using the edge boxes algorithm and select the best target location by applying the non-maximum suppression and the Euclidean metric methods. In addition, a fast multi-scale estimation method and an adaptive updating method are added to the tracking procedure to further improve the overall performance of the algorithm. Experimental results show that the proposed approach has a good performance in terms of precision and success rates.", "Keywords": "tracking procedure; lost object; adaptive detection criterion; cyber physical system; physics; CPS; edge boxes algorithm; re-detection object tracking algorithm", "DOI": "10.1049/iet-cps.2019.0086", "PubYear": 2020, "Volume": "5", "Issue": "3", "JournalId": 36956, "JournalTitle": "IET Cyber-Physical Systems: Theory & Applications", "ISSN": "", "EISSN": "2398-3396", "Authors": [{"AuthorId": 1, "Name": "Zhaohua Hu", "Affiliation": "School of Electronic & Information Engineering , Nanjing University of Information Science & Technology , Nanjing 210044 , People's Republic of China↑Jiangsu Collaborative Innovation Center on Atmospheric Environment and Equipment Technology , Nanjing University of Information Science & Technology , Nanjing 210044 , People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic & Information Engineering , Nanjing University of Information Science & Technology , Nanjing 210044 , People's Republic of China"}], "References": []}, {"ArticleId": 80295164, "Title": "Compact UWB flexible elliptical CPW‐fed antenna with triple notch bands for wireless communications", "Abstract": "<p>A coplanar waveguide (CPW)‐fed flexible elliptical antenna with triple band notched characteristics is presented in this article. The designed antenna consists of an elliptical patch and slots incorporated CPW feed line to cover the bandwidth requirements for ultra‐wideband (UWB) applications. The designed UWB antenna has a fractional bandwidth of about 166.19% (1.20‐13 GHz) with a center frequency of 7.1 GHz in simulation and about 170.10% (1.05‐13 GHz) with a center frequency of 7.025 GHz in measurement. The overall dimension of the proposed flexible antenna is 45 × 35 × 0.6 mm<sup>3</sup>. The triple notched bands are realized by designing with circular shaped split‐ring‐resonators (SRRs) and defected ground structure (DGS). According to the measurement, first notched band (2.0 − 2.70 GHz) is generated for rejecting 2.4 GHz WLAN by introducing a single circular ST‐SRR on the radiating patch. The second notch (3.45‐3.80 GHz) is obtained by embedding another circular ST‐SRR on the patch to mitigate the interference of 3.5 GHz Wi‐MAX system. Finally, due to presence of DGS, third notch (5.15‐6.20 GHz) is produced which suppresses the interference from 5.5 GHz Wi‐MAX and 5.2/5.8 GHz WLAN systems. The proposed antenna offers excellent performance in different flexible conditions that confirm its applicability on curved surfaces for UWB systems.</p>", "Keywords": "CPW antenna;DGS;flexible antenna;SRR;triple notch;UWB", "DOI": "10.1002/mmce.22201", "PubYear": 2020, "Volume": "30", "Issue": "7", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Smart Communications Research Team (ERSC), EMI, Mohammed V University in Rabat, Rabat, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, IMPS College of Engineering and Technology, Malda, West Bengal, India"}, {"AuthorId": 3, "Name": "Soumendu Ghos<PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, NIT Meghalaya, Shilong, Meghalaya, India"}, {"AuthorId": 4, "Name": "Boddapati Taraka Phani Madhav", "Affiliation": "Department of Electronics & Communication Engineering, Koneru Lakshmaiah Education Foundation, Vijayawada, Andhra Pradesh, India"}], "References": []}, {"ArticleId": 80295172, "Title": "A silicone-based soft matrix nanocomposite strain-like sensor fabricated using Graphene and Silly Putty ®", "Abstract": "Off-the-shelf planar strain gauges are ubiquitous and are generally designed for materials with a large elastic modulus such as steel or aluminum. Correspondingly, the strain gauges themselves are stiff and do not deform substantially under applied stress. Pairs of this type of strain gauge are typically used in a Wheatstone bridge circuit allowing the measurement of very small changes in resistance due to the changes in sensing element cross-sectional area to be measured. However, their use with softer low-modulus materials is limited due to the larger elastic deformations involved. The conductive property of graphene is leveraged to produce a different type of strain sensor that is sensitive yet also capable of significant elastic deformation. The graphene is dispersed in a silicone-based polymer matrix such that the deformation induces a change in resistance that can be measured using a voltage divider circuit. The target application for which this sensor is developed is to measure strain in a pressurized length of soft Tygon® tubing which is often used in pumping fluids through microfluidic devices. However, the silicone-based graphene polymer can easily be applied to a variety of other shapes and soft materials.", "Keywords": "Sensor ; Strain gauge ; Graphene ; Nanocomposite ; Silicone-based polymer ; Tubing dynamics ; Active microfluidics", "DOI": "10.1016/j.sna.2020.111917", "PubYear": 2020, "Volume": "305", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Corresponding author at: Mechanical and Mechatroncis Engineering at University of Waterloo, 200, University Avenue West, Waterloo, Ontario, Canada"}], "References": []}, {"ArticleId": 80295196, "Title": "Detecting clickbaits using two-phase hybrid CNN-LSTM biterm model", "Abstract": "Clickbait indicates the type of content with an intending goal to attract the attention of readers. It has grown to become a nuisance to social media users. The purpose of clickbait is to bring an appealing link in front of users. Clickbaits seen in the form of headlines influence people to get attracted and curious to read the inside content. The content seen in the form of text on clickbait posts is very short to identify its features as clickbait. In this paper, a novel approach (two-phase hybrid CNN-LSTM Biterm model) has been proposed for modeling short topic content. The hybrid CNN-LSTM model when implemented with pre-trained GloVe embedding yields the best results based on accuracy, recall, precision, and F1-score performance metrics. The proposed model achieves 91.24%, 95.64%, 95.87% precision values for Dataset 1, Dataset 2 and Dataset 3, respectively. Eight types of clickbait such as Reasoning, Number, Reaction, Revealing, Shocking/Unbelievable, Hypothesis/Guess, Questionable, Forward referencing are classified in this work using the Biterm Topic Model (BTM). It has been shown that the clickbaits such as Shocking/Unbelievable, Hypothesis/Guess and Reaction are the highest in numbers among rest of the clickbait headlines published online. Also, a ground dataset of non-textual (image-based) data using multiple social media platforms has been created in this paper. The textual information has been retrieved from the images with the help of OCR tool. A comparative study is performed to show the effectiveness of our proposed model which helps to identify the various categories of clickbait headlines that are spread on social media platforms.", "Keywords": "Clickbait ; News ; Classifier ; Features ; Social media", "DOI": "10.1016/j.eswa.2020.113350", "PubYear": 2020, "Volume": "151", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Thapar Institute of Engineering and Technology, Patiala, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Thapar Institute of Engineering and Technology, Patiala, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indraprastha Institute of Information Technology, Delhi, India"}], "References": []}, {"ArticleId": 80295197, "Title": "A bimetallic CoNi-based metal−organic framework as efficient platform for label-free impedimetric sensing toward hazardous substances", "Abstract": "A novel bimetallic CoNi-based metal−organic framework (CoNi-MOF) has been synthesized using the mixed organic linkers of 4-(1H-tetrazol-5-yl)benzoic acid (H<sub>2</sub>TZB) and 2,4,6-tri(4-pyridyl)-1,3,5-triazine (TPT), which can be employed as a platform for the feasible construction of label-free impedimetric immunosensor to efficiently detect the trace hazardous substances of deoxynivalenol (DON) and salbutamol (SAL). As compared with the individual Co-MOF and Ni-MOF, the bimetallic CoNi-MOF exhibits much smaller particle sizes and superior electrochemical activity. Owing to the carboxyl groups of H<sub>2</sub>TZB that can generate electrostatic interaction with the amino groups of antibodies to realize the high immobilization amounts of antibodies, as well as the π−π* stacking and multivalent affinity interactions between CoNi-MOF and antibodies, the as-prepared CoNi-MOF shows excellent biosensing ability toward analytes determined by electrochemical techniques. The developed CoNi-MOF-based electrochemical immunosensor demonstrates high sensitivity with low detection limits of 0.05 and 0.30 pg·mL<sup>−1</sup> toward DON and SAL, respectively, in the concentration range of 0.001 to 0.5 ng·mL<sup>−1</sup>, which also shows good selectivity in the presence of other interferences. Therefore, with the advantages of high sensitivity, good selectivity, and simple operation, this new strategy is believed to exhibit great potential for convenient detection of poisonous and harmful residues in foods.", "Keywords": "Bimetallic−organic framework ; Electrochemical immunosensor ; Detection of deoxynivalenol ; Detection of salbutamol ; Food safety", "DOI": "10.1016/j.snb.2020.127927", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Henan Provincial Key Laboratory of Surface and Interface Science, Zhengzhou University of Light Industry, Zhengzhou, 450002, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Henan Provincial Key Laboratory of Surface and Interface Science, Zhengzhou University of Light Industry, Zhengzhou, 450002, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "Henan Provincial Key Laboratory of Surface and Interface Science, Zhengzhou University of Light Industry, Zhengzhou, 450002, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Henan Provincial Key Laboratory of Surface and Interface Science, Zhengzhou University of Light Industry, Zhengzhou, 450002, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Henan Provincial Key Laboratory of Surface and Interface Science, Zhengzhou University of Light Industry, Zhengzhou, 450002, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Henan Provincial Key Laboratory of Surface and Interface Science, Zhengzhou University of Light Industry, Zhengzhou, 450002, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Henan Provincial Key Laboratory of Surface and Interface Science, Zhengzhou University of Light Industry, Zhengzhou, 450002, PR China;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Henan Provincial Key Laboratory of Surface and Interface Science, Zhengzhou University of Light Industry, Zhengzhou, 450002, PR China;Corresponding authors"}], "References": []}, {"ArticleId": 80295210, "Title": "A novel data augmentation scheme for pedestrian detection with attribute preserving GAN", "Abstract": "Recently pedestrian detection has progressed significantly. However, detecting pedestrians of small scale or in heavy occlusions is still notoriously difficult. Besides, the generalization ability of pre-trained detectors across different datasets remains to be improved. Both of these issues can be attributed to insufficient training data coverage. To cope with this, we present an efficient data augmentation scheme by transferring pedestrians from other datasets into the target scene with a novel Attribute Preserving Generative Adversarial Networks (APGAN). The proposed methodology consists of two steps: pedestrian embedding and style transfer. The former step can simulate pedestrian images of various scale and occlusion, in any pose or background, thus greatly promoting the data variation. The latter step aims to make the generated samples more realistic while guarantee the data coverage. To achieve this goal, we propose APGAN, which pursues both good visual quality and attribute preserving after style transfer. With the proposed method, we can make effective sample augmentations to improve the generalization ability of the trained detectors and enhance its robustness to scale change and occlusions. Extensive experiment results validate the effectiveness and advantages of our method.", "Keywords": "Generative Adversarial Networks ; Pedestrian detection ; Data augmentation", "DOI": "10.1016/j.neucom.2020.02.094", "PubYear": 2020, "Volume": "401", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, No.95, Zhongguancun East Road, Beijing 100190, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, No.95, Zhongguancun East Road, Beijing 100190, China;University of Chinese Academy of Sciences, Beijing 100049, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sun Yat-Sen University, Guangzhou 510000, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, No.95, Zhongguancun East Road, Beijing 100190, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Zhao", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, No.95, Zhongguancun East Road, Beijing 100190, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, No.95, Zhongguancun East Road, Beijing 100190, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, No.95, Zhongguancun East Road, Beijing 100190, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, No.95, Zhongguancun East Road, Beijing 100190, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, No.95, Zhongguancun East Road, Beijing 100190, China;University of Chinese Academy of Sciences, Beijing 100049, China"}], "References": []}, {"ArticleId": 80295213, "Title": "A compact broadband coplanar waveguide fed circularly polarized printed monopole antenna with asymmetric modified ground plane", "Abstract": "<p>In this communication, a broadband circularly polarized (CP) monopole antenna with coplanar waveguide (CPW) feeding is proposed. It consists of a modified rectangular monopole, an asymmetric ground plane, a two‐linked inverted L‐shaped strips on the left CPW ground, and two rectangular horizontal slots in asymmetric CPW ground plane. The overall dimension is only 0.47λ<sub> o </sub> × 0.47λ<sub> o </sub>. The antenna prototype has been fabricated. The measured results indicate that a broad −10 dB impedance bandwidth (IBW) of 107.5% (4.3 GHz, 1.85‐6.15 GHz) and a broad 3 dB axial ratio ARBW of 104.3% (4 GHz, 1.855‐5.9 GHz) can be achieved; the average realized gain is 2.3 dBi for the entire CP band. The proposed antenna is an attractive candidate for several wireless communication systems.</p>", "Keywords": "monopole antenna;circular polarization;broadband;axial ratio", "DOI": "10.1002/mmce.22205", "PubYear": 2020, "Volume": "30", "Issue": "7", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University Teknikal Malaysia Melaka (UTeM), Melaka, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University Teknikal Malaysia Melaka (UTeM), Melaka, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University Teknikal Malaysia Melaka (UTeM), Melaka, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University Teknikal Malaysia Melaka (UTeM), Melaka, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University Teknikal Malaysia Melaka (UTeM), Melaka, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering and Technology, Multimedia University, Melaka, Malaysia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University Teknikal Malaysia Melaka (UTeM), Melaka, Malaysia"}], "References": []}, {"ArticleId": 80295219, "Title": "An adaptive integral separated proportional–integral controller based strategy for particle swarm optimization", "Abstract": "Particle swarm optimization algorithm (PSO), which updates the particle by the linear summation of the particle’s past momentum and current search direction, has demonstrated its power in many optimization applications. However, few researches have focused on the overshoot problem of PSO caused by past momentum, which may result in an oscillation search and slow convergence speed on complex optimization problems. Based on the connection between the PSO optimization process and the PID controller based control system, we first analyze the effect of the momentum in PSO, then we find that the oscillation problem relates to the gathering of the momentum term and the current search direction. Inspired by the conditional integration in automatic control, we propose an adaptive search direction learning approach for PSO, namely ISPSO (Integral Separated PI controller-based PSO). The ISPSO separates momentum term adaptively when the current search direction is consistent with historical momentum direction, and then the IS strategy will guide particles to fly to better and steady directions by eliminating the integral gathering of historical momentum. We select seven main-stream approaches as control group, and conduct experiments on benchmark CEC2013 test suite. The experimental results of ISPSO, providing the faster steady global convergence and higher solution accuracy, show a significant improvement on PSO algorithm. Compared with the results of the seven methods, the performance of ISPSO is also promising in general, especially for unimodal and composition functions. Furthermore, our results validate the generalization and effectiveness of IS strategy by the designed experiments of PSO variants with and without IS strategy, which also indicates that the IS strategy can be applied to PSO variants with any topological structures.", "Keywords": "Particle swarm optimization ; Integral separated strategy ; Overshoot problem ; Search direction ; Proportional–integral controller", "DOI": "10.1016/j.knosys.2020.105696", "PubYear": 2020, "Volume": "195", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan 430072, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan 430072, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics and Information Engineering, Minnan Normal University, Zhangzhou 363000, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan 430072, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics and Information Engineering, Minnan Normal University, Zhangzhou 363000, China"}, {"AuthorId": 6, "Name": "Yuanxiang Li", "Affiliation": "School of Computer Science, Wuhan University, Wuhan 430072, China;Corresponding authors"}], "References": []}, {"ArticleId": 80295220, "Title": "A high BDR microstrip‐line fed antenna with multiple asymmetric elliptical wide‐slots for wideband applications", "Abstract": "<p>In this study, a novel printed wide‐slot antenna for wideband applications is presented. The designed antenna consists of four merged elliptical wide‐slots (EWSs) of different dimensions in the ground plane. An open‐ended microstrip line having a characteristic impedance of 50 Ω is used to excite the EWS. Each EWS corresponds to the different frequency of operation and hence when merged together give a wideband response. The fabricated prototype of the designed antenna shows the 10 dB return loss bandwidth (RLBW) of about 157.72% ranging from 2.21 to 18.7 GHz. The peak gain varies from 0.1 to 6.5 dB within the RLBW is reported. An almost constant group delay, low variation ( < −40 dB) in the transfer function S <sub>21</sub> and linear phase variation for both side by side and face to face orientations of the designed antenna shows its applicability for wideband applications. The electrical dimensions of about 0.176 λ <sub> L </sub> × 0.162 λ <sub> L </sub> (where λ <sub> L </sub> is the lowest operating wavelength) give rise to the bandwidth dimension ratio of about 5505 which is highest among the antenna structures reported in the literature. The measured results are found in good concordance with the results obtained from numerical simulations.</p>", "Keywords": "asymmetric elliptical wide‐slot antenna;bandwidth dimension ratio;microstrip line‐fed;return loss bandwidth", "DOI": "10.1002/mmce.22202", "PubYear": 2020, "Volume": "30", "Issue": "7", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "USIC&T, GGSIPU, New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "USIC&T, GGSIPU, New Delhi, India"}], "References": []}, {"ArticleId": ********, "Title": "Privacy-preserving human action recognition as a remote cloud service using RGB-D sensors and deep CNN", "Abstract": "Cloud-based expert systems are highly emerging nowadays. However, the data owners and cloud service providers are not in the same trusted domain in practice. For the sake of data privacy, sensitive data usually has to be encrypted before outsourcing which makes effective cloud utilization a challenging task. Taking this concern into account, we propose a novel cloud-based approach to securely recognize human activities. A few schemes exist in the literature for secure recognition. However, they suffer from the problem of constrained data and are vulnerable to re-identification attack, where advanced deep learning models are used to predict an object’s identity. We address these problems by considering color and depth data, and securing them using position based superpixel transformation. The proposed transformation is designed by actively involving additional noise while resizing the underlying image. Due to this, a higher degree of obfuscation is achieved. Further, in spite of securing the complete video, we secure only four images, that is, one motion history image and three depth motion maps which are highly saving the data overhead. The recognition is performed using a four stream deep Convolutional Neural Network (CNN), where each stream is based on pre-trained MobileNet architecture. Experimental results show that the proposed approach is the best suitable candidate in “security-recognition accuracy (%)” trade-off relation among other image obfuscation as well as state-of-the-art schemes. Moreover, a number of security tests and analyses demonstrate robustness of the proposed approach.", "Keywords": "Privacy-preserving ; Expert system ; Deep learning ; Human action recognition ; Multimedia security ; Cloud computing", "DOI": "10.1016/j.eswa.2020.113349", "PubYear": 2020, "Volume": "152", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Machine Vision Lab, S-211, South Block Department of Computer Science & Engineering, Indian Institute of Technology Roorkee, Roorkee 247667, Uttarakhand, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Machine Vision Lab, S-211, South Block Department of Computer Science & Engineering, Indian Institute of Technology Roorkee, Roorkee 247667, Uttarakhand, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Machine Vision Lab, S-211, South Block Department of Computer Science & Engineering, Indian Institute of Technology Roorkee, Roorkee 247667, Uttarakhand, India"}], "References": []}, {"ArticleId": 80295226, "Title": "MMCL-Net: Spinal disease diagnosis in global mode using progressive multi-task joint learning", "Abstract": "Simultaneous detection, segmentation, and classification of multiple spinal structures on MRI is crucial for the early and pathogenesis-based diagnosis of multiple spine diseases in the clinical setting. It is more assistance for radiologists reflections on the disease based on the pathogenesis when the lesion area and its adjacent structures are detected. Obviously, the multiple structures of the spine are directly interdependent and influential, and the multi-tasks under a deep convolutional neural network framework can also influence each other. Multi-task joint optimization in the spinal global mode is a direct outlet to seek the dynamic balance of the above potential correlation. In this paper, we propose a novel end-to-end Multi-task Multi-structure Correlation Learning Network (MMCL-Net) for the detection, segmentation, and classification (normal, slight, marked, and severe) of three types of spine structure: disc, vertebra, and neural foramen simultaneously. And the model is locally optimized to achieve a more stable dynamic equilibrium state. Extensive experiments on T1/T2-weighted MR scans from 200 subjects demonstrate that MMCL-Net achieves high performance with mAP of 0.9187, the classification accuracy of 90.67%, and dice coefficient of 90.60%. The experimental results show that the performance of our method is comparable to that of the state-of-the-art methods.", "Keywords": "Densely aggregation ; Level-set ; Global optimization ; Progressive multi-task ; Multi-structure ; Medical image ; 00-01 ; 99-00", "DOI": "10.1016/j.neucom.2020.01.112", "PubYear": 2020, "Volume": "399", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Science and Technology, Shandong University of Traditional Chinese Medicine, Jinan, SD, China;Center for Medical Artificial Intelligence, Shandong University of Traditional Chinese Medicine, Qingdao, SD, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Medical Artificial Intelligence, Shandong University of Traditional Chinese Medicine, Qingdao, SD, China;Qingdao Academy of Chinese Medical Sciences, Shandong University of Traditional Chinese Medicine, Qingdao, SD, China;Corresponding author at: Center for Medical Artificial Intelligence, Shandong University of Traditional Chinese Medicine, Qingdao, SD, China"}, {"AuthorId": 3, "Name": "Zhongyi Han", "Affiliation": "School of Software, Shandong University, Jinan, SD, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Science and Technology, Shandong University of Traditional Chinese Medicine, Jinan, SD, China;Center for Medical Artificial Intelligence, Shandong University of Traditional Chinese Medicine, Qingdao, SD, China;Qingdao Academy of Chinese Medical Sciences, Shandong University of Traditional Chinese Medicine, Qingdao, SD, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, SD, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Digital Image Group (DIG), London, ON, Canada;Department of Medical Imaging, Western University, London, ON, Canada"}], "References": []}, {"ArticleId": 80295246, "Title": "EpidNews: Extracting, exploring and annotating news for monitoring animal diseases", "Abstract": "In the recent years, there has been a massive increase in the amount of data published on the web about human and animal health events. Epidemiologists use this spatio-temporal information on a daily basis to detect and monitor disease outbreaks over time. While official sources such as the World Organization for Animal Health release formal outbreak notifications, unofficial sources such as online newspapers contain unstructured information with different levels of reliability. Manually retrieving the data from a website like Google News and then deriving sensible insights from the huge dataset takes a lot of time and effort. We present EpidNews , a new visual analytics tool that helps to visualize and explore epidemiological data for monitoring animal disease outbreaks. The tool uses several views depicting various levels of abstraction, which helps fulfill almost all the data analysis requirements of epidemiologists. EpidNews allows to visualize and compare data from both official and unofficial sources. We also present the use case of an epidemiology expert, wherein the expert assesses the usability and productivity of <PERSON><PERSON><PERSON><PERSON> by using the tool in her daily work.", "Keywords": "Visual analytics ; Animal epidemiology ; Spatio-temporal data", "DOI": "10.1016/j.cola.2019.100936", "PubYear": 2020, "Volume": "56", "Issue": "", "JournalId": 59906, "JournalTitle": "Journal of Computer Languages", "ISSN": "2665-9182", "EISSN": "2590-1184", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, BITS Pilani, Pilani 333031, Rajasthan, India;LIRMM - University of Montpellier - CNRS, 860 rue de St Priest, Montpellier 34095, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CIRAD - ASTRE - University of Montpellier, Campus international de Baillarguet, Montpellier 34398, France;CIRAD - TETIS - University of Montpellier, 500 rue Jean-Franois Breton, Montpellier 34000, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "LIRMM - University of Montpellier - CNRS, 860 rue de St Priest, Montpellier 34095, France;University of Paul-Valéry Montpellier 3, Route de Mende, Montpellier 34199, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "LIRMM - University of Montpellier - CNRS, 860 rue de St Priest, Montpellier 34095, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LIRMM - University of Montpellier - CNRS, 860 rue de St Priest, Montpellier 34095, France;University of Paul-Valéry Montpellier 3, Route de Mende, Montpellier 34199, France;Corresponding author at: LIRMM - University of Montpellier - CNRS, 860 rue de St Priest, 34095 Montpellier, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "CIRAD - TETIS - University of Montpellier, 500 rue Jean-Franois Breton, Montpellier 34000, France"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "LIRMM - University of Montpellier - CNRS, 860 rue de St Priest, Montpellier 34095, France"}], "References": [{"Title": "EpidVis: A visual web querying tool for animal epidemiology surveillance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "1", "Page": "48", "JournalTitle": "Information Visualization"}]}, {"ArticleId": 80295270, "Title": "Subspace-based multi-view fusion for instance-level image retrieval", "Abstract": "<p>In this paper, we address the problem of multiple features fusion in instance-level image retrieval. Achieving tremendous success in recent retrieval task, convolutional neural network (CNN) features are capable of encoding high-level image contents and demonstrate unrivaled superiority to the hand-crafted shallow image signatures. However, the shallow features still play a beneficial role in visual matching particularly when dramatic variances in viewpoint and scale are present, since they inherit certain invariance from the local robust descriptor, e.g., scale-invariant feature transform (SIFT). Thus, it is important to leverage the mutual correlation between these two heterogeneous signatures for effective visual representation. Since it is still an open problem, in this paper, we propose a subspace-based multi-view fusion strategy where a shared subspace is uncovered from the original high-dimensional features yielding a compact latent representation. Experiments on six public benchmark datasets reveal the proposed method works better than other classical fusion approaches and achieve the state-of-the-art performance. </p>", "Keywords": "Instance retrieval; Deep neural network; Shallow features; Multi-view fusion; Latent representations", "DOI": "10.1007/s00371-020-01828-2", "PubYear": 2021, "Volume": "37", "Issue": "3", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The School of Computer Science and Technology, Nanjing Normal University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The School of Instrument Science and Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The School of Automation, Southeast University, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The School of Automation, Southeast University, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The School of Computer Science and Technology, Nanjing Normal University, Nanjing, China"}], "References": []}, {"ArticleId": 80295280, "Title": "ASPPR: A New Assembly Sequence and Path Planner/Replanner for Monotone and Nonmonotone Assembly Planning", "Abstract": "The Assembly Sequence and Path Planning (ASPP) problem concerns with computing a sequence of assembly motions for constituent parts of an assembled final product while finding collision-free and short paths for parts from their initial location to their final position. ASPP is a challenging problem due to being NP-hard, and is an important subproblem of the Assembly Planning problem, which is encountered frequently in the manufacturing industry and takes up more than half of the total production time. In this paper, a new method called Assembly Sequence and Path Planner/Replanner (ASPPR) is presented to solve the ASPP problem. ASPPR has a sequence-planning component, which is a simple greedy heuristic that in each iteration tries to locally minimize geometric interferences between parts being assembled along the main directions, and a path-planning component, which employs a sampling-based stochastic path planner to plan short paths for parts while avoiding workspace obstacles. Thanks to its replanning feature, the ASPPR method is able to identify and resolve cases where already-assembled parts impede the movements of subsequent parts. While majority of the methods in the literature deal with monotone problems in workspaces without obstacles, the proposed method has the advantage of considering obstacles in the workspace, allowing planning translational and rotational movements for parts, and handling nonmonotone assembly sequence plans, in which the parts need to be relocated to one or more intermediate positions before moving to their final assembled position. To test and measure the effectiveness of the ASPPR, eight problems (a mixture of monotone and non-monotone, in 2D and 3D, and benchmark and new) were solved with five different sampling-based algorithms (three existing and two new) in the path planning component, and the results of 30 runs for each case were thoroughly compared and analyzed. Analytical and statistical analyses showed that our newly proposed unidirectional and bidirectional variants of the original unidirectional and bidirectional Rapidly Exploring Random Trees (RRT) outperformed other planners in the Total Path Length, Total Number of Nodes, Total Number of Edges, Total Number of Collision Checks, and Total Time performance criteria.", "Keywords": "Assembly Sequence Planning (ASP) ; Assembly Path Planning (APP) ; Assembly Sequence and Path Planner/Replanner (ASPPR) ; Assembly Interference Matrix (AIM) ; Nonmonotone assembly plan", "DOI": "10.1016/j.cad.2020.102828", "PubYear": 2020, "Volume": "123", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Industrial and Manufacturing Engineering Department, California State Polytechnic University, Pomona, 91768, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Faculty of Engineering, University of Kashan, Kashan, Iran"}], "References": []}, {"ArticleId": 80295338, "Title": "A virtual grave: legal regulations of a new virtual social phenomenon", "Abstract": "The main goal of this paper is to answer the question whether virtual cemeteries are becoming a new element of the death and funeral ritual and because of that cause legal conflicts. The article is based on the literature review, social observation and analysis of the virtual cemeteries. Also, the formal dogmatic approach was used to analyze whether this phenomena can lead to legal conflicts. The analysis discover that virtual mourning practices help the users to cope with mourning and grief after the death of a loved one, they supplement the commemoration of the dead in the real world and assist with carrying out funeral rituals. It also discovered that the application of legal regulations concerning traditional graves to virtual ones does not always give satisfactory results, although it does show a direction in which the legislators should go if they want to regulate this issue.", "Keywords": "Virtual grave ; virtual cemetery ; funeral law ; internet law ; mediatization of mourning", "DOI": "10.1080/13600834.2020.1732037", "PubYear": 2020, "Volume": "29", "Issue": "2", "JournalId": 4941, "JournalTitle": "Information & Communications Technology Law", "ISSN": "1360-0834", "EISSN": "1469-8404", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Semiotics and Rhetoric in Audio-visual Journalism in Journalism and Management Institute, John <PERSON> University of Lublin, Lublin, Poland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Media Culture Department in Journalism and Management Institute, <PERSON> University of Lublin, Lublin, Poland"}], "References": []}, {"ArticleId": 80295366, "Title": "When and why does emotional design foster learning? Evidence for situational interest as a mediator of increased persistence", "Abstract": "<p>Multiple studies have revealed the beneficial effects of emotional design on affective or motivational factors but not often on learning outcomes. We, therefore, tested one important boundary condition: the duration of the learning episode. For the implementation of emotional design, we used the video format of sketched explanation videos, which is particularly popular on online video platforms. We tested the hypothesis that emotional design is beneficial for learning but only in the later phases of studying (sustained learning). This learning benefit is mediated via triggered and maintained situational interest. Seventy‐nine students took part in a two‐group between‐subjects design. Students learned from one of two videos (16 min): either a neutral video that followed cognitive design principles or an emotional‐designed video that was based on the neutral video but enriched with emotional design elements (e.g., personalized frame story, warm colours, and tender voice). In line with our hypothesis, the emotional design led to triggered situational interest, leading to a higher maintained situational interest, which in turn fostered performance in the third (final) study phase but not in the two previous ones. Our results suggest that emotional design's positive effects are detectable especially (or only) during prolonged study situations in which motivational factors may play a more critical role. This study thus provides a potential explanation for mixed effects from previous research in this study field.</p> <h3 > Lay Description</h3> <p> What is already known about this topic <p> Many explanatory videos on video platforms use live sketching as emotional design. Emotional design influences affective or motivational factors in previous studies. These affective effects rarely translated into better learning outcomes. This mixed pattern of results of emotional design suggests boundary conditions. </p> What this paper adds <p> Emotional design improves learning in longer learning sessions. These beneficial effects were driven by situational interest. This process analysis suggests situational interest as an explanatory mechanism. Learning session length seems to explain previously ambiguous result patterns. </p> Implications for practice and/or policy <p> Live sketching videos can be used to implement emotional design. Emotional design would be of particular benefit in longer‐learning sessions. Emotional design is particularly promising in self‐guided learning arrangements. </p> </p>", "Keywords": "emotional design;motivation;multimedia learning;situational interest;video‐based learning", "DOI": "10.1111/jcal.12418", "PubYear": 2020, "Volume": "36", "Issue": "4", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Educational and Developmental Psychology, University of Freiburg, Freiburg, Germany"}, {"AuthorId": 2, "Name": "Steffen Weyreter", "Affiliation": "Entrepreneurship Education, University of Freiburg, Freiburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Educational and Developmental Psychology, University of Freiburg, Freiburg, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Educational Psychology, Justus‐Liebig University Giessen, Gießen, Germany"}], "References": []}, {"ArticleId": 80295408, "Title": "Collective reflection strategy for moderating conformity tendency and promoting reflective judgment performance", "Abstract": "<p>Reflective judgement is crucial for medical‐related practitioners in dealing with controversial issues. However, the conformity phenomenon is likely to occur and interfere with reflective judgement learning during interactive activities. Effective strategies are required to moderate the conformity behaviour tendency (CBT) and improve reflective judgement performance (RJP). This study demonstrates two significant results: (a) Compared with the guided self‐reflection learning strategy, the online collective reflection (OCR) learning strategy effectively weakened the learners' general CBT while dealing with professional controversial issues; and (b) a significantly negative correlation between the RJP achieved and the change of CBT in online environment was detected in the OCR group. The implications and potential applications in higher education were discussed. Further studies are needed to confirm the long‐term effects and the extending application to other professional studies.</p> <h3 >Lay Description</h3> <p> <h3 >Lay Description</h3> <h3 > What is already known about this topic</h3> <p> Reflective judgement is a critical competence in dealing with professional controversial issues. Conformity behaviour tendency becomes salient while encountering controversial issues, which may lead to interference with reflective judgement performance especially during class debates. </p> <h3 > What this paper adds</h3> <p> With the computer‐assisted anonymous collective reflective learning strategy, the learners performed better on reflective judgement with less inclination to exhibit conformity behaviour. </p> <h3 > Implications for practice and/or policy</h3> <p> The collective reflective learning interface simulated the function of online social media platforms, which presents the potential of widely applying the free and popular computer‐assisted technology (e.g., online social media platform) to enhance reflective judgement learning in higher education. </p> </p>", "Keywords": "collective reflection;conformity;higher education;online learning;reflective judgement", "DOI": "10.1111/jcal.12419", "PubYear": 2020, "Volume": "36", "Issue": "3", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Information and Computer Education, National Taiwan Normal University, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Y<PERSON> Z<PERSON>", "Affiliation": "Institute of Population Health Sciences, National Health Research Institutes, Miaoli, Taiwan"}, {"AuthorId": 3, "Name": "Yu<PERSON>", "Affiliation": "Department of Health and Nutrition, Chia Nan University of Pharmacy and Science, Tainan, Taiwan"}, {"AuthorId": 4, "Name": "Ku‐Chou Tai", "Affiliation": "BITs Information Technology Consultants Co., Ltd., Taipei, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Population Health Sciences, National Health Research Institutes, Miaoli, Taiwan; Division of Epidemiology and Genetics, Institute of Biomedical Sciences, Academia Sinica, Taipei, Taiwan"}], "References": []}, {"ArticleId": 80295495, "Title": "Search and Localization of a Weak Source with a Multi-robot Formation", "Abstract": "<p>This paper proposes an algorithm to guide a formation of mobile robots, subject to communication constraints, from an arbitrary position to the location of the source of a physical signal in a planar environment. The information on the signal is only based on noisy measurements of its strength collected during the mission and the signal is considered to be weak and indistinguishable from the noise in a large portion of the environment. The goal of the team is thus to search for a reliable signal and finally converge to the source location. An accurate estimation of the signal gradient is obtained by fusing the data gathered by the robots while moving in a circular formation. The algorithm proposed to steer the formation, called Gradient-biased Correlated Random Walk (GCRW), exploits the gradient estimation to bias a correlated random walk, which ensures an efficient non-oriented search motion when far from the source. The resulting strategy is so able to obtain a suitable trade-off between exploration and exploitation. Results obtained in simulated experiments, including comparisons with possible alternatives, are presented to analyze and evaluate the performance of the proposed approach.</p>", "Keywords": "Multi-robot systems; Source seeking; Robotic sensor networks", "DOI": "10.1007/s10846-019-01014-0", "PubYear": 2020, "Volume": "97", "Issue": "3-4", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "INRIA Grenoble and INSA CITI Lab Lyon, Université Grenoble-Alpes, Grenoble, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Université Grenoble Alpes, CEA, LETI, Grenoble, France"}], "References": []}, {"ArticleId": 80295512, "Title": "Object traceability graph: Applying temporal graph traversals for efficient object traceability", "Abstract": "EPC Information Services (EPCIS) is a de-facto standard for information systems for object traceability. Expert and intelligent systems complying with the standard reap benefits from its global interoperability, and thus every application can easily retrieve track and trace information of everyday-objects in an identical manner. However, existing systems are bound to go through scalability issues due to inevitable recursive queries because users are required to handle multiple transformation and aggregation of the objects instead of the systems. In the article, we propose an enhanced EPCIS system called Object Traceability Graph (OTG). The system applies a technique, temporal graph traversal, to resolve the issues. With the system, applications do not need to request recursive queries on their side. Instead, applications are able to represent their ad-hoc traceability queries in a single statement provided by the system. Then, the statement is interpreted and efficiently processed on the system side. In our evaluation, it is shown that our approach enhances the scalability of EPCIS systems by reducing the number of queries and the amount of data transmission. The proposed approach can be applied to existing expert and intelligent systems. Furthermore, we believe that an additional interface, abstracting our approach, is general enough to be included in the standard.", "Keywords": "Object traceability ; Oliot EPCIS ; ChronoGraph ; EPCIS ; Temporal graph traversal ; Information diffusion", "DOI": "10.1016/j.eswa.2020.113287", "PubYear": 2020, "Volume": "150", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Jaewook Byun", "Affiliation": "Department of Software, Sejong University, 209, Neungdong-ro, Gwangjin-gu, Seoul, 05006 Republic of Korea;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Korea Institute of Science and Technology (KAIST), 291 Daehak-ro, Yuseong-gu, Daejeon, 34141, Republic of Korea"}], "References": []}, {"ArticleId": 80295527, "Title": "A wireless slot-antenna integrated temperature-pressure-humidity sensor loaded with CSRR for harsh-environment applications", "Abstract": "In this study, a wireless slot-antenna integrated temperature-pressure-humidity (TPH) sensor loaded with complementary split ring resonator (CSRR) for harsh-environment applications was presented. The sensor is a multi-resonance structure with three separate resonant frequencies, which renders simultaneous measurements of temperature, pressure and humidity by placing sensitive elements in the corresponding CSRR structures. The sensitivity mechanisms for the temperature, pressure and humidity sensing are described in detail. The sensor was customized and fabricated on the high temperature co-fired ceramics (HTCC) using the three-dimensional co-firing and screen-printing technology. The humidity-sensitive graphene oxide modified polyimide ( [email protected] ) was used and characterized by the scanning electron microscope (SEM) and energy dispersive spectrometry (EDS). The as-prepared TPH sensor can stably work at the ambient environment of 25–300 ℃, 10–300 kPa, and 20–90 %RH. The temperature sensitivity of the TPH sensor is 133 kHz/℃. The frequency shift of the pressure sensor is 30 MHz with a highest sensitivity of 107.78 kHz/kPa at 60 %RH, and 300℃. The humidity sensor realizes a sensitivity of 389 kHz/%RH in the low humidity of 20–60 %RH and 1.52 MHz/%RH in the high humidity of 60–90 %RH at 10 kPa, and 25 ℃. The sensor described in this study has the advantages of simple structure, higher sensitivity, and lower environmental interference and has the potential for utilization in simultaneous TPH monitoring in harsh environments.", "Keywords": "Slot-antenna ; CSRR ; Wireless TPH sensor ; Harsh environment", "DOI": "10.1016/j.snb.2020.127907", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Science and Technology on Electronic Test and Measurement Laboratory, North University of China, Tai Yuan 030051, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Science and Technology on Electronic Test and Measurement Laboratory, North University of China, Tai Yuan 030051, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electronic, Electrical and Systems Engineering, University of Birmingham, Birmingham B15 2TT, UK"}, {"AuthorId": 4, "Name": "Guangjin Zhang", "Affiliation": "Science and Technology on Electronic Test and Measurement Laboratory, North University of China, Tai Yuan 030051, China"}, {"AuthorId": 5, "Name": "Shujing Su", "Affiliation": "Science and Technology on Electronic Test and Measurement Laboratory, North University of China, Tai Yuan 030051, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Science and Technology on Electronic Test and Measurement Laboratory, North University of China, Tai Yuan 030051, China"}], "References": []}, {"ArticleId": 80295538, "Title": "Design and implementation of low power and high speed multiplier using quaternary carry look-ahead adder", "Abstract": "Need of Digital Signal Processing (DSP) systems which is embedded and portable has been increasing as a result of the speed growth of semiconductor technology. Multiplier is a most crucial part in almost every DSP application. So, the low power, high speed multipliers is needed for high speed DSP. Array multiplier is one of the fast multiplier because it has regular structure and it can be designed very easily. Array multiplier is used for multiplication of unsigned numbers by using full adders and half adders. It depends on the previous computations of partial sum to produce the final output. Hence, delay is more to produce the output. In the previous work, Complementary Metal Oxide Semiconductor (CMOS) Carry Look-ahead Adders (CLA) and CMOS power gating based CLA are used for maximizing the speed of the multiplier and to improve the power dissipation with minimum delay. CMOS logic is based on radix 2(binary) number system. In arithmetic operation, major issue corresponds to carry in binary number system. Higher radix number system like Quaternary Signed Digit (QSD) can be used for performing arithmetic operations without carry. The proposed system designed an array multiplier with Quaternary Signed Digit number system (QSD) based Carry Look-Ahead Adder (CLA) to improve the performance. Generally, the quaternary devices require simpler circuit to process same amount of data than that needed in binary logic devices. Hence the Quaternary logic is applied in the CLA to improve the speed of adder and high throughput. In array multiplier architecture, instead of full adders, carry look-ahead adder based on QSD are used. This facilitates low consumption of power and quick multiplication. Tanner EDA tool is used for simulating the proposed multiplier circuit in 180 nm technology. With respect to area, Power Delay Product (PDP), Average power proposed QSD CLA multiplier is compared with Power gating CLA and CLA multiplier.", "Keywords": "Array multiplier ; Digital signal processing (DSP) ; Throughput ; Carry look-ahead adder (CLA) and quaternary logic", "DOI": "10.1016/j.micpro.2020.103054", "PubYear": 2020, "Volume": "75", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Christ the King Engineering College, Coimbatore, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sri Rama<PERSON>a Engineering College, Coimbatore, India"}], "References": []}, {"ArticleId": 80295546, "Title": "Improved community structure discovery algorithm based on penalised matrix decomposition for complex networks", "Abstract": "Complex networks are one of the main research fields in data mining. In this study, a penalised matrix decomposition-based community structure discovery algorithm (PMDCSDA) for complex networks is proposed. The complex network is firstly transformed into an adjacency matrix, which is then processed for dimension reduction via principal component analysis. Numerous clusters are produced on the basis of penalised matrix decomposition. To evaluate the performance of the proposed PMDCSDA, we compare it with several classical algorithms, such as K-means, CPM and GN, using three complex network datasets. Experimental results demonstrate that the proposed algorithm can achieve improved performance in precision, recall, F1 and Sep indicator.", "Keywords": "Big data processing ; Communication network ; Community structure ; Penalised matrix decomposition", "DOI": "10.1016/j.micpro.2020.103047", "PubYear": 2020, "Volume": "75", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of mathematics and computer science, Changsha University, Changsha, China;College of information science and engineering, Hunan University, Changsha, China"}, {"AuthorId": 2, "Name": "Hong<PERSON> Wei", "Affiliation": "Information Engineering Department, Zhangjiajie Institute of Aeronautical Engineering, Zhangjiajie, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Engineering Department, Zhangjiajie Institute of Aeronautical Engineering, Zhangjiajie, China;Corresponding author"}], "References": []}, {"ArticleId": 80295554, "Title": "Ultra-sensitive detection of Pb2+ based on DNAzymes coupling with multi-cycle strand displacement amplification (M-SDA) and nano-graphene oxide", "Abstract": "Lead is one of the toxic heavy metal ions which seriously threatens the environment and human health, even at ultra-trace level. Here, an innovative fluorescent sensor based on DNAzymes coupling with multi-cycle strand displacement amplification (M-SDA) and nano-graphene oxide (GO) was established for the first time to ultra-sensitively detect lead, which is the first attempt to combine biological nucleic acid amplification technology with GO to detect heavy metal ions. There are several advantages. Firstly, the high selectivity of DNAzymes endow the proposed sensor outstanding specificity. Secondly, the introduction of M-SDA let the proposed sensor own the high potential for ultra-sensitive detection of Pb<sup>2+</sup>. Compared with traditional SDA, the amplification efficiency of M-SDA is greatly enhanced due its intermediate products are able to induce another SDA. Thirdly, the using of GO in fluorescence ion sensor is simple and fast. Under the optimal experimental conditions, the proposed sensor exhibited a good linear from 0.01 nM to 850 nM (R<sup>2</sup>=0.99714) with the detection limit as low as 6.7 pM. Additionally, the proposed sensor could be applied to detect the real samples with good accuracy and reliability.", "Keywords": "DNAzymes ; Lead ; Strand displacement amplification ; Graphene oxide", "DOI": "10.1016/j.snb.2020.127898", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Biorheological Science and Technology (Chongqing University), Ministry of Education, College of Bioengineering, Chongqing University, Chongqing 400044, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory for Biorheological Science and Technology (Chongqing University), Ministry of Education, College of Bioengineering, Chongqing University, Chongqing 400044, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory for Biorheological Science and Technology (Chongqing University), Ministry of Education, College of Bioengineering, Chongqing University, Chongqing 400044, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Liquor Making Biology Technology and Application of Key Laboratory of Sichuan Province, College of Bioengineering, Sichuan University of Science and Engineering, Zigong 643000, PR China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Liquor Making Biology Technology and Application of Key Laboratory of Sichuan Province, College of Bioengineering, Sichuan University of Science and Engineering, Zigong 643000, PR China"}, {"AuthorId": 6, "Name": "<PERSON>hong <PERSON>", "Affiliation": "Key Laboratory for Biorheological Science and Technology (Chongqing University), Ministry of Education, College of Bioengineering, Chongqing University, Chongqing 400044, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Treatment Center of Breast Diseases, Chongqing Cancer Institute and Hospital, Chongqing University, Chongqing 400030, PR China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory for Biorheological Science and Technology (Chongqing University), Ministry of Education, College of Bioengineering, Chongqing University, Chongqing 400044, PR China;Liquor Making Biology Technology and Application of Key Laboratory of Sichuan Province, College of Bioengineering, Sichuan University of Science and Engineering, Zigong 643000, PR China;Corresponding author at: Key Laboratory for Biorheological Science and Technology (Chongqing University), Ministry of Education, College of Bioengineering, Chongqing University, Chongqing 400044, PR China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Biorheological Science and Technology (Chongqing University), Ministry of Education, College of Bioengineering, Chongqing University, Chongqing 400044, PR China;Corresponding author at: Key Laboratory for Biorheological Science and Technology (Chongqing University), Ministry of Education, College of Bioengineering, Chongqing University, Chongqing 400044, PR China"}], "References": []}, {"ArticleId": 80295560, "Title": "Asymmetric label switching resists binary imbalance", "Abstract": "In this correspondence, an asymmetric version of the label switching technique to build binary classification ensembles is introduced. The new version presents one more design parameter, the degree of asymmetry, and, consequently, it is more flexible to adapt to the problem under study. In particular, asymmetric switching allows designs that resist to class imbalance. A Bayesian analysis serves to establish how to deal with datasets for carrying out a principled rebalancing, which can be combined with other principled procedures according to the relative advantages of asymmetric switching. A number of simple experiments support the low sensitivity to imbalance and the validity of the analysis for this method of constructing ensembles.", "Keywords": "Bayesian framework ; Ensembles ; Imbalanced classification ; Iabel switching", "DOI": "10.1016/j.inffus.2020.02.004", "PubYear": 2020, "Volume": "60", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Signal Theory and Communications, Universidad Carlos III de Madrid, Avda. Universidad, 30, Legan<PERSON> 28911, Spain"}, {"AuthorId": 2, "Name": "Francisco<PERSON><PERSON>", "Affiliation": "Department of Signal Theory and Communications, Universidad Carlos III de Madrid, Avda. Universidad, 30, Legan<PERSON> 28911, Spain;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": "Department of Signal Theory and Communications, Universidad Carlos III de Madrid, Avda. Universidad, 30, Legan<PERSON> 28911, Spain"}], "References": []}, {"ArticleId": 80295572, "Title": "A ratiometric two-photon fluorescence probe for monitoring mitochondrial HOCl produced during the traumatic brain injury process", "Abstract": "Mitochondrial hypochlorous acid (HOCl) is closely related to the redox balance in mitochondria. Abnormal HOCl levels in mitochondria can induce mitochondrial inactivation and further cause cell apoptosis. Consequently, developing a reliable method for detecting HOCl in mitochondria is essential. A mitochondria-targeting two-photon fluorescence probe (Mito-P-OCl) with zero cross-talk ratiometric emission was synthesized and applied to study the traumatic brain injury process in mice. The probe gives a considerable variation (up to 58.5-fold) in its fluorescence intensity ratio (I<sub>595</sub>/I<sub>453</sub>) upon reacting with HOCl for 150 s. Mito-P-OCl exhibited excellent properties such as fast response, high sensitivity with a low detection limit of 55.4 nM, high selectivity and deep tissue penetration (270 μm). Moreover, the probe possesses the capability of monitoring endogenous HOCl in living cells and tissues using dual-emission channels and two-photon microscopy.", "Keywords": "Fluorescence probe ; Hypochlorous acid ; Two-photon bioimaging ; Traumatic brain injury (TBI)", "DOI": "10.1016/j.snb.2020.127895", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Biological Resources Protection and Utilization, School of Chemical and Environmental Engineering, Hubei Minzu University, Enshi, 445000, China;College of Chemistry and Molecular Sciences, Wuhan University, Wuhan, 430072, China;Corresponding authors at: School of Chemical and Environmental Engineering, Hubei Minzu University, Enshi, 445000, China; College of Chemistry and Molecular Sciences, Wuhan University, Wuhan, 430072, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Biological Resources Protection and Utilization, School of Chemical and Environmental Engineering, Hubei Minzu University, Enshi, 445000, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hubei Key Laboratory of Biological Resources Protection and Utilization, School of Chemical and Environmental Engineering, Hubei Minzu University, Enshi, 445000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Wang", "Affiliation": "Hubei Key Laboratory of Biological Resources Protection and Utilization, School of Chemical and Environmental Engineering, Hubei Minzu University, Enshi, 445000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry and Molecular Sciences, Wuhan University, Wuhan, 430072, China;Corresponding authors at: School of Chemical and Environmental Engineering, Hubei Minzu University, Enshi, 445000, China; College of Chemistry and Molecular Sciences, Wuhan University, Wuhan, 430072, China"}], "References": []}, {"ArticleId": 80295574, "Title": "Graph augmented triplet architecture for fine-grained patient similarity", "Abstract": "<p>Electronic Health Records (EHRs) provide rich information for the research of multiple healthcare applications that improve chance of survival in Intensive Care Units (ICU), especially the case-based decision support system, which helps physicians make effective clinical decisions in the rapidly changing environment of ICUs according to similar historical patient records. Thus, an efficient approach being able to measure clinically similarities among patients is a fundamental and critical module for the decision support system. In this paper, we propose a novel framework that derives informative EHR graphs from patient records to augment information transmission in Recurrent Neural Networks (RNNs) for fine-grained patient similarity learning, named Graph Augmented Triplet Architecture (GATA). Specifically, GATA firstly derives Dynamic Bayesian Networks (DBNs) from EHRs to reveal correlations among medical variables, then it constructs graph augmented RNNs where each unit aggregate information from variables that it conditionally dependent in DBNs. After that, the specially designed RNNs will act as the fundamental components of the Triplet architecture to measure similarities among patients. GATA has been compared to different baselines based on a real-world ICU dataset MIMIC III, and the experimental results illustrate the effectiveness of GATA in fine-grained patient similarity learning, providing a promising direction for the research on clinical decision support.</p>", "Keywords": "Patient similarity; EHRs; Graph augmentation; Triplet architecture", "DOI": "10.1007/s11280-020-00794-y", "PubYear": 2020, "Volume": "23", "Issue": "5", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing University of Aeronautics and Astronautics, Jiangsu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Queensland, Queensland, Australia"}, {"AuthorId": 3, "Name": "Dechang Pi", "Affiliation": "Nanjing University of Aeronautics and Astronautics, Jiangsu, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The University of Queensland, Queensland, Australia"}], "References": []}, {"ArticleId": 80295582, "Title": "Crosstalk-free simultaneous-source full waveform inversion with normalized seismic data", "Abstract": "Simultaneous-source algorithms can increase the efficiency of full waveform inversion (FWI) dramatically through reducing the number of times of wavefield simulations. However, the multiple sources induce the crosstalk artifacts, which severely contaminate the inversion results. To solve this problem, we provide a crosstalk-free simultaneous-source algorithm with the normalized seismic data. Sine harmonic functions with arbitrary phases are used as different wavelets in a super shot and as the encoding operator. Based on this algorithm, the crosstalk artifacts are eliminated by deblending the simultaneous-source wavefield with little additional computation. Moreover, the estimation or inversion of source wavelets at each iteration for simultaneous-source data, which is crucial for successful FWI but would severely reduce the efficiency of simultaneous-source algorithms, are avoided by normalizing the seismic data with deconvolution. Since the simultaneous-source data are deblended, the proposed algorithm is naturally applicable to the marine mobile streamer seismic data. Furthermore, it is convenient to select the reference traces for deblended data, instead of simultaneous-source data, to eliminate or unify the wavelet information by deconvolution. Finally, we verify the proposed algorithm with the synthetic data.", "Keywords": "FWI ; Simultaneous-source ; Crosstalk ; Wavelet ; Deconvolution", "DOI": "10.1016/j.cageo.2020.104460", "PubYear": 2020, "Volume": "138", "Issue": "", "JournalId": 4833, "JournalTitle": "Computers & Geosciences", "ISSN": "0098-3004", "EISSN": "1873-7803", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Geodesy and Earth’s Dynamics, Institute of Geodesy and Geophysics, CAS, 430077, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Geodesy and Earth’s Dynamics, Institute of Geodesy and Geophysics, CAS, 430077, Wuhan, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "China University of Petroleum, 102249, Beijing, China"}], "References": [{"Title": "Elastic full-waveform inversion based on GPU accelerated temporal fourth-order finite-difference approximation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "104381", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 80295606, "Title": "Cascaded Volumetric Fully Convolutional Networks for Whole-Heart and Great Vessel 3D segmentation", "Abstract": "The absence of cardiac chambers, holes in the heart and abnormal connections cause the death of hundreds of people every year. The clinical team involved in the diagnosis and treatment decisions for congenital heart disease (CHD) must be consistent in their choices. Therefore, in cases of CHD there is a need for a system that is capable of segmenting the whole heart and large blood vessels in 3D efficiently, quickly, and accurately. This article proposes to fill this need by using Cascaded Volumetric Fully Convolutional Networks. The approach proposes the use of two Volumetric Fully Convolution Networks (V-Net) in sequence. The first network aims at locating the cardiac region, while the second segment the substructures of the cardiac area and the great vessels. Both networks are trained with the 2016 data set from the MICCAI Workshop on Whole-Heart and Great Vessel Segmentation of 3D Cardiovascular MRI in Congenital Heart Disease (HVSMR). The experimental results show that the proposed method has a promising potential in decision-making in CHD cases (from MR images). The approach obtained on average 98.15% for Accuracy, 94.89% for Precision, 98.81% for Specificity Coefficient, 94.27% for Sensitivity Coefficient, 93.24% for Matthews Coefficient, 80.65% for Jaccard Index, 94.20% for <PERSON><PERSON> Coefficient, and 1.61 for the Hausdorff Distance. The proposed method enables the visualization and iteration of the segmented volume in 3D so that the doctor can analyze the entire structure of the heart along with the circulatory network.", "Keywords": "Segmentation ; Cascaded ; V-Net ; Congenital Heart Disease", "DOI": "10.1016/j.future.2020.02.055", "PubYear": 2020, "Volume": "108", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "Tao Han", "Affiliation": "DGUT-CNAM Institute, Dongguan University of Technology, Dongguan 523106, China;Corresponding authors"}, {"AuthorId": 2, "Name": "Roberto <PERSON>", "Affiliation": "Programa de Pós-Graduação em Engenharia de Teleinformática - Universidade Federal do Ceará, Fortaleza-CE, Brazil;Laboratório de Processamento de Imagens, Sinais e Computação Aplicada (LAPISCO), Instituto Federal de Educação, Ciência e Tecnologia do Ceará, Fortaleza-CE, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Programa de Pós-Graduação em Engenharia de Teleinformática - Universidade Federal do Ceará, Fortaleza-CE, Brazil;Laboratório de Processamento de Imagens, Sinais e Computação Aplicada (LAPISCO), Instituto Federal de Educação, Ciência e Tecnologia do Ceará, Fortaleza-CE, Brazil"}, {"AuthorId": 4, "Name": "Solon <PERSON><PERSON>", "Affiliation": "Programa de Pós-Graduação em Engenharia de Teleinformática - Universidade Federal do Ceará, Fortaleza-CE, Brazil;Laboratório de Processamento de Imagens, Sinais e Computação Aplicada (LAPISCO), Instituto Federal de Educação, Ciência e Tecnologia do Ceará, Fortaleza-CE, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Programa de Pós-Graduação em Informática Aplicada, Universidade de Fortaleza, Fortaleza/CE, Brazil;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Programa de Pós-Graduação em Engenharia de Teleinformática - Universidade Federal do Ceará, Fortaleza-CE, Brazil;Laboratório de Processamento de Imagens, Sinais e Computação Aplicada (LAPISCO), Instituto Federal de Educação, Ciência e Tecnologia do Ceará, Fortaleza-CE, Brazil"}], "References": []}, {"ArticleId": 80295613, "Title": "A highly sensitive and stable electrochemiluminescence immunosensor for alpha-fetoprotein detection based on luminol-AgNPs@Co/Ni-MOF nanosheet microflowers", "Abstract": "Due to their high catalytic effect on the electrochemiluminescence (ECL) of luminol, noble metal nanoparticles (NPs) have been widely used in luminol-based ECL immunosensors. However the aggregation of NPs often resulted in the decrease of ECL signal and affected the stability and durability of the sensor. In this study, a microflower-like structure made of ultrathin Co/Ni-based metal-organic frameworks (MOF) nanosheets were used as a platform for luminol-functionalized AgNPs to construct an immunosensor for tumor marker alpha-fetoprotein. The nanosheet and microflower-like assembly structure of the Co/Ni-MOF greatly improved the ECL performance of the luminol-AgNPs system, attributed to their large surface area, resistance to particle agglomeration and the high catalytic activity of the Co/Ni-MOF. The ultrathin Co/Ni-MOF was full of atomically dispersed cobalt ions and nickel ions, which can catalyze the ECL reaction of luminol and H<sub>2</sub>O<sub>2</sub>. The as-fabricated immunosensor has a good sensitivity with detection limit of 0.417 pg mL<sup>−1</sup> (S/N=3), and shows satisfactory performance in practical applications.", "Keywords": "Electrochemiluminescence ; Metal-organic framework ; Label-free immunosensor ; Alpha–fetoprotein", "DOI": "10.1016/j.snb.2020.127919", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry, Liaoning University, Shenyang 110036, China;State Key Laboratory of Electroanalytical Chemistry, Changchun Institute of Applied Chemistry, Chinese Academy of Sciences, Changchun 130022, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Electroanalytical Chemistry, Changchun Institute of Applied Chemistry, Chinese Academy of Sciences, Changchun 130022, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Electroanalytical Chemistry, Changchun Institute of Applied Chemistry, Chinese Academy of Sciences, Changchun 130022, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Electroanalytical Chemistry, Changchun Institute of Applied Chemistry, Chinese Academy of Sciences, Changchun 130022, China;Corresponding authors"}, {"AuthorId": 5, "Name": "Chunhua Ge", "Affiliation": "College of Chemistry, Liaoning University, Shenyang 110036, China;Corresponding authors"}, {"AuthorId": 6, "Name": "Xiangdong Zhang", "Affiliation": "College of Chemistry, Liaoning University, Shenyang 110036, China"}, {"AuthorId": 7, "Name": "Yongdong Jin", "Affiliation": "State Key Laboratory of Electroanalytical Chemistry, Changchun Institute of Applied Chemistry, Chinese Academy of Sciences, Changchun 130022, China;University of Chinese Academy of Sciences, Beijing 100049, China;Corresponding authors"}], "References": []}, {"ArticleId": 80295616, "Title": "Application of improved clustering algorithm in investment recommendation in embedded system", "Abstract": "The user&#x27;s investment behaviour is individual, and group-oriented, which can reflect the user&#x27;s cognitive background and interest on a certain extent. The user investment group can help users to find similar investment partners. Users can view the investment or other related people&#x27;s interests. With the development of the Internet financial industry, people&#x27;s demand for Internet financial knowledge services has become increasingly strong. Accessing financial information and conducting financial transactions through online financial platforms has become normal for investors. As a popular research area, the recommendation system can help users to better use Internet information, improve user loyalty, and promote products. In this paper, an improved kernel cluster-based incremental clustering method is proposed, and the stock information of the Shanghai Stock Exchange is used as the experimental data for cluster mining. The experimental results show that the improved kernel-based incremental clustering algorithm proposed in this paper can complete the investment recommendation for financial users. For a certain extent, it reduces the risk of financial investment, enhances the stability of the financial market, and has a strong positive effect.", "Keywords": "Embedded system ; Improved clustering algorithm ; Incremental clustering ; Recommendation system ; Stock information", "DOI": "10.1016/j.micpro.2020.103066", "PubYear": 2020, "Volume": "75", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Finance, Rongzhi College of Chongqing Technology and Business University, Chongqing, 400067, China"}], "References": []}, {"ArticleId": 80295632, "Title": "Schiff base and Lewis acid-base interaction-regulated aggregation/dispersion of gold nanoparticles for colorimetric recognition of rare-earth Sc3+ ions", "Abstract": "The design and development of colorimetric sensors for recognition of rare-earth Sc<sup>3+</sup> ions are very challenging and most of the reported methods are limited by poor sensitivity and serious interference. Herein, we unveiled a new colorimetric strategy for the sensitive and selective detection of Sc<sup>3+</sup> based on the surface plasmon resonance properties of gold nanoparticles (GNPs). The key aspect of the proposed protocol is the selection of pyridoxal phosphate (PLP), an important biomolecule involved in many biological processes, both as an effective aggregation promoter of GNPs stabilized by cysteamine (Cyst-GNPs) and a strong chelator for the selective complexation of Sc<sup>3+</sup>. PLP can covalently bind to the Cyst-GNPs through the formation of a Schiff base, which neutralizes the positive surface charges, weakens the electrostatic repulsion force among the nanoparticles, thereby triggering the agglomeration of Cyst-GNPs. Owing to the intensive Lewis acid-base interactions, the introduced Sc<sup>3+</sup> ions effectively compete with Cyst-GNPs to bind PLP, and the Cyst-GNPs tend to be dispersed in this case. A linear response from 0.1 to 3 μM is achieved for Sc<sup>3+</sup>, and a detection limit down to 0.02 μM is obtained. Negligible interfere from other ions is observed in this assay under identical experimental conditions.", "Keywords": "Gold nanoparticle ; Pyridoxal phosphate ; Scandium ; Anti-aggregation ; Schiff base ; Lewis acid-base interaction", "DOI": "10.1016/j.snb.2020.127925", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Higher Educational Key Laboratory for Nano Biomedical Technology of Fujian Province, Department of Pharmaceutical Analysis, Fujian Medical University, Fuzhou, 350004, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Higher Educational Key Laboratory for Nano Biomedical Technology of Fujian Province, Department of Pharmaceutical Analysis, Fujian Medical University, Fuzhou, 350004, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Higher Educational Key Laboratory for Nano Biomedical Technology of Fujian Province, Department of Pharmaceutical Analysis, Fujian Medical University, Fuzhou, 350004, China"}, {"AuthorId": 4, "Name": "Ya-Ping Lv", "Affiliation": "Higher Educational Key Laboratory for Nano Biomedical Technology of Fujian Province, Department of Pharmaceutical Analysis, Fujian Medical University, Fuzhou, 350004, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Higher Educational Key Laboratory for Nano Biomedical Technology of Fujian Province, Department of Pharmaceutical Analysis, Fujian Medical University, Fuzhou, 350004, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Higher Educational Key Laboratory for Nano Biomedical Technology of Fujian Province, Department of Pharmaceutical Analysis, Fujian Medical University, Fuzhou, 350004, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Analytical Chemistry for Life Science and Collaborative Innovation Center of Chemistry for Life Sciences, School of Chemistry and Chemical Engineering, Nanjing University, Nanjing, 210093, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Higher Educational Key Laboratory for Nano Biomedical Technology of Fujian Province, Department of Pharmaceutical Analysis, Fujian Medical University, Fuzhou, 350004, China;Corresponding author"}], "References": []}, {"ArticleId": 80295647, "Title": "Electrochemical biosensor based on gold nanoflowers-encapsulated magnetic metal-organic framework nanozymes for drug evaluation with in-situ monitoring of H2O2 released from H9C2 cardiac cells", "Abstract": "A novel electrochemical biosensor was fabricated for the determination of hydrogen peroxide (H<sub>2</sub>O<sub>2</sub>) by electrodeposition of gold nanoflowers (AuNFs) on molybdenum disulfide (MoS<sub>2</sub>) nanosheets-supported magnetic metal-organic framework (MMOF) Fe<sub>3</sub>O<sub>4</sub>@ZIF-8 hybrid nanozymes. Fe<sub>3</sub>O<sub>4</sub>@ZIF-8 nanomaterial was selected on account of the high peroxidase-mimicking activity of magnetic Fe<sub>3</sub>O<sub>4</sub> nanoparticle and the large pore size and surface area of metal-organic framework ZIF-8. However, we found that the MMOF Fe<sub>3</sub>O<sub>4</sub>@ZIF-8 nanomaterial was easily dissoluted into the electrolyte solution followed by an unreproducible electrochemical response. MoS<sub>2</sub> nanosheets and one-step electrodeposition of gold nanostructures can largely enhance the long-term stability, conductivity and catalytic performance. With the help of MoS<sub>2</sub> nanosheets and electrodeposited gold thin layer, it can have an important impact on the retaining of surface states of Fe<sub>3</sub>O<sub>4</sub>@ZIF-8 nanomaterials to keep the electrocatalytic activity. The obtained hybrid nanocomposites (AuNFs/Fe<sub>3</sub>O<sub>4</sub>@ZIF-8-MoS<sub>2</sub>) exhibited prominent electrocatalytic activity for the reduction of H<sub>2</sub>O<sub>2</sub> and the fabricated biosensor detected H<sub>2</sub>O<sub>2</sub> with a low detection limit of 0.9 μM and an ultra-wide linear detection range of 5 μM–120 mM. Moreover, this electrochemical biosensor was successfully performed to detect H<sub>2</sub>O<sub>2</sub> concentration released from H9C2 cardiac cells under drug stimulation. These results indicated that this modified electrode can serve as a general and powerful platform to detect other disease biomarkers for drug evaluation.", "Keywords": "Electrochemical biosensor ; Hydrogen peroxide ; Magnetic metal-organic frameworks ; Electrodeposition ; Gold nanostructures", "DOI": "10.1016/j.snb.2020.127909", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for Drug Research and Development, Guangdong Pharmaceutical University, Guangzhou, 510006, China;School of Pharmaceutical Sciences, Sun Yat-Sen University, Guangzhou, 510006, China"}, {"AuthorId": 2, "Name": "Yuehuai Hu", "Affiliation": "School of Pharmaceutical Sciences, Sun Yat-Sen University, Guangzhou, 510006, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Pharmaceutical Sciences, Sun Yat-Sen University, Guangzhou, 510006, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Pharmaceutical Sciences, Sun Yat-Sen University, Guangzhou, 510006, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Pharmaceutical Sciences, Sun Yat-Sen University, Guangzhou, 510006, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Drug Research and Development, Guangdong Pharmaceutical University, Guangzhou, 510006, China;Corresponding author"}], "References": []}, {"ArticleId": 80295685, "Title": "Semi-supervised hyperspectral image classification algorithm based on graph embedding and discriminative spatial information", "Abstract": "Image classification is one of the important techniques in computer vision. Due to the limited access of labeled samples in hyperspectral images, semi-supervised learning (SSL) methods have been widely applied in hyperspectral image classification. Graph based semi-supervised learning provides an effective solution to model data in classification problems, of which graph construction is the critical step. In this paper we employ the graphs constructed with a typical manifold learning method-locally linear embedding (LLE), based on which semi-supervised classification is then conducted. To exploit the valuable spatial information contained in hyperspectral images, discriminative spatial information (DSI) is then extracted. The proposed classification method is evaluated using three real hyperspectral data sets, revealing state-of-art performance when compared with different classification methods.", "Keywords": "Computer vision ; Graph embedding ; Locally linear embedding ; Image Classification", "DOI": "10.1016/j.micpro.2020.103070", "PubYear": 2020, "Volume": "75", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information, Nanjing Vocational College of Information Technology, Nanjing, Jiangsu 210023, China"}], "References": []}, {"ArticleId": 80295706, "Title": "The data on psychological adaptation during polar winter-overs in Sub-Antarctic and Antarctic stations", "Abstract": "<p>The data presented in this article relate to the research article entitled &quot;assessing psychological adaptation during polar winter-overs: The isolated and confined environments questionnaire (ICE-Q)&quot; [1]. These data were acquired in order to develop a standardized instrument - the ICE-Q - designed to assess psychological adaptation within isolated, confined, and extreme environments. A total of 140 winterers from several sub-Antarctic (Amsterdam, Crozet, Kerguelen) and Antarctic (Concordia, Terre Adélie) stations voluntarily participated. Data were collected by multiple self-report questionnaires including a wide variety of well-known and validated questionnaires to record the winterers' responses to polar stations. Data were gathered across two or three winter seasons within each of the 5 polar stations to ensure sufficiently large sample. From four to seven measurement time along a one-year period were proposed to the participants, resulting in 479 momentary assessments. Results of exploratory factor analyses, confirmatory factor analyses, exploratory structural equation modelling, reliability analyses, and test-retest provided strong evidence for the construct validity of the ICE-Q (19-item 4-factor questionnaire). The four factors were social, emotional, occupational and physical. Future studies would examine the dynamic of psychological adaptation in isolated, confined and/or extreme environments during polar missions.</p><p>© 2020 The Authors.</p>", "Keywords": "Emotional changes;Extreme environment;Isolated and confined environment;Occupational investment;Physical fatigue;Polar stations;Psychological adaptation;Social relationships", "DOI": "10.1016/j.dib.2020.105324", "PubYear": 2020, "Volume": "29", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Bourgogne Franche-Comté, Laboratory Psy-DREPI (EA 7458), Dijon, France."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Lyon, University of Claude Bernard Lyon 1, Laboratory of Vulnerability and Innovation in Sport, France."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychology, The University of British Columbia, Vancouver, Canada."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Bourgogne Franche-Comté, Laboratory Psy-DREPI (EA 7458), Dijon, France."}], "References": []}, {"ArticleId": 80295707, "Title": "<PERSON><PERSON><PERSON><PERSON>", "Abstract": "Sensor-driven systems often need to map sensed data into meaningfully labelled activities to classify the phenomena being observed. A motivating and challenging example comes from human activity recognition in which smart home and other datasets are used to classify human activities to support applications such as ambient assisted living, health monitoring, and behavioural intervention. Building a robust and meaningful classifier needs annotated ground truth, labelled with what activities are actually being observed—and acquiring high-quality, detailed, continuous annotations remains a challenging, time-consuming, and error-prone task, despite considerable attention in the literature. In this article, we use knowledge-driven ensemble learning to develop a technique that can combine classifiers built from individually labelled datasets, even when the labels are sparse and heterogeneous. The technique both relieves individual users of the burden of annotation and allows activities to be learned individually and then transferred to a general classifier. We evaluate our approach using four third-party, real-world smart home datasets and show that it enhances activity recognition accuracies even when given only a very small amount of training data.", "Keywords": "Human activity recognition; clustering; ensemble learning; smart home; transfer learning", "DOI": "10.1145/3368272", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of St Andrews, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of St Andrews, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dipartimento di Scienze e Metodi dell’Ingegneria, Universita’ di Modena e Reggio Emilia, Italy"}], "References": []}, {"ArticleId": 80295711, "Title": "A new midshelf record in the northern Bay of Biscay (NE Atlantic, CBT-CS11 core): Sedimentological, geochemical and palynological data over the last 7 kyrs", "Abstract": "<p>The high-time resolution (∼70 years in average) multi-proxy analysis conducted on the mid-shelf core CBT-CS11 (47°46.429'N; 4°25.308'W; 73 m depth; 3.96 m long; NW France, S Brittany) revealed the complexity of the palaeohydrological and palaeoclimatic signals recorded over the last 7 kyrs in the recently published paper: &quot;Oceanic <i>versus</i> continental influences over the last 7 kyrs from a midshelf record in the northern Bay of Biscay (NE Atlantic)&quot; [1]. This study presents the whole CBT-CS11 dataset discussed in [1] including sedimentological (XRF and grain-size (total from [1] and CaCO<sub>3</sub>-free from [2]) analyses), geochemical (oxygen and carbon stable isotopes on two different benthic foraminiferal species: <i>Ammonia falsobeccarii</i> from [1] and <i>Cibicides refulgens</i> from [2]) analyses) as well as palynological (dinoflagellate cyst and pollen assemblages from [1]) data. The present study also describes the different statistical tests from which ecological groups have been established from palynological indicators in [1].</p><p>© 2020 The Authors.</p>", "Keywords": "Holocene ; NE Atlantic Ocean ; Pollen assemblages ; Dinoflagellate cyst assemblages ; Stable isotopes ; Grain-size analysis ; XRF", "DOI": "10.1016/j.dib.2020.105323", "PubYear": 2020, "Volume": "29", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Univ Brest (UBO), CNRS, UMR 6538 Laboratoire Géosciences Océan (LGO), F-29280, Plouzané, France."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Univ Brest (UBO), CNRS, UMR 6538 Laboratoire Géosciences Océan (LGO), F-29280, Plouzané, France."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Univ Brest (UBO), CNRS, UMR 6538 Laboratoire Géosciences Océan (LGO), F-29280, Plouzané, France."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Univ Brest (UBO), CNRS, UMR 6538 Laboratoire Géosciences Océan (LGO), F-29280, Plouzané, France."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LPG-BIAF UMR-CNRS 6112, Un<PERSON> Angers, Univ Nantes, CNRS, UFR Sciences, 2 Bd Lavoisier, F-49045, Angers, France."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LPG-BIAF UMR-CNRS 6112, Un<PERSON> Angers, Univ Nantes, CNRS, UFR Sciences, 2 Bd Lavoisier, F-49045, Angers, France."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, Géosciences Marines, Centre de Bretagne. ZI Pointe du diable, CS 10070, F-29280, Plouzané, France."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, Géosciences Marines, Centre de Bretagne. ZI Pointe du diable, CS 10070, F-29280, Plouzané, France."}], "References": []}, {"ArticleId": 80295767, "Title": "Pair-based Uncertainty and Diversity Promoting Early Active Learning for Person Re-identification", "Abstract": "The effective training of supervised Person Re-identification (Re-ID) models requires sufficient pairwise labeled data. However, when there is limited annotation resource, it is difficult to collect pairwise labeled data. We consider a challenging and practical problem called Early Active Learning, which is applied to the early stage of experiments when there is no pre-labeled sample available as references for human annotating. Previous early active learning methods suffer from two limitations for Re-ID. First, these instance-based algorithms select instances rather than pairs, which can result in missing optimal pairs for Re-ID. Second, most of these methods only consider the representativeness of instances, which can result in selecting less diverse and less informative pairs. To overcome these limitations, we propose a novel pair-based active learning for Re-ID. Our algorithm selects pairs instead of instances from the entire dataset for annotation. Besides representativeness, we further take into account the uncertainty and the diversity in terms of pairwise relations. Therefore, our algorithm can produce the most representative, informative, and diverse pairs for Re-ID data annotation. Extensive experimental results on five benchmark Re-ID datasets have demonstrated the superiority of the proposed pair-based early active learning algorithm.", "Keywords": "Active learning; person re-identification", "DOI": "10.1145/3372121", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Language Technologies Institute, Carnegie Mellon University, Pittsburgh, Pennsylvania,"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Monash University, Melbourne, VIC, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centre for Artificial Intelligence, University of Technology Sydney, Sydney, NSW, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Monash University, Melbourne, VIC, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Artificial Intelligence, Wenzhou University, Wenzhou, Zhejiang, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Centre for Artificial Intelligence, University of Technology Sydney, Sydney, NSW, Australia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Language Technologies Institute, Carnegie Mellon University, Pittsburgh, Pennsylvania,"}], "References": []}, {"ArticleId": 80295771, "Title": "Flexible Multi-modal Hashing for Scalable Multimedia Retrieval", "Abstract": "Multi-modal hashing methods could support efficient multimedia retrieval by combining multi-modal features for binary hash learning at the both offline training and online query stages. However, existing multi-modal methods cannot binarize the queries, when only one or part of modalities are provided. In this article, we propose a novel\n Flexible Multi-modal Hashing \n (FMH) method to address this problem. FMH learns multiple modality-specific hash codes and multi-modal collaborative hash codes simultaneously within a single model. The hash codes are flexibly generated according to the newly coming queries, which provide any one or combination of modality features. Besides, the hashing learning procedure is efficiently supervised by the pair-wise semantic matrix to enhance the discriminative capability. It could successfully avoid the challenging symmetric semantic matrix factorization and\n O \n (\n n \n 2 \n ) storage cost of semantic matrix. Finally, we design a fast discrete optimization to learn hash codes directly with simple operations. Experiments validate the superiority of the proposed approach.", "Keywords": "Multi-modal hashing; efficient discrete optimization", "DOI": "10.1145/3365841", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shandong Computer Science Center (National Supercomputer Center in Jinan), Qilu University of Technology (Shandong Academy of Sciences), Jinan, China"}, {"AuthorId": 4, "Name": "Jingjing Li", "Affiliation": "School of Computer Science and Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China"}], "References": []}, {"ArticleId": 80295790, "Title": "Enhanced Double-Carrier Word Embedding via Phonetics and Writing", "Abstract": "Word embeddings, which map words into a unified vector space, capture rich semantic information. From a linguistic point of view, words have two carriers, speech and writing. Yet the most recent word embedding models focus on only the writing carrier and ignore the role of the speech carrier in semantic expressions. However, in the development of language, speech appears before writing and plays an important role in the development of writing. For phonetic language systems, the written forms are secondary symbols of spoken ones. Based on this idea, we carried out our work and proposed double-carrier word embedding (DCWE). We used DCWE to conduct a simulation of the generation order of speech and writing. We trained written embedding based on phonetic embedding. The final word embedding fuses writing and phonetic embedding. To illustrate that our model can be applied to most languages, we selected Chinese, English, and Spanish as examples and evaluated these models through word similarity and text classification experiments.", "Keywords": "Word embedding; linguistic; phonetic embedding", "DOI": "10.1145/3344920", "PubYear": 2020, "Volume": "19", "Issue": "2", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China"}, {"AuthorId": 2, "Name": "Xi<PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China"}, {"AuthorId": 4, "Name": "Zhiguo Lu", "Affiliation": "Library of Shanghai University, Shanghai University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Communication &amp; Information Engineering, Shanghai University, Shanghai, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON> Wei", "Affiliation": "College of Computer Science and Technology, Zhejiang University, Zhejiang, China"}], "References": []}, {"ArticleId": 80295797, "Title": "Single Image Snow Removal Using Sparse Representation and Particle Swarm Optimizer", "Abstract": "Images are often corrupted by natural obscuration (e.g., snow, rain, and haze) during acquisition in bad weather conditions. The removal of snowflakes from only a single image is a challenging task due to situational variety and has been investigated only rarely. In this article, we propose a novel snow removal framework for a single image, which can be separated into a sparse image approximation module and an adaptive tolerance optimization module. The first proposed module takes the advantage of sparsity-based regularization to reconstruct a potential snow-free image. An auto-tuning mechanism for this framework is then proposed to seek a better reconstruction of a snow-free image via the time-varying inertia weight particle swarm optimizers in the second proposed module. Through collaboration of these two modules iteratively, the number of snowflakes in the reconstructed image is reduced as generations progress. By the experimental results, the proposed method achieves a better efficacy of snow removal than do other state-of-the-art techniques via both objective and subjective evaluations. As a result, the proposed method is able to remove snowflakes successfully from only a single image while preserving most original object structure information.", "Keywords": "Snow removal; image restoration; sparse representation", "DOI": "10.1145/3372116", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Taipei University of Technology, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Taiwan University, Taipei, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Yuan Ze University, Taoyuan, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Taiwan University, Taipei, Taiwan"}], "References": []}]