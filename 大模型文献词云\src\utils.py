import re

class Utils:
    def __init__(self):
        pass

    # 将传入的字符串按各个标点符号（除了空格）分割，清洗前后空格后返回分割后的列表
    def split_string(self, string):
        if string is None:
            return []
        items = re.split(r'[;,]', string)
        return [item.strip() for item in items if item.strip()]
    
    # 去除传入字典的空值
    def remove_empty_value(self, dict):
        return {k: v for k, v in dict.items() if v is not None}
    
