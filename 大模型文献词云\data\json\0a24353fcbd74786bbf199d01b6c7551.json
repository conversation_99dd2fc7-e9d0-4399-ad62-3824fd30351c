[{"ArticleId": 89728473, "Title": "Prediction and Value of Ultrasound Image in Diagnosis of Fetal Central Nervous System Malformation under Deep Learning Algorithm", "Abstract": "<p> This study was to explore the application of deep learning neural network (DLNN) algorithms to identify and optimize the ultrasound image so as to analyze the effect and value in diagnosis of fetal central nervous system malformation (CNSM). 63 pregnant women who were gated in the hospital were suspected of being fetal CNSM and were selected as the research objects. The ultrasound images were reserved in duplicate, and one group was defined as the control group without any processing, and images in the experimental group were processed with the convolutional neural network (CNN) algorithm to identify and optimize. The ultrasound examination results and the pathological test results before, during, and after the pregnancy were observed and compared. The results showed that the test results in the experimental group were closer to the postpartum ultrasound and the results of the pathological result, but the results in both groups showed no statistical difference in contrast to the postpartum results in terms of similarity ( P > 0.05 ). In the same pregnancy stage, the ultrasound examination results of the experimental group were higher than those in the control group, and the contrast was statistically significant ( P 0.05 ); in the different pregnancy stages, the ultrasound examination results in the second trimester were more close to the postpartum examination results, showing statistically obvious difference ( P 0.05 ). In conclusion, ultrasonic image based on deep learning was higher in CNSM inspection; and ultrasonic technology had to be improved for the examination in different pregnancy stages, and the accuracy of the examination results is improved. However, the amount of data in this study was too small, so the representative was not high enough, which would be improved. </p>", "Keywords": "", "DOI": "10.1155/2021/6246274", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Ultrasound Imaging, Suizhou Central Hospital, Suizhou 441300, Hubei, China"}], "References": [{"Title": "Application of the best evacuation model of deep learning in the design of public structures", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "103975", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 89728531, "Title": "The Price Impact of Order Book Events from a Dimension of Time", "Abstract": "<p>We propose a new linear model to explain the price move by Level-2 high-frequency data in Chinese mainland stock market. In Chinese stock market, the cancellation ratio is very low, and imbalanced order flow prevails most of the time in the trading periods. From time dimension viewpoint, we find the difference of efficiency of limit orders executed, respectively, in bid/ask limit order book, order execution imbalance (OEI), could improve the classic model of <PERSON> et al. (2014) based on market microstructure of Chinese mainland stock market. In particular, when market’s liquidity is booming, our model’s explanatory power and R-squared increased sharply. And the correlations of OEI are very high that may be exploited to predict the price move in the next time window for doing high-frequency trading.</p>", "Keywords": "", "DOI": "10.1155/2021/9949565", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "Wentao Chi", "Affiliation": "School of Financial Technology, Shanghai Lixin University of Accounting and Finance, Shanghai 201209, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shandong Management University, Jinan, Shandong 250357, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Financial Technology, Shanghai Lixin University of Accounting and Finance, Shanghai 201209, China"}], "References": [{"Title": "Green Technology Collaboration Network Analysis of China’s Transportation Sector: A Patent-Based Analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}]}, {"ArticleId": ********, "Title": "Flight schedule adjustment for hub airports using multi-objective optimization", "Abstract": "<p>Based on the concept of “passengers self-help hubbing,” we build a flight schedule optimization model where maximizing the number of feasible flight connections, indicating transfer opportunities, as one objective and minimizing total slot displacements as the other objective. At the same time, the “Demand Smoothing Model” is introduced into the flight schedule optimization model to reduce the queuing delays for arrival and departure flights. We take into account all aircraft itineraries, the difficulty level of schedule coordination, and the maximum displacement of any single flight acceptable to airlines when optimizing flight schedule. Given an original schedule, the model produces a feasible modified schedule that obeys the slot limits specified for an airport without canceling any flights, increases transfer opportunities, and improves on-time performance for hub airports while reducing interference with airline scheduling preferences. The model was verified with the operating data of the Urumqi international airport, and the results show that minor adjustments to flight schedules can increase the transfer opportunities of the airport and significantly reduce flight queuing delays.</p>", "Keywords": "", "DOI": "10.1515/jisys-2020-0114", "PubYear": 2021, "Volume": "30", "Issue": "1", "JournalId": 7501, "JournalTitle": "Journal of Intelligent Systems", "ISSN": "0334-1860", "EISSN": "2191-026X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Airspace Planning and Aeronautical Information, College of Air Traffic Management, Civil Aviation University of China , Tianjin 300300 , China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Airspace Planning and Aeronautical Information, College of Air Traffic Management, Civil Aviation University of China , Tianjin 300300 , China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Avionics, Sino-European Institute of Aviation Engineering, Civil Aviation University of China , Tianjin 300300 , China"}], "References": []}, {"ArticleId": 89728570, "Title": "An intelligent algorithm to reduce and eliminate coverage holes in the mobile network", "Abstract": "<p>The need for services in the world of telecommunications and the prosperity and rapid development of this sector around the world has led mobile phone companies to compete to provide the best services to customers. One of these devices used for communications is the mobile phone. The mobile phone is simply an electronic device that is mainly serving unwired telecommunications through a cellular network of particular base stations known as cell sites. There are many obstacles or problems in the telecommunication field, and the relevant institutions and companies should find solutions to these obstacles and problems. One of these obstacles in telecommunications is the coverage holes. Coverage holes occur when the location of the mobile phone is set in midway between the two base transceiver stations (BTSs). This causes an abnormal interruption in communication until the user crosses the coverage holes area. This paper presents an intelligent algorithm as a set of technical steps that can be used to improve the communication services of the mobile phone network and to solve the communication problems via the reduction of the coverage holes between two BTSs. It suggests a method that could alleviate this problem using a strategy that reduces the coverage holes by developing an intelligent algorithm system to receive signal strength indicators and use AT command “+CREG” to disconnect and reconnect to available BTS within an acceptable energy level. As a result, the connection turned from marginal into good connection.</p>", "Keywords": "", "DOI": "10.1515/jisys-2021-0046", "PubYear": 2021, "Volume": "30", "Issue": "1", "JournalId": 7501, "JournalTitle": "Journal of Intelligent Systems", "ISSN": "0334-1860", "EISSN": "2191-026X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>ed <PERSON>", "Affiliation": "Department of Computer Science, College of Education, Mustansiriyah University , Baghdad , Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Education, Mustansiriyah University , Baghdad , Iraq"}], "References": []}, {"ArticleId": 89728623, "Title": "Stripline\n forward‐wave \n directional coupler based on double\n multi‐via \n mushroom and\n short‐circuited \n branch line", "Abstract": "<p>A stripline forward-wave directional coupler is proposed using periodical double multi-via mushroom structures and short-circuited branch lines in this paper. By adding the short-circuited branch lines to the coupled lines and the patch of the upper or lower mushroom structure, the operating bandwidth is enhanced for the unit cell design. The even/odd mode phase difference of one unit cell is located within 20 ~ 30° from 6.1 to 6.7 GHz with an acceptable even/odd mode Bloch impedance mismatching. Finally, a prototype is fabricated by multi-layer printed circuit broad technology and measured. The agreement is achieved between the simulated and measured results.</p>", "Keywords": "Bloch-Floquet;forward-wave directional coupler;multi-via mushroom;self-packaging;stripline", "DOI": "10.1002/mmce.22850", "PubYear": 2021, "Volume": "31", "Issue": "11", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication, Jiangsu Vocational College of Electronics and Information, Huai'an, China"}, {"AuthorId": 2, "Name": "Yongrong Shi", "Affiliation": "Key Laboratory of Dynamic Cognitive System of Electromagnetic Spectrum Space, Ministry of Industry and Information Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Communication Engineering, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Communication Engineering, Nanjing University of Science and Technology, Nanjing, China; School of Electronic and Information Engineering, South China University of Technology, Guangzhou, China"}], "References": [{"Title": "Dual‐band balanced coupler with wideband common‐mode suppression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "", "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering"}]}, {"ArticleId": 89728773, "Title": "Surface approximations using generalized NURBS", "Abstract": "<p>We extend the concept of generalized NURBS (GNURBS), recently introduced by the authors for parametric curves, to bivariate parametric surfaces. These generalizations are obtained via either explicit or implicit decoupling of the weights along different physical coordinates. This decoupling allows for treating the weights as additional degrees of freedom in a wider range of applications compared to classic NURBS surfaces, providing additional flexibility and increased control. This proposed concept effectively improves the capability of NURBS and alleviates its deficiencies in certain applications. In particular, we will demonstrate that GNURBS can be effectively used for improved approximation of certain class of surfaces such as helicoids, revolved surfaces and minimal surfaces. It will also be established that these proposed generalizations can be exactly transformed to equivalent, but higher order, classic NURBS surfaces, ensuring a strong theoretical foundation. Finally, a comprehensive MATLAB toolbox, GNURBS3D-Lab, has been developed and introduced in order to better demonstrate the behavior and properties of GNURBS surfaces compared to classic NURBS.</p>", "Keywords": "GNURBS; Bivariate surfaces; Directional weights; Non-isoparametric", "DOI": "10.1007/s00366-021-01483-8", "PubYear": 2022, "Volume": "38", "Issue": "5", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, UW-Madison, Madison, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, UW-Madison, Madison, USA"}], "References": [{"Title": "Generalizations of non-uniform rational B-splines via decoupling of the weights: theory, software and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1831", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 89729047, "Title": "Risk assessment for health insurance using equation modeling and machine learning", "Abstract": "<p>Due to the advancement of medical sensor technologies new vectors can be added to the health insurance packages. Such medical sensors can help the health as well as the insurance sector to construct mathematical risk equation models with parameters that can map the real-life risk conditions. In this paper parameter analysis in terms of medical relevancy as well in terms of correlation has been done. Considering it as ‘inverse problem’ the mathematical relationship has been found and are tested against the ground truth between the risk indicators. The pairwise correlation analysis gives a stable mathematical equation model can be used for health risk analysis. The equation gives coefficient values from which classification regarding health insurance risk can be derived and quantified. The Logistic Regression equation model gives the maximum accuracy (86.32%) among the Ridge Bayesian and Ordinary Least Square algorithms. Machine learning algorithm based risk analysis approach was formulated and the series of experiments show that K-Nearest Neighbor classifier has the highest accuracy of 93.21% to do risk classification.</p>", "Keywords": "", "DOI": "10.3233/KES-210065", "PubYear": 2021, "Volume": "25", "Issue": "2", "JournalId": 18404, "JournalTitle": "International Journal of Knowledge-based and Intelligent Engineering Systems", "ISSN": "1327-2314", "EISSN": "1875-8827", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89729224, "Title": "The Secret to Finding a Match: A Field Experiment on Choice Capacity Design in an Online Dating Platform", "Abstract": "<p>Online matching platforms require new approaches to market design because firms can now control many aspects of the search and interaction process through various IT-enabled features. Although choice capacity—the number of candidates a user can view and select—is a key design feature of online matching platforms, its effect on engagement and matching outcomes remains unclear. We examine the effect of different choice capacities on the number of choices and matches made on a platform by conducting a randomized field experiment in collaboration with an online dating platform. Specifically, we (1) select users who are of a similar age and live in the same geographical location, (2) design four treatment groups with different choice capacities in which users can only interact with other users in the same group, and (3) randomly assign the users to the treatment groups. We find that providing more choice capacity to male and female users has different effects on choice behaviors and matching outcomes. Although increasing the choice capacity of male users yields the highest engagement, increasing the choice capacity of female users is the most effective method to increase matching outcomes. We posit and empirically demonstrate four mechanisms underlying the effectiveness of different choice capacity designs. Furthermore, we generalize our findings to other online matching platforms and discuss how choice capacity can be designed to increase engagement and matching outcomes.</p>", "Keywords": "online matching platform design; choice capacity; online dating; field experiment", "DOI": "10.1287/isre.2021.1028", "PubYear": 2022, "Volume": "33", "Issue": "4", "JournalId": 11444, "JournalTitle": "Information Systems Research", "ISSN": "1047-7047", "EISSN": "1526-5536", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Fox School of Business, Temple University, Philadelphia, Pennsylvania 19122"}, {"AuthorId": 2, "Name": "Hyungsoo Lim", "Affiliation": "Hong Kong University of Science and Technology, Clear Water Bay, Kowloon, Hong Kong"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Clear Water Bay, Kowloon, Hong Kong"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Baruch College, The City University of New York, New York, New York 10010"}], "References": []}, {"ArticleId": 89729609, "Title": "Testing the reliability and validity of risk assessment methods in Human Factors and Ergonomics", "Abstract": "<p>There is growing interest in the use of systems-based risk assessment methods in Human Factors and Ergonomics (HFE). The purpose of this study was to test the intra-rater reliability and criterion-referenced concurrent validity of three systems-based risk assessment approaches: (i) the Systems-Theoretic Process Analysis (STPA) method; (ii) the Event Analysis of Systemic Teamwork Broken Links (EAST-BL) method; and, (iii) the Network Hazard Analysis and Risk Management System (Net-HARMS) method. Reliability and validity measures were obtained using the Signal Detection Theory (SDT) paradigm. Whilst STPA identified the highest number of risks, the findings indicate a weak to moderate level of reliability and validity for STPA, EAST-BL and Net-HARMS. There were no statistically significant differences between the methods across analyses. The results suggest that there is merit to the continued use of systems-based risk assessment methods following a series of methodological extensions that aim to enhance the reliability and validity of future applications. <b>Practitioner summary</b> The three risk assessment methods produced weak to moderate levels of stability and accuracy regarding their capability to predict risks. There is a pressing need to further test the reliability and validity of safety methods in Human Factors and Ergonomics.</p>", "Keywords": "EAST-BL;Net-HARMS;Risk assessment;STPA;reliability validity", "DOI": "10.1080/00140139.2021.1962969", "PubYear": 2022, "Volume": "65", "Issue": "3", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for Human Factors and Sociotechnical Systems, University of the Sunshine Coast, Sippy Downs, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Centre for Human Factors and Sociotechnical Systems, University of the Sunshine Coast, Sippy Downs, Australia;Transportation Research Group, Faculty of Engineering and Physical Sciences, University of Southampton, Southampton, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centre for Human Factors and Sociotechnical Systems, University of the Sunshine Coast, Sippy Downs, Australia;Centre for Sustainable Road Freight, School of Energy, Geoscience, Infrastructure and Society, Heriot-Watt University, Scotland, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Human Factors and Complex Systems Group, Design School, Loughborough University, Leicestershire, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Centre for Human Factors and Sociotechnical Systems, University of the Sunshine Coast, Sippy Downs, Australia;Transportation Research Group, Faculty of Engineering and Physical Sciences, University of Southampton, Southampton, UK"}], "References": [{"Title": "Evaluation of construct and criterion-referenced validity of a systems-thinking based near miss reporting form", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "2", "Page": "210", "JournalTitle": "Ergonomics"}, {"Title": "Systems thinking-based risk assessment methods applied to sports performance: A comparison of STPA, EAST-BL, and Net-HARMS in the context of elite women's road cycling", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "91", "Issue": "", "Page": "103297", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 89729672, "Title": "Complex Surface Deformation of the Coalfield in the Northwest Suburbs of Xuzhou from 2015 to 2020 Revealed by Time Series InSAR", "Abstract": "The coalfield in the northwest suburbs of Xuzhou has suffered from extensive land subsidence; however, the temporal nonlinearity and corresponding spatial variation of its surface deformation has not been adequately explored. This study revealed complex surface deformation of the study area from 2015 to 2020 using time series InSAR. A general trend from sharp subsidence to moderated subsidence or even uplift is observed. Three main characteristic types of deformation time series, namely “Subsidence (S)”, “Subsidence–Uplift (S-U)” and “Subsidence–Uplift–Subsidence (S-U-S)”, were detected. Generally, mining activities control the fundamental constraining factors for the surface deformation. Mine closures affect the date of reversal from subsidence to uplift, or in other places the return to subsidence after uplift. Higher deformation rates in preceding sections of the deformation time series are generally correlated with later reversals of deformation trend and higher deformation rates of the succeeding sections of the time series. However, universally applicable spatial and temporal patterns of deformation were not detected, suggesting that surface deformation in the study area is a very complex process. Detailed process and mechanism analysis of surface deformation in the study area should focus on specific mines and pay particular attentions on dewatering and subsequent flooding of mines. Le bassin houiller de la banlieue nord-ouest de Xuzhou a souffert d’un affaissement important des sols, mais la non-linéarité temporelle et la variation spatiale correspondante de la déformation de la surface n’ont pas été suffisamment examinées. Cette étude a révélé une déformation complexe de la surface de la zone d’étude de 2015 à 2020 à l’aide de séries chronologiques InSAR. On observe une tendance générale allant d’un affaissement marqué à un affaissement modéré ou même à un soulèvement. Trois principaux types caractéristiques de séries chronologiques de déformation, à savoir « Affaissement (S) », « Affaissement–soulèvement (S-U) » et « Affaissement–soulèvement–Affaissement (S-U-S) », ont été détectés. En général, les activités minières contrôlent les facteurs contraignants fondamentaux de la déformation de la surface. Les fermetures de mines ont une incidence sur la date d’inversion de l’affaissement au soulèvement, ou à d’autres endroits sur le retour à l’affaissement après le soulèvement. Des taux de déformation plus élevés de certaines portions de la série chronologique de déformation sont généralement corrélés avec des inversions ultérieures de la tendance à la déformation et des taux de déformation plus élevés des potions suivantes de la série. Cependant, des modèles spatiaux et temporels universellement applicables de déformation n’ont pas été détectés, ce qui suggère que la déformation de la surface dans la zone d’étude est un processus très complexe. L’analyze détaillée des processus et des mécanismes de déformation de surface devrait être axée sur des mines spécifiques et accorder une attention particulière à l’assèchement et à l’inondation ultérieure des mines. Acknowledgments The authors of this paper would like to thank ESA for providing SAR data and the authors of StaMPS and TRAIN for providing free InSAR processing scripts. Dr. Yuming Wu, Ms. Xiaoxia Zhao, and Mr. Chaodong Zhou are appreciated for helping in the InSAR processing. Professor Timothy Andrew Warner is particularly appreciated for improving the manuscript. Disclosure statement No potential conflict of interest was reported by the author(s). Additional information Funding This research was funded by the National Natural Science Foundation of China under Grant [41807291], by the Provincial Financial (Geological Prospecting) Special Fund of Jiangsu Province in 2019 and 2021 and by the Youth Science Funds of LREIS, IGSNRR, CAS under Grant [O8R8A082YA].", "Keywords": "", "DOI": "10.1080/07038992.2021.1951190", "PubYear": 2021, "Volume": "47", "Issue": "5", "JournalId": 4152, "JournalTitle": "Canadian Journal of Remote Sensing", "ISSN": "0703-8992", "EISSN": "1712-7971", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Resources and Environmental Information System, Institute of Geographic Sciences and Natural Resources Research, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "East China Mineral Exploration and Development Bureau for Non-Ferrous, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "East China Mineral Exploration and Development Bureau for Non-Ferrous, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "East China Mineral Exploration and Development Bureau for Non-Ferrous, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "East China Mineral Exploration and Development Bureau for Non-Ferrous, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "East China Mineral Exploration and Development Bureau for Non-Ferrous, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> Yuan", "Affiliation": "East China Mineral Exploration and Development Bureau for Non-Ferrous, Nanjing, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "East China Mineral Exploration and Development Bureau for Non-Ferrous, Nanjing, China"}], "References": [{"Title": "Reduced rate of land subsidence since 2016 in Beijing, China: evidence from Tomo-PSInSAR using RadarSAT-2 and Sentinel-1 datasets", "Authors": "<PERSON><PERSON> Zhou; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "4", "Page": "1259", "JournalTitle": "International Journal of Remote Sensing"}]}, {"ArticleId": 89729745, "Title": "A stochastic bi-objective simulation–optimization model for plasma supply chain in case of COVID-19 outbreak", "Abstract": "<p>As of March 24, 2020, the Food and Drug Administration (FDA) authorized to bleed the newly recovered from Coronavirus Disease 2019 (COVID-19), i.e., the ones whose lives were at risk, separate Plasma from their blood and inject it to COVID-19 patients. In many cases, as observed the plasma antibodies have cured the disease. Therefore, a four-echelon supply chain has been designed in this study to locate the blood collection centers, to find out how the collection centers are allocated to the temporary or permanent plasma-processing facilities, how the temporary facilities are allocated to the permanent ones, along with determining the allocation of the temporary and permanent facilities to hospitals. A simulation approach has been employed to investigate the structure of COVID-19 outbreak and to simulate the quantity of plasma demand. The proposed bi-objective model has been solved in small and medium scales using ε -constraint method, Strength Pareto Evolutionary Algorithm II (SPEA-II), Non-dominated Sorting Genetic Algorithm II (NSGA-II), Multi-Objective Grey Wolf Optimizer (MOGWO) and Multi Objective Invasive Weed Optimization algorithm (MOIWO) approaches. One of the novelties of this research is to study the system dynamic structure of COVID-19's prevalence so that to estimate the required plasma level by simulation. Besides, this paper has focused on blood substitutability which is becoming increasingly important for timely access to blood. Due to shorter computational time and higher solution quality, MOIWO is selected to solve the proposed model for a large-scale case study in Iran. The achieved results indicated that as the plasma demand increases, the amount of total system costs and flow time rise, too. The proposed simulation model has also been able to calculate the required plasma demand with 95% confidence interval.</p><p>© 2021 Elsevier B.V. All rights reserved.</p>", "Keywords": "Bi-objective stochastic model;Blood supply chain;COVID-19;Simulation–optimization model", "DOI": "10.1016/j.asoc.2021.107725", "PubYear": 2021, "Volume": "112", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Technology management, Qom branch, Islamic Azad University, Qom, Iran."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Business and Economics, Department of Logistics, Tourism and Service Management, German University of Technology in Oman (GUtech), Muscat, Oman. ;Department of Industrial Engineering, Firoozkooh Branch, Islamic Azad University, Firoozkooh, Iran."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, South Tehran Branch, Islamic Azad University, Tehran, Iran."}], "References": [{"Title": "Behavior of crossover operators in NSGA-III for large-scale optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "470", "JournalTitle": "Information Sciences"}, {"Title": "An epsilon‐constraint method for fully fuzzy multiobjective linear programming", "Authors": "<PERSON>ñedo; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "4", "Page": "600", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Reliable blood supply chain network design with facility disruption: A real-world application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103493", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Computation of multi-choice multi-objective fuzzy probabilistic two stage programming problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "168", "JournalTitle": "International Journal of Computing Science and Mathematics"}, {"Title": "A multi-objective pharmaceutical supply chain network based on a robust fuzzy model: A comparison of meta-heuristics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "106331", "JournalTitle": "Applied Soft Computing"}, {"Title": "An extended Pythagorean fuzzy complex proportional assessment approach with new entropy and score function: Application in pharmacological therapy selection for type 2 diabetes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106441", "JournalTitle": "Applied Soft Computing"}, {"Title": "A bi-objective home healthcare routing and scheduling problem considering patients’ satisfaction in a fuzzy environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106385", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multiobjective optimization of a continuous kraft pulp digester using SPEA2", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "107086", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "A robust bi-objective mathematical model for disaster rescue units allocation and scheduling with learning effect", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106790", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An integrated sustainable medical supply chain network during COVID-19", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "104188", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 89730054, "Title": "Review on\n COVID \n ‐19 diagnosis models based on machine learning and deep learning approaches", "Abstract": "<p>COVID-19 is the disease evoked by a new breed of coronavirus called the severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2). Recently, COVID-19 has become a pandemic by infecting more than 152 million people in over 216 countries and territories. The exponential increase in the number of infections has rendered traditional diagnosis techniques inefficient. Therefore, many researchers have developed several intelligent techniques, such as deep learning (DL) and machine learning (ML), which can assist the healthcare sector in providing quick and precise COVID-19 diagnosis. Therefore, this paper provides a comprehensive review of the most recent DL and ML techniques for COVID-19 diagnosis. The studies are published from December 2019 until April 2021. In general, this paper includes more than 200 studies that have been carefully selected from several publishers, such as IEEE, Springer and Elsevier. We classify the research tracks into two categories: DL and ML and present COVID-19 public datasets established and extracted from different countries. The measures used to evaluate diagnosis methods are comparatively analysed and proper discussion is provided. In conclusion, for COVID-19 diagnosing and outbreak prediction, SVM is the most widely used machine learning mechanism, and CNN is the most widely used deep learning mechanism. Accuracy, sensitivity, and specificity are the most widely used measurements in previous studies. Finally, this review paper will guide the research community on the upcoming development of machine learning for COVID-19 and inspire their works for future development. This review paper will guide the research community on the upcoming development of ML and DL for COVID-19 and inspire their works for future development.</p><p>© 2021 John Wiley &amp; Sons Ltd.</p>", "Keywords": "2019‐nCoV;COVID‐19;COVID‐19 dataset;deep learning;machine learning", "DOI": "10.1111/exsy.12759", "PubYear": 2022, "Volume": "39", "Issue": "3", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Artificial Intelligence Technology, Faculty of Information Science and Technology, Universiti Kebangsaan Malaysia, Bangi, Malaysia; ECE Department-Faculty of Engineering, University of Kufa, Najaf, Iraq"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates; Department of Information Technology, Al-Huson University College, Al-Balqa Applied University, Irbid, Jordan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computing Department, College of Engineering and Applied Sciences, American University of Kuwait, Salmiya, Kuwait; Computer Science Department, Yarmouk University, Irbid, Jordan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates; Department of Computer Science, Al-Aqsa University, Gaza, Palestine"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates; School of Computer Sciences, Universiti Sains Malaysia, Penang, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Faculty of Information Technology, Middle East University, Amman, Jordan; Artificial Intelligence Research Center (AIRC), Ajman University, Ajman, United Arab Emirates"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MLALP Research Group, University of Sharjah, Sharjah, United Arab Emirates"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Agriculture, Al-Muthanna University, Samawah, Iraq"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Artificial Intelligence Technology, Faculty of Information Science and Technology, Universiti Kebangsaan Malaysia, Bangi, Malaysia"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Applied Mathematics, Silesian University of Technology, Gliwice, Poland"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Information Technology, University of Anbar, Anbar, Iraq"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "Sorbonne Center of Artificial Intelligence, Sorbonne University-Abu Dhabi, Abu Dhabi, United Arab Emirates"}], "References": [{"Title": "Link-based multi-verse optimizer for text documents clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "106002", "JournalTitle": "Applied Soft Computing"}, {"Title": "Vaxign-ML: supervised machine learning reverse vaccinology model for improved prediction of bacterial protective antigens", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "10", "Page": "3185", "JournalTitle": "Bioinformatics"}, {"Title": "Predicting the growth and trend of COVID-19 pandemic using machine learning and cloud computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100222", "JournalTitle": "Internet of Things"}, {"Title": "A novel hybrid multi-verse optimizer with K-means for text documents clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "23", "Page": "17703", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Machine Learning and Statistical Modelling for Prediction of Novel COVID-19 Patients Case Study: Jordan", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "122", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "COVID-19 Public Sentiment Insights and Machine Learning for Tweets Classification", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "314", "JournalTitle": "Information"}, {"Title": "Data analysis of COVID-2019 epidemic using machine learning methods: a case study of India", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "1321", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Predictive Data Mining Models for Novel Coronavirus (COVID-19) Infected Patients’ Recovery", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "4", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "A Survey on Deep Transfer Learning to Edge Computing for Mitigating the COVID-19 Pandemic", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "101830", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "A review of mathematical modeling, artificial intelligence and datasets used in the study, prediction and management of COVID-19", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "11", "Page": "3913", "JournalTitle": "Applied Intelligence"}, {"Title": "Deep learning applications in pulmonary medical imaging: recent updates and insights on COVID-19", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "6", "Page": "53", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Coronavirus herd immunity optimizer (CHIO)", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON> <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "10", "Page": "5011", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Deep learning approaches for COVID-19 detection based on chest X-ray images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "114054", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Efficient Pediatric Pneumonia Diagnosis Using Depthwise Separable Convolutions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "6", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Supervised Machine Learning Models for Prediction of COVID-19 Infection using Epidemiology Dataset", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "1", "Page": "11", "JournalTitle": "SN Computer Science"}, {"Title": "CPAS: the UK’s national machine learning-based hospital capacity planning system for COVID-19", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "1", "Page": "15", "JournalTitle": "Machine Learning"}, {"Title": "A Comprehensive Investigation of Machine Learning Feature Extraction and Classification Methods for Automated Diagnosis of COVID-19 Based on X-Ray Images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "3", "Page": "3289", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "COVID-DeepNet: Hybrid Multimodal Deep Learning System for Improving COVID-19 Pneumonia Detection in Chest X-ray Images", "Authors": "<PERSON><PERSON> S<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "2", "Page": "2409", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Community detection using unsupervised machine learning techniques on COVID-19 dataset", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "28", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Predicting COVID-19 Based on Environmental Factors With Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "2", "Page": "305", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 89730341, "Title": "Scaling and Adopting a Multimodal Learning Analytics Application in an Institution-Wide Setting", "Abstract": "Multimodal learning analytics, which is collection, analysis, and report of diverse learning traces to better understand and improve the learning process, has been producing a series of interesting prototypes to analyze learning activities that were previously hard to objectively evaluate. However, none of these prototypes have been taken out of the laboratory and integrated into real learning settings. This article is the first to propose, execute, and evaluate a process to scale and deploy one of these applications, an automated oral presentation feedback system, into an institution-wide setting. Technological, logistical, and pedagogical challenges and adaptations are discussed. An evaluation of the use and effectiveness of the deployment shows both successful adoption and moderate learning gains, especially for low-performing students. In addition, the recording and summarizing of the perception of both instructors and students point to a generally positive experience in spite of the common problems of a first-generation deployment of a complex learning technology.", "Keywords": "Automated feedback;multimodal;oral presentation skills.", "DOI": "10.1109/TLT.2021.3100778", "PubYear": 2021, "Volume": "14", "Issue": "3", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Information Technology Center, and the Faculty of Electrical and Computer Engineering, Escuela Superior Politécnica del Litoral, Guayaquil, Ecuador"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Administration, Leadership, and Technology, Steinhardt School of Culture, Education, and Human Development, New York University, New York, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Natural Sciences and Mathematics, Escuela Superior Politécnica del Litoral, Guayaquil, Ecuador"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Natural Sciences and Mathematics, Escuela Superior Politécnica del Litoral, Guayaquil, Ecuador"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Information Technology Center, Escuela Superior Politécnica del Litoral, Guayaquil, Ecuador"}], "References": []}, {"ArticleId": 89730348, "Title": "Exploiting Zero Knowledge Proof and Blockchains Towards the Enforcement of A nonymity, D ata I ntegrity and P rivacy (ADIP) in the IoT", "Abstract": "In recent years, the Internet of Things (IoT) has been contemplated as the next technological advancement in the era of data communication and networking. However, although hundreds of new IoT platforms are introduced to the market every few months, the security of IoT ecosystems is still not fully understood. This paper discloses the architecture of a multilayer, multimode security system for the IoT. The proposed system is capable of providing multiple security solutions that support anonymous authentication, device privacy, data integrity, device sybil attack detection and IoT server spoofing attack detection. For IoT access control and authentication, our system can support two modes of operations, with one mode endorsing device privacy protection over the network and the second mode relinquishing device identity to establish data tracing during safety-critical IoT events. The new security system includes two innovative crypto approaches, zero knowledge proof (ZKP) and blockchains. IoT device anonymity was achieved via the multimode ZKP protocol, while data integrity and protection against sybil and IoT spoofing attacks were maintained via blockchains. Our threat analysis models showed that data modification and data injection attacks are not feasible. Probabilistic modeling of an IoT spoofing attack was performed in this paper, and the results show that our security system provides high resiliency against such attacks, with a probability approaching 1.", "Keywords": "Anonymity;blockchain;IoT;sybil attacks;ZKP", "DOI": "10.1109/TETC.2021.3099701", "PubYear": 2022, "Volume": "10", "Issue": "3", "JournalId": 17260, "JournalTitle": "IEEE Transactions on Emerging Topics in Computing", "ISSN": "", "EISSN": "2168-6750", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Sam <PERSON> State University, Huntsville, TX, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Texas A&amp;M University, College Station, TX, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Sam <PERSON> State University, Huntsville, TX, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Sam <PERSON> State University, Huntsville, TX, USA"}], "References": [{"Title": "Groupchain: Towards a Scalable Public Blockchain in Fog Computing of IoT Services Computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "2", "Page": "252", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 89730547, "Title": "A data-driven robust optimization algorithm for black-box cases: An application to hyper-parameter optimization of machine learning algorithms", "Abstract": "The huge availability of data in the last decade has raised the opportunity for the better use of data in decision-making processes. The idea of using the existing data to achieve a more coherent reality solution has led to a branch of optimization called data-driven optimization. On the one hand, the presence of uncertain variables in these datasets makes it crucial to design robust optimization methods in this area. On the other hand, in many real-world problems, the closed-form of the objective function is not available and a meta -model based framework is necessary. Motivated by the above points, in this paper a Gaussian process is used in a Bayesian optimization framework to design a method that is consistent with the data in a predefined confidence level. The advantage of the proposed method is that it is computationally tractable in addition to being robust and independent of the objective function’s form. As one of the applications of the proposed algorithm, hyper-parameter optimization for deep learning is investigated. The proposed method can help find the optimal hyper-parameters that are robust with respect to noise.", "Keywords": "Robust optimization ; Data-driven optimization ; Black-box optimization ; Gaussian process ; Bayesian optimization ; Hyper-parameter tuning ; Deep learning", "DOI": "10.1016/j.cie.2021.107581", "PubYear": 2021, "Volume": "160", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Sharif University of Technology, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial & Systems Engineering, University of Southern California, Los Angeles, CA 90007, United States;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Sharif University of Technology, P.O. Box 11155-9414, Azadi Ave, Tehran 1458889694, Iran"}], "References": []}, {"ArticleId": 89730551, "Title": "Value creation in blockchain-driven supply chain finance", "Abstract": "The objective of this study is to explore the blockchain-enabled value creation process in supply chain finance (SCF). We analyze the roles of participants (who?), motives (why?), resources (what?), and practices (how?) through the lens of service-dominant logic and social exchange theory. We conducted an exploratory case study of a blockchain-based platform that offers SCF solutions for supply chain partners and interpreted the results through a series of qualitative analyses. The results prove that the blockchain-driven SCF solution provides services to its customers by applying key resources and conducting corresponding practices, which create value for the participants through meeting their motives.", "Keywords": "Blockchain ; Capability ; Service-dominant logic ; Social exchange theory ; Supply chain finance", "DOI": "10.1016/j.im.2021.103510", "PubYear": 2022, "Volume": "59", "Issue": "7", "JournalId": 2493, "JournalTitle": "Information & Management", "ISSN": "0378-7206", "EISSN": "1872-7530", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Accounting, Beijing Wuzi University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> (<PERSON>) <PERSON><PERSON>", "Affiliation": "Anderson School of Management, University of New Mexico, USA;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> College of Business, Georgia State University, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Supply Chain Management and Information Systems, Rennes School of Business, France"}], "References": []}, {"ArticleId": ********, "Title": "Hybrid dynamic logic institutions for event/data-based systems", "Abstract": "We propose ε ↓ ( D → ) -logic as a formal foundation for the specification and development of event-based systems with data states. The framework is presented as an institution in the sense of Goguen and Burstall and the logic itself is parametrised by an underlying institution D → whose structures are used to model data states. ε ↓ ( D → ) -logic is intended to cover a broad range of abstraction levels from abstract requirements specifications up to constructive specifications. It uses modal diamond and box operators over complex actions adopted from dynamic logic. Atomic actions are pairs [inline-graphic not available: see fulltext] where e is an event and ψ a state transition predicate capturing the allowed reactions to the event. To write concrete specifications of recursive process structures we integrate (control) state variables and binders of hybrid logic. The semantic interpretation relies on event/data transition systems. For the presentation of constructive specifications we propose operational event/data specifications allowing for familiar, diagrammatic representations by state transition graphs. We show that ε ↓ ( D → ) -logic is powerful enough to characterise the semantics of an operational specification by a single ε ↓ ( D → ) -sentence. Thus the whole (formal) development process for event/data-based systems relies on ε ↓ ( D → ) -logic and its semantics as a common basis. It is supported by a variety of implementation constructors which can express, among others, event refinement and parallel composition. Due to the genericity of the approach, it is also possible to change a data state institution during system development when needed. All steps of our formal treatment are illustrated by a running example.", "Keywords": "", "DOI": "10.1007/s00165-021-00550-7", "PubYear": 2021, "Volume": "33", "Issue": "6", "JournalId": 592, "JournalTitle": "Formal Aspects of Computing", "ISSN": "0934-5043", "EISSN": "1433-299X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89730663, "Title": "A stochastic approximation approach to simultaneous feature weighting and selection for nearest neighbour learners", "Abstract": "Nearest neighbour (NN) learners are some of the most popular methods in supervised machine learning with feature selection and weighting being two fundamental tools for improving their performance. In this study, we introduce a novel methodology for simultaneous feature selection and weighting for NN learners based on simultaneous perturbation stochastic approximation (SPSA). This is a pseudo-gradient descent optimisation algorithm that approximates gradient information from noisy objective function measurements without a need for an explicit functional form of the objective function nor its derivatives. In particular, we show how the process of simultaneous feature selection and weighting can be optimised within a stochastic approximation framework with repeated cross-validation (CV) performance as the objective function, which we call SPSA-FWS. We provide extensive computational experiments for assessment of this approach and we compare performance of SPSA-FWS to other feature weighting methods. Our results indicate that SPSA-FWS outperforms existing feature weighting algorithms for the most part and it stands as a competitive new method for this task. Specifically, when compared against its unweighted counterpart and feature weighting alone with 5-repeated 5-fold CV accuracy being the performance metric, SPSA-FWS provides improvements of up to 177.88% with a mean improvement of 20.52% for classification tasks and a mean improvement of 0.19 in the R-Squared metric for regression tasks respectively. In addition to its superior performance, SPSA-FWS has two attractive features: (1) it can be used in conjunction with any performance metric and any variant of nearest neighbour learners, and (2) it can be hybridised with other feature weighting methods.", "Keywords": "Nearest neighbour learner ; Feature weighting ; Feature selection ; Stochastic approximation ; Gradient descent optimisation", "DOI": "10.1016/j.eswa.2021.115671", "PubYear": 2021, "Volume": "185", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Science, Royal Melbourne Institute of Technology, 124 La Trobe St, Melbourne, VIC 3000, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Royal Melbourne Institute of Technology, 124 La Trobe St, Melbourne, VIC 3000, Australia;Corresponding author"}], "References": []}, {"ArticleId": 89730704, "Title": "Effects of first- and third-person perspectives created using a head-mounted display on dart-throwing accuracy", "Abstract": "<p>The first-person perspective (1PP) and third-person perspective (3PP) have both been adopted in video games. The 1PP can induce a strong sense of immersion, and the 3PP allows players to perceive distances easily. Virtual reality technologies have also adopted both perspectives to facilitate skill acquisition. However, how 1PP and 3PP views affect motor skills in the real world, as opposed to in games and virtual environments, remains unclear. This study examined the effects of the 1PP and 3PP on real-world dart-throwing accuracy after head-mounted display (HMD)-based practice tasks involving either the 1PP or 3PP. The 1PP group showed poorer dart-throwing performance, whereas the 3PP task had no effect on performance. Furthermore, while the effect of the 1PP task persisted for some time, that of task 3PP disappeared immediately. Therefore, the effects of 1PP HMD-based practice tasks on motor control transfer more readily to the real world than do those of 3PP tasks.</p>", "Keywords": "HMD; Motor control; Skill transfer; Human performance; Visual perspective; Body ownership; Agency; Size perception; Distance perception", "DOI": "10.1007/s10055-021-00562-x", "PubYear": 2022, "Volume": "26", "Issue": "2", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Defense Academy of Japan, Yokosuka, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Defense Academy of Japan, Yokosuka, Japan"}], "References": [{"Title": "Visuomotor adaptation to excessive visual displacement in video see-through HMDs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "2", "Page": "211", "JournalTitle": "Virtual Reality"}, {"Title": "Performance evaluation of AR/VR training technologies for EMS first responders", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "83", "JournalTitle": "Virtual Reality"}, {"Title": "A novel method for VR sickness reduction based on dynamic field of view processing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "331", "JournalTitle": "Virtual Reality"}, {"Title": "Effects of dynamic field-of-view restriction on cybersickness and presence in HMD-based virtual reality", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "433", "JournalTitle": "Virtual Reality"}, {"Title": "Simulation-based surgical training systems in laparoscopic surgery: a current review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "491", "JournalTitle": "Virtual Reality"}, {"Title": "A VR training system for learning and skills development for construction workers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "523", "JournalTitle": "Virtual Reality"}, {"Title": "A meta-analysis of the virtual reality problem: Unequal effects of virtual reality sickness across individual differences", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "4", "Page": "1221", "JournalTitle": "Virtual Reality"}]}, {"ArticleId": 89730705, "Title": "Drug traceability and transparency in medical supply chain using blockchain for easing the process and creating trust between stakeholders and consumers", "Abstract": "<p>Without a proper mechanism to track and authenticate drugs, both stakeholder and consumer experience dilemmas. The dilemmas arise between stakeholders and consumers are related to of coordination, inventory management, human resource dependency, order management, stock management, expiry of medicines data etc. Stakeholders are unable to analyse the demands, hence becoming incapable of optimizing their production and storage. Likewise, a consumer remains suspicious about the authenticity of the drug. A medical supply chain promotes updating medicine status at each checkpoint, reducing the disputes caused by medicine ' s unseen journey. In the current market, medical supply chains are present, but they are centralized. A centralized medical supply chain is typically tedious and expensive to maintain and does not provide adequate features to analyse markets. Above all, it brands merely a certificate to prove the authenticity of a drug. However, by using a blockchain platform, the medical supply chain problems can be solved efficiently. This blockchain-based medical supply chain’s key feature is to system stores all the medicine batch ' s transfer history. First, a pharmaceutical company is permitted to register a medicine. After the manufacturing of a registered medicine, a batch-manager uploads its details on the platform ' s network. Hereafter, any further exchange of this batch needs both sender and receiver ' s approval. Only after the successful completion of this procedure, an exchange occurs. A corresponding exchange transaction is permanently stored on the network. This sequence shapes the system to follow a systematic order and eliminates the possibility of a third-person fraud. Lastly, we made a DApp (Decentralized Application) for the tracking of the medical supply chain. A medical supply chain built on the blockchain provides immutability, transparency, automation, and integrity. This paper aims to introduce a model to maintain medical supply chain records on the blockchain while explaining blockchain technology. Moreover, the blockchain platforms and their dependencies are explained lucidly. The medical supply chain’s implementation and design are performed using smart contracts, Web3.js library, and JavaScript. Furthermore, the system is tested on both a local network, the Truffle suite, and the Kovan test network. In the future, the use of IoT inculcating integrated chip that can automatically update a batch’s location, temperature, and other physical conditions periodically can be developed.</p>", "Keywords": "Medical supply chain;; Blockchain;; Optimization; Authenticity;; Centralized;; Test network", "DOI": "10.1007/s00779-021-01588-3", "PubYear": 2024, "Volume": "28", "Issue": "1", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Data Science and Artificial Intelligence, IcfaiTech (Faculty of Science and Technology), ICFAI Foundation for Higher Education (Deemed to be University), Hyderabad, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering, KIIT Deemed to be University, Bhubaneswar, India; Corresponding author."}], "References": [{"Title": "Medical Diagnosis Using Machine Learning: A Statistical Review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "1", "Page": "107", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 89730728, "Title": "Design of Internet of Things and big data analytics-based disaster risk management", "Abstract": "<p>Presently, the Internet of Things (IoT) and big data analytics technology offer enormous opportunities to disaster risk management services. Recent disaster risk management patterns involve disaster risk reduction to avoid future disaster risks, minimize current disastrous risks, resilience building, and disaster loss reduction. The main challenges in disaster management are communication within a disaster zone which is being disrupted. In this paper, the Internet of Things assisted disaster risk management framework (IOTDRMF) has been proposed for technical resources to communicate the emergency time and better visibility into reliable and prompt decision-making through observing, evaluating, and forecasting natural disasters. This IOTDRMF utilizes big data analytics to analyze disaster risk management build a kind of spatial data communication network infrastructure, making it a priority to establish rules, protocols, and knowledge sharing. The experimental results show the IOTDRMF, and big data analytics compensate for a weak communication network infrastructure and better decision-making to handle disaster risk management.</p>", "Keywords": "Internet of Things (IoT); Internet of Things assisted disaster risk management framework (IOTDRMF); Big data; Disaster risk management", "DOI": "10.1007/s00500-021-05953-5", "PubYear": 2021, "Volume": "25", "Issue": "18", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Business and Administration, Chongqing Technology and Business University, Chongqing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronical and Information Engineering, Chongqing Radio and TV University, Chongqing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Adhiyamaan College of Engineering, Hosur, India"}, {"AuthorId": 4, "Name": "C. B. Si<PERSON>pan", "Affiliation": "Department of Computer Science and Engineering, Adhiyamaan College of Engineering, Hosur, India"}], "References": [{"Title": "A data mining-based framework for supply chain risk management", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "105570", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Detection of flood disaster system based on IoT, big data and convolutional deep neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; C.<PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "150", "JournalTitle": "Computer Communications"}, {"Title": "Collaborative data acquisition and processing for post disaster management and surveillance related tasks using UAV-based IoT cloud", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "4", "Page": "216", "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing"}, {"Title": "Prediction of Landsliding using Univariate Forecasting Models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "5", "Issue": "1", "Page": "e209", "JournalTitle": "Internet Technology Letters"}, {"Title": "A deep learning-based social media text analysis framework for disaster resource management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Call for Special Issue Papers: Big Data Analytics for Agricultural Disaster Management", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "5", "Page": "450", "JournalTitle": "Big Data"}]}, {"ArticleId": 89730824, "Title": "Analysis of the spectral symbol associated to discretization schemes of linear self-adjoint differential operators", "Abstract": "Given a linear self-adjoint differential operator $$\\mathscr {L}$$ \n L \n along with a discretization scheme (like Finite Differences, Finite Elements, Galerkin Isogeometric Analysis, etc.), in many numerical applications it is crucial to understand how good the (relative) approximation of the whole spectrum of the discretized operator $$\\mathscr {L}\\,^{(n)}$$ \n \n L \n \n \n \n ( \n n \n ) \n \n \n \n is, compared to the spectrum of the continuous operator $$\\mathscr {L}$$ \n L \n . The theory of Generalized Locally Toeplitz sequences allows to compute the spectral symbol function $$\\omega $$ \n ω \n associated to the discrete matrix $$\\mathscr {L}\\,^{(n)}$$ \n \n L \n \n \n \n ( \n n \n ) \n \n \n \n . Inspired by a recent work by <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> and coauthors, we prove that the symbol $$\\omega $$ \n ω \n can measure, asymptotically, the maximum spectral relative error $$\\mathscr {E}\\ge 0$$ \n \n E \n ≥ \n 0 \n \n . It measures how the scheme is far from a good relative approximation of the whole spectrum of $$\\mathscr {L}$$ \n L \n , and it suggests a suitable (possibly non-uniform) grid such that, if coupled to an increasing refinement of the order of accuracy of the scheme, guarantees $$\\mathscr {E}=0$$ \n \n E \n = \n 0 \n \n .", "Keywords": "Spectral symbol; Discretization schemes; Linear self-adjoint differential operators; Maximum spectral relative error; 47B35; 35P05; 65N99; 65D25; 65F99", "DOI": "10.1007/s10092-021-00426-5", "PubYear": 2021, "Volume": "58", "Issue": "3", "JournalId": 12677, "JournalTitle": "<PERSON><PERSON><PERSON>", "ISSN": "0008-0624", "EISSN": "1126-5434", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Insubria, Como, Italy;Harbin Institute of Technology (Shenzhen), Shenzhen, China"}], "References": []}, {"ArticleId": 89730847, "Title": "Self-attention negative feedback network for real-time image super-resolution", "Abstract": "In the field of real-time image enhancement, image super-resolution (SR) is an important research hotspot. As an image super-resolution method, deep learning can extract more stable and higher level features. However, image super-resolution processing is an ill posed problem. Due to the lack of self-attentional negative feedback mechanism, the existing methods can not better constrain the mapping space from low-resolution image to high-resolution image, so generated high-resolution (HR) image does not conform to human visual perception. Therefore, this paper proposes a self-attention negative feedback network (SRAFBN) for realizing the real-time image SR. The network model constrains the image mapping space and selects the key information of the image through the self-attention negative feedback model, so that higher quality images can be generated to meet human visual perception. Specifically, a Recurrent Neural Network (RNN) is firstly utilized to construct multiple negative feedback modules, and each module generates a HR image. Then, the key information of the generated image is extracted by the self-attention mechanism. Finally, the extracted information is fused to generate the final HR image. Experimental results prove the proposed SRAFBN network model not only has higher PSNR and SSIM, but also can reconstruct more realistic and clear real-time images.", "Keywords": "Super-resolution ; Self-attention ; Negative feedback network ; SSIM ; PSNR", "DOI": "10.1016/j.jksuci.2021.07.014", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Computing and Language Information Processing, Hunan Normal University, No. 36 Lushan Road, Changsha, China;College of Information Science and Engineering, Hunan Normal University, No. 36 Lushan Road, Changsha, China;Hunan Xiangjiang Artificial Intelligence Academy, No. 36 Lushan Road, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Computing and Language Information Processing, Hunan Normal University, No. 36 Lushan Road, Changsha, China;College of Information Science and Engineering, Hunan Normal University, No. 36 Lushan Road, Changsha, China;Hunan Xiangjiang Artificial Intelligence Academy, No. 36 Lushan Road, Changsha, China"}, {"AuthorId": 3, "Name": "Liping Song", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Computing and Language Information Processing, Hunan Normal University, No. 36 Lushan Road, Changsha, China;College of Information Science and Engineering, Hunan Normal University, No. 36 Lushan Road, Changsha, China;Hunan Xiangjiang Artificial Intelligence Academy, No. 36 Lushan Road, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Applied Mathematics, Silesian University of Technology, Kaszubska 23, Gliwice, Poland;Corresponding authors at: Faculty of Applied Mathematics, Silesian University of Technology, Kaszubska 23, Gliwice, Poland (<PERSON><PERSON>). Hunan Provincial Key Laboratory of Intelligent Computing and Language Information Processing, Hunan Normal University, No. 36 Lushan Road, Changsha, China (S. <PERSON>)"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Computing and Language Information Processing, Hunan Normal University, No. 36 Lushan Road, Changsha, China;College of Information Science and Engineering, Hunan Normal University, No. 36 Lushan Road, Changsha, China;Hunan Xiangjiang Artificial Intelligence Academy, No. 36 Lushan Road, Changsha, China;Corresponding authors at: Faculty of Applied Mathematics, Silesian University of Technology, Kaszubska 23, Gliwice, Poland (<PERSON><PERSON>). Hunan Provincial Key Laboratory of Intelligent Computing and Language Information Processing, Hunan Normal University, No. 36 Lushan Road, Changsha, China (S. Liu)"}], "References": []}, {"ArticleId": 89730906, "Title": "An Improved Algorithm for Fatigue Face Image Detection and Recognition Based on Facial Feature Points", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2021.117206", "PubYear": 2021, "Volume": "11", "Issue": "7", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "真杰 尹", "Affiliation": ""}], "References": []}, {"ArticleId": 89730918, "Title": "Numerical Investigation of Thin Film Flow of a Third-Grade Fluid on a Moving Belt Using Evolutionary Algorithm-Based Heuristic Technique", "Abstract": "<p>This paper presents a stochastic heuristic approach to solve numerically nonlinear differential equation (NLDE) governing the thin film flow of a third-grade fluid (TFF-TGF) on a moving belt. Moreover, the impact on velocity profile due to fluid attribute is also investigated. The estimate solution of the given NLDE is achieved by using the linear combination of Bernstein polynomials with unknown constants. A fitness function is deduced to convert the given NLDE along with its boundary conditions into an optimization problem. Genetic algorithm (GA) is employed to optimize the values of unknown constants. The proposed approach provided results in good agreement with numerical values taken by <PERSON><PERSON> and more accurate than two popular classical methods including Adomian Decomposition Method (ADM) and Optimal Homotopy Asymptotic Method (OHAM). The error is minimized 10[Formula: see text] times to 10[Formula: see text] times.</p>", "Keywords": "", "DOI": "10.1142/S0218126622500116", "PubYear": 2022, "Volume": "31", "Issue": "1", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, School of Engineering and Applied Sciences (SEAS), Isra University Islamabad Campus, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Faculty of Engineering and Technology, International Islamic University, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hamdard Institute of Engineering and Technology, Islamabad 44000, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering, School of Engineering and Applied Sciences (SEAS), Isra University Islamabad Campus, Islamabad, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computing, Kohat University of Science and Technology, Kohat, Pakistan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hamdard Institute of Engineering and Technology, Islamabad 44000, Pakistan"}], "References": [{"Title": "3D Reconstruction for Motion Blurred Images Using Deep Learning-based Intelligent Systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "2", "Page": "2087", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A blockchain-empowered crowdsourcing system for 5G-enabled smart cities", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "76", "Issue": "", "Page": "103517", "JournalTitle": "Computer Standards & Interfaces"}]}, {"ArticleId": 89730948, "Title": "Towards robust explanations for deep neural networks", "Abstract": "Explanation methods shed light on the decision process of black-box classifiers such as deep neural networks. But their usefulness can be compromised because they are susceptible to manipulations. With this work, we aim to enhance the resilience of explanations. We develop a unified theoretical framework for deriving bounds on the maximal manipulability of a model. Based on these theoretical insights, we present three different techniques to boost robustness against manipulation: training with weight decay, smoothing activation functions, and minimizing the Hessian of the network. Our experimental results confirm the effectiveness of these approaches.", "Keywords": "Explanation method ; Saliency map ; Adversarial attacks ; Manipulation ; Neural networks,", "DOI": "10.1016/j.patcog.2021.108194", "PubYear": 2022, "Volume": "121", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Machine Learning Group, Department of Electrical Engineering & Computer Science, Technische Universität Berlin, Marchstr. 23, Berlin 10587, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Machine Learning Group, Department of Electrical Engineering & Computer Science, Technische Universität Berlin, Marchstr. 23, Berlin 10587, Germany;BIFOLD - Berlin Institute for the Foundations of Learning and Data, Technische Universität Berlin, Berlin, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Machine Learning Group, Department of Electrical Engineering & Computer Science, Technische Universität Berlin, Marchstr. 23, Berlin 10587, Germany;Department of Artificial Intelligence, Korea University, Anam-dong, Seongbuk-gu, Seoul 02841, Korea;Max Planck Institute for Informatics, Stuhlsatzenhausweg 4, Saarbrücken 66123, Germany;BIFOLD - Berlin Institute for the Foundations of Learning and Data, Technische Universität Berlin, Berlin, Germany;Google Research, Brain team, Berlin, Germany;Corresponding author"}, {"AuthorId": 4, "Name": "Pan Kessel", "Affiliation": "Machine Learning Group, Department of Electrical Engineering & Computer Science, Technische Universität Berlin, Marchstr. 23, Berlin 10587, Germany;BIFOLD - Berlin Institute for the Foundations of Learning and Data, Technische Universität Berlin, Berlin, Germany;Corresponding author"}], "References": [{"Title": "Towards explaining anomalies: A deep Taylor decomposition of one-class models", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "107198", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 89731040, "Title": "Self-reconfigurable multilegged robot swarms collectively accomplish challenging terradynamic tasks", "Abstract": "<p>Swarms of ground-based robots are presently limited to relatively simple environments, which we attribute in part to the lack of locomotor capabilities needed to traverse complex terrain. To advance the field of terradynamically capable swarming robotics, inspired by the capabilities of multilegged organisms, we hypothesize that legged robots consisting of reversibly chainable modular units with appropriate passive perturbation management mechanisms can perform diverse tasks in variable terrain without complex control and sensing. Here, we report a reconfigurable swarm of identical low-cost quadruped robots (with directionally flexible legs and tail) that can be linked on demand and autonomously. When tasks become terradynamically challenging for individuals to perform alone, the individuals suffer performance degradation. A systematic study of performance of linked units leads to new discoveries of the emergent obstacle navigation capabilities of multilegged robots. We also demonstrate the swarm capabilities through multirobot object transport. In summary, we argue that improvement capabilities of terrestrial swarms of robots can be achieved via the judicious interaction of relatively simple units.</p>", "Keywords": "", "DOI": "10.1126/scirobotics.abf1628", "PubYear": 2021, "Volume": "6", "Issue": "56", "JournalId": 15892, "JournalTitle": "Science Robotics", "ISSN": "", "EISSN": "2470-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, University of Notre Dame, Notre Dame, IN 46556, USA.;School of Physics, Georgia Institute of Technology, Atlanta, GA 30332, USA."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Physics, Georgia Institute of Technology, Atlanta, GA 30332, USA."}], "References": [{"Title": "Amphibious and Sprawling Locomotion: From Biology to Robotics and Back", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "1", "Page": "173", "JournalTitle": "Annual Review of Control, Robotics, and Autonomous Systems"}, {"Title": "Swarm Robotic Behaviors and Current Applications", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "36", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Learning quadrupedal locomotion over challenging terrain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "47", "Page": "eabc5986", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 89731073, "Title": "On the complexity of assimilation in urban communities", "Abstract": "<p>Cities are microcosms representing a diversity of human experience. The complexity of urban systems arises from this diversity, where the services that cities offer to their inhabitants have to be tailored for their unique requirements. This paper studies the complexity of urban environments in terms of the assimilation of its communities. We examine the urban assimilation complexity with respect to the foreignness between communities and formalize the level of complexity using information-theoretic measures. Our findings contribute to a sociological perspective of the relationship between urban complex systems and the diversity of communities that make up urban systems.</p>", "Keywords": "Assimilation;Urban communities;Entropy;Complexity;Renyi divergence;Immigration", "DOI": "10.1007/s41109-021-00399-y", "PubYear": 2021, "Volume": "6", "Issue": "1", "JournalId": 5330, "JournalTitle": "Applied Network Science", "ISSN": "", "EISSN": "2364-8228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Dallas, Irving, USA"}], "References": []}, {"ArticleId": 89731084, "Title": "Do Auto-Regressive Models Protect Privacy? Inferring Fine-Grained Energy Consumption From Aggregated Model Parameters", "Abstract": "We investigate the extent to which statistical predictive models leak information about their training data. More specifically, based on the use case of household (electrical) energy consumption, we evaluate whether white-box access to auto-regressive (AR) models trained on such data together with background information, such as household energy data aggregates (e.g., monthly billing information) and publicly-available weather data, can lead to inferring fine-grained energy data of any particular household. We construct two adversarial models aiming to infer fine-grained energy consumption patterns. Both threat models use monthly billing information of target households. The second adversary has access to the AR model for a cluster of households containing the target household. Using two real-world energy datasets, we demonstrate that this adversary can apply maximum a posteriori estimation to reconstruct daily consumption of target households with significantly lower error than the first adversary, which serves as a baseline. Such fine-grained data can essentially expose private information, such as occupancy levels. Finally, we use differential privacy (DP) to alleviate the privacy concerns of the adversary in dis-aggregating energy data. Our evaluations show that differentially private model parameters offer strong privacy protection against the adversary with moderate utility, captured in terms of model fitness to the cluster.", "Keywords": "Aggregate statistics;white-box attacks;inference attacks;auto-regressive models;differential privacy;energy data privacy", "DOI": "10.1109/TSC.2021.3100498", "PubYear": 2022, "Volume": "15", "Issue": "6", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Science and Engineering, Macquarie University, Sydney, NSW, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Macquarie University, Sydney, NSW, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Melbourne, Melbourne, VIC, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Macquarie University, Sydney, NSW, Australia"}], "References": [{"Title": "SMINT", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on the Web"}]}, {"ArticleId": 89731114, "Title": "A Sign Logic-Based Method of Current Sensor Fault Detection for PMSM Drivers", "Abstract": "<p>A simplified method of current sensor fault detection and isolation (FDI) for permanent magnet synchronous motor (PMSM) drives, which only requires to collect the information of motor positions and currents of the measured phases, has been proposed in this paper. Compared with the classical state-observer-based approaches, the calculations needed in the new method only involve a few addition and logical operations. The simplicity and reliability of the new method makes itself especially useful for fault detection of current sensors in real-time control systems with limited computational capability, e.g., single-chip microcomputers (SCM) or field programmable gate arrays (FPGA). The experimental results on the basis of a FPGA controller have validated the feasibility and robustness of the proposed FDI approach.</p>", "Keywords": "", "DOI": "10.1155/2021/9955348", "PubYear": 2021, "Volume": "2021", "Issue": "1", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Robotics and System, Harbin Institute of Technology, Harbin 150001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Robotics and System, Harbin Institute of Technology, Harbin 150001, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Robotics and System, Harbin Institute of Technology, Harbin 150001, China"}], "References": []}, {"ArticleId": 89731207, "Title": "RapidXfer - data transfer framework for square kilometre array", "Abstract": "Data Management of Astronomy Data is often a laborious task and it is even more challenging for the extraordinary amounts of data expected from the world’s largest radio telescope, Square Kilometre Array. There are overt issues in transferring the voluminous data and the traditional data transfer methods are fragile especially for the data transfer between two continents. To address this, a new data transfer framework is proposed and the data transfer is achieved using two steps: international and local transfers. The efficiency of different end-to-end data transfer tools used in is evaluated on different dataset sizes. Further, a comparative study of two IRIS grid data transfer methods is made to understand each methods’ advantages and disadvantages. This study can be used as a reference for the development of future SKA’s data transfer operations.", "Keywords": "Big data; MeerKAT; Square kilometre array; Telescope; Astronomy", "DOI": "10.1007/s42488-021-00055-1", "PubYear": 2021, "Volume": "3", "Issue": "4", "JournalId": 64686, "JournalTitle": "Journal of Data, Information and Management", "ISSN": "2524-6356", "EISSN": "2524-6364", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Manchester, Manchester, UK"}], "References": []}, {"ArticleId": 89731311, "Title": "Incremental BERT with commonsense representations for multi-choice reading comprehension", "Abstract": "<p>Compared to extractive machine reading comprehension (MRC) limited to text spans, multi-choice MRC is more flexible in evaluating the model’s ability to utilize external commonsense knowledge. On the one hand, existing methods leverage transfer learning and complicated matching networks to solve the multi-choice MRC, which lacks interpretability for commonsense questions. On the other hand, although Transformer based pre-trained language models such as BERT have shown powerful performance in MRC, external knowledge such as unspoken commonsense and world knowledge still can not be used explicitly for downstream tasks. In this work, we present three simple yet effective injection methods plugged in BERT’s structure to fine-tune the multi-choice MRC tasks with off-the-shelf commonsense representations directly. Moreover, we introduce a mask mechanism for the token-level multi-hop relationship searching to filter external knowledge. Experimental results indicate that the incremental BERT outperforms the baseline by a considerable margin on DREAM and CosmosQA, two knowledge-driven multi-choice datasets. Further analysis shows the robustness of the incremental model in the case of an incomplete training set.</p>", "Keywords": "Machine reading comprehension; BERT; External knowledge; Common sense; Deep learning", "DOI": "10.1007/s11042-021-11197-0", "PubYear": 2021, "Volume": "80", "Issue": "21-23", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northwestern Polytechnical University, Xi’an, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northwestern Polytechnical University, Xi’an, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northwestern Polytechnical University, Xi’an, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Northwestern Polytechnical University, Xi’an, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northwestern Polytechnical University, Xi’an, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Lu", "Affiliation": "School of Computer Science and Engineering, Northwestern Polytechnical University, Xi’an, People’s Republic of China"}], "References": []}, {"ArticleId": 89731332, "Title": "A novel bio-H2S sensor based on Cu2O/ZnO heterostructure ordered nanoarrays", "Abstract": "Cu<sub>2</sub>O/ZnO heterostructure ordered nanoarrays were prepared with an in situ electrodeposition method for H<sub>2</sub>S detection in biological samples at room temperature. Based on this new material, a novel sensor was proposed, which exhibited good performances for sensitivity and selectivity for bio-H<sub>2</sub>S detection due to the surface/interface effects and long-range ordered structure. It was confirmed by scanning electron microscope (SEM), X-ray photoelectron spectroscopy (XPS), and transmission electron microscopy (TEM) that these good performances were mainly attributed to the conversion of Cu<sub>2</sub>O to metallic Cu<sub>x</sub>S on the surface of the ordered structure. In addition, the sensor had good portability due to its small size. Our results suggest that the Cu<sub>2</sub>O/ZnO heterostructure ordered nanoarrays are promising candidates for high-performance H<sub>2</sub>S sensors applied in disease diagnosis and environmental assessment.", "Keywords": "Cu<sub>2</sub>O/ZnO ; Heterostructure ; Bio-sensor ; Sensitivity ; Selectivity", "DOI": "10.1016/j.sna.2021.113001", "PubYear": 2021, "Volume": "331", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Electrical Engineering, Linyi University, Linyi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics and Electrical Engineering, Linyi University, Linyi, China;@qq.com"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Xu<PERSON>", "Affiliation": "School of Mechanical and Vehicle Engineering, Linyi University, Linyi, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Electrical Engineering, Linyi University, Linyi, China;Corresponding author"}], "References": []}, {"ArticleId": 89731397, "Title": "Development and validation of the fluency in human-robot interaction scale. A two-wave study on three perspectives of fluency", "Abstract": "Contemporary work offers many humans the possibility to collaborate with robots. In fact, in industrial settings, human-robot interaction has become a necessity. To improve the quality of human collaboration with their robotic counterparts, practitioners and researchers turn their attention to the concept of fluency. It is crucial for human-robot interaction yet difficult to investigate. Moreover, we argue that the notion of fluency being a unidimensional construct is insufficient to understand the complex interaction in a human-robot team. Therefore, we aimed to develop and validate a new measure to study subjective fluency from three different perspectives, i.e., the human-oriented, the robot-oriented, and the team-oriented. The scale can serve as a tool to better understand the dynamics of the relationship between the operator and the robot. In this paper, we presented the factor analyses and psychometric properties of the Fluency in Human-Robot Interaction Scale. Furthermore, we tested the validity of the scale by investigating the relationship between three perspectives of fluency and the individual performance as well as fluency change over 6 weeks. The results showed positive relationships among all perspectives of fluency and performance. However, no change over time was observed. The findings were discussed with regard to the possible use of the scale in practical and academic contexts.", "Keywords": "Human-robot interaction ; Human-robot fluency ; Scale development ; Factor analysis ; Industry 4.0 ; Job performance", "DOI": "10.1016/j.ijhcs.2021.102698", "PubYear": 2021, "Volume": "155", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "Mateusz Paliga", "Affiliation": "Uniwersytet Śląski w Katowicach, Instytut Psychologii/ Institute of Psychology, 40-126 Katowice, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Uniwersytet Śląski w Katowicach, Instytut Psychologii/ Institute of Psychology, 40-126 Katowice, Poland"}], "References": [{"Title": "Stress in manual and autonomous modes of collaboration with a cobot", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "106469", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 89731408, "Title": "Double bayesian pairwise learning for one-class collaborative filtering", "Abstract": "Recommender systems have become an indispensable tool for real-world applications. Only one-class feedback can be obtained in many applications. Therefore, the one-class recommendation problem has attracted much attention. Pairwise ranking methods are popular for dealing with the one-class problem. Bayesian Personalized Ranking (BPR) is one of the most popular pairwise methods, assuming users prefer the observed item to the unobserved item. The parameters in BPR are learned based on stochastic gradient descent (SGD). However, the previous work has shown that existing the vanishing gradient problem in the learning process when the preference difference between the observed item and the unobserved item is very large. In this paper, we propose a novel algorithm called Double Bayesian Pairwise Learning (DBPL). In the learning process of DBPL, the preference difference between the observed item and the unobserved item can be reduced by fusing a relatively smaller preference difference between another pair of items. Moreover, we calculate potential preference scores between users and items based on user–item interactions to measure preference differences between unobserved items of each user. Experimental results on three real-world datasets show the effectiveness of the DBPL algorithm.", "Keywords": "One-class collaborative filtering ; Vanishing gradient problem ; Pairwise ranking ; Personalized recommendation", "DOI": "10.1016/j.knosys.2021.107339", "PubYear": 2021, "Volume": "229", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering, Tokushima University, 7700814, Japan"}, {"AuthorId": 2, "Name": "Fuji Ren", "Affiliation": "Faculty of Engineering, Tokushima University, 7700814, Japan;Corresponding author"}], "References": [{"Title": "Attention-based context-aware sequential recommendation model", "Authors": "Wei<PERSON> Yuan; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "510", "Issue": "", "Page": "122", "JournalTitle": "Information Sciences"}, {"Title": "A new similarity measure for collaborative filtering based recommender systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "188", "Issue": "", "Page": "105058", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-facet user preference learning for fine-grained item recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "385", "Issue": "", "Page": "258", "JournalTitle": "Neurocomputing"}, {"Title": "DFF-ResNet: An insect pest recognition model based on residual networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "4", "Page": "300", "JournalTitle": "Big Data Mining and Analytics"}, {"Title": "Prior-based bayesian pairwise ranking for one-class collaborative filtering", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "440", "Issue": "", "Page": "365", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 89731409, "Title": "A federated learning system with enhanced feature extraction for human activity recognition", "Abstract": "With the rapid growth of mobile devices, wearable sensor-based human activity recognition (HAR) has become one of the hottest topics in the Internet of Things. However, it is challenging for traditional approaches to achieving high recognition accuracy while protecting users’ privacy and sensitive information. To this end, we design a federated learning system for HAR (HARFLS). Based on the FederatedAveraging method, HARFLS enables each user to handle its activity recognition task safely and collectively. However, the recognition accuracy largely depends on the system’s feature extraction ability. To capture sufficient features from HAR data, we design a perceptive extraction network (PEN) as the feature extractor for each user. PEN is mainly composed of a feature network and a relation network. The feature network, based on a convolutional block, is responsible for discovering local features from the HAR data while the relation network, a combination of long short-term memory (LSTM) and attention mechanism, focuses on mining global relationships hidden in the data. Four widely used datasets, i.e., WISDM, UCI_HAR 2012, OPPORTUNITY, and PAMAP2, are used for performance evaluation. Experimental results demonstrate that PEN outperforms 14 existing HAR algorithms on these datasets in terms of the F1-score; HARFLS with PEN obtains better recognition results on the WISDM and PAMAP2 datasets, compared with 11 existing federated learning systems with various feature extraction structures.", "Keywords": "Deep learning ; Feature extraction ; Federated learning ; Human activity recognition ; Wearable sensors", "DOI": "10.1016/j.knosys.2021.107338", "PubYear": 2021, "Volume": "229", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Artificial Intelligence, Southwest Jiaotong University, Chengdu 611756, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou 221166, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Artificial Intelligence, Southwest Jiaotong University, Chengdu 611756, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Artificial Intelligence, Southwest Jiaotong University, Chengdu 611756, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Artificial Intelligence, Southwest Jiaotong University, Chengdu 611756, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computing and Artificial Intelligence, Southwest Jiaotong University, Chengdu 611756, China"}], "References": [{"Title": "On identification of driving-induced stress using electroencephalogram signals: A framework based on wearable safety-critical scheme and machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "66", "JournalTitle": "Information Fusion"}, {"Title": "Adaptive sliding window based activity recognition for assisted livings", "Authors": "<PERSON><PERSON><PERSON><PERSON> Ma; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "55", "JournalTitle": "Information Fusion"}, {"Title": "Collaborative learning based on centroid-distance-vector for wearable devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105569", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A knowledge-light approach to personalised and open-ended human activity recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "105651", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "The fusion of Internet of Intelligent Things (IoIT) in remote diagnosis of obstructive Sleep Apnea: A survey and a new model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "84", "JournalTitle": "Information Fusion"}, {"Title": "Federated and secure cloud services for building medical image classifiers on an intercontinental infrastructure", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "119", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A review and categorization of techniques on device-free human activity recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "102738", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Highly efficient federated learning with strong privacy preservation in cloud computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "101889", "JournalTitle": "Computers & Security"}, {"Title": "Computation offloading in Edge Computing environments using Artificial Intelligence techniques", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "103840", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A survey on security and privacy of federated learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "619", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "RTFN: A robust temporal feature network for time series classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "571", "Issue": "", "Page": "65", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 89731410, "Title": "A deep learning approach for semi-supervised community detection in Online Social Networks", "Abstract": "Social Network Analysis (SNA) has gained popularity as a way to unveil and identify useful social patterns as communities among users. However the continuous, exponential growth of these networks (both in terms of number of users, and in terms of the variety of different interactions that these networks allow) has made the development of efficient and effective community detection techniques a challenging computational task. In this paper, we propose an innovative approach for Semi-supervised Community Detection , exploiting Convolutional Neural Networks to simultaneously leverage different properties of a network — such as topological and context information. Crucially, computational cost is optimized by building on the insight that representing network connections over particular sparse matrices can significantly decrease the number of operations that need to be explicitly performed. By extensively evaluating our system on large (artificial and real-world) datasets, we show that our approach outperforms a variety of existing state-of-the-art techniques in terms of running time, as well as over M a c r o − and M i c r o − F 1 .", "Keywords": "Social Network Analysis ; Semi-supervised community detection ; Online Social Networks ; Deep learning", "DOI": "10.1016/j.knosys.2021.107345", "PubYear": 2021, "Volume": "229", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Linguistics of the University of Utah, Salt Lake City, UT 84112, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technology (DIETI), University of Naples “Federico II”, Via Claudio 21, Naples, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technology (DIETI), University of Naples “Federico II”, Via Claudio 21, Naples, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technology (DIETI), University of Naples “Federico II”, Via Claudio 21, Naples, Italy;Corresponding author"}], "References": [{"Title": "A three-stage algorithm on community detection in social networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104822", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "DICO: A Graph-DB Framework for Community Detection on Big Scholarly Data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "4", "Page": "1987", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Finding influential nodes in social networks based on neighborhood correlation coefficient", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105580", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Community-diversified influence maximization in social networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101522", "JournalTitle": "Information Systems"}, {"Title": "Node-community membership diversifies community structures: An overlapping community detection algorithm based on local expansion and boundary re-checking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "198", "Issue": "", "Page": "105935", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Community detection based on the Matthew effect", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "205", "Issue": "", "Page": "106256", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Earned benefit maximization in social networks under budget constraint", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114346", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Novel automatic group identification approaches for group recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> Bilge", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114709", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A survey about community detection over On-line Social and Heterogeneous Information Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "224", "Issue": "", "Page": "107112", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 89731411, "Title": "Dimensions of commonsense knowledge", "Abstract": "Commonsense knowledge is essential for many AI applications, including those in natural language processing, visual processing, and planning. Consequently, many sources that include commonsense knowledge have been designed and constructed over the past decades. Recently, the focus has been on large text-based sources, which facilitate easier integration with neural (language) models and application to textual tasks, typically at the expense of the semantics of the sources and their harmonization. Efforts to consolidate commonsense knowledge have yielded partial success, with no clear path towards a comprehensive solution. We aim to organize these sources around a common set of dimensions of commonsense knowledge. We survey a wide range of popular commonsense sources with a special focus on their relations. We consolidate these relations into 13 knowledge dimensions. This consolidation allows us to unify the separate sources and to compute indications of their coverage, overlap, and gaps with respect to the knowledge dimensions. Moreover, we analyze the impact of each dimension on downstream reasoning tasks that require commonsense knowledge, observing that the temporal and desire/goal dimensions are very beneficial for reasoning on current downstream tasks, while distinctness and lexical knowledge have little impact. These results reveal preferences for some dimensions in current evaluation, and potential neglect of others.", "Keywords": "Commonsense knowledge ; Semantics ; Knowledge graphs ; Reasoning", "DOI": "10.1016/j.knosys.2021.107347", "PubYear": 2021, "Volume": "229", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information Sciences Institute, University of Southern California, Marina del Rey, CA, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Intelligent Internet of Things, Bosch Research and Technology Center, Pittsburgh, PA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Language Technologies Institute, Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Information Sciences Institute, University of Southern California, Marina del Rey, CA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Tetherless World Constellation, Rensselaer Polytechnic Institute, Troy, NY, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Information Sciences Institute, University of Southern California, Marina del Rey, CA, USA"}], "References": []}, {"ArticleId": 89731432, "Title": "An improved loop subdivision to coordinate the smoothness and the number of faces via multi-objective optimization", "Abstract": "<p>3D mesh subdivision is essential for geometry modeling of complex surfaces, which benefits many important applications in the fields of multimedia such as computer animation. However, in the ordinary adaptive subdivision, with the deepening of the subdivision level, the benefits gained from the improvement of smoothness cannot keep pace with the cost caused by the incremental number of faces. To mitigate the gap between the smoothness and the number of faces, this paper devises a novel improved mesh subdivision method to coordinate the smoothness and the number of faces in a harmonious way. First, this paper introduces a variable threshold, rather than a constant threshold used in existing adaptive subdivision methods, to reduce the number of redundant faces while keeping the smoothness in each subdivision iteration. Second, to achieve the above goal, a new crack-solving method is developed to remove the cracks by refining the adjacent faces of the subdivided area. Third, as a result, the problem of coordinating the smoothness and the number of faces can be formulated as a multi-objective optimization problem, in which the possible threshold sequences constitute the solution space. Finally, the Non-dominated sorting genetic algorithm II (NSGA-II) is improved to efficiently search the Pareto frontier. Extensive experiments demonstrate that the proposed method consistently outperforms existing mesh subdivision methods in different settings.</p>", "Keywords": "", "DOI": "10.3233/ICA-210661", "PubYear": 2021, "Volume": "29", "Issue": "1", "JournalId": 24898, "JournalTitle": "Integrated Computer-Aided Engineering", "ISSN": "1069-2509", "EISSN": "1875-8835", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan, Hubei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan, Hubei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Evidence-Based and Translational Medicine, Zhongnan Hospital of Wuhan University, Wuhan, Hubei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, Wuhan, Hubei, China"}], "References": [{"Title": "Learning Single-Image 3D Reconstruction by Generative Modelling of Shape, Pose and Shading", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "4", "Page": "835", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "DRCDN: learning deep residual convolutional dehazing networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "9", "Page": "1797", "JournalTitle": "The Visual Computer"}, {"Title": "A multi-phase blending method with incremental intensity for training detection networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "245", "JournalTitle": "The Visual Computer"}, {"Title": "Multiobjective optimization of deep neural networks with combinations of Lp-norm cost functions for 3D medical image super-resolution", "Authors": "<PERSON>-<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "3", "Page": "233", "JournalTitle": "Integrated Computer-Aided Engineering"}, {"Title": "Multiobjective optimization of deep neural networks with combinations of Lp-norm cost functions for 3D medical image super-resolution", "Authors": "<PERSON>-<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "3", "Page": "233", "JournalTitle": "Integrated Computer-Aided Engineering"}, {"Title": "A full migration BBO algorithm with enhanced population quality bounds for multimodal biomedical image registration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106335", "JournalTitle": "Applied Soft Computing"}, {"Title": "Kinetic Shape Reconstruction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "3D Morphable Face Models—Past, Present, and Future", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Neural subdivision", "Authors": "Hsueh-<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Point2Mesh", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "3D mesh simplification with feature preservation based on Whale Optimization Algorithm and Differential Evolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "4", "Page": "417", "JournalTitle": "Integrated Computer-Aided Engineering"}, {"Title": "A semi-transparent selective undo algorithm for multi-user collaborative editors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "5", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}]}, {"ArticleId": 89731472, "Title": "Enhancement of electrocatalytic activity in tungsten trioxide nanoparticles by UV-light irradiation: Application for simultaneous detection of tyrosine and tryptophan", "Abstract": "Electromagnetic radiation is widely used as a tool for grafting or partially modifying materials for specific applications. Here, we demonstrate that the UV-rays irradiation on tungsten trioxide (WO<sub>3</sub>) nanoparticles, reduces its crystallite size and induces oxygen vacancy, thereby enhancing the catalytic ability significantly. This unique feature of WO<sub>3</sub> NPs has been utilised for the development of a novel electrochemical sensor for the simultaneous determination of two important amino acids l -Tyrosine (Tyr) and l -Tryptophan (Trp). Both the Tyr and Trp act as precursors for catecholamine and indolamine neurotransmitters which help neural communication and regulate the psychological, physiological, and behavioural responses in human beings. Therefore, it is important to know the precise amount of these amino acids in the human body fluids, food, and pharmaceutical supplements. Here, we report the fabrication of a simple method for the determination of Tyr and Trp using UV-rays irradiated WO<sub>3</sub> nanoparticles modified glassy carbon electrode (GCE) without using any additional surface mediator. Among various exposition times, 4 h irradiated WO<sub>3</sub> modified GCE showed an excellent electrocatalytic activity towards the oxidation of Tyr and Trp over wider concentration ranges of 0.01–1100 μM and 0.02–2000 μM for Tyr and Trp with the lowest detection limits of 2.3 nM and 4.4 nM respectively. A good separation in the oxidation potentials was realized during the simultaneous detection of Tyr and Trp from their binary mixture in phosphate buffer saline at pH 6.0. The developed sensor displayed good reproducibility, high sensitivity, and good selectivity towards the determination of the amino acids, making it suitable for the precise determination of Tyr and Trp in farm foods such as milk, curd, and egg.", "Keywords": "UV-irradiation ; Tungsten trioxide ; Tyrosine ; Tryptophan ; Amino acids ; Food sensor", "DOI": "10.1016/j.sna.2021.113011", "PubYear": 2021, "Volume": "331", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Bioelectronics and Biosensors, Alagappa University, Karaikudi, 630003, TN, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Bioelectronics and Biosensors, Alagappa University, Karaikudi, 630003, TN, India"}, {"AuthorId": 3, "Name": "Sekar C.", "Affiliation": "Dept. of Bioelectronics and Biosensors, Alagappa University, Karaikudi, 630003, TN, India;Corresponding author"}], "References": []}, {"ArticleId": 89731473, "Title": "Exfoliated tumor cells in bile as a promising indicator of disease status in cholangiocarcinoma", "Abstract": "Cholangiocarcinoma (CCA) is highly metastatic, extremely difficult to diagnose and is consequently characterized by a low 5-year survival rate. Herein we investigated the potential of using exfoliated tumor cells (ETCs) in bile to serve as an early-warning CCA metastasis indicator. ETCs were isolated from bile using a CCA-specific aptamer conjugated to magnetic beads and identified via immunostaining (CK17<sup>+</sup>CK7<sup>+</sup><PERSON><PERSON><PERSON><sup>+</sup>). A conventional protein biomarker, epithelial cell adhesion molecule (EpCAM), was used for comparison. Circulating tumor cell (CTC) isolation in blood was achieved by a microfluidic system from samples from five CCA patients undergoing chemotherapy, and the enumeration results of ETCs and CTCs were compared with diagnoses derived from computed tomography scans. The CCA-specific aptamer-conjugated magnetic beads effectively bound ETCs from bile samples of 40/40 CCA patients (>1 ETC in 3 ml of bile), and the ETC and CTC count data from patients corresponded well with cancer progression and tumor size. ETCs in bile could serve as promising indicators of disease status in early-advanced stage CCA while CTCs in blood could be representative of late-advanced stage CCA. The developed technique could consequently be a powerful tool in CCA diagnostics and prognostics.", "Keywords": "Bile ; Cholangiocarcinoma ; Circulating tumor cells ; Exfoliated tumor cells ; Microfluidics", "DOI": "10.1016/j.snb.2021.130526", "PubYear": 2021, "Volume": "346", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Power Mechanical Engineering, National Tsing Hua University, Hsinchu, 30013, Taiwan"}, {"AuthorId": 2, "Name": "Tsung-Han Lu", "Affiliation": "Department of Power Mechanical Engineering, National Tsing Hua University, Hsinchu, 30013, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Clinical Medicine, College of Medicine, National Cheng Kung University, Tainan, 70457, Taiwan;National Institute of Cancer Research, National Health Research Institutes, Miaoli, 35053, Taiwan;Department of Internal Medicine, National Cheng Kung University Hospital, College of Medicine, National Cheng Kung University, Tainan, 70403, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Clinical Medicine, College of Medicine, National Cheng Kung University, Tainan, 70457, Taiwan;Department of Internal Medicine, National Cheng Kung University Hospital, College of Medicine, National Cheng Kung University, Tainan, 70403, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Clinical Medicine, College of Medicine, National Cheng Kung University, Tainan, 70457, Taiwan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Genomics Research Centre, Academia Sinica, Taipei, 11529, Taiwan;Department of Applied Science, National Taitung University, Taitung, 95092, Taiwan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Clinical Medicine, College of Medicine, National Cheng Kung University, Tainan, 70457, Taiwan;Department of Surgery, National Cheng Kung University Hospital, College of Medicine, National Cheng Kung University, Tainan, 70403, Taiwan;Corresponding author at: Institute of Clinical Medicine, College of Medicine, National Cheng Kung University, Tainan, 70457, Taiwan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Power Mechanical Engineering, National Tsing Hua University, Hsinchu, 30013, Taiwan;Institute of Biomedical Engineering, National Tsing Hua University, Hsinchu, 30013, Taiwan;Institute of Nanoengineering and Microsystems, National Tsing Hua University, Hsinchu, 30013, Taiwan;Corresponding author at: Department of Power Mechanical Engineering, National Tsing Hua University, Hsinchu, 30013, Taiwan"}], "References": [{"Title": "Aptamer probed isolation of circulating tumor cells in cholangiocarcinoma patients", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "322", "Issue": "", "Page": "128569", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 89731513, "Title": "Gut microbiota specifically mediates the anti-hypercholesterolemic effect of berberine (BBR) and facilitates to predict BBR’s cholesterol-decreasing efficacy in patients", "Abstract": "<b  >Introduction</b> Gut microbiota has been implicated in the pharmacological activities of many natural products. As an effective hypolipidemic agent, berberine (BBR)’s clinical application is greatly impeded by the obvious inter-individual response variation. To date, little evidence exists on the causality between gut microbes and its therapeutic effects, and the linkage of bacteria alterations to the inter-individual response variation. <b  >Objectives</b> This study aims to confirm the causal role of the gut microbiota in BBR’s anti-hyperlipidemic effect and identify key bacteria that can predict its effectiveness. <b  >Methods</b> The correlation between gut microbiota and BBR’s inter-individual response variation was studied in hyperlipidemic patients. The causal role of gut microbes in BBR’s anti-hyperlipidemic effects was subsequently assessed by altered administration routes, co-treatment with antibiotics, fecal microbiota transplantation, and metagenomic analysis. <b  >Results</b> Three-month clinical study showed that BBR was effectively to decrease serum lipids but displayed an obvious response variation. The cholesterol-lowering but not triglyceride-decreasing effect of BBR was closely related to its modulation on gut microbiota. Interestingly, the baseline levels of Alistipes and Blautia could accurately predict its anti-hypercholesterolemic efficiency in the following treatment. Causality experiments in mice further confirmed that the gut microbiome is both necessary and sufficient to mediate the lipid-lowering effect of BBR. The absence of <PERSON><PERSON><PERSON> substantially abolished BBR&#x27;s cholesterol-decreasing efficacy. <b  >Conclusion</b> The gut microbiota is necessary and sufficient for BBR’s hyperlipidemia-ameliorating effect. The baseline composition of gut microbes can be an effective predictor for its pharmacotherapeutic efficacy, providing a novel way to achieve personalized therapy.", "Keywords": "Gut microbiota ; Berberine (BBR) ; Hyperlipidemia ; Hypercholesterolemia ; Inter-individual response variation ; Blautia ; Alistipe s ; AMPK AMP-activated protein kinase ; BBR berberine ; HFD high-fat diet ; H&E Hematoxylin and Eosin ; InsR insulin receptor ; LDL-c low-density lipoprotein cholesterol ; LDLR low-density lipoprotein receptors ; PS the responsive subjects ; NPS the non-responsive subjects ; RF analysis Random forest analysis ; ROC receiving operating characteristic ; SCFAs short-chain fatty acids ; TC total cholesterol ; TG triglycerides", "DOI": "10.1016/j.jare.2021.07.011", "PubYear": 2022, "Volume": "37", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Wu", "Affiliation": "Pharmacology and Toxicology Research Center, Institute of Medicinal Plant Development, Chinese Academy of Medical Sciences & Peking Union Medical College, Beijing 100193, China;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dongzhimen Hospital, Beijing University of Chinese Medicine, Beijing 100700, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Dongzhimen Hospital, Beijing University of Chinese Medicine, Beijing 100700, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Pharmacology and Toxicology Research Center, Institute of Medicinal Plant Development, Chinese Academy of Medical Sciences & Peking Union Medical College, Beijing 100193, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dongzhimen Hospital, Beijing University of Chinese Medicine, Beijing 100700, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Dongzhimen Hospital, Beijing University of Chinese Medicine, Beijing 100700, China"}, {"AuthorId": 7, "Name": "Le Sun", "Affiliation": "Pharmacology and Toxicology Research Center, Institute of Medicinal Plant Development, Chinese Academy of Medical Sciences & Peking Union Medical College, Beijing 100193, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Pharmacology and Toxicology Research Center, Institute of Medicinal Plant Development, Chinese Academy of Medical Sciences & Peking Union Medical College, Beijing 100193, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Pharmacology and Toxicology Research Center, Institute of Medicinal Plant Development, Chinese Academy of Medical Sciences & Peking Union Medical College, Beijing 100193, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dongzhimen Hospital, Beijing University of Chinese Medicine, Beijing 100700, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Pharmacology and Toxicology Research Center, Institute of Medicinal Plant Development, Chinese Academy of Medical Sciences & Peking Union Medical College, Beijing 100193, China;Corresponding authors"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "CAS Key Laboratory of Pathogenic Microbiology and Immunology, Institute of Microbiology, Chinese Academy of Sciences, Beijing 100101, China;Corresponding authors"}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dongzhimen Hospital, Beijing University of Chinese Medicine, Beijing 100700, China;Corresponding authors"}], "References": []}, {"ArticleId": 89731603, "Title": "Attribute-aware explainable complementary clothing recommendation", "Abstract": "<p>Modelling mix-and-match relationships among fashion items has become increasingly demanding yet challenging for modern E-commerce recommender systems. When performing clothes matching, most existing approaches leverage the latent visual features extracted from fashion item images for compatibility modelling, which lacks explainability of generated matching results and can hardly convince users of the recommendations. Though recent methods start to incorporate pre-defined attribute information (e.g., colour, style, length, etc.) for learning item representations and improving the model interpretability, their utilisation of attribute information is still mainly reserved for enhancing the learned item representations and generating explanations via post-processing. As a result, this creates a severe bottleneck when we are trying to advance the recommendation accuracy and generating fine-grained explanations since the explicit attributes have only loose connections to the actual recommendation process. This work aims to tackle the explainability challenge in fashion recommendation tasks by proposing a novel Attribute-aware Fashion Recommender (AFRec). Specifically, AFRec recommender assesses the outfit compatibility by explicitly leveraging the extracted attribute-level representations from each item’s visual feature. The attributes serve as the bridge between two fashion items, where we quantify the affinity of a pair of items through the learned compatibility between their attributes. Extensive experiments have demonstrated that, by making full use of the explicit attributes in the recommendation process, AFRec is able to achieve state-of-the-art recommendation accuracy and generate intuitive explanations at the same time.</p>", "Keywords": "Clothing recommendation; Explainable recommender systems", "DOI": "10.1007/s11280-021-00913-3", "PubYear": 2021, "Volume": "24", "Issue": "5", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The University of Queensland, Brisbane, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The University of Queensland, Brisbane, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Queensland, Brisbane, Australia"}], "References": []}, {"ArticleId": 89731683, "Title": "Playing videogames is associated with reduced awareness of bodily sensations", "Abstract": "Interoception, or the process of receiving, accessing and appraising internal bodily signals, is critical for health and well-being. However, people can sometimes become unaware of bodily sensations, for instance when they experience embodiment over a virtual body from a third person perspective (3PP) during a body illusion. Here, it was investigated to what degree playing videogames in which an avatar is controlled from a 3PP is similarly associated with a reduction in awareness of bodily sensations, and whether such effects are amplified when embodiment over the virtual body is stronger. Watching video on demand was selected as a conservative control condition. 142 participants who frequently played videogames and frequently watched video-on-demand (VoD; at least once a week) in longer sessions (at least 2 h) completed a survey in which they answered questions about reduced awareness of bodily sensations while playing videogames or watching VoD. As predicted, playing videogames was associated with various forms of reduced awareness of bodily signals, such as being unaware of tiredness and getting less sleep, and this reduced awareness was stronger than when people watch VoD. In addition, as predicted, degree of embodiment was positively related to the amount of reduced awareness, and this relation was descriptively stronger for the VG than the VoD condition. These results show that people may indeed become less aware of bodily sensations such as energy level or sleep when they play videogames. Considerations for health are discussed.", "Keywords": "Video games ; Body awareness ; Embodiment ; Body illusion ; Video-on-demand", "DOI": "10.1016/j.chb.2021.106953", "PubYear": 2021, "Volume": "125", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Behavioural Science Institute, Radboud University, <PERSON> 4, 6525 GD, Nijmegen, the Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Behavioural Science Institute, Radboud University, <PERSON> 4, 6525 GD, Nijmegen, the Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Behavioural Science Institute, Radboud University, <PERSON> 4, 6525 GD, Nijmegen, the Netherlands;Corresponding author. Behavioural Science Institute, Radboud University, <PERSON> 4, P.O. Box 9104, 6500 HE, Nijmegen, the Netherlands"}], "References": []}, {"ArticleId": 89731729, "Title": "Damping Performance Analysis of Magnetorheological Damper Based on Multiphysics Coupling", "Abstract": "<p>Magnetorheological (MR) damper performance is evaluated only by single-field analysis in the design process, which can easily lead to larger design errors. Based on this, a simulation method of MR damper considering multiphysics coupling was proposed. According to a certain automobile shock absorber requirement, an MR damper suitable for automobile suspension was designed. The mechanical model, electromagnetic field model, flow field model, and structural stress field model of the MR damper were deduced and established. To investigate the damping performance of the MR damper more accurately, the multiphysics coupling simulation model was established by COMSOL software, and coupling analysis of the electromagnetic field, flow field, and structural stress field was also carried out. The static magnetic field characteristics, dynamic flow field characteristics, stress distribution, and dynamic performance of the proposed MR damper under the action of multiphysics coupling were obtained. The simulation results show that the damping force is 1134.6 N, and the damping adjustable coefficient is 9.1 at an applied current of 1.4 A. A test system was established to analyze the dynamic performance of the MR damper, and the simulation results were compared with the experimental results. The results show that the simulated and experimental results have the same change rule. Moreover, the damping force increases with the applied current, and different external excitations have little effect on the damping force. The damper can output appropriate damping force and has a wide adjustable damping range. The experimental results illustrate that the damping force is 1200.0 N, and the damping adjustable coefficient is 10.1 when the current is 1.4 A. The error between simulation and experiment of the damping force and damping adjustable coefficient is only 5.5% and 9.9%, respectively.</p>", "Keywords": "MR damper; multiphysics coupling; damping performance MR damper ; multiphysics coupling ; damping performance", "DOI": "10.3390/act10080176", "PubYear": 2021, "Volume": "10", "Issue": "8", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Conveyance and Equipment, Ministry of Education, East China Jiaotong University, Nanchang 330013, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Conveyance and Equipment, Ministry of Education, East China Jiaotong University, Nanchang 330013, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Conveyance and Equipment, Ministry of Education, East China Jiaotong University, Nanchang 330013, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Conveyance and Equipment, Ministry of Education, East China Jiaotong University, Nanchang 330013, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Conveyance and Equipment, Ministry of Education, East China Jiaotong University, Nanchang 330013, China"}], "References": [{"Title": "Analytical Approach of a Pure Flow Mode Serpentine Path Rotary Magnetorheological Damper", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "3", "Page": "56", "JournalTitle": "Actuators"}, {"Title": "Internal magnetic field tests and magnetic field coupling model of a three-coil magnetorheological damper", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "19", "Page": "2179", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}, {"Title": "Modern Semi-Active Control Schemes for a Suspension with MR Actuator for Vibration Attenuation", "Authors": "<PERSON>-<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-Flores", "PubYear": 2021, "Volume": "10", "Issue": "2", "Page": "22", "JournalTitle": "Actuators"}]}, {"ArticleId": 89731765, "Title": "FEATURES OF POSITIONING PERSONAL DATABASES OF INFORMATION SYSTEMS OF SCIENTIFIC AND EDUCATIONAL INSTITUTIONS", "Abstract": "<p>В статье проведен анализ нормативных актов по организации обработки и защите персональных данных на предмет размещения баз данных, используемых в информационных системах российских учреждений научно-образовательной сферы. С 2015 года законодательством Российской Федерации определена необходимость размещения баз персональных данных на территории нашей страны. Однако есть случаи, когда хранение персональных данных возможно и за пределами нашей страны. В работе рассмотрены такие исключения, применимые к сфере деятельности научно-образовательных учреждений. На основе автоматизированного анализа реестра операторов персональных данных определено соотношение высших учебных заведений, представивших сведения о месте нахождения своих баз данных в соответствии с Российским законодательством. Более 24% высших учебных заведений такие сведения не предоставили, что может говорить о необходимости оказания университетскому операторскому сообществу методической помощи по вопросам порядка обработки и защиты персональных данных. В ходе проведения контроля за порядком обработки персональных данных по требованию Роскомнадзора необходимо представить, в том числе, документы, подтверждающие расположение баз персональных данных информационных систем в пределах границ Российской Федерации. В работе разработаны рекомендации по размещению и документальному оформлению местонахождения баз данных, использующихся в информационных системах научно-образовательных учреждений, при использовании собственной и предоставляемой третьими лицами ИТ-инфраструктуры.</p><p>The article analyzes normative acts on the organization of processing and protection of personal data for the location of databases used in information systems of Russian institutions of the scientific and educational sphere. Since 2015, Russian legislation has provided for the placement of personal data bases on the territory of our state. However, there are cases when the storage of personal data is possible outside our country. The paper considers such exceptions applicable to the field of activities of scientific and educational institutions. On the basis of an automated analysis of the register of personal data operators, the ratio of higher educational institutions that provided information about the location of their databases in accordance with the legislation of the Russian Federation was determined. More than 24% of higher educational institutions did not provide such information, which may indicate the need to provide the university operator community with methodological assistance on organizing the processing and protection of personal data. In the course of state control over the organization of personal data processing, it is required to submit, among other things, documents confirming the placement of databases of personal data of information systems on the territory of the Russian Federation. Recommendations have been developed for placing and documenting the location of databases, when processing them in the information systems of research and educational institutions using their own and provided by third parties IT infrastructure.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2021.24.1.002", "PubYear": 2021, "Volume": "", "Issue": "1(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>н", "Affiliation": ""}], "References": []}, {"ArticleId": 89731766, "Title": "MODELING, ANALYSIS AND COUNTERING SCENARIOS OF PREPARING COMPUTER ATTACKS REALIZED BY THE GROUP APT3 IN DISTRIBUTED COMPUTER SYSTEMS", "Abstract": "<p>В работе представлены результаты моделирования способов реализации долговременных целенаправленных атак на корпоративные распределённые компьютерные системы со стороны одной из опасных киберпреступных группировкок - Advanced Persistent Threat 3 (APT3). Осуществлено моделирование способов, реализуемых APT3. Построение моделей осуществлялось с использованием аппарата сетей Петри на основании сведений о технических приёмах, содержащихся в базе данных MITRE ATT&CK. Разработанные модели взаимосвязаны по условиям и последствиям реализации основных технических приёмов, актуальных для корпоративных распределённых компьютерных сетей. Реализованный подход также позволяет моделировать меры защиты, регламентируемые нормативными и методическим документами, что даст возможность принятия обоснованных решений при построении системы защиты с учётом специфики защищаемого объекта.</p><p>The paper presents the results of modeling methods for implementing APT-attacks on corporate distributed computer systems by one of the most dangerous cybercrime groups - Advanced Persistent Threat 3 (APT3). The methods implemented by APT3 are modeled. The models were constructed using the Petri nets apparatus based on the information about technical techniques contained in the MITRE ATT&CK database. The developed models are interrelated in terms of the conditions and consequences of the implementation of the main technical techniques relevant for corporate distributed computer networks. The implemented approach also allows to model the protection measures from regulatory and methodological documents, which will make it possible to make informed decisions when building a protection system, taking into account the specifics of the protected object.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2021.24.1.004", "PubYear": 2021, "Volume": "", "Issue": "1(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Алексей Леонидович Сердечный", "Affiliation": ""}, {"AuthorId": 2, "Name": "Алекс<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Айдаркин", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Тарелкин", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Евгеньевна Дешина", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "MODELING, ANALYSIS AND COUNTERING SCENARIOS OF INFORMATION SECURITY THREATS ON CORPORATE DISTRIBUTED COMPUTER SYSTEMS", "Abstract": "<p>В настоящей статье представлены результаты моделирования способов реализации компьютерных атак на корпоративные распределенные компьютерные системы. Предложенные модели способов предназначены для формирования методического обеспечения расчета рисков и выявления оценки защищенности таких систем от актуальных сценариев реализации угроз безопасности информации, которое даёт возможность обоснованного выбора мер защиты. Построение моделей способов реализации компьютерных атак осуществлялось с использованием аппарата сетей Петри на основании сведений, содержащихся в базе данных MITRE ATT&CK. Разработанные модели взаимосвязаны по условиям и последствиям реализации основных технических приёмов, определённых в базе данных ATT&CK и актуальных для корпоративных распределённых компьютерных сетей (условия и последствия моделируются позициями сети Петри, а сами технические приёмы - переходами сети Петри). Также в статье продемонстрирована возможность наращивания модели за счёт включения в неё моделей мер защиты, используемых в нормативных и методических документах ФСТЭК России.</p><p>This article presents the results of modeling computer attack methods on corporate distributed computer systems. The proposed models of methods are intended for the formation of methodological support for calculating risks and identifying the assessment of the security of such systems from current scenarios of information security threats, which makes it possible to choice of informed security measures. The model development of ways to implement computer attacks was carried out using the Petri nets approach based on the information contained in the MITRE ATT&CK database. The developed model is interconnected on the conditions and consequence of the basic techniques defined in the database ATT&CK and relevant for enterprise distributed computer networks (conditions and consequence are simulated positions Petri nets themselves and techniques - transitions Petri nets). In addition, the article demonstrates the possibility of increasing the model by including models of protection measures against the considered methods of implementing computer attacks, defined in the regulatory and methodological documents of the FSTEC of Russia</p>", "Keywords": "", "DOI": "10.36622/VSTU.2021.24.1.006", "PubYear": 2021, "Volume": "", "Issue": "1(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Алексей Леонидович Сердечный", "Affiliation": ""}, {"AuthorId": 2, "Name": "Артем Але<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Шевелюхин", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Тарелкин", "Affiliation": ""}, {"AuthorId": 4, "Name": "Алекс<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>бурин", "Affiliation": ""}], "References": []}, {"ArticleId": 89731768, "Title": "MODELING, ANALYSIS AND COUNTERING SCENARIOS OF PREPARING COMPUTER ATTACKS REALIZED BY THE GROUP APT29 IN DISTRIBUTED COMPUTER SYSTEMS", "Abstract": "<p>Статья посвящена моделированию компьютерных атак на распределённые корпоративные компьютерные системы, на примере действий группировки Advanced Persistent Threat 29 (APT29). В статье предлагается подход моделирования способов, реализуемых указанной группировкой, а также мер защиты от них. Подход основан на использовании аппарата сетей Петри, а также сведений о технических приёмах, предоставляемых в рамках проекта MITRE ATT&CK. Разработанные модели учитывают связи по условиям и последствиям действий, совершаемых группировкой APT29 в ходе атак на распределённые корпоративные системы. Также в статье продемонстрирована возможность наращивания модели за счёт включения в неё моделей мер защиты от рассмотренных способов реализации компьютерных атак. Предлагаемые модели могут быть дополнены за счёт моделирования новых способов реализации компьютерных атак, используемых другими кибергруппировками. Кроме того, модели могут быть расширены до моделей сети Петри-Маркова путём реализации частным методик расчёта вероятностно-временных характеристик для фрагментов предлагаемых моделей.</p><p>The article is devoted to modeling computer attacks on distributed corporate computer systems, using the example of the actions of the Advanced Persistent Threat 29 (APT29) group. The article proposes an approach to modeling the methods implemented by this grouping, as well as measures to protect against them. The approach is based on Petri nets and information about the techniques (MITRE ATT&CK project). The developed models take into account the relationship between the conditions and consequences of actions committed by the APT29 group during attacks on distributed enterprise systems. The article also demonstrates the possibility of increasing the model by including models of protection measures against the considered methods of implementing computer attacks. The proposed models can be supplemented by modeling new ways of implementing computer attacks used by other cyber groups. In addition, the models can be extended to Petri-Markov network models by implementing special methods for calculating probabilistic-time characteristics for fragments of the proposed models.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2021.24.1.008", "PubYear": 2021, "Volume": "", "Issue": "1(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Алексей Леонидович Сердечный", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>ав<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>евич Краюшкин", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Тарелкин", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89731769, "Title": "RISK ASSESSMENT OF SUC<PERSON><PERSON>FUL IMPLEMENTATION OF SPOOFING ATTACKS ON VOICE AUTHENTICATION ELEMENTS IN DISTRIBUTED COMPUTER SYSTEMS", "Abstract": "<p>Цель исследования заключается в рекомендациях по улучшению мер защиты голосовых систем аутентификации от реализации спуфинг-атак. В работе рассмотрены разнообразные виды спуфинг-атак и выделены самые опасные на данный момент. Разработана методика оценки защищенности голосовых систем аутентификации, учитывающая воздействие различных видов спуфиг-атак на системы голосовой аутентификации. Проведены количественные эксперименты, показывающие преимущество разработанной методики, в сравнении с существующими аналогами. Описан комплекс программных средств оценки защищенности систем голосовой аутентификации, который позволяет автоматизировать процесс оценки при проведении технологических испытаний. Полученные результаты могут быть использованы не только для оценки защищенности систем голосовой аутентификации, но и для проведения функционального и нагрузочного тестирования. Применение предложенного комплекса и методики оценки в дальнейшем может помочь в разработке технических решений по увеличению защищенности голосовых биометрических систем от реализации спуфинг-атак.</p><p>The purpose of the study is to provide recommendations for improving the protection of voice authentication systems against spoofing attacks. The paper considers various types of spoofing attacks and identifies the most dangerous ones at the moment. A method for assessing the security of voice authentication systems has been developed, taking into account the impact of various types of spoofing attacks on voice authentication systems. Quantitative experiments were carried out, showing the advantage of the developed method in comparison with existing analogues. A set of software tools for assessing the security of voice authentication systems is described, which allows you to automate the evaluation process during technological tests. The results obtained can be used not only to assess the security of voice authentication systems, but also to conduct functional and load testing. The use of the proposed complex and evaluation methodology in the future can help in the development of technical solutions to increase the security of voice biometric systems from spoofing attacks.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2021.24.1.011", "PubYear": 2021, "Volume": "", "Issue": "1(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Алек<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Сергей Алекс<PERSON><PERSON><PERSON><PERSON>ович Бодячевский", "Affiliation": ""}, {"AuthorId": 3, "Name": "Ник<PERSON><PERSON><PERSON><PERSON> Ни<PERSON><PERSON><PERSON><PERSON>евич Толстых", "Affiliation": ""}], "References": []}, {"ArticleId": 89731784, "Title": "DETECTION DESTRUCTIVE CONTENT IN SOCIAL MEDIA BASED ON MACHINE LEARNING MODELS", "Abstract": "<p>В статье обсуждаются модели классификации текстового контента и методы его предварительной обработки с целью выявления деструктивных воздействий в социальных медиа. Показано, что основным источником деструктивного контента выступает профиль пользователей, характеризующийся набором личным данных, содержанием публикаций, параметрами сообщества, аккаунтов сети, сообщений и чатов. Говорится об актуальности автоматизированного сбора и анализа данных с помощью моделей прецедентного и дедуктивного обучения. Рассматриваются их основные разновидности и задачи, решаемые на их основе, включающие прогнозирование и типологизацию в аспекте деструктивного содержания текстов, снижение размерности признаков их описания. Исследованы и применены основные методы векторизации текстов: Bag of Words, TF_IDF, Word2vec. На практических корпусах текстов из социальной сети ВКонтакте решены задачи выявления деструктивного контента, связанного с радикальным исламом. Показано, что с помощью примененных моделей и методов все тексты, включающие деструктивный контент, классифицированы верно. Наиболее высокую точность (0,97) при решении задачи распознавания деструктивного контента дает системная интеграция алгоритма векторизации Bag of Words, метода главных компонент для снижения пространства признаков описания текстов и логистической регрессии или случайного леса как моделей обучения. Сделан вывод, что наборы данных, имеющие связь с исламским радикализмом, характеризуются достаточно четкими признаками, которые хорошо вычисляемы с помощью современных моделей, методов и алгоритмов, и могут эффективно применяться для автоматизированной классификации текстовых массивов с целью выявления их деструктивной направленности. Развитие направления, представленного в статье, связано с увеличением исследуемых корпусов документов, более детальным анализом текстов на основе сложных моделей распознавания латентной экстремистской пропаганды, в том числе - представленной в фото, аудио- и видеоформатах.</p><p>The article discusses models of classification of text content and methods of its pre-processing in order to identify destructive influences in social media. It is shown that the main source of destructive content is the user profile, which is characterized by a set of personal data, the content of publications, community parameters, network accounts, messages and chats. Automated data collection and analysis using case-based and deductive learning models is discussed. We consider their main varieties and the tasks solved on their basis, including forecasting and typology in the aspect of the destructive content of texts, reducing the dimension of the features of their description. The main methods of text vectorization are investigated and applied: Bag of Words, TF_IDF, Word2vec. The tasks of identifying destructive content related to Islamic radicalism are solved on the practical corpus of texts from the social network VKontakte. It is shown that using the applied models and methods, all texts that include destructive content are classified correctly. The highest accuracy (0.97) in solving the problem of recognizing destructive content is provided by the system integration of the Bag of Words vectorization algorithm, the principal component method for reducing the feature space of text descriptions, and logistic regression or random forest as learning models. It is concluded that the data sets associated with Islamic radicalism are characterized by sufficiently clear features that are well calculated using modern models, methods and algorithms, and can be effectively used for automated classification of text arrays in order to identify their destructive orientation. The development of the direction presented in the article is associated with an increase in the studied corpus of documents, a more detailed analysis of texts based on complex models for recognizing latent extremist propaganda, including those presented in photo, audio and video formats.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2021.24.1.001", "PubYear": 2021, "Volume": "", "Issue": "1(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Алена Дмитриевна Реброва", "Affiliation": ""}, {"AuthorId": 3, "Name": "Алексан<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89731809, "Title": "Downscaling of far-red solar-induced chlorophyll fluorescence of different crops from canopy to leaf level using a diurnal data set acquired by the airborne imaging spectrometer HyPlant", "Abstract": "Remote sensing-based measurements of solar-induced chlorophyll fluorescence (SIF) are useful for assessing plant functioning at different spatial and temporal scales. SIF is the most direct measure of photosynthesis and is therefore considered important to advance capacity for the monitoring of gross primary production (GPP) while it has also been suggested that its yield facilitates the early detection of vegetation stress. However, due to the influence of different confounding effects, the apparent SIF signal measured at canopy level differs from the fluorescence emitted at leaf level, which makes its physiological interpretation challenging. One of these effects is the scattering of SIF emitted from leaves on its way through the canopy. The escape fraction ( f esc ) describes the scattering of SIF within the canopy and corresponds to the ratio of apparent SIF at canopy level to SIF at leaf level. In the present study, the fluorescence correction vegetation index (FCVI) was used to determine f esc of far-red SIF for three structurally different crops (sugar beet, winter wheat, and fruit trees) from a diurnal data set recorded by the airborne imaging spectrometer HyPlant. This unique data set, for the first time, allowed a joint analysis of spatial and temporal dynamics of structural effects and thus the downscaling of far-red SIF from canopy ( SIF 760 canopy ) to leaf level ( SIF 760 leaf ). For a homogeneous crop such as winter wheat, it seems to be sufficient to determine f esc once a day to reliably scale SIF<sub>760</sub> from canopy to leaf level. In contrast, for more complex canopies such as fruit trees, calculating f esc for each observation time throughout the day is strongly recommended. The compensation for structural effects, in combination with normalizing SIF<sub>760</sub> to remove the effect of incoming radiation, further allowed the estimation of SIF emission efficiency ( ε SIF ) at leaf level, a parameter directly related to the diurnal variations of plant photosynthetic efficiency.", "Keywords": "Solar-induced chlorophyll fluorescence ; SIF ; HyPlant ; Diurnal course ; Fluorescence correction vegetation index ; FCVI ; Fluorescence escape fraction ; Photosynthetically active radiation", "DOI": "10.1016/j.rse.2021.112609", "PubYear": 2021, "Volume": "264", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Bio- and Geosciences, IBG-2: Plant Sciences, Forschungszentrum Jülich GmbH, Wilhelm-Johnen-Straße, 52428 J<PERSON>lich, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": "Laboratory of Earth Observation, Image Processing Laboratory, University of Valencia, C/ Catedrátic<PERSON>, 2, 46980 Paterna, Valencia, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Remote Sensing of Environmental Dynamics Lab., DISAT, University of Milano-Bicocca, Piazza della Scienza 1, 20126 Milano, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Geography, University of Zurich, Winterthurerstrasse 190, 8057 Zurich, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Earth and Atmospheric Sciences and Department of Biological Sciences, University of Alberta, 11335 Saskatchewan Drive, Edmonton, AB T6G 2E3, Canada;Center for Advanced Land Management Information Technologies, School of Natural Resources, University of Nebraska–Lincoln, 3310 Holdrege Street, Lincoln, NE 68583, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute of Bio- and Geosciences, IBG-2: Plant Sciences, Forschungszentrum Jülich GmbH, Wilhelm-Johnen-Straße, 52428 Jülich, Germany"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Institute of Bio- and Geosciences, IBG-2: Plant Sciences, Forschungszentrum Jülich GmbH, Wilhelm-Johnen-Straße, 52428 Jülich, Germany"}, {"AuthorId": 8, "Name": "<PERSON>-<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Bio- and Geosciences, IBG-2: Plant Sciences, Forschungszentrum Jülich GmbH, Wilhelm-Johnen-Straße, 52428 Jülich, Germany"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Field Lab Campus Klein-Altendorf, Faculty of Agriculture, University of Bonn, Campus Klein-Altendorf 1, 53359 Rheinbach, Germany"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Bio- and Geosciences, IBG-2: Plant Sciences, Forschungszentrum Jülich GmbH, Wilhelm-Johnen-Straße, 52428 Jülich, Germany"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Institute of Bio- and Geosciences, IBG-2: Plant Sciences, Forschungszentrum Jülich GmbH, Wilhelm-Johnen-Straße, 52428 Jülich, Germany"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Geo-Information Science and Earth Observation (ITC), University of Twente, Hengelosestraat 99, Enschede, 7500, AE, the Netherlands"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "Institute of Bio- and Geosciences, IBG-2: Plant Sciences, Forschungszentrum Jülich GmbH, Wilhelm-Johnen-Straße, 52428 Jülich, Germany"}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Geo-Information Science and Earth Observation (ITC), University of Twente, Hengelosestraat 99, Enschede, 7500, AE, the Netherlands"}, {"AuthorId": 15, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Bio- and Geosciences, IBG-2: Plant Sciences, Forschungszentrum Jülich GmbH, Wilhelm-Johnen-Straße, 52428 Jülich, Germany"}], "References": [{"Title": "Fluorescence Correction Vegetation Index (FCVI): A physically based reflectance index to separate physiological and non-physiological information in far-red sun-induced chlorophyll fluorescence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111676", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Reduction of structural impacts and distinction of photosynthetic pathways in a global estimation of GPP from space-borne solar-induced chlorophyll fluorescence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111722", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Satellite footprint data from OCO-2 and TROPOMI reveal significant spatio-temporal and inter-vegetation type variabilities of solar-induced fluorescence yield in the U.S. Midwest", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "241", "Issue": "", "Page": "111728", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Canopy structure explains the relationship between photosynthesis and sun-induced chlorophyll fluorescence in crops", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "241", "Issue": "", "Page": "111733", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Re-absorption and scattering of chlorophyll fluorescence in canopies: A revised approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "246", "Issue": "", "Page": "111860", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Practical approaches for normalizing directional solar-induced fluorescence to a standard viewing geometry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "255", "Issue": "", "Page": "112171", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Solar-induced chlorophyll fluorescence is non-linearly related to canopy photosynthesis in a temperate evergreen needleleaf forest during the fall transition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "258", "Issue": "", "Page": "112362", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 89731825, "Title": "FORMALIZATION OF THE DEFINITION APPROACH THE LEVEL OF MOTIVATION OF THE VIOLATOR", "Abstract": "<p>В статье рассматривается подход, связанный с определение уровня мотивации нарушителя к совершению того или иного противоправного деяния относительно ресурсов организации. Предлагаемый подход, возможно, применять службам безопасности предприятия относительно работников как при приеме на работу, так и в процессе работы с целью выявления высокого уровня мотивации к совершению противоправного деяния и выполнению различных мер по нейтрализации или минимизации данного уровня. Уровень мотивации напрямую влияет на потенциал нарушителя и на вероятность реализации им угрозы, так как не только наличие на объекте средств защиты или наличие у нарушителя современных средств атак приводит к реализации угрозы. В первую очередь к ней приводит заинтересованность в совершении данного деяния, мотивируемость и цели, которые преследует нарушитель.</p><p>The article considers an approach related to determining the level of motivation of the violator to commit a particular illegal act with respect to the resources of the organization. The proposed approach can be applied by the security services of the enterprise in relation to employees both when hiring and in the process of work in order to identify a high level of motivation to commit an illegal act and to implement various measures to neutralize or minimize this level. The level of motivation directly affects the potential of the violator and the probability of the threat implementation, since not only the presence of protective equipment on the object or the presence of modern means of attack on the violator leads to the implementation of the threat. First of all, it leads to the interest in the commission of this act, the motivation and goals that the violator pursues.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2021.24.1.003", "PubYear": 2021, "Volume": "", "Issue": "1(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Оксана Михайловна Голембиовская", "Affiliation": ""}, {"AuthorId": 2, "Name": "Екатер<PERSON>на Владимировна Кондрашова", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Ма<PERSON><PERSON><PERSON><PERSON> М<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>биовский", "Affiliation": ""}], "References": []}, {"ArticleId": 89731910, "Title": "AGIMM tracking filter algorithm based on manoeuvring feature correction", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2021.116553", "PubYear": 2021, "Volume": "19", "Issue": "1", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON> fang <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON> long <PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> wei <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89731925, "Title": "Older adults and online communities: recent findings, gaps and opportunities", "Abstract": "Rapid population ageing and wider access to technology create opportunities for older adults to engage in online communities where they participate in various forms of social exchanges. Older adults are thriving online and it is paramount for research to present the recent directions of scholarly works to depict the everyday digital lives of this specific social cluster. Through a systematic literature review, we identify 20 papers investigating online communities of older adults from the last six years. Participation by older adults in online communities are primarily motivated by cognitive benefits, engagement and enjoyment. Further inquiry revealed that they are adopting communities in social media, that socio-cultural contextualisation is vital and modern methodologies are appropriate. We direct the attention of future studies towards the impact of social media, effects of phases in late life and inclusion of older adults from different cultural backgrounds to depict today’s reality of their digital lives. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Literature review; Older adults; Online communities; Social media; Web-based communities", "DOI": "10.1504/IJWBC.2021.116598", "PubYear": 2021, "Volume": "17", "Issue": "3", "JournalId": 9397, "JournalTitle": "International Journal of Web Based Communities", "ISSN": "1477-8394", "EISSN": "1741-8216", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "De La Salle University, 2401 Taft Avenue, Manila, Philippines; José R<PERSON>l University, #80 Shaw Boulevard, Mandaluyong City, Philippines"}, {"AuthorId": 2, "Name": "<PERSON>ia C<PERSON>", "Affiliation": "De La Salle University, 2401 Taft Avenue, Manila, Philippines"}], "References": []}, {"ArticleId": 89731936, "Title": "Integrating and navigating engineering design decision-related knowledge using decision knowledge graph", "Abstract": "Designers are usually facing a problem of finding information from a huge amount of unstructured textual documents in order to prepare for a decision to be made. The major challenge is that knowledge embedded in the textual documents are difficult to search at a semantic level and therefore not ready to support decisions in a timely manner. To address this challenge, in this paper we propose a knowledge-graph-based method for integrating and navigating decision-related knowledge in engineering design. The presented method is based on a meta-model of decision knowledge graph (mDKG) that is grounded in the compromise Decision Support Problem (cDSP) construct which is used by designers as a means to formulate design decisions linguistically and mathematically. Based on the mDKG, we propose a procedure for automatically converting word-based cDSPs to knowledge graph through natural language processing, and a procedure for rapidly and accurately navigating decision-related knowledge through divergence and convergence processes. The knowledge-graph-based method is verified using the textual data from the supply chain design domain. Results show that our method has better performance than the conventional keyword-based searching method in terms of both effectiveness and efficiency in finding the target knowledge.", "Keywords": "Design ; Decision support ; Knowledge graph ; Searching ; Navigation", "DOI": "10.1016/j.aei.2021.101366", "PubYear": 2021, "Volume": "50", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, No. 5, Zhongguancun South Street, Haidian District, Beijing 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, No. 5, Zhongguancun South Street, Haidian District, Beijing 100081, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Systems Realization Laboratory, Division of Industrial Design, School of Engineering, The University of Liverpool, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Beijing Institute of Technology, No. 5, Zhongguancun South Street, Haidian District, Beijing 100081, China;Corresponding author"}], "References": [{"Title": "A cooperative game theory based user-centered medical device design decision approach under uncertainty", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101204", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 89732022, "Title": "Room for e-learning development: the interaction between learners and the interface", "Abstract": "The present paper enhances the online interaction between teacher and student, through the proposal and development of several innovative assistive and interactional features added to a new e-learning user interface (ELUI). Using surveys distributed to 102 students and interviews with ten professors in the same community setting, the study measures student satisfaction toward the suggested interface and explores professors’ perceptions regarding the effect of these features as contrasted to the physical classroom. The results reveal that all embedded features of interactional communication in the proposed ELUI resulted in a high level of satisfaction from respondents. Interviews with professors revealed potential concerns of online teaching, suggesting that the proposed ELUI would complement, rather than replace, the traditional classroom. The results further suggest that professors, academic decision-makers and developers should all consider student satisfaction in terms of adopting these features in e-learning systems. These stakeholders should be proactive in recognising and promoting the benefits of these features to address implementation concerns and maximise the value of learning systems. The stakeholders also should offer a user-friendly system, an interactive learning environment, support for future updates through useful system content, and personalisation through the ability to modify content and layout. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "E-learning user interface; Electronic learning; ELUI; Interactional communication; Professors’ perceptions; Student satisfaction", "DOI": "10.1504/IJWBC.2021.116635", "PubYear": 2021, "Volume": "17", "Issue": "3", "JournalId": 9397, "JournalTitle": "International Journal of Web Based Communities", "ISSN": "1477-8394", "EISSN": "1741-8216", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Management, Al Ain University, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Management Information Systems, Al Ain University, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Education, Laurentian University, Sudbury, ON, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "College of Business, Al Ain University, Al Ain, United Arab Emirates"}], "References": []}, {"ArticleId": 89732023, "Title": "A lexicon weighted sentiment analysis approach on Twitter", "Abstract": "Sentiment analysis in social media has grabbed more considerable attention because the results of such studies are highly applicable in social, economic, and political contexts. This study aimed to present an approach for collecting data from Twitter while storing and analysing the data using the hadoop as a big data platform as well as a hybrid trial and error model using the <PERSON><PERSON> theorem plus a dictionary of words for the sentiment analysis. This method classifies tweets in two positive and negative classes based on the probability of positive words and negative words. According to the results, the accuracy of the proposed approach boosted from 67% to 71%. Then a new idea was employed in form of a weighted dictionary to achieve a higher accuracy. As such. the accuracy of the proposed approach reached a rate of 78% according to the results of another analysis conducted on the same data. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Big data; Hadoop; Lexicon sentiment analysis; TSA; Twitter sentiment analysis", "DOI": "10.1504/IJWBC.2021.116641", "PubYear": 2021, "Volume": "17", "Issue": "3", "JournalId": 9397, "JournalTitle": "International Journal of Web Based Communities", "ISSN": "1477-8394", "EISSN": "1741-8216", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, University of Science and Culture, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Science and Culture, Tehran, Iran"}], "References": []}, {"ArticleId": 89732087, "Title": "A novel version of Cuckoo search algorithm for solving optimization problems", "Abstract": "In this paper, a Cuckoo search algorithm, namely the New Movement Strategy of Cuckoo Search (NMS-CS), is proposed. The novelty is in a random walk with step lengths calculated by <PERSON><PERSON><PERSON> distribution. The step lengths in the original Cuckoo search (CS) are significant terms in simulating the Cuckoo bird's movement and are registered as a scalar vector. In NMS-CS, step lengths are modified from the scalar vector to the scalar number called orientation parameter. This parameter is controlled by using a function established from the random selection of one of three proposed novel functions. These functions have diverse characteristics such as; convex, concave, and linear, to establish a new strategy movement of Cuckoo birds in NMS-CS. As a result, the movement of NMS-CS is more flexible than a random walk in the original CS. By using the proposed functions, NMS-CS achieves the distance of movement long enough at the first iterations and short enough at the last iterations. It leads to the proposed algorithm achieving a better convergence rate and accuracy level in comparison with CS. The first 23 classical benchmark functions are selected to illustrate the convergence rate and level of accuracy of NMS-CS in detail compared with the original CS. Then, the other Algorithms such as Particle Swarm Optimization (PSO), Gravitational Search Algorithm (GSA), and Grey Wolf Optimizer (GWO) are employed to compare with NMS-CS in a ranking of the best accuracy. In the end, three engineering design problems (tension/compression spring design, pressure vessel design and welded beam design) are employed to demonstrate the effect of NMS-CS for solving various real-world problems. The statistical results show the potential performance of NMS-CS in a widespread class of optimization problems and its excellent application for optimization problems having many constraints. Source codes of NMS-CS is publicly available at http://goldensolutionrs.com/codes.html .", "Keywords": "Optimization ; Lévy distribution ; Benchmark test functions ; Cuckoo search algorithm", "DOI": "10.1016/j.eswa.2021.115669", "PubYear": 2021, "Volume": "186", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Faculty of Civil Engineering, Ho Chi Minh City Open University, Ho Chi Minh City, Viet Nam;Corresponding authors at: Faculty of Civil Engineering, Ho Chi Minh City Open University, Ho Chi Minh City, Vietnam (T. <PERSON>uo<PERSON>-Le). CIRTech Institute, Ho Chi Minh City University of Technology (HUTECH), Ho Chi Minh City, Viet Nam (M.A. Wahab)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Civil Engineering, Ho Chi Minh City Open University, Ho Chi Minh City, Viet Nam;Soete Laboratory, Faculty of Engineering and Architecture, Ghent University, Technologiepark, Zwijnaarde 903, B-9052 Zwijnaarde, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Soete Laboratory, Faculty of Engineering and Architecture, Ghent University, Technologiepark, Zwijnaarde 903, B-9052 Zwijnaarde, Belgium"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Soete Laboratory, Faculty of Engineering and Architecture, Ghent University, Technologiepark, Zwijnaarde 903, B-9052 Zwijnaarde, Belgium;CIRTech Institute, Ho Chi Minh City University of Technology (HUTECH), Ho Chi Minh City, Viet Nam;Corresponding authors at: Faculty of Civil Engineering, Ho Chi Minh City Open University, Ho Chi Minh City, Vietnam (T. Cuong-Le). CIRTech Institute, Ho Chi Minh City University of Technology (HUTECH), Ho Chi Minh City, Viet Nam (M.A. Wahab)"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, Ho Chi Minh City University of Technology, Viet Nam"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Artificial Intelligence Research and Optimisation, Torrens University Australia, Fortitude Valley, Brisbane 4006, QLD, Australia;Yonsei Frontier Lab, Yonsei University, Seoul, Korea"}], "References": [{"Title": "Balancing composite motion optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "520", "Issue": "", "Page": "250", "JournalTitle": "Information Sciences"}, {"Title": "Improved Cuckoo Search algorithmic variants for constrained nonlinear optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102865", "JournalTitle": "Advances in Engineering Software"}]}, {"ArticleId": 89732098, "Title": "MC-GP-HSMMを用いたマルチモーダル情報の分節化によるインタラクションのルール学習", "Abstract": "Humans can learn rules of interactions by observing others' interactions. To learn various interactions flexibly, we believe such an ability is also important for robots. To this end, we proposed Coupled Gaussian Process-Hidden Semi-Markov Models (C-GP-HSMM) that enabled robots to learn rules of interactions by observing motions of two persons. However, not only motions but also multimodal information is used in the actual human interactions. Therefore, in this paper, we extend C-GP-HSMM into a method to learn rules from multimodal data. In experiments, we show the proposed model can estimate the rules of a game, which contains multimodal interactions.", "Keywords": "Human-robot Interaction;Multimodal Interaction;Unsupervised Learning;Rule Learning, Motion Segmentation;Gaussian Process;Hidden Semi-Markov Model", "DOI": "10.7210/jrsj.39.553", "PubYear": 2021, "Volume": "39", "Issue": "6", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}], "References": []}, {"ArticleId": 89732110, "Title": "Two decades of game concepts in digital learning environments – A bibliometric study and research agenda", "Abstract": "In recent years, using game concepts for educational purposes in digital environments has become continually more popular and relevant. Games can be used to motivate and engage users in regular system use and, in the end, support learners in achieving better learning outcomes. In this context, different kinds of game concepts exist, such as gamification or serious games, each with a different perspective and usefulness in digital learning environments. Because developing and using with game concepts in digital learning environments has recently become more important, and developing them is still not fully established, questions arise about future research directions involving games in digital learning. Therefore, this study aims to identify the state of the field and determine what is relevant when using game concepts in digital learning. To achieve this goal, we present the results of a bibliometric analysis considering more than 10,000 articles between 2000 and 2019 and summarize them to develop a research agenda. This agenda supports researchers and practitioners in identifying avenues for future research. We contribute to theory by providing a detailed understanding of the relevance of game concepts in digital learning. We propose a research agenda to assist researchers in planning future approaches with and about gamification concepts in digital learning. Practical implications are proposed by demonstrating what should be considered when using game concepts in learning environments.", "Keywords": "Games ; Simulations ; Mobile learning ; Gamification ; Game concepts ; Bibliometric study", "DOI": "10.1016/j.compedu.2021.104296", "PubYear": 2021, "Volume": "173", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "Sofia <PERSON>ö<PERSON>", "Affiliation": "University of Kassel, Information Systems, Research Center for IS Design (ITeG), Pfannkuchstraße 1, 34121, Kassel, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing, University of Eastern Finland, Joensuu Campus, Yliopistokatu 2, P.O. Box 111, fi-80100, Joensuu, Finland;School of Electrical Engineering and Computer Science, Media Technology & Interaction Design, KTH Royal Institute of Technology, Lindstedtsvägen 3, SE-100 44 Stockholm, Sweden"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of St. Gallen, Institute of Information Management, Müller Friedbergstrasse 8, 9000, St. Gallen, Switzerland;Corresponding author"}], "References": [{"Title": "The Impact of Gamification on Learning Outcomes of Computer Science Majors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Computing Education"}, {"Title": "Capturing the complexity of gamification elements: a holistic approach for analysing existing and deriving novel gamification designs", "Authors": "<PERSON> <PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "6", "Page": "641", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Desperately seeking the artefacts and the foundations of native theory in gamification research: why information systems researchers can play a legitimate role in this discourse and how they can better contribute", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "6", "Page": "609", "JournalTitle": "European Journal of Information Systems"}]}, {"ArticleId": 89732112, "Title": "Constrained visual predictive control of tendon-driven continuum robots", "Abstract": "Due to their compliance, continuum robots (CRs) hold great potential for many applications. However, despite intensive recent research, their control poses significant challenges. The nonlinear kinematic behavior, limited actuation channels, and physical and environmental constraints typically associated with CRs hinder the development of effective control strategies. In this paper, a visual predictive position control method for tendon-driven continuum robots is proposed. The developed control approach integrates the advantages of image-based visual servoing and model predictive control techniques to enable direct end-point control in the presence of constraints and improve the control robustness to system uncertainties, sensing noise, and modeling errors. Both simulation and experimental results demonstrate the effectiveness of the method.", "Keywords": "Continuum robot ; Tendon driven ; Visual servoing ; Predictive control ; Robustness ; Steerable catheter", "DOI": "10.1016/j.robot.2021.103856", "PubYear": 2021, "Volume": "145", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Biomedical Engineering, Ryerson University, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mechanical and Industrial Engineering, Ryerson University, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical and Industrial Engineering, Ryerson University, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical and Industrial Engineering, Ryerson University, 350 Victoria Street, Toronto Ontario, Canada, M5B 2K3;Corresponding author"}], "References": [{"Title": "A Fuzzy Reinforcement Learning Approach for Continuum Robot Control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "3-4", "Page": "809", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Gaussian process-based nonlinear predictive control for visual servoing of constrained mobile robots with unknown dynamics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "136", "Issue": "", "Page": "103712", "JournalTitle": "Robotics and Autonomous Systems"}]}, {"ArticleId": 89732113, "Title": "Simultaneous measurement of strain and temperature based on no-core fiber and two-core fiber", "Abstract": "An optical fiber sensor based on no-core fiber (NCF) and two-core fiber(TCF) for simultaneous measurement of strain and temperature is proposed. The sensor is based on a hybrid structure of NCF-TCF-NCF-TCF-NCF. The proposed sensor solves the problem of cross-sensitivity to a certain extent. The sensitivities of strain are -5.46 p m / μ ε and -3.36 p m / μ ε in the range from 0 to 600 μ ε . The sensitivities of temperature are 48.6 p m / ℃ and 25.1 p m / ℃ in the ranging from 35 to 60 ℃ , respectively. By analyzing the amplitude of spatial frequency spectrum, the sensitivities of stain and temperature are 0.00025 a . u . / μ ε and 0.0118 a . u . / ℃ .", "Keywords": "Fiber optical sensor ; No-core fiber ; Two-core fiber", "DOI": "10.1016/j.sna.2021.113013", "PubYear": 2021, "Volume": "331", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of All Optical Network & Advanced Telecommunication Network of EMC, Institute of Lightwave Technology, Beijing Jiaotong University, Beijing, 100044, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of All Optical Network & Advanced Telecommunication Network of EMC, Institute of Lightwave Technology, Beijing Jiaotong University, Beijing, 100044, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Lab of All Optical Network & Advanced Telecommunication Network of EMC, Institute of Lightwave Technology, Beijing Jiaotong University, Beijing, 100044, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of All Optical Network & Advanced Telecommunication Network of EMC, Institute of Lightwave Technology, Beijing Jiaotong University, Beijing, 100044, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Lab of All Optical Network & Advanced Telecommunication Network of EMC, Institute of Lightwave Technology, Beijing Jiaotong University, Beijing, 100044, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of All Optical Network & Advanced Telecommunication Network of EMC, Institute of Lightwave Technology, Beijing Jiaotong University, Beijing, 100044, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> Wang", "Affiliation": "Key Lab of All Optical Network & Advanced Telecommunication Network of EMC, Institute of Lightwave Technology, Beijing Jiaotong University, Beijing, 100044, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of All Optical Network & Advanced Telecommunication Network of EMC, Institute of Lightwave Technology, Beijing Jiaotong University, Beijing, 100044, China"}], "References": []}, {"ArticleId": 89732131, "Title": "A portable, battery-powered photoelectrochemical aptasesor for field environment monitoring of E. coli O157:H7", "Abstract": "Escherichia coli O157:H7 ( E. coli O157:H7 ) is typically associated with food- and water-borne outbreaks, thus making continuous monitoring essential. Photoelectrochemical (PEC) detection, which harvests the unique photo-to-electric property of photoactive species, has emerged as an exciting tool for bioanalysis. However, the current attempts are mostly carried out in lab, and less explored in field. Here, a simple PEC aptasensor for E. coli O157:H7 was designed and optimized (LOD: 200 cfu/mL). To promote its field application, a portable PEC analyzer was further constructed with a miniaturized LED (3 V, 3 W) for excitation and a USB-type electrochemical workstation for detection. Meanwhile, a re-chargeable power bank (5 V, 20,000 mAh, originally used for cell phone charging) was explored to power the LED through a self-designed electrical circuit. In this manner, the PEC analyzer could work continuously for more than 12 h. To facilitate transportation or the portable PEC analyzer, all components were mounted into a suitcase (size: 42 × 34 × 17 cm<sup>3</sup>; wight: ∼3.5 kg). To validate the applicability of the portable PEC analyzer, on-site detection of E. coli O157:H7 in an in-land river was carried out (> 8 h), demonstrating the bright potential of the portable PEC analyzer for field environmental monitoring.", "Keywords": "Photoelectrochemical detection ; Escherichia coli O157:H7 ; LED ; Power bank ; On-site environmental water monitoring", "DOI": "10.1016/j.snb.2021.130520", "PubYear": 2021, "Volume": "346", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, College of Chemistry, Sichuan University, Chengdu 610064, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Analytical & Testing Center, Sichuan University, Chengdu 610064, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, College of Chemistry, Sichuan University, Chengdu 610064, China;Corresponding author at: State Key Laboratory of Hydraulics and Mountain River Engineering, Sichuan University, Chengdu 610064, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Research Center for Flue Gas Desulfurization, Sichuan University, Chengdu 610064, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, College of Chemistry, Sichuan University, Chengdu 610064, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Analytical Chemistry for Life Science, School of Chemistry and Chemical Engineering, Nanjing University, Nanjing 210093, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, College of Chemistry, Sichuan University, Chengdu 610064, China;Analytical & Testing Center, Sichuan University, Chengdu 610064, China;Corresponding author at: Analytical & Testing Center, Sichuan University, Chengdu 610064, China"}], "References": []}, {"ArticleId": 89732176, "Title": "SSA: Subset sum approach to protein β-sheet structure prediction", "Abstract": "<p>The three-dimensional structures of proteins provide their functions and incorrect folding of its β-strands can be the cause of many diseases. There are two major approaches for determining protein structures: computational prediction and experimental methods that employ technologies such as Cryo-electron microscopy. Due to experimental methods's high costs, extended wait times for its lengthy processes, and incompleteness of results, computational prediction is an attractive alternative. As the focus of the present paper, β-sheet structure prediction is a major portion of overall protein structure prediction. Prediction of other substructures, such as α-helices, is simpler with lower computational time complexities. Brute force methods are the most common approach and dynamic programming is also utilized to generate all possible conformations. The current study introduces the Subset Sum Approach (SSA) for the direct search space generation method, which is shown to outperform the dynamic programming approach in terms of both time and space. For the first time, the present work has calculated both the state space cardinality of the dynamic programming approach and the search space cardinality of the general brute force approaches. In regard to a set of pruning rules, SSA has demonstrated higher efficiency with respect to both time and accuracy in comparison to state-of-the-art methods.</p><p>Copyright © 2021 Elsevier Ltd. All rights reserved.</p>", "Keywords": "Brute force approach with pruning;Cardinality of the search space;Protein β-sheet conformation", "DOI": "10.1016/j.compbiolchem.2021.107552", "PubYear": 2021, "Volume": "94", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Ferdowsi University of Mashhad, Mashhad, Iran. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Ferdowsi University of Mashhad, Mashhad, Iran. Electronic address:  ."}], "References": []}, {"ArticleId": 89732219, "Title": "A Framework for Validation of Synthesized MicroElectrode Dot Array Actuations for Digital Microfluidic Biochips", "Abstract": "<p>Digital Microfluidics is an emerging technology for automating laboratory procedures in biochemistry. With more and more complex biochemical protocols getting mapped to biochip devices and microfluidics receiving a wide adoption, it is becoming indispensable to develop automated tools and synthesis platforms that can enable a smooth transformation from complex cumbersome benchtop laboratory procedures to biochip execution. Given an informal/semi-formal assay description and a target microfluidic grid architecture on which the assay has to be implemented, a synthesis tool typically translates the high-level assay operations to low-level actuation sequences that can drive the assay realization on the grid. With more and more complex biochemical assay protocols being taken up for synthesis and biochips supporting a wider variety of operations (e.g., MicroElectrode Dot Arrays (MEDAs)), the task of assay synthesis is getting intricately complex. Errors in the synthesized assay descriptions may have undesirable consequences in assay operations, leading to unacceptable outcomes after execution on the biochips. In this work, we focus on the challenge of examining the correctness of synthesized protocol descriptions, before they are taken up for realization on a microfluidic biochip. In particular, we take up a protocol description synthesized for a MEDA biochip and adopt a formal analysis method to derive correctness proofs or a violation thereof, pointing to the exact operation in the erroneous translation. We present experimental results on a few bioassay protocols and show the utility of our framework for verifiable protocol synthesis.</p>", "Keywords": "", "DOI": "10.1145/3460437", "PubYear": 2021, "Volume": "26", "Issue": "6", "JournalId": 8858, "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems", "ISSN": "1084-4309", "EISSN": "1557-7309", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Calcutta University & Indian Statistical Institute Kolkata, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Statistical Institute Kolkata, India"}], "References": [{"Title": "Secure Assay Execution on MEDA Biochips to Thwart Attacks Using Real-Time Sensing", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems"}]}, {"ArticleId": 89732223, "Title": "非平行性と粘弾性を両有する劣駆動関節機構を用いた非把持マニピュレーション", "Abstract": "This paper presents a nonprehensile manipulation using an underactuated mechanism, in which three degrees of freedom (DoF) of a planar object are controlled on a vibrating plate driven by only one actuator. First, the model of a manipulator with a plate end effector is proposed. The manipulator employs an underactuated mechanism including an active joint and multiple passive viscoelastic joints, in which the joint axes are arranged nonparallel to each other. Based on the model, the orbit of the plate for a sinusoidal displacement input to the active joint is theoretically derived. It is revealed that the orbit becomes ellipse-like and the orbital direction has a potential to be switched according to the increase of the input frequency. Subsequently, the contribution of the switching of the orbital direction to the three-DoF manipulation of the object is explored via trajectory maps of point masses. Eight primitives utilizing the plate orbits in both counter-clockwise and clockwise directions are designed. Finally, the proposed method is demonstrated by experiments.", "Keywords": "Nonprehensile Manipulation;Underactuated Mechanism;Flexible Joint Mechanism;Nonparallel Axis Layout", "DOI": "10.7210/jrsj.39.533", "PubYear": 2021, "Volume": "39", "Issue": "6", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Osaka University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Osaka University"}], "References": []}, {"ArticleId": 89732225, "Title": "複数物体が存在する環境下での共同注意を用いたロボットによる語意学習", "Abstract": "Humans can learn word meanings by associating objects with words even in an environment with a plurality of objects by using joint attention, which is an ability to detect a target object that others pay attention to. In this paper, we propose a method for robots to learn word meanings using joint attention and co-occurrence of objects and words, which is modeled by multimodal latent Dirichlet allocation (MLDA). A target object is detected by using MLDA and joint attention, and MLDA is updated by the detected object. This updated MLDA can improve the accuracy of the target object detection.", "Keywords": "Joint Attention;Word Meaning Acquisition;Unsupervised Learning;Multimodal Latent Dirichlet Allocation", "DOI": "10.7210/jrsj.39.549", "PubYear": 2021, "Volume": "39", "Issue": "6", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications"}], "References": []}, {"ArticleId": 89732234, "Title": "Optimization and Prediction Techniques for Self-Healing and Self-Learning Applications in a Trustworthy Cloud Continuum", "Abstract": "<p>The current IT market is more and more dominated by the “cloud continuum”. In the “traditional” cloud, computing resources are typically homogeneous in order to facilitate economies of scale. In contrast, in edge computing, computational resources are widely diverse, commonly with scarce capacities and must be managed very efficiently due to battery constraints or other limitations. A combination of resources and services at the edge (edge computing), in the core (cloud computing), and along the data path (fog computing) is needed through a trusted cloud continuum. This requires novel solutions for the creation, optimization, management, and automatic operation of such infrastructure through new approaches such as infrastructure as code (IaC). In this paper, we analyze how artificial intelligence (AI)-based techniques and tools can enhance the operation of complex applications to support the broad and multi-stage heterogeneity of the infrastructural layer in the “computing continuum” through the enhancement of IaC optimization, IaC self-learning, and IaC self-healing. To this extent, the presented work proposes a set of tools, methods, and techniques for applications’ operators to seamlessly select, combine, configure, and adapt computation resources all along the data path and support the complete service lifecycle covering: (1) optimized distributed application deployment over heterogeneous computing resources; (2) monitoring of execution platforms in real time including continuous control and trust of the infrastructural services; (3) application deployment and adaptation while optimizing the execution; and (4) application self-recovery to avoid compromising situations that may lead to an unexpected failure.</p>", "Keywords": "optimization; self-learning; concept drift; anomaly detection; cloud continuum; self-healing optimization ; self-learning ; concept drift ; anomaly detection ; cloud continuum ; self-healing", "DOI": "10.3390/info12080308", "PubYear": 2021, "Volume": "12", "Issue": "8", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "TECNALIA, Basque Research and Technology Alliance (BRTA), Parque Científico y Tecnológico de Bizkaia, 48160 Derio, Spain"}, {"AuthorId": 2, "Name": "Leire <PERSON>-Echevarria", "Affiliation": "TECNALIA, Basque Research and Technology Alliance (BRTA), Parque Científico y Tecnológico de Bizkaia, 48160 Derio, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TECNALIA, Basque Research and Technology Alliance (BRTA), Parque Científico y Tecnológico de Bizkaia, 48160 Derio, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TECNALIA, Basque Research and Technology Alliance (BRTA), Parque Científico y Tecnológico de Bizkaia, 48160 Derio, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TECNALIA, Basque Research and Technology Alliance (BRTA), Parque Científico y Tecnológico de Bizkaia, 48160 Derio, Spain"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "TECNALIA, Basque Research and Technology Alliance (BRTA), Parque Científico y Tecnológico de Bizkaia, 48160 Derio, Spain"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TECNALIA, Basque Research and Technology Alliance (BRTA), Parque Científico y Tecnológico de Bizkaia, 48160 Derio, Spain"}], "References": [{"Title": "A survey of swarm and evolutionary computing approaches for deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "1767", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "No Free Lunch Theorem for concept drift detection in streaming data classification: A review", "Authors": "<PERSON><PERSON> Hu; <PERSON><PERSON><PERSON>; Tegjyot S. Sethi", "PubYear": 2020, "Volume": "10", "Issue": "2", "Page": "", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "Analyzing rare event, anomaly, novelty and outlier detection terms under the supervised classification framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "5", "Page": "3575", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Data-driven decision support under concept drift in streamed big data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1", "Page": "157", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Self-adapting cloud services orchestration for fulfilling intensive sensory data-driven IoT workflows", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "583", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Multi-agent architecture for fault recovery in self-healing systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "2849", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Data stream analysis: Foundations, major tasks and tools", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "3", "Page": "e1405", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}]}, {"ArticleId": 89732235, "Title": "Reasoning Method between Polynomial Error Assertions", "Abstract": "<p>Error coefficients are ubiquitous in systems. In particular, errors in reasoning verification must be considered regarding safety-critical systems. We present a reasoning method that can be applied to systems described by the polynomial error assertion (PEA). The implication relationship between PEAs can be converted to an inclusion relationship between zero sets of PEAs; the PEAs are then transformed into first-order polynomial logic. Combined with the quantifier elimination method, based on cylindrical algebraic decomposition, the judgment of the inclusion relationship between zero sets of PEAs is transformed into judgment error parameters and specific error coefficient constraints, which can be obtained by the quantifier elimination method. The proposed reasoning method is validated by proving the related theorems. An example of intercepting target objects is provided, and the correctness of our method is tested through large-scale random cases. Compared with reasoning methods without error semantics, our reasoning method has the advantage of being able to deal with error parameters.</p>", "Keywords": "formal method; reasoning method; system verification; polynomial; error control formal method ; reasoning method ; system verification ; polynomial ; error control", "DOI": "10.3390/info12080309", "PubYear": 2021, "Volume": "12", "Issue": "8", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Beijing Jiaotong University, Beijing 100044, China↑School of Innovation, Design and Engineering, Mälardalen University, 72123 <PERSON>ä<PERSON><PERSON>s, Sweden↑Authors to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Innovation, Design and Engineering, Mälardalen University, 72123 Västerås, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Physics, Guangxi University for Nationalities, Nanning 530006, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Technology, Beijing Jiaotong University, Beijing 100044, China↑School of Mathematics and Physics, Guangxi University for Nationalities, Nanning 530006, China↑Authors to whom correspondence should be addressed"}], "References": [{"Title": "A New Approach to Nonlinear Invariants for Hybrid Systems Based on the Citing Instances Method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "246", "JournalTitle": "Information"}]}, {"ArticleId": 89732236, "Title": "Image Watermarking Approach Using a Hybrid Domain Based on Performance Parameter Analysis", "Abstract": "<p>In today’s scenario, image watermarking has been an integral part in various multimedia applications. Watermarking is the approach for adding additional information to the existing image to protect the data from modification and to provide data integrity. Frequency transform domain techniques are complex and costly and degrade the quality of the image due to less embedding of bits. The proposed work utilize the original DCT method with some modifications and applies this method on frequency bands of DWT. Furthermore, the output is used in combination with a pixel modification method for embedding and extraction. The proposed outcome is the improvement of performance achieved in terms of time, imperceptibility, and robustness.</p>", "Keywords": "DCT; DWT; PSNR; MSE; NC; Arnold Transform DCT ; DWT ; PSNR ; MSE ; NC ; Arnold Transform", "DOI": "10.3390/info12080310", "PubYear": 2021, "Volume": "12", "Issue": "8", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Petroleum and Energy Studies, Dehradun 248007, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of Petroleum and Energy Studies, Dehradun 248007, India↑Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Tennessee Technological University, Cookeville, TN 38505, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, DIT University, Dehradun 248007, India"}, {"AuthorId": 5, "Name": "Jaehong Park", "Affiliation": "Department of Management, Marketing and Information Systems, University of Alabama in Huntsville, Huntsville, AL 35899, USA"}], "References": [{"Title": "Parameter-free fuzzy histogram equalisation with illumination preserving characteristics dedicated for contrast enhancement of magnetic resonance images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106364", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 89732253, "Title": "Self-powered adjustable UV and NIR photodetectors based on one-step synthesized TeO2 doped ZnO composite nanorods/Si heterojunction", "Abstract": "Tellurium dioxide (TeO<sub>2</sub>) doped zinc oxide (ZnO) composite nanorods (TZO CNRs) were first synthesized by chemical vapor deposition method on a p-type silicon substrate covered by a sputtered gold (Au) film. The morphologies, structures, compositions and optical properties of the TZO CNRs can be modulated with the TeO<sub>2</sub> content. Three typical self-powered TZO CNRs/Si heterojunction photodetectors named device 1, 2 and 3 according to the different TeO<sub>2</sub> contents and TZO CNR morphologies are designed. Device 1 based on 1.81 At% Te content TZO CNRs can detect UV lights with wavelength from 290 to 380 nm and showed a responsivity of 4.3 mA/W, a detectivity of 1.1 × 10<sup>12</sup> cmHz<sup>1/2</sup>W<sup>−1</sup> upon exposure to 370 nm light at a zero-bias voltage. Device 2 based on 5.86 At% Te content TZO CNRs is found to be selectively sensitive to UV and NIR lights. Device 3 based on 9.23 At% Te content TZO CNRs is found to be also selectively sensitive to NIR lights. Its most sensitive wavelength is 1040 nm with responsivity and detectivity of 24.6 mA/W and 4.12 × 10<sup>12</sup> cmHz<sup>1/2</sup>W<sup>−1</sup>, respectively. In addition, the mechanism that led to their typical properties of these devices are also elucidated.", "Keywords": "Adjustable photodetectors ; One-step synthesized ; TeO<sub>2</sub> doped ZnO ; Nanorods ; Heterojunction", "DOI": "10.1016/j.sna.2021.113009", "PubYear": 2021, "Volume": "331", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Physics and Electronics, North China University of Water Resources and Electric Power, Zhengzhou, Henan 450046, China"}, {"AuthorId": 2, "Name": "Zengcai Song", "Affiliation": "School of Physics and Electronics, North China University of Water Resources and Electric Power, Zhengzhou, Henan 450046, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON>gzhen Hu", "Affiliation": "School of Physics and Electronics, North China University of Water Resources and Electric Power, Zhengzhou, Henan 450046, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Chen", "Affiliation": "School of Physics and Electronics, North China University of Water Resources and Electric Power, Zhengzhou, Henan 450046, China"}, {"AuthorId": 5, "Name": "<PERSON>g <PERSON>", "Affiliation": "School of Physics and Electronics, North China University of Water Resources and Electric Power, Zhengzhou, Henan 450046, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics and Electronics, North China University of Water Resources and Electric Power, Zhengzhou, Henan 450046, China;Corresponding authors"}], "References": [{"Title": "Ultraviolet sensing characteristics of Ag-doped ZnO micro-nano fiber", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "307", "Issue": "", "Page": "111989", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Interaction activated interfacial charge transfer in 2D g-C3N4/GaN nanorods heterostructure for self-powered UV photodetector and room temperature NO2 gas sensor at ppb level", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129175", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 89732260, "Title": "Sensing pH of individual microdroplet by combining SERS and indicator paper", "Abstract": "Microdroplets present unique physiochemical properties, which can influence chemical reactions and atmospheric aerosol processes. The microdroplets’ pH is one of the most important factors that dictate chemical processes, such as heterogeneous and condensed reactions. However, currently, there is no well-established method for directly measuring the pH in microdroplets. Herein, we develop a straightforward method, for the first time, to directly measure the pH of size-resolved, micron-sized single droplets with Surface Enhanced Raman Scattering (SERS)-activated pH-indicator paper; realized by embedded Au nanoparticles (20, 40 and 60 nm). We establish the key parameters for optimizing the method, and demonstrate its accuracy by benchmarking various acids and solutions with different ionic strength. With the established method, we report the pH dependence on the size of microdroplets. Microdroplets of sizes down to 230 nm can be analyzed by our method. We also showcase our method’s applicability to ambient samples, establishing its promising potential beyond the laboratory environment.", "Keywords": "pH measurement ; Single microdroplet ; Raman spectroscopy ; SERS ; Plasmonics", "DOI": "10.1016/j.snb.2021.130521", "PubYear": 2021, "Volume": "346", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Kedong Gong", "Affiliation": "Shanghai Key Laboratory of Atmospheric Particle Pollution and Prevention, Department of Environmental Science and Engineering, Fudan University, Shanghai, 200433, People’s Republic of China;Shanghai Institute of Pollution Control and Ecological Security, Shanghai, 200092, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Centre for Photonics and Photonic Materials, University of Bath, Bath, BA2 7AY, United Kingdom;Centre for Nanoscience and Nanotechnology, University of Bath, Bath, BA2 7AY, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Atmospheric Particle Pollution and Prevention, Department of Environmental Science and Engineering, Fudan University, Shanghai, 200433, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Atmospheric Particle Pollution and Prevention, Department of Environmental Science and Engineering, Fudan University, Shanghai, 200433, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Atmospheric Particle Pollution and Prevention, Department of Environmental Science and Engineering, Fudan University, Shanghai, 200433, People’s Republic of China"}, {"AuthorId": 6, "Name": "Yiqing Feng", "Affiliation": "Shanghai Key Laboratory of Atmospheric Particle Pollution and Prevention, Department of Environmental Science and Engineering, Fudan University, Shanghai, 200433, People’s Republic of China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Photonics and Photonic Materials, University of Bath, Bath, BA2 7AY, United Kingdom;Centre for Nanoscience and Nanotechnology, University of Bath, Bath, BA2 7AY, United Kingdom;Centre for Therapeutic Innovation, University of Bath, Bath, BA2 7AY, United Kingdom"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Atmospheric Particle Pollution and Prevention, Department of Environmental Science and Engineering, Fudan University, Shanghai, 200433, People’s Republic of China;Shanghai Institute of Pollution Control and Ecological Security, Shanghai, 200092, People’s Republic of China;Corresponding author at: Department of Environmental Science and Engineering, Fudan University, Shanghai, 200433, People’s Republic of China"}], "References": []}, {"ArticleId": 89732405, "Title": "Learning temporal logic formulas from suboptimal demonstrations: theory and experiments", "Abstract": "<p>We present a method for learning multi-stage tasks from demonstrations by learning the logical structure and atomic propositions of a consistent linear temporal logic (LTL) formula. The learner is given successful but potentially suboptimal demonstrations, where the demonstrator is optimizing a cost function while satisfying the LTL formula, and the cost function is uncertain to the learner. Our algorithm uses the Karush-Kuhn-Tucker (KKT) optimality conditions of the demonstrations together with a counterexample-guided falsification strategy to learn the atomic proposition parameters and logical structure of the LTL formula, respectively. We provide theoretical guarantees on the conservativeness of the recovered atomic proposition sets, as well as completeness in the search for finding an LTL formula consistent with the demonstrations. We evaluate our method on high-dimensional nonlinear systems by learning LTL formulas explaining multi-stage tasks on a simulated 7-DOF arm and a quadrotor, and show that it outperforms competing methods for learning LTL formulas from positive examples. Finally, we demonstrate that our approach can learn a real-world multi-stage tabletop manipulation task on a physical 7-DOF Kuka iiwa arm.</p>", "Keywords": "Learning from demonstration; Linear temporal logic; Motion planning", "DOI": "10.1007/s10514-021-10004-x", "PubYear": 2022, "Volume": "46", "Issue": "1", "JournalId": 20990, "JournalTitle": "Autonomous Robots", "ISSN": "0929-5593", "EISSN": "1573-7527", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, University of Michigan, Ann Arbor, Ann Arbor, USA"}, {"AuthorId": 2, "Name": "<PERSON>ec<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, University of Michigan, Ann Arbor, Ann Arbor, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, University of Michigan, Ann Arbor, Ann Arbor, USA"}], "References": []}, {"ArticleId": 89732435, "Title": "Analysing global professional gender gaps using LinkedIn advertising data", "Abstract": "<p>Although women’s participation in tertiary education and the labour force has expanded over the past decades, women continue to be underrepresented in technical and managerial occupations. We analyse if gender inequalities also manifest themselves in online populations of professionals by leveraging audience estimates from LinkedIn’s advertisement platform to explore gender gaps among LinkedIn users across countries, ages, industries and seniorities. We further validate LinkedIn gender gaps against ground truth professional gender gap indicators derived from the International Labour Organization’s (ILO) Statistical Database, and examine the feasibility and biases of predicting global professional gender gap indicators using gender gaps computed from LinkedIn’s online population. We find that women are significantly underrepresented relative to men on LinkedIn in countries in Africa, the Middle East and South Asia, among older individuals, in Science, Technology, Engineering and Mathematics (STEM) fields and higher-level managerial positions. Furthermore, a simple, aggregate indicator of the female-to-male ratio of LinkedIn users, which we term the LinkedIn Gender Gap Index (GGI), shows strong positive correlations with ILO ground truth professional gender gaps. A parsimonious regression model using the LinkedIn GGI to predict ILO professional gender gaps enables us to expand country coverage of different ILO indicators, albeit with better performance for general professional gender gaps than managerial gender gaps. Nevertheless, predictions generated using the LinkedIn population show some distinctive biases. Notably, we find that in countries where there is greater gender inequality in internet access, LinkedIn data predict greater gender equality than the ground truth, indicating an overrepresentation of high status women online in these settings. Our work contributes to a growing literature seeking to harness the ‘data revolution’ for global sustainable development by evaluating the potential of a novel data source for filling gender data gaps and monitoring key indicators linked to women’s economic empowerment.</p>", "Keywords": "LinkedIn;Digital Demography;Gender;Sustainable Development Goals;Data for Development", "DOI": "10.1140/epjds/s13688-021-00294-7", "PubYear": 2021, "Volume": "10", "Issue": "1", "JournalId": 5454, "JournalTitle": "EPJ Data Science", "ISSN": "", "EISSN": "2193-1127", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Sociology, University of Oxford, Oxford, United Kingdom;Nuffield College, University of Oxford, Oxford, United Kingdom;Leverhulme Centre for Demographic Science, University of Oxford, Oxford, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Sociology, University of Oxford, Oxford, United Kingdom;Nuffield College, University of Oxford, Oxford, United Kingdom"}], "References": [{"Title": "Mapping socioeconomic indicators using social media advertising data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "EPJ Data Science"}, {"Title": "The power of LinkedIn: how LinkedIn enables professionals to leave their organizations for professional advancement", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "1", "Page": "262", "JournalTitle": "Internet Research"}]}, {"ArticleId": 89732458, "Title": "Topics, Sentiments, and Emotions Triggered by COVID-19-Related Tweets from IRAN and Turkey Official News Agencies", "Abstract": "<p>There is no doubt that the COVID-19 epidemic posed the most significant challenge to all governments globally since January 2020. People have to readapt after the epidemic to daily life with the absence of an effective vaccine for a long time. The epidemic has led to society division and uncertainty. With such issues, governments have to take efficient procedures to fight the epidemic. In this paper, we analyze and discuss two official news agencies' tweets of Iran and Turkey by using sentiment- and semantic analysis-based unsupervised learning approaches. The main topics, sentiments, and emotions that accompanied the agencies' tweets are identified and compared. The results are analyzed from the perspective of psychology, sociology, and communication.</p><p>© The Author(s), under exclusive licence to Springer Nature Singapore Pte Ltd 2021.</p>", "Keywords": "COVID-19;Emotion classification;Sentiment analysis;Topic modeling", "DOI": "10.1007/s42979-021-00789-0", "PubYear": 2021, "Volume": "2", "Issue": "5", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information and Communications, Huazhong University of Science and Technology (HUST), Wuhan, China."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electronic Information and Communications, Huazhong University of Science and Technology (HUST), Wuhan, China."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Journalism and Information Communication, Huazhong University of Science and Technology (HUST), Wuhan, China."}, {"AuthorId": 4, "Name": "Minghua Xu", "Affiliation": "School of Journalism and Information Communication, Huazhong University of Science and Technology (HUST), Wuhan, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Infocomm Research, Agency for Science, Technology and Research (ASTAR), Singapore, Singapore."}], "References": [{"Title": "A comparative study of machine translation for multilingual sentence-level sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "1078", "JournalTitle": "Information Sciences"}, {"Title": "Sentiment analysis of Twitter data during critical events through Bayesian networks classifiers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "92", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 89732484, "Title": "High performance inventive system for gait automation and detection of physically disabled persons", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIE.2021.10040007", "PubYear": 2021, "Volume": "8", "Issue": "4", "JournalId": 13634, "JournalTitle": "International Journal of Intelligent Enterprise", "ISSN": "1745-3232", "EISSN": "1745-3240", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89732485, "Title": "A systematic analysis of defects, incidents, tickets and service effort estimation", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIE.2021.10040009", "PubYear": 2021, "Volume": "8", "Issue": "4", "JournalId": 13634, "JournalTitle": "International Journal of Intelligent Enterprise", "ISSN": "1745-3232", "EISSN": "1745-3240", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89732487, "Title": "A new training approach based on ECOC-SVM for SAR image retrieval", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIE.2021.10040022", "PubYear": 2021, "Volume": "8", "Issue": "4", "JournalId": 13634, "JournalTitle": "International Journal of Intelligent Enterprise", "ISSN": "1745-3232", "EISSN": "1745-3240", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89732488, "Title": "AI and machine learning for the analysis of data flow characteristics in industrial network communication security", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2021.10040024", "PubYear": 2021, "Volume": "37", "Issue": "3", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89732545, "Title": "Editorial", "Abstract": "", "Keywords": "", "DOI": "10.1017/S0958344021000227", "PubYear": 2021, "Volume": "33", "Issue": "3", "JournalId": 12184, "JournalTitle": "ReCALL", "ISSN": "0958-3440", "EISSN": "1474-0109", "Authors": [{"AuthorId": 1, "Name": "Cornelia Tschichold", "Affiliation": ""}], "References": []}, {"ArticleId": 89732589, "Title": "Smart grid wireless communications: a cyber-physical system perspective", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2021.10040027", "PubYear": 2021, "Volume": "37", "Issue": "3", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> Fu", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89732590, "Title": "A One-class Classification Approach Based on SVDD for Imbalanced and Overlapping Classes", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJDATS.2021.10040029", "PubYear": 2021, "Volume": "13", "Issue": "4", "JournalId": 27737, "JournalTitle": "International Journal of Data Analysis Techniques and Strategies", "ISSN": "1755-8050", "EISSN": "1755-8069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89732602, "Title": "Dominance-based rough approximation and knowledge reduction: a class-based approach", "Abstract": "<p>The dominance-based rough set approach (DRSA) extends <PERSON><PERSON><PERSON>’s rough set theory and has become prevailing for multicriteria decision-making. The literature has reported multiple versions of DRSA models, in which rough approximations are preserving the union of decision classes. In this paper, we propose a new type of rough approach based on the classes rather than the conventional class unions. We extend the class-based rough approximation to a series of DRSA models, including classical DRSA, variable consistency DRSA model, variable precision DRSA model, and believable rough set approach model. Besides, we explore the methods of criteria reduction under the framework of class-based rough approximation. We clarify the relations among the proposed and previous reducts in DRSA.</p>", "Keywords": "Multicriteria decision-making; Dominance principle; Rough set; Sorting; Criteria reduction", "DOI": "10.1007/s00500-021-06026-3", "PubYear": 2021, "Volume": "25", "Issue": "17", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Business and Management, BNU-HKBU United International College, Zhuhai, China;Centre for Evaluation Studies, Beijing Normal University, Zhuhai, China"}], "References": [{"Title": "Auto loan fraud detection using dominance-based rough set approach versus machine learning methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>ho; <PERSON>", "PubYear": 2021, "Volume": "163", "Issue": "", "Page": "113740", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 89732605, "Title": "Towards scalable and reusable predictive models for cyber twins in manufacturing systems", "Abstract": "Smart factories are intelligent, fully-connected and flexible systems that can continuously monitor and analyse data streams from interconnected systems to make decisions and dynamically adapt to new circumstances. The implementation of smart factories represents a leap forward compared to traditional automation. It is underpinned by the deployment of cyberphysical systems that, through the application of Artificial Intelligence, integrate predictive capabilities and foster rapid decision-making. Deep Learning (DL) is a key enabler for the development of smart factories. However, the implementation of DL in smart factories is hindered by its reliance on large amounts of data and extreme computational demand. To address this challenge, Transfer Learning (TL) has been proposed to promote the efficient training of models by enabling the reuse of previously trained models. In this paper, by means of a specific example in aluminium can manufacturing, an empirical study is presented, which demonstrates the potential of TL to achieve fast deployment of scalable and reusable predictive models for Cyber Manufacturing Systems. Through extensive experiments, the value of TL is demonstrated to achieve better generalisation and model performance, especially with limited datasets. This research provides a pragmatic approach towards predictive model building for cyber twins, paving the way towards the realisation of smart factories.", "Keywords": "Cyber physical systems; Transfer learning; ConvLSTM; Smart manufacturing; Deep learning", "DOI": "10.1007/s10845-021-01804-0", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Future Manufacturing Research Institute (FMRI), Faculty of Science and Engineering, Bay Campus, Swansea University, Swansea, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management, University of Sussex Business School, Falmer, Brighton, UK"}], "References": [{"Title": "Multi-source transfer learning of time series in cyclical manufacturing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "3", "Page": "777", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Fault diagnostics between different type of components: A transfer learning approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105950", "JournalTitle": "Applied Soft Computing"}, {"Title": "DAMER: a novel diagnosis aggregation method with evidential reasoning rule for bearing fault diagnosis", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A novel transfer learning fault diagnosis method based on Manifold Embedded Distribution Alignment with a little labeled data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "1", "Page": "151", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Using Convolutional Neural Networks to Map Houses Suitable for Electric Vehicle Home Charging", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "1", "Page": "135", "JournalTitle": "AI"}]}, {"ArticleId": 89732653, "Title": "MagnetOnto: modelling and evaluation of standardised domain ontologies for magnetic materials as a prospective domain", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIE.2021.********", "PubYear": 2021, "Volume": "8", "Issue": "4", "JournalId": 13634, "JournalTitle": "International Journal of Intelligent Enterprise", "ISSN": "1745-3232", "EISSN": "1745-3240", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Performance comparison of multipath routing protocols for mobile ad hoc network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCC.2021.********", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 19270, "JournalTitle": "International Journal of Systems, Control and Communications", "ISSN": "1755-9340", "EISSN": "1755-9359", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Demand-side management in smart electricity grids: a review", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIE.2021.********", "PubYear": 2021, "Volume": "8", "Issue": "4", "JournalId": 13634, "JournalTitle": "International Journal of Intelligent Enterprise", "ISSN": "1745-3232", "EISSN": "1745-3240", "Authors": [{"AuthorId": 1, "Name": "B.<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89732709, "Title": "Design, implementation, and evaluation of a shared LoRa network application architecture", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2021.10040025", "PubYear": 2021, "Volume": "37", "Issue": "3", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89732710, "Title": "Smart resource scheduling by correlating discriminative quality factors to optimise resource utilisation in IAAS of cloud networks", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJEB.2021.10040033", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 8392, "JournalTitle": "International Journal of Electronic Business", "ISSN": "1470-6067", "EISSN": "1741-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89732827, "Title": "RSSI-based node selection using neural network parameterised by particle swarm optimisation", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2021.10040028", "PubYear": 2021, "Volume": "37", "Issue": "3", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89732899, "Title": "Deterministic multikernel extreme learning machine with fuzzy feature extraction for pattern classification", "Abstract": "<p>In this paper a novel multikernel deterministic extreme learning machine (ELM) and its variants are developed for classification of non-linear problems. Over a decade ELM is proved to be efficacious learning algorithms, but due to the non-deterministic and single kernel dependent feature mapping proprietary, it cannot be efficiently applied to real time classification problems that require invariant output solution. We address this problem by analytically calculation of input and hidden layer parameters for achieving the deterministic solution and exploiting the data fusion proficiency of multiple kernel learning. This investigation originates a novel deterministic ELM with single layer architecture in which kernel function is aggregation of linear combination of disparate base kernels. The weight of kernels depends upon perspicacity of problem and is empirically calculated. To further enhance the performance we utilize the capabilities of fuzzy set to find the pixel-wise coalition of face images with different classes. This handles the uncertainty involved in face recognition under varying environment condition. The pixel-wise membership value extracts the unseen information from images up to significant extent. The validity of the proposed approach is tested extensively on diverse set of face databases: databases with and without illumination variations and discrete types of kernels. The proposed algorithms achieve 100% recognition rate for Yale database, when seven and eight images per identity are considered for training. Also, the superior recognition rate is achieved for AT & T, Georgia Tech and AR databases, when compared with contemporary methods that prove the efficacy of proposed approaches in uncontrolled conditions significantly.</p>", "Keywords": "Pattern classification; ELM; KELM; MKL; Deterministic learning; Fuzzy feature extraction", "DOI": "10.1007/s11042-021-11097-3", "PubYear": 2021, "Volume": "80", "Issue": "21-23", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University School of Information, Communication & Technology, <PERSON> Indraprastha University, New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University School of Information, Communication & Technology, <PERSON> Indraprastha University, New Delhi, India"}], "References": [{"Title": "A novel PCA–whale optimization-based deep neural network model for classification of tomato plant diseases using GPU", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "4", "Page": "1383", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "SCANet", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}]}, {"ArticleId": 89732904, "Title": "Real-world String Comparison", "Abstract": "<p>In many languages a string comparison is a pitfall for beginners. With any Unicode string as input, a comparison often causes problems even for advanced users. The semantic equivalence of different characters in Unicode requires a normalization of the strings before comparing them. This article shows how to handle Unicode sequences correctly. The comparison of two strings for equality often raises questions concerning the difference between comparison by value, comparison of object references, strict equality, and loose equality. The most important aspect is semantic equivalence.</p>", "Keywords": "", "DOI": "10.1145/3475965.3478522", "PubYear": 2021, "Volume": "19", "Issue": "3", "JournalId": 22497, "JournalTitle": "Queue", "ISSN": "1542-7730", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Fraunhofer Austria"}], "References": []}, {"ArticleId": 89733015, "Title": "CGP Box: An effective direction representation strategy for oriented object detection in remote sensing images", "Abstract": "In recent years, the emergence of convolutional neural networks (CNN) has greatly promoted the development of the object detection field, and many CNN-based detectors have achieved excellent performance on object detection in remote sensing images. To accurately locate the target, oriented bounding box (OBB) is usually used in remote sensing objects, such as the angle-based OBB, to represent the target. Nevertheless, the critical loss instability caused by the periodicity of the angle is always difficult to solve. In this paper, we propose a novel strategy called the Center-Guide points (CGP) box method that uses the guide points to locate the target, which breaks the limit of the angle-based thinking pattern to solve the critical loss instability problem. To be specific, we define a new guide-points selection rule and prediction structure, which replaces the traditional method of using angle values to indicate the direction. Furthermore, we propose the matching method of centre points and guide points, which is a box decoding method that matches the object and the corresponding guide points. Finally, an attention learning module called the Gaussian Center-Line (GC-L) Attention module based on the Gaussian centre-line is proposed to improve the accuracy of guide points. These strategies are applied to the key point detection framework and tested on three classical-oriented object remote sensing datasets. The results show that our method is effective and competitive.", "Keywords": "", "DOI": "10.1080/01431161.2021.1941389", "PubYear": 2021, "Volume": "42", "Issue": "17", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Space Control and Inertial Technology Research Center, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Space Control and Inertial Technology Research Center, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Space Control and Inertial Technology Research Center, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON> Shen", "Affiliation": "Institut de Robòtica i Informàtica Industrial, CSIC-UPC, Barcelona, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Du", "Affiliation": "Space Control and Inertial Technology Research Center, Harbin Institute of Technology, Harbin, China"}], "References": []}, {"ArticleId": 89733277, "Title": "An efficient hydrogen gas sensor based on hierarchical Ag/ZnO hollow microstructures", "Abstract": "The design of hierarchical zinc oxide (ZnO) microstructures modified with silver (Ag) nanoparticles have emerged as an effective approach for improving the hydrogen gas sensing performance. Here, we report a simple, low-cost chemical co-precipitation method to obtain the Ag/ZnO hollow microstructures and morphologically characterized them by field emission scanning electron microscopy (FESEM). FESEM images revealed the clear hollow hexagonal tube-like morphology. The existence of Ag in ZnO was confirmed by X-ray diffraction (XRD) and energy-dispersive X-ray spectroscopy (EDS). UV visible absorption and photoluminescence (PL) spectra were also recorded to observe the effect of Ag nanofiller on the optical properties of ZnO. Furthermore, the gas sensing properties of the as-prepared bare ZnO and Ag/ZnO sensor were investigated. The thoroughly sensing experiments demonstrated that after modification with Ag nanoparticles the ZnO sensor shows superior sensitivity 479 % towards 300 ppm hydrogen gas concentration, whereas 101 % response was noted for the pure ZnO sensor at 250 °C working temperature. Meanwhile, Ag/ZnO hybrids exhibited excellent selectivity, fast response, and recovery time and also obtained a good and stable response signal at 5 ppm H<sub>2</sub> exposure, which indicated that the lower concentration measurement is also attainable. This enhancement in the sensing performance of Ag/ZnO structures towards hydrogen is due to the chemical and electronic sensitization effect of Ag nanoparticles. As a result, such microstructure is attributed to more oxygen species and active sites, and it enhances the sensitivity of a sensor. Moreover, this type of hybrid opens a new path and supports the next generation of innovative materials to fabricate highly selective and sensitive H<sub>2</sub> gas sensing devices.", "Keywords": "Hydrogen sensors ; Silver ; Zinc oxide ; Hollow microstructures", "DOI": "10.1016/j.snb.2021.130510", "PubYear": 2021, "Volume": "346", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Malaviya National Institute of Technology, Jaipur, Rajasthan, 302017, India;Corresponding authors at: Department of Physics, Malaviya National Institute of Technology, Jaipur, Rajasthan, 302017, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Malaviya National Institute of Technology, Jaipur, Rajasthan, 302017, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Malaviya National Institute of Technology, Jaipur, Rajasthan, 302017, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Electrical Engineering (SCEE), Indian Institute of Technology (IIT)-Mandi, 175005, Himachal Pradesh, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Malaviya National Institute of Technology, Jaipur, Rajasthan, 302017, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing and Electrical Engineering (SCEE), Indian Institute of Technology (IIT)-Mandi, 175005, Himachal Pradesh, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Malaviya National Institute of Technology, Jaipur, Rajasthan, 302017, India;Materials Research Centre, Malaviya National Institute of Technology, Jaipur, Rajasthan, 302017, India;Corresponding authors at: Department of Physics, Malaviya National Institute of Technology, Jaipur, Rajasthan, 302017, India"}], "References": []}, {"ArticleId": 89733293, "Title": "A Linguistic Information Granulation Model and Its Penalty Function-Based Co-Evolutionary PSO Solution Approach for Supporting GDM with Distributed Linguistic Preference Relations", "Abstract": "This study focuses on linguistic information operational realization through information granulation in group decision-making (GDM) scenarios where the preference information offered by decision-makers over alternatives is described using distributed linguistic preference relations (DLPRs). First, an information granulation model is proposed to arrive at the operational realization of linguistic information in the GDM with DLPRs. The information granulation is formulated as a certain optimization problem where a combination of consistency degree of individual DLPRs and consensus degree among individuals is regarded as the underlying performance index. Then, considering that the proposed model is a constrained optimization problem (COP) with an adjustable parameter, which is difficult to be effectively solved using general optimization methods, we develop a novel approach towards achieving the optimal solution, referred to as penalty function-based co-evolutionary particle swarm optimization (PFCPSO). Within the PFCPSO setting, the designed penalty function is used to transform the COPs into unconstrained ones. Besides, the penalty factors and the adjustable parameter, as well as the decision variables of the optimization problems, are simultaneously optimized through the co-evolutionary mechanism of two populations in co-evolutionary particle swarm optimization (CPSO). Finally, a comprehensive evaluation problem about car brands is studied using the proposed model and the newly developed PFCPSO approach, which demonstrates their applicability. Two comparative studies are also conducted to show the effectiveness of the proposals. Overall, this study exhibits two facets of originality: the presentation of the linguistic information granulation model, and the development of the PFCPSO approach for solving the proposed model.", "Keywords": "Group decision making ; Distributed linguistic preference relation ; Constrained optimization problem ; Linguistic information granulation ; Particle swarm optimization", "DOI": "10.1016/j.inffus.2021.07.017", "PubYear": 2022, "Volume": "77", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Box 270, <PERSON>fei 230009, Anhui, P.R. China;;Key Laboratory of Process Optimization and Intelligent Decision-making, Ministry of Education, Hefei, Box 270, Hefei 230009, Anhui, P.R. China;;Ministry of Education Engineering Research Center for Intelligent Decision-Making & Information System Technologies, Hefei, 230009, Anhui, P.R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Box 270, <PERSON>fei 230009, Anhui, P.R. China;;Key Laboratory of Process Optimization and Intelligent Decision-making, Ministry of Education, Hefei, Box 270, Hefei 230009, Anhui, P.R. China;;Ministry of Education Engineering Research Center for Intelligent Decision-Making & Information System Technologies, Hefei, 230009, Anhui, P.R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Box 270, <PERSON>fei 230009, Anhui, P.R. China;;Key Laboratory of Process Optimization and Intelligent Decision-making, Ministry of Education, Hefei, Box 270, Hefei 230009, Anhui, P.R. China;;Ministry of Education Engineering Research Center for Intelligent Decision-Making & Information System Technologies, Hefei, 230009, Anhui, P.R. China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Xidian University, Xi'an, 710071, P.R. China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical & Computer Engineering, University of Alberta, Edmonton, T6R 2V4, AB, Canada"}], "References": [{"Title": "A novel multi-criteria group decision-making method for heterogeneous and dynamic contexts using multi-granular fuzzy linguistic modelling and consensus measures", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "240", "JournalTitle": "Information Fusion"}, {"Title": "Multiple-attribute group decision making for interval-valued intuitionistic fuzzy sets based on expert reliability and the evidential reasoning rule", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "5213", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A constrained multi-swarm particle swarm optimization without velocity for constrained optimization problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "112882", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Surrogate-assisted classification-collaboration differential evolution for expensive constrained optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "50", "JournalTitle": "Information Sciences"}, {"Title": "Consistency and consensus-driven models to personalize individual semantics of linguistic terms for supporting group decision making with distribution linguistic preference relations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "189", "Issue": "", "Page": "105078", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Distribution linguistic preference relations with incomplete symbolic proportions for group decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106005", "JournalTitle": "Applied Soft Computing"}, {"Title": "Consensus reaching for social network group decision making by considering leadership and bounded confidence", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "204", "Issue": "", "Page": "106240", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Identifying and prioritizing factors affecting in-cabin passenger comfort on high-speed rail in China: A fuzzy-based linguistic approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106558", "JournalTitle": "Applied Soft Computing"}, {"Title": "Distributed linguistic representations in decision making: Taxonomy, key elements and applications, and challenges in data science and explainable artificial intelligence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "65", "Issue": "", "Page": "165", "JournalTitle": "Information Fusion"}, {"Title": "Online-review analysis based large-scale group decision-making for determining passenger demands and evaluating passenger satisfaction: Case study of high-speed rail system in China", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "", "Page": "22", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 89733296, "Title": "Hydrogen bonds-induced room-temperature detection of DMMP based on polypyrrole-reduced graphene oxide hybrids", "Abstract": "The development of fast-response, high sensitivity and selectivity gas sensors for monitoring of organophosphorus is essential, where two dimensional graphene-based materials possessing exceptionsal carrier mobility are considered as promising candidates for room-temperature organophosphorus detection. However, it is challenging to fabrication of graphene-based gas sensors for detection of organophosphorus with excellent sensing performances. Herein, we develope a self-redox strategy to synthesize polypyrrole decorated-reduced graphene oxide hybrids (PPy-rGO) by redox reactions between pyrrole and GO during hydrothermal treatment process. This self-redox strategy results in the formation of perfact interfical strucutre between PPy and rGO through the π-π interactions without the impurity from the conventional oxidation/reducing agents. Most importantly, such PPy-rGO hybrids can be used as novel sensing materials for detection of dimethyl methylphosphonate (DMMP) at room temperature. Specially, the response of PPy-rGO-based sensor towards 100 ppm DMMP can reach 12.9 %, which is 3-fold higher than that pristine rGO-based sensor. Meanwhile, PPy-rGO-based sensor exhibits short response time/recovery time (43 s/75 s), low detection limit (5 ppm), excellent repeatability, and high selectivity. By combination of FT-IR and N<sub>2</sub> sorption isotherms, the enhanced DMMP sensing performances of PPy-rGO hybrids are concluded as following two aspects. Firstly, the formation of hydrogen bonds between PPy-rGO hybrids and DMMP molecules regulates the adsorption/desorption of DMMP. Secondly, increasing BET surface area by introduction of PPy into rGO matrix is beneficial to DMMP diffusion among the sensing materials. Our work would offer a new strategy for rational development of graphene-based materials for detection of organophosphorus at room temperature.", "Keywords": "PPy-rGO hybrids ; DMMP ; Gas sensor ; Hydrogen bond ; Room temperature", "DOI": "10.1016/j.snb.2021.130518", "PubYear": 2021, "Volume": "346", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, Changchun 130012, PR China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, Changchun 130012, PR China;Corresponding authors"}], "References": [{"Title": "Highly sensitive and selective electronic sensor based on Co catalyzed SnO2 nanospheres for acetone detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127237", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "An ammonia sensor composed of polypyrrole synthesized on reduced graphene oxide by electropolymerization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "305", "Issue": "", "Page": "127423", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Fabrication of electrospun Co3O4/CuO p-p heterojunctions nanotubes functionalized with HFIP for detecting chemical nerve agent under visible light irradiation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "314", "Issue": "", "Page": "128076", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A room-temperature NO2 gas sensor based on CuO nanoflakes modified with rGO nanosheets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "337", "Issue": "", "Page": "129783", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "UV-light-assisted NO2 gas sensor based on WS2/PbS heterostructures with full recoverability and reliable anti-humidity ability", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "339", "Issue": "", "Page": "129902", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Flexible MXene/rGO/CuO hybrid aerogels for high performance acetone sensing at room temperature", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "340", "Issue": "", "Page": "129946", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 89733297, "Title": "Fully component selection: An efficient combination of feature selection and principal component analysis to increase model performance", "Abstract": "Principal component analysis (PCA) is one the most effective and widely used dimensionality-reduction techniques which aggregates the maximal variance in the first few components. In many applications, the first few components of PCA are used in place of the original variables in statistical and machine learning models; nevertheless, such use does not guarantee the selection of the most relevant components to the target variable. This paper presents an efficient approach in which all components, rather than simply the first few, are considered as input for the random forest (RF) model, and a feature selection algorithm is integrated with the RF to select the most relevant components, hence called fully component selection (FCS). The proposed method was evaluated on spectroscopic data comprising 70 soil samples with 2050 spectral bands to estimate soil organic carbon (SOC). The FCS approach was compared to the RF model once considering the original features, then again using only the first few components, and again using all components without feature selection. The results showed that RF-FCS substantially outperformed other approaches, such that the R<sup>2</sup> was increased between 25.2% and 55.5%. Furthermore, the findings indicated that the most relevant principal components (PCs) to the target variable were not the first few, but PC6, PC7, PC8, PC15, and PC43. Using the FCS approach increased model performance substantively, and coupling it with machine learning and statistical models is strongly recommended for high dimensional data applications.", "Keywords": "High dimensional data ; Dimension reduction ; Random forest ; Spectroscopic data ; Principal component analysis", "DOI": "10.1016/j.eswa.2021.115678", "PubYear": 2021, "Volume": "186", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Water Engineering and Management, Tarbiat Modares University, Tehran, Iran;Address: Faculty of Agriculture, Tarbiat Modares University, VardAvard, Tehran 14977-1311, Iran"}], "References": [{"Title": "Overview and comparative study of dimensionality reduction techniques for high dimensional data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "44", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 89733305, "Title": "Blind image super-resolution based on prior correction network", "Abstract": "Convolutional neural networks (CNNs) based super-resolution (SR) methods have achieved remarkable progress in recent years. Most of these methods assume that the degradation is known and fixed, such as bicubic downsampling. However, the performance of CNNs-based methods suffers from a severe drop when the actual degradation mismatch the training one. This paper proposes a prior correction network (PCNet) to solve the blind SR problem, which makes CNNs-based super-resolvers trained in a fixed blur kernel but applied to other unknown blur kernels. The PCNet consists of a kernel estimation network, a correction filter module, and a correction refinement network. The kernel estimation network aims to estimate unknown blur kernel from the input low-resolution (LR) image. The correction filter module then transfers the estimated degraded domains to adapt to specific degradations (e.g., bicubic downsampling). Finally, the correction refinement network adjusts the corrected LR image to eliminate the influence of blur kernel mismatch or misestimate. Experimental results on diverse datasets show that the proposed PCNet, combined with existing CNNs-based SR methods, outperforms other state-of-the-art algorithms for blind SR. Code is available at: \\url{ https://github.com/caoxiang104/PCNet }.", "Keywords": "Convolutional neural networks ; Blind super-resolution ; Blur kernel ; Correction filter", "DOI": "10.1016/j.neucom.2021.07.070", "PubYear": 2021, "Volume": "463", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, Hubei 430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, Hubei 430074, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Design, Hunan University, Changsha, Hunan 410082, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Software and Communication Engineering, Xiangnan University, Chenzhou 423000, China"}, {"AuthorId": 5, "Name": "Tian<PERSON> Wang", "Affiliation": "School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, Hubei 430074, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, Hubei 430074, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai 200433, China;Corresponding author;@fudan.edu.cn"}], "References": [{"Title": "Super-resolution using multi-channel merged convolutional network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "394", "Issue": "", "Page": "136", "JournalTitle": "Neurocomputing"}, {"Title": "Residual scale attention network for arbitrary scale image super-resolution", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "427", "Issue": "", "Page": "201", "JournalTitle": "Neurocomputing"}, {"Title": "Image super-resolution based on residually dense distilled attention network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "443", "Issue": "", "Page": "47", "JournalTitle": "Neurocomputing"}, {"Title": "DAEANet: Dual auto-encoder attention network for depth map super-resolution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "454", "Issue": "", "Page": "350", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 89733329, "Title": "Ultrasensitive hydrogen detection by electrostatically formed silicon nanowire decorated by palladium nanoparticles", "Abstract": "Developing high performance hydrogen (H<sub>2</sub>) sensors is of utmost importance to facilitate the safe usage of H<sub>2</sub> as the alternative source of clean and renewable energy. We present an ultra-sensitive H<sub>2</sub> sensor operating in air and based on electrostatically formed nanowire (EFN) sensor decorated by palladium nanoparticles (Pd NPs). By appropriate tuning of the various gate voltages of the EFN, an extremely high sensor response of ∼2 × 10<sup>6</sup> % (0.8 % H<sub>2</sub> exposure) and a sensitivity of ∼400 % ppm<sup>−1</sup> is obtained at room temperature (20 ± 2 °C). This sensor outperforms, to the best of our knowledge, most of the reported resistive and field effect transistor (FET) based H<sub>2</sub> sensors. The EFN power consumption varies from few pW to ∼436 nW at maximum current operation thus enabling ultra-low power usage at room temperature. In addition, the sensor exhibits fast response and recovery times, retains good sensing performances even at 50 % relative humidity (RH) and exhibits reproducibility over time. Combining Pd NPs with the unique features of the EFN platform makes Pd-EFN a versatile, robust, low power, rapid, and highly sensitive H<sub>2</sub> sensor.", "Keywords": "Electrostatically formed silicon nanowire ; Palladium nanoparticles ; Hydrogen sensing ; Kelvin probe force microscopy", "DOI": "10.1016/j.snb.2021.130509", "PubYear": 2021, "Volume": "346", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physical Electronics, School of Electrical Engineering, Tel Aviv University, Ramat Aviv, 69978, Israel;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physical Electronics, School of Electrical Engineering, Tel Aviv University, Ramat Aviv, 69978, Israel"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physical Electronics, School of Electrical Engineering, Tel Aviv University, Ramat Aviv, 69978, Israel"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "London Centre for Nanotechnology and Department of Physics and Astronomy, University College London, Gower Street, London, WC1E 6BT, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Physical Electronics, School of Electrical Engineering, Tel Aviv University, Ramat Aviv, 69978, Israel"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "London Centre for Nanotechnology and Department of Physics and Astronomy, University College London, Gower Street, London, WC1E 6BT, UK"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physical Electronics, School of Electrical Engineering, Tel Aviv University, Ramat Aviv, 69978, Israel;Corresponding authors"}], "References": []}]