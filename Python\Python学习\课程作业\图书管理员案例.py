import os

class Book:
    def __init__(self, title, isbn, author, stock):
        self.title = title
        self.isbn = isbn # 国际标准书号
        self.author = author
        self.stock = stock

    def __str__(self):
        return f"书名：{self.title}\nISBN：{self.isbn}\n作者：{self.author}\n库存：{self.stock}"

class User:
    def __init__(self, username, password, is_admin=False):
        self.username = username
        self.password = password
        self.is_admin = is_admin

class Library:
    def __init__(self):
        self.books = []
        self.users = []
        self.current_user = None
        self.load_data()
    
    def load_data(self):
        # 加载图书数据
        if os.path.exists("课程作业\\books.txt"):
            with open("课程作业\\books.txt", "r", encoding="utf-8") as f:
                for line in f:
                    data = line.strip().split(",")
                    if len(data) == 4:
                        self.books.append(Book(data[0], data[1], data[2], int(data[3])))
        
        # 加载用户数据
        if os.path.exists("课程作业\\users.txt"):
            with open("课程作业\\users.txt", "r", encoding="utf-8") as f:
                for line in f:
                    data = line.strip().split(",")
                    if len(data) == 3:
                        self.users.append(User(data[0], data[1], data[2] == "True"))

    def save_data(self):
        # 保存图书数据
        with open("课程作业\\books.txt", "w", encoding="utf-8") as f:
            for book in self.books:
                f.write(f"{book.title},{book.isbn},{book.author},{book.stock}\n")
        
        # 保存用户数据
        with open("课程作业\\users.txt", "w", encoding="utf-8") as f:
            for user in self.users:
                f.write(f"{user.username},{user.password},{user.is_admin}\n")

    def login(self):
        while True:
            username = input("（魏海东 202331060927）用户名：")
            password = input("（魏海东 202331060927）密码：")
            
            for user in self.users:
                if user.username == username and user.password == password:
                    self.current_user = user
                    print(f"\n欢迎回来，{username}！\n（魏海东 202331060927）")
                    return True
            print("\n用户名或密码错误，请重试！（魏海东 202331060927）")

    def admin_menu(self):
        while True:
            print("\n===== 管理员菜单 =====")
            print("1. 添加图书")
            print("2. 删除图书")
            print("3. 修改图书信息")
            print("4. 显示所有图书")
            print("5. 退出系统\n（魏海东 202331060927）")
            
            choice = input("（魏海东 202331060927）请选择操作：")
            
            if choice == "1":
                self.add_book()
            elif choice == "2":
                self.delete_book()
            elif choice == "3":
                self.modify_book()
            elif choice == "4":
                self.show_all_books()
            elif choice == "5":
                self.save_data()
                print("（魏海东 202331060927）系统已退出！")
                exit()
            else:
                print("无效的输入！（魏海东 202331060927）")

    def user_menu(self):
        while True:
            print("\n===== 用户菜单 =====")
            print("1. 查询图书")
            print("2. 借阅图书")
            print("3. 归还图书")
            print("4. 退出系统\n（魏海东 202331060927）")
            
            choice = input("（魏海东 202331060927）请选择操作：")
            
            if choice == "1":
                self.search_book()
            elif choice == "2":
                self.borrow_book()
            elif choice == "3":
                self.return_book()
            elif choice == "4":
                self.save_data()
                print("系统已退出！（魏海东 202331060927）")
                exit()
            else:
                print("无效的输入！（魏海东 202331060927）")

    def add_book(self):
        print("\n===== 添加新书 =====\n（魏海东 202331060927）")
        isbn = input("ISBN：")
        
        # 检查ISBN是否重复
        for book in self.books:
            if book.isbn == isbn:
                print("该ISBN已存在！（魏海东 202331060927）")
                return
                
        title = input("书名：")
        author = input("作者：")
        stock = int(input("库存数量："))
        
        self.books.append(Book(title, isbn, author, stock))
        print("图书添加成功！（魏海东 202331060927）")

    def delete_book(self):
        isbn = input("请输入要删除的图书ISBN：")
        for book in self.books:
            if book.isbn == isbn:
                self.books.remove(book)
                print("图书删除成功！（魏海东 202331060927）")
                return
        print("未找到该ISBN对应的图书！")

    def modify_book(self):
        isbn = input("请输入要修改的图书ISBN：")
        for book in self.books:
            if book.isbn == isbn:
                print("留空表示不修改（魏海东 202331060927）")
                new_title = input(f"新书名（原：{book.title}）：") or book.title
                new_author = input(f"新作者（原：{book.author}）：") or book.author
                new_stock = input(f"新库存（原：{book.stock}）：") or book.stock
                
                book.title = new_title
                book.author = new_author
                book.stock = int(new_stock)
                print("修改成功！（魏海东 202331060927）")
                return
        print("未找到该ISBN对应的图书！（魏海东 202331060927）")

    def search_book(self):
        keyword = input("（魏海东 202331060927）请输入书名或ISBN进行搜索：").lower()
        results = []
        for book in self.books:
            if keyword in book.title.lower() or keyword == book.isbn:
                results.append(book)
        
        if results:
            print(f"\n找到 {len(results)} 本相关图书：")
            for i, book in enumerate(results, 1):
                print(f"\n【{i}】")
                print(book)
        else:
            print("未找到相关图书！（魏海东 202331060927）")

    def borrow_book(self):
        isbn = input("请输入要借阅的图书ISBN：")
        for book in self.books:
            if book.isbn == isbn:
                if book.stock > 0:
                    book.stock -= 1
                    print("借阅成功！（魏海东 202331060927）")
                else:
                    print("该图书库存不足！（魏海东 202331060927）")
                return
        print("（魏海东 202331060927）未找到该ISBN对应的图书！")

    def return_book(self):
        isbn = input("请输入要归还的图书ISBN：")
        for book in self.books:
            if book.isbn == isbn:
                book.stock += 1
                print("归还成功！（魏海东 202331060927）")
                return
        print("未找到该ISBN对应的图书！（魏海东 202331060927）")

    def show_all_books(self):
        if not self.books:
            print("当前没有图书！（魏海东 202331060927）")
            return
            
        print("\n===== 所有图书 =====")
        for i, book in enumerate(self.books, 1):
            print(f"\n【{i}】")
            print(book)

if __name__ == "__main__":
    # 初始化系统
    library = Library()
    
    # 添加默认管理员（首次运行时）
    if not library.users:
        library.users.append(User("admin", "123456", True))
        library.users.append(User("whd", "123456", False))
        library.save_data()
    
    if library.login():
        if library.current_user.is_admin:
            library.admin_menu()
        else:
            library.user_menu()