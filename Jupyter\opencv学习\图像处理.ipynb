{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 图像处理"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 形态学-腐蚀操作"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 展示图像\n", "def show_image(name,img):\n", "    cv2.imshow(name,img)\n", "    cv2.<PERSON><PERSON><PERSON>(0)\n", "    cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# cv2的涂片标题使用中文\n", "# utf-8转换为gbk解决问题\n", "def zh_ch(string):    \n", "\treturn string.encode(\"gbk\").decode(errors=\"ignore\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:2: <PERSON>yntaxWarning: invalid escape sequence '\\c'\n", "<>:2: <PERSON>yntaxWarning: invalid escape sequence '\\c'\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22308\\1404071341.py:2: SyntaxWarning: invalid escape sequence '\\c'\n", "  img = cv2.imread('images\\canjian.jpg')\n"]}], "source": ["# 原图\n", "img = cv2.imread('images\\canjian.jpg')\n", "\n", "show_image(zh_ch('原图'),img)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 腐蚀单位的大小\n", "kernel = np.ones((5,5),np.uint8)\n", "# 进行腐蚀操作\n", "erosion = cv2.erode(img,kernel,iterations = 1)\n", "show_image(zh_ch('腐蚀操作'),erosion)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:1: SyntaxWarning: invalid escape sequence '\\c'\n", "<>:1: SyntaxWarning: invalid escape sequence '\\c'\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22308\\*********.py:1: SyntaxWarning: invalid escape sequence '\\c'\n", "  pie = cv2.imread('images\\canjian.jpg')\n"]}], "source": ["pie = cv2.imread('images\\canjian.jpg')\n", "show_image(zh_ch('原图'),pie)\n", "# 腐蚀单位的大小\n", "kernel = np.ones((30,30),np.uint8)\n", "# 进行腐蚀操作\n", "erosion_1 = cv2.erode(pie,kernel,iterations = 1)\n", "erosion_2 = cv2.erode(pie,kernel,iterations = 2)\n", "erosion_3 = cv2.erode(pie,kernel,iterations = 3)\n", "res = np.hstack((erosion_1,erosion_2,erosion_3))\n", "\n", "show_image(zh_ch('腐蚀操作'),res)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 膨胀操作"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": [" # 选择前面腐蚀完的结果erosion\n", "kernel = np.ones((5,5),np.uint8)\n", "# 进行膨胀操作\n", "dilation = cv2.dilate(erosion,kernel,iterations = 1)\n", "h = np.hstack((erosion,dilation))\n", "\n", "show_image(zh_ch('膨胀操作'),h)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 开运算与闭运算"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# 开：先腐蚀，再膨胀\n", "img = cv2.imread('images\\\\canjian.jpg')\n", "kernel = np.ones((5,5),np.uint8)\n", "\n", "opening = cv2.morphologyEx(img, cv2.MORPH_OPEN, kernel)\n", "show_image(zh_ch('开运算'),opening)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# 闭：先膨胀，再腐蚀\n", "closing = cv2.morphologyEx(img, cv2.MORPH_CLOSE, kernel)\n", "show_image(zh_ch('闭运算'),closing)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 梯度运算"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# 梯度：膨胀图像-腐蚀图像\n", "img = cv2.imread('images\\\\canjian.jpg')\n", "kernel = np.ones((10,10),np.uint8)\n", "\n", "gradient = cv2.morphologyEx(img, cv2.MORPH_GRADIENT, kernel)\n", "show_image(zh_ch('梯度运算'),gradient)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 礼帽与黑帽\n", "- 礼帽：原始输入-开运算结果\n", "- 黑帽：闭运算结果-原始输入"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# 礼帽\n", "tophat = cv2.morphologyEx(img, cv2.MORPH_TOPHAT, kernel)\n", "show_image(zh_ch('礼帽'),tophat)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# 黑帽\n", "blackhat = cv2.morphologyEx(img, cv2.MORPH_BLACKHAT, kernel)\n", "show_image(zh_ch('黑帽'),blackhat)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["###  图像梯度-<PERSON><PERSON>算子\n", "![](2025-03-12-21-53-48.png)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["img = cv2.imread('images\\\\a.png',cv2.IMREAD_GRAYSCALE)\n", "show_image(zh_ch('原图'),img)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["dst = cv2.Sobel(src,ddepth,dx,dy,ksize)\n", "- ddepth :表示输出图像与原图像深度 -1表示相同\n", "- dx和dy：表示x和y方向上的导数阶数\n", "- ksize：表示Sobel算子的大小"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# 按照水平\n", "sobel = cv2.Sobel(img,cv2.CV_64F,1,0,ksize=3)\n", "# 右减左\n", "show_image(zh_ch('Sobel算子'),sobel)\n", "# 按照垂直\n", "sobel = cv2.Sobel(img,cv2.CV_64F,0,1,ksize=3)\n", "show_image(zh_ch('Sobel算子'),sobel)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["所有负数会被截断为0，也就是显示黑色\n", "\n", "所以需要使用绝对值函数"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["sobelx = cv2.Sobel(img,cv2.CV_64F,1,0,ksize=3)\n", "sobelx = cv2.convertScaleAbs(sobelx)\n", "show_image(zh_ch('Sobelx算子'),sobelx)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["sobely = cv2.Sobel(img,cv2.CV_64F,0,1,ksize=3)\n", "sobely = cv2.convertScaleAbs(sobely)\n", "show_image(zh_ch('Sobely算子'),sobely)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["分别计算x和y，再求和"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["sobelxy = cv2.addWeighted(sobelx,0.5,sobely,0.5,0)\n", "show_image(zh_ch('Sobelxy算子'),sobelxy)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["不建议直接计算x和y，因为x和y的值范围不同，所以需要归一化\n", "sobel = cv2.Sobel(img,cv2.CV_64F,1,1,ksize=3) 最好不要"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["img = cv2.imread('images\\\\test.png',cv2.IMREAD_GRAYSCALE)\n", "show_image(zh_ch('原图'),img)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# 分开算\n", "sobelx = cv2.Sobel(img,cv2.CV_64F,1,0,ksize=3)\n", "sobelx = cv2.convertScaleAbs(sobelx)\n", "show_image(zh_ch('Sobelx算子'),sobelx)\n", "sobely = cv2.Sobel(img,cv2.CV_64F,0,1,ksize=3)\n", "sobely = cv2.convertScaleAbs(sobely)\n", "show_image(zh_ch('Sobely算子'),sobely)\n", "sobelxy = cv2.addWeighted(sobelx,0.5,sobely,0.5,0)\n", "show_image(zh_ch('Sobelxy算子'),sobelxy)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# 一起算\n", "sobel = cv2.Sobel(img,cv2.CV_64F,1,1,ksize=3)\n", "sobel = cv2.convertScaleAbs(sobel)\n", "show_image(zh_ch('Sobel算子'),sobel)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 图像梯度- <PERSON><PERSON><PERSON>算子\n", "![](2025-03-12-21-52-53.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 图像梯度 - Laplacian算子\n", "![](2025-03-12-21-52-28.png)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# 不同算子的差异\n", "img = cv2.imread('images\\\\test.png',cv2.IMREAD_GRAYSCALE)\n", "sobelx = cv2.Sobel(img,cv2.CV_64F,1,0,ksize=3)\n", "sobelx = cv2.convertScaleAbs(sobelx)\n", "sobely = cv2.Sobel(img,cv2.CV_64F,0,1,ksize=3)\n", "sobely = cv2.convertScaleAbs(sobely)\n", "sobelxy = cv2.addWeighted(sobelx,0.5,sobely,0.5,0)\n", "\n", "\n", "scharrx = cv2.<PERSON><PERSON><PERSON>(img,cv2.CV_64F,1,0)\n", "scharrx = cv2.convertScaleAbs(scharrx)\n", "scharry = cv2.<PERSON><PERSON><PERSON>(img,cv2.CV_64F,0,1)\n", "scharry = cv2.convertScaleAbs(scharry)\n", "scharrxy = cv2.addWeighted(scharrx,0.5,scharry,0.5,0)\n", "\n", "laplacian = cv2.Laplacian(img,cv2.CV_64F)\n", "laplacian = cv2.convertScaleAbs(laplacian)\n", "\n", "res = np.hstack((sobelxy,scharrxy,laplacian))\n", "show_image(zh_ch('不同算子的差异'),res)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 图像平滑\n", "![](2025-03-12-22-16-28.png)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["img = cv2.imread('images\\\\test.png')\n", "show_image(zh_ch('原图'),img)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 均值滤波"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# 均值滤波\n", "# 简单的平均卷积操作\n", "blur = cv2.blur(img,(3,3))\n", "\n", "show_image(zh_ch('均值滤波'),blur)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# 方框滤波\n", "# 与均值滤波类似，可以选择归一化\n", "box = cv2.boxFilter(img,-1,(3,3),normalize=True)\n", "show_image(zh_ch('方框滤波'),box)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 高斯滤波\n", "# 高斯模糊，卷积核的值是按照高斯分布来计算的\n", "gaussian = cv2.<PERSON><PERSON><PERSON><PERSON>lur(img,(5,5),1)\n", "show_image(zh_ch('高斯滤波'),gaussian)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# 中值滤波\n", "# 用像素点邻域中数值的中间值来代替该像素点的值，对消除椒盐噪声非常有效\n", "median = cv2.medianBlur(img,5)\n", "show_image(zh_ch('中值滤波'),median)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# 展示所有的\n", "res = np.hstack((blur,box,gaussian,median))\n", "show_image(zh_ch('所有滤波'),res)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 图像阈值\n", "![](![](2025-03-13-17-36-42.png).png)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda\\Lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 21407 (\\N{CJK UNIFIED IDEOGRAPH-539F}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "d:\\Anaconda\\Lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ret,thresh1 = cv2.threshold(img,127,255,cv2.THRESH_BINARY)\n", "ret,thresh2 = cv2.threshold(img,127,255,cv2.THRESH_BINARY_INV)\n", "ret,thresh3 = cv2.threshold(img,127,255,cv2.THRESH_TRUNC)\n", "ret,thresh4 = cv2.threshold(img,127,255,cv2.THRESH_TOZERO)\n", "ret,thresh5 = cv2.threshold(img,127,255,cv2.THRESH_TOZERO_INV)\n", "\n", "titles = ['原图','BINARY','BINARY_INV','TRUNC','TOZERO','TOZERO_INV']\n", "images = [img,thresh1,thresh2,thresh3,thresh4,thresh5]\n", "\n", "for i in range(6):\n", "    plt.subplot(2,3,i+1),plt.imshow(images[i],'gray')\n", "    plt.title(titles[i])\n", "    plt.xticks([]),plt.yticks([])\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Canny 边缘检测\n", "![](2025-03-17-21-45-14.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](2025-03-17-21-50-03.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](2025-03-17-21-51-17.png)\n", "![](2025-03-17-21-51-50.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](2025-03-17-21-56-06.png)\n", "![](2025-03-17-21-56-35.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](2025-03-17-22-05-12.png)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["img = cv2.imread('images\\\\test.png',cv2.IMREAD_GRAYSCALE)\n", "\n", "v1 = cv2.<PERSON><PERSON>(img,80,150)\n", "v2 = cv2.Canny(img,50,100)\n", "\n", "res = np.hstack((img,v1,v2))\n", "show_image(zh_ch('<PERSON>ny 边缘检测'),res)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 图像轮廓\n", "![](2025-03-18-00-05-32.png)\n", "为了更高的准确率，使用二值图像"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["img = cv2.imread('images\\\\test.png')\n", "gray = cv2.cvtColor(img,cv2.COLOR_BGR2GRAY)\n", "ret,thresh = cv2.threshold(gray,127,255,cv2.THRESH_BINARY)\n", "show_image(zh_ch('二值图像'),thresh)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["contours,hierarchy = cv2.findContours(thresh,cv2.RETR_TREE,cv2.CHAIN_APPROX_NONE)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 绘制轮廓"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["draw_img = img.copy()\n", "res = cv2.drawContours(draw_img,contours,-1,(0,0,255),2)\n", "show_image(zh_ch('绘制轮廓'),res)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 轮廓特征"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["cnt = contours[0]\n", "# 轮廓面积\n", "area = cv2.contourArea(cnt)\n", "# 轮廓周长\n", "perimeter = cv2.arcLength(cnt,True)\n", "# 轮廓近似\n", "epsilon = 0.1*cv2.arcLength(cnt,True)\n", "approx = cv2.approxPolyDP(cnt,epsilon,True)\n", "# 凸包"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 轮廓近似"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["img = cv2.imread('images\\\\knn_01.png')\n", "\n", "gray = cv2.cvtColor(img,cv2.COLOR_BGR2GRAY)\n", "ret,thresh = cv2.threshold(gray,127,255,cv2.THRESH_BINARY)\n", "contours,hierarchy = cv2.findContours(thresh,cv2.RETR_TREE,cv2.CHAIN_APPROX_NONE)\n", "cnt = contours[0]\n", "\n", "draw_img = img.copy()\n", "res = cv2.drawContours(draw_img,[cnt],-1,(0,0,255),2)\n", "show_image(zh_ch('轮廓近似'),res)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["epsilon = 0.1*cv2.arcLength(cnt,True)\n", "approx = cv2.approxPolyDP(cnt,epsilon,True)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}