[{"ArticleId": 92454148, "Title": "Fast Barycentric-Based Evaluation Over Spectral/hp Elements", "Abstract": "As the use of spectral/ hp element methods, and high-order finite element methods in general, continues to spread, community efforts to create efficient, optimized algorithms associated with fundamental high-order operations have grown. Core tasks such as solution expansion evaluation at quadrature points, stiffness and mass matrix generation, and matrix assembly have received tremendous attention. With the expansion of the types of problems to which high-order methods are applied, and correspondingly the growth in types of numerical tasks accomplished through high-order methods, the number and types of these core operations broaden. This work focuses on solution expansion evaluation at arbitrary points within an element. This operation is core to many postprocessing applications such as evaluation of streamlines and pathlines, as well as to field projection techniques such as mortaring. We expand barycentric interpolation techniques developed on an interval to 2D (triangles and quadrilaterals) and 3D (tetrahedra, prisms, pyramids, and hexahedra) spectral/ hp element methods. We provide efficient algorithms for their implementations, and demonstrate their effectiveness using the spectral/ hp element library Nektar++ by running a series of baseline evaluations against the ‘standard’ Lagrangian method, where an interpolation matrix is generated and matrix-multiplication applied to evaluate a point at a given location. We present results from a rigorous series of benchmarking tests for a variety of element shapes, polynomial orders and dimensions. We show that when the point of interest is to be repeatedly evaluated, the barycentric method performs at worst $$50\\%$$ \n \n 50 \n % \n \n slower, when compared to a cached matrix evaluation. However, when the point of interest changes repeatedly so that the interpolation matrix must be regenerated in the ‘standard’ approach, the barycentric method yields far greater performance, with a minimum speedup factor of $$7\\times $$ \n \n 7 \n × \n \n . Furthermore, when derivatives of the solution evaluation are also required, the barycentric method in general slightly outperforms the cached interpolation matrix method across all elements and orders, with an up to $$30\\%$$ \n \n 30 \n % \n \n speedup. Finally we investigate a real-world example of scalar transport using a non-conformal discontinuous Galerkin simulation, in which we observe around $$6\\times $$ \n \n 6 \n × \n \n speedup in computational time for the barycentric method compared to the matrix-based approach. We also explore the complexity of both interpolation methods and show that the barycentric interpolation method requires $${\\mathcal {O}}(k)$$ \n \n O \n ( \n k \n ) \n \n storage compared to a best case space complexity of $${\\mathcal {O}}(k^2)$$ \n \n O \n ( \n \n k \n 2 \n \n ) \n \n for the Lagrangian interpolation matrix method.", "Keywords": "High-order finite elements; Spectral/hp elements; Point evaluation; Barycentric interpolation", "DOI": "10.1007/s10915-021-01750-2", "PubYear": 2022, "Volume": "90", "Issue": "2", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mathematics and Physical Sciences, College of Engineering, University of Exeter, Exeter, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Scientific Computing and Imaging Institute and School of Computing, University of Utah, Salt Lake City, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Scientific Computing and Imaging Institute and Department of Mathematics, University of Utah, Salt Lake City, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Scientific Computing and Imaging Institute and School of Computing, University of Utah, Salt Lake City, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Engineering, King’s College London, London, UK"}], "References": [{"Title": "Nektar++: Enhancing the capability and application of high-fidelity spectral/hp element methods", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "249", "Issue": "", "Page": "107110", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 92454243, "Title": "Development of Hausa dataset a baseline for speech recognition", "Abstract": "The Hausa language read-speech dataset was created by recording native Hausa speakers. The recording took place at Nile university of Nigeria audio studio and radio broadcasting studio. The recorded dataset was segmented into unigram and bigram. The Hausa speech dataset contain 47hr of recorded audio speech. The dataset can be used for automatic speech recognition, speech synthesis, Text-to-Speech and speech-to-text application.", "Keywords": "Corpus ; Automatic speech ; NLP ; Text-to-speech ; Hausa corpus", "DOI": "10.1016/j.dib.2022.107820", "PubYear": 2022, "Volume": "40", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Natural and Applied Sciences, Computer Science Department, Nile University of Nigeria, Abuja, Nigeria;Corresponding authors;@umarfaruk45"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Natural and Applied Sciences, Computer Science Department, Nile University of Nigeria, Abuja, Nigeria;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Natural and Applied Sciences, Computer Science Department, Nile University of Nigeria, Abuja, Nigeria"}], "References": []}, {"ArticleId": 92454508, "Title": "A trusted de-swinging k-anonymity scheme for location privacy protection", "Abstract": "K-anonymity has been gaining widespread attention as one of the most widely used technologies to protect location privacy. Nevertheless, there are still some threats such as behavior deception and service swing, since utilizing distributed k-anonymity technology to construct an anonymous domain. More specifically, the coordinate of the honest node will be a leak if the malicious nodes submit wrong locations coordinate to take part in the domain construction process. Worse still, owing to service swing, the attacker increases the reputation illegally to deceive honest nodes again. To overcome those drawbacks, we propose a trusted de-swinging k-anonymity scheme for location privacy protection. Primarily, we introduce a de-swinging reputation evaluation method (DREM), which designs a penalty factor to curb swinging behavior. This method calculates the reputation from entity honesty degree, location information entropy, and service swing degree. Besides, based on our proposed DREM, a credible cloaking area is constructed to protect the location privacy of the requester. In the area, nodes can choose some nodes with a high reputation for completing the construction process of the anonymous domain. Finally, we design reputation contracts to calculate credit automatically based on smart contracts. The security analysis and simulation results indicate that our proposed scheme effectively resists malicious attacks, curbs the service swing, and encourages nodes to participate honestly in the construction of cloaking areas.", "Keywords": "Computer Communication Networks", "DOI": "10.1186/s13677-021-00272-4", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Public Big Data, College of Computer Science and Technology, Guizhou University, Guiyang, China"}, {"AuthorId": 2, "Name": "Baopeng Ye", "Affiliation": "Information Technology Innovation Service Center of Guizhou Province, National University of Sciences, Guiyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Public Big Data, College of Computer Science and Technology, Guizhou University, Guiyang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Public Big Data, College of Computer Science and Technology, Guizhou University, Guiyang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyberspace Security, Beijing University of Posts and Telecommnuications, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Guizhou CoVision Science & Technology Co., Ltd, Guiyang, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China"}], "References": [{"Title": "Tesia: A trusted efficient service evaluation model in Internet of things based on improved aggregation signature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "16", "Page": "e5739", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Belief and fairness: A secure two-party protocol toward the view of entropy for IoT devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "102641", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Incentive compatible and anti-compounding of wealth in proof-of-stake", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "530", "Issue": "", "Page": "85", "JournalTitle": "Information Sciences"}, {"Title": "Location Privacy-preserving Mechanisms in Location-based Services", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "IoT-based data logger for weather monitoring using arduino-based wireless sensor networks with remote graphical application and alerts", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "1", "Page": "25", "JournalTitle": "Big Data Mining and Analytics"}, {"Title": "New enhanced authentication protocol for Internet of Things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Big Data Mining and Analytics"}, {"Title": "Analysis and predictions of spread, recovery, and death caused by COVID-19 in India", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "2", "Page": "65", "JournalTitle": "Big Data Mining and Analytics"}, {"Title": "An attention‐based category‐aware GRU model for the next POI recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "7", "Page": "3174", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Semi‐selfish mining based on hidden <PERSON>ov decision process", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "7", "Page": "3596", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Improvising personalized travel recommendation system with recency effects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "3", "Page": "139", "JournalTitle": "Big Data Mining and Analytics"}, {"Title": "PSSPR: A source location privacy protection scheme based on sector phantom routing in WSNs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "2", "Page": "1204", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": ********, "Title": "Adaptive neural decentralised control for switched interconnected nonlinear systems with backlash-like hysteresis and output constraints", "Abstract": "This paper considers the issue of adaptive neural decentralised tracking control for a class of output-constraint switched interconnected nonlinear systems with unknown backlash-like hysteresis control input. First, neural networks (NNs) are applied to approximate unknown nonlinear functions, and an NNs switched state observer is designed to estimate unmeasured system states. Then, the dynamic surface control technique is used to avoid the influence of explosion of complexity. In addition, the problem of output constraints is solved by introducing the barrier <PERSON><PERSON><PERSON><PERSON> functions. Based on the Lya<PERSON>nov stability theory, all signals in the switched closed-loop system can be verified to be uniformly ultimately bounded under the proposed control method. Moreover, the system output can track the target trajectory well within a small bounded error. Finally, a numerical simulation result is given to illustrate the effectiveness of the adaptive decentralised control scheme.", "Keywords": "Switched interconnected nonlinear systems ; adaptive neural decentralised control ; dynamic surface control ; output constraints ; backlash-like hysteresis", "DOI": "10.1080/00207721.2021.2017063", "PubYear": 2022, "Volume": "53", "Issue": "7", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Control Science and Engineering, Bohai University, Jinzhou, Liaoning, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Control Science and Engineering, Bohai University, Jinzhou, Liaoning, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Control Science and Engineering, Bohai University, Jinzhou, Liaoning, People's Republic of China"}, {"AuthorId": 4, "Name": "Hu<PERSON><PERSON> Wang", "Affiliation": "School of Mathematics and Physics, Bohai University, Jinzhou, Liaoning, People's Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Zhao", "Affiliation": "College of Control Science and Engineering, Bohai University, Jinzhou, Liaoning, People's Republic of China;Faculty of Electronic Information and Electrical Engineering, Dalian University of Technology, Dalian, Liaoning, People's Republic of China"}], "References": [{"Title": "Adaptive fuzzy hierarchical sliding mode control of uncertain under-actuated switched nonlinear systems with actuator faults", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "8", "Page": "1499", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Switched-observer-based adaptive output-feedback control design with unknown gain for pure-feedback switched nonlinear systems via average dwell time", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "9", "Page": "1731", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Sliding-mode surface-based adaptive actor-critic optimal control for switched nonlinear systems with average dwell time", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "756", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 92454684, "Title": "Global adaptive output regulation for nonlinear output feedback affine systems", "Abstract": "In this paper, the global output regulation problem (ORP) for nonlinear output feedback affine systems with unknown functions (UFs) and unmeasured states is studied. Firstly, a Lemma is proposed to solve not only the global ORP of nonlinear systems, but also the ‘explosion of terms’ problem of backstepping. Secondly, the control scheme is designed based on backstepping method, which avoids an Assumption to solve the ORP. Therefore, the algorithm reduces the conservatism and increases the applicability. Finally, the algorithm is applied to Duffing system to verify the effectiveness of the algorithm.", "Keywords": "Nonlinear systems ; output regulation ; internal model ; backstepping ; output feedback", "DOI": "10.1080/00207721.2021.2021314", "PubYear": 2022, "Volume": "53", "Issue": "8", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Automation Engineering, Nanjing Normal University, Nanjing, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Nanjing University of Science and Technology, Nanjing, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Lu", "Affiliation": "School of Electrical and Automation Engineering, Nanjing Normal University, Nanjing, People's Republic of China"}], "References": []}, {"ArticleId": 92454731, "Title": "Two studies of the perceptions of risk, benefits and likelihood of undertaking password management behaviours", "Abstract": "Passwords remain the most common form of authentication in the digital world. People have increasing numbers of passwords, and many people undertake risky password management behaviours such as re-using passwords, writing them down and sharing them. It is unclear whether people persist in these behaviours because they do not understand the risks involved or the benefits of the behaviour outweigh the risk. Two studies investigated the relationships between people's perceptions of the risks and benefits of a range of password management behaviours and their self-reports of their likelihood of undertaking them. The first study involved 120 MTurkers in the U.S.A., the second study involved 128 respondents from 26 different countries including approximately 50% students. In both studies, respondents took rated the risks, benefits and likelihood of undertaking 15 password management behaviours. The two very different samples provided very similar results, validating our classification of password management behaviours into four components: Storing, Sharing, Loggingn and Change behaviours. Somecomponents were more affected by perceptions of the benefits, others were equally affected by the perceptions of the risks and the benefits. These results have implications for how information about risky password behaviours is presented to users and general education about password security.", "Keywords": "Passwords ; password management behaviour ; password risks ; password benefits ; usable security", "DOI": "10.1080/0144929X.2021.2019832", "PubYear": 2022, "Volume": "41", "Issue": "12", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of York, York, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of York, York, UK"}], "References": [{"Title": "Nudging personalized password policies by understanding users’ personality", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "101801", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 92454842, "Title": "Spatially resolved transcriptomics in immersive environments", "Abstract": "Spatially resolved transcriptomics is an emerging class of high-throughput technologies that enable biologists to systematically investigate the expression of genes along with spatial information. Upon data acquisition, one major hurdle is the subsequent interpretation and visualization of the datasets acquired. To address this challenge, VR-Cardiomics is presented, which is a novel data visualization system with interactive functionalities designed to help biologists interpret spatially resolved transcriptomic datasets. By implementing the system in two separate immersive environments, fish tank virtual reality (FTVR) and head-mounted display virtual reality (HMD-VR), biologists can interact with the data in novel ways not previously possible, such as visually exploring the gene expression patterns of an organ, and comparing genes based on their 3D expression profiles. Further, a biologist-driven use-case is presented, in which immersive environments facilitate biologists to explore and compare the heart expression profiles of different genes.", "Keywords": "Fish tank virtual reality;Head-mounted display;Immersive analytics;Immersive environment;Spatial transcriptomics;Spatially-resolved transcriptomics;Virtual reality", "DOI": "10.1186/s42492-021-00098-6", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 4922, "JournalTitle": "Visual Computing for Industry, Biomedicine, and Art", "ISSN": "", "EISSN": "2524-4442", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer and Information Science, University of Konstanz, Konstanz, Germany. ;Cell Biology, Murdoch Children's Research Institute, Parkville, Melbourne, VIC, Australia."}, {"AuthorId": 2, "Name": "<PERSON>eu T<PERSON>", "Affiliation": "Cell Biology, Murdoch Children's Research Institute, Parkville, Melbourne, VIC, Australia. ;Australian Regenerative Medicine Institute, Monash University, Clayton, Melbourne, VIC, Australia. ;Systems Biology Institute Australia, Clayton, Melbourne, VIC, Australia."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Information Science, University of Konstanz, Konstanz, Germany."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Information Science, University of Konstanz, Konstanz, Germany."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Information Science, University of Konstanz, Konstanz, Germany."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Cell Biology, Murdoch Children's Research Institute, Parkville, Melbourne, VIC, Australia.  . ;Australian Regenerative Medicine Institute, Monash University, Clayton, Melbourne, VIC, Australia.  . ;Systems Biology Institute Australia, Clayton, Melbourne, VIC, Australia.  ."}, {"AuthorId": 7, "Name": "Falk Schreiber", "Affiliation": "Department of Computer and Information Science, University of Konstanz, Konstanz, Germany.  . ;Faculty of Information Technologies, Monash University, Melbourne, Australia.  ."}], "References": [{"Title": "Revisiting Milgram and <PERSON><PERSON><PERSON>'s Reality-Virtuality Continuum", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "27", "JournalTitle": "Frontiers in Virtual Reality"}]}, {"ArticleId": 92455153, "Title": "A multi-stakeholder foundation for peace in cyberspace", "Abstract": "In this article we first explore the concept of ‘multi-stakeholderism’, focusing on how the term was understood when internet governance was first envisioned. We then elaborate on how the concept has evolved in recent years. The focus of the article here, given the shift in how cyberspace is treated by state actors – increasingly as a domain of conflict. We detail how the United Nations (UN) has approached international peace and security online since 2004, via a series of ad hoc working groups that have been largely exclusive to a small number of state participants, and the expansion of interstate conflict online during this same period. The article also introduces a number of informal initiatives that have been spearheaded by multi-stakeholder groups outside the auspices of the UN since 2018 – the Charter of Trust, the Cybersecurity Tech Accord, the Paris Call for Trust and Security in Cyberspace, the Contract for the Web, and the Let’s Talk Cyber dialogue series – focusing on the role of the private sector in particular to promote peace and security online. Finally, the article explores what could help ensure that the next generation of cybersecurity dialogues at the UN are structured to address escalating conflict in cyberspace and to take full advantage of external voices in this effort.", "Keywords": "Multi-stakeholder ; cybersecurity ; cyber conflict ; diplomacy ; peace ; norms", "DOI": "10.1080/23738871.2021.2023603", "PubYear": 2021, "Volume": "6", "Issue": "3", "JournalId": 23731, "JournalTitle": "Journal of Cyber Policy", "ISSN": "2373-8871", "EISSN": "2373-8898", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Senior Director, Microsoft Corp, Ljubljana Slovenia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Senior Government Affairs Manager, Microsoft Corp, Redmond United States"}], "References": [{"Title": "Microsoft Digital Defense Report", "Authors": "", "PubYear": 2020, "Volume": "2020", "Issue": "10", "Page": "4", "JournalTitle": "Network Security"}]}, {"ArticleId": 92455165, "Title": "Thermometer: a webserver to predict protein thermal stability", "Abstract": "<p><b>MOTIVATION</b>:Thermal properties of proteins are of great importance for a number of theoretical and practical implications. Predicting the thermal stability of a protein is a difficult and still scarcely addressed task.</p><p><b>RESULTS</b>:Here, we introduce Therm<PERSON>, a webserver to assess the thermal stability of a protein using structural information. Thermometer is implemented as a publicly available with user-friendly interface.</p><p><b>AVAILABILITY</b>:Our server can be found at the following link (all major browser supported): http://service.tartaglialab.com/new_submission/thermometer_file.</p><p><b>SUPPLEMENTARY INFORMATION</b>:Supplementary data are available at Bioinformatics online.</p><p>© The Author(s) 2022. Published by Oxford University Press.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btab868", "PubYear": 2022, "Volume": "38", "Issue": "7", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Sapienza University of Rome, Rome, 00185, Italy. ;Center for Life Nano &amp; Neuroscience, Italian Institute of Technology, Rome, 00161, Italy."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biology, Sapienza University of Rome, Rome, 00185, Italy."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Life Nano &amp; Neuroscience, Italian Institute of Technology, Rome, 00161, Italy."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Sapienza University of Rome, Rome, 00185, Italy. ;Center for Life Nano &amp; Neuroscience, Italian Institute of Technology, Rome, 00161, Italy."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Sapienza University of Rome, Rome, 00185, Italy. ;Center for Life Nano &amp; Neuroscience, Italian Institute of Technology, Rome, 00161, Italy."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Life Nano &amp; Neuroscience, Italian Institute of Technology, Rome, 00161, Italy. ;Department of Biology, Sapienza University of Rome, Rome, 00185, Italy."}], "References": []}, {"ArticleId": 92455189, "Title": "BioThings SDK: a toolkit for building high-performance data APIs in biomedical research", "Abstract": "<p><b>SUMMARY</b>:To meet the increased need of making biomedical resources more accessible and reusable, Web APIs or web services have become a common way to disseminate knowledge sources. The BioThings APIs are a collection of high-performance, scalable, annotation as a service APIs that automate the integration of biological annotations from disparate data sources. This collection of APIs currently includes MyGene.info, MyVariant.info, and MyChem.info for integrating annotations on genes, variants, and chemical compounds, respectively. These APIs are used by both individual researchers and application developers to simplify the process of annotation retrieval and identifier mapping. Here, we describe the BioThings Software Development Kit (SDK), a generalizable and reusable toolkit for integrating data from multiple disparate data sources and creating high-performance APIs. This toolkit allows users to easily create their own BioThings APIs for any data type of interest to them, as well as keep APIs up-to-date with their underlying data sources.</p><p><b>AVAILABILITY AND IMPLEMENTATION</b>:The BioThings SDK is built in Python and released via PyPI (https://pypi.org/project/biothings/). Its source code is hosted at its github repository (https://github.com/biothings/biothings.api).</p><p><b>SUPPLEMENTARY INFORMATION</b>:Supplementary data are available at Bioinformatics online.</p><p>© The Author(s) 2022. Published by Oxford University Press.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btac017", "PubYear": 2022, "Volume": "38", "Issue": "7", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Integrative Structural and Computational Biology, The Scripps Research Institute, 10550 North Torrey Pines Rd, La Jolla, 92037, CA."}], "References": []}, {"ArticleId": 92455466, "Title": "Control flow equivalence method for establishing sanctity of compiling", "Abstract": "Compiler trap doors constitute a longstanding security problem for which there is currently no good solution. This paper presents a practical approach that shifts the focus from compiler correctness in its entirety to compiler correctness for a particular software. The guarantee is that the compiled binary’s control flow is consistent with the source’s control flow for a software of interest. We present an automated method to establish the equivalence by checking whether the source and the binary control flow graphs (CFGs) are isomorphic after semantics preserving graph transformations. The automated method produces evidence that can enable and simplify manual cross-checking as required to qualify an automated tool for safety-critical applications. We believe the proposed control equivalence method and its automation would be equally useful in avionics, automotive, medical devices and other safety-critical software industries where establishing trust in the binary code is critically important.", "Keywords": "Compiler ; Security ; Safety ; Control-equivalence ; Transformation", "DOI": "10.1016/j.cose.2022.102608", "PubYear": 2022, "Volume": "115", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "EnSoft Corp. Ames, Iowa"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "EnSoft Corp. Ames, Iowa;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Iowa State University, Ames, Iowa"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Iowa State University, Ames, Iowa"}], "References": [{"Title": "The graph isomorphism problem", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "128", "JournalTitle": "Communications of the ACM"}, {"Title": "A Survey of Binary Code Similarity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 92455837, "Title": "Combining haptics and inertial motion capture to enhance remote control of a dual-arm robot", "Abstract": "High dexterity is required in tasks in which there is contact between objects, such as surface conditioning (wiping, polishing, scuffing, sanding, etc.), specially when the location of the objects involved is unknown or highly inaccurate because they are moving, like a car body in automotive industry lines. These applications require the human adaptability and the robot accuracy. However, sharing the same workspace is not possible in most cases due to safety issues. Hence, a multi-modal teleoperation system combining haptics and an inertial motion capture system is introduced in this work. The human operator gets the sense of touch thanks to haptic feedback, whereas using the motion capture device allows more naturalistic movements. Visual feedback assistance is also introduced to enhance immersion. A Baxter dual-arm robot is used to offer more flexibility and manoeuvrability, allowing to perform two independent operations simultaneously. Several tests have been carried out to assess the proposed system. As it is shown by the experimental results, the task duration is reduced and the overall performance improves thanks to the proposed teleoperation method.", "Keywords": "Multimodal teleoperation; Haptic feedback; Motion capture; Dual-arm robotics; Collaborative robot; Surface conditioning", "DOI": "10.1007/s12193-021-00386-8", "PubYear": 2022, "Volume": "16", "Issue": "2", "JournalId": 28285, "JournalTitle": "Journal on Multimodal User Interfaces", "ISSN": "1783-7677", "EISSN": "1783-8738", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Departament d’Enginyeria Electrònica, Universitat de València, Burjassot, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Personal Robotics Laboratory, Department of Electrical and Electronic Engineering, Imperial College London, London, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto de Diseño y Fabricación, Universitat Politècnica de València, Valencia, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Diseño y Fabricación, Universitat Politècnica de València, Valencia, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Personal Robotics Laboratory, Department of Electrical and Electronic Engineering, Imperial College London, London, UK"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Diseño y Fabricación, Universitat Politècnica de València, Valencia, Spain"}], "References": [{"Title": "RFID-based tangible and touch tabletop for dual reality in crisis management context", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "1", "Page": "31", "JournalTitle": "Journal on Multimodal User Interfaces"}, {"Title": "Bimanual robot control for surface treatment tasks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "1", "Page": "74", "JournalTitle": "International Journal of Systems Science"}]}, {"ArticleId": 92455842, "Title": "Optimization on the Turning Process Parameters of SS 304 Using Taguchi and TOPSIS", "Abstract": "<p>Turning is a basic machining technique where parameters may be optimised to improve machining performance. The Taguchi and TOPSIS methods were used to find the parameters of optimum process in turning SS 304 using coated carbide tools. Cutting speed, feed rate, and depth of cut are all considered in the operation. This improves tool life while lowering production time and surface roughness. TOPSI and an orthogonal array are used to investigate the effects of input parameters on output parameters. In this work, S/N ratios are utilized to create a decision matrix, which is then utilized to convert a problem with multiple criteria for solving into a single-criteria issue using the TOPSIS approach. The results demonstrated that the strategy proposed is suitable for resolving multi-criteria process parameter enhancements. The best combination of process specifics was found to be 350 m/min cutting speed, 0.12 mm/rev feed rate, and 0.40 mm cut depth. </p>", "Keywords": "Surface roughness; Tool life; Production time; Optimization; TOPSIS", "DOI": "10.1007/s40745-021-00369-2", "PubYear": 2023, "Volume": "10", "Issue": "5", "JournalId": 6800, "JournalTitle": "Annals of Data Science", "ISSN": "2198-5804", "EISSN": "2198-5812", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Sarvepalli Radhakrishnan University, Bhopal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Sarvepalli Radhakrishnan University, Bhopal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Bansal Institute of Science and Technology, Bhopal, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "SMP Engineers and Electricals PVT. LTD, Nashik, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, <PERSON><PERSON><PERSON> College of Engineering and Technology, Nandayal, India"}], "References": [{"Title": "Multi-objective optimization of turning titanium-based alloy Ti-6Al-4V under dry, wet, and cryogenic conditions using gray relational analysis (GRA)", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "9-10", "Page": "3897", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A Two-Phase Multi-criteria Fuzzy Group Decision Making Approach for Supplier Evaluation and Order Allocation Considering Multi-objective, Multi-product and Multi-period", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "3", "Page": "577", "JournalTitle": "Annals of Data Science"}]}, {"ArticleId": 92455849, "Title": "Efficient and stable quorum-based log replication and replay for modern cluster-databases", "Abstract": "<p>The modern in-memory database (IMDB) can support highly concurrent on-line transaction processing (OLTP) workloads and generate massive transactional logs per second. Quorum-based replication protocols such as Paxos or Raft have been widely used in the distributed databases to offer higher availability and fault-tolerance. However, it is non-trivial to replicate IMDB because high transaction rate has brought new challenges. First, the leader node in quorum replication should have adaptivity by considering various transaction arrival rates and the processing capability of follower nodes. Second, followers are required to replay logs to catch up the state of the leader in the highly concurrent setting to reduce visibility gap. Third, modern databases are often built with a cluster of commodity machines connected by low configuration networks, in which the network anomalies often happen. In this case, the performance would be significantly affected because the follower node falls into the long-duration exception handling process (e.g., fetch lost logs from the leader). To this end, we build QuorumX, an efficient and stable quorum-based replication framework for IMDB under heavy OLTP workloads. QuorumX combines critical path based batching and pipeline batching to provide an adaptive log propagation scheme to obtain a stable and high performance at various settings. Further, we propose a safe and coordination-free log replay scheme to minimize the visibility gap between the leader and follower IMDBs. We further carefully design the process for the follower node in order to alleviate the influence of the unreliable network on the replication performance. Our evaluation results with the YCSB, TPC-C and a realistic microbenchmark demonstrate that QuorumX achieves the performance close to asynchronous primary-backup replication and could always provide a stable service with data consistency and a low-level visibility gap.</p>", "Keywords": "log replication; log replay; consensus protocol; high performance; high availability; quorum; unreliable network; packet loss", "DOI": "10.1007/s11704-020-0210-y", "PubYear": 2022, "Volume": "16", "Issue": "5", "JournalId": 4733, "JournalTitle": "Frontiers of Computer Science", "ISSN": "2095-2228", "EISSN": "2095-2236", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science and Engineering, East China Normal University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science and Engineering, East China Normal University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science and Engineering, East China Normal University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Data Science and Engineering, East China Normal University, Shanghai, China"}], "References": []}, {"ArticleId": 92456114, "Title": "Global self-optimizing control with active-set changes: A polynomial chaos approach", "Abstract": "Global self-optimizing control (gSOC) aims to identify optimal controlled variables (CVs) that minimize the average economic cost when uncertainties vary in the whole distribution space. Rigorous numerical optimization for the global optimal CVs is mainly hampered by the intensive computation load, hence only approximate solutions were previously developed to afford tractable computations. These approximations may however be large in many cases. This paper revisits this challenging problem within the nonlinear programming (NLP) framework for gSOC and proposes a sequential solution strategy. Within the solution method, the polynomial chaos expansion (PCE) is introduced for the sake of fast computations of the statistics of the cost function. We also handle the active-set change problem by satisfying chance constraints. To this end, PCEs of the constrained variables are also constructed, to formulate conditions that should be respected for the CVs. To determine the PCE coefficients, the sparse grid collocation method is adopted to further reduce the computation burden, as the dimension of uncertainty could be large for most gSOC problems. Three case studies are provided to show the new gSOC approach, all of which result in more accurate CVs, while maintaining tractable numerical optimizations.", "Keywords": "Real-time optimization ; Self-optimizing control ; Controlled variable ; Uncertainty ; Polynomial chaos", "DOI": "10.1016/j.compchemeng.2022.107662", "PubYear": 2022, "Volume": "159", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Huzhou University, Huzhou 313000, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Chemical and Biological Engineering, Zhejiang University, Hangzhou 310027, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemical and Biological Engineering, Zhejiang University, Hangzhou 310027, China"}], "References": []}, {"ArticleId": 92456182, "Title": "A Transformer-Based Model for Evaluation of Information Relevance in Online Social-Media: A Case Study of Covid-19 Media Posts", "Abstract": "<p>Online social media has become a major source of information gathering for a huge section of society. As the amount of information flows in online social media is enormous but on the other hand, the fact-checking sources are limited. This shortfall of fact-checking gives birth to the problem of misinformation and disinformation in the case of the truthfulness of facts on online social media which can have serious effects on the wellbeing of society. This problem of misconception becomes more rapid and critical when some events like the recent outbreak of Covid-19 happen when there is no or very little information is available anywhere. In this scenario, the identification of the content available online which is mostly propagated from person to person and not by any governing authority is very needed at the hour. To solve this problem, the information available online should be verified properly before being conceived by any individual. We propose a scheme to classify the online social media posts (Tweets) with the help of the BERT (Bidirectional Encoder Representations from Transformers)-based model. Also, we compared the performance of the proposed approach with the other machine learning techniques and other State of the art techniques available. The proposed model not only classifies the tweets as relevant or irrelevant, but also creates a set of topics by which one can identify a text as relevant or irrelevant to his/her need just by just matching the keywords of the topic. To accomplish this task, after the classification of the tweets, we apply a possible topic modelling approach based on latent semantic analysis and latent Dirichlet allocation methods to identify which of the topics are mostly propagated as false information.</p><p>© Ohmsha, Ltd. and Springer Japan KK, part of Springer Nature 2022.</p>", "Keywords": "BERT;Covid-19;Online social media;Text mining;Tweet classification", "DOI": "10.1007/s00354-021-00151-1", "PubYear": 2022, "Volume": "40", "Issue": "4", "JournalId": 6245, "JournalTitle": "New Generation Computing", "ISSN": "0288-3635", "EISSN": "1882-7055", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jaypee University of Engineering and Technology, Guna, India."}, {"AuthorId": 2, "Name": "Prateek Pandey", "Affiliation": "Department of Computer Science and Engineering, Jaypee University of Engineering and Technology, Guna, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, <PERSON><PERSON><PERSON><PERSON>kar University, Lucknow, India."}], "References": [{"Title": "Investigating mediated effects of fear of COVID-19 and COVID-19 misunderstanding in the association between problematic social media use, psychological distress, and insomnia", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "", "Page": "100345", "JournalTitle": "Internet Interventions"}, {"Title": "A deep learning-based social media text analysis framework for disaster resource management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Sentiment Analysis of COVID-19 tweets by Deep Learning Classifiers—A study to show how popularity is affecting accuracy in social media", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106754", "JournalTitle": "Applied Soft Computing"}, {"Title": "Would you notice if fake news changed your behavior? An experiment on the unconscious effects of disinformation", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "106633", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Exploiting discourse structure of traditional digital media to enhance automatic fake news detection", "Authors": "<PERSON>-<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114340", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A competing risks model based on latent Dirichlet Allocation for predicting churn reasons", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "146", "Issue": "", "Page": "113541", "JournalTitle": "Decision Support Systems"}, {"Title": "Sentiment analysis on the impact of coronavirus in social life using the BERT model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "33", "JournalTitle": "Social Network Analysis and Mining"}]}, {"ArticleId": 92456195, "Title": "Energy-Conserved Splitting Multidomain Legendre-Tau Spectral Method for Two Dimensional Maxwell’s Equations", "Abstract": "<p>In this paper, the energy-conserved splitting multidomain Legendre-tau Chebyshev-collocation spectral method is proposed for solving two dimensional <PERSON>’s equations. The method uses different degree polynomials to approximate the electric and magnetic fields respectively, and they can be decoupled in computation. Moreover, the error estimates are improved to the optimal order. In semi-discrete scheme, we apply the multidomain Legendre-tau Chebyshev-collocation spectral method. In fully discrete scheme, we use the energy-conserved splitting method in time step. The optimal error estimate is obtained for the homogeneous media. The multidomain spectral method is also applied to solve <PERSON>’s equations with discontinuous solutions. Numerical results indicate that the spectral accuracy is not affected by the discontinuity of solutions. With the multidomain method, the computation can be implemented in parallel.</p>", "Keywords": "<PERSON>’s equations; Multidomain spectral method; Optimal error estimates; Nonhomogeneous media; Energy-conserved splitting method", "DOI": "10.1007/s10915-021-01744-0", "PubYear": 2022, "Volume": "90", "Issue": "2", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Shanghai University, Shanghai, China;School of Computer Science and Technology, Shandong Institute of Business and Technology, Yantai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Shanghai University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Statistics, York University, Toronto, Canada"}], "References": []}, {"ArticleId": 92456246, "Title": "Compiler-assisted energy reduction of java real-time programs", "Abstract": "Optimizations in energy consumption are critical for battery-powered hard real-time embedded systems. Energy management is often accomplished by dynamic frequency scaling which requires compilers to insert special instructions in the code at appropriate places, which are then used by the operating system and the processor to calculate and change the frequency at these places in the code. The addition of code snippets for frequency change may affect the worst-case execution time of the program, which is a major concern for hard real-time systems. In addition to the above problem, run time calculation of the optimal frequency prohibits a compile-time estimate of the battery life by the system designers. In this paper, we present compiler optimizations for hard real-time Java applications executing on a time predictable Java processor, which reduces the energy consumption of the program while retaining the original worst-case execution time for the program intact. We also generate a compile-time frequency change schedule for the program resulting in compile-time estimates for worst-case energy consumption. We found that on average, reduction in energy consumption is better in run-time frequency schedule based mechanism but the compile-time frequency schedule allows us to get bounds on energy consumption at the compile-time.", "Keywords": "Real-time systems ; Embedded systems ; Processor ; Compiler ; Energy Management", "DOI": "10.1016/j.micpro.2022.104436", "PubYear": 2022, "Volume": "89", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical, Computer and Software Engineering, University of Auckland, Auckland, New Zealand;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical, Computer and Software Engineering, University of Auckland, Auckland, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical, Computer and Software Engineering, University of Auckland, Auckland, New Zealand"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical, Computer and Software Engineering, University of Auckland, Auckland, New Zealand"}], "References": []}, {"ArticleId": 92456251, "Title": "Statistical Validation of Cardiovascular Digital Biomarkers Towards Monitoring the Cardiac Risk in COPD: A Lyfas Case Study", "Abstract": "<p>Background: Mobile health (mHealth) is gaining popularity due to its pervasiveness. Lyfas is a smartphone-based optical biomarker instrument catering to mHealth. It captures the Pulse Rate Variability (PRV) and its associated digital biomarkers from the index finger capillary circulation using the principle of arterial photoplethysmography. PRV surrogates for the Cardiovascular Autonomic Modulation (CvAM) and provides a snapshot of psychophysiological homeostasis of the body. Objective: The paper investigates the roles of (a) physiological factors, e.g., Age, Duration of illness, Heart Rate (HR), Respiration Rate (RR), SpO2 level, and (b) popular digital biomarkers, such as SDNN, LF/HF, RMSSD, pNN50, SD1/SD2 to evaluate the cardiac risk. The paper hypothesizes that low FEV1, which is another physiological factor, plays a critical role in defining such risk. Method: A total of 50 males and females each, suffering from Chronic Obstructive Pulmonary Disease (COPD) took the Lyfas test after appropriate ethical measures. Data, thus collected by Lyfas had been statistically analyzed using histogram plots and Kolmogorov-Smirnov test for normality check, <PERSON>'s Correlations (PC) to measure the strength of associations, and linear regressions to test the goodness of fit of the model. Results: Positive PCs are noted between (a) RMSSD and SDNN ('very high'-females: 0.86 and males: 0.91), (b) pNN50 and RMSSD (PC: moderate 0.46), (c) pNN50 and SDNN (PC: moderate 0.44), (d) Duration of illness and Age ('high'-females: 0.71 and males: 0.77), and (e) Age and RR ('high'-females: 0.67, males: 0.53). Negative PC is noted between (a) LF/HF and FEV1 ('moderately high'-males 0.42) and (b) LF/HF and SpO2 ('moderately high'-males 0.30). Although the R2 values are not so encouraging (most are < 0.5), yet, the models are statistically significant (p-values 0.0336; CI 95%). Conclusion: The paper concludes that Lyfas may be used to predict the cardiac risk in COPD patients based on the LF/HF values correlated to SpO2 and FEV1 levels.</p>", "Keywords": "Lyfas, Pulse Rate Variability (PRV), Cardiac Autonomic Modulation (CAM), Cardiovascular Autonomic  Modulation (CvAM), digital biomarkers, home healthcare, physiological factors, COPD", "DOI": "10.37256/aie.**********", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 76759, "JournalTitle": "Artificial Intelligence Evolution", "ISSN": "2717-5944", "EISSN": "2717-5952", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Acculi Labs Pvt. Ltd, R. R. Nagar, Bangalore, Karnataka, 560098, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Acculi Labs Pvt. Ltd, R. R. Nagar, Bangalore, Karnataka, 560098, India"}], "References": []}, {"ArticleId": 92456253, "Title": "Circadian rhythms of locomotor activity in rats: Data on the effect of morphine administered from the early stages of embryonic development until weaning", "Abstract": "The circadian clock generates behavioural and physiological rhythms to maximize the efficacy of organismal functions. The circadian system with a major circadian pacemaker in the suprachiasmatic nucleus of the hypothalamus develops gradually and its proper function in adulthood depends on an appropriate neurochemical milieu during ontogeny [1] . Locomotor activity is under direct control by the circadian clock, and alterations in its rhythmicity indicate changes of circadian clock function. We evaluated circadian parameters of locomotor rhythms of adult male Wistar rats born to mothers that were exposed to a stable dose of 0.1 mg/ml of morphine in drinking water (36 ml water on average/day/each rat) from embryonic day 10 (E10) until weaning at postnatal day 28 (P28). Increasing the dose of morphine in drinking water was used to evaluate the changes in the rhythmic gene expression in the suprachiasmatic nucleus and in the livers of young rats at P20 [2] . At P90, we started measurement of endogenous rhythmicity for 12 days in constant darkness (DD), then we applied a 15 min light pulse at circadian time 15 (CT15) and followed the animals for the next 15 days in DD. We evaluated the magnitude of light-induced phase shift and compared the circadian parameters of free-running rhythmicity in the intervals before and after the light pulse. All data were also compared between morphine-exposed animals (M group) and controls (C group) that were not exposed to morphine. An unpaired t -test confirmed a significantly longer light-induced phase delay in M group compared with C group, a prolonged circadian period in M group in the interval after the light pulse, and greater amplitude for C group in the first interval, i.e. before the light pulse. No change in total activity counts between groups was confirmed.", "Keywords": "Circadian clock ; Rat ; Morphine ; Locomotor activity ; Development", "DOI": "10.1016/j.dib.2022.107812", "PubYear": 2022, "Volume": "40", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physiology, Faculty of Science, Charles University, Prague, Czech Republic"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physiology, Faculty of Science, Charles University, Prague, Czech Republic"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physiology, Faculty of Science, Charles University, Prague, Czech Republic"}, {"AuthorId": 4, "Name": "Zdeň<PERSON>", "Affiliation": "Department of Physiology, Faculty of Science, Charles University, Prague, Czech Republic;Corresponding author"}], "References": []}, {"ArticleId": 92456371, "Title": "Prototyping Feed-Forward Artificial Neural Network on Spartan 3S1000 FPGA for Blood Type Classification", "Abstract": "<p>In this research, a Feed-Forward Artificial Neural Network design was implemented on Xilinx Spartan 3S1000 Field Programable Gate Array using XSA-3S Board and prototyped blood type classification device. This research uses blood sample images as a system input. The system was built using VHSIC Hardware Description Language to describe the feed-forward propagation with a backpropagation neural network algorithm. We use three layers for the feed-forward ANN design with two hidden layers. The hidden layer designed has two neurons. In this study, the accuracy of detection obtained for four-type blood image resolutions results from 86%-92%, respectively.</p>", "Keywords": "Feed-Forward;Artificial;Neural-Network;FPGA;Xilinx;Spartan 3A;Blood type classification", "DOI": "10.25124/ijait.v5i01.3220", "PubYear": 2021, "Volume": "5", "Issue": "1", "JournalId": 51355, "JournalTitle": "IJAIT (International Journal of Applied Information Technology)", "ISSN": "", "EISSN": "2581-1223", "Authors": [{"AuthorId": 1, "Name": "Rizki Ardianto <PERSON>", "Affiliation": "Dept. of Electrical Engineering, Telkom University, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Diploma of Telecommunication Technology, Telkom University, Indonesia"}], "References": []}, {"ArticleId": 92456397, "Title": "CroLSSim: Cross‐language software similarity detector using hybrid approach of LSA‐based AST‐MDrep features and CNN‐LSTM model", "Abstract": "<p>Software similarity in different programming codes is a rapidly evolving field because of its numerous applications in software development, software cloning, software plagiarism, and software forensics. Currently, software researchers and developers search cross-language open-source repositories for similar applications for a variety of reasons, such as reusing programming code, analyzing different implementations, and looking for a better application. However, it is a challenging task because each programming language has a unique syntax and semantic structure. In this paper, a novel tool called Cross-Language Software Similarity (CroLSSim) is designed to detect similar software applications written in different programming codes. First, the Abstract Syntax Tree (AST) features are collected from different programming codes. These are high-quality features that can show the abstract view of each program. Then, Methods Description (MDrep) in combination with AST is used to examine the relationship among different method calls. Second, the Term Frequency Inverse Document Frequency approach is used to retrieve the local and global weights from AST-MDrep features. Third, the Latent Semantic Analysis-based features extraction and selection method is proposed to extract the semantic anchors in reduced dimensional space. Fourth, the Convolution Neural Network (CNN)-based features extraction method is proposed to mine the deep features. Finally, a hybrid deep learning model of CNN-Long-Short-Term Memory is designed to detect semantically similar software applications from these latent variables. The data set contains approximately 9.5K Java, 8.8K C#, and 7.4K C++ software applications obtained from GitHub. The proposed approach outperforms as compared with the state-of-the-art methods.</p>", "Keywords": "abstract syntax tree;data mining;deep learning;latent semantic analysis;software similarity", "DOI": "10.1002/int.22813", "PubYear": 2022, "Volume": "37", "Issue": "9", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Northwestern Polytechnical University Taicang Jiangsu 215400 China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of artificial intelligence Leshan Normal University Leshan Sichuan 614000 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology Zhoukou Normal University Zhoukou Henan 466000 China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science Middlesex University London NW4 4BT UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering IT and Environment at Charles Darwin University, Casuarina, Northern Territory 10095 Australia"}], "References": [{"Title": "A universal cross language software similarity detector for open source software categorization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "110491", "JournalTitle": "Journal of Systems and Software"}, {"Title": "An automated approach to assess the similarity of GitHub repositories", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "2", "Page": "595", "JournalTitle": "Software Quality Journal"}, {"Title": "An intelligent decision support system for software plagiarism detection in academia", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "6", "Page": "2730", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Multi-level graph neural network for text sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "92", "Issue": "", "Page": "107096", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Clone detection through srcClone: A program slicing based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "184", "Issue": "", "Page": "111115", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 92456414, "Title": "On the Effectiveness of Image Processing Based Malware Detection Techniques", "Abstract": "The number of cyberattack incidents is undeniably growing by the day, with malware attacks being the significant contributor. Whether it is from worms, Trojan horses, or ransomware, malware analysis and detection techniques have undoubtedly a vital role in protecting the cyber world. Due to the limitations and time-consuming nature of other static and dynamic analysis strategies, researchers have looked into image processing-based malware analysis and detection methodologies in the recent past. The executables are converted to grayscale or color images, and image processing techniques are being used to classify them into benign and malicious categories and across their corresponding families. This paper presents a detailed study on malware executable images and their effectiveness in detecting and classifying malicious samples, focusing on different executable to image conversion strategies, feature engineering approaches, and the classification models in use. A detailed insight on the performance overview and future perspective of various image-based malware detection methods is also presented.", "Keywords": "Malware ; malware detection ; malware visualization ; image processing ; survey", "DOI": "10.1080/01969722.2021.2020471", "PubYear": 2022, "Volume": "53", "Issue": "7", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Calicut, Kozhikode, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Calicut, Kozhikode, India"}], "References": [{"Title": "A novel method for malware detection on ML-based visualization technique", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "101682", "JournalTitle": "Computers & Security"}, {"Title": "Byte-level malware classification based on markov images and deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101740", "JournalTitle": "Computers & Security"}, {"Title": "Malware classification using compact image features and multiclass support vector machines", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "4", "Page": "419", "JournalTitle": "IET Information Security"}, {"Title": "IMCFN: Image-based malware classification using fine-tuned convolutional neural network architecture", "Authors": "Danish Vasan; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "107138", "JournalTitle": "Computer Networks"}, {"Title": "Assessing and Improving Malware Detection Sustainability through App Evolution Studies", "Authors": "Haipeng <PERSON>ai", "PubYear": 2020, "Volume": "29", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "Deep learning for image-based mobile malware detection", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "2", "Page": "157", "JournalTitle": "Journal of Computer Virology and Hacking Techniques"}, {"Title": "Multiclass malware classification via first- and second-order texture statistics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "101895", "JournalTitle": "Computers & Security"}, {"Title": "Automated malware identification method using image descriptors and singular value decomposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "7", "Page": "10881", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Data augmentation and transfer learning to classify malware images in a deep learning context", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "4", "Page": "279", "JournalTitle": "Journal of Computer Virology and Hacking Techniques"}]}, {"ArticleId": 92456445, "Title": "MS-CNN: multiscale recognition of building rooftops from high spatial resolution remote sensing imagery", "Abstract": "The effective recognition and precise positioning of multiscale building rooftop is one of the key scientific problems that have yet to be resolved urgently in the current implementation of high-resolution remote sensing. In recent years, the automatic recognition of high-resolution image targets often employs convolutional neural networks to extract features. However, such traditional methods often ignore multiscale features of geographical objects, while lacking effective multiscale information extraction strategies. By utilizing the feature learning capability of deep neural networks, this study proposes a multiscale convolutional neural network named MS-CNN to recognize building rooftops from high-resolution remote sensing imagery. In addition, this study constructs a pedigree deep learning sample library based on the remote sensing Tupu theory that considers the spectral and geometric characteristics of building rooftops. Able to utilize feature segmentation mechanism and fusion enhancement strategy, MS-CNN enriches the receptive fields obtained by each convolution layer. The proposed network of this study is also compared with the famous Mask R-CNN method, proving the relative advantages of the MS-CNN method with multiscale characteristics. The experimental results show that the precision and recall metrics of the MS-CNN are 4.18% (.8655 vs. .8238) and 5.71% (.8380 vs. .7809) higher than those of the Mask R-CNN, respectively. The proposed method has been deployed in practical engineering projects in Vietnam and Myanmar, etc.", "Keywords": "multiscale recognition ; building rooftop ; convolutional neural network ; high-resolution remote sensing imagery", "DOI": "10.1080/01431161.2021.2018146", "PubYear": 2022, "Volume": "43", "Issue": "1", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Geomatics and Urban Spatial Information, Beijing University of Civil Engineering and Architecture, Beijing, People’s Republic of China;First Branch of Basic Mapping, Beijing Institute of Surveying and Mapping, Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Geomatics and Urban Spatial Information, Beijing University of Civil Engineering and Architecture, Beijing, People’s Republic of China;Key Laboratory for Urban Geomatics of National Administration of Surveying; College of Geomatics and Urban Spatial Information, Beijing University of Civil Engineering and Architecture, Beijing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Geomatics and Urban Spatial Information, Beijing University of Civil Engineering and Architecture, Beijing, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Geomatics and Urban Spatial Information, Beijing University of Civil Engineering and Architecture, Beijing, People’s Republic of China"}], "References": [{"Title": "SO–CNN based urban functional zone fine division with VHR remote sensing image", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> Lv", "PubYear": 2020, "Volume": "236", "Issue": "", "Page": "111458", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Scale Sequence Joint Deep Learning (SS-JDL) for land use and land cover classification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111593", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A deep residual learning serial segmentation network for extracting buildings from remote sensing imagery", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "14", "Page": "5573", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Building extraction from VHR remote sensing imagery by combining an improved deep convolutional encoder-decoder architecture and historical land use vector map", "Authors": "Wen<PERSON> Feng; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "17", "Page": "6595", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Learning to extract buildings from ultra-high-resolution drone images and noisy labels", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "21", "Page": "8216", "JournalTitle": "International Journal of Remote Sensing"}]}, {"ArticleId": 92456492, "Title": "Analysis of Reactive Power Control Using Battery Energy Storage Systems for a Real Distribution Feeder", "Abstract": "<p>Following the dissemination of distributed photovoltaic generation, the operation of distribution grids is changing due to the challenges, mainly overvoltage and reverse power flow, arising from the high penetration of such sources. One way to mitigate such effects is using battery energy storage systems (BESSs), whose technology is experiencing rapid development. In this context, this work studies the influence that the reactive power control dispatched from BESS can have on a real distribution feeder considering its original configuration as well as a load transfer scenario. The studied feeder supplies a soccer stadium (Mineirão), which has on its roof a photovoltaic generation plant of 1320 kWp, and the Federal University of Minas Gerais Campus—UFMG, where two BESSs with a total capacity of 1150 kVA/1750 kWh are yet to be installed. The aim of the analysis is to validate the use of active and reactive power injection provided by BESS in controlling the feeder losses and voltage profile. The methodology consists of analyzing typical load curves obtained from feeder measurement data and carrying out simulations considering the BESS injections. The power flow simulations are done using OpenDSS focusing in analyzing the influence of BESS injections. Specifically, the BESSs are used for power factor control, volt–VAR control and power factor correction, whereas the analyses are devoted to the feeder performance, namely losses and voltage profile. </p>", "Keywords": "Battery energy storage systems; Distribution systems; Reactive power control; Volt–VAR control", "DOI": "10.1007/s40313-021-00877-9", "PubYear": 2022, "Volume": "33", "Issue": "4", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Souza", "Affiliation": "Concert Technologies, Belo Horizonte, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "FITec - Fundação para Inovações Tecnológicas, Belo Horizonte, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "FITec - Fundação para Inovações Tecnológicas, Belo Horizonte, Brazil"}, {"AuthorId": 4, "Name": "Wallace do Couto Boaventura", "Affiliation": "Programa de Pós-Graduação em Engenharia Elétrica, Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Companhia Energética de Minas Gerais - CEMIG, Belo Horizonte, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Companhia Energética de Minas Gerais - CEMIG, Belo Horizonte, Brazil"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Companhia Energética de Minas Gerais - CEMIG, Belo Horizonte, Brazil"}], "References": [{"Title": "Optimal Siting and Sizing of Battery Energy Storage System for Distribution Loss Reduction Based on Meta-heuristics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "6", "Page": "1469", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}]}, {"ArticleId": 92456602, "Title": "Differential evolution-assisted salp swarm algorithm with chaotic structure for real-world problems", "Abstract": "<p>There is a new nature-inspired algorithm called salp swarm algorithm (SSA), due to its simple framework, it has been widely used in many fields. But when handling some complicated optimization problems, especially the multimodal and high-dimensional optimization problems, SSA will probably have difficulties in convergence performance or dropping into the local optimum. To mitigate these problems, this paper presents a chaotic SSA with differential evolution (CDESSA). In the proposed framework, chaotic initialization and differential evolution are introduced to enrich the convergence speed and accuracy of SSA. Chaotic initialization is utilized to produce a better initial population aim at locating a better global optimal. At the same time, differential evolution is used to build up the search capability of each agent and improve the sense of balance of global search and intensification of SSA. These mechanisms collaborate to boost SSA in accelerating convergence activity. Finally, a series of experiments are carried out to test the performance of CDESSA. Firstly, IEEE CEC2014 competition fuctions are adopted to evaluate the ability of CDESSA in working out the real-parameter optimization problems. The proposed CDESSA is adopted to deal with feature selection (FS) problems, then five constrained engineering optimization problems are also adopted to evaluate the property of CDESSA in dealing with real engineering scenarios. Experimental results reveal that the proposed CDESSA method performs significantly better than the original SSA and other compared methods.</p><p>© The Author(s), under exclusive licence to Springer-Verlag London Ltd., part of Springer Nature 2022.</p>", "Keywords": "Chaotic initialization;Engineering optimization problems;Feature selection;Global optimization;Salp swarm algorithm", "DOI": "10.1007/s00366-021-01545-x", "PubYear": 2023, "Volume": "39", "Issue": "3", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Artificial Intelligence, Wenzhou University, Wenzhou, 325035 China."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, Changchun, 130012 China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Lixin University of Accounting and Finance, Shanghai, 201209 China."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Artificial Intelligence, Wenzhou University, Wenzhou, 325035 China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Wenzhou Polytechnic, Wenzhou, 325035 China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Department of Computer Science and Artificial Intelligence, Wenzhou University, Wenzhou, 325035 China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The First Affiliated Hospital of Wenzhou Medical University, Wenzhou, 325000 People's Republic of China."}], "References": [{"Title": "Weighted differential evolution algorithm for numerical function optimization: a comparative study with cuckoo search, artificial bee colony, adaptive differential evolution, and backtracking search optimization algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "8", "Page": "3923", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Efficient multi-population outpost fruit fly-driven optimizers: Framework and advances in support vector machines", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "112999", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Harmonized salp chain-built optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "1049", "JournalTitle": "Engineering with Computers"}, {"Title": "An efficient double adaptive random spare reinforced whale optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "113018", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An enhanced Bacterial Foraging Optimization and its application for training kernel extreme learning machine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105884", "JournalTitle": "Applied Soft Computing"}, {"Title": "Slime mould algorithm: A new method for stochastic optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "300", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Multi-population differential evolution-assisted Harris hawks optimization: Framework and case studies", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "175", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A novel lifetime scheme for enhancing the convergence performance of salp swarm algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "181", "JournalTitle": "Soft Computing"}, {"Title": "A multi-strategy enhanced salp swarm algorithm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1177", "JournalTitle": "Engineering with Computers"}, {"Title": "Dynamic Salp swarm algorithm for feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113873", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Ensemble mutation-driven salp swarm algorithm with restart mechanism: Framework and fundamental analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113897", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Application of mutation operators to salp swarm algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114368", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Chaos-assisted multi-population salp swarm algorithms: Framework and case studies", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114369", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Modified salp swarm algorithm for global optimisation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "14", "Page": "8709", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An improved version of salp swarm algorithm for solving optimal power flow problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "5", "Page": "4027", "JournalTitle": "Soft Computing"}, {"Title": "Oppositional salp swarm algorithm with mutation operator for global optimization and application in training higher order neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "28-29", "Page": "35415", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Improving high-impact bug report prediction with combination of interactive machine learning and active learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "133", "Issue": "", "Page": "106530", "JournalTitle": "Information and Software Technology"}, {"Title": "Improved Salp Swarm Algorithm with mutation schemes for solving global optimization and engineering problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S5", "Page": "3927", "JournalTitle": "Engineering with Computers"}, {"Title": "IWOSSA: An improved whale optimization salp swarm algorithm for solving optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "176", "Issue": "", "Page": "114901", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An improved salp swarm algorithm for complex multi-modal problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "15", "Page": "10441", "JournalTitle": "Soft Computing"}, {"Title": "HSSAGA: Designation and scheduling of nurses for taking care of COVID-19 patients using novel method of Hybrid Salp Swarm Algorithm and Genetic Algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107449", "JournalTitle": "Applied Soft Computing"}, {"Title": "Advancement of the search process of salp swarm algorithm for global optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115292", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 92457057, "Title": "CacheHawkeye: Detecting Cache Side Channel Attacks Based on Memory Events", "Abstract": "<p>Cache side channel attacks, as a type of cryptanalysis, seriously threaten the security of the cryptosystem. These attacks continuously monitor the memory addresses associated with the victim’s secret information, which cause frequent memory access on these addresses. This paper proposes CacheHawkeye, which uses the frequent memory access characteristic of the attacker to detect attacks. CacheHawkeye monitors memory events by CPU hardware performance counters. We proved the effectiveness of CacheHawkeye on Flush+Reload and Flush+Flush attacks. In addition, we evaluated the accuracy of CacheHawkeye under different system loads. Experiments demonstrate that CacheHawkeye not only has good accuracy but can also adapt to various system loads.</p>", "Keywords": "cryptanalysis; side channel; Flush+Reload; cache side channel; Flush+Flush; side channel detection cryptanalysis ; side channel ; Flush+Reload ; cache side channel ; Flush+Flush ; side channel detection", "DOI": "10.3390/fi14010024", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "Hui Yan", "Affiliation": "Institutes of Physical Science and Information Technology, Anhui University, Hefei 230601, China↑Institutes of Intelligent Machines, Hefei Institutes of Physical Sciences, Chinese Academy of Sciences, Hefei 230031, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institutes of Intelligent Machines, Hefei Institutes of Physical Sciences, Chinese Academy of Sciences, Hefei 230031, China↑Author to whom correspondence should be addressed"}], "References": [{"Title": "FLUSH + PREFETCH: A countermeasure against access-driven cache-based side-channel attacks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "101698", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Winter is here! A decade of cache-based side-channel attacks, detection & mitigation for RSA", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101524", "JournalTitle": "Information Systems"}]}, {"ArticleId": 92457451, "Title": "Real‐time data‐driven PID controller for multivariable process employing deep neural network", "Abstract": "<p>The complex industrial processes exhibiting nonstationary and multivariable with time-varying dynamics result in low accuracy. Also, stability compensation is difficult to be obtained by a conventional PID controller. Hence, a deep learning-based data-driven PID controller is designed for unmodeled dynamics compensation for complex industrial processes. In this research work, a nonlinear PID controller is designed with a deep neural network (DNN) model from unmodeled dynamics of the complex industrial processes. To validate the performance, results from stability compensation and convergence of the model parameters for closed-loop systems were obtained. When tested on a real-time twin tank system, it achieved an accurate output flowrate with 97.65% accuracy and 1.89% peak overshoot compared with conventional PID controller. Both simulated and experimental results validate that proposed controller has improved stability and uniform convergence of system variables. The proposed deep learning-based PID controller was employed on a twin tank control system. This confirms the feasibility and practical application of a real-time complex process.</p>", "Keywords": "closed-loop control;data-driven PID;deep learning algorithm;twin tank control;unmodeled dynamics", "DOI": "10.1002/asjc.2713", "PubYear": 2022, "Volume": "24", "Issue": "6", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "Pandia <PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering Mepco Schlenk Engineering College  Sivakasi India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering Mepco Schlenk Engineering College  Sivakasi India"}], "References": []}, {"ArticleId": 92457569, "Title": "Cloud/shadow segmentation based on multi-level feature enhanced network for remote sensing imagery", "Abstract": "In the application of remote sensing, cloud blocking brings trouble to the analysis of surface parameters and atmospheric parameters. Due to the complexity of the background, the influence of some cloud-like interferences (such as ice, snow, buildings, etc.) and the complexity of the cloud shape, the traditional deep learning method is difficult to segment the edge information of cloud and cloud shadow accurately, resulting in misjudgement at the edge. In order to solve these problems, a multilevel feature enhanced network is proposed for cloud/shadow segmentation. In this work, ResNet-18 is used as the backbone to extract all levels of semantic information, and Feature Enhancement Module is proposed to strengthen the feature information to obtain more effective feature information. Multiscale Fusion module is constructed to fuses multiscale features of deep information to obtain global feature information while considering local feature information. Finally, through the Feature Guidance module, low-level features are used to guide the high-level features to guide the recovery of spatial information and improve the efficiency of upsampling. On the data collected by Landsat-8, Sentinel-2, and HRC_WHU data set, the experimental results show that this method is superior to the existing methods.", "Keywords": "Cloud/shadow segmentation ; Remote-sensing images ; Feature enhanced network ; Deep learning", "DOI": "10.1080/01431161.2021.2014077", "PubYear": 2022, "Volume": "43", "Issue": "15-16", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Collaborative Innovation Center on Atmospheric Environment and Equipment Technology, Nanjing University of Information Science and Technology, Nanjing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Jiangsu Collaborative Innovation Center on Atmospheric Environment and Equipment Technology, Nanjing University of Information Science and Technology, Nanjing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory LIESMARS, Wuhan University, Wuhan, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Collaborative Innovation Center on Atmospheric Environment and Equipment Technology, Nanjing University of Information Science and Technology, Nanjing, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Collaborative Innovation Center on Atmospheric Environment and Equipment Technology, Nanjing University of Information Science and Technology, Nanjing, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Lin", "Affiliation": "College of Information Science and Technology, Nanjing Forestry University, Nanjing, People’s Republic of China"}], "References": [{"Title": "Cloud detection algorithm for multi-modal satellite imagery using convolutional neural-networks (CNN)", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111446", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Non-intrusive load disaggregation based on composite deep long short-term memory network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "113669", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Cloud/shadow segmentation based on global attention feature fusion residual network for remote sensing imagery", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "6", "Page": "2022", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "BiSeNet V2: Bilateral Network with Guided Aggregation for Real-Time Semantic Segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "11", "Page": "3051", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Strip pooling channel spatial attention network for the segmentation of cloud and cloud shadow", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "104940", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 92457669, "Title": "Artificial bee colony algorithm with bi‐coordinate systems for global numerical optimization", "Abstract": "<p>As an effective global optimization technique, artificial bee colony (ABC) algorithm has become one of the hottest research topics in the fields of evolutionary algorithms. However, the solution search equation is not rotationally invariant, which causes the problem that the performance of ABC is sensitive to the coordinate system. Although many improved ABC variants have been developed, they rarely considered the problem. Hence, to solve the problem, we propose a new ABC variant with bi-coordinate systems (BSABC), including the original coordinate system and the eigen coordinate system. The two coordinate systems own different characteristics: (1) the former one aims to maintain the population diversity, and (2) the latter one is to adapt the search to the fitness landscape of the problems. Based on the characteristics, in the BSABC, the two coordinate systems are used in the employed bee phase and onlooker bee phase, respectively. Meanwhile, two new solution search equations are designed by utilizing the elite information, and they are respectively performed in the two coordinate systems to further improve the algorithm performance. As another contribution of this study, in the scout bee phase, the multivariate Gaussian distribution is constructed to replace the original method to generate offspring, which is helpful to save the search experience. The performance of the BSABC is verified by extensive experiments on the CEC2013 test suite and one real-world optimization problem, and four well-established ABC variants and three other evolutionary algorithms are included in the performance comparison. The comparison results confirm that the BSABC shows competitive performance by achieving better results on the majority of test functions.</p>", "Keywords": "artificial bee colony;bi-coordinate systems;eigen coordinate system;elite information;multivariate Gaussian distribution", "DOI": "10.1002/int.22816", "PubYear": 2022, "Volume": "37", "Issue": "9", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering Jiangxi Normal University Nanchang China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering Jiangxi Normal University Nanchang China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering Jiangxi Normal University Nanchang China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering Jiangxi Normal University Nanchang China"}], "References": [{"Title": "Hybrid sine cosine artificial bee colony algorithm for global optimization and image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "13", "Page": "9521", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A multi-strategy fusion artificial bee colony algorithm with small population", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "112921", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-objective feature selection based on artificial bee colony: An acceleration approach with variable sample size", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106041", "JournalTitle": "Applied Soft Computing"}, {"Title": "Improving artificial Bee colony algorithm using a new neighborhood selection mechanism", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "527", "Issue": "", "Page": "227", "JournalTitle": "Information Sciences"}, {"Title": "Enhancing artificial bee colony algorithm with multi-elite guidance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "543", "Issue": "", "Page": "242", "JournalTitle": "Information Sciences"}, {"Title": "A particle swarm optimization algorithm for mixed-variable optimization problems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100808", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A survey on the Artificial Bee Colony algorithm variants for binary, integer and mixed integer programming problems", "Authors": "Bahr<PERSON>ye <PERSON>; <PERSON><PERSON>; Beyza Go<PERSON>emli", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107351", "JournalTitle": "Applied Soft Computing"}, {"Title": "African vultures optimization algorithm: A new nature-inspired metaheuristic algorithm for global optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107408", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Improved strength Pareto evolutionary algorithm based on reference direction and coordinated selection strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "9", "Page": "4693", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "IEGA: An improved elitism‐based genetic algorithm for task scheduling problem in fog computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "9", "Page": "4592", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Hysteresis compensation and adaptive control based evolutionary neural networks for piezoelectric actuator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "10", "Page": "5472", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A parallel learning particle swarm optimizer for inverse kinematics of robotic manipulator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "10", "Page": "6101", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Artificial gorilla troops optimizer: A new nature‐inspired metaheuristic algorithm for global optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "10", "Page": "5887", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Artificial bee colony algorithm based on multiple neighborhood topologies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107697", "JournalTitle": "Applied Soft Computing"}, {"Title": "Remora optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115665", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 92457788, "Title": "Hyperspherical class prototypes for adversarial robustness", "Abstract": "This work addresses the problem of adversarial robustness in deep neural network classification from an optimal class boundary estimation perspective. It is argued that increased model robustness to adversarial attacks can be achieved when the feature learning process is monitored by geometrically-inspired optimization criteria. To this end, we propose to learn hyperspherical class prototypes in the neural feature embedding space, along with training the network parameters. Three concurrent optimization functions for the intermediate hidden layer training data activations are devised, requiring items of the same class to be enclosed by the corresponding class prototype boundaries, to have minimum distance from their class prototype vector (i.e., hypersphere center) and to have maximum distance from the remainder hypersphere centers. Our experiments show that training standard classification model architectures with the proposed objectives, significantly increases their robustness to white-box adversarial attacks, without adverse (if not beneficial) effects to their classification accuracy.", "Keywords": "Adversarial defense ; Adversarial robustness ; Hypersphere prototype loss ; HCP loss", "DOI": "10.1016/j.patcog.2022.108527", "PubYear": 2022, "Volume": "125", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Aristotle University of Thessaloniki, Thessaloniki, Greece;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Aristotle University of Thessaloniki, Thessaloniki, Greece"}], "References": [{"Title": "Surrogate network-based sparseness hyper-parameter optimization for deep expression recognition", "Authors": "<PERSON><PERSON> Xie; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107701", "JournalTitle": "Pattern Recognition"}, {"Title": "Robust feature learning for adversarial defense via hierarchical feature alignment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "256", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 92458071, "Title": "GPU-enabled microfluidic design automation for concentration gradient generators", "Abstract": "<p>A GPU-enabled design framework is presented to automate the global optimization process of microfluidic concentration gradient generators (µCGGs). The optimization finds operational parameters (inlet concentrations and pressures) of CGGs to produce desired/prescribed concentration gradient (CG) profiles. To enhance optimization speed, the physics-based component model (PBCM) in the closed form is employed for simulation in lieu of the expensive CFD model. A genetic algorithm (GA) including the migration to mitigate the pre-maturation issue is developed. A new approach to include a penalty term that minimizes pressure non-uniformity in CGGs and the chance of violating physical assumptions used by PBCM is proposed. The entire process of PBCM evaluation and GA optimization is implemented on the GPU platform to utilize its massive computing parallelization. Two different laminar flow diffusion-based microfluidic CGGs: triple-Y and double-Ψ are used to verify the framework. Various shapes of desired CGs are examined for triple-Y and double-Ψ, and the average mean relative errors between the optimal designs and desired CGs are found to be 3.85% and 3.97%, respectively. The optimization is completed within 150 s on a GPGPU workstation and within 25 min on a GPU-embedded, small form-factor edge computing device, leading to about 130 × and 11–12 × speedup over the CPU process, respectively. The present research for the first time demonstrates the potential of applying microfluidic design automation on the edge-computing platform in laboratory environments.</p>", "Keywords": "Concentration gradient generator; CUDA; GPGPU; Microfluidics; Genetic algorithm", "DOI": "10.1007/s00366-021-01548-8", "PubYear": 2023, "Volume": "39", "Issue": "2", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of South Carolina, Columbia, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of South Carolina, Columbia, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of South Carolina, Columbia, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of South Carolina, Columbia, USA"}], "References": []}, {"ArticleId": 92458076, "Title": "Prediction of vibration responses in a reciprocating compressor interstage piping system using the modal expansion method", "Abstract": "<p>Reciprocating compressors are sources of vibration excitation in hydrotreating units used in the oil refinement industry. Due to their operating principle, these compressors can produce high levels of vibration in the pipes connected to it, which can affect their structural integrity and life span. Continuous vibration monitoring can be a difficult task due to the limited number of sensors and poor accessibility of the places with higher risk of failures. In this work, a methodology is proposed for predicting vibration responses in non-instrumented locations. For this, an output-only modal analysis of the interstage system was carried out and a finite element model of the system was developed to calibrate the numerical mode shapes or, in other words, to smooth the experimental mode shapes. The System Equivalent Reduction Expansion Process (SEREP) was used to reduce the numerical degrees of freedom and the Local Correspondence Principle for Modes and Coordinates (LCMC) was used in the smoothing process. Finally, the smoothed version of the experimental modal matrix was expanded using the modal expansion method and used to predict the vibration responses. Results showed high accuracy between the measured vibration data and the predicted ones.</p>", "Keywords": "FE model reduction; Modal expansion; Output-Only Modal Analysis (O-OMA); Vibration prediction; Compressor pipe", "DOI": "10.1007/s00170-021-08490-5", "PubYear": 2022, "Volume": "119", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Basic Engineering, Rio de Janeiro/RJ, Brasil"}, {"AuthorId": 2, "Name": "Ulisses <PERSON>", "Affiliation": "Laboratory of Dynamic Tests and Vibration Analysis (LEDAV), Ocean Engineering Program (PENO), Federal University of Rio de Janeiro (UFRJ), Rio de Janeiro/RJ, Brasil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratory of Dynamic Tests and Vibration Analysis (LEDAV), Ocean Engineering Program (PENO), Federal University of Rio de Janeiro (UFRJ), Rio de Janeiro/RJ, Brasil"}, {"AuthorId": 4, "Name": "<PERSON>iz <PERSON>", "Affiliation": "Laboratory of Dynamic Tests and Vibration Analysis (LEDAV), Ocean Engineering Program (PENO), Federal University of Rio de Janeiro (UFRJ), Rio de Janeiro/RJ, Brasil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Basic Engineering, Rio de Janeiro/RJ, Brasil"}, {"AuthorId": 6, "Name": "Ediberto B. Tin<PERSON>o", "Affiliation": "Basic Engineering, Rio de Janeiro/RJ, Brasil"}], "References": [{"Title": "New Paradigm of Data-Driven Smart Customisation through Digital Twin", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "270", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 92458081, "Title": "An efficient orthogonal opposition-based learning slime mould algorithm for maximum power point tracking", "Abstract": "<p>The slime mould algorithm (SMA) is a recent physics-based optimization approach. The main inspiration of the SMA is motivated by the natural oscillating state of the slime mould organisms. In order to boost the performance, several problems must be resolved properly on the original SMA itself. One of these problems is the dilemma of the improper balancing between the exploration and exploitation phases which might deviate the algorithm to be trapped in the local optima. This work introduces a new version of the SMA called mSMA-based on the hybridization of the original SMA with a modified version of the opposition-based learning (mOBL) and the Orthogonal learning (OL) strategies. To assess the performance of the proposed mSMA, it has been evaluated over ten CEC’2020 test suites and three engineering design problems. As the output performance of the thermoelectric generator (TEG) is mainly based on the applied temperatures on the hot and cold sides of the TEG together with the load value. Consequently, in case of either varying the applied temperature or the load, to force the TEG to operate as close as possible to the maximum power point (MPP), a robust maximum power point tracking (MPPT) strategy is highly required. Therefore, an optimized fractional-order (FO) MPPTS is proposed to increase the delivered energy from the TEG. The suggested strategy is based on the FO control approach. The optimal parameters of the optimized fractional MPPTS were identified by the new mSMA. To demonstrate the superiority of mSMA, the results are compared to other well-known algorithms such as the ABC, GSA, PSO, HHO, TSA, GBO, HBO, and the original SMA. The main purpose of the proposed optimal fractional MPPTS is to increase the dynamic response and to remove the oscillations that occurred at the steady-state response. Therefore, the performance of the proposed strategy is compared to two common methods; the incremental resistance and the perturb & observe. The obtained results proved the superiority of the optimized fractional MPPTS in comparison to the other traditional MPPT methods in both the dynamic and steady-state responses.</p>", "Keywords": "Slime mould algorithm (SMA); Opposition-based learning (OBL); Orthogonal learning (OL); Meta-heuristic algorithms; Thermoelectric generator; Maximum power point tracking (MPPT)", "DOI": "10.1007/s00521-021-06634-y", "PubYear": 2022, "Volume": "34", "Issue": "5", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computers and Information, Minia University, Minia, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computers and Artificial Intelligence, Beni-Suef University, Bemi-Suef, Egypt"}, {"AuthorId": 3, "Name": "Hegazy Rezk", "Affiliation": "College of Engineering at <PERSON><PERSON>, Prince <PERSON> University, Al-Kharj, Saudi Arabia;Electrical Engineering Department, Faculty of Engineering, Minia University, Minia, Egypt"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Engineering at <PERSON><PERSON>, Prince <PERSON> University, Al-Kharj, Saudi Arabia;Computers and Automatic Control Engineering Department, Faculty of Engineering, Tanta University, Tanta, Egypt"}], "References": [{"Title": "Tunicate Swarm Algorithm: A new bio-inspired based metaheuristic paradigm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103541", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Improving ant colony optimization algorithm with epsilon greedy and <PERSON> flight", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "4", "Page": "1711", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Slime mould algorithm: A new method for stochastic optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "300", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A memory-based Grey Wolf Optimizer for global optimization tasks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106367", "JournalTitle": "Applied Soft Computing"}, {"Title": "Gradient-based optimizer: A new metaheuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "540", "Issue": "", "Page": "131", "JournalTitle": "Information Sciences"}, {"Title": "Chaotic oppositional sine–cosine method for solving global optimization problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1223", "JournalTitle": "Engineering with Computers"}, {"Title": "Heap-based optimizer inspired by corporate rank hierarchy for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113702", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Archimedes optimization algorithm: a new metaheuristic algorithm for solving optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1531", "JournalTitle": "Applied Intelligence"}, {"Title": "An improved Manta ray foraging optimizer for cost-effective emission dispatch problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "104155", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An efficient Manta Ray Foraging Optimization algorithm for parameter extraction of three-diode photovoltaic model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "94", "Issue": "", "Page": "107304", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Development and application of evaporation rate water cycle algorithm for optimal coordination of directional overcurrent relays", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115538", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 92458082, "Title": "RIECNN: real-time image enhanced CNN for traffic sign recognition", "Abstract": "<p>Traffic sign recognition plays a crucial role in the development of autonomous cars to reduce the accident rate and promote road safety. It has been a necessity to address traffic signs that are affected significantly by the environment as well as poor real-time performance for deep-learning state-of-the-art algorithms. In this paper, we introduce Real-Time Image Enhanced CNN (RIECNN) for Traffic Sign Recognition. RIECNN is a real-time, novel approach that tackles multiple, diverse traffic sign datasets, and out-performs the state-of-the-art architectures in terms of recognition rate and execution time. Experiments are conducted using the German Traffic Sign Benchmark (GTSRB), the Belgium Traffic Sign Classification (BTSC), and the Croatian Traffic Sign (rMASTIF) benchmark. Experimental results show that our approach has achieved the highest recognition rate for all Benchmarks, achieving a recognition accuracy of 99.75% for GTSRB, 99.25% for BTSC and 99.55% for rMASTIF. In terms of latency and meeting the real-time constraint, the pre-processing time and inference time together do not exceed 1.3 ms per image. Not only have our proposed approach achieved remarkably high accuracy with real-time performance, but it also demonstrated robustness against traffic sign recognition challenges such as brightness and contrast variations in the environment.</p>", "Keywords": "Traffic sign recognition; Deep learning; Convolutional neural networks; Autonomous cars", "DOI": "10.1007/s00521-021-06762-5", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Cairo University, Cairo, Egypt"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Engineering Department, Cairo University, Cairo, Egypt"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computer Engineering Department, Cairo University, Cairo, Egypt"}], "References": []}, {"ArticleId": 92458100, "Title": "Deep representation learning for face hallucination", "Abstract": "<p>Recently, deep learning, as a novel emerging algorithm, offers an end-to-end effective paradigm for super-resolution. Various successful practices with the deep learning model have confirmed the truth that deeper features always bring better performance. In this paper, we present a novel deep representation learning framework for face hallucination to verify the “coarse-to-fine” nature of deep features. The proposed framework includes the optimization of deep representation coefficients and the updating of deep dictionary learning. First, local and nonlocal patches are used to enrich the self-similarity prior to local to global optimization. Then a unified regularization term is added into the representation objective function to fully exploit accurate prior. Deeply coupled multi-layer dictionaries are developed to support the deep representation scheme as refining the high-resolution image from coarse to fine layer-by-layer. Finally, residual recursive learning is combined into a deep representation framework for boosting the reconstruction performances. Different from neural network’s deep feature learning manner, the proposed method provides a novel explanation of how deep representation works. Extensive experiments are conducted on FEI, CAS-PEAL-R1, and LFW databases to testify its subjective and objective performance. Experimental results demonstrate that the proposed approach outperforms some state-of-the-art face hallucination methods, including the method based on convolution neural network and the method based on vanilla representation.</p>", "Keywords": "Deep representation; Deep dictionary updating; Residual recursive learning; Local and nonlocal patches", "DOI": "10.1007/s11042-021-11648-8", "PubYear": 2022, "Volume": "81", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Tao Lu", "Affiliation": "Hubei Province Key Laboratory of Intelligent Robot, School of Computer Science and Engineering, Wuhan Institute of Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hubei Province Key Laboratory of Intelligent Robot, School of Computer Science and Engineering, Wuhan Institute of Technology, Wuhan, China;School of General Aviation, Jingchu University of Technology, Jingmen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of IoT Engineering (School of Information Security), Jiangsu Vocational College Of Information Technology, Wuxi, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hubei Province Key Laboratory of Intelligent Robot, School of Computer Science and Engineering, Wuhan Institute of Technology, Wuhan, China"}, {"AuthorId": 5, "Name": "Wenhua Fang", "Affiliation": "Hubei Province Key Laboratory of Intelligent Robot, School of Computer Science and Engineering, Wuhan Institute of Technology, Wuhan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hubei Province Key Laboratory of Intelligent Robot, School of Computer Science and Engineering, Wuhan Institute of Technology, Wuhan, China"}], "References": [{"Title": "Hallucinating Unaligned Face Images by Multiscale Transformative Discriminative Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "500", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Edge and identity preserving network for face super-resolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "446", "Issue": "", "Page": "11", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 92458101, "Title": "A cheating immune (k, n) visual cryptography scheme by using the rotation of shares", "Abstract": "<p>Since <PERSON><PERSON> and <PERSON><PERSON><PERSON> introduced visual cryptography scheme (VCS), the cheating problem of VCS has absorbed much attention of scholars. The current researches on cheating immune have one or more of these serious disadvantages: (1) each share has extra pixel expansion, (2) some methods need extra verification shares to determine the share is genuine or not, (3) some schemes require a higher encryption/decryption overhead, and (4) the authentication procedure needs a trusted third party. In order to establish a novel cheating immune visual cryptography scheme (CIVCS) without pixel expansion, this paper generates n original shares by using random grid based visual cryptography scheme (RG-based VCS) and stamps authentication patterns on original shares to obtain verifiable shares, where the authentication patterns are three adjacent and non-intersect concentric solid black rings. In authentication phase, the authentication patterns can be revealed respectively by stacking any two verifiable shares in several ways, including rotating one of the two shares by (90^{\trc }) , (180^{\trc }) , and (270^{\trc }) counterclockwise. The main contribution of this paper is that we propose a novel CIVCS without the above deficiencies. Furthermore, experimental results and theoretical proofs are provided for illustrating the effectiveness of the proposed CIVCS.</p>", "Keywords": "Cheating immune; Visual cryptography scheme; Random grid; Threshold; Rotation; Secret sharing", "DOI": "10.1007/s11042-021-11692-4", "PubYear": 2022, "Volume": "81", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chern institute of Mathematics and LPMC, Nankai University, Tianjin, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chern institute of Mathematics and LPMC, Nankai University, Tianjin, People’s Republic of China"}], "References": []}, {"ArticleId": 92458111, "Title": "D2F: discriminative dense fusion of appearance and motion modalities for end-to-end video classification", "Abstract": "<p>Recently, two-stream networks with multi-modality inputs have shown to be of vital importance for state-of-the-art video understanding. Previous deep systems typically employ a late fusion strategy, however, despite its simplicity and effectiveness, the late strategy might experience insufficient fusion due to that it performs fusion across modalities only once and treats each modality equally without discrimination. In this paper, we propose a Discriminative Dense Fusion (D<sup>2</sup>F) network, addressing these limitations by densely inserting an attention-based fusion block at each layer. We experiment with two typical action classification benchmarks and three popular classification backbones, where our proposed module consistently outperforms state-of-the-art baselines by noticeable margins. Specifically, the two-stream VGG16, ResNet and I3D achieve accuracy of [93.5%, 69.2%], [94.6%, 70.5%], [94.1%, 72.3%] with D<sup>2</sup>F on [UCF101, HMDB51], respectively, with absolute gains of [5.5%, 9.8%], [5.13%, 9.91%], and [0.7%, 5.9%] compared with their late fusion counterparts. The qualitative performance also demonstrates that our model can learn more informative complementary representation.</p>", "Keywords": "Multi-modal fusion; Video classification; Attention; Convolution neural network (CNN); Optical flow fields; Complementary features", "DOI": "10.1007/s11042-021-11247-7", "PubYear": 2022, "Volume": "81", "Issue": "9", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, University of Science and Technology of China, Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, University of Science and Technology of China, Hefei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, University of Science and Technology of China, Hefei, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, University of Science and Technology of China, Hefei, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National Computer Network Emergency Response Technical Center of China, Chengdu, China"}], "References": [{"Title": "Hybrid and hierarchical fusion networks: a deep cross-modal learning architecture for action recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "14", "Page": "10423", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Hand-crafted and deep convolutional neural network features fusion and selection strategy: An application to intelligent human action recognition", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "105986", "JournalTitle": "Applied Soft Computing"}, {"Title": "Learning motion representation for real-time spatio-temporal action localization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "107312", "JournalTitle": "Pattern Recognition"}, {"Title": "A data augmentation method for human action recognition using dense joint motion images", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106713", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 92458112, "Title": "A nonlinear prediction model for Chinese speech signal based on RBF neural network", "Abstract": "<p>A novel method for Chinese speech time series prediction model is proposed. In order to reconstruct the phase space of Chinese speech signal, the delay time and embedding dimension are calculated by C–C method and false nearest neighbor algorithm. The maximum lyapunov exponent and correlation dimension of Chinese speech phoneme are calculated by wolf algorithm and genetic programming algorithm. The numerical results show that there exists nonlinear characteristics in Chinese speech signal. Based on the analysis method of RBF neural network and the nonlinear characteristic parameters such as the delay time and embedding dimension, a nonlinear prediction model is designed. In order to further verify the prediction performance of the designed prediction model, waveform comparison and four evaluation indexes are used. It is shown that compared with the linear prediction model and back propagation neural network nonlinear prediction model, prediction error of the RBF neural network nonlinear prediction model is significantly reduced, and the model has higher prediction accuracy and prediction performance.</p>", "Keywords": "Chinese speech signal; Nonlinear theory; Prediction model; Radical basis function neural network", "DOI": "10.1007/s11042-021-11612-6", "PubYear": 2022, "Volume": "81", "Issue": "4", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Longdong University, Qingyang, China"}], "References": [{"Title": "A multimodel keyword spotting system based on lip movement and speech features", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "20461", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 92458113, "Title": "A lightweight image encryption scheme based on chaos and diffusion circuit", "Abstract": "<p>The Internet of Things (IoT) devices is being deployed in almost all aspects of human life starting from smart home, health monitoring, smart metering, to smart garbage collection and industrial applications. These devices sense and collects data from the environment and send it to other high power computing devices called fog nodes or to the cloud. One of the major challenges in this process is secure communication of data as the IoT devices are having low processing power, memory and energy constraints. This paper proposes a lightweight encryption technique for images using chaotic maps and diffusion circuits. The chaotic maps are used to control the generation of random number sequences which are used for permutation and substitution of the pixel values in images. Both permutation and substitution of the pixel values are done in one scan of the image only reducing the time complexity. The substitution operations are simple bit-wise operations reducing the computational overhead. The scheme is tested by several statistical and security tests to ensure its strength against attacks.</p>", "Keywords": "Internet of things; Pseudorandom bit sequence generator; Encryption; Chaotic map; Diffusion circuit", "DOI": "10.1007/s11042-021-11657-7", "PubYear": 2022, "Volume": "81", "Issue": "24", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Patna, Bihar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Patna, Bihar, India"}], "References": [{"Title": "Image encryption based on a new 2D logistic adjusted logistic map", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "Page": "355", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel image encryption algorithm based on bit-plane matrix rotation and hyper chaotic systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "9-10", "Page": "5573", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A secure image encryption scheme based on a novel 2D sine–cosine cross-chaotic (SC3) map", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "A secure image encryption scheme based on genetic operations and a new hybrid pseudo random number generator", "Authors": "<PERSON><PERSON><PERSON>; Tarni Man<PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "17497", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "IECA: an efficient IoT friendly image encryption technique using programmable cellular automata", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "5083", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Fast image encryption algorithm based on parallel permutation-and-diffusion strategy", "Authors": "<PERSON><PERSON><PERSON> Wang; Hongyu Zhao", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "19005", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multiple-image encryption algorithm based on bit planes and chaos", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "29-30", "Page": "20753", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A new set of image encryption algorithms based on discrete orthogonal moments and Chaos theory", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "20263", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 92458114, "Title": "A secure encryption scheme using a Composite Logistic Sine Map (CLSM) and SHA-256", "Abstract": "<p>We present an algorithm for image encryption for secure transmission and storage of images over insecure public networks. Our algorithm uses Composite Logistic Sine Map (CLSM) and Secure Hash Algorithm-256 (SHA-256). The algorithm first scrambles the pixels of the plain image based on pseudo random number sequence (PRNS) generated by the CLSM. This is followed by diffusing the pixel values by using values generated by SHA-256. The initial conditions and parameters of the CLSM together with a nonce chosen by the user act as key for the algorithm. The nonce is used to initialize the SHA-256 that generates hash values for the diffusion phase. The algorithm has a very wide key space to defeat any brute force or guessing attack. The proposed algorithm was tested with several images of different characteristics against nine security measures. The tests results show that the scheme is robust, scalable and capable of providing strong security to data. The lightweight arithmetic and logical operations involved in the used chaotic map and in permutation-substitution steps enable it to have fast speed due to low computational overhead. This makes it suitable for low power battery operated devices equipped with inexpensive processors having limited computing speed.</p>", "Keywords": "Encryption; Chaotic map; Random number generation; PRNG; PRNS; SHA256", "DOI": "10.1007/s11042-021-11460-4", "PubYear": 2022, "Volume": "81", "Issue": "19", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Computer Science and Engineering, National Institute of Technology Jamshedpur, Jamshedpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Patna, Bihar, India"}, {"AuthorId": 3, "Name": "Tarni Man<PERSON>", "Affiliation": "Dept. of Mathematics, National Institute of Technology Jamshedpur, Jamshedpur, India"}], "References": [{"Title": "A secure image encryption scheme based on genetic operations and a new hybrid pseudo random number generator", "Authors": "<PERSON><PERSON><PERSON>; Tarni Man<PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "17497", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A new one-dimensional cosine polynomial chaotic map and its use in image encryption", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "3", "Page": "541", "JournalTitle": "The Visual Computer"}, {"Title": "Efficient chaotic-based image cryptosystem with different modes of operation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "29-30", "Page": "20665", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Multiple-image encryption algorithm based on bit planes and chaos", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "29-30", "Page": "20753", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A new set of image encryption algorithms based on discrete orthogonal moments and Chaos theory", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "20263", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Image encryption algorithm based on LDCML and DNA coding sequence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "1", "Page": "591", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel 1D chaotic system for image encryption, authentication and compression in cloud", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "6", "Page": "8721", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 92458116, "Title": "Deepfake generation and detection, a survey", "Abstract": "<p>Deepfake refers to realistic, but fake images, sounds, and videos generated by articial intelligence methods. Recent advances in deepfake generation make deepfake more realistic and easier to make. Deepfake has been a signicant threat to national security, democracy, society, and our privacy, which calls for deepfake detection methods to combat potential threats. In the paper, we make a survey on state-ofthe-art deepfake generation methods, detection methods, and existing datasets. Current deepfake generation methods can be classified into face swapping and facial reenactment. Deepfake detection methods are mainly based features and machine learning methods. There are still some challenges for deepfake detection, such as progress on deepfake generation, lack of high quality datasets and benchmark. Future trends on deepfake detection can be efficient, robust and systematical detection methods and high quality datasets.</p>", "Keywords": "Deepfake; Detection; Generation; Survey; Media forensics", "DOI": "10.1007/s11042-021-11733-y", "PubYear": 2022, "Volume": "81", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Cyber Science and Technology, Beihang University, Beijing, China;Guangxi Key Laboratory of Cryptography and Information Security, Guilin University of Electronic Technology, Guilin, China;Guangxi Key Laboratory of Trusted Software & Guangxi Key Laboratory of Cryptography and Information Security, Guilin University of Electronic Technology, Guilin, China;Key Lab of Film and TV Media Technology of Zhejiang Province, Hangzhou, China"}], "References": [{"Title": "Swapped face detection using deep learning and subjective assessment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "1", "Page": "1", "JournalTitle": "EURASIP Journal on Information Security"}]}, {"ArticleId": 92458118, "Title": "An ECG classification using DNN classifier with modified pigeon inspired optimizer", "Abstract": "<p>Arrhythmia is a form of heart disease in which the regularity of the pulse is changed.ECG data may be analyzed to detect heart-related illnesses or arrhythmias. This paper presents a wrapper feature selection strategy that employs a Pigeon-inspired optimizer(PIO). The modified Pigeon Inspired Optimizer (MPIO) is used to optimize ECG features and the Deep Neural Network (DNN) to classify the ECG signals. In MPIO, the new blood pigeons were introduced to improve the accuracy of the algorithm. Morphological features, wavelet transform coefficients, and R-R interval dynamic features are extracted for classification of ECG signals. After feature extraction, MPIO is used for feature optimization because optimizing the feature plays a key role in developing the model of machine learning, and irrelevant data features degrade model accuracy and enhance model training time. Using optimised features, the DNN classifier is utilised to classify ECG data. The proposed method achieves 99.10% accuracy, 98.90% specificity, and 98.50% sensitivity. Additionally, when compared with other state-of-the-art methodologies, our method of feature selection also exhibited better outcomes.</p>", "Keywords": "Deep neural network; Heartbeat classification; PIO; Wavelet transform", "DOI": "10.1007/s11042-021-11594-5", "PubYear": 2022, "Volume": "81", "Issue": "7", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, FET Gurukul Kangri University, Haridwar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, GBPIET, Pauri Garhwal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, GBPIET, Pauri Garhwal, India"}], "References": [{"Title": "GB-SVNN: Genetic BAT assisted support vector neural network for arrhythmia classification using ECG signals", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "1", "Page": "54", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "An efficient henry gas solubility optimization for feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "113364", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 92458272, "Title": "Investigating the Role of Having an Avatar in Virtual Reality on Pain Alleviation and Embodiment in Patients With Pain Using Electroencephalogram: A Neuroimaging Protocol", "Abstract": "<p>Chronic Pain (CP) is prevalent in industrialized countries and stands among the top 10 causes of disability. Given the widespread problems of pharmacological treatments such as opioids, a need to find alternative therapeutic approaches has emerged. Virtual Reality (VR) has shown potential as a non-pharmacological alternative for controlling pain over the past 20 years. The effectiveness of VR has been demonstrated in treating CP, and it has been suggested that VR’s analgesic effects may be associated with the Sense of Embodiment (SoE): the sensation of being inside, having and controlling a virtual body in VR. Studies have shown correlations among brain signals, reported pain and a SoE, and correlations have been observed between using an avatar in VR and pain alleviation among CP patients. However, little has been published about the changes in brain physiology associated with having an avatar in VR, and current published studies present methodological issues. Defining a proper methodology to investigate the underlying brain mechanisms of pain, a SoE associated with having an avatar in VR, and its effect on reducing pain in CP patients is key to the emerging field of VR-analgesia. Here, we propose an intervention trial design (test/intervention/test) to evaluate the effects of having a virtual avatar in VR on pain levels and SoE in CP patients using Electroencephalogram (EEG) recordings. Resting-state EEG recordings, perceived pain levels, and SoE scores will be collected before and after the VR intervention. Patients diagnosed with CP will be recruited from local pain clinics and pseudo-randomly assigned to one of two groups—with or without an avatar. Patients will experience a 10-min VR intervention built to treat CP while their EEG signals are recorded. In articulating the study procedure, we propose a framework for future studies that explores the mechanisms of VR-analgesia in patients with chronic pain.</p>", "Keywords": "virtual reality; EEG; VR analgesia; Theta wave; embodiment; Chronic Pain; Alpha wave; Avatar", "DOI": "10.3389/frvir.2021.775764", "PubYear": 2022, "Volume": "2", "Issue": "", "JournalId": 73463, "JournalTitle": "Frontiers in Virtual Reality", "ISSN": "", "EISSN": "2673-4192", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Pain Studies Lab, School of Interactive Arts and Technology, Simon Fraser University, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Pain Studies Lab, School of Interactive Arts and Technology, Simon Fraser University, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computational Health Research Laboratory, School of Interactive Arts and Technology, Simon Fraser University, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The Pain Studies Lab, School of Interactive Arts and Technology, Simon Fraser University, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computational Health Research Laboratory, School of Interactive Arts and Technology, Simon Fraser University, Canada"}], "References": []}, {"ArticleId": 92458451, "Title": "On efficient model selection for sparse hard and fuzzy center-based clustering algorithms", "Abstract": "The class of center-based clustering algorithms offers methods to efficiently identify clusters in data sets, making them applicable to larger data sets. While a data set may contain several features, not all of them may be equally informative or helpful towards cluster detection. Therefore, sparse center-based clustering methods offer a way to select only those features that may be useful in identifying the clusters present in a data set. However, to automatically determine the degree to which features should be selected, these methods use the Permutation Method which involves generating and clustering multiple randomly permuted data sets, leading to much higher computation costs. In this paper, we propose an improved approach towards model selection for sparse clustering by using expressions of Bayesian Information Criterion (BIC) derived for the center-based clustering methods of k -Means and Fuzzy c -Means. The derived expressions of BIC require significantly lower computation costs, yet allow us to compare and select a suitable sparse clustering among several possible sparse partitions that may have selected different subsets of features. Experiments on synthetic and real-world data sets show that using BIC for model selection leads to remarkable improvements in the identification of sparse clusterings for both Sparse k -Means and Sparse Fuzzy c -Means.", "Keywords": "", "DOI": "10.1016/j.ins.2021.12.070", "PubYear": 2022, "Volume": "590", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electronics and Communication Sciences Unit, Indian Statistical Institute, 203 B. T. Road, Kolkata 700108, West Bengal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electronics and Communication Sciences Unit, Indian Statistical Institute, 203 B. T. Road, Kolkata 700108, West Bengal, India;Corresponding author"}], "References": [{"Title": "Neither global nor local: A hierarchical robust subspace clustering for image data", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "514", "Issue": "", "Page": "333", "JournalTitle": "Information Sciences"}, {"Title": "Learning a consensus affinity matrix for multi-view clustering via subspaces merging on Grassmann manifold", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "68", "JournalTitle": "Information Sciences"}, {"Title": "Improved clustering algorithms for image segmentation based on non-local information and back projection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "550", "Issue": "", "Page": "129", "JournalTitle": "Information Sciences"}, {"Title": "Deep multi-view document clustering with enhanced semantic embedding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "564", "Issue": "", "Page": "273", "JournalTitle": "Information Sciences"}, {"Title": "Deep self-representative subspace clustering network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "108041", "JournalTitle": "Pattern Recognition"}, {"Title": "Robust subspace clustering based on automatic weighted multiple kernel learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "573", "Issue": "", "Page": "453", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 92458537, "Title": "A Simulation Study on Spread of Disease and Control Measures in Closed Population Using ABM", "Abstract": "<p>An infectious disease can cause a detrimental effect on national security. A group such as the military called a “closed population”, which is a subset of the general population but has many distinct characteristics, must survive even in the event of a pandemic. Hence, it requires its own distinct solution during a pandemic. In this study, we investigate a simulation analysis for implementing an agent-based model that reflects the characteristics of agents and the environment in a closed population and finds effective control measures for making the closed population functional in the course of disease spreading.</p>", "Keywords": "disease spread model; agent-based model; military and security; closed population disease spread model ; agent-based model ; military and security ; closed population", "DOI": "10.3390/computation10010002", "PubYear": 2022, "Volume": "10", "Issue": "1", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Operations Research, Korea National Defence University (KNDU), Nonsan 33021, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Author to whom correspondence should be addressed"}], "References": [{"Title": "How simulation modelling can help reduce the impact of COVID-19", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "2", "Page": "83", "JournalTitle": "Journal of Simulation"}]}, {"ArticleId": 92458559, "Title": "On Developing Generic Models for Predicting Student Outcomes in Educational Data Mining", "Abstract": "<p>Poor academic performance of students is a concern in the educational sector, especially if it leads to students being unable to meet minimum course requirements. However, with timely prediction of students’ performance, educators can detect at-risk students, thereby enabling early interventions for supporting these students in overcoming their learning difficulties. However, the majority of studies have taken the approach of developing individual models that target a single course while developing prediction models. These models are tailored to specific attributes of each course amongst a very diverse set of possibilities. While this approach can yield accurate models in some instances, this strategy is associated with limitations. In many cases, overfitting can take place when course data is small or when new courses are devised. Additionally, maintaining a large suite of models per course is a significant overhead. This issue can be tackled by developing a generic and course-agnostic predictive model that captures more abstract patterns and is able to operate across all courses, irrespective of their differences. This study demonstrates how a generic predictive model can be developed that identifies at-risk students across a wide variety of courses. Experiments were conducted using a range of algorithms, with the generic model producing an effective accuracy. The findings showed that the CatBoost algorithm performed the best on our dataset across the F-measure, ROC (receiver operating characteristic) curve and AUC scores; therefore, it is an excellent candidate algorithm for providing solutions on this domain given its capabilities to seamlessly handle categorical and missing data, which is frequently a feature in educational datasets.</p>", "Keywords": "machine learning; early prediction; CatBoost; at-risk students; educational data mining machine learning ; early prediction ; CatBoost ; at-risk students ; educational data mining", "DOI": "10.3390/bdcc6010006", "PubYear": 2022, "Volume": "6", "Issue": "1", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical and Computational Sciences, Massey University, Auckland 0632, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical and Computational Sciences, Massey University, Auckland 0632, New Zealand"}], "References": [{"Title": "CatBoost for big data: an interdisciplinary review", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "94", "JournalTitle": "Journal of Big Data"}, {"Title": "Massive LMS log data analysis for the early prediction of course-agnostic student performance", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON> del Puerto Paule-R<PERSON><PERSON>z; <PERSON>", "PubYear": 2021, "Volume": "163", "Issue": "", "Page": "104108", "JournalTitle": "Computers & Education"}, {"Title": "An interpretable prediction method for university student academic crisis warning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "1", "Page": "323", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 92458569, "Title": "Brain Tumour Classification Using Noble Deep Learning Approach with Parametric Optimization through Metaheuristics Approaches", "Abstract": "<p>Deep learning has surged in popularity in recent years, notably in the domains of medical image processing, medical image analysis, and bioinformatics. In this study, we offer a completely autonomous brain tumour segmentation approach based on deep neural networks (DNNs). We describe a unique CNN architecture which varies from those usually used in computer vision. The classification of tumour cells is very difficult due to their heterogeneous nature. From a visual learning and brain tumour recognition point of view, a convolutional neural network (CNN) is the most extensively used machine learning algorithm. This paper presents a CNN model along with parametric optimization approaches for analysing brain tumour magnetic resonance images. The accuracy percentage in the simulation of the above-mentioned model is exactly 100% throughout the nine runs, i.e., <PERSON><PERSON>’s L9 design of experiment. This comparative analysis of all three algorithms will pique the interest of readers who are interested in applying these techniques to a variety of technical and medical challenges. In this work, the authors have tuned the parameters of the convolutional neural network approach, which is applied to the dataset of Brain MRIs to detect any portion of a tumour, through new advanced optimization techniques, i.e., SFOA, FBIA and MGA.</p>", "Keywords": "deep learning; parametric optimization; metaheuristic approaches; brain tumour deep learning ; parametric optimization ; metaheuristic approaches ; brain tumour", "DOI": "10.3390/computers11010010", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Technology (CSE), GIET University, Gunupur 765022, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Technology (CSE), GIET University, Gunupur 765022, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering, Kalinga Institute of Technology, Deemed to be University, Bhubaneswar 751024, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Government College of Engineering, Bhawanipatna 766002, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, South Ural State University, 454080 Chelyabinsk, Russia↑Author to whom correspondence should be addressed"}], "References": [{"Title": "FBI inspired meta-optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106339", "JournalTitle": "Applied Soft Computing"}, {"Title": "Analyzing MRI scans to detect glioblastoma tumor using hybrid deep belief networks", "Authors": "Annaparedd<PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "An efficient two-step damage identification method using sunflower optimization algorithm and mode shape curvature (MSDBI–SFO)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1711", "JournalTitle": "Engineering with Computers"}, {"Title": "Multiobjective design optimization of CFRP isogrid tubes using sunflower optimization based on metamodel", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "249", "Issue": "", "Page": "106508", "JournalTitle": "Computers & Structures"}, {"Title": "Artificial intelligence with big data analytics-based brain intracranial hemorrhage e-diagnosis using CT images", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "22", "Page": "16037", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 92458915, "Title": "Repurposing metal additive manufacturing support structures for reduction of residual stress deformation", "Abstract": "<p>Support structures in additive manufacturing (AM) have traditionally been implemented to address process restrictions. This study repurposed the supports as design tools to be used to reduce deformation from residual stress in metal AM prints. Four geometric features were selected via industry interviews and simulations, and experimental prints were used to verify the use of new, novel supports addressing both mechanical and process limit needs. These supports reduced maximum deformation by 14.6% in a validation part simulation that contained all four features. Guidelines were created to present the new design envelopes for each geometric feature to aid in the growth of support structure documentation in AM. Using supports to reduce deformation presents a new design tool to AM engineers that allows them to retain critical part geometry and only change support design.</p>", "Keywords": "Additive manufacturing; Residual stress; DMLM; Support structures", "DOI": "10.1007/s00170-021-08646-3", "PubYear": 2022, "Volume": "119", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Clemson University, Clemson, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Texas at Dallas, Richardson, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Clemson University, Clemson, USA"}], "References": [{"Title": "A measurement method of the belt grinding allowance of hollow blades based on blue light scanning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "9-10", "Page": "3295", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 92458978, "Title": "Gene Ontology GAN (GOGAN): a novel architecture for protein function prediction", "Abstract": "<p>One of the most important aspects for a deep interpretation of molecular biology is the precise annotation of protein functions. An overwhelming majority of proteins, across species, do not have sufficient supplementary information available, which causes them to stay uncharacterized. Contrastingly, all known proteins have one key piece of information available: their amino acid sequence. Therefore, for a wider applicability of algorithms, across different species proteins, researchers are motivated to make computational techniques that characterize proteins using their amino acid sequence. However, in case of computational techniques like deep learning algorithms, huge amount of labeled information is required to produce good results. The labeling process of data is time and resource consuming making labeled data scarce. Utilizing the characteristic to address the formerly mentioned issues of uncharacterized proteins and traditional deep learning algorithms, we propose a model called GOGAN, that operates on the amino acid sequence of a protein to predict its functions. Our proposed GOGAN model does not require any handcrafted features, rather it extracts automatically, all the required information from the input sequence. GOGAN model extracts features from the massively large unlabeled protein datasets. The term “Unlabeled data” is used for piece of information that have not been assigned labels to identify their characteristics or properties. The features extracted by GOGAN model can be utilized in other applications like gene variation analysis, gene expression analysis and gene regulation network detection. The proposed model is benchmarked on the Homo sapiens protein dataset extracted from the UniProt database. Experimental results show clear improvements in different evaluation metrics when compared with other methods. Overall, GOGAN achieves an F1 score of 72.1% with Hamming loss of 9.5%, using only the amino acid sequences of protein.</p>", "Keywords": "Protein function prediction; Sequence analysis; Deep learning; Generative adversarial networks; Gene ontology; Transfer learning", "DOI": "10.1007/s00500-021-06707-z", "PubYear": 2022, "Volume": "26", "Issue": "16", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Excellence in Information Technology, Institute of Management Sciences, Peshawar, Pakistan; Department of Computer Science, National University of Computer and Emerging Sciences, Peshawar, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, National University of Computer and Emerging Sciences, Peshawar, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, National University of Computer and Emerging Sciences, Peshawar, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Control and Computer Engineering (DAUIN), Politecnico di Torino, Turin, Italy"}], "References": [{"Title": "Improved sequence generation model for multi-label classification via CNN and initialized fully connection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "382", "Issue": "", "Page": "188", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 92459012, "Title": "Evaluating Virtual Patient Interaction Fidelity With Advanced Communication Skills Learners", "Abstract": "<p> Previous research in educational medical simulation has drawn attention to the interplay between a simulation’s fidelity and its educational effectiveness. As virtual patients (VPs) are increasingly used in medical simulations for education purposes, a focus on the relationship between virtual patients’ fidelity and educational effectiveness should also be investigated. In this paper, we contribute to this investigation by evaluating the use of a virtual patient selection interface (in which learners interact with a virtual patient via a set of pre-defined choices) with advanced medical communication skills learners. To this end, we integrated virtual patient interviews into a graduate-level course for speech-language therapists over the course of 2 years. In the first cohort, students interacted with three VPs using only a chat interface. In the second cohort, students used both a chat interface and a selection interface to interact with the VPs. Our results suggest that these advanced learners view the selection interfaces as more appropriate for novice learners and that their communication behavior was not significantly affected by using the selection interface. Based on these results, we suggest that selection interfaces may be more appropriate for novice communication skills learners, but for applications in which selection interfaces are to be used with advanced learners, additional design research may be needed to best target these interfaces to advanced learners. </p>", "Keywords": "Virtual patients; medical simulation; virtual reality; simulation-based training; instructional design; Cognitive Load", "DOI": "10.3389/frvir.2021.801793", "PubYear": 2022, "Volume": "2", "Issue": "", "JournalId": 73463, "JournalTitle": "Frontiers in Virtual Reality", "ISSN": "", "EISSN": "2673-4192", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Central Florida, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Speech Science, The University of Auckland, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer and Information Sciences and Engineering, University of Florida, United States"}], "References": []}, {"ArticleId": 92459291, "Title": "A matrix cube-based estimation of distribution algorithm for the energy-efficient distributed assembly permutation flow-shop scheduling problem", "Abstract": "In this paper, a matrix-cube-based estimation of distribution algorithm (MCEDA) is proposed to solve the energy-efficient distributed assembly permutation flow-shop scheduling problem (EE_DAPFSP) that minimizes both the maximum completion time ( C max ) and the total carbon emission ( TCE ) simultaneously. Firstly, a high-quality and diverse initial population is constructed via a hybrid initialization method. Secondly, a matrix-cube-based probabilistic model and its update mechanism are designed to appropriately accumulate the valuable pattern information from superior solutions. Thirdly, a suitable sampling strategy is developed to sample the probabilistic model to generate a new population per generation, so as to guide the search direction toward promising regions in solution space. Fourthly, a problem-dependent neighborhood search based on critical path is provided to perform an in-depth local search around the promising regions found by the global search. Fifthly, two types of speed adjustment strategies based on problem properties are also embedded to further improve the quality of the obtained solutions. Sixthly, the influence of the parameters is investigated based on the multi-factor analysis of variance of Design-of-Experiments. Finally, extensive experiments and comprehensive comparisons with several recent state-of-the-art multi-objective algorithms are carried out based on the well-known benchmark instances, and the statistical results demonstrate the efficiency and effectiveness of the proposed MCEDA in addressing the EE_DAPFSP.", "Keywords": "Energy-efficient scheduling ; Estimation of distribution algorithm ; Distributed flowshop scheduling ; Assembly line ; Low-carbon manufacturing", "DOI": "10.1016/j.eswa.2021.116484", "PubYear": 2022, "Volume": "194", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China;School of Mechanical and Electrical Engineering, Kunming University of Science and Technology, Kunming 650500, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China;Yunnan Key Laboratory of Artificial Intelligence, Kunming University of Science and Technology, Kunming 650500, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China;School of Mechanical and Electrical Engineering, Kunming University of Science and Technology, Kunming 650500, China;Yunnan Key Laboratory of Artificial Intelligence, Kunming University of Science and Technology, Kunming 650500, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Automation, Tsinghua University, Beijing 100084, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Alliance Manchester Business School, The University of Manchester, Manchester M15 6PB, United Kingdom"}], "References": [{"Title": "A multi-population, multi-objective memetic algorithm for energy-efficient job-shop scheduling with deteriorating machines", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "157", "Issue": "", "Page": "113348", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Energy-efficient distributed permutation flow shop scheduling problem using a multi-objective whale swarm algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "100716", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A matrix-cube-based estimation of distribution algorithm for the distributed assembly permutation flow-shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100785", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 92459753, "Title": "The Dichotomy of Evaluating Homomorphism-Closed Queries on Probabilistic Graphs", "Abstract": "<p>We study the problem of query evaluation on probabilistic graphs, namely, tuple-independent probabilistic databases over signatures of arity two. We focus on the class of queries closed under homomorphisms, or, equivalently, the infinite unions of conjunctive queries. Our main result states that the probabilistic query evaluation problem is #P-hard for all unbounded queries from this class. As bounded queries from this class are equivalent to a union of conjunctive queries, they are already classified by the dichotomy of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2012). Hence, our result and theirs imply a complete data complexity dichotomy, between polynomial time and #P-hardness, on evaluating homomorphism-closed queries over probabilistic graphs. This dichotomy covers in particular all fragments of infinite unions of conjunctive queries over arity-two signatures, such as negation-free (disjunctive) Datalog, regular path queries, and a large class of ontology-mediated queries. The dichotomy also applies to a restricted case of probabilistic query evaluation called generalized model counting, where fact probabilities must be 0, 0.5, or 1. We show the main result by reducing from the problem of counting the valuations of positive partitioned 2-DNF formulae, or from the source-to-target reliability problem in an undirected graph, depending on properties of minimal models for the query.</p>", "Keywords": "Computer Science - Data Structures and Algorithms;Computer Science - Computational Complexity;Computer Science - Databases", "DOI": "10.46298/lmcs-18(1:2)2022", "PubYear": 2022, "Volume": "18, Issue 1", "Issue": "", "JournalId": 10075, "JournalTitle": "Logical Methods in Computer Science", "ISSN": "", "EISSN": "1860-5974", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "LTCI, Télécom Paris, Institut Polytechnique de Paris, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Oxford, United Kingdom"}], "References": []}, {"ArticleId": 92459792, "Title": "Automatic fabric pattern recognition and design based on deep learning and portable device", "Abstract": "With the development of e‐commerce, online shopping has become the main approach to buy clothes, in which accurately classifying clothing through images becomes more and more important. However, most of clothing classification works focus on clothing types. With the increasing volume of online clothing transactions, various platforms have accumulated a large number of unmarked clothing images that cannot be fully utilized. Meanwhile, many consumers pay more attention to the clothing style. In order to solve this problem, this paper proposes a clothing style recognition framework based on Alex Net run on a portable device. The proposed clothing style recognition is evaluated on Deepfashion dataset. The experimental results show that the proposed clothing style recognition is superior to traditional convolutional neural network (CNN), ResNet and Bilinear convolutional neural network (Bilinear‐CNN).", "Keywords": "Alex net;clothing styling recognition;deep learning;fabric pattern recognition", "DOI": "10.1002/itl2.343", "PubYear": 2023, "Volume": "6", "Issue": "5", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhuhai City Polytechnic  Zhuhai China"}, {"AuthorId": 2, "Name": "Hang Li", "Affiliation": "Chiangmai University  Chiengmai Thailand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> University  Bangkok Thailand"}], "References": [{"Title": "Image Matching from Handcrafted to Deep Features: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "1", "Page": "23", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Neighborhood linear discriminant analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108422", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 92459912, "Title": "Information-utilization strengthened equilibrium optimizer", "Abstract": "<p>Equilibrium Optimizer (EO) is a novel meta-heuristic algorithm proposed in 2020 and it has a unique search mechanism and good optimization performance. To further improve its optimization performance, this paper proposes an Information-utilization Strengthened EO (IS-EO). Firstly, a cross EO (CEO) is constructed under the guidance of the historical individual-best information to strengthen information guiding. Secondly, a Global-best opposition learning CEO (GCEO) is created under the guidance of the global best information to a random individual to further strengthen information guiding. Finally, a differential mutation strategy is incorporated into GCEO to construct IS-EO and strengthen information sharing between individuals. Experimental results on the 65 benchmark functions and the 3 engineering design problems show that IS-EO attains stronger search ability and faster running speed compared with EO and other state-of-the-art comparison algorithms and can solve the engineering problems more effectively.</p>", "Keywords": "Optimization algorithm; Equilibrium optimizer; Information guiding; Information sharing; Engineering problems", "DOI": "10.1007/s10462-021-10105-0", "PubYear": 2022, "Volume": "55", "Issue": "5", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Engineering, Henan Normal University, Xinxiang Henan, China; Engineering Lab of Intelligence Business & Internet of Things of Henan Province, Xinxiang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Engineering, Henan Normal University, Xinxiang Henan, China"}], "References": [{"Title": "Learning–interaction–diversification framework for swarm intelligence optimizers: a unified perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "6", "Page": "1789", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Improved GWO for large-scale function optimization and MLP optimization in cancer identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "1305", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A guided population archive whale optimization algorithm for solving multiobjective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112972", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Equilibrium optimizer: A novel optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105190", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Artificial electric field algorithm for engineering optimization problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113308", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Artificial bee colony algorithm based on adaptive neighborhood search and Gaussian perturbation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106955", "JournalTitle": "Applied Soft Computing"}, {"Title": "Hybrid Particle Swarm and Grey Wolf Optimizer and its application to clustering optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107061", "JournalTitle": "Applied Soft Computing"}, {"Title": "Information guiding and sharing enhanced simultaneous heat transfer search and its application to k-means optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107476", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 92459917, "Title": "Sequential learning for sketch-based 3D model retrieval", "Abstract": "<p>Sketch-based 3D model retrieval suffers large visual discrepancy between 3D models and 2D sketches. Most existing methods directly project samples from both modalities into a same semantic embedding space to alleviate the discrepancy. We argue that simultaneous learning of those two modalities would restrict the discrimination of 3D model representation, resulting in inferior retrieval results. In this work, we propose a novel sequential learning (SL) framework for sketch-based 3D model retrieval to learn 3D model representation and 2D sketch representation separately and sequentially. Specifically, the SL framework is composed of two modules, 3D model network (3DMN) and 2D sketch network (2DSN). Firstly, we train 3DMN with a discriminative loss formulated only on 3D models to promote discrimination. Then, the learned representations of 3D models guide 2DSN to learn discriminative 2D sketch representations. In the second phase, we further mine the implicit fine-grained class information of 3D models by unsupervised clustering algorithms. An alignment loss is formulated on 2D sketches and corresponding fine-grained class centers of 3D models. Extensive experiments on three large-scale benchmark datasets for 3D model retrieval validate the efficacy of the proposed SL framework and fine-grained class representations.</p>", "Keywords": "Sketch-based retrieval; 3D model retrieval; Sequential learning; Shape representation", "DOI": "10.1007/s00530-021-00871-w", "PubYear": 2022, "Volume": "28", "Issue": "3", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Technology, Dalian University of Technology, Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Software Technology, Dalian University of Technology, Dalian, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Software Technology, Dalian University of Technology, Dalian, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ritsumeikan University International School of Information Science and Engineering, Dalian University of Technology, Dalian, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Technology, Dalian University of Technology, Dalian, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ritsumeikan University International School of Information Science and Engineering, Dalian University of Technology, Dalian, China"}], "References": []}, {"ArticleId": 92459922, "Title": "COVID-19 and cyberbullying: deep ensemble model to identify cyberbullying from code-switched languages during the pandemic", "Abstract": "<p>It has been declared by the World Health Organization (WHO) the novel coronavirus a global pandemic due to an exponential spread in COVID-19 in the past months reaching over 100 million cases and resulting in approximately 3 million deaths worldwide. Amid this pandemic, identification of cyberbullying has become a more evolving area of research over posts or comments in social media platforms. In multilingual societies like India, code-switched texts comprise the majority of the Internet. Identifying the online bullying of the code-switched user is bit challenging than monolingual cases. As a first step towards enabling the development of approaches for cyberbullying detection, we developed a new code-switched dataset, collected from Twitter utterances annotated with binary labels. To demonstrate the utility of the proposed dataset, we build different machine learning (Support Vector Machine &amp; Logistic Regression) and deep learning (Multilayer Perceptron, Convolution Neural Network, BiLSTM, BERT) algorithms to detect cyberbullying of English-Hindi (En-Hi) code-switched text. Our proposed model integrates different hand-crafted features and is enriched by sequential and semantic patterns generated by different state-of-the-art deep neural network models. Initial experimental results of the proposed deep ensemble model on our code-switched data reveal that our approach yields state-of-the-art results, i.e., 0.93 in terms of macro-averaged F1 score. The dataset and codes of the present study will be made publicly available on the paper's companion repository [https://github.com/95sayanta/COVID-19-and-Cyberbullying].</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2021.</p>", "Keywords": "Code-switched language;Cyberbullying;Deep ensemble;Natural language processing", "DOI": "10.1007/s11042-021-11601-9", "PubYear": 2023, "Volume": "82", "Issue": "6", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Patna, Bihta, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Patna, Bihta, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Patna, Patna, India."}], "References": []}, {"ArticleId": 92459941, "Title": "AM-UNet: automated mini 3D end-to-end U-net based network for brain claustrum segmentation", "Abstract": "<p>Recent advances in deep learning (DL) have provided promising solutions to medical image segmentation. Among existing segmentation approaches, the U-Net-based methods have been used widely. However, very few U-Net-based studies have been conducted on automatic segmentation of the human brain claustrum (CL). The CL segmentation is challenging due to its thin, sheet-like structure, heterogeneity of its image modalities and formats, imperfect labels, and data imbalance. We propose an automatic optimized U-Net-based 3D segmentation model, called AM-UNet, designed as an end-to-end process of the pre and post-process techniques and a U-Net model for CL segmentation. It is a lightweight and scalable solution which has achieved the state-of-the-art accuracy for automatic CL segmentation on 3D magnetic resonance images (MRI). On the T1/T2 combined MRI CL dataset, AM-UNet has obtained excellent results, including Dice, Intersection over Union (IoU), and Intraclass Correlation Coefficient (ICC) scores of 82%, 70%, and 90%, respectively. We have conducted the comparative evaluation of AM-UNet with other pre-existing models for segmentation on the MRI CL dataset. As a result, medical experts confirmed the superiority of the proposed AM-UNet model for automatic CL segmentation. The source code and model of the AM-UNet project is publicly available on GitHub: https://github.com/AhmedAlbishri/AM-UNET.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2021.</p>", "Keywords": "", "DOI": "10.1007/s11042-021-11568-7", "PubYear": 2022, "Volume": "81", "Issue": "25", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing and Engineering, University of Missouri-Kansas City, Kansas City, MO 64110 USA. ;College of Computing and Informatics, Saudi Electronic University, Riyadh, Saudi Arabia."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing and Engineering, University of Missouri-Kansas City, Kansas City, MO 64110 USA."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychiatry Biomedical Sciences, School of Medicine, University of Missouri-Kansas City, Kansas City, MO 64110 USA."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Lee", "Affiliation": "School of Computing and Engineering, University of Missouri-Kansas City, Kansas City, MO 64110 USA."}], "References": []}, {"ArticleId": 92460155, "Title": "Adversarial Cross-domain Community Question Retrieval", "Abstract": "<p> Community Q&A forum is a special type of social media that provides a platform to raise questions and to answer them (both by forum participants), to facilitate online information sharing. Currently, community Q&A forums in professional domains have attracted a large number of users by offering professional knowledge. To support information access and save users’ efforts of raising new questions, they usually come with a question retrieval function, which retrieves similar existing questions (and their answers) to a user’s query. However, it can be difficult for community Q&A forums to cover all domains, especially those emerging lately with little labeled data but great discrepancy from existing domains. We refer to this scenario as cross-domain question retrieval. To handle the unique challenges of cross-domain question retrieval, we design a model based on adversarial training, namely, X-QR , which consists of two modules—a domain discriminator and a sentence matcher. The domain discriminator aims at aligning the source and target data distributions and unifying the feature space by domain-adversarial training. With the assistance of the domain discriminator, the sentence matcher is able to learn domain-consistent knowledge for the final matching prediction. To the best of our knowledge, this work is among the first to investigate the domain adaption problem of sentence matching for community Q&A forums question retrieval. The experiment results suggest that the proposed X-QR model offers better performance than conventional sentence matching methods in accomplishing cross-domain community Q&A tasks. </p>", "Keywords": "", "DOI": "10.1145/3487291", "PubYear": 2022, "Volume": "21", "Issue": "3", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "Ai<PERSON> Guo", "Affiliation": "National University of Defense Technology, Changsha, Hunan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National University of Defense Technology, Changsha, Hunan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Defense Technology, Changsha, Hunan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Defense Technology, Changsha, Hunan, China"}], "References": [{"Title": "Few-shot text classification by leveraging bi-directional attention and cross-class knowledge", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "3", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 92460173, "Title": "3D interest point detection using balance-distortion oriented selection", "Abstract": "<p>Interest point detection is a challenging problem in 3D objects. Compared to traditional corner detection based on the curvature, this paper proposes a novel method that quantifies the balance and uniformity of local geometric structures based on the distribution of vertex neighborhoods. We first define the neighborhoods of vertices and structure them within the two-ring, instead of constructing the overall mesh, so as to avoid the interference between the neighborhoods of different vertices. Then we introduce the concept \"balance-distortion\" to describe the geometric features of the local structure. The experimental results show that the proposed algorithm is robust against noise and invariant to geometric transformation. In addition, compared with the corner detection, more feature points that do not satisfy the balance and direction uniformity are detected, and the distribution of interest point is more uniform.</p>", "Keywords": "3D interest point detection; Local topological structure; Balance-distortion", "DOI": "10.1007/s00371-021-02371-4", "PubYear": 2023, "Volume": "39", "Issue": "2", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "Yu <PERSON>", "Affiliation": "Shenyang Institute of Automation, Chinese Academy of Sciences, Shenyang, China; Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China; University of Chinese Academy of Sciences, Beijing, China; Key Laboratory on Intelligent Detection and Equipment Technology of Liaoning Province, Shenyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shenyang Institute of Automation, Chinese Academy of Sciences, Shenyang, China; Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China; Key Laboratory on Intelligent Detection and Equipment Technology of Liaoning Province, Shenyang, China"}, {"AuthorId": 3, "Name": "Yao<PERSON>", "Affiliation": "Shenyang Institute of Automation, Chinese Academy of Sciences, Shenyang, China; Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China; University of Chinese Academy of Sciences, Beijing, China; Key Laboratory on Intelligent Detection and Equipment Technology of Liaoning Province, Shenyang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shenyang Institute of Automation, Chinese Academy of Sciences, Shenyang, China; Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China; University of Chinese Academy of Sciences, Beijing, China; Key Laboratory on Intelligent Detection and Equipment Technology of Liaoning Province, Shenyang, China"}, {"AuthorId": 5, "Name": "Jinsong Du", "Affiliation": "Shenyang Institute of Automation, Chinese Academy of Sciences, Shenyang, China; Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China; Key Laboratory on Intelligent Detection and Equipment Technology of Liaoning Province, Shenyang, China"}], "References": [{"Title": "Three-dimensional salient point detection based on the <PERSON><PERSON>–<PERSON> eigenfunctions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "767", "JournalTitle": "The Visual Computer"}, {"Title": "3D target detection using dual domain attention and SIFT operator in indoor scenes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "11", "Page": "3765", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 92460212, "Title": "Prediction and optimization of electrical conductivity for polymer-based composites using design of experiment and artificial neural networks", "Abstract": "<p>In this paper, conductive polymer-based composites in order to have higher electrical conductivity have been constructed using different nanoparticles and numerically considered by different classification techniques. Due to non-conducting feature of polymer-based composites, their other positive advantages (e.g., light weight and stress corrosion) underneath non-conducting defect in which this paper has tried to overcome the faced challenges. For this purpose, carbon black (CB), carbon nanotube (CNT), and expanded graphite (EG) with different weight percentages are added to the epoxy resin as input factors and the electrical conductivity of the samples are measured as response factor. The analysis of input factors is performed and the Taguchi method, artificial neural networks (ANNs) and extreme learning machine (ELM) are designed and used for the prediction of the response factor. The predicted responses using the applied methods are compared with the experimental results. In order to increase the mechanical strength, ten layers of unidirectional carbon fiber are used. The simulation results show that the ANNs and ELM provide good compatible predictions with respect to actual experiment data. Besides, obtained experimental results prove that the highest electrical conductivity has been achieved using 10, 15, and 25 percent using the CNT, EG, and CB, respectively. As a novelty of this paper, the constructed sample composite reaches the acceptable electrical conductivity suggested by United Stated Department of Energy standard considered as material development. In particular, the findings of this research can be used to construct conductive electrodes particularly in oil and gas industries.</p>", "Keywords": "Electrical conductivity; Artificial neural networks; Extreme learning machine; Polymer-based composite", "DOI": "10.1007/s00521-021-06798-7", "PubYear": 2022, "Volume": "34", "Issue": "10", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Science and Culture, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Science and Culture, Tehran, Iran"}, {"AuthorId": 3, "Name": "Abobakr <PERSON>", "Affiliation": "Department of Computer Science, University of Southampton Malaysia, Gelang Patah, Malaysia"}], "References": []}, {"ArticleId": 92460238, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1574-1192(21)00156-5", "PubYear": 2022, "Volume": "79", "Issue": "", "JournalId": 4135, "JournalTitle": "Pervasive and Mobile Computing", "ISSN": "1574-1192", "EISSN": "1873-1589", "Authors": [], "References": []}, {"ArticleId": 92460243, "Title": "An approach to capturing and reusing tacit design knowledge using relational learning for knowledge graphs", "Abstract": "Tacit design knowledge plays an important role in the process of product design and is a valuable knowledge asset for enterprises. In terms of the characteristics of tacit rational design knowledge, this paper puts forward a scientific hypothesis and approach on capturing and reusing tacit rational design knowledge. The presented approach represents the observable design result facts of products using design knowledge graphs. A design issue-solving oriented knowledge graph model is presented, where directed relation edges represent design issues, and nodes stand for design solutions. When a new design solutions requirement needs to be searched, tacit design knowledge can be reused by relational learning for the constructed design knowledge graphs. In relational learning, the design knowledge graph is converted into a three-order tensor, where two modes are solution nodes, and the third mode holds the issue relations. Then, a tensor factorization approach is employed to calculate the latent features between design solutions for an issue relation. As a result, a score vector to represent the existence of issue-solution relations can be obtained. By sorting the scores in descending order, we may select the solution node with the highest score as the design solution to be searched. Finally, a stamping die design case study is provided. The case study shows that the proposed approach is feasible, and effective, and has better flexibility, scalability and efficiency than CBR methods.", "Keywords": "Design knowledge ; Tacit design knowledge ; Knowledge graph ; Relational learning ; Tensor Factorization", "DOI": "10.1016/j.aei.2021.101505", "PubYear": 2022, "Volume": "51", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian 116024, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian 116024, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Dalian University of Technology, Dalian 116024, China"}], "References": [{"Title": "A review: Knowledge reasoning over knowledge graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112948", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Predicting tissue-specific protein functions using multi-part tensor decomposition", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "343", "JournalTitle": "Information Sciences"}, {"Title": "Cognitive factors of the transfer of empirical engineering knowledge: A behavioral and fNIRS study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101207", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "An improved case-based reasoning method and its application to predict machining performance", "Authors": "<PERSON><PERSON> Xu; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "7", "Page": "5683", "JournalTitle": "Soft Computing"}, {"Title": "Exploiting knowledge graphs in industrial products and services: A survey of key aspects, challenges, and future perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "", "Page": "103449", "JournalTitle": "Computers in Industry"}, {"Title": "A parameter-extended case-based reasoning method based on a functional basis for automated experiential reasoning in mechanical product designs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "", "Page": "101409", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 92460251, "Title": "The influence of packaging on consumers’ risk perception of chemical household products", "Abstract": "<p>Chemical household products are found in most households. If consumers are to safely handle such products, they need to be aware of the risks posed by the particular product they are using. Although most countries require that chemical household products feature warning labels (e.g. the Globally Harmonized System of Classification and Labelling of Chemicals), consumers appear to also use other cues to determine the risks associated with a specific product. Thus, we studied the influence of packaging on consumers' risk perception of chemical household products. More specifically, we examined the effect of the colour of the packaging (black or pink packaging versus the original packaging) as well as the presence of images of flowers or food-imitating elements on the packaging. Significant differences with regard to consumer's risk perception were found in terms of all four studied manipulations. Therefore, we conclude that consumers' risk perception can be influenced by the packaging design. In particular, if elements that lower consumer's risk perception (e.g. featuring flowers on the label and food-imitating elements on the packaging) are omitted from the packaging, consumers might be able to more accurately judge the risks associated with a product and so take appropriate safety precautions.</p><p>Copyright © 2022 The Authors. Published by Elsevier Ltd.. All rights reserved.</p>", "Keywords": "Chemical household products;Packaging design;Risk perception", "DOI": "10.1016/j.apergo.2021.103676", "PubYear": 2022, "Volume": "100", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Consumer Behavior, Institute for Environmental Decisions, ETH Zurich, Universitaetstrasse 22, 8092, Zurich, Switzerland. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Consumer Behavior, Institute for Environmental Decisions, ETH Zurich, Universitaetstrasse 22, 8092, Zurich, Switzerland."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Consumer Behavior, Institute for Environmental Decisions, ETH Zurich, Universitaetstrasse 22, 8092, Zurich, Switzerland."}], "References": []}, {"ArticleId": 92460298, "Title": "An emergency event detection approach in real-time for efficient vehicle safety in Smart City", "Abstract": "<p>Nowadays, the Internet of things (IoT) provides various services to drivers by equipped with smart devices. In this regard, the next generation of vehicles collaborates with the features of IoT to provide safety and security on the roads. To achieve this, it is equipped with short-range communication advances and establishes Vehicle-to-Vehicle (V2V) connectivity. The standardized V2V connectivity and communication are termed in IEEE 802.11p. Later, an alternative named (LTE-V2V) has been introduced. However, both technologies are only concerned with the continuous broadcast of information and cooperative awareness. It only takes information from one vehicle in a text way and sends it to another. In this regard, efficient and satisfactory safety is not provided by these technologies for the analysis of real-time road traffic monitoring. Therefore in this paper, we proposed a solution by providing real-time information on road conditions and traffic scenarios to the drivers. We utilized the capturing of images of road conditions by the positioned cameras and Global Positioning System (GPS) to extract the information regarding vehicle and camera position. The proposed work provides better security rather than a message-passing system in V2V communication. The drivers in our anticipated scenarios can extract and see a clear view of road conditions by the use of captured videos/images. Our proposed solution copes well with moderate traffic conditions and provides a high satisfaction score. The simulation results show that our proposed work can achieve high performance in the provision of providing safety compared to other schemes introduced in this field.</p>", "Keywords": "Internet of things; Energy; Wireless sensor network; Performance analysis", "DOI": "10.1007/s11042-021-11834-8", "PubYear": 2022, "Volume": "81", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Information Technology, Nagpur (IIIT Nagpur), Nagpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Information Technology, Nagpur (IIIT Nagpur), Nagpur, India"}], "References": []}, {"ArticleId": 92460399, "Title": "ZIF-8 derived ZnO-CsPbBr3 polyhedrons for efficient triethylamine detection", "Abstract": "Triethylamine, as a typical gas of volatile organic compounds, is a growing threat to human health due to its extensive applications in industry. In this study, ZIF-8 derived ZnO polyhedrons are decorated by CsPbBr<sub>3</sub> quantum dots via electrostatic adsorption. Then a highly sensitive sensor towards triethylamine based on ZnO-CsPbBr<sub>3</sub> composite is successfully fabricated. Compared with sensors based on pristine ZnO and ZnO (nanoparticles)-CsPbBr<sub>3</sub>, ZnO-CsPbBr<sub>3</sub> displays a higher response value (~ 60 towards 100 ppm of TEA), shorter response/recovery time (2/18 s), and lower detection limit (5 ppb). Thus, highly-sensitive detection and efficiently-catalytic oxidation of trimethylamine can be achieved. Such distinguished performance can be ascribed to its unique architecture and n-n heterojunction. Notably, the sensing mechanism is thoroughly interpreted by X-ray photoelectron spectroscopy, temperature programmed desorption of O<sub>2</sub> and in-situ diffuse scattering Fourier transform infrared spectra.", "Keywords": "Gas sensor ; ZnO-CsPbBr<sub>3</sub> ; Response ; n-n heterojunction ; Vinylamine", "DOI": "10.1016/j.snb.2022.131366", "PubYear": 2022, "Volume": "357", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Technology for Materials Synthesis and Processing, Wuhan University of Technology, Wuhan 430070, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Advanced Technology for Materials Synthesis and Processing, Wuhan University of Technology, Wuhan 430070, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Solar Fuel, Faculty of Materials Science and Chemistry, China University of Geosciences, 388 Lumo Road, Wuhan 430074, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry and Chemical Engineering, Jishou University, Jishou 416000, Hunan, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Solar Fuel, Faculty of Materials Science and Chemistry, China University of Geosciences, 388 Lumo Road, Wuhan 430074, PR China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Technology for Materials Synthesis and Processing, Wuhan University of Technology, Wuhan 430070, PR China;Laboratory of Solar Fuel, Faculty of Materials Science and Chemistry, China University of Geosciences, 388 Lumo Road, Wuhan 430074, PR China;Corresponding author at: State Key Laboratory of Advanced Technology for Materials Synthesis and Processing, Wuhan University of Technology, Wuhan 430070, PR China"}], "References": [{"Title": "Innovative development on a p-type delafossite CuCrO2 nanoparticles based triethylamine sensor", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "324", "Issue": "", "Page": "128743", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Temperature-dependent dual selectivity of hierarchical porous In2O3 nanospheres for sensing ethanol and TEA", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "330", "Issue": "", "Page": "129271", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Triethylamine gas sensor based on Pt-functionalized hierarchical ZnO microspheres", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "331", "Issue": "", "Page": "129425", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "An excellent triethylamine (TEA) sensor based on unique hierarchical MoS2/ZnO composites composed of porous microspheres and nanosheets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "333", "Issue": "", "Page": "129616", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "An excellent triethylamine (TEA) sensor based on unique hierarchical MoS2/ZnO composites composed of porous microspheres and nanosheets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "333", "Issue": "", "Page": "129616", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Metal-Organic frameworks-derived 2D spindle-like sn-doped Co3O4 porous nanosheets as efficient materials for TEA detection", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "338", "Issue": "", "Page": "129825", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Porous ZnSnO3 nanocubes as a triethylamine sensor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Tarcísio M<PERSON>", "PubYear": 2021, "Volume": "338", "Issue": "", "Page": "129869", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "MOF-derived polyhedral NiMoO4@NiO p-p heterostructure as an effective bridge for regulating carriers enhanced sensitivity and selectivity to trimethylamine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "343", "Issue": "", "Page": "130115", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "High-performance fluorescent sensor based on CsPbBr3 quantum dots for rapid analysis of total polar materials in edible oils", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "344", "Issue": "", "Page": "130193", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Fern-like metal-organic frameworks derived In2O3/ZnO nanocomposite for superior triethylamine sensing properties", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "345", "Issue": "", "Page": "130424", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Confined synthesis of 2D ultrathin ZnO/Co3O4 nanomeshes heterostructure for superior triethylamine detection at low temperature", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "346", "Issue": "", "Page": "130486", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Humidity-sensitive CsPbBr3 perovskite based photoluminescent sensor for detecting Water content in herbal medicines", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Jizhou Li", "PubYear": 2021, "Volume": "346", "Issue": "", "Page": "130547", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 92460420, "Title": "Efficient and sparse neural networks by pruning weights in a multiobjective learning approach", "Abstract": "Overparameterization and overfitting are common concerns when designing and training deep neural networks, that are often counteracted by pruning and regularization strategies. However, these strategies remain secondary to most learning approaches and suffer from time and computational intensive procedures. We suggest a multiobjective perspective on the training of neural networks by treating its prediction accuracy and the network complexity as two individual objective functions in a biobjective optimization problem. As a showcase example, we use the cross entropy as a measure of the prediction accuracy while adopting an l 1 -penalty function to assess the total cost (or complexity) of the network parameters. The latter is combined with an intra-training pruning approach that reinforces complexity reduction and requires only marginal extra computational cost. From the perspective of multiobjective optimization, this is a truly large-scale optimization problem. We compare two different optimization paradigms: On the one hand, we adopt a scalarization-based approach that transforms the biobjective problem into a series of weighted-sum scalarizations. On the other hand we implement stochastic multi-gradient descent algorithms that generate a single Pareto optimal solution without requiring or using preference information. In the first case, favorable knee solutions are identified by repeated training runs with adaptively selected scalarization parameters. Numerical results on exemplary convolutional neural networks confirm that large reductions in the complexity of neural networks with negligible loss of accuracy are possible.", "Keywords": "Multiobjective learning ; Unstructured pruning ; Stochastic multi-gradient descent ; l <sub>1</sub>-regularization ; Automated machine learning", "DOI": "10.1016/j.cor.2021.105676", "PubYear": 2022, "Volume": "141", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Natural Sciences, University of Wuppertal, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Natural Sciences, University of Wuppertal, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Natural Sciences, University of Wuppertal, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Natural Sciences, University of Wuppertal, Germany"}], "References": []}, {"ArticleId": 92460438, "Title": "HandO: a hybrid 3D hand–object reconstruction model for unknown objects", "Abstract": "<p>In various multimedia applications, it is of great significance to reconstruct 3D meshes of hands and objects from single RGB images. Mesh-based methods mainly resort to mesh displacements by estimating relative positions between hands and objects, while the distance may be inaccurate. Methods based on signed distance function (SDF) learn relative positions by concurrently sampling hand meshes and object meshes; unfortunately, these methods have very limited capability of reconstructing smooth surfaces with rich details. For example, SDF-based methods are inclined to lose the typologies. To the best of our knowledge, only limited works can simultaneously reconstruct the hands and objects with smooth surfaces and accurate relative positions. To this end, we present a novel hybrid model—hand–object Model (HandO) enabling the hand–object 3D reconstruction with smooth surfaces and accurate positions. Critically, our model for the first time makes the hybrid 3D representation for this task by bringing meshes, SDFs, and parametric models together. A feature extractor is employed to extract the image features, and SDF sample points are projected onto these features to extract the local features of each sampled point. Essentially, our model can be naturally extended to reconstruct a whole body holding an object via the new hybrid representation. Additionally, to overcome the lack of training data, a synthetic body-holding dataset is contributed to the community, thus facilitating the research of reconstructing the hand and object. It contains 31763 images of over 50 object categories. Extensive experiments demonstrate that our model can achieve better performance over the competitors on benchmark datasets.</p>", "Keywords": "", "DOI": "10.1007/s00530-021-00874-7", "PubYear": 2022, "Volume": "28", "Issue": "5", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Academy for Engineering and Technology, Fudan University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China"}, {"AuthorId": 3, "Name": "Yanwei Fu", "Affiliation": "School of Data Science, Fudan University, Shanghai, China"}, {"AuthorId": 4, "Name": "Xiangyang Xue", "Affiliation": "School of Computer Science, Fudan University, Shanghai, China"}], "References": [{"Title": "Text query based summarized event searching interface system using deep learning over cloud", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "7", "Page": "11079", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 92460444, "Title": "Borrowing wisdom from world: modeling rich external knowledge for Chinese named entity recognition", "Abstract": "<p>Chinese named entity recognition (CNER) is one of the fundamental tasks in natural language processing (NLP), supporting a wide range of downstream NLP tasks for Chinese texts. The recent best-performing CNER works have extensively shown that by using external knowledge, such as lexicons and syntactic dependency features, considerable task improvements can be secured. Nevertheless, we note that current works still fail to sufficiently integrate rich external knowledge to boost the CNER performances further. In this work, we propose to enhance the CNER by incorporating heterogeneous knowledge from the linguistic, syntactic and semantic perspectives. For linguistic source, we consider (1) part-of-speech (POS) tags and (2) multi-granularity lexicons, including characters, words and subwords. For syntactic source, we adopt label-wise character-level syntactic dependency structures. For semantic source, we employ (1) BERT contextualized representations and (2) rich sememe representations from HowNet. We build heterogeneous graphs based on the multi-granularity lexicons and encode them with graph attention neural network (GAT). We also propose an innovative label-aware graph convolutional network (LGCN) for modeling the syntactic dependency arcs and labels simultaneously. Further, we present a sememe composition attention module for better injecting the sememe representations. Our system achieves new state-of-the-art CNER performances over current best baselines on four benchmark datasets. Further in-depth analysis has been conducted to reveal the contribution of each used resource, as well as the strengths of our proposed methods for the task improvements.</p>", "Keywords": "Natural language processing; Named entity recognition; Lexicon; Graph neural network", "DOI": "10.1007/s00521-021-06680-6", "PubYear": 2022, "Volume": "34", "Issue": "6", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Jingdezhen Ceramic University, Jingdezhen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Jingdezhen Ceramic University, Jingdezhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jingdezhen Ceramic University, Jingdezhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Jingdezhen Ceramic University, Jingdezhen, China"}], "References": [{"Title": "Adversarial shared-private model for cross-domain clinical text entailment recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "221", "Issue": "", "Page": "106962", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 92460455, "Title": "Machine learning model for mapping of music mood and human emotion based on physiological signals", "Abstract": "<p>Emotion is considered a physiological state that appears whenever a transformation is observed by an individual in their environment or body. While studying the literature, it has been observed that combining the electrical activity of the brain, along with other physiological signals for the accurate analysis of human emotions is yet to be explored in greater depth. On the basis of physiological signals, this work has proposed a model using machine learning approaches for the calibration of music mood and human emotion. The proposed model consists of three phases (a) prediction of the mood of the song based on audio signals, (b) prediction of the emotion of the human-based on physiological signals using EEG, GSR, ECG, Pulse Detector, and finally, (c) the mapping has been done between the music mood and the human emotion and classifies them in real-time. Extensive experimentations have been conducted on the different music mood datasets and human emotion for influential feature extraction, training, testing and performance evaluation. An effort has been made to observe and measure the human emotions up to a certain degree of accuracy and efficiency by recording a person’s bio- signals in response to music. Further, to test the applicability of the proposed work, playlists are generated based on the user’s real-time emotion determined using features generated from different physiological sensors and mood depicted by musical excerpts. This work could prove to be helpful for improving mental and physical health by scientifically analyzing the physiological signals.</p>", "Keywords": "Human emotions; Music mood; Physiological signal; Multimedia; Audio signals; Machine learning", "DOI": "10.1007/s11042-021-11650-0", "PubYear": 2022, "Volume": "81", "Issue": "4", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institute of Engineering and Technology, Patiala, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institute of Engineering and Technology, Patiala, India"}, {"AuthorId": 3, "Name": "Arman Beer Kaur", "Affiliation": "Computer Science and Engineering Department, Thapar Institute of Engineering and Technology, Patiala, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institute of Engineering and Technology, Patiala, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institute of Engineering and Technology, Patiala, India"}], "References": [{"Title": "A deep convolutional neural network model for automated identification of abnormal EEG signals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "20", "Page": "15857", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Music mood and human emotion recognition based on physiological signals: a systematic review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "1", "Page": "21", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 92460462, "Title": "End-to-end music emotion variation detection using iteratively reconstructed deep features", "Abstract": "<p>Automatic music emotion recognition (MER) has received increased attention in areas of music information retrieval and user interface development. Music emotion variation detection (or dynamic MER) captures also temporal changes of emotion, and emotional content in music is expressed as a series of valence-arousal predictions. One of the issues in MER is extraction of emotional characteristics from audio signal. We propose a deep neural network based solution for mining music emotion-related salient features directly from raw audio waveform. The proposed architecture is based on stacking one-dimensional convolution layer, autoencoder-based layer with iterative reconstruction, and bidirectional gated recurrent unit. The tests on the DEAM dataset have shown that the proposed solution, in comparison with other state-of-the-art systems, can bring a significant improvement of the regression accuracy, notably for the valence dimension. It is shown that the proposed iterative reconstruction layer is able to enhance the discriminative properties of the features and further increase regression accuracy.</p>", "Keywords": "Music emotion recognition; Arousal; Valence; End-to-end deep learning; Bi-directional gated recurrent unit; Iterative reconstruction", "DOI": "10.1007/s11042-021-11584-7", "PubYear": 2022, "Volume": "81", "Issue": "4", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "BrainIt.sk, Zilina, Slovakia;University of Zilina, Zilina, Slovakia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Zilina, Zilina, Slovakia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Zilina, Zilina, Slovakia"}], "References": [{"Title": "One deep music representation to rule them all? A comparative analysis of different representation learning strategies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "4", "Page": "1067", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Recognition of emotion in music based on deep convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "Page": "765", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 92460463, "Title": "Predicting the impact of online news articles – is information necessary?: Application to COVID-19 articles", "Abstract": "<p>We exploit the Twitter platform to create a dataset of news articles derived from tweets concerning COVID-19, and use the associated tweets to define a number of popularity measures. The focus on (potentially) biomedical news articles allows the quantity of biomedically valid information (as extracted by biomedical relation extraction) to be included in the list of explored features. Aside from forming part of a systematic correlation exploration, the features - ranging from the semantic relations through readability measures to the article's digital content - are used within a number of machine learning classifier and regression algorithms. Unsurprisingly, the results support that for more complex articles (as determined by a readability measure) more sophisticated syntactic structure may be expected. A weak correlation is found with information within an article suggesting that other factors, such as numbers of videos, have a notable impact on the popularity of a news article. The best popularity prediction performance is obtained using a random forest machine learning algorithm, and the feature describing the quantity of biomedical information is in the top 3 most important features in almost a third of the experiments performed. Additionally, this feature is found to be more valuable than the widely used named entity recognition.</p><p>© The Author(s) 2021.</p>", "Keywords": "Grammatical relations;Popularity prediction;SemRep relations;Twitter", "DOI": "10.1007/s11042-021-11621-5", "PubYear": 2023, "Volume": "82", "Issue": "6", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Engineering and Environment, University of Salford, Salford, M5 4WT UK."}], "References": [{"Title": "What types of COVID-19 conspiracies are populated by Twitter bots?", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "First Monday"}]}, {"ArticleId": 92460464, "Title": "Segmentation of the optic disc and optic cup using a machine learning-based biregional contour evolution model for the cup-to-disc ratio", "Abstract": "<p>Glaucoma, a chronic eye disease with irreversible blindness, is difficult to diagnose early. The cup-to-disc ratio is an important diagnostic index in glaucoma screening. Segmenting the optic disc and the optic cup is critical for glaucoma screening. Artificial glaucoma screening is time-consuming and laborious, so computer-aided function is important to save more human resources. In this paper, a biregional contour evolution model based on machine learning is proposed to segment the optic disc and the optic cup. To segment probability information in a more complete OD-OC, feature space is integrated into the edge indicator function by a machine learning method, and the intensity, edge, and region features of the OD-OC are obtained. In addition, the cup-to-disc ratio is calculated to diagnose glaucoma because it is currently practiced and can effectively assist doctors in clinical diagnosis. To evaluate the reliability and effectiveness of the proposed model, qualitative and quantitative results are obtained in the Dhristi-GS, DRIVE, and REFUGE datasets.</p>", "Keywords": "Glaucoma; Machine learning; Biregional contour evolution; Optic disc and optic cup segmentation; Cup-to-disc ratio", "DOI": "10.1007/s11042-021-11583-8", "PubYear": 2022, "Volume": "81", "Issue": "25", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing and Information Technology, Liaoning Normal University, Dalian City, China; Nanchang Institute of Technology, Nanchang City, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing and Information Technology, Liaoning Normal University, Dalian City, China"}], "References": [{"Title": "A Hybrid Active Contour Model based on New Edge-Stop Functions for Image Segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "87", "JournalTitle": "International Journal of Ambient Computing and Intelligence"}, {"Title": "MMSparse: 2D partitioning of sparse matrix based on mathematical morphology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "521", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Automated detection of Glaucoma using deep learning convolution network (G-net)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "15531", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Automatic segmentation of optic disc in retinal fundus images using semi-supervised deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>.", "PubYear": 2021, "Volume": "80", "Issue": "3", "Page": "3443", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 92461056, "Title": "Adaptively local consistent concept factorization for multi-view clustering", "Abstract": "<p>Many real-world datasets consist of multiple views of data items. The rough method of combining multiple views directly through feature concatenation cannot uncover the optimal latent structure shared by multiple views, which would benefit many data analysis applications. Recently, multi-view clustering methods have emerged and been applied to solving many machine learning problems. However, most multi-view clustering methods ignore the joint information of multi-view data or neglect the quality difference between different views of data, resulting in decreased learning performance. In this paper, we discuss a multi-view clustering algorithm based on concept factorization that effectively fuses useful information to derive a better representation for more effective clustering. We incorporate two regularizers into the concept factorization framework. Specifically, one regularizer is adopted to force the coefficient matrix to move smoothly on the underlying manifold. The other regularizer is used to learn the latent clustering structure from different views. Both of these regularizers are incorporated into the concept factorization framework to learn the latent representation matrix. Optimization problems are solved efficiently via an iterative algorithm. The experimental results on seven real-world datasets demonstrate that our approach outperforms the state-of-the-art multi-view clustering algorithms.</p>", "Keywords": "Concept factorization; Multi-view clustering; Local consistent", "DOI": "10.1007/s00500-021-06526-2", "PubYear": 2022, "Volume": "26", "Issue": "3", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Software Engineering, Jinling Institute of Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology and Joint International Research Laboratory of Machine Learning and Neuromorphic Computing, Soochow University, Suzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "School of Computer Science and Technology and Joint International Research Laboratory of Machine Learning and Neuromorphic Computing, Soochow University, Suzhou, China"}], "References": []}, {"ArticleId": 92461123, "Title": "A new wideband filtering power divider with ultra‐wide stopband using microstrip/slotline transitions", "Abstract": "<p>In this article, a wideband filtering power divider (FPD) is proposed based on a new design scheme originating from the microstrip/slotline broadside-coupled structure. It is simply composed of one microstrip T-junction and two transmission lines on the top layer, and a pair of U-shape slotline resonators as well as one isolation resistor-loaded slotline stub with a circular slot terminal in the ground plane. The presented FPD stands out by much wider bandwidth, improved performance, and simple design layout. For verification, one prototype FPD is designed and fabricated with a center frequency of 4.0 GHz and a fractional bandwidth of 77.1%. Simulated results coincide well with the measured ones. Results indicate that the new wideband FPD exhibits not only a simple layout without additional loading elements, but also nice port-to-port isolation over a whole wide frequency band range, good return losses, and 20-dB harmonic suppression up to (2.5 f <sub>0</sub>).</p>", "Keywords": "Wideband filtering power divider;high harmonic suppression;ultra-wideband isolation", "DOI": "10.1002/mmce.23073", "PubYear": 2022, "Volume": "32", "Issue": "4", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical and Automation Engineering, Nanjing Normal University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Automation Engineering, Nanjing Normal University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Automation Engineering, Nanjing Normal University, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical and Automation Engineering, Nanjing Normal University, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Automation Engineering, Nanjing Normal University, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Electrical and Automation Engineering, Nanjing Normal University, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Electrical and Automation Engineering, Nanjing Normal University, Nanjing, China"}], "References": []}, {"ArticleId": 92461150, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0140-3664(22)00005-6", "PubYear": 2022, "Volume": "183", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [], "References": []}, {"ArticleId": 92461426, "Title": "Digital cultural heritage standards: from silo to semantic web", "Abstract": "This paper is a survey of standards being used in the domain of digital cultural heritage with focus on the Metadata Encoding and Transmission Standard (METS) created by the Library of Congress in the United States of America. The process of digitization of cultural heritage requires silo breaking in a number of areas—one area is that of academic disciplines to enable the performance of rich interdisciplinary work. This lays the foundation for the emancipation of the second form of silo which are the silos of knowledge, both traditional and born digital, held in individual institutions, such as galleries, libraries, archives and museums. Disciplinary silo breaking is the key to unlocking these institutional knowledge silos. Interdisciplinary teams, such as developers and librarians, work together to make the data accessible as open data on the “semantic web”. Description logic is the area of mathematics which underpins many ontology building applications today. Creating these ontologies requires a human–machine symbiosis. Currently in the cultural heritage domain, the institutions’ role is that of provider of this  open data to the national aggregator which in turn can make the data available to the trans-European aggregator known as Europeana. Current ingests to the aggregators are in the form of machine readable cataloguing metadata which is limited in the richness it provides to disparate object descriptions. METS can provide this richness.", "Keywords": "MARC;METS metadata;Metadata aggregators;Open-linked data;Semantic web;Silo", "DOI": "10.1007/s00146-021-01371-1", "PubYear": 2022, "Volume": "37", "Issue": "3", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computing and Mathematics, INSYTE Centre, Waterford Institute of Technology, Waterford, Ireland."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computing and Mathematics, INSYTE Centre, Waterford Institute of Technology, Waterford, Ireland."}], "References": [{"Title": "The ENRICHER Method for Human Machine Symbiotics & Smart Data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "17445", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Social Responsibility, Human Centred Systems and Engineering Ethics:", "Authors": "<PERSON><PERSON>; <PERSON>.", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "17451", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 92461427, "Title": "Relative density and surface roughness prediction for Inconel 718 by selective laser melting: central composite design and multi-objective optimization", "Abstract": "<p>Selective laser melting (SLM) is a dominant additive manufacturing (AM) technique for metal components in industry. For SLM produced parts, relative density and surface roughness are among the most important and convenient quality indicators. This study, for the first time, adopts central composite design (CCD) to systematically investigate the effect of four key SLM process parameters, namely, laser power, scan speed, hatch spacing, and layer thickness, on the relative density and surface roughness of SLM-ed Inconel 718. Prediction models for relative density and surface roughness are generated, and ANOVA analysis is conducted. It is found that the relative density model, in a reduced quadratic form, presents satisfactory prediction accuracy with MAE of 0.35, RMSE of 0.43, and R <sup>2</sup> of 70.14%; the surface roughness model, in a reduced linear form, shows good prediction accuracy with MAE of 1.07, RMSE of 1.37, and R <sup>2</sup> of 74.36%. Based on the response surface plots, the desired process windows are found respectively. In addition, three multi-objective optimization approaches are developed to address the simultaneous maximization of relative density and minimization of surface roughness. It is suggested that they are competing objectives and a balanced optimality will depend on the actual requirements.</p>", "Keywords": "Inconel 718; Selective laser melting; Relative density; Surface roughness; Response surface method; Prediction and process optimization", "DOI": "10.1007/s00170-021-08388-2", "PubYear": 2022, "Volume": "119", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Lu", "Affiliation": "Department of Mechanical and Materials Engineering, College of Engineering and Applied Science, University of Cincinnati, Cincinnati, USA"}, {"AuthorId": 2, "Name": "Jing Shi", "Affiliation": "Department of Mechanical and Materials Engineering, College of Engineering and Applied Science, University of Cincinnati, Cincinnati, USA"}], "References": [{"Title": "Optimization of selective laser melting process parameters for surface quality performance of the fabricated Ti6Al4V", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; N<PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "5-6", "Page": "1585", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 92461428, "Title": "Hand–eye calibration method based on three-dimensional visual measurement in robotic high-precision machining", "Abstract": "<p>When a three-dimensional (3D)-vision sensor is adopted to achieve high-precision measurement-assisted robotic machining, hand–eye calibration is necessary to determine the position and orientation between the 3D-vision sensor and the robot end-effector. However, errors exist in hand–eye calibration data, including robot poses and sensor measurement, which can seriously affect the solution accuracy for hand–eye transformation parameters. The main objective of this paper is to present an accurate method based on 3D visual measurement considering the effects of errors on modeling in nonlinear optimization and initial value calculation to improve the solution accuracy. Compared with a model established in the robot base frame, a nonlinear calibration model with a more accurate reference point in the sensor frame is established, which offers the possibility of a more accurate solution. An iterative weight optimization solution combined with the random errors of sensor measurement and robot position is proposed. To obtain an accurate initial value for iteration, considering the measurement error of reference in the base frame, a calibration model based on surveying adjustment is proposed and is solved using Lagrange multipliers. Simulations and experiments are conducted to test the superiority of the proposed methods. Finally, an experiment on measurement-assisted robotic machining is carried out to further demonstrate the validation.</p>", "Keywords": "Measurement-assisted robotic machining; Hand–eye calibration; Three-dimensional visual measurement; Industrial robot", "DOI": "10.1007/s00170-021-08591-1", "PubYear": 2022, "Volume": "119", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "Jinsheng Fu", "Affiliation": "Key Laboratory of Mechanism Theory and Equipment Design of Ministry of Education, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Mechanism Theory and Equipment Design of Ministry of Education, Tianjin University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Mechanism Theory and Equipment Design of Ministry of Education, Tianjin University, Tianjin, China;School of Engineering, University of Warwick, Coventry, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Mechanism Theory and Equipment Design of Ministry of Education, Tianjin University, Tianjin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, University of Warwick, Coventry, UK"}], "References": [{"Title": "Robust hand-eye calibration of 2D laser sensors using a single-plane calibration artefact", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101823", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Robotic grinding of complex components: A step towards efficient and intelligent machining – challenges, solutions, and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "", "Page": "101908", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Hand-eye calibration method with a three-dimensional-vision sensor considering the rotation parameters of the robot pose", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "6", "Page": "172988142097729", "JournalTitle": "International Journal of Advanced Robotic Systems"}]}, {"ArticleId": 92461434, "Title": "Does Comma Selection Help to Cope with Local Optima?", "Abstract": "<p>One hope when using non-elitism in evolutionary computation is that the ability to abandon the current-best solution aids leaving local optima. To improve our understanding of this mechanism, we perform a rigorous runtime analysis of a basic non-elitist evolutionary algorithm (EA), the \\((\\mu ,\\lambda )\\) EA, on the most basic benchmark function with a local optimum, the jump function. We prove that for all reasonable values of the parameters and the problem, the expected runtime of the \\((\\mu ,\\lambda )\\) EA is, apart from lower order terms, at least as large as the expected runtime of its elitist counterpart, the \\((\\mu +\\lambda )\\) EA (for which we conduct the first runtime analysis on jump functions to allow this comparison). Consequently, the ability of the \\((\\mu ,\\lambda )\\) EA to leave local optima to inferior solutions does not lead to a runtime advantage. We complement this lower bound with an upper bound that, for broad ranges of the parameters, is identical to our lower bound apart from lower order terms. This is the first runtime result for a non-elitist algorithm on a multi-modal problem that is tight apart from lower order terms. </p>", "Keywords": "", "DOI": "10.1007/s00453-021-00896-7", "PubYear": 2022, "Volume": "84", "Issue": "6", "JournalId": 2779, "JournalTitle": "Algorithmica", "ISSN": "0178-4617", "EISSN": "1432-0541", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratoire d’Informatique (LIX), CNRS, École Polytechnique, Institut Polytechnique de Paris, Palaiseau, France"}], "References": [{"Title": "Memetic algorithms outperform evolutionary algorithms in multimodal optimisation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "287", "Issue": "", "Page": "103345", "JournalTitle": "Artificial Intelligence"}, {"Title": "Runtime Analysis for Self-adaptive Mutation Rates", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "83", "Issue": "4", "Page": "1012", "JournalTitle": "Algorithmica"}, {"Title": "Multiplicative Up-Drift", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "83", "Issue": "10", "Page": "3017", "JournalTitle": "Algorithmica"}]}, {"ArticleId": 92461435, "Title": "Fast Exact Algorithms Using Hadamard Product of Polynomials", "Abstract": "<p>Let C be an arithmetic circuit of size s , given as input that computes a polynomial \\(f\\in {\\mathbb {F}}[x_1,x_2,\\ldots ,x_n]\\) , where \\({\\mathbb {F}}\\) is a finite field or the field of rationals. Using the Hadamard product of polynomials, we obtain new algorithms for the following two problems first studied by <PERSON><PERSON><PERSON> and <PERSON> (Faster algebraic algorithms for path and packing problems, 2008, https://doi.org/10.1007/978-3-540-70575-8_47 ; ACM Trans Algorithms 12(3):31:1–31:18, 2016, https://doi.org/10.1145/2885499 ; Inf Process Lett 109(6):315–318, 2009, https://doi.org/10.1016/j.ipl.2008.11.004 ):</p> <p> \\({{{(\\textit{k,n}){-}\\mathrm{M{L}\\normalsize {C}}}}}\\) : is the problem of computing the sum of the coefficients of all degree- k multilinear monomials in the polynomial f . We obtain a deterministic algorithm of running time \\({n\\atopwithdelims (){\\downarrow k/2}}\\cdot n^{O(\\log k)}\\cdot s^{O(1)}\\) . This improvement over the \\(O(n^k)\\) time brute-force search algorithm answers positively a question of <PERSON>utis and <PERSON> (2016). As applications, we give exact counting algorithms, faster than brute-force search, for counting the number of copies of a tree of size k in a graph, and also the problem of exact counting of m -dimensional k -matchings.</p> <p> \\({{{\\textit{k}{-}\\mathrm{M{M}\\normalsize {D}}}}}\\) : is the problem of checking if there is a degree- k multilinear monomial in the polynomial f with non-zero coefficient. We obtain a randomized algorithm of running time \\(O(4.32^k\\cdot n^{O(1)})\\) . Additionally, our algorithm is polynomial space bounded.</p> <p> Other results include fast deterministic algorithms for \\({{{(\\textit{k,n}){-}\\mathrm{M{L}\\normalsize {C}}}}}\\) and \\({{{\\textit{k}{-}\\mathrm{M{M}\\normalsize {D}}}}}\\) problems for depth three circuits.</p>", "Keywords": "Parameterized complexity; Multilinear monomial detection; Multilinear monomial counting; Arithmetic circuits", "DOI": "10.1007/s00453-021-00900-0", "PubYear": 2022, "Volume": "84", "Issue": "2", "JournalId": 2779, "JournalTitle": "Algorithmica", "ISSN": "0178-4617", "EISSN": "1432-0541", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mathematical Sciences (HBNI), Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Mathematical Sciences (HBNI), Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Chennai Mathematical Institute, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Chennai Mathematical Institute, Chennai, India"}], "References": [{"Title": "Fine-Grained Reductions from Approximate Counting to Decision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Computation Theory"}]}, {"ArticleId": 92461436, "Title": "Analyzing the impact of the MPI allreduce in distributed training of convolutional neural networks", "Abstract": "<p>For many distributed applications, data communication poses an important bottleneck from the points of view of performance and energy consumption. As more cores are integrated per node, in general the global performance of the system increases yet eventually becomes limited by the interconnection network. This is the case for distributed data-parallel training of convolutional neural networks (CNNs), which usually proceeds on a cluster with a small to moderate number of nodes. In this paper, we analyze the performance of the Allreduce collective communication primitive, a key to the efficient data-parallel distributed training of CNNs. Our study targets the distinct realizations of this primitive in three high performance instances of Message Passing Interface (MPI), namely MPICH, OpenMPI, and IntelMPI, and employs a cluster equipped with state-of-the-art processor and network technologies. In addition, we apply the insights gained from the experimental analysis to the optimization of the TensorFlow framework when running on top of Horovod. Our study reveals that a careful selection of the most convenient MPI library and Allreduce (ARD) realization accelerates the training throughput by a factor of (1.2\times ) compared with the default algorithm in the same MPI library, and up to (2.8\times ) when comparing distinct MPI libraries in a number of relevant combinations of CNN model+dataset. </p>", "Keywords": "Message passing interface (MPI); Collective communication primitives; Allreduce; Deep learning; Distributed training; 6804", "DOI": "10.1007/s00607-021-01029-2", "PubYear": 2023, "Volume": "105", "Issue": "5", "JournalId": 10374, "JournalTitle": "Computing", "ISSN": "0010-485X", "EISSN": "1436-5057", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitat Politècnica de València, Valencia, Spain"}, {"AuthorId": 2, "Name": "Mar Catalán", "Affiliation": "Universitat Jaume I, Castellón de la Plana, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universitat Jaume I, Castellón de la Plana, Spain"}, {"AuthorId": 4, "Name": "<PERSON>-Ortí", "Affiliation": "Universitat Politècnica de València, Valencia, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Universitat Politècnica de València, Valencia, Spain"}], "References": [{"Title": "Demystifying Parallel and Distributed Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 92461440, "Title": "Enhancing firefly algorithm with adaptive multi-group mechanism", "Abstract": "<p>Firefly algorithm (FA) is efficient in solving continuous optimal problems, because of its ability to a global search. However, the redundant attractions and incorrect directions may reduce the efficiency of FA. To improve the performance of FA, a novel multi-group mechanism is proposed based on an assumption that firefly has a visual field. The modified firefly algorithm is called the visual firefly algorithm(VFA). The framework of VFA combines the assumption with the designed strategies to balance the exploration and exploitation. Where the proposed observer strategy works for the exploration, the suggested selective random strategy plays the role of the exploiter. To verify the performance of the presented algorithm, extensive experiments are executed on CEC2013 benchmark functions. Additionally, the efficiency of the proposed multi-group mechanism is analyzed in-depth. The experimental results reveal that the proposed multi-group mechanism improves FA and provides a suitable solution for most CEC2013 problems with different dimensions. Especially, its performance remains robust, where the problems become more complex.</p>", "Keywords": "Optimization; Firefly algorithm; Multi-group; Efficiency", "DOI": "10.1007/s10489-021-02766-9", "PubYear": 2022, "Volume": "52", "Issue": "9", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Engineering, Naval University of Engineering, WuHan, China; School of Computer and Big Data Science, Jiujiang University, Jiujiang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Engineering, Naval University of Engineering, WuHan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer and Big Data Science, Jiujiang University, Jiujiang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Engineering, Naval University of Engineering, WuHan, China"}], "References": [{"Title": "ANN optimized by PSO and Firefly algorithms for predicting scour depths around bridge piers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "293", "JournalTitle": "Engineering with Computers"}, {"Title": "An under‐sampled software defect prediction method based on hybrid multi‐objective cuckoo search", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Multi-strategy brain storm optimization algorithm with dynamic parameters adjustment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "4", "Page": "1289", "JournalTitle": "Applied Intelligence"}, {"Title": "An improved firefly algorithm for global continuous optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113340", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A clustering-based differential evolution algorithm for solving multimodal multi-objective optimization problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100788", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 92461443, "Title": "Estimation of most effected cycles and busiest network route based on complexity function of graph in fuzzy environment", "Abstract": "<p>Connectivity and strength has a major role in the field of network connecting with real world life. Complexity function is one of these parameter which has manifold number of applications in molecular chemistry and the theory of network. Firstly, this paper introduces the thought of complexity function of fuzzy graph with its properties. Second, based on the highest and lowest load on a network system, the boundaries of complexity function of different types of fuzzy graphs are established. Third, the behavior of complexity function in fuzzy cycle, fuzzy tree and complete fuzzy graph are discussed with their properties. Fourth, applications of these thoughts are bestowed to identify the most effected COVID-19 cycles between some communicated countries using the concept of complexity function of fuzzy graph. Also the selection of the busiest network stations and connected internet paths can be done using the same concept in a graphical wireless network system.</p><p>© The Author(s), under exclusive licence to Springer Nature B.V. 2021.</p>", "Keywords": "Boundaries;COVID-19;Complexity function;Cycle;Fuzzy graph;Internet routing", "DOI": "10.1007/s10462-021-10111-2", "PubYear": 2022, "Volume": "55", "Issue": "6", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "Soumitra Poulik", "Affiliation": "Department of Applied Mathematics with Oceanology and Computer Programming, Vidyasagar University, Midnapore, 721102 West Bengal India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics with Oceanology and Computer Programming, Vidyasagar University, Midnapore, 721102 West Bengal India."}], "References": [{"Title": "Certain indices of graphs under bipolar fuzzy environment with applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "7", "Page": "5119", "JournalTitle": "Soft Computing"}, {"Title": "Domination integrity and efficient fuzzy graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "14", "Page": "10263", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Note on “Bipolar fuzzy graphs with applications”", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "105315", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Pragmatic results in Taiwan education system based IVFG & IVNG", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "711", "JournalTitle": "Soft Computing"}, {"Title": "Determination of journeys order based on graph’s Wiener absolute index with bipolar fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "545", "Issue": "", "Page": "608", "JournalTitle": "Information Sciences"}, {"Title": "Colouring of COVID-19 Affected Region Based on Fuzzy Directed Graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "1", "Page": "1219", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 92461444, "Title": "I2DKPCN: an unsupervised deep learning network", "Abstract": "<p>In this paper, we proposed an incremental two-dimensional kernel PCA-based convolutional network (I2DKPCN) which is a novel unsupervised deep learning network. In our architecture, I2DKPCN consists of several feature extraction stages and one output stage, and each feature extraction stage includes a convolutional layer, a feature pooling layer and a feature fusion layer. In the output stage, binary hashing and blockwise histograms are exploited for the generation of the final features. In particular, the filters of the convolutional layer are learned by using incremental two-dimensional kernel principal component analysis(I2DKPCA) rather than the gradient-based optimization. Due to the fact that the back propagation is not used to learn the parameters of the filter, the calculation speed of I2DKPCN is much faster than that of the existing deep network. We have extensively tested I2DKPCN on multiple data sets for three challenging tasks, including hand-written digit recognition, texture classification and face recognition. It can be seen from the results that I2DKPCN performs competitively or even better compared with other networks in all tests. Because the filters are learned by I2DKPCA, which combined the 2DPCA with kernel method and incremental learning, the non-linear problem was solved and the computational time was reduced.</p>", "Keywords": "Deep learning; Convolutional network; Unsupervised mode; I2DKPCA", "DOI": "10.1007/s10489-021-03007-9", "PubYear": 2022, "Volume": "52", "Issue": "9", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Tongji University, Shanghai, China"}, {"AuthorId": 2, "Name": "Fanhuai Shi", "Affiliation": "College of Electronics and Information Engineering, Tongji University, Shanghai, China"}], "References": [{"Title": "Computer aided Alzheimer's disease diagnosis by an unsupervised deep learning technology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "392", "Issue": "", "Page": "296", "JournalTitle": "Neurocomputing"}, {"Title": "Novel approaches to one-directional two-dimensional principal component analysis in hybrid pattern framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>ul Negi", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "4897", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Using high-fidelity meta-models to improve performance of small dataset trained Bayesian Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "112830", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Residual deep PCA-based feature extraction for hyperspectral image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "18", "Page": "14287", "JournalTitle": "Neural Computing and Applications"}, {"Title": "MDFN: Multi-scale deep feature learning network for object detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "", "Page": "107149", "JournalTitle": "Pattern Recognition"}, {"Title": "A novel non-linear modifier for adaptive illumination normalization for robust face recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "Page": "11503", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Autoencoder-based unsupervised clustering and hashing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "1", "Page": "493", "JournalTitle": "Applied Intelligence"}, {"Title": "DWE-IL: a new incremental learning algorithm for non-stationary time series prediction via dynamically weighting ensemble learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "1", "Page": "174", "JournalTitle": "Applied Intelligence"}, {"Title": "Neuroevolution in Deep Neural Networks: Current Trends and Future Challenges", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "6", "Page": "476", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}]}, {"ArticleId": 92461457, "Title": "Identification of oil authenticity and adulteration using deep long short-term memory-based neural network with seagull optimization algorithm", "Abstract": "<p>One of the most important aspects of people's everyday diet is edible oils. Good quality cooking oil plays a key role in one's health. Due to the increased demand for oil in both the international and domestic markets, vendors often mix the high-quality oil with low-quality ones causing adulteration which is a serious issue to be solved. Thus, qualified (authentic or pure) edible oils are expensive. Gall bladder cancer is mainly caused when the oil is adulterated with butter yellow, argemone oil, mixing good quality oil with low-quality oils, and wrong ingredients with fraudulent labeling. In the past decades, spectrophotometric methods and machine learning techniques are utilized for adulteration and authenticity identification of sunflower oil, olive oil, corn oil, coconut oil, mustard oil, soybean oils. Nevertheless, the performance of these methods is decreased due to data imbalance, overfitting, higher cost, more execution time, computational complexity, and inaccurate classification. To tackle these issues, we have proposed Deep Long Short-Term Memory (LSTM) neural network with a Seagull Optimization Algorithm (SOA) for the authenticity and adulteration of edible oils classification. In this study, 5 kinds of edible oils such as coconut oil, rice oil, sesame oil, sunflower oil, and Olive oil are used. Each of the oil samples was kept in the refrigerator at 4 °C. During data acquisition, the proton resonance frequency was 19.91 MHz and the magnetic field strength was 0.467 T. The obtained signals are applied for edible oil classification, which is handled using a deep LSTM neural network with SOA. Based on the experimental investigation, the proposed method accomplished superior performances than existing methods including LFNMR-CNN, LFNMR-SVM, DLC, Pre-trained CNN.</p>", "Keywords": "Edible oils; Seagull optimization algorithm; Deep LSTM neural network; Authenticity; Adulteration", "DOI": "10.1007/s00521-021-06829-3", "PubYear": 2022, "Volume": "34", "Issue": "10", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering, SRM Institute of Science and Technology, Ramapuram, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Engineering, SRM Institute of Science and Technology, Ramapuram, Chennai, India"}], "References": [{"Title": "An enhanced learning algorithm with a particle filter-based gradient descent optimizer method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12789", "JournalTitle": "Neural Computing and Applications"}, {"Title": "IoT based home monitoring system with secure data storage by <PERSON><PERSON>k–Chaotic sequence in cloud server", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "7", "Page": "7475", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "MOSOA: A new multi-objective seagull optimization algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114150", "JournalTitle": "Expert Systems with Applications"}, {"Title": "CSCF: a chaotic sine cosine firefly algorithm for practical application problems", "Authors": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "12", "Page": "7011", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Performance evaluation of adaptive neuro fuzzy system (ANFIS) over fuzzy inference system (FIS) with optimization algorithm in de-noising of images from salt and pepper noise", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "CNN‐OHGS: CNN‐oppositional‐based Henry gas solubility optimization model for autonomous vehicle control system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "7", "Page": "967", "JournalTitle": "Journal of Field Robotics"}]}, {"ArticleId": 92461459, "Title": "Forecasting the abnormal events at well drilling with machine learning", "Abstract": "<p>We present a data-driven and physics-informed algorithm for drilling accident forecasting. The core machine-learning algorithm uses the data from the drilling telemetry representing the time-series. We have developed a Bag-of-features representation of the time series that enables the algorithm to predict the probabilities of six types of drilling accidents in real-time. The machine-learning model is trained on the 125 past drilling accidents from 100 different Russian oil and gas wells. Validation shows that the model can forecast 70% of drilling accidents with a false positive rate equals to 40%. The model addresses partial prevention of the drilling accidents at the well construction.</p>", "Keywords": "Bag-of-features; Directional drilling; Machine learning; Classification; Telemetry", "DOI": "10.1007/s10489-021-03013-x", "PubYear": 2022, "Volume": "52", "Issue": "9", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Skolkovo Institute of Science and Technology (Skoltech), Moscow, Russia; Digital Petroleum, Skolkovo Innovation Center, Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Skolkovo Institute of Science and Technology (Skoltech), Moscow, Russia; Digital Petroleum, Skolkovo Innovation Center, Moscow, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Skolkovo Institute of Science and Technology (Skoltech), Moscow, Russia; Digital Petroleum, Skolkovo Innovation Center, Moscow, Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Skolkovo Institute of Science and Technology (Skoltech), Moscow, Russia; Digital Petroleum, Skolkovo Innovation Center, Moscow, Russia"}], "References": [{"Title": "Artificial intelligence, machine learning and process automation: existing knowledge frontier and way forward for mining sector", "Authors": "Danish <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "6025", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Adaptively constrained dynamic time warping for time series classification and clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "534", "Issue": "", "Page": "97", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 92461862, "Title": "Automatic calving front extraction from digital elevation model-derived data", "Abstract": "Calving Front (CF) is an important parameter to analyse ice sheet dynamics and to measure glacier mass balance. DEM products covering polar regions are critical remote sensing data sources to provide fundamental terrain information for glaciological studies. It is necessary to provide accurate CFs for DEM specific applications, such as mass balance calculation or the substitution of water values with geoid values in the DEM editing process. However, much less attention is paid to automatically delineate CFs from DEM data. In this study, we propose a DEM-based Automatic CF Extraction method (DACE) to efficiently extract the CFs from DEM data. In DACE, a height-sensitive terrain feature is designed to enhance the contrast between the ice sheet and the ocean by combining elevation and roughness information. To improve the CF extraction performance, a two-category image classification based on game theory is proposed that considers the spatial consistency of the feature image created. For validation, DACE was applied to DEM products generated from the single-pass SAR interferometry mission TanDEM-X (TDM) at different posting sizes (12 and 90 m) and to the optical photogrammetry-based Reference Elevation Model of Antarctica (REMA) with a 2 m posting. The proposed algorithm can achieve a CF extraction accuracy of better than 14, 20, and 70 m for the 2-m REMA, 12- and 90-m TDM DEMs, respectively, when compared with the manually delineated CFs. The experimental results demonstrate that the proposed algorithm can effectively extract the CFs from DEM data. DACE can be used to replace water values and edit the DEMs themselves. The CFs extracted from the DEM data can also be used for glacier mass balance calculation with the DEM-based geodetic method and for the temporal analysis of CF changes when multi-temporal DEM data are available.", "Keywords": "Image classification ; Digital Elevation Model (DEM) ; Calving Front ; Height-sensitive terrain feature ; Remote sensing ; TanDEM-X DEM ; REMA", "DOI": "10.1016/j.rse.2021.112854", "PubYear": 2022, "Volume": "270", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Geography and Information Engineering, China University of Geosciences, Wuhan 430074, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, China University of Geosciences, Wuhan 430074, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Remote Sensing Technology Institute (IMF), German Aerospace Center (DLR), Oberpfaffenhofen 82234, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Remote Sensing Technology Institute (IMF), German Aerospace Center (DLR), Oberpfaffenhofen 82234, Germany"}], "References": [{"Title": "Drainage basin delineation for outlet glaciers of Northeast Greenland based on Sentinel-1 ice velocities and TanDEM-X elevations", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111483", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "An automated, generalized, deep-learning-based method for delineating the calving fronts of Greenland glaciers from multi-sensor remote sensing imagery", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "254", "Issue": "", "Page": "112265", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Quantarctica, an integrated mapping environment for Antarctica, the Southern Ocean, and sub-Antarctic islands", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "140", "Issue": "", "Page": "105015", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 92461912, "Title": "Mobility in Unsupervised Word Embeddings for Knowledge Extraction—The Scholars’ Trajectories across Research Topics", "Abstract": "<p>In the knowledge discovery field of the Big Data domain the analysis of geographic positioning and mobility information plays a key role. At the same time, in the Natural Language Processing (NLP) domain pre-trained models such as BERT and word embedding algorithms such as Word2Vec enabled a rich encoding of words that allows mapping textual data into points of an arbitrary multi-dimensional space, in which the notion of proximity reflects an association among terms or topics. The main contribution of this paper is to show how analytical tools, traditionally adopted to deal with geographic data to measure the mobility of an agent in a time interval, can also be effectively applied to extract knowledge in a semantic realm, such as a semantic space of words and topics, looking for latent trajectories that can benefit the properties of neural network latent representations. As a case study, the Scopus database was queried about works of highly cited researchers in recent years. On this basis, we performed a dynamic analysis, for measuring the Radius of Gyration as an index of the mobility of researchers across scientific topics. The semantic space is built from the automatic analysis of the paper abstracts of each author. In particular, we evaluated two different methodologies to build the semantic space and we found that Word2Vec embeddings perform better than the BERT ones for this task. Finally, The scholars’ trajectories show some latent properties of this model, which also represent new scientific contributions of this work. These properties include (i) the correlation between the scientific mobility and the achievement of scientific results, measured through the H-index; (ii) differences in the behavior of researchers working in different countries and subjects; and (iii) some interesting similarities between mobility patterns in this semantic realm and those typically observed in the case of human mobility.</p>", "Keywords": "word embedding; semantic space; knowledge discovery; Word2vec; bert; human mobility; <PERSON><PERSON> of Gyration word embedding ; semantic space ; knowledge discovery ; Word2vec ; bert ; human mobility ; <PERSON><PERSON> of Gyration", "DOI": "10.3390/fi14010025", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "These authors contributed equally to this work"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "These authors contributed equally to this work"}, {"AuthorId": 4, "Name": "Gaia Codeluppi", "Affiliation": "These authors contributed equally to this work"}, {"AuthorId": 5, "Name": "Agos<PERSON>", "Affiliation": "Author to whom correspondence should be addressed"}], "References": [{"Title": "Tracking urban geo-topics based on dynamic topic model", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "", "Page": "101419", "JournalTitle": "Computers, Environment and Urban Systems"}, {"Title": "A Survey on Troll Detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "2", "Page": "31", "JournalTitle": "Future Internet"}, {"Title": "Neural network embeddings on corporate annual filings for portfolio selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "114053", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Continual representation learning for node classification in power-law graphs", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "420", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 92462031, "Title": "Pairwise feature-based generative adversarial network for incomplete multi-modal Alzheimer’s disease diagnosis", "Abstract": "<p>Magnetic resonance imaging (MRI) and positron emission tomography (PET) are widely used in diagnosis of Alzheimer’s disease (AD). In practice, incomplete modality problem is unavoidable due to the cost of data acquisition. Deep learning based models especially generative adversarial networks (GAN) are usually adopted to impute missing images. However, there are still some problems: (1) there are many regions unrelated to the disease and have little significance in the actual diagnosis in brain images, which are very cumbersome to generate. (2) The image generated by GAN would introduce noises causing the poor performance in the diagnostic model. To address these problems, a pairwise feature-based generation adversarial network is proposed. Specifically, features from the original brain images are extracted firstly. For the paired data without modality loss, the extracted MRI features are used as input to generate its corresponding PET features, which not only reduces the scale of the model, but also ensures the direct correlation between the generated features and the diagnosis. In addition, the available real PET features of the paired samples are added as label to constrain the generated ones. Finally, the attention mechanism is adopted in both the generator and discriminator, which can effectively retain the structural information of the feature itself. A large number of experiments have demonstrated that our proposed method has achieved promising results in the diagnosis of AD.</p>", "Keywords": "Incomplete multi-modal; GAN; AD; Medical imaging", "DOI": "10.1007/s00371-021-02354-5", "PubYear": 2023, "Volume": "39", "Issue": "6", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "Haizhou Ye", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indeed Singapore Pte Ltd, Singapore, Singapore"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}], "References": [{"Title": "Deep learning approach for microarray cancer data classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "1", "Page": "22", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "Neural saliency algorithm guide bi‐directional visual perception style transfer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "A two‐branch network with pyramid‐based local and spatial attention global feature learning for vehicle re‐identification", "Authors": "<PERSON><PERSON> Yang; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "1", "Page": "46", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": 92462055, "Title": "Attentional and adversarial feature mimic for efficient object detection", "Abstract": "<p>In this paper, we focus on learning efficient object detectors by knowledge (or network) distillation. More specifically, we mimic features from deeper and larger teacher networks to help train better efficient student networks. Unlike the previous method that mimics features through minimizing an L2 loss between feature generated by teacher and student networks, we propose an attentional and adversarial feature mimic (AAFM) method which consists of an attentional feature mimic module and an adversarial feature mimic module, where the former module uses an attentional L2 loss which learns to pay attention to important object-related regions for feature mimic, and the latter module uses an adversarial loss which makes features generated by teacher and student networks have similar distributions. We apply our AAFM method in the two-stage Faster R-CNN detector. Experiments on the PASCAL VOC 2007 and COCO datasets show that our method consistently improves the performance of detectors without feature mimic or with other feature mimic methods. In particular, our method obtains 72.1% mAP on the PASCAL VOC 2007 dataset using the ResNet-18-based detector. </p>", "Keywords": "Object detection; Knowledge distillation; Attention network; Feature mimic", "DOI": "10.1007/s00371-021-02363-4", "PubYear": 2023, "Volume": "39", "Issue": "2", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "Hong<PERSON> Wang", "Affiliation": "Jiangsu Frontier Electric Power Technology Co., Ltd., Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Frontier Electric Power Technology Co., Ltd., Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Jiangsu Frontier Electric Power Technology Co., Ltd., Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Frontier Electric Power Technology Co., Ltd., Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Jiangsu Frontier Electric Power Technology Co., Ltd., Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Information and Communications, Huazhong University of Science and Technology, Wuhan, China"}], "References": [{"Title": "A single-shot multi-level feature reused neural network for object detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "133", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 92462095, "Title": "Deep Reinforcement Learning-Based Workload Scheduling for Edge Computing", "Abstract": "Edge computing is a new paradigm for providing cloud computing capacities at the edge of network near mobile users. It offers an effective solution to help mobile devices with computation-intensive and delay-sensitive tasks. However, the edge of network presents a dynamic environment with large number of devices, high mobility of users, heterogeneous applications and intermittent traffic. In such environment, edge computing often suffers from unbalance resource allocation, which leads to task failure and affects system performance. To tackle this problem, we proposed a deep reinforcement learning(DRL)-based workload scheduling approach with the goal of balancing the workload, reducing the service time and the failed task rate. Meanwhile, We adopt Deep-Q-Network(DQN) algorithms to solve the complexity and high dimension of workload scheduling problem. Simulation results show that our proposed approach achieves the best performance in aspects of service time, virtual machine(VM) utilization, and failed tasks rate compared with other approaches. Our DRL-based approach can provide an efficient solution to the workload scheduling problem in edge computing.", "Keywords": "Computer Communication Networks", "DOI": "10.1186/s13677-021-00276-0", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China;College of Information Science and Technology, Zhejiang Shuren University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Jiang", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}], "References": []}, {"ArticleId": 92462105, "Title": "A Genetic Algorithm-Based Approach for the Inspection Scheduling Planning in Power Distribution Networks", "Abstract": "<p>The rising demand for energy and an still increasing world population are prompting the need to further improve power supply services upon a “zero interruption” target. This, combined with the process of shifting fossil fuel usage toward electrification, may escalate the economic and social impacts caused from interruptions. In this context, scheduled inspection plays a major role to manage risks. The challenge, however, is the trade-off of low O&M costs and high reliability. To solve that, this paper proposes a reliability-centered maintenance approach that combines heuristic and genetic algorithm (GA). The heuristic, based on benchmark practices, determines the cumulative total line length to be inspected to reach a targeted reliability level. The GA, on the other hand, yields a priority order list to portions of the grid to be inspected. The methodology relies on corporate systems historic data from outage management system, supervisory control and data acquisition, geographic information system and enterprise resource planning, typically available on power utilities. Tests are performed to a real 13.8 kV network in the South West of Brazil. Results demonstrate that the proposed methodology can significantly improve the decision-making by prioritizing feeder segments with higher load density, number of interruptions and voltage/current problems, up to a preset target, thus, improving reliability while limiting O&M costs.</p>", "Keywords": "Inspection; Prioritization; Maintenance; Distribution networks; Genetic algorithm", "DOI": "10.1007/s40313-021-00887-7", "PubYear": 2022, "Volume": "33", "Issue": "4", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electric Energy and Automation Engineering, University of Sao Paulo, USP/ENERQ, Sao Paulo, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electric Energy and Automation Engineering, University of Sao Paulo, USP/ENERQ, Sao Paulo, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electric Energy and Automation Engineering, University of Sao Paulo, USP/ENERQ, Sao Paulo, Brazil"}, {"AuthorId": 4, "Name": "Ananda Andrade Nascimento", "Affiliation": "Department of Electric Energy and Automation Engineering, University of Sao Paulo, USP/ENERQ, Sao Paulo, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electric Energy and Automation Engineering, University of Sao Paulo, USP/ENERQ, Sao Paulo, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Electric Energy and Automation Engineering, University of Sao Paulo, USP/ENERQ, Sao Paulo, Brazil"}], "References": [{"Title": "Reliability-Centered Maintenance Task Planning for Overhead Electric Power Distribution Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "5", "Page": "1278", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}, {"Title": "Maintenance Planning of Electric Distribution Systems—A Review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "186", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}]}, {"ArticleId": 92462138, "Title": "Receding horizon control and coordination of multi‐agent systems using polynomial expansion", "Abstract": "<p>The present paper proposes a flexible and efficient methodology for constructing norm-bounded optimal receding horizon control laws for a group of agents, each one of which is described as a repeated integrator of an arbitrary order and with a common input delay. The goal of each agent is to track the given target, while simultaneously avoiding other agents in the group. Polynomial expansion, together with appropriate subspace projection, is utilized in order to derive receding control law in the closed form. Properties of this control law have been investigated, and its link to a well-known control strategy based on avoidance functions, which ensures collision avoidance in the case of first-order integrator agents, has been derived.</p>", "Keywords": "explicit control law;multi-agent systems (MASs);polynomial expansion;receding horizon control (RCH)", "DOI": "10.1002/asjc.2732", "PubYear": 2022, "Volume": "24", "Issue": "6", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "Milan <PERSON><PERSON>", "Affiliation": "University of Novi Sad Faculty of Technical Sciences, Computing and Control Department  Novi Sad Serbia"}, {"AuthorId": 2, "Name": "Mirna N. Ka<PERSON>", "Affiliation": "University of Novi Sad Faculty of Technical Sciences, Computing and Control Department  Novi Sad Serbia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Coordinated Science Laboratory University of Illinois at Urbana‐Champaign  Urbana Illinois USA"}], "References": [{"Title": "Output Feedback Fault‐Tolerant Control of Heterogeneous Multi‐Agent Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "2", "Page": "949", "JournalTitle": "Asian Journal of Control"}]}, {"ArticleId": 92462312, "Title": "Self-Adaptive Feature Transformation Networks for Object Detection in low luminance Images", "Abstract": "<p>Despite the recent improvement of object detection techniques, many of them fail to detect objects in low-luminance images. The blurry and dimmed nature of low-luminance images results in the extraction of vague features and failure to detect objects. In addition, many existing object detection methods are based on models trained on both sufficient- and low-luminance images, which also negatively affect the feature extraction process and detection results. In this article, we propose a framework called Self-adaptive Feature Transformation Network (SFT-Net) to effectively detect objects in low-luminance conditions. The proposed SFT-Net consists of the following three modules: (1) feature transformation module, (2) self-adaptive module, and (3) object detection module. The purpose of the feature transformation module is to enhance the extracted feature through unsupervisely learning a feature domain projection procedure. The self-adaptive module is utilized as a probabilistic module producing appropriate features either from the transformed or the original features to further boost the performance and generalization ability of the proposed framework. Finally, the object detection module is designed to accurately detect objects in both low- and sufficient- luminance images by using the appropriate features produced by the self-adaptive module. The experimental results demonstrate that the proposed SFT-Net framework significantly outperforms the state-of-the-art object detection techniques, achieving an average precision (AP) of up to 6.35 and 11.89 higher on the sufficient- and low- luminance domain, respectively.</p>", "Keywords": "", "DOI": "10.1145/3480973", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Taipei University of Technology, Taiwan, R.O.C."}, {"AuthorId": 2, "Name": "Quoc-V<PERSON>ang", "Affiliation": "National Taipei University of Technology and Hung Yen University of Technology and Education, Vietnam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Taiwan University, Taipei, Taiwan"}], "References": [{"Title": "Video Object Segmentation and Tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 92462359, "Title": "Gene expression data of inflammatory mediators in apical periodontitis in 129 (wild type) and 5-lipoxygenase knockout mice", "Abstract": "Apical periodontitis is an immune inflammatory response around periapical tissues as a result of pathogens invasion into the root canal. The host immunoinflammatory response could determine the progression of this disease, which involves the recruitment of immune cells, and the release of several cytokines in the lesion site. The 5-lipoxygenase pathway has been activated in some osteolytic diseases due to its capacity to interfere in the proliferation and differentiation of bone cells, including the osteoclasts. As mean to understand the inflammatory genes regulation in the apical periodontitis progression, we evaluated the network of 66 genes related to cytokines, chemokines and other inflammatory mediators and receptors in the wild-type (WT) and 5-lipoxygenase enzyme genetically deficient mice (KO). This article presents data not published but related to the research article “Effects of 5-lipoxygenase gene disruption on inflammation, osteoclastogenesis and bone resorption in polymicrobial apical periodontitis” .", "Keywords": "Arachidonic acid ; Leukotrienes ; Inflammation ; Bone resorption", "DOI": "10.1016/j.dib.2021.107787", "PubYear": 2022, "Volume": "40", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pediatric Clinics, School of Dentistry of Ribeirão Preto, Universidade de São Paulo, Ribeirão Preto, Café s/n, 1° andar, sala M-28, CEP, São Paulo 14040-904, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Restorative Dentistry, School of Dentistry of Ribeirão Preto, Universidade de São Paulo, Ribeirão Preto, São Paulo, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Pediatric Clinics, School of Dentistry of Ribeirão Preto, Universidade de São Paulo, Ribeirão Preto, Café s/n, 1° andar, sala M-28, CEP, São Paulo 14040-904, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pediatric Clinics, School of Dentistry of Ribeirão Preto, Universidade de São Paulo, Ribeirão Preto, Café s/n, 1° andar, sala M-28, CEP, São Paulo 14040-904, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pediatric Clinics, School of Dentistry of Ribeirão Preto, Universidade de São Paulo, Ribeirão Preto, Café s/n, 1° andar, sala M-28, CEP, São Paulo 14040-904, Brazil"}, {"AuthorId": 6, "Name": "Marília Pacífico Lucisano Politi", "Affiliation": "Department of Pediatric Clinics, School of Dentistry of Ribeirão Preto, Universidade de São Paulo, Ribeirão Preto, Café s/n, 1° andar, sala M-28, CEP, São Paulo 14040-904, Brazil"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departamento de Análises Clínicas, Toxicológicas e Bromatológicas da Faculdade de Ciências Farmacêuticas de Ribeirão Preto da Universidade de São Paulo, Ribeirão Preto, São Paulo, Brazil"}, {"AuthorId": 8, "Name": "<PERSON>-<PERSON>", "Affiliation": "Department of Pediatric Clinics, School of Dentistry of Ribeirão Preto, Universidade de São Paulo, Ribeirão Preto, Café s/n, 1° andar, sala M-28, CEP, São Paulo 14040-904, Brazil;Corresponding author;@fwsilv58"}], "References": []}, {"ArticleId": 92462419, "Title": "A modular agricultural robotic system (MARS) for precision farming: Concept and implementation", "Abstract": "<p>Increasing global population, climate change, and shortage of labor pose significant challenges for meeting the global food and fiber demand, and agricultural robots offer a promising solution to these challenges. This paper presents a new robotic system architecture and the resulting modular agricultural robotic system (MARS) that is an autonomous, multi-purpose, and affordable robotic platform for in-field plant high throughput phenotyping and precision farming. There are five essential hardware modules (wheel module, connection module, robot controller, robot frame, and power module) and three optional hardware modules (actuation module, sensing module, and smart attachment). Various combinations of the hardware modules can create different robot configurations for specific agricultural tasks. The software was designed using the Robot Operating System (ROS) with three modules: control module, navigation module, and vision module. A robot localization method using dual Global Navigation Satellite System antennas was developed. Two line-following algorithms were implemented as the local planner for the ROS navigation stack. Based on the MARS design concept, two MARS designs were implemented: a low-cost, lightweight robotic system named MARS mini and a heavy-duty robot named MARS X. The autonomous navigation of both MARS X and mini was evaluated at different traveling speeds and payload levels, confirming satisfactory performances. The MARS X was further tested for its performance and navigation accuracy in a crop field, achieving a high accuracy over a 537 m long path with only 15% of the path having an error larger than 0.05 m. The MARS mini and MARS X were shown to be useful for plant phenotyping in two field tests. The modular design makes the robots easily adaptable to different agricultural tasks and the low-cost feature makes it affordable for researchers and growers.</p>", "Keywords": "agricultural robot;high-throughput phenotyping;phenotyping robot", "DOI": "10.1002/rob.22056", "PubYear": 2022, "Volume": "39", "Issue": "4", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Bio-sensing and Instrumentation Laboratory, College of Engineering, University of Georgia, Athens, Georgia, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Bio-sensing and Instrumentation Laboratory, College of Engineering, University of Georgia, Athens, Georgia, USA; Phenomics and Plant Robotics Center, Bio-sensing and Instrumentation Laboratory, College of Engineering, University of Georgia, Athens, Georgia, USA"}], "References": [{"Title": "A low‐cost and efficient autonomous row‐following robot for food production in polytunnels", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "2", "Page": "309", "JournalTitle": "Journal of Field Robotics"}, {"Title": "An autonomous strawberry‐harvesting robot: Design, development, integration, and field evaluation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "2", "Page": "202", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Development of a sweet pepper harvesting robot", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "6", "Page": "1027", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Apple harvesting robot under information technology: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "3", "Page": "172988142092531", "JournalTitle": "International Journal of Advanced Robotic Systems"}, {"Title": "A reinforcement learning‐based approach for modeling and coverage of an unknown field using a team of autonomous ground vehicles", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "2", "Page": "1069", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 92462755, "Title": "Virtual body anthropomorphism increases drift in self‐location: Further support for the humanoid shape rule", "Abstract": "<p>Previous studies on bodily self-consciousness (BCS) have shown that self-location and body ownership are prone to changes based on the perceptual appearances of the fake virtual body. In the current study with 36 participants, we assessed the influence of virtual avatar anthropomorphism and the synchronicity of the visuo-tactile stimulation on self-location using a virtual reality full-body illusion experiment. During the experiment, half of the participants observed a gender-matched full-body humanoid avatar from a first-person perspective (1PP) and the other half observed a less anthropomorphic full-body cubical avatar from 1PP while they were receiving synchronous and asynchronous visuo-tactile stimulation. Results showed a significant main effect of the synchronicity of the visuo-tactile stimulation and avatar body type on self-location but no significant interaction was found between them. Moreover, the results of the self-report questionnaire provide additional evidence showing that participants who received synchronous visuo-tactile stimulation, experienced not only greater changes in the feeling of self-location, but also, increased ownership, and referral of touch. Our results provided further support for the previous findings that showed evidence for the effect of virtual avatar appearance on BCS.</p>", "Keywords": "avatar anthropomorphism;bodily self-consciousness;drift in self-location;first-person perspective;virtual reality", "DOI": "10.1002/cav.2038", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 17200, "JournalTitle": "Computer Animation and Virtual Worlds", "ISSN": "1546-4261", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychology İzmir University of Economics  İzmir Turkey"}, {"AuthorId": 2, "Name": "Muhtar Ç. Uludağlı", "Affiliation": "Department of Computer Engineering, IEU Game Laboratory İzmir University of Economics  İzmir Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Software Engineering, IEU Game Laboratory İzmir University of Economics  İzmir Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychology İzmir University of Economics  İzmir Turkey"}], "References": []}, {"ArticleId": 92462901, "Title": "Correction to: Image deblurring based on enhanced salient edge selection", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00371-021-02385-y", "PubYear": 2023, "Volume": "39", "Issue": "3", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Hefei University of Technology, Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Tan", "Affiliation": "School of Mathematics, Hefei University of Technology, Hefei, China; School of Computer and Information, Hefei University of Technology, Hefei, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mathematics, Hefei University of Technology, Hefei, China"}, {"AuthorId": 4, "Name": "Xianyu Ge", "Affiliation": "School of Computer and Information, Hefei University of Technology, Hefei, China"}], "References": []}, {"ArticleId": 92462943, "Title": "Video action recognition with Key-detail Motion Capturing based on motion spectrum analysis and multiscale feature fusion", "Abstract": "<p>At present, existing research works on action recognition are still not ideal, when most of the video content is redundant such as video clips without any object motion, and human actions in the video are complex. The reasons are as follows: (1) Most of them lack attention to key-motion information of the video, thus irrelevant information will be input into the model. (2) And there is a lack of interaction between video spatial and temporal information, which may cause the loss of detailed motion information in the video. In this paper, we propose a Key-detail Motion Capturing Network (K-MCN) to solve these problems, which contains two modules. The first one is the Video Key-motion Spectrum Analyzer (VKSA) module. In this module, the video optical flow can be subjected to frequency spectrum analysis, filtering and clustering to extract the key-motion frames. The second one is the Multiscale Motion Spatiotemporal Interaction module, which allows multi-scale modeling and fusion of spatial and temporal features extracted from key-motion frames, enabling the network to realize the interaction and supplement of multiscale spatiotemporal information. Finally, we conducted extensive experiments on the UCF101, HMDB51 and Something-SomethingV1 datasets, and the results showed that our method achieves better performance compared with other state-of-the-art methods.</p>", "Keywords": "Action recognition; Key frame extraction; Multiscale feature fusion; Spatiotemporal feature pyramid", "DOI": "10.1007/s00371-021-02355-4", "PubYear": 2023, "Volume": "39", "Issue": "2", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computers, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computers, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 3, "Name": "Hai<PERSON> Chen", "Affiliation": "School of Computers, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Information Science, University of Macau, Macau SAR, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, South China University of Technology, Guangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Guangdong University of Technology, Guangzhou, China"}], "References": [{"Title": "Image representation of pose-transition feature for 3D skeleton-based action recognition", "Authors": "<PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "513", "Issue": "", "Page": "112", "JournalTitle": "Information Sciences"}, {"Title": "Perceptual image quality assessment: a survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "A survey on video-based Human Action Recognition: recent updates, datasets, challenges, and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "3", "Page": "2259", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Augmented Skeleton Based Contrastive Action Learning with Momentum LSTM for Unsupervised Action Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "569", "Issue": "", "Page": "90", "JournalTitle": "Information Sciences"}]}]