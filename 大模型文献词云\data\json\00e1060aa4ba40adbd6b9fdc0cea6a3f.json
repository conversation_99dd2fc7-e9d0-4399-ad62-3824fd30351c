[{"ArticleId": 87173791, "Title": "Study of the effect of orbital on interaction behaviour of SWCNT- metal phthalocyanines composites with ammonia gas", "Abstract": "The present study deals with analysing the effect of central metal atom on interaction behaviour of a composite constituted of two materials, Single walled carbon nanotubes (SWCNT) and metal phthalocyanines (MPcs) (SWCNT-MPc) with ammonia gas. For this purpose, three metal phthalocyanines namely; Magnesium phthalocyanine (MgPc), Aluminum phthalocyanine hydroxide (AlPcOH) and Titanyl phthalocyanine (TiOPc) were taken and their variation in resistances with exposure of ammonia gas is taken as detection parameter. The central metal ions of MPcs were chosen so that valence electrons are in s, p and d orbitals respectively. The surface-interface interaction of pristine SWCNT towards ammonia gas was found to increase with better repeatability and stability after addition of MPcs. SWCNT-TiOPc composite exhibited maximum interaction with ammonia vapors (≈ 3 times pristine SWCNT) followed by SWCNT- MgPc (≈ 2 times pristine SWCNT), while AlPcOH had shown faster desorption (≈ 0.5 times faster) as compared to pristine SWCNT during recovery stage. The concept of charge transfer mechanism between SWCNT-MPcs and ammonia gas and varied orbital state accommodating ammonia lone pairs were used to explain interaction studies. Consistencies observed in physics and chemistry analysis highlighted the significance of both these fields to have complete understanding of the interaction phenomena between SWCNT-MPcs and ammonia gas.", "Keywords": "Metal phthalocyanines ; Carbon nanotubes ; Sensors ; Charge transfer ; Orbital states", "DOI": "10.1016/j.snb.2021.129767", "PubYear": 2021, "Volume": "337", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Solid State Physics Laboratory, DRDO, Delhi, India"}, {"AuthorId": 2, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Solid State Physics Laboratory, DRDO, Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Solid State Physics Laboratory, DRDO, Delhi, India"}, {"AuthorId": 4, "Name": "G.S.S. <PERSON>", "Affiliation": "Department of Physics, Panjab University, Chandigarh, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Panjab University, Chandigarh, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Solid State Physics Laboratory, DRDO, Delhi, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Solid State Physics Laboratory, DRDO, Delhi, India;Corresponding author"}], "References": []}, {"ArticleId": 87173792, "Title": "A method to improve the resistive pulse sensing by modifying surface charge of nanochannels", "Abstract": "Controlling and patterning the surface charges of the sensing nanochannel in a resistive pulse sensing (RPS) device to enhance the signal detection are investigated in this paper. The electric currents and signal detection in the sensing nanochannels with positive surface charges, negative surface charges, and opposite surface charges at the two ends are systematically studied. It is found that the length of the surface-modified nanochannels has a significant effect on the sensitivity of the RPS detection. The pulse signal amplitude can be increased by 3 times in the surface-modified nanochannel when the channel length is 500 nm. The enhancement in the signal amplitude becomes less prominent in long nanochannels. Modified sensing nanochannels with a channel length of 5 μ m are fabricated by regulating surface charges with charged polyelectrolytes. Nanoparticles with a diameter of 5 nm are detected in the modified nanochannels. The experimental results are in good agreement with the numerical simulation results. It is also found that the surface charge pattern of the sensing nanochannel has a significant effect on the sensitivity of the RPS detection. The RPS signals can be enhanced by approximately 50 % when the sensing nanochannel has a section with positive surface charge and negative surface charge in the rest channel in a KCl solution with a bulk concentration of 100 mM.", "Keywords": "Nanochannel ; RPS ; Surface modification ; Nanoparticle detection", "DOI": "10.1016/j.snb.2021.129773", "PubYear": 2021, "Volume": "337", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Mechatronics Engineering, University of Waterloo, Waterloo, Ontario, N2L 3G1, Canada"}, {"AuthorId": 2, "Name": "Dongqing Li", "Affiliation": "Department of Mechanical and Mechatronics Engineering, University of Waterloo, Waterloo, Ontario, N2L 3G1, Canada;Corresponding author"}], "References": []}, {"ArticleId": 87173798, "Title": "Optimization-enabled deep stacked autoencoder for occupancy detection", "Abstract": "<p>Occupancy detection is the main building block of residential and commercial building automation systems. Currently, it is difficult to determine when and where people occupy a commercial building. The accurate occupancy detection in the buildings is used to save energy. Hence, this paper presents an occupancy detection approach for detecting the person’s count in the room or building using the proposed Chaotic Whale Spider Monkey (ChaoWSM) + Deep stacked autoencoder. The input data are initially fed to the pre-processing step. The pre-processing is done using missing value imputation and log transformation. Then, the feature reduction is performed from the pre-processed data. Here, the features are reduced based on probabilistic principal component analysis. On the next step, the reduced features are fed to the occupancy detection module where the occupancy is detected on the basis of the support vector neural network classifier. If the occupancy is detected, the features are forwarded to the person’s count identification. The identification of a person’s count is carried out based on deep stacked autoencoder, which is trained by an optimization approach. The optimization is done using the proposed Chaotic Whale Spider Monkey (ChaoWSM) optimization, which is the integration of Chaotic Whale optimization and the spider monkey optimization. The performance of occupancy detection based on ChaoWSM + Deep stacked autoencoder is evaluated based on sensitivity, specificity, accuracy and MSE. The proposed ChaoWSM + Deep stacked autoencoder method achieves the maximal accuracy of 0.945, maximal sensitivity of 0.946, the maximal specificity of 0.949 and the minimal MSE of 0.233 by varying the training data percentage for deep stacked autoencoder.</p>", "Keywords": "Occupancy detection; Chaotic Whale optimization; Spider Monkey optimization; Support vector neural network; Log transformation", "DOI": "10.1007/s13278-021-00730-6", "PubYear": 2021, "Volume": "11", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Technological Institute, Mumbai, Mumbai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Technological Institute, Mumbai, Mumbai, India"}], "References": []}, {"ArticleId": 87173855, "Title": "BOND", "Abstract": "<p> In a large-scale wireless sensor network, hundreds and thousands of sensors sample and forward data back to the sink periodically. In two real outdoor deployments GreenOrbs and CitySee, we observe that some bottleneck nodes strongly impact other nodes’ data collection and thus degrade the whole network performance. To figure out the importance of a node in the process of data collection, system manager is required to understand interactive behaviors among the parent and child nodes. So we present a management tool BOND (BOttleneck Node Detector), which explains the concept of Node Dependence to characterize how much a node relies on each of its parent nodes, and also models the routing process as a Hidden Markov Model and then uses a machine learning approach to learn the state transition probabilities in this model. Moreover, BOND can predict the network dataflow if some nodes are added or removed to avoid data loss and flow congestion in network redeployment. We implement BOND on real hardware and deploy it in an outdoor network system. The extensive experiments show that Node Dependence indeed help to explore the hidden bottleneck nodes in the network, and BOND infers the Node Dependence with an average accuracy of more than 85%. </p>", "Keywords": "", "DOI": "10.1145/3439956", "PubYear": 2021, "Volume": "17", "Issue": "2", "JournalId": 12743, "JournalTitle": "ACM Transactions on Sensor Networks", "ISSN": "1550-4859", "EISSN": "1550-4867", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tsinghua University, Beijing City, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Michigan State University, East Lansing, Michigan, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Science and Technology of China, Hefei, Anhui, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing City, China"}], "References": []}, {"ArticleId": 87173856, "Title": "Intercepting a Stealthy Network", "Abstract": "We investigate an understudied threat:\n networks of stealthy routers (S-Routers) \n , relaying messages to a hidden\n destination \n . The S-Routers relay communication along a path of multiple short-range, low-energy hops, to avoid remote localization by triangulation. Mobile devices called\n Interceptors \n can detect communication by an S-Router, but only when the Interceptor is next to the transmitting S-Router. We examine algorithms for a set of mobile Interceptors to find the\n destination \n of the communication relayed by the S-Routers. The algorithms are compared according to the number of\n communicating rounds \n before the\n destination \n is found, i.e., rounds in which data is transmitted from the source to the\n destination \n . We evaluate the algorithms analytically and using simulations, including against a parametric, optimized strategy for the S-Routers. Our main result is an Interceptors algorithm that bounds the expected number of\n communicating rounds \n by a term quasilinear in the number of S-Routers. For the case where S-Routers transmit at every round (“continuously”), we present an algorithm that improves this bound.", "Keywords": "", "DOI": "10.1145/3431223", "PubYear": 2021, "Volume": "17", "Issue": "2", "JournalId": 12743, "JournalTitle": "ACM Transactions on Sensor Networks", "ISSN": "1550-4859", "EISSN": "1550-4867", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Bar-Ilan University, Ramat Gan, Israel"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Connecticut, Storrs, Connecticut, USA, USA"}], "References": []}, {"ArticleId": 87173915, "Title": "Automated Comparative Study of Some Generalized Rough Approximations", "Abstract": "<p>The paper contains some remarks on building automated counterpart of a comparison of some generalized rough approximations of sets, where the classical indiscernibility relation is generalized to arbitrary binary relation. Our focus was on translating rationality postulates for such operators by means of the <PERSON>zar system – the software and the database which allows for expressing and checking mathematical knowledge for the logical correctness. The main objective was the formal (and machine-checked) proof of Theorem 4.1 from <PERSON><PERSON>’s paper “A Comparative Study of Some Generalized Rough Approximations”, hence the present title. We provide also the discussion on how to make the presentation more efficient to reuse the reasoning techniques of the Mizar verifier.</p>", "Keywords": "rough approximation; formalization of mathematics; Mizar Mathematical Library", "DOI": "10.3233/FI-2021-2019", "PubYear": 2021, "Volume": "179", "Issue": "2", "JournalId": 31657, "JournalTitle": "Fundamenta Informaticae", "ISSN": "0169-2968", "EISSN": "1875-8681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Informatics, University of Białystok, Konstantego Ciołkowskiego 1M, 15-245 Białystok, Poland. "}], "References": []}, {"ArticleId": 87173916, "Title": "A Novel Ensemble Model - The Random Granular Reflections", "Abstract": "<p>One of the most popular families of techniques to boost classification are Ensemble methods. Random Forests, Bagging and Boosting are the most popular and widely used ones. This article presents a novel Ensemble Model, named Random Granular Reflections. The algorithm used in this new approach creates an ensemble of homogeneous granular decision systems. The first step of the learning process is to take the training system and cover it with random homogeneous granules (groups of objects from the same decision class that are as little indiscernible from each other as possible). Next, granular reflection is created, which is finally used in the classification process. Results obtained by our initial experiments show that this approach is promising and comparable with other tested methods. The main advantage of our new method is that it is not necessary to search for optimal parameters while looking for granular reflections in the subsequent iterations of our ensemble model.</p>", "Keywords": "Random Granular Reflections; Homogeneous Granulation; CSG Classifier; Ensemble Model; Rough Sets; Decision Systems; Classification", "DOI": "10.3233/FI-2021-2020", "PubYear": 2021, "Volume": "179", "Issue": "2", "JournalId": 31657, "JournalTitle": "Fundamenta Informaticae", "ISSN": "0169-2968", "EISSN": "1875-8681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mathematics and Computer Science, University of Warmia and Mazury, Olsztyn, Poland. , <EMAIL>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mathematics and Computer Science, University of Warmia and Mazury, Olsztyn, Poland. , <EMAIL>"}], "References": []}, {"ArticleId": 87173942, "Title": "Factors that influence user perception of ubiquitous monitoring environments: An empirical study in a developing country", "Abstract": "<p>Over the years, conventional monitoring devices such as video cameras and tape-recorders have been redesigned into smarter and smaller forms which can be integrated seamlessly into an environment. The purpose of these ubiquitous monitoring devices is to enable the provision of innovative applications and services that support user wellbeing. Despite improving operations in essential areas such as health, there are still concerns associated with ubiquitous monitoring. For benefits associated with ubiquitous monitoring to be fully realized, there is the need to understand the role of user perceptions. This study investigates the factors that influence user perceptions of ubiquitous monitoring devices by drawing samples from a developing country. Users’ response on seven recurring ubiquitous monitoring perceptions were collected using a survey questionnaire. The relationships among these factors were analysed using Partial Least Square Structural Equation Modelling. The results suggest a significant relationship between Perceived Natural Border Crossing and Perceived Privacy Invasion. Also Perceived Affordance, Perceived Coverage and Perceived Privacy Invasion predicted Perceived Trust. The findings imply that more emphasis must be given to educating and familiarizing users with ubiquitous monitoring devices. Future studies are expected to replicate the study in other developing societies to validate these claims.</p>", "Keywords": "", "DOI": "10.3233/AIS-210592", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 20863, "JournalTitle": "Journal of Ambient Intelligence and Smart Environments", "ISSN": "1876-1364", "EISSN": "1876-1372", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Ghana, Legon-Accra, Ghana"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information Technology Education, University of Education, Winneba, Kumasi Campus, Ghana"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Ghana, Legon-Accra, Ghana"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Information Systems and Innovation, Ghana Institute of Management and Public Administration, Achimota-Accra, Ghana"}], "References": [{"Title": "Understanding trust on social networking sites among tertiary students: An empirical study in Ghana", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "19", "Issue": "3/4", "Page": "209", "JournalTitle": "Applied Computing and Informatics"}]}, {"ArticleId": 87173978, "Title": "Reasoning on the Efficiency of Distributed Complex Event Processing", "Abstract": "<p>Complex event processing (CEP) evaluates queries over streams of event data to detect situations of interest. If the event data are produced by geographically distributed sources, CEP may exploit in-network processing that distributes the evaluation of a query among the nodes of a network. To this end, a query is modularized and individual query operators are assigned to nodes, especially those that act as data sources. Existing solutions for such operator placement, however, are limited in that they assume all query results to be gathered at one designated node, commonly referred to as a sink. Hence, existing techniques postulate a hierarchical structure of the network that generates and processes the event data. This largely neglects the optimisation potential that stems from truly decentralised query evaluation with potentially many sinks. To address this gap, in this paper, we propose Multi-Sink Evaluation (MuSE) graphs as a formal computational model to evaluate common CEP queries in a decentralised manner. We further prove the completeness of query evaluation under this model. Striving for distributed CEP that can scale to large volumes of high-frequency event streams, we show how to reason on the network costs induced by distributed query evaluation and prune inefficient query execution plans. As such, our work lays the foundation for distributed CEP that is both, sound and efficient.</p>", "Keywords": "distributed systems; complex event processing; operator placement", "DOI": "10.3233/FI-2021-2017", "PubYear": 2021, "Volume": "179", "Issue": "2", "JournalId": 31657, "JournalTitle": "Fundamenta Informaticae", "ISSN": "0169-2968", "EISSN": "1875-8681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Humboldt-Universität zu Berlin, Berlin, Germany. "}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Humboldt-Universität zu Berlin, Berlin, Germany. "}], "References": []}, {"ArticleId": 87173979, "Title": "Quantitative Verification of Stochastic Regular Expressions", "Abstract": "<p>In this article, we introduce a probabilistic verification algorithm for stochastic regular expressions over a probabilistic extension of the Action based Computation Tree Logic (ACTL*). The main results include a novel model checking algorithm and a semantics on the probabilistic action logic for stochastic regular expressions (SREs). Specific to our model checking algorithm is that SREs are defined via local probabilistic functions. Such functions are beneficial since they enable to verify properties locally for sub-components. This ability provides a flexibility to reuse the local results for the global verification of the system; hence, the framework can be used for iterative verification. We demonstrate how to model a system with an SRE and how to verify it with the probabilistic action based logic and present a preliminary performance evaluation with respect to the execution time of the reachability algorithm.</p>", "Keywords": "quantitative verification; probabilistic verification; formal models; stochastic regular expressions; probabilistic regular expressions; stochastic algebra; action based logic", "DOI": "10.3233/FI-2021-2018", "PubYear": 2021, "Volume": "179", "Issue": "2", "JournalId": 31657, "JournalTitle": "Fundamenta Informaticae", "ISSN": "0169-2968", "EISSN": "1875-8681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Humboldt University, Berlin, Germany; Ege University, Izmir, Turkey. "}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Humboldt University, Berlin, Germany. "}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Humboldt University, Berlin, Germany. "}], "References": []}, {"ArticleId": 87173980, "Title": "Encoding Threshold Boolean Networks into Reaction Systems for the Analysis of Gene Regulatory Networks", "Abstract": "<p>Gene regulatory networks represent the interactions among genes regulating the activation of specific cell functionalities and they have been successfully modeled using threshold Boolean networks. In this paper we propose a systematic translation of threshold Boolean networks into reaction systems. Our translation produces a non redundant set of rules with a minimal number of objects. This translation allows us to simulate the behavior of a Boolean network simply by executing the (closed) reaction system we obtain. This can be very useful for investigating the role of different genes simply by “playing” with the rules. We developed a tool able to systematically translate a threshold Boolean network into a reaction system. We use our tool to translate two well known Boolean networks modelling biological systems: the yeast-cell cycle and the SOS response in Escherichia coli. The resulting reaction systems can be used for investigating dynamic causalities among genes.</p>", "Keywords": "", "DOI": "10.3233/FI-2021-2021", "PubYear": 2021, "Volume": "179", "Issue": "2", "JournalId": 31657, "JournalTitle": "Fundamenta Informaticae", "ISSN": "0169-2968", "EISSN": "1875-8681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dipartimento di Informatica, Università di Pisa, Italy. "}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dipartimento di Informatica, Università di Pisa, Italy. "}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dipartimento di Informatica, Università di Pisa, Italy. "}, {"AuthorId": 4, "Name": "Damas Gruska", "Affiliation": "Department of Applied Informatics, Comenius University in Bratislava, Slovak Republic. "}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Dipartimento di Informatica, Università di Pisa, Italy. "}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Dipartimento di Informatica, Università di Pisa, Italy. "}], "References": []}, {"ArticleId": 87174083, "Title": "A low noise and low power device composed of electrode and CD ASIC for neural and other vital signals recording", "Abstract": "In this paper, to effectively record the electroencephalogram (EEG) and other body-vital signals of the healthy volunteer, a low-noise and low-power device was developed. The proposed device consisted of a novel convenient, reliable and sensitive electrode realized by an Ag/AgCl dry-contact electrode coated with nano-composites and a novel post-electrode chopper digitizer (CD). The CD consisted of a low noise and low power chopping pre-amplifier with gain and band-width (BW) adjustable abilities and an intermediate speed and low power successive approximation register-analog digital converter (SAR-ADC). First, using the front-end electrode, the neural signal was received from the brain scalp; second, using the CD, the amplified analog voltage outputting from the chopping pre-amplifier was converted into digital sequences using the SAR-ADC. Testing results showed that the contact resistance between the electrode and the brain scalp was optimized to about 300.00 kΩ, as the electrode was coated with polypyrrole-graphene nano-particles. And, the linear relevancy between the electrode and the traditional wet-contact electrode was more than 93%. Finally, the CD was manufactured with CMOS technology (SmicRF180NM 1Poly6M) under a supply voltage of 1.0 V. Testing results of the CD showed that: (1) the CD achieved an inputting-referred noise of 0.40 μV root-mean-square (RMS) in 0.18–200 Hz, the energy consumption was 3.40 μW for one chopping pre-amplifier, 200 μW for SAR-ADC, etc.; (2) the SAR-ADC achieved an effective number of bits (ENOB) of 9.96-bits under conversion rate reaching 1.00 MSps, the signal noise ratio (SNR) was 60.97 dB, etc. The designed device could satisfy recording for EEG signals in μV level, also, the device could be used for detecting electrocardiography (EKG) signals in mV level. So, the low noise and low power device worked well for recording micro neural signals, and suitable for constructing reliable portable or wearable pervasive bio-devices used for multiple body-vital signals too.", "Keywords": "EEG/EKG ; Electrode ; Polypyrrole-graphene nano-composites ; CD ; Low noise bio-device", "DOI": "10.1016/j.sna.2021.112681", "PubYear": 2021, "Volume": "324", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Electronics, Shandong Normal University, Jinan 250014, China;State Key Laboratory of Transducer Technology, Institute of Electronics, Chinese Academy of Sciences, Beijing 100190, China;Shandong Key Laboratory of Medical Physics and Image Processing, School of Physics and Electronics, Shandong Normal University, Jinan 250014, China;Center of Light Manipulations and Applications & Shandong Provincial Key Laboratory of Optics and Photonic Device, School of Physics and Electronics, Shandong Normal University, Jinan 250014, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Electronics, Shandong Normal University, Jinan 250014, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Interuniversity Microelectronics Centre, Leuven, Belgium"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Science and Engineering, Southeast University, Nanjing 210096, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Natural Medicines, Jiangsu Key Laboratory of TCM Evaluation and Translation Research, School of Traditional Chinese Pharmacy, China Pharmaceutical University, Nanjing 211198, China;Corresponding author"}], "References": []}, {"ArticleId": 87174099, "Title": "Algorithmic augmentation of democracy: considering whether technology can enhance the concepts of democracy and the rule of law through four hypotheticals", "Abstract": "<p>The potential use, relevance, and application of AI and other technologies in the democratic process may be obvious to some. However, technological innovation and, even, its consideration may face an intuitive push-back in the form of algorithm aversion (<PERSON><PERSON><PERSON> et al. J Exp Psychol 144(1):114–126, 2015). In this paper, I confront this intuition and suggest that a more ‘extreme’ form of technological change in the democratic process does not necessarily result in a worse outcome in terms of the fundamental concepts of democracy and the Rule of Law. To provoke further consideration and illustrate that initial intuitions regarding democratic innovation may not always be accurate, I pose and explore four ways that AI and other forms of technology could be used to augment the representative democratic process. The augmentations range from voting online to the wholesale replacement of the legislature’s human representatives with algorithms. After first noting the intuition that less invasive forms of augmented democracy may be less objectionable than more extreme forms, I go on to critically assess whether the augmentation of existing systems satisfies or enhances ideas associated with democracy and the Rule of Law (provided by <PERSON> and <PERSON>). By imagining a (not too far-fetched) future in a (not too far-removed) democratic society, my conclusion is that, when it comes to democracy and the Rule of Law, intuitions regarding technology may lead us astray.</p>", "Keywords": "Artificial Intelligence; Democracy; Rule of Law; Algorithm", "DOI": "10.1007/s00146-021-01170-8", "PubYear": 2022, "Volume": "37", "Issue": "1", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Law, Monash University, Melbourne, Australia"}], "References": [{"Title": "The race for an artificial general intelligence: implications for public policy", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "2", "Page": "367", "JournalTitle": "AI & SOCIETY"}, {"Title": "Why friendly AIs won’t be that friendly: a friendly reply to <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "2", "Page": "505", "JournalTitle": "AI & SOCIETY"}, {"Title": "On social machines for algorithmic regulation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "3", "Page": "645", "JournalTitle": "AI & SOCIETY"}, {"Title": "Artificial intelligence, transparency, and public decision-making", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "4", "Page": "917", "JournalTitle": "AI & SOCIETY"}, {"Title": "15 challenges for AI: or what AI (currently) can’t do", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "2", "Page": "355", "JournalTitle": "AI & SOCIETY"}, {"Title": "Testing and unpacking the effects of digital fake news: on presidential candidate evaluations and voter support", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "4", "Page": "969", "JournalTitle": "AI & SOCIETY"}, {"Title": "In AI we trust? Perceptions about automated decision-making by artificial intelligence", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "3", "Page": "611", "JournalTitle": "AI & SOCIETY"}]}, {"ArticleId": 87174113, "Title": "A systematic literature review on state-of-the-art deep learning methods for process prediction", "Abstract": "Process mining enables the reconstruction and evaluation of business processes based on digital traces in IT systems. An increasingly important technique in this context is process prediction. Given a sequence of events of an ongoing trace, process prediction allows forecasting upcoming events or performance measurements. In recent years, multiple process prediction approaches have been proposed, applying different data processing schemes and prediction algorithms. This study focuses on deep learning algorithms since they seem to outperform their machine learning alternatives consistently. Whilst having a common learning algorithm, they use different data preprocessing techniques, implement a variety of network topologies and focus on various goals such as outcome prediction, time prediction or control-flow prediction. Additionally, the set of log-data, evaluation metrics and baselines used by the authors diverge, making the results hard to compare. This paper attempts to synthesise the advantages and disadvantages of the procedural decisions in these approaches by conducting a systematic literature review.", "Keywords": "Process prediction; Predictive process monitoring; Systematic literature review; Deep learning", "DOI": "10.1007/s10462-021-09960-8", "PubYear": 2022, "Volume": "55", "Issue": "2", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Information Systems, German Research Center for Artificial Intelligence (DFKI), Saarbruecken, Germany;Institute for Information Systems, Saarland University, Saarbruecken, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Information Systems, German Research Center for Artificial Intelligence (DFKI), Saarbruecken, Germany;Institute for Information Systems, Saarland University, Saarbruecken, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for Information Systems, German Research Center for Artificial Intelligence (DFKI), Saarbruecken, Germany;Institute for Information Systems, Saarland University, Saarbruecken, Germany"}], "References": [{"Title": "A Novel Business Process Prediction Model Using a Deep Learning Method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "2", "Page": "143", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Predicting performances in business processes using deep neural networks", "Authors": "Gyunam Park; Minseok Song", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "113191", "JournalTitle": "Decision Support Systems"}, {"Title": "Machine Learning in Business Process Monitoring: A Comparison of Deep Learning and Classical Approaches Used for Outcome Prediction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "3", "Page": "261", "JournalTitle": "Business & Information Systems Engineering"}]}, {"ArticleId": 87174145, "Title": "Implementation of RSA cryptographic algorithm using SN P systems based on HP/LP neurons", "Abstract": "<p>Asymmetric cryptographic systems are often more complex and require more computational power than symmetric systems. This is why they might be implemented using unconventional computing systems, such as P systems, spiking neural systems, and DNA computing. In this work, we design an implementation model for RSA encryption and decryption algorithms in the framework of Spiking neural P systems with HP/LP basic neurons.</p>", "Keywords": "SN P systems; RSA cryptography; HP/LP neurons; Dual-rail logic", "DOI": "10.1007/s41965-021-00073-3", "PubYear": 2021, "Volume": "3", "Issue": "1", "JournalId": 62371, "JournalTitle": "Journal of Membrane Computing", "ISSN": "2523-8906", "EISSN": "2523-8914", "Authors": [{"AuthorId": 1, "Name": "Ganbat Ganbaatar", "Affiliation": "School of Information and Communication Technology, Mongolian University of Science and Technology, Ulaanbaatar, Mongolia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Technology, Mongolian University of Science and Technology, Ulaanbaatar, Mongolia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information and Communication Technology, Mongolian University of Science and Technology, Ulaanbaatar, Mongolia"}, {"AuthorId": 4, "Name": "Tseren-<PERSON><PERSON>", "Affiliation": "School of Information and Communication Technology, Mongolian University of Science and Technology, Ulaanbaatar, Mongolia"}], "References": [{"Title": "An error-tolerant serial binary full-adder via a spiking neural P system using HP/LP basic neurons", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "42", "JournalTitle": "Journal of Membrane Computing"}, {"Title": "Formal verification of cP systems using PAT3 and ProB", "Authors": "<PERSON><PERSON> Liu; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "2", "Page": "80", "JournalTitle": "Journal of Membrane Computing"}, {"Title": "An Overview of Hardware Implementation of Membrane Computing Models", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 87174190, "Title": "Optical Crackmeter for Retaining Wall in a Landslide Area Using Computer Vision Technology", "Abstract": "An innovative 3D optical crackmeter employing computer vision technology is used for displacement monitoring in a crack of a retaining wall automatically and remotely. The 3D optical crackmeter is composed of a Raspberry Pi device and a digital camera in a box, and a fixed chessboard on the two sides of a crack. A network with LoRa wireless communication can be connected as an IoT system to provide automatic remote functions. The OpenCV library is employed to analyze changes in chessboard imaging so that relative displacements of the crack in the retaining wall can be measured in a landslide area. Through laboratory and field testing, the resolution and accuracy of the 3D optical crackmeter were determined as 0.04 and 0.1 mm, respectively. Using the crackmeter, we observed significant displacements in the xand z-directions of the crack in a retaining wall of 0.067 and 0.060 cm, respectively, in the Jhongsinlun landslide area of Taiwan over three months. Overall, the 3D optical crackmeter with computer vision technology can accurately measure the 3D displacement of cracks in a retaining wall. Moreover, the IoT-based 3D optical crackmeter is more cost-effective than traditional crackmeters used in landslide areas. © 2021 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "Computer vision; Crackmeter; IoT instrument; Landslide monitoring", "DOI": "10.18494/SAM.2021.3011", "PubYear": 2021, "Volume": "33", "Issue": "3", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Soil and Water Conservation, National Chung Hsing University, No. 145 Xingda Rd., South Dist., Taichung City, 402, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Chienkuo Technology University, No. 1 Chiehshou North Rd., Changhua City, 500, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, National Chung Hsing University, No. 145 Xingda Rd., South Dist., Taichung City, 402, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, National Chung Hsing University, No. 145 Xingda Rd., South Dist., Taichung City, 402, Taiwan"}], "References": []}, {"ArticleId": 87174191, "Title": "Pneumatically Driven Vertical Articulated Robotic Arm for Surgical Task with Inertia Estimation", "Abstract": "In this study, we developed a vertical articulated robotic arm for laparoscopic surgery and proposed a method of controlling the robot. The robotic arm operates surgical instruments around a virtually fixed remote center of motion (RCM) calculated from the robot kinematics. The robotic arm has six degrees of freedom and is driven by pneumatic actuators. The features of the pneumatic actuators of compactness, high backdrivability, and low heat generation allow the robotic arm to be moved passively by the human hand and prevent the risk of heat accumulation in the drape. We made the upper arm and forearm lightweight by mounting the pneumatic actuators on the base of the robot. The joint angles are controlled by pneumatic servo systems. The inertia of the forearm and upper arm was estimated by measuring the joint angles. We also proposed variable gain control of the yaw joint to compensate for changes in inertia. We experimentally confirmed that the variable gain improves the controllability of the robot and improves its operability around the virtual fixed RCM. © 2021 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "Inertia estimation; Pneumatic system; Robotic arm; Surgical robot", "DOI": "10.18494/SAM.2021.3153", "PubYear": 2021, "Volume": "33", "Issue": "3", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Biomaterials and Bioengineering, Tokyo Medical and Dental University, 2-3-10 Kandasurugadai, Chiyoda-ku, Tokyo, 101-0062, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Physics and Computing, Tokyo University, 7-3-1 Hongo, Bunkyo-ku, Tokyo, 113-8656, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Biomaterials and Bioengineering, Tokyo Medical and Dental University, 2-3-10 Kandasurugadai, Chiyoda-ku, Tokyo, 101-0062, Japan, Institute of Innovative Research, Tokyo Institute of Technology, 4259, Midori-ku, Yokohama, Kanagawa, 226-8503, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Riverfield Inc, Yotsuya Medical Bldg., 20 Samon-cho, Shinjuku-ku, Tokyo, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Physics and Computing, Tokyo University, 7-3-1 Hongo, Bunkyo-ku, Tokyo, 113-8656, Japan"}], "References": []}, {"ArticleId": 87174234, "Title": "Methodological tools for university transfer of high-demand nanotechnologies to the regional building industry", "Abstract": "Introduction. The development of methodological monitoring tools to implement building nanomaterials into production is an integral element of designing a mechanism for effective management of the development of business structures. University entrepreneurship and research and educational centers in the ecosystem trend are considered as central actors in the process of creating tools for university transfer of nanobinders for construction purposes. Methods and materials. The process of forming digital competencies among students and teachers in the process of commercialization of scientific developments of a construction university (institute, faculty, department) should be considered as the result of fractal interactions. The development of the innovative ecosystem of the university is achieved by the effective implementation of the process of transferring the results of intellectual activity for the creation of gypsum and ceramic nanocomposites, which are in demand by the regional construction industry. Results. The intellectual and technological potential of universities that train bachelor’s and master’s students for the construction industry determines the prospects for the successful development of the industry in an innovative society. Accelerated promotion of investment developments, requested nanotechnologies of universities, provides universities with additional extrabudgetary funding. On the example of the development of technology for producing small-piece wall and partition products based on nanostructured gypsum binders, they were tested in experimental industrial conditions. Discussion. Effective methodological tools for the transfer of nanotechnological university engineering to the construction industry are: the creation of basic departments at enterprises and the successful functioning of research and educational centers, the participation of employers in educational and industrial practice, etc. From the point of view of laborious commercialization and transfer of scientific developments, the effective way from the idea to the widespread introduction of high-tech products is the real application of the intellectual potential of the teaching staff of the university, institute, departments. Conclusions. The engineering of methodological tools for reliable monitoring of the attractiveness of the regional business ecosystem for the generation and development of transfer processes of popular nanomaterials is an integral element of designing a mechanism for effective management of business structures in construction. Due to the formation of the innovative ecosystem of the university, an effective implementation of the process of commercialization of the results of intellectual activity in the field of nanotechnology, which are in demand by the construction industry of the region, is achieved. © Shayakhmetov U.Sh., Larkina A.A., Khalikov R.M., Sinitsin D.A., Nedoseko I.V., 2021.", "Keywords": "Ceramic nanocomposites; Commercialization of nanotechnologies; Nanostructured gypsum products; Transfer of intellectual activity", "DOI": "10.15828/2075-8545-2021-13-1-12-17", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 36948, "JournalTitle": "Nanotechnologies in Construction: A Scientific Internet-Journal", "ISSN": "", "EISSN": "2075-8545", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department «Engineering physics and physics of materials», Bashkir State University, Ufa, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department «Machines and Apparatuses of Food Production», Moscow State University of Technologies and Management, Moscow, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department «Building Constructions», Ufa State Petroleum Technological University, Ufa, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department «Building Constructions», Ufa State Petroleum Technological University, Ufa, Russian Federation"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department «Building Constructions», Ufa State Petroleum Technological University, Ufa, Russian Federation"}], "References": []}, {"ArticleId": 87174235, "Title": "Biodegradable polymer materials and modifying additives: state of the art. Part II", "Abstract": "One of the most demanded materials on the planet is plastic, the excellent performance of which contributes to the accumulationof a significant amount of waste on its basis. In this regard, a new approach to the development of these materials hasbeen formed in scientific circles: the production of polymer composites with constant performance characteristics for a certainperiod and then capable of destruction under the influence of environmental factors. Analysis of the current state of the industry of polymeric materials shows that the most urgent is the use of such classical polymers as polyolefins and polyvinyl chloride. First of all, the optimal solution to this problem due to the lack of a suitable replacement for traditional polymers is the development of composites based on them with the use of biodegradable additives. In this case, a set of problems associated with waste disposal issolved: the decomposition period of the recycled waste is significantly reduced, the territories required for plastic waste are reduced. The paper outlines the preconditions for the emergence and further development of the field of biodegradable polymers. The mainquantitative characteristics of the production capacities of manufactured bioplastics by types, regions and industries of applicationare given. Modern methods of reducing and regulating the degradation time of polymer materials are presented. The main global and domestic manufacturers of biodegradable polymers and their products are listed, as well as a list of the main manufacturers of biodegradable additives for polymeric materials. Modern types of bioplastics based on renewable raw materials, composites with their use, aswell as modified materials from natural and synthetic polymers are listed. The main methods for determining the biodegradability of existing bioplastics are described. © Mazitova A.K., Aminova G.K., Zaripov I.I., Vikhareva I.N., 2021.", "Keywords": "Biodegradable additives; Biodegradation; Petrochemical raw materials; Plant sources; Plasticizers; Polymers", "DOI": "10.15828/2075-8545-2021-13-1-32-38", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 36948, "JournalTitle": "Nanotechnologies in Construction: A Scientific Internet-Journal", "ISSN": "", "EISSN": "2075-8545", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Applied and Natural Sciences Department, Ufa State Petroleum Technological University, Ufa, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Applied and Natural Sciences Department, Ufa State Petroleum Technological University, Ufa, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Applied and Natural Sciences Department, Ufa State Petroleum Technological University, Ufa, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Applied and Natural Sciences Department, Ufa State Petroleum Technological University, Ufa, Russian Federation"}], "References": []}, {"ArticleId": 87174268, "Title": "DeepUWF-plus: automatic fundus identification and diagnosis system based on ultrawide-field fundus imaging", "Abstract": "<p>Poor eye health is a major public health problem, and the timely detection and diagnosis of fundus abnormalities is important for eye health protection. Traditional imaging models can hinder the comprehensive evaluation of fundus abnormalities due to the use of a narrow field of view. The emerging ultrawide-field (UWF) imaging model surpasses this limitation in a non invasive, wide-view manner and is suitable for fundus observation and screening. Nevertheless, manual screening is labour intensive and subjective, especially in the absence of an ophthalmologist. Therefore, a set of auxiliary screening methods for a fundus screening service using a combination of deep learning and UWF imaging technology, which is designated as DeepUWF-Plus, is proposed. This service includes a subsystem for the screening of fundus, a subsystem for the identification of abnormalities regarding four important fundus locations, and a subsystem for the diagnosis of four retinal diseases that threaten vision. The influence of two-stage and one-stage classification strategies on the prediction performance of the model is experimentally investigated to alleviate severe class imbalance and similarity between classes, and evaluate the effectiveness and reliability of the system. Our experimental results show that DeepUWF-Plus is effective when using the two-stage strategy, especially for identifying signs or symptoms of minor diseases. DeepUWF-Plus can improve the practicality of fundus screening and enable ophthalmologists to provide more comprehensive fundus assessments.</p>", "Keywords": "Deep learning; Convolutional neural network; Ultra-wide-field (UWF) imaging; Fundus screening", "DOI": "10.1007/s10489-021-02242-4", "PubYear": 2021, "Volume": "51", "Issue": "10", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Machine Intelligence Laboratory, College of Computer Science, Sichuan University, Chengdu, People’s Republic of China"}, {"AuthorId": 2, "Name": "Yan Dai", "Affiliation": "Mianyang Central Hospital, Mianyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medicine, University of Electronic Science and Technology of China, Chengdu, China;The Department of Ophthalmology, Sichuan Academy of Medical Sciences and Sichuan Provincial Peoples Hospital, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Machine Intelligence Laboratory, College of Computer Science, Sichuan University, Chengdu, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medicine, University of Electronic Science and Technology of China, Chengdu, China;The Department of Ophthalmology, Sichuan Academy of Medical Sciences and Sichuan Provincial Peoples Hospital, Chengdu, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Machine Intelligence Laboratory, College of Computer Science, Sichuan University, Chengdu, People’s Republic of China"}], "References": []}, {"ArticleId": 87174269, "Title": "Development of new encryption system using Brownian motion based diffusion", "Abstract": "<p>A new cryptographic model is proposed incorporating intertwining logistic map based confusion process and two dimensional Brownian Motion based diffusion algorithm. An intertwining logistic map is utilized in the algorithm to provide better distribution of random numbers and to overcome blank and stable windows noticed in the schematic of logistic map \\(^{\\prime }\\) s bifurcation. Most of the existing image encryption models use raw images without any modification for the confusion and diffusion processes. Their main drawback is that original pixel values remain the same. This problem is overcome by a Pseudo random generator based key stream that modifies pixel values. Further, an intertwining logistic map based confusion process enhances the key sensitivity and complex relationship is created between cipher and test image. Finally, two dimensional Brownian motion based diffusion is applied to bind pixels with each other to such a degree that even one bit modification in the original image affects most of the pixels in the cipher. This makes the model sensitive to change in pixel value or secret key. Various test results indicate that the proposed encryption model can encrypt the plain image into a cipher of random binary sequence. Correlation coefficients among cipher image pixels is found to be negligible, NPCR values are greater than 99.60% (average) and the model is resistive against brute-force, statistical and differential attacks.</p>", "Keywords": "2D Brownian motion; Diffusion; Intertwining logistic map; Encryption", "DOI": "10.1007/s11042-021-10665-x", "PubYear": 2021, "Volume": "80", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Ramanujan College, University of Delhi, Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Ramanujan College, University of Delhi, Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> College(M), University of Delhi, North Campus, Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Informatics and Communication, University of Delhi, South Campus, New Delhi, India"}], "References": [{"Title": "An improved robust image watermarking by using different embedding strengths", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "Page": "12041", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Encryption for multimedia based on chaotic map: Several scenarios", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "19717", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An image encryption scheme using sequence generated by interval bisection of polynomial function", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "43-44", "Page": "31715", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Symmetric keys image encryption and decryption using 3D chaotic maps with DNA encoding technique", "Authors": "<PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON>;  <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "43-44", "Page": "31739", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A robust hybrid digital watermarking technique against a powerful CNN-based adversarial attack", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "43-44", "Page": "32769", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An efficient batch images encryption method based on DNA encoding and PWLCM", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "1", "Page": "943", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Cross-plane colour image encryption using a two-dimensional logistic tent modular map", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "1063", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 87174297, "Title": "A novel decoupling dynamic method with third-order accuracy and controllable dissipation", "Abstract": "In this paper, an efficient explicit integration method with third-order accuracy and controllable dissipation properties is proposed, and a new accuracy criterion is presented to evaluate relative errors of integration methods. By using this method, a desirable accuracy for responses in the low-frequency domain can be attained, spurious vibrations in the high-frequency domain can be strongly suppressed. Moreover, for the proposed method, update values of the displacement and velocity do not depend on the new state. Hence, it is convenient for decoupling a multi-spring coupling dynamic system to achieve high computational efficiency. The standard formulations of the proposed method, including the second-order and third-order accuracy schemes, are derived by discussing the local truncation error, difference accuracy, and stability firstly. Subsequently, the accuracy properties of the proposed method, e.g., the period elongation and amplitude decay, are analyzed in comparison with other available state-of-the-art explicit integration methods. Additionally, the properties of the proposed method, including the accuracy, convergence, dissipation, efficiency, and nonlinearity, are evaluated by using three examples. More specifically, the accuracy and convergence are evaluated through an example with theoretical solutions; the dissipation and efficiency are investigated by analyzing a geodesic dome truss; and the effectiveness in terms of the nonlinearity is investigated via a typical nonlinear dynamic system.", "Keywords": "Explicit method ; Efficiency ; Third-order accuracy ; Controllable dissipation ; Decoupling property", "DOI": "10.1016/j.compstruc.2021.106512", "PubYear": 2021, "Volume": "249", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Central South Univ., Changsha, Hunan 410075, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Civil Engineering, Central South Univ., Changsha, Hunan 410075, China"}, {"AuthorId": 3, "Name": "C.S. Cai", "Affiliation": "Dept. of Civil and Environmental Engineering, Louisiana State Univ., Baton Rouge, LA 70803, United States;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Bridge Engineering, Southwest Jiaotong University, Chengdu, China"}], "References": []}, {"ArticleId": 87174311, "Title": "Sources of value creation in aggregator platforms for digital services in agriculture - insights from likely users in Kenya", "Abstract": "A fragmented digital agriculture ecosystem has been linked to the slow scale-out of digital platforms and other digital technology solutions for agriculture. This has undermined the prospects of digitalizing agriculture and increasing sectoral outcomes in sub-Saharan African countries. We conceptualized an aggregator platform for digital services in agriculture as a special form of digital platforms that can enhance the value and usage of digital technologies at the industry level. Little is known about how such a platform can create value as a new service ecology in agriculture. We set out to examine the underlying structure and prioritizations of value creation sources in such a platform from the perspective of likely users in Kenya. We used a parallel convergent mixed methods approach to the study. Confirmatory factor analysis of data from 405 respondents supported a two-factor structure, being an adaptation of the framework on value creation sources in e-Business by Am<PERSON>, <PERSON>, & <PERSON>, C. (2001). We conceptualized the two factors as platform-wide efficiency and loyalty-centeredness . User experience related search costs were most impactful on platform-wide efficiency, while loyalty-centeredness was impacted most by providing guarantees for quality and reliability to platform users. Thematic analysis of 369 qualitative responses obtained platform inclusivity - comprising value chain coverage and digital inclusivity , as additional considerations for amplifying sector-wide benefits of an aggregator platform for digital services in agriculture. We discuss implications for policy and practice in the light of resource constraints and the promise to digitally transform agriculture in SSA countries.", "Keywords": "digital agriculture ; digital platforms ; loyalty centredness ; platform-wide efficiency ; platform inclusivity ; value creation ; digital transformation", "DOI": "10.1016/j.digbus.2021.100007", "PubYear": 2021, "Volume": "1", "Issue": "2", "JournalId": 82670, "JournalTitle": "Digital Business", "ISSN": "2666-9544", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing and Informatics, University of Nairobi, Chiromo Campus, P. O. Box 30197 - 00100 GPO, Nairobi, Kenya;Corresponding author at: University of Nairobi, School of Computing and Informatics, P. O. Box 30197 - 00100 GPO, Nairobi, Kenya"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing and Informatics, University of Nairobi, Chiromo Campus, P. O. Box 30197 - 00100 GPO, Nairobi, Kenya"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Business, University of Nairobi, Lower Kabete Campus, P. O. Box 30197 - 00100 GPO, Nairobi, Kenya"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Informatics, University of Nairobi, Chiromo Campus, P. O. Box 30197 - 00100 GPO, Nairobi, Kenya"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Development Research, University of Bonn, Genscherallee 3, D-53113, Bonn, Germany"}], "References": [{"Title": "Closed or open platform? the nature of platform and a qualitative comparative analysis of the performance effect of platform openness", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "", "Page": "101007", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "An effective approach to mobile device management: Security and privacy issues associated with mobile applications", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "1", "Page": "100001", "JournalTitle": "Digital Business"}, {"Title": "Digital platforms for development: Foundations and research agenda", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "6", "Page": "869", "JournalTitle": "Information Systems Journal"}]}, {"ArticleId": 87174491, "Title": "WITHDRAWN: A Sustainable Food Waste Management in an Institutional Hostel Environment Using IoT and Machine Learning", "Abstract": "This article has been withdrawn at the request of the author(s) and/or editor. The Publisher apologizes for any inconvenience this may cause. The full Elsevier Policy on Article Withdrawal can be found at  https://www.elsevier.com/about/our-business/policies/article-withdrawal", "Keywords": "", "DOI": "10.1016/j.suscom.2021.100549", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Francis Xavier Engineering College, Tirunelveli, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, SCAD College of Engineering and Technology, Cheranmahadevi, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Technology and Engineering, Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Anna University, Tiruchirappalli, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, National Engineering College, Kovilpatti, Tamilnadu, India"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computing and Mathematics, Manchester Metropolitan University, UK"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Sejong University, Seoul, South Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "University of Salford, Manchester, UK"}], "References": [{"Title": "Enhanced resource allocation in mobile edge computing using reinforcement learning based MOACO algorithm for IIOT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "355", "JournalTitle": "Computer Communications"}, {"Title": "Energy enhancement using Multiobjective Ant colony optimization with Double Q learning algorithm for IoT based cognitive radio networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "481", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 87174618, "Title": "Hierarchical distributed model predictive control based on fuzzy negotiation", "Abstract": "This work presents a hierarchical distributed model predictive control approach for multiple agents with cooperative negotiations based on fuzzy inference. Specifically, a fuzzy-based two-layer control architecture is proposed. In the lower control layer, there are pairwise negotiations between agents according to the couplings and the communication network. The resulting pairwise control sequences are sent to a coordinator in the upper control layer, which merges them to compute the final ones. Furthermore, conditions to guarantee feasibility and stability in the closed-loop system are provided. The proposed control algorithm has been tested on an eight-coupled tank plant via simulation.", "Keywords": "Model predictive control ; Hierarchical distributed control ; Pairwise negotiations ; Fuzzy logic ; Multi-agent systems ; Stability", "DOI": "10.1016/j.eswa.2021.114836", "PubYear": 2021, "Volume": "176", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Systems Engineering and Automation, Escuela Técnica Superior de Ingeniería, Universidad de Sevilla, Av. Camino de los Descubrimientos, s/n., 41092 Seville, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computing and Automation, Escuela Técnica Superior de Ingeniería Industrial, Universidad de Salamanca, A<PERSON>. <PERSON>, Béjar, 37700 Salamanca, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Systems Engineering and Automation, Escuela Técnica Superior de Ingeniería, Universidad de Sevilla, Av. Camino de los Descubrimientos, s/n., 41092 Seville, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing and Automation, Escuela Técnica Superior de Ingeniería Industrial, Universidad de Salamanca, A<PERSON>. <PERSON>, Béjar, 37700 Salamanca, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing and Automation, Facultad de Ciencias, Universidad de Salamanca, Plaza de la Merced, s/n, 37008 Salamanca, Spain"}], "References": []}, {"ArticleId": 87174680, "Title": "‘Into the wolves’ den: an investigation of predictors of sexism in online games’", "Abstract": "Online sexism against female gamers is reportedly common and pervasive, causing serious problems. To help solve these problems, the study identified various predictors of online game sexism, which is hypothesised to predict actual in-game harassment. Different from previous studies, the study approaches the problems from the perspective of perpetrators rather than victims. We proposed a theoretical model that include three groups of predictors: offline sexist beliefs (masculine norms and hostile sexism), game-related factors (perceived territoriality, advancement, and competition), and environmental factors (peer harassment and play time). The model was tested against online survey data collected from a sample of 528 male gamers in South Korea with age range of 14–64 years ( M = 34.70, SD = 12.81). The results showed that all the predictors, except competition and play time, were significantly associated with online game sexism, which mediated the relationships between the predictors and online sexual harassment. Perceived territoriality and peer harassment were found to have direct and positive effects on harassment. The findings are expected to contribute to developing more effective measures for preventing the hostility and aggression against female gamers by providing a new and more thorough diagnosis of the underlying causes of the problems.", "Keywords": "Female gamers ; online game sexism ; perceived territoriality ; peer harassment ; in-game harassment", "DOI": "10.1080/0144929X.2021.1899287", "PubYear": 2022, "Volume": "41", "Issue": "8", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Arts and Social Science, Monash University Malaysia, Selangor, Malaysia"}, {"AuthorId": 2, "Name": "Poong Oh", "Affiliation": "<PERSON><PERSON> School of Communication and Information, Nanyang Technological University, Singapore, Singapore"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Journalism and Communication, Kyung Hee University, Seoul, Korea"}], "References": []}, {"ArticleId": 87174682, "Title": "On the exploitation of GPS-based data for real-time visualisation of pedestrian dynamics in open environments", "Abstract": "Over the past few years, real-time visualisation of pedestrian dynamics has become more crucial to successfully organise and monitor open-crowded events. However, the process of collecting, efficiently handling and visualising a large volume of pedestrians' dynamic data in real time is challenging. This challenge becomes even more pronounced when pedestrians move in large-size, high-density, open and complex environments. In this article, we propose an efficient and accurate approach to acquire, process and visualise pedestrians' dynamic behaviour in real time. Our goal in this context is to produce GPS-based heat maps that assist event organisers as well as visitors in dynamically finding crowded spots using their smartphone devices. To validate our proposal, we have developed a prototype system for experimentally evaluating the quality of the proposed solution using real-world and simulation-based experimental datasets. The first phase of experiments was conducted in an open area with 37,000 square meters in Palestine. In the second phase, we have carried out a simulation for 5000 pedestrians to quantify the level of efficiency of the proposed system. We have utilised PHP scripting language to generate a larger-scale sample of randomly moving pedestrians across the same open area. A comparison with two well-known Web-based spatial data visualisation systems was conducted in the third phase. Findings indicate that the proposed approach can collect pedestrian's GPS-based trajectory information within 4 m horizontal accuracy in real time. The system demonstrated high efficiency in processing, storing, retrieving and visualising pedestrians' motion data (in the form of heat maps) in real time.", "Keywords": "Real-time visualisation ; pedestrian dynamics ; crowd management system ; GPS data ; heat map visualisation", "DOI": "10.1080/0144929X.2021.1896781", "PubYear": 2022, "Volume": "41", "Issue": "8", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Advanced Simulation, Forschungszentrum Jülich, Jülich, Germany;Faculty of Engineering and Information Technology, Computer Science Department, An-Najah National University, Nablus, Palestine"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Information Technology Department, Faculty of Engineering and Information Technology, Arab American University, Jenin, Palestine"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Advanced Simulation, Forschungszentrum Jülich, Jülich, Germany"}], "References": [{"Title": "Towards insight-driven sampling for big data visualisation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "7", "Page": "788", "JournalTitle": "Behaviour & Information Technology"}]}, {"ArticleId": 87174870, "Title": "Match matrix aggregation enhanced transition-based neural network for SQL parsing", "Abstract": "Nowadays many neural networks have been widely employed for semantic parsing problems especially for Structured Query Language (SQL) parsing, which aims at transforming natural language sentences into SQL representations. Selecting proper table headers in SQL tasks is extremely important, and the main cause of performance drop is that attention mechanism in neural models sometimes obtains wrong word-level distribution over headers. In order to obtain better header selection, we propose a match matrix aggregation enhanced SQL parser to consider the outer character-level ROUGE-L match information between the question and headers, then dynamically combine it with the inner generated attention matrix. We also introduce customized BERT and extra semantic information of the question and headers to further improve the performance. The results on two SQL datasets demonstrate that our method achieves an inspiring performance and highly outperforms other state-of-the-art alternatives.", "Keywords": "SQL parsing ; Match matrix aggregation ; Character-level ROUGE-L ; Generated attention matrix", "DOI": "10.1016/j.neucom.2021.03.005", "PubYear": 2021, "Volume": "445", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "Dongdong Xie", "Affiliation": "Key Laboratory of Aerospace Information Security and Trusted Computing, Ministry of Education, School of Cyber Science and Engineering, Wuhan University, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Aerospace Information Security and Trusted Computing, Ministry of Education, School of Cyber Science and Engineering, Wuhan University, Wuhan, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Aerospace Information Security and Trusted Computing, Ministry of Education, School of Cyber Science and Engineering, Wuhan University, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Aerospace Information Security and Trusted Computing, Ministry of Education, School of Cyber Science and Engineering, Wuhan University, Wuhan, China"}], "References": []}, {"ArticleId": 87174872, "Title": "EWNet: An early warning classification framework for smart grid based on local-to-global perception", "Abstract": "Early warning mechanism is crucial for maintaining the security and reliability of the power grid system. It remains to be a difficult task in a smart grid system due to complex environments in practice. In this paper, by considering the lack of vision-based datasets and models for early warning classification, we constructed a large-scale image dataset, namely EWSPG1.0, which contains 12,113 images annotated with five levels of early warnings. Moreover, 104,448 object instances with respect to ten categories of high-risk objects and power gird infrastructure were annotated with labels, bounding boxes and polygon masks. On the other hand, we proposed a local-to-global perception framework for arly warning classification, namely EWNet. Specifically, a local patch responsor is trained by using image patches extracted from the training set according to the labeled bounding box information of objects. The capability of recognizing high-risk objects and power grid infrastructure is transferred by loading the trained local patch responsor with frozen weights. Features are then fed into a feature integration module and a global classification module for early warning classification of an entire image. In order to evaluate the proposed framework, we benchmarked the proposed framework on our constructed dataset with 11 state-of-the-art deep convolutional neural networks (CNNs)-based classification models. Experimental results exhibit the effectiveness of our proposed method in terms of Top-1 classification accuracy. They also indicate that vision-based early warning classification remains challengeable under power grid surveillance and needs further study in future work.", "Keywords": "Power grid surveillance ; Early warning classification ; Deep learning ; Image recognition", "DOI": "10.1016/j.neucom.2021.03.007", "PubYear": 2021, "Volume": "443", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Xi’an Jiaotong University, Xi’an 710049, China;State Grid Shaanxi Electric Power Research Institute, Xi’an 710110, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology Shenzhen, Shenzhen 518005, China"}, {"AuthorId": 3, "Name": "Yuzhu <PERSON>", "Affiliation": "Harbin Institute of Technology Shenzhen, Shenzhen 518005, China"}, {"AuthorId": 4, "Name": "Shengchang Ji", "Affiliation": "Xi’an Jiaotong University, Xi’an 710049, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Xi’an Jiaotong University, Xi’an 710049, China"}, {"AuthorId": 6, "Name": "Haofei Sun", "Affiliation": "State Grid Shaanxi Electric Power Research Institute, Xi’an 710110, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "State Grid Xi’an Electric Power Supply Company, Xi’an 710032, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Shaanxi Electric Power Company, Xi’an 710054, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Shaanxi Electric Power Research Institute, Xi’an 710110, China"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "State Grid Shaanxi Electric Power Research Institute, Xi’an 710110, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology Shenzhen, Shenzhen 518005, China"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology Shenzhen, Shenzhen 518005, China;Corresponding author"}], "References": [{"Title": "Deep Learning for Generic Object Detection: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "261", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 87174873, "Title": "Image super-resolution using multi-granularity perception and pyramid attention networks", "Abstract": "Recently, single image super-resolution (SISR) has been widely applied in the fields of multimedia and computer vision communities and obtained remarkable performance. However, most current methods ignore to utilize multi-granularity features of low-resolution (LR) image to further improve the SISR performance. And the channel and spatial features obtained from original LR images are treated equally, resulting in unnecessary computations for abundant uninformative features, thereby hindering the representational ability of super-resolution (SR) models. In this paper, we present a novel Multi-Granularity Pyramid Attention Network (MGPAN) which fully exploits the multi-granularity perception and attention mechanisms to improve the quality of reconstructed images. We design a multi-branch dilated convolution layer with varied kernels corresponding to receptive fields of different sizes to modulate multi-granularity features for adaptively capturing more important information. Moreover, a novel spatial pyramid pooling attention (SPPA) module is constructed to integrate the channel-wise and multi-scale spatial information, which is beneficial to compute the response values from the multi-scale regions of each neuron, and then establish the accurate mapping from low to high dimensional solution space. Besides, for long-short-term information preservation and information flow enhancement, we adopt the short, long, and global skip connection structures to concatenate and fuse the states of each module, which can improve the SR network performance effectively. Extensive experiments on several standard benchmark datasets show that the proposed MGPAN can provide state-of-the-art or even better performance in both quantitative and qualitative measurements.", "Keywords": "Super-resolution ; Convolutional neural networks ; Multi-granularity perception ; Spatial pyramid pooling attention ; Multiple skip connections", "DOI": "10.1016/j.neucom.2021.03.010", "PubYear": 2021, "Volume": "443", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, NO. 195, Chuangxin Road, Hunnan District, Shenyang, China;Corresponding author"}, {"AuthorId": 2, "Name": "Chengdong Wu", "Affiliation": "Northeastern University, NO. 195, Chuangxin Road, Hunnan District, Shenyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northeastern University, NO. 195, Chuangxin Road, Hunnan District, Shenyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northeastern University, NO. 195, Chuangxin Road, Hunnan District, Shenyang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, NO. 195, Chuangxin Road, Hunnan District, Shenyang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "University of Sydney, NSW 2006, Sydney, Australia"}], "References": [{"Title": "Multi-scale feature fusion residual network for Single Image Super-Resolution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "379", "Issue": "", "Page": "334", "JournalTitle": "Neurocomputing"}, {"Title": "Content adaptive single image interpolation based super resolution of compressed images", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "3", "Page": "3014", "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)"}, {"Title": "Image super-resolution via multi-view information fusion networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "402", "Issue": "", "Page": "29", "JournalTitle": "Neurocomputing"}, {"Title": "Hierarchical dense recursive network for image super-resolution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107475", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 87174958, "Title": "Editorial: Special Issue on Interactive Unmanned Systems and Intelligent Applications", "Abstract": "", "Keywords": "", "DOI": "10.1142/S2301385021020015", "PubYear": 2021, "Volume": "9", "Issue": "3", "JournalId": 26691, "JournalTitle": "Unmanned Systems", "ISSN": "2301-3850", "EISSN": "2301-3869", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Toronto, Canada"}], "References": []}, {"ArticleId": 87174962, "Title": "An excellent impedance-type humidity sensor based on halide perovskite CsPbBr3 nanoparticles for human respiration monitoring", "Abstract": "Humidity sensors with fast response, high accuracy and low-power consumption are critical importance in smart home and health monitoring applications. Herein, we demonstrate an excellent impedance relative humidity sensor based on all-inorganic halide perovskite CsPbBr<sub>3</sub> nanoparticles (NPs) under low working-voltage (20 mV). The CsPbBr<sub>3</sub> NPs humidity sensor exhibits fast response and recovery behavior at room temperature. Moreover, the sensor demonstrates excellent repeatability, low hysteresis and good stability. The humidity sensing mechanism of CsPbBr<sub>3</sub> NPs is further studied and discussed by employing an electrochemical impedance spectroscopy measurement. Furthermore, the respiratory monitoring face mask based on CsPbBr<sub>3</sub> humidity sensor shows accurate and stable performance, suggesting perovskite CsPbBr<sub>3</sub> NPs based humidity sensor have great potential in future real-time health status monitoring applications.", "Keywords": "Impedance-type humidity sensor ; Fast response ; Perovskite ; Nanoparticles ; Respiration monitoring ; Low working-voltage", "DOI": "10.1016/j.snb.2021.129772", "PubYear": 2021, "Volume": "337", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Physics and Electronic Engineering, Chongqing Normal University, Chongqing 401331, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Chongqing University Cancer Hospital, Chongqing 400030, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Photovoltaic and Energy Conservation Materials, Anhui Institute of Optics and Fine Mechanics, Chinese Academy of Sciences, Hefei 230031, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 10, "Name": "Bingsheng Du", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 12, "Name": "Xiao<PERSON> Fang", "Affiliation": "Key Laboratory of Photovoltaic and Energy Conservation Materials, Anhui Institute of Optics and Fine Mechanics, Chinese Academy of Sciences, Hefei 230031, China"}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China;Corresponding authors"}, {"AuthorId": 14, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Optoelectronic Technology and Systems of the Education Ministry of China, College of Optoelectronic Engineering, Chongqing University, Chongqing 400044, China;Corresponding authors"}], "References": []}, {"ArticleId": 87174988, "Title": "Improved BTEX gas sensing characteristics of thermally treated TiO2 hierarchical spheres manifested by high-energy {001} crystal facets", "Abstract": "Aromatic hydrocarbons like benzene, toluene, ethylbenzene and xylene (BTEX) are lethal volatile organic compounds, which should be precisely monitored to maintain occupational safety and indoor air quality. Thus, herein, the superior sensitivity towards toluene and ethylbenzene, among other BTEX vapours was exhibited by thermally treated sea-urchin like TiO<sub>2</sub> hierarchical spheres achieved via hydrothermal method. X-Ray diffraction patterns displayed an improvement in crystallinity with annealing temperature and a total phase transformation to rutile phase, due to thermal instability of anatase phase. The Brunauer-Emmett-Teller surface area analyzer showed that the pore diameter increased with annealing temperature, whereas a thermal quench after 500 °C was observed on the photoluminescence results. To demonstrate the uniqueness of hierarchical structure and the effect of annealing at different temperatures, the gas sensing performances towards BTEX vapours was evaluated at various conditions. The sensor annealed at 500 °C (T5) displayed temperature-dependent selectivity towards ethylbenzene at 75 °C, with a detection limit of ∼ 0.1 ppm (i.e. 100 ppb). The sensor annealed at 700 °C (T7) revealed the highest response of 13 towards toluene at 150 °C, due to exposed high surface energy {001} facets, containing plentiful active oxygen species, which are more active for adsorption of toluene. The T7 sensor demonstrated a clear stability towards toluene for about 90 days, displaying only minimal drift. The fundamental gas sensing mechanism associated to the detection of BTEX vapour was discussed in detail.", "Keywords": "TiO<sub>2</sub> hierarchical spheres ; Annealing ; BTEX ; Gas sensing", "DOI": "10.1016/j.snb.2021.129774", "PubYear": 2021, "Volume": "338", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Zamaswazi P. <PERSON>", "Affiliation": "DSI/CSIR, Centre for Nanostructures and Advanced Materials, Council for Scientific Industrial Research, Pretoria, 0001, South Africa;Department of Physics, University of the Free State, P. O. Box 339, Bloemfontein, ZA9300, South Africa;Department of Physics, University of Limpopo, Private Bag X1106, Sovenga, 0727, South Africa;Corresponding authors at: Department of Physics, University of Limpopo, Private Bag X1106, Sovenga, 0727, South Africa"}, {"AuthorId": 2, "Name": "Teboho P<PERSON>", "Affiliation": "DSI/CSIR, Centre for Nanostructures and Advanced Materials, Council for Scientific Industrial Research, Pretoria, 0001, South Africa;Department of Physics, University of the Free State, P. O. Box 339, Bloemfontein, ZA9300, South Africa"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "DSI/CSIR, Centre for Nanostructures and Advanced Materials, Council for Scientific Industrial Research, Pretoria, 0001, South Africa;Department of Physics, University of the Free State, P. O. Box 339, Bloemfontein, ZA9300, South Africa"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, University of the Free State, P. O. Box 339, Bloemfontein, ZA9300, South Africa"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Physics, University of the Free State, P. O. Box 339, B<PERSON>emfontein, ZA9300, South Africa;Department of Physics, University of Limpopo, Private Bag X1106, Sovenga, 0727, South Africa;Corresponding authors at: Department of Physics, University of Limpopo, Private Bag X1106, Sovenga, 0727, South Africa"}], "References": [{"Title": "Variation of shell thickness in ZnO-SnO2 core-shell nanowires for optimizing sensing behaviors to CO, C6H6, and C7H8 gases", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "302", "Issue": "", "Page": "127150", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 87175034, "Title": "RETRACTED ARTICLE: Pedestrian identification\n using motion-controlled deep neural network in real-time visual surveillance", "Abstract": "<p>In the computer vision applications such as security surveillance and robotics, pedestrian identification shows much attention in the last decade. This is usually achieved by human biometrics. Besides human biometrics, sometimes it is required to identify pedestrians at a distance. This could be accomplished based on a fact of different whole-body appearances. The real-time pedestrian identification is a challenging task due to several factors such as illumination effects, noise, change in viewpoint, and video resolution. The more recent, the deep neural network (DNN) shows a massive performance for various real-world applications. In this article, we present a real-time architecture for pedestrian identification using motion-controlled DNN. In the proposed architecture, the motion vectors are calculating using optical flow and then utilized in the next step, named features extraction. Two types of features, such as HOG and DNN, are computing. The pre-trained VGG19 CNN model is employing and trained through transfer learning. The deep learning features are extracted from two layers—fully connected layers 7 and 8. Also, we proposed a feature selection method named Bayesian modeling along with LSVM. The best selected features of both HOG and DNN are finally fused in one matrix for final identification. The multi-class support vector machine classifier is used for final identification. The videos are recording in the real-time environment for the experimental process and achieve an average accuracy of 98.62%. Overall, identification accuracy shows the effectiveness of the proposed approach.</p>", "Keywords": "Pedestrian identification; Optical flow; HOG features; Deep learning; Features selection", "DOI": "10.1007/s00500-021-05701-9", "PubYear": 2023, "Volume": "27", "Issue": "1", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, HITEC University, Taxila, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Islamabad, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Islamabad, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Applied Computing and Technology, Noroff University College, Kristiansand, Norway"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Applications, KIIT Deemed to be University, Bhubaneswar, India"}], "References": [{"Title": "Vision Tracking: A Survey of the State-of-the-Art", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "1", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "A multilevel paradigm for deep convolutional neural network features selection with an application to human gait recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "7", "Page": "e12541", "JournalTitle": "Expert Systems"}, {"Title": "Human action recognition using fusion of multiview and deep features: an application to video surveillance", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "5", "Page": "14885", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A resource conscious human action recognition framework using 26-layered deep convolutional neural network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "28-29", "Page": "35827", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Robust local oriented patterns for ear recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "41-42", "Page": "31183", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 87175330, "Title": "Impact of the lossy image compression on the biometric system accuracy: a case study of hand biometrics", "Abstract": "Biometric recognition systems are used in several cases, to recognise people using images. Storing of large images require large storage space. To reduce the storage space, compression methods are employed. In this paper, we analyse the effect of lossy image compression on the performance of biometric identification systems. We propose a scheme to evaluate the recognition performance at low bitrates of hand images. The images are compressed using set partitioning in hierarchical trees (SPIHT) encoding. A powerful feature extraction algorithm based on quantising the phase information of the local Fourier transform is used. The nearest neighbour (NN) classifier and the support vector machine (SVM) classifier are employed to classify the feature extraction. The obtained results show at the compression does not significantly affect the performance of recognition operation at low bitrate for unimodal and multimodal systems. Thus, the low bitrate images perform equivalent to uncompressed images in the recognition system. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Biometrics; Finger-knuckle-print; FKP; Matching-score level; ML-LPQ; MSP; Multi-spectral palmprint; MultiLevel local phase quantisation; Set partitioning in hierarchical trees; SPIHT", "DOI": "10.1504/IJCAET.2021.113543", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratoire de Génie Electrique, Université Kasdi Merbah – Ouargla, Ouargla, 30000, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mathematics, Computer Science and Systems Laboratory, University Larbi <PERSON> of Tebessa, Tebessa, 12000, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, Aljouf University, Aljouf, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "LAMIH Laboratory, University of Valenciennes, UMR CNRS 8201 UVHC, France"}], "References": []}, {"ArticleId": 87175331, "Title": "Performance analysis of biorthogonal filter design using the lifting-based scheme for medical image transmission", "Abstract": "To minimise the storage space and for fast transfer of the digital images, it is necessary for the medical images to undergo image compression. There are various techniques in which the images are being diagnosed, based on that the image compression being performed. The choice of the filters in the image compression is an issue which affects the quality of the image. Hence, a novel biorthogonal filter using the lifting scheme has been developed. The proposed architecture gives the same characteristics of second generation wavelets. The proposed architecture is designed using MATLAB for different medical images and the PSNR, SNR, MSE, BPP having a compression of 96.85%. The verified model is synthesised using Xilinx Spartan-6 FPGA with the speed of –3. The results show that the designed method works at a frequency of 252.89 MHz, and absorbs the power 0.036 W for 2.5 volts with temperature at 25.2◦C. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Biorthogonal filter; DTDWT; DWT; Medical image transmission", "DOI": "10.1504/IJCAET.2021.113545", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON>etan <PERSON>", "Affiliation": "Visveswaraya Technological University, Jnana Sangama, Machhe, Belgaum, Karnataka, 590018, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, Cambridge Institute of Technology, Bangalore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, New Horizon College of Engineering, Bangalore, India"}], "References": []}, {"ArticleId": 87175332, "Title": "Dye sensitised solar power generating window: towards environmentally sustainable energy efficiency in ICT", "Abstract": "ICT equipment generates significant amount of green house gases. Presently, the ICT produces more than 830 million tons of CO2, that is about 2% of global CO2 emissions, and it is expected to double by 2020 (<PERSON> et al., 2012; <PERSON><PERSON><PERSON><PERSON> et al., 2017; Park, 2018). There is a need for ICT to standardise, energy consumption and emissions and investigate means to reduce energy consumption. The electricity consumption which dominates direct carbon footprint of the ICT sector can be reduced by using renewable energy sources. The dye-sensitised-solar-cell (DSC) is a renewable energy device that works well inside a built-environment. The transparent characteristic of DSCs makes it suitable for building-integrated-photovoltaic (BIPV) applications such as windows. In this study, we successfully fabricated and assembled a transparent DSC which was used to power ICT products. Such building-integrated DSC systems can potentially power ICT devices in homes and offices. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Building integrated photovoltaics; Dye-sensitised solar cells; ICT; Information communication technology; Sustainability", "DOI": "10.1504/IJCAET.2021.113547", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mehran University of Engineering and Technology, Main Indus Highway, Jamshoro, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mehran University of Engineering and Technology, Main Indus Highway, Jamshoro, Pakistan; University Technology PETRONAS, Persiaran UTP, Seri Iskander, Perak, 32610, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Mehran University of Engineering and Technology, Main Indus Highway, Jamshoro, Pakistan; University Technology PETRONAS, Persiaran UTP, Seri Iskander, Perak, 32610, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University Technology PETRONAS, Persiaran UTP, Ser<PERSON>, Perak, 32610, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University Technology PETRONAS, Persiaran UTP, Ser<PERSON>, Perak, 32610, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mehran University of Engineering and Technology, Main Indus Highway, Jamshoro, Pakistan"}], "References": []}, {"ArticleId": 87175333, "Title": "An empirical analysis of the statistical learning models for different categories of cross-project defect prediction", "Abstract": "Currently, the research community is addressing the problem of defect prediction with the availability of project defect data. The availability of different project data leads to extend the research on cross projects. Cross-project defect prediction has now become an accepted area of software project management. In this paper, an empirical study is carried out to investigate the predictive performance of availability within project and cross-project defect prediction models. Furthermore, different categories of cross-project data are taken for training and testing to analyse various statistical models. In this paper data models are analysed and compared using various statistical performance measures. The findings during the empirical analysis of the data models state that gradient boosting predictor outperforms in the cross-project defect prediction scenario. Results also infer that cross-project defect prediction is comparable with project defect prediction and has statistical significance. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Classification; Cross projects; Cross validation; Defect prediction; Homogeneous metrics; Machine learning; Quality assurance; Supervised learning; Training dataset; Within-project", "DOI": "10.1504/IJCAET.2021.113549", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Amity Institute of Information Technology, Amity University, Noida, Uttar Pradesh, 201313, India; AKG Engineering College, Uttar Pradesh, 201009, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Amity Institute of Information Technology, Amity University, Noida, Uttar Pradesh, 201313, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Amity Institute of Information Technology, Amity University, Noida, Uttar Pradesh, 201313, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Reliability, STQC Directorate, Department of Electronics and IT, Ministry of Communications and IT, Dr, VSI, Estate, Thiruvanmiyur, Chennai, India"}], "References": []}, {"ArticleId": 87175409, "Title": "Development of a White-Cane-Extension to Detect Automatic Doors", "Abstract": "The majority visually impaired persons are using a white cane to move within their environment. They avoid to collision the obstacles by detecting objects when swinging white cane touches them. An automatic door is opened when it detects the person comes to it by the sensor, for example, infrared sensor. This kind of automatic doors are very useful because we need not touch the doors in order to open them. However, for majority visually impaired persons, it is difficult to detected automatic doors because they detect the object by touching. In this paper, we propose the detection method which tells the existence of automatic doors to the visually impaired person. The device is consisted of an infrared sensor and a vibration motor. The infrared sensor is put on the tip of the white cane and the vibration motor is put on close to the hand. And when it detects the infrared signal, the vibration motor runs. Then the visually impaired persons can through the automatic door in safety without any cares", "Keywords": "", "DOI": "10.5121/ijasuc.2021.12101", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 8287, "JournalTitle": "International Journal of Ad hoc, Sensor & Ubiquitous Computing", "ISSN": "0976-2205", "EISSN": "0976-1764", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87175411, "Title": "ENHANCE NETWORK LIFETIME WIRELESS SENSOR WITH MULTIPLE SINK VERSATILITY", "Abstract": "Wireless Sensor Networks (WNS) is essential for ubiquitous computing. Sensor networks with variable sensing capabilities have seen applications in a broad range of regulators where correlations with the physical environment are relevant. Thus, the efforts have been made in attempt to detect atmosphere mainly by investigating sensor networks that integrates single wireless collector (i.e., sink). Many examples of WSNs' use are illustrated. It also has a variety of daunting problems such as topology parsing, routing and power management. The research focuses on solution to energy loss in WSN. This is a technical overview of a sensor system and a wireless network. There are still several challenges facing the WSNs.", "Keywords": "", "DOI": "10.47760/ijcsmc.2021.v10i02.014", "PubYear": 2021, "Volume": "10", "Issue": "2", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "A spectral index for the detection of algal blooms using Sentinel-2 Multispectral Instrument (MSI) imagery: a case study of Hulun Lake, China", "Abstract": "Lakes at a global level have increasingly experienced algal blooms in recent decades, and it has become a key challenge facing the aquatic ecological environment. Remote sensing technology is considered an effective means of algal bloom detection. This study proposed a novel algal bloom detection index (ABDI) based on Sentinel-2 Multispectral Instrument (MSI) data. The ABDI was evaluated by application to Hulun Lake, China. Areas of algal bloom detected by the ABDI were consistent with those identified from visual interpretation maps [the coefficient of determination = 0.87; root-mean-square error = 0.67 km<sup>2</sup>; overall accuracy >98%; Kappa coefficient >0.88]. The ABDI was less sensitive to thin cloud and turbid water compared to the floating algae index (FAI), adjusted floating algae index (AFAI), normalized difference vegetation index (NDVI), and enhanced vegetation index (EVI). Algal bloom dynamics in relation to meteorological factors in Hulun Lake were analysed using time-series MSI data, which indicated that algal blooms occurred mainly in summer and were distributed in the near-shore waters. Temperature, precipitation, sunshine duration, and wind speed as well as human activities were found to influence spatio-temporal patterns of algal blooms. The results indicate that ABDI is applicable to the detection of algal blooms under a variety of environmental conditions occurring in other regions, such as in the Taihu, Dianchi, and Chaohu lakes and the Yellow Sea. The results of this study can provide an operational algorithm for the detection of algal blooms and environmental management. Disclosure statement No potential conflict of interest was reported by the authors. Acknowledgments The authors appreciate ESA, USGS and NASA for providing Sentinel-2 MSI, Landsat-8 OLI, and MODIS data. We thank the two anonymous reviewers and the editor for their constructive comments which significantly improved this manuscript. Additional information Funding This work was supported by the National Natural Science Foundation of China under Grant 41961057; the Program for Young Talents of Science and Technology in the Universities of Inner Mongolia Autonomous Region under Grant NJYT-17-B04; and the Natural Science Foundation of Inner Mongolia Autonomous Region under Grant 2019MS04013.", "Keywords": "", "DOI": "10.1080/********.2021.1897186", "PubYear": 2021, "Volume": "42", "Issue": "12", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Geographical Science, Inner Mongolia Normal University, Hohhot, China"}, {"AuthorId": 2, "Name": "Song <PERSON>", "Affiliation": "College of Geographical Science, Inner Mongolia Normal University, Hohhot, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Geographical Science, Inner Mongolia Normal University, Hohhot, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Ecology and Environment, Inner Mongolia University, Hohhot, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ministry of Ecology and Environmental Protection, South China Institute of Environmental Sciences, Guangzhou, China"}], "References": []}, {"ArticleId": 87175819, "Title": "The Pandemic Made Me Do It: Changing Public Services", "Abstract": "", "Keywords": "", "DOI": "10.1080/10875301.2021.1891183", "PubYear": 2020, "Volume": "25", "Issue": "3", "JournalId": 25067, "JournalTitle": "Internet Reference Services Quarterly", "ISSN": "1087-5301", "EISSN": "1540-4749", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Saint Xavier University, Chicago, Illinois, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Illinois State University, Normal, Illinois, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Joliet Junior College, Joliet, Illinois, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Lake Forest College, Lake Forest, Illinois, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "<PERSON>’s College of Nursing, Springfield, Illinois, USA"}], "References": []}, {"ArticleId": 87175856, "Title": "<PERSON><PERSON><PERSON> Calon Siswa Baru Berbasis Mobile Andorid", "Abstract": "<p>The development of information and communication technology is rapidly increasing, giving a very big influence for the world of information and telecommunications technology. One of them is used to facilitate the registration of prospective new students. During this time the process of registering new students in various schools is still manual. In this study the author tries to resolve the above problems by utilizing an information system based on Android mobile for the registration system of prospective new students based on Android mobile to increase the effectiveness and flexibility of the school system. In the study of android applications used in the development of systems where new learners can input data through mobile phones. The study was conducted from the stages of needs analysis, design, implementation, to system testing. The purpose of this application is so that it can facilitate prospective new students in registering and the School in conducting and managing new student registration data.</p>", "Keywords": "andorid, aplikasi mobile, pendaftaran, siswa, smartphone.", "DOI": "10.52158/jacost.v1i1.32", "PubYear": 2020, "Volume": "1", "Issue": "1", "JournalId": 85412, "JournalTitle": "Journal of Applied Computer Science and Technology", "ISSN": "", "EISSN": "2723-1453", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Politeknik Negri Padang"}, {"AuthorId": 2, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Negeri Padang"}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": "Politeknik Negeri Padang"}], "References": []}, {"ArticleId": 87176014, "Title": "Single-image shadow removal using detail extraction and illumination estimation", "Abstract": "<p>Deep learning-based shadow removal methods are frequently hard to obtain a detail-rich and boundary-smoothing shadow removal result. In this work, we propose an illumination-sensitive filter and a multi-task generative adversarial networks architecture to tackle these problems. Firstly, we detect the shadow for the input shadow image and use the illumination-sensitive filter to extract the texture information for generating a coarse image with fewer texture details. Secondly, we conduct illumination estimation for this coarse shadow image to remove the shadow indirectly. Next, we restore the shadow boundary realistically inspired by the idea of image in painting. Finally, we recover the texture details for obtaining the final shadow removal result. Besides, we filter two large benchmark datasets, i.e., SRD and ISTD, to create a Low Error Synthesized Dataset (LESD). The extensive experiments demonstrate that our method can achieve superior performance to state of the arts. </p>", "Keywords": "Image processing; Shadow removal; Generative adversarial networks; Illumination estimation; Multi-scale decomposition", "DOI": "10.1007/s00371-021-02096-4", "PubYear": 2022, "Volume": "38", "Issue": "5", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "@qq.com;School of Information Engineering, Xinjiang Institute of Technology, Aksu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Xinjiang University, Urumqi, China"}, {"AuthorId": 3, "Name": "<PERSON> Wan", "Affiliation": "School of Electrical and Electronic Engineering, Wenzhou University, Wenzhou, China"}], "References": [{"Title": "A new TLD target tracking method based on improved correlation filter and adaptive scale", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "9", "Page": "1783", "JournalTitle": "The Visual Computer"}, {"Title": "Robust salient object detection for RGB images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "9", "Page": "1823", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 87176024, "Title": "What went wrong? Identification of everyday object manipulation anomalies", "Abstract": "<p>Enhancing the abilities of service robots is important for expanding what they can achieve in everyday manipulation tasks. In addition, it is also essential to ensure that they can determine what they cannot achieve. Such necessity may arise due to anomalies during task execution. These situations should be detected and identified to overcome and recover from them. Identification necessitates a deeper time series analysis of onboard sensor readings to keep track of and relate anomaly indicators since some indicators may be perceived long before the detection of an anomaly. These sensor readings are usually taken asynchronously and need to be fused effectively for correct interpretations. In this paper, we propose a multimodal long short-term memory-based (LSTM-based) anomaly identification approach that takes into account real-time observations by fusing visual, auditory and proprioceptive sensory modalities during everyday object manipulation tasks. The symptoms of anomalies are first trained and then are classified based on the learned models in real time. We first provide a comparative analysis of our method with hidden Markov models (HMMs), conditional random fields (CRFs) and gated recurring units (GRUs) on a Baxter robot executing everyday object manipulation scenarios. Then, we analyze the impact of each modality and various feature extraction techniques on the performance of the identification problem. We show that our method has the ability to identify anomalies by capturing long-term dependencies between the anomaly indicators. The results indicate that the LSTM-based anomaly identification method outperforms the closest baseline with a 2% improvement of f-score (0.92) in classifying anomalies that occur during run-time.</p>", "Keywords": "Anomaly classification; Diagnosis; Safety in robotics; Everyday object manipulation", "DOI": "10.1007/s11370-021-00355-w", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 2152, "JournalTitle": "Intelligent Service Robotics", "ISSN": "1861-2776", "EISSN": "1861-2784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer and Informatics Engineering, Artificial Intelligence and Robotics Laboratory, Istanbul Technical University, Istanbul, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer and Informatics Engineering, Artificial Intelligence and Robotics Laboratory, Istanbul Technical University, Istanbul, Turkey"}], "References": [{"Title": "CNN features with bi-directional LSTM for real-time anomaly detection in surveillance networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "11", "Page": "16979", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 87176043, "Title": "Improving Data Delivery in Large Environments and Mobile Areas", "Abstract": "Abstract \n Advances in communication technology have significantly altered our knowledge about telephone networks. The remarkable expansion in communication is obvious in numerous aspects of life of life; the transmission of information has never been easier. Wireless communication has also had a remarkable development, and these facilities have become more reliable and easily accessible.\n This study, through a descriptive approach in accordance with earlier studies, aims to explain how to increase the capacity of wireless data delivery in addition to other important challenges that face the enhancement of these services in remote areas. This study highlights the challenges that accompany the adoption of wireless networks in remote areas: particularly signal strength. This is highly linked to the specific features of rural areas in terms of living habits in separated nodes rather than in high-density population centers areas.", "Keywords": "", "DOI": "10.1515/comp-2020-0211", "PubYear": 2021, "Volume": "11", "Issue": "1", "JournalId": 5161, "JournalTitle": "Open Computer Science", "ISSN": "", "EISSN": "2299-1093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Computer Engineering Department , College of Engineering , University of Mosul , Mosul , 41000 , Iraq"}], "References": [{"Title": "Improving Specimen Labelling and Data Collection in Bio-science Research using Mobile and Web Applications", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Open Computer Science"}]}, {"ArticleId": 87176127, "Title": "Towards the Modelling of Veillance based Citizen Profiling using Knowledge Graphs", "Abstract": "Abstract \n In this work we have proposed a model for Citizen Profiling. It uses veillance (Surveillance and Sousveillance) for data acquisition. For representation of Citizen Profile Temporal Knowledge Graph has been used through which we can answer semantic queries. Previously, most of the work lacks representation of Citizen Profile and have used surveillance for data acquisition. Our contribution is towards enriching the data acquisition process by adding sousveillance mechanism and facilitating semantic queries through representation of Citizen Profiles using Temporal Knowledge Graphs. Our proposed solution is storage efficient as we have only stored data logs for Citizen Profiling instead of storing images, audio, and video for profiling purposes. Our proposed system can be extended to Smart City, Smart Traffic Management, Workplace profiling etc. Agent based mechanism can be used for data acquisition where each Citizen has its own agent. Another improvement can be to incorporate a decentralized version of database for maintaining Citizen profile.", "Keywords": "", "DOI": "10.1515/comp-2020-0209", "PubYear": 2021, "Volume": "11", "Issue": "1", "JournalId": 5161, "JournalTitle": "Open Computer Science", "ISSN": "", "EISSN": "2299-1093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science , Mohammad <PERSON> , Karachi , Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science , Mohammad <PERSON> , Karachi , Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science , Mohammad <PERSON> , Karachi , Pakistan"}], "References": []}, {"ArticleId": 87176330, "Title": "Dynamic multi-objective cooperative coevolutionary scheduling for mobile underwater wireless sensor networks", "Abstract": "Mobile underwater wireless sensor networks (MUWSNs) play an important role in monitoring the marine environment. Many existing research works have engaged on the deployment optimization. Typical examples include rebuilding the network by moving sensors or healing coverage holes using mobile sensors. However, they didn’t make full use of dynamic characteristics of mobile sensors. This paper focuses on the scheduling problem of MUWSNs that consist of a small number of mobile sensors and a large number of fixed ones. To solve the problem, we take mobile sensors as cluster heads to formulate the problem as a multi-objective dynamic scheduling model of fixed sensors as mobile ones move. Then, a dynamic multi-objective cooperative coevolutionary optimization algorithm is developed through decomposing decision variables, designing a strategy to respond environmental changes, and a collaborative strategy for generating an approximate Pareto-optimal set. The feasibility of the proposed optimization model and the effectiveness of its solving algorithm is empirically demonstrated.", "Keywords": "Mobile underwater wireless sensor network ; Scheduling ; Dynamic multi-objective evolutionary optimization ; Coverage ; Energy consumption", "DOI": "10.1016/j.cie.2021.107229", "PubYear": 2021, "Volume": "156", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Science, Jiangsu Ocean University, Lianyungang 222005, China"}, {"AuthorId": 2, "Name": "Jing Sun", "Affiliation": "School of Science, Jiangsu Ocean University, Lianyungang 222005, China;Corresponding author"}, {"AuthorId": 3, "Name": "Xingjia Gan", "Affiliation": "School of Computer Engineering, Jiangsu Ocean University, Lianyungang, 222005, China"}, {"AuthorId": 4, "Name": "Du<PERSON>wei Gong", "Affiliation": "School of Information and Control Engineering, China University of Mining and Technology, Xuzhou 221116, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Jiangsu Ocean University, Lianyungang 222005, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Jiangsu Ocean University, Lianyungang 222005, China"}], "References": [{"Title": "Autonomous deployment and adjustment of nodes in UWSN to achieve blanket coverage (ADAN-BC)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "1195", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Coverage area enhancement in wireless sensor network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "5", "Page": "1417", "JournalTitle": "Microsystem Technologies"}, {"Title": "FIS-RGSO: Dynamic Fuzzy Inference System Based Reverse Glowworm Swarm Optimization of energy and coverage in green mobile wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "163", "Issue": "", "Page": "12", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 87176331, "Title": "Path planning optimization of indoor mobile robot based on adaptive ant colony algorithm", "Abstract": "In view of the shortcomings of traditional ant colony algorithm (ACO) in path planning of indoor mobile robot, such as a long time path planning, non-optimal path for the slow convergence speed, and local optimal solution characteristic of ACO, an improvement adaptive ant colony algorithm (IAACO) is proposed in this paper. In IAACO, firstly, in order to accelerate the real-time and safety of robot path planning, angle guidance factor and obstacle exclusion factor are introduced into the transfer probability of ACO; secondly, heuristic information adaptive adjustment factor and adaptive pheromone volatilization factor are introduced into the pheromone update rule of ACO, to balance the convergence and global search ability of ACO; Finally, the multi-objective performance indexes are introduced to transform the path planning problem into a multi-objective optimization problem, so as to realize the comprehensive global optimization of robot path planning. The experimental results of main parameters selection, path planning performance in different environments, diversity of the optimal solution show that IAACO can make the robot attain global optimization path, and high real-time and stability performances of path planning.", "Keywords": "Ant colony algorithm ; Path planning ; Mobile robot ; Multi-objective optimal ; Energy consumption", "DOI": "10.1016/j.cie.2021.107230", "PubYear": 2021, "Volume": "156", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Nuclear Technology and Automation Engineering, Chengdu University of Technology, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Collegeof Information Science and Technology, Chengdu University of Technology, Chengdu, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Nuclear Technology and Automation Engineering, Chengdu University of Technology, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Wu", "Affiliation": "Collegeof Information Science and Technology, Chengdu University of Technology, Chengdu, China;Corresponding authors"}], "References": []}, {"ArticleId": 87176350, "Title": "Solving the online batching problem using deep reinforcement learning", "Abstract": "In e-commerce markets, on-time delivery is of great importance to customer satisfaction. In this paper, we present a Deep Reinforcement Learning (DRL) approach, together with a heuristic, for deciding how and when arrived orders should be batched and picked in a warehouse to minimize the number of tardy orders. In particular, the technique facilitates making decisions on whether an order should be picked individually (pick-by-order) or picked in a batch with other orders (pick-by-batch), and if so, with which other orders. We approach the problem by formulating it as a semi-Markov decision process and developing a vector-based state representation that includes the characteristics of the warehouse system. This allows us to create a deep reinforcement learning solution that learns a strategy by interacting with the environment and solve the problem with a proximal policy optimization algorithm. We evaluate the performance of the proposed DRL approach by comparing it with several batching and sequencing heuristics in different problem settings. The results show that the DRL approach can develop a strategy that produces consistent, good solutions and performs better than the proposed heuristics in most of the tested cases. We show that the strategy learned by DRL is different from the hand-crafted heuristics. In this paper, we demonstrate that the benefits from recent advancements of Deep Reinforcement Learning can be transferred to solve sequential decision-making problems in warehousing operations.", "Keywords": "Deep reinforcement learning ; Order batching ; Sequential decision making ; Machine learning ; Warehousing ; E-commerce", "DOI": "10.1016/j.cie.2021.107221", "PubYear": 2021, "Volume": "156", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Eindhoven University of Technology, School of Industrial Engineering, P.O. Box 513, 5600 MB Eindhoven, the Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Eindhoven University of Technology, School of Industrial Engineering, P.O. Box 513, 5600 MB Eindhoven, the Netherlands;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Eindhoven University of Technology, School of Industrial Engineering, P.O. Box 513, 5600 MB Eindhoven, the Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vanderlande Industries B.V., P.O. Box 18, 5460 AA Veghel, the Netherlands"}], "References": [{"Title": "Explainability in deep reinforcement learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106685", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 87176405, "Title": "Linking self-report and process data to performance as measured by different assessment types", "Abstract": "This study was motivated by a need to understand the extent to which behavioral indicators of engagement from digital log data are associated with various student learning outcomes above and beyond self-reported levels of engagement, and whether the strength of these associations vary depending on the type of learning outcome. Student learning was assessed by way of four distinct learning outcomes that varied according to stakes (low-v. high-stakes) and span (one-time v. aggregated). Participants included high school students between 14 and 18 years of age enrolled in an AP Statistics course ( N = 320, M age = 16.76 years, SD age = 0.85; 60.2% female) who had consented to use an online assessment system over the course of an academic year that was designed to provide personalized performance reports. While largely uncorrelated with self-report measures, certain process data variables were significantly correlated with learning outcomes. In particular, students’ frequency of score report checking, an indication of feedback-seeking behavior, while uncorrelated with self-reported student engagement, was associated with all learning outcomes. Other behaviors, such as the number of log-in sessions and the duration of sessions, were not. These findings suggest that process data from online assessment systems can help broaden and deepen our understanding of student behavior above and beyond self-report. That said, given that the volume and complexity of process data can make it challenging to mine and interpret, researchers must consider theory when identifying process data variables that are critical to the understanding of constructs of interest.", "Keywords": "Engagement ; Process data ; Clickstream data ; Assessment ; low- and high-stakes", "DOI": "10.1016/j.compedu.2021.104188", "PubYear": 2021, "Volume": "167", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Notre Dame, USA;Corresponding author. 390 Corbett Family Hall, Notre Dame, IN, 46556, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Notre Dame, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>-Ju", "Affiliation": "University of Notre Dame, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Notre Dame, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Notre Dame, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Notre Dame, USA"}], "References": [{"Title": "Detecting latent topics and trends in educational technologies over four decades using structural topic modeling: A retrospective of all volumes of Computers & Education", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "103855", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 87176407, "Title": "Classification Technology and Application of Multifunctional Enzymes", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2021.113048", "PubYear": 2021, "Volume": "11", "Issue": "3", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "鹏丽 毕", "Affiliation": ""}], "References": []}, {"ArticleId": 87176485, "Title": "Application and Security Research of New Media Technology in E-Government System", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2021.113049", "PubYear": 2021, "Volume": "11", "Issue": "3", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "明良 刘", "Affiliation": ""}], "References": []}, {"ArticleId": 87176581, "Title": "A novel multi-objective group teaching optimization algorithm and its application to engineering design", "Abstract": "This paper proposes a novel multi-objective algorithm by extending the recent group teaching optimization algorithm (GTOA) for the solution of multi-objective optimization problems. The proposed algorithm (designated as MOGTOA) is based on the Pareto dominance theory, and uses an external archive to guide the search direction of population. In order to strike a balance between exploration and exploitation of MOGTOA, a variety of improvements were made, including an improved teacher selection strategy, a modified student phase, and a hybrid mechanism of evolutionary search on the external archive population. The performance of the proposed algorithm was verified on 21 benchmark functions in comparison with six well established algorithms. The numerical results show that MOGTOA is comparable or even superior to other algorithms. In addition, the application of MOGTOA to two classic engineering design problems demonstrated its promising potential to solve practical problems.", "Keywords": "Group teaching optimization algorithm ; Multi-objective optimization ; Pareto solution ; Engineering design problem", "DOI": "10.1016/j.cie.2021.107198", "PubYear": 2021, "Volume": "155", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Huazhong Agricultural University, Wuhan, China"}, {"AuthorId": 2, "Name": "Qing Wu", "Affiliation": "College of Engineering, Huazhong Agricultural University, Wuhan, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Huazhong Agricultural University, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Engineering, Huazhong Agricultural University, Wuhan, China"}], "References": [{"Title": "Salp swarm algorithm: a comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "11195", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An efficient evolutionary grey wolf optimizer for multi-objective flexible job shop scheduling problem with hierarchical job precedence constraints", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106280", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Group teaching optimization algorithm: A novel metaheuristic method for solving global optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "113246", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-verse optimizer algorithm: a comprehensive survey of its results, variants, and applications", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12381", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Multi-verse optimizer algorithm: a comprehensive survey of its results, variants, and applications", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12381", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A comprehensive survey of the Grasshopper optimization algorithm: results, variants, and applications", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "19", "Page": "15533", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 87176582, "Title": "Improving service quality in a congested network with random breakdowns", "Abstract": "This paper employs some strategies for improving service quality in a congested network consisting of facilities and customers. The quality is measured considering both customers’ travel distances and congestion delays. The strategies include opening new facilities, increasing the service capacities, and incorporating multiple backup services for customers, empowering the network to distribute the facilities’ workload smoothly. The goal is to optimally configure the congested network while improving the service quality. Each facility is modeled as an M/M/1 queuing system and might be broken down frequently during its serving process. The recovery starts immediately, which is a multi-step process, and each step takes an exponentially-distributed random time. The problem is first modeled as a mixed-integer nonlinear program; then, reformulated as a mixed-integer second-order cone program and can be optimally solved. An efficient solution algorithm based on Lagrangian decomposition is also developed. The numerical experiments illustrate the high efficiency of the solution methodology. Several managerial implications, including determining the best backup service level, are provided. Last, an example in distributed computing system design is presented to demonstrate the applicability of the model.", "Keywords": "Service system design ; Congested network design ; Queue with breakdowns ; Lagrangian decomposition ; Mixed-integer second-order cone program ; Distributed computing system design", "DOI": "10.1016/j.cie.2021.107226", "PubYear": 2021, "Volume": "157", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering & Management Systems, Amirkabir University of Technology, Tehran, Iran"}], "References": []}, {"ArticleId": 87176594, "Title": "Network scheduling problem with cross-docking and loading constraints", "Abstract": "Cross-docking is a logistic strategy which can increase rates of consolidation, and reduce distribution and storage costs and delivery times. The optimization literature on cross-docking has mostly focused on the modeling and solution of problems considering a single cross-docking facility. Networks with multiple cross-docks remain rather unexplored and the few papers that deal with the problem do this by simplifying the geometry of the goods. We intend to shorten this gap by proposing a mixed-integer linear programming model for optimizing distribution and delay costs for the transportation of goods in open networks with multiple cross-docks considering the three-dimensional aspects of the cargo. Also, we propose a logic-based Benders decomposition strategy which allow for the solution of larger instances when compared with those that can be handled by a branch-and-cut MIP solver. Experiments showed that the decomposition can handle instances with two times more nodes and five times more boxes than a direct use of the solver. Also, the framework is flexible enough to accommodate other features of practical cases.", "Keywords": "Vehicle routing ; Three-dimensional packing ; Benders decomposition ; City logistics", "DOI": "10.1016/j.cor.2021.105271", "PubYear": 2021, "Volume": "132", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Informática e Estatística, Universidade Federal de Santa Catarina, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, The University of Melbourne, Australia"}, {"AuthorId": 3, "Name": "Franklina Toledo", "Affiliation": "Instituto de Ciências Matemáticas e de Computação, Universidade de São Paulo, Brazil"}], "References": [{"Title": "Logic-based benders decomposition for scheduling a batching machine", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "113", "Issue": "", "Page": "104777", "JournalTitle": "Computers & Operations Research"}, {"Title": "A hybrid algorithm for the vehicle routing problem with three-dimensional loading constraints and mixed backhauls", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "1", "Page": "71", "JournalTitle": "Journal of Scheduling"}, {"Title": "Truck scheduling in a multi-door cross-docking center with partial unloading – Reinforcement learning-based simulated annealing approaches", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106134", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Parallel-machine scheduling methodology for a multi-dock truck sequencing problem in a cross-docking center", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "106391", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Logic-based Benders decomposition for the heterogeneous fixed fleet vehicle routing problem with time windows", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "106641", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": ********, "Title": "Performance improvement of extreme multi-label classification using K-way tree construction with parallel clustering algorithm", "Abstract": "eXtreme Multi-Label Classification (XMLC) is the particular case of Multi-Label Classification, which deals with an extremely high number of labels. The main goal is to learn extreme classifier which extracts the subset of relevant labels from extremely large label space. In an extreme environment, one of the big issues is to deal with an extreme number of features, labels and instances which affects the performance of the classifier. The high dimensional feature space and label space makes existing approaches intractable in terms of data scalability, data sparsity, training and prediction cost. The appropriate input representation technique can be used to maintain the interdependency among labels and correlation between feature space and label space. The proposed approach called “K-way Tree based eXtreme Multi-Label Classifier (KTXMLC)“ works on tree based classifier to maintain correlations using feature-label input representation technique and node partitioning using a clustering algorithm. KTXMLC constructs multi-way multiple trees using a parallel clustering algorithm, which leads to fast computational cost. KTXMLC outperforms over the existing tree based classifier in terms of ranking based measures on six datasets named Delicious, Mediamill, Eurlex-4K, Wiki10-31K, AmazonCat-13K, Delicious-200K.", "Keywords": "Machine learning ; Multi-label classification ; Extreme classification ; Recommendation system", "DOI": "10.1016/j.jksuci.2021.02.014", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Smt. <PERSON><PERSON><PERSON><PERSON> Dinsha Patel Department of Information Technology, Chandubhai S Patel Institute of Technology (CSPIT), Charotar University of Science and Technology (CHARUSAT), CHARUSAT Campus, Changa 388421, Gujarat, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Chan<PERSON><PERSON>i S Patel Institute of Technology (CSPIT), Charotar University of Science and Technology (CHARUSAT), CHARUSAT Campus, Changa 388421, Gujarat, India"}], "References": [{"Title": "MLACO: A multi-label feature selection algorithm based on ant colony optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "105285", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Label Clustering for a Novel Problem Transformation in Multi-label Classification", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "1", "Page": "71", "JournalTitle": "JUCS - Journal of Universal Computer Science"}]}, {"ArticleId": 87176718, "Title": "Highly Constrained University Class Scheduling using Ant Colony Optimization", "Abstract": "Solving University Class Scheduling Problem (UCSP) is a complex real-world combinatorial optimization task that has been extensively studied over the last several decades. Many meta-heuristic based techniques, including prominent swarm intelligence (SI) methods have been investigated to solve it in different ways. In this study, Ant Colony Optimization (ACO) based two methods are investigated to solve UCSP: ACO based method and ACO with Selective Probability (ACOSP). ACO is the well-known SI method that differs from other SI based methods in the way of interaction among individuals (i.e., ants); and an ant interacts with others indirectly through pheromone to solve a given problem. ACO based method considers probabilistically all the unassigned time slots to select next solution point for a particular course assignment. In contrast, ACOSP probabilistically selects next solution point for a particular course assignment from the selective probabilities. Such selective probability employment with ACO improves performance but reduces computational cost. The performances of the proposed methods have been evaluated comparing with Genetic Algorithm (GA) in solving real-world simple UCSPs. In addition, proposed methods are compared with each other for solving highly constrained UCSPs. Both the proposed methods outperformed GA and ACOSP was the best to solve the given problems.", "Keywords": "", "DOI": "10.5121/ijcsit.2021.13102", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 20755, "JournalTitle": "International Journal of Computer Science and Information Technology", "ISSN": "0975-4660", "EISSN": "0975-3826", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87176719, "Title": "Real Time Vigilance Detection using Frontal EEG", "Abstract": "Vigilance of an operator is compromised in performing many monotonous activities like workshop and manufacturing floor tasks, driving, night shift workers, flying, and in general any activity which requires keen attention of an individual over prolonged periods of time. Driver or operator fatigue in these situations leads to drowsiness and lowered vigilance which is one of the largest contributors to injuries and fatalities amongst road accidents or workshop floor accidents. Having a vigilance monitoring system to detect drop in vigilance in these situations becomes very important. This paper presents a system which uses non-invasively recorded Frontal EEG from an easy-to-use commercially available Brain Computer Interface wearable device to determine the vigilance state of an individual. The change in the power spectrum in the Frontal Theta Band (4-8Hz) of an individual’s brain wave predicts the changes in the attention level of an individual - providing an early detection and warning system. This method provides an accurate, yet cheap and practical system for vigilance monitoring across different environments.", "Keywords": "Brain Computer Interface", "DOI": "10.5121/ijcsit.2021.13104", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 20755, "JournalTitle": "International Journal of Computer Science and Information Technology", "ISSN": "0975-4660", "EISSN": "0975-3826", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87176902, "Title": "Evaluation of Soil Quality Status under Different Forest Tree Species in Nigeria", "Abstract": "<p>A study was carried out at Forestry Research Institute of Nigeria, Ibadan, South -western, Nigeria in the second quarters of 2018 to evaluate the current status and the distribution of some soil quality factors of study area. The study also aimed at suggesting strategies for efficient management of the soil quality factors in the study areas. Soil samples were collected under six (6) different forest trees species plantations. The samples were subjected to routine laboratory analytical protocol for the determination of both physical and chemical properties of the samples. Results implicated that the soil organic matter (SOM) of the study area varied from one forest plantation to the other. However, SOM was found to be highest under natural forest (NF) with a mean value of 3.90% while open field had the least with a mean value of 1.62%. Similar results were observed for soil total nitrogen (N). There was a significant (p<0.05)difference in the amount of exchangeable K contents (0.008cmol/kg) in soils under open field with less ground cover and forest tree species relative to soils under NF, <PERSON><PERSON><PERSON> (ND) and Mangifera indica (MI) respectively. Soils under NF significantly recorded the highest exchangeable Ca compared to open field and others. It can therefore be concluded that good agricultural management practises that will enhance soil quality factors and multi forest species cultivation be encouraged for effective and efficient nutrient management systems in the forest ecosystems for enhancement of bio diversities of both micro and macro organisms. However, due to population pressure on the available land for other non-agricultural purposes coupled with annual bush burning, inadequate rainfall, lack of good agricultural practices and non-availability of natural forest for farming, it has become imperative that agroforestry system of farming in which trees or shrubs are grown in association with crops is advocated.\r  </p>", "Keywords": "", "DOI": "10.14738/tmlai.91.9824", "PubYear": 2021, "Volume": "9", "Issue": "1", "JournalId": 29184, "JournalTitle": "Transactions on Machine Learning and Artificial Intelligence", "ISSN": "", "EISSN": "2054-7390", "Authors": [{"AuthorId": 1, "Name": "Olorunfemi Sunday Ojo AKANBI", "Affiliation": "Cocoa Research Institute of Nigeria, Ibadan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87176951, "Title": "A bi-objective robust optimization model for disaster response planning under uncertainties", "Abstract": "There are various uncertainties post-disaster relief logistics, it is essential to optimize emergency logistics to provide timely and effective medical service in such emergency situations. This paper proposes a bi-objective robust optimization model for strategic and operational response to decide the facility location, emergency resource allocation, and casualty transportation plans in a three-level rescue chain composed of casualty clusters, temporary facilities, and general hospitals. The Injury Severity Score (ISS) is adopted to divide the casualties into two categories and give the dynamic injury deterioration of casualties over time. Also, the model considers various uncertainties in demand including the number of casualties and the number of rescue supplies and transportation time. The objectives are to minimize the sum of ISS for all casualties and minimize the total costs of the system. Meanwhile, the penalty coefficients for untransported casualties are applied to maximize the transport number of casualties, and the penalty costs for unmet relief supplies are used to maximize the satisfaction of supplies demand at temporary facilities. We employ the robust optimization method to derive the robust corresponding model of the proposed stochastic model. The bi-objective model is solved with the ε-constraint method. Additionally, case studies based on Yushu Earthquake are utilized to demonstrate the feasibility and validity of the proposed model. Sensitivity analyses discuss the impact of uncertainties on the proposed model results by changing the settings of the uncertain parameters in the robust optimization, to make a tradeoff between optimization and robustness.", "Keywords": "Uncertainty modeling ; Robust optimization ; ε-constraint ; Relief logistics ; Location-allocation", "DOI": "10.1016/j.cie.2021.107213", "PubYear": 2021, "Volume": "155", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Shanghai University, Shanghai 200444, China;Corresponding author at: No. 99, Shangda Road, Baoshan District, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Management, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Xu<PERSON>", "Affiliation": "Department of Education Information Technology, East China Normal University, Shanghai 200062, China"}], "References": [{"Title": "A robust neutrosophic fuzzy-based approach to integrate reliable facility location and routing decisions for disaster relief under fairness and aftershocks concerns", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "106734", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A robust bi-objective mathematical model for disaster rescue units allocation and scheduling with learning effect", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106790", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": ********, "Title": "Futuristic Technology in Architecture & Planning - Augmented and Virtual Reality: an Overview", "Abstract": "Speed has become a way of life. We are asymptotically piling data. Speed can be achieved with new design processes, techniques, and Technology. Innovations AR and VR are just some of the many forms of technologies that will play a key role in shaping the Architecture and Planning of tomorrow, making it future-ready and ushering in a new age of innovation. AR and VR in Architecture & Planning were introduced as assisting tools and has helped generate multiple design options, expanded possibilities of visualization, and provided us with more enhanced, detailed, and specific experience in real-time; enabling us to see the resultsof work on hand well before the commencement of the project. These tools are further developed for city development decisions, helping citizens interact with local authorities, access public services, and plan their commute. After reviewing multiple research papers, it had been observed that each one is moving forward with the changes brought by it, without entirely understanding its role. This paper provides a summary of theappliance of AR & VR in architecture and planning.", "Keywords": "", "DOI": "10.5121/ijscai.2021.10101", "PubYear": 2021, "Volume": "10", "Issue": "1", "JournalId": 37246, "JournalTitle": "International Journal on Soft Computing, Artificial Intelligence and Applications", "ISSN": "2319-4081", "EISSN": "2319-1015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Yadav", "Affiliation": ""}], "References": []}, {"ArticleId": 87177006, "Title": "Approximate moving horizon estimation and robust nonlinear model predictive control via deep learning", "Abstract": "Optimization-based methods for output-feedback control enable dealing with multiple-input and multiple-output nonlinear systems in the presence of uncertainties and constraints. The combination of moving horizon estimation (MHE) and nonlinear model predictive control (NMPC) can be especially powerful because of its general formulation but its implementation requires solving two optimization problems at every sampling instant, which can be challenging due to hardware or time constraints. We propose to take advantage of the expressive capabilities of deep neural networks to approximate the solution of the MHE and NMPC problems. By substituting the MHE and NMPC with their learning-based counterparts, the required online computations are significantly reduced. We also propose to use sensitivity analysis to compute an approximate upper-bound of the maximum one-step divergence from the optimal performance caused by the approximation error. The efficacy of the proposed learning-based approach is illustrated with simulation results of a semi-batch reactor for industrial polymerization.", "Keywords": "Robust control ; Nonlinear model predictive control ; Learning-based control ; Moving horizon estimation", "DOI": "10.1016/j.compchemeng.2021.107266", "PubYear": 2021, "Volume": "148", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Process Automation Systems, TU Dortmund University, Emil-Figge-Str. 70, 44227 Dortmund, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Process Automation Systems, TU Dortmund University, Emil-Figge-Str. 70, 44227 Dortmund, Germany;Corresponding author"}], "References": [{"Title": "Stability properties of multi-stage nonlinear model predictive control", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "104743", "JournalTitle": "Systems & Control Letters"}]}, {"ArticleId": 87177233, "Title": "Blockchain based solutions to secure IoT: Background, integration trends and a way forward", "Abstract": "The lack of intrinsic security technologies in the current Internet of Things (IoT) systems brings forth numerous security vulnerabilities and privacy risks. To this end, a distributed and decentralized technology named blockchain comes out as a viable solution. This paper investigates the integration trends of blockchain technology with IoT and discusses the insights of this new paradigm. In particular, this paper presents a comprehensive survey on security improvements achieved in IoT systems using blockchain and the challenges that originate during this integration. Further, the paper highlights the most relevant blockchain based IoT applications and outlines some future research directions.", "Keywords": "Internet of things ; Blockchain ; Security ; Privacy ; Smart contract", "DOI": "10.1016/j.jnca.2021.103050", "PubYear": 2021, "Volume": "181", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, HMR Institute of Technology and Management, New Delhi, 110036, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, School of Engineering and Technology, Sharda University, Greater Noida, Uttar Pradesh, 201310, India;Corresponding author. 282, Metro Apartments, Jahangir Puri, New Delhi, 110033, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, School of Engineering Sciences and Technology, Jamia <PERSON>d, New Delhi, 110062, India;Corresponding author"}], "References": [{"Title": "A survey on the security of blockchain systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "841", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Runtime verification for business processes utilizing the Bitcoin blockchain", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "816", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "From blockchain consensus back to Byzantine consensus", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "760", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Blockchain for the IoT and industrial IoT: A review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "", "Page": "100081", "JournalTitle": "Internet of Things"}, {"Title": "Blockchain-based approach to create a model of trust in open and ubiquitous higher education", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "109", "JournalTitle": "Journal of Computing in Higher Education"}, {"Title": "Cloud-based lightweight secure RFID mutual authentication protocol in IoT", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "527", "Issue": "", "Page": "329", "JournalTitle": "Information Sciences"}, {"Title": "SBAC: A secure blockchain-based access control framework for information-centric networking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102444", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "An efficient Lightweight integrated Blockchain (ELIB) model for IoT security and privacy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "1027", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Blockchain data-based cloud data integrity protection mechanism", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "902", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Blockchain smart contracts formalization: Approaches and challenges to address vulnerabilities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "101654", "JournalTitle": "Computers & Security"}, {"Title": "PrivySharing: A blockchain-based framework for privacy-preserving and secure data sharing in smart cities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "101653", "JournalTitle": "Computers & Security"}, {"Title": "A Comprehensive Survey on Attacks, Security Issues and Blockchain Solutions for IoT and IIoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102481", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "System architecture for blockchain based transparency of supply chain social sustainability", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "101896", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Deep learning and big data technologies for IoT security", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "495", "JournalTitle": "Computer Communications"}, {"Title": "Designing secure blockchain-based access control scheme in IoT-enabled Internet of Drones deployment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "229", "JournalTitle": "Computer Communications"}, {"Title": "RETRACTED: Applications of internet of things (IOT) to improve the stability of a grid connected power system using interline power flow controller", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "", "Page": "103038", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "A survey of blockchain consensus algorithms performance evaluation criteria", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "113385", "JournalTitle": "Expert Systems with Applications"}, {"Title": "GuardHealth: Blockchain empowered secure data management and Graph Convolutional Network enabled anomaly detection in smart healthcare", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "1", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Survey on IoT security: Challenges and solution using machine learning, artificial intelligence and blockchain technology", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100227", "JournalTitle": "Internet of Things"}, {"Title": "Untangling blockchain technology: A survey on state of the art, security threats, privacy services, applications and future research directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "90", "Issue": "", "Page": "106897", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 87177261, "Title": "It is like taking a ball for a walk: on boundary work in software development", "Abstract": "Abstract In this paper, we explore how the choices of boundary work in software development influence the team autonomy enacted by team members. Boundary work is when people protect their professional individual autonomy, when they downplay that autonomy to collaborate over professional boundaries, and when they create new boundaries. Team autonomy is here defined as a team using their autonomy to collaborate in deciding their own output. We use an action research design, with varied methodologies carried out through three action cycles. Our findings show that when collective, collaborative boundary work is not performed, a sort of individualized zone occurs where individuals either try to do collaborative boundary work by themselves or seek individual autonomy. We propose that individual autonomy can be divided into professional individual autonomy and situationally dependent individual autonomy. This research contributes theoretically by showing how the absence of collaborative boundary work can lead to an individualized zone. Practically, it can improve team autonomy by enhancing the understanding of why teams should perform collaborative boundary work. The value of the concept of boundary work used in this setting involves studying the intentions for collaboration, not whether collaboration actually takes place.", "Keywords": "Autonomy; Boundary work; Complexity; Software development; Action research", "DOI": "10.1007/s00146-021-01175-3", "PubYear": 2022, "Volume": "37", "Issue": "2", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Economics and Technology Management, Faculty of Economics and Management, Norwegian University of Science and Technology (NTNU), Trondheim, Norway"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Economics and Technology Management, Faculty of Economics and Management, Norwegian University of Science and Technology (NTNU), Trondheim, Norway"}], "References": []}, {"ArticleId": 87177341, "Title": "3D Metallic Plate Lens Antenna based Beamspace Channel Estimation Technique for 5G Mmwave Massive MIMO Systems", "Abstract": "Beamspace channel estimation mechanism for massive MIMO (multiple input multiple output) antenna system presents a major process to compensate the 5G spectrum challenges caused by the proliferation of information from mobile devices. However, this estimation is required to ensure the perfect channel state information (CSI) for lower amount of Radio Frequency (RF) chains for each beam. In addition, phase shifter (PS) components used in this estimation need high power to select the beam in the desired direction. To overcome these limitations, in this work, we propose Regular Scanning Support Detection (RSSD) based channel estimation mechanism. Moreover, we utilise a 3D lens antenna array having metallic plate and a switch in our model which compensates the limitation of phase shifters. Simulation results show that the proposed RSSD based channel estimation surpasses traditional technique and SD based channel estimation even in lower SNR area which is highly desirable in the millimeter wave (mmWave) massive MIMO systems.", "Keywords": "", "DOI": "10.5121/ijwmn.2021.13101", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 11718, "JournalTitle": "International Journal of Wireless & Mobile Networks", "ISSN": "0975-4679", "EISSN": "0975-3834", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87177426, "Title": "BCD-WERT: a novel approach for breast cancer detection using whale optimization based efficient features and extremely randomized tree algorithm", "Abstract": "<p>Breast cancer is one of the leading causes of death in the current age. It often results in subpar living conditions for a patient as they have to go through expensive and painful treatments to fight this cancer. One in eight women all over the world is affected by this disease. Almost half a million women annually do not survive this fight and die from this disease. Machine learning algorithms have proven to outperform all existing solutions for the prediction of breast cancer using models built on the previously available data. In this paper, a novel approach named BCD-WERT is proposed that utilizes the Extremely Randomized Tree and Whale Optimization Algorithm (WOA) for efficient feature selection and classification. WOA reduces the dimensionality of the dataset and extracts the relevant features for accurate classification. Experimental results on state-of-the-art comprehensive dataset demonstrated improved performance in comparison with eight other machine learning algorithms: Support Vector Machine (SVM), Random Forest, Kernel Support Vector Machine, Decision Tree, Logistic Regression, Stochastic Gradient Descent, Gaussian Naive Bayes and k-Nearest Neighbor. BCD-WERT outperformed all with the highest accuracy rate of 99.30% followed by SVM achieving 98.60% accuracy. Experimental results also reveal the effectiveness of feature selection techniques in improving prediction accuracy.</p>", "Keywords": "Breast cancer;Machine learning;Support vector machine;Whale optimization algorithm", "DOI": "10.7717/peerj-cs.390", "PubYear": 2021, "Volume": "7", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Air University, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Cyber Security, Air University, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Cyber Security, Air University, Islamabad, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Air University, Islamabad, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, College of Computer Science and Engineering, Taibah University, Madinah, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, Umm Al-Qura University, Makkah, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON> Gadekallu", "Affiliation": "School of Information Technology and Engineering, Vellore Institute of Technology University, Tamil Nadu, India"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Air University, Islamabad, Pakistan"}], "References": [{"Title": "A snapshot neural ensemble method for cancer-type prediction based on copy number variations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "19", "Page": "15281", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Internet of health things-driven deep learning system for detection and classification of cervical cells using transfer learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "11", "Page": "8590", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A metaheuristic optimization approach for energy efficiency in the IoT networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "12", "Page": "2558", "JournalTitle": "Software: Practice and Experience"}, {"Title": "A novel PCA–whale optimization-based deep neural network model for classification of tomato plant diseases using GPU", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "4", "Page": "1383", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "PARCIV: Recognizing physical activities having complex interclass variations using semantic data of smartphone", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "532", "JournalTitle": "Software: Practice and Experience"}, {"Title": "A collaborative healthcare framework for shared healthcare plan with ambient intelligence", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Human-centric Computing and Information Sciences"}, {"Title": "PP-SPA: Privacy Preserved Smartphone-Based Personal Assistant to Improve Routine Life Functioning of Cognitive Impaired Individuals", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "1", "Page": "35", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 87177677, "Title": "Robust 3D reconstruction from uncalibrated small motion clips", "Abstract": "<p>Small motion can be induced from burst video clips captured by a handheld camera when the shutter button is pressed. Although uncalibrated burst video clip conveys valuable parallax information, it generally has small baseline between frames, making it difficult to reconstruct 3D scenes. Existing methods usually employ a simplified camera parameterization process with keypoint-based structure from small motion (SFSM), followed by a tailored dense reconstruction. However, such SFSM methods are sensitive to insufficient or unreliable keypoint features, and the subsequent dense reconstruction may fail to recover the detailed surface. In this paper, we propose a robust 3D reconstruction pipeline by leveraging both keypoint and line segment features from video clips to alleviate the uncertainty induced by small baseline. A joint feature-based structure from small motion method is first presented to improve the robustness of the self-calibration with line segment constraints, and then, a noise-aware PatchMatch stereo module is proposed to improve the accuracy of the dense reconstruction. Finally, a confidence weighted fusion process is utilized to further suppress depth noise and mitigate erroneous depth. The proposed method can reduce the failure cases of self-calibration when the keypoints are insufficient, while recovering the detailed 3D surfaces. In comparison with state of the arts, our method achieves more robust and accurate 3D reconstruction results for a variety of challenging scenes.</p>", "Keywords": "3D reconstruction; Small motion; Multi-view stereo; Line segment; PatchMatch stereo", "DOI": "10.1007/s00371-021-02090-w", "PubYear": 2022, "Volume": "38", "Issue": "5", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing, The Hong Kong Polytechnic University, Kowloon, Hong Kong"}], "References": []}, {"ArticleId": 87177689, "Title": "Software engineering and formal methods: SEFM 2019 special section", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10270-021-00874-1", "PubYear": 2021, "Volume": "20", "Issue": "2", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Informatics, University of Oslo, Oslo, Norway"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University Grenoble Alpes, Grenoble, France"}], "References": []}, {"ArticleId": 87177729, "Title": "One dimensional Au-ZnO hybrid nanostructures based CO2 detection: Growth mechanism and role of the seed layer on sensing performance", "Abstract": "In the present research, hybrid Au-ZnO one-dimensional (1-D) nanostructures were grown on silicon substrates with an Al-doped ZnO (AZO) seed layer (Ultrasonic Spray Pyrolysis: USP grown) and no seed layer (NSL) using two different catalytic gold films of 2 nm and 4 nm, respectively. Consequently, such 1-D nanostructures growth was associated with the vapor-liquid-solid (VLS) and vapor-solid (VS) processes. Scanning electron microscopy (SEM) imaging analysis confirms that heat treatment triggered Au nanoparticles nucleation with varying diameters. The Au nanoparticles size and underneath seed layer texture strongly affect the morphology and aspect ratio of 1-D ZnO nanostructures. The seed layer (1-D USP) sample resulted in the growth of longer nanowires (NWs) with a high aspect ratio. The NSL sample showed the formation of nanorods (NRs) with a low aspect ratio mainly via VS growth process. X-ray diffraction (XRD), X-Ray photoelectron spectroscopy (XPS), and photoluminescence (PL) analysis also revealed the differences in the NWs and NRs properties and confirmed VLS and VS growth mechanisms. CO<sub>2</sub> gas sensing performance at different concentrations was demonstrated, and NWs with seed layer showed a relatively higher sensing response. In contrast, NSL samples (NRs) exhibited two times faster response. A detailed gas sensing mechanism with different CO<sub>2</sub> adsorption modes based on properties of 1-D nanostructures has been discussed. Currently, CO<sub>2</sub> sensing and capturing are critical topics in the green transition framework. The present work would be of high significance to the scientific field of NW growth and fulfill the urgent need for CO<sub>2</sub> gas sensing.", "Keywords": "ZnO/Au nanowires ; Metal oxide semiconductor ; Seed layers ; VLS/VS growth mechanism ; Ultrasonic spray pyrolysis ; CO<sub>2</sub> gas sensors ; Environmental monitoring ; Sustainable systems", "DOI": "10.1016/j.snb.2021.129765", "PubYear": 2021, "Volume": "337", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> González-Garnica", "Affiliation": "Universidad Tecnológica de Tulancingo, Tulancingo, 43642, Hidalgo, Mexico;Instituto de Investigaciones en Materiales, UNAM, 04510, CDMX, Mexico"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instituto de Investigaciones en Materiales, UNAM, 04510, CDMX, Mexico"}, {"AuthorId": 3, "Name": "Francisco <PERSON>", "Affiliation": "Instituto de Investigaciones en Materiales, UNAM, 04510, CDMX, Mexico"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Instituto de Investigaciones en Materiales, UNAM, 04510, CDMX, Mexico"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Investigaciones en Materiales, UNAM, 04510, CDMX, Mexico"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Mads Clausen Institute, NanoSYD, University of Southern Denmark, Alsion 2, 6400, Sønderborg, Denmark"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Condensed Matter Theory Group, Materials Theory Division, Department of Physics and Astronomy, Uppsala University, Box 516, 75120, Uppsala, Sweden"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Natural Sciences, Division of Sciences, Art & Mathematics, Florida Polytechnic University, 4700 Research Way, Lakeland, FL, 33805, United States"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mads Clausen Institute, NanoSYD, University of Southern Denmark, Alsion 2, 6400, Sønderborg, Denmark"}, {"AuthorId": 10, "Name": "Tangirala V.<PERSON>", "Affiliation": "Tepeji Graduate School, Industrial Engineering, Autonomous University of Hidalgo State, Av. del Maestro n.41, Col. No<PERSON> 2ª Sección, Tepeji del Río, Hidalgo, 42855, Mexico;Corresponding authors"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Investigaciones en Materiales, UNAM, 04510, CDMX, Mexico;Corresponding authors"}], "References": []}, {"ArticleId": 87177739, "Title": "Visual screening of PGP-1 inhibitors and identification of intestinal microbiota with active PGP-1 using a NIR fluorescent probe", "Abstract": "Pyroglutamyl aminopeptidase I (PGP-1) from intestinal microbiota is known as a hydrolase, specifically for degradation of l -pyroglutamate (L-pGlu) residue from the amino terminus of l -pGlu proteins, peptides, and synthetic analogues. In the present work, an enzyme-activated NIR fluorescent probe ( ADMP ) with a remarkable Stokes shift has been developed for PGP-1 assay. Using a high-throughput screening method based on detecting ability of ADMP , several potent inhibitors such as menadione, alantolactone, constunolide and carnosic acid were obtained toward PGP-1 from natural compounds library. Additionally, under the guidance of fluorescence sensing of endogenous PGP-1 using ADMP as a NIR sensor, a intestianl bacterium ( Klebsiella pneumoniae strain 1557) and a saccharomycete ( Trichosporon asteroids ) repsectively, were efficiently identified from human feces, which were expected to play an important role for regulating l -pGlu-constituents metabolism in gut.", "Keywords": "Pyroglutamyl aminopeptidase I ; Fluorescent probe ; High-Throughput screen of inhibitor ; Intestinal microbiota", "DOI": "10.1016/j.snb.2021.129764", "PubYear": 2021, "Volume": "337", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Pharmacy, Engineering Laboratory of Development and Application of Traditional Chinese Medicines, Hangzhou Normal University, Hangzhou 311121, China;State Key Laboratory of Fine Chemicals, Dalian University of Technology, Dalian 116024, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Pharmacy, Engineering Laboratory of Development and Application of Traditional Chinese Medicines, Hangzhou Normal University, Hangzhou 311121, China;Dalian Key Laboratory of Metabolic Target Characterization and Traditional Chinese Medicine Intervention, Dalian Medical University, Dalian 116044, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Pharmacy, Engineering Laboratory of Development and Application of Traditional Chinese Medicines, Hangzhou Normal University, Hangzhou 311121, China;State Key Laboratory of Fine Chemicals, Dalian University of Technology, Dalian 116024, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Fine Chemicals, Dalian University of Technology, Dalian 116024, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dalian Key Laboratory of Metabolic Target Characterization and Traditional Chinese Medicine Intervention, Dalian Medical University, Dalian 116044, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dalian Key Laboratory of Metabolic Target Characterization and Traditional Chinese Medicine Intervention, Dalian Medical University, Dalian 116044, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dalian Key Laboratory of Metabolic Target Characterization and Traditional Chinese Medicine Intervention, Dalian Medical University, Dalian 116044, China"}, {"AuthorId": 8, "Name": "Sa Deng", "Affiliation": "Dalian Key Laboratory of Metabolic Target Characterization and Traditional Chinese Medicine Intervention, Dalian Medical University, Dalian 116044, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Dalian Key Laboratory of Metabolic Target Characterization and Traditional Chinese Medicine Intervention, Dalian Medical University, Dalian 116044, China;Corresponding authors at: Hangzhou Normal University, Hangzhou 311121, China and Dalian Medical University, Dalian 116044, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "College of Pharmacy, Engineering Laboratory of Development and Application of Traditional Chinese Medicines, Hangzhou Normal University, Hangzhou 311121, China;Dalian Key Laboratory of Metabolic Target Characterization and Traditional Chinese Medicine Intervention, Dalian Medical University, Dalian 116044, China;Corresponding authors at: Hangzhou Normal University, Hangzhou 311121, China and Dalian Medical University, Dalian 116044, China"}], "References": []}, {"ArticleId": 87177835, "Title": "Paving the way to collaborative context-aware mobile applications: a case study on preventing worsening of allergy symptoms", "Abstract": "<p>In recent years, the evolution of smartphones and their software applications has grown exponentially; together with the advance of the Internet of Things and smart cities, it has raised huge demand for services and applications in these domains. Although the wide range of mobile applications is unquestionable, citizens already demand that applications adapt to their specific needs and situations in real time, that is, that they are context-aware. However, context-aware mobile applications are often very limited and miss out on the opportunity of benefiting from feedback provided by citizen collaboration. In order to fill this gap, this paper proposes a context-aware and collaborative software architecture and mobile application. In particular, we have implemented them in the scope of e-health, more specifically in the area of seasonal allergies, which cause allergic people to experience annoying symptoms that could be avoided by having access to pollen information in real time. Furthermore, they will also benefit from citizen collaboration through the knowledge of the symptoms other allergic people with the same allergy and in the same location are experiencing. To this end, users will be able to provide their symptoms at any time through their mobile application and the proposed architecture will constantly process that information in real time, sending notifications to users as soon as reported symptoms are seen to exceed a certain threshold. The architecture’s performance, the application’s resource consumption and a satisfaction survey of the app’s usability and usefulness have been tested; all results have been fully satisfactory.</p>", "Keywords": "Collaborative context awareness; Mobile application; Contextual alert; Internet of things; Smart city; E-health", "DOI": "10.1007/s11042-021-10759-6", "PubYear": 2021, "Volume": "80", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "UCASE Software Engineering Group, School of Engineering, University of Cádiz, Cádiz, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "UCASE Software Engineering Group, School of Engineering, University of Cádiz, Cádiz, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "UCASE Software Engineering Group, School of Engineering, University of Cádiz, Cádiz, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "UCASE Software Engineering Group, School of Engineering, University of Cádiz, Cádiz, Spain"}], "References": []}, {"ArticleId": 87177836, "Title": "Effect of compliance on morphological control of dynamic locomotion with HyQ", "Abstract": "<p>Classic control theory applied to compliant and soft robots generally involves an increment of computation that has no equivalent in biology. To tackle this, morphological computation describes a theoretical framework that takes advantage of the computational capabilities of physical bodies. However, concrete applications in robotic locomotion control are still rare. Also, the trade-off between compliance and the capacity of a physical body to facilitate its own control has not been thoroughly studied in a real locomotion task. In this paper, we address these two problems on the state-of-the-art hydraulic robot HyQ. An end-to-end neural network is trained to control HyQ’s joints positions and velocities using only Ground Reaction Forces. Our simulations and experiments demonstrate better controllability using less memory and computational resources when increasing compliance. However, we show empirically that this effect cannot be attributed to the ability of the body to perform intrinsic computation. It invites to give an increased emphasis on compliance and co-design of the controller and the robot to facilitate attempts in machine learning locomotion.</p>", "Keywords": "Quadruped locomotion; Embodiment; Morphological computation; Morphological control; Reflex-based locomotion", "DOI": "10.1007/s10514-021-09974-9", "PubYear": 2021, "Volume": "45", "Issue": "3", "JournalId": 20990, "JournalTitle": "Autonomous Robots", "ISSN": "0929-5593", "EISSN": "1573-7527", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IDLab-AIRO, elis, Ghent University – imec, Ghent, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dynamic Legged Systems (DLS) Lab, Istituto Italiano di Tecnologia (IIT), Genova, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dynamic Legged Systems (DLS) Lab, Istituto Italiano di Tecnologia (IIT), Genova, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "IDLab-AIRO, elis, Ghent University – imec, Ghent, Belgium"}, {"AuthorId": 5, "Name": "<PERSON> w<PERSON>", "Affiliation": "IDLab-AIRO, elis, Ghent University – imec, Ghent, Belgium"}], "References": []}, {"ArticleId": 87178112, "Title": "Correction to a novel framework for rapid diagnosis of COVID-19 on computed tomography scans", "Abstract": "<p>[This corrects the article DOI: 10.1007/s10044-020-00950-0.].</p><p>© The Author(s) 2021.</p>", "Keywords": "", "DOI": "10.1007/s10044-021-00969-x", "PubYear": 2021, "Volume": "24", "Issue": "3", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, COMSATS University Islamabad, Wah Campus, Pakistan."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, HITEC University Taxila, Rawalpindi, Pakistan."}, {"AuthorId": 3, "Name": "Sal<PERSON>", "Affiliation": "Department of Radiology, Shifa International Hospital, Islamabad, Pakistan."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of EE, COMSATS University Islamabad, Abbottabad Campus, Pakistan."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of ECE, COMSATS University Islamabad, Wah Campus, Pakistan."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, COMSATS University Islamabad, Wah Campus, Pakistan."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Applied Mathematics, Silesian University of Technology, Gliwice, Poland."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Informatics, Vytautas Magnus University, Kaunas, Lithuania."}], "References": []}, {"ArticleId": 87178309, "Title": "Motion planning for a pair of tethered robots", "Abstract": "<p>Considering an environment containing polygonal obstacles, we address the problem of planning motions for a pair of planar robots connected to one another via a cable of limited length. Much like prior problems with a single robot connected via a cable to a fixed base, straight line-of-sight visibility plays an important role. The present paper shows how the reduced visibility graph provides a natural discretization and captures the essential topological considerations very effectively for the two robot case as well. Unlike the single robot case, however, the bounded cable length introduces considerations around coordination (or equivalently, when viewed from the point of view of a centralized planner, relative timing) that complicates the matter. Indeed, the paper has to introduce a rather more involved formalization than prior single-robot work in order to establish the core theoretical result—a theorem permitting the problem to be cast as one of finding paths rather than trajectories. Once affirmed, the planning problem reduces to a straightforward graph search with an elegant representation of the connecting cable, demanding only a few extra ancillary checks that ensure sufficiency of cable to guarantee feasibility of the solution. We describe our implementation of A \\({}^\\star \\) search, and report experimental results. Lastly, we prescribe an optimal execution for the solutions provided by the algorithm.</p>", "Keywords": "Motion planning; Tethered robots; Multi-robot coordination; A* search", "DOI": "10.1007/s10514-021-09972-x", "PubYear": 2021, "Volume": "45", "Issue": "5", "JournalId": 20990, "JournalTitle": "Autonomous Robots", "ISSN": "0929-5593", "EISSN": "1573-7527", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Distributed AI and Robotics Laboratory, Department of Computer Science and Engineering, Texas A&M University, College Station, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Distributed AI and Robotics Laboratory, Department of Computer Science and Engineering, Texas A&M University, College Station, USA"}], "References": []}, {"ArticleId": 87178401, "Title": "An Analytic Framework for Effective Public Health Program Design Using Correctional Facilities", "Abstract": "<p>We present a decision analytic framework that uses a mathematical model of Chlamydia trachomatis transmission dynamics in two interacting populations using ordinary differential equations. A public health survey informs model parametrization, and analytical findings guide the computational design of the decision-making process. The potential impact of jail-based screen-treat (S-T) programs on community health outcomes is presented. Numerical experiments are conducted for a case study population to quantify the effect and evaluate the cost-effectiveness of considered interventions. Numerical experiments show the effectiveness of increased jail S-T rates on community cases when resources for a community S-T program stays constant. Although this effect decreases when higher S-T rates are in place, jail-based S-T programs are cost-effective relative to community-based programs.</p><p>Summary of Contribution: Public health programs have been developed to control community-wide infectious diseases and to reduce prevalence of sexually transmitted diseases (STD). These programs can consist of screening and treatment of diseases and behavioral interventions. Public correctional facilities play an important role in operational execution of these public health programs. However, because of lack of capacity and resources, public health programs using correctional facilities are questioned by policy-makers in terms of their costs and benefits. In this article, we present an analytical framework using a computational epidemiology model for supporting public health policy making. The system represents the dynamics of Chlamydia trachomatis transmission in two interacting populations, with an ordinary differential equations-based simulation model. The theoretical epidemic control conditions are derived and numerically tested, which guide the design of simulation experiments. Then cost-effectiveness of the potential policies is analyzed. We also present an extensive sensitivity analyses on model parameters. This study contributes to the computational epidemiology literature by presenting an analytical framework to guide effective simulation experimentation for policy decision making. The presented methodology can be applied to other complex policy and public health problems.</p>", "Keywords": "public health; simulation; program evaluation; computational epidemiology; cost-effectiveness", "DOI": "10.1287/ijoc.2020.1056", "PubYear": 2022, "Volume": "34", "Issue": "1", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "Ozgur M. Araz", "Affiliation": "Supply Chain Management and Analytics, College of Business, University of Nebraska-Lincoln, Nebraska 68588"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics-Physics, University of Puerto Rico at Cayey, Puerto Rico 00736"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Matheson Center for Health Care Studies, University of Utah, Salt Lake City, Utah 84108"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Public Health, University of Nebraska Medical Center, Omaha, Nebraska 68198"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Center for Global Engagement, Technion Israel Institute of Technology, Haifa 3200003, Israel"}], "References": []}, {"ArticleId": 87178441, "Title": "FG-SMOTE: Fuzzy-based Gaussian synthetic minority oversampling with deep belief networks classifier for skewed class distribution", "Abstract": "Purpose Adequate resources for learning and training the data are an important constraint to develop an efficient classifier with outstanding performance. The data usually follows a biased distribution of classes that reflects an unequal distribution of classes within a dataset. This issue is known as the imbalance problem, which is one of the most common issues occurring in real-time applications. Learning of imbalanced datasets is a ubiquitous challenge in the field of data mining. Imbalanced data degrades the performance of the classifier by producing inaccurate results. Design/methodology/approach In the proposed work, a novel fuzzy-based Gaussian synthetic minority oversampling (FG-SMOTE) algorithm is proposed to process the imbalanced data. The mechanism of the Gaussian SMOTE technique is based on finding the nearest neighbour concept to balance the ratio between minority and majority class datasets. The ratio of the datasets belonging to the minority and majority class is balanced using a fuzzy-based Levenshtein distance measure technique. Findings The performance and the accuracy of the proposed algorithm is evaluated using the deep belief networks classifier and the results showed the efficiency of the fuzzy-based Gaussian SMOTE technique achieved an AUC: 93.7%. F1 Score Prediction: 94.2%, Geometric Mean Score: 93.6% predicted from confusion matrix. Research limitations/implications The proposed research still retains some of the challenges that need to be focused such as application FG-SMOTE to multiclass imbalanced dataset and to evaluate dataset imbalance problem in a distributed environment. Originality/value The proposed algorithm fundamentally solves the data imbalance issues and challenges involved in handling the imbalanced data. FG-SMOTE has aided in balancing minority and majority class datasets.", "Keywords": "Imbalanced data;Gaussian SMOTE;Levenshtein distance measure technique;Skewed class distribution;Fuzzy based Gaussian SMOTE;Deep learning;Deep belief network classifier", "DOI": "10.1108/IJICC-12-2020-0202", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 26058, "JournalTitle": "International Journal of Intelligent Computing and Cybernetics", "ISSN": "1756-378X", "EISSN": "1756-3798", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "SCOPE, Vellore Institute of Technology , Vellore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "SCOPE, Vellore Institute of Technology , Vellore, India"}], "References": []}, {"ArticleId": 87178661, "Title": "Identifying the effects of chronic saltwater intrusion in coastal floodplain swamps using remote sensing", "Abstract": "Coastal floodplain swamps (CFS) are an important part of the coastal wetland mosaic, however they are threatened due to accelerated rates of sea level rise and saltwater intrusion (SWI). While remote sensing-based detection of wholesale coastal ecosystem shifts (i.e., from forest to marsh) are relatively straightforward, assessments of chronic, low-level SWI into CFS using remote sensing have yet to be developed and can provide a critical early-warning signal of ecosystem deterioration. In this study, we developed nine ecologically-based hypotheses to test whether remote sensing data could be used to reliably detect the presence of CFS experiencing SWI. Hypotheses were motivated by field- and literature-based understanding of the phenological and vegetative dynamics of CFS experiencing SWI relative to unimpacted, control systems. Hypotheses were organized into two primary groups: those that analyzed differences in summary measures (e.g., median and distribution) between SWI-impacted and unimpacted control sites and those that examined timeseries trends (e.g., sign and magnitude of slope). The enhanced vegetation index (EVI) was used as a proxy for production/biomass and was generated using MODIS surface reflectance data spanning 2000 to 2018. Experimental sites ( n = 8) were selected from an existing network of long-term monitoring sites and included 4 pairs of impacted/non-impacted CFS across the northern Gulf of Mexico from Texas to Florida. The four best-supported hypotheses (81% across all sties) all used summary statistics, indicating that there were significant differences in the EVI of CFS experiencing chronic, low-level SWI compared to controls. These hypotheses were tested using data across a large and diverse region, supporting their implementation by researchers and managers seeking to identify CFS undergoing the first phases of SWI. In contrast, hypotheses that assessed CFS change over time were poorly supported, likely due to the slow and variable pace of ecological change, relatively short remote sensing data record, and/or specific site histories. Overall, these results show that remote sensing data can be used to identify differences in CFS vegetation associated with long-term, low-level SWI, but further methodological advancements are needed to reliably detect the temporal transition process.", "Keywords": "Saltwater intrusion ; Sea level rise ; Coastal floodplain swamps ; Remote sensing ; EVI ; MODIS ; Climate change ; Google Earth Engine ; Ghost Forest", "DOI": "10.1016/j.rse.2021.112385", "PubYear": 2021, "Volume": "258", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Environmental Sciences Department, University of Virginia, Charlottesville, VA 22903, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Engineering School of Sustainable Infrastructure and Environment, Environmental Engineering Sciences Department, University of Florida, Gainesville, FL 32611, USA"}], "References": [{"Title": "Change point estimation of deciduous forest land surface phenology", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111698", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 87178662, "Title": "Aboveground biomass patterns across treeless northern landscapes", "Abstract": "Aboveground vegetation biomass in northern treeless landscapes – peatlands and Arctic tundra – has been modelled with spectral information derived from optical remote sensing in several studies. However, synthesized overviews of biomass patterns across circumpolar sites have been limited. Based on data from eight study sites in Europe, Siberia and Canada, we ask (1) how biomass is divided between plant functional types (PFTs) and (2) how well biomass patterns can be detected with widely available, moderate spatial resolution (3–10 m) satellite imagery and topographic data. We explain biomass patterns using random forest regressions with the predictors being spectral bands and indices calculated from multi-temporal Sentinel-2 and PlanetScope imagery and topographic information calculated from ArcticDEM data. Our results indicate that there are notable differences in vegetation composition between northern landscapes with mosses, graminoids and deciduous shrubs being the most dominant PFTs. Remote sensing data detects biomass patterns, but regression performance varies between sites (explained variance 36–70%, normalized root mean square error 9–19%). There is also variability between sites whether Sentinel-2 or PlanetScope data is more suitable to detect biomass patterns and which the most important predictors are. Topographic information has a minor or negligible importance in most of the sites. Our results suggest that there is no easily generalizable relationship between satellite-derived vegetation greenness and biomass.", "Keywords": "", "DOI": "10.1080/********.2021.1897187", "PubYear": 2021, "Volume": "42", "Issue": "12", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "Aleksi Rä<PERSON>änen", "Affiliation": "Ecosystems and Environment Research Programme, Faculty of Biological and Environmental Sciences, University of Helsinki, Helsinki, Finland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Physical Geography, Stockholm University, Stockholm, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physical Geography, Stockholm University, Stockholm, Sweden;Bolin Centre for Climate Research, Stockholm University, Stockholm, Sweden"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ecosystems and Environment Research Programme, Faculty of Biological and Environmental Sciences, University of Helsinki, Helsinki, Finland"}], "References": [{"Title": "Feasibility of tundra vegetation height retrieval from Sentinel-1 and Sentinel-2 data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111515", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 87178667, "Title": "A detailed evaluation of surface, thermal, and flammable properties of polyamide 12/glass beads composites fabricated by multi jet fusion", "Abstract": "Multi jet fusion (MJF) is an emerging powder three-dimensional (3D) printing technology with an ultrafast printing speed, in which polyamide 12 (PA12) is the main material currently utilised. Although structurally sophisticated 3D components have been printed by MJF, there are limitations due to the high cost of printing materials and inferior performance of the printed parts. Here, PA12/glass beads (PA12/GBs) composites with improved performance were manufactured via the MJF technique. Incorporating GBs effectively regulated the dimensional accuracy. Furthermore, the composite possessed lower surface roughness and higher surface hardness than neat PA12. Thermal analysis showed that the composite exhibited an enhanced decomposition temperature (390 °C) and char yield (38.4 wt.%) compared to neat PA12. Remarkably, the incorporation of GBs did not improve the flame retardance of neat PA12. We identified the improved functionalities of PA12/GBs composites in terms of dimensional accuracy, surface, and thermal properties and analysed possible mechanisms for the observed increased flammability.", "Keywords": "Additive manufacturing ; multi jet fusion ; polyamide 12/glass beads composites ; surface property ; thermal property ; flammable property", "DOI": "10.1080/17452759.2021.1899463", "PubYear": 2021, "Volume": "16", "Issue": "sup1", "JournalId": 7390, "JournalTitle": "Virtual and Physical Prototyping", "ISSN": "1745-2759", "EISSN": "1745-2767", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Key Laboratory for Additive Manufacturing of High-Performance Materials, Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, 518055, People’s Republic of China;School of Mechanical Engineering, Harbin Institute of Technology, Harbin, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Key Laboratory for Additive Manufacturing of High-Performance Materials, Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, 518055, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Key Laboratory for Additive Manufacturing of High-Performance Materials, Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, 518055, People’s Republic of China"}, {"AuthorId": 4, "Name": "Jiaming Bai", "Affiliation": "Shenzhen Key Laboratory for Additive Manufacturing of High-Performance Materials, Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, 518055, People’s Republic of China"}], "References": [{"Title": "3D/4D-printed bending-type soft pneumatic actuators: fabrication, modelling, and control", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "4", "Page": "373", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "A review on machine learning in 3D printing: applications, potential, and challenges", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "63", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A highly stretchable and intrinsically self-healing strain sensor produced by 3D printing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "sup1", "Page": "520", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "3D printed hybrid-dimensional electrodes for flexible micro-supercapacitors with superior electrochemical behaviours", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "sup1", "Page": "511", "JournalTitle": "Virtual and Physical Prototyping"}]}, {"ArticleId": 87178763, "Title": "Adaptive, Hybrid Feature Selection (AHFS)", "Abstract": "This paper deals with the problem of integrating the most suitable feature selection methods for a given problem in order to achieve the best feature order. A new, adaptive and hybrid feature selection approach is proposed, which combines and utilizes multiple individual methods in order to achieve a more generalized solution. Various state-of-the-art feature selection methods are presented in detail with examples of their applications and an exhaustive evaluation is conducted to measure and compare the their performance with the proposed approach. Results prove that while the individual feature selection methods may perform with high variety on the test cases, the combined algorithm steadily provides noticeably better solution.", "Keywords": "Adaptive ; Hybrid Feature sSelection (AHFS) ; Combination of methods ; Statistics ; Information theory ; Exhausting evaluation ; 00-01 ; 99-00", "DOI": "10.1016/j.patcog.2021.107932", "PubYear": 2021, "Volume": "116", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "Zsolt Ján<PERSON>", "Affiliation": "Institute for Computer Science and Control (SZTAKI), Centre of Excellence in Production Informatics and Control, Eötvös Loránd Research Network (ELKH), Research Laboratory on Engineering and Management Intelligence, Intelligent Processes Research Group, H-1111, Budapest, Hungary, Kende u. 13–17., Hungary;<PERSON>, Faculty of Economics, Department of International Economics, Kecskemét, H-1117, Izsáki u. 10., Hungary;Corresponding author.;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> Balázs Ki<PERSON>", "Affiliation": "Institute for Computer Science and Control (SZTAKI), Centre of Excellence in Production Informatics and Control, Eötvös Loránd Research Network (ELKH), Research Laboratory on Engineering and Management Intelligence, Intelligent Processes Research Group, H-1111, Budapest, Hungary, Kende u. 13–17., Hungary"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute for Computer Science and Control (SZTAKI), Centre of Excellence in Production Informatics and Control, Eötvös Loránd Research Network (ELKH), Research Laboratory on Engineering and Management Intelligence, Intelligent Processes Research Group, H-1111, Budapest, Hungary, Kende u. 13–17., Hungary;Eötvös Loránd University, Department of Software Technology and Methodology, Budapest, H-1117, Pázmány P. sny 1/C., Hungary"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Computer Science and Control (SZTAKI), Centre of Excellence in Production Informatics and Control, Eötvös Loránd Research Network (ELKH), Research Laboratory on Engineering and Management Intelligence, Intelligent Processes Research Group, H-1111, Budapest, Hungary, Kende u. 13–17., Hungary"}], "References": [{"Title": "Informative variable identifier: Expanding interpretability in feature selection", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107077", "JournalTitle": "Pattern Recognition"}, {"Title": "Accelerating information entropy-based feature selection using rough set theory with classified nested equivalence classes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107517", "JournalTitle": "Pattern Recognition"}, {"Title": "Mutual information based feature subset selection in multivariate time series classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107525", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 87178818, "Title": "Leader–follower consensus control for a nonlinear multi-agent robot system with input saturation and external disturbance", "Abstract": "This paper addresses the leader–follower consensus control problem for a nonlinear multi-agent robot system with control input constraint and external disturbances. Robot system is one of the most important practical systems in the industry. Due to the presence of disturbances in most practical systems, this paper considers the issue of finite-time leader–follower consensus control of the nonlinear multi-agent robot system along with actuator saturation and bounded disturbance. The modified terminal sliding mode control method is suggested for the system which is able to guarantee the stability of the overall system and fast finite-time leader–follower consensus control. For two different scenarios, the simulation of multi-agent robot system has been performed. The results show the effectiveness of the proposed control method.", "Keywords": "Control input constraint ; robot system ; multi-agent system ; sliding mode control ; finite-time leader–follower consensus control", "DOI": "10.1080/21642583.2021.1897959", "PubYear": 2021, "Volume": "9", "Issue": "1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Science and Research branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Tarbiat Modares University Tehran, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, K.N. Toosi University of Technology, Tehran, Iran"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Science and Research branch, Islamic Azad University, Tehran, Iran"}], "References": [{"Title": "Fuzzy finite-time stable compensation control for a building structural vibration system with actuator failures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106372", "JournalTitle": "Applied Soft Computing"}, {"Title": "A survey on multi-sensor fusion based obstacle detection for intelligent ground vehicles in off-road environments", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "5", "Page": "675", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}]}, {"ArticleId": 87178820, "Title": "Production of global daily seamless data cubes and quantification of global land cover change from 1985 to 2020 - iMap World 1.0", "Abstract": "Longer time high-resolution, high-frequency, consistent, and more detailed land cover data are urgently needed in order to achieve sustainable development goals on food security, high-quality habitat construction, biodiversity conservation and planetary health, and for the understanding, simulation and management of the Earth system. However, due to technological constraints, it is difficult to provide simultaneously high spatial resolution, high temporal frequency, and high quality observation data. Existing mapping solutions are limited by traditional remotely sensed data, that have shorter observation periods, poor spatio-temporal consistency and comparability. Therefore, a new mapping paradigm is needed. This paper develops a framework for intelligent mapping (iMap) of land cover based on state-of-the-art technologies such as cloud computing, artificial intelligence, virtual constellations, and spatio-temporal reconstruction and fusion. Under this framework, we built an automated, serverless, end-to-end data production chain and parallel mapping system based on Amazon Web Services (AWS) and produced the first 30 m global daily seamless data cubes (SDC), and annual to seasonal land cover maps for 1985–2020. The SDC was produced through a multi-source spatio-temporal data reconstruction and fusion workflow based on Landsat, MODIS, and AVHRR virtual constellations. Independent validation results show that the relative mean error of the SDC is less than 2.14%. As analysis ready data (ARD), it can lay a foundation for high-precision quantitative remote sensing information extraction. From this SDC, we produced 36-year long, 30 m resolution global land cover map data set by combining strategies of sample migration, machine learning, and spatio-temporal adjustment. The average overall accuracy of our annual land cover maps over multiple periods of time is 80% for level 1 classification and over 73% for level 2 classification (29 and 33 classes). Based on an objective validation sample consisting of FLUXNET sites, our map accuracy is 10% higher than that of existing global land cover datasets including Globeland30. Our results show that the average global land cover change rate is 0.36%/yr. Global forest decreased by 1.47 million km<sup>2</sup> from 38.44 million km<sup>2</sup>, cropland increased by 0.84 million km<sup>2</sup> from 12.49 million km<sup>2</sup> and impervious surface increased by 0.48 million km<sup>2</sup> from 0.57 million km<sup>2</sup> during 1985– 2020.", "Keywords": "Global land cover mapping ; Seamless data cube ; Daily ; Analysis ready data ; Cloud computing ; Intelligent mapping ; Amazon web Services", "DOI": "10.1016/j.rse.2021.112364", "PubYear": 2021, "Volume": "258", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing 100084, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing 100084, China;Ministry of Education Ecological Field Station for East Asian Migratory Birds, Tsinghua University, Beijing 100084, China;Departments of Geography and Earth Sciences, University of Hong Kong, Hong Kong;Corresponding author at: Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing 100084, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "AI for Earth Laboratory, Tsinghua Cross-Strait Institute, Beijing 100084, China;State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "AI for Earth Laboratory, Tsinghua Cross-Strait Institute, Beijing 100084, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Sinnet Cloud Services Co., Ltd., Dongcheng District, Beijing 100011, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing 100084, China;Ministry of Education Ecological Field Station for East Asian Migratory Birds, Tsinghua University, Beijing 100084, China"}], "References": [{"Title": "Lessons learned implementing an operational continuous United States national land change monitoring capability: The Land Change Monitoring, Assessment, and Projection (LCMAP) approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "238", "Issue": "", "Page": "111356", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Annual maps of global artificial impervious area (GAIA) between 1985 and 2018", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "236", "Issue": "", "Page": "111510", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "SFSDAF: An enhanced FSDAF that incorporates sub-pixel class fraction change information for spatio-temporal image fusion", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111537", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Transitioning from change detection to monitoring with remote sensing: A paradigm shift", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "238", "Issue": "", "Page": "111558", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Deep learning in environmental remote sensing: Achievements and challenges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "241", "Issue": "", "Page": "111716", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Spatially and temporally complete Landsat reflectance time series modelling: The fill-and-fit approach", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "241", "Issue": "", "Page": "111718", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Mapping and sampling to characterize global inland water dynamics from 1999 to 2018 with full Landsat time-series", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "243", "Issue": "", "Page": "111792", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Multispectral high resolution sensor fusion for smoothing and gap-filling in the cloud", "Authors": "<PERSON><PERSON><PERSON>-<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "247", "Issue": "", "Page": "111901", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "FSDAF 2.0: Improving the performance of retrieving land cover changes and preserving spatial details", "Authors": "Dizhou Guo; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "248", "Issue": "", "Page": "111973", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 87179063, "Title": "IFC: Editorial", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1110-8665(21)00012-8", "PubYear": 2021, "Volume": "22", "Issue": "1", "JournalId": 5980, "JournalTitle": "Egyptian Informatics Journal", "ISSN": "1110-8665", "EISSN": "2090-4754", "Authors": [], "References": []}, {"ArticleId": 87179097, "Title": "Application of a Multilevel Technology Acceptance Management Model for Effective Technology Deployment", "Abstract": "Effective deployment of a technology in an environment is the desire of many system developers. Positive uptake of a technology coupled with user acceptance is deemed as a key indicator towards technology acceptance. Knowledge is weighed as a strategic resource for any successful data driven decision making initiative. Institutions leverage on technological initiatives and tools to drive knowledge management (KM) initiatives that enhance quality service delivery and prudent data management. These initiatives provide the overall strategy for managing data resources. They make available knowledge organization tools and techniques while enabling regular updates. Derived benefits of positive deployment of a technological intervention are competency enhancement through gained knowledge, raised quality of service and promotion of healthy development of e-commerce. Successful and timely adoption of technological interventions through which knowledge management initiatives are deployed remains a key challenge to many organizations. This paper proposes the application of a wholesome multilevel technology acceptance management model towards effective technology deployment. The proposed model takes into account human, technological and organizational variables, which exist in a deployment environment. This model will be vital in driving early technology acceptance prediction and timely deployment of mitigation measures to deploy technological interventions successfully.", "Keywords": "", "DOI": "10.5121/ijcsit.2021.13105", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 20755, "JournalTitle": "International Journal of Computer Science and Information Technology", "ISSN": "0975-4660", "EISSN": "0975-3826", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1084-8045(21)00069-2", "PubYear": 2021, "Volume": "180", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [], "References": []}, {"ArticleId": 87179343, "Title": "FakeNewsPerception: An eye movement dataset on the perceived believability of news stories", "Abstract": "Extensive use of the internet has enabled easy access to many different sources, such as news and social media. Content shared on the internet cannot be fully fact-checked and, as a result, misinformation can spread in a fast and easy way. Recently, psychologists and economists have shown in many experiments that prior beliefs, knowledge, and the willingness to think deliberately are important determinants to explain who falls for fake news. Many of these studies only rely on self-reports, which suffer from social desirability. We need more objective measures of information processing, such as eye movements, to effectively analyze the reading of news. To provide the research community the opportunity to study human behaviors in relation to news truthfulness, we propose the FakeNewsPerception dataset. FakeNewsPerception consists of eye movements during reading, perceived believability scores, questionnaires including Cognitive Reflection Test (CRT) and News-Find-Me (NFM) perception, and political orientation, collected from 25 participants with 60 news items. Initial analyses of the eye movements reveal that human perception differs when viewing true and fake news.", "Keywords": "Fake news perception ; Eye tracking dataset ; Scanpath comparison ; Eye movements ; Reading comprehension ; Misinformation", "DOI": "10.1016/j.dib.2021.106909", "PubYear": 2021, "Volume": "35", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>ü<PERSON>", "Affiliation": "Human-Computer Interaction, Department of Computer Science, University of Tübingen, Tübingen, Germany;@osuemer"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Human-Computer Interaction, Department of Computer Science, University of Tübingen, Tübingen, Germany;@efebozkir"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Human-Computer Interaction, Department of Computer Science, University of Tübingen, Tübingen, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Natural Sciences, Martin Luther University Halle-Wittenberg, Halle, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Leibniz-Institut für Wissensmedien, Tübingen, Germany;Department of Psychology, University of Tübingen, Tübingen, Germany;@SonjaUtz"}, {"AuthorId": 6, "Name": "Enkelejda Kasneci", "Affiliation": "Human-Computer Interaction, Department of Computer Science, University of Tübingen, Tübingen, Germany;Corresponding author;@EnkelejdaKasne1"}], "References": []}, {"ArticleId": 87179398, "Title": "Deep and confident prediction for a laboratory earthquake", "Abstract": "<p>Many laboratory fault failure experiments are conducted as analogue of earthquakes, in which most of them are coupled with acoustic emission (AE) as a powerful diagnostic tool for investigating failure precursors. The purpose of this study is to predict time to the next failure in a laboratory fault failure experiment before failures happen based on the instantaneous recorded AE signals. A customized deep learning network comprising the convolutional neural network module and the recurrent neural network module is built and trained using raw AE data directly. No statistical characteristics or handmade features are extracted from raw data, avoiding any possible precursor information losses. More than 600 million AE data from a repetitive fault failure experiment are segmented as several thousand equilong sequences to form training and validation samples. The proposed network delivers satisfactory predicted results with the R2 value 0.55, much better than results using traditional earthquake catalogs method. Results of this study also demonstrate that our network does not prioritize those AE signals collected when failures impend, which is a common bias in traditional earthquake prediction methods. This study definitely holds the promise of using deep learning in earthquake prediction. Further studies are needed when analogous studies proceed to an industrial practice.</p>", "Keywords": "Laboratory earthquake experiment; Deep learning; Convolutional neural network; Long short-term memory", "DOI": "10.1007/s00521-021-05872-4", "PubYear": 2021, "Volume": "33", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Yuanyuan Pu", "Affiliation": "State Key Laboratory of Coal Mine Disaster Dynamics and Control, College of Resources and Safety Engineering, Chongqing University, Chongqing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Coal Mine Disaster Dynamics and Control, College of Resources and Safety Engineering, Chongqing University, Chongqing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mining and Petroleum Engineering, University of Alberta, Edmonton, Canada"}], "References": []}, {"ArticleId": 87179627, "Title": "A pansharpened image quality assessment using segmentation procedure", "Abstract": "Pansharpening is an important way of integrating spatial and spectral information in the field of remote sensing. This field uses the complementary and redundant information between multispectral (MS) images and panchromatic (PAN) images to obtain high spectral and high spatial resolution images. Various pansharpening methods have been introduced so far, each one attempting to provide a pansharpened image with the least distortion and maximum preservation of spectral and spatial information. Due to the importance of this issue, there should be methods and indices to evaluate the performance of different pansharpening algorithms and assess the quality of pansharpened images. In this paper, a segmentation-based method for assessing the quality of fused images is proposed. The advantage of this approach over pixel-based methods is that the pixel-based methods consider the fused images as a set of separate pixels while segmentation can take into account useful spatial information such as neighbourhoods, textures, etc. In the proposed method, by using k-means clustering algorithm, the reference and pansharpened images are segmented into areas with similar spectral and spatial features and the corresponding segments of the images are compared. This method is tested on three real data sets acquired by Pleiades, GeoEye-1, and QuickBird sensors. Experimental results demonstrate the effectiveness of the proposed method in evaluation of the quality of fused images. Disclosure statement No potential conflict of interest was reported by the authors.", "Keywords": "", "DOI": "10.1080/********.2021.1890853", "PubYear": 2021, "Volume": "42", "Issue": "11", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Image Processing and Information Analysis Lab., Faculty of Electrical and Computer Engineering, Tarbiat Modares University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Image Processing and Information Analysis Lab., Faculty of Electrical and Computer Engineering, Tarbiat Modares University, Tehran, Iran"}], "References": [{"Title": "An overview on spectral and spatial information fusion for hyperspectral image classification: Current trends and challenges", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "59", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 87179655, "Title": "Estimating Collaboration through Variation in Time Series from Members in Group Works", "Abstract": "<p>In successful group works, members engage in monitoring where they guess what other members are thinking. Since monitoring promotes self-regulated learning, many teachers try to introduce group works.\r This paper proposes a method to inform teachers on the fly of the monitoring status of students in group works using inexpensive and less invasive sensors. In the field of education, the method enables a teacher who supervises many student groups to direct their group works to successful ones.\r The method collects accelerations of multiple body parts and pulse waves from each member using sensors. The method uses the singular spectral transformation (SST) to detect the relative changes of each signal in the course of time. When a significant change takes place, it is considered the members get arousal. The method considers group members engage in monitoring when significant changes appear simultaneously among them.\r In an experiment to detect successful discussion to integrate ideas from the members into one feasible solution, we have obtained the accuracy of over 0.7. It indicates that the simultaneity of significant changes detected by SST is effective to estimate the monitoring state. In the experiment result, acceleration often worked better than pulse wave signals. It was also found that each member has a different role in the group. The behavior of each member varies with the role of the member. These results show that the method has potential to estimate the monitoring state. They also imply examination of the acceleration of each body parts would enable us to estimate the role of the members. The method allows one teacher to lead many groups to successful group works.</p>", "Keywords": "Group work; Collaboration; Time series data; Singular spectral transformation", "DOI": "10.14738/tmlai.91.9678", "PubYear": 2021, "Volume": "9", "Issue": "1", "JournalId": 29184, "JournalTitle": "Transactions on Machine Learning and Artificial Intelligence", "ISSN": "", "EISSN": "2054-7390", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ritsumeikan University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87179872, "Title": "MOEMS-based accelerometer sensor using photonic crystal for vibration monitoring in an automotive system", "Abstract": "Diagnosing the vibration in automobiles is a priority since it provides comfort to the passenger inside vehicle. This paper presents MOEMS accelerometer sensor by using photonic crystal. A spring mass system with photonic crystal technology is visualised and scrutinised. An optical sensing system with photonic crystal technology was studied and simulated with rods in air and holes in slab configuration. The applied force deflection of rectangular defect slab for vertical and horizontal movement is verified. Gaussian pulse propagated through the defect region in a photonic crystal slab resulted in wavelength shift for each defection of slab. Transmission spectrum was obtained for each deflection direction of slab and configurations. Q factor analysed for each displacement of slab found to be 3,210 for HIS vertical movement. It is found that distinct change in wavelength obtained for holes in slab configuration during vertical and horizontal movement of a slab compared to the results of rods in air configuration. High quality factor and sensitivity improvement obtained in this work showed feasibility of future fabrication for HIS and RIA configuration-based MOEMS sensor for vibration monitoring system. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Accelerometer; HIS; Holes in slab; Light propagation; Micro displacement; MOEMS; Monitoring; Photonic crystal; Q-factor; RIA; Rods in air; Vibration", "DOI": "10.1504/IJCAET.2021.113546", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Emerging Technologies, Jain University, Bangalore, Karnataka, 560069, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Incubation, Innovation, Research and Consultancy (CIIRC), Bangalore, Karnataka, 560082, India"}, {"AuthorId": 3, "Name": "N. <PERSON>", "Affiliation": "School of Engineering and Technology, Jain University, Bangalore, Karnataka, 560069, India"}], "References": []}, {"ArticleId": 87179873, "Title": "Integrating coarse-resolution images and agricultural statistics to generate sub-pixel crop type maps and reconciled area estimates", "Abstract": "Reliable crop type maps are vital for agricultural monitoring, ensuring food security, and environmental sustainability assessments. Coarse-resolution imagery such as MODIS are widely used for crop type mapping due to their short revisit cycles, which is advantageous for detecting the seasonal dynamics of different crop types. However, the inherently low spatial resolution may restrict their utility for mapping crop types in regions with heterogeneous agricultural landscapes. Agricultural statistics, which provide crop acreage information at different spatial and temporal scales, have the potential to improve crop type mapping from remote sensing. Yet, previous studies have often used agricultural statistics as reference data to evaluate the accuracy of satellite-derived crop type maps but have rarely utilized them to improve crop type distribution mapping. The utility of integrating agricultural statistics with satellite images to produce high-accuracy crop type maps is rarely explored. This study presents a methodology for mapping sub-pixel crop type distributions via the integration of MODIS time series and agricultural statistics. We tested our approach in Heilongjiang Province, which has the highest agricultural production in China. First, we used an optimized random forest regression (RF-r) model with training samples derived from high spatial resolution images (i.e. SPOT and Landsat) to predict the sub-pixel crop type distributions from MODIS time series. To optimize the RF-r model, an 8-day MODIS time series of five vegetation indices in 2011 were used as the candidate independent variables, and a backward feature elimination strategy was implemented to select the best variables for model prediction. Second, we developed an Iterative Area Gap Spatial Allocation (IAGSA) method to spatially reconcile the discrepancies between the crop acreage estimated from MODIS-based maps and the agricultural statistics. We found that the MODIS-derived crop fractions agreed with those derived from the high-resolution images, with R<sup>2</sup> > 0.75 for all crop types, yet there was a clear discrepancy between the crop acreage estimated from MODIS and agricultural statistics. The sub-pixel crop type maps adjusted by IAGSA were not only consistent with the agricultural statistics for crop acreage, but also retained the spatial distribution patterns of the original MODIS-derived crop fraction. Our results suggest the advantages of integrating coarse-resolution images and agricultural statistics to map sub-pixel crop type distributions, and to provide consistent estimation of crop acreage. The presented methodology has the potential to map large-scale crop type extent across regions in a cost-effective way.", "Keywords": "Crop type mapping ; Random forest regression ; Iterative area gap spatial allocation ; MODIS ; Area estimate ; Feature selection", "DOI": "10.1016/j.rse.2021.112365", "PubYear": 2021, "Volume": "258", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Geographical Process Analysis & Simulation of Hubei province/College of Urban and Environmental Sciences, Central China Normal University, Wuhan 430079, China;Department of Earth and Environment, Boston University, Boston, MA 02215, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Geography, Kent State University, 325 S. Lincoln Street, Kent, OH 44242, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Earth and Environment, Boston University, Boston, MA 02215, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "International Food Policy Research Institute, 1201 I Street, NW, Washington, DC 20005, USA;Macro Agriculture Research Institute, College of Economics and Management, Huazhong Agricultural University, Wuhan 430070, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Agricultural Remote Sensing (AGRIRS), Ministry of Agriculture and Rural Affairs/Institute of Agricultural Resources and Regional Planning, Chinese Academy of Agricultural Sciences, Beijing 100081, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Agricultural Remote Sensing (AGRIRS), Ministry of Agriculture and Rural Affairs/Institute of Agricultural Resources and Regional Planning, Chinese Academy of Agricultural Sciences, Beijing 100081, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Agricultural Remote Sensing (AGRIRS), Ministry of Agriculture and Rural Affairs/Institute of Agricultural Resources and Regional Planning, Chinese Academy of Agricultural Sciences, Beijing 100081, China;Corresponding author"}], "References": [{"Title": "Monitoring cropland abandonment with Landsat time series", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "246", "Issue": "", "Page": "111873", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 87179922, "Title": "An extended infrastructure security scheme for multi-cloud systems with verifiable inter-server communication protocol", "Abstract": "Cloud service providers need to have elevated level of security than local system users, as users outsource their data to a remote system relying on its genuineness and also data has to be protected from intruders that exist globally. Multi-cloud systems are more prone to various kinds of attacks due to numerous internal communications of sensitive data. Multi-cloud infrastructure security can be enhanced by using stringent encryption algorithms or protocols with several integrity checks on each transaction, but that may drastically reduce the efficiency of the system. This research is one of a kind where we propose a multi-cloud framework comprising different techniques for data security as well as internal server communication security that can efficiently secure the privacy of data from eavesdroppers. Use of efficient cryptographic algorithms, modified <PERSON><PERSON><PERSON><PERSON> key exchange scheme and fragmentation technique assures overall efficiency of a system along with high security. The newest variant of SHA, i.e., SHA-3 outperformed other variants in terms of time efficiency and security. Results on multi-cloud setup demonstrate efficiency of proposed framework in terms of time taken for data storage, computations and retrieval while eliminating risks of attacks. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Cloud computing; Cryptography; Data security; Key exchange protocol; Multi-cloud", "DOI": "10.1504/IJCAET.2021.113544", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, JNTUA, Anantapur, A.P., India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, SVU College of Engineering, Tirupati, A.P., India"}], "References": []}, {"ArticleId": 87179923, "Title": "Heterogeneous wireless network selection by combining GRA and VIKOR under a fuzzy environment", "Abstract": "In a heterogeneous wireless environment, network selection is a strategic issue and has a significant impact on providing the best quality of service (QoS) to the users. The selection of an apt network among various alternatives is a kind of multi-criteria decision-making (MCDM) problem. This paper proposes a model based on VlseKriterijumskaOptimizacija I KompromisnoResenje (VIKOR) under fuzzy environment and grey relational analysis (GRA) for the selection of suitable network in a heterogeneous wireless environment. Here, triangular fuzzy linguistic variables are used to handle the vagueness and subjectivity of the decision-making process. This study focuses on four alternatives such as universal mobile telecommunications system (UMTS), long term evolution (LTE), worldwide interoperability for microwave access (WiMAX) and wireless local area network (WLAN). A six evaluation criteria such as throughput, delay, jitter, packet loss, cost, security are considered for choosing the suitable network in heterogeneous wireless environment. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Fuzzy sets; GRA; Grey relational analysis; MCDM; Multi-criteria decision-making; Network selection; VIKOR", "DOI": "10.1504/IJCAET.2021.113548", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, JKK Nataraja College of Arts and Science, Komarapalayam, Tamilnadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechatronics Engineering, KS Rangasamy College of Technology, Tiruchengode, Tamilnadu, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Bannari Amman Institute of Technology, Sathyamangalam, Erode, India"}], "References": []}, {"ArticleId": 87179924, "Title": "Parameter extraction of PSP MOSFET model using particle swarm optimisation", "Abstract": "System on chip (SoC) architecture offers performance acceleration by offloading compute-intensive functions in FPGA logic, together with application specific instruction set processor (ASIP). In this paper, we report a novel approach of MOSFET parameter extraction at hardware level using Xilinx’s Zynq-7000 SoC AVNET ZedboardTM platform. Extraction of PSP MOSFET model of 65 nm technology devices has been carried out using particle swarm optimisation (PSO) algorithm. It is demonstrated that the SoC implementation of PSO algorithm is able to accurately minimise the rms error between model generated data and experimental data below 9.5%. ARM Cortex A9 processor inside the Zynq-7000 SoC, was found capable to execute the MOSFET model library. It has been observed that the proposed SoC implementation of PSO algorithm runs 3.68 times faster compared to a software-based approach. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Application specific instruction set processor; ARM Cortex; ASIP; Evolutionary algorithm; MOSFET parameter extraction; Particle swarm optimisation; PSO; PSP MOSFET model; SoC; System on chip; Zynq-7000 SoC", "DOI": "10.1504/IJCAET.2021.113550", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 20091, "JournalTitle": "International Journal of Computer Aided Engineering and Technology", "ISSN": "1757-2657", "EISSN": "1757-2665", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, Gujarat Technological University, Ahmadabad, Gujarat, 382424, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication, Vishwakarma Government Engineering College, Chandkheda, Gujarat, 382424, India"}], "References": []}, {"ArticleId": 87179931, "Title": "The incidence of breast health issues and the efficacy of a sports bra fit and issue service in British Army recruits", "Abstract": "<p>Increasing retention of female recruits throughout Basic Training (BT) is a key priority for the British Army. The aims of this study were two-fold; (i) quantify breast health issues and sports bra usage within female British Army recruits, and (ii) assess the influence of professionally fitted sports bras on breast health and bra fit issues across 13 weeks of BT. A survey was completed by 246 female recruits that identified the incidence of breast health issues during BT. Subsequently, 33 female recruits were provided with professionally fitted sports bras during Week-1 of BT. Recruits completed a survey in Week-1 (Pre) and Week-13 (Post). There was a high incidence of bra issues during BT, which did not reduce following the implementation of professionally fitted sports bras. The authors recommend further research into the specific functional requirements of breast support relative to the demands of BT and the needs of the female recruit. <b>Practitioner Summary:</b> The British Army have a duty of care to ensure female recruits are equipped sufficiently for the demands of training. Despite the implementation of a sports bra fitting and issue service bra fit issues remained high. Further research into the specific functional requirements of breast support during training is recommended. <b>Abbreviations:</b> BT: Basic Training; ATR(W): Army Training Regiment Winchester; ATC(P): Army Training Centre Pirbright; BMI: Body Mass Index; NRS: Numeric Rating Scale; FET: Fisher's Exact Test.</p>", "Keywords": "Military recruits;basic training;education;sports bra;women", "DOI": "10.1080/00140139.2021.1895324", "PubYear": 2021, "Volume": "64", "Issue": "8", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Sport, Health and Exercise Science, University of Portsmouth, Hampshire, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Occupational Medicine, HQ Army Recruiting and Initial Training Command, Ministry of Defence, Uphaven, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Human Performance, Defence Security Analysis, Defence Science and Technology Laboratory, Porton Down, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Occupational Medicine, HQ Army Recruiting and Initial Training Command, Ministry of Defence, Uphaven, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Science and Technology Commissioning, Defence Science and Technology, Ministry of Defence, Salisbury, UK"}], "References": []}, {"ArticleId": 87179935, "Title": "Laser-based powder bed fusion of niobium with different build-up rates", "Abstract": "Abstract Niobium is an important material for high temperature applications, in space, in superconductors or in chemical process constructions. Laser-based powder bed fusion of niobium (PBF-LB/M/Nb) offers new opportunities in design, though it is still an expensive technique. The build-up rate is an important factor for economical manufacturing using PBF-LB/M/Nb. It is largely influenced by variation of process parameters, affecting the heat flow during the manufacturing process. In this work, an empirical model for PBF-LB/M/Nb is developed. Based on this model, manufacturing parameter sets using different volume build-up rates are predicted and confirmed. They enable the manufacture of parts with homogeneous and crack-free microstructure with more than 99.9% relative density. Tensile and hardness tests of specimens, which were manufactured using different parameter sets, are performed to determine the effects of the build-up rate—and thus the heat flow during manufacturing—on different mechanical properties. The ultimate tensile strength and yield strength of as-manufactured specimens reach values up to 525 MPa and 324 MPa, respectively, while the elongation at break ranges between approximately 8 and 16%. The Vickers hardness of all specimens was in the range of 149 ± 8 HV0.1. In addition, the microstructure of the manufactured samples is investigated by means of light as well as scanning electron microscopy.", "Keywords": "Laser-based powder bed fusion; Niobium; Mechanical properties; Design of experiments", "DOI": "10.1007/s00170-021-06645-y", "PubYear": 2021, "Volume": "114", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laser Zentrum Hannover e.V., Hannover, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laser Zentrum Hannover e.V., Hannover, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laser Zentrum Hannover e.V., Hannover, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laser Zentrum Hannover e.V., Hannover, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "TANIOBIS GmbH, Goslar, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Laser Zentrum Hannover e.V., Hannover, Germany"}], "References": []}, {"ArticleId": 87180546, "Title": "A niching cross-entropy method for multimodal satellite layout optimization design", "Abstract": "Abstract Satellite layout optimization design (SLOD) relies on solving a high-dimensional and multimodal optimization problem, in which there exist multiple global optimal solutions. Existing algorithms for SLOD focus on seeking only one approximate global optimum. However, finding multiple solutions simultaneously could provide more design diversity for the designers. To alleviate this problem, multimodal optimization method is studied for SLOD in this paper, and an improved niching-based cross-entropy method (INCE) is proposed. INCE consists of an improved niching strategy, cross-entropy method-based offspring generation and a cross operator. CEC2013 benchmarks and satellite layout optimization design problem are investigated to verify the validity and feasibility of the proposed INCE. Compared with several state-of-the-art algorithms, the proposed algorithm performs better.", "Keywords": "Multimodal optimization; Cross entropy; Niching method; Satellite layout optimization design", "DOI": "10.1007/s40747-021-00302-3", "PubYear": 2021, "Volume": "7", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "Jialiang Sun", "Affiliation": "College of Aerospace Science and Engineering, National University of Defense Technology, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Aerospace Science and Engineering, National University of Defense Technology, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Innovation Institute of Defense Technology, Chinese Academy of Military Science, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National Innovation Institute of Defense Technology, Chinese Academy of Military Science, Beijing, China"}], "References": [{"Title": "EEWC: energy-efficient weighted clustering method based on genetic algorithm for HWSNs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "391", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Trade-off between exploration and exploitation with genetic algorithm using a novel selection operator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A PSO-algorithm-based consensus model with the application to large-scale group decision-making", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "287", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Multi-objective particle swarm optimization with random immigrants", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "3", "Page": "635", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 87180550, "Title": "Efficient curvature-constrained least cost route optimization on parallel architectures", "Abstract": "<p>When choosing a path for a linear infrastructure between two terminals, several types of constraints apply for installation and operational criteria. For offshore pipelines or cables, curvature constraints are typically important not only due to the flexural rigidity but also the maneuverability of the laying vessels. Roads are subject to similar curvature constraints to ensure the safety of users travelling at the design speed. Yet, such constraints are not taken into account in traditional least cost routing methods, often based on <PERSON><PERSON><PERSON>’s algorithm. Post-process smoothing is often necessary, resulting in non-optimal routes. We present a new method for least cost route optimization, allowing to incorporate the curvature constraints into the primary calculations as opposed to a post-determination consideration. With this technique, smoothing of the least cost route becomes unnecessary, preserving its optimal character. Optimization algorithms for the trajectories of forward-moving vehicles were adapted for the routing of linear infrastructures and modified to include an angular discretization of variable resolution, resulting in faster and more accurate results. In addition to the inclusion of curvature constraints, the proposed method offers a higher flexibility in terms of local route orientation compared to the traditional <PERSON><PERSON><PERSON>’s algorithm, producing routes of lower cost. The numerical solver was implemented on parallel architectures, to compute optimal routes on realistic domains in reasonable computational times. For personal computers with a few computing cores, the OpenMP implementation on Central Processing Units does not significantly increase the iteration count and provides significant speedups. For certain configurations, the use of Graphical Processing Units reduces further the computational time.</p>", "Keywords": "Pipeline; Route; Cable; Routing; Optimization; Fast sweeping; Least cost route; Parallel processing", "DOI": "10.1007/s00366-021-01343-5", "PubYear": 2022, "Volume": "38", "Issue": "S3", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Fugro Belgium, Louvain-la-Neuve, Belgium"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Fugro Belgium, Louvain-la-Neuve, Belgium"}], "References": []}, {"ArticleId": ********, "Title": "Designing business model development tools for sustainability—a design science study", "Abstract": "Abstract The development of business models that boost fundamental changes in behavior to act more economically, ecologically, and socially is a challenging task because the consideration of sustainability is a multidimensional problem characterized by uncertainty and value conflicts. In order to deal with such complex tasks, methodological and technical tool support is required. Even though tools for business model development are well-established, they typically focus on economic obligations and pay little attention to ecological and social concerns. To bridge this gap, we shed light on the question of how functions for software can be designed to respect sustainability in business models. We present a software prototype and prescriptive design knowledge in the form of design principles and features, and thereby aim to contribute to the information systems body of knowledge by providing guidance to software designers and business model developers on how to reflect on sustainability.", "Keywords": "Software-based tools; Canvas-based tools; Sustainability; Design science; Reflection; O3; Q5; M14", "DOI": "10.1007/s12525-021-00466-3", "PubYear": 2022, "Volume": "32", "Issue": "2", "JournalId": 16578, "JournalTitle": "Electronic Markets", "ISSN": "1019-6781", "EISSN": "1422-8890", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information Systems and Enterprise Modeling, University of Hildesheim, Hildesheim, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Information Systems and Enterprise Modeling, University of Hildesheim, Hildesheim, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Information Systems and Enterprise Modeling, University of Hildesheim, Hildesheim, Germany"}], "References": [{"Title": "Computer-supported reflective learning: how apps can foster reflection at work", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "2", "Page": "167", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Software tools for business model innovation: current state and future challenges", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "469", "JournalTitle": "Electronic Markets"}, {"Title": "Business model tooling: where research and practice meet", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "413", "JournalTitle": "Electronic Markets"}]}]