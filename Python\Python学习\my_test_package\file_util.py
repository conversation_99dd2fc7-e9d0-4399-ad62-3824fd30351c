"""
    文件处理相关的模块
"""

def file_read(file_name):
    """
    读取文件内容
    :param file_name: 文件名
    :return: 文件内容
    """
    f = None
    try:
        f = open(file_name,'r',encoding='utf-8')
        content = f.read()
        print(content)
    except Exception as e:
        print(f"出现异常，原因是{e}")
    finally:
        if f:
            f.close()

def file_write(file_name,data):
    """
    写入文件内容
    :param file_name: 文件名
    :param content: 文件内容
    :return:
    """
    f = open(file_name,'a',encoding='utf-8')
    f.write(data)
    f.write('\n')
    f.close()

if __name__ == '__main__':
    file_write('my_test_package/text.txt','hello world')
    file_read('my_test_package/text.txt')
    file_write('my_test_package/text.txt','hello python')