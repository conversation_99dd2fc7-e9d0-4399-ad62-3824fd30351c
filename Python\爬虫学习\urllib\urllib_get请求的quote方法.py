import urllib.request
import urllib.parse

# 需求 获取https://www.baidu.com/s?wd=周杰伦 的网页源码

# 将周杰伦三个字变成unicode编码
name = urllib.parse.quote('周杰伦')

url = 'https://www.baidu.com/s?wd=' + name

# 请求对象的定制是为了解决反爬的第一种手段

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0'
}



# 定制请求对象
request = urllib.request.Request(url=url, headers=headers)

# 模拟浏览器向服务器发送请求
response = urllib.request.urlopen(request)

# 获取响应内容
content = response.read().decode('utf-8')

print(content)
