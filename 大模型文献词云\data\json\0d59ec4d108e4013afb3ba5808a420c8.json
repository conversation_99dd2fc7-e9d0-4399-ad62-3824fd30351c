[{"ArticleId": 79519907, "Title": "Mapping nature's contribution to SDG 6 and implications for other SDGs at policy relevant scales", "Abstract": "The natural world has multiple, sometimes conflicting, sometimes synergistic, values to society when viewed through the lens of the Sustainable Development Goals (SDGs), Spatial mapping of nature&#x27;s contributions to the SDGs has the potential to support the implementation of SDG strategies through sustainable land management and conservation of ecosystem services. Such mapping requires a range of spatial data. This paper examines the use of remote sensing and spatial ecosystem service modelling to examine nature&#x27;s contribution to targets under SDG 6, also highlighting synergies with other key SDGs and trade-offs with agriculture. We use a wide range of remotely sensed and globally available datasets (for land cover, climate, soil, population, agriculture) alongside the existing and widely used spatial ecosystem services assessment tool, Co$tingNature. With these we identify priority areas for sustainable management to realise targets under SDG 6 (water) at the country scale for Madagascar and at the basin scale for the Volta basin, though the application developed can be applied to any country or major basin in the world. Within this SDG 6 priority areas footprint, we assess the synergies and trade-offs provided by this land for SDG 15 (biodiversity) and SDG 13 (climate action) as well as SDG 2 (zero hunger). Results highlight the co-benefits of sustainably managing nature&#x27;s contribution to SDG 6, such as the protection of forest cover (for SDG target 15.2), carbon storage as a contribution to the Paris climate agreement and nationally determined contributions (SDG 13) and biodiversity (for SDG target 15.5) but also trade-offs with the zero hunger goal (for SDG 2). Such analyses allow for better understanding of land management requirements for realising multiple SDGs through protection and restoration of green infrastructure. We provide a freely available tool, within the Co$tingNature platform, based on a variety of remotely sensed products, that can be used by SDG practitioners to carry out similar analyses and inform decision-making at national or sub-national levels globally.", "Keywords": "Earth observation ; SDGs ; Water ; Trade-off ; Opportunity cost ; Conservation planning ; Co$tingNature", "DOI": "10.1016/j.rse.2020.111671", "PubYear": 2020, "Volume": "239", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Geography, King's College London, Bush House (NE) 4.01, 40 Aldwych, London WC2B 4BG, United Kingdom;Institute of Marine and Antarctic Studies, University of Tasmania, Hobart, Australia;ICRAF, University of the Philippines Los Baños, Los Baños, Philippines;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography, King's College London, Bush House (NE) 4.01, 40 Aldwych, London WC2B 4BG, United Kingdom;UN Environment World Conservation Monitoring Centre, 219 Huntingdon Road, Cambridge CB3 0DL, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Betty & Gordon Moore Center for Science, Conservation International, 2011 Crystal Drive Suite 500, Arlington, VA 22202, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "International Union for Conservation of Nature (IUCN), 28 Rue Mae<PERSON>ey, CH-1196 Gland, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "AmbioTEK Community Interest Company, Leigh on Sea, Essex, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Luc Hoffmann Institute, Rue Mauverney 28, 1196 Gland, Switzerland"}], "References": []}, {"ArticleId": 79520654, "Title": "Draft genome assembly dataset of the Basidiomycete pathogenic fungus, Ganoderma boninense", "Abstract": "<p><i>Ganoderma boninense</i> is a soil-borne Basidiomycete pathogenic fungus that eminent as the key causal of devastating disease in oil palm, named basal stem rot. Being a threat to sustainable palm oil production, it is essential to comprehend the fundamental view of this fungus. However, there is gap of information due to its limited number of genome sequence that is available for this pathogenic fungus. This implies the hitches in performing biological research to unravel the mechanism underlying the pathogen attack in oil palm. Therefore, here we report a dataset of draft genome of <i>G. boninense</i> that was sequenced using Illumina Hiseq 2000. The raw reads were deposited into NCBI database (SRX7136614 and SRX7136615) and can be accessed via Bioproject accession number PRJNA503786.</p><p>© 2020 The Authors.</p>", "Keywords": "Basal stem rot;Ganoderma boninense;Genome sequencing;Pathogenic", "DOI": "10.1016/j.dib.2020.105167", "PubYear": 2020, "Volume": "29", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "FGV R&amp;D Sdn. Bhd., FGV Innovation Centre (Biotechnology), PT 23417 Lengkuk Teknologi, 71760, <PERSON><PERSON> Enstek, Negeri Se<PERSON>n, Malaysia."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "FGV R&amp;D Sdn. Bhd., FGV Innovation Centre (Biotechnology), PT 23417 Lengkuk Teknologi, 71760, <PERSON><PERSON> Enstek, Negeri Se<PERSON>n, Malaysia."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "PT. Tunggal Yunus Estate, Oil Palm Research Station- Topaz, Jl. Soekarno Hatta No.7, 8, 9, 10, <PERSON>ekanbaru, Riau, 28125, Indonesia."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "FGV R&amp;D Sdn. Bhd., FGV Innovation Centre (Biotechnology), PT 23417 Lengkuk Teknologi, 71760, <PERSON><PERSON> Enstek, Negeri Se<PERSON>n, Malaysia."}], "References": []}, {"ArticleId": 79520656, "Title": "Dataset for synthesis of conducting polymers nanocomposites based on aniline and 4-amino-benzylamine catalyzed by chromium (III) exchanged maghnite (Algerian MMT) via in situ polymerization", "Abstract": "In this data we report on conductors polymers nanocomposites synthesized by in situ polymerization of aniline (ANI) and/or 4-aminobenzylamine (4-ABA) in presence of chromium montmorillonite (MMT-Cr<sup>+3</sup>) and ammonium persulfate as oxidizing agent. Homopolymers and copolymers (PANI-co-4-ABA) were prepared at various initial monomer composition and were characterized by Fourier transform Infrared (FT-IR) and UV–vis spectroscopy, X-ray diffraction (XRD) and cyclic voltammeter. The data describes the behavior of the corresponding homopolymers Poly (4-ABA) and (PANI) and showed that the in-situ polymerization produced real nanocomposites containing aniline and 4-aminobenzylamine units and films of products exhibit good electrochemical properties.", "Keywords": "In situ polymerization;Montmorillonite clay;Nanocomposites;Poly (4-aminobenzylamine);Polyaniline", "DOI": "10.1016/j.dib.2020.105161", "PubYear": 2020, "Volume": "29", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre de Recherche Scientifique et Technique en Analyses Physico-chimiques (CRAPC), BP 38Bou-Ismail-RP, 42004, Tipaza, Algeria;Polymer Chemistry Laboratory, Oran1 University Ahmed <PERSON>, BP 1524, <PERSON><PERSON>, 31000, Oran, Algeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Polymer Chemistry Laboratory, Oran1 University Ahmed <PERSON>, BP 1524, <PERSON><PERSON>, 31000, Oran, Algeria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Paris-Est University, UMR 7182. 2-8, F-94320, <PERSON><PERSON><PERSON>, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Polymer Chemistry Laboratory, Oran1 University Ahmed <PERSON>, BP 1524, <PERSON><PERSON>, 31000, Oran, Algeria;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Centre de Recherche Scientifique et Technique en Analyses Physico-chimiques (CRAPC), BP 38Bou-Ismail-RP, 42004, Tipaza, Algeria"}], "References": []}, {"ArticleId": 79520722, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0957-4174(20)30006-3", "PubYear": 2020, "Volume": "144", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [], "References": []}, {"ArticleId": 79520753, "Title": "Corrigendum to “A novel sparse representation model for pedestrian abnormal trajectory understanding” [Expert Systems with Applications, Volume 138, 30 December 2019, 112753]", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.eswa.2019.113093", "PubYear": 2020, "Volume": "144", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Intelligent Transportation Systems Research Center, Wuhan University of Technology, Wuhan, 430063, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Cai", "Affiliation": "Intelligent Transportation Systems Research Center, Wuhan University of Technology, Wuhan, 430063, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Wuhan University of Technology, Wuhan, 430070, PR China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Intelligent Transportation Systems Research Center, Wuhan University of Technology, Wuhan, 430063, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Intelligent Transportation Systems Research Center, Wuhan University of Technology, Wuhan, 430063, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON> Li", "Affiliation": "School of Mechanical, Materials, Mechatronic and Biomedical Engineering, University of Wollongong, Wollongong, NSW 2522, Australia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Computer Engineering Department, University of Alcalá, Alcalá de Henares (Madrid), Spain"}], "References": []}, {"ArticleId": 79520764, "Title": "Performance analysis of user-centric SBS deployment with load balancing in heterogeneous cellular networks: A Thomas cluster process approach", "Abstract": "In conventional heterogeneous cellular networks (HCNets), the locations of user equipments (UEs) and base stations (BSs) are modeled randomly using two different homogeneous Poisson point processes (PPPs). However, this might not be a suitable assumption in case of UE distribution because UE density is not uniform everywhere in HCNets. Keeping in view the existence of nonuniform UEs, the small base stations (SBSs) are assumed to be deployed in the areas with high UE density, which results in correlation between UEs and BS locations. In this paper, we analyse the performance of HCNets with nonuniform UE deployment containing a union of clustered and uniform UE sets. The clustered UEs are considered to be modeled according to Thomas cluster process, and random UEs are assumed to be deployed via homogeneous PPP. The SBSs are considered to be deployed at the center of the UE clusters, which results in user-centric SBS deployment. We derive outage probability and rate coverage of the proposed model. Furthermore, to improve the network performance, the impact of association biasing is also assumed. Our results show that the user-centric SBS deployment outperforms the conventional HCNets model. Increase in association bias upto a certain value results in performance improvement of the proposed user-centric HCNet.", "Keywords": "Heterogeneous cellular networks ; Stochastic geometry ; Nonuniform user distribution ; Thomas cluster process ; Rate coverage ; User-centric SBS deployment ; Capacity driven small cells", "DOI": "10.1016/j.comnet.2020.107120", "PubYear": 2020, "Volume": "170", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Telecommunication and Networking (TeleCoN) Research Lab, GIK Institute of Engineering Sciences and Technology, Topi 23460, Pakistan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering, GIK Institute of Engineering Sciences and Technology, Topi 23460, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Sciences and Engineering, GIK Institute of Engineering Sciences and Technology, Topi 23460, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, City University of Science and Information Technology, Peshawar 25000 Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Communication Technology, University of Agder (UiA), Grimstad N-4898, Norway"}], "References": []}, {"ArticleId": 79520773, "Title": "Influence of service quality in sharing economy: Understanding customers’ continuance intention of bicycle sharing", "Abstract": "The widespread usage of smart bicycle-sharing and the dockless bicycle design creates a new development trend for the public transportation service. Drawing on service quality theory, this study proposes a theoretical model to uncover the impacts of five online to offline service qualities on user satisfaction and behavioral intention with regard to dockless bicycle-sharing. Our results suggest that location reliability, prompt response, customization, transaction assurance and vividness exhibit different extent of influences on customers’ confirmation of a bicycle-sharing service. Moreover, customers’ confirmation is positively related to perceived usefulness and satisfaction, which further enhances their continuance intention. A multi-group analysis suggests that males and females react differently to the five service qualities. Particularly, male users are more likely affected by location reliability and vivid appearance, whilst female users focus more on transaction assurance.", "Keywords": "Bicycle-sharing ; Satisfaction ; Continuance intention ; Service quality ; Gender ; Sharing economy", "DOI": "10.1016/j.elerap.2020.100944", "PubYear": 2020, "Volume": "40", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Harbin Institute of Technology, China;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "College of Business, University of Alabama in Huntsville, United States;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Southern University of Science and Technology, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Management, Harbin Institute of Technology, China"}], "References": [{"Title": "The market impacts of sharing economy entrants: evidence from USA and China", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "3", "Page": "629", "JournalTitle": "Electronic Commerce Research"}]}, {"ArticleId": 79520884, "Title": "Compact eight‐port MIMO/diversity antenna with band rejection characteristics", "Abstract": "<p>A new compact three‐dimensional multiple‐input‐multiple‐output (MIMO) antenna comprised of eight antenna elements is presented. The unit cell of the proposed MIMO/diversity antenna consists of three elliptical rings connected together in the region close to the feed line and a rectangular‐shaped modified ground plane. To achieve polarization diversity with the proposed eight‐port MIMO configuration, four antenna elements are horizontally arranged and the remaining four are vertically oriented. The proposed antenna has an impedance bandwidth ( S <sub>11</sub> < −10 dB) of 25.68 GHz (3.1‐28.78 GHz) with a wireless local area network notch‐band at 5.8 GHz (5.2‐6.5 GHz). In addition to polarization diversity, the proposed antenna provides a reliable link with wireless devices. The prototype antenna design is fabricated and measured for diversity performance. Also, the proposed MIMO antenna provides good performance metrics such as apparent diversity gain, channel capacity loss, envelope correlation coefficient, isolation, mean effective gain, multiplexing efficiency, and total active reflection coefficient.</p>", "Keywords": "diversity;isolation;MIMO;notch", "DOI": "10.1002/mmce.22170", "PubYear": 2020, "Volume": "30", "Issue": "5", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics Engineering, Indian Institute of Technology (ISM), Dhanbad, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics Engineering, Kyungpook National University, Daegu, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "School of Computational and Integrative Sciences, Jawaharlal Nehru University, New Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics Engineering, Indian Institute of Technology (ISM), Dhanbad, India"}, {"AuthorId": 5, "Name": "<PERSON>yun C<PERSON> Choi", "Affiliation": "School of Electronics Engineering, Kyungpook National University, Daegu, Republic of Korea"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Electronics Engineering, Kyungpook National University, Daegu, Republic of Korea"}], "References": []}, {"ArticleId": 79520888, "Title": "Applications of anisotropic one‐step leapfrog HIE‐FDTD method in microwave circuit and antenna", "Abstract": "<p>To verify the effect of artificial anisotropy parameters in one‐step leapfrog hybrid implicit‐explicit finite‐difference time‐domain (FDTD) method, we calculated several microwave components with different characteristics. Introduced auxiliary field variable can reduce the program difficulty and improve the computational efficiency without additional computational time and memory cost. Analyses of the numerical results are proved that the calculation time is reduced to about one‐sixth compared to the traditional FDTD method for the same example simulated. The memory cost and relative error are remained at a good level. The numerical experiments for microwave circuit and antenna have been well demonstrated the method available.</p>", "Keywords": "artificial anisotropy parameters;auxiliary field variable;finite‐difference time‐domain;hybrid implicit‐explicit;one‐step leapfrog", "DOI": "10.1002/mmce.22151", "PubYear": 2020, "Volume": "30", "Issue": "5", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 4, "Name": "Hong<PERSON> Zheng", "Affiliation": "School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang University‐UIUC Institute, Zhejiang University, Hangzhou, China"}], "References": []}, {"ArticleId": 79520935, "Title": "Surface plasmon resonance amplified efficient polarization-selective volatile organic compounds CdSe-CdS/Ag/PMMA sensing material", "Abstract": "Volatile organic compounds (VOCs) are potentially dangerous to the environment and human health. The developed sensing template in this study was prepared by a one-step process on a glass substrate using the electrospinning technique. The sensing material made of the uniaxial aligned CdSe-CdS/Ag/PMMA composite scaffold consists of CdSe-CdS core-shell quantum rods, silver nanoparticles (Ag NPs), and poly(methyl methacrylate) (PMMA) nanofibers. Increasing the specific surface area is beneficial for VOCs harvesting; therefore, the uniaxial aligned CdSe-CdS/Ag/PMMA composite scaffold was slightly treated with UV-ozone etching, and thus efficiently enhancing the extinction changes of VOCs. Also, a high VOC detection capability was demonstrated by the polarization response and Ag surface plasmon resonance of the nanocomposite. A series of typical VOCs and alcoholic VOCs were used for the VOCs sensor practical tests. This VOC sensor achieved a spectacular detection limit of 100 ppm for butanol and 500 ppm for chlorobenzene. The uniaxial aligned CdSe-CdS/Ag/PMMA composite scaffold is a newly designed nanocomposite and can reach the market demands comparing to other commercial high sensitive VOC sensors.", "Keywords": "Volatile organic compounds ; Sensor ; Quantum rods ; Electrospinning ; Polarization-selective ; Surface plasmon resonance", "DOI": "10.1016/j.snb.2020.127760", "PubYear": 2020, "Volume": "309", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Materials Engineering, Chang Gung University, Taoyuan, 33302, Taiwan;Green Technology Research Center, Chang Gung University, Taoyuan, 33302, Taiwan;Division of Neonatology, Department of Pediatrics, Chang Gung Memorial Hospital, Linkou, Taoyuan, 33305, Taiwan;Corresponding author at: Department of Chemical and Materials Engineering, Chang Gung University, Taoyuan, 33302, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Materials Engineering, Chang Gung University, Taoyuan, 33302, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Fiber and Composite Materials, Feng Chia University, Taichung, 40724, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Materials Engineering, Chang Gung University, Taoyuan, 33302, Taiwan;Green Technology Research Center, Chang Gung University, Taoyuan, 33302, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Materials Engineering, Chang Gung University, Taoyuan, 33302, Taiwan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Materials Engineering, Chang Gung University, Taoyuan, 33302, Taiwan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Materials Engineering, Ming Chi University of Technology, New Taipei City, 24301, Taiwan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering, South University of Science and Technology of China, Shenzhen, 518055, Guangdong, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering, South University of Science and Technology of China, Shenzhen, 518055, Guangdong, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Materials Engineering, Ming Chi University of Technology, New Taipei City, 24301, Taiwan;Department of Electronic Engineering, Chang Gung University, Taoyuan, 33302, Taiwan;Biosensor Group, Biomedical Engineering Research Center, Chang Gung University, Taoyuan, 33302, Taiwan;Department of Nephrology, Chang Gung Memorial Hospital, Linkou, Taoyuan, 33305, Taiwan;Corresponding author at: Department of Electronic Engineering, Chang Gung University, Taoyuan, 33302, Taiwan"}], "References": []}, {"ArticleId": 79520938, "Title": "A weight perturbation-based regularisation technique for convolutional neural networks and the application in medical imaging", "Abstract": "A convolutional neural network has the capacity to learn multiple representation levels and abstraction in order to provide a better understanding of image data. In addition, a good multi-level representation of data typically results in a better generalisation capability. This fact emphasises the importance of concentrating on the regularity information of training data in order to improve generalisation. However, the training data contain erroneous information owing to noise and outliers. In this paper, we propose a new regularisation approach for convolutional neural networks with better generalisation properties. Specifically, the weights of the convolution layers are perturbed by additive noise in each learning iteration. The approach provides a better model for prediction, as shown by the experimental results on a number of medical benchmark data sets. Furthermore, the effectiveness and accuracy of the proposed convolutional neural network are demonstrated by comparing with several recent perturbation techniques.", "Keywords": "Convolutional Neural Network ; Regularisation ; Generalisation ; Weight perturbation", "DOI": "10.1016/j.eswa.2020.113196", "PubYear": 2020, "Volume": "149", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Intelligent Systems Research and Innovation (IISRI), Deakin University, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Technology, Deakin University, Geelong, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for Intelligent Systems Research and Innovation (IISRI), Deakin University, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Intelligent Systems Research and Innovation (IISRI), Deakin University, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Intelligent Systems Research and Innovation (IISRI), Deakin University, Australia"}], "References": []}, {"ArticleId": 79520945, "Title": "Single-replicate longitudinal data analysis in the presence of multiple instrumental measurement errors", "Abstract": "In this paper, a novel method as a combination of the expectation–maximization (EM) algorithm and Variogram is proposed to decompose the longitudinal measurement errors in the absence of replications and the presence of multiple instrumental measurement errors. In the proposed method, multiple measurements are considered where the units are observed by several distinct instruments (gauges). The approach decouples the observed variance of the measurement model into the process and measurement system variances. In addition, it decomposes the variance of multiple instruments into the process and instrument variances. In the end, the proposed model is validated and tested based on simulated longitudinal data as well as a real case study related to the Framingham Heart study, measuring systolic blood pressure by multiple instruments. In addition, the robustness of the proposed method to the missing values, a common problem in longitudinal data, is demonstrated.", "Keywords": "Longitudinal data ; Reproducibility ; Multiple instruments ; EM-algorithm ; Variogram ; Missing values", "DOI": "10.1016/j.cie.2020.106301", "PubYear": 2020, "Volume": "141", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Sharif University of Technology, P.O. Box 11155-9414 Azadi Ave., Tehran 1458889694, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Sharif University of Technology, P.O. Box 11155-9414 Azadi Ave., Tehran 1458889694, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Sharif University of Technology, P.O. Box 11155-9414 Azadi Ave., Tehran 1458889694, Iran;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Sharif University of Technology, P.O. Box 11155-9414 Azadi Ave., Tehran 1458889694, Iran"}], "References": []}, {"ArticleId": 79521220, "Title": "Near-infrared methane sensor system using off-axis integrated cavity output spectroscopy with novel dual-input dual-output coupling scheme for mode noise suppression", "Abstract": "Mode noise suppression in off-axis integrated cavity output spectroscopy (OA-ICOS) is a key for improving the signal-to-noise ratio (SNR) of a sensor system, which basically depends on the ability to smooth out the cavity mode structure and to reduce the cavity mode linewidth. We demonstrated a novel dual-input dual-output (DIDO) coupling scheme of OA-ICOS for mode noise suppression. The influences of beam splitting ratio and light reflection number to the output intensity and cavity mode linewidth were theoretically studied. Experimental investigation on the suppression of cavity mode noise was carried out in terms of detection sensitivity and SNR through methane (CH<sub>4</sub>) measurements. A SNR of 221 and a detection sensitivity of 1.73 × 10<sup>−8</sup> cm<sup>-1</sup> Hz<sup>-1/2</sup> was obtained for the DIDO-based OA-ICOS sensor system. The SNR was improved by a factor of ∼ 2.5 and the sensitivity was improved by a factor of ∼ 2.2 compared to the regular single-input single-output (R-SISO) approach. Multi-input multi-output (MIMO) configuration was further numerically studied to verify the noise suppression ability of this detection concept. This work reveals a new laser-to-cavity coupling method and provides a novel way to exploit OA-ICOS sensors with improved SNR and sensitivity.", "Keywords": "Chemical gas sensor ; Dual-input dual-output ; Off-axis integrated cavity output spectroscopy ; Methane detection ; Wavelength modulation spectroscopy", "DOI": "10.1016/j.snb.2020.127674", "PubYear": 2020, "Volume": "308", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, PR China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, PR China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Rice University, 6100 Main Street, Houston, TX, 77005, USA"}], "References": []}, {"ArticleId": 79521247, "Title": "Learning the representation of raw acoustic emission signals by direct generative modelling and its use in chronology-based clusters identification", "Abstract": "Acoustic emission (AE) is a passive monitoring technique used for learning about the behaviour of an engineered system. The streaming obtained by continuously recording AE transient signals is treated by a four steps procedure: 1) The detection of salient AE signals by distinguishing noise against non-noise signals using wavelet denoising, 2) the statistical representation of randomly selected AE signals using Autoregressive Weakly Hidden Markov Models, 3) an inference phase by applying those models to unknown AE signals and generating a set of novelty scores reflecting differences between signals, 4) the clustering of novelty scores using constraint-based consensus clustering. Compared to the standard way relying on the transformation of all AE signals by manual feature engineering (MFE) before clustering, the main breaktrough proposed in this paper holds in the use of the raw AE signals, with different lengths and various scales, to build high level information and organise the low level streaming data. Validated first on simulated data, we show the potential of this methodology for interpreting acoustic emission streaming originating from composite materials.", "Keywords": "Acoustic emission ; Raw waveform ; Model-based clustering ; Representation learning ; Novelty detection", "DOI": "10.1016/j.engappai.2020.103478", "PubYear": 2020, "Volume": "90", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Univ. Bourgogne <PERSON>-Comté, FEMTO-ST, CNRS/UFC/ENSMM/UTBM, Department of Applied Mechanics, 25000 Besançon, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Univ. Bourgogne Franche-Comté, FEMTO-ST, CNRS/UFC/ENSMM/UTBM, Department of Applied Mechanics, 25000 Besançon, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Univ. Bourgogne Franche-Comté, FEMTO-ST, CNRS/UFC/ENSMM/UTBM, Department of Applied Mechanics, 25000 Besançon, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sapienza-Università di Roma, Department of Chemical Engineering Materials Environment, Via Eudossiana 18, 00184 Rome, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Univ. Bourgogne Franche-Comté, FEMTO-ST, CNRS/UFC/ENSMM/UTBM, Department of Applied Mechanics, 25000 Besançon, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "INSA Lyon, MATEIS Laboratory, 7 av. <PERSON>, 69000 Lyon, France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sapienza-Università di Roma, Department of Chemical Engineering Materials Environment, Via Eudossiana 18, 00184 Rome, Italy"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Univ. Bourgogne Franche-Comté, FEMTO-ST, CNRS/UFC/ENSMM/UTBM, Department of Applied Mechanics, 25000 Besançon, France"}], "References": []}, {"ArticleId": 79521263, "Title": "On Channel Codes for Short Underwater Messages", "Abstract": "<p>Acoustic underwater communication is a challenging task. For a reliable transmission, not only good channel estimation and equalization, but also strong error correcting codes are needed. In this paper, we present the results of the coding competition “Wanted: Best channel codes for short underwater messages” as well as our own findings on the influence of the modulation alphabet size in the example of non-binary polar codes. Furthermore, the proposals of the competition are compared to other commonly used channel codes.</p>", "Keywords": "channel coding; underwater acoustic sensor network; forward error correction; phase shift keying; polar code channel coding ; underwater acoustic sensor network ; forward error correction ; phase shift keying ; polar code", "DOI": "10.3390/info11020058", "PubYear": 2020, "Volume": "11", "Issue": "2", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Communications, Hamburg University of Technology, 21073 Hamburg, Germany↑Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Communications, Hamburg University of Technology, 21073 Hamburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "German Technical Center for Ships and Naval Weapons, Naval Technology and Research (WTD 71), Underwater Communication Branch, 24340 Eckernförde, Germany"}], "References": []}, {"ArticleId": 79521374, "Title": "A Revision of the <PERSON><PERSON><PERSON>ner–Tavani Model of Digital Trust and a Philosophical Problem It Raises for Social Robotics", "Abstract": "<p>In this paper the <PERSON><PERSON><PERSON><PERSON><PERSON> model of digital trust is revised—new conditions for self-trust are incorporated into the model. These new conditions raise several philosophical problems concerning the idea of a substantial self for social robotics, which are closely examined. I conclude that reductionism about the self is incompatible with, while the idea of a substantial self is compatible with, trust relations between human agents, between human agents and artificial agents, and between artificial agents.</p>", "Keywords": "artificial agent (AA); trust; self-trust; <PERSON><PERSON><PERSON><PERSON>–<PERSON><PERSON> model of trust; self-identification; reductionism about the self; substantial self; personal identity; normative expectation; diffuse default trust artificial agent (AA) ; trust ; self-trust ; <PERSON><PERSON><PERSON><PERSON>–<PERSON><PERSON> model of trust ; self-identification ; reductionism about the self ; substantial self ; personal identity ; normative expectation ; diffuse default trust", "DOI": "10.3390/info11010048", "PubYear": 2020, "Volume": "11", "Issue": "1", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Philosophy, Rutgers University-Newark, Newark, NJ 07103, USA ; The Saul Kripke Center, CUNY, The Graduate Center, New York, NY 10016, USA"}], "References": []}, {"ArticleId": 79521520, "Title": "Scaled Coupled Norms and Coupled Higher-Order Tensor Completion", "Abstract": "<p>Recently, a set of tensor norms known as coupled norms has been proposed as a convex solution to coupled tensor completion. Coupled norms have been designed by combining low-rank inducing tensor norms with the matrix trace norm. Though coupled norms have shown good performances, they have two major limitations: they do not have a method to control the regularization of coupled modes and uncoupled modes, and they are not optimal for couplings among higher-order tensors. In this letter, we propose a method that scales the regularization of coupled components against uncoupled components to properly induce the low-rankness on the coupled mode. We also propose coupled norms for higher-order tensors by combining the square norm to coupled norms. Using the excess risk-bound analysis, we demonstrate that our proposed methods lead to lower risk bounds compared to existing coupled norms. We demonstrate the robustness of our methods through simulation and real-data experiments.</p>", "Keywords": "", "DOI": "10.1162/neco_a_01254", "PubYear": 2020, "Volume": "32", "Issue": "2", "JournalId": 11544, "JournalTitle": "Neural Computation", "ISSN": "0899-7667", "EISSN": "1530-888X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Bioinformatics Center, Institute for Chemical Research, Kyoto University, Uji, Kyoto 611-0011, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Informatics, Kyoto University, Yoshida-honmachi, Sakyo-ku, Kyoto 606-8501, Japan; RIKEN, Center for Advanced Intelligence Project, Tokyo 103-0027, Japan; Institute of Statistical Mathematics, Tokyo 190-8562, Japan; and PRESTO, Japan Science and Technological Agency, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bioinformatics Center, Institute for Chemical Research, Kyoto University, Gokasho, Uji, Kyoto 611-0011, Japan, and Department of Computer Science, Aalto University, Espoo F1-00076, Finland"}], "References": []}, {"ArticleId": 79521521, "Title": "Transition Scale-Spaces: A Computational Theory for the Discretized Entorhinal Cortex", "Abstract": "<p>Although hippocampal grid cells are thought to be crucial for spatial navigation, their computational purpose remains disputed. Recently, they were proposed to represent spatial transitions and convey this knowledge downstream to place cells. However, a single scale of transitions is insufficient to plan long goal-directed sequences in behaviorally acceptable time.</p><p>Here, a scale-space data structure is suggested to optimally accelerate retrievals from transition systems, called transition scale-space (TSS). Remaining exclusively on an algorithmic level, the scale increment is proved to be ideally [Formula: see text] for biologically plausible receptive fields. It is then argued that temporal buffering is necessary to learn the scale-space online. Next, two modes for retrieval of sequences from the TSS are presented: top down and bottom up. The two modes are evaluated in symbolic simulations (i.e., without biologically plausible spiking neurons). Additionally, a TSS is used for short-cut discovery in a simulated Morris water maze. Finally, the results are discussed in depth with respect to biological plausibility, and several testable predictions are derived. Moreover, relations to other grid cell models, multiresolution path planning, and scale-space theory are highlighted. Summarized, reward-free transition encoding is shown here, in a theoretical model, to be compatible with the observed discretization along the dorso-ventral axis of the medial entorhinal cortex. Because the theoretical model generalizes beyond navigation, the TSS is suggested to be a general-purpose cortical data structure for fast retrieval of sequences and relational knowledge.</p><p>Source code for all simulations presented in this paper can be found at https://github.com/rochus/transitionscalespace .</p>", "Keywords": "", "DOI": "10.1162/neco_a_01255", "PubYear": 2020, "Volume": "32", "Issue": "2", "JournalId": 11544, "JournalTitle": "Neural Computation", "ISSN": "0899-7667", "EISSN": "1530-888X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Bosch Center for Artificial Intelligence, Robert Bosch GmbH, 71272 Renningen, Germany"}], "References": []}, {"ArticleId": 79521522, "Title": "Improving Generalization via Attribute Selection on Out-of-the-Box Data", "Abstract": "<p>Zero-shot learning (ZSL) aims to recognize unseen objects (test classes) given some other seen objects (training classes) by sharing information of attributes between different objects. Attributes are artificially annotated for objects and treated equally in recent ZSL tasks. However, some inferior attributes with poor predictability or poor discriminability may have negative impacts on the ZSL system performance. This letter first derives a generalization error bound for ZSL tasks. Our theoretical analysis verifies that selecting the subset of key attributes can improve the generalization performance of the original ZSL model, which uses all the attributes. Unfortunately, previous attribute selection methods have been conducted based on the seen data, and their selected attributes have poor generalization capability to the unseen data, which is unavailable in the training stage of ZSL tasks. Inspired by learning from pseudo-relevance feedback, this letter introduces out-of-the-box data—pseudo-data generated by an attribute-guided generative model—to mimic the unseen data. We then present an iterative attribute selection (IAS) strategy that iteratively selects key attributes based on the out-of-the-box data. Since the distribution of the generated out-of-the-box data is similar to that of the test data, the key attributes selected by IAS can be effectively generalized to test data. Extensive experiments demonstrate that IAS can significantly improve existing attribute-based ZSL methods and achieve state-of-the-art performance.</p>", "Keywords": "", "DOI": "10.1162/neco_a_01256", "PubYear": 2020, "Volume": "32", "Issue": "2", "JournalId": 11544, "JournalTitle": "Neural Computation", "ISSN": "0899-7667", "EISSN": "1530-888X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Xu", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, Jiangsu 210094, China, and Centre for Artificial Intelligence, University of Technology Sydney, Ultimo, NSW 2007, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Artificial Intelligence, University of Technology Sydney, Ultimo, NSW 2007, Australia"}, {"AuthorId": 3, "Name": "Chuancai Liu", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, Jiangsu 210094, China, and Collaborative Innovation Center of IoT Technology and Intelligent Systems, Minjiang University, Fuzhou, Fujian 350000, China"}], "References": []}, {"ArticleId": 79521548, "Title": "From Synaptic Interactions to Collective Dynamics in Random Neuronal Networks Models: Critical Role of Eigenvectors and Transient Behavior", "Abstract": "<p>The study of neuronal interactions is at the center of several big collaborative neuroscience projects (including the Human Connectome Project, the Blue Brain Project, and the Brainome) that attempt to obtain a detailed map of the entire brain. Under certain constraints, mathematical theory can advance predictions of the expected neural dynamics based solely on the statistical properties of the synaptic interaction matrix. This work explores the application of free random variables to the study of large synaptic interaction matrices. Besides recovering in a straightforward way known results on eigenspectra in types of models of neural networks proposed by <PERSON><PERSON> and <PERSON> ( 2006 ), we extend them to heavy-tailed distributions of interactions. More important, we analytically derive the behavior of eigenvector overlaps, which determine the stability of the spectra. We observe that on imposing the neuronal excitation/inhibition balance, despite the eigenvalues remaining unchanged, their stability dramatically decreases due to the strong nonorthogonality of associated eigenvectors. This leads us to the conclusion that understanding the temporal evolution of asymmetric neural networks requires considering the entangled dynamics of both eigenvectors and eigenvalues, which might bear consequences for learning and memory processes in these models. Considering the success of free random variables theory in a wide variety of disciplines, we hope that the results presented here foster the additional application of these ideas in the area of brain sciences.</p>", "Keywords": "", "DOI": "10.1162/neco_a_01253", "PubYear": 2020, "Volume": "32", "Issue": "2", "JournalId": 11544, "JournalTitle": "Neural Computation", "ISSN": "0899-7667", "EISSN": "1530-888X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "<PERSON> Institute of Physics and Mark Kac Complex Systems Research Center, Jagiellonian University, PL 30-348 Kraków, Poland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "<PERSON> Institute of Physics and Mark Kac Complex Systems Research Center, Jagiellonian University, PL 30-348 Kraków, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Center for Complex Systems and Brain Sciences, Escuela de Ciencia y Tecnología, Universidad Nacional de San Martín, San Martín, 1650 Buenos Aires, Argentina and Consejo Nacional de Investigaciones Científicas y Tecnológicas, 1650 Buenos Aires, Argentina"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "<PERSON> Institute of Physics and Mark Kac Complex Systems Research Center, Jagiellonian University, PL 30-348 Kraków, Poland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> Institute of Physics and Mark Kac Complex Systems Research Center, Jagiellonian University, PL 30-348 Kraków, Poland"}], "References": []}, {"ArticleId": 79521549, "Title": "Synaptic Scaling Improves the Stability of Neural Mass Models Capable of Simulating Brain Plasticity", "Abstract": "<p>Neural mass models offer a way of studying the development and behavior of large-scale brain networks through computer simulations. Such simulations are currently mainly research tools, but as they improve, they could soon play a role in understanding, predicting, and optimizing patient treatments, particularly in relation to effects and outcomes of brain injury. To bring us closer to this goal, we took an existing state-of-the-art neural mass model capable of simulating connection growth through simulated plasticity processes. We identified and addressed some of the model's limitations by implementing biologically plausible mechanisms. The main limitation of the original model was its instability, which we addressed by incorporating a representation of the mechanism of synaptic scaling and examining the effects of optimizing parameters in the model. We show that the updated model retains all the merits of the original model, while being more stable and capable of generating networks that are in several aspects similar to those found in real brains.</p>", "Keywords": "", "DOI": "10.1162/neco_a_01257", "PubYear": 2020, "Volume": "32", "Issue": "2", "JournalId": 11544, "JournalTitle": "Neural Computation", "ISSN": "0899-7667", "EISSN": "1530-888X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer and Information Science, University of Ljubljana, 1000 Ljubljana, Slovenia, and MBLab, Department of Psychology, Faculty of Arts, University of Ljubljana, Slovenia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Translational and Clinical Research Institute, Newcastle University, Newcastle upon Tyne, NE1 4LP, U.K."}], "References": []}, {"ArticleId": 79521550, "Title": "Face Representations via Tensorfaces of Various Complexities", "Abstract": "<p>Neurons selective for faces exist in humans and monkeys. However, characteristics of face cell receptive fields are poorly understood. In this theoretical study, we explore the effects of complexity, defined as algorithmic information (Kolmogorov complexity) and logical depth, on possible ways that face cells may be organized. We use tensor decompositions to decompose faces into a set of components, called tensorfaces, and their associated weights, which can be interpreted as model face cells and their firing rates. These tensorfaces form a high-dimensional representation space in which each tensorface forms an axis of the space. A distinctive feature of the decomposition algorithm is the ability to specify tensorface complexity. We found that low-complexity tensorfaces have blob-like appearances crudely approximating faces, while high-complexity tensorfaces appear clearly face-like. Low-complexity tensorfaces require a larger population to reach a criterion face reconstruction error than medium- or high-complexity tensorfaces, and thus are inefficient by that criterion. Low-complexity tensorfaces, however, generalize better when representing statistically novel faces, which are faces falling beyond the distribution of face description parameters found in the tensorface training set. The degree to which face representations are parts based or global forms a continuum as a function of tensorface complexity, with low and medium tensorfaces being more parts based. Given the computational load imposed in creating high-complexity face cells (in the form of algorithmic information and logical depth) and in the absence of a compelling advantage to using high-complexity cells, we suggest face representations consist of a mixture of low- and medium-complexity face cells.</p>", "Keywords": "", "DOI": "10.1162/neco_a_01258", "PubYear": 2020, "Volume": "32", "Issue": "2", "JournalId": 11544, "JournalTitle": "Neural Computation", "ISSN": "0899-7667", "EISSN": "1530-888X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cognitive Brain Mapping Laboratory, RIKEN Center for Brain Science, Wako-shi, Saitama 351-0198, Japan, and Computational Neurobiology Laboratory, Salk Institute, La Jolla, CA 92037, U.S.A."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Computational and Data-Intensive Science and Engineering, Skolkovo Institute of Science and Technology, 143026 Moscow, Russia; and Institute of Global Innovation Research, Tokyo University of Agriculture and Technology, Tokyo 183-8538, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Computational and Data-Intensive Science and Engineering, Skolkovo Institute of Science and Technology, 143026 Moscow, Russia; Systems Research Institute, Polish Academy of Sciences, 01447 Warsaw, Poland; College of Computer Science, Hangzhou Dianzu University, Hangzhou 310018, China; and Institute of Global Innovation Research, Tokyo University of Agriculture and Technology, Tokyo 183-8538, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Cognitive Brain Mapping Laboratory, RIKEN Center for Brain Science, Wako-shi, Saitama 325-0198, Japan"}], "References": []}, {"ArticleId": 79521594, "Title": "The impact of carbon emissions tax on vertical centralized supply chain channel structure", "Abstract": "This paper studies the supply chain channel structure which is considered choosing either centralization or decentralization in both a single chain system with a single product and a two chains system with competition in the two substitutable products under carbon emissions tax, respectively. In the two cases, each chain consists of a retailer, who acts as a follower and a manufacturer who acts as a leader. Our analytical results show that: (i) When decentralization is practicable, the government is willing to set a lower carbon tax. (ii) In a single-chain system, the monopolistic manufacturer is better off under decentralization than under centralization when its product emissions pollution is high enough. (iii) In the two chains system, two manufacturers can benefit from decentralization when their product is more emissions polluting. Under certain conditions, comparing with centralization, decentralization enhances two manufacturers’ profits through carbon emissions tax reduction and keeps consumer surplus and social welfare at the first-best level. (iv) The manufacturer’s product quantity under decentralization is lower than that under centralization due to double marginalization effect, which also results in the lower environmental pollution. Our study also suggests that for the seriously emissions polluter, when it utilizes decentralization, the government have to introduce a lower carbon emissions tax in the consideration of abating environmental pollution and of maximizing its profits. The result can also benefit policy-makers in instructing firms to choose decentralization for promoting social development and environmental protection.", "Keywords": "Carbon emissions tax ; Supply chain channel structure ; Game theory ; Nash equilibrium", "DOI": "10.1016/j.cie.2020.106303", "PubYear": 2020, "Volume": "141", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Tourism and Urban Management, Jiangxi University of Finance and Economics, Nanchang 330013, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Technology, East China Jiaotong University, Nanchang 330100, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Southeast University, Nanjing 211102, China;Corresponding author"}], "References": []}, {"ArticleId": 79521601, "Title": "Evolving clustering algorithm based on mixture of typicalities for stream data mining", "Abstract": "Many applications have been producing streaming data nowadays, which motivates techniques to extract knowledge from such sources. In this sense, the development of data stream clustering algorithms has gained an increasing interest. However, the application of these algorithms in real systems remains a challenge, since data streams often come from non-stationary environments, which can affect the choice of a proper set of model parameters for fitting the data or finding a correct number of clusters. This work proposes an evolving clustering algorithm based on a mixture of typicalities. It is based on the TEDA framework and divide the clustering problem into two subproblems: micro-clusters and macro-clusters. Experimental results with benchmarking data sets showed that the proposed methodology can provide good results for clustering data and estimating its density even in the presence of events that can affect data distribution parameters, such as concept drifts. In addition, the model parameters were robust in relation to the state-of-the-art algorithms.", "Keywords": "Clustering ; Data stream ; Concept drift ; Stream data mining ; Evolving fuzzy systems", "DOI": "10.1016/j.future.2020.01.017", "PubYear": 2020, "Volume": "106", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Universidade Federal de Minas Gerais, Belo Horizonte, Brazil;Machine Intelligence and Data Science (MINDS) Laboratory, Federal University of Minas Gerais, Belo Horizonte, Brazil;Instituto Federal de Minas Gerais, Campus Sabará, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Universidade Federal de Minas Gerais, Belo Horizonte, Brazil;Machine Intelligence and Data Science (MINDS) Laboratory, Federal University of Minas Gerais, Belo Horizonte, Brazil;Corresponding author at: Department of Electrical Engineering, Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Universidade Federal de Minas Gerais, Belo Horizonte, Brazil;Machine Intelligence and Data Science (MINDS) Laboratory, Federal University of Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Engineering, Braude College of Engineering, Karmiel, Israel"}], "References": []}, {"ArticleId": 79521613, "Title": "Probabilistic quantum clustering", "Abstract": "Quantum Clustering is a powerful method to detect clusters with complex shapes. However, it is very sensitive to a length parameter that controls the shape of the Gaussian kernel associated with a wave function, which is employed in the Schr<PERSON><PERSON><PERSON> equation with the role of a density estimator. In addition, linking data points into clusters requires local estimates of covariance which requires further parameters. This paper proposes a Bayesian framework that provides an objective measure of goodness-of-fit to the data, to optimise the adjustable parameters. This also quantifies the probabilities of cluster membership, thus partitioning the data into a specific number of clusters, where each cluster probability is estimated through an aggregated density function composed of the data samples that generate the cluster, having each cluster an associated probability density function P ( K   X ) ; this probability can be used as a measure of how well the clusters fit the data. Another main contribution of the work is the adaptation of the <PERSON><PERSON><PERSON><PERSON><PERSON> equation to deal with local length parameters for cluster discrimination by density. The proposed framework is tested on real and synthetic data sets, assessing its validity by measuring concordance with the Jaccard score.", "Keywords": "Quantum clustering ; Mixture of Gaussians ; Probabilistic framework ; Unsupervised assessment ; <PERSON><PERSON><PERSON>", "DOI": "10.1016/j.knosys.2020.105567", "PubYear": 2020, "Volume": "194", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Liverpool John Moores University, 3 Byrom Street, Liverpool, L3 3AF, UK;Corresponding author"}, {"AuthorId": 2, "Name": "Paulo J.G. Lisboa", "Affiliation": "Department of Applied Mathematics, Liverpool John Moores University, 3 Byrom Street, Liverpool, L3 3AF, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Applied Mathematics, Liverpool John Moores University, 3 Byrom Street, Liverpool, L3 3AF, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Applied Mathematics, Liverpool John Moores University, 3 Byrom Street, Liverpool, L3 3AF, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Departament d’Enginyeria Electrònica - ETSE, Universitat de València, Av. Universitat, SN, 46100 Burjassot, València, Spain"}], "References": [{"Title": "Low-rank local tangent space embedding for subspace clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Rong <PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 79521691, "Title": "Generative adversarial network-based semi-supervised learning for real-time risk warning of process industries", "Abstract": "Due to the non-cognition of real-time data, rare loss-based risk warning methods can effectively respond to unexpected emergencies. Machine learning has powerful data processing capabilities and real-time computing functions and thus is suitable for offsetting the shortcomings of traditional risk methods. Risk analysis can be easily employed to perform risk-based data classification for a set of process data. However, the risk analysis process is too complicated to label risk levels for all processes, which is hard to satisfy the requirements of the amount of data for supervised learning. Therefore, the present paper focuses on developing semi-supervised learning methods for the construction of real-time risk-based early warning systems. By using fuzzy HAZOP, we estimate the risk of systems quantitatively based on the process data. With the consideration of scarce labeled data and numerous unlabeled information, we develop the generative adversarial network (GAN)-based semi-supervised learning method to identify the process risk timely. Besides, deep network architecture integrated with the convolutional neural network (CNN) is used for the codification of multi-dimensional process data to enhance the generalization of warning models. Finally, the effectiveness of the proposed method is evaluated through a comparative study with different algorithms on a case of multizone circulating reactor (MZCR).", "Keywords": "Risk warning ; Deep learning ; Generative adversarial networks ; Semi-supervised learning ; Fuzzy HAZOP ; Multizone circulating reactor", "DOI": "10.1016/j.eswa.2020.113244", "PubYear": 2020, "Volume": "150", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Offshore Engineering and Safety Technology (COEST), China University of Petroleum (East China), No.66, Changjiang West Road, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "School of Resources Engineering, Xi'an University of Architecture and Technology, No.13 Yanta Road, Xi'an, 710055, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Offshore Engineering and Safety Technology (COEST), China University of Petroleum (East China), No.66, Changjiang West Road, Qingdao, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Offshore Engineering and Safety Technology (COEST), China University of Petroleum (East China), No.66, Changjiang West Road, Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Offshore Engineering and Safety Technology (COEST), China University of Petroleum (East China), No.66, Changjiang West Road, Qingdao, China"}], "References": []}, {"ArticleId": 79521702, "Title": "Special issue on “Generalized Barycentric Coordinates”", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.cagd.2020.101818", "PubYear": 2020, "Volume": "77", "Issue": "", "JournalId": 6580, "JournalTitle": "Computer Aided Geometric Design", "ISSN": "0167-8396", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Oslo, Norway"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Università della Svizzera italiana, Lugano, Switzerland;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Davis, USA"}], "References": [{"Title": "An h-adaptive meshfree-enriched finite element method based on convex approximations for the three-dimensional ductile crack propagation simulation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "", "Page": "101795", "JournalTitle": "Computer Aided Geometric Design"}, {"Title": "Polyhedral finite elements for nonlinear solid mechanics using tetrahedral subdivisions and dual-cell aggregation", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "77", "Issue": "", "Page": "101812", "JournalTitle": "Computer Aided Geometric Design"}, {"Title": "On the application of polygonal finite element method for Stokes flow – A comparison between equal order and different order approximation", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "77", "Issue": "", "Page": "101813", "JournalTitle": "Computer Aided Geometric Design"}]}, {"ArticleId": 79521707, "Title": "Efficient algorithms for mining clickstream patterns using pseudo-IDLists", "Abstract": "Sequential pattern mining is an important task in data mining. Its subproblem, clickstream pattern mining, is starting to attract more research due to the growth of the Internet and the need to analyze online customer behaviors. To date, only few works are dedicately proposed for the problem of mining clickstream patterns. Although one approach is to use the general algorithms for sequential pattern mining, those algorithms’ performance may suffer and the resources needed are more than would be necessary with a dedicated method for mining clickstreams. In this paper, we present pseudo-IDList, a novel data structure that is more suitable for clickstream pattern mining. Based on this structure, a vertical format algorithm named CUP (Clickstream pattern mining Using Pseudo-IDList) is proposed. Furthermore, we propose a pruning heuristic named DUB (Dynamic intersection Upper Bound) to improve our proposed algorithm. Four real-life clickstream databases are used for the experiments and the results show that our proposed methods are effective and efficient regarding runtimes and memory consumption.", "Keywords": "Sequential pattern mining ; Clickstream pattern mining ; Candidate pruning ; Vertical format", "DOI": "10.1016/j.future.2020.01.034", "PubYear": 2020, "Volume": "107", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Institute of Research and Development, Duy Tan University, Da Nang 550000, Viet Nam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, International University, Ho Chi Minh City, Viet Nam;Vietnam National University, Ho Chi Minh City, Viet Nam"}, {"AuthorId": 3, "Name": "Bay Vo", "Affiliation": "Faculty of Information Technology, Ho Chi Minh City University of Technology (HUTECH), Ho Chi Minh City, Viet Nam;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Sejong University, Seoul, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Applied Informatics, Tomas Bata University in Zlín, Nám. T.G. Masaryka 5555, Zlín, Czech Republic"}, {"AuthorId": 6, "Name": "Tzung-<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National University of Kaohsiung, Kaohsiung, Taiwan"}], "References": []}, {"ArticleId": 79521740, "Title": "Developing a deep learning framework with two-stage feature selection for multivariate financial time series forecasting", "Abstract": "Intelligent financial forecasting modeling plays an important role in facilitating investment-related decision-making activities in financial markets. However, accurate multivariate financial time series forecasting remains a challenge due to its complex nonlinear pattern. Aiming to fill the gap in the field, a novel forecasting framework, based on a two-stage feature selection model, deep learning model, and error correction model, is presented in this study, aiming at effectively capturing the nonlinearity inherent in multivariate financial time series. Concretely, the proposed two-stage feature selection model is utilized to determine the optimal feature set to further improve the generalization of the proposed deep learning model based on three deep learning units. Meanwhile, the error correction model is used to correct the forecasts and improve the accuracy further. To validate the performance of the forecasting framework, the case studies and the corresponding sensitivity analysis are carried out, consequently demonstrating its superiority, as compared to 16 benchmarks considered.", "Keywords": "Deep learning ; Multivariate financial time series ; Forecasting ; Feature selection ; Multi-objective optimization", "DOI": "10.1016/j.eswa.2020.113237", "PubYear": 2020, "Volume": "148", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for Energy, Environment & Economy Research, Zhengzhou University, Zhengzhou, 450001, China;School of Statistics, Dongbei University of Finance and Economics, Dalian, 116025, China;School of Computer Science, Faculty of Engineering and Information Technology, University of Technology Sydney, Australia"}, {"AuthorId": 2, "Name": "Jianzhou Wang", "Affiliation": "School of Statistics, Dongbei University of Finance and Economics, Dalian, 116025, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Faculty of Engineering and Information Technology, University of Technology Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Statistics, Dongbei University of Finance and Economics, Dalian, 116025, China;School of Computer Science, Faculty of Engineering and Information Technology, University of Technology Sydney, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Statistics, Dongbei University of Finance and Economics, Dalian, 116025, China"}], "References": []}, {"ArticleId": 79521741, "Title": "An improved tactile sensing device for material characterization via friction-induced vibrations", "Abstract": "We report an improvement of an instrument used to correlate perceived material surface characteristics to vibrations induced in a transducer via surface profile tracing by addition of stick-slip vibration and stochastic measurements. Our instrument utilizes an inductive transducer and turntable stage to measure and characterize induced vibrations, and additionally incorporates an elastic bead of poly(ethylene-co-vinyl acetate) (EVA) on the stylus tip to simulate the slight tackiness and elasticity of skin, when in contact with certain types of materials. It is thus able to tease vibrational frequencies out of the measurement through stick-slip friction from the polymer’s elasticity and tackiness. The result is a larger range of measurable frequencies of vibration from both normal and tangential forces, for use in classification of selected materials, that vary uniquely with roughness, speed of measurement, contact forces, and material compositions. Additionally, to simulate varying pressure during stroking of a material surface with a finger-tip, the turntable was warped to induce a periodic wobble. This wobble produces variations in contact friction over each period of the measurement by varying contact area and contact force, and thus increases consistency of measurements via statistical distributions of contact force and area. The distributions that manifest are high frequencies over a single period of measurement that can be parsed for classification purposes. The results of this work show that the improved instrument has significantly more data characteristics to utilize and can identify subtle differences between similar materials in identical environments.", "Keywords": "Haptic device ; Material characterization ; Random Signal processing ; Tactile sensor ; Stick-Slip friction vibration", "DOI": "10.1016/j.sna.2019.111824", "PubYear": 2020, "Volume": "303", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Case Western Reserve University, Cleveland, OH 44106, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Case Western Reserve University, Cleveland, OH 44106, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Macromolecular Science and Engineering, Case Western Reserve University, Cleveland, OH 44106, USA"}], "References": []}, {"ArticleId": 79521743, "Title": "Self-accelerated Thompson sampling with near-optimal regret upper bound", "Abstract": "Thompson sampling utilizes Bayesian heuristic strategy to balance the exploration-exploitation trade-off. It has been applied in a variety of practical domains and achieved great success. Despite being empirically efficient and powerful, Thompson sampling has eluded theoretical analysis. Existing analyses of Thompson sampling only provide regret upper bound of O ˜ ( d 3 / 2 T ) for linear contextual bandits, which is worse than the information-theoretic lower bound by a factor of d . In this paper, we design and analyze self-accelerated Thompson Sampling algorithm for the stochastic contextual multi-armed bandit problem. Our analysis establishes that the regret upper bound of self-accelerated Thompson sampling is O ˜ ( d T ) , which is the best upper bound achieved by any efficient contextual bandit algorithm in infinite action space. Our experiment on simulated data and real-world dataset shows that self-accelerated Thompson sampling outperforms standard Thompson sampling in both convergence rate and prediction accuracy.", "Keywords": "Thompson sampling ; Linear bandits ; Online optimization", "DOI": "10.1016/j.neucom.2020.01.086", "PubYear": 2020, "Volume": "399", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Science and Technology of China, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Science and Technology of China, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Science and Technology of China, China"}], "References": []}, {"ArticleId": 79521744, "Title": "Heterogeneous graph neural networks for noisy few-shot relation classification", "Abstract": "Relation classification is an essential and fundamental task in natural language processing. Distant supervised methods have achieved great success on relation classification, which improve the performance of the task through automatically extending the dataset. However, the distant supervised methods also bring the problem of wrong labeling. Inspired by people learning new knowledge from only a few samples, we focus on predicting formerly unseen classes with a few labeled data. In this paper, we propose a heterogeneous graph neural network for few-shot relation classification, which contains sentence nodes and entity nodes. We build the heterogeneous graph based on the message passing between entity nodes and sentence nodes in the graph, which can capture rich neighborhood information of the graph. Besides, we introduce adversarial learning for training a robust model and evaluate our heterogeneous graph neural networks under the scene of introducing different rates of noise data. Experimental results have demonstrated that our model outperforms the state-of-the-art baseline models on the FewRel dataset.", "Keywords": "Relation extraction ; Heterogeneous graph neural networks ; Few-shot learning ; Adversarial learning", "DOI": "10.1016/j.knosys.2020.105548", "PubYear": 2020, "Volume": "194", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Intelligent Technology and Systems, Department of Computer Science and Technology, Tsinghua University, Beijing 100084, China;School of Information Science and Engineering, Hebei University of Science and Technology, Shijiazhuang 050018, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Intelligent Technology and Systems, Department of Computer Science and Technology, Tsinghua University, Beijing 100084, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Hebei University of Science and Technology, Shijiazhuang 050018, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Intelligent Technology and Systems, Department of Computer Science and Technology, Tsinghua University, Beijing 100084, China;School of Information Science and Engineering, Hebei University of Science and Technology, Shijiazhuang 050018, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Hebei University of Science and Technology, Shijiazhuang 050018, China;Corresponding authors"}], "References": []}, {"ArticleId": 79521748, "Title": "Machine learning explainability via microaggregation and shallow decision trees", "Abstract": "Artificial intelligence (AI) is being deployed in missions that are increasingly critical for human life. To build trust in AI and avoid an algorithm-based authoritarian society, automated decisions should be explainable. This is not only a right of citizens, enshrined for example in the European General Data Protection Regulation, but a desirable goal for engineers, who want to know whether the decision algorithms are capturing the relevant features. For explainability to be scalable, it should be possible to derive explanations in a systematic way. A common approach is to use simpler, more intuitive decision algorithms to build a surrogate model of the black-box model (for example a deep learning algorithm) used to make a decision. Yet, there is a risk that the surrogate model is too large for it to be really comprehensible to humans. We focus on explaining black-box models by using decision trees of limited depth as a surrogate model. Specifically, we propose an approach based on microaggregation to achieve a trade-off between the comprehensibility and the representativeness of the surrogate model on the one side and the privacy of the subjects used for training the black-box model on the other side.", "Keywords": "Explainability ; Machine learning ; Data protection ; Microaggregation ; Privacy", "DOI": "10.1016/j.knosys.2020.105532", "PubYear": 2020, "Volume": "194", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitat Rovira i Virgili, Department of Computer Engineering and Mathematics, CYBERCAT-Center for Cybersecurity Research of Catalonia, UNESCO Chair in Data Privacy, Av. <PERSON><PERSON> 26, 43007 Tarragona, Catalonia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitat Rovira i Virgili, Department of Computer Engineering and Mathematics, CYBERCAT-Center for Cybersecurity Research of Catalonia, UNESCO Chair in Data Privacy, Av. <PERSON> 26, 43007 Tarragona, Catalonia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universitat Rovira i Virgili, Department of Computer Engineering and Mathematics, CYBERCAT-Center for Cybersecurity Research of Catalonia, UNESCO Chair in Data Privacy, Av. <PERSON><PERSON> 26, 43007 Tarragona, Catalonia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universitat Rovira i Virgili, Department of Computer Engineering and Mathematics, CYBERCAT-Center for Cybersecurity Research of Catalonia, UNESCO Chair in Data Privacy, Av. <PERSON><PERSON> 26, 43007 Tarragona, Catalonia"}], "References": [{"Title": "Low-rank local tangent space embedding for subspace clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Rong <PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 79521811, "Title": "Could social media help in newcomers' socialization? The moderating effect of newcomers’ utilitarian motivation", "Abstract": "Organizational turnover is often highest among newcomers. Thus, exploring how to help newcomers integrate into an organization is critical to both practice and theory. Social media is a popular tool used by employees to communicate and establish relationships with their colleagues and leaders to achieve rapid socialization. However, the influence of social media usage intensity on newcomer socialization has been neglected in the literature. Using a sample of 238 newcomers and taking WeC<PERSON> as an example, this study examines the impact of the intensity of social media usage on newcomers&#x27; socialization and its boundary conditions. The results show that the intensity of social media usage has significant and positive effects on the two dimensions of newcomer socialization (performance proficiency and interpersonal relationships) and that the effect of the intensity of social media usage on performance proficiency is mediated by interpersonal relationships. In addition, the intensity of social media usage has a stronger impact on the two dimensions of newcomer socialization when newcomers’ utilitarian motivation is high than when it is low.", "Keywords": "Social media usage intensity ; Utilitarian motivation ; Performance proficiency ; Interpersonal relationships ; Newcomers", "DOI": "10.1016/j.chb.2020.106273", "PubYear": 2020, "Volume": "107", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "Di C<PERSON>", "Affiliation": "School of Management, Shandong University, No.27 Shanda South Road, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Shandong University, No.27 Shanda South Road, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Shandong University, No.27 Shanda South Road, Jinan, China;Corresponding author. Department of Marketing, School of Management, Shandong University, No.27 Shanda South Road, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The London School of Economics and Political Science, UK"}], "References": []}, {"ArticleId": 79522413, "Title": "Twitter-Based Safety Confirmation System for Disaster Situations", "Abstract": "<p>In the aftermath of disastrous events in Japan, safety information and rescue requests, as well as emergency alerts and damage situations, have been shared on Twitter. However, even victims who are familiar with smartphones or similar devices and social media cannot easily share detailed information, such as the coordinates or address of their current location, which are essential components of safety information and rescue requests. Moreover, local governments and rescue experts have difficulty in gathering such tweets from Twitter. In this paper, we propose a novel system to enable the victims to share their safety information, make rescue requests, and enable quick information gathering for decision making by local government staff or rescue experts. The proposed system is a Twitter-based safety confirmation system named T-@npi. Using the proposed application, the users can easily submit their safety information and send rescue requests on Twitter. The users who want to confirm the safety information can check it quickly on Twitter or via this system. Furthermore, the registered safety information is displayed on an online map to support rescue and assistance activities by local governments and rescue experts.</p>", "Keywords": "safety confirmation; disaster; Twitter; social media safety confirmation ; disaster ; Twitter ; social media", "DOI": "10.3390/fi12010014", "PubYear": 2020, "Volume": "12", "Issue": "1", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Communication and Network Engineering, School of Information and Telecommunication Engineering, Tokai University, Minato City, Tokyo 108-8619, Japan↑Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Communication and Network Engineering, School of Information and Telecommunication Engineering, Tokai University, Minato City, Tokyo 108-8619, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Course of Information and Telecommunication Engineering, Graduate School of Information and Telecommunication Engineering, Tokai University, Minato City, Tokyo 108-8619, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Human and Information Science, School of Information Science and Technology, Tokai University, Hiratsuka City, Kanagawa 259-1292, Japan"}], "References": []}, {"ArticleId": 79522525, "Title": "Privacy-preserving video fall detection using visual shielding information", "Abstract": "<p>In recent years, fall detection, especially for the elderly living alone at home, is a challenge in the field of computer vision and pattern recognition. However, there is a concern of loss of privacy in intelligent visual surveillance. In order to solve the contradiction between security surveillance and privacy protection in vision-based fall detection methods, we propose a concept named visual shielding, which can be applied to eliminate visual information but not reduce monitoring function. In the preprocessing, the multilayer compressed sensing is used to compress video frames to achieve visual shielding effect. Then, object region is separated from shielded videos by the low-rank and sparse decomposition theory, based on which to extract motion trajectory features of the object via the dense trajectory algorithm. Finally, the fall detection issue is transformed into the sparse recognition problem of the signal. Experimental results on three public fall databases show that the specificity and sensitivity of the proposed method can be maintained at an ideal level, which has the dual advantages of high privacy protection and high recognition accuracy.</p>", "Keywords": "Fall detection; Privacy protection; Compressed sensing; Video surveillance; Feature extraction; Sparse recognition", "DOI": "10.1007/s00371-020-01804-w", "PubYear": 2021, "Volume": "37", "Issue": "2", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Research Center of Wideband Wireless Communication Technology, Ministry of Education, Nanjing University of Posts and Telecommunications, Nanjing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Research Center of Wideband Wireless Communication Technology, Ministry of Education, Nanjing University of Posts and Telecommunications, Nanjing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Engineering Research Center of Wideband Wireless Communication Technology, Ministry of Education, Nanjing University of Posts and Telecommunications, Nanjing, People’s Republic of China"}], "References": []}, {"ArticleId": 79522554, "Title": "Using Behavioral Analytics to Predict Customer Invoice Payment", "Abstract": "<p>Experiences from various industries show that companies may have problems collecting customer invoice payments. Studies report that almost half of the small- and medium-sized enterprise and business-to-business invoices in the United States and United Kingdom are paid late. In this study, our aim is to understand customer behavior regarding invoice payments, and propose an analytical approach to learning and predicting payment behavior. Our logic can then be embedded into a decision support system where decision makers can make predictions regarding future payments, and take actions as necessary toward the collection of potentially unpaid debt, or adjust their financial plans based on the expected invoice-to-cash amount. In our analysis, we utilize a large data set with more than 1.6 million customers and their invoice and payment history, as well as various actions (e.g., e-mail, short message service, phone call) performed by the invoice-issuing company toward customers to encourage payment. We use supervised and unsupervised learning techniques to help predict whether a customer will pay the invoice or outstanding balance by the next due date based on the actions generated by the company and the customer's response. We propose a novel behavioral scoring model used as an input variable to our predictive models. Among the three machine learning approaches tested, we report the results of logistic regression that provides up to 97% accuracy with or without preclustering of customers. Such a model has a high potential to help decision makers in generating actions that contribute to the financial stability of the company in terms of cash flow management and avoiding unnecessary corporate lines of credit.</p>", "Keywords": "behavioral analytics;invoice collection;invoice to cash;logistic regression;machine learning;predictive analytics", "DOI": "10.1089/big.2018.0116", "PubYear": 2020, "Volume": "8", "Issue": "1", "JournalId": 18108, "JournalTitle": "Big Data", "ISSN": "2167-6461", "EISSN": "2167-647X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MIT Media Laboratory, Human Dynamics Group, Massachusetts Institute of Technology, Cambridge, Massachusetts.;School of Management, Sabanci University, Istanbul, Turkey."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Sabanci University, Istanbul, Turkey.;Graduate Program in Data Science, New College of Florida, Sarasota, Florida."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering and Natural Sciences, Sabanci University, Istanbul, Turkey."}], "References": []}, {"ArticleId": 79522573, "Title": "A Novel Approach to Develop and Deploy Preventive Measures for Different Types of DDoS Attacks", "Abstract": "<p>In the new era of computers, everyone relies on the internet for basic day-to-day activities to sophisticated and secret tasks. The cyber threats are increasing, not only theft and manipulation of someone's information, but also forcing the victim to deny other requests. A DDoS (Distributed Denial of Service) attack, which is one of the serious issues in today's cyber world needs to be detected and their advance towards the server should be blocked. In the article, the authors are focusing mainly on preventive measures of different types of DDoS attacks using multiple IPtables rules and Windows firewall advance security settings configuration, which would be feasibly free on any PC. The IPtables when appropriately selected and implemented can establish a relatively secure barrier for the system and the external environment.</p>", "Keywords": "", "DOI": "10.4018/IJISP.2020040101", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 13525, "JournalTitle": "International Journal of Information Security and Privacy", "ISSN": "1930-1650", "EISSN": "1930-1669", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology, Manipur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Manipur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "NIELIT, Imphal, India"}], "References": []}, {"ArticleId": 79522760, "Title": "Structure-based discovery of novel inhibitors of Mycobacterium tuberculosis CYP121 from Indonesian natural products", "Abstract": "<p>Tuberculosis (TB) continues to be a serious global health threat with the emergence of multidrug-resistant tuberculosis (MDR-TB) and extremely drug-resistant tuberculosis (XDR-TB). There is an urgent need to discover new drugs to deal with the advent of drug-resistant TB variants. This study aims to find new M. tuberculosis CYP121 inhibitors by the screening of Indonesian natural products using the principle of structure-based drug design and discovery. In this work, eight natural compounds isolated from Rhoeo spathacea and Pluchea indica were selected based on their antimycobacterial activity. Derivatives compound were virtually designed from these natural molecules to improve the interaction of ligands with CYP121. Virtual screening of ligands was carried out using AutoDock Vina followed by 50 ns molecular dynamics simulation using YASARA to study the inhibition mechanism of the ligands. Two ligands, i.e., kaempferol (KAE) and its benzyl derivative (KAE3), are identified as the best CYP121 inhibitors based on their binding affinities and adherence to the <PERSON><PERSON><PERSON>'s rule. Results of molecular dynamics simulation indicate that KAE and KAE3 possess a unique inhibitory mechanism against CYP121 that is different from GGJ (control ligand). The control ligand alters the overall dynamics of the receptor, which is indicated by changes in residue flexibility away from CYP121 binding site. Meanwhile, the dynamic changes caused by the binding of KAE and KAE3 are isolated around the binding site of CYP121. These ligands can be developed for further potential biological activities.</p><p>Copyright © 2020. Published by Elsevier Ltd.</p>", "Keywords": "CYP121;Molecular docking;Molecular dynamic;Tuberculosis", "DOI": "10.1016/j.compbiolchem.2020.107205", "PubYear": 2020, "Volume": "85", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Biotechnology, Atma Jaya Catholic University, Jakarta, 12930, DKI Jakarta, Indonesia. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Biotechnology, Faculty of Life Sciences, Surya University, Tangerang, 15810, Banten, Indonesia."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biotechnology, Faculty of Life Sciences, Surya University, Tangerang, 15810, Banten, Indonesia."}, {"AuthorId": 4, "Name": "Hilya<PERSON>z <PERSON>", "Affiliation": "Genetics Research Centre, Universitas Yarsi, Jakarta, 10510, DKI Jakarta, Indonesia."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Biology, Universitas Nasional, Jakarta, 12520, Indonesia."}], "References": []}, {"ArticleId": 79522810, "Title": "Automated urban train control with hybrid Event-B: ‘Tackling’ the rugby club problem", "Abstract": "Normally, the passengers on urban rail systems remain fairly stationary, allowing for a relatively straightforward approach to controlling the dynamics of the system, based on the total rest mass of the train and passengers. However, when a mischievous rugby club board an empty train and then run and jump-stop during the braking process, they can disrupt the automatic mechanisms for aligning train and platform doors. This is the Rugby Club Problem for automated urban train control. A simple scenario of this kind is modelled in Hybrid Event-B, and sufficient conditions are derived for the prevention of the overshoot caused by the jump-stop. The challenges of making the model more realistic are discussed, and a strategy for dealing with the Rugby Club Problem, when it cannot be prevented, is contemplated.", "Keywords": "Automated train control ; Formal system development ; Hybrid Event-B ; Newtonian mechanics", "DOI": "10.1016/j.scico.2020.102404", "PubYear": 2020, "Volume": "190", "Issue": "", "JournalId": 12043, "JournalTitle": "Science of Computer Programming", "ISSN": "0167-6423", "EISSN": "1872-7964", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Manchester, Oxford Road, Manchester, M13 9PL, UK"}], "References": []}, {"ArticleId": 79522897, "Title": "Characterisation of the internal resonances of a clamped-clamped beam MEMS resonator", "Abstract": "<p>Nonlinear coupling between modes through internal resonance has many applications in MEMS resonators. The primary objective of this work is to systematically study all possible internal resonance conditions between the first three modes and the feasibility of mode interaction in an electrostatically excited, straight, clamped-clamped beam. The static displacement and the first three natural frequencies of the beam are obtained for an applied DC voltage by using Galerkin based reduced order model and finite element method. All six possible commensurable relations between the first three frequencies of the beam are obtained by sweeping the non-dimensional parameters ( \\(\\alpha _1\\) and \\(\\alpha _2 V_\\mathrm{dc}^2\\) ) which depend on beam dimensions, material properties and external forcing. It is also demonstrated by a further examination of the dynamical equations that only one resonance condition is capable of exhibiting modal coupling when externally excited. A detailed analysis is carried out for this feasible resonance condition by altering \\(\\alpha _1\\) and \\(\\alpha _2 V_\\mathrm{dc}^2\\) and solving the relevant nonlinear coupled equations by using both numerical time integration and the method of multiple scales. From this study, we observe that the lower mode is automatically excited after driving amplitude for the higher mode reaches a critical value—a sign of mode interaction initiation. We also find that amplitude of the higher mode is saturated after interaction with the lower mode for a combination of \\(\\alpha _1\\) and \\(\\alpha _2 V_\\mathrm{dc}^2\\) . Moreover, we see that the frequency bandwidth of modal interaction increases with excitation amplitude for all combinations of \\(\\alpha _1\\) and \\(\\alpha _2 V_\\mathrm{dc}^2\\) . Finally, the role of external damping on the amplitude-frequency response curve of both modes during the mode interaction is also investigated.</p>", "Keywords": "", "DOI": "10.1007/s00542-020-04750-8", "PubYear": 2020, "Volume": "26", "Issue": "6", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Indian Institute of Technology Bombay, Mumbai, India"}, {"AuthorId": 2, "Name": "Mandar <PERSON>", "Affiliation": "Department of Civil Engineering, Indian Institute of Technology Bombay, Mumbai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Mechanical Engineering, Indian Institute of Technology Bombay, Mumbai, India"}], "References": []}, {"ArticleId": 79523102, "Title": "Assessment of trapped powder removal and inspection strategies for powder bed fusion techniques", "Abstract": "Abstract The issue of trapped powder within a part made using powder bed fusion additive manufacturing (AM) is one of the ‘dirty secrets’ of AM, yet it has not received significant attention by the research community. Trapped powders limit the application of AM for complex geometries, including heat exchangers and dies with conformal cooling channels. Being able to detect and remove trapped powder from the build is a necessary step to avoid downstream processing and performance challenges. In this work, ‘powder challenge geometries’ with complex internal features were fabricated via laser powder bed fusion (L-PBF) and electron beam selective melting (EBSM) and were used to assess the effectiveness of several powder removal and inspection methods. Hand-held ultrasonic polishing was explored as a powder removal technique and was shown to effectively clear extremely elongated channels that grit-blasting (the current industry standard) cannot clear. X-ray computed tomography (XCT) and weighing were used to inspect and quantitatively assess the effectiveness of powder removal techniques on the challenge geometries. Using the lesser known ‘vacuum boiling’ powder removal process and the more common ultrasonic bathing process, trapped L-PBF powder was easily removed from the deep channels. Conversely, trapped EBSM powder was difficult to remove using ultrasonic polishing as the powder was sintered inside the channels. It was shown that the powder recovered by the ultrasonic polishing process had size distributions, surface chemistry, morphology and porosity similar to the virgin powder. It is suggested, on these bases, that the recovered powder could likely be recycled without detrimental effects on the process operation.", "Keywords": "Additive manufacturing; Post-processing; Inspection; Micro-CT", "DOI": "10.1007/s00170-020-04930-w", "PubYear": 2020, "Volume": "106", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Metallurgy and Materials, University of Birmingham, Edgbaston, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The Manufacturing Technology Centre, Coventry, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The Manufacturing Technology Centre, Coventry, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The Manufacturing Technology Centre, Coventry, UK"}, {"AuthorId": 5, "Name": "Moataz M<PERSON>", "Affiliation": "School of Metallurgy and Materials, University of Birmingham, Edgbaston, UK"}], "References": []}, {"ArticleId": 79523328, "Title": "Social media crisis communication in racially charged crises: Exploring the effects of social media and image restoration strategies", "Abstract": "Using a 3 × 4 between-subjects experiment, the present study delineates the effects of social media platforms (Facebook, Twitter, and Instagram) and image restoration strategies (denial, reduction of offensiveness, corrective action, and mortification) on post-crisis reputation evaluations, positive social media engagement, and offline behavioral intentions, in the context of a fictitious hotel company facing a racially charged crisis. The results indicate that Twitter was more effective than Facebook and Instagram in terms of restoring a company&#x27;s post-crisis reputation, triggering positive social media engagement with the message, and enhancing the offline behavioral intentions of users. Irrespective of the social media platform on which the message was disseminated, corrective action proved to be the most engaging strategy, triggering more online reactions such as likes, shares, and positive comments. This study also sheds light on the interplay between the dependent variables (post-crisis reputation, positive social media engagement, and offline behavioral intentions) and social media usage frequency. Important implications for crisis managers facing racially charged crisis are derived.", "Keywords": "Social media ; Crisis communication ; Racially charged crisis ; Image restoration strategies ; Facebook ; Instagram ; Twitter", "DOI": "10.1016/j.chb.2020.106269", "PubYear": 2020, "Volume": "106", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Communication and Digital Media, University of Western Macedonia, Fourka Area, P.O. Box: 30, 5100, Kastoria, Greece;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, University of West Attica, Campus 2, Building D-Office 215, Petrou-Ralli & Thivon 250, 12244, Egaleo, Greece"}], "References": []}, {"ArticleId": 79523329, "Title": "Balancing public bicycle sharing system using inventory critical levels in queuing network", "Abstract": "Public Bicycle Sharing System has recently been developed and installed in many cities as a workable and popular transportation system. There are still some noticeable challenges associated with the operation of the system, like responding to all renting requests and all demands of vacant docks for returning bikes. Balancing the inventory of stations is necessary to minimize the rejected demands of bikes and the empty lockers. Here, critical levels are defined to control requests of different routes in which a demand of a specified destination is accepted if the inventory of the original station is higher than the route’s critical level. The capacity of stations and the fleet size are determined in addition to the different critical levels considering a constraint for the fleet size of the system. After developing the model using the Jackson network, a genetic algorithm is developed to obtain the proper amounts of variables for balancing the inventory of the system as much as possible. Finally, different examples are worked through to evaluate the applicability of the proposed method.", "Keywords": "Public Bicycle Sharing System (PBSS) ; Jackson Network ; Inventory critical levels ; Mean Value Analysis (MVA)", "DOI": "10.1016/j.cie.2020.106277", "PubYear": 2020, "Volume": "141", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Mazandaran University of Science and Technology, Babol, Iran;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Mazandaran University of Science and Technology, Babol, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematical Sciences, Sharif University of Technology, Tehran, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Amirkabir University of Technology, Tehran, Iran"}], "References": []}, {"ArticleId": 79523425, "Title": "Multi-dimensional dynamic trust evaluation scheme for cloud environment", "Abstract": "In cloud computing environment, Cloud Customers (CCs) and Cloud Service Providers (CSPs) require to evaluate the trust levels of potential partner prior to appealing in communications. The accurateness of trust evaluation significantly influences the success velocity of the communication. Determining trustworthiness dynamically is a demanding problem in an open and dynamic environment (such as cloud computing) because of huge number of CSPs offering similar types of services. Also it is a challenging job for both CCs and CSPs to mutually recognize and distinguish between the trustworthy and untrustworthy CSPs and CCs. Presently, there are very less number of dynamic trust evaluation scheme that permits CCs to evaluate the trustworthiness of CSPs from multi-dimensional perspectives (i.e., perspectives from Cloud Auditors (CAs), Cloud Brokers (CBs), Service Level Agreement Agents (SLAAs) and Peers). Similarly, there is no scheme that permits CSPs to evaluate trustworthiness of CCs. This paper proposes a Multi-dimensional Dynamic Trust Evaluation Scheme (MDTES) that facilitates CCs to evaluate the trustworthiness of CSPs from various viewpoints. Similar approach can be employed by CSPs to evaluate the trustworthiness of CCs. The proposed MDTES helps CCs to choose trustworthy CSP which provides desired QoS and CSPs to choose desired and legal CCs. The experimental results illustrate that the MDTES is dynamic, efficient and steady in distinguishing trustworthy and untrustworthy CSPs and CCs compared to other trust models.", "Keywords": "Cloud computing ; Trustworthiness ; Trust ; Dynamic trust evaluation scheme ; Compliance Information", "DOI": "10.1016/j.cose.2020.101722", "PubYear": 2020, "Volume": "91", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>en S. <PERSON>", "Affiliation": "Department of Computer Science and Engineering, Basaveshwar Engineering College, Vidyagiri, Bagalkot 587102, Karnataka, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Visvesvaraya Technological University, Belagavi 590018, Karnataka, India"}], "References": []}, {"ArticleId": 79523439, "Title": "Design, Modelling and Control of Novel Series-Elastic Actuators for Industrial Robots", "Abstract": "<p>This paper describes data-driven modelling methods and their use for the control of a novel set of series-elastic actuators (SEAs). A set of elastic actuators was developed in order to fulfill the end-user needs for tailored industrial collaborative robot manipulators of different morphologies and payloads. Three different types of elastic actuation were investigated, namely, disc springs, coil springs and torsion bars. The developed algorithms were validated both on single actuators and on a 6-DOF robotic arm composed of such actuators.</p>", "Keywords": "series-elastic actuators; human–robot collaboration; modularity; safety series-elastic actuators ; human–robot collaboration ; modularity ; safety", "DOI": "10.3390/act9010006", "PubYear": 2020, "Volume": "9", "Issue": "1", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Robotics Innovation Center at German Research Center for Artificial Intelligence (DFKI), Robert-Hooke Str. 1, 28359 Bremen, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Robotics Innovation Center at German Research Center for Artificial Intelligence (DFKI), Robert-Hooke Str. 1, 28359 Bremen, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Robotics Innovation Center at German Research Center for Artificial Intelligence (DFKI), Robert-Hooke Str. 1, 28359 Bremen, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Robotics Innovation Center at German Research Center for Artificial Intelligence (DFKI), Robert-Hooke Str. 1, 28359 Bremen, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Robotics Innovation Center at German Research Center for Artificial Intelligence (DFKI), Robert-Hooke Str. 1, 28359 Bremen, Germany"}], "References": []}, {"ArticleId": 79523509, "Title": "On the Optimal Control of a Rolling Ball Robot Actuated by Internal Point Masses", "Abstract": "Abstract <p>The controlled motion of a rolling ball actuated by internal point masses that move along arbitrarily-shaped rails fixed within the ball is considered. Application of the variational Pont<PERSON><PERSON>'s minimum principle yields the ball's controlled equations of motion, a solution of which obeys the ball's uncontrolled equations of motion, satisfies prescribed initial and final conditions, and minimizes a prescribed performance index.</p>", "Keywords": "optimal control ; rolling ball robots", "DOI": "10.1115/1.4046104", "PubYear": 2020, "Volume": "142", "Issue": "5", "JournalId": 9493, "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control", "ISSN": "0022-0434", "EISSN": "1528-9028", "Authors": [{"AuthorId": 1, "Name": "Vakhta<PERSON>", "Affiliation": "Department of Mathematical and Statistical Sciences, Faculty of Science, University of Alberta, CAB 632, Edmonton, AB T6G 2G1, Canada; ATCO SpaceLab, 5302 Forand St. SW, Calgary, AB T3E 8B4, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Mathematics and its Applications, College of Science and Engineering, University of Minnesota, 207 Church St. SE, 306 Lind Hall, Minneapolis, MN 55455"}], "References": [{"Title": "On the Optimal Control of a Rolling Ball Robot Actuated by Internal Point Masses", "Authors": "<PERSON>ak<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "5", "Page": "", "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control"}]}, {"ArticleId": 79523517, "Title": "Microfluidic Jetting Deformation and Pinching-off Mechanism in Capillary Tubes by Using Traveling Surface Acoustic Waves", "Abstract": "<p>To date, there has been little research attention paid to jetting deformation and pinching-off of microfluidic flows induced by the surface acoustic wave (SAW) mechanism. Further, such studies were almost limited to one sessile drop actuation without any confinement mechanisms. Such a scenario is likely attributable to the mechanism’s relatively poor controllability, the difficulty of maintaining the fluid loading position and issues related to stability and repeatability. In this paper, a novel SAW-microfluidic jetting system with a vertical capillary tube was designed, accompanied by a large number of experiments investigating the single droplet jetting mechanism with different device dimensions, resonance frequencies and radio frequency (RF) power capabilities. The study began with the whole jetting deformation and droplet pinching off through the use of a microscope with a high-speed camera, after which the results were discussed to explain the droplet jetting mechanism in a vertical capillary tube. After that, the study continued with experimental and theoretical examinations for high-quality single droplet jetting conditions. Jetting characterization parameters, including threshold RF power, resonance frequency, liquid volume, pinching off droplet dimensions, were thoroughly analyzed. Lastly, the Weber number range, a significant parameter in SAW-microfluidic jetting, was verified, and the pinching off microdroplet dimension was analyzed and compared via experiments. The significance of this study lies in the realization of microfluidic drop-on-demand based on SAW technology.</p>", "Keywords": "surface acoustic wave; jetting; pinch-off; drop-on-demand surface acoustic wave ; jetting ; pinch-off ; drop-on-demand", "DOI": "10.3390/act9010005", "PubYear": 2020, "Volume": "9", "Issue": "1", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Harbin Institute of Technology, Shenzhen 518055, China"}, {"AuthorId": 2, "Name": "Hong Hu", "Affiliation": "School of Mechanical Engineering and Automation, Harbin Institute of Technology, Shenzhen 518055, China ↑ Author to whom correspondence should be addressed"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Harbin Institute of Technology, Shenzhen 518055, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Harbin Institute of Technology, Shenzhen 518055, China"}], "References": []}, {"ArticleId": 79523531, "Title": "Automatic kernel code synthesis and verification", "Abstract": "Formal verification of an OS kernel is widely considered a major challenge; however, traditional interactive theorem provers used in OS kernel verification require manually written proofs and come with a non-trivial cost. In this paper, we propose a formal verification framework to build a verifiably correct OS kernel, named iv6, with a high degree of proof automation and a low burden of proof. Using this framework, programmers only need to write the specification as required, and it can be translated into the corresponding implementation of C code automatically. The verification framework can guarantee that the behaviour of the implementation code adheres to its specifications. Iv6 introduces four key ideas to achieve proof automation: its interfaces and corresponding specifications are designed to be finite to avoid unbounded loops or recursion; it separates kernel and user address spaces using a kernel page table isolation approach to simplify reasoning about virtual memory; it partitions the modules of a kernel state machine according to a state transition function to improve verification performance; and to avoid modelling complicated C semantics, it performs verification at the LLVM intermediate representation level. A total of 48 system calls and some high-level system properties in iv6 have been verified using this method. Experience shows that this framework can reduce the impact of human error on kernel development and make the verification of iv6 more efficient and straightforward.", "Keywords": "Operating system ; Formal verification ; Functional correctness ; Symbolic execution ; RISCV ; Code synthesis", "DOI": "10.1016/j.cose.2020.101733", "PubYear": 2020, "Volume": "91", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, Zhihui Main Street No. 500, Hun <PERSON>, ShenYang, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Northeastern University, Zhihui Main Street No. 500, Hun Nan, ShenYang, China"}, {"AuthorId": 3, "Name": "Qingyang Meng", "Affiliation": "Shenyang GoldenRiver Technology Co., Ltd., Room No. 400-13 (2-401) of Zhihui Second Street, Hun Nan, ShenYang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Tsinghua University, Shuang Qing Road No. 30, Hai Dian, Beijing, China"}], "References": []}, {"ArticleId": 79523860, "Title": "Dual band frequency tunable planar inverted‐F antenna for mobile handheld devices", "Abstract": "<p>A single feed, dual‐band frequency tunable planar inverted‐F antenna (PIFA) is presented for mobile handheld device applications. The proposed antenna is designed using the transmission line model. The dual‐band frequency tunability is achieved by varying the capacitance of the varactor diode between 4.15 pF (0 V) and 0.72 pF (15 V). The measured impedance bandwidth of −6 dB is realized from 0.8 to 0.98 GHz for the lower band and 1.65 to 2.2 GHz for the higher band. The designed antenna provides the independent frequency tunability for both the bands without disturbing each other. The maximum antenna gain is estimated 2.64 dBi for the proposed PIFA. Also, it has a maximum efficiency of ~85% for the mobile handheld device. In addition, the proposed PIFA is investigated with SAM phantom model for head and hand, found to be within the acceptable SAR limit of 1.6 W/Kg.</p>", "Keywords": "dual‐band antenna;frequency tuning;PIFA", "DOI": "10.1002/mmce.22150", "PubYear": 2020, "Volume": "30", "Issue": "5", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Communication Engineering, School of Electronics Engineering, Vellore Institute of Technology (VIT), Vellore, Tamil Nadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Communication Engineering, School of Electronics Engineering, Vellore Institute of Technology (VIT), Vellore, Tamil Nadu, India"}], "References": []}, {"ArticleId": 79524009, "Title": "Deterministic and reliability-based lightweight design of Timoshenko composite beams", "Abstract": "<p>In this article, the lightweight design problems of <PERSON>oshenko composite beams with and without considering the uncertainties of input data (i.e., the geometry of beam, material properties and applied load) are studied. To deal with the deterministic optimization problems, an improved version of Jaya algorithm (called iJaya), which is able to effectively handle the optimization problems with both continuous and discrete design variables, is applied. Then, a novel combination of iJaya and the single-loop deterministic method (SLDM) (called SLDM–iJaya) is developed to solve the reliability-based lightweight design problems. A laminated composite beam of eight layers with various boundary conditions is studied. With regard to the deterministic optimization problems, numerical results reveal that iJaya outperforms some other methods, such as Jaya, differential evolution and improved differential evolution, and has good ability in handling the optimization problems with both discrete and continuous design variables. Regarding the reliability-based lightweight design problems, numerical experiments show that SLDM–iJaya can generate reliability-based optimal solutions when the uncertainties of input data of the beam are taken into account, and in comparison with other approaches, SLDM–iJaya is much better in terms of computational cost while keeping almost the same quality of optimal results.</p>", "Keywords": "<PERSON><PERSON>enko composite beam; Lightweight design; Reliability-based design optimization; Single-loop deterministic method; Jaya algorithm; Reliability analysis", "DOI": "10.1007/s00366-020-00946-8", "PubYear": 2021, "Volume": "37", "Issue": "3", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Civil Engineering, Ho Chi Minh City University of Technology and Education, Ho Chi Minh City, Vietnam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Computational Mathematics and Engineering, Institute for Computational Science, Ton Duc Thang University, Ho Chi Minh City, Vietnam;Faculty of Civil Engineering, Ton Duc Thang University, Ho Chi Minh City, Vietnam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electronics Technology, Industrial University of Ho Chi Minh City, Ho Chi Minh City, Vietnam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Civil Engineering, Ho Chi Minh City University of Technology and Education, Ho Chi Minh City, Vietnam"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Division of Computational Mathematics and Engineering, Institute for Computational Science, Ton Duc Thang University, Ho Chi Minh City, Vietnam;Faculty of Civil Engineering, Ton Duc Thang University, Ho Chi Minh City, Vietnam"}], "References": []}, {"ArticleId": 79524118, "Title": "A high-capacity performance-preserving blind technique for reversible information hiding via MIDI files using delta times", "Abstract": "<p>A new high-capacity information hiding method for embedding secret messages into MIDI files is proposed. The method can preserve the original musical performance of the cover MIDI file. The property of the variable-length quantity, which expresses the magnitude of the delta time before every event in a MIDI file, is utilized for secret bit embedding. The embedding is accomplished by padding the delta times with different numbers of leading constant bytes of 80<sub>16</sub> to represent the secret bits. The method is both reversible and blind because the original cover MIDI file can be restored completely from the stego-MIDI file by extracting the embedded data out from the resulting stego-MIDI file without referencing the original cover MIDI file. A capability of hiding a large amount of secret information is achieved since the delta time is a basic parameter that appears before every event in the MIDI file. Good experimental results yielded by the proposed method as well as a comparison of the method with five existing performance-preserving methods from the viewpoints of stego-file quality, payload capacity, and data security show the superiority and feasibility of the proposed method.</p>", "Keywords": "Information hiding; MIDI files; Delta time; Variable length quantity", "DOI": "10.1007/s11042-019-08526-9", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Communication Engineering, National Kaohsiung University of Science and Technology, Kaohsiung City, Taiwan, Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Communication Engineering, National Kaohsiung University of Science and Technology, Kaohsiung City, Taiwan, Republic of China"}], "References": []}, {"ArticleId": 79524154, "Title": "Nonlocal Elastica Model for Sparse Reconstruction", "Abstract": "<p>In view of the exceptional ability of curvature in connecting missing edges and structures, we propose novel sparse reconstruction models via the Euler’s elastica energy. In particular, we firstly extend the Euler’s elastica regularity into the nonlocal formulation to fully take the advantages of the pattern redundancy and structural similarity in image data. Due to its non-convexity, non-smoothness and nonlinearity, we regard both local and nonlocal elastica functional as the weighted total variation for a good trade-off between the runtime complexity and performance. The splitting techniques and alternating direction method of multipliers (ADMM) are used to achieve efficient algorithms, the convergence of which is also discussed under certain assumptions. The weighting function occurred in our model can be well estimated according to the local approach. Numerical experiments demonstrate that our nonlocal elastica model achieves the state-of-the-art reconstruction results for different sampling patterns and sampling ratios, especially when the sampling rate is extremely low.</p>", "Keywords": "<PERSON><PERSON><PERSON>’s elastica; Nonlocal regularization; Sparse reconstruction; ADMM", "DOI": "10.1007/s10851-019-00943-7", "PubYear": 2020, "Volume": "62", "Issue": "4", "JournalId": 6119, "JournalTitle": "Journal of Mathematical Imaging and Vision", "ISSN": "0924-9907", "EISSN": "1573-7683", "Authors": [{"AuthorId": 1, "Name": "Men<PERSON><PERSON> Yan", "Affiliation": "The Center for Applied Mathematics, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The Center for Applied Mathematics, Tianjin University, Tianjin, China"}], "References": []}, {"ArticleId": 79524264, "Title": "Carcinogenic effect of potassium bromate on tongue of adult male albino rats", "Abstract": "Potassium bromate (KBrO<sub>3</sub>), a strong oxidizing agent, is mainly used as a flour improver; it is classified as B2 (probable human) carcinogen and health organizations start banning it. However, it is still used in bakeries in large amounts. The aim of this study was to investigate the effect of KBrO<sub>3</sub> administration on the tongue of adult male albino rats. Sixty adult male albino rats weighting ~200 gm were divided equally into threegroups: control, experimental group I (gp I), and experimental group II (gp II). Control group received distilled water and the experimental gps. received 62 and 123 mg\\kg KBrO<sub>3</sub>, respectively, dissolved in distilled water orally on a daily basis for 2 and 4 months. Histopathological examination of specimens in experimental groups revealed dysplastic and carcinogenic changes that were dose- and time-dependent to end in the epithelial invasion of connective tissue. The immunohistochemical results showed a significant increase in the immunoreactivity of PCNA in KBrO<sub>3</sub> treated groups compared to the control group. In conclusion, KBrO<sub>3</sub> long-term administration has a carcinogenic effect, so it is considered as a risk on public health.", "Keywords": "Potassium bromate ; KBrO<sub>3</sub> ; tongue ; carcinogenicity", "DOI": "10.1080/16878507.2020.1713584", "PubYear": 2020, "Volume": "13", "Issue": "1", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Han<PERSON>", "Affiliation": "Oral Biology Department, Faculty of Dentistry, Minia University, Minya, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Oral Biology Department, Faculty of Dentistry, Cairo University, Giza, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Oral Biology Department, Faculty of Dentistry, Minia University, Minya, Egypt"}], "References": []}, {"ArticleId": 79524311, "Title": "Interpretation Method of Transformation Matrix Clarifying Relationships between Impression Factors of Multimedia Data", "Abstract": "<p > <p>This paper proposes a method of interpreting a transformation matrix of the factor loading matrixes obtained through factor analysis often used in affective engineering. A transformation matrix transforms a factor loading matrix of a data set to another one of another data set, and vice versa. It represents the relationship between two sets of factors. A transformation matrix is decomposed into a rotation matrix and a mapping one. It is shown that these two matrixes could be decided by specifying a non-corresponding factor in the factors of two data sets. This enables us to easily interpret the meaning of the transformation matrix.</p> </p>", "Keywords": "Factor analysis;Multimedia data;Factor loading matrix;Transformation;Matrix", "DOI": "10.5057/ijae.IJAE-D-19-00010", "PubYear": 2020, "Volume": "19", "Issue": "2", "JournalId": 24153, "JournalTitle": "International Journal of Affective Engineering", "ISSN": "", "EISSN": "2187-5413", "Authors": [{"AuthorId": 1, "Name": "Teruhisa HOCHIN", "Affiliation": "Kyoto Institute of Technology"}, {"AuthorId": 2, "Name": "Tomoki MAEDA", "Affiliation": "Graduate School of Information Science, Kyoto Institute of Technology"}], "References": []}, {"ArticleId": 79524387, "Title": "IoTP an Efficient Privacy Preserving Scheme for Internet of Things Environment", "Abstract": "<p>Many emerging fields are adopting Internet of Things technologies to incorporate smartness in respective areas. Several IoT based application area produces large volumes of real time data. Data aggregated through sensor nodes may contain highly sensitive information. An effective and successful IoT system must protect sensitive data from revealing to unauthorized persons. In this article, the authors present an efficient privacy-preserving mechanism called Internet of Things privacy (IoTp). The research simulates and analyzes the effectiveness of the proposed data aggregation and data access mechanism for a typical IoT system. Proposed IoTp scheme ensures privacy at data collection, data store and data access phases of the IoT system. The authors have compared proposed work with existing model. Results show that IoTp scheme is efficient and lightweight mechanism for data collection and data access. It is suitable for the resource constrained IoT ecosystems.</p>", "Keywords": "", "DOI": "10.4018/IJISP.2020040107", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 13525, "JournalTitle": "International Journal of Information Security and Privacy", "ISSN": "1930-1650", "EISSN": "1930-1669", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Central University of Rajasthan, NH-8, Bandar Sindri, Ajmer, 305817, Rajasthan, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Central University of Rajasthan, NH-8, Bandar Sindri, Ajmer, 305817, Rajasthan, India"}], "References": []}, {"ArticleId": 79524499, "Title": "Using argumentation schemes to find motives and intentions of a rational agent", "Abstract": "Because motives and intentions are internal, and not directly observable by another agent, it has always been a problem to find a pathway of reasoning linking them to externally observable evidence. This paper proposes an argumentation-based method that one can use to support or attack hypotheses about the motives or intentions of an intelligent autonomous agent based on verifiable evidence. The method is based on a dialectical argumentation approach along with a commitment-based theory of mind. It is implemented using the Carneades Argumentation System, which already has 106 programmed schemes available to it and has an argument search tool. The method uses schemes, notably ones for abductive reasoning, argument from action to intention, argument from action to motive, and some new ones.", "Keywords": "Multiagent systems; evidential reasoning in law; finding intentions; artificial intelligence", "DOI": "10.3233/AAC-190480", "PubYear": 2020, "Volume": "10", "Issue": "3", "JournalId": 17289, "JournalTitle": "Argument & Computation", "ISSN": "1946-2166", "EISSN": "1946-2174", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for Research on Reasoning, Argumentation and Rhetoric, University of Windsor, Windsor, Canada"}], "References": []}, {"ArticleId": 79524513, "Title": "Intrusion Detection Systems for Mitigating SQL Injection Attacks", "Abstract": "<p>Databases are widely used by organizations to store business-critical information, which makes them one of the most attractive targets for security attacks. SQL Injection is the most common attack to webpages with dynamic content. To mitigate it, organizations use Intrusion Detection Systems (IDS) as part of the security infrastructure, to detect this type of attack. However, the authors observe a gap between the comprehensive state-of-the-art in detecting SQL Injection attacks and the state-of-practice regarding existing tools capable of detecting such attacks. The majority of IDS implementations provide little or no protection against SQL Injection attacks, with exceptions like the tools Bro and ModSecurity. In this article, the authors compare these tools using the CSIC dataset in order to examine the state-of-practice in database protection from SQL Injection attacks, identifying the main characteristics and implementation details needed for IDSs to successfully detect such attacks. The experiments indicate that signature-based IDS provide the greatest coverage against SQL Injection.</p>", "Keywords": "", "DOI": "10.4018/IJISP.2020040102", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 13525, "JournalTitle": "International Journal of Information Security and Privacy", "ISSN": "1930-1650", "EISSN": "1930-1669", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Polytechnic of Coimbra - ISEC, Coimbra, Portugal"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Coimbra - UC Center for Informatics and Systems of University of Coimbra, Coimbra, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Polytechnic of Coimbra - ISEC, Coimbra, Portugal"}], "References": []}, {"ArticleId": 79524536, "Title": "Adaptive Pre/Post-Compensation of Cascade Filters in Coherent Optical Transponders", "Abstract": "<p>We propose an adaptive joint pre- and post- compensation to correct the filtering effects caused by cascading reconfigurable optical add drop multiplexers (ROADMs). The improvement is achieved without using additional hardware (HW) on the link or within the signal processor in the transponders. Using Monte Carlo simulations, the gradient-descent based method shows an improvement of 0.6 dB and 1.1 dB in the required optical signal-to-noise ratio (R-OSNR) at the threshold pre-decoder bit error rate (BER) of 0.02 versus pre-compensation only in the linear and nonlinear operating region of fiber respectively. We experimentally verified the method with lab measurements in the presence of heavy filtering and optical impairments. We observed a gain up to ~0.4 dB compared to typically used pre-compensation only. Additionally, other tangible system benefits of our method are listed and discussed.</p>", "Keywords": "optical communications; filtering effects; long-haul; pre-emphasis; coherent communications; joint-optimization optical communications ; filtering effects ; long-haul ; pre-emphasis ; coherent communications ; joint-optimization", "DOI": "10.3390/fi12020021", "PubYear": 2020, "Volume": "12", "Issue": "2", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, University of Ottawa, Ottawa, ON K1N 6N5, Canada ↑ Author to whom correspondence should be addressed"}], "References": []}, {"ArticleId": 79524554, "Title": "Aggregate Searchable Encryption With Result Privacy", "Abstract": "<p>With searchable encryption (SE), the user is allowed to extract partial data from stored ciphertexts from the storage server, based on a chosen query of keywords. A majority of the existing SE schemes support SQL search query, i.e. 'Select * where (list of keywords).' However, applications for encrypted data analysis often need to count data matched with a query, instead of data extraction. For such applications, the execution of SQL aggregate query, i.e. 'Count * where (list of keywords)' at server is essential. Additionally, in case of semi-honest server, privacy of aggregate result is of primary concern. In this article, the authors propose an aggregate searchable encryption with result privacy (ASE-RP) that includes ASearch() algorithm. The proposed ASearch() performs aggregate operation (i.e. Count *) on the implicitly searched ciphertexts (for the conjunctive query) and outputs an encrypted result. The server, due to encrypted form of aggregate result, would not be able to get actual count unless having a decryption key and hence ASearch() offers result privacy.</p>", "Keywords": "", "DOI": "10.4018/IJISP.2020040104", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 13525, "JournalTitle": "International Journal of Information Security and Privacy", "ISSN": "1930-1650", "EISSN": "1930-1669", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "S.V. National Institute of Technology, Surat, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "S.V. National Institute of Technology, Surat, India"}], "References": []}, {"ArticleId": 79524583, "Title": "Fabrication of single crystalline WO3 nano-belts based photoelectric gas sensor for detection of high concentration ethanol gas at room temperature", "Abstract": "WO<sub>3</sub> gas sensing material was synthesized by hydrothermal process, exhibiting a belt-like surface morphology. By comparing and analysing the UV–vis absorption spectra, it is shown that the band gap of WO<sub>3</sub> nano-belts (NBs) is 0.56 eV larger than that of WO<sub>3</sub> nano-particles (NPs). Surface photo-voltage (SPV) spectra and SPV phase spectra indicate that the direction of the surface built-in electric field is from the surface to the bulk of WO<sub>3</sub> NBs and the surface band bending is upwards. WO<sub>3</sub> NBs based photoelectric gas sensor was assembled for detecting high concentration of C<sub>2</sub>H<sub>5</sub>OH at room temperature. The results demonstrate that the WO<sub>3</sub> NBs based photoelectric sensor exhibits higher sensitivity and resolution to high concentration (1000 ppm–20000 ppm) of ethanol at room temperature than WO<sub>3</sub> NPs based photoelectric sensor. WO<sub>3</sub> NBs are promising candidates of gas sensing material used to fabricate photoelectric gas sensor for high concentration C<sub>2</sub>H<sub>5</sub>OH detection operating at room temperature.", "Keywords": "WO<sub>3</sub>nano-belts ; Photoelectric gas sensor ; Room temperature ; High concentration ethanol gas", "DOI": "10.1016/j.sna.2020.111865", "PubYear": 2020, "Volume": "303", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Marine Materials and Smart Sensors, Minjiang University, Fuzhou 350108, PR China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center for Advanced Marine Materials and Smart Sensors, Minjiang University, Fuzhou 350108, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Marine Materials and Smart Sensors, Minjiang University, Fuzhou 350108, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Marine Materials and Smart Sensors, Minjiang University, Fuzhou 350108, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Marine Materials and Smart Sensors, Minjiang University, Fuzhou 350108, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Marine Materials and Smart Sensors, Minjiang University, Fuzhou 350108, PR China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Center for Advanced Marine Materials and Smart Sensors, Minjiang University, Fuzhou 350108, PR China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Marine Materials and Smart Sensors, Minjiang University, Fuzhou 350108, PR China"}], "References": []}, {"ArticleId": 79524652, "Title": "New frontiers against antibiotic resistance: A Raman-based approach for rapid detection of bacterial susceptibility and biocide-induced antibiotic cross-tolerance", "Abstract": "To overcome the widespread misuse of antibiotics and reduce the growing problem of multidrug resistance, rapid, sensitive and specific novel methods to determine bacterial antibiotic susceptibility are needed. This study presents a combined dielectrophoresis (DEP) - Raman Spectroscopy (RS) method to obtain direct, real-time measurements of the susceptibility of a suspension of planktonic bacteria without labelling or other time-consuming and intrusive sample preparation processes. Using an in-house constructed DEP-Raman device, we demonstrated the susceptibility of Escherichia coli MG1655 towards the second-generation fluoroquinolone antibiotic ciprofloxacin (CP) after only 1 h of treatment, by monitoring spectral changes in the chemical fingerprint of bacteria related to the mode of action of the drug. Spectral variance was modelled by multivariate techniques and a classification model was calculated to determine bacterial viability in the exponential growth phase at the minimum bactericidal concentration (MBC) over a 3 h time span. Further tests at a sub-minimum inhibitory concentration (MIC) and with a CP-resistant E. coli were carried out to validate the model. The method was then successfully applied for class prediction in a biocide cross-induced tolerance assay that involved pre-treating E. coli with triclosan (TCS), an antimicrobial used in many consumer products. High specificity and adequate sensitivity of the proposed method was thereby demonstrated. Simultaneously, standard microbiological assays based on cell viability, turbidity and fluorescence microscopy, were carried out as reference methods to confirm the observed Raman response.", "Keywords": "Raman spectroscopy ; Dielectrophoresis ; E. coli ; Antibiotic resistance ; Ciprofloxacin ; Antibiotic susceptibility test", "DOI": "10.1016/j.snb.2020.127774", "PubYear": 2020, "Volume": "309", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Quantum Metrology and Nano Technologies Division, Istituto Nazionale di Ricerca Metrologica (INRiM), Strada delle Cacce, 91, 10135 Turin, Italy;Department of Electronics and Telecommunications, Politecnico di Torino, Corso Duca degli Abruzzi, 24, 10129 Turin, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Quantum Metrology and Nano Technologies Division, Istituto Nazionale di Ricerca Metrologica (INRiM), Strada delle Cacce, 91, 10135 Turin, Italy;Department of Electronics and Telecommunications, Politecnico di Torino, Corso Duca degli Abruzzi, 24, 10129 Turin, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Quantum Metrology and Nano Technologies Division, Istituto Nazionale di Ricerca Metrologica (INRiM), Strada delle Cacce, 91, 10135 Turin, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Quantum Metrology and Nano Technologies Division, Istituto Nazionale di Ricerca Metrologica (INRiM), Strada delle Cacce, 91, 10135 Turin, Italy;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Life Sciences, Centre for Biomolecular Sciences, University of Nottingham, Nottingham NG7 2RD, UK"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Quantum Metrology and Nano Technologies Division, Istituto Nazionale di Ricerca Metrologica (INRiM), Strada delle Cacce, 91, 10135 Turin, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Pharmacy, University of Nottingham, Nottingham NG7 2RD, UK"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Life Sciences, Centre for Biomolecular Sciences, University of Nottingham, Nottingham NG7 2RD, UK"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "School of Life Sciences, Centre for Biomolecular Sciences, University of Nottingham, Nottingham NG7 2RD, UK"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Quantum Metrology and Nano Technologies Division, Istituto Nazionale di Ricerca Metrologica (INRiM), Strada delle Cacce, 91, 10135 Turin, Italy"}], "References": []}, {"ArticleId": 79524661, "Title": "High-precision human body acquisition via multi-view binocular stereopsis", "Abstract": "It remains challenging how to acquire a human body shape with high precision and evaluate the reconstructed models effectively, because the results can be easily affected by various factors (e.g., the performance of the capture device, the unwanted movement of the subject, and the self-occlusion of the articulated body structure). To tackle the above challenges, this research presents a passive acquisition system, which comprises 60 spatially-configured Digital Single Lens Reflex (DSLR) cameras and a carefully devised algorithmic pipeline for shape acquisition in a single shot. Different from traditional multi-view stereo solutions, the constituent cameras are synchronized and organized into 30 binocular stereo rigs to capture images from multiple views simultaneously. Each binocular stereo rig is regarded as a depth sensor. The acquisition pipeline consists of three stages. First, camera calibration is performed to estimate intrinsic and extrinsic parameters of all cameras, especially for paired binocular cameras. Second, depth inference based on stereo matching is employed to recover reliable depth information from RGB images. A novel hierarchical seed-propagation stereo matching framework is proposed, resulting in 30 dense and uniform-distributed partial point clouds. Finally, a point-based geometry processing step composed of multi-view registration and surface meshing is carried out to obtain high-quality watertight human body shapes. This research also proposes an elaborate and novel method to assess the accuracy of reconstructed non-rigid human body model based on anthropometry parameters, which solves the synchronization of the ground-truth values and the measured values. Experimental results show that the system can achieve the reconstruction accuracy within 2.5 mm in average.", "Keywords": "Human body modeling ; Acquisition system ; Stereo matching ; Anthropometry", "DOI": "10.1016/j.cag.2020.01.003", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 3909, "JournalTitle": "Computers & Graphics", "ISSN": "0097-8493", "EISSN": "1873-7684", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou 310058, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou 310058, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Bath, Bath BA2 7AY, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou 310058, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou 310058, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Tang", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou 310058, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON> Feng", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou 310058, China;Corresponding author"}], "References": []}, {"ArticleId": 79524683, "Title": "A logic for Lawson compact algebraic L-domains", "Abstract": "In this paper, we build a logic which is named N-sequent calculus. Based on this logic, we provide two kinds of logical representations of Lawson compact algebraic L-domains: one in terms of logical algebras and the other in terms of logical syntax. The first representation takes the corresponding logical algebras as research objects. The use of prime filters achieves the connection between our logic and Lawson compact algebraic L-domains. This approach is inspired by <PERSON><PERSON>'s SFP domain logic and the disjunctive propositional logic on algebraic L-domains introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. However, there are essential differences between them at the morphisms part. For the second representation, we directly adopt N-sequent calculi themselves as objects instead of the logical algebras. Then we establish the category of N-sequent calculi with consequence relations equivalent to that of Lawson compact algebraic L-domains with Scott continuous maps. This demonstrates the capability of the syntax of the logic in representing domains.", "Keywords": "Domain theory ; Lawson compact algebraic L-domains ; Domain logic ; N-sequent calculus ; FD-lattice", "DOI": "10.1016/j.tcs.2020.01.025", "PubYear": 2020, "Volume": "813", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Econometrics, Hunan University, Changsha, 410082, China;School of Mathematical Sciences, Qufu Normal University, Qufu, 273165, China"}, {"AuthorId": 2, "Name": "Qingguo Li", "Affiliation": "College of Mathematics and Econometrics, Hunan University, Changsha, 410082, China;Corresponding author"}], "References": [{"Title": "A representation of proper BC domains based on conjunctive sequent calculi", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "1", "Page": "1", "JournalTitle": "Mathematical Structures in Computer Science"}]}, {"ArticleId": 79524762, "Title": "Imaging of the mutual regulation between zinc cation and nitrosyl via two-photon fluorescent probes in cells and in vivo", "Abstract": "The homeostatic disorder of intracellular Zn<sup>2+</sup> pool is closely associated with severe diseases. It has been reported that the high level of free Zn<sup>2+</sup> during ischemia/reperfusion (I/R) process can result in oxidative stress damage on nerve cells. Given that nitrosyl (HNO) can aggravate the nerve injury during cerebral I/R process, we assume that there may exist a mutual regulation between Zn<sup>2+</sup> and HNO under certain physiological conditions. To reveal this potential small-signaling-molecule crosstalk, we synthesized two-photon fluorescent probes CHP-H and CHP-CH<sub>3</sub> to monitor intracellular Zn<sup>2+</sup> in cell and mice hippocampus I/R models. The probes consist of two moieties: coumarin derivative as the two-photon fluorescence transducer, 2-hydrazino pyridine as the fluorescence modulator and Zn<sup>2+</sup> chelator. Both probes exhibit excellent analytical properties for Zn<sup>2+</sup> detection in simulated physiological systems. Utilizing CHP-H and an HNO probe Cyto-JN , we perform fluorescent imaging of cell I/R models. The results confirm that HNO can stimulate Zn<sup>2+</sup> release from labile Zn<sup>2+</sup> pool, whereas, the increase of intracellular Zn<sup>2+</sup> cannot upregulate the level of HNO. Combining with the deep tissue imaging of mice hippocampus tissues, our probes may provide potential approaches for the medical diagnostic assessment of HNO regulation effect on Zn<sup>2+</sup> release in clinical cerebral I/R-related diseases.", "Keywords": "Fluorescent probes ; Two-photon ; Zinc cation ; Nitrosyl ; Cell imaging ; In vivo imaging", "DOI": "10.1016/j.snb.2020.127772", "PubYear": 2020, "Volume": "309", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Key Laboratory of Life-Organic Analysis, Key Laboratory of Pharmaceutical Intermediates and Analysis of Natural Medicine, College of Chemistry and Chemical Engineering, Qufu Normal University, Qufu 273165, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Functional Materials and Molecular Imaging, Key Laboratory of Emergency and Trauma, Ministry of Education, Key Laboratory of Hainan Trauma and Disaster Rescue, College of Clinical Medicine, College of Emergency and Trauma, Hainan Medical University, Haikou 571199, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Functional Materials and Molecular Imaging, Key Laboratory of Emergency and Trauma, Ministry of Education, Key Laboratory of Hainan Trauma and Disaster Rescue, College of Clinical Medicine, College of Emergency and Trauma, Hainan Medical University, Haikou 571199, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The Key Laboratory of Life-Organic Analysis, Key Laboratory of Pharmaceutical Intermediates and Analysis of Natural Medicine, College of Chemistry and Chemical Engineering, Qufu Normal University, Qufu 273165, China;Corresponding authors at: The Key Laboratory of Life-Organic Analysis, Key Laboratory of Pharmaceutical Intermediates and Analysis of Natural Medicine, College of Chemistry and Chemical Engineering, Qufu Normal University, Qufu 273165, China"}, {"AuthorId": 5, "Name": "Jin<PERSON>o You", "Affiliation": "The Key Laboratory of Life-Organic Analysis, Key Laboratory of Pharmaceutical Intermediates and Analysis of Natural Medicine, College of Chemistry and Chemical Engineering, Qufu Normal University, Qufu 273165, China;Corresponding authors at: The Key Laboratory of Life-Organic Analysis, Key Laboratory of Pharmaceutical Intermediates and Analysis of Natural Medicine, College of Chemistry and Chemical Engineering, Qufu Normal University, Qufu 273165, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Key Laboratory of Life-Organic Analysis, Key Laboratory of Pharmaceutical Intermediates and Analysis of Natural Medicine, College of Chemistry and Chemical Engineering, Qufu Normal University, Qufu 273165, China;Institute of Functional Materials and Molecular Imaging, Key Laboratory of Emergency and Trauma, Ministry of Education, Key Laboratory of Hainan Trauma and Disaster Rescue, College of Clinical Medicine, College of Emergency and Trauma, Hainan Medical University, Haikou 571199, China;Corresponding authors at: The Key Laboratory of Life-Organic Analysis, Key Laboratory of Pharmaceutical Intermediates and Analysis of Natural Medicine, College of Chemistry and Chemical Engineering, Qufu Normal University, Qufu 273165, China"}], "References": []}, {"ArticleId": 79524790, "Title": "Are you sure your software will not kill anyone?", "Abstract": "Using software to control potentially unsafe systems requires the use of new software and system engineering approaches.", "Keywords": "", "DOI": "10.1145/3376127", "PubYear": 2020, "Volume": "63", "Issue": "2", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology (MIT), Cambridge, MA"}], "References": []}, {"ArticleId": 79524795, "Title": "Tracking shoppers", "Abstract": "Retailers of all stripes are using technology to follow consumers through their brick-and-mortar stores in order to develop detailed profiles of their shopping habits and preferences.", "Keywords": "", "DOI": "10.1145/3374876", "PubYear": 2020, "Volume": "63", "Issue": "2", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "4K Research &amp; Consulting, LLC"}], "References": []}, {"ArticleId": 79524799, "Title": "Automating visual privacy protection using a smart LED", "Abstract": "The ubiquity of mobile camera devices has been triggering an outcry of privacy concerns, whereas existing privacy protection solutions still rely on the cooperation of the photographer or camera hardware, which can hardly be enforced in practice. In this paper, we introduce LiShield, which automatically protects a physical scene against photographing, by illuminating it with smart LEDs flickering in specialized waveforms. We use a model-driven approach to optimize the waveform design, so as to ensure protection against the (uncontrollable) cameras and potential image-processing-based attacks. We have also designed mechanisms to unblock authorized cameras and enable graceful degradation under strong ambient light interference. Our prototype implementation and experiments show that LiShield can effectively destroy unauthorized capturing while maintaining robustness against potential attacks.", "Keywords": "", "DOI": "10.1145/3375571", "PubYear": 2020, "Volume": "63", "Issue": "2", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of California San Diego, CA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of California San Diego, CA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of California San Diego, CA"}], "References": []}, {"ArticleId": 79524803, "Title": "Mining top-k frequent patterns from uncertain databases", "Abstract": "<p>Mining uncertain frequent patterns (UFPs) from uncertain databases was recently introduced, and there are various approaches to solve this problem in the last decade. However, systems are often faced with the problem of too many UFPs being discovered by the traditional approaches to this issue, and thus will spend a lot of time and resources to rank and find the most promising patterns. Therefore, this paper introduces a task named mining top- k UFPs from uncertain databases. We then propose an efficient method named TUFP (mining Top- k UFPs) to carry this out. Effective threshold raising strategies are introduced to help the proposed algorithm reduce the number of generated candidates to enhance the performance in terms of the runtime as well as memory usage. Finally, several experiments on the number of generated candidates, mining time, memory usage and scalability of TUFP and two state-of-the-art approaches (CUFP-mine and LUNA) were conducted. The performance studies show that TUFP is efficient in terms of mining time, memory usage and scalability for mining top- k UFPs.</p>", "Keywords": "Pattern mining; Uncertain frequent pattern; Top-k uncertain frequent patterns", "DOI": "10.1007/s10489-019-01622-1", "PubYear": 2020, "Volume": "50", "Issue": "5", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Research and Development, Duy Tan University, Da Nang, Vietnam"}, {"AuthorId": 2, "Name": "Bay Vo", "Affiliation": "Faculty of Information Technology, Ho Chi Minh City University of Technology (HUTECH), Ho Chi Minh City, Vietnam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Japan Advanced Institute of Science and Technology, Nomi, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Management, Wroclaw University of Science and Technology, Wroclaw, Poland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Digital Contents Research Institute, Sejong University, Seoul, Republic of Korea"}], "References": []}, {"ArticleId": 79524816, "Title": "Learning to trust quantum computers", "Abstract": "They need to show us they can solve the biggest problems.", "Keywords": "", "DOI": "10.1145/3374874", "PubYear": 2020, "Volume": "63", "Issue": "2", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Surrey, U.K"}], "References": []}, {"ArticleId": ********, "Title": "Dark web's doppelgängers aim to dupe antifraud systems", "Abstract": "Digital doppelgängers that fool online payment fraud detection systems are a threat to your bank balance.", "Keywords": "", "DOI": "10.1145/3374878", "PubYear": 2020, "Volume": "63", "Issue": "2", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "London, U.K"}], "References": []}, {"ArticleId": ********, "Title": "Analytical Approaches for Dynamic Scheduling in Cloud Environments", "Abstract": "Brief Biography: <PERSON><PERSON><PERSON> is a researcher in the Computer Laboratory (CompAcctSys group1) at the University of Cambridge, His primary research interests include cloud computing and the Internet of Things (IoT). His current research involves addressing audit logging challenges in IoT environments, online social platforms, and cloud computing environments.", "Keywords": "", "DOI": "10.1145/3380908.3380913", "PubYear": 2020, "Volume": "47", "Issue": "3", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Stony Brook University, Stony Brook, NY, USA"}], "References": []}, {"ArticleId": 79524869, "Title": "Network Centralities as Statistical Inference for Large Networks", "Abstract": "Brief Biography: <PERSON><PERSON> received his Ph.D. degree in Computer Science at City University of Hong Kong (CityU) under the supervision of Dr. <PERSON><PERSON>. Before joining CityU in 2016, <PERSON><PERSON> received M.Sc. in Applied Mathematics at National Chiao Tung University, Taiwan. He was a recipient of the CityU Outstanding Academic Performance Award.", "Keywords": "", "DOI": "10.1145/3380908.3380918", "PubYear": 2020, "Volume": "47", "Issue": "3", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "Pei-Duo YU", "Affiliation": "City University of Hong Kong, Kowloon Tong, Hong Kong"}], "References": []}, {"ArticleId": 79524873, "Title": "CostEfficient Dynamic Management of Cloud Resources through Supervised Learning", "Abstract": "Brief Biography: <PERSON> is a PhD candi- date in the department of Computer Science at Stony Brook University. He works in the Performance Analysis of Com- puter Systems (PACE) Lab, under the supervision of Dr. <PERSON><PERSON><PERSON>. Before joining PACE LAb, he graduated with distinction from Lahore University of Management Sci- ences (LUMS), Pakistan, with an honor thesis that investi- gated performance improvement of cloud gaming using Soft- ware De ned Networking (SDN). His current research fo- cuses on performance modeling and resource management to improving performance and resource utilization in cloud systems. He has worked with leading cloud research groups at IBM Research and AT&T Labs Research on application autoscaling and hotspot mitigation in private clouds.", "Keywords": "", "DOI": "10.1145/3380908.3380917", "PubYear": 2020, "Volume": "47", "Issue": "3", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Stony Brook University, Stony Brook, NY, USA"}], "References": []}, {"ArticleId": 79524877, "Title": "Optimization Algorithms for Large-Scale Systems", "Abstract": "Brief Biography: <PERSON><PERSON> is a fifth-year PhD candidate in Computing and Mathematical Sciences (CMS) at the California Institute of Technology (Caltech), where he is co-advised by <PERSON> and <PERSON><PERSON>, and is a member of multiple research groups: DOLCIT, RSRG and SISL. During the summer of 2019, he was a research scientist intern at Google DeepMind. He received the B.Sc. degree in EE form Sharif University of Technology and the M.Sc. degree in ECE from the University of Southern California, in 2013 and 2015, respectively. His research interests broadly lie in mathematical optimization, machine learning, networks, and markets. He is the recipient of several awards, including the 2016 ACM GreenMetrics Best Student Paper Award, the Amazon Fellowship in Artificial Intelligence, the PIMCO Fellowship in Data Science, the Caltech CMS Fellowship, and the USC Provost's Fellowship.", "Keywords": "", "DOI": "10.1145/3380908.3380910", "PubYear": 2020, "Volume": "47", "Issue": "3", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "California Institute of Technology, Pasadena, CA, USA"}], "References": []}, {"ArticleId": 79524878, "Title": "Defending Distributed Systems Against Adversarial Attacks", "Abstract": "Brief Biography: I am a postdoc in the Computer Sci- ence and Arti cial Intelligence Laboratory (CSAIL) at MIT, hosted by Professor <PERSON>. She received a Ph.D. in Electrical and Computer Engineering from the University of Illinois at Urbana-Champaign in 2017, supervised by <PERSON><PERSON> <PERSON><PERSON>or <PERSON><PERSON>. Her research intersects distributed systems, learning, security, and brain computing. She was the runner-up for the Best Student Paper Award at DISC 2016, and she received the 2015 Best Student Paper Award at SSS 2015. She received UIUC's Sundaram Seshu Interna- tional Student Fellowship for 2016, and was invited to par- ticipate in Rising Stars in EECS (2018). She has served on TPC for several conferences including ICDCS and ICDCN.", "Keywords": "", "DOI": "10.1145/3380908.3380916", "PubYear": 2020, "Volume": "47", "Issue": "3", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, MA, USA"}], "References": []}, {"ArticleId": 79524879, "Title": "Scalable Dynamic Analysis of Browsers for Privacy and Performance", "Abstract": "Brief Biography: <PERSON><PERSON><PERSON> is a fth-year Ph.D. student in Computer Engineering at the University of California, Irvine. His research primarily revolves around performance analysis and privacy on the web including work- load characterization of web browsers, online advertising, and tracking ecosystem. He is interested in designing and developing scalable and low-overhead pro ling tools for pri- vacy and performance measurements. His previous research was in HPC and parallel computing with the focus on GPU acceleration and fault-tolerance. He earned his M.Sc. in Computer Engineering from UCI in 2017 and his B.Sc. in Electrical Engineering from Sharif University in 2015 with honor.", "Keywords": "", "DOI": "10.1145/3380908.3380915", "PubYear": 2020, "Volume": "47", "Issue": "3", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of California, Irvine, CA, USA"}], "References": []}, {"ArticleId": 79524916, "Title": "On durability", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3377424", "PubYear": 2020, "Volume": "63", "Issue": "2", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "Vinton G. Cerf", "Affiliation": "Google"}], "References": []}, {"ArticleId": 79524921, "Title": "Visionbased Sensor Coverage in Uncertain Geometric Domains", "Abstract": "Brief Biography: <PERSON><PERSON> was born in Shandong, China, in 1989. She received the bachelors degree in mathemat- ics and applied mathematics and minor bachelors degree in Finance from the Tianjin University and Nankai University (Tianjin, China) in 2012, and the Master and Ph.D. degrees in Operations Research from SUNY Stony Brook University, Stony Brook, NY, USA in 2014 and 2019, respectively. In 2012, she joined the Department of Applied Mathematics and Statistics (AMS), Stony Brook University, and in 2014 she became a Ph.D. candidate focus on Computational Ge- ometry. Since August 2012, she has been teaching assistant, summer instructor and research assistant in Department of AMS. Her main research is Sensor Networks and Coverage Under Uncertainty.", "Keywords": "", "DOI": "10.1145/3380908.3380914", "PubYear": 2020, "Volume": "47", "Issue": "3", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Stony Brook University, Stony Brook, NY, USA"}], "References": []}, {"ArticleId": 79524922, "Title": "Fuzzing", "Abstract": "Reviewing software testing techniques for finding security vulnerabilities.", "Keywords": "", "DOI": "10.1145/3363824", "PubYear": 2020, "Volume": "63", "Issue": "2", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Research, Redmond, WA"}], "References": []}, {"ArticleId": 79524926, "Title": "Directions for professional social matching systems", "Abstract": "Future PSM systems will require diversity-enhancing yet contextually sensitive designs.", "Keywords": "", "DOI": "10.1145/3363825", "PubYear": 2020, "Volume": "63", "Issue": "2", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Tampere University, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tampere University, Finland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Tampere University, Finland"}], "References": []}, {"ArticleId": 79524934, "Title": "Start small, then achieve big impact", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3377932", "PubYear": 2020, "Volume": "63", "Issue": "2", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 79524974, "Title": "Asymptotically Optimal Load Balancing", "Abstract": "Brief Biography: <PERSON><PERSON><PERSON> is a Ph.D. student at the ECE department of Ohio State University, advised by Prof. <PERSON><PERSON>. He is currently a Presidential Fellow, the most prestigious award at OSU. His primary research focus is on load balancing in large-scale data center systems. He is heavily involved with the ACM SIGMETRICS community with three full papers published on the ACM SIGMETRICS conference so far. His research results have not only drawn interest from academics (e.g., invited talks at Caltech, CMU and INFORMS Annual Meeting), but also attracted cooperation from industry companies (e.g., Facebook Core System team). He obtained his B.S from BUPT and M.S. from Tsinghua University, both in the Department of Electrical Engineering and with the highest honor. He is also the recipient of various awards including the Outstanding Graduate Award of Beijing city, Distinguished Dissertation Award from BUPT and Tsinghua, National Scholarship of China, and Academic Rising Star Award from Tsinghua.", "Keywords": "", "DOI": "10.1145/3380908.3380919", "PubYear": 2020, "Volume": "47", "Issue": "3", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Ohio State University, Columbus, OH, USA"}], "References": []}, {"ArticleId": 79525071, "Title": "Incorporating copper nanoclusters into a zeolitic imidazole framework-90 for use as a highly sensitive adenosine triphosphate sensing system to evaluate the freshness of aquatic products", "Abstract": "Based on the encapsulation of copper nanoclusters (CuNCs) mixed with Al<sup>3+</sup> into a zeolitic imidazole framework-90 (ZIF-90) to form CuNCs-Al<sup>3+</sup>/ZIF-90 composites, a novel, simple and sensitive fluorescent sensor was developed to detect adenosine triphosphate (ATP) and then evaluate the freshness of aquatic products by determining the changes in the level of ATP. Encapsulated CuNCs could be released from the ZIF-90 after their addition to ATP, resulting in the fluorescence quenching of this system. Based on this phenomenon, the sensitive detection of ATP was realized with a detection limit of 0.67 μM, and a good linear relationship was observed between the fluorescence quenching ratios of the CuNCs-Al<sup>3+</sup>/ZIF-90 composites and ATP in the concentration range from 1 to 2000 μM. Moreover, satisfactory results were obtained when the proposed ATP detection method was applied to real samples to evaluate the freshness of aquatic products.", "Keywords": "Copper nanoclusters ; Zeolitic imidazole framework ; Adenosine triphosphate ; Fluorescent sensor ; Aquatic products ; Freshness", "DOI": "10.1016/j.snb.2020.127720", "PubYear": 2020, "Volume": "308", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Xue<PERSON> Gao", "Affiliation": "College of Chemistry and Chemical Engineering, Yantai University, Yantai 264005, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry and Chemical Engineering, Yantai University, Yantai 264005, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry and Chemical Engineering, Yantai University, Yantai 264005, China;Corresponding author"}, {"AuthorId": 4, "Name": "Chunyuan Tian", "Affiliation": "College of Chemistry and Chemical Engineering, Yantai University, Yantai 264005, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Chemistry and Chemical Engineering, Yantai University, Yantai 264005, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry and Chemical Engineering, Yantai University, Yantai 264005, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Shenzhen Research Institute, City University of Hong Kong, Shenzhen, 518057, China"}], "References": [{"Title": "A copper nanocluster incorporated nanogel: Confinement‐assisted emission enhancement for zinc ion detection in living cells", "Authors": "Xueqing Gao; <PERSON><PERSON>; Chunyuan Tian", "PubYear": 2020, "Volume": "307", "Issue": "", "Page": "127626", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 79525095, "Title": "Weighted quantile regression in varying-coefficient model with longitudinal data", "Abstract": "A weighted approach is developed to improve estimation efficiency in varying-coefficient quantile regression model, with longitudinal data. The weights are obtained from empirical likelihood of varying-coefficient mean model, where the nonparametric functions are approximated by basis splines, and the matrix expansion idea in quadratic inference function method is used, to model the inverse of conditional correlation matrix within subject. Theoretical results show that, the weighted estimators of the varying coefficients in quantile regression, can achieve higher efficiency than conventional estimators without weighting scheme. Simulation studies are used to assess the finite sample performance and a real data analysis is also conducted.", "Keywords": "Empirical likelihood ; Longitudinal data analysis ; Quadratic inference function ; Spline approximation ; Varying-coefficient quantile regression", "DOI": "10.1016/j.csda.2020.106915", "PubYear": 2020, "Volume": "145", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, Fudan University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Theory and Application in Statistics and Data Science — MOE, School of Statistics, East China Normal University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, Fudan University, China;Corresponding author"}], "References": []}, {"ArticleId": 79525109, "Title": "Adalward: a deep-learning framework for multi-class malicious webpage detection", "Abstract": "A global village is what the father of digital media and communications, <PERSON> had dreamt of in the late 1970s. In June 2017, reaching over 51.7% of the global population, the Internet has made it a reality. In past couple of decades, with upsurge of wireless communication technologies, the Internet has spread its web to connect all corners of the world. Its undeniable merits aside, interconnectivity on such a massive scale ushered in a whole new era of rampant malfeasance, characterized by an ever-increasing rate of cyber-crimes. Intrinsically, cyber-security researchers around the globe have been trying to develop several effective mechanisms to deal with the threats posed by cybercriminals. In this paper, we are presenting Adalward – a five-layer deep-learning framework, which has the potential for overcoming most of the challenges faced by such existing systems. Unique framework of Adalward allows it to utilize both static and dynamic web features for making accurate classification decisions with unmatched efficiency. Adalward was trained on one million labelled URLs obtained from numerous trustworthy sources. By the end of its training phase, Adalward achieved an overall detection accuracy of 99.76%, with a negligible false-positive rate of 0.10% and a nominal false-negative rate of 0.14%.", "Keywords": "Malicious webpage detection ; multi-class classification ; cyber-security ; deep-learning ; static and dynamic web features", "DOI": "10.1080/23742917.2020.1714195", "PubYear": 2020, "Volume": "4", "Issue": "3", "JournalId": 16753, "JournalTitle": "Journal of Cyber Security Technology", "ISSN": "2374-2917", "EISSN": "2374-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Computer Science & Engineering, L.N.C.T, Bhopal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Computer Science & Engineering, L.N.C.T, Bhopal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Computer Science & Engineering, L.N.C.T, Bhopal, India"}], "References": []}, {"ArticleId": 79525244, "Title": "A Secure and Efficient Parallel-Dependency RFID Grouping-Proof Protocol", "Abstract": "In this time of ubiquitous computing and the evolution of the Internet of Things (IoT), the deployment and development of Radio Frequency Identification (RFID) is becoming more extensive. Proving the simultaneous presence of a group of RFID tagged objects is a practical need in many application areas within the IoT domain. Security, privacy, and efficiency are central issues when designing such a grouping-proof protocol. Our serial-dependent and <PERSON><PERSON><PERSON> et al.'s grouping-proof protocols motivate this work. In this paper, we propose a light, improved offline protocol: parallel-dependency grouping-proof protocol (PDGPP). The protocol focuses on security, privacy, and efficiency. PDGPP tackles the challenges of including robust privacy mechanisms and accommodates missing tags. It is scalable and complies with EPC C1G2.", "Keywords": "RFID;grouping-proofs;parallel protocol;security;formal privacy model", "DOI": "10.1109/JRFID.2020.2969379", "PubYear": 2020, "Volume": "4", "Issue": "1", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Electrical and Computer Engineering, Louisiana State University, Baton Rouge, LA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Division of Electrical and Computer Engineering, Louisiana State University, Baton Rouge, LA, USA"}], "References": []}, {"ArticleId": 79525333, "Title": "Liver disorder detection using variable- neighbor weighted fuzzy K nearest neighbor approach", "Abstract": "<p>The human liver disorder is a genetic problem due to the habituality of alcohol or effect by the virus. It can lead to liver failure or liver cancer, if not been detected in initial stage. The aim of the proposed method is to detect the liver disorder in initial stage using liver function test dataset. The problem with many real-world datasets including liver disease diagnosis data is class imbalanced. The word imbalance refers to the conditions that the number of observations belongs to one class having more or less than the other class(es). Traditional K- Nearest Neighbor (KNN) or Fuzzy KNN classifier does not work well on the imbalanced dataset because they treat the neighbor equally. The weighted variant of Fuzzy KNN assign a large weight for the neighbor belongs to the minority class data and relatively small weight for the neighbor belongs to the majority class to resolve the issues with data imbalance. In this paper, Variable- Neighbor Weighted Fuzzy K Nearest Neighbor Approach (Variable-NWFKNN) is proposed, which is an improved variant of Fuzzy-NWKNN. The proposed Variable-NWFKNN method is implemented on three real-world imbalance liver function test datasets BUPA, ILPD from UCI and MPRLPD. The Variable-NWFKNN is compared with existing NWKNN and Fuzzy-NWKKNN methods and found accuracy 73.91% (BUPA Dataset), 77.59% (ILPD Dataset) and 87.01% (MPRLPD Dataset). Further, TL_RUS method is used for preprocessing and it improved the accuracy as 78.46% (BUPA Dataset), 78.46% (ILPD Dataset) and 95.79% (MPRLPD Dataset).</p>", "Keywords": "Liver disease; Imbalance data; <PERSON><PERSON> link (T-Link); Redundant pair; Undersampling; Fuzzy K-NN; Classification", "DOI": "10.1007/s11042-019-07978-3", "PubYear": 2021, "Volume": "80", "Issue": "11", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, <PERSON><PERSON><PERSON> National Institute of Technology, Bhopal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, <PERSON><PERSON><PERSON> National Institute of Technology, Bhopal, India"}], "References": [{"Title": "Diabetic retinopathy detection through artificial intelligent techniques: a review and open issues", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "15209", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 79525588, "Title": "Genome wide characterization of the SERK/SERL gene family in Phalaenopsis equestris, Dendrobium catenatum and Apostasia shenzhenica (Orchidaceae)", "Abstract": "Somatic embryogenesis receptor kinases ( SERK s) play a significant role in morphogenesis, stress/defense and signal transduction. In the present study, we have identified two SERK and 11 SERK-like ( SERL ) genes in Phalaenopsis equestris, two SERK and 11 SERL genes in Dendrobium catenatum, and one SERK and eight SERL genes in Apostasia shenzhenica genome. Characterization of the SERK proteins revealed the presence of a signal peptide, a leucine zipper, five leucine-rich repeats (LRRs), a serine proline proline (SPP) motif, a transmembrane region, a kinase domain, and a C-terminus. Most of the SERK/SERL proteins were characterized with similar physicochemical properties. The presence of transmembrane region predicted their membranous localization. Tertiary structure prediction of all the five identified SERK proteins had sequence identity with BAK1 protein of Arabidopsis thaliana . Generally, all the SERK/SERL genes shared similar gene architecture and intron phasing. Gene ontology analysis indicated the role of SERKs in receptor and ATP binding, signal transduction, and protein phosphorylation. Phylogenetic analysis revealed the clustering of SERKs and SERLs in distinct clades. Expression of SERK s in reproductive tissues like floral bud, floral stalk, whole flower and pollen was reported to be higher than their expression in vegetative tissues with an exception of PeSERK1 and DcSERK1 which showed higher expression in leaves and roots, respectively. Likewise, a higher expression of AsSERK1 was observed in tubers. However, lower expression of SERL s was observed in majority of tissues studied irrespective of their vegetative or reproductive origin. This work paves way for future studies involving functional characterization of SERK/SERLs and their potential role in embryogenesis/organogenesis as an aid to regeneration and multiplication of endangered orchids.", "Keywords": "Apostasia shenzhenica;Dendrobium catenatum;Orchid;Phalaenopsis equestris;Somatic embryogenesis;Somatic embryogenesis receptor kinase (SERK)", "DOI": "10.1016/j.compbiolchem.2020.107210", "PubYear": 2020, "Volume": "85", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Botany, Panjab University, Chandigarh-160014, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Botany, Panjab University, Chandigarh-160014, India."}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": "Department of Botany, Panjab University, Chandigarh-160014, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Botany, Panjab University, Chandigarh-160014, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Botany, Panjab University, Chandigarh-160014, India. Electronic address:  ."}], "References": []}, {"ArticleId": 79525592, "Title": "Reconstructibility of singular Boolean control networks via automata approach", "Abstract": "Three types of reconstructibility of singular Boolean control networks (SBCNs) are introduced and investigated in this paper. Two approaches are proposed to solve these three types of reconstructibility of SBCNs, one is the weighted set graph approach, the other is automata approach. Using the former, some criterions are obtained, while by the latter, several necessary and sufficient conditions are derived for the reconstructibility of SBCNs. Then an algorithm is designed to determine the reconstructible state and the computational complexity is analyzed. Finally, two numerical examples are shown to demonstrate the effectiveness of the theoretical results.", "Keywords": "Reconstructibility ; Singular Boolean control networks ; Semi-tensor product ; Automata", "DOI": "10.1016/j.neucom.2020.01.061", "PubYear": 2020, "Volume": "416", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics, Shandong University, Jinan 250100, Peoples Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics, Shandong University, Jinan 250100, Peoples Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics, Shandong University, Jinan 250100, Peoples Republic of China;Corresponding author"}], "References": []}, {"ArticleId": 79526078, "Title": "In silico identification of potential inhibitors against human 2’-5’- oligoadenylate synthetase (OAS) proteins", "Abstract": "<p>As part of the type I IFN signaling, the 2'-5'- oligoadenylate synthetase (OAS) proteins have been involved in the progression of several non-viral diseases. Notably, OAS has been correlated with immune-modulatory functions that promote chronic inflammatory conditions, autoimmune disorders, cancer, and infectious diseases. In spite of this, OAS enzymes have been ignored as drug targets, and to date, there are no reports of compounds that can inhibit their activity. In this study, we have used homology modeling and virtual high-throughput screening to identify potential inhibitors of the human proteins OAS1, OAS2, and OAS3. Alto<PERSON>her, we have found 37 molecules that could exert a competitive inhibition in the ATP binding sites of OAS proteins, independently of the activation state of the enzyme. This latter characteristic, which might be crucial for a versatile inhibitor, was observed in compounds interacting with the residues Asp75, Asp77, Gln229, and Tyr230 in OAS1, and their equivalents in OAS2 and OAS3. Although there was little correlation between specific chemical fragments and their interactions, intermolecular contacts with OAS catalytic triad and other critical amino acids were mainly promoted by heterocycles with π electrons and hydrogen bond acceptors. In conclusion, this study provides a potential set of OAS inhibitors as well as valuable information for their design, development, and optimization.</p><p>Copyright © 2020 Elsevier Ltd. All rights reserved.</p>", "Keywords": "2’-5’- oligoadenylate synthetase (OAS);Docking;In-silico inhibition;Small drug-like molecules;Virtual screening", "DOI": "10.1016/j.compbiolchem.2020.107211", "PubYear": 2020, "Volume": "85", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Bioinformatics, University of Georgia, Athens, GA, 30602, USA."}, {"AuthorId": 2, "Name": "<PERSON>G<PERSON><PERSON>", "Affiliation": "Institute of Bioinformatics, University of Georgia, Athens, GA, 30602, USA."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, University of Texas at San Antonio, San Antonio, TX, 78249, USA. Electronic address:  ."}], "References": []}, {"ArticleId": 79526156, "Title": "Highly sensitive and selective detection of human-derived volatile organic compounds based on odorant binding proteins functionalized silicon nanowire array", "Abstract": "In this paper, we report the real-time, ultrasensitive, reproducible detection of volatile odorant molecules emitted by human based on free-standing silicon nanowire (SiNW) array functionalized with Anopheles gambiae odorant binding protein ( Ag OBP). Highly responsive SiNW array was fabricated with a novel low-cost top-down fabrication approach. The free-standing and triangular cross sectional structure allowed efficient sensing of Ag OBP conformational change induced by odorant binding. The resulting bioelectronic nose (B-nose) possessed high sensitivity down to several ppb, and outstanding size and functional group selectivity. The device showed reproducible and long-lasting performances. We also demonstrated the multiplex detection of various odorants with a sensor array functionalized with three different Ag OBPs. The Ag OBP-SiNW B-nose reported herein provides a powerful and effective platform for future development of multiplexed human body odor sensor arrays.", "Keywords": "Bioelectronic nose ; Volatile organic compounds (VOCs) ; High sensitivity ; Silicon nanowire array ; Odorant binding proteins (OBPs)", "DOI": "10.1016/j.snb.2020.127762", "PubYear": 2020, "Volume": "309", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Polar Materials and Devices (MOE), Department of Electronics, East China Normal University, Shanghai 200241, China;Science and Technology on Micro-system Laboratory, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Sciences, Shanghai 200050, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Proteomics, National Center for Protein Sciences (Beijing), Beijing Institute of Lifeomics, Beijing 102206, China;National Engineering Research Center for Protein Drugs (NERCPD), Beijing 102206, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Proteomics, National Center for Protein Sciences (Beijing), Beijing Institute of Lifeomics, Beijing 102206, China;National Engineering Research Center for Protein Drugs (NERCPD), Beijing 102206, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Science and Technology on Micro-system Laboratory, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Sciences, Shanghai 200050, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Proteomics, National Center for Protein Sciences (Beijing), Beijing Institute of Lifeomics, Beijing 102206, China;National Engineering Research Center for Protein Drugs (NERCPD), Beijing 102206, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Science and Technology on Micro-system Laboratory, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Sciences, Shanghai 200050, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Science and Technology on Micro-system Laboratory, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Sciences, Shanghai 200050, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON> Song", "Affiliation": "State Key Laboratory of Proteomics, National Center for Protein Sciences (Beijing), Beijing Institute of Lifeomics, Beijing 102206, China;National Engineering Research Center for Protein Drugs (NERCPD), Beijing 102206, China;Corresponding author at: State key laboratory of proteomics, National Center for Protein Sciences (Beijing), Beijing Institute of Lifeomics, Beijing 102206, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Science and Technology on Micro-system Laboratory, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Sciences, Shanghai 200050, China;Corresponding author"}], "References": []}, {"ArticleId": 79526261, "Title": "Personalized Tag Recommendation Based on Convolution Feature and Weighted Random Walk", "Abstract": "", "Keywords": "Flickr; User group; Bipartite graph; Weighted random walk; Personalized tag recommendation", "DOI": "10.2991/ijcis.d.200114.001", "PubYear": 2020, "Volume": "13", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 79526321, "Title": "New Ant Colony Optimization Algorithm for the Traveling Salesman Problem", "Abstract": "", "Keywords": "Computational intelligence optimization; New ant colony optimization algorithm; Meeting strategy; Performance; Traveling salesman problem", "DOI": "10.2991/ijcis.d.200117.001", "PubYear": 2020, "Volume": "13", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 79526345, "Title": "A Hybrid Method for Traffic Flow Forecasting Using Multimodal Deep Learning", "Abstract": "", "Keywords": "Traffic flow forecasting; Multimodal deep learning; Gated recurrent units; Attention mechanism; Convolutional neural networks", "DOI": "10.2991/ijcis.d.200120.001", "PubYear": 2020, "Volume": "13", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "Shengdong Du", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 79526380, "Title": "Multi-Class Skin Lesions Classification System Using Probability Map Based Region Growing and DCNN", "Abstract": "", "Keywords": "Black frame removal; Gaussian filtering; Region growing; Optimal thresholding; Geometric features; SVM classification", "DOI": "10.2991/ijcis.d.200117.002", "PubYear": 2020, "Volume": "13", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 79526742, "Title": "Social Processes: What Determines Industrial Workers’ Intention to Use Exoskeletons?", "Abstract": "Objective <p>The aim of this study is to test the unified theory of acceptance and use of technology (UTAUT) model for explaining the intention to use exoskeletons among industrial workers.</p> Background <p>Exoskeletons could help reduce physical workload and risk for injuries among industrial workers. Therefore, it is crucial to understand which factors play a role in workers’ intention to use such exoskeletons.</p> Method <p>Industrial workers ( N = 124) completed a survey on their attitudes regarding the use of exoskeletons at their workplace. Using partial least squares (PLS) path modeling, the UTAUT model and a revised version of the UTAUT model were fitted to these data.</p> Results <p>The adapted UTAUT model of <PERSON><PERSON><PERSON><PERSON> et al. (2017) was able to explain up to 75.6% of the variance in intention to use exoskeletons, suggesting a reasonable model fit.</p> Conclusion <p>The model fit suggests that effort expectancy (how easy it seems to use an exoskeleton) plays an important role in predicting the intention to use exoskeletons. Social influence (whether others think workers should use exoskeletons) and performance expectancy (how useful exoskeletons seem to be for work) play a smaller role in predicting the intention to use.</p> Applications <p>This research informs companies about the optimal implementation of exoskeletons by improving the determinants of acceptance among their workers.</p>", "Keywords": "experience;statistics and data analysis;structural equation modeling;technology acceptance;wearable devices", "DOI": "10.1177/0018720819889534", "PubYear": 2020, "Volume": "62", "Issue": "3", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Brussel, Belgium;BruBOTICS, Belgium"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON>", "Affiliation": "imec, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Vrije Universiteit Brussel, Belgium;BruBOTICS, Belgium"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Brussel, Belgium;BruBOTICS, Belgium;Flanders Make, Belgium"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Brussel, Belgium;BruBOTICS, Belgium"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Vrije Universiteit Brussel, Belgium;BruBOTICS, Belgium"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Brussel, Belgium;BruBOTICS, Belgium"}], "References": []}, {"ArticleId": 79526760, "Title": "Analysis of double chambered single and cascaded microbial fuel cells: characterisation study based on the enrichment of fuel", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIE.2020.10026348", "PubYear": 2020, "Volume": "7", "Issue": "1/3", "JournalId": 13634, "JournalTitle": "International Journal of Intelligent Enterprise", "ISSN": "1745-3232", "EISSN": "1745-3240", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 79526761, "Title": "Advanced graphical-based security approach to handle hard AI problems based on visual security", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIE.2020.10026353", "PubYear": 2020, "Volume": "7", "Issue": "1/3", "JournalId": 13634, "JournalTitle": "International Journal of Intelligent Enterprise", "ISSN": "1745-3232", "EISSN": "1745-3240", "Authors": [{"AuthorId": 1, "Name": "T. Venkata Sa<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 79526762, "Title": "Regulations on sustainability reporting as a global force in shaping business enterprises: evidence from India", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIE.2020.10026350", "PubYear": 2020, "Volume": "7", "Issue": "1/3", "JournalId": 13634, "JournalTitle": "International Journal of Intelligent Enterprise", "ISSN": "1745-3232", "EISSN": "1745-3240", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}]