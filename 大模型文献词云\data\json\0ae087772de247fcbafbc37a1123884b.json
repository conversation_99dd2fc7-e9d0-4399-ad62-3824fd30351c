[{"ArticleId": 90388594, "Title": "A survey of SRAM-based in-memory computing techniques and applications", "Abstract": "As von <PERSON> computing architectures become increasingly constrained by data-movement overheads, researchers have started exploring in-memory computing (IMC) techniques to offset data-movement overheads. Due to the widespread use of SRAM, IMC techniques for SRAM hold the promise of accelerating a broad range of computing systems and applications. In this article, we present a survey of techniques for in-memory computing using SRAM memory. We review the use of SRAM-IMC for implementing Boolean, search and arithmetic operations, and accelerators for machine learning (especially neural networks) and automata computing. This paper aims to accelerate co-design efforts by informing researchers in both algorithm and hardware architecture fields about the recent developments in SRAM-based IMC techniques.", "Keywords": "Review ; Deep neural networks ; SRAM ; Cache ; In-memory computing ; Neural network ; Automata computing", "DOI": "10.1016/j.sysarc.2021.102276", "PubYear": 2021, "Volume": "119", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>l", "Affiliation": "Department of Electronics and Communications Engineering, IIT Roorkee, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communications Engineering, IIT Roorkee, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communications Engineering, IIT Roorkee, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Electronics and instrumentation Technology, University of Kashmir, India"}], "References": [{"Title": "A survey on modeling and improving reliability of DNN algorithms and accelerators", "Authors": "<PERSON><PERSON><PERSON>l", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "101689", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "A survey of accelerator architectures for 3D convolution neural networks", "Authors": "<PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "102041", "JournalTitle": "Journal of Systems Architecture"}]}, {"ArticleId": 90388629, "Title": "An Analysis of Computational Resources of Event-Driven Streaming Data Flow for Internet of Things: A Case Study", "Abstract": "<p>Information and communication technologies backbone of a smart city is an Internet of Things (IoT) application that combines technologies such as low power IoT networks, device management, analytics or event stream processing. Hence, designing an efficient IoT architecture for real-time IoT applications brings technical challenges that include the integration of application network protocols and data processing. In this context, the system scalability of two architectures has been analysed: the first architecture, named as POST architecture, integrates the hyper text transfer protocol with an Extract-Transform-Load technique, and is used as baseline; the second architecture, named as MQTT-CEP, is based on a publish-subscribe protocol, i.e. message queue telemetry transport, and a complex event processor engine. In this analysis, SAVIA, a smart city citizen security application, has been deployed following both architectural approaches. Results show that the design of the network protocol and the data analytic layer impacts highly in the Quality of Service experimented by the final IoT users. The experiments show that the integrated MQTT-CEP architecture scales properly, keeps energy consumption limited and thereby, promotes the development of a distributed IoT architecture based on constraint resources. The drawback is an increase in latency, mainly caused by the loosely coupled communication pattern of MQTT, but within reasonable levels which stabilize with increasing workloads.</p>", "Keywords": "", "DOI": "10.1093/comjnl/bxab143", "PubYear": 2023, "Volume": "66", "Issue": "1", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Escuela de Posgrado, Universidad Peruana Cayetano Heredia, Lima, Peru"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": "Universidad de Lima, Lima, Peru"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center of Information and Communication Technologies, Universidad Nacional de Ingenieria, Lima, Peru"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Albacete Research Institute of Informatics, Universidad de Castilla-La Mancha, Albacete, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Albacete Research Institute of Informatics, Universidad de Castilla-La Mancha, Albacete, Spain"}], "References": [{"Title": "Survey, comparison and research challenges of IoT application protocols for smart farming", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "168", "Issue": "", "Page": "107037", "JournalTitle": "Computer Networks"}, {"Title": "A stream processing architecture for heterogeneous data sources in the Internet of Things", "Authors": "<PERSON>-Plaza; Inmaculada Medina-<PERSON>ulo; <PERSON>", "PubYear": 2020, "Volume": "70", "Issue": "", "Page": "103426", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "Application Management in Fog Computing Environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "MEdit4CEP-SP: A model-driven solution to improve decision-making through user-friendly management and real-time processing of heterogeneous data streams", "Authors": "<PERSON>-Plaza; <PERSON>; Inmaculada Medina-Bulo", "PubYear": 2021, "Volume": "213", "Issue": "", "Page": "106682", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 90388641, "Title": "Cognitive load considerations for Augmented Reality in network security training", "Abstract": "This paper presents an Augmented Reality (AR)-based network cabling tutoring system that trains users how to interconnect cables within a Virtual Local Area Network (VLAN) on a physical switching rack. AR arrows combined with text-based instruction and a checklist provided assistance during practical learning. When learners made a mistake, an Intelligent Tutoring System (ITS) identified the source of the mistake and provided real-time feedback using text, AR and text-to-speech mechanisms. The design was motivated by the human-cognitive architecture and its five evolutionary principles (proposed by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2006)). Users performed four consecutive network cabling training tasks with assistance from our ITS. We found that users made fewer errors when the AR cues, text-based instruction and checklist solutions were replaced with summarised information and then removed completely in the final task compared to those who used an identical system with fixed instruction. Cognitive Load Theory (CLT) explains our results by suggesting that the instructional mechanisms become redundant as knowledge increases. Implications of the study are discussed as well as how AR can help facilitate knowledge transfer in the network cabling domain.", "Keywords": "Intelligent Tutoring Systems ; Augmented Reality ; Cognitive Load Theory ; Instructional design ; Training and education", "DOI": "10.1016/j.cag.2021.09.001", "PubYear": 2022, "Volume": "102", "Issue": "", "JournalId": 3909, "JournalTitle": "Computers & Graphics", "ISSN": "0097-8493", "EISSN": "1873-7684", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of South Australia, Mawson Lakes Boulevard, SA 5095 Mawson Lakes, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of South Australia, Mawson Lakes Boulevard, SA 5095 Mawson Lakes, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Monash University, Wellington Road, Clayton, Clayton Vic 3800, Australia;University of South Australia, Mawson Lakes Boulevard, SA 5095 Mawson Lakes, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Auckland, Auckland Bioengineering Institute University of Auckland, 70 Symonds Street, Auckland, Auckland 1010, New Zealand;University of South Australia, Mawson Lakes Boulevard, SA 5095 Mawson Lakes, Australia"}], "References": [{"Title": "Comparative study of AR versus video tutorials for minor maintenance operations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7073", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Effects of augmented reality on learning and cognitive load in university physics laboratory courses", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "106316", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": ********, "Title": "Risk-aware optimized quickest path computing technique for critical routing services", "Abstract": "In this paper, a novel model has been proposed for the computation of optimized path with link reliability, delay and capacity. Algorithms have been proposed for the optimized data path with a single link-weight and it constitutes two main parts: (i) characterization of the network for the best optimized path, and (ii) tuning of the network parameters for the prediction/validation for the Best Optimized Path, according to a given data flow. It also needs very little a priori information during the online computation as the computed optimized path requires less computational overhead as each data flow shall be assigned with a precomputed and optimized link weight. Now, with the help of this optimized link weight one can find the optimum path with respect to both risk and lag-time in an online approach simply by adopting the <PERSON><PERSON><PERSON>'s algorithm. This approach has been illustrated here in this paper with the help of two different topologies for different values of data flow from single source to single destination. Algorithms have been proposed based on this mathematical model. It also can be observed that proposed algorithms are able to compute the optimized path with having complexity of <PERSON><PERSON><PERSON>'s algorithm when computer together. This study can be claimed that the proposed algorithms are self-sufficient to provide the risk-aware quickest path routing, even without having any prior information on the path to be allocated. One can easily extend it for the case of many source nodes to the many destination nodes as well as for the software defined networking (SDN) in the future work.", "Keywords": "Network tuning ; Risk ; Characterization of network ; Mission-critical routing ; Continuity", "DOI": "10.1016/j.compeleceng.2021.107436", "PubYear": 2021, "Volume": "95", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computer Technology and Information Security, Southern Federal University, Rastov-on-Don, Russian Federation;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AGH University of Science and Technology, Institute of Telecommunications, Al. <PERSON> 30, Krakow 30-059, PolandD"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Jaypee University of Information Technology Solan, H. P. 173234, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Government Bikram College of Commerce, Patiala, Punjab 147001, India"}], "References": []}, {"ArticleId": 90388872, "Title": "Optimal Design of Magneto-Force-Thermal Parameters for Electromagnetic Actuators with Halbach Array", "Abstract": "<p>A magnetic levitation isolation system applied for the active control of micro-vibration in space requires actuators with high accuracy, linear thrust and low power consumption. The magneto-force-thermal characteristics of traditional electromagnetic actuators are not optimal, while actuators with a Halbach array can converge magnetic induction lines and enhance the unilateral magnetic field. To improve the control effect, an accurate magnetic field analytical model is required. In this paper, a magnetic field analytical model of a non-equal-size Halbach array was established based on the equivalent magnetic charge method and the field strength superposition principle. Comparisons were conducted between numerical simulations and analytical results of the proposed model. The relationship between the magnetic flux density at the air gap and the size parameters of the Halbach array was analyzed by means of a finite element calculation. The mirror image method was adopted to consider the influence of the ferromagnetic boundary on the magnetic flux density. Finally, a parametric model of the non-equal-size Halbach actuator was established, and the multi-objective optimization design was carried out using a genetic algorithm. The actuator with optimized parameters was manufactured and experiments were conducted to verify the proposed analytical model. The difference between the experimental results and the analytical results is only 5%, which verifies the correctness of the magnetic field analytical model of the non-equal-size Halbach actuator.</p>", "Keywords": "electromagnetic actuators; Halbach arrays; magnetic levitation; magnetic field modeling; finite element simulation; analysis; multi-objective optimal design electromagnetic actuators ; Halbach arrays ; magnetic levitation ; magnetic field modeling ; finite element simulation ; analysis ; multi-objective optimal design", "DOI": "10.3390/act10090231", "PubYear": 2021, "Volume": "10", "Issue": "9", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Qingdao University of Technology, Qingdao 266000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Qingdao University of Technology, Qingdao 266000, China↑Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Qingdao University of Technology, Qingdao 266000, China"}, {"AuthorId": 4, "Name": "Zhaopei Gong", "Affiliation": "Harbin Institute of Technology, School of Mechatronics Engineering, Harbin 150001, China"}], "References": [{"Title": "Research on an Electromagnetic Actuator for Vibration Suppression and Energy Regeneration", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "2", "Page": "42", "JournalTitle": "Actuators"}, {"Title": "Analysis of High Force Voice Coil Motors for Magnetic Levitation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "4", "Page": "133", "JournalTitle": "Actuators"}, {"Title": "Analytical and Experimental Investigations of Novel Maglev Coupling Based on Opposed Halbach Array for a 2D Valve", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "3", "Page": "61", "JournalTitle": "Actuators"}, {"Title": "Design and Simulation of Novel 3-DOF Spherical Voice Coil Motor", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "7", "Page": "155", "JournalTitle": "Actuators"}]}, {"ArticleId": 90388915, "Title": "An Approximation of Minimax Control using Random Sampling and Symbolic Computation", "Abstract": "We aim at the synthesis of an approximate minimax control for a system dynamic given in the form x(t) = f(x(t), u (t), d(t)) where x represents the state, u the control (or input) and d a disturbance (or perturbation). The disturbance d(t) is prescribed to a compact domain D on which it can take any value (bounded uncertainty). Our method makes use of symbolic computation to enclose the solutions corresponding to all possible disturbance d (-) ϵ D, together with a simple algorithm of random sampling to select the minimum control u<sup>⁎</sup>(•). We illustrate the interest of this approximate minimax control on an example of a biochemical reactor.", "Keywords": "Switched systems ; bounded uncertainty ; optimal control ; random sampling ; <PERSON><PERSON><PERSON>’s method", "DOI": "10.1016/j.ifacol.2021.08.509", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université Sorbonne Paris Nord, LIPN, CNRS, F-93430 Villetaneuse, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Université Paris-Saclay, CNRS, ENS Paris-Saclay, LMF, 91190 Gif-sur-Yvette, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Université de Lorraine, CNRS, Inria, LORIA, F-54000 Nancy, France"}], "References": []}, {"ArticleId": 90388960, "Title": "Hammer peening technology—the past, present, and future", "Abstract": "<p>A number of surface modification processes have been developed over the past decades. Depending on the application in industry, different processes are selected to meet the needs. Shot peening is one of the widely adopted surface modification processes in various industries dedicated for improving the material properties. On the other hand, hammer peening is an emerging and promising surface modification process that can provide wide range of benefits, encompassing fatigue life enhancement, surface finish improvement and texturing, microstructural refinement, corrosion, and wear resistance. This technology has been extensively studied on its application as a post-welding treatment on metal alloys. However, there is limited information available for other applications such as additive manufacturing. This paper discusses how hammer peening has evolved into its current state today and the role it played in various industries. The variants of hammer peening technology and the standardized key process parameters are elucidated. In view of the rigorous research and development, the current state-of-the-art on different applications area also represented. Despite the proven capability by many studies, the gaps to the industrialization of this technology still exist which are identified in the final section of the paper.</p>", "Keywords": "Hammer peening; Surface modification; Residual stress; Fatigue; Manufacturing", "DOI": "10.1007/s00170-021-07993-5", "PubYear": 2022, "Volume": "118", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Remanufacturing and Technology Centre, Singapore, Singapore"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Advanced Remanufacturing and Technology Centre, Singapore, Singapore"}], "References": [{"Title": "Experimental investigation of shot peening: correlation of pressure and shot velocity to Almen intensity", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "11-12", "Page": "4859", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 90388964, "Title": "A benchmark for visual analysis of insider threat detection", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11432-019-2776-4", "PubYear": 2022, "Volume": "65", "Issue": "9", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science, Fudan University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Qi An Xin Group, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Qi An Xin Group, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Qi An Xin Group, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Qi An Xin Group, Beijing, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Qi An Xin Group, Beijing, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Technology and Management, Hunan University of Finance and Economics, Changsha, China"}], "References": []}, {"ArticleId": 90388966, "Title": "An approximation for nonlinear differential-algebraic equations via singular perturbation theory", "Abstract": "In this paper, we study the jumps of nonlinear DAEs caused by inconsistent initial values. First, we propose a simple normal form called the index-1 nonlinear Weierstrass form (INWF) for nonlinear DAEs. Then we generalize the notion of consistency projector introduced in <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2009) for linear DAEs to the nonlinear case. By an example, we compare our proposed nonlinear consistency projectors with two existing consistent initialization methods (one is from the paper <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2012) and the other is given by a MATLAB function) to show that the two existing methods are not coordinate-free, i.e., the consistent points calculated by the two methods are not invariant under nonlinear coordinates transformations. Next we propose a singular perturbed system approximation for nonlinear DAEs, which is an ordinary differential equation (ODE) with a small perturbation parameter and we show that the solutions of the proposed perturbation system approximate both the jumps resulting from the nonlinear consistency projectors and the C<sup>1</sup>-solutions of the DAE. At last, we use a numerical simulation of a nonlinear DAE model arising from an electric circuit to illustrate the effectiveness of the proposed singular perturbed system approximation of DAEs.", "Keywords": "differential-algebraic equations ; singular perturbation ; jumps ; index-1 ; nonlinear Wei<PERSON>trass form ; inconsistent initial values", "DOI": "10.1016/j.ifacol.2021.08.496", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Bernoulli Institute for Mathematics, Computer Science, and Artificial Intelligence, University of Groningen, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Bernoulli Institute for Mathematics, Computer Science, and Artificial Intelligence, University of Groningen, The Netherlands"}], "References": []}, {"ArticleId": 90389115, "Title": "Design and development of a reconfigurable tiling robot", "Abstract": "<p>This paper presents a novel polyromb-based self-reconfigurable domestic floor cleaning robot called “hRhombo” that consists of four rhombus-shaped modules connected with three revolute joints. The mechanical design of the robot gives rise to an over-actuated mathematical model. The mechanical design, electronics layout, kinematics relations, reconfiguration theory and trajectory tracking methodology for the robot are discussed. The forward kinematics and inverse kinematics are derived in terms of the angle between the modules and the wheel steering angles. The tracking controller exploits a poly-cycle approach, where each wheel has a different reference path depending on the current configuration of the robot. The model predictive controller technique is used to design optimal controller for four unicycles, modeling the four wheels. The numerical simulations and experiments reveal the usefulness of the robot in the domestic environment with complex furniture settings.</p>", "Keywords": "", "DOI": "10.1007/s11370-021-00384-5", "PubYear": 2021, "Volume": "14", "Issue": "5", "JournalId": 2152, "JournalTitle": "Intelligent Service Robotics", "ISSN": "1861-2776", "EISSN": "1861-2784", "Authors": [{"AuthorId": 1, "Name": "Rizuwana Parween", "Affiliation": "Singapore University of Technology and Design, Singapore, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, Delhi Technological University, Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Singapore University of Technology and Design, Singapore, Singapore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Singapore University of Technology and Design, Singapore, Singapore"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Singapore University of Technology and Design, Singapore, Singapore"}], "References": []}, {"ArticleId": 90389208, "Title": "Data-driven Nonlinear MPC using Dynamic Response Surface Methodology", "Abstract": "For many complex processes, it is desirable to use a nonlinear model in the MPC design, and the recently proposed Dynamic Response Surface Methodology (DRSM) is capable of accurately modeling nonlinear continuous processes over semi-infinite time horizons. We exploit the DRSM to identify nonlinear data-driven dynamic models that are used in an NMPC. We demonstrate the ability and effectiveness of the DRSM data-driven model to be used as the prediction model for a nonlinear MPC regulator. This DRSM model is efficiently used to solve a non-equally-spaced finite-horizon optimal control problem so that the number of decision variables is reduced. The proposed DRSM-based NMPC is tested on a representative nonlinear process, an isothermal CSTR in which a second-order irreversible reaction is taking place. It is shown that the obtained quadratic data-driven model accurately represents the open-loop process dynamics and that DRSM-based NMPC is an effective data-driven implementation of nonlinear MPC.", "Keywords": "Nonlinear MPC ; Dynamic Response Surface Methodology ; Data-driven MPC ; Systems identification", "DOI": "10.1016/j.ifacol.2021.08.556", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Pisa, Department of Civil and Industrial Engineering, Largo Lazzarino 2, 56126 Pisa (Italy)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department Chemical and Biological Engineering, Tufts University, Medford, Massachusetts"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Pisa, Department of Civil and Industrial Engineering, Largo Lazzarino 2, 56126 Pisa (Italy)"}], "References": []}, {"ArticleId": 90389218, "Title": "A new software cache structure on Sunway TaihuLight", "Abstract": "<p>The Sunway TaihuLight is the first supercomputer built entirely with domestic processors in China. On Sunway Taihulight, the local data memory (LDM) of the slave core is limited, so data transmission with the main memory is frequent during calculation, and the memory access efficiency is low. On the other hand, for many scientific computing programs, how to solve the storage problem of irregular access data is the key to program optimization. Software cache (SWC) is one of the effective means to solve these problems. Based on the characteristics of Sunway TaihuLight structure and irregular access, this paper designs and implements a new software cache structure by using part of the space in LDM to simulate the cache function, which uses new cache address mapping and conflicts solution to solve high data access overhead and storage overhead in a traditional cache. At the same time, the SWC uses the register communication between the slave cores to share on the different slave core LDMs, increasing the capacity of the software cache and improving the hit rate. In addition, we adopt a double buffer strategy to access regular data in batches, which hides the communication overhead between the slave core and the main memory. The test results on the Sunway TaihuLight platform show that the software cache structure in this paper can effectively reduce the program running time, improve the software cache hit rate, and achieve a better optimization effect.</p>", "Keywords": "Software cache; Irregular access; Sunway TaihuLight; Memory access optimization", "DOI": "10.1007/s11227-021-04056-0", "PubYear": 2022, "Volume": "78", "Issue": "4", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "Jianjiang Li", "Affiliation": "Department of Computer Science and Technology, University of Science and Technology, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, University of Science and Technology, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, University of Science and Technology, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, University of Science and Technology, Beijing, China"}], "References": [{"Title": "A Novel Fog Computing Approach for Minimization of Latency in Healthcare using Machine Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "7", "Page": "7", "JournalTitle": "International Journal of Interactive Multimedia and Artificial Intelligence"}, {"Title": "Design and Development of an Energy Efficient Multimedia Cloud Data Center with Minimal SLA Violation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "7", "Page": "49", "JournalTitle": "International Journal of Interactive Multimedia and Artificial Intelligence"}, {"Title": "Optimal QoE Scheduling in MPEG-DASH Video Streaming", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "7", "Page": "71", "JournalTitle": "International Journal of Interactive Multimedia and Artificial Intelligence"}]}, {"ArticleId": 90389223, "Title": "Machine learning techniques to predict different levels of hospital care of CoVid-19", "Abstract": "In this study, we analyze the capability of several state of the art machine learning methods to predict whether patients diagnosed with CoVid-19 (CoronaVirus disease 2019) will need different levels of hospital care assistance (regular hospital admission or intensive care unit admission), during the course of their illness, using only demographic and clinical data. For this research, a data set of 10,454 patients from 14 hospitals in Galicia (Spain) was used. Each patient is characterized by 833 variables, two of which are age and gender and the other are records of diseases or conditions in their medical history. In addition, for each patient, his/her history of hospital or intensive care unit (ICU) admissions due to CoVid-19 is available. This clinical history will serve to label each patient and thus being able to assess the predictions of the model. Our aim is to identify which model delivers the best accuracies for both hospital and ICU admissions only using demographic variables and some structured clinical data, as well as identifying which of those are more relevant in both cases. The results obtained in the experimental study show that the best models are those based on oversampling as a preprocessing phase to balance the distribution of classes. Using these models and all the available features, we achieved an area under the curve (AUC) of 76.1% and 80.4% for predicting the need of hospital and ICU admissions, respectively. Furthermore, feature selection and oversampling techniques were applied and it has been experimentally verified that the relevant variables for the classification are age and gender, since only using these two features the performance of the models is not degraded for the two mentioned prediction problems.", "Keywords": "CoVid-19;Feature selection;Machine learning;Supervised classification", "DOI": "10.1007/s10489-021-02743-2", "PubYear": 2022, "Volume": "52", "Issue": "6", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON>", "Affiliation": "Universidade da Coruña. CITIC Research and Development Laboratory in Artificial Intelligence (LIDIA) Facultad de informática, Campus de Elviña s/n. A, Coruña, Spain."}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": "Universidade da Coruña. CITIC Research and Development Laboratory in Artificial Intelligence (LIDIA) Facultad de informática, Campus de Elviña s/n. A, Coruña, Spain."}, {"AuthorId": 3, "Name": "Verónica <PERSON>-Canedo", "Affiliation": "Universidade da Coruña. CITIC Research and Development Laboratory in Artificial Intelligence (LIDIA) Facultad de informática, Campus de Elviña s/n. A, Coruña, Spain."}, {"AuthorId": 4, "Name": "Brais <PERSON>-Barizo", "Affiliation": "Universidade da Coruña. CITIC Research and Development Laboratory in Artificial Intelligence (LIDIA) Facultad de informática, Campus de Elviña s/n. A, Coruña, Spain."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidade da Coruña. CITIC Research and Development Laboratory in Artificial Intelligence (LIDIA) Facultad de informática, Campus de Elviña s/n. A, Coruña, Spain."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "Affiliation": "Universidade da Coruña. CITIC Research and Development Laboratory in Artificial Intelligence (LIDIA) Facultad de informática, Campus de Elviña s/n. A, Coruña, Spain."}], "References": [{"Title": "Generalized Z-numbers with hesitant fuzzy linguistic information and its application to medicine selection for the patients with mild symptoms of the COVID-19", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "106517", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Machine-Learning Approaches in COVID-19 Survival Analysis and Discharge-Time Likelihood Prediction Using Clinical Data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "5", "Page": "100074", "JournalTitle": "Patterns"}]}, {"ArticleId": 90389292, "Title": "Driver Fatigue Detection Based On Facial Feature Analysis", "Abstract": "<p>Fatigue driving is one of the main causes of traffic accidents. In recent years, considerable attention has been paid to fatigue detection systems, which is an important solution for preventing fatigue driving. In order to prevent and reduce fatigue driving, a driver fatigue detection system based on computer vision is proposed. In this system, an improved face detection method is used to detect the driver’s face from the image obtained by a charge coupled device (CCD) camera. Then, the feature points of the eyes and mouth are located by an ensemble of regression trees. Next, fatigue characteristic parameters are calculated by the improved percentage of eyelid closure over the pupil over time algorithm. Finally, the state of drivers is evaluated by using a fuzzy neural network. The system can effectively monitor and remind the state of drivers so as to significantly avoid or decrease the occurrence of traffic accidents. The experimental results show that the system is of wonderful real-time performance and accurate recognition rate, so it meets the requirements of practicality in driver fatigue detection greatly.</p>", "Keywords": "", "DOI": "10.1142/S0218001421500348", "PubYear": 2021, "Volume": "35", "Issue": "15", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics and Electronics, Henan University, Kaifeng 475004, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Electronics, Henan University, Kaifeng 475004, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Physics and Electronics, Henan University, Kaifeng 475004, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Academy of Aerospace Technology, Shenzhen 518057, P. R. <PERSON>"}], "References": []}, {"ArticleId": 90389309, "Title": "Accuracy-awareness: A pessimistic approach to optimal control of triggered mobile communication networks", "Abstract": "We use nonlinear model predictive control to procure a joint control of mobility and transmission to minimize total network communication energy use. The nonlinear optimization problem is solved numerically in a self-triggered framework, where the next control update time depends on the predicted state trajectory and the accuracy of the numerical solution. Solution accuracy must be accounted for in any circumstance where systems are run in open-loop for long stretches of time based on potentially inaccurate predictions. These triggering conditions allow us to place wireless nodes in low energy ‘idle’ states for extended periods, saving over 70% of energy compared to a periodic policy where nodes consistently use energy to receive control updates.", "Keywords": "Autonomous mobile robots ; Communication networks ; Model-based control ; Nonlinear control ; Optimal control ; Triggered control", "DOI": "10.1016/j.ifacol.2021.08.560", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering, Imperial College London, SW7 2AZ London, U.K"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering, Imperial College London, SW7 2AZ London, U.K.;Department of Aeronautics, Imperial College London, SW7 2AZ London, U.K"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering, Imperial College London, SW7 2AZ London, U.K"}], "References": [{"Title": "Mesh refinement for event-triggered nonlinear model predictive control", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "6516", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Mesh refinement for event-triggered nonlinear model predictive control", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "6516", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 90389310, "Title": "Planning in Dynamic and Partially Unknown Environments", "Abstract": "Motion planning in dynamic and partially unknown environments is a difficult problem requiring both perception and control components. We propose a solution to the control component while cleanly abstracting perception. We show that this clean abstraction can be used to synthesize verifiably safe reference trajectories using a combination of reachability analysis and Mixed Integer Linear Programming. Experiments with a prototype implementation of this algorithm show promise as it has subsecond synthesis performance for nonlinear vehicle models in scenarios with hundred plus obstacles on standard hardware.", "Keywords": "control synthesis ; switched systems ; cyberphysical systems ; verification ; autonomy", "DOI": "10.1016/j.ifacol.2021.08.493", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Illinois Urbana-Champaign, Champaign, IL 61820 USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "MIT, Cambridge, MA 02139 USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Illinois Urbana-Champaign, Champaign, IL 61820 USA"}], "References": []}, {"ArticleId": 90389366, "Title": "Constrained Optimization for Hybrid System Falsification and Application to Conjunctive Synthesis", "Abstract": "The synthesis problem of a cyber-physical system (CPS) is to find an input signal under which the system’s behavior satisfies a given specification. Our setting is that the specification is a formula of signal temporal logic, and furthermore, that the specification is a conjunction of different and often conflicting requirements. Conjunctive synthesis is often challenging for optimization-based falsification—an established method for CPS analysis that can also be used for synthesis—since the usual framework (especially how its robust semantics handles Boolean connectives) is not suited for finding delicate trade-offs between different requirements. Our proposed method consists of a combination of optimization-based falsification and constrained optimization. Specifically, we show that the state-of-the-art multiple constraint ranking method can be combined with falsification powered by CMA-ES optimization; its performance advantage for conjunctive synthesis is demonstrated in experiments.", "Keywords": "control system synthesis ; cyber-physical system ; constrained optimization ; evolutionary algorithm ; temporal logic ; hybrid system falsification ; search-based testing", "DOI": "10.1016/j.ifacol.2021.08.501", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Informatics, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Informatics, Tokyo, Japan;Graduate School of Informatics, Kyoto University, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Informatics, Tokyo, Japan"}], "References": []}, {"ArticleId": 90389371, "Title": "Robustness of Model Predictive Control to (Large) Discrete Disturbances", "Abstract": "In recent years, theoretical results for model predictive control (MPC) have been expanded to address discrete actuators (decisions) and high-level planning and scheduling problems. The application of MPC-style methods to scheduling problems has been driven, in part, by the robustness afforded by feedback. The ability of MPC, and feedback methods in general, to reject small persistent disturbances is well-recognized. In many planning and scheduling applications, however, we must also consider an additional class of discrete and infrequent disturbances, such as breakdowns and unplanned maintenance. In this paper, we establish that nominal MPC is robust, in a stochastic context, to this class of discrete and infrequent disturbances. We illustrate these results with a nonlinear blending example.", "Keywords": "", "DOI": "10.1016/j.ifacol.2021.08.525", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of Chemical Engineering, Univ. of California, Santa Barbara, CA 93106 USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dept. of Chemical Engineering, Univ. of California, Santa Barbara, CA 93106 USA"}], "References": []}, {"ArticleId": 90389372, "Title": "Investigation of weight updating modes on oxide-based resistive switching memory synapse towards neuromorphic computing applications", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11432-020-3127-x", "PubYear": 2021, "Volume": "64", "Issue": "11", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "Tiancheng Gong", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 6, "Name": "Hangbing Lv", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Institute of Microelectronics, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}], "References": []}, {"ArticleId": 90389424, "Title": "Research on Network Layer Recursive Reduction Model Compression for Image Recognition", "Abstract": "<p>ResNet has been widely used in the field of machine learning since it was proposed. This network model is successful in extracting features from input data by superimposing multiple layers of neural networks and thus achieves high accuracy in many applications. However, the superposition of multilayer neural networks increases their computational cost. For this reason, we propose a network model compression technique that removes multiple neural network layers from ResNet without decreasing the accuracy rate. The key idea is to provide a priority term to identify the importance of each neural network layer, and then select the unimportant layers to be removed during the training process based on the priority of the neural network layers. In addition, this paper also retrains the network model to avoid the accuracy degradation caused by the deletion of network layers. Experiments demonstrate that the network size can be reduced by 24.00%–42.86% of the number of layers without reducing the classification accuracy when classification is performed on CIFAR-10/100 and ImageNet.</p>", "Keywords": "", "DOI": "10.1155/2021/4054435", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering, Huaqiao University, Quanzhou 362021, Fujian, China;Industrial Intelligence and System Fujian University Engineering Research Center, Huaqiao University, Quanzhou 362021, Fujian, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Huaqiao University, Quanzhou 362021, Fujian, China;Industrial Intelligence and System Fujian University Engineering Research Center, Huaqiao University, Quanzhou 362021, Fujian, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering, Huaqiao University, Quanzhou 362021, Fujian, China;Industrial Intelligence and System Fujian University Engineering Research Center, Huaqiao University, Quanzhou 362021, Fujian, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Engineering, Huaqiao University, Quanzhou 362021, Fujian, China;Industrial Intelligence and System Fujian University Engineering Research Center, Huaqiao University, Quanzhou 362021, Fujian, China"}], "References": [{"Title": "Deep packet: a novel approach for encrypted traffic classification using deep learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "1999", "JournalTitle": "Soft Computing"}, {"Title": "Noise-robust image fusion with low-rank sparse decomposition guided by external patch prior", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "523", "Issue": "", "Page": "14", "JournalTitle": "Information Sciences"}, {"Title": "Adaptively accelerating FWM2DA seismic modelling program on multi-core CPU and GPU architectures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "146", "Issue": "", "Page": "104637", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 90389546, "Title": "LATOC: an enhanced load balancing algorithm based on hybrid AHP-TOPSIS and OPSO algorithms in cloud computing", "Abstract": "<p>Providing required level of service quality in cloud computing is one of the most significant cloud computing challenges because of software and hardware complexities, different features of tasks and computing resources and also, lack of appropriate distribution of tasks in cloud computing environments. The recent research in this field show that lack of smart prioritization and ordering of tasks in scheduling (as an NP-hard problem) has been very effective and resulted in lack of load balancing, response time increase, total execution time increase and also, average resource use decrease. In line with this, the proposed method of this research called LATOC considered first the key criteria of an input task like required processing unit, data length of task and execution time. Then, it addressed task prioritization in separate queues using the technique for order preference by similarity to ideal solution (TOPSIS) and analytic hierarchy process (AHP) in figure of a hybrid intelligent algorithm (AHP-TOPSIS). Each ordered task in separate priority queues was placed based on its priority level, and then, to assign each task from each priority queue to virtual machines, optimized particle swarm optimization was used. Many simulations based on various scenarios in Cloudsim simulator show that smart assignment of prioritized tasks by LATOC resulted in improvement of important cloud computing parameters such as total execution time and average resource use comparing similar methods.</p>", "Keywords": "Task scheduling; Cloud computing; Load balancing; Total execution time; AHP-TOPSIS; OPSO", "DOI": "10.1007/s11227-021-04042-6", "PubYear": 2022, "Volume": "78", "Issue": "4", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ACECR Institute of Higher Education (Isfahan Branch), Isfahan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Engineering, Najafabad Branch, Islamic Azad University, Najafabad, Iran;Big Data Research Center, Najafabad Branch, Islamic Azad University, Najafabad, Iran;ACECR Institute of Higher Education (Isfahan Branch), Isfahan, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Engineering, Najafabad Branch, Islamic Azad University, Najafabad, Iran;Big Data Research Center, Najafabad Branch, Islamic Azad University, Najafabad, Iran;ACECR Institute of Higher Education (Isfahan Branch), Isfahan, Iran"}], "References": [{"Title": "PSO-based novel resource scheduling technique to improve QoS parameters in cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12103", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An enhanced AHP–TOPSIS-based load balancing algorithm for switch migration in software-defined networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "1", "Page": "563", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A discrete PSO-based static load balancing algorithm for distributed simulations in a cloud environment", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "497", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "CMODLB: an efficient load balancing approach in cloud computing environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "8", "Page": "8787", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An enhanced AHP-TOPSIS-based clustering algorithm for high-quality live video streaming in flying ad hoc networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "9", "Page": "10664", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 90389552, "Title": "Scalable blockchain storage mechanism based on two-layer structure and improved distributed consensus", "Abstract": "<p>Existing public blockchain architectures suffer from the difficulty of scaling to support large-scale networks with high TPS, low latency and security. Using the idea of network fragmentation, an improved Raft-based PBFT consensus mechanism is proposed to solve the problem. The network nodes are grouped, and the group adopts the improved Raft mechanism for consensus, and then, the leaders elected in each group form the network committee, and the network committee uses the PBFT mechanism for consensus within the network committee. The results show that R-PBFT is more scalable than PBFT and Raft in a large-scale network environment because it can guarantee high consensus efficiency while having Byzantine fault tolerance; similarly, to improve the fairness between user experience and TPS in blockchain systems, based on the transaction fairness model, a fairness packing algorithm is proposed for storing transactions. It is based on a two-level model, firstly sorting the transactions in descending order based on GasPrice, moreover considering the fairness model for descending order. The experimental confirmed that all the performance of fairness packing is superior to Ethereum packing.</p>", "Keywords": "Public blockchain; Consensus mechanism; R-PBFT; Raft; Fairness model", "DOI": "10.1007/s11227-021-04061-3", "PubYear": 2022, "Volume": "78", "Issue": "4", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CAAC Key Laboratory of Civil Aviation Wide Survellence and Safety Operation Management & Control Technology, Tianjin, China;School of Computer Science and Technology, Wuhan University of Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Wuhan University of Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Data Recovery Key Laboratory of Sichuan Province, College of Mathematics and Information Science, Neijiang Normal University, Neijiang, People’s Republic of China"}], "References": [{"Title": "Load balance based workflow job scheduling algorithm in distributed cloud", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "102518", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Resource and replica management strategy for optimizing financial cost and user experience in edge cloud computing system", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "516", "Issue": "", "Page": "33", "JournalTitle": "Information Sciences"}, {"Title": "An effective scheduling strategy based on hypergraph partition in geographically distributed datacenters", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "170", "Issue": "", "Page": "107096", "JournalTitle": "Computer Networks"}, {"Title": "Effective replica management for improving reliability and availability in edge-cloud computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "107", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Mobility and marginal gain based content caching and placement for cooperative edge-cloud computing", "Authors": "Chunlin Li; <PERSON><PERSON>; Chongchong Yu", "PubYear": 2021, "Volume": "548", "Issue": "", "Page": "153", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 90389569, "Title": "Discrete hybrid cuckoo search and simulated annealing algorithm for solving the job shop scheduling problem", "Abstract": "<p>The job shop scheduling problem (JSSP) is a popular NP-hard scheduling problem that simulates the scheduling problems of different real-world applications in fields such as manufacturing, engineering, and planning. In general, many optimization-based scheduling algorithms are guaranteed to provide an optimal solution for small instances of the JSSP, but do not perform well with large instances of the JSSP. The hybrid cuckoo search and simulated annealing (CSA) algorithm is a new hybrid optimization algorithm for solving continuous optimization problems. It incorporates the optimization operators of the simulated annealing algorithm into the cuckoo search algorithm. In this paper, we present discrete CSA (DCSA) which is a discrete variation of CSA for solving the JSSP. DCSA incorporates four modifications into CSA. First, it uses the opposition-based learning method in the initialization step to produce diverse candidate solutions. Second, it uses a combination of the variable neighborhood search (VNS) and Lévy-flight methods for better exploration of the search space. Third, it uses the elite opposition-based learning method before the abandon step of CSA to jump out of local optima. Finally, it uses the smallest position value with the JSSP to convert the continuous candidate solutions generated by CSA into discrete ones. DCSA was examined and compared to six popular optimization-based scheduling algorithms (DABC (<PERSON> et al. in Sci Res Essays 6:2578–2596, 2011), PSO-VNS (<PERSON><PERSON><PERSON><PERSON><PERSON> et al. in Int J Oper Res 3:120–135, 2006), DCS (<PERSON><PERSON><PERSON><PERSON> et al., in: <PERSON> (ed) Recent advances in swarm intelligence and evolutionary computation, Springer, New York, 2015), GW<PERSON> (Jiang and <PERSON> in IEEE Access 6:26231–26240, 2018), DWPA (Wang et al. in 2019 5th International Conference on Control, Automation and Robotics ICCAR 2019 581–585, 2019) and DGA (Kalshetty et al. in J Sci Res 64:310–321, 2020) using 34 JSSP instances from the OR-Library: Fisher and Thompson (3 instances), and Lawrence (31 instances). The experimental and statistical results indicate that DCSA converges faster to the Best-Known Solution for 29 instances out of the 34 tested JSSP instances and requires less computational time for the 34 tested instances than the computational times of the other algorithms.</p>", "Keywords": "Cuckoo search; Simulated annealing; Job shop scheduling problem; Opposition-based learning; Hybrid cuckoo search and simulated annealing algorithm", "DOI": "10.1007/s11227-021-04050-6", "PubYear": 2022, "Volume": "78", "Issue": "4", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Yarmouk University, Irbid, Jordan"}, {"AuthorId": 2, "Name": "Bilal H<PERSON>", "Affiliation": "Department of Computer Sciences, Yarmouk University, Irbid, Jordan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Sciences, Yarmouk University, Irbid, Jordan"}], "References": [{"Title": "Hybrid whale optimization algorithm enhanced with Lévy flight and differential evolution for job shop scheduling problems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "105954", "JournalTitle": "Applied Soft Computing"}, {"Title": "Distributed Grey Wolf Optimizer for scheduling of workflow applications in cloud environments", "Authors": "Bilal <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107113", "JournalTitle": "Applied Soft Computing"}, {"Title": "Hybrid whale optimisation and β-hill climbing algorithm for continuous optimisation problems", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "350", "JournalTitle": "International Journal of Computing Science and Mathematics"}, {"Title": "Chaotic vortex search algorithm: metaheuristic algorithm for feature selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "1777", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "An improved particle swarm optimization with backtracking search optimization algorithm for solving continuous optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> Gharehchopogh", "PubYear": 2022, "Volume": "38", "Issue": "S4", "Page": "2797", "JournalTitle": "Engineering with Computers"}, {"Title": "A modified farmland fertility algorithm for solving constrained engineering problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "17", "Page": "e6310", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Advances in Spotted Hyena Optimizer: A Comprehensive Survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "3", "Page": "1569", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Discrete Jaya with refraction learning and three mutation methods for the permutation flow shop scheduling problem", "Authors": "<PERSON>or <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "3", "Page": "3517", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 90389661, "Title": "Examining theory use in design research on fantasy play", "Abstract": "Play is an essential activity in children’s lives. In the Child Computer Interaction (CCI) field, many authors refer to play and play theories when they describe their work. Play theories can come from many different disciplines, such as psychology, sociology and learning sciences. Theories from different disciplines can provide interpretations and inspiration sources when designing for play. In this study, we explore what theory clusters authors use and how they are used when design researchers report on design work for fantasy play. Based on 19 artefact-centred papers from the ACM digital library from the period 1999–2018, we analyse four components of reported theory use: design intention , design argumentation , design decisions and design evaluation . This paper provides a list of theory clusters that designers report on, showing that different clusters also indicate different conceptualisations of play. Furthermore, it describes three common strategies of theory cluster use: for contextualising the value of play, for highlighting the outcome of play and using design cases as ‘theory’ for supporting making design decisions. The paper concludes by providing reflective questions about how to report on the use of theory in designing for fantasy play. The questions can be used in order for future work in the Child Computer Interaction community to be precise and transparent about theory use in order to make it easier to build upon each other’s work.", "Keywords": "Fantasy play ; Design ; Theory-inspired design ; Fading traceability", "DOI": "10.1016/j.ijcci.2021.100400", "PubYear": 2022, "Volume": "32", "Issue": "", "JournalId": 7619, "JournalTitle": "International Journal of Child-Computer Interaction", "ISSN": "2212-8689", "EISSN": "2212-8697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Design School Kolding, Denmark;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Eindhoven Technical University, Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Eindhoven Technical University, Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Eindhoven Technical University, Netherlands"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Design School Kolding, Denmark"}], "References": []}, {"ArticleId": 90389692, "Title": "Joint sparse principal component regression with robust property", "Abstract": "Standard principal component regression (PCR) selects principal components without any considerations of response variable. Therefore, standard PCR cannot produce a sufficiently accurate model. Sparse principal component regression (SPCR) is a novel one-stage procedure that extracts principal components and constructs a linear regression model simultaneously. Since SPCR can be viewed as a combination of standard principal component regression and sparse principal component analysis (SPCA), it also inherits many drawbacks from them. Consequently, the absolute constraints and the least square loss function make SPCR lack of joint property and robustness. To tackle these problems, this paper proposes joint sparse principal component regression (JSPCR), which can select the important features jointly and estimate the coefficients robustly. Thus, JSPCR can have higher prediction accuracy. To encourage the sparsity of loading matrix, we apply a sparse group penalty and extend JSPCR to joint bi-level sparse PCR (JBSPCR). We derive an alternating optimization criterion and prove its convergence as well. The synthetical data simulations and the real data analysis powerfully support that JSPCR is superior to SPCR and other famous dimensionality reduction procedures or sparse techniques.", "Keywords": "Dimension reduction ; Joint property ; Principal component regression ; Robustness", "DOI": "10.1016/j.eswa.2021.115845", "PubYear": 2022, "Volume": "187", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Mathematics and Statistics, Chongqing University, Chongqing, 401331, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Statistics, Chongqing University, Chongqing, 401331, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Mathematics and Statistics, Chongqing University, Chongqing, 401331, China;Corresponding author"}], "References": []}, {"ArticleId": 90389703, "Title": "<PERSON>, <PERSON><PERSON><PERSON>, Go AI: A practical tutorial on fundamentals of artificial intelligence and its applications in phenomics image analysis", "Abstract": "High-throughput image-based technologies are now widely used in the rapidly developing field of digital phenomics and are generating ever-increasing amounts and diversity of data. Artificial intelligence (AI) is becoming a game changer in turning the vast seas of data into valuable predictions and insights. However, this requires specialized programming skills and an in-depth understanding of machine learning, deep learning, and ensemble learning algorithms. Here, we attempt to methodically review the usage of different tools, technologies, and services available to the phenomics data community and show how they can be applied to selected problems in explainable AI-based image analysis. This tutorial provides practical and useful resources for novices and experts to harness the potential of the phenomic data in explainable AI-led breeding programs.", "Keywords": "algorithms ; deep learning ; explainable artificial intelligence ; image analysis ; integrated development environments ; machine learning ; phenomics ; programming languages ; software frameworks ; software libraries", "DOI": "10.1016/j.patter.2021.100323", "PubYear": 2021, "Volume": "2", "Issue": "9", "JournalId": 74143, "JournalTitle": "Patterns", "ISSN": "2666-3899", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department for Innovation in Biological, Agro-food and Forest systems, University of Tuscia, Via S. Camillo de Lellis, Viterbo 01100, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department for Innovation in Biological, Agro-food and Forest systems, University of Tuscia, Via S. Camillo de Lellis, Viterbo 01100, Italy;Corresponding author"}], "References": [{"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "Julia language in machine learning: Algorithms, applications, and open issues", "Authors": "<PERSON><PERSON> Gao; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "", "Page": "100254", "JournalTitle": "Computer Science Review"}, {"Title": "Image classifiers and image deep learning classifiers evolved in detection of Oryza sativa diseases: survey", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "359", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A practical tutorial on bagging and boosting based ensembles for machine learning: Algorithms, software tools, performance study, practical perspectives and opportunities", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "205", "JournalTitle": "Information Fusion"}, {"Title": "Predicting rice phenotypes with meta and multi-target learning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "11", "Page": "2195", "JournalTitle": "Machine Learning"}, {"Title": "Data augmentation for skin lesion using self-attention based progressive generative adversarial network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113922", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Evaluating local explanation methods on ground truth", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "291", "Issue": "", "Page": "103428", "JournalTitle": "Artificial Intelligence"}, {"Title": "A survey on generative adversarial networks for imbalance problems in computer vision tasks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "27", "JournalTitle": "Journal of Big Data"}, {"Title": "Learning programs by learning from failures", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "4", "Page": "801", "JournalTitle": "Machine Learning"}, {"Title": "Appyters: Turning Jupyter Notebooks into data-driven web apps", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "100213", "JournalTitle": "Patterns"}, {"Title": "Random sketch learning for deep neural networks in edge computing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "3", "Page": "221", "JournalTitle": "Nature Computational Science"}]}, {"ArticleId": 90389728, "Title": "Cache management for large data transfers and multipath forwarding strategies in Named Data Networking", "Abstract": "Named Data Networking (NDN) is a promising approach to provide fast in-network access to compact muon solenoid (CMS) datasets. It proposes a content-centric rather than a host-centric approach to data retrieval. Data packets with unique and immutable names are retrieved from a content store (CS) using Interest packets. The current NDN architecture relies on forwarding strategies that are only dependent upon on-path caching. Such a design does not take advantage of the cached content available on the adjacent off-path routers in the network, thus reducing data transfer efficiency. In this work, we propose a software-defined, storage-aware routing mechanism that leverages NDN router cache-states, software defined networking (SDN) and multipath forwarding strategies to improve the efficiency of very large data transfers. First, we propose a novel distributed multipath (D-MP) forwarding strategy and enhancements to the NDN Interest forwarding pipeline. In addition, we develop a centralized SDN-enabled control for the multipath forwarding strategy (S-MP), which leverages the global knowledge of NDN network states that distributes Interests efficiently. We perform extensive evaluations of our proposed methods on an at-scale wide area network (WAN) testbed spanning six geographically separated sites. Our proposed solutions easily outperform the existing NDN forwarding strategies. The D-MP strategy results in performance gains ranging between 10.4x to 12.5x over the default NDN implementation without in-network caching, and 12.2x to 18.4x with in-network caching enabled. For S-MP strategy, we demonstrate a performance improvement of 10.6x to 12.6x, and 12.9x to 18.5x, with in-network caching disabled and enabled, respectively. Further, we also present a comprehensive analysis of NDN cache management for large data transfers and propose a novel prefetching mechanism to improve data transfer performance. Due to the inherent capacity limitations of the NDN router caches, we use SDN to provide an intelligent and efficient solution for data distribution and routing across multiple NDN router caches. We demonstrate how software-defined control can be used to partition and distribute large CMS files based on NDN router cache-state knowledge. Further, SDN control will also configure the router forwarding strategy to retrieve CMS data from the network. Our proposed solution demonstrates that the CMS datasets can be retrieved 28%–38% faster from the NDN routers’ caches than existing NDN approaches. Lastly, we develop a prefetching mechanism to improve the transfer performance of files that are not available in the router’s cache.", "Keywords": "NDN ; SDN ; Compact Muon Solenoid ; Forwarding strategy ; Cache management", "DOI": "10.1016/j.comnet.2021.108437", "PubYear": 2021, "Volume": "199", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Nebraska-Lincoln, 256 Avery Hall, Lincoln, NE 68588, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Information Technology, Purdue University, Knoy Hall of Technology, 401 N Grant St, West Lafayette, IN 47907, USA;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Nebraska-Lincoln, 256 Avery Hall, Lincoln, NE 68588, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Nebraska-Lincoln, 256 Avery Hall, Lincoln, NE 68588, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Morgridge Institute for Research, University of Wisconsin-Madison, 330 N Orchard Street, Madison, WI 53715, USA"}], "References": []}, {"ArticleId": 90389756, "Title": "Generalized Empirical Regret Bounds for Control of Renewable Energy Systems in Spatiotemporally Varying Environments", "Abstract": "<p>This paper focuses on the empirical derivation of regret bounds for mobile systems that can optimize their locations in real time within a spatiotemporally varying renewable energy resource. The case studies in this paper focus specifically on an airborne wind energy system, where the replacement of towers with tethers and a lifting body allows the system to adjust its altitude continuously, with the goal of operating at the altitude that maximizes net power production. While prior publications have proposed control strategies for this problem, often with favorable results based on simulations that use real wind data, they lack any theoretical or statistical performance guarantees. In the present work, we make use of a very large synthetic data set, identified through parameters from real wind data, to derive probabilistic bounds on the difference between optimal and actual performance, termed regret. The results are presented for a variety of control strategies, including maximum probability of improvement, upper confidence bound, greedy, and constant altitude approaches. In addition, we use dimensional analysis to generalize the aforementioned results to other spatiotemporally varying environments, making the results applicable to a wider variety of renewably powered mobile systems. Finally, to deal with more general environmental mean models, we introduce a novel approach to modify calculable regret bounds to accommodate any mean model through what we term an \"effective spatial domain.\"</p>", "Keywords": "", "DOI": "10.1115/1.4052396", "PubYear": 2022, "Volume": "144", "Issue": "4", "JournalId": 9493, "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control", "ISSN": "0022-0434", "EISSN": "1528-9028", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, North Carolina State University, Raleigh, North Carolina 27607"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, North Carolina State University, Raleigh, North Carolina 27607"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of California, Berkeley, Berkeley, California 94720"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of California, Berkeley, Berkeley, California 94720"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of California, Berkeley, Berkeley, California 94720"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of California, Berkeley, Berkeley, California 94720"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, North Carolina State University, Raleigh, North Carolina 27607"}], "References": []}, {"ArticleId": 90389757, "Title": "Research on adaptive prediction of multimedia data transmission path based on machine learning", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2021.10041045", "PubYear": 2021, "Volume": "19", "Issue": "3", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90389809, "Title": "Nanostructured foam ceramics for building purposes: production technology and applications", "Abstract": "Introduction. Foam-ceramic heat-insulating building materials have the greatest stability of the demanded technological characteristics due to their unique physical and technical properties. Increasing the large-capacity production of nanostructured foam-ceramic products and developing educational programs for advanced training of process engineers remains an urgent task. Methods and materials. A technological method for manufacturing nanostructured foam ceramics is the method of direct foaming: Ceramic foams are created by involving atmospheric air in a suspension. Further, the consolidated foams are carefully dried and sintered for 12 hours by heat treatment (950–1100оC) to obtain sufficiently high-strength foam ceramics for building purposes. The most important raw materials for the production of construction foam ceramics are clays, diatomites, siliceous minerals, zeolite rocks, etc., as well as ceramic and slag waste, and the like. Results. The technology of production of foam-ceramic materials for building purposes based on clay raw materials has been developed. As a result of physical and chemical transformations in the production cycle, including firing, uniformly closed micropores of foam ceramics with a diameter of up to 120 microns are formed, and the wall thickness varies from 1.8 microns to 6.3 microns. The compressive strength of the obtained nanostructured construction foam-ceramic products with an average density of 450–850 kg/m3 is 3–8 MPa, thermal conductivity is 0.12–0.15 W / (m●оC), frost resistance is at least 50 cycles. Discussion. In large-scale technological production, bubbles mass (three-phase foam) can be obtained by mixing nanostructured foam with a highly dispersed mineral powder. By controlled sintering, a dried foam mass is produced with the required technological characteristics due to the crystal bond of a solid-phase mullite based on cluster microparticles with dimensions of 15–200 nm, and the walls of micropores and nodal joints of nanostructured foam ceramics provide high mechanical strength, hydrophobicity and chemical resistance. Conclusions. Nanostructured foam ceramics for building purposes is sufficiently moisture-resistant, since it has a microstructure of closed ultramicropores; it is resistant to chemical and physical effects and therefore is the optimal thermal insulation material. © Sinitsin D.A., Shayakhmetov U.Sh., Rakhimova O.N., Khalikov R.M., Nedoseko I.V., 2021.", "Keywords": "Educational programs; Foam-ceramic building materials; Manufacturing technology; Nanostructured foam ceramics", "DOI": "10.15828/2075-8545-2021-13-4-213-221", "PubYear": 2021, "Volume": "13", "Issue": "4", "JournalId": 36948, "JournalTitle": "Nanotechnologies in Construction: A Scientific Internet-Journal", "ISSN": "", "EISSN": "2075-8545", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ufa State Petroleum Technological University, Ufa, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bashkir State University, Ufa, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Orenburg State University, Kumertau Branch, Kumertau, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ufa State Petroleum Technological University, Ufa, Russian Federation"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ufa State Petroleum Technological University, Ufa, Russian Federation"}], "References": []}, {"ArticleId": 90389860, "Title": "Characteristics of suspended sediment in Sentinel-1 synthetic aperture radar observations", "Abstract": "The main purpose of this study is to quantitatively examine the characteristics of suspended sediment on synthetic aperture radar (SAR) observations of sea roughness. In this work, seven Sentinel-1 (S-1) SAR images in 2019 acquired in the Interferometric Wide-swath (IW) mode at vertical-vertical (VV) polarization channel were collected, where Yangtze River freshwater dominants and suspended sediment concentration (SSC) are higher in continental shelf of East China Sea (ECS). These images were collocated with waves simulated from WAVEWATCH-III (WW3) and SSC inverted from the Haiyang-1C (HY-1C) Coastal Zone Imager (CZI) image. The comparison of WW3-simulated SWH yielded a 0.45-m root-mean-square error (RMSE) with a 0.78 correlation (COR) when comparing the simulations with the measurements from Haiyang-2B (HY-2B). The SAR-measured normalized radar cross-section (NRCS) is linearly related with the HY-1 C SSC at SSC > 1200 mg l<sup>–1</sup>, which is caused by the change of dielectric constant of sea water and viscosity. Moreover, the NRCS is simulated by the three-scale radar backscattering model. It is generally found that HY-1 C SSC enhances the NRCS in difference between simulations and observations up to 8 dB at SSC of 2000 mg l<sup>–1</sup>, which could distort the accuracy of SAR wind retrieval. Acknowledgments We appreciate NCEP for providing the WW3 model free of charge. The S-1 SAR images were provided by ESA via https://scihub.copernicus.eu . ECMWF wind and HYCOM sea surface current were accessed via http://www.ecmwf.int and https://www.hycom.org , respectively. The Chinese HY-1C and HY-2B data were collected via ftp://osdds-ftp.nsoas.org.cn. Additional information Funding This research was partly supported by the National Natural Science Foundation of China under contract Nos. 41976174, and 42076238 and the China Postdoctoral Science Foundation under contract No. 2020M670245;China Postdoctoral Science Foundation [2020M670245];National Natural Science Foundation of China [41976174 and 42076238];", "Keywords": "", "DOI": "10.1080/2150704X.2021.1974119", "PubYear": 2021, "Volume": "12", "Issue": "11", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Marine Sciences, Shanghai Ocean University, Shanghai China;NSOAS, National Satellite Ocean Application Service, Ministry of Natural Resources, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Marine Sciences, Shanghai Ocean University, Shanghai China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NSOAS, National Satellite Ocean Application Service, Ministry of Natural Resources, Beijing, China"}, {"AuthorId": 4, "Name": "Zhanfeng Sun", "Affiliation": "College of Marine Sciences, Shanghai Ocean University, Shanghai China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Communication Engineering, Sun Yat-sen University, Guangzhou China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Communication Engineering, Sun Yat-sen University, Guangzhou China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Marine Science and Technology College, Zhejiang Ocean University, Zhoushan China"}], "References": []}, {"ArticleId": ********, "Title": "Pixel-by-pixel rectification of urban perspective thermography", "Abstract": "A method for rectifying thermograms in perspective captured at human height is proposed. It applies to the image pixel-by-pixel corrections that account for the surface emissivity, the reflected longwave infrared radiation flux, and the radiation emitted and attenuated by the atmosphere. The results are validated in two steps: first by comparison with contact temperature devices and then by comparison with finite element simulations. They give satisfactory results in both cases. The method is illustrated on a canyon-type street located in a dense urban area during a winter period of 24 h. Detailed observation of urban façade surface temperatures opens up new perspectives. Indeed, it allows conclusions to be drawn about the energy behavior of inhabitants by offering a non-intrusive alternative for identifying urban thermal bridges. Furthermore, the combined use of measurement and simulation facilitates the exploration of the thermal and optical properties of the scene. This work contributes significantly to the interpretation of urban metering at human height. It provides essential improvements in the evaluation of errors associated with urban surface temperatures retrieved from remote sensing observations.", "Keywords": "Urban physics ; Thermography ; Surface temperature ; Image processing", "DOI": "10.1016/j.rse.2021.112689", "PubYear": 2021, "Volume": "266", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> y Miño", "Affiliation": "Urban Physics Joint Laboratory, E2S UPPA, Université de Pau et des Pays de l'Adour, Anglet, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Urban Physics Joint Laboratory, E2S UPPA, Université de Pau et des Pays de l'Adour, Anglet, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Urban Physics Joint Laboratory, E2S UPPA, Université de Pau et des Pays de l'Adour, Anglet, France"}], "References": [{"Title": "Atmospheric and emissivity corrections for ground-based thermography using 3D radiative transfer modelling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111524", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Physically Based Simulation and Rendering of Urban Thermography", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON> y Miño", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "377", "JournalTitle": "Computer Graphics Forum"}]}, {"ArticleId": 90389947, "Title": "Corrigendum to “Comparison of Physiological Effects Induced by Two Compression Stockings and Regular Socks During Prolonged Standing Work”", "Abstract": "", "Keywords": "", "DOI": "10.1177/00187208211043821", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [], "References": []}, {"ArticleId": 90389951, "Title": "A new class of elliptic quasi-variational-hemivariational inequalities for fluid flow with mixed boundary conditions", "Abstract": "In this paper we study a class of quasi-variational-hemivariational inequalities in reflexive Banach spaces. The inequalities contain a convex potential, a locally Lipschitz superpotential, and a implicit obstacle set of constraints. Solution existence and compactness of the solution set to the inequality problem are established based on the <PERSON><PERSON><PERSON>–<PERSON> fixed point theorem. The applicability of the results is illustrated by the steady-state Oseen model of a generalized Newtonian incompressible fluid with mixed boundary conditions. The latter involve a unilateral boundary condition, the Navier slip condition, a nonmonotone version of the nonlinear Navier-Fujita slip condition, and a generalization of the threshold slip and leak condition of frictional type.", "Keywords": "Quasi-variational-hemivariational inequality ; Obstacle ; Variational inequality ; Clarke subgradient ; Mosco convergence ; Fixed point", "DOI": "10.1016/j.camwa.2021.08.022", "PubYear": 2021, "Volume": "100", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Applied Mathematics, Chengdu University of Information Technology, Chengdu, 610225, Sichuan Province, PR China;Jagiellonian University, Chair of Optimization and Control, ul<PERSON> <PERSON> 6, 30348 Krakow, Poland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Faculty of Computer Science and Telecommunications, Krakow University of Technology, ul. Warszawska 24, 31155 Krakow, Poland"}], "References": []}, {"ArticleId": 90389952, "Title": "Lazy Symbolic Controller for Continuous-Time Systems Based on Safe Set Boundary Exploration", "Abstract": "In this paper, we present an abstraction-based approach to robust safety controller synthesis for continuous-time nonlinear systems. To reduce the computational complexity associated with symbolic control approaches, we develop a lazy controller synthesis algorithm, which iteratively explores states on the boundary of controllable domain while avoiding exploration of internal states, supposing that they are safely controllable a priory. A closed-loop safety controller for the original problem is then defined as follows: we use the abstract controller to push the system from a boundary state back towards the interior, while for inner states, any admissible input is valid. We then compare the proposed approach with the classical safety synthesis algorithm and illustrate the advantages, in terms of run-time and memory efficiency, on an adaptive cruise control problem.", "Keywords": "Safety specifications ; Lazy controller synthesis ; Symbolic control", "DOI": "10.1016/j.ifacol.2021.08.483", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratoire des Signaux et Systèmes (L2S) CNRS, CentraleSupélec, Université Paris-Sud, Université Paris-Saclay 3, rue Joliot-Curie, 91192 Gif-sur-Yvette, cedex, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratoire des Signaux et Systèmes (L2S) CNRS, CentraleSupélec, Université Paris-Sud, Université Paris-Saclay 3, rue Joliot-Curie, 91192 Gif-sur-Yvette, cedex, France"}], "References": [{"Title": "Lazy Safety Controller Synthesis with Multi-Scale Adaptive-Sampling Abstractions of Nonlinear Systems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "1837", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 90389968, "Title": "On the Quasi-Concavity of Equipment Sharing Games with Perishable Raw Materials", "Abstract": "<p>We study the equipment sharing problem where a group of food & beverage companies share the same equipment of a contractor and wish to have their processing tasks coordinated such that the total cost is minimized. The raw materials to be processed are perishable, which incur a decay cost as time goes. One key issue of this equipment sharing problem is how to allocate the total cost among the participants. We apply cooperative game theory to tackle this issue and formulate the problem as an equipment sharing game. First, we study the 1-equipment sharing game in which all participants share one equipment. We show that the 1-equipment sharing game is quasi-concave when the fixed operation cost is larger than a certain value. We then discuss the special case where the processing time for all participants is equal. For this case, we further investigate the properties of the 1-equipment sharing game and the multi-equipment sharing game. We identify the conditions under which the Shapely value and the τ -value can be easily calculated for the 1-equipment and multi-equipment sharing games.</p>", "Keywords": "Equipment sharing; quasi-concave games; perishable raw materials; decay loss", "DOI": "10.1007/s11518-021-5508-2", "PubYear": 2021, "Volume": "30", "Issue": "6", "JournalId": 22773, "JournalTitle": "Journal of Systems Science and Systems Engineering", "ISSN": "1004-3756", "EISSN": "1861-9576", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Southwest Jiaotong University, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Research Institute of Big Data, Shenzhen, China;School of Data Science, The Chinese University of Hong Kong, Shenzhen, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business, Southern University of Science and Technology, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business, Sichuan Normal University, Chengdu, China"}], "References": []}, {"ArticleId": 90389974, "Title": "Towards Necessary and Sufficient Stability Conditions for NMPC", "Abstract": "The analysis of convergence and stability conditions for Non-linear Model Predictive Control (NMPC) schemes has seen significant progress during the last decades. Numerous results in the literature state sufficient conditions for convergence and/or stability of NMPC. Yet, necessary and sufficient conditions are—to the best of the author’s knowledge—not known and, given the variety of choices for MPC design, seemingly appear to be difficult. This paper analyzes the convergence of NMPC of continuous-time systems in time-invariant settings. We introduce the receding-horizon Hamiltonian—i.e., the value of the optimal control Hamiltonian along the sequence of OCPs—as a novel tool for stability/convergence analysis. Based on the assumption of strict dissipativity, we prove necessary and sufficient conditions for asymptotic convergence of the closed loop to the optimal steady state. Numerical results obtained for the Van de Vusse reactor illustrate the proposed conditions.", "Keywords": "model predictive control ; optimal control ; stability ; asymptotic convergence ; necessary conditions of optimality ; dissipativity", "DOI": "10.1016/j.ifacol.2021.08.536", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Energy Systems, Energy Efficiency and Energy Economics, TU Dortmund University, 44227 Dortmund, Germany"}], "References": []}, {"ArticleId": 90389975, "Title": "Streamlining Active Set Method in MPC using Cache Memory", "Abstract": "This paper investigates how various caching strategies can reduce the computational effort of the active set method (ASM) applied to solve constrained model predictive control problems with quadratic objective function and linear constraints. Specifically, we show that during closed-loop operation, the active set method often re-visits the same combination of active constraints while searching for optimal control inputs by factoring Karush-Kuhn-Tucker (KKT) systems. By storing the factors of the corresponding KKT system in a cache, these repetitive calculations can be simplified to a mere cache search and evaluation of the appropriate factors. Since the cache memory is typically fairly restricted, the efficiency of the scheme depends on how well the cache space can be utilized. In particular, when the cache is fully utilized, and a new element needs to be stored, the cache replacement policy needs to determine which element should be removed from the cache to make space for the new one. In the paper, we scrutinize various cache replacement policies and how well they work as a function of the cache size. The results show that by using a cache of modest size, the number of computational operations performed by the ASM can be reduced by up to 80%, thus significantly accelerating the implementation of model predictive control.", "Keywords": "cache memory ; cache replacement policy ; active set method ; model predictive control", "DOI": "10.1016/j.ifacol.2021.08.543", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information Engineering, Automation and Mathematics, Slovak University of Technology in Bratislava, Slovakia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information Engineering, Automation and Mathematics, Slovak University of Technology in Bratislava, Slovakia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information Engineering, Automation and Mathematics, Slovak University of Technology in Bratislava, Slovakia"}], "References": []}, {"ArticleId": 90389979, "Title": "Augmented obstacle avoidance controller design for mobile robots", "Abstract": "For unicycle robots and an arbitrary continuous reference tracking controller, we propose a mildly invasive obstacle avoidance augmentation only activated in an eye-shaped neighborhood around an obstacle and providing avoidance guarantees. Switching between different control phases is orchestrated by a hybrid automaton with hysteresis mechanisms that prevent Zeno behavior. Since the size of the eye-shaped neighborhood and its orientation depend continuously on the orientation and velocity of the robot, the velocity control input is continuous. Numerical simulations illustrate the performance of an avoidance-augmented tracking controller.", "Keywords": "obstacle avoidance ; reference tracking ; controller design for hybrid systems", "DOI": "10.1016/j.ifacol.2021.08.491", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Engineering, ANU, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dept. of Industrial Engineering, University of Trento, Italy & LAAS-CNRS, Université de Toulouse, CNRS, France"}], "References": [{"Title": "A 3D reactive collision avoidance algorithm for underactuated underwater vehicles", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "6", "Page": "1094", "JournalTitle": "Journal of Field Robotics"}]}, {"ArticleId": 90390108, "Title": "Reset-control-based current tracking for a solenoid with unknown parameters", "Abstract": "We propose a hybrid controller for reference tracking for the output current of a solenoid. The solenoid can be modeled as a first-order linear plant with unknown parameters. The proposed hybrid controller comprises 1) a feed forward action, based on an estimate of the unknown plant parameters exploiting a hybrid formulation of the recursive least-squares method, and 2) a feedback action, exploiting a reset control scheme based on a first-order reset element. We prove stability properties for the closed-loop system and we show through simulations that the current output converges to the reference for sufficiently exciting reference signals.", "Keywords": "", "DOI": "10.1016/j.ifacol.2021.08.498", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Industrial Engineering, University of Trento, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Automation and Control Institute, TU Wien, Austria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Automation and Control Institute, TU Wien, Austria;AIT Austrian Institute of Technology GmbH, Center for Vision, Automation & Control, Vienna, Austria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Industrial Engineering, University of Trento, Italy;LAAS-CNRS, Université de Toulouse, CNRS, Toulouse, France"}], "References": []}, {"ArticleId": 90390122, "Title": "Evaluation of factors affecting dance training effects based on reinforcement learning", "Abstract": "<p>The traditional dance training process lacks a certain degree of scientificity due to the lack of precise motion capture and analysis system, which directly affects the final training effect. In view of the robust limitations of the type 1 fuzzy reinforcement learning flexible structure control system to the uncertainty of noise interference, based on the reinforcement learning algorithm, this paper proposes a flexible structure controller based on the type 2 fuzzy reinforcement learning algorithm. Moreover, this paper uses fuzzy sets with equidistant fuzzy centers to divide large-scale state or continuous state space into two types of fuzzy divisions, divide the action space uniformly, and build fuzzy rules based on the basic ideas of type 1 fuzzy reinforcement learning. In addition, this paper constructs an evaluation system for factors affecting dance training effects based on reinforcement learning and designs experiments to verify the performance of the system. The research results show that the system constructed in this paper meets the theoretical needs and can be applied to dance training practice later.</p>", "Keywords": "Reinforcement learning; Dance training; Movement recognition; Training effect", "DOI": "10.1007/s00521-021-06032-4", "PubYear": 2022, "Volume": "34", "Issue": "9", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Music and Dance College, Huaihua University, Huaihua, China"}], "References": []}, {"ArticleId": 90390131, "Title": "Reachability of Black-Box Nonlinear Systems after Koopman Operator Linearization", "Abstract": "Reachability analysis of nonlinear dynamical systems is a challenging and computationally expensive task. Computing the reachable states for linear systems, in contrast, can often be done efficiently in high dimensions. In this paper, we explore verification methods that leverage a connection between these two classes of systems based on the concept of the <PERSON><PERSON><PERSON> operator. The <PERSON><PERSON><PERSON> operator links the behaviors of a nonlinear system to a linear system embedded in a higher dimensional space, with an additional set of so-called observable variables. Although the new dynamical system has linear differential equations, the set of initial states is defined with nonlinear constraints. For this reason, existing approaches for linear systems reachability cannot be used directly. We propose the first reachability algorithm that deals with this unexplored type of reachability problem. Our evaluation examines several optimizations, and shows the proposed workflow is a promising avenue for verifying behaviors of nonlinear systems.", "Keywords": "Reachability Analysis ; Nonlinear Systems ; Koopman Operator", "DOI": "10.1016/j.ifacol.2021.08.507", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Stony Brook University, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Newcastle University, Newcastle Upon Tyne, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of North Carolina at Chapel Hill, NC, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Aerospace Systems Directorate, United State Air Force Research Laboratory, Wright–Patterson Air Force Base, OH, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Newcastle University, Newcastle Upon Tyne, UK"}], "References": []}, {"ArticleId": 90390132, "Title": "Adaptive Testing for Specification Coverage in CPS Models", "Abstract": "Ensuring correctness of cyber-physical systems (CPS) is a challenging task that is in practice often addressed with simulation-based testing. Formal specification languages, such as Signal Temporal Logic (STL), are used to mathematically express CPS requirements and thus render the simulation activity more principled. We propose a novel method for adaptive generation of tests with specification coverage for STL. To achieve this goal, we devise cooperative reachability games that we combine with numerical optimization to create tests that explore the system in a way that exercise various parts of the specification. To the best of our knowledge our approach is the first adaptive testing approach that can be applied directly to MATLAB™ Simulink/Stateflow models. We implemented our approach in a prototype tool and evaluated it on several illustrating examples and a case study from the avionics domain, demonstrating the effectiveness of adaptive testing to (1) incrementally build a test case that reaches a test objective, (2) generate a test suite that increases the specification coverage, and (3) infer what part of the specification is actually implemented.", "Keywords": "", "DOI": "10.1016/j.ifacol.2021.08.503", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TU Wien, Vienna, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "TU Graz, Graz, Austria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "TU Graz, Graz, Austria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "AIT Austrian Institute of Technology, Vienna, Austria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "AIT Austrian Institute of Technology, Vienna, Austria"}], "References": []}, {"ArticleId": 90390139, "Title": "Theoretical and practical issues in single-machine scheduling with two job release and delivery times", "Abstract": "<p>We study a single-machine scheduling problem with two allowable job release and delivery times. This special case of a strongly NP-hard single-machine scheduling problem with an arbitrary number of job release and delivery times and with the objective to minimize the maximum job completion time remains NP-hard. Nevertheless, it is more transparent and accessible than the general version. Hence, it permitted us to establish some nice properties that yielded simple and efficient solution methods. On the one hand, the restricted setting is useful by its own right since it fits some real-life applications. On the other hand, the presented case study is helpful in the solution of a more general setting with a constant number of job release and delivery times. In particular, the optimality conditions and the heuristics methods that we propose here for the restricted setting might be generalized to the extended setting. The established optimality criteria are explicit conditions under which the restricted problem can be solved optimally in time \\(O(n\\log n)\\) . The extensive computational study showed that at least one of these conditions is satisfied for practically all the randomly generated 50 million problem instances while applying our heuristics to these instances. We report a favorable practical performance of our polynomial-time subroutine that invokes our five heuristics and verifies the proposed optimality conditions consecutively. These conditions were verified for the solutions delivered by the five heuristics individually and in a combined fashion as four pairs of heuristics and as all the five heuristics together. Fifty million problem instances were randomly generated using uniform distribution for each of these ten combinations. For the 50 million instances generated for the last (overall) combination, the schedule created by at least one of the heuristics has satisfied at least one of our optimality conditions (so all these problem instances were solved optimally). We also addressed the (theoretical) possibility that none of our conditions is satisfied, and showed how a known dynamic programming algorithm for SUBSET SUM problem can be used for the solution of the scheduling problem in pseudo-polynomial time. For a considerable part of the tested instances, one of the five scheduling heuristics has solved optimally also SUBSET SUM problem.</p>", "Keywords": "Polynomial and pseudo-polynomial time algorithms; SUBSET SUM; Single-machine scheduling; Release time; Delivery time; Time complexity", "DOI": "10.1007/s10951-021-00708-4", "PubYear": 2021, "Volume": "24", "Issue": "6", "JournalId": 9977, "JournalTitle": "Journal of Scheduling", "ISSN": "1094-6136", "EISSN": "1099-1425", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centro de Investigación en Ciencias, UAEMor, Cuernavaca, Mexico"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centro de Investigación en Ciencias, UAEMor, Cuernavaca, Mexico"}], "References": []}, {"ArticleId": 90390144, "Title": "The impact of a gamified mobile question-asking app on museum visitor group interactions: an ICAP framing", "Abstract": "<p>Mobile devices and apps have become a standard for the museum experience. Many studies have begun to explore the impact mobile apps may have on user experience and informal learning. However, there has been relatively little research on how visitor groups interact collaboratively while using these devices in computer-supported collaborative learning (CSCL) environments. In this paper, we explore the impact of a mobile question-asking app on museum visitor group interactions using the Interactive-Constructive-Active–Passive (ICAP) framework, a hierarchical taxonomy that differentiates modes of cognitive engagement. In a post-hoc analysis of survey findings from a study conducted at two large museums in the American southwest, we found that our app encouraged sharing of information among group members. In addition, users of a gamified version of the app were significantly more likely to report engaging in a group discussion during question-asking than groups using a non-game version of the app. We also found that group collaboration levels depended on the group-designated primary user of the app. Whenever a child or the group collaboratively asked the most questions, group discussion frequency was significantly higher. The study’s findings support mobile question-asking apps’ viability as a means to better understanding of museum visitor groups’ interactions with exhibit content and provide evidence that game-based mobile apps, designed to foster question-asking by visitors, may bolster collaborative group interactions and informal learning.</p>", "Keywords": "Group interaction; Informal learning; Computer-supported collaborative learning; ICAP; Gamification; Mobile apps; Museum; Museum evaluation; Museum apps", "DOI": "10.1007/s11412-021-09350-w", "PubYear": 2021, "Volume": "16", "Issue": "3", "JournalId": 29983, "JournalTitle": "International Journal of Computer-Supported Collaborative Learning", "ISSN": "1556-1607", "EISSN": "1556-1615", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON> Teachers College, Arizona State University, Tempe, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Learning Research and Development Center, University of Pittsburgh, Pittsburgh, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "<PERSON> Teachers College, Arizona State University, Tempe, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "<PERSON> Teachers College, Arizona State University, Tempe, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Earth and Space Exploration, Arizona State University, Tempe, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Earth and Space Exploration, Arizona State University, Tempe, USA"}], "References": []}, {"ArticleId": 90390146, "Title": "An IOMT assisted lung nodule segmentation using enhanced receptive field-based modified UNet", "Abstract": "<p>Smart healthcare systems manage and deal with a large amount of data generated by the Internet of Medical Things (IOMT) devices. This big data processing requires efficient computer-aided diagnostic (CAD) systems. Recently, IOMT-based systems are in use for lung nodule detection especially for remote locations where experts are not available. Lung cancer is one of the critical types of cancer and requires an efficient CAD for early diagnosis of the disease. In the presented research, we proposed two modified UNET architectures based on enhanced receptive fields with the help of atrous convolutions for lung nodule segmentation that can be used with blockchain-assisted IOMT frameworks. Our first framework is based on dual branches and combined with deep residual learning to extract rich features. It employs different scales of pooling to capture information at both local and global levels of context. The second model utilizes naïve inception blocks which consist of parallel convolutions in which different scales of kernels are used to bring out features of distinct size nodules. Moreover, the search space of both models is reduced to lung region of interest (ROI) by utilizing K -means clustering and morphological operators. Both proposed models use atrous convolutions to increase the filter’s field of views. The proposed models achieve 85% and 86.2% dice score on publicly available benchmark LIDC-IDRI dataset and show significant improvement over standard UNET and also outperform all existing published work.</p>", "Keywords": "Lung cancer; Nodule detection; Blockchain-assisted IOMT; Semantic segmentation; Smart healthcare system", "DOI": "10.1007/s00779-021-01637-x", "PubYear": 2024, "Volume": "28", "Issue": "1", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, UET, Taxila, Pakistan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, UET, Taxila, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Attock Campus, Attock, Pakistan"}], "References": [{"Title": "Dual-branch residual network for lung nodule segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105934", "JournalTitle": "Applied Soft Computing"}, {"Title": "Medical Diagnosis Using Machine Learning: A Statistical Review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "1", "Page": "107", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A Comprehensive Review on Medical Diagnosis Using Machine Learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "2", "Page": "1997", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 90390171, "Title": "PredMS: a random forest model for predicting metabolic stability of drug candidates in human liver microsomes", "Abstract": "Motivation <p>Poor metabolic stability leads to drug development failure. Therefore, it is essential to evaluate the metabolic stability of small compounds for successful drug discovery and development. However, evaluating metabolic stability in vitro and in vivo is expensive, time-consuming and laborious. In addition, only a few free software programs are available for metabolic stability data and prediction. Therefore, in this study, we aimed to develop a prediction model that predicts the metabolic stability of small compounds.</p> Results <p>We developed a computational model, PredMS, which predicts the metabolic stability of small compounds as stable or unstable in human liver microsomes. PredMS is based on a random forest model using an in-house database of metabolic stability data of 1917 compounds. To validate the prediction performance of PredMS, we generated external test data of 61 compounds. PredMS achieved an accuracy of 0.74, <PERSON>’s correlation coefficient of 0.48, sensitivity of 0.70, specificity of 0.86, positive predictive value of 0.94 and negative predictive value of 0.46 on the external test dataset. PredMS will be a useful tool to predict the metabolic stability of small compounds in the early stages of drug discovery and development.</p> Availability and implementation <p>The source code for PredMS is available at https://bitbucket.org/krictai/predms, and the PredMS web server is available at https://predms.netlify.app.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btab547", "PubYear": 2022, "Volume": "38", "Issue": "2", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biotechnology, Duksung Women’s University, Seoul 01369, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Data Convergence Drug Research Center, Korea Research Institute of Chemical Technology, 34114 Daejeon, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Data Convergence Drug Research Center, Korea Research Institute of Chemical Technology, 34114 Daejeon, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Data Convergence Drug Research Center, Korea Research Institute of Chemical Technology, 34114 Daejeon, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Data Convergence Drug Research Center, Korea Research Institute of Chemical Technology, 34114 Daejeon, Republic of Korea;Department of Medicinal and Pharmaceutical Chemistry, University of Science and Technology, Daejeon 34129, Republic of Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Data Convergence Drug Research Center, Korea Research Institute of Chemical Technology, 34114 Daejeon, Republic of Korea;Department of Medicinal and Pharmaceutical Chemistry, University of Science and Technology, Daejeon 34129, Republic of Korea"}], "References": [{"Title": "DeepHIT: a deep learning framework for prediction of hERG-induced cardiotoxicity", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "10", "Page": "3049", "JournalTitle": "Bioinformatics"}, {"Title": "LightBBB: computational prediction model of blood–brain-barrier penetration based on LightGBM", "Authors": "B<PERSON>l Shaker; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "8", "Page": "1135", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 90390198, "Title": "Prediction of Sovereign Credit Risk Rating Using Sensing Technology", "Abstract": "In recent years, the study and application of sensor technology have expanded from the industrial to the commercial, financial, and even medical fields. In Taiwan, many studies link sensor technology and the Internet of Things (IoT) with commercial finance, and research on their applications to commercial financial warning or company management is being conducted. The cross-discipline integration of IoT and finance has advanced through the diversification of IoT, and that is why the financial service industry will use IoT as a service platform to provide cross-disciplinary integration services. After exchanging and collecting information by using sensors and handheld devices, it helps to develop new business models such as supply chain financial services to share and manage networks. This enables the financial industry to have a more comprehensive and different understanding of customers and enterprises, which can help the financial industry explore different business opportunities. The aim of this study is to use IoT technology to perform real-time analysis of treasury bill big data and to apply the principles behind sensing to predict and analyze the probability of international defaults in order to reduce investment risks and estimate the credit default swap (CDS) spread. We will then compare the results for the macroeconomic variables with the results of relevant analyses on indicators. The results of the correlation analysis show that the CDS spread and the ratio of the current account balance (CAB) to the gross domestic product have a negative correlation with the gross domestic product growth (GDPG) rate. It has a positive correlation with the inflation rate (INF), the ratio of government debt to gross domestic product (DEBT), and the industrial production index annual growth rate (IPIG). Therefore, the result and the rating-implied expected loss (RIEL) are the same as those in the relevant analysis results of these five macroeconomic indicators. It was also found that IoT technology can be used in the real-time analysis of large-scale treasury bill data, and the principle of sensing can be applied to predict and analyze the accuracy of international defaults. © MYU K.K.", "Keywords": "CDS spread; Internet of Things (IoT); Sovereign credit rating", "DOI": "10.18494/SAM.2021.3244", "PubYear": 2021, "Volume": "33", "Issue": "9", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Hospitality, Meiho University, No. 23, Pingguang Rd., Neipu Township, Pingtung County, 912009, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering, I-Shou University, No. 1, Sec. 1, Syuecheng Rd., Dashu District, Kaohsiung City, 84001, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, I-Shou University, No. 1, Sec. 1, Syuecheng Rd., Dashu District, Kaohsiung City, 84001, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, I-Shou University, No. 1, Sec. 1, Syuecheng Rd., Dashu District, Kaohsiung City, 84001, Taiwan"}, {"AuthorId": 5, "Name": "En-Der Su", "Affiliation": "Department of Risk Management and Insurance, National Kaohsiung University of Science and Technology, No. 1, University Rd., Yanchao District, Kaohsiung City, 824, Taiwan"}], "References": []}, {"ArticleId": 90390253, "Title": "Kernel-Target Alignment Based Fuzzy Lagrangian Twin Bounded Support Vector Machine", "Abstract": "<p>To improve the generalization performance, we develop a new technique for handling the impacts of outliers using Lagrangian twin bounded SVM (TBSVM) with kernel fuzzy membership values, which is termed kernel-target alignment-based fuzzy Lagrangian twin bounded support vector machine (KTA-FLTBSVM). Here, the objective functions are having L2-norm vectors of the slack variable that leads to the optimization problem more convex and yields a unique global solution. Also, the fuzzy membership values are employing the importance of data samples assigned to each sample to minimize the impacts of outlier and noise. Further, we have suggested a linearly convergent iterative approach to obtain the solution of the problem unlike in place to solve the quadratic programming problem in Twin SVM (TSVM) and TBSVM. To investigate the effectiveness of the proposed KTA-FLTBSVM, the comprehensive experiments demonstrate with other reported models on artificial datasets along with benchmark real-life publicly available datasets. Our KTA-FLTBSVM outperforms to other models in terms of better classification accuracy.</p>", "Keywords": "", "DOI": "10.1142/S021848852150029X", "PubYear": 2021, "Volume": "29", "Issue": "5", "JournalId": 15072, "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems", "ISSN": "0218-4885", "EISSN": "1793-6411", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Arunachal Pradesh, Arunachal Pradesh 791112, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Arunachal Pradesh, Arunachal Pradesh 791112, India"}], "References": []}, {"ArticleId": 90390279, "Title": "Carbon nanodot-hybridized silica nanospheres assisted immunoassay for sensitive detection of Escherichia coli", "Abstract": "Carbon dots (CDs) are intrinsically luminescent nanomaterials that have many potential applications in biosensing technologies due in part to their high photostability and fluorescent properties. However, attempts to integrate CDs into immunosorbent assays have been deterred by challenges preventing quantum yield augmentation and surface functionalization. To address these issues, we fabricated carbon dot-encapsulated silica nanospheres (CSNs) using a facile one-pot synthesis method. The enclosing silica matrix rendered extensive material stability to the nanospheres and abated quenching by nonradiative decay to cause considerable signal amplification. Nanosphere hybridization to antibodies permitted their use in solid-phase immunoassays as tracers. Here, we demonstrate the suitability of CSN-based immunosorbent assays as a point-of-care technique for the detection of foodborne pathogens through E. coli O157:H7 quantitation experiments. After optimization, detection limit of E. coli O157:H7 was determined to be 2.4 CFU mL<sup>−1</sup>. The estimated recoveries were in the range of 91.7–110.5% in spiked samples, which indicated that the developed method is capable for detecting E. coli O157:H7 in food samples. The nanosphere tracers described herein, and the methods used to create them, may be beneficial tools for the development of new pathogen biosensing strategies.", "Keywords": "Carbon nanodot ; Silica nanospheres ; ELISA ; Immunoassay ; High quantum yield ; E. coli O157:H7", "DOI": "10.1016/j.snb.2021.130730", "PubYear": 2021, "Volume": "349", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Materials Engineering, Washington State University, Pullman, WA 99164, United States;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Biological Sciences, Washington State University, Pullman, WA 99164, United States;Outpatient Heart & Vascular/Endoscopy, St. Joseph Regional Medical Center, Lewiston, ID 83501, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Materials Engineering, Washington State University, Pullman, WA 99164, United States"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Materials Engineering, Washington State University, Pullman, WA 99164, United States;Corresponding authors"}], "References": []}, {"ArticleId": 90390331, "Title": "Dual-functional sensing properties of ZnFe2O4 nanoparticles for detection of the chloramphenicol antibiotic and sulphur dioxide gas", "Abstract": "Zinc Ferrite (ZnFe<sub>2</sub>O<sub>4</sub>, ZFO) nanoparticles (NPs) as a multi-sensing material are successfully synthesised via a hydrothermal method and a following annealing treatment. The as-prepared ZFO-NPs were applied in electrochemical and mass-type sensors by depositing NPs directly on the screen-printed electrode (SPE) and the active electrode of a quartz crystal microbalance (QCM), respectively. The obtained results show the ZFO-NPs possessed excellent detection performance to sulphur dioxide (SO<sub>2</sub>) in the range of 2.5–20 ppm at room temperature. QCM sensor coated with ZFO-NPs showed a high response, good repeatability, as well as short-response time. Moreover, the modified-SPE with ZFO-NPs sensitively responded to chloramphenicol (CAP) of concentration range from 5 to 50 μM in phosphate buffer solution (PBS). The sensing mechanism of ZFO-NPs based sensors towards SO<sub>2</sub> and CAP was also discussed. This study demonstrates that ZFO-NPs could be known as a potential sensor material capable of detecting SO<sub>2</sub> gas and CAP quickly in both mass-type gas sensors and electrochemical sensors.", "Keywords": "Electrochemical sensor ; QCM ; ZnFe<sub>2</sub>O<sub>4</sub> ; SO<sub>2</sub> ; CAP", "DOI": "10.1016/j.sna.2021.113093", "PubYear": 2021, "Volume": "332", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "International Training Institute for Materials Science, Ha Noi University of Science and Technology, No. 1 Dai Co Viet, Ha Ba Trung District, Hanoi, Viet Nam;University of Transport Technology, No. 54 Trieu Khuc, Thanh Xuan District, Hanoi, Viet Nam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "International Training Institute for Materials Science, Ha Noi University of Science and Technology, No. 1 Dai Co Viet, Ha Ba Trung District, Hanoi, Viet Nam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "International Training Institute for Materials Science, Ha Noi University of Science and Technology, No. 1 Dai Co Viet, Ha Ba Trung District, Hanoi, Viet Nam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "International Training Institute for Materials Science, Ha Noi University of Science and Technology, No. 1 Dai Co Viet, Ha Ba Trung District, Hanoi, Viet Nam"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Phenikaa University Nano Institute, Phenikaa University, Ha Dong District, Hanoi, Viet Nam"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Nhung <PERSON>", "Affiliation": "Phenikaa University Nano Institute, Phenikaa University, Ha Dong District, Hanoi, Viet Nam"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Phenikaa University Nano Institute, Phenikaa University, Ha Dong District, Hanoi, Viet Nam"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate University of Science and Technology, Vietnam Academy of Science and Technology, No. 18 Hoang Quoc Viet, Cau Giay District, Hanoi, Viet Nam"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Materials Science, Vietnam Academy of Science and Technology, No. 18 Hoang Quoc Viet, Nghia Do District, Hanoi, Viet Nam"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Materials Science, Vietnam Academy of Science and Technology, No. 18 Hoang Quoc Viet, Nghia Do District, Hanoi, Viet Nam"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Tropical Technology, Vietnam Academy of Science and Technology, No. 18 Hoang Quoc Viet, Nghia Do District, Hanoi, Viet Nam"}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "International Training Institute for Materials Science, Ha Noi University of Science and Technology, No. 1 Dai Co Viet, Ha Ba Trung District, Hanoi, Viet Nam;Corresponding author"}], "References": []}, {"ArticleId": 90390380, "Title": "Algorithm selection and instance space analysis for curriculum-based course timetabling", "Abstract": "<p>We propose an algorithm selection approach and an instance space analysis for the well-known curriculum-based course timetabling problem (CB-CTT), which is an important problem for its application in higher education. Several state of the art algorithms exist, including both exact and metaheuristic methods. Results of these algorithms on existing instances in the literature show that there is no single algorithm outperforming the others. Therefore, a deep analysis of the strengths and weaknesses of these algorithms, depending on the instance, is an important research question. In this work, a detailed analysis of the instance space for CB-CTT is performed, charting the regions where these algorithms perform best. We further investigate the application of machine learning methods to automated algorithm selection for CB-CTT, strengthening the insights gained through the instance space analysis. For our research, we contribute new real-life instances and extend the generation of synthetic instances to better correspond to these new instances. Finally, this work shows how instance space analysis and the application of algorithm selection complement each other, underlining the value of both approaches in understanding algorithm performance.</p>", "Keywords": "Timetabling; Scheduling; Algorithm selection; Classification; Instance space; Instance generation", "DOI": "10.1007/s10951-021-00701-x", "PubYear": 2022, "Volume": "25", "Issue": "1", "JournalId": 9977, "JournalTitle": "Journal of Scheduling", "ISSN": "1094-6136", "EISSN": "1099-1425", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Database and Artificial Intelligence Group, Institute of Logic and Computation, TU Wien, Vienna, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Christian <PERSON>ler Laboratory for Artificial Intelligence and Optimization for Planning and Scheduling DBAI, TU Wien, Vienna, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "DPIA, University of Udine, Udine, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Database and Artificial Intelligence Group, Institute of Logic and Computation, TU Wien, Vienna, Austria"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Statistics, The University of Melbourne, Melbourne, Australia"}], "References": []}, {"ArticleId": 90390432, "Title": "Hybrid neural networks based facial expression recognition for smart city", "Abstract": "<p>With the development of science and technology and the progress of human beings, intelligence is gradually integrated into human daily life. The smart city uses innovative technology to manage and operate cities intelligently. Through the research of facial expression recognition technology, this paper explores the application of facial expression recognition in smart city construction. In this paper, a hybrid neural network structure is proposed, which includes Sparse Autoencoder and Convolutional Neural Network (SCNN). The network reconstructs the input data by Sparse Autoencoder, so as to learn the approximate value between the original data and the reconstructed data, and obtain more high-dimensional abstract features. Then, combined with the Convolutional Neural Network, the features are further extracted and dimensionally reduced. The model can effectively solve the problem that the shallow network structure can not fully extract image features and train the model with a small number of samples. In this paper, CK+, FER2013 and Oulu-CASIA databases are used for Cross-Validation of the model. The experimental results show that the model has achieved good results in both databases. Compared with other methods, the accuracy of this model has been greatly improved.</p>", "Keywords": "Smart city; Facial expression recognition; Sparse Autoencoder (SAE); Convolutional Neural Network (CNN)", "DOI": "10.1007/s11042-021-11530-7", "PubYear": 2022, "Volume": "81", "Issue": "1", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Lingyu Yan", "Affiliation": "School of Computer Science, Hubei University of Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Hubei University of Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Hubei University of Technology, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Hubei University of Technology, Wuhan, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Wuhan Fiberhome Technical Services Co_,Ltd, Fiberhome Telecommunication Technologies Co_,Ltd, Wuhan, China"}], "References": []}, {"ArticleId": 90390468, "Title": "Mapping Black London in World War II: A staged approach to digital spatial history", "Abstract": "<p>This article suggests ways scholars may be able to deploy digital techniques in contexts without readily available quantitative data. Using the Mapping Black London (MBL) project in World War II as a case study, the article proposes ways scholars can ‘reverse engineer’ qualitative information in order to generate the data needed to make meaningful digital maps. The MBL project conducted a staged approach to digitally map the black presence in London during World War II. In contrast to other phased approaches where work typically begins with a lengthy stage of database construction and data entry followed by ‘traditional’ analysis toward the end, MBL sought to minimize the lengthy and costly lead times common to many Historic Geographic Information Systems (HGIS) projects. Work progressed through three alternative stages where the MBL team ‘mapped the topic’, before mapping selected aspects of the secondary literature and primary evidence. This article discusses the benefits drawn from such an approach, as well as suggesting some circumstances where similar methods might be deployed by others seeking to ‘reverse engineer’ qualitative sources to create digital maps.</p>", "Keywords": "", "DOI": "10.1093/llc/fqab085", "PubYear": 2022, "Volume": "37", "Issue": "4", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of History, Northeastern University, Boston, MA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of History, New College of the Humanities at Northeastern, London, UK"}], "References": []}, {"ArticleId": 90390506, "Title": "DETECTION OF PHISHING WEBSITE USING MACHINE LEARNING AL<PERSON><PERSON><PERSON><PERSON><PERSON> AND DEPLOYMENT USING FAST API", "Abstract": "", "Keywords": "", "DOI": "10.26634/jcom.8.4.18143", "PubYear": 2021, "Volume": "8", "Issue": "4", "JournalId": 43475, "JournalTitle": "i-manager's Journal on Computer Science", "ISSN": "2347-2227", "EISSN": "2347-6141", "Authors": [{"AuthorId": 1, "Name": "P. MOHAMMED ISMAIL", "Affiliation": "Department of Computer Science & Engineering, SRM Valliammai Engineering College, Chennai, India."}, {"AuthorId": 2, "Name": "R K. VIJAYRAAJESH", "Affiliation": "Department of Computer Science & Engineering, SRM Valliammai Engineering College, Chennai, India."}, {"AuthorId": 3, "Name": "V. DHANAKOTI", "Affiliation": "Department of Computer Science & Engineering, SRM Valliammai Engineering College, Chennai, India."}, {"AuthorId": 4, "Name": "J. <PERSON>", "Affiliation": "Department of Computer Science & Engineering, SRM Valliammai Engineering College, Chennai, India."}], "References": []}, {"ArticleId": 90390527, "Title": "A survey on deep learning for challenged networks: Applications and trends", "Abstract": "Computer networks are dealing with growing complexity, given the ever-increasing volume of data produced by all sorts of network nodes. Performance improvements are a non-stop ambition and require tuning fine-grained details of the system operation. Analyzing such data deluge, however, is not straightforward and sometimes not supported by the system. There are often problems regarding scalability and the predisposition of the involved nodes to understand and transfer the data. This issue is at least partially circumvented by knowledge acquisition from past experiences, which is a characteristic of the herein called “challenged networks”. The addition of intelligence in these scenarios is fundamental to extract linear and non-linear relationships from the data collected by multiple sources. This is undoubtedly an invitation to machine learning and, more particularly, to deep learning. This paper identifies five different challenged networks: IoT and sensor, mobile, industrial, and vehicular networks as typical scenarios that may have multiple and heterogeneous data sources and face obstacles concerning connectivity. As a consequence, deep learning solutions can contribute to system performance by adding intelligence and the ability to interpret data. We start the paper by providing an overview of deep learning, further explaining this approach’s benefits over the cited scenarios. We propose a workflow based on our observations of deep learning applications over challenged networks, and based on it, we strive to survey the literature on deep-learning-based solutions at an application-oriented level using the PRISMA methodology. Afterward, we also discuss new deep learning techniques that show enormous potential for further improvements as well as transversal issues, such as security. Finally, we provide lessons learned raising trends linking all surveyed papers to deep learning approaches. We are confident that the proposed paper contributes to the state of the art and can be a piece of inspiration for beginners and also for enthusiasts on advanced networking research.", "Keywords": "Challenged networks ; Internet of Things ; Sensor networks ; Industrial networks ; Wireless mobile networks ; Vehicular networks ; Deep learning ; Machine learning", "DOI": "10.1016/j.jnca.2021.103213", "PubYear": 2021, "Volume": "194", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Grupo de Teleinformática e Automação – GTA, PEE/COPPE-DEL/POLI, Universidade Federal do Rio de Janeiro – UFRJ, Rio de Janeiro, RJ, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Grupo de Teleinformática e Automação – GTA, PEE/COPPE-DEL/POLI, Universidade Federal do Rio de Janeiro – UFRJ, Rio de Janeiro, RJ, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Grupo de Teleinformática e Automação – GTA, PEE/COPPE-DEL/POLI, Universidade Federal do Rio de Janeiro – UFRJ, Rio de Janeiro, RJ, Brazil"}, {"AuthorId": 4, "Name": "Mariana S.M<PERSON>", "Affiliation": "Grupo de Teleinformática e Automação – GTA, PEE/COPPE-DEL/POLI, Universidade Federal do Rio de Janeiro – UFRJ, Rio de Janeiro, RJ, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal Fluminense – UFF, PPGEET, MídiaCom, Niterói, RJ, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Grupo de Teleinformática e Automação – GTA, PEE/COPPE-DEL/POLI, Universidade Federal do Rio de Janeiro – UFRJ, Rio de Janeiro, RJ, Brazil"}], "References": [{"Title": "Machine Learning Methods for Reliable Resource Provisioning in Edge-Cloud Computing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Resource slicing and customization in RAN with dueling deep Q-Network", "Authors": "<PERSON><PERSON> Sun; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "157", "Issue": "", "Page": "102573", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "A Survey on Distributed Machine Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Machine learning based solutions for security of Internet of Things (IoT): A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "102630", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "An Unsupervised Approach to Infer Quality of Service for Large-Scale Wireless Networking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "4", "Page": "1228", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "Detecting Internet of Things attacks using distributed deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "163", "Issue": "", "Page": "102662", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Orchestrating the Development Lifecycle of Machine Learning-based IoT Applications", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deep Learning on Mobile and Embedded Devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Pseudoinverse learning autoencoder with DCGAN for plant diseases classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "35-36", "Page": "26245", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Security and Privacy in IoT Using Machine Learning and Blockchain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "DP-GAN: Differentially private consecutive data publishing using generative adversarial nets", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "103066", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "A Systematic Review of Quality of Service in Wireless Sensor Networks using Machine Learning: Recent Trend and Future Vision", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "188", "Issue": "", "Page": "103084", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 90390700, "Title": "Compatibilization of polymer mixtures during processing of waste products from thermoplastics", "Abstract": "", "Keywords": "", "DOI": "10.15828/2075-8545-2021-13-4-229-236", "PubYear": 2021, "Volume": "13", "Issue": "4", "JournalId": 36948, "JournalTitle": "Nanotechnologies in Construction: A Scientific Internet-Journal", "ISSN": "", "EISSN": "2075-8545", "Authors": [{"AuthorId": 1, "Name": "N.N. <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90390762, "Title": "On the Stability of Switched ARX Models, with an Application to Learning via Regression Trees", "Abstract": "This work studies the stability properties of Switched AutoRegressive eXogenous (SARX) models subject to arbitrary switching sequences. We provide necessary and sufficient conditions for the arbitrary switching stability of multiple-input single-output SARX models under nonnegativity constraints, and sufficient-only conditions removing sign constraints. The conditions are equivalently formulated on state-space representations of SARX models, due to their influential use in designing control strategies. As an application of the aforementioned results, we propose a novel algorithm for the identification of switched models with stability guarantees via Regression Trees, a powerful machine learning technique.", "Keywords": "Stability of switched systems ; System identification ; Regression Trees", "DOI": "10.1016/j.ifacol.2021.08.475", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dipartimento di Ingegneria e Scienze dell’Informazione, e Matematica, Università degli Studi dell’Aquila, Via Vetoio, 67100 Coppito (AQ), Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dipartimento di Ingegneria e Scienze dell’Informazione, e Matematica, Università degli Studi dell’Aquila, Via Vetoio, 67100 Coppito (AQ), Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dipartimento di Ingegneria e Scienze dell’Informazione, e Matematica, Università degli Studi dell’Aquila, Via Vetoio, 67100 Coppito (AQ), Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Dipartimento di Ingegneria e Scienze dell’Informazione, e Matematica, Università degli Studi dell’Aquila, Via Vetoio, 67100 Coppito (AQ), Italy"}], "References": [{"Title": "On the stability of discrete-time linear switched systems in block companion form", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "2033", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 90390817, "Title": "Handwritten CAPTCHA recognizer: a text CAPTCHA breaking method based on style transfer network", "Abstract": "<p>The CAPTCHA technology can be used to ensure big multimedia data security, which includes CAPTCHA design and CAPTCHA recognition. For the existing methods are difficult to achieve high breaking accuracy for complex handwritten text CAPTCHA, a handwritten CAPTCHA recognizer is proposed, which is a text CAPTCHA breaking method based on style transfer network. Firstly, different from the traditional viewpoints that font structure and font style of characters are inseparable in this field, a new idea of separating font structure and font style of characters is proposed, and it is pointed out that character recognition mainly depends on font structure rather than font style. Secondly, based on this idea, a style transfer network for text CAPTCHA is constructed to convert complex and variable handwritten CAPTCHA into easy-to-recognize printed CAPTCHA. Finally, based on deep convolutional neural network, a text CAPTCHA recognition network is constructed to identify the converted printed CAPTCHAs. With CAPTCHAs from three real websites: eBay, Google and reCAPTCHA, experimental results show that the recognizer has higher breaking accuracy for handwritten CAPTCHA compared with the methods proposed in NDSS’16, CCS’18 and “Science” in 2017.</p>", "Keywords": "Big multimedia data Security; Text CAPTCHA breaking; Style transfer network; Handwritten; Print", "DOI": "10.1007/s11042-021-11485-9", "PubYear": 2023, "Volume": "82", "Issue": "9", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Nanjing Sport Institute, Nanjing, China"}, {"AuthorId": 2, "Name": "Xiangyang Luo", "Affiliation": "State Key Laboratory of Mathematical Engineering and Advanced Computing, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mathematical Engineering and Advanced Computing, Zhengzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhengzhou University of Light Industry, Zhengzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Zhengzhou University of Light Industry, Zhengzhou, China"}], "References": [{"Title": "An efficient blockchain-based privacy preserving scheme for vehicular social networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "540", "Issue": "", "Page": "308", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 90390828, "Title": "Normalish Amenable Subgroups of the R. Thompson Groups", "Abstract": "<p>Results in [Formula: see text] algebras, of <PERSON><PERSON> and <PERSON>, and of <PERSON><PERSON><PERSON> and <PERSON>, apply to the <PERSON><PERSON> groups [Formula: see text]. These results together show that [Formula: see text] is non-amenable if and only if [Formula: see text] has a simple reduced [Formula: see text]-algebra.</p><p>In further investigations into the structure of [Formula: see text]-algebras, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> introduce the notion of a normalish subgroup of a group [Formula: see text]. They show that if a group [Formula: see text] admits no non-trivial finite normal subgroups and no normalish amenable subgroups then it has a simple reduced [Formula: see text]-algebra. Our chief result concerns the <PERSON><PERSON> Thompson groups [Formula: see text]; we show that there is an elementary amenable group [Formula: see text] [where here, [Formula: see text]] with [Formula: see text] normalish in [Formula: see text].</p><p>The proof given uses a natural partial action of the group [Formula: see text] on a regular language determined by a synchronising automaton in order to verify a certain stability condition: once again highlighting the existence of interesting intersections of the theory of [Formula: see text] with various forms of formal language theory.</p>", "Keywords": "", "DOI": "10.1142/S0129054121420089", "PubYear": 2021, "Volume": "32", "Issue": "6", "JournalId": 9642, "JournalTitle": "International Journal of Foundations of Computer Science", "ISSN": "0129-0541", "EISSN": "1793-6373", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of St Andrews, St Andrews, Scotland, UK, KY16 9SS, UK"}], "References": []}, {"ArticleId": 90391061, "Title": "Exploring online green behavior among college students in Taiwan: A moderated mediation model of perceived compatibility", "Abstract": "<p>Although there are many studies related to offline green behavior, limited efforts have been devoted to investigating key elements that would lead to better online green behavior. Accordingly, the primary purpose of this study is not only to examine the key elements that could result in better online green behavior, but also to investigate the moderating role of perceived compatibility in green continuance intention. 553 college students from different universities took part in this study. The partial least squares structural equation modelling (PLS-SEM) technique and SPSS PROCESS (model 7) were utilized not only to probe into the key factors that would lead to better green continuance intention, but also to examine the moderating impact of perceived compatibility on green continuance intention. The study findings have revealed that subjective norm, perceived usefulness, and green product satisfaction would be positively linked to green continuance intention. Additionally, it has been found that environmental awareness and personal innovativeness have a positive impact on perceived usefulness. Finally, the study findings have shown that perceived compatibility would play a moderating role in reinforcing the relationship between perceived usefulness and green product satisfaction.</p>", "Keywords": "Environmental issue; Green continuance intention; Green behavior; Perceived compatibility; Pro-environmental products", "DOI": "10.1007/s11042-021-11526-3", "PubYear": 2022, "Volume": "81", "Issue": "1", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Chung Hsing University, Taichung, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Chung Hsing University, Taichung, Taiwan"}], "References": []}, {"ArticleId": 90391127, "Title": "Dynamic Detection of Mobile Malware Using Smartphone Data and Machine Learning", "Abstract": "<p>Mobile malware are malicious programs that target mobile devices. They are an increasing problem, as seen with the rise of detected mobile malware samples per year. The number of active smartphone users is expected to grow, stressing the importance of research on the detection of mobile malware. Detection methods for mobile malware exist but are still limited.</p><p>In this article, we provide an overview of the performance of machine learning (ML) techniques to detect malware on Android, without using privileged access. The ML-classifiers use device information such as the CPU usage, battery usage, and memory usage for the detection of 10 subtypes of Mobile Trojans on the Android Operating System.</p><p>We use a real-life dataset containing device and malware data from 47 users for a year (2016). We examine which features, i.e., aspects, of a device, are most important to monitor to detect (subtypes of) Mobile Trojans. The focus of this article is on dynamic hardware features. Using these dynamic features we apply state-of-the-art machine learning classifiers: Random Forest, K-Nearest Neighbour, and AdaBoost. We show classification results on different feature sets, making a distinction between global device features, and specific app features. None of the measured feature sets require privileged access.</p><p>Our results show that the Random Forest classifier performs best as a general malware classifier: across 10 subtypes of Mobile Trojans, it achieves an F1 score of 0.73 with a False Positive Rate (FPR) of 0.009 and a False Negative Rate (FNR) of 0.380. The Random Forest, K-Nearest Neighbours, and AdaBoost classifiers achieve F1 scores above 0.72, an FPR below 0.02 and, an FNR below 0.33, when trained separately to detect each subtype of Mobile Trojans.</p>", "Keywords": "", "DOI": "10.1145/3484246", "PubYear": 2022, "Volume": "3", "Issue": "2", "JournalId": 74064, "JournalTitle": "Digital Threats: Research and Practice", "ISSN": "2576-5337", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "University of Twente, Enschede, Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Twente, Enschede, Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Twente, Netherlands and NCSC-NL, The Hague, Netherlands"}], "References": [{"Title": "DL-Droid: Deep learning based android malware detection using real devices", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "101663", "JournalTitle": "Computers & Security"}, {"Title": "Assessing and Improving Malware Detection Sustainability through App Evolution Studies", "Authors": "Haipeng <PERSON>ai", "PubYear": 2020, "Volume": "29", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}]}, {"ArticleId": 90391213, "Title": "Understanding developers’ privacy and security mindsets via climate theory", "Abstract": "<p>Privacy and security by design are policy measures that guide software developers to engineer privacy and security solutions inherently into the software systems they develop. However, although these policy measures have been widely discussed and promoted over the years, recent studies still show a consistent underperformance of privacy and security practices in industry. This research follows previous findings that indicate the role the organizational work environments of developers play in forming their mindsets and behavior. Specifically, we aimed to explore the potential of using organizational climate theory for attaining a better understanding of developers’ perceptions and behaviors and the underlying forces affecting them, and to unveil the constructs that compose organizational privacy and security climates. To this end, we conducted interviews with 27 practitioners involved in developing software systems from 14 companies and qualitatively analyzed the collected data. Our findings indicate that software developers are faced with inconsistent and confusing cues conveyed by management and other parties in their work environment, many of which indicate that these facets are of relatively low priority, leading to perceptions and behaviors that are not in line with those expected and recommended by policy makers. Further, we show how these perceptions and behaviors can be explained by constructs of the organizational climate theory and how, based on our findings, organizational climate mechanisms can be used to go beyond understanding developers’ current privacy and security mindsets toward improving them, thereby leading to an effective implementation of privacy and security by design.</p>", "Keywords": "Data protection; Privacy; Security; Privacy by design; Security by design; Organizational climate; Qualitative research", "DOI": "10.1007/s10664-021-09995-z", "PubYear": 2021, "Volume": "26", "Issue": "6", "JournalId": 3327, "JournalTitle": "Empirical Software Engineering", "ISSN": "1382-3256", "EISSN": "1573-7616", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Systems, University of Haifa, Haifa, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Systems, University of Haifa, Haifa, Israel"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Human Services, University of Haifa, Haifa, Israel"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Information Systems, The Academic College of Tel-Aviv-Yaffo, Tel Aviv, Israel"}], "References": []}, {"ArticleId": 90391230, "Title": "Optimized hybrid design with stabilizing transition probability for stochastic Markovian jump systems under hidden Markov mode detector", "Abstract": "", "Keywords": "", "DOI": "10.1002/asjc.2649", "PubYear": 2022, "Volume": "24", "Issue": "5", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "Tinggang Jia", "Affiliation": "Shanghai Electric Automation Group  Shanghai China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Smart Manufacturing in Energy Chemical Process (East China University of Science and Technology) Ministry of Education  Shanghai China;Key Laboratory of Intelligent Computing and Signal Processing (Ministry of Education), School of Electrical Engineering and Automation Anhui University  Hefei China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Smart Manufacturing in Energy Chemical Process (East China University of Science and Technology) Ministry of Education  Shanghai China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electric and Electronic Engineering Shanghai University of Engineering Science  Shanghai China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Smart Manufacturing in Energy Chemical Process (East China University of Science and Technology) Ministry of Education  Shanghai China"}], "References": [{"Title": "Robust dissipative control for semilinear Markovian jump distributed parameter systems with time‐varying delay and incomplete transition probabilities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "6", "Page": "2513", "JournalTitle": "Asian Journal of Control"}, {"Title": "Optimal design of non‐fragile PID controller", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "2", "Page": "729", "JournalTitle": "Asian Journal of Control"}, {"Title": "Event‐triggered stabilization for continuous‐time saturating Markov jump systems with generally uncertain transition rates", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "3", "Page": "1545", "JournalTitle": "Asian Journal of Control"}]}, {"ArticleId": 90391349, "Title": "Metric monads", "Abstract": "<p> We develop universal algebra over an enriched category and relate it to finitary enriched monads over . Using it, we deduce recent results about ordered universal algebra where inequations are used instead of equations. Then we apply it to metric universal algebra where quantitative equations are used instead of equations. This contributes to understanding of finitary monads on the category of metric spaces. </p>", "Keywords": "Equational theory; monad; enriched category; metric space; poset", "DOI": "10.1017/S0960129521000220", "PubYear": 2021, "Volume": "31", "Issue": "5", "JournalId": 10211, "JournalTitle": "Mathematical Structures in Computer Science", "ISSN": "0960-1295", "EISSN": "1469-8072", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Statistics, Masaryk University, Faculty of Sciences, Kotlářská 2, 611 37 Brno, Czech Republic; Corresponding author."}], "References": []}, {"ArticleId": 90391354, "Title": "<PERSON><PERSON><PERSON> Longsor Di Kabupaten Brebes Memanfaatkan Citra Landsat 8 Dengan Metode Inverse Distance Weighted (IDW)", "Abstract": "<p>Brebes adalah salah satu Kabupaten di Provinsi Jawa Tengah. Sebagian besar wilayahnya berupa dataran rendah yang diapit sungai pemali dan sungai serayu. Maka dari itu, Kabupaten Brebes merupakan salah satu daerah yang rawan terjadi tanah longsor maupun bencana lainnya. Maka dari itu akan dilakukan penelitian untuk menganalisis daerah rawan longsor di Kabupaten Brebes dengan citra landsat 8 yang dipadukan dengan metode Inverse Distance Weighted (IDW) sehingga dapat diketahui daerah mana saja yang berpotensi longsor. Parameter yang digunakan untuk melakukan analisis adalah jenis tanah, curah hujan, dan kemiringan lereng. Ketiga parameter tersebut akan dilakukan overlay sehingga mendapatkan peta daerah rawan tanah longsor. Hasil penelitian ini diharapkan dapat digunakan oleh pemerintah setempat untuk melakukan upaya preventif sehingga dapat mengurangi kerugian dari masyarakat setempat.</p>", "Keywords": "Landslide;Landsat 8 Image;Inverse Distance Weighted (IDW);Kabupaten Brebes", "DOI": "10.46229/jifotech.v1i2.276", "PubYear": 2021, "Volume": "1", "Issue": "2", "JournalId": 85255, "JournalTitle": "Journal of Information Technology", "ISSN": "2774-4884", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas Kristen Satya <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitas Kristen Satya <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON> Yu<PERSON><PERSON>", "Affiliation": "Universitas Kristen Satya <PERSON>"}], "References": []}, {"ArticleId": 90391362, "Title": "Perancangan Dan Pembuatan Company Profile Berbasis Website Sebagai MEdia Promosi Dan Informasi Pada Sekolah Tinggi Teologia Ekklesia Pontianak", "Abstract": "<p>Sekolah Tinggi Teologia Ekklesia Pontianak salah satu Perguruan Tinggi Swasta yang berada di Pontianak. Dalam mempromosikan STT Ekklesia ke masyarakat masih menggunakan konvensional seperti brosur, flyer, dan spanduk. Tujuan dari penelitian ini adalah merancang dan membuat website sebagai media promosi. Sekolah Tinggi Teologi Ekklesia Pontianak bisa memanfaatkan website sebagai media promosi untuk menunjukan bahwa Sekolah Tinggi Teologia Ekklesia Pontianak tersebut memiliki alternatif untuk mempromosikan kampusnya. Pada penelitian ini menggunakan HTML, CSS dan JavaScript untuk pembuatan website dan memiliki kriteria website yang menarik, rapi dan isi konten yang dapat dimengerti sehingga masyarakat dapat melihat website tersebut menggunakan media handphone, laptop yang sudah mempunyai tampilan halaman website yang sesuai dengan device. Metode yang digunakan dalam penelitian ini yaitu wawancara dan observasi. Has<PERSON> yang telah dilakukan dengan merancang, membuat dan implementasi website sebagai media promosi, diharapkan akan meningkatkan kualitas baik itu fasilitas, tenaga pengajar dan sebagainya untuk dapat bersaing dengan Sekolah Tinggi lainnya.\r Kata Kunci— School, website, promosi, informasi, company profile</p>", "Keywords": "school;website;promotion;information;company profile", "DOI": "10.46229/jifotech.v1i2.198", "PubYear": 2021, "Volume": "1", "Issue": "2", "JournalId": 85255, "JournalTitle": "Journal of Information Technology", "ISSN": "2774-4884", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Listra Firgia", "Affiliation": "Institut Shanti Bhuana"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut Shanti Bhuana"}], "References": []}, {"ArticleId": 90391398, "Title": "Mining Fuzzy Temporal Gradual Emerging Patterns", "Abstract": "<p>Gradual emerging patterns (GEPs) are gradual item sets that occur less frequently in one data set and more frequently in another. For instance, let ‘fan speed’ and ‘temperature’ be attributes of two numerical data sets. A gradual item set “the higher the speed, the lower the temperature” (which correlates a data set’s attributes) becomes a GEP if it is less frequent (in terms of support as in frequent pattern mining) in one data set and more frequent in another. However, such patterns do not indicate how time gap impacts the emergence. Many correlations appear over time, for instance when phenomena appear after some meteorological situation due to latency. Previous works have not taken this temporal aspect into account. In this paper, we introduce temporal gradual emerging patterns (TGEPs) which are temporal gradual patterns (TGPs) whose frequency supports increase significantly between transformed data sets. For instance, a TGP “the higher the speed, the lower the temperature, almost 3 minutes later” becomes a TGEP if it occurs more frequently in one transformed data set than in another. Furthermore, we extend border manipulation to the case of mining TGEPs. In addition, we propose a more efficient ant colony optimization technique that exploits a heuristic approach to construct TGEPs.</p>", "Keywords": "", "DOI": "10.1142/S0218488521500288", "PubYear": 2021, "Volume": "29", "Issue": "5", "JournalId": 15072, "JournalTitle": "International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems", "ISSN": "0218-4885", "EISSN": "1793-6411", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing and Engineering Sciences, Strathmore University, Nairobi, Kenya"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LIRMM, University of Montpellier, CNRS, Montpellier, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computing and Engineering Sciences, Strathmore University, Nairobi, Kenya"}], "References": []}, {"ArticleId": 90391463, "Title": "Efficient access control with traceability and user revocation in IoT", "Abstract": "With the universality and availability of Internet of Things (IoT), data privacy protection in IoT has become a hot issue. As a branch of attribute-based encryption (ABE), ciphertext policy attribute-based encryption (CP-ABE) is widely used in IoT to offer flexible one-to-many encryption. However, in IoT, different mobile devices share messages collected, transmission of large amounts of data brings huge burdens to mobile devices. Efficiency is a bottleneck which restricts the wide application and adoption of CP-ABE in Internet of things. Besides, the decryption key in CP-ABE is shared by multiple users with the same attribute, once the key disclosure occurs, it is non-trivial for the system to tell who maliciously leaked the key. Moreover, if the malicious mobile device is not revoked in time, more security threats will be brought to the system. These problems hinder the application of CP-ABE in IoT. Motivated by the actual need, a scheme called traceable and revocable ciphertext policy attribute-based encryption scheme with constant-size ciphertext and key is proposed in this paper. Compared with the existing schemes, our proposed scheme has the following advantages: (1) Malicious users can be traced; (2) Users exiting the system and misbehaving users are revoked in time, so that they no longer have access to the encrypted data stored in the cloud server; (3) Constant-size ciphertext and key not only improve the efficiency of transmission, but also greatly reduce the time spent on decryption operation; (4) The storage overhead for traceability is constant. Finally, the formal security proof and experiment has been conducted to demonstrate the feasibility of our scheme.", "Keywords": "IoT; Attribute-based encryption; Constant-size ciphertext and key; Traceability; User revocation", "DOI": "10.1007/s11042-021-11286-0", "PubYear": 2021, "Volume": "80", "Issue": "20", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "@std.uestc.edu.cn;School of Information and Software Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information and Software Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information and Software Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 4, "Name": "Zhiguang Qin", "Affiliation": "School of Information and Software Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, National Dong Hwa University, Hualien, Taiwan, ROC"}], "References": []}, {"ArticleId": 90391676, "Title": "DeepM6ASeq-EL: prediction of human N6-methyladenosine (m6A) sites with LSTM and ensemble learning", "Abstract": "<p>N6-methyladenosine (m<sup>6</sup>A) is a prevalent methylation modification and plays a vital role in various biological processes, such as metabolism, mRNA processing, synthesis, and transport. Recent studies have suggested that m<sup>6</sup>A modification is related to common diseases such as cancer, tumours, and obesity. Therefore, accurate prediction of methylation sites in RNA sequences has emerged as a critical issue in the area of bioinformatics. However, traditional high-throughput sequencing and wet bench experimental techniques have the disadvantages of high costs, significant time requirements and inaccurate identification of sites. But through the use of traditional experimental methods, researchers have produced many large databases of m<sup>6</sup>A sites. With the support of these basic databases and existing deep learning methods, we developed an m<sup>6</sup>A site predictor named DeepM6ASeq-EL, which integrates an ensemble of five LSTM and CNN classifiers with the combined strategy of hard voting. Compared to the state-of-the-art prediction method WHISTLE (average AUC 0.948 and 0.880), the DeepM6ASeq-EL had a lower accuracy in m<sup>6</sup>A site prediction (average AUC: 0.861 for the full transcript models and 0.809 for the mature messenger RNA models) when tested on six independent datasets.</p>", "Keywords": "N6-methyladenosine; site prediction; LSTM; CNN; ensemble learning", "DOI": "10.1007/s11704-020-0180-0", "PubYear": 2022, "Volume": "16", "Issue": "2", "JournalId": 4733, "JournalTitle": "Frontiers of Computer Science", "ISSN": "2095-2228", "EISSN": "2095-2236", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Fundamental and Frontier Sciences, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Fundamental and Frontier Sciences, University of Electronic Science and Technology of China, Chengdu, China;Hainan Key Laboratory for Computational Science and Application, Hainan Normal University, Haikou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China"}], "References": [{"Title": "A survey on ensemble learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "2", "Page": "241", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Identification of membrane protein types via multivariate information fusion with <PERSON><PERSON>–Schmidt Independence Criterion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "383", "Issue": "", "Page": "257", "JournalTitle": "Neurocomputing"}, {"Title": "iMRM: a platform for simultaneously identifying multiple kinds of RNA modifications", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "11", "Page": "3336", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 90391677, "Title": "Deep learning-based multidimensional feature fusion for classification of ECG arrhythmia", "Abstract": "<p>Feature extraction plays an important role in arrhythmia classification, and successful arrhythmia classification generally depends on ECG feature extraction. This paper proposed a feature extraction method combining traditional approaches and 1D-CNN aiming to find the optimal feature set to improve the accuracy of arrhythmia classification. The proposed method is verified by using the MIT-BIH arrhythmia benchmark database. It is found that the features extracted by 1D-CNN and discrete wavelet transform form the optimal feature set with the average classification accuracy up to 98.35%, which is better than the latest methods.</p>", "Keywords": "ECG signals; Arrhythmia classification; Feature fusion; 1D-CNN4", "DOI": "10.1007/s00521-021-06487-5", "PubYear": 2023, "Volume": "35", "Issue": "22", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, Xiamen University of Technology, Xiamen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Xiamen University of Technology, Xiamen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Xiamen University of Technology, Xiamen, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Faculty of Engineering, University of Porto (FEUP), Porto, Portugal"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, College of Computer and Information Sciences, King Saud University, Riyadh, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Information Systems Department, College of Computer and Information Sciences, King Saud University, Riyadh, Saudi Arabia"}], "References": [{"Title": "RETRACTED ARTICLE: Lung cancer prediction using higher-order recurrent neural network based on glowworm swarm optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "4373", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Circuit Copyright Blockchain: Blockchain-Based Homomorphic Encryption for IP Circuit Protection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "3", "Page": "1410", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Deep learning for intelligent IoT: Opportunities, challenges and solutions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "164", "Issue": "", "Page": "50", "JournalTitle": "Computer Communications"}, {"Title": "A deep learning-based driver distraction identification framework over edge cloud", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A survey on deep learning in medicine: Why, how and when?", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "111", "JournalTitle": "Information Fusion"}, {"Title": "Medical Diagnosis Using Machine Learning: A Statistical Review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "1", "Page": "107", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Extended Kalman Filter-Based Power Line Interference Canceller for Electrocardiogram Signal", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "1", "Page": "34", "JournalTitle": "Big Data"}]}, {"ArticleId": 90391783, "Title": "Modeling and temperature control of a moving substrate", "Abstract": "This paper describes the development of a control system for an industrial heating application. In this process a moving substrate is passing through a heating zone with variable speed. Heat is applied by hot air to the substrate with the air flow rate being the manipulated variable. The aim is to control the substrate’s temperature at a specific location after passing the heating zone. First, a model is derived for a point attached to the moving substrate. This is modified to reflect the temperature of the moving substrate at the specified location. In order to regulate the temperature a nonlinear model predictive control approach is applied using an implicit Euler scheme to integrate the model and an augmented gradient based optimization approach. The performance of the controller has been validated both by simulations and experiments on the physical plant. The respective results are presented in this paper.", "Keywords": "Model Predictive Control ; convective heating ; thermal model ; optimization", "DOI": "10.1016/j.ifacol.2021.08.547", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Weiss", "Affiliation": "Institute of System Dynamics, Hochschule Konstanz"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Microsystems Engineering, Universität Freiburg"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "HOMAG AG, Schopfloch, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of System Dynamics, Hochschule Konstanz"}], "References": []}, {"ArticleId": 90391954, "Title": "Research on influencing factors of production control valve used for water control in horizontal wells", "Abstract": "In this paper, in view of the heterogeneity caused by the physical properties of a reservoir and lithologic differences in horizontal wells, which can easily become locally watered out in horizontal section, the production control valve which can adjust the flow rate is used to control the production of horizontal wells in sections, which is an effective method to delay edge and bottom water inrush. The results show that the smaller the opening of control valves is, the stronger the balance degree of liquid production profile is, and the balance degree of liquid production profile gradually decreases with the increase of the number of control valves. When installing the control valve, its parameters should be optimised. Through the simulation of the production flow pressure chart, the optimal production is selected, and the corresponding bottom hole flow pressure and optimal production pressure difference are selected. Copyright © 2020 Inderscience Enterprises Ltd.", "Keywords": "Balanced production; Control valve; Horizontal well; Liquid production profile", "DOI": "10.1504/IJMIC.2020.117496", "PubYear": 2020, "Volume": "36", "Issue": "4", "JournalId": 12335, "JournalTitle": "International Journal of Modelling, Identification and Control", "ISSN": "1746-6172", "EISSN": "1746-6180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, China University of Petroleum (East China), Shandong, Qingdao, 266580, China; Shengli Oil Field Petroleum Engineering Technology Research Institute Sinopec, Shandong, Dongying, 257000, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, China University of Petroleum (East China), Shandong, Qingdao, 266580, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer Control, Shengli College China University of Petroleum, Shandong, Dongying, 257001, China"}], "References": []}, {"ArticleId": 90392097, "Title": "How to Sell a Data Set? Pricing Policies for Data Monetization", "Abstract": "<p>The wide variety of pricing policies used in practice by data sellers suggests that there are significant challenges in pricing data sets. In this paper, we develop a utility framework that is appropriate for data buyers and the corresponding pricing of the data by the data seller. Buyers interested in purchasing a data set have private valuations in two aspects—their ideal record that they value the most, and the rate at which their valuation for the records in the data set decays as they differ from the buyers’ ideal record. The seller allows individual buyers to filter the data set and select the records that are of interest to them. The multidimensional private information of the buyers coupled with the endogenous selection of records makes the seller’s problem of optimally pricing the data set a challenging one. We formulate a tractable model and successfully exploit its special structure to obtain optimal and near-optimal data-selling mechanisms. Specifically, we provide insights into the conditions under which a commonly used mechanism—namely, a price-quantity schedule—is optimal for the data seller. When the conditions leading to the optimality of a price-quantity schedule do not hold, we show that the optimal price-quantity schedule offers an attractive worst-case guarantee relative to an optimal mechanism. Further, we numerically solve for the optimal mechanism and show that the actual performance of two simple and well-known price-quantity schedules—namely, two-part tariff and two-block tariff—is near optimal. We also quantify the value to the seller from allowing buyers to filter the data set.</p>", "Keywords": "data monetization; multidimensional mechanism design; price-quantity schedules", "DOI": "10.1287/isre.2021.1027", "PubYear": 2021, "Volume": "32", "Issue": "4", "JournalId": 11444, "JournalTitle": "Information Systems Research", "ISSN": "1047-7047", "EISSN": "1526-5536", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Rotterdam School of Management, Erasmus University, 3062 PA Rotterdam, Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> School of Management, The University of Texas at Dallas, Richardson, Texas 75080-3021"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> School of Management, The University of Texas at Dallas, Richardson, Texas 75080-3021"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> School of Management, The University of Texas at Dallas, Richardson, Texas 75080-3021"}], "References": []}, {"ArticleId": 90392136, "Title": "Design of power line safety operation and maintenance monitoring system based on cloud computing", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2021.10041044", "PubYear": 2021, "Volume": "19", "Issue": "3", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90392262, "Title": "Superblock-based performance optimization for Sunway Math Library on SW26010 many-core processor", "Abstract": "<p>The SW26010 many-core processor is based on the Sunway architecture that is composed of management and computing processing elements (MPE and CPE, respectively), each of which is equipped with a stand-alone math library. The issue is that each Sunway Math Library (SML) version is written in assembly which is outside the power of compilers that take high-level languages as input; existing optimization approaches thus mainly rely on manual strategies, which are considered inefficient. In this paper, we leverage the concept of superblock scheduling, a well-known compilation technique, and present a tool named SMPOT to optimize the SML. SMPOT first builds a superblock using a novel tail duplication algorithm, and then uses code motion restrictions to avoid code compensation, followed by matching the machine model. Finally, it reorders instructions on the main path by an activation algorithm based on available computing resources. The experimental results show that SMPOT can effectively improve the performance of the SML. The main path performance of MPE functions is improved by 10.61% on average and overall performance by 5.40%. The main path performance of CPE functions is improved by 5.72% on average and overall performance by 2.98%.</p>", "Keywords": "Assembly; Performance optimization; Superblock scheduling; SW26010", "DOI": "10.1007/s11227-021-03997-w", "PubYear": 2022, "Volume": "78", "Issue": "4", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mathematical Engineering and Advanced Computing, Zhengzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mathematical Engineering and Advanced Computing, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mathematical Engineering and Advanced Computing, Zhengzhou, China"}, {"AuthorId": 4, "Name": "Yuanyuan Xia", "Affiliation": "State Key Laboratory of Mathematical Engineering and Advanced Computing, Zhengzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mathematical Engineering and Advanced Computing, Zhengzhou, China"}], "References": []}, {"ArticleId": 90392267, "Title": "Uniform mixture design via evolutionary multi‐objective optimization", "Abstract": "Design of experiments is a branch of statistics that has been employed in different areas of knowledge. A particular case of experimental designs is uniform mixture design. A uniform mixture design method aims to spread points (mixtures) uniformly distributed in the experimental region. Each mixture should meet the constraint that the sum of its components must be equal to one. In this paper, we propose a new method to approximate uniform mixture designs via evolutionary multi-objective optimization. For this task, we formulate three M -objective optimization problems whose Pareto optimal fronts correspond to a mixture design of M components (or dimensions). In order to obtain a uniform mixture design, we consider six well-known algorithms used in the area of evolutionary multi-objective optimization to solve M -objective optimization problems. Thus, a set of solutions approximates the entire Pareto front of each M -objective problem, while it implicitly approximates a uniform mixture design. We evaluate our proposed methodology by generating mixture designs in two, three, and up to eight dimensions, and we compare the results obtained concerning those produced by different methods available in the specialized literature. Our results indicate that the proposed strategy is a promising alternative to approximate uniform mixture designs. Unlike most of the existing approaches, it obtains mixture designs for an arbitrary number of points. Moreover, the generated design points are properly distributed in the experimental region.", "Keywords": "Uniform mixture design ; Evolutionary multi-objective optimization", "DOI": "10.1016/j.swevo.2021.100979", "PubYear": 2022, "Volume": "68", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Technologies for Information in Science, ENES, Unidad Morelia, UNAM, Morelia, Michoacán, México;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Departamento de Matemáticas Aplicadas y Sistemas UAM, Unidad Cuajimalpa, Ciudad de México, México"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Technologies for Information in Science, ENES, Unidad Morelia, UNAM, Morelia, Michoacán, México"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Evolutionary Computation Group, CINVESTAV, IPN, Ciudad de México, México"}], "References": []}, {"ArticleId": 90392295, "Title": "Design and Control of 6-DOF Robotic Manipulator Driven by Pneumatic Muscles and Motor", "Abstract": "A pneumatic-muscle actuator (PMA) is made of a unique material with fibers wrapped inside and metal fittings attached at each end. Because of reversible physical deformation, a PMA can produce linear motion during contraction and muscle expansion. We use a PMA and motors’ hybrid drivers to implement a low-cost and safe robot manipulator with six degrees of freedom (6-DOF). Safety is achieved by applying a novel proxy-based sliding mode controller (NPSMC) and a linear extended state observer (LESO) on each joint of the 6-DOF robot manipulator. The NPSMC can compensate the six joints of the 6-DOF robot manipulator reaching Lyapunov stability, and we prove that their pathing errors converge to a neighborhood of zero. An experiment on all joints is conducted to verify the trajectory pathing precision and system safety for the 6-DOF robot manipulator. The experimental results show that under NPSMC compensation and the LESO estimate, the 6-DOF robot manipulator using the hybrid drivers satisfies both the pathing performance demands and safety control. © MYU K.K.", "Keywords": "Linear extended state observer; Novel proxy-based sliding mode control; Pneumatic-muscle actuator; Serial manipulator arm; Smooth control", "DOI": "10.18494/SAM.2021.3250", "PubYear": 2021, "Volume": "33", "Issue": "9", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Chung Hsing University, No. 145, Xingda Road, South District, Taichung City, 40227, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Sun Yat-sen University, No. 70, Lien-hai Road,, Kaohsiung, 80424, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electro-Mechanical Engineering, Tamkang University, No. 151, Yingzhuan Road, Tamsui District, New Taipei City, 25137, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Chung Hsing University, No. 145, Xingda Road, South District, Taichung City, 40227, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, National Kaohsiung University of Science and Technology, No. 415, Jiangong Road, Sanmin District, Kaohsiung City, 807618, Taiwan, Graduate Institute of Clinical Medicine, Kaohsiung Medical University, Taiwan, No. 100, <PERSON><PERSON><PERSON><PERSON><PERSON> 1st Road, Sanmin Dist., Kaohsiung City, 80708, Taiwan"}], "References": []}, {"ArticleId": 90392325, "Title": "Improved edge-guided network for single image super-resolution", "Abstract": "<p>In recent years, deep learning has been successfully applied to image super-resolution. It is still a challenge to reconstruct high-frequency details from low-resolution images. However, many works lack attention to the high-frequency part. We find that edge prior information can be used to extract high-frequency parts and applying soft edges to image reconstruction has achieved great results. Inspired by this, we focus on how to make full use of edge information to generate high-frequency details. We propose an improved edge-guided neural network for single image super-resolution (IEGSR), which makes full use of the edge prior information to reconstruct images with more abundant high-frequency information. For high-frequency information, we propose an edge-net to generate image edges better. For low-frequency information, we propose a global and local feature extraction module (GLM) to reconstruct the texture details. For the fusion of high-frequency information and low-frequency information, we propose a progressive fusion method, which can greatly reduce the number of parameters. Extensive experimental results demonstrate that our method can obtain images with sharper details. Applying our model to the Manga109 test set, the PSNR value of 4 times image super-resolution is as high as 39.02.</p>", "Keywords": "Single image super-resolution; Edge prior; Feature fusion; Convolutional neural network; Feature extraction", "DOI": "10.1007/s11042-021-11429-3", "PubYear": 2022, "Volume": "81", "Issue": "1", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Windsor, Windsor, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Henan Institute of Science and Technology, Xinxiang, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan, China"}], "References": [{"Title": "Residual network with detail perception loss for single image super-resolution", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "199", "Issue": "", "Page": "103007", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 90392593, "Title": "In plane sight: Inattentional blindness affects visual detection of external targets in simulated flight", "Abstract": "Aviation places significant demands on pilots' perceptual and attentional capacities. The avoidance of other objects both on the ground and in the air is critical to safe flight. Research on automobile driving has revealed the occurrence of ‘inattentional blindness’ (IB) whereby objects clearly located within the visual field may not detected when drivers are concurrently engaged in another attention capturing task such as a cellphone conversation. Almost no comparable research has been conducted within the aviation domain despite the significance of both ground-based and mid-air collisions. The present study was designed to investigate the effects of diverting attentional resources away from the primary task of safely flying a simulated light aircraft from takeoff to cruising. Flight naïve students were trained to proficiency in a flight-simulator and flew two simulated flights with and without a competing attentional task. Detection of a variety of objects placed in the background was measured. The results showed that when distracted by an engaging cellphone conversation novice pilots failed to detect many of the objects located within the visual scene. Recognition accuracy was greater when pilots' attention was not diverted elsewhere. There was a reduction in time spent looking at some key flight instruments but not on others. Inattentional blindness poses significant flight safety risks and further research into both the stimulus and perceiver characteristics that promote or reduce inattentional blindness would be of significant benefit to aviation safety.", "Keywords": "Aviation safety ; Attention ; Distraction ; Simulation ; Eye-tracking", "DOI": "10.1016/j.apergo.2021.103578", "PubYear": 2022, "Volume": "98", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "Alaska White", "Affiliation": "Department of Psychology, University of Otago, Dunedin, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Psychology, University of Otago, Dunedin, New Zealand;Corresponding author. Department of Psychology, University of Otago, Box 56, Dunedin, New Zealand"}], "References": []}, {"ArticleId": 90392686, "Title": "COVID-19 and Networks", "Abstract": "Ongoing COVID-19 pandemic poses many challenges to the research of artificial intelligence. Epidemics are important in network science for modeling disease spread over networks of contacts between individuals. To prevent disease spread, it is desirable to introduce prioritized isolation of the individuals contacting many and unspecified others, or connecting different groups. Finding such influential individuals in social networks, and simulating the speed and extent of the disease spread are what we need for combating COVID-19. This article focuses on the following topics, and discusses some of the traditional and emerging research attempts: (1) topics related to epidemics in network science, such as epidemic modeling, influence maximization and temporal networks, (2) recent research of network science for COVID-19 and (3) datasets and resources for COVID-19 research.", "Keywords": "Epidemics;Influence maximization;Network science;Temporal networks", "DOI": "10.1007/s00354-021-00134-2", "PubYear": 2021, "Volume": "39", "Issue": "3-4", "JournalId": 6245, "JournalTitle": "New Generation Computing", "ISSN": "0288-3635", "EISSN": "1882-7055", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, School of Computing, Tokyo Institute of Technology, W8-59 2-12-1 Ookayama, Meguro, Tokyo 152-8552 Japan."}], "References": []}, {"ArticleId": 90392715, "Title": "Prevalence of shifted Rayleigh filter for passive surveillance in underwater", "Abstract": "Purpose From many decades, bearings-only tracking (BOT) is the interested problem for researchers. This utilises nonlinear filtering methods for state estimation as there is only information about the target, i.e. bearing is a nonlinear measurement. The measurement bearing is tangentially related to the target state vector. There are many nonlinear filtering algorithms developed so far in the literature. Design/methodology/approach In this research work, the recently developed nonlinear filtering algorithm, i.e. shifted Rayleigh filter (SRF), is applied to BOT. Findings The SRF is tested for two-dimensional BOT against various scenarios. The simulation results emphasise that the SRF performs well when compared to the standard nonlinear filtering algorithm, unscented Kalman filter (UKF). Originality/value SRF utilises the nonlinearities present in the bearing measurement through the use of moment matching. The SRF is able to produce the solution in highly noisy environment, long ranges and high dimension tracking.", "Keywords": "Stochastic signal processing;Shifted Rayleigh filter;State estimation;Underwater surveillance", "DOI": "10.1108/IJICC-06-2021-0105", "PubYear": 2022, "Volume": "15", "Issue": "1", "JournalId": 26058, "JournalTitle": "International Journal of Intelligent Computing and Cybernetics", "ISSN": "1756-378X", "EISSN": "1756-3798", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Koneru Lakshmaiah Education Foundation , Guntur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Koneru Lakshmaiah Education Foundation , Guntur, India"}], "References": []}, {"ArticleId": 90392718, "Title": "From posthumanism to ethics of artificial intelligence", "Abstract": "<p>Posthumanism is one of the well-known and significant concepts in the present day. It impacted numerous contemporary fields like philosophy, literary theories, art, and culture for the last few decades. The movement has been concentrated around the technological development of present days due to industrial advancement in society and the current proliferated daily usage of technology. Posthumanism indicated a deconstruction of our radical conception of ‘ human ’, and it further shifts our societal value alignment system to a novel dimension. The majority of our population is getting deeply involved in virtual reality in daily life. Sooner or later, we shall get a different conception of ‘biological human being’ through the advancement of artificial intelligence (AI) technology. If an automated artificial system could replace the human brain and repair any physical loss of our biological body, it will certainly become a journey towards immortality for scientists. However, we must analyze whether posthumanism will consider ‘hybrid human beings’ as moral agents, similar to biological humans. This is why, in the future, the relation between biological human beings and posthumans will play an active role in designing artificial moral agents. Whether the future posthumans would overpower biological humanity or both of them would work as peers to form a digital utopian society and create new dimensions of rationality is still a case of anticipation. Our aim in this paper is to critically analyze the authenticity of the posthuman cyborg as an agent, their relations with humans and the emergence of ‘AI ethics’.</p>", "Keywords": "Posthumanism; Postmodernity; Artificial intelligence (AI); Artificial moral agent; Ethics; Moral agent", "DOI": "10.1007/s00146-021-01274-1", "PubYear": 2023, "Volume": "38", "Issue": "1", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "Raja<PERSON><PERSON>", "Affiliation": "Department of Humanities and Social Sciences, IIT Bombay, Mumbai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Humanities and Social Sciences, IIT Bombay, Mumbai, India"}], "References": [{"Title": "The problem of machine ethics in artificial intelligence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "1", "Page": "103", "JournalTitle": "AI & SOCIETY"}]}, {"ArticleId": 90392731, "Title": "Stability and performance in MPC using a finite-tail cost", "Abstract": "In this paper, we provide a stability and performance analysis of model predictive control (MPC) schemes based on finite-tail costs. We study the MPC formulation originally proposed by <PERSON> et al. (2001) wherein the standard terminal penalty is replaced by a finite-horizon cost of some stabilizing control law. In order to analyse the closed loop, we leverage the more recent technical machinery developed for MPC without terminal ingredients. For a specified set of initial conditions, we obtain sufficient conditions for stability and a performance bound in dependence of the prediction horizon and the extended horizon used for the terminal penalty. The main practical benefit of the considered finite-tail cost MPC formulation is the simpler offline design in combination with typically significantly less restrictive bounds on the prediction horizon to ensure stability. We demonstrate the benefits of the considered MPC formulation using the classical example of a four tank system.", "Keywords": "", "DOI": "10.1016/j.ifacol.2021.08.540", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Systems Theory and Automatic Control, University of Stuttgart, Germany;Institute for Dynamic Systems and Control, ETH Zürich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Systems Theory and Automatic Control, University of Stuttgart, Germany"}], "References": []}, {"ArticleId": 90392732, "Title": "Data-Driven Estimation of Infinitesimal Generators of Stochastic Systems", "Abstract": "This paper is concerned with a data-driven approach for the estimation of infinitesimal generators of continuous-time stochastic systems with unknown dynamics. We first approximate the infinitesimal generator of the solution process via a set of data collected from trajectories of the unknown system. The approximation utilizes both time discretization and sampling from the solution process. Assuming proper continuity assumptions on dynamics of the system, we then quantify the closeness between the infinitesimal generator and its approximation while providing a priori guaranteed confidence bound. We demonstrate that both the time discretization and the number of data play significant roles in providing a reasonable closeness precision. Moreover, for a fixed size of data, variance of the estimation converges to infinity when the time discretization parameter goes to zero. The formulated error bound shows how to pick proper data size and time discretization jointly to prevent this counter-intuitive behavior. The proposed results are demonstrated on a case study.", "Keywords": "Continuous-time stochastic systems ; Data-driven estimation ; Infinitesimal generators", "DOI": "10.1016/j.ifacol.2021.08.511", "PubYear": 2021, "Volume": "54", "Issue": "5", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Technical University of Munich, Germany"}, {"AuthorId": 2, "Name": "Abolfazl Lavaei", "Affiliation": "Institute for Dynamic Systems and Control, ETH Zurich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Newcastle University, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Colorado Boulder, USA, and LMU Munich, Germany"}], "References": [{"Title": "Compositional Construction of Finite MDPs for Continuous-Time Stochastic Systems: A Dissipativity Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "1962", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Compositional Construction of Control Barrier Functions for Networks of Continuous-Time Stochastic Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "1856", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 90392740, "Title": "A unified control strategy for autonomous aerial vehicles", "Abstract": "<p>Unmanned aerial vehicles (UAVs) have become popular in a wide range of applications, including many military and civilian uses. State-of-the-art control strategies for these vehicles are typically tailored to a specific platform and are often limited to a portion of the vehicle’s flight envelope. This article presents a single physics-based controller capable of aggressive maneuvering for the majority of UAVs. The controller is applicable to UAVs with the ability to apply a force along a body-fixed direction, and a moment about an arbitrary axis, which includes UAVs such as multi-copters, conventional fixed-wing, agile fixed-wing, most flying-wings, most tailsitters, some tilt-rotor/wing platforms, and some flapping-wing vehicles. We describe the implementation of this controller on numerous platforms, and demonstrate autonomous flight in outdoor flight tests for a quadrotor and an agile fixed-wing aircraft. To specifically demonstrate the extreme maneuvering capability of the control logic, we perform a rolling flip with the quadrotor and a rolling Harrier and an aggressive turnaround with the fixed-wing aircraft, all using a single controller. </p>", "Keywords": "Aerial robotics; Control; UAV; Aerobatics", "DOI": "10.1007/s10514-021-10015-8", "PubYear": 2021, "Volume": "45", "Issue": "6", "JournalId": 20990, "JournalTitle": "Autonomous Robots", "ISSN": "0929-5593", "EISSN": "1573-7527", "Authors": [{"AuthorId": 1, "Name": "Eitan Bul<PERSON>", "Affiliation": "Department of Mechanical Engineering, McGill University, Montreal, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, McGill University, Montreal, Canada"}], "References": []}, {"ArticleId": 90392742, "Title": "Contents", "Abstract": "", "Keywords": "", "DOI": "10.1016/S2405-8963(21)01390-2", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 90392744, "Title": "A dissipativity–based framework for analyzing stability of predictive controllers", "Abstract": "Stabilizing conditions for nonlinear predictive control typically rely on standard Lyapunov functions and thus require a monotonically decreasing cost function. These conditions cannot certify stability of predictive controllers in the presence of non–monotonic cost functions. In this paper we develop new dissipativity–based stabilizing conditions for nonlinear predictive control that allow for non–monotonic cost functions. Firstly, we establish that dissipation inequalities with a cyclically negative supply imply asymptotic stability. Secondly, we show that closed–loop trajectories generated by predictive control satisfy a fundamental dissipation inequality. This enables dissipativity–based stabilizing conditions that do not require a special terminal cost and apply to both model–based and data–driven predictive control algorithms.", "Keywords": "Predictive control ; Stability of nonlinear systems ; Dissipative systems ; <PERSON><PERSON><PERSON><PERSON> function ; Data–driven control", "DOI": "10.1016/j.ifacol.2021.08.539", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Eindhoven University of Technology, Eindhoven, Netherlands"}], "References": []}, {"ArticleId": 90392747, "Title": "State and parameter estimation for model-based retinal laser treatment", "Abstract": "We present an approach for state and parameter estimation in retinal laser treatment by a novel setup where both measurement and heating is performed by a single laser. In this medical application, the temperature that is induced by the laser in the patient’s eye is critical for a successful and safe treatment. To this end, we pursue a model-based approach using a model given by a heat diffusion equation on a cylindrical domain, where the source term is given by the absorbed laser power. The model is parametric in the sense that it involves an absorption coefficient, which depends on the treatment spot and plays a central role in the input-output behavior of the system. After discretization, we apply a particularly suited parametric model order reduction to ensure real-time tractability while retaining parameter dependence. We augment known state estimation techniques, i.e., extended Kalman filtering and moving horizon estimation, with parameter estimation to estimate the absorption coefficient and the current state of the system. Eventually, we show first results for simulated and experimental data from porcine eyes. We find that, regarding convergence speed, the moving horizon estimation slightly outperforms the extended <PERSON><PERSON> filter on measurement data in terms of parameter and state estimation, however, on simulated data the results are very similar.", "Keywords": "moving horizon estimation ; nonlinear observers ; filter design ; model predictive control in medicine applications ; modeling ; parameter-varying systems ; model reduction", "DOI": "10.1016/j.ifacol.2021.08.552", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Leibniz University Hannover, Institute of Automatic Control"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technische Universität Ilmemau, Institute for Mathematics"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technische Universität Ilmemau, Institute for Mathematics"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Lübeck, Institute of Biomedical Optics"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Lübeck, Institute of Biomedical Optics"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Technische Universität Ilmemau, Institute for Mathematics"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Leibniz University Hannover, Institute of Automatic Control"}], "References": [{"Title": "Modeling and parameter identification for real-time temperature controlled retinal laser therapies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "68", "Issue": "11", "Page": "953", "JournalTitle": "at - Automatisierungstechnik"}]}, {"ArticleId": 90392829, "Title": "Artificial Intelligence and Cyber-Physical Systems: A Review and Perspectives for the Future in the Chemical Industry", "Abstract": "<p>Modern society is living in an age of paradigm changes. In part, these changes have been driven by new technologies, which provide high performance computing capabilities that enable the creation of complex Artificial Intelligence systems. Those developments are allowing the emergence of new Cyber Systems where the continuously generated data is utilized to build Artificial Intelligence models used to perform specialized tasks within the system. While, on one hand, the isolated application of the cyber systems is becoming widespread, on the other hand, their synchronical integration with other cyber systems to build a concise and cognitive structure that can interact deeply and autonomously with a physical system is still a completely open question, only addressed in some works from a philosophical point of view. From this standpoint, the AI can play an enabling role to allow the existence of these cognitive CPSs. This review provides a look at some of the aspects that will be crucial in the development of cyber-physical systems, focusing on the application of artificial intelligence to confer cognition to the system. Topics such as control and optimization architectures and digital twins are presented as components of the CPS. It also provides a conceptual overview of the impacts that the application of these technologies might have in the chemical industry, more specifically in the purification of methane.</p>", "Keywords": "artificial intelligence; cyber-physical systems; industry 4.0; digital twins; chemical industry; methane purification artificial intelligence ; cyber-physical systems ; industry 4.0 ; digital twins ; chemical industry ; methane purification", "DOI": "10.3390/ai2030027", "PubYear": 2021, "Volume": "2", "Issue": "3", "JournalId": 69255, "JournalTitle": "AI", "ISSN": "", "EISSN": "2673-2688", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratory of Separation and Reaction Engineering, Associate Laboratory LSRE/LCM, Department of Chemical Engineering, Faculty of Engineering, University of Porto, Rua Dr. <PERSON>, 4200-465 Porto, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory of Separation and Reaction Engineering, Associate Laboratory LSRE/LCM, Department of Chemical Engineering, Faculty of Engineering, University of Porto, Rua Dr. <PERSON>, 4200-465 Porto, Portugal"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Engenharia Química, Escola Politécnica (Polytechnic Institute), Universidade Federal da Bahia, Salvador 40210-630, Brazil"}, {"AuthorId": 4, "Name": "Márcio A. F. Martins", "Affiliation": "Departamento de Engenharia Química, Escola Politécnica (Polytechnic Institute), Universidade Federal da Bahia, Salvador 40210-630, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Separation and Reaction Engineering, Associate Laboratory LSRE/LCM, Department of Chemical Engineering, Faculty of Engineering, University of Porto, Rua Dr. <PERSON>, 4200-465 Porto, Portugal"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Laboratory of Separation and Reaction Engineering, Associate Laboratory LSRE/LCM, Department of Chemical Engineering, Faculty of Engineering, University of Porto, Rua Dr. <PERSON>, 4200-465 Porto, Portugal"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Separation and Reaction Engineering, Associate Laboratory LSRE/LCM, Department of Chemical Engineering, Faculty of Engineering, University of Porto, Rua Dr. <PERSON>, 4200-465 Porto, Portugal"}], "References": [{"Title": "Engineering human-in-the-loop interactions in cyber-physical systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "126", "Issue": "", "Page": "106349", "JournalTitle": "Information and Software Technology"}, {"Title": "Analysing the role of information technology towards sustainable cities living", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "49", "Issue": "7", "Page": "2037", "JournalTitle": "Kybernetes"}, {"Title": "Industrial pipelines data generator", "Authors": "Mubarak AL-<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "", "Page": "106275", "JournalTitle": "Data in Brief"}, {"Title": "Optimal fragrances formulation using a deep learning neural network architecture: A novel systematic approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "150", "Issue": "", "Page": "107344", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 90392979, "Title": "A spatial–temporal graph attention network approach for air temperature forecasting", "Abstract": "Air temperature prediction is a significant task for researchers and forecasters in the field of meteorology. In this paper, we present an innovative, deep spatial–temporal learning air temperature forecasting framework based on graph attention network and gated recurrent unit. Particularly, historical observations containing multiple environmental variables at different stations are constructed as graph signals. The original stations’ conditions and the learned attention information are all included in our model, which overcomes the flaw of the conventional graph network approach. Results of experiments on a real-world dataset demonstrate that, compared to the state-of-the-art baselines, our model achieves the best performance in terms of short-, middle- and long-term air temperature predictions.", "Keywords": "Air temperature forecasting ; Deep learning ; Spatial–temporal characteristics ; Graph attention network", "DOI": "10.1016/j.asoc.2021.107888", "PubYear": 2021, "Volume": "113", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> Shi", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai 200444, China;Key Laboratory of Digital Ocean, National Marine Data and Information Service, Tianjin 300171, China;Corresponding authors at: School of Computer Engineering and Science, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shanghai 200444, China;Shanghai Institute for Advanced Communication and Data Science, Shanghai University, Shanghai 200444, China;Corresponding authors at: School of Computer Engineering and Science, Shanghai University, Shanghai 200444, China"}], "References": [{"Title": "Short-term traffic speed forecasting based on graph attention temporal convolutional networks", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "410", "Issue": "", "Page": "387", "JournalTitle": "Neurocomputing"}, {"Title": "Deep Learning for Time Series Forecasting: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "3", "JournalTitle": "Big Data"}]}, {"ArticleId": 90393232, "Title": "Critical reflections on Water-Energy-Food Nexus in Computable General Equilibrium models: A systematic literature review", "Abstract": "The paper analyses how the Water-Energy-Food Nexus is treated in Computable General Equilibrium (CGE) models, discussing their design, importance and possible ways of improvement. The analysis of their structure is critical for evaluating their potential efficiency in understanding the Nexus, which will be particularly effective for gauging the importance of the topic, the reciprocal dependency of its elements and the expected macroeconomic, demographic and climatic pressures that will act on its components. General equilibrium models can be useful devices to this end, as they are specifically built to track interdependencies and transmission effects across sectors and countries. Nevertheless, the review showed that most CGEs in the literature struggle to represent the competing water uses across sectors and, in particular, those concerning the energy sector. Therefore, it highlights the need to resolve this issue as a necessary step toward improving future research.", "Keywords": "Water-energy-food nexus ; Computable general equilibrium model ; Economic modelling", "DOI": "10.1016/j.envsoft.2021.105201", "PubYear": 2021, "Volume": "145", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Economia, Università Ca’ Foscari Venezia, Fondamenta S. G<PERSON>bbe, VE, 873, 30121, Venezia, Italy;Centro Euro-mediterraneo sui Cambiamenti Climatici (CMCC), Edificio Porta dell'Innovazione - Piano 2, Via della Libertà, 12 - 30175, <PERSON>ene<PERSON> Mar<PERSON>era, Italy;Corresponding author.Dipartimento di Economia, Università Ca’ Foscari Venezia, Fondamenta S. Giobbe, VE, 873, 30121, Venezia, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dipartimento di Economia, Università Ca’ Foscari Venezia, Fondamenta S. Giobbe, VE, 873, 30121, Venezia, Italy;Centro Euro-mediterraneo sui Cambiamenti Climatici (CMCC), Edificio Porta dell'Innovazione - Piano 2, Via della Libertà, 12 - 30175, Venezia Marghera, Italy"}], "References": []}, {"ArticleId": ********, "Title": "Electric vehicle charging station location determination with consideration of routing selection policies and driver’s risk preference", "Abstract": "Nowadays, petroleum resources reduction along with increased fossil fuels price and severe pollution in large cities have invoked the consumption of clean energy. The advancement of modern electricity generation infrastructures and substantial improvement in automobile industry make electric vehicles become the promising alternative in the common transportation fleet. Motivating people to use electric vehicles and removing its development obstacles, require to identify appropriate locations for the installation of public charging stations. Having one reserve battery as the complement is one of the primary options to reduce the electric driving range limitation for inter-city travels. This paper proposes a three-stage model consisting of: (1) regional traffic load network development using different route policies and Monte Carlo simulation, (2) vehicle battery capacity/driving range determination with consideration of drivers’ risk preference towards reserve battery, and (3) mixed integer linear programming model for charging station location selection and socket count determination. A novel metaheuristic algorithm based on scatter search and variable neighborhood search algorithm is proposed to identify non-dominated solutions. A numerical example and charging stations in the state of California are used as the case studies to validate the proposed model and algorithm performance. The results confirm that the low traffic regions near large cities would satisfy the charge demands and reduce the installation cost simultaneously.", "Keywords": "Battery Electric Vehicle ; Reserve Battery ; Route Generation Algorithm ; Route Selection Problem ; Charging Station Location Problem ; Hybrid Multi Objective Scatter Search Variable Neighborhood Search", "DOI": "10.1016/j.cie.2021.107674", "PubYear": 2021, "Volume": "162", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, <PERSON><PERSON><PERSON><PERSON><PERSON>an Pishro Higher Education Institute, Isfahan, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Yazd University, Yazd, Iran"}, {"AuthorId": 3, "Name": "Jun<PERSON> Ma", "Affiliation": "Dept. of Industrial & Systems Engineering, Mississippi State University, Mississippi State, MS 39762, United States;Corresponding author"}], "References": [{"Title": "Sustainability driven multi-criteria project portfolio selection under uncertain decision-making environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106236", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A multi-stage stochastic integer programming approach for locating electric vehicle charging stations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "117", "Issue": "", "Page": "104888", "JournalTitle": "Computers & Operations Research"}, {"Title": "A hybrid algorithm on the vessel routing optimization for marine debris collection", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115198", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": ********, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0020-0255(21)00919-1", "PubYear": 2021, "Volume": "575", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "Bimetallic–organic framework-derived Co3O4–ZnO heterojunction nanofibers: A new kind of emerging porous nanomaterial for enhanced ethanol sensing", "Abstract": "Design of appropriate heterojunction oxides with unique nanostructures and abundant porosities is important to develop superior gas sensors. In this study, a new kind of bimetal–organic framework (BMOF)-derived porous Co<sub>3</sub>O<sub>4</sub>–ZnO heterojunction nanofibers (NFs) was constructed using a combination of BMOF and an electrospinning technique. This emerging heterojunction NFs integrated two kinds of porous materials (BMOFs and NFs), thereby showing abundant wormhole-like pores on each nanofiber. Sensing results indicated that the BMOF-derived 1% Co<sub>3</sub>O<sub>4</sub>–ZnO NFs exhibited higher ethanol sensing properties compared to metal–organic framework-derived ZnO nanocages (NCs) and ZnO NFs, and BMOF-derived 1% Co<sub>3</sub>O<sub>4</sub>–ZnO NCs. In addition to excellent ethanol sensing response ( R <sub>a</sub>/ R <sub>g</sub> = 101.5–100 ppm at 275 °C), the BMOF-derived 1% Co<sub>3</sub>O<sub>4</sub>–ZnO NFs displayed good selectivity and stability. High ethanol sensing properties were attributed to the formation of Co<sub>3</sub>O<sub>4</sub>–ZnO p–n heterojunctions, and the unique BMOF-derived nanofiber structure with abundant pores, high surface area and anti-aggregation property. This study provides a new strategy for constructing various porous metal oxide heterojunction nanofiber network structure derived from BMOFs, that can be used for high-performance gas-sensing application.", "Keywords": "ZnO ; Co<sub>3</sub>O<sub>4</sub> ; Bimetallic–organic frameworks ; Nanofibers ; Gas sensors", "DOI": "10.1016/j.snb.2021.130732", "PubYear": 2021, "Volume": "349", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Information Technology, Shaanxi Normal University, Xi’an 710062, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics and Information Technology, Shaanxi Normal University, Xi’an 710062, China"}, {"AuthorId": 3, "Name": "Gang Li", "Affiliation": "School of Physics and Information Technology, Shaanxi Normal University, Xi’an 710062, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics and Information Technology, Shaanxi Normal University, Xi’an 710062, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Shaanxi University of Science and Technology, Xi’an 710 021, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Zhu", "Affiliation": "Department of Physics, Shaanxi University of Science and Technology, Xi’an 710 021, China"}, {"AuthorId": 7, "Name": "Hongbing Lu", "Affiliation": "School of Physics and Information Technology, Shaanxi Normal University, Xi’an 710062, China;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Information Technology, Shaanxi Normal University, Xi’an 710062, China;Corresponding authors"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Optical and Electronic Information, Wuhan National Laboratory for Optoelectronics, Huazhong University of Science and Technology, Wuhan 430074, China;State Key Laboratory of Transducer Technology, Chinese Academy of Sciences, Shanghai 200050, China;Correspondence to: School of Optical and Electronic Information, Huazhong University of Science and Technology, Wuhan 430074, China"}], "References": [{"Title": "Sub-ppm H2S sensing by tubular ZnO-Co3O4 nanofibers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "307", "Issue": "", "Page": "127624", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Nanoscale Pd catalysts decorated WO3–SnO2 heterojunction nanotubes for highly sensitive and selective acetone sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "306", "Issue": "", "Page": "127575", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Synthesis of Co3O4/ZnO nano-heterojunctions by one-off processing ZIF-8@ZIF-67 and their gas-sensing performances for trimethylamine", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "308", "Issue": "", "Page": "127657", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Surface functionalization of porous In2O3 nanofibers with Zn nanoparticles for enhanced low-temperature NO2 sensing properties", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "308", "Issue": "", "Page": "127716", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 90393471, "Title": "NL4Py: Agent-based modeling in Python with parallelizable NetLogo workspaces", "Abstract": "External control of agent-based models is vital for complex adaptive systems research. Often these experiments require vast numbers of simulation runs and are computationally expensive. NetLogo is the language of choice for most agent-based modelers but lacks direct API access through Python. NL4Py is a Python package for the parallel execution of NetLogo simulations via Python, designed for speed, scalability, and simplicity of use. NL4Py provides access to the large number of open-source machine learning and analytics libraries of Python and enables convenient and efficient parallelization of NetLogo simulations with minimal coding expertise by domain scientists.", "Keywords": "Python ; NetLogo ; Agent-based modeling ; Complex adaptive systems ; Parameter calibration", "DOI": "10.1016/j.softx.2021.100801", "PubYear": 2021, "Volume": "16", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Complex Adaptive Systems Lab, Department of Industrial Engineering and Management Systems, College of Engineering and Computer Science, University of Central Florida, Orlando, FL, United States of America;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Complex Adaptive Systems Lab, Department of Industrial Engineering and Management Systems, College of Engineering and Computer Science, University of Central Florida, Orlando, FL, United States of America"}], "References": []}, {"ArticleId": 90393473, "Title": "Constraint-adaptive MPC for large-scale systems: Satisfying state constraints without imposing them", "Abstract": "Model Predictive Control (MPC) is a successful control methodology, which is applied to increasingly complex systems. However, real-time feasibility of MPC can be challenging for complex systems, certainly when an (extremely) large number of constraints have to be adhered to. For such scenarios with a large number of state constraints, this paper proposes two novel MPC schemes for general nonlinear systems, which we call constraint-adaptive MPC. These novel schemes dynamically select at each time step a (varying) set of constraints that are included in the on-line optimization problem. Carefully selecting the included constraints can significantly reduce, as we will demonstrate, the computational complexity with often only a slight impact on the closed-loop performance. Although not all (state) constraints are imposed in the on-line optimization, the schemes still guarantee recursive feasibility and constraint satisfaction. A numerical case study illustrates the proposed MPC schemes and demonstrates the achieved computation time improvements exceeding two orders of magnitude without loss of performance.", "Keywords": "Model Predictive Control ; Large-Scale Systems ; Adaptive Constraints", "DOI": "10.1016/j.ifacol.2021.08.550", "PubYear": 2021, "Volume": "54", "Issue": "6", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "S.A.<PERSON><PERSON>", "Affiliation": "Eindhoven University of Technology, Mechanical Engineering - Control Systems Technology, Rotterdam, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Eindhoven University of Technology, Mechanical Engineering - Control Systems Technology, Rotterdam, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering - Electromagnetics for Care & Cure Lab, Rotterdam, The Netherlands;Eindhoven, The Netherlands Erasmus University Medical Center Cancer Institute, Department of Radiotherapy, Rotterdam, The Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Eindhoven University of Technology, Mechanical Engineering - Control Systems Technology, Rotterdam, The Netherlands"}], "References": []}, {"ArticleId": 90393500, "Title": "Service function chain composition and placement using grammar‐based genetic algorithm", "Abstract": "<p>Service function chaining (SFC) creates a requested orchestration for composition and placement of several service functions. One of the most critical issues for the service function providers is achieving the highest quality of service. We consider the problem of optimal Service Function composition and placement in an SFC-enabled network with the aim of achieving maximum Quality of Service (QoS). To this end, a method of merging the composition, routing, and placement phases is introduced. A genetic algorithm is designed to find the service chain instance with the maximum QoS using grammar-based Genetic Algorithm (GA) operators. This grammar is provided to filter invalid service compositions based on the data center, security, and mobile scenarios defined by Internet engineering task force. A chromosome in the proposed genetic algorithm corresponds to a service chain instance that matches the proposed grammar. For routing, strongly connected components (SCCs) are extracted to reduce search time in the network. Consequently, the search space is reduced for requests whose start and end nodes are placed in a SCC. Besides, the Floyd Warshall algorithm is used for routing between consecutive services. According to the results, the proposed method outperformed other methods in terms of the number of responded requests, runtime, and cost.</p>", "Keywords": "composition;genetic algorithm;grammar;IETF;placement;service function chaining", "DOI": "10.1002/cpe.6587", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Yazd Branch, Islamic Azad University, Yazd, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Yazd Branch, Islamic Azad University, Yazd, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Yazd University, Yazd, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Shahrekord Branch, Islamic Azad University, Shahrekord, Iran"}], "References": []}, {"ArticleId": 90393504, "Title": "K-Means Clustering Algorithm-Based Detection of Carotid Atherosclerotic Plaque Using Contrast-Enhanced Ultrasound Images", "Abstract": "<p> The aim of this study was to investigate the diagnostic value of contrast-enhanced ultrasound (CEUS) based on the K-means clustering (KMC) algorithm for the vulnerability of carotid atherosclerotic plaque (CAA). In this study, 90 patients with CAA were enrolled into a control group (group A) and an experimental group (group B). The angiography method and KMC-based ultrasound detection were applied to diagnose the CAA patients from the two groups, respectively. The results showed that the sensitivity, specificity, and positive predictive value of patients from group B (92.3%, 90.1%, and 94.8%) for diagnosing CAA were obviously higher than those of patients from group A (81.4%, 88.6%, and 75.3%) ( P 0.05 ). The detection rate of patients from group B (83%, 85%) was dramatically higher than that of patients from group A (65%, 71%) in terms of artery bifurcation and CAA ( P 0.05 ). Besides, patients from group B were more satisfied with their diagnostic method than group A ( P 0.05 ). In conclusion, the ultrasound detection method based on KMC had high sensitivity, specificity, and accuracy in the detection of CAA. In addition, ultrasound detection was better than angiography in the diagnosis of plaque in different parts, and it was worthwhile to apply the ultrasound detection method based on KMC in clinical practice. </p>", "Keywords": "", "DOI": "10.1155/2021/2223344", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Ultrasound, Chifeng Municipal Hospital, Chifeng 024000, Inner Mongolia, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Endocrinology, Chifeng Municipal Hospital, Chifeng 024000, Inner Mongolia, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Emergency Center, Chifeng Municipal Hospital, Chifeng 024000, Inner Mongolia, China"}], "References": [{"Title": "Application of the best evacuation model of deep learning in the design of public structures", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "103975", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 90393615, "Title": "An adaptive method and a new dataset, UKM-IDS20, for the network intrusion detection system", "Abstract": "In recent years, the demand for computer networks has grown rapidly, thus allowing for higher risk of novel attack incidents. Traditional network intrusion detection systems (IDSs) usually have difficulties detecting these attacks because they need to adapt to more advanced or challenging technologies of novel attacks, yet updating them can be computationally expensive and complicated. Therefore, an adaptive IDS is crucial to keep computer networks protected. In addition, consistent update of IDS datasets is essential due to the advancement in network technology and attack strategies. Updating the IDS datasets would allow for the testing of the proposed IDSs on datasets that are relevant to the recent attacks. Moreover, the connection between processing raw network data and creating an adaptive IDS has not been sufficiently studied in this domain. Therefore, this study presents an adaptive IDS and a new real-world network dataset called the UKM-IDS20. The proposed IDS employs the homogeneous ensemble method to create a model that can be periodically updated to detect novel attacks. The update procedure includes training new classifiers and adding them to the base ensemble model. Since this procedure requires further data, a simple data acquisition methodology is used for processing raw network traffic data. This process involves three stages; packet capturing, packet integration, and feature extraction. The collected data from the tests of this study is then used to create the UKM-IDS20 dataset. The created dataset contains 46 features and covers four types of attacks, namely ARP poisoning, DoS, Scans, and Exploits. The complexity of the UKM-IDS20 is compared to the KDD99 and UNSW-NB15 datasets from two aspects. First, an analysis of the features and classes is demonstrated using the rough-set theory. Second, a dynamic artificial neural network is used to test and compare the three datasets mentioned above. The results show a higher complexity and relevancy of the features in the introduced dataset. The UKM-IDS20 dataset is publicly available and can be accessed by all researchers. This study is anticipated to provide enough information to help cybersecurity academics to generate effective IDSs and up-to-date datasets.", "Keywords": "Network data capturing ; Feature extraction ; Computer network security ; Benchmark testing ; Ensemble method", "DOI": "10.1016/j.comcom.2021.09.007", "PubYear": 2021, "Volume": "180", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>z <PERSON>", "Affiliation": "Centre for Artificial Intelligence Technology, Universiti Kebangsaan Malaysia, 43600, Bangi, Malaysia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Artificial Intelligence Technology, Universiti Kebangsaan Malaysia, 43600, Bangi, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Cyber Security, Universiti Kebangsaan Malaysia, 43600, Bangi, Malaysia"}], "References": [{"Title": "Deep learning approaches for anomaly-based intrusion detection systems: A survey, taxonomy, and open issues", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "189", "Issue": "", "Page": "105124", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A survey and taxonomy of the fuzzy signature-based Intrusion Detection Systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "106301", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Review of the Advancement in Intrusion Detection Datasets", "Authors": "<PERSON><PERSON><PERSON>; R<PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "636", "JournalTitle": "Procedia Computer Science"}, {"Title": "MLEsIDSs: machine learning-based ensembles for intrusion detection systems—a review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "11", "Page": "8938", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Deep learning methods in network intrusion detection: A survey and an objective comparison", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "169", "Issue": "", "Page": "102767", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Ensemble Classifiers for Network Intrusion Detection Using a Novel Network Attack Dataset", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "11", "Page": "180", "JournalTitle": "Future Internet"}, {"Title": "Distributed denial of service attacks in cloud: State-of-the-art of scientific and commercial solutions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "", "Page": "100332", "JournalTitle": "Computer Science Review"}, {"Title": "A homogeneous ensemble based dynamic artificial neural network for solving the intrusion detection problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "34", "Issue": "", "Page": "100449", "JournalTitle": "International Journal of Critical Infrastructure Protection"}]}]