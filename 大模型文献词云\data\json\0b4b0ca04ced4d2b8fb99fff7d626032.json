[{"ArticleId": 78271127, "Title": "Multifactorial optimization via explicit multipopulation evolutionary framework", "Abstract": "Multifactorial Optimization (MFO) has attracted considerable attention in the community of evolutionary computation, which aims to deal with multiple optimization tasks simultaneously by information transfer. Unfortunately, information transfer may cause both positive and negative effects. To address this issue, this paper exploits an explicit multipopulation evolutionary framework (MPEF) to intelligently take advantage of positive information transfer and effectively reduce negative information transfer. In MPEF, each task possesses an independent population and has its random mating probability for exploiting the information of other tasks. Moreover, the random mating probability of each task is adjusted adaptively. The benefits of using MPEF are twofold. 1) Various well-developed search engines can be easily embedded into MPEF for solving the single task of multifactorial optimization problems. 2) The positive information transfer can be exploited. Meanwhile, negative information transfer can be prevented. A multifactorial evolutionary algorithm (named MFMP) is realized as an instance by embedding a well-designed search engine into MPEF. The experimental results on some MFO benchmark problems demonstrate the advantage of MFMP over some state-of-the-art algorithms. Moreover, MFMP is also successfully employed to solve the spread spectrum radar polyphase code design (SSRPCD) problem.", "Keywords": "Evolutionary algorithm ; Multifactorial optimization ; Multipopulation evolutionary framework ; Differential evolution ; Spread spectrum radar polyphase code design", "DOI": "10.1016/j.ins.2019.10.066", "PubYear": 2020, "Volume": "512", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, City University of Hong Kong, Hong Kong, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Gao", "Affiliation": "School of Mathematics and Statistics, Xidian University, Xi'an, PR China;Corresponding author"}], "References": []}, {"ArticleId": 78271136, "Title": "Evolutionary many-objective assembly of cloud services via angle and adversarial direction driven search", "Abstract": "Cloud service composition (CSC) is an effective way to carry out large-scale complicated applications by the ensemble of existing individual services. Each service typically involves several Quality of Service (QoS) criteria contracted for non-functional aspects like time or price, among others, which greatly influence the overall performance of the resulting applications. Service composition approaches have emerged as an important technique in leveraging the quality of composite service efficiently and have attracted significant attention. However, most existing proposals ignore the many-objective nature of CSC and consider up to three objectives, the optimization of diverse QoS aspects of CSC from a many-objective perspective (at least four) still lacks. On another aspect, due to the rapid growth of nondominated solutions in high-dimensional objective spaces, the traditional multi-objective optimization algorithms are usually not capable of handling problems possessing many objectives. To address the above issue, we develop an angle and adversarial direction based optimizer for many-objective CSC scenarios, which evolves a number of subpopulations with adversarial search directions in a parallel paradigm. Additionally, vector angle based selection criterion, which adaptively captures beacon individuals, is utilized to diversify the population. Extensive experiments are carried out on a series of CSC instances utilizing synthetic datasets and the results show that our proposition is competitive and has better versatility compared with the state-of-the-art.", "Keywords": "Service composition ; Evolutionary algorithm ; Many-objective optimization ; Angle based selection ; Adversarial search", "DOI": "10.1016/j.ins.2019.10.054", "PubYear": 2020, "Volume": "513", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Digital Manufacturing Equipment and Technology, School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan 430074, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Digital Manufacturing Equipment and Technology, School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan 430074, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, South China University of Technology, Guangzhou 510640, Guangdong, China"}, {"AuthorId": 4, "Name": "Chun<PERSON> Zhang", "Affiliation": "State Key Laboratory of Digital Manufacturing Equipment and Technology, School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan 430074, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, The Hong Kong Polytechnic University, Hung Hom, Kowloon, Hong Kong"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, Northeastern University, Boston, MA 02115, USA"}], "References": [{"Title": "Quality Evaluation of Solution Sets in Multiobjective Optimisation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 78271200, "Title": "Handwriting posture prediction based on unsupervised model", "Abstract": "Writing is an important basic skill for humans. To acquire such a skill, pupils often have to practice writing for several hours each day. However, different pupils usually possess distinct writing postures. Bad postures not only affect the speed and quality of writing, but also severely harm the healthy development of pupils’ spine and eyesight. Therefore, it is of key importance to identify or predict pupils’ writing postures and accordingly correct bad ones. In this paper, we formulate the problem of handwriting posture prediction for the first time. Further, we propose a neural network constructed with small convolution kernels to extract features from handwriting, and incorporate unsupervised learning and handwriting data analysis to predict writing postures. Extensive experiments reveal that our approach achieves an accuracy rate of 93.3%, which is significantly higher than the 76.67% accuracy of human experts.", "Keywords": "Pupils ; Writing posture prediction ; Features extracting ; Neural network model ; Unsupervised learning ; Data analysis", "DOI": "10.1016/j.patcog.2019.107093", "PubYear": 2020, "Volume": "100", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Zhejiang Gongshang University, Hangzhou 310018, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Zhejiang Gongshang University, Hangzhou 310018, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Zhejiang Gongshang University, Hangzhou 310018, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Zhengzhou University, Zhengzhou 450000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Zhengzhou University, Zhengzhou 450000, China;Corresponding author."}], "References": []}, {"ArticleId": 78271315, "Title": "Characterization of MEMS comb capacitor", "Abstract": "<p>With the advancement of micro-electro-mechanical systems (MEMS) technologies, it is compulsory to have the sources which power the micro devices at micron scale. Due to the miniaturization, compactness, inexpensive and ease of integration with the standard process compatibility, CMOS-MEMS capacitor is characterized. There are two major parts of characterization; static mode and dynamic mode. The moveable shuttle remains in static mode until the input voltage reaches to 50 V. In static mode, due to constant capacitance among stator and shuttle fingers, charge accumulates up to 1.17 pC and level of energy stored does not go so high. The shuttle fingers initially overlap with the stator fingers by 30 μm but in dynamic mode when the voltage raises above 50 V, moveable shuttle starts moving and fingers overlapping distance reaches to 50 μm with the increase in voltage of approximately 307 V. In dynamic mode, stored energy level increases from 30 to 1800 pJ. The stored energy increases exponentially in dynamic mode due to the increased overlapping of the fingers and against elastic forces of beams (1, 2, 3 and 4).</p>", "Keywords": "", "DOI": "10.1007/s00542-019-04671-1", "PubYear": 2020, "Volume": "26", "Issue": "4", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, University of Engineering and Technology, Lahore, Pakistan;Department of Electrical and Electronic Engineering, Universiti Teknologi PETRONAS, Tronoh, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON>har <PERSON>", "Affiliation": "Department of Electrical Engineering, University of Engineering and Technology, Lahore, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Fundamental and Applied Sciences, Universiti Teknologi PETRONAS, Tronoh, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Universiti Teknologi PETRONAS, Tronoh, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Universiti Teknologi PETRONAS, Tronoh, Malaysia"}], "References": []}, {"ArticleId": 78271317, "Title": "ω-Lyndon words", "Abstract": "Let A be a finite non-empty set and ⪯ a total order on A N verifying the following lexicographic like condition: For each n ∈ N and u , v ∈ A n , if u ω ≺ v ω then u x ≺ v y for all x , y ∈ A N . A word x ∈ A N is called ω -Lyndon if x ≺ y for each proper suffix y of x . A finite word w ∈ A + is called ω -Lyndon if w ω ≺ v ω for each proper suffix v of w . In this note we prove that every infinite word may be written uniquely as a non-increasing product of ω -Lyndon words.", "Keywords": "Lyndon words ; Lexicographic orders", "DOI": "10.1016/j.tcs.2019.11.004", "PubYear": 2020, "Volume": "809", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université de Lyon, Université Lyon 1, CNRS UMR 5208, Institut Camille Jordan, 43 boulevard du 11 novembre 1918, F69622 Villeurbanne Cedex, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Université de Lyon, Université Lyon 1, CNRS UMR 5208, <PERSON> Camille Jordan, 43 boulevard du 11 novembre 1918, F69622 Villeurbanne Cedex, France;Corresponding author"}], "References": []}, {"ArticleId": 78271361, "Title": "Recognizing Profile Faces by Imagining Frontal View", "Abstract": "<p>Extreme pose variation is one of the key obstacles to accurate face recognition in practice. Compared with current techniques for pose-invariant face recognition, which either expect pose invariance from hand-crafted features or data-driven deep learning solutions, or first normalize profile face images to frontal pose before feature extraction, we argue that it is more desirable to perform both tasks jointly to allow them to benefit from each other. To this end, we propose a Pose-Invariant Model (PIM) for face recognition in the wild, with three distinct novelties. First, PIM is a novel and unified deep architecture, containing a Face Frontalization sub-Net (FFN) and a Discriminative Learning sub-Net (DLN), which are jointly learned from end to end. Second, FFN is a well-designed dual-path Generative Adversarial Network which simultaneously perceives global structures and local details, incorporating an unsupervised cross-domain adversarial training and a meta-learning (“learning to learn”) strategy using siamese discriminator with dynamic convolution for high-fidelity and identity-preserving frontal view synthesis. Third, DLN is a generic Convolutional Neural Network (CNN) for face recognition with our enforced cross-entropy optimization strategy for learning discriminative yet generalized feature representations with large intra-class affinity and inter-class separability. Qualitative and quantitative experiments on both controlled and in-the-wild benchmark datasets demonstrate the superiority of the proposed model over the state-of-the-arts.</p>", "Keywords": "Pose-invariant face recognition; Face frontalization; Cross-domain adversarial learning; Meta-learning; Learning to learn; Enforced cross-entropy optimization; Generative adversarial networks", "DOI": "10.1007/s11263-019-01252-7", "PubYear": 2020, "Volume": "128", "Issue": "2", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of North Electronic Equipment, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "JD Digits, Silicon Valley, USA"}, {"AuthorId": 4, "Name": "Shuicheng Yan", "Affiliation": "National University of Singapore, Singapore, Singapore;Yitu Technology, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Singapore, Singapore, Singapore"}], "References": []}, {"ArticleId": 78271443, "Title": "How do consumers in the sharing economy value sharing? Evidence from online reviews", "Abstract": "With the rapid development of information technology, platform-facilitated collaborative consumption has recently become attractive to consumers. A comparative study of consumers&#x27; online review behavior and its impact on overall satisfaction and demand in the accommodation-sharing economy and the hotel industry indicates that consumers&#x27; perceptions and behavior change gradually with changes in the level of sharing—from no sharing when staying in hotel rooms to intensive sharing when sharing rooms through collaborative consumption. Online consumer reviews focus on product and service attributes, and the influential factors of customer satisfaction and demand differ when consumers are at different accommodation-sharing levels. Not all attributes described in online reviews influence overall customer satisfaction. With a higher level of sharing, consumers&#x27; valuation changes from more to less tangible attributes. Consumers at a higher sharing level care more about social interaction and economic value than consumers at a lower sharing level. Transaction costs, particularly the information search and acquisition costs, play an important role in influencing customer purchase decisions in the sharing economy. Consumers refer to direct information for tangible attributes and to previous consumers&#x27; online reviews for intangible attributes to familiarize themselves with details before making purchase decisions. Our study provides implications that help platforms and hosts better target consumer segments with different sharing levels and more effectively utilize online reviews to generate positive electronic word of mouth to enhance consumer demand and the performance of platform economics.", "Keywords": "Sharing economy ; Customer satisfaction ; Product and service attributes ; Online reviews", "DOI": "10.1016/j.dss.2019.113162", "PubYear": 2020, "Volume": "128", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management, Operations, and Marketing, College of Business Administration, California State University, Stanislaus, One University Circle, Turlock, CA 95382, United States"}], "References": []}, {"ArticleId": 78271790, "Title": "Guest editorial: revised selected papers from the LION 12 conference", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10472-019-09677-z", "PubYear": 2020, "Volume": "88", "Issue": "1-3", "JournalId": 27228, "JournalTitle": "Annals of Mathematics and Artificial Intelligence", "ISSN": "1012-2443", "EISSN": "1573-7470", "Authors": [{"AuthorId": 1, "Name": "Ilias S. Kotsireas", "Affiliation": "CARGO Lab, Wil<PERSON>rid Laurier University, Waterloo, Canada"}, {"AuthorId": 2, "Name": "Panos <PERSON>", "Affiliation": "Center for Applied Optimization, University of Florida, Gainesville, USA"}], "References": []}, {"ArticleId": 78271876, "Title": "Influence of air humidity on the low-frequency capacitance of silicon MOS structures with a nanoscale oxide layer", "Abstract": "The influence of air humidity on the low-frequency capacitance of Mо - SiO<sub>2</sub> - NN<sup>+</sup>Si(111) structures with a donor concentration of 2∙10<sup>21</sup> m<sup><sub>−</sub>3</sup> in the N silicon layer and a thin porous oxide, about 5 nm thick, has been investigated. It has been established that in weakly inverted structures the capacitance of surface states at the silicon - oxide interface to a great extent determines the structures low-frequency capacitance. Largely, these states are induced by water molecules adsorbed by a thin porous oxide layer from the air. The recharging of the surface states is carried out by minority charge carriers and is determined by the generation-recombination processes in the silicon space charge region. This allows the surface states capacitance, as well as the total structure capacitance, to linearly depend on the humidity of the surrounding gas medium. The obtained expression for calculation of the low-frequency capacitance of the studied structures adequately describes the experimental dependences and serves as a proof of the new low-frequency conductivity model proposed in the paper for such structures.", "Keywords": "Air humidity ; MOS structure ; Nanoscale silicon oxide ; Low-frequency capacitance ; Surface states ; Minority carriers ; Generation-recombination processes", "DOI": "10.1016/j.snb.2019.127318", "PubYear": 2020, "Volume": "304", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Odessa I.I.<PERSON> National University, 4 <PERSON>., UA-65058 Odessa, Ukraine"}], "References": []}, {"ArticleId": 78272044, "Title": "Can children benefit from early internet exposure? Short- and long-term links between internet use, digital skill, and academic performance", "Abstract": "Educational policymakers are optimistic that providing young children access to technology can catalyze academic achievement and eventual positive labor market outcomes. However, possessing digital skill – or the ability to use technology effectively – might be necessary for young children to realize measurable benefits from the Internet. In the present longitudinal study, we explored whether Internet use and digital skill in early childhood predicted academic performance in middle childhood. We surveyed 101 US parents when their children were roughly 5 years and 11 years, collecting data on children&#x27;s Internet use, digital skill, and academic performance. Structural equation modeling revealed that children&#x27;s time online in early childhood was a marginally significant negative predictor of middle childhood academic performance, but digital skill in early childhood was a marginally significant positive predictor ( p s &lt; .1). Moreover, digital skill in early childhood indirectly influenced middle childhood academic performance via middle childhood digital skill. Early childhood digital skill significantly predicted middle childhood digital skill, which was significantly and positively associated with school performance. These findings suggest that allowing young children to engage with digital technologies for some amount of can be beneficial, provided that children use that time fruitfully to acquire digital skill.", "Keywords": "Early years education ; Elementary education ; 21st century abilities", "DOI": "10.1016/j.compedu.2019.103750", "PubYear": 2020, "Volume": "146", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center on Media and Human Development, Northwestern University, 2240 Campus Drive, Evanston, IL 60208-3545, USA;Corresponding author. Present address: Lexia Learning Systems LLC, a Rosetta Stone company, 300 Baker Avenue, Suite 320, Concord, MA 01742, USA."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Keiser University Graduate School, 1900 W. <PERSON> Blvd., Ft. Lauderdale, FL 33309, USA;1900 E. Golf Rd., Suite 950A, Schaumburg, IL 60173, USA"}], "References": []}, {"ArticleId": 78272457, "Title": "Fuzzy energy based active contour model for multi-region image segmentation", "Abstract": "<p>In this article, we present a new multi-phase pseudo 0.5 level set framework on fuzzy energy based active contour model to segment images into more than two regions. The proposed method is a generalization of fuzzy active contour based on 2-phase segmentation (object and background), developed by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. The proposed method needs only l o g <sub>2</sub> n pseudo 0.5 level set functions for n phase piece-wise constant case. In piece-wise smooth case, only two pseudo 0.5 level set functions are sufficient to represent any partition based on ‘the four colo theorem. The proposed fuzzy active contour model can segment images into multiple regions instead of two regions (object and background) based on curve evolution. In this article, instead of solving the <PERSON><PERSON><PERSON>-<PERSON> equation, a multi-phase pseudo 0.5 level set based optimization is proposed to speed up the convergence. Finally, the proposed method is compared with state-of-the-art techniques on several images. Analysis (both qualitative and quantitative) of the results concludes that the proposed method segments images into multiple regions in a better way as compared to the existing ones.</p>", "Keywords": "Multi-phase pseudo level set; Fuzzy energy; Active contour model; Four color theorem; Segmentation", "DOI": "10.1007/s11042-019-08207-7", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Machine Intelligence Unit, Indian Statistical Institute, Kolkata, India"}], "References": []}, {"ArticleId": 78272479, "Title": "Multiobjective evolutionary-based multi-kernel learner for realizing transfer learning in the prediction of HIV-1 protease cleavage sites", "Abstract": "<p>Due to the unavailability of adequate patients and expensive labeling cost, many real-world biomedical cases have scarcity in the annotated data. This holds very true for HIV-1 protease specificity problem where only a few experimentally verified cleavage sites are present. The challenge then is to exploit the auxiliary data. However, the problem becomes more complicated when the underlying train and test data are generated from different distributions. To deal with the challenges, we formulate the HIV-1 protease cleavage site prediction problem into a bi-objective optimization problem and solving it by introducing a multiobjective evolutionary-based multi-kernel model. A solution for the optimization problem will lead us to decide the optimal number of base kernels with the best pairing of features. The bi-objective criteria encourage different individual kernels in the ensemble to mitigate the effect of distribution difference in training and test data with the ideal number of base kernels. In this paper, we considered eight different feature descriptors and three different kernel variants of support vector machines to generate the optimal multi-kernel learning model. Non-dominated sorting genetic algorithm-II is employed with bi-objective of achieving a maximum area under the receiver operating characteristic curve simultaneously with a minimum number of features. To validate the effectiveness of the model, the experiments were performed on four HIV-1 protease datasets. The performance comparison with fifteen state-of-the-art techniques on average accuracy and area under curve has been evaluated to justify the improvement of the proposed model. We then analyze Friedman and post hoc tests to demonstrate the significant improvement. The result obtained following the extensive experiment enumerates the bi-objective multi-kernel model performance enhancement on within and cross-learning over the other state-of-the-art techniques.</p>", "Keywords": "HIV-1 protease; Multi-kernel; Multiobjective evolutionary algorithm; Transfer learning", "DOI": "10.1007/s00500-019-04487-1", "PubYear": 2020, "Volume": "24", "Issue": "13", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Raipur, Raipur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Raipur, Raipur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Raipur, Raipur, India"}], "References": []}, {"ArticleId": 78272524, "Title": "An eigendecomposition method based on deep learning and probabilistic graph model", "Abstract": "<p>With the rapid development of computer, computer vision derived from computer vision has also made important progress in the field of image research. The extraction of image information is the most basic work in the field of image research. However, in the current environment, there is still a lack of effective methods to understand more complex image problems, such as image shape, material and illumination distribution in the environment. Eigenimage decomposition can be achieved by obtaining albedo eigenvalues and luminance eigenvalues. The color and illumination information of the image can be obtained more intuitively. Based on this, this paper proposes an intrinsic image decomposition method based on depth learning and probability graph model, in order to extract image information more accurately. Firstly, a deep convolution neural network is trained to decompose reflectivity image and shadow image. Then the conditional random field is used to optimize the reflectivity image and shadow image. The convolutional neural network designed in this paper obtains preliminary results through multi-scale architecture, deep supervision, step-by-step refinement of synthetic images and multi-stage training, which has been significantly improved compared with previous algorithms. Then the essential image and the corresponding gradient image are further optimized by conditional random field, and the eigenvalue image with richer details and clearer boundary can be obtained.</p>", "Keywords": "Eigendecomposition method; Deep learning; Probability graph model; Convolutional neural network; Conditional random field", "DOI": "10.1007/s12652-019-01555-0", "PubYear": 2020, "Volume": "11", "Issue": "9", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Army Engineering University of PLA, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "@qq.com;Army Engineering University of PLA, Nanjing, China"}, {"AuthorId": 3, "Name": "Z<PERSON>ong Pan", "Affiliation": "@njust.edu.cn;Army Engineering University of PLA, Nanjing, China"}], "References": []}, {"ArticleId": ********, "Title": "Optimisation of Server Selection for Maximising Utility in Erlang-Loss Systems", "Abstract": "This paper undertakes the challenge of server selection problem in Erlang-loss system (ELS). We propose a novel approach to the server selection problem in the ELS taking into account probabilistic modelling to reflect a practical scenario when user arrivals vary over time. The proposed framework is divided into three stages, including i) developing a new method for server selection based on the M/ M/ n/ n queuing model with probabilistic arrivals; ii) combining server allocation results with further research on utility-maximising server selection to optimise system performance; and iii) designing a heuristic approach to efficiently solve the developed optimisation problem. Simulation results show that by using this framework, Internet Service Providers (ISPs) can significantly improve QoS for better revenue with optimal server allocation in their data centre networks. © 2020. <PERSON><PERSON><PERSON> et al., licensed to EAI. All Rights Reserved.", "Keywords": "Erlang-Loss System (ELS); Server Selection; Utility function", "DOI": "10.4108/eai.24-10-2019.161367", "PubYear": 2020, "Volume": "7", "Issue": "22", "JournalId": 42306, "JournalTitle": "EAI Endorsed Transactions on Industrial Networks and Intelligent Systems", "ISSN": "", "EISSN": "2410-0218", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Computing Services, University of Cambridge, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science and Technology, Middlesex University, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Electrical Engineering, University College London, United Kingdom"}], "References": []}, {"ArticleId": ********, "Title": "Retrieving the resource availability calendars of a process from an event log", "Abstract": "Knowing the availability of human resources for a business process is required, e.g., when allocating resources to work items, or when analyzing the process using a simulation model. In this respect, it should be taken into account that staff members are not permanently available and that they can be involved in multiple processes within the company. Consequently, it is far from trivial to specify their availability for the single process from, e.g., generic timetables. To this end, this paper presents a new method to automatically retrieve resource availability calendars from event logs containing process execution information. The retrieved resource availability calendars are the first to take into account (i) the temporal dimension of availability, i.e. the time of day at which a resource is available, and (ii) intermediate availability interruptions (e.g. due to a break). Empirical evaluation using synthetic data shows that the method’s key outputs closely resemble their equivalents in reality.", "Keywords": "Resource availability calendars ; Human resources availability ; Process mining ; Event logs ; Process execution data", "DOI": "10.1016/j.is.2019.101463", "PubYear": 2020, "Volume": "88", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hasselt University, Research group Business Informatics, Martelarenlaan 42, 3500 Hasselt, Belgium;Research Foundation Flanders (FWO), Egmontstraat 5, 1000 Brussels, Belgium;Corresponding author at: Hasselt University, Research group Business Informatics, Martelarenlaan 42, 3500 Hasselt, Belgium."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hasselt University, Research group Business Informatics, Martelarenlaan 42, 3500 Hasselt, Belgium"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hasselt University, Research group Logistics, Martelarenlaan 42, 3500 Hasselt, Belgium"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hasselt University, Research group Business Informatics, Martelarenlaan 42, 3500 Hasselt, Belgium"}], "References": []}, {"ArticleId": 78272856, "Title": "Color image encryption based on hybrid chaotic system and DNA sequences", "Abstract": "<p>Stability and complexity of the encryption process besides affordable encryption time are the two challenges of image encryption. In this paper, Chen chaotic system is applied to produce random sequences and using these sequences some arrays are created for image permutation and key stream production. Since these random sequences have two different applications, separate calculation time is not necessary. As a result, time complexity decreases. Each of the color components of the plain image are converted to a one dimensional vector for image permutation and the permutation is performed using the generated chaotic arrays. Created key stream is converted into a chaotic image. Then permutated image and the chaotic image are divided into equal blocks. The plain image blocks are encoded using DNA rules. The encoding rules are chosen randomly using a three-dimensional Logistic map. This process makes it possible to have various options for choosing DNA coding rules. Finally, all the encrypted blocks are combined and the encrypted image is obtained. Experimental results show that the proposed approach has a large key space and is resistant against different attacks. Also, the correlation between the neighboring pixels is decreased and the resulting entropy is very close to ideal.</p>", "Keywords": "Image encryption; Stability; DNA sequence; 3D Logistic map; Chen system", "DOI": "10.1007/s11042-019-08247-z", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Abolfazl Yaghouti Niyat", "Affiliation": "Quchan Technical and Vocational University of Iran, Quchan, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Mashhad Branch, Islamic Azad University, Mashhad, Iran"}], "References": []}, {"ArticleId": 78272881, "Title": "Design and development of virtual instrument for fault diagnosis in fractal antenna array", "Abstract": "<p>This paper deals with the development of a virtual instrument for fault diagnosis in fractal antenna array using Lab‐VIEW software. Faults in antenna array are considered on the basis of the radiation pattern. In this study, theta and gain values of radiation patterns for each fault are used in Lab‐VIEW for curve fitting. An artificial neural network (ANN) has been developed for fitted data points using the Leavenberg <PERSON>rd algorithm in MATLAB software and mean square error (MSE) is minimized. The designed ANN model has been embedded in the virtual instrument. The proposed virtual instrument system gets test patterns as input and generates output for several faults present in antenna array. Simulated and measured results of the fractal antenna array are validated experimentally. This virtual instrument model has not been developed for fractal antenna array so far.</p>", "Keywords": "antenna array;radiation pattern;curve fitting;artificial neural model;virtual instrument", "DOI": "10.1002/mmce.22026", "PubYear": 2020, "Volume": "30", "Issue": "1", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> College of Engineering, Punjabi University, Talwandi Sabo, Punjab, India"}, {"AuthorId": 2, "Name": "Jagtar S. Sivia", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> College of Engineering, Punjabi University, Talwandi Sabo, Punjab, India"}], "References": []}, {"ArticleId": 78272903, "Title": "Multiple Shooting Method for Solving Black–Scholes Equation", "Abstract": "<p>In this paper, the Black–Scholes (B–S) model for the pricing of the European and the barrier call options are considered, which yields a partial differential problem. First, A numerical technique based on <PERSON><PERSON><PERSON> (C–N) method is used to discretisize the time domain. Consequently, the partial differential equation will be converted to a system of an ordinary differential equation (ODE). Then, the multiple shooting method combined with Lagrange polynomials is utilized to solve the ODEs. Regarding the convergence order of the approximate solution which normally decreases due to the non-smooth properties of the option’s payoff (at the strike price), in this study, the equipped C–N scheme with variable step size strategy is applied for the time discretization. As a result, the variable step size strategy prevents the error propagation by controlling the error at each time step and increases the computational speed by raising the step size in the smooth points of the domain. In order to implement the variable step size, an algorithm is presented. In addition, the stability of the presented method is analyzed. The extracted numerical results represent the accuracy and efficiency of the proposed method.</p>", "Keywords": "Black–Scholes equation; Multiple shooting method; <PERSON><PERSON><PERSON> method; Option pricing; Variable step size", "DOI": "10.1007/s10614-019-09940-9", "PubYear": 2020, "Volume": "56", "Issue": "4", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Sciences, Azarbaijan Shahid Madani University, Tabriz, Iran;Department of Applied Mathematics, Faculty of Mathematical Sciences, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Sciences, Azarbaijan Shahid Madani University, Tabriz, Iran"}, {"AuthorId": 3, "Name": "Safar Irandoust-Pakchin", "Affiliation": "Department of Applied Mathematics, Faculty of Mathematical Sciences, University of Tabriz, Tabriz, Iran"}], "References": []}, {"ArticleId": 78273246, "Title": "Machine vision gait-based biometric cryptosystem using a fuzzy commitment scheme", "Abstract": "In this paper, a fuzzy commitment scheme is applied with a machine vision gait-based biometric system to enhance system security. The proposed biometric cryptosystem has two phases: enrolment and verification. Each of them comprises three main stages: feature extraction, reliable components extraction, and fuzzy commitment scheme. Gait features are extracted from gait images using local ternary pattern (LTP), and then, the average of one complete gait cycle using the gait energy image (GEl) concept is calculated. The average images are joined using a 2D joint histogram, which is reduced using principal component analysis (PCA) to produce the final feature vector. To enhance the robustness of the system, only highly robust and reliable bits from the feature vector are extracted. Finally, the fuzzy commitment scheme is used to secure feature templates. Bose–<PERSON><PERSON>–Hocquenghem codes (BCH) are used for key encoding in the enrolment phase and for decoding in the verification phase. The proposed system is tested using the CMU MoBo and CASIA A databases. The experimental results show that the best error rate for the CMU MoBo database is obtained when using a fast walk for enrolment and verification, where we obtain 0% for the false acceptance rate (FAR) and 0% for the false rejection rate (FRR) for a key length equal to 50 bits. The best error rate for CASIA A dataset is obtained when using the 45-degree direction to the image plane view for enrolment and verification, where we obtain 0% for the false acceptance rate (FAR) and 0% for the false rejection rate (FRR) for a key length equal to 45 bits.", "Keywords": "Biometrics cryptosystems ; Fuzzy commitment ; Machine vision gait ; Key binding ; Local ternary pattern ; Gait energy image", "DOI": "10.1016/j.jksuci.2019.10.011", "PubYear": 2022, "Volume": "34", "Issue": "2", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "Lamiaa <PERSON>", "Affiliation": "Computer Science Department, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah, Saudi Arabia;Electrical Engineering Department, Faculty of Engineering at Shoubra, Benha University, Cairo, Egypt;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah, Saudi Arabia;Computer Science and Information Department, College of Al-Hynakiah Community, Taibah University, Medina, Saudi Arabia"}], "References": []}, {"ArticleId": 78273247, "Title": "An Enhanced Multiclass Support Vector Machine Model and its Application to Classifying File Systems Affected by a Digital Crime", "Abstract": "The digital revolution we are witnessing nowadays goes hand in hand with a revolution in cybercrime. This irrefutable fact has been a major reason for making digital forensic (DF) a pressing and timely topic to investigate. Thanks to the file system which is a rich source of digital evidence that may prove or deny a digital crime. Yet, although there are many tools that can be used to extract potentially conclusive evidence from the file system, there is still a need to develop effective techniques for evaluating the extracted evidence and link it directly to a digital crime. Machine learning can be posed as a possible solution looming in the horizon. This article proposes an Enhanced Multiclass Support Vector Machine (EMSVM) model that aims to improve the classification performance. The EMSVM suggests a new technique in selecting the most effective set of parameters when building a SVM model. In addition, since the DF is considered a multiclass classification problem duo to the fact that a file system might be accecced by more than one application, the EMSVM enhances the class assignment mechanism by supporting multi-class classification. The article then investigates the applicability of the proposed model in analysing incriminating digital evidence by inspecting the historical activities of file systems to realize if a malicious program manipulated them. The results obtained from the proposed model were promising when compared to several machine-learning algorithms.", "Keywords": "Digital-forensics ; File-systems ; SVM ; Log-Files ; Digital-evidence", "DOI": "10.1016/j.jksuci.2019.10.010", "PubYear": 2022, "Volume": "34", "Issue": "2", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Information Systems, College of Computer Science and Information Technology, <PERSON> F<PERSON>al University, P.O. Box 1982, Dammam, Saudi Arabia"}], "References": [{"Title": "Selective encryption on ECG data in body sensor network based on supervised machine learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "59", "JournalTitle": "Information Fusion"}, {"Title": "SVM-based image partitioning for vision recognition of AGV guide paths under complex illumination conditions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101856", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": ********, "Title": "Enhanced Zone-Based Energy Aware Data Collection Protocol for WSNs (E-ZEAL)", "Abstract": "In the era of IoT, the energy consumption of sensor nodes in WSN is one of the main challenges. It is crucial to reduce energy consumption due to the limited battery life of the sensor nodes. Recently, Zone-based Energy-Aware data coLlection (ZEAL) routing protocol is proposed to improve energy consumption and data delivery. In this paper, an enhancement to ZEAL is proposed to improve WSN performance in terms of energy consumption and data delivery. Enhanced ZEAL (E-ZEAL) applies the K-means clustering algorithm to find the optimal path for the mobile-sink node. As well, it provides better selections for sub-sink nodes. The experiments are performed using the ns-3 simulator. The performance of E-ZEAL is compared to ZEAL. E-ZEAL reduces the number of hops and distance by more than 50%, Leading to speed up the data-collection phase by more than 30% with complete delivery of data. Moreover, E-ZEAL improves the lifetime of the network by 30%.", "Keywords": "Internet of Things (IoT) ; Wireless Sensor Networks (WSNs) ; Routing protocols ; Zone-based Energy-Aware data coLlection (ZEAL)", "DOI": "10.1016/j.jksuci.2019.10.012", "PubYear": 2022, "Volume": "34", "Issue": "2", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Faculty of Computers & Informatics, Benha University, Egypt;Corresponding author at: Department of Computer Science, Faculty of Computers and Informatics, Benha University, Benha Mansoura Road, next to Holding Company for Water Supply and Sanitation Benha, Qalyubia Governorate, Egypt"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Faculty of Computers & Informatics, Benha University, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Faculty of Computers & Informatics, Benha University, Egypt"}], "References": []}, {"ArticleId": 78273250, "Title": "Locality preserving partial least squares discriminant analysis for face recognition", "Abstract": "We propose a locality preserving partial least squares discriminant analysis (LPPLSDA) which adds a locality preserving feature to the conventional partial least squares discriminant analysis(PLS-DA). The locality preserving feature captures the within group structural information via a similarity graph. The ability of LPPLS-DA to capture local structures allows it to be better suited for face recognition. We evaluate the performance of our proposed method on several benchmarked face databases which offer different levels of complexity in terms of sample size as well as image acquisition conditions. The experimental results indicate that, for each database used, the proposed method consistently outperformed the conventional PLS-DA method.", "Keywords": "Partial least squares discriminant analysis ; Linear discriminant analysis ; Locality preserving projection ; Manifold structure ; Machine learning", "DOI": "10.1016/j.jksuci.2019.10.007", "PubYear": 2022, "Volume": "34", "Issue": "2", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mathematical Sciences, Universiti Sains Malaysia, 11800 Minden, Pulau <PERSON>, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Universiti Sains Malaysia, 11800 Minden, <PERSON><PERSON><PERSON>, Malaysia;Corresponding author"}], "References": []}, {"ArticleId": 78273256, "Title": "An Empirical Survey of Functions and Configurations of Open-Source Capture the Flag (CTF) Environments", "Abstract": "Capture the Flag (CTF) is a computer security competition that is generally used to give participants experience in securing (virtual) machines and responding to cyber attacks. CTF contests have been getting larger and are receiving many participants every year (e.g., DEFCON, NYU-CSAW). CTF competitions are typically hosted in virtual environments, specifically set up to fulfill the goals and scenarios of the CTF. This article investigates the underlying infrastructures and CTF environments, specifically open-source CTF environments. A systematic review is conducted to assess functionality and game configuration in CTF environments where the source code is available on the web (i.e., open-source software). In particular, from out of 28 CTF platforms, we found 12 open-source CTF environments. As four platforms were not installable for several reasons, we finally examined 8 open-source CTF environments ( PicoCTF, FacebookCTF, HackTheArch, WrathCTF, Pedagogic-CTF, RootTheBox, CTFd and Mellivora ) regarding their features and functions for hosting CTFs (e.g., scoring, statistics or supported challenge types) and providing game configurations (e.g., multiple flags, points, hint penalities). Surprisingly, while many platforms provide similar base functionality, game configurations between the platforms varied strongly. For example, hint penalty, time frames for solving challenges, limited number of attempts or dependencies between challenges are game options that might be relevant for potential CTF organizers and for choosing a technology. This article contributes to the general understanding of CTF software configurations and technology design and implementation. Potential CTF organizers and participants may use this as a reference for challenge configurations and technology utilization. Based on our analysis, we would like to further review commercial and other platforms in order to establish a golden standard for CTF environments and further contribute to a better understanding of CTF design and development.", "Keywords": "CTF ; Capture the Flag ; Cyber range ; Computer network operations ; Cyber security exercises ; Cyber security training", "DOI": "10.1016/j.jnca.2019.102470", "PubYear": 2020, "Volume": "151", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "AIT Austrian Institute of Technology, Center for Digital Safety & Security, Giefinggasse 4, 1210, Vienna, Austria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "AIT Austrian Institute of Technology, Center for Digital Safety & Security, Giefinggasse 4, 1210, Vienna, Austria;Corresponding author"}], "References": []}, {"ArticleId": 78273259, "Title": "An efficient reinforcement learning-based Botnet detection approach", "Abstract": "The use of bot malware and botnets as a tool to facilitate other malicious cyber activities (e.g. distributed denial of service attacks, dissemination of malware and spam, and click fraud). However, detection of botnets, particularly peer-to-peer (P2P) botnets, is challenging. Hence, in this paper we propose a sophisticated traffic reduction mechanism, integrated with a reinforcement learning technique. We then evaluate the proposed approach using real-world network traffic, and achieve a detection rate of 98.3%. The approach also achieves a relatively low false positive rate (i.e. 0.012%).", "Keywords": "Botnet detection ; Network security ; Traffic reduction ; Neural network ; C2C ; Reinforcement-learning", "DOI": "10.1016/j.jnca.2019.102479", "PubYear": 2020, "Volume": "150", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Faculty of Information Technology, Zarqa University, Zarqa, Jordan;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Information Sciences, Faculty of Engineering and Environment, Northumbria University, Newcastle Upon Tyne, NE1-8ST, UK"}, {"AuthorId": 3, "Name": "Mouhammd Al-kasassbeh", "Affiliation": "Department of Computer Science, Princess <PERSON><PERSON><PERSON> for Technology, Amman, Jordan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Khan", "Affiliation": "Department of Computer and Information Sciences, Faculty of Engineering and Environment, Northumbria University, Newcastle Upon Tyne, NE1-8ST, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Princess <PERSON><PERSON><PERSON> for Technology, Amman, Jordan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems and Cyber Security, University of Texas at San Antonio, San Antonio, TX, 78249, USA"}], "References": []}, {"ArticleId": 78273762, "Title": "Crystallographic orientation and grain size data obtained by Electron Back Scatter Diffraction (EBSD) on quartz analysed in mylonitic quartzite from the Island of Elba (Italy)", "Abstract": "Raw Electron Back Scatter Diffraction (EBSD) data on deformed quartz from a mylonitic quartzite sample of the Calamita Schists (Island of Elba, Italy) is available at https://doi.org/10.17632/8c937t6zs4.3 . The investigated sample (IESP3SP78) was collected in quartz-rich outcrops exposed at the Praticciolo Cape and was used to realize an oriented thin section (cut parallel to lineation and perpendicular to foliation). Preliminary investigations were carried out by transmitted-light and scanning electron microscopy (SEM), in order to select key areas for EBSD analysis. EBSD mapping was performed on selected areas of deformed quartz, which was the only phase indexed and were processed to derive orientation maps, pole figures, inverse pole figures, misorientation axis distribution in sample and crystal coordinates. While the processed data is available on the original research article (“Fluid-assisted Strain Localization in Quartz at the Brittle/Ductile Transition; https://doi.org/10.1029/2019GC008270 ), this contribution is devoted to supply the unprocessed EBSD data, together with a methodological description, aimed to allow the reproduction of the processed dataset. A brief statistical description of the investigated EBSD maps is also available. This data is valuable because it offers grain size and orientation analysis of deformed quartz investigated in a natural study case and the present publication makes it accessible to those working on naturally and experimentally deformed quartz.", "Keywords": "EBSD ; Quartz ; Grain size ; Crystallographic orientation ; Mylonite ; Brittle/ductile transition ; Metamorphic fluids", "DOI": "10.1016/j.dib.2019.104744", "PubYear": 2020, "Volume": "30", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Earth Sciences, University of Pisa, 56125 Pisa, PI, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Earth Sciences, University of Pisa, 56125 Pisa, PI, Italy"}], "References": []}, {"ArticleId": 78273790, "Title": "Introducing complexity to formal testing", "Abstract": "A general theory introducing asymptotic complexity to testing is presented. Our goal is measuring how fast the effort of testing must increase to reach higher levels of partial certainty on the correctness of the implementation under test (IUT). By recent works it is known that, for many practical testing scenarios, any partial level of correctness certainty less than 1 (where 1 means full certainty) can be reached by some finite test suite. In this paper we address the problem of finding out how fast must these test suites grow as long as the target level gets closer to 1. More precisely, we want to study how test suites grow with α , where α is the inverse of the distance to 1 (e.g., if α = 4 then our target level is 0.75 = 1 − 1 4 ). A general theory to measure this testing complexity is developed. We use this theory to analyze the testing complexity of some general testing problems, as well as the complexity of some specific testing strategies for these problems, and discover that they are within e.g., O ( l o g α ) , O ( l o g 2 α ) , O ( α ) , O ( α l o g α ) , or O ( α ) . Similarly as the computational complexity theory conceptually distinguishes between the complexity of problems and algorithms , tightly identifying the complexity of a testing problem will require reasoning about any testing strategy for the problem. The capability to identify testing complexities will provide testers with a measure of the productivity of testing, that is, a measure of the utility of applying the ( n + 1 ) -th planned test case (after having passed the n previous ones) in terms of how closer would that additional test case get us to the (ideal) complete certainty on the IUT (in-)correctness.", "Keywords": "Testing ; Complexity theory", "DOI": "10.1016/j.jlamp.2019.100502", "PubYear": 2020, "Volume": "111", "Issue": "", "JournalId": 781, "JournalTitle": "Journal of Logical and Algebraic Methods in Programming", "ISSN": "2352-2208", "EISSN": "2352-2216", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dpto. Sistemas Informáticos y Computación, Facultad de Informática, Universidad Complutense de Madrid, 28040 Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dpto. Sistemas Informáticos y Computación, Facultad de Informática, Universidad Complutense de Madrid, 28040 Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dpto. Sistemas Informáticos y Computación, Facultad de Informática, Universidad Complutense de Madrid, 28040 Madrid, Spain;Corresponding author."}], "References": []}, {"ArticleId": 78273974, "Title": "A comparison of methods used for inducing mental fatigue in performance research: individualised, dual-task and short duration cognitive tests are most effective", "Abstract": "<p>Despite research indicating the negative impact that mental fatigue has on physical and cognitive performance, whether this is a result of mental fatigue or a state of under-arousal remains unclear. The current research aimed to explore the effectiveness of the methods being used to induce mental fatigue. Twelve participants attended six sessions in which two cognitive tests, the AX-continuous performance test (AX-CPT) and the TloadDback test, were compared for their effectiveness in inducing mental fatigue. Both tests were set at a standard processing speed (1.2 ms) for two conditions, and a further condition involved the individualisation of the TloadDback test. Participants presented significantly higher physiological and psychological arousal (<i>p</i> &lt; 0.05) in the individualised dual-task test compared to the AX-CPT. The individualised TloadDback test is a more effective method of inducing mental fatigue compared to the AX-CPT, as it sustains physiological arousal whilst inducing measurable reductions in mental resources. <b>Practitioner summary:</b> Mental fatigue negatively impacts physical and cognitive performance. It is unclear whether the current methods being used to induce mental fatigue are effective. This study compared different methods and confirmed that short, individualised and dual-task tests are most effective for inducing mental fatigue whilst maintaining arousal.</p>", "Keywords": "Mental fatigue ; arousal ; galvanic skin conductance ; heart rate variability ; cognitive loading", "DOI": "10.1080/00140139.2019.1687940", "PubYear": 2020, "Volume": "63", "Issue": "1", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Environmental Ergonomics Research Centre, Loughborough University, Loughborough, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Environmental Ergonomics Research Centre, Loughborough University, Loughborough, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Environmental Ergonomics Research Centre, Loughborough University, Loughborough, UK"}], "References": []}, {"ArticleId": 78274071, "Title": "Computational thinking education: Issues and challenges", "Abstract": "Computational Thinking is a term applied to describe the increasing attention on students' knowledge development about designing computational solutions to problems, algorithmic thinking, and coding. It focuses on skills children develop from practicing programming and algorithms, and enables the development of qualities such as abstract thinking, problem solving, pattern recognition, and logical reasoning. Contemporary educational and infrastructural developments, like “CS for All” ( https://www.csforall.org/ ), ISTE's Standards for Students in Computational Thinking ( https://www.iste.org/explore/Solutions/Computational-thinking-for-all?articleid=152 ), Computer Science Teachers Association's Concepts of Computational Thinking ( http://advocate.csteachers.org/2014/09/15/computational-thinking-and-beyond/ ), and the appearance of tools such as robotics, 3D printing, microprocessors, and intuitive programming languages posit Computational Thinking as a very promising area to support these learning competences. In this special issue of Computers in Human Behavior , the Editors report four studies conducted by interdisciplinary teams. The introduction to the special issue also draws attention to the great potential and need for further research in the area of Computational Thinking Education to engage students in meaningful learning so as to develop useful thinking skills and digital competences. Finally, the Editorspropose directions for future research and practice in Computational Thinking Education.", "Keywords": "Computational thinking ; Digital competences ; Coding ; Technological fluency ; Algorithmic thinking ; Robotics", "DOI": "10.1016/j.chb.2019.106185", "PubYear": 2020, "Volume": "105", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Cyprus, Cyprus Norwegian University of Science and Technology, Norway;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Norwegian University of Science and Technology, Norway"}], "References": []}, {"ArticleId": 78274086, "Title": "A systematic literature review of machine learning techniques for software maintainability prediction", "Abstract": "Context Software maintainability is one of the fundamental quality attributes of software engineering. The accurate prediction of software maintainability is a significant challenge for the effective management of the software maintenance process. Objective The major aim of this paper is to present a systematic review of studies related to the prediction of maintainability of object-oriented software systems using machine learning techniques. This review identifies and investigates a number of research questions to comprehensively summarize, analyse and discuss various viewpoints concerning software maintainability measurements, metrics, datasets, evaluation measures, individual models and ensemble models. Method The review uses the standard systematic literature review method applied to the most common computer science digital database libraries from January 1991 to July 2018. Results We survey 56 relevant studies in 35 journals and 21 conference proceedings. The results indicate that there is relatively little activity in the area of software maintainability prediction compared with other software quality attributes. CHANGE maintenance effort and the maintainability index were the most commonly used software measurements (dependent variables) employed in the selected primary studies, and most made use of class-level product metrics as the independent variables. Several private datasets were used in the selected studies, and there is a growing demand to publish datasets publicly. Most studies focused on regression problems and performed k-fold cross-validation. Individual prediction models were employed in the majority of studies, while ensemble models relatively rarely. Conclusion Based on the findings obtained in this systematic literature review, ensemble models demonstrated increased accuracy prediction over individual models, and have been shown to be useful models in predicting software maintainability. However, their application is relatively rare and there is a need to apply these, and other models to an extensive variety of datasets with the aim of improving the accuracy and consistency of results.", "Keywords": "Systematic literature review ; Software maintainability prediction ; Machine learning ; Metric ; Dataset", "DOI": "10.1016/j.infsof.2019.106214", "PubYear": 2020, "Volume": "119", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Information system, Princess <PERSON><PERSON><PERSON>, Riyadh, Saudi Arabia;Computer and Information Sciences, University of Strathclyde, Glasgow, United Kingdom;Corresponding author at: Computer Science and Information system, Princess <PERSON><PERSON><PERSON>, Riyadh, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer and Information Sciences, University of Strathclyde, Glasgow, United Kingdom"}], "References": []}, {"ArticleId": 78274124, "Title": "Experimental study on deformation characteristics of collapsible loess under isotropic stress", "Abstract": "In order to study the deformation characteristics of collapsible loess and to construct different vertical stresses, different isotropic stresses are used to represent the vertical stresses, and their corresponding stresses are set to be The hidden stresses in different positions of collapsible loess foundation are analyzed by using 0 kPa, 50 kPa, 100 kPa, 220 kPa, 330 kPa and 400 kPa. In this study, various kinds of deformation analysis of collapsible loess are needed. Ground settlement tester, GJY K0 tester produced by Suzhou Longstan Automation Factory and ADINA software are used. Experiments show that under the action of different filling heights, different isotropic stresses are applied to the foundation, and the hidden stresses of the loess are gradually decreasing. At the same time, the higher the height of the fill, the faster the decline of the underlying stress of the foundation loess; with the increase of time at the bottom of the cushion, the corresponding underlying stress at the midline decreases, while with the increase of the height of the fill, the corresponding underlying stress at the midline rises; the study of the underlying stress at the bottom of the pile is set up differently. Under certain time and position of the pile bottom, increasing the isotropic stress can increase the hidden stress at the corresponding pile bottom, and change the filling height at the pile bottom has little effect on the hidden stress at the pile bottom. Under the combined action of water content and isotropic stress, they are positively correlated with the change of lateral stress. With the increase of isotropic stress, the K0 coefficient rises as a whole. This study can provide a strong reference for the deformation characteristics of collapsible loess itself, and help construction workers to improve the safety of the project in the field of engineering construction.", "Keywords": "", "DOI": "10.3233/JCM-193957", "PubYear": 2020, "Volume": "20", "Issue": "3", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Civil Engineering and Architecture Institute, Xi’an University of Technology, Xi’an, Shaanxi 710048, China;Gansu Forestry Technological College, Tianshui, Gansu 741020, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Civil Engineering and Architecture Institute, Xi’an University of Technology, Xi’an, Shaanxi 710048, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Civil Engineering and Architecture Institute, Xi’an University of Technology, Xi’an, Shaanxi 710048, China"}], "References": []}, {"ArticleId": 78274544, "Title": "Noise robust Laws’ filters based on fuzzy filters for texture classification", "Abstract": "Laws’ mask method has achieved wide acceptance in texture analysis, however it is not robust to noise. Fuzzy filters are well known for denoising applications. This work proposes a noise-robust Laws’ mask descriptor by integrating the exiting fuzzy filters with the traditional Laws’ mask for the improvement of texture classification of noisy texture images. Images are corrupted by adding Gaussian noise of different values. These noisy images are transformed into fuzzy images through fuzzy filters of different windows. Then the texture features are extracted using Laws’ mask descriptor. To investigate the proposed techniques two texture databases i.e. Brodatz and STex are used. The proposals are assessed by comparing the performance of the traditional Laws’ mask descriptor alone and after combined with the fuzzy filters on noisy images. The k-Nearest Neighbor (k-NN) classifier is utilized in the classification task. Results indicate that the proposed approach delivers higher classification accuracy than the traditional Laws’ mask method. Hence, validate that the suggested methods significantly improve the noised texture classification.", "Keywords": "Fuzzy filter ; Laws’ mask ; Texture classification ; Texture features", "DOI": "10.1016/j.eij.2019.10.003", "PubYear": 2020, "Volume": "21", "Issue": "1", "JournalId": 5980, "JournalTitle": "Egyptian Informatics Journal", "ISSN": "1110-8665", "EISSN": "2090-4754", "Authors": [{"AuthorId": 1, "Name": "Sonali Dash", "Affiliation": "Department of Electronics and Communication Engineering, Raghu Institute of Technology, Visakhapatnam 531162, Andhra Pradesh, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, <PERSON><PERSON> University of Technology, Burla 768018, Odisha, India"}], "References": []}, {"ArticleId": 78274966, "Title": "Intelligent traffic control for autonomous vehicle systems based on machine learning", "Abstract": "This study aimed to resolve a real-world traffic problem in a large-scale plant. Autonomous vehicle systems (AVSs), which are designed to use multiple vehicles to transfer materials, are widely used to transfer wafers in semiconductor manufacturing. Traffic control is a significant challenge with AVSs because all vehicles must be monitored and controlled in real time, to cope with uncertainties such as congestion. However, existing traffic control systems, which are primarily designed and controlled by human experts, are insufficient to prevent heavy congestion that impedes production. In this study, we developed a traffic control system based on machine learning predictions, and a routing method that dynamically determines AVS routes with reduced congestion rates. We predicted congestion for critical bottleneck areas, and utilized the predictions for adaptive routing control of all vehicles to avoid congestion. We conducted an experimental evaluation to compare the predictive performance of four popular algorithms. We performed a simulation study based on data from semiconductor fabrication to demonstrate the utility and superiority of the proposed method. The experimental results showed that AVSs with the proposed approach outperformed the existing approach in terms of delivery time, transfer time, and queuing time. We found that adopting machine learning-based traffic control can enhance the performance of existing AVSs and reduce the burden on the human experts who monitor and control AVSs.", "Keywords": "Intelligent traffic control ; Machine learning ; Autonomous vehicle systems ; Material handling ; Vehicle routing", "DOI": "10.1016/j.eswa.2019.113074", "PubYear": 2020, "Volume": "144", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Industrial Management Engineering, Korea University, 145 Anam-dong, Seongbuk-gu, Seoul 02841, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial Management Engineering, Korea University, 145 Anam-dong, Seongbuk-gu, Seoul 02841, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial Management Engineering, Korea University, 145 Anam-dong, Seongbuk-gu, Seoul 02841, South Korea"}, {"AuthorId": 4, "Name": "Soon-<PERSON><PERSON>", "Affiliation": "School of Industrial Management Engineering, Korea University, 145 Anam-dong, Seongbuk-gu, Seoul 02841, South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Industrial and Operations Engineering, University of Michigan, 1221 Beal Avenue, Ann Arbor, MI 48109, United States"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial Management Engineering, Korea University, 145 Anam-dong, Seongbuk-gu, Seoul 02841, South Korea"}, {"AuthorId": 7, "Name": "<PERSON>eyong Shin", "Affiliation": "Material Handling Automation Group, Samsung Electronics, SamsungJeonJa-ro 1, <PERSON>waseong-si, Gyeonggi-do, 18448, South Korea"}, {"AuthorId": 8, "Name": "Jeehyuk Park", "Affiliation": "Material Handling Automation Group, Samsung Electronics, SamsungJeonJa-ro 1, <PERSON>waseong-si, Gyeonggi-do, 18448, South Korea"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Industrial Management Engineering, Korea University, 145 Anam-dong, Seongbuk-gu, Seoul 02841, South Korea;Corresponding author."}], "References": []}, {"ArticleId": 78274982, "Title": "ProUM: Projection-based utility mining on sequence data", "Abstract": "Utility is an important concept in Economics. A variety of applications consider utility in real-life situations, which has lead to the emergence of utility-oriented mining (also called utility mining) in the recent decade. Utility mining has attracted a great amount of attention, but most of the existing studies have been developed to deal with itemset-based data. Time-ordered sequence data is more commonly seen in real-world situations, which is different from itemset-based data. Since they are time-consuming and require large amount of memory usage, current utility mining algorithms still have limitations when dealing with sequence data. In addition, the mining efficiency of utility mining on sequence data still needs to be improved, especially for long sequences or when there is a low minimum utility threshold. In this paper, we propose an efficient Pro jection-based U tility M ining (ProUM) approach to discover high-utility sequential patterns from sequence data. The utility-array structure is designed to store the necessary information of the sequence-order and utility. ProUM can significantly improve the mining efficiency by utilizing the projection technique in generating utility-array, and it effectively reduces the memory consumption. Furthermore, a new upper bound named sequence extension utility is proposed and several pruning strategies are further applied to improve the efficiency of ProUM. By taking utility theory into account, the derived high-utility sequential patterns have more insightful and interesting information than other kinds of patterns. Experimental results showed that the proposed ProUM algorithm significantly outperformed the state-of-the-art algorithms in terms of execution time, memory usage, and scalability.", "Keywords": "Economics ; Utility mining ; Sequence ; Projection ; Sequential pattern", "DOI": "10.1016/j.ins.2019.10.033", "PubYear": 2020, "Volume": "513", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "Wensheng Gan", "Affiliation": "School of Computer Sciences and Technology, Harbin Institute of Technology (Shenzhen), Shenzhen, Guangdong 518055, China;Department of Computer Sciences, University of Illinois at Chicago, Chicago, IL 60607, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Sciences and Technology, Harbin Institute of Technology (Shenzhen), Shenzhen, Guangdong 518055, China;Department of Computer Science, Electrical Engineering and Mathematical Sciences, Western Norway University of Applied Sciences, Bergen 5050, Norway;Corresponding author at: Department of Computing, Mathematics and Physics, Inndalsveien 28, 5063 Bergen, Norway"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Sciences and Technology, Harbin Institute of Technology (Shenzhen), Shenzhen, Guangdong 518055, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Dong Hwa University, Hualien 97401, Taiwan R.O.C"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Software and Information Science, Iwate Prefectural University, Morioka 020–8550, Japan"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Sciences, University of Illinois at Chicago, Chicago, IL 60607, USA"}], "References": []}, {"ArticleId": 78275397, "Title": "Specification-driven predictive business process monitoring", "Abstract": "Abstract \n Predictive analysis in business process monitoring aims at forecasting the future information of a running business process. The prediction is typically made based on the model extracted from historical process execution logs (event logs). In practice, different business domains might require different kinds of predictions. Hence, it is important to have a means for properly specifying the desired prediction tasks, and a mechanism to deal with these various prediction tasks. Although there have been many studies in this area, they mostly focus on a specific prediction task. This work introduces a language for specifying the desired prediction tasks, and this language allows us to express various kinds of prediction tasks. This work also presents a mechanism for automatically creating the corresponding prediction model based on the given specification. Differently from previous studies, instead of focusing on a particular prediction task, we present an approach to deal with various prediction tasks based on the given specification of the desired prediction tasks. We also provide an implementation of the approach which is used to conduct experiments using real-life event logs.", "Keywords": "Predictive business process monitoring; Prediction task specification language; Automatic prediction model creation; Machine learning-based prediction", "DOI": "10.1007/s10270-019-00761-w", "PubYear": 2020, "Volume": "19", "Issue": "6", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Innsbruck, Innsbruck, Austria;Faculty of Computer Science, Free University of Bozen-Bolzano, Bozen-Bolzano, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Innsbruck, Innsbruck, Austria;Department of Software Engineering, Blekinge Institute of Technology, Karlskrona, Sweden"}], "References": []}, {"ArticleId": 78275928, "Title": "IC3 software model checking", "Abstract": "<p>In recent years, the inductive, incremental verification algorithm IC3 had a major impact on hardware model checking. Also for software model checking, a number of adaptations of Boolean IC3 and combinations with CEGAR and ART-based techniques have been developed. However, most of them exploit the peculiarities of software programs, such as the explicit representation of control flow, only to a limited extent. In this paper, we present an approach that supports this explicit representation in the form of control-flow automata, and integrates it with symbolic reasoning about the data state space of the program. By maintaining reachability information specifically for each control location, we arrive at a “two-dimensional” extension of IC3, which provides a true lifting from hardware to software model checking. Moreover, we address the problem of generalization in this setting, an essential feature to ensure the scalability of IC3. We introduce several improvements that range from efficient caching of generalizations over variable reductions to syntax-oriented generalization by means of weakest preconditions. Using a prototypical implementation, we evaluate our approach on a number of case studies, including a significant subset of the SV-COMP 2018 benchmarks, and compare the outcomes with results obtained from other IC3 software model checkers. </p>", "Keywords": "Program verification; Safety properties; Software model checking; IC3", "DOI": "10.1007/s10009-019-00547-x", "PubYear": 2020, "Volume": "22", "Issue": "2", "JournalId": 15615, "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)", "ISSN": "1433-2779", "EISSN": "1433-2787", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Siemens AG, Munich, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}], "References": []}, {"ArticleId": 78275950, "Title": "Modelling the relationship between urban expansion processes and urban forest characteristics: An application to the Metropolitan District of Quito", "Abstract": "The rapid process of global urbanisation engenders changes in urban socio-ecological systems and in the landscape structure. However, the future processes of urban expansion in Latin American cities has been little studied even though the wellbeing of its citizens will depend on territorial management and on planning the provision of ecosystemic benefits and services. This research, considering different socio-ecological dimensions, proposed to determine the causes of potential urban expansion, analysing the dimensions and possible predictors that would explain the expansion of a high Andean city and its influence on peri-urban forest landscapes. To develop a model that integrates the complexity of the system, we used the following five dimensions: biophysics, land cover and management, infrastructure and services, socio-economics, and landscape metrics, and we opted for a binomial analysis through a spatial logistic regression model developed from 33 predictors. Considering the odd radio of the model, we observe that the independent increase in predictors, including building blocks, drinking water, sewerage, waste collection, average land size, the Interspersion and Juxtaposition Index (IJI) and Largest Patch Index (LPI), and the constant behaviour of the others predictors, would increase the probability of a potential urbanisation of the territory. Similarly, the independent increase in predictors, including the presence of protected areas, the presence of protected forests, land cover, unemployment, and the Shannon Diversity Index(SHDI), reduce the probability of the urbanisation process. Our results suggest that the territorial vulnerability from a potential urbanisation process is strongly related to an increase in infrastructure, services, and the average size of properties variables. Moreover, the landscape with the greatest potential for urbanisation presents an adequate intercalation of the different patches that compose it. However, the presence of variables such as protected areas and protective forests, in addition to monitoring indicators such as landscape diversity and mitigation strategies, could be considered to focus the analysis on the current dynamics of urbanisation processes in Latin America.", "Keywords": "Urban sprawl ; Urban-drivers ; Forest landscape ; Geo-statistical", "DOI": "10.1016/j.compenvurbsys.2019.101420", "PubYear": 2020, "Volume": "79", "Issue": "", "JournalId": 1688, "JournalTitle": "Computers, Environment and Urban Systems", "ISSN": "0198-9715", "EISSN": "1873-7587", "Authors": [{"AuthorId": 1, "Name": "Santiago Bonilla-Bedoya", "Affiliation": "Research Center for the Territory and Sustainable Habitat, Universidad Tecnológica Indoamérica, Machala y Sabanilla, 170301, Quito, Ecuador;Ingeniería en Biodiversidad y Recursos Genéticos. Facultad de Ciencias del Medio Ambiente. Universidad Tecnológica Indoamérica, Machala y Sabanilla, 170301, Quito, Ecuador;Facultad de Arquitectura, Artes y Diseño, Universidad Tecnológica Indoamérica, Machala y Sabanilla, 170301, Quito, Ecuador"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "EARTH University, Las Mercedes de Guácimo, Limón, Costa Rica"}, {"AuthorId": 3, "Name": "Angélica Vaca", "Affiliation": "Research Center for the Territory and Sustainable Habitat, Universidad Tecnológica Indoamérica, Machala y Sabanilla, 170301, Quito, Ecuador"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Departament of Agrarian Economy. E.T.S.I.A.M., Campus de Excelencia Internacional Agroalimentario (ceiA3), Universidad de Córdoba, Córdoba, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Departament of Forest Engineering. E.T.S.I.A.M., Campus de Excelencia Internacional Agroalimentario (ceiA3), Universidad de Córdoba, Córdoba, Spain"}], "References": []}, {"ArticleId": 78275992, "Title": "Image compression using explored bat algorithm by <PERSON>yi 2-d histogram based on multilevel thresholding", "Abstract": "<p>The main objective of the image compression is to extract meaningful clusters from a given image. A meaningful cluster is possible with perfect threshold values, which are optimized by assuming Renyi entropy as an objective function. Due to the equal distribution of energy over the entire 1-D histogram, it is computationally complex. In order to improve the visual quality of a reconstructed image, a 2-D histogram based multilevel thresholding is proposed to maximize the Renyi entropy using explored bat algorithm. Thus procured results are compared with other optimization techniques and these are incorporated. It is the first time, incorporating a weighted peak signal to noise ratio (WPSNR) and the visual PSNR (VPSNR) in the proposed method, because of the failure in measuring the visual quality of peak signal to noise ratio (PSNR). Experimental results are examined on a standard set of images, which are observed precisely and efficiently in the multilevel thresholding problem.</p>", "Keywords": "Explored bat algorithm; Image compression; 2-D histogram; Thresholding; Objective function; Renyi entropy", "DOI": "10.1007/s12065-019-00313-7", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 5202, "JournalTitle": "Evolutionary Intelligence", "ISSN": "1864-5909", "EISSN": "1864-5917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Jawaharlal Nehru Technological University, Hyderabad, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Jawaharlal Nehru Technological University, Hyderabad, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Jawaharlal Nehru Technological University, Hyderabad, India"}], "References": []}, {"ArticleId": 78276250, "Title": "Industry 4.0: <PERSON><PERSON>?", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.engappai.2019.103324", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Deutsches Forschungszentrum für Künstliche Intelligenz, Germany|Universidad de Murcia, Spain|Machine Intelligence Research Labs, USA|IBM Watson IoT Industry Lab, Germany|AIT Austrian Institute of Technology, Austria|Czech Technical University, Czech Republic|VSB-Technical University of Ostrava, Czech Republic|Huawei Technologies Canada Co. Ltd., Canada|University of Cordoba, Spain;Corresponding editor."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Huawei Technologies Canada Co. Ltd., Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IBM Watson IoT Industry Lab, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Cordoba, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Czech Technical University, Czech Republic"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Universidad de Murcia, Spain"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "VSB-Technical University of Ostrava, Czech Republic"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "AIT Austrian Institute of Technology, Austria"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Deutsches Forschungszentrum für Künstliche Intelligenz, Germany"}], "References": []}, {"ArticleId": 78276251, "Title": "Multi-view Locality Low-rank Embedding for Dimension Reduction", "Abstract": "During the last decades, we have witnessed a surge of interest in learning a low-dimensional space with discriminative information from one single view. Even though most of them can achieve satisfactory performance in certain situations, they fail to fully consider the information from multiple views which are highly relevant but sometimes look different from each other. Besides, correlations between features from multiple views always vary greatly, which challenges the capability of multi-view subspace learning methods. Therefore, how to learn an appropriate subspace which could maintain valuable information from multi-view features is of vital importance but challenging. To tackle this problem, this paper proposes a novel multi-view dimension reduction method named Multi-view Locality Low-rank Embedding for Dimension Reduction (MvL 2 E). MvL 2 E mainly focuses on capturing a common low-dimensional embedding among multiple different views, which makes full use of correlations between multi-view features by adopting low-rank representations. Meanwhile, it aims to maintain the correlations and construct a suitable manifold structure to capture the low-dimensional embedding for multi-view features. A centroid based scheme is designed to get one common low-dimensional manifold space and force multiple views to learn from each other. And an iterative alternating strategy is developed to obtain the optimal solution of MvL 2 E. The proposed method is evaluated on 5 benchmark datasets. Comprehensive experiments show that our proposed MvL 2 E can achieve comparable performance with previous approaches proposed in recent works of literature.", "Keywords": "Multi-view learning ; Low rank ; Dimension reduction", "DOI": "10.1016/j.knosys.2019.105172", "PubYear": 2020, "Volume": "191", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Dalian University of Technology, Dalian, 116024, PR China;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Dalian University of Technology, Dalian, 116024, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Information Science and Technology College, Dalian Maritime University, Dalian 116024, PR China;Corresponding authors"}], "References": []}, {"ArticleId": 78276265, "Title": "Semi-supervised representation learning via dual autoencoders for domain adaptation", "Abstract": "Domain adaptation aims to exploit the knowledge in source domain to promote the learning tasks in target domain, which plays a critical role in real-world applications. Recently, lots of deep learning approaches based on autoencoders have achieved a significance performance in domain adaptation. However, most existing methods focus on minimizing the distribution divergence by putting the source and target data together to learn global feature representations, while they do not consider the local relationship between instances in the same category from different domains. To address this problem, we propose a novel Semi-Supervised Representation Learning framework via Dual Autoencoders for domain adaptation, named SSRLDA. More specifically, we extract richer feature representations by learning the global and local feature representations simultaneously using two novel autoencoders, which are referred to as marginalized denoising autoencoder with adaptation distribution (MDA a d ) and multi-class marginalized denoising autoencoder (MMDA) respectively. Meanwhile, we make full use of label information to optimize feature representations. Experimental results show that our proposed approach outperforms several state-of-the-art baseline methods.", "Keywords": "Domain adaptation ; Dual autoencoders ; Representation learning ; Semi-supervised", "DOI": "10.1016/j.knosys.2019.105161", "PubYear": 2020, "Volume": "190", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Engineering with Big Data (Hefei University of Technology), Ministry of Education, China;School of Computer Science and Information Engineering, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Engineering with Big Data (Hefei University of Technology), Ministry of Education, China;School of Computer Science and Information Engineering, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Engineering with Big Data (Hefei University of Technology), Ministry of Education, China;School of Computer Science and Information Engineering, Hefei University of Technology, Hefei 230009, China;Corresponding author"}, {"AuthorId": 4, "Name": "Peipei Li", "Affiliation": "Key Laboratory of Knowledge Engineering with Big Data (Hefei University of Technology), Ministry of Education, China;School of Computer Science and Information Engineering, Hefei University of Technology, Hefei 230009, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Yangzhou University, Jiangsu 225009, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Engineering with Big Data (Hefei University of Technology), Ministry of Education, China;School of Computer Science and Information Engineering, Hefei University of Technology, Hefei 230009, China;Anhui Province Key Laboratory of Industry Safety and Emergency Technology, Hefei 230009, China"}], "References": []}, {"ArticleId": 78276303, "Title": "Generative adversarial networks based on <PERSON><PERSON><PERSON> distance for knowledge graph embeddings", "Abstract": "Knowledge graph embedding aims to project entities and relations into low-dimensional and continuous semantic feature spaces, which has captured more attention in recent years. Most of the existing models roughly construct negative samples via a uniformly random mode, by which these corrupted samples are practically trivial for training the embedding model. Inspired by generative adversarial networks (GANs), the generator can be employed to sample more plausible negative triplets, that boosts the discriminator to improve its embedding performance further. However, vanishing gradient on discrete data is an inherent problem in traditional GANs. In this paper, we propose a generative adversarial network based knowledge graph representation learning model by introducing the Wasserstein distance to replace traditional divergence for settling this issue. Moreover, the additional weak supervision information is also absorbed to refine the performance of embedding model since these textual information contains detailed semantic description and offers abundant semantic relevance. In the experiments, we evaluate our method on the tasks of link prediction and triplet classification. The experimental results indicate that the Wasserstein distance is capable of solving the problem of vanishing gradient on discrete data and accelerating the convergence, additional weak supervision information also can significantly improve the performance of the model.", "Keywords": "Knowledge graph embedding ; Generative adversarial networks ; <PERSON><PERSON><PERSON> distance ; Weak supervision information", "DOI": "10.1016/j.knosys.2019.105165", "PubYear": 2020, "Volume": "190", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Computer Sciences, Fuzhou University, Fuzhou 350116, China"}, {"AuthorId": 2, "Name": "Shiping <PERSON>", "Affiliation": "College of Mathematics and Computer Sciences, Fuzhou University, Fuzhou 350116, China;Key Laboratory of Network Computing and Intelligent Information Processing, Fuzhou University, Fuzhou 350116, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Computer Sciences, Fuzhou University, Fuzhou 350116, China;Key Laboratory of Network Computing and Intelligent Information Processing, Fuzhou University, Fuzhou 350116, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Xu", "Affiliation": "School of Information Engineering, Putian University, Putian 351100, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Computer Sciences, Fuzhou University, Fuzhou 350116, China;Key Laboratory of Network Computing and Intelligent Information Processing, Fuzhou University, Fuzhou 350116, China;Corresponding author at: College of Mathematics and Computer Sciences, Fuzhou University, Fuzhou 350116, China"}], "References": []}, {"ArticleId": 78276305, "Title": "Trust based group decision making in environments with extreme uncertainty", "Abstract": "In group decision making scenarios, where multiple anonymous agents interact, as is the case of social networks, the uncertainty in the provided information as well as the diversity in the experts’ opinions make of them a real challenge from the point of view of information aggregation and consensus achievement. This contribution addresses these two main issues in the following way: On the one hand, in order to deal with highly uncertainty group decision making scenarios, whose main particularity is that some of their experts may not be able to provide any single judgment about an alternative, the proposed approach estimates these missing information using the preferences coming from other trusted similar experts who present high degrees of confidence and consistency. On the other hand, with the objective of increasing the consensus among the agents involved in the decision making process, a feedback based influence network has been proposed. In this network, the influence between the agents is calculated by means of a dynamic combination of the inter agents trust, their self confidence, and their similarity. Thanks to this influence network our approach is able to recognize and isolate malicious users adjusting their influence according to the trust degree between them.", "Keywords": "Group decision making ; Uncertainty ; Incomplete information ; Ignorance situations ; Intuitionistic fuzzy preference relations. ; Consensus ; Trust", "DOI": "10.1016/j.knosys.2019.105168", "PubYear": 2020, "Volume": "191", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Graduate University of Advanced Technology, Kerman, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mathematics and computer, <PERSON><PERSON> University of Kerman, Kerman, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Andalusian Research Institute on Data Science and Computational Intelligence (DaSCI), University of Granada, Granada, Spain;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence (IAI),School of Computer Science and Informatics, De Montfort University, Leicester, UK;Corresponding authors"}], "References": []}, {"ArticleId": 78276317, "Title": "Non-convex hull based anomaly detection in CPPS", "Abstract": "Along with the constantly increasing complexity of industrial automation systems, machine learning methods have been widely applied to detecting abnormal states in such systems. Anomaly detection tasks can be treated as one-class classification problems in machine learning. Geometric methods can give an intuitive solution to such problems. In this paper, we propose a new geometric structure, oriented non-convex hulls, to represent decision boundaries used for one-class classification. Based on this geometric structure, a novel boundary based one-class classification algorithm is developed to solve the anomaly detection problem. Compared with traditional boundary-based approaches such as convex hulls based methods and one-class support vector machines, the proposed approach can better reflect the true geometry of target data and needs little effort for parameter tuning. The effectiveness of this approach is evaluated with artificial and real world data sets to solve the anomaly detection problem in Cyber–Physical-Production-Systems (CPPS). The evaluation results also show that the proposed approach has higher generality than the used baseline algorithms.", "Keywords": "One-class classification ; n -dimensional oriented non-convex hull ; Anomaly detection ; CPPS", "DOI": "10.1016/j.engappai.2019.103301", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "inIT - Institute industrial IT, OWL University of Applied Sciences and Arts, Germany;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Automation Technology, Helmut Schmidt University, Germany"}], "References": []}, {"ArticleId": 78276324, "Title": "On extracting data from tables that are encoded using HTML", "Abstract": "Tables are a common means to display data in human-friendly formats. Many authors have worked on proposals to extract those data back since this has many interesting applications. In this article, we summarise and compare many of the proposals to extract data from tables that are encoded using HTML and have been published between 2000 and 2018. We first present a vocabulary that homogenises the terminology used in this field; next, we use it to summarise the proposals; finally, we compare them side by side. Our analysis highlights several challenges to which no proposal provides a conclusive solution and a few more that have not been addressed sufficiently; simply put, no proposal provides a complete solution to the problem, which seems to suggest that this research field shall keep active in the near future. We have also realised that there is no consensus regarding the datasets and the methods used to evaluate the proposals, which hampers comparing the experimental results.", "Keywords": "HTML documents ; Web tables ; Table mining ; Data extraction", "DOI": "10.1016/j.knosys.2019.105157", "PubYear": 2020, "Volume": "190", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Seville, ETSI Informática Avda. Reina Mercedes s/n, Sevilla E-41012, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Seville, ETSI Informática Avda. Reina Mercedes s/n, Sevilla E-41012, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Seville, ETSI Informática Avda. Reina Mercedes s/n, Sevilla E-41012, Spain"}], "References": []}, {"ArticleId": 78276528, "Title": "Multi-label learning for concept-oriented labels of product image data", "Abstract": "In the designing field, designers usually retrieve the images for reference according to product attributes when designing new proposals. To obtain the attributes of the product, the designers take lots of time and effort to collect product images and annotate them with multiple labels. However, the labels of product images represent the concept of subjective perception, which makes the multi-label learning more challenging to imitate the human aesthetic rather than discriminate the appearance. In this paper, a Feature Correlation Learning (FCL) network is proposed to solve this problem by exploiting the potential feature correlations of product images. Given a product image, the FCL network calculates the features of different levels and their correlations via gram matrices. The FCL is aggregated with the DenseNet to predict the labels of the input product image. The proposed method is compared with several outstanding multi-label learning methods, as well as DenseNet. Experimental results demonstrate that the proposed method outperforms the state-of-the-arts for multi-label learning problem of product image data.", "Keywords": "Multi-label learning ; Concept-oriented labels ; Product image data ; Feature correlation learning ; Gram matrices", "DOI": "10.1016/j.imavis.2019.10.007", "PubYear": 2020, "Volume": "93", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, Hunan University, Changsha, Hunan, China;Key Laboratory of Visual Perception and Artificial Intelligence of Hunan Province, Changsha, Hunan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Design, Hunan University, Changsha, Hunan, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Hunan University, Changsha, Hunan, China;Key Laboratory of Visual Perception and Artificial Intelligence of Hunan Province, Changsha, Hunan, China"}], "References": []}, {"ArticleId": 78276546, "Title": "Fine-Grained Image Retrieval via Piecewise Cross Entropy loss", "Abstract": "Fine-Grained Image Retrieval is an important problem in computer vision. It is more challenging than the task of content-based image retrieval because it has small diversity within the different classes but large diversity in the same class. Recently, the cross entropy loss can be utilized to make Convolutional Neural Network (CNN) generate distinguish feature for Fine-Grained Image Retrieval, and it can obtain further improvement with some extra operations, such as Normalize-Scale layer. In this paper, we propose a variant of the cross entropy loss, named Piecewise Cross Entropy loss function, for enhancing model generalization and promoting the retrieval performance. Besides, the Piecewise Cross Entropy loss is easy to implement. We evaluate the performance of the proposed scheme on two standard fine-grained retrieval benchmarks, and obtain significant improvements over the state-of-the-art, with 11.8% and 3.3% over the previous work on CARS196 and CUB-200-2011, respectively.", "Keywords": "Fine-Grained Image Retrieval ; CNN ; Piecewise cross entropy loss ; 00-01 ; 99-00", "DOI": "10.1016/j.imavis.2019.10.006", "PubYear": 2020, "Volume": "93", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Automation, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Automation, Guangdong University of Technology, Guangzhou 510006, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Automation, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Guangzhou University, Guangzhou 510006, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Automation, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electromechanical Engineering, Guangzhou City Polytechnic, Guangzhou 510405, China"}], "References": []}, {"ArticleId": 78276548, "Title": "Multiple stream deep learning model for human action recognition", "Abstract": "Human action recognition is one of the most important and challenging topic in the fields of image processing. Unlike object recognition, action recognition requires motion feature modeling which contains not only spatial but also temporal information. In this paper, we use multiple models to characterize both global and local motion features. Global motion patterns are represented efficiently by the depth-based 3-channel motion history images (MHIs). Meanwhile, the local spatial and temporal patterns are extracted from the skeleton graph. The decisions of these two streams are fused. At the end, the domain knowledge, which is the object/action dependency is considered. The proposed framework is evaluated on two RGB-D datasets. The experimental results show the effectiveness of our proposed approach. The performance is comparable with the state-of-the-art.", "Keywords": "Deep learning ; Information fusion ; Action recognition", "DOI": "10.1016/j.imavis.2019.10.004", "PubYear": 2020, "Volume": "93", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Shenzhen Technology University, No. 3002, Lantian Street, Pingshan District, Shenzhen 518118, China;Corresponding author"}, {"AuthorId": 2, "Name": "Xiaofeng Ye", "Affiliation": "Shenzhen Academy of Robotics, No. 6, Yuexing er Street, Shenzhen 518052, China"}, {"AuthorId": 3, "Name": "Weihua Sheng", "Affiliation": "Oklahoma State University, Whitehurst Hall 301, Stillwater, OK 74078, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Institutes of Advanced Technology, Chinese Academy of Sciences, No. 1068, Xueyuan Street, Shenzhen 518055, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, No.92, Xidazhi Street, Harbin 150001, China"}], "References": []}, {"ArticleId": 78276582, "Title": "Region-based Fitting of Overlapping Ellipses and its application to cells segmentation", "Abstract": "We present RFOVE, a region-based method for approximating an arbitrary 2D shape with an automatically determined number of possibly overlapping ellipses. RFOVE is completely unsupervised, operates without any assumption or prior knowledge on the object&#x27;s shape and extends and improves the Decremental Ellipse Fitting Algorithm (DEFA) [ 1 ]. Both RFOVE and DEFA solve the multi-ellipse fitting problem by performing model selection that is guided by the minimization of the Akaike Information Criterion on a suitably defined shape complexity measure. However, in contrast to DEFA, RFOVE minimizes an objective function that allows for ellipses with higher degree of overlap and, thus, achieves better ellipse-based shape approximation. A comparative evaluation of RFOVE with DEFA on several standard datasets shows that RFOVE achieves better shape coverage with simpler models (less ellipses). As a practical exploitation of RFOVE, we present its application to the problem of detecting and segmenting potentially overlapping cells in fluorescence microscopy images. Quantitative results obtained in three public datasets (one synthetic and two with more than 4000 actual stained cells) show the superiority of RFOVE over the state of the art in overlapping cells segmentation.", "Keywords": "Cell segmentation ; 2D shape modeling ; Overlapping objects ; Ellipse fitting ; AIC", "DOI": "10.1016/j.imavis.2019.09.001", "PubYear": 2020, "Volume": "93", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Management Science and Technology Department, Hellenic Mediterranean University, Agios Nikolaos 72100, Crete, Greece;Institute of Computer Science, FORTH, Heraklion 70013, Crete, Greece;Corresponding author at: Management Science and Technology Department, Hellenic Mediterranean University, Agios Nikolaos 72100, Crete, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, University of Crete, Heraklion 70013, Crete, Greece;Institute of Computer Science, FORTH, Heraklion 70013, Crete, Greece"}], "References": []}, {"ArticleId": 78276590, "Title": "ResFeats: Residual network based features for underwater image classification", "Abstract": "Oceanographers rely on advanced digital imaging systems to assess the health of marine ecosystems. The majority of the imagery collected by these systems do not get annotated due to lack of resources. Consequently, the expert labeled data is not enough to train dedicated deep networks. Meanwhile, in the deep learning community, much focus is on how to use pre-trained deep networks to classify out-of-domain images and transfer learning. In this paper, we leverage these advances to evaluate how well features extracted from deep neural networks transfer to underwater image classification. We propose new image features (called ResFeats) extracted from the different convolutional layers of a deep residual network pre-trained on ImageNet. We further combine the ResFeats extracted from different layers to obtain compact and powerful deep features. Moreover, we show that ResFeats consistently perform better than their CNN counterparts. Experimental results are provided to show the effectiveness of ResFeats with state-of-the-art classification accuracies on MLC, Benthoz15, EILAT and RSMAS datasets.", "Keywords": "Deep learning ; Residual networks ; Deep features ; Image classification ; Underwater image classification", "DOI": "10.1016/j.imavis.2019.09.002", "PubYear": 2020, "Volume": "93", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Western Australia, WA 6009, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The University of Western Australia, WA 6009, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Western Australia, WA 6009, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Murdoch University, WA 6150, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Western Australia, WA 6009, Australia"}], "References": []}, {"ArticleId": 78276667, "Title": "A framework for topological based map building: A solution to autonomous robot navigation in smart cities", "Abstract": "Advancement in the Information and Communications Technology (ICT) has transformed urban environments into modern smart cities by connecting physical devices or sensors to communicate via the Internet of things (IoT) networks. These sensors collect a massive amount of data, which is eventually used in the efficient management of shared assets and resources. In this article, we provide a framework for topological based map building for autonomous robot navigation in smart cities supporting IoT-based technologies. The classical methods of topological based map building do not incorporate the real essence of topology but instead, an illustration is provided where the topology is considered to be a cluster of features. We analyzed the problem of map building from a pure robotic perspective by considering the shape of free-space into a robot navigation path. In the proposed framework, shape-based invariant features are extracted from the free-space, which are used in the construction of topological nodes for robot autonomous navigation. In the context of topological navigation, topology is considered as a structural representation of connecting nodes representing free-space shapes and edges that define their connectivity. To evaluate the performance of the proposed approach, real-time experiments are performed, which show improvements in autonomous navigation in terms of efficiency and maneuverability.", "Keywords": "Internet of things ; Smart cities ; Topological maps ; Autonomous robot navigation ; Topo-metric mapping ; Fourier descriptors ; Image moments ; Shape features", "DOI": "10.1016/j.future.2019.10.036", "PubYear": 2020, "Volume": "111", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "Naveed <PERSON>", "Affiliation": "Department of Computer Science, Islamia College University Peshawar, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Islamia College University Peshawar, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer and Information Sciences, King Saud University, Riyadh, Saudi Arabia;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, The University of Haripur, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Qatar University, Doha, Qatar"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Natural and Engineering Sciences, College of Applied Studies and Community Services, King Saud University, Riyadh, Saudi Arabia"}], "References": []}, {"ArticleId": 78276675, "Title": "Multi-domain sentiment analysis with mimicked and polarized word embeddings for human–robot interaction", "Abstract": "This paper presents a state-of-the-art approach for sentiment polarity classification. Our approach relies on an ensemble of Bidirectional Long Short-Term Memory networks equipped with a neural attention mechanism. The system makes use of pre-trained word embeddings, and is capable of predicting new vectors for out-of-vocabulary words, by learning distributional representations based on word spellings. Also, during the training process the recurrent neural network is used to perform a fine-tuning of the original word embeddings, taking into account information about sentiment polarity. This step can be particularly helpful for sentiment analysis, as word embeddings are usually built based on context information, while words with opposite sentiment polarity often occur in similar contexts. The system described in this paper is an improved version of an approach that competed in a recent challenge on semantic sentiment analysis. We evaluate the performance of the system on the same multi-domain test set used by the organizers of the challenge, showing that our approach allows reaching better results with respect to the previous top-scoring system. Last but not least, we embedded the proposed sentiment polarity approach on top of a humanoid robot to lively identify the sentiment of the speaking user.", "Keywords": "Word embeddings ; LSTM ; Deep learning ; Neural attention ; Sentiment analysis ; RNN ; Natural language processing", "DOI": "10.1016/j.future.2019.10.012", "PubYear": 2020, "Volume": "110", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Università degli Studi di Cagliari, Department of Mathematics and Computer Science, Via Ospedale 72, 09124, Cagliari, Italy"}, {"AuthorId": 2, "Name": "Diego Reforg<PERSON>", "Affiliation": "Università degli Studi di Cagliari, Department of Mathematics and Computer Science, Via Ospedale 72, 09124, Cagliari, Italy;Corresponding author"}], "References": []}, {"ArticleId": 78276700, "Title": "Bus travel time prediction based on deep belief network with back-propagation", "Abstract": "<p>In an intelligent transportation system, accurate bus information is vital for passengers to schedule their departure time and make reasonable route choice. In this paper, an improved deep belief network (DBN) is proposed to predict the bus travel time. By using G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> restricted Boltzmann machines to construct a DBN, we update the classical DBN to model continuous data. In addition, a back-propagation (BP) neural network is further applied to improve the performance. Based on the real traffic data collected in Shenyang, China, several experiments are conducted to validate the technique. Comparison with typical forecasting methods such as k -nearest neighbor algorithm ( k -NN), artificial neural network (ANN), support vector machine (SVM) and random forests (RFs) shows that the proposed method is applicable to the prediction of bus travel time and works better than traditional methods.</p>", "Keywords": "Bus travel time prediction; Multi-factor influence; Deep belief network; Machine learning models", "DOI": "10.1007/s00521-019-04579-x", "PubYear": 2020, "Volume": "32", "Issue": "14", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Structural Analysis for Industrial Equipment, School of Automotive Engineering, Dalian University of Technology, Dalian, People’s Republic of China;Urban Planning Group, Department of the Built Environment, Eindhoven University of Technology, Eindhoven, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Structural Analysis for Industrial Equipment, School of Automotive Engineering, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Structural Analysis for Industrial Equipment, School of Automotive Engineering, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 4, "Name": "Huizhong <PERSON>", "Affiliation": "State Key Laboratory of Structural Analysis for Industrial Equipment, School of Automotive Engineering, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Structural Analysis for Industrial Equipment, School of Automotive Engineering, Dalian University of Technology, Dalian, People’s Republic of China"}], "References": []}, {"ArticleId": 78276701, "Title": "Formalising and animating multiple instances in BPMN collaborations", "Abstract": "The increasing adoption of modelling methods contributes to a better understanding of the flow of processes, from the internal behaviour of a single organisation to a wider perspective where several organisations exchange messages. In this regard, BPMN collaborations provide a suitable modelling abstraction. Even if this is a widely accepted notation, only a limited effort has been expended in formalising its semantics, especially for what it concerns the interplay among control features, data handling and exchange of messages in scenarios requiring multiple instances of interacting participants. In this paper, we face the problem of providing a formal semantics for BPMN collaborations including elements dealing with multiple instances, i.e., multi-instance pools and sequential/parallel multi-instance tasks. For an accurate account of these features, it is necessary to consider the data perspective of collaboration models, thus supporting data objects, data collections and data stores, and different execution modalities of tasks concerning atomicity and concurrency. Beyond defining a novel formalisation, we also provide a BPMN collaboration animator tool, named MIDA , faithfully implementing the formal semantics. MIDA  can also support designers in debugging multi-instance collaboration models.", "Keywords": "BPMN 2.0 ; Multiple instances ; Data ; Formal semantics ; Animation", "DOI": "10.1016/j.is.2019.101459", "PubYear": 2022, "Volume": "103", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science and Technology, University of Camerino, Camerino, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science and Technology, University of Camerino, Camerino, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Science and Technology, University of Camerino, Camerino, Italy;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Science and Technology, University of Camerino, Camerino, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Science and Technology, University of Camerino, Camerino, Italy"}], "References": []}, {"ArticleId": 78276706, "Title": "EntropyDB: a probabilistic approach to approximate query processing", "Abstract": "<p>We present, an interactive data exploration system that uses a probabilistic approach to generate a small, query-able summary of a dataset. Departing from traditional summarization techniques, we use the Principle of Maximum Entropy to generate a probabilistic representation of the data that can be used to give approximate query answers. We develop the theoretical framework and formulation of our probabilistic representation and show how to use it to answer queries. We then present solving techniques, give two critical optimizations to improve preprocessing time and query execution time, and explore methods to reduce query error. Lastly, we experimentally evaluate our work using a 5 GB dataset of flights within the USA and a 210 GB dataset from an astronomy particle simulation. While our current work only supports linear queries, we show that our technique can successfully answer queries faster than sampling while introducing, on average, no more error than sampling and can better distinguish between rare and nonexistent values. We also discuss extensions that can allow for data updates and linear queries over joins.</p>", "Keywords": "Database summarization; Approximate query processing; Principle of maximum entropy; Data exploration; Probabilistic databases; Graphical models", "DOI": "10.1007/s00778-019-00582-9", "PubYear": 2020, "Volume": "29", "Issue": "1", "JournalId": 4083, "JournalTitle": "The VLDB Journal", "ISSN": "1066-8888", "EISSN": "0949-877X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, USA"}], "References": []}, {"ArticleId": 78276778, "Title": "(Strong) conflict-free connectivity: Algorithm and complexity", "Abstract": "Let G be an(a) edge(vertex)-colored graph. A path P of G is called a conflict-free path if there is a color that is used on exactly one of the edges(vertices) of P . The graph G is called conflict-free (vertex-)connected if any two distinct vertices of G are connected by a conflict-free path, whereas the graph G is called strongly conflict-free connected if any two distinct vertices u , v of G are connected by a conflict-free path of length of a shortest path between u and v in G . For a connected graph G , the (strong) conflict-free connection number of G , denoted by ( s c f c ( G ) ) c f c ( G ) , is defined as the smallest number of colors that are required to make G (strongly) conflict-free connected. In this paper, we first investigate the question: Given a connected graph G and a coloring c : E ( o r V ) → { 1 , 2 , ⋯ , k } ( k ≥ 1 ) of G , determine whether or not G is, respectively, conflict-free connected, conflict-free vertex-connected, strongly conflict-free connected under the coloring c . We solve this question by providing polynomial-time algorithms. We then show that the problem of deciding whether s c f c ( G ) ≤ k ( k ≥ 2 ) for a given graph G is NP-complete. Moreover, we prove that it is NP-complete to decide whether there is a k -edge-coloring ( k ≥ 2 ) of G such that all pairs ( u , v ) ∈ P ( P ⊂ V × V ) are strongly conflict-free connected.", "Keywords": "Conflict-free connection ; Polynomial-time algorithm ; Strong conflict-free connection ; Complexity", "DOI": "10.1016/j.tcs.2019.10.043", "PubYear": 2020, "Volume": "804", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Combinatorics and LPMC, Nankai University, Tianjin 300071, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Combinatorics and LPMC, Nankai University, Tianjin 300071, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Combinatorics and LPMC, Nankai University, Tianjin 300071, China"}], "References": []}, {"ArticleId": 78276806, "Title": "On the Convergence of the Matrix Lambert W Approach to Solution of Systems of Delay Differential Equations", "Abstract": "Abstract \n Convergence of the matrix Lambert W function method for solving systems of delay differential equations (DDEs) is considered. Recent research shows that convergence problems occur with certain DDEs when using the well-established Q-iteration approach. A complementary, and recently proposed, W-iteration approach is shown to converge even on systems where Q-iteration fails. Furthermore, the role played by the branch numbers k = −∞ … −1, 0, 1, … ∞ of the matrix Lambert W function, Wk, in terms of initializing the iterative solutions, is also discussed and elucidated. Several second-order examples, known to have convergence problems with Q-iteration, are readily solved by W-iteration. Examples of third- and fourth-order DDEs show that W-iteration is also effective on higher-order systems.", "Keywords": "Delay differential equations", "DOI": "10.1115/1.4045368", "PubYear": 2020, "Volume": "142", "Issue": "2", "JournalId": 9493, "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control", "ISSN": "0022-0434", "EISSN": "1528-9028", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Michigan, Ann Arbor, Michigan USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, University of Michigan, Ann Arbor, Michigan USA"}], "References": []}, {"ArticleId": 78276846, "Title": "Morphology-controlled electrochemical sensing of erbium- benzenetricarboxylic acid frameworks for azo dyes and flavonoids", "Abstract": "Generally, materials with different morphologies are expected to have different electrochemical properties. Here, three kinds of erbium-based metal-organic frameworks with different morphologies were prepared using erbium nitrate and 1,3,5-benzenetricarboxylic acid (H<sub>3</sub>BTC) as the source in N, N - dimethylformamide (DMF) by adding various amounts of ammonium acetate (CH<sub>3</sub>COONH<sub>4</sub>). The characterization of X-ray diffraction, infrared spectroscopy and X-ray photoelectron spectroscopy proved that the obtained materials were Er-BTC. Scanning electron microscope measurements indicated that the amount of CH<sub>3</sub>COONH<sub>4</sub> had obvious effects on the morphology. More importantly, the active sensing area and interface electron transfer ability of Er-BTC were controlled by the morphology, which revealed by rotating ring disk electrode and electrochemical impedance spectroscopy. Moreover, the prepared Er-BTC with different morphologies exhibited different signal enhancement ability toward the oxidation of azo dyes (allura red and rhodamine B) and flavonoids (quercetin and luteolin). Based on the morphology-controlled sensing of Er-BTC, a highly sensitive detection platform were developed for allura red/rhodamine B and quercetin/luteolin, with detection limits of 0.30/0.56 nM and 0.22/0.14 nM. Besides, this new method was successfully used in drink and tea samples, and the results were in good agreement with those obtained from high-performance liquid chromatography.", "Keywords": "Metal-organic frameworks ; Er-BTC ; Morphology ; Electrochemical ; detection ; Azo dyes ; Flavonoids", "DOI": "10.1016/j.snb.2019.127370", "PubYear": 2020, "Volume": "304", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Bioinorganic Chemistry and Materia Medica, School of Chemistry and Chemical Engineering, Huazhong University of Science and Technology, Wuhan,430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Biological Resources Protection and Utilization, School of Chemistry and Environmental Engineering, Hubei University for Nationalities, Enshi, 445000, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Bioinorganic Chemistry and Materia Medica, School of Chemistry and Chemical Engineering, Huazhong University of Science and Technology, Wuhan,430074, China;Corresponding authors"}], "References": []}, {"ArticleId": 78276863, "Title": "Algorithmic aspects of upper paired-domination in graphs", "Abstract": "A set D of vertices in a graph G is a paired-dominating set of G if every vertex of G is adjacent to a vertex in D and the subgraph induced by D contains a perfect matching (not necessarily as an induced subgraph). A paired-dominating set of G is minimal if no proper subset of it is a paired-dominating set of G . The upper paired-domination number of G , denoted by Γ pr ( G ) , is the maximum cardinality of a minimal paired-dominating set of G . In Upper-PDS , it is required to compute a minimal paired-dominating set with cardinality Γ pr ( G ) for a given graph G . In this paper, we show that Upper-PDS cannot be approximated within a factor of n 1 − ε for any ε &gt; 0 , unless P = NP and Upper-PDS is APX -complete for bipartite graphs of maximum degree 4. On the positive side, we show that Upper-PDS can be approximated within O ( Δ ) -factor for graphs with maximum degree Δ. We also show that Upper-PDS is solvable in polynomial time for threshold graphs, chain graphs, and proper interval graphs.", "Keywords": "Domination ; Paired-domination ; Upper paired-domination ; Polynomial time algorithm ; NP-complete ; APX-complete", "DOI": "10.1016/j.tcs.2019.10.045", "PubYear": 2020, "Volume": "804", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Pure and Applied Mathematics, University of Johannesburg, Auckland Park, 2006, South Africa"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computing, Indian Institute of Technology (ISM), Dhanbad, India;Corresponding author"}], "References": []}, {"ArticleId": 78276873, "Title": "Achieving M2M-device authentication through heterogeneous information bound with USIM card", "Abstract": "In the commercial launches of Long-term evolution machine type communication (LTE-M), which includes enhanced machine type communication or machine to machine (M2M) communication in the Long-term evolution (LTE), the security issues is worth the attention. Several security problems have been discovered in LTE-M. One such important problem is the non-authenticated usage of the subscriber identity module (SIM) cards. Because M2M communications are not human-centric, the common personal identification number (PIN) code verification mechanism is not suitable for the machine driven communications. In this paper, we propose two approaches to solve the non-authenticated SIM card usage problem by restricting the code in the SIM card to the specific user device. One approach, named IMEI–IMSI pairing, is an enhancement of the original authentication mechanism which performs device authentication by pairing the International Mobile Equipment Identification Number (IMEI), the device’s unique code, and the International Mobile Subscriber Identification Number (IMSI), which is the SIM card’s unique code. Besides ClockSkew-IMSI pairing, the other approach, leverages certain hardware characteristics which are difficult to alter. In particular, we make use of the clock skew, which for each device is slightly different. To evaluate our methods, we implement two approaches on OpenAirInterface, an open source 5G development platform. In comparison to the method proposed for 3rd Generation Partnership Project (3GPP), both of our approaches are more efficient in achieving secure device authentication.", "Keywords": "Internet of things ; Mobile communication ; Authentication", "DOI": "10.1016/j.future.2019.10.042", "PubYear": 2020, "Volume": "110", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Networking and Multimedia, National Taiwan University, Taipei, Taiwan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Networking and Multimedia, National Taiwan University, Taipei, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Networking and Multimedia, National Taiwan University, Taipei, Taiwan;Department of Computer Science and Information Engineering, National Taiwan University, Taipei, Taiwan"}], "References": []}, {"ArticleId": 78276888, "Title": "Sparsification of core set models in non-metric supervised learning", "Abstract": "Supervised learning employing positive semi definite kernels has gained wide attraction and lead to a variety of successful machine learning approaches. The restriction to positive semi definite kernels and a hilbert space is common to simplify the mathematical derivations of the respective learning methods, but is also limiting because more recent research indicates that non-metric, and therefore non positive semi definite, data representations are often more effective. This challenge is addressed by multiple approaches and recently dedicated algorithms for so called indefinite learning have been proposed. Along this line, the Krĕin space Support Vector Machine (KSVM) and variants are very efficient classifiers for indefinite learning problems, but with a non-sparse decision function. This very dense decision function prevents practical applications due to a costly out of sample extension. We focus on this problem and provide two post processing techniques to sparsify models as obtained by a Krĕin space SVM approach. In particular we consider the indefinite Core Vector Machine and indefinite Core Vector Regression Machine which are both efficient for psd kernels, but suffer from the same dense decision function, if the Krĕin space approach is used. We evaluate the influence of different levels of sparsity and employ a Nyström approach to address large scale problems. Experiments show that our algorithm is similar efficient as the non-sparse Krĕin space Support Vector Machine but with substantially lower costs, such that also problems of larger scale can be processed.", "Keywords": "Large scale indefinite learning ; <PERSON><PERSON><PERSON> space ; Sparse models ; Orthogonal matching pursuit ; Core sets ; Non-metric learning", "DOI": "10.1016/j.patrec.2019.10.024", "PubYear": 2020, "Volume": "129", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Applied Sciences Würzburg-Schweinfurt, Sanderheinrichsleitenweg 20, Würzburg, 97074, Germany;University of Birmingham School of Computer Science, Edgbaston, Birmingham, B15 2TT, UK;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of Applied Sciences Würzburg-Schweinfurt, Sanderheinrichsleitenweg 20, Würzburg, 97074, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Birmingham School of Computer Science, Edgbaston, Birmingham, B15 2TT, UK"}], "References": []}, {"ArticleId": 78276907, "Title": "Linking students’ emotions to engagement and writing performance when learning Japanese letters with a pen-based tablet: An investigation based on individual pen pressure parameters", "Abstract": "In this paper, two studies were conducted to understand the link between students’ emotions and their engagement and performance when learning to write Japanese letters with a learning technology involving a pen-based tablet. The incorporation of this haptic sensory modality in the form of a pen tablet is seen to have much potential for continuous emotional detection. In Study 1 (136 students, 84 females, M<sub>age</sub>  = 23.55, SD  = 3.89), the learning effectiveness in terms of students’ writing performance was assessed. We further examined the relationship between discrete learning-centered emotions such as enjoyment, frustration and boredom on the one hand, and pen pressure parameters on the other. The results demonstrated a significant gain in writing. Generalized estimating equation (GEE) models showed that minimum, maximum and average pen pressures all serve as indicators of enjoyment and frustration; on the other hand, for boredom, no significant relationships were found. Compared to enjoyment, the results demonstrate that, the higher a student&#x27;s frustration when learning, the more pressure they apply to the pen when writing. In Study 2 (90 students, 60 females, M<sub>age</sub>  = 22.94, SD  = 3.82), the findings from Study 1 were used to understand the practical implications of the results, investigating the relationship between emotion-based pen pressure, student engagement and writing performance over the course of the learning process. As in Study 1, the results, again, showed a significant gain in learning how to write Japanese letters. The partial least squares path model (PLS-PM) analyses revealed that higher pen pressure was a negative predictor of engagement, whereas higher engagement was a positive predictor of writing performance. Furthermore, the relationship between pen pressure and performance was mediated by engagement. Overall, the findings can contribute to our understanding of the interaction between students’ emotions and different learning variables in an attempt to support the development of adaptive learning technologies.", "Keywords": "Achievement emotions ; Pen pressure ; Engagement ; Writing performance", "DOI": "10.1016/j.ijhcs.2019.102374", "PubYear": 2020, "Volume": "135", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ulm University, Institute of Psychology, <PERSON><PERSON><PERSON> 47, 89081 Ulm, Germany;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of New South Wales, School of Education, Sydney, New South Wales, 2052 Australia"}], "References": []}, {"ArticleId": 78276927, "Title": "Online interval scheduling to maximize total satisfaction", "Abstract": "The interval scheduling problem is one variant of the scheduling problem. In this paper, we propose a novel variant of the interval scheduling problem, whose definition is as follows: given jobs are specified by their release times , deadlines and profits . An algorithm must start a job at its release time on one of m identical machines, and continue processing until its deadline on the machine to complete the job. All the jobs must be completed and the algorithm can obtain the profit of a completed job as a user&#x27;s satisfaction. It is possible to process more than one job at a time on one machine. The profit of a job is distributed uniformly between its release time and deadline, that is its interval, and the profit gained from a subinterval of a job decreases in reverse proportion to the number of jobs whose intervals intersect with the subinterval on the same machine. The objective of our variant is to maximize the total profit of completed jobs. This formulation is naturally motivated by best-effort requests and responses to them, which appear in many situations. In best-effort requests and responses, the total amount of available resources for users is always invariant and the resources are equally shared with every user. We study online algorithms for this problem. Specifically, we show that for the case where the profits of jobs are arbitrary, there does not exist an algorithm whose competitive ratio is bounded. Then, we consider the case in which the profit of each job is equal to its length, that is, the time interval between its release time and deadline. For this case, we prove that for m = 2 and m ≥ 3 , the competitive ratios of a greedy algorithm are at most 4/3 and at most 3, respectively. Also, for each m ≥ 2 , we show a lower bound on the competitive ratio of any deterministic algorithm.", "Keywords": "Scheduling problem ; Interval scheduling ; Online algorithm ; Competitive analysis ; Best-effort service", "DOI": "10.1016/j.tcs.2019.10.046", "PubYear": 2020, "Volume": "806", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Tokyo, 3-8-1 Ko<PERSON>ba, Meguro-ku, Tokyo 113-8902, Japan"}], "References": []}, {"ArticleId": 78276944, "Title": "A multivariate normal regression model for survival data subject to different types of dependent censoring", "Abstract": "In survival analysis observations are often right censored and this complicates considerably the analysis of these data. Right censoring can have several underlying causes: administrative censoring, loss to follow up, competing risks, etc. The (latent) censoring times corresponding to the latter two types of censoring are possibly related to the survival time of interest, and in that case this should be taken into account in the model. A unifying model is presented that allows these censoring mechanisms in one single model, and that is also able to incorporate the effect of covariates on these times. Each time variable is modeled by means of a transformed linear model, with the particularity that the error terms of the transformed times follow a multivariate normal distribution allowing for non-zero correlations. It is shown that the model is identified and the model parameters are estimated through a maximum likelihood approach. The performance of the proposed method is compared with methods that assume independent censoring using finite sample simulations. The results show that the proposed method exhibits major advantages in terms of reducing the bias of the parameter estimates. However, a strong deviation from normality and/or a strong violation of the homogeneous variance assumption may lead to biased estimates. Finally, the model and the estimation method are illustrated using the analysis of data coming from a prostate cancer clinical trial.", "Keywords": "Administrative censoring ; Association ; Competing risks ; Identifiability ; Loss to follow up ; Parametric models ; Survival analysis", "DOI": "10.1016/j.csda.2019.106879", "PubYear": 2020, "Volume": "144", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [{"AuthorId": 1, "Name": "Negera Wakgari Deresa", "Affiliation": "ORSTAT, KU Leuven, Naamsestraat 69, B-3000 Leuven, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ORSTAT, KU Leuven, Naamsestraat 69, B-<PERSON> Leuven, Belgium;Corresponding author."}], "References": []}, {"ArticleId": ********, "Title": "A new approach to formulate fuzzy regression models", "Abstract": "A fuzzy regression model is developed to construct the relationship between the response and explanatory variables in fuzzy environments. To enhance explanatory power and take into account the uncertainty of the formulated model and parameters, a new operator, called the fuzzy product core ( FPC ), is proposed for the formulation processes to establish fuzzy regression models with fuzzy parameters using fuzzy observations that include fuzzy response and explanatory variables. In addition, the sign of parameters can be determined in the model-building processes. Compared to existing approaches, the proposed approach reduces the amount of unnecessary or unimportant information arising from fuzzy observations and determines the sign of parameters in the models to increase model performance. This improves the weakness of the relevant approaches in which the parameters in the models are fuzzy and must be predetermined in the formulation processes. The proposed approach outperforms existing models in terms of distance, mean similarity, and credibility measures, even when crisp explanatory variables are used.", "Keywords": "Fuzzy regression model ; Fuzzy product core ; Mathematical programming", "DOI": "10.1016/j.asoc.2019.105915", "PubYear": 2020, "Volume": "86", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Information Management, National Cheng Kung University, Tainan 701, Taiwan, ROC;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Information Management, National Cheng Kung University, Tainan 701, Taiwan, ROC"}], "References": []}, {"ArticleId": 78277021, "Title": "Computing real radicals and S-radicals of polynomial systems", "Abstract": "Let f = ( f 1 , … , f s ) be a sequence of polynomials in Q [ X 1 , … , X n ] of maximal degree D and V ⊂ C n be the algebraic set defined by f and r be its dimension. The real radical 〈 f 〉 r e associated to f is the largest ideal which defines the real trace of V . When V is smooth, we show that 〈 f 〉 r e , has a finite set of generators with degrees bounded by deg ⁡ V . Moreover, we present a probabilistic algorithm of complexity ( s n D n ) O ( 1 ) to compute the minimal primes of 〈 f 〉 r e . When V is not smooth, we give a probabilistic algorithm of complexity s O ( 1 ) ( n D ) O ( n r 2 r ) to compute rational parametrizations for all irreducible components of the real algebraic set V ∩ R n . Let ( g 1 , … , g p ) in Q [ X 1 , … , X n ] and S be the basic closed semi-algebraic set defined by g 1 ≥ 0 , … , g p ≥ 0 . The S -radical of 〈 f 〉 , which is denoted by 〈 f 〉 S , is the ideal associated to the <PERSON>ariski closure of V ∩ S . We give a probabilistic algorithm to compute rational parametrizations of all irreducible components of that Zariski closure, hence encoding 〈 f 〉 S . Assuming now that D is the maximum of the degrees of the f i 's and the g i 's, this algorithm runs in time 2 p ( s + p ) O ( 1 ) ( n D ) O ( r n 2 r ) . Experiments are performed to illustrate and show the efficiency of our approaches on computing real radicals.", "Keywords": "Polynomial system ; Real radical ; S -radical ideal ; Semi-algebraic set ; Real algebraic geometry", "DOI": "10.1016/j.jsc.2019.10.018", "PubYear": 2021, "Volume": "102", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Sorbonne Université, CNRS, INRIA, Laboratoire d'Informatique de Paris 6, L<PERSON>6, <PERSON><PERSON><PERSON>, 4 place <PERSON><PERSON><PERSON>, F-75252, Paris Cedex 05, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, North Carolina State University, Raleigh, NC, USA;Department of Computer Science, Duke University, Durham, NC, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "KLMM, Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing 100190, China;University of Chinese Academy of Sciences, Beijing 100049, China"}], "References": []}, {"ArticleId": 78277025, "Title": "Real-time purchase behavior recognition system based on deep learning-based object detection and tracking for an unmanned product cabinet", "Abstract": "We propose a system to recognize purchasing behavior by detecting and tracking products in real time using only camera sensors in an unmanned product cabinet. To detect and track products in real time, we focused on the simultaneous pre-processing of videos from multiple cameras for robust product detection. After synchronizing multiple videos, unnecessary frames with relatively little information are removed based on change detection. An object score is measured on a frame-by-frame basis to select the most significant frames. Next, the target products are detected and tracked in the selected frames. Finally, the purchasing behavior of the detected product is recognized based on the tracking information. These processes were used to design an end-to-end recognition framework. The contribution of this paper is significant in that by redesigning the existing deep neural networks a real-time integrated system for a practical application was successfully realized without any bottleneck from multi-camera inputs to final object recognition process. Furthermore, the proposed object detection network shows comparable performance with the state-of-the-art methods. We performed intensive experiments to evaluate pure object detection performance as well as to evaluate various purchase/return scenarios. For example, for a basic purchase/return scenario, the proposed system achieved about 92% or more accuracy, which can be the actual level of commercialization.", "Keywords": "Deep learning ; Object detection ; Purchase behavior recognition ; Unmanned product cabinet", "DOI": "10.1016/j.eswa.2019.113063", "PubYear": 2020, "Volume": "143", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Inha University, 100 Inha-ro, Michuhol-gu, Incheon 22212, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Inha University, 100 Inha-ro, Michuhol-gu, Incheon 22212, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Inha University, 100 Inha-ro, Michuhol-gu, Incheon 22212, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Inha University, 100 Inha-ro, Michuhol-gu, Incheon 22212, Republic of Korea;Corresponding author."}], "References": []}, {"ArticleId": 78277030, "Title": "Molecular imprinted polymer based electrochemical sensor for selective detection of paraben", "Abstract": "A new, simple, rapid, sensitive and selective disposable sensor platform was developed for the electrochemical detection of paraben based on molecular imprinting technique. Highly selective and specific recognition was established for the analyte through incorporating amino acid based polymerizable functional monomer. Paraben-imprinted poly-(2-hydroxyethyl methacrylate -N- methacryloyl-L-phenylalanine) (PHEMA-MAPA) nanofilm on a screen-printed gold electrode surface in the presence of polyvinyl alcohol (PVA) was synthesized by the molecular imprinting technique. Characterization of the fabricated electrode surfaces were performed with cyclic voltammetry (CV), atomic force microscopy (AFM) and attenuated total reflection fourier transform infrared spectrophotometry (ATR FT-IR). The electrochemical behavior of paraben was investigated using CV and electrochemical detection studies were carried out with square wave voltammetry (SWV). Under optimal conditions, the linear working range was found to be 1–30 μM with a low detection limit as 0.706 μM. The obtained recovery values proved that the developed sensor can be effectively applied to cosmetic samples. Besides, the fabricated disposable sensor system can be used successfully in the determination of other important analytes in the future due to its good sensor performance.", "Keywords": "Molecular imprinted polymer ; Paraben ; Electrochemical sensor", "DOI": "10.1016/j.snb.2019.127368", "PubYear": 2020, "Volume": "305", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Beyhan Buse Yücebaş", "Affiliation": "Biochemistry Division, Department of Chemistry, Hacettepe University, Ankara, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Technologies Application and Research Center, Hacettepe University, Ankara, Turkey;Analytical Chemistry Division, Department of Chemistry, Hacettepe University, Ankara, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Analytical Chemistry Division, Department of Chemistry, Hacettepe University, Ankara, Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Biochemistry Division, Department of Chemistry, Hacettepe University, Ankara, Turkey;Advanced Technologies Application and Research Center, Hacettepe University, Ankara, Turkey;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Biochemistry Division, Department of Chemistry, Hacettepe University, Ankara, Turkey;Advanced Technologies Application and Research Center, Hacettepe University, Ankara, Turkey;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Technologies Application and Research Center, Hacettepe University, Ankara, Turkey;Analytical Chemistry Division, Department of Chemistry, Hacettepe University, Ankara, Turkey"}], "References": []}, {"ArticleId": 78277034, "Title": "Enhanced strain derivative of Ho substituted cobalt ferrite and improved magnetoelectric coupling in co-sintered bi-layered ME composites", "Abstract": "The magnetoelectric bilayer composites of Ho substituted cobalt ferrite as piezomagnetic phase and Sr, La modified PZT (PSLZT) as a piezoelectric phase were prepared to study magnetoelectric effect. The Ho substituted cobalt ferrite CoFe<sub>2-x</sub>Ho<sub>x</sub>O<sub>4</sub> where, x = 0.0, 0.01, 0.02, 0.03, 0.05 and 0.15, were successfully synthesized by sol-gel auto combustion method. The phase formation of synthesized Ho substituted cobalt ferrite series has been confirmed by using X-ray diffraction (XRD). For the samples up to x = 0.05 formation of single spinel ferrite phase and for higher concentration i.e. x = 0.15, along with spinel ferrite phase secondary ortho-ferrite phase have been observed. FTIR and Raman spectroscopic analysis has been carried out to confirm the presence of metal oxide functional group at tetrahedral and octahedral sites of spinel structure. Morphological analysis of all the samples under investigation has been carried out using Scanning Electron Microscopy. The magnetostrictive properties were measured at room temperature and the influence of Ho substitution on magnetostrictive properties of cobalt ferrite has also been investigated. Further, the strain derivative (dλ/dH) has been determined in order to understand the piezomagnetic properties of the samples. The strain derivative of Ho substituted cobalt ferrite attains maximum value at a low magnetic field with respect to cobalt ferrite which is useful in stress sensors applications. Finally, the magnetoelectric properties of PSLZT -Ho substituted cobalt ferrite layered composite structure have been investigated. The relation between magnetoelectric properties of the composites and piezomagnetic properties of the ferrite samples has been elucidated based on the theoretical model proposed in the literature by Bichurin et al.", "Keywords": "Magnetoelectric effect ; Magnetostriction ; Bilayer composites ; Morphological properties", "DOI": "10.1016/j.sna.2019.111716", "PubYear": 2020, "Volume": "301", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Savitribai Phule Pune University, Pune, 411007, MS, India;Department of Physics, Rajaram College, Kolhapur, 416004, MS, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Savitribai Phule Pune University, Pune, 411007, MS, India;Armament Research and Development Establishment, Pune, 411021, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Rajaram College, Kolhapur, 416004, MS, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "UGC-DAE-Consortium for Scientific Research, Mumbai Centre, BARC R-5 shade, Mumbai 400085, MS, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Savitribai Phule Pune University, Pune, 411007, MS, India;Corresponding author."}], "References": []}, {"ArticleId": 78277178, "Title": "A Bayesian network approach for cybersecurity risk assessment implementing and extending the FAIR model", "Abstract": "Quantitative risk assessment can play a crucial role in effective decision making about cybersecurity strategies. The Factor Analysis of Information Risk (FAIR) is one of the most popular models for quantitative cybersecurity risk assessment. It provides a taxonomic framework to classify cybersecurity risk into a set of quantifiable risk factors and combines this with quantitative algorithms, in the form of a kind of Monte Carlo (MC) simulation combined with statistical approximation techniques, to estimate cybersecurity risk. However, the FAIR algorithms restrict both the type of statistical distributions that can be used and the expandability of the model structure. Moreover, the applied approximation techniques (including using cached data and interpolation methods) introduce inaccuracy into the FAIR model. To address restrictions of the FAIR model, we develop a more flexible alternative approach, which we call FAIR-BN, to implement the FAIR model using Bayesian Networks (BNs). To evaluate the performance of FAIR and FAIR-BN, we use a MC method (FAIR-MC) to implement calculations of the FAIR model without using any of the approximation techniques adopted by FAIR, thus avoiding the corresponding inaccuracy that can be introduced. We compare the empirical results generated by FAIR and FAIR-BN against a large number of samples generated using FAIR-MC. Both FAIR and FAIR-BN provide consistent results compared with FAIR-MC for general cases. However, the FAIR-BN achieves higher accuracy in several cases that cannot be accurately modelled by the FAIR model. Moreover, we demonstrate that FAIR-BN is more flexible and extensible by showing how it can incorporate process-oriented and game-theoretic methods. We call the resulting combined approach “Extended FAIR-BN” (EFBN) and show that it has the potential to provide an integrated solution for cybersecurity risk assessment and related decision making.", "Keywords": "Cybersecurity risk assessment ; FAIR model ; Bayesian networks ; Monte Carlo simulation ; Risk aggregation ; Adversarial risk analysis ; Game theory", "DOI": "10.1016/j.cose.2019.101659", "PubYear": 2020, "Volume": "89", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Engineering & Computer Science, Queen Mary University of London, Mile End Road, London E1 4NS, United Kingdom;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Agena Ltd, Cambridge, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Agena Ltd, Cambridge, United Kingdom"}], "References": []}, {"ArticleId": 78277193, "Title": "Realizability of three-valued semantics for abstract dialectical frameworks", "Abstract": "We investigate fundamental properties of three-valued semantics for abstract dialectical frameworks (ADFs). In particular, we deal with realizability , i.e., the question whether there exists an ADF that has a given set of interpretations as its semantics. We provide necessary and sufficient conditions that hold for a set of three-valued interpretations whenever there is an ADF realizing it under admissible, complete, grounded, or preferred semantics. Moreover, we show how to construct such an ADF in case of realizability. Our results lay the ground for studying the expressiveness of ADFs under three-valued semantics. Since ADFs generalize many existing approaches, our results have immediate impact on many argumentation formalisms including abstract argumentation frameworks. As first applications, we study implications of our results on the existence of certain join operators on ADFs. Furthermore, we exploit our (exact) characterizations to obtain realizability results also for a more relaxed setting, where realizing ADFs may contain hidden statements.", "Keywords": "Argumentation ; Abstract dialectical frameworks ; Realizability ; Signatures ; Expressiveness", "DOI": "10.1016/j.artint.2019.103198", "PubYear": 2020, "Volume": "278", "Issue": "", "JournalId": 13748, "JournalTitle": "Artificial Intelligence", "ISSN": "0004-3702", "EISSN": "1872-7921", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Logic and Computation, TU Wien, Austria"}], "References": []}, {"ArticleId": 78277230, "Title": "Rank minimization on tensor ring: an efficient approach for tensor decomposition and completion", "Abstract": "<p>In recent studies, tensor ring decomposition (TRD) has become a promising model for tensor completion. However, TRD suffers from the rank selection problem due to the undetermined multilinear rank. For tensor decomposition with missing entries, the sub-optimal rank selection of traditional methods leads to the overfitting/underfitting problem. In this paper, we first explore the latent space of the TRD and theoretically prove the relationship between the TR-rank and the rank of the tensor unfoldings. Then, we propose two tensor completion models by imposing the different low-rank regularizations on the TR-factors, by which the TR-rank of the underlying tensor is minimized and the low-rank structures of the underlying tensor are exploited. By employing the alternating direction method of multipliers scheme, our algorithms obtain the TR factors and the underlying tensor simultaneously. In experiments of tensor completion tasks, our algorithms show robustness to rank selection and high computation efficiency, in comparison to traditional low-rank approximation algorithms.</p>", "Keywords": "Tensor ring decomposition; Tensor completion; Structured nuclear norm; ADMM scheme", "DOI": "10.1007/s10994-019-05846-7", "PubYear": 2020, "Volume": "109", "Issue": "3", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "Long<PERSON> Yuan", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, China;Graduate School of Engineering, Saitama Institute of Technology, Fukaya, Japan;Tensor Learning Unit, RIKEN Center for Advanced Intelligence Project (AIP), Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tensor Learning Unit, RIKEN Center for Advanced Intelligence Project (AIP), Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>ng Cao", "Affiliation": "Graduate School of Engineering, Saitama Institute of Technology, Fukaya, Japan;Tensor Learning Unit, RIKEN Center for Advanced Intelligence Project (AIP), Tokyo, Japan;School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, China;Tensor Learning Unit, RIKEN Center for Advanced Intelligence Project (AIP), Tokyo, Japan"}], "References": []}, {"ArticleId": 78277493, "Title": "A method for outlier detection based on cluster analysis and visual expert criteria", "Abstract": "<p>Outlier detection is an important problem occurring in a wide range of areas. Outliers are the outcome of fraudulent behaviour, mechanical faults, human error, or simply natural deviations. Many data mining applications perform outlier detection, often as a preliminary step in order to filter out outliers and build more representative models. In this paper, we propose an outlier detection method based on a clustering process. The aim behind the proposal outlined in this paper is to overcome the specificity of many existing outlier detection techniques that fail to take into account the inherent dispersion of domain objects. The outlier detection method is based on four criteria designed to represent how human beings (experts in each domain) visually identify outliers within a set of objects after analysing the clusters. This has an advantage over other clustering‐based outlier detection techniques that are founded on a purely numerical analysis of clusters. Our proposal has been evaluated, with satisfactory results, on data (particularly time series) from two different domains: stabilometry, a branch of medicine studying balance‐related functions in human beings and electroencephalography (EEG), a neurological exploration used to diagnose nervous system disorders. To validate the proposed method, we studied method outlier detection and efficiency in terms of runtime. The results of regression analyses confirm that our proposal is useful for detecting outlier data in different domains, with a false positive rate of less than 2% and a reliability greater than 99%.</p>", "Keywords": "clustering;data mining;KDD;outlier detection;visual expert criteria", "DOI": "10.1111/exsy.12473", "PubYear": 2020, "Volume": "37", "Issue": "5", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Madrid Open University, UDIMA, Engineering School, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Madrid Open University, UDIMA, Engineering School, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ETS de Ingenieros Informáticos, Universidad Politécnica de Madrid, Campus de Montegancedo, Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ETS de Ingenieros Informáticos, Universidad Politécnica de Madrid, Campus de Montegancedo, Madrid, Spain"}], "References": []}, {"ArticleId": 78277564, "Title": "On social-aware data uploading study of D2D-enabled cellular networks", "Abstract": "The data uploading study of device-to-device (D2D) enabled cellular networks is critical for supporting their applications. Available data uploading studies for D2D-enabled cellular networks mainly consider either the cooperative scenarios with full trust or no trust, where the devices in the network forward data with each other in full cooperative or noncooperative way, while largely neglecting the effect of human social relationships on the cooperation behaviors among the devices under real scenarios. As a first step to address this issue, this paper extends the previous works on cooperative D2D data uploading for cellular networks to a more real social network scenario and investigates social-aware data uploading in D2D-enable cellular networks. We first propose an incentive mechanism to compensate the resource consumption (e.g., energy, memory, and time spent) of devices on data uploading. With this incentive mechanism, the nearby devices can obtain rewards such that they are willing to construct a multi-hop D2D chain to assist the other devices for data uploading. To this end, we adopt coalitional game to formulate the D2D chain with careful consideration of social-aware data uploading, where each device acts as a player and the individual reward is modeled as the utility function. We further design a coalition formation algorithm with merge-and-split rules to determine the solution for formulated D2D chain. Extensive simulations are conducted to illustrate the performance improvement of our proposed scheme in comparison with that of other state-of-the-art schemes.", "Keywords": "Data uploading ; D2D communications ; Social-awareness ; Incentive mechanism ; Coalitional game", "DOI": "10.1016/j.comnet.2019.106955", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Chuzhou University, China;School of Systems Information Science, Future University Hakodate, Japan;Shaanxi Key Laboratory of Network and System Security, Xidian University, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer and Information Engineering, Chuzhou University, China;School of Computer Science, Shaanxi Normal University, China;Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Systems Information Science, Future University Hakodate, Japan"}, {"AuthorId": 4, "Name": "Lisheng Ma", "Affiliation": "School of Computer and Information Engineering, Chuzhou University, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Shen", "Affiliation": "School of Information and Technology, Kunming University, China"}], "References": []}, {"ArticleId": 78277572, "Title": "The Use of Partial Fractional Form of A-Stable Padé Schemes for the Solution of Fractional Diffusion Equation with Application in Option Pricing", "Abstract": "<p>In this work, we propose a numerical technique based on the Padé scheme for solving the two-sided space-fractional diffusion equation. First, space fractional diffusion equations are approximated with respect to space variable. We will achieve a system of ODE. Then by applying a parallel implementation of the A-stable methods, this system is solved. Also, we use of the presented method for pricing European call option under a geometric Lévy process. Illustrative examples are included to show the accuracy and applicability of the new technique presented in the current paper.</p>", "Keywords": "Fractional diffusion equation; Padé approximation; A-stable method; <PERSON><PERSON><PERSON> equation; Option pricing", "DOI": "10.1007/s10614-019-09927-6", "PubYear": 2020, "Volume": "56", "Issue": "4", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Azarbaijan Shahid Madani University, Tabriz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Azarbaijan Shahid Madani University, Tabriz, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Azarbaijan Shahid Madani University, Tabriz, Iran"}], "References": []}, {"ArticleId": 78277578, "Title": "GWAS: Fast-forwarding gene identification and characterization in temperate Cereals: lessons from <PERSON><PERSON> – A review", "Abstract": "<p>Understanding the genetic complexity of traits is an important objective of small grain temperate cereals yield and adaptation improvements. Bi-parental quantitative trait loci (QTL) linkage mapping is a powerful method to identify genetic regions that co-segregate in the trait of interest within the research population. However, recently, association or linkage disequilibrium (LD) mapping using a genome-wide association study (GWAS) became an approach for unraveling the molecular genetic basis underlying the natural phenotypic variation. Many causative allele(s)/loci have been identified using the power of this approach which had not been detected in QTL mapping populations. In barley (<i>Hordeum vulgare</i> L.), GWAS has been successfully applied to define the causative allele(s)/loci which can be used in the breeding crop for adaptation and yield improvement. This promising approach represents a tremendous step forward in genetic analysis and undoubtedly proved it is a valuable tool in the identification of candidate genes. In this review, we describe the recently used approach for genetic analyses (linkage mapping or association mapping), and then provide the basic genetic and statistical concepts of GWAS, and subsequently highlight the genetic discoveries using GWAS. The review explained how the candidate gene(s) can be detected using state-of-art bioinformatic tools.</p><p>© 2019 THE AUTHORS. Published by Elsevier BV on behalf of Cairo University.</p>", "Keywords": "Association mapping;Barley breeding, GWAS;Gene identification;Hordeum vulgare L;QTL mapping", "DOI": "10.1016/j.jare.2019.10.013", "PubYear": 2020, "Volume": "22", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Resources Genetics and Reproduction, Department Genebank, Leibniz Institute of Plant Genetics and Crop Plant Research (IPK), Corrensstr. 3, OT Gatersleben, D-06466 Stadt Seeland, Germany."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Genetics, Faculty of Agriculture, Assiut University, 71526- Assiut, Egypt."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Agronomy &amp; Horticulture, University of Nebraska-Lincoln, 68583-Lincoln, NE, USA."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Resources Genetics and Reproduction, Department Genebank, Leibniz Institute of Plant Genetics and Crop Plant Research (IPK), Corrensstr. 3, OT Gatersleben, D-06466 Stadt Seeland, Germany."}], "References": []}, {"ArticleId": ********, "Title": "The embeddedness of social capital in personal networks", "Abstract": "Abstract <p> Name generators (NGs) and position generators (PGs) have been used to measure resources embedded in personal relationships, namely social support and social capital, respectively. Comparisons of these measures adopted NGs that only elicit a small number of alters (max. 5). In this paper we explore whether the measurement of social capital with NGs eliciting larger personal networks (say 15 to 20 alters) gives more comparable results to the PG in terms of occupational prestige. To address this issue, we designed a personal network questionnaire that combined a multiple name generator (MNG) and a PG and enquired about alter characteristics and alter-alter ties for the two sets of nominations simultaneously, allowing their integrated analysis. The questionnaire was implemented in the software EgoNet to collect data from social/environmental entrepreneurs in Spain ( N = 30) and Mexico ( N = 30. The analysis shows that the two approaches capture mostly non-overlapping sets of personal network members, suggesting that the PG measured in this case available, but not accessed social capital. Remarkably the NG led to a higher average prestige for this occupational group than the PG, but also a lower heterogeneity in prestige. The consequences of using one or another approach and their interpretations are discussed. </p>", "Keywords": "<PERSON><PERSON>;<PERSON><PERSON>;<PERSON>;<PERSON>;personal networks;social capital;name generator;position generator;social entrepreneurship", "DOI": "10.1017/nws.2019.30", "PubYear": 2020, "Volume": "8", "Issue": "2", "JournalId": 25796, "JournalTitle": "Network Science", "ISSN": "2050-1242", "EISSN": "2050-1250", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitat Autònoma de Barcelona,Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma de Aguascalientes,México"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universitat Autònoma de Barcelona,Spain"}, {"AuthorId": 4, "Name": "<PERSON>-<PERSON>", "Affiliation": "Universitat Autònoma de Barcelona,Spain"}], "References": []}, {"ArticleId": 78277608, "Title": "A Music Classification model based on metric learning applied to MP3 audio files", "Abstract": "The development of models for learning music similarity from audio media files is an increasingly important task for the entertainment industry. This work proposes a novel music classification model based on metric learning whose main objective is to learn a personalized metric for each customer. The metric learning process considers the learning of a set of parameterized distances employing a structured prediction approach from a set of MP3 audio files containing several music genres according to the users taste. The structured prediction solution aims to maximize the separation margin between genre centroids and to minimize the overall intra-cluster distances. To extract the acoustic information we use the Mel-Frequency Cepstral Coecient (MFCC) and made a dimensionality reduction using Principal Components Analysis (PCA). We attest the model validity performing a set of experiments and comparing the training and testing results with baseline algorithms, such as K-means and Soft Margin Linear Support Vector Machine (SVM). Also, to prove the prediction capacity, we compare our results with two recent works with good prediction results on the GTZAN dataset. Experiments show promising results and encourage the future development of an online version of the learning model to be applied in streaming platforms.", "Keywords": "Music similarity ; Metric learning ; Feature extraction ; Mel frequency cepstral coefficient ; Principal components analysis ; RCEPS Real Cepstral Coefficients ; ZCR Zero-crossing Rate ; AFTE Auditory Filterbank Temporal Envelopes ; CHR Chromagram ; LSP Line Spectral Pairs ; TMBR Timbre ; SCF Spectral Crest Factor ; SFM Spectral Flatness Measure", "DOI": "10.1016/j.eswa.2019.113071", "PubYear": 2020, "Volume": "144", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departament of Computer Science, Universidade Federal de Juiz de Fora, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Academic Department of Computer Science, IF Sudeste MG - Rio Pomba, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>ul Fonseca Neto", "Affiliation": "Departament of Computer Science, Universidade Federal de Juiz de Fora, Brazil;Corresponding author."}], "References": []}, {"ArticleId": ********, "Title": "A DSL for WSN software components coordination", "Abstract": "Wireless Sensor Networks (WSNs) have become an integral part of urban scenarios. They are usually composed of a large number of devices. Developing systems for such networks is a hard task and often involves validation on simulation environments before deployment on real settings. Component-based development allows systems to be built from reusable, existing components that share a common interface. This paper proposes a domain specific language (DSL) for coordination of WSN software components. The language provides high-level composition primitives to promote a flexible coordination execution flow and interaction between them. We present the language specification as well as a case study of an in-network WSN data storage coordination. The current specification of the language generates code for the NS2 simulation environment. The case study shows that the language implements a flexible development model. Moreover, we analyze the code reusability promoted by the language and show that it reduces the programming effort in a component-based development framework.", "Keywords": "Domain-Specific Language ; Component-based software development ; Wireless Sensor Network", "DOI": "10.1016/j.is.2019.101461", "PubYear": 2021, "Volume": "98", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidade Federal do Paraná, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidade Federal do Rio Grande do Norte, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidade Federal do Paraná, Brazil"}, {"AuthorId": 4, "Name": "Carmem S. Hara", "Affiliation": "Universidade Federal do Paraná, Brazil"}], "References": []}, {"ArticleId": 78277865, "Title": "Rainbowarray Microsphere-Based Gene Detection Assay", "Abstract": "<p>Here, we have developed a set of fluorophore-labeled microspheres named rainbowarray microspheres. Based on the spectrally encoded microspheres, we further developed a liquid hybridization approach for multiplex target detection. Different from the prototype Luminex xMAP array, this technology enables feasible, flexible, and cost-efficient microsphere labeling and multiplex detection in a timely and high-throughput manner. To demonstrate the practicability of this technology, quantitative measurement of microRNA regulation was performed during the differentiation of 3T3-L1 cells, in which the expression of two microRNAs was determined at a 2 h interval during a process of 2 days. The flexibility and the timely and high-throughput properties of the technology enable it to be widely implemented in clinical testing.</p>", "Keywords": "adipocyte differentiation;liquid hybridization;multiplex detection;spectrally encoded microsphere", "DOI": "10.1177/2472630319882319", "PubYear": 2020, "Volume": "25", "Issue": "3", "JournalId": 19198, "JournalTitle": "SLAS TECHNOLOGY: Translating Life Sciences Innovation", "ISSN": "2472-6303", "EISSN": "2472-6311", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The Sixth Medical Center of Chinese PLA General Hospital, Beijing, China;Cancer Research Center Nantong, Tumor Hospital Affiliated to Nantong University, Nantong, Jiangsu, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing, China"}], "References": []}, {"ArticleId": 78277960, "Title": "Effects of steering locomotion and teleporting on cybersickness and presence in HMD-based virtual reality", "Abstract": "<p>While head-mounted display-based virtual reality (VR) can produce compelling feelings of presence (or “being there”) in its users, it also often induces motion sickness. This study compared the presence, cybersickness and perceptions of self-motion (or “vection”) induced when using two common methods of virtual locomotion: steering locomotion and teleporting. In four trials, conducted over two separate days, 25 participants repeatedly explored the “Red Fall” virtual environment in the game Nature Treks VR for 16 min at a time. Although steering locomotion was found to be more sickening on average than teleporting, 9 participants reported more severe sickness while teleporting. On checking their spontaneous postural activity before entering VR, these “TELEsick” participants were found to differ from “STEERsick” participants in terms of their positional variability when attempting to stand still. While cybersickness was not altered by having the user stand or sit during gameplay, presence was enhanced by standing during virtual locomotion. Cybersickness was found to increase with time in trial for both methods of virtual locomotion. By contrast, presence only increased with time in trial during steering locomotion (it did not vary over time when teleporting). Steering locomotion was also found to generate greater presence for female, but not male, participants. While there was not a clear advantage for teleporting over steering locomotion in terms of reducing cybersickness, we did find some evidence of the benefits of steering locomotion for presence.</p>", "Keywords": "Motion sickness; Cybersickness; Virtual reality; Head-mounted display; Presence", "DOI": "10.1007/s10055-019-00407-8", "PubYear": 2020, "Volume": "24", "Issue": "3", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Psychology, University of Wollongong, Wollongong, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Psychology, University of Wollongong, Wollongong, Australia"}], "References": []}, {"ArticleId": 78277961, "Title": "A method to decompose the systemic risk in geographic areas", "Abstract": "<p>In this paper, a method for evaluating systemic risk in different geographic areas is presented. The proposed methodology is based on the decomposition by subpopulations of the Gini index, largely used to assess the inequality of income and wealth. This decomposition procedure follows a two-step approach that goes beyond the “classical” decomposition Within and Between components, also allowing the assessment of the contribution to the total inequality for each subpopulation.</p>", "Keywords": "Systemic risk; Inequality index; Decomposition by subpopulations; Gini index", "DOI": "10.1007/s00500-019-04463-9", "PubYear": 2020, "Volume": "24", "Issue": "12", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dipartimento di Statistica e Metodi Quantitativi, Università degli Studi di Milano-Bicocca, Milan, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dipartimento di Statistica e Metodi Quantitativi, Università degli Studi di Milano-Bicocca, Milan, Italy"}], "References": []}, {"ArticleId": 78277978, "Title": "A novel hybrid bat algorithm with a fast clustering-based hybridization", "Abstract": "<p>Bat algorithm (BA) is a new and promising metaheuristic search algorithm which could outperform existing algorithms. However, BA can be easily trapped in a local optimum regarded to low exploration ability. The present study proposed a new local-search-based hybrid heuristic to escape such scenario. The proposed hybrid BA (hBA) uses a clustering-based hybridization method which detects the early convergence of BA population by analyzing similarities among individuals. The main motivation for such an analysis is that when BA is continually converging, the similarity among individuals becomes higher. The proposed hBA is extensively evaluated on CEC2017 benchmark suite. The Experiments demonstrate that the algorithm achieves better results than continues variants of BA in every way. Moreover, as a case study, a binary version of the proposed hBA (hBBA) is applied to the well-known feature selection problem. The recorded results on 13 datasets demonstrate that hBBA would be considered as a new state-of-art in metaheuristic-based wrapper feature selection methods.</p>", "Keywords": "Bat Algorithm; Metaheuristic search; Nature inspired search; Feature selection", "DOI": "10.1007/s12065-019-00307-5", "PubYear": 2020, "Volume": "13", "Issue": "3", "JournalId": 5202, "JournalTitle": "Evolutionary Intelligence", "ISSN": "1864-5909", "EISSN": "1864-5917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mathematical Sciences, University of Guilan, Rasht, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> of Kerman, Kerman, Iran"}], "References": []}, {"ArticleId": 78278011, "Title": "The Class of All Natural Implicative Expansions of <PERSON><PERSON><PERSON>’s Strong Logic Functionally Equivalent to <PERSON><PERSON><PERSON><PERSON>’s 3-Valued Logic Ł3", "Abstract": "<p>We consider the logics determined by the set of all natural implicative expansions of <PERSON><PERSON><PERSON>’s strong 3-valued matrix (with both only one and two designated values) and select the class of all logics functionally equivalent to <PERSON><PERSON><PERSON><PERSON>’s 3-valued logic Ł3. The concept of a “natural implicative matrix” is based upon the notion of a “natural conditional” defined in Tomova (Rep Math Log 47:173–182, 2012).</p>", "Keywords": "Many-valued logic; 3-Valued logic; <PERSON><PERSON><PERSON>’s strong logic; <PERSON><PERSON><PERSON><PERSON>’s 3-valued logic; Functional equivalence; Natural conditionals; 03B47", "DOI": "10.1007/s10849-019-09306-2", "PubYear": 2020, "Volume": "29", "Issue": "3", "JournalId": 5283, "JournalTitle": "Journal of Logic, Language and Information", "ISSN": "0925-8531", "EISSN": "1572-9583", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dpto. de Psicología, Sociología y Filosofía, Universidad de León, León, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad de Salamanca, Salamanca, Spain"}], "References": []}, {"ArticleId": 78278014, "Title": "Inspyred: Bio-inspired algorithms in Python", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10710-019-09367-z", "PubYear": 2020, "Volume": "21", "Issue": "1-2", "JournalId": 4125, "JournalTitle": "Genetic Programming and Evolvable Machines", "ISSN": "1389-2576", "EISSN": "1573-7632", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "UMR 782 GMPA, INRA, Université Paris-Saclay, Thiverval-Grignon, France"}], "References": []}, {"ArticleId": 78278249, "Title": "Color image segmentation using saturated RGB colors and decoupling the intensity from the hue", "Abstract": "<p>Although the RGB space is accepted to represent colors, it is not adequate for color processing. In related works the colors are usually mapped to other color spaces more suitable for color processing, but it may imply an important computational load because of the non-linear operations involved to map the colors between spaces; nevertheless, it is common to find in the state-of-the-art works using the RGB space. In this paper we introduce an approach for color image segmentation, using the RGB space to represent and process colors; where the chromaticity and the intensity are processed separately, mimicking the human perception of color, reducing the underlying sensitiveness to intensity of the RGB space. We show the hue of colors can be processed by training a self-organizing map with chromaticity samples of the most saturated colors, where the training set is small but very representative; once the neural network is trained it can be employed to process any given image without training it again. We create an intensity channel by extracting the magnitudes of the color vectors; by using the Otsu method, we compute the threshold values to divide the intensity range in three classes. We perform experiments with the Berkeley segmentation database; in order to show the benefits of our proposal, we perform experiments with a neural network trained with different colors by subsampling the RGB space, where the chromaticity and the intensity are processed jointly. We evaluate and compare quantitatively the segmented images obtained with both approaches. We claim to obtain competitive results with respect to related works.</p>", "Keywords": "RGB space; Color image segmentation; Self-organizing maps; Otsu method", "DOI": "10.1007/s11042-019-08278-6", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de México, Centro Universitario UAEM Texcoco, Texcoco, Mexico"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de México, Centro Universitario UAEM Texcoco, Texcoco, Mexico"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de México, Centro Universitario UAEM Zumpango, Zumpango, Mexico"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de México, Centro Universitario UAEM Texcoco, Texcoco, Mexico"}], "References": []}, {"ArticleId": 78278252, "Title": "Fault diagnosis and accommodation with flight control applications", "Abstract": "This paper reviews some main results and process concerning with fault diagnosis and accommodation with flight control applications. The fault detection and diagnosis including the quantitative and qualitative methods and the fault-tolerant control including passive and active schemes are introduced, respectively. The control designs and developments of the fault diagnosis and accommodation for continuous-time systems and discrete-time systems, the fault diagnosis and fault-tolerant control for switched systems and interconnected systems as well as the applied flight control research including hypersonic vehicle, spacecraft and helicopter systems are summarised.", "Keywords": "Fault diagnosis ; fault accommodation ; fault-tolerant control ; switched systems ; interconnected systems ; multi-agent systems ; flight control applications", "DOI": "10.1080/23307706.2019.1686434", "PubYear": 2020, "Volume": "7", "Issue": "1", "JournalId": 30013, "JournalTitle": "Journal of Control and Decision", "ISSN": "2330-7706", "EISSN": "2330-7714", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Automation Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Automation Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Automation Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, People's Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Automation Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, People's Republic of China"}], "References": []}, {"ArticleId": 78278401, "Title": "Data analysis and mining of the correlations between meteorological conditions and air quality: A case study in Beijing", "Abstract": "The air pollution caused by PM2.5, PM10, and O<sub>3</sub> is an emerging problem that threatens public health, especially in China’s megacities. Meteorological factors have significant impacts on the dilution and diffusion of air pollutants which further affect the distribution and concentration of pollutants. In this paper, we analyze the relationships between air pollutant concentrations and meteorological conditions in Beijing from January 2017 to January 2018. We observe that: (1) the influence of a single meteorological factor on the concentration of pollutants is limited; (2) the temperature-wind speed combination, temperature-pressure combination, and humidity-wind speed combination are highly correlated with the concentration of pollutants, indicating that a variety of meteorological factors combine to affect the concentration of pollutants; and (3) different meteorological factors have different effects on the concentration of the same pollutant, while the same meteorological conditions have different effects on the concentration of different pollutants. Our findings can assist in predicting the air quality according to meteorological conditions while further improving the urban management performance.", "Keywords": "Data mining ; Air quality ; Meteorology ; Correlation ; Beijing", "DOI": "10.1016/j.iot.2019.100127", "PubYear": 2021, "Volume": "14", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Technology, China University of Geosciences (Beijing), China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Engineering and Technology, China University of Geosciences (Beijing), China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Applications, University of Naples Federico II, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Automation, Beijing University of Posts and Telecommunications, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON> Xu", "Affiliation": "School of Engineering and Technology, China University of Geosciences (Beijing), China"}], "References": []}, {"ArticleId": 78278404, "Title": "Aggregating diverse deep attention networks for large-scale plant species identification", "Abstract": "In this paper, a novel fusion method is proposed to deal with large-scale plant species identification by aggregating diverse outputs from multiple deep networks, where each deep network focus on one subset of the whole plant species. Firstly, a fixed plant taxonomy is constructed for organizing large number of fine-grained plant species hierarchically and it is further used as a guideline to help generating diverse but overlapped task groups. Secondly, an attention-based deep hierarchical multi-task learning (AHMTL) algorithm is proposed to recognize fine-grained plant species belonging to the same task group effectively by learning more discriminative deep features and classifiers jointly. Finally, we fuse all outputs from multiple deep networks to obtain the final high-level feature representation and give the prediction probability for each plant species. The experimental results have proved the effectiveness of our proposed method on large-scale plant species identification.", "Keywords": "Large-scale plant species identification ; Plant taxonomy ; Attention-based hierarchical multi-task learning ; Fusion", "DOI": "10.1016/j.neucom.2019.10.077", "PubYear": 2020, "Volume": "378", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "Northwestern Polytechnical University, Shaanxi, China"}, {"AuthorId": 2, "Name": "Zhenz<PERSON>", "Affiliation": "Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University, Shaanxi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> He", "Affiliation": "Northwestern Polytechnical University, Shaanxi, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Northwestern Polytechnical University, Shaanxi, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northwest University, Shaanxi, China"}], "References": []}, {"ArticleId": 78278464, "Title": "Lower bounds for special cases of syntactic multilinear ABPs", "Abstract": "Algebraic Branching Programs (ABPs) are standard models for computing polynomials. Syntactic multilinear ABPs (smABPs) are restrictions of ABPs where every variable is allowed to occur at most once in every path from the source to the sink node. Proving super-polynomial lower bounds for syntactic multilinear ABPs remains a challenging open question in Algebraic Complexity Theory. The current best lower bound for smABPs is only quadratic in the number of variables [1] . In this article, we develop a new approach for proving syntactic multilinear branching program lower bounds: Convert the smABP into an equivalent multilinear formula with a super-polynomial blow-up in size and then exploit the structural limitations of the resulting formula to obtain an upper bound on the rank of partial derivative matrix of the polynomial computed by the smABP. Using this approach, we prove exponential lower bounds for special cases of smABPs namely sum of Read-Once Oblivious smABPs, multilinear r -pass ABPs and α -set-multilinear ABPs. En route, we also prove an exponential lower bound for a special class of syntactic multilinear arithmetic circuits using a similar approach.", "Keywords": "Computational complexity ; Algebraic complexity theory ; Polynomials", "DOI": "10.1016/j.tcs.2019.10.047", "PubYear": 2020, "Volume": "809", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, IIT Madras, Chennai, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, IIT Madras, Chennai, India"}], "References": []}, {"ArticleId": 78278480, "Title": "Left-handed completeness", "Abstract": "We give a new proof of the completeness of the left-handed star rule of Kleene algebra. The proof is significantly shorter than previous proofs and exposes the rich interaction of algebra and coalgebra in the theory of Kleene algebra.", "Keywords": "Nivat ; Kleene algebra ; Completeness", "DOI": "10.1016/j.tcs.2019.10.040", "PubYear": 2020, "Volume": "807", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Cornell University, Ithaca, NY 14853-7501, USA;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University College London, Gower Street, London WC1E 6BT, UK;Corresponding authors"}], "References": []}, {"ArticleId": 78278487, "Title": "Novel updatable identity-based hash proof system and its applications", "Abstract": "<PERSON><PERSON> et al. in Eurocrypt 2010, showed that an identity-based hash proof system (IB-HPS), where IB-HPS generalizes the notion of hash proof system (HPS) to the identity-based setting, almost immediately yields an identity-based encryption (IBE) scheme which is secure against partial leakage of the target identity&#x27;s decryption key. That is, an IBE scheme with bounded leakage resilience can be naturally created from an IB-HPS. However, in the real world, the leakage is unbounded, and any adversary can break the security of cryptography shceme by performing continuous leakage attacks. To further increase the practicability, a cryptography scheme must hold the claimed security in the continuous leakage setting. <PERSON><PERSON> et al. in FOCS 2010, showed a generic method how to create a cryptography shceme with continuous leakage resilience from the bounded leakage-resilient cryptosystem by performing an additional key update algorithm while the public parameters keep unchanged. To construct a continuous leakage-resilient cryptography scheme, a new primitive, called it updatable identity-based hash proof system (U-IB-HPS), is proposed, which is an improved IB-HPS. In particular, the improved system has an additional key update algorithm, which can push some new randomness into the private key of user (or the master secret key), the updated results are random in the adversary&#x27;s view, and the leakage of previous private key of user (or the master secret key) does not work for the updated results. However, the previous instantiations of U-IB-HPS cannot achieve the claimed security. To solve the above problems, in this paper, two instantiations of U-IB-HPS with better performance are created, and the security of proposed system is proved, in the standard model, based on the classic decisional bilinear Diffie-Hellman assumption. The corresponding IBE scheme created with our U-IB-HPS allows continuous leakage of multiple keys, i.e., continuous leakage of the master secret key and the private key of user. Additionally, our U-IB-HPS can also be employed as an underlying basic tool to build the generic construction of continuous leakage-amplified public-key encryption scheme, continuous leakage-resilient identity-based authenticated key exchange protocol, and continuous leakage-resilient public-key encryption scheme with keyword search, etc.", "Keywords": "Continuous Leakage Resilience ; Identity-based Hash Proof System ; Identity-based Cryptography ; DBDH Assumption", "DOI": "10.1016/j.tcs.2019.10.031", "PubYear": 2020, "Volume": "804", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Shaanxi Normal University, Xi'an, China;State Key Laboratory of Cryptology, P.O. Box 5159, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Shaanxi Normal University, Xi'an, China;State Key Laboratory of Cryptology, P.O. Box 5159, Beijing, China;Corresponding author at: School of Computer Science, Shaanxi Normal University, Xi'an, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Shaanxi Normal University, Xi'an, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Fujian Provincial Key Laboratory of Network Security and Cryptology, College of Mathematics and Informatics, Fujian Normal University, Fuzhou, China"}], "References": []}, {"ArticleId": 78278657, "Title": "Workers’ biomechanical loads and kinematics during multiple-task manual material handling", "Abstract": "This study investigated the biomechanical loads and kinematics of workers during multiple-task manual material handling (MMH) jobs, and developed prediction models for the moments acting on a worker&#x27;s body and their peak joint angles. An experiment was conducted in which 20 subjects performed a total of 3780 repetitions of a box-conveying task. This task included continuous sequential removing, carrying and depositing of boxes weighing 2–12 kg. The subjects&#x27; motion was captured using motion-capture technology. The origin/destination height was the most influencing predictor of the spinal and shoulder moments and the peak trunk, shoulder and knee angles. The relationship between the origin/destination heights and the above parameters was nonlinear. The mass of the box, and the subject&#x27;s height and mass, also influenced the spinal and shoulder moments. A tradeoff between the moments acting on the L5/S1 vertebrae and on the shoulder joint was found. Compared to the models developed in similar studies that focused on manual material handling (albeit under different conditions), the high-order prediction equation for peak spinal moment formulated in the present study was found to explain between 10% and 48% more variability in the moments. This suggests that using a high-order equation in future studies might improve the prediction.", "Keywords": "Joint angle;Manual material handling;Shoulder moments;Spinal moments", "DOI": "10.1016/j.apergo.2019.102985", "PubYear": 2020, "Volume": "83", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, Ben-Gurion University of the Negev, Beer Sheva, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, Ben-Gurion University of the Negev, Beer Sheva, Israel;Institute of Agricultural Engineering, Agricultural Research Organization, Bet Dagan, Israel"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, Ben-Gurion University of the Negev, Beer Sheva, Israel;Corresponding author. Ben-Gurion University of the Negev, Department of Industrial Engineering and Management, P.O.B. 653, Beer Sheva, Israel."}], "References": []}, {"ArticleId": 78278672, "Title": "A 3-stage fuzzy-decision tree model for traffic signal optimization in urban city via a SDN based VANET architecture", "Abstract": "One of the main challenges for developed cities is vehicle traffic and its management. Because of physical structures and cross roads of urban intersections, traffic flows may cause time delay, congestion, traffic accidents and more fuel/time consumption. For these reasons, there are many studies on traffic management systems which are a part of smart city applications in both literature and practice. In recent years, it seems that VANET (Vehicular Ad Hoc Networks) architecture has been preferred in these studies, which enables vehicles to easily communicate with each other or devices on the edge of the road and also to transfer the related traffic data to the center. On the other hand, there are many studies on a new network paradigm called SDN (Software Defined Networks) that solves problems such as performance, programmability, scalability, security and management difficulties in today’s traditional networks. Depending on all this, in this study, we will present an architectural proposal about how to use SDN and VANET network paradigms together in traffic management systems in order to use both existing intersection management system modules more adaptively and future traffic based services to be added in desired service quality without changing the communication infrastructure. Also in this study, a 3-stage fuzzy-decision tree model was proposed for traffic signaling which is the most important problems of urban traffic by focusing environmental factors (accident situation, priority vehicle transition etc.) and activity information (road construction, meeting, train and bus schedule etc. ) not only vehicle density unlike other signaling studies in literature. The proposed model also generates/sends a dynamic flow input to SDN agent vehicles and SDN agent RSU (Road Side Unit) in intersection for making VANET routing protocols (AODV-ad hoc on-demand distance vector, DSDV-destination-sequenced distance vector) selection automatically according to traffic incident and density. In order to measure the functionality of the SDN based VANET architecture and 3 stage fuzzy-decision tree model on it for the transmission infrastructure of the traffic management, performance tests were performed on different traffic and network scenarios on Adapazar ı City Centre Model. SDN based VANET architecture was compared with traditional systems according to transmission infrastructure and also proposed 3-stage fuzzy-decision model was compared with signaling techiques in literature such as fixed-time signaling, Webster equation, ant colony algorithm and particle swarm optimization under same traffic and network scenarios. The results obtained from performance tests shows that the proposed 3-stage fuzzy-decision model has %15-17 better performance than fixed-time signaling and webster equation and also %9-11 better performance that particle swarm and ant colony optimizations in low density traffic scenarios. In high density and dynamic traffic scenarios these rates reach %20-22 compared to fixed-time signaling and webster equation and %14-15 compared to particle swarm and ant colony optimizations. In addition in this study, proposed SDN based VANET architecture was compared to traditional VANET ITS architectures within the same traffic and network scenarios. According to obtained results, it is shown that the proposed SDN based VANET has %30-40 better performance than traditional systems in end-end delay, throughput and packet loss rate criteria.", "Keywords": "Traffic management systems ; Software defined networks ; Vehicular networks ; Fuzzy logic ; SUMO ; NS-2", "DOI": "10.1016/j.future.2019.10.020", "PubYear": 2020, "Volume": "104", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "Musa Balta", "Affiliation": "Computer Engineering Department of Sakarya University, Turkey;Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Özçeli̇k", "Affiliation": "Computer Engineering Department of Sakarya University, Turkey"}], "References": []}, {"ArticleId": 78278747, "Title": "Stylized human motion warping method based on identity-independent coordinates", "Abstract": "<p>Human motion style is a vital concept to virtual human that has a great impact on the expressiveness of the final animation. This paper presents a novel technique that transfers style between heterogeneous motions in real time. Unlike previous approaches, our stylized motion warping is capable of reusing style between heterogeneous motions. The key idea of our work is to represent human motions with identity-independent coordinates (IICs) and learn relative space–time transformations between stylistically different IICs, instead of separating style from content. Once the relative space–time transformations are estimated from a small set of stylized example motions whose contents are identical, our technique is capable of generating style-controllable human motions by applying these transformations to heterogeneous motions with simple linear operations. Experimental results demonstrate that our technique is efficient and powerful in stylized human motion generation. Besides, our technique can also be used in numerous interactive applications, such as real-time human motion style control, stylizing motion graphs and style-based human motion editing. </p>", "Keywords": "Human motion simulation; Motion capture; Motion warping; Identity-independent coordinates", "DOI": "10.1007/s00500-019-04489-z", "PubYear": 2020, "Volume": "24", "Issue": "13", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Jinan, China;Shandong Provincial Key Laboratory for Distributed Computer Software Novel Technology, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information, Renmin University of China, Beijing, China"}], "References": []}, {"ArticleId": 78278825, "Title": "Coverage area enhancement in wireless sensor network", "Abstract": "<p>In the wireless sensor network, coverage area may be enhanced after an initial deployment of sensors. Though, some research works propose how to decrease the coverage hole by increasing sensing range or movement assisted sensor deployment, these are not suitable for energy constraint wireless sensor network, as longer mobility distance or higher power level consume more energy. In this paper, we address the increasing coverage area through smaller mobility of nodes. We find out the coverage hole in the monitoring region, which is not covering by any sensing disk of sensor. Then, we address the new position of mobility nodes to increase the coverage area. The simulation result shows the mobile nodes can recover the coverage hole perfectly. The coverage holes is recovered by mobility on the existing recovery area, which cannot be lost. Moreover, hole detection time in our proposed protocol is better than existing algorithm.</p>", "Keywords": "", "DOI": "10.1007/s00542-019-04674-y", "PubYear": 2020, "Volume": "26", "Issue": "5", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology, Patna, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology, Patna, India"}], "References": []}, {"ArticleId": 78279008, "Title": "Congruence-based proofs of the recognizability theorems for free many-sorted algebras", "Abstract": "<p>We generalize several recognizability theorems for free single-sorted algebras to free many-sorted algebras and provide, in a uniform way and without using either regular tree grammars or tree automata, purely algebraic proofs of them based on congruences.</p>", "Keywords": "", "DOI": "10.1093/logcom/exz032", "PubYear": 2020, "Volume": "30", "Issue": "2", "JournalId": 2581, "JournalTitle": "Journal of Logic and Computation", "ISSN": "0955-792X", "EISSN": "1465-363X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departament de Lògica i Filosofia de la Ciència, Universitat de València, Av. <PERSON><PERSON><PERSON>, 30-7a, 46010 València, Spain"}, {"AuthorId": 2, "Name": "E Cosme LlÓ<PERSON>z", "Affiliation": "Departament de Matemàtiques, Universitat de València, Dr<PERSON>, 50, 46100 Burjassot, València, Spain"}], "References": []}, {"ArticleId": 78279010, "Title": "Improved dropClust R package with integrative analysis support for scRNA-seq data", "Abstract": "Abstract Summary <p>DropClust leverages Locality Sensitive Hashing (LSH) to speed up clustering of large scale single cell expression data. Here we present the improved dropClust, a complete R package that is, fast, interoperable and minimally resource intensive. The new dropClust features a novel batch effect removal algorithm that allows integrative analysis of single cell RNA-seq (scRNA-seq) datasets.</p> Availability and implementation <p>dropClust is freely available at https://github.com/debsin/dropClust as an R package. A lightweight online version of the dropClust is available at https://debsinha.shinyapps.io/dropClust/.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btz823", "PubYear": 2020, "Volume": "36", "Issue": "6", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "SyMeC Data Center, Indian Statistical Institute, Kolkata 700108, India;Department of Computer Science & Engineering, University of Calcutta, Kolkata 700098, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Delhi Technological University, New Delhi 110042, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Delhi Technological University, New Delhi 110042, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "SyMeC Data Center, Indian Statistical Institute, Kolkata 700108, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering;Department of Computational Biology, Center for Artificial Intelligence, Indraprastha Institute of Information Technology, New Delhi 110020, India;Center for Artificial Intelligence, Indraprastha Institute of Information Technology, New Delhi 110020, India"}], "References": []}, {"ArticleId": 78279102, "Title": "Explaining Natural Language query results", "Abstract": "<p>Multiple lines of research have developed Natural Language (NL) interfaces for formulating database queries. We build upon this work, but focus on presenting a highly detailed form of the answers in NL. The answers that we present are importantly based on the provenance of tuples in the query result, detailing not only the results but also their explanations . We develop a novel method for transforming provenance information to NL, by leveraging the original NL query structure. Furthermore, since provenance information is typically large and complex, we present two solutions for its effective presentation as NL text: one that is based on provenance factorization, with novel desiderata relevant to the NL case and one that is based on summarization. We have implemented our solution in an end-to-end system supporting questions, answers and provenance, all expressed in NL. Our experiments, including a user study, indicate the quality of our solution and its scalability. </p>", "Keywords": "Provenance; CQ; UCQ; NL; Natural Language", "DOI": "10.1007/s00778-019-00584-7", "PubYear": 2020, "Volume": "29", "Issue": "1", "JournalId": 4083, "JournalTitle": "The VLDB Journal", "ISSN": "1066-8888", "EISSN": "0949-877X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Tel Aviv University, Tel Aviv, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tel Aviv University, Tel Aviv, Israel"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Tel Aviv University, Tel Aviv, Israel"}], "References": []}, {"ArticleId": 78279127, "Title": "Machine intelligence-based algorithms for spam filtering on document labeling", "Abstract": "<p>The internet has provided numerous modes for secure data transmission from one end station to another, and email is one of those. The reason behind its popular usage is its cost-effectiveness and facility for fast communication. In the meantime, many undesirable emails are generated in a bulk format for a monetary benefit called spam. Despite the fact that people have the ability to promptly recognize an email as spam, performing such task may waste time. To simplify the classification task of a computer in an automated way, a machine learning method is used. Due to limited availability of datasets for email spam, constrained data and the text written in an informal way are the most feasible issues that forced the current algorithms to fail to meet the expectations during classification. This paper proposed a novel, spam mail detection method based on the document labeling concept which classifies the new ones into ham or spam. Moreover, algorithms like Naive Bayes, Decision Tree and Random Forest (RF) are used in the classification process. Three datasets are used to evaluate how the proposed algorithm works. Experimental results illustrate that RF has higher accuracy when compared with other methods.</p>", "Keywords": "Machine learning; Spam detection; Document labeling; Feature selection", "DOI": "10.1007/s00500-019-04473-7", "PubYear": 2020, "Volume": "24", "Issue": "13", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Chandigarh University, Punjab, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ontology Engineering Group, Universidad Polytecnica de Madrid, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Texas A&M University - Kingsville, Kingsville, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Mumbai, Mumbai, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Machine Intelligence Research Labs (MIR Labs), Auburn, USA"}], "References": [{"Title": "Swarm intelligence in anomaly detection systems: an overview", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "43", "Issue": "2", "Page": "109", "JournalTitle": "International Journal of Computers and Applications"}]}, {"ArticleId": 78279770, "Title": "Little rewards, big changes: Using exercise analytics to motivate sustainable changes in physical activity", "Abstract": "Even using simple techniques like taking the stairs, many individuals struggle to maintain the motivation to be physically active. Health gamification systems can aid this goal by providing points earned through exercise that are redeemable for tangible extrinsic rewards. Using self-determination theory, we conduct research on one such system and investigate rewards’ effectiveness to promote exercise considering reward value, redemption frequency patterns, and fitness levels. We find that rewards do significantly increase activity levels, and this effect is larger for advanced users who redeem multiple times for higher value rewards. We close by offering future research avenues and advice to optimize reward portfolios.", "Keywords": "Health gamification system ; Tangible extrinsic rewards ; Reward portfolio design ; Optimize reward effectiveness ; Redemption patterns ; Fitness stage", "DOI": "10.1016/j.im.2019.103216", "PubYear": 2022, "Volume": "59", "Issue": "5", "JournalId": 2493, "JournalTitle": "Information & Management", "ISSN": "0378-7206", "EISSN": "1872-7530", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for Consumer and Organisational Data Analytics (CODA), King’s Business School, King’s College London, Bush House 30 Aldwych, London, WC2B 4BG, United Kingdom;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of San Diego School of Business, 5998 Alcala Park, San Diego, 92101, USA;Centre for Consumer and Organisational Data Analytics (CODA), King’s Business School, King’s College London, Bush House 30 Aldwych, London, WC2B 4BG, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Odette School of Business, University of Windsor, 401 Sunset Ave, N9B 3P4, Windsor, Canada;Centre for Consumer and Organisational Data Analytics (CODA), King’s Business School, King’s College London, Bush House 30 Aldwych, London, WC2B 4BG, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Centre for Consumer and Organisational Data Analytics (CODA), King’s Business School, King’s College London, Bush House 30 Aldwych, London, WC2B 4BG, United Kingdom"}], "References": []}]