import numpy as np
import time
from random import sample, random, randint

# 城市坐标生成（保持相同随机种子）
np.random.seed(42)
cities = np.random.uniform(1, 10, (100, 2))

# 向量化计算距离矩阵
delta = cities[:, np.newaxis] - cities
dist_matrix = np.sqrt((delta**2).sum(axis=2))

def calculate_distance(path):
    """计算闭环路径总距离"""
    return dist_matrix[np.roll(path, 1), path].sum()

def fitness_evaluation(individual):
    """适应度函数（取距离倒数）"""
    return 1 / calculate_distance(individual)

def tournament_selection(population, fitness, k=3):
    """锦标赛选择（k=3）"""
    selected_idx = np.random.choice(len(population), k)
    best_idx = selected_idx[np.argmax([fitness[i] for i in selected_idx])]
    return population[best_idx]

def ordered_crossover(p1, p2):
    """顺序交叉（OX）"""
    size = len(p1)
    start, end = sorted([randint(0, size-1), randint(0, size-1)])
    
    # 继承父代片段
    child = [-1]*size
    child[start:end+1] = p1[start:end+1]
    
    # 填充剩余基因
    ptr = 0
    for gene in p2:
        if gene not in child:
            while child[ptr] != -1:
                ptr += 1
            child[ptr] = gene
    return child

def swap_mutation(individual, mutation_rate=0.01):
    """交换变异"""
    if random() < mutation_rate:
        i, j = sample(range(len(individual)), 2)
        individual[i], individual[j] = individual[j], individual[i]
    return individual

def genetic_algorithm(city_num=100, pop_size=200, generations=500):
    # 初始化种群
    population = [np.random.permutation(city_num).tolist() for _ in range(pop_size)]
    best_dist = float('inf')
    best_route = None
    
    for gen in range(generations):
        # 计算适应度
        fitness = [fitness_evaluation(ind) for ind in population]
        
        # 精英保留（保留最优个体）
        elite_idx = np.argmax(fitness)
        current_best = population[elite_idx]
        current_dist = calculate_distance(current_best)
        
        if current_dist < best_dist:
            best_dist = current_dist
            best_route = current_best.copy()
        
        # 生成新一代种群
        new_pop = [best_route.copy()]  # 精英保留
        
        while len(new_pop) < pop_size:
            # 选择
            parent1 = tournament_selection(population, fitness)
            parent2 = tournament_selection(population, fitness)
            
            # 交叉
            if random() < 0.8:
                child = ordered_crossover(parent1, parent2)
            else:
                child = parent1.copy()
            
            # 变异
            child = swap_mutation(child)
            new_pop.append(child)
        
        population = new_pop
        print(f"Generation {gen+1}: Best {best_dist:.2f}")
    
    return best_route, best_dist

# 执行算法
start_time = time.time()
optimal_route, min_distance = genetic_algorithm()
print(f"\nTotal time: {time.time() - start_time:.2f}s")
print(f"Minimum distance: {min_distance:.2f}")
print("Optimal route:", optimal_route)
print('魏海东 202331060927')