{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["部门管理"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class Department:\n", "    def __init__(self,dep_name,boss_name):\n", "        self.dep_name = dep_name\n", "        self.boss_name = boss_name\n", "        self.stuff_list = []\n", "\n", "    # 添加员工\n", "    def add_stuff(self,name):\n", "        self.stuff_list.append(name)\n", "\n", "    # 删除员工\n", "    def remove_stuff(self,name):\n", "        self.stuff_list.remove(name)\n", "\n", "    # 打印信息\n", "    def print_dep_info(self):\n", "        print(\"部门：\",self.dep_name)\n", "        print(\"主管：\",self.boss_name)\n", "        print(\"员工：\",self.stuff_list)\n", "        print()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["部门： IT部\n", "主管： 小明\n", "员工： ['小五', '老六']\n", "\n", "部门： 财务部\n", "主管： 红姐\n", "员工： ['张三', '李四']\n", "\n", "部门： IT部\n", "主管： 小明\n", "员工： ['老六']\n", "\n"]}], "source": ["it_dep = Department(\"IT部\",\"小明\")\n", "finance_dep = Department(\"财务部\",\"红姐\")\n", "\n", "it_dep.add_stuff(\"小五\")\n", "it_dep.add_stuff(\"老六\")\n", "finance_dep.add_stuff(\"张三\")\n", "finance_dep.add_stuff(\"李四\")\n", "\n", "it_dep.print_dep_info()\n", "finance_dep.print_dep_info()\n", "\n", "it_dep.remove_stuff(\"小五\")\n", "it_dep.print_dep_info()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}