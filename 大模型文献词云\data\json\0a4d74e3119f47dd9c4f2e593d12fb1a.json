[{"ArticleId": 110103020, "Title": "CrowdQ: Predicting the Queue State of Hospital Emergency Department Using Crowdsensing Mobility Data-Driven Models", "Abstract": "<p>Hospital Emergency Departments (EDs) are essential for providing emergency medical services, yet often overwhelmed due to increasing healthcare demand. Current methods for monitoring ED queue states, such as manual monitoring, video surveillance, and front-desk registration are inefficient, invasive, and delayed to provide real-time updates. To address these challenges, this paper proposes a novel framework, CrowdQ, which harnesses spatiotemporal crowdsensing data for real-time ED demand sensing, queue state modeling, and prediction. By utilizing vehicle trajectory and urban geographic environment data, CrowdQ can accurately estimate emergency visits from noisy traffic flows. Furthermore, it employs queueing theory to model the complex emergency service process with medical service data, effectively considering spatiotemporal dependencies and event context impact on ED queue states. Experiments conducted on large-scale crowdsensing urban traffic datasets and hospital information system datasets from Xiamen City demonstrate the framework's effectiveness. It achieves an F1 score of 0.93 in ED demand identification, effectively models the ED queue state of key hospitals, and reduces the error in queue state prediction by 18.5%-71.3% compared to baseline methods. CrowdQ, therefore, offers valuable alternatives for public emergency treatment information disclosure and maximized medical resource allocation.</p>", "Keywords": "", "DOI": "10.1145/3610875", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics, Xiamen University, Xiamen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics, Xiamen University, Xiamen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics, Xiamen University, Xiamen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Virginia, Charlottesville, United States"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Informatics, Xiamen University, Xiamen, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Macau, Macau, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang University City College, Hangzhou, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "School of Informatics, Xiamen University, Xiamen, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics, Xiamen University, Xiamen, China"}], "References": [{"Title": "Understanding urban structures and crowd dynamics leveraging large-scale vehicle mobility data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "5", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Graph Neural Networks in IoT: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "19", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}]}, {"ArticleId": 110103161, "Title": "Efficient implementation of modern entropy stable and kinetic energy preserving discontinuous Galerkin methods for conservation laws", "Abstract": "<p>Many modern discontinuous Galerkin (DG) methods for conservation laws make use of summation by parts operators and flux differencing to achieve kinetic energy preservation or entropy stability. While these techniques increase the robustness of DG methods significantly, they are also computationally more demanding than standard weak form nodal DG methods. We present several implementation techniques to improve the efficiency of flux differencing DG methods that use tensor product quadrilateral or hexahedral elements, in 2D or 3D respectively. Focus is mostly given to CPUs and DG methods for the compressible Euler equations, although these techniques are generally also useful for other physical systems including the compressible Navier-Stokes and magnetohydrodynamics equations. We present results using two open source codes, Trixi.jl written in Julia and FLUXO written in Fortran, to demonstrate that our proposed implementation techniques are applicable to different code bases and programming languages.</p>", "Keywords": "", "DOI": "10.1145/3625559", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 14400, "JournalTitle": "ACM Transactions on Mathematical Software", "ISSN": "0098-3500", "EISSN": "1557-7295", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Applied Mathematics, University of Hamburg, Germany"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "Applied and Computational Mathematics, RWTH Aachen University, Germany and High-Performance Computing Center Stuttgart, University of Stuttgart, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computational and Applied Mathematics, Rice University, USA"}, {"AuthorId": 4, "Name": "<PERSON>-<PERSON>", "Affiliation": "Department of Mathematics and Computer Science, University of Cologne, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Computational Mathematics, Division of Applied Mathematics, Linköping University, Sweden"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Max Planck Institute for Plasma Physics, NMPP division, Germany"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Center for Data and Simulation Science, University of Cologne, Germany"}], "References": [{"Title": "Entropy-Stable, High-Order Summation-by-Parts Discretizations Without Interface Penalties", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "82", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "FLEXI: A high order discontinuous Galerkin framework for hyperbolic–parabolic conservation laws", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "81", "Issue": "", "Page": "186", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "Efficient Parallel 3D Computation of the Compressible Euler Equations with an Invariant-domain Preserving Second-order Finite-element Scheme", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Parallel Computing"}]}, {"ArticleId": 110103294, "Title": "Two New Classes of (Almost) Perfect c-Nonlinear Permutations", "Abstract": "<p>In this short note, we present two classes of (almost) perfect [Formula: see text]-nonlinear permutations over finite fields of even characteristic.</p>", "Keywords": "Finite fields; permutation polynomials; c-differential uniformity", "DOI": "10.1142/S0129054123500211", "PubYear": 2024, "Volume": "35", "Issue": "7", "JournalId": 9642, "JournalTitle": "International Journal of Foundations of Computer Science", "ISSN": "0129-0541", "EISSN": "1793-6373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Physics, University of South China, Hengyang, Hunan, 421001, P. R. <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Physics, University of South China, Hengyang, Hunan, 421001, P. R. <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Physics, University of South China, Hengyang, Hunan, 421001, P. R. <PERSON>"}], "References": []}, {"ArticleId": 110103443, "Title": "Fast Calibration for Computer Models with Massive Physical Observations", "Abstract": "Computer model calibration is a crucial step in building a reliable computer model. In the face of massive physical observations, a fast estimation of the calibration parameters is urgently needed. To alleviate the computational burden, we design a two-step algorithm to estimate the calibration parameters by employing the subsampling techniques. Compared with the current state-of-the-art calibration methods, the complexity of the proposed algorithm is greatly reduced without sacrificing too much accuracy. We prove the consistency and asymptotic normality of the proposed estimator. The form of the variance of the proposed estimation is also presented, which provides a natural way to quantify the uncertainty of the calibration parameters. The obtained results of two numerical simulations and two real-case studies demonstrate the advantages of the proposed method. © 2023 Society for Industrial and Applied Mathematics and American Statistical Association.", "Keywords": "massive data; weighted least squares calibration; optimal subsampling; Poisson sampling; 62G08; 62M30; 62M40", "DOI": "10.1137/22M153673X", "PubYear": 2023, "Volume": "11", "Issue": "3", "JournalId": 11089, "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification", "ISSN": "", "EISSN": "2166-2525", "Authors": [{"AuthorId": 1, "Name": "Shurui Lv", "Affiliation": "School of Statistics and Data Science, Faculty of Science, Beijing University of Technology, Beijing, China."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Statistics, Beijing Institute of Technology, Beijing, China."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Statistics and Data Science, Faculty of Science, Beijing University of Technology, Beijing, China."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Statistics and Data Science, Faculty of Science, Beijing University of Technology, Beijing, China."}], "References": [{"Title": "On the Improved Rates of Convergence for Matérn-Type Kernel Ridge Regression with Application to Calibration of Computer Models", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "4", "Page": "1522", "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification"}]}, {"ArticleId": 110103606, "Title": "Hybrid parameter-based PSO flexible needle percutaneous puncture path planning", "Abstract": "<p>This paper addresses the challenge of guiding a flexible needle from its entry point to a target location through an obstacle-laden environment. To achieve this, we propose a path planning approach grounded in an enhanced particle swarm optimization algorithm (PSO). Our method uses the number and angle of needle rotations to govern the flexible needle's movement. By optimizing for parameters such as path length, distance from obstacles, and needle rotation, we transform the flexible needle puncture path planning problem into a multi-objective optimization task. The optimal path is determined by assigning distinct weights to these four optimization objectives and solving them using the improved PSO algorithm. To demonstrate the effectiveness and practicality of our approach, we conduct MATLAB simulations and compare the results with the unimproved PSO algorithm and the rapidly-exploring random trees algorithm.</p>", "Keywords": "Path planning; Flexible needles; Particle swarm arithmetic", "DOI": "10.1007/s11227-023-05661-x", "PubYear": 2024, "Volume": "80", "Issue": "4", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medicine Information and Engineering, Xuzhou Medical University, Xuzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medicine Information and Engineering, Xuzhou Medical University, Xuzhou, China; Corresponding author."}], "References": []}, {"ArticleId": 110103611, "Title": "Breast lesions segmentation and classification in a two-stage process based on Mask-RCNN and Transfer Learning", "Abstract": "<p>The most prevalent malignancy of concern among women is breast cancer. Early detection plays a crucial role in improving survival chances. However, the current reliance on mammography for breast cancer detection has limitations due to the delicate nature of breast tissue, human visual system constraints, and variations in accumulated experience, leading to significant false positive and false negative results. This study aims to minimize these errors by developing an intelligent computer-based diagnosis method for breast cancer utilizing digital mammography, employing the Transfer Learning approach. The proposed technique involves two stages. In the first stage, we fine-tune the pre-trained Mask R-CNN model on the COCO dataset to identify and segment breast lesions. In the second stage, various convolutional Deep Learning models such as ResNet101, ResNet34, VGG16, VGG19, AlexNet, and DenseNet121 classify the segmented lesions as benign or malignant. The lesion detection and segmentation stages achieved an average precision of 96.26%, while breast lesion classification using the DenseNet121 model obtained 99.44% accuracy on the INbreast dataset. An additional benefit of this study is the development of a new dataset extracted from the INbreast dataset, containing solely lesion images. This novel dataset reduces storage capacity and computational complexity during Deep neural network training and testing as it avoids the use of entire images. Moreover, the lesion dataset holds potential for use in breast cancer diagnosis research and may be integrated into an advanced computer-assisted diagnostic system for breast cancer screening.</p>", "Keywords": "Breast Cancer; Lesion Detection and Classification; Deep Learning; Transfer Learning; Mask-RCNN", "DOI": "10.1007/s11042-023-16895-5", "PubYear": 2024, "Volume": "83", "Issue": "12", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "LAMIS Laboratory, <PERSON><PERSON><PERSON><PERSON> Tbessi University, Tebessa, Algeria; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LAMIS Laboratory, <PERSON><PERSON><PERSON><PERSON>arbi Tbessi University, Tebessa, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LAMIS Laboratory, <PERSON><PERSON><PERSON><PERSON>arbi Tbessi University, Tebessa, Algeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "LAMIS Laboratory, <PERSON><PERSON><PERSON><PERSON>arbi Tbessi University, Tebessa, Algeria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Staffordshire University, Stoke-On-Trent, UK"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "National University of Science and Technology (NUST), Islamabad, Pakistan; The Alan Turing Institute, London, UK"}], "References": [{"Title": "Images data practices for Semantic Segmentation of Breast Cancer using Deep Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "11", "Page": "15227", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Fastai: A Layered API for Deep Learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "108", "JournalTitle": "Information"}, {"Title": "Mammogram breast cancer CAD systems for mass detection and classification: a review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "14", "Page": "20043", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 110103640, "Title": "PoseSonic", "Abstract": "<p>In this paper, we introduce PoseSonic, an intelligent acoustic sensing solution for smartglasses that estimates upper body poses. Our system only requires two pairs of microphones and speakers on the hinges of the eyeglasses to emit FMCW-encoded inaudible acoustic signals and receive reflected signals for body pose estimation. Using a customized deep learning model, PoseSonic estimates the 3D positions of 9 body joints including the shoulders, elbows, wrists, hips, and nose. We adopt a cross-modal supervision strategy to train our model using synchronized RGB video frames as ground truth. We conducted in-lab and semi-in-the-wild user studies with 22 participants to evaluate PoseSonic, and our user-independent model achieved a mean per joint position error of 6.17 cm in the lab setting and 14.12 cm in semi-in-the-wild setting when predicting the 9 body joint positions in 3D. Our further studies show that the performance was not significantly impacted by different surroundings or when the devices were remounted or by real-world environmental noise. Finally, we discuss the opportunities, challenges, and limitations of deploying PoseSonic in real-world applications.</p>", "Keywords": "", "DOI": "10.1145/3610895", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Cornell University, Ithaca, NY, USA"}], "References": [{"Title": "Winect", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "EarIO", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "GoPose", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "BodyTrak", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 110103649, "Title": "Abacus Gestures", "Abstract": "<p>Designing an extensive set of mid-air gestures that are both easy to learn and perform quickly presents a significant challenge. Further complicating this challenge is achieving high-accuracy detection of such gestures using commonly available hardware, like a 2D commodity camera. Previous work often proposed smaller, application-specific gesture sets, requiring specialized hardware and struggling with adaptability across diverse environments. Addressing these limitations, this paper introduces Abacus Gestures, a comprehensive collection of 100 mid-air gestures. Drawing on the metaphor of Finger Abacus counting, gestures are formed from various combinations of open and closed fingers, each assigned different values. We developed an algorithm using an off-the-shelf computer vision library capable of detecting these gestures from a 2D commodity camera feed with an accuracy exceeding 98% for palms facing the camera and 95% for palms facing the body. We assessed the detection accuracy, ease of learning, and usability of these gestures in a user study involving 20 participants. The study found that participants could learn Abacus Gestures within five minutes after executing just 15 gestures and could recall them after a four-month interval. Additionally, most participants developed motor memory for these gestures after performing 100 gestures. Most of the gestures were easy to execute with the designated finger combinations, and the flexibility in executing the gestures using multiple finger combinations further enhanced the usability. Based on these findings, we created a taxonomy that categorizes Abacus Gestures into five groups based on motor memory development and three difficulty levels according to their ease of execution. Finally, we provided design guidelines and proposed potential use cases for Abacus Gestures in the realm of mid-air interaction.</p>", "Keywords": "", "DOI": "10.1145/3610898", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Pennsylvania State University, University Park, Pennsylvania, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Pennsylvania State University, University Park, Pennsylvania, USA"}], "References": [{"Title": "Designing Mid-Air Haptic Gesture Controlled User Interfaces for Cars", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "EICS", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 110103676, "Title": "Symbolic control for stochastic systems via finite parity games", "Abstract": "We consider the problem of computing the maximal probability of satisfying an ω -regular specification for stochastic, continuous-state, nonlinear systems evolving in discrete time. The problem reduces, after automata-theoretic constructions, to finding the maximal probability of satisfying a parity condition on a (possibly hybrid) state space. While characterizing the exact satisfaction probability is open, we show that a lower bound on this probability can be obtained by (I) computing an under-approximation of the qualitative winning region , i.e., states from which the parity condition can be enforced almost surely, and (II) computing the maximal probability of reaching this qualitative winning region. The heart of our approach is a technique to symbolically compute the under-approximation of the qualitative winning region in step (I) via a finite-state abstraction of the original system as a 2 1 2 - player parity game . Our abstraction procedure uses only the support of the probabilistic evolution; it does not use precise numerical transition probabilities. We prove that the winning set in the abstract 2 1 2 -player game induces an under-approximation of the qualitative winning region in the original synthesis problem, along with a policy to solve it. By combining these contributions with (a) a symbolic fixpoint algorithm to solve 2 1 2 -player games and (b) existing techniques for reachability policy synthesis in stochastic nonlinear systems, we get an abstraction-based algorithm for finding a lower bound on the maximal satisfaction probability. We have implemented the abstraction-based algorithm in Mascot-SDS , where we combined the outlined abstraction step with our tool <PERSON><PERSON> (<PERSON><PERSON><PERSON> et al., 2023) that solves 2 1 2 -player parity games (through a reduction to <PERSON>bin games) more efficiently than existing algorithms. We evaluated our implementation on the nonlinear model of a perturbed bistable switch from the literature. We show empirically that the lower bound on the winning region computed by our approach is precise, by comparing against an over-approximation of the qualitative winning region. Moreover, our implementation outperforms a recently proposed tool for solving this problem by a large margin.", "Keywords": "Correct-by-design controller synthesis ; Controlled Markov processes ; Stochastic dynamical systems ; ω-regular specifications", "DOI": "10.1016/j.nahs.2023.101430", "PubYear": 2024, "Volume": "51", "Issue": "", "JournalId": 5866, "JournalTitle": "Nonlinear Analysis: Hybrid Systems", "ISSN": "1751-570X", "EISSN": "1878-7460", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Max Planck Institute for Software Systems, Kaiserslautern, 67663, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Science and Technology Austria (ISTA), Klosterneuburg, 3400, Austria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Max Planck Institute for Software Systems, Kaiserslautern, 67663, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Newcastle University, NE4 5TG, United Kingdom;Corresponding author"}], "References": [{"Title": "Set Propagation Techniques for Reachability Analysis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "1", "Page": "369", "JournalTitle": "Annual Review of Control, Robotics, and Autonomous Systems"}, {"Title": "Symbolic Qualitative Control for Stochastic Systems via Finite Parity Games", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "5", "Page": "127", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Abstraction-based synthesis for stochastic systems with omega-regular objectives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "45", "Issue": "", "Page": "101204", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}]}, {"ArticleId": 110103775, "Title": "Development and Validation of a Positive-Item Version of the Visual Aesthetics of Websites Inventory: The VisAWI-Pos", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2023.2258634", "PubYear": 2024, "Volume": "40", "Issue": "20", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center for General Psychology and Methodology, University of Basel, Basel, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center for General Psychology and Methodology, University of Basel, Basel, Switzerland"}, {"AuthorId": 3, "Name": "Marimo Honda", "Affiliation": "Center for General Psychology and Methodology, University of Basel, Basel, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Center for General Psychology and Methodology, University of Basel, Basel, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for General Psychology and Methodology, University of Basel, Basel, Switzerland"}], "References": [{"Title": "Is It Time to Go Positive? Assessing the Positively Worded System Usability Scale (SUS)", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "6", "Page": "987", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Quick Assessment of Web Content Perceptions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "68", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "What Causes the Dependency between Perceived Aesthetics and Perceived Usability?", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "6", "Page": "78", "JournalTitle": "International Journal of Interactive Multimedia and Artificial Intelligence"}, {"Title": "Heywood cases: possible causes and solutions", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "1", "Page": "79", "JournalTitle": "International Journal of Data Analysis Techniques and Strategies"}, {"Title": "Farsi Version of Visual Aesthetics of Website Inventory (FV-VisAWI): Translation and Psychometric Evaluation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "4", "Page": "834", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "An Arabic Version of the Visual Aesthetics of Websites Inventory (AR-VisAWI): Translation and Psychometric Properties", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "14", "Page": "2785", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 110103890, "Title": "Impact of SAR-based vegetation attributes on the SMAP high-resolution soil moisture product", "Abstract": "NASA&#x27;s Soil Moisture Active Passive (SMAP) mission employs L-band radiometer observations with a spatial resolution of ~33 km to produce global soil moisture products at various spatial resolutions and regular intervals. Among these products is the SMAP-Sentinel active-passive high-resolution soil moisture, available at spatial resolutions of 1 km and 3 km. The SMAP-Sentinel product combines data from the SMAP L-band radiometer and the Copernicus Sentinel-1A and Sentinel-1B C-band Synthetic Aperture Radar (SAR) observations. The SMAP-Sentinel high-resolution soil moisture product enables various hydrological and agricultural applications that were previously beyond the capability of coarser resolution radiometer-only products. However, the assessment of SMAP-Sentinel product over SMAP core validation sites (CVS) has revealed high errors and biases, particularly in agricultural regions. We suspect that the discrepancy may be related to the vegetation attribute parameters (e.g., vegetation optical depth, τ) used in the SMAP-Sentinel baseline Single-Channel Algorithm (SCA) microwave emission model (i.e., τ-ω model) for soil moisture retrievals, which might be out of sync due to reliance on NDVI climatology in the τ estimation. In this study, we leveraged and evaluated the advantage of using vegetation attribute information inherently available through the SMAP ratiometer and Sentinel- 1A/1B SAR backscatter as an alternative to NDVI climatology-derived τ in the SCA to enhance soil moisture retrievals. The SAR cross-polarization ( vh ) backscatter potentially contains vegetation attribute information that can be correlated with the coarse resolution τ derived from SMAP observations. Therefore, we hypothesized that including Sentinel- 1A/1B vh observations to derive high-resolution (1 km and 3 km) τ, and subsequently incorporating it into the SCA, can significantly improve the accuracy of the SMAP-Sentinel soil moisture product. We established a statistical relationship between Sentinel- 1A/1B SAR backscatter and the SMAP Dual-Channel Algorithm (DCA)-based τ retrieval at ~33 km, for a specific region and different landcover classes at a global extent, using seven years of observations (2015–2022). Further, we developed an algorithm to downscale the SMAP DCA τ of ~33 km to 1 km and 3 km resolutions based on the developed statistical relationships, enabling us to capture the actual temporal variability in vegetation attributes (i.e., τ) at higher resolutions. The downscaled τ values were then incorporated into the SCA to retrieve SMAP-Sentinel high-resolution (1 km and 3 km) soil moisture. The validation analysis of high-resolution soil moisture retrievals demonstrates that the inclusion of the downscaled τ approach significantly improved the performance of the SMAP-Sentinel product by reducing both bias and error standard deviation compared to the retrievals based on τ derived from the NDVI climatology.", "Keywords": "Active-passive approach ; Downscaling algorithm ; NDVI climatology ; SMAP ; SAR ; High-resolution soil moisture ; SMAP-Sentinel ; Vegetation optical depth", "DOI": "10.1016/j.rse.2023.113826", "PubYear": 2023, "Volume": "298", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Michigan State University, MI, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Michigan State University, MI, USA;Department of Biosystem and Agricultural Engineering, Michigan State University, MI, USA;Corresponding author at: Department of Civil and Environmental Engineering, Michigan State University, MI, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "NASA Jet Propulsion Laboratory, California Institute of Technology, CA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Massachusetts Institute of Technology, Cambridge, MA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "NASA Jet Propulsion Laboratory, California Institute of Technology, CA, USA"}], "References": [{"Title": "Global-scale assessment and inter-comparison of recently developed/reprocessed microwave satellite vegetation optical depth products", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "253", "Issue": "", "Page": "112208", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "SMOS-IC data record of soil moisture and L-VOD: Historical development, applications and perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "254", "Issue": "", "Page": "112238", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "From field observations to temporally dynamic soil surface roughness retrievals in the U.S. Corn Belt", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "287", "Issue": "", "Page": "113458", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Evaluating the saturation effect of vegetation indices in forests using 3D radiative transfer simulations and satellite observations", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "295", "Issue": "", "Page": "113665", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 110103968, "Title": "Application of Fuzzy and Conventional Forecasting Techniques to Predict Energy Consumption in Buildings", "Abstract": "<p>This paper presents the implementation and analysis of two approaches (fuzzy and conventional). Using hourly data from buildings at the University of Granada, we have examined their electricity demand and designed a model to predict energy consumption. Our proposal was conducted with the aid of time series techniques as well as the combination of artificial neural networks and clustering algorithms. Both approaches proved to be suitable for energy modelling although nonfuzzy models provided more variability and less robustness than fuzzy ones. Despite the relatively small difference between fuzzy and nonfuzzy estimates, the results reported in this study show that the fuzzy solution may be useful to enhance and enrich energy predictions.</p>", "Keywords": "", "DOI": "10.1155/2023/4391555", "PubYear": 2023, "Volume": "2023", "Issue": "1", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Granada, Department of Computer Science and Artificial Intelligence, Granada, Spain"}, {"AuthorId": 2, "Name": "L. G. B<PERSON> Ruiz", "Affiliation": "University of Granada, Department of Computer Science and Artificial Intelligence, Granada, Spain;Pablo de Olavide University, Division of Computer Science, School of Engineering, Seville, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Granada, Department of Computer Science and Artificial Intelligence, Granada, Spain"}, {"AuthorId": 4, "Name": "C. D. Barranco", "Affiliation": "Pablo de Olavide University, Division of Computer Science, School of Engineering, Seville, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "University of Granada, Department of Computer Science and Artificial Intelligence, Granada, Spain"}], "References": [{"Title": "Big data time series forecasting based on pattern sequence similarity and its application to the electricity demand", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; F<PERSON>", "PubYear": 2020, "Volume": "540", "Issue": "", "Page": "160", "JournalTitle": "Information Sciences"}, {"Title": "Coronavirus Optimization Algorithm: A Bioinspired Metaheuristic Based on the COVID-19 Propagation Model", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; J. F. Torres", "PubYear": 2020, "Volume": "8", "Issue": "4", "Page": "308", "JournalTitle": "Big Data"}, {"Title": "Deep Learning for Time Series Forecasting: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "3", "JournalTitle": "Big Data"}, {"Title": "An intelligent nonintrusive load monitoring scheme based on 2D phase encoding of power signals", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "1", "Page": "72", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "The emergence of explainability of intelligent systems: Delivering explainable and personalized recommendations for energy efficiency", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "2", "Page": "656", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A dual‐stage attention‐based Conv‐LSTM network for spatio‐temporal correlation and multivariate time series prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "5", "Page": "2036", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Smart power consumption abnormality detection in buildings using micromoments and improved K‐nearest neighbors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "6", "Page": "2865", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Electricity production and consumption modeling through fuzzy logic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "11", "Page": "8348", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Piecewise forecasting of nonlinear time series with model tree dynamic Bayesian networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "11", "Page": "9108", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 110104031, "Title": "BCSoM: Blockchain-based certificateless aggregate signcryption scheme for Internet of Medical Things", "Abstract": "The integration of fog computing and blockchain in the Internet of Medical Things (IoMT) domain has wholly transformed the healthcare industry and e-health services. In IoMT, the patient’s personal health data is shared on an open channel that makes the IoMT system an easy target for attackers. Therefore, confidentiality and authentication are the key security requirements for an IoMT system. Although few works have been proposed to provide secure communication in conventional healthcare infrastructures, limited work has been done to achieve data confidentiality and authentication in blockchain-based IoMT under fog environment. Therefore, this work proposes a blockchain-assisted certificateless aggregate signcryption called BCSoM scheme that achieves data confidentiality and device/patient authentication. In the proposed work, first, IoMT devices generate a signcrypted text and transmit it to an aggregator that generates an aggregated signcrypted text and sends it to a receiver fog server. The receiver fog server verifies the signcrypted text through the blockchain and also performs unsigncryption to retrieve the original message. The proposed scheme is proven secure under the Discrete Logarithm (DL) and Computational Diffe–Hellman (CDH) assumptions. Finally, a thorough performance analysis using the Hyperledger Fabric platform and cryptographic libraries shows that the proposed scheme is computationally more efficient when compared to existing works.", "Keywords": "", "DOI": "10.1016/j.comcom.2023.09.027", "PubYear": 2023, "Volume": "212", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology (ISM), Dhanbad, 826004, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology (ISM), Dhanbad, 826004, India"}], "References": [{"Title": "A blockchain-based attribute-based signcryption scheme to secure data sharing in the cloud", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "101653", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "An efficient and practical certificateless signcryption scheme for wireless body area networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "169", "JournalTitle": "Computer Communications"}, {"Title": "An efficient signcryption of heterogeneous systems for Internet of Vehicles", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "101885", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "A secure and lightweight certificateless hybrid signcryption scheme for Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "23", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Internet of Things and Blockchain Integration: Security, Privacy, Technical, and Design Challenges", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "7", "Page": "216", "JournalTitle": "Future Internet"}]}, {"ArticleId": 110104073, "Title": "Miniaturized Neural Observation System for in vivo Brain Imaging in Freely Moving Rats", "Abstract": "In this study, we developed a miniaturized observation system for rat brain imaging using an implantable imager. A small data-acquisition device was designed to be mounted on the back of an adult rat, reducing the disturbance to the output of the imager caused by a long cable. With a weight of approximately 6.8 g, the movement of adult rats with an average weight of 200 g was unrestricted. The readout characteristics were evaluated at the light level. The in vivo experiments were performed using mice and freely moving rats. Using the developed data analysis pipeline, blood flow in the vessels was observed. ISSN 0914-4935 © MYU K.K.", "Keywords": "brain surface imaging; CMOS image sensor; implantable device; in vivo imaging", "DOI": "10.18494/SAM4563", "PubYear": 2023, "Volume": "35", "Issue": "9", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Ronnakorn Siwadamrongpong", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}], "References": []}, {"ArticleId": 110104082, "Title": "Implantable Multimodal Sensing Device for Simultaneous Imaging and Electrophysiological Recording of Mouse Brain Activity", "Abstract": "Optical and electrophysiological measurements help us understand mouse brain functions. One type of device available for optical measurements is an implantable complementary metal-oxide-semiconductor (CMOS) image sensor (ICIS). However, an ICIS alone cannot directly measure the electrical signals emitted by mouse brain neurons. Considering this limitation, we have developed an implantable multimodal sensor that can simultaneously make optical and electrophysiological measurements of the neural activity in the brains of mice. The proposed device integrates a CMOS image sensor and a neural amplifier with a recording electrode into a single chip, making it no more invasive than a conventional implantable CMOS image sensor. The proposed device is based on a 0.35-μm CMOS standard process and occupies an area of 0.50 × 5.0 mm2. Furthermore, a hybrid filter is fabricated on the imaging pixel array to remove the excitation light and selectively detect fluorescence. From electrophysiological measurements, we confirm that the neural amplifier features a mid-band gain of 39 dB from 500 mHz to 4 kHz, which is the bandwidth that includes local field and action potentials. Crosstalk noise is observed because of the digital signal used to control the image sensor. However, in vivo experiments demonstrate that the device is capable of simultaneously measuring and processing optical and electrophysiological signals when the amplitude spectrum has a peak of less than 1 μV. ISSN 0914-4935 © MYU K.K.", "Keywords": "CMOS image sensor; implantable device; multimodal sensor; neural recording", "DOI": "10.18494/SAM4264", "PubYear": 2023, "Volume": "35", "Issue": "9", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan; Department of Health Science, Faculty of Medical Sciences, Kyushu University, 3-1-1, Maidashi, Higashi-ku, Fukuoka, 812-8582, Japan"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Division of Materials Science, Graduate School of Science and Technology, Nara Institute of Science and Technology, 8916-5 Takayama, Nara, Ikoma, 630-0192, Japan"}], "References": []}, {"ArticleId": 110104150, "Title": "User Interaction Within Online Innovation Communities: A Social Network Analysis", "Abstract": "<p>In the digital era, enterprises have established online innovation communities to attract customers to participate. Presented in this study is user interactions within these communities using social network analysis. By identifying distinct subgroups within the network and comparing the user interactions among these subgroups, this research aims to identify the group diversity of online interactions. The findings indicate that dialogists are more willing to interact and hold a favorable network position, followed by questioners, while answerers have the lowest level of interaction. User subgroups are identified using k-core analysis. The higher the value of the core k, the more interactions between users in the k-core subgroup and the better the network position. By combining both ego-centered and group dimensions of online interaction characteristics, this paper also outlines an investigation into an empirical study on the influence of user interactions on community recognition. The results confirm heterogeneous effects among different subgroups.</p>", "Keywords": "", "DOI": "10.4018/IJWSR.330988", "PubYear": 2023, "Volume": "20", "Issue": "1", "JournalId": 33589, "JournalTitle": "International Journal of Web Services Research", "ISSN": "1545-7362", "EISSN": "1546-5004", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Feng", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Determinants of Individual Knowledge Innovation Behavior", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "1", "JournalTitle": "Journal of Organizational and End User Computing"}]}, {"ArticleId": 110104170, "Title": "MR Object Identification and Interaction: Fusing Object Situation Information from Heterogeneous Sources", "Abstract": "<p>The increasing number of objects in ubiquitous computing environments creates a need for effective object detection and identification mechanisms that permit users to intuitively initiate interactions with these objects. While multiple approaches to such object detection -- including through visual object detection, fiducial markers, relative localization, or absolute spatial referencing -- are available, each of these suffers from drawbacks that limit their applicability. In this paper, we propose ODIF, an architecture that permits the fusion of object situation information from such heterogeneous sources and that remains vertically and horizontally modular to allow extending and upgrading systems that are constructed accordingly. We furthermore present BLEARVIS, a prototype system that builds on the proposed architecture and integrates computer-vision (CV) based object detection with radio-frequency (RF) angle of arrival (AoA) estimation to identify BLE-tagged objects. In our system, the front camera of a Mixed Reality (MR) head-mounted display (HMD) provides a live image stream to a vision-based object detection module, while an antenna array that is mounted on the HMD collects AoA information from ambient devices. In this way, BLEARVIS is able to differentiate between visually identical objects in the same environment and can provide an MR overlay of information (data and controls) that relates to them. We include experimental evaluations of both, the CV-based object detection and the RF-based AoA estimation, and discuss the applicability of the combined RF and CV pipelines in different ubiquitous computing scenarios. This research can form a starting point to spawn the integration of diverse object detection, identification, and interaction approaches that function across the electromagnetic spectrum, and beyond.</p>", "Keywords": "", "DOI": "10.1145/3610879", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of St. Gallen, St. Gallen, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Trento, Trento, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Trento, Trento, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of St. Gallen, St. Gallen, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of St. Gallen, St. Gallen, Switzerland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "TU Braunschweig, Braunschweig, Germany"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of St. Gallen, St. Gallen, Switzerland"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Trento, Trento, Italy"}], "References": [{"Title": "Trends in Smart Manufacturing: Role of Humans and Industrial Robots in Smart Factories", "Authors": "<PERSON>n <PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "2", "Page": "35", "JournalTitle": "Current Robotics Reports"}, {"Title": "Towards Indistinguishable Augmented Reality", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Telelife: The Future of Remote Living", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "147", "JournalTitle": "Frontiers in Virtual Reality"}, {"Title": "The Eye in Extended Reality: A Survey on Gaze Interaction and Eye Tracking in Head-worn Extended Reality", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "BLEselect", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "A self-learning mean optimization filter to improve bluetooth 5.1 AoA indoor positioning accuracy for ship environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>se<PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "3", "Page": "59", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}]}, {"ArticleId": 110104171, "Title": "Diagnosing Medical Score Calculator Apps", "Abstract": "<p>Mobile medical score calculator apps are widely used among practitioners to help make decisions regarding patient treatment and diagnosis. Errors in score definition, input, or calculations can result in severe and potentially life-threatening situations. Despite these high stakes, there has been no systematic or rigorous effort to examine and verify score calculator apps. We address these issues via a novel, interval-based score checking approach. Based on our observation that medical reference tables themselves may contain errors (which can propagate to apps) we first introduce automated correctness checking of reference tables. Specifically, we reduce score correctness checking to partition checking (coverage and non-overlap) over score parameters' ranges. We checked 12 scoring systems used in emergency, intensive, and acute care. Surprisingly, though some of these scores have been used for decades, we found errors in 5 score specifications: 8 coverage violations and 3 non-overlap violations. Second, we design and implement an automatic, dynamic analysis-based approach for verifying score correctness in a given Android app; the approach combines efficient, automatic GUI extraction and app exploration with partition/consistency checking to expose app errors. We applied the approach to 90 Android apps that implement medical score calculators. We found 23 coverage violations in 11 apps; 32 non-overlap violations in 12 apps, and 16 incorrect score calculations in 16 apps. We reported all findings to developers, which so far has led to fixes in 6 apps.</p>", "Keywords": "", "DOI": "10.1145/3610912", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "New Jersey Institute of Technology, Newark, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "New Jersey Institute of Technology, Newark, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "New Jersey Institute of Technology, Newark, USA"}], "References": []}, {"ArticleId": 110104201, "Title": "A computational approach towards food-wine recommendations", "Abstract": "Food-wine pairing is an essential study in the culinary world and requires extensive research of the underlying food and wine pairing principles. To understand the principles of pairing, we must understand the characteristics of food, and wine, the interaction between them, and certain classic food-wine pairing norms that are religiously adhered to. To take this understanding to a broader class of masses, there is a need for a recommender system that considers all the trivial and non-trivial details of food-wine pairing and the preferences of individual users to provide the perfect pairing. To apply these pairing principles in the context of recommender systems, we require abstract features such as flavor, aroma, major ingredients, type of meat, and many others. To achieve this, our recommender system relies on text mining techniques and sentiment analysis at its core to extract the relevant information from the text in the dataset. Such extensive feature engineering has not been undertaken before in the context of generating food and wine recommendations. We have created a recommender system that embodies the age-old essence of wine paring, considering not only the food wine characteristics but also the user’s preference of pairing (congruent or contrast).", "Keywords": "", "DOI": "10.1016/j.eswa.2023.121766", "PubYear": 2024, "Volume": "238", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Big Data Analytics and Web Intelligence Laboratory, Department of Computer Science and Engineering, Delhi Technological University Delhi 110042, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Big Data Analytics and Web Intelligence Laboratory, Department of Computer Science and Engineering, Delhi Technological University Delhi 110042, India;Corresponding author"}], "References": []}, {"ArticleId": 110104273, "Title": "Hybrid integrated decision-making model for operating system based on complex intuitionistic fuzzy and soft information", "Abstract": "The major objective of this study is to create innovative and useful concepts to address challenging real-world decision-making issues. The assessment of the importance of a specific element in a process could be always important, but regarding the importance of the operating system (OS), in a computer system, is critical due to its pivotal role in contemporary human life, enabling the efficient and seamless execution of multiple tasks for various users. The proposed methodology integrates two theoretical concepts, namely complex intuitionistic fuzzy sets (CIFS) and soft sets (SS). Furthermore, we aim at assessing the significance of the OS by an innovative technique of complex intuitionistic fuzzy soft relations (CIFSRs), that consists of merging two complex intuitionistic fuzzy soft sets (CIFSSs), the Cartesian product (CP) of the CIFSRs notions can be determined. Hence, the proposed fuzzy-based structures are utilized to model problems related to the OS, which is accountable for managing the memory and operations of computer systems, including their hardware and software. It facilitates users in performing tasks with ease. The CIFSRs offer a comprehensive structure as they consider the membership degree (MD) and non-membership degree (NMD) aspects of objects in complex numbers. Moreover, score functions are established for the proposed CIFSRs to facilitate effective decision-making processes. Finally, a comparative analysis section presents a comprehensive and structured analysis of existing works concerning the proposed research.", "Keywords": "Complex intuitionistic fuzzy soft set ; Complex intuitionistic fuzzy soft relation ; Operating system ; Decision making", "DOI": "10.1016/j.ins.2023.119592", "PubYear": 2023, "Volume": "651", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Software, Korea National University of Transportation, Chungju 27469, Republic of Korea"}, {"AuthorId": 2, "Name": "Jeonghwan G<PERSON>", "Affiliation": "Department of Software, Korea National University of Transportation, Chungju 27469, Republic of Korea;Department of Biomedical Engineering, Korea National University of Transportation, Chungju 27469, Republic of Korea;Department of AI Robotics Engineering, Korea National University of Transportation, Chungju 27469, Republic of Korea;Department of IT & Energy Convergence (BK21 FOUR), Korea National University of Transportation, Chungju 27469, Republic of Korea;Correspondence to: <PERSON><PERSON>, Department of Software, Korea National University of Transportation, Chungju 27469, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Mathematics, Lebanese American University, Byblos, Lebanon;College of Engineering, Yuan Ze University, Taiwan;Department of Operations Research and Statistics, Faculty of Organizational Sciences, University of Belgrade, 1100 Belgrade, Serbia;Correspondence to: <PERSON><PERSON>, Department of Operations Research and Statistics, Faculty of Organizational Sciences, University of Belgrade, 1100 Belgrade, Serbia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Computer Science Department, University of Jaén, 23007 Jaén, Spain"}], "References": [{"Title": "Novel aggregation operators and ranking method for complex intuitionistic fuzzy sets and their applications to decision-making process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "5", "Page": "3595", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A new expert system in prediction of lung cancer disease based on fuzzy soft sets", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "18", "Page": "14179", "JournalTitle": "Soft Computing"}, {"Title": "Enhancing PROMETHEE method with intuitionistic fuzzy soft sets", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "7", "Page": "1071", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "An advanced uncertainty measure using fuzzy soft sets: Application to decision-making problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "2", "Page": "94", "JournalTitle": "Big Data Mining and Analytics"}, {"Title": "A new decision-making model using complex intuitionistic fuzzy Hamacher aggregation operators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "10", "Page": "7059", "JournalTitle": "Soft Computing"}, {"Title": "Approximations of fuzzy soft sets by fuzzy soft relations with image processing application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "10", "Page": "6915", "JournalTitle": "Soft Computing"}, {"Title": "A regret-theory-based three-way decision method with a priori probability tolerance dominance relation in fuzzy incomplete information systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "89", "Issue": "", "Page": "382", "JournalTitle": "Information Fusion"}, {"Title": "The intuitionistic fuzzy concept-oriented three-way decision model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "619", "Issue": "", "Page": "52", "JournalTitle": "Information Sciences"}, {"Title": "Some neighborhood-related fuzzy covering-based rough set models and their applications for decision making", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "621", "Issue": "", "Page": "799", "JournalTitle": "Information Sciences"}, {"Title": "Unsupervised multilayer fuzzy neural networks for image clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "622", "Issue": "", "Page": "682", "JournalTitle": "Information Sciences"}, {"Title": "Discrete choice models with Atanassov-type intuitionistic fuzzy membership degrees", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "622", "Issue": "", "Page": "46", "JournalTitle": "Information Sciences"}, {"Title": "A novel fuzzy hierarchical fusion attention convolution neural network for medical image super-resolution reconstruction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "622", "Issue": "", "Page": "424", "JournalTitle": "Information Sciences"}, {"Title": "Granular approximations: A novel statistical learning approach for handling data inconsistency with respect to a fuzzy relation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "629", "Issue": "", "Page": "249", "JournalTitle": "Information Sciences"}, {"Title": "Implementation of art therapy assisted by the internet of medical things based on blockchain and fuzzy set theory", "Authors": "Xiaofeng Lu", "PubYear": 2023, "Volume": "632", "Issue": "", "Page": "776", "JournalTitle": "Information Sciences"}, {"Title": "A multi-objective sustainable financial portfolio selection approach under an intuitionistic fuzzy framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "646", "Issue": "", "Page": "119379", "JournalTitle": "Information Sciences"}]}, {"ArticleId": *********, "Title": "Ergonomic Chair Design as a Solution to Musculoskeletal Disorders among Traditional Cobblers: An Anthropometric Study", "Abstract": "This study was undertaken with the aim of engineering an ergonomic chair tailored specifically for cobblers in the Malang Regency, taking into account anthropometric measurements, the Nordic body map, and the Rapid Upper Limb Assessment method. Musculoskeletal disorders, often resulting from prolonged sitting and incorrect posture, represent significant occupational hazards for them. To mitigate these health risks, the study employed the analysis of Nordic body maps to quantify complaints based on body points, and determined anthropometric dimensions to guide the design of ergonomically correct chairs. The aspiration is that the findings of this study will diminish the prevalence of musculoskeletal disorders, while enhancing the productivity and work quality of cobblers. Cobblers are particularly susceptible to musculoskeletal disorders in the waist and left calf areas, along with the back, hips, right thigh, right calf, left foot, and right foot. Insights gleaned from the Nordic body maps analysis and Rapid Upper Limb Assessment method demonstrated the need for modifications to the traditional cobbler chair to reduce these risks. Consequently, an innovative ergonomic chair was designed, incorporating anthropometric dimensions and several distinctive features, including an adjustable tilt angle on the backrest, a height-adjustable seat base, and a seat base that conforms to the load conditions of the buttocks and thighs. © 2023 Lavoisier. All rights reserved.", "Keywords": "cobblers; ergonomic chair; musculoskeletal disorders; Nordic body maps; Rapid Upper Limb Assessment", "DOI": "10.18280/jesa.560419", "PubYear": 2023, "Volume": "56", "Issue": "4", "JournalId": 14230, "JournalTitle": "Journal Européen des Systèmes Automatisés", "ISSN": "1269-6935", "EISSN": "2116-7087", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Industrial Engineering Study Program, National Institute of Technology Malang, Malang, 65152, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Industrial Engineering Study Program, Kadiri University, Kediri, 64115, Indonesia"}, {"AuthorId": 3, "Name": "Iftitah Ruwana", "Affiliation": "Industrial Engineering Study Program, National Institute of Technology Malang, Malang, 65152, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Industrial Engineering Study Program, National Institute of Technology Malang, Malang, 65152, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Industrial Engineering Study Program, National Institute of Technology Malang, Malang, 65152, Indonesia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Industrial Engineering Study Program, National Institute of Technology Malang, Malang, 65152, Indonesia"}], "References": []}, {"ArticleId": 110104291, "Title": "Advances in Natural Language Processing A Thorough Examination", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2023.12822", "PubYear": 2023, "Volume": "12", "Issue": "8", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Dr. <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 110104311, "Title": "An intelligent approach for anomaly detection in credit card data using bat optimization algorithm", "Abstract": "<p>As technology advances, many people are utilising credit cards to purchase their necessities, and the number of credit card scams is increasing tremendously. However, illegal card transactions have been on the rise, costing financial institutions millions of dollars each year. The development of efficient fraud detection techniques is critical in reducing these deficits, but it is difficult due to the extremely unbalanced nature of most credit card datasets. As compared to conventional fraud detection methods, the proposed method will help in automatically detecting the fraud, identifying hidden correlations in data and reduced time for verification process. This is achieved by selecting relevant and unique features by using Bat Optimization Algorithm (BOA). Next, balancing is performed in the highly imbalanced credit card fraud dataset using Synthetic Minority over-sampling technique (SMOTE). Then finally the CNN model for anomaly detection in credit card data is built using full center loss function to improve fraud detection performance and stability. The proposed model is tested with Kaggle dataset and yields around 99% accuracy.</p>", "Keywords": "Credit card anomaly detection; feature selection; imbalanced data; loss function; neural network; optimization", "DOI": "10.4114/intartif.vol26iss72pp202-222", "PubYear": 2023, "Volume": "26", "Issue": "72", "JournalId": 16278, "JournalTitle": "INTELIGENCIA ARTIFICIAL", "ISSN": "1137-3601", "EISSN": "1988-3064", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Mepco Schlenk Engineering College, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Applications, National Institute of Technology, Trichy, India"}, {"AuthorId": 3, "Name": "Suseandhiran N", "Affiliation": "Department of Information Technology, Mepco Schlenk Engineering College, India"}, {"AuthorId": 4, "Name": "Manikandan B", "Affiliation": "Department of Information Technology, Mepco Schlenk Engineering College, India"}], "References": []}, {"ArticleId": 110104495, "Title": "Conversion of Roadway Noise to Electrical Energy: An Innovative Approach for Sustainable Energy Generation", "Abstract": "Roadway noise is the collective sound energy emanating from motor vehicles. It consists chiefly on road surfaces, tire, engine/transmission, aerodynamic, and braking elements. Noise of rolling tires driving on pavement is found to be the biggest contributor of highway noise and increases with higher vehicle speeds. This study explores the use of a rather unconventional form of energy (Sound). An application is proposed for the same in which a distinctly designed circuitry is used to convert the sound produced by a loudspeaker. Based on the law of electromagnetic induction, the vibrations produced by the speaker can be converted into electrical energy. The use of sound energy is both clean and unconventional. It is an entire paradigm shift from the concept of noise cancellation to a new idea of noise utilization. This paper takes a step forward in this direction, using sound as a source of energy to provide a viable electronic source in a vehicle, converting the sound waves into electrical energy indicator used to power streetlight. The result of this study shows the relationship between the spring displacement and the DC voltage generated, and the relationship between the sound source and the generated voltage. It follows that the relationship is directly proportional, and a 95 dB sound generated of 1.3V, which is regulated by using an XL4016 8A regulator to maintain a constant voltage of 2V required to lighten the LED indicator used to power the prototype streetlight implemented in this study. © 2023 Lavoisier. All rights reserved.", "Keywords": "acoustic; automation; embedded system; energy conversion; vibration", "DOI": "10.18280/jesa.560415", "PubYear": 2023, "Volume": "56", "Issue": "4", "JournalId": 14230, "JournalTitle": "Journal Européen des Systèmes Automatisés", "ISSN": "1269-6935", "EISSN": "2116-7087", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical & Mechatronics Engineering, Afe Babalola University, Ado E<PERSON>, 23401, Nigeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical & Mechatronics Engineering, Afe Babalola University, Ado E<PERSON>, 23401, Nigeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical & Mechatronics Engineering, Afe Babalola University, Ado E<PERSON>, 23401, Nigeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical & Mechatronics Engineering, Afe Babalola University, Ado E<PERSON>, 23401, Nigeria"}], "References": []}, {"ArticleId": 110104496, "Title": "Iris: Passive Visible Light Positioning Using Light Spectral Information", "Abstract": "<p>We propose a novel Visible Light Positioning (VLP) method, called Iris, that leverages light spectral information (LSI) to localize individuals in a completely passive manner. This means that the user does not need to carry any device, and the existing lighting infrastructure remains unchanged. Our method uses a background subtraction approach to accurately detect changes in ambient LSI caused by human movement. Furthermore, we design a Convolutional Neural Network (CNN) capable of learning and predicting user locations from the LSI change data.</p><p>To validate our approach, we implemented a prototype of Iris using a commercial-off-the-shelf light spectral sensor and conducted experiments in two typical real-world indoor environments: a 25 m2 one-bedroom apartment and a 13.3m × 8.4m office space. Our results demonstrate that Iris performs effectively in both artificial lighting at night and in highly dynamic natural lighting conditions during the day. Moreover, Iris outperforms the state-of-the-art passive VLP techniques significantly in terms of localization accuracy and the required density of light sensors.</p><p>To reduce the overhead associated with multi-channel spectral sensing, we develop and validate an algorithm that can minimize the required number of spectral channels for a given environment. Finally, we propose a conditional Generative Adversarial Network (cGAN) that can artificially generate LSI and reduce data collection effort by 50% without sacrificing localization accuracy.</p>", "Keywords": "", "DOI": "10.1145/3610913", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "Ji<PERSON><PERSON>", "Affiliation": "University of New South Wales and CSIRO, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of New South Wales and CSIRO, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Cambridge, United of Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of New South Wales, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of New South Wales, Australia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "CSIRO, Australia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of New South Wales, Australia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "AUC and Alexandria University, Egypt"}], "References": [{"Title": "PassiveVLP", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Internet of Things"}, {"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "DAFI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": *********, "Title": "ProxiFit", "Abstract": "<p>Although many works bring exercise monitoring to smartphone and smartwatch, inertial sensors used in such systems require device to be in motion to detect exercises. We introduce ProxiFit, a highly practical on-device exercise monitoring system capable of classifying and counting exercises even if the device stays still. Utilizing novel proximity sensing of natural magnetism in exercise equipment, ProxiFit brings (1) a new category of exercise not involving device motion such as lower-body machine exercise, and (2) a new off-body exercise monitoring mode where a smartphone can be conveniently viewed in front of the user during workouts. ProxiFit addresses common issues of faint magnetic sensing by choosing appropriate preprocessing, negating adversarial motion artifacts, and designing a lightweight yet noise-tolerant classifier. Also, application-specific challenges such as a wide variety of equipment and the impracticality of obtaining large datasets are overcome by devising a unique yet challenging training policy. We evaluate ProxiFit on up to 10 weight machines (5 lower- and 5 upper-body) and 4 free-weight exercises, on both wearable and signage mode, with 19 users, at 3 gyms, over 14 months, and verify robustness against user and weather variations, spatial and rotational device location deviations, and neighboring machine interference.</p>", "Keywords": "", "DOI": "10.1145/3610920", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "POSTECH, Pohang, Gyeongbuk, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "POSTECH, Pohang, Gyeongbuk, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "POSTECH, Pohang, Gyeongbuk, South Korea"}, {"AuthorId": 4, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "POSTECH, Pohang, Gyeongbuk, South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "POSTECH, Pohang, Gyeongbuk, South Korea"}], "References": [{"Title": "SEARE: A System for Exercise Activity Recognition and Quality Evaluation Based on Green Sensing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "3", "Page": "752", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Towards a Complete Set of Gym Exercises Detection Using Smartphone Sensors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}, {"Title": "Deep learning approaches for workout repetition counting and validation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "259", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "AI-to-Human Actuation: Boosting Unmodified AI's Robustness by Proactively Inducing Favorable Human Sensing Conditions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 110104540, "Title": "Improved capsule networks based on Nash equilibrium for malicious code classification", "Abstract": "With the cutting-edge technology and artificial intelligence, various types of malware have seriously attacked cyberspace, thus relying on the deep learning to maintain high accuracy on malware classification was the solution. After the malware binaries are changed into grayscale images as network inputs, most of the existing methods deal with the observed substantial visual similarities in image texture for malware as if they were from the same family. In addition, the patterns among the different families should include exclusive features, which are prerequisite for malicious code classification. However, the previous methods do not focus on the feature extraction. To solve this problem, we propose an improved capsule network based on the Nash equilibrium for malicious code classification. From the perspective of game theory, extracting of the exclusive features can be viewed as a noncooperative game through a novel dynamic routing embedded with the Nash equilibrium process in the proposed method. The three most recent datasets are used in the evaluation period. Five indicators are calculated to test the general performance and ability to distinguish the malware categories between the Nash capsule networks, traditional capsule network and CNN. Experiments show that the classification effect of the proposed method is better than that of the traditional machine learning methods. CCS CONCEPTS: Computing Methodologies, Malware Classification, Nash Equilibrium, Machine learning, Deep Learning, Capsule Network", "Keywords": "", "DOI": "10.1016/j.cose.2023.103503", "PubYear": 2024, "Volume": "136", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Information Science and Technology University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Telecommunication & Information Branch of State Grid Corporation of China, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software & Microelectronics, Peking University, China"}], "References": [{"Title": "A Multi-Perspective malware detection approach through behavioral fusion of API call sequence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "102449", "JournalTitle": "Computers & Security"}, {"Title": "S-DCNN: stacked deep convolutional neural networks for malware classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "21", "Page": "30997", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 110104578, "Title": "Federated few-shot learning for cough classification with edge devices", "Abstract": "<p>Automatically classifying cough sounds is one of the most critical tasks for the diagnosis and treatment of respiratory diseases. However, collecting a huge amount of labeled cough dataset is challenging mainly due to high laborious expenses, data scarcity, and privacy concerns. In this work, our aim is to develop a framework that can effectively perform cough classification even in situations when enormous cough data is not available, while also addressing privacy concerns. Specifically, we formulate a new problem to tackle these challenges and adopt few-shot learning and federated learning to design a novel framework, termed F2LCough, for solving the newly formulated problem. We illustrate the superiority of our method compared with other approaches on COVID-19 Thermal Face & Cough dataset, in which F2LCough achieves an average F1-Score of 86%. Our results show the feasibility of few-shot learning combined with federated learning to build a classification model of cough sounds. This new methodology is able to classify cough sounds in data-scarce situations and maintain privacy properties. The outcomes of this work can be a fundamental framework for building support systems for the detection and diagnosis of cough-related diseases.</p>", "Keywords": "Attention mechanism; Cough classification; Deep neural network; Federated learning; Few-shot learning", "DOI": "10.1007/s10489-023-05006-4", "PubYear": 2023, "Volume": "53", "Issue": "23", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Posts and Telecommunications Institute of Technology, Hanoi, Vietnam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Thuyloi University, Hanoi, Vietnam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "VinAI Research, Hanoi, Vietnam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Posts and Telecommunications Institute of Technology, Hanoi, Vietnam"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Posts and Telecommunications Institute of Technology, Hanoi, Vietnam"}], "References": [{"Title": "Recognizing diseases with multivariate physiological signals by a DeepCNN-LSTM network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "11", "Page": "7933", "JournalTitle": "Applied Intelligence"}, {"Title": "Critical direction projection networks for few-shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "5", "Page": "5400", "JournalTitle": "Applied Intelligence"}, {"Title": "FedProLs: federated learning for IoT perception data prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "3", "Page": "3563", "JournalTitle": "Applied Intelligence"}, {"Title": "Evaluation and comparison of federated learning algorithms for Human Activity Recognition on smartphones", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "87", "Issue": "", "Page": "101714", "JournalTitle": "Pervasive and Mobile Computing"}]}, {"ArticleId": 110104586, "Title": "Expanded relative density peak clustering for image segmentation", "Abstract": "<p>The density peak clustering (DPC) is one of the most popular algorithms for segmenting images due to its simplicity and efficiency. Since DPC and its variants are not specifically designed for image segmentation, their segmentation results do not necessarily meet both subjective and objective metrics. We propose an expanded relative density-based clustering algorithm as a solution to the above problems, which can automatically determine the cluster number and make the image segmentation results more consistent with subjective criteria. First, the image is pre-segmented into superpixels using the simple linear iterative clustering algorithm, and the superpixels are represented by feature vectors containing color and texture information. Secondly, the expanded relative density of the data point is obtained by comparing the tightness of a mini-cluster with its neighboring mini-clusters. The Sigmoid function is then applied to the data point with small density but large relative distance to further adjust its relative distance so that the distribution of cluster centers matches the characteristics of the image. Next, the optimal cluster number is determined by the rate of change of the sum of squared errors. Finally, the cluster center pairs with smaller distances are merged using the cluster center merging algorithm. The experiments conducted on synthetic and real datasets demonstrate that the performance of the proposed algorithm outperforms the compared algorithms.</p>", "Keywords": "Image segmentation; Superpixel; Sigmoid function; Cluster center; Relative distance", "DOI": "10.1007/s10044-023-01195-3", "PubYear": 2023, "Volume": "26", "Issue": "4", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China"}], "References": [{"Title": "Density peak clustering based on relative density relationship", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107554", "JournalTitle": "Pattern Recognition"}, {"Title": "A new multilevel histogram thresholding approach using variational mode decomposition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "7", "Page": "11331", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A multi-stage hierarchical clustering algorithm based on centroid of tree and cut edge constraint", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "557", "Issue": "", "Page": "194", "JournalTitle": "Information Sciences"}, {"Title": "A neighborhood-based three-stage hierarchical clustering algorithm", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "21-23", "Page": "32379", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "AnalyZr: A Python application for zircon grain image segmentation and shape analysis", "Authors": "<PERSON><PERSON>; C.L<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "162", "Issue": "", "Page": "105057", "JournalTitle": "Computers & Geosciences"}, {"Title": "Density Peak Clustering with connectivity estimation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "243", "Issue": "", "Page": "108501", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 110104804, "Title": "A3GC-IP: Attention-oriented adjacency adaptive recurrent graph convolutions for human pose estimation from sparse inertial measurements", "Abstract": "Conventional methods for human pose estimation either require a high degree of instrumentation, by relying on many inertial measurement units (IMUs), or constraint the recording space, by relying on extrinsic cameras. These deficits are tackled through the approach of human pose estimation from sparse IMU data. We define attention-oriented adjacency adaptive graph convolutional long-short term memory networks (A3GC-LSTM), to tackle human pose estimation based on six IMUs, through incorporating the human body graph structure directly into the network. The A3GC-LSTM combines both spatial and temporal dependency in a single network operation, more memory efficiently than previous approaches. The recurrent graph learning on arbitrarily long sequences is made possible by equipping graph convolutions with adjacency adaptivity, which eliminates the problem of information loss in deep or recurrent graph networks, while it also allows for learning unknown dependencies between the human body joints. To further boost accuracy, a spatial attention formalism is incorporated into the recurrent LSTM cell. With our presented approach, we are able to utilize the inherent graph nature of the human body, and thus can outperform the state-of-the-art for human pose estimation from sparse IMU data.", "Keywords": "", "DOI": "10.1016/j.cag.2023.09.009", "PubYear": 2023, "Volume": "117", "Issue": "", "JournalId": 3909, "JournalTitle": "Computers & Graphics", "ISSN": "0097-8493", "EISSN": "1873-7684", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Media Informatics, Ulm University, Helmholtzstraße 16, Ulm, 89081, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Media Informatics, Ulm University, Helmholtzstraße 16, Ulm, 89081, Germany"}], "References": [{"Title": "TransPose", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 110104805, "Title": "Energy efficient resource allocation algorithms combining PSO with FLC and Taguchi method in hybrid opportunistic networks", "Abstract": "In order to reduce power consumption and delay time when mobile devices use wireless networks, this paper proposes dividing groups of mobile devices into clusters, and proposes two cluster architectures, which are dual-radio opportunistic networking for energy efficiency combining particle swarm optimization with fuzzy logic control and Taguchi by 2-hop of priority weighted round robin (DRONEE-PFT2-PWRR) and dual-radio opportunistic networking for energy efficiency combining particle swarm optimization with fuzzy logic control and Taguchi by multi-hop of priority weighted rate control (DRONEE-PFTM-PWRC). Furthermore, our proposed DRONEE-PFT2-PWRR algorithm increases cluster coverage using 2-hop and our proposed DRONEE-PFTM-PWRC algorithm improves power consumption of the system by multi-hop cluster architecture. Internal cluster communication uses Wi-Fi transmission packets , which can reduce energy consumption when a mobile device communicates with a long term evolution advanced (LTE-A) base station , and reduce interference on signals between mobile devices. In the selection of cluster heads , this paper uses particle swarm optimization (PSO) to find the best cluster head position, and the PSO fitness parameter value is adjusted using fuzzy logic control (FLC) combined with the Taguchi method . Priority weighted round robin (PWRR) architecture is proposed for LTE-A base station and cluster network resource allocation, and quality of service is introduced to give priority weight to packets according to data type . Furthermore, priority weighted rate control (PWRC) is proposed. This method determines whether the network resources of the base station meet the demands of the cluster, and then assigns weights according to the requirements of the cluster and its priorities, and finally allocates network resources according to those weights. This increases the uplink throughput of network resource allocation. Simulation results show that our proposed methods significantly outperform the DRONEE-weighted (DRONEE-W) algorithm method in terms of power consumption, network throughput, and transmission delay time.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110717", "PubYear": 2023, "Volume": "148", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National Taichung University of Science and Technology, No. 129, Sec. 3, Sanmin Rd., Taichung 404, Taiwan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National Taichung University of Science and Technology, No. 129, Sec. 3, Sanmin Rd., Taichung 404, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National Taichung University of Science and Technology, No. 129, Sec. 3, Sanmin Rd., Taichung 404, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National Taichung University of Science and Technology, No. 129, Sec. 3, Sanmin Rd., Taichung 404, Taiwan"}], "References": [{"Title": "New complex fuzzy multiple objective programming procedure for a portfolio making under uncertainty", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106607", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-task allocation with an optimized quantum particle swarm method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106603", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 110104815, "Title": "A new effective decoupling method to identify the tracking errors of the motion axes of the five-axis machine tools", "Abstract": "<p>Currently, there are demands for machine tools with higher dynamic performance as a result of their high machining accuracy and efficiency in designing complex parts in the manufacturing industry. The motion delays in each of the motion axes cause dynamic tracking errors or tool tracking errors which greatly affect the surface quality of machined parts; hence, tuning the dynamic parameters like the position gains of the motion axis causing the defect is as good as eliminating the error source rather than tuning the parameters of all the motion axes. This work proposes a decoupling method to identify the particular motion axis that greatly affects or causes the tool tracking error by using the ISO BK3 kinematic test, and this involves orientation contour error estimation and motion axis error computation based on inverse kinematics of the machine tool transformation. An experiment was carried out on an industrial machine tool with a tilting rotary table to verify the simulation results; the feed servo system model of the motion axes was constructed using MATLAB Simulink tools. The results obtained show that the tool tracking errors are greatly influenced by some dynamic parameters like the feedrate and the position gains emanating from a particular motion axis with dynamic deficiency, and the proposed decoupling method robustly identifies the individual motion axes greatly affecting the dynamic tracking error which makes it very relevant to tune only the dynamic parameters of those particular motion axes instead tuning all. Moreover, this method is simple and robust, and its implementation can help improve the dynamic performance of machine tools in the manufacturing industry.</p>", "Keywords": "Machine tool; Tracking error; Motion axis; Decoupling", "DOI": "10.1007/s10845-023-02220-2", "PubYear": 2024, "Volume": "35", "Issue": "7", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, University of Electronic Science and Technology of China, Chengdu, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 4, "Name": "Qicheng Ding", "Affiliation": "AVIC Chengdu Aircraft Industrial (Group) Company Limited, Chengdu, China"}], "References": [{"Title": "Dynamic error of CNC machine tools: a state-of-the-art review", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "5-6", "Page": "1869", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "All position-dependent geometric error identification for rotary axes of five-axis machine tool using double ball bar", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "5-6", "Page": "1351", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Dynamic performance test under complicated motion states for five-axis machine tools based on double ballbar", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "3-4", "Page": "765", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 110104903, "Title": "Sistem Informasi Manajemen Audit PT. Trio Motor Banjarmasin (Studi Kasus : Audit Part)", "Abstract": "<p><PERSON><PERSON><PERSON> ini bertujuan membangun Sistem Informasi Manajemen Audit (SIMA) di PT. Trio Motor Banjarmasin. Masalah yang ditemui saat ini pada PT. Trio Motor Banjarmasin yaitu kerap terjadi selisih pencatatan pada stok unit dan suku cadang. Hal ini terjadi dikarenakan lemahnya pengawasan dan tidak sinkronnya data cabang dengan PT. Trio Motor. Selisih stok tersebut terjadi salah satunya karena kecurangan bagian gudang dan menyebabkan kerugian pada cabang tersebut. SIMA dirancang menggunakan UML dan dibangun dengan Framework Codeigniter serta SQL Server sebagai databasenya. Metode pengembangan sistem menggunakan model Waterfall dan pengujian menggunakan Blackbox Testing. SIMA berhasil dibangun dan dilakukan pengujian menunjukkan hasil setiap fungsinya berjalan dengan baik (berhasil). Dengan sistem yang dibangun (SIMA) proses audit menjadi lebih efektif dan efisien sebab data diambil secara otomatis lewat fitur scan dan terintegrasi dengan sistem transaksi pada bagian kasir dan gudang sehingga menjadikan adanya data yang sinkron antar bagian tersebut. Dengan demikian proses pencocokan dengan jumlah fisik unit menjadi lebih akurat disebabkan data yang telah tersedia.</p>", "Keywords": "Audit;Fraud;Management;Information System;Trio Motor", "DOI": "10.46229/jifotech.v3i2.738", "PubYear": 2023, "Volume": "3", "Issue": "2", "JournalId": 85255, "JournalTitle": "Journal of Information Technology", "ISSN": "2774-4884", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Negeri Tanah Laut"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Negeri Tanah Laut"}], "References": []}, {"ArticleId": 110105005, "Title": "Does familiarity with the attraction matter? Antecedents of satisfaction with virtual reality for heritage tourism", "Abstract": "Virtual reality (VR) is a topic of growing interest. While many researchers have identified factors that influence satisfaction with VR, additional important factors remain uninvestigated. In our research model, system quality, presence, and authenticity influence two mediating variables of enjoyment and usefulness of information. Enjoyment (a hedonic aspect of the VR experience) and usefulness of information (a utilitarian aspect), in turn, influence satisfaction, with familiarity moderating both of these relationships. PLS analysis of survey data collected in a heritage tourism context finds relationships that have not been previously identified. While system quality and authenticity are associated with enjoyment and usefulness of information, presence is positively associated only with enjoyment. Familiarity negatively moderates the relationship between enjoyment and satisfaction with VR. This study thus extends prior research on a key metaverse technology, VR, by identifying and explicating the roles of authenticity and familiarity—and also extends prior research by focusing on the hedonic and utilitarian dimensions of the VR experience.", "Keywords": "Virtual reality (VR); Metaverse; Virtual tourism; Heritage tourism; Hedonic; Utilitarian; Partial least squares (PLS)", "DOI": "10.1007/s40558-023-00273-w", "PubYear": 2024, "Volume": "26", "Issue": "1", "JournalId": 6239, "JournalTitle": "Information Technology & Tourism", "ISSN": "1098-3058", "EISSN": "1943-4294", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business Administration, American University of Sharjah, Sharjah, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Business Administration, American University of Sharjah, Sharjah, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Al Rayyan International University College, Doha, Qatar; Emirates Academy of Hospitality Management, Dubai, United Arab Emirates; Corresponding author."}], "References": [{"Title": "Experiencing immersive virtual reality in museums", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "5", "Page": "103229", "JournalTitle": "Information & Management"}, {"Title": "Understanding Users’ Engagement and Responses in 3D Virtual Reality: The Influence of Presence on User Value", "Authors": "<PERSON><PERSON><PERSON> (Fone) Pengnate; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "2", "Page": "103", "JournalTitle": "Interacting with Computers"}, {"Title": "Memory of virtual experiences: Role of immersion, emotion and sense of presence", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON> B. Cadet; <PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "102506", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 110105010, "Title": "SecMISS: Secured Medical Image Secret Sharing mechanism for smart health applications", "Abstract": "<p>Sustainable smart city initiatives significantly improve the living standards of citizens and bring significant changes in economic, environmental, and social well-being. Sustainable and remote medical services are vital attributes of modern smart health infrastructure. Smart remote health infrastructure uses interconnected devices to gather and exchange health data. In smart health infrastructure, massive quantities of health data are gathered as Personal Health Records (PHR). Clinical images form a major part of the accumulated PHR. As medical images are confidential and sensitive, secure storage and transmission are significant. Hence, different cryptographic techniques are used to enhance medical image security. The conventional cryptographic schemes are complex and generate a single cipher image corresponding to a plain image. The cipher images are stored in a single database. Such storage makes the cryptosystem weak against single-point attacks. This paper discusses a simple, keyless, and distributed secure storage mechanism for medical images called SecMISS. SecMISS uses Random Grid-based Visual Secret Sharing (RGVSS) along with super-resolution for secure encryption with distributed storage and better reconstruction. The security parameters such as SSIM ( \\(\\approx \\) 0), correlation of adjacent pixels ( \\(\\approx \\) 0), PSNR ( \\(\\le \\) 8 dB), and entropy (= 1) between share and initial image show that SecMISS provides highest level of security. Similarly, different Human Visual System (HVS) parameters between the initial and reconstructed images show an improvement in the reconstruction. Experimental results show that SecMISS outperforms the existing technique in terms of security and reconstruction. Thereby SecMISS can be efficiently used for securing medical image in sustainable smart hospitals and health infrastructures.</p>", "Keywords": "Visual Secret Sharing (VSS); Security; Super-resolution; Medical images; Smart health", "DOI": "10.1007/s00371-023-03080-w", "PubYear": 2024, "Volume": "40", "Issue": "6", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics Engineering, Pondicherry University, Pondicherry, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics Engineering, Pondicherry University, Pondicherry, India"}], "References": [{"Title": "A secure visual secret sharing (VSS) scheme with CNN-based image enhancement for underwater images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "8", "Page": "2097", "JournalTitle": "The Visual Computer"}, {"Title": "A novel fingerprint template protection and fingerprint authentication scheme using visual secret sharing and super-resolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "7", "Page": "10255", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Secured image steganography based on Catalan transform", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "9", "Page": "14495", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Robust medical image encryption based on DNA-chaos cryptosystem for secure telemedicine and healthcare applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "10", "Page": "9007", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Enhanced halftone-based secure and improved visual cryptography scheme for colour/binary Images", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "21-23", "Page": "32071", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A medical image cryptosystem using bit-level diffusion with DNA coding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "1731", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A holistic overview of deep learning approach in medical imaging", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "3", "Page": "881", "JournalTitle": "Multimedia Systems"}, {"Title": "Domain-flexible selective image encryption based on genetic operations and chaotic maps", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "3", "Page": "1057", "JournalTitle": "The Visual Computer"}, {"Title": "A survey of image encryption algorithms based on chaotic system", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "5", "Page": "1975", "JournalTitle": "The Visual Computer"}, {"Title": "Resource Optimized Selective Image Encryption of Medical Images Using Multiple Chaotic Systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "91", "Issue": "", "Page": "104546", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "An efficient chaotic image encryption scheme using simultaneous permutation–diffusion operation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "3", "Page": "1643", "JournalTitle": "The Visual Computer"}, {"Title": "Image security enhancement to medical images by RDWT-DCT-Schur decomposition-based watermarking and its authentication using BRISK features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "22", "Page": "61883", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 110105012, "Title": "Publisher Correction: Picture Fuzzy Parameterized Picture Fuzzy Soft Sets and Their Application in a Performance-Based Value Assignment Problem to Salt-and-Pepper Noise Removal Filters", "Abstract": "", "Keywords": "", "DOI": "10.1007/s40815-023-01596-w", "PubYear": 2023, "Volume": "25", "Issue": "7", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering and Natural Sciences, İstanbul Rumeli University, İstanbul, Türkiye; Department of Marine Engineering, Faculty of Maritime, Bandırma Onyedi Eylül University, Balıkesir, Türkiye"}], "References": [{"Title": "Picture Fuzzy Parameterized Picture Fuzzy Soft Sets and Their Application in a Performance-Based Value Assignment Problem to Salt-and-Pepper Noise Removal Filters", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "7", "Page": "2860", "JournalTitle": "International Journal of Fuzzy Systems"}]}, {"ArticleId": 110105037, "Title": "A product requirement development method based on multi-layer heterogeneous networks", "Abstract": "The rapid advancement of emerging technologies in the information age has resulted in intricate, diverse, and rapidly changing user requirements, thereby reducing the effectiveness of traditional requirement development approaches. This paper proposes a multi-layer heterogeneous network-based method for product requirement development to identify key requirements in the product development process and address the complex relationships among users, requirements, products, and requirement development teams. By incorporating the theory of hesitant fuzzy into the expert decision-making process, this method constructs four single-layer networks: user attention network, expert trust network, requirement correlation network, and component combination network, which assigns realistic significance to nodes in each network layer and establishes connections between different networks, forming a multi-layer heterogeneous network model for product requirement development. Furthermore, this paper defines indicators such as layer significance and node comprehensive significance and proposes a calculation method for node comprehensive significance based on biased random walk, which enables deep integration of node’s realistic significance and topological centrality. The proposed method is applied to a data experiment of product requirement development for a specific type of new energy vehicle. By analyzing the calculation results of node comprehensive significance, high-quality recommendations are provided for the development team from the perspectives of functional requirements, product components, and user groups. Moreover, comparative experiments to assess algorithm performance demonstrate that the average accuracy of the proposed algorithm is 0.95, which is significantly higher than that of the commonly used single-layer network node centrality measurement methods and recently proposed multi-layer network node centrality measurement methods. These results reflect the advancement and applicability of the proposed method in product development scenarios.", "Keywords": "Multi-layer heterogeneous networks ; Product requirement development ; Biased random walks ; Key requirement mining", "DOI": "10.1016/j.aei.2023.102184", "PubYear": 2023, "Volume": "58", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Deya Road No. 109, Changsha, 410073, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Deya Road No. 109, Changsha, 410073, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Deya Road No. 109, Changsha, 410073, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Deya Road No. 109, Changsha, 410073, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Deya Road No. 109, Changsha, 410073, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Deya Road No. 109, Changsha, 410073, China"}], "References": [{"Title": "Crowdsourcing service requirement oriented requirement pattern elicitation method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "14", "Page": "10109", "JournalTitle": "Neural Computing and Applications"}, {"Title": "IoT-enabled smart appliances under industry 4.0: A case study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101043", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Requirements elicitation methods based on interviews in comparison: A family of experiments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "126", "Issue": "", "Page": "106361", "JournalTitle": "Information and Software Technology"}, {"Title": "Dynamical mining of ever-changing user requirements: A product design and improvement perspective", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "", "Page": "101174", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Identification of multi-layer networks community by fusing nonnegative matrix factorization and topological structural information", "Authors": "Changzhou Ma; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "213", "Issue": "", "Page": "106666", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "The state-of-practice in requirements elicitation: an extended interview study at 12 companies", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "2", "Page": "273", "JournalTitle": "Requirements Engineering"}, {"Title": "Network structure and requirements crowdsourcing for OSS projects", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "4", "Page": "509", "JournalTitle": "Requirements Engineering"}, {"Title": "Info2vec: An aggregative representation method in multi-layer and heterogeneous networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "444", "JournalTitle": "Information Sciences"}, {"Title": "An approach to user knowledge acquisition in product design", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "", "Page": "101408", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "How do requirements evolve during elicitation? An empirical study combining interviews and app store analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "4", "Page": "489", "JournalTitle": "Requirements Engineering"}, {"Title": "A Quality 4.0 Model for architecting industry 4.0 systems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101801", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A new centrality ranking method for multilayer networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "", "Page": "101924", "JournalTitle": "Journal of Computational Science"}, {"Title": "Smart experience-oriented customer requirement analysis for smart product service system: A novel hesitant fuzzy linguistic cloud DEMATEL method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "101917", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 110105110, "Title": "Review and Performance Analysis of Nonlinear Model Predictive Control—Current Prospects, Challenges and Future Directions", "Abstract": "Nonlinear model predictive control (NMPC) has been recognized as an influential control strategy for intricate dynamical systems due to its superior performance over conventional linear control systems. The complexity associated with nonlinear dynamics is a recurring issue in a multitude of engineering applications, rendering the development of nonlinear models a challenging endeavor. The construction of such models, either through correlating input and output data or applying fundamental energy conservation laws, presents considerable difficulties. The absence of an effective model suitable for fundamental nonlinear processes is a marked deficiency, one that NMPCs are poised to address. NMPCs demonstrate a pronounced advantage over linear MPCs, particularly in managing the complexities and nonlinearities inherent in various systems. They exhibit efficacy in controlling nonlinear dynamics, including input/output constraints, objective functions, and computationally demanding optimization problems integral to real-time applications in process industries, power systems, and autonomous vehicular systems. This capability has prompted extensive research into nonlinear dynamics, thereby diminishing the disparity between the analysis of linear and nonlinear MPCs. This review provides a thorough examination of NMPCs, encompassing the fundamental principle, mathematical formulation, and various algorithms associated with NMPCs. A concise overview of NMPC applications, along with the challenges they pose, is also discussed. © 2023 Lavoisier. All rights reserved.", "Keywords": "applications; applications and performance analysis; control system; NMPC algorithms; nonlinear dynamics; nonlinear model predictive control", "DOI": "10.18280/jesa.560409", "PubYear": 2023, "Volume": "56", "Issue": "4", "JournalId": 14230, "JournalTitle": "Journal Européen des Systèmes Automatisés", "ISSN": "1269-6935", "EISSN": "2116-7087", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Systems and Control Engineering Department, College of Electronics Engineering, Ninevah University, Mosul, 41001, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Systems and Control Engineering Department, College of Electronics Engineering, Ninevah University, Mosul, 41001, Iraq"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Systems and Control Engineering Department, College of Electronics Engineering, Ninevah University, Mosul, 41001, Iraq"}], "References": []}, {"ArticleId": 110105183, "Title": "Economic Analysis of PV-Generator Hybrid Off-Grid Systems in Underdeveloped Indonesian Regions", "Abstract": "The escalating demand for electrical energy in both rural and urban sectors necessitates a reliable and sustainable source, as the availability of traditional fossil fuels diminishes and their contribution to global warming becomes increasingly untenable. This study thus explores an environmentally-friendly solution via the Hybrid Renewable Energy System (HRES), a convergence of multiple renewable energy sources for electricity production. The focus is on an off-grid photovoltaic-wind turbine hybrid system that harnesses solar and wind energy to meet the electrical needs of the scarcely accessible Maluku Province. A feasibility analysis is conducted using the Homer software to evaluate the system's potential. The results reveal that Tual City, with the lowest Net Present Cost (NPC) amounting to Rp. 268,439,300.00 and a Cost of Energy (COE) of Rp. 3,220.56, presents the most promising potential for development. The total electricity generated by this hybrid system is projected to reach 9,457 kWh/year, highlighting its potential as a sustainable solution to the pressing energy needs in remote regions. © 2023 Lavoisier. All rights reserved.", "Keywords": "HOMER; HRES; hybrid PV-generator; rural electrification; techno-economic analysis", "DOI": "10.18280/jesa.560401", "PubYear": 2023, "Volume": "56", "Issue": "4", "JournalId": 14230, "JournalTitle": "Journal Européen des Systèmes Automatisés", "ISSN": "1269-6935", "EISSN": "2116-7087", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Universitas Sebelas Maret, Jawa Tengah, Surakarta, 57126, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Universitas Sebelas Maret, Jawa Tengah, Surakarta, 57126, Indonesia"}, {"AuthorId": 3, "Name": "Noval Fattah Alfaiz", "Affiliation": "Department of Mechanical Engineering, Universitas Sebelas Maret, Jawa Tengah, Surakarta, 57126, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Universitas Sebelas Maret, Jawa Tengah, Surakarta, 57126, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Universitas Sebelas Maret, Jawa Tengah, Surakarta, 57126, Indonesia"}], "References": []}, {"ArticleId": 110105206, "Title": "All-sky surface and top-of-atmosphere shortwave radiation components estimation: Surface shortwave radiation, PAR, UV radiation, and TOA albedo", "Abstract": "Surface and top-of-atmosphere (TOA) shortwave radiation components are key parameters in the energy budget of the Earth-atmosphere system, which influence the global climate, ecology, hydrology, etc. However, except for total surface shortwave downward radiation (SWDR), only a few satellite missions consistently release other radiation components such as solar direct/diffuse radiation, photosynthetically active radiation (PAR), ultraviolet radiation-A/B (UVA/UVB), as well as TOA albedo, although they are indispensable for many land and ocean applications. In this study, a unified framework by combining multi-band LUT inversion and classification of the atmospheric condition is proposed, which enables the simultaneous derivation of nine surface and TOA radiation variables and emphasizes the separation of direct and diffuse components. The estimated radiation variables include total and direct components of SWDR, PAR, UVA, UVB, as well as TOA albedo. The proposed method is easy-to-use with only a few inputs and works well with reasonable accuracy. Using Moderate Resolution Imaging Spectroradiometer (MODIS) raw resolution images as test data, the surface radiation variables are validated by 80 global sites from BSRN, SURFRAD, and FLUXNET, and the instantaneous RMSEs (biases) of SWDR, SWDRdir, PAR, PARdiff, and UVB are 103.6 (0.1), 114 (−1.7), 47.8 (6.8), 32.9 (5.7), and 0.14 (0.003) W/m<sup>2</sup>, respectively, demonstrating comparable or even better accuracy compared with existing products. In particular, the estimated SWDR behaves more accurate than CERES in polar regions. Due to the lack of in-situ measurements, TOA albedo is compared with CERES TOA products and shows good agreement with R<sup>2</sup> of 0.9 and RMSE (bias) of 0.055 (−0.003). The unified framework reveals obvious advantages over existing studies in generating almost all physically consistent shortwave components in the same manner with simple inputs, implying the great potentials in globally mapping spatio-temporally continuous multiple components of shortwave radiation with a unified scheme.", "Keywords": "Shortwave downward radiation ; Photosynthetically active radiation ; Ultraviolet radiation ; TOA albedo ; Look-up table", "DOI": "10.1016/j.rse.2023.113830", "PubYear": 2023, "Volume": "298", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Geospatial Engineering and Science, Sun Yat-sen University & Southern Marine Science and Engineering Guangdong Laboratory (Zhuhai), Zhuhai 519082, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Geospatial Engineering and Science, Sun Yat-sen University & Southern Marine Science and Engineering Guangdong Laboratory (Zhuhai), Zhuhai 519082, China;Key Laboratory of Comprehensive Observation of Polar Environment (Sun Yat-sen University), Ministry of Education, Zhuhai 519082, China;Key Laboratory of Natural Resources Monitoring in Tropical and Subtropical Area of South China, Ministry of Natural Resources, Zhuhai 519082, China;Corresponding author at: School of Geospatial Engineering and Science, Sun Yat-sen University & Southern Marine Science and Engineering Guangdong Laboratory (Zhuhai), Zhuhai 519082, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Wang", "Affiliation": "School of Geospatial Engineering and Science, Sun Yat-sen University & Southern Marine Science and Engineering Guangdong Laboratory (Zhuhai), Zhuhai 519082, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences (CAS), Beijing 100101, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Geospatial Engineering and Science, Sun Yat-sen University & Southern Marine Science and Engineering Guangdong Laboratory (Zhuhai), Zhuhai 519082, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Geospatial Engineering and Science, Sun Yat-sen University & Southern Marine Science and Engineering Guangdong Laboratory (Zhuhai), Zhuhai 519082, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Geospatial Engineering and Science, Sun Yat-sen University & Southern Marine Science and Engineering Guangdong Laboratory (Zhuhai), Zhuhai 519082, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Geospatial Engineering and Science, Sun Yat-sen University & Southern Marine Science and Engineering Guangdong Laboratory (Zhuhai), Zhuhai 519082, China"}], "References": []}, {"ArticleId": 110105214, "Title": "Exponential stability of impulsive conformable fractional-order nonlinear differential system with time-varying delay and its applications", "Abstract": "This paper investigates the exponential stability of conformable fractional-order nonlinear differential systems with time-varying delay and impulses. The authors utilize the principle of comparison and the <PERSON><PERSON><PERSON><PERSON> function method to establish sufficient conditions that guarantee the exponential stability of a specific class of conformable fractional-order nonlinear differential systems. These findings extend the existing results on systems with integer-order to a certain extent. Furthermore, the paper considers the effect of impulses in the delayed system, which was not addressed in previous literature The authors also apply the criterion for exponential stability to conformable fractional-order neural networks . Finally, two numerical examples are presented to illustrate the effectiveness of the proposed results.", "Keywords": "", "DOI": "10.1016/j.neucom.2023.126845", "PubYear": 2023, "Volume": "560", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of High Performance Computing and Stochastic Information Processing, Hunan Normal University, Changsha, Hunan 410081, PR China;School of Mathematics and Statistics, Hunan Normal University, Changsha, Hunan 410081, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of High Performance Computing and Stochastic Information Processing, Hunan Normal University, Changsha, Hunan 410081, PR China;School of Mathematics and Statistics, Hunan Normal University, Changsha, Hunan 410081, PR China;Corresponding author at: School of Mathematics and Statistics, Hunan Normal University, Changsha, Hunan 410081, PR China"}], "References": [{"Title": "Stability analysis of Riemann-Liouville fractional-order neural networks with reaction-diffusion terms and mixed time-varying delays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "431", "Issue": "", "Page": "169", "JournalTitle": "Neurocomputing"}, {"Title": "New results on finite-time stability for fractional-order neural networks with proportional delay", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "442", "Issue": "", "Page": "327", "JournalTitle": "Neurocomputing"}, {"Title": "Synchronization of fractional-order spatiotemporal complex networks with boundary communication", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "450", "Issue": "", "Page": "197", "JournalTitle": "Neurocomputing"}, {"Title": "Global asymptotic stability of fractional-order complex-valued neural networks with probabilistic time-varying delays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Zhenjiang Zhao", "PubYear": 2021, "Volume": "450", "Issue": "", "Page": "311", "JournalTitle": "Neurocomputing"}, {"Title": "Exponential stability of Hopfield neural networks with conformable fractional derivative", "Authors": "<PERSON><PERSON><PERSON>; Fatma <PERSON>", "PubYear": 2021, "Volume": "456", "Issue": "", "Page": "263", "JournalTitle": "Neurocomputing"}, {"Title": "Identifiability and predictability of integer- and fractional-order epidemiological models using physics-informed neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "11", "Page": "744", "JournalTitle": "Nature Computational Science"}, {"Title": "LMI Conditions for Fractional Exponential Stability and Passivity Analysis of Uncertain Hopfield Conformable Fractional-Order Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "2", "Page": "1333", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 110105232, "Title": "Gish: a novel activation function for image classification", "Abstract": "<p>In Convolutional Neural Networks (CNNs), the selection and use of appropriate activation functions is of critical importance. It has been seen that the Rectified Linear Unit (ReLU) is widely used in many CNN models. Looking at the recent studies, it has been seen that some non-monotonic activation functions are gradually moving towards becoming the new standard to improve the performance of CNN models. It has been observed that some non-monotonic activation functions such as <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> are used to obtain successful results in various deep learning models. However, only a few of them have been widely used in most of the studies. Inspired by them, in this study, a new activation function named <PERSON><PERSON>, whose mathematical model can be represented by (y=x ot ln(2-{e}^{{-e}^{x}})) , which can overcome other activation functions with its good properties, is proposed. The variable (x) is used to contribute to a strong regulation effect of negative output. The logarithm operation is done to reduce the numerical range of the expression ((2-{e}^{{-e}^{x}})) . To present our contributions in this work, various experiments were conducted on different network models and datasets to evaluate the performance of Gish. With the experimental results, 98.7% success was achieved with the EfficientNetB4 model in the MNIST dataset, 86.5% with the EfficientNetB5 model in the CIFAR-10 dataset and 90.8% with the EfficientNetB6 model in the SVHN dataset. The obtained performances were shown to be higher than <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>gish and <PERSON>mish. These results confirm the effectiveness and performance of Gish.</p>", "Keywords": "Image classification; Gish; Nonmonotonic activation function; Convolutional neural network", "DOI": "10.1007/s00521-023-09035-5", "PubYear": 2023, "Volume": "35", "Issue": "34", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Programming Program, Department of Computer Technologies, Vocational School of Technical Sciences, Harran University, Şanlıurfa, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Harran University, Şanlıurfa, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering and Natural Science, İskenderun Technical University, İskenderun, Turkey"}], "References": [{"Title": "New activation functions for single layer feedforward neural network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Gülesen Üstündağ Şiray", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113977", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Biased ReLU neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "423", "Issue": "", "Page": "71", "JournalTitle": "Neurocomputing"}, {"Title": "PFLU and FPFLU: Two novel non-monotonic activation functions in convolutional neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "429", "Issue": "", "Page": "110", "JournalTitle": "Neurocomputing"}, {"Title": "Shape autotuning activation function", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "114534", "JournalTitle": "Expert Systems with Applications"}, {"Title": "TanhExp: A smooth activation function with high convergence speed for lightweight neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "2", "Page": "136", "JournalTitle": "IET Computer Vision"}, {"Title": "RSigELU: A nonlinear activation function for deep neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114805", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Logish: A new nonlinear nonmonotonic activation function for convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "458", "Issue": "", "Page": "490", "JournalTitle": "Neurocomputing"}, {"Title": "hyper-sinh: An accurate and reliable function from shallow to deep learning in TensorFlow and Keras", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "", "Page": "100112", "JournalTitle": "Machine Learning with Applications"}, {"Title": "Deep Learning: A Comprehensive Overview on Techniques, Taxonomy, Applications and Research Directions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "6", "Page": "420", "JournalTitle": "SN Computer Science"}, {"Title": "Activation functions in deep learning: A comprehensive survey and benchmark", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "503", "Issue": "", "Page": "92", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": *********, "Title": "A transformer-based neural ODE for dense prediction", "Abstract": "<p>Neural ordinary differential equations (ODEs) represent an emergent class of deep learning models exhibiting continuous depth. While they have shown promising results across various machine learning tasks, existing methods for dense prediction tasks have not fully harnessed their potential, often due to employing sub-optimal architectural components and limited dataset studies. To address this, our paper introduces a robust neural ODE architecture specifically tailored for dense prediction tasks and performs an extensive evaluation across a broad range of datasets. Our approach draws upon proven design elements from top-performing networks, integrating transformer blocks as core building blocks. Unique to our design is the retention of multiple concurrent representations at varying resolutions throughout the network. These representations continually exchange information, ensuring they remain updated. Our network achieves unrivaled performance in tasks such as image classification, semantic segmentation, and answer grounding. We conduct several ablation studies to shed light on the impacts of various design parameters. Our results affirm the effectiveness of our approach and its potential for further advancements in dense prediction tasks.</p>", "Keywords": "Neural ODE; Dense prediction; Transformer", "DOI": "10.1007/s00138-023-01465-4", "PubYear": 2023, "Volume": "34", "Issue": "6", "JournalId": 1175, "JournalTitle": "Machine Vision and Applications", "ISSN": "0932-8092", "EISSN": "1432-1769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Information Sciences, University of Delaware, Newark, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer and Information Sciences, University of Delaware, Newark, USA"}], "References": [{"Title": "Explaining VQA predictions using visual grounding and a knowledge base", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "103968", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 110105438, "Title": "HT2ML: An efficient hybrid framework for privacy-preserving Machine Learning using HE and TEE", "Abstract": "Outsourcing Machine Learning (ML) tasks to cloud servers is a cost-effective solution when dealing with distributed data. However, outsourcing these tasks to cloud servers could lead to data breaches. Secure computing methods, such as Homomorphic Encryption (HE) and Trusted Execution Environments (TEE), have been used to protect outsourced data. Nevertheless, HE remains inefficient in processing complicated functions (e.g., non-linear functions) and TEE (e.g., Intel SGX) is not ideal for directly processing ML tasks due to side-channel attacks and parallel-unfriendly computation. In this paper, we propose a hybrid framework integrating SGX and HE, called HT2ML , to protect user&#x27;s data and models. In HT2ML , HE-friendly functions are protected with HE and performed outside the enclave, while the remaining operations are performed inside the enclave obliviously. HT2ML leverages optimised HE matrix multiplications to accelerate HE computations outside the enclave while using oblivious blocks inside the enclave to prevent access-pattern-based attacks. We evaluate HT2ML using Linear Regression (LR) training and Convolutional Neural Network (CNN) inference as two instantiations. The performance results show that HT2ML is up to ∼11× faster than HE only baseline with 6-dimensional data in LR training. For CNN inference, HT2ML is ∼196× faster than the most recent approach (<PERSON> et al., ICDCS&#x27;21).", "Keywords": "Cloud computing ; Homomorphic encryption ; SGX enclave ; Privacy-preserving ; Machine learning", "DOI": "10.1016/j.cose.2023.103509", "PubYear": 2023, "Volume": "135", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Auckland, Auckland, 1010, New Zealand;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, National University of Defense Technology, Changsha, 410073, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Auckland, Auckland, 1010, New Zealand"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of Auckland, Auckland, 1010, New Zealand"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Monash University, Melbourne, 3800, Australia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of Auckland, Auckland, 1010, New Zealand"}], "References": [{"Title": "A distributed learning based sentiment analysis methods with Web applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "5", "Page": "1905", "JournalTitle": "World Wide Web"}]}, {"ArticleId": *********, "Title": "A novel skip connection mechanism based on channel-wise cross transformer for speech enhancement", "Abstract": "<p>The skip connection mechanism has been proven to be an effective approach for improving speech enhancement networks. By strengthening the information transfer between the encoder and the decoder, it facilitates the restoration of speech features during the up-sampling process. However, simple skip connection mechanism that directly connect corresponding layers of the encoder and decoder have several issues. Firstly, it only forces the features of the same scale to be aggregated, ignoring the potential relationships between different scales. Secondly, the shallow encoder feature contains a lot of redundant information. Studies have shown that coarse skip connections can even be detrimental to model performance in some cases. In this work, we propose a novel skip connection mechanism based on channel-wise Transformer for speech enhancement, comprising two components: multi-scale channel-wise cross fusion and channel-wise cross attention. This proposed skip connection mechanism can fuse multi-scale speech features from different levels of the encoder and effectively connect the reconstructed features to the decoder. Building on this, we propose a lightweight U-shaped network (UNet) structure called UCTNet. Experimental results show that UCTNet is comparable to other competitive models in terms of various objective speech quality metrics with only a few parameters.</p>", "Keywords": "Channel-wise cross Transformer; Skip connection; Multi-scale speech features; Speech enhancement", "DOI": "10.1007/s11042-023-16977-4", "PubYear": 2024, "Volume": "83", "Issue": "12", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Nanchang Hangkong University, Nanchang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, Guangzhou Maritime University, Guangzhou, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, Nanchang Hangkong University, Nanchang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Physics and Electronics, Shandong Normal University, Jinan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Zhaoyang Gevotai (Xin Feng) Technology Co., Ltd., Ganzhou, China"}], "References": []}, {"ArticleId": 110105463, "Title": "RIS-enhanced multi-cell downlink transmission using statistical channel state information", "Abstract": "<p>This paper considers a multi-cell downlink transmission system, where a reconfigurable intelligent surface (RIS) is deployed to support the connectivity of users in blind areas (i.e., at the cell edges). In order to reduce the instantaneous channel state information (CSI) acquisition overhead at each base station (BS), only statistical CSI is considered at both the BSs and the RIS. Particularly, each BS has only statistical CSI within its own cell. Under these assumptions, we first obtain the approximated ergodic spectral efficiency (SE) for each user. Then, we investigate the BSs beamforming and RIS phase shift joint optimization under different criteria. For the approximated ergodic weighted sum SE maximization criteria, a sub-optimal algorithm is proposed, based on complex circle manifold, to design the RIS phase shift while the statistical maximum ratio transmission beamforming is employed at each BS. To ensure fairness, we adopt the projected subgradient-based algorithm in order to optimize the approximated minimum ergodic user SE. Simulation results demonstrate that the approximated ergodic SE expression matches well with the exact expression, whilst the superiority of the weighted sum SE and fairness performance of the proposed algorithms are verified.</p>", "Keywords": "fairness transmission; RIS; statistical CSI; WSSE", "DOI": "10.1007/s11432-022-3723-5", "PubYear": 2023, "Volume": "66", "Issue": "11", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The National Communications Research Laboratory, Southeast University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The National Communications Research Laboratory, Southeast University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The National Communications Research Laboratory, Southeast University, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The National Communications Research Laboratory, Southeast University, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Wireless Innovation (CWI), Queen’s University Belfast, Belfast, UK"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "The National Communications Research Laboratory, Southeast University, Nanjing, China"}], "References": [{"Title": "Deep learning based user scheduling for massive MIMO downlink system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "8", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 110105467, "Title": "A text guided multi-task learning network for multimodal sentiment analysis", "Abstract": "Multimodal Sentiment Analysis (MSA) is an active area of research that leverages multimodal signals for affective understanding of user-generated videos. Existing research tends to develop sophisticated fusion techniques to fuse unimodal representations into multimodal representation and treat MSA as a single prediction task. However, we find that the text modality with the pre-trained model (BERT) learn more semantic information and dominates the training in multimodal models, inhibiting the learning of other modalities. Besides, the classification ability of each modality is also suppressed by single-task learning. In this paper, We propose a text guided multi-task learning network to enhance the semantic information of non-text modalities and improve the learning ability of unimodal networks. We conducted experiments on multimodal sentiment analysis datasets, CMU-MOSI, CMU-MOSEI, and CH-SIMS. The results show that our method outperforms the current SOTA method.", "Keywords": "", "DOI": "10.1016/j.neucom.2023.126836", "PubYear": 2023, "Volume": "560", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, 150001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, 150001, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, 150001, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, 150001, China"}], "References": [{"Title": "Multimodal sentiment analysis with unidirectional modality translation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "467", "Issue": "", "Page": "130", "JournalTitle": "Neurocomputing"}, {"Title": "Aspect-based sentiment analysis with component focusing multi-head co-attention networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "489", "Issue": "", "Page": "9", "JournalTitle": "Neurocomputing"}, {"Title": "Learning discriminative multi-relation representations for multimodal sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "641", "Issue": "", "Page": "119125", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 110105557, "Title": "Strain and crack growth monitoring of aluminum alloy sheet using high-sensitivity buckypaper film sensors", "Abstract": "The development of micro-nano sensor technology provides a new method for structural health monitoring . This study focuses on the application of buckypaper (BP) thin film sensors for continuous monitoring of strain and crack evolution in metallic structures. The buckypaper (BP) sensor was prepared by using commercial multi-walled carbon nanotubes (MWCNTs) through dispersion, vacuum filtration and other processes. It has the advantages of high sensitivity, wide detection range and good stability. The BP sensor also has good compatibility with epoxy resin . Thus, epoxy resin was used as the adhesive, and the BP sensor was used to monitor the strain and crack growth of the aluminum alloy sheet (2024-T3). In the structural health monitoring test, strain gauges were used for comparison. Through the electrical monitoring of the BP sensor, the strain field measured by the sensor was consistent with the measurement of the strain gauge. The BP sensor can be used to monitor the crack growth information of metal structures. The results highlight the great potential and robustness of the proposed BP thin-film sensor for online structural health monitoring of metal structures.", "Keywords": "", "DOI": "10.1016/j.sna.2023.114697", "PubYear": 2023, "Volume": "363", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Material Science and Engineering, Shenyang Aerospace University, Shenyang 110136, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Aerospace Engineering, Shenyang Aerospace University, Shenyang 110136, China"}, {"AuthorId": 3, "Name": "S<PERSON><PERSON> Lu", "Affiliation": "College of Material Science and Engineering, Shenyang Aerospace University, Shenyang 110136, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Aerospace Engineering, Shenyang Aerospace University, Shenyang 110136, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Aerospace Engineering, Shenyang Aerospace University, Shenyang 110136, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Aerospace Engineering, Shenyang Aerospace University, Shenyang 110136, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Aerospace Engineering, Shenyang Aerospace University, Shenyang 110136, China;Corresponding authors"}], "References": [{"Title": "Lifetime health monitoring of fiber reinforced composites using highly flexible and sensitive MXene/CNT film sensor", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "332", "Issue": "", "Page": "113148", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Elastic-plastic deformation and organization analysis for Al 7075 friction stir welding joints based on MXene/SWCNT sensor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; S<PERSON><PERSON> Lu", "PubYear": 2023, "Volume": "352", "Issue": "", "Page": "114203", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 110105645, "Title": "Hierarchical neural network detection model based on deep context and attention mechanism", "Abstract": "In order to improve the ability of sentence event detection in natural language processing and solve the problem of event processing caused by polysemy, an event detection model based on neural network is proposed. The model adjusts the structure to a hierarchical neural network model based on neural network, and introduces attention calculation into the internal structure to realise the correlation analysis of sentence context. The value of the model is judged through performance analysis and application test. The results show that the average harmonic value of the model in polysemy detection is 74.1%, which is higher than the existing model. The application test shows that the model can detect events for sentences in different environments. The results show that the hierarchical neural network event detection model with deep contextual representation and attention mechanism has good performance, which provides theoretical support for the development of multi event detection technology. Copyright © 2023 Inderscience Enterprises Ltd.", "Keywords": "attention mechanism; deep context; event detection; hierarchical neural network; natural language processing", "DOI": "10.1504/IJCSM.2023.133634", "PubYear": 2023, "Volume": "18", "Issue": "2", "JournalId": 33075, "JournalTitle": "International Journal of Computing Science and Mathematics", "ISSN": "1752-5055", "EISSN": "1752-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Public Courses Department, Shaanxi Polytechnic Institute, Shaanxi Province, Xianyang City, 712000, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Public Courses Department, Shaanxi Polytechnic Institute, Shaanxi Province, Xianyang City, 712000, China"}], "References": []}, {"ArticleId": 110105733, "Title": "Heterogeneous Federated Learning: State-of-the-art and Research Challenges", "Abstract": "<p>Federated learning (FL) has drawn increasing attention owing to its potential use in large-scale industrial applications. Existing FL works mainly focus on model homogeneous settings. However, practical FL typically faces the heterogeneity of data distributions, model architectures, network environments, and hardware devices among participant clients. Heterogeneous Federated Learning (HFL) is much more challenging, and corresponding solutions are diverse and complex. Therefore, a systematic survey on this topic about the research challenges and state-of-the-art is essential. In this survey, we firstly summarize the various research challenges in HFL from five aspects: statistical heterogeneity, model heterogeneity, communication heterogeneity, device heterogeneity, and additional challenges. In addition, recent advances in HFL are reviewed and a new taxonomy of existing HFL methods is proposed with an in-depth analysis of their pros and cons. We classify existing methods from three different levels according to the HFL procedure: data-level, model-level, and server-level. Finally, several critical and promising future research directions in HFL are discussed, which may facilitate further developments in this field. A periodically updated collection on HFL is available at https://github.com/marswhu/HFL_Survey.</p>", "Keywords": "", "DOI": "10.1145/3625558", "PubYear": 2024, "Volume": "56", "Issue": "3", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Wuhan University, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Wuhan University, China"}, {"AuthorId": 4, "Name": "Pong C. <PERSON>en", "Affiliation": "Department of Computer Science, Hong Kong Baptist University, China"}, {"AuthorId": 5, "Name": "Dacheng Tao", "Affiliation": "The University of Sydney, Australia"}], "References": [{"Title": "PMF", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>ing Sun", "PubYear": 2020, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Highly efficient federated learning with strong privacy preservation in cloud computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "101889", "JournalTitle": "Computers & Security"}, {"Title": "A survey on security and privacy of federated learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "619", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "End-to-end privacy preserving deep learning on multi-institutional medical imaging", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>-<PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "6", "Page": "473", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "A Comprehensive Survey of Privacy-preserving Federated Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Federated learning on non-IID data: A survey", "Authors": "<PERSON><PERSON>; Jinjin Xu; Shi<PERSON> Liu", "PubYear": 2021, "Volume": "465", "Issue": "", "Page": "371", "JournalTitle": "Neurocomputing"}, {"Title": "Adversarial interference and its mitigations in privacy-preserving collaborative machine learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "9", "Page": "749", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Hybrid differential privacy based federated learning for Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "102418", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Federated Learning for Personalized Humor Recognition", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Multi-center federated learning: clients clustering for better personalization", "Authors": "<PERSON><PERSON> Long; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "1", "Page": "481", "JournalTitle": "World Wide Web"}, {"Title": "The OARF Benchmark Suite: Characterization and Implications for Federated Learning Systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "FedProc: Prototypical contrastive federated learning on non-IID data", "Authors": "<PERSON><PERSON>g Mu; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "93", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Federated learning with superquantile aggregation for heterogeneous data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "113", "Issue": "5", "Page": "2955", "JournalTitle": "Machine Learning"}, {"Title": "Federated unsupervised representation learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "24", "Issue": "8", "Page": "1181", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}]}, {"ArticleId": *********, "Title": "Can an online scenario-based learning intervention influence preservice teachers’ self-efficacy, career intentions, and perceived fit with the profession?", "Abstract": "The purpose of this article is to explore how a brief, scalable, online scenario-based learning (SBL) intervention influences preservice teachers’ self-efficacy, career intentions, and perceived fit with the profession. A sample of 1,513 preservice teachers from a large undergraduate teacher education programme in Australia was recruited over two years to complete three SBL sessions (with four measurement points) over the course of three weeks. We conducted a series of latent change analyses to explore the patterns of change over time, with covariates including year in ITE programme, prospective teaching level, and sex. Results showed that self-efficacy, teaching commitment, and perceived fit with the profession increased after the initial SBL session, and the effect was maintained for self-efficacy and perceived fit, but not for teaching commitment. Implications for practice and further research are discussed.", "Keywords": "Improving classroom teaching ; Teacher professional development ; Teaching/learning strategies ; Self-efficacy ; Career intentions", "DOI": "10.1016/j.compedu.2023.104935", "PubYear": 2023, "Volume": "207", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of York, Heslington, York, YO10 5DD, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Education University of Hong Kong, 10 Lo Ping Rd, Ting Kok, Hong Kong"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of New South Wales, Sydney NSW 2052, Australia"}], "References": [{"Title": "The power of feedback and reflection: Testing an online scenario-based learning intervention for student teachers", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "104194", "JournalTitle": "Computers & Education"}]}, {"ArticleId": *********, "Title": "N-euro Predictor", "Abstract": "<p>Jitter and lag severely impact the smoothness and responsiveness of user experience on vision-based human-display interactive systems such as phones, TVs, and VR/AR. Current manually-tuned filters for smoothing and predicting motion trajectory struggle to effectively address both issues, especially for applications that have a large range of movement speed. To overcome this, we introduce N-euro, a residual-learning-based neural network predictor that can simultaneously reduce jitter and lag while maintaining low computational overhead. Compared to the fine-tuned existing filters, N-euro improves prediction performance by 36% and smoothing performance by 42%. We fabricated a Fish Tank VR system and an AR mirror system and conducted a user experience study (n=34) with the real-time implementation of N-euro. Our results indicate that the N-euro predictor brings a statistically significant improvement in user experience. With its validated effectiveness and usability, we expect this approach to bring a better user experience to various vision-based interactive systems.</p>", "Keywords": "", "DOI": "10.1145/3610884", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Columbia University, New York City, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Snap Inc., New York City, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Snap Inc., USA"}, {"AuthorId": 4, "Name": "Vu An Tran", "Affiliation": "Snap Inc., USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Snap Inc., USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Snap Inc., USA"}], "References": [{"Title": "3D Morphable Face Models—Past, Present, and Future", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Latency and Cybersickness: Impact, Causes, and Measures. A Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "31", "JournalTitle": "Frontiers in Virtual Reality"}, {"Title": "Latency and Cybersickness: Impact, Causes, and Measures. A Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "31", "JournalTitle": "Frontiers in Virtual Reality"}, {"Title": "Teaching American Sign Language in Mixed Reality", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "FaceSense", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 110105881, "Title": "Development of a digital maturity model for Industry 4.0 based on the technology-organization-environment framework", "Abstract": "The successful adoption of Industry 4.0 technologies by firms requires them to formulate a digital strategy and implementation roadmap. An established approach to assess firms’ needs towards digitalization is through maturity models. While there is a large number of maturity models in the literature, they present several limitations related to their generalizability and theoretical foundations. Our study aims to build and empirically validate an Industry 4.0 digital maturity model, based on the Technology-Organization-Environment framework. We conducted a systematic literature review of 55 digital maturity models, which we synthesized to create an integrated digital maturity assessment model. We tested our model through a focus group with industry experts and 24 companies from various manufacturing sectors. Our review suggests that existing digital maturity models have underestimated the relevance of the Environment dimension. Our empirical data suggests that companies often invest in digital technologies without considering critical organizational and environmental constraints.", "Keywords": "Digital maturity model ; Industry 4.0 ; Technology management ; Industrial management ; Systematic literature review ; Case research", "DOI": "10.1016/j.cie.2023.109645", "PubYear": 2023, "Volume": "185", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "INESC TEC, Centre for Enterprise Systems Engineering, Campus da FEUP, Rua Dr. <PERSON>, 4200-465 Porto, Portugal;Faculty of Engineering, University of Porto, Rua Dr. <PERSON>, 4200-465 Porto, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "INESC TEC, Centre for Enterprise Systems Engineering, Campus da FEUP, Rua Dr<PERSON>, 4200-465 Porto, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Eindhoven University of Technology, Department of Industrial Engineering & Innovation Sciences, P.O. Box 513, 5600, MB, Eindhoven, The Netherlands;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "INESC TEC, Centre for Enterprise Systems Engineering, Campus da FEUP, Rua Dr. <PERSON>, 4200-465 Porto, Portugal;Faculty of Engineering, University of Porto, Rua Dr. <PERSON>, 4200-465 Porto, Portugal"}], "References": [{"Title": "Estimating Industry 4.0 impact on job profiles and skills using text mining", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "118", "Issue": "", "Page": "103222", "JournalTitle": "Computers in Industry"}, {"Title": "A Model to Evaluate the Organizational Readiness for Big Data Adoption", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "3", "Page": "", "JournalTitle": "International Journal of Computers Communications & Control"}, {"Title": "A systematic literature review for industry 4.0 maturity modeling: state-of-the-art and future challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "11", "Page": "2957", "JournalTitle": "Kybernetes"}, {"Title": "Tracking the maturity of industry 4.0: the perspective of a real scenario", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "7-8", "Page": "2161", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Data-driven manufacturing: An assessment model for data science maturity", "Authors": "Mert Onuralp Gökalp; Ebru Gökalp; Kerem <PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "527", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "An extended technology-organization-environment framework to investigate smart manufacturing system implementation in small and medium enterprises", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "107865", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Measuring the fourth industrial revolution through the Industry 4.0 lens: The relevance of resources, capabilities and the value chain", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "138", "Issue": "", "Page": "103639", "JournalTitle": "Computers in Industry"}, {"Title": "Extension of the CCMS 2.0 maturity model towards Artificial Intelligence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "10", "Page": "293", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 110105888, "Title": "Supervised Domain Adaptation by transferring both the parameter set and its gradient", "Abstract": "A well-known obstacle in the successful implementation of deep learning-based systems to real-world problems is the performance degradation that occurs when applying a network that was trained on data collected in one domain, to data from a different domain. In this study, we focus on the Supervised Domain Adaptation (SDA) setup where we assume the availability of a small amount of labeled data from the target domain. Our approach is based on transferring the gradient history of the pre-training phase to the fine-tuning phase in addition to the parameter set to improve the generalization achieved during pre-training while fine-tuning the model. We present two schemes to transfer the gradient’s information. Mixed Minibatch Transfer Learning (MMTL) is based on using examples from both the source and target domains and Optimizer-Continuation Transfer Learning (OCTL) preserves the gradient history when shifting from training to fine-tuning. This approach is also applicable to the more general setup of transfer learning across different tasks. We show that our methods outperform the state-of-the-art at different levels of data scarcity from the target domain, on multiple datasets and tasks involving both scenery and medical images.", "Keywords": "", "DOI": "10.1016/j.neucom.2023.126828", "PubYear": 2023, "Volume": "560", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Tel-Aviv University, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Tel-Aviv University, Israel;Icahn School of Medicine, Mount Sinai, New York, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Engineering Faculty, Bar-Ilan University, Israel;Corresponding author"}], "References": [{"Title": "Deep Unsupervised Domain Adaptation: A Review of Recent Advances and Perspectives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "", "JournalTitle": "APSIPA Transactions on Signal and Information Processing"}]}, {"ArticleId": 110105905, "Title": "A new recurrent neural network based on direct discretization method for solving discrete time-variant matrix inversion with application", "Abstract": "In recent years, many researchers have worked hard to find a better way for solving discrete time-variant problems in industrial control science and automation. For example, some researchers propose RNN models to deal with such problems. Typical discrete time-variant problems, such as discrete time-variant matrix inversion , are developed from continuous time-variant problems. In the present paper, an efficient and straightforward method is proposed to solve discrete time-variant matrix inversion, note that it can skip the solving procedures of continuous time-variant problem and solves matrix inversion directly in the discrete time-variant environment. Specifically, an innovative discrete time-variant recurrent neural network (I-DT-RNN) model for dealing with discrete time-variant matrix inversion is proposed, furthermore it is mathematically founded on the second-order Taylor expansion . The theoretical analysis results of I-DT-RNN model are also presented, which proves that the proposed I-DT-RNN model has a reasonable characteristic and also shows that the proposed I-DT-RNN model has an excellent computational performance. Moreover, in the numerical experiments part, we present three different matrices as numerical experiment examples and an application of two-link robot manipulator as an industrial example for validating the practicability of the I-DT-RNN model.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119729", "PubYear": 2024, "Volume": "652", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou 225127, China;Jiangsu Province Engineering Research Center of Knowledge Management and Intelligent Service, Yangzhou University, Yangzhou 225127, China;Corresponding author at: School of Information Engineering, Yangzhou University, Yangzhou 225127, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou 225127, China;Jiangsu Province Engineering Research Center of Knowledge Management and Intelligent Service, Yangzhou University, Yangzhou 225127, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou 225127, China;Jiangsu Province Engineering Research Center of Knowledge Management and Intelligent Service, Yangzhou University, Yangzhou 225127, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology and Electrical Engineering, University of Oulu, Pentti Kaiteran katu 1, 90570 Oulu, Finland;Technology Research Center of Finland (VTT), Kaitovayla 1, 90570 Oulu, Finland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou 225127, China;Jiangsu Province Engineering Research Center of Knowledge Management and Intelligent Service, Yangzhou University, Yangzhou 225127, China"}, {"AuthorId": 6, "Name": "<PERSON>bing Sun", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou 225127, China;Jiangsu Province Engineering Research Center of Knowledge Management and Intelligent Service, Yangzhou University, Yangzhou 225127, China"}], "References": [{"Title": "Modified gradient neural networks for solving the time-varying Sylvester equation with adaptive coefficients and elimination of matrix inversion", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "379", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "A parallel computing method based on zeroing neural networks for time-varying complex-valued matrix Moore-Penrose inversion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "524", "Issue": "", "Page": "216", "JournalTitle": "Information Sciences"}, {"Title": "Prescribed-time convergent and noise-tolerant Z-type neural dynamics for calculating time-dependent quadratic programming", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "10", "Page": "5327", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Improved recurrent neural networks for solving Moore-Penrose inverse of real-time full-rank matrix", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "418", "Issue": "", "Page": "221", "JournalTitle": "Neurocomputing"}, {"Title": "Design, analysis and verification of recurrent neural dynamics for handling time-variant augmented Sylvester linear system", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "426", "Issue": "", "Page": "274", "JournalTitle": "Neurocomputing"}, {"Title": "A noise-suppressing Newton-<PERSON><PERSON><PERSON> iteration algorithm for solving the time-varying <PERSON><PERSON><PERSON><PERSON> equation and robotic tracking problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "550", "Issue": "", "Page": "239", "JournalTitle": "Information Sciences"}, {"Title": "Comprehensive study on complex-valued ZNN models activated by novel nonlinear functions for dynamic complex linear equations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "561", "Issue": "", "Page": "101", "JournalTitle": "Information Sciences"}, {"Title": "Five-step discrete-time noise-tolerant zeroing neural network model for time-varying matrix inversion with application to manipulator motion generation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "104306", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "High-order error function designs to compute time-varying linear matrix equations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "576", "Issue": "", "Page": "173", "JournalTitle": "Information Sciences"}, {"Title": "Noise-suppressing zeroing neural network for online solving time-varying matrix square roots problems: A control-theoretic approach", "Authors": "<PERSON><PERSON><PERSON> Sun; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "116272", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Convergence and robustness of bounded recurrent neural networks for solving dynamic Lyapunov equations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "588", "Issue": "", "Page": "106", "JournalTitle": "Information Sciences"}, {"Title": "Direct derivation scheme of DT-RNN algorithm for discrete time-variant matrix pseudo-inversion with application to robotic manipulator", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109861", "JournalTitle": "Applied Soft Computing"}, {"Title": "Switching-Like Event-Triggered State Estimation for Reaction–Diffusion Neural Networks Against DoS Attacks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "7", "Page": "8997", "JournalTitle": "Neural Processing Letters"}, {"Title": "Quantized neural adaptive finite-time preassigned performance control for interconnected nonlinear systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "21", "Page": "15429", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An advanced discrete‐time RNN for handling discrete time‐varying matrix inversion: Form model design to disturbance‐suppression analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "3", "Page": "607", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": 110105943, "Title": "Orthogonal Procrustes and Machine Learning: Predicting Bill of Materials errors on time", "Abstract": "In an industrial product development process, the Bill of Materials (BOM) is a hierarchical, multi-level representation of all components, parts and quantities of a product. With increasing complexity of industrial products, also BOMs become more complex and thus prone to errors, for example when the individual parts of a product are changed during the product development process. Frequently, these Bill of Materials errors have to be identified manually or by using simple, rule-based schemes. In this paper, we provide a technical background of BOMs, showing the intricacy of temporal BOMs errors in an industrial product development process. The work of other authors, which focused on association mining and tree reconciliation to detect Bill of Materials errors, is analysed. We found that there is currently no system being able to prescribe where in a Bill of Materials and when in the product development process, errors are probable to occur. Also, Machine Learning (ML) methods have not been applied yet. Based on these findings, we formalize the notions Bill of Materials and Bill of Materials errors. Furthermore, we present a deterministic distance measure for BOMS. We provide an answer to the main question of how to represent a Bill of Materials for Machine Learning tasks by solving the orthogonal Procrustes problem for dynamic, hierarchical datasets. Then, we describe an isolation forest based approach to temporal anomaly detection, which points at potential errors in a Bill of Materials at a specific timestamp. Furthermore, we apply Machine Learning and present a multi-output Multi Layer Perceptron for the prediction of temporal Bill of Materials errors. The model predicts where and at which point of time Bill of Materials errors are probable to occur, which renders it a prescriptive system. Eventually, we optimize the performance of our model using contextualization via k -means clustering. Finally, we apply our prescriptive pipeline to a real world dataset and show its superiority to existing methods using a qualitative comparison.", "Keywords": "Orthogonal Procrustes ; Bill of Materials ; k -means clustering ; Isolati on forest ; Multi-output model ; Multi Layer Perceptron", "DOI": "10.1016/j.cie.2023.109606", "PubYear": 2023, "Volume": "185", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Applied Sciences Munich, Department of Computer Science and Mathematics, Lothstr. 64, Munich, 80335, Bavaria, Germany;BMW Group Research and Development, Petuelring 130, Munich, 80809, Bavaria, Germany;Corresponding author at: BMW Group Research and Development, Petuelring 130, Munich, 80809, Bavaria, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "BMW Group Research and Development, Petuelring 130, Munich, 80809, Bavaria, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Applied Sciences Munich, Department of Computer Science and Mathematics, Lothstr. 64, Munich, 80335, Bavaria, Germany"}], "References": []}, {"ArticleId": 110105946, "Title": "Multi-objective optimization for energy-efficient flow shop scheduling problem with blocking and collision-free transportation constraints", "Abstract": "In this paper, the energy-efficient flow shop scheduling problem with blocking and collision-free transportation constraints (EFSP-BCFT) with sequence dependent setup times (SDST), scheduling of automated guided vehicles (AGV), transportation speed control and battery management is investigated. An enhanced multi-objective ant colony algorithm (EMOACA) is developed to minimize makespan and total energy consumption simultaneously. Several enhancement techniques including a novel low-high resolution search strategy, an effective AGV dispatching heuristic information and critical-path-based energy reduction improvement procedure are proposed. Extensive experiments spanning various scenarios of the problem are conducted and computational results demonstrate the effectiveness of the proposed method and enhancements to obtain high quality solutions compared to standard and state- of-the-art metaheuristics . The results also showcase the significance of considering transportation speed control, battery management and AGV idle power consumption in the modelling of the problem where better system performance can be achieved both in terms of shorter schedules and smoother transportation flow.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110884", "PubYear": 2023, "Volume": "148", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Manufacturing Engineering Laboratory of Tlemcen, University of Tlemcen, B.P.N.°230, Tlemcen, Algeria;Corresponding author"}, {"AuthorId": 2, "Name": "Fayçal Belkaid", "Affiliation": "Manufacturing Engineering Laboratory of Tlemcen, University of Tlemcen, B.P.N.°230, Tlemcen, Algeria"}], "References": [{"Title": "Resource management in decentralized industrial Automated Guided Vehicle systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "204", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Time-space network model and MILP formulation of the conflict-free routing problem of a capacitated AGV system", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "106270", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An effective discrete artificial bee colony algorithm for multi-AGVs dispatching problem in a matrix manufacturing workshop", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113675", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Modeling the electrical power and energy consumption of automated guided vehicles to improve the energy efficiency of production systems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "1-2", "Page": "481", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "An imperialist competitive algorithm with feedback for energy-efficient flexible job shop scheduling with transportation and sequence-dependent setup times", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "104307", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Low-carbon joint scheduling in flexible open-shop environment with constrained automatic guided vehicle by multi-objective particle swarm optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107695", "JournalTitle": "Applied Soft Computing"}, {"Title": "A multiobjective evolutionary algorithm for achieving energy efficiency in production environments integrated with multiple automated guided vehicles", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "243", "Issue": "", "Page": "108315", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 110105961, "Title": "An UAV and EV based mobile edge computing system for total delay minimization", "Abstract": "In this paper, we studied a collaborative mobile edge computing(MEC) system based on unmanned aerial vehicle(UAV) and electric vehicle(EV), in which the EV moves to the target area carrying the UAV to provide computing and offloading services to the user equipment(UE). We aimed at minimizing all the UE tasks delay by optimizing the task offloading ratio, the UAV hovering position, and the computing resource allocation of EV and UAV, respectively. The problem was formulated as a Nonlinear Programming(NLP) problem, and we decomposed it into EV related subproblem and UAV related subproblem by the Block-Coordinate Descent(BCD) method. For EV related subproblem, we obtained the optimal offloading ratio by making the computing time on the UAV equal to its offloading time, and proved the method is feasible. For the UAV related subproblem, we introduced the non-orthogonal multiple access(NOMA) and successive interference cancellation(SIC) techniques to improve the communication efficiency, and we obtained the optimal hovering position by using the successive convex approximation(SCA) technique twice to transfer this subproblem into a convex problem . Finally, an overall iterative algorithm was proposed. To verify the effectiveness of our algorithm, we compared our scheme with ‘PSO’, ‘GT’, ‘MC’ and benchmark algorithm.", "Keywords": "", "DOI": "10.1016/j.comcom.2023.09.025", "PubYear": 2023, "Volume": "212", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Processing of Big Data on Transportation, School of Computer and Communication Engineering, Changsha University of Science and Technology, Changsha 410004, PR China;School of Computer Science and Communication Engineering, Changsha University of Science and Technology, Changsha, Hunan, China;Corresponding author at: School of Computer Science and Communication Engineering, Changsha University of Science and Technology, Changsha, Hunan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Processing of Big Data on Transportation, School of Computer and Communication Engineering, Changsha University of Science and Technology, Changsha 410004, PR China;School of Computer Science and Communication Engineering, Changsha University of Science and Technology, Changsha, Hunan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> Yu", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Processing of Big Data on Transportation, School of Computer and Communication Engineering, Changsha University of Science and Technology, Changsha 410004, PR China;School of Computer Science and Communication Engineering, Changsha University of Science and Technology, Changsha, Hunan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Processing of Big Data on Transportation, School of Computer and Communication Engineering, Changsha University of Science and Technology, Changsha 410004, PR China;School of Computer Science and Communication Engineering, Changsha University of Science and Technology, Changsha, Hunan, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Hunan Provincial Key Laboratory of Intelligent Processing of Big Data on Transportation, School of Computer and Communication Engineering, Changsha University of Science and Technology, Changsha 410004, PR China;School of Computer Science and Communication Engineering, Changsha University of Science and Technology, Changsha, Hunan, China"}], "References": [{"Title": "An UAV-assisted mobile edge computing offloading strategy for minimizing energy consumption", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "108857", "JournalTitle": "Computer Networks"}, {"Title": "Heterogeneous UAVs assisted mobile edge computing for energy consumption minimization of the edge side", "Authors": "<PERSON><PERSON>; Linjiang Li; <PERSON>aiyan <PERSON>", "PubYear": 2022, "Volume": "194", "Issue": "", "Page": "268", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 110105974, "Title": "Empirical study on using adapters for debiased Visual Question Answering", "Abstract": "In this work, we empirically study debiased Visual Question Answering (VQA) works with Adapters. Most VQA debiasing works sacrifice in-distribution (ID) performance for the sake of out-of-distribution (OOD) performance. Hence, we explore and experiment with the use of adapters to preserve the ID performance by training only a simple adapter network to debias and recreate performance. We conduct an extensive empirical study on recent well-established VQA debiasing works and show that the entirety of the debiasing information from the proposed debiasing methods can be captured and modelled using a single fully connected layer while preserving original network performance by skipping the adapters. Through our exploration, we find that different placements of adapters are required for different debiasing techniques and show the different possibilities of using adapters for debiasing through our experiments. We believe our findings in this work open up more questions to be asked and explored for the VQA community.", "Keywords": "", "DOI": "10.1016/j.cviu.2023.103842", "PubYear": 2023, "Volume": "237", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "KAIST, <PERSON><PERSON><PERSON><PERSON> 23, <PERSON><PERSON><PERSON> 34141, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "KAIST, <PERSON><PERSON><PERSON><PERSON> 23, <PERSON><PERSON><PERSON> 34141, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "KAIST, <PERSON><PERSON><PERSON><PERSON> 23, <PERSON><PERSON><PERSON> 34141, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hanyang University, Wangsimni-ro, Seoul 04763, Republic of Korea"}, {"AuthorId": 5, "Name": "In So Kweon", "Affiliation": "KAIS<PERSON>, <PERSON><PERSON><PERSON><PERSON> 23, <PERSON><PERSON><PERSON> 34141, Republic of Korea;Corresponding author"}], "References": []}, {"ArticleId": 110105982, "Title": "Is attention all geosciences need? Advancing quantitative petrography with attention-based deep learning", "Abstract": "Recent advances in deep learning have transformed data-driven geoscientific analysis. In particular, the adoption of attention mechanism in deep learning has received considerable interest and shown impressive results that can surpass traditional convolutional neural networks (CNN) in image analysis. The application of attention-based algorithms has been fairly limited in geosciences, hence the actual value and potential of the attention mechanism to perform image analysis remain untapped. In geosciences, petrographical image analysis provides fundamental qualitative and quantitative information to characterize different rock types and their composition. The quality and accuracy of such an analysis depend on several factors, including: (i) thin section quality; (ii) image resolution, and (iii) subject matter expertise. However, with the current technology , there is a common trade-off between image resolution and field of view of microscope cameras which could potentially hinder reliable quantitative analysis. This is compounded by the time-consuming and costly analysis to obtain high-resolution images. In this study, we evaluated the performance of attention-based deep learning algorithm, Super Petrography that adopted the architecture of shifted window vision transformer , to (i) perform multi-resolution image upscaling of petrographic images and (ii) provide improved quantitative petrographic analysis. Overall, the proposed model is proven superior to other conventional methods (e.g., Bicubic and Lanczos) and even CNN-based model, showing up to 30% improvement in both peak signal-to-noise ratio and structural similarity index measure. Furthermore, we observed a more accurate quantitative petrographical analysis, including grain edge detection and segmentation from these reconstructed super resolution images. This work highlights the potential application of attention-based deep learning in advancing quantitative petrography that otherwise is not possible to achieve with traditional methods. The proposed method could help mitigate the limiting effect of low-resolution images and improve the accuracy of geological or geophysical description and interpretation such as mineral and porosity segmentation, and lithology identification.", "Keywords": "", "DOI": "10.1016/j.cageo.2023.105466", "PubYear": 2023, "Volume": "181", "Issue": "", "JournalId": 4833, "JournalTitle": "Computers & Geosciences", "ISSN": "0098-3004", "EISSN": "1873-7803", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Geosciences, College of Petroleum Engineering and Geosciences, King <PERSON> University of Petroleum and Minerals, Saudi Arabia;Centre for Integrative Petroleum Research, King F<PERSON>d University of Petroleum and Minerals, Saudi Arabia;Corresponding author. Department of Geosciences, College of Petroleum Engineering and Geosciences, King Fahd University of Petroleum and Minerals, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geosciences, College of Petroleum Engineering and Geosciences, King <PERSON> University of Petroleum and Minerals, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Li", "Affiliation": "Aramco Americas - Houston Research Center, Houston, TX, USA"}], "References": [{"Title": "Deep convolutions for in-depth automated rock typing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "104330", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 110106061, "Title": "Fast image encryption algorithm with random structures", "Abstract": "Block encryption algorithms are among the most preferred applications in cryptographic systems. Block ciphers should have accomplished some requirements for a secure communication system. They should be evaluated in terms of cryptanalysis methods for widespread usage. The aim of this paper is to introduce a new secure and fast block encryption algorithm for images. For this purpose, a new block cipher, which offers an innovative encryption structure for key generation systems and can use S-boxes with different methods, is proposed. A Dynamic S-Box is used in the algorithm for both substitution and key generation purposes. Linear and differential cryptanalysis methods were performed successfully. UACI and NPCR tests show that the proposed symmetric block cipher algorithm is compatible with image encryption systems. The 512-bit key length provides the highest security for block encryption. Additionally, information entropy test, correlation coefficients, mean-squared error, and peak signal-to-noise ratio analyses were concluded successfully. The novelty of the paper is building a cryptanalysis attack-resistant block cipher algorithm that presents a lightweight cryptographic solution for image encryption systems.", "Keywords": "Block cipher ; symmetric encryption ; S-Box ; NPCR and UACI ; image processing", "DOI": "10.1080/1206212X.2023.2260617", "PubYear": 2023, "Volume": "45", "Issue": "10", "JournalId": 4962, "JournalTitle": "International Journal of Computers and Applications", "ISSN": "1206-212X", "EISSN": "1925-7074", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering, Computer Engineering, Cankiri Karatekin University, Cankiri, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering, Electrical-Electronics Engineering, Firat University, Elazig, Turkey"}], "References": [{"Title": "Embedded implementation of multispectral satellite image encryption using a chaos-based block cipher", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "50", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "New approach for attack of permutation-based image encryption schemes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "43", "Issue": "7", "Page": "697", "JournalTitle": "International Journal of Computers and Applications"}, {"Title": "An enhanced image encryption technique combining genetic algorithm and particle swarm optimization with chaotic function", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> Pritom P<PERSON>ti", "PubYear": 2021, "Volume": "43", "Issue": "9", "Page": "960", "JournalTitle": "International Journal of Computers and Applications"}, {"Title": "Differential cryptanalysis of image cipher using block-based scrambling and image filtering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "554", "Issue": "", "Page": "145", "JournalTitle": "Information Sciences"}, {"Title": "Development of new encryption system using Brownian motion based diffusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "14", "Page": "21011", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Image encryption based on logistic chaotic systems and deep autoencoder", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "153", "Issue": "", "Page": "59", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Image Encryption Algorithm Based on Arnold Transform and Chaos Theory in the Multi-wavelet Domain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "45", "Issue": "4", "Page": "306", "JournalTitle": "International Journal of Computers and Applications"}]}, {"ArticleId": 110106124, "Title": "Collaborative Virtual Reality in Higher Education: Students' Perceptions on Presence, Challenges, Affordances, and Potential", "Abstract": "The metaverse is a network of interoperable and persistent 3-D virtual worlds where users can coexist and interact through mechanisms, such as gamification, nonfungible tokens, and cryptocurrencies. Although the metaverse is a theoretical construct today, many collaborative virtual reality (CVR) applications have emerged as potential components of the metaverse. The demand for distance learning in higher education grew rapidly with COVID-19, but it has many challenges such as low motivation, low interest, and lack of suitable environment. We investigated the suitability of CVR applications for higher education by analyzing 9 existing CVR applications and then conducting a mixed-method user study on the Spatial CVR application with 41 university students (24 females, 17 males, mean age: 23.5). The results revealed 14 challenges, 13 affordances, and 20 ideas for using CVR in higher education. Moreover, lack of instructions, cybersickness, discomfort, limited embodiment, and limited sensory stimulation were among the identified issues that may inhibit the sense of presence in Spatial. Based on the findings, we proposed 13 guidelines to facilitate providing CVR learning in higher education and concluded that CVR has some use cases in higher education to complement conventional methods. These results can be useful for researchers, developers, and educators who seek to be at the forefront of adopting CVR as a part of the metaverse to come.", "Keywords": "Affordances;challenges;collaborative learning;metaverse;presence;user study;virtual reality (VR)", "DOI": "10.1109/TLT.2023.3319628", "PubYear": 2024, "Volume": "17", "Issue": "", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Digital Media, Ajou University, Suwon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Digital Media, Ajou University, Suwon, Republic of Korea"}], "References": [{"Title": "A systematic review of immersive virtual reality applications for higher education: Design elements, lessons learned, and research agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "103778", "JournalTitle": "Computers & Education"}, {"Title": "Estimating cybersickness from virtual reality applications", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "165", "JournalTitle": "Virtual Reality"}, {"Title": "A Scoping Review of Immersive Virtual Reality in STEM Education", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "748", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "A Comparison of Procedural Safety Training in Three Conditions: Virtual Reality Headset, Smartphone, and Printed Materials", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "Higher education students' experiences and opinion about distance learning during the Covid‐19 pandemic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "6", "Page": "1682", "JournalTitle": "Journal of Computer Assisted Learning"}]}, {"ArticleId": 110106160, "Title": "Quasi-synchronization of fractional-order complex-value neural networks with discontinuous activations", "Abstract": "In this paper, under the framework of <PERSON><PERSON><PERSON><PERSON> solutions, quasi-synchronization issue of fractional-order complex-valued neural networks (FCNNs) with discontinuous activations and uncertainties is investigated. Firstly, using <PERSON><PERSON> transform and the property of <PERSON><PERSON><PERSON><PERSON> function, a novel fractional differential inequality is derived. Then combining the newly constructed inequality with delay feedback control scheme, several quasi-synchronization conditions are obtained for the considered FCNNs by means of non-decomposable method. Eventually, a numerical example is provided to substantiate validation of the proposed results.", "Keywords": "", "DOI": "10.1016/j.neucom.2023.126856", "PubYear": 2023, "Volume": "560", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi 830017, China"}, {"AuthorId": 2, "Name": "Hong-<PERSON> Li", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi 830017, China;School of Mathematics, Southeast University, Nanjing 210096, China;Corresponding author at: College of Mathematics and System Sciences, Xinjiang University, Urumqi 830017, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi 830017, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi 830017, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi 830017, China"}], "References": [{"Title": "Finite-time synchronization of fully complex-valued neural networks with fractional-order", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "373", "Issue": "", "Page": "70", "JournalTitle": "Neurocomputing"}, {"Title": "LMIs conditions to robust pinning synchronization of uncertain fractional-order neural networks with discontinuous activations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "21", "Page": "15927", "JournalTitle": "Soft Computing"}, {"Title": "Fixed-time synchronization analysis for discontinuous fuzzy inertial neural networks with parameter uncertainties", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "422", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}, {"Title": "Finite-time lag synchronization of inertial neural networks with mixed infinite time-varying delays and state-dependent switching", "Authors": "Chang<PERSON> Long; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "433", "Issue": "", "Page": "50", "JournalTitle": "Neurocomputing"}, {"Title": "Global asymptotic stability of fractional-order complex-valued neural networks with probabilistic time-varying delays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Zhenjiang Zhao", "PubYear": 2021, "Volume": "450", "Issue": "", "Page": "311", "JournalTitle": "Neurocomputing"}, {"Title": "Fixed-time synchronization of fractional-order complex-valued neural networks with time-varying delay via sliding mode control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "505", "Issue": "", "Page": "339", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 110106174, "Title": "Cross-regional analysis of RRM design and implementation in mobile games by developers in China, the EU, Japan, and the USA", "Abstract": "The broad adoption of random reward mechanisms (RRMs) in digital games requires cross-regional analysis and discussions on RRMs to implement socially acceptable RRM configurations. This study clarifies overall trends, cross-regional common practices, and region-dependent characteristics of RRM design, by empirically and comparatively examining RRM implementations in four regions through an integrated qualitative coding analysis and quantitative principal component analysis (PCA). We constructed an empirically grounded taxonomy of RRMs, consisting of 73 labels in 11 categories and employed these labels in a coding analysis of 40 top-grossing mobile games by Chinese, European, Japanese, and US developers. We found 293 RRMs, to which we applied 3,223 codes and subsequently conducted a PCA with the frequency of assigned codes to identify similarities and differences in RRMs across these regions. The results indicated a higher coherence of RRM designs in Japan than in other regions, arguably owing to a relatively long history of RRMs, higher social acceptance, and robust industry-initiated self-regulation. Our integrated method of qualitative coding analysis and PCA is viable for further comparative studies on game design.", "Keywords": "Random reward mechanism ; Loot box ; Gacha ; Cross-regional analysis ; Mobile games", "DOI": "10.1016/j.entcom.2023.100606", "PubYear": 2024, "Volume": "48", "Issue": "", "JournalId": 21223, "JournalTitle": "Entertainment Computing", "ISSN": "1875-9521", "EISSN": "1875-953X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Foreign Studies, Sophia University, 7-1 <PERSON><PERSON><PERSON><PERSON><PERSON>, Chiyoda-ku, Tokyo 102-8554, Japan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Business Administration, Toyo University, 5-28-20 Hakusan, Bunkyo-ku, Tokyo 112-8606, Japan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Cygames Research, Cygames, Inc., 16-17 Nanpeidai-cho, Shibuya-ku, Tokyo 150-0036, Japan;Faculty of Educational Sciences, University of Helsinki, Siltavuorenpenger 5A, P.O. Box 9, 00014 Helsinki, Finland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Media and Governance, Keio University, 5322 Endo, Fujisawa-shi, Kanagawa 252-0882, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Cygames Research, Cygames, Inc., 16-17 Nanpeidai-cho, Shibuya-ku, Tokyo 150-0036, Japan;Graduate School of Media and Governance, Keio University, 5322 Endo, Fujisawa-shi, Kanagawa 252-0882, Japan"}], "References": [{"Title": "Paying for loot boxes is linked to problem gambling, regardless of specific features like cash-out and pay-to-win", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "181", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 110106232, "Title": "A hybrid lightweight blockchain based encryption scheme for security enhancement in cloud computing", "Abstract": "<p>Cloud services provide an optimal form of demand based data outsourcing. The large amount of user data sharing increases the possibility of attacks, and unauthorized users get easy data access to the data. Blockchain technology provides better security in the cloud based on its distributed and highly cohesive nature. In order to enhance the block chain based encryption process, the second work intends to propose a blockchain based hybrid optimized cryptography scheme for secure cloud storage. At first, key generation is performed using the ECC approach in the cloud. In cloud user registration, keys and data are needed, and the cloud will provide the user ID. Then, the optimal key selection is performed by using flamingo search optimization (FSO). The public and the private key is selected by using this optimization approach. Afterwards, data encryption is performed using the Elgamal scheme on the owner side. This hybrid lightweight elliptic Elgamal based encryption (HLEEE) approach in key generation and data encryption process increases data security. After the authentication process, the cloud controller maintains the blockchain to protect the data and signatures of the users by generating the hash in blocks. An optimal hash generation is performed using the SHA-256 approach in the blockchain. The generated hash value, encrypted data and timestamp are stored in each block to provide more security. Finally, blockchain validation is performed using the proof of authority (PoA) approach.</p>", "Keywords": "Blockchain technology; Hybrid lightweight data encryption; Cloud computing; Optimization; Cryptographic hashing; Proof of authority", "DOI": "10.1007/s11042-023-17040-y", "PubYear": 2024, "Volume": "83", "Issue": "1", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Jamia Millia Islamia, Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Jamia Millia Islamia, Delhi, India"}, {"AuthorId": 3, "Name": "Mans<PERSON>", "Affiliation": "Department of Computer Science, Jamia Millia Islamia, Delhi, India"}], "References": [{"Title": "RETRACTED ARTICLE: Securing e-health records using keyless signature infrastructure blockchain technology in the cloud", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "3", "Page": "639", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A cloud-edge based data security architecture for sharing and analysing cyber threat information", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "710", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Blockchain data-based cloud data integrity protection mechanism", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "902", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Consensus-oriented cloud manufacturing based on blockchain technology: An exploratory study", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "", "Page": "101113", "JournalTitle": "Pervasive and Mobile Computing"}, {"Title": "RETRACTED ARTICLE: R2R-CSES: proactive security data process using random round crypto security encryption standard in cloud environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "5", "Page": "4643", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "BCAS: A blockchain-based ciphertext-policy attribute-based encryption scheme for cloud data security sharing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "3", "Page": "155014772199961", "JournalTitle": "International Journal of Distributed Sensor Networks"}, {"Title": "Security Threats, Defense Mechanisms, Challenges, and Future Directions in Cloud Computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "1", "Page": "223", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Efficient sensitivity orient blockchain encryption for improved data security in cloud", "Authors": "<PERSON><PERSON>; <PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "249", "JournalTitle": "Concurrent Engineering"}, {"Title": "A new SLA-aware method for discovering the cloud services using an improved nature-inspired optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 110106305, "Title": "FROM THE EDITOR‐IN‐CHIEF", "Abstract": "", "Keywords": "", "DOI": "10.1002/inst.12450", "PubYear": 2023, "Volume": "26", "Issue": "3", "JournalId": 10971, "JournalTitle": "INSIGHT", "ISSN": "2156-485X", "EISSN": "2156-4868", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 110106306, "Title": "Incorporating Digital Twins In Early Research and Development of Megaprojects To Reduce Cost and Schedule Risk", "Abstract": "<p>Early‐stage research and development (ESR&D) plays a vital role in the product development lifecycle, necessitating innovative approaches to address the complex challenges faced during this phase. This article quantifies how the incorporation of digital twin (DT) technology can reduce cost and schedule risk during ESR&D and later lifecycle stages in megaprojects. The Idaho National Laboratory demonstrated the application of DT in the Microreactor AGile Non‐Nuclear Experimental Testbed (MAGNET) operations phase, showcasing the transformative potential of DT in both design and operation. These advances allowed real‐time assessment of construction changes and their impact on project requirements. By focusing on the benefits of digital twinning, this article aims to promote a more positive attitude toward the incorporation of digital twin technologies in the early stages of R&D projects.</p>", "Keywords": "", "DOI": "10.1002/inst.12457", "PubYear": 2023, "Volume": "26", "Issue": "3", "JournalId": 10971, "JournalTitle": "INSIGHT", "ISSN": "2156-485X", "EISSN": "2156-4868", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 110106341, "Title": "Analyzing and forecasting service demands using human mobility data: A two-stage predictive framework with decomposition and multivariate analysis", "Abstract": "Accurate service demand forecasts at critical facilities are fundamental for efficiently managing resources and promptly providing essential services to people and community. However, it has received little attention in the literature, mainly due to the unavailability of granular data and the lack of sophisticated forecasting methods. To address this gap, we provide a new perspective on sensing service demands at critical facilities leveraging fine-grained human mobility data, and propose a novel data-driven framework to forecast mobility patterns at the neighborhood level. Specifically, we develop a two-stage forecasting scheme to manage large-scale and complex human movement information. The first stage is to decompose the large-scale mobility data into spatial and temporal patterns, whereas the second stage is to model complex temporal dynamics using multivariate time series analysis. The proposed framework is implemented using real human mobility data obtained from mobile phone users. The results show that our model demonstrates the best predictive performance for varying forecast horizons, when compared to multiple benchmark methods including traditionally-used statistical and deep learning models. We also performed model robustness checks, showing that the proposed model is robust in making short-term and long-term forecasts. The proposed predictive framework could help businesses and local governments accurately forecast service demands for critical facilities for better allocating their resources.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.121698", "PubYear": 2024, "Volume": "238", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, University at Buffalo - The State University of New York, Buffalo NY 14260, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, University at Buffalo - The State University of New York, Buffalo NY 14260, USA;Corresponding author"}], "References": [{"Title": "Predictive big data analytics for supply chain demand forecasting: methods, applications, and research opportunities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "Forecasting demand profiles of new products", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "113401", "JournalTitle": "Decision Support Systems"}, {"Title": "Deep Learning of Spatiotemporal Patterns for Urban Mobility Prediction Using Big Data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "2", "Page": "224", "JournalTitle": "Information Systems Research"}, {"Title": "Approaching sales forecasting using recurrent neural networks and transformers", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON>; <PERSON><PERSON>-<PERSON><PERSON>", "PubYear": 2022, "Volume": "201", "Issue": "", "Page": "116993", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Human mobility forecasting with region-based flows and geotagged Twitter data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117477", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Commodity demand forecasting using modulated rank reduction for humanitarian logistics planning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "206", "Issue": "", "Page": "117753", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Improving Sales Forecasting Accuracy: A Tensor Factorization Approach with Demand Awareness", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "3", "Page": "1644", "JournalTitle": "INFORMS Journal on Computing"}, {"Title": "Predictive analytics for demand forecasting: A deep learning-based decision support system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "258", "Issue": "", "Page": "109956", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An LSTM<sup>+</sup> Model for Managing Epidemics: Using Population Mobility and Vulnerability for Forecasting COVID-19 Hospital Admissions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "2", "Page": "440", "JournalTitle": "INFORMS Journal on Computing"}]}, {"ArticleId": 110106484, "Title": "An improved decomposition-based heuristic for truck platooning", "Abstract": "Truck platooning is a promising transportation mode in which several trucks drive together and thus save fuel consumption by suffering less air resistance. In this paper, we consider a truck platooning system for which we jointly optimize the truck routes and schedules from the perspective of a central platform. We improve an existing decomposition-based heuristic by <PERSON><PERSON> and <PERSON> (2022), which iteratively solves a routing and a scheduling problem, with a cost modification step after each scheduling run. We propose different formulations for the routing and the scheduling problem and embed these into <PERSON><PERSON> and <PERSON>’s framework, and we examine ways to improve their iterative process. In addition, we propose another scheduling heuristic, the pairwise preprocessing heuristic, to deal with large instances. The computational results show that our procedure achieves better performance than the existing one under certain realistic settings.", "Keywords": "", "DOI": "10.1016/j.cor.2023.106439", "PubYear": 2024, "Volume": "161", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ORSTAT, KU Leuven, Leuven, Belgium;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ORSTAT, KU Leuven, Leuven, Belgium"}], "References": []}, {"ArticleId": *********, "Title": "BinAIV: Semantic-enhanced vulnerability detection for Linux x86 binaries", "Abstract": "Binary code vulnerability detection is an important research direction in the field of network security . The extensive reuse of open-source code has led to the spread of vulnerabilities that originally only affected a small number of targets to other software. Existing vulnerability detection methods are mainly based on binary code similarity analysis, that is, by comparing the similarity of code embedding to detect vulnerabilities. However, existing methods lack semantic understanding of binary code and cannot distinguish between different functions with similar code structures, which reduces the accuracy of vulnerability detection. This paper proposes a binary vulnerability detection method BinAIV based on function semantics . BinAIV is based on a neural network model , which defines and constructs binary function semantics to achieve more accurate similarity analysis. Experimental results show that in terms of binary code similarity analysis performance, BinAIV has a significant improvement compared to traditional methods that only use function embedding. In cross-compiler function search, cross-optimization function search, and cross-obfuscation function search experiments, the average Recall@1 value of BinAIV compared to the best-performing baseline methods increased by 40.1 %, 99.8 %, and 184.0 %. In the real-world vulnerability detection experiment, BinAIV had the highest detection accuracy for all vulnerabilities, with an improvement of 155.1 % and 97.7 % compared to Asm2Vec and SAFE, respectively.", "Keywords": "", "DOI": "10.1016/j.cose.2023.103508", "PubYear": 2023, "Volume": "135", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "PLA Strategic Support Force Information Engineering University, Zhengzhou 450000, China"}, {"AuthorId": 2, "Name": "Hui Shu", "Affiliation": "PLA Strategic Support Force Information Engineering University, Zhengzhou 450000, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "PLA Strategic Support Force Information Engineering University, Zhengzhou 450000, China"}], "References": [{"Title": "BinVulDet: Detecting vulnerability in binary program via decompiled pseudo code and BiLSTM-attention", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "", "Page": "103023", "JournalTitle": "Computers & Security"}, {"Title": "DeepDual-SD: Deep Dual Attribute-Aware Embedding for Binary Code Similarity Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Computational Intelligence Systems"}]}, {"ArticleId": 110106531, "Title": "PARNet: Deep neural network for the diagnosis of <PERSON><PERSON><PERSON>'s disease", "Abstract": "<p>In this study, the successful network architecture we developed from scratch to diagnose COVID-19 has been retrained, using single photon emission computed tomography (SPECT) images to detect Parkinson’s disease (PD). We aim to investigate whether a network trained on medical images can be adapted for the diagnosis of another disease successfully. This retrained neural network, PARNet, can detect PD patients. In this study, we use 1213 SPECT images as a dataset. The number of PD and healthy control (HC) group images is 1000 and 213, respectively. We divided the dataset into training (70%), validation (10%), and test (20%) sets. Our network shows outstanding performance with an accuracy of 95.43%, a sensitivity of 95.25%, a specificity of 95.70%, a precision of 97%, and an f1-score of 96%. Our method has the potential to improve the diagnosis and treatment of PD. PARNet, with high diagnosis performance, can contribute to assisting clinicians in diagnosing PD at an earlier. PARNet network based on COV19-ResNet architecture showed performance similar to even high to that of the larger pre-trained models of ImageNet in diagnosing PD. This network can be easily retrained with images from different medical domains to detect various diseases.</p>", "Keywords": "Deep neural network; SPECT; Parkinson’s disease; Diagnosis; Image classification", "DOI": "10.1007/s11042-023-16940-3", "PubYear": 2024, "Volume": "83", "Issue": "12", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Education and Instructional Technology, Faculty of Education, Agri Ibrahim Cecen University, Agri, Turkey; Corresponding author."}, {"AuthorId": 2, "Name": "Ayturk Keles", "Affiliation": "Department of Computer Education and Instructional Technology, Faculty of Education, Agri Ibrahim Cecen University, Agri, Turkey"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "TUBITAK BILGEM, Kocaeli, Turkey"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering, Faculty of Engineering, Istanbul Aydin University, Istanbul, Turkey"}], "References": [{"Title": "Parkinson and essential tremor classification to identify the patient’s risk based on tremor severity", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "107946", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Deep learning based diagnosis of Parkinson’s disease using convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "15467", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 110106534, "Title": "A prospective study: Advances in chaotic characteristics of serum Raman spectroscopy in the field of assisted diagnosis of disease", "Abstract": "Chaos theory is an important branch of mathematics and its theory has been widely applied in many fields such as physics and medicine. Based on existing spectroscopic techniques, this paper used chaos theory as a research method for nonlinear time series to analyze Raman spectral time domain curves in order to improve the performance of disease diagnostic models and explore a new paradigm of spectroscopic technology for intelligent assisted disease diagnosis. We quantitatively identified the chaotic characteristics of time domain Raman spectra by three methods, extracted chaotic features such as correlation dimension and Kolmogorov entropy, and used the chaotic features as input to Extreme Learning Machine (ELM), Back Propagation Neural Network (BPNN), K-Nearest Neighbor (KNN) and Support Vector Machine (SVM) to diagnose patients with lung cancer (LC), glioma, renal cell carcinoma (RCC) and esophageal cancer (EC). The Raman spectra were also analyzed by traditional spectral feature extraction &amp; modelling method, and the results of traditional spectral feature extraction &amp; modelling method and chaotic feature modelling method were compared. The experimental results showed that the extraction of effective chaotic features in the full spectral range could achieve comparable diagnostic results with the traditional spectral feature extraction &amp; modelling method. To further validate the effectiveness of chaos theory in Raman spectral data, the full spectrum was divided into three consecutive subsequences of 500–1000, 1000–1500, and 1500–2000 cm <sup>−1</sup>, and the above experimental steps were repeated respectively, and the results of the traditional spectral feature extraction &amp; modelling method and chaotic feature modelling were compared. The results showed that as the spectral range was split into consecutive subsequences, the diagnostic performance of the chaotic features in each subsequence performed better than that of conventional spectral analysis techniques. In this study, the technique bridges the gap in the application of chaotic signals to Raman spectroscopy techniques, focuses on global features in the time domain profile of Raman spectra, and demonstrates the significant value of chaos theory in artificial intelligence-assisted spectroscopic medical diagnosis.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.121787", "PubYear": 2024, "Volume": "238", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi 830046, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Xinjiang University, Urumqi 830046, China;Key Laboratory of Signal Detection and Processing, Xinjiang University, Urumqi 830046, China;Xinjiang Cloud Computing Application Laboratory, Karamay 834099, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Xinjiang University, Urumqi 830046, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Xinjiang University, Urumqi 830046, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi 830046, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Xinjiang University, Urumqi 830046, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi 830046, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Xinjiang University, Urumqi 830046, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi 830046, China;Corresponding authors"}, {"AuthorId": 10, "Name": "Xiaoyi Lv", "Affiliation": "College of Software, Xinjiang University, Urumqi 830046, China;Key Laboratory of Signal Detection and Processing, Xinjiang University, Urumqi 830046, China;Corresponding authors"}], "References": []}, {"ArticleId": 110106537, "Title": "Portable and visual quantification of urine cell-free DNA through smartphone-based colorimetric biosensor", "Abstract": "The trace amount of cell-free DNA (cfDNA) in urine provides a promising diagnosis of urological disease, but its clinical applications are restricted by complicated operations for enrichment and detection. Herein, a portable and visual smartphone-based colorimetric platform is created for urine cfDNA quantification in quick, low-cost, and instrument-free manners. cfDNA in urine is enriched by the zinc ions-dipicolylamino complexes on glass slides and labeled by PicoGreen (PG) to form the DNA-PG complexes with photocatalysis for the generation of a visible output signal. The high sensitivity is related to the amplification of the optical signal of enriched cfDNA through the photocatalysis strategy, which yields greater color changes without interference from other ingredients. Notably, accurate quantitation of cfDNA is achieved with the aid of the smartphone App equipped with collecting and processing Color Grab. As a powerful alternative, such a robust colorimetric biosensor platform is employed for urine cfDNA quantification of clinical samples with excellent linearity compared with the two-stage commercial kits (R <sup>2</sup> &gt; 0.95), helping to distinguish the difference between healthy and urological disease. The comprehensive performance of our developed platform will be the first to enable visual quantification of urine cfDNA in terms of quick detection, low cost, and high portability.", "Keywords": "", "DOI": "10.1016/j.snb.2023.134684", "PubYear": 2024, "Volume": "398", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Medicine, South China University of Technology, Guangzhou, Guangdong 510006, China;National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Biomedical Sciences and Engineering, South China University of Technology, Guangzhou International Campus, Guangzhou, Guangdong 511442, China;National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Clinical Laboratory, The First Affiliated Hospital of Zhengzhou University, Zhengzhou, Henan 450052, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medicine, South China University of Technology, Guangzhou, Guangdong 510006, China;National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Biology and Biological Engineering, South China University of Technology, Guangzhou, Guangdong 510006, China;National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China"}, {"AuthorId": 6, "Name": "Huan<PERSON> Lu", "Affiliation": "School of Biomedical Sciences and Engineering, South China University of Technology, Guangzhou International Campus, Guangzhou, Guangdong 511442, China;National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Biology and Biological Engineering, South China University of Technology, Guangzhou, Guangdong 510006, China;National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Biomedical Sciences and Engineering, South China University of Technology, Guangzhou International Campus, Guangzhou, Guangdong 511442, China;National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "School of Biomedical Sciences and Engineering, South China University of Technology, Guangzhou International Campus, Guangzhou, Guangdong 511442, China;National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "School of Medicine, South China University of Technology, Guangzhou, Guangdong 510006, China;Department of Clinical Laboratory, The First Affiliated Hospital of Zhengzhou University, Zhengzhou, Henan 450052, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "School of Biomedical Sciences and Engineering, South China University of Technology, Guangzhou International Campus, Guangzhou, Guangdong 511442, China;National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "School of Medicine, South China University of Technology, Guangzhou, Guangdong 510006, China;School of Biomedical Sciences and Engineering, South China University of Technology, Guangzhou International Campus, Guangzhou, Guangdong 511442, China;National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China;Corresponding author at: School of Biomedical Sciences and Engineering, South China University of Technology, Guangzhou International Campus, Guangzhou, Guangdong 511442, China"}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Medicine, South China University of Technology, Guangzhou, Guangdong 510006, China;Corresponding author"}, {"AuthorId": 14, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China;State Key Laboratory of Quality Research in Chinese Medicine, Institute of Chinese Medical Sciences, University of Macau, Macau;Corresponding author at: National Engineering Research Center for Tissue Restoration and Reconstruction, South China University of Technology, Guangdong 510006, China"}], "References": [{"Title": "Separation, Purification, and Detection of cfDNA in a Microfluidic Device", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "2", "Page": "195", "JournalTitle": "BioChip Journal"}]}, {"ArticleId": 110106586, "Title": "A general framework for preferences in answer set programming", "Abstract": "We introduce a general, flexible, and extensible framework for quantitative and qualitative preferences among the stable models of logic programs. Since it is straightforward to capture propositional theories and constraint satisfaction problems with logic programs, our approach is also relevant to optimization in satisfiability testing and constraint processing. We show how complex preference relations can be specified through user-defined preference types and their arguments. We describe how preference specifications are handled internally by so-called preference programs, which are used for dominance testing. We also provide algorithms for computing one, or all, preferred stable models of a logic program, and study the complexity of these problems. We implemented our approach in the asprin system by means of multi-shot answer set solving technology. We demonstrate the generality and flexibility of our methodology by showing how easily existing preference languages can be implemented in asprin . Finally, we empirically evaluate our contributions and contrast them with dedicated implementations.", "Keywords": "", "DOI": "10.1016/j.artint.2023.104023", "PubYear": 2023, "Volume": "325", "Issue": "", "JournalId": 13748, "JournalTitle": "Artificial Intelligence", "ISSN": "0004-3702", "EISSN": "1872-7921", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Leipzig , Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON>, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Potsdam, Germany;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Potsdam, Germany"}], "References": [{"Title": "Optimum stable model search: algorithms and implementation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "4", "Page": "863", "JournalTitle": "Journal of Logic and Computation"}, {"Title": "ASP-Core-2 Input Language Format", "Authors": "FRANCESCO CALIMERI; WOLFGANG FABER; MARTIN GEBSER", "PubYear": 2020, "Volume": "20", "Issue": "2", "Page": "294", "JournalTitle": "Theory and Practice of Logic Programming"}, {"Title": "ASP (): Answer Set Programming with Algebraic Constraints", "Authors": "THOMAS EITER; RAFAEL KIESEL", "PubYear": 2020, "Volume": "20", "Issue": "6", "Page": "895", "JournalTitle": "Theory and Practice of Logic Programming"}, {"Title": "An ASP approach for reasoning in a concept-aware multipreferential lightweight DL", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "5", "Page": "751", "JournalTitle": "Theory and Practice of Logic Programming"}, {"Title": "Declarative Algorithms and Complexity Results for Assumption-Based Argumentation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "265", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "A Logical Characterization of the Preferred Models of Logic Programs with Ordered Disjunction", "Authors": "ANGELOS CHARALAMBIDIS; PANOS RONDOGIANNIS; ANTONIS TROUMPOUKIS", "PubYear": 2021, "Volume": "21", "Issue": "5", "Page": "629", "JournalTitle": "Theory and Practice of Logic Programming"}, {"Title": "Reasoning on Multirelational Contextual Hierarchies via Answer Set Programming with Algebraic Measures", "Authors": "LORIS BOZZATO; THOMAS EITER; RAFAEL KIESEL", "PubYear": 2021, "Volume": "21", "Issue": "5", "Page": "593", "JournalTitle": "Theory and Practice of Logic Programming"}, {"Title": "How to Build Your Own ASP-based System?!", "Authors": "ROLAND KAMINSKI; JAVIER ROMERO; TORSTEN SCHAUB", "PubYear": 2023, "Volume": "23", "Issue": "1", "Page": "299", "JournalTitle": "Theory and Practice of Logic Programming"}, {"Title": "An ASP Approach for Reasoning on Neural Networks under a Finitely Many-Valued Semantics for Weighted Conditional Knowledge Bases", "Authors": "LAURA GIORDANO; DANIELE THESEIDER DUPRÉ", "PubYear": 2022, "Volume": "22", "Issue": "4", "Page": "589", "JournalTitle": "Theory and Practice of Logic Programming"}, {"Title": "ASP and subset minimality: Enumeration, cautious reasoning and MUSes", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "320", "Issue": "", "Page": "103931", "JournalTitle": "Artificial Intelligence"}]}, {"ArticleId": 110106632, "Title": "Assessment of college students’ mental health status based on temporal perception and hybrid clustering algorithm under the impact of public health events", "Abstract": "<p>The dynamic landscape of public health occurrences presents a formidable challenge to the emotional well-being of college students, necessitating a precise appraisal of their mental health (MH) status. A pivotal metric in this realm is the Mental Health Assessment Index, a prevalent gauge utilized to ascertain an individual’s psychological well-being. However, prevailing indices predominantly stem from a physical vantage point, neglecting the intricate psychological dimensions. In pursuit of a judicious evaluation of college students’ mental health within the crucible of public health vicissitudes, we have pioneered an innovative metric, underscored by temporal perception, in concert with a hybrid clustering algorithm. This augmentation stands poised to enrich the extant psychological assessment index framework. Our approach hinges on the transmutation of temporal perception into a quantifiable measure, harmoniously interwoven with established evaluative metrics, thereby forging a novel composite evaluation metric. This composite metric serves as the fulcrum upon which we have conceived a pioneering clustering algorithm, seamlessly fusing the fireworks algorithm with K-means clustering. The strategic integration of the fireworks algorithm addresses a noteworthy vulnerability inherent to K-means—its susceptibility to converging onto local optima. Empirical validation of our paradigm attests to its efficacy. The proposed hybrid clustering algorithm aptly captures the dynamic nuances characterizing college students’ mental health trajectories. Across diverse assessment stages, our model consistently attains an accuracy threshold surpassing 90%, thus outshining existing evaluation techniques in both precision and simplicity. In summation, this innovative amalgamation presents a formidable stride toward an augmented understanding of college students’ mental well-being during times of fluctuating public health dynamics.</p>", "Keywords": "Mental health; College students; Public health; Fireworks algorithm; K-means", "DOI": "10.7717/peerj-cs.1586", "PubYear": 2023, "Volume": "9", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Sichuan Vocational College of Health and Rehabilitation, Zigong, Sichuan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Student Affairs Department, Huanggang Normal University, Huanggang, Hubei, China"}], "References": [{"Title": "A Comprehensive Review of the Fireworks Algorithm", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 110106681, "Title": "Advances in lifelog retrieval at the lifelog search challenge 2021", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-023-17073-3", "PubYear": 2023, "Volume": "82", "Issue": "24", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [], "References": []}, {"ArticleId": 110106827, "Title": "Formalstyler: GPT-Based Model for Formal Style Transfer with Meaning Preservation", "Abstract": "<p>Style transfer is a natural language processing generation task, it consists of substituting one given writing style for another one. In this work, we seek to perform informal-to-formal style transfers in the English language by using a style transfer model that takes advantage of the GPT-2. This process is shown in our web interface where the user input a informal message by text or voice. Our target audience are students and professionals in the need to improve the quality of their work by formalizing their texts. A style transfer is considered successful when the original semantic meaning of the message is preserved after the independent style has been replaced with a formal one with a high degree of grammatical correctness. This task is hindered by the scarcity of training and evaluation datasets alongside the lack of metrics. To accomplish this task, we opted to utilize OpenAI’s GPT-2 Transformer-based pre-trained model. To adapt the GPT-2 to our research, we fine-tuned the model with a parallel corpus containing informal text entries paired with the equivalent formal ones. We evaluate the fine-tuned model results with two specific metrics, formality and meaning preservation. To further fine-tune the model, we integrate a human-based feedback system where the user selects the best formal sentence out of the ones generated by the model. The resulting evaluations of our solution exhibit similar to improved scores in formality and meaning preservation to state-of-the-art approaches.</p>", "Keywords": "Natural language processing; Style transfer; GPT-2; Formalization; Meaning preservation; Transformers", "DOI": "10.1007/s42979-023-02110-7", "PubYear": 2023, "Volume": "4", "Issue": "6", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departement of Computer Science, Universidad Peruana de Ciencias Aplicadas (UPC), Lima, Peru"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Departement of Computer Science, Universidad Peruana de Ciencias Aplicadas (UPC), Lima, Peru"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Departement of Computer Science, Universidad Peruana de Ciencias Aplicadas (UPC), Lima, Peru"}], "References": []}, {"ArticleId": 110106835, "Title": "A brief survey of observers for disturbance estimation and compensation", "Abstract": "<p> An accurate dynamic model of a robot is fundamentally important for a control system, while uncertainties residing in the model are inevitable in a physical robot system. The uncertainties can be categorized as internal disturbances and external disturbances in general. The former may include dynamic model errors and joint frictions, while the latter may include external payloads or human-exerted force to the robot. Disturbance observer is an important technique to estimate and compensate for the uncertainties of the dynamic model. Different types of disturbance observers have been developed to estimate the lumped uncertainties so far. In this paper, we conducted a brief survey on five typical types of observers from a perspective of practical implementation in a robot control system, including generalized momentum observer (GMO), joint velocity observer (JVOB), nonlinear disturbance observer (NDOB), disturbance Kalman filter (DKF), and extended state observer (ESO). First, we introduced the basics of each observer including equations and derivations. Two common types of disturbances are considered as two scenarios, that is , constant external disturbance and time-varying external disturbance. Then, the observers are separately implemented in each of the two simulated scenarios, and the disturbance tracking performance of each observer is presented while their performance in the same scenario has also been compared in the same figure. Finally, the main features and possible behaviors of each type of observer are summarized and discussed. This survey is devoted to helping readers learn the basic expressions of five typical observers and implement them in a robot control system. </p>", "Keywords": "disturbance observer; generalized momentum observer; <PERSON><PERSON> filter; extended state observer; constant disturbance; time-varying disturbance; impedance control", "DOI": "10.1017/S0263574723001091", "PubYear": 2023, "Volume": "41", "Issue": "12", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Faculty of Engineering, University of Alberta, Edmonton, T6G 1H9, AB, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Astronautics, Nanjing University of Aeronautics and Astronautics, Nanjing, 210016, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Faculty of Engineering, University of Alberta, Edmonton, T6G 1H9, AB, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Robotics and Automated Systems (ARAS), Faculty of Electrical Engineering, K. N. Toosi University of Technology, Tehran, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Faculty of Engineering, University of Alberta, Edmonton, T6G 1H9, AB, Canada; Corresponding author."}], "References": [{"Title": "Sensorless environment stiffness and interaction force estimation for impedance control tuning in robotized interaction tasks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "45", "Issue": "3", "Page": "371", "JournalTitle": "Autonomous Robots"}, {"Title": "Sensorless force estimation for industrial robots using disturbance observer and neural learning of friction approximation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "102168", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 110106920, "Title": "Securing edge computing using cryptographic schemes: a review", "Abstract": "<p>The exponential growth and wide-area applications of the Internet of Things have garnered a lot of interest from academics and industries, thus becoming one of the most actively studied research paradigms in recent years. Internet of Things is a concept that builds an inter-connected environment where physical devices collect data from the surroundings and customize it to our requirements. It is known for its fast processing and quick response. Edge computing is a platform that performs the computations on the data supplied by the Internet of Thing devices at the network edge that leads to real-time processing. Despite its advantages, privacy protection and security challenges remain a critical concern that must be addressed. This paper aims to give a comprehensive review on cryptographic schemes used for securing edge computing. In particular, we first present a concept of edge computing in the context of IoT including architecture and advantages over cloud computing. We have explored various encryption techniques like identity-based encryption, attribute-based encryption, searchable encryption, and homomorphic encryption that secure the sensitive data before processing it at the network edge. Various parameters of each encryption technique, such as the length of the public key, private key, and ciphetext, are compared. Computational complexity of encryption and decryption is also included in our comparative study. Although many review papers focused on authentication and authorization challenges, this review on encryption techniques in context of edge security was highly needed. We have also looked at some of the other review papers and compared them to our survey to show how important it is.</p>", "Keywords": "Attribute based encryption; Cryptography; Edge computing; Internet of things; Homomorphic encryption; Identity based encryption; Searchable encryption", "DOI": "10.1007/s11042-023-15592-7", "PubYear": 2024, "Volume": "83", "Issue": "12", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, National Institute of Technology, Papum Pare, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, National Institute of Technology, Papum Pare, India"}], "References": [{"Title": "Privacy-preserving searchable encryption in the intelligent edge computing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "164", "Issue": "", "Page": "31", "JournalTitle": "Computer Communications"}, {"Title": "Designated-ciphertext searchable encryption", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "102709", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Energy-efficient dynamic homomorphic security scheme for fog computing in IoT networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "102768", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Towards edge computing in intelligent manufacturing: Past, present and future", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "588", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A review on trust management in fog/edge computing: Techniques, trends, and challenges", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "204", "Issue": "", "Page": "103402", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 110106921, "Title": "Evaluation of data governance effectiveness in power grid enterprises using deep neural network", "Abstract": "<p>In an era of unprecedented technological advancement, the power industry is undergoing a transformative evolution, particularly in intelligent power grid enterprises. The integration of cutting-edge information science and technology has ushered in a new era of automation, informatization, and intelligence within these enterprises. While this progression promises enhanced production, operation, and management capabilities, it also brings forth a daunting challenge: the effective governance of the burgeoning volumes of power data. This paper aims to conduct in-depth research on evaluating the effectiveness of data governance in power grid enterprises based on deep learning to integrate more closely with their business systems. The main objective is to provide effective and convenient intelligent services for decision-making within an innovative power grid enterprise management system and strengthen the data architecture of these enterprises in data management. First, the deep learning neural network's principle structure and training methods are introduced in detail and combined with the deep learning neural network. This is a different evaluation model for the data governance effectiveness of power grid enterprises based on penalty variable weight. The difference probability density of the power difference data series in the power grid is taken as the evaluation index. The evaluation model for the governance effectiveness of different data is modified. Build a different evaluation model of power grid enterprise data governance effectiveness based on punishment and weight change, comprehensively consider the extent to which the data volume of power grid abnormal data in power grid enterprises affects the evaluation of data governance effectiveness, and complete the assessment of power grid enterprise data governance effectiveness based on deep learning. Experimental results underscore the method's efficacy, demonstrating an exceptional accuracy rate of 94%. This empirical validation highlights the method's efficient evaluation process, offering invaluable technical support for enhancing power data management's consistency, precision, and reliability within power grid enterprises. Moreover, comparative analyses against other methodologies, including KNN, SVM, RF, DT, and RNN, reaffirm the superiority of the DNN model, solidifying its outstanding performance.</p>", "Keywords": "Deep learning; Smart power grid enterprises; Effectiveness of data governance; Penalty changes power", "DOI": "10.1007/s00500-023-09210-9", "PubYear": 2023, "Volume": "27", "Issue": "23", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Electric Power Research Institute, Guangxi Power Grid Co., Ltd, Nanning, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Nanning Power Supply Bureau of Guangxi Power Grid Co., Ltd, Nanning, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Electric Power Research Institute, Guangxi Power Grid Co., Ltd, Nanning, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Digital Power Research Institute, China Southern Power Grid, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Digital Power Research Institute, China Southern Power Grid, Guangzhou, China"}], "References": [{"Title": "Pruning filters with L1-norm and capped L1-norm for CNN compression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "2", "Page": "1152", "JournalTitle": "Applied Intelligence"}, {"Title": "Adaptive event‐triggered robust\n H \n <sub>\n ∞ \n </sub>\n control for Takagi–Sugeno fuzzy networked Markov jump systems with time‐varying delay", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "1", "Page": "213", "JournalTitle": "Asian Journal of Control"}, {"Title": "Resource Orchestration of Cloud-Edge–based Smart Grid Fault Detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}, {"Title": "Evaluating quality in human-robot interaction: A systematic search and classification of performance and human-centered factors, measures and metrics towards an industry 5.0", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "63", "Issue": "", "Page": "392", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Jerk-bounded trajectory planning for rotary flexible joint manipulator: an experimental approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "7", "Page": "4029", "JournalTitle": "Soft Computing"}, {"Title": "A practical study of active disturbance rejection control for rotary flexible joint robot manipulator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "8", "Page": "4987", "JournalTitle": "Soft Computing"}, {"Title": "A data-driven approach for intrusion and anomaly detection using automated machine learning for the Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "19", "Page": "14469", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 110106949, "Title": "Enhanced cross-prompt trait scoring via syntactic feature fusion and contrastive learning", "Abstract": "<p>Automated essay scoring aims to evaluate the quality of an essay automatically. It is one of the main educational applications in the field of natural language processing. Recently, the research scope has been extended from prompt-special scoring to cross-prompt scoring and further concentrating on scoring different traits. However, cross-prompt trait scoring requires identifying inner-relations, domain knowledge, and trait representation as well as dealing with insufficient training data for the specific traits. To address these problems, we propose a RDCTS model that employs contrastive learning and utilizes <PERSON><PERSON>back–Le<PERSON> divergence to measure the similarity of positive and negative samples, and we design a feature fusion algorithm that combines POS and syntactic features instead of using single text attribute features as input for the neural AES system. We incorporate implicit data augmentation by adding the dropout layer to the word level and sentence level of the hierarchical model to mitigate the effects of limited data. Experimental results show that our RDCTS achieves state-of-the-art performance and greater consistency.</p>", "Keywords": "Automated essay scoring; Natural language processing; Contrastive learning; Data augmentation; Information fusion", "DOI": "10.1007/s11227-023-05640-2", "PubYear": 2024, "Volume": "80", "Issue": "4", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "Jingbo Sun", "Affiliation": "School of Artificial Intelligence, Beijing Normal University, Beijing, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chinese Character Research and Application Laboratory, Beijing Normal University, Beijing, China; Linguistic Data Consortium, University of Pennsylvania, Philadelphia, USA"}, {"AuthorId": 3, "Name": "Tianbao Song", "Affiliation": "School of Computer Science and Engineering, Beijing Technology and Business University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing Normal University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Teachers’ College, Beijing Union University, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Song", "Affiliation": "School of Artificial Intelligence, Beijing Normal University, Beijing, China; Corresponding author."}], "References": [{"Title": "Automatic assessment of descriptive answers in online examination system using semantic relational features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "6", "Page": "4430", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "SEDNN: Shared and enhanced deep neural network model for cross-prompt automated essay scoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "210", "Issue": "", "Page": "106491", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Robust log anomaly detection based on contrastive learning and multi-scale MASS", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "16", "Page": "17491", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": *********, "Title": "Detection of obfuscated Tor traffic based on bidirectional generative adversarial networks and vision transform", "Abstract": "The Onion Router (TOR) network is a decentralized system of volunteer-run servers that aims to protect the anonymity and privacy of users by routing their internet traffic through a series of nodes. Individuals who use the TOR network may employ obfuscated traffic to conceal their internet activity from network administrators or security systems attempting to block or monitor them. Furthermore, some may use obfuscated Tor traffic to hide illegal activities, such as buying and selling illegal goods or accessing illegal services on the dark web. Despite efforts to identify and block Tor traffic, challenges remain, such as a limited set of features for identification, leading to false positives and negatives. To address these challenges, this paper proposes a novel approach using Visual Transformation (ViT), and augmentation by Bidirectional Generative Adversarial Networks (BiGAN). The proposed approach demonstrates superior performance on the ISCX-Tor2016 dataset, achieving 99.59% accuracy, 99.83% recall, 99.72% precision, and 99.78% F-score, thereby outperforming current state-of-the-art techniques.", "Keywords": "BiGAN ; Obfuscated detection ; ViT ; Tor traffic system", "DOI": "10.1016/j.cose.2023.103512", "PubYear": 2023, "Volume": "135", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information Security Department, Faculty of Information Technology, University of Petra, 11196 Amman, Jordan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Cybersecurity Department, School of Information Technology, American University of Madaba, 11821 Amman, Jordan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Artificial Intelligence Research Center (AIRC), College of Engineering and Information Technology, Ajman University, 346 Ajman, United Arab Emirates;Corresponding author"}], "References": [{"Title": "An improved BiGAN based approach for anomaly detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "185", "JournalTitle": "Procedia Computer Science"}, {"Title": "TSCRNN: A novel classification scheme of encrypted traffic based on flow spatiotemporal features for efficient management of IIoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "190", "Issue": "", "Page": "107974", "JournalTitle": "Computer Networks"}, {"Title": "EMVD: Efficient Multitype Vehicle Detection Algorithm Using Deep Learning Approach in Vehicular Communication Network for Radio Resource Management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "2", "Page": "25", "JournalTitle": "International Journal of Image, Graphics and Signal Processing"}, {"Title": "Improved Bidirectional GAN-Based Approach for Network Intrusion Detection Using One-Class Classifier", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "6", "Page": "85", "JournalTitle": "Computers"}, {"Title": "DDoS Attack Detection in Cloud Computing Based on Ensemble Feature Selection and Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "75", "Issue": "2", "Page": "3571", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Securing Cloud Computing from Flash Crowd Attack Using Ensemble Intrusion Detection System", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "1", "Page": "453", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 110106960, "Title": "<PERSON>ie disclaimers: Dark patterns and lack of transparency", "Abstract": "While cookie disclaimers on websites have been proposed to ensure that users make informed decisions regarding consenting to data collection via cookies, such informed consent is hindered by several factors. One of them is the presence of so-called dark patterns, that is, design elements that are used to lead users to accept more cookies than needed and more than they are aware of. The second factor is lack of transparency on behalf of the service providers with regards to what happens if the user does not consent to cookie usage even despite dark patterns nudging them to do so. The contributions of this paper are (1) evaluating the efficacy of several of these factors while measuring actual behaviour; (2) identifying users&#x27; attitude towards cookie disclaimers including how they decide which cookies to accept or reject; (3) assessing the behaviour of websites regarding storing non-necessary cookies despite user&#x27;s consent. We show that different visual representation of the reject/accept option have a significant impact on users&#x27; decision. We also found that the labelling of the reject option has a significant impact. In addition, we confirm previous research regarding biasing text (which has no significant impact on users&#x27; decision). Our results on users&#x27; attitude towards cookie disclaimers indicate that for several user groups the design of the disclaimer only plays a secondary role when it comes to decision making. We furthermore show that even without user&#x27;s explicit consent, the majority of websites we investigated still uses non-necessary cookies. We provide recommendations on how to improve the situation for different stakeholders, namely, for developers and policy makers.", "Keywords": "", "DOI": "10.1016/j.cose.2023.103507", "PubYear": 2024, "Volume": "136", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Karlsruhe, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Karlsruhe, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Karlsruhe, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Karlsruhe, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IT University of Copenhagen, Copenhagen, Denmark;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Karlsruhe, Germany"}], "References": [{"Title": "Has the GDPR hype affected users’ reaction to cookie disclaimers?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1", "Page": "", "JournalTitle": "Journal of Cybersecurity"}, {"Title": "The Nudge Puzzle", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "Quod erat demonstrandum? - Towards a typology of the concept of explanation for the design of explainable AI", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "118888", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Cookie-Nutzung nach Inkrafttreten des TTDSG: Zur Datenschutzkonformität des Cookie-Einsatzes auf den meistgenutzten deutschen Websites", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "5", "Page": "283", "JournalTitle": "Datenschutz und Datensicherheit - DuD"}]}, {"ArticleId": 110107038, "Title": "Spoofing Against Spoofing: Towards Caller ID Verification In Heterogeneous Telecommunication Systems", "Abstract": "<p>Caller ID spoofing is a global industry problem and often acts as a critical enabler for telephone fraud. To address this problem, the Federal Communications Commission (FCC) has mandated telecom providers in the US to implement STIR/SHAKEN, an industry-driven solution based on digital signatures. STIR/SHAKEN relies on a public key infrastructure (PKI) to manage digital certificates, but scaling up this PKI for the global telecom industry is extremely difficult, if not impossible. Furthermore, it only works with IP-based systems (e.g., SIP), leaving the traditional non-IP systems (e.g., SS7) unprotected. So far the alternatives to the STIR/SHAKEN have not been sufficiently studied. In this paper, we propose a PKI-free solution, called Caller ID Verification (CIV). CIV authenticates the caller ID based on a challenge-response process instead of digital signatures, hence requiring no PKI. It supports both IP and non-IP systems. Perhaps counter-intuitively, we show that number spoofing can be leveraged, in conjunction with Dual-Tone Multi-Frequency (DTMF), to efficiently implement the challenge-response process, i.e., using spoofing to fight against spoofing. We implement CIV for VoIP, cellular, and landline phones across heterogeneous networks (SS7/SIP) by only updating the software on the user’s phone. This is the first caller ID authentication solution with working prototypes for all three types of telephone systems in the current telecom architecture. Finally, we show how the implementation of CIV can be optimized by integrating it into telecom clouds as a service, which users may subscribe to.</p>", "Keywords": "", "DOI": "10.1145/3625546", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 26772, "JournalTitle": "ACM Transactions on Privacy and Security", "ISSN": "2471-2566", "EISSN": "2471-2574", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Warwick, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Warwick, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computing and Digital Technology, Birmingham City University, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nar Co., Iran"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "trueCall Ltd, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Warwick, United Kingdom"}], "References": []}, {"ArticleId": 110107319, "Title": "3D Ergonomic Board: Kids Teaching and Learning Proposition", "Abstract": "The caprice for the study came from an indigenous designed instructional board for teaching learners in nursery/primary schools (2 - 10 years) and the task whose overall topic is the reminiscence, retention of visuals aid use in the pedagogy. The study is to appraise the opinion of toddlers/teachers about the helpfulness of 3D-visuals (ergonomic board); the clarity of the intended functions of the 3D-visuals in the toddler’s lessons instruction and support from the producers in using them; and why visuals (conventional visual aids) in schoolroom instruction are misjudged/misinterpreted; their view about the functions in specific (conventional visuals) for the instruction pedagogy; and other sources of visuals provided other than the lesson’s instruction. Four nursery/primary schools participated in the study. Seventy-two (72) toddler’s/teachers participated in the study. The study examined using quantitative and qualitative approach for statistical analysis (using pie-chart and histogram). The findings suggested that the aspects of visuals items selected for comment and description are to some extent circumscribed by toddler’s learners’ linguistic resources. Understandably, the point made by researchers referred that visuals often fail to support learning as effectively as they might because they are not regarded as a full-fledged information mode that requires the same careful processing as verbal text, is borne out by the findings. Recommendation, the accountability is above all on producers of kid’s instructional materials: authors, editors, designers and illustrators of the materials. The implication of this is that toddlers’ instructional materials should, as in the case of illustrated books include both an overt explanation of the overall philosophy toward visuals and overview of their use.", "Keywords": "Visual Enhancement;Kid’s;Teaching;Pedagogy", "DOI": "10.4236/jsip.2023.143003", "PubYear": 2023, "Volume": "14", "Issue": "3", "JournalId": 14793, "JournalTitle": "Journal of Signal and Information Processing", "ISSN": "2159-4465", "EISSN": "2159-4481", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Industrial Design Department, Faculty of Environmental Studies, University of Maiduguri, Maiduguri, Nigeria ."}, {"AuthorId": 2, "Name": "Ijudai <PERSON>", "Affiliation": "Industrial Design Department, Faculty of Environmental Studies, University of Maiduguri, Maiduguri, Nigeria ."}], "References": []}, {"ArticleId": 110107442, "Title": "An IoT and Machine Learning-Based Predictive Maintenance System for Electrical Motors", "Abstract": "The rise of Industry 4.0 and smart manufacturing has highlighted the importance of utilizing intelligent manufacturing techniques, tools, and methods, including predictive maintenance. This feature allows for the early identification of potential issues with machinery, preventing them from reaching critical stages. This paper proposes an intelligent predictive maintenance system for industrial equipment monitoring. The system integrates Industrial IoT, MQTT messaging and machine learning algorithms. Vibration, current and temperature sensors collect real-time data from electrical motors which is analyzed using five ML models to detect anomalies and predict failures, enabling proactive maintenance. The MQTT protocol is used for efficient communication between the sensors, gateway devices, and the cloud server. The system was tested on an operational motors dataset, five machine learning algorithms, namely k-nearest neighbor (KNN), supported vector machine (SVM), random forest (RF), linear regression (LR), and naive bayes (NB), are used to analyze and process the collected data to predict motor failures and offer maintenance recommendations. Results demonstrate the random forest model achieves the highest accuracy in failure prediction. The solution minimizes downtime and costs through optimized maintenance schedules and decisions. It represents an Industry 4.0 approach to sustainable smart manufacturing. © 2023 Lavoisier. All rights reserved.", "Keywords": "cloud computing/platform; fault diagnosis/detection; Industrial IoT (IIoT); machine learning algorithm; MQTT; predictive maintenance; prognostics and health management; random forest", "DOI": "10.18280/jesa.560414", "PubYear": 2023, "Volume": "56", "Issue": "4", "JournalId": 14230, "JournalTitle": "Journal Européen des Systèmes Automatisés", "ISSN": "1269-6935", "EISSN": "2116-7087", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Automated Manufacturing Eng. Dept., AlKhwarizmi College of Engineering, University of Baghdad, Baghdad, 10071, Iraq"}, {"AuthorId": 2, "Name": "Osamah F. <PERSON>", "Affiliation": "Information and Communication Eng. Dept., AlKhwarizmi College of Engineering, University of Baghdad, Baghdad, 10071, Iraq"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Information and Communication Eng. Dept., AlKhwarizmi College of Engineering, University of Baghdad, Baghdad, 10071, Iraq"}], "References": []}, {"ArticleId": 110107476, "Title": "Advanced efficient strategy for detection of dark objects based on spiking network with multi-box detection", "Abstract": "<p>Several deep learning algorithms have shown amazing performance for existing object detection tasks, but recognizing darker objects is the largest challenge. Moreover, those techniques struggled to detect or had a slow recognition rate, resulting in significant performance losses. As a result, an improved and accurate detection approach is required to address the above difficulty. The whole study proposes a combination of spiked and normal convolution layers as an energy-efficient and reliable object detector model. The proposed model is split into two sections. The first section is developed as a feature extractor, which utilizes pre-trained VGG16, and the second section of the proposal structure is the combination of spiked and normal Convolutional layers to detect the bounding boxes of images. We drew a pre-trained model for classifying detected objects. With state of the art Python libraries, spike layers can be trained efficiently. The proposed spike convolutional object detector (SCOD) has been evaluated on VOC and Ex-Dark datasets. SCOD reached 66.01% and 41.25% mAP for detecting 20 different objects in the VOC-12 and 12 objects in the Ex-Dark dataset. SCOD uses 14 Giga FLOPS for its forward path calculations. Experimental results indicated superior performance compared to Tiny YOLO, Spike YOLO, YOLO-LITE, Tinier YOLO and Center of loc + Xception based on mAP for the VOC dataset.</p>", "Keywords": "Deep learning; Object detection; Spiking neural network; Convolutional layer; Single shot multi-box detector (SSD); VGG-16", "DOI": "10.1007/s11042-023-16852-2", "PubYear": 2024, "Volume": "83", "Issue": "12", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei, People’s Republic of China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei, People’s Republic of China"}], "References": [{"Title": "Saliency guided faster-RCNN (SGFr-RCNN) model for object detection and recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "1687", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Object Detection through Modified YOLO Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}, {"Title": "CNN based feature extraction and classification for sign language", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "2", "Page": "3051", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A real-time and lightweight traffic sign detection method based on ghost-YOLO", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "17", "Page": "26063", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Autonomous vehicles decision-making enhancement using self-determination theory and mixed-precision neural networks", "Authors": "<PERSON>; <PERSON>; <PERSON> <PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Mask R-CNN for quality control of table olives", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "14", "Page": "21657", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Sign language recognition from digital videos using feature pyramid network with detection transformer", "Authors": "<PERSON>; <PERSON>d; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "14", "Page": "21673", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An intelligent framework to detect and generate alert while cattle lying on road in dangerous states using surveillance videos", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "22", "Page": "34589", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 110107802, "Title": "Applicability of double power law model for the statistical analysis of meteorological wind velocity time series data", "Abstract": "This paper is devoted to the statistical analysis of wind velocity variance time-series data obtained over the Bay of Bengal to investigate the turbulent energy cascade. The spectral distribution of wind velocity variance followed two different power laws. It was proposed that these power laws express the inertial subrange and the dissipation range. So, to cover these spectral ranges of frequencies, a double power-law (DPL) model was employed. The statistical analysis conducted on the power spectrum provided equations of both the power laws with high confidence levels. The Heaviside functions helped to formulate a single equation describing the entire spectral density. It was observed that the DPL model was very compliant with the -5/3 power law, and the statistical tests concluded that this model was a better fit with a close to 96% R2 score when compared to a 90% R2 score of –5/3 power law. Copyright © 2023 Inderscience Enterprises Ltd.", "Keywords": "double power law; DPL; inertial subrange; Kolmogorov Energy Spectra; time series analysis; turbulence; –5/3 power law", "DOI": "10.1504/IJCSM.2023.133632", "PubYear": 2023, "Volume": "18", "Issue": "2", "JournalId": 33075, "JournalTitle": "International Journal of Computing Science and Mathematics", "ISSN": "1752-5055", "EISSN": "1752-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>, Andhra Pradesh, Tada, 524401, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Powervector Tech. Pvt. Ltd., Karnataka, 590001, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Vellore Institute of Technology, Tamil Nadu, Vellore, 632014, India"}], "References": []}, {"ArticleId": 110107871, "Title": "GlassMessaging", "Abstract": "<p>Communicating with others while engaging in simple daily activities is both common and natural for people. However, due to the hands- and eyes-busy nature of existing digital messaging applications, it is challenging to message someone while performing simple daily activities. We present GlassMessaging, a messaging application on Optical See-Through Head-Mounted Displays (OHMDs), to support messaging with voice and manual inputs in hands- and eyes-busy scenarios. GlassMessaging is iteratively developed through a formative study identifying current messaging behaviors and challenges in common multitasking with messaging scenarios. We then evaluated this application against the mobile phone platform on varying texting complexities in eating and walking scenarios. Our results showed that, compared to phone-based messaging, GlassMessaging increased messaging opportunities during multitasking due to its hands-free, wearable nature, and multimodal input capabilities. The affordance of GlassMessaging also allows users easier access to voice input than the phone, which thus reduces the response time by 33.1% and increases the texting speed by 40.3%, with a cost in texting accuracy of 2.5%, particularly when the texting complexity increases. Lastly, we discuss trade-offs and insights to lay a foundation for future OHMD-based messaging applications.</p>", "Keywords": "", "DOI": "10.1145/3610931", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National University of Singapore, NUS-HCI Lab, School of Computing, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Singapore University of Technology and Design, Information Systems Technology and Design, Singapore and NUS-HCI Lab at the National University of Singapore"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Tsinghua University, Information Systems Technology and Design, Beijing, China and NUS-HCI Lab at the National University of Singapore"}, {"AuthorId": 4, "Name": "Sheng<PERSON> Zhao", "Affiliation": "National University of Singapore, NUS-HCI Lab, School of Computing, Singapore"}, {"AuthorId": 5, "Name": "<PERSON>n <PERSON>", "Affiliation": "National University of Singapore, NUS-HCI Lab, School of Computing, Singapore"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National University of Singapore, NUS-HCI Lab, School of Computing, Singapore"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of North Carolina at Chapel Hill, Computer Science, North Carolina, United States"}, {"AuthorId": 8, "Name": "<PERSON><PERSON> Wang", "Affiliation": "National University of Singapore, NUS-HCI Lab, School of Computing, Singapore"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Singapore, NUS-HCI Lab, School of Computing, Singapore"}], "References": [{"Title": "I Share, You Care: Private Status Sharing and Sender-Controlled Notifications in Mobile Instant Messaging", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "QwertyRing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "LSVP", "Authors": "<PERSON><PERSON>; Sheng<PERSON> Zhao", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Towards Indistinguishable Augmented Reality", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Voice messaging while driving: Effects on driving performance and attention", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "103692", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 110107879, "Title": "AttFL: A Personalized Federated Learning Framework for Time-series Mobile and Embedded Sensor Data Processing", "Abstract": "<p>This work presents AttFL, a federated learning framework designed to continuously improve a personalized deep neural network for efficiently analyzing time-series data generated from mobile and embedded sensing applications. To better characterize time-series data features and efficiently abstract model parameters, AttFL appends a set of attention modules to the baseline deep learning model and exchanges their feature map information to gather collective knowledge across distributed local devices at the server. The server groups devices with similar contextual goals using cosine similarity, and redistributes updated model parameters for improved inference performance at each local device. Specifically, unlike previously proposed federated learning frameworks, AttFL is designed specifically to perform well for various recurrent neural network (RNN) baseline models, making it suitable for many mobile and embedded sensing applications producing time-series sensing data. We evaluate the performance of AttFL and compare with five state-of-the-art federated learning frameworks using three popular mobile/embedded sensing applications (e.g., physiological signal analysis, human activity recognition, and audio processing). Our results obtained from CPU core-based emulations and a 12-node embedded platform testbed shows that AttFL outperforms all alternative approaches in terms of model accuracy and communication/computational overhead, and is flexible enough to be applied in various application scenarios exploiting different baseline deep learning model architectures.</p>", "Keywords": "", "DOI": "10.1145/3610917", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "JaeYeon Park", "Affiliation": "School of Integrated Technology, College of Computing, Yonsei University, Seodaemun-gu, Seoul, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Integrated Technology, College of Computing, Yonsei University, Seodaemun-gu, Seoul, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Integrated Technology, College of Computing, Yonsei University, Seodaemun-gu, Seoul, South Korea"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, The Ohio State University, Columbus, OH, United States"}, {"AuthorId": 5, "Name": "JeongGil Ko", "Affiliation": "School of Integrated Technology, College of Computing, Yonsei University, Seodaemun-gu, Seoul, South Korea"}], "References": [{"Title": "Deep Learning on Mobile and Embedded Devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "HeartQuake", "Authors": "Jaeyeon Park; <PERSON><PERSON><PERSON> Cho; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "PMC", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Enabling Real-time Sign Language Translation on Mobile Platforms with On-board Depth Cameras", "Authors": "HyeonJung Park; <PERSON><PERSON>; JeongGil Ko", "PubYear": 2021, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Robust Inertial Motion Tracking through Deep Sensor Fusion across Smart Earbuds and Smartphone", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "iMon", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; JeongG<PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Multi-center federated learning: clients clustering for better personalization", "Authors": "<PERSON><PERSON> Long; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "1", "Page": "481", "JournalTitle": "World Wide Web"}, {"Title": "Are You Left Out?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Template Matching Based Early Exit CNN for Energy-efficient Myocardial Infarction Detection on Low-power Wearable Devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "FLAME", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "SafeFac: Video-based smart safety monitoring for preventing industrial work accidents", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119397", "JournalTitle": "Expert Systems with Applications"}, {"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 110107882, "Title": "Environment-aware Multi-person Tracking in Indoor Environments with MmWave Radars", "Abstract": "<p>Device-free indoor localization and tracking using commercial millimeter wave radars have attracted much interest lately due to their non-intrusive nature and high spatial resolution. However, it is challenging to achieve high tracking accuracy due to rich multipath reflection and occlusion in indoor environments. Static objects with non-negligible reflectance of mmWave signals interact with moving human subjects and generate time-varying multipath ghosts and shadow ghosts, which can be easily confused as real subjects. To characterize the complex interactions, we first develop a geometric model that estimates the location of multipath ghosts given the locations of humans and static reflectors. Based on this model, the locations of static reflectors that form a reflection map are automatically estimated from received radar signals as a single person traverses the environment along arbitrary trajectories. The reflection map allows for the elimination of multipath and shadow ghost interference as well as the augmentation of weakly reflected human subjects in occluded areas. The proposed environment-aware multi-person tracking system can generate reflection maps with a mean error of 15.5cm and a 90-percentile error of 30.3cm, and achieve multi-person tracking accuracy with a mean error of 8.6cm and a 90-percentile error of 17.5cm, in four representative indoor spaces with diverse subjects using a single mmWave radar.</p>", "Keywords": "", "DOI": "10.1145/3610902", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Ministry of Education), School of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Ministry of Education), School of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 3, "Name": "Xiaoyang B<PERSON>", "Affiliation": "Queen <PERSON> University of London, London, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computing and Software, McMaster University, Hamilton, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Computer Sciences, Institute of Software, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Peking University, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Télécom SudParis, Institut Polytechnique de Paris, Palaiseau, France"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Télécom SudParis, Institut Polytechnique de Paris, Palaiseau, France"}, {"AuthorId": 9, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Ministry of Education), School of Computer Science, Peking University, Beijing, China, Telecom SudParis and Institut Polytechnique de Paris, Evry, France"}], "References": [{"Title": "A Survey on Device-free Indoor Localization and Tracking in the Multi-resident Environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "3D Point Cloud Generation with Millimeter-Wave Radar", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Constructing Floor Plan through Smoke Using Ultra Wideband Radar", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "CornerRadar", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 110107917, "Title": "<PERSON><PERSON><PERSON><PERSON>", "Abstract": "<p>Technical advances in the smart device market have fixated smartphones at the heart of our lives, warranting an ever more secure means of authentication. Although most smartphones have adopted biometrics-based authentication, after a couple of failed attempts, most users are given the option to quickly bypass the system with passcodes. To add a layer of security, two-factor authentication (2FA) has been implemented but has proven to be vulnerable to various attacks. In this paper, we introduce VibPath, a simultaneous 2FA scheme that can understand the user's hand neuromuscular system through touch behavior. VibPath captures the individual's vibration path responses between the hand and the wrist with the attention-based encoder-decoder network, authenticating the genuine users from the imposters unobtrusively. In a user study with 30 participants, VibPath achieved an average performance of 0.98 accuracy, 0.99 precision, 0.98 recall, 0.98 f1-score for user verification, and 94.3% accuracy for user identification across five passcodes. Furthermore, we also conducted several extensive studies, including in-the-wile, permanence, vulnerability, usability, and system overhead studies, to assess the practicability and viability of the VibPath from multiple aspects.</p>", "Keywords": "", "DOI": "10.1145/3610894", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University at Buffalo, Department of Computer Science and Engineering, Buffalo, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University at Buffalo, Department of Computer Science and Engineering, USA"}, {"AuthorId": 3, "Name": "<PERSON> <PERSON>", "Affiliation": "University at Buffalo, Department of Computer Science and Engineering, USA"}, {"AuthorId": 4, "Name": "Yincheng Jin", "Affiliation": "University at Buffalo, Department of Computer Science and Engineering, Buffalo, NY, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Hunan University, School of Design, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "South China University of Technology, School of Future Technology, China and University at Buffalo, Department of Computer Science and Engineering, USA"}], "References": [{"Title": "Adaptive Biometric Systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Listen to Your Fingers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Knowledge Distillation: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "6", "Page": "1789", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Vi<PERSON>in", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "PPGface", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "I Want to Know Your Hand", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "EFRing", "Authors": "Taizhou Chen; Tianpei Li; Xingyu Yang", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "WristAcoustic", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 110107934, "Title": "DC-PSENet: a novel scene text detection method integrating double ResNet-based and changed channels recursive feature pyramid", "Abstract": "<p>Due to the emergence and advancement of deep learning technologies, scene text detection is becoming more widespread in various fields. However, due to the complexity of distances, angles and backgrounds, the adjacent texts in images have the problem that the detection boxes are far away from the texts, i.e., a position is not accurate enough. In this paper, we propose a text detection method centered on double ResNet-based and changed channels recursive feature pyramid, which integrates ResNet50-Mish and Res2Net50-Mish, as well as using recursive feature pyramid with changed channels. Firstly, scene images are fed into ResNet50-Mish and Res2Net50-Mish of double ResNet-based, and results are passed through a weight-based addition step to generate the fused feature maps. Secondly, the processed feature maps of double ResNet-based are sent into changed channels recursive feature pyramid to obtain feature maps with enhanced feature information. Also, the relevant segmentation results are then obtained by concatenating and convoluting. Finally, the results are given to progressive scale expansion algorithm to output the location of texts in images. The proposed model is trained and tested on ICDAR15 and CTW1500 benchmark datasets. In terms of precision values, our method outperforms or is comparable to state-of-the-art methods. In particular, experimental results achieve 91.53% precision on ICDAR15 dataset and 84.89% precision on CTW-1500 dataset.</p>", "Keywords": "Computer vision; Scene text detection; Multi-scale; Feature pyramid network; Composite architectures", "DOI": "10.1007/s00371-023-03093-5", "PubYear": 2024, "Volume": "40", "Issue": "6", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Granular Computing and Application, Minnan Normal University, Zhangzhou, China; School of Mathematics and Statistics, Minnan Normal University, Zhangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Granular Computing and Application, Minnan Normal University, Zhangzhou, China; School of Mathematics and Statistics, Minnan Normal University, Zhangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Granular Computing and Application, Minnan Normal University, Zhangzhou, China; School of Mathematics and Statistics, Minnan Normal University, Zhangzhou, China; Corresponding author."}], "References": [{"Title": "Scene Text Detection and Recognition: The Deep Learning Era", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "1", "Page": "161", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Deep learning for detection of text polarity in natural scene images", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "431", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Scene text detection by adaptive feature selection with text scale-aware loss", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "1", "Page": "514", "JournalTitle": "Applied Intelligence"}, {"Title": "A decade: Review of scene text detection methods", "Authors": "<PERSON><PERSON>;  <PERSON><PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "", "Page": "100434", "JournalTitle": "Computer Science Review"}, {"Title": "Arbitrary-shaped scene text detection by predicting distance map", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "12", "Page": "14374", "JournalTitle": "Applied Intelligence"}, {"Title": "LIST: low illumination scene text detector with automatic feature enhancement", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "9-10", "Page": "3231", "JournalTitle": "The Visual Computer"}, {"Title": "Fruit detection and positioning technology for a Camellia oleifera C. Abel orchard based on improved YOLOv4-tiny model and binocular stereo vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118573", "JournalTitle": "Expert Systems with Applications"}, {"Title": "ODSPC: deep learning-based 3D object detection using semantic point cloud", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "2", "Page": "849", "JournalTitle": "The Visual Computer"}, {"Title": "ODRP: a new approach for spatial street sign detection from EXIF using deep learning-based object detection, distance estimation, rotation and projection system", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "2", "Page": "983", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 110107988, "Title": "Experimental study on the machining performance of nickel-based superalloy GH4169 milled by AWJ", "Abstract": "<p>The experimental study of AWJ milling performance of nickel-based superalloy materials is the basis for the study of milling process parameter optimization. Therefore, this paper focuses on the effect law of process parameters (waterjet pressure, abrasive flow rate, nozzle traverse speed, nozzle tilt angle and step-over distance) on milling performance (milling depth and surface roughness) when milling of nickel-based superalloys via AWJ. The prediction models of milling depth and surface roughness were established using the response surface method. The waterjet pressure is the factor that affects the milling depth and surface roughness the most, while the abrasive flow rate is the factor that affects the milling depth and surface roughness the least. In the interaction term of the milling depth model, the maximum milling depth is obtained at low waterjet pressure when the nozzle tilt angle is between 60° and 80°. While at high waterjet pressure, the maximum milling depth is obtained when the nozzle tilt angle is close to 90°; at any step-over distance, the effect on the milling depth is smaller when the nozzle traverse speed increases above 25 mm/s. In the interaction term of the surface roughness model, when the waterjet pressure is low, there is an optimal nozzle traverse speed to minimize the surface roughness value; at a higher nozzle traverse speed, the minimum surface roughness value occurs at minimum step-over distance. The validation experiment shows that the maximum relative error of the milling depth model is 10.09% and the maximum relative error of the surface roughness model is 12.12%, which proves the validity of the established milling depth model and surface roughness model.</p>", "Keywords": "Abrasive waterjet milling; Nickel-based superalloys; Milling depth; Surface roughness", "DOI": "10.1007/s00170-023-12327-8", "PubYear": 2023, "Volume": "129", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Jet Engineering Technologies (CaJET), Key Laboratory of High-Efficiency and Clean Mechanical Manufacture (Ministry of Education), National Demonstration Center for Experimental Mechanical Engineering Education (Shandong University), School of Mechanical Engineering, Shandong University, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Advanced Jet Engineering Technologies (CaJET), Key Laboratory of High-Efficiency and Clean Mechanical Manufacture (Ministry of Education), National Demonstration Center for Experimental Mechanical Engineering Education (Shandong University), School of Mechanical Engineering, Shandong University, Jinan, China; Shenzhen Research Institute of Shandong University, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Jet Engineering Technologies (CaJET), Key Laboratory of High-Efficiency and Clean Mechanical Manufacture (Ministry of Education), National Demonstration Center for Experimental Mechanical Engineering Education (Shandong University), School of Mechanical Engineering, Shandong University, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Advanced Jet Engineering Technologies (CaJET), Key Laboratory of High-Efficiency and Clean Mechanical Manufacture (Ministry of Education), National Demonstration Center for Experimental Mechanical Engineering Education (Shandong University), School of Mechanical Engineering, Shandong University, Jinan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Advanced Jet Engineering Technologies (CaJET), Key Laboratory of High-Efficiency and Clean Mechanical Manufacture (Ministry of Education), National Demonstration Center for Experimental Mechanical Engineering Education (Shandong University), School of Mechanical Engineering, Shandong University, Jinan, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Advanced Jet Engineering Technologies (CaJET), Key Laboratory of High-Efficiency and Clean Mechanical Manufacture (Ministry of Education), National Demonstration Center for Experimental Mechanical Engineering Education (Shandong University), School of Mechanical Engineering, Shandong University, Jinan, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, University of Science and Technology Beijing, Beijing, China"}], "References": [{"Title": "An investigation into the abrasive waterjet milling circular pocket on titanium alloy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "11-12", "Page": "4503", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Influence of abrasive water jet parameters on the surface integrity of Inconel 718", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "3-4", "Page": "997", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 110108054, "Title": "A transformer-based deep learning framework to predict employee attrition", "Abstract": "<p>In all areas of business, employee attrition has a detrimental impact on the accuracy of profit management. With modern advanced computing technology, it is possible to construct a model for predicting employee attrition to minimize business owners’ costs. Despite the reality that these types of models have never been evaluated under real-world conditions, several implementations were developed and applied to the IBM HR Employee Attrition dataset to evaluate how these models may be incorporated into a decision support system and their effect on strategic decisions. In this study, a Transformer-based neural network was implemented and was characterized by contextual embeddings adapting to tubular data as a computational technique for determining employee turnover. Experimental outcomes showed that this model had significantly improved prediction efficiency compared to other state-of-the-art models. In addition, this study pointed out that deep learning, in general, and Transformer-based networks, in particular, are promising for dealing with tabular and unbalanced data.</p>", "Keywords": "Data science; Machine learning; Artificial intelligence; Attrition prediction; Deep learning", "DOI": "10.7717/peerj-cs.1570", "PubYear": 2023, "Volume": "9", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, Shandong, China"}], "References": [{"Title": "Employee Attrition Estimation Using Random Forest Algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "49", "JournalTitle": "Baltic Journal of Modern Computing"}, {"Title": "Prediction of Employee Attrition Using Machine Learning and Ensemble Methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Al-H<PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "2", "Page": "110", "JournalTitle": "International Journal of Machine Learning and Computing"}]}, {"ArticleId": 110108127, "Title": "The Power of Speech in the Wild", "Abstract": "<p>Mobile phone sensing is increasingly being used in clinical research studies to assess a variety of mental health conditions (e.g., depression, psychosis). However, in-the-wild speech analysis -- beyond conversation detecting -- is a missing component of these mobile sensing platforms and studies. We augment an existing mobile sensing platform with a daily voice diary to assess and predict the severity of auditory verbal hallucinations (i.e., hearing sounds or voices in the absence of any speaker), a condition that affects people with and without psychiatric or neurological diagnoses. We collect 4809 audio diaries from N=384 subjects over a one-month-long study period. We investigate the performance of various deep-learning architectures using different combinations of sensor behavioral streams (e.g., voice, sleep, mobility, phone usage, etc.) and show the discriminative power of solely using audio recordings of speech as well as automatically generated transcripts of the recordings; specifically, our deep learning model achieves a weighted f-1 score of 0.78 solely from daily voice diaries. Our results surprisingly indicate that a simple periodic voice diary combined with deep learning is sufficient enough of a signal to assess complex psychiatric symptoms (e.g., auditory verbal hallucinations) collected from people in the wild as they go about their daily lives.</p>", "Keywords": "", "DOI": "10.1145/3610890", "PubYear": 2023, "Volume": "7", "Issue": "3", "JournalId": 35986, "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies", "ISSN": "", "EISSN": "2474-9567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dartmouth College, Computer Science, Hanover, NH, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Washington, Biomedical Informatics and Medical Education, Seattle, WA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Washington, Department of Psychiatry and Behavioral Sciences, Seattle, WA, USA"}, {"AuthorId": 4, "Name": "Subigya Nepal", "Affiliation": "Dartmouth College, Computer Science, Hanover, NH, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Washington, Department of Psychiatry and Behavioral Sciences, Seattle, WA, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of Washington, Biomedical Informatics and Medical Education, Seattle, WA, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "University of Washington, Department of Psychiatry and Behavioral Sciences, Seattle, WA, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Dartmouth College, Computer Science, Hanover, NH, USA"}], "References": [{"Title": "A Social Media Study on Demographic Differences in Perceived Job Satisfaction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "First-Gen <PERSON>", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 110108134, "Title": "Structural reliability analysis based on neural networks with physics-informed training samples", "Abstract": "In order to develop high-fidelity and high-efficiency machine learning (ML) models in reliability engineering, a novel ML approach based on Neural Networks (NN) with Physics-Informed Training Samples (PITS) is established, which is abbreviated as NN-PITS. The proposed NN-PITS framework uses data augmentation techniques to improve the training samples based on the properties of the failure surface, so that the physical information is embedded into the NN. Two approaches for embedding physical information are introduced. When the limit state equation (LSE) can be solved analytically, the training samples are extended to the failure surface based on the LSE, and then the physics-informed loss function is constructed. When the LSE is implicit or unsolvable analytically, a novel sampling method based on pseudo-probability distribution to generate the samples of required distribution is proposed, so as to obtain the PITS that are near the failure surface. Compared with the common data-driven NN model, the proposed NN-PITS framework can effectively utilize the physical information in reliability problem, so as to reduce the dependence on label samples. The proposed NN-PITS can be combined with finite element analysis (FEA) and applied to reliability engineering problems. Three engineering examples, including two static problems and one time-varying problem, are given to illustrate the good applicability and capability of the proposed methods for structural reliability analysis.", "Keywords": "Structural reliability ; Neural networks ; Physics-informed training samples ; Failure probability ; Surrogate models", "DOI": "10.1016/j.engappai.2023.107157", "PubYear": 2023, "Volume": "126", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Aeronautics, Northwestern Polytechnical University, Xi'an, Shaanxi, 710072, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Aeronautics, Northwestern Polytechnical University, Xi'an, Shaanxi, 710072, China;Corresponding author"}], "References": [{"Title": "A tutorial on solving ordinary differential equations using Python and hybrid physics-informed neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Felipe A.C<PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "103996", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Hybrid physics-informed neural networks for main bearing fatigue prognosis with visual grease inspection", "Authors": "<PERSON><PERSON>; Felipe A.<PERSON>", "PubYear": 2021, "Volume": "125", "Issue": "", "Page": "103386", "JournalTitle": "Computers in Industry"}, {"Title": "Data-driven failure prediction and RUL estimation of mechanical components using accumulative artificial neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "119", "Issue": "", "Page": "105749", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 110108270, "Title": "Passivity-Based State Estimation of Markov Jump Singularly Perturbed Neural Networks Subject to Sensor Nonlinearity and Partially Known Transition Rates", "Abstract": "<p>In this paper, the passivity-based state estimation problem is investigated for Markov jump singularly perturbed neural networks, in which the partially known transition rate matrix and the nonlinear characteristics of sensors are considered simultaneously. By using a new inequality, a novel perturbed parameter dependent Lyapunov function is constructed for Markov jump singularly perturbed neural networks. Based on those, some sufficient conditions are established to guarantee the stochastically mean-square stable for the considered system with the property of passivity. Besides, a less conservativeness state estimator design method is established for Markov jump singularly perturbed neural networks subject to sensor nonlinearity and partially known transition rates. At last, a numerical example is presented to demonstrate the validity of the obtained results.</p>", "Keywords": "Markov jump neural networks; Partial information; State estimation; Singularly perturbed systems", "DOI": "10.1007/s11063-023-11416-9", "PubYear": 2023, "Volume": "55", "Issue": "9", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Anhui Province Key Laboratory of Special Heavy Load Robot, Anhui University of Technology, Ma’anshan, China; School of Electrical and Information Engineering, Anhui University of Technology, Ma’anshan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Anhui Province Key Laboratory of Special Heavy Load Robot, Anhui University of Technology, Ma’anshan, China; School of Electrical and Information Engineering, Anhui University of Technology, Ma’anshan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Anhui Province Key Laboratory of Special Heavy Load Robot, Anhui University of Technology, Ma’anshan, China; School of Electrical and Information Engineering, Anhui University of Technology, Ma’anshan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Anhui University of Technology, Ma’anshan, China"}], "References": [{"Title": "Impact of fully connected layers on performance of convolutional neural networks for image classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "378", "Issue": "", "Page": "112", "JournalTitle": "Neurocomputing"}, {"Title": "Legendre Neural Network Method for Several Classes of Singularly Perturbed Differential Equations Based on Mapping and Piecewise Optimization Technology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "3", "Page": "2891", "JournalTitle": "Neural Processing Letters"}, {"Title": "Robust stability analysis of stochastic switched neural networks with parameter uncertainties via state-dependent switching law", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "813", "JournalTitle": "Neurocomputing"}, {"Title": "An LMI Based State Estimation for Fractional-Order Memristive Neural Networks with Leakage and Time Delays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "3", "Page": "2089", "JournalTitle": "Neural Processing Letters"}, {"Title": "A Survey of Encoding Techniques for Signal Processing in Spiking Neural Networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "6", "Page": "4693", "JournalTitle": "Neural Processing Letters"}, {"Title": "A neural network training algorithm for singular perturbation boundary value problems", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "607", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Mode-dependent guaranteed cost event-triggered synchronization for singular semi-markov jump neural networks with time delays", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "464", "Issue": "", "Page": "265", "JournalTitle": "Neurocomputing"}, {"Title": "Asynchronous $$H_\\infty $$ Filtering for Singular Markov Jump Neural Networks with Mode-Dependent Time-Varying Delays", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "6", "Page": "5439", "JournalTitle": "Neural Processing Letters"}, {"Title": "Observer-based l2−l∞ control for singularly perturbed semi-Markov jump systems with an improved weighted TOD protocol", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>g <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "9", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Bifurcation Mechanism for Fractional-Order Three-Triangle Multi-delayed Neural Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "5", "Page": "6125", "JournalTitle": "Neural Processing Letters"}, {"Title": "Partial-Neurons-Based $$H_{\\infty }$$ State Estimation for Time-Varying Neural Networks Subject to Randomly Occurring Time Delays under Variance Constraint", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "6", "Page": "8285", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 110108423, "Title": "Rehabilitation facilities' research needs and knowledge transfer in Southwest Germany: Data of the REHA-KNOWS study", "Abstract": "The REHA-KNOWS study was conducted to identify research needs, to explore attitudes and barriers towards health services research as well as to investigate knowledge transfer strategies in rehabilitation facilities in Southwest Germany. We designed a short online survey with 18 questions. From March to May 2023, we surveyed representatives of rehabilitation facilities in two German Federal States (Baden-Wuerttemberg and Saarland) using an online questionnaire provided via Unipark<sup>Ⓡ</sup>. The dataset contains all responses from n=88 individuals. We applied a list-based sampling approach and contacted n=206 rehabilitation facilities in total. Data collection started on March 2<sup>nd</sup> and the last response was received on May 2<sup>nd</sup>. As this sampling strategy allows multiple answers per facility, we applied an anonymized coding system to identify the affiliation of each respondent. In total, the 88 responses come from 74 centers. The dataset includes information on characteristics of the facility where the respondents work, the perceived benefits and barriers regarding health services research in practice, the need for research on specific topics and the transfer strategies established within the facilities. Analyses of these topics were performed in a descriptive and exploratory manner. This data offers the potential to be linked with data resulting from future research in this field in other Federal States of Germany. Further subgroup analyses can be performed with this dataset for specific research questions.", "Keywords": "Attitudes;Determinants;Evidence-based medicine;Health services research;Knowledge transfer;Knowledge-to-practice gap;Participatory research;Perceptions", "DOI": "10.1016/j.dib.2023.109632", "PubYear": 2023, "Volume": "51", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Institute of Medical Biometry and Statistics, Section of Health Care Research and Rehabilitation Research, Faculty of Medicine and Medical Center - University of Freiburg, Hugstetter Straße 49, 79106 Freiburg im Breisgau, Germany."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Medical Biometry and Statistics, Section of Health Care Research and Rehabilitation Research, Faculty of Medicine and Medical Center - University of Freiburg, Hugstetter Straße 49, 79106 Freiburg im Breisgau, Germany."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Medical Biometry and Statistics, Section of Health Care Research and Rehabilitation Research, Faculty of Medicine and Medical Center - University of Freiburg, Hugstetter Straße 49, 79106 Freiburg im Breisgau, Germany."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Medical Biometry and Statistics, Section of Health Care Research and Rehabilitation Research, Faculty of Medicine and Medical Center - University of Freiburg, Hugstetter Straße 49, 79106 Freiburg im Breisgau, Germany."}], "References": []}, {"ArticleId": 110108495, "Title": "Economic hubs and the domination of inter-regional ties in world city networks", "Abstract": "<p>Cities are widely considered as the lifeblood of a nations’ economy housing the bulk of industries, commercial and trade activities, and employment opportunities. Within this economic context, multinational corporations play an important role in this economic development of cities in particular, and subsequently the countries and regions they belong to, in general. As multinational companies are spread throughout the world by virtue of ownership–subsidiary relationship, these ties create complex inter-dependent networks of cities that shape and define socio-economic status, as well as macro-regional influences impacting the world economy. In this paper, we study these networks of cities formed as a result of ties between multinational firms. We analyze these networks using intra-regional, inter-regional, and hybrid ties (conglomerate integration) as spatial motifs defined by geographic delineation of world’s economic regions. We attempt to understand how global cities position themselves in spatial and economic geographies and how their ties promote regional integration along with global expansion for sustainable growth and economic development. We study these networks over four time periods from 2010 to 2019 and discover interesting trends and patterns. The most significant result is the domination of inter-regional motifs representing cross-regional ties among cities rather than national and regional integration. </p>", "Keywords": "Complex Networks; Multinational Firms; Networks of Cities; Network Motifs", "DOI": "10.1007/s13278-023-01134-4", "PubYear": 2023, "Volume": "13", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Computer Science, Institute of Business Administration, Karachi, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Computer Science, Institute of Business Administration, Karachi, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Administrative Studies, York University, Toronto, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Geography and Sustainability, University of Lausanne, Lausanne, Switzerland"}], "References": [{"Title": "Motif discovery in networks: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "", "Page": "100267", "JournalTitle": "Computer Science Review"}, {"Title": "Modeling supply-chain networks with firm-to-firm wire transfers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "190", "Issue": "", "Page": "116162", "JournalTitle": "Expert Systems with Applications"}]}]