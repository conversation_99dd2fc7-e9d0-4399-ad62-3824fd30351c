import urllib.request
from bs4 import BeautifulSoup
import re
import csv
import random
import time
import chardet  # 导入 chardet 库

# 电影《釜山行》的评论页面 URL
base_url = 'https://movie.douban.com/subject/25827935/comments'
# 随机选择 User-Agent 的列表
user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
    'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:54.0) Gecko/20100101 Firefox/54.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36'
]

# 存储所有评论数据的列表
all_comments = []

# 循环获取多页评论
for start in range(0, 200, 20):  # 这里获取前 10 页评论，可以根据需要调整
    page_num = start // 20 + 1
    url = f'{base_url}?start={start}&limit=20&status=P&sort=new_score'
    # 随机选择一个 User-Agent
    headers = {
        'User-Agent': random.choice(user_agents),
        'Referer': 'https://movie.douban.com/subject/25827935/',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
    }

    req = urllib.request.Request(url, headers=headers)
    try:
        response = urllib.request.urlopen(req)
        html = response.read()
        # 使用 chardet 检测编码
        encoding = chardet.detect(html)['encoding']
        # 如果检测失败，使用默认编码 utf-8
        if encoding is None:
            encoding = 'utf-8'
        html = html.decode(encoding)

        soup = BeautifulSoup(html, 'html.parser')
        comment_items = soup.find_all('div', class_='comment-item')
        for item in comment_items:
            # 提取评论者姓名
            name = item.find('span', class_='comment-info').find('a').text.strip()

            # 提取评分
            score_tag = item.find('span', class_=re.compile(r'rating'))
            score = score_tag['title'] if score_tag else '无评分'

            # 提取评论时间
            comment_time = item.find('span', class_='comment-time').text.strip()

            # 提取有用数量
            usefulNum = item.find('span', class_='votes').text.strip()

            # 提取评论内容
            comment = item.find('span', class_='short').text.strip()

            all_comments.append([name, score, comment_time, usefulNum, comment])

        print(f"成功访问第 {page_num} 页评论，当前共爬取到 {len(all_comments)} 条数据。")
    except Exception as e:
        print(f"访问第 {page_num} 页评论出错: {e}")
    # 设置请求间隔，模拟人类浏览行为
    time.sleep(random.uniform(3, 5))

# 将数据写入 CSV 文件
with open('douban.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
    writer = csv.writer(csvfile)
    # 写入表头
    writer.writerow(['name', 'score', 'time', 'usefulNum', 'comment'])
    # 写入评论数据
    writer.writerows(all_comments)

print("数据已成功写入 douban.csv 文件。")