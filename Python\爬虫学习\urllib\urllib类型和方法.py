import urllib.request

# 打开一个url
response = urllib.request.urlopen('http://www.baidu.com')

# 1个类型 6个方法
# response是一个HTTPResponse对象
# print(type(response))

# 读取响应内容 

# 按照一字节一字节的读
# print(response.read())
# 返回多少字节
# print(response.read(5)) # 返回5个字节

# 按照行来读取，返回一行
# print(response.readline())
# 按照行来读取，返回所有行
# print(response.readlines())

# 返回状态码 超过400说明出错了
# print(response.getcode())

# 返回url地址
# print(response.geturl())

# 获取状态信息
print(response.getheaders())