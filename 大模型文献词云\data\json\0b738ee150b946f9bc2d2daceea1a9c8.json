[{"ArticleId": 111057905, "Title": "The Design of Miniature Frequency Reconfigurable Antenna Based on Inductive Loading Technology", "Abstract": "<p>A circularly polarized (CP) and frequency reconfigurable microstrip antenna with loading inductive is presented in this paper. The designed antenna is comprised of a radiating patch, four short-circuited grounded metal posts, and four coupling branches. Each coupling branch has an end that is coupled to the shorted ground post and is also connected to the parasitic branches by means of a group of PIN diodes. By controlling the state of the PIN diodes connected to each parasitic branch, the working resonant frequency of the antenna can be changed. In order to further understand the mechanisms of operation of the antenna, the equivalent circuit model was built, and the circuit model of the antenna was analyzed, and this analysis was used for the development of the frequency reconfigurable microstrip patch antenna. Furthermore, the parameters of specific equivalent circuits can be solved by the three lengths of branch. Meanwhile, the calculated results derived from the given resonant frequency formula for the antenna are in good agreement with the simulation results of the antenna. Simulated results for the input impedance of the antenna are also in good agreement with the calculated values for the equivalent circuit. Finally, the antenna is fabricated and measured, and the measured results show that the antenna can not only achieve frequency reconfiguration at 1.14 GHz, 1.21 GHz, and 1.39 GHz but also accord well with the simulation value, while maintaining a compact size.</p>", "Keywords": "", "DOI": "10.1155/2023/2913342", "PubYear": 2023, "Volume": "2023", "Issue": "", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics Eng., Xi’an University of Post & Telecommunications, Xi’an, China"}, {"AuthorId": 2, "Name": "Qingsheng Zeng", "Affiliation": "School of Astronautics, Nanjing University of Aeronautics & Astronautics, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics Eng., Xi’an University of Post & Telecommunications, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics Eng., Xi’an University of Post & Telecommunications, Xi’an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics Eng., Xi’an University of Post & Telecommunications, Xi’an, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Electronics Eng., Xi’an University of Post & Telecommunications, Xi’an, China"}], "References": []}, {"ArticleId": 111057954, "Title": "ITNOW Volume 65 Issue 4, Full Issue", "Abstract": "", "Keywords": "", "DOI": "10.1093/itnow/bwad142", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [], "References": []}, {"ArticleId": 111057957, "Title": "Dynamic link prediction by learning the representation of node-pair via graph neural networks", "Abstract": "Many real-world networks are dynamic, whose structure keeps changing over time. Link prediction, which can foretell the emergence of future links, is one crucial task in dynamic network analysis . Compared to link prediction in static networks, it is more challenging and complicated in dynamic ones due to the dynamic nature. On the other hand, effective use of the information carried out by dynamic networks can enhance prediction accuracy. In this study, we presents a new end-to-end solution for dynamic link prediction, in which the representations of node-pairs can be effectively learned via an improved graph neural network and a nonlinear function by leveraging the structural information of individual snapshots, historical features from network evolution, and global knowledge of the collapsed network. The proposed method can effectively cope with the challenge of dynamic link prediction. Extensive tests are implemented on several dynamic networks to assess the prediction performance of our proposed method. The results on these networks demonstrate that our proposed method achieves superior effectiveness compared to the baselines in most cases.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122685", "PubYear": 2024, "Volume": "241", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou 730000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou 730000, China;Key Laboratory of Media Convergence Technology and Communication, Gansu Province, Lanzhou 730000, China;Corresponding author at: School of Information Science and Engineering, Lanzhou University, Lanzhou 730000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou 730000, China"}, {"AuthorId": 4, "Name": "Yiyang Sun", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou 730000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Zhao", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou 730000, China"}], "References": [{"Title": "Friend recommendation for cross marketing in online brand community based on intelligent attention allocation link prediction algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "112839", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Temporal Link Prediction: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "1", "Page": "213", "JournalTitle": "New Generation Computing"}, {"Title": "Link prediction of time-evolving network based on node ranking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105740", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Applications of link prediction in social networks: A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "166", "Issue": "", "Page": "102716", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "Network embedding based link prediction in dynamic networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "409", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Temporal link prediction in directed networks based on self-attention mechanism", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "173", "JournalTitle": "Intelligent Data Analysis"}]}, {"ArticleId": 111057987, "Title": "MOID: Many-to-One Patent Graph Embedding Base Infringement Detection Model", "Abstract": "With the increasing number of patent applications over the years, instances of patent infringement cases have become more frequent. However, traditional manual patent infringement detection models are no longer suitable for large-scale infringement detection. Existing automated models mainly focus on detecting one-to-one patent infringements, but neglect the many-to-one scenarios. The many-to-one patent infringement detection model faces some major challenges. First, the diversity of patent domains, complexity of content and ambiguity of features make it difficult to extract and represent patent features. Second, patent infringement detection relies on the correlation between patents and the comparison of contextual information as the key factors, but modeling the process and drawing conclusions present challenges. To address these challenges, we propose a many-to-one patent graph (MPG) embedding base infringement detection model. Our model extracts the relationship between keywords and patents, as well as association relation between keywords from many-to-one patent texts (MPTs), to construct a MPG. We obtain patent infringement features through graph embedding of MPG. By using these embedding features as input, the many-to-one infringement detection (MOID) model outputs the conclusion on whether a patent is infringed or not. The comparative experimental results indicate that our model improves accuracy, precision and F-measure by 3.81%, 11.82% and 5.37%, respectively, when compared to the state-of-the-art method.", "Keywords": "", "DOI": "10.1142/S0218194023420019", "PubYear": 2024, "Volume": "34", "Issue": "3", "JournalId": 12900, "JournalTitle": "International Journal of Software Engineering and Knowledge Engineering", "ISSN": "0218-1940", "EISSN": "1793-6403", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Liu", "Affiliation": "College of Computer Science, Inner Mongolia University, Hohhot, P. R. China;Institute of Scientific and Technical, Information of China, Beijing, P. R. China;National & Local Joint Engineering, Research Center of Intelligent Information, Processing Technology for Mongolian, Hohhot, P. R. China;Inner Mongolia Key Laboratory of Social, Computing and Data Processing, Hohhot, P. R. China;Inner Mongolia Engineering, Laboratory for Big Data Analysis Technology, Hohhot, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science, Inner Mongolia University, Hohhot, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science, Inner Mongolia University, Hohhot, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science, Inner Mongolia University, Hohhot, P. R. China"}], "References": [{"Title": "Patent analysis and classification prediction of biomedicine industry: SOM-KPCA-SVM model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "15-16", "Page": "10177", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Patent infringement analysis using a text mining technique based on SAO structure", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "125", "Issue": "", "Page": "103379", "JournalTitle": "Computers in Industry"}]}, {"ArticleId": *********, "Title": "Mathematical Modelling of the Cathodically Polarized Pipeline Electric Field Taking Into Account External and Internal Insulation Coating", "Abstract": "Formation of mathematical models for correct calculation of cathodic protection system parameters in order to protect pipelines from corrosion formation on pipe metal is an urgent task. However, not all models take into account the necessary factors influencing the reliability of calculated indicators, on the basis of which the analysis and appropriate decisions about further pipeline operation are made. The authors of the article considers a task of calculation of electric parameters of cathodic protection system for underground pipeline, located in a homogeneous half-space taking into account transition resistance of external and internal layers of insulation. To solve this problem the discretization method was used. © 2023 South Ural State University. All rights reserved.", "Keywords": "cathodic protection; external; internal insulation resistance; main pipeline; mathematical modeling of electric field", "DOI": "10.14529/mmp230102", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": [{"Title": "A framework of monitoring water pipeline techniques based on sensors technologies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "2", "Page": "47", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Adjustment of Planned Surveying and Geodetic Networks Using Second-Order Nonlinear Programming Methods", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "12", "Page": "131", "JournalTitle": "Computation"}]}, {"ArticleId": 111058009, "Title": "Multi-Party Secure Computation of Multi-Variable Polynomials", "Abstract": "The goal of decentralizing the calculations performed by participants in information interaction protocols is usually to improve the reliability and security of information systems. Decentralized computing is based on multi-party secure computing protocols (MSCP), which are usually not universal, but are built for pre-specific functions calculated by participants. In this work, an MSCP is constructed to calculate polynomial values from several variables over a finite field. The constructed protocol is based on linear secret separation schemes, and its characteristics, such as the power of valid and unauthorized coalitions, can be described in terms of the characteristics of linear codes and their Schur-<PERSON> degrees. Some codes and code constructs for which such characteristics can be determined analytically are described. © 2023 South Ural State University. All rights reserved.", "Keywords": "linear codes; secure computation", "DOI": "10.14529/mmp230107", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111058043, "Title": "Reviewer Thank you", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0743-7315(23)00178-8", "PubYear": 2024, "Volume": "184", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [], "References": []}, {"ArticleId": 111058107, "Title": "Car Detection for Smart Parking Systems Based on Improved YOLOv5", "Abstract": "<p>Nowadays, YOLOv5 is one of the most popular object detection network architectures used in real-time and industrial systems. Traffic management and regulation are typical applications. To take advantage of the YOLOv5 network and develop a parking management tool, this paper proposes a car detection network based on redesigning the YOLOv5 network architecture. This research focuses on network parameter optimization using lightweight modules from EfficientNet and PP-LCNet architectures. On the other hand, this work also presents an aerial view dataset for car detection tasks in the parking, named the AVPL. The proposed network is trained and evaluated on two benchmark datasets which are the Car Parking Lot Dataset and the Pontifical Catholic University of Parana+ Dataset and one proposed dataset. The experiments are reported on mAP@0.5 and mAP@0.5:0.95 measurement units. As a result, this network achieves the best performances at 95.8%, 97.4%, and 97.0% of mAP@0.5 on the Car Parking Lot Dataset, the Pontifical Catholic University of Parana+ Dataset, and the proposed AVPL dataset, respectively. A set of demonstration videos and the proposed dataset are available here: https://bit.ly/3YUoSwi .</p>", "Keywords": "", "DOI": "10.1142/S2196888823500185", "PubYear": 2024, "Volume": "11", "Issue": "2", "JournalId": 29506, "JournalTitle": "Vietnam Journal of Computer Science", "ISSN": "2196-8888", "EISSN": "2196-8896", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical, Electronic and Computer Engineering, University of Ulsan, Ulsan 44610, South Korea"}, {"AuthorId": 2, "Name": "<PERSON>an<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical, Electronic and Computer Engineering, University of Ulsan, Ulsan 44610, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical, Electronic and Computer Engineering, University of Ulsan, Ulsan 44610, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical, Electronic and Computer Engineering, University of Ulsan, Ulsan 44610, South Korea"}], "References": [{"Title": "Binary cross entropy with deep learning technique for Image classification", "Authors": "<PERSON><PERSON><PERSON> Dr.A", "PubYear": 2020, "Volume": "9", "Issue": "4", "Page": "5393", "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering"}]}, {"ArticleId": 111058176, "Title": "Scale-pyramid dynamic atrous convolution for pixel-level labeling", "Abstract": "For achieving better performance, the majority of deep convolutional neural networks have endeavored to increase the model capacity by adding more convolutional layers or increasing the size of the filters. Consequently, the computational cost increases proportionally with the model capacity. This problem can be alleviated by dynamic convolution. In the case of pixel-level labeling, existing pixel-level dynamic convolution methods have a smaller scanning area than ordinary convolution or image-level dynamic convolution and are thus unable to exploit fine contextual information. As a consequence, pixel-level dynamic convolution is more sensitive to large-scale varying objects and confusion categories. In this paper, we propose a scale-pyramid dynamic atrous convolution (SDAConv) and exploit multi-scale pixel-level features in finer granularity , in order to efficiently increase model capacity, exploring contextual information, capture detail information and alleviate large-scale variation problem at the same time. Through kernel engineering (instead of network engineering), SDAConv dynamically arranges atrous filters in the individual convolutional kernels over different semantic areas at dense scales in the spatial dimension. By simply replacing the regular convolution with SDAConv in SOTA architectures, extensive experiments on three public datasets, Cityscapes, PASCAL VOC 2012 and ADE20K benchmarks demonstrate the superior performance of SDAConv on pixel-level labeling tasks.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122695", "PubYear": 2024, "Volume": "241", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> Li", "Affiliation": "The Key Laboratory of Geographic Information Science (Ministry of Education of China), East China Normal University, Shanghai, 200241, China;Shanghai Key Laboratory of Multidimensional Information Processing, East China Normal University, Shanghai, 200241, China;School of Geographic Sciences, East China Normal University, Shanghai, 200241, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The Key Laboratory of Geographic Information Science (Ministry of Education of China), East China Normal University, Shanghai, 200241, China;School of Geographic Sciences, East China Normal University, Shanghai, 200241, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The Key Laboratory of Geographic Information Science (Ministry of Education of China), East China Normal University, Shanghai, 200241, China;Shanghai Key Laboratory of Multidimensional Information Processing, East China Normal University, Shanghai, 200241, China;School of Geographic Sciences, East China Normal University, Shanghai, 200241, China;Corresponding author at: The Key Laboratory of Geographic Information Science (Ministry of Education of China), East China Normal University, Shanghai, 200241, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Engineering University of PAP, Xi’an, 710086, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Sun Yat-sen University, Shenzhen, 518107, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Multidimensional Information Processing, East China Normal University, Shanghai, 200241, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON> Qi", "Affiliation": "School of Computer Science and Technology, University of Chinese Academy of Sciences, Beijing, 100049, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "The Key Laboratory of Geographic Information Science (Ministry of Education of China), East China Normal University, Shanghai, 200241, China;School of Geographic Sciences, East China Normal University, Shanghai, 200241, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, University of Ottawa, Ottawa K1N 6N5, Canada"}], "References": [{"Title": "Optimized HRNet for image semantic segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "174", "Issue": "", "Page": "114532", "JournalTitle": "Expert Systems with Applications"}, {"Title": "DSANet: Dilated spatial attention for real-time semantic segmentation in urban street scenes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115090", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Combining max-pooling and wavelet pooling strategies for semantic image segmentation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115403", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Superdense-scale network for semantic segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "504", "Issue": "", "Page": "30", "JournalTitle": "Neurocomputing"}, {"Title": "CSRNet: Cascaded Selective Resolution Network for real-time semantic segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118537", "JournalTitle": "Expert Systems with Applications"}, {"Title": "PCSCNet: Fast 3D semantic segmentation of LiDAR point cloud for autonomous car using point convolution and sparse convolution network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "118815", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 111058257, "Title": "Consensus-based generalized TODIM approach for occupational health and safety risk analysis with opinion interactions", "Abstract": "Occupational health and safety (OHS) risk analysis serves as a foundation for identifying, preventing, and controlling OHS hazards to reduce occupational accidents. As a representative risk analysis approach, <PERSON><PERSON><PERSON><PERSON> has been commonly applied to control hazards . However, current <PERSON><PERSON><PERSON><PERSON> studies ranked hazards without considering the consensus reaching process (CRP) with incomplete information, insufficient to tackle decision makers’ (DMs’) dissatisfaction. Besides, risk analysis mainly relies on DMs’ subjective assessments, where opinion interactions inevitably exist because of DMs’ communication during the assessment process. This paper aims to develop a hybrid generalized TODIM (an acronym in Portuguese for Interactive Multi-criteria Decision Making) approach in the Fine-Kinney framework, integrating CRP with dynamic social influence network (SIN), and probabilistic linguistic terms (PLTSs). The PLTSs are used to cope with the complex and incomplete DMs’ opinions. The dynamic SIN is proposed to calculate the weights of DMs and describe the opinion interactions considering the psychological behaviors of DMs. Then, a new CRP is developed including a two-fold personalized feedback mechanism based on the dynamic SIN. The generalized TODIM method is introduced to rank all identified potential occupational hazards based on the collective opinions after CRP. Finally, a numerical example is conducted to verify the efficiency of the proposed approach. Comparison and sensitivity studies are also carried out to test the rationality and efficiency of the proposed approach.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.111093", "PubYear": 2024, "Volume": "150", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Southeast University, Nanjing, Jiangsu 211189, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, Southeast University, Nanjing, Jiangsu 211189, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, Anhui Normal University, Wuhu, Anhui 230000, China"}], "References": [{"Title": "Large-Scale decision-making: Characterization, taxonomy, challenges and future directions from an Artificial Intelligence and applications perspective", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "84", "JournalTitle": "Information Fusion"}, {"Title": "Social network community analysis based large-scale group decision making approach with incomplete fuzzy preference relations", "Authors": "<PERSON><PERSON> Chu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "98", "JournalTitle": "Information Fusion"}, {"Title": "A consensus measure for group decision making with hesitant linguistic preference information based on double alpha-cut", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "106890", "JournalTitle": "Applied Soft Computing"}, {"Title": "Toward a scalable type-2 fuzzy model for resource-constrained project scheduling problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106988", "JournalTitle": "Applied Soft Computing"}, {"Title": "Consensus reaching with two-stage minimum adjustments in multi-attribute group decision making: A method based on preference-approval structure and prospect theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107349", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Occupational health and safety risk assessment using an integrated TODIM‐PROMETHEE model under linguistic spherical fuzzy environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "11", "Page": "6814", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Extension of safety and critical effect analysis to neutrosophic sets for the evaluation of occupational risks", "Authors": "<PERSON><PERSON>; Sel<PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107719", "JournalTitle": "Applied Soft Computing"}, {"Title": "An interval 2-Tuple linguistic Fine-<PERSON><PERSON> model for risk analysis based on extended ORESTE method with cumulative prospect theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "40", "JournalTitle": "Information Fusion"}, {"Title": "Consensus reaching with trust evolution in social network group decision making", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "188", "Issue": "", "Page": "116022", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Minimum conflict consensus with budget constraint based on social network analysis", "Authors": "Yuxiang Yuan; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "168", "Issue": "", "Page": "108098", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "New model for occupational health and safety risk assessment based on Fermatean fuzzy linguistic sets and CoCoSo approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "109262", "JournalTitle": "Applied Soft Computing"}, {"Title": "From diversity to consensus: Impacts of opinion evolution and psychological behaviours in failure mode and effect analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "109399", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 111058301, "Title": "Adversarial attacks against dynamic graph neural networks via node injection", "Abstract": "Dynamic graph neural networks (DGNNs) have demonstrated their extraordinary value in many practical applications. Nevertheless, the vulnerability of DNNs is a serious hidden danger as a small disturbance added to the model can markedly reduce its performance. At the same time, current adversarial attack schemes are implemented on static graphs, and the variability of attack models prevents these schemes from transferring to dynamic graphs. In this paper, we use the diffused attack of node injection to attack the DGNNs, and first propose the node injection attack based on structural fragility against DGNNs, named Structural Fragility-based Dynamic Graph Node Injection Attack (SFIA). SFIA firstly determines the target time based on the period weight. Then, it introduces a structural fragile edge selection strategy to establish the target nodes set and link them with the malicious node using serial inject. Finally, an optimization function is designed to generate adversarial features for malicious nodes. Experiments on datasets from four different fields show that SFIA is significantly superior to many comparative approaches. When the graph is injected with 1% of the original total number of nodes through SFIA, the link prediction Recall and MRR of the target DGNN link decrease by 17.4% and 14.3% respectively, and the accuracy of node classification decreases by 8.7%.", "Keywords": "Dynamic graph neural network ; Adversarial attack ; Malicious node ; Vulnerability", "DOI": "10.1016/j.hcc.2023.100185", "PubYear": 2024, "Volume": "4", "Issue": "1", "JournalId": 85789, "JournalTitle": "High-Confidence Computing", "ISSN": "2667-2952", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Ocean University of China, Qingdao 266100, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Ocean University of China, Qingdao 266100, China;Corresponding author"}], "References": [{"Title": "Dynamic graph convolutional networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "107000", "JournalTitle": "Pattern Recognition"}, {"Title": "Scalable attack on graph data by injecting vicious nodes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "5", "Page": "1363", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Adversarial Attacks and Defenses on Graphs", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "2", "Page": "19", "JournalTitle": "ACM SIGKDD Explorations Newsletter"}, {"Title": "Generative Adversarial Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 111058309, "Title": "Discovering e-commerce user groups from online comments: An emotional correlation analysis-based clustering method", "Abstract": "Platform merchants mine user clusters and their characteristics to assist in precision marketing. In view of the information overload in e-commerce reviews, machine methods are needed to efficiently obtain clustering information from text. This study innovatively integrated the emotional correlation analysis model and Self-organizing Map (SOM) in application, to construct fine-grained user emotion vector based on review text and perform visual cluster analysis, which helped quickly mine user clustering and characteristics from review text. The result of empirical analysis based on real reviews of Amazon books showed that the proposed method had the average precision as 0.71, confirming that the clustering method integrating the emotional correlation analysis model and SOM could efficiently mine user groups and match appropriate marketing strategies, which will help platform merchants carry out precision marketing. The study makes contributions to the application and innovation of researches in the field of user clustering and e-commerce precision marketing.", "Keywords": "E-commerce ; Commodity review ; Sentiment analysis ; the emotional correlation analysis model ; SOM ; User clustering", "DOI": "10.1016/j.compeleceng.2023.109035", "PubYear": 2024, "Volume": "113", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Management School, Jiangsu University, Zhenjiang 212013, China;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Management School, Jiangsu University, Zhenjiang 212013, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Management School, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Management School, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Management School, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Southwest University, Chongqing 400700, China"}], "References": [{"Title": "Online Review Helpfulness and Firms’ Financial Performance: An Empirical Study in a Service Industry", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "4", "Page": "421", "JournalTitle": "International Journal of Electronic Commerce"}, {"Title": "An explainable sentiment prediction model based on the portraits of users sharing representative opinions in social sensors", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "10", "Page": "155014772110337", "JournalTitle": "International Journal of Distributed Sensor Networks"}]}, {"ArticleId": 111058325, "Title": "Modelling of Water Level Oscillations in the Don Delta According to Wind Situation Forecasts", "Abstract": "We present a mathematical model consisting of a model of the hydrodynamics of the Azov Sea and a model of water movement in the Don channel. This model makes it possible to calculate fluctuations in the water level in the mouth area of the Don, depending on the wind situation over the Azov Sea. This approach makes it possible to predict in advance the change in the level surface in the main branches of the Don River. The movement of water in the Azov Sea (the marine part of the model) is described by shallow water equations. Hydrodynamics in the main branches of the Don Delta is considered as the movement of water in an open channel (channel part of the model). Both parts of the model (marine and channel ones) are solved by finite difference methods. The predicted wind load over the water area of the Sea of Azov was set at points corresponding to the location of coastal hydrometeorological stations with subsequent interpolation for the entire water area. Two problems of forecasting water level fluctuations are considered. In the first problem, water level fluctuations are calculated depending on the morning forecast of the wind situation with a lead time of three days. In the second problem, we study the dynamics of changes in the quality of the forecast of the water level over a fixed period of time as it approaches its beginning. The quality of the forecasts is determined by comparing the predicted and observed level values. The result of the comparison shows that the presented model adequately describes the hydrodynamics in the Don Delta region depending on the wind situation over the Azov Sea. © 2023 South Ural State University. All rights reserved.", "Keywords": "channel flow; computational experiment; Don delta; surge phenomenon; unsteady flow", "DOI": "10.14529/mmp230307", "PubYear": 2023, "Volume": "16", "Issue": "3", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111058357, "Title": "Disturbance rejection with compensation on features", "Abstract": "In pattern recognition tasks, the information from system input is modeled through a series of nonlinear operations, which include but not limited to feature extraction, regression, and classification. Both theoretically and practically, these operations are inevitably subject to internal modeling error and external disturbance , resulting at a performance challenge. Those state-of-the-art methods, e.g. Convolutional Neural Network and Transformer, still display significant instabilities and failures under practical applications, so comes a lack of generalization. Consequently, the more robust pattern recognition methods and related theories still merit a further study. This paper firstly reviews those state-of-the-art technologies in the field. The bottleneck of performances in those latest researches is associated with a lack of disturbance estimation and corresponding compensation. Therefore, the implications of disturbance rejection in pattern recognition field are further discussed from a control point of view. Then, the open problems are summarized. Ultimately, a discussion of the potential solutions, which is related to the application of compensation on features, is given to highlight the future study. Through the systematic review in this paper, the disturbance rejection in pattern recognition is developed into a control problem. Hopefully, more effective control technologies for the compensation on features can be used to improve the robustness of pattern recognition theoretically and practically.", "Keywords": "", "DOI": "10.1016/j.patcog.2023.110129", "PubYear": 2024, "Volume": "147", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "Xiaobo Hu", "Affiliation": "Joint Institute of UM-SJTU, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Center of Intelligent Robotics, Shanghai Jiao Tong University, Shanghai, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Joint Institute of UM-SJTU, Shanghai Jiao Tong University, Shanghai, China"}], "References": [{"Title": "A survey on 3D mask presentation attack detection and countermeasures", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107032", "JournalTitle": "Pattern Recognition"}, {"Title": "Systematic review of 3D facial expression recognition methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "", "Page": "107108", "JournalTitle": "Pattern Recognition"}, {"Title": "Aleatoric and epistemic uncertainty in machine learning: an introduction to concepts and methods", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "3", "Page": "457", "JournalTitle": "Machine Learning"}, {"Title": "Detection of COVID-19 from speech signal using bio-inspired based cepstral features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Ganapati Panda", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "107999", "JournalTitle": "Pattern Recognition"}, {"Title": "Contour-enhanced attention CNN for CT-based COVID-19 segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "125", "Issue": "", "Page": "108538", "JournalTitle": "Pattern Recognition"}, {"Title": "Towards Disturbance Rejection in Feature Pyramid Network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "4", "Page": "946", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Exploring transformers for behavioural biometrics: A case study in gait recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "109798", "JournalTitle": "Pattern Recognition"}, {"Title": "Sorting of coal and coal waste with transferred deep kernel learning", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "274", "JournalTitle": "International Journal of Systems, Control and Communications"}]}, {"ArticleId": 111058380, "Title": "Implementasi Cloud Computing Cluster Menggunakan Ovirt Untuk Layanan Web", "Abstract": "Cloud Computing Cluster&nbsp;terdiri dari&nbsp;server&nbsp;yang berbeda dan bekerja sama untuk memastikan bahwa downtime dari sumber daya kritis dikurangi seminimal mungkin. Tujuannya yaitu memastikan bahwa sumber daya kritis mencapai ketersediaan maksimum yang disebut sebagai high availability. Untuk penerapan metode cluster cloud computing membutuhkan platform yang memiliki tools yang dapat menunjang kebutuhan tersebut. Salah satu platform yang dapat digunakan&nbsp;adalah Ovirt. Ovirt adalah solusi virtualisasi berbasis open source yang dirancang untuk mengelola seluruh infrastruktur instansi. Ovirt menggunakan hypervisor KVM dan dibangun di atas beberapa proyek komunitas lainnya, termasuk libvirt, Gluster, PatternFly, dan Ansible. Secara teknis Ovirt dapat memiliki teknologi Clustering dan memiliki High Availability pada environment yang menggunakan hypervisor lebih dari satu. Kegunaannya adalah jika salah satu hypervisor bermasalah maka Virtual Machine (VM) yang ada didalamnya dapat berpindah ke hypervisor lain. Hasil dari penelitian ini adalah tercapainya high availability pada layanan web server dimana layanan web masih dapat diakses saat ada dua node dimatikan dan saat salah satu server dimatikan. Penggunaan CPU dan memori meningkat 1% ketika ada beberapa jumlah permintaan http request saat beberapa node dimatikan dan juga saat salah satu server fisik dimatikan.", "Keywords": "Cloud Computing; Cluster; Ovirt", "DOI": "10.35143/jkt.v9i1.6018", "PubYear": 2023, "Volume": "", "Issue": "Vol. 9 No. 1 (2023)", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Politeknik Caltex Riau"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}], "References": []}, {"ArticleId": 111058453, "Title": "Diagnosis of photovoltaic faults using digital twin and PSO-optimized shifted window transformer", "Abstract": "This work proposes a new method for the detection, localization , and classification of grid-connected photovoltaic (PV) array faults. Line-to-line, open-module, shorted-module, open-string, and shorted-string faults as well as partial shading conditions are studied. The proposed method has two stages, which are (1) detection and localization of faults and (2) classification of faults. In the first stage, detection and localization are performed using a digital twin (DT) by analyzing the current ratio of each PV array. The measured DC (direct current) power after the operation of the DC/DC boost converter in the physical object is firstly converted into a 2-dimensional image using a recurrence plot (RP) and is then inputted to the second stage. A deep learning-based shifted windows (swin) transformer optimized by particle swarm optimization (PSO) is used in the classification stage, eliminating the need for model tuning by trial-and-error. A PV system of ten arrays with 49 kW is studied. The coefficients of determination (R <sup>2</sup>) between the results of the digital object (digital twin) and the physical object for different scenarios demonstrate the accuracy and success of the digital twin. R<sup>2</sup> values of 0.99988 for varying irradiation with constant temperature and 0.97923 for constant irradiation with varying temperature indicate strong correlations between the digital and physical objects, further confirming the applicability of the digital twin in PV fault detection. The comparative evaluation of the PSO-optimized swin transformer against classical machine learning algorithms and state-of-the-art convolutional neural networks reveals the superior performance of the proposed method. It achieves an outstanding classification accuracy of 98.55%, demonstrating its ability to effectively classify various types of PV faults. The results of the area under the curve (AUC) of the receiver operating characteristic (ROC) curve, which measures the trade-off between true positive rate (TPR) and false positive rate (FPR), further illustrate the effectiveness of the proposed method for PV fault classification.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.111092", "PubYear": 2024, "Volume": "150", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Chung Yuan Christian University, Taoyuan 32023, Taiwan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Chung Yuan Christian University, Taoyuan 32023, Taiwan;University of Perpetual Help System DALTA, Las Piñas City, 1740, Philippines"}], "References": [{"Title": "Optimized PSO algorithm based on the simplicial algorithm of fixed point theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "7", "Page": "2009", "JournalTitle": "Applied Intelligence"}, {"Title": "Review of digital twin about concepts, technologies, and industrial applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "346", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 111058518, "Title": "Tweaked optimization based quality aware VM selection method for effectual placement strategy", "Abstract": "Cloud computing has become a standard and promising distributed computing framework for the provision of on-demand computing resources and pay-per-use concepts. Operations of these computing resources result in maximum power consumption, enraged cost and high Co<sub>2</sub> emission to the environment. The major difficulties faced when accessing cloud data center are SLA violations, increased time, less utilization of resources, high consumption of power and energy. Hence, considering these difficulties, a novel virtual machine (VM) selection approach is proposed to minimize the constraints while maintaining the SLA. First, based on the assumptions of VMs and physical machines (PMs), the overutilized hosts are detected using a static threshold approach, while underutilized hosts are identified based on the utilized resources. After load detection, the VMs that need to be migrated over other PMs are selected using the tweaked chimp optimization algorithm (TCOA). After selecting VMs without influencing the capacity of other VMs, the placement process is performed over other PMs using a power aware best fit decreasing approach. The proposed approach can greatly improve the QoS by selecting the optimal VMs that need to be migrated. Cloudsim is used as a simulation tool, and the results are compared with existing techniques in terms of migration time, energy consumption, SLA violation per host and so on to prove the superiority. The energy consumption of the proposed model is obtained to be 195.3 kWh, the overall SLA violation rate is attained to be 0.032%, and the migration time for 500 virtual machines is 8.72 s", "Keywords": "", "DOI": "10.1016/j.suscom.2023.100939", "PubYear": 2024, "Volume": "41", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Information Science, Raiganj University, West Bengal 733134, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Computer Science and Engineering, Narula Institute of Technology, Kolkata, West Bengal 700109, India"}], "References": [{"Title": "Energy-efficient and quality-aware VM consolidation method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "789", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Chimp optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113338", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A metaheuristic method for joint task scheduling and virtual machine placement in cloud data centers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "201", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Multi-resource balance optimization for virtual machine placement in cloud data centers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106866", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Min-max exclusive virtual machine placement in cloud computing for scientific data environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "An improved thermodynamic simulated annealing-based approach for resource-skewness-aware and power-efficient virtual machine consolidation in cloud datacenters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "7", "Page": "5233", "JournalTitle": "Soft Computing"}, {"Title": "Multi-objective optimization for VM placement in homogeneous and heterogeneous cloud service provider data centers", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "6", "Page": "1255", "JournalTitle": "Computing"}, {"Title": "Efficient VM Selection Strategies in Cloud Datacenter Using Fuzzy Soft Set", "Authors": "<PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON>.", "PubYear": 2021, "Volume": "33", "Issue": "5", "Page": "153", "JournalTitle": "Journal of Organizational and End User Computing"}, {"Title": "An ACO-based multi-objective optimization for cooperating VM placement in cloud data center", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "3", "Page": "3093", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "PACO-VMP: Parallel Ant Colony Optimization for Virtual Machine Placement", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "174", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Adaptive Computational Solutions to Energy Efficiency in Cloud Computing Environment Using VM Consolidation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "3", "Page": "1789", "JournalTitle": "Archives of Computational Methods in Engineering"}]}, {"ArticleId": 111058539, "Title": "Long-Term Spatiotemporal Oceanographic Data from the Northeast Pacific Ocean: 1980–2022 Reconstruction Based on the Korea Oceanographic Data Center (KODC) Dataset", "Abstract": "<p>The Korea Oceanographic Data Center (KODC), overseen by the National Institute of Fisheries Science (NIFS), is a pivotal hub for collecting, processing, and disseminating marine science data. By digitizing and subjecting observational data to rigorous quality control, the KODC ensures accurate information in line with international standards. The center actively engages in global partnerships and fosters marine data exchange. A wide array of marine information is provided through the KODC website, including observational metadata, coastal oceanographic data, real-time buoy records, and fishery environmental data. Coastal oceanographic observational data from 207 stations across various sea regions have been collected biannually since 1961. This dataset covers 14 standard water depths; includes essential parameters, such as temperature, salinity, nutrients, and pH; serves as the foundation for news, reports, and analyses by the NIFS; and is widely employed to study seasonal and regional marine variations, with researchers supplementing the limited data for comprehensive insights. The dataset offers information for each water depth at a 1 m interval over 1980–2022, facilitating research across disciplines. Data processing, including interpolation and quality control, is based on MATLAB. These data are classified by region and accessible online; hence, researchers can easily explore spatiotemporal trends in marine environments.</p>", "Keywords": "", "DOI": "10.3390/data8120175", "PubYear": 2023, "Volume": "8", "Issue": "12", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Marine Domain and Security Research Department, Korea Institute of Ocean Science & Technology (KIOST), Busan 49111, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Marine Domain and Security Research Department, Korea Institute of Ocean Science & Technology (KIOST), Busan 49111, Republic of Korea; Corresponding author"}], "References": []}, {"ArticleId": 111058583, "Title": "An overview of simultaneous localisation and mapping: towards multi-sensor fusion", "Abstract": "", "Keywords": "", "DOI": "10.1080/00207721.2023.2282409", "PubYear": 2024, "Volume": "55", "Issue": "3", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Control Science and Engineering, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Dalian University of Technology, Dalian, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Information Science and Technology College, Dalian Maritime University, Dalian, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Artificial Intelligence, Dalian Maritime University, Dalian, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Control Science and Engineering, Dalian University of Technology, Dalian, People’s Republic of China"}], "References": [{"Title": "DVL-SLAM: sparse depth enhanced direct visual-LiDAR SLAM", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "2", "Page": "115", "JournalTitle": "Autonomous Robots"}, {"Title": "Keyframe‐based thermal–inertial odometry", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "4", "Page": "552", "JournalTitle": "Journal of Field Robotics"}, {"Title": "GR-LOAM: LiDAR-based sensor fusion SLAM for ground robots on complex terrain", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "140", "Issue": "", "Page": "103759", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "VIR-SLAM: visual, inertial, and ranging SLAM for single and multi-robot systems", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "45", "Issue": "6", "Page": "905", "JournalTitle": "Autonomous Robots"}, {"Title": "PC-SD-VIO: A constant intensity semi-direct monocular visual-inertial odometry with online photometric calibration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "146", "Issue": "", "Page": "103877", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Point‐LIO: Robust High‐Bandwidth Light Detection and Ranging Inertial Odometry", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "7", "Page": "2200459", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 111058627, "Title": "Augmented feedback modes during functional grasp training with an intelligent glove and virtual reality for persons with traumatic brain injury", "Abstract": "<p> Introduction: Physical therapy is crucial to rehabilitating hand function needed for activities of daily living after neurological traumas such as traumatic brain injury (TBI). Virtual reality (VR) can motivate participation in motor rehabilitation therapies. This study examines how multimodal feedback in VR to train grasp-and-place function will impact the neurological and motor responses in TBI participants ( n = 7) compared to neurotypicals ( n = 13). </p><p> Methods: We newly incorporated VR with our existing intelligent glove system to seamlessly enhance the augmented visual and audio feedback to inform participants about grasp security. We then assessed how multimodal feedback (audio plus visual cues) impacted electroencephalography (EEG) power, grasp-and-place task performance (motion pathlength, completion time), and electromyography (EMG) measures. </p><p> Results: After training with multimodal feedback, electroencephalography (EEG) alpha power significantly increased for TBI and neurotypical groups. However, only the TBI group demonstrated significantly improved performance or significant shifts in EMG activity. </p><p> Discussion: These results suggest that the effectiveness of motor training with augmented sensory feedback will depend on the nature of the feedback and the presence of neurological dysfunction. Specifically, adding sensory cues may better consolidate early motor learning when neurological dysfunction is present. Computerized interfaces such as virtual reality offer a powerful platform to personalize rehabilitative training and improve functional outcomes based on neuropathology. </p>", "Keywords": "Traumatic Brain Injury; virtual reality; motor rehabilitation; Sensory feedback; hand grasp; Physical Therapy", "DOI": "10.3389/frobt.2023.1230086", "PubYear": 2023, "Volume": "10", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, United States; Movement Control Rehabilitation (MOCORE) Laboratory, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, United States; Movement Control Rehabilitation (MOCORE) Laboratory, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, United States; Movement Control Rehabilitation (MOCORE) Laboratory, United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Center for Mobility and Rehabilitation Engineering Research, United States"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, United States; Movement Control Rehabilitation (MOCORE) Laboratory, United States"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Mobility and Rehabilitation Engineering Research, United States"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, United States; Movement Control Rehabilitation (MOCORE) Laboratory, United States"}], "References": [{"Title": "Investigating features in augmented visual feedback for virtual reality rehabilitation of upper-extremity function through isometric muscle control", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "159", "JournalTitle": "Frontiers in Virtual Reality"}]}, {"ArticleId": 111058706, "Title": "Digital twin-enabled collision early warning system for marine piling: Application to a wharf project in China", "Abstract": "The safety and effectiveness of marine piling construction are hindered by the display of imprecise spatial information and the potential for collisions due to the complexity of pile positions and attitudes. Digital twin (DT) can solve the above problems in piling construction because of their inherent three-dimensionality and real-time feedback. The present study proposes a system that utilizes the DT framework to construct a collision early warning system intended for marine piling. A five-dimensional model is used to introduce the overall architecture. The main twin objects are piles. To maximize the effectiveness of DT, the five-dimensional model is composed of four independently maintainable development modules. Consequently, a prototype of the system is created. The core of the system comprises an algorithm for pile positioning and an algorithm for collision early warning, especially for complex pile groups. Two distinct implementations are identified: one involves controlling the virtual with the real, while the other involves controlling the real with the virtual. Field experiments are conducted with the aim of establishing a benchmark for collision early warning during the construction process . The prototype of the system is capable of producing a simulated environment through a dual-layer mapping approach, which involves pile-boat mapping and early-warning feedback mapping. The presented system in a practical wharf project showcases both the capacity for three-dimensional visualization of collision warning and the ability to provide feedback for boat control. The research enables more intuitive and effective pile positioning.", "Keywords": "", "DOI": "10.1016/j.aei.2023.102269", "PubYear": 2024, "Volume": "59", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulic Engineering Intelligent Construction and Operation, Tianjin University, Tianjin 300350, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>ng Ren", "Affiliation": "State Key Laboratory of Hydraulic Engineering Intelligent Construction and Operation, Tianjin University, Tianjin 300350, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulic Engineering Intelligent Construction and Operation, Tianjin University, Tianjin 300350, China;Corresponding author"}, {"AuthorId": 4, "Name": "Ting Kong", "Affiliation": "Department of Building and Real Estate, The Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Building and Real Estate, The Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 6, "Name": "Huijing Tian", "Affiliation": "CCCC (Tianjin) Eco-Environmental Protection Design & Research Institute Co., Ltd., Tianjin 300461, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "CCCC (Tianjin) Eco-Environmental Protection Design & Research Institute Co., Ltd., Tianjin 300461, China"}], "References": [{"Title": "A review of digital twin in product design and development", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "101297", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Rapid construction method of equipment model for discrete manufacturing digital twin workshop system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "102309", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Just Trolley: Implementation of industrial IoT and digital twin-enabled spatial-temporal traceability and visibility for finished goods logistics", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101571", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "From simple digital twin to complex digital twin Part I: A novel modeling method for multi-scale and multi-scenario digital twin", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "101706", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Digital twin-driven intelligent production line for automotive MEMS pressure sensors", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101779", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A systematic review of digital twin about physical entities, virtual models, twin data, and applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101876", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "From simple digital twin to complex digital twin part II: Multi-scenario applications of digital twin shop floor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "", "Page": "101915", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 111058805, "Title": "Modeling and shadowing paraconsistent BDI agents", "Abstract": "The Bdi model of rational agency has been studied for over three decades. Many robust multiagent systems have been developed, and a number of Bdi logics have been studied. Following this intensive development phase, the importance of integrating Bdi models with inconsistency handling and revision theory have been emphasized. There is also a demand for a tighter connection between Bdi -based implementations and Bdi logics. In this paper, we address these postulates by introducing a novel, paraconsistent logical Bdi model close to implementation, with building blocks that can be represented as Sql /rule-based databases. Importantly, tractability is achieved by reasoning as querying. This stands in a sharp contrast to the high complexity of known Bdi logics. We also extend belief shadowing, a shallow and lightweight alternative to deep and computationally demanding belief revision, to encompass agents’ motivational attitudes.", "Keywords": "Beliefs-Desires-Intentions models; Paraconsistent reasoning; Doxastic reasoning; Shadowing; Reasoning by querying; 93A16; 68T42; 03B42; 03B50; 03B53; 68T27", "DOI": "10.1007/s10472-023-09902-w", "PubYear": 2024, "Volume": "92", "Issue": "4", "JournalId": 27228, "JournalTitle": "Annals of Mathematics and Artificial Intelligence", "ISSN": "1012-2443", "EISSN": "1573-7470", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informatics, University of Warsaw, Warsaw, Poland; QED Software, Warsaw, Poland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informatics, University of Warsaw, Warsaw, Poland; Department of Computer and Information Science, Linköping University, Linköping, Sweden; Corresponding author."}], "References": [{"Title": "Agent programming in the cognitive era", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "2", "Page": "1", "JournalTitle": "Autonomous Agents and Multi-Agent Systems"}, {"Title": "A Review of Agent-Based Programming for Multi-Agent Systems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "2", "Page": "16", "JournalTitle": "Computers"}]}, {"ArticleId": 111058845, "Title": "Responsible Recommendation Services with Blockchain Empowered Asynchronous Federated Learning", "Abstract": "<p> Privacy and trust are highly demanding in practical recommendation engines. Although Federated Learning (FL) has significantly addressed privacy concerns, commercial operators are still worried about several technical challenges while bringing FL into production. Additionally, classical FL has several intrinsic operational limitations such as single-point failure, data and model tampering, and heterogenic clients participating in the FL process. To address these challenges in practical recommenders, we propose a responsible recommendation generation framework based on blockchain-empowered asynchronous FL that can be adopted for any model-based recommender system. In standard FL settings, we build an additional aggregation layer in which multiple trusted nodes guided by a mediator component perform gradient aggregation to achieve an optimal model locally in a parallel fashion. The mediator partitions users into K clusters, and each cluster is represented by a cluster head. Once a cluster gets semi-global convergence, the cluster head transmits model gradients to the FL server for global aggregation. Additionally, the trusted cluster heads are responsible to submit the converged semi-global model to a blockchain to ensure tamper resilience. In our settings, an additional mediator component works like an independent observer that monitors the performance of each cluster head, updates a reward score, and records it into a digital ledger. Finally, evaluation results on three diversified benchmarks illustrate that the recommendation performance on selected measures is considerably comparable with the standard and federated version of a well-known neural collaborative filtering recommender. </p>", "Keywords": "", "DOI": "10.1145/3633520", "PubYear": 2024, "Volume": "15", "Issue": "4", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sichuan Artificial Intelligence Research Institute, China Yibin Park, University of Electronic Science and Technology of China, Yibin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Yangtze Delta Region Institute (Huzhou), University of Electronic Science and Technology of China, Huzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing Technologies, RMIT University, Melbourne, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Electronic Science and Technology of China, China and Sichuan Artificial Intelligence Research Institute, Chengdu, China"}], "References": [{"Title": "EPRT: An Efficient Privacy-Preserving Medical Service Recommendation and Trust Discovery Scheme for eHealth System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "Towards responsible media recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "1", "Page": "103", "JournalTitle": "AI and Ethics"}, {"Title": "Blockchain-based recommender systems: Applications, challenges and future opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "", "Page": "100439", "JournalTitle": "Computer Science Review"}, {"Title": "FairSR: Fairness-aware Sequential Recommendation through Multi-Task Learning with Preference Graph Embeddings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "A survey on blockchain-based Recommender Systems: Integration architecture and taxonomy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "1", "JournalTitle": "Computer Communications"}, {"Title": "Federated Neural Collaborative Filtering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "242", "Issue": "", "Page": "108441", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Recommender Systems in the EU: from Responsibility to Regulation", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "60", "JournalTitle": "Morals & Machines"}, {"Title": "Blockchain-enabled Federated Learning: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Multi-center federated learning: clients clustering for better personalization", "Authors": "<PERSON><PERSON> Long; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "1", "Page": "481", "JournalTitle": "World Wide Web"}, {"Title": "A Survey on the Fairness of Recommender Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "A blockchain framework data integrity enhanced recommender system", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "1", "Page": "104", "JournalTitle": "Computational Intelligence"}]}, {"ArticleId": 111058851, "Title": "Stereotypical nationality representations in HRI: perspectives from international young adults", "Abstract": "<p>People often form immediate expectations about other people, or groups of people, based on visual appearance and characteristics of their voice and speech. These stereotypes, often inaccurate or overgeneralized, may translate to robots that carry human-like qualities. This study aims to explore if nationality-based preconceptions regarding appearance and accents can be found in people’s perception of a virtual and a physical social robot. In an online survey with 80 subjects evaluating different first-language-influenced accents of English and nationality-influenced human-like faces for a virtual robot, we find that accents, in particular, lead to preconceptions on perceived competence and likeability that correspond to previous findings in social science research. In a physical interaction study with 74 participants, we then studied if the perception of competence and likeability is similar after interacting with a robot portraying one of four different nationality representations from the online survey. We find that preconceptions on national stereotypes that appeared in the online survey vanish or are overshadowed by factors related to general interaction quality. We do, however, find some effects of the robot’s stereotypical alignment with the subject group, with Swedish subjects (the majority group in this study) rating the Swedish-accented robot as less competent than the international group, but, on the other hand, recalling more facts from the Swedish robot’s presentation than the international group does. In an extension in which the physical robot was replaced by a virtual robot interacting in the same scenario online, we further found the same results that preconceptions are of less importance after actual interactions, hence demonstrating that the differences in the ratings of the robot between the online survey and the interaction is not due to the interaction medium. We hence conclude that attitudes towards stereotypical national representations in HRI have a weak effect, at least for the user group included in this study (primarily educated young students in an international setting).</p>", "Keywords": "accent; Appearance; social robot; nationality; stereotype; Impressions; competence; likeability", "DOI": "10.3389/frobt.2023.1264614", "PubYear": 2023, "Volume": "10", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Speech, Music and Hearing (TMH), Sweden"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Speech, Music and Hearing (TMH), Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Speech, Music and Hearing (TMH), Sweden"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Speech, Music and Hearing (TMH), Sweden"}], "References": [{"Title": "The Influence of robot personality on the development of uncanny feelings", "Authors": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>; G<PERSON>lia <PERSON>; Ginevra <PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "106756", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The impact of mixed-cultural speech on the stereotypical perception of a virtual robot", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "", "Page": "322", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 111058877, "Title": "Investigation of Boundary Control and Final Observation in Mathematical Model of Motion Speed Potentials Distribution of Filtered Liquid Free Surface", "Abstract": "In this paper, we study the problem of boundary control and final observation for one degenerate mathematical model of motion speed potentials distribution of filtered liquid free surface with the <PERSON><PERSON><PERSON><PERSON> initial condition. The mathematical model is based on the degenerate Bo<PERSON>inesq equation with an inhomogeneous Dirichlet condition. This model belongs to the class of semilinear Sobolev-type models in which the nonlinear operator is p-coercive and s-monotone. In the paper, the problem of boundary control and final observation for a semilinear Sobolev-type model is considered and conditions for the existence of a control-state pair of the problem are found. In applied studies of a research problem, it is allowed to find such a potentials distributionof filtered liquid free surface, at which the system transitions from the initial condition to a given final state within a certain period of time T . © 2023 South Ural State University. All rights reserved.", "Keywords": "mathematical model of motion speed potentials distribution of filtered liquid free surface; problem of boundary control and final observation; the <PERSON><PERSON>ev type equations", "DOI": "10.14529/mmp230211", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111058910, "Title": "Upskilling a New Generation of Digital Technologists", "Abstract": "<p><PERSON> is Programme Lead for the Digital and Technology Solutions Professional (BSc) degree apprenticeship programme at Buckinghamshire New University. Here he shares his insights about apprenticeships and their potential to help close the UK digital skills gap, following five years of experience working with forward-thinking employers.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad132", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111058945, "Title": "A Survey of Methods for Converting Unstructured Data to CSG Models", "Abstract": "The goal of this document is to survey existing methods for recovering or extracting CSG (Constructive Solid Geometry) representations from unstructured data such as 3D point-clouds or polygon meshes . We review and discuss related topics such as the segmentation and fitting of the input data. We cover techniques from solid modeling for the conversion of a polyhedron to a CSG expression and for the conversion of a B-rep to a CSG expression. We look at approaches coming from program synthesis , evolutionary techniques (such as genetic programming or genetic algorithm), and deep learning . Finally, we conclude our survey with a discussion of techniques for the generation of computer programs involving higher-level constructs, representations, and operations for representing solids.", "Keywords": "", "DOI": "10.1016/j.cad.2023.103655", "PubYear": 2024, "Volume": "168", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Aizu, Japan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Munich University of Applied Sciences, Germany"}], "References": [{"Title": "Learning Generative Models of 3D Structures", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "2", "Page": "643", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Topological Computing of Arrangements with (Co)Chains", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Spatial Algorithms and Systems"}, {"Title": "Sketch2CAD", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "ShapeMOD", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Fusion 360 gallery", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Unsupervised learning for cuboid shape abstraction via joint segmentation from point clouds", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Fit4CAD: A point cloud benchmark for fitting simple geometric primitives in CAD objects", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "133", "JournalTitle": "Computers & Graphics"}, {"Title": "AutoMate: a dataset and learning approach for automatic mating of CAD assemblies", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Combinatorial optimization with physics-inspired graph neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "4", "Page": "367", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Neural Fields in Visual Computing and Beyond", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "2", "Page": "641", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Advances in Neural Rendering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "2", "Page": "703", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Free2CAD", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Neurosymbolic Models for Computer Graphics", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "2", "Page": "545", "JournalTitle": "Computer Graphics Forum"}, {"Title": "A Review of a B-spline based Volumetric Representation: Design, Analysis and Fabrication of Porous and/or Heterogeneous Geometries", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "163", "Issue": "", "Page": "103587", "JournalTitle": "Computer-Aided Design"}, {"Title": "ShapeCoder: Discovering Abstractions for Visual Programs from Unstructured Primitives", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 111059012, "Title": "Base on contextual phrases with cross-correlation attention for aspect-level sentiment analysis", "Abstract": "In recent years, sentiment analysis has emerged as a prominent area of research within the field of natural language processing . Particularly, aspect-level sentiment classification has gained significant attention for its focus on discerning and analyzing sentiment expressed towards specific aspects within sentences. Existing methods primarily rely on extracting keywords from sentence contexts to determine sentiment polarity, yielding satisfactory results. However, a notable limitation of these approaches is their inability to consider the crucial information contained within key phrases in sentences, which plays a vital role in sentiment analysis. To address this limitation, we propose a novel deformable convolutional network model designed to leverage the power of phrases for aspect-level sentiment analysis. By utilizing deformable convolutions with adaptive receptive fields, our model effectively extracts phrase representations at various contextual distances. Furthermore, a cross-correlation attention mechanism is incorporated to capture interdependencies between phrases and words in the context. To evaluate the effectiveness of our approach, we conduct comprehensive evaluations across widely used datasets, demonstrating the promising performance of our model in enhancing sentiment classification tasks . Our model outperforms the model based on CNN , which also leverages phrase extraction, by improving accuracy by 1.71%, 2.5%, and 1.89%, respectively, on the Laptop, Restaurant, and Twitter datasets. Additionally, it surpasses the performance of the latest models.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122683", "PubYear": 2024, "Volume": "241", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electronic Information, Wuhan University, Bayi Road, Wuhan, 430072, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Information, Wuhan University, Bayi Road, Wuhan, 430072, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Information, Wuhan University, Bayi Road, Wuhan, 430072, China"}], "References": [{"Title": "Fine-grained attention-based phrase-aware network for aspect-level sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "5", "Page": "3727", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Aspect-Pair Supervised Contrastive Learning for aspect-based sentiment analysis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "274", "Issue": "", "Page": "110648", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Enhancing aspect-based sentiment analysis using a dual-gated graph convolutional network via contextual affective knowledge", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "553", "Issue": "", "Page": "126526", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111059021, "Title": "Ciphertext-policy attribute-based delay encryption", "Abstract": "Timed-release CP-ABE can provide fine-grained and timed-release access control while ensuring data confidentiality. Existing schemes usually rely on a trusted third-party called time server. This paper proposes a novel timed-release CP-ABE scheme named ciphertext-policy attribute-based delay encryption (CP-ABDE), which does not require a time server. Specifically, we formalise the notion of CP-ABDE and its system model and security model. Furthermore, we provide a formal construction that is secure under the decisional bilinear <PERSON><PERSON><PERSON>-<PERSON> assumption and repeated squaring assumption. Finally, performance analysis shows that the scheme performs well while achieving timed-release access control. © 2023 Inderscience Enterprises Ltd.", "Keywords": "access control; ciphertext-policy attribute-based encryption; CP-ABE; delay; time-lock puzzle; timed-release; TLP", "DOI": "10.1504/IJICS.2023.134960", "PubYear": 2023, "Volume": "22", "Issue": "2", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Information Security, Institute of Information Engineering, Chinese Academy of Sciences, Beijing, 100093, China; State Key Laboratory of Cryptology, P.O. Box 5159, Beijing, 100878, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing, 100049, China; Data Assurance and Communication Security Research Center, Chinese Academy of Sciences, Beijing, 100093, China"}, {"AuthorId": 2, "Name": "Kewei Lv", "Affiliation": "State Key Laboratory of Information Security, Institute of Information Engineering, Chinese Academy of Sciences, Beijing, 100093, China; State Key Laboratory of Cryptology, P.O. Box 5159, Beijing, 100878, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing, 100049, China; Data Assurance and Communication Security Research Center, Chinese Academy of Sciences, Beijing, 100093, China"}], "References": []}, {"ArticleId": 111059074, "Title": "Emotion-and-knowledge grounded response generation in an open-domain dialogue setting", "Abstract": "The neural-based interactive dialogue system focuses on engaging and retaining humans in long-lasting conversations. This has been explored for a variety of goal-oriented dialogue domains, such as education, health care, entertainment, sports, and politics. To develop an understanding and awareness of social and cultural norms, and to address specific social skills, we need to invent strategies for building interactive systems that take into account the user’s emotions and relevant-facts in a multi-turn conversation. In this paper, we propose a new neural generative model that combines step-wise co-attention with a self-attention-based transformer network along with an emotion classifier to jointly control emotion and knowledge transfer during response generation . Quantitative, qualitative, and human evaluation results on the benchmark Topical Chat and the CMU_DoG dataset show that the proposed models can generate natural and coherent sentences, capturing essential facts with considerable improvement over emotional content.", "Keywords": "", "DOI": "10.1016/j.knosys.2023.111173", "PubYear": 2024, "Volume": "284", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, IIT Patna, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, IIT Patna, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, NTU, Singapore;Corresponding author"}], "References": [{"Title": "End-to-End latent-variable task-oriented dialogue system with exact log-likelihood optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "3", "Page": "1989", "JournalTitle": "World Wide Web"}, {"Title": "A survey on empathetic dialogue systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "50", "JournalTitle": "Information Fusion"}, {"Title": "Recent advances in deep learning based dialogue systems: a systematic survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "4", "Page": "3055", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Multitask learning for multilingual intent detection and slot filling in dialogue systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "299", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 111059095, "Title": "Leadership", "Abstract": "<p>As an IT leader, there will always be a role for you as the 53rd card in the pack irrespective of what the current generation of AI can do. This sounds cryptic, but thankfully Professor of Problem Science and Computing at the Open University, <PERSON>, explains.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad122", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111059100, "Title": "Formal verification of software-only mechanisms for live migration of SGX enclaves", "Abstract": "Live migration is not supported by current Intel/R SGX implementations. So, software emulation is unavoidable to enable deployed hypervisors migrating live virtual machines running SGX enclaves in the cloud. However, copying the running state of an enclave requires read/write of enclave's memory from outside, which is impossible. Therefore, software-only mechanisms, as opposed to hardware extensions, are not transparent to the virtual machines, and need to deduce some hardware-defined metadata by emulation. Also, they need to synchronise enclaves' stop/resume between two remote platforms. In this paper, we formally verify the only solution proposed in the literature that uses such a mechanism, in which we identify a severe critical situation where the whole live migration process gets stuck. Moreover and due to a specification flaw, we determine a condition that leads to the violation of instance uniqueness of enclaves. That may induce vulnerabilities for fork and rollback attacks. To remedy to these design shortcomings, we propose some easy-to-implement solutions to remove deadlock situations and ensure state uniqueness at the expense of a longer downtime. © 2023 Inderscience Enterprises Ltd.", "Keywords": "fork attack; hardware-assisted security; Intel/R SGX; live migration; model-checking; PROMELA; rollback attack; SPIN; trust cloud computing; trusted execution environment", "DOI": "10.1504/IJICS.2023.134965", "PubYear": 2023, "Volume": "22", "Issue": "2", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Ecole Militaire Polytechnique, P.O. Box 17, Algiers, 16111, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Ecole Militaire Polytechnique, P.O. Box 17, Algiers, 16111, Algeria"}], "References": []}, {"ArticleId": 111059308, "Title": "Experimental investigation of a grid-tied high efficiency reversible pump-turbine energy storage system employing an adjustable BLDC drive", "Abstract": "<p>In this study, analysis of a high-efficiency grid-connected pump hydro energy storage (PHES) system that uses a bidirectional brushless DC (BLDC) machine is presented. The proposed layout is substantially less complicated since it uses an electronic power conditioning system to make all necessary modifications before connecting to the power grid, which removes the need for any mechanical components. This allows for the power plant's reliability to be improved while simultaneously reducing costs. An exhaustive model of the PHES system is created, and a two-tiered synchronous reference control technique for a grid side converter and a simple PI controller for the machine side converter is developed. Hardware data from a compact experimental setup are used to analyse the efficacy of the proposed PHES system.</p>", "Keywords": "", "DOI": "10.1007/s00542-023-05557-z", "PubYear": 2024, "Volume": "30", "Issue": "9", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Institute of Technology, Meghalaya, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Institute of Technology, Meghalaya, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology (ISM), Dhanbad, India"}], "References": []}, {"ArticleId": 111059339, "Title": "Artificial Intelligence: Where Do You Fit In?", "Abstract": "<p><PERSON>, Editor ITNOW, refl ects on AI's rich history and explores what it tells us about tomorrow's technology.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad109", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111059468, "Title": "Artificial Intelligence", "Abstract": "<p>Dr <PERSON><PERSON>, Principal Lecturer in Computer Science at University of Hertfordshire, and <PERSON><PERSON>, Vice Chair of BCS ISSG explore AI and its impact on women's safety and security.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad128", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111059546, "Title": "Grid-Characteristic Difference Scheme for Solving the Hopf Equation Based on Two Different Divergent Forms", "Abstract": "A new two-parameter family of difference schemes for the numerical solution of the Hopf equation is constructed. The original problem was replaced by a problem for a system of two differential equations based on various divergent forms of the Hopf equation. The flux terms were expressed as linear combinations of the variables included in the different divergent forms. In contrast to most works that use uncertain coefficient methods to construct difference schemes, in this approach uncertain coefficients arise in the formulation of the differential problem. The system of equations retains the hyperbolic type for any parameter values. For the numerical implementation, the well-known grid-characteristic scheme in Riemann invariants is chosen as the basis, which in the case of a linear equation with constant coefficients passes into a La<PERSON>–<PERSON> scheme. Calculations of two test problems – on the evolution of a smooth initial condition and the formation of a discontinuous solution and on the propagation of a “shock wave” – have been performed. Based on the results of the test calculations, we selected extrapolation coefficients that allow us to obtain a good agreement with the exact solution. The a posteriori order of convergence to the limit function for discontinuous solutions was investigated. When the extrapolation coefficients are well chosen, it insignificantly exceeds 1 at the moment of a gradient catastrophe. The order of convergence decreases to 0,76 when the strong discontinuity propagates at large times. The question of setting an optimization problem that allows one to choose the extrapolation coefficients in the best way, possibly depending on the local properties of the solution, remains open. The question of creating hybrid difference schemes with variable extrapolation coefficients depending on the smoothness of the solution also remains open. © 2023 South Ural State University. All rights reserved.", "Keywords": "discontinuous solution; divergent form; Hopf equation; <PERSON><PERSON><PERSON> scheme; undefined coefficients", "DOI": "10.14529/mmp230209", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111059576, "Title": "Inferring connectivity of an oscillatory network via the phase dynamics reconstruction", "Abstract": "<p>We review an approach for reconstructing oscillatory networks’ undirected and directed connectivity from data. The technique relies on inferring the phase dynamics model. The central assumption is that we observe the outputs of all network nodes. We distinguish between two cases. In the first one, the observed signals represent smooth oscillations, while in the second one, the data are pulse-like and can be viewed as point processes. For the first case, we discuss estimating the true phase from a scalar signal, exploiting the protophase-to-phase transformation. With the phases at hand, pairwise and triplet synchronization indices can characterize the undirected connectivity. Next, we demonstrate how to infer the general form of the coupling functions for two or three oscillators and how to use these functions to quantify the directional links. We proceed with a different treatment of networks with more than three nodes. We discuss the difference between the structural and effective phase connectivity that emerges due to high-order terms in the coupling functions. For the second case of point-process data, we use the instants of spikes to infer the phase dynamics model in the Winfree form directly. This way, we obtain the network’s coupling matrix in the first approximation in the coupling strength.</p>", "Keywords": "oscillations; network; connectivity; data analysis; Phase reduction", "DOI": "10.3389/fnetp.2023.1298228", "PubYear": 2023, "Volume": "3", "Issue": "", "JournalId": 89151, "JournalTitle": "Frontiers in Network Physiology", "ISSN": "", "EISSN": "2674-0109", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Physics and Astronomy, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Physics and Astronomy, Germany"}], "References": []}, {"ArticleId": 111059665, "Title": "RED-SP-CoDel: Random early detection with static priority scheduling and controlled delay AQM in programmable data planes", "Abstract": "Emerging network application paradigms, such as the Tactile Internet, re-emphasize the need for different Quality of Service (QoS) levels. Due to the large packet buffers in the underlying network data plane, Active Queue Management (AQM) is generally required to curtail packet latencies for flows requiring high QoS levels. At the same time, programmable data planes, such as P4, enable packet processing at line-speed, albeit with limited packet processing functionalities. However, the existing AQM mechanisms that support QoS differentiation are too complex to readily run on P4, while the existing AQM mechanisms that run on P4 do generally not support effective QoS differentiation. We address this gap by developing to the best of our knowledge the first AQM mechanism that supports effective QoS differentiation while running on P4. Specifically, we propose SP-CoDel, which combines the well-known Controlled Delay (CoDel) AQM mechanism with Static Priority (SP) scheduling for QoS differentiation. Also, we propose RED-SP-CoDel, which adds a RED AQM component to SP-CoDel so as to make the AQM with priorities essentially parameterless. As a community resource contribution, we substantially extend the existing P4Simulator to the novel fused P4-NS3 Simulator so as to enable the evaluation of packet processing mechanisms through the combined functionalities of P4 device emulation and NS3 packet simulation. Evaluations conducted with the P4 reference switch model in the P4-NS3 Simulator indicate that SP-CoDel and RED-SP-CoDel provide high QoS to high-priority data streams, i.e., significantly reduce latency and packet loss compared to CoDel, while effectively mitigating bufferbloat.", "Keywords": "Active queue management (AQM) ; Bufferbloat ; Low latency ; P4 data plane ; Quality of service (QoS) ; Packet priority ; Tactile internet", "DOI": "10.1016/j.comcom.2023.11.026", "PubYear": 2024, "Volume": "214", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Deutsche Telekom Chair of Communication Networks, TU Dresden, Georg-Schumann-Straße 11, Dresden, 01187, Saxony, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Haptic Communication Systems, TU Dresden, Georg-Schumann-Straße 9, Dresden, 01187, Saxony, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Haptic Communication Systems, TU Dresden, Georg-Schumann-Straße 9, Dresden, 01187, Saxony, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Deutsche Telekom Chair of Communication Networks, TU Dresden, Georg-Schumann-Straße 11, Dresden, 01187, Saxony, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Haptic Communication Systems, TU Dresden, Georg-Schumann-Straße 9, Dresden, 01187, Saxony, Germany;Centre for Tactile Internet with Human-in-the-Loop (CeTI), TU Dresden, Georg-Schumann-Straße 9, Dresden, 01187, Saxony, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Electrical, Computer, and Energy Engineering, Arizona State University, 650 E. Tyler Mall, Goldwater Center, Tempe, 85287-5706, AZ, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Deutsche Telekom Chair of Communication Networks, TU Dresden, Georg-Schumann-Straße 11, Dresden, 01187, Saxony, Germany;Centre for Tactile Internet with Human-in-the-Loop (CeTI), TU Dresden, Georg-Schumann-Straße 9, Dresden, 01187, Saxony, Germany"}], "References": [{"Title": "Advancing SDN from OpenFlow to P4: A Survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Trading Throughput for Freshness: Freshness-aware Traffic Engineering and In-Network Freshness Control", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "1-2", "Page": "1", "JournalTitle": "ACM Transactions on Modeling and Performance Evaluation of Computing Systems"}, {"Title": "A survey on data plane programming with P4: Fundamentals, advances, and applied research", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "103561", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Packet rank-aware active queue management for programmable flow scheduling", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "109632", "JournalTitle": "Computer Networks"}, {"Title": "Intelligent queue management of open vSwitch in multi-tenant data center", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "50", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Active queue management for alleviating Internet congestion via a nonlinear differential equation with a variable delay", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "61", "JournalTitle": "Annual Reviews in Control"}]}, {"ArticleId": 111059690, "Title": "Identification of movement phenotypes from occupational gesture kinematics: Advancing individual ergonomic exposure classification and personalized training", "Abstract": "The identification of personalized preventive strategies plays a major role in contrasting the occurrence of work-related musculoskeletal disorders. This requires the identification of distinct movement patterns within large samples and the attribution of a proper risk level to each identified movement phenotype. We assessed the feasibility of this approach by exploiting wearable inertial measurement units to estimate the whole-body kinematics of 43 healthy participants performing 18 reach-to-manipulate movements, which differed based on the object&#x27;s position in the space and the type of manipulation required. Through unsupervised clustering, we identified multiple movement phenotypes graded by ergonomic performance. Furthermore, we determined which joints mostly contributed to instantiating the ergonomic differences across clusters, emphasizing the importance of monitoring this aspect during occupational gestures. Overall, our analysis suggests that movement phenotypes can be identified within occupational motor repertoires. Assigning individual performance to specific phenotypes has the potential to inform the development of more effective and tailored interventions.", "Keywords": "Ergonomics assessment;Movement phenotypes;Wearable technologies", "DOI": "10.1016/j.apergo.2023.104182", "PubYear": 2024, "Volume": "115", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "Emilia Scalona", "Affiliation": "Dipartimento di Scienze Medico Chirurgiche, Scienza Radiologiche e Sanità Pubblica (DSMC), Università Degli Studi di Brescia, Viale Europa 11, 25123, Brescia, Italy. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Medicina e Chirurgia, Università Degli Studi di Parma, Parma, Italy."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Consiglio Nazionale Delle Ricerche, Istituto di Neuroscienze, Parma, Italy; School of Advanced Studies, Università di Camerino, Camerino, Italy."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Centro di Riabilitazione Motoria, INAIL, Volterra, Italy."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Centro di Riabilitazione Motoria, INAIL, Volterra, Italy."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Dipartimento di Design, Politecnico di Milano, Milano, Italy."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Consiglio Nazionale Delle Ricerche, Istituto di Neuroscienze, Parma, Italy."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Consiglio Nazionale Delle Ricerche, Istituto di Neuroscienze, Parma, Italy."}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Consiglio Nazionale Delle Ricerche, Istituto di Neuroscienze, Parma, Italy; Dipartimento di Ingegneria Dell'Informazione, Università Degli Studi di Brescia, Brescia, Italy."}], "References": [{"Title": "Shoulder electromyography-based indicators to assess manifestation of muscle fatigue during laboratory-simulated manual handling task", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "1", "Page": "118", "JournalTitle": "Ergonomics"}, {"Title": "A Repertoire of Virtual-Reality, Occupational Therapy Exercises for Motor Rehabilitation Based on Action Observation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "1", "Page": "9", "JournalTitle": "Data"}, {"Title": "Musculoskeletal disorder risk assessment tool use: A Canadian perspective", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "103740", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 111059784, "Title": "Tree derived feature importance and Bayesian optimisation for improved multi-class classification of DDoS attacks in software defined networks", "Abstract": "Software defined networking (SDN) is an emerging networking paradigm which mitigates the inadequacies of traditional networks. The centralised controller in SDN allows for the global view of network as well as for controlling the network operations from a single point. Like the traditional networks, SDN is also prone to network vulnerabilities. Intrusion detection based on machine learning techniques is effectively used in traditional networks and have found promising results. The research in security of SDN is in its early stages and researchers from academia and industry are working for this cause. In this paper, machine learning-based intrusion detection is attempted for multi-class classification of distributed denial of service (DDoS) attacks in a SDN environment. The feature importance derived from tree-based classifiers has been used for the feature selection to reduce the feature space which in turn reduces the time and space complexities. Hyperparameter tuning with TPE driven Bayesian optimisation (BO) has also been used for performance enhancement of the classifier. This multistage machine learning model achieves DDoS detection accuracy of 99.87%. The experimental evaluation is performed with SDN DDoS dataset and the results have been tabulated. © 2023 Inderscience Enterprises Ltd.", "Keywords": "Bayesian optimisation; DDoS attack detection; feature importance; machine learning; ML; multi-class classification; SDN; software defined networking", "DOI": "10.1504/IJICS.2023.134962", "PubYear": 2023, "Volume": "22", "Issue": "2", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Computer Science and Engineering, Cochin University of Science and Technology, Kochi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Computer Science and Engineering, Cochin University of Science and Technology, Kochi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Rajagiri School of Engineering and Technology, Kochi, India"}], "References": []}, {"ArticleId": 111059843, "Title": "Approximation Algorithms for the Min–Max Mixed Rural Postmen Cover Problem and Its Variants", "Abstract": "<p>In this work, we introduce a multi-vehicle (or multi-postman) extension of the classical Mixed Rural Postman Problem, which we call the Min–Max Mixed Rural Postmen Cover Problem (MRPCP). The MRPCP is defined on a mixed graph \\(G=(V,E,A)\\) , where V is the vertex set, E denotes the (undirected) edge set and A represents the (directed) arc set. Let \\(F\\subseteq E\\) ( \\(H\\subseteq A\\) ) be the set of required edges (required arcs). There is a nonnegative weight associated with each edge and arc. The objective is to determine no more than k closed walks to cover all the required edges in F and all the required arcs in H such that the weight of the maximum weight closed walk is minimized. By replacing closed walks with (open) walks in the MRPCP, we obtain the Min–<PERSON> Mixed Rural Postmen Walk Cover Problem (MRPWCP). The Min–Max Mixed Chinese Postmen Cover Problem (MCPCP) is a special case of the MRPCP where \\(F=E\\) and \\(H=A\\) . The Min–Max <PERSON>er Crane Cover Problem (SCCP) is another special case of the MRPCP where \\(F=\\emptyset \\) and \\(H=A\\) For the MRPCP with the input graph satisfying the weakly symmetric condition, i.e., for each arc there exists a parallel edge whose weight is not greater than this arc, we devise a \\(\\frac{27}{4}\\) -approximation algorithm. This algorithm achieves an approximation ratio of \\(\\frac{33}{5}\\) for the SCCP with the weakly symmetric condition. Moreover, we obtain the first 5-approximation algorithm (4-approximation algorithm) for the MRPWCP (MCPCP) with the weakly symmetric condition.</p>", "Keywords": "Approximation algorithm; Mixed Chinese postman problem; Mixed rural postman problem; <PERSON><PERSON><PERSON> crane problem; Postmen cover", "DOI": "10.1007/s00453-023-01187-z", "PubYear": 2024, "Volume": "86", "Issue": "4", "JournalId": 2779, "JournalTitle": "Algorithmica", "ISSN": "0178-4617", "EISSN": "1432-0541", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, East China University of Science and Technology, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mathematics, East China University of Science and Technology, Shanghai, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, East China University of Science and Technology, Shanghai, China"}], "References": [{"Title": "A Constant-factor Approximation Algorithm for the Asymmetric Traveling Salesman Problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "67", "Issue": "6", "Page": "1", "JournalTitle": "Journal of the ACM"}]}, {"ArticleId": *********, "Title": "Active distribution network operational optimization problem: A multi-objective tuna swarm optimization model", "Abstract": "This study proposes multi-objective tuna swarm optimization through multi-objective transformation, initialization improvement and population variation for active distribution network (ADN). The ADN energy optimization model with dynamic reconfiguration , reactive power compensation, on-load tap changer and controllable load coordinated control are established with the minimum economic and environmental cost. Several controllable resources increase and increase the complexity of energy optimization problem in realized the effective clean energy consumption and the power grid stable operation. The minimum control cost and the minimum node voltage deviation are proposed. This study also proposes a decision-making method based on pareto front and an index to evaluate the maximum extensible dimension of intelligent algorithms in the process of analyzing energy optimization problems with intelligent algorithms. The proposed ADN energy optimization method based on multi-objective tuna swarm optimization shows excellent results after testing with the improved IEEE33 system. The voltage deviation and network loss are reduced by 51.62% and 22.16% on average compared with the single means control. The proposed model provides the support for the new energy consumption and the power grid stable operation, has strong engineering significance in the intelligent upgrading and power grid active control transformation.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.111087", "PubYear": 2024, "Volume": "150", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Reliability and Intelligence of Electrical Equipment, Hebei University of Technology, Tianjin 300401, China;Key Laboratory of Electromagnetic Field and Electrical Apparatus Reliability of Hebei Province, Hebei University of Technology, Tianjin 300401, China"}, {"AuthorId": 2, "Name": "Bing-<PERSON><PERSON> Ji", "Affiliation": "State Key Laboratory of Reliability and Intelligence of Electrical Equipment, Hebei University of Technology, Tianjin 300401, China;Key Laboratory of Electromagnetic Field and Electrical Apparatus Reliability of Hebei Province, Hebei University of Technology, Tianjin 300401, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "<PERSON> Business School, University of Glasgow, Glasgow, United Kingdom;UKM-Graduate School of Business, Universiti Kebangsaan Malaysia, 43000 Bangi, Selangor, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UKM-Graduate School of Business, Universiti Kebangsaan Malaysia, 43000 Bangi, Selangor, Malaysia;Institute of Innovation and Circular Economy, Asia University, Taiwan;Department of Medical Research, China Medical University Hospital, China Medical University, Taichung, Taiwan;Department of Industrial Engineering Department, Khon Kaen University, 40002, Thailand;Corresponding author at: Institute of Innovation and Circular Economy, Asia University, Taiwan"}], "References": [{"Title": "Feature selection using bare-bones particle swarm optimization with mutual information", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107804", "JournalTitle": "Pattern Recognition"}, {"Title": "Modified non-dominated sorting genetic algorithm III with fine final level selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "7", "Page": "4236", "JournalTitle": "Applied Intelligence"}, {"Title": "An improved differential evolution algorithm and its application in optimization problem", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "7", "Page": "5277", "JournalTitle": "Soft Computing"}, {"Title": "Improved tunicate swarm algorithm: Solving the dynamic economic emission dispatch problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107504", "JournalTitle": "Applied Soft Computing"}, {"Title": "A dual-population algorithm based on alternative evolution and degeneration for solving constrained multi-objective optimization problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "579", "Issue": "", "Page": "89", "JournalTitle": "Information Sciences"}, {"Title": "Particle swarm-differential evolution algorithm with multiple random mutation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "108640", "JournalTitle": "Applied Soft Computing"}, {"Title": "Optimization of the location of piezoelectric actuator and sensor in active vibration control using Multi-Verse Optimizer algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "4", "Page": "401", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}, {"Title": "Hybrid multi-objective Harris Hawk optimization algorithm based on elite non-dominated sorting and grid index mechanism", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "172", "Issue": "", "Page": "103218", "JournalTitle": "Advances in Engineering Software"}, {"Title": "Multi population-based chaotic differential evolution for multi-modal and multi-objective optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "109909", "JournalTitle": "Applied Soft Computing"}, {"Title": "A non-convex economic load dispatch problem using chameleon swarm algorithm with roulette wheel and Levy flight methods", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "14", "Page": "17508", "JournalTitle": "Applied Intelligence"}, {"Title": "A nondominated sorting genetic algorithm III with three crossover strategies for the combined heat and power dynamic economic emission dispatch with or without prohibited operating zones", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106443", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 111059922, "Title": "Improved energy management of chiller system with AI-based regression", "Abstract": "This research aims to enhance energy management in commercial building air-conditioning systems, specifically focusing on chillers, which are significant energy consumers. This study evaluates various regularized regression models using comprehensive time series operating data from a system comprising five chillers of two distinct capacities. Compared with lasso and elastic net regression, ridge regression exhibits superior performance metrics when optimized with the appropriate hyperparameter. This makes it the most suitable method for modeling the system coefficient of performance (SCOP), thereby facilitating the development of an effective energy management plan. Key variables that strongly influence SCOP include the part load ratios of operating chillers, the operating numbers of chillers and pumps, and the temperatures of chilled water and condenser water. This study further identifies July as the month with the highest potential for performance improvement based on the predicted benchmark regions. This study introduces a novel approach that balances feature selection, model accuracy, and optimal tuning of hyperparameters. It highlights the significance of a generic and simplified chiller system model in evaluating energy management opportunities for sustainable operation, taking into account the distinct performance characteristics and time series features of individual system components. The findings from this research can guide future efforts towards more energy-efficient and sustainable operations in commercial buildings.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.111091", "PubYear": 2024, "Volume": "150", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "Fu<PERSON><PERSON>", "Affiliation": "School of Professional Education and Executive Development, The Hong Kong Polytechnic University, Hong Kong, China;Corresponding author"}, {"AuthorId": 2, "Name": "Wai-<PERSON>ng Ho", "Affiliation": "School of Professional Education and Executive Development, The Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Professional Education and Executive Development, The Hong Kong Polytechnic University, Hong Kong, China"}], "References": [{"Title": "Feature selection based on regularization of sparsity based regression models by hesitant fuzzy correlation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106255", "JournalTitle": "Applied Soft Computing"}, {"Title": "A data-driven approach to simultaneous fault detection and diagnosis in data centers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107638", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multicondition operation fault detection for chillers based on global density-weighted support vector data description", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107795", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 111059937, "Title": "Federated computation: a survey of concepts and challenges", "Abstract": "<p>Federated Computation is an emerging area that seeks to provide stronger privacy for user data, by performing large scale, distributed computations where the data remains in the hands of users. Only the necessary summary information is shared, and additional security and privacy tools can be employed to provide strong guarantees of secrecy. The most prominent application of federated computation is in training machine learning models (federated learning), but many additional applications are emerging, more broadly relevant to data management and querying data. This survey gives an overview of federated computation models and algorithms. It includes an introduction to security and privacy techniques and guarantees, and shows how they can be applied to solve a variety of distributed computations providing statistics and insights to distributed data. It also discusses the issues that arise when implementing systems to support federated computation, and open problems for future research.</p>", "Keywords": "Federated learning; Federated analytics; Histograms; Mean estimation; Differential privacy; Secure multiparty computation", "DOI": "10.1007/s10619-023-07438-w", "PubYear": 2024, "Volume": "42", "Issue": "3", "JournalId": 3983, "JournalTitle": "Distributed and Parallel Databases", "ISSN": "0926-8782", "EISSN": "1573-7578", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Meta, Menlo Park, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Meta, Coventry, UK; Corresponding author."}], "References": [{"Title": "Advances and Open Problems in Federated Learning", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1–2", "Page": "1", "JournalTitle": "Foundations and Trends® in Machine Learning"}, {"Title": "Trustworthy scientific computing", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "5", "Page": "18", "JournalTitle": "Communications of the ACM"}, {"Title": "Towards Sparse Federated Analytics: Location Heatmaps under Distributed Differential Privacy with Secure Aggregation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "4", "Page": "162", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}]}, {"ArticleId": 111059948, "Title": "Wiretap resisting and privacy preserving data exchange with physical layer security and blockchain based authentication in Internet of Vehicles", "Abstract": "With the development of automobile industry technology, vehicles have greatly affected everyday life, work and other aspects. With the continuous innovation of sensor technology, computer technology, wireless communication technology, and GPS technology, the concept of Inter of Vehicles (IoV) has been widely regarded as the core technology to solve a series of problems. However, as a complexity network with multiple elements including people, vehicle, base-station and so on, IoV is confronted with security threatened. In this paper, secure data exchange has been considered for two authenticated On Board Units (OBUs) with help of Road Side Unit (RSU). Blockchain based authentication and physical layer security have been applied into IoV for wiretap resisting and privacy preserving data exchange. For wiretap resisting, two synchronized transmitted signals from OBUs act as artificial noise at eavesdropper. In addition, for privacy preserving, summed codeword is formed at RSU which cannot be recovered individually. Finally, simulation results have been conducted to demonstrate that the proposed protocol can achieve transmission efficiency as well as informatics security .", "Keywords": "", "DOI": "10.1016/j.micpro.2023.104965", "PubYear": 2024, "Volume": "104", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi’an, Shaanxi, China;State Key Lab of ISN, Xidian University, Xi’an, Shaanxi, China;Corresponding author at: School of Cyber Engineering, Xidian University, Xi’an, Shaanxi, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Xi’an Institute of Applied Optics, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi’an, Shaanxi, China;State Key Lab of ISN, Xidian University, Xi’an, Shaanxi, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi’an, Shaanxi, China;State Key Lab of ISN, Xidian University, Xi’an, Shaanxi, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi’an, Shaanxi, China;State Key Lab of ISN, Xidian University, Xi’an, Shaanxi, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi’an, Shaanxi, China;State Key Lab of ISN, Xidian University, Xi’an, Shaanxi, China"}], "References": []}, {"ArticleId": 111060007, "Title": "Navigating Data Scarce Environments in Computer Vision", "Abstract": "<p><PERSON><PERSON><PERSON> is a senior software engineer with expertise in computer vision. He explores ways in which an absence of data may not necessarily result in AI being unable to identify new objects correctly.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad137", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>r", "Affiliation": ""}], "References": []}, {"ArticleId": 111060021, "Title": "Do you want a secure e-wallet? Understanding the role of risk and security in e-wallet continuance intention", "Abstract": "Purpose \nThis study aims to explore e-wallet continuance intention in Malaysia using perceived usefulness (PU), perceived ease of use (PEU), perceived risk (PR) and perceived security (PS). Additional emphasis is placed on the degree of necessity of risk and security conditions driving the continuance intention to use e-wallets.\n \n \n Design/methodology/approach \nThis quantitative study used a sample of 281 respondents. Partial least-squares structural equation modelling (PLS-SEM) was used to test the associations, while necessary condition analysis (NCA) was performed to uncover the factors’ necessity level.\n \n \n Findings \nPU is the primary facilitator for the continuance intention of e-wallet usage, followed by PS, PR and PEU. However, none were necessary for usage. E-wallet providers must emphasise enhancing PU, prioritise data security and improve the PEU to foster sustained e-wallet usage, while prudent risk management is a nice-to-have.\n \n \n Practical implications \nTo maintain the survival of e-wallets, service providers must focus on prioritising PEU, PU and PS for positive user experiences, including contentment, utility, convenience, comfort and safety. Compliance with user authorisation, real-time transaction monitoring and comprehensive security protocols is a must for security concerns.\n \n \n Originality/value \nThis study represents the limited empirical research focusing on risk and security in the continuance intention of e-wallet usage. Furthermore, an NCA was performed to determine the essential circumstances of risk and security for the continuance intention of e-wallets.", "Keywords": "Continuance intention;E-wallet usage;NCA;Perceived risk;Perceived security", "DOI": "10.1108/ICS-05-2023-0085", "PubYear": 2024, "Volume": "32", "Issue": "3", "JournalId": 31004, "JournalTitle": "Information and Computer Security", "ISSN": "2056-4961", "EISSN": "2056-497X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate Business School, UCSI University , Kuala Lumpur, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Business and Management, UCSI University , Kuala Lumpur, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate Business School, UCSI University, Kuala Lumpur Campus , Kuala Lumpur, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Business and Management, UCSI University, Sarawak Campus , Kuching, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Business and Management, UCSI University, Kuala Lumpur Campus , Kuala Lumpur, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Faculty of Business and Management, UCSI University, Kuala Lumpur Campus , Kuala Lumpur, Malaysia"}], "References": [{"Title": "Modeling Customers' Intention to Use E-Wallet in a Developing Nation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "1", "Page": "89", "JournalTitle": "Journal of Electronic Commerce in Organizations"}, {"Title": "The effect of Fair information practices and data collection methods on privacy-related behaviors: A study of Mobile apps", "Authors": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "1", "Page": "103284", "JournalTitle": "Information & Management"}, {"Title": "M-commerce adoption among youths in Malaysia: Dataset article", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2022, "Volume": "42", "Issue": "", "Page": "108238", "JournalTitle": "Data in Brief"}, {"Title": "Mobile payment service adoption: understanding customers for an application of emerging financial technology", "Authors": "<PERSON><PERSON> P.H<PERSON>", "PubYear": 2023, "Volume": "31", "Issue": "2", "Page": "145", "JournalTitle": "Information and Computer Security"}]}, {"ArticleId": 111060112, "Title": "Diffusion model with disentangled modulations for sharpening multispectral and hyperspectral images", "Abstract": "The denoising diffusion model has received increasing attention in the field of image generation in recent years, thanks to its powerful generation capability. However, diffusion models should be deeply investigated in the field of multi-source image fusion, such as remote sensing pansharpening and multispectral and hyperspectral image fusion (MHIF). In this paper, we introduce a novel supervised diffusion model with two conditional modulation modules, specifically designed for the task of multi-source image fusion. These modules mainly consist of a coarse-grained style modulation (CSM) and a fine-grained wavelet modulation (FWM), which aim to disentangle coarse-grained style information and fine-grained frequency information, respectively, thereby generating competitive fused images. Moreover, some essential strategies for the training of the given diffusion model are well discussed, e.g., the selection of training objectives. The superiority of the proposed method is verified compared with recent state-of-the-art (SOTA) techniques by extensive experiments on two multi-source image fusion benchmarks, i.e., pansharpening and MHIF. In addition, sufficient discussions and ablation studies in the experiments are involved to demonstrate the effectiveness of our approach. The code is accessible at https://github.com/294coder/Dif-PAN for reproducibility purposes.", "Keywords": "", "DOI": "10.1016/j.inffus.2023.102158", "PubYear": 2024, "Volume": "104", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Electronic Science and Technology of China, Chengdu, 611731, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Electronic Science and Technology of China, Chengdu, 611731, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Electronic Science and Technology of China, Chengdu, 611731, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Electronic Science and Technology of China, Chengdu, 611731, China"}, {"AuthorId": 5, "Name": "Junming Hou", "Affiliation": "Southeast University, Nanjing, 210000, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Methodologies for Environmental Analysis (CNR-IMAA), Tito, 85050, Italy;National Biodiversity Future Center (NBFC), Palermo, 90133, Italy"}], "References": [{"Title": "Remote sensing image fusion based on two-stream fusion network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "1", "JournalTitle": "Information Fusion"}, {"Title": "Pan-GAN: An unsupervised pan-sharpening method for remote sensing image fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "", "Page": "110", "JournalTitle": "Information Fusion"}, {"Title": "SRDiff: Single image super-resolution with diffusion probabilistic models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "479", "Issue": "", "Page": "47", "JournalTitle": "Neurocomputing"}, {"Title": "Multispectral and hyperspectral image fusion in remote sensing: A survey", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "89", "Issue": "", "Page": "405", "JournalTitle": "Information Fusion"}, {"Title": "UPanGAN: Unsupervised pansharpening based on the spectral and spatial loss constrained Generative Adversarial Network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "31", "JournalTitle": "Information Fusion"}, {"Title": "P2Sharpen: A progressive pansharpening network with deep spectral transformation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "103", "JournalTitle": "Information Fusion"}, {"Title": "An efficient unfolding network with disentangled spatial-spectral representation for hyperspectral image super-resolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "94", "Issue": "", "Page": "92", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 111060128, "Title": "Designing an artificial intelligence tool to understand student engagement based on teacher's behaviours and movements in video conferencing", "Abstract": "Video conferencing is an effective tool that promotes interaction and collaboration, increasing student engagement in online learning. This study is the second phase of design-based research to create a tool to generate a report of engaging teaching videos using deep learning as an artificial intelligence (AI) methodology. In this second phase, the authors have applied the characteristics and indicators of engaging teaching videos identified in the first phase, reported in another study, to develop an Artificial Intelligence enabled tool. Twenty-five recorded lecture videos presented to higher education students were annotated based on the indicators and characteristics of engaging teaching videos. An AI expert has assisted the authors in creating the Artificial Intelligence-enabled tool from the reports generated by this manual annotation. With the assistance of this tool, the engagement enhancing teachers&#x27; behaviours and movements can be identified from recorded lecture videos, and a report can be generated on engaging teaching videos. For the classification task of video analysis, the deep learning model is adopted in this research. The model is trained with manually annotated videos and determines class imbalance issues and misleading metrics. The model was further improved by adopting the oversampling technique. The second version of the tool achieved promising outputs with average precision, recall, f1-score, and balanced accuracy of 68, 75, 73, and 79%, respectively, in classifying the annotated videos at the indicator level. The tool can assist the education institutes in creating moderation in the lecture delivery and whether the teachers are utilising the technology effectively. Additionally, this can help teachers recognise the presence or absence of engagement-enhancing behaviours and movements during their video conferencing sessions.", "Keywords": "Artificial intelligence ; Video conferencing ; Teachers' behaviours ; Teachers' movements", "DOI": "10.1016/j.caeai.2023.100187", "PubYear": 2023, "Volume": "5", "Issue": "", "JournalId": 79689, "JournalTitle": "Computers and Education: Artificial Intelligence", "ISSN": "2666-920X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Southern Queensland, Springfield Education City, 37 Sinnathamby Blvd, Springfield Central, QLD, 4300, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Southern Queensland, School of Education, Australia"}, {"AuthorId": 3, "Name": "Dr <PERSON>", "Affiliation": "University of Southern Queensland, School of Education, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Southern Queensland, School of Maths, Physics & Computing, Australia"}], "References": [{"Title": "Students’ emotion extraction and visualization for engagement detection in online learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "192", "Issue": "", "Page": "3423", "JournalTitle": "Procedia Computer Science"}, {"Title": "An intelligent system for monitoring students' engagement in large classroom teaching through facial expression recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "1", "Page": "e12839", "JournalTitle": "Expert Systems"}, {"Title": "Contrastive predictive coding with transformer for video representation learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "482", "Issue": "", "Page": "154", "JournalTitle": "Neurocomputing"}, {"Title": "On the Care and Feeding of Virtual Assistants: Automating Conversation Review with AI", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "42", "Issue": "4", "Page": "29", "JournalTitle": "AI Magazine"}, {"Title": "Engagement and disengagement in online learning", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "188", "Issue": "", "Page": "104561", "JournalTitle": "Computers & Education"}, {"Title": "Evaluation of e-learners’ concentration using recurrent neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "4", "Page": "4146", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "FedStack: Personalized activity monitoring using stacked federated learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "257", "Issue": "", "Page": "109929", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Biases in Large Language Models: Origins, Inventory, and Discussion", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Data and Information Quality"}]}, {"ArticleId": *********, "Title": "Link State Estimator for VANETs Using Neural Networks", "Abstract": "<p>In Vehicular Ad-hoc NETworks (VANETs), it is important to consider the quality of the path used to forward data packets. Because of the fluctuating conditions of VANETs, stringent requirements have been imposed on routing protocols and thus complicating the entire process of packet delivery. To determine which path is the best, a routing protocol relies on a path assessment mechanism. In this paper, the problem of link quality estimation in VANET networks is addressed. Based on the information gathered from the packet decoding errors at the physical layer, a novel link quality estimator is proposed. The proposed link quality estimator named LSENN for Link State estimation based on Neural Networks, has been tested under realistic physical layer and mobility models for reactivity, accuracy and stability evaluation. </p>", "Keywords": "VANETs; Routing protocols; OFDM; Link state estimator; Vehicle-to-vehicle communication", "DOI": "10.1007/s10922-023-09786-5", "PubYear": 2024, "Volume": "32", "Issue": "1", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "LaRIA Laboratory, Computer Science, University of Jijel, Jijel, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LaRIA Laboratory, Computer Science, University of Jijel, Jijel, Algeria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "LaRIA Laboratory, Computer Science, University of Jijel, Jijel, Algeria"}], "References": []}, {"ArticleId": *********, "Title": "Incorporating medical domain knowledge into data-driven method: A vessel attention guided multi-granularity network for automatic cataract classification", "Abstract": "Early cataract screening is essential to help patients prevent irreversible vision loss. Existing data-driven methods can automatically extract deep fundus features for automatic cataract classification. However, due to the lack of medical domain knowledge, these deep features may contain large pathological irrelevant information, thus reducing the classification accuracy of data-driven methods. To fully take the advantages of domain knowledge to enhance the discriminative representation and avoid the irrelevant impact, we comprehensively investigate how to incorporate it into data-driven models. Considering the vessel information and multi-granularity representation are two key points of domain knowledge for cataract diagnosis, we propose a vessel attention guided multi-granularity network (VAM-Net), which contains three main components. Specifically, the vessel-level segmentation subnet is firstly designed by leveraging U-shape segmentation network. The refined vessel information obtained in this step can be regarded as visual attention to enable deep network extract discriminative features . With the guidance of vessel attention, the global-local classification subnet is further proposed to eliminate the interference of irrelevant information and enhance the multi-granularity feature learning (including global structural features and local subtle features). Finally, the multiple levels predictions are integrated into the final cataract diagnosis decision via constructing multi-granularity ensemble network. In this manner, both domain knowledge and deep networks are organically combined for pursuing better diagnosis performance. By setting extensive comparative and ablation experiments, we evaluate the effectiveness of the proposed VAM-Net on the real-world cataract dataset (92.78% detection accuracy and 87.68% grading accuracy).", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122671", "PubYear": 2024, "Volume": "241", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China;Correspondence to: Faculty of Information Technology, Beijing University of Technology, Beijing Engineering Research Center for IoT Software and Systems, Beijing, 100124, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Beijing Children’s Hospital affiliated to Capital Medical University, Beijing, 100045, China"}], "References": [{"Title": "Hierarchical method for cataract grading based on retinal images using improved Haar wavelet", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "196", "JournalTitle": "Information Fusion"}, {"Title": "CCA-Net: Clinical-awareness attention network for nuclear cataract classification in AS-OCT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "250", "Issue": "", "Page": "109109", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 111060304, "Title": "Selection of Suitable Cloud Vendors for Health Centre: A Personalized Decision Framework with Fermatean Fuzzy Set, LOPCOW, and CoCoSo", "Abstract": "<p>Cloud computing has emerged as a transformative technology in the healthcare industry, but selecting the most suitable CV (“cloud vendor”) remains a complex task. This research presents a decision framework for CV selection in the healthcare industry, addressing the challenges of uncertainty, expert hesitation, and conflicting criteria. The proposed framework incorporates FFS (“Fermatean fuzzy set”) to handle uncertainty and data representation effectively. The importance of experts is attained via the variance approach, which considers hesitation and variability. Furthermore, the framework addresses the issue of extreme value hesitancy in criteria through the LOPCOW (“logarithmic percentage change-driven objective weighting”) method, which ensures a balanced and accurate assessment of criterion importance. Personalized grading of CVs is done via the ranking algorithm that considers the formulation of CoCoSo (“combined compromise solution”) with rank fusion, providing a compromise solution that balances conflicting criteria. By integrating these techniques, the proposed framework aims to enhance the rationale and reduce human intervention in CV selection for the healthcare industry. Also, valuable insights are gained from the framework for making informed decisions when selecting CVs for efficient data management and process implementation. A case example from Tamil Nadu is presented to testify to the applicability, while sensitivity and comparison analyses reveal the pros and cons of the framework.</p>", "Keywords": "cloud vendor selection; CoCoSo method; fermatean fuzzy set; Health 4.0; LOPCOW method; variance method", "DOI": "10.15388/23-INFOR537", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 35016, "JournalTitle": "Informatica", "ISSN": "0868-4952", "EISSN": "1822-8844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Amrita School of Physical Sciences, Amrita Vishwa Vidyapeetham, Coimbatore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information Technology Systems and Analytics Area, Indian Institute of Management Bodh Gaya, Bihar, Bodh Gaya, 824234, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Sustainable Construction, Vilnius Gediminas Technical University, Sauletekio ave. 11, Vilnius, LT-10223, Lithuania"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>ara<PERSON>", "Affiliation": "Department of Mathematics, Amrita School of Physical Sciences, Amrita Vishwa Vidyapeetham, Coimbatore, India"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering & Information Technology, University of Technology Sydney, Ultimo, Australia; University Research and Innovation Center (EKIK), Óbuda University, Budapest, 1034, Hungary"}], "References": [{"Title": "Solving cloud vendor selection problem using intuitionistic fuzzy decision framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "2", "Page": "589", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Fermatean fuzzy sets", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "663", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "IIVIFS-WASPAS: An integrated Multi-Criteria Decision-Making perspective for cloud service provider selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "91", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A novel framework towards viable Cloud Service Selection as a Service (CSSaaS) under a fuzzy environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "74", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Hybrid Approach for Sentiment Analysis of Twitter Posts Using a Dictionary-based Approach and Fuzzy Logic Methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "1", "Page": "116", "JournalTitle": "International Journal on Semantic Web and Information Systems"}, {"Title": "A novel customer-centric Methodology for Optimal Service Selection (MOSS) in a cloud environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "562", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A fuzzy inference system (FIS) to evaluate the security readiness of cloud service providers", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "An integrated decision-making COPRAS approach to probabilistic hesitant fuzzy set information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "5", "Page": "2281", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "RETRACTED ARTICLE: Cloud vendor selection for the healthcare industry using a big data-driven decision model with probabilistic linguistic information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "12", "Page": "13497", "JournalTitle": "Applied Intelligence"}, {"Title": "Low-carbon cities comprehensive evaluation method based on Fermatean fuzzy hybrid distance measure and TOPSIS", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "8", "Page": "8591", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Multi-criteria group decision-making based on an integrated PROMETHEE approach with 2-tuple linguistic Fermatean fuzzy sets", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "5", "Page": "917", "JournalTitle": "Granular Computing"}, {"Title": "Selection of cloud service providers using MCDM methodology under intuitionistic fuzzy uncertainty", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "5", "Page": "2403", "JournalTitle": "Soft Computing"}, {"Title": "An Overview on the Challenges and Limitations Using Cloud Computing in Healthcare Corporations", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "2", "Page": "68", "JournalTitle": "Big Data and Cognitive Computing"}]}, {"ArticleId": 111060312, "Title": "Model Design and Applied Methodology in Geothermal Simulations in Very Low Enthalpy for Big Data Applications", "Abstract": "<p>Low-enthalpy geothermal installations for heating, air conditioning, and domestic hot water are gaining traction due to efforts towards energy decarbonization. This article is part of a broader research project aimed at employing artificial intelligence and big data techniques to develop a predictive system for the thermal behavior of the ground in very low-enthalpy geothermal applications. In this initial article, a summarized process is outlined to generate large quantities of synthetic data through a ground simulation method. The proposed theoretical model allows simulation of the soil’s thermal behavior using an electrical equivalent. The electrical circuit derived is loaded into a simulation program along with an input function representing the system’s thermal load pattern. The simulator responds with another function that calculates the values of the ground over time. Some examples of value conversion and the utility of the input function system to encode thermal loads during simulation are demonstrated. It bears the limitation of invalidity in the presence of underground water currents. Model validation is pending, and once defined, a corresponding testing plan will be proposed for its validation.</p>", "Keywords": "", "DOI": "10.3390/data8120176", "PubYear": 2023, "Volume": "8", "Issue": "12", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Departamento de Ingeniería Geológica y Minera, Escuela Técnica Superior de Ingenieros de Minas y Energía, Universidad Politécnica de Madrid, 28003 Madrid, Spain; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Canal de Isabel II, Área de Infraestructura Informática, 28003 Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Departamento de Ingeniería Geológica y Minera, Escuela Técnica Superior de Ingenieros de Minas y Energía, Universidad Politécnica de Madrid, 28003 Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Departamento de Ingeniería Geológica y Minera, Escuela Técnica Superior de Ingenieros de Minas y Energía, Universidad Politécnica de Madrid, 28003 Madrid, Spain"}], "References": []}, {"ArticleId": 111060320, "Title": "Aviation: Cyber Security in the Skies", "Abstract": "<p><PERSON><PERSON>, Managing Director of LeapTronx, explores the challenges facing aviation and explains the importance of aligning boards, organisations and suppliers to help mitigate cyber risk.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad130", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111060350, "Title": "Energy-Efficient and Privacy-Preserving Blockchain Based Federated Learning for Smart Healthcare System", "Abstract": "", "Keywords": "", "DOI": "10.1109/TSC.2023.3332955", "PubYear": 2024, "Volume": "17", "Issue": "5", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology (Banaras Hindu University), Varanasi, Uttar Pradesh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology (Banaras Hindu University), Varanasi, Uttar Pradesh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology (Banaras Hindu University), Varanasi, Uttar Pradesh, India"}], "References": [{"Title": "Two-sided preferences task matching mechanisms for blockchain-based crowdsourcing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "191", "Issue": "", "Page": "103155", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Criticality and Utility-Aware Fog Computing System for Remote Health Monitoring", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 111060401, "Title": "An empirical study on the performance and energy costs of ads and analytics in mobile web apps", "Abstract": "<b  >Context:</b> As the use of mobile devices has increased immensely through the years, the presence of analytics and advertisements on web and native applications has become prevalent. However, serving ads and analytics comes with costs, as they are associated with additional code and network requests to execute properly. Subsequently, more computing resources are used, having an impact on the energy consumption and the performance of web applications. Previous work has focused only on native Android applications, has used different metrics for performance, or has focused on other aspects of web applications. <b  >Goal:</b> This paper aims to investigate the costs of including advertisements and analytics in web applications. This is done in terms of energy consumption and performance. For energy, the consumption is measured in Joules. For performance, the following metrics are used: first contentful paint and full page load time . The results of this study could influence the decisions of web developers and web browser vendors related to ads and analytics usage, while providing the foundation for further research on this topic. <b  >Method:</b> To collect reliable and population-representative results, the research focused on 9 popular web applications included in the Tranco list. Energy consumption and performance metrics were gathered for 3 versions of each web application — original version with ads and analytics, without ads, and without analytics. A cross-over paired comparison design is conducted. Multiple executions of each run were performed in random order to ascertain rigorous measures. The experiment is carried out on an Android tablet using two browsers, Google Chrome and Opera. <b  >Results:</b> Ads significantly impact the energy consumption of mobile web apps for both browsers, with a large effect size; analytics have a significant impact on the energy consumption of Chrome (with a medium effect size), but not on Opera. In terms of performance, both ads and analytics do not significantly impact the first contentful paint metric on both browsers; differently, both ads and analytics significantly impact the full page load time of the mobile web apps on both browsers, but with a small effect size. <b  >Conclusions:</b> This study provides evidence that both ads and analytics can have a significant impact on the energy consumption and performance of mobile web apps loaded either on Opera or Chrome. Depending on the requirements of the mobile web app, it is advisable to limit both ads and analytics in a mobile web app in order to reduce its energy consumption and improve its full page load time. Special attention should be paid to the presence of ads since they resulted to be the most impactful in terms of energy consumption.", "Keywords": "Mobile web ; Empirical study ; Controlled experiment ; Energy efficiency ; Performance", "DOI": "10.1016/j.infsof.2023.107370", "PubYear": 2024, "Volume": "166", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, The Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, The Netherlands"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, The Netherlands"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, The Netherlands;Corresponding author"}], "References": [{"Title": "The state of the art in measurement-based experiments on the mobile web", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "149", "Issue": "", "Page": "106944", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 111060435, "Title": "Corrigendum: mPD-APP: a mobile-enabled plant diseases diagnosis application using convolutional neural network toward the attainment of a food secure world", "Abstract": "<p>[This corrects the article DOI: 10.3389/frai.2023.1227950.].</p><p>Copyright © 2023 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and Kolawole.</p>", "Keywords": "Mobile-enabled; Convolutional Neural Networks; Diseases Diagnosis System; SDG 2; pathogens", "DOI": "10.3389/frai.2023.1325606", "PubYear": 2023, "Volume": "6", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Landmark University, Nigeria; Landmark University SDG 11 (Sustainable Cities and Communities Research Group), Nigeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Landmark University, Nigeria"}, {"AuthorId": 3, "Name": "Adekan<PERSON>", "Affiliation": "School of Mathematics, Statistics and Computer Science, University of Kwazulu-Natal, South Africa"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics, Statistics and Computer Science, University of Kwazulu-Natal, South Africa"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Santa Clara University, United States"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural Economics and Extension, Landmark University, Nigeria"}], "References": []}, {"ArticleId": 111060437, "Title": "Algorithms Invenire Asymptotic Formulas Eigenvalues Discreta Semi-Terminus Operators", "Abstract": "Methods for finding asymptotic formulas for eigenvalues of discrete semibounded operators given on compact sets are individual in each case. Therefore, it becomes necessary to develop algorithms that allow one to find asymptotic formulas for the eigenvalues of any discrete semi-bounded operators given on compact sets. This will greatly simplify their finding and allow you to write programs to obtain asymptotic formulas. These algorithms will help to find asymptotic formulas for eigenvalues of vector operators given on finite connected graphs. In the article, based on the methods developed earlier, an algorithm is created that allows finding asymptotic formulas for eigenvalues with any ordinal number for discrete semi-bounded operators given on compact sets. Examples are given of comparing asymptotic formulas found by the developed method and known formulas previously obtained by other authors, which are in good agreement with each other © 2023 South Ural State University. All rights reserved.", "Keywords": "asymptotic formulas; discrete semibounded operators; eigenvalues and eigenfunctions of linear operators", "DOI": "10.14529/mmp230210", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111060454, "Title": "Design Game Mobile Edukasi Bin<PERSON>ri untuk Anak Berkebut<PERSON><PERSON>", "Abstract": "Designing a game according to user needs and evaluating the design will increase the positive aspects and positive benefits of the game. The self-development curriculum in schools is assistance given to students to be able to carry out daily activities and take care of themselves without help or dependence on others by optimizing their abilities. This research was conducted at SLB Srimujinab Pekanbaru. The research method in designing games is Research &amp; Development (R&amp;D). Games are designed according to the needs of Sri Mujinab Special School students identified through interviews and discussions with teachers. Observation of game testing that has been designed was tested on 30 mentally retarded students (10 elementary, 10 junior high, 10 high school) at Srimujinab SLB Pekanbaru. The results of the research have succeeded in building a self-development mobile game that is 100% valid from the functional side of UAT, content and material validation. Based on the results of field observations, students are very interested, happy and more concentrated when playing games with the help of voice instructions and also red arrows in the game. Games that have been designed can be used as interesting learning media at Srimujinab SLB Pekanbaru.", "Keywords": "<PERSON><PERSON>;<PERSON>;<PERSON><PERSON><PERSON><PERSON>", "DOI": "10.35143/jkt.v9i1.5765", "PubYear": 2023, "Volume": "", "Issue": "Vol. 9 No. 1 (2023)", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}], "References": []}, {"ArticleId": 111060457, "Title": "Noise estimation based on optimal smoothing and minimum controlled through recursive averaging for speech enhancement", "Abstract": "One of the most significant challenges in the real time speech processing applications is the elimination of noise in the corrupted speech data. This noise can significantly impact the efficacy/performance of the applications of speech processing. However, developing a robust noise reduction algorithm is crucial for improving the accuracy of automatic speech recognition (ASR) and other speech processing systems under uncontrolled conditions. In this paper, we propose an algorithm to reduce the background noise in the degraded speech data under highly non-stationary conditions. The proposed optimal smoothing and minima controlled (OSMC) technique uses recursive averaging to enhance degraded speech data. Initially, a smoothed periodogram and local minima of the degraded speech data are computed and determined the time-frequency dependent threshold factor. The ratio of smoothed periodogram to local minima is used to find the active regions of speech in the degraded speech data by adapting the Bayesian minimum cost decision rule. To calculate the estimated noise spectrum for each frequency bin, a time-frequency smoothing factors are used. The perceptual evaluation of speech quality (PESQ) and normalized covariance metric (NCM) are used to evaluate the speech quality and intelligibility of the proposed technique over competing algorithms after speech enhancement. The experimental results demonstrated that the proposed algorithm has given a significant improvement in terms of average values of PESQ by 15.03% and 16.71% and NCM by 3.45% and 5.73% for NOIZEUS and Kannada speech databases at 5 dB and 10 dB respectively, over unprocessed speech under highly non-stationary noisy environments.", "Keywords": "Speech enhancement ; Optimal smoothing and minimum controlled (OSMC) ; Recursive averaging ; Perceptual evaluation of speech quality (PESQ) ; Normalized covariance metric (NCM) ; Automatic speech recognition (ASR) ; NOIZEUS database ; Kannada database", "DOI": "10.1016/j.iswa.2023.200310", "PubYear": 2024, "Volume": "21", "Issue": "", "JournalId": 89889, "JournalTitle": "Intelligent Systems with Applications", "ISSN": "2667-3053", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Raghudathesh G P", "Affiliation": "Manipal School of Information Sciences, Manipal Academy of Higher Education, Manipal-576104, Karnataka, India"}, {"AuthorId": 2, "Name": "Chandrakala C B", "Affiliation": "Dept. of Information and Communication Technology, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal-576104, Karnataka, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Manipal School of Information Sciences, Manipal Academy of Higher Education, Manipal-576104, Karnataka, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Electronics and Communication Engineering, Nitte Meenakshi Institute of Technology, Bengaluru-560064, Karnataka, India"}], "References": [{"Title": "Enhancements in automatic Kannada speech recognition system by background noise elimination and alternate acoustic modelling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "1", "Page": "149", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Speech intelligibility enhancement: a hybrid wiener approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "3", "Page": "517", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Real-time speech enhancement algorithm for transient noise suppression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "3", "Page": "3681", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Speech enhancement and encoding by combining SS-VAD and LPC", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "1", "Page": "165", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Noisy speech enhancement based on correlation canceling/log-MMSE hybrid method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "4", "Page": "5803", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Speech enhancement with noise estimation and filtration using deep learning models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "941", "Issue": "", "Page": "14", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 111060460, "Title": "Pengukuran Kinerja Kubernetes Cluster pada Nested Virtualization Berbasis KVM", "Abstract": "Kubernetes sebagai sistem orkestrasi container open-source yang dapat diimplementasikan dalam infrastruktur fisik dan infrastruktur tervisualisasi. <PERSON><PERSON> implementasi virtualisasi, masih terdapat sisa sumber daya yang tidak dimanfaatkan secara maksimal. Salah satu cara untuk mengatasi masalah tersebut adalah dengan menerapkan Nested Virtualization. Penelitian ini melakukan pengukuran kinerja untuk menganalisis penggunaan sumber daya (CPU, memori, disk dan network) kubernetes cluster pada teknologi nested virtualization berbasis KVM. Hasil penggunaan memori keadaan stanby dan busy kubernetes cluster pada nested virtualization lebih tinggi yaitu mencapai 42% dibandingkan dengan hanya kubernetes cluster yaitu 30%. Penggunaan CPU dalam keadaan stanby dan busy pada kubernetes cluster lebih tinggi yaitu 65% dibandingkan kubernetes cluster pada Nested Virtualization yaitu 36%. Hasil latency tinggi yaitu 237 ms pada kubernetes cluster dibandingkan kubernetes cluster pada Nested Virtualization yaitu 40 ms. Kecepatan baca dan tulis disk pada teknologi kubernetes cluster yaitu 410 MB/sec dan 397 Mb/sec, sementara kubernetes cluster pada Nested Virtualization yaitu mencapai 116 MB/sec dan 597 MB/sec. Kecepatan network untuk download dan upload pada kubernetes cluster lebih besar yaitu 39.1 Mbit/s dan 53/94 Mbit/s sementara kubernetes cluster pada Nested Virtualization yaitu 35.54 Mbit/s dan 53.58 Mbit/s.", "Keywords": "Cloud Computing;Kubernetes cluster;KVM;Nested Virtualization", "DOI": "10.35143/jkt.v9i1.5606", "PubYear": 2023, "Volume": "", "Issue": "Vol. 9 No. 1 (2023)", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Caltex Riau"}, {"AuthorId": 2, "Name": "Sugeng Purwantoro E.S.G.S", "Affiliation": "Politeknik Caltex Riau"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Politeknik Caltex Riau"}], "References": []}, {"ArticleId": 111060513, "Title": "Distributed online primal-dual subgradient method on unbalanced directed networks", "Abstract": "In this paper, we investigate a distributed online optimization method on multiagent communication networks. We consider a distributed prime-dual subgradient algorithm for an online convex optimization problem with time-varying coupled constraints. Each agent updates the estimations for the primal and dual optimizers by a consensus-based online algorithm. In the proposed algorithm, the gradient direction is scaled by estimating the left eigenvector of a weight matrix associated with the directed communication network. The scaling procedure enables agents in the network to estimate the time-varying optimal solutions by counterbalancing the unbalanced communication flows. The performance of the proposed algorithm is examined by a dynamic regret and a fit, which evaluate the cumulative error against the time-varying optimal cost function and the constraint function, respectively. We provide a sufficient condition under which both the dynamic regret and the fit are sublinear. A numerical example of an online economic dispatch problem confirms the validity of the proposed method.", "Keywords": "Distributed online optimization ; multiagent system ; primal-dual algorithm", "DOI": "10.1080/01691864.2023.2285806", "PubYear": 2024, "Volume": "38", "Issue": "9-10", "JournalId": 5595, "JournalTitle": "Advanced Robotics", "ISSN": "0169-1864", "EISSN": "1568-5535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Osaka University, Suita, Osaka, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering Science, Osaka University, Toyonaka, Osaka, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Osaka University, Suita, Osaka, Japan"}], "References": []}, {"ArticleId": 111060521, "Title": "Strategic oscillation tabu search for improved hierarchical graph drawing", "Abstract": "In the last years, many areas in science, business, and engineering have experienced an enormous growth in the amount of data that they are required to analyze. In many cases, this analysis relies intimately on data visualization and, as a result, graph drawing has emerged as a new field of research. This paper addresses the challenge of drawing hierarchical graphs, which is one of the most widely used drawing standards. We introduce a new mathematical model to automatically represent a graph based on the alignment of long arcs, which we combine with the classic arc crossing minimization objective in hierarchical drawings. We complement our proposal with a heuristic algorithm that can obtain high-quality results in the short computational time required by graph drawing systems. Our algorithm joins two methodologies, tabu search and strategic oscillation (SOS), to perform a fast and effective exploration of the search space. We conduct extensive experimentation that integrates our new mathematical programming formulation and the SOS tabu search that targets large instances. Our statistical analysis confirms the effectiveness of this proposal.", "Keywords": "Graph drawing ; Arc crossing ; Strategic oscillation ; Tabu search ; Metaheuristics", "DOI": "10.1016/j.eswa.2023.122668", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Informática y Estadística, Universidad Rey Juan Carlos, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Informática y Estadística, Universidad Rey Juan Carlos, Spain;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Entanglement, Inc., CO, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Departamento de Estadística e Investigación Operativa, Universitat de València, Spain"}], "References": [{"Title": "Tabu search for min-max edge crossing in graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "114", "Issue": "", "Page": "104830", "JournalTitle": "Computers & Operations Research"}, {"Title": "Multistart search for the Cyclic Cutwidth Minimization Problem", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "126", "Issue": "", "Page": "105116", "JournalTitle": "Computers & Operations Research"}, {"Title": "A variable neighborhood search approach for cyclic bandwidth sum problem", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "246", "Issue": "", "Page": "108680", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 111060528, "Title": "Examination of the effect of consumer comments on e-commerce sites on continuance intention to online shopping within the scope of social exchange theory", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJEB.2023.10060655", "PubYear": 2023, "Volume": "1", "Issue": "1", "JournalId": 8392, "JournalTitle": "International Journal of Electronic Business", "ISSN": "1470-6067", "EISSN": "1741-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Zübeyir Çelik", "Affiliation": ""}, {"AuthorId": 3, "Name": "Özge Habiboğlu", "Affiliation": ""}], "References": []}, {"ArticleId": 111060529, "Title": "An automated face mask detection system using transfer learning based neural network to preventing viral infection", "Abstract": "As the “Internet of Medical Things (IoMT)” grows, healthcare systems can collect and process data. It is also challenging to study public health prevention requirements. Virus transmission can be prevented by wearing a mask. The World Health Organization (WHO) recommends wearing a facemask to protect against the COVID‐19 pandemic—the levels of a pandemic rise across almost all regions of the world. By following the WHO rules, we support the development of face mask‐detecting technologies and determine whether or not people are using masks in public locations. The proposed paradigm in this paper will work in three stages. Firstly, we use an Image data generator to import the images. In addition to using a Haar cascade (HC) classifier for detecting faces, residual learning (ResNet152V2) trains a model that detects whether someone is wearing a face mask. Detection and classification are carried out in real‐time with high precision. Compared with other recently proposed methods, the model achieved 99.65% accuracy during training and 99.63% during validation.", "Keywords": "classification;face mask detection;Haar Cascade (HC) classifier;neural network;ResNet152V2;transfer learning", "DOI": "10.1111/exsy.13507", "PubYear": 2024, "Volume": "41", "Issue": "3", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science ABES Engineering College  Ghaziabad India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication SRM Institute of Science and Technology, NCR‐Campus, Delhi‐Meerut Road, Modinagar  Ghaziabad India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of computer science & engineering (Artificial Intelligence) KIET Group of Institutions  Ghaziabad India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Bhagwan Parshuram Institute of Technology  Rohini, New Delhi India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering University of Ha'il  Ha'il Saudi Arabia"}, {"AuthorId": 6, "Name": "Arwa N. <PERSON>edaily", "Affiliation": "College of Computer Science and Engineering University of Ha'il  Ha'il Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computer Engineering and Sciences Prince <PERSON> Abdul<PERSON>z University  Al‐kharj Saudi Arabia"}], "References": [{"Title": "Cropping and attention based approach for masked face recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "5", "Page": "3012", "JournalTitle": "Applied Intelligence"}, {"Title": "XCOVNet: Chest X-ray Image Classification for COVID-19 Early Detection Using Convolutional Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "3-4", "Page": "583", "JournalTitle": "New Generation Computing"}, {"Title": "Cascaded deep learning classifiers for computer-aided diagnosis of COVID-19 and pneumonia diseases in X-ray scans", "Authors": "<PERSON>; <PERSON><PERSON>, <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "1", "Page": "235", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Leveraging Artificial Intelligence (AI) Capabilities for COVID-19 Containment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "3-4", "Page": "717", "JournalTitle": "New Generation Computing"}, {"Title": "FMD-Yolo: An efficient face mask detection method for COVID-19 prevention and control in public", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "117", "Issue": "", "Page": "104341", "JournalTitle": "Image and Vision Computing"}, {"Title": "Deep transfer learning for the recognition of types of face masks as a core measure to prevent the transmission of COVID-19", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "125", "Issue": "", "Page": "109207", "JournalTitle": "Applied Soft Computing"}, {"Title": "Intelligent transportation system for internet of vehicles based vehicular networks for smart cities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "105", "Issue": "", "Page": "108543", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Optimized face detector-based intelligent face mask detection model in IoT using deep learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109933", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": *********, "Title": "An incremental singular value decomposition approach for large-scale spatially parallel & distributed but temporally serial data – applied to technical flows", "Abstract": "The article presents a strategy and its algorithm to compile a simulation-accompanying, incremental Singular Value Decomposition (SVD) for time-evolving, spatially parallel discrete data sets. The framework addresses state-of-the-art PDE solvers for computational science and engineering applications. An important characteristic of such applications is that the spatial size of the data is often time-invariant and significantly exceeds the temporal size due to the large computational grid in 3D applications. Typical examples, which are also considered in this article, relate to results extracted from unsteady flow simulations. Herein, the flow data, which progresses over time, is frequently calculated spatially parallel based on domain decomposition strategies, which allow to parallelize the simulation on distributed memory machines following a Single Instruction Multiple Data (SIMD) concept. With a view to the memory-efficient reuse of (compressed) simulation results and their CPU time-saving, sufficiently accurate generation, the paper scrutinizes the efficiency of incremental/parallel SVD approaches for such simulation examples. To improve the computational efficiency, the introduction of a bunch matrix is proposed, which enables the aggregation of multiple time steps and SVD updates, and significantly increases the efficiency. The suggested strategy is verified and validated by simple 2D laminar single-phase flows and subsequently applied to more complex 2D and 3D turbulent two-phase flows. Emphasis is given to (a) the accuracy of SVD-based reconstruction, (b) the physical realizability of the reconstructed quantities, (c) the independence of domain partitioning, (d) an efficient snapshot bunching , and (e) related implementation aspects. In addition, the influence of lower and (adaptive) upper rank thresholds on the effort and accuracy is evaluated. A final application renders the practical benefits of the approach and refers to a merchant ship in head waves at Re = 1.4 × 10 7 and Fn = 0.26. The simulation involves 2880 processor cores and the related full-rank snapshot matrix has ( 10 8 × 10 4 ) entries. With a numerical overhead of O(10%), this snapshot matrix can be incrementally generated and compressed by O(95%). The compression is accompanied by only small errors in the integral force and local wave elevation of O(10<sup>−2</sup> %). This qualifies the method for an efficient subsequent data processing .", "Keywords": "", "DOI": "10.1016/j.cpc.2023.109022", "PubYear": 2024, "Volume": "296", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hamburg Ship Model Basin, Bramfelder Strasse 164, D-22305 Hamburg, Germany;Hamburg University of Technology, Institute for Fluid Dynamics and Ship Theory, Am Schwarzenberg-Campus 4, D-21075 Hamburg, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Leibniz Universität Hannover, Institute of Applied Mathematics, Welfengarten 1, D-30167 Hannover, Germany;Université Paris-Saclay, CentraleSupélec, ENS Paris-Saclay, LMPS - Laboratoire de Mecanique Paris-Saclay, F-91190 Gif-sur-Yvette, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University Koblenz-Landau, Department of Mathematics, Campus Koblenz, Universitätsstrasse 1, D-56070 Koblenz, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hamburg University of Technology, Institute for Fluid Dynamics and Ship Theory, Am Schwarzenberg-Campus 4, D-21075 Hamburg, Germany"}], "References": [{"Title": "An enhanced algorithm for online Proper Orthogonal Decomposition and its parallelization for unsteady simulations", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "43", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 111060629, "Title": "Stability of a Stationary Solution to One Class of Non-Autonomous Sobolev Type Equations", "Abstract": "The article is devoted to the study of the stability of a stationary solution to the Cauchy problem for a non-autonomous linear Sobolev type equation in a relatively bounded case. Namely, we consider the case when the relative spectrum of the equation operator can intersect with the imaginary axis. In this case, there exist no exponential dichotomies and the second <PERSON><PERSON><PERSON><PERSON> method is used to study stability. The stability of stationary solutions makes it possible to evaluate the qualitative behavior of systems described using such equations. In addition to introduction, conclusion and list of references, the article contains two sections. Section 1 describes the construction of solutions to non-autonomous equations of the class under consideration, and Section 2 examines the stability of a stationary solution to such equations. © 2023 South Ural State University. All rights reserved.", "Keywords": "asymptotic stability; local stream of operators; <PERSON><PERSON><PERSON><PERSON>’s second method; relatively bounded operator", "DOI": "10.14529/mmp230305", "PubYear": 2023, "Volume": "16", "Issue": "3", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 111060673, "Title": "Transformer-Based Unified Segmentation: Simultaneous Semantic and Instance Tasks on Point Clouds", "Abstract": "Traditionally, 3D segmentation tasks have operated in silos, focusing separately on semantic and instance segmentation. However, this disjointed approach lacks interoperability and fails to fully unleash the potential of a more integrated, multitask solution. To overcome this limitation, we introduce TUS-Net, an innovative transformer-based architecture meticulously crafted for both semantic and instance segmentation of point clouds. Our model introduces two pivotal advancements: First, it employs a superpoint-based pre-processing step that minimizes computational overhead without compromising on precision. Second, we leverage a dual-branch design within the transformer architecture, allowing it to adapt to the nuances of both segmentation tasks dynamically. Through extensive experimentation on the ScanNet dataset, our findings demonstrate that TUS-Net surpasses prevailing specialized models by a substantial margin and maintains remarkable computational efficiency. Notably, we achieve a 5.7% enhancement in mean Average Precision (mAP), for instance, segmentation, while striking an optimal balance between accuracy and runtime for semantic segmentation. These outcomes underscore the versatility, efficiency and high-performance attributes of TUS-Net, positioning it as an indispensable framework for robust 3D point cloud segmentation.", "Keywords": "", "DOI": "10.1142/S0218126624501482", "PubYear": 2024, "Volume": "33", "Issue": "8", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Civil Aviation Flight University of China, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Civil Aviation Flight University of China, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Civil Aviation Flight University of China, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Civil Aviation Flight University of China, P. R. China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Civil Aviation Flight University of China, P. R. China"}], "References": [{"Title": "PCT: Point cloud transformer", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "2", "Page": "187", "JournalTitle": "Computational Visual Media"}]}, {"ArticleId": 111060683, "Title": "Multivariate multiscale dispersion Lempel–Ziv complexity for fault diagnosis of machinery with multiple channels", "Abstract": "Lempel–Ziv complexity (LZC), as a nonlinear feature in information science, has shown great promise in detecting correlations and capturing dynamic changes in single-channel time series. However, its application to multichannel data has been largely unexplored, while the complexity of real-world systems demands the utilization of data collected from multiple sensors or channels so as to extract distinguishable fault features for fault diagnosis. This paper proposes a novel method called multivariate multiscale dispersion Lempel–Ziv complexity (mvMDLZC) to extract the fault features hidden in multi-source information. First, multivariate embedding theory is applied to obtain multivariate embedded vectors and multivariate dispersion patterns, which can reflect the inherent relationships in the multichannel series. Second, by assigning labels to these patterns, the original multichannel time series can be transformed into a symbolic sequence with multiple symbols instead of the original binary conversion, enabling the accurate recovery of the system dynamics . Finally, the complexity counter value and normalized LZC are calculated for the complexity measure. Experimental results using synthetic and real-world datasets demonstrate that mvMDLZC outperforms existing LZC-based methods and multivariate dispersion entropy in recognizing different states of mechanical systems . Additionally, mvMDLZC exhibits robustness in handling challenges such as small sample datasets and noise interference, making it suitable for real industrial applications. These findings highlight the potential of mvMDLZC as a valuable approach for dissecting multichannel systems across various real-world scenarios.", "Keywords": "", "DOI": "10.1016/j.inffus.2023.102152", "PubYear": 2024, "Volume": "104", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Aeronautics, Northwestern Polytechnical University, Xi’an, Shaanxi 710072, China"}, {"AuthorId": 2, "Name": "Yongbo Li", "Affiliation": "School of Aeronautics, Northwestern Polytechnical University, Xi’an, Shaanxi 710072, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Civil Aviation, Northwestern Polytechnical University, Xi’an, Shaanxi 710072, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON> Li", "Affiliation": "Opole University of Technology, Faculty of Mechanical Engineering, Opole, 45-758, Poland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Industrial Systems Engineering and Management, National University of Singapore, 117576, Singapore"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Engineering, University of British Columbia, Kelowna V1V 1V7, Canada"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Aeronautics, Northwestern Polytechnical University, Xi’an, Shaanxi 710072, China"}], "References": [{"Title": "A sequential deep learning application for recognising human activities in smart homes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "501", "JournalTitle": "Neurocomputing"}, {"Title": "Intelligent fault identification of rotary machinery using refined composite multi-scale Lempel–Ziv complexity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "725", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Multi-source information fusion based on rough set theory: A review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "85", "JournalTitle": "Information Fusion"}, {"Title": "A novel approach of multisensory fusion to collaborative fault diagnosis in maintenance", "Authors": "<PERSON><PERSON> Shao; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "74", "Issue": "", "Page": "65", "JournalTitle": "Information Fusion"}, {"Title": "An intelligent fault diagnosis for machine maintenance using weighted soft-voting rule based multi-attention module with multi-scale information fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "86-87", "Issue": "", "Page": "17", "JournalTitle": "Information Fusion"}, {"Title": "Non-contact diagnosis for gearbox based on the fusion of multi-sensor heterogeneous data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "94", "Issue": "", "Page": "112", "JournalTitle": "Information Fusion"}, {"Title": "CFCNN: A novel convolutional fusion framework for collaborative fault identification of rotating machinery", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "95", "Issue": "", "Page": "1", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 111060732, "Title": "Honey-block: Edge assisted ensemble learning model for intrusion detection and prevention using defense mechanism in IoT", "Abstract": "The Internet of Things (IoT) has gained popularity with interconnected devices and diverse network applications, leading to increased vulnerability of sensitive data to security threats. Many researchers have focused on intrusion detection without considering prevention mechanisms. To overcome these issues, we propose the honeypot and blockchain-based intrusion detection and prevention (HB-IDP) model, in which edge computing is introduced to reduce the latency during communication. Initially, three-fold authentication is performed for entities (users, devices, and gateway) to ensure legitimacy using the camellia encryption algorithm (CEA), which provides secret keys. The proposed datasets (i.e., UNSW-NB15 and BoT-IoT) are pre-processed at the gateway using min–max normalization to reduce redundancy and complexity during feature extraction and classification. Signature-based intrusion detection is performed on the pre-processed data, with known attacks classified into three classes (normal, malicious, and suspicious) using the improved isolation forest (IIF) algorithm. Suspicious data are forwarded for anomaly detection to the edge level; here, a honeypot is deployed to attract the attacker’s patterns. Ensemble learning technique, including multi-layer perceptron (MLP), general adversarial network (GAN), and lightweight convolutional neural Network (LCNN), is applied to classify suspicious packet behaviors. Once intrusions are detected, the proposed work prevents future intrusions by generating reports, which are then encrypted by the CEA algorithm and provided to legitimate users. All transactions (i.e., key generation, report generation, and attacker patterns) are stored in the blockchain . The HB-IDP model’s performance and effectiveness were evaluated using network simulator 3.26 (NS-3.26), showcasing its superiority over existing approaches.", "Keywords": "", "DOI": "10.1016/j.comcom.2023.11.023", "PubYear": 2024, "Volume": "214", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Software, Dalian University of Technology, 116024 Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Dalian University of Technology, 116024 <PERSON>, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, Chang Gung University, Taoyuan, Taiwan, ROC;Department of Surgery, Chang Gung Memorial Hospital, Linkou Branch, Taoyuan, Taiwan, ROC"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Software, Dalian University of Technology, 116024 Dalian, China"}], "References": [{"Title": "Intrusion detection in Edge-of-Things computing", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "259", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Lightweight collaborative anomaly detection for the IoT using blockchain", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "75", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Anomaly detection optimization using big data and deep learning to reduce false-positive", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "Anti-Honeypot Enabled Optimal Attack Strategy for Industrial Cyber-Physical Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "250", "JournalTitle": "IEEE Open Journal of the Computer Society"}, {"Title": "HFFPNN classifier: a hybrid approach for intrusion detection based OPSO and hybridization of feed forward neural network (FFNN) and probabilistic neural network (PNN)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "4", "Page": "6457", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A survey on intrusion detection and prevention systems in digital substations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Diego <PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "107679", "JournalTitle": "Computer Networks"}, {"Title": "Towards a deep learning-driven intrusion detection approach for Internet of Things", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "186", "Issue": "", "Page": "107784", "JournalTitle": "Computer Networks"}, {"Title": "AS-IDS: Anomaly and Signature Based IDS for the Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "SCAB - IoTA: Secure communication and authentication for IoT applications using blockchain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "154", "Issue": "", "Page": "94", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Threat model and risk management for a smart home IoT system", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "1", "Page": "51", "JournalTitle": "Informatica"}]}, {"ArticleId": 111060747, "Title": "Practical Security", "Abstract": "<p><PERSON> explains AI prompt injection, the difficulties associated with mitigating its potential effects, and the moves that can be made to address the challenges it presents.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad131", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111060918, "Title": "Commit-time defect prediction using one-class classification", "Abstract": "Existing Just-In-Time Software Defect Prediction methods suffer from the data imbalance problem, where the majority class (normal commits) significantly outnumbers the minority class (buggy commits). This results in a higher probability of misclassification. Various data balancing techniques have been proposed to address this challenge with varying degrees of success. In this study, we propose an approach that rely on One-Class Classification (OCC) to train models using data from the majority class only. This eliminates the need for data balancing. We compare the accuracy of three OCC algorithms - One-class SVM , Isolation Forest, and One-class k-NN - to their binary counterparts - SVM, Random Forest , and k-NN - on 34 software projects. Our results show that the data imbalance ratio (the proportion of normal to buggy commits) plays a crucial role in determining the optimal classification approach . We found that for projects with medium to high imbalance ratio, OCC algorithms outperform binary classifiers with and without data balancing, using cross and time-sensitive validation approaches. Furthermore, we found that OCC methods require fewer features for projects with medium to high IR, reducing the computational overhead of training and response time while providing a better understanding of the data and algorithm behavior.", "Keywords": "", "DOI": "10.1016/j.jss.2023.111914", "PubYear": 2024, "Volume": "208", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Concordia University, 1455 De Maisonneuve Blvd. W., Montreal, H3G 1M8, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "American University of Beirut, Beirut, Lebanon"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Concordia University, 1455 De Maisonneuve Blvd. W., Montreal, H3G 1M8, Canada;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Concordia University, 1455 De Maisonneuve Blvd. W., Montreal, H3G 1M8, Canada"}], "References": [{"Title": "On hyperparameter optimization of machine learning algorithms: Theory and practice", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}, {"Title": "Problems with SZZ and features: An empirical study of the state of practice of defect prediction data collection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "2", "Page": "1", "JournalTitle": "Empirical Software Engineering"}, {"Title": "A Systematic Survey of Just-in-Time Software Defect Prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "10", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 111060989, "Title": "Technical and Digital Debt", "Abstract": "<p>Ali Law FBCS explores technical debt, its potential impact on digital transformation and why a thorough understanding of its implications are paramount to success.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad140", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111061035, "Title": "SoK: Privacy-preserving smart contract", "Abstract": "The privacy concern in smart contract applications continues to grow, leading to the proposal of various schemes aimed at developing comprehensive and universally applicable privacy-preserving smart contract (PPSC) schemes. However, the existing research in this area is fragmented and lacks a comprehensive system overview. This paper aims to bridge the existing research gap on PPSC schemes by systematizing previous studies in this field. The primary focus is on two categories: PPSC schemes based on cryptographic tools like zero-knowledge proofs, as well as schemes based on trusted execution environments. In doing so, we aim to provide a condensed summary of the different approaches taken in constructing PPSC schemes. Additionally, we also offer a comparative analysis of these approaches, highlighting the similarities and differences between them. Furthermore, we shed light on the challenges that developers face when designing and implementing PPSC schemes. Finally, we delve into potential future directions for improving and advancing these schemes, discussing possible avenues for further research and development.", "Keywords": "Privacy ; Smart contract ; Zero-knowledge proof ; Trusted execution environment ; Blockchain", "DOI": "10.1016/j.hcc.2023.100183", "PubYear": 2024, "Volume": "4", "Issue": "1", "JournalId": 85789, "JournalTitle": "High-Confidence Computing", "ISSN": "2667-2952", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, China"}], "References": [{"Title": "FAPS: A fair, autonomous and privacy-preserving scheme for big data exchange based on oblivious transfer, Ether cheque and smart contracts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "469", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 111061041, "Title": "Complete genome sequence data of an Antarctic bacterium Arthrobacter sp. EM1 from the freshwater lake of the King George Island", "Abstract": "Arthrobacter sp. EM1 is a cold-adapted bacterium isolated from the Antarctic region, which was known to exhibit mannan-degrading activity. Accordingly, this strain not only promises a cell factory for mannan-degrading enzymes, widely used in industry but also serves as a model organism to decipher its cold adaptation mechanism. Accordingly, whole genome sequencing of the EM1 strain was performed via Single Molecule Real Time sequencing under the PacBio platform, followed by genome HGAP de novo assembly and genome annotation through Rapid Annotation System Technology (RAST) server. The chromosome of this strain is 3,885,750 bp in size with a GC content of 65.8. The annotation predicted a total of 3607 protein-coding genes and 65 RNA genes, which were classified under 398 subsystems. The subsystem with the highest number of genes is carbohydrate metabolism (397 genes), which includes two genes encoding mannan-degrading enzymes (endoglucanase and α-mannosidase). This confirmed that the EM1 strain is able to produce cold-adapted mannan degrading enzymes. The complete genome sequence data have been submitted to the National Center for Biotechnology Information (NCBI) and have been deposited at GenBank (Bioproject ID Accession Number: PRJNA963062; Biosample ID Accession Number: SAMN34434776; GenBank: CP124836.1; https://www.ncbi.nlm.nih.gov/nuccore/CP124836 ).", "Keywords": "Antarctica;Arthrobacter;Cold-adapted bacteria;Whole genome sequence", "DOI": "10.1016/j.dib.2023.109841", "PubYear": 2024, "Volume": "52", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Biotechnology Research Institute, University Malaysia Sabah, Jalan UMS, Kota Kinabalu, Sabah 88400, Malaysia."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Biotechnology Research Institute, University Malaysia Sabah, Jalan UMS, Kota Kinabalu, Sabah 88400, Malaysia."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Biotechnology Research Institute, University Malaysia Sabah, Jalan UMS, Kota Kinabalu, Sabah 88400, Malaysia."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Biotechnology Research Institute, University Malaysia Sabah, Jalan UMS, Kota Kinabalu, Sabah 88400, Malaysia."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Biotechnology Research Institute, University Malaysia Sabah, Jalan UMS, Kota Kinabalu, Sabah 88400, Malaysia. ;Sarawak Tropical Peat Research Institute, Lot 6035, Kuching-Kota Samarahan Expressway, Kota Samarahan, Sarawak 94300, Malaysia."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Biotechnology Research Institute, University Malaysia Sabah, Jalan UMS, Kota Kinabalu, Sabah 88400, Malaysia."}], "References": []}, {"ArticleId": 111061047, "Title": "Fusion of monocular height maps for 3D urban scene reconstruction from uncalibrated satellite images", "Abstract": "", "Keywords": "", "DOI": "10.1080/2150704X.2023.2283901", "PubYear": 2023, "Volume": "14", "Issue": "12", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "Soon-Yong Park", "Affiliation": "School of Electronics Engineering, Kyungpook National University, Daegu, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Electronics and Electrical Engineering, Kyungpook National University, Daegu, Korea"}, {"AuthorId": 3, "Name": "DongUk <PERSON>", "Affiliation": "Graduate School of Electronics and Electrical Engineering, Kyungpook National University, Daegu, Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "KLA Corporation, LS-Swift Division, Hwaseong-si, Gyeonggi Prov, Korea"}], "References": []}, {"ArticleId": 111061055, "Title": "FedQMIX: Communication-efficient federated learning via multi-agent reinforcement learning", "Abstract": "Since the data samples on client devices are usually non-independent and non-identically distributed (non-IID), this will challenge the convergence of federated learning (FL) and reduce communication efficiency. This paper proposes FedQMIX, a node selection algorithm based on multi-agent reinforcement learning(MARL), to address these challenges. Firstly, we observe a connection between model weights and data distribution, and a clustering algorithm can group clients with similar data distribution into the same cluster. Secondly, we propose a QMIX-based mechanism that learns to select devices from clustering results in each communication round to maximize the reward, penalizing the use of more communication rounds and thereby improving the communication efficiency of FL. Finally, experiments show that FedQMIX can reduce the number of communication rounds by 11% and 30% on the MNIST and CIFAR-10 datasets, respectively, compared to the baseline algorithm (Favor).", "Keywords": "Communication efficient; Federated learning; MARL", "DOI": "10.1016/j.hcc.2023.100179", "PubYear": 2024, "Volume": "4", "Issue": "2", "JournalId": 85789, "JournalTitle": "High-Confidence Computing", "ISSN": "2667-2952", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Cao", "Affiliation": "Qingdao Institute of Software, College of Computer Science and Technology, China University of Petroleum (East China), Qingdao 266580, China;Corresponding author"}, {"AuthorId": 2, "Name": "Han<PERSON> Zhang", "Affiliation": "Qingdao Institute of Software, College of Computer Science and Technology, China University of Petroleum (East China), Qingdao 266580, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Qingdao Institute of Software, College of Computer Science and Technology, China University of Petroleum (East China), Qingdao 266580, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Zhao", "Affiliation": "Qingdao Institute of Software, College of Computer Science and Technology, China University of Petroleum (East China), Qingdao 266580, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Zheng", "Affiliation": "Qingdao Institute of Software, College of Computer Science and Technology, China University of Petroleum (East China), Qingdao 266580, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Qingdao Institute of Software, College of Computer Science and Technology, China University of Petroleum (East China), Qingdao 266580, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> Zheng", "Affiliation": "School of Computing and Artificial Intelligence, Xipu Campus, Southwest Jiaotong University, Chengdu 611756, China"}], "References": [{"Title": "Contribution‐based Federated Learning client selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "10", "Page": "7235", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "FedMBC: Personalized federated learning via mutually beneficial collaboration", "Authors": "<PERSON><PERSON> Gong; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "205", "Issue": "", "Page": "108", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 111061090, "Title": "Automation of the Application of Data Distribution with Overlapping in Distributed Memory", "Abstract": "The article deals with block-affine data layouts with overlapping for optimizing parallel computing in a distributed memory computing system. Examples of target computing systems are high-performance clusters and advanced systems on a chip with a large number of computing cores. It is proposed to describe the placement of an array with overlaps as a new array of slightly greater length, in which additional elements have the values of some elements of the original array. The possibility of developing an automatic transformation (by the compiler) of the usual allocation of an array in distributed memory into a new array containing overlaps is being considered. The proposed method is illustrated by a well-known numerical algorithm for solving the heat conduction problem. © 2023 South Ural State University. All rights reserved.", "Keywords": "automation of parallelization; data distribution; data transfer; distributed memory; program transformations", "DOI": "10.14529/mmp230105", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": [{"Title": "Algorithm for Searching Minimum Inter-node Data Transfers", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "193", "Issue": "", "Page": "306", "JournalTitle": "Procedia Computer Science"}, {"Title": "Parallel Programming", "Authors": "<PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "3", "JournalTitle": "PROGRAMMNAYA INGENERIA"}]}, {"ArticleId": 111061117, "Title": "Three-dimension object detection and forward-looking control strategy for non-destructive grasp of thin-skinned fruits", "Abstract": "The dynamic non-destructive grasp of thin-skinned fruits using flexible robotic hands , which requires obtaining three-dimension(3D) spatial structure information along with adaptive planning and motion control toward the target object, is a challenging topic in agricultural intelligence. To tackle the issue of 3D detection, we utilize RGB images and LiDAR point clouds for feature extraction and construct a multi-modal depth fusion convolution neural network (MDF-CNN) to obtain the classification information and perform image segmentation . Incorporating the advantages of a variable palm structure, we establish an evaluation mechanism of the optimal grasping stability (EM-OGS) using a hybrid method of the best configuration and force closure to build a new comprehensive performance optimal configuration planning (CPO-CP) method that is based on the multiple grasping performance indexes. We also create three cross-related nonlinear prediction models P-MGF, P-ODAP, and P-OBA along with a forward-looking non-destructive grasp control algorithm (FL-NGCA) using minimum grasping force to grasp thin-skinned fruits. The control algorithm carries out online, self-directed learning in the actual grasping process of the flexible hand and constantly optimizes the accuracy of the prediction model. Experimental results show that our proposed approach greatly improves the flexible hand comprehensive performance of grasping, outperforming state-of-the-art methods for non-destructive grasping of delicate fruits in most areas.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.111082", "PubYear": 2024, "Volume": "150", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Wuxi University, 333 Xishan Avenue, Wuxi 214105, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Sun", "Affiliation": "Jiangsu JITRI Composite Equipment Research Institute Co., Ltd, 108 Huishan Avenue, Wuxi 214174, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Key Laboratory of Advanced Food Manufacturing Equipment and Technology, Jiangnan University, 1800 Lihu Avenue, Wuxi 214122, China"}, {"AuthorId": 4, "Name": "<PERSON>esong Dai", "Affiliation": "School of Automation, Wuxi University, 333 Xishan Avenue, Wuxi 214105, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Automation, Wuxi University, 333 Xishan Avenue, Wuxi 214105, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Wuxi University, 333 Xishan Avenue, Wuxi 214105, China"}], "References": [{"Title": "Stable grasp planning based on minimum force for dexterous hands", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "2", "Page": "251", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "A novel robotic grasp detection method based on region proposal networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "", "Page": "101963", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Two-stage grasp strategy combining CNN-based classification and adaptive detection on a flexible hand", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106729", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-fingered grasping force optimization based on generalized penalty-function concepts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "135", "Issue": "", "Page": "103672", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Robotic grasp manipulation using evolutionary computing and deep reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON> <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "61", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Grasp2Hardness: fuzzy hardness inference of cylindrical objects for grasp force adjustment of force sensor-less robots", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "2", "Page": "129", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Multi-modal 3D object detection by 2D-guided precision anchor proposal and multi-layer fusion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107405", "JournalTitle": "Applied Soft Computing"}, {"Title": "Learning-based object detection and localization for a mobile robot manipulator in SME production", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102229", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Highly interacting machining feature recognition via small sample learning", "Authors": "<PERSON><PERSON><PERSON> Shi; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102260", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Multi-modal feature fusion for 3D object detection in the production workshop", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "108245", "JournalTitle": "Applied Soft Computing"}, {"Title": "SemRegionNet: Region ensemble 3D semantic instance segmentation network with semantic spatial aware discriminative loss", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "513", "Issue": "", "Page": "247", "JournalTitle": "Neurocomputing"}, {"Title": "Dynamic graph transformer for 3D object detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "259", "Issue": "", "Page": "110085", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Depth dynamic center difference convolutions for monocular 3D object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "520", "Issue": "", "Page": "73", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111061138, "Title": "Considering AI Deployment in Defence", "Abstract": "<p><PERSON>, Lead Data Scientist at BAE Systems Digital Intelligence, looks at how the defence sector might lay the foundations for the safe and effective use of artificial intelligence at scale.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad110", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111061139, "Title": "BCS at the Party Conferences 2024", "Abstract": "<p>The political party conference season has been busy for BCS, with our policy team, senior managers and Fellows heading to Bournemouth,Liverpool and Manchester to engage with the Liberal Democrats, Conservative and Labour parties.</p><p><PERSON>, BCS' Senior Press Officer, reports.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad115", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111061161, "Title": "Vulnerability detection based on federated learning", "Abstract": "<b  >Context:</b> Detecting potential vulnerabilities is a key step in defending against network attacks. However, manual detection is time-consuming and requires expertise. Therefore, vulnerability detection must require automated techniques. <b  >Objective:</b> Vulnerability detection methods based on deep learning need to rely on sufficient vulnerable code samples. However, the problem of code islands has not been extensively researched. For example, in the case of multi-party vulnerability data, how to securely combine multi-party data to improve vulnerability detection performance. From the perspectives of data augmentation and data security, we propose a v ulnerability d etection framework b ased on f ederated l earning (VDBFL). VDBFL is a new model for vulnerability code detection that combines multi-party data. <b  >Method:</b> Firstly, VDBFL utilizes the code property graph as a code representation. The code property graph contains various semantic dependencies of the code. Secondly, VDBFL utilizes graph neural networks and convolutional neural networks as the code feature extractor. VDBFL utilizes the jump-structured graph attention network to aggregate node information of important neighbors. Finally, VDBFL utilizes horizontal federated learning to train a local vulnerability detection model for the client. <b  >Result:</b> In the real world, VDBFL improves F1-Score by 37.4% compared to the vulnerability detection method <PERSON>eal. Among the 5401 vulnerability samples, VDBFL detected 11.8 times more vulnerabilities than Reveal. <b  >Conclusion:</b> Under different datasets, VDBFL has shown better performance than advanced vulnerability detection methods in multiple metrics. In addition, the federated learning stage of VDBFL can be expanded on top of the feature extraction stage of any vulnerable detection method.", "Keywords": "", "DOI": "10.1016/j.infsof.2023.107371", "PubYear": 2024, "Volume": "167", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China;National Engineering Lab fo DBR, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China;National Engineering Lab fo DBR, Beijing, China;College of Information and Network Engineering, Anhui Science and Technology University, Bengbu, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, China;National Engineering Lab fo DBR, Beijing, China;Corresponding author"}], "References": [{"Title": "A survey on federated learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106775", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "BGNN4VD: Constructing Bidirectional Graph Neural-Network for Vulnerability Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "136", "Issue": "", "Page": "106576", "JournalTitle": "Information and Software Technology"}, {"Title": "DeepWukong", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "A Survey on Data-driven Software Vulnerability Assessment and Prioritization", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 111061169, "Title": "Models with Uncertain Volatility", "Abstract": "Models in which volatility is one of the possible trajectories are considered in the paper. As an example of a model with a certain volatility, the Black–Scholes model is considered. As an example of models with uncertain volatility three models are considered: the <PERSON><PERSON> model with random trajectories and two models with deterministic trajectories from a confidence set of possible trajectories. Three computational methods are proposed for finding the range of fair prices for a European option. The first method is based on solving viscosity equations using difference schemes. The second is the <PERSON> method, which is based on the simulation of the initial stock price process. The third is the tree method, which is based on approximating the original continuous model with a discrete model and obtaining recurrent formulas on a binary tree to calculate the upper and lower prices. The results of calculations using the listed methods are presented. It is shown that the ranges of fair prices obtained using the three numerical methods practically coincide. © 2023 South Ural State University. All rights reserved.", "Keywords": "Black–Scholes model; fair price; Heston model; option; uncertain volatility; viscosity equation", "DOI": "10.14529/mmp230301", "PubYear": 2023, "Volume": "16", "Issue": "3", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [], "References": [{"Title": "Control in Binary Models with Disorder", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "67", "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\""}]}, {"ArticleId": 111061172, "Title": "Tracking people across ultra populated indoor spaces by matching unreliable Wi-Fi signals with disconnected video feeds", "Abstract": "Tracking in dense indoor environments where several thousands of people move around is an extremely challenging problem. In this paper, we present a system — DenseTrack for tracking people in such environments. DenseTrack leverages data from the sensing modalities that are already present in these environments — Wi-Fi (from enterprise network deployments) and Video (from surveillance cameras). We combine Wi-Fi information with video data to overcome the individual errors induced by these modalities. More precisely, the locations derived from video are used to overcome the localization errors inherent in using Wi-Fi signals where precise Wi-Fi MAC IDs are used to locate the same devices across different levels and locations inside a building. Typically, localization in dense environments is a computationally expensive process when done with just video data; hence hard to scale. DenseTrack combines Wi-Fi and video data to improve the accuracy of tracking people that are represented by video objects from non-overlapping video feeds. DenseTrack is a scalable and device-agnostic solution as it does not require any app installation on user smartphones or modifications to the Wi-Fi system. At the core of DenseTrack , is our algorithm — inCremental Association of Independent Variables under Uncertainty (CAIVU). CAIVU is inspired by the multi-armed bandits model and is designed to handle various complex features of practical real-world environments. CAIVU matches the devices reported by an off-the-shelf Wi-Fi system using connectivity information to specific video blobs obtained through a computationally efficient analysis of video data. By exploiting data from heterogeneous sources, DenseTrack offers an effective real-time solution for individual tracking in heavily populated indoor environments. We emphasize that no other previous system targeted nor was validated in such dense indoor environments. We tested DenseTrack extensively using both simulated data, as well as two real-world validations using data from an extremely dense convention center and a moderately dense university environment. Our simulation results show that DenseTrack achieves an average video-to-Wi-Fi matching accuracy of up to 90% in dense environments with a matching latency of 60 s on the simulator. When tested in a real-world extremely dense environment with over 500,000 people moving between different non-overlapping camera feeds, DenseTrack achieved an average match accuracy of 83% to within a 2-people distance with an average latency of 48 s.", "Keywords": "", "DOI": "10.1016/j.pmcj.2023.101860", "PubYear": 2024, "Volume": "97", "Issue": "", "JournalId": 4135, "JournalTitle": "Pervasive and Mobile Computing", "ISSN": "1574-1192", "EISSN": "1873-1589", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Singapore Management University, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Northern Iowa, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Stony Brook University, United States"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Rutgers University Rutgers Business School, United States"}, {"AuthorId": 5, "Name": "JeongGil Ko", "Affiliation": "Yonsei University, South Korea;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Singapore Management University, Singapore"}], "References": [{"Title": "FairMOT: On the Fairness of Detection and Re-identification in Multiple Object Tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "11", "Page": "3069", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Deep attention aware feature learning for person re-Identification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "108567", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 111061206, "Title": "Joint Caching and Computing Offloading Strategies for Power Supply Chain Services Under Cloud Edge Collaborative Computing Environment", "Abstract": "Aiming at the problem of long task calculation delay of traditional edge computing offloading strategy (COS), a joint caching and COS for power supply chain services under cloud edge collaborative computing environment is proposed by introducing the master-slave game model. First, for the network of information interaction between power grid system and electrical equipment suppliers, the corresponding overall model of cloud edge collaborative network is constructed by combining cloud computing, edge computing, supplier terminal communication and remote cloud. Then, the cloud-side collaborative network model is analyzed and solved by building the service function cache model, task unloading and data transmission model and power business data calculation model, respectively. Finally, by introducing the master-slave game model, the whole system is divided into three different interests of power service providers, communication providers and communication network terminal providers and the joint caching and COS for power supply chain services is given. Through simulation experiments, the proposed computing unloading strategy and the other three methods are compared and analyzed under the same experimental conditions. The results show that the service cache hit rate of the proposed method is the highest, reaching 96.35%. Compared with the other three comparison methods, it increased by 14.96%, 11.24% and 4.85%, respectively. In addition, its task computing delay is the least affected by agent computing power and agent computing resources, and its performance is better than the other three comparison methods.", "Keywords": "", "DOI": "10.1142/S0218126624500671", "PubYear": 2024, "Volume": "33", "Issue": "4", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Changchun University of Finance and Economics, Changchun, Jilin 130122, P. R. <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Changchun University of Finance and Economics, Changchun, Jilin 130122, P. R. <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Changchun University of Finance and Economics, Changchun, Jilin 130122, P. R. <PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Technology, Jilin Agricultural University, Changchun, Jilin 130118, P. R. <PERSON>"}], "References": [{"Title": "Game Theory Approaches for the Solution of Power System Problems: A Comprehensive Review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "1", "Page": "81", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "A Stackelberg game theoretical approach for demand response in smart grid", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "4", "Page": "511", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "Two-stage computing offloading algorithm in cloud-edge collaborative scenarios based on game theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "97", "Issue": "", "Page": "107624", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "DQN based user association control in hierarchical mobile edge computing systems for mobile IoT services", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "137", "Issue": "", "Page": "53", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A collaborative optimization strategy for computing offloading and resource allocation based on multi-agent deep reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "103", "Issue": "", "Page": "108278", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Collaborative computation offloading and resource allocation based on dynamic pricing in mobile edge computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "198", "Issue": "", "Page": "52", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 111061225, "Title": "Implementing generative adversarial network (GAN) as a data-driven multi-site stochastic weather generator for flood frequency estimation", "Abstract": "Precipitation is a key driving factor of hydrologic modeling for impact studies. However, there are challenges due to limited long-term data availability and complex parameterizations of existing stochastic weather generators (SWGs) due to spatiotemporal uncertainty. We introduced state-of-the-art Generative Adversarial Network (GAN) as a data-driven multi-site SWG and synthesized extensive hourly precipitation over 30 years at 14 stations. These samples were then fed into an hourly-calibrated SWAT model for streamflow generation. Results showed that the well-trained GAN improved rainfall data by accurately representing spatiotemporal distribution of raw data rather than simply replicating its statistical characteristics. GAN also helped display authentic spatial correlation patterns of extreme rainfall events well. We concluded that GAN offers a superior spatiotemporal distribution of raw data compared to conventional methods, thus enhancing the reliability of flood frequency evaluations.", "Keywords": "", "DOI": "10.1016/j.envsoft.2023.105896", "PubYear": 2024, "Volume": "172", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, Faculty of Engineering, University of Malaya (UM), Kuala Lumpur, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Environmental Science and Technology, University of Maryland, College Park, MD, 20742, USA;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, Faculty of Engineering, Universiti Malaysia Sarawak (UNIMAS), 94300, Kota Samarahan, Sarawak, Malaysia;UNIMAS Water Centre (UWC), Faculty of Engineering, Universiti Malaysia Sarawak, 94300 Kota Samarahan, Sarawak, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Manufacturing Engineering, Faculty of Engineering, Universiti Putra Malaysia, Selangor, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Environment, College of Engineering, University of Tehran, Tehran, Iran"}], "References": []}, {"ArticleId": 111061239, "Title": "A TOPSIS method based on sequential three-way decision", "Abstract": "<p>Technique for Order Preference by Similarity to Ideal Solution (TOPSIS) is a method for ranking a limited number of alternatives based on their closeness to an idealized goal. For specific decision-making problems, there may be some alternatives whose merits cannot be judged. Many researchers have proposed some improved ranking methods that enable a more accurate ranking result of the alternatives. However, these methods only serve to rank the alternatives, not to classify them. In order to extend the application scope and decision-making ability of TOPSIS method, this paper designs a three-way TOPSIS method that can handle both classification and ranking of alternatives by introducing sequential three-way decisions. Specifically, we first use the basic principles of TOPSIS method to obtain the Positive Ideal Solution (PIS) and Negative Ideal Solution (NIS) of the alternatives, and design four different three-way TOPSIS models are designed according to the distance measures of each alternative to different ideal solutions. Then we employ sequential three-way decision to divide the alternatives in order to obtain the corresponding decision regions. The alternatives are initially ranked according to the ranking rules of the same decision region, and the final ranking is performed using the ranking rules of different decision regions. Finally, this paper verifies the validity and feasibility of the method through an example about project investment to test the results and comparative analysis.</p>", "Keywords": "Multi-attribute decision making; TOPSIS method; Sequential three-way decision; Decision region; Ideal solution", "DOI": "10.1007/s10489-023-05183-2", "PubYear": 2023, "Volume": "53", "Issue": "24", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Software, East China Jiaotong University, Nanchang, China; School of Computer, Jiangsu University of Science and Technology, Zhenjiang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, East China Jiaotong University, Nanchang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, East China Jiaotong University, Nanchang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Software, East China Jiaotong University, Nanchang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, East China Jiaotong University, Nanchang, China; Department of Computer Science and Technology, Tongji University, Shanghai, China"}], "References": [{"Title": "Interval-valued intuitionistic fuzzy multiple attribute decision making based on nonlinear programming methodology and TOPSIS method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "506", "Issue": "", "Page": "424", "JournalTitle": "Information Sciences"}, {"Title": "Complex network analysis of three-way decision researches", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "973", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Enhancing PROMETHEE method with intuitionistic fuzzy soft sets", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "7", "Page": "1071", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "TOPSIS-WAA method based on a covering-based fuzzy rough set: An application to rating problem", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "539", "Issue": "", "Page": "397", "JournalTitle": "Information Sciences"}, {"Title": "A novel decision-making approach based on three-way decisions in fuzzy information systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "541", "Issue": "", "Page": "362", "JournalTitle": "Information Sciences"}, {"Title": "Score function based on concentration degree for probabilistic linguistic term sets: An application to TOPSIS and VIKOR", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "551", "Issue": "", "Page": "270", "JournalTitle": "Information Sciences"}, {"Title": "A new classification and ranking decision method based on three-way decision theory and TOPSIS models", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "568", "Issue": "", "Page": "54", "JournalTitle": "Information Sciences"}, {"Title": "ELECTRE-II method for group decision-making in Pythagorean fuzzy environment", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "12", "Page": "8701", "JournalTitle": "Applied Intelligence"}, {"Title": "Mixed data-driven sequential three-way decision via subjective–objective dynamic fusion", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "237", "Issue": "", "Page": "107728", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "RETRACTED ARTICLE: Online education satisfaction assessment\n based on cloud model and fuzzy TOPSIS", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "12", "Page": "13659", "JournalTitle": "Applied Intelligence"}, {"Title": "BMW-TOPSIS: A generalized TOPSIS model based on three-way decision", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "799", "JournalTitle": "Information Sciences"}, {"Title": "Decision making framework based Fermatean fuzzy integrated weighted distance and TOPSIS for green low-carbon port evaluation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "105048", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A novel TOPSIS method with decision-theoretic rough fuzzy sets", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "1221", "JournalTitle": "Information Sciences"}, {"Title": "A prospect-regret theory-based three-way decision model with intuitionistic fuzzy numbers under incomplete multi-scale decision information systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "214", "Issue": "", "Page": "119144", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi‐granularity re‐ranking for visible‐infrared person re‐identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "3", "Page": "770", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": 111061331, "Title": "OPKT: Enhancing Knowledge Tracing With Optimized Pretraining Mechanisms in Intelligent Tutoring", "Abstract": "Knowledge tracing (KT) is essential in intelligent tutoring systems for tracking learners' knowledge states and predicting their future performance. Numerous prevailing KT methods prioritize modeling learners' behavioral patterns in acquiring knowledge and the relationship among interactions. However, due to the sparsity problem, they frequently encounter challenges in effectively uncovering latent contextual features embedded within the learning sequences. This limitation may impose certain constraints on the predictive performance. In light of this concern, this article focuses on extracting latent features from learning sequences to enhance the assessment of knowledge states. Consequently, we design optimized pretraining mechanisms and introduce an enhanced deep KT method, optimized pretraining deep KT (OPKT). In the pretraining phase, the self-supervised learning approach is effectively employed to train comprehensive contextual encodings of the learning sequences. During fine-tuning, the contextual encodings are transferred to the downstream KT model, which then generates the knowledge states and makes predictions. Through our experiments, the superiority of our method over six existing KT models on five publicly available datasets is demonstrated. Furthermore, extensive ablation studies and visualized analysis validate the rationality and effectiveness of every component of the OPKT architecture.", "Keywords": "", "DOI": "10.1109/TLT.2023.3336240", "PubYear": 2024, "Volume": "17", "Issue": "", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Liqing Qiu", "Affiliation": "Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhou", "Affiliation": "Shandong University of Science and Technology, Qingdao, China"}], "References": [{"Title": "Time Interval Aware Self-Attention approach for Knowledge Tracing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "108179", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Knowledge Tracing: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "11", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "HHSKT: A learner–question interactions based heterogeneous graph neural network model for knowledge tracing", "Authors": "Qin Ni; Tingjiang Wei; Jiabao Zhao", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119334", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Improving knowledge tracing via a heterogeneous information network enhanced by student interactions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "232", "Issue": "", "Page": "120853", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 111061390, "Title": "Mentoring", "Abstract": "<p><PERSON><PERSON> provides insight into the importance of building robust mentor- mentee relationships to enhance understanding and support ethical navigation in the dynamic world of AI.</p>", "Keywords": "", "DOI": "10.1093/itnow/bwad124", "PubYear": 2023, "Volume": "65", "Issue": "4", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111061456, "Title": "A new dynamic spatial information design framework for AR-HUD to evoke drivers’ instinctive responses and improve accident prevention", "Abstract": "Driver’s instinctive responses and skill-based behaviors enable them to react faster and better control their vehicle in dangerous situations. This study incorporated dynamic spatial information design (DSID) in an augmented reality head-up display (AR-HUD) under manual driving conditions. By integrating the skill, rule, and knowledge (SRK) taxonomy and situation awareness (SA) theory, our AR-HUD successfully evoked drivers’ instinctive responses and improved driving safety. First, we converted symbol and sign information processed at the knowledge-based and rule-based levels, respectively, into signal information processed at the skill-based level. Then we developed four AR-HUD interfaces with different dynamic designs for use in a hazardous scenario at an intersection. Finally, we investigated each design’s impact on drivers’ SA and driving performance. Experimental results demonstrated that our DSID enhanced drivers’ SA and accident-avoidance capabilities while reducing their cognitive workload. Among the four AR-HUD interfaces, the one that incorporated all three information elements under study (i.e., lateral warning, dynamic driving space, and speedometer) performed the best. This indicates that our proposed framework has potential applications in other similar dangerous driving scenarios, thus contributing to the development of safer and more efficient driving environments.", "Keywords": "", "DOI": "10.1016/j.ijhcs.2023.103194", "PubYear": 2024, "Volume": "183", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Arts and Media, Tongji University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Arts and Media, Tongji University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Arts and Media, Tongji University, Shanghai, China;College of Design and Innovation, Tongji University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Design, The Hong Kong Polytechnic University, Hong Kong, China;Department of Computing, The Hong Kong Polytechnic University, Hong Kong, China;Corresponding author at: School of Design, The Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Arts and Media, Tongji University, Shanghai, China;College of Design and Innovation, Tongji University, Shanghai, China;School of Design, The Hong Kong Polytechnic University, Hong Kong, China;Corresponding author at: College of Design and Innovation, Tongji University, Shanghai, China"}], "References": [{"Title": "Assessing Distraction Potential of Augmented Reality Head-Up Displays for Vehicle Drivers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "5", "Page": "852", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Determining the impact of augmented reality graphic spatial location and motion on driver behaviors", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "96", "Issue": "", "Page": "103510", "JournalTitle": "Applied Ergonomics"}, {"Title": "HierVid: Lowering the Barriers to Entry of Interactive Video Making with a Hierarchical Authoring System", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "22", "Page": "7688", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 111061529, "Title": "Improved algorithm for permutation testing", "Abstract": "For a permutation π : [ K ] → [ K ] , a sequence f : { 1 , 2 , ⋯ , n } → R contains a π -pattern of size K , if there is a sequence of indices ( i 1 , i 2 , ⋯ , i K ) ( i 1 &lt; i 2 &lt; ⋯ &lt; i K ), satisfying that f ( i a ) &lt; f ( i b ) if π ( a ) &lt; π ( b ) , for a , b ∈ [ K ] . Otherwise, f is referred to as π -free. For the special case where π = ( 1 , 2 , ⋯ , K ) , it is referred to as the monotone pattern. [1] initiated the study of testing π -freeness with one-sided error. They focused on two specific problems, testing the monotone permutations and the ( 1 , 3 , 2 ) permutation. For the problem of testing monotone permutation ( 1 , 2 , ⋯ , K ) , [2] improved the ( log ⁡ n ) O ( K 2 ) non-adaptive query complexity of [1] to O ( ( log ⁡ n ) ⌊ log 2 ⁡ K ⌋ ) . Further, [3] proposed an adaptive algorithm with O ( log ⁡ n ) query complexity. However, no progress has yet been made on the problem of testing ( 1 , 3 , 2 ) -freeness. In this work, we present an adaptive algorithm for testing ( 1 , 3 , 2 ) -freeness. The query complexity of our algorithm is O ( ϵ − 2 log 4 ⁡ n ) , which significantly improves over the O ( ϵ − 7 log 26 ⁡ n ) -query adaptive algorithm of [1] . This improvement is mainly achieved by the proposal of a new structure embedded in the patterns.", "Keywords": "", "DOI": "10.1016/j.tcs.2023.114316", "PubYear": 2024, "Volume": "986", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "Huazhong University of Science and Technology, China"}], "References": []}, {"ArticleId": *********, "Title": "Leveraging the edge and cloud for V2X-based real-time object detection in autonomous driving", "Abstract": "Environmental perception is a key element of autonomous driving because the information received from the perception module influences core driving decisions. An outstanding challenge in real-time perception for autonomous driving lies in finding the best trade-off between detection quality and latency. Major constraints on both computation and power must be taken into account for real-time perception in autonomous vehicles. Larger detection models tend to produce the best results but are also slower at runtime. Since the most accurate detectors may not run in real-time locally, we investigate the possibility of offloading computation to edge and cloud platforms, which are less resource-constrained. We create a synthetic dataset to train object detection models and evaluate different offloading strategies. We measure inference and processing times for object detection on real hardware, and we rely on a network simulation framework to estimate data transmission latency. Our study compares different trade-offs between prediction quality and end-to-end delay. Following the existing literature, we aim to perform object detection at a rate of 20Hz. Since sending raw frames over the network implies additional transmission delays, we also explore the use of JPEG and H.265 compression at varying qualities and measure their impact on prediction. We show that models with adequate compression can be run in real-time on the edge/cloud while outperforming local detection performance.", "Keywords": "V2X ; Object detection ; Latency optimization ; Edge computing ; Cloud computing", "DOI": "10.1016/j.comcom.2023.11.025", "PubYear": 2024, "Volume": "213", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Interdisciplinary Centre for Security, Reliability, and Trust (SnT), University of Luxembourg, L-1855, Luxembourg;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Interdisciplinary Centre for Security, Reliability, and Trust (SnT), University of Luxembourg, L-1855, Luxembourg"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Interdisciplinary Centre for Security, Reliability, and Trust (SnT), University of Luxembourg, L-1855, Luxembourg"}], "References": [{"Title": "Cloud-based elastic architecture for distributed video encoding: Evaluating H.265, VP9, and AV1", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "102782", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "A Survey on Task Offloading in Multi-access Edge Computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "102225", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "High-resolution image compression algorithms in remote sensing imaging", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "", "Page": "102462", "JournalTitle": "Displays"}]}, {"ArticleId": 111061619, "Title": "Implementasi Algoritma Random Forest Regression untuk Memprediksi Hasil Panen Padi di Desa Minanga", "Abstract": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Kabupaten <PERSON><PERSON> penduduknya melakukan budidaya tanaman padi yang biasanya hasil panen setiap musimnya mengalami fluktuasi yang seringkali terjadi penurunan atau pun peningkatan yang tidak stabil. Pen<PERSON>tian ini diharapkan&nbsp; dapat membantu dalam memprediksi hasil panen padi sesuai dengan kriteria dan data yang ada sebelumnya seperti luas lahan, jumlah bibit, jenis pupuk, curah hujan, hama dan gulma, pengendalian hama dan gulma, dan sistem penanaman padi yang digunakan (jajar legowo), dengan menerapkan algoritma Random Forest Regression.&nbsp; Evaluasi kinerja algoritma diukur dengan menggunakan Root Mean Squared Error (RMSE), Mean Absolute Percentage Error (MAPE) dan koefisien determinasi (RÂ²), hasil dari model Random Forest yang didapatkan dari 9 pohon, variabel yang memiliki nilai paling tinggi pada variabel importance adalah variabel luas lahan. Sehingga dari model tersebut diperoleh nilai akurasi 95,11%, Nilai MAPE pada model ini yaitu 4,884%, nilai RMSE yaitu 0,250 dan nilai RÂ² yaitu 0.99.", "Keywords": "<PERSON><PERSON>;Prediksi;Random Forest", "DOI": "10.35143/jkt.v9i1.5917", "PubYear": 2023, "Volume": "9", "Issue": "1", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Sulawesi Barat"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Sulawesi Barat"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sulawesi Barat"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sulawesi Barat"}], "References": [{"Title": "Prediksi Harga Bitcoin Menggunakan Metode Random Forest", "Authors": "<PERSON><PERSON>; Haifa Salsabila", "PubYear": 2021, "Volume": "7", "Issue": "1", "Page": "24", "JournalTitle": "Jurnal Komputer Terapan"}]}, {"ArticleId": 111061640, "Title": "Posiform planting: generating QUBO instances for benchmarking", "Abstract": "<p> We are interested in benchmarking both quantum annealing and classical algorithms for minimizing quadratic unconstrained binary optimization (QUBO) problems. Such problems are NP-hard in general, implying that the exact minima of randomly generated instances are hard to find and thus typically unknown. While brute forcing smaller instances is possible, such instances are typically not interesting due to being too easy for both quantum and classical algorithms. In this contribution, we propose a novel method, called posiform planting , for generating random QUBO instances of arbitrary size with known optimal solutions, and use those instances to benchmark the sampling quality of four D-Wave quantum annealers utilizing different interconnection structures (Chimera, Pegasus, and Zephyr hardware graphs) and the simulated annealing algorithm. Posiform planting differs from many existing methods in two key ways. It ensures the uniqueness of the planted optimal solution, thus avoiding groundstate degeneracy, and it enables the generation of QUBOs that are tailored to a given hardware connectivity structure, provided that the connectivity is not too sparse. Posiform planted QUBOs are a type of 2-SAT boolean satisfiability combinatorial optimization problems. Our experiments demonstrate the capability of the D-Wave quantum annealers to sample the optimal planted solution of combinatorial optimization problems with up to 5, 627 qubits. </p>", "Keywords": "Quantum Annealing (QA); QUBO problem; MAX-2-SAT; Planted solution; Time-to-solution; Combinatorial optimisation problem; Boolean SATisfiability (SAT); Quadratic unconstrained binary optimization", "DOI": "10.3389/fcomp.2023.1275948", "PubYear": 2023, "Volume": "5", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> School of Public Health, Harvard University, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Los Alamos National Laboratory, CCS-3 Information Sciences, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Los Alamos National Laboratory, CCS-3 Information Sciences, United States; Institute of Information and Communication Technologies, Bulgarian Academy of Sciences, Bulgaria"}], "References": [{"Title": "The potential of quantum annealing for rapid solution structure identification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "1-4", "Page": "1", "JournalTitle": "Constraints"}]}, {"ArticleId": 111061656, "Title": "Performance implications of match between social media–enabled interactions and contracts in interfirm governance", "Abstract": "Purpose Integrating transaction costs economics and task-technology fit theory, this study distinguishes two categories of social media–enabled interactions, namely task-related interactions and tie-related interactions, and explores the match between these two and firms' use of contracts in achieving safeguarding and coordinating purposes in interfirm governance. Design/methodology/approach Two studies were conducted to test the hypotheses. In Study 1, this study collaborated with a professional market research firm and collected responses from Chinese manufacturing firms in a survey. In Study 2, this study designed a scenario-based experiment and collected 239 participants from the Credamo platform. Findings This study categorized social media–enabled interactions into task-related interactions and tie-related interactions and conducted two studies to reveal that the safeguarding purpose of contract specificity is amplified by tie-related interactions, whereas the coordinating purpose of contract specificity is strengthened by task-related interactions. Research limitations/implications This study assumes that firms permit and encourage the use of social media. However, some firms might prohibit the use of social media due to risk issues, or their partners may be prohibited from using social media. Practical implications Given that social media–enabled interactions have joint effects with contracts in achieving safeguarding and coordinating purposes, a firm's employees should match their goals with an appropriate type of social media–enabled interactions. Originality/value This study enriches the interfirm governance literature by uncovering the roles of these two types of interactions in matching contract specificity to achieve safeguarding and coordinating purposes, which provides actionable insights for managers in governing interfirm relationships.", "Keywords": "Contracts;Social media;Opportunism;Collaborative activities", "DOI": "10.1108/INTR-10-2022-0844", "PubYear": 2025, "Volume": "35", "Issue": "1", "JournalId": 20482, "JournalTitle": "Internet Research", "ISSN": "1066-2243", "EISSN": "2054-5657", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Economics and Management , Nanjing University of Aeronautics and Astronautics , Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Business , City University of Hong Kong , Kowloon Tong, Hong Kong"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China-ASEAN Institute of Statistics, Guangxi University of Finance and Economics , Nanning, China College of Business , City University of Hong Kong , Kowloon Tong, Hong Kong"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Ginling College, Nanjing Normal University , Nanjing, China"}], "References": [{"Title": "How to build employees' relationship capital through different enterprise social media platform use: the moderating role of innovation culture", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "5", "Page": "1823", "JournalTitle": "Internet Research"}, {"Title": "Social media marketing system: conceptualization, scale development and validation", "Authors": "Concepción Varela-<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>-Rodriguez", "PubYear": 2023, "Volume": "33", "Issue": "4", "Page": "1302", "JournalTitle": "Internet Research"}]}, {"ArticleId": 111061660, "Title": "Technologies evolution in robot-assisted fracture reduction systems: a comprehensive review", "Abstract": "<p> Background: Robot-assisted fracture reduction systems can potentially reduce the risk of infection and improve outcomes, leading to significant health and economic benefits. However, these systems are still in the laboratory stage and not yet ready for commercialization due to unresolved difficulties. While previous reviews have focused on individual technologies, system composition, and surgical stages, a comprehensive review is necessary to assist future scholars in selecting appropriate research directions for clinical use. </p><p> Methods: A literature review using Google Scholar identified articles on robot-assisted fracture reduction systems. A comprehensive search yielded 17,800, 18,100, and 16,700 results for “fracture reduction,” “computer-assisted orthopedic surgery,” and “robot-assisted fracture reduction,” respectively. Approximately 340 articles were selected, and 90 highly relevant articles were chosen for further reading after reviewing the abstracts. </p><p> Results and Conclusion: Robot-assisted fracture reduction systems offer several benefits, including improved reduction accuracy, reduced physical work and radiation exposure, enhanced preoperative planning and intraoperative visualization, and shortened learning curve for skill acquisition. In the future, these systems will become integrated and practical, with automatic preoperative planning and high intraoperative safety. </p>", "Keywords": "Bone fracture reduction; Virtual surgery; Robot-assisted fracture reduction; Preoperative planning; Intraoperative registration; navigation; Fracture reduction robot", "DOI": "10.3389/frobt.2023.1315250", "PubYear": 2023, "Volume": "10", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering, China; College of Health Science and Environment Engineering, China"}, {"AuthorId": 5, "Name": "<PERSON>ing <PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering, China"}], "References": []}, {"ArticleId": 111061693, "Title": "Structural gender imbalances in ballet collaboration networks", "Abstract": "Ballet, a mainstream performing art predominantly associated with women, exhibits significant gender imbalances in leading positions. However, the collaboration’s structural composition vis-à-vis gender representation in the field remains unexplored. Our study investigates the gendered labor force composition and collaboration patterns in ballet creations. Our findings reveal gender disparities in ballet creations aligned with gendered collaboration patterns and women’s occupation of more peripheral network positions than men. Productivity disparities show women accessing 20–25% of ballet creations compared to men. Mathematically derived perception errors show the underestimation of women artists’ representation within ballet collaboration networks, potentially impacting women’s careers in the field. Our study highlights the structural imbalances that women face in ballet creations and emphasizes the need for a more inclusive and equal professional environment in the ballet industry. These insights contribute to a broader understanding of structural gender imbalances in artistic domains and can inform cultural organizations about potential affirmative actions toward a better representation of women leaders in ballet.", "Keywords": "Computer Appl. in Social and Behavioral Sciences;Data-driven Science; Modeling and Theory Building;Complexity", "DOI": "10.1140/epjds/s13688-023-00428-z", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 5454, "JournalTitle": "EPJ Data Science", "ISSN": "", "EISSN": "2193-1127", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>uz<PERSON>", "Affiliation": "Centro de Investigación en Complejidad Social (CICS), Facultad de Gobierno, Universidad del Desarrollo, Santiago, Chile"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Scientific Computing, Pukyong National University, Busan, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Energy Engineering, Korea Institute of Energy Technology, Naju, Republic of Korea;Instituto de Data Science, Facultad de Ingeniería, Universidad del Desarrollo, Santiago, Chile"}], "References": [{"Title": "Success and luck in creative careers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "EPJ Data Science"}]}, {"ArticleId": *********, "Title": "Cosine modulated filter bank‐based architecture for extracting and fusing saliency features", "Abstract": "Many academics are interested in content‐based image retrieval techniques like image segmentation. In computer vision, the most popular method for segmenting a digital image into different parts is known as image segmentation. We assigned the artificially intelligent algorithm to the image's critical areas by modeling human features in specific regions. In order to detect the object and identify the key parts in the ‘RGB’ photographs, we combined scenes based on a colour and depth map, or ‘RGB‐D’, and used cosine modulated filter bank (CMFB), which conducts cross‐scale extraction of joint features from the images during feature extraction. The proposed ‘CMFB’ combines the discovered collaborative elements with the discovered supplementary data. The features in multi‐scale images is combined using fusion blocks with the goal of producing additional features (FB). Then, a saliency mapping calculation is made for the loss linked to two blocks. The suggested ‘CMFB’ is tested with the aid of five data sets, and it is shown that, the proposed ‘CMFB’ outperforms other conventional techniques.", "Keywords": "CMFB (cosine modulated filter bank);convolutional layer;cross-scale;enhancing the results;RGB-D (red green blue-depth);saliency", "DOI": "10.1111/exsy.13508", "PubYear": 2024, "Volume": "41", "Issue": "3", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, College of Computer Science and Electronic Engineering Hunan University  Changsha China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, College of Computer Science and Electronic Engineering Hunan University  Changsha China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering American International University‐Bangladesh (AIUB)  Dhaka Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Ma<PERSON>ana Bhashani Science and Technology University  Tangail Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Engineering, College of Computer and Information Sciences King <PERSON> University  Riyadh Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering, College of Computer and Information Sciences King <PERSON> University  Riyadh Saudi Arabia"}], "References": [{"Title": "Fusion of B‐mode and shear wave elastography ultrasound features for automated detection of axillary lymph node metastasis in breast carcinoma", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "5", "Page": "e12947", "JournalTitle": "Expert Systems"}, {"Title": "Suggesting method names based on graph neural network with salient information modelling", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "6", "Page": "e13030", "JournalTitle": "Expert Systems"}, {"Title": "Residual attentive feature learning network for salient object detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "501", "Issue": "", "Page": "741", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111061749, "Title": "A model-agnostic, network theory-based framework for supporting XAI on classifiers", "Abstract": "In recent years, the enormous development of Machine Learning, especially Deep Learning, has led to the widespread adoption of Artificial Intelligence (AI) systems in a large variety of contexts. Many of these systems provide excellent results but act as black-boxes. This can be accepted in various contexts, but there are others (e.g., medical ones) where a result returned by a system cannot be accepted without an explanation on how it was obtained. Explainable AI (XAI) is an area of AI well suited to explain the behavior of AI systems that act as black-boxes. In this paper, we propose a model-agnostic XAI framework to explain the behavior of classifiers. Our framework is based on network theory; thus, it is able to make use of the enormous amount of results that researchers in this area have discovered over time. Being network-based, our framework is completely different from the other model-agnostic XAI approaches. Furthermore, it is parameter-free and is able to handle heterogeneous features that may not even be independent of each other. Finally, it introduces the notion of dyscrasia that allows us to detect not only which features are important in a particular task but also how they interact with each other.", "Keywords": "Explainable Artificial Intelligence ; Model-agnostic XAI systems ; Graph theory ; Feature relevance ; Feature dyscrasia ; Sensitivity analysis", "DOI": "10.1016/j.eswa.2023.122588", "PubYear": 2024, "Volume": "241", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "DII, Polytechnic University of Marche, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "DII, Polytechnic University of Marche, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "DII, Polytechnic University of Marche, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "DII, Polytechnic University of Marche, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "DEMACS, University of Calabria, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "DII, Polytechnic University of Marche, Italy;Corresponding author"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "DII, Polytechnic University of Marche, Italy"}], "References": [{"Title": "Artificial intelligence for industry 4.0: Systematic review of applications, challenges, and opportunities", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "216", "Issue": "", "Page": "119456", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Neurosymbolic AI: the 3rd wave", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "11", "Page": "12387", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Quantized neural adaptive finite-time preassigned performance control for interconnected nonlinear systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "21", "Page": "15429", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "Applications of Artificial Intelligence and Machine learning in smart cities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "313", "JournalTitle": "Computer Communications"}, {"Title": "A novel hybrid feature selection method based on dynamic feature importance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106337", "JournalTitle": "Applied Soft Computing"}, {"Title": "The four dimensions of social network analysis: An overview of research methods, applications, and software tools", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "88", "JournalTitle": "Information Fusion"}, {"Title": "Post-hoc explanation of black-box classifiers using confident itemsets", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113941", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Survey on the Explainability of Supervised Machine Learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "70", "Issue": "", "Page": "245", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "A survey on deep learning and its applications", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100379", "JournalTitle": "Computer Science Review"}, {"Title": "Explainable artificial intelligence for manufacturing cost estimation and machining feature visualization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115430", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Artificial Emotional Intelligence: Conventional and deep learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "118651", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 111061764, "Title": "Intelligent Target Classification Algorithm for 77G Radar Based on Correction Data Set", "Abstract": "Traffic participant classification is crucial in autonomous driving perception. Millimeter wave radio detection and ranging radar is a cost-effective and powerful method to perform the task in adverse traffic scenarios, especially in bad inclement weather (e.g. fog, snow and rain) and poor lighting conditions. This paper presents an intelligent target classification algorithm for 77G radar based on correction data set. First, in order to handle the problem that the original data set may easily be interfered by obstacles, the angle information is filtered by analyzing the spatial information of radar signals, which means the interference clutter of obstacles can be effectively removed. Second, the primary data set is corrected using the significant difference between the micro-Doppler of the human body and car. Finally, the characteristic information of the radar signal is extracted, including distance, speed, orientation, micro-Doppler and reflection intensity, and the obtained data sets containing three types of targets (vehicles, human bodies and obstacles) are generated. The generated dynamic and static data sets are collected by sufficient experiments to construct deep learning classification models. The results show that the classification accuracy is improved by the measure of data set correction.", "Keywords": "", "DOI": "10.1142/S0218001423590231", "PubYear": 2023, "Volume": "37", "Issue": "16", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, West Anhui University, Lu’an 237012, Anhui, P. R. China;Generic Technology Research Center for Anhui, Traditional Chinese Medicine Industry, West Anhui University, Lu’an 237012, Anhui, P. R. China;Anhui Undergrowth Crop Intelligent Equipment Engineering, Research Center, West Anhui University, Lu’an 237012, Anhui, P. R. China"}, {"AuthorId": 2, "Name": "Qing Jiang", "Affiliation": "Faculty of Electronic and Information Engineering, West Anhui University, Lu’an 237012, Anhui, P. R. China"}, {"AuthorId": 3, "Name": "Maosheng Fu", "Affiliation": "Faculty of Electronic and Information Engineering, West Anhui University, Lu’an 237012, Anhui, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, West Anhui University, Lu’an 237012, Anhui, P. R. China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, West Anhui University, Lu’an 237012, Anhui, P. R. China"}, {"AuthorId": 6, "Name": "<PERSON>ui<PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, West Anhui University, Lu’an 237012, Anhui, P. R. China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, West Anhui University, Lu’an 237012, Anhui, P. R. China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, West Anhui University, Lu’an 237012, Anhui, P. R. China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronic and Information Engineering, West Anhui University, Lu’an 237012, Anhui, P. R. China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON> Shen", "Affiliation": "Faculty of Electronic and Information Engineering, West Anhui University, Lu’an 237012, Anhui, P. R. China"}], "References": []}]